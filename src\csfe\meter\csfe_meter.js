/**
 * CSFE Meter Component
 * 
 * This module implements the CSFE Meter component, which measures and visualizes
 * financial entropy metrics. It mirrors the Comphyon Meter but is tailored for
 * financial systems.
 * 
 * Key metrics include:
 * - Velocity (∂Ψₜᶠ/∂t): Rate of entropy change in financial systems
 * - Acceleration (∂²Ψₜᶠ/∂t²): Jumps in risk exposure
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * CSFEMeter class
 */
class CSFEMeter extends EventEmitter {
  /**
   * Create a new CSFEMeter instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 1000, // ms
      historySize: 100, // Number of historical data points to keep
      alertThresholds: {
        critical: 0.95, // Critical alert threshold
        high: 0.8, // High alert threshold
        medium: 0.6, // Medium alert threshold
        low: 0.4 // Low alert threshold
      },
      enableLogging: true, // Enable logging
      enableMetrics: true, // Enable performance metrics
      ...options
    };
    
    // Initialize state
    this.state = {
      currentFinancialEntropy: 0.5, // Default financial entropy
      entropyHistory: [], // History of entropy values
      velocityHistory: [], // History of velocity values
      accelerationHistory: [], // History of acceleration values
      lastUpdateTime: Date.now(),
      alerts: [], // Recent alerts
      isRunning: false
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      alertsGenerated: 0
    };
    
    console.log('CSFE Meter initialized');
  }
  
  /**
   * Start the meter
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      console.log('CSFE Meter is already running');
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    console.log('CSFE Meter started');
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the meter
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      console.log('CSFE Meter is not running');
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    console.log('CSFE Meter stopped');
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Update financial entropy value
   * @param {number} entropy - New financial entropy value
   * @returns {Object} - Updated state
   */
  updateFinancialEntropy(entropy) {
    const startTime = performance.now();
    this.metrics.totalUpdates++;
    
    // Validate entropy value
    const validEntropy = Math.max(0, Math.min(1, entropy));
    
    // Get current time
    const currentTime = Date.now();
    const deltaTime = (currentTime - this.state.lastUpdateTime) / 1000; // in seconds
    
    // Calculate velocity (first derivative of entropy)
    const previousEntropy = this.state.currentFinancialEntropy;
    const velocity = deltaTime > 0 ? (validEntropy - previousEntropy) / deltaTime : 0;
    
    // Calculate acceleration (second derivative of entropy)
    const previousVelocity = this.state.velocityHistory.length > 0 ? 
      this.state.velocityHistory[this.state.velocityHistory.length - 1] : 0;
    const acceleration = deltaTime > 0 ? (velocity - previousVelocity) / deltaTime : 0;
    
    // Update state
    this.state.currentFinancialEntropy = validEntropy;
    this.state.lastUpdateTime = currentTime;
    
    // Add to history
    this._addToHistory('entropyHistory', validEntropy);
    this._addToHistory('velocityHistory', velocity);
    this._addToHistory('accelerationHistory', acceleration);
    
    // Check for alerts
    this._checkAlerts(validEntropy, velocity, acceleration);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit update event
    this.emit('update', {
      entropy: validEntropy,
      velocity,
      acceleration,
      timestamp: currentTime
    });
    
    return {
      entropy: validEntropy,
      velocity,
      acceleration,
      timestamp: currentTime
    };
  }
  
  /**
   * Get current meter state
   * @returns {Object} - Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Get meter metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get recent alerts
   * @param {number} limit - Maximum number of alerts to return
   * @returns {Array} - Recent alerts
   */
  getAlerts(limit = 10) {
    return this.state.alerts.slice(0, limit);
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would fetch real-time data
        // For now, just simulate entropy changes
        const randomChange = (Math.random() - 0.5) * 0.05;
        const newEntropy = Math.max(0, Math.min(1, this.state.currentFinancialEntropy + randomChange));
        
        this.updateFinancialEntropy(newEntropy);
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Add value to history
   * @param {string} historyName - Name of history array
   * @param {number} value - Value to add
   * @private
   */
  _addToHistory(historyName, value) {
    // Add value to history
    this.state[historyName].push(value);
    
    // Limit history size
    if (this.state[historyName].length > this.options.historySize) {
      this.state[historyName].shift();
    }
  }
  
  /**
   * Check for alerts
   * @param {number} entropy - Current entropy value
   * @param {number} velocity - Current velocity value
   * @param {number} acceleration - Current acceleration value
   * @private
   */
  _checkAlerts(entropy, velocity, acceleration) {
    // Check entropy thresholds
    let alertLevel = null;
    let reason = '';
    
    if (entropy >= this.options.alertThresholds.critical) {
      alertLevel = 'critical';
      reason = 'Financial entropy at critical level';
    } else if (entropy >= this.options.alertThresholds.high) {
      alertLevel = 'high';
      reason = 'Financial entropy at high level';
    } else if (entropy >= this.options.alertThresholds.medium) {
      alertLevel = 'medium';
      reason = 'Financial entropy at medium level';
    } else if (entropy >= this.options.alertThresholds.low) {
      alertLevel = 'low';
      reason = 'Financial entropy at low level';
    }
    
    // Check velocity and acceleration
    if (Math.abs(velocity) > 0.1) {
      // Rapid change in entropy
      if (velocity > 0) {
        // Increasing entropy (worsening)
        if (alertLevel !== 'critical') {
          alertLevel = alertLevel === 'high' ? 'critical' : 'high';
          reason = 'Rapid increase in financial entropy';
        }
      } else {
        // Decreasing entropy (improving)
        reason = 'Rapid decrease in financial entropy';
      }
    }
    
    if (Math.abs(acceleration) > 0.05) {
      // Acceleration in entropy change
      if (acceleration > 0) {
        // Accelerating entropy increase (worsening faster)
        if (alertLevel !== 'critical') {
          alertLevel = alertLevel === 'high' ? 'critical' : 'high';
          reason = 'Accelerating increase in financial entropy';
        }
      }
    }
    
    // Generate alert if needed
    if (alertLevel) {
      const alert = {
        level: alertLevel,
        reason,
        entropy,
        velocity,
        acceleration,
        timestamp: Date.now()
      };
      
      // Add to alerts
      this.state.alerts.unshift(alert);
      
      // Limit alerts size
      if (this.state.alerts.length > this.options.historySize) {
        this.state.alerts.pop();
      }
      
      // Update metrics
      this.metrics.alertsGenerated++;
      
      // Emit alert event
      this.emit('alert', alert);
      
      if (this.options.enableLogging) {
        console.log(`CSFE Meter Alert: ${alertLevel} - ${reason}`);
      }
    }
  }
  
  /**
   * Generate visualization data
   * @returns {Object} - Visualization data
   */
  generateVisualizationData() {
    return {
      entropy: {
        current: this.state.currentFinancialEntropy,
        history: this.state.entropyHistory,
        thresholds: this.options.alertThresholds
      },
      velocity: {
        current: this.state.velocityHistory.length > 0 ? 
          this.state.velocityHistory[this.state.velocityHistory.length - 1] : 0,
        history: this.state.velocityHistory
      },
      acceleration: {
        current: this.state.accelerationHistory.length > 0 ? 
          this.state.accelerationHistory[this.state.accelerationHistory.length - 1] : 0,
        history: this.state.accelerationHistory
      },
      alerts: this.state.alerts.slice(0, 5), // Last 5 alerts
      timestamp: Date.now()
    };
  }
}

module.exports = CSFEMeter;
