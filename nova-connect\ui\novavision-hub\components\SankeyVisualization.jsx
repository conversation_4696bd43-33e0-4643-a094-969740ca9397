/**
 * SankeyVisualization Component
 * 
 * A component for visualizing flow data as a Sankey diagram.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme';

/**
 * SankeyVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Sankey data
 * @param {Array} props.data.nodes - Nodes in the diagram
 * @param {Array} props.data.links - Links between nodes
 * @param {Object} [props.options] - Visualization options
 * @param {string} [props.options.title] - Sankey diagram title
 * @param {Array} [props.options.colorScale] - Color scale for the nodes
 * @param {boolean} [props.options.showTooltip=true] - Whether to show tooltips
 * @param {boolean} [props.options.showLabels=true] - Whether to show node labels
 * @param {boolean} [props.options.showLinkLabels=false] - Whether to show link labels
 * @param {Function} [props.options.formatValue] - Function to format link values
 * @param {number} [props.options.nodeWidth=20] - Width of nodes
 * @param {number} [props.options.nodePadding=10] - Padding between nodes
 * @param {Function} [props.onNodeClick] - Function to call when a node is clicked
 * @param {Function} [props.onLinkClick] - Function to call when a link is clicked
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} SankeyVisualization component
 */
const SankeyVisualization = ({
  data,
  options = {},
  onNodeClick,
  onLinkClick,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const containerRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [tooltip, setTooltip] = useState({ visible: false, x: 0, y: 0, content: '' });
  const [sankeyData, setSankeyData] = useState(null);
  
  // Default options
  const {
    title,
    colorScale = [
      theme.colors.primary,
      theme.colors.secondary,
      theme.colors.success,
      theme.colors.warning,
      theme.colors.error,
      theme.colors.info
    ],
    showTooltip = true,
    showLabels = true,
    showLinkLabels = false,
    formatValue = (value) => value.toLocaleString(),
    nodeWidth = 20,
    nodePadding = 10
  } = options;
  
  // Update dimensions when container size changes
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      const { width, height } = containerRef.current.getBoundingClientRect();
      setDimensions({ width, height });
    };
    
    // Initial update
    updateDimensions();
    
    // Add resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);
    
    // Cleanup
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);
  
  // Process data
  useEffect(() => {
    if (!data || !dimensions.width || !dimensions.height) return;
    
    // Calculate Sankey layout
    const sankey = calculateSankey(data, {
      width: dimensions.width - 40,
      height: dimensions.height - 40,
      nodeWidth,
      nodePadding
    });
    
    setSankeyData(sankey);
  }, [data, dimensions, nodeWidth, nodePadding]);
  
  // Calculate Sankey layout
  const calculateSankey = (data, options) => {
    const { width, height, nodeWidth, nodePadding } = options;
    
    // Clone data to avoid modifying the original
    const nodes = [...data.nodes].map((node, i) => ({
      ...node,
      index: i,
      x: 0,
      y: 0,
      width: nodeWidth,
      height: 0,
      sourceLinks: [],
      targetLinks: []
    }));
    
    const links = [...data.links].map((link, i) => ({
      ...link,
      index: i,
      source: typeof link.source === 'number' ? link.source : nodes.findIndex(n => n.id === link.source),
      target: typeof link.target === 'number' ? link.target : nodes.findIndex(n => n.id === link.target),
      value: link.value || 1,
      points: []
    }));
    
    // Compute links for each node
    links.forEach(link => {
      nodes[link.source].sourceLinks.push(link);
      nodes[link.target].targetLinks.push(link);
    });
    
    // Compute node depths
    computeNodeDepths(nodes);
    
    // Compute node heights
    computeNodeHeights(nodes, height, nodePadding);
    
    // Compute node positions
    computeNodePositions(nodes, width, height);
    
    // Compute link positions
    computeLinkPositions(links);
    
    return { nodes, links };
  };
  
  // Compute node depths
  const computeNodeDepths = (nodes) => {
    let remainingNodes = nodes;
    let nextNodes = [];
    let depth = 0;
    
    // Assign depth to nodes without incoming links
    remainingNodes.forEach(node => {
      if (node.targetLinks.length === 0) {
        node.depth = 0;
        nextNodes.push(node);
      }
    });
    
    // Assign depth to other nodes
    while (nextNodes.length > 0) {
      depth++;
      remainingNodes = remainingNodes.filter(node => !nextNodes.includes(node));
      
      const currentNodes = nextNodes;
      nextNodes = [];
      
      currentNodes.forEach(node => {
        node.sourceLinks.forEach(link => {
          const target = nodes[link.target];
          
          if (remainingNodes.includes(target)) {
            target.depth = depth;
            nextNodes.push(target);
          }
        });
      });
    }
    
    // Handle cycles by assigning remaining nodes to the deepest level
    if (remainingNodes.length > 0) {
      remainingNodes.forEach(node => {
        node.depth = depth;
      });
    }
  };
  
  // Compute node heights
  const computeNodeHeights = (nodes, height, nodePadding) => {
    // Group nodes by depth
    const nodesByDepth = {};
    nodes.forEach(node => {
      if (!nodesByDepth[node.depth]) {
        nodesByDepth[node.depth] = [];
      }
      nodesByDepth[node.depth].push(node);
    });
    
    // Calculate total value for each depth
    const depthTotals = {};
    Object.keys(nodesByDepth).forEach(depth => {
      depthTotals[depth] = nodesByDepth[depth].reduce((total, node) => {
        return total + Math.max(
          node.sourceLinks.reduce((sum, link) => sum + link.value, 0),
          node.targetLinks.reduce((sum, link) => sum + link.value, 0)
        );
      }, 0);
    });
    
    // Calculate node heights
    Object.keys(nodesByDepth).forEach(depth => {
      const nodes = nodesByDepth[depth];
      const total = depthTotals[depth];
      const availableHeight = height - (nodes.length - 1) * nodePadding;
      
      nodes.forEach(node => {
        const nodeValue = Math.max(
          node.sourceLinks.reduce((sum, link) => sum + link.value, 0),
          node.targetLinks.reduce((sum, link) => sum + link.value, 0)
        );
        
        node.height = total > 0 ? (nodeValue / total) * availableHeight : 0;
      });
    });
  };
  
  // Compute node positions
  const computeNodePositions = (nodes, width, height) => {
    // Group nodes by depth
    const nodesByDepth = {};
    nodes.forEach(node => {
      if (!nodesByDepth[node.depth]) {
        nodesByDepth[node.depth] = [];
      }
      nodesByDepth[node.depth].push(node);
    });
    
    // Calculate max depth
    const maxDepth = Math.max(...Object.keys(nodesByDepth).map(d => parseInt(d)));
    
    // Calculate x positions
    Object.keys(nodesByDepth).forEach(depth => {
      const nodes = nodesByDepth[depth];
      const d = parseInt(depth);
      
      nodes.forEach(node => {
        node.x = d * (width / maxDepth) + 20;
      });
    });
    
    // Calculate y positions
    Object.keys(nodesByDepth).forEach(depth => {
      const nodes = nodesByDepth[depth];
      
      let y = 20;
      nodes.forEach(node => {
        node.y = y;
        y += node.height + options.nodePadding;
      });
    });
  };
  
  // Compute link positions
  const computeLinkPositions = (links) => {
    links.forEach(link => {
      const source = link.source;
      const target = link.target;
      
      // Source node
      const sourceNode = typeof source === 'number' ? sankeyData.nodes[source] : source;
      
      // Target node
      const targetNode = typeof target === 'number' ? sankeyData.nodes[target] : target;
      
      // Calculate source y position
      let sourceY = sourceNode.y;
      sourceNode.sourceLinks.forEach(outLink => {
        if (outLink.index < link.index) {
          sourceY += outLink.value;
        }
      });
      
      // Calculate target y position
      let targetY = targetNode.y;
      targetNode.targetLinks.forEach(inLink => {
        if (inLink.index < link.index) {
          targetY += inLink.value;
        }
      });
      
      // Calculate link points
      link.points = [
        { x: sourceNode.x + sourceNode.width, y: sourceY + link.value / 2 },
        { x: targetNode.x, y: targetY + link.value / 2 }
      ];
    });
  };
  
  // Generate path for a link
  const generateLinkPath = (link) => {
    const source = link.points[0];
    const target = link.points[1];
    const curvature = 0.5;
    
    const x0 = source.x;
    const y0 = source.y;
    const x1 = target.x;
    const y1 = target.y;
    
    const xi = d3_interpolateNumber(x0, x1);
    const x2 = xi(curvature);
    const x3 = xi(1 - curvature);
    
    return `M${x0},${y0}C${x2},${y0} ${x3},${y1} ${x1},${y1}`;
  };
  
  // D3 interpolate number function
  const d3_interpolateNumber = (a, b) => {
    return t => a * (1 - t) + b * t;
  };
  
  // Get color for a node
  const getNodeColor = (node) => {
    if (!node) return theme.colors.divider;
    
    // Use color from node if provided
    if (node.color) return node.color;
    
    // Use color from scale
    return colorScale[node.index % colorScale.length];
  };
  
  // Get color for a link
  const getLinkColor = (link) => {
    if (!link) return theme.colors.divider;
    
    // Use color from link if provided
    if (link.color) return link.color;
    
    // Use color from source node
    const sourceNode = sankeyData.nodes[link.source];
    return getNodeColor(sourceNode);
  };
  
  // Handle node click
  const handleNodeClick = (node) => {
    if (onNodeClick) {
      onNodeClick(node);
    }
  };
  
  // Handle link click
  const handleLinkClick = (link) => {
    if (onLinkClick) {
      onLinkClick(link);
    }
  };
  
  // Handle node mouse enter
  const handleNodeMouseEnter = (event, node) => {
    if (!showTooltip) return;
    
    const rect = event.target.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    
    setTooltip({
      visible: true,
      x: rect.left - containerRect.left + rect.width / 2,
      y: rect.top - containerRect.top,
      content: node.name || `Node ${node.index}`
    });
  };
  
  // Handle link mouse enter
  const handleLinkMouseEnter = (event, link) => {
    if (!showTooltip) return;
    
    const rect = event.target.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    
    const sourceNode = sankeyData.nodes[link.source];
    const targetNode = sankeyData.nodes[link.target];
    
    setTooltip({
      visible: true,
      x: rect.left - containerRect.left + rect.width / 2,
      y: rect.top - containerRect.top,
      content: `${sourceNode.name || `Node ${link.source}`} → ${targetNode.name || `Node ${link.target}`}: ${formatValue(link.value)}`
    });
  };
  
  // Handle mouse leave
  const handleMouseLeave = () => {
    if (!showTooltip) return;
    
    setTooltip({ ...tooltip, visible: false });
  };
  
  // Check if data is valid
  if (!data || !data.nodes || !data.links) {
    return (
      <div
        ref={containerRef}
        className={`flex items-center justify-center bg-surface border border-divider rounded-md ${className}`}
        style={{ minHeight: '300px', ...style }}
        data-testid="sankey-visualization-empty"
      >
        <p className="text-textSecondary">No data available</p>
      </div>
    );
  }
  
  return (
    <div
      ref={containerRef}
      className={`relative bg-background border border-divider rounded-md overflow-hidden ${className}`}
      style={{ minHeight: '300px', ...style }}
      data-testid="sankey-visualization"
    >
      {/* Title */}
      {title && (
        <div className="absolute top-2 left-0 right-0 text-center">
          <h3 className="text-lg font-medium text-textPrimary">{title}</h3>
        </div>
      )}
      
      {/* SVG Container */}
      <svg
        width={dimensions.width}
        height={dimensions.height}
        className="font-sans text-textPrimary"
      >
        {sankeyData && (
          <g>
            {/* Links */}
            {sankeyData.links.map((link, i) => (
              <g key={`link-${i}`}>
                <path
                  d={generateLinkPath(link)}
                  fill="none"
                  stroke={getLinkColor(link)}
                  strokeOpacity={0.4}
                  strokeWidth={Math.max(1, link.value)}
                  onClick={() => handleLinkClick(link)}
                  onMouseEnter={(e) => handleLinkMouseEnter(e, link)}
                  onMouseLeave={handleMouseLeave}
                  className="cursor-pointer transition-colors duration-200 hover:stroke-opacity-60"
                  data-testid={`sankey-link-${i}`}
                />
                
                {/* Link labels */}
                {showLinkLabels && link.value > 5 && (
                  <text
                    x={(link.points[0].x + link.points[1].x) / 2}
                    y={(link.points[0].y + link.points[1].y) / 2}
                    dy={-5}
                    textAnchor="middle"
                    className="text-xs pointer-events-none fill-current text-textSecondary"
                  >
                    {formatValue(link.value)}
                  </text>
                )}
              </g>
            ))}
            
            {/* Nodes */}
            {sankeyData.nodes.map((node, i) => (
              <g key={`node-${i}`}>
                <rect
                  x={node.x}
                  y={node.y}
                  width={node.width}
                  height={node.height}
                  fill={getNodeColor(node)}
                  stroke={theme.colors.background}
                  strokeWidth={1}
                  onClick={() => handleNodeClick(node)}
                  onMouseEnter={(e) => handleNodeMouseEnter(e, node)}
                  onMouseLeave={handleMouseLeave}
                  className="cursor-pointer transition-colors duration-200 hover:opacity-80"
                  data-testid={`sankey-node-${i}`}
                />
                
                {/* Node labels */}
                {showLabels && node.height > 20 && (
                  <text
                    x={node.x + node.width / 2}
                    y={node.y + node.height / 2}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    className="text-xs pointer-events-none fill-current"
                    style={{
                      fill: 'white'
                    }}
                  >
                    {node.name || `Node ${i}`}
                  </text>
                )}
              </g>
            ))}
          </g>
        )}
      </svg>
      
      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="absolute bg-surface text-textPrimary px-2 py-1 rounded shadow-md text-xs pointer-events-none z-10 border border-divider"
          style={{
            left: tooltip.x,
            top: tooltip.y - 30,
            transform: 'translateX(-50%)'
          }}
          data-testid="sankey-tooltip"
        >
          {tooltip.content}
        </div>
      )}
    </div>
  );
};

SankeyVisualization.propTypes = {
  data: PropTypes.shape({
    nodes: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        name: PropTypes.string,
        color: PropTypes.string
      })
    ).isRequired,
    links: PropTypes.arrayOf(
      PropTypes.shape({
        source: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        target: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
        value: PropTypes.number,
        color: PropTypes.string
      })
    ).isRequired
  }),
  options: PropTypes.shape({
    title: PropTypes.string,
    colorScale: PropTypes.arrayOf(PropTypes.string),
    showTooltip: PropTypes.bool,
    showLabels: PropTypes.bool,
    showLinkLabels: PropTypes.bool,
    formatValue: PropTypes.func,
    nodeWidth: PropTypes.number,
    nodePadding: PropTypes.number
  }),
  onNodeClick: PropTypes.func,
  onLinkClick: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default SankeyVisualization;
