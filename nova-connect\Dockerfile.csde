# Build stage
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Production stage
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production \
    PORT=3001 \
    CSDE_API_URL=http://csde-api:3010 \
    LOG_LEVEL=info \
    ENABLE_CACHING=true \
    ENABLE_METRICS=true \
    CACHE_SIZE=1000 \
    DOMAIN=security \
    INPUT_DIR=/app/data/input \
    OUTPUT_DIR=/app/data/output \
    BATCH_SIZE=10

# Create app directories
RUN mkdir -p /app/data/input /app/data/output /app/logs

# Copy from build stage
COPY --from=build /app/node_modules /app/node_modules
COPY --from=build /app/package*.json /app/
COPY --from=build /app/src /app/src
COPY --from=build /app/scripts /app/scripts
COPY --from=build /app/config /app/config

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001 -G nodejs

# Set ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3001

# Set health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD node -e "const http = require('http'); const options = { hostname: 'localhost', port: 3001, path: '/health', timeout: 2000 }; const req = http.get(options, (res) => { process.exit(res.statusCode === 200 ? 0 : 1); }); req.on('error', () => { process.exit(1); });"

# Set entry point
ENTRYPOINT ["node", "scripts/run-csde-integration.js"]
