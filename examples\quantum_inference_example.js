/**
 * Quantum State Inference Example
 *
 * This example demonstrates how to use the Quantum State Inference Layer
 * with NovaVision to visualize quantum states and threat predictions.
 */

// Import required modules
const NovaStoreTrinityIntegration = require('../src/novastore/trinity_csde_integration');
const QuantumInferenceIntegration = require('../src/novavision/integrations/quantum-inference-integration');
const { NovaVision } = require('../src/novavision');
const { ROLES, PERMISSIONS } = require('../src/novavision/security/rbac');

// Example user IDs
const users = {
  ciso: 'user-123',
  securityAnalyst: 'user-456',
  standardUser: 'user-789'
};

// Create a NovaVision instance with security enabled
const novaVision = new NovaVision({
  theme: 'cyber-safety',
  enableSecurity: true,
  enableNIST: true,
  enableRBAC: true
});

// Create a NovaStore Trinity Integration instance with Quantum Inference enabled
const trinityIntegration = new NovaStoreTrinityIntegration({
  enableMetrics: true,
  enableCaching: true,
  enableQuantumInference: true
});

// Assign roles to users
const securityManager = novaVision.securityManager;
securityManager.rbac.assignRole(users.ciso, 'CISO');
securityManager.rbac.assignRole(users.securityAnalyst, 'SECURITY_ANALYST');
securityManager.rbac.assignRole(users.standardUser, 'USER');

// Create a Quantum Inference Integration instance with the same security manager
const quantumInferenceIntegration = new QuantumInferenceIntegration({
  theme: 'cyber-safety',
  colorScheme: 'quantum',
  detailLevel: 'advanced',
  enableSecurity: true,
  enableRBAC: true,
  securityManager: securityManager // Share the security manager
});



// Example component to verify
const exampleComponent = {
  id: 'component-123',
  name: 'Example Security Component',
  type: 'security',
  category: 'firewall',
  tags: ['network', 'security', 'firewall'],
  detectionCapability: 0.75,
  threatSeverity: 0.65,
  threatConfidence: 0.8,
  baselineSignals: 0.7,
  complianceScore: 0.85,
  auditFrequency: 2,
  baseResponseTime: 50,
  threatSurface: 0.8,
  systemRadius: 120,
  reactionTime: 0.3,
  mitigationSurface: 0.6,
  estimatedRevenue: 5000,
  timestamp: new Date().toISOString(),
  location: 'us-east-1',
  timePatterns: [
    {
      timestamp: new Date().toISOString(),
      window: 3600000 // 1 hour
    }
  ],
  locationPatterns: [
    {
      location: 'us-east-1'
    }
  ],

  // Add user information for security context and audit trail
  userId: users.ciso,
  roles: ['CISO'],
  permissions: ['quantum_inference:predict', 'quantum_inference:view'],
  threats: {
    'threat-1': {
      severity: 0.8,
      confidence: 0.7,
      metadata: {
        type: 'malware',
        source: 'external',
        timestamp: new Date().toISOString()
      }
    },
    'threat-2': {
      severity: 0.6,
      confidence: 0.9,
      metadata: {
        type: 'ddos',
        source: 'external',
        timestamp: new Date().toISOString()
      }
    },
    'threat-3': {
      severity: 0.4,
      confidence: 0.5,
      metadata: {
        type: 'insider',
        source: 'internal',
        timestamp: new Date().toISOString()
      }
    }
  }
};

/**
 * Run the example
 */
async function runExample() {
  console.log('=== Quantum State Inference Example ===\n');

  try {
    // Verify the component using Trinity CSDE with Quantum Inference
    console.log('Verifying component...');
    const verificationResult = await trinityIntegration.verifyComponent(exampleComponent, 'advanced');

    console.log('\nVerification Result:');
    console.log(`- Component: ${verificationResult.componentName} (${verificationResult.componentId})`);
    console.log(`- Verification Score: ${verificationResult.verificationScore.toFixed(4)}`);
    console.log(`- Verification Status: ${verificationResult.verificationStatus}`);

    // Check if quantum inference was applied
    if (verificationResult.quantumInference) {
      console.log('\nQuantum Inference Applied:');
      console.log(`- Collapsed States: ${verificationResult.quantumInference.collapsedStates}`);
      console.log(`- Certainty Rate: ${verificationResult.quantumInference.certaintyRate ? verificationResult.quantumInference.certaintyRate.toFixed(4) : 'N/A'}`);
      console.log(`- Actionable Intelligence: ${verificationResult.quantumInference.actionableIntelligence.length} items`);

      // Debug the quantumInference object
      console.log('\nQuantum Inference Object Structure:');
      console.log(JSON.stringify(verificationResult.quantumInference, null, 2));

      // Get quantum predictions
      const quantumPredictions = trinityIntegration.getQuantumPredictions();
      console.log(`\nQuantum Predictions: ${quantumPredictions.length}`);

      // Generate quantum dashboard for different users
      console.log('\nGenerating Quantum Dashboards for Different Users:');

      // CISO dashboard
      console.log('\n1. CISO Dashboard:');
      const cisoDashboard = quantumInferenceIntegration.renderQuantumDashboard(
        verificationResult.quantumInference,
        { userId: users.ciso }
      );
      console.log(`- Dashboard ID: ${cisoDashboard.id}`);
      console.log(`- Title: ${cisoDashboard.title}`);
      console.log(`- Sections: ${cisoDashboard.sections.length}`);
      console.log(`- Section IDs: ${cisoDashboard.sections.map(s => s.id).join(', ')}`);

      // Security Analyst dashboard
      console.log('\n2. Security Analyst Dashboard:');
      const analystDashboard = quantumInferenceIntegration.renderQuantumDashboard(
        verificationResult.quantumInference,
        { userId: users.securityAnalyst }
      );
      console.log(`- Dashboard ID: ${analystDashboard.id}`);
      console.log(`- Title: ${analystDashboard.title}`);
      console.log(`- Sections: ${analystDashboard.sections.length}`);
      console.log(`- Section IDs: ${analystDashboard.sections.map(s => s.id).join(', ')}`);

      // Standard User dashboard (should be restricted)
      console.log('\n3. Standard User Dashboard:');
      const userDashboard = quantumInferenceIntegration.renderQuantumDashboard(
        verificationResult.quantumInference,
        { userId: users.standardUser }
      );
      console.log(`- Dashboard ID: ${userDashboard.id}`);
      console.log(`- Title: ${userDashboard.title}`);
      console.log(`- Sections: ${userDashboard.sections.length}`);
      if (userDashboard.sections.length > 0) {
        console.log(`- Section IDs: ${userDashboard.sections.map(s => s.id).join(', ')}`);
      }

      // Validate access to quantum inference
      console.log('\nValidating Access to Quantum Inference:');
      console.log(`- CISO: ${novaVision.validateAccess(users.ciso, 'quantum_inference') || novaVision.securityManager.rbac.hasPermission(users.ciso, 'view:quantum_inference')}`);
      console.log(`- Security Analyst: ${novaVision.validateAccess(users.securityAnalyst, 'quantum_inference') || novaVision.securityManager.rbac.hasPermission(users.securityAnalyst, 'view:quantum_inference')}`);
      console.log(`- Standard User: ${novaVision.validateAccess(users.standardUser, 'quantum_inference') || novaVision.securityManager.rbac.hasPermission(users.standardUser, 'view:quantum_inference')}`);
    } else {
      console.log('\nQuantum Inference not applied to this component.');
    }

    // Get performance metrics
    const performanceMetrics = trinityIntegration.getPerformanceMetrics();
    console.log('\nPerformance Metrics:');
    if (performanceMetrics.quantumInference) {
      console.log(`- Inference Count: ${performanceMetrics.quantumInference.inferenceCount}`);
      console.log(`- Average Inference Time: ${performanceMetrics.quantumInference.averageInferenceTime.toFixed(2)} ms`);
      console.log(`- Collapse Events: ${performanceMetrics.quantumInference.collapseEvents}`);
      console.log(`- Certainty Rate: ${(performanceMetrics.quantumInference.certaintyRate * 100).toFixed(2)}%`);
    } else {
      console.log('No quantum inference metrics available.');
    }
  } catch (error) {
    console.error('Error running example:', error);
  }

  console.log('\n=== End of Example ===');
}

// Run the example
runExample();

/**
 * How to run this example:
 *
 * 1. Make sure all dependencies are installed
 * 2. Run the example using Node.js:
 *    node examples/quantum_inference_example.js
 */
