<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology Framework API Documentation</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding-top: 20px;
      color: #333;
    }
    .container {
      max-width: 1200px;
    }
    .card {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #6c757d;
      color: white;
      font-weight: bold;
      border-radius: 10px 10px 0 0 !important;
    }
    pre {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 5px;
      padding: 15px;
      margin-top: 10px;
    }
    .endpoint {
      background-color: #e9ecef;
      border-radius: 5px;
      padding: 10px;
      margin-bottom: 15px;
    }
    .method {
      font-weight: bold;
      color: #007bff;
    }
    .path {
      font-family: monospace;
      font-weight: bold;
    }
    .description {
      margin-top: 10px;
    }
    .parameters {
      margin-top: 15px;
    }
    .parameter {
      margin-bottom: 5px;
    }
    .parameter-name {
      font-weight: bold;
      font-family: monospace;
    }
    .parameter-type {
      color: #6c757d;
      font-style: italic;
    }
    .parameter-required {
      color: #dc3545;
      font-weight: bold;
    }
    .parameter-optional {
      color: #28a745;
    }
    .response {
      margin-top: 15px;
    }
    .response-code {
      font-weight: bold;
      color: #28a745;
    }
    .response-description {
      margin-left: 5px;
    }
    .nav-pills .nav-link.active {
      background-color: #6c757d;
    }
    .nav-pills .nav-link {
      color: #495057;
    }
    .nav-pills .nav-link:hover {
      color: #007bff;
    }
    h1, h2, h3, h4 {
      color: #343a40;
    }
    .equation {
      font-family: 'Cambria Math', serif;
      font-size: 1.2em;
      text-align: center;
      margin: 20px 0;
    }
    .framework-description {
      margin-bottom: 30px;
    }
    .component-icon {
      font-size: 2em;
      margin-right: 10px;
    }
    .toc {
      position: sticky;
      top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="text-center mb-4">Comphyology Framework API Documentation</h1>
    
    <div class="row">
      <div class="col-md-3">
        <div class="toc card">
          <div class="card-header">
            Table of Contents
          </div>
          <div class="card-body">
            <nav class="nav flex-column nav-pills">
              <a class="nav-link active" href="#overview">Overview</a>
              <a class="nav-link" href="#components">Framework Components</a>
              <a class="nav-link" href="#api-reference">API Reference</a>
              <a class="nav-link" href="#uuft">UUFT Equation</a>
              <a class="nav-link" href="#principle1882">18/82 Principle</a>
              <a class="nav-link" href="#piphie">πφe Scoring System</a>
              <a class="nav-link" href="#nestedtrinity">Nested Trinity</a>
              <a class="nav-link" href="#finiteuniverse">Finite Universe Math</a>
              <a class="nav-link" href="#examples">Code Examples</a>
              <a class="nav-link" href="#dashboard">Dashboard</a>
            </nav>
          </div>
        </div>
      </div>
      
      <div class="col-md-9">
        <section id="overview" class="mb-5">
          <div class="card">
            <div class="card-header">
              Overview
            </div>
            <div class="card-body">
              <div class="framework-description">
                <p>The Comphyology Framework is a philosophical/mathematical foundation based on Finite Universe Math, structured as a Nested Trinity with three layers: Micro, Meso, and Macro. It provides a comprehensive approach to solving complex problems across different domains.</p>
                
                <p>The framework is built on several key components:</p>
                <ul>
                  <li><strong>Universal Unified Field Theory (UUFT)</strong>: Uses the equation (A ⊗ B ⊕ C) × π10³ and has consistently delivered 3,142x performance improvement across domains.</li>
                  <li><strong>Nested Trinity Structure</strong>: Organizes systems into Micro (Ψ₁), Meso (Ψ₂), and Macro (Ψ₃) layers for optimal interaction and energy transfer.</li>
                  <li><strong>18/82 Principle</strong>: Applies optimal resource allocation with 18% for key components and 82% for complementary components.</li>
                  <li><strong>πφe Scoring System</strong>: Quantifies intelligence and coherence using π for governance, φ for resonance, and e for adaptation.</li>
                  <li><strong>Finite Universe Math</strong>: Provides bounded computational models, deterministic outcome validators, and quantifiable intelligence metrics.</li>
                </ul>
                
                <div class="equation">
                  <strong>UUFT Equation:</strong> (A ⊗ B ⊕ C) × π10³
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <section id="components" class="mb-5">
          <div class="card">
            <div class="card-header">
              Framework Components
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 mb-4">
                  <h4><span class="component-icon">⊗</span> UUFT</h4>
                  <p>The Universal Unified Field Theory provides a mathematical foundation for the framework, using tensor operations and fusion operators to achieve optimal performance.</p>
                </div>
                <div class="col-md-6 mb-4">
                  <h4><span class="component-icon">Ψ</span> Nested Trinity</h4>
                  <p>The Nested Trinity structure organizes systems into three layers (Micro, Meso, Macro) for optimal interaction, energy transfer, and cross-domain communication.</p>
                </div>
                <div class="col-md-6 mb-4">
                  <h4><span class="component-icon">%</span> 18/82 Principle</h4>
                  <p>The 18/82 Principle applies optimal resource allocation with 18% for key components and 82% for complementary components, ensuring maximum efficiency.</p>
                </div>
                <div class="col-md-6 mb-4">
                  <h4><span class="component-icon">πφe</span> Scoring System</h4>
                  <p>The πφe Scoring System quantifies intelligence and coherence using π for governance, φ for resonance, and e for adaptation, providing a comprehensive metric.</p>
                </div>
                <div class="col-md-6 mb-4">
                  <h4><span class="component-icon">∞</span> Finite Universe Math</h4>
                  <p>Finite Universe Math provides bounded computational models, deterministic outcome validators, and quantifiable intelligence metrics for reliable system behavior.</p>
                </div>
                <div class="col-md-6 mb-4">
                  <h4><span class="component-icon">⚙️</span> Component Alignment</h4>
                  <p>Component Alignment ensures that all NovaFuse components (NovaCore, NovaProof, NovaConnect, NovaVision) are properly integrated with the Comphyology Framework.</p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <section id="api-reference" class="mb-5">
          <div class="card">
            <div class="card-header">
              API Reference
            </div>
            <div class="card-body">
              <p>The Comphyology Framework API provides endpoints for interacting with all components of the framework. All endpoints are prefixed with <code>/api/comphyology</code>.</p>
              
              <div class="endpoint">
                <span class="method">GET</span>
                <span class="path">/status</span>
                <div class="description">Get the status of all Comphyology Framework components.</div>
                <div class="response">
                  <span class="response-code">200</span>
                  <span class="response-description">Returns the status and performance metrics of all components.</span>
                </div>
                <pre><code class="language-json">{
  "status": "active",
  "components": {
    "uuft": { ... },
    "nestedTrinity": { ... },
    "principle1882": { ... },
    "piPhiEScoring": { ... },
    "finiteUniverseMath": { ... },
    "componentAlignment": { ... }
  },
  "overall": { ... }
}</code></pre>
              </div>
            </div>
          </div>
        </section>
        
        <section id="uuft" class="mb-5">
          <div class="card">
            <div class="card-header">
              UUFT Equation API
            </div>
            <div class="card-body">
              <div class="endpoint">
                <span class="method">POST</span>
                <span class="path">/uuft/apply</span>
                <div class="description">Apply the UUFT equation (A ⊗ B ⊕ C) × π10³ to the provided values.</div>
                <div class="parameters">
                  <div class="parameter">
                    <span class="parameter-name">A</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>First input component</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">B</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>Second input component</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">C</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>Third input component</div>
                  </div>
                </div>
                <div class="response">
                  <span class="response-code">200</span>
                  <span class="response-description">Returns the result of applying the UUFT equation.</span>
                </div>
                <pre><code class="language-json">{
  "result": 2361.6046039279804
}</code></pre>
              </div>
            </div>
          </div>
        </section>
        
        <section id="principle1882" class="mb-5">
          <div class="card">
            <div class="card-header">
              18/82 Principle API
            </div>
            <div class="card-body">
              <div class="endpoint">
                <span class="method">POST</span>
                <span class="path">/principle1882/apply</span>
                <div class="description">Apply the 18/82 Principle to the provided key and complementary components.</div>
                <div class="parameters">
                  <div class="parameter">
                    <span class="parameter-name">keyComponent</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>Key component (18%)</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">complementaryComponent</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>Complementary component (82%)</div>
                  </div>
                </div>
                <div class="response">
                  <span class="response-code">200</span>
                  <span class="response-description">Returns the result of applying the 18/82 Principle.</span>
                </div>
                <pre><code class="language-json">{
  "result": 0.572
}</code></pre>
              </div>
            </div>
          </div>
        </section>
        
        <section id="piphie" class="mb-5">
          <div class="card">
            <div class="card-header">
              πφe Scoring System API
            </div>
            <div class="card-body">
              <div class="endpoint">
                <span class="method">POST</span>
                <span class="path">/piphie/calculate</span>
                <div class="description">Calculate the πφe score for the provided π, φ, and e values.</div>
                <div class="parameters">
                  <div class="parameter">
                    <span class="parameter-name">piScore</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>π (Governance) score (0-1)</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">phiScore</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>φ (Resonance) score (0-1)</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">eScore</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>e (Adaptation) score (0-1)</div>
                  </div>
                </div>
                <div class="response">
                  <span class="response-code">200</span>
                  <span class="response-description">Returns the calculated πφe score.</span>
                </div>
                <pre><code class="language-json">{
  "result": 0.7800000000000001
}</code></pre>
              </div>
            </div>
          </div>
        </section>
        
        <section id="nestedtrinity" class="mb-5">
          <div class="card">
            <div class="card-header">
              Nested Trinity API
            </div>
            <div class="card-body">
              <div class="endpoint">
                <span class="method">POST</span>
                <span class="path">/nestedtrinity/communicate</span>
                <div class="description">Send data from one layer to another in the Nested Trinity structure.</div>
                <div class="parameters">
                  <div class="parameter">
                    <span class="parameter-name">sourceLayer</span>
                    <span class="parameter-type">(string)</span>
                    <span class="parameter-required">Required</span>
                    <div>Source layer ('micro', 'meso', 'macro')</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">targetLayer</span>
                    <span class="parameter-type">(string)</span>
                    <span class="parameter-required">Required</span>
                    <div>Target layer ('micro', 'meso', 'macro')</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">sourceId</span>
                    <span class="parameter-type">(string)</span>
                    <span class="parameter-required">Required</span>
                    <div>Source component ID</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">data</span>
                    <span class="parameter-type">(object)</span>
                    <span class="parameter-required">Required</span>
                    <div>Data to send</div>
                  </div>
                </div>
                <div class="response">
                  <span class="response-code">200</span>
                  <span class="response-description">Returns the processed data.</span>
                </div>
                <pre><code class="language-json">{
  "sourceId": "testComponent",
  "layer": "micro",
  "targetLayer": "meso",
  "data": {
    "value": 42
  }
}</code></pre>
              </div>
            </div>
          </div>
        </section>
        
        <section id="finiteuniverse" class="mb-5">
          <div class="card">
            <div class="card-header">
              Finite Universe Math API
            </div>
            <div class="card-body">
              <div class="endpoint">
                <span class="method">POST</span>
                <span class="path">/finiteuniverse/applyBoundary</span>
                <div class="description">Apply a boundary condition to a value.</div>
                <div class="parameters">
                  <div class="parameter">
                    <span class="parameter-name">value</span>
                    <span class="parameter-type">(number)</span>
                    <span class="parameter-required">Required</span>
                    <div>Value to apply boundary to</div>
                  </div>
                  <div class="parameter">
                    <span class="parameter-name">boundaryType</span>
                    <span class="parameter-type">(string)</span>
                    <span class="parameter-required">Required</span>
                    <div>Boundary type ('computational', 'temporal', 'spatial', 'energetic')</div>
                  </div>
                </div>
                <div class="response">
                  <span class="response-code">200</span>
                  <span class="response-description">Returns the bounded value.</span>
                </div>
                <pre><code class="language-json">{
  "result": 1
}</code></pre>
              </div>
            </div>
          </div>
        </section>
        
        <section id="examples" class="mb-5">
          <div class="card">
            <div class="card-header">
              Code Examples
            </div>
            <div class="card-body">
              <h4>JavaScript Example</h4>
              <pre><code class="language-javascript">// Apply UUFT Equation
fetch('/api/comphyology/uuft/apply', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    A: 0.5,
    B: 0.7,
    C: 0.3
  })
})
.then(response => response.json())
.then(data => console.log('UUFT Result:', data.result))
.catch(error => console.error('Error:', error));

// Apply 18/82 Principle
fetch('/api/comphyology/principle1882/apply', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    keyComponent: 0.9,
    complementaryComponent: 0.5
  })
})
.then(response => response.json())
.then(data => console.log('18/82 Principle Result:', data.result))
.catch(error => console.error('Error:', error));</code></pre>
              
              <h4>Python Example</h4>
              <pre><code class="language-python">import requests
import json

# Apply UUFT Equation
response = requests.post(
    'http://localhost:3001/api/comphyology/uuft/apply',
    json={
        'A': 0.5,
        'B': 0.7,
        'C': 0.3
    }
)
print('UUFT Result:', response.json()['result'])

# Apply 18/82 Principle
response = requests.post(
    'http://localhost:3001/api/comphyology/principle1882/apply',
    json={
        'keyComponent': 0.9,
        'complementaryComponent': 0.5
    }
)
print('18/82 Principle Result:', response.json()['result'])</code></pre>
            </div>
          </div>
        </section>
        
        <section id="dashboard" class="mb-5">
          <div class="card">
            <div class="card-header">
              Dashboard
            </div>
            <div class="card-body">
              <p>The Comphyology Framework Dashboard provides a user-friendly interface for interacting with all components of the framework. You can access the dashboard at <a href="/comphyology-dashboard">/comphyology-dashboard</a>.</p>
              
              <p>The dashboard includes:</p>
              <ul>
                <li>Real-time status monitoring of all framework components</li>
                <li>Interactive forms for applying the UUFT equation, 18/82 Principle, πφe Scoring System, etc.</li>
                <li>Visualization of performance metrics</li>
                <li>Cross-layer communication interface for the Nested Trinity structure</li>
              </ul>
              
              <div class="text-center mt-4">
                <a href="/comphyology-dashboard" class="btn btn-primary">Open Dashboard</a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize syntax highlighting
      hljs.highlightAll();
      
      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
          e.preventDefault();
          
          const targetId = this.getAttribute('href');
          const targetElement = document.querySelector(targetId);
          
          if (targetElement) {
            window.scrollTo({
              top: targetElement.offsetTop - 20,
              behavior: 'smooth'
            });
            
            // Update active nav link
            document.querySelectorAll('.nav-link').forEach(link => {
              link.classList.remove('active');
            });
            this.classList.add('active');
          }
        });
      });
    });
  </script>
</body>
</html>
