/**
 * NovaPulse API Client
 *
 * This client provides access to the NovaPulse API endpoints.
 * NovaPulse is the Universal Regulatory Compliance Monitoring System (URCMS) component
 * of the NovaFuse Cyber-Safety Platform.
 */

import { AxiosInstance, AxiosRequestConfig } from 'axios';
import axiosInstance from './axiosConfig';
import authService from './authService';

// Types
export interface Regulation {
  _id: string;
  name: string;
  shortName: string;
  description: string;
  type: string;
  category: string;
  status: string;
  jurisdiction: {
    country: string;
    region: string;
    isGlobal: boolean;
  };
  applicability: {
    industries: string[];
    organizationTypes: string[];
    dataTypes: string[];
  };
  requirements: Array<{
    id: string;
    name: string;
    description: string;
    section: string;
    article: string;
    text: string;
    status: string;
  }>;
  versions: Array<{
    versionNumber: string;
    name: string;
    description: string;
    publicationDate: string;
    effectiveDate: string;
    status: string;
  }>;
  currentVersion: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface RegulatoryChange {
  _id: string;
  regulationId: string;
  regulationName: string;
  type: string;
  title: string;
  description: string;
  summary: string;
  source: string;
  sourceUrl: string;
  publicationDate: string;
  effectiveDate: string;
  complianceDeadline: string;
  jurisdiction: {
    country: string;
    region: string;
    isGlobal: boolean;
  };
  category: string;
  status: string;
  priority: string;
  applicability: {
    industries: string[];
    organizationTypes: string[];
    dataTypes: string[];
  };
  changes: Array<{
    requirementId: string;
    type: string;
    description: string;
    previousText: string;
    newText: string;
    section: string;
    article: string;
  }>;
  impactAssessment: {
    level: string;
    description: string;
    affectedAreas: string[];
    requiredActions: Array<{
      type: string;
      description: string;
      priority: string;
      dueDate: string;
    }>;
  };
  implementationStatus: {
    status: string;
    progress: number;
    notes: string;
    lastUpdated: string;
    updatedBy: string;
  };
  notifications: Array<{
    id: string;
    type: string;
    recipients: string[];
    sentAt: string;
    status: string;
    error: string;
  }>;
  relatedWorkflows: Array<{
    id: string;
    name: string;
    type: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    assignedTo: string;
  }>;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface Framework {
  _id: string;
  name: string;
  shortName: string;
  description: string;
  type: string;
  category: string;
  status: string;
  jurisdiction: {
    country: string;
    region: string;
    isGlobal: boolean;
  };
  industry: string;
  applicability: {
    industries: string[];
    organizationTypes: string[];
    dataTypes: string[];
  };
  domains: Array<{
    id: string;
    name: string;
    description: string;
  }>;
  categories: Array<{
    id: string;
    name: string;
    description: string;
    domainId: string;
  }>;
  controls: Array<{
    id: string;
    name: string;
    description: string;
    domainId: string;
    categoryId: string;
    objective: string;
    guidance: string;
    references: Array<{
      frameworkId: string;
      controlId: string;
      mappingStrength: string;
    }>;
    status: string;
  }>;
  versions: Array<{
    versionNumber: string;
    name: string;
    description: string;
    publicationDate: string;
    effectiveDate: string;
    status: string;
  }>;
  currentVersion: string;
  isCustom: boolean;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface ComplianceProfile {
  _id: string;
  name: string;
  description: string;
  status: string;
  organizationId: string;
  organizationDetails: {
    name: string;
    industry: string;
    size: string;
    type: string;
  };
  jurisdictions: Array<{
    country: string;
    region: string;
    isGlobal: boolean;
  }>;
  dataInventory: Array<{
    dataType: string;
    classification: string;
    volume: string;
    storage: string;
    processing: string;
    transfer: string;
  }>;
  businessActivities: Array<{
    name: string;
    description: string;
    dataTypes: string[];
    riskLevel: string;
  }>;
  applicableFrameworks: Array<{
    frameworkId: string;
    frameworkName: string;
    status: string;
    complianceStatus: {
      status: string;
      score: number;
      lastAssessment: string;
      nextAssessment: string;
    };
    controlImplementation: {
      implemented: number;
      partiallyImplemented: number;
      notImplemented: number;
      notApplicable: number;
      total: number;
    };
  }>;
  applicableRegulations: Array<{
    regulationId: string;
    regulationName: string;
    status: string;
    complianceStatus: {
      status: string;
      score: number;
      lastAssessment: string;
      nextAssessment: string;
    };
    requirementImplementation: {
      implemented: number;
      partiallyImplemented: number;
      notImplemented: number;
      notApplicable: number;
      total: number;
    };
  }>;
  overallComplianceStatus: {
    status: string;
    score: number;
    lastUpdated: string;
  };
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export class NovaPulseAPI {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor(baseUrl = '/api/v1/novapulse') {
    this.baseUrl = baseUrl;

    // Use the configured axios instance with authentication and error handling
    this.client = axiosInstance;

    // Override the base URL if provided
    if (baseUrl !== '/api/v1/novapulse') {
      this.client.defaults.baseURL = baseUrl;
    }
  }

  // Regulation Methods
  async getRegulations(params?: any): Promise<PaginatedResponse<Regulation>> {
    const response = await this.client.get('/regulations', { params });
    return response.data;
  }

  async getRegulationById(id: string): Promise<Regulation> {
    const response = await this.client.get(`/regulations/${id}`);
    return response.data.data;
  }

  async createRegulation(data: Partial<Regulation>): Promise<Regulation> {
    const response = await this.client.post('/regulations', data);
    return response.data.data;
  }

  async updateRegulation(id: string, data: Partial<Regulation>): Promise<Regulation> {
    const response = await this.client.put(`/regulations/${id}`, data);
    return response.data.data;
  }

  async deleteRegulation(id: string): Promise<boolean> {
    const response = await this.client.delete(`/regulations/${id}`);
    return response.data.success;
  }

  async addRequirement(regulationId: string, requirement: any): Promise<Regulation> {
    const response = await this.client.post(`/regulations/${regulationId}/requirements`, requirement);
    return response.data.data;
  }

  async updateRequirement(regulationId: string, requirementId: string, data: any): Promise<Regulation> {
    const response = await this.client.put(`/regulations/${regulationId}/requirements/${requirementId}`, data);
    return response.data.data;
  }

  async removeRequirement(regulationId: string, requirementId: string): Promise<Regulation> {
    const response = await this.client.delete(`/regulations/${regulationId}/requirements/${requirementId}`);
    return response.data.data;
  }

  async addVersion(regulationId: string, version: any): Promise<Regulation> {
    const response = await this.client.post(`/regulations/${regulationId}/versions`, version);
    return response.data.data;
  }

  async findRegulationsByJurisdiction(country?: string, region?: string): Promise<Regulation[]> {
    const response = await this.client.get('/regulations/jurisdiction', { params: { country, region } });
    return response.data.data;
  }

  async findRegulationsByCategory(category: string): Promise<Regulation[]> {
    const response = await this.client.get(`/regulations/category/${category}`);
    return response.data.data;
  }

  async findRegulationsByApplicability(criteria: any): Promise<Regulation[]> {
    const response = await this.client.get('/regulations/applicability', { params: criteria });
    return response.data.data;
  }

  // Regulatory Change Methods
  async getRegulatoryChanges(organizationId: string, params?: any): Promise<PaginatedResponse<RegulatoryChange>> {
    const response = await this.client.get(`/organizations/${organizationId}/regulatory-changes`, { params });
    return response.data;
  }

  async getRegulatoryChangeById(id: string): Promise<RegulatoryChange> {
    const response = await this.client.get(`/regulatory-changes/${id}`);
    return response.data.data;
  }

  async createRegulatoryChange(organizationId: string, data: Partial<RegulatoryChange>): Promise<RegulatoryChange> {
    const response = await this.client.post(`/organizations/${organizationId}/regulatory-changes`, data);
    return response.data.data;
  }

  async updateRegulatoryChange(id: string, data: Partial<RegulatoryChange>): Promise<RegulatoryChange> {
    const response = await this.client.put(`/regulatory-changes/${id}`, data);
    return response.data.data;
  }

  async deleteRegulatoryChange(id: string): Promise<boolean> {
    const response = await this.client.delete(`/regulatory-changes/${id}`);
    return response.data.success;
  }

  async addImpactAssessment(id: string, assessment: any): Promise<RegulatoryChange> {
    const response = await this.client.post(`/regulatory-changes/${id}/impact-assessment`, assessment);
    return response.data.data;
  }

  async updateImplementationStatus(id: string, status: any): Promise<RegulatoryChange> {
    const response = await this.client.put(`/regulatory-changes/${id}/implementation-status`, status);
    return response.data.data;
  }

  async findPendingRegulatoryChanges(organizationId: string): Promise<RegulatoryChange[]> {
    const response = await this.client.get(`/organizations/${organizationId}/regulatory-changes/pending`);
    return response.data.data;
  }

  async findOverdueRegulatoryChanges(organizationId: string): Promise<RegulatoryChange[]> {
    const response = await this.client.get(`/organizations/${organizationId}/regulatory-changes/overdue`);
    return response.data.data;
  }

  async findUpcomingRegulatoryChanges(organizationId: string, days: number = 30): Promise<RegulatoryChange[]> {
    const response = await this.client.get(`/organizations/${organizationId}/regulatory-changes/upcoming`, { params: { days } });
    return response.data.data;
  }

  // Framework Methods
  async getFrameworks(params?: any): Promise<PaginatedResponse<Framework>> {
    const response = await this.client.get('/frameworks', { params });
    return response.data;
  }

  async getFrameworkById(id: string): Promise<Framework> {
    const response = await this.client.get(`/frameworks/${id}`);
    return response.data.data;
  }

  async createFramework(data: Partial<Framework>): Promise<Framework> {
    const response = await this.client.post('/frameworks', data);
    return response.data.data;
  }

  async createCustomFramework(organizationId: string, data: Partial<Framework>): Promise<Framework> {
    const response = await this.client.post(`/organizations/${organizationId}/frameworks`, data);
    return response.data.data;
  }

  async updateFramework(id: string, data: Partial<Framework>): Promise<Framework> {
    const response = await this.client.put(`/frameworks/${id}`, data);
    return response.data.data;
  }

  async deleteFramework(id: string): Promise<boolean> {
    const response = await this.client.delete(`/frameworks/${id}`);
    return response.data.success;
  }

  async addControl(frameworkId: string, control: any): Promise<Framework> {
    const response = await this.client.post(`/frameworks/${frameworkId}/controls`, control);
    return response.data.data;
  }

  async updateControl(frameworkId: string, controlId: string, data: any): Promise<Framework> {
    const response = await this.client.put(`/frameworks/${frameworkId}/controls/${controlId}`, data);
    return response.data.data;
  }

  async removeControl(frameworkId: string, controlId: string): Promise<Framework> {
    const response = await this.client.delete(`/frameworks/${frameworkId}/controls/${controlId}`);
    return response.data.data;
  }

  async addDomain(frameworkId: string, domain: any): Promise<Framework> {
    const response = await this.client.post(`/frameworks/${frameworkId}/domains`, domain);
    return response.data.data;
  }

  async addCategory(frameworkId: string, category: any): Promise<Framework> {
    const response = await this.client.post(`/frameworks/${frameworkId}/categories`, category);
    return response.data.data;
  }

  async findFrameworksByType(type: string): Promise<Framework[]> {
    const response = await this.client.get(`/frameworks/type/${type}`);
    return response.data.data;
  }

  async findFrameworksByCategory(category: string): Promise<Framework[]> {
    const response = await this.client.get(`/frameworks/category/${category}`);
    return response.data.data;
  }

  async findFrameworksByJurisdiction(country?: string, region?: string): Promise<Framework[]> {
    const response = await this.client.get('/frameworks/jurisdiction', { params: { country, region } });
    return response.data.data;
  }

  async findCustomFrameworks(organizationId: string): Promise<Framework[]> {
    const response = await this.client.get(`/organizations/${organizationId}/frameworks/custom`);
    return response.data.data;
  }

  // Compliance Profile Methods
  async getComplianceProfiles(organizationId: string, params?: any): Promise<PaginatedResponse<ComplianceProfile>> {
    const response = await this.client.get(`/organizations/${organizationId}/compliance-profiles`, { params });
    return response.data;
  }

  async getComplianceProfileById(id: string): Promise<ComplianceProfile> {
    const response = await this.client.get(`/compliance-profiles/${id}`);
    return response.data.data;
  }

  async createComplianceProfile(organizationId: string, data: Partial<ComplianceProfile>): Promise<ComplianceProfile> {
    const response = await this.client.post(`/organizations/${organizationId}/compliance-profiles`, data);
    return response.data.data;
  }

  async updateComplianceProfile(id: string, data: Partial<ComplianceProfile>): Promise<ComplianceProfile> {
    const response = await this.client.put(`/compliance-profiles/${id}`, data);
    return response.data.data;
  }

  async deleteComplianceProfile(id: string): Promise<boolean> {
    const response = await this.client.delete(`/compliance-profiles/${id}`);
    return response.data.success;
  }

  async addApplicableFramework(profileId: string, framework: any): Promise<ComplianceProfile> {
    const response = await this.client.post(`/compliance-profiles/${profileId}/frameworks`, framework);
    return response.data.data;
  }

  async updateApplicableFramework(profileId: string, frameworkId: string, data: any): Promise<ComplianceProfile> {
    const response = await this.client.put(`/compliance-profiles/${profileId}/frameworks/${frameworkId}`, data);
    return response.data.data;
  }

  async removeApplicableFramework(profileId: string, frameworkId: string): Promise<ComplianceProfile> {
    const response = await this.client.delete(`/compliance-profiles/${profileId}/frameworks/${frameworkId}`);
    return response.data.data;
  }

  async addApplicableRegulation(profileId: string, regulation: any): Promise<ComplianceProfile> {
    const response = await this.client.post(`/compliance-profiles/${profileId}/regulations`, regulation);
    return response.data.data;
  }

  async addDataInventoryItem(profileId: string, dataItem: any): Promise<ComplianceProfile> {
    const response = await this.client.post(`/compliance-profiles/${profileId}/data-inventory`, dataItem);
    return response.data.data;
  }

  async addBusinessActivity(profileId: string, activity: any): Promise<ComplianceProfile> {
    const response = await this.client.post(`/compliance-profiles/${profileId}/business-activities`, activity);
    return response.data.data;
  }

  async updateOverallComplianceStatus(profileId: string): Promise<ComplianceProfile> {
    const response = await this.client.post(`/compliance-profiles/${profileId}/update-compliance-status`, {});
    return response.data.data;
  }

  // Visualization Data Methods
  async getFrameworkVisualizationData(frameworkIds: string[]): Promise<any> {
    // This method will generate visualization data for frameworks
    // It will be used by the FrameworkVisualizer component

    try {
      // First, get the frameworks
      const frameworks = await Promise.all(
        frameworkIds.map(id => this.getFrameworkById(id))
      );

      // Create nodes for frameworks and controls
      const nodes = [];
      const links = [];

      // Add framework nodes
      frameworks.forEach(framework => {
        nodes.push({
          id: framework._id,
          name: framework.name,
          type: 'framework',
          val: 20 // Size
        });

        // Add control nodes
        framework.controls.forEach(control => {
          const controlId = `${framework._id}:${control.id}`;
          nodes.push({
            id: controlId,
            name: control.name,
            description: control.description,
            type: 'control',
            framework: framework._id,
            val: 5 // Size
          });

          // Link control to framework
          links.push({
            source: framework._id,
            target: controlId,
            type: 'contains'
          });
        });
      });

      // Add cross-framework links based on control references
      frameworks.forEach(framework => {
        framework.controls.forEach(control => {
          if (control.references && control.references.length > 0) {
            control.references.forEach(reference => {
              if (frameworkIds.includes(reference.frameworkId)) {
                links.push({
                  source: `${framework._id}:${control.id}`,
                  target: `${reference.frameworkId}:${reference.controlId}`,
                  type: 'maps_to',
                  confidence: reference.mappingStrength === 'strong' ? 0.9 :
                              reference.mappingStrength === 'moderate' ? 0.7 : 0.5
                });
              }
            });
          }
        });
      });

      return { nodes, links };
    } catch (error) {
      console.error('Error getting visualization data:', error);
      return { nodes: [], links: [] };
    }
  }

  /**
   * Get the current user's organization ID
   * @returns Organization ID or throws an error if not available
   */
  getOrganizationId(): string {
    const orgId = authService.getOrganizationId();
    if (!orgId) {
      throw new Error('Organization ID not available. User may not be authenticated.');
    }
    return orgId;
  }

  async getComplianceStatusData(organizationId?: string): Promise<any> {
    // This method will generate data for compliance status charts
    try {
      // Use provided organizationId or get from auth service
      const orgId = organizationId || this.getOrganizationId();

      // Get compliance profiles for the organization
      const response = await this.getComplianceProfiles(orgId);
      const profiles = response.data;

      if (profiles.length === 0) {
        return [];
      }

      // Use the first profile (most organizations will have just one)
      const profile = profiles[0];

      // Generate data for frameworks
      const frameworkData = profile.applicableFrameworks.map(framework => ({
        framework: framework.frameworkName,
        compliant: Math.round(framework.complianceStatus.score),
        gap: 100 - Math.round(framework.complianceStatus.score)
      }));

      return frameworkData;
    } catch (error) {
      console.error('Error getting compliance status data:', error);
      return [];
    }
  }

  async getRegulatoryChangeData(organizationId?: string): Promise<any> {
    // This method will generate data for regulatory change charts
    try {
      // Use provided organizationId or get from auth service
      const orgId = organizationId || this.getOrganizationId();

      // Get all regulatory changes
      const pending = await this.findPendingRegulatoryChanges(orgId);
      const overdue = await this.findOverdueRegulatoryChanges(orgId);
      const upcoming = await this.findUpcomingRegulatoryChanges(orgId);

      // Group by impact level
      const byImpact = {
        high: [...pending, ...overdue, ...upcoming].filter(change =>
          change.impactAssessment && change.impactAssessment.level === 'high'
        ).length,
        medium: [...pending, ...overdue, ...upcoming].filter(change =>
          change.impactAssessment && change.impactAssessment.level === 'medium'
        ).length,
        low: [...pending, ...overdue, ...upcoming].filter(change =>
          change.impactAssessment && change.impactAssessment.level === 'low'
        ).length
      };

      // Group by status
      const byStatus = {
        pending: pending.length,
        overdue: overdue.length,
        upcoming: upcoming.length
      };

      // Group by category
      const categories = {};
      [...pending, ...overdue, ...upcoming].forEach(change => {
        if (!categories[change.category]) {
          categories[change.category] = 0;
        }
        categories[change.category]++;
      });

      return {
        byImpact,
        byStatus,
        byCategory: Object.entries(categories).map(([category, count]) => ({
          category,
          count
        }))
      };
    } catch (error) {
      console.error('Error getting regulatory change data:', error);
      return {
        byImpact: { high: 0, medium: 0, low: 0 },
        byStatus: { pending: 0, overdue: 0, upcoming: 0 },
        byCategory: []
      };
    }
  }
}

export default NovaPulseAPI;
