#!/bin/bash
# Script to perform security testing for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-test"}

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE --project $PROJECT_ID

# Get the service URL
echo "Getting service URL..."
SERVICE_IP=$(kubectl get service novafuse-uac -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$SERVICE_IP" ]; then
  echo "Service is not exposed externally. Creating a port-forward..."
  kubectl port-forward service/novafuse-uac -n $NAMESPACE 8080:80 &
  SERVICE_URL="http://localhost:8080"
  echo "Service available at $SERVICE_URL"
else
  SERVICE_URL="http://$SERVICE_IP"
  echo "Service available at $SERVICE_URL"
fi

# Test authentication
echo "Testing authentication..."
echo "Testing with no authentication..."
curl -s -o /dev/null -w "%{http_code}" $SERVICE_URL/api/v1/status
echo ""

echo "Testing with invalid API key..."
curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer invalid-key" $SERVICE_URL/api/v1/status
echo ""

echo "Testing with valid API key..."
curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status
echo ""

# Test CORS
echo "Testing CORS..."
curl -s -o /dev/null -w "%{http_code}" -H "Origin: http://example.com" -H "Access-Control-Request-Method: GET" -X OPTIONS $SERVICE_URL/api/v1/status
echo ""

# Test rate limiting
echo "Testing rate limiting..."
for i in {1..110}; do
  STATUS=$(curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer test-api-key-12345" $SERVICE_URL/api/v1/status)
  echo -n "$STATUS "
  if [ "$STATUS" == "429" ]; then
    echo "Rate limiting working!"
    break
  fi
  if [ $i -eq 110 ]; then
    echo "Rate limiting not working!"
  fi
done
echo ""

# Test CSRF protection
echo "Testing CSRF protection..."
curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer test-api-key-12345" -X POST $SERVICE_URL/api/v1/status
echo ""

# Test SQL injection
echo "Testing SQL injection..."
curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer test-api-key-12345" "$SERVICE_URL/api/v1/status?id=1%27%20OR%20%271%27=%271"
echo ""

# Test XSS
echo "Testing XSS..."
curl -s -o /dev/null -w "%{http_code}" -H "Authorization: Bearer test-api-key-12345" "$SERVICE_URL/api/v1/status?name=<script>alert(1)</script>"
echo ""

echo "Security testing complete!"
