/**
 * Offline Collaboration Context
 * 
 * This module provides a context for offline collaboration functionality in the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { usePerformance } from '../performance/usePerformance';
import { useOffline } from '../offline/OfflineContext';
import OfflineCollaborationService from './OfflineCollaborationService';

// Create offline collaboration context
const OfflineCollaborationContext = createContext();

/**
 * Offline Collaboration Provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.onlineCollaborationService] - Online collaboration service
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Offline Collaboration Provider component
 */
export const OfflineCollaborationProvider = ({
  children,
  onlineCollaborationService,
  enableLogging = false
}) => {
  const { measureOperation } = usePerformance('OfflineCollaborationProvider');
  const { isOnline, offlineMode } = useOffline();
  
  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState(null);
  const [user, setUser] = useState(null);
  const [currentRoom, setCurrentRoom] = useState(null);
  const [rooms, setRooms] = useState([]);
  const [messages, setMessages] = useState([]);
  const [users, setUsers] = useState([]);
  const [cursors, setCursors] = useState({});
  const [sharedState, setSharedState] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [pendingChanges, setPendingChanges] = useState([]);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [offlineService] = useState(() => new OfflineCollaborationService());
  
  // Determine which service to use
  const [service, setService] = useState(() => {
    if (!isOnline || offlineMode) {
      return offlineService;
    }
    return onlineCollaborationService || offlineService;
  });
  
  // Update service when online status changes
  useEffect(() => {
    if (!isOnline || offlineMode) {
      setService(offlineService);
    } else {
      setService(onlineCollaborationService || offlineService);
      
      // Sync pending changes when coming back online
      if (isConnected && pendingChanges.length > 0 && !isSyncing) {
        syncPendingChanges();
      }
    }
  }, [isOnline, offlineMode, onlineCollaborationService, offlineService, isConnected, pendingChanges, isSyncing]);
  
  // Log actions if enabled
  const log = useCallback((action, data) => {
    if (enableLogging) {
      console.log(`[OfflineCollaboration] ${action}`, data);
    }
  }, [enableLogging]);
  
  // Sync pending changes
  const syncPendingChanges = useCallback(async () => {
    if (!isConnected || isSyncing || pendingChanges.length === 0 || !isOnline || offlineMode) {
      return;
    }
    
    setIsSyncing(true);
    
    try {
      log('Syncing pending changes', pendingChanges);
      
      const result = await offlineService.syncPendingChanges();
      
      if (result.success) {
        setPendingChanges([]);
        setLastSyncTime(new Date());
        log('Sync completed', result);
      } else {
        log('Sync failed', result);
      }
    } catch (error) {
      log('Sync error', error);
    } finally {
      setIsSyncing(false);
    }
  }, [isConnected, isSyncing, pendingChanges, isOnline, offlineMode, offlineService, log]);
  
  // Add pending change
  const addPendingChange = useCallback((change) => {
    setPendingChanges(prev => [...prev, change]);
    
    // Try to sync immediately if online
    if (isOnline && !offlineMode && !isSyncing) {
      syncPendingChanges();
    }
  }, [isOnline, offlineMode, isSyncing, syncPendingChanges]);
  
  // Connect to collaboration service
  const connect = useCallback(async (options = {}) => {
    if (isConnected || isConnecting) return;
    
    setIsConnecting(true);
    setConnectionError(null);
    
    try {
      log('Connecting', options);
      
      const result = await measureOperation('connect', () => 
        service.connect(options)
      );
      
      setIsConnected(true);
      setUser(result.user);
      
      log('Connected', result);
      
      return result;
    } catch (error) {
      log('Connection error', error);
      setConnectionError(error.message || 'Failed to connect');
      throw error;
    } finally {
      setIsConnecting(false);
    }
  }, [isConnected, isConnecting, service, log, measureOperation]);
  
  // Disconnect from collaboration service
  const disconnect = useCallback(async () => {
    if (!isConnected) return;
    
    try {
      log('Disconnecting');
      
      await measureOperation('disconnect', () => 
        service.disconnect()
      );
      
      setIsConnected(false);
      setCurrentRoom(null);
      setMessages([]);
      setUsers([]);
      setCursors({});
      setSharedState({});
      
      log('Disconnected');
    } catch (error) {
      log('Disconnect error', error);
      throw error;
    }
  }, [isConnected, service, log, measureOperation]);
  
  // Create room
  const createRoom = useCallback(async (options = {}) => {
    if (!isConnected) {
      throw new Error('Not connected');
    }
    
    setIsLoading(true);
    
    try {
      log('Creating room', options);
      
      const room = await measureOperation('createRoom', () => 
        service.createRoom(options)
      );
      
      // Update rooms list
      setRooms(prevRooms => {
        const roomExists = prevRooms.some(r => r.id === room.id);
        return roomExists ? prevRooms : [...prevRooms, room];
      });
      
      log('Room created', room);
      
      return room;
    } catch (error) {
      log('Create room error', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, service, log, measureOperation]);
  
  // Join room
  const joinRoom = useCallback(async (roomId) => {
    if (!isConnected) {
      throw new Error('Not connected');
    }
    
    setIsLoading(true);
    
    try {
      log('Joining room', roomId);
      
      const room = await measureOperation('joinRoom', () => 
        service.joinRoom(roomId)
      );
      
      // Get messages
      const roomMessages = await measureOperation('getMessages', () => 
        service.getMessages(roomId)
      );
      
      // Get users
      const roomUsers = await measureOperation('getUsersInRoom', () => 
        service.getUsersInRoom(roomId)
      );
      
      // Get cursor positions
      const cursorPositions = await measureOperation('getCursorPositions', () => 
        service.getCursorPositions(roomId)
      );
      
      // Get shared state
      const roomSharedState = await measureOperation('getSharedState', () => 
        service.getSharedState(roomId)
      );
      
      // Update state
      setCurrentRoom(room);
      setMessages(roomMessages);
      setUsers(roomUsers);
      setCursors(cursorPositions);
      setSharedState(roomSharedState);
      
      log('Room joined', room);
      
      return room;
    } catch (error) {
      log('Join room error', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, service, log, measureOperation]);
  
  // Leave room
  const leaveRoom = useCallback(async () => {
    if (!isConnected || !currentRoom) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      log('Leaving room', currentRoom.id);
      
      await measureOperation('leaveRoom', () => 
        service.leaveRoom(currentRoom.id)
      );
      
      // Update state
      setCurrentRoom(null);
      setMessages([]);
      setUsers([]);
      setCursors({});
      setSharedState({});
      
      log('Room left', currentRoom.id);
    } catch (error) {
      log('Leave room error', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, currentRoom, service, log, measureOperation]);
  
  // Get rooms
  const getRooms = useCallback(async () => {
    if (!isConnected) {
      throw new Error('Not connected');
    }
    
    setIsLoading(true);
    
    try {
      log('Getting rooms');
      
      const roomsList = await measureOperation('getRooms', () => 
        service.getRooms()
      );
      
      setRooms(roomsList);
      
      log('Rooms retrieved', roomsList);
      
      return roomsList;
    } catch (error) {
      log('Get rooms error', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isConnected, service, log, measureOperation]);
  
  // Send message
  const sendMessage = useCallback(async (text) => {
    if (!isConnected || !currentRoom) {
      throw new Error('Not in a room');
    }
    
    try {
      log('Sending message', { roomId: currentRoom.id, text });
      
      const message = await measureOperation('sendMessage', () => 
        service.sendMessage(currentRoom.id, text)
      );
      
      log('Message sent', message);
      
      return message;
    } catch (error) {
      log('Send message error', error);
      throw error;
    }
  }, [isConnected, currentRoom, service, log, measureOperation]);
  
  // Update cursor position
  const updateCursorPosition = useCallback(async (position) => {
    if (!isConnected || !currentRoom) {
      return;
    }
    
    try {
      await measureOperation('updateCursorPosition', () => 
        service.updateCursorPosition(currentRoom.id, position)
      );
    } catch (error) {
      log('Update cursor position error', error);
    }
  }, [isConnected, currentRoom, service, log, measureOperation]);
  
  // Update shared state
  const updateSharedState = useCallback(async (state, merge = true) => {
    if (!isConnected || !currentRoom) {
      throw new Error('Not in a room');
    }
    
    try {
      log('Updating shared state', { roomId: currentRoom.id, state, merge });
      
      const updatedState = await measureOperation('updateSharedState', () => 
        service.updateSharedState(currentRoom.id, state, merge)
      );
      
      log('Shared state updated', updatedState);
      
      return updatedState;
    } catch (error) {
      log('Update shared state error', error);
      throw error;
    }
  }, [isConnected, currentRoom, service, log, measureOperation]);
  
  // Handle message event
  const handleMessage = useCallback((message) => {
    log('Message received', message);
    
    if (currentRoom && message.roomId === currentRoom.id) {
      setMessages(prevMessages => {
        // Check if message already exists
        const messageExists = prevMessages.some(m => m.id === message.id);
        return messageExists ? prevMessages : [...prevMessages, message];
      });
    }
  }, [currentRoom, log]);
  
  // Handle cursor move event
  const handleCursorMove = useCallback((data) => {
    if (currentRoom && data.roomId === currentRoom.id) {
      setCursors(prevCursors => ({
        ...prevCursors,
        [data.userId]: {
          userId: data.userId,
          position: data.position
        }
      }));
    }
  }, [currentRoom]);
  
  // Handle state change event
  const handleStateChange = useCallback((data) => {
    log('State change', data);
    
    if (currentRoom && data.roomId === currentRoom.id) {
      setSharedState(data.state);
    }
  }, [currentRoom, log]);
  
  // Handle user join event
  const handleUserJoin = useCallback((data) => {
    log('User joined', data);
    
    if (currentRoom && data.roomId === currentRoom.id) {
      setUsers(prevUsers => {
        // Check if user already exists
        const userExists = prevUsers.some(u => u.id === data.user.id);
        return userExists ? prevUsers : [...prevUsers, data.user];
      });
    }
  }, [currentRoom, log]);
  
  // Handle user leave event
  const handleUserLeave = useCallback((data) => {
    log('User left', data);
    
    if (currentRoom && data.roomId === currentRoom.id) {
      setUsers(prevUsers => prevUsers.filter(u => u.id !== data.userId));
      setCursors(prevCursors => {
        const newCursors = { ...prevCursors };
        delete newCursors[data.userId];
        return newCursors;
      });
    }
  }, [currentRoom, log]);
  
  // Add event listeners
  useEffect(() => {
    service.addEventListener('message', handleMessage);
    service.addEventListener('cursorMove', handleCursorMove);
    service.addEventListener('stateChange', handleStateChange);
    service.addEventListener('userJoin', handleUserJoin);
    service.addEventListener('userLeave', handleUserLeave);
    
    return () => {
      service.removeEventListener('message', handleMessage);
      service.removeEventListener('cursorMove', handleCursorMove);
      service.removeEventListener('stateChange', handleStateChange);
      service.removeEventListener('userJoin', handleUserJoin);
      service.removeEventListener('userLeave', handleUserLeave);
    };
  }, [
    service,
    handleMessage,
    handleCursorMove,
    handleStateChange,
    handleUserJoin,
    handleUserLeave
  ]);
  
  // Load rooms when connected
  useEffect(() => {
    if (isConnected) {
      getRooms().catch(error => {
        log('Load rooms error', error);
      });
    }
  }, [isConnected, getRooms, log]);
  
  // Create context value
  const contextValue = {
    isConnected,
    isConnecting,
    connectionError,
    user,
    currentRoom,
    rooms,
    messages,
    users,
    cursors,
    sharedState,
    isLoading,
    pendingChanges,
    isSyncing,
    lastSyncTime,
    isOffline: !isOnline || offlineMode,
    connect,
    disconnect,
    createRoom,
    joinRoom,
    leaveRoom,
    getRooms,
    sendMessage,
    updateCursorPosition,
    updateSharedState,
    syncPendingChanges
  };
  
  return (
    <OfflineCollaborationContext.Provider value={contextValue}>
      {children}
    </OfflineCollaborationContext.Provider>
  );
};

OfflineCollaborationProvider.propTypes = {
  children: PropTypes.node.isRequired,
  onlineCollaborationService: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Use offline collaboration hook
 * 
 * @returns {Object} Offline collaboration context value
 */
export const useOfflineCollaboration = () => {
  const context = useContext(OfflineCollaborationContext);
  
  if (!context) {
    throw new Error('useOfflineCollaboration must be used within an OfflineCollaborationProvider');
  }
  
  return context;
};

export default OfflineCollaborationContext;
