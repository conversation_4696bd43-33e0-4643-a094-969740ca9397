/**
 * CSDE Monitoring Module
 * 
 * This module provides performance monitoring for the CSDE engine.
 */

const CSEDPerformanceMonitoringService = require('./performance-monitoring-service');
const createPerformanceMonitoringMiddleware = require('./performance-monitoring-middleware');
const createPerformanceMonitoringRoutes = require('./performance-monitoring-routes');

module.exports = {
  CSEDPerformanceMonitoringService,
  createPerformanceMonitoringMiddleware,
  createPerformanceMonitoringRoutes
};
