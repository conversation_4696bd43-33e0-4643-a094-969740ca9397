/**
 * Simple RBAC Test Script
 *
 * This script tests the RBAC system with a focus on the core functionality.
 */

const mongoose = require('mongoose');
const Role = require('../api/models/Role');
const Permission = require('../api/models/Permission');
const UserRole = require('../api/models/UserRole');
const User = require('../api/models/User');

// Rename the test cache service to replace the original
const fs = require('fs');
const path = require('path');

// Backup the original CacheService.js if it exists
const cacheServicePath = path.join(__dirname, '../api/services/CacheService.js');
const cacheServiceBackupPath = path.join(__dirname, '../api/services/CacheService.js.bak');
const cacheServiceTestPath = path.join(__dirname, '../api/services/CacheService.test.js');

if (fs.existsSync(cacheServicePath)) {
  fs.copyFileSync(cacheServicePath, cacheServiceBackupPath);
}

// Replace with test version
fs.copyFileSync(cacheServiceTestPath, cacheServicePath);

// Now import RBACService
const RBACService = require('../api/services/RBACService');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/novaconnect-test';

// Connect to MongoDB
async function setupDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing data to avoid duplicate key errors
    await Permission.deleteMany({});
    await Role.deleteMany({});
    await UserRole.deleteMany({});
    await User.deleteMany({ email: '<EMAIL>' });

    console.log('Cleared existing test data');

    return true;
  } catch (err) {
    console.error('Failed to connect to MongoDB', err);
    return false;
  }
}

// Start tests
setupDatabase()
  .then(success => {
    if (success) {
      runTests();
    } else {
      process.exit(1);
    }
  });

// Run tests
async function runTests() {
  try {
    console.log('Starting RBAC tests...');

    // Create RBAC service
    const rbacService = new RBACService();

    // Test 1: Create permissions
    console.log('\nTest 1: Creating permissions...');

    const viewPermission = await rbacService.createPermission({
      name: 'View Test Resource',
      description: 'Permission to view test resources',
      resource: 'test-resource',
      action: 'view'
    });

    const editPermission = await rbacService.createPermission({
      name: 'Edit Test Resource',
      description: 'Permission to edit test resources',
      resource: 'test-resource',
      action: 'edit'
    });

    const deletePermission = await rbacService.createPermission({
      name: 'Delete Test Resource',
      description: 'Permission to delete test resources',
      resource: 'test-resource',
      action: 'delete'
    });

    console.log(`Created permissions:
      - ${viewPermission.resource}:${viewPermission.action}
      - ${editPermission.resource}:${editPermission.action}
      - ${deletePermission.resource}:${deletePermission.action}`);

    // Test 2: Create roles
    console.log('\nTest 2: Creating roles...');

    const adminRole = await rbacService.createRole({
      name: 'Test Admin',
      description: 'Administrator role for testing',
      permissions: ['*']
    });

    const editorRole = await rbacService.createRole({
      name: 'Test Editor',
      description: 'Editor role for testing',
      permissions: [viewPermission._id, editPermission._id]
    });

    const viewerRole = await rbacService.createRole({
      name: 'Test Viewer',
      description: 'Viewer role for testing',
      permissions: [viewPermission._id]
    });

    console.log(`Created roles:
      - ${adminRole.name}
      - ${editorRole.name}
      - ${viewerRole.name}`);

    // Test 3: Create users
    console.log('\nTest 3: Creating users...');

    const adminUser = new User({
      username: 'admin-test',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Admin',
      lastName: 'Test',
      status: 'active'
    });

    const editorUser = new User({
      username: 'editor-test',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Editor',
      lastName: 'Test',
      status: 'active'
    });

    const viewerUser = new User({
      username: 'viewer-test',
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Viewer',
      lastName: 'Test',
      status: 'active'
    });

    await adminUser.save();
    await editorUser.save();
    await viewerUser.save();

    console.log('Created test users');

    // Test 4: Assign roles to users
    console.log('\nTest 4: Assigning roles to users...');

    await rbacService.assignRoleToUser(adminUser._id, adminRole._id);
    await rbacService.assignRoleToUser(editorUser._id, editorRole._id);
    await rbacService.assignRoleToUser(viewerUser._id, viewerRole._id);

    console.log('Assigned roles to users');

    // Test 5: Check permissions
    console.log('\nTest 5: Checking permissions...');

    const adminViewPermission = await rbacService.hasPermission(adminUser._id, 'test-resource:view');
    const adminEditPermission = await rbacService.hasPermission(adminUser._id, 'test-resource:edit');
    const adminDeletePermission = await rbacService.hasPermission(adminUser._id, 'test-resource:delete');

    const editorViewPermission = await rbacService.hasPermission(editorUser._id, 'test-resource:view');
    const editorEditPermission = await rbacService.hasPermission(editorUser._id, 'test-resource:edit');
    const editorDeletePermission = await rbacService.hasPermission(editorUser._id, 'test-resource:delete');

    const viewerViewPermission = await rbacService.hasPermission(viewerUser._id, 'test-resource:view');
    const viewerEditPermission = await rbacService.hasPermission(viewerUser._id, 'test-resource:edit');
    const viewerDeletePermission = await rbacService.hasPermission(viewerUser._id, 'test-resource:delete');

    console.log(`Admin permissions:
      - test-resource:view: ${adminViewPermission}
      - test-resource:edit: ${adminEditPermission}
      - test-resource:delete: ${adminDeletePermission}`);

    console.log(`Editor permissions:
      - test-resource:view: ${editorViewPermission}
      - test-resource:edit: ${editorEditPermission}
      - test-resource:delete: ${editorDeletePermission}`);

    console.log(`Viewer permissions:
      - test-resource:view: ${viewerViewPermission}
      - test-resource:edit: ${viewerEditPermission}
      - test-resource:delete: ${viewerDeletePermission}`);

    // Test 6: Get user permissions
    console.log('\nTest 6: Getting user permissions...');

    const adminPermissions = await rbacService.getUserPermissions(adminUser._id);
    const editorPermissions = await rbacService.getUserPermissions(editorUser._id);
    const viewerPermissions = await rbacService.getUserPermissions(viewerUser._id);

    console.log(`Admin has ${adminPermissions.length} permissions`);
    console.log(`Editor has ${editorPermissions.length} permissions`);
    console.log(`Viewer has ${viewerPermissions.length} permissions`);

    // Test 7: Remove role from user
    console.log('\nTest 7: Removing role from user...');

    await rbacService.removeRoleFromUser(viewerUser._id, viewerRole._id);

    const viewerPermissionsAfterRemoval = await rbacService.getUserPermissions(viewerUser._id);
    console.log(`Viewer has ${viewerPermissionsAfterRemoval.length} permissions after role removal`);

    // Test 8: Delete role
    console.log('\nTest 8: Deleting role...');

    await rbacService.deleteRole(editorRole._id);

    const roles = await rbacService.getAllRoles();
    console.log(`Remaining roles: ${roles.length}`);

    console.log('\nRBAC tests completed successfully');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');

    // Restore original CacheService
    if (fs.existsSync(cacheServiceBackupPath)) {
      fs.copyFileSync(cacheServiceBackupPath, cacheServicePath);
      fs.unlinkSync(cacheServiceBackupPath);
      console.log('Restored original CacheService');
    }

    process.exit(0);
  }
}
