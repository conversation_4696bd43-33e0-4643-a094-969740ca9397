# Wait for <PERSON> to be ready
Write-Host "Waiting for <PERSON> to be ready..."
$ready = $false
while (-not $ready) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8001" -UseBasicParsing -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            $ready = $true
        }
    } catch {
        Start-Sleep -Seconds 5
    }
}
Write-Host "Kong is ready!"

# Create services in Kong
Write-Host "Creating services in Kong..."
Invoke-RestMethod -Uri "http://localhost:8001/services" -Method Post -Body @{
    name = "governance-service"
    url = "http://novafuse-governance-api:3000"
}

Invoke-RestMethod -Uri "http://localhost:8001/services" -Method Post -Body @{
    name = "security-service"
    url = "http://novafuse-security-api:3000"
}

Invoke-RestMethod -Uri "http://localhost:8001/services" -Method Post -Body @{
    name = "apis-service"
    url = "http://novafuse-apis-api:3000"
}

# Create routes for the services
Write-Host "Creating routes for the services..."

# Governance route
$governanceRouteBody = @{
    name = "governance-route"
}
$governanceRouteBody.Add("paths[]", "/governance")
$governanceRouteBody.Add("paths[]", "/governance/*")
Invoke-RestMethod -Uri "http://localhost:8001/services/governance-service/routes" -Method Post -Body $governanceRouteBody

# Security route
$securityRouteBody = @{
    name = "security-route"
}
$securityRouteBody.Add("paths[]", "/security")
$securityRouteBody.Add("paths[]", "/security/*")
Invoke-RestMethod -Uri "http://localhost:8001/services/security-service/routes" -Method Post -Body $securityRouteBody

# APIs route
$apisRouteBody = @{
    name = "apis-route"
}
$apisRouteBody.Add("paths[]", "/apis")
$apisRouteBody.Add("paths[]", "/apis/*")
Invoke-RestMethod -Uri "http://localhost:8001/services/apis-service/routes" -Method Post -Body $apisRouteBody

# Add key-auth plugin to all services
Write-Host "Adding key-auth plugin..."
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "key-auth"
}

# Create a consumer
Write-Host "Creating consumer..."
Invoke-RestMethod -Uri "http://localhost:8001/consumers" -Method Post -Body @{
    username = "partner-app"
}

# Provision key credentials for the consumer
Write-Host "Provisioning API key for consumer..."
Invoke-RestMethod -Uri "http://localhost:8001/consumers/partner-app/key-auth" -Method Post -Body @{
    key = "test-api-key"
}

# Add tiered rate limiting plugins
Write-Host "Adding tiered rate limiting plugins..."

# Create consumer groups for different tiers
Write-Host "Creating consumer groups for subscription tiers..."
Invoke-RestMethod -Uri "http://localhost:8001/consumer_groups" -Method Post -Body @{
    name = "free-tier"
}

Invoke-RestMethod -Uri "http://localhost:8001/consumer_groups" -Method Post -Body @{
    name = "basic-tier"
}

Invoke-RestMethod -Uri "http://localhost:8001/consumer_groups" -Method Post -Body @{
    name = "professional-tier"
}

Invoke-RestMethod -Uri "http://localhost:8001/consumer_groups" -Method Post -Body @{
    name = "enterprise-tier"
}

# Free tier - 10 requests per minute
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "rate-limiting"
    "config.minute" = 10
    consumer_group = "free-tier"
}

# Basic tier - 100 requests per minute
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "rate-limiting"
    "config.minute" = 100
    consumer_group = "basic-tier"
}

# Professional tier - 1000 requests per minute
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "rate-limiting"
    "config.minute" = 1000
    consumer_group = "professional-tier"
}

# Enterprise tier - 10000 requests per minute
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "rate-limiting"
    "config.minute" = 10000
    consumer_group = "enterprise-tier"
}

# Default rate limiting for unauthenticated users - 5 requests per minute
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "rate-limiting"
    "config.minute" = 5
}

# Add NovaFuse usage tracking plugin
Write-Host "Adding NovaFuse usage tracking plugin..."
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "novafuse-usage-tracking"
    "config.subscription_tier" = "standard"
}

# Add JWT authentication plugin
Write-Host "Adding JWT authentication plugin..."
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "jwt"
}

# Add IP restriction plugin for enterprise tier
Write-Host "Adding IP restriction plugin for enterprise tier..."
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "ip-restriction"
    consumer_group = "enterprise-tier"
    "config.whitelist" = @("0.0.0.0/0")  # Allow all IPs by default, to be configured per customer
}

# Add request transformer plugin for header normalization
Write-Host "Adding request transformer plugin..."
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "request-transformer"
    "config.add.headers" = @("X-NovaFuse-Version:1.0")
}

# Add response transformer plugin for consistent responses
Write-Host "Adding response transformer plugin..."
Invoke-RestMethod -Uri "http://localhost:8001/plugins" -Method Post -Body @{
    name = "response-transformer"
    "config.add.headers" = @("X-NovaFuse-Powered-By:NovaFuse API Superstore")
}

Write-Host "Kong API Gateway setup complete!"
Write-Host "You can now test the API with:"
Write-Host "Invoke-RestMethod -Uri 'http://localhost:8000/governance/board/meetings' -Headers @{apikey = 'test-api-key'}"
