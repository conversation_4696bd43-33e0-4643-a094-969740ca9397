import React, { useState } from 'react';
import BasicDemoTemplate from '../../components/demo-framework/BasicDemoTemplate';
import EcosystemSlotSelector from '../../components/demo-framework/EcosystemSlotSelector';
import PageWithSidebar from '../../components/PageWithSidebar';
import ecosystemSlots from '../../data/ecosystem-slots';

export default function PartnerNetworkDemo() {
  // State for selected collaborative roles
  const [selectedSlots, setSelectedSlots] = useState([]);

  // Get selected role data
  const selectedSlotData = ecosystemSlots.filter(slot => selectedSlots.includes(slot.id));

  // SEO metadata
  const pageProps = {
    title: 'NovaFuse Partner Network Demo - NovaFuse',
    description: 'Explore the NovaFuse Partner Network that organizes the GRC landscape into a structured, scalable platform for partners and developers.',
    keywords: 'NovaFuse Partner Network, GRC ecosystem, compliance categories, partner ecosystem, collaborative roles',
    canonical: 'https://novafuse.io/component-demos/partner-network-demo',
    ogImage: '/images/demos/partner-network-demo-og.png'
  };

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Partner Network', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Role Categories', href: '#categories' },
      { label: 'Partner Allocation', href: '#allocation' },
      { label: 'Use Cases', href: '#use-cases' }
    ]},
    { type: 'category', label: 'Related Demos', items: [
      { label: 'Partner Empowerment Model', href: '/component-demos/partner-empowerment-demo' },
      { label: 'Universal API Connector', href: '/component-demos/uac-demo' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Partner Network Handbook', href: '/resources/partner-network-handbook.pdf' },
      { label: 'Partner Program', href: '/partner-program' },
      { label: 'Schedule Demo', href: '/contact?demo=partner-network' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <BasicDemoTemplate
        title="NovaFuse Partner Network"
        description="Discover NovaFuse's structured approach to organizing the GRC landscape"
        demoType="executive"
      >
        {/* Custom content for the 144-Slot Ecosystem Framework demo */}
        <div className="space-y-8">
          {/* Overview Section */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">The NovaFuse Partner Network</h2>
            <p className="text-gray-300 mb-6">
              The NovaFuse Partner Network is a revolutionary approach to organizing the GRC landscape. It provides a structured, scalable platform for partners and developers to build upon, ensuring comprehensive coverage of all compliance domains while preventing overlap and redundancy.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-xl font-semibold mb-3">Network Structure</h3>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span><strong>100 Strategic Alliance Leaders:</strong> For implementation and consulting partners (Platinum/Gold/Silver)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span><strong>40 Daring Developers:</strong> For specialized app developers across compliance domains</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span><strong>3 Cross-Cloud Guardians:</strong> For major cloud providers (AWS, Google Cloud, Azure)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span><strong>1 Innovation Catalyst:</strong> For partners who excel at both services and product development</span>
                  </li>
                </ul>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-xl font-semibold mb-3">Strategic Benefits</h3>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    <span><strong>Structured Growth:</strong> Organized approach to ecosystem expansion</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    <span><strong>Comprehensive Coverage:</strong> Ensures all compliance domains are addressed</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    <span><strong>Prevents Overlap:</strong> Clear boundaries between partner responsibilities</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    <span><strong>Scalable Platform:</strong> Flexible framework that can evolve with the market</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">✓</span>
                    <span><strong>Strategic Allocation:</strong> Focuses resources on high-impact areas</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Collaborative Role Explorer Section */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">Partner Network Explorer</h2>
            <p className="text-gray-300 mb-6">
              Explore the NovaFuse Partner Network and discover how we organize the GRC landscape. Select up to 3 collaborative roles to see how they could apply to your organization.
            </p>

            <EcosystemSlotSelector
              slots={ecosystemSlots}
              selectedSlots={selectedSlots}
              onSlotSelect={setSelectedSlots}
              maxSelections={3}
              showCategories={true}
            />
          </div>

          {/* Selected Collaborative Roles Section */}
          {selectedSlots.length > 0 && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-4">Your Selected Collaborative Roles</h2>
              <p className="text-gray-300 mb-6">
                Here's how these selected collaborative roles could apply to your organization:
              </p>

              <div className="space-y-4">
                {selectedSlotData.map(slot => (
                  <div key={slot.id} className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-4">
                    <div className="flex items-center mb-2">
                      <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                        {slot.id}
                      </span>
                      <h3 className="text-xl font-semibold">{slot.name}</h3>
                    </div>
                    <p className="text-gray-300 mb-3">{slot.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-800 bg-opacity-50 p-3 rounded-lg">
                        <h4 className="font-medium mb-2">Potential Applications</h4>
                        <ul className="space-y-1 text-gray-300 text-sm">
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            <span>Streamline {slot.name.toLowerCase()} processes</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            <span>Automate compliance checks for {slot.category.toLowerCase()} requirements</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-2">•</span>
                            <span>Integrate with existing {slot.tags[0].toLowerCase()} systems</span>
                          </li>
                        </ul>
                      </div>

                      <div className="bg-gray-800 bg-opacity-50 p-3 rounded-lg">
                        <h4 className="font-medium mb-2">NovaFuse Solutions</h4>
                        <ul className="space-y-1 text-gray-300 text-sm">
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-2">✓</span>
                            <span><strong>APIs:</strong> {slot.name} API endpoints</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-2">✓</span>
                            <span><strong>UI Components:</strong> {slot.name} dashboards and reports</span>
                          </li>
                          <li className="flex items-start">
                            <span className="text-blue-500 mr-2">✓</span>
                            <span><strong>App Store:</strong> {slot.tags[0]} compliance apps</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 text-center">
                <button
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold"
                  onClick={() => {
                    // In a real implementation, this would generate a custom report
                    alert('In a live demo, this would generate a custom report for your selected slots.');
                  }}
                >
                  Generate Custom Solution Report
                </button>
              </div>
            </div>
          )}

          {/* Dynamic Rebalancing Section */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">Dynamic Network Evolution</h2>
            <p className="text-gray-300 mb-6">
              The NovaFuse Partner Network is not static—it evolves with the market. Our quarterly network assessment process uses AI to analyze compliance trends and dynamically reallocate collaborative roles to high-demand domains.
            </p>

            <div className="bg-gray-700 p-4 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">How It Works</h3>
              <ol className="space-y-3 text-gray-300">
                <li className="flex items-start">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">1</span>
                  <span><strong>Trend Analysis:</strong> AI analyzes compliance trends, regulatory changes, and market demand</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">2</span>
                  <span><strong>Demand Identification:</strong> High-demand domains are identified based on customer needs and regulatory heat</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">3</span>
                  <span><strong>Role Reallocation:</strong> Collaborative roles are reallocated from lower-demand areas to high-demand domains</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">4</span>
                  <span><strong>Partner Notification:</strong> Partners are notified of changes and opportunities in new domains</span>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">5</span>
                  <span><strong>Transparency:</strong> Public dashboard shows network changes, reinforcing NovaFuse as a living ecosystem</span>
                </li>
              </ol>
            </div>
          </div>
        </div>
      </BasicDemoTemplate>
    </PageWithSidebar>
  );
}
