#!/bin/bash
# Build and run CSDE tests in Docker

# Build the Docker image
echo "Building CSDE Docker image..."
docker build -t novafuse/csde-testing -f csde/Dockerfile .

# Run the container with GPU support
echo "Running CSDE tests with GPU support..."
docker run --gpus all -v $(pwd)/results:/app/results novafuse/csde-testing --use-gpu --output-dir results

# Display results
echo "Test results:"
cat results/summary_results.txt
