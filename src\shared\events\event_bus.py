"""
Event bus for NovaFuse components.

This module provides a simple event bus for communication between components.
"""

import logging
from typing import Dict, List, Any, Callable, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EventBus:
    """
    Simple event bus for communication between components.

    This class implements a singleton pattern to ensure that there is only one
    event bus instance in the application.
    """

    _instance = None

    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            logger.info("Creating EventBus instance")
            cls._instance = super(EventBus, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the EventBus."""
        if self._initialized:
            return

        logger.info("Initializing EventBus")

        # Initialize the event handlers dictionary
        # Structure: {event_type: [handler1, handler2, ...]}
        self.event_handlers: Dict[str, List[Callable]] = {}

        self._initialized = True

        logger.info("EventBus initialized")

    def register_handler(self, event_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific event type.

        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []

        self.event_handlers[event_type].append(handler)
        logger.info(f"Registered handler for event type: {event_type}")

    def unregister_handler(self, event_type: str, handler: Callable) -> None:
        """
        Unregister a handler for a specific event type.

        Args:
            event_type: The type of event
            handler: The handler function
        """
        if event_type in self.event_handlers and handler in self.event_handlers[event_type]:
            self.event_handlers[event_type].remove(handler)
            logger.info(f"Unregistered handler for event type: {event_type}")

    def emit(self, event_type: str, event_data: Optional[Dict[str, Any]] = None) -> None:
        """
        Emit an event.

        Args:
            event_type: The type of event
            event_data: Data associated with the event
        """
        logger.info(f"Emitting event: {event_type}")

        if event_data is None:
            event_data = {}

        # Add event type to the event data
        event_data['event_type'] = event_type

        # Add timestamp to the event data
        import datetime
        event_data['timestamp'] = datetime.datetime.now().isoformat()

        # Call all registered handlers for this event type
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(event_data)
                except Exception as e:
                    logger.error(f"Error in event handler for {event_type}: {e}")

    def get_registered_event_types(self) -> List[str]:
        """
        Get all registered event types.

        Returns:
            List of event types
        """
        return list(self.event_handlers.keys())


# Create a global instance of the EventBus
event_bus = EventBus()