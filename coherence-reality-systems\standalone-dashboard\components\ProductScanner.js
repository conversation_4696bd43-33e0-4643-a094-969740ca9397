import React from 'react';
import { motion } from 'framer-motion';
import styles from '../styles/Home.module.css';

const ProductScanner = ({ onScan, scanning, products }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          Product Scanner
        </h2>
        <button
          onClick={onScan}
          disabled={scanning}
          className={`px-4 py-2 rounded-lg font-semibold ${
            scanning
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
        >
          {scanning ? 'Scanning...' : 'Scan Products'}
        </button>
      </div>

      <div className="overflow-x-auto">
        {scanning ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : products && products.length > 0 ? (
          <>
            <table className="min-w-full">
              <thead>
                <tr className="bg-gray-100">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ψ Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    φ Resonance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    κ Boost
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Commission
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {products.map((product, index) => (
                  <tr
                    key={index}
                    className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.name || 'Unknown Product'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.psi || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.phi || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {product.kappa || 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      ${product.commission || 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">No products found. Click "Scan Products" to start scanning.</p>
          </div>
        )}
              >
      </div>
    </div>
  );
};

export default ProductScanner;
