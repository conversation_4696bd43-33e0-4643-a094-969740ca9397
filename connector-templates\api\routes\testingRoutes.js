/**
 * Testing Routes
 */

const express = require('express');
const router = express.Router();
const TestingController = require('../controllers/TestingController');

// Execute connector endpoint
router.post('/execute', (req, res, next) => {
  TestingController.executeEndpoint(req, res, next);
});

// Get request history
router.get('/history/:connectorId/:endpointId', (req, res, next) => {
  TestingController.getRequestHistory(req, res, next);
});

// Validate response against rules
router.post('/validate', (req, res, next) => {
  TestingController.validateResponse(req, res, next);
});

// Simulate error scenarios
router.post('/simulate-error', (req, res, next) => {
  TestingController.simulateErrorScenario(req, res, next);
});

module.exports = router;
