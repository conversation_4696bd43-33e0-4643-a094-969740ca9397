/**
 * NovaFuse Component Mocks
 * 
 * This module provides mock implementations of NovaFuse components for testing.
 */

const EventEmitter = require('events');

/**
 * NovaShield Mock
 */
class NovaShieldMock extends EventEmitter {
  constructor() {
    super();
    this.threats = [];
    this.metrics = {
      averageEntropy: 0.5,
      averagePhase: Math.PI,
      averageCertainty: 0.7
    };
  }
  
  async getThreatData() {
    return {
      threats: this.threats,
      ...this.metrics
    };
  }
  
  generateRandomThreat() {
    const threatTypes = ['Malware', 'Phishing', 'DDoS', 'Intrusion', 'Data Exfiltration'];
    const threat = {
      id: `threat-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
      entropy: Math.random(),
      phase: Math.random() * Math.PI * 2,
      certainty: Math.random(),
      direction: Math.random() * Math.PI * 2,
      magnitude: Math.random(),
      timestamp: new Date()
    };
    
    this.threats.push(threat);
    
    // Limit to 20 threats
    if (this.threats.length > 20) {
      this.threats.shift();
    }
    
    // Update metrics
    this.metrics.averageEntropy = this.threats.reduce((sum, t) => sum + t.entropy, 0) / this.threats.length;
    this.metrics.averagePhase = this.threats.reduce((sum, t) => sum + t.phase, 0) / this.threats.length;
    this.metrics.averageCertainty = this.threats.reduce((sum, t) => sum + t.certainty, 0) / this.threats.length;
    
    // Emit event
    this.emit('threatDetected', threat);
    this.emit('threatAnalyzed', {
      threats: this.threats,
      ...this.metrics
    });
    
    return threat;
  }
  
  generateHighLoadThreatBatch(count = 100) {
    const startTime = Date.now();
    const threats = [];
    
    for (let i = 0; i < count; i++) {
      threats.push(this.generateRandomThreat());
    }
    
    const endTime = Date.now();
    
    return {
      threats,
      duration: endTime - startTime,
      throughput: count / ((endTime - startTime) / 1000)
    };
  }
}

/**
 * NovaTrack Mock
 */
class NovaTrackMock extends EventEmitter {
  constructor() {
    super();
    this.regulations = [];
    this.metrics = {
      averageComplexity: 0.5,
      averageAdaptability: 0.5,
      averageResonance: 0.7
    };
  }
  
  async getComplianceData() {
    return {
      regulations: this.regulations,
      ...this.metrics
    };
  }
  
  generateRandomRegulation() {
    const regulationTypes = ['GDPR', 'HIPAA', 'PCI-DSS', 'SOX', 'NIST CSF'];
    const regulation = {
      id: `regulation-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      name: regulationTypes[Math.floor(Math.random() * regulationTypes.length)],
      complexity: Math.random(),
      adaptability: Math.random(),
      resonance: Math.random(),
      environmentalPressure: Math.random(),
      timestamp: new Date()
    };
    
    this.regulations.push(regulation);
    
    // Limit to 20 regulations
    if (this.regulations.length > 20) {
      this.regulations.shift();
    }
    
    // Update metrics
    this.metrics.averageComplexity = this.regulations.reduce((sum, r) => sum + r.complexity, 0) / this.regulations.length;
    this.metrics.averageAdaptability = this.regulations.reduce((sum, r) => sum + r.adaptability, 0) / this.regulations.length;
    this.metrics.averageResonance = this.regulations.reduce((sum, r) => sum + r.resonance, 0) / this.regulations.length;
    
    // Emit event
    this.emit('complianceChanged', regulation);
    this.emit('regulationUpdated', {
      regulations: this.regulations,
      ...this.metrics
    });
    
    return regulation;
  }
  
  generateHighLoadRegulationBatch(count = 100) {
    const startTime = Date.now();
    const regulations = [];
    
    for (let i = 0; i < count; i++) {
      regulations.push(this.generateRandomRegulation());
    }
    
    const endTime = Date.now();
    
    return {
      regulations,
      duration: endTime - startTime,
      throughput: count / ((endTime - startTime) / 1000)
    };
  }
}

/**
 * NovaCore Mock
 */
class NovaCoreMock extends EventEmitter {
  constructor() {
    super();
    this.decisions = [];
    this.metrics = {
      averageFairness: 0.5,
      averageTransparency: 0.5,
      averageEthicalTensor: 0.7
    };
  }
  
  async getDecisionData() {
    return {
      decisions: this.decisions,
      ...this.metrics
    };
  }
  
  generateRandomDecision() {
    const decisionTypes = ['Block', 'Allow', 'Quarantine', 'Alert', 'Escalate'];
    const decision = {
      id: `decision-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: decisionTypes[Math.floor(Math.random() * decisionTypes.length)],
      fairness: Math.random(),
      transparency: Math.random(),
      ethicalTensor: Math.random(),
      accountability: Math.random(),
      timestamp: new Date()
    };
    
    this.decisions.push(decision);
    
    // Limit to 20 decisions
    if (this.decisions.length > 20) {
      this.decisions.shift();
    }
    
    // Update metrics
    this.metrics.averageFairness = this.decisions.reduce((sum, d) => sum + d.fairness, 0) / this.decisions.length;
    this.metrics.averageTransparency = this.decisions.reduce((sum, d) => sum + d.transparency, 0) / this.decisions.length;
    this.metrics.averageEthicalTensor = this.decisions.reduce((sum, d) => sum + d.ethicalTensor, 0) / this.decisions.length;
    
    // Emit event
    this.emit('decisionMade', decision);
    this.emit('policyApplied', {
      decisions: this.decisions,
      ...this.metrics
    });
    
    return decision;
  }
  
  generateHighLoadDecisionBatch(count = 100) {
    const startTime = Date.now();
    const decisions = [];
    
    for (let i = 0; i < count; i++) {
      decisions.push(this.generateRandomDecision());
    }
    
    const endTime = Date.now();
    
    return {
      decisions,
      duration: endTime - startTime,
      throughput: count / ((endTime - startTime) / 1000)
    };
  }
}

module.exports = {
  NovaShieldMock,
  NovaTrackMock,
  NovaCoreMock
};
