/**
 * OnboardingSpotlight Component
 * 
 * A component for highlighting a specific element on the page during onboarding.
 */

import React, { useEffect, useState, useRef } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';

/**
 * OnboardingSpotlight component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.targetSelector] - CSS selector for the target element
 * @param {Object} [props.targetElement] - Target element reference
 * @param {number} [props.padding=10] - Padding around the target element
 * @param {string} [props.color='rgba(25, 118, 210, 0.8)'] - Spotlight color
 * @param {boolean} [props.pulse=true] - Whether to add a pulse animation
 * @param {boolean} [props.visible=true] - Whether the spotlight is visible
 * @param {Function} [props.onClick] - Function to call when the spotlight is clicked
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} OnboardingSpotlight component
 */
const OnboardingSpotlight = ({
  targetSelector,
  targetElement,
  padding = 10,
  color = 'rgba(25, 118, 210, 0.8)',
  pulse = true,
  visible = true,
  onClick,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  
  // Refs
  const spotlightRef = useRef(null);
  const targetRef = useRef(null);
  
  // State
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0, height: 0 });
  const [isVisible, setIsVisible] = useState(false);
  
  // Find target element
  useEffect(() => {
    if (!visible) {
      setIsVisible(false);
      return;
    }
    
    if (targetElement) {
      targetRef.current = targetElement;
    } else if (targetSelector) {
      targetRef.current = document.querySelector(targetSelector);
    }
    
    updatePosition();
    
    // Show spotlight after a short delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    
    return () => {
      clearTimeout(timer);
    };
  }, [targetElement, targetSelector, visible]);
  
  // Update position on window resize
  useEffect(() => {
    const handleResize = () => {
      updatePosition();
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize, true);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize, true);
    };
  }, []);
  
  // Update spotlight position
  const updatePosition = () => {
    if (!targetRef.current) return;
    
    const targetRect = targetRef.current.getBoundingClientRect();
    
    setPosition({
      top: targetRect.top - padding,
      left: targetRect.left - padding,
      width: targetRect.width + (padding * 2),
      height: targetRect.height + (padding * 2)
    });
  };
  
  // Handle click
  const handleClick = (e) => {
    if (onClick) {
      onClick(e);
    }
  };
  
  // If not visible, don't render anything
  if (!isVisible) {
    return null;
  }
  
  return (
    <div
      ref={spotlightRef}
      className={`onboarding-spotlight ${pulse ? 'onboarding-spotlight--pulse' : ''} ${className}`}
      style={{
        ...style,
        top: `${position.top}px`,
        left: `${position.left}px`,
        width: `${position.width}px`,
        height: `${position.height}px`,
        borderColor: color
      }}
      onClick={handleClick}
      role="presentation"
    />
  );
};

OnboardingSpotlight.propTypes = {
  targetSelector: PropTypes.string,
  targetElement: PropTypes.object,
  padding: PropTypes.number,
  color: PropTypes.string,
  pulse: PropTypes.bool,
  visible: PropTypes.bool,
  onClick: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default OnboardingSpotlight;
