/**
 * API Key Routes
 */

const express = require('express');
const router = express.Router();
const ApiKeyController = require('../controllers/ApiKeyController');
const { authenticate, hasRole } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all API keys (admin only)
router.get('/', hasRole('admin'), (req, res, next) => {
  ApiKeyController.getAllApiKeys(req, res, next);
});

// Get API key by ID
router.get('/:id', (req, res, next) => {
  ApiKeyController.getApiKeyById(req, res, next);
});

// Update API key
router.put('/:id', (req, res, next) => {
  ApiKeyController.updateApiKey(req, res, next);
});

// Revoke all API keys for a user (admin only)
router.delete('/user/:userId', hasRole('admin'), (req, res, next) => {
  ApiKeyController.revokeUserApiKeys(req, res, next);
});

module.exports = router;
