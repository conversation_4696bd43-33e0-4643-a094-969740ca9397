/**
 * AccessibilityMenu Component
 * 
 * A component for displaying an accessibility menu.
 */

import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useAccessibility } from '../accessibility/AccessibilityContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';
import { useI18n } from '../i18n/I18nContext';
import AccessibilitySettings from './AccessibilitySettings';

/**
 * AccessibilityMenu component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='dropdown'] - Variant ('dropdown', 'button', or 'icon')
 * @param {string} [props.size='md'] - Size ('sm', 'md', or 'lg')
 * @param {string} [props.position='bottom-right'] - Position ('bottom-right', 'bottom-left', 'top-right', or 'top-left')
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} AccessibilityMenu component
 */
const AccessibilityMenu = ({
  variant = 'dropdown',
  size = 'md',
  position = 'bottom-right',
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { settings, toggleSetting } = useAccessibility();
  const { translate } = useI18n();
  
  // State
  const [isOpen, setIsOpen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // Refs
  const menuRef = useRef(null);
  const buttonRef = useRef(null);
  
  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isOpen &&
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);
  
  // Handle toggle
  const handleToggle = () => {
    setIsOpen(!isOpen);
  };
  
  // Handle setting toggle
  const handleSettingToggle = (setting) => {
    toggleSetting(setting);
  };
  
  // Handle settings click
  const handleSettingsClick = () => {
    setIsOpen(false);
    setShowSettings(true);
  };
  
  // Handle settings close
  const handleSettingsClose = () => {
    setShowSettings(false);
  };
  
  // Get button size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-2 text-base';
      default:
        return 'px-3 py-1.5 text-sm';
    }
  };
  
  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-full right-0 mb-2';
      case 'top-right':
        return 'top-full left-0 mt-2';
      case 'top-left':
        return 'top-full right-0 mt-2';
      default:
        return 'bottom-full left-0 mb-2';
    }
  };
  
  // Quick settings
  const quickSettings = [
    {
      id: 'highContrast',
      label: translate('accessibility.highContrast', 'High Contrast'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 01-6-6c0-1.4.5-2.7 1.3-3.7L12 13a6 6 0 01-2 3zm4.7-2.3L8 6a6 6 0 016.7 7.7z" />
        </svg>
      ),
      value: settings.highContrast
    },
    {
      id: 'largeText',
      label: translate('accessibility.largeText', 'Large Text'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
        </svg>
      ),
      value: settings.largeText
    },
    {
      id: 'reducedMotion',
      label: translate('accessibility.reducedMotion', 'Reduced Motion'),
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      ),
      value: settings.reducedMotion
    }
  ];
  
  // Dropdown variant
  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`} style={style} data-testid="accessibility-menu-dropdown">
        <button
          ref={buttonRef}
          type="button"
          className={`flex items-center justify-between rounded-md border border-divider bg-background text-textPrimary hover:bg-surface transition-colors duration-200 ${
            getSizeClasses()
          } ${isOpen ? 'bg-surface' : ''}`}
          onClick={handleToggle}
          aria-haspopup="true"
          aria-expanded={isOpen}
        >
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span>{translate('accessibility.accessibility', 'Accessibility')}</span>
          </span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`ml-2 h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
        
        {isOpen && (
          <Animated
            animation="fadeIn"
            className={`absolute ${getPositionClasses()} w-64 rounded-md border border-divider bg-background shadow-lg z-10`}
            ref={menuRef}
          >
            <div className="p-3">
              <div className="mb-3">
                <h3 className="text-sm font-medium text-textPrimary mb-2">
                  {translate('accessibility.quickSettings', 'Quick Settings')}
                </h3>
                <div className="space-y-2">
                  {quickSettings.map(setting => (
                    <button
                      key={setting.id}
                      type="button"
                      className={`flex items-center justify-between w-full px-3 py-2 rounded-md ${
                        setting.value
                          ? 'bg-primary bg-opacity-10 text-primary'
                          : 'text-textPrimary hover:bg-surface'
                      }`}
                      onClick={() => handleSettingToggle(setting.id)}
                    >
                      <span className="flex items-center">
                        <span className="mr-2">{setting.icon}</span>
                        <span>{setting.label}</span>
                      </span>
                      <span className={`relative w-8 h-4 bg-background border border-divider rounded-full transition-colors duration-200 ease-in-out ${setting.value ? 'bg-primary border-primary' : ''}`}>
                        <span className={`absolute left-0 top-0 w-4 h-4 bg-white rounded-full transition-transform duration-200 ease-in-out ${setting.value ? 'transform translate-x-4' : ''}`}></span>
                      </span>
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="border-t border-divider pt-3">
                <button
                  type="button"
                  className="flex items-center w-full px-3 py-2 rounded-md text-textPrimary hover:bg-surface"
                  onClick={handleSettingsClick}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                  <span>{translate('accessibility.allSettings', 'All Settings')}</span>
                </button>
              </div>
            </div>
          </Animated>
        )}
        
        {showSettings && (
          <AccessibilitySettings
            variant="dialog"
            onClose={handleSettingsClose}
          />
        )}
      </div>
    );
  }
  
  // Button variant
  if (variant === 'button') {
    return (
      <div className={`${className}`} style={style} data-testid="accessibility-menu-button">
        <button
          ref={buttonRef}
          type="button"
          className={`flex items-center justify-center rounded-md border border-divider bg-background text-textPrimary hover:bg-surface transition-colors duration-200 ${
            getSizeClasses()
          }`}
          onClick={() => setShowSettings(true)}
          aria-haspopup="dialog"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span>{translate('accessibility.accessibility', 'Accessibility')}</span>
        </button>
        
        {showSettings && (
          <AccessibilitySettings
            variant="dialog"
            onClose={handleSettingsClose}
          />
        )}
      </div>
    );
  }
  
  // Icon variant
  return (
    <div className={`${className}`} style={style} data-testid="accessibility-menu-icon">
      <button
        ref={buttonRef}
        type="button"
        className={`flex items-center justify-center rounded-md border border-divider bg-background text-textPrimary hover:bg-surface transition-colors duration-200 p-2`}
        onClick={handleToggle}
        aria-haspopup="true"
        aria-expanded={isOpen}
        aria-label={translate('accessibility.accessibility', 'Accessibility')}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
        </svg>
      </button>
      
      {isOpen && (
        <Animated
          animation="fadeIn"
          className={`absolute ${getPositionClasses()} w-64 rounded-md border border-divider bg-background shadow-lg z-10`}
          ref={menuRef}
        >
          <div className="p-3">
            <div className="mb-3">
              <h3 className="text-sm font-medium text-textPrimary mb-2">
                {translate('accessibility.quickSettings', 'Quick Settings')}
              </h3>
              <div className="space-y-2">
                {quickSettings.map(setting => (
                  <button
                    key={setting.id}
                    type="button"
                    className={`flex items-center justify-between w-full px-3 py-2 rounded-md ${
                      setting.value
                        ? 'bg-primary bg-opacity-10 text-primary'
                        : 'text-textPrimary hover:bg-surface'
                    }`}
                    onClick={() => handleSettingToggle(setting.id)}
                  >
                    <span className="flex items-center">
                      <span className="mr-2">{setting.icon}</span>
                      <span>{setting.label}</span>
                    </span>
                    <span className={`relative w-8 h-4 bg-background border border-divider rounded-full transition-colors duration-200 ease-in-out ${setting.value ? 'bg-primary border-primary' : ''}`}>
                      <span className={`absolute left-0 top-0 w-4 h-4 bg-white rounded-full transition-transform duration-200 ease-in-out ${setting.value ? 'transform translate-x-4' : ''}`}></span>
                    </span>
                  </button>
                ))}
              </div>
            </div>
            
            <div className="border-t border-divider pt-3">
              <button
                type="button"
                className="flex items-center w-full px-3 py-2 rounded-md text-textPrimary hover:bg-surface"
                onClick={handleSettingsClick}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                <span>{translate('accessibility.allSettings', 'All Settings')}</span>
              </button>
            </div>
          </div>
        </Animated>
      )}
      
      {showSettings && (
        <AccessibilitySettings
          variant="dialog"
          onClose={handleSettingsClose}
        />
      )}
    </div>
  );
};

AccessibilityMenu.propTypes = {
  variant: PropTypes.oneOf(['dropdown', 'button', 'icon']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  position: PropTypes.oneOf(['bottom-right', 'bottom-left', 'top-right', 'top-left']),
  className: PropTypes.string,
  style: PropTypes.object
};

export default AccessibilityMenu;
