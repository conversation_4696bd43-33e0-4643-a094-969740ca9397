afbcd5c707f2318261f524d8885d493a
/**
 * NovaFuse Universal API Connector Security Middleware
 * 
 * This module provides security middleware for the connector API.
 */

const {
  RateLimiter,
  InputValidator
} = require('../../src/security');

/**
 * Create rate limiting middleware
 * @param {Object} options - Rate limiter options
 * @returns {Function} - Express middleware
 */
function createRateLimiter(options = {}) {
  const rateLimiter = new RateLimiter(options);
  return function rateLimiterMiddleware(req, res, next) {
    if (rateLimiter.isAllowed(req)) {
      // Add rate limit headers
      res.setHeader('X-RateLimit-Limit', rateLimiter.options.maxRequests);
      res.setHeader('X-RateLimit-Remaining', rateLimiter.getRemainingRequests(req));
      res.setHeader('X-RateLimit-Reset', Math.floor(rateLimiter.getResetTime(req) / 1000));
      next();
    } else {
      // Rate limit exceeded
      res.setHeader('X-RateLimit-Limit', rateLimiter.options.maxRequests);
      res.setHeader('X-RateLimit-Remaining', 0);
      res.setHeader('X-RateLimit-Reset', Math.floor(rateLimiter.getResetTime(req) / 1000));
      res.setHeader('Retry-After', Math.ceil((rateLimiter.getResetTime(req) - Date.now()) / 1000));
      res.status(429).json({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.'
      });
    }
  };
}

/**
 * Create input validation middleware
 * @returns {Function} - Express middleware
 */
function createInputValidator() {
  return function inputValidatorMiddleware(req, res, next) {
    try {
      // Validate URL parameters
      for (const param in req.params) {
        if (!InputValidator.isXssSafe(req.params[param]) || !InputValidator.isCommandSafe(req.params[param])) {
          return res.status(400).json({
            error: 'Invalid parameter',
            message: `Parameter '${param}' contains invalid characters`
          });
        }
      }

      // Validate query parameters
      for (const param in req.query) {
        if (!InputValidator.isXssSafe(req.query[param]) || !InputValidator.isCommandSafe(req.query[param])) {
          return res.status(400).json({
            error: 'Invalid parameter',
            message: `Query parameter '${param}' contains invalid characters`
          });
        }
      }

      // Validate request body
      if (req.body && typeof req.body === 'object') {
        const validateObject = (obj, path = '') => {
          for (const key in obj) {
            const value = obj[key];
            const currentPath = path ? `${path}.${key}` : key;
            if (typeof value === 'string') {
              if (!InputValidator.isXssSafe(value) || !InputValidator.isCommandSafe(value)) {
                throw new Error(`Field '${currentPath}' contains invalid characters`);
              }
            } else if (typeof value === 'object' && value !== null) {
              validateObject(value, currentPath);
            }
          }
        };
        try {
          validateObject(req.body);
        } catch (error) {
          return res.status(400).json({
            error: 'Invalid request body',
            message: error.message
          });
        }
      }
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Create security headers middleware
 * @returns {Function} - Express middleware
 */
function createSecurityHeaders() {
  return function securityHeadersMiddleware(req, res, next) {
    // Set security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.setHeader('Content-Security-Policy', "default-src 'self'");
    res.setHeader('Referrer-Policy', 'no-referrer');
    next();
  };
}
module.exports = {
  createRateLimiter,
  createInputValidator,
  createSecurityHeaders
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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