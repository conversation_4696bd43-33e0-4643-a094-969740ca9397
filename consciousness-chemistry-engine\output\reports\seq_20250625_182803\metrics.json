{"psi_score": 0.66, "fibonacci_analysis": {"residue_distances": [{"start": 1, "end": 3, "score": 0.9697950832191009, "ratio": 1.6230734638745399, "golden_ratio_diff": 0.0031145669124902084}, {"start": 3, "end": 8, "score": 0.9220406668172991, "ratio": 1.60435337189974, "golden_ratio_diff": 0.008455086200460236}, {"start": 1, "end": 9, "score": 0.898774949933813, "ratio": 1.6362571904014387, "golden_ratio_diff": 0.011262558004497276}, {"start": 2, "end": 9, "score": 0.8879373632147763, "ratio": 1.6384544781559216, "golden_ratio_diff": 0.012620556519831651}, {"start": 6, "end": 11, "score": 0.8358403404708815, "ratio": 1.586255681836629, "golden_ratio_diff": 0.019640073777324144}, {"start": 6, "end": 10, "score": 0.8340873385923245, "ratio": 1.6502191464546785, "golden_ratio_diff": 0.01989152139483181}, {"start": 12, "end": 15, "score": 0.8222677151495359, "ratio": 1.6530076199028498, "golden_ratio_diff": 0.02161489276252832}, {"start": 1, "end": 10, "score": 0.7375335675643331, "ratio": 1.5604529338306836, "golden_ratio_diff": 0.035587049048146906}, {"start": 2, "end": 10, "score": 0.7086376127044596, "ratio": 1.5515071438244183, "golden_ratio_diff": 0.04111585132823798}, {"start": 3, "end": 11, "score": 0.7008350044654905, "ratio": 1.6871029033923972, "golden_ratio_diff": 0.04268693681513172}, {"start": 1, "end": 11, "score": 0.6803074603141971, "ratio": 1.541998750958661, "golden_ratio_diff": 0.04699236129766302}, {"start": 0, "end": 6, "score": 0.6572003411325372, "ratio": 1.5336363607109944, "golden_ratio_diff": 0.05216060269791164}, {"start": 2, "end": 11, "score": 0.6525041624619538, "ratio": 1.531864411844176, "golden_ratio_diff": 0.05325572732393228}, {"start": 0, "end": 9, "score": 0.6277076467165399, "ratio": 1.5220686640457424, "golden_ratio_diff": 0.05930983240858621}, {"start": 3, "end": 12, "score": 0.6108643436081198, "ratio": 1.5149612321117232, "golden_ratio_diff": 0.06370246691653647}, {"start": 3, "end": 14, "score": 0.5998443191713629, "ratio": 1.5100950666531563, "golden_ratio_diff": 0.06670992256481152}, {"start": 1, "end": 7, "score": 0.5921567956710551, "ratio": 1.5065932062562866, "golden_ratio_diff": 0.06887419131393421}, {"start": 3, "end": 15, "score": 0.5845826916984411, "ratio": 1.5030529374987827, "golden_ratio_diff": 0.07106219773538099}, {"start": 5, "end": 9, "score": 0.5840261310130183, "ratio": 1.5027891696036555, "golden_ratio_diff": 0.0712252152597106}, {"start": 3, "end": 10, "score": 0.5785678691600673, "ratio": 1.7358925059607169, "golden_ratio_diff": 0.07284056949995243}, {"start": 1, "end": 6, "score": 0.5776060251155798, "ratio": 1.736358205999776, "golden_ratio_diff": 0.07312838795265311}, {"start": 11, "end": 14, "score": 0.5594726562447137, "ratio": 1.490630404818889, "golden_ratio_diff": 0.0787397451579116}, {"start": 2, "end": 7, "score": 0.5351274204436764, "ratio": 1.4774731418517233, "golden_ratio_diff": 0.08687138087054028}, {"start": 11, "end": 15, "score": 0.524416445335225, "ratio": 1.471297485197608, "golden_ratio_diff": 0.09068814658563303}, {"start": 3, "end": 13, "score": 0.5217651549875029, "ratio": 1.4697296747423234, "golden_ratio_diff": 0.09165710673491631}, {"start": 0, "end": 10, "score": 0.5178327330761696, "ratio": 1.4673747166890374, "golden_ratio_diff": 0.09311255085392735}, {"start": 2, "end": 6, "score": 0.508993072072995, "ratio": 1.774119786708188, "golden_ratio_diff": 0.09646632829937403}, {"start": 0, "end": 11, "score": 0.5060094141070753, "ratio": 1.4600737738183818, "golden_ratio_diff": 0.09762478169791376}], "torsion_angles": [{"start": 3, "end": 5, "score": 0.9694586683431118, "ratio": 1.612936616959204, "golden_ratio_diff": 0.003150349019941834}], "overall_score": 0.679523896303633}, "fibonacci_alignment": {"closest_fibonacci": 21, "difference": 1, "ratio": 0.9523809523809523, "alignment_score": 0.9545454545454545}, "trinity_validation": {"ners": 0.6658571688910898, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6362428675564359, "passed": false}, "trinity_report": {"scores": {"ners": 0.6658571688910898, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6362428675564359, "passed": false}, "thresholds": {"ners": 0.7, "nepi": 0.5, "nefc": 0.6}, "weights": {"ners": 0.4, "nepi": 0.3, "nefc": 0.3}, "validation": {"ners": {"score": 0.6658571688910898, "threshold": 0.7, "passed": false, "description": "Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance."}, "nepi": {"score": 0.5389999999999999, "threshold": 0.5, "passed": true, "description": "Neural-Emotional Potential Index: Evaluates functional potential and adaptability."}, "nefc": {"score": 0.694, "threshold": 0.6, "passed": true, "description": "Neural-Emotional Field Coherence: Assesses field coherence and quantum effects."}, "overall": {"score": 0.6362428675564359, "passed": false, "description": "Overall validation status based on all metrics."}}}}