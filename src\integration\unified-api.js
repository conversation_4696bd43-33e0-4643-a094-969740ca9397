/**
 * NovaFuse Unified API
 *
 * This module provides a unified API for accessing NovaCore, NovaProof, NovaConnect, and NovaVision
 * functionality through a single interface. It uses the NovaFuse Integration to
 * coordinate between the components.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const { NovaFuseIntegration } = require('./novafuse-integration');
const { CSDEFoundation } = require('./csde-foundation');

/**
 * NovaFuse Unified API class
 */
class UnifiedAPI {
  /**
   * Create a new Unified API instance
   * @param {Object} options - API options
   */
  constructor(options = {}) {
    this.options = {
      port: options.port || process.env.PORT || 3000,
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      enableCors: options.enableCors !== undefined ? options.enableCors : true,
      enableCompression: options.enableCompression !== undefined ? options.enableCompression : true,
      enableHelmet: options.enableHelmet !== undefined ? options.enableHelmet : true,
      ...options
    };

    // Initialize components
    this.novaCore = options.novaCore;
    this.novaProof = options.novaProof;
    this.novaConnect = options.novaConnect;
    this.novaVision = options.novaVision;

    // Initialize CSDE Foundation
    this.csde = new CSDEFoundation({
      enableLogging: this.options.enableLogging,
      apiUrl: options.csdeApiUrl || process.env.CSDE_API_URL,
      apiKey: options.csdeApiKey || process.env.CSDE_API_KEY
    });

    // Initialize NovaFuse Integration
    this.integration = new NovaFuseIntegration({
      enableLogging: this.options.enableLogging,
      novaCore: this.novaCore,
      novaProof: this.novaProof,
      novaConnect: this.novaConnect,
      novaVision: this.novaVision,
      csde: this.csde
    });

    // Initialize Express app
    this.app = express();

    // Configure middleware
    this._configureMiddleware();

    // Configure routes
    this._configureRoutes();

    this.log('NovaFuse Unified API initialized with options:', this.options);
  }

  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[UnifiedAPI ${new Date().toISOString()}]`, ...args);
    }
  }

  /**
   * Configure Express middleware
   * @private
   */
  _configureMiddleware() {
    // Parse JSON request bodies
    this.app.use(bodyParser.json());

    // Parse URL-encoded request bodies
    this.app.use(bodyParser.urlencoded({ extended: true }));

    // Enable CORS if configured
    if (this.options.enableCors) {
      this.app.use(cors());
    }

    // Enable compression if configured
    if (this.options.enableCompression) {
      const compression = require('compression');
      this.app.use(compression());
    }

    // Enable Helmet security headers if configured
    if (this.options.enableHelmet) {
      const helmet = require('helmet');
      this.app.use(helmet());
    }

    // Add request logging middleware
    this.app.use((req, res, next) => {
      if (this.options.enableLogging) {
        this.log(`${req.method} ${req.url}`);
      }
      next();
    });
  }

  /**
   * Configure API routes
   * @private
   */
  _configureRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      const health = {
        status: 'ok',
        timestamp: new Date().toISOString(),
        components: {
          integration: this.integration.getHealth(),
          csde: this.csde.getHealth()
        }
      };

      res.json(health);
    });

    // NovaCore API routes
    this.app.use('/api/novacore', this._createNovaCoreRouter());

    // NovaProof API routes
    this.app.use('/api/novaproof', this._createNovaProofRouter());

    // NovaConnect API routes
    this.app.use('/api/novaconnect', this._createNovaConnectRouter());

    // NovaVision API routes
    this.app.use('/api/novavision', this._createNovaVisionRouter());

    // CSDE API routes
    this.app.use('/api/csde', this._createCSDERouter());

    // Unified API routes
    this.app.use('/api/unified', this._createUnifiedRouter());

    // Error handling middleware
    this.app.use((err, req, res, next) => {
      this.log('Error handling request:', err);

      res.status(err.status || 500).json({
        error: {
          message: err.message,
          status: err.status || 500
        }
      });
    });
  }

  /**
   * Create NovaCore API router
   * @returns {express.Router} - The NovaCore API router
   * @private
   */
  _createNovaCoreRouter() {
    const router = express.Router();

    // Tensor operations
    router.post('/tensor/create', async (req, res, next) => {
      try {
        const { dimensions, data, metadata } = req.body;

        const result = await this.integration.eventProcessor.processEvent(new Event('tensor.create', {
          dimensions,
          data,
          metadata
        }));

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    router.post('/tensor/process', async (req, res, next) => {
      try {
        const { tensor, operation, options } = req.body;

        const result = await this.integration.eventProcessor.processEvent(new Event('tensor.process', {
          tensor,
          operation,
          options
        }));

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    // Control system operations
    router.get('/control/loops', async (req, res, next) => {
      try {
        const controlLoops = this.integration.controlSystem.getAllControlLoopStates();
        res.json(controlLoops);
      } catch (error) {
        next(error);
      }
    });

    router.post('/control/loop', async (req, res, next) => {
      try {
        const controlLoop = this.integration.controlSystem.registerControlLoop(req.body);
        res.json(controlLoop.getState());
      } catch (error) {
        next(error);
      }
    });

    router.post('/control/loop/:id/start', async (req, res, next) => {
      try {
        const controlLoop = this.integration.controlSystem.startControlLoop(req.params.id);

        if (!controlLoop) {
          return res.status(404).json({ error: { message: 'Control loop not found' } });
        }

        res.json(controlLoop.getState());
      } catch (error) {
        next(error);
      }
    });

    router.post('/control/loop/:id/stop', async (req, res, next) => {
      try {
        const controlLoop = this.integration.controlSystem.stopControlLoop(req.params.id);

        if (!controlLoop) {
          return res.status(404).json({ error: { message: 'Control loop not found' } });
        }

        res.json(controlLoop.getState());
      } catch (error) {
        next(error);
      }
    });

    return router;
  }

  /**
   * Create NovaProof API router
   * @returns {express.Router} - The NovaProof API router
   * @private
   */
  _createNovaProofRouter() {
    const router = express.Router();

    // Evidence operations
    router.post('/evidence', async (req, res, next) => {
      try {
        const evidence = new Evidence(req.body);
        res.json(evidence);
      } catch (error) {
        next(error);
      }
    });

    router.post('/evidence/verify', async (req, res, next) => {
      try {
        const { evidence, options } = req.body;

        const result = await this.integration.eventProcessor.processEvent(new Event('evidence.verify', {
          evidence,
          options
        }));

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    router.post('/evidence/batch-verify', async (req, res, next) => {
      try {
        const { evidenceItems, options } = req.body;

        const result = await this.integration.eventProcessor.processEvent(new Event('evidence.batchVerify', {
          evidenceItems,
          options
        }));

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    // Blockchain operations
    router.get('/blockchain/providers', async (req, res, next) => {
      try {
        const providers = Array.from(this.integration.blockchainManager.providers.keys());
        res.json(providers);
      } catch (error) {
        next(error);
      }
    });

    router.post('/blockchain/connect', async (req, res, next) => {
      try {
        const { type } = req.body;

        await this.integration.blockchainManager.connect(type);

        res.json({ status: 'connected', type });
      } catch (error) {
        next(error);
      }
    });

    router.post('/blockchain/disconnect', async (req, res, next) => {
      try {
        const { type } = req.body;

        await this.integration.blockchainManager.disconnect(type);

        res.json({ status: 'disconnected', type });
      } catch (error) {
        next(error);
      }
    });

    return router;
  }

  /**
   * Create NovaConnect API router
   * @returns {express.Router} - The NovaConnect API router
   * @private
   */
  _createNovaConnectRouter() {
    const router = express.Router();

    // API request handling
    router.all('*', async (req, res, next) => {
      try {
        const endpoint = req.path;
        const method = req.method;
        const data = method === 'GET' ? req.query : req.body;

        const result = await this.integration.eventProcessor.processEvent(new Event('api.request', {
          endpoint,
          method,
          data,
          options: {
            headers: req.headers
          }
        }));

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    return router;
  }

  /**
   * Create NovaVision API router
   * @returns {express.Router} - The NovaVision API router
   * @private
   */
  _createNovaVisionRouter() {
    const router = express.Router();

    // Generate visualization
    router.post('/visualization', async (req, res, next) => {
      try {
        const { type, data, options } = req.body;

        const result = await this.integration.eventProcessor.processEvent(new Event('visualization.generate', {
          type,
          data,
          options
        }));

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    // Get visualization types
    router.get('/visualization/types', async (req, res, next) => {
      try {
        const types = [
          'tensor',
          'controlSystem',
          'evidence',
          'blockchain',
          'connector',
          'api',
          'csde'
        ];

        res.json({ types });
      } catch (error) {
        next(error);
      }
    });

    // Get visualization options for a type
    router.get('/visualization/options/:type', async (req, res, next) => {
      try {
        const { type } = req.params;

        let options;

        switch (type) {
          case 'tensor':
            options = {
              visualizationTypes: ['heatmap', 'matrix', 'network', '3d'],
              colorScales: ['viridis', 'plasma', 'inferno', 'magma', 'cividis']
            };
            break;

          case 'controlSystem':
            options = {
              visualizationTypes: ['dashboard', 'timeline', 'status']
            };
            break;

          case 'evidence':
            options = {
              visualizationTypes: ['card', 'table', 'timeline']
            };
            break;

          case 'blockchain':
            options = {
              visualizationTypes: ['timeline', 'tree', 'network']
            };
            break;

          case 'connector':
            options = {
              visualizationTypes: ['card', 'status', 'metrics']
            };
            break;

          case 'api':
            options = {
              visualizationTypes: ['table', 'tree', 'documentation']
            };
            break;

          case 'csde':
            options = {
              visualizationTypes: ['dashboard', 'domain', 'operation']
            };
            break;

          default:
            return res.status(404).json({ error: { message: `Unknown visualization type: ${type}` } });
        }

        res.json({ type, options });
      } catch (error) {
        next(error);
      }
    });

    return router;
  }

  /**
   * Create CSDE API router
   * @returns {express.Router} - The CSDE API router
   * @private
   */
  _createCSDERouter() {
    const router = express.Router();

    // CSDE operations
    router.post('/process', async (req, res, next) => {
      try {
        const result = await this.csde.processData(req.body);
        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    router.get('/health', async (req, res, next) => {
      try {
        const health = this.csde.getHealth();
        res.json(health);
      } catch (error) {
        next(error);
      }
    });

    return router;
  }

  /**
   * Create unified API router
   * @returns {express.Router} - The unified API router
   * @private
   */
  _createUnifiedRouter() {
    const router = express.Router();

    // Unified operations
    router.post('/process', async (req, res, next) => {
      try {
        const { type, data, component } = req.body;

        let result;

        if (component === 'novacore') {
          result = await this.integration.eventProcessor.processEvent(new Event('tensor.process', {
            tensor: data,
            operation: type
          }));
        } else if (component === 'novaproof') {
          result = await this.integration.eventProcessor.processEvent(new Event('evidence.verify', {
            evidence: data
          }));
        } else if (component === 'novaconnect') {
          result = await this.integration.eventProcessor.processEvent(new Event('api.request', {
            endpoint: data.endpoint,
            method: data.method,
            data: data.data
          }));
        } else if (component === 'novavision') {
          result = await this.integration.eventProcessor.processEvent(new Event('visualization.generate', {
            type,
            data,
            options: data.options
          }));
        } else if (component === 'csde') {
          result = await this.csde.processData({
            type,
            data
          });
        } else {
          throw new Error(`Unknown component: ${component}`);
        }

        res.json(result);
      } catch (error) {
        next(error);
      }
    });

    return router;
  }

  /**
   * Start the API server
   * @returns {Promise<void>} - A promise that resolves when the server is started
   */
  async start() {
    try {
      this.log('Starting NovaFuse Unified API...');

      // Initialize CSDE Foundation
      await this.csde.initialize();

      // Initialize NovaFuse Integration
      await this.integration.initialize();

      // Start the server
      return new Promise((resolve) => {
        this.server = this.app.listen(this.options.port, () => {
          this.log(`NovaFuse Unified API listening on port ${this.options.port}`);
          resolve();
        });
      });
    } catch (error) {
      this.log('Error starting NovaFuse Unified API:', error);
      throw error;
    }
  }

  /**
   * Stop the API server
   * @returns {Promise<void>} - A promise that resolves when the server is stopped
   */
  async stop() {
    try {
      this.log('Stopping NovaFuse Unified API...');

      // Shutdown NovaFuse Integration
      await this.integration.shutdown();

      // Shutdown CSDE Foundation
      await this.csde.shutdown();

      // Stop the server
      return new Promise((resolve, reject) => {
        if (!this.server) {
          return resolve();
        }

        this.server.close((err) => {
          if (err) {
            this.log('Error stopping server:', err);
            return reject(err);
          }

          this.log('NovaFuse Unified API stopped');
          resolve();
        });
      });
    } catch (error) {
      this.log('Error stopping NovaFuse Unified API:', error);
      throw error;
    }
  }
}

module.exports = {
  UnifiedAPI
};
