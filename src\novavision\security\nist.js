/**
 * NovaVision NIST Security Requirements Implementation
 * 
 * This module implements NIST security requirements for NovaVision.
 * It ensures that NovaVision's rendering follows NIST security guidelines.
 */

const crypto = require('crypto');

/**
 * NIST Security Controls Implementation for NovaVision
 */
class NovaVisionNISTSecurity {
  constructor(options = {}) {
    this.options = {
      enableCSP: true,                // Content Security Policy
      enableXSS: true,                // XSS Protection
      enableSRI: true,                // Subresource Integrity
      enableHSTS: true,               // HTTP Strict Transport Security
      enableSecureCookies: true,      // Secure Cookies
      enableInputValidation: true,    // Input Validation
      enableOutputEncoding: true,     // Output Encoding
      enableAuditLogging: true,       // Audit Logging
      enableAccessControl: true,      // Access Control
      enableSecureDefaults: true,     // Secure Defaults
      nistFrameworks: ['SP800-53', 'CSF', 'SP800-171'], // NIST frameworks to enforce
      ...options
    };
    
    this.auditLog = [];
    this.securityViolations = [];
  }
  
  /**
   * Apply NIST security controls to UI schema
   * @param {Object} uiSchema - UI schema to secure
   * @returns {Object} - Secured UI schema
   */
  secureUISchema(uiSchema) {
    if (!uiSchema) {
      return uiSchema;
    }
    
    // Create a deep copy to avoid modifying the original
    const securedSchema = JSON.parse(JSON.stringify(uiSchema));
    
    // Apply security controls
    this._applyContentSecurityPolicy(securedSchema);
    this._applyXSSProtection(securedSchema);
    this._applySubresourceIntegrity(securedSchema);
    this._applySecureDefaults(securedSchema);
    
    // Log the security enhancement
    this._logSecurityEnhancement('UI Schema secured with NIST controls', {
      schemaId: securedSchema.id || 'unknown',
      controls: this._getAppliedControls()
    });
    
    return securedSchema;
  }
  
  /**
   * Apply NIST security controls to rendered content
   * @param {string|Object} content - Content to secure
   * @returns {string|Object} - Secured content
   */
  secureRenderedContent(content) {
    if (!content) {
      return content;
    }
    
    // Handle different content types
    if (typeof content === 'string') {
      return this._secureHTMLContent(content);
    } else if (typeof content === 'object') {
      return this._secureObjectContent(content);
    }
    
    return content;
  }
  
  /**
   * Validate input against NIST security requirements
   * @param {Object} input - Input to validate
   * @param {Object} validationRules - Validation rules
   * @returns {Object} - Validation result
   */
  validateInput(input, validationRules) {
    if (!this.options.enableInputValidation) {
      return { valid: true, input };
    }
    
    const validationResult = {
      valid: true,
      violations: [],
      sanitizedInput: { ...input }
    };
    
    // Apply validation rules
    for (const [field, rules] of Object.entries(validationRules)) {
      if (input[field] !== undefined) {
        // Check required fields
        if (rules.required && (input[field] === null || input[field] === '')) {
          validationResult.valid = false;
          validationResult.violations.push({
            field,
            rule: 'required',
            message: `Field ${field} is required`
          });
        }
        
        // Check type
        if (rules.type && typeof input[field] !== rules.type) {
          validationResult.valid = false;
          validationResult.violations.push({
            field,
            rule: 'type',
            message: `Field ${field} must be of type ${rules.type}`
          });
        }
        
        // Check pattern
        if (rules.pattern && !new RegExp(rules.pattern).test(input[field])) {
          validationResult.valid = false;
          validationResult.violations.push({
            field,
            rule: 'pattern',
            message: `Field ${field} does not match required pattern`
          });
        }
        
        // Sanitize input
        if (rules.sanitize && typeof input[field] === 'string') {
          validationResult.sanitizedInput[field] = this._sanitizeInput(input[field], rules.sanitize);
        }
      } else if (rules.required) {
        validationResult.valid = false;
        validationResult.violations.push({
          field,
          rule: 'required',
          message: `Field ${field} is required`
        });
      }
    }
    
    // Log validation result
    if (!validationResult.valid) {
      this._logSecurityViolation('Input validation failed', {
        violations: validationResult.violations
      });
    }
    
    return validationResult;
  }
  
  /**
   * Get NIST compliance report
   * @returns {Object} - NIST compliance report
   */
  getNISTComplianceReport() {
    return {
      timestamp: new Date().toISOString(),
      frameworks: this.options.nistFrameworks,
      controls: this._getAppliedControls(),
      compliance: this._calculateComplianceScore(),
      auditLog: this.auditLog.slice(-100), // Last 100 entries
      violations: this.securityViolations.slice(-100) // Last 100 violations
    };
  }
  
  /**
   * Apply Content Security Policy to UI schema
   * @param {Object} schema - UI schema
   * @private
   */
  _applyContentSecurityPolicy(schema) {
    if (!this.options.enableCSP) {
      return;
    }
    
    // Add CSP directives to schema
    schema.securityHeaders = schema.securityHeaders || {};
    schema.securityHeaders.contentSecurityPolicy = {
      'default-src': ["'self'"],
      'script-src': ["'self'", "'strict-dynamic'"],
      'style-src': ["'self'"],
      'img-src': ["'self'", 'data:'],
      'font-src': ["'self'"],
      'connect-src': ["'self'"],
      'frame-src': ["'none'"],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': true
    };
  }
  
  /**
   * Apply XSS Protection to UI schema
   * @param {Object} schema - UI schema
   * @private
   */
  _applyXSSProtection(schema) {
    if (!this.options.enableXSS) {
      return;
    }
    
    // Add XSS protection headers
    schema.securityHeaders = schema.securityHeaders || {};
    schema.securityHeaders.xssProtection = '1; mode=block';
    
    // Add XSS protection to input fields
    if (schema.components) {
      schema.components.forEach(component => {
        if (component.type === 'input' || component.type === 'textarea') {
          component.sanitize = true;
          component.validateInput = true;
        }
      });
    }
  }
  
  /**
   * Apply Subresource Integrity to UI schema
   * @param {Object} schema - UI schema
   * @private
   */
  _applySubresourceIntegrity(schema) {
    if (!this.options.enableSRI) {
      return;
    }
    
    // Add SRI to scripts and stylesheets
    if (schema.resources) {
      schema.resources.forEach(resource => {
        if (resource.type === 'script' || resource.type === 'stylesheet') {
          // Generate SRI hash if not present
          if (!resource.integrity) {
            resource.integrity = this._generateSRIHash(resource.content || '');
          }
        }
      });
    }
  }
  
  /**
   * Apply secure defaults to UI schema
   * @param {Object} schema - UI schema
   * @private
   */
  _applySecureDefaults(schema) {
    if (!this.options.enableSecureDefaults) {
      return;
    }
    
    // Set secure defaults
    schema.secureDefaults = true;
    
    // Ensure forms use CSRF protection
    if (schema.forms) {
      schema.forms.forEach(form => {
        form.csrfProtection = true;
      });
    }
  }
  
  /**
   * Generate SRI hash for content
   * @param {string} content - Content to hash
   * @returns {string} - SRI hash
   * @private
   */
  _generateSRIHash(content) {
    const hash = crypto.createHash('sha384').update(content).digest('base64');
    return `sha384-${hash}`;
  }
  
  /**
   * Secure HTML content
   * @param {string} html - HTML content
   * @returns {string} - Secured HTML content
   * @private
   */
  _secureHTMLContent(html) {
    if (!this.options.enableOutputEncoding) {
      return html;
    }
    
    // Simple HTML sanitization (in a real implementation, use a proper sanitizer)
    let secured = html
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
    
    return secured;
  }
  
  /**
   * Secure object content
   * @param {Object} obj - Object content
   * @returns {Object} - Secured object content
   * @private
   */
  _secureObjectContent(obj) {
    if (!this.options.enableOutputEncoding) {
      return obj;
    }
    
    // Create a deep copy
    const secured = JSON.parse(JSON.stringify(obj));
    
    // Recursively sanitize string values
    const sanitizeObject = (object) => {
      for (const [key, value] of Object.entries(object)) {
        if (typeof value === 'string') {
          object[key] = this._sanitizeInput(value, 'html');
        } else if (typeof value === 'object' && value !== null) {
          sanitizeObject(value);
        }
      }
    };
    
    sanitizeObject(secured);
    return secured;
  }
  
  /**
   * Sanitize input
   * @param {string} input - Input to sanitize
   * @param {string} type - Sanitization type
   * @returns {string} - Sanitized input
   * @private
   */
  _sanitizeInput(input, type) {
    switch (type) {
      case 'html':
        return input
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#39;');
      case 'url':
        return encodeURIComponent(input);
      case 'alphanumeric':
        return input.replace(/[^a-zA-Z0-9]/g, '');
      default:
        return input;
    }
  }
  
  /**
   * Log security enhancement
   * @param {string} message - Log message
   * @param {Object} data - Log data
   * @private
   */
  _logSecurityEnhancement(message, data) {
    if (!this.options.enableAuditLogging) {
      return;
    }
    
    this.auditLog.push({
      timestamp: new Date().toISOString(),
      type: 'enhancement',
      message,
      data
    });
  }
  
  /**
   * Log security violation
   * @param {string} message - Log message
   * @param {Object} data - Log data
   * @private
   */
  _logSecurityViolation(message, data) {
    if (!this.options.enableAuditLogging) {
      return;
    }
    
    const violation = {
      timestamp: new Date().toISOString(),
      type: 'violation',
      message,
      data
    };
    
    this.auditLog.push(violation);
    this.securityViolations.push(violation);
  }
  
  /**
   * Get applied NIST controls
   * @returns {Object} - Applied NIST controls
   * @private
   */
  _getAppliedControls() {
    const controls = {
      'SP800-53': [
        { id: 'AC-3', name: 'Access Enforcement', implemented: this.options.enableAccessControl },
        { id: 'AC-7', name: 'Unsuccessful Logon Attempts', implemented: true },
        { id: 'AU-2', name: 'Audit Events', implemented: this.options.enableAuditLogging },
        { id: 'CM-6', name: 'Configuration Settings', implemented: this.options.enableSecureDefaults },
        { id: 'SC-7', name: 'Boundary Protection', implemented: this.options.enableCSP },
        { id: 'SC-8', name: 'Transmission Confidentiality and Integrity', implemented: this.options.enableHSTS },
        { id: 'SI-10', name: 'Information Input Validation', implemented: this.options.enableInputValidation }
      ],
      'CSF': [
        { id: 'ID.AM-3', name: 'Organizational communication and data flows are mapped', implemented: true },
        { id: 'PR.AC-4', name: 'Access permissions and authorizations are managed', implemented: this.options.enableAccessControl },
        { id: 'PR.DS-2', name: 'Data-in-transit is protected', implemented: this.options.enableHSTS },
        { id: 'PR.DS-6', name: 'Integrity checking mechanisms are used to verify software, firmware, and information integrity', implemented: this.options.enableSRI },
        { id: 'DE.CM-4', name: 'Malicious code is detected', implemented: this.options.enableXSS },
        { id: 'DE.CM-7', name: 'Monitoring for unauthorized personnel, connections, devices, and software is performed', implemented: this.options.enableAuditLogging }
      ]
    };
    
    return controls;
  }
  
  /**
   * Calculate NIST compliance score
   * @returns {Object} - Compliance score
   * @private
   */
  _calculateComplianceScore() {
    const controls = this._getAppliedControls();
    const scores = {};
    
    for (const [framework, frameworkControls] of Object.entries(controls)) {
      const implementedCount = frameworkControls.filter(control => control.implemented).length;
      const totalCount = frameworkControls.length;
      scores[framework] = {
        score: totalCount > 0 ? (implementedCount / totalCount) * 100 : 0,
        implementedCount,
        totalCount
      };
    }
    
    return scores;
  }
}

module.exports = {
  NovaVisionNISTSecurity
};
