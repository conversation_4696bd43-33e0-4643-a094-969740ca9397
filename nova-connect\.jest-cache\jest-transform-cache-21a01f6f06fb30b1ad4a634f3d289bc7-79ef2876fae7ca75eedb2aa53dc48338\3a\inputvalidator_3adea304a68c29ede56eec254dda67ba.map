{"version": 3, "names": ["InputValidator", "isSqlSafe", "input", "sqlPatterns", "some", "pattern", "test", "isXssSafe", "isCommandSafe", "commandPatterns", "sanitizeHtml", "replace", "sanitizeSql", "sanitizeCommand", "validateObject", "schema", "errors", "result", "<PERSON><PERSON><PERSON><PERSON>", "push", "required", "Array", "isArray", "field", "undefined", "properties", "fieldSchema", "Object", "entries", "type", "enum", "includes", "join", "RegExp", "minimum", "maximum", "<PERSON><PERSON><PERSON><PERSON>", "length", "max<PERSON><PERSON><PERSON>", "minItems", "maxItems", "module", "exports"], "sources": ["input-validator.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Input Validator\n * \n * This module provides input validation utilities for the UAC.\n */\n\n/**\n * Input Validation Utility\n * \n * Provides methods for validating user input to prevent injection attacks.\n */\nclass InputValidator {\n  /**\n   * Validate a string against SQL injection\n   * @param {string} input - Input to validate\n   * @returns {boolean} - Whether the input is safe\n   */\n  static isSqlSafe(input) {\n    if (typeof input !== 'string') {\n      return true;\n    }\n    \n    // Check for common SQL injection patterns\n    const sqlPatterns = [\n      /(\\s|^)(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|EXEC|UNION|CREATE|WHERE)(\\s|$)/i,\n      /(\\s|^)(OR|AND)(\\s+)(['\"]?\\d+['\"]?\\s*=\\s*['\"]?\\d+['\"]?)/i,\n      /--/,\n      /;.*/,\n      /\\/\\*.+\\*\\//\n    ];\n    \n    return !sqlPatterns.some(pattern => pattern.test(input));\n  }\n\n  /**\n   * Validate a string against XSS\n   * @param {string} input - Input to validate\n   * @returns {boolean} - Whether the input is safe\n   */\n  static isXssSafe(input) {\n    if (typeof input !== 'string') {\n      return true;\n    }\n    \n    return !/<script|javascript:|on\\w+\\s*=|data:|vbscript:|<iframe/i.test(input);\n  }\n\n  /**\n   * Validate a string against command injection\n   * @param {string} input - Input to validate\n   * @returns {boolean} - Whether the input is safe\n   */\n  static isCommandSafe(input) {\n    if (typeof input !== 'string') {\n      return true;\n    }\n    \n    // Check for common command injection patterns\n    const commandPatterns = [\n      /(\\s|^)(bash|sh|cmd|powershell|exec|eval)(\\s|$)/i,\n      /[&|;`]/,\n      /\\$\\(.+\\)/,\n      />\\s*[a-zA-Z0-9]/,\n      /<\\s*[a-zA-Z0-9]/\n    ];\n    \n    return !commandPatterns.some(pattern => pattern.test(input));\n  }\n\n  /**\n   * Sanitize a string for safe use in HTML\n   * @param {string} input - Input to sanitize\n   * @returns {string} - Sanitized input\n   */\n  static sanitizeHtml(input) {\n    if (typeof input !== 'string') {\n      return input;\n    }\n    \n    return input\n      .replace(/&/g, '&amp;')\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;')\n      .replace(/\"/g, '&quot;')\n      .replace(/'/g, '&#x27;')\n      .replace(/\\//g, '&#x2F;');\n  }\n\n  /**\n   * Sanitize a string for safe use in SQL\n   * @param {string} input - Input to sanitize\n   * @returns {string} - Sanitized input\n   */\n  static sanitizeSql(input) {\n    if (typeof input !== 'string') {\n      return input;\n    }\n    \n    return input\n      .replace(/'/g, \"''\")\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\x1a/g, '\\\\Z');\n  }\n\n  /**\n   * Sanitize a string for safe use in shell commands\n   * @param {string} input - Input to sanitize\n   * @returns {string} - Sanitized input\n   */\n  static sanitizeCommand(input) {\n    if (typeof input !== 'string') {\n      return input;\n    }\n    \n    return input\n      .replace(/[&;|`$><!\\\\]/g, '')\n      .replace(/\\r/g, '')\n      .replace(/\\n/g, '');\n  }\n\n  /**\n   * Validate an object against a schema\n   * @param {Object} input - Input to validate\n   * @param {Object} schema - Schema to validate against\n   * @returns {Object} - Validation result with isValid and errors properties\n   */\n  static validateObject(input, schema) {\n    const errors = [];\n    const result = { isValid: true, errors };\n    \n    if (!input || typeof input !== 'object') {\n      result.isValid = false;\n      errors.push('Input must be an object');\n      return result;\n    }\n    \n    if (!schema || typeof schema !== 'object') {\n      result.isValid = false;\n      errors.push('Schema must be an object');\n      return result;\n    }\n    \n    // Check required fields\n    if (schema.required && Array.isArray(schema.required)) {\n      for (const field of schema.required) {\n        if (input[field] === undefined) {\n          result.isValid = false;\n          errors.push(`Required field '${field}' is missing`);\n        }\n      }\n    }\n    \n    // Check field types and constraints\n    if (schema.properties && typeof schema.properties === 'object') {\n      for (const [field, fieldSchema] of Object.entries(schema.properties)) {\n        if (input[field] !== undefined) {\n          // Check type\n          if (fieldSchema.type) {\n            const type = Array.isArray(input[field]) ? 'array' : typeof input[field];\n            if (type !== fieldSchema.type) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must be of type '${fieldSchema.type}'`);\n            }\n          }\n          \n          // Check enum\n          if (fieldSchema.enum && Array.isArray(fieldSchema.enum)) {\n            if (!fieldSchema.enum.includes(input[field])) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must be one of: ${fieldSchema.enum.join(', ')}`);\n            }\n          }\n          \n          // Check pattern\n          if (fieldSchema.pattern && typeof input[field] === 'string') {\n            const pattern = new RegExp(fieldSchema.pattern);\n            if (!pattern.test(input[field])) {\n              result.isValid = false;\n              errors.push(`Field '${field}' does not match pattern '${fieldSchema.pattern}'`);\n            }\n          }\n          \n          // Check min/max for numbers\n          if (typeof input[field] === 'number') {\n            if (fieldSchema.minimum !== undefined && input[field] < fieldSchema.minimum) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must be greater than or equal to ${fieldSchema.minimum}`);\n            }\n            \n            if (fieldSchema.maximum !== undefined && input[field] > fieldSchema.maximum) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must be less than or equal to ${fieldSchema.maximum}`);\n            }\n          }\n          \n          // Check minLength/maxLength for strings\n          if (typeof input[field] === 'string') {\n            if (fieldSchema.minLength !== undefined && input[field].length < fieldSchema.minLength) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must be at least ${fieldSchema.minLength} characters long`);\n            }\n            \n            if (fieldSchema.maxLength !== undefined && input[field].length > fieldSchema.maxLength) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must be at most ${fieldSchema.maxLength} characters long`);\n            }\n          }\n          \n          // Check minItems/maxItems for arrays\n          if (Array.isArray(input[field])) {\n            if (fieldSchema.minItems !== undefined && input[field].length < fieldSchema.minItems) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must have at least ${fieldSchema.minItems} items`);\n            }\n            \n            if (fieldSchema.maxItems !== undefined && input[field].length > fieldSchema.maxItems) {\n              result.isValid = false;\n              errors.push(`Field '${field}' must have at most ${fieldSchema.maxItems} items`);\n            }\n          }\n        }\n      }\n    }\n    \n    return result;\n  }\n}\n\nmodule.exports = InputValidator;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,cAAc,CAAC;EACnB;AACF;AACA;AACA;AACA;EACE,OAAOC,SAASA,CAACC,KAAK,EAAE;IACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMC,WAAW,GAAG,CAClB,+EAA+E,EAC/E,yDAAyD,EACzD,IAAI,EACJ,KAAK,EACL,YAAY,CACb;IAED,OAAO,CAACA,WAAW,CAACC,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC;EAC1D;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOK,SAASA,CAACL,KAAK,EAAE;IACtB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI;IACb;IAEA,OAAO,CAAC,wDAAwD,CAACI,IAAI,CAACJ,KAAK,CAAC;EAC9E;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOM,aAAaA,CAACN,KAAK,EAAE;IAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMO,eAAe,GAAG,CACtB,iDAAiD,EACjD,QAAQ,EACR,UAAU,EACV,iBAAiB,EACjB,iBAAiB,CAClB;IAED,OAAO,CAACA,eAAe,CAACL,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,CAACJ,KAAK,CAAC,CAAC;EAC9D;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOQ,YAAYA,CAACR,KAAK,EAAE;IACzB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IAEA,OAAOA,KAAK,CACTS,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;EAC7B;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOC,WAAWA,CAACV,KAAK,EAAE;IACxB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IAEA,OAAOA,KAAK,CACTS,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CACnBA,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;EACE,OAAOE,eAAeA,CAACX,KAAK,EAAE;IAC5B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAOA,KAAK;IACd;IAEA,OAAOA,KAAK,CACTS,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,OAAOG,cAAcA,CAACZ,KAAK,EAAEa,MAAM,EAAE;IACnC,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAMC,MAAM,GAAG;MAAEC,OAAO,EAAE,IAAI;MAAEF;IAAO,CAAC;IAExC,IAAI,CAACd,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvCe,MAAM,CAACC,OAAO,GAAG,KAAK;MACtBF,MAAM,CAACG,IAAI,CAAC,yBAAyB,CAAC;MACtC,OAAOF,MAAM;IACf;IAEA,IAAI,CAACF,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MACzCE,MAAM,CAACC,OAAO,GAAG,KAAK;MACtBF,MAAM,CAACG,IAAI,CAAC,0BAA0B,CAAC;MACvC,OAAOF,MAAM;IACf;;IAEA;IACA,IAAIF,MAAM,CAACK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACP,MAAM,CAACK,QAAQ,CAAC,EAAE;MACrD,KAAK,MAAMG,KAAK,IAAIR,MAAM,CAACK,QAAQ,EAAE;QACnC,IAAIlB,KAAK,CAACqB,KAAK,CAAC,KAAKC,SAAS,EAAE;UAC9BP,MAAM,CAACC,OAAO,GAAG,KAAK;UACtBF,MAAM,CAACG,IAAI,CAAC,mBAAmBI,KAAK,cAAc,CAAC;QACrD;MACF;IACF;;IAEA;IACA,IAAIR,MAAM,CAACU,UAAU,IAAI,OAAOV,MAAM,CAACU,UAAU,KAAK,QAAQ,EAAE;MAC9D,KAAK,MAAM,CAACF,KAAK,EAAEG,WAAW,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACb,MAAM,CAACU,UAAU,CAAC,EAAE;QACpE,IAAIvB,KAAK,CAACqB,KAAK,CAAC,KAAKC,SAAS,EAAE;UAC9B;UACA,IAAIE,WAAW,CAACG,IAAI,EAAE;YACpB,MAAMA,IAAI,GAAGR,KAAK,CAACC,OAAO,CAACpB,KAAK,CAACqB,KAAK,CAAC,CAAC,GAAG,OAAO,GAAG,OAAOrB,KAAK,CAACqB,KAAK,CAAC;YACxE,IAAIM,IAAI,KAAKH,WAAW,CAACG,IAAI,EAAE;cAC7BZ,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,sBAAsBG,WAAW,CAACG,IAAI,GAAG,CAAC;YACvE;UACF;;UAEA;UACA,IAAIH,WAAW,CAACI,IAAI,IAAIT,KAAK,CAACC,OAAO,CAACI,WAAW,CAACI,IAAI,CAAC,EAAE;YACvD,IAAI,CAACJ,WAAW,CAACI,IAAI,CAACC,QAAQ,CAAC7B,KAAK,CAACqB,KAAK,CAAC,CAAC,EAAE;cAC5CN,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,qBAAqBG,WAAW,CAACI,IAAI,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAChF;UACF;;UAEA;UACA,IAAIN,WAAW,CAACrB,OAAO,IAAI,OAAOH,KAAK,CAACqB,KAAK,CAAC,KAAK,QAAQ,EAAE;YAC3D,MAAMlB,OAAO,GAAG,IAAI4B,MAAM,CAACP,WAAW,CAACrB,OAAO,CAAC;YAC/C,IAAI,CAACA,OAAO,CAACC,IAAI,CAACJ,KAAK,CAACqB,KAAK,CAAC,CAAC,EAAE;cAC/BN,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,6BAA6BG,WAAW,CAACrB,OAAO,GAAG,CAAC;YACjF;UACF;;UAEA;UACA,IAAI,OAAOH,KAAK,CAACqB,KAAK,CAAC,KAAK,QAAQ,EAAE;YACpC,IAAIG,WAAW,CAACQ,OAAO,KAAKV,SAAS,IAAItB,KAAK,CAACqB,KAAK,CAAC,GAAGG,WAAW,CAACQ,OAAO,EAAE;cAC3EjB,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,sCAAsCG,WAAW,CAACQ,OAAO,EAAE,CAAC;YACzF;YAEA,IAAIR,WAAW,CAACS,OAAO,KAAKX,SAAS,IAAItB,KAAK,CAACqB,KAAK,CAAC,GAAGG,WAAW,CAACS,OAAO,EAAE;cAC3ElB,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,mCAAmCG,WAAW,CAACS,OAAO,EAAE,CAAC;YACtF;UACF;;UAEA;UACA,IAAI,OAAOjC,KAAK,CAACqB,KAAK,CAAC,KAAK,QAAQ,EAAE;YACpC,IAAIG,WAAW,CAACU,SAAS,KAAKZ,SAAS,IAAItB,KAAK,CAACqB,KAAK,CAAC,CAACc,MAAM,GAAGX,WAAW,CAACU,SAAS,EAAE;cACtFnB,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,sBAAsBG,WAAW,CAACU,SAAS,kBAAkB,CAAC;YAC3F;YAEA,IAAIV,WAAW,CAACY,SAAS,KAAKd,SAAS,IAAItB,KAAK,CAACqB,KAAK,CAAC,CAACc,MAAM,GAAGX,WAAW,CAACY,SAAS,EAAE;cACtFrB,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,qBAAqBG,WAAW,CAACY,SAAS,kBAAkB,CAAC;YAC1F;UACF;;UAEA;UACA,IAAIjB,KAAK,CAACC,OAAO,CAACpB,KAAK,CAACqB,KAAK,CAAC,CAAC,EAAE;YAC/B,IAAIG,WAAW,CAACa,QAAQ,KAAKf,SAAS,IAAItB,KAAK,CAACqB,KAAK,CAAC,CAACc,MAAM,GAAGX,WAAW,CAACa,QAAQ,EAAE;cACpFtB,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,wBAAwBG,WAAW,CAACa,QAAQ,QAAQ,CAAC;YAClF;YAEA,IAAIb,WAAW,CAACc,QAAQ,KAAKhB,SAAS,IAAItB,KAAK,CAACqB,KAAK,CAAC,CAACc,MAAM,GAAGX,WAAW,CAACc,QAAQ,EAAE;cACpFvB,MAAM,CAACC,OAAO,GAAG,KAAK;cACtBF,MAAM,CAACG,IAAI,CAAC,UAAUI,KAAK,uBAAuBG,WAAW,CAACc,QAAQ,QAAQ,CAAC;YACjF;UACF;QACF;MACF;IACF;IAEA,OAAOvB,MAAM;EACf;AACF;AAEAwB,MAAM,CAACC,OAAO,GAAG1C,cAAc", "ignoreList": []}