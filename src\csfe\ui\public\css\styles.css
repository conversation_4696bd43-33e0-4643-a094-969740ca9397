/* CSFE NovaVision Dashboard Styles */

:root {
  --primary-color: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --white-color: #ffffff;
  --black-color: #000000;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 8px;
  margin-bottom: 20px;
}

.card-header {
  border-radius: 8px 8px 0 0 !important;
  font-weight: 500;
}

.csfe-value-container {
  padding: 20px;
  background-color: var(--gray-100);
  border-radius: 8px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

.csfe-value {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 10px 0;
}

.csfe-performance {
  font-size: 1.2rem;
  color: var(--success-color);
  margin-bottom: 5px;
}

.csfe-timestamp {
  font-size: 0.9rem;
  color: var(--gray-600);
}

.progress {
  height: 25px;
  border-radius: 5px;
  margin-bottom: 15px;
}

.progress-bar {
  font-weight: 600;
  font-size: 0.9rem;
}

.nav-tabs .nav-link {
  color: var(--gray-700);
  font-weight: 500;
}

.nav-tabs .nav-link.active {
  color: var(--primary-color);
  font-weight: 600;
}

.form-label {
  font-weight: 500;
  margin-bottom: 5px;
}

.form-control, .form-select {
  border-radius: 5px;
  border: 1px solid var(--gray-400);
  padding: 8px 12px;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
}

.btn {
  border-radius: 5px;
  padding: 8px 16px;
  font-weight: 500;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #004494;
  border-color: #004494;
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.footer {
  background-color: var(--gray-200) !important;
  border-top: 1px solid var(--gray-300);
}

/* Market Direction Styles */
.market-direction {
  margin-bottom: 20px;
}

.market-direction h4 {
  margin-bottom: 10px;
}

/* Risk Factors Styles */
.risk-factor {
  margin-bottom: 15px;
}

.risk-factor-name {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.risk-factor-name span {
  font-weight: 500;
}

.risk-factor-impact {
  font-size: 0.9rem;
  color: var(--gray-600);
}

.risk-factor-impact.high {
  color: var(--danger-color);
}

.risk-factor-impact.medium {
  color: var(--warning-color);
}

.risk-factor-impact.low {
  color: var(--success-color);
}

/* Asset Allocation Styles */
#asset-allocation-chart {
  max-height: 300px;
}

/* Timeline Predictions Styles */
#timeline-predictions-chart {
  max-height: 300px;
}

/* Depression Prediction Styles */
#depression-timeline-chart {
  max-height: 200px;
}

#depression-indicators {
  max-height: 250px;
  overflow-y: auto;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .csfe-value {
    font-size: 2.5rem;
  }
  
  .csfe-performance {
    font-size: 1rem;
  }
  
  .card-body {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .csfe-value {
    font-size: 2rem;
  }
  
  .csfe-performance {
    font-size: 0.9rem;
  }
  
  .card-body {
    padding: 10px;
  }
}
