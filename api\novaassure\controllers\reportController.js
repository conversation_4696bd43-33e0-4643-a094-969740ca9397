/**
 * Report Controller
 * 
 * This controller handles API requests for report management.
 */

const reportService = require('../services/reportService');
const logger = require('../utils/logger');

/**
 * Get all reports
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getAllReports(req, res, next) {
  try {
    const { type, framework, search, page = 1, limit = 10 } = req.query;
    
    const filters = {};
    
    if (type) {
      filters.type = type;
    }
    
    if (framework) {
      filters.framework = framework;
    }
    
    if (search) {
      filters.search = search;
    }
    
    const result = await reportService.getAllReports(
      filters,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error getting reports', error);
    next(error);
  }
}

/**
 * Get report by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getReportById(req, res, next) {
  try {
    const { id } = req.params;
    
    const report = await reportService.getReportById(id);
    
    res.json(report);
  } catch (error) {
    logger.error(`Error getting report ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Generate compliance report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function generateComplianceReport(req, res, next) {
  try {
    const reportData = req.body;
    const userId = req.user.id;
    
    const report = await reportService.generateComplianceReport(reportData, userId);
    
    res.status(201).json(report);
  } catch (error) {
    logger.error('Error generating compliance report', error);
    next(error);
  }
}

/**
 * Generate test results report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function generateTestResultsReport(req, res, next) {
  try {
    const reportData = req.body;
    const userId = req.user.id;
    
    const report = await reportService.generateTestResultsReport(reportData, userId);
    
    res.status(201).json(report);
  } catch (error) {
    logger.error('Error generating test results report', error);
    next(error);
  }
}

/**
 * Generate evidence report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function generateEvidenceReport(req, res, next) {
  try {
    const reportData = req.body;
    const userId = req.user.id;
    
    const report = await reportService.generateEvidenceReport(reportData, userId);
    
    res.status(201).json(report);
  } catch (error) {
    logger.error('Error generating evidence report', error);
    next(error);
  }
}

/**
 * Generate control effectiveness report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function generateControlEffectivenessReport(req, res, next) {
  try {
    const reportData = req.body;
    const userId = req.user.id;
    
    const report = await reportService.generateControlEffectivenessReport(reportData, userId);
    
    res.status(201).json(report);
  } catch (error) {
    logger.error('Error generating control effectiveness report', error);
    next(error);
  }
}

/**
 * Download report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function downloadReport(req, res, next) {
  try {
    const { id } = req.params;
    
    const fileInfo = await reportService.downloadReport(id);
    
    res.download(fileInfo.filePath, fileInfo.fileName, {
      headers: {
        'Content-Type': fileInfo.fileType
      }
    });
  } catch (error) {
    logger.error(`Error downloading report ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Share report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function shareReport(req, res, next) {
  try {
    const { id } = req.params;
    const { recipients, message, expirationDate } = req.body;
    
    const result = await reportService.shareReport(
      id,
      recipients,
      message,
      expirationDate
    );
    
    res.json(result);
  } catch (error) {
    logger.error(`Error sharing report ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Schedule report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function scheduleReport(req, res, next) {
  try {
    const { id } = req.params;
    const { schedule, recipients } = req.body;
    
    const report = await reportService.scheduleReport(
      id,
      schedule,
      recipients
    );
    
    res.json(report);
  } catch (error) {
    logger.error(`Error scheduling report ${req.params.id}`, error);
    next(error);
  }
}

module.exports = {
  getAllReports,
  getReportById,
  generateComplianceReport,
  generateTestResultsReport,
  generateEvidenceReport,
  generateControlEffectivenessReport,
  downloadReport,
  shareReport,
  scheduleReport
};
