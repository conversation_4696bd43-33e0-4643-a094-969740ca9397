const express = require('express');
const router = express.Router();
const { validateConnectorId } = require('../middleware/validators');
const { authenticateJWT, authorizePartner } = require('../middleware/auth');

// Import controllers
const connectorsController = require('../controllers/connectorsController');

/**
 * @route GET /api/connectors
 * @desc Get all connectors with optional filtering
 * @access Public
 */
router.get('/', connectorsController.getAllConnectors);

/**
 * @route GET /api/connectors/:id
 * @desc Get a single connector by ID
 * @access Public
 */
router.get('/:id', validateConnectorId, connectorsController.getConnectorById);

/**
 * @route POST /api/connectors
 * @desc Create a new connector (partners only)
 * @access Private (Partner)
 */
router.post('/', authenticateJWT, authorizePartner, connectorsController.createConnector);

/**
 * @route PUT /api/connectors/:id
 * @desc Update a connector (owner only)
 * @access Private (Owner)
 */
router.put('/:id', authenticateJWT, validateConnectorId, connectorsController.updateConnector);

/**
 * @route DELETE /api/connectors/:id
 * @desc Delete a connector (owner or admin only)
 * @access Private (Owner/Admin)
 */
router.delete('/:id', authenticateJWT, validateConnectorId, connectorsController.deleteConnector);

/**
 * @route GET /api/connectors/category/:category
 * @desc Get connectors by category
 * @access Public
 */
router.get('/category/:category', connectorsController.getConnectorsByCategory);

/**
 * @route GET /api/connectors/framework/:framework
 * @desc Get connectors by framework
 * @access Public
 */
router.get('/framework/:framework', connectorsController.getConnectorsByFramework);

/**
 * @route POST /api/connectors/:id/install
 * @desc Install a connector for the authenticated user
 * @access Private
 */
router.post('/:id/install', authenticateJWT, validateConnectorId, connectorsController.installConnector);

/**
 * @route POST /api/connectors/:id/uninstall
 * @desc Uninstall a connector for the authenticated user
 * @access Private
 */
router.post('/:id/uninstall', authenticateJWT, validateConnectorId, connectorsController.uninstallConnector);

/**
 * @route GET /api/connectors/:id/reviews
 * @desc Get reviews for a connector
 * @access Public
 */
router.get('/:id/reviews', validateConnectorId, connectorsController.getConnectorReviews);

/**
 * @route POST /api/connectors/:id/reviews
 * @desc Add a review for a connector
 * @access Private
 */
router.post('/:id/reviews', authenticateJWT, validateConnectorId, connectorsController.addConnectorReview);

module.exports = router;
