<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .card {
            background-color: #2d2d2d;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        .button {
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .button:hover {
            background-color: #2563eb;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #22c55e; }
        .error { background-color: #ef4444; }
        .info { background-color: #3b82f6; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        #output {
            background-color: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 NovaFuse Simple Test Dashboard</h1>
            <p>Basic functionality test - No external dependencies</p>
            <div id="connectionStatus" class="status info">Checking connection...</div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🔧 Basic Tests</h3>
                <button class="button" onclick="testBasicFunction()">Test Basic Function</button>
                <button class="button" onclick="testAPIConnection()">Test API Connection</button>
                <button class="button" onclick="testWebSocket()">Test WebSocket</button>
                <div id="basicTestResults"></div>
            </div>

            <div class="card">
                <h3>📊 Mock Test Execution</h3>
                <button class="button" onclick="runMockTest()">Run Mock Test</button>
                <button class="button" onclick="runAllMockTests()">Run All Mock Tests</button>
                <div id="testProgress"></div>
                <div id="testResults"></div>
            </div>

            <div class="card">
                <h3>🚀 Mock Deployment</h3>
                <button class="button" onclick="checkDeploymentStatus()">Check Status</button>
                <button class="button" onclick="deployService()">Deploy Service</button>
                <div id="deploymentResults"></div>
            </div>

            <div class="card">
                <h3>🎮 Mock Demo</h3>
                <button class="button" onclick="launchDemo()">Launch Demo</button>
                <button class="button" onclick="stopDemo()">Stop Demo</button>
                <div id="demoResults"></div>
            </div>
        </div>

        <div class="card">
            <h3>📝 Output Log</h3>
            <button class="button" onclick="clearOutput()">Clear Log</button>
            <div id="output">Dashboard loaded successfully!
Ready for testing...
</div>
        </div>
    </div>

    <script>
        let testCount = 0;
        let socket = null;

        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            container.appendChild(div);
            
            // Remove after 5 seconds
            setTimeout(() => {
                if (div.parentNode) {
                    div.parentNode.removeChild(div);
                }
            }, 5000);
        }

        function testBasicFunction() {
            testCount++;
            log(`Basic function test #${testCount} - SUCCESS`);
            showResult('basicTestResults', `✅ Basic function working! Test #${testCount}`, 'success');
        }

        async function testAPIConnection() {
            try {
                log('Testing API connection...');
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.success) {
                    log('✅ API connection successful');
                    showResult('basicTestResults', '✅ API Connection: SUCCESS', 'success');
                    document.getElementById('connectionStatus').textContent = 'Connected to API';
                    document.getElementById('connectionStatus').className = 'status success';
                } else {
                    log('❌ API connection failed');
                    showResult('basicTestResults', '❌ API Connection: FAILED', 'error');
                }
            } catch (error) {
                log(`❌ API Error: ${error.message}`);
                showResult('basicTestResults', `❌ API Error: ${error.message}`, 'error');
            }
        }

        function testWebSocket() {
            try {
                log('Testing WebSocket connection...');
                
                // Check if Socket.IO is available
                if (typeof io === 'undefined') {
                    log('❌ Socket.IO not loaded, using mock WebSocket');
                    showResult('basicTestResults', '⚠️ WebSocket: Using Mock', 'info');
                    return;
                }
                
                socket = io();
                
                socket.on('connect', () => {
                    log('✅ WebSocket connected');
                    showResult('basicTestResults', '✅ WebSocket: CONNECTED', 'success');
                });
                
                socket.on('disconnect', () => {
                    log('❌ WebSocket disconnected');
                    showResult('basicTestResults', '❌ WebSocket: DISCONNECTED', 'error');
                });
                
                socket.on('connect_error', (error) => {
                    log(`❌ WebSocket Error: ${error.message}`);
                    showResult('basicTestResults', `❌ WebSocket Error: ${error.message}`, 'error');
                });
                
            } catch (error) {
                log(`❌ WebSocket Error: ${error.message}`);
                showResult('basicTestResults', `❌ WebSocket Error: ${error.message}`, 'error');
            }
        }

        function runMockTest() {
            log('Starting mock test execution...');
            showResult('testResults', '🔄 Running mock test...', 'info');
            
            // Simulate test progress
            let progress = 0;
            const progressDiv = document.getElementById('testProgress');
            progressDiv.innerHTML = '<div style="background: #333; border-radius: 5px; overflow: hidden;"><div id="progressBar" style="background: #3b82f6; height: 20px; width: 0%; transition: width 0.3s;"></div></div>';
            
            const interval = setInterval(() => {
                progress += 10;
                document.getElementById('progressBar').style.width = progress + '%';
                log(`Test progress: ${progress}%`);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    const success = Math.random() > 0.3;
                    if (success) {
                        log('✅ Mock test completed successfully');
                        showResult('testResults', '✅ Mock Test: PASSED', 'success');
                    } else {
                        log('❌ Mock test failed');
                        showResult('testResults', '❌ Mock Test: FAILED', 'error');
                    }
                    progressDiv.innerHTML = '';
                }
            }, 200);
        }

        function runAllMockTests() {
            log('Starting all mock tests...');
            const tests = ['UUFT Tests', 'Consciousness Tests', 'Financial Tests', 'Medical Tests'];
            let currentTest = 0;
            
            function runNextTest() {
                if (currentTest < tests.length) {
                    const testName = tests[currentTest];
                    log(`Running ${testName}...`);
                    showResult('testResults', `🔄 Running ${testName}...`, 'info');
                    
                    setTimeout(() => {
                        const success = Math.random() > 0.2;
                        if (success) {
                            log(`✅ ${testName} - PASSED`);
                            showResult('testResults', `✅ ${testName}: PASSED`, 'success');
                        } else {
                            log(`❌ ${testName} - FAILED`);
                            showResult('testResults', `❌ ${testName}: FAILED`, 'error');
                        }
                        currentTest++;
                        runNextTest();
                    }, 1000);
                } else {
                    log('✅ All mock tests completed');
                    showResult('testResults', '✅ All Tests: COMPLETED', 'success');
                }
            }
            
            runNextTest();
        }

        async function checkDeploymentStatus() {
            try {
                log('Checking deployment status...');
                const response = await fetch('/api/deployment/status');
                const data = await response.json();
                
                if (data.success) {
                    log(`✅ Deployment status: ${data.data.healthyServices}/${data.data.totalServices} services healthy`);
                    showResult('deploymentResults', `✅ ${data.data.healthyServices}/${data.data.totalServices} services healthy`, 'success');
                } else {
                    log('❌ Failed to get deployment status');
                    showResult('deploymentResults', '❌ Status check failed', 'error');
                }
            } catch (error) {
                log(`❌ Deployment status error: ${error.message}`);
                showResult('deploymentResults', `❌ Error: ${error.message}`, 'error');
            }
        }

        function deployService() {
            log('Deploying mock service...');
            showResult('deploymentResults', '🔄 Deploying service...', 'info');
            
            setTimeout(() => {
                const success = Math.random() > 0.1;
                if (success) {
                    log('✅ Service deployed successfully');
                    showResult('deploymentResults', '✅ Service: DEPLOYED', 'success');
                } else {
                    log('❌ Service deployment failed');
                    showResult('deploymentResults', '❌ Deployment: FAILED', 'error');
                }
            }, 2000);
        }

        function launchDemo() {
            log('Launching mock demo...');
            showResult('demoResults', '🔄 Launching demo...', 'info');
            
            setTimeout(() => {
                log('✅ Demo launched successfully');
                showResult('demoResults', '✅ Demo: RUNNING', 'success');
                
                setTimeout(() => {
                    log('✅ Demo completed');
                    showResult('demoResults', '✅ Demo: COMPLETED', 'success');
                }, 3000);
            }, 1000);
        }

        function stopDemo() {
            log('Stopping demo...');
            showResult('demoResults', '🛑 Demo: STOPPED', 'info');
        }

        function clearOutput() {
            document.getElementById('output').textContent = 'Output cleared.\n';
        }

        // Auto-test on load
        window.onload = function() {
            log('Dashboard initialized successfully!');
            setTimeout(testAPIConnection, 1000);
        };
    </script>
</body>
</html>
