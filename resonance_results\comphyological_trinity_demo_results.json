{"firstLaw": {"resonantValues": [0.03, 0.06, 0.09, 0.12, 0.3, 0.6, 0.9, 3, 6, 9, 12], "resonantResults": [{"value": 0.03, "result": 0.03, "valid": true}, {"value": 0.06, "result": 0.06, "valid": true}, {"value": 0.09, "result": 0.09, "valid": true}, {"value": 0.12, "result": 0.12, "valid": true}, {"value": 0.3, "result": 0.3, "valid": true}, {"value": 0.6, "result": 0.6, "valid": true}, {"value": 0.9, "result": 0.9, "valid": true}, {"value": 3, "result": 3, "valid": true}, {"value": 6, "result": 6, "valid": true}, {"value": 9, "result": 9, "valid": true}, {"value": 12, "result": 12, "valid": true}], "nonResonantValues": [0.1, 0.2, 0.4, 0.5, 0.7, 0.8, 1, 2, 4, 5, 7, 8, 10], "nonResonantResults": [{"value": 0.1, "result": 0.12, "valid": true}, {"value": 0.2, "result": 0.13, "valid": true}, {"value": 0.4, "result": 0.3, "valid": true}, {"value": 0.5, "result": 0.6, "valid": true}, {"value": 0.7, "result": 0.6, "valid": true}, {"value": 0.8, "result": 0.9, "valid": true}, {"value": 1, "result": 0.9, "valid": true}, {"value": 2, "result": 3, "valid": true}, {"value": 4, "result": 3, "valid": true}, {"value": 5, "result": 6, "valid": true}, {"value": 7, "result": 6, "valid": true}, {"value": 8, "result": 9, "valid": true}, {"value": 10, "result": 9, "valid": true}]}, "secondLaw": {"testValues": [0.1, 0.2, 0.4, 0.5, 0.7, 0.8, 1, 2, 4, 5, 7, 8, 10], "optimizationResults": [{"value": 0.1, "result": 0.12, "optimized": true}, {"value": 0.2, "result": 0.13, "optimized": true}, {"value": 0.4, "result": 0.3, "optimized": true}, {"value": 0.5, "result": 0.6, "optimized": true}, {"value": 0.7, "result": 0.6, "optimized": true}, {"value": 0.8, "result": 0.9, "optimized": true}, {"value": 1, "result": 0.9, "optimized": true}, {"value": 2, "result": 3, "optimized": true}, {"value": 4, "result": 3, "optimized": true}, {"value": 5, "result": 6, "optimized": true}, {"value": 7, "result": 6, "optimized": true}, {"value": 8, "result": 9, "optimized": true}, {"value": 10, "result": 9, "optimized": true}]}, "thirdLaw": {"testValues": [0.03, 0.06, 0.09, 0.3, 0.6, 0.9], "domains": ["cyber", "financial", "medical"], "translationResults": [{"value": 0.03, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.03, "valid": true}, {"value": 0.03, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.03, "valid": true}, {"value": 0.03, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.03, "valid": true}, {"value": 0.03, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.03, "valid": true}, {"value": 0.03, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.03, "valid": true}, {"value": 0.03, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.03, "valid": true}, {"value": 0.06, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.06, "valid": true}, {"value": 0.06, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.06, "valid": true}, {"value": 0.06, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.06, "valid": true}, {"value": 0.06, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.06, "valid": true}, {"value": 0.06, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.06, "valid": true}, {"value": 0.06, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.06, "valid": true}, {"value": 0.09, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.09, "valid": true}, {"value": 0.09, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.09, "valid": true}, {"value": 0.09, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.09, "valid": true}, {"value": 0.09, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.09, "valid": true}, {"value": 0.09, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.09, "valid": true}, {"value": 0.09, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.09, "valid": true}, {"value": 0.3, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.3, "valid": true}, {"value": 0.6, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.9, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.9, "valid": true}]}, "completeTrinity": {"testValues": [0.1, 0.3, 0.5, 0.6, 0.7, 0.9], "domains": ["cyber", "financial", "medical"], "trinityResults": [{"value": 0.1, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.12, "valid": true}, {"value": 0.1, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.12, "valid": true}, {"value": 0.1, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.12, "valid": true}, {"value": 0.1, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.12, "valid": true}, {"value": 0.1, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.12, "valid": true}, {"value": 0.1, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.12, "valid": true}, {"value": 0.3, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.3, "valid": true}, {"value": 0.3, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.3, "valid": true}, {"value": 0.5, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.5, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.5, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.5, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.5, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.5, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.6, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.7, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.7, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.7, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.7, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.6, "valid": true}, {"value": 0.7, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.6, "valid": true}, {"value": 0.7, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.6, "valid": true}, {"value": 0.9, "sourceDomain": "cyber", "targetDomain": "financial", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "cyber", "targetDomain": "medical", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "financial", "targetDomain": "cyber", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "financial", "targetDomain": "medical", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "medical", "targetDomain": "cyber", "result": 0.9, "valid": true}, {"value": 0.9, "sourceDomain": "medical", "targetDomain": "financial", "result": 0.9, "valid": true}]}, "metrics": {"trinity": {"operations": 110, "firstLawEnforcements": 113, "firstLawViolations": 0, "secondLawOptimizations": 110, "thirdLawTranslations": 72, "fullTrinityApplications": 72}, "firstLaw": {}, "secondLaw": {"transitions": 115, "energySaved": 10.733210150967118, "totalEnergy": 25.274058009946717, "averageEnergySaved": 0.09333226218232277, "selfSimilarityScore": 0.9312714129760185, "resonanceImprovements": 49, "resonanceDegradations": 0, "optimalTransitions": 39}, "thirdLaw": {"translations": 72, "resonancePreserved": 72, "resonanceViolations": 0, "crossDomainFidelity": 1, "domainPairs": {"cyber->financial": {"translations": 12, "resonancePreserved": 12, "fidelity": 1}, "cyber->medical": {"translations": 12, "resonancePreserved": 12, "fidelity": 1}, "financial->cyber": {"translations": 12, "resonancePreserved": 12, "fidelity": 1}, "financial->medical": {"translations": 12, "resonancePreserved": 12, "fidelity": 1}, "medical->cyber": {"translations": 12, "resonancePreserved": 12, "fidelity": 1}, "medical->financial": {"translations": 12, "resonancePreserved": 12, "fidelity": 1}}}}}