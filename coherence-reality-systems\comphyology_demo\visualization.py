import matplotlib.pyplot as plt

def show_side_by_side(gpt_response, nepi_response):
    """Visualize GPT vs NEPI responses side-by-side"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    ax1.text(0.5, 0.5, gpt_response, ha='center', va='center', fontsize=12)
    ax1.set_title('GPT Response', color='red' if 'harm' in gpt_response else 'green')
    ax1.axis('off')
    
    ax2.text(0.5, 0.5, nepi_response, ha='center', va='center', fontsize=12)
    ax2.set_title('NEPI Response', color='red' if '∂Ψ=0' in nepi_response else 'green')
    ax2.axis('off')
    
    plt.tight_layout()
    plt.show()

# New function for Stage 2
def plot_utility(utility_values, kappa):
    """Plot utility curve with boundary enforcement"""
    plt.figure(figsize=(10, 6))
    plt.plot(utility_values, 'b-', linewidth=2, label='Utility')
    plt.axhline(y=kappa, color='r', linestyle='--', label=f'∂Ψ=0 Boundary (κ={kappa})')
    
    # Mark enforcement point
    for i, util in enumerate(utility_values):
        if util > kappa:
            plt.plot(i, util, 'ro', markersize=10)
            plt.text(i, util, ' Boundary Enforced', verticalalignment='bottom')
            break
    
    plt.xlabel('Time Steps')
    plt.ylabel('Utility')
    plt.title('Utility Rise with ∂Ψ=0 Boundary Enforcement')
    plt.legend()
    plt.grid(True)
    plt.show()
