"""
Template Manager for the Universal Compliance Visualization Framework.

This module provides functionality for managing visualization templates.
"""

import os
import logging
import importlib
from typing import Dict, List, Any, Optional, Type

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VisualizationTemplate:
    """Base class for visualization templates."""

    def __init__(self, name: str, description: str):
        """
        Initialize the visualization template.

        Args:
            name: The name of the template
            description: A description of the template
        """
        self.name = name
        self.description = description

    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided data.

        Args:
            data: The data to visualize
            context: Additional context for the visualization

        Returns:
            The visualization data

        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement the apply method")


class TemplateManager:
    """
    Manager for visualization templates.

    This class is responsible for loading, managing, and providing access to
    visualization templates for different stakeholder roles.
    """

    def __init__(self, templates_dir: Optional[str] = None):
        """
        Initialize the Template Manager.

        Args:
            templates_dir: Path to a directory containing custom templates
        """
        logger.info("Initializing Template Manager")

        # Initialize the templates dictionary
        # Structure: {role: {template_name: template_instance}}
        self.templates: Dict[str, Dict[str, VisualizationTemplate]] = {}

        # Load default templates
        self._load_default_templates()

        # Load custom templates if provided
        if templates_dir and os.path.exists(templates_dir):
            self._load_templates_from_directory(templates_dir)

        logger.info(f"Template Manager initialized with templates for {len(self.templates)} roles")

    def _load_default_templates(self) -> None:
        """Load the default visualization templates."""
        # Import default templates
        from ..templates.dashboard_templates import (
            BoardDashboardTemplate,
            CISODashboardTemplate,
            ComplianceManagerDashboardTemplate,
            ITManagerDashboardTemplate,
            AuditorDashboardTemplate
        )

        from ..templates.report_templates import (
            BoardReportTemplate,
            CISOReportTemplate,
            ComplianceManagerReportTemplate,
            ITManagerReportTemplate,
            AuditorReportTemplate
        )

        # Import test result templates
        from ..templates.test_result_templates import (
            TestResultDashboardTemplate,
            TestResultReportTemplate
        )

        # Register board templates
        self.templates['board'] = {
            'dashboard': BoardDashboardTemplate(),
            'report': BoardReportTemplate()
        }

        # Register CISO templates
        self.templates['ciso'] = {
            'dashboard': CISODashboardTemplate(),
            'report': CISOReportTemplate()
        }

        # Register Compliance Manager templates
        self.templates['compliance_manager'] = {
            'dashboard': ComplianceManagerDashboardTemplate(),
            'report': ComplianceManagerReportTemplate()
        }

        # Register IT Manager templates
        self.templates['it_manager'] = {
            'dashboard': ITManagerDashboardTemplate(),
            'report': ITManagerReportTemplate()
        }

        # Register Auditor templates
        self.templates['auditor'] = {
            'dashboard': AuditorDashboardTemplate(),
            'report': AuditorReportTemplate()
        }

        # Register Test Result templates
        self.templates['test_result'] = {
            'dashboard': TestResultDashboardTemplate(),
            'report': TestResultReportTemplate()
        }

    def _load_templates_from_directory(self, directory: str) -> None:
        """
        Load templates from a directory.

        Args:
            directory: Path to the directory containing template modules
        """
        try:
            # Get all Python files in the directory
            template_files = [f for f in os.listdir(directory) if f.endswith('.py') and not f.startswith('__')]

            for template_file in template_files:
                try:
                    # Import the module
                    module_name = template_file[:-3]  # Remove .py extension
                    module_path = os.path.join(directory, template_file)
                    spec = importlib.util.spec_from_file_location(module_name, module_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # Find all template classes in the module
                    for attr_name in dir(module):
                        attr = getattr(module, attr_name)
                        if (isinstance(attr, type) and
                            issubclass(attr, VisualizationTemplate) and
                            attr is not VisualizationTemplate):

                            # Create an instance of the template
                            template = attr()

                            # Extract role and template name from the class name
                            # Assuming naming convention like RoleNameTemplateType
                            class_name = attr.__name__
                            if 'Template' in class_name:
                                parts = class_name.split('Template')[0]

                                # Try to determine role and template type
                                if 'Board' in parts:
                                    role = 'board'
                                elif 'CISO' in parts:
                                    role = 'ciso'
                                elif 'Compliance' in parts:
                                    role = 'compliance_manager'
                                elif 'IT' in parts:
                                    role = 'it_manager'
                                elif 'Auditor' in parts:
                                    role = 'auditor'
                                else:
                                    # Skip if we can't determine the role
                                    continue

                                if 'Dashboard' in parts:
                                    template_type = 'dashboard'
                                elif 'Report' in parts:
                                    template_type = 'report'
                                else:
                                    # Skip if we can't determine the template type
                                    continue

                                # Register the template
                                if role not in self.templates:
                                    self.templates[role] = {}

                                self.templates[role][template_type] = template
                                logger.info(f"Loaded template {class_name} for role {role}")

                except Exception as e:
                    logger.error(f"Failed to load template from {template_file}: {e}")

        except Exception as e:
            logger.error(f"Failed to load templates from directory {directory}: {e}")

    def get_template(self, role: str, template_type: str) -> VisualizationTemplate:
        """
        Get a template for a specific role and type.

        Args:
            role: The stakeholder role
            template_type: The type of template (e.g., 'dashboard', 'report')

        Returns:
            The template instance

        Raises:
            ValueError: If the template does not exist
        """
        if role not in self.templates:
            logger.warning(f"Role '{role}' not found, using default role 'compliance_manager'")
            role = 'compliance_manager'

        if template_type not in self.templates[role]:
            logger.warning(f"Template type '{template_type}' not found for role '{role}', using 'dashboard'")
            template_type = 'dashboard'

        return self.templates[role][template_type]

    def get_available_templates(self, role: str) -> List[str]:
        """
        Get the list of available templates for a specific role.

        Args:
            role: The stakeholder role

        Returns:
            A list of template types
        """
        if role not in self.templates:
            return []

        return list(self.templates[role].keys())

    def register_template(self, role: str, template_type: str, template: VisualizationTemplate) -> None:
        """
        Register a new template or update an existing one.

        Args:
            role: The stakeholder role
            template_type: The type of template
            template: The template instance
        """
        if role not in self.templates:
            self.templates[role] = {}

        self.templates[role][template_type] = template
        logger.info(f"Registered template '{template_type}' for role '{role}'")
