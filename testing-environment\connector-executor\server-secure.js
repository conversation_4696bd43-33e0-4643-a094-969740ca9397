/**
 * Secure Connector Executor for NovaConnect Universal API Connector
 * 
 * This service executes connector operations with enhanced security.
 */

const express = require('express');
const axios = require('axios');
const cors = require('cors');
const bodyParser = require('body-parser');
const jp = require('jsonpath');
const winston = require('winston');
const https = require('https');
const fs = require('fs');
const path = require('path');

// Import encryption utilities
const { encrypt, decrypt } = require('../utils/encryption');

const app = express();
const port = process.env.PORT || 3000;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Configuration
const registryApiUrl = process.env.REGISTRY_API_URL || 'http://localhost:3001';
const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:3002';
const usageMeteringUrl = process.env.USAGE_METERING_URL || 'http://localhost:3004';

// Create secure axios instance with TLS
const createSecureAxios = () => {
  // In production, this would use proper TLS certificates
  const httpsAgent = new https.Agent({
    rejectUnauthorized: process.env.NODE_ENV === 'production'
  });

  return axios.create({
    httpsAgent,
    headers: {
      'User-Agent': 'NovaConnect-Universal-API-Connector',
      'X-API-Version': '1.0'
    }
  });
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Execute connector endpoint with enhanced security
app.post('/execute/:connectorId/:endpointId', async (req, res) => {
  const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  logger.info(`Starting execution [${requestId}]`, { 
    connectorId: req.params.connectorId,
    endpointId: req.params.endpointId
  });
  
  try {
    const { connectorId, endpointId } = req.params;
    const { credentialId, parameters = {}, userId } = req.body;
    
    if (!credentialId) {
      return res.status(400).json({ error: 'credentialId is required' });
    }
    
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    // Get connector from registry
    logger.info(`Fetching connector [${requestId}]`);
    const connectorResponse = await axios.get(`${registryApiUrl}/connectors/${connectorId}`);
    const connector = connectorResponse.data;
    
    // Find the endpoint
    const endpoint = connector.endpoints.find(e => e.id === endpointId);
    if (!endpoint) {
      logger.error(`Endpoint not found [${requestId}]`, { endpointId });
      return res.status(404).json({ error: 'Endpoint not found' });
    }
    
    // Get credentials from auth service
    logger.info(`Fetching credentials [${requestId}]`);
    const credentialResponse = await axios.get(`${authServiceUrl}/credentials/${credentialId}/decrypt`);
    const { authType, credentials } = credentialResponse.data;
    
    // Validate auth type
    if (authType !== connector.authentication.type) {
      logger.error(`Auth type mismatch [${requestId}]`, { 
        expected: connector.authentication.type,
        actual: authType
      });
      return res.status(400).json({ 
        error: `Auth type mismatch. Expected ${connector.authentication.type}, got ${authType}` 
      });
    }
    
    // Build request URL
    const baseUrl = connector.configuration.baseUrl;
    let url = `${baseUrl}${endpoint.path}`;
    
    // Replace path parameters
    if (parameters.path) {
      Object.entries(parameters.path).forEach(([key, value]) => {
        url = url.replace(`{{${key}}}`, encodeURIComponent(value));
      });
    }
    
    // Build headers
    const headers = { ...connector.configuration.headers };
    
    // Add authentication headers
    switch (authType) {
      case 'API_KEY':
        headers[credentials.headerName || 'X-API-Key'] = credentials.apiKey;
        break;
      case 'BASIC':
        const basicAuth = Buffer.from(`${credentials.username}:${credentials.password}`).toString('base64');
        headers['Authorization'] = `Basic ${basicAuth}`;
        break;
      case 'BEARER':
      case 'JWT':
        headers['Authorization'] = `Bearer ${credentials.token}`;
        break;
      case 'OAUTH2':
        // For OAuth2, we would need to get a token first
        // This is simplified for the example
        headers['Authorization'] = `Bearer ${credentials.accessToken}`;
        break;
      case 'CUSTOM':
        // For custom auth, apply the custom headers
        Object.entries(credentials).forEach(([key, value]) => {
          if (key !== 'headerName') {
            headers[credentials.headerName || key] = value;
          }
        });
        break;
    }
    
    // Create secure axios instance
    const secureAxios = createSecureAxios();
    
    // Execute the request
    logger.info(`Executing request [${requestId}]`, { url, method: endpoint.method });
    
    // Log request details (excluding sensitive data)
    logger.info(`Request details [${requestId}]`, {
      url,
      method: endpoint.method,
      hasQueryParams: !!parameters.query,
      hasBodyParams: !!parameters.body,
      headerKeys: Object.keys(headers).filter(k => !k.toLowerCase().includes('auth'))
    });
    
    let response;
    switch (endpoint.method.toUpperCase()) {
      case 'GET':
        response = await secureAxios.get(url, { 
          headers, 
          params: parameters.query,
          timeout: connector.configuration.timeout || 30000
        });
        break;
      case 'POST':
        response = await secureAxios.post(url, parameters.body, { 
          headers,
          timeout: connector.configuration.timeout || 30000
        });
        break;
      case 'PUT':
        response = await secureAxios.put(url, parameters.body, { 
          headers,
          timeout: connector.configuration.timeout || 30000
        });
        break;
      case 'DELETE':
        response = await secureAxios.delete(url, { 
          headers,
          data: parameters.body,
          timeout: connector.configuration.timeout || 30000
        });
        break;
      case 'PATCH':
        response = await secureAxios.patch(url, parameters.body, { 
          headers,
          timeout: connector.configuration.timeout || 30000
        });
        break;
      default:
        logger.error(`Unsupported method [${requestId}]`, { method: endpoint.method });
        return res.status(400).json({ error: `Unsupported method: ${endpoint.method}` });
    }
    
    // Log response (excluding sensitive data)
    logger.info(`Response received [${requestId}]`, {
      status: response.status,
      statusText: response.statusText,
      hasData: !!response.data,
      dataType: typeof response.data
    });
    
    // Check if response status matches expected status
    const expectedStatus = endpoint.response?.successCode || 200;
    if (response.status !== expectedStatus) {
      logger.warn(`Unexpected response status [${requestId}]`, {
        expected: expectedStatus,
        actual: response.status
      });
    }
    
    // Find mapping for this endpoint
    const mapping = connector.mappings?.find(m => m.sourceEndpoint === endpointId);
    
    // Transform response if mapping exists
    let result = response.data;
    if (mapping && mapping.transformations) {
      logger.info(`Applying transformations [${requestId}]`);
      result = {};
      
      // Apply each transformation
      for (const transformation of mapping.transformations) {
        const { source, target, transform } = transformation;
        
        try {
          // Extract value using JSONPath
          let value = jp.query(response.data, source);
          
          // If the result is an array with a single item, extract it
          if (Array.isArray(value) && value.length === 1 && !source.endsWith('[*]')) {
            value = value[0];
          }
          
          // Apply transformation if specified
          if (transform && transform !== 'identity') {
            // Get transformation function from registry
            const transformResponse = await axios.get(`${registryApiUrl}/transformations/${transform}`);
            const transformFn = new Function('value', 'params', transformResponse.data.code);
            
            // Apply transformation
            value = transformFn(value, transformation.parameters);
          }
          
          // Set the transformed value
          result[target] = value;
        } catch (error) {
          logger.error(`Transformation error [${requestId}]`, {
            source,
            target,
            error: error.message
          });
          // Continue with other transformations
        }
      }
    }
    
    // Log usage
    try {
      await axios.post(`${usageMeteringUrl}/log`, {
        connectorId,
        endpointId,
        userId,
        timestamp: new Date().toISOString(),
        status: 'success'
      });
    } catch (error) {
      logger.error(`Error logging usage [${requestId}]`, { error: error.message });
      // Continue even if usage logging fails
    }
    
    logger.info(`Execution completed successfully [${requestId}]`);
    res.json(result);
  } catch (error) {
    logger.error(`Execution error [${requestId}]`, { 
      error: error.message,
      stack: error.stack
    });
    
    // Handle different types of errors
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      logger.error(`API error response [${requestId}]`, {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
      
      res.status(error.response.status).json({
        error: 'API Error',
        message: error.message,
        statusCode: error.response.status,
        data: error.response.data,
        timestamp: new Date().toISOString(),
        path: req.originalUrl
      });
    } else if (error.request) {
      // The request was made but no response was received
      logger.error(`No response received [${requestId}]`, {
        request: {
          method: error.request.method,
          path: error.request.path
        }
      });
      
      res.status(504).json({
        error: 'Gateway Timeout',
        message: 'No response received from the API',
        timestamp: new Date().toISOString(),
        path: req.originalUrl
      });
    } else {
      // Something happened in setting up the request that triggered an Error
      res.status(500).json({
        error: 'Internal Server Error',
        message: error.message,
        timestamp: new Date().toISOString(),
        path: req.originalUrl
      });
    }
    
    // Log usage failure
    try {
      await axios.post(`${usageMeteringUrl}/log`, {
        connectorId: req.params.connectorId,
        endpointId: req.params.endpointId,
        userId: req.body.userId,
        timestamp: new Date().toISOString(),
        status: 'error',
        error: error.message
      });
    } catch (logError) {
      logger.error(`Error logging usage failure [${requestId}]`, { error: logError.message });
      // Continue even if usage logging fails
    }
  }
});

// Start the server
const server = app.listen(port, () => {
  logger.info(`Secure connector executor running on port ${port}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    logger.info('HTTP server closed');
    process.exit(0);
  });
});

module.exports = app;
