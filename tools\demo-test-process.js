/**
 * NovaFuse Test Process Demonstration
 *
 * This script demonstrates the NovaFuse testing process in a simple, visual way.
 * It shows how our tests catch compliance issues and generate evidence.
 */

const chalk = require('chalk');

// Simple utility to create a delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Print with typing effect
async function typePrint(text, speed = 10) {
  for (let i = 0; i < text.length; i++) {
    process.stdout.write(text[i]);
    await delay(speed);
  }
  console.log('');
}

// Main demo function
async function runDemo() {
  console.clear();

  // Introduction
  await typePrint(chalk.blue.bold('=== NovaFuse: The Trust Automation Platform ==='), 20);
  await delay(500);
  await typePrint(chalk.white.bold('Trust, Automated.'), 30);
  await delay(1000);
  await typePrint('This demo shows how NovaFuse tests catch compliance issues and generate evidence.');
  await delay(1000);

  // Step 1: Code Change
  console.log('\n' + chalk.yellow.bold('STEP 1: Developer Makes a Code Change'));
  await delay(500);
  console.log(chalk.gray('// Developer adds a new feature to store user data'));
  console.log(chalk.white(`
function storeUserData(userData) {
  // Store user data in database
  database.insert('users', userData);

  // No encryption or validation implemented yet
  return { success: true };
}`));
  await delay(2000);

  // Step 2: Commit and Push
  console.log('\n' + chalk.yellow.bold('STEP 2: Developer Commits and Pushes Code'));
  await delay(500);
  await typePrint(chalk.gray('$ git commit -m "Add user data storage functionality"'));
  await delay(300);
  await typePrint(chalk.gray('[main 3e7c45d] Add user data storage functionality'));
  await delay(300);
  await typePrint(chalk.gray('$ git push origin main'));
  await delay(500);
  await typePrint(chalk.gray('Enumerating objects: 5, done.'));
  await delay(200);
  await typePrint(chalk.gray('Counting objects: 100% (5/5), done.'));
  await delay(200);
  await typePrint(chalk.gray('Writing objects: 100% (3/3), 350 bytes | 350.00 KiB/s, done.'));
  await delay(200);
  await typePrint(chalk.gray('Total 3 (delta 2), reused 0 (delta 0), pack-reused 0'));
  await delay(200);
  await typePrint(chalk.gray('remote: Resolving deltas: 100% (2/2), completed with 2 local objects.'));
  await delay(200);
  await typePrint(chalk.gray('To github.com:Dartan1983/nova-marketplace.git'));
  await delay(200);
  await typePrint(chalk.gray('   a7b2c3d..3e7c45d  main -> main'));
  await delay(1000);

  // Step 3: CI Pipeline Starts
  console.log('\n' + chalk.yellow.bold('STEP 3: CI Pipeline Automatically Starts'));
  await delay(500);
  await typePrint(chalk.gray('GitHub Actions workflow "NovaFuse Test Suite" started'));
  await delay(500);
  console.log(chalk.white('Running tests...'));

  // Show progress bar
  const stages = ['Linting', 'Unit Tests', 'API Tests', 'Security Tests', 'Compliance Tests'];
  for (const stage of stages) {
    process.stdout.write(chalk.white(`  ${stage}: `));
    for (let i = 0; i < 20; i++) {
      process.stdout.write(chalk.green('█'));
      await delay(50);
    }
    if (stage !== 'Compliance Tests') {
      console.log(chalk.green(' PASSED'));
    } else {
      console.log(chalk.red(' FAILED'));
    }
    await delay(300);
  }
  await delay(1000);

  // Step 4: Test Failure
  console.log('\n' + chalk.yellow.bold('STEP 4: Compliance Tests Detect Issues'));
  await delay(500);
  console.log(chalk.red('FAIL') + chalk.white(' tests/compliance/gdpr.test.js'));
  await delay(300);
  console.log(chalk.white(`  ● GDPR Compliance › Article 5 › should encrypt personal data at rest

    Expected userData to be encrypted before storage

    > 15 |   expect(encryptionUtil.isEncrypted(storedData)).toBe(true);
         |                                                  ^

    at Object.<anonymous> (tests/compliance/gdpr.test.js:15:50)
  `));
  await delay(300);
  console.log(chalk.red('FAIL') + chalk.white(' tests/compliance/hipaa.test.js'));
  await delay(300);
  console.log(chalk.white(`  ● HIPAA Compliance › 164.312(a)(1) › should validate data before storage

    Expected input validation to be performed

    > 22 |   expect(validationUtil.isValidated(userData)).toBe(true);
         |                                                ^

    at Object.<anonymous> (tests/compliance/hipaa.test.js:22:48)
  `));
  await delay(1000);

  // Step 5: Automated Report
  console.log('\n' + chalk.yellow.bold('STEP 5: Automated Compliance Report Generated'));
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  NovaFuse Compliance Test Report                    │
│  ───────────────────────────────────                │
│                                                     │
│  Report ID: CR-2023-06-15-7842                      │
│  Generated: June 15, 2023 10:15:22 AM               │
│                                                     │
│  Test Results:                                      │
│    ✓ 1241 tests passed                              │
│    ✗ 2 tests failed                                 │
│                                                     │
│  Compliance Issues:                                 │
│    ✗ GDPR Article 5 - Data not encrypted at rest    │
│    ✗ HIPAA 164.312(a)(1) - Missing input validation │
│                                                     │
│  Affected Frameworks:                               │
│    - GDPR                                           │
│    - HIPAA                                          │
│    - PCI DSS (via cross-framework mapping)          │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(2000);

  // Step 6: Developer Fixes Issues
  console.log('\n' + chalk.yellow.bold('STEP 6: Developer Fixes the Issues'));
  await delay(500);
  console.log(chalk.gray('// Developer updates the code to fix compliance issues'));
  console.log(chalk.white(`
function storeUserData(userData) {
  // Validate user data
  if (!validationUtil.validate(userData)) {
    throw new Error('Invalid user data');
  }

  // Encrypt sensitive data before storage
  const encryptedData = encryptionUtil.encrypt(userData);

  // Store encrypted data in database
  database.insert('users', encryptedData);

  return { success: true };
}`));
  await delay(2000);

  // Step 7: Tests Pass
  console.log('\n' + chalk.yellow.bold('STEP 7: Tests Now Pass'));
  await delay(500);
  await typePrint(chalk.gray('$ git commit -m "Fix GDPR and HIPAA compliance issues"'));
  await delay(300);
  await typePrint(chalk.gray('[main 8f9g67h] Fix GDPR and HIPAA compliance issues'));
  await delay(300);
  await typePrint(chalk.gray('$ git push origin main'));
  await delay(1000);

  console.log(chalk.white('Running tests again...'));

  // Show progress bar for all passing tests
  for (const stage of stages) {
    process.stdout.write(chalk.white(`  ${stage}: `));
    for (let i = 0; i < 20; i++) {
      process.stdout.write(chalk.green('█'));
      await delay(50);
    }
    console.log(chalk.green(' PASSED'));
    await delay(300);
  }
  await delay(1000);

  // Step 8: Compliance Evidence
  console.log('\n' + chalk.yellow.bold('STEP 8: Compliance Evidence Generated'));
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  NovaFuse Compliance Evidence                       │
│  ────────────────────────────────                   │
│                                                     │
│  Evidence ID: EV-2023-06-15-4291                    │
│  Generated: June 15, 2023 10:45:37 AM               │
│                                                     │
│  Test Results:                                      │
│    ✓ 1243 tests passed                              │
│    ✗ 0 tests failed                                 │
│                                                     │
│  Compliance Status:                                 │
│    ✓ GDPR Article 5 - Data encrypted at rest        │
│    ✓ HIPAA 164.312(a)(1) - Input validation added   │
│    ✓ PCI DSS Req 3.4 - Encryption implemented       │
│                                                     │
│  This evidence can be used for:                     │
│    - GDPR Compliance Audit                          │
│    - HIPAA Compliance Audit                         │
│    - PCI DSS Compliance Audit                       │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(2000);

  // Trust Automation Metrics
  console.log('\n' + chalk.yellow.bold('STEP 9: Trust Automation Metrics'));
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  NovaFuse Trust Automation Metrics                  │
│  ────────────────────────────────                   │
│                                                     │
│  Traditional Approach:                              │
│    Time to Compliance: 3-6 weeks                    │
│    Resources Required: 4-6 team members             │
│    Evidence Quality: Variable                       │
│                                                     │
│  NovaFuse Approach:                                 │
│    Time to Compliance: 45.37 seconds                │
│    Resources Required: 0 (fully automated)          │
│    Evidence Quality: Consistent & Verifiable        │
│                                                     │
│  Trust Automation Score™: 97.3%                     │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(2000);

  // Conclusion
  console.log('\n' + chalk.blue.bold('=== Demonstration Complete ==='));
  await delay(500);
  console.log(chalk.white(`
NovaFuse: Trust, Automated.

Compliance Without Compromise, Effort Without End

This demonstration shows how NovaFuse:

1. Automatically detects compliance issues in code changes
2. Provides detailed information about the specific requirements violated
3. Generates compliance reports for audit purposes
4. Verifies fixes and generates compliance evidence
5. Maps evidence across multiple regulatory frameworks

From 300-Day Audits → 3-Click Compliance

This process ensures that NovaFuse maintains the highest standards of compliance
and security, preventing non-compliant code from reaching production.
`));
}

// Check if chalk is installed, if not, suggest installing it
try {
  require.resolve('chalk');
  // Run the demo
  runDemo().catch(console.error);
} catch (e) {
  console.error('This demo requires the "chalk" package. Please install it with:');
  console.error('npm install chalk');
}
