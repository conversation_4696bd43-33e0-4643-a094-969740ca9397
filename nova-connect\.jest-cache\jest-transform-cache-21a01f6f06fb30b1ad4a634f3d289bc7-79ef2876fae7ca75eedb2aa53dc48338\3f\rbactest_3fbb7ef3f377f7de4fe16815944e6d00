852a61997917a73b4fe829eeeeaaf27d
/**
 * RBAC Tests
 * 
 * This file contains tests for the RBAC system.
 */

const mongoose = require('mongoose');
const {
  MongoMemoryServer
} = require('mongodb-memory-server');
const RBACService = require('../../api/services/RBACService');
const Role = require('../../api/models/Role');
const Permission = require('../../api/models/Permission');
const UserRole = require('../../api/models/UserRole');
const User = require('../../api/models/User');
let mongoServer;
let rbacService;
let testUser;
let adminRole;
let userRole;
let viewPermission;
let editPermission;

// Connect to in-memory MongoDB
beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const uri = mongoServer.getUri();
  await mongoose.connect(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });

  // Create RBAC service
  rbacService = new RBACService();

  // Initialize database with default roles and permissions
  await rbacService.initializeDatabase();
});

// Disconnect from in-memory MongoDB
afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

// Clear database before each test
beforeEach(async () => {
  // Create test user
  testUser = new User({
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Test',
    lastName: 'User',
    status: 'active'
  });
  await testUser.save();

  // Get roles
  adminRole = await Role.findOne({
    name: 'Administrator'
  });
  userRole = await Role.findOne({
    name: 'User'
  });

  // Create test permissions
  viewPermission = new Permission({
    name: 'View Test',
    description: 'View test resources',
    resource: 'test',
    action: 'view',
    isSystem: false
  });
  editPermission = new Permission({
    name: 'Edit Test',
    description: 'Edit test resources',
    resource: 'test',
    action: 'edit',
    isSystem: false
  });
  await viewPermission.save();
  await editPermission.save();
});

// Clear database after each test
afterEach(async () => {
  await User.deleteMany({});
  await UserRole.deleteMany({});

  // Delete test permissions
  await Permission.deleteOne({
    resource: 'test',
    action: 'view'
  });
  await Permission.deleteOne({
    resource: 'test',
    action: 'edit'
  });
});
describe('RBAC Service', () => {
  describe('Role Management', () => {
    test('should get all roles', async () => {
      const roles = await rbacService.getAllRoles();
      expect(roles).toBeDefined();
      expect(roles.length).toBeGreaterThanOrEqual(4); // At least 4 default roles
      expect(roles.map(r => r.name)).toContain('Administrator');
      expect(roles.map(r => r.name)).toContain('Manager');
      expect(roles.map(r => r.name)).toContain('User');
      expect(roles.map(r => r.name)).toContain('Viewer');
    });
    test('should get role by ID', async () => {
      const role = await rbacService.getRoleById(adminRole._id);
      expect(role).toBeDefined();
      expect(role.name).toBe('Administrator');
      expect(role.permissions).toContain('*');
    });
    test('should create a new role', async () => {
      const newRole = await rbacService.createRole({
        name: 'Test Role',
        description: 'Test role description',
        permissions: ['test:view', 'test:edit']
      });
      expect(newRole).toBeDefined();
      expect(newRole.name).toBe('Test Role');
      expect(newRole.description).toBe('Test role description');
      expect(newRole.permissions).toContain('test:view');
      expect(newRole.permissions).toContain('test:edit');

      // Clean up
      await Role.deleteOne({
        _id: newRole._id
      });
    });
    test('should update a role', async () => {
      // Create a role to update
      const role = new Role({
        name: 'Role to Update',
        description: 'Original description',
        permissions: ['test:view']
      });
      await role.save();

      // Update the role
      const updatedRole = await rbacService.updateRole(role._id, {
        name: 'Updated Role',
        description: 'Updated description',
        permissions: ['test:view', 'test:edit']
      });
      expect(updatedRole).toBeDefined();
      expect(updatedRole.name).toBe('Updated Role');
      expect(updatedRole.description).toBe('Updated description');
      expect(updatedRole.permissions).toContain('test:view');
      expect(updatedRole.permissions).toContain('test:edit');

      // Clean up
      await Role.deleteOne({
        _id: role._id
      });
    });
    test('should delete a role', async () => {
      // Create a role to delete
      const role = new Role({
        name: 'Role to Delete',
        description: 'This role will be deleted',
        permissions: ['test:view']
      });
      await role.save();

      // Delete the role
      const result = await rbacService.deleteRole(role._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify role is deleted
      const deletedRole = await Role.findById(role._id);
      expect(deletedRole).toBeNull();
    });
  });
  describe('Permission Management', () => {
    test('should get all permissions', async () => {
      const permissions = await rbacService.getAllPermissions();
      expect(permissions).toBeDefined();
      expect(permissions.length).toBeGreaterThanOrEqual(2); // At least our 2 test permissions
      expect(permissions.map(p => p.name)).toContain('View Test');
      expect(permissions.map(p => p.name)).toContain('Edit Test');
    });
    test('should get permission by ID', async () => {
      const permission = await rbacService.getPermissionById(viewPermission._id);
      expect(permission).toBeDefined();
      expect(permission.name).toBe('View Test');
      expect(permission.resource).toBe('test');
      expect(permission.action).toBe('view');
    });
    test('should get permission by resource and action', async () => {
      const permission = await rbacService.getPermissionByResourceAction('test', 'view');
      expect(permission).toBeDefined();
      expect(permission.name).toBe('View Test');
      expect(permission.resource).toBe('test');
      expect(permission.action).toBe('view');
    });
  });
  describe('User Role Management', () => {
    test('should assign role to user', async () => {
      const result = await rbacService.assignRoleToUser(testUser._id, adminRole._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify user has role
      const userRoles = await rbacService.getUserRoles(testUser._id);
      expect(userRoles).toBeDefined();
      expect(userRoles.length).toBe(1);
      expect(userRoles[0]._id.toString()).toBe(adminRole._id.toString());
    });
    test('should remove role from user', async () => {
      // Assign role to user
      await rbacService.assignRoleToUser(testUser._id, adminRole._id);

      // Remove role from user
      const result = await rbacService.removeRoleFromUser(testUser._id, adminRole._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify user does not have role
      const userRoles = await rbacService.getUserRoles(testUser._id);
      expect(userRoles).toBeDefined();
      expect(userRoles.length).toBe(0);
    });
  });
  describe('Permission Checking', () => {
    test('should check if user has permission', async () => {
      // Assign role to user
      await rbacService.assignRoleToUser(testUser._id, userRole._id);

      // Check if user has permission
      const hasViewPermission = await rbacService.hasPermission(testUser._id, 'connector:view');
      const hasEditPermission = await rbacService.hasPermission(testUser._id, 'connector:edit');
      expect(hasViewPermission).toBe(true);
      expect(hasEditPermission).toBe(false);
    });
    test('should get user permissions', async () => {
      // Assign role to user
      await rbacService.assignRoleToUser(testUser._id, userRole._id);

      // Get user permissions
      const permissions = await rbacService.getUserPermissions(testUser._id);
      expect(permissions).toBeDefined();
      expect(permissions).toContain('connector:view');
      expect(permissions).toContain('connector:use');
      expect(permissions).toContain('workflow:view');
      expect(permissions).toContain('workflow:use');
      expect(permissions).toContain('normalization:view');
      expect(permissions).toContain('normalization:use');
      expect(permissions).toContain('monitoring:view');
      expect(permissions).not.toContain('connector:edit');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************