import React, { useState } from 'react';
import Link from 'next/link';
import axios from 'axios';
import Head from 'next/head';

// API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    try {
      setError('');
      setIsSubmitting(true);

      await axios.post(`${API_URL}/auth/forgot-password`, { email });

      setIsSubmitted(true);
    } catch (error) {
      // Don't reveal if the email exists or not for security reasons
      // Just show the success message anyway
      setIsSubmitted(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Head>
        <title>Forgot Password - NovaFuse Compliance App Store</title>
        <meta name="description" content="Reset your password for the NovaFuse Compliance App Store" />
      </Head>

      <div className="min-h-screen flex items-center justify-center bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <Link href="/" className="flex justify-center">
              <img className="h-12 w-auto" src="/images/logo.png" alt="NovaFuse" />
            </Link>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
              Reset your password
            </h2>
            <p className="mt-2 text-center text-sm text-gray-400">
              Enter your email address and we'll send you a link to reset your password.
            </p>
          </div>

          {isSubmitted ? (
            <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-6 text-center">
              <h3 className="text-xl font-bold mb-2 text-green-400">Check your email</h3>
              <p className="text-gray-300 mb-4">
                If an account exists with the email you provided, we've sent instructions to reset your password.
              </p>
              <Link href="/login" className="text-blue-500 hover:text-blue-400 font-medium">
                Return to login
              </Link>
            </div>
          ) : (
            <>
              {error && (
                <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 text-red-400">
                  {error}
                </div>
              )}

              <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                <div>
                  <label htmlFor="email-address" className="sr-only">Email address</label>
                  <input
                    id="email-address"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="appearance-none rounded-md relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-500 text-white bg-gray-800 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="Email address"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>

                <div>
                  <button
                    type="submit"
                    className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </span>
                    ) : 'Send reset link'}
                  </button>
                </div>
              </form>

              <div className="text-center">
                <Link href="/login" className="text-sm text-blue-500 hover:text-blue-400">
                  Back to login
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}
