/**
 * NovaPulse+ - Impact Analysis Service
 * 
 * This service provides impact analysis capabilities for regulatory changes.
 */

const { createLogger } = require('../../utils/logger');
const impactCalculator = require('../utils/impact-calculator');

const logger = createLogger('impact-analysis-service');

// In-memory storage for impact analyses (would be replaced with a database in production)
const impactAnalyses = [];

/**
 * Analyze impact of a regulatory change
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {Promise<Object>} - Impact analysis result
 */
async function analyzeImpact(regulatoryChange) {
  logger.info('Analyzing impact', { 
    changeId: regulatoryChange.id,
    regulation: regulatoryChange.regulation
  });
  
  try {
    // Calculate impact scores
    const impactScores = impactCalculator.calculateImpactScores(regulatoryChange);
    
    // Determine priority
    const priority = determinePriority(impactScores);
    
    // Identify affected systems
    const affectedSystems = identifyAffectedSystems(regulatoryChange);
    
    // Generate implementation plan
    const implementationPlan = generateImplementationPlan(regulatoryChange, impactScores);
    
    // Create impact analysis
    const impactAnalysis = {
      id: `impact-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      changeId: regulatoryChange.id,
      regulation: regulatoryChange.regulation,
      impactScores,
      priority,
      affectedSystems,
      implementationPlan,
      analysisDate: new Date().toISOString(),
      status: 'active'
    };
    
    // Store impact analysis
    impactAnalyses.push(impactAnalysis);
    
    return impactAnalysis;
  } catch (error) {
    logger.error('Error analyzing impact', {
      changeId: regulatoryChange.id,
      error: error.message
    });
    throw error;
  }
}

/**
 * Determine priority based on impact scores
 * 
 * @param {Object} impactScores - Impact scores
 * @returns {string} - Priority level
 * @private
 */
function determinePriority(impactScores) {
  const totalScore = impactScores.overall;
  
  if (totalScore >= 80) {
    return 'critical';
  } else if (totalScore >= 60) {
    return 'high';
  } else if (totalScore >= 40) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Identify affected systems
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @returns {Array} - Affected systems
 * @private
 */
function identifyAffectedSystems(regulatoryChange) {
  const affectedSystems = [];
  
  // Map categories to systems
  const categorySystemMap = {
    'data-protection': ['data-storage', 'data-processing', 'data-transfer'],
    'data-security': ['encryption', 'access-control', 'authentication'],
    'consumer-rights': ['user-portal', 'consent-management', 'data-subject-requests'],
    'contracts': ['vendor-management', 'contract-management'],
    'payment-security': ['payment-processing', 'card-data-storage'],
    'reporting': ['reporting-system', 'audit-logs'],
    'governance': ['policy-management', 'training-system']
  };
  
  // Add systems based on categories
  regulatoryChange.categories.forEach(category => {
    const systems = categorySystemMap[category] || [];
    systems.forEach(system => {
      if (!affectedSystems.includes(system)) {
        affectedSystems.push(system);
      }
    });
  });
  
  return affectedSystems;
}

/**
 * Generate implementation plan
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @param {Object} impactScores - Impact scores
 * @returns {Object} - Implementation plan
 * @private
 */
function generateImplementationPlan(regulatoryChange, impactScores) {
  // Calculate timeline based on impact and deadline
  let timelineInDays = 30; // Default
  
  if (regulatoryChange.deadline) {
    const daysUntilDeadline = Math.ceil(
      (new Date(regulatoryChange.deadline) - new Date()) / (1000 * 60 * 60 * 24)
    );
    
    timelineInDays = Math.min(daysUntilDeadline - 14, 180); // At least 2 weeks before deadline, max 6 months
    timelineInDays = Math.max(timelineInDays, 7); // At least 1 week
  } else if (impactScores.overall >= 80) {
    timelineInDays = 30; // Critical impact: 1 month
  } else if (impactScores.overall >= 60) {
    timelineInDays = 60; // High impact: 2 months
  } else if (impactScores.overall >= 40) {
    timelineInDays = 90; // Medium impact: 3 months
  } else {
    timelineInDays = 120; // Low impact: 4 months
  }
  
  // Generate phases
  const phases = generateImplementationPhases(regulatoryChange, timelineInDays);
  
  // Generate resource estimates
  const resourceEstimates = generateResourceEstimates(regulatoryChange, impactScores);
  
  return {
    timelineInDays,
    startDate: new Date().toISOString(),
    targetCompletionDate: new Date(Date.now() + timelineInDays * 24 * 60 * 60 * 1000).toISOString(),
    phases,
    resourceEstimates
  };
}

/**
 * Generate implementation phases
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @param {number} timelineInDays - Timeline in days
 * @returns {Array} - Implementation phases
 * @private
 */
function generateImplementationPhases(regulatoryChange, timelineInDays) {
  const phases = [];
  
  // Phase 1: Analysis and Planning
  const analysisDuration = Math.ceil(timelineInDays * 0.2); // 20% of timeline
  phases.push({
    name: 'Analysis and Planning',
    description: 'Analyze requirements and plan implementation',
    durationInDays: analysisDuration,
    startDay: 0,
    endDay: analysisDuration - 1,
    tasks: [
      {
        name: 'Detailed requirements analysis',
        description: 'Analyze regulatory requirements in detail',
        durationInDays: Math.ceil(analysisDuration * 0.4),
        dependencies: []
      },
      {
        name: 'Gap assessment',
        description: 'Assess gaps between current state and requirements',
        durationInDays: Math.ceil(analysisDuration * 0.3),
        dependencies: ['Detailed requirements analysis']
      },
      {
        name: 'Implementation planning',
        description: 'Develop detailed implementation plan',
        durationInDays: Math.ceil(analysisDuration * 0.3),
        dependencies: ['Gap assessment']
      }
    ]
  });
  
  // Phase 2: Implementation
  const implementationDuration = Math.ceil(timelineInDays * 0.5); // 50% of timeline
  phases.push({
    name: 'Implementation',
    description: 'Implement required changes',
    durationInDays: implementationDuration,
    startDay: analysisDuration,
    endDay: analysisDuration + implementationDuration - 1,
    tasks: regulatoryChange.requirements.map((req, index) => ({
      name: `Implement: ${req.description}`,
      description: req.description,
      durationInDays: Math.ceil(implementationDuration / regulatoryChange.requirements.length),
      dependencies: index === 0 ? ['Implementation planning'] : [`Implement: ${regulatoryChange.requirements[index - 1].description}`]
    }))
  });
  
  // Phase 3: Testing and Validation
  const testingDuration = Math.ceil(timelineInDays * 0.2); // 20% of timeline
  phases.push({
    name: 'Testing and Validation',
    description: 'Test and validate implemented changes',
    durationInDays: testingDuration,
    startDay: analysisDuration + implementationDuration,
    endDay: analysisDuration + implementationDuration + testingDuration - 1,
    tasks: [
      {
        name: 'Testing',
        description: 'Test implemented changes',
        durationInDays: Math.ceil(testingDuration * 0.6),
        dependencies: ['Implementation']
      },
      {
        name: 'Validation',
        description: 'Validate compliance with requirements',
        durationInDays: Math.ceil(testingDuration * 0.4),
        dependencies: ['Testing']
      }
    ]
  });
  
  // Phase 4: Documentation and Reporting
  const documentationDuration = Math.ceil(timelineInDays * 0.1); // 10% of timeline
  phases.push({
    name: 'Documentation and Reporting',
    description: 'Document changes and prepare compliance reports',
    durationInDays: documentationDuration,
    startDay: analysisDuration + implementationDuration + testingDuration,
    endDay: timelineInDays - 1,
    tasks: [
      {
        name: 'Documentation',
        description: 'Document implemented changes',
        durationInDays: Math.ceil(documentationDuration * 0.5),
        dependencies: ['Validation']
      },
      {
        name: 'Compliance reporting',
        description: 'Prepare compliance reports',
        durationInDays: Math.ceil(documentationDuration * 0.5),
        dependencies: ['Documentation']
      }
    ]
  });
  
  return phases;
}

/**
 * Generate resource estimates
 * 
 * @param {Object} regulatoryChange - Regulatory change object
 * @param {Object} impactScores - Impact scores
 * @returns {Object} - Resource estimates
 * @private
 */
function generateResourceEstimates(regulatoryChange, impactScores) {
  // Base estimates
  let personDays = 0;
  let costEstimate = 0;
  
  // Adjust based on impact scores
  if (impactScores.overall >= 80) {
    personDays = 60; // Critical impact: 60 person-days
    costEstimate = 100000; // $100,000
  } else if (impactScores.overall >= 60) {
    personDays = 40; // High impact: 40 person-days
    costEstimate = 60000; // $60,000
  } else if (impactScores.overall >= 40) {
    personDays = 20; // Medium impact: 20 person-days
    costEstimate = 30000; // $30,000
  } else {
    personDays = 10; // Low impact: 10 person-days
    costEstimate = 15000; // $15,000
  }
  
  // Adjust based on number of requirements
  personDays += regulatoryChange.requirements.length * 2;
  costEstimate += regulatoryChange.requirements.length * 3000;
  
  // Adjust based on categories
  personDays += regulatoryChange.categories.length * 3;
  costEstimate += regulatoryChange.categories.length * 5000;
  
  return {
    personDays,
    costEstimate,
    teams: [
      {
        name: 'Legal',
        personDays: Math.ceil(personDays * 0.2)
      },
      {
        name: 'Compliance',
        personDays: Math.ceil(personDays * 0.3)
      },
      {
        name: 'IT',
        personDays: Math.ceil(personDays * 0.4)
      },
      {
        name: 'Business',
        personDays: Math.ceil(personDays * 0.1)
      }
    ]
  };
}

/**
 * Get latest impact analysis for a regulatory change
 * 
 * @param {string} changeId - Regulatory change ID
 * @returns {Promise<Object|null>} - Latest impact analysis or null if not found
 */
async function getLatestImpactAnalysis(changeId) {
  logger.debug('Getting latest impact analysis', { changeId });
  
  // Find all impact analyses for the regulatory change
  const changeAnalyses = impactAnalyses.filter(a => a.changeId === changeId);
  
  if (changeAnalyses.length === 0) {
    return null;
  }
  
  // Sort by date (newest first) and return the first one
  return changeAnalyses.sort((a, b) => 
    new Date(b.analysisDate) - new Date(a.analysisDate)
  )[0];
}

/**
 * Get all impact analyses for a regulatory change
 * 
 * @param {string} changeId - Regulatory change ID
 * @returns {Promise<Array>} - List of impact analyses
 */
async function getImpactAnalyses(changeId) {
  logger.debug('Getting impact analyses', { changeId });
  
  // Find all impact analyses for the regulatory change
  return impactAnalyses.filter(a => a.changeId === changeId);
}

module.exports = {
  analyzeImpact,
  getLatestImpactAnalysis,
  getImpactAnalyses
};
