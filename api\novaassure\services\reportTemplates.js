/**
 * Report Templates Service
 * 
 * This service provides templates for report generation.
 */

const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');
const puppeteer = require('puppeteer');
const { createObjectCsvWriter } = require('csv-writer');
const ExcelJS = require('exceljs');
const logger = require('../utils/logger');

// Register handlebars helpers
handlebars.registerHelper('formatDate', function(date) {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
});

handlebars.registerHelper('formatDateTime', function(date) {
  if (!date) return 'N/A';
  return new Date(date).toLocaleString();
});

handlebars.registerHelper('statusBadge', function(status) {
  let color;
  
  switch (status) {
    case 'pass':
    case 'implemented':
    case 'completed':
      color = 'green';
      break;
    case 'fail':
    case 'not-implemented':
    case 'failed':
      color = 'red';
      break;
    case 'partially-implemented':
    case 'in-progress':
      color = 'orange';
      break;
    case 'not-applicable':
    case 'pending':
    case 'draft':
      color = 'gray';
      break;
    default:
      color = 'blue';
  }
  
  return new handlebars.SafeString(`<span class="badge badge-${color}">${status}</span>`);
});

handlebars.registerHelper('riskLevelBadge', function(riskLevel) {
  let color;
  
  switch (riskLevel) {
    case 'low':
      color = 'green';
      break;
    case 'medium':
      color = 'orange';
      break;
    case 'high':
    case 'critical':
      color = 'red';
      break;
    default:
      color = 'blue';
  }
  
  return new handlebars.SafeString(`<span class="badge badge-${color}">${riskLevel}</span>`);
});

handlebars.registerHelper('percentage', function(value, total) {
  if (!total) return '0%';
  return Math.round((value / total) * 100) + '%';
});

handlebars.registerHelper('progressBar', function(value, total) {
  if (!total) return '';
  
  const percentage = Math.round((value / total) * 100);
  let color;
  
  if (percentage >= 80) {
    color = 'green';
  } else if (percentage >= 50) {
    color = 'orange';
  } else {
    color = 'red';
  }
  
  return new handlebars.SafeString(`
    <div class="progress">
      <div class="progress-bar progress-bar-${color}" style="width: ${percentage}%;">
        ${percentage}%
      </div>
    </div>
  `);
});

/**
 * Generate compliance report HTML
 * @param {Object} data - Report data
 * @returns {Promise<string>} - HTML content
 */
async function generateComplianceReportHtml(data) {
  try {
    // Load template
    const templatePath = path.join(__dirname, '../templates/compliance-report.hbs');
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    
    // Compile template
    const template = handlebars.compile(templateContent);
    
    // Generate HTML
    const html = template(data);
    
    return html;
  } catch (error) {
    logger.error('Failed to generate compliance report HTML', error);
    throw error;
  }
}

/**
 * Generate test results report HTML
 * @param {Object} data - Report data
 * @returns {Promise<string>} - HTML content
 */
async function generateTestResultsReportHtml(data) {
  try {
    // Load template
    const templatePath = path.join(__dirname, '../templates/test-results-report.hbs');
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    
    // Compile template
    const template = handlebars.compile(templateContent);
    
    // Generate HTML
    const html = template(data);
    
    return html;
  } catch (error) {
    logger.error('Failed to generate test results report HTML', error);
    throw error;
  }
}

/**
 * Generate evidence report HTML
 * @param {Object} data - Report data
 * @returns {Promise<string>} - HTML content
 */
async function generateEvidenceReportHtml(data) {
  try {
    // Load template
    const templatePath = path.join(__dirname, '../templates/evidence-report.hbs');
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    
    // Compile template
    const template = handlebars.compile(templateContent);
    
    // Generate HTML
    const html = template(data);
    
    return html;
  } catch (error) {
    logger.error('Failed to generate evidence report HTML', error);
    throw error;
  }
}

/**
 * Generate control effectiveness report HTML
 * @param {Object} data - Report data
 * @returns {Promise<string>} - HTML content
 */
async function generateControlEffectivenessReportHtml(data) {
  try {
    // Load template
    const templatePath = path.join(__dirname, '../templates/control-effectiveness-report.hbs');
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    
    // Compile template
    const template = handlebars.compile(templateContent);
    
    // Generate HTML
    const html = template(data);
    
    return html;
  } catch (error) {
    logger.error('Failed to generate control effectiveness report HTML', error);
    throw error;
  }
}

/**
 * Generate PDF from HTML
 * @param {string} html - HTML content
 * @param {Object} options - PDF options
 * @returns {Promise<Buffer>} - PDF buffer
 */
async function generatePdf(html, options = {}) {
  try {
    // Launch browser
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    // Create page
    const page = await browser.newPage();
    
    // Set content
    await page.setContent(html, { waitUntil: 'networkidle0' });
    
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '1cm',
        right: '1cm',
        bottom: '1cm',
        left: '1cm'
      },
      ...options
    });
    
    // Close browser
    await browser.close();
    
    return pdfBuffer;
  } catch (error) {
    logger.error('Failed to generate PDF', error);
    throw error;
  }
}

/**
 * Generate CSV
 * @param {Array} data - Data array
 * @param {Array} header - CSV header
 * @param {string} path - Output path
 * @returns {Promise<void>}
 */
async function generateCsv(data, header, path) {
  try {
    // Create CSV writer
    const csvWriter = createObjectCsvWriter({
      path,
      header
    });
    
    // Write CSV
    await csvWriter.writeRecords(data);
  } catch (error) {
    logger.error('Failed to generate CSV', error);
    throw error;
  }
}

/**
 * Generate Excel
 * @param {Object} data - Report data
 * @param {string} path - Output path
 * @returns {Promise<void>}
 */
async function generateExcel(data, path) {
  try {
    // Create workbook
    const workbook = new ExcelJS.Workbook();
    
    // Add summary worksheet
    const summarySheet = workbook.addWorksheet('Summary');
    
    // Add summary data
    summarySheet.columns = [
      { header: 'Report Name', key: 'name', width: 30 },
      { header: 'Description', key: 'description', width: 50 },
      { header: 'Generated', key: 'generated', width: 20 },
      { header: 'Framework', key: 'framework', width: 20 }
    ];
    
    summarySheet.addRow({
      name: data.name,
      description: data.description,
      generated: new Date().toLocaleString(),
      framework: data.framework
    });
    
    // Add controls worksheet if applicable
    if (data.controls && data.controls.length > 0) {
      const controlsSheet = workbook.addWorksheet('Controls');
      
      controlsSheet.columns = [
        { header: 'ID', key: 'id', width: 30 },
        { header: 'Name', key: 'name', width: 30 },
        { header: 'Description', key: 'description', width: 50 },
        { header: 'Framework', key: 'framework', width: 20 },
        { header: 'Category', key: 'category', width: 20 },
        { header: 'Risk Level', key: 'riskLevel', width: 15 },
        { header: 'Status', key: 'status', width: 20 }
      ];
      
      data.controls.forEach(control => {
        controlsSheet.addRow({
          id: control._id,
          name: control.name,
          description: control.description,
          framework: control.framework,
          category: control.category,
          riskLevel: control.riskLevel,
          status: control.implementationStatus
        });
      });
    }
    
    // Add test results worksheet if applicable
    if (data.testResults && data.testResults.length > 0) {
      const testResultsSheet = workbook.addWorksheet('Test Results');
      
      testResultsSheet.columns = [
        { header: 'Control', key: 'control', width: 30 },
        { header: 'Status', key: 'status', width: 15 },
        { header: 'Notes', key: 'notes', width: 50 },
        { header: 'Executed By', key: 'executedBy', width: 20 },
        { header: 'Executed At', key: 'executedAt', width: 20 }
      ];
      
      data.testResults.forEach(result => {
        testResultsSheet.addRow({
          control: result.control.name,
          status: result.status,
          notes: result.notes,
          executedBy: result.executedBy,
          executedAt: new Date(result.executedAt).toLocaleString()
        });
      });
    }
    
    // Add evidence worksheet if applicable
    if (data.evidence && data.evidence.length > 0) {
      const evidenceSheet = workbook.addWorksheet('Evidence');
      
      evidenceSheet.columns = [
        { header: 'ID', key: 'id', width: 30 },
        { header: 'Name', key: 'name', width: 30 },
        { header: 'Description', key: 'description', width: 50 },
        { header: 'Type', key: 'type', width: 15 },
        { header: 'Control', key: 'control', width: 30 },
        { header: 'Created At', key: 'createdAt', width: 20 },
        { header: 'Verified', key: 'verified', width: 10 }
      ];
      
      data.evidence.forEach(evidence => {
        evidenceSheet.addRow({
          id: evidence._id,
          name: evidence.name,
          description: evidence.description,
          type: evidence.type,
          control: evidence.control ? evidence.control.name : 'N/A',
          createdAt: new Date(evidence.createdAt).toLocaleString(),
          verified: evidence.blockchain && evidence.blockchain.merkleRoot ? 'Yes' : 'No'
        });
      });
    }
    
    // Write to file
    await workbook.xlsx.writeFile(path);
  } catch (error) {
    logger.error('Failed to generate Excel', error);
    throw error;
  }
}

/**
 * Generate JSON
 * @param {Object} data - Report data
 * @returns {string} - JSON string
 */
function generateJson(data) {
  return JSON.stringify(data, null, 2);
}

module.exports = {
  generateComplianceReportHtml,
  generateTestResultsReportHtml,
  generateEvidenceReportHtml,
  generateControlEffectivenessReportHtml,
  generatePdf,
  generateCsv,
  generateExcel,
  generateJson
};
