version: '3.8'

services:
  # KetherNet Simulation Test Runner
  kethernet-simulator:
    build:
      context: .
      dockerfile: Dockerfile.simulator
    container_name: kethernet-simulator
    depends_on:
      - governance-api
      - security-api
      - apis-api
    environment:
      - SIMULATION_MODE=full
      - CONSCIOUSNESS_THRESHOLD=2847
      - COHERENCE_VALIDATION=true
      - TRINITY_STACK_ENABLED=true
    volumes:
      - ./simulation-results:/app/results
    networks:
      - novafuse-network
    command: ["python", "/app/kethernet-simulation-suite.py"]
    
  # Consciousness Traffic Generator
  consciousness-traffic:
    image: alpine:latest
    container_name: consciousness-traffic
    depends_on:
      - kethernet-simulator
    environment:
      - TARGET_HOSTS=governance-api:3001,security-api:3002,apis-api:3003
      - CONSCIOUSNESS_LEVELS=0.12,0.52,0.82,0.95,2.847
    networks:
      - novafuse-network
    command: |
      sh -c "
        apk add --no-cache curl &&
        echo '🧠 Starting consciousness-filtered traffic generation...' &&
        while true; do
          # Test different consciousness levels
          for psi in 0.12 0.52 0.82 0.95 2.847; do
            coherence=$$(echo \"$$psi * 0.618\" | bc -l 2>/dev/null || echo \"0.5\")
            echo \"Testing Ψ=$$psi, κ=$$coherence\"
            
            # Test Governance API
            curl -H \"X-Consciousness-Level: $$psi\" \
                 -H \"X-Coherence-Score: $$coherence\" \
                 -H \"X-Comphyon-Units: $$(echo \"$$psi * 1000\" | bc -l 2>/dev/null || echo \"500\")\" \
                 http://governance-api:3001/health || echo \"Governance test failed\"
            
            # Test Security API  
            curl -H \"X-Consciousness-Level: $$psi\" \
                 -H \"X-Threat-Level: low\" \
                 http://security-api:3002/health || echo \"Security test failed\"
            
            # Test APIs Service
            curl -H \"X-Consciousness-Level: $$psi\" \
                 -H \"X-Evolution-Tracking: true\" \
                 http://apis-api:3003/health || echo \"APIs test failed\"
            
            sleep 2
          done
          
          echo \"Consciousness traffic cycle complete, waiting 30s...\"
          sleep 30
        done
      "
    
  # Threat Simulation Engine
  threat-simulator:
    image: alpine:latest
    container_name: threat-simulator
    depends_on:
      - security-api
    environment:
      - THREAT_TYPES=port_scan,malformed_headers,consciousness_bypass,ddos_simulation
      - AUTO_BLOCK_TEST=true
    networks:
      - novafuse-network
    command: |
      sh -c "
        apk add --no-cache curl nmap &&
        echo '🛡️ Starting threat simulation...' &&
        while true; do
          echo 'Running threat detection tests...'
          
          # Malformed header attacks
          curl -H 'X-Malicious: ☠️' http://security-api:3002/health || echo 'Malicious header test'
          curl -H 'X-Consciousness-Level: -999' http://governance-api:3001/health || echo 'Consciousness bypass test'
          curl -H 'X-Threat-Type: port_scan' http://security-api:3002/health || echo 'Port scan simulation'
          
          # Legitimate request (should pass)
          curl -H 'X-Consciousness-Level: 0.85' \
               -H 'X-Legitimate-User: true' \
               http://security-api:3002/health || echo 'Legitimate request test'
          
          echo 'Threat simulation cycle complete, waiting 60s...'
          sleep 60
        done
      "

  # Evolution Tracker
  evolution-tracker:
    image: python:3.9-alpine
    container_name: evolution-tracker
    depends_on:
      - apis-api
    environment:
      - EVOLUTION_TRACKING=true
      - CONSCIOUSNESS_VALIDATION=true
    networks:
      - novafuse-network
    command: |
      sh -c "
        pip install requests &&
        python -c \"
import requests
import time
import json
from datetime import datetime

def track_evolution():
    users = ['user_001', 'user_002', 'user_003']
    events = ['consciousness_upgrade', 'coherence_training', 'divine_alignment', 'trinity_validation']
    
    while True:
        for user in users:
            event = events[int(time.time()) % len(events)]
            delta = 0.05 + (hash(user + event) % 20) / 100
            current_level = 0.52 + delta
            
            payload = {
                'user_id': user,
                'event_type': event,
                'consciousness_delta': delta,
                'new_level': current_level,
                'timestamp': datetime.now().isoformat()
            }
            
            try:
                response = requests.post('http://apis-api:3003/evolution-update',
                                       json=payload, timeout=5)
                print(f'Evolution tracked: {user} → {event} (+{delta:.3f}) = Ψ{current_level:.3f}')
            except Exception as e:
                print(f'Evolution tracking: {user} → {event} (simulated)')
            
            time.sleep(5)
        
        time.sleep(30)

track_evolution()
\"
      "

networks:
  novafuse-network:
    external: true

volumes:
  simulation-results:
