/**
 * Authentication Tests for NovaConnect Universal API Connector
 * 
 * These tests verify that the connector can authenticate with various authentication methods.
 */

const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { 
  startAllServices, 
  stopAllServices,
  registryUrl,
  authUrl,
  executorUrl,
  mockApiUrl
} = require('../setup');

// Test data
const connectors = {
  apiKey: require('../connectors/api-key-connector.json'),
  basicAuth: require('../connectors/basic-auth-connector.json'),
  oauth2: require('../connectors/oauth2-connector.json'),
  jwt: require('../connectors/jwt-connector.json'),
  awsSigV4: require('../connectors/aws-sigv4-connector.json'),
  customAuth: require('../connectors/custom-auth-connector.json')
};

// Test credentials
const credentials = {
  apiKey: {
    name: 'API Key Test Credential',
    authType: 'API_KEY',
    credentials: {
      apiKey: 'valid-api-key',
      headerName: 'X-API-Key'
    }
  },
  basicAuth: {
    name: 'Basic Auth Test Credential',
    authType: 'BASIC',
    credentials: {
      username: 'testuser',
      password: 'testpassword'
    }
  },
  oauth2: {
    name: 'OAuth2 Test Credential',
    authType: 'OAUTH2',
    credentials: {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret'
    }
  },
  jwt: {
    name: 'JWT Test Credential',
    authType: 'JWT',
    credentials: {
      token: 'test-jwt-token'
    }
  },
  awsSigV4: {
    name: 'AWS SigV4 Test Credential',
    authType: 'AWS_SIG_V4',
    credentials: {
      accessKeyId: 'test-access-key',
      secretAccessKey: 'test-secret-key',
      region: 'us-east-1'
    }
  },
  customAuth: {
    name: 'Custom Auth Test Credential',
    authType: 'CUSTOM',
    credentials: {
      customAuthValue: 'custom-auth-value',
      headerName: 'X-Custom-Auth'
    }
  }
};

// Store connector and credential IDs
const connectorIds = {};
const credentialIds = {};

describe('Authentication Tests', () => {
  // Start services before all tests
  beforeAll(async () => {
    await startAllServices();
    
    // Clear request history
    await axios.post(`${mockApiUrl}/clear-history`);
  }, 60000);
  
  // Stop services after all tests
  afterAll(async () => {
    // Clean up test data
    for (const connectorId of Object.values(connectorIds)) {
      try {
        await axios.delete(`${registryUrl}/connectors/${connectorId}`);
      } catch (error) {
        console.error(`Error deleting connector ${connectorId}:`, error.message);
      }
    }
    
    for (const credentialId of Object.values(credentialIds)) {
      try {
        await axios.delete(`${authUrl}/credentials/${credentialId}`);
      } catch (error) {
        console.error(`Error deleting credential ${credentialId}:`, error.message);
      }
    }
    
    stopAllServices();
  });
  
  // Test API Key authentication
  describe('API Key Authentication', () => {
    it('should register the API Key connector', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, connectors.apiKey);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      connectorIds.apiKey = response.data.id;
      credentials.apiKey.connectorId = response.data.id;
    });
    
    it('should store API Key credentials', async () => {
      const response = await axios.post(`${authUrl}/credentials`, credentials.apiKey);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      credentialIds.apiKey = response.data.id;
    });
    
    it('should execute an endpoint with API Key authentication', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorIds.apiKey}/getResource`, {
        credentialId: credentialIds.apiKey,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('resourceId', 'resource-1');
      expect(response.data).toHaveProperty('resourceName', 'Test Resource');
    });
    
    it('should fail with invalid API Key', async () => {
      // Create invalid credentials
      const invalidCredentials = {
        ...credentials.apiKey,
        name: 'Invalid API Key Credential',
        credentials: {
          ...credentials.apiKey.credentials,
          apiKey: 'invalid-api-key'
        }
      };
      
      const credResponse = await axios.post(`${authUrl}/credentials`, invalidCredentials);
      const invalidCredentialId = credResponse.data.id;
      
      try {
        await axios.post(`${executorUrl}/execute/${connectorIds.apiKey}/getResource`, {
          credentialId: invalidCredentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(403);
      }
      
      // Clean up
      await axios.delete(`${authUrl}/credentials/${invalidCredentialId}`);
    });
  });
  
  // Test Basic authentication
  describe('Basic Authentication', () => {
    it('should register the Basic Auth connector', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, connectors.basicAuth);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      connectorIds.basicAuth = response.data.id;
      credentials.basicAuth.connectorId = response.data.id;
    });
    
    it('should store Basic Auth credentials', async () => {
      const response = await axios.post(`${authUrl}/credentials`, credentials.basicAuth);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      credentialIds.basicAuth = response.data.id;
    });
    
    it('should execute an endpoint with Basic authentication', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorIds.basicAuth}/getResource`, {
        credentialId: credentialIds.basicAuth,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('resourceId', 'resource-1');
      expect(response.data).toHaveProperty('resourceName', 'Test Resource');
    });
    
    it('should fail with invalid Basic Auth credentials', async () => {
      // Create invalid credentials
      const invalidCredentials = {
        ...credentials.basicAuth,
        name: 'Invalid Basic Auth Credential',
        credentials: {
          username: 'wronguser',
          password: 'wrongpassword'
        }
      };
      
      const credResponse = await axios.post(`${authUrl}/credentials`, invalidCredentials);
      const invalidCredentialId = credResponse.data.id;
      
      try {
        await axios.post(`${executorUrl}/execute/${connectorIds.basicAuth}/getResource`, {
          credentialId: invalidCredentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(403);
      }
      
      // Clean up
      await axios.delete(`${authUrl}/credentials/${invalidCredentialId}`);
    });
  });
  
  // Test OAuth2 authentication
  describe('OAuth2 Authentication', () => {
    it('should register the OAuth2 connector', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, connectors.oauth2);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      connectorIds.oauth2 = response.data.id;
      credentials.oauth2.connectorId = response.data.id;
    });
    
    it('should store OAuth2 credentials', async () => {
      const response = await axios.post(`${authUrl}/credentials`, credentials.oauth2);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      credentialIds.oauth2 = response.data.id;
    });
    
    it('should execute an endpoint with OAuth2 authentication', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorIds.oauth2}/getResource`, {
        credentialId: credentialIds.oauth2,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('resourceId', 'resource-1');
      expect(response.data).toHaveProperty('resourceName', 'Test Resource');
    });
    
    it('should fail with invalid OAuth2 credentials', async () => {
      // Create invalid credentials
      const invalidCredentials = {
        ...credentials.oauth2,
        name: 'Invalid OAuth2 Credential',
        credentials: {
          clientId: 'wrong-client-id',
          clientSecret: 'wrong-client-secret'
        }
      };
      
      const credResponse = await axios.post(`${authUrl}/credentials`, invalidCredentials);
      const invalidCredentialId = credResponse.data.id;
      
      try {
        await axios.post(`${executorUrl}/execute/${connectorIds.oauth2}/getResource`, {
          credentialId: invalidCredentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(403);
      }
      
      // Clean up
      await axios.delete(`${authUrl}/credentials/${invalidCredentialId}`);
    });
  });
  
  // Test JWT authentication
  describe('JWT Authentication', () => {
    it('should register the JWT connector', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, connectors.jwt);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      connectorIds.jwt = response.data.id;
      credentials.jwt.connectorId = response.data.id;
    });
    
    it('should store JWT credentials', async () => {
      const response = await axios.post(`${authUrl}/credentials`, credentials.jwt);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      credentialIds.jwt = response.data.id;
    });
    
    it('should execute an endpoint with JWT authentication', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorIds.jwt}/getResource`, {
        credentialId: credentialIds.jwt,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('resourceId', 'resource-1');
      expect(response.data).toHaveProperty('resourceName', 'Test Resource');
    });
    
    it('should fail with invalid JWT token', async () => {
      // Create invalid credentials
      const invalidCredentials = {
        ...credentials.jwt,
        name: 'Invalid JWT Credential',
        credentials: {
          token: 'invalid-jwt-token'
        }
      };
      
      const credResponse = await axios.post(`${authUrl}/credentials`, invalidCredentials);
      const invalidCredentialId = credResponse.data.id;
      
      try {
        await axios.post(`${executorUrl}/execute/${connectorIds.jwt}/getResource`, {
          credentialId: invalidCredentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(403);
      }
      
      // Clean up
      await axios.delete(`${authUrl}/credentials/${invalidCredentialId}`);
    });
  });
  
  // Test AWS SigV4 authentication
  describe('AWS SigV4 Authentication', () => {
    it('should register the AWS SigV4 connector', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, connectors.awsSigV4);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      connectorIds.awsSigV4 = response.data.id;
      credentials.awsSigV4.connectorId = response.data.id;
    });
    
    it('should store AWS SigV4 credentials', async () => {
      const response = await axios.post(`${authUrl}/credentials`, credentials.awsSigV4);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      credentialIds.awsSigV4 = response.data.id;
    });
    
    it('should execute an endpoint with AWS SigV4 authentication', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorIds.awsSigV4}/getResource`, {
        credentialId: credentialIds.awsSigV4,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('resourceId', 'resource-1');
      expect(response.data).toHaveProperty('resourceName', 'Test Resource');
    });
  });
  
  // Test Custom authentication
  describe('Custom Authentication', () => {
    it('should register the Custom Auth connector', async () => {
      const response = await axios.post(`${registryUrl}/connectors`, connectors.customAuth);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      connectorIds.customAuth = response.data.id;
      credentials.customAuth.connectorId = response.data.id;
    });
    
    it('should store Custom Auth credentials', async () => {
      const response = await axios.post(`${authUrl}/credentials`, credentials.customAuth);
      
      expect(response.status).toBe(201);
      expect(response.data).toHaveProperty('id');
      
      credentialIds.customAuth = response.data.id;
    });
    
    it('should execute an endpoint with Custom authentication', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorIds.customAuth}/getResource`, {
        credentialId: credentialIds.customAuth,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('resourceId', 'resource-1');
      expect(response.data).toHaveProperty('resourceName', 'Test Resource');
    });
    
    it('should fail with invalid Custom Auth value', async () => {
      // Create invalid credentials
      const invalidCredentials = {
        ...credentials.customAuth,
        name: 'Invalid Custom Auth Credential',
        credentials: {
          customAuthValue: 'invalid-custom-auth',
          headerName: 'X-Custom-Auth'
        }
      };
      
      const credResponse = await axios.post(`${authUrl}/credentials`, invalidCredentials);
      const invalidCredentialId = credResponse.data.id;
      
      try {
        await axios.post(`${executorUrl}/execute/${connectorIds.customAuth}/getResource`, {
          credentialId: invalidCredentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(403);
      }
      
      // Clean up
      await axios.delete(`${authUrl}/credentials/${invalidCredentialId}`);
    });
  });
  
  // Verify request headers
  describe('Request Headers Verification', () => {
    it('should verify that the correct authentication headers were sent', async () => {
      const response = await axios.get(`${mockApiUrl}/history`);
      const requests = response.data;
      
      // API Key request
      const apiKeyRequest = requests.find(req => 
        req.path === '/api-key/resource' && 
        req.headers['x-api-key'] === 'valid-api-key'
      );
      expect(apiKeyRequest).toBeDefined();
      
      // Basic Auth request
      const basicAuthRequest = requests.find(req => 
        req.path === '/basic-auth/resource' && 
        req.headers.authorization && 
        req.headers.authorization.startsWith('Basic ')
      );
      expect(basicAuthRequest).toBeDefined();
      
      // OAuth2 request
      const oauth2Request = requests.find(req => 
        req.path === '/oauth2/resource' && 
        req.headers.authorization && 
        req.headers.authorization.startsWith('Bearer test-access-token')
      );
      expect(oauth2Request).toBeDefined();
      
      // JWT request
      const jwtRequest = requests.find(req => 
        req.path === '/jwt/resource' && 
        req.headers.authorization && 
        req.headers.authorization.startsWith('Bearer test-jwt-token')
      );
      expect(jwtRequest).toBeDefined();
      
      // AWS SigV4 request
      const awsRequest = requests.find(req => 
        req.path === '/aws/resource' && 
        req.headers['x-amz-date'] && 
        req.headers['x-api-key'] === 'test-access-key'
      );
      expect(awsRequest).toBeDefined();
      
      // Custom Auth request
      const customAuthRequest = requests.find(req => 
        req.path === '/custom/resource' && 
        req.headers['x-custom-auth'] === 'custom-auth-value'
      );
      expect(customAuthRequest).toBeDefined();
    });
  });
});
