const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /governance/esg/targets:
 *   get:
 *     summary: Get a list of ESG targets
 *     description: Returns a paginated list of ESG targets with optional filtering
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by target category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance]
 *       - name: status
 *         in: query
 *         description: Filter by target status
 *         schema:
 *           type: string
 *           enum: [planned, in-progress, achieved, missed, cancelled]
 *       - name: metricId
 *         in: query
 *         description: Filter by associated metric ID
 *         schema:
 *           type: string
 *       - name: targetDateBefore
 *         in: query
 *         description: Filter by target date before (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *       - name: targetDateAfter
 *         in: query
 *         description: Filter by target date after (format YYYY-MM-DD)
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGTarget'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', controllers.getTargets);

/**
 * @swagger
 * /governance/esg/targets/{id}:
 *   get:
 *     summary: Get a specific ESG target
 *     description: Returns a specific ESG target by ID
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGTarget'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', controllers.getTargetById);

/**
 * @swagger
 * /governance/esg/targets:
 *   post:
 *     summary: Create a new ESG target
 *     description: Creates a new ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               subcategory:
 *                 type: string
 *               metricId:
 *                 type: string
 *               targetValue:
 *                 type: string
 *               baselineValue:
 *                 type: string
 *               baselineDate:
 *                 type: string
 *                 format: date
 *               targetDate:
 *                 type: string
 *                 format: date
 *               unit:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, achieved, missed, cancelled]
 *               owner:
 *                 type: string
 *               initiatives:
 *                 type: array
 *                 items:
 *                   type: string
 *               milestones:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     targetValue:
 *                       type: string
 *                     targetDate:
 *                       type: string
 *                       format: date
 *                     status:
 *                       type: string
 *                       enum: [planned, in-progress, achieved, missed, cancelled]
 *             required:
 *               - name
 *               - description
 *               - category
 *               - targetValue
 *               - targetDate
 *               - status
 *     responses:
 *       201:
 *         description: Target created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGTarget'
 *                 message:
 *                   type: string
 *                   example: ESG target created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/', validateRequest('createTarget'), controllers.createTarget);

/**
 * @swagger
 * /governance/esg/targets/{id}:
 *   put:
 *     summary: Update an ESG target
 *     description: Updates an existing ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [environmental, social, governance]
 *               subcategory:
 *                 type: string
 *               metricId:
 *                 type: string
 *               targetValue:
 *                 type: string
 *               baselineValue:
 *                 type: string
 *               baselineDate:
 *                 type: string
 *                 format: date
 *               targetDate:
 *                 type: string
 *                 format: date
 *               unit:
 *                 type: string
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, achieved, missed, cancelled]
 *               owner:
 *                 type: string
 *               initiatives:
 *                 type: array
 *                 items:
 *                   type: string
 *               milestones:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     targetValue:
 *                       type: string
 *                     targetDate:
 *                       type: string
 *                       format: date
 *                     status:
 *                       type: string
 *                       enum: [planned, in-progress, achieved, missed, cancelled]
 *     responses:
 *       200:
 *         description: Target updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGTarget'
 *                 message:
 *                   type: string
 *                   example: ESG target updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id', validateRequest('updateTarget'), controllers.updateTarget);

/**
 * @swagger
 * /governance/esg/targets/{id}:
 *   delete:
 *     summary: Delete an ESG target
 *     description: Deletes an existing ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Target deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG target deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id', controllers.deleteTarget);

/**
 * @swagger
 * /governance/esg/targets/{id}/progress:
 *   get:
 *     summary: Get progress records for a target
 *     description: Returns a paginated list of progress records for a specific ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TargetProgress'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id/progress', controllers.getTargetProgress);

/**
 * @swagger
 * /governance/esg/targets/{id}/progress:
 *   post:
 *     summary: Add a progress record to a target
 *     description: Adds a new progress record to a specific ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               value:
 *                 type: string
 *               percentComplete:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               notes:
 *                 type: string
 *             required:
 *               - date
 *               - value
 *     responses:
 *       201:
 *         description: Progress record added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/TargetProgress'
 *                 message:
 *                   type: string
 *                   example: Progress record added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:id/progress', validateRequest('createProgress'), controllers.addProgressRecord);

/**
 * @swagger
 * /governance/esg/targets/{id}/progress/{progressId}:
 *   put:
 *     summary: Update a progress record
 *     description: Updates an existing progress record for a specific ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: progressId
 *         in: path
 *         description: Progress record ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               value:
 *                 type: string
 *               percentComplete:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Progress record updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/TargetProgress'
 *                 message:
 *                   type: string
 *                   example: Progress record updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id/progress/:progressId', validateRequest('updateProgress'), controllers.updateProgressRecord);

/**
 * @swagger
 * /governance/esg/targets/{id}/progress/{progressId}:
 *   delete:
 *     summary: Delete a progress record
 *     description: Deletes an existing progress record for a specific ESG target
 *     tags: [ESG Targets]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG target ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: progressId
 *         in: path
 *         description: Progress record ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Progress record deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Progress record deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id/progress/:progressId', controllers.deleteProgressRecord);

module.exports = router;
