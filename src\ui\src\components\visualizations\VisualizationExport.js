import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Dialog,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Slider,
  TextField,
  Typography,
  CircularProgress,
  Snackbar,
  Alert
} from '@mui/material';
import {
  Download as DownloadIcon,
  Image as ImageIcon,
  Videocam as VideoIcon,
  GifBox as GifIcon,
  Screenshot as ScreenshotIcon
} from '@mui/icons-material';
import visualizationExportService from '../../services/VisualizationExportService';

/**
 * VisualizationExport component
 * 
 * Provides controls for exporting visualizations as images, videos, or GIFs
 */
function VisualizationExport({ canvasRef, visualizationType }) {
  const [open, setOpen] = useState(false);
  const [exportType, setExportType] = useState('image');
  const [imageFormat, setImageFormat] = useState('png');
  const [imageQuality, setImageQuality] = useState(0.9);
  const [fileName, setFileName] = useState(`${visualizationType}-${Date.now()}`);
  const [videoDuration, setVideoDuration] = useState(5);
  const [videoFps, setVideoFps] = useState(30);
  const [gifDuration, setGifDuration] = useState(3);
  const [gifFps, setGifFps] = useState(10);
  const [gifQuality, setGifQuality] = useState(10);
  const [isExporting, setIsExporting] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');
  
  // Open dialog
  const handleOpen = () => {
    setOpen(true);
  };
  
  // Close dialog
  const handleClose = () => {
    setOpen(false);
  };
  
  // Export visualization
  const handleExport = async () => {
    if (!canvasRef.current) {
      showSnackbar('Canvas not available', 'error');
      return;
    }
    
    try {
      setIsExporting(true);
      
      // Get canvas element
      const canvas = canvasRef.current.querySelector('canvas');
      
      if (!canvas) {
        throw new Error('Canvas element not found');
      }
      
      // Export based on type
      switch (exportType) {
        case 'image':
          await visualizationExportService.exportAsImage(canvas, {
            format: imageFormat,
            quality: imageQuality,
            fileName
          });
          showSnackbar(`Image exported as ${fileName}.${imageFormat}`);
          break;
          
        case 'video':
          await visualizationExportService.exportAsVideo(canvas, {
            duration: videoDuration,
            fps: videoFps,
            fileName
          });
          showSnackbar(`Video exported as ${fileName}.webm`);
          break;
          
        case 'gif':
          await visualizationExportService.createAnimatedGif(canvas, {
            duration: gifDuration,
            fps: gifFps,
            fileName,
            quality: gifQuality,
            width: canvas.width / 2,
            height: canvas.height / 2
          });
          showSnackbar(`GIF exported as ${fileName}.gif`);
          break;
          
        case 'screenshot':
          const dataUrl = visualizationExportService.captureScreenshot(canvas);
          
          // Create download link
          const link = document.createElement('a');
          link.href = dataUrl;
          link.download = `${fileName}-screenshot.png`;
          
          // Trigger download
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          
          showSnackbar(`Screenshot exported as ${fileName}-screenshot.png`);
          break;
          
        default:
          throw new Error(`Unsupported export type: ${exportType}`);
      }
      
      // Close dialog
      handleClose();
    } catch (error) {
      console.error('Error exporting visualization:', error);
      showSnackbar(`Error exporting visualization: ${error.message}`, 'error');
    } finally {
      setIsExporting(false);
    }
  };
  
  // Show snackbar
  const showSnackbar = (message, severity = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };
  
  // Close snackbar
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        startIcon={<DownloadIcon />}
        onClick={handleOpen}
        size="small"
      >
        Export
      </Button>
      
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle>Export Visualization</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2, mb: 2 }}>
            <InputLabel id="export-type-label">Export Type</InputLabel>
            <Select
              labelId="export-type-label"
              id="export-type"
              value={exportType}
              label="Export Type"
              onChange={(e) => setExportType(e.target.value)}
            >
              <MenuItem value="image">Image</MenuItem>
              <MenuItem value="video">Video</MenuItem>
              <MenuItem value="gif">Animated GIF</MenuItem>
              <MenuItem value="screenshot">Screenshot</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            fullWidth
            label="File Name"
            value={fileName}
            onChange={(e) => setFileName(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          {exportType === 'image' && (
            <>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="image-format-label">Image Format</InputLabel>
                <Select
                  labelId="image-format-label"
                  id="image-format"
                  value={imageFormat}
                  label="Image Format"
                  onChange={(e) => setImageFormat(e.target.value)}
                >
                  <MenuItem value="png">PNG</MenuItem>
                  <MenuItem value="jpg">JPG</MenuItem>
                </Select>
              </FormControl>
              
              {imageFormat === 'jpg' && (
                <>
                  <Typography gutterBottom>Image Quality</Typography>
                  <Slider
                    value={imageQuality}
                    onChange={(e, newValue) => setImageQuality(newValue)}
                    min={0.1}
                    max={1}
                    step={0.1}
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                    sx={{ mb: 2 }}
                  />
                </>
              )}
            </>
          )}
          
          {exportType === 'video' && (
            <>
              <Typography gutterBottom>Video Duration (seconds)</Typography>
              <Slider
                value={videoDuration}
                onChange={(e, newValue) => setVideoDuration(newValue)}
                min={1}
                max={30}
                step={1}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
              
              <Typography gutterBottom>Frames Per Second</Typography>
              <Slider
                value={videoFps}
                onChange={(e, newValue) => setVideoFps(newValue)}
                min={10}
                max={60}
                step={5}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
            </>
          )}
          
          {exportType === 'gif' && (
            <>
              <Typography gutterBottom>GIF Duration (seconds)</Typography>
              <Slider
                value={gifDuration}
                onChange={(e, newValue) => setGifDuration(newValue)}
                min={1}
                max={10}
                step={1}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
              
              <Typography gutterBottom>Frames Per Second</Typography>
              <Slider
                value={gifFps}
                onChange={(e, newValue) => setGifFps(newValue)}
                min={5}
                max={30}
                step={5}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
              
              <Typography gutterBottom>Quality (lower is better)</Typography>
              <Slider
                value={gifQuality}
                onChange={(e, newValue) => setGifQuality(newValue)}
                min={1}
                max={20}
                step={1}
                valueLabelDisplay="auto"
                sx={{ mb: 2 }}
              />
            </>
          )}
          
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              {exportType === 'image' && (
                <>
                  <ImageIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                  Exports the current view as a static image.
                </>
              )}
              {exportType === 'video' && (
                <>
                  <VideoIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                  Records a video of the visualization with rotation.
                </>
              )}
              {exportType === 'gif' && (
                <>
                  <GifIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                  Creates an animated GIF of the visualization.
                </>
              )}
              {exportType === 'screenshot' && (
                <>
                  <ScreenshotIcon fontSize="small" sx={{ verticalAlign: 'middle', mr: 0.5 }} />
                  Takes a quick screenshot of the current view.
                </>
              )}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} disabled={isExporting}>
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            variant="contained"
            color="primary"
            disabled={isExporting}
            startIcon={isExporting ? <CircularProgress size={20} /> : <DownloadIcon />}
          >
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DialogActions>
      </Dialog>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
}

export default VisualizationExport;
