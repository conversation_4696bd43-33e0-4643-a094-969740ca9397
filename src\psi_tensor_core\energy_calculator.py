#!/usr/bin/env python3
"""
Domain-Specific Energy Calculator for Ψ Tensor Core

This module calculates domain-specific energies (E) and their gradients (∇E)
for the CSDE, CSFE, and CSME engines.

Energy definitions:
- E_CSDE = A₁ × D (Risk × Data relevance)
- E_CSFE = A₂ × P (Alignment accuracy × Policy relevance)
- E_CSME = T × I (Trust × Integrity)

Gradients are calculated as the time derivative of energy:
∇E_x = dE_x/dt ≈ (E_x[t] - E_x[t-1]) / Δt
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Union, Optional, Any
import time

class EnergyCalculator:
    """
    Calculates domain-specific energies and their gradients for the Ψ Tensor Core.
    """

    def __init__(self):
        """
        Initialize the Energy Calculator.
        """
        # Initialize history
        self.history = {
            "CSDE": {
                "timestamps": [],
                "energies": []
            },
            "CSFE": {
                "timestamps": [],
                "energies": []
            },
            "CSME": {
                "timestamps": [],
                "energies": []
            }
        }

        # Initialize last update time
        self.last_update_time = time.time()

    def calculate_csde_energy(self, csde_tensor: np.ndarray) -> float:
        """
        Calculate the domain-specific energy for CSDE.

        E_CSDE = A₁ × D (Risk × Data relevance)

        Args:
            csde_tensor: CSDE tensor [G, D, A₁, c₁]

        Returns:
            Domain-specific energy for CSDE
        """
        # Extract components
        G = csde_tensor[0]  # Governance input signal
        D = csde_tensor[1]  # Data relevance signal
        A1 = csde_tensor[2]  # Risk classification or threat vector
        c1 = csde_tensor[3]  # Contextual accuracy or confidence

        # Calculate energy
        E_CSDE = A1 * D

        return E_CSDE

    def calculate_csfe_energy(self, csfe_tensor: np.ndarray) -> float:
        """
        Calculate the domain-specific energy for CSFE.

        E_CSFE = A₂ × P (Alignment accuracy × Policy relevance)

        Args:
            csfe_tensor: CSFE tensor [F₁, P, A₂, c₂]

        Returns:
            Domain-specific energy for CSFE
        """
        # Extract components
        F1 = csfe_tensor[0]  # Framework signal (renamed from R in your description)
        P = csfe_tensor[1]   # Policy relevance (renamed from φ in your description)
        A2 = csfe_tensor[2]  # Alignment accuracy
        c2 = csfe_tensor[3]  # Confidence

        # Calculate energy
        E_CSFE = A2 * P

        return E_CSFE

    def calculate_csme_energy(self, csme_tensor: np.ndarray) -> float:
        """
        Calculate the domain-specific energy for CSME.

        E_CSME = T × I (Trust × Integrity)

        Args:
            csme_tensor: CSME tensor [T, I, E, c₃]

        Returns:
            Domain-specific energy for CSME
        """
        # Extract components
        T = csme_tensor[0]  # Trust (renamed from B in your description)
        I = csme_tensor[1]  # Integrity (renamed from Γ in your description)
        E = csme_tensor[2]  # Ethical component
        c3 = csme_tensor[3]  # Confidence

        # Calculate energy
        E_CSME = T * I

        return E_CSME

    def calculate_gradient(self,
                          current_energy: float,
                          engine_type: str,
                          timestamp: Optional[float] = None) -> float:
        """
        Calculate the gradient (time derivative) of energy.

        ∇E_x = dE_x/dt ≈ (E_x[t] - E_x[t-1]) / Δt

        Args:
            current_energy: Current energy value
            engine_type: Engine type ("CSDE", "CSFE", or "CSME")
            timestamp: Current timestamp (default: current time)

        Returns:
            Gradient of energy
        """
        # Use current time if timestamp is not provided
        if timestamp is None:
            timestamp = time.time()

        # Get history for the specified engine
        engine_history = self.history[engine_type]

        # Add current energy to history
        engine_history["timestamps"].append(timestamp)
        engine_history["energies"].append(current_energy)

        # Calculate gradient
        if len(engine_history["energies"]) < 2:
            # Not enough history to calculate gradient
            gradient = 0.0
        else:
            # Calculate time difference
            delta_time = engine_history["timestamps"][-1] - engine_history["timestamps"][-2]

            # Ensure delta_time is not zero
            if delta_time < 1e-6:
                delta_time = 1e-6

            # Calculate energy difference
            delta_energy = engine_history["energies"][-1] - engine_history["energies"][-2]

            # Calculate gradient
            gradient = delta_energy / delta_time

        return gradient

    def calculate_comphyon(self,
                          csde_tensor: np.ndarray,
                          csfe_tensor: np.ndarray,
                          csme_tensor: np.ndarray,
                          timestamp: Optional[float] = None) -> Dict[str, Any]:
        """
        Calculate the Comphyon value based on domain-specific energies and their gradients.

        Cph = ((∇E_CSDE ∘ ∇E_CSFE) × log(E_CSME)) / 166000

        Args:
            csde_tensor: CSDE tensor [G, D, A₁, c₁]
            csfe_tensor: CSFE tensor [F₁, P, A₂, c₂]
            csme_tensor: CSME tensor [T, I, E, c₃]
            timestamp: Current timestamp (default: current time)

        Returns:
            Dictionary containing Comphyon value, energies, and gradients
        """
        # Use current time if timestamp is not provided
        if timestamp is None:
            timestamp = time.time()

        # Calculate domain-specific energies
        E_CSDE = self.calculate_csde_energy(csde_tensor)
        E_CSFE = self.calculate_csfe_energy(csfe_tensor)
        E_CSME = self.calculate_csme_energy(csme_tensor)

        # Calculate gradients
        dE_CSDE = self.calculate_gradient(E_CSDE, "CSDE", timestamp)
        dE_CSFE = self.calculate_gradient(E_CSFE, "CSFE", timestamp)
        dE_CSME = self.calculate_gradient(E_CSME, "CSME", timestamp)

        # Calculate Comphyon values using both formulas
        # Avoid log(0) by adding a small epsilon
        epsilon = 1e-5

        # 1. Original Formula (Theoretical Rigor) - "Emergent Intelligence Acceleration"
        # Preserves sign information and interaction effects (Hadamard product)
        # ∇²E_unified = (∇E_CSDE * ∇E_CSFE) * log(E_CSME)
        cph_acceleration = (dE_CSDE * dE_CSFE) * math.log(abs(E_CSME) + epsilon)

        # Apply scaling factor derived from π10³
        cph_acceleration = cph_acceleration * 3142

        # 2. Simplified Formula (Operational Metric) - "Domain Energy Flux"
        # Uses absolute values for more intuitive interpretation
        # Represents the combined velocity of domain energies
        cph_velocity = (abs(dE_CSDE) + abs(dE_CSFE)) * math.log(abs(E_CSME) + epsilon)

        # Apply scaling if needed
        if abs(cph_velocity) < 0.001:
            cph_velocity = cph_velocity * 1000

        # Update last update time
        self.last_update_time = timestamp

        # Create result with both Comphyon metrics
        result = {
            "Comphyon": {
                "acceleration": cph_acceleration,  # Original formula (theoretical rigor)
                "velocity": cph_velocity           # Simplified formula (operational metric)
            },
            "Energies": {
                "CSDE": E_CSDE,
                "CSFE": E_CSFE,
                "CSME": E_CSME
            },
            "Gradients": {
                "CSDE": dE_CSDE,
                "CSFE": dE_CSFE,
                "CSME": dE_CSME
            },
            "Timestamp": timestamp
        }

        return result

    def get_history(self) -> Dict[str, Dict[str, List[float]]]:
        """
        Get the energy history.

        Returns:
            Dictionary containing energy history for each engine
        """
        return self.history

    def clear_history(self):
        """
        Clear the energy history.
        """
        for engine_type in self.history:
            self.history[engine_type]["timestamps"] = []
            self.history[engine_type]["energies"] = []
