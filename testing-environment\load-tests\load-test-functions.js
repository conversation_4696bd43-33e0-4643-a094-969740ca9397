/**
 * Artillery load test functions
 */

// Test data
const connectors = [
  {
    id: 'aws-connector-id',
    name: 'AWS Security Hub',
    endpoints: ['getFindings']
  },
  {
    id: 'okta-connector-id',
    name: 'Ok<PERSON>',
    endpoints: ['getUsers']
  },
  {
    id: 'jira-connector-id',
    name: '<PERSON><PERSON>',
    endpoints: ['getIssues']
  }
];

const credentials = [
  {
    id: 'aws-credential-id',
    connectorId: 'aws-connector-id'
  },
  {
    id: 'okta-credential-id',
    connectorId: 'okta-connector-id'
  },
  {
    id: 'jira-credential-id',
    connectorId: 'jira-connector-id'
  }
];

const userIds = [
  'user-1',
  'user-2',
  'user-3',
  'user-4',
  'user-5'
];

// Generate random credentials for the test
function generateCredentials(userContext, events, done) {
  // Select a random connector based on the scenario
  let connector;
  
  if (userContext.vars.name.includes('AWS')) {
    connector = connectors[0];
  } else if (userContext.vars.name.includes('Okta')) {
    connector = connectors[1];
  } else if (userContext.vars.name.includes('Jira')) {
    connector = connectors[2];
  } else {
    // Default to a random connector
    connector = connectors[Math.floor(Math.random() * connectors.length)];
  }
  
  // Select a random credential for the connector
  const credential = credentials.find(c => c.connectorId === connector.id);
  
  // Select a random user ID
  const userId = userIds[Math.floor(Math.random() * userIds.length)];
  
  // Set the variables in the user context
  userContext.vars.connectorId = connector.id;
  userContext.vars.credentialId = credential.id;
  userContext.vars.userId = userId;
  
  return done();
}

module.exports = {
  generateCredentials
};
