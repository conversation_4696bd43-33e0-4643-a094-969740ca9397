/**
 * BigQuery Connector
 * Connector for scanning Google BigQuery datasets for personal data
 */

const { BigQuery } = require('@google-cloud/bigquery');
const BaseConnector = require('./baseConnector');

/**
 * Connector for Google BigQuery
 * @extends BaseConnector
 */
class BigQueryConnector extends BaseConnector {
  /**
   * Create a new BigQuery connector
   */
  constructor() {
    super('bigquery');
    this.client = null;
    this.isAuthenticated = false;
  }

  /**
   * Authenticate with BigQuery
   * @param {Object} credentials - Authentication credentials
   * @param {string} credentials.projectId - Google Cloud project ID
   * @param {string} credentials.keyFilename - Path to service account key file (optional)
   * @param {Object} credentials.credentials - Service account credentials object (optional)
   * @returns {Promise<void>}
   */
  async authenticate(credentials) {
    try {
      const options = {
        projectId: credentials.projectId
      };

      // Add credentials if provided
      if (credentials.keyFilename) {
        options.keyFilename = credentials.keyFilename;
      } else if (credentials.credentials) {
        options.credentials = credentials.credentials;
      }

      this.client = new BigQuery(options);
      
      // Test the connection
      await this.client.getDatasets();
      
      this.isAuthenticated = true;
    } catch (error) {
      throw new Error(`BigQuery authentication failed: ${error.message}`);
    }
  }

  /**
   * Scan BigQuery datasets for personal data
   * @param {Object} options - Scan options
   * @param {Array} options.patterns - Patterns to search for
   * @param {string} options.depth - Scan depth ('basic', 'standard', 'deep')
   * @param {number} options.sampleSize - Number of rows to sample
   * @returns {Promise<Array>} Scan results
   */
  async scanData(options) {
    this._checkAuthentication();
    
    const results = [];
    
    try {
      // Get all datasets
      const [datasets] = await this.client.getDatasets();
      
      for (const dataset of datasets) {
        // Get dataset metadata
        const [datasetMeta] = await dataset.getMetadata();
        
        // Skip if this is a temporary or system dataset
        if (datasetMeta.id.startsWith('_') || datasetMeta.id.startsWith('temp_')) {
          continue;
        }
        
        // Get all tables in the dataset
        const [tables] = await dataset.getTables();
        
        for (const table of tables) {
          // Get table metadata
          const [tableMeta] = await table.getMetadata();
          
          // Skip if this is a temporary or system table
          if (tableMeta.tableReference.tableId.startsWith('_') || 
              tableMeta.tableReference.tableId.startsWith('temp_')) {
            continue;
          }
          
          // Scan the table
          const tableResults = await this._scanTable(
            dataset.id,
            table.id,
            tableMeta,
            options
          );
          
          results.push(...tableResults);
        }
      }
      
      return results;
    } catch (error) {
      throw new Error(`BigQuery scan failed: ${error.message}`);
    }
  }

  /**
   * Scan a specific BigQuery table
   * @private
   * @param {string} datasetId - Dataset ID
   * @param {string} tableId - Table ID
   * @param {Object} tableMeta - Table metadata
   * @param {Object} options - Scan options
   * @returns {Promise<Array>} Scan results for the table
   */
  async _scanTable(datasetId, tableId, tableMeta, options) {
    const results = [];
    const { patterns, sampleSize } = options;
    
    try {
      // Build a query to sample data from the table
      let query = `SELECT * FROM \`${datasetId}.${tableId}\``;
      
      // Add sampling if specified
      if (sampleSize && sampleSize > 0) {
        query += ` LIMIT ${sampleSize}`;
      }
      
      // Execute the query
      const [rows] = await this.client.query(query);
      
      // Check each row for matches to privacy patterns
      rows.forEach((row, rowIndex) => {
        Object.entries(row).forEach(([column, value]) => {
          // Skip null values
          if (value === null || value === undefined) {
            return;
          }
          
          // Convert value to string for pattern matching
          const stringValue = String(value);
          
          // Check each pattern for a match
          patterns.forEach(pattern => {
            if (pattern.regex.test(stringValue)) {
              results.push({
                source: {
                  type: 'bigquery',
                  projectId: this.client.projectId,
                  datasetId,
                  tableId,
                  column
                },
                pattern: pattern.id,
                category: pattern.category,
                value: this._maskSensitiveData(stringValue, pattern),
                rowIndex,
                confidence: this._calculateConfidence(stringValue, pattern)
              });
            }
          });
        });
      });
      
      return results;
    } catch (error) {
      console.warn(`Error scanning table ${datasetId}.${tableId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Mask sensitive data for reporting
   * @private
   * @param {string} value - Original value
   * @param {Object} pattern - Pattern that matched
   * @returns {string} Masked value
   */
  _maskSensitiveData(value, pattern) {
    // If the value is short, replace with asterisks
    if (value.length < 20) {
      return '*'.repeat(value.length);
    }
    
    // For longer values, keep first and last characters
    const firstChars = value.substring(0, 2);
    const lastChars = value.substring(value.length - 2);
    return `${firstChars}${'*'.repeat(6)}${lastChars}`;
  }

  /**
   * Calculate confidence score for a match
   * @private
   * @param {string} value - Matched value
   * @param {Object} pattern - Pattern that matched
   * @returns {number} Confidence score (0-1)
   */
  _calculateConfidence(value, pattern) {
    // Base confidence from the pattern
    let confidence = pattern.baseConfidence || 0.7;
    
    // Adjust based on additional factors
    
    // Length factor - longer values that match patterns are more likely to be correct
    const lengthFactor = Math.min(value.length / 10, 1) * 0.1;
    
    // Format factor - if the value perfectly matches the expected format
    const formatFactor = pattern.strictRegex && pattern.strictRegex.test(value) ? 0.2 : 0;
    
    // Combine factors
    confidence += lengthFactor + formatFactor;
    
    // Ensure confidence is between 0 and 1
    return Math.min(Math.max(confidence, 0), 1);
  }
}

module.exports = BigQueryConnector;
