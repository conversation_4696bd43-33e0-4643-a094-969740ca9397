/**
 * Unit tests for the Connector Executor
 */

const path = require('path');
const fs = require('fs').promises;
const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');
const connectorExecutor = require('../../connector-executor');
const connectorRegistry = require('../../registry/connector-registry');
const { mockGoogleCloudConnector } = require('../mocks/mock-connector');

// Mock axios
const mockAxios = new MockAdapter(axios);

// Mock connector registry
jest.mock('../../registry/connector-registry', () => ({
  getConnector: jest.fn(),
  initialize: jest.fn().mockResolvedValue(true)
}));

describe('Connector Executor', () => {
  beforeEach(() => {
    // Reset mocks
    mockAxios.reset();
    jest.clearAllMocks();
  });
  
  test('should execute a connector endpoint successfully', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);
    
    // Mock axios response
    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings')
      .reply(200, {
        findings: [
          {
            name: 'finding-1',
            parent: 'organizations/123/sources/456',
            resourceName: 'projects/my-project/instances/my-instance',
            state: 'ACTIVE',
            category: 'VULNERABILITY',
            severity: 'HIGH',
            eventTime: '2023-06-01T00:00:00Z',
            createTime: '2023-06-01T00:00:00Z'
          }
        ],
        nextPageToken: 'next-page-token'
      });
    
    // Execute connector
    const result = await connectorExecutor.executeConnector(
      'google-cloud-security-1.0.0',
      'list-findings',
      {
        path: {
          organizationId: '123',
          sourceId: '456'
        },
        auth: {
          token: 'test-token'
        }
      }
    );
    
    // Verify result
    expect(result.success).toBe(true);
    expect(result.data).toEqual([
      {
        name: 'finding-1',
        parent: 'organizations/123/sources/456',
        resourceName: 'projects/my-project/instances/my-instance',
        state: 'ACTIVE',
        category: 'VULNERABILITY',
        severity: 'HIGH',
        eventTime: '2023-06-01T00:00:00Z',
        createTime: '2023-06-01T00:00:00Z'
      }
    ]);
    expect(result.statusCode).toBe(200);
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');
    
    // Verify axios was called with correct parameters
    expect(mockAxios.history.get.length).toBe(1);
    expect(mockAxios.history.get[0].url).toBe('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings');
    expect(mockAxios.history.get[0].headers.Authorization).toBe('Bearer test-token');
  });
  
  test('should handle connector not found error', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(null);
    
    // Execute connector
    const result = await connectorExecutor.executeConnector(
      'non-existent-connector',
      'list-findings',
      {}
    );
    
    // Verify result
    expect(result.success).toBe(false);
    expect(result.error).toContain('not found');
    expect(result.statusCode).toBe(500);
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('non-existent-connector');
    
    // Verify axios was not called
    expect(mockAxios.history.get.length).toBe(0);
  });
  
  test('should handle endpoint not found error', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);
    
    // Execute connector
    const result = await connectorExecutor.executeConnector(
      'google-cloud-security-1.0.0',
      'non-existent-endpoint',
      {}
    );
    
    // Verify result
    expect(result.success).toBe(false);
    expect(result.error).toContain('not found');
    expect(result.statusCode).toBe(500);
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');
    
    // Verify axios was not called
    expect(mockAxios.history.get.length).toBe(0);
  });
  
  test('should handle API error', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);
    
    // Mock axios response
    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings')
      .reply(403, {
        error: {
          code: 403,
          message: 'Permission denied',
          status: 'PERMISSION_DENIED'
        }
      });
    
    // Execute connector
    const result = await connectorExecutor.executeConnector(
      'google-cloud-security-1.0.0',
      'list-findings',
      {
        path: {
          organizationId: '123',
          sourceId: '456'
        },
        auth: {
          token: 'test-token'
        }
      }
    );
    
    // Verify result
    expect(result.success).toBe(false);
    expect(result.statusCode).toBe(403);
    
    // Verify connector registry was called
    expect(connectorRegistry.getConnector).toHaveBeenCalledWith('google-cloud-security-1.0.0');
    
    // Verify axios was called with correct parameters
    expect(mockAxios.history.get.length).toBe(1);
    expect(mockAxios.history.get[0].url).toBe('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings');
  });
  
  test('should update metrics after execution', async () => {
    // Mock connector registry response
    connectorRegistry.getConnector.mockReturnValue(mockGoogleCloudConnector);
    
    // Mock axios response
    mockAxios.onGet('https://securitycenter.googleapis.com/v1/organizations/123/sources/456/findings')
      .reply(200, {
        findings: [],
        nextPageToken: ''
      });
    
    // Get initial metrics
    const initialMetrics = connectorExecutor.getMetrics();
    
    // Execute connector
    await connectorExecutor.executeConnector(
      'google-cloud-security-1.0.0',
      'list-findings',
      {
        path: {
          organizationId: '123',
          sourceId: '456'
        }
      }
    );
    
    // Get updated metrics
    const updatedMetrics = connectorExecutor.getMetrics();
    
    // Verify metrics were updated
    expect(updatedMetrics.totalRequests).toBe(initialMetrics.totalRequests + 1);
    expect(updatedMetrics.successfulRequests).toBe(initialMetrics.successfulRequests + 1);
  });
});
