#!/usr/bin/env python3
"""
Integrate LBL-TCP-3 data analysis results with the UUFT framework.
This script takes the results from the LBL-TCP-3 analysis and integrates them
with the UUFT cross-domain analysis framework.
"""

import os
import sys
import json
import logging
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('uuft_lbl_tcp_integration.log')
    ]
)
logger = logging.getLogger('UUFT_LBL_TCP_Integration')

# Constants
RESULTS_DIR = "results"
LBL_TCP_RESULTS_DIR = "lbl_tcp_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Helper function to make objects JSON serializable
def json_serializable(obj):
    """Convert any non-serializable values to strings."""
    if isinstance(obj, (np.int8, np.int16, np.int32, np.int64,
                        np.uint8, np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (bool, np.bool_)):
        return bool(obj)
    elif isinstance(obj, (np.ndarray,)):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: json_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serializable(item) for item in obj]
    else:
        return str(obj)

def load_lbl_tcp_results():
    """Load the LBL-TCP-3 analysis results."""
    results_path = os.path.join(LBL_TCP_RESULTS_DIR, "lbl_tcp_analysis_results.json")
    logger.info(f"Loading LBL-TCP-3 analysis results from {results_path}")
    
    with open(results_path, 'r') as f:
        results = json.load(f)
    
    logger.info(f"Loaded LBL-TCP-3 analysis results with {len(results['1882_pattern_results'])} 18/82 pattern results and {len(results['pi_relationship_results'])} Pi relationship results")
    
    return results

def load_uuft_combined_results():
    """Load the UUFT combined results."""
    results_path = os.path.join(RESULTS_DIR, "combined_1882_patterns_results.json")
    logger.info(f"Loading UUFT combined results from {results_path}")
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        logger.info(f"Loaded UUFT combined results with {results['total_tests']} tests across {len(results['domains_tested'])} domains")
        
        return results
    except FileNotFoundError:
        logger.warning(f"UUFT combined results file not found at {results_path}")
        return None

def load_uuft_cross_domain_results():
    """Load the UUFT cross-domain analysis results."""
    results_path = os.path.join(RESULTS_DIR, "cross_domain_analysis_results.json")
    logger.info(f"Loading UUFT cross-domain analysis results from {results_path}")
    
    try:
        with open(results_path, 'r') as f:
            results = json.load(f)
        
        logger.info(f"Loaded UUFT cross-domain analysis results")
        
        return results
    except FileNotFoundError:
        logger.warning(f"UUFT cross-domain analysis results file not found at {results_path}")
        return None

def integrate_lbl_tcp_with_uuft(lbl_tcp_results, uuft_combined_results, uuft_cross_domain_results):
    """Integrate the LBL-TCP-3 results with the UUFT framework."""
    logger.info("Integrating LBL-TCP-3 results with UUFT framework...")
    
    # Extract key metrics from LBL-TCP-3 results
    total_1882_patterns = len(lbl_tcp_results['1882_pattern_results'])
    matching_1882_patterns = sum(1 for r in lbl_tcp_results['1882_pattern_results'] if r['is_1882_pattern'])
    pattern_1882_percentage = matching_1882_patterns / total_1882_patterns * 100 if total_1882_patterns > 0 else 0
    
    total_pi_values = sum(int(r['pi_values_count']) for r in lbl_tcp_results['pi_relationship_results'])
    total_pi_ratios = sum(int(r['pi_ratios_count']) for r in lbl_tcp_results['pi_relationship_results'])
    total_pi_10e3_values = sum(int(r['pi_10e3_values_count']) for r in lbl_tcp_results['pi_relationship_results'])
    total_pi_10e3_ratios = sum(int(r['pi_10e3_ratios_count']) for r in lbl_tcp_results['pi_relationship_results'])
    
    # Create a new technological domain result based on LBL-TCP-3
    lbl_tcp_domain_result = {
        "domain": "technological",
        "dataset": "LBL-TCP-3",
        "description": "Internet traffic data from Lawrence Berkeley National Laboratory",
        "date_analyzed": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "pattern_1882": {
            "total_tests": total_1882_patterns,
            "matching_tests": matching_1882_patterns,
            "percentage": pattern_1882_percentage,
            "details": lbl_tcp_results['1882_pattern_results']
        },
        "pattern_pi": {
            "total_pi_values": total_pi_values,
            "total_pi_ratios": total_pi_ratios,
            "total_pi_10e3_values": total_pi_10e3_values,
            "total_pi_10e3_ratios": total_pi_10e3_ratios,
            "details": lbl_tcp_results['pi_relationship_results']
        }
    }
    
    # Add the LBL-TCP-3 result to the UUFT combined results
    if uuft_combined_results:
        # Check if there's already a technological domain result
        tech_domain_index = None
        for i, result in enumerate(uuft_combined_results['results']):
            if result['domain'] == 'technological':
                tech_domain_index = i
                break
        
        if tech_domain_index is not None:
            # Replace the existing technological domain result
            uuft_combined_results['results'][tech_domain_index] = lbl_tcp_domain_result
        else:
            # Add a new technological domain result
            uuft_combined_results['results'].append(lbl_tcp_domain_result)
        
        # Update the total tests count
        uuft_combined_results['total_tests'] = len(uuft_combined_results['results'])
        
        # Save the updated combined results
        updated_combined_results_path = os.path.join(RESULTS_DIR, "combined_1882_patterns_results_with_lbl_tcp.json")
        try:
            with open(updated_combined_results_path, 'w') as f:
                json.dump(json_serializable(uuft_combined_results), f, indent=2)
            logger.info(f"Updated UUFT combined results saved to {updated_combined_results_path}")
        except Exception as e:
            logger.error(f"Error saving updated UUFT combined results: {e}")
    
    # Create a new cross-domain analysis with LBL-TCP-3 data
    if uuft_cross_domain_results:
        # Extract the cross-domain metrics
        pattern_presence = uuft_cross_domain_results.get('pattern_presence', {})
        pattern_presence['technological'] = pattern_1882_percentage
        
        # Calculate the new overall pattern presence
        overall_pattern_presence = sum(pattern_presence.values()) / len(pattern_presence)
        
        # Update the cross-domain analysis results
        updated_cross_domain_results = {
            "pattern_presence": pattern_presence,
            "overall_pattern_presence": overall_pattern_presence,
            "strongest_pattern": uuft_cross_domain_results.get('strongest_pattern', "Pi relationships"),
            "strongest_domain": "technological" if pattern_1882_percentage > max(pattern_presence.values()) else uuft_cross_domain_results.get('strongest_domain', "cosmological"),
            "universal_patterns_indicated": overall_pattern_presence > 50,
            "cross_domain_consistency": uuft_cross_domain_results.get('cross_domain_consistency', False),
            "pattern_interdependence": uuft_cross_domain_results.get('pattern_interdependence', False),
            "date_analyzed": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "lbl_tcp_integration": {
                "original_data": lbl_tcp_results,
                "integration_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "integration_notes": "LBL-TCP-3 data integrated as a real-world technological domain dataset"
            }
        }
        
        # Save the updated cross-domain analysis results
        updated_cross_domain_results_path = os.path.join(RESULTS_DIR, "cross_domain_analysis_results_with_lbl_tcp.json")
        try:
            with open(updated_cross_domain_results_path, 'w') as f:
                json.dump(json_serializable(updated_cross_domain_results), f, indent=2)
            logger.info(f"Updated UUFT cross-domain analysis results saved to {updated_cross_domain_results_path}")
        except Exception as e:
            logger.error(f"Error saving updated UUFT cross-domain analysis results: {e}")
    
    # Create a standalone integration report
    integration_report = {
        "integration_type": "Real-world technological data integration",
        "dataset": "LBL-TCP-3",
        "description": "Internet traffic data from Lawrence Berkeley National Laboratory",
        "date_integrated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "pattern_1882_summary": {
            "total_tests": total_1882_patterns,
            "matching_tests": matching_1882_patterns,
            "percentage": pattern_1882_percentage
        },
        "pattern_pi_summary": {
            "total_pi_values": total_pi_values,
            "total_pi_ratios": total_pi_ratios,
            "total_pi_10e3_values": total_pi_10e3_values,
            "total_pi_10e3_ratios": total_pi_10e3_ratios
        },
        "uuft_alignment": {
            "18/82_pattern_alignment": pattern_1882_percentage / 100,
            "pi_relationship_alignment": (total_pi_values + total_pi_ratios + total_pi_10e3_values + total_pi_10e3_ratios) / (4 * total_1882_patterns) if total_1882_patterns > 0 else 0
        }
    }
    
    # Save the integration report
    integration_report_path = os.path.join(RESULTS_DIR, "lbl_tcp_uuft_integration_report.json")
    try:
        with open(integration_report_path, 'w') as f:
            json.dump(json_serializable(integration_report), f, indent=2)
        logger.info(f"LBL-TCP-3 UUFT integration report saved to {integration_report_path}")
    except Exception as e:
        logger.error(f"Error saving LBL-TCP-3 UUFT integration report: {e}")
    
    return integration_report

def main():
    """Main function to integrate LBL-TCP-3 data with UUFT framework."""
    logger.info("Starting LBL-TCP-3 integration with UUFT framework...")
    
    # Load the results
    lbl_tcp_results = load_lbl_tcp_results()
    uuft_combined_results = load_uuft_combined_results()
    uuft_cross_domain_results = load_uuft_cross_domain_results()
    
    # Integrate the results
    integration_report = integrate_lbl_tcp_with_uuft(lbl_tcp_results, uuft_combined_results, uuft_cross_domain_results)
    
    # Print summary
    logger.info("\n=== LBL-TCP-3 UUFT Integration Summary ===")
    logger.info(f"18/82 Pattern Alignment: {integration_report['uuft_alignment']['18/82_pattern_alignment']:.2f}")
    logger.info(f"Pi Relationship Alignment: {integration_report['uuft_alignment']['pi_relationship_alignment']:.2f}")
    logger.info(f"Integration Report saved to {os.path.join(RESULTS_DIR, 'lbl_tcp_uuft_integration_report.json')}")
    
    return integration_report

if __name__ == "__main__":
    main()
