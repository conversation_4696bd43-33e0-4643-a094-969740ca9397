const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const port = 3006;

// In-memory storage for connectors
const connectors = [];

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Get all connectors
app.get('/connectors', (req, res) => {
  res.json(connectors);
});

// Get connector by ID
app.get('/connectors/:id', (req, res) => {
  const connector = connectors.find(c => c.id === req.params.id);
  if (!connector) {
    return res.status(404).json({ error: 'Connector not found' });
  }
  res.json(connector);
});

// Create a new connector
app.post('/connectors', (req, res) => {
  const connector = {
    id: `connector-${Date.now()}`,
    ...req.body,
    created: new Date().toISOString(),
    updated: new Date().toISOString()
  };
  
  connectors.push(connector);
  res.status(201).json(connector);
});

// Update a connector
app.put('/connectors/:id', (req, res) => {
  const index = connectors.findIndex(c => c.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Connector not found' });
  }
  
  connectors[index] = {
    ...connectors[index],
    ...req.body,
    updated: new Date().toISOString()
  };
  
  res.json(connectors[index]);
});

// Delete a connector
app.delete('/connectors/:id', (req, res) => {
  const index = connectors.findIndex(c => c.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Connector not found' });
  }
  
  connectors.splice(index, 1);
  res.status(204).send();
});

// Start the server
app.listen(port, () => {
  console.log(`Connector Registry service running on port ${port}`);
});
