{"psi_score": 0.66, "fibonacci_analysis": {"residue_distances": [{"start": 5, "end": 8, "score": 0.9416059753853125, "ratio": 1.6079996942534311, "golden_ratio_diff": 0.0062015350519406135}, {"start": 0, "end": 3, "score": 0.894799355527568, "ratio": 1.637057048762542, "golden_ratio_diff": 0.011756897657844904}, {"start": 15, "end": 17, "score": 0.6455584224999676, "ratio": 1.529196402648815, "golden_ratio_diff": 0.05490464768896266}, {"start": 1, "end": 4, "score": 0.6219238941128683, "ratio": 1.5196714727812508, "golden_ratio_diff": 0.060791378087576335}, {"start": 14, "end": 17, "score": 0.6065794822570149, "ratio": 1.5130901539846415, "golden_ratio_diff": 0.06485885679467943}, {"start": 14, "end": 16, "score": 0.5720396212087334, "ratio": 1.496983905320468, "golden_ratio_diff": 0.0748130659003962}, {"start": 5, "end": 9, "score": 0.5157559069172374, "ratio": 1.4661165039570336, "golden_ratio_diff": 0.09389016908738354}], "torsion_angles": [{"start": 5, "end": 17, "score": 0.9453627536113643, "ratio": 1.608682560277512, "golden_ratio_diff": 0.005779500639296074}, {"start": 9, "end": 11, "score": 0.9247104921723531, "ratio": 1.6312079479179367, "golden_ratio_diff": 0.008141954532253096}, {"start": 7, "end": 16, "score": 0.911669602781948, "ratio": 1.6337108945612178, "golden_ratio_diff": 0.009688860629828273}, {"start": 7, "end": 18, "score": 0.9055209692323218, "ratio": 1.63491601447878, "golden_ratio_diff": 0.0104336656994012}, {"start": 15, "end": 18, "score": 0.8817340211205722, "ratio": 1.6397364941490287, "golden_ratio_diff": 0.013412885977692768}, {"start": 5, "end": 19, "score": 0.8044213815996791, "ratio": 1.578694799205335, "golden_ratio_diff": 0.024312956228412477}, {"start": 6, "end": 19, "score": 0.7215939918451606, "ratio": 1.6804611138309713, "golden_ratio_diff": 0.038582085120046246}, {"start": 5, "end": 18, "score": 0.6268661070134451, "ratio": 1.714345356433052, "golden_ratio_diff": 0.05952369873117928}, {"start": 8, "end": 19, "score": 0.6076905384124046, "ratio": 1.7224901227248028, "golden_ratio_diff": 0.06455744112990576}, {"start": 5, "end": 16, "score": 0.5926071267191432, "ratio": 1.7292671288898567, "golden_ratio_diff": 0.06874586128187661}, {"start": 6, "end": 17, "score": 0.5829275857302068, "ratio": 1.733800913935493, "golden_ratio_diff": 0.07154789453776589}, {"start": 7, "end": 17, "score": 0.5719652255865978, "ratio": 1.4969471145159787, "golden_ratio_diff": 0.07483580386804407}, {"start": 7, "end": 19, "score": 0.5429374268559349, "ratio": 1.4818225688418665, "golden_ratio_diff": 0.08418328715904565}, {"start": 6, "end": 9, "score": 0.5227536500005061, "ratio": 1.4703160693618118, "golden_ratio_diff": 0.09129469492925242}], "overall_score": 0.7114773109804924}, "fibonacci_alignment": {"closest_fibonacci": 21, "difference": 1, "ratio": 0.9523809523809523, "alignment_score": 0.9545454545454545}, "trinity_validation": {"ners": 0.6754431932941477, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6400772773176591, "passed": false}, "trinity_report": {"scores": {"ners": 0.6754431932941477, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6400772773176591, "passed": false}, "thresholds": {"ners": 0.7, "nepi": 0.5, "nefc": 0.6}, "weights": {"ners": 0.4, "nepi": 0.3, "nefc": 0.3}, "validation": {"ners": {"score": 0.6754431932941477, "threshold": 0.7, "passed": false, "description": "Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance."}, "nepi": {"score": 0.5389999999999999, "threshold": 0.5, "passed": true, "description": "Neural-Emotional Potential Index: Evaluates functional potential and adaptability."}, "nefc": {"score": 0.694, "threshold": 0.6, "passed": true, "description": "Neural-Emotional Field Coherence: Assesses field coherence and quantum effects."}, "overall": {"score": 0.6400772773176591, "passed": false, "description": "Overall validation status based on all metrics."}}}}