/**
 * NovaCore Cyber-Safety Middleware
 * 
 * This middleware implements the "Cyber-Safety by Design" philosophy by:
 * 1. Automatically capturing compliance evidence for all API operations
 * 2. Enforcing security policies at the API layer
 * 3. Providing real-time risk assessment
 */

const logger = require('../../config/logger');
const { EvidenceService } = require('../../api/services');
const { generateOperationHash } = require('../utils/safetyUtils');

/**
 * Cyber-Safety middleware that automatically captures evidence and enforces policies
 */
const safetyMiddleware = async (req, res, next) => {
  try {
    // Skip for health check and non-API routes
    if (req.path === '/health' || !req.path.startsWith('/api')) {
      return next();
    }
    
    // Create safety context
    req.safetyContext = {
      operationId: generateOperationHash(req),
      timestamp: new Date(),
      user: req.user ? req.user.id : 'system',
      endpoint: req.path,
      method: req.method,
      requestData: sanitizeRequestData(req.body),
      evidenceId: null,
      riskLevel: 'unknown',
      complianceStatus: 'pending',
      securityStatus: 'pending'
    };
    
    // Log operation for debugging
    logger.debug('Cyber-Safety middleware processing request', {
      operationId: req.safetyContext.operationId,
      endpoint: req.safetyContext.endpoint,
      method: req.safetyContext.method
    });
    
    // Capture response for evidence
    const originalSend = res.send;
    res.send = function(data) {
      // Store response data (sanitized)
      req.safetyContext.responseData = sanitizeResponseData(data);
      req.safetyContext.statusCode = res.statusCode;
      
      // Generate evidence asynchronously
      generateEvidence(req.safetyContext)
        .then(evidenceId => {
          req.safetyContext.evidenceId = evidenceId;
          logger.debug('Generated evidence for operation', {
            operationId: req.safetyContext.operationId,
            evidenceId
          });
        })
        .catch(error => {
          logger.error('Failed to generate evidence', {
            operationId: req.safetyContext.operationId,
            error: error.message
          });
        });
      
      // Continue with the original response
      return originalSend.apply(res, arguments);
    };
    
    // Continue to the next middleware
    next();
  } catch (error) {
    logger.error('Error in Cyber-Safety middleware', {
      path: req.path,
      method: req.method,
      error: error.message
    });
    
    // Continue to the next middleware even if there's an error
    // We don't want to block the request if the safety middleware fails
    next();
  }
};

/**
 * Generate evidence for an API operation
 * @param {Object} safetyContext - Safety context
 * @returns {Promise<string>} - Evidence ID
 */
async function generateEvidence(safetyContext) {
  try {
    // Create evidence record
    const evidence = {
      name: `API Operation: ${safetyContext.method} ${safetyContext.endpoint}`,
      description: `Automatic evidence for API operation ${safetyContext.operationId}`,
      type: 'api_operation',
      category: 'automatic',
      status: 'collected',
      metadata: {
        source: 'cyber_safety_middleware',
        collector: 'automatic',
        collectionDate: safetyContext.timestamp,
        tags: ['api', 'automatic', safetyContext.method.toLowerCase(), getEndpointCategory(safetyContext.endpoint)]
      },
      content: {
        format: 'json',
        data: {
          operationId: safetyContext.operationId,
          timestamp: safetyContext.timestamp,
          user: safetyContext.user,
          endpoint: safetyContext.endpoint,
          method: safetyContext.method,
          requestData: safetyContext.requestData,
          responseData: safetyContext.responseData,
          statusCode: safetyContext.statusCode
        },
        hash: generateOperationHash(safetyContext)
      }
    };
    
    // Save evidence
    const savedEvidence = await EvidenceService.createEvidence(evidence, safetyContext.user);
    
    return savedEvidence._id;
  } catch (error) {
    logger.error('Failed to generate evidence', {
      operationId: safetyContext.operationId,
      error: error.message
    });
    
    throw error;
  }
}

/**
 * Sanitize request data to remove sensitive information
 * @param {Object} data - Request data
 * @returns {Object} - Sanitized data
 */
function sanitizeRequestData(data) {
  if (!data) {
    return {};
  }
  
  // Create a deep copy
  const sanitized = JSON.parse(JSON.stringify(data));
  
  // List of sensitive fields to redact
  const sensitiveFields = [
    'password', 'token', 'secret', 'key', 'apiKey', 'api_key', 'auth',
    'authorization', 'credential', 'credentials', 'accessToken', 'refreshToken'
  ];
  
  // Recursively sanitize object
  function sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') {
      return;
    }
    
    for (const key in obj) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        sanitizeObject(obj[key]);
      }
    }
  }
  
  sanitizeObject(sanitized);
  
  return sanitized;
}

/**
 * Sanitize response data to remove sensitive information
 * @param {Object|string} data - Response data
 * @returns {Object} - Sanitized data
 */
function sanitizeResponseData(data) {
  if (!data) {
    return {};
  }
  
  // Parse string data if needed
  let parsedData;
  if (typeof data === 'string') {
    try {
      parsedData = JSON.parse(data);
    } catch (e) {
      // If not valid JSON, return truncated string
      return data.length > 1000 ? data.substring(0, 1000) + '...[truncated]' : data;
    }
  } else {
    parsedData = data;
  }
  
  // Sanitize the parsed data
  return sanitizeRequestData(parsedData);
}

/**
 * Get endpoint category from path
 * @param {string} endpoint - API endpoint
 * @returns {string} - Endpoint category
 */
function getEndpointCategory(endpoint) {
  const path = endpoint.split('/');
  
  // Get the first segment after /api
  const apiIndex = path.findIndex(segment => segment === 'api');
  
  if (apiIndex !== -1 && apiIndex + 1 < path.length) {
    return path[apiIndex + 1];
  }
  
  return 'unknown';
}

module.exports = safetyMiddleware;
