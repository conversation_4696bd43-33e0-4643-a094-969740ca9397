# Script to remove "3 Critical Aspects" section from all terms except "Consciousness"

$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the content
$content = [System.IO.File]::ReadAllText($dictionaryPath)

# Split the content into terms (split by markdown headers)
$terms = [System.Text.RegularExpressions.Regex]::Split($content, '(?=^##?\s+[A-Z]|^\*\*[^\*]+\*\*)', [System.Text.RegularExpressions.RegexOptions]::Multiline)

$modifiedTerms = @()
$currentTerm = $null

foreach ($term in $terms) {
    if ($term -match '^##?\s+([A-Z])|^\*\*([^\*]+)\*\*') {
        $currentTerm = $matches[1] + $matches[2]  # Combine both possible matches (one will be empty)
    }
    
    # Skip empty terms
    if ([string]::IsNullOrWhiteSpace($term)) {
        continue
    }
    
    # If this is not the Consciousness term, remove the 3 Critical Aspects section
    if ($currentTerm -notmatch 'Consciousness') {
        # This pattern matches the 3 Critical Aspects section with any emoji and any amount of whitespace
        $pattern1 = '(?s)ðŸ”‘\s*Consciousness in Comphyology has 3 Critical Aspects:.*?(?=And all of this can be measured using tools like:|\*\*[^\*]|\n\n|$)'
        $pattern2 = '(?s)Consciousness in Comphyology has 3 Critical Aspects:.*?(?=And all of this can be measured using tools like:|\*\*[^\*]|\n\n|$)'
        $pattern3 = '(?s)3 Critical Aspects:.*?(?=And all of this can be measured using tools like:|\*\*[^\*]|\n\n|$)'
        
        $modifiedTerm = $term -replace $pattern1, '' -replace $pattern2, '' -replace $pattern3, ''
        $modifiedTerms += $modifiedTerm
    } else {
        $modifiedTerms += $term
    }
}

# Join the terms back together
$modifiedContent = $modifiedTerms -join ""

# Remove any double blank lines
$modifiedContent = [System.Text.RegularExpressions.Regex]::Replace($modifiedContent, '(\r?\n){3,}', "`r`n`r`n")

# Write the modified content back to the file
[System.IO.File]::WriteAllText($dictionaryPath, $modifiedContent.Trim())

Write-Host "Removed '3 Critical Aspects' sections from all terms except 'Consciousness'"
Write-Host "Please review the changes in the dictionary file."
