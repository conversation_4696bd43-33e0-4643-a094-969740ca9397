/**
 * Error Handling Middleware
 *
 * This middleware handles errors for the Privacy Management API.
 */

const logger = require('../config/logger');

/**
 * Global error handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 * @returns {void}
 */
const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error({
    message: err.message,
    stack: err.stack,
    name: err.name,
    status: err.status || 500,
    path: req.path,
    method: req.method,
    user: req.user ? req.user.username : 'anonymous'
  });

  // Handle specific error types
  switch (err.name) {
    case 'ValidationError':
      return res.status(400).json({
        error: 'ValidationError',
        message: err.message || 'Validation failed',
        errors: err.errors
      });

    case 'AuthenticationError':
      return res.status(401).json({
        error: 'Unauthorized',
        message: err.message || 'Authentication required'
      });

    case 'NotFoundError':
      return res.status(404).json({
        error: 'NotFound',
        message: err.message || 'Resource not found'
      });

    case 'ConflictError':
      return res.status(409).json({
        error: 'Conflict',
        message: err.message || 'Resource already exists'
      });

    case 'ForbiddenError':
      return res.status(403).json({
        error: 'Forbidden',
        message: err.message || 'Insufficient permissions'
      });

    case 'BadRequestError':
      return res.status(400).json({
        error: 'BadRequest',
        message: err.message || 'Bad request'
      });

    case 'UnprocessableEntityError':
      return res.status(422).json({
        error: 'UnprocessableEntity',
        message: err.message || 'Unprocessable entity'
      });

    case 'TooManyRequestsError':
    case 'RateLimitError':
      return res.status(429).json({
        error: 'TooManyRequests',
        message: err.message || 'Too many requests'
      });

    default:
      // If the error has a custom status code, use it
      if (err.status && err.status !== 500) {
        return res.status(err.status).json({
          error: 'InternalServerError',
          message: 'An unexpected error occurred',
          originalError: err.message,
          stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
        });
      }

      // Otherwise, return a generic 500 error
      return res.status(500).json({
        error: 'InternalServerError',
        message: 'An unexpected error occurred',
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
      });
  }
};

module.exports = {
  errorHandler
};
