require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');
const fs = require('fs');
const path = require('path');

// Initialize Express app
const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(helmet());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan('combined'));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: require('./package.json').version
  });
});

// Swagger documentation setup
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'NovaMarketplace GRC APIs',
      version: '1.0.0',
      description: 'API documentation for NovaMarketplace GRC APIs',
      contact: {
        name: 'NovaFuse',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: `http://localhost:${port}`,
        description: 'Development server'
      }
    ]
  },
  apis: ['./apis/**/*.js', './swagger.js']
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));
app.get('/docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerDocs);
});

// Dynamically load API routes
const apiDirectory = path.join(__dirname, 'apis');
const apiCategories = fs.readdirSync(apiDirectory);

apiCategories.forEach(category => {
  const categoryPath = path.join(apiDirectory, category);
  if (fs.statSync(categoryPath).isDirectory()) {
    const apiModules = fs.readdirSync(categoryPath);
    
    apiModules.forEach(apiModule => {
      const modulePath = path.join(categoryPath, apiModule);
      if (fs.statSync(modulePath).isDirectory()) {
        const routesPath = path.join(modulePath, 'routes.js');
        if (fs.existsSync(routesPath)) {
          const routes = require(routesPath);
          app.use(`/${category}/${apiModule}`, routes);
          console.log(`Loaded API routes: /${category}/${apiModule}`);
        }
      }
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : err.message
  });
});

// Start the server
app.listen(port, () => {
  console.log(`NovaMarketplace API server running on port ${port}`);
  console.log(`API documentation available at http://localhost:${port}/docs`);
});

module.exports = app; // For testing
