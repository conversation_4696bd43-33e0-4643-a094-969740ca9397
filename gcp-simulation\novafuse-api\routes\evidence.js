/**
 * Evidence API Routes
 * 
 * This file defines the API routes for evidence-related functionality.
 */

const express = require('express');
const router = express.Router();
const { evidenceRecords, evidenceBinder } = require('../../models');

// Feature flag middleware
const checkFeatureAccess = (req, res, next) => {
  // In a real implementation, this would check the user's product tier
  // For simulation, we'll check if the feature flag is enabled in the request
  const productTier = req.headers['x-product-tier'] || 'novaPrime';
  
  // For simulation, we'll allow all features for novaPrime
  if (productTier === 'novaPrime') {
    return next();
  }
  
  // For other tiers, we'll check the feature flag configuration
  // This is a simplified implementation for simulation purposes
  const featureFlags = {
    novaCore: ['privacy-management', 'security-assessment', 'regulatory-compliance'],
    novaShield: ['security-assessment', 'control-testing'],
    novaLearn: ['regulatory-compliance'],
    novaAssistAI: ['privacy-management', 'security-assessment', 'regulatory-compliance', 'control-testing', 'esg']
  };
  
  if (featureFlags[productTier] && (
    featureFlags[productTier].includes('regulatory-compliance') || 
    featureFlags[productTier].includes('control-testing')
  )) {
    return next();
  }
  
  return res.status(403).json({
    error: 'Feature not available',
    message: `This feature is not available in your current plan (${productTier})`
  });
};

/**
 * @route GET /evidence/records
 * @description Get all evidence records
 * @access Private
 */
router.get('/records', checkFeatureAccess, (req, res) => {
  // Get query parameters for filtering
  const { type, framework, controlId, resourceType, fromDate, toDate } = req.query;
  
  // Filter records based on query parameters
  let filteredRecords = [...evidenceRecords];
  
  if (type) {
    filteredRecords = filteredRecords.filter(record => 
      record.type === type
    );
  }
  
  if (framework) {
    filteredRecords = filteredRecords.filter(record => 
      record.relatedControls.some(control => control.framework === framework)
    );
  }
  
  if (controlId) {
    filteredRecords = filteredRecords.filter(record => 
      record.relatedControls.some(control => control.controlId === controlId)
    );
  }
  
  if (resourceType) {
    filteredRecords = filteredRecords.filter(record => 
      record.resource.type === resourceType
    );
  }
  
  if (fromDate) {
    const fromTimestamp = new Date(fromDate).getTime();
    filteredRecords = filteredRecords.filter(record => 
      new Date(record.timestamp).getTime() >= fromTimestamp
    );
  }
  
  if (toDate) {
    const toTimestamp = new Date(toDate).getTime();
    filteredRecords = filteredRecords.filter(record => 
      new Date(record.timestamp).getTime() <= toTimestamp
    );
  }
  
  // Return filtered records with limited information
  res.json({
    data: filteredRecords.map(record => ({
      id: record.id,
      type: record.type,
      name: record.name,
      description: record.description,
      resource: record.resource,
      timestamp: record.timestamp,
      expiryDate: record.expiryDate,
      relatedControlsCount: record.relatedControls.length
    }))
  });
});

/**
 * @route GET /evidence/records/:id
 * @description Get a specific evidence record by ID
 * @access Private
 */
router.get('/records/:id', checkFeatureAccess, (req, res) => {
  const { id } = req.params;
  
  // Find the record by ID
  const record = evidenceRecords.find(r => r.id === id);
  
  if (!record) {
    return res.status(404).json({
      error: 'Evidence record not found',
      message: `No evidence record found with ID ${id}`
    });
  }
  
  // Return the record
  res.json({
    data: record
  });
});

/**
 * @route GET /evidence/types
 * @description Get all evidence types
 * @access Private
 */
router.get('/types', checkFeatureAccess, (req, res) => {
  // Extract unique evidence types
  const types = [...new Set(evidenceRecords.map(record => record.type))];
  
  // Return the types
  res.json({
    data: types
  });
});

/**
 * @route GET /evidence/binder
 * @description Get evidence binder configuration
 * @access Private
 */
router.get('/binder', checkFeatureAccess, (req, res) => {
  // Return the evidence binder configuration
  res.json({
    data: evidenceBinder
  });
});

/**
 * @route GET /evidence/binder/types
 * @description Get evidence types supported by the binder
 * @access Private
 */
router.get('/binder/types', checkFeatureAccess, (req, res) => {
  // Return the evidence types supported by the binder
  res.json({
    data: evidenceBinder.evidenceTypes
  });
});

/**
 * @route GET /evidence/summary
 * @description Get a summary of evidence records
 * @access Private
 */
router.get('/summary', checkFeatureAccess, (req, res) => {
  // Calculate summary statistics
  const summary = {
    totalRecords: evidenceRecords.length,
    byType: {},
    byFramework: {},
    byResourceType: {},
    expiringRecords: []
  };
  
  // Process each record
  evidenceRecords.forEach(record => {
    // Count by type
    if (!summary.byType[record.type]) {
      summary.byType[record.type] = 0;
    }
    summary.byType[record.type]++;
    
    // Count by framework
    record.relatedControls.forEach(control => {
      if (!summary.byFramework[control.framework]) {
        summary.byFramework[control.framework] = 0;
      }
      summary.byFramework[control.framework]++;
    });
    
    // Count by resource type
    if (!summary.byResourceType[record.resource.type]) {
      summary.byResourceType[record.resource.type] = 0;
    }
    summary.byResourceType[record.resource.type]++;
    
    // Check for expiring records (within 30 days)
    const expiryDate = new Date(record.expiryDate);
    const now = new Date();
    const daysDifference = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
    
    if (daysDifference <= 30 && daysDifference > 0) {
      summary.expiringRecords.push({
        id: record.id,
        name: record.name,
        type: record.type,
        expiryDate: record.expiryDate,
        daysRemaining: daysDifference
      });
    }
  });
  
  // Sort expiring records by days remaining
  summary.expiringRecords.sort((a, b) => a.daysRemaining - b.daysRemaining);
  
  // Return the summary
  res.json({
    data: summary
  });
});

module.exports = router;
