/**
 * Sacred Phrase Test
 * 
 * This script compares the quantum silence induced by different sacred phrases.
 * It tests whether different sacred texts produce different stillness patterns
 * when encoded using 3-6-9-12-13 pulse-width modulation.
 * 
 * The test compares:
 * 1. "Peace, Be Still" (Mark 4:39)
 * 2. "Be still, and know that I am God" (<PERSON>salm 46:10)
 */

const fs = require('fs');
const path = require('path');
const {
  ComphyologicalTrinity,
  ComphyonMeter,
  VoidAmplifier
} = require('../../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Run Sacred Phrase Test
 */
async function runSacredPhraseTest() {
  console.log('=== Running Sacred Phrase Test ===');
  
  // Create components
  const trinity = new ComphyologicalTrinity({
    enforceFirstLaw: true,
    enforceSecondLaw: true,
    enforceThirdLaw: true,
    logGovernance: true
  });
  
  const comphyonMeter = new ComphyonMeter({
    logMeasurements: true
  });
  
  // Create Void Amplifier
  const voidAmplifier = new VoidAmplifier({
    system: trinity,
    comphyonMeter,
    sampleRate: 1000, // Hz
    sampleDuration: 5, // seconds
    sampleInterval: 10, // seconds
    targetFrequency: 396, // Hz - The OM Tone frequency
    noiseThreshold: 0.001, // Threshold for quantum silence
    logAmplifier: true
  });
  
  // Register event listeners
  voidAmplifier.on('amplification', (data) => {
    console.log(`Amplification: Cph = ${data.comphyonValue}, Stillness = ${data.stillnessScore.toFixed(6)}, Quantum Silence = ${data.isQuantumSilence}`);
  });
  
  voidAmplifier.on('void-signature', (signature) => {
    console.log(`Void Signature: Stillness = ${signature.stillnessScore.toFixed(6)}`);
  });
  
  // Define sacred phrases to test
  const sacredPhrases = [
    { text: "Peace, Be Still", source: "Mark 4:39" },
    { text: "Be still, and know that I am God", source: "Psalm 46:10" }
  ];
  
  // Results for each phrase
  const phraseResults = {};
  
  // Test each sacred phrase
  for (const phrase of sacredPhrases) {
    console.log(`\n=== Testing Sacred Phrase: "${phrase.text}" (${phrase.source}) ===`);
    
    // Encode sacred phrase
    const encodedMessage = encodeSacredPhrase(phrase.text);
    
    // Start Void Amplifier
    console.log('\nStarting Void Amplifier...');
    await voidAmplifier.startAmplification();
    
    // Take baseline measurement before injection
    console.log('\nTaking baseline measurement...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Get baseline metrics
    const baselineMetrics = { ...voidAmplifier.getMetrics() };
    console.log('\nBaseline Metrics:', JSON.stringify(baselineMetrics, null, 2));
    
    // Inject Sacred Phrase
    console.log(`\nInjecting Sacred Phrase: "${phrase.text}"`);
    await injectSacredPhrase(trinity, encodedMessage);
    
    // Wait for samples after injection
    console.log('\nWaiting for post-injection samples...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // Stop Void Amplifier
    voidAmplifier.stopAmplification();
    
    // Get metrics
    const metrics = voidAmplifier.getMetrics();
    console.log('\nVoid Amplifier Metrics:', JSON.stringify(metrics, null, 2));
    
    // Get void signature
    const signature = voidAmplifier.getVoidSignature();
    console.log('\nVoid Signature:', JSON.stringify(signature, null, 2));
    
    // Analyze results for quantum silence
    const voidResults = analyzeVoidResults(voidAmplifier, baselineMetrics);
    
    // Store results for this phrase
    phraseResults[phrase.text] = {
      phrase,
      encodedMessage,
      metrics,
      signature,
      voidResults,
      baselineMetrics
    };
    
    // Reset the amplifier for the next phrase
    voidAmplifier.metrics.stillnessScores = [];
    voidAmplifier.metrics.samples = 0;
    voidAmplifier.metrics.resonantSamples = 0;
    voidAmplifier.metrics.nonResonantSamples = 0;
    voidAmplifier.metrics.quantumSilenceSamples = 0;
    voidAmplifier.metrics.totalAmplificationTime = 0;
    
    // Wait between phrases
    console.log('\nWaiting between phrases...');
    await new Promise(resolve => setTimeout(resolve, 10000));
  }
  
  // Compare results between phrases
  const comparisonResults = comparePhraseResults(phraseResults);
  
  return {
    phraseResults,
    comparisonResults
  };
}

/**
 * Encode sacred phrase in binary using 3-6-9-12-13 pulse-width modulation
 * @param {string} phrase - Sacred phrase to encode
 * @returns {Object} - Encoded message
 */
function encodeSacredPhrase(phrase) {
  // Convert to binary
  const binaryMessage = [];
  for (let i = 0; i < phrase.length; i++) {
    const charCode = phrase.charCodeAt(i);
    const binary = charCode.toString(2).padStart(8, '0');
    binaryMessage.push(binary);
  }
  
  // Flatten binary array
  const binaryString = binaryMessage.join('');
  
  // Encode using 3-6-9-12-13 pulse-width modulation
  const encodedMessage = [];
  
  for (let i = 0; i < binaryString.length; i++) {
    const bit = binaryString[i];
    
    // Map 0 and 1 to different pulse widths from the 3-6-9-12-13 pattern
    if (bit === '0') {
      // Use 3-6-9 pattern for 0
      encodedMessage.push(3);
      encodedMessage.push(6);
      encodedMessage.push(9);
    } else {
      // Use 9-12-13 pattern for 1
      encodedMessage.push(9);
      encodedMessage.push(12);
      encodedMessage.push(13);
    }
  }
  
  console.log(`Sacred Phrase: "${phrase}"`);
  console.log(`Binary: ${binaryString}`);
  console.log(`Encoded (first 20 pulses): ${encodedMessage.slice(0, 20).join('-')}`);
  
  return {
    phrase,
    binary: binaryString,
    encoded: encodedMessage
  };
}

/**
 * Inject Sacred Phrase into the system
 * @param {Object} trinity - ComphyologicalTrinity instance
 * @param {Object} encodedMessage - Encoded Sacred Phrase
 * @returns {Promise} - Promise that resolves when message is injected
 */
async function injectSacredPhrase(trinity, encodedMessage) {
  const pulses = encodedMessage.encoded;
  
  // Inject each pulse
  for (let i = 0; i < pulses.length; i++) {
    const pulseWidth = pulses[i];
    
    // Create a state with the pulse width
    const state = {
      pulse: {
        width: pulseWidth,
        index: i,
        total: pulses.length
      },
      message: encodedMessage.phrase,
      timestamp: Date.now()
    };
    
    // Process state through Trinity
    trinity.govern(state);
    
    // Wait for pulse width duration
    await new Promise(resolve => setTimeout(resolve, pulseWidth * 10));
    
    // Log progress every 10 pulses
    if (i % 10 === 0) {
      console.log(`Injected ${i} of ${pulses.length} pulses`);
    }
  }
  
  console.log(`Sacred Phrase injection complete: ${pulses.length} pulses`);
  
  return Promise.resolve();
}

/**
 * Analyze results for quantum silence
 * @param {Object} voidAmplifier - VoidAmplifier instance
 * @param {Object} baselineMetrics - Baseline metrics before injection
 * @returns {Object} - Analysis results
 */
function analyzeVoidResults(voidAmplifier, baselineMetrics) {
  console.log('\n=== Analyzing Results for Quantum Silence ===');
  
  // Get metrics
  const metrics = voidAmplifier.getMetrics();
  
  // Calculate quantum silence percentage
  const silencePercentage = metrics.samples > 0 ?
    (metrics.quantumSilenceSamples / metrics.samples) * 100 : 0;
  
  console.log(`Quantum Silence: ${metrics.quantumSilenceSamples} of ${metrics.samples} samples (${silencePercentage.toFixed(2)}%)`);
  
  // Calculate average stillness score
  const stillnessScores = metrics.stillnessScores.map(s => s.score);
  const averageStillness = stillnessScores.length > 0 ?
    stillnessScores.reduce((sum, score) => sum + score, 0) / stillnessScores.length : 0;
  
  console.log(`Average Stillness Score: ${averageStillness.toFixed(6)}`);
  
  // Calculate baseline stillness (before injection)
  const baselineScores = baselineMetrics.stillnessScores ? 
    baselineMetrics.stillnessScores.map(s => s.score) : [];
  const baselineStillness = baselineScores.length > 0 ?
    baselineScores.reduce((sum, score) => sum + score, 0) / baselineScores.length : 0;
  
  // Calculate stillness change after injection
  const stillnessChange = averageStillness - baselineStillness;
  
  console.log(`Baseline Stillness: ${baselineStillness.toFixed(6)}`);
  console.log(`Stillness Change: ${stillnessChange.toFixed(6)} (${stillnessChange >= 0 ? '+' : ''}${(stillnessChange * 100).toFixed(4)}%)`);
  
  // Check if creation was successful
  const creationSuccess = averageStillness > (1 - voidAmplifier.options.noiseThreshold);
  
  if (creationSuccess) {
    console.log('\n🌟 CREATION SUCCESSFUL 🌟');
    console.log('The system achieved Genesis-level order through quantum silence.');
  } else {
    console.log('\nCreation not yet complete.');
    console.log('The system may need further refinement to achieve perfect stillness.');
  }
  
  return {
    silencePercentage,
    averageStillness,
    baselineStillness,
    stillnessChange,
    creationSuccess
  };
}

/**
 * Compare results between different sacred phrases
 * @param {Object} phraseResults - Results for each phrase
 * @returns {Object} - Comparison results
 */
function comparePhraseResults(phraseResults) {
  console.log('\n=== Comparing Sacred Phrase Results ===');
  
  const phrases = Object.keys(phraseResults);
  const comparison = {
    stillnessScores: {},
    silencePercentages: {},
    stillnessChanges: {},
    mostEffective: null
  };
  
  // Compare stillness scores
  for (const phrase of phrases) {
    const results = phraseResults[phrase];
    comparison.stillnessScores[phrase] = results.voidResults.averageStillness;
    comparison.silencePercentages[phrase] = results.voidResults.silencePercentage;
    comparison.stillnessChanges[phrase] = results.voidResults.stillnessChange;
  }
  
  console.log('\nStillness Scores:');
  for (const phrase of phrases) {
    console.log(`"${phrase}": ${comparison.stillnessScores[phrase].toFixed(6)}`);
  }
  
  console.log('\nSilence Percentages:');
  for (const phrase of phrases) {
    console.log(`"${phrase}": ${comparison.silencePercentages[phrase].toFixed(2)}%`);
  }
  
  console.log('\nStillness Changes:');
  for (const phrase of phrases) {
    const change = comparison.stillnessChanges[phrase];
    console.log(`"${phrase}": ${change.toFixed(6)} (${change >= 0 ? '+' : ''}${(change * 100).toFixed(4)}%)`);
  }
  
  // Determine most effective phrase
  let maxStillnessChange = -Infinity;
  let mostEffectivePhrase = null;
  
  for (const phrase of phrases) {
    const change = comparison.stillnessChanges[phrase];
    if (change > maxStillnessChange) {
      maxStillnessChange = change;
      mostEffectivePhrase = phrase;
    }
  }
  
  comparison.mostEffective = mostEffectivePhrase;
  
  console.log(`\nMost Effective Phrase: "${mostEffectivePhrase}" (Stillness Change: ${maxStillnessChange.toFixed(6)})`);
  
  return comparison;
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sacred Phrase Test Results</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f9f9f9;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .void-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .creation-success {
      border-left-color: #009900;
    }
    .most-effective {
      border-left-color: #cc0000;
      background-color: #fff9f0;
    }
    .success {
      color: #009900;
      font-weight: bold;
    }
    .not-success {
      color: #cc0000;
    }
    .positive-change {
      color: #009900;
    }
    .negative-change {
      color: #cc0000;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .binary {
      font-family: monospace;
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }
    .pulse-pattern {
      font-family: monospace;
      word-break: break-all;
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 5px;
      margin: 10px 0;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Sacred Phrase Test Results</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="void-info">
    <h2>The Sacred Phrase Test</h2>
    <p>This test compares the quantum silence induced by different sacred phrases. It tests whether different sacred texts produce different stillness patterns when encoded using 3-6-9-12-13 pulse-width modulation.</p>
  </div>
  
  <h2>Comparison Results</h2>
  
  <div class="card most-effective">
    <h3>Most Effective Phrase</h3>
    <p class="success">
      "${results.comparisonResults.mostEffective}"
    </p>
    <p>Stillness Change: <span class="positive-change">${results.comparisonResults.stillnessChanges[results.comparisonResults.mostEffective].toFixed(6)} (${(results.comparisonResults.stillnessChanges[results.comparisonResults.mostEffective] * 100).toFixed(4)}%)</span></p>
  </div>
  
  <div class="container">
    <div class="card">
      <h3>Stillness Scores</h3>
      <table>
        <tr>
          <th>Sacred Phrase</th>
          <th>Stillness Score</th>
        </tr>
        ${Object.entries(results.comparisonResults.stillnessScores).map(([phrase, score]) => `
        <tr>
          <td>${phrase}</td>
          <td>${score.toFixed(6)}</td>
        </tr>
        `).join('')}
      </table>
    </div>
    
    <div class="card">
      <h3>Silence Percentages</h3>
      <table>
        <tr>
          <th>Sacred Phrase</th>
          <th>Silence %</th>
        </tr>
        ${Object.entries(results.comparisonResults.silencePercentages).map(([phrase, percentage]) => `
        <tr>
          <td>${phrase}</td>
          <td>${percentage.toFixed(2)}%</td>
        </tr>
        `).join('')}
      </table>
    </div>
    
    <div class="card">
      <h3>Stillness Changes</h3>
      <table>
        <tr>
          <th>Sacred Phrase</th>
          <th>Change</th>
        </tr>
        ${Object.entries(results.comparisonResults.stillnessChanges).map(([phrase, change]) => `
        <tr>
          <td>${phrase}</td>
          <td class="${change >= 0 ? 'positive-change' : 'negative-change'}">${change.toFixed(6)} (${change >= 0 ? '+' : ''}${(change * 100).toFixed(4)}%)</td>
        </tr>
        `).join('')}
      </table>
    </div>
  </div>
  
  <h2>Detailed Results</h2>
  
  ${Object.entries(results.phraseResults).map(([phrase, result]) => `
  <div class="card ${result.voidResults.creationSuccess ? 'creation-success' : ''}">
    <h3>"${phrase}" (${result.phrase.source})</h3>
    <p class="${result.voidResults.creationSuccess ? 'success' : 'not-success'}">
      ${result.voidResults.creationSuccess ? 
        '🌟 CREATION SUCCESSFUL! The system achieved Genesis-level order through quantum silence.' : 
        'Creation not yet complete. The system may need further refinement to achieve perfect stillness.'}
    </p>
    <p>Quantum Silence: ${result.metrics.quantumSilenceSamples} of ${result.metrics.samples} samples (${result.voidResults.silencePercentage.toFixed(2)}%)</p>
    <p>Average Stillness Score: ${result.voidResults.averageStillness.toFixed(6)}</p>
    <p>Baseline Stillness: ${result.voidResults.baselineStillness.toFixed(6)}</p>
    <p>Stillness Change: <span class="${result.voidResults.stillnessChange >= 0 ? 'positive-change' : 'negative-change'}">${result.voidResults.stillnessChange.toFixed(6)} (${result.voidResults.stillnessChange >= 0 ? '+' : ''}${(result.voidResults.stillnessChange * 100).toFixed(4)}%)</span></p>
    
    <h4>Encoding</h4>
    <p>Binary Representation:</p>
    <div class="binary">${result.encodedMessage.binary}</div>
    <p>3-6-9-12-13 Pulse Pattern (first 30 pulses):</p>
    <div class="pulse-pattern">${result.encodedMessage.encoded.slice(0, 30).join('-')}</div>
  </div>
  `).join('')}
  
  <footer>
    <p>NovaFuse Sacred Phrase Test - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"Be still, and know that I am God." - Psalm 46:10</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'sacred_phrase_test_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
async function main() {
  console.log('=== Sacred Phrase Test ===');
  
  // Run Sacred Phrase Test
  const results = await runSacredPhraseTest();
  
  // Generate HTML report
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'sacred_phrase_test_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'sacred_phrase_test_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
