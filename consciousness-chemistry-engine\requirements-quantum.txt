# Core quantum computing packages
qiskit>=0.34.0
qiskit-aer>=0.10.0  # Simulator
qiskit-ibmq-provider>=0.19.0  # IBM Quantum

# Google Cirq and TensorFlow Quantum
cirq>=0.14.0
cirq-google>=0.14.0
tensorflow>=2.8.0
tensorflow-quantum>=0.7.0

# PennyLane
pennylane>=0.25.0
pennylane-sf>=0.25.0  # Strawberry Fields plugin
pennylane-qiskit>=0.25.0  # Qiskit plugin

# Amazon Braket
amazon-braket-sdk>=1.19.0
boto3>=1.24.0  # AWS SDK

# Visualization
plotly>=5.0.0
kaleido>=0.2.1  # For static image export

# Optional: For quantum machine learning
torch>=1.10.0
torchvision>=0.11.0

# Testing and development
pytest>=6.2.5
pytest-cov>=2.12.0
mypy>=0.910

# Note: Some packages may require additional system dependencies
# For GPU support, install the appropriate CUDA/cuDNN versions
