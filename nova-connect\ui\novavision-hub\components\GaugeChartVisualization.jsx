/**
 * GaugeChartVisualization Component
 * 
 * A component for visualizing a single value within a range using a gauge chart.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme/ThemeContext';
import { usePerformance } from '../performance/usePerformance';
import { useAnimation } from '../animation/useAnimation';

/**
 * GaugeChartVisualization component
 * 
 * @param {Object} props - Component props
 * @param {number} props.value - Current value
 * @param {number} [props.min=0] - Minimum value
 * @param {number} [props.max=100] - Maximum value
 * @param {Array} [props.thresholds] - Threshold values for color changes
 * @param {Object} [props.options] - Chart options
 * @param {boolean} [props.options.showValue=true] - Whether to show the value
 * @param {boolean} [props.options.showLabel=true] - Whether to show the label
 * @param {string} [props.options.label] - Label text
 * @param {boolean} [props.options.showMinMax=true] - Whether to show min and max values
 * @param {boolean} [props.options.animate=true] - Whether to animate the gauge
 * @param {number} [props.options.animationDuration=1000] - Animation duration in milliseconds
 * @param {number} [props.options.thickness=20] - Gauge thickness
 * @param {number} [props.options.startAngle=135] - Start angle in degrees
 * @param {number} [props.options.endAngle=45] - End angle in degrees
 * @param {string} [props.options.valueFormat] - Value format function or string
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} GaugeChartVisualization component
 */
const GaugeChartVisualization = ({
  value,
  min = 0,
  max = 100,
  thresholds = [],
  options = {},
  className = '',
  style = {}
}) => {
  const { measureOperation } = usePerformance('GaugeChartVisualization');
  const { theme } = useTheme();
  
  // Refs
  const svgRef = useRef(null);
  
  // State
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  // Default options
  const {
    showValue = true,
    showLabel = true,
    label = '',
    showMinMax = true,
    animate = true,
    animationDuration = 1000,
    thickness = 20,
    startAngle = 135,
    endAngle = 45,
    valueFormat
  } = options;
  
  // Animation
  const { ref: animationRef, progress } = useAnimation({
    keyframes: [
      { strokeDashoffset: 1000 },
      { strokeDashoffset: 0 }
    ],
    duration: animationDuration,
    easing: 'easeOutCubic'
  });
  
  // Update dimensions on resize
  useEffect(() => {
    if (!svgRef.current) return;
    
    const updateDimensions = () => {
      if (svgRef.current) {
        const { width, height } = svgRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    
    updateDimensions();
    
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(svgRef.current);
    
    return () => {
      if (svgRef.current) {
        resizeObserver.unobserve(svgRef.current);
      }
    };
  }, []);
  
  // Calculate gauge properties
  const gaugeProps = measureOperation('calculateGaugeProps', () => {
    if (dimensions.width === 0 || dimensions.height === 0) {
      return {
        centerX: 0,
        centerY: 0,
        radius: 0,
        startAngleRad: 0,
        endAngleRad: 0,
        angleRangeRad: 0,
        valueAngleRad: 0,
        arcLength: 0,
        valueArcLength: 0
      };
    }
    
    // Calculate center and radius
    const centerX = dimensions.width / 2;
    const centerY = dimensions.height * 0.6;
    const radius = Math.min(centerX, centerY) * 0.8;
    
    // Convert angles to radians
    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;
    
    // Handle angle wrap-around
    let angleRangeRad = endAngleRad - startAngleRad;
    if (angleRangeRad < 0) {
      angleRangeRad += 2 * Math.PI;
    }
    
    // Calculate value angle
    const normalizedValue = Math.min(Math.max(value, min), max);
    const valueRatio = (normalizedValue - min) / (max - min);
    const valueAngleRad = startAngleRad + valueRatio * angleRangeRad;
    
    // Calculate arc length
    const arcLength = angleRangeRad * radius;
    const valueArcLength = valueRatio * arcLength;
    
    return {
      centerX,
      centerY,
      radius,
      startAngleRad,
      endAngleRad,
      angleRangeRad,
      valueAngleRad,
      arcLength,
      valueArcLength
    };
  });
  
  // Get color for value
  const getValueColor = (value) => {
    if (!thresholds || thresholds.length === 0) {
      return theme.colors.primary;
    }
    
    // Sort thresholds by value
    const sortedThresholds = [...thresholds].sort((a, b) => a.value - b.value);
    
    // Find the appropriate threshold
    for (let i = 0; i < sortedThresholds.length; i++) {
      if (value <= sortedThresholds[i].value) {
        return sortedThresholds[i].color;
      }
    }
    
    // If no threshold is found, use the last one
    return sortedThresholds[sortedThresholds.length - 1].color;
  };
  
  // Format value
  const formatValueText = (value) => {
    if (typeof valueFormat === 'function') {
      return valueFormat(value);
    }
    
    if (valueFormat === 'percentage') {
      return `${value}%`;
    }
    
    return value.toLocaleString();
  };
  
  // Create gauge arc path
  const createArcPath = (startAngle, endAngle, radius) => {
    const start = {
      x: Math.cos(startAngle) * radius + gaugeProps.centerX,
      y: Math.sin(startAngle) * radius + gaugeProps.centerY
    };
    
    const end = {
      x: Math.cos(endAngle) * radius + gaugeProps.centerX,
      y: Math.sin(endAngle) * radius + gaugeProps.centerY
    };
    
    const largeArcFlag = endAngle - startAngle <= Math.PI ? 0 : 1;
    
    return `M ${start.x} ${start.y} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${end.x} ${end.y}`;
  };
  
  // Calculate tick marks
  const calculateTicks = () => {
    const { centerX, centerY, radius, startAngleRad, angleRangeRad } = gaugeProps;
    const tickCount = 5;
    const ticks = [];
    
    for (let i = 0; i <= tickCount; i++) {
      const tickValue = min + (i / tickCount) * (max - min);
      const tickAngle = startAngleRad + (i / tickCount) * angleRangeRad;
      
      const innerPoint = {
        x: Math.cos(tickAngle) * (radius - thickness - 5) + centerX,
        y: Math.sin(tickAngle) * (radius - thickness - 5) + centerY
      };
      
      const outerPoint = {
        x: Math.cos(tickAngle) * (radius + 5) + centerX,
        y: Math.sin(tickAngle) * (radius + 5) + centerY
      };
      
      const labelPoint = {
        x: Math.cos(tickAngle) * (radius + 20) + centerX,
        y: Math.sin(tickAngle) * (radius + 20) + centerY
      };
      
      ticks.push({
        value: tickValue,
        angle: tickAngle,
        innerPoint,
        outerPoint,
        labelPoint
      });
    }
    
    return ticks;
  };
  
  // Calculate needle points
  const calculateNeedlePoints = () => {
    const { centerX, centerY, radius, valueAngleRad } = gaugeProps;
    
    const needleLength = radius * 0.8;
    const needleWidth = thickness * 0.5;
    
    const tip = {
      x: Math.cos(valueAngleRad) * needleLength + centerX,
      y: Math.sin(valueAngleRad) * needleLength + centerY
    };
    
    const baseLeft = {
      x: Math.cos(valueAngleRad + Math.PI / 2) * needleWidth + centerX,
      y: Math.sin(valueAngleRad + Math.PI / 2) * needleWidth + centerY
    };
    
    const baseRight = {
      x: Math.cos(valueAngleRad - Math.PI / 2) * needleWidth + centerX,
      y: Math.sin(valueAngleRad - Math.PI / 2) * needleWidth + centerY
    };
    
    return `M ${tip.x} ${tip.y} L ${baseLeft.x} ${baseLeft.y} L ${baseRight.x} ${baseRight.y} Z`;
  };
  
  // Get ticks
  const ticks = gaugeProps.radius ? calculateTicks() : [];
  
  // Get needle points
  const needlePath = gaugeProps.radius ? calculateNeedlePoints() : '';
  
  // Get value color
  const valueColor = getValueColor(value);
  
  return (
    <div
      className={`relative ${className}`}
      style={{ ...style, minHeight: '200px' }}
      data-testid="gauge-chart-visualization"
    >
      <svg
        ref={svgRef}
        width="100%"
        height="100%"
        viewBox={`0 0 ${dimensions.width || 100} ${dimensions.height || 100}`}
        preserveAspectRatio="xMidYMid meet"
      >
        {gaugeProps.radius > 0 && (
          <>
            {/* Background arc */}
            <path
              d={createArcPath(gaugeProps.startAngleRad, gaugeProps.endAngleRad, gaugeProps.radius)}
              fill="none"
              stroke={theme.colors.divider}
              strokeWidth={thickness}
              strokeLinecap="round"
            />
            
            {/* Value arc */}
            <path
              ref={animationRef}
              d={createArcPath(gaugeProps.startAngleRad, gaugeProps.valueAngleRad, gaugeProps.radius)}
              fill="none"
              stroke={valueColor}
              strokeWidth={thickness}
              strokeLinecap="round"
              style={{
                transition: animate ? `stroke-dashoffset ${animationDuration}ms ease-out` : 'none'
              }}
            />
            
            {/* Tick marks */}
            {showMinMax && ticks.map((tick, index) => (
              <g key={index}>
                <line
                  x1={tick.innerPoint.x}
                  y1={tick.innerPoint.y}
                  x2={tick.outerPoint.x}
                  y2={tick.outerPoint.y}
                  stroke={theme.colors.textSecondary}
                  strokeWidth={1}
                />
                
                {(index === 0 || index === ticks.length - 1) && (
                  <text
                    x={tick.labelPoint.x}
                    y={tick.labelPoint.y}
                    textAnchor={index === 0 ? 'start' : (index === ticks.length - 1 ? 'end' : 'middle')}
                    dominantBaseline="middle"
                    fill={theme.colors.textSecondary}
                    fontSize={theme.typography.caption.fontSize}
                  >
                    {tick.value.toLocaleString()}
                  </text>
                )}
              </g>
            ))}
            
            {/* Needle */}
            <path
              d={needlePath}
              fill={theme.colors.error}
              stroke={theme.colors.background}
              strokeWidth={1}
              style={{
                transition: animate ? `transform ${animationDuration}ms ease-out` : 'none'
              }}
            />
            
            {/* Needle center */}
            <circle
              cx={gaugeProps.centerX}
              cy={gaugeProps.centerY}
              r={thickness * 0.8}
              fill={theme.colors.background}
              stroke={theme.colors.divider}
              strokeWidth={1}
            />
            
            {/* Value text */}
            {showValue && (
              <text
                x={gaugeProps.centerX}
                y={gaugeProps.centerY + gaugeProps.radius * 0.4}
                textAnchor="middle"
                dominantBaseline="middle"
                fill={theme.colors.textPrimary}
                fontSize={theme.typography.h4.fontSize}
                fontWeight="bold"
              >
                {formatValueText(value)}
              </text>
            )}
            
            {/* Label text */}
            {showLabel && label && (
              <text
                x={gaugeProps.centerX}
                y={gaugeProps.centerY + gaugeProps.radius * 0.6}
                textAnchor="middle"
                dominantBaseline="middle"
                fill={theme.colors.textSecondary}
                fontSize={theme.typography.body2.fontSize}
              >
                {label}
              </text>
            )}
          </>
        )}
      </svg>
    </div>
  );
};

GaugeChartVisualization.propTypes = {
  value: PropTypes.number.isRequired,
  min: PropTypes.number,
  max: PropTypes.number,
  thresholds: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.number.isRequired,
      color: PropTypes.string.isRequired
    })
  ),
  options: PropTypes.shape({
    showValue: PropTypes.bool,
    showLabel: PropTypes.bool,
    label: PropTypes.string,
    showMinMax: PropTypes.bool,
    animate: PropTypes.bool,
    animationDuration: PropTypes.number,
    thickness: PropTypes.number,
    startAngle: PropTypes.number,
    endAngle: PropTypes.number,
    valueFormat: PropTypes.oneOfType([PropTypes.func, PropTypes.string])
  }),
  className: PropTypes.string,
  style: PropTypes.object
};

export default GaugeChartVisualization;
