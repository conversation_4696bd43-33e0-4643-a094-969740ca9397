class NovaConnect {
  constructor(config = {}) {
    this.connections = new Map();
    this.config = {
      publicOnly: config.publicOnly || false,
      platform: config.platform || 'sharesale',
      credentials: {
        apiKey: config.apiKey || null,
        affiliateId: config.affiliateId || null
      }
    };
  }

  // Connect to API endpoint
  async connect(apiConfig) {
    try {
      const endpoint = apiConfig.endpoint;
      const credentials = apiConfig.credentials;
      
      // If publicOnly mode, use scraping instead of API
      if (this.config.publicOnly) {
        const products = await this.scrapePublicFeed();
        const connection = {
          endpoint: endpoint,
          credentials: credentials,
          lastAccess: new Date(),
          status: 'connected',
          data: products
        };
        this.connections.set(endpoint, connection);
        return {
          success: true,
          data: products
        };
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      });

      if (!response.ok) {
        throw new Error('Failed to connect to API');
      }

      const data = await response.json();
      const connection = {
        endpoint: apiConfig.endpoint,
        credentials: apiConfig.credentials,
        lastAccess: new Date(),
        status: 'connected'
      };

      this.connections.set(apiConfig.endpoint, connection);
      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('Connection failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Scrape public product feed
  async scrapePublicFeed() {
    try {
      const platform = this.config.platform;
      let url;
      
      switch (platform) {
        case 'sharesale':
          url = 'https://www.shareasale.com/members/health.htm';
          break;
        default:
          throw new Error('Unsupported platform');
      }

      const response = await fetch(url);
      const text = await response.text();
      
      // Basic HTML scraping (this would need to be more robust in production)
      const parser = new DOMParser();
      const doc = parser.parseFromString(text, 'text/html');
      const products = Array.from(doc.querySelectorAll('.product-listing')).map(product => ({
        title: product.querySelector('.product-title')?.textContent || 'Unknown',
        description: product.querySelector('.product-desc')?.textContent || '',
        link: product.querySelector('a')?.href || '',
        triadicScore: this.calculateBasicTriadicScore(product.textContent)
      }));

      return products;
    } catch (error) {
      console.error('Scraping failed:', error);
      return [];
    }
  }

  // Calculate basic triadic score from text
  calculateBasicTriadicScore(text) {
    // Basic scoring based on positive/negative keywords
    const positiveWords = ['health', 'wellness', 'benefit', 'support'];
    const negativeWords = ['risk', 'side effects', 'warning'];

    let score = 0;
    positiveWords.forEach(word => {
      if (text.toLowerCase().includes(word)) score += 0.1;
    });
    negativeWords.forEach(word => {
      if (text.toLowerCase().includes(word)) score -= 0.1;
    });

    return Math.max(0, Math.min(1, score));
  }

  // Make API request
  async request(endpoint, method = 'GET', data = null) {
    try {
      const connection = this.connections.get(endpoint);
      if (!connection) {
        throw new Error('No active connection');
      }

      if (this.config.publicOnly) {
        const cachedData = connection.data;
        if (cachedData && cachedData.length > 0) {
          return {
            success: true,
            data: cachedData
          };
        }
        
        const products = await this.scrapePublicFeed();
        connection.data = products;
        return {
          success: true,
          data: products
        };
      }

      const response = await fetch(endpoint, {
        method: method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: data ? JSON.stringify(data) : undefined
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      connection.lastAccess = new Date();
      this.connections.set(endpoint, connection);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('Request failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Close connection
  disconnect(endpoint) {
    this.connections.delete(endpoint);
  }

  // Check connection status
  getConnectionStatus(endpoint) {
    const connection = this.connections.get(endpoint);
    return connection ? { status: connection.status, lastAccess: connection.lastAccess } : { status: 'disconnected' };
  }

  // Disconnect from API
  async disconnectFromAPI(endpoint) {
    try {
      const connection = this.connections.get(endpoint);
      if (connection) {
        // Log disconnect
        this.novaShield.logThreat({
          type: 'CONNECTION_DISCONNECT',
          source: endpoint,
          timestamp: new Date().toISOString()
        });

        // Remove connection
        this.connections.delete(endpoint);
        return {
          success: true,
          message: 'Disconnected successfully'
        };
      }

      return {
        success: false,
        message: 'Connection not found'
      };
    } catch (error) {
      throw error;
    }
  }
}

export default NovaConnect;
