from setuptools import setup, find_packages

setup(
    name="psi_tensor_core",
    version="0.1.0",
    description="Ψ Tensor Core (CSE Engine Fusion)",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    author="NovaFuse",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "torch>=1.8.0",
        "numpy>=1.19.0",
        "matplotlib>=3.3.0"
    ],
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
    ],
    python_requires=">=3.7",
)
