/**
 * Action Handler Service
 * 
 * This service handles the execution of actions suggested by NovaAssistAI.
 * It provides a centralized way to define and execute actions that can be
 * triggered by the chatbot.
 */

const mongoose = require('mongoose');
const User = require('../models/user');
const ControlTest = require('../models/controlTest');
const TestRun = require('../models/testRun');
const Framework = require('../models/framework');
const Regulation = require('../models/regulation');
const ComplianceProfile = require('../models/complianceProfile');
const logger = require('../utils/logger');

/**
 * Execute an action
 * @param {String} action - Action name
 * @param {Object} params - Action parameters
 * @param {String} userId - User ID
 * @returns {Object} Action result
 */
exports.executeAction = async (action, params, userId) => {
  try {
    // Get user
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Execute action based on name
    switch (action) {
      case 'showFailedTests':
        return await showFailedTests(params, user);
      case 'createControlTest':
        return await createControlTest(params, user);
      case 'runTest':
        return await runTest(params, user);
      case 'showFrameworkDetails':
        return await showFrameworkDetails(params, user);
      case 'showComplianceStatus':
        return await showComplianceStatus(params, user);
      case 'showRegulationDetails':
        return await showRegulationDetails(params, user);
      case 'generateComplianceReport':
        return await generateComplianceReport(params, user);
      case 'scheduleTest':
        return await scheduleTest(params, user);
      case 'navigateTo':
        return navigateTo(params);
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  } catch (error) {
    logger.error(`Error executing action ${action}:`, error);
    throw error;
  }
};

/**
 * Show failed tests
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function showFailedTests(params, user) {
  try {
    const { timeframe = 'week', limit = 5 } = params;
    
    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case 'day':
        startDate = new Date(now.setDate(now.getDate() - 1));
        break;
      case 'week':
        startDate = new Date(now.setDate(now.getDate() - 7));
        break;
      case 'month':
        startDate = new Date(now.setMonth(now.getMonth() - 1));
        break;
      case 'quarter':
        startDate = new Date(now.setMonth(now.getMonth() - 3));
        break;
      case 'year':
        startDate = new Date(now.setFullYear(now.getFullYear() - 1));
        break;
      default:
        startDate = new Date(now.setDate(now.getDate() - 7));
    }
    
    // Get failed test runs
    const failedTestRuns = await TestRun.find({
      organizationId: user.organizationId,
      status: 'completed',
      result: 'fail',
      endTime: { $gte: startDate }
    })
    .sort({ endTime: -1 })
    .limit(parseInt(limit))
    .populate('testId', 'name frameworkName controlName');
    
    // Format results
    const formattedResults = failedTestRuns.map(testRun => ({
      id: testRun._id,
      testName: testRun.testName,
      frameworkName: testRun.frameworkName,
      controlName: testRun.controlName,
      executedBy: testRun.executedBy,
      endTime: testRun.endTime,
      url: `/novaassure/test-runs/${testRun._id}`
    }));
    
    // Generate message
    let message;
    if (failedTestRuns.length === 0) {
      message = `Great news! I couldn't find any failed tests in the last ${timeframe}.`;
    } else {
      message = `I found ${failedTestRuns.length} failed tests from the last ${timeframe}:\n\n`;
      formattedResults.forEach((result, index) => {
        message += `${index + 1}. **${result.testName}** (${result.frameworkName} - ${result.controlName})\n`;
        message += `   Failed on ${new Date(result.endTime).toLocaleDateString()}\n`;
        message += `   [View Details](${result.url})\n\n`;
      });
    }
    
    // Generate suggestions
    const suggestions = [
      { id: 'failed-1', text: 'Show all failed tests', action: 'navigateTo', params: { path: '/novaassure/test-runs?result=fail' } },
      { id: 'failed-2', text: `Show last month's failures`, action: 'showFailedTests', params: { timeframe: 'month' } }
    ];
    
    return {
      message,
      suggestions,
      data: formattedResults
    };
  } catch (error) {
    logger.error('Error showing failed tests:', error);
    throw new Error('Failed to retrieve failed tests');
  }
}

/**
 * Create a control test
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function createControlTest(params, user) {
  try {
    const { name, frameworkId, controlId, type = 'manual' } = params;
    
    if (!name || !frameworkId || !controlId) {
      throw new Error('Missing required parameters: name, frameworkId, and controlId are required');
    }
    
    // Get framework and control details
    const framework = await Framework.findById(frameworkId);
    if (!framework) {
      throw new Error('Framework not found');
    }
    
    const control = framework.controls.find(c => c._id.toString() === controlId);
    if (!control) {
      throw new Error('Control not found');
    }
    
    // Create test
    const test = new ControlTest({
      name,
      description: control.description || '',
      frameworkId,
      frameworkName: framework.name,
      controlId,
      controlName: control.name,
      type,
      frequency: 'monthly',
      status: 'draft',
      owner: `${user.firstName} ${user.lastName}`,
      organizationId: user.organizationId,
      createdBy: user._id,
      updatedBy: user._id,
      testSteps: [],
      evidenceRequirements: [],
      tags: [framework.name.toLowerCase(), control.name.toLowerCase().replace(/\s+/g, '_')],
      riskLevel: control.riskLevel || 'medium'
    });
    
    await test.save();
    
    // Generate message
    const message = `I've created a new ${type} test for you:\n\n` +
      `**${name}**\n` +
      `Framework: ${framework.name}\n` +
      `Control: ${control.name}\n\n` +
      `The test is currently in draft status. Would you like to add test steps now?`;
    
    // Generate suggestions
    const suggestions = [
      { id: 'test-1', text: 'Add test steps', action: 'navigateTo', params: { path: `/novaassure/tests/${test._id}/edit-steps` } },
      { id: 'test-2', text: 'View test details', action: 'navigateTo', params: { path: `/novaassure/tests/${test._id}` } }
    ];
    
    return {
      message,
      suggestions,
      data: {
        testId: test._id,
        name: test.name,
        frameworkName: test.frameworkName,
        controlName: test.controlName,
        url: `/novaassure/tests/${test._id}`
      }
    };
  } catch (error) {
    logger.error('Error creating control test:', error);
    throw new Error(`Failed to create control test: ${error.message}`);
  }
}

/**
 * Run a test
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function runTest(params, user) {
  try {
    const { testId } = params;
    
    if (!testId) {
      throw new Error('Missing required parameter: testId');
    }
    
    // Get test
    const test = await ControlTest.findById(testId);
    if (!test) {
      throw new Error('Test not found');
    }
    
    // Check if test belongs to user's organization
    if (test.organizationId.toString() !== user.organizationId.toString()) {
      throw new Error('Unauthorized: Test does not belong to your organization');
    }
    
    // Check if test is active
    if (test.status !== 'active') {
      throw new Error('Test is not active');
    }
    
    // Create test run
    const testRun = new TestRun({
      testId: test._id,
      testName: test.name,
      controlId: test.controlId,
      controlName: test.controlName,
      frameworkId: test.frameworkId,
      frameworkName: test.frameworkName,
      status: 'in_progress',
      result: null,
      startTime: new Date(),
      executedBy: `${user.firstName} ${user.lastName}`,
      executionType: test.type,
      stepResults: test.testSteps.map(step => ({
        stepId: step.id,
        stepName: step.name,
        status: 'pending',
        result: null,
        notes: '',
        evidence: [],
        startTime: null,
        endTime: null,
        duration: null
      })),
      evidence: [],
      notes: '',
      issues: [],
      organizationId: user.organizationId,
      createdBy: user._id,
      updatedBy: user._id
    });
    
    await testRun.save();
    
    // Update test lastRun
    test.lastRun = new Date();
    await test.save();
    
    // Generate message
    const message = `I've started a new test run for **${test.name}**.\n\n` +
      `You can now proceed with the test execution. Would you like to view the test details?`;
    
    // Generate suggestions
    const suggestions = [
      { id: 'run-1', text: 'View test run', action: 'navigateTo', params: { path: `/novaassure/test-runs/${testRun._id}` } },
      { id: 'run-2', text: 'Complete test run', action: 'navigateTo', params: { path: `/novaassure/test-runs/${testRun._id}/complete` } }
    ];
    
    return {
      message,
      suggestions,
      data: {
        testRunId: testRun._id,
        testName: testRun.testName,
        frameworkName: testRun.frameworkName,
        controlName: testRun.controlName,
        url: `/novaassure/test-runs/${testRun._id}`
      }
    };
  } catch (error) {
    logger.error('Error running test:', error);
    throw new Error(`Failed to run test: ${error.message}`);
  }
}

/**
 * Show framework details
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function showFrameworkDetails(params, user) {
  try {
    const { frameworkId, frameworkName } = params;
    
    let framework;
    
    // Get framework by ID or name
    if (frameworkId) {
      framework = await Framework.findById(frameworkId);
    } else if (frameworkName) {
      framework = await Framework.findOne({ 
        name: { $regex: new RegExp(frameworkName, 'i') }
      });
    } else {
      throw new Error('Missing required parameter: frameworkId or frameworkName');
    }
    
    if (!framework) {
      throw new Error('Framework not found');
    }
    
    // Get compliance profile for the organization
    const complianceProfile = await ComplianceProfile.findOne({
      organizationId: user.organizationId,
      'applicableFrameworks.frameworkId': framework._id
    });
    
    // Get framework compliance status
    let complianceStatus = null;
    if (complianceProfile) {
      const frameworkStatus = complianceProfile.applicableFrameworks.find(
        f => f.frameworkId.toString() === framework._id.toString()
      );
      if (frameworkStatus) {
        complianceStatus = frameworkStatus.complianceStatus;
      }
    }
    
    // Get control tests for the framework
    const controlTests = await ControlTest.find({
      frameworkId: framework._id,
      organizationId: user.organizationId
    });
    
    // Generate message
    let message = `Here are the details for the **${framework.name}** framework:\n\n`;
    
    message += `**Description**: ${framework.description}\n\n`;
    
    if (complianceStatus) {
      message += `**Compliance Status**: ${complianceStatus.score}%\n`;
      message += `**Last Assessment**: ${new Date(complianceStatus.lastAssessment).toLocaleDateString()}\n\n`;
    }
    
    message += `**Controls**: ${framework.controls.length} total controls\n`;
    
    if (controlTests.length > 0) {
      message += `**Tests**: ${controlTests.length} tests configured\n\n`;
      
      // Group tests by status
      const activeTests = controlTests.filter(test => test.status === 'active').length;
      const draftTests = controlTests.filter(test => test.status === 'draft').length;
      const inactiveTests = controlTests.filter(test => test.status === 'inactive').length;
      
      message += `- ${activeTests} active tests\n`;
      message += `- ${draftTests} draft tests\n`;
      message += `- ${inactiveTests} inactive tests\n\n`;
    } else {
      message += `**Tests**: No tests configured yet\n\n`;
    }
    
    message += `Would you like to view the framework details or create a new test?`;
    
    // Generate suggestions
    const suggestions = [
      { id: 'framework-1', text: 'View framework details', action: 'navigateTo', params: { path: `/frameworks/${framework._id}` } },
      { id: 'framework-2', text: 'Create a new test', action: 'navigateTo', params: { path: `/novaassure/tests/create?frameworkId=${framework._id}` } }
    ];
    
    return {
      message,
      suggestions,
      data: {
        frameworkId: framework._id,
        name: framework.name,
        description: framework.description,
        controlCount: framework.controls.length,
        testCount: controlTests.length,
        complianceStatus: complianceStatus
      }
    };
  } catch (error) {
    logger.error('Error showing framework details:', error);
    throw new Error(`Failed to retrieve framework details: ${error.message}`);
  }
}

/**
 * Show compliance status
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function showComplianceStatus(params, user) {
  try {
    // Get compliance profiles for the organization
    const complianceProfiles = await ComplianceProfile.find({
      organizationId: user.organizationId
    });
    
    if (complianceProfiles.length === 0) {
      return {
        message: "I couldn't find any compliance profiles for your organization. Would you like to set up a compliance profile?",
        suggestions: [
          { id: 'compliance-1', text: 'Set up compliance profile', action: 'navigateTo', params: { path: '/compliance-profiles/create' } }
        ],
        data: null
      };
    }
    
    // Use the first profile (most organizations will have just one)
    const profile = complianceProfiles[0];
    
    // Generate message
    let message = `Here's your current compliance status:\n\n`;
    
    if (profile.applicableFrameworks.length === 0) {
      message += "You don't have any frameworks configured in your compliance profile yet.";
    } else {
      // Sort frameworks by compliance score (descending)
      const sortedFrameworks = [...profile.applicableFrameworks].sort(
        (a, b) => b.complianceStatus.score - a.complianceStatus.score
      );
      
      sortedFrameworks.forEach(framework => {
        const score = framework.complianceStatus.score;
        const statusEmoji = score >= 90 ? '🟢' : score >= 70 ? '🟡' : '🔴';
        
        message += `${statusEmoji} **${framework.frameworkName}**: ${score}%\n`;
      });
      
      // Calculate overall compliance score
      const overallScore = Math.round(
        sortedFrameworks.reduce((sum, fw) => sum + fw.complianceStatus.score, 0) / 
        sortedFrameworks.length
      );
      
      message += `\n**Overall Compliance**: ${overallScore}%\n\n`;
      
      // Add information about recent test results
      const recentTestRuns = await TestRun.find({
        organizationId: user.organizationId,
        status: 'completed'
      })
      .sort({ endTime: -1 })
      .limit(5);
      
      if (recentTestRuns.length > 0) {
        const passedTests = recentTestRuns.filter(tr => tr.result === 'pass').length;
        const failedTests = recentTestRuns.filter(tr => tr.result === 'fail').length;
        
        message += `**Recent Test Results**: ${passedTests} passed, ${failedTests} failed\n\n`;
      }
      
      message += `Would you like to view the detailed compliance dashboard or run a new test?`;
    }
    
    // Generate suggestions
    const suggestions = [
      { id: 'status-1', text: 'View compliance dashboard', action: 'navigateTo', params: { path: '/dashboard' } },
      { id: 'status-2', text: 'Run a new test', action: 'navigateTo', params: { path: '/novaassure/tests' } }
    ];
    
    // Add framework-specific suggestions
    if (profile.applicableFrameworks.length > 0) {
      const lowestFramework = profile.applicableFrameworks.reduce(
        (lowest, fw) => fw.complianceStatus.score < lowest.complianceStatus.score ? fw : lowest,
        profile.applicableFrameworks[0]
      );
      
      suggestions.push({
        id: 'status-3',
        text: `Improve ${lowestFramework.frameworkName}`,
        action: 'showFrameworkDetails',
        params: { frameworkId: lowestFramework.frameworkId }
      });
    }
    
    return {
      message,
      suggestions,
      data: {
        profileId: profile._id,
        frameworks: profile.applicableFrameworks.map(fw => ({
          id: fw.frameworkId,
          name: fw.frameworkName,
          score: fw.complianceStatus.score,
          lastAssessment: fw.complianceStatus.lastAssessment
        }))
      }
    };
  } catch (error) {
    logger.error('Error showing compliance status:', error);
    throw new Error(`Failed to retrieve compliance status: ${error.message}`);
  }
}

/**
 * Show regulation details
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function showRegulationDetails(params, user) {
  try {
    const { regulationId, regulationName } = params;
    
    let regulation;
    
    // Get regulation by ID or name
    if (regulationId) {
      regulation = await Regulation.findById(regulationId);
    } else if (regulationName) {
      regulation = await Regulation.findOne({ 
        name: { $regex: new RegExp(regulationName, 'i') }
      });
    } else {
      throw new Error('Missing required parameter: regulationId or regulationName');
    }
    
    if (!regulation) {
      throw new Error('Regulation not found');
    }
    
    // Generate message
    let message = `Here are the details for **${regulation.name}**:\n\n`;
    
    message += `**Description**: ${regulation.description}\n\n`;
    message += `**Jurisdiction**: ${regulation.jurisdiction}\n`;
    message += `**Category**: ${regulation.category}\n`;
    message += `**Effective Date**: ${new Date(regulation.effectiveDate).toLocaleDateString()}\n\n`;
    
    if (regulation.requirements && regulation.requirements.length > 0) {
      message += `**Key Requirements**:\n`;
      regulation.requirements.slice(0, 3).forEach(req => {
        message += `- ${req.name}: ${req.description.substring(0, 100)}...\n`;
      });
      
      if (regulation.requirements.length > 3) {
        message += `- And ${regulation.requirements.length - 3} more requirements\n`;
      }
      
      message += `\n`;
    }
    
    message += `Would you like to view the full regulation details or see how it maps to compliance frameworks?`;
    
    // Generate suggestions
    const suggestions = [
      { id: 'regulation-1', text: 'View regulation details', action: 'navigateTo', params: { path: `/regulations/${regulation._id}` } },
      { id: 'regulation-2', text: 'See framework mappings', action: 'navigateTo', params: { path: `/regulations/${regulation._id}/mappings` } }
    ];
    
    return {
      message,
      suggestions,
      data: {
        regulationId: regulation._id,
        name: regulation.name,
        description: regulation.description,
        jurisdiction: regulation.jurisdiction,
        category: regulation.category,
        effectiveDate: regulation.effectiveDate,
        requirementCount: regulation.requirements ? regulation.requirements.length : 0
      }
    };
  } catch (error) {
    logger.error('Error showing regulation details:', error);
    throw new Error(`Failed to retrieve regulation details: ${error.message}`);
  }
}

/**
 * Generate compliance report
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function generateComplianceReport(params, user) {
  try {
    const { frameworkId, reportType = 'summary' } = params;
    
    // Validate parameters
    if (!frameworkId) {
      throw new Error('Missing required parameter: frameworkId');
    }
    
    // Get framework
    const framework = await Framework.findById(frameworkId);
    if (!framework) {
      throw new Error('Framework not found');
    }
    
    // Get compliance profile
    const complianceProfile = await ComplianceProfile.findOne({
      organizationId: user.organizationId,
      'applicableFrameworks.frameworkId': frameworkId
    });
    
    if (!complianceProfile) {
      throw new Error('Compliance profile not found for this framework');
    }
    
    // Get framework status from profile
    const frameworkStatus = complianceProfile.applicableFrameworks.find(
      fw => fw.frameworkId.toString() === frameworkId.toString()
    );
    
    if (!frameworkStatus) {
      throw new Error('Framework status not found in compliance profile');
    }
    
    // Get control tests for the framework
    const controlTests = await ControlTest.find({
      frameworkId,
      organizationId: user.organizationId
    });
    
    // Get recent test runs
    const testRuns = await TestRun.find({
      frameworkId,
      organizationId: user.organizationId,
      status: 'completed'
    })
    .sort({ endTime: -1 })
    .limit(50);
    
    // Generate report ID
    const reportId = new mongoose.Types.ObjectId();
    
    // Generate message
    let message = `I've generated a ${reportType} compliance report for **${framework.name}**.\n\n`;
    
    message += `**Compliance Score**: ${frameworkStatus.complianceStatus.score}%\n`;
    message += `**Last Assessment**: ${new Date(frameworkStatus.complianceStatus.lastAssessment).toLocaleDateString()}\n\n`;
    
    // Add test statistics
    const passedTests = testRuns.filter(tr => tr.result === 'pass').length;
    const failedTests = testRuns.filter(tr => tr.result === 'fail').length;
    const inconclusiveTests = testRuns.filter(tr => tr.result === 'inconclusive').length;
    
    message += `**Test Results**:\n`;
    message += `- Passed: ${passedTests}\n`;
    message += `- Failed: ${failedTests}\n`;
    message += `- Inconclusive: ${inconclusiveTests}\n\n`;
    
    // Add control coverage
    const totalControls = framework.controls.length;
    const coveredControls = controlTests.length;
    const coveragePercentage = Math.round((coveredControls / totalControls) * 100) || 0;
    
    message += `**Control Coverage**: ${coveragePercentage}% (${coveredControls}/${totalControls} controls)\n\n`;
    
    message += `Your report is ready. Would you like to download the full report or view it in the browser?`;
    
    // Generate suggestions
    const suggestions = [
      { id: 'report-1', text: 'View report in browser', action: 'navigateTo', params: { path: `/reports/${reportId}` } },
      { id: 'report-2', text: 'Download PDF report', action: 'navigateTo', params: { path: `/reports/${reportId}/download` } }
    ];
    
    return {
      message,
      suggestions,
      data: {
        reportId,
        frameworkId,
        frameworkName: framework.name,
        complianceScore: frameworkStatus.complianceStatus.score,
        lastAssessment: frameworkStatus.complianceStatus.lastAssessment,
        testResults: {
          passed: passedTests,
          failed: failedTests,
          inconclusive: inconclusiveTests
        },
        controlCoverage: {
          percentage: coveragePercentage,
          covered: coveredControls,
          total: totalControls
        }
      }
    };
  } catch (error) {
    logger.error('Error generating compliance report:', error);
    throw new Error(`Failed to generate compliance report: ${error.message}`);
  }
}

/**
 * Schedule a test
 * @param {Object} params - Action parameters
 * @param {Object} user - User object
 * @returns {Object} Action result
 */
async function scheduleTest(params, user) {
  try {
    const { testId, frequency, startDate } = params;
    
    // Validate parameters
    if (!testId || !frequency) {
      throw new Error('Missing required parameters: testId and frequency');
    }
    
    // Validate frequency
    const validFrequencies = ['daily', 'weekly', 'monthly', 'quarterly', 'annually'];
    if (!validFrequencies.includes(frequency)) {
      throw new Error(`Invalid frequency: ${frequency}. Must be one of: ${validFrequencies.join(', ')}`);
    }
    
    // Get test
    const test = await ControlTest.findById(testId);
    if (!test) {
      throw new Error('Test not found');
    }
    
    // Check if test belongs to user's organization
    if (test.organizationId.toString() !== user.organizationId.toString()) {
      throw new Error('Unauthorized: Test does not belong to your organization');
    }
    
    // Parse start date or use current date
    const parsedStartDate = startDate ? new Date(startDate) : new Date();
    
    // Update test
    test.frequency = frequency;
    test.nextRun = parsedStartDate;
    test.updatedBy = user._id;
    
    await test.save();
    
    // Generate message
    const message = `I've scheduled the test **${test.name}** to run ${frequency}.\n\n` +
      `The first test run is scheduled for ${parsedStartDate.toLocaleDateString()}.\n\n` +
      `Would you like to view the test details or run the test now?`;
    
    // Generate suggestions
    const suggestions = [
      { id: 'schedule-1', text: 'View test details', action: 'navigateTo', params: { path: `/novaassure/tests/${test._id}` } },
      { id: 'schedule-2', text: 'Run test now', action: 'runTest', params: { testId: test._id } }
    ];
    
    return {
      message,
      suggestions,
      data: {
        testId: test._id,
        testName: test.name,
        frequency,
        nextRun: parsedStartDate
      }
    };
  } catch (error) {
    logger.error('Error scheduling test:', error);
    throw new Error(`Failed to schedule test: ${error.message}`);
  }
}

/**
 * Navigate to a page
 * @param {Object} params - Action parameters
 * @returns {Object} Action result
 */
function navigateTo(params) {
  const { path } = params;
  
  if (!path) {
    throw new Error('Missing required parameter: path');
  }
  
  return {
    message: `I'll take you to the requested page.`,
    suggestions: [],
    data: {
      path
    },
    action: 'navigate',
    path
  };
}

module.exports = exports;
