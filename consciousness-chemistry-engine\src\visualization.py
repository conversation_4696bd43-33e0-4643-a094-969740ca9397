"""
Visualization module for protein structures with consciousness metrics.
"""
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
from pathlib import Path
import json

# Try to import PyMOL, but don't fail if not available
try:
    import pymol
    from pymol import cmd
    PYMOL_AVAILABLE = True
except ImportError:
    PYMOL_AVAILABLE = False
    print("PyMOL not available. Visualization features will be limited.")

class ProteinVisualizer:
    """Handles 3D visualization of proteins with consciousness metrics."""
    
    def __init__(self):
        """Initialize the visualizer."""
        self.session_initialized = False
        
    def ensure_session(self):
        """Ensure PyMOL session is initialized."""
        if not PYMOL_AVAILABLE:
            raise ImportError("PyMOL is required for 3D visualization")
            
        if not self.session_initialized:
            pymol.finish_launching(['pymol', '-c'])  # No GUI for now
            self.session_initialized = True
    
    def color_by_psi_scores(self, structure_path: str, psi_scores: Dict[int, float], 
                          color_map: str = "blue_white_red",
                          selection: str = "all"):
        """
        Color a protein structure by Ψ-scores.
        
        Args:
            structure_path: Path to PDB file or PDB ID
            psi_scores: Dict mapping residue numbers (1-based) to Ψ-scores (0-1)
            color_map: PyMOL color map name
            selection: Atom selection to color
        """
        self.ensure_session()
        
        # Load structure if not already loaded
        obj_name = Path(structure_path).stem
        if obj_name not in cmd.get_names():
            cmd.load(structure_path, obj_name)
        
        # Create a custom color map based on Ψ-scores
        cmd.ramp_new("psi_ramp", obj_name, 
                    [0, 0.5, 1.0],  # Positions
                    ["blue", "white", "red"])  # Colors
        
        # Apply colors to residues
        for resi, score in psi_scores.items():
            # Map score to color index
            color_val = int(score * 100)
            cmd.color(f"psi_ramp_{color_val}", 
                     f"{obj_name} and resi {resi} and {selection}")
        
        # Apply surface representation
        cmd.show("surface", obj_name)
        cmd.set("surface_color", "white", obj_name)
        cmd.set("transparency", 0.5, obj_name)
        
        # Add color bar
        cmd.ramp_show("psi_ramp", obj_name)
        
        return obj_name
    
    def highlight_domains(self, obj_name: str, domains: List[Dict], 
                         color: str = "gold", 
                         selection: str = "name CA"):
        """
        Highlight protein domains in the structure.
        
        Args:
            obj_name: Name of the PyMOL object
            domains: List of domain dicts with 'start', 'end', 'name' keys
            color: Color to use for highlighting
            selection: Atom selection to highlight
        """
        self.ensure_session()
        
        for domain in domains:
            start = domain.get('start', 1)
            end = domain.get('end', start)
            name = domain.get('name', 'Domain')
            
            # Create selection for this domain
            sel_name = f"{obj_name}_{name}"
            cmd.select(sel_name, f"{obj_name} and resi {start}-{end} and {selection}")
            
            # Show as sticks
            cmd.show("sticks", sel_name)
            cmd.color(color, sel_name)
            
            # Add label
            cmd.label(f"{sel_name} and name CA", f"' {name}'")
    
    def save_session(self, filename: str):
        """
        Save the current PyMOL session.
        
        Args:
            filename: Output filename (.pse)
        """
        self.ensure_session()
        cmd.save(filename)
    
    def render_image(self, filename: str, width: int = 1024, height: int = 768):
        """
        Render the current scene to an image file.
        
        Args:
            filename: Output filename (.png, .jpg, etc.)
            width: Image width in pixels
            height: Image height in pixels
        """
        self.ensure_session()
        cmd.ray(width, height)
        cmd.png(filename, ray=1)


def create_consciousness_report(protein_data: Dict, output_dir: str = "reports"):
    """
    Create an HTML report of consciousness metrics.
    
    Args:
        protein_data: Dictionary containing protein analysis results
        output_dir: Directory to save the report
    """
    import os
    from datetime import datetime
    
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(output_dir, f"consciousness_report_{timestamp}.html")
    
    # Extract data for the report
    name = protein_data.get('name', 'Unnamed Protein')
    sequence = protein_data.get('sequence', '')
    length = len(sequence)
    
    # Extract metrics
    metrics = protein_data.get('consciousness_metrics', {})
    trinity = metrics.get('trinity_validation', {})
    
    # Create HTML report
    html = f"""<!DOCTYPE html>
    <html>
    <head>
        <title>Consciousness Analysis Report - {name}</title>
        <style>
            body {{ font-family: Arial, sans-serif; line-height: 1.6; }}
            .container {{ max-width: 1200px; margin: 0 auto; padding: 20px; }}
            .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
            .metrics {{ display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }}
            .metric-card {{ 
                background: #fff; 
                border: 1px solid #ddd; 
                border-radius: 5px; 
                padding: 15px; 
                flex: 1; 
                min-width: 200px;
            }}
            .metric-value {{ 
                font-size: 24px; 
                font-weight: bold; 
                color: #2c3e50;
                margin: 10px 0;
            }}
            .progress-bar {{ 
                height: 20px; 
                background-color: #e0e0e0; 
                border-radius: 10px; 
                margin: 10px 0;
                overflow: hidden;
            }}
            .progress-fill {{ 
                height: 100%; 
                background-color: #3498db; 
                width: 0%;
                transition: width 0.5s;
            }}
            .domain {{ margin: 10px 0; }}
            .domain-bar {{ 
                height: 15px; 
                background-color: #e0e0e0; 
                border-radius: 5px; 
                margin: 5px 0;
                position: relative;
            }}
            .domain-fill {{ 
                height: 100%; 
                background-color: #e74c3c; 
                width: 0%;
                border-radius: 5px;
            }}
            .domain-label {{ 
                position: absolute; 
                top: 0; 
                left: 10px; 
                color: white; 
                font-size: 12px;
                line-height: 15px;
                text-shadow: 1px 1px 1px #000;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Consciousness Analysis Report</h1>
                <p><strong>Protein:</strong> {name}</p>
                <p><strong>Length:</strong> {length} amino acids</p>
                <p><strong>Date:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <h2>Trinity Validation</h2>
            <div class="metrics">
                <div class="metric-card">
                    <h3>NERS</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {trinity.get('NERS', {}).get('score', 0) * 100}%"></div>
                    </div>
                    <div class="metric-value">{trinity.get('NERS', {}).get('score', 0):.2f}</div>
                    <p>Structural Consciousness</p>
                </div>
                
                <div class="metric-card">
                    <h3>NEPI</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {trinity.get('NEPI', {}).get('score', 0) * 100}%"></div>
                    </div>
                    <div class="metric-value">{trinity.get('NEPI', {}).get('score', 0):.2f}</div>
                    <p>Functional Truth</p>
                </div>
                
                <div class="metric-card">
                    <h3>NEFC</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: {trinity.get('NEFC', {}).get('score', 0) * 100}%"></div>
                    </div>
                    <div class="metric-value">{trinity.get('NEFC', {}).get('score', 0):.2f}</div>
                    <p>Purpose Alignment</p>
                </div>
            </div>
            
            <h2>Domain Analysis</h2>
            <div class="domains">
    """
    
    # Add domain information if available
    if 'domain_analysis' in protein_data:
        domains = protein_data['domain_analysis'].get('domains', [])
        for domain in domains:
            html += f"""
                <div class="domain">
                    <h3>{domain.get('name', 'Domain')} (residues {domain.get('start', 0)}-{domain.get('end', 0)})</h3>
                    <p>Length: {domain.get('length', 0)} aa | 
                       Closest Fibonacci: {domain.get('closest_fibonacci', 0)} | 
                       Alignment Score: {domain.get('alignment_score', 0):.2f}</p>
                    <div class="domain-bar">
                        <div class="domain-fill" style="width: {domain.get('alignment_score', 0) * 100}%">
                            <span class="domain-label">{domain.get('name', 'Domain')}</span>
                        </div>
                    </div>
                </div>
            """
    
    # Close the HTML
    html += """
            </div>
            
            <div class="footer" style="margin-top: 40px; padding: 20px 0; border-top: 1px solid #eee; color: #777;">
                <p>Generated by ConsciousNovaFold - Consciousness-Informed Protein Analysis</p>
            </div>
        </div>
        
        <script>
            // Simple animation for progress bars
            document.addEventListener('DOMContentLoaded', function() {
                const fills = document.querySelectorAll('.progress-fill, .domain-fill');
                fills.forEach(fill => {
                    const width = fill.style.width;
                    fill.style.width = '0';
                    setTimeout(() => {
                        fill.style.width = width;
                    }, 100);
                });
            });
        </script>
    </body>
    </html>
    """
    
    # Write the report
    with open(report_file, 'w') as f:
        f.write(html)
    
    return report_file
