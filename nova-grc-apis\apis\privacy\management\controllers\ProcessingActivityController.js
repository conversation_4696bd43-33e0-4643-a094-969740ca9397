/**
 * Processing Activity Controller
 * 
 * Handles operations related to data processing activities.
 */

const ProcessingActivity = require('../models/ProcessingActivity');
const { validationResult } = require('express-validator');

/**
 * Get all processing activities
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllProcessingActivities = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build query based on filters
    const query = {};
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.legalBasis) {
      query.legalBasis = req.query.legalBasis;
    }
    
    // Count total documents for pagination
    const total = await ProcessingActivity.countDocuments(query);
    
    // Get processing activities with pagination
    const processingActivities = await ProcessingActivity.find(query)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      count: processingActivities.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: processingActivities
    });
  } catch (error) {
    console.error('Error in getAllProcessingActivities:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Get a single processing activity by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getProcessingActivity = async (req, res) => {
  try {
    const processingActivity = await ProcessingActivity.findById(req.params.id);
    
    if (!processingActivity) {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: processingActivity
    });
  } catch (error) {
    console.error('Error in getProcessingActivity:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Create a new processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createProcessingActivity = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    // Create new processing activity
    const processingActivity = await ProcessingActivity.create(req.body);
    
    res.status(201).json({
      success: true,
      data: processingActivity
    });
  } catch (error) {
    console.error('Error in createProcessingActivity:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update a processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateProcessingActivity = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    let processingActivity = await ProcessingActivity.findById(req.params.id);
    
    if (!processingActivity) {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    // Update processing activity
    processingActivity = await ProcessingActivity.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );
    
    res.status(200).json({
      success: true,
      data: processingActivity
    });
  } catch (error) {
    console.error('Error in updateProcessingActivity:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Delete a processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteProcessingActivity = async (req, res) => {
  try {
    const processingActivity = await ProcessingActivity.findById(req.params.id);
    
    if (!processingActivity) {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    await processingActivity.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error in deleteProcessingActivity:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Check if a DPIA is required for a processing activity
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkDpiaRequired = async (req, res) => {
  try {
    const processingActivity = await ProcessingActivity.findById(req.params.id);
    
    if (!processingActivity) {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    const isDpiaRequired = processingActivity.isDpiaRequired();
    
    res.status(200).json({
      success: true,
      data: {
        isDpiaRequired,
        reasons: isDpiaRequired ? getDpiaRequirementReasons(processingActivity) : []
      }
    });
  } catch (error) {
    console.error('Error in checkDpiaRequired:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Get reasons why a DPIA is required for a processing activity
 * @param {Object} processingActivity - Processing activity object
 * @returns {Array} Array of reasons
 */
const getDpiaRequirementReasons = (processingActivity) => {
  const reasons = [];
  const specialCategories = ['Special', 'Criminal', 'Children', 'Biometric', 'Health', 'Genetic'];
  
  // Check for special categories of data
  const hasSpecialCategories = processingActivity.dataCategories.some(category => 
    specialCategories.includes(category)
  );
  
  if (hasSpecialCategories) {
    reasons.push('Processing involves special categories of data');
  }
  
  // Check for high-risk activities
  if (processingActivity.risks.some(risk => risk.impact === 'High')) {
    reasons.push('Processing involves high-risk activities');
  }
  
  // Check if DPIA is explicitly required
  if (processingActivity.dpia.required) {
    reasons.push('DPIA is explicitly required for this activity');
  }
  
  return reasons;
};
