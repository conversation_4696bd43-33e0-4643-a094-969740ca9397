import React from 'react';
import BasicDemoTemplate from '../../components/demo-framework/BasicDemoTemplate';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function RegulatoryComplianceApiDemo() {
  // SEO metadata
  const pageProps = {
    title: 'Regulatory Compliance API Demo - NovaFuse',
    description: 'Explore NovaFuse\'s Regulatory Compliance API for managing regulatory frameworks, requirements, and compliance status tracking.',
    keywords: 'Regulatory Compliance API, compliance frameworks, regulatory requirements, compliance status, NovaFuse demo',
    canonical: 'https://novafuse.io/api-demos/regulatory-compliance-api-demo',
    ogImage: '/images/demos/regulatory-compliance-api-demo-og.png'
  };

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Regulatory Compliance API', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Key Features', href: '#features' },
      { label: 'API Endpoints', href: '#endpoints' },
      { label: 'Use Cases', href: '#use-cases' },
      { label: 'Integration Examples', href: '#integration' }
    ]},
    { type: 'category', label: 'Related APIs', items: [
      { label: 'Privacy Management API', href: '/api-demos/privacy-management-api-demo' },
      { label: 'Security Assessment API', href: '/api-demos/security-assessment-api-demo' },
      { label: 'Control Testing API', href: '/api-demos/control-testing-api-demo' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'API Documentation', href: '/documentation/apis/regulatory-compliance' },
      { label: 'Sample Code', href: '/documentation/code-samples/regulatory-compliance' },
      { label: 'Schedule Demo', href: '/contact?demo=regulatory-compliance' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <BasicDemoTemplate
        title="Regulatory Compliance API"
        description="Manage regulatory frameworks, requirements, and compliance status tracking across your organization"
        demoType="technical"
      >
        {/* Custom content for the Regulatory Compliance API demo */}
        <div className="bg-gray-800 rounded-lg p-6 mt-8">
          <h2 className="text-2xl font-bold mb-4">About This Demo</h2>
          <p className="text-gray-300 mb-6">
            This demo showcases NovaFuse's Regulatory Compliance API, which provides a comprehensive solution for managing regulatory frameworks, requirements, and compliance status tracking. The API is designed to help organizations streamline their compliance processes and ensure they meet regulatory requirements across multiple frameworks.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-700 p-4 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Key Features</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>Manage regulatory frameworks (GDPR, HIPAA, CCPA, etc.)</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>Track compliance requirements for each framework</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>Monitor compliance status across your organization</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>Generate compliance reports for audits</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>Map requirements between different frameworks</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">✓</span>
                  <span>Stay updated on regulatory changes</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-gray-700 p-4 rounded-lg">
              <h3 className="text-xl font-semibold mb-3">Use Cases</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Streamline compliance management across multiple frameworks</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Automate compliance reporting for audits</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Track compliance status in real-time</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Identify compliance gaps and remediation actions</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Stay ahead of regulatory changes</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Reduce compliance overhead and costs</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </BasicDemoTemplate>
    </PageWithSidebar>
  );
}
