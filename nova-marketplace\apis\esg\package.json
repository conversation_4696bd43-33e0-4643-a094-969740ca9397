{"name": "esg-api", "version": "1.0.0", "description": "ESG API for NovaGRC", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "keywords": ["esg", "environmental", "social", "governance", "api", "novagrc"], "author": "NovaFuse", "license": "MIT", "dependencies": {"express": "^4.18.2", "joi": "^17.9.2", "mongoose": "^7.2.0", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}}