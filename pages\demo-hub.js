import React from 'react';
import DemoHub from '../components/DemoHub';
import PageWithSidebar from '../components/PageWithSidebar';

export default function DemoHubPage() {
  // SEO metadata
  const pageProps = {
    title: 'Demo Hub - NovaFuse',
    description: 'Explore interactive demos of NovaFuse\'s revolutionary components and APIs. Experience the power of the Universal API Connector, Regulatory Compliance API, and more.',
    keywords: 'NovaFuse demos, API demos, UAC demo, compliance demos, GRC demos, interactive demos',
    canonical: 'https://novafuse.io/demo-hub',
    ogImage: '/images/demo-hub-og-image.png'
  };

  // Demo data
  const demos = [
    {
      id: 'uac-demo',
      title: 'Universal API Connector (UAC)',
      description: 'Experience the power of NovaFuse\'s patent-pending Universal API Connector technology. Connect, normalize, and control data across your entire ecosystem in real-time.',
      category: 'core',
      tags: ['UAC', 'API Integration', 'Compliance'],
      image: '/images/demos/uac-demo.png',
      icon: '🔌',
      url: '/uac-demo',
    },
    {
      id: 'regulatory-compliance-demo',
      title: 'Regulatory Compliance API',
      description: 'Explore NovaFuse\'s Regulatory Compliance API. Manage regulatory frameworks, requirements, and compliance status tracking across your organization.',
      category: 'api',
      tags: ['Compliance', 'Regulatory', 'API'],
      image: '/images/demos/regulatory-compliance-demo.png',
      icon: '📋',
      url: '/regulatory-compliance-demo',
    },
    {
      id: 'privacy-management-demo',
      title: 'Privacy Management API',
      description: 'Discover how NovaFuse\'s Privacy Management API helps you manage data privacy and compliance with regulations like GDPR, CCPA, and more.',
      category: 'api',
      tags: ['Privacy', 'GDPR', 'CCPA', 'API'],
      image: '/images/demos/privacy-management-demo.png',
      icon: '🔒',
      url: '/privacy-management-demo',
    },
    {
      id: 'security-assessment-demo',
      title: 'Security Assessment API',
      description: 'See how NovaFuse\'s Security Assessment API helps you manage security assessments and identify vulnerabilities in your systems.',
      category: 'api',
      tags: ['Security', 'Assessment', 'API'],
      image: '/images/demos/security-assessment-demo.png',
      icon: '🛡️',
      url: '/security-assessment-demo',
    },
    {
      id: 'control-testing-demo',
      title: 'Control Testing API',
      description: 'Learn how NovaFuse\'s Control Testing API helps you manage control testing and ensure compliance with regulatory requirements.',
      category: 'api',
      tags: ['Control', 'Testing', 'API'],
      image: '/images/demos/control-testing-demo.png',
      icon: '✓',
      url: '/control-testing-demo',
    },
    {
      id: 'esg-demo',
      title: 'ESG API',
      description: 'Explore NovaFuse\'s ESG API for managing environmental, social, and governance data and reporting.',
      category: 'api',
      tags: ['ESG', 'Sustainability', 'API'],
      image: '/images/demos/esg-demo.png',
      icon: '🌱',
      url: '/esg-demo',
    },
    {
      id: 'compliance-automation-demo',
      title: 'Compliance Automation API',
      description: 'See how NovaFuse\'s Compliance Automation API helps you automate compliance processes and reduce manual effort.',
      category: 'api',
      tags: ['Automation', 'Compliance', 'API'],
      image: '/images/demos/compliance-automation-demo.png',
      icon: '⚙️',
      url: '/compliance-automation-demo',
    },
    {
      id: 'breach-to-boardroom-demo',
      title: 'Breach to Boardroom Demo',
      description: 'Experience NovaFuse\'s end-to-end breach response workflow, from detection to executive reporting.',
      category: 'workflow',
      tags: ['Breach Response', 'Workflow', 'Executive Reporting'],
      image: '/images/demos/breach-to-boardroom-demo.png',
      icon: '📊',
      url: '/breach-to-boardroom-demo',
    },
    {
      id: 'partner-empowerment-demo',
      title: 'Partner Empowerment Model',
      description: 'Explore NovaFuse\'s revolutionary 18/82 Partner Empowerment Model and see how it transforms the GRC landscape.',
      category: 'business',
      tags: ['Partner', 'Revenue Sharing', 'Business Model'],
      image: '/images/demos/partner-empowerment-demo.png',
      icon: '🤝',
      url: '/partner-empowerment-demo',
    }
  ];

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Demo Hub', items: [
      { label: 'All Demos', href: '#' },
      { label: 'Core Components', href: '#', filter: { category: 'core' } },
      { label: 'API Demos', href: '#', filter: { category: 'api' } },
      { label: 'Workflow Demos', href: '#', filter: { category: 'workflow' } },
      { label: 'Business Model Demos', href: '#', filter: { category: 'business' } }
    ]},
    { type: 'category', label: 'Featured Demos', items: [
      { label: 'Universal API Connector', href: '/uac-demo' },
      { label: 'Regulatory Compliance API', href: '/regulatory-compliance-demo' },
      { label: 'Breach to Boardroom', href: '/breach-to-boardroom-demo' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Documentation', href: '/documentation' },
      { label: 'API Reference', href: '/api-docs' },
      { label: 'Request Custom Demo', href: '/contact' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <DemoHub demos={demos} />
    </PageWithSidebar>
  );
}
