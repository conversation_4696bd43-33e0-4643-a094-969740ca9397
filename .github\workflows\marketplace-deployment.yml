name: NovaConnect UAC Marketplace Deployment

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy to marketplace'
        required: true
        default: '1.0.0'
      tier:
        description: 'Marketplace tier'
        required: true
        default: 'core'
        type: choice
        options:
          - core
          - secure
          - enterprise
          - ai_boost

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: novafuse-cluster
  GKE_ZONE: us-central1-a
  IMAGE: novafuse-uac
  REGISTRY_HOSTNAME: gcr.io
  MARKETPLACE_BUCKET: novafuse-marketplace

jobs:
  build-marketplace-package:
    name: Build Marketplace Package
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker ${{ env.REGISTRY_HOSTNAME }}
      
      - name: Install mpdev
        run: |
          curl -LO https://github.com/GoogleCloudPlatform/marketplace-tools/releases/download/v0.3.5/mpdev_linux_amd64
          chmod +x mpdev_linux_amd64
          sudo mv mpdev_linux_amd64 /usr/local/bin/mpdev
      
      - name: Build Docker image
        run: |
          docker build \
            --tag "${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ github.event.inputs.version }}" \
            .
      
      - name: Push Docker image
        run: |
          docker push "${{ env.REGISTRY_HOSTNAME }}/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ github.event.inputs.version }}"
      
      - name: Update schema.yaml
        run: |
          # Update version in schema.yaml
          sed -i "s/publishedVersion: '.*'/publishedVersion: '${{ github.event.inputs.version }}'/g" nova-connect/marketplace/schema.yaml
          
          # Update image in schema.yaml
          sed -i "s|gcr.io/novafuse-production/novafuse-uac:.*|gcr.io/${{ env.PROJECT_ID }}/${{ env.IMAGE }}:${{ github.event.inputs.version }}|g" nova-connect/marketplace/schema.yaml
          
          # Update tier in schema.yaml
          sed -i "s/default: core/default: ${{ github.event.inputs.tier }}/g" nova-connect/marketplace/schema.yaml
      
      - name: Package for marketplace
        run: |
          cd nova-connect/marketplace
          mpdev pkg package .
          mpdev pkg verify
      
      - name: Upload to Google Cloud Storage
        run: |
          # Create bucket if it doesn't exist
          gsutil mb -p ${{ env.PROJECT_ID }} -l us-central1 gs://${{ env.MARKETPLACE_BUCKET }} || true
          
          # Upload package to GCS
          gsutil cp nova-connect/marketplace/*.tar.gz gs://${{ env.MARKETPLACE_BUCKET }}/
      
      - name: Generate marketplace listing
        run: |
          # Create marketplace listing
          cat > marketplace-listing.yaml << EOF
          name: NovaConnect UAC
          version: ${{ github.event.inputs.version }}
          description: Universal API Connector for seamless API integration
          icon: https://novafuse.io/images/logo.png
          documentationUrl: https://docs.novafuse.io
          supportUrl: https://novafuse.io/support
          categories:
            - API Management
            - Integration
            - Compliance
          packageUrl: gs://${{ env.MARKETPLACE_BUCKET }}/novafuse-uac-${{ github.event.inputs.version }}.tar.gz
          tier: ${{ github.event.inputs.tier }}
          EOF
      
      - name: Submit to marketplace
        run: |
          # Submit to marketplace
          gcloud beta marketplace solutions submit \
            --project ${{ env.PROJECT_ID }} \
            --solution-id novafuse-uac \
            --listing-file marketplace-listing.yaml
      
      - name: Verify submission
        run: |
          # Verify submission
          gcloud beta marketplace solutions describe \
            --project ${{ env.PROJECT_ID }} \
            --solution-id novafuse-uac
      
      - name: Notify submission
        run: |
          # Send notification
          echo "NovaConnect UAC ${{ github.event.inputs.version }} has been submitted to Google Cloud Marketplace"
          
          # Send email notification
          curl -X POST \
            -H "Content-Type: application/json" \
            -d '{
              "to": "<EMAIL>",
              "subject": "NovaConnect UAC ${{ github.event.inputs.version }} Marketplace Submission",
              "body": "NovaConnect UAC ${{ github.event.inputs.version }} has been submitted to Google Cloud Marketplace with tier ${{ github.event.inputs.tier }}."
            }' \
            ${{ secrets.NOTIFICATION_WEBHOOK_URL }}
  
  test-marketplace-deployment:
    name: Test Marketplace Deployment
    runs-on: ubuntu-latest
    needs: build-marketplace-package
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true
      
      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}
      
      - name: Create test namespace
        run: |
          kubectl create namespace marketplace-test || true
      
      - name: Deploy from marketplace
        run: |
          # Deploy from marketplace
          mpdev install \
            --deployer=gcr.io/${{ env.PROJECT_ID }}/${{ env.IMAGE }}/deployer:${{ github.event.inputs.version }} \
            --parameters='{
              "name": "novafuse-uac-test",
              "namespace": "marketplace-test",
              "tier": "${{ github.event.inputs.tier }}",
              "mongodb.uri": "mongodb://mongodb:27017/novafuse",
              "redis.uri": "redis://redis:6379"
            }'
      
      - name: Verify deployment
        run: |
          # Wait for deployment to be ready
          kubectl rollout status deployment/novafuse-uac-test --namespace=marketplace-test
      
      - name: Run smoke tests
        run: |
          # Get service URL
          SERVICE_URL=$(kubectl get service novafuse-uac-test --namespace=marketplace-test -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Run smoke tests
          curl -f http://$SERVICE_URL/health
          curl -f http://$SERVICE_URL/metrics
      
      - name: Cleanup test deployment
        run: |
          # Delete test deployment
          kubectl delete namespace marketplace-test
      
      - name: Notify test results
        run: |
          # Send notification
          echo "NovaConnect UAC ${{ github.event.inputs.version }} marketplace deployment test completed successfully"
          
          # Send email notification
          curl -X POST \
            -H "Content-Type: application/json" \
            -d '{
              "to": "<EMAIL>",
              "subject": "NovaConnect UAC ${{ github.event.inputs.version }} Marketplace Test",
              "body": "NovaConnect UAC ${{ github.event.inputs.version }} marketplace deployment test completed successfully."
            }' \
            ${{ secrets.NOTIFICATION_WEBHOOK_URL }}
