/**
 * NovaCore Assessment Template Controller
 * 
 * This controller handles API requests related to assessment templates.
 */

const { AssessmentTemplateService } = require('../services');
const logger = require('../../../config/logger');

class AssessmentTemplateController {
  /**
   * Create a new assessment template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await AssessmentTemplateService.createTemplate(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all assessment templates
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllTemplates(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.type) filter.type = req.query.type;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.frameworks) filter.frameworks = req.query.frameworks.split(',');
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.isDefault) filter.isDefault = req.query.isDefault;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await AssessmentTemplateService.getAllTemplates(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get assessment template by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getTemplateById(req, res, next) {
    try {
      const template = await AssessmentTemplateService.getTemplateById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update assessment template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await AssessmentTemplateService.updateTemplate(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete assessment template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteTemplate(req, res, next) {
    try {
      await AssessmentTemplateService.deleteTemplate(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Assessment template deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Set template as default
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async setAsDefault(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await AssessmentTemplateService.setAsDefault(req.params.id, userId);
      
      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Create new version of template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createNewVersion(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const template = await AssessmentTemplateService.createNewVersion(req.params.id, req.body, userId);
      
      res.status(201).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get default template
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getDefaultTemplate(req, res, next) {
    try {
      const { organizationId } = req.params;
      const type = req.query.type || 'vendor';
      
      const template = await AssessmentTemplateService.getDefaultTemplate(organizationId, type);
      
      res.status(200).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Import template from JSON
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async importTemplate(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { organizationId } = req.params;
      
      const template = await AssessmentTemplateService.importTemplate(req.body, organizationId, userId);
      
      res.status(201).json({
        success: true,
        data: template
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AssessmentTemplateController();
