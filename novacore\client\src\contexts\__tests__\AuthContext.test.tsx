/**
 * Tests for Authentication Context
 */

import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from '../AuthContext';
import authService from '../../api/authService';

// Mock auth service
jest.mock('../../api/authService', () => ({
  __esModule: true,
  default: {
    login: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: jest.fn(),
    getCurrentUser: jest.fn(),
    getToken: jest.fn(),
    hasPermissions: jest.fn()
  }
}));

// Test component that uses the auth context
const TestComponent = () => {
  const { isAuthenticated, user, login, logout, hasPermission } = useAuth();
  
  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'Authenticated' : 'Not authenticated'}
      </div>
      {user && (
        <div data-testid="user-info">
          {user.email} - {user.role}
        </div>
      )}
      <button 
        data-testid="login-button" 
        onClick={() => login({ email: '<EMAIL>', password: 'password123' })}
      >
        Login
      </button>
      <button data-testid="logout-button" onClick={logout}>
        Logout
      </button>
      <div data-testid="has-permission">
        {hasPermission('frameworks:view') ? 'Has permission' : 'No permission'}
      </div>
    </div>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should provide authentication state', () => {
    (authService.isAuthenticated as jest.Mock).mockReturnValue(true);
    (authService.getCurrentUser as jest.Mock).mockReturnValue({
      id: 'user123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      organizationId: 'org123',
      permissions: ['frameworks:view']
    });
    (authService.getToken as jest.Mock).mockReturnValue('valid_token');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    expect(screen.getByTestId('user-info')).toHaveTextContent('<EMAIL> - admin');
  });
  
  it('should handle login', async () => {
    const mockUser = {
      id: 'user123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      organizationId: 'org123',
      permissions: ['frameworks:view']
    };
    
    (authService.login as jest.Mock).mockResolvedValue(mockUser);
    (authService.getToken as jest.Mock).mockReturnValue('valid_token');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Initially not authenticated
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
    
    // Click login button
    await act(async () => {
      userEvent.click(screen.getByTestId('login-button'));
    });
    
    // Wait for state update
    await waitFor(() => {
      expect(authService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });
  });
  
  it('should handle logout', async () => {
    // Start authenticated
    (authService.isAuthenticated as jest.Mock).mockReturnValue(true);
    (authService.getCurrentUser as jest.Mock).mockReturnValue({
      id: 'user123',
      email: '<EMAIL>',
      role: 'admin'
    });
    (authService.getToken as jest.Mock).mockReturnValue('valid_token');
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    // Initially authenticated
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    
    // After logout, auth service will return false
    (authService.isAuthenticated as jest.Mock).mockReturnValue(false);
    (authService.getCurrentUser as jest.Mock).mockReturnValue(null);
    (authService.getToken as jest.Mock).mockReturnValue(null);
    
    // Click logout button
    await act(async () => {
      userEvent.click(screen.getByTestId('logout-button'));
    });
    
    // Check logout was called
    expect(authService.logout).toHaveBeenCalled();
    
    // Wait for state update
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not authenticated');
    });
  });
  
  it('should check permissions', () => {
    // User has permission
    (authService.isAuthenticated as jest.Mock).mockReturnValue(true);
    (authService.getCurrentUser as jest.Mock).mockReturnValue({
      id: 'user123',
      email: '<EMAIL>',
      permissions: ['frameworks:view']
    });
    (authService.hasPermissions as jest.Mock).mockImplementation((perms) => {
      return perms.includes('frameworks:view');
    });
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    expect(screen.getByTestId('has-permission')).toHaveTextContent('Has permission');
    
    // User doesn't have permission
    (authService.hasPermissions as jest.Mock).mockReturnValue(false);
    
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    expect(screen.getByTestId('has-permission')).toHaveTextContent('No permission');
  });
});
