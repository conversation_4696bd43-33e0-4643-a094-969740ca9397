/**
 * UCTO Dashboard Schema
 * 
 * This schema defines the structure and components of the UCTO Unified Dashboard.
 * It follows NovaVision principles for dynamic UI rendering based on API responses.
 */

/**
 * Dashboard schema definition
 * @type {Object}
 */
const dashboardSchema = {
  // Dashboard metadata
  metadata: {
    name: "UCTO Unified Dashboard",
    description: "Comprehensive dashboard for Universal Compliance Tracking Optimizer",
    version: "1.0.0"
  },
  
  // Layout configuration
  layout: {
    type: "grid",
    columns: 12,
    rows: "auto",
    gap: "1rem"
  },
  
  // Dashboard sections
  sections: [
    {
      id: "overview",
      title: "Compliance Overview",
      description: "High-level overview of compliance status",
      layout: {
        x: 0,
        y: 0,
        width: 12,
        height: "auto"
      },
      components: [
        {
          type: "compliance-score-card",
          title: "Overall Compliance Score",
          dataSource: "compliance-score",
          width: 4,
          height: 2,
          settings: {
            showTrend: true,
            thresholds: [
              { value: 60, color: "red" },
              { value: 80, color: "yellow" },
              { value: 90, color: "green" }
            ]
          }
        },
        {
          type: "framework-summary",
          title: "Framework Coverage",
          dataSource: "framework-coverage",
          width: 4,
          height: 2,
          settings: {
            showPercentage: true,
            showCount: true
          }
        },
        {
          type: "status-summary",
          title: "Requirement Status",
          dataSource: "requirement-status",
          width: 4,
          height: 2,
          settings: {
            showLegend: true,
            colors: {
              completed: "#4CAF50",
              in_progress: "#2196F3",
              pending: "#FFC107",
              overdue: "#F44336"
            }
          }
        }
      ]
    },
    {
      id: "requirements",
      title: "Requirements",
      description: "Compliance requirements tracking",
      layout: {
        x: 0,
        y: 1,
        width: 6,
        height: "auto"
      },
      components: [
        {
          type: "requirements-table",
          title: "Requirements",
          dataSource: "requirements-list",
          width: 6,
          height: 4,
          settings: {
            pageSize: 10,
            columns: [
              { field: "name", header: "Name", sortable: true },
              { field: "framework", header: "Framework", sortable: true, filterable: true },
              { field: "status", header: "Status", sortable: true, filterable: true },
              { field: "due_date", header: "Due Date", sortable: true, type: "date" }
            ],
            actions: ["view", "edit"]
          }
        }
      ]
    },
    {
      id: "activities",
      title: "Activities",
      description: "Compliance activities tracking",
      layout: {
        x: 6,
        y: 1,
        width: 6,
        height: "auto"
      },
      components: [
        {
          type: "activities-table",
          title: "Activities",
          dataSource: "activities-list",
          width: 6,
          height: 4,
          settings: {
            pageSize: 10,
            columns: [
              { field: "name", header: "Name", sortable: true },
              { field: "requirement", header: "Requirement", sortable: true },
              { field: "status", header: "Status", sortable: true, filterable: true },
              { field: "due_date", header: "Due Date", sortable: true, type: "date" }
            ],
            actions: ["view", "edit"]
          }
        }
      ]
    },
    {
      id: "predictions",
      title: "Predictive Intelligence",
      description: "Compliance predictions and forecasts",
      layout: {
        x: 0,
        y: 2,
        width: 12,
        height: "auto"
      },
      components: [
        {
          type: "compliance-gap-prediction",
          title: "Predicted Compliance Gaps",
          dataSource: "compliance-gap-predictions",
          width: 4,
          height: 3,
          settings: {
            showProbability: true,
            thresholds: [
              { value: 0.3, label: "Low Risk", color: "green" },
              { value: 0.6, label: "Medium Risk", color: "yellow" },
              { value: 0.8, label: "High Risk", color: "red" }
            ]
          }
        },
        {
          type: "resource-forecast",
          title: "Resource Forecast",
          dataSource: "resource-forecast",
          width: 4,
          height: 3,
          settings: {
            resourceTypes: ["staff", "budget", "time"],
            timeHorizon: 90,
            showChart: true
          }
        },
        {
          type: "recommended-actions",
          title: "Recommended Actions",
          dataSource: "recommended-actions",
          width: 4,
          height: 3,
          settings: {
            maxItems: 5,
            showPriority: true,
            priorityColors: {
              high: "red",
              medium: "yellow",
              low: "green"
            }
          }
        }
      ]
    },
    {
      id: "control-mapping",
      title: "Control Mapping",
      description: "Cross-framework control mapping",
      layout: {
        x: 0,
        y: 3,
        width: 12,
        height: "auto"
      },
      components: [
        {
          type: "framework-mapping-heatmap",
          title: "Framework Mapping Coverage",
          dataSource: "framework-mapping-coverage",
          width: 6,
          height: 4,
          settings: {
            showLegend: true,
            colorScale: ["#FFFFFF", "#E3F2FD", "#90CAF9", "#42A5F5", "#1E88E5", "#0D47A1"]
          }
        },
        {
          type: "control-mapping-sankey",
          title: "Control Relationships",
          dataSource: "control-mapping-relationships",
          width: 6,
          height: 4,
          settings: {
            showLabels: true,
            nodeColor: "#2196F3",
            linkColor: "#90CAF9"
          }
        }
      ]
    },
    {
      id: "adaptive-optimization",
      title: "Adaptive Optimization",
      description: "Adaptive workflow optimization",
      layout: {
        x: 0,
        y: 4,
        width: 12,
        height: "auto"
      },
      components: [
        {
          type: "optimization-strategies",
          title: "Applied Optimization Strategies",
          dataSource: "optimization-strategies",
          width: 6,
          height: 3,
          settings: {
            showImpact: true,
            showDescription: true
          }
        },
        {
          type: "optimization-recommendations",
          title: "Optimization Recommendations",
          dataSource: "optimization-recommendations",
          width: 6,
          height: 3,
          settings: {
            maxItems: 5,
            showImpact: true,
            showDescription: true
          }
        }
      ]
    },
    {
      id: "integrations",
      title: "Integrations",
      description: "Integration status with other Universal components",
      layout: {
        x: 0,
        y: 5,
        width: 12,
        height: "auto"
      },
      components: [
        {
          type: "integration-status",
          title: "Integration Status",
          dataSource: "integration-status",
          width: 12,
          height: 2,
          settings: {
            components: ["UCECS", "UCWO", "UCVF", "UCTF"],
            showStatus: true,
            showLastSync: true,
            statusColors: {
              connected: "green",
              disconnected: "red",
              error: "orange",
              pending: "yellow"
            }
          }
        }
      ]
    }
  ],
  
  // Data sources for the dashboard
  dataSources: [
    {
      id: "compliance-score",
      type: "api",
      endpoint: "/api/dashboard/compliance-score",
      refreshInterval: 300, // 5 minutes
      cacheEnabled: true
    },
    {
      id: "framework-coverage",
      type: "api",
      endpoint: "/api/dashboard/framework-coverage",
      refreshInterval: 300,
      cacheEnabled: true
    },
    {
      id: "requirement-status",
      type: "api",
      endpoint: "/api/dashboard/requirement-status",
      refreshInterval: 300,
      cacheEnabled: true
    },
    {
      id: "requirements-list",
      type: "api",
      endpoint: "/api/dashboard/requirements",
      refreshInterval: 300,
      cacheEnabled: true,
      pagination: true,
      sorting: true,
      filtering: true
    },
    {
      id: "activities-list",
      type: "api",
      endpoint: "/api/dashboard/activities",
      refreshInterval: 300,
      cacheEnabled: true,
      pagination: true,
      sorting: true,
      filtering: true
    },
    {
      id: "compliance-gap-predictions",
      type: "api",
      endpoint: "/api/dashboard/predictions/compliance-gaps",
      refreshInterval: 600, // 10 minutes
      cacheEnabled: true
    },
    {
      id: "resource-forecast",
      type: "api",
      endpoint: "/api/dashboard/predictions/resource-forecast",
      refreshInterval: 600,
      cacheEnabled: true
    },
    {
      id: "recommended-actions",
      type: "api",
      endpoint: "/api/dashboard/predictions/recommended-actions",
      refreshInterval: 600,
      cacheEnabled: true
    },
    {
      id: "framework-mapping-coverage",
      type: "api",
      endpoint: "/api/dashboard/control-mapping/coverage",
      refreshInterval: 600,
      cacheEnabled: true
    },
    {
      id: "control-mapping-relationships",
      type: "api",
      endpoint: "/api/dashboard/control-mapping/relationships",
      refreshInterval: 600,
      cacheEnabled: true
    },
    {
      id: "optimization-strategies",
      type: "api",
      endpoint: "/api/dashboard/optimization/strategies",
      refreshInterval: 600,
      cacheEnabled: true
    },
    {
      id: "optimization-recommendations",
      type: "api",
      endpoint: "/api/dashboard/optimization/recommendations",
      refreshInterval: 600,
      cacheEnabled: true
    },
    {
      id: "integration-status",
      type: "api",
      endpoint: "/api/dashboard/integrations/status",
      refreshInterval: 300,
      cacheEnabled: true
    }
  ],
  
  // User permissions for the dashboard
  permissions: {
    view: ["admin", "compliance-manager", "auditor"],
    edit: ["admin", "compliance-manager"],
    configure: ["admin"]
  },
  
  // Dashboard settings
  settings: {
    refreshInterval: 300, // 5 minutes
    theme: "light",
    allowCustomization: true,
    defaultFilters: {
      timeRange: "last30Days"
    }
  }
};

module.exports = dashboardSchema;
