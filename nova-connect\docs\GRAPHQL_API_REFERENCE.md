# GraphQL API Reference

NovaConnect provides a comprehensive GraphQL API for flexible querying and mutations. This document provides a reference for the available queries, mutations, and subscriptions.

## Endpoint

The GraphQL API is available at:

```
http://localhost:3010/graphql
```

## Authentication

Authentication is required for most operations. You can authenticate by including an `Authorization` header with a valid JWT token:

```
Authorization: Bearer <token>
```

## GraphQL Playground

NovaConnect includes a GraphQL Playground that you can use to explore the API. It's available at the same endpoint as the API:

```
http://localhost:3010/graphql
```

## Types

### Connector

```graphql
type Connector {
  id: ID!
  name: String!
  description: String
  category: String
  version: String
  author: String
  authentication: Authentication
  endpoints: [Endpoint]
  createdAt: String
  updatedAt: String
}

type Authentication {
  type: String!
  fields: [AuthField]
}

type AuthField {
  id: String!
  name: String!
  description: String
  type: String!
  required: Boolean
  sensitive: Boolean
}

type Endpoint {
  id: ID!
  name: String!
  description: String
  method: String!
  path: String!
  parameters: [Parameter]
  response: Response
}

type Parameter {
  id: String!
  name: String!
  description: String
  type: String!
  required: Boolean
  default: String
}

type Response {
  type: String!
  schema: String
}
```

### Credential

```graphql
type Credential {
  id: ID!
  connectorId: String!
  name: String
  createdAt: String
  updatedAt: String
}
```

### Execution

```graphql
type ExecutionResult {
  id: ID!
  connectorId: String!
  endpointId: String!
  status: String!
  data: JSON
  error: String
  startTime: String
  endTime: String
  duration: Int
}
```

### RBAC

```graphql
type Role {
  id: ID!
  name: String!
  description: String
  permissions: [String]
  isSystem: Boolean
  createdAt: String
  updatedAt: String
}

type Permission {
  id: ID!
  name: String!
  description: String
  resource: String!
  action: String!
  createdAt: String
  updatedAt: String
}
```

### Billing

```graphql
type BillingPlan {
  id: ID!
  name: String!
  price: Float
  features: JSON
}

type BillingCycle {
  id: ID!
  name: String!
  months: Int
  discount: Float
}

type Subscription {
  id: ID!
  tenantId: String!
  planId: String!
  planName: String!
  cycleId: String
  cycleName: String
  price: Float
  features: JSON
  startDate: String
  endDate: String
  status: String!
  createdAt: String
  updatedAt: String
}
```

### Marketplace

```graphql
type MarketplacePlan {
  id: ID!
  name: String!
  description: String
  features: JSON
}

type Tenant {
  id: ID!
  name: String!
  planId: String!
  planName: String!
  features: JSON
  status: String!
  createdAt: String
  updatedAt: String
}
```

## Queries

### Connector Queries

```graphql
# Get all connectors
query GetConnectors {
  connectors {
    id
    name
    description
    category
  }
}

# Get a connector by ID
query GetConnector($id: ID!) {
  connector(id: $id) {
    id
    name
    description
    category
    endpoints {
      id
      name
      method
      path
    }
  }
}

# Get connectors by category
query GetConnectorsByCategory($category: String!) {
  connectorsByCategory(category: $category) {
    id
    name
    description
  }
}

# Search connectors
query SearchConnectors($query: String!) {
  searchConnectors(query: $query) {
    id
    name
    description
    category
  }
}
```

### Credential Queries

```graphql
# Get all credentials
query GetCredentials {
  credentials {
    id
    connectorId
    name
    createdAt
  }
}

# Get a credential by ID
query GetCredential($id: ID!) {
  credential(id: $id) {
    id
    connectorId
    name
    createdAt
    updatedAt
  }
}
```

### RBAC Queries

```graphql
# Get all roles
query GetRoles {
  roles {
    id
    name
    description
    permissions
  }
}

# Get a role by ID
query GetRole($id: ID!) {
  role(id: $id) {
    id
    name
    description
    permissions
    isSystem
    createdAt
    updatedAt
  }
}

# Get all permissions
query GetPermissions {
  permissions {
    id
    name
    description
    resource
    action
  }
}

# Get a permission by ID
query GetPermission($id: ID!) {
  permission(id: $id) {
    id
    name
    description
    resource
    action
    createdAt
    updatedAt
  }
}

# Get user roles
query GetUserRoles($userId: ID!) {
  userRoles(userId: $userId) {
    id
    name
    description
  }
}

# Get user permissions
query GetUserPermissions($userId: ID!) {
  userPermissions(userId: $userId)
}

# Check if user has permission
query HasPermission($userId: ID!, $permissionId: String!) {
  hasPermission(userId: $userId, permissionId: $permissionId)
}
```

### Billing Queries

```graphql
# Get all billing plans
query GetBillingPlans {
  billingPlans {
    id
    name
    price
    features
  }
}

# Get a billing plan by ID
query GetBillingPlan($id: ID!) {
  billingPlan(id: $id) {
    id
    name
    price
    features
  }
}

# Get all billing cycles
query GetBillingCycles {
  billingCycles {
    id
    name
    months
    discount
  }
}

# Get a billing cycle by ID
query GetBillingCycle($id: ID!) {
  billingCycle(id: $id) {
    id
    name
    months
    discount
  }
}

# Calculate price
query CalculatePrice($planId: ID!, $cycleId: ID!) {
  calculatePrice(planId: $planId, cycleId: $cycleId)
}
```

### Marketplace Queries

```graphql
# Get all marketplace plans
query GetMarketplacePlans {
  marketplacePlans {
    id
    name
    description
    features
  }
}

# Get a marketplace plan by ID
query GetMarketplacePlan($id: ID!) {
  marketplacePlan(id: $id) {
    id
    name
    description
    features
  }
}

# Get tenant status
query GetTenantStatus($id: ID!) {
  tenantStatus(id: $id)
}
```

## Mutations

### Connector Mutations

```graphql
# Register a new connector
mutation RegisterConnector($input: ConnectorInput!) {
  registerConnector(input: $input) {
    id
    name
    description
  }
}

# Update a connector
mutation UpdateConnector($id: ID!, $input: ConnectorInput!) {
  updateConnector(id: $id, input: $input) {
    id
    name
    description
  }
}

# Delete a connector
mutation DeleteConnector($id: ID!) {
  deleteConnector(id: $id)
}
```

### Credential Mutations

```graphql
# Store credentials
mutation StoreCredentials($connectorId: ID!, $credentials: JSON!) {
  storeCredentials(connectorId: $connectorId, credentials: $credentials) {
    id
    connectorId
  }
}

# Delete credentials
mutation DeleteCredentials($id: ID!) {
  deleteCredentials(id: $id)
}

# Test connection
mutation TestConnection($id: ID!, $connectorId: ID!) {
  testConnection(id: $id, connectorId: $connectorId)
}
```

### Execution Mutations

```graphql
# Execute an endpoint
mutation ExecuteEndpoint($connectorId: ID!, $endpointId: ID!, $credentialId: ID!, $parameters: JSON) {
  executeEndpoint(
    connectorId: $connectorId
    endpointId: $endpointId
    credentialId: $credentialId
    parameters: $parameters
  ) {
    id
    status
    data
    error
    duration
  }
}
```

### RBAC Mutations

```graphql
# Create a role
mutation CreateRole($input: RoleInput!) {
  createRole(input: $input) {
    id
    name
    description
    permissions
  }
}

# Update a role
mutation UpdateRole($id: ID!, $input: RoleInput!) {
  updateRole(id: $id, input: $input) {
    id
    name
    description
    permissions
  }
}

# Delete a role
mutation DeleteRole($id: ID!) {
  deleteRole(id: $id)
}

# Create a permission
mutation CreatePermission($input: PermissionInput!) {
  createPermission(input: $input) {
    id
    name
    description
    resource
    action
  }
}

# Update a permission
mutation UpdatePermission($id: ID!, $input: PermissionInput!) {
  updatePermission(id: $id, input: $input) {
    id
    name
    description
    resource
    action
  }
}

# Delete a permission
mutation DeletePermission($id: ID!) {
  deletePermission(id: $id)
}

# Assign role to user
mutation AssignRoleToUser($userId: ID!, $roleId: ID!) {
  assignRoleToUser(userId: $userId, roleId: $roleId)
}

# Remove role from user
mutation RemoveRoleFromUser($userId: ID!, $roleId: ID!) {
  removeRoleFromUser(userId: $userId, roleId: $roleId)
}
```

### Billing Mutations

```graphql
# Create a subscription
mutation CreateSubscription($tenantId: ID!, $planId: ID!, $cycleId: ID!) {
  createSubscription(tenantId: $tenantId, planId: $planId, cycleId: $cycleId) {
    id
    tenantId
    planId
    planName
    cycleId
    cycleName
    price
    features
    startDate
    endDate
    status
  }
}

# Report usage
mutation ReportUsage($subscriptionId: ID!, $metricId: String!, $quantity: Float!) {
  reportUsage(subscriptionId: $subscriptionId, metricId: $metricId, quantity: $quantity)
}
```

### Marketplace Mutations

```graphql
# Provision a tenant
mutation ProvisionTenant($input: TenantInput!) {
  provisionTenant(input: $input) {
    id
    name
    planId
    planName
    features
    status
    createdAt
    updatedAt
  }
}

# Update tenant plan
mutation UpdateTenantPlan($tenantId: ID!, $planId: ID!) {
  updateTenantPlan(tenantId: $tenantId, planId: $planId) {
    id
    name
    planId
    planName
    features
    status
    updatedAt
  }
}

# Deprovision a tenant
mutation DeprovisionTenant($tenantId: ID!) {
  deprovisionTenant(tenantId: $tenantId)
}
```

## Subscriptions

```graphql
# Subscribe to execution completed events
subscription ExecutionCompleted($connectorId: ID, $endpointId: ID) {
  executionCompleted(connectorId: $connectorId, endpointId: $endpointId) {
    id
    connectorId
    endpointId
    status
    data
    error
    startTime
    endTime
    duration
  }
}
```
