/**
 * Regulations Page
 * 
 * This page displays a list of regulations and allows users to manage them.
 * It leverages the existing NovaPrime components and connects them to the NovaPulse API.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { NovaPulseAPI, Regulation } from '@/api/novaPulseApi';
import { ComplianceNavigation } from '@/components/compliance/ComplianceNavigation';
import { RegulationItem } from '@/components/regulatory/RegulationItem';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, Download, Calendar } from 'lucide-react';

export default function RegulationsPage() {
  const router = useRouter();
  const [regulations, setRegulations] = useState<Regulation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterCountry, setFilterCountry] = useState('');

  const api = new NovaPulseAPI();

  useEffect(() => {
    loadRegulations();
  }, []);

  const loadRegulations = async () => {
    setLoading(true);
    try {
      const response = await api.getRegulations();
      setRegulations(response.data);
    } catch (error) {
      console.error('Error loading regulations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleCreateRegulation = () => {
    router.push('/regulations/create');
  };

  const handleRegulationClick = (id: string) => {
    router.push(`/regulations/${id}`);
  };

  const filteredRegulations = regulations.filter(regulation => {
    // Apply search filter
    const matchesSearch = 
      regulation.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      regulation.description.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Apply category filter
    const matchesCategory = !filterCategory || regulation.category === filterCategory;
    
    // Apply country filter
    const matchesCountry = !filterCountry || 
      regulation.jurisdiction.country === filterCountry || 
      regulation.jurisdiction.isGlobal;
    
    return matchesSearch && matchesCategory && matchesCountry;
  });

  // Get unique categories and countries for filters
  const categories = [...new Set(regulations.map(r => r.category))];
  const countries = [...new Set(regulations.map(r => r.jurisdiction.country))];

  // Filter regulations based on active tab
  const tabFilteredRegulations = filteredRegulations.filter(regulation => {
    if (activeTab === 'all') return true;
    if (activeTab === 'active') return regulation.status === 'active';
    if (activeTab === 'upcoming') {
      // Check if any version has an effective date in the future
      return regulation.versions.some(v => 
        new Date(v.effectiveDate) > new Date() && v.status === 'upcoming'
      );
    }
    if (activeTab === 'archived') return regulation.status === 'archived';
    return true;
  });

  // Convert regulations to the format expected by RegulationItem component
  const adaptedRegulations = tabFilteredRegulations.map(regulation => {
    // Find the current version
    const currentVersion = regulation.versions.find(v => 
      v.versionNumber === regulation.currentVersion
    ) || regulation.versions[0];

    return {
      id: regulation._id,
      title: regulation.name,
      description: regulation.description,
      status: regulation.status,
      region: regulation.jurisdiction.country || 'Global',
      framework: regulation.category,
      date: currentVersion?.publicationDate || '',
      effectiveDate: currentVersion?.effectiveDate || '',
      impact: regulation.applicability.industries.length > 2 ? 'High' : 
              regulation.applicability.industries.length > 0 ? 'Medium' : 'Low'
    };
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Regulations</h1>
      
      <ComplianceNavigation />
      
      <div className="mb-6 flex flex-col md:flex-row justify-between gap-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <input
            type="text"
            placeholder="Search regulations..."
            className="pl-9 pr-4 py-2 border rounded-md w-full md:w-64"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
        
        <div className="flex gap-2">
          <select
            className="border rounded-md px-3 py-2 text-sm"
            value={filterCategory}
            onChange={(e) => setFilterCategory(e.target.value)}
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <select
            className="border rounded-md px-3 py-2 text-sm"
            value={filterCountry}
            onChange={(e) => setFilterCountry(e.target.value)}
          >
            <option value="">All Jurisdictions</option>
            {countries.map(country => (
              <option key={country} value={country}>{country || 'Global'}</option>
            ))}
          </select>
          
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleCreateRegulation}>
            <Plus className="h-4 w-4 mr-2" />
            New Regulation
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="all">All Regulations</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="archived">Archived</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading regulations...</div>
          ) : adaptedRegulations.length === 0 ? (
            <div className="text-center py-8">
              No regulations found. {searchQuery && 'Try a different search term.'}
            </div>
          ) : (
            <div className="space-y-6 relative pl-36">
              {/* Timeline line */}
              <div className="absolute left-16 top-0 bottom-0 w-0.5 bg-gray-200"></div>
              
              {adaptedRegulations.map(regulation => (
                <div key={regulation.id} onClick={() => handleRegulationClick(regulation.id)}>
                  <RegulationItem regulation={regulation} />
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
