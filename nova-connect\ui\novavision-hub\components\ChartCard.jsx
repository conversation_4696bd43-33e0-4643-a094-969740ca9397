/**
 * ChartCard Component
 * 
 * A reusable chart card component for displaying various types of charts with enhanced styling and interactivity.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import Chart from 'chart.js/auto';

/**
 * ChartCard component
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {string} props.chartType - Chart type (bar, line, pie, doughnut, radar, polarArea)
 * @param {Object} props.data - Chart data
 * @param {Array} props.data.labels - Chart labels
 * @param {Array} props.data.datasets - Chart datasets
 * @param {Object} [props.options] - Chart.js options
 * @param {boolean} [props.loading=false] - Whether the card is in loading state
 * @param {boolean} [props.collapsible=false] - Whether the card is collapsible
 * @param {boolean} [props.defaultCollapsed=false] - Whether the card is collapsed by default
 * @param {Function} [props.onRefresh] - Function to call when the refresh button is clicked
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} ChartCard component
 */
const ChartCard = ({
  title,
  chartType,
  data,
  options = {},
  loading = false,
  collapsible = false,
  defaultCollapsed = false,
  onRefresh,
  className = '',
  style = {}
}) => {
  const chartRef = useRef(null);
  const canvasRef = useRef(null);
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [isHovered, setIsHovered] = useState(false);
  
  // Create and update chart
  useEffect(() => {
    if (collapsed || loading || !canvasRef.current) return;
    
    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy();
    }
    
    // Create default options based on chart type
    const defaultOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          padding: 10,
          cornerRadius: 4,
          boxPadding: 3
        }
      }
    };
    
    // Add specific options based on chart type
    if (chartType === 'bar') {
      defaultOptions.scales = {
        x: {
          grid: {
            display: false
          }
        },
        y: {
          beginAtZero: true
        }
      };
    } else if (chartType === 'line') {
      defaultOptions.elements = {
        line: {
          tension: 0.4
        },
        point: {
          radius: 4,
          hitRadius: 10,
          hoverRadius: 6
        }
      };
    } else if (chartType === 'pie' || chartType === 'doughnut') {
      defaultOptions.cutout = chartType === 'doughnut' ? '70%' : undefined;
      defaultOptions.plugins.legend.position = 'right';
    }
    
    // Create chart
    const ctx = canvasRef.current.getContext('2d');
    chartRef.current = new Chart(ctx, {
      type: chartType,
      data: data,
      options: { ...defaultOptions, ...options }
    });
    
    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [chartType, data, options, collapsed, loading]);
  
  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden transition-all duration-300 ${className} ${isHovered ? 'shadow-md' : ''}`}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-testid="chart-card"
    >
      <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
        <div className="flex items-center space-x-2">
          {onRefresh && (
            <button
              className="p-1 rounded-full hover:bg-white/50 transition-colors duration-200"
              onClick={onRefresh}
              aria-label="Refresh"
              data-testid="refresh-button"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
            </button>
          )}
          {collapsible && (
            <button
              className="p-1 rounded-full hover:bg-white/50 transition-colors duration-200"
              onClick={() => setCollapsed(!collapsed)}
              aria-label={collapsed ? 'Expand' : 'Collapse'}
              data-testid="collapse-button"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`h-5 w-5 transition-transform duration-200 ${collapsed ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>
      
      <div
        className={`overflow-hidden transition-all duration-300 ${collapsed ? 'max-h-0 opacity-0' : 'max-h-[500px] opacity-100'}`}
        data-testid="chart-container"
      >
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <div className="p-4 h-64">
            <canvas ref={canvasRef} />
          </div>
        )}
      </div>
    </div>
  );
};

ChartCard.propTypes = {
  title: PropTypes.string.isRequired,
  chartType: PropTypes.oneOf(['bar', 'line', 'pie', 'doughnut', 'radar', 'polarArea']).isRequired,
  data: PropTypes.shape({
    labels: PropTypes.array.isRequired,
    datasets: PropTypes.array.isRequired
  }).isRequired,
  options: PropTypes.object,
  loading: PropTypes.bool,
  collapsible: PropTypes.bool,
  defaultCollapsed: PropTypes.bool,
  onRefresh: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default ChartCard;
