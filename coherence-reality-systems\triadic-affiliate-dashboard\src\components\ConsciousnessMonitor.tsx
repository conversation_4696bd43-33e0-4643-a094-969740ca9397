import { motion } from 'framer-motion'
import { BrainIcon } from '@heroicons/react/outline'

export function ConsciousnessMonitor() {
  const [level, setLevel] = useState(75)

  return (
    <div className="space-y-4">
      <div className="relative">
        <motion.div
          animate={{
            width: `${level}%`,
            backgroundColor: level > 90 ? 'rgb(34 197 94)' : level > 70 ? 'rgb(59 130 246)' : 'rgb(156 52 173)'
          }}
          className="absolute inset-0 rounded-full"
          style={{
            height: '8px',
            backgroundColor: 'transparent'
          }}
        />
        <div className="w-full h-8 bg-gray-200 rounded-full">
          <div className="absolute inset-0 rounded-full" />
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-sm text-gray-400">Consciousness Level</h3>
          <p className="text-xl font-bold">{level}%</p>
        </div>
        <BrainIcon className="w-8 h-8 text-purple-500" />
      </div>

      <button
        onClick={() => setLevel(Math.floor(Math.random() * 100) + 1)}
        className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
      >
        Update Level
      </button>
    </div>
  )
}
