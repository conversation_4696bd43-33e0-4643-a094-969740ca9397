version: '3.8'
services:
  mongodb:
    restart: always
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USER}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    volumes:
      - mongodb_data:/data/db

  nova-connect:
    image: ghcr.io/${GITHUB_REPOSITORY_OWNER}/nova-connect:latest
    restart: always
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/nova-connect
      - PORT=3001
      - JWT_SECRET=${JWT_SECRET}

  nova-grc-apis:
    image: ghcr.io/${GITHUB_REPOSITORY_OWNER}/nova-grc-apis:latest
    restart: always
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://${MONGO_USER}:${MONGO_PASSWORD}@mongodb:27017/nova-grc-apis
      - PORT=3002
      - JWT_SECRET=${JWT_SECRET}

  nova-gateway:
    image: ghcr.io/${GITHUB_REPOSITORY_OWNER}/nova-gateway:latest
    restart: always
    environment:
      - NODE_ENV=production
      - NOVACONNECT_URL=http://nova-connect:3001
      - PRIVACY_MANAGEMENT_URL=http://nova-grc-apis:3002
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET}

  nova-ui:
    image: ghcr.io/${GITHUB_REPOSITORY_OWNER}/nova-ui:latest
    restart: always
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=https://api.novafuse.io

  nginx:
    image: nginx:latest
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/www:/var/www/html
    depends_on:
      - nova-gateway
      - nova-ui
