/**
 * Permission Model
 * 
 * This model defines the permission schema for the RBAC system.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const permissionSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    unique: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  resource: { 
    type: String, 
    required: true, 
    trim: true 
  },
  action: { 
    type: String, 
    required: true, 
    trim: true 
  },
  isSystem: { 
    type: Boolean, 
    default: false 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, { 
  timestamps: true 
});

// Virtual for full permission string (resource:action)
permissionSchema.virtual('fullName').get(function() {
  return `${this.resource}:${this.action}`;
});

// Add indexes
permissionSchema.index({ name: 1 });
permissionSchema.index({ resource: 1, action: 1 }, { unique: true });
permissionSchema.index({ isSystem: 1 });

// Create model
const Permission = mongoose.model('Permission', permissionSchema);

module.exports = Permission;
