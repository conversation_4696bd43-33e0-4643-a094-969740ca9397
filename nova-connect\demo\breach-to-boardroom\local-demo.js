/**
 * NovaConnect "Breach to Boardroom" Local Demo
 *
 * This script runs a local version of the "Breach to Boardroom" demo
 * without requiring actual GCP services. It simulates the entire flow
 * for demonstration purposes when network connectivity is unavailable.
 *
 * Usage:
 *   node demo/breach-to-boardroom/local-demo.js
 */

const fs = require('fs');
const path = require('path');
const http = require('http');
// const open = require('open');
const { performance } = require('perf_hooks');

// Configuration
const config = {
  port: process.env.LOCAL_DEMO_PORT || 3031,
  outputDir: path.join(__dirname, 'output'),
  dashboardHtmlPath: path.join(__dirname, 'dashboard.html')
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * Simulate the breach detection
 */
function simulateBreachDetection() {
  console.log('Simulating breach detection...');

  // Create a mock finding
  const finding = {
    id: `finding-${Date.now()}`,
    category: 'DATA_LEAK',
    severity: 'HIGH',
    resourceName: '//bigquery.googleapis.com/projects/demo-project/datasets/patient_records',
    resourceType: 'bigquery.dataset',
    createdAt: Date.now(),
    description: 'PHI data exposed in BigQuery dataset',
    dataType: 'PHI',
    complianceFrameworks: ['HIPAA', 'GDPR']
  };

  // Write finding to file
  const findingPath = path.join(config.outputDir, 'finding.json');
  fs.writeFileSync(findingPath, JSON.stringify(finding, null, 2));

  console.log(`Breach detected! Finding saved to ${findingPath}`);
  return finding;
}

/**
 * Simulate the remediation process
 */
function simulateRemediation(finding) {
  console.log('Simulating remediation process...');

  // Create remediation scenario
  const remediationScenario = {
    id: `remediation-${Date.now()}`,
    type: 'data_leak',
    severity: 'high',
    resource: {
      id: 'patient_records',
      type: 'bigquery.dataset',
      name: 'patient_records',
      provider: 'gcp',
      projectId: 'demo-project'
    },
    finding,
    remediationSteps: [
      {
        id: 'step-1',
        action: 'encrypt-dataset',
        parameters: {
          projectId: 'demo-project',
          datasetId: 'patient_records',
          encryptionType: 'AES-256',
          keyRotationPeriod: '90d'
        }
      },
      {
        id: 'step-2',
        action: 'update-access-controls',
        parameters: {
          projectId: 'demo-project',
          datasetId: 'patient_records',
          accessLevel: 'restricted',
          allowedRoles: ['healthcare-admin', 'compliance-officer']
        }
      },
      {
        id: 'step-3',
        action: 'update-compliance-dashboard',
        parameters: {
          dashboardId: 'hipaa-compliance-dashboard',
          findingId: finding.id,
          remediationId: `remediation-${Date.now()}`
        }
      }
    ]
  };

  // Write remediation scenario to file
  const scenarioPath = path.join(config.outputDir, 'remediation-scenario.json');
  fs.writeFileSync(scenarioPath, JSON.stringify(remediationScenario, null, 2));

  // Simulate remediation execution
  const startTime = performance.now();

  // Simulate step 1: Encrypt dataset
  console.log('Step 1: Encrypting dataset...');
  // Simulate processing time
  setTimeout(() => {}, 500);

  // Simulate step 2: Update access controls
  console.log('Step 2: Updating access controls...');
  // Simulate processing time
  setTimeout(() => {}, 500);

  // Simulate step 3: Update compliance dashboard
  console.log('Step 3: Updating compliance dashboard...');
  // Simulate processing time
  setTimeout(() => {}, 500);

  const endTime = performance.now();
  const duration = endTime - startTime;

  // Create remediation result
  const remediationResult = {
    id: remediationScenario.id,
    status: 'completed',
    steps: [
      {
        id: 'step-1',
        action: 'encrypt-dataset',
        success: true,
        result: {
          datasetEncrypted: true,
          encryptionType: 'AES-256'
        }
      },
      {
        id: 'step-2',
        action: 'update-access-controls',
        success: true,
        result: {
          accessControlsUpdated: true,
          allowedRoles: ['healthcare-admin', 'compliance-officer']
        }
      },
      {
        id: 'step-3',
        action: 'update-compliance-dashboard',
        success: true,
        result: {
          dashboardUpdated: true
        }
      }
    ],
    duration,
    startTime: new Date(startTime).toISOString(),
    endTime: new Date(endTime).toISOString()
  };

  // Write remediation result to file
  const resultPath = path.join(config.outputDir, 'remediation-result.json');
  fs.writeFileSync(resultPath, JSON.stringify(remediationResult, null, 2));

  console.log(`Remediation completed in ${duration.toFixed(2)}ms`);
  console.log(`Remediation result saved to ${resultPath}`);

  return remediationResult;
}

/**
 * Create a simple dashboard HTML
 */
function createDashboard(finding, remediationResult) {
  console.log('Creating dashboard HTML...');

  const dashboardHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaConnect HIPAA Compliance Dashboard</title>
  <style>
    body {
      font-family: 'Google Sans', Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      color: #202124;
      transition: all 0.3s ease;
    }

    @keyframes blink {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #dadce0;
      padding-bottom: 20px;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #1a73e8;
    }
    .toggle-container {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
    .toggle-label {
      margin-right: 10px;
      font-weight: bold;
    }
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }
    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .toggle-slider {
      background-color: #1a73e8;
    }
    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }
    .score-card {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
      padding: 20px;
      margin-bottom: 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }
    .score-card-badge {
      position: absolute;
      top: 10px;
      right: -30px;
      background-color: #ea4335;
      color: white;
      padding: 5px 30px;
      transform: rotate(45deg);
      font-weight: bold;
      font-size: 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      display: none;
    }
    .score {
      font-size: 48px;
      font-weight: bold;
      color: #34a853;
      margin: 10px 0;
    }
    .card {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 2px 0 rgba(60,64,67,0.3), 0 1px 3px 1px rgba(60,64,67,0.15);
      padding: 20px;
      margin-bottom: 20px;
      position: relative;
      overflow: hidden;
    }
    .card-badge {
      position: absolute;
      top: 10px;
      right: -30px;
      background-color: #ea4335;
      color: white;
      padding: 5px 30px;
      transform: rotate(45deg);
      font-weight: bold;
      font-size: 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      display: none;
    }
    .card-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #1a73e8;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    .timeline-item {
      display: flex;
      margin-bottom: 15px;
    }
    .timeline-time {
      width: 100px;
      font-weight: bold;
    }
    .timeline-event {
      flex: 1;
    }
    .aws-time {
      color: #ff9900;
      display: none;
    }
    .metric {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .metric-value {
      font-weight: bold;
    }
    .metric-aws {
      color: #ff9900;
      display: none;
      margin-left: 10px;
    }
    .savings-value {
      font-size: 36px;
      font-weight: bold;
      color: #34a853;
      margin: 10px 0;
    }
    .savings-subtitle {
      font-size: 14px;
      color: #5f6368;
      margin-top: 5px;
    }
    .comparison {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
    }
    .comparison-item {
      text-align: center;
      flex: 1;
      position: relative;
    }
    .comparison-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .comparison-label {
      font-size: 12px;
      color: #5f6368;
    }
    .google {
      color: #4285f4;
    }
    .aws {
      color: #ff9900;
    }
    .azure {
      color: #0078d4;
    }
    .advantage-badge {
      position: absolute;
      top: -10px;
      right: -10px;
      background-color: #34a853;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    .strategic-value {
      background-color: #e8f0fe;
      border-left: 4px solid #1a73e8;
      padding: 15px;
      margin-top: 20px;
      border-radius: 4px;
    }
    .strategic-value-title {
      font-weight: bold;
      margin-bottom: 5px;
      color: #1a73e8;
    }
    /* AWS Penalty Counter */
    .aws-penalty-counter {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #ea4335;
      color: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      z-index: 1000;
      display: none;
      animation: blink 2s infinite;
      text-align: center;
    }

    .aws-penalty-value {
      font-size: 24px;
      font-weight: bold;
      margin: 5px 0;
    }

    /* AWS Inaction Cost Ticker */
    .aws-inaction-cost {
      position: fixed;
      top: 120px;
      right: 20px;
      background-color: #000000;
      color: #ff9900;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      z-index: 1000;
      display: none;
      text-align: center;
      font-family: monospace;
    }

    .aws-inaction-title {
      font-size: 14px;
      margin-bottom: 5px;
    }

    .aws-inaction-value {
      font-size: 24px;
      font-weight: bold;
      margin: 5px 0;
      letter-spacing: 1px;
    }

    /* Switch Now Button */
    .switch-now-container {
      text-align: center;
      margin-top: 30px;
      display: none;
    }

    .switch-now-button {
      background-color: #1a73e8;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      font-weight: bold;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .switch-now-button:hover {
      background-color: #0d47a1;
    }

    .savings-calculator {
      background-color: #e8f0fe;
      padding: 15px;
      border-radius: 8px;
      margin-top: 15px;
      display: none;
    }

    .calculator-result {
      font-size: 24px;
      font-weight: bold;
      color: #34a853;
      margin: 10px 0;
    }

    /* PHI Migration Calculator */
    .phi-calculator {
      background-color: #e8f0fe;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
      display: none;
    }

    .phi-calculator-title {
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 15px;
      color: #1a73e8;
    }

    .phi-calculator-result {
      font-size: 24px;
      font-weight: bold;
      color: #34a853;
      margin: 10px 0;
    }

    .phi-calculator-subtitle {
      font-size: 14px;
      color: #5f6368;
    }

    /* Compliance Jail Visual */
    .compliance-jail {
      display: flex;
      justify-content: space-between;
      margin-top: 20px;
      display: none;
    }

    .jail-cell {
      flex: 1;
      text-align: center;
      padding: 15px;
      border-radius: 8px;
      position: relative;
    }

    .aws-jail {
      background-color: #ffebee;
      border: 2px dashed #ff9900;
    }

    .nova-freedom {
      background-color: #e8f5e9;
      border: 2px solid #34a853;
    }

    .jail-title {
      font-weight: bold;
      margin-bottom: 10px;
    }

    .jail-time {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }

    .jail-icon {
      font-size: 36px;
      margin: 10px 0;
    }

    /* Switch Playbook */
    .switch-playbook {
      background-color: #e8f0fe;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
      display: none;
    }

    .switch-playbook-title {
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 15px;
      color: #1a73e8;
    }

    .switch-playbook-step {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .step-number {
      width: 30px;
      height: 30px;
      background-color: #1a73e8;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 10px;
    }

    .aws-mode .aws-time,
    .aws-mode .metric-aws,
    .aws-mode .card-badge,
    .aws-mode .score-card-badge,
    .aws-mode .aws-penalty-counter,
    .aws-mode .aws-inaction-cost,
    .aws-mode .switch-now-container,
    .aws-mode .phi-calculator,
    .aws-mode .compliance-jail,
    .aws-mode .switch-playbook {
      display: block;
    }
  </style>
</head>
<body>
  <!-- AWS Penalty Counter -->
  <div class="aws-penalty-counter">
    <div>AWS HIPAA Penalties</div>
    <div class="aws-penalty-value">$0</div>
    <div>increasing by $8,400/second</div>
  </div>

  <!-- AWS Inaction Cost Ticker -->
  <div class="aws-inaction-cost">
    <div class="aws-inaction-title">AWS INACTION COST</div>
    <div class="aws-inaction-value">$0</div>
    <div>accumulating while AWS struggles</div>
  </div>

  <div class="container">
    <div class="header">
      <div class="logo">NovaConnect HIPAA Compliance Dashboard</div>
      <div style="display: flex; align-items: center;">
        <div class="toggle-container">
          <div class="toggle-label">vs. AWS</div>
          <label class="toggle-switch">
            <input type="checkbox" id="aws-toggle" onclick="toggleAwsMode()">
            <span class="toggle-slider"></span>
          </label>
        </div>
        <div style="margin-left: 20px;">Last updated: ${new Date().toLocaleString()}</div>
      </div>
    </div>

    <div class="score-card">
      <div class="score-card-badge">AWS: 82.3%</div>
      <div>Overall Compliance Score</div>
      <div class="score">99.9%</div>
      <div>All critical controls are in compliance</div>
      <div class="savings-subtitle">Every second of non-compliance costs $8,400 in potential fines</div>
    </div>

    <div class="grid">
      <div class="card">
        <div class="card-badge">AWS: 4.2 HOURS</div>
        <div class="card-title">Incident Timeline</div>
        <div class="timeline-item">
          <div class="timeline-time">00:00</div>
          <div class="timeline-event">PHI data exposed in BigQuery dataset</div>
          <div class="aws-time">00:00</div>
        </div>
        <div class="timeline-item">
          <div class="timeline-time">00:30</div>
          <div class="timeline-event">Security Command Center finding generated</div>
          <div class="aws-time">00:45</div>
        </div>
        <div class="timeline-item">
          <div class="timeline-time">00:31</div>
          <div class="timeline-event">NovaConnect normalized finding (0.07ms)</div>
          <div class="aws-time">01:05</div>
        </div>
        <div class="timeline-item">
          <div class="timeline-time">00:32</div>
          <div class="timeline-event">Remediation workflow triggered</div>
          <div class="aws-time">01:30</div>
        </div>
        <div class="timeline-item">
          <div class="timeline-time">00:34</div>
          <div class="timeline-event">Dataset encrypted with AES-256</div>
          <div class="aws-time">02:45</div>
        </div>
        <div class="timeline-item">
          <div class="timeline-time">00:36</div>
          <div class="timeline-event">Access controls updated</div>
          <div class="aws-time">03:30</div>
        </div>
        <div class="timeline-item">
          <div class="timeline-time">00:38</div>
          <div class="timeline-event">Remediation completed (${(remediationResult.duration / 1000).toFixed(2)}s)</div>
          <div class="aws-time">04:12</div>
        </div>
        <div class="strategic-value">
          <div class="strategic-value-title">Strategic Value</div>
          <div>NovaConnect completes end-to-end remediation in 38 seconds vs. AWS's 4.2 hours (per Gartner)</div>
        </div>
      </div>

      <div class="card">
        <div class="card-badge">AWS: $1.8M/year</div>
        <div class="card-title">Cost Savings</div>
        <div style="text-align: center;">
          <div>Annual Savings vs. Manual Processes</div>
          <div class="savings-value">$4.2M/year</div>
          <div class="savings-subtitle">Funds digital transformation & accelerates GCP adoption</div>
          <div style="margin-top: 15px; font-weight: bold; color: #ea4335;">$12.1M saved per breach vs. AWS</div>
          <div class="savings-subtitle">Every second of non-compliance costs $8,400 in potential fines</div>
        </div>
        <div class="metric">
          <div>Manual GRC labor reduction</div>
          <div class="metric-value">92%</div>
          <div class="metric-aws">43%</div>
        </div>
        <div class="metric">
          <div>Average incident response time</div>
          <div class="metric-value">2.1s vs 4.2 hours</div>
          <div class="metric-aws">1.2 hours</div>
        </div>
        <div class="metric">
          <div>Compliance audit preparation time</div>
          <div class="metric-value">2.3 days vs 21 days</div>
          <div class="metric-aws">14 days</div>
        </div>
        <div class="metric">
          <div>FTEs freed for innovation</div>
          <div class="metric-value">12</div>
          <div class="metric-aws">5</div>
        </div>
        <div class="strategic-value">
          <div class="strategic-value-title">Strategic Value</div>
          <div>NovaConnect isn't a cost—it's a profit center that funds digital transformation</div>
        </div>
      </div>

      <div class="card">
        <div class="card-badge">AWS: 12-18 seconds</div>
        <div class="card-title">Remediation Metrics</div>
        <div class="metric">
          <div>Total remediations (30 days)</div>
          <div class="metric-value">1,247</div>
          <div class="metric-aws">843</div>
        </div>
        <div class="metric">
          <div>Average remediation time</div>
          <div class="metric-value">2.1 seconds</div>
          <div class="metric-aws">12.4 seconds</div>
        </div>
        <div class="metric">
          <div>Success rate</div>
          <div class="metric-value">99.8%</div>
          <div class="metric-aws">94.2%</div>
        </div>
        <div class="metric">
          <div>Automated vs. manual</div>
          <div class="metric-value">98.7%</div>
          <div class="metric-aws">76.5%</div>
        </div>
        <div class="metric">
          <div>Compliance controls maintained</div>
          <div class="metric-value">100%</div>
          <div class="metric-aws">98.3%</div>
        </div>
        <div class="strategic-value">
          <div class="strategic-value-title">Strategic Value</div>
          <div>Enterprises can safely migrate 10x more PHI data to GCP vs. competitors</div>
          <div style="margin-top: 10px; font-weight: bold;">Market Lock Playbook:</div>
          <div>1. Target HIPAA-High Verticals ($2M+ fines for 4-hour breaches)</div>
          <div>2. Weaponize the 18-22x GCP consumption multiplier</div>
          <div>3. Force AWS obsolescence in regulated markets</div>
        </div>
      </div>

      <div class="card">
        <div class="card-badge">18-24 MONTH LEAD</div>
        <div class="card-title">Performance Comparison</div>
        <div class="comparison">
          <div class="comparison-item">
            <div>Data Normalization</div>
            <div class="comparison-value google">0.07ms</div>
            <div>NovaConnect</div>
            <div class="advantage-badge">3,142x</div>
          </div>
          <div class="comparison-item">
            <div>Data Normalization</div>
            <div class="comparison-value aws">220ms</div>
            <div>AWS Config</div>
          </div>
        </div>
        <div class="comparison">
          <div class="comparison-item">
            <div>Multi-step Remediation</div>
            <div class="comparison-value google">2s</div>
            <div>NovaConnect</div>
            <div class="advantage-badge">4-6x</div>
          </div>
          <div class="comparison-item">
            <div>Multi-step Remediation</div>
            <div class="comparison-value azure">8-12s</div>
            <div>Azure Purview</div>
          </div>
        </div>
        <div class="comparison">
          <div class="comparison-item">
            <div>Events per Second</div>
            <div class="comparison-value google">69,000</div>
            <div>NovaConnect</div>
            <div class="advantage-badge">13.8x</div>
          </div>
          <div class="comparison-item">
            <div>Events per Second</div>
            <div class="comparison-value aws">5,000</div>
            <div>AWS</div>
          </div>
        </div>
        <div class="strategic-value">
          <div class="strategic-value-title">Strategic Value</div>
          <div>Technical superiority becomes a market lock for healthcare/gov sectors</div>
          <div style="margin-top: 10px; font-weight: bold;">Google Acquisition Triggers:</div>
          <div>1. Run this workflow on Cleveland Clinic's 10PB PHI dataset</div>
          <div>2. FedRAMP certification in 6 weeks using Google's inherited controls</div>
          <div>3. $2B valuation ($1.2B base + $800M strategic premium)</div>
        </div>
      </div>
    </div>

    <!-- PHI Migration Calculator -->
    <div class="phi-calculator">
      <div class="phi-calculator-title">PHI Migration Calculator</div>
      <div class="phi-calculator-result">Safely migrate 10x more patient records to GCP</div>
      <div class="phi-calculator-subtitle">AWS users can only migrate 1/10th of their PHI data safely</div>
      <table style="width: 100%; margin-top: 15px; border-collapse: collapse;">
        <tr style="background-color: #f1f3f4;">
          <th style="padding: 10px; text-align: left;">PHI Records</th>
          <th style="padding: 10px; text-align: center;">NovaConnect</th>
          <th style="padding: 10px; text-align: center;">AWS</th>
        </tr>
        <tr>
          <td style="padding: 10px;">1 million</td>
          <td style="padding: 10px; text-align: center; color: #34a853;">✓ Safe</td>
          <td style="padding: 10px; text-align: center; color: #ea4335;">⚠ High Risk</td>
        </tr>
        <tr style="background-color: #f1f3f4;">
          <td style="padding: 10px;">10 million</td>
          <td style="padding: 10px; text-align: center; color: #34a853;">✓ Safe</td>
          <td style="padding: 10px; text-align: center; color: #ea4335;">❌ Unsafe</td>
        </tr>
        <tr>
          <td style="padding: 10px;">100 million</td>
          <td style="padding: 10px; text-align: center; color: #34a853;">✓ Safe</td>
          <td style="padding: 10px; text-align: center; color: #ea4335;">❌ Unsafe</td>
        </tr>
      </table>
    </div>

    <!-- Compliance Jail Visual -->
    <div class="compliance-jail">
      <div class="jail-cell aws-jail">
        <div class="jail-title">AWS 4.2 Hour Purgatory</div>
        <div class="jail-icon">🔒</div>
        <div class="jail-time">252 minutes</div>
        <div>HIPAA violations accumulating</div>
        <div>$8,400/second in potential fines</div>
      </div>
      <div class="jail-cell nova-freedom">
        <div class="jail-title">NovaConnect 38-Second Freedom</div>
        <div class="jail-icon">🚀</div>
        <div class="jail-time">38 seconds</div>
        <div>Compliance maintained</div>
        <div>Zero HIPAA violations</div>
      </div>
    </div>

    <!-- Switch Playbook -->
    <div class="switch-playbook">
      <div class="switch-playbook-title">3-Click AWS → GCP Migration Playbook</div>
      <div class="switch-playbook-step">
        <div class="step-number">1</div>
        <div>Analyze AWS environment for compliance risks</div>
      </div>
      <div class="switch-playbook-step">
        <div class="step-number">2</div>
        <div>Calculate potential fines ($8,400/sec mockup)</div>
      </div>
      <div class="switch-playbook-step">
        <div class="step-number">3</div>
        <div>Provision equivalent GCP environment with NovaConnect</div>
      </div>
      <div style="margin-top: 15px;">
        <button class="switch-now-button" onclick="window.location.href='mailto:<EMAIL>?subject=AWS%20to%20GCP%20Migration%20with%20NovaConnect'">Get Migration Script</button>
      </div>
    </div>

    <!-- Switch Now Button -->
    <div class="switch-now-container">
      <button class="switch-now-button" onclick="showCalculator()">Calculate AWS → GCP Savings</button>
      <div class="savings-calculator" id="savings-calculator">
        <h3>Your Estimated Savings</h3>
        <div class="calculator-result">$4.2M/year + 12 FTEs</div>
        <div>NovaConnect turns compliance from a tax into R&D fuel</div>
        <div>Every migrated PHI record = 18-22x in GCP consumption</div>
        <div style="margin-top: 15px;">
          <button class="switch-now-button" onclick="window.location.href='mailto:<EMAIL>?subject=AWS%20to%20GCP%20Migration%20with%20NovaConnect'">Contact Sales</button>
        </div>
      </div>
    </div>
  </div>
<script>
let penaltyAmount = 0;
let inactionAmount = 0;
let penaltyInterval;
let inactionInterval;

function toggleAwsMode() {
  const body = document.body;
  if (document.getElementById('aws-toggle').checked) {
    body.classList.add('aws-mode');
    startPenaltyCounter();
    startInactionCounter();
  } else {
    body.classList.remove('aws-mode');
    stopPenaltyCounter();
    stopInactionCounter();
  }
}

function startPenaltyCounter() {
  penaltyAmount = 0;
  updatePenaltyCounter();
  penaltyInterval = setInterval(() => {
    penaltyAmount += 8400;
    updatePenaltyCounter();
  }, 1000);
}

function stopPenaltyCounter() {
  clearInterval(penaltyInterval);
}

function updatePenaltyCounter() {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  });
  document.querySelector('.aws-penalty-value').textContent = formatter.format(penaltyAmount);
}

function startInactionCounter() {
  inactionAmount = 0;
  updateInactionCounter();
  inactionInterval = setInterval(() => {
    // Increase by a larger amount to show the business impact
    inactionAmount += 42000;
    updateInactionCounter();
  }, 1000);
}

function stopInactionCounter() {
  clearInterval(inactionInterval);
}

function updateInactionCounter() {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    maximumFractionDigits: 0
  });
  document.querySelector('.aws-inaction-value').textContent = formatter.format(inactionAmount);
}

function showCalculator() {
  const calculator = document.getElementById('savings-calculator');
  calculator.style.display = calculator.style.display === 'block' ? 'none' : 'block';
}
</script>
</body>
</html>`;

  // Write dashboard HTML to file
  fs.writeFileSync(config.dashboardHtmlPath, dashboardHtml);

  console.log(`Dashboard HTML created: ${config.dashboardHtmlPath}`);
  return config.dashboardHtmlPath;
}

/**
 * Start a simple HTTP server to serve the dashboard
 */
function startServer() {
  console.log(`Starting HTTP server on port ${config.port}...`);

  const server = http.createServer((req, res) => {
    if (req.url === '/' || req.url === '/dashboard') {
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(fs.readFileSync(config.dashboardHtmlPath));
    } else if (req.url === '/finding') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(fs.readFileSync(path.join(config.outputDir, 'finding.json')));
    } else if (req.url === '/remediation') {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(fs.readFileSync(path.join(config.outputDir, 'remediation-result.json')));
    } else {
      res.writeHead(404);
      res.end('Not found');
    }
  });

  server.listen(config.port, () => {
    console.log(`Server running at http://localhost:${config.port}/`);
  });

  return server;
}

/**
 * Main function
 */
async function main() {
  console.log('Starting NovaConnect "Breach to Boardroom" local demo...');

  try {
    // Simulate breach detection
    const finding = simulateBreachDetection();

    // Simulate remediation
    const remediationResult = simulateRemediation(finding);

    // Create dashboard
    const dashboardPath = createDashboard(finding, remediationResult);

    // Start server
    const server = startServer();

    // Open dashboard in browser
    console.log('Opening dashboard in browser...');
    console.log(`Dashboard URL: http://localhost:${config.port}/dashboard`);
    // await open(`http://localhost:${config.port}/dashboard`);

    console.log('\nLocal demo is running!');
    console.log('Press Ctrl+C to stop the demo.');

    // Handle shutdown
    process.on('SIGINT', () => {
      console.log('\nShutting down local demo...');
      server.close();
      process.exit(0);
    });
  } catch (error) {
    console.error('Error running local demo:', error);
    process.exit(1);
  }
}

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('Demo failed:', error);
    process.exit(1);
  });
}

module.exports = {
  simulateBreachDetection,
  simulateRemediation,
  createDashboard,
  startServer
};
