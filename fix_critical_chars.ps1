# Script to fix critical character rendering issues in the dictionary

# Define paths
$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the content with UTF-8 encoding
$content = [System.IO.File]::ReadAllText($dictionaryPath, [System.Text.Encoding]::UTF8)

# Define the most critical replacements
$replacements = @{
    # Fix common encoding issues
    'Ã°Å¸â€' = '💡'  # Lightbulb emoji
    'Ã°Å¸Â§Â¬' = '📋'  # Clipboard emoji
    'Ã°Å¸Â§Â' = '⚠️'  # Warning emoji
    'ÃŽÂº' = 'κ'    # Greek kappa
    'Ã—' = '×'      # Multiplication sign
    'Ã¢Å“Â¨' = '✨'  # Sparkles emoji
    'Ã¢â€œ' = '"'   # Left double quote
    'Ã¢â€' = '"' # Right double quote
    'Ã¢â€˜' = "'"   # Single quote
    'Ã¢â„¢Â' = '™'  # Trademark symbol
    'Ã‚Â' = ''     # Non-breaking space
    'Ã©' = 'é'     # e with acute
    'Ã¨' = 'è'     # e with grave
    'Ã´' = 'ô'     # o with circumflex
    'Ã¢' = 'â'     # a with circumflex
    'Ã§' = 'ç'     # c with cedilla
    'Ãª' = 'ê'     # e with circumflex
    'Ã«' = 'ë'     # e with diaeresis
    'Ã¯' = 'ï'     # i with diaeresis
    'Ã®' = 'î'     # i with circumflex
    'Ã¹' = 'ù'     # u with grave
    'Ã»' = 'û'     # u with circumflex
    'Ã¼' = 'ü'     # u with diaeresis
    'Ã¿' = 'ÿ'     # y with diaeresis
}

# Apply all replacements
foreach ($key in $replacements.Keys) {
    $content = $content.Replace($key, $replacements[$key])
}

# Write the modified content back to the file with UTF-8 encoding
[System.IO.File]::WriteAllText($dictionaryPath, $content.Trim(), [System.Text.Encoding]::UTF8)

Write-Host "Fixed critical character rendering issues in the dictionary file."
Write-Host "Please review the changes in the file: $dictionaryPath"
