# Script to analyze the dictionary file and add letter headers
$filePath = "d:\novafuse-api-superstore\coherence-reality-systems\imported-docs\the-magnificent-seven\documentation\Comphyological_Dictionary_Standardized_Format_1.md"

# Read the file content
$content = Get-Content -Path $filePath -Raw

# Find all terms (they start with ** and end with **)
$terms = [regex]::Matches($content, '(?<=\*\*)([^\*]+?)(?=\*\*)') | 
    ForEach-Object { $_.Value.Trim() } | 
    Where-Object { $_ -notmatch '^Framework:|^Standard:|^Date:' } |
    Select-Object -Unique

# Group terms by their starting letter
$termsByLetter = @{}
foreach ($term in $terms) {
    $firstLetter = $term[0].ToString().ToUpper()
    if (-not $termsByLetter.ContainsKey($firstLetter)) {
        $termsByLetter[$firstLetter] = @()
    }
    $termsByLetter[$firstLetter] += $term
}

# Output the analysis
Write-Host "Found $($terms.Count) unique terms starting with the following letters:"
$termsByLetter.Keys | Sort-Object | ForEach-Object {
    Write-Host "$_ ($($termsByLetter[$_].Count) terms)"
}

# Now let's modify the file to add letter headers
$lines = Get-Content -Path $filePath
$outputLines = @()
$currentLetter = $null
$inTerm = $false

foreach ($line in $lines) {
    if ($line -match '^\*\*([A-Za-z])') {
        $termFirstLetter = $matches[1].ToUpper()
        if ($currentLetter -ne $termFirstLetter) {
            $currentLetter = $termFirstLetter
            $outputLines += ""
            $outputLines += "## $currentLetter"
            $outputLines += ""
        }
        $inTerm = $true
    } elseif ($line -match '^---$' -and $inTerm) {
        $inTerm = $false
    }
    $outputLines += $line
}

# Write the modified content back to a new file
$outputPath = $filePath -replace '\.md$', '_with_headers.md'
$outputLines | Out-File -FilePath $outputPath -Encoding utf8

Write-Host "Modified dictionary with letter headers has been saved to: $outputPath"
