/**
 * NovaFuse Universal API Connector - Connector Error
 * 
 * This module defines connector-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for connector failures
 * @class ConnectorError
 * @extends UAConnectorError
 */
class ConnectorError extends UAConnectorError {
  /**
   * Create a new ConnectorError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {string} options.connectorId - ID of the connector
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'CONNECTOR_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    
    this.connectorId = options.connectorId;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred with the connector. Please try again later or contact support if the issue persists.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    
    if (this.connectorId) {
      json.connectorId = this.connectorId;
    }
    
    return json;
  }
}

/**
 * Error class for connector not found errors
 * @class ConnectorNotFoundError
 * @extends ConnectorError
 */
class ConnectorNotFoundError extends ConnectorError {
  /**
   * Create a new ConnectorNotFoundError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {Object} options - Error options
   */
  constructor(connectorId, options = {}) {
    const message = `Connector not found: ${connectorId}`;
    
    super(message, {
      code: options.code || 'CONNECTOR_NOT_FOUND',
      severity: options.severity || 'error',
      context: { ...options.context, connectorId },
      cause: options.cause,
      connectorId
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `The connector "${this.connectorId}" was not found. Please check the connector ID and try again.`;
  }
}

/**
 * Error class for connector configuration errors
 * @class ConnectorConfigurationError
 * @extends ConnectorError
 */
class ConnectorConfigurationError extends ConnectorError {
  /**
   * Create a new ConnectorConfigurationError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(connectorId, message = 'Invalid connector configuration', options = {}) {
    super(message, {
      code: options.code || 'CONNECTOR_CONFIGURATION_ERROR',
      severity: options.severity || 'error',
      context: { ...options.context, connectorId },
      cause: options.cause,
      connectorId
    });
    
    this.configErrors = options.configErrors || [];
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    if (this.configErrors.length > 0) {
      const errorMessages = this.configErrors.map(err => err.message).join('; ');
      return `The connector configuration is invalid: ${errorMessages}`;
    }
    return `The connector configuration is invalid. Please check your configuration and try again.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.configErrors = this.configErrors;
    return json;
  }
}

/**
 * Error class for connector execution errors
 * @class ConnectorExecutionError
 * @extends ConnectorError
 */
class ConnectorExecutionError extends ConnectorError {
  /**
   * Create a new ConnectorExecutionError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {string} operationId - ID of the operation
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(connectorId, operationId, message = 'Error executing connector operation', options = {}) {
    super(message, {
      code: options.code || 'CONNECTOR_EXECUTION_ERROR',
      severity: options.severity || 'error',
      context: { ...options.context, connectorId, operationId },
      cause: options.cause,
      connectorId
    });
    
    this.operationId = operationId;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `An error occurred while executing the connector operation. Please try again later or contact support if the issue persists.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.operationId = this.operationId;
    return json;
  }
}

/**
 * Error class for connector version incompatibility errors
 * @class ConnectorVersionError
 * @extends ConnectorError
 */
class ConnectorVersionError extends ConnectorError {
  /**
   * Create a new ConnectorVersionError
   * 
   * @param {string} connectorId - ID of the connector
   * @param {string} currentVersion - Current version
   * @param {string} requiredVersion - Required version
   * @param {Object} options - Error options
   */
  constructor(connectorId, currentVersion, requiredVersion, options = {}) {
    const message = `Connector version incompatible: ${connectorId} (current: ${currentVersion}, required: ${requiredVersion})`;
    
    super(message, {
      code: options.code || 'CONNECTOR_VERSION_ERROR',
      severity: options.severity || 'error',
      context: { ...options.context, connectorId, currentVersion, requiredVersion },
      cause: options.cause,
      connectorId
    });
    
    this.currentVersion = currentVersion;
    this.requiredVersion = requiredVersion;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `The connector version is incompatible. Please update the connector to version ${this.requiredVersion} or later.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.currentVersion = this.currentVersion;
    json.requiredVersion = this.requiredVersion;
    return json;
  }
}

module.exports = {
  ConnectorError,
  ConnectorNotFoundError,
  ConnectorConfigurationError,
  ConnectorExecutionError,
  ConnectorVersionError
};
