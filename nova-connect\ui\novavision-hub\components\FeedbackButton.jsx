/**
 * FeedbackButton Component
 * 
 * A button that opens the feedback dialog.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import FeedbackDialog from './FeedbackDialog';

/**
 * FeedbackButton component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.component='novavision-hub'] - The component to provide feedback on
 * @param {string} [props.type='general'] - The type of feedback
 * @param {Object} [props.metadata={}] - Additional metadata to include with the feedback
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @param {string} [props.variant='floating'] - Button variant (floating, inline, icon)
 * @param {string} [props.position='bottom-right'] - Position of the floating button
 * @param {Function} [props.onSubmit] - Function to call when feedback is submitted
 * @returns {React.ReactElement} FeedbackButton component
 */
const FeedbackButton = ({
  component = 'novavision-hub',
  type = 'general',
  metadata = {},
  className = '',
  style = {},
  variant = 'floating',
  position = 'bottom-right',
  onSubmit
}) => {
  // Hooks
  const { translate } = useI18n();
  
  // State
  const [dialogOpen, setDialogOpen] = useState(false);
  
  // Open dialog
  const handleOpenDialog = () => {
    setDialogOpen(true);
  };
  
  // Close dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };
  
  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };
  
  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'inline':
        return (
          <button
            className={`px-4 py-2 bg-primary text-white rounded-md hover:bg-opacity-90 ${className}`}
            onClick={handleOpenDialog}
            style={style}
            aria-label={translate('feedback.provideFeedback', 'Provide Feedback')}
          >
            {translate('feedback.provideFeedback', 'Provide Feedback')}
          </button>
        );
      
      case 'icon':
        return (
          <button
            className={`p-2 text-textPrimary hover:text-primary ${className}`}
            onClick={handleOpenDialog}
            style={style}
            aria-label={translate('feedback.provideFeedback', 'Provide Feedback')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </button>
        );
      
      case 'floating':
      default:
        return (
          <button
            className={`fixed ${getPositionClasses()} z-40 p-3 bg-primary text-white rounded-full shadow-lg hover:bg-opacity-90 ${className}`}
            onClick={handleOpenDialog}
            style={style}
            aria-label={translate('feedback.provideFeedback', 'Provide Feedback')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </button>
        );
    }
  };
  
  return (
    <>
      {renderButton()}
      
      <FeedbackDialog
        open={dialogOpen}
        onClose={handleCloseDialog}
        component={component}
        type={type}
        metadata={metadata}
        onSubmit={onSubmit}
      />
    </>
  );
};

FeedbackButton.propTypes = {
  component: PropTypes.string,
  type: PropTypes.oneOf(['general', 'feature', 'bug', 'suggestion', 'usability', 'performance', 'other']),
  metadata: PropTypes.object,
  className: PropTypes.string,
  style: PropTypes.object,
  variant: PropTypes.oneOf(['floating', 'inline', 'icon']),
  position: PropTypes.oneOf(['top-left', 'top-right', 'bottom-left', 'bottom-right']),
  onSubmit: PropTypes.func
};

export default FeedbackButton;
