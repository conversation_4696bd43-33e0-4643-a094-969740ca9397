[{"name": "organizations/123/sources/456/findings/finding-0", "parent": "organizations/123/sources/456", "resourceName": "//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-0", "state": "ACTIVE", "category": "VULNERABILITY", "severity": "HIGH", "eventTime": "2023-07-10T12:00:00.000Z", "createTime": "2023-07-10T12:00:00.000Z", "sourceProperties": {"finding_type": "Vulnerability", "finding_id": "finding-0", "finding_description": "<PERSON><PERSON> finding 0 description"}}, {"name": "organizations/123/sources/456/findings/finding-1", "parent": "organizations/123/sources/456", "resourceName": "//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-1", "state": "ACTIVE", "category": "MISCONFIGURATION", "severity": "MEDIUM", "eventTime": "2023-07-10T12:01:00.000Z", "createTime": "2023-07-10T12:01:00.000Z", "sourceProperties": {"finding_type": "Misconfiguration", "finding_id": "finding-1", "finding_description": "<PERSON><PERSON> finding 1 description"}}, {"name": "organizations/123/sources/456/findings/finding-2", "parent": "organizations/123/sources/456", "resourceName": "//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-2", "state": "ACTIVE", "category": "THREAT", "severity": "LOW", "eventTime": "2023-07-10T12:02:00.000Z", "createTime": "2023-07-10T12:02:00.000Z", "sourceProperties": {"finding_type": "Threat", "finding_id": "finding-2", "finding_description": "<PERSON><PERSON> finding 2 description"}}, {"name": "organizations/123/sources/456/findings/finding-3", "parent": "organizations/123/sources/456", "resourceName": "//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-3", "state": "ACTIVE", "category": "VULNERABILITY", "severity": "HIGH", "eventTime": "2023-07-10T12:03:00.000Z", "createTime": "2023-07-10T12:03:00.000Z", "sourceProperties": {"finding_type": "Vulnerability", "finding_id": "finding-3", "finding_description": "<PERSON><PERSON> finding 3 description"}}, {"name": "organizations/123/sources/456/findings/finding-4", "parent": "organizations/123/sources/456", "resourceName": "//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-4", "state": "ACTIVE", "category": "MISCONFIGURATION", "severity": "MEDIUM", "eventTime": "2023-07-10T12:04:00.000Z", "createTime": "2023-07-10T12:04:00.000Z", "sourceProperties": {"finding_type": "Misconfiguration", "finding_id": "finding-4", "finding_description": "<PERSON><PERSON> finding 4 description"}}]