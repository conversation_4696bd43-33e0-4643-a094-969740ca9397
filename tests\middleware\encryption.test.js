/**
 * Tests for the encryption middleware
 */

const encryptionMiddleware = require('../../middleware/encryption');
const encryption = require('../../utils/encryption');
const config = require('../../config');

// Mock Express request and response
const mockRequest = (body = {}) => ({
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

const mockNext = jest.fn();

describe('Encryption Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('encryptResponseFields', () => {
    test('should encrypt specified fields in the response', () => {
      const middleware = encryptionMiddleware.encryptResponseFields(['user.password', 'payment.cardNumber']);
      const req = mockRequest();
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(typeof res.json).toBe('function');
      
      const responseData = {
        user: {
          id: '123',
          name: 'Test User',
          password: 'secret-password'
        },
        payment: {
          id: '456',
          cardNumber: '1234567890123456',
          expiryDate: '12/25'
        }
      };
      
      res.json(responseData);
      
      expect(res.json).toHaveBeenCalled();
      const encryptedData = res.json.mock.calls[0][0];
      
      // Check that non-sensitive fields are not encrypted
      expect(encryptedData.user.id).toBe('123');
      expect(encryptedData.user.name).toBe('Test User');
      expect(encryptedData.payment.id).toBe('456');
      expect(encryptedData.payment.expiryDate).toBe('12/25');
      
      // Check that sensitive fields are encrypted
      expect(encryptedData.user.password).not.toBe('secret-password');
      expect(encryptedData.payment.cardNumber).not.toBe('1234567890123456');
      
      // Check that encrypted fields have the correct format (base64:base64:base64)
      expect(encryptedData.user.password.split(':').length).toBe(3);
      expect(encryptedData.payment.cardNumber.split(':').length).toBe(3);
      
      // Decrypt the fields to verify they contain the original data
      const [encryptedPassword, ivPassword, authTagPassword] = encryptedData.user.password.split(':');
      const decryptedPassword = encryption.decryptSymmetric(
        encryptedPassword,
        ivPassword,
        authTagPassword,
        config.encryption.responseKey
      );
      expect(decryptedPassword).toBe('secret-password');
      
      const [encryptedCardNumber, ivCardNumber, authTagCardNumber] = encryptedData.payment.cardNumber.split(':');
      const decryptedCardNumber = encryption.decryptSymmetric(
        encryptedCardNumber,
        ivCardNumber,
        authTagCardNumber,
        config.encryption.responseKey
      );
      expect(decryptedCardNumber).toBe('1234567890123456');
    });
    
    test('should handle missing fields gracefully', () => {
      const middleware = encryptionMiddleware.encryptResponseFields(['user.password', 'payment.cardNumber']);
      const req = mockRequest();
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      const responseData = {
        user: {
          id: '123',
          name: 'Test User'
          // No password field
        }
        // No payment field
      };
      
      res.json(responseData);
      
      expect(res.json).toHaveBeenCalled();
      const encryptedData = res.json.mock.calls[0][0];
      
      // Check that the structure is preserved
      expect(encryptedData.user.id).toBe('123');
      expect(encryptedData.user.name).toBe('Test User');
      expect(encryptedData.user.password).toBeUndefined();
      expect(encryptedData.payment).toBeUndefined();
    });
    
    test('should handle null values gracefully', () => {
      const middleware = encryptionMiddleware.encryptResponseFields(['user.password']);
      const req = mockRequest();
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      const responseData = {
        user: {
          id: '123',
          name: 'Test User',
          password: null
        }
      };
      
      res.json(responseData);
      
      expect(res.json).toHaveBeenCalled();
      const encryptedData = res.json.mock.calls[0][0];
      
      // Check that null values are preserved
      expect(encryptedData.user.password).toBeNull();
    });
  });
  
  describe('decryptRequestFields', () => {
    test('should decrypt specified fields in the request', () => {
      // Encrypt some data first
      const password = 'secret-password';
      const cardNumber = '1234567890123456';
      
      const encryptedPassword = encryption.encryptSymmetric(password, config.encryption.requestKey);
      const encryptedCardNumber = encryption.encryptSymmetric(cardNumber, config.encryption.requestKey);
      
      const encryptedPasswordString = `${encryptedPassword.encrypted}:${encryptedPassword.iv}:${encryptedPassword.authTag}`;
      const encryptedCardNumberString = `${encryptedCardNumber.encrypted}:${encryptedCardNumber.iv}:${encryptedCardNumber.authTag}`;
      
      // Create the middleware and request
      const middleware = encryptionMiddleware.decryptRequestFields(['user.password', 'payment.cardNumber']);
      const req = mockRequest({
        user: {
          id: '123',
          name: 'Test User',
          password: encryptedPasswordString
        },
        payment: {
          id: '456',
          cardNumber: encryptedCardNumberString,
          expiryDate: '12/25'
        }
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that non-sensitive fields are not modified
      expect(req.body.user.id).toBe('123');
      expect(req.body.user.name).toBe('Test User');
      expect(req.body.payment.id).toBe('456');
      expect(req.body.payment.expiryDate).toBe('12/25');
      
      // Check that sensitive fields are decrypted
      expect(req.body.user.password).toBe(password);
      expect(req.body.payment.cardNumber).toBe(cardNumber);
    });
    
    test('should handle missing fields gracefully', () => {
      const middleware = encryptionMiddleware.decryptRequestFields(['user.password', 'payment.cardNumber']);
      const req = mockRequest({
        user: {
          id: '123',
          name: 'Test User'
          // No password field
        }
        // No payment field
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that the structure is preserved
      expect(req.body.user.id).toBe('123');
      expect(req.body.user.name).toBe('Test User');
      expect(req.body.user.password).toBeUndefined();
      expect(req.body.payment).toBeUndefined();
    });
    
    test('should handle non-encrypted values gracefully', () => {
      const middleware = encryptionMiddleware.decryptRequestFields(['user.password']);
      const req = mockRequest({
        user: {
          id: '123',
          name: 'Test User',
          password: 'plain-text-password' // Not encrypted
        }
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that non-encrypted values are preserved
      expect(req.body.user.password).toBe('plain-text-password');
    });
  });
  
  describe('encryptResponse', () => {
    test('should encrypt the entire response', () => {
      const middleware = encryptionMiddleware.encryptResponse();
      const req = mockRequest();
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(typeof res.json).toBe('function');
      
      const responseData = {
        user: {
          id: '123',
          name: 'Test User',
          password: 'secret-password'
        },
        payment: {
          id: '456',
          cardNumber: '1234567890123456',
          expiryDate: '12/25'
        }
      };
      
      res.json(responseData);
      
      expect(res.json).toHaveBeenCalled();
      const encryptedResponse = res.json.mock.calls[0][0];
      
      // Check that the response has the correct structure
      expect(encryptedResponse).toHaveProperty('encrypted');
      expect(encryptedResponse).toHaveProperty('iv');
      expect(encryptedResponse).toHaveProperty('authTag');
      expect(encryptedResponse).toHaveProperty('isEncrypted', true);
      
      // Decrypt the response to verify it contains the original data
      const decrypted = encryption.decryptSymmetric(
        encryptedResponse.encrypted,
        encryptedResponse.iv,
        encryptedResponse.authTag,
        config.encryption.responseKey
      );
      
      const decryptedData = JSON.parse(decrypted);
      
      // Check that the decrypted data matches the original
      expect(decryptedData).toEqual(responseData);
    });
    
    test('should handle null response gracefully', () => {
      const middleware = encryptionMiddleware.encryptResponse();
      const req = mockRequest();
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      res.json(null);
      
      expect(res.json).toHaveBeenCalled();
      const response = res.json.mock.calls[0][0];
      
      // Check that null response is preserved
      expect(response).toBeNull();
    });
  });
  
  describe('decryptRequest', () => {
    test('should decrypt the entire request body', () => {
      // Encrypt some data first
      const requestData = {
        user: {
          id: '123',
          name: 'Test User',
          password: 'secret-password'
        },
        payment: {
          id: '456',
          cardNumber: '1234567890123456',
          expiryDate: '12/25'
        }
      };
      
      const encrypted = encryption.encryptSymmetric(
        JSON.stringify(requestData),
        config.encryption.requestKey
      );
      
      // Create the middleware and request
      const middleware = encryptionMiddleware.decryptRequest();
      const req = mockRequest({
        encrypted: encrypted.encrypted,
        iv: encrypted.iv,
        authTag: encrypted.authTag,
        isEncrypted: true
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that the request body is decrypted
      expect(req.body).toEqual(requestData);
    });
    
    test('should handle non-encrypted request gracefully', () => {
      const middleware = encryptionMiddleware.decryptRequest();
      const requestData = {
        user: {
          id: '123',
          name: 'Test User'
        }
      };
      const req = mockRequest(requestData);
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that the request body is preserved
      expect(req.body).toEqual(requestData);
    });
    
    test('should return 400 for invalid encrypted request', () => {
      const middleware = encryptionMiddleware.decryptRequest();
      const req = mockRequest({
        encrypted: 'invalid-encrypted-data',
        iv: 'invalid-iv',
        authTag: 'invalid-auth-tag',
        isEncrypted: true
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Bad Request',
        message: 'Failed to decrypt request body'
      });
    });
  });
  
  describe('maskSensitiveData', () => {
    test('should mask sensitive fields for logging', () => {
      const middleware = encryptionMiddleware.maskSensitiveData(['user.password', 'payment.cardNumber']);
      const req = mockRequest({
        user: {
          id: '123',
          name: 'Test User',
          password: 'secret-password'
        },
        payment: {
          id: '456',
          cardNumber: '1234567890123456',
          expiryDate: '12/25'
        }
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that the original request body is not modified
      expect(req.body.user.password).toBe('secret-password');
      expect(req.body.payment.cardNumber).toBe('1234567890123456');
      
      // Check that the masked body has sensitive fields masked
      expect(req.maskedBody.user.password).toBe('secr********word');
      expect(req.maskedBody.payment.cardNumber).toBe('1234********3456');
      
      // Check that non-sensitive fields are not masked
      expect(req.maskedBody.user.id).toBe('123');
      expect(req.maskedBody.user.name).toBe('Test User');
      expect(req.maskedBody.payment.id).toBe('456');
      expect(req.maskedBody.payment.expiryDate).toBe('12/25');
    });
    
    test('should handle missing fields gracefully', () => {
      const middleware = encryptionMiddleware.maskSensitiveData(['user.password', 'payment.cardNumber']);
      const req = mockRequest({
        user: {
          id: '123',
          name: 'Test User'
          // No password field
        }
        // No payment field
      });
      const res = mockResponse();
      
      middleware(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      
      // Check that the structure is preserved
      expect(req.maskedBody.user.id).toBe('123');
      expect(req.maskedBody.user.name).toBe('Test User');
      expect(req.maskedBody.user.password).toBeUndefined();
      expect(req.maskedBody.payment).toBeUndefined();
    });
  });
});
