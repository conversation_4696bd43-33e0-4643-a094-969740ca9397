@echo off
setlocal enabledelayedexpansion

set "file=d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
set "backup=%file%.backup.%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"

:: Create backup
copy "%file%" "%backup%" >nul
echo Created backup at: %backup%

:: Read the file and apply replacements
(
    for /f "tokens=* delims=" %%a in ('type "%file%" ^& break ^>"%file%"') do (
        set "line=%%a"
        
        :: Apply replacements
        set "line=!line:â€˜='!"
        set "line=!line:â€"="!"
        set "line=!line:â„¢=™!"
        set "line=!line:Â= !"
        set "line=!line:Ãƒâ€"=–!"
        set "line=!line:Ã¢â€œ="!"
        set "line=!line:Ã¢â€="!"
        set "line=!line:Ã¢â€˜='!"
        set "line=!line:Ã¢â„¢Â=™!"
        set "line=!line:ÃŽÂ¨=Ψ!"
        set "line=!line:Ãƒâ€”=×!"
        
        :: Clean up any remaining problematic patterns
        set "line=!line:Ã?=?!"
        
        echo !line!
    )
) > "%file%.tmp"

:: Replace original file with fixed version
move /y "%file%.tmp" "%file%" >nul

echo Fixed character issues in the dictionary file.
echo Please review the file: %file%
