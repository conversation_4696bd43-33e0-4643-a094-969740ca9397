<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Diagrams Viewer</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .diagram-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            padding: 20px;
            page-break-inside: avoid;
        }
        .diagram-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .diagram {
            text-align: center;
            margin: 0 auto;
            overflow: auto;
        }
        .mermaid {
            margin: 0 auto;
        }
        .file-info {
            font-size: 0.8em;
            color: #7f8c8d;
            margin-top: 10px;
            font-style: italic;
        }
        @media print {
            body {
                background: white;
            }
            .diagram-container {
                page-break-inside: avoid;
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Comphyology Patent Diagrams</h1>
        <div id="diagrams">
            <!-- Diagrams will be inserted here by JavaScript -->
        </div>
    </div>

    <script>
        // Configuration for Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'Arial, sans-serif'
        });

        // Get all Mermaid files
        async function loadMermaidDiagrams() {
            const response = await fetch('list_mmd.php');
            const files = await response.json();
            
            const container = document.getElementById('diagrams');
            
            for (const file of files) {
                const fileResponse = await fetch(file);
                const content = await fileResponse.text();
                
                const diagramContainer = document.createElement('div');
                diagramContainer.className = 'diagram-container';
                
                const title = document.createElement('div');
                title.className = 'diagram-title';
                title.textContent = file.split('/').pop().replace('.mmd', '').replace(/_/g, ' ');
                
                const diagramDiv = document.createElement('div');
                diagramDiv.className = 'diagram';
                diagramDiv.id = 'mermaid-' + file.replace(/[^a-z0-9]/gi, '_');
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'file-info';
                fileInfo.textContent = `Source: ${file}`;
                
                diagramContainer.appendChild(title);
                diagramContainer.appendChild(diagramDiv);
                diagramContainer.appendChild(fileInfo);
                container.appendChild(diagramContainer);
                
                // Render Mermaid diagram
                try {
                    const { svg } = await mermaid.render(diagramDiv.id, content);
                    diagramDiv.innerHTML = svg;
                } catch (error) {
                    console.error(`Error rendering ${file}:`, error);
                    diagramDiv.innerHTML = `<div style="color: red;">Error rendering diagram: ${error.message}</div>`;
                }
            }
        }

        // Load HTML diagrams (SVGs)
        async function loadHtmlDiagrams() {
            const response = await fetch('list_html.php');
            const files = await response.json();
            
            const container = document.getElementById('diagrams');
            
            for (const file of files) {
                const iframe = document.createElement('iframe');
                iframe.src = file;
                iframe.style.width = '100%';
                iframe.style.height = '600px';
                iframe.style.border = 'none';
                
                const diagramContainer = document.createElement('div');
                diagramContainer.className = 'diagram-container';
                
                const title = document.createElement('div');
                title.className = 'diagram-title';
                title.textContent = file.split('/').pop().replace('.html', '').replace(/_/g, ' ');
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'file-info';
                fileInfo.textContent = `Source: ${file}`;
                
                diagramContainer.appendChild(title);
                diagramContainer.appendChild(iframe);
                diagramContainer.appendChild(fileInfo);
                container.appendChild(diagramContainer);
            }
        }

        // Create PHP files if they don't exist
        async function createPhpHelpers() {
            const phpContent = `<?php
                header('Content-Type: application/json');
                $files = glob('*.{mmd,html,svg}', GLOB_BRACE);
                echo json_encode($files);
            ?>`;
            
            // Create list_mmd.php
            const mmdPhp = new Blob([phpContent], { type: 'text/php' });
            const mmdUrl = URL.createObjectURL(mmdPhp);
            
            // Create list_html.php
            const htmlPhp = new Blob([phpContent], { type: 'text/php' });
            const htmlUrl = URL.createObjectURL(htmlPhp);
            
            // Add download links (user needs to save these files to the server)
            const phpInfo = document.createElement('div');
            phpInfo.style.marginTop = '20px';
            phpInfo.style.padding = '15px';
            phpInfo.style.background = '#f8f9fa';
            phpInfo.style.borderRadius = '5px';
            phpInfo.innerHTML = `
                <h3>Server Setup Required</h3>
                <p>To view all diagrams, please save these PHP files to your server:</p>
                <p><a href="${mmdUrl}" download="list_mmd.php">Download list_mmd.php</a> - Lists all MMD files</p>
                <p><a href="${htmlUrl}" download="list_html.php">Download list_html.php</a> - Lists all HTML/SVG files</p>
                <p>Place these files in the same directory as your diagrams.</p>
            `;
            
            document.querySelector('.container').appendChild(phpInfo);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            createPhpHelpers();
            // Uncomment these lines when PHP files are on the server
            // loadMermaidDiagrams();
            // loadHtmlDiagrams();
        });
    </script>
</body>
</html>
