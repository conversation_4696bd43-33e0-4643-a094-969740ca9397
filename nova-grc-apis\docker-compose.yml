version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb://mongo:27017/nova-grc-apis
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mongo
    networks:
      - nova-grc-network

  mongo:
    image: mongo:4.4
    ports:
      - "27018:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - nova-grc-network

  mongo-express:
    image: mongo-express
    ports:
      - "8082:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
    depends_on:
      - mongo
    networks:
      - nova-grc-network

  test:
    build:
      context: .
      dockerfile: Dockerfile.test
    environment:
      - NODE_ENV=test
      - MONGODB_URI=mongodb://mongo:27017/nova-grc-apis-test
    depends_on:
      - mongo
    networks:
      - nova-grc-network
    command: npm test

networks:
  nova-grc-network:
    driver: bridge

volumes:
  mongo-data:
