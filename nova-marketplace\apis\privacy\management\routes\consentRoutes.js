/**
 * Consent Record Routes
 *
 * This file defines the routes for consent records.
 */

const express = require('express');
const router = express.Router();
const { consentController } = require('../controllers');
const { validate } = require('../middleware/validation');
const { consentRecordSchema } = require('../validation');
const { authorize } = require('../middleware/auth');

// Get all consent records
router.get('/records', authorize(['admin', 'user']), consentController.getAllConsentRecords);

// Get a specific consent record by ID
router.get('/records/:id', authorize(['admin', 'user']), consentController.getConsentRecordById);

// Create a new consent record
router.post('/records',
  validate(consentRecordSchema.createConsentRecordSchema),
  consentController.createConsentRecord
);

// Update a consent record
router.put('/records/:id',
  authorize(['admin']),
  validate(consentRecordSchema.updateConsentRecordSchema),
  consentController.updateConsentRecord
);

// Withdraw consent
router.post('/records/:id/withdraw',
  validate(consentRecordSchema.withdrawConsentSchema),
  consentController.withdrawConsent
);

// Verify consent validity
router.get('/records/:id/validity', authorize(['admin', 'user']), consentController.verifyConsentValidity);

// Get consent records by data subject
router.get('/data-subjects/:dataSubjectId', authorize(['admin', 'user']), consentController.getConsentRecordsByDataSubject);

// Get consent records by email
router.get('/emails/:email', authorize(['admin', 'user']), consentController.getConsentRecordsByEmail);

// Generate a consent form
router.get('/forms', consentController.generateConsentForm);

// Verify consent proof
router.post('/proof/verify', authorize(['admin', 'user']), consentController.verifyConsentProof);

// Generate consent proof
router.post('/proof/generate', authorize(['admin']), consentController.generateConsentProof);

module.exports = router;
