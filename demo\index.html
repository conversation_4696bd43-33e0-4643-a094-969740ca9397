<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse API Superstore</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .bg-primary {
            background-color: #172030;
        }
        .bg-secondary {
            background-color: #1e293b;
        }
        .text-primary {
            color: #f8fafc;
        }
        .text-secondary {
            color: #cbd5e1;
        }
        .accent-color {
            color: #2563eb;
        }
        .accent-bg {
            background-color: #2563eb;
        }
    </style>
</head>
<body class="bg-primary text-primary min-h-screen">
    <header class="bg-secondary shadow-lg">
        <div class="container mx-auto px-4 py-6 flex justify-between items-center">
            <h1 class="text-3xl font-bold">NovaFuse API Superstore</h1>
            <div class="flex space-x-4">
                <button class="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700">
                    Partner Login
                </button>
                <button class="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-900 hover:bg-opacity-20">
                    Become a Partner
                </button>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <!-- Hero Section -->
        <div class="accent-bg text-white rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-4">Integrate with NovaFuse</h2>
            <p class="text-xl mb-6">
                Connect your GRC workflows with premium partners and services.
                Streamline compliance, automate risk management, and enhance governance.
            </p>
            <button class="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50">
                Explore Integrations
            </button>
        </div>

        <!-- Featured Partners -->
        <div class="mb-12">
            <h2 class="text-2xl font-bold mb-6">Featured Partners</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Zapier -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 mr-4 bg-gray-800 rounded-md flex items-center justify-center">
                            <span class="text-2xl">Z</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold">Zapier</h3>
                            <p class="text-sm text-gray-400">Integration</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">Connect NovaFuse to 3,000+ apps without code. Automate workflows and integrate with your favorite tools.</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            automation
                        </span>
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            workflow
                        </span>
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            no-code
                        </span>
                    </div>
                    <button class="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700 w-full">
                        Connect
                    </button>
                </div>

                <!-- Okta -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 mr-4 bg-gray-800 rounded-md flex items-center justify-center">
                            <span class="text-2xl">O</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold">Okta</h3>
                            <p class="text-sm text-gray-400">Security</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">Enterprise identity management and single sign-on. Secure access to all your applications.</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            identity
                        </span>
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            authentication
                        </span>
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            sso
                        </span>
                    </div>
                    <button class="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700 w-full">
                        Connect
                    </button>
                </div>

                <!-- Airtable -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 mr-4 bg-gray-800 rounded-md flex items-center justify-center">
                            <span class="text-2xl">A</span>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold">Airtable</h3>
                            <p class="text-sm text-gray-400">Productivity</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4">Low-code database integration. Build custom apps and workflows without coding expertise.</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            database
                        </span>
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            low-code
                        </span>
                        <span class="bg-blue-900 text-blue-200 text-xs px-2 py-1 rounded">
                            collaboration
                        </span>
                    </div>
                    <button class="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700 w-full">
                        Connect
                    </button>
                </div>
            </div>
        </div>

        <!-- API Categories -->
        <div>
            <h2 class="text-2xl font-bold mb-6">API Categories</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Governance -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3">Governance & Board Compliance</h3>
                    <p class="text-gray-300 mb-4">APIs for board meetings, governance policies, compliance reports, and board resolutions.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">12 Endpoints</span>
                        <button class="text-blue-400 hover:text-blue-300">View Documentation ?</button>
                    </div>
                </div>

                <!-- Security -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3">Security</h3>
                    <p class="text-gray-300 mb-4">APIs for vulnerabilities, security policies, incidents, and security scanning.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">15 Endpoints</span>
                        <button class="text-blue-400 hover:text-blue-300">View Documentation ?</button>
                    </div>
                </div>

                <!-- APIs -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3">APIs & Developer Tools</h3>
                    <p class="text-gray-300 mb-4">APIs for API catalog, metrics, integration flows, and developer resources.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">10 Endpoints</span>
                        <button class="text-blue-400 hover:text-blue-300">View Documentation ?</button>
                    </div>
                </div>

                <!-- Risk -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3">Risk & Audit</h3>
                    <p class="text-gray-300 mb-4">APIs for risk assessments, audit reports, risk register, and audit findings.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">14 Endpoints</span>
                        <button class="text-blue-400 hover:text-blue-300">View Documentation ?</button>
                    </div>
                </div>

                <!-- Contracts -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3">Contracts & Policy Lifecycle</h3>
                    <p class="text-gray-300 mb-4">APIs for contracts, policies, contract renewals, and policy reviews.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">11 Endpoints</span>
                        <button class="text-blue-400 hover:text-blue-300">View Documentation ?</button>
                    </div>
                </div>

                <!-- Certifications -->
                <div class="bg-secondary border border-gray-700 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-3">Certifications & Accreditation</h3>
                    <p class="text-gray-300 mb-4">APIs for certifications, accreditations, audit schedules, and certification status.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-400">9 Endpoints</span>
                        <button class="text-blue-400 hover:text-blue-300">View Documentation ?</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-secondary text-white py-12 mt-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">NovaFuse API Superstore</h3>
                    <p class="text-gray-400">
                        Connect your GRC workflows with premium partners and services.
                    </p>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">For Partners</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Become a Partner</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Partner Portal</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">API Documentation</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Support</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">For Customers</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Integration Guides</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">API Reference</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Support Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Contact Us</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">Cookie Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white">GDPR Compliance</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
                <p>&copy; 2025 NovaFuse. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
