/**
 * NovaCore Blockchain Routes
 * 
 * This file defines the routes for blockchain verification.
 */

const express = require('express');
const router = express.Router();
const { BlockchainController } = require('../controllers');
const { authenticate, authorize } = require('../middleware/authMiddleware');

// Verify evidence on blockchain
router.post(
  '/evidence/:evidenceId/verify',
  authenticate,
  authorize('verify:evidence'),
  BlockchainController.verifyEvidence
);

// Check verification status
router.get(
  '/verification/:id/status',
  authenticate,
  authorize('read:verification'),
  BlockchainController.checkVerificationStatus
);

// Get verification by ID
router.get(
  '/verification/:id',
  authenticate,
  authorize('read:verification'),
  BlockchainController.getVerificationById
);

// Get verification by evidence ID
router.get(
  '/evidence/:evidenceId/verification',
  authenticate,
  authorize('read:verification'),
  BlockchainController.getVerificationByEvidenceId
);

// Verify hash against blockchain
router.get(
  '/verify/:hash',
  authenticate,
  authorize('verify:hash'),
  BlockchainController.verifyHash
);

module.exports = router;
