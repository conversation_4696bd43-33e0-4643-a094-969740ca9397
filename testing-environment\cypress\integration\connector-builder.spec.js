describe('Connector Builder', () => {
  beforeEach(() => {
    cy.visit('/partner-portal/connectors/new');
  });

  it('should create a new AWS Security Hub connector', () => {
    // Step 1: Metadata
    cy.get('[data-testid="connector-name"]').type('AWS Security Hub Connector');
    cy.get('[data-testid="connector-category"]').select('Cloud Security');
    cy.get('[data-testid="connector-description"]').type('Connector for AWS Security Hub');
    cy.get('[data-testid="next-button"]').click();

    // Step 2: Authentication
    cy.get('[data-testid="auth-type-AWS_SIG_V4"]').click();
    cy.get('[data-testid="field-accessKeyId"]').type('AKIAIOSFODNN7EXAMPLE');
    cy.get('[data-testid="field-secretAccessKey"]').type('wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY');
    cy.get('[data-testid="field-region"]').type('us-east-1');
    cy.get('[data-testid="test-connection-button"]').click();
    cy.get('[data-testid="test-success-message"]').should('be.visible');
    cy.get('[data-testid="next-button"]').click();

    // Step 3: Configuration
    cy.get('[data-testid="base-url"]').type('https://securityhub.us-east-1.amazonaws.com');
    cy.get('[data-testid="next-button"]').click();

    // Step 4: Endpoints
    cy.get('[data-testid="add-endpoint-button"]').click();
    cy.get('[data-testid="endpoint-name"]').type('Get Findings');
    cy.get('[data-testid="endpoint-path"]').type('/findings');
    cy.get('[data-testid="endpoint-method"]').select('GET');
    cy.get('[data-testid="next-button"]').click();

    // Step 5: Mappings
    cy.get('[data-testid="add-mapping-button"]').click();
    cy.get('[data-testid="mapping-source-endpoint"]').select('Get Findings');
    cy.get('[data-testid="mapping-target-entity"]').type('ComplianceFindings');
    cy.get('[data-testid="add-transformation-button"]').click();
    cy.get('[data-testid="transformation-source"]').type('$.Findings[*].Id');
    cy.get('[data-testid="transformation-target"]').type('findingId');
    cy.get('[data-testid="transformation-function"]').select('identity');
    cy.get('[data-testid="save-transformation-button"]').click();
    cy.get('[data-testid="next-button"]').click();

    // Step 6: Events
    cy.get('[data-testid="add-polling-button"]').click();
    cy.get('[data-testid="polling-endpoint"]').select('Get Findings');
    cy.get('[data-testid="polling-interval"]').type('1h');
    cy.get('[data-testid="save-polling-button"]').click();
    cy.get('[data-testid="next-button"]').click();

    // Step 7: Review and Save
    cy.get('[data-testid="save-connector-button"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
});

describe('Connector Management', () => {
  beforeEach(() => {
    cy.visit('/partner-portal/connectors');
  });

  it('should list connectors', () => {
    cy.get('[data-testid="connector-list"]').should('be.visible');
    cy.get('[data-testid="connector-item"]').should('have.length.at.least', 1);
  });

  it('should view connector details', () => {
    cy.get('[data-testid="connector-item"]').first().click();
    cy.get('[data-testid="connector-details"]').should('be.visible');
    cy.get('[data-testid="connector-name"]').should('be.visible');
    cy.get('[data-testid="connector-category"]').should('be.visible');
    cy.get('[data-testid="connector-description"]').should('be.visible');
  });

  it('should edit connector', () => {
    cy.get('[data-testid="connector-item"]').first().find('[data-testid="edit-button"]').click();
    cy.get('[data-testid="connector-name"]').clear().type('Updated Connector Name');
    cy.get('[data-testid="save-button"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="connector-name"]').should('have.value', 'Updated Connector Name');
  });

  it('should delete connector', () => {
    const initialCount = cy.get('[data-testid="connector-item"]').its('length');
    cy.get('[data-testid="connector-item"]').first().find('[data-testid="delete-button"]').click();
    cy.get('[data-testid="confirm-delete-button"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="connector-item"]').should('have.length', initialCount - 1);
  });
});

describe('Credential Management', () => {
  beforeEach(() => {
    cy.visit('/partner-portal/credentials');
  });

  it('should create new credentials', () => {
    cy.get('[data-testid="add-credential-button"]').click();
    cy.get('[data-testid="credential-name"]').type('Test AWS Credentials');
    cy.get('[data-testid="connector-select"]').select('AWS Security Hub');
    cy.get('[data-testid="field-accessKeyId"]').type('AKIAIOSFODNN7EXAMPLE');
    cy.get('[data-testid="field-secretAccessKey"]').type('wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY');
    cy.get('[data-testid="field-region"]').type('us-east-1');
    cy.get('[data-testid="save-credential-button"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
  });

  it('should list credentials', () => {
    cy.get('[data-testid="credential-list"]').should('be.visible');
    cy.get('[data-testid="credential-item"]').should('have.length.at.least', 1);
  });

  it('should edit credentials', () => {
    cy.get('[data-testid="credential-item"]').first().find('[data-testid="edit-button"]').click();
    cy.get('[data-testid="credential-name"]').clear().type('Updated Credential Name');
    cy.get('[data-testid="save-credential-button"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="credential-name"]').should('have.value', 'Updated Credential Name');
  });

  it('should delete credentials', () => {
    const initialCount = cy.get('[data-testid="credential-item"]').its('length');
    cy.get('[data-testid="credential-item"]').first().find('[data-testid="delete-button"]').click();
    cy.get('[data-testid="confirm-delete-button"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="credential-item"]').should('have.length', initialCount - 1);
  });
});
