<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIG. 8: NovaConnect Performance Benchmarks</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 950px;
            height: 800px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
        }
        .chart-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .chart {
            width: 45%;
            height: 250px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            position: relative;
        }
        .chart-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .bar-container {
            height: 180px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: 30px;
            margin-top: 20px;
        }
        .bar-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 90px;
        }
        .bar {
            width: 40px;
            background-color: #0A84FF;
            margin-bottom: 5px;
            position: relative;
        }
        .bar-label {
            text-align: center;
            font-size: 12px;
            width: 80px;
            overflow: visible;
            white-space: nowrap;
        }
        .bar-value {
            position: absolute;
            top: -20px;
            width: 100%;
            text-align: center;
            font-size: 12px;
            font-weight: bold;
        }
        .axis {
            position: absolute;
            background-color: #333;
        }
        .x-axis {
            bottom: 30px;
            left: 50px;
            width: calc(100% - 60px);
            height: 2px;
        }
        .y-axis {
            bottom: 30px;
            left: 50px;
            width: 2px;
            height: 180px;
        }
        .test-scenario {
            margin-top: 20px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            position: relative;
        }

        /* Special styling for the remediation card */
        .test-scenario:nth-child(4) {
            padding: 20px;
            min-height: 180px;
        }
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        /* Make remediation card title more prominent */
        .test-scenario:nth-child(4) .test-title {
            font-size: 18px;
            margin-bottom: 15px;
        }
        .test-details {
            font-size: 14px;
            margin-bottom: 5px;
        }

        /* Make remediation card text more prominent */
        .test-scenario:nth-child(4) .test-details {
            font-size: 15px;
            margin-bottom: 8px;
        }
        .test-result {
            margin-top: 10px;
            font-weight: bold;
            color: #0A84FF;
        }

        /* Make remediation card result more prominent */
        .test-scenario:nth-child(4) .test-result {
            margin-top: 15px;
            font-size: 16px;
        }
        .component-number {
            position: absolute;
            width: 28px;
            height: 28px;
            background-color: #000;
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            border: 2px solid white;
            box-shadow: 0 0 5px rgba(0,0,0,0.5);
            z-index: 100;
        }
        .inventor-label {
            position: absolute;
            left: 20px;
            bottom: 20px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
    </style>
</head>
<body>
    <h1 class="title">FIG. 8: NovaConnect Performance Benchmarks</h1>

    <div class="diagram-container">
        <div class="chart-container">
            <div class="chart">
                <div class="chart-title">Data Normalization Speed (ms)</div>
                <div class="component-number" style="top: 10px; right: 10px; z-index: 100;">801</div>
                <div class="bar-container">
                    <div class="bar-group">
                        <div class="bar" style="height: 1px;">
                            <div class="bar-value">0.07</div>
                        </div>
                        <div class="bar-label" style="transform: translateX(15px);">NovaConnect</div>
                    </div>
                    <div class="bar-group">
                        <div class="bar" style="height: 160px; background-color: #6c757d;">
                            <div class="bar-value">220</div>
                        </div>
                        <div class="bar-label">AWS</div>
                    </div>
                    <div class="bar-group">
                        <div class="bar" style="height: 130px; background-color: #6c757d;">
                            <div class="bar-value">180</div>
                        </div>
                        <div class="bar-label">Azure</div>
                    </div>
                </div>
                <div class="axis x-axis"></div>
                <div class="axis y-axis"></div>
            </div>

            <div class="chart">
                <div class="chart-title">Events Processed Per Second</div>
                <div class="component-number" style="top: 10px; right: 10px; z-index: 100;">802</div>
                <div class="bar-container">
                    <div class="bar-group">
                        <div class="bar" style="height: 160px;">
                            <div class="bar-value">69,000</div>
                        </div>
                        <div class="bar-label" style="transform: translateX(15px);">NovaConnect</div>
                    </div>
                    <div class="bar-group">
                        <div class="bar" style="height: 12px; background-color: #6c757d;">
                            <div class="bar-value">5,000</div>
                        </div>
                        <div class="bar-label">AWS</div>
                    </div>
                    <div class="bar-group">
                        <div class="bar" style="height: 17px; background-color: #6c757d;">
                            <div class="bar-value">7,500</div>
                        </div>
                        <div class="bar-label">Azure</div>
                    </div>
                </div>
                <div class="axis x-axis"></div>
                <div class="axis y-axis"></div>
            </div>
        </div>

        <div class="test-scenario">
            <div class="test-title">Test Scenario: Data Normalization Speed</div>
            <div class="component-number" style="top: 10px; right: 10px; z-index: 100;">803</div>
            <div class="test-details"><strong>Methodology:</strong> Process 10,000 compliance findings through normalization pipeline</div>
            <div class="test-details"><strong>Environment:</strong> Standard cloud instance (8 vCPU, 32GB RAM)</div>
            <div class="test-details"><strong>Results:</strong></div>
            <div class="test-details">- NovaConnect: 0.07ms per finding</div>
            <div class="test-details">- AWS: 220ms per finding</div>
            <div class="test-details">- Azure: 180ms per finding</div>
            <div class="test-result">Performance Gain: 3,142x faster than industry average</div>
        </div>

        <div class="test-scenario">
            <div class="test-title">Test Scenario: Remediation Response Time</div>
            <div class="component-number" style="top: 10px; right: 10px; z-index: 100;">804</div>
            <div class="test-details"><strong>Methodology:</strong> Measure time from detection to containment of simulated threat</div>
            <div class="test-details"><strong>Environment:</strong> Multi-cloud test environment with standardized threat simulation</div>
            <div class="test-details"><strong>Results:</strong></div>
            <div class="test-details">- NovaConnect: 2s remediation time</div>
            <div class="test-details">- AWS: 8-12s remediation time</div>
            <div class="test-details">- Azure: 8-10s remediation time</div>
            <div class="test-result">Performance Gain: 4-6x faster than competitors</div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
