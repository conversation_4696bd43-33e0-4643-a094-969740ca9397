/**
 * Zapier Routes
 * 
 * This file contains routes for Zapier integration.
 */

const express = require('express');
const router = express.Router();
const ZapierController = require('../controllers/ZapierController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// Zapier app definition
router.get('/app-definition', ZapierController.getAppDefinition);

// Zapier triggers and actions
router.get('/triggers', ZapierController.getTriggers);
router.get('/actions', ZapierController.getActions);

// OAuth endpoints
router.get('/oauth/authorize', ZapierController.authorizeOAuth);
router.post('/oauth/token', ZapierController.getOAuthToken);

// App hooks
router.get('/before-app', ZapierController.beforeApp);
router.get('/after-app', ZapierController.afterApp);

// Trigger endpoints
router.get('/triggers/new-connector', ZapierController.newConnectorTrigger);
router.get('/triggers/new-workflow', ZapierController.newWorkflowTrigger);
router.get('/triggers/compliance-event', ZapierController.complianceEventTrigger);

// Action endpoints
router.post('/actions/create-connector', ZapierController.createConnectorAction);
router.post('/actions/execute-workflow', ZapierController.executeWorkflowAction);
router.post('/actions/create-compliance-evidence', ZapierController.createComplianceEvidenceAction);

// Admin routes (require authentication)
router.use(authenticate);

// App management
router.post('/apps', hasPermission('admin:zapier'), ZapierController.registerApp);
router.get('/apps', hasPermission('admin:zapier'), ZapierController.getAllApps);
router.get('/apps/:id', hasPermission('admin:zapier'), ZapierController.getAppById);
router.put('/apps/:id', hasPermission('admin:zapier'), ZapierController.updateApp);
router.delete('/apps/:id', hasPermission('admin:zapier'), ZapierController.deleteApp);

// Trigger and action management
router.post('/triggers/register', hasPermission('admin:zapier'), ZapierController.registerTrigger);
router.post('/actions/register', hasPermission('admin:zapier'), ZapierController.registerAction);

module.exports = router;
