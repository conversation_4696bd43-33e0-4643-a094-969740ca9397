/**
 * Error Display Component
 * 
 * A reusable component for displaying error messages with different severity levels.
 * It can be used to show API errors, validation errors, or informational messages.
 */

import React from 'react';
import { AlertCircle, AlertTriangle, Info, CheckCircle, X } from 'lucide-react';

export type ErrorSeverity = 'error' | 'warning' | 'info' | 'success';

interface ErrorDisplayProps {
  message: string;
  severity?: ErrorSeverity;
  details?: string;
  onDismiss?: () => void;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  message,
  severity = 'error',
  details,
  onDismiss,
  className = '',
}) => {
  // Define styles based on severity
  const getSeverityStyles = (): { bgColor: string; textColor: string; icon: React.ReactNode } => {
    switch (severity) {
      case 'error':
        return {
          bgColor: 'bg-red-50',
          textColor: 'text-red-800',
          icon: <AlertCircle className="h-5 w-5 text-red-500" />,
        };
      case 'warning':
        return {
          bgColor: 'bg-yellow-50',
          textColor: 'text-yellow-800',
          icon: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
        };
      case 'info':
        return {
          bgColor: 'bg-blue-50',
          textColor: 'text-blue-800',
          icon: <Info className="h-5 w-5 text-blue-500" />,
        };
      case 'success':
        return {
          bgColor: 'bg-green-50',
          textColor: 'text-green-800',
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        };
      default:
        return {
          bgColor: 'bg-red-50',
          textColor: 'text-red-800',
          icon: <AlertCircle className="h-5 w-5 text-red-500" />,
        };
    }
  };

  const { bgColor, textColor, icon } = getSeverityStyles();

  return (
    <div className={`rounded-md ${bgColor} p-4 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">{icon}</div>
        <div className="ml-3 flex-grow">
          <h3 className={`text-sm font-medium ${textColor}`}>{message}</h3>
          {details && (
            <div className={`mt-2 text-sm ${textColor} opacity-80`}>
              <p>{details}</p>
            </div>
          )}
        </div>
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className={`inline-flex rounded-md p-1.5 ${textColor} hover:bg-opacity-20 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-${bgColor} focus:ring-${textColor}`}
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ErrorDisplay;
