"""
Evidence Manager for the Universal Compliance Evidence Collection System.

This module provides the main manager for handling compliance evidence,
including evidence lifecycle management, tagging, categorization, expiration,
workflow management, and relationships between evidence items.
"""

import logging
import uuid
import json
import os
import datetime
import time
import threading
from enum import Enum
from typing import Dict, List, Any, Optional, Callable, Set, Union, Tuple, Iterator

from .collector_manager import CollectorManager
from .validator_manager import ValidatorManager, ValidationLevel, ValidationMode, ValidationResult
from .storage_manager import StorageManager, AccessLevel, EncryptionType
from .report_manager import ReportManager
from .notification_manager import NotificationManager, NotificationType
from .query_manager import QueryManager, QueryField, QueryOperator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class EvidenceRelationshipType(Enum):
    """Types of relationships between evidence items."""
    PARENT = "parent"          # Parent-child relationship
    CHILD = "child"            # Child-parent relationship
    RELATED = "related"        # General relationship
    SUPERSEDES = "supersedes"  # This evidence supersedes another
    SUPERSEDED_BY = "superseded_by"  # This evidence is superseded by another
    DERIVED_FROM = "derived_from"  # This evidence is derived from another
    SUPPORTS = "supports"      # This evidence supports another
    CONTRADICTS = "contradicts"  # This evidence contradicts another


class EvidenceWorkflowState(Enum):
    """States in an evidence workflow."""
    DRAFT = "draft"            # Initial draft state
    PENDING_REVIEW = "pending_review"  # Pending review
    UNDER_REVIEW = "under_review"  # Currently being reviewed
    APPROVED = "approved"      # Approved
    REJECTED = "rejected"      # Rejected
    NEEDS_REVISION = "needs_revision"  # Needs revision
    REVISED = "revised"        # Revised after rejection
    EXPIRED = "expired"        # Expired
    ARCHIVED = "archived"      # Archived

class EvidenceManager:
    """
    Main manager for handling compliance evidence.

    This class orchestrates the collection, validation, and storage of compliance
    evidence, with support for evidence lifecycle management, tagging, categorization,
    workflow management, relationships, and expiration.
    """

    # Evidence lifecycle states
    LIFECYCLE_STATES = {
        'DRAFT': 'draft',
        'COLLECTED': 'collected',
        'VALIDATED': 'validated',
        'INVALID': 'invalid',
        'STORED': 'stored',
        'EXPIRED': 'expired',
        'ARCHIVED': 'archived',
        'DELETED': 'deleted'
    }

    # Evidence categories
    DEFAULT_CATEGORIES = [
        'configuration',
        'policy',
        'procedure',
        'log',
        'report',
        'assessment',
        'certification',
        'audit',
        'interview',
        'observation'
    ]

    def __init__(self,
                evidence_dir: Optional[str] = None,
                relationships_dir: Optional[str] = None,
                workflow_dir: Optional[str] = None,
                auto_expire: bool = True,
                default_expiry_days: int = 365,
                enable_workflows: bool = True,
                enable_relationships: bool = True,
                current_user_id: str = "system",
                collectors_dir: Optional[str] = None,
                validators_dir: Optional[str] = None,
                storage_dir: Optional[str] = None,
                chains_dir: Optional[str] = None,
                scripts_dir: Optional[str] = None,
                audit_log_dir: Optional[str] = None,
                versions_dir: Optional[str] = None,
                access_control_dir: Optional[str] = None,
                encryption_keys_dir: Optional[str] = None,
                enable_versioning: bool = True,
                enable_encryption: bool = True,
                enable_access_control: bool = True,
                enable_audit_logging: bool = True):
        """
        Initialize the Evidence Manager.

        Args:
            evidence_dir: Path to a directory for storing evidence metadata
            relationships_dir: Path to a directory for storing evidence relationships
            workflow_dir: Path to a directory for storing workflow state
            auto_expire: Whether to automatically check for expired evidence
            default_expiry_days: Default number of days until evidence expires
            enable_workflows: Whether to enable evidence workflows
            enable_relationships: Whether to enable evidence relationships
            current_user_id: ID of the current user
            collectors_dir: Path to a directory containing collector implementations
            validators_dir: Path to a directory containing validator implementations
            storage_dir: Path to a directory containing storage provider implementations
            chains_dir: Path to a directory containing validation chain definitions
            scripts_dir: Path to a directory containing custom validation scripts
            audit_log_dir: Path to a directory for storing audit logs
            versions_dir: Path to a directory for storing evidence versions
            access_control_dir: Path to a directory for storing access control information
            encryption_keys_dir: Path to a directory for storing encryption keys
            enable_versioning: Whether to enable evidence versioning
            enable_encryption: Whether to enable evidence encryption
            enable_access_control: Whether to enable evidence access control
            enable_audit_logging: Whether to enable audit logging
        """
        logger.info("Initializing Evidence Manager")

        # Set the current user ID
        self.current_user_id = current_user_id

        # Set feature flags
        self.enable_workflows = enable_workflows
        self.enable_relationships = enable_relationships

        # Initialize the collector manager
        self.collector_manager = CollectorManager(collectors_dir=collectors_dir)

        # Initialize the validator manager
        self.validator_manager = ValidatorManager(
            validators_dir=validators_dir,
            chains_dir=chains_dir,
            scripts_dir=scripts_dir
        )

        # Initialize the storage manager
        self.storage_manager = StorageManager(
            storage_dir=storage_dir,
            audit_log_dir=audit_log_dir,
            versions_dir=versions_dir,
            access_control_dir=access_control_dir,
            encryption_keys_dir=encryption_keys_dir,
            enable_versioning=enable_versioning,
            enable_encryption=enable_encryption,
            enable_access_control=enable_access_control,
            enable_audit_logging=enable_audit_logging,
            current_user_id=current_user_id
        )

        # Initialize the report manager
        self.report_manager = ReportManager(reports_dir=os.path.join(os.getcwd(), 'reports'))

        # Initialize the notification manager
        self.notification_manager = NotificationManager(notifications_dir=os.path.join(os.getcwd(), 'notifications'))

        # Initialize the query manager
        self.query_manager = QueryManager()

        # Dictionary to store registered evidence handlers
        self.evidence_handlers: Dict[str, List[Callable]] = {}

        # Set the evidence directory
        self.evidence_dir = evidence_dir or os.path.join(os.getcwd(), 'evidence_data')

        # Create the evidence directory if it doesn't exist
        os.makedirs(self.evidence_dir, exist_ok=True)

        # Set the relationships directory
        self.relationships_dir = relationships_dir or os.path.join(os.getcwd(), 'evidence_relationships')

        # Create the relationships directory if it doesn't exist
        os.makedirs(self.relationships_dir, exist_ok=True)

        # Set the workflow directory
        self.workflow_dir = workflow_dir or os.path.join(os.getcwd(), 'evidence_workflows')

        # Create the workflow directory if it doesn't exist
        os.makedirs(self.workflow_dir, exist_ok=True)

        # Dictionary to store evidence metadata in memory
        self.evidence_metadata: Dict[str, Dict[str, Any]] = {}

        # Dictionary to store evidence tags
        self.evidence_tags: Dict[str, Set[str]] = {}

        # Dictionary to store evidence by category
        self.evidence_by_category: Dict[str, Set[str]] = {}

        # Dictionary to store evidence by requirement
        self.evidence_by_requirement: Dict[str, Set[str]] = {}

        # Dictionary to store evidence relationships
        self.evidence_relationships: Dict[str, Dict[str, List[Dict[str, Any]]]] = {}

        # Dictionary to store evidence workflow state
        self.evidence_workflows: Dict[str, Dict[str, Any]] = {}

        # Set auto-expire flag
        self.auto_expire = auto_expire

        # Set default expiry days
        self.default_expiry_days = default_expiry_days

        # Load evidence metadata from disk
        self._load_evidence_metadata()

        # Load evidence relationships from disk
        if self.enable_relationships:
            self._load_evidence_relationships()

        # Load evidence workflow state from disk
        if self.enable_workflows:
            self._load_evidence_workflows()

        # Check for expired evidence if auto-expire is enabled
        if self.auto_expire:
            self._check_expired_evidence()

        logger.info("Evidence Manager initialized")

    def register_evidence_handler(self, evidence_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific evidence type.

        Args:
            evidence_type: The type of evidence
            handler: The handler function
        """
        if evidence_type not in self.evidence_handlers:
            self.evidence_handlers[evidence_type] = []

        self.evidence_handlers[evidence_type].append(handler)
        logger.info(f"Registered evidence handler for evidence type: {evidence_type}")

    def unregister_evidence_handler(self, evidence_type: str, handler: Callable) -> None:
        """
        Unregister a handler for a specific evidence type.

        Args:
            evidence_type: The type of evidence
            handler: The handler function
        """
        if evidence_type in self.evidence_handlers and handler in self.evidence_handlers[evidence_type]:
            self.evidence_handlers[evidence_type].remove(handler)
            logger.info(f"Unregistered evidence handler for evidence type: {evidence_type}")

    def register_evidence(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Register evidence with the system.

        Args:
            evidence: The evidence to register

        Returns:
            The registered evidence

        Raises:
            ValueError: If the evidence is missing required fields
        """
        # Check if the evidence has an ID
        if 'id' not in evidence:
            evidence['id'] = str(uuid.uuid4())

        evidence_id = evidence['id']
        logger.info(f"Registering evidence: {evidence_id}")

        # Check if the evidence already exists
        if evidence_id in self.evidence_metadata:
            logger.warning(f"Evidence already exists: {evidence_id}")

            # Send a notification
            self.notification_manager.send_notification(
                message=f"Evidence already exists: {evidence_id}",
                notification_type=NotificationType.WARNING,
                subject="Evidence Registration",
                details={
                    'evidence_id': evidence_id,
                    'type': evidence.get('type'),
                    'source': evidence.get('source')
                }
            )

            return self.evidence_metadata[evidence_id]

        # Set the status to draft if not provided
        if 'status' not in evidence:
            evidence['status'] = self.LIFECYCLE_STATES['DRAFT']

        # Set the created_at timestamp if not provided
        if 'created_at' not in evidence:
            evidence['created_at'] = self._get_current_timestamp()

        # Set the updated_at timestamp
        evidence['updated_at'] = self._get_current_timestamp()

        # Extract metadata from the evidence
        metadata = {
            'id': evidence_id,
            'type': evidence.get('type'),
            'source': evidence.get('source'),
            'status': evidence.get('status'),
            'created_at': evidence.get('created_at'),
            'updated_at': evidence.get('updated_at')
        }

        # Add the metadata to the in-memory dictionary
        self.evidence_metadata[evidence_id] = metadata

        # Initialize the tags set
        self.evidence_tags[evidence_id] = set()

        # Extract tags from the evidence
        if 'metadata' in evidence and 'tags' in evidence['metadata']:
            tags = evidence['metadata']['tags']
            if isinstance(tags, list):
                for tag in tags:
                    self.add_evidence_tag(evidence_id, tag)

        # Save the evidence metadata
        self._save_evidence_metadata(evidence_id)

        # Call registered evidence handlers
        evidence_type = evidence.get('type')
        if evidence_type in self.evidence_handlers:
            for handler in self.evidence_handlers[evidence_type]:
                try:
                    handler(evidence)
                except Exception as e:
                    logger.error(f"Error in evidence handler for {evidence_type}: {e}")

        logger.info(f"Evidence registered: {evidence_id}")

        # Send a notification
        self.notification_manager.send_notification(
            message=f"New evidence registered: {evidence_id}",
            notification_type=NotificationType.INFO,
            subject="Evidence Registration",
            details={
                'evidence_id': evidence_id,
                'type': evidence.get('type'),
                'source': evidence.get('source'),
                'status': evidence.get('status')
            }
        )

        return metadata

    def collect_evidence(self,
                        collector_id: str,
                        parameters: Optional[Dict[str, Any]] = None,
                        category: Optional[str] = None,
                        tags: Optional[List[str]] = None,
                        requirements: Optional[List[str]] = None,
                        expiry_days: Optional[int] = None) -> Dict[str, Any]:
        """
        Collect evidence using a specific collector.

        Args:
            collector_id: The ID of the collector
            parameters: Parameters for the collector
            category: The category of the evidence
            tags: List of tags to associate with the evidence
            requirements: List of requirement IDs to associate with the evidence
            expiry_days: Number of days until the evidence expires

        Returns:
            The collected evidence

        Raises:
            ValueError: If the collector does not exist
        """
        logger.info(f"Collecting evidence using collector: {collector_id}")

        # Generate a unique evidence ID
        evidence_id = str(uuid.uuid4())

        # Collect the evidence
        evidence_data = self.collector_manager.collect(collector_id, parameters)

        # Get the current timestamp
        current_timestamp = self._get_current_timestamp()

        # Calculate the expiry date if provided
        expiry_date = None
        if expiry_days is not None:
            expiry_date = self._calculate_expiry_date(current_timestamp, expiry_days)

        # Create the evidence object
        evidence = {
            'id': evidence_id,
            'collector_id': collector_id,
            'type': evidence_data.get('type', 'unknown'),
            'data': evidence_data,
            'status': self.LIFECYCLE_STATES['COLLECTED'],
            'validation_results': None,
            'storage_location': None,
            'category': category,
            'tags': tags or [],
            'requirements': requirements or [],
            'expiry_date': expiry_date,
            'created_at': current_timestamp,
            'updated_at': current_timestamp
        }

        # Store the evidence metadata
        self.evidence_metadata[evidence_id] = evidence

        # Add the evidence to the tags dictionary
        if tags:
            self.evidence_tags[evidence_id] = set(tags)

        # Add the evidence to the category dictionary
        if category:
            if category not in self.evidence_by_category:
                self.evidence_by_category[category] = set()
            self.evidence_by_category[category].add(evidence_id)

        # Add the evidence to the requirement dictionary
        if requirements:
            for requirement_id in requirements:
                if requirement_id not in self.evidence_by_requirement:
                    self.evidence_by_requirement[requirement_id] = set()
                self.evidence_by_requirement[requirement_id].add(evidence_id)

        # Save the evidence metadata
        self._save_evidence_metadata(evidence_id)

        # Call registered evidence handlers
        evidence_type = evidence.get('type')
        if evidence_type in self.evidence_handlers:
            for handler in self.evidence_handlers[evidence_type]:
                try:
                    handler(evidence)
                except Exception as e:
                    logger.error(f"Error in evidence handler for {evidence_type}: {e}")

        logger.info(f"Evidence collected: {evidence_id}")

        return evidence

    def validate_evidence(self,
                       evidence: Dict[str, Any],
                       validator_id: str,
                       user_id: str = None) -> Dict[str, Any]:
        """
        Validate evidence using a specific validator.

        Args:
            evidence: The evidence to validate
            validator_id: The ID of the validator
            user_id: The ID of the user performing the validation

        Returns:
            The updated evidence with validation results

        Raises:
            ValueError: If the validator does not exist
        """
        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        evidence_id = evidence.get('id')
        logger.info(f"Validating evidence: {evidence_id} using validator: {validator_id}")

        # Validate the evidence
        validation_result = self.validator_manager.validate(validator_id, evidence)

        # Convert ValidationResult to dictionary if needed
        if hasattr(validation_result, 'to_dict'):
            validation_results = validation_result.to_dict()
        else:
            validation_results = validation_result

        # Update the evidence object
        evidence['validation_results'] = validation_results
        evidence['status'] = self.LIFECYCLE_STATES['VALIDATED'] if validation_results.get('is_valid', False) else self.LIFECYCLE_STATES['INVALID']
        evidence['updated_at'] = self._get_current_timestamp()

        # Update the evidence metadata
        if evidence_id in self.evidence_metadata:
            self.evidence_metadata[evidence_id].update({
                'validation_results': validation_results,
                'status': evidence['status'],
                'updated_at': evidence['updated_at'],
                'validated_by': user_id
            })

            # Save the updated metadata
            self._save_evidence_metadata(evidence_id)

            # Update workflow state if workflows are enabled
            if self.enable_workflows and evidence_id in self.evidence_workflows:
                workflow = self.evidence_workflows[evidence_id]

                # Update the workflow state based on validation result
                if validation_results.get('is_valid', False):
                    # If the evidence was in DRAFT or NEEDS_REVISION state, move it to PENDING_REVIEW
                    if workflow.get('state') in [EvidenceWorkflowState.DRAFT.value, EvidenceWorkflowState.NEEDS_REVISION.value]:
                        workflow['state'] = EvidenceWorkflowState.PENDING_REVIEW.value
                        workflow['updated_at'] = self._get_current_timestamp()
                        workflow['updated_by'] = user_id
                        workflow['history'].append({
                            'state': EvidenceWorkflowState.PENDING_REVIEW.value,
                            'timestamp': self._get_current_timestamp(),
                            'user_id': user_id,
                            'comment': f"Validation passed using {validator_id}"
                        })

                        # Save the updated workflow state
                        self._save_evidence_workflow(evidence_id)
                else:
                    # If validation failed, move to NEEDS_REVISION state
                    if workflow.get('state') != EvidenceWorkflowState.NEEDS_REVISION.value:
                        workflow['state'] = EvidenceWorkflowState.NEEDS_REVISION.value
                        workflow['updated_at'] = self._get_current_timestamp()
                        workflow['updated_by'] = user_id
                        workflow['history'].append({
                            'state': EvidenceWorkflowState.NEEDS_REVISION.value,
                            'timestamp': self._get_current_timestamp(),
                            'user_id': user_id,
                            'comment': f"Validation failed using {validator_id}: {', '.join(validation_results.get('errors', []))}"
                        })

                        # Save the updated workflow state
                        self._save_evidence_workflow(evidence_id)

        # Call registered evidence handlers
        evidence_type = evidence.get('type')
        if evidence_type in self.evidence_handlers:
            for handler in self.evidence_handlers[evidence_type]:
                try:
                    handler(evidence)
                except Exception as e:
                    logger.error(f"Error in evidence handler for {evidence_type}: {e}")

        logger.info(f"Evidence validated: {evidence_id}")

        # Send a notification about the validation result
        if validation_results.get('is_valid', False):
            self.notification_manager.send_notification(
                message=f"Evidence validation passed: {evidence_id}",
                notification_type=NotificationType.INFO,
                subject="Evidence Validation",
                details={
                    'evidence_id': evidence_id,
                    'validator_id': validator_id,
                    'user_id': user_id,
                    'validation_results': validation_results
                }
            )
        else:
            self.notification_manager.send_notification(
                message=f"Evidence validation failed: {evidence_id}",
                notification_type=NotificationType.WARNING,
                subject="Evidence Validation",
                details={
                    'evidence_id': evidence_id,
                    'validator_id': validator_id,
                    'user_id': user_id,
                    'validation_results': validation_results,
                    'errors': validation_results.get('errors', [])
                }
            )

        return evidence

    def store_evidence(self,
                     evidence: Dict[str, Any],
                     storage_id: str,
                     user_id: str = None,
                     encrypt: bool = None,
                     encryption_type: EncryptionType = None,
                     create_version: bool = None,
                     version_comment: str = None) -> Dict[str, Any]:
        """
        Store evidence using a specific storage provider.

        Args:
            evidence: The evidence to store
            storage_id: The ID of the storage provider
            user_id: The ID of the user storing the evidence
            encrypt: Whether to encrypt the evidence
            encryption_type: The type of encryption to use
            create_version: Whether to create a version of the evidence
            version_comment: A comment describing the version

        Returns:
            The updated evidence with storage location

        Raises:
            ValueError: If the storage provider does not exist
        """
        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        evidence_id = evidence.get('id')
        logger.info(f"Storing evidence: {evidence_id} using storage provider: {storage_id}")

        # Store the evidence
        storage_location = self.storage_manager.store(
            provider_id=storage_id,
            evidence=evidence,
            user_id=user_id,
            encrypt=encrypt,
            encryption_type=encryption_type,
            create_version=create_version,
            version_comment=version_comment
        )

        # Update the evidence object
        evidence['storage_location'] = storage_location
        evidence['status'] = self.LIFECYCLE_STATES['STORED']
        evidence['updated_at'] = self._get_current_timestamp()

        # Update the evidence metadata
        if evidence_id in self.evidence_metadata:
            self.evidence_metadata[evidence_id].update({
                'storage_location': storage_location,
                'status': evidence['status'],
                'updated_at': evidence['updated_at'],
                'stored_by': user_id,
                'encrypted': encrypt if encrypt is not None else False,
                'encryption_type': encryption_type.value if encryption_type else None,
                'versioned': create_version if create_version is not None else False
            })

            # Save the updated metadata
            self._save_evidence_metadata(evidence_id)

            # Update workflow state if workflows are enabled
            if self.enable_workflows and evidence_id in self.evidence_workflows:
                workflow = self.evidence_workflows[evidence_id]

                # If the evidence was in PENDING_REVIEW state, keep it there
                # This allows for storing intermediate versions during the review process
                if workflow.get('state') == EvidenceWorkflowState.PENDING_REVIEW.value:
                    workflow['updated_at'] = self._get_current_timestamp()
                    workflow['updated_by'] = user_id
                    workflow['history'].append({
                        'state': EvidenceWorkflowState.PENDING_REVIEW.value,
                        'timestamp': self._get_current_timestamp(),
                        'user_id': user_id,
                        'comment': f"Evidence stored using {storage_id}"
                    })

                    # Save the updated workflow state
                    self._save_evidence_workflow(evidence_id)

        # Call registered evidence handlers
        evidence_type = evidence.get('type')
        if evidence_type in self.evidence_handlers:
            for handler in self.evidence_handlers[evidence_type]:
                try:
                    handler(evidence)
                except Exception as e:
                    logger.error(f"Error in evidence handler for {evidence_type}: {e}")

        logger.info(f"Evidence stored: {evidence_id}")

        # Send a notification about the storage
        self.notification_manager.send_notification(
            message=f"Evidence stored: {evidence_id}",
            notification_type=NotificationType.INFO,
            subject="Evidence Storage",
            details={
                'evidence_id': evidence_id,
                'storage_id': storage_id,
                'user_id': user_id,
                'storage_location': storage_location,
                'encrypted': encrypt if encrypt is not None else False,
                'versioned': create_version if create_version is not None else False
            }
        )

        return evidence

    def retrieve_evidence(self,
                        evidence_id: str,
                        storage_id: str,
                        user_id: str = None,
                        version_id: str = None,
                        decrypt: bool = None) -> Dict[str, Any]:
        """
        Retrieve evidence from a specific storage provider.

        Args:
            evidence_id: The ID of the evidence
            storage_id: The ID of the storage provider
            user_id: The ID of the user retrieving the evidence
            version_id: The ID of the version to retrieve
            decrypt: Whether to decrypt the evidence

        Returns:
            The retrieved evidence

        Raises:
            ValueError: If the storage provider does not exist
            ValueError: If the evidence does not exist
            ValueError: If the user does not have permission to retrieve evidence
        """
        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        logger.info(f"Retrieving evidence: {evidence_id} from storage provider: {storage_id}")

        # Retrieve the evidence
        evidence = self.storage_manager.retrieve(
            provider_id=storage_id,
            evidence_id=evidence_id,
            user_id=user_id,
            version_id=version_id,
            decrypt=decrypt
        )

        # Update the evidence metadata to record the retrieval
        if evidence_id in self.evidence_metadata:
            # Record the last retrieval
            self.evidence_metadata[evidence_id].update({
                'last_retrieved_at': self._get_current_timestamp(),
                'last_retrieved_by': user_id,
                'last_retrieved_version': version_id
            })

            # Save the updated metadata
            self._save_evidence_metadata(evidence_id)

        logger.info(f"Evidence retrieved: {evidence_id}")

        return evidence

    def delete_evidence(self,
                      evidence_id: str,
                      storage_id: str,
                      user_id: str = None,
                      delete_versions: bool = True,
                      delete_audit_logs: bool = False,
                      permanent: bool = False) -> None:
        """
        Delete evidence from a specific storage provider.

        Args:
            evidence_id: The ID of the evidence
            storage_id: The ID of the storage provider
            user_id: The ID of the user deleting the evidence
            delete_versions: Whether to delete all versions of the evidence
            delete_audit_logs: Whether to delete audit logs for the evidence
            permanent: Whether to permanently delete the evidence metadata

        Raises:
            ValueError: If the storage provider does not exist
            ValueError: If the evidence does not exist
            ValueError: If the user does not have permission to delete evidence
        """
        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        logger.info(f"Deleting evidence: {evidence_id} from storage provider: {storage_id}")

        # Delete the evidence from storage
        self.storage_manager.delete(
            provider_id=storage_id,
            evidence_id=evidence_id,
            user_id=user_id,
            delete_versions=delete_versions,
            delete_audit_logs=delete_audit_logs
        )

        # Update the evidence metadata
        if evidence_id in self.evidence_metadata:
            # Update the status to deleted
            self.evidence_metadata[evidence_id]['status'] = self.LIFECYCLE_STATES['DELETED']
            self.evidence_metadata[evidence_id]['updated_at'] = self._get_current_timestamp()
            self.evidence_metadata[evidence_id]['deleted_at'] = self._get_current_timestamp()
            self.evidence_metadata[evidence_id]['deleted_by'] = user_id

            # Save the updated metadata
            self._save_evidence_metadata(evidence_id)

            # Update workflow state if workflows are enabled
            if self.enable_workflows and evidence_id in self.evidence_workflows:
                workflow = self.evidence_workflows[evidence_id]

                # Update the workflow state to archived
                workflow['state'] = EvidenceWorkflowState.ARCHIVED.value
                workflow['updated_at'] = self._get_current_timestamp()
                workflow['updated_by'] = user_id
                workflow['history'].append({
                    'state': EvidenceWorkflowState.ARCHIVED.value,
                    'timestamp': self._get_current_timestamp(),
                    'user_id': user_id,
                    'comment': f"Evidence deleted from {storage_id}"
                })

                # Save the updated workflow state
                self._save_evidence_workflow(evidence_id)

            # Remove evidence relationships if relationships are enabled
            if self.enable_relationships and evidence_id in self.evidence_relationships:
                # Get all related evidence IDs
                related_evidence_ids = set()
                for relationship_type, relationships in self.evidence_relationships[evidence_id].items():
                    for relationship in relationships:
                        related_evidence_ids.add(relationship['evidence_id'])

                # Remove this evidence from the relationships of related evidence
                for related_id in related_evidence_ids:
                    if related_id in self.evidence_relationships:
                        for relationship_type, relationships in self.evidence_relationships[related_id].items():
                            # Filter out relationships to this evidence
                            self.evidence_relationships[related_id][relationship_type] = [
                                r for r in relationships if r['evidence_id'] != evidence_id
                            ]

                        # Save the updated relationships
                        self._save_evidence_relationships(related_id)

                # Delete the relationships for this evidence
                del self.evidence_relationships[evidence_id]

                # Delete the relationships file
                relationships_file = os.path.join(self.relationships_dir, f"{evidence_id}.json")
                if os.path.exists(relationships_file):
                    os.remove(relationships_file)

            # Permanently delete the evidence metadata if requested
            if permanent:
                # Remove the evidence from the metadata dictionary
                del self.evidence_metadata[evidence_id]

                # Remove the evidence from the tags dictionary
                if evidence_id in self.evidence_tags:
                    del self.evidence_tags[evidence_id]

                # Remove the evidence from the category dictionary
                for category, evidence_ids in self.evidence_by_category.items():
                    evidence_ids.discard(evidence_id)

                # Remove the evidence from the requirement dictionary
                for requirement_id, evidence_ids in self.evidence_by_requirement.items():
                    evidence_ids.discard(evidence_id)

                # Delete the metadata file
                metadata_file = os.path.join(self.evidence_dir, f"{evidence_id}.json")
                if os.path.exists(metadata_file):
                    os.remove(metadata_file)

            # Call registered evidence handlers
            evidence_type = self.evidence_metadata[evidence_id].get('type')
            if evidence_type in self.evidence_handlers:
                for handler in self.evidence_handlers[evidence_type]:
                    try:
                        handler(self.evidence_metadata[evidence_id])
                    except Exception as e:
                        logger.error(f"Error in evidence handler for {evidence_type}: {e}")

        logger.info(f"Evidence deleted: {evidence_id}")

        # Send a notification about the deletion
        self.notification_manager.send_notification(
            message=f"Evidence deleted: {evidence_id}",
            notification_type=NotificationType.WARNING,
            subject="Evidence Deletion",
            details={
                'evidence_id': evidence_id,
                'storage_id': storage_id,
                'user_id': user_id,
                'permanent': permanent,
                'delete_versions': delete_versions,
                'delete_audit_logs': delete_audit_logs
            }
        )

    def add_tags(self, evidence_id: str, tags: List[str]) -> Dict[str, Any]:
        """
        Add tags to evidence.

        Args:
            evidence_id: The ID of the evidence
            tags: List of tags to add

        Returns:
            The updated evidence

        Raises:
            ValueError: If the evidence does not exist
        """
        logger.info(f"Adding tags to evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Get the evidence metadata
        evidence = self.evidence_metadata[evidence_id]

        # Add the tags
        if 'tags' not in evidence:
            evidence['tags'] = []

        # Add new tags that don't already exist
        for tag in tags:
            if tag not in evidence['tags']:
                evidence['tags'].append(tag)

        # Update the evidence tags dictionary
        self.evidence_tags[evidence_id] = set(evidence['tags'])

        # Update the timestamp
        evidence['updated_at'] = self._get_current_timestamp()

        # Save the updated metadata
        self._save_evidence_metadata(evidence_id)

        logger.info(f"Tags added to evidence: {evidence_id}")

        return evidence

    def remove_tags(self, evidence_id: str, tags: List[str]) -> Dict[str, Any]:
        """
        Remove tags from evidence.

        Args:
            evidence_id: The ID of the evidence
            tags: List of tags to remove

        Returns:
            The updated evidence

        Raises:
            ValueError: If the evidence does not exist
        """
        logger.info(f"Removing tags from evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Get the evidence metadata
        evidence = self.evidence_metadata[evidence_id]

        # Remove the tags
        if 'tags' in evidence:
            evidence['tags'] = [tag for tag in evidence['tags'] if tag not in tags]

        # Update the evidence tags dictionary
        self.evidence_tags[evidence_id] = set(evidence.get('tags', []))

        # Update the timestamp
        evidence['updated_at'] = self._get_current_timestamp()

        # Save the updated metadata
        self._save_evidence_metadata(evidence_id)

        logger.info(f"Tags removed from evidence: {evidence_id}")

        return evidence

    def set_category(self, evidence_id: str, category: str) -> Dict[str, Any]:
        """
        Set the category of evidence.

        Args:
            evidence_id: The ID of the evidence
            category: The category to set

        Returns:
            The updated evidence

        Raises:
            ValueError: If the evidence does not exist
        """
        logger.info(f"Setting category for evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Get the evidence metadata
        evidence = self.evidence_metadata[evidence_id]

        # Remove the evidence from the old category
        old_category = evidence.get('category')
        if old_category and old_category in self.evidence_by_category:
            self.evidence_by_category[old_category].discard(evidence_id)

        # Set the new category
        evidence['category'] = category

        # Add the evidence to the new category
        if category not in self.evidence_by_category:
            self.evidence_by_category[category] = set()
        self.evidence_by_category[category].add(evidence_id)

        # Update the timestamp
        evidence['updated_at'] = self._get_current_timestamp()

        # Save the updated metadata
        self._save_evidence_metadata(evidence_id)

        logger.info(f"Category set for evidence: {evidence_id}")

        return evidence

    def set_expiry_date(self, evidence_id: str, expiry_days: int) -> Dict[str, Any]:
        """
        Set the expiry date of evidence.

        Args:
            evidence_id: The ID of the evidence
            expiry_days: Number of days until the evidence expires

        Returns:
            The updated evidence

        Raises:
            ValueError: If the evidence does not exist
        """
        logger.info(f"Setting expiry date for evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Get the evidence metadata
        evidence = self.evidence_metadata[evidence_id]

        # Calculate the expiry date
        current_timestamp = self._get_current_timestamp()
        expiry_date = self._calculate_expiry_date(current_timestamp, expiry_days)

        # Set the expiry date
        evidence['expiry_date'] = expiry_date

        # Update the timestamp
        evidence['updated_at'] = current_timestamp

        # Save the updated metadata
        self._save_evidence_metadata(evidence_id)

        logger.info(f"Expiry date set for evidence: {evidence_id}")

        return evidence

    def archive_evidence(self, evidence_id: str) -> Dict[str, Any]:
        """
        Archive evidence.

        Args:
            evidence_id: The ID of the evidence

        Returns:
            The updated evidence

        Raises:
            ValueError: If the evidence does not exist
        """
        logger.info(f"Archiving evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Get the evidence metadata
        evidence = self.evidence_metadata[evidence_id]

        # Set the status to archived
        evidence['status'] = self.LIFECYCLE_STATES['ARCHIVED']

        # Update the timestamp
        evidence['updated_at'] = self._get_current_timestamp()

        # Save the updated metadata
        self._save_evidence_metadata(evidence_id)

        # Call registered evidence handlers
        evidence_type = evidence.get('type')
        if evidence_type in self.evidence_handlers:
            for handler in self.evidence_handlers[evidence_type]:
                try:
                    handler(evidence)
                except Exception as e:
                    logger.error(f"Error in evidence handler for {evidence_type}: {e}")

        logger.info(f"Evidence archived: {evidence_id}")

        return evidence

    def get_collector_types(self) -> List[str]:
        """
        Get all supported collector types.

        Returns:
            List of collector types
        """
        return self.collector_manager.get_collector_types()

    def get_validator_types(self) -> List[str]:
        """
        Get all supported validator types.

        Returns:
            List of validator types
        """
        return self.validator_manager.get_validator_types()

    def get_storage_types(self) -> List[str]:
        """
        Get all supported storage types.

        Returns:
            List of storage types
        """
        return self.storage_manager.get_storage_types()

    # Evidence relationship methods

    def add_evidence_relationship(self,
                                evidence_id: str,
                                related_evidence_id: str,
                                relationship_type: EvidenceRelationshipType,
                                user_id: str = None,
                                description: str = None) -> Dict[str, Any]:
        """
        Add a relationship between two evidence items.

        Args:
            evidence_id: The ID of the evidence
            related_evidence_id: The ID of the related evidence
            relationship_type: The type of relationship
            user_id: The ID of the user adding the relationship
            description: A description of the relationship

        Returns:
            The updated evidence relationships

        Raises:
            ValueError: If either evidence does not exist
            ValueError: If relationships are not enabled
        """
        if not self.enable_relationships:
            raise ValueError("Evidence relationships are not enabled")

        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        logger.info(f"Adding relationship between evidence {evidence_id} and {related_evidence_id}")

        # Check if both evidence items exist
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        if related_evidence_id not in self.evidence_metadata:
            raise ValueError(f"Related evidence not found: {related_evidence_id}")

        # Initialize the relationships dictionary for the evidence if it doesn't exist
        if evidence_id not in self.evidence_relationships:
            self.evidence_relationships[evidence_id] = {}

        # Initialize the relationship type list if it doesn't exist
        if relationship_type.value not in self.evidence_relationships[evidence_id]:
            self.evidence_relationships[evidence_id][relationship_type.value] = []

        # Check if the relationship already exists
        for relationship in self.evidence_relationships[evidence_id][relationship_type.value]:
            if relationship['evidence_id'] == related_evidence_id:
                # Update the existing relationship
                relationship['updated_at'] = self._get_current_timestamp()
                relationship['updated_by'] = user_id
                if description is not None:
                    relationship['description'] = description

                # Save the updated relationships
                self._save_evidence_relationships(evidence_id)

                logger.info(f"Updated relationship between evidence {evidence_id} and {related_evidence_id}")

                return self.evidence_relationships[evidence_id]

        # Create the relationship
        relationship = {
            'evidence_id': related_evidence_id,
            'created_at': self._get_current_timestamp(),
            'created_by': user_id,
            'updated_at': self._get_current_timestamp(),
            'updated_by': user_id,
            'description': description
        }

        # Add the relationship
        self.evidence_relationships[evidence_id][relationship_type.value].append(relationship)

        # Save the updated relationships
        self._save_evidence_relationships(evidence_id)

        # Add the inverse relationship if appropriate
        inverse_type = None
        if relationship_type == EvidenceRelationshipType.PARENT:
            inverse_type = EvidenceRelationshipType.CHILD
        elif relationship_type == EvidenceRelationshipType.CHILD:
            inverse_type = EvidenceRelationshipType.PARENT
        elif relationship_type == EvidenceRelationshipType.SUPERSEDES:
            inverse_type = EvidenceRelationshipType.SUPERSEDED_BY
        elif relationship_type == EvidenceRelationshipType.SUPERSEDED_BY:
            inverse_type = EvidenceRelationshipType.SUPERSEDES
        elif relationship_type == EvidenceRelationshipType.DERIVED_FROM:
            # No inverse for DERIVED_FROM
            pass
        elif relationship_type == EvidenceRelationshipType.SUPPORTS:
            # No inverse for SUPPORTS
            pass
        elif relationship_type == EvidenceRelationshipType.CONTRADICTS:
            # CONTRADICTS is its own inverse
            inverse_type = EvidenceRelationshipType.CONTRADICTS
        elif relationship_type == EvidenceRelationshipType.RELATED:
            # RELATED is its own inverse
            inverse_type = EvidenceRelationshipType.RELATED

        if inverse_type:
            # Initialize the relationships dictionary for the related evidence if it doesn't exist
            if related_evidence_id not in self.evidence_relationships:
                self.evidence_relationships[related_evidence_id] = {}

            # Initialize the inverse relationship type list if it doesn't exist
            if inverse_type.value not in self.evidence_relationships[related_evidence_id]:
                self.evidence_relationships[related_evidence_id][inverse_type.value] = []

            # Check if the inverse relationship already exists
            for inverse_relationship in self.evidence_relationships[related_evidence_id][inverse_type.value]:
                if inverse_relationship['evidence_id'] == evidence_id:
                    # Update the existing inverse relationship
                    inverse_relationship['updated_at'] = self._get_current_timestamp()
                    inverse_relationship['updated_by'] = user_id
                    if description is not None:
                        inverse_relationship['description'] = description

                    # Save the updated relationships
                    self._save_evidence_relationships(related_evidence_id)

                    logger.info(f"Updated inverse relationship between evidence {related_evidence_id} and {evidence_id}")

                    return self.evidence_relationships[evidence_id]

            # Create the inverse relationship
            inverse_relationship = {
                'evidence_id': evidence_id,
                'created_at': self._get_current_timestamp(),
                'created_by': user_id,
                'updated_at': self._get_current_timestamp(),
                'updated_by': user_id,
                'description': description
            }

            # Add the inverse relationship
            self.evidence_relationships[related_evidence_id][inverse_type.value].append(inverse_relationship)

            # Save the updated relationships
            self._save_evidence_relationships(related_evidence_id)

        logger.info(f"Added relationship between evidence {evidence_id} and {related_evidence_id}")

        return self.evidence_relationships[evidence_id]

    def remove_evidence_relationship(self,
                                   evidence_id: str,
                                   related_evidence_id: str,
                                   relationship_type: EvidenceRelationshipType,
                                   user_id: str = None) -> Dict[str, Any]:
        """
        Remove a relationship between two evidence items.

        Args:
            evidence_id: The ID of the evidence
            related_evidence_id: The ID of the related evidence
            relationship_type: The type of relationship
            user_id: The ID of the user removing the relationship

        Returns:
            The updated evidence relationships

        Raises:
            ValueError: If either evidence does not exist
            ValueError: If the relationship does not exist
            ValueError: If relationships are not enabled
        """
        if not self.enable_relationships:
            raise ValueError("Evidence relationships are not enabled")

        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        logger.info(f"Removing relationship between evidence {evidence_id} and {related_evidence_id}")

        # Check if both evidence items exist
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        if related_evidence_id not in self.evidence_metadata:
            raise ValueError(f"Related evidence not found: {related_evidence_id}")

        # Check if the relationship exists
        if (evidence_id not in self.evidence_relationships or
            relationship_type.value not in self.evidence_relationships[evidence_id]):
            raise ValueError(f"Relationship not found: {relationship_type.value}")

        # Find and remove the relationship
        relationships = self.evidence_relationships[evidence_id][relationship_type.value]
        for i, relationship in enumerate(relationships):
            if relationship['evidence_id'] == related_evidence_id:
                # Remove the relationship
                del relationships[i]

                # Save the updated relationships
                self._save_evidence_relationships(evidence_id)

                # Remove the inverse relationship if appropriate
                inverse_type = None
                if relationship_type == EvidenceRelationshipType.PARENT:
                    inverse_type = EvidenceRelationshipType.CHILD
                elif relationship_type == EvidenceRelationshipType.CHILD:
                    inverse_type = EvidenceRelationshipType.PARENT
                elif relationship_type == EvidenceRelationshipType.SUPERSEDES:
                    inverse_type = EvidenceRelationshipType.SUPERSEDED_BY
                elif relationship_type == EvidenceRelationshipType.SUPERSEDED_BY:
                    inverse_type = EvidenceRelationshipType.SUPERSEDES
                elif relationship_type == EvidenceRelationshipType.DERIVED_FROM:
                    # No inverse for DERIVED_FROM
                    pass
                elif relationship_type == EvidenceRelationshipType.SUPPORTS:
                    # No inverse for SUPPORTS
                    pass
                elif relationship_type == EvidenceRelationshipType.CONTRADICTS:
                    # CONTRADICTS is its own inverse
                    inverse_type = EvidenceRelationshipType.CONTRADICTS
                elif relationship_type == EvidenceRelationshipType.RELATED:
                    # RELATED is its own inverse
                    inverse_type = EvidenceRelationshipType.RELATED

                if inverse_type and related_evidence_id in self.evidence_relationships:
                    if inverse_type.value in self.evidence_relationships[related_evidence_id]:
                        # Find and remove the inverse relationship
                        inverse_relationships = self.evidence_relationships[related_evidence_id][inverse_type.value]
                        for j, inverse_relationship in enumerate(inverse_relationships):
                            if inverse_relationship['evidence_id'] == evidence_id:
                                # Remove the inverse relationship
                                del inverse_relationships[j]

                                # Save the updated relationships
                                self._save_evidence_relationships(related_evidence_id)

                                break

                logger.info(f"Removed relationship between evidence {evidence_id} and {related_evidence_id}")

                return self.evidence_relationships[evidence_id]

        # If we get here, the relationship was not found
        raise ValueError(f"Relationship not found: {relationship_type.value} to {related_evidence_id}")

    def get_evidence_relationships(self, evidence_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get all relationships for an evidence item.

        Args:
            evidence_id: The ID of the evidence

        Returns:
            Dictionary of relationships by type

        Raises:
            ValueError: If the evidence does not exist
            ValueError: If relationships are not enabled
        """
        if not self.enable_relationships:
            raise ValueError("Evidence relationships are not enabled")

        logger.info(f"Getting relationships for evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Return the relationships
        return self.evidence_relationships.get(evidence_id, {})

    def get_related_evidence(self,
                           evidence_id: str,
                           relationship_type: Optional[EvidenceRelationshipType] = None) -> List[Dict[str, Any]]:
        """
        Get all evidence related to an evidence item.

        Args:
            evidence_id: The ID of the evidence
            relationship_type: The type of relationship to filter by

        Returns:
            List of related evidence items

        Raises:
            ValueError: If the evidence does not exist
            ValueError: If relationships are not enabled
        """
        if not self.enable_relationships:
            raise ValueError("Evidence relationships are not enabled")

        logger.info(f"Getting related evidence for: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Get the relationships
        relationships = self.evidence_relationships.get(evidence_id, {})

        # Filter by relationship type if provided
        if relationship_type:
            relationships = {relationship_type.value: relationships.get(relationship_type.value, [])}

        # Get the related evidence
        related_evidence = []
        for relationship_type, type_relationships in relationships.items():
            for relationship in type_relationships:
                related_id = relationship['evidence_id']
                if related_id in self.evidence_metadata:
                    related_evidence.append({
                        'evidence': self.evidence_metadata[related_id],
                        'relationship_type': relationship_type,
                        'relationship': relationship
                    })

        return related_evidence

    # Evidence workflow methods

    def create_evidence_workflow(self,
                               evidence_id: str,
                               initial_state: EvidenceWorkflowState = EvidenceWorkflowState.DRAFT,
                               user_id: str = None,
                               comment: str = None) -> Dict[str, Any]:
        """
        Create a workflow for an evidence item.

        Args:
            evidence_id: The ID of the evidence
            initial_state: The initial state of the workflow
            user_id: The ID of the user creating the workflow
            comment: A comment about the workflow creation

        Returns:
            The created workflow

        Raises:
            ValueError: If the evidence does not exist
            ValueError: If a workflow already exists for the evidence
            ValueError: If workflows are not enabled
        """
        if not self.enable_workflows:
            raise ValueError("Evidence workflows are not enabled")

        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        logger.info(f"Creating workflow for evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Check if a workflow already exists
        if evidence_id in self.evidence_workflows:
            raise ValueError(f"Workflow already exists for evidence: {evidence_id}")

        # Create the workflow
        timestamp = self._get_current_timestamp()
        workflow = {
            'evidence_id': evidence_id,
            'state': initial_state.value,
            'created_at': timestamp,
            'created_by': user_id,
            'updated_at': timestamp,
            'updated_by': user_id,
            'history': [
                {
                    'state': initial_state.value,
                    'timestamp': timestamp,
                    'user_id': user_id,
                    'comment': comment
                }
            ]
        }

        # Add the workflow
        self.evidence_workflows[evidence_id] = workflow

        # Save the workflow
        self._save_evidence_workflow(evidence_id)

        logger.info(f"Created workflow for evidence: {evidence_id}")

        return workflow

    def update_evidence_workflow_state(self,
                                     evidence_id: str,
                                     new_state: EvidenceWorkflowState,
                                     user_id: str = None,
                                     comment: str = None) -> Dict[str, Any]:
        """
        Update the state of an evidence workflow.

        Args:
            evidence_id: The ID of the evidence
            new_state: The new state of the workflow
            user_id: The ID of the user updating the workflow
            comment: A comment about the state change

        Returns:
            The updated workflow

        Raises:
            ValueError: If the evidence does not exist
            ValueError: If no workflow exists for the evidence
            ValueError: If workflows are not enabled
        """
        if not self.enable_workflows:
            raise ValueError("Evidence workflows are not enabled")

        # Use the current user ID if not provided
        user_id = user_id or self.current_user_id

        logger.info(f"Updating workflow state for evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Check if a workflow exists
        if evidence_id not in self.evidence_workflows:
            raise ValueError(f"No workflow exists for evidence: {evidence_id}")

        # Get the workflow
        workflow = self.evidence_workflows[evidence_id]

        # Update the workflow state
        old_state = workflow['state']
        workflow['state'] = new_state.value
        workflow['updated_at'] = self._get_current_timestamp()
        workflow['updated_by'] = user_id

        # Add to the history
        workflow['history'].append({
            'state': new_state.value,
            'timestamp': self._get_current_timestamp(),
            'user_id': user_id,
            'comment': comment,
            'previous_state': old_state
        })

        # Save the workflow
        self._save_evidence_workflow(evidence_id)

        logger.info(f"Updated workflow state for evidence: {evidence_id}")

        return workflow

    def get_evidence_workflow(self, evidence_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the workflow for an evidence item.

        Args:
            evidence_id: The ID of the evidence

        Returns:
            The workflow, or None if no workflow exists

        Raises:
            ValueError: If the evidence does not exist
            ValueError: If workflows are not enabled
        """
        if not self.enable_workflows:
            raise ValueError("Evidence workflows are not enabled")

        logger.info(f"Getting workflow for evidence: {evidence_id}")

        # Check if the evidence exists
        if evidence_id not in self.evidence_metadata:
            raise ValueError(f"Evidence not found: {evidence_id}")

        # Return the workflow
        return self.evidence_workflows.get(evidence_id)

    # Report generation methods

    def generate_evidence_report(self,
                               format: str = 'json',
                               filters: Optional[Dict[str, Any]] = None,
                               output_file: Optional[str] = None) -> str:
        """
        Generate a report of evidence items.

        Args:
            format: Output format ('json' or 'html')
            filters: Filters to apply to the evidence
            output_file: Path to the output file

        Returns:
            Path to the generated report

        Raises:
            ValueError: If the format is not supported
        """
        logger.info(f"Generating evidence report in {format} format")

        # Generate the report
        report_path = self.report_manager.generate_evidence_report(
            evidence_metadata=self.evidence_metadata,
            evidence_tags=self.evidence_tags,
            evidence_by_category=self.evidence_by_category,
            format=format,
            filters=filters,
            output_file=output_file
        )

        logger.info(f"Evidence report generated: {report_path}")

        return report_path

    def generate_compliance_report(self,
                                 requirements: Dict[str, Dict[str, Any]],
                                 format: str = 'json',
                                 output_file: Optional[str] = None) -> str:
        """
        Generate a compliance summary report.

        Args:
            requirements: Dictionary of compliance requirements
            format: Output format ('json' or 'html')
            output_file: Path to the output file

        Returns:
            Path to the generated report

        Raises:
            ValueError: If the format is not supported
        """
        logger.info(f"Generating compliance summary report in {format} format")

        # Generate the report
        report_path = self.report_manager.generate_compliance_summary_report(
            evidence_metadata=self.evidence_metadata,
            requirements=requirements,
            evidence_by_requirement=self.evidence_by_requirement,
            format=format,
            output_file=output_file
        )

        logger.info(f"Compliance summary report generated: {report_path}")

        return report_path

    # Search and query methods

    def search_evidence(self,
                      query: Dict[str, Any],
                      page: int = 1,
                      page_size: int = 10) -> Dict[str, Any]:
        """
        Search for evidence based on a query.

        Args:
            query: The search query
            page: The page number (1-based)
            page_size: The number of results per page

        Returns:
            Dictionary containing search results and pagination information
        """
        logger.info(f"Searching evidence with query: {query}")

        # Use the query manager to search for evidence
        results = self.query_manager.search_evidence(
            evidence_metadata=self.evidence_metadata,
            evidence_tags=self.evidence_tags,
            evidence_by_category=self.evidence_by_category,
            evidence_by_requirement=self.evidence_by_requirement,
            query=query,
            page=page,
            page_size=page_size
        )

        # Send a notification about the search
        self.notification_manager.send_notification(
            message=f"Evidence search performed: {results['pagination']['total_results']} results",
            notification_type=NotificationType.INFO,
            subject="Evidence Search",
            details={
                'query': query,
                'total_results': results['pagination']['total_results'],
                'page': page,
                'page_size': page_size
            }
        )

        return results

    def search_requirements(self,
                          requirements: Dict[str, Dict[str, Any]],
                          query: Dict[str, Any],
                          page: int = 1,
                          page_size: int = 10) -> Dict[str, Any]:
        """
        Search for requirements based on a query.

        Args:
            requirements: Dictionary of compliance requirements
            query: The search query
            page: The page number (1-based)
            page_size: The number of results per page

        Returns:
            Dictionary containing search results and pagination information
        """
        logger.info(f"Searching requirements with query: {query}")

        # Use the query manager to search for requirements
        results = self.query_manager.search_requirements(
            requirements=requirements,
            evidence_by_requirement=self.evidence_by_requirement,
            query=query,
            page=page,
            page_size=page_size
        )

        # Send a notification about the search
        self.notification_manager.send_notification(
            message=f"Requirements search performed: {results['pagination']['total_results']} results",
            notification_type=NotificationType.INFO,
            subject="Requirements Search",
            details={
                'query': query,
                'total_results': results['pagination']['total_results'],
                'page': page,
                'page_size': page_size
            }
        )

        return results

    def build_query(self,
                  field: Union[str, QueryField],
                  value: Any) -> Dict[str, Any]:
        """
        Build a simple query for a field.

        Args:
            field: The field to query
            value: The value to match

        Returns:
            A query dictionary
        """
        return self.query_manager.build_query(field, value)

    def build_compound_query(self,
                           operator: Union[str, QueryOperator],
                           conditions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Build a compound query with multiple conditions.

        Args:
            operator: The operator to use (AND, OR, NOT)
            conditions: The conditions to combine

        Returns:
            A compound query dictionary
        """
        return self.query_manager.build_compound_query(operator, conditions)

    # Legacy search methods (for backward compatibility)

    def search_evidence_legacy(self,
                       category: Optional[str] = None,
                       tags: Optional[List[str]] = None,
                       requirement_id: Optional[str] = None,
                       status: Optional[str] = None,
                       collector_id: Optional[str] = None,
                       date_range: Optional[Dict[str, str]] = None) -> List[Dict[str, Any]]:
        """
        Legacy method to search for evidence based on various criteria.

        This method is maintained for backward compatibility.
        New code should use the more powerful search_evidence method.

        Args:
            category: Filter by category
            tags: Filter by tags (evidence must have all specified tags)
            requirement_id: Filter by requirement ID
            status: Filter by status
            collector_id: Filter by collector ID
            date_range: Filter by date range (created_at)
                Example: {'start': '2023-01-01T00:00:00Z', 'end': '2023-12-31T23:59:59Z'}

        Returns:
            List of evidence matching the criteria
        """
        logger.info("Using legacy search method")

        # Build a query using the new query system
        conditions = []

        if category:
            conditions.append(self.build_query(QueryField.CATEGORY, category))

        if tags:
            for tag in tags:
                conditions.append(self.build_query(QueryField.TAGS, tag))

        if requirement_id:
            conditions.append(self.build_query(QueryField.REQUIREMENT, requirement_id))

        if status:
            conditions.append(self.build_query(QueryField.STATUS, status))

        if collector_id:
            conditions.append(self.build_query('collector_id', collector_id))

        if date_range:
            conditions.append(self.build_query(QueryField.CREATED_AT, date_range))

        # If there are no conditions, return all evidence
        if not conditions:
            return list(self.evidence_metadata.values())

        # If there's only one condition, use it directly
        if len(conditions) == 1:
            query = conditions[0]
        else:
            # Otherwise, combine conditions with AND
            query = self.build_compound_query(QueryOperator.AND, conditions)

        # Execute the query
        results = self.search_evidence(query=query, page=1, page_size=1000)

        # Return the results as a list
        return results['results']

    def get_evidence_by_id(self, evidence_id: str) -> Optional[Dict[str, Any]]:
        """
        Get evidence by ID.

        Args:
            evidence_id: The ID of the evidence

        Returns:
            The evidence if found, None otherwise
        """
        return self.evidence_metadata.get(evidence_id)

    def get_evidence_by_tag(self, tag: str) -> List[Dict[str, Any]]:
        """
        Get evidence by tag.

        Args:
            tag: The tag to search for

        Returns:
            List of evidence with the specified tag
        """
        # Find evidence IDs with the tag
        evidence_ids = {evidence_id for evidence_id, tags in self.evidence_tags.items() if tag in tags}

        # Get the evidence objects
        return [self.evidence_metadata[evidence_id] for evidence_id in evidence_ids if evidence_id in self.evidence_metadata]

    def get_evidence_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Get evidence by category.

        Args:
            category: The category to search for

        Returns:
            List of evidence with the specified category
        """
        # Find evidence IDs with the category
        evidence_ids = self.evidence_by_category.get(category, set())

        # Get the evidence objects
        return [self.evidence_metadata[evidence_id] for evidence_id in evidence_ids if evidence_id in self.evidence_metadata]

    def get_evidence_by_requirement(self, requirement_id: str) -> List[Dict[str, Any]]:
        """
        Get evidence by requirement ID.

        Args:
            requirement_id: The requirement ID to search for

        Returns:
            List of evidence associated with the specified requirement
        """
        # Find evidence IDs with the requirement
        evidence_ids = self.evidence_by_requirement.get(requirement_id, set())

        # Get the evidence objects
        return [self.evidence_metadata[evidence_id] for evidence_id in evidence_ids if evidence_id in self.evidence_metadata]

    def get_evidence_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Get evidence by status.

        Args:
            status: The status to search for

        Returns:
            List of evidence with the specified status
        """
        # Find evidence with the status
        return [evidence for evidence in self.evidence_metadata.values() if evidence.get('status') == status]

    def get_expired_evidence(self) -> List[Dict[str, Any]]:
        """
        Get all expired evidence.

        Returns:
            List of expired evidence
        """
        return self.get_evidence_by_status(self.LIFECYCLE_STATES['EXPIRED'])

    def _load_evidence_metadata(self) -> None:
        """Load evidence metadata from disk."""
        try:
            # Get all JSON files in the evidence directory
            metadata_files = [f for f in os.listdir(self.evidence_dir) if f.endswith('.json')]

            for metadata_file in metadata_files:
                try:
                    # Load the metadata
                    file_path = os.path.join(self.evidence_dir, metadata_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)

                    # Get the evidence ID from the filename (without extension)
                    evidence_id = os.path.splitext(metadata_file)[0]

                    # Add the metadata to the in-memory dictionary
                    self.evidence_metadata[evidence_id] = metadata

                    # Add the evidence to the tags dictionary
                    if 'tags' in metadata:
                        self.evidence_tags[evidence_id] = set(metadata['tags'])

                    # Add the evidence to the category dictionary
                    if 'category' in metadata:
                        category = metadata['category']
                        if category not in self.evidence_by_category:
                            self.evidence_by_category[category] = set()
                        self.evidence_by_category[category].add(evidence_id)

                    # Add the evidence to the requirement dictionary
                    if 'requirements' in metadata:
                        for requirement_id in metadata['requirements']:
                            if requirement_id not in self.evidence_by_requirement:
                                self.evidence_by_requirement[requirement_id] = set()
                            self.evidence_by_requirement[requirement_id].add(evidence_id)

                    logger.info(f"Loaded evidence metadata for evidence: {evidence_id}")

                except Exception as e:
                    logger.error(f"Failed to load evidence metadata from {metadata_file}: {e}")

            logger.info(f"Loaded metadata for {len(self.evidence_metadata)} evidence items")

        except Exception as e:
            logger.error(f"Failed to load evidence metadata from directory {self.evidence_dir}: {e}")

    def _save_evidence_metadata(self, evidence_id: str) -> None:
        """
        Save evidence metadata to disk.

        Args:
            evidence_id: The ID of the evidence
        """
        try:
            # Get the metadata
            metadata = self.evidence_metadata.get(evidence_id)

            if not metadata:
                logger.warning(f"No metadata found for evidence: {evidence_id}")
                return

            # Save the metadata to disk
            file_path = os.path.join(self.evidence_dir, f"{evidence_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2)

            logger.info(f"Saved metadata for evidence: {evidence_id}")

        except Exception as e:
            logger.error(f"Failed to save metadata for evidence {evidence_id}: {e}")

    def _check_expired_evidence(self) -> None:
        """Check for expired evidence and update their status."""
        current_time = datetime.datetime.now(datetime.timezone.utc)

        for evidence_id, metadata in self.evidence_metadata.items():
            # Skip evidence that is already expired or deleted
            if metadata.get('status') in [self.LIFECYCLE_STATES['EXPIRED'], self.LIFECYCLE_STATES['DELETED']]:
                continue

            # Check if the evidence has an expiry date
            expiry_date_str = metadata.get('expiry_date')
            if not expiry_date_str:
                continue

            try:
                # Parse the expiry date
                expiry_date = datetime.datetime.fromisoformat(expiry_date_str.rstrip('Z'))
                # Make the expiry date timezone-aware if it's not already
                if expiry_date.tzinfo is None:
                    expiry_date = expiry_date.replace(tzinfo=datetime.timezone.utc)

                # Check if the evidence has expired
                if current_time > expiry_date:
                    # Update the status to expired
                    metadata['status'] = self.LIFECYCLE_STATES['EXPIRED']
                    metadata['updated_at'] = self._get_current_timestamp()

                    # Save the updated metadata
                    self._save_evidence_metadata(evidence_id)

                    logger.info(f"Evidence {evidence_id} has expired")

                    # Call registered evidence handlers
                    evidence_type = metadata.get('type')
                    if evidence_type in self.evidence_handlers:
                        for handler in self.evidence_handlers[evidence_type]:
                            try:
                                handler(metadata)
                            except Exception as e:
                                logger.error(f"Error in evidence handler for {evidence_type}: {e}")

            except Exception as e:
                logger.error(f"Error checking expiry for evidence {evidence_id}: {e}")

    def _calculate_expiry_date(self, collection_date: str, expiry_days: Optional[int] = None) -> str:
        """
        Calculate the expiry date for evidence.

        Args:
            collection_date: The collection date in ISO 8601 format
            expiry_days: The number of days until expiry (defaults to self.default_expiry_days)

        Returns:
            The expiry date in ISO 8601 format
        """
        # Parse the collection date
        collection_datetime = datetime.datetime.fromisoformat(collection_date.rstrip('Z'))

        # Calculate the expiry date
        expiry_days = expiry_days or self.default_expiry_days
        expiry_datetime = collection_datetime + datetime.timedelta(days=expiry_days)

        # Return the expiry date in ISO 8601 format
        return expiry_datetime.isoformat() + 'Z'

    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.

        Returns:
            The current timestamp as a string
        """
        return datetime.datetime.now(datetime.timezone.utc).isoformat()

    def _load_evidence_relationships(self) -> None:
        """Load evidence relationships from disk."""
        try:
            # Get all JSON files in the relationships directory
            relationship_files = [f for f in os.listdir(self.relationships_dir) if f.endswith('.json')]

            for relationship_file in relationship_files:
                try:
                    # Get the evidence ID from the filename (without extension)
                    evidence_id = os.path.splitext(relationship_file)[0]

                    # Load the relationships
                    file_path = os.path.join(self.relationships_dir, relationship_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        relationships = json.load(f)

                    # Add the relationships to the in-memory dictionary
                    self.evidence_relationships[evidence_id] = relationships

                    logger.info(f"Loaded relationships for evidence: {evidence_id}")

                except Exception as e:
                    logger.error(f"Failed to load relationships from {relationship_file}: {e}")

            logger.info(f"Loaded relationships for {len(self.evidence_relationships)} evidence items")

        except Exception as e:
            logger.error(f"Failed to load relationships from directory {self.relationships_dir}: {e}")

    def _save_evidence_relationships(self, evidence_id: str) -> None:
        """
        Save evidence relationships to disk.

        Args:
            evidence_id: The ID of the evidence
        """
        try:
            # Get the relationships for the evidence
            relationships = self.evidence_relationships.get(evidence_id)

            if not relationships:
                logger.warning(f"No relationships found for evidence: {evidence_id}")
                return

            # Save the relationships to disk
            file_path = os.path.join(self.relationships_dir, f"{evidence_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(relationships, f, indent=2)

            logger.info(f"Saved relationships for evidence: {evidence_id}")

        except Exception as e:
            logger.error(f"Failed to save relationships for evidence {evidence_id}: {e}")

    def _load_evidence_workflows(self) -> None:
        """Load evidence workflow state from disk."""
        try:
            # Get all JSON files in the workflow directory
            workflow_files = [f for f in os.listdir(self.workflow_dir) if f.endswith('.json')]

            for workflow_file in workflow_files:
                try:
                    # Get the evidence ID from the filename (without extension)
                    evidence_id = os.path.splitext(workflow_file)[0]

                    # Load the workflow state
                    file_path = os.path.join(self.workflow_dir, workflow_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        workflow = json.load(f)

                    # Add the workflow state to the in-memory dictionary
                    self.evidence_workflows[evidence_id] = workflow

                    logger.info(f"Loaded workflow state for evidence: {evidence_id}")

                except Exception as e:
                    logger.error(f"Failed to load workflow state from {workflow_file}: {e}")

            logger.info(f"Loaded workflow state for {len(self.evidence_workflows)} evidence items")

        except Exception as e:
            logger.error(f"Failed to load workflow state from directory {self.workflow_dir}: {e}")

    def _save_evidence_workflow(self, evidence_id: str) -> None:
        """
        Save evidence workflow state to disk.

        Args:
            evidence_id: The ID of the evidence
        """
        try:
            # Get the workflow state for the evidence
            workflow = self.evidence_workflows.get(evidence_id)

            if not workflow:
                logger.warning(f"No workflow state found for evidence: {evidence_id}")
                return

            # Save the workflow state to disk
            file_path = os.path.join(self.workflow_dir, f"{evidence_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(workflow, f, indent=2)

            logger.info(f"Saved workflow state for evidence: {evidence_id}")

        except Exception as e:
            logger.error(f"Failed to save workflow state for evidence {evidence_id}: {e}")
