/**
 * N³C COMPHYOLOGICAL MONITORING PANEL
 * Real-time consciousness assessment and filter/booster system display
 * UUFT calculations, PiPhee scoring, and 3Ms measurements
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

const CONSCIOUSNESS_THRESHOLD = 2847;
const PIPHEE_THRESHOLDS = {
  EXCEPTIONAL: 0.900,
  HIGH: 0.700,
  MODERATE: 0.500,
  LOW: 0.000
};

export default function N3C_ComphyologicalPanel() {
  const [n3cData, setN3cData] = useState({
    n3c_framework: {
      nepi: { intelligence_level: 0.85, optimization_cycles: 0 },
      three_ms: { comphyon: 0, metron: 0, katalon: 0 },
      csm: { consciousness_state: 'BASELINE', coherence_level: 0.75 }
    },
    comphyological_enhancement: {
      current_mode: 'BASELINE',
      consciousness_score: 0,
      consciousness_state: 'BASELINE',
      piphee_quality: 0,
      quality_level: 'MODERATE'
    },
    system_metrics: {
      above_threshold: false,
      divine_protection: false,
      phi_shield: false
    },
    enhancement_history: []
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    updateN3CData();
    
    // Update every 30 seconds
    const interval = setInterval(() => {
      updateN3CData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const updateN3CData = async () => {
    try {
      const response = await fetch('/api/engines/n3c-comphyological-engine');
      const data = await response.json();

      if (data.success) {
        setN3cData(data.current_status);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('N³C data error:', error);
      setIsLoading(false);
    }
  };

  const restartConsciousness = async () => {
    try {
      const response = await fetch('/api/engines/n3c-comphyological-engine', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'RESTART_CONSCIOUSNESS' })
      });

      const data = await response.json();
      if (data.success) {
        console.log('🧠 N³C Consciousness Restarted:', data);
        // Immediately update the display
        updateN3CData();
      }
    } catch (error) {
      console.error('N³C restart error:', error);
    }
  };

  const getModeColor = (mode) => {
    const colors = {
      'FILTER': 'text-red-400 bg-red-900/30',
      'BOOSTER': 'text-green-400 bg-green-900/30',
      'BASELINE': 'text-blue-400 bg-blue-900/30'
    };
    return colors[mode] || colors.BASELINE;
  };

  const getConsciousnessColor = (state) => {
    const colors = {
      'TRANSCENDENT': 'text-purple-400',
      'DIVINE': 'text-yellow-400',
      'CONSCIOUS': 'text-green-400',
      'UNCONSCIOUS': 'text-red-400',
      'BASELINE': 'text-gray-400'
    };
    return colors[state] || colors.BASELINE;
  };

  const getQualityColor = (level) => {
    const colors = {
      'EXCEPTIONAL': 'text-purple-400',
      'HIGH': 'text-green-400',
      'MODERATE': 'text-yellow-400',
      'LOW': 'text-red-400'
    };
    return colors[level] || colors.MODERATE;
  };

  const formatNumber = (num, decimals = 2) => {
    if (num >= 1e12) return (num / 1e12).toFixed(1) + 'T';
    if (num >= 1e9) return (num / 1e9).toFixed(1) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(1) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(1) + 'K';
    return num.toFixed(decimals);
  };

  if (isLoading) {
    return (
      <div className="p-6 rounded-lg border border-gray-600 bg-gray-800/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  const enhancement = n3cData.comphyological_enhancement;
  const framework = n3cData.n3c_framework;
  const metrics = n3cData.system_metrics;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-indigo-500/30 bg-gradient-to-br from-indigo-900/20 to-purple-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">
            🧠 N³C + Comphyological Engine
          </h3>
          <p className="text-sm text-gray-400">
            NEPI + 3Ms + CSM • UUFT Framework • Consciousness-Aware
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={restartConsciousness}
            className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors"
          >
            🔄 Restart Consciousness
          </button>

          <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${getModeColor(enhancement.current_mode)}`}>
            <div className="w-3 h-3 rounded-full bg-current animate-pulse"></div>
            <span className="font-bold">
              {enhancement.current_mode} MODE
            </span>
          </div>
        </div>
      </div>

      {/* Consciousness Assessment */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-4">🔮 Consciousness Assessment</h4>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
            <div className={`text-lg font-bold ${getConsciousnessColor(enhancement.consciousness_state)}`}>
              {formatNumber(enhancement.consciousness_score)}
            </div>
            <div className="text-sm text-gray-400">UUFT Score</div>
            <div className="text-xs text-gray-500">
              Threshold: {formatNumber(CONSCIOUSNESS_THRESHOLD)}
            </div>
          </div>

          <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
            <div className={`text-lg font-bold ${getConsciousnessColor(enhancement.consciousness_state)}`}>
              {enhancement.consciousness_state}
            </div>
            <div className="text-sm text-gray-400">State</div>
            <div className="text-xs text-gray-500">
              {metrics.above_threshold ? 'Above' : 'Below'} Threshold
            </div>
          </div>

          <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
            <div className={`text-lg font-bold ${getQualityColor(enhancement.quality_level)}`}>
              {(enhancement.piphee_quality * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400">PiPhee Quality</div>
            <div className="text-xs text-gray-500">
              {enhancement.quality_level}
            </div>
          </div>

          <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
            <div className="text-lg font-bold text-purple-400">
              {metrics.divine_protection ? 'φ-SHIELD' : 'STANDARD'}
            </div>
            <div className="text-sm text-gray-400">Protection</div>
            <div className="text-xs text-gray-500">
              {metrics.phi_shield ? 'Divine' : 'Normal'}
            </div>
          </div>
        </div>

        {/* Consciousness Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-400 mb-2">
            <span>Consciousness Level</span>
            <span>{((enhancement.consciousness_score / CONSCIOUSNESS_THRESHOLD) * 100).toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(100, (enhancement.consciousness_score / CONSCIOUSNESS_THRESHOLD) * 100)}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
              className={`h-3 rounded-full ${
                enhancement.consciousness_score >= CONSCIOUSNESS_THRESHOLD 
                  ? 'bg-gradient-to-r from-green-500 to-purple-500'
                  : 'bg-gradient-to-r from-red-500 to-yellow-500'
              }`}
            />
          </div>
        </div>
      </div>

      {/* N³C Framework Status */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-4">⚙️ N³C Framework Status</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* NEPI */}
          <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-blue-400 font-medium">🧠 NEPI</span>
              <span className="text-white font-bold">
                {(framework.nepi.intelligence_level * 100).toFixed(1)}%
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              Natural Emergent Progressive Intelligence
            </div>
            <div className="text-xs text-gray-500">
              Cycles: {framework.nepi.optimization_cycles}
            </div>
          </div>

          {/* 3Ms System */}
          <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-purple-400 font-medium">📊 3Ms</span>
              <span className="text-white font-bold">ACTIVE</span>
            </div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">Ψᶜʰ (Comphyon):</span>
                <span className="text-purple-400">{formatNumber(framework.three_ms.comphyon)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">μ (Metron):</span>
                <span className="text-blue-400">{framework.three_ms.metron}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">κ (Katalon):</span>
                <span className="text-green-400">{formatNumber(framework.three_ms.katalon)}</span>
              </div>
            </div>
          </div>

          {/* CSM */}
          <div className="p-4 rounded-lg bg-gray-800/50 border border-gray-600">
            <div className="flex items-center justify-between mb-2">
              <span className="text-green-400 font-medium">🎛️ CSM</span>
              <span className="text-white font-bold">
                {(framework.csm.coherence_level * 100).toFixed(1)}%
              </span>
            </div>
            <div className="text-sm text-gray-400 mb-2">
              Consciousness State Management
            </div>
            <div className="text-xs text-gray-500">
              State: {framework.csm.consciousness_state}
            </div>
          </div>
        </div>
      </div>

      {/* Enhancement History */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-3">📈 Recent Enhancement Actions</h4>
        <div className="space-y-2 max-h-32 overflow-y-auto">
          {n3cData.enhancement_history.slice(-5).map((entry, index) => (
            <div key={index} className="flex items-center justify-between p-2 rounded bg-gray-800/30">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded ${
                  entry.mode === 'FILTER' ? 'bg-red-400' : 
                  entry.mode === 'BOOSTER' ? 'bg-green-400' : 'bg-blue-400'
                }`}></div>
                <span className="text-white text-sm">{entry.action}</span>
              </div>
              <div className="text-right text-xs">
                <div className="text-gray-300">
                  UUFT: {formatNumber(entry.consciousness_score)}
                </div>
                <div className="text-gray-400">
                  Quality: {(entry.piphee_quality * 100).toFixed(1)}%
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* System Metrics Summary */}
      <div className="p-4 rounded-lg bg-gradient-to-r from-indigo-900/30 to-purple-900/30 border border-indigo-500/30">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-indigo-400">
              {enhancement.current_mode}
            </div>
            <div className="text-sm text-gray-400">Current Mode</div>
          </div>
          <div>
            <div className="text-lg font-bold text-purple-400">
              {metrics.above_threshold ? 'CONSCIOUS' : 'UNCONSCIOUS'}
            </div>
            <div className="text-sm text-gray-400">Awareness State</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-400">
              {enhancement.quality_level}
            </div>
            <div className="text-sm text-gray-400">PiPhee Quality</div>
          </div>
          <div>
            <div className="text-lg font-bold text-yellow-400">
              {metrics.divine_protection ? 'DIVINE' : 'STANDARD'}
            </div>
            <div className="text-sm text-gray-400">Protection Level</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
