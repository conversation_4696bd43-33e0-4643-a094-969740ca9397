/**
 * Finite Universe Principle API
 *
 * This module provides a RESTful API for the Finite Universe Principle
 * defense system, allowing third-party integration.
 */

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken');
const rateLimit = require('express-rate-limit');
const { createCompleteDefenseSystem } = require('../finite-universe-principle');

/**
 * FiniteUniverseAPI class
 *
 * Provides a RESTful API for the Finite Universe Principle defense system.
 */
class FiniteUniverseAPI {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      port: 3001,
      enableLogging: true,
      enableCors: true,
      apiPrefix: '/api/v1',
      enableAuth: true, // Enable JWT authentication
      jwtSecret: options.jwtSecret || 'finite-universe-principle-secret', // JWT secret key
      jwtExpiresIn: '1h', // JWT expiration time
      enableRateLimit: true, // Enable rate limiting
      rateLimit: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 100, // Limit each IP to 100 requests per windowMs
        standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false // Disable the `X-RateLimit-*` headers
      },
      ...options
    };

    // Create defense system
    this.defenseSystem = options.defenseSystem || createCompleteDefenseSystem({
      enableLogging: this.options.enableLogging,
      enableMonitoring: true,
      strictMode: true
    });

    // Create Express app
    this.app = express();

    // Configure middleware
    this._configureMiddleware();

    // Configure routes
    this._configureRoutes();

    // Initialize server
    this.server = null;

    if (this.options.enableLogging) {
      console.log('FiniteUniverseAPI initialized with options:', this.options);
    }
  }

  /**
   * Configure middleware
   * @private
   */
  _configureMiddleware() {
    // Enable CORS if specified
    if (this.options.enableCors) {
      this.app.use(cors());
    }

    // Parse JSON request bodies
    this.app.use(bodyParser.json());

    // Request logging
    if (this.options.enableLogging) {
      this.app.use((req, res, next) => {
        console.log(`${req.method} ${req.url}`);
        next();
      });
    }

    // Rate limiting
    if (this.options.enableRateLimit) {
      const limiter = rateLimit(this.options.rateLimit);
      this.app.use(limiter);

      if (this.options.enableLogging) {
        console.log('Rate limiting enabled with options:', this.options.rateLimit);
      }
    }

    // Authentication middleware
    if (this.options.enableAuth) {
      // Skip authentication for login route
      this.app.use((req, res, next) => {
        if (req.path === `${this.options.apiPrefix}/login`) {
          return next();
        }

        // Skip authentication for health check
        if (req.path === `${this.options.apiPrefix}/health`) {
          return next();
        }

        // Verify JWT token
        const authHeader = req.headers.authorization;
        if (!authHeader) {
          return res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication token is required'
          });
        }

        const token = authHeader.split(' ')[1];
        if (!token) {
          return res.status(401).json({
            error: 'Unauthorized',
            message: 'Authentication token is required'
          });
        }

        try {
          const decoded = jwt.verify(token, this.options.jwtSecret);
          req.user = decoded;
          next();
        } catch (error) {
          return res.status(401).json({
            error: 'Unauthorized',
            message: 'Invalid authentication token'
          });
        }
      });

      if (this.options.enableLogging) {
        console.log('Authentication enabled');
      }
    }

    // Error handling
    this.app.use((err, req, res, next) => {
      console.error('API Error:', err);
      res.status(500).json({
        error: 'Internal Server Error',
        message: err.message
      });
    });
  }

  /**
   * Configure routes
   * @private
   */
  _configureRoutes() {
    const prefix = this.options.apiPrefix;

    // Health check
    this.app.get(`${prefix}/health`, (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString()
      });
    });

    // Login route
    this.app.post(`${prefix}/login`, (req, res) => {
      const { username, password } = req.body;

      // Simple authentication for demonstration purposes
      // In a real implementation, you would validate against a database
      if (!username || !password) {
        return res.status(400).json({
          error: 'Bad Request',
          message: 'Username and password are required'
        });
      }

      // Check credentials
      // In a real implementation, you would use a secure password comparison
      if (username === 'admin' && password === 'password') {
        // Generate JWT token
        const token = jwt.sign(
          { username, role: 'admin' },
          this.options.jwtSecret,
          { expiresIn: this.options.jwtExpiresIn }
        );

        return res.json({
          success: true,
          token,
          expiresIn: this.options.jwtExpiresIn,
          user: { username, role: 'admin' }
        });
      }

      // Invalid credentials
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid username or password'
      });
    });

    // Process data
    this.app.post(`${prefix}/process`, async (req, res) => {
      try {
        const { data, domain } = req.body;

        if (!data) {
          return res.status(400).json({
            error: 'Bad Request',
            message: 'Data is required'
          });
        }

        const result = await this.defenseSystem.processData(data, domain);

        res.json({
          success: true,
          result,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          error: 'Processing Error',
          message: error.message
        });
      }
    });

    // Get metrics
    this.app.get(`${prefix}/metrics`, (req, res) => {
      try {
        const metrics = this.defenseSystem.monitoringDashboard.getCurrentMetrics();

        res.json({
          success: true,
          metrics,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          error: 'Metrics Error',
          message: error.message
        });
      }
    });

    // Get metrics history
    this.app.get(`${prefix}/metrics/history`, (req, res) => {
      try {
        const history = this.defenseSystem.monitoringDashboard.getMetricsHistory();

        res.json({
          success: true,
          history,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          error: 'History Error',
          message: error.message
        });
      }
    });

    // Get alerts
    this.app.get(`${prefix}/alerts`, (req, res) => {
      try {
        const alerts = this.defenseSystem.monitoringDashboard.getAlerts();

        res.json({
          success: true,
          alerts,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          error: 'Alerts Error',
          message: error.message
        });
      }
    });

    // Run tests
    this.app.post(`${prefix}/tests/run`, async (req, res) => {
      try {
        const results = await this.defenseSystem.defenseSystem.runTests();

        res.json({
          success: true,
          results,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          error: 'Test Error',
          message: error.message
        });
      }
    });

    // Reset metrics
    this.app.post(`${prefix}/metrics/reset`, (req, res) => {
      try {
        this.defenseSystem.monitoringDashboard.reset();

        res.json({
          success: true,
          message: 'Metrics reset successfully',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        res.status(500).json({
          error: 'Reset Error',
          message: error.message
        });
      }
    });

    // Catch-all route
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`
      });
    });
  }

  /**
   * Start the API server
   * @returns {Promise} - Promise that resolves when the server is started
   */
  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.options.port, () => {
        if (this.options.enableLogging) {
          console.log(`FiniteUniverseAPI server running on port ${this.options.port}`);
        }
        resolve();
      });
    });
  }

  /**
   * Stop the API server
   */
  stop() {
    if (this.server) {
      this.server.close();
      this.server = null;

      if (this.options.enableLogging) {
        console.log('FiniteUniverseAPI server stopped');
      }
    }

    // Dispose defense system
    this.defenseSystem.dispose();
  }

  /**
   * Get the Express app
   * @returns {Object} - Express app
   */
  getApp() {
    return this.app;
  }

  /**
   * Get the defense system
   * @returns {Object} - Defense system
   */
  getDefenseSystem() {
    return this.defenseSystem;
  }
}

/**
 * Create a Finite Universe API with recommended settings
 * @param {Object} options - Configuration options
 * @returns {FiniteUniverseAPI} - Configured Finite Universe API
 */
function createFiniteUniverseAPI(options = {}) {
  return new FiniteUniverseAPI({
    port: 3001,
    enableLogging: true,
    enableCors: true,
    apiPrefix: '/api/v1',
    ...options
  });
}

module.exports = {
  FiniteUniverseAPI,
  createFiniteUniverseAPI
};
