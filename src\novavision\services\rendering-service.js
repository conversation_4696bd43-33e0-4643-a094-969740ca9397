/**
 * NovaVision - Rendering Service
 * 
 * This service provides UI rendering capabilities based on UI schemas.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('rendering-service');

/**
 * Render UI from schema
 * 
 * @param {Object} schema - UI schema
 * @param {Object} data - Data to render
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered UI
 */
function renderUiFromSchema(schema, data = {}, options = {}) {
  logger.debug('Rendering UI from schema', { 
    schemaType: schema.type,
    schemaId: schema.id
  });
  
  // Validate schema
  if (!schema || !schema.type) {
    throw new Error('Invalid schema: schema must have a type');
  }
  
  // Render based on schema type
  switch (schema.type) {
    case 'form':
      return renderForm(schema, data, options);
    case 'dashboard':
      return renderDashboard(schema, data, options);
    case 'report':
      return renderReport(schema, data, options);
    default:
      logger.warn('Unknown schema type', { schemaType: schema.type });
      throw new Error(`Unknown schema type: ${schema.type}`);
  }
}

/**
 * Render form
 * 
 * @param {Object} schema - Form schema
 * @param {Object} data - Form data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered form
 * @private
 */
function renderForm(schema, data = {}, options = {}) {
  logger.debug('Rendering form', { formId: schema.id });
  
  // Create base form structure
  const form = {
    id: schema.id,
    type: 'form',
    title: schema.title,
    description: schema.description,
    submitUrl: schema.submitUrl,
    method: schema.method || 'POST',
    sections: [],
    actions: [],
    data: data,
    metadata: {
      ...(schema.metadata || {}),
      renderedAt: new Date().toISOString(),
      renderer: 'NovaVision',
      rendererVersion: '1.0',
      theme: options.theme || 'default'
    }
  };
  
  // Render sections
  if (schema.sections && Array.isArray(schema.sections)) {
    form.sections = schema.sections.map(section => renderFormSection(section, data, options));
  }
  
  // Render actions
  if (schema.actions && Array.isArray(schema.actions)) {
    form.actions = schema.actions.map(action => renderFormAction(action, options));
  }
  
  return form;
}

/**
 * Render form section
 * 
 * @param {Object} section - Form section
 * @param {Object} data - Form data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered form section
 * @private
 */
function renderFormSection(section, data = {}, options = {}) {
  logger.debug('Rendering form section', { sectionId: section.id });
  
  // Create base section structure
  const renderedSection = {
    id: section.id,
    title: section.title,
    description: section.description,
    collapsible: section.collapsible || false,
    collapsed: section.collapsed || false,
    fields: []
  };
  
  // Render fields
  if (section.fields && Array.isArray(section.fields)) {
    renderedSection.fields = section.fields.map(field => renderFormField(field, data, options));
  }
  
  return renderedSection;
}

/**
 * Render form field
 * 
 * @param {Object} field - Form field
 * @param {Object} data - Form data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered form field
 * @private
 */
function renderFormField(field, data = {}, options = {}) {
  logger.debug('Rendering form field', { 
    fieldId: field.id,
    fieldType: field.type
  });
  
  // Get field value from data
  const fieldValue = data[field.id] !== undefined ? data[field.id] : field.defaultValue;
  
  // Create base field structure
  const renderedField = {
    id: field.id,
    type: field.type,
    label: field.label,
    description: field.description,
    required: field.required || false,
    disabled: field.disabled || false,
    hidden: field.hidden || false,
    value: fieldValue,
    placeholder: field.placeholder,
    helpText: field.helpText,
    validation: field.validation || {},
    styling: field.styling || {}
  };
  
  // Add type-specific properties
  switch (field.type) {
    case 'text':
    case 'password':
    case 'email':
    case 'url':
    case 'tel':
      renderedField.minLength = field.minLength;
      renderedField.maxLength = field.maxLength;
      renderedField.pattern = field.pattern;
      break;
      
    case 'number':
    case 'range':
      renderedField.min = field.min;
      renderedField.max = field.max;
      renderedField.step = field.step;
      break;
      
    case 'select':
    case 'radio':
    case 'checkbox':
      renderedField.options = field.options || [];
      renderedField.multiple = field.multiple || false;
      break;
      
    case 'textarea':
      renderedField.rows = field.rows || 3;
      renderedField.cols = field.cols;
      renderedField.minLength = field.minLength;
      renderedField.maxLength = field.maxLength;
      break;
      
    case 'date':
    case 'time':
    case 'datetime-local':
      renderedField.min = field.min;
      renderedField.max = field.max;
      break;
      
    case 'file':
      renderedField.accept = field.accept;
      renderedField.multiple = field.multiple || false;
      renderedField.maxSize = field.maxSize;
      break;
      
    case 'group':
      renderedField.fields = field.fields ? field.fields.map(f => renderFormField(f, data, options)) : [];
      break;
  }
  
  // Apply conditional display logic
  if (field.displayWhen) {
    renderedField.display = evaluateDisplayCondition(field.displayWhen, data);
  }
  
  return renderedField;
}

/**
 * Render form action
 * 
 * @param {Object} action - Form action
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered form action
 * @private
 */
function renderFormAction(action, options = {}) {
  logger.debug('Rendering form action', { 
    actionId: action.id,
    actionType: action.type
  });
  
  // Create base action structure
  const renderedAction = {
    id: action.id,
    type: action.type,
    label: action.label,
    primary: action.primary || false,
    disabled: action.disabled || false,
    hidden: action.hidden || false,
    styling: action.styling || {}
  };
  
  // Add type-specific properties
  switch (action.type) {
    case 'submit':
      renderedAction.submitUrl = action.submitUrl;
      renderedAction.method = action.method || 'POST';
      break;
      
    case 'button':
    case 'link':
      renderedAction.url = action.url;
      renderedAction.target = action.target;
      renderedAction.onClick = action.onClick;
      break;
  }
  
  return renderedAction;
}

/**
 * Render dashboard
 * 
 * @param {Object} schema - Dashboard schema
 * @param {Object} data - Dashboard data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered dashboard
 * @private
 */
function renderDashboard(schema, data = {}, options = {}) {
  logger.debug('Rendering dashboard', { dashboardId: schema.id });
  
  // Create base dashboard structure
  const dashboard = {
    id: schema.id,
    type: 'dashboard',
    title: schema.title,
    description: schema.description,
    sections: [],
    filters: [],
    data: data,
    metadata: {
      ...(schema.metadata || {}),
      renderedAt: new Date().toISOString(),
      renderer: 'NovaVision',
      rendererVersion: '1.0',
      theme: options.theme || 'default'
    }
  };
  
  // Render sections
  if (schema.sections && Array.isArray(schema.sections)) {
    dashboard.sections = schema.sections.map(section => renderDashboardSection(section, data, options));
  }
  
  // Render filters
  if (schema.filters && Array.isArray(schema.filters)) {
    dashboard.filters = schema.filters.map(filter => renderDashboardFilter(filter, data, options));
  }
  
  return dashboard;
}

/**
 * Render dashboard section
 * 
 * @param {Object} section - Dashboard section
 * @param {Object} data - Dashboard data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered dashboard section
 * @private
 */
function renderDashboardSection(section, data = {}, options = {}) {
  logger.debug('Rendering dashboard section', { sectionId: section.id });
  
  // Create base section structure
  const renderedSection = {
    id: section.id,
    title: section.title,
    description: section.description,
    widgets: [],
    styling: section.styling || {}
  };
  
  // Render widgets
  if (section.widgets && Array.isArray(section.widgets)) {
    renderedSection.widgets = section.widgets.map(widget => renderDashboardWidget(widget, data, options));
  }
  
  return renderedSection;
}

/**
 * Render dashboard widget
 * 
 * @param {Object} widget - Dashboard widget
 * @param {Object} data - Dashboard data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered dashboard widget
 * @private
 */
function renderDashboardWidget(widget, data = {}, options = {}) {
  logger.debug('Rendering dashboard widget', { 
    widgetId: widget.id,
    widgetType: widget.type
  });
  
  // Create base widget structure
  const renderedWidget = {
    id: widget.id,
    type: widget.type,
    title: widget.title,
    description: widget.description,
    styling: widget.styling || {}
  };
  
  // Add type-specific properties
  switch (widget.type) {
    case 'chart':
      renderedWidget.chartData = prepareChartData(widget.chartConfig, data);
      renderedWidget.chartConfig = widget.chartConfig;
      break;
      
    case 'table':
      renderedWidget.tableData = prepareTableData(widget.tableConfig, data);
      renderedWidget.tableConfig = widget.tableConfig;
      break;
      
    case 'metric':
      renderedWidget.metricData = prepareMetricData(widget.metricConfig, data);
      renderedWidget.metricConfig = widget.metricConfig;
      break;
      
    case 'list':
      renderedWidget.listData = prepareListData(widget.listConfig, data);
      renderedWidget.listConfig = widget.listConfig;
      break;
      
    case 'map':
      renderedWidget.mapData = prepareMapData(widget.mapConfig, data);
      renderedWidget.mapConfig = widget.mapConfig;
      break;
  }
  
  return renderedWidget;
}

/**
 * Render dashboard filter
 * 
 * @param {Object} filter - Dashboard filter
 * @param {Object} data - Dashboard data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered dashboard filter
 * @private
 */
function renderDashboardFilter(filter, data = {}, options = {}) {
  logger.debug('Rendering dashboard filter', { 
    filterId: filter.id,
    filterType: filter.type
  });
  
  // Create base filter structure
  const renderedFilter = {
    id: filter.id,
    type: filter.type,
    label: filter.label,
    value: data[filter.id] !== undefined ? data[filter.id] : filter.defaultValue,
    styling: filter.styling || {}
  };
  
  // Add type-specific properties
  switch (filter.type) {
    case 'select':
      renderedFilter.options = filter.options || [];
      renderedFilter.multiple = filter.multiple || false;
      break;
      
    case 'date':
    case 'daterange':
      renderedFilter.min = filter.min;
      renderedFilter.max = filter.max;
      break;
      
    case 'range':
      renderedFilter.min = filter.min;
      renderedFilter.max = filter.max;
      renderedFilter.step = filter.step;
      break;
  }
  
  return renderedFilter;
}

/**
 * Render report
 * 
 * @param {Object} schema - Report schema
 * @param {Object} data - Report data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered report
 * @private
 */
function renderReport(schema, data = {}, options = {}) {
  logger.debug('Rendering report', { reportId: schema.id });
  
  // Create base report structure
  const report = {
    id: schema.id,
    type: 'report',
    title: schema.title,
    description: schema.description,
    sections: [],
    parameters: [],
    data: data,
    metadata: {
      ...(schema.metadata || {}),
      renderedAt: new Date().toISOString(),
      renderer: 'NovaVision',
      rendererVersion: '1.0',
      theme: options.theme || 'default'
    }
  };
  
  // Render sections
  if (schema.sections && Array.isArray(schema.sections)) {
    report.sections = schema.sections.map(section => renderReportSection(section, data, options));
  }
  
  // Render parameters
  if (schema.parameters && Array.isArray(schema.parameters)) {
    report.parameters = schema.parameters.map(parameter => renderReportParameter(parameter, data, options));
  }
  
  return report;
}

/**
 * Render report section
 * 
 * @param {Object} section - Report section
 * @param {Object} data - Report data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered report section
 * @private
 */
function renderReportSection(section, data = {}, options = {}) {
  logger.debug('Rendering report section', { sectionId: section.id });
  
  // Create base section structure
  const renderedSection = {
    id: section.id,
    title: section.title,
    description: section.description,
    elements: [],
    styling: section.styling || {}
  };
  
  // Render elements
  if (section.elements && Array.isArray(section.elements)) {
    renderedSection.elements = section.elements.map(element => renderReportElement(element, data, options));
  }
  
  return renderedSection;
}

/**
 * Render report element
 * 
 * @param {Object} element - Report element
 * @param {Object} data - Report data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered report element
 * @private
 */
function renderReportElement(element, data = {}, options = {}) {
  logger.debug('Rendering report element', { 
    elementId: element.id,
    elementType: element.type
  });
  
  // Create base element structure
  const renderedElement = {
    id: element.id,
    type: element.type,
    title: element.title,
    styling: element.styling || {}
  };
  
  // Add type-specific properties
  switch (element.type) {
    case 'text':
      renderedElement.content = element.textConfig.content;
      renderedElement.format = element.textConfig.format;
      break;
      
    case 'chart':
      renderedElement.chartData = prepareChartData(element.chartConfig, data);
      renderedElement.chartConfig = element.chartConfig;
      break;
      
    case 'table':
      renderedElement.tableData = prepareTableData(element.tableConfig, data);
      renderedElement.tableConfig = element.tableConfig;
      break;
      
    case 'image':
      renderedElement.src = element.imageConfig.src;
      renderedElement.alt = element.imageConfig.alt;
      renderedElement.width = element.imageConfig.width;
      renderedElement.height = element.imageConfig.height;
      break;
      
    case 'page-break':
      // No additional properties needed
      break;
  }
  
  return renderedElement;
}

/**
 * Render report parameter
 * 
 * @param {Object} parameter - Report parameter
 * @param {Object} data - Report data
 * @param {Object} options - Rendering options
 * @returns {Object} - Rendered report parameter
 * @private
 */
function renderReportParameter(parameter, data = {}, options = {}) {
  logger.debug('Rendering report parameter', { 
    parameterId: parameter.id,
    parameterType: parameter.type
  });
  
  // Create base parameter structure
  const renderedParameter = {
    id: parameter.id,
    type: parameter.type,
    label: parameter.label,
    value: data[parameter.id] !== undefined ? data[parameter.id] : parameter.defaultValue,
    styling: parameter.styling || {}
  };
  
  // Add type-specific properties
  switch (parameter.type) {
    case 'text':
      renderedParameter.placeholder = parameter.placeholder;
      break;
      
    case 'select':
      renderedParameter.options = parameter.options || [];
      renderedParameter.multiple = parameter.multiple || false;
      break;
      
    case 'date':
      renderedParameter.min = parameter.min;
      renderedParameter.max = parameter.max;
      break;
  }
  
  return renderedParameter;
}

/**
 * Prepare chart data
 * 
 * @param {Object} chartConfig - Chart configuration
 * @param {Object} data - Data
 * @returns {Object} - Prepared chart data
 * @private
 */
function prepareChartData(chartConfig, data = {}) {
  if (!chartConfig) {
    return {};
  }
  
  // If chart data is already provided in the config, use it
  if (chartConfig.data) {
    return chartConfig.data;
  }
  
  // If a data source is specified, extract data from the source
  if (chartConfig.dataSource) {
    const sourceData = data[chartConfig.dataSource];
    
    if (!sourceData) {
      return {};
    }
    
    // Extract labels and datasets based on the chart type
    switch (chartConfig.type) {
      case 'bar':
      case 'line':
      case 'pie':
        return {
          labels: Object.keys(sourceData),
          datasets: [{
            data: Object.values(sourceData)
          }]
        };
        
      default:
        return sourceData;
    }
  }
  
  return {};
}

/**
 * Prepare table data
 * 
 * @param {Object} tableConfig - Table configuration
 * @param {Object} data - Data
 * @returns {Array} - Prepared table data
 * @private
 */
function prepareTableData(tableConfig, data = {}) {
  if (!tableConfig) {
    return [];
  }
  
  // If table data is already provided in the config, use it
  if (tableConfig.data) {
    return tableConfig.data;
  }
  
  // If a data source is specified, extract data from the source
  if (tableConfig.dataSource) {
    return data[tableConfig.dataSource] || [];
  }
  
  return [];
}

/**
 * Prepare metric data
 * 
 * @param {Object} metricConfig - Metric configuration
 * @param {Object} data - Data
 * @returns {Object} - Prepared metric data
 * @private
 */
function prepareMetricData(metricConfig, data = {}) {
  if (!metricConfig) {
    return {};
  }
  
  // If metric value is already provided in the config, use it
  if (metricConfig.value !== undefined) {
    return {
      value: metricConfig.value,
      previousValue: metricConfig.previousValue,
      trend: metricConfig.trend
    };
  }
  
  // If a data source is specified, extract data from the source
  if (metricConfig.dataSource) {
    const value = data[metricConfig.dataSource];
    const previousValue = data[metricConfig.previousValueSource];
    
    // Calculate trend if both values are available
    let trend = null;
    if (value !== undefined && previousValue !== undefined && previousValue !== 0) {
      trend = ((value - previousValue) / Math.abs(previousValue)) * 100;
    }
    
    return {
      value,
      previousValue,
      trend
    };
  }
  
  return {};
}

/**
 * Prepare list data
 * 
 * @param {Object} listConfig - List configuration
 * @param {Object} data - Data
 * @returns {Array} - Prepared list data
 * @private
 */
function prepareListData(listConfig, data = {}) {
  if (!listConfig) {
    return [];
  }
  
  // If list items are already provided in the config, use them
  if (listConfig.items) {
    return listConfig.items;
  }
  
  // If a data source is specified, extract data from the source
  if (listConfig.dataSource) {
    return data[listConfig.dataSource] || [];
  }
  
  return [];
}

/**
 * Prepare map data
 * 
 * @param {Object} mapConfig - Map configuration
 * @param {Object} data - Data
 * @returns {Object} - Prepared map data
 * @private
 */
function prepareMapData(mapConfig, data = {}) {
  if (!mapConfig) {
    return {};
  }
  
  // If map data is already provided in the config, use it
  if (mapConfig.data) {
    return mapConfig.data;
  }
  
  // If a data source is specified, extract data from the source
  if (mapConfig.dataSource) {
    return data[mapConfig.dataSource] || {};
  }
  
  return {};
}

/**
 * Evaluate display condition
 * 
 * @param {Object} condition - Display condition
 * @param {Object} data - Data
 * @returns {boolean} - Whether the condition is met
 * @private
 */
function evaluateDisplayCondition(condition, data = {}) {
  if (!condition) {
    return true;
  }
  
  // Simple field value condition
  if (condition.field && condition.value !== undefined) {
    const fieldValue = data[condition.field];
    
    switch (condition.operator) {
      case 'eq':
        return fieldValue === condition.value;
      case 'neq':
        return fieldValue !== condition.value;
      case 'gt':
        return fieldValue > condition.value;
      case 'gte':
        return fieldValue >= condition.value;
      case 'lt':
        return fieldValue < condition.value;
      case 'lte':
        return fieldValue <= condition.value;
      case 'contains':
        return String(fieldValue).includes(String(condition.value));
      case 'startsWith':
        return String(fieldValue).startsWith(String(condition.value));
      case 'endsWith':
        return String(fieldValue).endsWith(String(condition.value));
      default:
        return fieldValue === condition.value;
    }
  }
  
  // AND condition
  if (condition.and && Array.isArray(condition.and)) {
    return condition.and.every(subCondition => evaluateDisplayCondition(subCondition, data));
  }
  
  // OR condition
  if (condition.or && Array.isArray(condition.or)) {
    return condition.or.some(subCondition => evaluateDisplayCondition(subCondition, data));
  }
  
  // NOT condition
  if (condition.not) {
    return !evaluateDisplayCondition(condition.not, data);
  }
  
  return true;
}

module.exports = {
  renderUiFromSchema
};
