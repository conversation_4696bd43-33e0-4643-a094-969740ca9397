/**
 * FocusTrap Component
 * 
 * A component for trapping focus within a container.
 */

import React, { useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useAccessibility } from '../accessibility/AccessibilityContext';

/**
 * FocusTrap component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {boolean} [props.active=true] - Whether the focus trap is active
 * @param {boolean} [props.autoFocus=true] - Whether to auto-focus the first focusable element
 * @param {boolean} [props.returnFocusOnDeactivate=true] - Whether to return focus to the previously focused element when deactivated
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} FocusTrap component
 */
const FocusTrap = ({
  children,
  active = true,
  autoFocus = true,
  returnFocusOnDeactivate = true,
  className = '',
  style = {},
  ...rest
}) => {
  const { settings } = useAccessibility();
  
  // Refs
  const containerRef = useRef(null);
  const previouslyFocusedElement = useRef(null);
  
  // Get focusable elements
  const getFocusableElements = () => {
    if (!containerRef.current) return [];
    
    return Array.from(
      containerRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
    ).filter(
      element =>
        !element.hasAttribute('disabled') &&
        !element.getAttribute('aria-hidden') &&
        element.offsetWidth > 0 &&
        element.offsetHeight > 0
    );
  };
  
  // Focus first element
  const focusFirstElement = () => {
    if (!containerRef.current || !autoFocus || !active) return;
    
    const focusableElements = getFocusableElements();
    
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    } else {
      containerRef.current.focus();
    }
  };
  
  // Handle tab key
  const handleTabKey = (event) => {
    if (!containerRef.current || !active || !settings.keyboardNavigation) return;
    
    const focusableElements = getFocusableElements();
    
    if (focusableElements.length === 0) return;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    // If shift + tab and first element is focused, move to last element
    if (event.shiftKey && document.activeElement === firstElement) {
      event.preventDefault();
      lastElement.focus();
    }
    // If tab and last element is focused, move to first element
    else if (!event.shiftKey && document.activeElement === lastElement) {
      event.preventDefault();
      firstElement.focus();
    }
  };
  
  // Handle key down
  const handleKeyDown = (event) => {
    if (event.key === 'Tab') {
      handleTabKey(event);
    }
    
    // Close on escape key
    if (event.key === 'Escape' && active) {
      if (returnFocusOnDeactivate && previouslyFocusedElement.current) {
        previouslyFocusedElement.current.focus();
      }
    }
  };
  
  // Save previously focused element and focus first element
  useEffect(() => {
    if (active) {
      previouslyFocusedElement.current = document.activeElement;
      focusFirstElement();
    }
    
    return () => {
      if (active && returnFocusOnDeactivate && previouslyFocusedElement.current) {
        previouslyFocusedElement.current.focus();
      }
    };
  }, [active]);
  
  // Add event listeners
  useEffect(() => {
    if (active) {
      document.addEventListener('keydown', handleKeyDown);
    }
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [active, handleKeyDown]);
  
  return (
    <div
      ref={containerRef}
      className={className}
      style={style}
      tabIndex={active ? -1 : undefined}
      {...rest}
    >
      {children}
    </div>
  );
};

FocusTrap.propTypes = {
  children: PropTypes.node.isRequired,
  active: PropTypes.bool,
  autoFocus: PropTypes.bool,
  returnFocusOnDeactivate: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default FocusTrap;
