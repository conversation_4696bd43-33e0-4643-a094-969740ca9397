83398f4cfd8394550b1a063332676dc0
// Mock axios
_getJestObj().mock('axios');

// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Unit tests for the Certifications & Accreditation Connector
 */

const axios = require('axios');
const CertificationsAccreditationConnector = require('../../../../connector/implementations/certifications-accreditation');
describe('CertificationsAccreditationConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    mockCredentials = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    };

    // Create connector instance
    connector = new CertificationsAccreditationConnector(mockConfig, mockCredentials);
  });
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
    });
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new CertificationsAccreditationConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
  });
  describe('initialize', () => {
    it('should authenticate if credentials are provided', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockResolvedValue();
      await connector.initialize();
      expect(connector.authenticate).toHaveBeenCalled();
    });
    it('should not authenticate if credentials are not provided', async () => {
      // Create connector without credentials
      const connectorWithoutCredentials = new CertificationsAccreditationConnector(mockConfig, {});

      // Mock authenticate method
      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();
      await connectorWithoutCredentials.initialize();
      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();
    });
  });
  describe('authenticate', () => {
    it('should make a POST request to the token endpoint', async () => {
      // Mock axios post response
      axios.post.mockResolvedValue({
        data: {
          access_token: 'test-access-token',
          expires_in: 3600
        }
      });
      await connector.authenticate();
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: mockCredentials.clientId,
        client_secret: mockCredentials.clientSecret,
        scope: 'read:certifications write:certifications read:assessments write:assessments read:evidence write:evidence'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      expect(connector.accessToken).toBe('test-access-token');
      expect(connector.tokenExpiry).toBeDefined();
    });
    it('should throw an error if authentication fails', async () => {
      // Mock axios post error
      const errorMessage = 'Authentication failed';
      axios.post.mockRejectedValue(new Error(errorMessage));
      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);
    });
  });
  describe('getAuthHeaders', () => {
    it('should return authorization headers with access token', async () => {
      // Set access token and expiry
      connector.accessToken = 'test-access-token';
      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now

      const headers = await connector.getAuthHeaders();
      expect(headers).toEqual({
        'Authorization': 'Bearer test-access-token'
      });
    });
    it('should authenticate if access token is not set', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      const headers = await connector.getAuthHeaders();
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
  });
  describe('listCertifications', () => {
    it('should make a GET request to the certifications endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'cert-1',
            name: 'ISO 27001'
          }, {
            id: 'cert-2',
            name: 'SOC 2 Type II'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'active',
        limit: 50
      };
      const result = await connector.listCertifications(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/certifications`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if the request fails', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      await expect(connector.listCertifications()).rejects.toThrow(`Error listing certifications: ${errorMessage}`);
    });
  });
  describe('getCertification', () => {
    it('should make a GET request to the specific certification endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'cert-123',
          name: 'ISO 27001',
          description: 'Information Security Management System certification'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const certificationId = 'cert-123';
      const result = await connector.getCertification(certificationId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/certifications/${certificationId}`, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if certificationId is not provided', async () => {
      await expect(connector.getCertification()).rejects.toThrow('Certification ID is required');
    });
  });
  describe('listAssessments', () => {
    it('should make a GET request to the assessments endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'assess-1',
            name: 'ISO 27001 Annual Assessment'
          }, {
            id: 'assess-2',
            name: 'SOC 2 Type II Audit'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'in_progress',
        certificationId: 'cert-123'
      };
      const result = await connector.listAssessments(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/assessments`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
  });
  describe('getAssessment', () => {
    it('should make a GET request to the specific assessment endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'assess-123',
          name: 'ISO 27001 Annual Assessment',
          status: 'in_progress'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const assessmentId = 'assess-123';
      const result = await connector.getAssessment(assessmentId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/assessments/${assessmentId}`, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if assessmentId is not provided', async () => {
      await expect(connector.getAssessment()).rejects.toThrow('Assessment ID is required');
    });
  });
  describe('listEvidence', () => {
    it('should make a GET request to the evidence endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'evidence-1',
            name: 'Information Security Policy Document'
          }, {
            id: 'evidence-2',
            name: 'Risk Assessment Report'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        assessmentId: 'assess-123'
      };
      const result = await connector.listEvidence(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/evidence`, {
        params,
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
  });
  describe('getEvidence', () => {
    it('should make a GET request to the specific evidence endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });

      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'evidence-123',
          name: 'Information Security Policy Document',
          type: 'document'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const evidenceId = 'evidence-123';
      const result = await connector.getEvidence(evidenceId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/evidence/${evidenceId}`, {
        headers: {
          'Authorization': 'Bearer test-access-token',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if evidenceId is not provided', async () => {
      await expect(connector.getEvidence()).rejects.toThrow('Evidence ID is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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