/**
 * Domain Transition Mapper
 * 
 * This module provides functionality for mapping tensors between different domains
 * (cyber, financial, biological) while preserving their essential properties.
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS, saturate, asymptotic } = require('./constants');
const { sanitizeTensor } = require('./input-sanitizer');

/**
 * Domain Transition Mapper class
 */
class DomainTransitionMapper extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Domain transition options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      preserveCoherence: true,
      coherenceThreshold: 0.7,
      entropyContainmentThreshold: 0.05,
      ...options
    };
    
    // Domain transformation matrices
    this.transformationMatrices = {
      'universal-to-cyber': [
        [0.9, 0.05, 0.05],
        [0.1, 0.8, 0.1],
        [0.05, 0.1, 0.85]
      ],
      'universal-to-financial': [
        [0.85, 0.1, 0.05],
        [0.05, 0.9, 0.05],
        [0.05, 0.1, 0.85]
      ],
      'universal-to-biological': [
        [0.8, 0.1, 0.1],
        [0.1, 0.8, 0.1],
        [0.05, 0.15, 0.8]
      ],
      'cyber-to-universal': [
        [0.9, 0.05, 0.05],
        [0.1, 0.8, 0.1],
        [0.05, 0.1, 0.85]
      ],
      'cyber-to-financial': [
        [0.7, 0.2, 0.1],
        [0.1, 0.8, 0.1],
        [0.1, 0.1, 0.8]
      ],
      'cyber-to-biological': [
        [0.5, 0.3, 0.2],
        [0.2, 0.5, 0.3],
        [0.1, 0.3, 0.6]
      ],
      'financial-to-universal': [
        [0.85, 0.1, 0.05],
        [0.05, 0.9, 0.05],
        [0.05, 0.1, 0.85]
      ],
      'financial-to-cyber': [
        [0.8, 0.1, 0.1],
        [0.2, 0.7, 0.1],
        [0.1, 0.2, 0.7]
      ],
      'financial-to-biological': [
        [0.6, 0.2, 0.2],
        [0.3, 0.6, 0.1],
        [0.2, 0.1, 0.7]
      ],
      'biological-to-universal': [
        [0.8, 0.1, 0.1],
        [0.1, 0.8, 0.1],
        [0.05, 0.15, 0.8]
      ],
      'biological-to-cyber': [
        [0.5, 0.2, 0.3],
        [0.3, 0.5, 0.2],
        [0.2, 0.2, 0.6]
      ],
      'biological-to-financial': [
        [0.6, 0.3, 0.1],
        [0.2, 0.6, 0.2],
        [0.1, 0.2, 0.7]
      ]
    };
    
    this.tensors = new Map();
    this.mappingHistory = new Map();
  }
  
  /**
   * Register a tensor
   * @param {string} id - Tensor ID
   * @param {Object} tensor - Tensor to register
   * @param {string} domain - Domain to register tensor in
   * @returns {Object} - Registered tensor
   */
  registerTensor(id, tensor, domain = 'universal') {
    // Sanitize tensor
    const sanitizedTensor = sanitizeTensor(tensor, domain);
    
    // Add domain information
    const domainTensor = {
      ...sanitizedTensor,
      domain,
      originalDomain: domain,
      mappingHistory: [],
      registeredAt: Date.now()
    };
    
    // Store tensor
    this.tensors.set(id, domainTensor);
    
    // Initialize mapping history
    this.mappingHistory.set(id, []);
    
    this.emit('tensor-registered', { id, domain });
    
    return domainTensor;
  }
  
  /**
   * Get a tensor
   * @param {string} id - Tensor ID
   * @returns {Object} - Tensor
   */
  getTensor(id) {
    return this.tensors.get(id);
  }
  
  /**
   * Map a tensor to a different domain
   * @param {string} id - Tensor ID
   * @param {string} targetDomain - Target domain
   * @returns {Object} - Mapped tensor
   */
  mapToDomain(id, targetDomain) {
    const tensor = this.tensors.get(id);
    
    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }
    
    // Skip if already in target domain
    if (tensor.domain === targetDomain) {
      return tensor;
    }
    
    // Get transformation matrix
    const transformationKey = `${tensor.domain}-to-${targetDomain}`;
    const transformationMatrix = this.transformationMatrices[transformationKey];
    
    if (!transformationMatrix) {
      throw new Error(`No transformation matrix for ${transformationKey}`);
    }
    
    // Apply transformation
    const transformedValues = this._applyTransformation(tensor.values, transformationMatrix);
    
    // Apply domain-specific bounds
    const boundedValues = transformedValues.map(v => 
      saturate.forDomain(targetDomain, v)
    );
    
    // Create mapped tensor
    const mappedTensor = {
      ...tensor,
      domain: targetDomain,
      values: boundedValues,
      originalDomain: tensor.originalDomain || tensor.domain,
      previousDomain: tensor.domain,
      mappingTimestamp: Date.now()
    };
    
    // Store mapped tensor
    this.tensors.set(id, mappedTensor);
    
    // Update mapping history
    const history = this.mappingHistory.get(id) || [];
    history.push({
      fromDomain: tensor.domain,
      toDomain: targetDomain,
      timestamp: mappedTensor.mappingTimestamp,
      consistency: this._calculateMappingConsistency(tensor.values, boundedValues)
    });
    this.mappingHistory.set(id, history);
    
    // Emit mapping event
    this.emit('tensor-mapped', {
      id,
      fromDomain: tensor.domain,
      toDomain: targetDomain,
      consistency: history[history.length - 1].consistency
    });
    
    return mappedTensor;
  }
  
  /**
   * Validate domain mapping
   * @param {string} id - Tensor ID
   * @returns {Object} - Validation result
   */
  validateMapping(id) {
    const tensor = this.tensors.get(id);
    
    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }
    
    // Skip if not mapped
    if (tensor.domain === tensor.originalDomain) {
      return {
        isValid: true,
        isMapped: false,
        consistency: 1.0
      };
    }
    
    // Get mapping history
    const history = this.mappingHistory.get(id) || [];
    
    // Calculate overall consistency
    const overallConsistency = history.reduce(
      (acc, mapping) => acc * mapping.consistency, 
      1.0
    );
    
    // Check if mapping is valid
    const isValid = overallConsistency >= this.options.coherenceThreshold;
    
    return {
      isValid,
      isMapped: true,
      mappingPath: history.map(h => `${h.fromDomain}->${h.toDomain}`).join('->'),
      consistency: overallConsistency,
      mappingHistory: history
    };
  }
  
  /**
   * Map a tensor back to its original domain
   * @param {string} id - Tensor ID
   * @returns {Object} - Mapped tensor
   */
  mapToOriginalDomain(id) {
    const tensor = this.tensors.get(id);
    
    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }
    
    // Skip if already in original domain
    if (tensor.domain === tensor.originalDomain) {
      return tensor;
    }
    
    return this.mapToDomain(id, tensor.originalDomain);
  }
  
  /**
   * Get mapping history for a tensor
   * @param {string} id - Tensor ID
   * @returns {Array} - Mapping history
   */
  getMappingHistory(id) {
    return this.mappingHistory.get(id) || [];
  }
  
  /**
   * Apply transformation matrix to values
   * @param {Array} values - Values to transform
   * @param {Array} matrix - Transformation matrix
   * @returns {Array} - Transformed values
   * @private
   */
  _applyTransformation(values, matrix) {
    // For simplicity, we'll use a weighted average approach
    return values.map(v => {
      // Apply each row of the matrix as a weighted transformation
      const transformedValues = matrix.map(row => {
        let result = 0;
        for (let i = 0; i < row.length; i++) {
          result += v * row[i];
        }
        return result / row.length;
      });
      
      // Average the results
      return transformedValues.reduce((sum, val) => sum + val, 0) / transformedValues.length;
    });
  }
  
  /**
   * Calculate mapping consistency
   * @param {Array} sourceValues - Source values
   * @param {Array} mappedValues - Mapped values
   * @returns {number} - Consistency (0-1)
   * @private
   */
  _calculateMappingConsistency(sourceValues, mappedValues) {
    if (sourceValues.length !== mappedValues.length) {
      return 0;
    }
    
    // Calculate normalized difference
    let sumSquaredDiff = 0;
    for (let i = 0; i < sourceValues.length; i++) {
      // Normalize values to [0,1] range for comparison
      const normalizedSource = (sourceValues[i] - Math.min(...sourceValues)) / 
                              (Math.max(...sourceValues) - Math.min(...sourceValues) || 1);
      const normalizedMapped = (mappedValues[i] - Math.min(...mappedValues)) / 
                              (Math.max(...mappedValues) - Math.min(...mappedValues) || 1);
      
      sumSquaredDiff += Math.pow(normalizedSource - normalizedMapped, 2);
    }
    
    const rmsDiff = Math.sqrt(sumSquaredDiff / sourceValues.length);
    const consistency = 1 - rmsDiff;
    
    return Math.max(0, Math.min(1, consistency));
  }
  
  /**
   * Add a custom transformation matrix
   * @param {string} fromDomain - Source domain
   * @param {string} toDomain - Target domain
   * @param {Array} matrix - Transformation matrix
   */
  addTransformationMatrix(fromDomain, toDomain, matrix) {
    const transformationKey = `${fromDomain}-to-${toDomain}`;
    this.transformationMatrices[transformationKey] = matrix;
    
    this.emit('transformation-matrix-added', {
      fromDomain,
      toDomain,
      matrix
    });
  }
}

module.exports = DomainTransitionMapper;
