/**
 * Contracts & Policy Lifecycle Connector Implementation
 * 
 * This module implements the connector for contract management and policy lifecycle systems.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('contracts-policy-lifecycle-connector');

/**
 * Contracts & Policy Lifecycle Connector
 */
class ContractsPolicyLifecycleConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing Contracts & Policy Lifecycle connector');
    
    // Authenticate if needed
    if (this.credentials.clientId && this.credentials.clientSecret) {
      await this.authenticate();
    }
  }

  /**
   * Authenticate with the API
   * 
   * @returns {Promise<void>}
   */
  async authenticate() {
    logger.info('Authenticating with Contracts & Policy Lifecycle API');
    
    try {
      const response = await axios.post(`${this.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: this.credentials.clientId,
        client_secret: this.credentials.clientSecret,
        scope: 'read:contracts write:contracts read:policies write:policies'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      
      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);
      
      logger.info('Authentication successful');
    } catch (error) {
      logger.error('Authentication failed', { error: error.message });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Promise<Object>} - Authentication headers
   */
  async getAuthHeaders() {
    // Check if token is expired or about to expire (within 5 minutes)
    if (!this.accessToken || (this.tokenExpiry && this.tokenExpiry - Date.now() < 300000)) {
      await this.authenticate();
    }
    
    return {
      'Authorization': `Bearer ${this.accessToken}`
    };
  }

  /**
   * List contracts
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of contracts
   */
  async listContracts(params = {}) {
    logger.info('Listing contracts', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/contracts`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing contracts', { error: error.message });
      throw new Error(`Error listing contracts: ${error.message}`);
    }
  }

  /**
   * Get a contract
   * 
   * @param {string} contractId - Contract ID
   * @returns {Promise<Object>} - Contract details
   */
  async getContract(contractId) {
    logger.info('Getting contract', { contractId });
    
    if (!contractId) {
      throw new Error('Contract ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/contracts/${contractId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting contract', { contractId, error: error.message });
      throw new Error(`Error getting contract: ${error.message}`);
    }
  }

  /**
   * Create a contract
   * 
   * @param {Object} contractData - Contract data
   * @returns {Promise<Object>} - Created contract
   */
  async createContract(contractData) {
    logger.info('Creating contract');
    
    if (!contractData) {
      throw new Error('Contract data is required');
    }
    
    // Validate required fields
    const requiredFields = ['title', 'type', 'parties', 'startDate'];
    for (const field of requiredFields) {
      if (!contractData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/contracts`, contractData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating contract', { error: error.message });
      throw new Error(`Error creating contract: ${error.message}`);
    }
  }

  /**
   * Update a contract
   * 
   * @param {string} contractId - Contract ID
   * @param {Object} contractData - Contract data
   * @returns {Promise<Object>} - Updated contract
   */
  async updateContract(contractId, contractData) {
    logger.info('Updating contract', { contractId });
    
    if (!contractId) {
      throw new Error('Contract ID is required');
    }
    
    if (!contractData) {
      throw new Error('Contract data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/contracts/${contractId}`, contractData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating contract', { contractId, error: error.message });
      throw new Error(`Error updating contract: ${error.message}`);
    }
  }

  /**
   * Delete a contract
   * 
   * @param {string} contractId - Contract ID
   * @returns {Promise<void>}
   */
  async deleteContract(contractId) {
    logger.info('Deleting contract', { contractId });
    
    if (!contractId) {
      throw new Error('Contract ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      await axios.delete(`${this.baseUrl}/contracts/${contractId}`, {
        headers: {
          ...authHeaders,
          'Accept': 'application/json'
        }
      });
      
      logger.info('Contract deleted successfully', { contractId });
    } catch (error) {
      logger.error('Error deleting contract', { contractId, error: error.message });
      throw new Error(`Error deleting contract: ${error.message}`);
    }
  }

  /**
   * List policies
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of policies
   */
  async listPolicies(params = {}) {
    logger.info('Listing policies', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/policies`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing policies', { error: error.message });
      throw new Error(`Error listing policies: ${error.message}`);
    }
  }

  /**
   * Get a policy
   * 
   * @param {string} policyId - Policy ID
   * @returns {Promise<Object>} - Policy details
   */
  async getPolicy(policyId) {
    logger.info('Getting policy', { policyId });
    
    if (!policyId) {
      throw new Error('Policy ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/policies/${policyId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting policy', { policyId, error: error.message });
      throw new Error(`Error getting policy: ${error.message}`);
    }
  }

  /**
   * Create a policy
   * 
   * @param {Object} policyData - Policy data
   * @returns {Promise<Object>} - Created policy
   */
  async createPolicy(policyData) {
    logger.info('Creating policy');
    
    if (!policyData) {
      throw new Error('Policy data is required');
    }
    
    // Validate required fields
    const requiredFields = ['title', 'category', 'content'];
    for (const field of requiredFields) {
      if (!policyData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/policies`, policyData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating policy', { error: error.message });
      throw new Error(`Error creating policy: ${error.message}`);
    }
  }

  /**
   * Update a policy
   * 
   * @param {string} policyId - Policy ID
   * @param {Object} policyData - Policy data
   * @returns {Promise<Object>} - Updated policy
   */
  async updatePolicy(policyId, policyData) {
    logger.info('Updating policy', { policyId });
    
    if (!policyId) {
      throw new Error('Policy ID is required');
    }
    
    if (!policyData) {
      throw new Error('Policy data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/policies/${policyId}`, policyData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating policy', { policyId, error: error.message });
      throw new Error(`Error updating policy: ${error.message}`);
    }
  }

  /**
   * Delete a policy
   * 
   * @param {string} policyId - Policy ID
   * @returns {Promise<void>}
   */
  async deletePolicy(policyId) {
    logger.info('Deleting policy', { policyId });
    
    if (!policyId) {
      throw new Error('Policy ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      await axios.delete(`${this.baseUrl}/policies/${policyId}`, {
        headers: {
          ...authHeaders,
          'Accept': 'application/json'
        }
      });
      
      logger.info('Policy deleted successfully', { policyId });
    } catch (error) {
      logger.error('Error deleting policy', { policyId, error: error.message });
      throw new Error(`Error deleting policy: ${error.message}`);
    }
  }
}

module.exports = ContractsPolicyLifecycleConnector;
