/**
 * Time Drift Analyzer
 *
 * This module provides functionality for analyzing and compensating for
 * time drift in tensor operations, ensuring temporal stability and
 * coherence across time-dependent operations.
 */

const EventEmitter = require('events');
const { MAX_SAFE_BOUNDS, saturate, asymptotic } = require('./constants');
const { sanitizeTensor } = require('./input-sanitizer');

/**
 * Time Drift Analyzer class
 */
class TimeDriftAnalyzer extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Time drift options
   */
  constructor(options = {}) {
    super();

    this.options = {
      maxDriftThreshold: 0.2, // Maximum allowed drift (20%)
      driftCompensationFactor: 0.8, // Effectiveness of drift compensation
      timeSignatureEnabled: true, // Whether to use time signatures
      driftDetectionThreshold: 0.01, // Minimum drift to trigger detection
      ...options
    };

    this.tensors = new Map();
    this.driftHistory = new Map();
  }

  /**
   * Register a tensor
   * @param {string} id - Tensor ID
   * @param {Object} tensor - Tensor to register
   * @param {string} domain - Domain to register tensor in
   * @returns {Object} - Registered tensor with time properties
   */
  registerTensor(id, tensor, domain = 'universal') {
    // Sanitize tensor
    const sanitizedTensor = sanitizeTensor(tensor, domain);

    // Current timestamp
    const now = Date.now();

    // Add time properties
    const timeTensor = {
      ...sanitizedTensor,
      domain,
      createdAt: now,
      lastUpdated: now,
      timeSignature: this._generateTimeSignature(sanitizedTensor.values, now),
      driftFactor: 0,
      driftCompensated: false,
      registeredAt: now
    };

    // Store tensor
    this.tensors.set(id, timeTensor);

    // Initialize drift history
    this.driftHistory.set(id, []);

    this.emit('tensor-registered', { id, domain });

    return timeTensor;
  }

  /**
   * Get a tensor
   * @param {string} id - Tensor ID
   * @returns {Object} - Tensor
   */
  getTensor(id) {
    return this.tensors.get(id);
  }

  /**
   * Update a tensor
   * @param {string} id - Tensor ID
   * @param {Object} updatedTensor - Updated tensor
   * @returns {Object} - Updated tensor with time properties
   */
  updateTensor(id, updatedTensor) {
    const existingTensor = this.tensors.get(id);

    if (!existingTensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Current timestamp
    const now = Date.now();

    // Calculate time drift
    const timeDiff = now - existingTensor.lastUpdated;
    const driftFactor = this._calculateDriftFactor(timeDiff);

    // Check if drift detection is needed
    const driftDetected = driftFactor >= this.options.driftDetectionThreshold;

    // Apply drift compensation if needed
    let compensatedValues = [...updatedTensor.values];
    let driftCompensated = false;

    if (driftDetected && this.options.timeSignatureEnabled) {
      // Verify time signature
      const expectedTimeSignature = this._generateTimeSignature(existingTensor.values, existingTensor.lastUpdated);
      const actualTimeSignature = this._generateTimeSignature(updatedTensor.values, now);

      // If time signature mismatch, apply compensation
      if (Math.abs(expectedTimeSignature - actualTimeSignature) > 0.01) {
        compensatedValues = this._compensateForDrift(
          existingTensor.values,
          updatedTensor.values,
          driftFactor
        );
        driftCompensated = true;
      }
    }

    // Update tensor
    const updatedTimeTensor = {
      ...updatedTensor,
      domain: existingTensor.domain,
      createdAt: existingTensor.createdAt,
      lastUpdated: now,
      timeSignature: this._generateTimeSignature(compensatedValues, now),
      driftFactor,
      driftCompensated,
      values: compensatedValues,
      registeredAt: existingTensor.registeredAt
    };

    // Store updated tensor
    this.tensors.set(id, updatedTimeTensor);

    // Update drift history if drift detected
    if (driftDetected) {
      const history = this.driftHistory.get(id) || [];
      history.push({
        timestamp: now,
        timeDiff,
        driftFactor,
        driftCompensated,
        timeSignatureBefore: this._generateTimeSignature(updatedTensor.values, now),
        timeSignatureAfter: updatedTimeTensor.timeSignature
      });
      this.driftHistory.set(id, history);

      // Emit drift event
      this.emit('time-drift-detected', {
        id,
        driftFactor,
        driftCompensated,
        timeDiff
      });
    }

    return updatedTimeTensor;
  }

  /**
   * Simulate time drift (for testing)
   * @param {string} id - Tensor ID
   * @param {number} futureTime - Future timestamp
   * @param {number} cycleCount - Current cycle count (for warm-start adjustment)
   * @returns {Object} - Drift simulation result
   */
  simulateTimeDrift(id, futureTime, cycleCount = 0) {
    const tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Calculate time difference
    const timeDiff = futureTime - tensor.lastUpdated;

    // Calculate drift factor
    const driftFactor = this._calculateDriftFactor(timeDiff);

    // Apply drift to values
    const driftedValues = tensor.values.map(v => {
      // Simulate time-based decay
      return v * (1 - driftFactor);
    });

    // Create drifted tensor
    const driftedTensor = {
      ...tensor,
      values: driftedValues,
      lastUpdated: futureTime,
      timeSignature: this._generateTimeSignature(driftedValues, futureTime),
      driftFactor
    };

    // Apply warm-start adjustment for early cycles
    const warmStartFactor = Math.min(1.0, cycleCount / 2); // Full sensitivity by cycle 2
    const adjustedThreshold = this.options.driftDetectionThreshold * (0.5 + (0.5 * warmStartFactor));

    // Detect drift with adjusted threshold
    const driftDetected = driftFactor >= adjustedThreshold;

    // Compensate for drift if detected
    let compensatedValues = [...driftedValues];
    let driftCompensated = false;

    if (driftDetected) {
      compensatedValues = this._compensateForDrift(
        tensor.values,
        driftedValues,
        driftFactor
      );
      driftCompensated = true;
    }

    // Create compensated tensor
    const compensatedTensor = {
      ...driftedTensor,
      values: compensatedValues,
      driftCompensated,
      timeSignature: this._generateTimeSignature(compensatedValues, futureTime)
    };

    return {
      originalTensor: tensor,
      driftedTensor,
      compensatedTensor,
      driftDetected,
      driftCompensated,
      driftFactor,
      timeDiff,
      coherenceMaintained: this._verifyCoherence(tensor.values, compensatedValues)
    };
  }

  /**
   * Verify tensor integrity after drift
   * @param {string} id - Tensor ID
   * @returns {Object} - Integrity verification result
   */
  verifyIntegrity(id) {
    const tensor = this.tensors.get(id);

    if (!tensor) {
      throw new Error(`Tensor not found: ${id}`);
    }

    // Calculate time since creation
    const timeSinceCreation = Date.now() - tensor.createdAt;

    // Calculate expected drift
    const expectedDriftFactor = this._calculateDriftFactor(timeSinceCreation);

    // Calculate drift resistance score
    const driftResistanceScore = tensor.driftCompensated
      ? 1 - (tensor.driftFactor / expectedDriftFactor)
      : 1 - tensor.driftFactor;

    // Verify time signature
    const currentTimeSignature = this._generateTimeSignature(tensor.values, Date.now());
    const timeSignatureValid = Math.abs(currentTimeSignature - tensor.timeSignature) < 0.01;

    // Check if integrity is maintained
    const integrityMaintained = driftResistanceScore > 0.8 && timeSignatureValid;

    return {
      integrityMaintained,
      driftResistanceScore,
      timeSignatureValid,
      timeSinceCreation,
      expectedDriftFactor,
      actualDriftFactor: tensor.driftFactor,
      driftCompensated: tensor.driftCompensated
    };
  }

  /**
   * Get drift history for a tensor
   * @param {string} id - Tensor ID
   * @returns {Array} - Drift history
   */
  getDriftHistory(id) {
    return this.driftHistory.get(id) || [];
  }

  /**
   * Calculate drift factor based on time difference
   * @param {number} timeDiff - Time difference in milliseconds
   * @returns {number} - Drift factor (0-1)
   * @private
   */
  _calculateDriftFactor(timeDiff) {
    // Convert to days
    const timeDiffDays = timeDiff / (24 * 3600 * 1000);

    // Calculate drift factor (max 20% per day)
    return Math.min(this.options.maxDriftThreshold, timeDiffDays * 0.2);
  }

  /**
   * Generate time signature
   * @param {Array} values - Tensor values
   * @param {number} timestamp - Timestamp
   * @returns {number} - Time signature
   * @private
   */
  _generateTimeSignature(values, timestamp) {
    // Calculate value component
    const valueComponent = values.reduce((sum, v) => sum + v, 0) / values.length;

    // Calculate time component
    const timeComponent = (timestamp % 1000000) / 1000000;

    // Combine components
    return (valueComponent * 0.7) + (timeComponent * 0.3);
  }

  /**
   * Compensate for drift
   * @param {Array} originalValues - Original values
   * @param {Array} driftedValues - Drifted values
   * @param {number} driftFactor - Drift factor
   * @returns {Array} - Compensated values
   * @private
   */
  _compensateForDrift(originalValues, driftedValues, driftFactor) {
    // Skip if no drift
    if (driftFactor === 0) {
      return driftedValues;
    }

    // Calculate compensation factor
    const compensationFactor = this.options.driftCompensationFactor;

    // Apply compensation
    return driftedValues.map((v, i) => {
      // Calculate expected value
      const expectedValue = originalValues[i];

      // Calculate compensation
      const compensation = (expectedValue - v) * compensationFactor;

      // Apply compensation
      return v + compensation;
    });
  }

  /**
   * Verify coherence between original and compensated values
   * @param {Array} originalValues - Original values
   * @param {Array} compensatedValues - Compensated values
   * @returns {boolean} - Whether coherence is maintained
   * @private
   */
  _verifyCoherence(originalValues, compensatedValues) {
    // Calculate similarity
    let sumSquaredDiff = 0;
    for (let i = 0; i < originalValues.length; i++) {
      sumSquaredDiff += Math.pow(originalValues[i] - compensatedValues[i], 2);
    }

    const rmsDiff = Math.sqrt(sumSquaredDiff / originalValues.length);
    const similarity = 1 - rmsDiff;

    // Check if coherence is maintained
    return similarity > 0.8;
  }
}

module.exports = TimeDriftAnalyzer;
