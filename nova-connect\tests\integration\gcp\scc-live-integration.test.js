/**
 * NovaConnect - Security Command Center Live Integration Test
 * 
 * This test connects to the actual Google Security Command Center API
 * to validate real-world performance and functionality.
 * 
 * NOTE: This test requires valid GCP credentials with SCC access.
 * Set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point
 * to a service account key file with appropriate permissions.
 */

const { SCCConnector } = require('../../../src/connectors/gcp/scc-connector');
const { TransformationEngine } = require('../../../src/engines/transformation-engine');
const { RemediationEngine } = require('../../../src/engines/remediation-engine');
const { performance } = require('perf_hooks');
const fs = require('fs');
const path = require('path');

// Skip tests if credentials are not available
const hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS && 
  fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);

// Test configuration
const config = {
  organizationId: process.env.GCP_ORGANIZATION_ID || '************',
  projectId: process.env.GCP_PROJECT_ID || 'test-project',
  findingFilter: 'severity="HIGH" OR severity="CRITICAL"',
  maxFindings: 1000,
  normalizationBatchSize: 100
};

describe('Security Command Center Live Integration', () => {
  let sccConnector;
  let transformationEngine;
  let remediationEngine;
  
  beforeAll(async () => {
    // Initialize the connectors and engines
    sccConnector = new SCCConnector();
    transformationEngine = new TransformationEngine();
    remediationEngine = new RemediationEngine();
    
    // Register a test remediation action
    remediationEngine.registerAction('update-firewall-rule', async ({ parameters }) => {
      console.log('Mock updating firewall rule:', parameters);
      return { success: true };
    });
    
    // Initialize the SCC connector with credentials
    if (hasCredentials) {
      try {
        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
        await sccConnector.initialize(credentials);
      } catch (error) {
        console.error('Error initializing SCC connector:', error);
      }
    }
  });
  
  // Skip tests if credentials are not available
  const conditionalTest = hasCredentials ? it : it.skip;
  
  conditionalTest('should retrieve findings from SCC', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    const startTime = performance.now();
    
    // Get findings from SCC
    const { findings, nextPageToken } = await sccConnector.getFindings({
      organizationId: config.organizationId,
      filter: config.findingFilter,
      pageSize: 10
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Retrieved ${findings.length} findings in ${duration.toFixed(2)}ms`);
    console.log(`Average time per finding: ${(duration / Math.max(1, findings.length)).toFixed(2)}ms`);
    
    // Verify findings
    expect(Array.isArray(findings)).toBe(true);
    if (findings.length > 0) {
      const firstFinding = findings[0];
      expect(firstFinding).toHaveProperty('name');
      expect(firstFinding).toHaveProperty('category');
      expect(firstFinding).toHaveProperty('severity');
    }
  }, 30000);
  
  conditionalTest('should normalize findings efficiently', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get findings from SCC
    const { findings } = await sccConnector.getFindings({
      organizationId: config.organizationId,
      filter: config.findingFilter,
      pageSize: 10
    });
    
    if (findings.length === 0) {
      console.log('No findings to normalize');
      return;
    }
    
    const startTime = performance.now();
    
    // Normalize findings
    const normalizedFindings = sccConnector.normalizeFindings(findings);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Normalized ${findings.length} findings in ${duration.toFixed(2)}ms`);
    console.log(`Average time per finding: ${(duration / findings.length).toFixed(2)}ms`);
    
    // Verify normalized findings
    expect(Array.isArray(normalizedFindings)).toBe(true);
    expect(normalizedFindings.length).toBe(findings.length);
    
    if (normalizedFindings.length > 0) {
      const firstNormalized = normalizedFindings[0];
      expect(firstNormalized).toHaveProperty('id');
      expect(firstNormalized).toHaveProperty('severity');
      expect(firstNormalized).toHaveProperty('category');
      expect(firstNormalized).toHaveProperty('createdAt');
      expect(typeof firstNormalized.createdAt).toBe('number');
    }
    
    // Verify normalization speed
    expect(duration / findings.length).toBeLessThan(1); // Less than 1ms per finding
  }, 30000);
  
  conditionalTest('should handle large batch of findings', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get a larger batch of findings
    const allFindings = [];
    let nextPageToken = null;
    
    do {
      const { findings, nextPageToken: token } = await sccConnector.getFindings({
        organizationId: config.organizationId,
        filter: config.findingFilter,
        pageSize: 100,
        pageToken: nextPageToken
      });
      
      allFindings.push(...findings);
      nextPageToken = token;
      
      // Limit the number of findings to avoid long test times
      if (allFindings.length >= config.maxFindings) {
        break;
      }
    } while (nextPageToken);
    
    console.log(`Retrieved ${allFindings.length} findings for batch test`);
    
    if (allFindings.length === 0) {
      console.log('No findings to process');
      return;
    }
    
    // Process findings in batches
    const batchSize = config.normalizationBatchSize;
    const batches = [];
    
    for (let i = 0; i < allFindings.length; i += batchSize) {
      batches.push(allFindings.slice(i, i + batchSize));
    }
    
    console.log(`Processing ${batches.length} batches of ${batchSize} findings each`);
    
    const startTime = performance.now();
    
    // Process each batch
    for (const batch of batches) {
      const normalizedBatch = sccConnector.normalizeFindings(batch);
      expect(normalizedBatch.length).toBe(batch.length);
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Processed ${allFindings.length} findings in ${duration.toFixed(2)}ms`);
    console.log(`Average time per finding: ${(duration / allFindings.length).toFixed(2)}ms`);
    console.log(`Throughput: ${((allFindings.length / duration) * 1000).toFixed(2)} findings/second`);
    
    // Verify processing speed
    const findingsPerSecond = (allFindings.length / duration) * 1000;
    expect(findingsPerSecond).toBeGreaterThan(1000); // At least 1000 findings per second
  }, 120000);
  
  conditionalTest('should create and execute remediation workflow', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get a finding to remediate
    const { findings } = await sccConnector.getFindings({
      organizationId: config.organizationId,
      filter: config.findingFilter,
      pageSize: 1
    });
    
    if (findings.length === 0) {
      console.log('No findings to remediate');
      return;
    }
    
    // Normalize the finding
    const normalizedFindings = sccConnector.normalizeFindings(findings);
    const finding = normalizedFindings[0];
    
    // Create a remediation scenario
    const remediationScenario = {
      id: `remediation-${Date.now()}`,
      type: 'security',
      severity: finding.severity,
      resource: {
        id: finding.resourceName,
        type: 'firewall',
        provider: 'gcp'
      },
      finding,
      remediationSteps: [
        {
          id: 'step-1',
          action: 'update-firewall-rule',
          parameters: {
            ruleName: 'default-allow-all',
            action: 'deny',
            priority: 1000
          }
        }
      ]
    };
    
    const startTime = performance.now();
    
    // Execute the remediation
    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Executed remediation in ${duration.toFixed(2)}ms`);
    
    // Verify remediation result
    expect(remediationResult).toHaveProperty('id');
    expect(remediationResult).toHaveProperty('status');
    expect(remediationResult).toHaveProperty('steps');
    expect(remediationResult.steps.length).toBe(1);
    expect(remediationResult.steps[0].success).toBe(true);
    
    // Verify remediation speed
    expect(duration).toBeLessThan(8000); // Less than 8 seconds
  }, 30000);
});
