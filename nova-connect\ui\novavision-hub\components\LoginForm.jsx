/**
 * LoginForm Component
 * 
 * A component for user authentication.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useAuth } from '../auth/AuthContext';
import { useTheme } from '../theme/ThemeContext';
import { Animated } from './Animated';

/**
 * LoginForm component
 * 
 * @param {Object} props - Component props
 * @param {Function} [props.onSuccess] - Callback when login is successful
 * @param {Function} [props.onError] - Callback when login fails
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} LoginForm component
 */
const LoginForm = ({
  onSuccess,
  onError,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { signInWithEmailAndPassword, signInWithProvider, resetPassword } = useAuth();
  
  // State
  const [formState, setFormState] = useState('login'); // login, register, reset
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);
    
    try {
      if (formState === 'login') {
        // Login
        const user = await signInWithEmailAndPassword(email, password);
        
        if (onSuccess) {
          onSuccess(user);
        }
      } else if (formState === 'register') {
        // Register
        if (password !== confirmPassword) {
          throw new Error('Passwords do not match');
        }
        
        const user = await signInWithProvider('email', {
          email,
          password,
          displayName
        });
        
        if (onSuccess) {
          onSuccess(user);
        }
      } else if (formState === 'reset') {
        // Reset password
        await resetPassword(email);
        
        setSuccessMessage('Password reset email sent. Please check your inbox.');
        setFormState('login');
      }
    } catch (err) {
      console.error('Authentication error:', err);
      setError(err.message);
      
      if (onError) {
        onError(err);
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle social login
  const handleSocialLogin = async (provider) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const user = await signInWithProvider(provider);
      
      if (onSuccess) {
        onSuccess(user);
      }
    } catch (err) {
      console.error('Social login error:', err);
      setError(err.message);
      
      if (onError) {
        onError(err);
      }
    } finally {
      setIsLoading(false);
    }
  };
  
  // Switch form state
  const switchFormState = (state) => {
    setFormState(state);
    setError(null);
    setSuccessMessage(null);
  };
  
  return (
    <Animated
      animation="fadeIn"
      className={`bg-surface p-6 rounded-lg shadow-lg max-w-md w-full ${className}`}
      style={style}
    >
      {/* Form header */}
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-textPrimary">
          {formState === 'login' ? 'Sign In' : formState === 'register' ? 'Create Account' : 'Reset Password'}
        </h2>
        <p className="text-textSecondary mt-2">
          {formState === 'login' ? 'Sign in to your account' : formState === 'register' ? 'Create a new account' : 'Reset your password'}
        </p>
      </div>
      
      {/* Error message */}
      {error && (
        <div className="bg-error bg-opacity-10 border border-error text-error px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Success message */}
      {successMessage && (
        <div className="bg-success bg-opacity-10 border border-success text-success px-4 py-3 rounded mb-4">
          {successMessage}
        </div>
      )}
      
      {/* Form */}
      <form onSubmit={handleSubmit}>
        {/* Email */}
        <div className="mb-4">
          <label htmlFor="email" className="block text-textPrimary font-medium mb-1">
            Email
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
            placeholder="Enter your email"
            required
          />
        </div>
        
        {/* Password (for login and register) */}
        {formState !== 'reset' && (
          <div className="mb-4">
            <label htmlFor="password" className="block text-textPrimary font-medium mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
              placeholder="Enter your password"
              required
            />
          </div>
        )}
        
        {/* Confirm Password (for register) */}
        {formState === 'register' && (
          <div className="mb-4">
            <label htmlFor="confirmPassword" className="block text-textPrimary font-medium mb-1">
              Confirm Password
            </label>
            <input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
              placeholder="Confirm your password"
              required
            />
          </div>
        )}
        
        {/* Display Name (for register) */}
        {formState === 'register' && (
          <div className="mb-4">
            <label htmlFor="displayName" className="block text-textPrimary font-medium mb-1">
              Display Name
            </label>
            <input
              id="displayName"
              type="text"
              value={displayName}
              onChange={(e) => setDisplayName(e.target.value)}
              className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
              placeholder="Enter your name"
              required
            />
          </div>
        )}
        
        {/* Submit button */}
        <button
          type="submit"
          className="w-full bg-primary text-primaryContrast py-2 px-4 rounded-md hover:bg-primaryDark transition-colors duration-200 mt-2"
          disabled={isLoading}
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
          ) : (
            formState === 'login' ? 'Sign In' : formState === 'register' ? 'Create Account' : 'Reset Password'
          )}
        </button>
      </form>
      
      {/* Social login */}
      {formState === 'login' && (
        <div className="mt-4">
          <div className="relative flex items-center justify-center">
            <div className="border-t border-divider w-full"></div>
            <div className="bg-surface px-2 text-textSecondary text-sm absolute">or continue with</div>
          </div>
          
          <div className="flex space-x-2 mt-4">
            <button
              type="button"
              className="flex-1 flex items-center justify-center bg-background border border-divider text-textPrimary py-2 px-4 rounded-md hover:bg-surface transition-colors duration-200"
              onClick={() => handleSocialLogin('google')}
              disabled={isLoading}
            >
              <span>Google</span>
            </button>
            
            <button
              type="button"
              className="flex-1 flex items-center justify-center bg-background border border-divider text-textPrimary py-2 px-4 rounded-md hover:bg-surface transition-colors duration-200"
              onClick={() => handleSocialLogin('microsoft')}
              disabled={isLoading}
            >
              <span>Microsoft</span>
            </button>
          </div>
        </div>
      )}
      
      {/* Form switcher */}
      <div className="mt-4 text-center text-sm">
        {formState === 'login' ? (
          <div>
            <p className="text-textSecondary">
              Don't have an account?{' '}
              <button
                type="button"
                className="text-primary hover:underline"
                onClick={() => switchFormState('register')}
              >
                Sign up
              </button>
            </p>
            <p className="text-textSecondary mt-2">
              <button
                type="button"
                className="text-primary hover:underline"
                onClick={() => switchFormState('reset')}
              >
                Forgot password?
              </button>
            </p>
          </div>
        ) : (
          <p className="text-textSecondary">
            {formState === 'register' ? 'Already have an account?' : 'Remember your password?'}{' '}
            <button
              type="button"
              className="text-primary hover:underline"
              onClick={() => switchFormState('login')}
            >
              Sign in
            </button>
          </p>
        )}
      </div>
    </Animated>
  );
};

LoginForm.propTypes = {
  onSuccess: PropTypes.func,
  onError: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default LoginForm;
