/**
 * NovaCore Workflow Service
 *
 * This service provides functionality for managing workflows.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const { Workflow, WorkflowTemplate, WorkflowExecution } = require('../models');
const { WorkflowExecutionEngine } = require('../engines');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const { v4: uuidv4 } = require('uuid');

class WorkflowService {
  /**
   * Create a new workflow
   * @param {Object} data - Workflow data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created workflow
   */
  async createWorkflow(data, userId) {
    try {
      logger.info('Creating new workflow', { organizationId: data.organizationId });

      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;

      // Create workflow
      const workflow = new Workflow(data);
      await workflow.save();

      logger.info('Workflow created successfully', { id: workflow._id });

      return workflow;
    } catch (error) {
      logger.error('Error creating workflow', { error });
      throw error;
    }
  }

  /**
   * Create workflow from template
   * @param {string} templateId - Template ID
   * @param {Object} data - Additional workflow data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created workflow
   */
  async createWorkflowFromTemplate(templateId, data, userId) {
    try {
      logger.info('Creating workflow from template', { templateId });

      // Get template
      const template = await WorkflowTemplate.findById(templateId);

      if (!template) {
        throw new NotFoundError(`Workflow template with ID ${templateId} not found`);
      }

      // Create workflow data from template
      const workflowData = {
        name: data.name || template.name,
        description: data.description || template.description,
        organizationId: data.organizationId || template.organizationId,
        type: template.type,
        category: template.category,
        status: 'draft',
        executionStatus: 'not_started',
        priority: template.priority,
        stages: JSON.parse(JSON.stringify(template.stages)),
        triggers: JSON.parse(JSON.stringify(template.triggers)),
        frameworks: template.frameworks,
        tags: template.tags,
        createdBy: userId,
        updatedBy: userId
      };

      // Apply data model and variables from template
      if (template.dataModel && template.dataModel.length > 0) {
        workflowData.dataModel = {};

        for (const field of template.dataModel) {
          if (field.defaultValue !== undefined) {
            workflowData.dataModel[field.name] = field.defaultValue;
          }
        }
      }

      if (template.variables && template.variables.length > 0) {
        workflowData.variables = {};

        for (const variable of template.variables) {
          if (variable.defaultValue !== undefined) {
            workflowData.variables[variable.name] = variable.defaultValue;
          }
        }
      }

      // Apply additional data
      if (data.dataModel) {
        workflowData.dataModel = {
          ...workflowData.dataModel,
          ...data.dataModel
        };
      }

      if (data.variables) {
        workflowData.variables = {
          ...workflowData.variables,
          ...data.variables
        };
      }

      if (data.relatedEntities) {
        workflowData.relatedEntities = data.relatedEntities;
      }

      if (data.dueDate) {
        workflowData.dueDate = data.dueDate;
      }

      // Create workflow
      const workflow = await this.createWorkflow(workflowData, userId);

      return workflow;
    } catch (error) {
      logger.error('Error creating workflow from template', { templateId, error });
      throw error;
    }
  }

  /**
   * Get all workflows for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Workflows with pagination info
   */
  async getAllWorkflows(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;

      // Build query
      const query = { organizationId };

      // Apply filters
      if (filter.type) {
        query.type = filter.type;
      }

      if (filter.category) {
        query.category = filter.category;
      }

      if (filter.status) {
        query.status = filter.status;
      }

      if (filter.executionStatus) {
        query.executionStatus = filter.executionStatus;
      }

      if (filter.priority) {
        query.priority = filter.priority;
      }

      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }

      if (filter.frameworks) {
        query.frameworks = { $all: Array.isArray(filter.frameworks) ? filter.frameworks : [filter.frameworks] };
      }

      if (filter.search) {
        query.$or = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }

      // Execute query with pagination
      const skip = (page - 1) * limit;

      const [workflows, total] = await Promise.all([
        Workflow.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Workflow.countDocuments(query)
      ]);

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      return {
        data: workflows,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting workflows', { error });
      throw error;
    }
  }

  /**
   * Get workflow by ID
   * @param {string} id - Workflow ID
   * @returns {Promise<Object>} - Workflow
   */
  async getWorkflowById(id) {
    try {
      const workflow = await Workflow.findById(id);

      if (!workflow) {
        throw new NotFoundError(`Workflow with ID ${id} not found`);
      }

      return workflow;
    } catch (error) {
      logger.error('Error getting workflow by ID', { id, error });
      throw error;
    }
  }

  /**
   * Update workflow
   * @param {string} id - Workflow ID
   * @param {Object} data - Updated workflow data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated workflow
   */
  async updateWorkflow(id, data, userId) {
    try {
      // Get existing workflow
      const workflow = await this.getWorkflowById(id);

      // Validate status transition
      if (data.status && data.status !== workflow.status) {
        this._validateStatusTransition(workflow.status, data.status);
      }

      // Set updated by
      data.updatedBy = userId;

      // Update workflow
      Object.assign(workflow, data);
      await workflow.save();

      logger.info('Workflow updated successfully', { id });

      return workflow;
    } catch (error) {
      logger.error('Error updating workflow', { id, error });
      throw error;
    }
  }

  /**
   * Delete workflow
   * @param {string} id - Workflow ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteWorkflow(id) {
    try {
      const result = await Workflow.findByIdAndDelete(id);

      if (!result) {
        throw new NotFoundError(`Workflow with ID ${id} not found`);
      }

      logger.info('Workflow deleted successfully', { id });

      return true;
    } catch (error) {
      logger.error('Error deleting workflow', { id, error });
      throw error;
    }
  }

  /**
   * Activate workflow
   * @param {string} id - Workflow ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Activated workflow
   */
  async activateWorkflow(id, userId) {
    try {
      // Get workflow
      const workflow = await this.getWorkflowById(id);

      // Validate workflow status
      if (workflow.status !== 'draft') {
        throw new ValidationError(`Cannot activate workflow with status ${workflow.status}`);
      }

      // Update workflow status
      workflow.status = 'active';
      workflow.updatedBy = userId;

      await workflow.save();

      logger.info('Workflow activated successfully', { id });

      return workflow;
    } catch (error) {
      logger.error('Error activating workflow', { id, error });
      throw error;
    }
  }

  /**
   * Start workflow execution
   * @param {string} id - Workflow ID
   * @param {Object} data - Execution data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Workflow execution
   */
  async startWorkflow(id, data = {}, userId) {
    try {
      // Get workflow
      const workflow = await this.getWorkflowById(id);

      // Validate workflow status
      if (workflow.status !== 'active') {
        throw new ValidationError(`Cannot start workflow with status ${workflow.status}`);
      }

      // Create execution data
      const executionData = {
        workflowId: workflow._id,
        organizationId: workflow.organizationId,
        name: workflow.name,
        description: workflow.description,
        status: 'pending',
        priority: workflow.priority,
        startedAt: new Date(),
        dueAt: data.dueAt || workflow.dueDate,
        triggerType: 'manual',
        triggerDetails: {
          triggeredBy: userId
        },
        data: data.data || {},
        variables: workflow.variables || {},
        relatedEntities: data.relatedEntities || workflow.relatedEntities || [],
        tags: workflow.tags || [],
        createdBy: userId,
        updatedBy: userId
      };

      // Create stage executions
      executionData.stages = workflow.stages.map(stage => ({
        stageId: stage.id,
        name: stage.name,
        status: 'pending',
        dueAt: executionData.dueAt
      }));

      // Create task executions
      executionData.tasks = [];

      for (const stage of workflow.stages) {
        for (const task of stage.tasks) {
          executionData.tasks.push({
            taskId: task.id,
            stageId: stage.id,
            name: task.name,
            type: task.type,
            status: 'pending',
            assignedTo: task.assignedTo,
            assignedRole: task.assignedRole,
            dueAt: executionData.dueAt
          });
        }
      }

      // Set current stage to first stage
      if (workflow.stages.length > 0) {
        executionData.currentStageId = workflow.stages[0].id;
      }

      // Create execution
      const execution = new WorkflowExecution(executionData);
      await execution.save();

      // Update workflow execution status
      workflow.executionStatus = 'in_progress';
      workflow.updatedBy = userId;
      await workflow.save();

      // Initialize workflow execution
      await WorkflowExecutionEngine.initializeExecution(execution);

      // Process workflow execution
      await WorkflowExecutionEngine.processExecution(execution._id);

      logger.info('Workflow execution started successfully', { id, executionId: execution._id });

      return execution;
    } catch (error) {
      logger.error('Error starting workflow', { id, error });
      throw error;
    }
  }

  /**
   * Get workflow executions
   * @param {string} id - Workflow ID
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Workflow executions with pagination info
   */
  async getWorkflowExecutions(id, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;

      // Get workflow
      await this.getWorkflowById(id);

      // Execute query with pagination
      const skip = (page - 1) * limit;

      const [executions, total] = await Promise.all([
        WorkflowExecution.find({ workflowId: id })
          .sort(sort)
          .skip(skip)
          .limit(limit),
        WorkflowExecution.countDocuments({ workflowId: id })
      ]);

      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      return {
        data: executions,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting workflow executions', { id, error });
      throw error;
    }
  }

  /**
   * Get execution by ID
   * @param {string} id - Execution ID
   * @returns {Promise<Object>} - Workflow execution
   */
  async getExecutionById(id) {
    try {
      const execution = await WorkflowExecution.findById(id);

      if (!execution) {
        throw new NotFoundError(`Workflow execution with ID ${id} not found`);
      }

      return execution;
    } catch (error) {
      logger.error('Error getting workflow execution by ID', { id, error });
      throw error;
    }
  }

  /**
   * Complete task in workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} taskId - Task ID
   * @param {Object} data - Task completion data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async completeTask(executionId, taskId, data, userId) {
    try {
      logger.info('Completing task in workflow execution', { executionId, taskId });

      // Complete task using execution engine
      const execution = await WorkflowExecutionEngine.completeTask(executionId, taskId, data, userId);

      return execution;
    } catch (error) {
      logger.error('Error completing task', { executionId, taskId, error });
      throw error;
    }
  }

  /**
   * Fail task in workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} taskId - Task ID
   * @param {Object} data - Task failure data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async failTask(executionId, taskId, data, userId) {
    try {
      logger.info('Failing task in workflow execution', { executionId, taskId });

      // Fail task using execution engine
      const execution = await WorkflowExecutionEngine.failTask(executionId, taskId, data, userId);

      return execution;
    } catch (error) {
      logger.error('Error failing task', { executionId, taskId, error });
      throw error;
    }
  }

  /**
   * Skip task in workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} taskId - Task ID
   * @param {Object} data - Task skip data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async skipTask(executionId, taskId, data, userId) {
    try {
      logger.info('Skipping task in workflow execution', { executionId, taskId });

      // Skip task using execution engine
      const execution = await WorkflowExecutionEngine.skipTask(executionId, taskId, data, userId);

      return execution;
    } catch (error) {
      logger.error('Error skipping task', { executionId, taskId, error });
      throw error;
    }
  }

  /**
   * Pause workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async pauseExecution(executionId, userId) {
    try {
      logger.info('Pausing workflow execution', { executionId });

      // Pause execution using execution engine
      const execution = await WorkflowExecutionEngine.pauseExecution(executionId, userId);

      return execution;
    } catch (error) {
      logger.error('Error pausing workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Resume workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async resumeExecution(executionId, userId) {
    try {
      logger.info('Resuming workflow execution', { executionId });

      // Resume execution using execution engine
      const execution = await WorkflowExecutionEngine.resumeExecution(executionId, userId);

      return execution;
    } catch (error) {
      logger.error('Error resuming workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Cancel workflow execution
   * @param {string} executionId - Execution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated execution
   */
  async cancelExecution(executionId, userId) {
    try {
      logger.info('Cancelling workflow execution', { executionId });

      // Cancel execution using execution engine
      const execution = await WorkflowExecutionEngine.cancelExecution(executionId, userId);

      return execution;
    } catch (error) {
      logger.error('Error cancelling workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Process workflow execution
   * @param {string} executionId - Execution ID
   * @returns {Promise<Object>} - Updated execution
   */
  async processExecution(executionId) {
    try {
      logger.info('Processing workflow execution', { executionId });

      // Process execution using execution engine
      const execution = await WorkflowExecutionEngine.processExecution(executionId);

      return execution;
    } catch (error) {
      logger.error('Error processing workflow execution', { executionId, error });
      throw error;
    }
  }

  /**
   * Validate workflow status transition
   * @param {string} currentStatus - Current status
   * @param {string} newStatus - New status
   * @private
   */
  _validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = {
      'draft': ['active', 'archived'],
      'active': ['inactive', 'archived'],
      'inactive': ['active', 'archived'],
      'archived': []
    };

    if (!validTransitions[currentStatus].includes(newStatus)) {
      throw new ValidationError(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}

module.exports = new WorkflowService();
