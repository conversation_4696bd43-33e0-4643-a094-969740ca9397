/**
 * NovaFuse Compliance App Store - Main JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function(tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Initialize popovers
  const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
  popoverTriggerList.map(function(popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl);
  });
  
  // Search functionality
  const searchInput = document.getElementById('searchInput');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      const searchTerm = this.value.toLowerCase();
      const connectorCards = document.querySelectorAll('.connector-card');
      
      connectorCards.forEach(card => {
        const title = card.querySelector('.card-title').textContent.toLowerCase();
        const description = card.querySelector('.card-text').textContent.toLowerCase();
        const badges = Array.from(card.querySelectorAll('.badge')).map(badge => badge.textContent.toLowerCase());
        
        const matchesSearch = 
          title.includes(searchTerm) || 
          description.includes(searchTerm) || 
          badges.some(badge => badge.includes(searchTerm));
        
        card.closest('.col-md-4').style.display = matchesSearch ? 'block' : 'none';
      });
    });
  }
  
  // Filter functionality
  const filterCheckboxes = document.querySelectorAll('.filter-checkbox');
  if (filterCheckboxes.length > 0) {
    filterCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', applyFilters);
    });
  }
  
  function applyFilters() {
    const selectedCategories = Array.from(document.querySelectorAll('.category-filter:checked')).map(cb => cb.value);
    const selectedFrameworks = Array.from(document.querySelectorAll('.framework-filter:checked')).map(cb => cb.value);
    const selectedRegions = Array.from(document.querySelectorAll('.region-filter:checked')).map(cb => cb.value);
    const selectedPricing = Array.from(document.querySelectorAll('.pricing-filter:checked')).map(cb => cb.value);
    
    const connectorCards = document.querySelectorAll('.connector-card');
    
    connectorCards.forEach(card => {
      const cardCategories = Array.from(card.querySelectorAll('[data-category]')).map(el => el.dataset.category);
      const cardFrameworks = Array.from(card.querySelectorAll('[data-framework]')).map(el => el.dataset.framework);
      const cardRegions = Array.from(card.querySelectorAll('[data-region]')).map(el => el.dataset.region);
      const cardPricing = card.querySelector('[data-pricing]')?.dataset.pricing;
      
      const matchesCategory = selectedCategories.length === 0 || selectedCategories.some(cat => cardCategories.includes(cat));
      const matchesFramework = selectedFrameworks.length === 0 || selectedFrameworks.some(framework => cardFrameworks.includes(framework));
      const matchesRegion = selectedRegions.length === 0 || selectedRegions.some(region => cardRegions.includes(region));
      const matchesPricing = selectedPricing.length === 0 || selectedPricing.includes(cardPricing);
      
      const matches = matchesCategory && matchesFramework && matchesRegion && matchesPricing;
      card.closest('.col-md-4').style.display = matches ? 'block' : 'none';
    });
  }
  
  // Sort functionality
  const sortSelect = document.getElementById('sortSelect');
  if (sortSelect) {
    sortSelect.addEventListener('change', function() {
      const sortBy = this.value;
      const connectorContainer = document.querySelector('.connector-container');
      const connectorCards = Array.from(document.querySelectorAll('.connector-card'));
      
      connectorCards.sort((a, b) => {
        if (sortBy === 'name-asc') {
          return a.querySelector('.card-title').textContent.localeCompare(b.querySelector('.card-title').textContent);
        } else if (sortBy === 'name-desc') {
          return b.querySelector('.card-title').textContent.localeCompare(a.querySelector('.card-title').textContent);
        } else if (sortBy === 'price-asc') {
          const priceA = parseFloat(a.querySelector('[data-price]')?.dataset.price || '0');
          const priceB = parseFloat(b.querySelector('[data-price]')?.dataset.price || '0');
          return priceA - priceB;
        } else if (sortBy === 'price-desc') {
          const priceA = parseFloat(a.querySelector('[data-price]')?.dataset.price || '0');
          const priceB = parseFloat(b.querySelector('[data-price]')?.dataset.price || '0');
          return priceB - priceA;
        } else if (sortBy === 'rating-desc') {
          const ratingA = parseFloat(a.querySelector('[data-rating]')?.dataset.rating || '0');
          const ratingB = parseFloat(b.querySelector('[data-rating]')?.dataset.rating || '0');
          return ratingB - ratingA;
        } else if (sortBy === 'newest') {
          const dateA = new Date(a.querySelector('[data-date]')?.dataset.date || '');
          const dateB = new Date(b.querySelector('[data-date]')?.dataset.date || '');
          return dateB - dateA;
        }
        return 0;
      });
      
      // Reappend sorted cards
      connectorCards.forEach(card => {
        connectorContainer.appendChild(card.closest('.col-md-4'));
      });
    });
  }
  
  // Connector details page - tabs
  const connectorTabs = document.querySelectorAll('#connectorTabs .nav-link');
  if (connectorTabs.length > 0) {
    connectorTabs.forEach(tab => {
      tab.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active class from all tabs and tab panes
        connectorTabs.forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active', 'show'));
        
        // Add active class to clicked tab and corresponding tab pane
        this.classList.add('active');
        const target = document.querySelector(this.getAttribute('href'));
        target.classList.add('active', 'show');
      });
    });
  }
  
  // Handle URL parameters
  function handleUrlParams() {
    const urlParams = new URLSearchParams(window.location.search);
    
    // Handle category parameter
    const category = urlParams.get('category');
    if (category) {
      const categoryCheckbox = document.querySelector(`.category-filter[value="${category}"]`);
      if (categoryCheckbox) {
        categoryCheckbox.checked = true;
        applyFilters();
      }
    }
    
    // Handle framework parameter
    const framework = urlParams.get('framework');
    if (framework) {
      const frameworkCheckbox = document.querySelector(`.framework-filter[value="${framework}"]`);
      if (frameworkCheckbox) {
        frameworkCheckbox.checked = true;
        applyFilters();
      }
    }
    
    // Handle search parameter
    const search = urlParams.get('search');
    if (search && searchInput) {
      searchInput.value = search;
      searchInput.dispatchEvent(new Event('input'));
    }
  }
  
  // Call handleUrlParams on page load
  handleUrlParams();
});
