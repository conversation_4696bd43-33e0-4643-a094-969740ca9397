# NovaFuse Universal Platform - Coverage Tests

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )

    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for test results and coverage reports
Write-ColorOutput "Creating directories for test results and coverage reports..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/junit" | Out-Null
New-Item -ItemType Directory -Force -Path "./coverage" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - Coverage Tests" -ForegroundColor Cyan
Write-ColorOutput "=============================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run tests with coverage reporting." -ForegroundColor Cyan
Write-ColorOutput "Coverage threshold is set to 81% for branches, functions, lines, and statements." -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Run the tests with coverage
Write-ColorOutput "Running tests with coverage..." -ForegroundColor Green
npx jest --coverage --coverageThreshold='{"global":{"branches":81,"functions":81,"lines":81,"statements":81}}'

# Generate a coverage badge
Write-ColorOutput "`nGenerating coverage badge..." -ForegroundColor Green
npx jest-coverage-badges --output ./coverage/badges

# Display summary
Write-ColorOutput "`nTesting completed!" -ForegroundColor Green
Write-ColorOutput "Coverage report is available in ./coverage/lcov-report/index.html" -ForegroundColor Green
Write-ColorOutput "Coverage badges are available in ./coverage/badges" -ForegroundColor Green

# Open the coverage report
Write-ColorOutput "`nOpening coverage report..." -ForegroundColor Green
Start-Process "./coverage/lcov-report/index.html"
