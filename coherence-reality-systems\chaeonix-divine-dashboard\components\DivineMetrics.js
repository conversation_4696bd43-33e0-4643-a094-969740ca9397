/**
 * FUNDAMENTAL METRICS COMPONENT
 * Key performance indicators and sacred ratios
 * Real-time display of fundamental intelligence metrics
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  BoltIcon,
  EyeIcon,
  StarIcon,
  TrendingUpIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

import { PHI, FIBONACCI_LEVELS, COHERENCE_THRESHOLDS } from '../utils/chaeonixConstants';

export default function FundamentalMetrics({ engineStatus, marketData }) {
  const [metrics, setMetrics] = useState({
    coherence_level: 0.95,
    phi_alignment: 1.618,
    trinity_score: 0.89,
    engine_health: 0.97
  });

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/api/dashboard/realtime-status');
        const data = await response.json();

        if (data.success) {
          const divineMetrics = data.divine_metrics;
          setMetrics({
            coherence_level: divineMetrics.coherence_level / 100, // Convert from percentage
            phi_alignment: divineMetrics.phi_alignment,
            trinity_score: divineMetrics.trinity_score / 100,
            engine_health: divineMetrics.engine_health / 100
          });
        }
      } catch (error) {
        console.error('Metrics fetch error:', error);
        // Fallback to simulated data
        setMetrics({
          coherence_level: 0.75 + Math.random() * 0.2,
          phi_alignment: 1.618 * (0.8 + Math.random() * 0.3),
          trinity_score: 0.70 + Math.random() * 0.25,
          engine_health: 0.85 + Math.random() * 0.12
        });
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, [engineStatus]);

  return (
    <div className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6">
      <div className="flex items-center space-x-2 mb-4">
        <ChartBarIcon className="w-5 h-5 text-green-400" />
        <h3 className="text-lg font-semibold text-white">
          Fundamental Metrics
        </h3>
      </div>

      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-400">Coherence Level</span>
          <span className="text-green-400 font-bold">
            {(metrics.coherence_level * 100).toFixed(1)}%
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-400">φ-Alignment</span>
          <span className="text-blue-400 font-bold">
            {metrics.phi_alignment.toFixed(3)}
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-400">Trinity Score</span>
          <span className="text-purple-400 font-bold">
            {(metrics.trinity_score * 100).toFixed(1)}%
          </span>
        </div>

        <div className="flex justify-between items-center">
          <span className="text-gray-400">Engine Health</span>
          <span className="text-yellow-400 font-bold">
            {(metrics.engine_health * 100).toFixed(1)}%
          </span>
        </div>
      </div>
    </div>
  );
}
