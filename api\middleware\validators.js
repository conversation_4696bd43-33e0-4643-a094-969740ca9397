const { body, param, validationResult } = require('express-validator');
const { Connector } = require('../models');

/**
 * Middleware to validate request data
 */
const validate = (validations) => {
  return async (req, res, next) => {
    await Promise.all(validations.map(validation => validation.run(req)));

    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }

    return res.status(400).json({
      error: true,
      message: 'Validation error',
      errors: errors.array()
    });
  };
};

/**
 * Validate user registration data
 */
const validateRegistration = validate([
  body('email')
    .isEmail().withMessage('Must be a valid email address')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 }).withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('firstName')
    .trim()
    .isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName')
    .trim()
    .isLength({ min: 1 }).withMessage('Last name is required')
]);

/**
 * Validate login data
 */
const validateLogin = validate([
  body('email')
    .isEmail().withMessage('Must be a valid email address')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 1 }).withMessage('Password is required')
]);

/**
 * Validate user update data
 */
const validateUserUpdate = validate([
  body('email')
    .optional()
    .isEmail().withMessage('Must be a valid email address')
    .normalizeEmail(),
  body('firstName')
    .optional()
    .trim()
    .isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName')
    .optional()
    .trim()
    .isLength({ min: 1 }).withMessage('Last name is required'),
  body('company')
    .optional()
    .trim(),
  body('phone')
    .optional()
    .trim()
]);

/**
 * Validate connector ID
 */
const validateConnectorId = validate([
  param('id')
    .custom(async (value) => {
      const connector = await Connector.findById(value);
      if (!connector) {
        throw new Error('Connector not found');
      }
      return true;
    })
]);

/**
 * Validate connector submission data
 */
const validateSubmission = validate([
  body('connectorName')
    .trim()
    .isLength({ min: 1 }).withMessage('Connector name is required'),
  body('vendorName')
    .trim()
    .isLength({ min: 1 }).withMessage('Vendor name is required'),
  body('description')
    .trim()
    .isLength({ min: 1, max: 150 }).withMessage('Description is required and must be less than 150 characters'),
  body('category')
    .isIn(['data-privacy', 'security', 'healthcare', 'financial', 'other']).withMessage('Invalid category'),
  body('framework')
    .isIn(['gdpr', 'hipaa', 'soc2', 'pci-dss', 'iso-27001', 'ccpa', 'nist', 'finra', 'fedramp', 'other']).withMessage('Invalid framework'),
  body('price')
    .trim()
    .isLength({ min: 1 }).withMessage('Price is required')
]);

module.exports = {
  validateRegistration,
  validateLogin,
  validateUserUpdate,
  validateConnectorId,
  validateSubmission
};
