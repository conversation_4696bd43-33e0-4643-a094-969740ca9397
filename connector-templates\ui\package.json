{"name": "novafuse-connector-builder", "version": "1.0.0", "description": "NovaFuse Universal API Connector Builder UI", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": ["novafuse", "connector", "api", "integration", "builder"], "author": "NovaGRC", "license": "UNLICENSED", "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@monaco-editor/react": "^4.4.6", "@mui/icons-material": "^5.11.11", "@mui/material": "^5.11.11", "axios": "^1.3.4", "jsonpath": "^1.1.1", "jsonschema": "^1.4.1", "next": "^13.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.43.5", "react-json-view": "^1.21.3", "react-query": "^3.39.3", "uuid": "^9.0.0"}, "devDependencies": {"eslint": "^8.35.0", "eslint-config-next": "^13.2.3"}}