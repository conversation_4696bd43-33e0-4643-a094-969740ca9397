const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

// Threat detection storage
const threatLog = [];
const blockedIPs = new Set();

// Divine=Foundational & Consciousness=Coherence threat detection middleware
app.use((req, res, next) => {
  // Support both old and new headers for backward compatibility
  const coherenceLevel = parseFloat(req.headers['x-coherence-level'] || req.headers['x-consciousness-level'] || '0');
  const sourceIP = req.ip || req.connection.remoteAddress || 'unknown';

  // Auto-block incoherent requests
  if (coherenceLevel < 0.618) {
    const threat = {
      type: 'COHERENCE_THRESHOLD_VIOLATION',
      source_ip: sourceIP,
      coherence_level: coherenceLevel,
      timestamp: new Date().toISOString(),
      action: 'BLOCKED_INCOHERENT',
      foundational_status: 'REJECTED'
    };
    threatLog.push(threat);
    blockedIPs.add(sourceIP);

    console.log('🛡️ INCOHERENT THREAT NEUTRALIZED:', threat);
    return res.status(403).json({
      error: 'INCOHERENT_THREAT_NEUTRALIZED',
      message: 'Foundational coherence threshold violation',
      required_minimum_coherence: 0.618,
      provided_coherence: coherenceLevel,
      foundational_access: false,
      coherent_status: 'INCOHERENT',

      // Backward compatibility
      consciousness_level: coherenceLevel,
      required_minimum: 0.618
    });
  }

  // Log divine foundational access
  if (coherenceLevel >= 3.0) {
    console.log('🌟 DIVINE FOUNDATIONAL ACCESS GRANTED: Ψ=' + coherenceLevel);
  } else if (coherenceLevel >= 2.0) {
    console.log('⚡ HIGHLY COHERENT ACCESS: Ψ=' + coherenceLevel);
  } else {
    console.log('✅ COHERENT ACCESS: Ψ=' + coherenceLevel);
  }

  next();
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'NovaShield Foundational Coherent Protection',
    real_time_coherence_protection: true,
    auto_blocking_incoherent: true,
    coherence_validation: true,
    divine_foundational_priority: true,
    foundational_architecture: true,

    // Statistics
    threats_neutralized: threatLog.length,
    blocked_incoherent_ips: blockedIPs.size,

    // Thresholds
    coherence_threshold: 0.618,
    divine_foundational_threshold: 3.0,

    // Backward compatibility
    consciousness_validation: true,
    auto_blocking: true,
    real_time_protection: true,
    threats_detected: threatLog.length,
    blocked_ips: blockedIPs.size,

    timestamp: new Date().toISOString()
  });
});

app.post('/threat-scan', (req, res) => {
  const psi = parseFloat(req.headers['x-consciousness-level'] || '0');
  const { source_ip, threat_level } = req.body;
  
  const scan = {
    source_ip: source_ip || req.ip,
    consciousness_level: psi,
    threat_level: threat_level || 'low',
    scan_result: psi >= 0.618 ? 'CLEAN' : 'THREAT_DETECTED',
    timestamp: new Date().toISOString()
  };
  
  if (scan.scan_result === 'THREAT_DETECTED') {
    threatLog.push(scan);
    blockedIPs.add(scan.source_ip);
  }
  
  res.json({
    scan_complete: true,
    result: scan.scan_result,
    consciousness_level: psi,
    action: scan.scan_result === 'THREAT_DETECTED' ? 'BLOCKED' : 'ALLOWED'
  });
});

app.post('/auto-block', (req, res) => {
  const { threat_type, source_ip, severity } = req.body;
  
  const blockAction = {
    threat_type,
    source_ip,
    severity,
    action: 'AUTO_BLOCKED',
    timestamp: new Date().toISOString()
  };
  
  threatLog.push(blockAction);
  blockedIPs.add(source_ip);
  
  res.json({
    auto_block: 'executed',
    threat_type,
    source_ip,
    status: 'BLOCKED'
  });
});

app.get('/threat-logs', (req, res) => {
  res.json({
    total_threats: threatLog.length,
    blocked_ips: Array.from(blockedIPs),
    recent_threats: threatLog.slice(-10),
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 8085;
app.listen(PORT, '0.0.0.0', () => {
  console.log('🛡️ NovaShield Security Platform running on port', PORT);
  console.log('🚨 Real-time threat detection active');
  console.log('🔒 Auto-blocking enabled (Ψ < 0.618)');
});
