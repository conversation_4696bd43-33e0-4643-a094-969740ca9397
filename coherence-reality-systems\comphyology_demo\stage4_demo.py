from kethernet import KetherNet, generate_psi_signature

# Create KetherNet
kether_net = KetherNet()

# Simulate valid transactions
print("Adding valid blocks...")
kether_net.add_block("Transaction 1", generate_psi_signature("Transaction 1"))
kether_net.add_block("Transaction 2", generate_psi_signature("Transaction 2"))

# Attempt to add invalid block
print("\nAttempting to add invalid block...")
invalid_block = kether_net.add_block("Malicious Transaction", generate_psi_signature("Malicious Transaction", valid=False))

# Validate chain
print("\nValidating entire chain:")
for i, block in enumerate(kether_net.chain):
    valid = "VALID" if kether_net.validate_block(block) else "INVALID"
    print(f"Block {i}: {block.data} - {valid}")

# Show red node disconnection
print("\nSimulating network enforcement:")
print("Red node (unauthorized AI) disconnected by boundary check.")
