
FROM node:18-alpine

# Install Python
RUN apk add --no-cache python3 py3-pip

# Set working directory
WORKDIR /app

# Copy CASTL™ components
COPY coherence-reality-systems/nhetx-castl-alpha/ ./castl/
COPY src/novacaia/ ./novacaia/

# Install Node.js dependencies
WORKDIR /app/castl
RUN npm install

# Install Python dependencies
WORKDIR /app/novacaia
RUN pip3 install --no-cache-dir asyncio

# Set environment variables
ENV NODE_ENV=production
ENV PYTHONPATH=/app
ENV CASTL_PATH=/app/castl

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python3 nova_caia_bridge.py --test || exit 1

# Start command
CMD ["python3", "nova_caia_bridge.py", "--simulate"]
