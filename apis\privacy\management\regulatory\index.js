/**
 * Regulatory Intelligence Module
 * 
 * This module provides regulatory intelligence for the Privacy Management API.
 * It includes a database of privacy regulations and automated compliance checks.
 */

const logger = require('../logging');
const cache = require('../cache');

// Cache keys
const REGULATIONS_CACHE_KEY = 'regulatory:regulations';
const REQUIREMENTS_CACHE_KEY = 'regulatory:requirements';
const CHANGES_CACHE_KEY = 'regulatory:changes';

/**
 * Get all regulations
 * @param {Object} db - MongoDB database instance
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @param {boolean} options.includeInactive - Whether to include inactive regulations
 * @returns {Promise<Array>} - List of regulations
 */
async function getRegulations(db, options = {}) {
  const { useCache = true, includeInactive = false } = options;
  
  try {
    // Try to get from cache
    if (useCache) {
      const cachedRegulations = await cache.get(REGULATIONS_CACHE_KEY);
      if (cachedRegulations) {
        return includeInactive
          ? cachedRegulations
          : cachedRegulations.filter(reg => reg.status === 'active');
      }
    }
    
    // Get from database
    const query = includeInactive ? {} : { status: 'active' };
    const regulations = await db.collection('regulations').find(query).toArray();
    
    // Cache the result
    if (useCache) {
      await cache.set(REGULATIONS_CACHE_KEY, regulations, 3600); // Cache for 1 hour
    }
    
    return regulations;
  } catch (error) {
    logger.error('Error getting regulations', { error: error.message });
    throw error;
  }
}

/**
 * Get a specific regulation
 * @param {Object} db - MongoDB database instance
 * @param {string} id - Regulation ID
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @returns {Promise<Object>} - Regulation
 */
async function getRegulation(db, id, options = {}) {
  const { useCache = true } = options;
  
  try {
    // Get all regulations from cache or database
    const regulations = await getRegulations(db, { useCache, includeInactive: true });
    
    // Find the regulation by ID
    const regulation = regulations.find(reg => reg.id === id);
    
    if (!regulation) {
      throw new Error(`Regulation with ID ${id} not found`);
    }
    
    return regulation;
  } catch (error) {
    logger.error('Error getting regulation', { id, error: error.message });
    throw error;
  }
}

/**
 * Get all requirements
 * @param {Object} db - MongoDB database instance
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @param {string} options.regulationId - Filter by regulation ID
 * @param {string} options.category - Filter by category
 * @param {string} options.status - Filter by status
 * @returns {Promise<Array>} - List of requirements
 */
async function getRequirements(db, options = {}) {
  const {
    useCache = true,
    regulationId,
    category,
    status = 'active'
  } = options;
  
  try {
    // Try to get from cache
    if (useCache && !regulationId && !category) {
      const cachedRequirements = await cache.get(REQUIREMENTS_CACHE_KEY);
      if (cachedRequirements) {
        return status
          ? cachedRequirements.filter(req => req.status === status)
          : cachedRequirements;
      }
    }
    
    // Build query
    const query = {};
    if (regulationId) {
      query.regulationId = regulationId;
    }
    if (category) {
      query.category = category;
    }
    if (status) {
      query.status = status;
    }
    
    // Get from database
    const requirements = await db.collection('requirements').find(query).toArray();
    
    // Cache the result if no filters were applied
    if (useCache && !regulationId && !category) {
      await cache.set(REQUIREMENTS_CACHE_KEY, requirements, 3600); // Cache for 1 hour
    }
    
    return requirements;
  } catch (error) {
    logger.error('Error getting requirements', { error: error.message });
    throw error;
  }
}

/**
 * Get a specific requirement
 * @param {Object} db - MongoDB database instance
 * @param {string} id - Requirement ID
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @returns {Promise<Object>} - Requirement
 */
async function getRequirement(db, id, options = {}) {
  const { useCache = true } = options;
  
  try {
    // Try to get from cache
    if (useCache) {
      const cachedRequirements = await cache.get(REQUIREMENTS_CACHE_KEY);
      if (cachedRequirements) {
        const requirement = cachedRequirements.find(req => req.id === id);
        if (requirement) {
          return requirement;
        }
      }
    }
    
    // Get from database
    const requirement = await db.collection('requirements').findOne({ id });
    
    if (!requirement) {
      throw new Error(`Requirement with ID ${id} not found`);
    }
    
    return requirement;
  } catch (error) {
    logger.error('Error getting requirement', { id, error: error.message });
    throw error;
  }
}

/**
 * Get regulatory changes
 * @param {Object} db - MongoDB database instance
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @param {string} options.regulationId - Filter by regulation ID
 * @param {string} options.status - Filter by status
 * @param {Date} options.since - Filter by date
 * @returns {Promise<Array>} - List of changes
 */
async function getChanges(db, options = {}) {
  const {
    useCache = true,
    regulationId,
    status = 'active',
    since
  } = options;
  
  try {
    // Try to get from cache
    if (useCache && !regulationId && !since) {
      const cachedChanges = await cache.get(CHANGES_CACHE_KEY);
      if (cachedChanges) {
        return status
          ? cachedChanges.filter(change => change.status === status)
          : cachedChanges;
      }
    }
    
    // Build query
    const query = {};
    if (regulationId) {
      query.regulationId = regulationId;
    }
    if (status) {
      query.status = status;
    }
    if (since) {
      query.effectiveDate = { $gte: since };
    }
    
    // Get from database
    const changes = await db.collection('regulatoryChanges').find(query).sort({ effectiveDate: -1 }).toArray();
    
    // Cache the result if no filters were applied
    if (useCache && !regulationId && !since) {
      await cache.set(CHANGES_CACHE_KEY, changes, 3600); // Cache for 1 hour
    }
    
    return changes;
  } catch (error) {
    logger.error('Error getting regulatory changes', { error: error.message });
    throw error;
  }
}

/**
 * Get a specific change
 * @param {Object} db - MongoDB database instance
 * @param {string} id - Change ID
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @returns {Promise<Object>} - Change
 */
async function getChange(db, id, options = {}) {
  const { useCache = true } = options;
  
  try {
    // Try to get from cache
    if (useCache) {
      const cachedChanges = await cache.get(CHANGES_CACHE_KEY);
      if (cachedChanges) {
        const change = cachedChanges.find(c => c.id === id);
        if (change) {
          return change;
        }
      }
    }
    
    // Get from database
    const change = await db.collection('regulatoryChanges').findOne({ id });
    
    if (!change) {
      throw new Error(`Regulatory change with ID ${id} not found`);
    }
    
    return change;
  } catch (error) {
    logger.error('Error getting regulatory change', { id, error: error.message });
    throw error;
  }
}

/**
 * Check compliance for a data processing activity
 * @param {Object} db - MongoDB database instance
 * @param {Object} activity - Data processing activity
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @param {boolean} options.includeRequirements - Whether to include requirements
 * @param {boolean} options.includeChanges - Whether to include changes
 * @returns {Promise<Object>} - Compliance check result
 */
async function checkCompliance(db, activity, options = {}) {
  const {
    useCache = true,
    includeRequirements = false,
    includeChanges = false
  } = options;
  
  try {
    // Get all requirements
    const requirements = await getRequirements(db, { useCache });
    
    // Filter requirements applicable to the activity
    const applicableRequirements = requirements.filter(req => {
      // Check if the requirement applies to the activity based on various criteria
      if (req.dataCategories && req.dataCategories.length > 0) {
        const hasMatchingCategory = req.dataCategories.some(category =>
          activity.dataCategories.includes(category)
        );
        if (!hasMatchingCategory) {
          return false;
        }
      }
      
      if (req.dataSubjects && req.dataSubjects.length > 0) {
        const hasMatchingSubject = req.dataSubjects.some(subject =>
          activity.dataSubjects.includes(subject)
        );
        if (!hasMatchingSubject) {
          return false;
        }
      }
      
      if (req.legalBasis && req.legalBasis.length > 0) {
        if (!req.legalBasis.includes(activity.legalBasis)) {
          return false;
        }
      }
      
      return true;
    });
    
    // Check compliance for each requirement
    const complianceChecks = applicableRequirements.map(req => {
      // Perform compliance check based on requirement type
      let isCompliant = false;
      let complianceGap = null;
      
      switch (req.type) {
        case 'documentation':
          // Check if the activity has the required documentation
          isCompliant = activity.documentation && activity.documentation.includes(req.documentationType);
          if (!isCompliant) {
            complianceGap = `Missing documentation: ${req.documentationType}`;
          }
          break;
          
        case 'consent':
          // Check if consent is properly managed
          isCompliant = activity.legalBasis === 'consent' && activity.consentManagement === true;
          if (!isCompliant && activity.legalBasis === 'consent') {
            complianceGap = 'Consent management is not implemented';
          }
          break;
          
        case 'data-retention':
          // Check if data retention period is defined
          isCompliant = activity.retentionPeriod && activity.retentionPeriod.length > 0;
          if (!isCompliant) {
            complianceGap = 'Data retention period is not defined';
          }
          break;
          
        case 'data-minimization':
          // Check if data minimization is implemented
          isCompliant = activity.dataMinimization === true;
          if (!isCompliant) {
            complianceGap = 'Data minimization is not implemented';
          }
          break;
          
        case 'dpia':
          // Check if DPIA is required and performed
          const dpiaRequired = req.dpiaRequired && activity.highRisk === true;
          isCompliant = !dpiaRequired || (dpiaRequired && activity.dpiaPerformed === true);
          if (!isCompliant && dpiaRequired) {
            complianceGap = 'Data Protection Impact Assessment (DPIA) is required but not performed';
          }
          break;
          
        default:
          // Default to non-compliant for unknown requirement types
          isCompliant = false;
          complianceGap = `Unknown requirement type: ${req.type}`;
      }
      
      return {
        requirementId: req.id,
        regulationId: req.regulationId,
        requirement: includeRequirements ? req : undefined,
        isCompliant,
        complianceGap
      };
    });
    
    // Calculate overall compliance score
    const compliantChecks = complianceChecks.filter(check => check.isCompliant);
    const complianceScore = complianceChecks.length > 0
      ? Math.round((compliantChecks.length / complianceChecks.length) * 100)
      : 100;
    
    // Get compliance gaps
    const complianceGaps = complianceChecks
      .filter(check => !check.isCompliant)
      .map(check => ({
        requirementId: check.requirementId,
        regulationId: check.regulationId,
        gap: check.complianceGap
      }));
    
    // Get relevant regulatory changes if requested
    let relevantChanges = [];
    if (includeChanges) {
      const allChanges = await getChanges(db, { useCache });
      
      // Filter changes relevant to the activity
      relevantChanges = allChanges.filter(change => {
        // Check if the change affects the activity based on various criteria
        if (change.dataCategories && change.dataCategories.length > 0) {
          const hasMatchingCategory = change.dataCategories.some(category =>
            activity.dataCategories.includes(category)
          );
          if (!hasMatchingCategory) {
            return false;
          }
        }
        
        if (change.dataSubjects && change.dataSubjects.length > 0) {
          const hasMatchingSubject = change.dataSubjects.some(subject =>
            activity.dataSubjects.includes(subject)
          );
          if (!hasMatchingSubject) {
            return false;
          }
        }
        
        if (change.legalBasis && change.legalBasis.length > 0) {
          if (!change.legalBasis.includes(activity.legalBasis)) {
            return false;
          }
        }
        
        return true;
      });
    }
    
    return {
      activityId: activity.id,
      complianceScore,
      complianceStatus: complianceScore >= 80 ? 'compliant' : 'non-compliant',
      complianceChecks: includeRequirements ? complianceChecks : undefined,
      complianceGaps,
      relevantChanges: includeChanges ? relevantChanges : undefined
    };
  } catch (error) {
    logger.error('Error checking compliance', { activityId: activity.id, error: error.message });
    throw error;
  }
}

/**
 * Get compliance status for multiple activities
 * @param {Object} db - MongoDB database instance
 * @param {Array} activities - Data processing activities
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @returns {Promise<Object>} - Compliance status
 */
async function getComplianceStatus(db, activities, options = {}) {
  const { useCache = true } = options;
  
  try {
    // Check compliance for each activity
    const complianceChecks = await Promise.all(
      activities.map(activity => checkCompliance(db, activity, { useCache }))
    );
    
    // Calculate overall compliance score
    const totalScore = complianceChecks.reduce((sum, check) => sum + check.complianceScore, 0);
    const overallScore = complianceChecks.length > 0
      ? Math.round(totalScore / complianceChecks.length)
      : 100;
    
    // Get all compliance gaps
    const allGaps = complianceChecks.flatMap(check => check.complianceGaps);
    
    // Group gaps by regulation
    const gapsByRegulation = allGaps.reduce((result, gap) => {
      if (!result[gap.regulationId]) {
        result[gap.regulationId] = [];
      }
      result[gap.regulationId].push(gap);
      return result;
    }, {});
    
    return {
      overallScore,
      status: overallScore >= 80 ? 'compliant' : 'non-compliant',
      activityChecks: complianceChecks,
      gapsByRegulation
    };
  } catch (error) {
    logger.error('Error getting compliance status', { error: error.message });
    throw error;
  }
}

/**
 * Get regulatory intelligence for a data processing activity
 * @param {Object} db - MongoDB database instance
 * @param {Object} activity - Data processing activity
 * @param {Object} options - Options
 * @param {boolean} options.useCache - Whether to use cache
 * @param {boolean} options.includeCompliance - Whether to include compliance information
 * @param {boolean} options.includeRequirements - Whether to include requirements
 * @param {boolean} options.includeChanges - Whether to include changes
 * @returns {Promise<Object>} - Regulatory intelligence
 */
async function getRegulatoryIntelligence(db, activity, options = {}) {
  const {
    useCache = true,
    includeCompliance = true,
    includeRequirements = false,
    includeChanges = false
  } = options;
  
  try {
    // Get applicable regulations
    const regulations = await getRegulations(db, { useCache });
    
    // Filter regulations applicable to the activity
    const applicableRegulations = regulations.filter(reg => {
      // Check if the regulation applies to the activity based on various criteria
      if (reg.dataCategories && reg.dataCategories.length > 0) {
        const hasMatchingCategory = reg.dataCategories.some(category =>
          activity.dataCategories.includes(category)
        );
        if (!hasMatchingCategory) {
          return false;
        }
      }
      
      if (reg.dataSubjects && reg.dataSubjects.length > 0) {
        const hasMatchingSubject = reg.dataSubjects.some(subject =>
          activity.dataSubjects.includes(subject)
        );
        if (!hasMatchingSubject) {
          return false;
        }
      }
      
      if (reg.jurisdictions && reg.jurisdictions.length > 0) {
        if (!activity.jurisdictions || activity.jurisdictions.length === 0) {
          return false;
        }
        
        const hasMatchingJurisdiction = reg.jurisdictions.some(jurisdiction =>
          activity.jurisdictions.includes(jurisdiction)
        );
        if (!hasMatchingJurisdiction) {
          return false;
        }
      }
      
      return true;
    });
    
    // Get compliance information if requested
    let compliance = null;
    if (includeCompliance) {
      compliance = await checkCompliance(db, activity, {
        useCache,
        includeRequirements,
        includeChanges
      });
    }
    
    return {
      activityId: activity.id,
      applicableRegulations,
      compliance
    };
  } catch (error) {
    logger.error('Error getting regulatory intelligence', { activityId: activity.id, error: error.message });
    throw error;
  }
}

module.exports = {
  getRegulations,
  getRegulation,
  getRequirements,
  getRequirement,
  getChanges,
  getChange,
  checkCompliance,
  getComplianceStatus,
  getRegulatoryIntelligence
};
