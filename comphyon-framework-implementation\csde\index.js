/**
 * Cyber-Safety Dominance Equation (CSDE)
 * 
 * This module exports all components of the CSDE system.
 */

const EntropicGRCControlSystem = require('./entropic-grc-control-system');
const HumanSystemCoherenceInterface = require('./human-system-coherence-interface');
const CrossDomainEntropyBridge = require('./cross-domain-entropy-bridge');

/**
 * Create a basic CSDE system
 * @param {Object} options - Configuration options
 * @returns {Object} - CSDE system components
 */
function createCSDESystem(options = {}) {
  // Create components
  const entropicGRCControlSystem = new EntropicGRCControlSystem(options.entropicGRCControlSystemOptions);
  const humanSystemCoherenceInterface = new HumanSystemCoherenceInterface(options.humanSystemCoherenceInterfaceOptions);
  const crossDomainEntropyBridge = new CrossDomainEntropyBridge(options.crossDomainEntropyBridgeOptions);
  
  return {
    entropicGRCControlSystem,
    humanSystemCoherenceInterface,
    crossDomainEntropyBridge
  };
}

/**
 * Create an enhanced CSDE system with integrated components
 * @param {Object} options - Configuration options
 * @returns {Object} - Enhanced CSDE system
 */
function createEnhancedCSDESystem(options = {}) {
  // Create basic system
  const csdeSystem = createCSDESystem(options);
  
  // Set up event listeners for integration
  
  // When policy entropy changes, update cyber entropy in bridge
  csdeSystem.entropicGRCControlSystem.on('unified-entropy-update', (data) => {
    csdeSystem.crossDomainEntropyBridge.updateCyberEntropy(data.unifiedEntropy);
  });
  
  // When human coherence changes, adjust control system thresholds
  csdeSystem.humanSystemCoherenceInterface.on('coherence-update', (data) => {
    // In a real implementation, this would adjust thresholds based on human coherence
    // For now, just log the event
    if (options.enableLogging) {
      console.log(`Enhanced CSDE: Human coherence updated to ${data.humanCoherenceIndex}`);
    }
  });
  
  // When risk status changes, update UI adaptations
  csdeSystem.crossDomainEntropyBridge.on('status-change', (data) => {
    // In a real implementation, this would trigger UI adaptations based on risk status
    // For now, just log the event
    if (options.enableLogging) {
      console.log(`Enhanced CSDE: Risk status changed to ${data.riskStatus}`);
    }
  });
  
  // Add enhanced methods
  const enhancedSystem = {
    ...csdeSystem,
    
    /**
     * Start all components
     * @returns {boolean} - Success status
     */
    start() {
      const grcStarted = csdeSystem.entropicGRCControlSystem.start();
      const humanStarted = csdeSystem.humanSystemCoherenceInterface.start();
      const bridgeStarted = csdeSystem.crossDomainEntropyBridge.start();
      
      return grcStarted && humanStarted && bridgeStarted;
    },
    
    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      const grcStopped = csdeSystem.entropicGRCControlSystem.stop();
      const humanStopped = csdeSystem.humanSystemCoherenceInterface.stop();
      const bridgeStopped = csdeSystem.crossDomainEntropyBridge.stop();
      
      return grcStopped && humanStopped && bridgeStopped;
    },
    
    /**
     * Get unified state from all components
     * @returns {Object} - Unified state
     */
    getUnifiedState() {
      return {
        grc: csdeSystem.entropicGRCControlSystem.getState(),
        human: csdeSystem.humanSystemCoherenceInterface.getState(),
        bridge: csdeSystem.crossDomainEntropyBridge.getState(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Get unified metrics from all components
     * @returns {Object} - Unified metrics
     */
    getUnifiedMetrics() {
      return {
        grc: csdeSystem.entropicGRCControlSystem.getMetrics(),
        human: csdeSystem.humanSystemCoherenceInterface.getMetrics(),
        bridge: csdeSystem.crossDomainEntropyBridge.getMetrics(),
        timestamp: Date.now()
      };
    },
    
    /**
     * Calculate cyber entropy (Ψₜᵈ)
     * @param {Object} data - Input data
     * @returns {number} - Cyber entropy value
     */
    calculateCyberEntropy(data) {
      const { policyData, auditData, regulatoryData } = data;
      
      // Calculate individual entropy values
      let policyEntropy = 0.5;
      let auditEntropy = 0.5;
      let regulatoryEntropy = 0.5;
      
      if (policyData) {
        policyEntropy = csdeSystem.entropicGRCControlSystem.calculatePolicyEntropy(policyData);
      }
      
      if (auditData) {
        auditEntropy = csdeSystem.entropicGRCControlSystem.calculateAuditEntropy(auditData);
      }
      
      if (regulatoryData) {
        regulatoryEntropy = csdeSystem.entropicGRCControlSystem.calculateRegulatoryEntropy(regulatoryData);
      }
      
      // Calculate unified entropy using 18/82 principle
      const unifiedEntropy = (
        0.18 * policyEntropy +
        0.82 * ((auditEntropy + regulatoryEntropy) / 2)
      );
      
      // Update bridge
      csdeSystem.crossDomainEntropyBridge.updateCyberEntropy(unifiedEntropy);
      
      return unifiedEntropy;
    },
    
    /**
     * Update human factors
     * @param {Object} humanData - Human data
     * @returns {number} - Human coherence index
     */
    updateHumanFactors(humanData) {
      return csdeSystem.humanSystemCoherenceInterface.updateHumanCoherenceIndex(humanData);
    },
    
    /**
     * Get unified risk assessment
     * @returns {Object} - Risk assessment
     */
    getUnifiedRiskAssessment() {
      const bridgeState = csdeSystem.crossDomainEntropyBridge.getState();
      const grcState = csdeSystem.entropicGRCControlSystem.getState();
      const humanState = csdeSystem.humanSystemCoherenceInterface.getState();
      
      return {
        unifiedRiskScore: bridgeState.unifiedRiskScore,
        riskStatus: bridgeState.riskStatus,
        cyberEntropy: bridgeState.cyberEntropy,
        financialEntropy: bridgeState.financialEntropy,
        biologicalEntropy: bridgeState.biologicalEntropy,
        policyEntropy: grcState.policyEntropy,
        auditEntropy: grcState.auditEntropy,
        regulatoryEntropy: grcState.regulatoryEntropy,
        humanCoherenceIndex: humanState.humanCoherenceIndex,
        timestamp: Date.now()
      };
    }
  };
  
  return enhancedSystem;
}

module.exports = {
  EntropicGRCControlSystem,
  HumanSystemCoherenceInterface,
  CrossDomainEntropyBridge,
  createCSDESystem,
  createEnhancedCSDESystem
};
