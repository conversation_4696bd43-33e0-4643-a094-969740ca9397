# NovaUI

<div align="center">
<h3>Unified UI Components and Interfaces for NovaFuse Platform</h3>
</div>

> NovaUI provides a comprehensive set of user interfaces and components for the NovaFuse GRC platform, with feature toggles to support different product offerings.

## Overview

NovaUI is a unified codebase that powers all NovaFuse product interfaces through feature toggles and configuration. This approach allows for:

- **Consistent User Experience**: Shared design system and components
- **Development Efficiency**: Single codebase for all products
- **Feature Toggling**: Products defined by configuration, not separate codebases
- **Simplified Maintenance**: Bug fixes apply across all products

## Product Interfaces

NovaUI powers the following product interfaces:

1. **NovaPrime** - Flagship full-featured UI
   - Complete dashboard with all metrics
   - Access to all GRC functionality
   - Advanced reporting and analytics

2. **NovaCore** - Freemium version
   - Limited version of NovaPrime
   - Essential GRC functionality
   - Basic reporting

3. **NovaShield** - Security & privacy focused UI
   - Security posture dashboard
   - Privacy compliance tracker
   - Data protection tools

4. **NovaAssistAI** - Chatbot component
   - AI-driven assistant
   - NLP compliance analysis
   - Regulatory suggestions

5. **NovaLearn** - Gamification platform
   - Learning path management
   - Certification tracking
   - Gamified training modules

6. **NovaMarketplace** - Module selection interface
   - Module catalog/marketplace
   - Module activation/deactivation
   - Usage tracking and billing

## Repository Structure

```
nova-ui/
├── packages/                # Shared packages (monorepo structure)
│   ├── ui-components/       # Shared UI components
│   ├── api-client/          # API client libraries
│   ├── utils/               # Shared utilities
│   └── feature-flags/       # Feature flag system
│
├── components/              # Reusable UI components
│   ├── common/              # Common components
│   ├── dashboard/           # Dashboard components
│   ├── forms/               # Form components
│   ├── layout/              # Layout components
│   └── navigation/          # Navigation components
│
├── pages/                   # Next.js pages
│   ├── dashboard/           # Dashboard pages
│   ├── privacy/             # Privacy Management pages
│   ├── compliance/          # Compliance pages
│   ├── security/            # Security pages
│   ├── feature-flag-demo.js # Feature flag demo page
│   └── index.js             # Home page
│
├── docs/                    # Documentation
│   └── feature-flag-system.md # Feature flag system documentation
│
└── styles/                  # Global styles
```

## Feature Flag System

NovaUI uses a feature flag system to enable/disable features based on the product tier. This allows for a single codebase to power multiple products with different feature sets.

### Feature Flag Configuration

```javascript
// Feature flag configuration for each product
const featureFlags = {
  // NovaPrime features (all enabled)
  novaPrime: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true
    },

    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: true,
      esg: true
    },

    // Advanced features
    advanced: {
      aiAssistant: true,
      predictiveAnalytics: true,
      automatedRemediation: true,
      customIntegrations: true
    }
  },

  // NovaCore features (limited)
  novaCore: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: false,
      reports: true,
      customization: false
    },

    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: false,
      esg: false
    },

    // Advanced features
    advanced: {
      aiAssistant: false,
      predictiveAnalytics: false,
      automatedRemediation: false,
      customIntegrations: false
    }
  }
};
```

### Using Feature Flags in React Components

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function AnalyticsPanel() {
  const isAnalyticsEnabled = useFeatureFlag('dashboard', 'analytics');

  if (!isAnalyticsEnabled) {
    return null;
  }

  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

### Using FeatureFlaggedComponent

```jsx
import { FeatureFlaggedComponent, UpgradePrompt } from '@nova-ui/feature-flags';

function Dashboard() {
  return (
    <div className="dashboard">
      <FeatureFlaggedComponent
        category="dashboard"
        feature="analytics"
        fallback={<UpgradePrompt featureName="Analytics" />}
      >
        <AnalyticsPanel />
      </FeatureFlaggedComponent>
    </div>
  );
}
```

## New Components

### ProductSwitcher

We've added a `ProductSwitcher` component that allows users to easily switch between different product tiers to see how the UI changes based on the feature flags for each product.

```jsx
import ProductSwitcher from '../components/common/ProductSwitcher';

function YourComponent() {
  return (
    <div>
      <ProductSwitcher />
      {/* Your component content */}
    </div>
  );
}
```

### Feature Flag Demo Page

We've added a Feature Flag Demo page that showcases all the features and how they appear in each product tier. This page is useful for testing and demonstrating the feature flag system.

To access the Feature Flag Demo page, navigate to `/feature-flag-demo`.

## Documentation

We've added comprehensive documentation for the feature flag system in `docs/feature-flag-system.md`. This documentation covers:

- Overview of the feature flag system
- Product tiers and feature categories
- Using feature flags in React components
- Setting up the Feature Flag Provider
- Adding new features and products
- Best practices for using feature flags

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/Dartan1983/nova-ui.git
   cd nova-ui
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm run dev
   ```

4. Navigate to the Feature Flag Demo page:
   ```
   http://localhost:3000/feature-flag-demo
   ```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For more information, contact the NovaFuse <NAME_EMAIL>.