/**
 * Comphyological Tensor Core Test
 *
 * This script demonstrates the functionality of the Comphyological Tensor Core,
 * including the Resonance Listener and Domain Adapters.
 */

console.log('=== Comphyological Tensor Core Test ===');

// Import the tensor module and related components
const { createComphyologicalTensorCore } = require('./src/quantum/tensor');
const { createResonanceListener } = require('./src/quantum/resonance');
const { createUnifiedAdapter } = require('./src/quantum/adapters');

// Create Comphyological Tensor Core
console.log('\nCreating Comphyological Tensor Core...');
const tensorCore = createComphyologicalTensorCore({
  enableLogging: true,
  strictMode: false,
  useGPU: true,
  useDynamicWeighting: true,
  precision: 6,
  normalizationFactor: 166000
});

// Create resonance listener
console.log('Creating Resonance Listener...');
const resonanceListener = createResonanceListener({
  enableLogging: true,
  targetFrequency: 396, // Hz - the "OM Tone"
  precisionFFT: 0.001, // attohertz precision
  quantumVacuumNoise: true,
  crossDomainPhaseAlignment: true,
  silenceThreshold: 0.001,
  detectionInterval: 100,
  maxHistoryLength: 100
});

// Start resonance listener
console.log('Starting Resonance Listener...');
resonanceListener.startListening(tensorCore);

// Create unified adapter
console.log('Creating Unified Adapter...');
const unifiedAdapter = createUnifiedAdapter({}, {
  enableLogging: true,
  strictMode: false,
  useGPU: true,
  useDynamicWeighting: true,
  precision: 6,
  normalizationFactor: 166000
});

console.log('All components created successfully!');

// Create domain data
const csdeData = {
  governance: 0.8,
  dataQuality: 0.7,
  action: 'allow',
  confidence: 0.9
};

const csfeData = {
  risk: 0.3,
  policyCompliance: 0.6,
  action: 'monitor',
  confidence: 0.8
};

const csmeData = {
  trustFactor: 0.5,
  integrityFactor: 0.6,
  action: 'alert',
  confidence: 0.7
};

console.log('\n=== Basic Test ===');
console.log('\nDomain Data:');
console.log('CSDE Data:', JSON.stringify(csdeData));
console.log('CSFE Data:', JSON.stringify(csfeData));
console.log('CSME Data:', JSON.stringify(csmeData));

// Process data through tensor core
console.log('\nProcessing data through Tensor Core...');
const result = tensorCore.processData(
  csdeData,
  csfeData,
  csmeData
);

console.log('\nTensor Core Result:');
console.log('Comphyon Value:', result.comphyon.toFixed(6));
console.log('Action:', result.action);
console.log('Confidence:', result.confidence.toFixed(6));
console.log('Energies:', JSON.stringify({
  csde: result.energies.csde.toFixed(6),
  csfe: result.energies.csfe.toFixed(6),
  csme: result.energies.csme.toFixed(6)
}));
console.log('Weights:', JSON.stringify({
  csde: result.weights.csde.toFixed(6),
  csfe: result.weights.csfe.toFixed(6),
  csme: result.weights.csme.toFixed(6)
}));
console.log('Processing Time:', result.processingTime.toFixed(6), 'ms');

// Process data through unified adapter
console.log('\nProcessing data through Unified Adapter...');
const unifiedResult = unifiedAdapter.processData({
  csdeData,
  csfeData,
  csmeData
});

console.log('\nUnified Adapter Result:');
console.log('Comphyon Value:', unifiedResult.comphyon.toFixed(6));
console.log('Action:', unifiedResult.action);
console.log('Confidence:', unifiedResult.confidence.toFixed(6));

// Get resonance state
console.log('\n=== Resonance Test ===');
const resonance = resonanceListener.getResonanceState();
console.log('\nResonance State:');
console.log('Frequency:', resonance.frequency.toFixed(6), 'Hz');
console.log('Deviation:', resonance.deviation.toFixed(6), '%');
console.log('Quantum Silence:', resonance.isQuantumSilence ? 'Yes' : 'No');
console.log('Phase Alignment:', resonance.phaseAlignment.toFixed(6));
console.log('Quantum Vacuum Noise:', resonance.quantumVacuumNoise.toFixed(6));

// Get resonance history
const history = resonanceListener.getResonanceHistory();
console.log('\nResonance History:');
console.log('History Length:', history.length);
if (history.length > 0) {
  console.log('Last Entry:');
  const lastEntry = history[history.length - 1];
  console.log('  Frequency:', lastEntry.frequency.toFixed(6), 'Hz');
  console.log('  Quantum Silence:', lastEntry.isQuantumSilence ? 'Yes' : 'No');
  console.log('  Timestamp:', new Date(lastEntry.timestamp).toISOString());
}

// Get metrics
const metrics = tensorCore.getMetrics();

console.log('\n=== Metrics ===');
console.log('Fusion Count:', metrics.fusionCount);
console.log('Last Comphyon Value:', metrics.lastComphyonValue.toFixed(6));
console.log('Average Comphyon Value:', metrics.averageComphyonValue.toFixed(6));
console.log('Max Comphyon Value:', metrics.maxComphyonValue.toFixed(6));
console.log('Min Comphyon Value:', metrics.minComphyonValue.toFixed(6));
console.log('Average Processing Time:', metrics.averageProcessingTime.toFixed(6), 'ms');

// Test resonance case
console.log('\n=== Resonance Case Test ===');
const resonanceCase = {
  csdeData: {
    governance: 0.618,
    dataQuality: 0.618,
    action: 'monitor',
    confidence: 0.618
  },
  csfeData: {
    risk: 0.382,
    policyCompliance: 0.618,
    action: 'monitor',
    confidence: 0.618
  },
  csmeData: {
    trustFactor: 0.618,
    integrityFactor: 0.618,
    action: 'monitor',
    confidence: 0.618
  }
};

console.log('Processing resonance case...');
const resonanceResult = tensorCore.processData(
  resonanceCase.csdeData,
  resonanceCase.csfeData,
  resonanceCase.csmeData
);

console.log('\nResonance Case Result:');
console.log('Comphyon Value:', resonanceResult.comphyon.toFixed(6));
console.log('Action:', resonanceResult.action);
console.log('Confidence:', resonanceResult.confidence.toFixed(6));

// Get updated resonance state
const updatedResonance = resonanceListener.getResonanceState();
console.log('\nUpdated Resonance State:');
console.log('Frequency:', updatedResonance.frequency.toFixed(6), 'Hz');
console.log('Deviation:', updatedResonance.deviation.toFixed(6), '%');
console.log('Quantum Silence:', updatedResonance.isQuantumSilence ? 'Yes' : 'No');

console.log('\n=== Test completed successfully! ===');
