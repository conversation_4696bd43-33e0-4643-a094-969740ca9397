/**
 * Requirement.ts
 * 
 * Model for compliance requirements in the NovaCore system.
 * These requirements define the compliance controls that evidence must satisfy.
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * Requirement status enum
 */
export enum RequirementStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DEPRECATED = 'deprecated',
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
}

/**
 * Compliance status enum
 */
export enum ComplianceStatus {
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant',
  PARTIALLY_COMPLIANT = 'partially_compliant',
  NOT_APPLICABLE = 'not_applicable',
  PENDING_ASSESSMENT = 'pending_assessment',
}

/**
 * Requirement type enum
 */
export enum RequirementType {
  CONTROL = 'control',
  POLICY = 'policy',
  PROCEDURE = 'procedure',
  STANDARD = 'standard',
  REGULATION = 'regulation',
  BEST_PRACTICE = 'best_practice',
}

/**
 * Risk level enum
 */
export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

/**
 * Assessment frequency enum
 */
export enum AssessmentFrequency {
  CONTINUOUS = 'continuous',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  SEMI_ANNUALLY = 'semi_annually',
  ANNUALLY = 'annually',
  CUSTOM = 'custom',
}

/**
 * Framework reference interface
 */
export interface FrameworkReference {
  frameworkId: string;
  frameworkName: string;
  controlId: string;
  controlName?: string;
  version?: string;
  section?: string;
  clause?: string;
  requirement?: string;
}

/**
 * Assessment interface
 */
export interface Assessment {
  id: string;
  status: ComplianceStatus;
  assessedAt: Date;
  assessedBy: string;
  evidenceIds: string[];
  notes?: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Requirement interface
 */
export interface Requirement {
  id: string;
  name: string;
  description: string;
  type: RequirementType;
  status: RequirementStatus;
  riskLevel: RiskLevel;
  organization: string;
  frameworks: FrameworkReference[];
  assessmentFrequency: AssessmentFrequency;
  customFrequencyDays?: number;
  lastAssessment?: Assessment;
  assessmentHistory: Assessment[];
  relatedRequirements?: string[];
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  metadata?: Record<string, any>;
}

/**
 * Create a new requirement
 */
export function createRequirement(
  name: string,
  description: string,
  type: RequirementType,
  riskLevel: RiskLevel,
  assessmentFrequency: AssessmentFrequency,
  frameworks: FrameworkReference[],
  createdBy: string,
  organization: string,
  customFrequencyDays?: number,
  relatedRequirements?: string[],
  tags?: string[],
  metadata?: Record<string, any>,
): Requirement {
  const now = new Date();
  
  return {
    id: uuidv4(),
    name,
    description,
    type,
    status: RequirementStatus.DRAFT,
    riskLevel,
    organization,
    frameworks,
    assessmentFrequency,
    customFrequencyDays,
    assessmentHistory: [],
    relatedRequirements,
    tags,
    createdAt: now,
    updatedAt: now,
    createdBy,
    updatedBy: createdBy,
    metadata,
  };
}

/**
 * Update requirement status
 */
export function updateRequirementStatus(
  requirement: Requirement,
  status: RequirementStatus,
  updatedBy: string,
  statusDetails?: Record<string, any>,
): Requirement {
  const now = new Date();
  
  return {
    ...requirement,
    status,
    updatedAt: now,
    updatedBy,
    metadata: {
      ...requirement.metadata,
      statusDetails,
      statusUpdatedAt: now,
    },
  };
}

/**
 * Create a new assessment
 */
export function createAssessment(
  requirement: Requirement,
  status: ComplianceStatus,
  assessedBy: string,
  evidenceIds: string[],
  notes?: string,
  expiresAt?: Date,
  metadata?: Record<string, any>,
): Requirement {
  const now = new Date();
  
  const assessment: Assessment = {
    id: uuidv4(),
    status,
    assessedAt: now,
    assessedBy,
    evidenceIds,
    notes,
    expiresAt,
    metadata,
  };
  
  return {
    ...requirement,
    lastAssessment: assessment,
    assessmentHistory: [...requirement.assessmentHistory, assessment],
    updatedAt: now,
    updatedBy: assessedBy,
  };
}

/**
 * Calculate next assessment date
 */
export function calculateNextAssessmentDate(requirement: Requirement): Date {
  const now = new Date();
  const lastAssessmentDate = requirement.lastAssessment?.assessedAt || now;
  
  switch (requirement.assessmentFrequency) {
    case AssessmentFrequency.CONTINUOUS:
      return now; // Always due for continuous assessment
    case AssessmentFrequency.DAILY:
      const tomorrow = new Date(lastAssessmentDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    case AssessmentFrequency.WEEKLY:
      const nextWeek = new Date(lastAssessmentDate);
      nextWeek.setDate(nextWeek.getDate() + 7);
      return nextWeek;
    case AssessmentFrequency.MONTHLY:
      const nextMonth = new Date(lastAssessmentDate);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      return nextMonth;
    case AssessmentFrequency.QUARTERLY:
      const nextQuarter = new Date(lastAssessmentDate);
      nextQuarter.setMonth(nextQuarter.getMonth() + 3);
      return nextQuarter;
    case AssessmentFrequency.SEMI_ANNUALLY:
      const nextHalfYear = new Date(lastAssessmentDate);
      nextHalfYear.setMonth(nextHalfYear.getMonth() + 6);
      return nextHalfYear;
    case AssessmentFrequency.ANNUALLY:
      const nextYear = new Date(lastAssessmentDate);
      nextYear.setFullYear(nextYear.getFullYear() + 1);
      return nextYear;
    case AssessmentFrequency.CUSTOM:
      if (requirement.customFrequencyDays) {
        const nextCustomDate = new Date(lastAssessmentDate);
        nextCustomDate.setDate(nextCustomDate.getDate() + requirement.customFrequencyDays);
        return nextCustomDate;
      }
      return new Date(lastAssessmentDate.getTime() + 30 * 24 * 60 * 60 * 1000); // Default to 30 days
    default:
      return new Date(lastAssessmentDate.getTime() + 90 * 24 * 60 * 60 * 1000); // Default to quarterly
  }
}

/**
 * Check if requirement is due for assessment
 */
export function isRequirementDueForAssessment(requirement: Requirement): boolean {
  const now = new Date();
  
  // If there's no last assessment, it's due
  if (!requirement.lastAssessment) {
    return true;
  }
  
  // If there's an expiration date and it's in the past, it's due
  if (requirement.lastAssessment.expiresAt && requirement.lastAssessment.expiresAt < now) {
    return true;
  }
  
  // Calculate next assessment date based on frequency
  const nextAssessmentDate = calculateNextAssessmentDate(requirement);
  
  // If next assessment date is in the past, it's due
  return nextAssessmentDate <= now;
}
