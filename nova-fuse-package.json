{"name": "nova-fuse", "version": "1.0.0", "description": "NovaFuse: A comprehensive API marketplace and integration platform for GRC APIs", "private": true, "scripts": {"start": "echo \"This is a documentation repository. Please see individual repositories for runnable code.\"", "test": "echo \"This is a documentation repository. Please see individual repositories for tests.\""}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-fuse.git"}, "keywords": ["api", "marketplace", "grc", "governance", "risk", "compliance", "security", "privacy", "connector", "integration"], "author": "NovaGRC", "license": "MIT", "bugs": {"url": "https://github.com/Dartan1983/nova-fuse/issues"}, "homepage": "https://github.com/Dartan1983/nova-fuse#readme"}