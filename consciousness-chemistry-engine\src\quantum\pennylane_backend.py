"""
PennyLane backend for quantum computing in protein folding.

This module provides an implementation of the QuantumBackend interface
using Xanadu's PennyLane for quantum circuit simulation and execution.
"""

from typing import Dict, Any, List, Optional, Tuple
import numpy as np
import pennylane as qml
from pennylane import numpy as pnp

from . import QuantumBackend

class PennyLaneBackend(QuantumBackend):
    """PennyLane implementation of the QuantumBackend interface."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the PennyLane backend.
        
        Args:
            config: Configuration dictionary with the following optional keys:
                - device: Name of the PennyLane device to use (default: 'default.qubit')
                - shots: Number of shots for measurements (default: 1000)
                - diff_method: Differentiation method ('parameter-shift', 'backprop', etc.)
                - interface: Interface to use ('numpy', 'torch', 'tf', 'jax')
                - wires: Number of qubits (if None, will be set based on circuit)
                - analytic: Whether to use analytic mode (exact probabilities)
        """
        super().__init__(config)
        self.device_name = self.config.get('device', 'default.qubit')
        self.shots = self.config.get('shots', 1000)
        self.diff_method = self.config.get('diff_method', 'parameter-shift')
        self.interface = self.config.get('interface', 'numpy')
        self.wires = self.config.get('wires')
        self.analytic = self.config.get('analytic', False)
        
        # Initialize device
        self.device = None
        self.qnode = None
        self._initialize_device()
    
    def _initialize_device(self) -> None:
        """Initialize the PennyLane device."""
        try:
            self.device = qml.device(
                self.device_name,
                wires=self.wires,
                shots=self.shots,
                analytic=self.analytic
            )
        except Exception as e:
            raise ValueError(f"Failed to initialize PennyLane device: {str(e)}")
    
    def _create_qnode(self, num_qubits: int, depth: int):
        """Create a quantum node with the given number of qubits and depth.
        
        Args:
            num_qubits: Number of qubits in the circuit
            depth: Depth of the quantum circuit
            
        Returns:
            A tuple of (qnode, params) where params are the trainable parameters
        """
        # Update device wires if needed
        if self.wires is None or self.wires < num_qubits:
            self.wires = num_qubits
            self._initialize_device()
        
        # Define the quantum circuit
        def circuit(params, **kwargs):
            # Initial state preparation (can be customized)
            for i in range(num_qubits):
                qml.Hadamard(wires=i)
            
            # Variational layers
            for d in range(depth):
                # Add parameterized rotations
                for i in range(num_qubits):
                    qml.RX(params[d, i, 0], wires=i)
                    qml.RY(params[d, i, 1], wires=i)
                    qml.RZ(params[d, i, 2], wires=i)
                
                # Add entangling layers (linear nearest neighbor)
                for i in range(num_qubits - 1):
                    qml.CNOT(wires=[i, (i + 1) % num_qubits])
            
            # Measure all qubits
            return [qml.probs(wires=i) for i in range(num_qubits)]
        
        # Initialize parameters
        shape = (depth, num_qubits, 3)  # 3 parameters per qubit per layer (RX, RY, RZ)
        params = pnp.random.uniform(0, 2 * np.pi, size=shape, requires_grad=True)
        
        # Create QNode
        self.qnode = qml.QNode(
            circuit,
            device=self.device,
            interface=self.interface,
            diff_method=self.diff_method
        )
        
        return self.qnode, params
    
    def run_circuit(
        self, 
        circuit: Any,  # Not used, we define our own circuit
        num_qubits: int,
        depth: int,
        shots: int
    ) -> Dict[str, int]:
        """Run a quantum circuit and return the measurement results.
        
        Args:
            circuit: Not used (we define our own circuit)
            num_qubits: Number of qubits in the circuit
            depth: Depth of the quantum circuit
            shots: Number of measurement shots
            
        Returns:
            Dictionary mapping measurement outcomes to counts
        """
        # Create a new circuit if needed
        if self.qnode is None:
            self.shots = shots
            self._create_qnode(num_qubits, depth)
        
        # Generate new parameters for this run
        shape = (depth, num_qubits, 3)
        params = pnp.random.uniform(0, 2 * np.pi, size=shape, requires_grad=False)
        
        # Execute the circuit
        try:
            # Get probabilities for each qubit
            probs = self.qnode(params)
            
            # Sample from the probabilities
            samples = []
            for _ in range(shots):
                sample = ''
                for i in range(num_qubits):
                    bit = np.random.choice([0, 1], p=probs[i])
                    sample += str(bit)
                samples.append(sample)
            
            # Count the results
            counts = {}
            for sample in samples:
                counts[sample] = counts.get(sample, 0) + 1
                
            return counts
            
        except Exception as e:
            raise RuntimeError(f"Error running PennyLane circuit: {str(e)}")
    
    def get_quantum_volume(self) -> int:
        """Get the quantum volume of the backend."""
        # For simulators, return a high value
        return 1024
    
    def get_backend_info(self) -> Dict[str, Any]:
        """Get information about the backend."""
        return {
            'name': 'pennylane',
            'device': self.device_name,
            'shots': self.shots,
            'diff_method': self.diff_method,
            'interface': self.interface,
            'wires': self.wires,
            'analytic': self.analytic,
            'quantum_volume': self.get_quantum_volume()
        }
    
    @classmethod
    def is_available(cls) -> bool:
        """Check if PennyLane is available."""
        try:
            import pennylane
            return True
        except ImportError:
            return False
