/**
 * Connector Routes
 * 
 * This module defines the connector routes for the UAC demo.
 */

const express = require('express');
const router = express.Router();
const { getLogger } = require('../core/logger');

const logger = getLogger('connector-routes');

// Middleware to check authentication
const checkAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = req.uac.authManager.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Register a new API
router.post('/apis', checkAuth, (req, res) => {
  const { apiId, baseUrl, type, authMethod, dataFormat, headers } = req.body;
  
  if (!apiId || !baseUrl) {
    return res.status(400).json({ error: 'API ID and base URL are required' });
  }
  
  try {
    const success = req.uac.connector.registerApi(apiId, {
      baseUrl,
      type,
      authMethod,
      dataFormat,
      headers
    });
    
    if (success) {
      res.status(201).json({ message: `API ${apiId} registered successfully` });
    } else {
      res.status(400).json({ error: `Failed to register API ${apiId}` });
    }
  } catch (error) {
    logger.error('API registration failed', error);
    res.status(400).json({ error: error.message });
  }
});

// Get API configuration
router.get('/apis/:apiId', checkAuth, (req, res) => {
  const { apiId } = req.params;
  
  const apiConfig = req.uac.connector.getApiConfig(apiId);
  
  if (!apiConfig) {
    return res.status(404).json({ error: `API ${apiId} not found` });
  }
  
  res.json(apiConfig);
});

// List all APIs
router.get('/apis', checkAuth, (req, res) => {
  const apis = req.uac.connector.listApis();
  res.json(apis);
});

// Make an API call
router.post('/call/:apiId', checkAuth, (req, res) => {
  const { apiId } = req.params;
  const { method, endpoint, data, params, headers } = req.body;
  
  try {
    req.uac.connector.callApi(apiId, {
      method,
      endpoint,
      data,
      params,
      headers
    })
    .then(response => {
      res.json(response);
    })
    .catch(error => {
      logger.error(`API call to ${apiId} failed`, error);
      res.status(500).json({ 
        error: 'API call failed',
        details: error.message
      });
    });
  } catch (error) {
    logger.error(`API call to ${apiId} failed`, error);
    res.status(400).json({ error: error.message });
  }
});

// Get supported API types
router.get('/types', (req, res) => {
  res.json(req.uac.connector.API_TYPES);
});

// Get supported authentication methods
router.get('/auth-methods', (req, res) => {
  res.json(req.uac.connector.AUTH_METHODS);
});

// Get supported data formats
router.get('/data-formats', (req, res) => {
  res.json(req.uac.connector.DATA_FORMATS);
});

module.exports = router;
