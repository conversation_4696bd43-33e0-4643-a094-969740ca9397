/**
 * GraphQL Response Viewer Component
 * 
 * This component displays GraphQL query responses.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  Paper, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

// Monaco Editor for code display
import Editor from '@monaco-editor/react';

const GraphQLResponseViewer = ({ response }) => {
  const [activeTab, setActiveTab] = useState('data');
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  if (!response) {
    return (
      <Card variant="outlined">
        <CardContent>
          <Typography variant="body2" color="textSecondary" align="center">
            No response data available. Execute a query to see results.
          </Typography>
        </CardContent>
      </Card>
    );
  }
  
  const isSuccess = response.status >= 200 && response.status < 300 && !response.data?.errors;
  const hasErrors = response.data?.errors || response.status >= 400;
  
  return (
    <Box>
      <Card variant="outlined" sx={{ mb: 2 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item>
              {isSuccess ? (
                <CheckCircleIcon color="success" fontSize="large" />
              ) : hasErrors ? (
                <ErrorIcon color="error" fontSize="large" />
              ) : (
                <WarningIcon color="warning" fontSize="large" />
              )}
            </Grid>
            
            <Grid item xs>
              <Typography variant="h6">
                {isSuccess ? 'Query Successful' : hasErrors ? 'Query Failed' : 'Partial Success'}
              </Typography>
              
              <Typography variant="body2" color="textSecondary">
                Status: {response.status} {response.statusText}
              </Typography>
            </Grid>
            
            <Grid item>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <AccessTimeIcon fontSize="small" sx={{ mr: 0.5 }} />
                <Typography variant="body2">
                  {response.responseTime || 0} ms
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="response tabs">
          <Tab label="Data" value="data" />
          {response.data?.errors && <Tab label="Errors" value="errors" />}
          <Tab label="Headers" value="headers" />
          <Tab label="Raw Response" value="raw" />
        </Tabs>
      </Box>
      
      {activeTab === 'data' && (
        <Paper variant="outlined" sx={{ height: 400 }}>
          <Editor
            height="400px"
            language="json"
            value={JSON.stringify(response.data?.data || {}, null, 2)}
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              fontSize: 14,
              wordWrap: 'on'
            }}
          />
        </Paper>
      )}
      
      {activeTab === 'errors' && response.data?.errors && (
        <Box>
          {response.data.errors.map((error, index) => (
            <Card key={index} variant="outlined" sx={{ mb: 2, bgcolor: 'error.light' }}>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {error.message}
                </Typography>
                
                {error.locations && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" fontWeight="medium">
                      Locations:
                    </Typography>
                    {error.locations.map((location, locIndex) => (
                      <Typography key={locIndex} variant="body2">
                        Line {location.line}, Column {location.column}
                      </Typography>
                    ))}
                  </Box>
                )}
                
                {error.path && (
                  <Box sx={{ mb: 1 }}>
                    <Typography variant="body2" fontWeight="medium">
                      Path:
                    </Typography>
                    <Typography variant="body2">
                      {error.path.join(' > ')}
                    </Typography>
                  </Box>
                )}
                
                {error.extensions && (
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      Extensions:
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 1, mt: 1 }}>
                      <pre style={{ margin: 0, fontSize: 12 }}>
                        {JSON.stringify(error.extensions, null, 2)}
                      </pre>
                    </Paper>
                  </Box>
                )}
              </CardContent>
            </Card>
          ))}
        </Box>
      )}
      
      {activeTab === 'headers' && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Response Headers
          </Typography>
          
          <Grid container spacing={1}>
            {Object.entries(response.headers || {}).map(([key, value]) => (
              <Grid item xs={12} key={key}>
                <Paper variant="outlined" sx={{ p: 1 }}>
                  <Typography variant="body2" fontWeight="medium">
                    {key}:
                  </Typography>
                  <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                    {value}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>
      )}
      
      {activeTab === 'raw' && (
        <Paper variant="outlined" sx={{ height: 400 }}>
          <Editor
            height="400px"
            language="json"
            value={JSON.stringify(response, null, 2)}
            options={{
              readOnly: true,
              minimap: { enabled: false },
              scrollBeyondLastLine: false,
              fontSize: 14,
              wordWrap: 'on'
            }}
          />
        </Paper>
      )}
    </Box>
  );
};

export default GraphQLResponseViewer;
