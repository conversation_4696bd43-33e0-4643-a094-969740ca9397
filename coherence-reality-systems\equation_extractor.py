import os
import re
import json
from collections import defaultdict
import markdown

class EquationExtractor:
    def __init__(self, codebase_paths):
        self.codebase_paths = codebase_paths
        self.equations = defaultdict(list)
        self.patterns = {
            'equation': r'\\beq\\s*([^\\n]+)\\s*\\eeq',
            'formula': r'\\bfml\\s*([^\\n]+)\\s*\\efml',
            'constant': r'\\bconst\\s*([^\\n]+)\\s*\\econst',
            'variable': r'\\bvar\\s*([^\\n]+)\\s*\\evar',
            'math_block': r'\\$\\$([^\\n]+)\\$\\$',
            'inline_math': r'\\$([^\\n]+)\\$'
        }

    def extract_from_markdown(self, content, filepath):
        """Extract equations from markdown content"""
        # Extract tables first
        tables = re.findall(r'\|.*?\n\|.*?\n\|.*?\n', content, re.DOTALL)
        for table in tables:
            # Look for equations in tables
            if '=' in table:
                self._process_table(table, filepath)
                
        # Extract math blocks
        for eq_type, pattern in self.patterns.items():
            matches = re.finditer(pattern, content)
            for match in matches:
                equation = match.group(1).strip()
                self.equations[eq_type].append({
                    'equation': equation,
                    'source': filepath,
                    'type': eq_type,
                    'context': self._get_context(content, match.start())
                })

    def _process_table(self, table, filepath):
        """Process tables containing equations"""
        lines = table.strip().split('\n')
        headers = [cell.strip() for cell in lines[0].split('|') if cell.strip()]
        
        # Look for equations in table cells
        for line in lines[1:]:
            cells = [cell.strip() for cell in line.split('|') if cell.strip()]
            if any('=' in cell for cell in cells):
                for i, cell in enumerate(cells):
                    if '=' in cell:
                        self.equations['table'].append({
                            'equation': cell,
                            'source': filepath,
                            'type': 'table',
                            'header': headers[i],
                            'context': table
                        })

    def _get_context(self, content, position, window=200):
        """Get surrounding context for an equation"""
        start = max(0, position - window)
        end = min(len(content), position + window)
        return content[start:end]

    def extract_all(self):
        """Extract equations from all files in codebases"""
        for path in self.codebase_paths:
            for root, dirs, files in os.walk(path):
                for file in files:
                    if file.endswith(('.py', '.md', '.txt')):
                        filepath = os.path.join(root, file)
                        try:
                            with open(filepath, 'r', encoding='utf-8') as f:
                                content = f.read()
                            
                            if file.endswith('.md'):
                                self.extract_from_markdown(content, filepath)
                            else:
                                self._extract_from_text(content, filepath)
                        except Exception as e:
                            print(f"Error processing {filepath}: {e}")

    def format_equations(self):
        """Format equations for EPUB"""
        formatted = []
        for eq_type, equations in self.equations.items():
            for eq in equations:
                formatted.append({
                    'html': self._format_html(eq['equation'], eq_type),
                    'latex': eq['equation'],
                    'source': eq['source'],
                    'type': eq['type'],
                    'context': eq.get('context', '')
                })
        return formatted

    def _format_html(self, equation, eq_type):
        """Convert equation to HTML format"""
        if eq_type == 'equation':
            return f'<div class="equation">{equation}</div>'
        elif eq_type == 'formula':
            return f'<div class="formula">{equation}</div>'
        elif eq_type == 'constant':
            return f'<span class="constant">{equation}</span>'
        elif eq_type == 'variable':
            return f'<span class="variable">{equation}</span>'
        elif eq_type == 'table':
            return f'<div class="table-equation">{equation}</div>'

    def save_to_json(self, filename):
        """Save extracted equations to JSON"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.format_equations(), f, indent=2)

    def _extract_from_text(self, content, filepath):
        """Extract equations from plain text content"""
        for eq_type, pattern in self.patterns.items():
            matches = re.finditer(pattern, content)
            for match in matches:
                equation = match.group(1).strip()
                self.equations[eq_type].append({
                    'equation': equation,
                    'source': filepath,
                    'type': eq_type,
                    'context': self._get_context(content, match.start())
                })

if __name__ == "__main__":
    # Set up paths to both codebases
    codebase_paths = [
        "d:\\novafuse-api-superstore\\coherence-reality-systems",
        "d:\\novafuse-api-superstore\\another-codebase"  # Add path to second codebase
    ]
    
    extractor = EquationExtractor(codebase_paths)
    extractor.extract_all()
    extractor.save_to_json("equations.json")
    print("Equation extraction complete!")
