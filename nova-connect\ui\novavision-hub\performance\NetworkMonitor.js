/**
 * Network Monitor
 *
 * This module provides utilities for monitoring network performance.
 */

/**
 * Network Monitor class
 */
class NetworkMonitor {
  /**
   * Constructor
   */
  constructor() {
    this.metrics = {
      requests: {},
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalSize: 0,
      totalTime: 0,
      averageTime: 0
    };

    this.observers = [];
    this.isMonitoring = false;
    this.originalFetch = null;
    this.originalXHR = null;

    // Initialize
    this.init();
  }

  /**
   * Initialize network monitor
   */
  init() {
    // Check if window is available
    if (typeof window === 'undefined' && typeof global === 'undefined') {
      console.warn('Network monitor is not available in this environment');
      return;
    }

    // Set global object
    this.global = typeof window !== 'undefined' ? window : global;
  }

  /**
   * Start monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.clearMetrics();

    // Intercept fetch
    this.interceptFetch();

    // Intercept XMLHttpRequest
    this.interceptXHR();

    console.log('Network monitoring started');
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    // Restore original fetch
    if (this.originalFetch) {
      this.global.fetch = this.originalFetch;
      this.originalFetch = null;
    }

    // Restore original XMLHttpRequest
    if (this.originalXHR) {
      this.global.XMLHttpRequest = this.originalXHR;
      this.originalXHR = null;
    }

    console.log('Network monitoring stopped');
  }

  /**
   * Clear metrics
   */
  clearMetrics() {
    this.metrics = {
      requests: {},
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalSize: 0,
      totalTime: 0,
      averageTime: 0
    };
  }

  /**
   * Intercept fetch
   */
  interceptFetch() {
    if (!this.global.fetch) return;

    // Save original fetch
    this.originalFetch = this.global.fetch;

    // Override fetch
    this.global.fetch = async (input, init) => {
      const url = typeof input === 'string' ? input : input.url;
      const method = init?.method || 'GET';
      const startTime = this.global.performance.now();

      try {
        const response = await this.originalFetch(input, init);

        // Clone response to avoid consuming it
        const clonedResponse = response.clone();

        // Get response size
        const size = await this.getResponseSize(clonedResponse);

        // Record metric
        this.recordRequestMetric({
          url,
          method,
          status: response.status,
          size,
          time: performance.now() - startTime,
          success: response.ok
        });

        return response;
      } catch (error) {
        // Record failed request
        this.recordRequestMetric({
          url,
          method,
          status: 0,
          size: 0,
          time: performance.now() - startTime,
          success: false,
          error: error.message
        });

        throw error;
      }
    };
  }

  /**
   * Intercept XMLHttpRequest
   */
  interceptXHR() {
    if (!this.global.XMLHttpRequest) return;

    // Save original XMLHttpRequest
    this.originalXHR = this.global.XMLHttpRequest;

    // Override XMLHttpRequest
    this.global.XMLHttpRequest = function() {
      const xhr = new NetworkMonitor.prototype.originalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;
      let method = '';
      let url = '';
      let startTime = 0;

      // Override open
      xhr.open = function() {
        method = arguments[0];
        url = arguments[1];
        return originalOpen.apply(this, arguments);
      };

      // Override send
      xhr.send = function() {
        startTime = performance.now();

        // Add event listeners
        xhr.addEventListener('load', function() {
          const size = xhr.responseText?.length || 0;

          // Record metric
          NetworkMonitor.prototype.recordRequestMetric({
            url,
            method,
            status: xhr.status,
            size,
            time: performance.now() - startTime,
            success: xhr.status >= 200 && xhr.status < 300
          });
        });

        xhr.addEventListener('error', function() {
          // Record failed request
          NetworkMonitor.prototype.recordRequestMetric({
            url,
            method,
            status: xhr.status,
            size: 0,
            time: performance.now() - startTime,
            success: false,
            error: 'Network error'
          });
        });

        xhr.addEventListener('timeout', function() {
          // Record failed request
          NetworkMonitor.prototype.recordRequestMetric({
            url,
            method,
            status: xhr.status,
            size: 0,
            time: performance.now() - startTime,
            success: false,
            error: 'Timeout'
          });
        });

        return originalSend.apply(this, arguments);
      };

      return xhr;
    };

    // Set original XHR
    this.global.XMLHttpRequest.prototype.originalXHR = this.originalXHR;
  }

  /**
   * Get response size
   *
   * @param {Response} response - Fetch response
   * @returns {Promise<number>} Response size in bytes
   */
  async getResponseSize(response) {
    try {
      const blob = await response.blob();
      return blob.size;
    } catch (error) {
      console.error('Error getting response size:', error);
      return 0;
    }
  }

  /**
   * Record request metric
   *
   * @param {Object} data - Request data
   * @param {string} data.url - Request URL
   * @param {string} data.method - Request method
   * @param {number} data.status - Response status
   * @param {number} data.size - Response size
   * @param {number} data.time - Request time
   * @param {boolean} data.success - Whether the request was successful
   * @param {string} [data.error] - Error message
   */
  recordRequestMetric(data) {
    if (!this.isMonitoring) return;

    // Extract domain from URL
    const domain = this.extractDomain(data.url);

    // Create domain entry if it doesn't exist
    if (!this.metrics.requests[domain]) {
      this.metrics.requests[domain] = {
        count: 0,
        successCount: 0,
        failCount: 0,
        totalSize: 0,
        totalTime: 0,
        averageTime: 0,
        requests: []
      };
    }

    // Update domain metrics
    const domainMetrics = this.metrics.requests[domain];
    domainMetrics.count++;

    if (data.success) {
      domainMetrics.successCount++;
    } else {
      domainMetrics.failCount++;
    }

    domainMetrics.totalSize += data.size;
    domainMetrics.totalTime += data.time;
    domainMetrics.averageTime = domainMetrics.totalTime / domainMetrics.count;

    // Add request to domain requests
    domainMetrics.requests.push({
      url: data.url,
      method: data.method,
      status: data.status,
      size: data.size,
      time: data.time,
      success: data.success,
      error: data.error,
      timestamp: Date.now()
    });

    // Limit requests array size
    if (domainMetrics.requests.length > 100) {
      domainMetrics.requests.shift();
    }

    // Update global metrics
    this.metrics.totalRequests++;

    if (data.success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }

    this.metrics.totalSize += data.size;
    this.metrics.totalTime += data.time;
    this.metrics.averageTime = this.metrics.totalTime / this.metrics.totalRequests;

    // Notify observers
    this.notifyObservers({
      type: 'request',
      data
    });
  }

  /**
   * Extract domain from URL
   *
   * @param {string} url - URL
   * @returns {string} Domain
   */
  extractDomain(url) {
    try {
      // Handle relative URLs
      if (url.startsWith('/')) {
        return this.global.location ? this.global.location.hostname : 'localhost';
      }

      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      console.error('Error extracting domain:', error);
      return 'unknown';
    }
  }

  /**
   * Add observer
   *
   * @param {Function} observer - Observer function
   */
  addObserver(observer) {
    this.observers.push(observer);
  }

  /**
   * Remove observer
   *
   * @param {Function} observer - Observer function
   */
  removeObserver(observer) {
    this.observers = this.observers.filter(obs => obs !== observer);
  }

  /**
   * Notify observers
   *
   * @param {Object} data - Data to notify
   */
  notifyObservers(data) {
    this.observers.forEach(observer => {
      try {
        observer(data);
      } catch (error) {
        console.error('Error notifying observer:', error);
      }
    });
  }

  /**
   * Get metrics
   *
   * @returns {Object} Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Get network report
   *
   * @returns {Object} Network report
   */
  getNetworkReport() {
    const report = {
      timestamp: Date.now(),
      totalRequests: this.metrics.totalRequests,
      successRate: this.metrics.totalRequests > 0
        ? (this.metrics.successfulRequests / this.metrics.totalRequests) * 100
        : 0,
      averageTime: this.metrics.averageTime,
      totalSize: this.metrics.totalSize,
      slowestDomains: [],
      busiestDomains: [],
      largestDomains: []
    };

    // Get domains
    const domains = Object.entries(this.metrics.requests);

    // Get slowest domains
    domains.sort((a, b) => b[1].averageTime - a[1].averageTime);
    report.slowestDomains = domains.slice(0, 5).map(([domain, metrics]) => ({
      domain,
      averageTime: metrics.averageTime,
      count: metrics.count
    }));

    // Get busiest domains
    domains.sort((a, b) => b[1].count - a[1].count);
    report.busiestDomains = domains.slice(0, 5).map(([domain, metrics]) => ({
      domain,
      count: metrics.count,
      successRate: metrics.count > 0
        ? (metrics.successCount / metrics.count) * 100
        : 0
    }));

    // Get largest domains
    domains.sort((a, b) => b[1].totalSize - a[1].totalSize);
    report.largestDomains = domains.slice(0, 5).map(([domain, metrics]) => ({
      domain,
      totalSize: metrics.totalSize,
      count: metrics.count
    }));

    return report;
  }
}

// Create singleton instance
const networkMonitor = new NetworkMonitor();

module.exports = networkMonitor;
