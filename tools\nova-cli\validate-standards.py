#!/usr/bin/env python3
"""
Nova Standards Validation Tool
Validates existing Nova components against NovaFuse standards
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import re


@dataclass
class ValidationResult:
    """Validation result for a component"""
    component: str
    passed: bool
    score: float
    issues: List[str]
    recommendations: List[str]


class NovaStandardsValidator:
    """Validates Nova components against established standards"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.standards = self._load_standards()
        self.results: List[ValidationResult] = []
    
    def _load_standards(self) -> Dict[str, Any]:
        """Load NovaFuse standards from NOVA_SCaffolding_STANDARDS.md"""
        standards_file = self.workspace_path / "NOVA_SCaffolding_STANDARDS.md"
        
        # Default standards if file not found
        return {
            "required_endpoints": ["/health", "/metrics", "/auth"],
            "security_requirements": [
                "JWT validation",
                "Q-Score compliance", 
                "∂Ψ=0 enforcement"
            ],
            "observability_requirements": [
                "Prometheus metrics",
                "Anomaly detection",
                "NovaPulse+ integration"
            ],
            "documentation_requirements": [
                "README.md",
                "API documentation",
                "Architecture diagrams"
            ],
            "testing_requirements": [
                "Unit tests",
                "Integration tests",
                "Chaos tests"
            ],
            "language_standards": {
                "go": {
                    "files": ["go.mod", "main.go"],
                    "structure": ["cmd/", "internal/", "pkg/"]
                },
                "python": {
                    "files": ["requirements.txt", "setup.py"],
                    "structure": ["src/", "tests/"]
                },
                "typescript": {
                    "files": ["package.json", "tsconfig.json"],
                    "structure": ["src/", "dist/", "types/"]
                }
            }
        }
    
    def validate_all_components(self) -> Dict[str, Any]:
        """Validate all Nova components in the workspace"""
        print("🔍 Scanning for Nova components...")
        
        # Find all Nova components
        components = self._discover_components()
        
        print(f"📋 Found {len(components)} Nova components")
        
        for component in components:
            print(f"🧪 Validating {component['name']}...")
            result = self._validate_component(component)
            self.results.append(result)
        
        return self._generate_validation_report()
    
    def _discover_components(self) -> List[Dict[str, Any]]:
        """Discover Nova components in the workspace"""
        components = []
        
        # Check src/ directory for Nova components
        src_path = self.workspace_path / "src"
        if src_path.exists():
            for item in src_path.iterdir():
                if item.is_dir() and item.name.lower().startswith("nova"):
                    components.append({
                        "name": item.name,
                        "path": item,
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item)
                    })
        
        # Check for components in other locations
        for pattern in ["nova*", "Nova*"]:
            for item in self.workspace_path.glob(pattern):
                if item.is_dir() and item.name not in [c["name"] for c in components]:
                    components.append({
                        "name": item.name,
                        "path": item,
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item)
                    })
        
        return components
    
    def _detect_component_type(self, path: Path) -> str:
        """Detect component type from directory structure"""
        # Check for specific patterns
        if (path / "auth").exists() or "auth" in path.name.lower():
            return "Security"
        elif (path / "telemetry").exists() or "pulse" in path.name.lower():
            return "Telemetry"
        elif (path / "event-bus").exists() or "core" in path.name.lower():
            return "Infrastructure"
        elif "ui" in path.name.lower() or "vision" in path.name.lower():
            return "UI/Dashboard"
        else:
            return "Unknown"
    
    def _detect_language(self, path: Path) -> str:
        """Detect primary language from files"""
        if (path / "go.mod").exists() or any(path.glob("*.go")):
            return "go"
        elif (path / "package.json").exists() or any(path.glob("*.ts")):
            return "typescript"
        elif (path / "requirements.txt").exists() or any(path.glob("*.py")):
            return "python"
        elif (path / "Cargo.toml").exists() or any(path.glob("*.rs")):
            return "rust"
        else:
            return "unknown"
    
    def _validate_component(self, component: Dict[str, Any]) -> ValidationResult:
        """Validate a single Nova component"""
        issues = []
        recommendations = []
        score_components = []
        
        path = component["path"]
        language = component["language"]
        
        # 1. Validate directory structure
        structure_score = self._validate_directory_structure(path, language, issues, recommendations)
        score_components.append(("structure", structure_score, 0.2))
        
        # 2. Validate required files
        files_score = self._validate_required_files(path, language, issues, recommendations)
        score_components.append(("files", files_score, 0.15))
        
        # 3. Validate security implementation
        security_score = self._validate_security_implementation(path, issues, recommendations)
        score_components.append(("security", security_score, 0.25))
        
        # 4. Validate observability
        observability_score = self._validate_observability(path, issues, recommendations)
        score_components.append(("observability", observability_score, 0.2))
        
        # 5. Validate documentation
        docs_score = self._validate_documentation(path, issues, recommendations)
        score_components.append(("documentation", docs_score, 0.1))
        
        # 6. Validate testing
        testing_score = self._validate_testing(path, issues, recommendations)
        score_components.append(("testing", testing_score, 0.1))
        
        # Calculate weighted score
        total_score = sum(score * weight for _, score, weight in score_components)
        passed = total_score >= 0.8 and len(issues) == 0
        
        return ValidationResult(
            component=component["name"],
            passed=passed,
            score=total_score,
            issues=issues,
            recommendations=recommendations
        )
    
    def _validate_directory_structure(self, path: Path, language: str, 
                                    issues: List[str], recommendations: List[str]) -> float:
        """Validate directory structure against standards"""
        score = 1.0
        
        if language in self.standards["language_standards"]:
            required_dirs = self.standards["language_standards"][language].get("structure", [])
            
            for required_dir in required_dirs:
                if not (path / required_dir).exists():
                    issues.append(f"Missing required directory: {required_dir}")
                    score -= 0.2
        
        # Check for common directories
        common_dirs = ["tests", "docs", "config"]
        for common_dir in common_dirs:
            if not (path / common_dir).exists():
                recommendations.append(f"Consider adding {common_dir}/ directory")
                score -= 0.1
        
        return max(0.0, score)
    
    def _validate_required_files(self, path: Path, language: str,
                               issues: List[str], recommendations: List[str]) -> float:
        """Validate required files exist"""
        score = 1.0
        
        # README.md is always required
        if not (path / "README.md").exists():
            issues.append("Missing README.md file")
            score -= 0.3
        
        # Language-specific files
        if language in self.standards["language_standards"]:
            required_files = self.standards["language_standards"][language].get("files", [])
            
            for required_file in required_files:
                if not (path / required_file).exists():
                    issues.append(f"Missing required file: {required_file}")
                    score -= 0.2
        
        return max(0.0, score)
    
    def _validate_security_implementation(self, path: Path, 
                                        issues: List[str], recommendations: List[str]) -> float:
        """Validate security implementation"""
        score = 1.0
        
        # Check for auth-related files
        auth_patterns = ["auth", "security", "jwt", "q-score"]
        auth_found = False
        
        for pattern in auth_patterns:
            if any(path.rglob(f"*{pattern}*")):
                auth_found = True
                break
        
        if not auth_found:
            issues.append("No authentication/security implementation found")
            score -= 0.5
        
        # Check for Q-Score implementation
        q_score_found = False
        for file_path in path.rglob("*.py"):
            try:
                content = file_path.read_text()
                if "q_score" in content.lower() or "∂Ψ" in content:
                    q_score_found = True
                    break
            except:
                pass
        
        if not q_score_found:
            recommendations.append("Implement Q-Score validation for ∂Ψ=0 compliance")
            score -= 0.2
        
        return max(0.0, score)
    
    def _validate_observability(self, path: Path,
                              issues: List[str], recommendations: List[str]) -> float:
        """Validate observability implementation"""
        score = 1.0
        
        # Check for metrics implementation
        metrics_patterns = ["metrics", "prometheus", "telemetry"]
        metrics_found = False
        
        for pattern in metrics_patterns:
            if any(path.rglob(f"*{pattern}*")):
                metrics_found = True
                break
        
        if not metrics_found:
            issues.append("No metrics/observability implementation found")
            score -= 0.4
        
        # Check for health endpoint
        health_found = False
        for file_path in path.rglob("*.py"):
            try:
                content = file_path.read_text()
                if "/health" in content:
                    health_found = True
                    break
            except:
                pass
        
        if not health_found:
            recommendations.append("Implement /health endpoint")
            score -= 0.2
        
        return max(0.0, score)
    
    def _validate_documentation(self, path: Path,
                              issues: List[str], recommendations: List[str]) -> float:
        """Validate documentation"""
        score = 1.0
        
        readme_path = path / "README.md"
        if readme_path.exists():
            try:
                content = readme_path.read_text()
                if len(content) < 100:
                    recommendations.append("README.md is too brief, add more details")
                    score -= 0.3
            except:
                pass
        
        # Check for API documentation
        api_docs_found = any(path.rglob("*api*")) or any(path.rglob("*swagger*"))
        if not api_docs_found:
            recommendations.append("Add API documentation")
            score -= 0.2
        
        return max(0.0, score)
    
    def _validate_testing(self, path: Path,
                         issues: List[str], recommendations: List[str]) -> float:
        """Validate testing implementation"""
        score = 1.0
        
        # Check for test files
        test_files = list(path.rglob("*test*"))
        if not test_files:
            issues.append("No test files found")
            score -= 0.5
        
        return max(0.0, score)
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_components = len(self.results)
        passed_components = sum(1 for r in self.results if r.passed)
        
        avg_score = sum(r.score for r in self.results) / total_components if total_components > 0 else 0.0
        
        return {
            "summary": {
                "total_components": total_components,
                "passed_components": passed_components,
                "failed_components": total_components - passed_components,
                "average_score": avg_score,
                "compliance_rate": passed_components / total_components if total_components > 0 else 0.0
            },
            "components": [
                {
                    "name": result.component,
                    "passed": result.passed,
                    "score": result.score,
                    "issues": result.issues,
                    "recommendations": result.recommendations
                }
                for result in self.results
            ]
        }


def main():
    import sys
    
    workspace = sys.argv[1] if len(sys.argv) > 1 else "."
    
    validator = NovaStandardsValidator(workspace)
    report = validator.validate_all_components()
    
    print("\n" + "="*60)
    print("NOVA STANDARDS VALIDATION REPORT")
    print("="*60)
    print(json.dumps(report, indent=2))
    
    # Exit with error if compliance is below threshold
    if report["summary"]["compliance_rate"] < 0.8:
        print(f"\n❌ Compliance rate ({report['summary']['compliance_rate']:.1%}) below 80% threshold")
        sys.exit(1)
    else:
        print(f"\n✅ Compliance rate: {report['summary']['compliance_rate']:.1%}")


if __name__ == "__main__":
    main()
