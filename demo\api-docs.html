<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse API Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .bg-primary {
            background-color: #172030;
        }
        .bg-secondary {
            background-color: #1e293b;
        }
        .text-primary {
            color: #f8fafc;
        }
        .text-secondary {
            color: #cbd5e1;
        }
        .accent-color {
            color: #2563eb;
        }
        .accent-bg {
            background-color: #2563eb;
        }
        pre {
            background-color: #0f172a;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        .method {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: bold;
            display: inline-block;
            width: 60px;
            text-align: center;
        }
        .get {
            background-color: #0891b2;
            color: white;
        }
        .post {
            background-color: #65a30d;
            color: white;
        }
        .put {
            background-color: #d97706;
            color: white;
        }
        .delete {
            background-color: #dc2626;
            color: white;
        }
    </style>
</head>
<body class="bg-primary text-primary min-h-screen">
    <header class="bg-secondary shadow-lg">
        <div class="container mx-auto px-4 py-6 flex justify-between items-center">
            <h1 class="text-3xl font-bold">NovaFuse API Documentation</h1>
            <div class="flex space-x-4">
                <button class="accent-bg text-white px-4 py-2 rounded hover:bg-blue-700">
                    Get API Key
                </button>
                <button class="border border-blue-600 text-blue-600 px-4 py-2 rounded hover:bg-blue-900 hover:bg-opacity-20">
                    Back to Marketplace
                </button>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row gap-8">
            <!-- Sidebar -->
            <div class="w-full md:w-1/4">
                <div class="bg-secondary p-4 rounded-lg sticky top-4">
                    <h2 class="text-xl font-bold mb-4">API Categories</h2>
                    <ul class="space-y-2">
                        <li><a href="#governance" class="text-blue-400 hover:text-blue-300">Governance & Board Compliance</a></li>
                        <li><a href="#security" class="text-blue-400 hover:text-blue-300">Security</a></li>
                        <li><a href="#apis" class="text-blue-400 hover:text-blue-300">APIs & Developer Tools</a></li>
                        <li><a href="#risk" class="text-blue-400 hover:text-blue-300">Risk & Audit</a></li>
                        <li><a href="#contracts" class="text-blue-400 hover:text-blue-300">Contracts & Policy Lifecycle</a></li>
                        <li><a href="#certifications" class="text-blue-400 hover:text-blue-300">Certifications & Accreditation</a></li>
                    </ul>

                    <h2 class="text-xl font-bold mt-8 mb-4">SDKs</h2>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-blue-400 hover:text-blue-300">JavaScript</a></li>
                        <li><a href="#" class="text-blue-400 hover:text-blue-300">Python</a></li>
                        <li><a href="#" class="text-blue-400 hover:text-blue-300">Java</a></li>
                        <li><a href="#" class="text-blue-400 hover:text-blue-300">C#</a></li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="w-full md:w-3/4">
                <!-- Introduction -->
                <div class="mb-12">
                    <h2 class="text-3xl font-bold mb-4">Introduction</h2>
                    <p class="mb-4">
                        Welcome to the NovaFuse API Superstore documentation. This API allows you to integrate with NovaFuse's governance, risk, and compliance platform.
                    </p>
                    <p class="mb-4">
                        All API requests require authentication using an API key. You can obtain an API key by registering as a partner.
                    </p>
                    <div class="bg-secondary p-4 rounded-lg mb-4">
                        <h3 class="text-xl font-bold mb-2">Base URL</h3>
                        <code class="text-green-400">https://api.novafuse.io</code>
                    </div>
                    <div class="bg-secondary p-4 rounded-lg">
                        <h3 class="text-xl font-bold mb-2">Authentication</h3>
                        <p class="mb-2">Add your API key to the request header:</p>
                        <code class="text-green-400">apikey: YOUR_API_KEY</code>
                    </div>
                </div>

                <!-- Governance API -->
                <div id="governance" class="mb-12">
                    <h2 class="text-3xl font-bold mb-6">Governance & Board Compliance</h2>
                    
                    <!-- Board Meetings Endpoint -->
                    <div class="bg-secondary p-6 rounded-lg mb-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <span class="method get">GET</span>
                                <code class="text-green-400 ml-2">/governance/board/meetings</code>
                            </div>
                            <button class="text-sm bg-gray-700 px-3 py-1 rounded hover:bg-gray-600">Try It</button>
                        </div>
                        <p class="mb-4">Get a list of board meetings with optional filtering.</p>
                        
                        <h4 class="font-bold mb-2">Parameters</h4>
                        <table class="w-full mb-4">
                            <thead class="bg-gray-800">
                                <tr>
                                    <th class="text-left p-2">Name</th>
                                    <th class="text-left p-2">Type</th>
                                    <th class="text-left p-2">Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-700">
                                    <td class="p-2">status</td>
                                    <td class="p-2">string</td>
                                    <td class="p-2">Filter by status (scheduled, completed, cancelled)</td>
                                </tr>
                                <tr class="border-b border-gray-700">
                                    <td class="p-2">start_date</td>
                                    <td class="p-2">string</td>
                                    <td class="p-2">Filter by start date (YYYY-MM-DD)</td>
                                </tr>
                                <tr>
                                    <td class="p-2">end_date</td>
                                    <td class="p-2">string</td>
                                    <td class="p-2">Filter by end date (YYYY-MM-DD)</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <h4 class="font-bold mb-2">Response</h4>
                        <pre class="text-green-400">
{
  "data": [
    {
      "id": "bm-001",
      "title": "Q1 Board Meeting",
      "date": "2025-01-15",
      "status": "scheduled",
      "agenda": [
        "Financial review",
        "Strategic initiatives",
        "Compliance updates"
      ],
      "participants": [
        { "id": "p-001", "name": "John Smith", "role": "Chairman" },
        { "id": "p-002", "name": "Jane Doe", "role": "CEO" },
        { "id": "p-003", "name": "Bob Johnson", "role": "CFO" }
      ]
    },
    {
      "id": "bm-002",
      "title": "Q2 Board Meeting",
      "date": "2025-04-15",
      "status": "scheduled"
    }
  ],
  "total": 2,
  "page": 1,
  "limit": 10
}</pre>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-secondary text-white py-12 mt-12">
        <div class="container mx-auto px-4">
            <div class="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
                <p>&copy; 2025 NovaFuse. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
