import { useState, useEffect, useRef, useCallback } from 'react';
import { useNovaCore } from '@novafuse/nova-core';
import { QUALITY_PROFILES } from '../config/quantum-stream';

// Performance metrics tracking window (in seconds)
const METRICS_WINDOW = 5;
// Minimum samples before making quality adjustments
const MIN_SAMPLES = 10;
// Hysteresis to prevent rapid quality toggling
const HYSTERESIS = 0.9;

/**
 * Hook for adaptive quality management of quantum streams
 * @param {Object} config - Configuration object
 * @param {string} [config.initialQuality='high'] - Initial quality setting
 * @param {boolean} [config.autoAdjust=true] - Whether to automatically adjust quality
 * @param {number} [config.targetFps=60] - Target frames per second
 * @param {number} [config.minFps=45] - Minimum acceptable FPS before reducing quality
 * @param {function} [config.onQualityChange] - Callback when quality changes
 * @returns {Object} - Quality controls and metrics
 */
export function useAdaptiveStream({
  initialQuality = 'high',
  autoAdjust = true,
  targetFps = 60,
  minFps = 45,
  onQualityChange
} = {}) {
  const { novaTrack } = useNovaCore();
  const [quality, setQuality] = useState(initialQuality);
  const [isAdjusting, setIsAdjusting] = useState(false);
  const metrics = useRef({
    frameTimes: [],
    fpsSamples: [],
    qualityChanges: 0,
    lastAdjustment: 0,
    lastMetrics: {}
  });
  
  // Performance observer for memory and CPU metrics
  const perfObserver = useRef(null);
  
  // Get available quality levels
  const qualityLevels = useMemo(() => Object.keys(QUALITY_PROFILES), []);
  
  // Get current quality profile
  const profile = useMemo(() => QUALITY_PROFILES[quality] || {}, [quality]);
  
  /**
   * Calculate current FPS based on frame times
   */
  const calculateFps = useCallback(() => {
    const { frameTimes } = metrics.current;
    if (frameTimes.length < 2) return 0;
    
    const avgFrameTime = frameTimes.reduce((sum, t) => sum + t, 0) / frameTimes.length;
    return 1000 / avgFrameTime;
  }, []);
  
  /**
   * Record a frame render time
   * @param {number} frameTime - Time taken to render the frame in ms
   */
  const recordFrame = useCallback((frameTime) => {
    const now = Date.now();
    const { frameTimes } = metrics.current;
    
    // Add new frame time
    frameTimes.push(frameTime);
    
    // Remove old frame times outside our metrics window
    while (frameTimes.length > 0 && frameTimes[0] < now - (METRICS_WINDOW * 1000)) {
      frameTimes.shift();
    }
    
    // Update FPS samples every second
    if (frameTimes.length >= 2) {
      const currentFps = calculateFps();
      metrics.current.fpsSamples.push({
        timestamp: now,
        fps: currentFps
      });
      
      // Keep only recent samples
      while (metrics.current.fpsSamples.length > METRICS_WINDOW) {
        metrics.current.fpsSamples.shift();
      }
    }
  }, [calculateFps]);
  
  /**
   * Get current performance metrics
   */
  const getMetrics = useCallback(() => {
    const { frameTimes, fpsSamples } = metrics.current;
    const currentFps = calculateFps();
    
    // Calculate FPS stability (coefficient of variation)
    let fpsStability = 0;
    if (fpsSamples.length > 1) {
      const mean = fpsSamples.reduce((sum, s) => sum + s.fps, 0) / fpsSamples.length;
      const variance = fpsSamples.reduce((sum, s) => {
        return sum + Math.pow(s.fps - mean, 2);
      }, 0) / fpsSamples.length;
      fpsStability = Math.sqrt(variance) / mean;
    }
    
    return {
      fps: currentFps,
      frameTime: frameTimes[frameTimes.length - 1] || 0,
      avgFrameTime: frameTimes.reduce((sum, t) => sum + t, 0) / frameTimes.length || 0,
      frameCount: frameTimes.length,
      fpsStability,
      quality,
      timestamp: Date.now()
    };
  }, [quality, calculateFps]);
  
  /**
   * Adjust quality based on performance metrics
   */
  const adjustQuality = useCallback(() => {
    if (!autoAdjust || isAdjusting) return;
    
    const now = Date.now();
    const { lastAdjustment, qualityChanges } = metrics.current;
    
    // Don't adjust too frequently (min 5s between adjustments)
    if (now - lastAdjustment < 5000) return;
    
    // Need enough samples to make a decision
    if (metrics.current.frameTimes.length < MIN_SAMPLES) return;
    
    const currentMetrics = getMetrics();
    const currentFps = currentMetrics.fps;
    const currentQualityIndex = qualityLevels.indexOf(quality);
    let newQuality = quality;
    
    // Check if we need to reduce quality
    if (currentFps < minFps * HYSTERESIS && currentQualityIndex < qualityLevels.length - 1) {
      // Move to lower quality level
      newQuality = qualityLevels[currentQualityIndex + 1];
    } 
    // Check if we can increase quality
    else if (currentFps > targetFps * (1/HYSTERESIS) && currentQualityIndex > 0) {
      // Only increase quality if FPS is stable
      if (currentMetrics.fpsStability < 0.1) {
        newQuality = qualityLevels[currentQualityIndex - 1];
      }
    }
    
    // Apply quality change if needed
    if (newQuality !== quality) {
      setIsAdjusting(true);
      
      // Update metrics
      metrics.current.qualityChanges++;
      metrics.current.lastAdjustment = now;
      metrics.current.lastMetrics = currentMetrics;
      
      // Notify parent component
      Promise.resolve().then(() => {
        setQuality(newQuality);
        onQualityChange?.(newQuality, quality, currentMetrics);
        
        // Track quality change
        novaTrack('quantum_stream_quality_change', {
          from: quality,
          to: newQuality,
          reason: currentFps < minFps ? 'low_fps' : 'high_performance',
          metrics: currentMetrics,
          timestamp: now
        });
        
        // Reset adjustment lock
        setTimeout(() => setIsAdjusting(false), 1000);
      });
    }
  }, [quality, autoAdjust, minFps, targetFps, qualityLevels, isAdjusting, onQualityChange, getMetrics]);
  
  // Set up performance monitoring
  useEffect(() => {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Monitor long tasks that might affect performance
      perfObserver.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          if (entry.entryType === 'longtask') {
            // Long task detected, consider reducing quality
            metrics.current.frameTimes = [];
            adjustQuality();
          }
        });
      });
      
      perfObserver.current.observe({ entryTypes: ['longtask'] });
    }
    
    return () => {
      if (perfObserver.current) {
        perfObserver.current.disconnect();
      }
    };
  }, [adjustQuality]);
  
  // Periodically check if we need to adjust quality
  useEffect(() => {
    if (!autoAdjust) return;
    
    const interval = setInterval(() => {
      adjustQuality();
    }, 2000); // Check every 2 seconds
    
    return () => clearInterval(interval);
  }, [autoAdjust, adjustQuality]);
  
  // Manually set quality level
  const setQualityLevel = useCallback((newQuality) => {
    if (qualityLevels.includes(newQuality) && newQuality !== quality) {
      const oldQuality = quality;
      setQuality(newQuality);
      
      // Track manual quality change
      novaTrack('quantum_stream_quality_manual', {
        from: oldQuality,
        to: newQuality,
        timestamp: Date.now()
      });
      
      return true;
    }
    return false;
  }, [quality, qualityLevels]);
  
  // Get recommended quality based on device capabilities
  const getRecommendedQuality = useCallback(() => {
    if (typeof navigator === 'undefined') return 'high';
    
    // Check device capabilities
    const isMobile = /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
    const isLowEnd = (navigator.hardwareConcurrency || 4) < 4 || 
                    (navigator.deviceMemory || 4) < 4;
    
    if (isMobile) {
      return isLowEnd ? 'low' : 'medium';
    }
    
    return isLowEnd ? 'medium' : 'high';
  }, []);
  
  return {
    // State
    quality,
    profile,
    isAdjusting,
    
    // Controls
    setQuality: setQualityLevel,
    resetQuality: () => setQualityLevel(initialQuality),
    
    // Metrics
    getMetrics,
    recordFrame,
    
    // Helpers
    getRecommendedQuality,
    getAvailableQualities: () => [...qualityLevels]
  };
}

// Quality profiles configuration
export const QUALITY_PROFILES = {
  high: {
    label: 'High',
    description: 'Maximum visual fidelity',
    updateRate: 60,
    maxQubits: 1000,
    maxEntanglements: 5000,
    particleCount: 10000,
    shadowQuality: 'high',
    textureResolution: 2048,
    antialias: true,
    postProcessing: true,
    physicsQuality: 'high',
    lodThresholds: [0, 0.5, 1],
    maxLights: 8
  },
  medium: {
    label: 'Medium',
    description: 'Balanced performance and quality',
    updateRate: 30,
    maxQubits: 500,
    maxEntanglements: 2000,
    particleCount: 5000,
    shadowQuality: 'medium',
    textureResolution: 1024,
    antialias: true,
    postProcessing: false,
    physicsQuality: 'medium',
    lodThresholds: [0.3, 0.7, 1],
    maxLights: 4
  },
  low: {
    label: 'Low',
    description: 'Maximum performance',
    updateRate: 15,
    maxQubits: 100,
    maxEntanglements: 500,
    particleCount: 1000,
    shadowQuality: 'low',
    textureResolution: 512,
    antialias: false,
    postProcessing: false,
    physicsQuality: 'low',
    lodThresholds: [0.5, 0.8, 1],
    maxLights: 2
  }
};
