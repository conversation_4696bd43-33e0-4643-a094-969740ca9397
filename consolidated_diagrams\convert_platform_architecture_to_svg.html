<!DOCTYPE html>
<html>
<head>
    <title>Convert Platform Architecture Diagrams to SVG</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .diagram-container {
            background: white;
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }
        .controls {
            background: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        button {
            padding: 8px 15px;
            margin: 5px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Platform Architecture Diagrams to SVG</h1>
        
        <div class="controls">
            <button onclick="convertAllDiagrams()">Convert All to SVG</button>
            <button onclick="downloadAllSVGs()">Download All SVGs</button>
            <div id="status" class="status"></div>
        </div>

        <!-- NovaFuse Universal Stack -->
        <div class="diagram-container">
            <h2>NovaFuse Universal Stack</h2>
            <div class="mermaid" id="universal-stack">
                graph TD
                    subgraph "NovaFuse Universal Stack"
                        A["External Systems & Users"] --> B(Interface Layer: NovaAlign Studio)
                        B --> C(Application Layer: Nova Modules & CSEs)
                        C --> D(Governance Layer: Cadence C-AIaaS)
                        D --> E(Foundation Layer: Comphyology Core)
                        E -->|Feedback| C
                        E -->|Governance| D
                    end
                    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef layer fill:#fff,stroke:#000,stroke-width:2px,stroke-dasharray: 5 5,color:#000
                    classDef external fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:box3d
                    class A external
                    class B,C,D,E layer
            </div>
            <div id="universal-stack-svg"></div>
            <button onclick="generateSVG('universal-stack', 'nova_fuse_universal_stack.svg')">Generate SVG</button>
            <button onclick="downloadSVG('universal-stack', 'nova_fuse_universal_stack.svg')">Download SVG</button>
        </div>

        <!-- C-AIaaS (Cadence) Governance Loop -->
        <div class="diagram-container">
            <h2>C-AIaaS (Cadence) Governance Loop</h2>
            <div class="mermaid" id="governance-loop">
                graph TD
                    subgraph "Cadence Governance Loop (∂Ψ=0 Enforcement)"
                        A["System State & Data Input"] --> B{"Entropy Meter (∂Ψ Sensor Array)"}
                        B -- ∂Ψ Measurement --> C("Comphyon Core: Coherence Calculation")
                        C -- Coherence Status --> D{"Governance Logic (UUFT Triads)"}
                        D -- Action Directives --> E["TEE Optimizer Unit"]
                        D -- Pattern Insights --> F["NEPI Accelerator"]
                        E -- Resource Allocation --> G["System Control & Execution"]
                        F -- Anomaly/Opportunity --> G
                        G --> A
                    end
                    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:rectangle
                    classDef decision fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:diamond
                    classDef io fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:parallelogram
                    class A,G io
                    class B,D decision
                    class C,E,F process
            </div>
            <div id="governance-loop-svg"></div>
            <button onclick="generateSVG('governance-loop', 'cadence_governance_loop.svg')">Generate SVG</button>
            <button onclick="downloadSVG('governance-loop', 'cadence_governance_loop.svg')">Download SVG</button>
        </div>

        <!-- NovaAlign Studio Interaction Layers -->
        <div class="diagram-container">
            <h2>NovaAlign Studio Interaction Layers</h2>
            <div class="mermaid" id="studio-interaction">
                %%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%
                graph TD
                    A["User (Comphyological Architect)"] --> B["Design & Configuration Interface"]
                    B --> C["NovaAlign Core Engine"]
                    C -->|"System Blueprints"| D["Comphyological Pattern Library"]
                    C -->|"Component Selection"| E["Nova Modules & CSEs Repository"]
                    D --> F["System Coherence Validation"]
                    E --> F
                    F -->|"Validated Design"| G["Deployment & Orchestration Manager"]
                    G --> H["Operational NovaFuse Platform"]
                    H -->|"Performance Feedback"| B
                    
                    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef user fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef decision fill:#fff,stroke:#000,stroke-width:2px,color:#000
                    classDef storage fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef system fill:#fff,stroke:#000,stroke-width:2px,color:#000
                    
                    class A user
                    class B,G process
                    class C,F decision
                    class D,E storage
                    class H system
            </div>
            <div id="studio-interaction-svg"></div>
            <button onclick="generateSVG('studio-interaction', 'nova_align_studio.svg')">Generate SVG</button>
            <button onclick="downloadSVG('studio-interaction', 'nova_align_studio.svg')">Download SVG</button>
        </div>

        <!-- Application/Data Layer -->
        <div class="diagram-container">
            <h2>Application/Data Layer (NEPI, NovaFold, etc.)</h2>
            <div class="mermaid" id="data-layer">
                %%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%
                graph TD
                    A["Raw / Domain-Specific Data"] --> B["Data Ingestion & Pre-processing"]
                    B --> C["Data Coherence Buffer"]
                    C --> D["NEPI Engine (Pattern Identification)"]
                    C --> E["NovaFold Engine (Protein Coherence)"]
                    C --> F["NECE Engine (Material Coherence)"]
                    C --> G["Other Specialized CSEs / Novas"]

                    D -->|"Coherent Patterns"| H["Comphyology Core / Governance Layer"]
                    E -->|"Optimized Structures"| H
                    F -->|"Material Properties"| H
                    G -->|"Processed Data"| H

                    H --> I["Coherent Output / Action"]
                    
                    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef data fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef buffer fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef engine fill:#fff,stroke:#000,stroke-width:1px,color:#000
                    classDef core fill:#fff,stroke:#000,stroke-width:2px,color:#000
                    classDef output fill:#fff,stroke:#000,stroke-width:2px,color:#000
                    
                    class A,I data
                    class B process
                    class C buffer
                    class D,E,F,G engine
                    class H core
                    class I output
            </div>
            <div id="data-layer-svg"></div>
            <button onclick="generateSVG('data-layer', 'application_data_layer.svg')">Generate SVG</button>
            <button onclick="downloadSVG('data-layer', 'application_data_layer.svg')">Download SVG</button>
        </div>
    </div>

    <script>
        // Initialize Mermaid with USPTO-compliant styling
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#fff',
                primaryTextColor: '#000',
                primaryBorderColor: '#000',
                lineColor: '#000',
                secondaryColor: '#fff',
                tertiaryColor: '#fff'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // Track generated SVGs
        const generatedSVGs = {};

        // Function to generate SVG for a diagram
        async function generateSVG(diagramId, filename) {
            const status = document.getElementById('status');
            status.style.display = 'block';
            status.className = 'status';
            status.textContent = `Generating ${filename}...`;
            
            try {
                const { svg } = await mermaid.render(`svg-${diagramId}`, document.getElementById(diagramId).textContent);
                document.getElementById(`${diagramId}-svg`).innerHTML = svg;
                generatedSVGs[diagramId] = { svg, filename };
                status.textContent = `Generated ${filename} successfully!`;
                status.className = 'status success';
            } catch (error) {
                console.error('Error generating SVG:', error);
                status.textContent = `Error generating ${filename}: ${error.message}`;
                status.className = 'status error';
            }
        }

        // Function to download SVG
        function downloadSVG(diagramId, filename) {
            if (!generatedSVGs[diagramId]) {
                alert('Please generate the SVG first!');
                return;
            }
            
            const svgData = generatedSVGs[diagramId].svg;
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml;charset=utf-8'});
            const svgUrl = URL.createObjectURL(svgBlob);
            
            const downloadLink = document.createElement('a');
            downloadLink.href = svgUrl;
            downloadLink.download = filename;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }

        // Convert all diagrams
        async function convertAllDiagrams() {
            const diagrams = [
                { id: 'universal-stack', filename: 'nova_fuse_universal_stack.svg' },
                { id: 'governance-loop', filename: 'cadence_governance_loop.svg' },
                { id: 'studio-interaction', filename: 'nova_align_studio.svg' },
                { id: 'data-layer', filename: 'application_data_layer.svg' }
            ];
            
            for (const diagram of diagrams) {
                await generateSVG(diagram.id, diagram.filename);
                // Small delay between generations to prevent UI freezing
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // Download all generated SVGs
        function downloadAllSVGs() {
            for (const diagramId in generatedSVGs) {
                downloadSVG(diagramId, generatedSVGs[diagramId].filename);
            }
        }
    </script>
</body>
</html>
