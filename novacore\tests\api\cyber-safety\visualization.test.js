/**
 * Cyber-Safety Visualization API Tests
 * 
 * This file contains tests for the Cyber-Safety visualization API endpoints.
 */

const request = require('supertest');
const app = require('../../../api/app');

describe('Cyber-Safety Visualization API', () => {
  // Test Tri-Domain Tensor endpoint
  describe('GET /api/cyber-safety/visualizations/tri-domain-tensor', () => {
    it('should return Tri-Domain Tensor data', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/tri-domain-tensor');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      // Check data structure
      const { data } = response.body;
      expect(data).toHaveProperty('grc');
      expect(data).toHaveProperty('it');
      expect(data).toHaveProperty('cybersecurity');
      expect(data).toHaveProperty('connections');
      
      // Check domain data structure
      expect(data.grc).toHaveProperty('values');
      expect(data.grc).toHaveProperty('health');
      expect(data.grc).toHaveProperty('entropyContainment');
      
      // Check connections
      expect(Array.isArray(data.connections)).toBe(true);
      if (data.connections.length > 0) {
        expect(data.connections[0]).toHaveProperty('source');
        expect(data.connections[0]).toHaveProperty('target');
        expect(data.connections[0]).toHaveProperty('strength');
      }
    });
  });
  
  // Test Harmony Index endpoint
  describe('GET /api/cyber-safety/visualizations/harmony-index', () => {
    it('should return Harmony Index data', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/harmony-index');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      // Check data structure
      const { data } = response.body;
      expect(data).toHaveProperty('domainData');
      expect(data).toHaveProperty('harmonyHistory');
      
      // Check domain data
      expect(data.domainData).toHaveProperty('grc');
      expect(data.domainData).toHaveProperty('it');
      expect(data.domainData).toHaveProperty('cybersecurity');
      
      // Check domain metrics
      expect(data.domainData.grc).toHaveProperty('score');
      expect(data.domainData.grc).toHaveProperty('metrics');
      
      // Check harmony history
      expect(Array.isArray(data.harmonyHistory)).toBe(true);
    });
  });
  
  // Test Risk-Control Fusion endpoint
  describe('GET /api/cyber-safety/visualizations/risk-control-fusion', () => {
    it('should return Risk-Control Fusion data', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/risk-control-fusion');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      // Check data structure
      const { data } = response.body;
      expect(data).toHaveProperty('riskData');
      expect(data).toHaveProperty('controlData');
      
      // Check risk data
      expect(data.riskData).toHaveProperty('grc');
      expect(data.riskData).toHaveProperty('it');
      expect(data.riskData).toHaveProperty('cybersecurity');
      
      // Check control data
      expect(data.controlData).toHaveProperty('grc');
      expect(data.controlData).toHaveProperty('it');
      expect(data.controlData).toHaveProperty('cybersecurity');
    });
  });
  
  // Test Resonance Spectrogram endpoint
  describe('GET /api/cyber-safety/visualizations/resonance-spectrogram', () => {
    it('should return Resonance Spectrogram data', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/resonance-spectrogram');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      // Check data structure
      const { data } = response.body;
      expect(data).toHaveProperty('domainData');
      expect(data).toHaveProperty('predictionData');
      
      // Check domain data
      expect(data.domainData).toHaveProperty('grc');
      expect(data.domainData).toHaveProperty('it');
      expect(data.domainData).toHaveProperty('cybersecurity');
      expect(data.domainData).toHaveProperty('crossDomainFlows');
      
      // Check domain properties
      expect(data.domainData.grc).toHaveProperty('values');
      expect(data.domainData.grc).toHaveProperty('frequency');
      expect(data.domainData.grc).toHaveProperty('amplitude');
      expect(data.domainData.grc).toHaveProperty('phase');
      
      // Check prediction data
      expect(data.predictionData).toHaveProperty('timeHorizon');
      expect(data.predictionData).toHaveProperty('dissonanceProbability');
      expect(data.predictionData).toHaveProperty('criticalPoints');
    });
  });
  
  // Test Unified Compliance-Security endpoint
  describe('GET /api/cyber-safety/visualizations/unified-compliance-security', () => {
    it('should return Unified Compliance-Security data', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/unified-compliance-security');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      
      // Check data structure
      const { data } = response.body;
      expect(data).toHaveProperty('complianceData');
      expect(data).toHaveProperty('impactAnalysis');
      
      // Check compliance data
      expect(data.complianceData).toHaveProperty('requirements');
      expect(data.complianceData).toHaveProperty('controls');
      expect(data.complianceData).toHaveProperty('implementations');
      expect(data.complianceData).toHaveProperty('links');
      
      // Check impact analysis
      expect(data.impactAnalysis).toHaveProperty('proposedChanges');
    });
  });
  
  // Test real-time data endpoint
  describe('GET /api/cyber-safety/visualizations/:visualizationType/real-time', () => {
    it('should return real-time data for Tri-Domain Tensor', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/tri-domain-tensor/real-time');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });
  });
  
  // Test historical data endpoint
  describe('GET /api/cyber-safety/visualizations/:visualizationType/historical', () => {
    it('should return historical data for Harmony Index', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/harmony-index/historical')
        .query({ startDate: '2023-01-01', endDate: '2023-01-31' });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
    });
  });
  
  // Test export endpoint
  describe('GET /api/cyber-safety/visualizations/:visualizationType/export', () => {
    it('should export data for Risk-Control Fusion', async () => {
      const response = await request(app)
        .get('/api/cyber-safety/visualizations/risk-control-fusion/export')
        .query({ format: 'json' });
      
      expect(response.status).toBe(200);
      expect(response.header['content-type']).toContain('application/json');
    });
  });
  
  // Test feedback endpoint
  describe('POST /api/cyber-safety/visualizations/:visualizationType/feedback', () => {
    it('should submit feedback for Resonance Spectrogram', async () => {
      const feedback = {
        rating: 4,
        comments: 'Very insightful visualization',
        suggestions: 'Add more color options',
        userEmail: '<EMAIL>'
      };
      
      const response = await request(app)
        .post('/api/cyber-safety/visualizations/resonance-spectrogram/feedback')
        .send(feedback);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message', 'Feedback submitted successfully');
    });
  });
});
