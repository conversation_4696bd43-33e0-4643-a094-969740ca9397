/**
 * Database Seeders
 * 
 * This script runs all seeders for the Privacy Management API.
 */

const mongoose = require('mongoose');
const logger = require('../config/logger');
const regulatoryComplianceSeeder = require('./regulatoryComplianceSeeder');

/**
 * Run all seeders
 * @returns {Promise<void>}
 */
const runSeeders = async () => {
  try {
    logger.info('Starting database seeding...');
    
    // Connect to MongoDB
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/privacy-management';
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    logger.info('Connected to MongoDB');
    
    // Run seeders
    await regulatoryComplianceSeeder();
    
    // Add other seeders here
    
    logger.info('Database seeding completed successfully');
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    logger.error('Error seeding database:', error);
    
    // Disconnect from MongoDB
    try {
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
    } catch (disconnectError) {
      logger.error('Error disconnecting from MongoDB:', disconnectError);
    }
    
    process.exit(1);
  }
};

// Run seeders if this script is executed directly
if (require.main === module) {
  runSeeders();
}

module.exports = runSeeders;
