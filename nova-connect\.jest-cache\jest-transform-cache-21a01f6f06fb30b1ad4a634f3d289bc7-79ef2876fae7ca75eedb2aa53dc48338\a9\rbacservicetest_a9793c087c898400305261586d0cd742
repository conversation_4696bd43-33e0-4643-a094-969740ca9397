6ed0873f09e62f5f89d474f57b949e94
/**
 * RBAC Service Unit Tests
 * 
 * This file contains unit tests for the RBAC service.
 */

const mongoose = require('mongoose');
const RBACService = require('../../api/services/RBACService');
const Role = require('../../api/models/Role');
const Permission = require('../../api/models/Permission');
const UserRole = require('../../api/models/UserRole');
const User = require('../../api/models/User');
const {
  ValidationError,
  NotFoundError
} = require('../../api/utils/errors');
const {
  setupTestEnvironment,
  clearDatabase,
  disconnectFromDatabase,
  getTestData
} = require('../setup/rbac-test-setup');

// Initialize RBAC service
const rbacService = new RBACService();

// Test data
let adminUser;
let regularUser;
let viewerUser;
let testRole;
let testPermission;

// Setup and teardown
beforeAll(async () => {
  try {
    const testData = await setupTestEnvironment();
    adminUser = testData.adminUser;
    regularUser = testData.regularUser;
    viewerUser = testData.viewerUser;
    testRole = testData.testRole;
    testPermission = testData.testPermission;
  } catch (error) {
    console.error('Error in beforeAll:', error);
    throw error;
  }
});
afterAll(async () => {
  await disconnectFromDatabase();
});

// Reset database between tests
afterEach(async () => {
  // No need to clear database between tests as we're using transactions
});

// Test suites
describe('RBACService', () => {
  // Role tests
  describe('Role Management', () => {
    test('getAllRoles should return all roles', async () => {
      const roles = await rbacService.getAllRoles();
      expect(roles).toBeDefined();
      expect(Array.isArray(roles)).toBe(true);
      expect(roles.length).toBeGreaterThan(0);

      // Check if roles have expected properties
      const role = roles[0];
      expect(role).toHaveProperty('_id');
      expect(role).toHaveProperty('name');
      expect(role).toHaveProperty('permissions');
    });
    test('getRoleById should return a role by ID', async () => {
      const role = await rbacService.getRoleById(testRole._id);
      expect(role).toBeDefined();
      expect(role._id.toString()).toBe(testRole._id.toString());
      expect(role.name).toBe(testRole.name);
    });
    test('getRoleById should throw NotFoundError for non-existent role', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      await expect(rbacService.getRoleById(nonExistentId)).rejects.toThrow(NotFoundError);
    });
    test('createRole should create a new role', async () => {
      const newRole = {
        name: 'New Test Role',
        description: 'A new role for testing',
        permissions: [testPermission._id]
      };
      const createdRole = await rbacService.createRole(newRole);
      expect(createdRole).toBeDefined();
      expect(createdRole.name).toBe(newRole.name);
      expect(createdRole.description).toBe(newRole.description);
      expect(Array.isArray(createdRole.permissions)).toBe(true);
      expect(createdRole.permissions.length).toBe(1);

      // Clean up
      await Role.deleteOne({
        _id: createdRole._id
      });
    });
    test('createRole should handle string permission format', async () => {
      const newRole = {
        name: 'String Permission Role',
        description: 'A role with string permission format',
        permissions: ['resource:view']
      };
      const createdRole = await rbacService.createRole(newRole);
      expect(createdRole).toBeDefined();
      expect(createdRole.name).toBe(newRole.name);
      expect(Array.isArray(createdRole.permissions)).toBe(true);
      expect(createdRole.permissions.length).toBe(1);

      // Clean up
      await Role.deleteOne({
        _id: createdRole._id
      });
    });
    test('createRole should throw ValidationError for duplicate role name', async () => {
      const duplicateRole = {
        name: testRole.name,
        description: 'This should fail',
        permissions: [testPermission._id]
      };
      await expect(rbacService.createRole(duplicateRole)).rejects.toThrow(ValidationError);
    });
    test('updateRole should update a role', async () => {
      // Create a role to update
      const roleToUpdate = await Role.create({
        name: 'Role To Update',
        description: 'This role will be updated',
        permissions: [testPermission._id]
      });
      const updateData = {
        name: 'Updated Role Name',
        description: 'This role has been updated'
      };
      const updatedRole = await rbacService.updateRole(roleToUpdate._id, updateData);
      expect(updatedRole).toBeDefined();
      expect(updatedRole.name).toBe(updateData.name);
      expect(updatedRole.description).toBe(updateData.description);

      // Clean up
      await Role.deleteOne({
        _id: roleToUpdate._id
      });
    });
    test('deleteRole should delete a role', async () => {
      // Create a role to delete
      const roleToDelete = await Role.create({
        name: 'Role To Delete',
        description: 'This role will be deleted',
        permissions: [testPermission._id]
      });
      const result = await rbacService.deleteRole(roleToDelete._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify role is deleted
      const deletedRole = await Role.findById(roleToDelete._id);
      expect(deletedRole).toBeNull();
    });
  });

  // Permission tests
  describe('Permission Management', () => {
    test('getAllPermissions should return all permissions', async () => {
      const permissions = await rbacService.getAllPermissions();
      expect(permissions).toBeDefined();
      expect(Array.isArray(permissions)).toBe(true);
      expect(permissions.length).toBeGreaterThan(0);

      // Check if permissions have expected properties
      const permission = permissions[0];
      expect(permission).toHaveProperty('_id');
      expect(permission).toHaveProperty('name');
      expect(permission).toHaveProperty('resource');
      expect(permission).toHaveProperty('action');
    });
    test('getPermissionById should return a permission by ID', async () => {
      const permission = await rbacService.getPermissionById(testPermission._id);
      expect(permission).toBeDefined();
      expect(permission._id.toString()).toBe(testPermission._id.toString());
      expect(permission.name).toBe(testPermission.name);
    });
    test('getPermissionByResourceAction should return a permission by resource and action', async () => {
      const permission = await rbacService.getPermissionByResourceAction('resource', 'view');
      expect(permission).toBeDefined();
      expect(permission.resource).toBe('resource');
      expect(permission.action).toBe('view');
    });
    test('createPermission should create a new permission', async () => {
      const newPermission = {
        name: 'New Test Permission',
        description: 'A new permission for testing',
        resource: 'test',
        action: 'test'
      };
      const createdPermission = await rbacService.createPermission(newPermission);
      expect(createdPermission).toBeDefined();
      expect(createdPermission.name).toBe(newPermission.name);
      expect(createdPermission.resource).toBe(newPermission.resource);
      expect(createdPermission.action).toBe(newPermission.action);

      // Clean up
      await Permission.deleteOne({
        _id: createdPermission._id
      });
    });
  });

  // User role tests
  describe('User Role Management', () => {
    test('getUserRoles should return roles for a user', async () => {
      const roles = await rbacService.getUserRoles(adminUser._id);
      expect(roles).toBeDefined();
      expect(Array.isArray(roles)).toBe(true);
      expect(roles.length).toBeGreaterThan(0);

      // Admin should have Administrator role
      const adminRole = roles.find(role => role.name === 'Administrator');
      expect(adminRole).toBeDefined();
    });
    test('assignRoleToUser should assign a role to a user', async () => {
      // Assign test role to regular user
      const result = await rbacService.assignRoleToUser(regularUser._id, testRole._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify role was assigned
      const userRoles = await rbacService.getUserRoles(regularUser._id);
      const hasTestRole = userRoles.some(role => role._id.toString() === testRole._id.toString());
      expect(hasTestRole).toBe(true);

      // Clean up
      await UserRole.deleteOne({
        user: regularUser._id,
        role: testRole._id
      });
    });
    test('removeRoleFromUser should remove a role from a user', async () => {
      // First assign the role
      await UserRole.create({
        user: regularUser._id,
        role: testRole._id
      });

      // Then remove it
      const result = await rbacService.removeRoleFromUser(regularUser._id, testRole._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);

      // Verify role was removed
      const userRoles = await rbacService.getUserRoles(regularUser._id);
      const hasTestRole = userRoles.some(role => role._id.toString() === testRole._id.toString());
      expect(hasTestRole).toBe(false);
    });
  });

  // Permission checking tests
  describe('Permission Checking', () => {
    test('hasPermission should return true for admin with wildcard permission', async () => {
      const hasPermission = await rbacService.hasPermission(adminUser._id, 'any:permission');
      expect(hasPermission).toBe(true);
    });
    test('hasPermission should return true for user with specific permission', async () => {
      const hasPermission = await rbacService.hasPermission(regularUser._id, 'resource:view');
      expect(hasPermission).toBe(true);
    });
    test('hasPermission should return false for user without permission', async () => {
      const hasPermission = await rbacService.hasPermission(viewerUser._id, 'resource:delete');
      expect(hasPermission).toBe(false);
    });
    test('getUserPermissions should return all permissions for a user', async () => {
      const permissions = await rbacService.getUserPermissions(regularUser._id);
      expect(permissions).toBeDefined();
      expect(Array.isArray(permissions)).toBe(true);
      expect(permissions.length).toBeGreaterThan(0);

      // Regular user should have resource:view permission
      const hasViewPermission = permissions.some(p => p === 'resource:view' || p.toString() === testPermission._id.toString());
      expect(hasViewPermission).toBe(true);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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