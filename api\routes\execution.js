const express = require('express');
const router = express.Router();
const { authenticateJWT } = require('../middleware/auth');
const { validateConnectorId } = require('../middleware/validators');

// Import controllers
const connectorExecutionController = require('../controllers/connectorExecutionController');

/**
 * @route POST /api/execution/connectors/:id/execute
 * @desc Execute a connector
 * @access Private
 */
router.post('/connectors/:id/execute', authenticateJWT, validateConnectorId, connectorExecutionController.executeConnectorHandler);

/**
 * @route POST /api/execution/connectors/:id/schedule
 * @desc Schedule a connector for execution
 * @access Private
 */
router.post('/connectors/:id/schedule', authenticateJWT, validateConnectorId, connectorExecutionController.scheduleConnectorHandler);

/**
 * @route DELETE /api/execution/schedule/:jobId
 * @desc Cancel a scheduled connector execution
 * @access Private
 */
router.delete('/schedule/:jobId', authenticateJWT, connectorExecutionController.cancelScheduledConnectorHandler);

/**
 * @route GET /api/execution/connectors/:id/history
 * @desc Get execution history for a connector
 * @access Private
 */
router.get('/connectors/:id/history', authenticateJWT, validateConnectorId, connectorExecutionController.getExecutionHistory);

module.exports = router;
