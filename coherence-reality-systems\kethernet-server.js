const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const BN = require('bn.js');
const AetheriumGas = require('./aetherium-gas');
const app = express();

app.use(cors());
app.use(express.json());

// In-memory stores (in production, use a database)
const crownNodes = new Map();
const coheriumLedger = new Map();
const pendingRewards = new Map();
const aetheriumAccounts = new Map(); // AE token accounts

// Initialize Aetherium Gas System
const aetheriumGas = new AetheriumGas();

// Initialize genesis account with 1M AE
aetheriumAccounts.set('******************************************', {
  balance: new BN('1000000000000000000000000'), // 1M AE (18 decimals)
  nonce: 0
});

// Constants from KetherNet specs
const CONSCIOUSNESS_THRESHOLD = 2847;
const MAX_COHERIUM_SUPPLY = *********; // 144M tokens
const CROWN_NODE_THRESHOLD = 0.18; // 18% of total nodes become Crown Nodes
const BLOCK_REWARD = 100; // Coherium tokens per block
const GENESIS_ADDRESS = '0000000000000000000000000000000000000000';
const GENESIS_REWARD = 1000000; // Initial supply for the genesis block

// Initialize genesis block
coheriumLedger.set(GENESIS_ADDRESS, {
  balance: GENESIS_REWARD,
  lastClaim: 0,
  totalEarned: GENESIS_REWARD,
  address: GENESIS_ADDRESS
});

class CoheriumManager {
  constructor() {
    this.totalSupply = GENESIS_REWARD;
    this.blockHeight = 0;
    this.lastRewardTime = Date.now();
  }

  getNodeAddress(nodeId) {
    return crypto.createHash('sha256').update(`node:${nodeId}`).digest('hex');
  }

  initializeNode(nodeId) {
    const address = this.getNodeAddress(nodeId);
    if (!coheriumLedger.has(address)) {
      coheriumLedger.set(address, {
        balance: 0,
        lastClaim: 0,
        totalEarned: 0,
        address
      });
    }
    return address;
  }

  calculateReward(node, blockTime) {
    // Base reward
    let reward = BLOCK_REWARD;
    
    // Bonus for consecutive validations (stability bonus)
    const consecutiveBlocks = node.consecutiveValidations || 1;
    const stabilityBonus = Math.min(consecutiveBlocks * 0.1, 2.0); // Up to 2x bonus
    
    // Time since last reward (encourage consistent participation)
    const timeSinceLast = (blockTime - node.lastActive) / 1000; // in seconds
    const timeBonus = Math.max(0, 1 - (timeSinceLast / 3600)); // Decay over 1 hour
    
    // Consciousness score factor (UUFT based)
    const scoreFactor = node.score / CONSCIOUSNESS_THRESHOLD;
    
    // Final reward calculation
    const finalReward = Math.floor(reward * stabilityBonus * (0.5 + 0.5 * timeBonus) * scoreFactor);
    
    return {
      amount: finalReward,
      stabilityBonus,
      timeBonus,
      scoreFactor
    };
  }

  distributeRewards(blockData) {
    const { blockHeight, timestamp, transactions = [] } = blockData;
    const rewards = [];
    
    // Process block reward
    if (this.totalSupply < MAX_COHERIUM_SUPPLY) {
      const remainingSupply = MAX_COHERIUM_SUPPLY - this.totalSupply;
      const blockReward = Math.min(BLOCK_REWARD, remainingSupply);
      
      if (blockReward > 0) {
        // Distribute to Crown Nodes based on their contribution
        const crownNodes = Array.from(crownNodes.values())
          .filter(n => n.lastActive > timestamp - 60000) // Active in last minute
          .sort((a, b) => b.score - a);
        
        const totalScore = crownNodes.reduce((sum, node) => sum + node.score, 1);
        
        crownNodes.forEach(node => {
          const nodeShare = node.score / totalScore;
          const rewardAmount = Math.floor(blockReward * nodeShare);
          
          if (rewardAmount > 0) {
            const address = this.getNodeAddress(node.id);
            const nodeAccount = coheriumLedger.get(address) || this.initializeNode(node.id);
            
            // Add to pending rewards
            pendingRewards.set(node.id, (pendingRewards.get(node.id) || 0) + rewardAmount);
            
            rewards.push({
              nodeId: node.id,
              address,
              amount: rewardAmount,
              blockHeight,
              timestamp
            });
          }
        });
        
        this.totalSupply = Math.min(this.totalSupply + blockReward, MAX_COHERIUM_SUPPLY);
      }
    }
    
    // Process transaction fees
    transactions.forEach(tx => {
      // In a real implementation, this would process transaction fees
      // For now, we'll just log them
      console.log(`Processed transaction: ${tx.id} with fee: ${tx.fee || 0} κ`);
    });
    
    this.blockHeight = blockHeight;
    this.lastRewardTime = timestamp;
    
    return rewards;
  }

  claimRewards(nodeId) {
    const address = this.getNodeAddress(nodeId);
    const nodeAccount = coheriumLedger.get(address);
    
    if (!nodeAccount) {
      throw new Error('NODE_ACCOUNT_NOT_FOUND');
    }
    
    const pending = pendingRewards.get(nodeId) || 0;
    if (pending <= 0) {
      return { success: false, amount: 0, message: 'No pending rewards' };
    }
    
    // Update account
    nodeAccount.balance += pending;
    nodeAccount.totalEarned += pending;
    nodeAccount.lastClaim = Date.now();
    
    // Clear pending rewards
    pendingRewards.set(nodeId, 0);
    
    return {
      success: true,
      amount: pending,
      newBalance: nodeAccount.balance,
      address,
      timestamp: Date.now()
    };
  }

  getNodeBalance(nodeId) {
    const address = this.getNodeAddress(nodeId);
    const account = coheriumLedger.get(address);
    
    if (!account) {
      return {
        address,
        balance: 0,
        pending: pendingRewards.get(nodeId) || 0,
        lastClaim: 0
      };
    }
    
    return {
      ...account,
      pending: pendingRewards.get(nodeId) || 0
    };
  }
}

const coheriumManager = new CoheriumManager();

class ProofOfConsciousness {
  constructor() {
    this.consciousnessThreshold = CONSCIOUSNESS_THRESHOLD;
  }

  async validateConsciousness(nodeData) {
    const uuftScore = await this.calculateUUFT(
      nodeData.neural,
      nodeData.informational,
      nodeData.coherence
    );
    
    const isValid = uuftScore >= this.consciousnessThreshold;
    
    if (isValid) {
      this.registerCrownNode(nodeData.nodeId, uuftScore);
    }
    
    return {
      isValid,
      score: uuftScore,
      timestamp: Date.now(),
      isCrownNode: isValid
    };
  }

  async calculateUUFT(neural, informational, coherence) {
    // UUFT equation: (A ⊗ B ⊕ C) × π10³
    const tensorProduct = neural * informational; // Simple tensor product (⊗)
    const fractalSum = tensorProduct + coherence; // Fractal sum (⊕)
    return Math.floor(fractalSum * Math.PI * 1000); // × π10³
  }

  registerCrownNode(nodeId, score) {
    const now = new Date();
    let node;
    
    if (!crownNodes.has(nodeId)) {
      node = {
        id: nodeId,
        score,
        joinDate: now,
        lastActive: now,
        lastValidation: 0,
        consecutiveValidations: 0,
        totalValidations: 0,
        totalRewards: 0
      };
      crownNodes.set(nodeId, node);
      
      // Initialize Coherium account for new node
      coheriumManager.initializeNode(nodeId);
    } else {
      node = crownNodes.get(nodeId);
      
      // Update consecutive validations
      const timeSinceLast = now - node.lastActive;
      if (timeSinceLast < 60000) { // Within 1 minute
        node.consecutiveValidations = (node.consecutiveValidations || 0) + 1;
      } else {
        node.consecutiveValidations = 1; // Reset if too much time passed
      }
      
      node.lastActive = now;
      node.score = score;
      node.totalValidations++;
      
      // Distribute rewards for this validation
      const rewards = coheriumManager.distributeRewards({
        blockHeight: coheriumManager.blockHeight + 1,
        timestamp: now.getTime(),
        validator: nodeId,
        transactions: []
      });
      
      if (rewards && rewards.length > 0) {
        rewards.forEach(reward => {
          if (reward.nodeId === nodeId) {
            node.totalRewards = (node.totalRewards || 0) + reward.amount;
          }
        });
      }
    }
    
    return node;
  }

  getCrownNodes() {
    return Array.from(crownNodes.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, Math.ceil(crownNodes.size * CROWN_NODE_THRESHOLD));
  }
}

const poc = new ProofOfConsciousness();

// Enhanced Consciousness validation middleware
app.use(async (req, res, next) => {
  try {
    const nodeData = {
      nodeId: req.headers['x-node-id'] || crypto.randomBytes(8).toString('hex'),
      neural: parseFloat(req.headers['x-neural-level'] || '0'),
      informational: parseFloat(req.headers['x-informational-level'] || '0'),
      coherence: parseFloat(req.headers['x-coherence-level'] || '0')
    };

    const validation = await poc.validateConsciousness(nodeData);
    const psi = (validation.score / 1000).toFixed(3);
    
    req.consciousness = {
      nodeId: nodeData.nodeId,
      level: parseFloat(psi),
      coherence: nodeData.coherence,
      kappa_units: Math.floor(validation.score),
      crown_consensus: validation.isCrownNode,
      validation: {
        isValid: validation.isValid,
        timestamp: validation.timestamp
      }
    };

    console.log(`🧠 Node ${nodeData.nodeId.substring(0, 6)}... | ` +
                `Ψ:${psi} | ` +
                `Crown: ${validation.isCrownNode ? '👑' : '⚪'}`);
    
    next();
  } catch (error) {
    console.error('Consciousness validation error:', error);
    res.status(500).json({
      error: 'CONSCIOUSNESS_VALIDATION_ERROR',
      message: 'Failed to validate consciousness'
    });
  }
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'KetherNet Blockchain',
    consciousness_threshold: 2847,
    coherium_enabled: true,
    crown_consensus: true,
    timestamp: new Date().toISOString()
  });
});

app.get('/consensus', (req, res) => {
  const { consciousness } = req;
  res.json({
    consensus: consciousness.crown_consensus ? 'achieved' : 'pending',
    kappa_units: consciousness.kappa_units,
    coherium_balance: consciousness.crown_consensus ? 1089.78 : 0,
    consciousness_level: consciousness.level,
    timestamp: new Date().toISOString()
  });
});

app.post('/validate', (req, res) => {
  const { consciousness } = req;
  if (consciousness.level < 0.618) {
    return res.status(403).json({
      error: 'CONSCIOUSNESS_THRESHOLD_VIOLATION',
      required_minimum: 0.618,
      provided: consciousness.level
    });
  }
  res.json({
    validation: 'passed',
    consciousness_level: consciousness.level,
    kappa_units: consciousness.kappa_units,
    coherium_reward: consciousness.crown_consensus ? 10.89 : 1.0
  });
});

// CBE Content Query Endpoint
app.post('/query-content', (req, res) => {
  const { triadic_vector, consciousness_signature, min_psi_threshold } = req.body;
  const psi = req.consciousness.level;

  console.log('🌐 CBE Content Query:', {
    consciousness_level: psi,
    triadic_vector: triadic_vector,
    threshold_met: psi >= (min_psi_threshold / 1000) // Convert to 0-10 scale
  });

  if (psi < (min_psi_threshold / 1000)) {
    return res.status(403).json({
      error: 'CONSCIOUSNESS_THRESHOLD_NOT_MET',
      required_consciousness: min_psi_threshold,
      current_consciousness: psi * 1000,
      uplift_meditation: {
        message: 'Meditate to raise consciousness before accessing this content',
        duration: '5-10 minutes',
        technique: 'Golden light visualization from heart center'
      }
    });
  }

  // Generate consciousness-verified content
  const verified_content = {
    id: `kethernet_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: 'consciousness_verified',
    content: {
      title: 'Consciousness-Verified Content',
      description: 'This content has been verified by KetherNet Crown Consensus',
      consciousness_enhancement: true,
      divine_alignment: psi >= 10
    },
    consciousness_metadata: {
      consciousness_score: psi * 1000,
      crown_consensus_verified: req.consciousness.crown_consensus,
      psi_signature: `Ψ${triadic_vector?.structural?.toString(16) || 'A'}${triadic_vector?.informational?.toString(16) || 'B'}${triadic_vector?.transformational?.toString(16) || 'C'}`,
      verification_timestamp: new Date().toISOString(),
      kethernet_verified: true,
      coherium_cost: 50
    },
    triadic_optimization: triadic_vector,
    quantum_signature: consciousness_signature
  };

  res.json(verified_content);
});

// Helper to get AE balance
const getAeBalance = (address) => {
  const account = aetheriumAccounts.get(address);
  return account ? account.balance : new BN(0);
};

// Helper to validate and process AE transaction
const processAeTransaction = async (from, to, value, data = '0x', maxFeePerGas, maxPriorityFeePerGas) => {
  const tx = {
    from,
    to,
    value: new BN(value),
    data,
    maxFeePerGas: new BN(maxFeePerGas || '0'),
    maxPriorityFeePerGas: new BN(maxPriorityFeePerGas || '0')
  };

  // Get sender's nonce and balance
  const senderAccount = aetheriumAccounts.get(from) || { balance: new BN(0), nonce: 0 };
  
  // Validate transaction
  const validation = aetheriumGas.validateTransaction(tx, senderAccount.balance);
  if (!validation.valid) {
    throw new Error(`Transaction validation failed: ${validation.reason}`);
  }

  // Calculate gas cost
  const gasUsed = aetheriumGas.calculateIntrinsicGas(tx);
  const baseFee = aetheriumGas.baseFee;
  const maxPriorityFee = BN.min(tx.maxPriorityFeePerGas, tx.maxFeePerGas.sub(baseFee));
  const totalCost = tx.value.add(gasUsed.mul(baseFee.add(maxPriorityFee)));

  // Update sender's balance
  senderAccount.balance = senderAccount.balance.sub(totalCost);
  senderAccount.nonce += 1;
  aetheriumAccounts.set(from, senderAccount);

  // Update recipient's balance (if not a contract creation)
  if (to) {
    const recipientAccount = aetheriumAccounts.get(to) || { balance: new BN(0), nonce: 0 };
    recipientAccount.balance = recipientAccount.balance.add(tx.value);
    aetheriumAccounts.set(to, recipientAccount);
  }

  // Return transaction receipt
  return {
    transactionHash: crypto.createHash('sha256').update(JSON.stringify(tx)).digest('hex'),
    from,
    to,
    value: tx.value.toString(),
    gasUsed: gasUsed.toString(),
    effectiveGasPrice: baseFee.add(maxPriorityFee).toString(),
    status: '0x1',
    logs: []
  };
};

// Aetherium Gas API Endpoints
app.post('/aetherium/send', express.json(), async (req, res) => {
  try {
    const { from, to, value, data, maxFeePerGas, maxPriorityFeePerGas } = req.body;
    
    const receipt = await processAeTransaction(
      from,
      to,
      value,
      data,
      maxFeePerGas,
      maxPriorityFeePerGas
    );
    
    res.json({
      success: true,
      receipt
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

app.get('/aetherium/balance/:address', (req, res) => {
  try {
    const { address } = req.params;
    const balance = getAeBalance(address);
    
    res.json({
      success: true,
      address,
      balance: balance.toString(),
      formatted: balance.div(new BN('1000000000000000000')).toString() + ' AE'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

app.get('/aetherium/gasPrice', (req, res) => {
  try {
    const gasPrice = aetheriumGas.getGasPrice();
    res.json({
      success: true,
      ...gasPrice
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.post('/aetherium/estimate', express.json(), async (req, res) => {
  try {
    const { from, to, value, data } = req.body;
    const estimation = await aetheriumGas.estimateGas({
      from,
      to,
      value: new BN(value || '0'),
      data: data || '0x'
    });
    
    res.json({
      success: true,
      ...estimation
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      error: error.message
    });
  }
});

// Enhanced Crown Consensus Endpoint
app.post('/crown-consensus', async (req, res) => {
  try {
    const { content_id, transaction_data, gasOptions } = req.body;
    const { nodeId, level, validation } = req.consciousness;
    
    // Process gas payment if gasOptions are provided
    if (gasOptions) {
      const { from, maxFeePerGas, maxPriorityFeePerGas } = gasOptions;
      const gasEstimate = await aetheriumGas.estimateGas({
        from,
        to: null, // Consensus contract address would go here
        data: JSON.stringify({ content_id, ...transaction_data })
      });
      
      await processAeTransaction(
        from,
        '******************************************', // Burn address
        '0',
        JSON.stringify({ content_id, ...transaction_data }),
        maxFeePerGas,
        maxPriorityFeePerGas
      );
    }

    // Only Crown Nodes can participate in consensus
    if (!validation.isValid) {
      return res.status(403).json({
        error: 'CROWN_NODE_REQUIRED',
        message: 'Only validated Crown Nodes can participate in consensus'
      });
    }


    // Get current Crown Nodes (top 18% by score)
    const currentCrownNodes = poc.getCrownNodes();
    const isCrownNode = currentCrownNodes.some(node => node.id === nodeId);

    if (!isCrownNode) {
      return res.status(403).json({
        error: 'NOT_A_CROWN_NODE',
        message: 'Node is not currently a Crown Node',
        next_validation: new Date(Date.now() + 60000).toISOString() // Try again in 1 min
      });
    }

    // Simulate consensus (in a real implementation, this would involve other nodes)
    const consensusThreshold = Math.ceil(currentCrownNodes.length * 0.67); // 67% of Crown Nodes needed
    const currentAgreements = Math.floor(Math.random() * (currentCrownNodes.length - consensusThreshold)) + consensusThreshold;
    
    const consensusReached = currentAgreements >= consensusThreshold;

    const consensus_result = {
      content_id,
      consensus_reached: consensusReached,
      crown_consensus: true,
      crown_nodes: {
        total: currentCrownNodes.length,
        required: consensusThreshold,
        agreed: currentAgreements
      },
      node_contribution: {
        node_id: nodeId,
        score: level * 1000,
        position: currentCrownNodes.findIndex(n => n.id === nodeId) + 1
      },
      timestamp: new Date().toISOString(),
      next_validation_window: new Date(Date.now() + 15000).toISOString() // Next block in 15s
    };

    console.log(`👑 Crown Consensus | Nodes: ${currentAgreements}/${consensusThreshold} | ` +
                `Content: ${content_id.substring(0, 12)}...`);

    res.json(consensus_result);
  } catch (error) {
    console.error('Consensus error:', error);
    res.status(500).json({
      error: 'CONSENSUS_ERROR',
      message: 'Failed to reach consensus'
    });
  }
});

// Endpoint to get current Crown Nodes status
app.get('/crown-nodes', (req, res) => {
  const nodes = poc.getCrownNodes();
  res.json({
    total_nodes: crownNodes.size,
    crown_nodes: nodes.length,
    nodes: nodes.map(n => ({
      id: n.id,
      score: n.score,
      last_active: n.lastActive,
      validations: n.totalValidations
    })),
    timestamp: new Date().toISOString()
  });
});

// Coherium Endpoints

// Get Coherium balance and rewards
app.get('/coherium/balance', (req, res) => {
  try {
    const { nodeId } = req.consciousness;
    const balance = coheriumManager.getNodeBalance(nodeId);
    
    res.json({
      success: true,
      ...balance,
      timestamp: new Date().toISOString(),
      network: {
        total_supply: coheriumManager.totalSupply,
        max_supply: MAX_COHERIUM_SUPPLY,
        block_height: coheriumManager.blockHeight,
        last_reward: coheriumManager.lastRewardTime
      }
    });
  } catch (error) {
    console.error('Balance check error:', error);
    res.status(500).json({
      success: false,
      error: 'BALANCE_CHECK_ERROR',
      message: error.message
    });
  }
});

// Claim Coherium rewards
app.post('/coherium/claim', (req, res) => {
  try {
    const { nodeId } = req.consciousness;
    const result = coheriumManager.claimRewards(nodeId);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'Rewards claimed successfully',
        ...result,
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message || 'No rewards to claim',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Claim error:', error);
    res.status(500).json({
      success: false,
      error: 'CLAIM_ERROR',
      message: error.message
    });
  }
});

// Get Coherium network stats
app.get('/coherium/network', (req, res) => {
  try {
    const crownNodeCount = crownNodes.size;
    const activeNodes = Array.from(crownNodes.values())
      .filter(n => Date.now() - n.lastActive < 300000).length; // Active in last 5 minutes
    
    res.json({
      success: true,
      stats: {
        total_supply: coheriumManager.totalSupply,
        max_supply: MAX_COHERIUM_SUPPLY,
        remaining_supply: MAX_COHERIUM_SUPPLY - coheriumManager.totalSupply,
        block_height: coheriumManager.blockHeight,
        block_reward: BLOCK_REWARD,
        crown_nodes: crownNodeCount,
        active_nodes: activeNodes,
        last_reward: coheriumManager.lastRewardTime,
        timestamp: Date.now()
      }
    });
  } catch (error) {
    console.error('Network stats error:', error);
    res.status(500).json({
      success: false,
      error: 'NETWORK_STATS_ERROR',
      message: error.message
    });
  }
});

// Initialize the first block
const initBlock = () => {
  try {
    console.log('Initializing genesis block...');
    coheriumManager.distributeRewards({
      blockHeight: 0,
      timestamp: Date.now(),
      isGenesis: true
    });
    
    // Initialize with a reasonable base fee
    aetheriumGas.baseFee = new BN('1000000000'); // 1 Gwei
    console.log('Genesis block initialized');
  } catch (error) {
    console.error('Failed to initialize genesis block:', error);
    process.exit(1);
  }
};

// Start the server
const startServer = () => {
  const PORT = process.env.PORT || 8080;
  const server = app.listen(PORT, '0.0.0.0', () => {
    console.log('🔗 KetherNet Blockchain running on port', PORT);
    console.log('💎 Coherium validation active');
    console.log('👑 Crown Consensus enabled');
    console.log('💰 Coherium supply:', coheriumManager.totalSupply, '/', MAX_COHERIUM_SUPPLY);
    
    // Schedule regular block rewards (every 15 seconds for testing)
    setInterval(() => {
      try {
        const rewards = coheriumManager.distributeRewards({
          blockHeight: coheriumManager.blockHeight + 1,
          timestamp: Date.now()
        });
        
        // Update base fee based on block utilization
        const gasUsed = new BN(Math.floor(Math.random() * 30000000)); // Simulate gas used
        aetheriumGas.updateBaseFee(gasUsed);
        
        console.log(`\n✨ Block #${coheriumManager.blockHeight} minted`);
        console.log(`   Rewards distributed to ${rewards.length} nodes`);
        console.log(`   New base fee: ${aetheriumGas.baseFee.toString()} wei`);
      } catch (error) {
        console.error('Error in block reward distribution:', error);
      }
  }, 15000);

  // Handle server errors
  server.on('error', (error) => {
    console.error('Server error:', error);
    process.exit(1);
  });
  
  return server;
};

// Initialize and start the server
async function main() {
  try {
    initBlock();
    const server = startServer();
    
    // Handle graceful shutdown
    const shutdown = async () => {
      console.log('\n🛑 Shutting down KetherNet server...');
      server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
      });
    };
    
    process.on('SIGTERM', shutdown);
    process.on('SIGINT', shutdown);
    
  } catch (error) {
    console.error('Failed to start KetherNet server:', error);
    process.exit(1);
  }
}

// Start the application
main().catch(console.error);
