/**
 * @swagger
 * components:
 *   schemas:
 *     SecurityAssessment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the security assessment
 *         title:
 *           type: string
 *           description: Title of the security assessment
 *         description:
 *           type: string
 *           description: Description of the security assessment
 *         type:
 *           type: string
 *           enum: [internal, external, vendor, compliance]
 *           description: Type of security assessment
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the security assessment
 *         scope:
 *           type: string
 *           description: Scope of the security assessment
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the security assessment
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the security assessment
 *         findings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/SecurityFinding'
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to the assessment
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the assessment was last updated
 *       required:
 *         - id
 *         - title
 *         - type
 *         - status
 *         - startDate
 *         - createdAt
 *         - updatedAt
 *     
 *     SecurityAssessmentInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the security assessment
 *         description:
 *           type: string
 *           description: Description of the security assessment
 *         type:
 *           type: string
 *           enum: [internal, external, vendor, compliance]
 *           description: Type of security assessment
 *         status:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *           description: Status of the security assessment
 *         scope:
 *           type: string
 *           description: Scope of the security assessment
 *         startDate:
 *           type: string
 *           format: date
 *           description: Start date of the security assessment
 *         endDate:
 *           type: string
 *           format: date
 *           description: End date of the security assessment
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to the assessment
 *       required:
 *         - title
 *         - type
 *         - status
 *         - startDate
 *     
 *     SecurityFinding:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the security finding
 *         title:
 *           type: string
 *           description: Title of the security finding
 *         description:
 *           type: string
 *           description: Description of the security finding
 *         severity:
 *           type: string
 *           enum: [critical, high, medium, low, info]
 *           description: Severity of the security finding
 *         status:
 *           type: string
 *           enum: [open, in-remediation, remediated, accepted, false-positive]
 *           description: Status of the security finding
 *         remediation:
 *           type: string
 *           description: Recommended remediation steps
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to remediate the finding
 *         dueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the finding was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the finding was last updated
 *       required:
 *         - id
 *         - title
 *         - severity
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     SecurityFindingInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the security finding
 *         description:
 *           type: string
 *           description: Description of the security finding
 *         severity:
 *           type: string
 *           enum: [critical, high, medium, low, info]
 *           description: Severity of the security finding
 *         status:
 *           type: string
 *           enum: [open, in-remediation, remediated, accepted, false-positive]
 *           description: Status of the security finding
 *         remediation:
 *           type: string
 *           description: Recommended remediation steps
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to remediate the finding
 *         dueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation
 *       required:
 *         - title
 *         - severity
 *         - status
 */

// Sample security assessments
const securityAssessments = [
  {
    id: 'sa-001',
    title: 'Annual Network Security Assessment',
    description: 'Comprehensive assessment of network security controls and vulnerabilities',
    type: 'internal',
    status: 'completed',
    scope: 'Corporate network infrastructure including firewalls, routers, and switches',
    startDate: '2024-01-10',
    endDate: '2024-01-25',
    findings: [
      {
        id: 'sf-001',
        title: 'Outdated Firewall Firmware',
        description: 'The main firewall is running outdated firmware with known vulnerabilities',
        severity: 'high',
        status: 'remediated',
        remediation: 'Update firewall firmware to the latest version',
        assignedTo: 'Network Team',
        dueDate: '2024-02-15',
        createdAt: '2024-01-15T14:30:00Z',
        updatedAt: '2024-02-10T09:45:00Z'
      },
      {
        id: 'sf-002',
        title: 'Weak SSH Configuration',
        description: 'SSH servers are configured to use weak ciphers and authentication methods',
        severity: 'medium',
        status: 'in-remediation',
        remediation: 'Update SSH configuration to use strong ciphers and authentication methods',
        assignedTo: 'System Administration Team',
        dueDate: '2024-02-28',
        createdAt: '2024-01-18T11:20:00Z',
        updatedAt: '2024-02-05T16:30:00Z'
      }
    ],
    assignedTo: 'Internal Security Team',
    createdAt: '2024-01-05T09:00:00Z',
    updatedAt: '2024-01-30T15:45:00Z'
  },
  {
    id: 'sa-002',
    title: 'Cloud Infrastructure Security Review',
    description: 'Security assessment of AWS cloud infrastructure and services',
    type: 'external',
    status: 'in-progress',
    scope: 'AWS environment including EC2, S3, RDS, and IAM configurations',
    startDate: '2024-03-01',
    endDate: '2024-03-15',
    findings: [
      {
        id: 'sf-003',
        title: 'Excessive S3 Bucket Permissions',
        description: 'Multiple S3 buckets have overly permissive access controls',
        severity: 'high',
        status: 'open',
        remediation: 'Review and restrict S3 bucket permissions following the principle of least privilege',
        assignedTo: 'Cloud Team',
        dueDate: '2024-03-20',
        createdAt: '2024-03-05T10:15:00Z',
        updatedAt: '2024-03-05T10:15:00Z'
      }
    ],
    assignedTo: 'External Security Consultant',
    createdAt: '2024-02-15T13:30:00Z',
    updatedAt: '2024-03-05T16:20:00Z'
  },
  {
    id: 'sa-003',
    title: 'Vendor Security Assessment - CRM Provider',
    description: 'Security assessment of our CRM SaaS provider',
    type: 'vendor',
    status: 'planned',
    scope: 'CRM provider security controls, data protection measures, and compliance certifications',
    startDate: '2024-04-10',
    endDate: '2024-04-30',
    findings: [],
    assignedTo: 'Vendor Management Team',
    createdAt: '2024-03-10T11:00:00Z',
    updatedAt: '2024-03-10T11:00:00Z'
  }
];

// Sample security findings (separate from assessments for direct API access)
const securityFindings = [
  {
    id: 'sf-001',
    title: 'Outdated Firewall Firmware',
    description: 'The main firewall is running outdated firmware with known vulnerabilities',
    severity: 'high',
    status: 'remediated',
    remediation: 'Update firewall firmware to the latest version',
    assignedTo: 'Network Team',
    dueDate: '2024-02-15',
    createdAt: '2024-01-15T14:30:00Z',
    updatedAt: '2024-02-10T09:45:00Z'
  },
  {
    id: 'sf-002',
    title: 'Weak SSH Configuration',
    description: 'SSH servers are configured to use weak ciphers and authentication methods',
    severity: 'medium',
    status: 'in-remediation',
    remediation: 'Update SSH configuration to use strong ciphers and authentication methods',
    assignedTo: 'System Administration Team',
    dueDate: '2024-02-28',
    createdAt: '2024-01-18T11:20:00Z',
    updatedAt: '2024-02-05T16:30:00Z'
  },
  {
    id: 'sf-003',
    title: 'Excessive S3 Bucket Permissions',
    description: 'Multiple S3 buckets have overly permissive access controls',
    severity: 'high',
    status: 'open',
    remediation: 'Review and restrict S3 bucket permissions following the principle of least privilege',
    assignedTo: 'Cloud Team',
    dueDate: '2024-03-20',
    createdAt: '2024-03-05T10:15:00Z',
    updatedAt: '2024-03-05T10:15:00Z'
  },
  {
    id: 'sf-004',
    title: 'Insecure API Endpoints',
    description: 'Several API endpoints are not using HTTPS',
    severity: 'critical',
    status: 'open',
    remediation: 'Configure all API endpoints to use HTTPS with proper certificates',
    assignedTo: 'API Development Team',
    dueDate: '2024-03-15',
    createdAt: '2024-03-02T09:30:00Z',
    updatedAt: '2024-03-02T09:30:00Z'
  },
  {
    id: 'sf-005',
    title: 'Default Credentials in Test Environment',
    description: 'Test environment is using default credentials for database access',
    severity: 'medium',
    status: 'open',
    remediation: 'Change all default credentials in the test environment',
    assignedTo: 'QA Team',
    dueDate: '2024-03-25',
    createdAt: '2024-03-08T14:45:00Z',
    updatedAt: '2024-03-08T14:45:00Z'
  }
];

// Assessment types for reference
const assessmentTypes = [
  {
    id: 'internal',
    name: 'Internal Assessment',
    description: 'Security assessments conducted by internal security teams'
  },
  {
    id: 'external',
    name: 'External Assessment',
    description: 'Security assessments conducted by external security consultants or firms'
  },
  {
    id: 'vendor',
    name: 'Vendor Assessment',
    description: 'Security assessments of third-party vendors and service providers'
  },
  {
    id: 'compliance',
    name: 'Compliance Assessment',
    description: 'Security assessments focused on regulatory compliance requirements'
  }
];

module.exports = {
  securityAssessments,
  securityFindings,
  assessmentTypes
};
