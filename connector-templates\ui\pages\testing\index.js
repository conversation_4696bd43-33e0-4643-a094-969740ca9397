/**
 * Testing & Validation Page
 * 
 * This page provides tools for testing and validating API connectors.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  CircularProgress, 
  Container, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RuleIcon from '@mui/icons-material/Rule';
import ErrorIcon from '@mui/icons-material/Error';
import DashboardLayout from '../../layouts/DashboardLayout';
import RequestSimulator from '../../components/testing/RequestSimulator';
import ValidationRuleTester from '../../components/testing/ValidationRuleTester';
import ErrorScenarioTester from '../../components/testing/ErrorScenarioTester';

const TestingPage = () => {
  const [activeTab, setActiveTab] = useState('request');
  const [connectors, setConnectors] = useState([]);
  const [selectedConnector, setSelectedConnector] = useState('');
  const [credentials, setCredentials] = useState([]);
  const [selectedCredential, setSelectedCredential] = useState('');
  const [connector, setConnector] = useState(null);
  const [credential, setCredential] = useState(null);
  const [loading, setLoading] = useState(true);
  const [response, setResponse] = useState(null);
  
  useEffect(() => {
    // In a real implementation, this would fetch connectors from the API
    const fetchConnectors = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockConnectors = [
          { id: 'conn1', name: 'GitHub API Connector' },
          { id: 'conn2', name: 'Jira API Connector' },
          { id: 'conn3', name: 'Salesforce API Connector' },
          { id: 'conn4', name: 'Google Analytics API Connector' }
        ];
        
        setConnectors(mockConnectors);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching connectors:', error);
        setLoading(false);
      }
    };
    
    fetchConnectors();
  }, []);
  
  useEffect(() => {
    if (selectedConnector) {
      // In a real implementation, this would fetch connector details from the API
      const fetchConnectorDetails = async () => {
        try {
          setLoading(true);
          
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 800));
          
          // Mock data
          const mockConnector = {
            id: selectedConnector,
            name: connectors.find(c => c.id === selectedConnector)?.name || 'Unknown Connector',
            version: '1.0.0',
            description: 'API connector for testing',
            endpoints: [
              {
                id: 'get_users',
                name: 'Get Users',
                path: '/users',
                method: 'GET',
                description: 'Get a list of users',
                parameters: [
                  {
                    name: 'page',
                    type: 'number',
                    description: 'Page number',
                    required: false,
                    default: 1
                  },
                  {
                    name: 'per_page',
                    type: 'number',
                    description: 'Items per page',
                    required: false,
                    default: 10
                  },
                  {
                    name: 'sort',
                    type: 'enum',
                    description: 'Sort order',
                    required: false,
                    options: [
                      { value: 'asc', label: 'Ascending' },
                      { value: 'desc', label: 'Descending' }
                    ]
                  }
                ]
              },
              {
                id: 'get_user',
                name: 'Get User',
                path: '/users/{id}',
                method: 'GET',
                description: 'Get a user by ID',
                parameters: [
                  {
                    name: 'id',
                    type: 'string',
                    description: 'User ID',
                    required: true
                  }
                ]
              },
              {
                id: 'create_user',
                name: 'Create User',
                path: '/users',
                method: 'POST',
                description: 'Create a new user',
                parameters: [
                  {
                    name: 'name',
                    type: 'string',
                    description: 'User name',
                    required: true
                  },
                  {
                    name: 'email',
                    type: 'string',
                    description: 'User email',
                    required: true
                  },
                  {
                    name: 'role',
                    type: 'enum',
                    description: 'User role',
                    required: false,
                    options: [
                      { value: 'admin', label: 'Administrator' },
                      { value: 'user', label: 'Regular User' },
                      { value: 'guest', label: 'Guest' }
                    ]
                  },
                  {
                    name: 'active',
                    type: 'boolean',
                    description: 'Is user active',
                    required: false,
                    default: true
                  }
                ]
              }
            ]
          };
          
          setConnector(mockConnector);
          
          // Fetch credentials for this connector
          const mockCredentials = [
            { id: 'cred1', name: 'Production Credentials' },
            { id: 'cred2', name: 'Staging Credentials' },
            { id: 'cred3', name: 'Development Credentials' }
          ];
          
          setCredentials(mockCredentials);
          setSelectedCredential('');
          setLoading(false);
        } catch (error) {
          console.error('Error fetching connector details:', error);
          setLoading(false);
        }
      };
      
      fetchConnectorDetails();
    } else {
      setConnector(null);
      setCredentials([]);
      setSelectedCredential('');
    }
  }, [selectedConnector, connectors]);
  
  useEffect(() => {
    if (selectedCredential) {
      // In a real implementation, this would fetch credential details from the API
      const fetchCredentialDetails = async () => {
        try {
          setLoading(true);
          
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Mock data
          const mockCredential = {
            id: selectedCredential,
            name: credentials.find(c => c.id === selectedCredential)?.name || 'Unknown Credential',
            type: 'API_KEY',
            created: new Date().toISOString(),
            lastUsed: new Date().toISOString()
          };
          
          setCredential(mockCredential);
          setLoading(false);
        } catch (error) {
          console.error('Error fetching credential details:', error);
          setLoading(false);
        }
      };
      
      fetchCredentialDetails();
    } else {
      setCredential(null);
    }
  }, [selectedCredential, credentials]);
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const handleConnectorChange = (event) => {
    setSelectedConnector(event.target.value);
    setResponse(null);
  };
  
  const handleCredentialChange = (event) => {
    setSelectedCredential(event.target.value);
    setResponse(null);
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Testing & Validation
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Test and validate your API connectors with various tools.
          </Typography>
        </Box>
        
        <Card variant="outlined" sx={{ mb: 4 }}>
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="connector-select-label">Connector</InputLabel>
                  <Select
                    labelId="connector-select-label"
                    id="connector-select"
                    value={selectedConnector}
                    label="Connector"
                    onChange={handleConnectorChange}
                    disabled={loading}
                  >
                    {connectors.map(connector => (
                      <MenuItem key={connector.id} value={connector.id}>
                        {connector.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth disabled={!selectedConnector || loading}>
                  <InputLabel id="credential-select-label">Credentials</InputLabel>
                  <Select
                    labelId="credential-select-label"
                    id="credential-select"
                    value={selectedCredential}
                    label="Credentials"
                    onChange={handleCredentialChange}
                  >
                    {credentials.map(cred => (
                      <MenuItem key={cred.id} value={cred.id}>
                        {cred.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {connector && credential ? (
              <>
                <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                  <Tabs value={activeTab} onChange={handleTabChange} aria-label="testing tabs">
                    <Tab label="Request Simulator" value="request" icon={<PlayArrowIcon />} iconPosition="start" />
                    <Tab label="Validation Rules" value="validation" icon={<RuleIcon />} iconPosition="start" />
                    <Tab label="Error Scenarios" value="error" icon={<ErrorIcon />} iconPosition="start" />
                  </Tabs>
                </Box>
                
                {activeTab === 'request' && (
                  <RequestSimulator 
                    connector={connector} 
                    credentials={credential} 
                  />
                )}
                
                {activeTab === 'validation' && (
                  <ValidationRuleTester 
                    response={response} 
                  />
                )}
                
                {activeTab === 'error' && (
                  <ErrorScenarioTester 
                    connector={connector} 
                    credentials={credential} 
                  />
                )}
              </>
            ) : (
              <Paper sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="h6" gutterBottom>
                  Select a Connector and Credentials
                </Typography>
                <Typography variant="body1" color="textSecondary">
                  To start testing, please select a connector and credentials from the dropdown menus above.
                </Typography>
              </Paper>
            )}
          </>
        )}
      </Container>
    </DashboardLayout>
  );
};

export default TestingPage;
