"""
Example of using collection templates with the Collector Manager.

This example demonstrates how to create and use collection templates
with the enhanced Collector Manager.
"""

import os
import sys
import json
import datetime
import logging

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.collector_manager import CollectorManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the collection template example."""
    # Create a collector manager
    collector_manager = CollectorManager()
    
    # Create a collection template for GitHub issues
    template_id = "github_issues_template"
    template_name = "GitHub Issues Template"
    template_description = "Template for collecting GitHub issues"
    collector_id = "github"
    parameters = {
        "owner": "Dartan1983",
        "repo": "nova-marketplace",
        "token": "YOUR_GITHUB_TOKEN",  # Replace with your GitHub token
        "include_issues": True,
        "include_prs": False,
        "include_commits": False,
        "include_workflows": False
    }
    
    try:
        # Create the template
        template = collector_manager.create_template(
            template_id=template_id,
            name=template_name,
            description=template_description,
            collector_id=collector_id,
            parameters=parameters
        )
        
        logger.info(f"Created template: {json.dumps(template, indent=2)}")
        
        # Create a collection schedule
        schedule_id = "github_issues_daily"
        schedule_name = "GitHub Issues Daily"
        schedule_description = "Collect GitHub issues daily"
        frequency = collector_manager.SCHEDULE_FREQUENCIES['DAILY']
        
        schedule = collector_manager.create_schedule(
            schedule_id=schedule_id,
            name=schedule_name,
            description=schedule_description,
            frequency=frequency,
            template_id=template_id,
            strategy=collector_manager.COLLECTION_STRATEGIES['INCREMENTAL'],
            enabled=True
        )
        
        logger.info(f"Created schedule: {json.dumps(schedule, indent=2)}")
        
        # Collect evidence using the template
        evidence = collector_manager.collect_with_template(
            template_id=template_id,
            strategy=collector_manager.COLLECTION_STRATEGIES['FULL']
        )
        
        logger.info(f"Collected evidence: {json.dumps(evidence, indent=2)}")
        
        # Update the template
        updated_template = collector_manager.update_template(
            template_id=template_id,
            parameters={
                "owner": "Dartan1983",
                "repo": "nova-marketplace",
                "token": "YOUR_GITHUB_TOKEN",  # Replace with your GitHub token
                "include_issues": True,
                "include_prs": True,  # Now include PRs
                "include_commits": False,
                "include_workflows": False
            }
        )
        
        logger.info(f"Updated template: {json.dumps(updated_template, indent=2)}")
        
        # Update the schedule
        updated_schedule = collector_manager.update_schedule(
            schedule_id=schedule_id,
            frequency=collector_manager.SCHEDULE_FREQUENCIES['WEEKLY'],
            enabled=True
        )
        
        logger.info(f"Updated schedule: {json.dumps(updated_schedule, indent=2)}")
        
        # Get all templates
        templates = collector_manager.get_templates()
        logger.info(f"All templates: {json.dumps(templates, indent=2)}")
        
        # Get all schedules
        schedules = collector_manager.get_schedules()
        logger.info(f"All schedules: {json.dumps(schedules, indent=2)}")
        
        # Delete the schedule
        collector_manager.delete_schedule(schedule_id)
        logger.info(f"Deleted schedule: {schedule_id}")
        
        # Delete the template
        collector_manager.delete_template(template_id)
        logger.info(f"Deleted template: {template_id}")
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
