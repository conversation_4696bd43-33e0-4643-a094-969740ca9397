/**
 * File Upload Routes
 * 
 * This file defines the API routes for file uploads.
 */

const express = require('express');
const router = express.Router();
const fileUploadController = require('../controllers/fileUploadController');
const auth = require('../middleware/auth');

/**
 * @route POST /api/v1/uploads/file
 * @desc Upload a single file
 * @access Private
 */
router.post('/file', auth, fileUploadController.uploadFile);

/**
 * @route POST /api/v1/uploads/files
 * @desc Upload multiple files
 * @access Private
 */
router.post('/files', auth, fileUploadController.uploadFiles);

module.exports = router;
