# Quantum Backend Benchmarking

This guide explains how to benchmark and visualize the performance of different quantum backends with the RoseTTAFold engine in the ConsciousNovaFold system.

## Prerequisites

1. Python 3.8+
2. RoseTTAFold installed and configured
3. Required Python packages (install with `pip install -r requirements-visualization.txt`)

## Quick Start

### 1. Run a Benchmark

```bash
# Basic benchmark with default settings
python examples/run_quantum_benchmark.py --rosettafold-path /path/to/rosettafold

# Custom benchmark with specific parameters
python examples/run_quantum_benchmark.py \
    --rosettafold-path /path/to/rosettafold \
    --sequence ACEDGFIHKMLNQPSRTWV \
    --backends qiskit cirq \
    --shots 1000 \
    --depth 100 \
    --layers 2 \
    --output-dir my_benchmark_results
```

### 2. Visualize Results

Visualizations are generated automatically after the benchmark completes. You can also generate them manually:

```bash
# Visualize existing benchmark results
python scripts/visualize_benchmark.py path/to/benchmark_results

# Specify output directory
python scripts/visualize_benchmark.py path/to/benchmark_results -o path/to/output_dir
```

## Available Quantum Backends

### Qiskit Backend
- Uses IBM Quantum or local simulators
- Supports both CPU and GPU execution
- Configuration options:
  - `use_simulator`: Use local simulator (default: True)
  - `api_token`: IBM Quantum API token (required for real quantum hardware)
  - `backend_name`: Name of the IBM Quantum backend
  - `optimization_level`: Circuit optimization level (0-3)

### Cirq Backend
- Uses Google's Cirq and TensorFlow Quantum
- Supports hybrid quantum-classical computation
- Configuration options:
  - `use_tensorflow`: Use TensorFlow Quantum (default: True)
  - `simulator_type`: Type of simulator ('density_matrix' or 'wavefunction')
  - `noise_model`: Optional noise model configuration

## Benchmark Results

Benchmark results are saved in JSON format with the following structure:

```json
{
  "timestamp": "20230501_143000",
  "results": [
    {
      "status": "COMPLETED",
      "pdb_path": "/path/to/output.pdb",
      "output_dir": "/path/to/output/dir",
      "processing_time_seconds": 123.45,
      "sequence_length": 20,
      "quantum_info": {
        "used_quantum": true,
        "quantum_backend": "qiskit",
        "quantum_circuit_depth": 100,
        "quantum_shots": 1000,
        "quantum_layers": 2
      },
      "benchmark_metrics": {
        "backend": "qiskit",
        "sequence_length": 20,
        "duration": 123.45,
        "timestamp": "2023-05-01T14:30:00.123456",
        "config": { ... }
      }
    }
  ]
}
```

## Visualization Features

The visualization tools generate the following plots:

1. **Performance Comparison**
   - Processing time by backend and sequence length
   - Log-scale y-axis for better comparison of different time scales

2. **Resource Usage**
   - CPU usage (%)
   - Memory usage (MB)
   - GPU memory usage (MB, if available)
   - GPU load (%, if available)

3. **Quantum Metrics**
   - Circuit depth by backend
   - Number of shots by backend
   - Number of quantum layers by backend
   - Quantum backend distribution (pie chart)

## Advanced Usage

### Custom Benchmark Script

For more control over the benchmarking process, you can create a custom script:

```python
from src.rosettafold_engine import RoseTTAFoldEngine
from src.visualization.benchmark_visualizer import visualize_benchmark_results

def run_custom_benchmark():
    config = {
        'mode': 'hybrid',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': 'custom_benchmark',
        'quantum_backend': 'qiskit',
        'quantum_shots': 1000,
        'quantum_circuit_depth': 100,
        'quantum_layers': 2,
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
    
    # Initialize engine
    engine = RoseTTAFoldEngine(config=config)
    
    # Run prediction
    sequence = 'ACDEFGHIKLMNPQRSTVWY'
    result = engine.predict(sequence)
    
    return result

if __name__ == "__main__":
    # Run benchmark
    result = run_custom_benchmark()
    
    # Generate visualizations
    visualize_benchmark_results(
        results_dir='path/to/benchmark/results',
        output_dir='path/to/output/visualizations'
    )
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
   pip install -r requirements-visualization.txt
   ```

2. **RoseTTAFold Not Found**
   - Ensure the `--rosettafold-path` points to the correct directory
   - Verify that RoseTTAFold is properly installed

3. **GPU Not Detected**
   - Install the appropriate GPU drivers
   - Make sure CUDA is properly configured
   - Check that TensorFlow/PyTorch can detect the GPU

4. **Visualization Errors**
   - Install the required visualization dependencies
   - Ensure you have write permissions to the output directory

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
