/**
 * FormFactorManager.js
 * 
 * This module manages the physical form factors for NovaDNA, including
 * QR codes and NFC data for wristbands, stickers, and other physical devices.
 */

const QRCode = require('qrcode');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

/**
 * FormFactorManager class for managing physical form factors
 */
class FormFactorManager {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'https://novadna.novafuse.io';
    this.qrCodeOptions = options.qrCodeOptions || {
      errorCorrectionLevel: 'H',
      margin: 1,
      width: 300
    };
    this.nfcOptions = options.nfcOptions || {
      format: 'NDEF',
      writeLock: false
    };
    this.encryptionEnabled = options.encryptionEnabled !== false;
    this.encryptionKey = options.encryptionKey || crypto.randomBytes(32);
    this.formFactors = new Map();
  }

  /**
   * Generate a QR code for an emergency profile
   * @param {String} profileId - The profile ID
   * @param {Object} options - Options for QR code generation
   * @returns {Promise<Object>} - The generated QR code
   */
  async generateQRCode(profileId, options = {}) {
    if (!profileId) {
      throw new Error('Profile ID is required');
    }
    
    const formFactorId = uuidv4();
    const accessCode = this._generateAccessCode();
    
    // Create the data URL
    const dataUrl = this._createDataUrl(profileId, accessCode, options.accessLevel);
    
    // Generate QR code
    const qrOptions = {
      ...this.qrCodeOptions,
      ...options.qrCodeOptions
    };
    
    const qrCodeDataUrl = await QRCode.toDataURL(dataUrl, qrOptions);
    
    // Store the form factor
    const formFactor = {
      formFactorId,
      profileId,
      type: 'QR_CODE',
      accessCode,
      accessLevel: options.accessLevel || 'standard',
      dataUrl,
      createdAt: new Date().toISOString(),
      metadata: options.metadata || {}
    };
    
    this.formFactors.set(formFactorId, formFactor);
    
    return {
      formFactorId,
      profileId,
      type: 'QR_CODE',
      qrCodeDataUrl,
      dataUrl,
      accessCode,
      createdAt: formFactor.createdAt
    };
  }

  /**
   * Generate NFC data for an emergency profile
   * @param {String} profileId - The profile ID
   * @param {Object} options - Options for NFC data generation
   * @returns {Object} - The generated NFC data
   */
  generateNFCData(profileId, options = {}) {
    if (!profileId) {
      throw new Error('Profile ID is required');
    }
    
    const formFactorId = uuidv4();
    const accessCode = this._generateAccessCode();
    
    // Create the data URL
    const dataUrl = this._createDataUrl(profileId, accessCode, options.accessLevel);
    
    // Generate NFC data
    const nfcOptions = {
      ...this.nfcOptions,
      ...options.nfcOptions
    };
    
    // Create NDEF record
    const ndefRecord = {
      recordType: 'uri',
      data: dataUrl,
      encoding: 'UTF-8',
      id: formFactorId
    };
    
    // Create NDEF message
    const ndefMessage = {
      records: [ndefRecord]
    };
    
    if (options.additionalRecords) {
      ndefMessage.records.push(...options.additionalRecords);
    }
    
    // Store the form factor
    const formFactor = {
      formFactorId,
      profileId,
      type: 'NFC',
      accessCode,
      accessLevel: options.accessLevel || 'standard',
      dataUrl,
      createdAt: new Date().toISOString(),
      metadata: options.metadata || {}
    };
    
    this.formFactors.set(formFactorId, formFactor);
    
    return {
      formFactorId,
      profileId,
      type: 'NFC',
      ndefMessage,
      dataUrl,
      accessCode,
      createdAt: formFactor.createdAt,
      writeLock: nfcOptions.writeLock
    };
  }

  /**
   * Generate data for a vehicle sticker
   * @param {String} profileId - The profile ID
   * @param {Object} vehicleInfo - Information about the vehicle
   * @param {Object} options - Options for sticker generation
   * @returns {Object} - The generated sticker data
   */
  generateVehicleSticker(profileId, vehicleInfo, options = {}) {
    if (!profileId) {
      throw new Error('Profile ID is required');
    }
    
    if (!vehicleInfo || !vehicleInfo.make || !vehicleInfo.model) {
      throw new Error('Vehicle information is required');
    }
    
    const formFactorId = uuidv4();
    const accessCode = this._generateAccessCode();
    
    // Create the data URL with vehicle info
    const dataUrl = this._createDataUrl(profileId, accessCode, options.accessLevel, {
      vehicleInfo
    });
    
    // Generate QR code
    const qrOptions = {
      ...this.qrCodeOptions,
      ...options.qrCodeOptions
    };
    
    // Store the form factor
    const formFactor = {
      formFactorId,
      profileId,
      type: 'VEHICLE_STICKER',
      accessCode,
      accessLevel: options.accessLevel || 'standard',
      dataUrl,
      vehicleInfo,
      createdAt: new Date().toISOString(),
      metadata: options.metadata || {}
    };
    
    this.formFactors.set(formFactorId, formFactor);
    
    return {
      formFactorId,
      profileId,
      type: 'VEHICLE_STICKER',
      dataUrl,
      accessCode,
      vehicleInfo,
      createdAt: formFactor.createdAt
    };
  }

  /**
   * Generate a wristband QR/NFC combo
   * @param {String} profileId - The profile ID
   * @param {Object} options - Options for wristband generation
   * @returns {Promise<Object>} - The generated wristband data
   */
  async generateWristband(profileId, options = {}) {
    if (!profileId) {
      throw new Error('Profile ID is required');
    }
    
    const formFactorId = uuidv4();
    const accessCode = this._generateAccessCode();
    
    // Create the data URL
    const dataUrl = this._createDataUrl(profileId, accessCode, options.accessLevel);
    
    // Generate QR code
    const qrOptions = {
      ...this.qrCodeOptions,
      ...options.qrCodeOptions
    };
    
    const qrCodeDataUrl = await QRCode.toDataURL(dataUrl, qrOptions);
    
    // Generate NFC data
    const nfcOptions = {
      ...this.nfcOptions,
      ...options.nfcOptions
    };
    
    // Create NDEF record
    const ndefRecord = {
      recordType: 'uri',
      data: dataUrl,
      encoding: 'UTF-8',
      id: formFactorId
    };
    
    // Create NDEF message
    const ndefMessage = {
      records: [ndefRecord]
    };
    
    // Store the form factor
    const formFactor = {
      formFactorId,
      profileId,
      type: 'WRISTBAND',
      accessCode,
      accessLevel: options.accessLevel || 'standard',
      dataUrl,
      createdAt: new Date().toISOString(),
      metadata: options.metadata || {}
    };
    
    this.formFactors.set(formFactorId, formFactor);
    
    return {
      formFactorId,
      profileId,
      type: 'WRISTBAND',
      qrCodeDataUrl,
      ndefMessage,
      dataUrl,
      accessCode,
      createdAt: formFactor.createdAt
    };
  }

  /**
   * Verify a form factor access code
   * @param {String} formFactorId - The form factor ID
   * @param {String} accessCode - The access code to verify
   * @returns {Object} - Verification result
   */
  verifyFormFactor(formFactorId, accessCode) {
    const formFactor = this.formFactors.get(formFactorId);
    
    if (!formFactor) {
      return {
        valid: false,
        error: 'Form factor not found'
      };
    }
    
    if (formFactor.accessCode !== accessCode) {
      return {
        valid: false,
        error: 'Invalid access code'
      };
    }
    
    return {
      valid: true,
      profileId: formFactor.profileId,
      type: formFactor.type,
      accessLevel: formFactor.accessLevel,
      createdAt: formFactor.createdAt
    };
  }

  /**
   * Revoke a form factor
   * @param {String} formFactorId - The form factor ID
   * @returns {Boolean} - Whether the form factor was successfully revoked
   */
  revokeFormFactor(formFactorId) {
    return this.formFactors.delete(formFactorId);
  }

  /**
   * Get all form factors for a profile
   * @param {String} profileId - The profile ID
   * @returns {Array} - The form factors
   */
  getProfileFormFactors(profileId) {
    const formFactors = [];
    
    for (const [id, formFactor] of this.formFactors.entries()) {
      if (formFactor.profileId === profileId) {
        formFactors.push({
          formFactorId: id,
          type: formFactor.type,
          accessLevel: formFactor.accessLevel,
          createdAt: formFactor.createdAt,
          metadata: formFactor.metadata
        });
      }
    }
    
    return formFactors;
  }

  /**
   * Generate an access code
   * @returns {String} - The generated access code
   * @private
   */
  _generateAccessCode() {
    // Generate a 6-character alphanumeric code
    const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code = '';
    
    for (let i = 0; i < 6; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      code += characters.charAt(randomIndex);
    }
    
    return code;
  }

  /**
   * Create a data URL for a profile
   * @param {String} profileId - The profile ID
   * @param {String} accessCode - The access code
   * @param {String} accessLevel - The access level
   * @param {Object} additionalData - Additional data to include
   * @returns {String} - The data URL
   * @private
   */
  _createDataUrl(profileId, accessCode, accessLevel = 'standard', additionalData = {}) {
    const data = {
      pid: profileId,
      ac: accessCode,
      al: accessLevel,
      ...additionalData
    };
    
    let dataString = JSON.stringify(data);
    
    // Encrypt the data if enabled
    if (this.encryptionEnabled) {
      dataString = this._encryptData(dataString);
    }
    
    // Create the URL
    const encodedData = Buffer.from(dataString).toString('base64url');
    return `${this.baseUrl}/emergency/${encodedData}`;
  }

  /**
   * Encrypt data
   * @param {String} data - The data to encrypt
   * @returns {String} - The encrypted data
   * @private
   */
  _encryptData(data) {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-gcm', this.encryptionKey, iv);
    
    const encrypted = Buffer.concat([
      cipher.update(data, 'utf8'),
      cipher.final()
    ]);
    
    const authTag = cipher.getAuthTag();
    
    // Format: iv:authTag:encryptedData
    return Buffer.concat([
      iv,
      authTag,
      encrypted
    ]).toString('base64');
  }

  /**
   * Decrypt data
   * @param {String} encryptedData - The data to decrypt
   * @returns {String} - The decrypted data
   * @private
   */
  _decryptData(encryptedData) {
    const buffer = Buffer.from(encryptedData, 'base64');
    
    // Extract components
    const iv = buffer.slice(0, 16);
    const authTag = buffer.slice(16, 32);
    const encrypted = buffer.slice(32);
    
    // Decrypt
    const decipher = crypto.createDecipheriv('aes-256-gcm', this.encryptionKey, iv);
    decipher.setAuthTag(authTag);
    
    return Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]).toString('utf8');
  }
}

module.exports = FormFactorManager;
