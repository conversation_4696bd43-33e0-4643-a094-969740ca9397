{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 1, "numPassedTests": 9, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 1, "numTotalTests": 9, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1745912531106, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 9, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1745912533559, "runtime": 1454, "slow": false, "start": 1745912532105}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\connectors\\connector-model.test.js", "testResults": [{"ancestorTitles": ["Connector Model", "constructor"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model constructor should create a connector with default values", "invocations": 1, "location": null, "numPassingAsserts": 9, "retryReasons": [], "status": "passed", "title": "should create a connector with default values"}, {"ancestorTitles": ["Connector Model", "constructor"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model constructor should create a connector with provided values", "invocations": 1, "location": null, "numPassingAsserts": 9, "retryReasons": [], "status": "passed", "title": "should create a connector with provided values"}, {"ancestorTitles": ["Connector Model", "validate"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model validate should return valid=true when all required fields are present", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return valid=true when all required fields are present"}, {"ancestorTitles": ["Connector Model", "validate"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model validate should return valid=false when name is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return valid=false when name is missing"}, {"ancestorTitles": ["Connector Model", "validate"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model validate should return valid=false when description is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return valid=false when description is missing"}, {"ancestorTitles": ["Connector Model", "validate"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model validate should return valid=false when both name and description are missing", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return valid=false when both name and description are missing"}, {"ancestorTitles": ["Connector Model", "toJSON"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model toJSON should return a JSON representation of the connector", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return a JSON representation of the connector"}, {"ancestorTitles": ["Connector Model", "ConnectorStatus enum"], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model ConnectorStatus enum should have the correct values", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should have the correct values"}, {"ancestorTitles": ["Connector Model", "ConnectorType enum"], "duration": 0, "failureDetails": [], "failureMessages": [], "fullName": "Connector Model ConnectorType enum should have the correct values", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should have the correct values"}], "failureMessage": null}], "wasInterrupted": false}