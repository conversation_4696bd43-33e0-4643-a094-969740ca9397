/**
 * CSDE Engine
 * 
 * This file contains the declaration of the CSDE Engine class, which implements
 * the Cyber-Safety Dominance Equation (CSDE) using CUDA acceleration.
 */

#ifndef CSDE_ENGINE_H
#define CSDE_ENGINE_H

#include <cuda_runtime.h>
#include <vector>
#include <string>
#include <memory>
#include <unordered_map>

/**
 * Remediation Action
 */
struct RemediationAction {
    enum Type {
        ISOLATE,
        BLOCK,
        PATCH,
        ALERT,
        LOG
    };
    
    Type type;
    std::string target;
    std::string priority;
    
    RemediationAction(Type type, const std::string& target, const std::string& priority)
        : type(type), target(target), priority(priority) {}
};

/**
 * CSDE Result
 */
struct CSDEResult {
    float csde_value;
    std::vector<RemediationAction> remediation_actions;
    float processing_time_ms;
    
    CSDEResult() : csde_value(0.0f), processing_time_ms(0.0f) {}
};

/**
 * CSDE Engine
 * 
 * Implements the Cyber-Safety Dominance Equation (CSDE) using CUDA acceleration.
 */
class CSDEEngine {
public:
    /**
     * Constructor
     */
    CSDEEngine();
    
    /**
     * Destructor
     */
    ~CSDEEngine();
    
    /**
     * Initialize the CSDE Engine
     * 
     * @return True if initialization was successful, false otherwise
     */
    bool initialize();
    
    /**
     * Calculate CSDE
     * 
     * @param compliance_data Compliance data (N)
     * @param cloud_data Cloud data (G)
     * @param security_data Security data (C)
     * @return CSDE result
     */
    CSDEResult calculate_csde(
        const std::unordered_map<std::string, float>& compliance_data,
        const std::unordered_map<std::string, float>& cloud_data,
        const std::unordered_map<std::string, float>& security_data);
    
    /**
     * Process security event
     * 
     * @param event Security event
     * @return CSDE result
     */
    CSDEResult process_event(const std::unordered_map<std::string, std::string>& event);
    
    /**
     * Get the last error message
     * 
     * @return Last error message
     */
    std::string get_last_error() const;

private:
    // CUDA resources
    cudaStream_t stream_;
    cudaMemPool_t mem_pool_;
    
    // Device memory
    float* d_compliance_data_;
    float* d_cloud_data_;
    float* d_security_data_;
    float* d_tensor_ng_;
    float* d_fused_tensor_;
    float* d_csde_values_;
    float* d_gradients_;
    float* d_optimized_values_;
    int* d_remediation_counts_;
    int* d_remediation_types_;
    
    // Dimensions
    int compliance_dims_;
    int cloud_dims_;
    int security_dims_;
    int tensor_ng_dims_;
    int fused_tensor_dims_;
    
    // Error handling
    std::string last_error_;
    
    /**
     * Set error message
     * 
     * @param error Error message
     */
    void set_error(const std::string& error);
    
    /**
     * Check CUDA error
     * 
     * @param error CUDA error code
     * @param message Error message
     * @return True if no error, false otherwise
     */
    bool check_cuda_error(cudaError_t error, const std::string& message);
    
    /**
     * Allocate device memory
     * 
     * @param size Size in bytes
     * @param ptr Pointer to device memory
     * @return True if allocation was successful, false otherwise
     */
    bool allocate_device_memory(size_t size, void** ptr);
    
    /**
     * Free device memory
     * 
     * @param ptr Pointer to device memory
     */
    void free_device_memory(void* ptr);
    
    /**
     * Convert remediation type to string
     * 
     * @param type Remediation type
     * @return String representation of remediation type
     */
    std::string remediation_type_to_string(RemediationAction::Type type) const;
};

#endif // CSDE_ENGINE_H
