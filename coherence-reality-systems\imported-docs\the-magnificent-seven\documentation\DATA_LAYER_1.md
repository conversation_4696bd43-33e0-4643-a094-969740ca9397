# NovaFuse Universal API Connector - Data Layer

This document provides an overview of the data layer for the NovaFuse Universal API Connector (UAC).

## Overview

The data layer is responsible for storing and retrieving data from the database. It uses MongoDB as the database and Mongoose as the ODM (Object Document Mapper).

## Components

### Database Connection

The database connection manager (`src/data/db-connection.js`) handles connecting to and disconnecting from MongoDB. It provides the following features:

- Connection pooling
- Automatic reconnection
- Connection event handling
- Connection status tracking

### Models

The data layer includes the following Mongoose models:

#### Connector Model (`src/data/models/connector.js`)

Stores connector templates with the following key information:

- Metadata (name, version, category, description, etc.)
- Authentication configuration
- Base configuration (base URL, headers, etc.)
- Endpoints
- Mappings
- Events

#### Credential Model (`src/data/models/credential.js`)

Stores API credentials with the following key information:

- Connector ID
- Encrypted credentials
- Owner ID
- Test results
- Usage information

The credential model includes encryption for sensitive fields using AES-256-CBC.

#### API Usage Model (`src/data/models/api-usage.js`)

Tracks API usage with the following key information:

- Partner ID
- Connector ID
- Endpoint ID
- Credential ID
- Request details (status, duration, size, etc.)
- Billing information

The API usage model includes TTL (Time-To-Live) indexing for automatic data expiration.

#### Partner Model (`src/data/models/partner.js`)

Stores partner information with the following key information:

- Contact information
- API credentials
- Subscription tier
- Usage limits
- Billing information
- Customization settings

### Repositories

The data layer includes the following repositories:

#### Connector Repository (`src/data/repositories/connector-repository.js`)

Provides methods for accessing and manipulating connector templates:

- Get connector by ID
- Get connectors by category
- Search connectors
- Create, update, and delete connectors
- Import connectors from JSON files

#### Credential Repository (`src/data/repositories/credential-repository.js`)

Provides methods for accessing and manipulating API credentials:

- Get credential by ID
- Get credentials by connector
- Get credentials by owner
- Create, update, and delete credentials
- Get decrypted credentials
- Update test results and usage information

#### API Usage Repository (`src/data/repositories/api-usage-repository.js`)

Provides methods for tracking and analyzing API usage:

- Record API usage
- Get usage statistics
- Get usage by partner, connector, endpoint, or credential
- Get billable usage
- Get usage summary

#### Partner Repository (`src/data/repositories/partner-repository.js`)

Provides methods for accessing and manipulating partner information:

- Get partner by ID or API key
- Validate API credentials
- Create, update, and delete partners
- Regenerate API credentials
- Check and update partner limits

## Data Flow

1. The UAC receives a request to execute a connector endpoint
2. The connector is retrieved from the connector repository
3. The credentials are retrieved from the credential repository
4. The request is authenticated using the credentials
5. The request is executed
6. The API usage is recorded in the API usage repository
7. The response is returned to the client

## Security

The data layer includes the following security features:

- Encryption of sensitive credential fields
- Secure credential storage and retrieval
- API key and secret management
- Usage tracking and rate limiting

## Performance

The data layer includes the following performance optimizations:

- Indexing for frequently queried fields
- Connection pooling
- TTL indexing for automatic data expiration
- Efficient query patterns

## Scalability

The data layer is designed to scale horizontally:

- Stateless repositories
- MongoDB sharding support
- Efficient indexing
- Minimal dependencies between components

## Error Handling

The data layer includes comprehensive error handling:

- Detailed error logging
- Graceful error recovery
- Consistent error patterns
- Validation before database operations

## Testing

The data layer includes comprehensive tests:

- Unit tests for models and repositories
- Integration tests for database operations
- Mock testing for external dependencies
- Coverage targets of 80% or higher

## Environment Variables

The data layer uses the following environment variables:

- `MONGODB_URI` - MongoDB connection URI
- `CREDENTIALS_ENCRYPTION_KEY` - Key for encrypting sensitive credential data
- `API_USAGE_TTL_DAYS` - Number of days to keep API usage data

## Future Enhancements

Planned enhancements for the data layer include:

- Caching layer for frequently accessed data
- Read replicas for improved read performance
- Data archiving for long-term storage
- Enhanced analytics capabilities
