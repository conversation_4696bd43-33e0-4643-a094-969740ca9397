{"numFailedTestSuites": 25, "numFailedTests": 17, "numPassedTestSuites": 3, "numPassedTests": 19, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 22, "numTodoTests": 0, "numTotalTestSuites": 28, "numTotalTests": 36, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1752313423735, "success": false, "testResults": [{"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/certifications-accreditation.integration.test.js'\n\n    \u001b[0m \u001b[90m  7 |\u001b[39m\n     \u001b[90m  8 |\u001b[39m \u001b[90m// Mock logger\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m jest\u001b[33m.\u001b[39mmock(\u001b[32m'../../../../utils/logger'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m ({\n     \u001b[90m    |\u001b[39m      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 10 |\u001b[39m   createLogger\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn(() \u001b[33m=>\u001b[39m ({\n     \u001b[90m 11 |\u001b[39m     info\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\n     \u001b[90m 12 |\u001b[39m     error\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.mock (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/connector/implementations/certifications-accreditation.integration.test.js\u001b[39m\u001b[0m\u001b[2m:9:6)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/certifications-accreditation.integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\connector\\implementations\\certifications-accreditation.integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31m<PERSON><PERSON> encountered an unexpected token\u001b[39m\u001b[22m\n\n    <PERSON><PERSON> failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when <PERSON><PERSON> is not configured to support such syntax.\n\n    Out of the box Je<PERSON> supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    D:\\novafuse-api-superstore\\node_modules\\chai\\chai.js:4101\n    export {\n    ^^^^^^\n\n    SyntaxError: Unexpected token 'export'\n\n    \u001b[0m \u001b[90m 4 |\u001b[39m\n     \u001b[90m 5 |\u001b[39m \u001b[36mconst\u001b[39m request \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m \u001b[36mconst\u001b[39m { expect } \u001b[33m=\u001b[39m require(\u001b[32m'chai'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 7 |\u001b[39m \u001b[36mconst\u001b[39m sinon \u001b[33m=\u001b[39m require(\u001b[32m'sinon'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 8 |\u001b[39m \u001b[36mconst\u001b[39m app \u001b[33m=\u001b[39m require(\u001b[32m'../../../api/app'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 9 |\u001b[39m \u001b[36mconst\u001b[39m badgeSystem \u001b[33m=\u001b[39m require(\u001b[32m'../../../api/services/badgeSystem'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Runtime.createScriptFromCode (\u001b[22mnode_modules/jest-runtime/build/index.js\u001b[2m:1505:14)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnovacore/tests/integration/compliance/complianceRoutes.test.js\u001b[39m\u001b[0m\u001b[2m:6:20)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Unexpected token 'export'", "testFilePath": "D:\\novafuse-api-superstore\\novacore\\tests\\integration\\compliance\\complianceRoutes.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/business-intelligence-workflow.integration.test.js'\n\n    \u001b[0m \u001b[90m  7 |\u001b[39m\n     \u001b[90m  8 |\u001b[39m \u001b[90m// Mock logger\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m jest\u001b[33m.\u001b[39mmock(\u001b[32m'../../../../utils/logger'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m ({\n     \u001b[90m    |\u001b[39m      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 10 |\u001b[39m   createLogger\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn(() \u001b[33m=>\u001b[39m ({\n     \u001b[90m 11 |\u001b[39m     info\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\n     \u001b[90m 12 |\u001b[39m     error\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.mock (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/connector/implementations/business-intelligence-workflow.integration.test.js\u001b[39m\u001b[0m\u001b[2m:9:6)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/business-intelligence-workflow.integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\connector\\implementations\\business-intelligence-workflow.integration.test.js", "testResults": []}, {"leaks": false, "numFailingTests": 8, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1752313432334, "runtime": 1228, "slow": false, "start": 1752313431106}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\enterprise-integration.test.js", "testResults": [{"ancestorTitles": ["Enterprise Integration Tests", "Active Directory Integration"], "duration": 271, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/auth/ad/login", "data": "{\"username\":\"<EMAIL>\",\"password\":\"test-password\"}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests Active Directory Integration should authenticate and authorize based on AD groups", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should authenticate and authorize based on AD groups"}, {"ancestorTitles": ["Enterprise Integration Tests", "Active Directory Integration"], "duration": 17, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/auth/ad/login", "data": "{\"username\":\"<EMAIL>\",\"password\":\"test-password\"}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests Active Directory Integration should handle role-based access control correctly", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle role-based access control correctly"}, {"ancestorTitles": ["Enterprise Integration Tests", "SIEM Integration"], "duration": 23, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/integrations/siem/send-event", "data": "{\"id\":\"compliance-event-789\",\"type\":\"compliance\",\"framework\":\"PCI-DSS\",\"control\":\"1.2.1\",\"status\":\"failed\",\"resource\":{\"id\":\"fw-123\",\"type\":\"firewall\",\"name\":\"payment-gateway-fw\"},\"timestamp\":\"2025-07-12T09:43:52.186Z\",\"details\":{\"finding\":\"Firewall rules do not restrict inbound traffic to only necessary protocols\",\"severity\":\"high\",\"remediation\":\"Update firewall rules to restrict traffic to required protocols only\"}}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests SIEM Integration should send compliance events to SIEM", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should send compliance events to SIEM"}, {"ancestorTitles": ["Enterprise Integration Tests", "SIEM Integration"], "duration": 23, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/integrations/siem/process-event", "data": "{\"id\":\"siem-event-123\",\"type\":\"security\",\"source\":\"firewall\",\"severity\":\"high\",\"timestamp\":\"2025-07-12T09:43:51.886Z\",\"details\":{\"sourceIp\":\"*************\",\"destinationIp\":\"********\",\"port\":22,\"protocol\":\"SSH\",\"action\":\"BLOCK\",\"reason\":\"Unauthorized access attempt\"}}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests SIEM Integration should receive and process security events from SIEM", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should receive and process security events from SIEM"}, {"ancestorTitles": ["Enterprise Integration Tests", "SIEM Integration"], "duration": 16, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/integrations/siem/process-event", "data": "{\"id\":\"siem-event-123\",\"type\":\"security\",\"source\":\"firewall\",\"severity\":\"high\",\"timestamp\":\"2025-07-12T09:43:51.886Z\",\"details\":{\"sourceIp\":\"*************\",\"destinationIp\":\"********\",\"port\":22,\"protocol\":\"SSH\",\"action\":\"BLOCK\",\"reason\":\"Unauthorized access attempt\"}}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests SIEM Integration should handle bidirectional SIEM integration efficiently", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle bidirectional SIEM integration efficiently"}, {"ancestorTitles": ["Enterprise Integration Tests", "Change Management Integration"], "duration": 16, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/integrations/change-management/create-request", "data": "{\"id\":\"cr-456\",\"type\":\"remediation\",\"title\":\"Update firewall rules for PCI compliance\",\"description\":\"Add required firewall rules to meet PCI-DSS requirement 1.2.1\",\"priority\":\"high\",\"requester\":\"compliance-system\",\"affectedSystems\":[\"payment-gateway\",\"card-processing-api\"],\"changes\":[{\"resourceType\":\"firewall\",\"resourceId\":\"fw-123\",\"action\":\"add-rule\",\"parameters\":{\"protocol\":\"tcp\",\"ports\":[443],\"sourceRanges\":[\"10.0.0.0/24\"],\"targetTags\":[\"payment-processing\"]}}]}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests Change Management Integration should create change requests for manual approval", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should create change requests for manual approval"}, {"ancestorTitles": ["Enterprise Integration Tests", "Change Management Integration"], "duration": 18, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/integrations/change-management/create-request", "data": "{\"id\":\"cr-456\",\"type\":\"remediation\",\"title\":\"Update firewall rules for PCI compliance\",\"description\":\"Add required firewall rules to meet PCI-DSS requirement 1.2.1\",\"priority\":\"high\",\"requester\":\"compliance-system\",\"affectedSystems\":[\"payment-gateway\",\"card-processing-api\"],\"changes\":[{\"resourceType\":\"firewall\",\"resourceId\":\"fw-123\",\"action\":\"add-rule\",\"parameters\":{\"protocol\":\"tcp\",\"ports\":[443],\"sourceRanges\":[\"10.0.0.0/24\"],\"targetTags\":[\"payment-processing\"]}}]}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests Change Management Integration should track change request approval workflow", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should track change request approval workflow"}, {"ancestorTitles": ["Enterprise Integration Tests", "Change Management Integration"], "duration": 17, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json"}, "method": "post", "url": "http://localhost:3001/api/integrations/change-management/create-request", "data": "{\"id\":\"cr-456\",\"type\":\"remediation\",\"title\":\"Update firewall rules for PCI compliance\",\"description\":\"Add required firewall rules to meet PCI-DSS requirement 1.2.1\",\"priority\":\"high\",\"requester\":\"compliance-system\",\"affectedSystems\":[\"payment-gateway\",\"card-processing-api\"],\"changes\":[{\"resourceType\":\"firewall\",\"resourceId\":\"fw-123\",\"action\":\"add-rule\",\"parameters\":{\"protocol\":\"tcp\",\"ports\":[443],\"sourceRanges\":[\"10.0.0.0/24\"],\"targetTags\":[\"payment-processing\"]}}]}", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Enterprise Integration Tests Change Management Integration should execute approved changes and update status", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should execute approved changes and update status"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › Active Directory Integration › should authenticate and authorize based on AD groups\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › Active Directory Integration › should handle role-based access control correctly\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › SIEM Integration › should send compliance events to SIEM\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › SIEM Integration › should receive and process security events from SIEM\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › SIEM Integration › should handle bidirectional SIEM integration efficiently\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › Change Management Integration › should create change requests for manual approval\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › Change Management Integration › should track change request approval workflow\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mEnterprise Integration Tests › Change Management Integration › should execute approved changes and update status\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n"}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module 'google-auth-library' from 'nova-connect/src/connectors/gcp/chronicle-connector.js'\n\n    Require stack:\n      nova-connect/src/connectors/gcp/chronicle-connector.js\n      nova-connect/tests/integration/gcp/chronicle-live-integration.test.js\n\n    \u001b[0m \u001b[90m 10 |\u001b[39m\n     \u001b[90m 11 |\u001b[39m \u001b[36mconst\u001b[39m axios \u001b[33m=\u001b[39m require(\u001b[32m'axios'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mGoogleAuth\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'google-auth-library'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[36mconst\u001b[39m \u001b[33mTransformationEngine\u001b[39m \u001b[33m=\u001b[39m require(\u001b[32m'../../engines/transformation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m\n     \u001b[90m 15 |\u001b[39m \u001b[36mclass\u001b[39m \u001b[33mChronicleConnector\u001b[39m {\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-connect/src/connectors/gcp/chronicle-connector.js\u001b[2m:12:24)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/gcp/chronicle-live-integration.test.js\u001b[39m\u001b[0m\u001b[2m:12:32)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module 'google-auth-library' from 'nova-connect/src/connectors/gcp/chronicle-connector.js'\n\nRequire stack:\n  nova-connect/src/connectors/gcp/chronicle-connector.js\n  nova-connect/tests/integration/gcp/chronicle-live-integration.test.js\n", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\gcp\\chronicle-live-integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../src/connectors/gcp/chronicle-connector' from 'tests/integration/gcp/chronicle-live-integration.test.js'\n\n    \u001b[0m \u001b[90m 10 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m 11 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mChronicleConnector\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/connectors/gcp/chronicle-connector'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mRemediationEngine\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/engines/remediation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m { performance } \u001b[33m=\u001b[39m require(\u001b[32m'perf_hooks'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 15 |\u001b[39m \u001b[36mconst\u001b[39m fs \u001b[33m=\u001b[39m require(\u001b[32m'fs'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mtests/integration/gcp/chronicle-live-integration.test.js\u001b[39m\u001b[0m\u001b[2m:12:32)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../src/connectors/gcp/chronicle-connector' from 'tests/integration/gcp/chronicle-live-integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\gcp\\chronicle-live-integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../src/novatrack' from 'tests/integration/novatrack/api.test.js'\n\n    \u001b[0m \u001b[90m 12 |\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[90m// Import the TrackingManager\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mTrackingManager\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/novatrack'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                             \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 15 |\u001b[39m\n     \u001b[90m 16 |\u001b[39m describe(\u001b[32m'NovaTrack API'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n     \u001b[90m 17 |\u001b[39m   \u001b[36mlet\u001b[39m app\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mtests/integration/novatrack/api.test.js\u001b[39m\u001b[0m\u001b[2m:14:29)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../src/novatrack' from 'tests/integration/novatrack/api.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novatrack\\api.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m 10 |\u001b[39m \u001b[36mconst\u001b[39m cors \u001b[33m=\u001b[39m require(\u001b[32m'cors'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 11 |\u001b[39m \u001b[36mconst\u001b[39m morgan \u001b[33m=\u001b[39m require(\u001b[32m'morgan'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[36mconst\u001b[39m winston \u001b[33m=\u001b[39m require(\u001b[32m'winston'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m rateLimit \u001b[33m=\u001b[39m require(\u001b[32m'express-rate-limit'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 15 |\u001b[39m \u001b[36mconst\u001b[39m helmet \u001b[33m=\u001b[39m require(\u001b[32m'helmet'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/encoding.js\u001b[2m:2:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/url-state-machine.js\u001b[2m:5:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL-impl.js\u001b[2m:2:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL.js\u001b[2m:442:14)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/webidl2js-wrapper.js\u001b[2m:3:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/index.js\u001b[2m:3:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb-connection-string-url/src/index.ts\u001b[2m:1:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/connection_string.ts\u001b[2m:3:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/mongo_client.ts\u001b[2m:17:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/change_stream.ts\u001b[2m:16:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/index.ts\u001b[2m:4:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/collection.js\u001b[2m:9:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/index.js\u001b[2m:7:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/index.js\u001b[2m:7:25)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/index.js\u001b[2m:8:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mserver.js\u001b[2m:12:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mtests/integration/novatrack/api.comprehensive.test.js\u001b[39m\u001b[0m\u001b[2m:14:13)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novatrack\\api.comprehensive.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/apis-ipaas-developer-tools.integration.test.js'\n\n    \u001b[0m \u001b[90m  7 |\u001b[39m\n     \u001b[90m  8 |\u001b[39m \u001b[90m// Mock logger\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m jest\u001b[33m.\u001b[39mmock(\u001b[32m'../../../../utils/logger'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m ({\n     \u001b[90m    |\u001b[39m      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 10 |\u001b[39m   createLogger\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn(() \u001b[33m=>\u001b[39m ({\n     \u001b[90m 11 |\u001b[39m     info\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\n     \u001b[90m 12 |\u001b[39m     error\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.mock (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/connector/implementations/apis-ipaas-developer-tools.integration.test.js\u001b[39m\u001b[0m\u001b[2m:9:6)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/apis-ipaas-developer-tools.integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\connector\\implementations\\apis-ipaas-developer-tools.integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m  6 |\u001b[39m\n     \u001b[90m  7 |\u001b[39m \u001b[36mconst\u001b[39m request \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m  9 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mMongoMemoryServer\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'mongodb-memory-server'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 10 |\u001b[39m \u001b[36mconst\u001b[39m app \u001b[33m=\u001b[39m require(\u001b[32m'../../app'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 11 |\u001b[39m \u001b[36mconst\u001b[39m \u001b[33mUser\u001b[39m \u001b[33m=\u001b[39m require(\u001b[32m'../../api/models/User'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/encoding.js\u001b[2m:2:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/url-state-machine.js\u001b[2m:5:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL-impl.js\u001b[2m:2:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL.js\u001b[2m:442:14)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/webidl2js-wrapper.js\u001b[2m:3:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/index.js\u001b[2m:3:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb-connection-string-url/src/index.ts\u001b[2m:1:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/connection_string.ts\u001b[2m:3:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/mongo_client.ts\u001b[2m:17:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/change_stream.ts\u001b[2m:16:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/index.ts\u001b[2m:4:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/collection.js\u001b[2m:9:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/index.js\u001b[2m:7:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/index.js\u001b[2m:7:25)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/index.js\u001b[2m:8:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/rbac-api.test.js\u001b[39m\u001b[0m\u001b[2m:8:18)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\rbac-api.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '@google-cloud/bigquery' from 'nova-connect/tests/integration/gcp/bigquery-live-integration.test.js'\n\n    \u001b[0m \u001b[90m 10 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m 11 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mBigQuery\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'@google-cloud/bigquery'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mRemediationEngine\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/engines/remediation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mEncryptionService\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/security/encryption-service'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 15 |\u001b[39m \u001b[36mconst\u001b[39m { performance } \u001b[33m=\u001b[39m require(\u001b[32m'perf_hooks'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/gcp/bigquery-live-integration.test.js\u001b[39m\u001b[0m\u001b[2m:12:22)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '@google-cloud/bigquery' from 'nova-connect/tests/integration/gcp/bigquery-live-integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\gcp\\bigquery-live-integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/contracts-policy-lifecycle.integration.test.js'\n\n    \u001b[0m \u001b[90m  7 |\u001b[39m\n     \u001b[90m  8 |\u001b[39m \u001b[90m// Mock logger\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m jest\u001b[33m.\u001b[39mmock(\u001b[32m'../../../../utils/logger'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m ({\n     \u001b[90m    |\u001b[39m      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 10 |\u001b[39m   createLogger\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn(() \u001b[33m=>\u001b[39m ({\n     \u001b[90m 11 |\u001b[39m     info\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\n     \u001b[90m 12 |\u001b[39m     error\u001b[33m:\u001b[39m jest\u001b[33m.\u001b[39mfn()\u001b[33m,\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.mock (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/connector/implementations/contracts-policy-lifecycle.integration.test.js\u001b[39m\u001b[0m\u001b[2m:9:6)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../../utils/logger' from 'nova-connect/tests/integration/connector/implementations/contracts-policy-lifecycle.integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\connector\\implementations\\contracts-policy-lifecycle.integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '@google-cloud/bigquery' from 'tests/integration/gcp/bigquery-live-integration.test.js'\n\n    \u001b[0m \u001b[90m 10 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m 11 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mBigQuery\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'@google-cloud/bigquery'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                      \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mRemediationEngine\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/engines/remediation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mEncryptionService\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/security/encryption-service'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 15 |\u001b[39m \u001b[36mconst\u001b[39m { performance } \u001b[33m=\u001b[39m require(\u001b[32m'perf_hooks'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mtests/integration/gcp/bigquery-live-integration.test.js\u001b[39m\u001b[0m\u001b[2m:12:22)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '@google-cloud/bigquery' from 'tests/integration/gcp/bigquery-live-integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\gcp\\bigquery-live-integration.test.js", "testResults": []}, {"leaks": false, "numFailingTests": 7, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1752313436576, "runtime": 781, "slow": false, "start": 1752313435795}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\gcp-integration.test.js", "testResults": [{"ancestorTitles": ["Google Cloud Integration Tests", "Security Command Center Integration"], "duration": 0, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests Security Command Center Integration should retrieve findings from SCC", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should retrieve findings from SCC"}, {"ancestorTitles": ["Google Cloud Integration Tests", "Security Command Center Integration"], "duration": 1, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests Security Command Center Integration should normalize SCC findings to NovaConnect format", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should normalize SCC findings to NovaConnect format"}, {"ancestorTitles": ["Google Cloud Integration Tests", "Cloud IAM Integration"], "duration": 0, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests Cloud IAM Integration should retrieve IAM roles", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should retrieve IAM roles"}, {"ancestorTitles": ["Google Cloud Integration Tests", "Cloud IAM Integration"], "duration": 1, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests Cloud IAM Integration should retrieve IAM policy", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should retrieve IAM policy"}, {"ancestorTitles": ["Google Cloud Integration Tests", "BigQuery Integration"], "duration": 0, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests BigQuery Integration should execute a BigQuery query", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should execute a BigQuery query"}, {"ancestorTitles": ["Google Cloud Integration Tests", "Cloud Storage Integration"], "duration": 1, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests Cloud Storage Integration should store evidence in Cloud Storage", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should store evidence in Cloud Storage"}, {"ancestorTitles": ["Google Cloud Integration Tests", "Multi-Service Orchestration"], "duration": 1, "failureDetails": [{"message": "Network Error", "name": "AxiosError", "code": "ERR_NETWORK", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*"}, "method": "get", "url": "http://localhost:3001/api/credentials/gcp-test-credential", "allowAbsoluteUrls": true}, "request": {}}], "failureMessages": ["AxiosError: Network Error\n    at XMLHttpRequest.handleError (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\adapters\\xhr.js:110:14)\n    at XMLHttpRequest.invokeTheCallbackFunction (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\generated\\EventHandlerNonNull.js:14:28)\n    at XMLHttpRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\create-event-accessor.js:35:32)\n    at innerInvokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:350:25)\n    at invokeEventListeners (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:286:3)\n    at XMLHttpRequestImpl._dispatch (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\events\\EventTarget-impl.js:233:9)\n    at fireAnEvent (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\events.js:18:36)\n    at requestErrorSteps (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:131:3)\n    at Object.dispatchError (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\xhr-utils.js:60:3)\n    at Request.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\xhr\\XMLHttpRequest-impl.js:655:18)\n    at Request.emit (node:events:529:35)\n    at ClientRequest.<anonymous> (D:\\novafuse-api-superstore\\node_modules\\jsdom\\lib\\jsdom\\living\\helpers\\http-request.js:121:14)\n    at ClientRequest.emit (node:events:517:28)\n    at Socket.socketErrorListener (node:_http_client:501:9)\n    at Socket.emit (node:events:517:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (D:\\novafuse-api-superstore\\node_modules\\axios\\lib\\core\\Axios.js:45:41)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)"], "fullName": "Google Cloud Integration Tests Multi-Service Orchestration should orchestrate a compliance workflow across multiple GCP services", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should orchestrate a compliance workflow across multiple GCP services"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › Security Command Center Integration › should retrieve findings from SCC\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › Security Command Center Integration › should normalize SCC findings to NovaConnect format\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › Cloud IAM Integration › should retrieve IAM roles\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › Cloud IAM Integration › should retrieve IAM policy\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › BigQuery Integration › should execute a BigQuery query\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › Cloud Storage Integration › should store evidence in Cloud Storage\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mGoogle Cloud Integration Tests › Multi-Service Orchestration › should orchestrate a compliance workflow across multiple GCP services\u001b[39m\u001b[22m\n\n    AxiosError: Network Error\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.handleError (\u001b[22m\u001b[2mnode_modules/axios/lib/adapters/xhr.js\u001b[2m:110:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.invokeTheCallbackFunction (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/generated/EventHandlerNonNull.js\u001b[2m:14:28)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/create-event-accessor.js\u001b[2m:35:32)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat innerInvokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:350:25)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat invokeEventListeners (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:286:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat XMLHttpRequestImpl._dispatch (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/events/EventTarget-impl.js\u001b[2m:233:9)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat fireAnEvent (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/events.js\u001b[2m:18:36)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat requestErrorSteps (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:131:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.dispatchError (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/xhr-utils.js\u001b[2m:60:3)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Request.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/xhr/XMLHttpRequest-impl.js\u001b[2m:655:18)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat ClientRequest.<anonymous> (\u001b[22m\u001b[2mnode_modules/jsdom/lib/jsdom/living/helpers/http-request.js\u001b[2m:121:14)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Axios.request (\u001b[22m\u001b[2mnode_modules/axios/lib/core/Axios.js\u001b[2m:45:41)\u001b[22m\u001b[2m\u001b[22m\n"}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31m<PERSON><PERSON> encountered an unexpected token\u001b[39m\u001b[22m\n\n    <PERSON><PERSON> failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when <PERSON><PERSON> is not configured to support such syntax.\n\n    Out of the box Je<PERSON> supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    D:\\novafuse-api-superstore\\node_modules\\chai\\chai.js:4101\n    export {\n    ^^^^^^\n\n    SyntaxError: Unexpected token 'export'\n\n    \u001b[0m \u001b[90m 4 |\u001b[39m\n     \u001b[90m 5 |\u001b[39m \u001b[36mconst\u001b[39m request \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m \u001b[36mconst\u001b[39m { expect } \u001b[33m=\u001b[39m require(\u001b[32m'chai'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 7 |\u001b[39m \u001b[36mconst\u001b[39m sinon \u001b[33m=\u001b[39m require(\u001b[32m'sinon'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 8 |\u001b[39m \u001b[36mconst\u001b[39m app \u001b[33m=\u001b[39m require(\u001b[32m'../../../api/app'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 9 |\u001b[39m \u001b[36mconst\u001b[39m blockchainEvidence \u001b[33m=\u001b[39m require(\u001b[32m'../../../api/services/blockchainEvidence'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Runtime.createScriptFromCode (\u001b[22mnode_modules/jest-runtime/build/index.js\u001b[2m:1505:14)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnovacore/tests/integration/security/securityRoutes.test.js\u001b[39m\u001b[0m\u001b[2m:6:20)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Unexpected token 'export'", "testFilePath": "D:\\novafuse-api-superstore\\novacore\\tests\\integration\\security\\securityRoutes.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m  6 |\u001b[39m\n     \u001b[90m  7 |\u001b[39m \u001b[36mconst\u001b[39m request \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m  9 |\u001b[39m \u001b[36mconst\u001b[39m app \u001b[33m=\u001b[39m require(\u001b[32m'../../../app'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 10 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mTestExecution\u001b[39m\u001b[33m,\u001b[39m \u001b[33mTestPlan\u001b[39m\u001b[33m,\u001b[39m \u001b[33mControl\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../models'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 11 |\u001b[39m \u001b[36mconst\u001b[39m jwt \u001b[33m=\u001b[39m require(\u001b[32m'jsonwebtoken'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/encoding.js\u001b[2m:2:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/url-state-machine.js\u001b[2m:5:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL-impl.js\u001b[2m:2:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL.js\u001b[2m:442:14)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/webidl2js-wrapper.js\u001b[2m:3:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/index.js\u001b[2m:3:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb-connection-string-url/src/index.ts\u001b[2m:1:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/connection_string.ts\u001b[2m:3:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/mongo_client.ts\u001b[2m:17:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/change_stream.ts\u001b[2m:16:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/index.ts\u001b[2m:4:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/collection.js\u001b[2m:9:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/index.js\u001b[2m:7:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/index.js\u001b[2m:7:25)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/index.js\u001b[2m:8:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mapi/novaassure/tests/integration/testExecution.test.js\u001b[39m\u001b[0m\u001b[2m:8:18)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\api\\novaassure\\tests\\integration\\testExecution.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m 4 |\u001b[39m\n     \u001b[90m 5 |\u001b[39m \u001b[36mconst\u001b[39m request \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 7 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mMongoMemoryServer\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'mongodb-memory-server'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 8 |\u001b[39m \u001b[36mconst\u001b[39m express \u001b[33m=\u001b[39m require(\u001b[32m'express'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 9 |\u001b[39m \u001b[36mconst\u001b[39m bodyParser \u001b[33m=\u001b[39m require(\u001b[32m'body-parser'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/encoding.js\u001b[2m:2:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/url-state-machine.js\u001b[2m:5:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL-impl.js\u001b[2m:2:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL.js\u001b[2m:442:14)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/webidl2js-wrapper.js\u001b[2m:3:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/index.js\u001b[2m:3:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb-connection-string-url/src/index.ts\u001b[2m:1:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/connection_string.ts\u001b[2m:3:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/mongo_client.ts\u001b[2m:17:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/change_stream.ts\u001b[2m:16:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/index.ts\u001b[2m:4:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/collection.js\u001b[2m:9:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/index.js\u001b[2m:7:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/index.js\u001b[2m:7:25)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/index.js\u001b[2m:8:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mapi/novaassure/tests/integration/controlApi.test.js\u001b[39m\u001b[0m\u001b[2m:6:18)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\api\\novaassure\\tests\\integration\\controlApi.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '@google-cloud/security-center' from 'nova-connect/src/connectors/gcp/scc-connector.js'\n\n    Require stack:\n      nova-connect/src/connectors/gcp/scc-connector.js\n      nova-connect/tests/integration/gcp/scc-live-integration.test.js\n\n    \u001b[0m \u001b[90m  6 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m  7 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  8 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mSecurityCenterClient\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'@google-cloud/security-center'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m  9 |\u001b[39m \u001b[36mconst\u001b[39m \u001b[33mTransformationEngine\u001b[39m \u001b[33m=\u001b[39m require(\u001b[32m'../../engines/transformation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 10 |\u001b[39m\n     \u001b[90m 11 |\u001b[39m \u001b[36mclass\u001b[39m \u001b[33mSCCConnector\u001b[39m {\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-connect/src/connectors/gcp/scc-connector.js\u001b[2m:8:34)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/gcp/scc-live-integration.test.js\u001b[39m\u001b[0m\u001b[2m:12:26)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '@google-cloud/security-center' from 'nova-connect/src/connectors/gcp/scc-connector.js'\n\nRequire stack:\n  nova-connect/src/connectors/gcp/scc-connector.js\n  nova-connect/tests/integration/gcp/scc-live-integration.test.js\n", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\gcp\\scc-live-integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module '../../../src/connectors/gcp/scc-connector' from 'tests/integration/gcp/scc-live-integration.test.js'\n\n    \u001b[0m \u001b[90m 10 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m 11 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mSCCConnector\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/connectors/gcp/scc-connector'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 13 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mTransformationEngine\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/engines/transformation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 14 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mRemediationEngine\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'../../../src/engines/remediation-engine'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 15 |\u001b[39m \u001b[36mconst\u001b[39m { performance } \u001b[33m=\u001b[39m require(\u001b[32m'perf_hooks'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mtests/integration/gcp/scc-live-integration.test.js\u001b[39m\u001b[0m\u001b[2m:12:26)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module '../../../src/connectors/gcp/scc-connector' from 'tests/integration/gcp/scc-live-integration.test.js'", "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\gcp\\scc-live-integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m  5 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m  6 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  7 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m  8 |\u001b[39m \u001b[36mconst\u001b[39m logger \u001b[33m=\u001b[39m require(\u001b[32m'./logger'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m  9 |\u001b[39m\n     \u001b[90m 10 |\u001b[39m \u001b[90m// Default connection options\u001b[39m\u001b[0m\n\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/encoding.js\u001b[2m:2:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/url-state-machine.js\u001b[2m:5:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL-impl.js\u001b[2m:2:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/lib/URL.js\u001b[2m:442:14)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/webidl2js-wrapper.js\u001b[2m:3:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/whatwg-url/index.js\u001b[2m:3:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb-connection-string-url/src/index.ts\u001b[2m:1:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/connection_string.ts\u001b[2m:3:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/mongo_client.ts\u001b[2m:17:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/change_stream.ts\u001b[2m:16:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongodb/src/index.ts\u001b[2m:4:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/collection.js\u001b[2m:9:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/drivers/node-mongodb-native/index.js\u001b[2m:7:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/lib/index.js\u001b[2m:7:25)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnode_modules/mongoose/index.js\u001b[2m:8:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-connect/api/config/database.js\u001b[2m:7:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-connect/api/app.js\u001b[2m:12:25)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/routes/securityFeatures.test.js\u001b[39m\u001b[0m\u001b[2m:6:13)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\routes\\securityFeatures.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    TypeError: D:\\novafuse-api-superstore\\tests\\integration\\ui-navigation.test.js: Property declarations[0] of VariableDeclaration expected node to be of a type [\"VariableDeclarator\"] but instead got undefined\n\n      \u001b[2mat validate (\u001b[22mnode_modules/@babel/types/src/definitions/utils.ts\u001b[2m:163:11)\u001b[22m\n      \u001b[2mat callback (\u001b[22mnode_modules/@babel/types/src/definitions/utils.ts\u001b[2m:112:7)\u001b[22m\n      \u001b[2mat Object.fn [as validate] (\u001b[22mnode_modules/@babel/types/src/definitions/utils.ts\u001b[2m:279:7)\u001b[22m\n      \u001b[2mat validate (\u001b[22mnode_modules/@babel/types/src/validators/validate.ts\u001b[2m:33:9)\u001b[22m\n      \u001b[2mat validate (\u001b[22mnode_modules/@babel/types/src/builders/generated/lowercase.ts\u001b[2m:707:3)\u001b[22m\n      \u001b[2mat NodePath.call [as _call] (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:36:20)\u001b[22m\n      \u001b[2mat NodePath.call (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:21:18)\u001b[22m\n      \u001b[2mat NodePath.call [as visit] (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:97:31)\u001b[22m\n      \u001b[2mat TraversalContext.visit [as visitQueue] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:148:16)\u001b[22m\n      \u001b[2mat TraversalContext.visitQueue [as visitMultiple] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:99:17)\u001b[22m\n      \u001b[2mat TraversalContext.visitMultiple [as visit] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:178:19)\u001b[22m\n      \u001b[2mat visit (\u001b[22mnode_modules/@babel/traverse/src/traverse-node.ts\u001b[2m:40:17)\u001b[22m\n      \u001b[2mat NodePath.visit (\u001b[22mnode_modules/@babel/traverse/src/path/context.ts\u001b[2m:104:33)\u001b[22m\n      \u001b[2mat TraversalContext.visit [as visitQueue] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:148:16)\u001b[22m\n      \u001b[2mat TraversalContext.visitQueue [as visitMultiple] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:99:17)\u001b[22m\n      \u001b[2mat TraversalContext.visitMultiple [as visit] (\u001b[22mnode_modules/@babel/traverse/src/context.ts\u001b[2m:178:19)\u001b[22m\n      \u001b[2mat visit (\u001b[22mnode_modules/@babel/traverse/src/traverse-node.ts\u001b[2m:40:17)\u001b[22m\n      \u001b[2mat traverse (\u001b[22mnode_modules/@babel/traverse/src/index.ts\u001b[2m:83:15)\u001b[22m\n      \u001b[2mat NodePath.traverse (\u001b[22mnode_modules/@babel/traverse/src/path/index.ts\u001b[2m:147:13)\u001b[22m\n      \u001b[2mat PluginPass.apply (\u001b[22mnode_modules/@babel/core/src/gensync-utils/async.ts\u001b[2m:31:25)\u001b[22m\n      \u001b[2mat PluginPass.sync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:182:19)\u001b[22m\n      \u001b[2mat PluginPass.<anonymous> (\u001b[22mnode_modules/gensync/index.js\u001b[2m:210:24)\u001b[22m\n      \u001b[2mat call (\u001b[22mnode_modules/@babel/core/src/transformation/index.ts\u001b[2m:130:19)\u001b[22m\n          at transformFile.next (<anonymous>)\n      \u001b[2mat transformFile (\u001b[22mnode_modules/@babel/core/src/transformation/index.ts\u001b[2m:49:12)\u001b[22m\n          at run.next (<anonymous>)\n      \u001b[2mat transform (\u001b[22mnode_modules/@babel/core/src/transform.ts\u001b[2m:29:20)\u001b[22m\n          at transform.next (<anonymous>)\n      \u001b[2mat evaluateSync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:251:28)\u001b[22m\n      \u001b[2mat sync (\u001b[22mnode_modules/gensync/index.js\u001b[2m:89:14)\u001b[22m\n      \u001b[2mat fn (\u001b[22mnode_modules/@babel/core/src/errors/rewrite-stack-trace.ts\u001b[2m:99:14)\u001b[22m\n      \u001b[2mat transformSync (\u001b[22mnode_modules/@babel/core/src/transform.ts\u001b[2m:66:52)\u001b[22m\n      \u001b[2mat ScriptTransformer.transformSource (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:545:31)\u001b[22m\n      \u001b[2mat ScriptTransformer._transformAndBuildScript (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:674:40)\u001b[22m\n      \u001b[2mat ScriptTransformer.transform (\u001b[22mnode_modules/@jest/transform/build/ScriptTransformer.js\u001b[2m:726:19)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "D:\\novafuse-api-superstore\\tests\\integration\\ui-navigation.test.js: Property declarations[0] of VariableDeclaration expected node to be of a type [\"VariableDeclarator\"] but instead got undefined", "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\ui-navigation.test.js", "testResults": []}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 7, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1752313440313, "runtime": 1258, "slow": false, "start": 1752313439055}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novaproof\\evidence-verification.test.js", "testResults": [{"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 50, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints GET /api/v1/evidence should return a list of evidence items", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence should return a list of evidence items"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 296, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints POST /api/v1/evidence should create a new evidence item", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "POST /api/v1/evidence should create a new evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Evidence Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Evidence Endpoints GET /api/v1/evidence/:id should return a specific evidence item", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence/:id should return a specific evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints POST /api/v1/verification should verify an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "POST /api/v1/verification should verify an evidence item"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification should return a list of verifications", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification should return a list of verifications"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification/:id should return a specific verification", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification/:id should return a specific verification"}, {"ancestorTitles": ["NovaProof API Integration Tests", "Verification Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaProof API Integration Tests Verification Endpoints GET /api/v1/verification/:id/proof should return the proof for a verification", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "GET /api/v1/verification/:id/proof should return the proof for a verification"}], "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 7, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1752313440969, "runtime": 616, "slow": false, "start": 1752313440353}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novacore\\api-endpoints.test.js", "testResults": [{"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints GET /api/v1/evidence should return a list of evidence items", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence should return a list of evidence items"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints POST /api/v1/evidence should create a new evidence item", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "POST /api/v1/evidence should create a new evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints GET /api/v1/evidence/:id should return a specific evidence item", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence/:id should return a specific evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints PUT /api/v1/evidence/:id should update an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "PUT /api/v1/evidence/:id should update an evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints DELETE /api/v1/evidence/:id should delete an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "DELETE /api/v1/evidence/:id should delete an evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Blockchain Endpoints"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Blockchain Endpoints POST /api/v1/blockchain/verify should verify data on the blockchain", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "POST /api/v1/blockchain/verify should verify data on the blockchain"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Connector Endpoints"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Connector Endpoints GET /api/v1/connectors should return a list of connectors", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/connectors should return a list of connectors"}], "failureMessage": null}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    Cannot find module 'apollo-server-express' from 'nova-connect/api/graphql/server.js'\n\n    Require stack:\n      nova-connect/api/graphql/server.js\n      nova-connect/api/connector-api.js\n      nova-connect/tests/integration/connector-api.integration.test.js\n\n    \u001b[0m \u001b[90m  5 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m  6 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  7 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mApolloServer\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'apollo-server-express'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m  8 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mApolloServerPluginDrainHttpServer\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'apollo-server-core'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m  9 |\u001b[39m \u001b[36mconst\u001b[39m { makeExecutableSchema } \u001b[33m=\u001b[39m require(\u001b[32m'@graphql-tools/schema'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 10 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mSubscriptionServer\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'subscriptions-transport-ws'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Resolver._throwModNotFoundError (\u001b[22mnode_modules/jest-resolve/build/resolver.js\u001b[2m:427:11)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-connect/api/graphql/server.js\u001b[2m:7:26)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-connect/api/connector-api.js\u001b[2m:14:29)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/connector-api.integration.test.js\u001b[39m\u001b[0m\u001b[2m:8:22)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Cannot find module 'apollo-server-express' from 'nova-connect/api/graphql/server.js'\n\nRequire stack:\n  nova-connect/api/graphql/server.js\n  nova-connect/api/connector-api.js\n  nova-connect/tests/integration/connector-api.integration.test.js\n", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\connector-api.integration.test.js", "testResults": []}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    \u001b[1m\u001b[31m<PERSON><PERSON> encountered an unexpected token\u001b[39m\u001b[22m\n\n    <PERSON><PERSON> failed to parse a file. This happens e.g. when your code or its dependencies use non-standard JavaScript syntax, or when <PERSON><PERSON> is not configured to support such syntax.\n\n    Out of the box Je<PERSON> supports Babel, which will be used to transform your files into valid JS based on your Babel configuration.\n\n    By default \"node_modules\" folder is ignored by transformers.\n\n    Here's what you can do:\n     • If you are trying to use ECMAScript Modules, see \u001b[4mhttps://jestjs.io/docs/ecmascript-modules\u001b[24m for how to enable it.\n     • If you are trying to use TypeScript, see \u001b[4mhttps://jestjs.io/docs/getting-started#using-typescript\u001b[24m\n     • To have some of your \"node_modules\" files transformed, you can specify a custom \u001b[1m\"transformIgnorePatterns\"\u001b[22m in your config.\n     • If you need a custom transformation specify a \u001b[1m\"transform\"\u001b[22m option in your config.\n     • If you simply want to mock your non-JS modules (e.g. binary assets) you can stub them out with the \u001b[1m\"moduleNameMapper\"\u001b[22m config option.\n\n    You'll find more details and examples of these config options in the docs:\n    \u001b[36mhttps://jestjs.io/docs/configuration\u001b[39m\n    For information about custom transformations, see:\n    \u001b[36mhttps://jestjs.io/docs/code-transformation\u001b[39m\n\n    \u001b[1m\u001b[31mDetails:\u001b[39m\u001b[22m\n\n    D:\\novafuse-api-superstore\\node_modules\\chai\\chai.js:4101\n    export {\n    ^^^^^^\n\n    SyntaxError: Unexpected token 'export'\n\n    \u001b[0m \u001b[90m 3 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m 4 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 5 |\u001b[39m \u001b[36mconst\u001b[39m { expect } \u001b[33m=\u001b[39m require(\u001b[32m'chai'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m   |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m 6 |\u001b[39m \u001b[36mconst\u001b[39m supertest \u001b[33m=\u001b[39m require(\u001b[32m'supertest'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 7 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m 8 |\u001b[39m \u001b[36mconst\u001b[39m { \u001b[33mMongoMemoryServer\u001b[39m } \u001b[33m=\u001b[39m require(\u001b[32m'mongodb-memory-server'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\n      \u001b[2mat Runtime.createScriptFromCode (\u001b[22mnode_modules/jest-runtime/build/index.js\u001b[2m:1505:14)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-connect/tests/integration/api.integration.test.js\u001b[39m\u001b[0m\u001b[2m:5:20)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "Unexpected token 'export'", "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\api.integration.test.js", "testResults": []}, {"leaks": false, "numFailingTests": 2, "numPassingTests": 2, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1752313442100, "runtime": 1091, "slow": false, "start": 1752313441009}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\tenant-audit-isolation.test.js", "testResults": [{"ancestorTitles": ["Tenant Audit Isolation"], "duration": 447, "failureDetails": [], "failureMessages": [], "fullName": "Tenant Audit Isolation should log events for multiple tenants", "invocations": 1, "location": null, "numPassingAsserts": 22, "retryReasons": [], "status": "passed", "title": "should log events for multiple tenants"}, {"ancestorTitles": ["Tenant Audit Isolation"], "duration": 7, "failureDetails": [{"matcherResult": {"actual": "test-tenant-3", "expected": "test-tenant-1", "message": "\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"test-tenant-\u001b[7m1\u001b[27m\"\u001b[39m\nReceived: \u001b[31m\"test-tenant-\u001b[7m3\u001b[27m\"\u001b[39m", "name": "toBe", "pass": false}}], "failureMessages": ["Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m\"test-tenant-\u001b[7m1\u001b[27m\"\u001b[39m\nReceived: \u001b[31m\"test-tenant-\u001b[7m3\u001b[27m\"\u001b[39m\n    at toBe (D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\tenant-audit-isolation.test.js:96:30)\n    at Array.forEach (<anonymous>)\n    at Object.forEach (D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\tenant-audit-isolation.test.js:95:23)"], "fullName": "Tenant Audit Isolation should not expose logs across tenants", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should not expose logs across tenants"}, {"ancestorTitles": ["Tenant Audit Isolation"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Tenant Audit Isolation should handle requests without tenant ID", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle requests without tenant ID"}, {"ancestorTitles": ["Tenant Audit Isolation"], "duration": 2, "failureDetails": [{}], "failureMessages": ["TypeError: this.logTenantEvent is not a function\n    at Object.logTenantEvent (D:\\novafuse-api-superstore\\nova-connect\\api\\services\\AuditService.js:434:16)\n    at Object.end (D:\\novafuse-api-superstore\\nova-connect\\tests\\integration\\tenant-audit-isolation.test.js:161:9)\n    at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)\n    at Object.worker (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\testWorker.js:106:12)"], "fullName": "Tenant Audit Isolation should handle tenant-specific middleware", "invocations": 1, "location": null, "numPassingAsserts": 0, "retryReasons": [], "status": "failed", "title": "should handle tenant-specific middleware"}], "failureMessage": "\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTenant Audit Isolation › should not expose logs across tenants\u001b[39m\u001b[22m\n\n    \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\n    Expected: \u001b[32m\"test-tenant-\u001b[7m1\u001b[27m\"\u001b[39m\n    Received: \u001b[31m\"test-tenant-\u001b[7m3\u001b[27m\"\u001b[39m\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 94 |\u001b[39m       \u001b[90m// Verify that all logs belong to this tenant\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 95 |\u001b[39m       tenantLogs\u001b[33m.\u001b[39mlogs\u001b[33m.\u001b[39mforEach(log \u001b[33m=>\u001b[39m {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 96 |\u001b[39m         expect(log\u001b[33m.\u001b[39mtenantId)\u001b[33m.\u001b[39mtoBe(tenantId)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m    |\u001b[39m                              \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 97 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 98 |\u001b[39m       \u001b[22m\n\u001b[2m     \u001b[90m 99 |\u001b[39m       \u001b[90m// Verify that the count matches expected\u001b[39m\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat toBe (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/integration/tenant-audit-isolation.test.js\u001b[39m\u001b[0m\u001b[2m:96:30)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m          at Array.forEach (<anonymous>)\u001b[22m\n\u001b[2m      \u001b[2mat Object.forEach (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/integration/tenant-audit-isolation.test.js\u001b[39m\u001b[0m\u001b[2m:95:23)\u001b[22m\u001b[2m\u001b[22m\n\n\u001b[1m\u001b[31m  \u001b[1m● \u001b[22m\u001b[1mTenant Audit Isolation › should handle tenant-specific middleware\u001b[39m\u001b[22m\n\n    TypeError: this.logTenantEvent is not a function\n\u001b[2m\u001b[22m\n\u001b[2m    \u001b[0m \u001b[90m 432 |\u001b[39m         \u001b[90m// If tenant ID is present, use tenant-specific logging\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 433 |\u001b[39m         \u001b[36mif\u001b[39m (req\u001b[33m.\u001b[39mheaders[\u001b[32m'x-tenant-id'\u001b[39m]) {\u001b[22m\n\u001b[2m    \u001b[31m\u001b[1m>\u001b[22m\u001b[2m\u001b[39m\u001b[90m 434 |\u001b[39m           \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogTenantEvent(req\u001b[33m.\u001b[39mheaders[\u001b[32m'x-tenant-id'\u001b[39m]\u001b[33m,\u001b[39m auditData)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m     |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[2m\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 435 |\u001b[39m         } \u001b[36melse\u001b[39m {\u001b[22m\n\u001b[2m     \u001b[90m 436 |\u001b[39m           \u001b[36mthis\u001b[39m\u001b[33m.\u001b[39mlogEvent(auditData)\u001b[33m;\u001b[39m\u001b[22m\n\u001b[2m     \u001b[90m 437 |\u001b[39m         }\u001b[0m\u001b[22m\n\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.logTenantEvent (\u001b[22m\u001b[2mnova-connect/api/services/AuditService.js\u001b[2m:434:16)\u001b[22m\u001b[2m\u001b[22m\n\u001b[2m      \u001b[2mat Object.end (\u001b[22m\u001b[2m\u001b[0m\u001b[36mnova-connect/tests/integration/tenant-audit-isolation.test.js\u001b[39m\u001b[0m\u001b[2m:161:9)\u001b[22m\u001b[2m\u001b[22m\n"}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 3, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1752313442978, "runtime": 896, "slow": false, "start": 1752313442082}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novatrack\\api.simple.test.js", "testResults": [{"ancestorTitles": ["NovaTrack API - Simple Tests", "GET /health"], "duration": 49, "failureDetails": [], "failureMessages": [], "fullName": "NovaTrack API - Simple Tests GET /health should return a 200 status code", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a 200 status code"}, {"ancestorTitles": ["NovaTrack API - Simple Tests", "POST /api/v1/novatrack/requirements"], "duration": 49, "failureDetails": [], "failureMessages": [], "fullName": "NovaTrack API - Simple Tests POST /api/v1/novatrack/requirements should create a new requirement", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should create a new requirement"}, {"ancestorTitles": ["NovaTrack API - Simple Tests", "POST /api/v1/novatrack/requirements"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NovaTrack API - Simple Tests POST /api/v1/novatrack/requirements should return a 400 error for invalid requirement data", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a 400 error for invalid requirement data"}], "failureMessage": null}, {"failureMessage": "  \u001b[1m● \u001b[22mTest suite failed to run\n\n    ReferenceError: TextEncoder is not defined\n\n    \u001b[0m \u001b[90m  5 |\u001b[39m \u001b[90m */\u001b[39m\n     \u001b[90m  6 |\u001b[39m\n    \u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  7 |\u001b[39m \u001b[36mconst\u001b[39m mongoose \u001b[33m=\u001b[39m require(\u001b[32m'mongoose'\u001b[39m)\u001b[33m;\u001b[39m\n     \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n     \u001b[90m  8 |\u001b[39m\n     \u001b[90m  9 |\u001b[39m \u001b[90m// MongoDB connection options\u001b[39m\n     \u001b[90m 10 |\u001b[39m \u001b[36mconst\u001b[39m options \u001b[33m=\u001b[39m {\u001b[0m\n\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/whatwg-url/lib/encoding.js\u001b[2m:2:21)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/whatwg-url/lib/url-state-machine.js\u001b[2m:5:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/whatwg-url/lib/URL-impl.js\u001b[2m:2:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/whatwg-url/lib/URL.js\u001b[2m:442:14)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/whatwg-url/webidl2js-wrapper.js\u001b[2m:3:13)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/whatwg-url/index.js\u001b[2m:3:34)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongodb-connection-string-url/src/index.ts\u001b[2m:1:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/node_modules/mongodb/src/connection_string.ts\u001b[2m:3:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/node_modules/mongodb/src/mongo_client.ts\u001b[2m:17:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/node_modules/mongodb/src/change_stream.ts\u001b[2m:16:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/node_modules/mongodb/src/index.ts\u001b[2m:4:1)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/lib/drivers/node-mongodb-native/collection.js\u001b[2m:9:20)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/lib/drivers/node-mongodb-native/index.js\u001b[2m:7:22)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/lib/index.js\u001b[2m:7:25)\u001b[22m\n      \u001b[2mat Object.<anonymous> (\u001b[22mnova-marketplace/apis/privacy/management/node_modules/mongoose/index.js\u001b[2m:8:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-marketplace/apis/privacy/management/config/database.js\u001b[2m:7:18)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22mnova-marketplace/apis/privacy/management/server.js\u001b[2m:10:21)\u001b[22m\n      \u001b[2mat Object.require (\u001b[22m\u001b[0m\u001b[36mnova-marketplace/apis/privacy/management/tests/integration/auth.test.js\u001b[39m\u001b[0m\u001b[2m:8:13)\u001b[22m\n", "leaks": false, "numFailingTests": 0, "numPassingTests": 0, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 0, "runtime": 0, "slow": false, "start": 0}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testExecError": "TextEncoder is not defined", "testFilePath": "D:\\novafuse-api-superstore\\nova-marketplace\\apis\\privacy\\management\\tests\\integration\\auth.test.js", "testResults": []}], "wasInterrupted": false}