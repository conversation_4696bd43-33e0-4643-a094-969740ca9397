/**
 * Tests for Authentication Service
 */

import authService, { LoginCredentials } from '../authService';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    }
  };
})();

Object.defineProperty(window, 'localStorage', { value: localStorageMock });

// Mock jwt-decode
jest.mock('jwt-decode', () => jest.fn((token) => {
  if (token === 'valid_token') {
    return {
      sub: 'user123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      organizationId: 'org123',
      permissions: ['frameworks:view'],
      exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
    };
  } else if (token === 'expired_token') {
    return {
      sub: 'user123',
      email: '<EMAIL>',
      exp: Math.floor(Date.now() / 1000) - 3600 // 1 hour ago (expired)
    };
  }
  throw new Error('Invalid token');
}));

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.clear();
  });

  describe('login', () => {
    it('should store token and user data on successful login', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockResponse = {
        data: {
          token: 'valid_token',
          user: {
            id: 'user123',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            role: 'admin',
            organizationId: 'org123',
            permissions: ['frameworks:view']
          }
        }
      };

      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const result = await authService.login(credentials);

      expect(mockedAxios.post).toHaveBeenCalledWith('/api/v1/auth/login', credentials);
      expect(localStorageMock.getItem('novafuse_token')).toBe('valid_token');
      expect(JSON.parse(localStorageMock.getItem('novafuse_user') || '{}')).toEqual(mockResponse.data.user);
      expect(result).toEqual(mockResponse.data.user);
    });

    it('should throw an error when login fails', async () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'wrong_password'
      };

      mockedAxios.post.mockRejectedValueOnce({
        response: {
          data: {
            message: 'Invalid credentials'
          }
        }
      });

      await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
    });
  });

  describe('isAuthenticated', () => {
    it('should return true for valid token', () => {
      localStorageMock.setItem('novafuse_token', 'valid_token');
      expect(authService.isAuthenticated()).toBe(true);
    });

    it('should return false for expired token', () => {
      localStorageMock.setItem('novafuse_token', 'expired_token');
      expect(authService.isAuthenticated()).toBe(false);
    });

    it('should return false when no token exists', () => {
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('logout', () => {
    it('should remove token and user data', () => {
      // Setup
      localStorageMock.setItem('novafuse_token', 'valid_token');
      localStorageMock.setItem('novafuse_user', JSON.stringify({ id: 'user123' }));
      
      // Mock window.location
      const originalLocation = window.location;
      delete window.location;
      window.location = { href: '' } as unknown as Location;
      
      // Execute
      authService.logout();
      
      // Assert
      expect(localStorageMock.getItem('novafuse_token')).toBeNull();
      expect(localStorageMock.getItem('novafuse_user')).toBeNull();
      expect(window.location.href).toBe('/login');
      
      // Restore
      window.location = originalLocation;
    });
  });

  describe('getCurrentUser', () => {
    it('should return user data if authenticated', () => {
      const userData = {
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'admin',
        organizationId: 'org123',
        permissions: ['frameworks:view']
      };
      
      localStorageMock.setItem('novafuse_token', 'valid_token');
      localStorageMock.setItem('novafuse_user', JSON.stringify(userData));
      
      expect(authService.getCurrentUser()).toEqual(userData);
    });

    it('should return null if not authenticated', () => {
      expect(authService.getCurrentUser()).toBeNull();
    });
  });

  describe('hasPermissions', () => {
    it('should return true if user has all required permissions', () => {
      const userData = {
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'admin',
        organizationId: 'org123',
        permissions: ['frameworks:view', 'frameworks:edit', 'regulations:view']
      };
      
      localStorageMock.setItem('novafuse_token', 'valid_token');
      localStorageMock.setItem('novafuse_user', JSON.stringify(userData));
      
      expect(authService.hasPermissions(['frameworks:view', 'regulations:view'])).toBe(true);
    });

    it('should return false if user is missing any required permission', () => {
      const userData = {
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'admin',
        organizationId: 'org123',
        permissions: ['frameworks:view', 'regulations:view']
      };
      
      localStorageMock.setItem('novafuse_token', 'valid_token');
      localStorageMock.setItem('novafuse_user', JSON.stringify(userData));
      
      expect(authService.hasPermissions(['frameworks:view', 'frameworks:edit'])).toBe(false);
    });

    it('should return false if user is not authenticated', () => {
      expect(authService.hasPermissions(['frameworks:view'])).toBe(false);
    });
  });

  describe('getOrganizationId', () => {
    it('should return organization ID if authenticated', () => {
      const userData = {
        id: 'user123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'admin',
        organizationId: 'org123',
        permissions: ['frameworks:view']
      };
      
      localStorageMock.setItem('novafuse_token', 'valid_token');
      localStorageMock.setItem('novafuse_user', JSON.stringify(userData));
      
      expect(authService.getOrganizationId()).toBe('org123');
    });

    it('should return null if not authenticated', () => {
      expect(authService.getOrganizationId()).toBeNull();
    });
  });
});
