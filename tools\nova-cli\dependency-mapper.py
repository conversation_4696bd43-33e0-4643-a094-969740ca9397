#!/usr/bin/env python3
"""
Nova Component Dependency Mapper
Maps dependencies and relationships between Nova components
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import networkx as nx


@dataclass
class Dependency:
    """Represents a dependency relationship"""
    source: str
    target: str
    dependency_type: str  # "import", "api_call", "config", "data"
    strength: float  # 0.0 to 1.0
    evidence: List[str]


@dataclass
class ComponentNode:
    """Represents a component in the dependency graph"""
    name: str
    path: str
    component_type: str
    language: str
    dependencies_out: List[str]
    dependencies_in: List[str]
    centrality_score: float


class NovaDependencyMapper:
    """Maps dependencies between Nova components"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.components: Dict[str, ComponentNode] = {}
        self.dependencies: List[Dependency] = []
        self.graph = nx.DiGraph()
    
    def map_all_dependencies(self) -> Dict[str, Any]:
        """Map all dependencies between Nova components"""
        print("🗺️  Mapping Nova component dependencies...")
        
        # Discover components
        components = self._discover_components()
        
        # Analyze each component
        for component in components:
            print(f"   🔍 Analyzing {component['name']}...")
            self._analyze_component_dependencies(component)
        
        # Build dependency graph
        self._build_dependency_graph()
        
        # Calculate metrics
        self._calculate_metrics()
        
        return self._generate_dependency_report()
    
    def _discover_components(self) -> List[Dict[str, Any]]:
        """Discover Nova components"""
        components = []
        
        # Check src/ directory
        src_path = self.workspace_path / "src"
        if src_path.exists():
            for item in src_path.iterdir():
                if item.is_dir() and (item.name.lower().startswith("nova") or 
                                    item.name.lower().startswith("cs") or
                                    item.name.lower().startswith("uc")):
                    components.append({
                        "name": item.name,
                        "path": str(item),
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item)
                    })
        
        # Check root level components
        for pattern in ["nova*", "Nova*", "coherence-reality-systems"]:
            for item in self.workspace_path.glob(pattern):
                if item.is_dir() and item.name not in [c["name"] for c in components]:
                    components.append({
                        "name": item.name,
                        "path": str(item),
                        "type": self._detect_component_type(item),
                        "language": self._detect_language(item)
                    })
        
        return components
    
    def _detect_component_type(self, path: Path) -> str:
        """Detect component type"""
        name = path.name.lower()
        if "shield" in name or "auth" in name:
            return "Security"
        elif "core" in name:
            return "Infrastructure"
        elif "vision" in name or "ui" in name:
            return "UI"
        elif "ai" in name or "sentient" in name:
            return "AI/ML"
        elif "data" in name or "mem" in name:
            return "Data"
        else:
            return "Service"
    
    def _detect_language(self, path: Path) -> str:
        """Detect primary language"""
        if (path / "package.json").exists():
            return "javascript"
        elif (path / "requirements.txt").exists() or any(path.glob("*.py")):
            return "python"
        elif (path / "go.mod").exists():
            return "go"
        else:
            return "mixed"
    
    def _analyze_component_dependencies(self, component: Dict[str, Any]):
        """Analyze dependencies for a single component"""
        path = Path(component["path"])
        name = component["name"]
        
        dependencies_out = []
        dependencies_in = []
        
        # Analyze code files for imports and references
        code_files = list(path.rglob("*.py")) + list(path.rglob("*.js")) + list(path.rglob("*.ts"))
        
        for file_path in code_files:
            try:
                content = file_path.read_text()
                deps = self._extract_dependencies_from_code(content, name)
                dependencies_out.extend(deps)
            except:
                continue
        
        # Create component node
        self.components[name] = ComponentNode(
            name=name,
            path=component["path"],
            component_type=component["type"],
            language=component["language"],
            dependencies_out=list(set(dependencies_out)),
            dependencies_in=[],  # Will be populated later
            centrality_score=0.0
        )
    
    def _extract_dependencies_from_code(self, content: str, source_component: str) -> List[str]:
        """Extract dependencies from code content"""
        dependencies = []
        
        # Python imports
        python_imports = re.findall(r'from\s+(\w+)', content)
        python_imports.extend(re.findall(r'import\s+(\w+)', content))
        
        # JavaScript imports/requires
        js_imports = re.findall(r'from\s+[\'"]([^\'\"]+)[\'"]', content)
        js_imports.extend(re.findall(r'require\([\'"]([^\'\"]+)[\'"]\)', content))
        
        # API calls (look for nova component names in URLs or function calls)
        api_calls = re.findall(r'[\'"].*?(nova\w+).*?[\'"]', content, re.IGNORECASE)
        
        # Combine all potential dependencies
        all_deps = python_imports + js_imports + api_calls
        
        # Filter for Nova components
        for dep in all_deps:
            if dep.lower().startswith('nova') or dep.lower().startswith('cs') or dep.lower().startswith('uc'):
                if dep != source_component:  # Don't include self-references
                    dependencies.append(dep)
                    
                    # Create dependency object
                    self.dependencies.append(Dependency(
                        source=source_component,
                        target=dep,
                        dependency_type="import",
                        strength=0.7,
                        evidence=[f"Found in code: {dep}"]
                    ))
        
        return dependencies
    
    def _build_dependency_graph(self):
        """Build NetworkX graph from dependencies"""
        
        # Add nodes
        for comp_name, comp_node in self.components.items():
            self.graph.add_node(comp_name, **{
                "type": comp_node.component_type,
                "language": comp_node.language,
                "path": comp_node.path
            })
        
        # Add edges
        for dep in self.dependencies:
            if dep.source in self.components and dep.target in self.components:
                self.graph.add_edge(dep.source, dep.target, **{
                    "type": dep.dependency_type,
                    "strength": dep.strength,
                    "evidence": dep.evidence
                })
    
    def _calculate_metrics(self):
        """Calculate dependency metrics"""
        
        if not self.graph.nodes():
            return
        
        # Calculate centrality scores
        try:
            centrality = nx.betweenness_centrality(self.graph)
            pagerank = nx.pagerank(self.graph)
            
            for comp_name in self.components:
                if comp_name in centrality:
                    self.components[comp_name].centrality_score = centrality[comp_name]
        except:
            pass  # Handle empty graph case
        
        # Update incoming dependencies
        for comp_name, comp_node in self.components.items():
            comp_node.dependencies_in = list(self.graph.predecessors(comp_name))
    
    def _generate_dependency_report(self) -> Dict[str, Any]:
        """Generate comprehensive dependency report"""
        
        # Component statistics
        total_components = len(self.components)
        total_dependencies = len(self.dependencies)
        
        # Find critical components (high centrality)
        critical_components = sorted(
            self.components.values(),
            key=lambda x: x.centrality_score,
            reverse=True
        )[:5]
        
        # Find isolated components
        isolated_components = [
            comp for comp in self.components.values()
            if not comp.dependencies_in and not comp.dependencies_out
        ]
        
        # Dependency chains
        try:
            cycles = list(nx.simple_cycles(self.graph))
        except:
            cycles = []
        
        return {
            "summary": {
                "total_components": total_components,
                "total_dependencies": total_dependencies,
                "average_dependencies_per_component": total_dependencies / total_components if total_components > 0 else 0,
                "circular_dependencies": len(cycles),
                "isolated_components": len(isolated_components)
            },
            "components": [
                {
                    "name": comp.name,
                    "type": comp.component_type,
                    "language": comp.language,
                    "dependencies_out": comp.dependencies_out,
                    "dependencies_in": comp.dependencies_in,
                    "centrality_score": comp.centrality_score,
                    "dependency_count": len(comp.dependencies_out) + len(comp.dependencies_in)
                }
                for comp in self.components.values()
            ],
            "critical_components": [
                {
                    "name": comp.name,
                    "centrality_score": comp.centrality_score,
                    "reason": "High centrality - many components depend on this"
                }
                for comp in critical_components
            ],
            "isolated_components": [
                {
                    "name": comp.name,
                    "reason": "No dependencies found - may be standalone or analysis incomplete"
                }
                for comp in isolated_components
            ],
            "circular_dependencies": [
                {
                    "cycle": cycle,
                    "risk": "High - circular dependencies can cause issues"
                }
                for cycle in cycles
            ],
            "dependencies": [
                {
                    "source": dep.source,
                    "target": dep.target,
                    "type": dep.dependency_type,
                    "strength": dep.strength,
                    "evidence": dep.evidence
                }
                for dep in self.dependencies
            ],
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on dependency analysis"""
        recommendations = []
        
        # Check for high-dependency components
        high_dep_components = [
            comp for comp in self.components.values()
            if len(comp.dependencies_out) + len(comp.dependencies_in) > 5
        ]
        
        if high_dep_components:
            recommendations.append(
                f"Consider refactoring high-dependency components: {', '.join([c.name for c in high_dep_components])}"
            )
        
        # Check for isolated components
        isolated = [comp for comp in self.components.values() 
                   if not comp.dependencies_in and not comp.dependencies_out]
        
        if isolated:
            recommendations.append(
                f"Review isolated components for integration opportunities: {', '.join([c.name for c in isolated])}"
            )
        
        # Check for circular dependencies
        try:
            cycles = list(nx.simple_cycles(self.graph))
            if cycles:
                recommendations.append(
                    f"Resolve circular dependencies: {len(cycles)} cycles detected"
                )
        except:
            pass
        
        return recommendations
    
    def export_graph_visualization(self, output_path: str = "nova-dependencies.json"):
        """Export graph data for visualization"""
        
        # Prepare data for D3.js or similar visualization
        nodes = [
            {
                "id": comp.name,
                "type": comp.component_type,
                "language": comp.language,
                "centrality": comp.centrality_score,
                "dependencies": len(comp.dependencies_out) + len(comp.dependencies_in)
            }
            for comp in self.components.values()
        ]
        
        links = [
            {
                "source": dep.source,
                "target": dep.target,
                "type": dep.dependency_type,
                "strength": dep.strength
            }
            for dep in self.dependencies
            if dep.source in self.components and dep.target in self.components
        ]
        
        graph_data = {
            "nodes": nodes,
            "links": links,
            "metadata": {
                "generated": datetime.now().isoformat(),
                "total_components": len(nodes),
                "total_dependencies": len(links)
            }
        }
        
        with open(output_path, 'w') as f:
            json.dump(graph_data, f, indent=2)
        
        print(f"📊 Graph visualization data exported to {output_path}")


def main():
    import sys
    
    workspace = sys.argv[1] if len(sys.argv) > 1 else "."
    
    mapper = NovaDependencyMapper(workspace)
    report = mapper.map_all_dependencies()
    
    print("\n" + "="*60)
    print("NOVA COMPONENT DEPENDENCY REPORT")
    print("="*60)
    print(json.dumps(report, indent=2))
    
    # Export visualization data
    mapper.export_graph_visualization()
    
    # Summary
    summary = report["summary"]
    print(f"\n🗺️  Dependency Summary:")
    print(f"   📦 Components: {summary['total_components']}")
    print(f"   🔗 Dependencies: {summary['total_dependencies']}")
    print(f"   📊 Avg Dependencies/Component: {summary['average_dependencies_per_component']:.1f}")
    print(f"   🔄 Circular Dependencies: {summary['circular_dependencies']}")
    print(f"   🏝️  Isolated Components: {summary['isolated_components']}")


if __name__ == "__main__":
    main()
