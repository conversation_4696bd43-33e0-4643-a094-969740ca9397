/**
 * NovaFuse Universal API Connector - Error Handler Middleware Tests
 * 
 * This module tests the error handler middleware for the UAC.
 */

const { errorHandler, notFoundHandler } = require('../../src/middleware/error-handler');
const {
  UAConnectorError,
  ValidationError,
  AuthenticationError,
  ResourceNotFoundError,
  ServerError
} = require('../../src/errors');

// Mock response object
const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

// Mock request object
const mockRequest = () => ({
  originalUrl: '/test',
  method: 'GET'
});

// Mock next function
const mockNext = jest.fn();

describe('Error Handler Middleware', () => {
  beforeEach(() => {
    // Set environment to production for most tests
    process.env.NODE_ENV = 'production';
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  describe('errorHandler', () => {
    it('should handle UAConnectorError with correct status code', () => {
      const error = new ValidationError('Validation failed');
      const req = mockRequest();
      const res = mockResponse();
      
      errorHandler(error, req, res, mockNext);
      
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: {
          message: 'The provided data is invalid. Please check your input and try again.',
          code: 'VALIDATION_ERROR',
          id: error.errorId
        }
      });
    });
    
    it('should handle AuthenticationError with correct status code', () => {
      const error = new AuthenticationError('Authentication failed');
      const req = mockRequest();
      const res = mockResponse();
      
      errorHandler(error, req, res, mockNext);
      
      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        error: {
          message: 'Authentication failed. Please check your credentials and try again.',
          code: 'AUTH_ERROR',
          id: error.errorId
        }
      });
    });
    
    it('should handle ResourceNotFoundError with correct status code', () => {
      const error = new ResourceNotFoundError('User', '123');
      const req = mockRequest();
      const res = mockResponse();
      
      errorHandler(error, req, res, mockNext);
      
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: {
          message: 'The requested user could not be found.',
          code: 'API_RESOURCE_NOT_FOUND',
          id: error.errorId
        }
      });
    });
    
    it('should handle ServerError with correct status code', () => {
      const error = new ServerError('Server error');
      const req = mockRequest();
      const res = mockResponse();
      
      errorHandler(error, req, res, mockNext);
      
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: {
          message: 'An error occurred on the server. Please try again later or contact support if the issue persists.',
          code: 'API_SERVER_ERROR',
          id: error.errorId
        }
      });
    });
    
    it('should handle generic Error with 500 status code', () => {
      const error = new Error('Generic error');
      const req = mockRequest();
      const res = mockResponse();
      
      errorHandler(error, req, res, mockNext);
      
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: {
          message: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR'
        }
      });
    });
    
    it('should include more details in development mode', () => {
      process.env.NODE_ENV = 'development';
      
      const error = new ValidationError('Validation failed');
      const req = mockRequest();
      const res = mockResponse();
      
      errorHandler(error, req, res, mockNext);
      
      const response = res.json.mock.calls[0][0];
      expect(response.error.message).toBe('[VALIDATION_ERROR] Validation failed');
      expect(response.error.details).toBeDefined();
      expect(response.error.details.stack).toBeDefined();
    });
  });
  
  describe('notFoundHandler', () => {
    it('should create a ResourceNotFoundError and pass it to next', () => {
      const req = mockRequest();
      const res = mockResponse();
      
      notFoundHandler(req, res, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      const error = mockNext.mock.calls[0][0];
      expect(error).toBeInstanceOf(ResourceNotFoundError);
      expect(error.resourceType).toBe('Route');
      expect(error.resourceId).toBe('/test');
    });
  });
});
