/**
 * NovaAssure API Client
 * 
 * This module provides functions for interacting with the NovaAssure API.
 */

import axios from 'axios';

const API_BASE_URL = '/api/v1/novaassure';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Controls API
export const controlsApi = {
  getAll: (params) => api.get('/controls', { params }),
  getById: (id) => api.get(`/controls/${id}`),
  create: (data) => api.post('/controls', data),
  update: (id, data) => api.put(`/controls/${id}`, data),
  delete: (id) => api.delete(`/controls/${id}`),
  import: (formData) => api.post('/controls/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  export: (params) => api.get('/controls/export', { params, responseType: 'blob' }),
  getMapping: (params) => api.get('/controls/mapping', { params })
};

// Test Plans API
export const testPlansApi = {
  getAll: (params) => api.get('/test-plans', { params }),
  getById: (id) => api.get(`/test-plans/${id}`),
  create: (data) => api.post('/test-plans', data),
  update: (id, data) => api.put(`/test-plans/${id}`, data),
  delete: (id) => api.delete(`/test-plans/${id}`),
  schedule: (id, data) => api.post(`/test-plans/${id}/schedule`, data),
  assign: (id, data) => api.post(`/test-plans/${id}/assign`, data),
  clone: (id, data) => api.post(`/test-plans/${id}/clone`, data)
};

// Test Execution API
export const testExecutionApi = {
  getAll: (params) => api.get('/test-execution', { params }),
  getById: (id) => api.get(`/test-execution/${id}`),
  start: (data) => api.post('/test-execution/start', data),
  complete: (id, data) => api.post(`/test-execution/${id}/complete`, data),
  cancel: (id, data) => api.post(`/test-execution/${id}/cancel`, data),
  addResult: (id, data) => api.post(`/test-execution/${id}/result`, data),
  scheduleAutomated: (data) => api.post('/test-execution/schedule', data),
  runAutomated: (data) => api.post('/test-execution/automated', data)
};

// Evidence API
export const evidenceApi = {
  getAll: (params) => api.get('/evidence', { params }),
  getById: (id) => api.get(`/evidence/${id}`),
  create: (formData) => api.post('/evidence', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  update: (id, data) => api.put(`/evidence/${id}`, data),
  delete: (id) => api.delete(`/evidence/${id}`),
  getFile: (id) => api.get(`/evidence/${id}/file`, { responseType: 'blob' }),
  verify: (id) => api.get(`/evidence/${id}/verify`),
  collectApi: (data) => api.post('/evidence/collect/api', data),
  collectSystem: (data) => api.post('/evidence/collect/system', data)
};

// Reports API
export const reportsApi = {
  getAll: (params) => api.get('/reports', { params }),
  getById: (id) => api.get(`/reports/${id}`),
  generateCompliance: (data) => api.post('/reports/generate/compliance', data),
  generateTestResults: (data) => api.post('/reports/generate/test-results', data),
  generateEvidence: (data) => api.post('/reports/generate/evidence', data),
  generateControlEffectiveness: (data) => api.post('/reports/generate/control-effectiveness', data),
  download: (id) => api.get(`/reports/${id}/download`, { responseType: 'blob' }),
  share: (id, data) => api.post(`/reports/${id}/share`, data),
  schedule: (id, data) => api.post(`/reports/${id}/schedule`, data)
};

export default {
  controlsApi,
  testPlansApi,
  testExecutionApi,
  evidenceApi,
  reportsApi
};
