import ProductDiscoverer from '../utils/product-discovery';

async function discoverBestProducts() {
  try {
    const discoverer = new ProductDiscoverer();
    
    console.log('🔍 Starting product discovery...');
    
    // Get products from ClickBank
    const products = await discoverer.getClickBankProducts();
    
    // Process each product with our consciousness-based scoring
    const optimizedProducts = products.map(product => {
      const consciousnessScore = discoverer.calculateConsciousnessScore(product);
      const triadicScore = discoverer.calculateTriadicScore(product);
      const boostedScore = discoverer.applyKBoost(triadicScore);
      
      return {
        ...product,
        consciousness_score: consciousnessScore,
        triadic_score: triadicScore,
        boosted_score: boostedScore,
        is_valid: discoverer.validateEthics({
          ...product,
          consciousness_score: consciousnessScore
        })
      };
    });
    
    // Filter valid products and sort by score
    const validProducts = optimizedProducts
      .filter(p => p.is_valid)
      .sort((a, b) => b.boosted_score - a.boosted_score);
    
    console.log('\n🎯 Top Products Found:');
    validProducts.forEach((product, index) => {
      console.log(`\n${index + 1}. ${product.name}`);
      console.log(`- Price: $${product.price}`);
      console.log(`- Commission: $${product.commission}`);
      console.log(`- Consciousness Score: ${product.consciousness_score.toFixed(2)}`);
      console.log(`- Triadic Score: ${product.triadic_score.toFixed(2)}`);
      console.log(`- Boosted Score: ${product.boosted_score.toFixed(2)}`);
      console.log(`- Vendor: ${product.vendor}`);
      console.log(`- ClickBank Link: https://www.clickbank.com/${product.id}`);
    });
    
    console.log('\n✨ Product discovery complete!');
    
  } catch (error) {
    console.error('❌ Error discovering products:', error.message);
  }
}

// Run the discovery
discoverBestProducts();
