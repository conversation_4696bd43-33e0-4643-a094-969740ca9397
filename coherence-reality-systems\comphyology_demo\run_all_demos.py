"""Unified script to run all comphyology demos"""
import os
import sys
import subprocess

# Get current directory
current_dir = os.path.dirname(os.path.abspath(__file__))

def run_demo(script_name):
    """Run a demo script"""
    script_path = os.path.join(current_dir, script_name)
    try:
        result = subprocess.run([sys.executable, script_path], capture_output=True, text=True)
        print(f"=== {script_name} ===")
        print(result.stdout)
        if result.stderr:
            print(f"Errors: {result.stderr}", file=sys.stderr)
    except Exception as e:
        print(f"Error running {script_name}: {e}", file=sys.stderr)

if __name__ == "__main__":
    print("Running all comphyology demos...\n")
    run_demo("stage1_demo.py")
    run_demo("stage2_demo.py")
    run_demo("stage3_demo.py")
    run_demo("stage4_demo.py")
    print("\nAll demos completed!")
