graph TD
    %% Core Architecture
    A[Domain A] --⊗--> B[Domain B]
    B --⊕--> C[Domain C]
    C --π10³--> D[Unified Field Output]
    
    %% USPTO Compliance
    classDef uspto fill:#fff,stroke:#000,stroke-width:2px
    class A,B,C,D uspto
    
    %% Reference Numbers (100 series)
    A:::reference100
    B:::reference200
    C:::reference300
    D:::reference400
    
    %% Mathematical Annotations
    Math1[π10³ Scaling Factor]:::math
    Math2[⊗: Tensor Product]:::math
    Math3[⊕: Direct Sum]:::math
    
    %% Legend
    Legend[UUFT Core Architecture]:::legend
    
    %% Styling
    classDef reference100,reference200,reference300,reference400 fill:none,stroke:none,font-size:8pt
    classDef math fill:#f9f9f9,stroke:#ddd,stroke-dasharray: 5 5,font-size:8pt
    classDef legend fill:#f0f0f0,stroke:#ccc,stroke-width:1px
