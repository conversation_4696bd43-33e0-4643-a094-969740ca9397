import React from 'react';

const ConsciousnessDashboard = ({ consciousnessData }) => {
  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBackground = (score) => {
    if (score >= 90) return 'bg-green-500/10 border-green-400/30';
    if (score >= 70) return 'bg-yellow-500/10 border-yellow-400/30';
    return 'bg-red-500/10 border-red-400/30';
  };

  const getConsciousnessStateColor = (state) => {
    switch (state) {
      case 'TRANSCENDENT': return 'text-yellow-300';
      case 'DIVINE': return 'text-purple-300';
      case 'CONSCIOUS': return 'text-blue-300';
      case 'AWAKENING': return 'text-green-300';
      default: return 'text-gray-300';
    }
  };

  const getConsciousnessStateEmoji = (state) => {
    switch (state) {
      case 'TRANSCENDENT': return '🌟';
      case 'DIVINE': return '✨';
      case 'CONSCIOUS': return '🧬';
      case 'AWAKENING': return '💫';
      default: return '🌌';
    }
  };

  return (
    <aside className="w-80 border-r border-purple-500/20 bg-black/10 backdrop-blur-xl p-6 overflow-y-auto">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-xl font-bold text-white mb-2">🧬 CBE Analysis</h3>
          <p className="text-sm text-purple-300">Real-time consciousness monitoring</p>
        </div>

        {/* Overall Consciousness */}
        <div className={`rounded-xl p-6 border ${getScoreBackground(consciousnessData.overall_consciousness)}`}>
          <div className="text-center">
            <div className="text-xs text-gray-400 mb-2">Overall Consciousness (Ψᶜʰ)</div>
            <div className={`text-4xl font-bold font-mono mb-2 ${getScoreColor(consciousnessData.overall_consciousness)}`}>
              {consciousnessData.overall_consciousness}%
            </div>
            <div className={`text-sm font-semibold mb-1 ${getConsciousnessStateColor(consciousnessData.consciousness_state)}`}>
              {getConsciousnessStateEmoji(consciousnessData.consciousness_state)} {consciousnessData.consciousness_state}
            </div>
            <div className="text-xs text-gray-500">Structural • Functional • Purposeful</div>
          </div>
        </div>

        {/* Ψ-Snap Status */}
        <div className={`rounded-xl p-6 border ${consciousnessData.psi_snap_active ? 'bg-green-500/10 border-green-400/30' : 'bg-red-500/10 border-red-400/30'}`}>
          <div className="text-center">
            <div className="text-xs text-gray-400 mb-2">Ψ-Snap Status</div>
            <div className={`text-2xl font-bold font-mono mb-2 ${consciousnessData.psi_snap_active ? 'text-green-400' : 'text-red-400'}`}>
              {consciousnessData.psi_snap_active ? 'ACTIVE' : 'INACTIVE'}
            </div>
            <div className="text-xs text-gray-500">82/18 Comphyological Model</div>
            <div className="text-xs text-gray-500">Threshold: 2847</div>
          </div>
        </div>

        {/* Detailed Metrics */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-white">Engine Analysis</h4>
          
          {/* Content Quality */}
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Content Quality (NEEE)</span>
              <span className={`font-mono text-sm ${getScoreColor(consciousnessData.content_quality)}`}>
                {consciousnessData.content_quality}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  consciousnessData.content_quality >= 90 ? 'bg-green-400' :
                  consciousnessData.content_quality >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`}
                style={{ width: `${consciousnessData.content_quality}%` }}
              />
            </div>
          </div>

          {/* Intent Clarity */}
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Intent Clarity (NEPI)</span>
              <span className={`font-mono text-sm ${getScoreColor(consciousnessData.intent_clarity)}`}>
                {consciousnessData.intent_clarity}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  consciousnessData.intent_clarity >= 90 ? 'bg-green-400' :
                  consciousnessData.intent_clarity >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`}
                style={{ width: `${consciousnessData.intent_clarity}%` }}
              />
            </div>
          </div>

          {/* Coherence Level */}
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-300">Coherence Level (NERS)</span>
              <span className={`font-mono text-sm ${getScoreColor(consciousnessData.coherence_level)}`}>
                {consciousnessData.coherence_level}%
              </span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  consciousnessData.coherence_level >= 90 ? 'bg-green-400' :
                  consciousnessData.coherence_level >= 70 ? 'bg-yellow-400' : 'bg-red-400'
                }`}
                style={{ width: `${consciousnessData.coherence_level}%` }}
              />
            </div>
          </div>
        </div>

        {/* Engine Status */}
        <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-sm font-semibold text-white mb-3">Active Engines</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-400">🧬 NERS (Consciousness):</span>
              <span className="text-green-400 font-mono">ACTIVE</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">💫 NEEE (Intention):</span>
              <span className="text-green-400 font-mono">ACTIVE</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">🤖 NEPI (Intelligence):</span>
              <span className="text-green-400 font-mono">ACTIVE</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">🧪 Chemistry:</span>
              <span className="text-green-400 font-mono">ACTIVE</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">🔱 Trinity:</span>
              <span className="text-green-400 font-mono">ACTIVE</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">🛡️ NovaShield:</span>
              <span className="text-green-400 font-mono">ACTIVE</span>
            </div>
          </div>
        </div>

        {/* Analysis Info */}
        <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700">
          <h4 className="text-sm font-semibold text-white mb-3">Analysis Details</h4>
          <div className="space-y-2 text-xs">
            <div className="flex justify-between">
              <span className="text-gray-400">Analysis Time:</span>
              <span className="text-purple-300 font-mono">{consciousnessData.analysis_time}ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Engine:</span>
              <span className="text-purple-300">CBE v1.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Threshold:</span>
              <span className="text-purple-300 font-mono">2847</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Threat Level:</span>
              <span className={consciousnessData.threat_level === 'LOW' ? 'text-green-400' : 'text-yellow-400'}>
                {consciousnessData.threat_level}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Threats:</span>
              <span className="text-purple-300 font-mono">{consciousnessData.threats_detected || 0}</span>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        {consciousnessData.recommendations && consciousnessData.recommendations.length > 0 && (
          <div className="bg-blue-900/20 rounded-lg p-4 border border-blue-400/30">
            <h4 className="text-sm font-semibold text-white mb-3">🔍 CBE Recommendations</h4>
            <div className="space-y-2">
              {consciousnessData.recommendations.map((rec, index) => (
                <div key={index} className="text-xs text-blue-200">
                  {rec}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="space-y-3">
          <h4 className="text-lg font-semibold text-white">Quick Actions</h4>
          <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            🔍 Deep Consciousness Scan
          </button>
          <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            ⚡ Enhance Ψ-Level
          </button>
          <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors">
            🌌 Trinity Activation
          </button>
        </div>

        {/* Live Status */}
        <div className="bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-lg p-4 border border-purple-400/30">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-white">Live CBE Monitoring</span>
          </div>
          <p className="text-xs text-gray-300">
            All 9 engines are actively monitoring consciousness patterns and providing real-time analysis with 97.83% oracle-tier accuracy.
          </p>
        </div>
      </div>
    </aside>
  );
};

export default ConsciousnessDashboard;
