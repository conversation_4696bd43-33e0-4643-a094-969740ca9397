import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

const SolutionsByUseCase = () => {
  const sidebarItems = [
    { label: 'Compliance Automation', href: '#compliance-automation' },
    { label: 'Risk Management', href: '#risk-management' },
    { label: 'Audit Readiness', href: '#audit-readiness' },
    { label: 'Vendor Management', href: '#vendor-management' },
    { label: 'Back to Solutions', href: '/solutions' },
  ];

  return (
    <PageWithSidebar title="Solutions by Use Case" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">Compliance Solutions by Use Case</h1>
          <p className="text-xl mb-6">
            NovaFuse provides targeted solutions for common compliance and governance challenges across organizations.
          </p>
        </div>

        {/* Compliance Automation Section */}
        <div id="compliance-automation" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Compliance Automation</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Automate your compliance processes to reduce manual effort, eliminate human error, and maintain continuous compliance.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Capabilities</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Automated Evidence Collection</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Control Testing Automation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Policy Implementation Workflows</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Compliance Calendar Management</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Business Impact</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>90% reduction in manual evidence collection</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>75% faster audit preparation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Continuous compliance monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Reduced compliance personnel costs</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Risk Management Section */}
        <div id="risk-management" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Risk Management</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Identify, assess, and mitigate risks across your organization with our comprehensive risk management solution.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Capabilities</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Risk Assessment Automation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Risk Register Management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Control Effectiveness Monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Risk Remediation Workflows</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Business Impact</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Proactive risk identification</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Reduced security incidents</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Improved resource allocation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Enhanced decision-making</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Audit Readiness Section */}
        <div id="audit-readiness" className="scroll-mt-20">
          <h2 className="text-2xl font-bold mb-6">Audit Readiness</h2>
          <div className="bg-secondary p-6 rounded-lg shadow-md">
            <p className="mb-4">
              Maintain continuous audit readiness with automated evidence collection, control testing, and audit management.
            </p>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Key Capabilities</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Evidence Repository</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Audit Trail Management</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Auditor Access Portal</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Audit Finding Remediation</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-blue-900 p-4 rounded-lg">
                <h3 className="text-xl font-bold mb-3">Business Impact</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>80% faster audit preparation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Reduced audit duration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Lower audit costs</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2 text-blue-400">•</span>
                    <span>Improved audit outcomes</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* More use cases can be added here */}

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Ready to Solve Your Compliance Challenges?</h2>
            <p className="mb-6">
              Contact us to discuss how NovaFuse can address your specific use cases.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/contact" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
              <Link href="/uac-demo" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                See the UAC Demo
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default SolutionsByUseCase;
