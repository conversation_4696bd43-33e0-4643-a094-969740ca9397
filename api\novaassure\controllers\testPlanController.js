/**
 * Test Plan Controller
 * 
 * This controller handles API requests for test plan management.
 */

const testPlanService = require('../services/testPlanService');
const logger = require('../utils/logger');

/**
 * Get all test plans
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getAllTestPlans(req, res, next) {
  try {
    const { framework, status, search, page = 1, limit = 10 } = req.query;
    
    const filters = {};
    
    if (framework) {
      filters.framework = framework;
    }
    
    if (status) {
      filters.status = status;
    }
    
    if (search) {
      filters.search = search;
    }
    
    const result = await testPlanService.getAllTestPlans(
      filters,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error getting test plans', error);
    next(error);
  }
}

/**
 * Get test plan by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getTestPlanById(req, res, next) {
  try {
    const { id } = req.params;
    
    const testPlan = await testPlanService.getTestPlanById(id);
    
    res.json(testPlan);
  } catch (error) {
    logger.error(`Error getting test plan ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Create test plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function createTestPlan(req, res, next) {
  try {
    const testPlanData = req.body;
    const userId = req.user.id;
    
    const testPlan = await testPlanService.createTestPlan(testPlanData, userId);
    
    res.status(201).json(testPlan);
  } catch (error) {
    logger.error('Error creating test plan', error);
    next(error);
  }
}

/**
 * Update test plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function updateTestPlan(req, res, next) {
  try {
    const { id } = req.params;
    const testPlanData = req.body;
    const userId = req.user.id;
    
    const testPlan = await testPlanService.updateTestPlan(id, testPlanData, userId);
    
    res.json(testPlan);
  } catch (error) {
    logger.error(`Error updating test plan ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Delete test plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function deleteTestPlan(req, res, next) {
  try {
    const { id } = req.params;
    
    const testPlan = await testPlanService.deleteTestPlan(id);
    
    res.json({
      success: true,
      message: `Test plan ${id} deleted successfully`,
      testPlan
    });
  } catch (error) {
    logger.error(`Error deleting test plan ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Schedule test plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function scheduleTestPlan(req, res, next) {
  try {
    const { id } = req.params;
    const { schedule } = req.body;
    const userId = req.user.id;
    
    const testPlan = await testPlanService.scheduleTestPlan(id, schedule, userId);
    
    res.json(testPlan);
  } catch (error) {
    logger.error(`Error scheduling test plan ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Assign test plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function assignTestPlan(req, res, next) {
  try {
    const { id } = req.params;
    const { assignees } = req.body;
    const userId = req.user.id;
    
    const testPlan = await testPlanService.assignTestPlan(id, assignees, userId);
    
    res.json(testPlan);
  } catch (error) {
    logger.error(`Error assigning test plan ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Clone test plan
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function cloneTestPlan(req, res, next) {
  try {
    const { id } = req.params;
    const { name } = req.body;
    const userId = req.user.id;
    
    const testPlan = await testPlanService.cloneTestPlan(id, name, userId);
    
    res.status(201).json(testPlan);
  } catch (error) {
    logger.error(`Error cloning test plan ${req.params.id}`, error);
    next(error);
  }
}

module.exports = {
  getAllTestPlans,
  getTestPlanById,
  createTestPlan,
  updateTestPlan,
  deleteTestPlan,
  scheduleTestPlan,
  assignTestPlan,
  cloneTestPlan
};
