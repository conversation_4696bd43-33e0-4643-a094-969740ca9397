/**
 * Validation Error Handler
 * 
 * This utility provides error handling functions for data validation.
 */

const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const errorHandlingService = require('../services/ErrorHandlingService');
const logger = require('./logger');

// Create Ajv instance
const ajv = new Ajv({ allErrors: true, coerceTypes: true, verbose: true });
addFormats(ajv);

/**
 * Handle validation errors
 * @param {Error} error - Error object
 * @param {Object} context - Error context
 * @returns {Object} - Error response
 */
const handleValidationError = (error, context = {}) => {
  // Create validation-specific error
  const validationError = createValidationError(error, context);
  
  // Handle the error using the error handling service
  return errorHandlingService.handleError(validationError, context);
};

/**
 * Create a validation-specific error
 * @param {Error} error - Original error
 * @param {Object} context - Error context
 * @returns {Error} - Validation error
 */
const createValidationError = (error, context = {}) => {
  const { schema, data, path } = context;
  
  // Create a new error with validation-specific information
  const validationError = new Error(
    `Validation error: ${path ? path + ' - ' : ''}${error.message}`
  );
  
  // Copy properties from the original error
  Object.assign(validationError, error);
  
  // Set validation-specific properties
  validationError.name = 'ValidationError';
  validationError.schema = schema;
  validationError.data = data;
  validationError.path = path;
  validationError.originalError = error;
  
  return validationError;
};

/**
 * Validate data against a JSON schema
 * @param {Object} schema - JSON schema
 * @param {Object} data - Data to validate
 * @param {Object} options - Validation options
 * @returns {Object} - Validation result
 */
const validateSchema = (schema, data, options = {}) => {
  // Compile schema
  const validate = ajv.compile(schema);
  
  // Validate data
  const valid = validate(data);
  
  if (!valid) {
    // Format validation errors
    const errors = formatValidationErrors(validate.errors, options);
    
    // Create validation error
    const error = new Error('Schema validation failed');
    error.name = 'ValidationError';
    error.errors = errors;
    
    // Create context
    const context = {
      schema,
      data,
      path: options.path
    };
    
    // Handle validation error
    return {
      valid: false,
      errors,
      error: handleValidationError(error, context)
    };
  }
  
  return {
    valid: true,
    data
  };
};

/**
 * Format validation errors
 * @param {Array} errors - Ajv errors
 * @param {Object} options - Formatting options
 * @returns {Array} - Formatted errors
 */
const formatValidationErrors = (errors, options = {}) => {
  return errors.map(error => {
    // Format error path
    const path = error.instancePath.replace(/^\//, '').replace(/\//g, '.');
    
    // Format error message
    let message = error.message;
    
    // Add property name to message
    if (path) {
      message = `${path} ${message}`;
    }
    
    // Add additional information based on error keyword
    switch (error.keyword) {
      case 'required':
        message = `Missing required property: ${error.params.missingProperty}`;
        break;
      case 'type':
        message = `${path} must be a ${error.params.type}`;
        break;
      case 'enum':
        message = `${path} must be one of: ${error.params.allowedValues.join(', ')}`;
        break;
      case 'format':
        message = `${path} must be a valid ${error.params.format}`;
        break;
      case 'minimum':
        message = `${path} must be >= ${error.params.limit}`;
        break;
      case 'maximum':
        message = `${path} must be <= ${error.params.limit}`;
        break;
      case 'minLength':
        message = `${path} must be at least ${error.params.limit} characters`;
        break;
      case 'maxLength':
        message = `${path} must be at most ${error.params.limit} characters`;
        break;
      case 'pattern':
        message = `${path} must match pattern: ${error.params.pattern}`;
        break;
    }
    
    return {
      path: path || '',
      property: error.params.missingProperty || path.split('.').pop() || '',
      message,
      keyword: error.keyword,
      params: error.params,
      schemaPath: error.schemaPath
    };
  });
};

/**
 * Create a validation middleware for Express
 * @param {Object} schema - JSON schema
 * @param {Object} options - Validation options
 * @returns {Function} - Express middleware
 */
const validationMiddleware = (schema, options = {}) => {
  const { path = 'body', errorHandler = true } = options;
  
  return (req, res, next) => {
    // Get data to validate
    const data = req[path];
    
    // Validate data
    const result = validateSchema(schema, data, { path });
    
    if (!result.valid) {
      if (errorHandler) {
        // Send validation error response
        return res.status(400).json(result.error);
      } else {
        // Create validation error
        const error = new Error('Schema validation failed');
        error.name = 'ValidationError';
        error.errors = result.errors;
        
        // Pass error to next middleware
        return next(error);
      }
    }
    
    // Update request data with validated data
    req[path] = result.data;
    
    next();
  };
};

/**
 * Create a validation function with error handling
 * @param {Function} fn - Validation function to wrap
 * @param {Object} options - Options
 * @returns {Function} - Wrapped function
 */
const withValidationErrorHandling = (fn, options = {}) => {
  const { path } = options;
  
  // Create wrapped function
  return async (...args) => {
    try {
      return await fn(...args);
    } catch (error) {
      // Create context
      const context = {
        path,
        data: args[0]
      };
      
      throw createValidationError(error, context);
    }
  };
};

module.exports = {
  handleValidationError,
  createValidationError,
  validateSchema,
  formatValidationErrors,
  validationMiddleware,
  withValidationErrorHandling
};
