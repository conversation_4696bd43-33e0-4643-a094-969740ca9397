import axios from 'axios';

// API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

/**
 * Execute a connector
 * @param {string} connectorId - ID of the connector to execute
 * @param {Object} parameters - Parameters for connector execution
 * @returns {Promise<Object>} - Execution results
 */
export const executeConnector = async (connectorId, parameters = {}) => {
  try {
    const response = await axios.post(`${API_URL}/execution/connectors/${connectorId}/execute`, { parameters });
    return response.data;
  } catch (error) {
    console.error('Error executing connector:', error);
    throw error;
  }
};

/**
 * Schedule a connector for execution
 * @param {string} connectorId - ID of the connector to schedule
 * @param {Object} parameters - Parameters for connector execution
 * @param {Date|string} scheduledTime - Time to execute the connector
 * @returns {Promise<Object>} - Scheduled job information
 */
export const scheduleConnector = async (connectorId, parameters = {}, scheduledTime) => {
  try {
    const response = await axios.post(`${API_URL}/execution/connectors/${connectorId}/schedule`, { 
      parameters, 
      scheduledTime 
    });
    return response.data;
  } catch (error) {
    console.error('Error scheduling connector:', error);
    throw error;
  }
};

/**
 * Cancel a scheduled connector execution
 * @param {string} jobId - ID of the scheduled job
 * @returns {Promise<Object>} - Cancellation result
 */
export const cancelScheduledConnector = async (jobId) => {
  try {
    const response = await axios.delete(`${API_URL}/execution/schedule/${jobId}`);
    return response.data;
  } catch (error) {
    console.error('Error cancelling scheduled connector:', error);
    throw error;
  }
};

/**
 * Get execution history for a connector
 * @param {string} connectorId - ID of the connector
 * @param {number} page - Page number for pagination
 * @param {number} limit - Number of items per page
 * @returns {Promise<Object>} - Execution history
 */
export const getExecutionHistory = async (connectorId, page = 1, limit = 10) => {
  try {
    const response = await axios.get(`${API_URL}/execution/connectors/${connectorId}/history`, {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    console.error('Error getting execution history:', error);
    throw error;
  }
};
