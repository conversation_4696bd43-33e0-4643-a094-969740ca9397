/**
 * Message Handlers
 * 
 * This module provides message handlers for processing messages between components.
 */

/**
 * Base message handler
 */
class BaseMessageHandler {
  /**
   * Create a new BaseMessageHandler instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      ...options
    };
    
    if (this.options.enableLogging) {
      console.log(`${this.constructor.name} initialized`);
    }
  }
  
  /**
   * Handle a message
   * @param {Object} message - Message to handle
   * @param {string} channel - Channel the message was received on
   * @returns {Promise<Object>} - Promise that resolves with the result
   */
  async handleMessage(message, channel) {
    throw new Error('handleMessage must be implemented by subclasses');
  }
  
  /**
   * Validate a message
   * @param {Object} message - Message to validate
   * @param {Object} schema - Message schema
   * @returns {boolean} - Whether the message is valid
   */
  validateMessage(message, schema) {
    if (!message) {
      return false;
    }
    
    // Check required fields
    for (const field of schema.required || []) {
      if (message[field] === undefined) {
        return false;
      }
    }
    
    // Check field types
    for (const [field, type] of Object.entries(schema.types || {})) {
      if (message[field] !== undefined) {
        if (type === 'string' && typeof message[field] !== 'string') {
          return false;
        } else if (type === 'number' && typeof message[field] !== 'number') {
          return false;
        } else if (type === 'boolean' && typeof message[field] !== 'boolean') {
          return false;
        } else if (type === 'object' && (typeof message[field] !== 'object' || message[field] === null)) {
          return false;
        } else if (type === 'array' && !Array.isArray(message[field])) {
          return false;
        }
      }
    }
    
    return true;
  }
}

/**
 * Tensor message handler
 */
class TensorMessageHandler extends BaseMessageHandler {
  /**
   * Create a new TensorMessageHandler instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super(options);
    
    this.tensorAdapter = options.tensorAdapter;
    
    if (!this.tensorAdapter) {
      throw new Error('tensorAdapter is required');
    }
  }
  
  /**
   * Handle a message
   * @param {Object} message - Message to handle
   * @param {string} channel - Channel the message was received on
   * @returns {Promise<Object>} - Promise that resolves with the result
   */
  async handleMessage(message, channel) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['type'],
      types: {
        type: 'string'
      }
    })) {
      throw new Error('Invalid message format');
    }
    
    // Handle message based on type
    switch (message.type) {
      case 'register-tensor':
        return this._handleRegisterTensor(message);
        
      case 'get-tensor':
        return this._handleGetTensor(message);
        
      case 'update-tensor':
        return this._handleUpdateTensor(message);
        
      case 'heal-tensor':
        return this._handleHealTensor(message);
        
      case 'damage-tensor':
        return this._handleDamageTensor(message);
        
      case 'get-healing-history':
        return this._handleGetHealingHistory(message);
        
      default:
        throw new Error(`Unknown message type: ${message.type}`);
    }
  }
  
  /**
   * Handle register tensor message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleRegisterTensor(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id', 'tensor'],
      types: {
        id: 'string',
        tensor: 'object',
        domain: 'string'
      }
    })) {
      throw new Error('Invalid register-tensor message format');
    }
    
    // Register tensor
    const tensor = await this.tensorAdapter.registerTensor(
      message.id,
      message.tensor,
      message.domain || 'universal'
    );
    
    return {
      type: 'tensor-registered',
      id: message.id,
      tensor
    };
  }
  
  /**
   * Handle get tensor message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleGetTensor(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id'],
      types: {
        id: 'string'
      }
    })) {
      throw new Error('Invalid get-tensor message format');
    }
    
    // Get tensor
    const tensor = await this.tensorAdapter.getTensor(message.id);
    
    if (!tensor) {
      throw new Error(`Tensor not found: ${message.id}`);
    }
    
    return {
      type: 'tensor',
      id: message.id,
      tensor
    };
  }
  
  /**
   * Handle update tensor message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleUpdateTensor(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id', 'tensor'],
      types: {
        id: 'string',
        tensor: 'object'
      }
    })) {
      throw new Error('Invalid update-tensor message format');
    }
    
    // Update tensor
    const tensor = await this.tensorAdapter.updateTensor(
      message.id,
      message.tensor
    );
    
    return {
      type: 'tensor-updated',
      id: message.id,
      tensor
    };
  }
  
  /**
   * Handle heal tensor message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleHealTensor(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id'],
      types: {
        id: 'string'
      }
    })) {
      throw new Error('Invalid heal-tensor message format');
    }
    
    // Heal tensor
    const result = await this.tensorAdapter.healTensor(message.id);
    
    return {
      type: 'tensor-healed',
      id: message.id,
      tensor: result.tensor,
      diagnostics: result.diagnostics
    };
  }
  
  /**
   * Handle damage tensor message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleDamageTensor(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id', 'damageLevel'],
      types: {
        id: 'string',
        damageLevel: 'number'
      }
    })) {
      throw new Error('Invalid damage-tensor message format');
    }
    
    // Damage tensor
    const tensor = await this.tensorAdapter.tensor.damageTensor(
      message.id,
      message.damageLevel
    );
    
    return {
      type: 'tensor-damaged',
      id: message.id,
      tensor
    };
  }
  
  /**
   * Handle get healing history message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleGetHealingHistory(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id'],
      types: {
        id: 'string'
      }
    })) {
      throw new Error('Invalid get-healing-history message format');
    }
    
    // Get healing history
    const history = await this.tensorAdapter.getHealingHistory(message.id);
    
    return {
      type: 'healing-history',
      id: message.id,
      history
    };
  }
}

/**
 * Visualization message handler
 */
class VisualizationMessageHandler extends BaseMessageHandler {
  /**
   * Create a new VisualizationMessageHandler instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super(options);
    
    this.visualizationAdapter = options.visualizationAdapter;
    
    if (!this.visualizationAdapter) {
      throw new Error('visualizationAdapter is required');
    }
  }
  
  /**
   * Handle a message
   * @param {Object} message - Message to handle
   * @param {string} channel - Channel the message was received on
   * @returns {Promise<Object>} - Promise that resolves with the result
   */
  async handleMessage(message, channel) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['type'],
      types: {
        type: 'string'
      }
    })) {
      throw new Error('Invalid message format');
    }
    
    // Handle message based on type
    switch (message.type) {
      case 'get-visualization-types':
        return this._handleGetVisualizationTypes(message);
        
      case 'create-visualization':
        return this._handleCreateVisualization(message);
        
      case 'update-visualization':
        return this._handleUpdateVisualization(message);
        
      case 'delete-visualization':
        return this._handleDeleteVisualization(message);
        
      default:
        throw new Error(`Unknown message type: ${message.type}`);
    }
  }
  
  /**
   * Handle get visualization types message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleGetVisualizationTypes(message) {
    // Get visualization types
    const types = this.visualizationAdapter.getVisualizationTypes();
    
    return {
      type: 'visualization-types',
      types
    };
  }
  
  /**
   * Handle create visualization message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleCreateVisualization(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['visualizationType', 'data'],
      types: {
        visualizationType: 'string',
        data: 'object',
        options: 'object'
      }
    })) {
      throw new Error('Invalid create-visualization message format');
    }
    
    // Create visualization
    const visualization = await this.visualizationAdapter.createVisualization(
      message.visualizationType,
      message.data,
      message.options || {}
    );
    
    return {
      type: 'visualization-created',
      id: visualization.id,
      visualization
    };
  }
  
  /**
   * Handle update visualization message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleUpdateVisualization(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id', 'data'],
      types: {
        id: 'string',
        data: 'object'
      }
    })) {
      throw new Error('Invalid update-visualization message format');
    }
    
    // Update visualization
    const visualization = await this.visualizationAdapter.updateVisualization(
      message.id,
      message.data
    );
    
    return {
      type: 'visualization-updated',
      id: message.id,
      visualization
    };
  }
  
  /**
   * Handle delete visualization message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleDeleteVisualization(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id'],
      types: {
        id: 'string'
      }
    })) {
      throw new Error('Invalid delete-visualization message format');
    }
    
    // Delete visualization
    const success = await this.visualizationAdapter.deleteVisualization(message.id);
    
    return {
      type: 'visualization-deleted',
      id: message.id,
      success
    };
  }
}

/**
 * Analytics message handler
 */
class AnalyticsMessageHandler extends BaseMessageHandler {
  /**
   * Create a new AnalyticsMessageHandler instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super(options);
    
    this.analyticsAdapter = options.analyticsAdapter;
    
    if (!this.analyticsAdapter) {
      throw new Error('analyticsAdapter is required');
    }
  }
  
  /**
   * Handle a message
   * @param {Object} message - Message to handle
   * @param {string} channel - Channel the message was received on
   * @returns {Promise<Object>} - Promise that resolves with the result
   */
  async handleMessage(message, channel) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['type'],
      types: {
        type: 'string'
      }
    })) {
      throw new Error('Invalid message format');
    }
    
    // Handle message based on type
    switch (message.type) {
      case 'get-metrics':
        return this._handleGetMetrics(message);
        
      case 'get-dashboards':
        return this._handleGetDashboards(message);
        
      case 'get-dashboard':
        return this._handleGetDashboard(message);
        
      case 'execute-query':
        return this._handleExecuteQuery(message);
        
      default:
        throw new Error(`Unknown message type: ${message.type}`);
    }
  }
  
  /**
   * Handle get metrics message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleGetMetrics(message) {
    // Get metrics
    const metrics = this.analyticsAdapter.getMetrics();
    
    return {
      type: 'metrics',
      metrics
    };
  }
  
  /**
   * Handle get dashboards message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleGetDashboards(message) {
    // Get dashboards
    const dashboards = this.analyticsAdapter.getDashboards();
    
    return {
      type: 'dashboards',
      dashboards
    };
  }
  
  /**
   * Handle get dashboard message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleGetDashboard(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['id'],
      types: {
        id: 'string'
      }
    })) {
      throw new Error('Invalid get-dashboard message format');
    }
    
    // Get dashboard
    const dashboard = this.analyticsAdapter.getDashboard(message.id);
    
    if (!dashboard) {
      throw new Error(`Dashboard not found: ${message.id}`);
    }
    
    return {
      type: 'dashboard',
      id: message.id,
      dashboard
    };
  }
  
  /**
   * Handle execute query message
   * @param {Object} message - Message to handle
   * @returns {Promise<Object>} - Promise that resolves with the result
   * @private
   */
  async _handleExecuteQuery(message) {
    // Validate message
    if (!this.validateMessage(message, {
      required: ['query'],
      types: {
        query: 'string',
        params: 'object'
      }
    })) {
      throw new Error('Invalid execute-query message format');
    }
    
    // Execute query
    const result = await this.analyticsAdapter.executeQuery(
      message.query,
      message.params || {}
    );
    
    return {
      type: 'query-result',
      query: message.query,
      result
    };
  }
}

module.exports = {
  BaseMessageHandler,
  TensorMessageHandler,
  VisualizationMessageHandler,
  AnalyticsMessageHandler
};
