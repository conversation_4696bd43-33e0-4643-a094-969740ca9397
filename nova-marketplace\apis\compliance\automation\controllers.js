/**
 * Compliance Automation API - Controllers
 * 
 * This file defines the controllers for the Compliance Automation API.
 */

const { AutomationRule, AutomationExecution, Workflow, WorkflowInstance } = require('./models');
const logger = require('../../../utils/logger');

/**
 * Automation Rule Controllers
 */
const automationRuleController = {
  /**
   * Get all automation rules
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllAutomationRules: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', type, status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (type) filter.type = type;
      if (status) filter.status = status;
      
      const automationRules = await AutomationRule.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit);
      
      const total = await AutomationRule.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: automationRules,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting automation rules: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting automation rules'
      });
    }
  },
  
  /**
   * Get automation rule by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAutomationRuleById: async (req, res) => {
    try {
      const automationRule = await AutomationRule.findById(req.params.id);
      
      if (!automationRule) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Automation rule not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: automationRule
      });
    } catch (error) {
      logger.error(`Error getting automation rule: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the automation rule'
      });
    }
  },
  
  /**
   * Create a new automation rule
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createAutomationRule: async (req, res) => {
    try {
      const automationRule = new AutomationRule({
        ...req.body,
        owner: req.user.id
      });
      
      await automationRule.save();
      
      return res.status(201).json({
        success: true,
        data: automationRule,
        message: 'Automation rule created successfully'
      });
    } catch (error) {
      logger.error(`Error creating automation rule: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the automation rule'
      });
    }
  },
  
  /**
   * Update an automation rule
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateAutomationRule: async (req, res) => {
    try {
      const automationRule = await AutomationRule.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!automationRule) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Automation rule not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: automationRule,
        message: 'Automation rule updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating automation rule: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the automation rule'
      });
    }
  },
  
  /**
   * Delete an automation rule
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteAutomationRule: async (req, res) => {
    try {
      const automationRule = await AutomationRule.findByIdAndDelete(req.params.id);
      
      if (!automationRule) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Automation rule not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Automation rule deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting automation rule: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the automation rule'
      });
    }
  }
};

/**
 * Workflow Controllers
 */
const workflowController = {
  /**
   * Get all workflows
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllWorkflows: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', type, status } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      // Build filter object
      const filter = {};
      if (type) filter.type = type;
      if (status) filter.status = status;
      
      const workflows = await Workflow.find(filter)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit);
      
      const total = await Workflow.countDocuments(filter);
      
      return res.status(200).json({
        success: true,
        data: workflows,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting workflows: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting workflows'
      });
    }
  },
  
  /**
   * Get workflow by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getWorkflowById: async (req, res) => {
    try {
      const workflow = await Workflow.findById(req.params.id);
      
      if (!workflow) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Workflow not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: workflow
      });
    } catch (error) {
      logger.error(`Error getting workflow: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the workflow'
      });
    }
  },
  
  /**
   * Create a new workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createWorkflow: async (req, res) => {
    try {
      const workflow = new Workflow({
        ...req.body,
        owner: req.user.id
      });
      
      await workflow.save();
      
      return res.status(201).json({
        success: true,
        data: workflow,
        message: 'Workflow created successfully'
      });
    } catch (error) {
      logger.error(`Error creating workflow: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the workflow'
      });
    }
  },
  
  /**
   * Update a workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateWorkflow: async (req, res) => {
    try {
      const workflow = await Workflow.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!workflow) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Workflow not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: workflow,
        message: 'Workflow updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating workflow: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the workflow'
      });
    }
  },
  
  /**
   * Delete a workflow
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteWorkflow: async (req, res) => {
    try {
      const workflow = await Workflow.findByIdAndDelete(req.params.id);
      
      if (!workflow) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Workflow not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Workflow deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting workflow: ${error.message}`, { service: 'compliance-automation-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the workflow'
      });
    }
  }
};

module.exports = {
  automationRuleController,
  workflowController
};
