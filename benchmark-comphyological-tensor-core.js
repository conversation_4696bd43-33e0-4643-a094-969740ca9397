/**
 * Comphyological Tensor Core Benchmark
 *
 * This script benchmarks the performance of the Comphyological Tensor Core
 * with different configurations and input sizes.
 */

const { performance } = require('perf_hooks');
const { createComphyologicalTensorCore } = require('./src/quantum/tensor');
const { createResonanceListener } = require('./src/quantum/resonance');
const { createUnifiedAdapter } = require('./src/quantum/adapters');

// Benchmark configuration
const ITERATIONS = 100;
const WARMUP_ITERATIONS = 10;
const INPUT_SIZES = [
  { name: 'Small', scale: 1 },
  { name: 'Medium', scale: 10 },
  { name: 'Large', scale: 100 }
];
const CONFIGURATIONS = [
  { name: 'CPU', useGPU: false, useDynamicWeighting: false },
  { name: 'CPU + Dynamic Weighting', useGPU: false, useDynamicWeighting: true },
  { name: 'GPU', useGPU: true, useDynamicWeighting: false },
  { name: 'GPU + Dynamic Weighting', useGPU: true, useDynamicWeighting: true }
];

/**
 * Generate test data
 * @param {number} scale - Scale factor for data size
 * @returns {Object} Test data
 */
function generateTestData(scale) {
  const baseData = {
    csdeData: {
      governance: 0.8,
      dataQuality: 0.7,
      action: 'allow',
      confidence: 0.9
    },
    csfeData: {
      risk: 0.3,
      policyCompliance: 0.6,
      action: 'monitor',
      confidence: 0.8
    },
    csmeData: {
      trustFactor: 0.5,
      integrityFactor: 0.6,
      action: 'alert',
      confidence: 0.7
    }
  };

  // For larger scales, add more data
  if (scale > 1) {
    // Add additional fields to CSDE data
    for (let i = 0; i < scale; i++) {
      baseData.csdeData[`field_${i}`] = Math.random();
    }

    // Add additional fields to CSFE data
    for (let i = 0; i < scale; i++) {
      baseData.csfeData[`field_${i}`] = Math.random();
    }

    // Add additional fields to CSME data
    for (let i = 0; i < scale; i++) {
      baseData.csmeData[`field_${i}`] = Math.random();
    }
  }

  return baseData;
}

/**
 * Run benchmark for a specific configuration and input size
 * @param {Object} config - Configuration
 * @param {Object} inputSize - Input size
 * @returns {Object} Benchmark results
 */
function runBenchmark(config, inputSize) {
  console.log(`Running benchmark: ${config.name} with ${inputSize.name} input...`);

  // Create tensor core
  const tensorCore = createComphyologicalTensorCore({
    enableLogging: false,
    strictMode: false,
    useGPU: config.useGPU,
    useDynamicWeighting: config.useDynamicWeighting,
    precision: 6,
    normalizationFactor: 166000
  });

  // Create resonance listener
  const resonanceListener = createResonanceListener({
    enableLogging: false,
    targetFrequency: 396,
    precisionFFT: 0.001,
    quantumVacuumNoise: true,
    crossDomainPhaseAlignment: true,
    silenceThreshold: 0.001,
    detectionInterval: 100,
    maxHistoryLength: 100
  });

  // Start resonance listener
  resonanceListener.startListening(tensorCore);

  // Create unified adapter
  const unifiedAdapter = createUnifiedAdapter({}, {
    enableLogging: false,
    strictMode: false,
    useGPU: config.useGPU,
    useDynamicWeighting: config.useDynamicWeighting,
    precision: 6,
    normalizationFactor: 166000
  });

  // Generate test data
  const testData = generateTestData(inputSize.scale);

  // Warm up
  for (let i = 0; i < WARMUP_ITERATIONS; i++) {
    tensorCore.processData(
      testData.csdeData,
      testData.csfeData,
      testData.csmeData
    );

    unifiedAdapter.processData(testData);
  }

  // Benchmark tensor core
  const tensorCoreResults = [];
  for (let i = 0; i < ITERATIONS; i++) {
    const startTime = performance.now();

    const result = tensorCore.processData(
      testData.csdeData,
      testData.csfeData,
      testData.csmeData
    );

    const endTime = performance.now();
    tensorCoreResults.push({
      time: endTime - startTime,
      comphyon: result.comphyon,
      action: result.action,
      confidence: result.confidence
    });
  }

  // Benchmark unified adapter
  const unifiedAdapterResults = [];
  for (let i = 0; i < ITERATIONS; i++) {
    const startTime = performance.now();

    const result = unifiedAdapter.processData(testData);

    const endTime = performance.now();
    unifiedAdapterResults.push({
      time: endTime - startTime,
      comphyon: result.comphyon,
      action: result.action,
      confidence: result.confidence
    });
  }

  // Calculate statistics
  const tensorCoreStats = calculateStats(tensorCoreResults.map(r => r.time));
  const unifiedAdapterStats = calculateStats(unifiedAdapterResults.map(r => r.time));

  return {
    config: config.name,
    inputSize: inputSize.name,
    tensorCore: {
      mean: tensorCoreStats.mean,
      median: tensorCoreStats.median,
      min: tensorCoreStats.min,
      max: tensorCoreStats.max,
      stdDev: tensorCoreStats.stdDev
    },
    unifiedAdapter: {
      mean: unifiedAdapterStats.mean,
      median: unifiedAdapterStats.median,
      min: unifiedAdapterStats.min,
      max: unifiedAdapterStats.max,
      stdDev: unifiedAdapterStats.stdDev
    }
  };
}

/**
 * Calculate statistics for an array of values
 * @param {Array} values - Array of values
 * @returns {Object} Statistics
 */
function calculateStats(values) {
  // Sort values for median and percentiles
  const sortedValues = [...values].sort((a, b) => a - b);

  // Calculate mean
  const sum = values.reduce((acc, val) => acc + val, 0);
  const mean = sum / values.length;

  // Calculate median
  const median = sortedValues[Math.floor(values.length / 2)];

  // Calculate min and max
  const min = sortedValues[0];
  const max = sortedValues[values.length - 1];

  // Calculate standard deviation
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / values.length;
  const stdDev = Math.sqrt(variance);

  return {
    mean,
    median,
    min,
    max,
    stdDev
  };
}

/**
 * Format time in milliseconds
 * @param {number} time - Time in milliseconds
 * @returns {string} Formatted time
 */
function formatTime(time) {
  if (time < 1) {
    return `${(time * 1000).toFixed(2)} µs`;
  } else {
    return `${time.toFixed(2)} ms`;
  }
}

/**
 * Run all benchmarks
 */
async function runAllBenchmarks() {
  console.log('=== Comphyological Tensor Core Benchmark ===');
  console.log(`Iterations: ${ITERATIONS}`);
  console.log(`Warmup Iterations: ${WARMUP_ITERATIONS}`);
  console.log('');

  const results = [];

  // Run benchmarks for all configurations and input sizes
  for (const config of CONFIGURATIONS) {
    for (const inputSize of INPUT_SIZES) {
      const result = runBenchmark(config, inputSize);
      results.push(result);

      // Print results
      console.log(`\nResults for ${config.name} with ${inputSize.name} input:`);
      console.log('Tensor Core:');
      console.log(`  Mean: ${formatTime(result.tensorCore.mean)}`);
      console.log(`  Median: ${formatTime(result.tensorCore.median)}`);
      console.log(`  Min: ${formatTime(result.tensorCore.min)}`);
      console.log(`  Max: ${formatTime(result.tensorCore.max)}`);
      console.log(`  Std Dev: ${formatTime(result.tensorCore.stdDev)}`);

      console.log('Unified Adapter:');
      console.log(`  Mean: ${formatTime(result.unifiedAdapter.mean)}`);
      console.log(`  Median: ${formatTime(result.unifiedAdapter.median)}`);
      console.log(`  Min: ${formatTime(result.unifiedAdapter.min)}`);
      console.log(`  Max: ${formatTime(result.unifiedAdapter.max)}`);
      console.log(`  Std Dev: ${formatTime(result.unifiedAdapter.stdDev)}`);
    }
  }

  // Print summary
  console.log('\n=== Summary ===');
  console.log('Configuration | Input Size | Tensor Core | Unified Adapter');
  console.log('------------- | ---------- | ----------- | ---------------');

  for (const result of results) {
    console.log(`${result.config} | ${result.inputSize} | ${formatTime(result.tensorCore.mean)} | ${formatTime(result.unifiedAdapter.mean)}`);
  }

  // Calculate speedup
  const baselineResult = results.find(r => r.config === 'CPU' && r.inputSize === 'Medium');
  const gpuResult = results.find(r => r.config === 'GPU' && r.inputSize === 'Medium');

  if (baselineResult && gpuResult) {
    const speedup = baselineResult.tensorCore.mean / gpuResult.tensorCore.mean;
    console.log(`\nGPU Speedup (Medium input): ${speedup.toFixed(2)}x`);
  }

  console.log('\n=== Benchmark Complete ===');
}

// Run all benchmarks
runAllBenchmarks().catch(error => {
  console.error('Error running benchmarks:', error);
});
