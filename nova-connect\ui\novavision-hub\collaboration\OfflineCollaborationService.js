/**
 * Offline Collaboration Service
 * 
 * This module provides offline collaboration functionality for the NovaVision Hub.
 */

import { v4 as uuidv4 } from 'uuid';
import offlineManager from '../offline/OfflineManager';

/**
 * Offline Collaboration Service class
 */
class OfflineCollaborationService {
  /**
   * Constructor
   */
  constructor() {
    this.rooms = new Map();
    this.users = new Map();
    this.messages = new Map();
    this.cursors = new Map();
    this.sharedStates = new Map();
    this.pendingChanges = [];
    this.syncInProgress = false;
    this.localUser = null;
    this.listeners = {
      message: [],
      cursorMove: [],
      stateChange: [],
      userJoin: [],
      userLeave: [],
      sync: []
    };
    
    // Initialize
    this.init();
  }
  
  /**
   * Initialize offline collaboration service
   */
  async init() {
    // Load data from offline storage
    await this.loadOfflineData();
    
    // Generate local user if not exists
    if (!this.localUser) {
      this.localUser = {
        id: uuidv4(),
        name: 'Local User',
        color: this.getRandomColor(),
        isLocal: true
      };
      
      // Save local user
      await offlineManager.saveOfflineData('collaboration:localUser', this.localUser);
    }
  }
  
  /**
   * Load offline data
   */
  async loadOfflineData() {
    try {
      // Load local user
      const localUser = await offlineManager.getOfflineData('collaboration:localUser');
      if (localUser) {
        this.localUser = localUser;
      }
      
      // Load rooms
      const rooms = await offlineManager.getOfflineData('collaboration:rooms');
      if (rooms) {
        this.rooms = new Map(rooms.map(room => [room.id, room]));
      }
      
      // Load users
      const users = await offlineManager.getOfflineData('collaboration:users');
      if (users) {
        this.users = new Map(users.map(user => [user.id, user]));
      }
      
      // Load messages
      const messages = await offlineManager.getOfflineData('collaboration:messages');
      if (messages) {
        this.messages = new Map();
        for (const [roomId, roomMessages] of Object.entries(messages)) {
          this.messages.set(roomId, roomMessages);
        }
      }
      
      // Load cursors
      const cursors = await offlineManager.getOfflineData('collaboration:cursors');
      if (cursors) {
        this.cursors = new Map();
        for (const [roomId, roomCursors] of Object.entries(cursors)) {
          this.cursors.set(roomId, new Map(Object.entries(roomCursors)));
        }
      }
      
      // Load shared states
      const sharedStates = await offlineManager.getOfflineData('collaboration:sharedStates');
      if (sharedStates) {
        this.sharedStates = new Map();
        for (const [roomId, roomState] of Object.entries(sharedStates)) {
          this.sharedStates.set(roomId, roomState);
        }
      }
      
      // Load pending changes
      const pendingChanges = await offlineManager.getOfflineData('collaboration:pendingChanges');
      if (pendingChanges) {
        this.pendingChanges = pendingChanges;
      }
    } catch (error) {
      console.error('Failed to load offline collaboration data:', error);
    }
  }
  
  /**
   * Save offline data
   */
  async saveOfflineData() {
    try {
      // Save rooms
      await offlineManager.saveOfflineData('collaboration:rooms', Array.from(this.rooms.values()));
      
      // Save users
      await offlineManager.saveOfflineData('collaboration:users', Array.from(this.users.values()));
      
      // Save messages
      const messages = {};
      for (const [roomId, roomMessages] of this.messages.entries()) {
        messages[roomId] = roomMessages;
      }
      await offlineManager.saveOfflineData('collaboration:messages', messages);
      
      // Save cursors
      const cursors = {};
      for (const [roomId, roomCursors] of this.cursors.entries()) {
        cursors[roomId] = Object.fromEntries(roomCursors);
      }
      await offlineManager.saveOfflineData('collaboration:cursors', cursors);
      
      // Save shared states
      const sharedStates = {};
      for (const [roomId, roomState] of this.sharedStates.entries()) {
        sharedStates[roomId] = roomState;
      }
      await offlineManager.saveOfflineData('collaboration:sharedStates', sharedStates);
      
      // Save pending changes
      await offlineManager.saveOfflineData('collaboration:pendingChanges', this.pendingChanges);
    } catch (error) {
      console.error('Failed to save offline collaboration data:', error);
    }
  }
  
  /**
   * Connect to collaboration service
   * 
   * @param {Object} options - Connection options
   * @returns {Promise<Object>} Connection result
   */
  async connect(options = {}) {
    // Set local user properties
    if (options.userName) {
      this.localUser.name = options.userName;
    }
    
    if (options.userColor) {
      this.localUser.color = options.userColor;
    }
    
    // Save local user
    await offlineManager.saveOfflineData('collaboration:localUser', this.localUser);
    
    // Add local user to users map
    this.users.set(this.localUser.id, this.localUser);
    
    // Save offline data
    await this.saveOfflineData();
    
    return {
      success: true,
      user: this.localUser
    };
  }
  
  /**
   * Disconnect from collaboration service
   * 
   * @returns {Promise<Object>} Disconnect result
   */
  async disconnect() {
    // Save offline data before disconnecting
    await this.saveOfflineData();
    
    return {
      success: true
    };
  }
  
  /**
   * Create room
   * 
   * @param {Object} options - Room options
   * @returns {Promise<Object>} Room
   */
  async createRoom(options = {}) {
    const roomId = options.roomId || uuidv4();
    
    // Create room
    const room = {
      id: roomId,
      name: options.name || `Room ${roomId.substring(0, 8)}`,
      createdAt: Date.now(),
      createdBy: this.localUser.id,
      users: [this.localUser.id],
      isOffline: true
    };
    
    // Add room to rooms map
    this.rooms.set(roomId, room);
    
    // Initialize messages for room
    this.messages.set(roomId, []);
    
    // Initialize cursors for room
    this.cursors.set(roomId, new Map());
    
    // Initialize shared state for room
    this.sharedStates.set(roomId, options.initialState || {});
    
    // Add system message
    await this.addSystemMessage(roomId, `${this.localUser.name} created the room`);
    
    // Save offline data
    await this.saveOfflineData();
    
    return room;
  }
  
  /**
   * Join room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Room
   */
  async joinRoom(roomId) {
    // Check if room exists
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    // Check if user is already in room
    if (room.users.includes(this.localUser.id)) {
      return room;
    }
    
    // Add user to room
    room.users.push(this.localUser.id);
    
    // Add system message
    await this.addSystemMessage(roomId, `${this.localUser.name} joined the room`);
    
    // Notify listeners
    this.notifyListeners('userJoin', {
      roomId,
      user: this.localUser
    });
    
    // Add to pending changes
    this.addPendingChange({
      type: 'joinRoom',
      roomId,
      userId: this.localUser.id,
      timestamp: Date.now()
    });
    
    // Save offline data
    await this.saveOfflineData();
    
    return room;
  }
  
  /**
   * Leave room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Result
   */
  async leaveRoom(roomId) {
    // Check if room exists
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    // Check if user is in room
    if (!room.users.includes(this.localUser.id)) {
      return { success: true };
    }
    
    // Remove user from room
    room.users = room.users.filter(userId => userId !== this.localUser.id);
    
    // Add system message
    await this.addSystemMessage(roomId, `${this.localUser.name} left the room`);
    
    // Notify listeners
    this.notifyListeners('userLeave', {
      roomId,
      userId: this.localUser.id
    });
    
    // Add to pending changes
    this.addPendingChange({
      type: 'leaveRoom',
      roomId,
      userId: this.localUser.id,
      timestamp: Date.now()
    });
    
    // Save offline data
    await this.saveOfflineData();
    
    return { success: true };
  }
  
  /**
   * Get rooms
   * 
   * @returns {Promise<Array>} Rooms
   */
  async getRooms() {
    return Array.from(this.rooms.values());
  }
  
  /**
   * Get room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Room
   */
  async getRoom(roomId) {
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    return room;
  }
  
  /**
   * Send message
   * 
   * @param {string} roomId - Room ID
   * @param {string} text - Message text
   * @returns {Promise<Object>} Message
   */
  async sendMessage(roomId, text) {
    // Check if room exists
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    // Check if user is in room
    if (!room.users.includes(this.localUser.id)) {
      throw new Error(`User is not in room ${roomId}`);
    }
    
    // Create message
    const message = {
      id: uuidv4(),
      roomId,
      userId: this.localUser.id,
      text,
      timestamp: Date.now(),
      isOffline: true
    };
    
    // Add message to room
    const roomMessages = this.messages.get(roomId) || [];
    roomMessages.push(message);
    this.messages.set(roomId, roomMessages);
    
    // Notify listeners
    this.notifyListeners('message', message);
    
    // Add to pending changes
    this.addPendingChange({
      type: 'sendMessage',
      message,
      timestamp: Date.now()
    });
    
    // Save offline data
    await this.saveOfflineData();
    
    return message;
  }
  
  /**
   * Add system message
   * 
   * @param {string} roomId - Room ID
   * @param {string} text - Message text
   * @returns {Promise<Object>} Message
   */
  async addSystemMessage(roomId, text) {
    // Create message
    const message = {
      id: uuidv4(),
      roomId,
      userId: 'system',
      text,
      timestamp: Date.now(),
      isSystem: true,
      isOffline: true
    };
    
    // Add message to room
    const roomMessages = this.messages.get(roomId) || [];
    roomMessages.push(message);
    this.messages.set(roomId, roomMessages);
    
    // Notify listeners
    this.notifyListeners('message', message);
    
    // Save offline data
    await this.saveOfflineData();
    
    return message;
  }
  
  /**
   * Get messages
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Array>} Messages
   */
  async getMessages(roomId) {
    return this.messages.get(roomId) || [];
  }
  
  /**
   * Update cursor position
   * 
   * @param {string} roomId - Room ID
   * @param {Object} position - Cursor position
   * @returns {Promise<Object>} Result
   */
  async updateCursorPosition(roomId, position) {
    // Check if room exists
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    // Check if user is in room
    if (!room.users.includes(this.localUser.id)) {
      throw new Error(`User is not in room ${roomId}`);
    }
    
    // Get room cursors
    let roomCursors = this.cursors.get(roomId);
    
    if (!roomCursors) {
      roomCursors = new Map();
      this.cursors.set(roomId, roomCursors);
    }
    
    // Update cursor position
    roomCursors.set(this.localUser.id, {
      userId: this.localUser.id,
      position,
      timestamp: Date.now()
    });
    
    // Notify listeners
    this.notifyListeners('cursorMove', {
      roomId,
      userId: this.localUser.id,
      position
    });
    
    // Add to pending changes
    this.addPendingChange({
      type: 'updateCursorPosition',
      roomId,
      userId: this.localUser.id,
      position,
      timestamp: Date.now()
    });
    
    // Save offline data
    await this.saveOfflineData();
    
    return { success: true };
  }
  
  /**
   * Get cursor positions
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Cursor positions
   */
  async getCursorPositions(roomId) {
    const roomCursors = this.cursors.get(roomId);
    
    if (!roomCursors) {
      return {};
    }
    
    return Object.fromEntries(roomCursors);
  }
  
  /**
   * Update shared state
   * 
   * @param {string} roomId - Room ID
   * @param {Object} state - State to update
   * @param {boolean} [merge=true] - Whether to merge with existing state
   * @returns {Promise<Object>} Updated state
   */
  async updateSharedState(roomId, state, merge = true) {
    // Check if room exists
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    // Check if user is in room
    if (!room.users.includes(this.localUser.id)) {
      throw new Error(`User is not in room ${roomId}`);
    }
    
    // Get current state
    let currentState = this.sharedStates.get(roomId) || {};
    
    // Update state
    if (merge) {
      currentState = {
        ...currentState,
        ...state
      };
    } else {
      currentState = state;
    }
    
    // Set updated state
    this.sharedStates.set(roomId, currentState);
    
    // Notify listeners
    this.notifyListeners('stateChange', {
      roomId,
      userId: this.localUser.id,
      state: currentState
    });
    
    // Add to pending changes
    this.addPendingChange({
      type: 'updateSharedState',
      roomId,
      userId: this.localUser.id,
      state,
      merge,
      timestamp: Date.now()
    });
    
    // Save offline data
    await this.saveOfflineData();
    
    return currentState;
  }
  
  /**
   * Get shared state
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Shared state
   */
  async getSharedState(roomId) {
    return this.sharedStates.get(roomId) || {};
  }
  
  /**
   * Get users in room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Array>} Users
   */
  async getUsersInRoom(roomId) {
    // Check if room exists
    const room = this.rooms.get(roomId);
    
    if (!room) {
      throw new Error(`Room ${roomId} not found`);
    }
    
    // Get users in room
    return room.users.map(userId => this.users.get(userId)).filter(Boolean);
  }
  
  /**
   * Add pending change
   * 
   * @param {Object} change - Change to add
   */
  addPendingChange(change) {
    this.pendingChanges.push(change);
  }
  
  /**
   * Sync pending changes
   * 
   * @returns {Promise<Object>} Sync result
   */
  async syncPendingChanges() {
    if (this.syncInProgress || this.pendingChanges.length === 0) {
      return { success: true, synced: 0 };
    }
    
    this.syncInProgress = true;
    
    try {
      // In a real implementation, this would send pending changes to the server
      // For now, we'll just clear the pending changes
      const syncedCount = this.pendingChanges.length;
      this.pendingChanges = [];
      
      // Save offline data
      await this.saveOfflineData();
      
      // Notify listeners
      this.notifyListeners('sync', {
        success: true,
        synced: syncedCount
      });
      
      return {
        success: true,
        synced: syncedCount
      };
    } catch (error) {
      console.error('Failed to sync pending changes:', error);
      
      // Notify listeners
      this.notifyListeners('sync', {
        success: false,
        error: error.message
      });
      
      return {
        success: false,
        error: error.message
      };
    } finally {
      this.syncInProgress = false;
    }
  }
  
  /**
   * Add event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback);
    }
  }
  
  /**
   * Remove event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }
  
  /**
   * Notify listeners
   * 
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  notifyListeners(event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        callback(data);
      }
    }
  }
  
  /**
   * Get random color
   * 
   * @returns {string} Random color
   */
  getRandomColor() {
    const colors = [
      '#F44336', '#E91E63', '#9C27B0', '#673AB7', '#3F51B5',
      '#2196F3', '#03A9F4', '#00BCD4', '#009688', '#4CAF50',
      '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800',
      '#FF5722', '#795548', '#9E9E9E', '#607D8B'
    ];
    
    return colors[Math.floor(Math.random() * colors.length)];
  }
}

export default OfflineCollaborationService;
