"""
Example of using NovaFoldClient with AlphaFold integration.

This script demonstrates how to use the NovaFoldClient to predict protein structures
using the AlphaFold API and analyze the results with comphyological metrics.
"""

import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import from src
sys.path.append(str(Path(__file__).parent.parent))

from src.ConsciousNovaFold import NovaFoldClient
from src.folding_engines import AlphaFoldEngine

def main():
    # Example sequence (T4 lysozyme)
    sequence = "MKALTARQQEVFDLIRDHISQTGMPPTRAEIAQRLGFRSPNADKRVNGQTYAQQARKAFQERIDKSKEA"
    
    # You can set your AlphaFold API key here or as an environment variable
    alphafold_api_key = os.getenv('ALPHAFOLD_API_KEY')
    
    try:
        # Create a NovaFold client with AlphaFold integration
        print("Initializing NovaFoldClient with AlphaFold engine...")
        client = NovaFoldClient(
            folding_engine='alphafold',
            alphafold_api_key=alphafold_api_key,
            enable_benchmark=True
        )
        
        # Make a prediction using the external engine
        print(f"\nPredicting structure for sequence (length: {len(sequence)}):")
        print(sequence)
        
        result = client.predict(
            sequence=sequence,
            use_external_engine=True,
            job_name=f"test_prediction_{int(time.time())}"
        )
        
        # Print some information about the prediction
        print("\nPrediction complete!")
        print(f"Prediction source: {result['structure']['metadata'].get('prediction_source', 'unknown')}")
        print(f"Engine: {result['structure']['metadata'].get('engine', 'unknown')}")
        print(f"Job ID: {result['structure']['metadata'].get('job_id', 'N/A')}")
        
        # If we have validation results, print them
        if 'validation' in result and result['validation']:
            print("\nValidation results:")
            for key, value in result['validation'].items():
                if isinstance(value, dict):
                    print(f"- {key}:")
                    for k, v in value.items():
                        print(f"  - {k}: {v}")
                else:
                    print(f"- {key}: {value}")
                    
    except Exception as e:
        print(f"\nError: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import time
    main()
