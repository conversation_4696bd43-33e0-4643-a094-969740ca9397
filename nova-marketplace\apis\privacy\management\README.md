# NovaFuse Privacy Management API

The Privacy Management API provides a comprehensive solution for managing privacy compliance across multiple systems and jurisdictions. It includes features for managing data subject requests, consent records, privacy notices, data breaches, and regulatory compliance.

## Features

- **Data Subject Request Management**: Handle data subject requests (access, rectification, erasure, etc.) across multiple systems.
- **Consent Management**: Track and manage consent records, including collection, withdrawal, and verification.
- **Privacy Notice Management**: Create, update, and manage privacy notices for different audiences and jurisdictions.
- **Data Breach Management**: Track and manage data breaches, including notification requirements and reporting.
- **Integration Framework**: Connect with external systems to automate privacy operations.
- **Reporting and Analytics**: Generate reports and analytics on privacy compliance.

## API Structure

The API is organized into the following components:

- **Models**: Define the data structures for the API.
- **Controllers**: Handle the business logic for the API.
- **Routes**: Define the API endpoints.
- **Middleware**: Handle authentication, error handling, and other cross-cutting concerns.
- **Services**: Provide reusable services for the API.
- **Utils**: Provide utility functions for the API.

## API Endpoints

### Data Processing Activities

- `GET /processing-activities`: Get all data processing activities.
- `GET /processing-activities/:id`: Get a specific data processing activity.
- `POST /processing-activities`: Create a new data processing activity.
- `PUT /processing-activities/:id`: Update a data processing activity.
- `DELETE /processing-activities/:id`: Delete a data processing activity.

### Data Subject Requests

- `GET /subject-requests`: Get all data subject requests.
- `GET /subject-requests/:id`: Get a specific data subject request.
- `POST /subject-requests`: Create a new data subject request.
- `PUT /subject-requests/:id`: Update a data subject request.
- `POST /subject-requests/:id/process`: Process a data subject request.
- `GET /subject-requests/:id/export`: Generate a data export for a data subject request.
- `GET /subject-requests/:id/affected-systems`: Identify affected systems for a data subject request.

### Consent Management

- `GET /consent/records`: Get all consent records.
- `GET /consent/records/:id`: Get a specific consent record.
- `POST /consent/records`: Create a new consent record.
- `PUT /consent/records/:id`: Update a consent record.
- `POST /consent/records/:id/withdraw`: Withdraw consent.
- `GET /consent/records/:id/validity`: Verify consent validity.
- `GET /consent/data-subjects/:dataSubjectId`: Get consent records by data subject.
- `GET /consent/emails/:email`: Get consent records by email.
- `GET /consent/forms`: Generate a consent form.
- `POST /consent/proof/verify`: Verify consent proof.
- `POST /consent/proof/generate`: Generate consent proof.

### Privacy Notices

- `GET /privacy-notices`: Get all privacy notices.
- `GET /privacy-notices/:id`: Get a specific privacy notice.
- `POST /privacy-notices`: Create a new privacy notice.
- `PUT /privacy-notices/:id`: Update a privacy notice.
- `DELETE /privacy-notices/:id`: Delete a privacy notice.
- `POST /privacy-notices/:id/archive`: Archive a privacy notice.
- `GET /privacy-notices/audiences/:audience`: Get privacy notices by audience.
- `GET /privacy-notices/latest`: Get the latest version of a privacy notice.
- `GET /privacy-notices/compare`: Compare two versions of a privacy notice.

### Data Breaches

- `GET /data-breaches`: Get all data breaches.
- `GET /data-breaches/:id`: Get a specific data breach.
- `POST /data-breaches`: Create a new data breach.
- `PUT /data-breaches/:id`: Update a data breach.
- `DELETE /data-breaches/:id`: Delete a data breach.
- `POST /data-breaches/:id/notify`: Send notifications for a data breach.
- `GET /data-breaches/:id/report`: Generate a breach report.
- `GET /data-breaches/:id/notification-requirements`: Assess notification requirements for a data breach.

### Integrations

- `GET /integrations`: Get all integrations.
- `GET /integrations/:id`: Get a specific integration.
- `POST /integrations`: Create a new integration.
- `PUT /integrations/:id`: Update an integration.
- `DELETE /integrations/:id`: Delete an integration.
- `POST /integrations/:id/configure`: Configure an integration.
- `POST /integrations/:id/:action`: Execute an integration action.
- `GET /integrations/:id/health`: Check integration health.
- `GET /integrations/:id/logs`: Get integration logs.
- `GET /integrations/:id/metrics`: Get integration metrics.

### Notifications

- `GET /notifications`: Get all notifications.
- `GET /notifications/:id`: Get a specific notification.
- `POST /notifications`: Create a new notification.
- `POST /notifications/:id/read`: Mark a notification as read.
- `POST /notifications/:id/send`: Send a notification.
- `POST /notifications/generate`: Generate notifications based on criteria.
- `POST /notifications/send-all`: Send all pending notifications.
- `GET /notifications/recipients/:recipient`: Get notifications by recipient.
- `GET /notifications/entities/:entityType/:entityId`: Get notifications by related entity.

### Reports

- `GET /reports/:reportType`: Generate a report based on the report type.
- `GET /reports/dashboard/metrics`: Get dashboard metrics.

## Authentication

The API uses JWT authentication. To authenticate, send a POST request to `/auth/login` with your username and password. The response will include a JWT token that you can use for subsequent requests.

Include the token in the `Authorization` header of your requests:

```
Authorization: Bearer <token>
```

## Error Handling

The API returns appropriate HTTP status codes and error messages for different error scenarios:

- `400 Bad Request`: Invalid request parameters.
- `401 Unauthorized`: Authentication required or invalid token.
- `403 Forbidden`: Insufficient permissions.
- `404 Not Found`: Resource not found.
- `409 Conflict`: Resource already exists.
- `429 Too Many Requests`: Rate limit exceeded.
- `500 Internal Server Error`: Server error.

## Integration Framework

The API includes a powerful integration framework that allows you to connect with external systems to automate privacy operations. The framework includes:

- **Integration Registry**: Manage available integrations.
- **Authentication Manager**: Handle authentication with external systems.
- **Request Executor**: Execute requests to external systems.
- **Data Transformer**: Transform data between the API and external systems.

## Reporting and Analytics

The API includes comprehensive reporting and analytics capabilities:

- **DSR Summary Report**: Summary of data subject requests.
- **Consent Management Report**: Summary of consent records.
- **Data Breach Report**: Summary of data breaches.
- **Processing Activities Report**: Summary of data processing activities.
- **Compliance Status Report**: Summary of compliance status.
- **Dashboard Metrics**: Key metrics for the dashboard.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
