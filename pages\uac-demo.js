import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../components/PageWithSidebar';

export default function UACDemo() {
  // SEO metadata
  const pageProps = {
    title: 'Universal API Connector (UAC) Demo - NovaFuse',
    description: 'Experience the power of NovaFuse\'s Universal API Connector (UAC) with our interactive demo. Test compliance checks, connect to APIs, and see the UAC in action.',
    keywords: 'UAC demo, Universal API Connector, API integration, compliance checks, HIPAA, GDPR, PCI DSS, NovaFuse demo',
    canonical: 'https://novafuse.io/uac-demo',
    ogImage: '/images/uac-demo-og-image.png'
  };

  const sidebarItems = [
    { label: 'Overview', href: '#overview' },
    { label: 'Features', href: '#features' },
    { label: 'Demo Access', href: '#demo-access' },
    { label: 'Use Cases', href: '#use-cases' },
    { label: 'NovaConnect UAC', href: '/novaconnect-uac' },
    { label: 'API Docs', href: '/api-docs' },
    { label: 'Back to Home', href: '/' }
  ];

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
        {/* Hero Section */}
        <div id="overview" className="accent-bg text-white rounded-lg p-8 mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
              <span className="mr-2">🔌</span>
              <span>4 PATENTS PENDING</span>
            </div>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-center">Universal API Connector (UAC)</h2>

          <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
            The world's most versatile API connector with built-in compliance intelligence.
            GRC is just our first use case - the UAC has unlimited potential applications.
          </p>

          <div className="flex flex-wrap justify-center gap-4">
            <a href="#demo-access" className="bg-white text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-50 inline-block">
              Try the Demo
            </a>
            <Link href="/api-docs" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
              View API Docs
            </Link>
          </div>
        </div>

        {/* Features Section */}
        <div id="features" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Key Features</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-secondary p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                  <span className="text-2xl">🔄</span>
                </div>
                <h3 className="text-xl font-semibold">Universal Connectivity</h3>
              </div>
              <p className="text-gray-300">
                Connect to any API regardless of type (REST, GraphQL, SOAP, gRPC) with a unified interface.
                Transform data between formats seamlessly.
              </p>
            </div>

            <div className="bg-secondary p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                  <span className="text-2xl">🛡️</span>
                </div>
                <h3 className="text-xl font-semibold">Compliance Engine</h3>
              </div>
              <p className="text-gray-300">
                Apply compliance rules to API data in real-time. Support for HIPAA, GDPR, PCI DSS, SOC2, ISO27001, and more.
                Automatic data classification and validation.
              </p>
            </div>

            <div className="bg-secondary p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                  <span className="text-2xl">🔐</span>
                </div>
                <h3 className="text-xl font-semibold">Security First</h3>
              </div>
              <p className="text-gray-300">
                Enterprise-grade security with role-based access control, audit logging, and encryption.
                Support for various authentication methods including OAuth, JWT, and API keys.
              </p>
            </div>
          </div>
        </div>

        {/* Demo Access Section */}
        <div id="demo-access" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Try the UAC Demo</h2>

          <div className="bg-secondary p-6 rounded-lg">
            <p className="text-gray-300 mb-6">
              Experience the power of the Universal API Connector with our interactive demo.
              Test compliance checks, connect to APIs, and see the UAC in action.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-semibold mb-4">Demo Features</h3>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>HIPAA, GDPR, and PCI DSS compliance checks</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>API connection and data transformation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>Multi-API orchestration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>Real-time compliance monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-400 mr-2">✓</span>
                    <span>Interactive API documentation</span>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-semibold mb-4">Access the Demo</h3>
                <p className="text-gray-300 mb-4">
                  The UAC demo is available for partners and qualified prospects.
                  Request access to try it yourself.
                </p>
                <div className="space-y-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-blue-900 p-4 rounded-lg border border-blue-700">
                        <h4 className="font-semibold mb-2">Option 1: Access Local Demo</h4>
                        <p className="text-sm mb-3">Access the locally running demo environment:</p>
                        <ul className="list-disc pl-5 text-sm space-y-1 mb-4">
                          <li>No installation required</li>
                          <li>Pre-configured environment</li>
                          <li>Immediate access</li>
                        </ul>
                        <a
                          href="http://localhost:3030"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="accent-bg text-white px-4 py-2 rounded-lg font-bold hover:bg-blue-700 inline-block w-full text-center text-sm"
                        >
                          Launch Local Demo
                        </a>
                      </div>

                      <div className="bg-blue-900 p-4 rounded-lg border border-blue-700">
                        <h4 className="font-semibold mb-2">Option 2: Run Locally</h4>
                        <p className="text-sm mb-3">Run the UAC demo on your own machine:</p>
                        <ol className="list-decimal pl-5 text-sm space-y-1 mb-4">
                          <li>Clone the repository</li>
                          <li>Install dependencies</li>
                          <li>Start the demo server</li>
                          <li>Full customization options</li>
                        </ol>
                        <a
                          href="https://github.com/novafuse/uac-demo"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="border border-blue-400 text-white px-4 py-2 rounded-lg font-bold hover:bg-blue-800 inline-block w-full text-center text-sm"
                        >
                          Get Demo Code
                        </a>
                      </div>
                    </div>

                    <div className="bg-blue-800 p-4 rounded-lg border border-blue-600">
                      <h4 className="font-semibold mb-2">Quick Start Guide</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-semibold mb-1">For Local Demo:</p>
                          <ol className="list-decimal pl-5 text-sm space-y-1">
                            <li>Click "Launch Local Demo"</li>
                            <li>Login with: <code className="bg-gray-800 px-2 py-1 rounded">admin / admin123</code></li>
                            <li>Explore the compliance scenarios</li>
                          </ol>
                        </div>
                        <div>
                          <p className="text-sm font-semibold mb-1">For Local Installation:</p>
                          <ol className="list-decimal pl-5 text-sm space-y-1">
                            <li><code className="bg-gray-800 px-2 py-1 rounded">git clone https://github.com/novafuse/uac-demo.git</code></li>
                            <li><code className="bg-gray-800 px-2 py-1 rounded">npm install</code></li>
                            <li><code className="bg-gray-800 px-2 py-1 rounded">npm start</code></li>
                            <li>Access at: <code className="bg-gray-800 px-2 py-1 rounded">http://localhost:3030</code></li>
                          </ol>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Link href="/partner-onboarding" className="border border-gray-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-800 inline-block w-full text-center">
                    Request Partner Access
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Use Cases Section */}
        <div id="use-cases" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Beyond GRC: Unlimited Applications</h2>

          <div className="bg-secondary p-6 rounded-lg">
            <p className="text-gray-300 mb-6">
              While the UAC excels at GRC use cases, its potential applications extend far beyond compliance.
              Here are just a few examples of what you can build with the Universal API Connector:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Healthcare Interoperability</h4>
                <p className="text-gray-300 text-sm">
                  Connect EHRs, HIEs, and healthcare apps with HIPAA-compliant data exchange.
                  Support for FHIR, HL7, and other healthcare standards.
                </p>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Financial Data Aggregation</h4>
                <p className="text-gray-300 text-sm">
                  Securely connect to banking, investment, and payment APIs with PCI DSS and SOX compliance.
                  Real-time transaction monitoring and fraud detection.
                </p>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">IoT Data Management</h4>
                <p className="text-gray-300 text-sm">
                  Connect to IoT devices and platforms with secure data collection and processing.
                  Apply compliance rules to IoT data streams in real-time.
                </p>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Supply Chain Integration</h4>
                <p className="text-gray-300 text-sm">
                  Connect to supplier, logistics, and inventory systems with secure data exchange.
                  Ensure compliance with industry regulations and standards.
                </p>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Customer Data Platform</h4>
                <p className="text-gray-300 text-sm">
                  Aggregate customer data from multiple sources with GDPR and CCPA compliance.
                  Create a unified customer view while maintaining privacy and security.
                </p>
              </div>

              <div className="bg-gray-800 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">AI/ML Data Pipeline</h4>
                <p className="text-gray-300 text-sm">
                  Connect to data sources and AI/ML platforms with secure data processing.
                  Ensure compliance with AI ethics guidelines and data protection regulations.
                </p>
              </div>
            </div>
          </div>
        </div>



        {/* CTA Section */}
        <div className="bg-secondary p-6 rounded-lg text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Experience the UAC?</h2>
          <p className="text-gray-300 mb-6 max-w-3xl mx-auto">
            Join our partner ecosystem and be among the first to leverage the power of the Universal API Connector.
            Transform how you connect, integrate, and comply with regulations.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/partner-onboarding" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              Become a Partner
            </Link>
            <a href="https://github.com/novafuse/uac-demo/blob/main/src/swagger.yaml" target="_blank" rel="noopener noreferrer" className="border border-gray-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-gray-800 inline-block">
              View API Documentation
            </a>
          </div>
        </div>
    </PageWithSidebar>
  );
}
