/**
 * NovaAssistAI Chat Widget
 *
 * A floating chat widget that provides access to NovaAssistAI.
 */

import React, { useState, useRef, useEffect } from 'react';
import { MessageSquare, X, Send, Zap, History, ChevronLeft, Paperclip, Plus } from 'lucide-react';
import useNovaAssistAI, { Suggestion, FileAttachment as FileAttachmentType } from '../../hooks/useNovaAssistAI';
import ConversationHistory from './ConversationHistory';
import CodeSnippet from './CodeSnippet';
import FileUpload from './FileUpload';
import FileAttachment from './FileAttachment';

interface ChatWidgetProps {
  context?: {
    currentPage?: string;
    selectedItems?: any[];
    [key: string]: any;
  };
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({ context }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [input, setInput] = useState('');
  const [showHistory, setShowHistory] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const {
    messages,
    isLoading,
    error,
    suggestions,
    pendingAttachments,
    sendMessage,
    clearConversation,
    executeSuggestion,
    loadConversation,
    addAttachments,
    removeAttachment,
    clearAttachments
  } = useNovaAssistAI();

  // Scroll to bottom when messages change
  useEffect(() => {
    if (isOpen && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isOpen]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const toggleChat = () => {
    setIsOpen(prev => !prev);
  };

  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (input.trim() === '') return;

    await sendMessage(input, context);
    setInput('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSuggestionClick = async (suggestion: Suggestion) => {
    await executeSuggestion(suggestion);
  };

  const handleFileUpload = (fileUrls: string[], fileNames: string[]) => {
    // Create file attachment objects
    const attachments: FileAttachmentType[] = fileUrls.map((url, index) => ({
      id: `file-${Date.now()}-${index}`,
      url,
      name: fileNames[index],
      type: getFileType(fileNames[index]),
      size: 0 // Size is not available after upload
    }));

    // Add attachments to pending list
    addAttachments(attachments);

    // Hide file upload panel
    setShowFileUpload(false);
  };

  const handleFileUploadError = (error: string) => {
    // Show error message in the UI
    console.error(`File upload error: ${error}`);
  };

  const getFileType = (fileName: string): string => {
    const extension = `.${fileName.split('.').pop()}`.toLowerCase();

    if (['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(extension)) {
      return 'image/' + extension.substring(1);
    } else if (extension === '.pdf') {
      return 'application/pdf';
    } else if (['.doc', '.docx'].includes(extension)) {
      return 'application/msword';
    } else if (['.xls', '.xlsx'].includes(extension)) {
      return 'application/vnd.ms-excel';
    } else if (extension === '.csv') {
      return 'text/csv';
    } else if (['.zip', '.rar'].includes(extension)) {
      return 'application/zip';
    } else if (['.mp4', '.webm'].includes(extension)) {
      return 'video/' + extension.substring(1);
    } else if (['.mp3', '.wav'].includes(extension)) {
      return 'audio/' + extension.substring(1);
    } else {
      return 'application/octet-stream';
    }
  };

  // Format message content with markdown-like syntax
  const formatMessageContent = (content: string): React.ReactNode => {
    // Split content by code blocks
    const parts = content.split(/(`{3}([\w-]+)?\n[\s\S]*?\n`{3})/g);
    const result: React.ReactNode[] = [];

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];

      // Skip empty parts and language identifiers (captured in group)
      if (!part || (i > 0 && i % 4 === 2)) continue;

      // Check if this is a code block
      if (part.startsWith('```') && part.endsWith('```') && part.includes('\n')) {
        // Extract language (if specified) and code
        const language = parts[i+1] || 'javascript';
        const code = part.slice(part.indexOf('\n') + 1, part.lastIndexOf('\n'));

        // Add code snippet component
        result.push(
          <CodeSnippet
            key={`code-${i}`}
            code={code}
            language={language}
          />
        );

        // Skip the language part
        i += 1;
      } else {
        // Handle regular text with markdown formatting

        // Handle links
        const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g;
        const textWithLinks = part.replace(linkRegex, (_match, text, url) => {
          return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">${text}</a>`;
        });

        // Handle bold text
        const boldRegex = /\*\*([^*]+)\*\*/g;
        const textWithBold = textWithLinks.replace(boldRegex, '<strong>$1</strong>');

        // Handle italic text
        const italicRegex = /\_([^_]+)\_/g;
        const textWithItalic = textWithBold.replace(italicRegex, '<em>$1</em>');

        // Handle lists
        const listItemRegex = /^\s*[\*\-]\s+(.+)$/gm;
        const textWithLists = textWithItalic.replace(listItemRegex, '<li>$1</li>');

        result.push(
          <div
            key={`text-${i}`}
            dangerouslySetInnerHTML={{ __html: textWithLists }}
            className="whitespace-pre-wrap"
          />
        );
      }
    }

    return result;
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat toggle button */}
      <button
        onClick={toggleChat}
        className="flex items-center justify-center w-12 h-12 rounded-full bg-blue-600 text-white shadow-lg hover:bg-blue-700 focus:outline-none"
        aria-label={isOpen ? 'Close chat' : 'Open chat'}
      >
        {isOpen ? <X size={20} /> : <MessageSquare size={20} />}
      </button>

      {/* Chat window */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 w-80 sm:w-96 h-96 bg-white rounded-lg shadow-xl flex flex-col border border-gray-200">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b">
            {showHistory ? (
              <>
                <div className="flex items-center">
                  <button
                    onClick={() => setShowHistory(false)}
                    className="text-gray-500 hover:text-gray-700 mr-2"
                    aria-label="Back to chat"
                  >
                    <ChevronLeft size={16} />
                  </button>
                  <h3 className="font-medium">Conversation History</h3>
                </div>
                <button
                  onClick={toggleChat}
                  className="text-gray-500 hover:text-gray-700"
                  aria-label="Close chat"
                >
                  <X size={16} />
                </button>
              </>
            ) : (
              <>
                <h3 className="font-medium">NovaAssistAI</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowHistory(true)}
                    className="text-gray-500 hover:text-gray-700"
                    aria-label="View history"
                  >
                    <History size={16} />
                  </button>
                  <button
                    onClick={clearConversation}
                    className="text-gray-500 hover:text-gray-700"
                    aria-label="Clear conversation"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 6h18"></path>
                      <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                      <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                    </svg>
                  </button>
                  <button
                    onClick={toggleChat}
                    className="text-gray-500 hover:text-gray-700"
                    aria-label="Close chat"
                  >
                    <X size={16} />
                  </button>
                </div>
              </>
            )}
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-y-auto">
            {showHistory ? (
              <ConversationHistory
                onSelectConversation={(conversationId) => {
                  loadConversation(conversationId);
                  setShowHistory(false);
                }}
              />
            ) : (
              <div className="p-3">
                {messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-full text-center text-gray-500">
                    <MessageSquare size={32} className="mb-2 text-blue-500" />
                    <p className="mb-1">How can I help you today?</p>
                    <p className="text-xs">Ask me about compliance, controls, or tests.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg p-3 ${
                            message.role === 'user'
                              ? 'bg-blue-600 text-white'
                              : message.role === 'system'
                                ? 'bg-gray-200 text-gray-800'
                                : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {formatMessageContent(message.content)}

                          {/* Display attachments if any */}
                          {message.attachments && message.attachments.length > 0 && (
                            <div className="mt-3 space-y-2">
                              {message.attachments.map(attachment => (
                                <FileAttachment
                                  key={attachment.id}
                                  url={attachment.url}
                                  name={attachment.name}
                                />
                              ))}
                            </div>
                          )}

                          <div
                            className={`text-xs mt-1 ${
                              message.role === 'user' ? 'text-blue-200' : 'text-gray-500'
                            }`}
                          >
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </div>
                        </div>
                      </div>
                    ))}

                    {isLoading && (
                      <div className="flex justify-start">
                        <div className="bg-gray-100 rounded-lg p-3 max-w-[80%]">
                          <div className="flex items-center">
                            <div className="typing-indicator">
                              <span></span>
                              <span></span>
                              <span></span>
                            </div>
                            <span className="text-xs text-gray-500 ml-2">NovaAssistAI is thinking...</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {error && (
                      <div className="flex justify-center">
                        <div className="bg-red-100 text-red-800 rounded-lg p-3 text-sm">
                          Error: {error.message}
                        </div>
                      </div>
                    )}

                    {suggestions.length > 0 && (
                      <div className="mt-4">
                        <div className="text-xs text-gray-500 mb-2">Suggestions:</div>
                        <div className="flex flex-wrap gap-2">
                          {suggestions.map((suggestion) => (
                            <button
                              key={suggestion.id}
                              onClick={() => handleSuggestionClick(suggestion)}
                              className={`px-3 py-1.5 rounded-full text-sm flex items-center transition-colors ${
                                suggestion.action
                                  ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            >
                              {suggestion.action ? (
                                <Zap size={12} className="mr-1.5 text-blue-500" />
                              ) : (
                                <MessageSquare size={12} className="mr-1.5 text-gray-500" />
                              )}
                              <span className="truncate max-w-[150px]">{suggestion.text}</span>
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    <div ref={messagesEndRef} />
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Input */}
          {!showHistory && (
            <>
              {/* File upload panel */}
              {showFileUpload && (
                <div className="p-3 border-t">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium">Upload Files</h3>
                    <button
                      onClick={() => setShowFileUpload(false)}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <X size={16} />
                    </button>
                  </div>
                  <FileUpload
                    onUploadComplete={handleFileUpload}
                    onUploadError={handleFileUploadError}
                    maxFiles={5}
                    maxSizeMB={10}
                  />
                </div>
              )}

              {/* Pending attachments */}
              {pendingAttachments.length > 0 && (
                <div className="px-3 pt-3">
                  <div className="text-xs text-gray-500 mb-2">Attachments:</div>
                  <div className="flex flex-wrap gap-2">
                    {pendingAttachments.map(attachment => (
                      <FileAttachment
                        key={attachment.id}
                        url={attachment.url}
                        name={attachment.name}
                        showRemove={true}
                        onRemove={() => removeAttachment(attachment.id)}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Message input */}
              <form onSubmit={handleSendMessage} className="p-3 border-t">
                <div className="flex items-end">
                  <textarea
                    ref={inputRef}
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Type your message..."
                    className="flex-1 p-2 border rounded-l-md focus:outline-none focus:ring-1 focus:ring-blue-500 resize-none"
                    rows={1}
                    style={{ maxHeight: '100px', minHeight: '38px' }}
                  />
                  <div className="flex">
                    <button
                      type="button"
                      onClick={() => setShowFileUpload(prev => !prev)}
                      className="bg-gray-100 text-gray-600 p-2 hover:bg-gray-200"
                      title="Attach files"
                    >
                      <Paperclip size={18} />
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading || (input.trim() === '' && pendingAttachments.length === 0)}
                      className="bg-blue-600 text-white p-2 rounded-r-md hover:bg-blue-700 disabled:bg-blue-300"
                    >
                      <Send size={18} />
                    </button>
                  </div>
                </div>
              </form>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ChatWidget;
