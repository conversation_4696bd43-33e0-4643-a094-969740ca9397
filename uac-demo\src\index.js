/**
 * NovaFuse Universal API Connector (UAC) Demo
 * 
 * This is a simplified version of the UAC that demonstrates its core capabilities.
 * It showcases how the UAC can connect to various APIs and apply compliance rules
 * in real-time.
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const swaggerUi = require('swagger-ui-express');
const YAML = require('yamljs');
const path = require('path');

// Import UAC core modules
const { initializeConnector } = require('./core/connector');
const { setupComplianceEngine } = require('./core/compliance');
const { setupAuthManager } = require('./core/auth');
const apiRoutes = require('./routes');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3030;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Initialize UAC components
const connector = initializeConnector();
const complianceEngine = setupComplianceEngine();
const authManager = setupAuthManager();

// Make UAC components available to routes
app.use((req, res, next) => {
  req.uac = {
    connector,
    complianceEngine,
    authManager
  };
  next();
});

// API Documentation
const swaggerDocument = YAML.load(path.join(__dirname, 'swagger.yaml'));
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// API Routes
app.use('/api', apiRoutes);

// Home route
app.get('/', (req, res) => {
  res.send({
    name: 'NovaFuse Universal API Connector (UAC)',
    version: '0.1.0',
    status: 'operational',
    documentation: '/api-docs'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).send({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'Something went wrong' : err.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`NovaFuse UAC Demo running on http://localhost:${PORT}`);
  console.log(`API Documentation available at http://localhost:${PORT}/api-docs`);
});

module.exports = app; // For testing
