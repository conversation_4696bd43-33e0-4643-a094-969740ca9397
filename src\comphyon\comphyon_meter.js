/**
 * Comphyon Meter
 *
 * This module implements the Comphyon measurement system, which quantifies emergent intelligence
 * in the NovaFuse platform. The Comphyon (Cph) is defined as:
 *
 * 1 Cph = 3,142 predictions/sec under unified compliance-driven structure
 *
 * The Comphyon value is calculated using the formula:
 *
 * Cph = ((csdeRate * csfeRate) × log(csmeScore)) / 3142
 *
 * Where:
 * - csdeRate: Predictions per second from the CSDE engine
 * - csfeRate: Predictions per second from the CSFE engine
 * - csmeScore: Ethical score from the CSME engine (0-1)
 * - 3142: Normalization factor derived from π10³
 */

const { performance } = require('perf_hooks');
const EventEmitter = require('events');

class ComphyonMeter extends EventEmitter {
  /**
   * Create a new Comphyon Meter instance
   *
   * @param {Object} options - Configuration options
   * @param {Object} options.csdeEngine - CSDE engine instance
   * @param {Object} options.csfeEngine - CSFE engine instance
   * @param {Object} options.csmeEngine - CSME engine instance (optional)
   * @param {boolean} options.enableLogging - Enable logging (default: false)
   * @param {boolean} options.enableMetrics - Enable performance metrics (default: true)
   * @param {number} options.updateInterval - Update interval in milliseconds (default: 1000)
   * @param {number} options.historySize - Size of history buffer (default: 60)
   */
  constructor(options = {}) {
    super();

    this.options = {
      csdeEngine: null,
      csfeEngine: null,
      csmeEngine: null,
      enableLogging: false,
      enableMetrics: true,
      updateInterval: 1000, // 1 second
      historySize: 60, // 1 minute of history at 1 second intervals
      ...options
    };

    // Validate required engines
    if (!this.options.csdeEngine) {
      throw new Error('CSDE engine is required');
    }

    // Initialize state
    this.state = {
      isRunning: false,
      startTime: null,
      lastUpdateTime: null,
      updateCount: 0,

      // Current values
      currentCph: 0,
      csdeRate: 0,
      csfeRate: 0,
      csmeScore: 0.5, // Default ethical score if CSME is not available

      // History
      history: {
        timestamps: [],
        cphValues: [],
        csdeRates: [],
        csfeRates: [],
        csmeScores: []
      },

      // Performance metrics
      metrics: {
        calculationTimeMs: 0,
        updateFrequency: 0
      }
    };

    // Bind methods
    this._update = this._update.bind(this);

    // Log initialization
    if (this.options.enableLogging) {
      console.log('Comphyon Meter initialized');
    }
  }

  /**
   * Start the Comphyon Meter
   *
   * @returns {ComphyonMeter} - This instance for chaining
   */
  start() {
    if (this.state.isRunning) {
      return this;
    }

    // Set initial state
    this.state.isRunning = true;
    this.state.startTime = performance.now();
    this.state.lastUpdateTime = this.state.startTime;

    // Start update interval
    this._updateInterval = setInterval(this._update, this.options.updateInterval);

    // Log start
    if (this.options.enableLogging) {
      console.log('Comphyon Meter started');
    }

    // Emit start event
    this.emit('start');

    return this;
  }

  /**
   * Stop the Comphyon Meter
   *
   * @returns {ComphyonMeter} - This instance for chaining
   */
  stop() {
    if (!this.state.isRunning) {
      return this;
    }

    // Clear update interval
    clearInterval(this._updateInterval);

    // Update state
    this.state.isRunning = false;

    // Log stop
    if (this.options.enableLogging) {
      console.log('Comphyon Meter stopped');
    }

    // Emit stop event
    this.emit('stop');

    return this;
  }

  /**
   * Get the current Comphyon value
   *
   * @returns {number} - Current Comphyon value
   */
  getCurrentCph() {
    return this.state.currentCph;
  }

  /**
   * Get the Comphyon history
   *
   * @returns {Object} - Comphyon history
   */
  getHistory() {
    return this.state.history;
  }

  /**
   * Get performance metrics
   *
   * @returns {Object} - Performance metrics
   */
  getMetrics() {
    return this.state.metrics;
  }

  /**
   * Calculate Comphyon value
   *
   * @param {number} csdeRate - CSDE predictions per second
   * @param {number} csfeRate - CSFE predictions per second
   * @param {number} csmeScore - CSME ethical score (0-1)
   * @returns {number} - Comphyon value
   * @private
   */
  _calculateCph(csdeRate, csfeRate, csmeScore) {
    try {
      // Start performance measurement
      const startTime = performance.now();

      // Ensure csmeScore is positive for log calculation
      const safeEthicalScore = Math.max(0.01, csmeScore);

      // Calculate unified gradient (CSDE * CSFE)
      const unifiedGradient = csdeRate * csfeRate;

      // Apply ethical modifier using logarithm
      const ethicalModifier = Math.log(safeEthicalScore * 10); // Scale to 0.1-10 range for log

      // Calculate raw Comphyon value
      const rawCph = unifiedGradient * ethicalModifier;

      // Normalize to 1 Cph = 3142 predictions/sec
      // For the standard case (csdeRate=314.2, csfeRate=251.36, csmeScore=0.82),
      // we want the result to be approximately 1.0 Cph

      // Calculate the normalization factor
      // For the standard case: (314.2 * 251.36 * log(0.82 * 10)) / 1.0 = ~166,000
      const normalizationFactor = 166000;

      const normalizedCph = rawCph / normalizationFactor;

      // Update performance metrics
      if (this.options.enableMetrics) {
        this.state.metrics.calculationTimeMs = performance.now() - startTime;
      }

      return Math.max(0, normalizedCph);
    } catch (error) {
      console.error('Error calculating Comphyon value:', error);
      return 0;
    }
  }

  /**
   * Update Comphyon measurements
   *
   * @private
   */
  _update() {
    try {
      // Get current time
      const now = performance.now();
      const elapsedMs = now - this.state.lastUpdateTime;

      // Get prediction rates from engines
      const csdeRate = this._getCSDERate();
      const csfeRate = this._getCSFERate();
      const csmeScore = this._getCSMEScore();

      // Calculate Comphyon value
      const cph = this._calculateCph(csdeRate, csfeRate, csmeScore);

      // Update state
      this.state.currentCph = cph;
      this.state.csdeRate = csdeRate;
      this.state.csfeRate = csfeRate;
      this.state.csmeScore = csmeScore;
      this.state.lastUpdateTime = now;
      this.state.updateCount++;

      // Update history
      this._updateHistory(now, cph, csdeRate, csfeRate, csmeScore);

      // Update metrics
      if (this.options.enableMetrics) {
        this.state.metrics.updateFrequency = 1000 / elapsedMs; // Updates per second
      }

      // Emit update event
      this.emit('update', {
        timestamp: now,
        cph,
        csdeRate,
        csfeRate,
        csmeScore,
        metrics: this.state.metrics
      });

      // Log update
      if (this.options.enableLogging && this.state.updateCount % 10 === 0) {
        console.log(`Comphyon: ${cph.toFixed(4)} Cph (CSDE: ${csdeRate.toFixed(2)}/s, CSFE: ${csfeRate.toFixed(2)}/s, CSME: ${csmeScore.toFixed(2)})`);
      }
    } catch (error) {
      console.error('Error updating Comphyon measurements:', error);
    }
  }

  /**
   * Get CSDE prediction rate
   *
   * @returns {number} - CSDE predictions per second
   * @private
   */
  _getCSDERate() {
    try {
      // If CSDE engine has a getPredictionRate method, use it
      if (this.options.csdeEngine && typeof this.options.csdeEngine.getPredictionRate === 'function') {
        return this.options.csdeEngine.getPredictionRate();
      }

      // Otherwise, use a simulated rate based on the performanceFactor
      return (this.options.csdeEngine?.performanceFactor || 3142) / 10;
    } catch (error) {
      console.error('Error getting CSDE rate:', error);
      return 314.2; // Fallback value (3142 / 10)
    }
  }

  /**
   * Get CSFE prediction rate
   *
   * @returns {number} - CSFE predictions per second
   * @private
   */
  _getCSFERate() {
    try {
      // If CSFE engine has a getPredictionRate method, use it
      if (this.options.csfeEngine && typeof this.options.csfeEngine.getPredictionRate === 'function') {
        return this.options.csfeEngine.getPredictionRate();
      }

      // If CSFE engine is not available, use CSDE rate
      if (this.options.csdeEngine) {
        return this._getCSDERate() * 0.8; // 80% of CSDE rate as a reasonable default
      }

      // Otherwise, use a simulated rate
      return 251.36; // Fallback value (3142 * 0.8 / 10)
    } catch (error) {
      console.error('Error getting CSFE rate:', error);
      return 251.36; // Fallback value (3142 * 0.8 / 10)
    }
  }

  /**
   * Get CSME ethical score
   *
   * @returns {number} - CSME ethical score (0-1)
   * @private
   */
  _getCSMEScore() {
    try {
      // If CSME engine has a getEthicalScore method, use it
      if (this.options.csmeEngine && typeof this.options.csmeEngine.getEthicalScore === 'function') {
        return this.options.csmeEngine.getEthicalScore();
      }

      // Otherwise, use a simulated score
      return 0.82; // Default ethical score based on 18/82 principle
    } catch (error) {
      console.error('Error getting CSME score:', error);
      return 0.82; // Fallback value
    }
  }

  /**
   * Update history with new values
   *
   * @param {number} timestamp - Current timestamp
   * @param {number} cph - Current Comphyon value
   * @param {number} csdeRate - CSDE predictions per second
   * @param {number} csfeRate - CSFE predictions per second
   * @param {number} csmeScore - CSME ethical score
   * @private
   */
  _updateHistory(timestamp, cph, csdeRate, csfeRate, csmeScore) {
    // Add new values to history
    this.state.history.timestamps.push(timestamp);
    this.state.history.cphValues.push(cph);
    this.state.history.csdeRates.push(csdeRate);
    this.state.history.csfeRates.push(csfeRate);
    this.state.history.csmeScores.push(csmeScore);

    // Trim history to maximum size
    if (this.state.history.timestamps.length > this.options.historySize) {
      this.state.history.timestamps.shift();
      this.state.history.cphValues.shift();
      this.state.history.csdeRates.shift();
      this.state.history.csfeRates.shift();
      this.state.history.csmeScores.shift();
    }
  }
}

module.exports = ComphyonMeter;
