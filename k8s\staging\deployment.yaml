apiVersion: apps/v1
kind: Deployment
metadata:
  name: novafuse-uac
  namespace: staging
  labels:
    app: novafuse-uac
    environment: staging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: novafuse-uac
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novafuse-uac
        environment: staging
    spec:
      containers:
      - name: novafuse-uac
        image: gcr.io/PROJECT_ID/novafuse-uac:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        env:
        - name: NODE_ENV
          value: "staging"
        - name: PORT
          value: "3001"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: mongodb-uri
        - name: REDIS_URI
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: redis-uri
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: api-key
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: jwt-secret
        - name: CLUSTER_ENABLED
          value: "true"
        - name: LOG_LEVEL
          value: "info"
        - name: MONITORING_ENABLED
          value: "true"
        - name: TRACING_ENABLED
          value: "true"
        - name: GCP_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: novafuse-uac-secrets
              key: gcp-project-id
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        - name: CORS_ORIGIN
          value: "*"
        - name: RATE_LIMIT_WINDOW_MS
          value: "60000"
        - name: RATE_LIMIT_MAX
          value: "100"
        - name: DB_OPTIMIZATION_ENABLED
          value: "true"
        - name: DB_OPTIMIZATION_INTERVAL
          value: "86400000"
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: google-cloud-key
        secret:
          secretName: novafuse-uac-gcp-key
---
apiVersion: v1
kind: Service
metadata:
  name: novafuse-uac
  namespace: staging
  labels:
    app: novafuse-uac
    environment: staging
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: novafuse-uac
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: novafuse-uac
  namespace: staging
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: novafuse-uac
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 70
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novafuse-uac
  namespace: staging
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "novafuse-uac-staging-ip"
    networking.gke.io/managed-certificates: "novafuse-uac-staging-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novafuse-uac-frontend-config"
spec:
  rules:
  - host: staging-api.novafuse.io
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: novafuse-uac
            port:
              number: 80
---
apiVersion: networking.gke.io/v1beta1
kind: FrontendConfig
metadata:
  name: novafuse-uac-frontend-config
  namespace: staging
spec:
  redirectToHttps:
    enabled: true
    responseCodeName: MOVED_PERMANENTLY_DEFAULT
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: novafuse-uac-staging-cert
  namespace: staging
spec:
  domains:
  - staging-api.novafuse.io
