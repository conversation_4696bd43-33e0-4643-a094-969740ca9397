{"id": "legal-regulatory-intelligence", "name": "Legal & Regulatory Intelligence Connector", "description": "Connector for legal and regulatory intelligence systems", "version": "1.0.0", "category": "legal", "icon": "legal-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/legal", "supportEmail": "<EMAIL>", "authentication": {"type": "oauth2", "oauth2": {"authorizationUrl": "https://auth.example.com/oauth2/authorize", "tokenUrl": "https://auth.example.com/oauth2/token", "scopes": ["read:regulations", "read:legal"], "refreshTokenUrl": "https://auth.example.com/oauth2/token"}, "fields": {"clientId": {"type": "string", "label": "Client ID", "required": true, "sensitive": false, "description": "OAuth 2.0 Client ID"}, "clientSecret": {"type": "string", "label": "Client Secret", "required": true, "sensitive": true, "description": "OAuth 2.0 Client Secret"}, "redirectUri": {"type": "string", "label": "Redirect URI", "required": true, "sensitive": false, "description": "OAuth 2.0 Redirect URI"}}}, "endpoints": [{"id": "listRegulations", "name": "List Regulations", "description": "List all regulations", "method": "GET", "url": "https://api.example.com/regulations", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "jurisdiction": {"type": "string", "label": "Juris<PERSON>", "required": false, "description": "Filter by jurisdiction (e.g., US, EU, UK)"}, "industry": {"type": "string", "label": "Industry", "required": false, "description": "Filter by industry sector"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["proposed", "active", "superseded", "repealed"], "description": "Filter by regulation status"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "jurisdiction": {"type": "string", "description": "Filter by jurisdiction"}, "industry": {"type": "string", "description": "Filter by industry sector"}, "status": {"type": "string", "description": "Filter by regulation status", "enum": ["proposed", "active", "superseded", "repealed"]}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Regulation ID"}, "title": {"type": "string", "description": "Regulation title"}, "description": {"type": "string", "description": "Regulation description"}, "jurisdiction": {"type": "string", "description": "Jurisdiction (e.g., US, EU, UK)"}, "industry": {"type": "string", "description": "Industry sector"}, "status": {"type": "string", "description": "Regulation status", "enum": ["proposed", "active", "superseded", "repealed"]}, "effectiveDate": {"type": "string", "format": "date", "description": "Effective date of the regulation"}, "publishedDate": {"type": "string", "format": "date", "description": "Published date of the regulation"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getRegulation", "name": "Get Regulation", "description": "Get a specific regulation", "method": "GET", "url": "https://api.example.com/regulations/{regulationId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"regulationId": {"type": "string", "label": "Regulation ID", "required": true, "description": "ID of the regulation to retrieve"}}, "inputSchema": {"type": "object", "properties": {"regulationId": {"type": "string", "description": "ID of the regulation to retrieve"}}, "required": ["regulationId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Regulation ID"}, "title": {"type": "string", "description": "Regulation title"}, "description": {"type": "string", "description": "Regulation description"}, "fullText": {"type": "string", "description": "Full text of the regulation"}, "jurisdiction": {"type": "string", "description": "Jurisdiction (e.g., US, EU, UK)"}, "industry": {"type": "string", "description": "Industry sector"}, "status": {"type": "string", "description": "Regulation status", "enum": ["proposed", "active", "superseded", "repealed"]}, "effectiveDate": {"type": "string", "format": "date", "description": "Effective date of the regulation"}, "publishedDate": {"type": "string", "format": "date", "description": "Published date of the regulation"}, "regulatoryBody": {"type": "string", "description": "Regulatory body that issued the regulation"}, "citations": {"type": "array", "items": {"type": "string"}, "description": "Citations and references"}, "relatedRegulations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Related regulation ID"}, "title": {"type": "string", "description": "Related regulation title"}, "relationship": {"type": "string", "description": "Relationship type (e.g., supersedes, amends)"}}}}}}}, {"id": "searchRegulations", "name": "Search Regulations", "description": "Search for regulations by keyword", "method": "GET", "url": "https://api.example.com/regulations/search", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"query": {"type": "string", "label": "Query", "required": true, "description": "Search query"}, "jurisdiction": {"type": "string", "label": "Juris<PERSON>", "required": false, "description": "Filter by jurisdiction (e.g., US, EU, UK)"}, "industry": {"type": "string", "label": "Industry", "required": false, "description": "Filter by industry sector"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["proposed", "active", "superseded", "repealed"], "description": "Filter by regulation status"}, "page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}}, "inputSchema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "jurisdiction": {"type": "string", "description": "Filter by jurisdiction"}, "industry": {"type": "string", "description": "Filter by industry sector"}, "status": {"type": "string", "description": "Filter by regulation status", "enum": ["proposed", "active", "superseded", "repealed"]}, "page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}}, "required": ["query"]}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Regulation ID"}, "title": {"type": "string", "description": "Regulation title"}, "description": {"type": "string", "description": "Regulation description"}, "jurisdiction": {"type": "string", "description": "Jurisdiction (e.g., US, EU, UK)"}, "industry": {"type": "string", "description": "Industry sector"}, "status": {"type": "string", "description": "Regulation status", "enum": ["proposed", "active", "superseded", "repealed"]}, "effectiveDate": {"type": "string", "format": "date", "description": "Effective date of the regulation"}, "relevanceScore": {"type": "number", "description": "Relevance score for the search query"}, "highlights": {"type": "array", "items": {"type": "string"}, "description": "Highlighted text snippets matching the query"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}]}