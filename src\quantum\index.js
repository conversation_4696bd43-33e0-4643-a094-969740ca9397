/**
 * Quantum Components
 *
 * This module exports all quantum components, including the Self-Healing Tensor,
 * Finite Universe Principle, Comphyological Tensor Core, and related utilities.
 */

const SelfHealingTensor = require('./self-healing-tensor');
const {
  PI,
  PI_10_CUBED,
  GOLDEN_RATIO,
  MAX_SAFE_BOUNDS,
  saturate,
  asymptotic
} = require('./constants');

// Import Comphyological Tensor Core components
const {
  // Core tensor components
  TensorOperations,
  DynamicWeightingProtocol,
  PsiTensorCore,
  EnergyCalculator,

  // Comphyological tensor core
  ComphyologicalTensorCore,
  createComphyologicalTensorCore,

  // Factory function
  createTensorComponents
} = require('./tensor');

// Import Finite Universe Principle components
const {
  // Core components
  FiniteUniverse,
  BoundaryEnforcer,

  // Basic scenario classes
  MathematicalIncompatibilityScenario,
  InfiniteValueScenario,
  UnboundedRecursionScenario,
  BoundaryViolationScenario,
  CircularReferenceScenario,
  ExcessiveComplexityScenario,
  InfiniteLoopScenario,

  // Advanced components
  EnhancedBoundaryEnforcer,
  DomainIntegration,

  // Advanced scenario classes
  MultiVectorScenario,
  DomainSpecificScenario,
  QuantumDecoherenceScenario,
  CrossDomainLeakageScenario,
  TemporalParadoxScenario,

  // Domain engines
  CSEDAdapter,
  CSFEAdapter,
  CSMEAdapter,

  // Cross-domain validation
  CrossDomainValidator,

  // Monitoring dashboard
  MonitoringDashboard,

  // NovaFuse integration
  NovaFuseIntegration,

  // Testing framework
  ResilienceGauntlet,

  // Basic factory functions
  createBoundaryEnforcer,
  createResilienceGauntlet,

  // Advanced factory functions
  createEnhancedBoundaryEnforcer,
  createDomainIntegration,
  createCSDEAdapter,
  createCSFEAdapter,
  createCSMEAdapter,
  createAdvancedResilienceGauntlet,
  createIntegratedDefenseSystem,

  // Domain engines factory functions
  createAllDomainAdapters,

  // Cross-domain validation factory functions
  createCrossDomainValidator,

  // Monitoring dashboard factory functions
  createMonitoringDashboard,

  // NovaFuse integration factory functions
  createNovaFuseIntegration,
  createCompleteDefenseSystem,

  // Utility functions
  runQuickTest,
  runAdvancedTest
} = require('./finite-universe-principle');

module.exports = {
  // Self-Healing Tensor
  SelfHealingTensor,

  // Constants
  PI,
  PI_10_CUBED,
  GOLDEN_RATIO,
  MAX_SAFE_BOUNDS,
  saturate,
  asymptotic,

  // Core Finite Universe Principle components
  FiniteUniverse,
  BoundaryEnforcer,

  // Basic Mathematical Incompatibility Scenarios
  MathematicalIncompatibilityScenario,
  InfiniteValueScenario,
  UnboundedRecursionScenario,
  BoundaryViolationScenario,
  CircularReferenceScenario,
  ExcessiveComplexityScenario,
  InfiniteLoopScenario,

  // Advanced Finite Universe Principle components
  EnhancedBoundaryEnforcer,
  DomainIntegration,

  // Advanced Mathematical Incompatibility Scenarios
  MultiVectorScenario,
  DomainSpecificScenario,
  QuantumDecoherenceScenario,
  CrossDomainLeakageScenario,
  TemporalParadoxScenario,

  // Domain engines
  CSEDAdapter,
  CSFEAdapter,
  CSMEAdapter,

  // Cross-domain validation
  CrossDomainValidator,

  // Monitoring dashboard
  MonitoringDashboard,

  // NovaFuse integration
  NovaFuseIntegration,

  // Testing framework
  ResilienceGauntlet,

  // Basic factory functions
  createBoundaryEnforcer,
  createResilienceGauntlet,

  // Advanced factory functions
  createEnhancedBoundaryEnforcer,
  createDomainIntegration,
  createCSDEAdapter,
  createCSFEAdapter,
  createCSMEAdapter,
  createAdvancedResilienceGauntlet,
  createIntegratedDefenseSystem,

  // Domain engines factory functions
  createAllDomainAdapters,

  // Cross-domain validation factory functions
  createCrossDomainValidator,

  // Monitoring dashboard factory functions
  createMonitoringDashboard,

  // NovaFuse integration factory functions
  createNovaFuseIntegration,
  createCompleteDefenseSystem,

  // Utility functions
  runQuickTest,
  runAdvancedTest,

  // Core tensor components
  TensorOperations,
  DynamicWeightingProtocol,
  PsiTensorCore,
  EnergyCalculator,

  // Comphyological tensor core
  ComphyologicalTensorCore,
  createComphyologicalTensorCore,

  // Factory function for tensor components
  createTensorComponents
};
