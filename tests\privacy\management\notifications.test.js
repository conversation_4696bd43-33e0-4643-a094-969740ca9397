const request = require('supertest');
const app = require('./mockApp');

describe('Privacy Management API - Notifications', () => {
  describe('GET /privacy/management/notifications', () => {
    it('should return a list of notifications', async () => {
      const response = await request(app)
        .get('/privacy/management/notifications');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should filter notifications by recipient', async () => {
      // First create a notification with a specific recipient
      const newNotification = {
        type: 'dsr-received',
        title: 'New DSR Received',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      const response = await request(app)
        .get('/privacy/management/notifications')
        .query({ recipient: 'privacy-team' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned notifications should have the specified recipient
      response.body.data.forEach(notification => {
        expect(notification.recipients).toContain('privacy-team');
      });
    });

    it('should filter notifications by status', async () => {
      const response = await request(app)
        .get('/privacy/management/notifications')
        .query({ status: 'pending' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned notifications should have the specified status
      response.body.data.forEach(notification => {
        expect(notification.status).toBe('pending');
      });
    });

    it('should filter notifications by type', async () => {
      const response = await request(app)
        .get('/privacy/management/notifications')
        .query({ type: 'dsr-received' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned notifications should have the specified type
      response.body.data.forEach(notification => {
        expect(notification.type).toBe('dsr-received');
      });
    });

    it('should filter notifications by priority', async () => {
      const response = await request(app)
        .get('/privacy/management/notifications')
        .query({ priority: 'medium' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned notifications should have the specified priority
      response.body.data.forEach(notification => {
        expect(notification.priority).toBe('medium');
      });
    });

    it('should filter notifications by date range', async () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 7); // 7 days ago

      const response = await request(app)
        .get('/privacy/management/notifications')
        .query({
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString()
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned notifications should be within the date range
      response.body.data.forEach(notification => {
        const createdAt = new Date(notification.createdAt);
        expect(createdAt >= startDate).toBe(true);
        expect(createdAt <= new Date()).toBe(true);
      });
    });
  });

  describe('GET /privacy/management/notifications/:id', () => {
    it('should return a specific notification', async () => {
      // First create a notification
      const newNotification = {
        type: 'dsr-received',
        title: 'New DSR Received',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      const createResponse = await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      expect(createResponse.status).toBe(201);

      const notificationId = createResponse.body.data.id;

      const response = await request(app)
        .get(`/privacy/management/notifications/${notificationId}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(notificationId);
      expect(response.body.data.type).toBe(newNotification.type);
      expect(response.body.data.title).toBe(newNotification.title);
      expect(response.body.data.message).toBe(newNotification.message);
    });

    it('should return 404 for non-existent notification', async () => {
      const response = await request(app)
        .get('/privacy/management/notifications/non-existent');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Not Found');
    });
  });

  describe('POST /privacy/management/notifications', () => {
    it('should create a new notification', async () => {
      const newNotification = {
        type: 'dsr-due-soon',
        title: 'DSR Due Soon',
        message: 'A data subject request is due in 3 days',
        recipients: ['privacy-team', 'privacy-officer'],
        priority: 'high',
        channels: ['email', 'in-app'],
        relatedEntityType: 'data-subject-request',
        relatedEntityId: 'dsr-0001',
        metadata: {
          requestType: 'access',
          dueDate: '2023-07-20T00:00:00Z'
        }
      };

      const response = await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      expect(response.status).toBe(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.type).toBe(newNotification.type);
      expect(response.body.data.title).toBe(newNotification.title);
      expect(response.body.data.message).toBe(newNotification.message);
      expect(response.body.data.recipients).toEqual(newNotification.recipients);
      expect(response.body.data.priority).toBe(newNotification.priority);
      expect(response.body.data.channels).toEqual(newNotification.channels);
      expect(response.body.data.relatedEntityType).toBe(newNotification.relatedEntityType);
      expect(response.body.data.relatedEntityId).toBe(newNotification.relatedEntityId);
      expect(response.body.data.metadata).toEqual(newNotification.metadata);
      expect(response.body.data.status).toBe('pending');
      expect(response.body.message).toBe('Notification created successfully');
    });

    it('should return 400 if required fields are missing', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications')
        .send({
          type: 'dsr-due-soon',
          title: 'DSR Due Soon'
          // Missing message and recipients
        });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('POST /privacy/management/notifications/:id/read', () => {
    it('should mark a notification as read', async () => {
      // First create a notification
      const newNotification = {
        type: 'dsr-received',
        title: 'New DSR Received',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      const createResponse = await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      expect(createResponse.status).toBe(201);

      const notificationId = createResponse.body.data.id;

      const response = await request(app)
        .post(`/privacy/management/notifications/${notificationId}/read`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(notificationId);
      expect(response.body.data.status).toBe('read');
      expect(response.body.data.readAt).toBeDefined();
      expect(response.body.message).toBe('Notification marked as read');
    });

    it('should return 404 for non-existent notification', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications/non-existent/read');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Not Found');
    });
  });

  describe('POST /privacy/management/notifications/:id/send', () => {
    it('should send a notification', async () => {
      // First create a notification
      const newNotification = {
        type: 'dsr-received',
        title: 'New DSR Received',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      const createResponse = await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      expect(createResponse.status).toBe(201);

      const notificationId = createResponse.body.data.id;

      const response = await request(app)
        .post(`/privacy/management/notifications/${notificationId}/send`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBe(notificationId);
      expect(response.body.data.status).toBe('sent');
      expect(response.body.data.sentAt).toBeDefined();
      expect(response.body.message).toBeDefined();
    });

    it('should return 400 for already sent notification', async () => {
      // First create and send a notification
      const newNotification = {
        type: 'dsr-received',
        title: 'New DSR Received',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      const createResponse = await request(app)
        .post('/privacy/management/notifications')
        .send(newNotification);

      const notificationId = createResponse.body.data.id;

      await request(app)
        .post(`/privacy/management/notifications/${notificationId}/send`);

      // Try to send it again
      const response = await request(app)
        .post(`/privacy/management/notifications/${notificationId}/send`);

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });

    it('should return 400 for non-existent notification', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications/non-existent/send');

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('POST /privacy/management/notifications/generate', () => {
    it('should generate notifications', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications/generate');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.message).toBeDefined();
    });

    it('should generate DSR notifications', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications/generate')
        .query({ type: 'dsr' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.message).toBeDefined();

      // All generated notifications should be DSR-related
      response.body.data.forEach(notification => {
        expect(['dsr-received', 'dsr-due-soon', 'dsr-overdue', 'dsr-completed'].includes(notification.type)).toBe(true);
      });
    });

    it('should generate data breach notifications', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications/generate')
        .query({ type: 'data-breach' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.message).toBeDefined();

      // All generated notifications should be data breach-related
      response.body.data.forEach(notification => {
        expect(['data-breach-reported', 'data-breach-notification-due'].includes(notification.type)).toBe(true);
      });
    });

    it('should generate DPIA notifications', async () => {
      const response = await request(app)
        .post('/privacy/management/notifications/generate')
        .query({ type: 'dpia' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.message).toBeDefined();

      // All generated notifications should be DPIA-related
      response.body.data.forEach(notification => {
        expect(notification.type).toBe('dpia-required');
      });
    });
  });

  describe('POST /privacy/management/notifications/send-all', () => {
    it('should send all pending notifications', async () => {
      // First create some notifications
      const notification1 = {
        type: 'dsr-received',
        title: 'New DSR Received 1',
        message: 'A new data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      const notification2 = {
        type: 'dsr-received',
        title: 'New DSR Received 2',
        message: 'Another data subject request has been received',
        recipients: ['privacy-team'],
        priority: 'medium'
      };

      await request(app)
        .post('/privacy/management/notifications')
        .send(notification1);

      await request(app)
        .post('/privacy/management/notifications')
        .send(notification2);

      const response = await request(app)
        .post('/privacy/management/notifications/send-all');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.total).toBeGreaterThan(0);
      expect(response.body.data.sent).toBeGreaterThan(0);
      expect(response.body.data.details).toBeDefined();
      expect(Array.isArray(response.body.data.details)).toBe(true);
      expect(response.body.message).toBeDefined();
    });
  });
});
