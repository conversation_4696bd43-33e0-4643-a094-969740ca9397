{"metadata": {"name": "API Key Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for API Key authentication", "author": "NovaGRC", "tags": ["test", "api-key"]}, "authentication": {"type": "API_KEY", "fields": {"apiKey": {"type": "string", "description": "API Key", "required": true, "sensitive": true}, "headerName": {"type": "string", "description": "Header name for the API key", "required": true, "default": "X-API-Key"}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getResource", "name": "Get Resource", "path": "/api-key/resource", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getResource", "targetSystem": "NovaGRC", "targetEntity": "Resource", "transformations": [{"source": "$.data.id", "target": "resourceId", "transform": "identity"}, {"source": "$.data.name", "target": "resourceName", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}