/**
 * Export/Import Routes
 */

const express = require('express');
const router = express.Router();
const ExportImportController = require('../controllers/ExportImportController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');
const { hasFeatureAccess, hasNotReachedFeatureLimit, trackFeatureUsage } = require('../middleware/featureAccessMiddleware');

// All routes require authentication
router.use(authenticate);

// Export routes
router.get('/exports', hasPermission('system:audit'), (req, res, next) => {
  ExportImportController.getAllExports(req, res, next);
});

router.get('/exports/my', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.getMyExports(req, res, next);
});

router.get('/exports/:id', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.getExportById(req, res, next);
});

router.post('/exports',
  hasFeatureAccess('export_import.basic'),
  trackFeatureUsage('export_import.basic'),
  (req, res, next) => {
    ExportImportController.createExport(req, res, next);
  }
);

router.delete('/exports/:id', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.deleteExport(req, res, next);
});

router.get('/exports/:id/download', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.downloadExport(req, res, next);
});

// Import routes
router.get('/imports', hasPermission('system:audit'), (req, res, next) => {
  ExportImportController.getAllImports(req, res, next);
});

router.get('/imports/my', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.getMyImports(req, res, next);
});

router.get('/imports/:id', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.getImportById(req, res, next);
});

router.post('/imports',
  hasFeatureAccess('export_import.basic'),
  trackFeatureUsage('export_import.basic'),
  ExportImportController.getFileUploadMiddleware(),
  (req, res, next) => {
    ExportImportController.createImport(req, res, next);
  }
);

router.post('/imports/:id/process',
  hasFeatureAccess('export_import.basic'),
  trackFeatureUsage('export_import.basic'),
  (req, res, next) => {
    ExportImportController.processImport(req, res, next);
  }
);

router.delete('/imports/:id', hasFeatureAccess('export_import.basic'), (req, res, next) => {
  ExportImportController.deleteImport(req, res, next);
});

module.exports = router;
