/**
 * Feature Flag System for NovaSphere (formerly UCECS)
 * 
 * This system allows for controlled rollout of new features and easy toggling of functionality.
 * Features can be enabled/disabled globally or for specific users/roles.
 */

// Feature flag definitions
export enum FeatureFlag {
  // Core features
  ADVANCED_DASHBOARD = 'advanced_dashboard',
  EVIDENCE_VERSIONING = 'evidence_versioning',
  REQUIREMENT_MAPPING = 'requirement_mapping',
  ADVANCED_SEARCH = 'advanced_search',
  REPORT_SCHEDULING = 'report_scheduling',
  
  // Integration features
  VENDOR_RISK_INTEGRATION = 'vendor_risk_integration', // BridgeCore integration
  REGULATORY_CHANGE_ALERTS = 'regulatory_change_alerts', // NovaPulse integration
  WORKFLOW_AUTOMATION = 'workflow_automation', // NovaFlow integration
  AI_INSIGHTS = 'ai_insights', // NovaEdge integration
  
  // Advanced features
  EVIDENCE_AUTO_COLLECTION = 'evidence_auto_collection',
  EVIDENCE_AUTO_VALIDATION = 'evidence_auto_validation',
  REAL_TIME_COMPLIANCE = 'real_time_compliance',
  PREDICTIVE_COMPLIANCE = 'predictive_compliance',
  BLOCKCHAIN_VERIFICATION = 'blockchain_verification',
  
  // UI/UX features
  DARK_MODE = 'dark_mode',
  CUSTOMIZABLE_DASHBOARD = 'customizable_dashboard',
  MOBILE_OPTIMIZED = 'mobile_optimized',
  ACCESSIBILITY_FEATURES = 'accessibility_features',
  
  // Experimental features
  EXPERIMENTAL_AI_ASSISTANT = 'experimental_ai_assistant',
  EXPERIMENTAL_GRAPH_VIEW = 'experimental_graph_view',
}

// Default feature flag configuration
// In a production environment, this would be loaded from a server or environment variables
const defaultFeatureFlags: Record<FeatureFlag, boolean> = {
  // Core features - enabled by default
  [FeatureFlag.ADVANCED_DASHBOARD]: true,
  [FeatureFlag.EVIDENCE_VERSIONING]: true,
  [FeatureFlag.REQUIREMENT_MAPPING]: true,
  [FeatureFlag.ADVANCED_SEARCH]: true,
  [FeatureFlag.REPORT_SCHEDULING]: true,
  
  // Integration features - enabled by default
  [FeatureFlag.VENDOR_RISK_INTEGRATION]: true,
  [FeatureFlag.REGULATORY_CHANGE_ALERTS]: true,
  [FeatureFlag.WORKFLOW_AUTOMATION]: true,
  [FeatureFlag.AI_INSIGHTS]: true,
  
  // Advanced features - some disabled by default
  [FeatureFlag.EVIDENCE_AUTO_COLLECTION]: true,
  [FeatureFlag.EVIDENCE_AUTO_VALIDATION]: true,
  [FeatureFlag.REAL_TIME_COMPLIANCE]: true,
  [FeatureFlag.PREDICTIVE_COMPLIANCE]: false, // Requires advanced AI capabilities
  [FeatureFlag.BLOCKCHAIN_VERIFICATION]: false, // Experimental feature
  
  // UI/UX features - enabled by default
  [FeatureFlag.DARK_MODE]: true,
  [FeatureFlag.CUSTOMIZABLE_DASHBOARD]: true,
  [FeatureFlag.MOBILE_OPTIMIZED]: true,
  [FeatureFlag.ACCESSIBILITY_FEATURES]: true,
  
  // Experimental features - disabled by default
  [FeatureFlag.EXPERIMENTAL_AI_ASSISTANT]: false,
  [FeatureFlag.EXPERIMENTAL_GRAPH_VIEW]: false,
};

// User-specific feature flag overrides
// This would typically be loaded from a user profile or server
type UserFeatureFlags = Partial<Record<FeatureFlag, boolean>>;

// Role-based feature flag overrides
// This would typically be defined in a server configuration
type RoleFeatureFlags = Record<string, Partial<Record<FeatureFlag, boolean>>>;

const roleFeatureFlags: RoleFeatureFlags = {
  admin: {
    // Admins get access to experimental features
    [FeatureFlag.EXPERIMENTAL_AI_ASSISTANT]: true,
    [FeatureFlag.EXPERIMENTAL_GRAPH_VIEW]: true,
    [FeatureFlag.PREDICTIVE_COMPLIANCE]: true,
    [FeatureFlag.BLOCKCHAIN_VERIFICATION]: true,
  },
  auditor: {
    // Auditors get access to advanced compliance features
    [FeatureFlag.EVIDENCE_AUTO_VALIDATION]: true,
    [FeatureFlag.REAL_TIME_COMPLIANCE]: true,
  },
  user: {
    // Regular users have standard features
  },
};

// Feature flag service
class FeatureFlagService {
  private globalFlags: Record<FeatureFlag, boolean>;
  private userFlags: UserFeatureFlags = {};
  private userRoles: string[] = [];
  
  constructor(initialFlags: Record<FeatureFlag, boolean> = defaultFeatureFlags) {
    this.globalFlags = { ...initialFlags };
    
    // Load from localStorage if available
    if (typeof window !== 'undefined') {
      const savedFlags = localStorage.getItem('featureFlags');
      if (savedFlags) {
        try {
          this.globalFlags = { ...this.globalFlags, ...JSON.parse(savedFlags) };
        } catch (e) {
          console.error('Failed to parse saved feature flags', e);
        }
      }
    }
  }
  
  // Set user roles and user-specific feature flags
  public setUser(roles: string[], userFlags: UserFeatureFlags = {}) {
    this.userRoles = roles;
    this.userFlags = userFlags;
  }
  
  // Check if a feature is enabled
  public isEnabled(feature: FeatureFlag): boolean {
    // Check user-specific override first
    if (feature in this.userFlags) {
      return this.userFlags[feature] as boolean;
    }
    
    // Check role-based overrides
    for (const role of this.userRoles) {
      if (role in roleFeatureFlags && feature in roleFeatureFlags[role]) {
        return roleFeatureFlags[role][feature] as boolean;
      }
    }
    
    // Fall back to global setting
    return this.globalFlags[feature];
  }
  
  // Enable a feature globally
  public enable(feature: FeatureFlag) {
    this.globalFlags[feature] = true;
    this.saveFlags();
  }
  
  // Disable a feature globally
  public disable(feature: FeatureFlag) {
    this.globalFlags[feature] = false;
    this.saveFlags();
  }
  
  // Toggle a feature globally
  public toggle(feature: FeatureFlag) {
    this.globalFlags[feature] = !this.globalFlags[feature];
    this.saveFlags();
  }
  
  // Save flags to localStorage
  private saveFlags() {
    if (typeof window !== 'undefined') {
      localStorage.setItem('featureFlags', JSON.stringify(this.globalFlags));
    }
  }
  
  // Get all feature flags and their status
  public getAllFlags() {
    const result: Record<FeatureFlag, boolean> = { ...this.globalFlags };
    
    // Apply role-based overrides
    for (const role of this.userRoles) {
      if (role in roleFeatureFlags) {
        Object.assign(result, roleFeatureFlags[role]);
      }
    }
    
    // Apply user-specific overrides
    Object.assign(result, this.userFlags);
    
    return result;
  }
}

// Create and export a singleton instance
export const featureFlags = new FeatureFlagService();

// React hook for using feature flags in components
export function useFeatureFlag(feature: FeatureFlag): boolean {
  return featureFlags.isEnabled(feature);
}
