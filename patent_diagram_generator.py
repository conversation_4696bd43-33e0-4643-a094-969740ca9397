import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from matplotlib.patches import Fancy<PERSON>rrowPatch, Circle, Rectangle
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patheffects as path_effects
import matplotlib as mpl
from pathlib import Path

# Set USPTO-compliant style
plt.style.use('seaborn-v0_8-white')  # Updated to use a valid style
mpl.rcParams['font.family'] = 'sans-serif'
mpl.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'sans-serif']
mpl.rcParams['figure.dpi'] = 300
mpl.rcParams['savefig.dpi'] = 300
mpl.rcParams['savefig.format'] = 'tiff'
mpl.rcParams['savefig.bbox'] = 'tight'
mpl.rcParams['savefig.pad_inches'] = 0.1

class PatentDiagramGenerator:
    def __init__(self, output_dir='patent_figures'):
        self.output_dir = Path(output_dir).absolute()
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.fig_counter = 1
        
        # Color scheme
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'background': '#FFFFFF',
            'text': '#000000',
            'highlight': '#E71D36',
            'energy_flow': '#4CC9F0',
            'control_flow': '#7209B7'
        }
        
    def _save_figure(self, fig, name=None):
        """Save figure with auto-incrementing number."""
        if name is None:
            name = f"Figure_{self.fig_counter}"
            self.fig_counter += 1
        
        # Save as TIFF for patent submission
        fig.savefig(self.output_dir / f"{name}.tiff", format='tiff')
        # Also save as SVG for editing
        fig.savefig(self.output_dir / f"{name}.svg", format='svg')
        plt.close(fig)
    
    def generate_system_architecture(self):
        """Generate Figure 1: System Architecture Diagram."""
        fig, ax = plt.subplots(figsize=(10, 8))
        G = nx.DiGraph()
        
        # Add nodes
        nodes = [
            ("Input", {"pos": (1, 5), "color": self.colors['primary']}),
            ("∂Ψ=0 Enforcer", {"pos": (3, 5), "color": self.colors['highlight']}),
            ("TEE Governor", {"pos": (5, 5), "color": self.colors['secondary']}),
            ("UUFT Triad\nDirector", {"pos": (7, 5), "color": self.colors['accent']}),
            ("Coherent\nOutput", {"pos": (9, 5), "color": self.colors['primary']}),
        ]
        
        for node, attrs in nodes:
            G.add_node(node, **attrs)
        
        # Add edges with different styles for energy/control flows
        edges = [
            ("Input", "∂Ψ=0 Enforcer", {"color": self.colors['energy_flow'], "width": 8.2, "style": 'solid'}),
            ("∂Ψ=0 Enforcer", "TEE Governor", {"color": self.colors['energy_flow'], "width": 8.2, "style": 'solid'}),
            ("TEE Governor", "UUFT Triad\nDirector", {"color": self.colors['control_flow'], "width": 1.8, "style": 'dashed'}),
            ("UUFT Triad\nDirector", "Coherent\nOutput", {"color": self.colors['energy_flow'], "width": 10.0, "style": 'solid'}),
        ]
        
        for src, tgt, attrs in edges:
            G.add_edge(src, tgt, **attrs)
        
        # Draw nodes
        pos = nx.get_node_attributes(G, 'pos')
        node_colors = [G.nodes[n].get('color', self.colors['primary']) for n in G.nodes()]
        
        # Draw edges first (behind nodes)
        for u, v, data in G.edges(data=True):
            edge = FancyArrowPatch(
                pos[u], pos[v],
                arrowstyle='-|>',
                connectionstyle=f'arc3,rad={0.2}',
                lw=data['width']/2,
                alpha=0.8,
                color=data['color'],
                linestyle=data['style'],
                zorder=1
            )
            ax.add_patch(edge)
        
        # Draw nodes on top
        for node, (x, y) in pos.items():
            node_circle = Circle((x, y), 0.5, color=G.nodes[node]['color'], ec='black', lw=1, zorder=2)
            ax.add_patch(node_circle)
            
            # Add node label
            ax.text(x, y, node, ha='center', va='center', 
                   color='white', weight='bold', fontsize=8, zorder=3)
        
        # Add energy flow labels
        ax.text(2, 5.5, "82% Energy Flow", color=self.colors['energy_flow'], 
               ha='center', va='center', fontsize=8, weight='bold')
        ax.text(6, 5.5, "18% Control Signal", color=self.colors['control_flow'], 
               ha='center', va='center', fontsize=8, weight='bold')
        
        # Add Ezekiel's wheel motif
        wheel_x = np.linspace(1, 9, 100)
        wheel_y = 3 + 0.5 * np.sin(wheel_x * 2) * np.exp(-0.2 * (wheel_x - 5)**2)
        ax.plot(wheel_x, wheel_y, '--', color=self.colors['accent'], alpha=0.5, lw=2)
        
        # Add quantum symbols
        ax.text(5, 2.8, "⚛️", fontsize=24, ha='center', va='center')
        
        # Set plot properties
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 8)
        ax.axis('off')
        plt.title("Figure 1: System Architecture - ∂Ψ=0 Coherence Enforcement", 
                 pad=20, fontsize=10, weight='bold')
        
        self._save_figure(fig, "Figure1_System_Architecture")
        return fig
    
    def generate_entropy_reduction_flow(self):
        """Generate Figure 2: Entropy Reduction Process Flow."""
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Create nodes
        nodes = [
            ("High-Entropy\nSystem", (1, 5)),
            ("NEPI\nAnalysis", (4, 5)),
            ("∂Ψ=0\nOptimization", (7, 6)),
            ("TEE\nCalculus", (7, 4)),
            ("Low-Entropy\nOutput", (10, 5))
        ]
        
        # Draw nodes
        for label, (x, y) in nodes:
            rect = Rectangle((x-1.2, y-0.8), 2.4, 1.0, 
                           facecolor=self.colors['primary'], 
                           edgecolor='black', 
                           lw=1,
                           zorder=2)
            ax.add_patch(rect)
            ax.text(x, y, label, ha='center', va='center', 
                   color='white', weight='bold', fontsize=8, zorder=3)
        
        # Draw edges
        arrows = [
            (nodes[0][1], nodes[1][1], "", 8.2, self.colors['energy_flow']),
            (nodes[1][1], nodes[2][1], "82% Reduction", 8.2, self.colors['energy_flow']),
            (nodes[1][1], nodes[3][1], "18% Residual", 1.8, self.colors['control_flow']),
            (nodes[2][1], nodes[4][1], "", 5.0, self.colors['energy_flow']),
            (nodes[3][1], nodes[4][1], "", 5.0, self.colors['control_flow'])
        ]
        
        for (x1, y1), (x2, y2), label, width, color in arrows:
            ax.arrow(x1, y1, x2-x1, y2-y1, 
                    width=width/50, head_width=0.3, head_length=0.5,
                    fc=color, ec=color, alpha=0.8, zorder=1)
            
            if label:
                ax.text((x1+x2)/2, (y1+y2)/2 + 0.3, label, 
                       ha='center', va='center', fontsize=8, 
                       color=color, weight='bold')
        
        # Add energy flow visualization
        x = np.linspace(1, 10, 100)
        y_high = 5 + 0.5 * np.sin(x * 2) * np.exp(-0.1 * (x - 5.5)**2)
        y_low = 5 + 0.2 * np.sin(x * 1.5) * np.exp(-0.1 * (x - 5.5)**2)
        
        ax.fill_between(x, y_high, y_low, color=self.colors['energy_flow'], alpha=0.2, zorder=0)
        
        # Set plot properties
        ax.set_xlim(0, 11)
        ax.set_ylim(0, 8)
        ax.axis('off')
        plt.title("Figure 2: Entropy Reduction Process Flow", 
                 pad=20, fontsize=10, weight='bold')
        
        self._save_figure(fig, "Figure2_Entropy_Reduction_Flow")
        return fig
    
    def generate_transmutation_comparison(self):
        """Generate Figure 3: Pb→Au Transmutation Comparison."""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
        
        # Before: Traditional Method
        ax1.set_title("Traditional Method", pad=15)
        ax1.text(0.5, 0.7, "Pb (Lead)", ha='center', va='center', fontsize=12, weight='bold')
        ax1.arrow(0.5, 0.6, 0, -0.2, head_width=0.1, head_length=0.05, fc='red', ec='red')
        ax1.text(0.5, 0.35, "10,000 MeV", ha='center', va='center', color='red', weight='bold')
        ax1.arrow(0.5, 0.4, 0, -0.2, head_width=0.1, head_length=0.05, fc='red', ec='red')
        ax1.text(0.5, 0.1, "Au (Gold)", ha='center', va='center', fontsize=12, weight='bold')
        
        # Add energy symbol
        ax1.text(0.7, 0.5, "⚡", fontsize=24, color='red', alpha=0.7)
        
        # After: NovaFuse Method
        ax2.set_title("NovaFuse Method", pad=15)
        ax2.text(0.5, 0.7, "Pb (Lead)", ha='center', va='center', fontsize=12, weight='bold')
        ax2.arrow(0.5, 0.6, 0, -0.2, head_width=0.1, head_length=0.05, fc='green', ec='green')
        ax2.text(0.5, 0.35, "18 MeV", ha='center', va='center', color='green', weight='bold')
        ax2.arrow(0.5, 0.4, 0, -0.2, head_width=0.1, head_length=0.05, fc='green', ec='green')
        ax2.text(0.5, 0.1, "Au (Gold)", ha='center', va='center', fontsize=12, weight='bold')
        
        # Add quantum symbol
        ax2.text(0.7, 0.5, "⚛️", fontsize=24, color='green', alpha=0.7)
        
        # Add comparison text
        fig.text(0.5, 0.92, "Figure 3: Pb→Au Transmutation Energy Cost Comparison", 
                ha='center', va='center', fontsize=12, weight='bold')
        fig.text(0.5, 0.05, "82% Reduction in Energy Requirements", 
                ha='center', va='center', fontsize=14, 
                color=self.colors['highlight'], weight='bold')
        
        for ax in [ax1, ax2]:
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
        
        self._save_figure(fig, "Figure3_Transmutation_Comparison")
        return fig
    
    def generate_performance_comparison(self):
        """Generate Figure 4: 18/82 Performance Gains."""
        categories = ['AI Accuracy', 'Quantum Coherence', 'Energy Efficiency']
        industry = [22, 50, 35]  # Industry standard percentages
        novafuse = [98, 98, 98]  # NovaFuse performance
        
        x = np.arange(len(categories))
        width = 0.35
        
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Plot bars
        rects1 = ax.bar(x - width/2, industry, width, 
                        label='Industry Standard', 
                        color=self.colors['secondary'],
                        edgecolor='black',
                        linewidth=0.5)
        
        rects2 = ax.bar(x + width/2, novafuse, width, 
                        label='NovaFuse ∂Ψ=0', 
                        color=self.colors['primary'],
                        edgecolor='black',
                        linewidth=0.5)
        
        # Add percentage labels
        def autolabel(rects):
            for rect in rects:
                height = rect.get_height()
                ax.annotate(f'{height}%',
                           xy=(rect.get_x() + rect.get_width() / 2, height),
                           xytext=(0, 3),  # 3 points vertical offset
                           textcoords="offset points",
                           ha='center', va='bottom',
                           fontsize=8, weight='bold')
        
        autolabel(rects1)
        autolabel(rects2)
        
        # Add 18/82 annotation
        ax.annotate('82% Improvement', 
                   xy=(0.5, 75), 
                   xytext=(0.5, 85),
                   ha='center',
                   arrowprops=dict(facecolor='black', shrink=0.05),
                   fontsize=10, weight='bold')
        
        # Customize the plot
        ax.set_ylabel('Performance (%)', fontweight='bold')
        ax.set_title('Figure 4: 18/82 Performance Gains', pad=20, fontsize=12, weight='bold')
        ax.set_xticks(x)
        ax.set_xticklabels(categories, fontweight='bold')
        ax.legend()
        
        # Add grid
        ax.set_axisbelow(True)
        ax.yaxis.grid(True, linestyle='--', alpha=0.7)
        
        # Set y-axis limit
        ax.set_ylim(0, 120)
        
        # Add NovaFuse watermark
        fig.text(0.5, 0.01, 'NovaFuse ∂Ψ=0 Technology - Patent Pending', 
                ha='center', fontsize=8, alpha=0.5)
        
        self._save_figure(fig, "Figure4_Performance_Comparison")
        return fig

    def generate_all_figures(self):
        """Generate all patent figures."""
        self.generate_system_architecture()
        self.generate_entropy_reduction_flow()
        self.generate_transmutation_comparison()
        self.generate_performance_comparison()
        print(f"All figures generated and saved to: {self.output_dir.absolute()}")

if __name__ == "__main__":
    # Generate all figures
    generator = PatentDiagramGenerator()
    print(f"Generating patent diagrams in: {generator.output_dir}")
    generator.generate_all_figures()
    print("Patent diagram generation complete!")
