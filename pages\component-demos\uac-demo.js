import React, { useState } from 'react';
import BasicDemoTemplate from '../../components/demo-framework/BasicDemoTemplate';
import EcosystemSlotSelector from '../../components/demo-framework/EcosystemSlotSelector';
import PageWithSidebar from '../../components/PageWithSidebar';
import ecosystemSlots from '../../data/ecosystem-slots';
import Link from 'next/link';

export default function EnhancedUACDemo() {
  // State for demo interaction
  const [selectedComplianceFramework, setSelectedComplianceFramework] = useState('hipaa');
  const [complianceStatus, setComplianceStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Relevant collaborative roles for UAC
  const relevantSlots = [
    ecosystemSlots.find(slot => slot.id === 'SP-02'), // Compliance Automation
    ecosystemSlots.find(slot => slot.id === 'SP-01'), // Cross-Framework Mapping
    ecosystemSlots.find(slot => slot.id === 'RC-01')  // GDPR Compliance
  ];

  // SEO metadata
  const pageProps = {
    title: 'Universal API Connector (UAC) Demo - NovaFuse',
    description: 'Experience the power of NovaFuse\'s Universal API Connector (UAC) with our interactive demo. Test compliance checks, connect to APIs, and see the UAC in action.',
    keywords: 'UAC demo, Universal API Connector, API integration, compliance checks, HIPAA, GDPR, PCI DSS, NovaFuse demo',
    canonical: 'https://novafuse.io/component-demos/uac-demo',
    ogImage: '/images/demos/uac-demo-og.png'
  };

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'UAC Demo', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Key Features', href: '#features' },
      { label: 'Interactive Demo', href: '#interactive-demo' },
      { label: 'Use Cases', href: '#use-cases' },
      { label: 'API Documentation', href: '#api-docs' }
    ]},
    { type: 'category', label: 'Related Demos', items: [
      { label: 'Regulatory Compliance API', href: '/api-demos/regulatory-compliance-api-demo' },
      { label: 'Privacy Management API', href: '/api-demos/privacy-management-api-demo' },
      { label: 'Partner Network', href: '/component-demos/partner-network-demo' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'UAC Documentation', href: '/documentation/uac' },
      { label: 'GitHub Repository', href: 'https://github.com/novafuse/uac-demo' },
      { label: 'Schedule Demo', href: '/contact?demo=uac' }
    ]}
  ];

  // Simulate compliance check
  const runComplianceCheck = (framework) => {
    setIsLoading(true);
    setComplianceStatus(null);

    // Simulate API call delay
    setTimeout(() => {
      let result;

      switch (framework) {
        case 'hipaa':
          result = {
            compliant: false,
            score: 65,
            issues: [
              { severity: 'high', message: 'Unencrypted PHI detected in patient records' },
              { severity: 'medium', message: 'Missing authorization for data access' },
              { severity: 'low', message: 'Audit logging incomplete for data access events' }
            ],
            recommendations: [
              'Implement encryption for all PHI data',
              'Add authorization checks before accessing patient data',
              'Enable comprehensive audit logging for all data access'
            ]
          };
          break;
        case 'gdpr':
          result = {
            compliant: false,
            score: 72,
            issues: [
              { severity: 'high', message: 'Missing explicit consent for data processing' },
              { severity: 'medium', message: 'Data retention period not specified' },
              { severity: 'low', message: 'Incomplete data subject access request handling' }
            ],
            recommendations: [
              'Implement explicit consent collection for all data processing',
              'Define and enforce data retention policies',
              'Enhance data subject access request handling procedures'
            ]
          };
          break;
        case 'pci':
          result = {
            compliant: false,
            score: 58,
            issues: [
              { severity: 'high', message: 'Unmasked credit card numbers detected' },
              { severity: 'high', message: 'CVV codes stored in database' },
              { severity: 'medium', message: 'Weak encryption for payment data' }
            ],
            recommendations: [
              'Implement card number masking for all displays and storage',
              'Never store CVV codes after transaction authorization',
              'Upgrade encryption to meet PCI DSS requirements'
            ]
          };
          break;
        default:
          result = {
            compliant: true,
            score: 95,
            issues: [
              { severity: 'low', message: 'Minor documentation issues detected' }
            ],
            recommendations: [
              'Update compliance documentation for completeness'
            ]
          };
      }

      setComplianceStatus(result);
      setIsLoading(false);
    }, 1500);
  };

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <BasicDemoTemplate
        title="Universal API Connector (UAC)"
        description="The world's most versatile API connector with built-in compliance intelligence"
        demoType="technical"
      >
        <div className="space-y-8">
          {/* Overview Section */}
          <div id="overview" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <div className="flex justify-center mb-4">
              <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
                <span className="mr-2">🔌</span>
                <span>4 PATENTS PENDING</span>
              </div>
            </div>

            <h2 className="text-2xl font-bold mb-4 text-center">What is the Universal API Connector?</h2>
            <p className="text-gray-300 mb-6 text-center max-w-3xl mx-auto">
              The Universal API Connector (UAC) is NovaFuse's revolutionary technology that connects to any API,
              normalizes data, and applies compliance rules in real-time. GRC is just our first use case -
              the UAC has unlimited potential applications.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                    <span className="text-2xl">🔄</span>
                  </div>
                  <h3 className="text-xl font-semibold">Universal Connectivity</h3>
                </div>
                <p className="text-gray-300">
                  Connect to any API regardless of type (REST, GraphQL, SOAP, gRPC) with a unified interface.
                  Transform data between formats seamlessly.
                </p>
              </div>

              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                    <span className="text-2xl">🛡️</span>
                  </div>
                  <h3 className="text-xl font-semibold">Compliance Engine</h3>
                </div>
                <p className="text-gray-300">
                  Apply compliance rules to API data in real-time. Support for HIPAA, GDPR, PCI DSS, SOC2, ISO27001, and more.
                  Automatic data classification and validation.
                </p>
              </div>

              <div className="bg-gray-700 p-6 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900 flex items-center justify-center mr-4">
                    <span className="text-2xl">🔐</span>
                  </div>
                  <h3 className="text-xl font-semibold">Security First</h3>
                </div>
                <p className="text-gray-300">
                  Enterprise-grade security with role-based access control, audit logging, and encryption.
                  Support for various authentication methods including OAuth, JWT, and API keys.
                </p>
              </div>
            </div>
          </div>

          {/* Interactive Demo Section */}
          <div id="interactive-demo" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">Interactive Compliance Check Demo</h2>
            <p className="text-gray-300 mb-6">
              Experience the power of the UAC's compliance engine with this interactive demo.
              Select a compliance framework and run a check to see how the UAC identifies compliance issues and provides recommendations.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div
                className={`p-4 rounded-lg cursor-pointer border ${
                  selectedComplianceFramework === 'hipaa'
                    ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                    : 'border-gray-700 hover:border-gray-500 bg-gray-700'
                }`}
                onClick={() => setSelectedComplianceFramework('hipaa')}
              >
                <div className="flex items-center mb-2">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                    HIPAA
                  </span>
                  <span className="font-medium">Healthcare Privacy</span>
                </div>
                <p className="text-sm text-gray-400">Check compliance with healthcare privacy regulations</p>
              </div>

              <div
                className={`p-4 rounded-lg cursor-pointer border ${
                  selectedComplianceFramework === 'gdpr'
                    ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                    : 'border-gray-700 hover:border-gray-500 bg-gray-700'
                }`}
                onClick={() => setSelectedComplianceFramework('gdpr')}
              >
                <div className="flex items-center mb-2">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                    GDPR
                  </span>
                  <span className="font-medium">Data Protection</span>
                </div>
                <p className="text-sm text-gray-400">Check compliance with EU data protection regulations</p>
              </div>

              <div
                className={`p-4 rounded-lg cursor-pointer border ${
                  selectedComplianceFramework === 'pci'
                    ? 'border-blue-500 bg-blue-900 bg-opacity-30'
                    : 'border-gray-700 hover:border-gray-500 bg-gray-700'
                }`}
                onClick={() => setSelectedComplianceFramework('pci')}
              >
                <div className="flex items-center mb-2">
                  <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                    PCI DSS
                  </span>
                  <span className="font-medium">Payment Security</span>
                </div>
                <p className="text-sm text-gray-400">Check compliance with payment card industry standards</p>
              </div>
            </div>

            <div className="mb-6">
              <button
                className={`px-6 py-3 rounded-lg font-bold text-white ${
                  isLoading
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
                onClick={() => runComplianceCheck(selectedComplianceFramework)}
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Running Compliance Check...
                  </span>
                ) : (
                  `Run ${selectedComplianceFramework.toUpperCase()} Compliance Check`
                )}
              </button>
            </div>

            {/* Compliance Check Results */}
            {complianceStatus && (
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold">Compliance Check Results</h3>
                  <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                    complianceStatus.compliant
                      ? 'bg-green-900 text-green-300'
                      : 'bg-red-900 text-red-300'
                  }`}>
                    {complianceStatus.compliant ? 'COMPLIANT' : 'NON-COMPLIANT'}
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400">Compliance Score:</span>
                    <span className="font-bold">{complianceStatus.score}/100</span>
                  </div>
                  <div className="w-full bg-gray-800 rounded-full h-2.5">
                    <div
                      className={`h-2.5 rounded-full ${
                        complianceStatus.score >= 90
                          ? 'bg-green-600'
                          : complianceStatus.score >= 70
                            ? 'bg-yellow-600'
                            : 'bg-red-600'
                      }`}
                      style={{ width: `${complianceStatus.score}%` }}
                    ></div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Compliance Issues</h4>
                    <ul className="space-y-2">
                      {complianceStatus.issues.map((issue, index) => (
                        <li key={index} className="flex items-start">
                          <span className={`mr-2 ${
                            issue.severity === 'high'
                              ? 'text-red-500'
                              : issue.severity === 'medium'
                                ? 'text-yellow-500'
                                : 'text-blue-500'
                          }`}>
                            {issue.severity === 'high' ? '⚠️' : issue.severity === 'medium' ? '⚠' : 'ℹ️'}
                          </span>
                          <span className="text-gray-300">{issue.message}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Recommendations</h4>
                    <ul className="space-y-2">
                      {complianceStatus.recommendations.map((recommendation, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-green-500 mr-2">✓</span>
                          <span className="text-gray-300">{recommendation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-600">
                  <p className="text-sm text-gray-400">
                    In a real implementation, the UAC would provide detailed compliance reports, remediation guidance, and integration with your existing systems.
                  </p>
                </div>
              </div>
            )}

            <div className="mt-6 text-center">
              <Link href="http://localhost:3030" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300">
                Access the full UAC demo environment →
              </Link>
            </div>
          </div>

          {/* Relevant Partner Network Roles */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">UAC in the NovaFuse Partner Network</h2>
            <p className="text-gray-300 mb-6">
              The Universal API Connector is a core component of the NovaFuse Partner Network.
              Here are some of the key collaborative roles that leverage the UAC:
            </p>

            <div className="space-y-4">
              {relevantSlots.map(slot => (
                <div key={slot.id} className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                      {slot.id}
                    </span>
                    <h3 className="text-xl font-semibold">{slot.name}</h3>
                  </div>
                  <p className="text-gray-300 mb-3">{slot.description}</p>

                  <div className="flex flex-wrap gap-1 mt-2">
                    {slot.tags.map((tag, index) => (
                      <span key={index} className="bg-gray-800 text-gray-300 px-2 py-0.5 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 text-center">
              <Link href="/component-demos/partner-network-demo" className="text-blue-400 hover:text-blue-300">
                Explore the full NovaFuse Partner Network →
              </Link>
            </div>
          </div>

          {/* Use Cases Section */}
          <div id="use-cases" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-6">Beyond GRC: Unlimited Applications</h2>
            <p className="text-gray-300 mb-6">
              While the UAC excels at GRC use cases, its potential applications extend far beyond compliance.
              Here are just a few examples of what you can build with the Universal API Connector:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Healthcare Interoperability</h4>
                <p className="text-gray-300 text-sm">
                  Connect EHRs, HIEs, and healthcare apps with HIPAA-compliant data exchange.
                  Support for FHIR, HL7, and other healthcare standards.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Financial Data Aggregation</h4>
                <p className="text-gray-300 text-sm">
                  Securely connect to banking, investment, and payment APIs with PCI DSS and SOX compliance.
                  Real-time transaction monitoring and fraud detection.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">IoT Data Management</h4>
                <p className="text-gray-300 text-sm">
                  Connect to IoT devices and platforms with secure data collection and processing.
                  Apply compliance rules to IoT data streams in real-time.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Supply Chain Integration</h4>
                <p className="text-gray-300 text-sm">
                  Connect to supplier, logistics, and inventory systems with secure data exchange.
                  Ensure compliance with industry regulations and standards.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">Customer Data Platform</h4>
                <p className="text-gray-300 text-sm">
                  Aggregate customer data from multiple sources with GDPR and CCPA compliance.
                  Create a unified customer view while maintaining privacy and security.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-lg font-semibold mb-2">AI/ML Data Pipeline</h4>
                <p className="text-gray-300 text-sm">
                  Connect to data sources and AI/ML platforms with secure data processing.
                  Ensure compliance with AI ethics guidelines and data protection regulations.
                </p>
              </div>
            </div>
          </div>

          {/* API Documentation Section */}
          <div id="api-docs" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">API Documentation</h2>
            <p className="text-gray-300 mb-6">
              The Universal API Connector provides a comprehensive API for connecting to various data sources,
              applying compliance rules, and managing data flows. Here's a sample of the available endpoints:
            </p>

            <div className="space-y-4">
              <div className="border border-gray-700 rounded-lg overflow-hidden">
                <div className="bg-gray-700 p-3 flex items-center">
                  <span className="px-2 py-1 rounded text-xs font-bold mr-3 bg-green-900 text-green-300">
                    GET
                  </span>
                  <code className="font-mono text-sm">/api/connectors</code>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-400 mb-3">Get a list of all configured API connectors</p>
                </div>
              </div>

              <div className="border border-gray-700 rounded-lg overflow-hidden">
                <div className="bg-gray-700 p-3 flex items-center">
                  <span className="px-2 py-1 rounded text-xs font-bold mr-3 bg-blue-900 text-blue-300">
                    POST
                  </span>
                  <code className="font-mono text-sm">/api/connectors</code>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-400 mb-3">Create a new API connector configuration</p>
                </div>
              </div>

              <div className="border border-gray-700 rounded-lg overflow-hidden">
                <div className="bg-gray-700 p-3 flex items-center">
                  <span className="px-2 py-1 rounded text-xs font-bold mr-3 bg-blue-900 text-blue-300">
                    POST
                  </span>
                  <code className="font-mono text-sm">/api/compliance/check</code>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-400 mb-3">Check data against compliance rules</p>
                </div>
              </div>

              <div className="border border-gray-700 rounded-lg overflow-hidden">
                <div className="bg-gray-700 p-3 flex items-center">
                  <span className="px-2 py-1 rounded text-xs font-bold mr-3 bg-green-900 text-green-300">
                    GET
                  </span>
                  <code className="font-mono text-sm">/api/compliance/frameworks</code>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-400 mb-3">Get a list of supported compliance frameworks</p>
                </div>
              </div>

              <div className="border border-gray-700 rounded-lg overflow-hidden">
                <div className="bg-gray-700 p-3 flex items-center">
                  <span className="px-2 py-1 rounded text-xs font-bold mr-3 bg-blue-900 text-blue-300">
                    POST
                  </span>
                  <code className="font-mono text-sm">/api/data/transform</code>
                </div>
                <div className="p-4">
                  <p className="text-sm text-gray-400 mb-3">Transform data between different formats</p>
                </div>
              </div>
            </div>

            <div className="mt-6 text-center">
              <a
                href="https://github.com/novafuse/uac-demo/blob/main/src/swagger.yaml"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300"
              >
                View full API documentation on GitHub →
              </a>
            </div>
          </div>

          {/* CTA Section */}
          <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold mb-3">Ready to Experience the UAC?</h2>
            <p className="text-lg mb-6 max-w-3xl mx-auto">
              Join our partner ecosystem and be among the first to leverage the power of the Universal API Connector.
              Transform how you connect, integrate, and comply with regulations.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                href="/partner-onboarding"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold"
              >
                Become a Partner
              </Link>
              <a
                href="https://github.com/novafuse/uac-demo"
                target="_blank"
                rel="noopener noreferrer"
                className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900"
              >
                Get Demo Code
              </a>
            </div>
          </div>
        </div>
      </BasicDemoTemplate>
    </PageWithSidebar>
  );
}
