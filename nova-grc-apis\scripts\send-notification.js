/**
 * NovaFuse Milestone Notification Script
 * 
 * This script sends email notifications when project milestones are completed.
 * It can be called from the command line or integrated into build/deployment processes.
 */

const nodemailer = require('nodemailer');
require('dotenv').config();

// Configure email transport
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASSWORD || 'your-app-password'
  }
});

/**
 * Send a milestone notification email
 * @param {string} milestone - The milestone that was completed
 * @param {string} details - Additional details about the milestone
 * @param {string} recipient - Email address to send the notification to
 */
async function sendMilestoneNotification(milestone, details, recipient) {
  // Email content
  const mailOptions = {
    from: process.env.EMAIL_USER || '<EMAIL>',
    to: recipient,
    subject: `NovaFuse Milestone Completed: ${milestone}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2 style="color: #2c3e50; border-bottom: 1px solid #eee; padding-bottom: 10px;">NovaFuse Milestone Completed</h2>
        
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #27ae60;">${milestone}</h3>
          <p style="color: #555;">${details}</p>
        </div>
        
        <p style="color: #555;">You can review the completed work in the development environment.</p>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
          <p>This is an automated notification from the NovaFuse development team.</p>
        </div>
      </div>
    `
  };

  try {
    // Send email
    const info = await transporter.sendMail(mailOptions);
    console.log(`Milestone notification sent: ${milestone}`);
    console.log(`Message ID: ${info.messageId}`);
    return true;
  } catch (error) {
    console.error('Error sending milestone notification:', error);
    return false;
  }
}

// If script is run directly from command line
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const milestone = args[0] || 'Unknown Milestone';
  const details = args[1] || 'No additional details provided.';
  const recipient = args[2] || process.env.NOTIFICATION_RECIPIENT || '<EMAIL>';

  // Send notification
  sendMilestoneNotification(milestone, details, recipient)
    .then(result => {
      if (result) {
        console.log('Notification sent successfully');
        process.exit(0);
      } else {
        console.error('Failed to send notification');
        process.exit(1);
      }
    })
    .catch(err => {
      console.error('Error:', err);
      process.exit(1);
    });
} else {
  // Export for use as a module
  module.exports = { sendMilestoneNotification };
}
