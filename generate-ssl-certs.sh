#!/bin/bash

# Generate SSL certificates for development
# This script generates self-signed SSL certificates for development

# Create the SSL directory
mkdir -p nginx/ssl

# Generate a private key
openssl genrsa -out nginx/ssl/novafuse.key 2048

# Generate a certificate signing request
openssl req -new -key nginx/ssl/novafuse.key -out nginx/ssl/novafuse.csr -subj "/C=US/ST=State/L=City/O=NovaFuse/CN=novafuse.io"

# Generate a self-signed certificate
openssl x509 -req -days 365 -in nginx/ssl/novafuse.csr -signkey nginx/ssl/novafuse.key -out nginx/ssl/novafuse.crt

echo "SSL certificates generated successfully!"
