# Trinity of Trust Deployment Script
Write-Host "🚀 DEPLOYING TRINITY OF TRUST - MAXIMUM ROBUSTNESS" -ForegroundColor Green
Write-Host "=============================================="

# Stop any existing containers
Write-Host "🧹 Cleaning up existing containers..." -ForegroundColor Yellow
docker stop kethernet-blockchain novashield-security 2>$null
docker rm kethernet-blockchain novashield-security 2>$null

# Deploy KetherNet Blockchain (Coherium)
Write-Host "🔗 Deploying KetherNet Blockchain with Coherium..." -ForegroundColor Cyan
docker run -d --name kethernet-blockchain -p 8080:8080 -p 8081:8081 -p 8082:8082 -v ${PWD}:/app -w /app node:18-alpine sh -c "npm install express cors && node kethernet-server.js"

Start-Sleep -Seconds 5

# Deploy NovaShield Security
Write-Host "🛡️ Deploying NovaShield Security Platform..." -ForegroundColor Cyan  
docker run -d --name novashield-security -p 8085:8085 -p 8086:8086 -v ${PWD}:/app -w /app node:18-alpine sh -c "npm install express cors && node novashield-server.js"

Start-Sleep -Seconds 5

# Verify deployments
Write-Host "✅ Verifying Trinity deployments..." -ForegroundColor Green

Write-Host "Testing KetherNet Blockchain..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/health" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ KetherNet Blockchain: OPERATIONAL" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ KetherNet Blockchain: Failed to start" -ForegroundColor Red
}

Write-Host "Testing NovaShield Security..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8085/health" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ NovaShield Security: OPERATIONAL" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ NovaShield Security: Failed to start" -ForegroundColor Red
}

Write-Host "Testing NovaDNA Identity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8083/health" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ NovaDNA Identity: OPERATIONAL" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ NovaDNA Identity: Not responding" -ForegroundColor Red
}

Write-Host "=============================================="
Write-Host "🎯 TRINITY OF TRUST DEPLOYMENT COMPLETE!" -ForegroundColor Green
Write-Host "Ready for consciousness filtering tests..." -ForegroundColor Cyan
