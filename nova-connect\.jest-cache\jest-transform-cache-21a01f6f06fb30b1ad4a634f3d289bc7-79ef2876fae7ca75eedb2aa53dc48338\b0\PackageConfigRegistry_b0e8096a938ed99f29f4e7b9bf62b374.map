{"version": 3, "names": ["fs", "require", "promises", "path", "v4", "uuidv4", "NodeCache", "PackageConfigRegistry", "constructor", "dataDir", "join", "__dirname", "packagesDir", "packagesFile", "tenantMappingFile", "cache", "stdTTL", "checkperiod", "tiers", "CORE", "SECURE", "ENTERPRISE", "AI_BOOST", "ensureDataDir", "mkdir", "recursive", "access", "error", "code", "writeFile", "JSON", "stringify", "getDefaultPackages", "console", "loadData", "filePath", "data", "readFile", "parse", "saveData", "id", "name", "description", "tier", "features", "limits", "connections", "operations_per_day", "workflows", "actions_per_workflow", "scheduled_workflows", "alerts", "getAllPackages", "cache<PERSON>ey", "cachedPackages", "get", "packages", "set", "getPackageById", "cachedPackage", "pkg", "find", "p", "Error", "createPackage", "packageData", "some", "push", "del", "updatePackage", "index", "findIndex", "deletePackage", "splice", "getAllTenantMappings", "cachedMappings", "mappings", "getTenantMapping", "tenantId", "cachedMapping", "mapping", "m", "packageId", "customFeatures", "customLimits", "setTenantMapping", "deleteTenantMapping", "hasTenantFeatureAccess", "featureId", "includes", "getTenantFeatureLimit", "limit<PERSON>ey", "undefined", "getTenantAvailableFeatures", "Set", "clearCache", "flushAll", "module", "exports"], "sources": ["PackageConfigRegistry.js"], "sourcesContent": ["/**\n * Package Configuration Registry\n * \n * This service manages the package configuration registry for NovaConnect UAC.\n * It provides functionality for defining feature sets by package and mapping tenants to packages.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst { v4: uuidv4 } = require('uuid');\nconst NodeCache = require('node-cache');\n\nclass PackageConfigRegistry {\n  constructor(dataDir = path.join(__dirname, '../data')) {\n    this.dataDir = dataDir;\n    this.packagesDir = path.join(this.dataDir, 'packages');\n    this.packagesFile = path.join(this.packagesDir, 'packages.json');\n    this.tenantMappingFile = path.join(this.packagesDir, 'tenant_package_mapping.json');\n    \n    // Initialize cache with 5-minute TTL\n    this.cache = new NodeCache({ stdTTL: 300, checkperiod: 60 });\n    \n    // Define package tiers\n    this.tiers = {\n      CORE: 'core',\n      SECURE: 'secure',\n      ENTERPRISE: 'enterprise',\n      AI_BOOST: 'ai_boost'\n    };\n    \n    this.ensureDataDir();\n  }\n  \n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.packagesDir, { recursive: true });\n      \n      // Initialize packages file if it doesn't exist\n      try {\n        await fs.access(this.packagesFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          // File doesn't exist, create it with default packages\n          await fs.writeFile(this.packagesFile, JSON.stringify(this.getDefaultPackages(), null, 2));\n        } else {\n          throw error;\n        }\n      }\n      \n      // Initialize tenant mapping file if it doesn't exist\n      try {\n        await fs.access(this.tenantMappingFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          // File doesn't exist, create it with empty array\n          await fs.writeFile(this.tenantMappingFile, JSON.stringify([]));\n        } else {\n          throw error;\n        }\n      }\n    } catch (error) {\n      console.error('Error creating packages directory:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Load data from file\n   */\n  async loadData(filePath) {\n    try {\n      const data = await fs.readFile(filePath, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      if (error.code === 'ENOENT') {\n        // File doesn't exist, return empty array\n        return [];\n      }\n      console.error(`Error loading data from ${filePath}:`, error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Save data to file\n   */\n  async saveData(filePath, data) {\n    try {\n      await fs.writeFile(filePath, JSON.stringify(data, null, 2));\n    } catch (error) {\n      console.error(`Error saving data to ${filePath}:`, error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get default packages\n   */\n  getDefaultPackages() {\n    return [\n      {\n        id: 'core',\n        name: 'NovaConnect Core',\n        description: 'Basic functionality for connecting APIs and automating workflows',\n        tier: this.tiers.CORE,\n        features: [\n          'core.basic_connectors',\n          'core.basic_workflows',\n          'core.basic_authentication',\n          'core.basic_monitoring',\n          'core.basic_logging'\n        ],\n        limits: {\n          connections: 10,\n          operations_per_day: 1000,\n          workflows: 5,\n          actions_per_workflow: 10,\n          scheduled_workflows: 2,\n          alerts: 5\n        }\n      },\n      {\n        id: 'secure',\n        name: 'NovaConnect Secure',\n        description: 'Enhanced security and compliance features',\n        tier: this.tiers.SECURE,\n        features: [\n          'core.basic_connectors',\n          'core.basic_workflows',\n          'core.basic_authentication',\n          'core.basic_monitoring',\n          'core.basic_logging',\n          'security.encryption',\n          'security.audit_logs',\n          'security.compliance_checks',\n          'security.vulnerability_scanning',\n          'security.access_controls'\n        ],\n        limits: {\n          connections: 25,\n          operations_per_day: 5000,\n          workflows: 15,\n          actions_per_workflow: 20,\n          scheduled_workflows: 5,\n          alerts: 15\n        }\n      },\n      {\n        id: 'enterprise',\n        name: 'NovaConnect Enterprise',\n        description: 'Advanced enterprise features for large-scale deployments',\n        tier: this.tiers.ENTERPRISE,\n        features: [\n          'core.basic_connectors',\n          'core.basic_workflows',\n          'core.basic_authentication',\n          'core.basic_monitoring',\n          'core.basic_logging',\n          'security.encryption',\n          'security.audit_logs',\n          'security.compliance_checks',\n          'security.vulnerability_scanning',\n          'security.access_controls',\n          'enterprise.advanced_connectors',\n          'enterprise.advanced_workflows',\n          'enterprise.advanced_authentication',\n          'enterprise.advanced_monitoring',\n          'enterprise.advanced_logging',\n          'enterprise.sla',\n          'enterprise.priority_support'\n        ],\n        limits: {\n          connections: 100,\n          operations_per_day: 25000,\n          workflows: 50,\n          actions_per_workflow: 50,\n          scheduled_workflows: 20,\n          alerts: 50\n        }\n      },\n      {\n        id: 'ai_boost',\n        name: 'NovaConnect AI Boost',\n        description: 'AI-powered features for intelligent automation',\n        tier: this.tiers.AI_BOOST,\n        features: [\n          'core.basic_connectors',\n          'core.basic_workflows',\n          'core.basic_authentication',\n          'core.basic_monitoring',\n          'core.basic_logging',\n          'security.encryption',\n          'security.audit_logs',\n          'security.compliance_checks',\n          'security.vulnerability_scanning',\n          'security.access_controls',\n          'enterprise.advanced_connectors',\n          'enterprise.advanced_workflows',\n          'enterprise.advanced_authentication',\n          'enterprise.advanced_monitoring',\n          'enterprise.advanced_logging',\n          'enterprise.sla',\n          'enterprise.priority_support',\n          'ai.predictive_analytics',\n          'ai.anomaly_detection',\n          'ai.natural_language_processing',\n          'ai.automated_remediation',\n          'ai.intelligent_recommendations'\n        ],\n        limits: {\n          connections: -1, // Unlimited\n          operations_per_day: -1, // Unlimited\n          workflows: -1, // Unlimited\n          actions_per_workflow: -1, // Unlimited\n          scheduled_workflows: -1, // Unlimited\n          alerts: -1 // Unlimited\n        }\n      }\n    ];\n  }\n  \n  /**\n   * Get all packages\n   */\n  async getAllPackages() {\n    // Check cache first\n    const cacheKey = 'all_packages';\n    const cachedPackages = this.cache.get(cacheKey);\n    \n    if (cachedPackages) {\n      return cachedPackages;\n    }\n    \n    // Load from file\n    const packages = await this.loadData(this.packagesFile);\n    \n    // Cache the result\n    this.cache.set(cacheKey, packages);\n    \n    return packages;\n  }\n  \n  /**\n   * Get package by ID\n   */\n  async getPackageById(id) {\n    // Check cache first\n    const cacheKey = `package_${id}`;\n    const cachedPackage = this.cache.get(cacheKey);\n    \n    if (cachedPackage) {\n      return cachedPackage;\n    }\n    \n    // Load from file\n    const packages = await this.getAllPackages();\n    const pkg = packages.find(p => p.id === id);\n    \n    if (!pkg) {\n      throw new Error(`Package with ID ${id} not found`);\n    }\n    \n    // Cache the result\n    this.cache.set(cacheKey, pkg);\n    \n    return pkg;\n  }\n  \n  /**\n   * Create a new package\n   */\n  async createPackage(packageData) {\n    const packages = await this.getAllPackages();\n    \n    // Check if package with the same ID already exists\n    if (packages.some(p => p.id === packageData.id)) {\n      throw new Error(`Package with ID ${packageData.id} already exists`);\n    }\n    \n    // Add the new package\n    packages.push(packageData);\n    \n    // Save to file\n    await this.saveData(this.packagesFile, packages);\n    \n    // Invalidate cache\n    this.cache.del('all_packages');\n    \n    return packageData;\n  }\n  \n  /**\n   * Update a package\n   */\n  async updatePackage(id, packageData) {\n    const packages = await this.getAllPackages();\n    \n    // Find the package\n    const index = packages.findIndex(p => p.id === id);\n    \n    if (index === -1) {\n      throw new Error(`Package with ID ${id} not found`);\n    }\n    \n    // Update the package\n    packages[index] = { ...packages[index], ...packageData };\n    \n    // Save to file\n    await this.saveData(this.packagesFile, packages);\n    \n    // Invalidate cache\n    this.cache.del('all_packages');\n    this.cache.del(`package_${id}`);\n    \n    return packages[index];\n  }\n  \n  /**\n   * Delete a package\n   */\n  async deletePackage(id) {\n    const packages = await this.getAllPackages();\n    \n    // Find the package\n    const index = packages.findIndex(p => p.id === id);\n    \n    if (index === -1) {\n      throw new Error(`Package with ID ${id} not found`);\n    }\n    \n    // Remove the package\n    packages.splice(index, 1);\n    \n    // Save to file\n    await this.saveData(this.packagesFile, packages);\n    \n    // Invalidate cache\n    this.cache.del('all_packages');\n    this.cache.del(`package_${id}`);\n    \n    return true;\n  }\n  \n  /**\n   * Get all tenant-to-package mappings\n   */\n  async getAllTenantMappings() {\n    // Check cache first\n    const cacheKey = 'all_tenant_mappings';\n    const cachedMappings = this.cache.get(cacheKey);\n    \n    if (cachedMappings) {\n      return cachedMappings;\n    }\n    \n    // Load from file\n    const mappings = await this.loadData(this.tenantMappingFile);\n    \n    // Cache the result\n    this.cache.set(cacheKey, mappings);\n    \n    return mappings;\n  }\n  \n  /**\n   * Get tenant-to-package mapping by tenant ID\n   */\n  async getTenantMapping(tenantId) {\n    // Check cache first\n    const cacheKey = `tenant_mapping_${tenantId}`;\n    const cachedMapping = this.cache.get(cacheKey);\n    \n    if (cachedMapping) {\n      return cachedMapping;\n    }\n    \n    // Load from file\n    const mappings = await this.getAllTenantMappings();\n    const mapping = mappings.find(m => m.tenantId === tenantId);\n    \n    if (!mapping) {\n      // Return default mapping\n      return {\n        tenantId,\n        packageId: 'core', // Default package\n        customFeatures: [],\n        customLimits: {}\n      };\n    }\n    \n    // Cache the result\n    this.cache.set(cacheKey, mapping);\n    \n    return mapping;\n  }\n  \n  /**\n   * Create or update tenant-to-package mapping\n   */\n  async setTenantMapping(tenantId, packageId, customFeatures = [], customLimits = {}) {\n    const mappings = await this.getAllTenantMappings();\n    \n    // Find the mapping\n    const index = mappings.findIndex(m => m.tenantId === tenantId);\n    \n    // Create or update the mapping\n    const mapping = {\n      tenantId,\n      packageId,\n      customFeatures,\n      customLimits\n    };\n    \n    if (index === -1) {\n      // Add new mapping\n      mappings.push(mapping);\n    } else {\n      // Update existing mapping\n      mappings[index] = mapping;\n    }\n    \n    // Save to file\n    await this.saveData(this.tenantMappingFile, mappings);\n    \n    // Invalidate cache\n    this.cache.del('all_tenant_mappings');\n    this.cache.del(`tenant_mapping_${tenantId}`);\n    \n    return mapping;\n  }\n  \n  /**\n   * Delete tenant-to-package mapping\n   */\n  async deleteTenantMapping(tenantId) {\n    const mappings = await this.getAllTenantMappings();\n    \n    // Find the mapping\n    const index = mappings.findIndex(m => m.tenantId === tenantId);\n    \n    if (index === -1) {\n      throw new Error(`Tenant mapping for tenant ${tenantId} not found`);\n    }\n    \n    // Remove the mapping\n    mappings.splice(index, 1);\n    \n    // Save to file\n    await this.saveData(this.tenantMappingFile, mappings);\n    \n    // Invalidate cache\n    this.cache.del('all_tenant_mappings');\n    this.cache.del(`tenant_mapping_${tenantId}`);\n    \n    return true;\n  }\n  \n  /**\n   * Check if tenant has access to feature\n   */\n  async hasTenantFeatureAccess(tenantId, featureId) {\n    // Get tenant mapping\n    const mapping = await this.getTenantMapping(tenantId);\n    \n    // Check if tenant has custom access to this feature\n    if (mapping.customFeatures.includes(featureId)) {\n      return true;\n    }\n    \n    // Get tenant's package\n    const pkg = await this.getPackageById(mapping.packageId);\n    \n    // Check if feature is included in the package\n    return pkg.features.includes(featureId);\n  }\n  \n  /**\n   * Get tenant's feature limit\n   */\n  async getTenantFeatureLimit(tenantId, limitKey) {\n    // Get tenant mapping\n    const mapping = await this.getTenantMapping(tenantId);\n    \n    // Check if tenant has custom limit\n    if (mapping.customLimits[limitKey] !== undefined) {\n      return mapping.customLimits[limitKey];\n    }\n    \n    // Get tenant's package\n    const pkg = await this.getPackageById(mapping.packageId);\n    \n    // Return package limit\n    return pkg.limits[limitKey] !== undefined ? pkg.limits[limitKey] : null;\n  }\n  \n  /**\n   * Get tenant's available features\n   */\n  async getTenantAvailableFeatures(tenantId) {\n    // Get tenant mapping\n    const mapping = await this.getTenantMapping(tenantId);\n    \n    // Get tenant's package\n    const pkg = await this.getPackageById(mapping.packageId);\n    \n    // Combine package features and custom features\n    const features = [...new Set([...pkg.features, ...mapping.customFeatures])];\n    \n    return features;\n  }\n  \n  /**\n   * Clear cache\n   */\n  clearCache() {\n    this.cache.flushAll();\n  }\n}\n\nmodule.exports = PackageConfigRegistry;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAM;EAAEG,EAAE,EAAEC;AAAO,CAAC,GAAGJ,OAAO,CAAC,MAAM,CAAC;AACtC,MAAMK,SAAS,GAAGL,OAAO,CAAC,YAAY,CAAC;AAEvC,MAAMM,qBAAqB,CAAC;EAC1BC,WAAWA,CAACC,OAAO,GAAGN,IAAI,CAACO,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAE;IACrD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,WAAW,GAAGT,IAAI,CAACO,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,UAAU,CAAC;IACtD,IAAI,CAACI,YAAY,GAAGV,IAAI,CAACO,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,eAAe,CAAC;IAChE,IAAI,CAACE,iBAAiB,GAAGX,IAAI,CAACO,IAAI,CAAC,IAAI,CAACE,WAAW,EAAE,6BAA6B,CAAC;;IAEnF;IACA,IAAI,CAACG,KAAK,GAAG,IAAIT,SAAS,CAAC;MAAEU,MAAM,EAAE,GAAG;MAAEC,WAAW,EAAE;IAAG,CAAC,CAAC;;IAE5D;IACA,IAAI,CAACC,KAAK,GAAG;MACXC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,YAAY;MACxBC,QAAQ,EAAE;IACZ,CAAC;IAED,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACE,MAAMA,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMvB,EAAE,CAACwB,KAAK,CAAC,IAAI,CAACZ,WAAW,EAAE;QAAEa,SAAS,EAAE;MAAK,CAAC,CAAC;;MAErD;MACA,IAAI;QACF,MAAMzB,EAAE,CAAC0B,MAAM,CAAC,IAAI,CAACb,YAAY,CAAC;MACpC,CAAC,CAAC,OAAOc,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B;UACA,MAAM5B,EAAE,CAAC6B,SAAS,CAAC,IAAI,CAAChB,YAAY,EAAEiB,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC3F,CAAC,MAAM;UACL,MAAML,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAM3B,EAAE,CAAC0B,MAAM,CAAC,IAAI,CAACZ,iBAAiB,CAAC;MACzC,CAAC,CAAC,OAAOa,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B;UACA,MAAM5B,EAAE,CAAC6B,SAAS,CAAC,IAAI,CAACf,iBAAiB,EAAEgB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC,MAAM;UACL,MAAMJ,KAAK;QACb;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMO,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMpC,EAAE,CAACqC,QAAQ,CAACF,QAAQ,EAAE,MAAM,CAAC;MAChD,OAAOL,IAAI,CAACQ,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACA,OAAO,EAAE;MACX;MACAK,OAAO,CAACN,KAAK,CAAC,2BAA2BQ,QAAQ,GAAG,EAAER,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMY,QAAQA,CAACJ,QAAQ,EAAEC,IAAI,EAAE;IAC7B,IAAI;MACF,MAAMpC,EAAE,CAAC6B,SAAS,CAACM,QAAQ,EAAEL,IAAI,CAACC,SAAS,CAACK,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,wBAAwBQ,QAAQ,GAAG,EAAER,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEK,kBAAkBA,CAAA,EAAG;IACnB,OAAO,CACL;MACEQ,EAAE,EAAE,MAAM;MACVC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,kEAAkE;MAC/EC,IAAI,EAAE,IAAI,CAACzB,KAAK,CAACC,IAAI;MACrByB,QAAQ,EAAE,CACR,uBAAuB,EACvB,sBAAsB,EACtB,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,CACrB;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,SAAS,EAAE,CAAC;QACZC,oBAAoB,EAAE,EAAE;QACxBC,mBAAmB,EAAE,CAAC;QACtBC,MAAM,EAAE;MACV;IACF,CAAC,EACD;MACEX,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,2CAA2C;MACxDC,IAAI,EAAE,IAAI,CAACzB,KAAK,CAACE,MAAM;MACvBwB,QAAQ,EAAE,CACR,uBAAuB,EACvB,sBAAsB,EACtB,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,4BAA4B,EAC5B,iCAAiC,EACjC,0BAA0B,CAC3B;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,SAAS,EAAE,EAAE;QACbC,oBAAoB,EAAE,EAAE;QACxBC,mBAAmB,EAAE,CAAC;QACtBC,MAAM,EAAE;MACV;IACF,CAAC,EACD;MACEX,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,wBAAwB;MAC9BC,WAAW,EAAE,0DAA0D;MACvEC,IAAI,EAAE,IAAI,CAACzB,KAAK,CAACG,UAAU;MAC3BuB,QAAQ,EAAE,CACR,uBAAuB,EACvB,sBAAsB,EACtB,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,4BAA4B,EAC5B,iCAAiC,EACjC,0BAA0B,EAC1B,gCAAgC,EAChC,+BAA+B,EAC/B,oCAAoC,EACpC,gCAAgC,EAChC,6BAA6B,EAC7B,gBAAgB,EAChB,6BAA6B,CAC9B;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,GAAG;QAChBC,kBAAkB,EAAE,KAAK;QACzBC,SAAS,EAAE,EAAE;QACbC,oBAAoB,EAAE,EAAE;QACxBC,mBAAmB,EAAE,EAAE;QACvBC,MAAM,EAAE;MACV;IACF,CAAC,EACD;MACEX,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,sBAAsB;MAC5BC,WAAW,EAAE,gDAAgD;MAC7DC,IAAI,EAAE,IAAI,CAACzB,KAAK,CAACI,QAAQ;MACzBsB,QAAQ,EAAE,CACR,uBAAuB,EACvB,sBAAsB,EACtB,2BAA2B,EAC3B,uBAAuB,EACvB,oBAAoB,EACpB,qBAAqB,EACrB,qBAAqB,EACrB,4BAA4B,EAC5B,iCAAiC,EACjC,0BAA0B,EAC1B,gCAAgC,EAChC,+BAA+B,EAC/B,oCAAoC,EACpC,gCAAgC,EAChC,6BAA6B,EAC7B,gBAAgB,EAChB,6BAA6B,EAC7B,yBAAyB,EACzB,sBAAsB,EACtB,gCAAgC,EAChC,0BAA0B,EAC1B,gCAAgC,CACjC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,CAAC,CAAC;QAAE;QACjBC,kBAAkB,EAAE,CAAC,CAAC;QAAE;QACxBC,SAAS,EAAE,CAAC,CAAC;QAAE;QACfC,oBAAoB,EAAE,CAAC,CAAC;QAAE;QAC1BC,mBAAmB,EAAE,CAAC,CAAC;QAAE;QACzBC,MAAM,EAAE,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CACF;EACH;;EAEA;AACF;AACA;EACE,MAAMC,cAAcA,CAAA,EAAG;IACrB;IACA,MAAMC,QAAQ,GAAG,cAAc;IAC/B,MAAMC,cAAc,GAAG,IAAI,CAACvC,KAAK,CAACwC,GAAG,CAACF,QAAQ,CAAC;IAE/C,IAAIC,cAAc,EAAE;MAClB,OAAOA,cAAc;IACvB;;IAEA;IACA,MAAME,QAAQ,GAAG,MAAM,IAAI,CAACtB,QAAQ,CAAC,IAAI,CAACrB,YAAY,CAAC;;IAEvD;IACA,IAAI,CAACE,KAAK,CAAC0C,GAAG,CAACJ,QAAQ,EAAEG,QAAQ,CAAC;IAElC,OAAOA,QAAQ;EACjB;;EAEA;AACF;AACA;EACE,MAAME,cAAcA,CAAClB,EAAE,EAAE;IACvB;IACA,MAAMa,QAAQ,GAAG,WAAWb,EAAE,EAAE;IAChC,MAAMmB,aAAa,GAAG,IAAI,CAAC5C,KAAK,CAACwC,GAAG,CAACF,QAAQ,CAAC;IAE9C,IAAIM,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;;IAEA;IACA,MAAMH,QAAQ,GAAG,MAAM,IAAI,CAACJ,cAAc,CAAC,CAAC;IAC5C,MAAMQ,GAAG,GAAGJ,QAAQ,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKA,EAAE,CAAC;IAE3C,IAAI,CAACoB,GAAG,EAAE;MACR,MAAM,IAAIG,KAAK,CAAC,mBAAmBvB,EAAE,YAAY,CAAC;IACpD;;IAEA;IACA,IAAI,CAACzB,KAAK,CAAC0C,GAAG,CAACJ,QAAQ,EAAEO,GAAG,CAAC;IAE7B,OAAOA,GAAG;EACZ;;EAEA;AACF;AACA;EACE,MAAMI,aAAaA,CAACC,WAAW,EAAE;IAC/B,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAACJ,cAAc,CAAC,CAAC;;IAE5C;IACA,IAAII,QAAQ,CAACU,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKyB,WAAW,CAACzB,EAAE,CAAC,EAAE;MAC/C,MAAM,IAAIuB,KAAK,CAAC,mBAAmBE,WAAW,CAACzB,EAAE,iBAAiB,CAAC;IACrE;;IAEA;IACAgB,QAAQ,CAACW,IAAI,CAACF,WAAW,CAAC;;IAE1B;IACA,MAAM,IAAI,CAAC1B,QAAQ,CAAC,IAAI,CAAC1B,YAAY,EAAE2C,QAAQ,CAAC;;IAEhD;IACA,IAAI,CAACzC,KAAK,CAACqD,GAAG,CAAC,cAAc,CAAC;IAE9B,OAAOH,WAAW;EACpB;;EAEA;AACF;AACA;EACE,MAAMI,aAAaA,CAAC7B,EAAE,EAAEyB,WAAW,EAAE;IACnC,MAAMT,QAAQ,GAAG,MAAM,IAAI,CAACJ,cAAc,CAAC,CAAC;;IAE5C;IACA,MAAMkB,KAAK,GAAGd,QAAQ,CAACe,SAAS,CAACT,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKA,EAAE,CAAC;IAElD,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAIP,KAAK,CAAC,mBAAmBvB,EAAE,YAAY,CAAC;IACpD;;IAEA;IACAgB,QAAQ,CAACc,KAAK,CAAC,GAAG;MAAE,GAAGd,QAAQ,CAACc,KAAK,CAAC;MAAE,GAAGL;IAAY,CAAC;;IAExD;IACA,MAAM,IAAI,CAAC1B,QAAQ,CAAC,IAAI,CAAC1B,YAAY,EAAE2C,QAAQ,CAAC;;IAEhD;IACA,IAAI,CAACzC,KAAK,CAACqD,GAAG,CAAC,cAAc,CAAC;IAC9B,IAAI,CAACrD,KAAK,CAACqD,GAAG,CAAC,WAAW5B,EAAE,EAAE,CAAC;IAE/B,OAAOgB,QAAQ,CAACc,KAAK,CAAC;EACxB;;EAEA;AACF;AACA;EACE,MAAME,aAAaA,CAAChC,EAAE,EAAE;IACtB,MAAMgB,QAAQ,GAAG,MAAM,IAAI,CAACJ,cAAc,CAAC,CAAC;;IAE5C;IACA,MAAMkB,KAAK,GAAGd,QAAQ,CAACe,SAAS,CAACT,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKA,EAAE,CAAC;IAElD,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAIP,KAAK,CAAC,mBAAmBvB,EAAE,YAAY,CAAC;IACpD;;IAEA;IACAgB,QAAQ,CAACiB,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;;IAEzB;IACA,MAAM,IAAI,CAAC/B,QAAQ,CAAC,IAAI,CAAC1B,YAAY,EAAE2C,QAAQ,CAAC;;IAEhD;IACA,IAAI,CAACzC,KAAK,CAACqD,GAAG,CAAC,cAAc,CAAC;IAC9B,IAAI,CAACrD,KAAK,CAACqD,GAAG,CAAC,WAAW5B,EAAE,EAAE,CAAC;IAE/B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMkC,oBAAoBA,CAAA,EAAG;IAC3B;IACA,MAAMrB,QAAQ,GAAG,qBAAqB;IACtC,MAAMsB,cAAc,GAAG,IAAI,CAAC5D,KAAK,CAACwC,GAAG,CAACF,QAAQ,CAAC;IAE/C,IAAIsB,cAAc,EAAE;MAClB,OAAOA,cAAc;IACvB;;IAEA;IACA,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAACpB,iBAAiB,CAAC;;IAE5D;IACA,IAAI,CAACC,KAAK,CAAC0C,GAAG,CAACJ,QAAQ,EAAEuB,QAAQ,CAAC;IAElC,OAAOA,QAAQ;EACjB;;EAEA;AACF;AACA;EACE,MAAMC,gBAAgBA,CAACC,QAAQ,EAAE;IAC/B;IACA,MAAMzB,QAAQ,GAAG,kBAAkByB,QAAQ,EAAE;IAC7C,MAAMC,aAAa,GAAG,IAAI,CAAChE,KAAK,CAACwC,GAAG,CAACF,QAAQ,CAAC;IAE9C,IAAI0B,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;;IAEA;IACA,MAAMH,QAAQ,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;IAClD,MAAMM,OAAO,GAAGJ,QAAQ,CAACf,IAAI,CAACoB,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKA,QAAQ,CAAC;IAE3D,IAAI,CAACE,OAAO,EAAE;MACZ;MACA,OAAO;QACLF,QAAQ;QACRI,SAAS,EAAE,MAAM;QAAE;QACnBC,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,CAAC;MACjB,CAAC;IACH;;IAEA;IACA,IAAI,CAACrE,KAAK,CAAC0C,GAAG,CAACJ,QAAQ,EAAE2B,OAAO,CAAC;IAEjC,OAAOA,OAAO;EAChB;;EAEA;AACF;AACA;EACE,MAAMK,gBAAgBA,CAACP,QAAQ,EAAEI,SAAS,EAAEC,cAAc,GAAG,EAAE,EAAEC,YAAY,GAAG,CAAC,CAAC,EAAE;IAClF,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;IAElD;IACA,MAAMJ,KAAK,GAAGM,QAAQ,CAACL,SAAS,CAACU,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKA,QAAQ,CAAC;;IAE9D;IACA,MAAME,OAAO,GAAG;MACdF,QAAQ;MACRI,SAAS;MACTC,cAAc;MACdC;IACF,CAAC;IAED,IAAId,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB;MACAM,QAAQ,CAACT,IAAI,CAACa,OAAO,CAAC;IACxB,CAAC,MAAM;MACL;MACAJ,QAAQ,CAACN,KAAK,CAAC,GAAGU,OAAO;IAC3B;;IAEA;IACA,MAAM,IAAI,CAACzC,QAAQ,CAAC,IAAI,CAACzB,iBAAiB,EAAE8D,QAAQ,CAAC;;IAErD;IACA,IAAI,CAAC7D,KAAK,CAACqD,GAAG,CAAC,qBAAqB,CAAC;IACrC,IAAI,CAACrD,KAAK,CAACqD,GAAG,CAAC,kBAAkBU,QAAQ,EAAE,CAAC;IAE5C,OAAOE,OAAO;EAChB;;EAEA;AACF;AACA;EACE,MAAMM,mBAAmBA,CAACR,QAAQ,EAAE;IAClC,MAAMF,QAAQ,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;IAElD;IACA,MAAMJ,KAAK,GAAGM,QAAQ,CAACL,SAAS,CAACU,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKA,QAAQ,CAAC;IAE9D,IAAIR,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAIP,KAAK,CAAC,6BAA6Be,QAAQ,YAAY,CAAC;IACpE;;IAEA;IACAF,QAAQ,CAACH,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;;IAEzB;IACA,MAAM,IAAI,CAAC/B,QAAQ,CAAC,IAAI,CAACzB,iBAAiB,EAAE8D,QAAQ,CAAC;;IAErD;IACA,IAAI,CAAC7D,KAAK,CAACqD,GAAG,CAAC,qBAAqB,CAAC;IACrC,IAAI,CAACrD,KAAK,CAACqD,GAAG,CAAC,kBAAkBU,QAAQ,EAAE,CAAC;IAE5C,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMS,sBAAsBA,CAACT,QAAQ,EAAEU,SAAS,EAAE;IAChD;IACA,MAAMR,OAAO,GAAG,MAAM,IAAI,CAACH,gBAAgB,CAACC,QAAQ,CAAC;;IAErD;IACA,IAAIE,OAAO,CAACG,cAAc,CAACM,QAAQ,CAACD,SAAS,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;;IAEA;IACA,MAAM5B,GAAG,GAAG,MAAM,IAAI,CAACF,cAAc,CAACsB,OAAO,CAACE,SAAS,CAAC;;IAExD;IACA,OAAOtB,GAAG,CAAChB,QAAQ,CAAC6C,QAAQ,CAACD,SAAS,CAAC;EACzC;;EAEA;AACF;AACA;EACE,MAAME,qBAAqBA,CAACZ,QAAQ,EAAEa,QAAQ,EAAE;IAC9C;IACA,MAAMX,OAAO,GAAG,MAAM,IAAI,CAACH,gBAAgB,CAACC,QAAQ,CAAC;;IAErD;IACA,IAAIE,OAAO,CAACI,YAAY,CAACO,QAAQ,CAAC,KAAKC,SAAS,EAAE;MAChD,OAAOZ,OAAO,CAACI,YAAY,CAACO,QAAQ,CAAC;IACvC;;IAEA;IACA,MAAM/B,GAAG,GAAG,MAAM,IAAI,CAACF,cAAc,CAACsB,OAAO,CAACE,SAAS,CAAC;;IAExD;IACA,OAAOtB,GAAG,CAACf,MAAM,CAAC8C,QAAQ,CAAC,KAAKC,SAAS,GAAGhC,GAAG,CAACf,MAAM,CAAC8C,QAAQ,CAAC,GAAG,IAAI;EACzE;;EAEA;AACF;AACA;EACE,MAAME,0BAA0BA,CAACf,QAAQ,EAAE;IACzC;IACA,MAAME,OAAO,GAAG,MAAM,IAAI,CAACH,gBAAgB,CAACC,QAAQ,CAAC;;IAErD;IACA,MAAMlB,GAAG,GAAG,MAAM,IAAI,CAACF,cAAc,CAACsB,OAAO,CAACE,SAAS,CAAC;;IAExD;IACA,MAAMtC,QAAQ,GAAG,CAAC,GAAG,IAAIkD,GAAG,CAAC,CAAC,GAAGlC,GAAG,CAAChB,QAAQ,EAAE,GAAGoC,OAAO,CAACG,cAAc,CAAC,CAAC,CAAC;IAE3E,OAAOvC,QAAQ;EACjB;;EAEA;AACF;AACA;EACEmD,UAAUA,CAAA,EAAG;IACX,IAAI,CAAChF,KAAK,CAACiF,QAAQ,CAAC,CAAC;EACvB;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG3F,qBAAqB", "ignoreList": []}