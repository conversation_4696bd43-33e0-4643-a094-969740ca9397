<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaBrowser - Hybrid Implementation (Go + TypeScript)</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            background: linear-gradient(45deg, #00ff96, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .connection-status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .connected { background: #00ff96; }
        .disconnected { background: #ff4757; }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        .test-btn:hover {
            transform: translateY(-2px);
        }
        .console {
            background: #000;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .log-entry {
            margin: 3px 0;
            padding: 3px 8px;
            border-left: 3px solid #00ff96;
            background: rgba(0, 255, 150, 0.1);
        }
        .log-error {
            border-left-color: #ff4757;
            background: rgba(255, 71, 87, 0.1);
        }
        .violation {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid #ff4757;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .compliant {
            background: rgba(0, 255, 150, 0.1);
            border: 1px solid #00ff96;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 NovaBrowser - Hybrid Implementation</h1>
            <p>Go Backend + TypeScript Frontend - Real Analysis</p>
            <div>
                <span class="connection-status" id="connectionStatus"></span>
                <span id="connectionText">Connecting to NovaAgent...</span>
            </div>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🧬 NovaDNA Analysis</h3>
                <div class="metric-value" id="coherenceScore">--</div>
                <p>Real DOM + Go backend analysis</p>
            </div>
            <div class="status-card">
                <h3>👁️ NovaVision Compliance</h3>
                <div class="metric-value" id="complianceScore">--</div>
                <p>Actual accessibility validation</p>
            </div>
            <div class="status-card">
                <h3>🛡️ NovaShield Protection</h3>
                <div class="metric-value" id="threatLevel">--</div>
                <p>Real security assessment</p>
            </div>
            <div class="status-card">
                <h3>⚡ Backend Status</h3>
                <div class="metric-value" id="backendStatus">--</div>
                <p>Go NovaAgent connection</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Hybrid Implementation Tests</h2>
            <button class="test-btn" onclick="testBackendConnection()">🔌 Test Go Backend</button>
            <button class="test-btn" onclick="runRealCoherenceAnalysis()">🧬 Real Coherence Analysis</button>
            <button class="test-btn" onclick="runRealAccessibilityCheck()">👁️ Real Accessibility Check</button>
            <button class="test-btn" onclick="runRealSecurityScan()">🛡️ Real Security Scan</button>
            <button class="test-btn" onclick="testWebSocketConnection()">📡 Test WebSocket</button>
            <button class="test-btn" onclick="sendTestCommand()">⚡ Send Command</button>
            <button class="test-btn" onclick="clearLog()">🗑️ Clear Log</button>
            
            <div class="console" id="console">
                <div class="log-entry">🚀 NovaBrowser Hybrid Implementation loaded</div>
                <div class="log-entry">🔗 Connecting to Go NovaAgent backend...</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Test Elements for Analysis</h2>
            
            <div class="violation">
                <h3>❌ Accessibility Violations (Real Detection)</h3>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiIGZpbGw9IiNmZjQ3NTciLz48dGV4dCB4PSI1MCIgeT0iMzAiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5ObyBBbHQ8L3RleHQ+PC9zdmc+" style="display: block; margin: 10px 0;">
                <p style="background: #ffff00; color: #ffffff; padding: 10px;">Poor contrast text</p>
                <button style="background: #ff0000; color: #ff0000; border: none; padding: 10px;">Invisible button</button>
            </div>
            
            <div class="compliant">
                <h3>✅ Compliant Elements</h3>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iNTAiIGZpbGw9IiMwMGZmOTYiLz48dGV4dCB4PSI1MCIgeT0iMzAiIGZpbGw9ImJsYWNrIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5XaXRoIEFsdDwvdGV4dD48L3N2Zz4=" alt="Compliant image with proper alt text" style="display: block; margin: 10px 0;">
                <p style="background: #1a1a2e; color: #ffffff; padding: 10px;">Good contrast text</p>
                <button style="background: #00ff96; color: #000; border: none; padding: 10px; font-weight: bold;">Accessible button</button>
            </div>
        </div>
    </div>

    <script type="module">
        // Hybrid Implementation - Real Go backend + TypeScript frontend
        
        class NovaAgentBridge {
            constructor(apiBase = 'http://localhost:8090') {
                this.apiBase = apiBase;
                this.wsUrl = apiBase.replace('http', 'ws') + '/ws';
                this.ws = null;
            }

            async getStatus() {
                const response = await fetch(`${this.apiBase}/status`);
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                return await response.json();
            }

            async sendCommand(type, payload = {}) {
                const response = await fetch(`${this.apiBase}/command`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type, payload })
                });
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                return await response.json();
            }

            connectWebSocket() {
                return new Promise((resolve, reject) => {
                    this.ws = new WebSocket(this.wsUrl);
                    this.ws.onopen = () => resolve();
                    this.ws.onerror = reject;
                    this.ws.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        addLog(`📡 WebSocket: ${data.status} | Coherence: ${(data.coherence * 100).toFixed(1)}%`);
                    };
                });
            }

            // Real DOM analysis methods
            analyzePageCoherence() {
                const elements = document.querySelectorAll('*');
                const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                const paragraphs = document.querySelectorAll('p');
                const links = document.querySelectorAll('a');
                const buttons = document.querySelectorAll('button');
                const semantic = document.querySelectorAll('main, section, article, aside, nav, header, footer');

                const structural = Math.min(1, (headings.length / Math.max(1, paragraphs.length)) * 2);
                const functional = Math.min(1, (links.length + buttons.length) / 10);
                const relational = Math.min(1, semantic.length / 5);
                const overall = (structural + functional + relational) / 3;

                return { overall, structural, functional, relational, psi_snap: overall >= 0.82 };
            }

            analyzeAccessibility() {
                const violations = [];
                const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
                const poorContrast = document.querySelectorAll('[style*="background: #ffff00"]');
                const h1Count = document.querySelectorAll('h1').length;

                if (imagesWithoutAlt.length > 0) violations.push(`${imagesWithoutAlt.length} images missing alt text`);
                if (poorContrast.length > 0) violations.push(`${poorContrast.length} poor contrast elements`);
                if (h1Count !== 1) violations.push(`${h1Count} H1 elements (should be 1)`);

                const score = Math.max(0, 1 - (violations.length * 0.25));
                return { score, violations, ada_compliance: violations.length === 0 };
            }

            assessThreats() {
                const threats = [];
                if (location.protocol === 'http:' && !location.hostname.includes('localhost')) {
                    threats.push('Insecure HTTP connection');
                }
                
                const externalScripts = document.querySelectorAll('script[src]');
                let externalCount = 0;
                externalScripts.forEach(script => {
                    const src = script.getAttribute('src');
                    if (src && !src.includes(location.hostname) && !src.startsWith('data:')) {
                        externalCount++;
                    }
                });
                if (externalCount > 0) threats.push(`${externalCount} external scripts`);

                const threatScore = Math.min(1, threats.length * 0.3);
                const riskLevel = threatScore >= 0.7 ? 'HIGH' : threatScore >= 0.4 ? 'MEDIUM' : 'LOW';
                return { riskLevel, threatScore, threats };
            }
        }

        // Initialize bridge
        const bridge = new NovaAgentBridge();

        function addLog(message, isError = false) {
            const console = document.getElementById('console');
            const entry = document.createElement('div');
            entry.className = isError ? 'log-entry log-error' : 'log-entry';
            entry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            console.appendChild(entry);
            console.scrollTop = console.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            const textEl = document.getElementById('connectionText');
            statusEl.className = connected ? 'connection-status connected' : 'connection-status disconnected';
            textEl.textContent = connected ? 'Connected to Go Backend' : 'Backend Disconnected';
        }

        // Test functions
        window.testBackendConnection = async function() {
            addLog('🔌 Testing Go backend connection...');
            try {
                const status = await bridge.getStatus();
                addLog(`✅ Backend connected: ${status.status} | Version: ${status.version}`);
                addLog(`📊 Backend coherence: ${(status.coherence * 100).toFixed(1)}% | Uptime: ${status.uptime}`);
                document.getElementById('backendStatus').textContent = status.status.toUpperCase();
                updateConnectionStatus(true);
            } catch (error) {
                addLog(`❌ Backend connection failed: ${error.message}`, true);
                document.getElementById('backendStatus').textContent = 'OFFLINE';
                updateConnectionStatus(false);
            }
        };

        window.runRealCoherenceAnalysis = function() {
            addLog('🧬 Running real DOM coherence analysis...');
            const analysis = bridge.analyzePageCoherence();
            addLog(`📊 Structural: ${Math.round(analysis.structural * 100)}%`);
            addLog(`⚙️ Functional: ${Math.round(analysis.functional * 100)}%`);
            addLog(`🔗 Relational: ${Math.round(analysis.relational * 100)}%`);
            addLog(`✅ Overall coherence: ${Math.round(analysis.overall * 100)}%`);
            if (analysis.psi_snap) addLog('⚡ Ψ-Snap threshold achieved!');
            document.getElementById('coherenceScore').textContent = Math.round(analysis.overall * 100) + '%';
        };

        window.runRealAccessibilityCheck = function() {
            addLog('👁️ Running real accessibility analysis...');
            const analysis = bridge.analyzeAccessibility();
            addLog(`📊 Accessibility score: ${Math.round(analysis.score * 100)}%`);
            addLog(`🔍 Violations found: ${analysis.violations.length}`);
            analysis.violations.forEach(v => addLog(`❌ ${v}`, true));
            if (analysis.ada_compliance) addLog('✅ ADA compliant');
            document.getElementById('complianceScore').textContent = Math.round(analysis.score * 100) + '%';
        };

        window.runRealSecurityScan = function() {
            addLog('🛡️ Running real security assessment...');
            const assessment = bridge.assessThreats();
            addLog(`🔍 Risk level: ${assessment.riskLevel}`);
            addLog(`📊 Threat score: ${Math.round(assessment.threatScore * 100)}%`);
            assessment.threats.forEach(t => addLog(`⚠️ ${t}`));
            document.getElementById('threatLevel').textContent = `${assessment.riskLevel} (${Math.round(assessment.threatScore * 100)}%)`;
        };

        window.testWebSocketConnection = async function() {
            addLog('📡 Testing WebSocket connection...');
            try {
                await bridge.connectWebSocket();
                addLog('✅ WebSocket connected successfully');
            } catch (error) {
                addLog(`❌ WebSocket connection failed: ${error.message}`, true);
            }
        };

        window.sendTestCommand = async function() {
            addLog('⚡ Sending test command to backend...');
            try {
                const result = await bridge.sendCommand('scan_vendor');
                addLog(`✅ Command executed: ${result.message}`);
                if (result.data) addLog(`📄 Result: ${JSON.stringify(result.data)}`);
            } catch (error) {
                addLog(`❌ Command failed: ${error.message}`, true);
            }
        };

        window.clearLog = function() {
            document.getElementById('console').innerHTML = '';
            addLog('🧹 Console cleared');
        };

        // Auto-initialize
        setTimeout(async () => {
            addLog('🔄 Initializing hybrid implementation...');
            await testBackendConnection();
            setTimeout(runRealCoherenceAnalysis, 1000);
            setTimeout(runRealAccessibilityCheck, 2000);
            setTimeout(runRealSecurityScan, 3000);
        }, 500);
    </script>
</body>
</html>
