"""
Machine Learning Utilities for the Predictive Engine.

This module provides utilities for integrating machine learning with the Predictive Engine.
"""

import os
import json
import logging
import pickle
from typing import Dict, List, Any, Optional, Callable, Tuple, Union
import numpy as np

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MLUtils:
    """
    Utilities for machine learning integration with the Predictive Engine.
    """
    
    @staticmethod
    def extract_features(requirements: List[Dict[str, Any]], 
                         activities: List[Dict[str, Any]],
                         feature_config: Optional[Dict[str, Any]] = None) -> Tuple[np.ndarray, List[str]]:
        """
        Extract features from requirements and activities.
        
        Args:
            requirements: List of requirements
            activities: List of activities
            feature_config: Optional configuration for feature extraction
            
        Returns:
            Tuple of (feature matrix, feature names)
        """
        logger.info("Extracting features from requirements and activities")
        
        # Default feature configuration
        config = {
            'requirement_features': ['priority', 'status', 'days_until_due'],
            'activity_features': ['status', 'days_until_due'],
            'normalize': True
        }
        
        # Update with provided configuration
        if feature_config:
            config.update(feature_config)
        
        # Initialize feature matrix and names
        features = []
        feature_names = []
        
        # Process each requirement
        for req in requirements:
            # Extract requirement features
            req_features = []
            
            # Priority feature
            if 'priority' in config['requirement_features']:
                priority_value = MLUtils._encode_priority(req.get('priority', 'medium'))
                req_features.append(priority_value)
                feature_names.append('requirement_priority')
            
            # Status feature
            if 'status' in config['requirement_features']:
                status_value = MLUtils._encode_status(req.get('status', 'pending'))
                req_features.append(status_value)
                feature_names.append('requirement_status')
            
            # Days until due feature
            if 'days_until_due' in config['requirement_features']:
                days_until_due = MLUtils._calculate_days_until_due(req.get('due_date'))
                if config['normalize']:
                    # Normalize to 0-1 range (assuming 90 days is the max)
                    days_until_due_norm = max(0, min(days_until_due / 90, 1))
                    req_features.append(days_until_due_norm)
                else:
                    req_features.append(days_until_due)
                feature_names.append('requirement_days_until_due')
            
            # Find activities for this requirement
            req_activities = [a for a in activities if a.get('requirement_id') == req.get('id')]
            
            # Activity features
            if req_activities:
                # Calculate average activity status
                if 'status' in config['activity_features']:
                    avg_status = np.mean([MLUtils._encode_status(a.get('status', 'pending')) for a in req_activities])
                    req_features.append(avg_status)
                    feature_names.append('activity_avg_status')
                
                # Calculate average days until due
                if 'days_until_due' in config['activity_features']:
                    avg_days = np.mean([MLUtils._calculate_days_until_due(a.get('end_date')) for a in req_activities])
                    if config['normalize']:
                        # Normalize to 0-1 range (assuming 90 days is the max)
                        avg_days_norm = max(0, min(avg_days / 90, 1))
                        req_features.append(avg_days_norm)
                    else:
                        req_features.append(avg_days)
                    feature_names.append('activity_avg_days_until_due')
            else:
                # No activities, add placeholder values
                if 'status' in config['activity_features']:
                    req_features.append(0)
                    feature_names.append('activity_avg_status')
                
                if 'days_until_due' in config['activity_features']:
                    req_features.append(0)
                    feature_names.append('activity_avg_days_until_due')
            
            # Add the features for this requirement
            features.append(req_features)
        
        # Convert to numpy array
        feature_matrix = np.array(features)
        
        logger.info(f"Extracted {feature_matrix.shape[1]} features for {feature_matrix.shape[0]} requirements")
        
        return feature_matrix, feature_names
    
    @staticmethod
    def save_model(model: Any, model_path: str) -> None:
        """
        Save a machine learning model to disk.
        
        Args:
            model: The model to save
            model_path: Path to save the model
        """
        logger.info(f"Saving model to {model_path}")
        
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            
            # Save the model
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
            
            logger.info(f"Model saved to {model_path}")
        
        except Exception as e:
            logger.error(f"Failed to save model to {model_path}: {e}")
            raise
    
    @staticmethod
    def load_model(model_path: str) -> Any:
        """
        Load a machine learning model from disk.
        
        Args:
            model_path: Path to load the model from
            
        Returns:
            The loaded model
        """
        logger.info(f"Loading model from {model_path}")
        
        try:
            # Check if the file exists
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"Model file not found: {model_path}")
            
            # Load the model
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            
            logger.info(f"Model loaded from {model_path}")
            
            return model
        
        except Exception as e:
            logger.error(f"Failed to load model from {model_path}: {e}")
            raise
    
    @staticmethod
    def evaluate_model(model: Any, 
                      X_test: np.ndarray, 
                      y_test: np.ndarray,
                      evaluation_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Evaluate a machine learning model.
        
        Args:
            model: The model to evaluate
            X_test: Test features
            y_test: Test labels
            evaluation_config: Optional configuration for evaluation
            
        Returns:
            Evaluation metrics
        """
        logger.info("Evaluating model")
        
        # Default evaluation configuration
        config = {
            'metrics': ['accuracy', 'precision', 'recall', 'f1']
        }
        
        # Update with provided configuration
        if evaluation_config:
            config.update(evaluation_config)
        
        # Initialize metrics
        metrics = {}
        
        try:
            # Make predictions
            y_pred = model.predict(X_test)
            
            # Calculate metrics
            if 'accuracy' in config['metrics']:
                from sklearn.metrics import accuracy_score
                metrics['accuracy'] = float(accuracy_score(y_test, y_pred))
            
            if 'precision' in config['metrics']:
                from sklearn.metrics import precision_score
                metrics['precision'] = float(precision_score(y_test, y_pred, average='weighted', zero_division=0))
            
            if 'recall' in config['metrics']:
                from sklearn.metrics import recall_score
                metrics['recall'] = float(recall_score(y_test, y_pred, average='weighted', zero_division=0))
            
            if 'f1' in config['metrics']:
                from sklearn.metrics import f1_score
                metrics['f1'] = float(f1_score(y_test, y_pred, average='weighted', zero_division=0))
            
            logger.info(f"Model evaluation completed with metrics: {metrics}")
            
            return metrics
        
        except Exception as e:
            logger.error(f"Failed to evaluate model: {e}")
            
            # Return empty metrics
            return {'error': str(e)}
    
    @staticmethod
    def _encode_priority(priority: str) -> float:
        """
        Encode priority as a numeric value.
        
        Args:
            priority: Priority string
            
        Returns:
            Numeric value
        """
        priority_map = {
            'low': 0.3,
            'medium': 0.6,
            'high': 0.9
        }
        
        return priority_map.get(priority.lower(), 0.5)
    
    @staticmethod
    def _encode_status(status: str) -> float:
        """
        Encode status as a numeric value.
        
        Args:
            status: Status string
            
        Returns:
            Numeric value
        """
        status_map = {
            'pending': 0.0,
            'in_progress': 0.5,
            'completed': 1.0
        }
        
        return status_map.get(status.lower(), 0.0)
    
    @staticmethod
    def _calculate_days_until_due(due_date_str: Optional[str]) -> float:
        """
        Calculate days until due date.
        
        Args:
            due_date_str: Due date string
            
        Returns:
            Days until due date
        """
        if not due_date_str:
            return 0.0
        
        try:
            from datetime import datetime
            due_date = datetime.fromisoformat(due_date_str)
            days_until_due = (due_date - datetime.now()).days
            return max(0, days_until_due)
        
        except Exception:
            return 0.0
