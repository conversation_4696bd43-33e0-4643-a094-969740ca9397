import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs'
import { prisma } from '@/lib/prisma'

export async function GET(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const networks = await prisma.affiliateNetwork.findMany({
      where: {
        userId: userId
      },
      include: {
        stats: true
      }
    })

    return NextResponse.json(networks)
  } catch (error) {
    console.error('Error fetching networks:', error)
    return NextResponse.json({ error: 'Failed to fetch networks' }, { status: 500 })
  }
}

export async function POST(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await req.json()
    const network = await prisma.affiliateNetwork.create({
      data: {
        userId,
        name: body.name,
        apiKey: body.apiKey,
        secretKey: body.secretKey,
        status: 'ACTIVE'
      }
    })

    return NextResponse.json(network)
  } catch (error) {
    console.error('Error creating network:', error)
    return NextResponse.json({ error: 'Failed to create network' }, { status: 500 })
  }
}
