/**
 * Consent Management Routes
 * 
 * Routes for managing consent records.
 */

const express = require('express');
const { check } = require('express-validator');
const ConsentController = require('../controllers/ConsentController');
const auth = require('../../../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/privacy/management/consent
 * @desc    Get all consent records
 * @access  Private
 */
router.get('/', auth, ConsentController.getAllConsents);

/**
 * @route   GET /api/privacy/management/consent/:id
 * @desc    Get a single consent record
 * @access  Private
 */
router.get('/:id', auth, ConsentController.getConsent);

/**
 * @route   POST /api/privacy/management/consent
 * @desc    Create a new consent record
 * @access  Private
 */
router.post('/', [
  auth,
  [
    check('dataSubject.identifier', 'Data subject identifier is required').not().isEmpty(),
    check('dataSubject.identifierType', 'Data subject identifier type is required').not().isEmpty(),
    check('processingPurposes', 'Processing purposes must be an array').isArray(),
    check('processingPurposes.*.purpose', 'Purpose is required for each processing purpose').not().isEmpty(),
    check('processingPurposes.*.status', 'Status is required for each processing purpose').not().isEmpty(),
    check('consentSource.type', 'Consent source type is required').not().isEmpty(),
    check('consentText.version', 'Consent text version is required').not().isEmpty(),
    check('consentText.content', 'Consent text content is required').not().isEmpty(),
    check('proofOfConsent', 'Proof of consent is required').not().isEmpty()
  ]
], ConsentController.createConsent);

/**
 * @route   PUT /api/privacy/management/consent/:id
 * @desc    Update a consent record
 * @access  Private
 */
router.put('/:id', [
  auth,
  [
    check('dataSubject.identifier', 'Data subject identifier is required').optional().not().isEmpty(),
    check('dataSubject.identifierType', 'Data subject identifier type is required').optional().not().isEmpty(),
    check('processingPurposes', 'Processing purposes must be an array').optional().isArray(),
    check('consentSource.type', 'Consent source type is required').optional().not().isEmpty(),
    check('consentText.version', 'Consent text version is required').optional().not().isEmpty(),
    check('consentText.content', 'Consent text content is required').optional().not().isEmpty(),
    check('proofOfConsent', 'Proof of consent is required').optional().not().isEmpty()
  ]
], ConsentController.updateConsent);

/**
 * @route   DELETE /api/privacy/management/consent/:id
 * @desc    Delete a consent record
 * @access  Private
 */
router.delete('/:id', auth, ConsentController.deleteConsent);

/**
 * @route   POST /api/privacy/management/consent/:id/withdraw
 * @desc    Withdraw consent for a specific purpose
 * @access  Private
 */
router.post('/:id/withdraw', [
  auth,
  [
    check('purpose', 'Purpose is required').not().isEmpty(),
    check('details', 'Details are required').optional()
  ]
], ConsentController.withdrawConsent);

/**
 * @route   GET /api/privacy/management/consent/validate
 * @desc    Validate consent for a specific purpose
 * @access  Private
 */
router.get('/validate', [
  auth,
  [
    check('identifier', 'Identifier is required').not().isEmpty(),
    check('identifierType', 'Identifier type is required').not().isEmpty(),
    check('purpose', 'Purpose is required').not().isEmpty()
  ]
], ConsentController.validateConsent);

/**
 * @route   GET /api/privacy/management/consent/data-subject
 * @desc    Get all consent records for a data subject
 * @access  Private
 */
router.get('/data-subject', [
  auth,
  [
    check('identifier', 'Identifier is required').not().isEmpty(),
    check('identifierType', 'Identifier type is required').not().isEmpty()
  ]
], ConsentController.getConsentsByDataSubject);

module.exports = router;
