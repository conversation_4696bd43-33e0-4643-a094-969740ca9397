apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novafuse-uac
  namespace: novafuse
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "novafuse-uac-ip"
    networking.gke.io/managed-certificates: "novafuse-uac-cert"
    networking.gke.io/v1beta1.FrontendConfig: "novafuse-uac-frontend-config"
spec:
  rules:
  - host: api.novafuse.app
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: novafuse-uac
            port:
              name: http
