<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nova Home - Coherence-First Web Gateway</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Nova Brand Colors */
            --nova-primary: #6366f1;
            --nova-secondary: #8b5cf6;
            --nova-accent: #a855f7;
            --nova-success: #10b981;
            --nova-warning: #f59e0b;
            --nova-danger: #ef4444;
            
            /* Background Colors */
            --bg-primary: #0a0a0f;
            --bg-secondary: #1a1a2e;
            --bg-tertiary: #16213e;
            --bg-glass: rgba(255, 255, 255, 0.05);
            --bg-glass-hover: rgba(255, 255, 255, 0.08);
            
            /* Text Colors */
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-tertiary: rgba(255, 255, 255, 0.6);
            --text-muted: rgba(255, 255, 255, 0.4);
            
            /* Effects */
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.2);
            --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
        }

        body {
            font-family: 'SF Pro Display', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Hero Section */
        .hero {
            text-align: center;
            padding: 80px 20px 60px;
            position: relative;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(135deg, var(--nova-primary), var(--nova-accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto 60px;
            position: relative;
        }

        .search-bar {
            width: 100%;
            padding: 16px 24px;
            font-size: 16px;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            color: var(--text-primary);
            outline: none;
            transition: all 0.3s ease;
        }

        .search-bar:focus {
            border-color: var(--nova-primary);
            box-shadow: var(--shadow-glow);
        }

        .search-bar::placeholder {
            color: var(--text-muted);
        }

        /* Services Grid */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .service-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--nova-primary), var(--nova-accent));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
            border-color: var(--nova-primary);
        }

        .service-card:hover::before {
            transform: scaleX(1);
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .service-features {
            list-style: none;
            margin-bottom: 24px;
        }

        .service-features li {
            color: var(--text-tertiary);
            font-size: 0.9rem;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .service-features li::before {
            content: '✓';
            color: var(--nova-success);
            font-weight: bold;
        }

        .service-cta {
            background: linear-gradient(135deg, var(--nova-primary), var(--nova-secondary));
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .service-cta:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Premium Service Highlight */
        .service-card.premium {
            border: 2px solid var(--nova-accent);
            background: linear-gradient(135deg, rgba(168, 85, 247, 0.1), rgba(99, 102, 241, 0.05));
        }

        .premium-badge {
            position: absolute;
            top: 16px;
            right: 16px;
            background: var(--nova-accent);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        /* Quick Stats */
        .stats-section {
            margin: 80px 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 32px;
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .stat-item {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--nova-primary);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-muted);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            margin-top: 80px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .service-card {
                padding: 24px;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .service-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .service-card:nth-child(1) { animation-delay: 0.1s; }
        .service-card:nth-child(2) { animation-delay: 0.2s; }
        .service-card:nth-child(3) { animation-delay: 0.3s; }
        .service-card:nth-child(4) { animation-delay: 0.4s; }
        .service-card:nth-child(5) { animation-delay: 0.5s; }
        .service-card:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero">
        <h1 class="hero-title">Nova Ecosystem</h1>
        <p class="hero-subtitle">
            Experience the future of consciousness-driven technology with our integrated suite of Comphyological services
        </p>
        
        <div class="search-container">
            <input type="text" class="search-bar" placeholder="Search the web with consciousness validation..." id="search-input">
        </div>
    </div>

    <!-- Services Grid -->
    <div class="services-grid">
        <!-- NovaAgent -->
        <div class="service-card premium" onclick="openService('novaagent')">
            <div class="premium-badge">Premium</div>
            <div class="service-icon">🤖</div>
            <h3 class="service-title">NovaAgent</h3>
            <p class="service-description">
                Advanced AI assistant powered by Comphyological principles for consciousness-aware interactions
            </p>
            <ul class="service-features">
                <li>Consciousness-based responses</li>
                <li>Coherence optimization</li>
                <li>Multi-dimensional analysis</li>
                <li>Real-time learning</li>
            </ul>
            <a href="#" class="service-cta">Launch NovaAgent</a>
        </div>

        <!-- NovaBrowser -->
        <div class="service-card" onclick="openService('browser')">
            <div class="service-icon">🌐</div>
            <h3 class="service-title">NovaBrowser</h3>
            <p class="service-description">
                Coherence-first web gateway with real-time consciousness analysis of websites
            </p>
            <ul class="service-features">
                <li>Real-time coherence scoring</li>
                <li>Ψ-Snap threshold alerts</li>
                <li>Accessibility compliance</li>
                <li>Threat detection</li>
            </ul>
            <a href="#" class="service-cta">Continue Browsing</a>
        </div>

        <!-- NovaVision -->
        <div class="service-card" onclick="openService('novavision')">
            <div class="service-icon">👁️</div>
            <h3 class="service-title">NovaVision</h3>
            <p class="service-description">
                Advanced accessibility and compliance analysis with consciousness-aware recommendations
            </p>
            <ul class="service-features">
                <li>WCAG 2.1 compliance</li>
                <li>ADA compliance checking</li>
                <li>Consciousness accessibility</li>
                <li>Auto-fix suggestions</li>
            </ul>
            <a href="#" class="service-cta">Analyze Site</a>
        </div>

        <!-- NovaShield -->
        <div class="service-card" onclick="openService('novashield')">
            <div class="service-icon">🛡️</div>
            <h3 class="service-title">NovaShield</h3>
            <p class="service-description">
                Quantum-enhanced security with consciousness-based threat detection and prevention
            </p>
            <ul class="service-features">
                <li>Real-time threat monitoring</li>
                <li>Consciousness-based filtering</li>
                <li>Quantum encryption</li>
                <li>Predictive security</li>
            </ul>
            <a href="#" class="service-cta">Enable Protection</a>
        </div>

        <!-- KetherNet -->
        <div class="service-card premium" onclick="openService('kethernet')">
            <div class="premium-badge">Enterprise</div>
            <div class="service-icon">⛓️</div>
            <h3 class="service-title">KetherNet</h3>
            <p class="service-description">
                Blockchain-powered consciousness verification and identity management system
            </p>
            <ul class="service-features">
                <li>Consciousness verification</li>
                <li>Decentralized identity</li>
                <li>Quantum-safe blockchain</li>
                <li>Cross-platform sync</li>
            </ul>
            <a href="#" class="service-cta">Connect Wallet</a>
        </div>

        <!-- CHAEONIX Dashboard -->
        <div class="service-card premium" onclick="openService('chaeonix')">
            <div class="premium-badge">Divine</div>
            <div class="service-icon">🔮</div>
            <h3 class="service-title">CHAEONIX</h3>
            <p class="service-description">
                Divine consciousness dashboard for advanced Comphyological analysis and insights
            </p>
            <ul class="service-features">
                <li>Divine coherence metrics</li>
                <li>Consciousness mapping</li>
                <li>Temporal analysis</li>
                <li>Reality synthesis</li>
            </ul>
            <a href="#" class="service-cta">Enter Dashboard</a>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">99.7%</div>
                <div class="stat-label">Coherence Accuracy</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">2.3M+</div>
                <div class="stat-label">Sites Analyzed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">82%</div>
                <div class="stat-label">Avg Ψ-Snap Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Consciousness Monitoring</div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>&copy; 2024 Nova Ecosystem. Powered by Comphyological Technology.</p>
        <p>Advancing consciousness through technology • Building the future of aware computing</p>
    </div>

    <script>
        // Search functionality
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    // Navigate to search results
                    window.parent.postMessage({
                        type: 'navigate',
                        url: query
                    }, '*');
                }
            }
        });

        // Service navigation
        function openService(service) {
            const serviceUrls = {
                'novaagent': 'http://localhost:8090',
                'browser': 'browser-ui.html',
                'novavision': 'http://localhost:3002',
                'novashield': 'http://localhost:3003',
                'kethernet': 'http://localhost:3004',
                'chaeonix': 'http://localhost:3005'
            };

            const url = serviceUrls[service];
            if (url) {
                if (service === 'browser') {
                    // Stay in current browser
                    window.location.reload();
                } else {
                    // Open service in new tab or navigate
                    window.parent.postMessage({
                        type: 'navigate',
                        url: url
                    }, '*');
                }
            }
        }

        // Add some interactive effects
        document.querySelectorAll('.service-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
