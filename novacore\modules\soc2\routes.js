/**
 * NovaCore SOC 2 Routes
 * 
 * This file defines the routes for the SOC 2 module.
 */

const express = require('express');
const router = express.Router();
const { SOC2Controller } = require('./controllers');
const { authenticate, authorize } = require('../../api/middleware/authMiddleware');

// SOC 2 Controls Routes

// Initialize SOC 2 controls
router.post(
  '/controls/initialize',
  authenticate,
  authorize('admin:soc2'),
  SOC2Controller.initializeControls
);

// Get all SOC 2 controls
router.get(
  '/controls',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getAllControls
);

// Get SOC 2 control by ID
router.get(
  '/controls/:id',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getControlById
);

// Get SOC 2 control implementation
router.get(
  '/organizations/:organizationId/controls/:controlId',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getControlImplementation
);

// Update SOC 2 control implementation
router.put(
  '/organizations/:organizationId/controls/:controlId',
  authenticate,
  authorize('update:soc2'),
  SOC2Controller.updateControlImplementation
);

// Get SOC 2 implementation summary
router.get(
  '/organizations/:organizationId/summary',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getImplementationSummary
);

// SOC 2 Evidence Routes

// Create SOC 2 evidence
router.post(
  '/evidence',
  authenticate,
  authorize('create:soc2'),
  SOC2Controller.createEvidence
);

// Get all SOC 2 evidence
router.get(
  '/organizations/:organizationId/evidence',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getAllEvidence
);

// Get SOC 2 evidence by ID
router.get(
  '/evidence/:id',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getEvidenceById
);

// Collect SOC 2 evidence
router.post(
  '/organizations/:organizationId/controls/:controlId/collect/:source',
  authenticate,
  authorize('create:soc2'),
  SOC2Controller.collectEvidence
);

// Verify SOC 2 evidence
router.post(
  '/evidence/:id/verify',
  authenticate,
  authorize('verify:soc2'),
  SOC2Controller.verifyEvidence
);

// SOC 2 Assessment Routes

// Create SOC 2 assessment
router.post(
  '/assessments',
  authenticate,
  authorize('create:soc2'),
  SOC2Controller.createAssessment
);

// Get all SOC 2 assessments
router.get(
  '/organizations/:organizationId/assessments',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getAllAssessments
);

// Get SOC 2 assessment by ID
router.get(
  '/assessments/:id',
  authenticate,
  authorize('read:soc2'),
  SOC2Controller.getAssessmentById
);

module.exports = router;
