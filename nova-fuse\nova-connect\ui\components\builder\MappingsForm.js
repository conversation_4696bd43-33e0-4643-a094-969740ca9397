import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  Button,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

export default function MappingsForm({ connector, updateConnector }) {
  const [mappings, setMappings] = useState(connector.mappings || []);
  const [selectedMapping, setSelectedMapping] = useState(null);
  const [newMapping, setNewMapping] = useState({
    sourceEndpoint: '',
    targetSystem: 'NovaGRC',
    targetEntity: '',
    transformations: []
  });
  const [newTransformation, setNewTransformation] = useState({
    source: '',
    target: '',
    transform: 'identity'
  });

  const transformFunctions = [
    { value: 'identity', label: 'Identity (No Change)' },
    { value: 'formatDate', label: 'Format Date' },
    { value: 'mapComplianceStatus', label: 'Map Compliance Status' },
    { value: 'mapSeverityToRisk', label: 'Map Severity to Risk' },
    { value: 'extractResourceDetails', label: 'Extract Resource Details' },
    { value: 'extractAssetDetails', label: 'Extract Asset Details' },
    { value: 'mapJiraStatusToCompliance', label: 'Map Jira Status to Compliance' },
    { value: 'mapJiraPriorityToRisk', label: 'Map Jira Priority to Risk' }
  ];

  const handleAddMapping = () => {
    if (newMapping.sourceEndpoint && newMapping.targetEntity) {
      const updatedMappings = [...mappings, { ...newMapping, transformations: [] }];
      setMappings(updatedMappings);
      updateConnector('mappings', updatedMappings);
      setSelectedMapping(updatedMappings.length - 1);
      setNewMapping({
        sourceEndpoint: '',
        targetSystem: 'NovaGRC',
        targetEntity: '',
        transformations: []
      });
    }
  };

  const handleDeleteMapping = (index) => {
    const updatedMappings = mappings.filter((_, i) => i !== index);
    setMappings(updatedMappings);
    updateConnector('mappings', updatedMappings);
    
    if (selectedMapping === index) {
      setSelectedMapping(updatedMappings.length > 0 ? 0 : null);
    } else if (selectedMapping > index) {
      setSelectedMapping(selectedMapping - 1);
    }
  };

  const handleAddTransformation = () => {
    if (selectedMapping !== null && newTransformation.source && newTransformation.target) {
      const updatedMappings = mappings.map((mapping, index) => {
        if (index === selectedMapping) {
          return {
            ...mapping,
            transformations: [...mapping.transformations, { ...newTransformation }]
          };
        }
        return mapping;
      });
      
      setMappings(updatedMappings);
      updateConnector('mappings', updatedMappings);
      setNewTransformation({
        source: '',
        target: '',
        transform: 'identity'
      });
    }
  };

  const handleDeleteTransformation = (mappingIndex, transformIndex) => {
    const updatedMappings = mappings.map((mapping, index) => {
      if (index === mappingIndex) {
        return {
          ...mapping,
          transformations: mapping.transformations.filter((_, i) => i !== transformIndex)
        };
      }
      return mapping;
    });
    
    setMappings(updatedMappings);
    updateConnector('mappings', updatedMappings);
  };

  const handleUpdateMapping = (index, field, value) => {
    const updatedMappings = mappings.map((mapping, i) => {
      if (i === index) {
        return {
          ...mapping,
          [field]: value
        };
      }
      return mapping;
    });
    
    setMappings(updatedMappings);
    updateConnector('mappings', updatedMappings);
  };

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Data Mappings
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Define how data from API responses should be mapped to NovaGRC entities.
          </Typography>
          
          {/* Add New Mapping */}
          <Paper sx={{ p: 3, mb: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom>
              Add New Mapping
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="source-endpoint-label">Source Endpoint</InputLabel>
                  <Select
                    labelId="source-endpoint-label"
                    value={newMapping.sourceEndpoint}
                    onChange={(e) => setNewMapping({ ...newMapping, sourceEndpoint: e.target.value })}
                    label="Source Endpoint"
                  >
                    {connector.endpoints.map((endpoint) => (
                      <MenuItem key={endpoint.id} value={endpoint.id}>
                        {endpoint.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Target System"
                  value={newMapping.targetSystem}
                  onChange={(e) => setNewMapping({ ...newMapping, targetSystem: e.target.value })}
                  fullWidth
                  placeholder="e.g., NovaGRC"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Target Entity"
                  value={newMapping.targetEntity}
                  onChange={(e) => setNewMapping({ ...newMapping, targetEntity: e.target.value })}
                  fullWidth
                  placeholder="e.g., ComplianceFindings"
                />
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleAddMapping}
                  disabled={!newMapping.sourceEndpoint || !newMapping.targetEntity}
                >
                  Add Mapping
                </Button>
              </Grid>
            </Grid>
          </Paper>
          
          {/* Mappings List */}
          {mappings.length > 0 ? (
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Paper sx={{ backgroundColor: 'background.paper' }}>
                  <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                    <Typography variant="subtitle1">Mappings</Typography>
                  </Box>
                  <List sx={{ maxHeight: '400px', overflow: 'auto' }}>
                    {mappings.map((mapping, index) => (
                      <ListItem
                        key={index}
                        button
                        selected={selectedMapping === index}
                        onClick={() => setSelectedMapping(index)}
                        sx={{
                          borderBottom: 1,
                          borderColor: 'divider',
                          backgroundColor: selectedMapping === index ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                          '&:hover': {
                            backgroundColor: selectedMapping === index ? 'rgba(37, 99, 235, 0.1)' : 'rgba(0, 0, 0, 0.04)'
                          }
                        }}
                      >
                        <ListItemText
                          primary={`${connector.endpoints.find(e => e.id === mapping.sourceEndpoint)?.name || mapping.sourceEndpoint} → ${mapping.targetEntity}`}
                          secondary={`${mapping.transformations.length} transformation${mapping.transformations.length !== 1 ? 's' : ''}`}
                        />
                        <ListItemSecondaryAction>
                          <IconButton
                            edge="end"
                            aria-label="delete"
                            onClick={() => handleDeleteMapping(index)}
                            size="small"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={9}>
                {selectedMapping !== null ? (
                  <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
                    <Typography variant="subtitle1" gutterBottom>
                      Mapping Details
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <FormControl fullWidth margin="normal">
                          <InputLabel id={`edit-source-endpoint-label-${selectedMapping}`}>Source Endpoint</InputLabel>
                          <Select
                            labelId={`edit-source-endpoint-label-${selectedMapping}`}
                            value={mappings[selectedMapping].sourceEndpoint}
                            onChange={(e) => handleUpdateMapping(selectedMapping, 'sourceEndpoint', e.target.value)}
                            label="Source Endpoint"
                          >
                            {connector.endpoints.map((endpoint) => (
                              <MenuItem key={endpoint.id} value={endpoint.id}>
                                {endpoint.name}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          label="Target System"
                          value={mappings[selectedMapping].targetSystem}
                          onChange={(e) => handleUpdateMapping(selectedMapping, 'targetSystem', e.target.value)}
                          fullWidth
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          label="Target Entity"
                          value={mappings[selectedMapping].targetEntity}
                          onChange={(e) => handleUpdateMapping(selectedMapping, 'targetEntity', e.target.value)}
                          fullWidth
                          margin="normal"
                        />
                      </Grid>
                    </Grid>
                    
                    <Divider sx={{ my: 3 }} />
                    
                    <Typography variant="subtitle1" gutterBottom>
                      Transformations
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Define how to transform data from the source endpoint to the target entity.
                    </Typography>
                    
                    {/* Transformations List */}
                    {mappings[selectedMapping].transformations.length > 0 ? (
                      <Box sx={{ mb: 3 }}>
                        <Paper variant="outlined" sx={{ backgroundColor: 'background.default' }}>
                          <List>
                            {mappings[selectedMapping].transformations.map((transformation, index) => (
                              <ListItem
                                key={index}
                                sx={{
                                  borderBottom: index < mappings[selectedMapping].transformations.length - 1 ? 1 : 0,
                                  borderColor: 'divider'
                                }}
                              >
                                <Grid container spacing={1} alignItems="center">
                                  <Grid item xs={5}>
                                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                      {transformation.source}
                                    </Typography>
                                  </Grid>
                                  <Grid item xs={2} sx={{ textAlign: 'center' }}>
                                    <ArrowForwardIcon color="primary" />
                                    <Typography variant="caption" display="block">
                                      {transformFunctions.find(f => f.value === transformation.transform)?.label || transformation.transform}
                                    </Typography>
                                  </Grid>
                                  <Grid item xs={5}>
                                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                      {transformation.target}
                                    </Typography>
                                  </Grid>
                                </Grid>
                                <ListItemSecondaryAction>
                                  <IconButton
                                    edge="end"
                                    aria-label="delete"
                                    onClick={() => handleDeleteTransformation(selectedMapping, index)}
                                    size="small"
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </ListItemSecondaryAction>
                              </ListItem>
                            ))}
                          </List>
                        </Paper>
                      </Box>
                    ) : (
                      <Paper 
                        variant="outlined" 
                        sx={{ 
                          p: 3, 
                          mb: 3, 
                          backgroundColor: 'background.default',
                          textAlign: 'center'
                        }}
                      >
                        <Typography color="text.secondary">
                          No transformations defined yet. Add transformations below.
                        </Typography>
                      </Paper>
                    )}
                    
                    {/* Add New Transformation */}
                    <Paper sx={{ p: 3, backgroundColor: 'background.default' }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Add New Transformation
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="Source Path"
                            value={newTransformation.source}
                            onChange={(e) => setNewTransformation({ ...newTransformation, source: e.target.value })}
                            fullWidth
                            placeholder="e.g., $.Findings[*].Compliance.Status"
                            helperText="JSONPath to source field"
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <FormControl fullWidth>
                            <InputLabel id="transform-function-label">Transform Function</InputLabel>
                            <Select
                              labelId="transform-function-label"
                              value={newTransformation.transform}
                              onChange={(e) => setNewTransformation({ ...newTransformation, transform: e.target.value })}
                              label="Transform Function"
                            >
                              {transformFunctions.map((func) => (
                                <MenuItem key={func.value} value={func.value}>
                                  {func.label}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="Target Field"
                            value={newTransformation.target}
                            onChange={(e) => setNewTransformation({ ...newTransformation, target: e.target.value })}
                            fullWidth
                            placeholder="e.g., complianceStatus"
                            helperText="Target field name"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            variant="outlined"
                            startIcon={<AddIcon />}
                            onClick={handleAddTransformation}
                            disabled={!newTransformation.source || !newTransformation.target}
                          >
                            Add Transformation
                          </Button>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Paper>
                ) : (
                  <Paper 
                    variant="outlined" 
                    sx={{ 
                      p: 4, 
                      textAlign: 'center',
                      backgroundColor: 'background.default',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <Typography variant="body1" color="text.secondary" gutterBottom>
                      No mapping selected
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Select a mapping from the list or create a new one
                    </Typography>
                  </Paper>
                )}
              </Grid>
            </Grid>
          ) : (
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 4, 
                textAlign: 'center',
                backgroundColor: 'background.default'
              }}
            >
              <Typography variant="body1" color="text.secondary" gutterBottom>
                No mappings defined yet
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Define how data from API responses should be mapped to NovaGRC entities
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
