/**
 * Jest configuration for connector tests
 */

module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/tests/unit/connectors/**/*.test.js'],
  collectCoverage: true,
  coverageReporters: ['text', 'lcov', 'html'],
  coveragePathIgnorePatterns: [
    '/node_modules/',
    '/tests/mocks/'
  ],
  coverageThreshold: {
    global: {
      branches: 81,
      functions: 81,
      lines: 81,
      statements: 81
    }
  },
  testPathIgnorePatterns: ['/node_modules/', '/tests/mocks/'],
  verbose: true
};
