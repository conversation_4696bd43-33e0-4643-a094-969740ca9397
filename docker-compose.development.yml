version: '3.8'
services:
  mongodb:
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password

  nova-connect:
    build:
      context: ./nova-connect
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    volumes:
      - ./nova-connect:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - MONGODB_URI=***************************************************
      - PORT=3001

  nova-grc-apis:
    build:
      context: ./nova-grc-apis
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    volumes:
      - ./nova-grc-apis:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - MONGODB_URI=****************************************************
      - PORT=3002

  nova-gateway:
    build:
      context: ./nova-gateway
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./nova-gateway:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NOVACONNECT_URL=http://nova-connect:3001
      - PRIVACY_MANAGEMENT_URL=http://nova-grc-apis:3002
      - PORT=3000

  nova-ui:
    build:
      context: ./nova-ui
      dockerfile: Dockerfile
    ports:
      - "3003:3000"
    volumes:
      - ./nova-ui:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:3000
