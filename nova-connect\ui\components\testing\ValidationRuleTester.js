/**
 * Validation Rule Tester Component
 * 
 * This component allows users to define and test validation rules against sample responses.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  CircularProgress, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  TextField, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';

const ValidationRuleTester = ({ response }) => {
  const [rules, setRules] = useState([
    { id: 1, path: '', operator: 'exists', value: '', description: '' }
  ]);
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  
  const operators = [
    { value: 'exists', label: 'Exists' },
    { value: 'not_exists', label: 'Does Not Exist' },
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Does Not Contain' },
    { value: 'greater_than', label: 'Greater Than' },
    { value: 'less_than', label: 'Less Than' },
    { value: 'matches', label: 'Matches Regex' }
  ];
  
  const handleAddRule = () => {
    setRules([
      ...rules,
      { 
        id: Date.now(), 
        path: '', 
        operator: 'exists', 
        value: '',
        description: '' 
      }
    ]);
  };
  
  const handleRemoveRule = (id) => {
    setRules(rules.filter(rule => rule.id !== id));
  };
  
  const handleRuleChange = (id, field, value) => {
    setRules(rules.map(rule => {
      if (rule.id === id) {
        return { ...rule, [field]: value };
      }
      return rule;
    }));
  };
  
  const handleTestRules = async () => {
    setLoading(true);
    
    try {
      // In a real implementation, this would validate the rules against the response
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const validationResults = rules.map(rule => {
        const result = validateRule(rule, response?.data);
        return {
          ...rule,
          passed: result.passed,
          message: result.message
        };
      });
      
      setResults(validationResults);
    } catch (error) {
      console.error('Error testing validation rules:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleSaveRules = () => {
    // In a real implementation, this would save the rules to a database
    alert('Validation rules saved!');
  };
  
  // Function to validate a rule against response data
  const validateRule = (rule, data) => {
    if (!data) {
      return { passed: false, message: 'No response data to validate against' };
    }
    
    try {
      // Get the value at the specified path
      const pathParts = rule.path.split('.');
      let value = data;
      
      for (const part of pathParts) {
        if (part === '') continue;
        
        if (part.includes('[') && part.includes(']')) {
          // Handle array access
          const arrayName = part.substring(0, part.indexOf('['));
          const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
          
          if (!value[arrayName]) {
            return { passed: false, message: `Path ${rule.path} does not exist in response` };
          }
          
          value = value[arrayName][index];
        } else {
          // Handle object property access
          if (value[part] === undefined) {
            return { passed: false, message: `Path ${rule.path} does not exist in response` };
          }
          
          value = value[part];
        }
      }
      
      // Validate based on operator
      switch (rule.operator) {
        case 'exists':
          return { 
            passed: value !== undefined, 
            message: value !== undefined 
              ? `Path ${rule.path} exists` 
              : `Path ${rule.path} does not exist` 
          };
          
        case 'not_exists':
          return { 
            passed: value === undefined, 
            message: value === undefined 
              ? `Path ${rule.path} does not exist` 
              : `Path ${rule.path} exists but should not` 
          };
          
        case 'equals':
          // Try to convert value to appropriate type
          let expectedValue = rule.value;
          if (!isNaN(Number(rule.value))) {
            expectedValue = Number(rule.value);
          } else if (rule.value === 'true') {
            expectedValue = true;
          } else if (rule.value === 'false') {
            expectedValue = false;
          }
          
          return { 
            passed: value === expectedValue, 
            message: value === expectedValue 
              ? `Value at ${rule.path} equals ${rule.value}` 
              : `Value at ${rule.path} is ${value}, expected ${rule.value}` 
          };
          
        case 'not_equals':
          // Try to convert value to appropriate type
          let notExpectedValue = rule.value;
          if (!isNaN(Number(rule.value))) {
            notExpectedValue = Number(rule.value);
          } else if (rule.value === 'true') {
            notExpectedValue = true;
          } else if (rule.value === 'false') {
            notExpectedValue = false;
          }
          
          return { 
            passed: value !== notExpectedValue, 
            message: value !== notExpectedValue 
              ? `Value at ${rule.path} does not equal ${rule.value}` 
              : `Value at ${rule.path} equals ${rule.value} but should not` 
          };
          
        case 'contains':
          if (typeof value === 'string') {
            return { 
              passed: value.includes(rule.value), 
              message: value.includes(rule.value) 
                ? `Value at ${rule.path} contains ${rule.value}` 
                : `Value at ${rule.path} does not contain ${rule.value}` 
            };
          } else if (Array.isArray(value)) {
            return { 
              passed: value.includes(rule.value), 
              message: value.includes(rule.value) 
                ? `Array at ${rule.path} contains ${rule.value}` 
                : `Array at ${rule.path} does not contain ${rule.value}` 
            };
          }
          return { 
            passed: false, 
            message: `Cannot check contains on value of type ${typeof value}` 
          };
          
        case 'not_contains':
          if (typeof value === 'string') {
            return { 
              passed: !value.includes(rule.value), 
              message: !value.includes(rule.value) 
                ? `Value at ${rule.path} does not contain ${rule.value}` 
                : `Value at ${rule.path} contains ${rule.value} but should not` 
            };
          } else if (Array.isArray(value)) {
            return { 
              passed: !value.includes(rule.value), 
              message: !value.includes(rule.value) 
                ? `Array at ${rule.path} does not contain ${rule.value}` 
                : `Array at ${rule.path} contains ${rule.value} but should not` 
            };
          }
          return { 
            passed: false, 
            message: `Cannot check not_contains on value of type ${typeof value}` 
          };
          
        case 'greater_than':
          const greaterThanValue = Number(rule.value);
          if (isNaN(greaterThanValue)) {
            return { 
              passed: false, 
              message: `Cannot compare with non-numeric value ${rule.value}` 
            };
          }
          return { 
            passed: value > greaterThanValue, 
            message: value > greaterThanValue 
              ? `Value ${value} at ${rule.path} is greater than ${greaterThanValue}` 
              : `Value ${value} at ${rule.path} is not greater than ${greaterThanValue}` 
          };
          
        case 'less_than':
          const lessThanValue = Number(rule.value);
          if (isNaN(lessThanValue)) {
            return { 
              passed: false, 
              message: `Cannot compare with non-numeric value ${rule.value}` 
            };
          }
          return { 
            passed: value < lessThanValue, 
            message: value < lessThanValue 
              ? `Value ${value} at ${rule.path} is less than ${lessThanValue}` 
              : `Value ${value} at ${rule.path} is not less than ${lessThanValue}` 
          };
          
        case 'matches':
          try {
            const regex = new RegExp(rule.value);
            return { 
              passed: regex.test(String(value)), 
              message: regex.test(String(value)) 
                ? `Value at ${rule.path} matches pattern ${rule.value}` 
                : `Value at ${rule.path} does not match pattern ${rule.value}` 
            };
          } catch (error) {
            return { 
              passed: false, 
              message: `Invalid regex pattern: ${error.message}` 
            };
          }
          
        default:
          return { 
            passed: false, 
            message: `Unknown operator: ${rule.operator}` 
          };
      }
    } catch (error) {
      return { 
        passed: false, 
        message: `Error validating rule: ${error.message}` 
      };
    }
  };
  
  return (
    <Box>
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Validation Rule Tester
          </Typography>
          
          <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
            Define rules to validate API responses. Use dot notation for nested properties (e.g., "data.results[0].id").
          </Typography>
          
          {rules.map((rule, index) => (
            <Grid container spacing={2} key={rule.id} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="JSON Path"
                  value={rule.path}
                  onChange={(e) => handleRuleChange(rule.id, 'path', e.target.value)}
                  placeholder="data.results[0].id"
                />
              </Grid>
              
              <Grid item xs={12} sm={2}>
                <FormControl fullWidth>
                  <InputLabel id={`operator-select-label-${rule.id}`}>Operator</InputLabel>
                  <Select
                    labelId={`operator-select-label-${rule.id}`}
                    value={rule.operator}
                    label="Operator"
                    onChange={(e) => handleRuleChange(rule.id, 'operator', e.target.value)}
                  >
                    {operators.map(op => (
                      <MenuItem key={op.value} value={op.value}>
                        {op.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={2}>
                <TextField
                  fullWidth
                  label="Value"
                  value={rule.value}
                  onChange={(e) => handleRuleChange(rule.id, 'value', e.target.value)}
                  disabled={['exists', 'not_exists'].includes(rule.operator)}
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Description"
                  value={rule.description}
                  onChange={(e) => handleRuleChange(rule.id, 'description', e.target.value)}
                  placeholder="Describe what this rule validates"
                />
              </Grid>
              
              <Grid item xs={12} sm={1} sx={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={() => handleRemoveRule(rule.id)}
                  disabled={rules.length === 1}
                  sx={{ minWidth: 'auto' }}
                >
                  <DeleteIcon />
                </Button>
              </Grid>
              
              {results && (
                <Grid item xs={12}>
                  <Box sx={{ 
                    p: 1, 
                    bgcolor: results[index].passed ? 'success.light' : 'error.light',
                    borderRadius: 1,
                    display: 'flex',
                    alignItems: 'center'
                  }}>
                    {results[index].passed ? (
                      <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                    ) : (
                      <ErrorIcon color="error" sx={{ mr: 1 }} />
                    )}
                    <Typography variant="body2">
                      {results[index].message}
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          ))}
          
          <Box sx={{ mt: 2, mb: 3 }}>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddRule}
            >
              Add Rule
            </Button>
          </Box>
          
          <Divider sx={{ my: 2 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <PlayArrowIcon />}
              onClick={handleTestRules}
              disabled={loading || !response}
            >
              {loading ? 'Testing...' : 'Test Rules'}
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={handleSaveRules}
            >
              Save Rules
            </Button>
          </Box>
        </CardContent>
      </Card>
      
      {results && (
        <Card variant="outlined">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Validation Results
            </Typography>
            
            <Box sx={{ mb: 2 }}>
              <Chip 
                icon={results.every(r => r.passed) ? <CheckCircleIcon /> : <ErrorIcon />}
                label={results.every(r => r.passed) ? 'All rules passed' : `${results.filter(r => r.passed).length} of ${results.length} rules passed`}
                color={results.every(r => r.passed) ? 'success' : 'error'}
              />
            </Box>
            
            <Paper variant="outlined" sx={{ p: 2 }}>
              {results.map((result, index) => (
                <Box key={index} sx={{ mb: index < results.length - 1 ? 2 : 0 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {result.passed ? (
                      <CheckCircleIcon color="success" sx={{ mr: 1 }} />
                    ) : (
                      <ErrorIcon color="error" sx={{ mr: 1 }} />
                    )}
                    <Typography variant="body1" fontWeight="bold">
                      Rule {index + 1}: {result.description || `Validate ${result.path}`}
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ ml: 4 }}>
                    {result.message}
                  </Typography>
                  {index < results.length - 1 && <Divider sx={{ my: 1 }} />}
                </Box>
              ))}
            </Paper>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ValidationRuleTester;
