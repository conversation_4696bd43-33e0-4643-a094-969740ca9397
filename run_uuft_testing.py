#!/usr/bin/env python3
# UUFT Testing Framework Runner
# This script runs the entire UUFT testing framework

import os
import sys
import subprocess
import logging
import time
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("uuft_testing_runner.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("UUFT_Testing_Runner")

# Base directory for all data
BASE_DIR = "D:/Archives"
RESULTS_DIR = os.path.join(BASE_DIR, "Results")

# List of scripts to run
SCRIPTS = [
    "uuft_framework.py",
    "uuft_1882_pattern.py",
    "uuft_pi_relationships.py",
    "uuft_trinity_patterns.py",
    "uuft_nested_patterns.py",
    "uuft_cross_domain_analysis.py"
]

# Function to run a script
def run_script(script_name):
    """Run a Python script and return the result."""
    logger.info(f"Running {script_name}...")
    
    try:
        # Run the script
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            check=True
        )
        
        # Log the output
        logger.info(f"{script_name} output:")
        for line in result.stdout.splitlines():
            logger.info(line)
        
        logger.info(f"{script_name} completed successfully.")
        return True
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running {script_name}:")
        logger.error(f"Return code: {e.returncode}")
        logger.error(f"Output: {e.stdout}")
        logger.error(f"Error: {e.stderr}")
        return False
    
    except Exception as e:
        logger.error(f"Error running {script_name}: {e}")
        return False

# Function to check if required packages are installed
def check_requirements():
    """Check if required packages are installed."""
    logger.info("Checking requirements...")
    
    required_packages = [
        "numpy",
        "pandas",
        "matplotlib",
        "scipy",
        "scikit-learn",
        "seaborn",
        "pywavelets",
        "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.info("Please install the missing packages using:")
        logger.info(f"pip install {' '.join(missing_packages)}")
        return False
    
    logger.info("All required packages are installed.")
    return True

# Main function
def main():
    """Main function to run the UUFT testing framework."""
    logger.info("Starting UUFT Testing Framework Runner...")
    
    # Record start time
    start_time = time.time()
    
    # Check requirements
    if not check_requirements():
        logger.error("Requirements check failed. Please install the required packages.")
        return
    
    # Create results directory if it doesn't exist
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    # Run each script in sequence
    success_count = 0
    for script in SCRIPTS:
        if run_script(script):
            success_count += 1
    
    # Record end time
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # Print summary
    logger.info("\n=== UUFT Testing Framework Summary ===")
    logger.info(f"Total scripts: {len(SCRIPTS)}")
    logger.info(f"Successful scripts: {success_count}")
    logger.info(f"Failed scripts: {len(SCRIPTS) - success_count}")
    logger.info(f"Total elapsed time: {elapsed_time:.2f} seconds")
    
    if success_count == len(SCRIPTS):
        logger.info("All scripts completed successfully!")
        logger.info(f"Results are available in: {RESULTS_DIR}")
        
        # List result files
        result_files = os.listdir(RESULTS_DIR)
        logger.info("\nResult files:")
        for file in result_files:
            logger.info(f"- {file}")
    else:
        logger.warning("Some scripts failed. Please check the logs for details.")
    
    logger.info("UUFT Testing Framework Runner completed.")

if __name__ == "__main__":
    main()
