import { NextApiResponse } from 'next'
import { Server } from 'socket.io'
import { createServer } from 'http'

export const config = {
  api: {
    bodyParser: false,
  },
}

const httpServer = createServer()
const io = new Server(httpServer, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
})

io.on('connection', (socket) => {
  console.log('Client connected')

  socket.on('join', (userId: string) => {
    socket.join(userId)
  })

  socket.on('disconnect', () => {
    console.log('Client disconnected')
  })
})

export default function handler(req, res) {
  if (req.method === 'GET') {
    res.setHeader('Content-Type', 'text/plain')
    res.end('WebSocket server is running')
  } else {
    res.setHeader('Allow', ['GET'])
    res.status(405).end(`Method ${req.method} Not Allowed`)
  }
}

// Export the WebSocket server for use in other files
export { io }
