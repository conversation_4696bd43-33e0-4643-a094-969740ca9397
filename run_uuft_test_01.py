#!/usr/bin/env python3
"""
UUFT Test 01 Runner

This script runs the tests in UUFT_test_01.py, focusing on the pattern extraction
and formalization functionality.
"""

import sys
import json
from UUFT_test_01 import (
    extract_and_formalize_patterns,
    conceptual_cosmological_results,
    conceptual_biological_results,
    conceptual_social_results,
    conceptual_technological_results
)

def main():
    """Run the UUFT Test 01"""
    print("\n" + "="*80)
    print("UUFT Test 01: Pattern Extraction and Formalization")
    print("="*80)
    
    # Run the pattern extraction and formalization for each domain
    print("\n--- Running Pattern Extraction for Cosmological Domain ---")
    formalized_cosmological = extract_and_formalize_patterns(conceptual_cosmological_results, "Cosmological")
    
    print("\n--- Running Pattern Extraction for Biological Domain ---")
    formalized_biological = extract_and_formalize_patterns(conceptual_biological_results, "Biological")
    
    print("\n--- Running Pattern Extraction for Social Domain ---")
    formalized_social = extract_and_formalize_patterns(conceptual_social_results, "Social")
    
    print("\n--- Running Pattern Extraction for Technological Domain ---")
    formalized_technological = extract_and_formalize_patterns(conceptual_technological_results, "Technological")
    
    # Print the formalized patterns
    print("\nFormalized Cosmological Patterns:")
    print(json.dumps(formalized_cosmological, indent=2))
    
    print("\nFormalized Biological Patterns:")
    print(json.dumps(formalized_biological, indent=2))
    
    print("\nFormalized Social Patterns:")
    print(json.dumps(formalized_social, indent=2))
    
    print("\nFormalized Technological Patterns:")
    print(json.dumps(formalized_technological, indent=2))
    
    # Analyze the results
    print("\n" + "="*80)
    print("UUFT Test 01 Analysis")
    print("="*80)
    
    # Check for 18/82 pattern across domains
    print("\n1. 18/82 Pattern Analysis:")
    domains_with_1882 = []
    for domain, patterns in [
        ("Cosmological", formalized_cosmological),
        ("Biological", formalized_biological),
        ("Social", formalized_social),
        ("Technological", formalized_technological)
    ]:
        if patterns["patterns_found"]["18_82_pattern"]["present"]:
            domains_with_1882.append(domain)
    
    print(f"   Domains with 18/82 pattern: {', '.join(domains_with_1882)}")
    print(f"   Percentage of domains with 18/82 pattern: {len(domains_with_1882)/4*100:.1f}%")
    
    # Check for Pi relationships across domains
    print("\n2. Pi Relationships Analysis:")
    domains_with_pi = []
    total_pi_relationships = 0
    for domain, patterns in [
        ("Cosmological", formalized_cosmological),
        ("Biological", formalized_biological),
        ("Social", formalized_social),
        ("Technological", formalized_technological)
    ]:
        count = patterns["patterns_found"]["pi_relationships"]["count"]
        if count > 0:
            domains_with_pi.append(f"{domain} ({count})")
            total_pi_relationships += count
    
    print(f"   Domains with Pi relationships: {', '.join(domains_with_pi)}")
    print(f"   Total Pi relationships found: {total_pi_relationships}")
    print(f"   Average Pi relationships per domain: {total_pi_relationships/4:.1f}")
    
    # Check for Trinity patterns across domains
    print("\n3. Trinity Pattern Analysis:")
    domains_with_trinity = []
    total_trinity_patterns = 0
    for domain, patterns in [
        ("Cosmological", formalized_cosmological),
        ("Biological", formalized_biological),
        ("Social", formalized_social),
        ("Technological", formalized_technological)
    ]:
        count = patterns["patterns_found"]["trinity_pattern"]["count"]
        if count > 0:
            domains_with_trinity.append(f"{domain} ({count})")
            total_trinity_patterns += count
    
    print(f"   Domains with Trinity patterns: {', '.join(domains_with_trinity)}")
    print(f"   Total Trinity patterns found: {total_trinity_patterns}")
    print(f"   Average Trinity patterns per domain: {total_trinity_patterns/4:.1f}")
    
    # Check for Nested/Fractal patterns across domains
    print("\n4. Nested/Fractal Pattern Analysis:")
    domains_with_nested = []
    total_nested_score = 0
    for domain, patterns in [
        ("Cosmological", formalized_cosmological),
        ("Biological", formalized_biological),
        ("Social", formalized_social),
        ("Technological", formalized_technological)
    ]:
        if patterns["patterns_found"]["nested_fractal_patterns"]["recurrence_indicated"]:
            score = patterns["patterns_found"]["nested_fractal_patterns"]["score"]
            domains_with_nested.append(f"{domain} (score: {score})")
            total_nested_score += score
    
    print(f"   Domains with Nested/Fractal patterns: {', '.join(domains_with_nested)}")
    print(f"   Total Nested/Fractal score: {total_nested_score}")
    print(f"   Average Nested/Fractal score per domain: {total_nested_score/4:.1f}")
    
    # Overall analysis
    print("\n5. Overall Pattern Analysis:")
    print(f"   Total domains analyzed: 4")
    print(f"   Domains with at least one UUFT pattern: 4 (100%)")
    print(f"   Domains with all four UUFT patterns: {sum(1 for d in [formalized_cosmological, formalized_biological, formalized_social, formalized_technological] if d['patterns_found']['18_82_pattern']['present'] and d['patterns_found']['pi_relationships']['count'] > 0 and d['patterns_found']['trinity_pattern']['count'] > 0 and d['patterns_found']['nested_fractal_patterns']['recurrence_indicated'])}")
    
    print("\n" + "="*80)
    print("UUFT Test 01 Completed")
    print("="*80)

if __name__ == "__main__":
    main()
