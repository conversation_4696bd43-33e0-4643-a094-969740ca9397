const Joi = require('joi');

// Validation schemas
const schemas = {
  // Framework validation schemas
  createFramework: Joi.object({
    name: Joi.string().required().min(2).max(100),
    description: Joi.string().required().max(500),
    version: Joi.string().required().max(50),
    category: Joi.string().required().max(50),
    authority: Joi.string().optional().max(100),
    website: Joi.string().optional().uri().max(200),
    applicability: Joi.array().items(Joi.string().max(100)).optional()
  }),

  updateFramework: Joi.object({
    name: Joi.string().optional().min(2).max(100),
    description: Joi.string().optional().max(500),
    version: Joi.string().optional().max(50),
    category: Joi.string().optional().max(50),
    authority: Joi.string().optional().max(100),
    website: Joi.string().optional().uri().max(200),
    applicability: Joi.array().items(Joi.string().max(100)).optional()
  }).min(1), // At least one field must be provided

  // Requirement validation schemas
  createRequirement: Joi.object({
    code: Joi.string().required().max(50),
    title: Joi.string().required().min(3).max(200),
    description: Joi.string().required().max(1000),
    category: Joi.string().optional().max(100),
    priority: Joi.string().required().valid('critical', 'high', 'medium', 'low'),
    status: Joi.string().required().valid('applicable', 'not-applicable', 'under-review')
  }),

  updateRequirement: Joi.object({
    code: Joi.string().optional().max(50),
    title: Joi.string().optional().min(3).max(200),
    description: Joi.string().optional().max(1000),
    category: Joi.string().optional().max(100),
    priority: Joi.string().optional().valid('critical', 'high', 'medium', 'low'),
    status: Joi.string().optional().valid('applicable', 'not-applicable', 'under-review')
  }).min(1), // At least one field must be provided

  // Control validation schemas
  createControl: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().required().max(500),
    type: Joi.string().required().valid('preventive', 'detective', 'corrective', 'administrative', 'technical', 'physical'),
    status: Joi.string().required().valid('implemented', 'partially-implemented', 'not-implemented', 'planned'),
    owner: Joi.string().required().max(100),
    implementationDetails: Joi.string().optional().max(1000),
    testProcedure: Joi.string().optional().max(1000),
    lastTestedDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    nextTestDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    relatedRequirements: Joi.array().items(Joi.string()).optional()
  }),

  updateControl: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().optional().valid('preventive', 'detective', 'corrective', 'administrative', 'technical', 'physical'),
    status: Joi.string().optional().valid('implemented', 'partially-implemented', 'not-implemented', 'planned'),
    owner: Joi.string().optional().max(100),
    implementationDetails: Joi.string().optional().max(1000),
    testProcedure: Joi.string().optional().max(1000),
    lastTestedDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    nextTestDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    relatedRequirements: Joi.array().items(Joi.string()).optional()
  }).min(1), // At least one field must be provided

  // Assessment validation schemas
  createAssessment: Joi.object({
    frameworkId: Joi.string().required(),
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    assessor: Joi.string().required().max(100),
    startDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().required().valid('planned', 'in-progress', 'completed', 'cancelled'),
    scope: Joi.string().optional().max(500),
    findings: Joi.array().items(
      Joi.object({
        requirementId: Joi.string().required(),
        status: Joi.string().required().valid('compliant', 'non-compliant', 'partially-compliant', 'not-applicable'),
        notes: Joi.string().optional().max(500),
        remediationPlan: Joi.string().optional().max(500),
        remediationDueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
      })
    ).optional()
  }),

  updateAssessment: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    assessor: Joi.string().optional().max(100),
    startDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().optional().valid('planned', 'in-progress', 'completed', 'cancelled'),
    scope: Joi.string().optional().max(500),
    findings: Joi.array().items(
      Joi.object({
        requirementId: Joi.string().required(),
        status: Joi.string().required().valid('compliant', 'non-compliant', 'partially-compliant', 'not-applicable'),
        notes: Joi.string().optional().max(500),
        remediationPlan: Joi.string().optional().max(500),
        remediationDueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
      })
    ).optional()
  }).min(1), // At least one field must be provided

  // Evidence validation schemas
  createEvidence: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().required().valid('document', 'screenshot', 'log', 'report', 'certification', 'attestation', 'other'),
    location: Joi.string().required().max(500),
    collectedBy: Joi.string().required().max(100),
    collectedAt: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    expiresAt: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().required().valid('valid', 'expired', 'pending-review', 'rejected')
  }),

  updateEvidence: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().optional().valid('document', 'screenshot', 'log', 'report', 'certification', 'attestation', 'other'),
    location: Joi.string().optional().max(500),
    collectedBy: Joi.string().optional().max(100),
    collectedAt: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    expiresAt: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    status: Joi.string().optional().valid('valid', 'expired', 'pending-review', 'rejected')
  }).min(1), // At least one field must be provided

  // Assessment workflow validation schemas
  startAssessment: Joi.object({
    assessor: Joi.string().optional().max(100),
    assessorType: Joi.string().optional().valid('internal', 'external', 'third-party'),
    startDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
  }),

  completeAssessment: Joi.object({
    endDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    overallScore: Joi.number().optional().min(0).max(100),
    findings: Joi.array().items(
      Joi.object({
        id: Joi.string().optional(),
        requirementId: Joi.string().required(),
        description: Joi.string().required().max(500),
        severity: Joi.string().required().valid('critical', 'high', 'medium', 'low'),
        status: Joi.string().required().valid('open', 'in-remediation', 'closed')
      })
    ).optional(),
    recommendations: Joi.array().items(
      Joi.object({
        id: Joi.string().optional(),
        description: Joi.string().required().max(500),
        priority: Joi.string().required().valid('critical', 'high', 'medium', 'low'),
        status: Joi.string().required().valid('planned', 'in-progress', 'completed', 'rejected')
      })
    ).optional()
  }),

  // Automation rule validation schemas
  createAutomationRule: Joi.object({
    name: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    requirementId: Joi.string().required(),
    triggerType: Joi.string().required().valid('scheduled', 'event-based', 'manual'),
    triggerCondition: Joi.string().optional().max(500),
    actions: Joi.array().items(
      Joi.object({
        type: Joi.string().required().valid('collect-evidence', 'notify', 'update-status', 'create-task'),
        parameters: Joi.object().optional()
      })
    ).required().min(1),
    schedule: Joi.string().when('triggerType', {
      is: 'scheduled',
      then: Joi.string().required().max(100),
      otherwise: Joi.string().optional().max(100)
    }),
    frequency: Joi.string().optional().valid('daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom'),
    status: Joi.string().optional().valid('active', 'inactive', 'draft').default('active')
  }),

  updateAutomationRule: Joi.object({
    name: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    requirementId: Joi.string().optional(),
    triggerType: Joi.string().optional().valid('scheduled', 'event-based', 'manual'),
    triggerCondition: Joi.string().optional().max(500),
    actions: Joi.array().items(
      Joi.object({
        type: Joi.string().required().valid('collect-evidence', 'notify', 'update-status', 'create-task'),
        parameters: Joi.object().optional()
      })
    ).optional().min(1),
    schedule: Joi.string().optional().max(100),
    frequency: Joi.string().optional().valid('daily', 'weekly', 'monthly', 'quarterly', 'annually', 'custom'),
    status: Joi.string().optional().valid('active', 'inactive', 'draft')
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];

    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }

    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }

    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
