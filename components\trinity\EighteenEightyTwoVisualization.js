import React, { useState, useEffect, useRef } from 'react';

/**
 * 18/82 Principle Visualization
 * 
 * This component visualizes the 18/82 principle, showing how 18% of indicators
 * account for 82% of predictive power across multiple domains.
 */
const EighteenEightyTwoVisualization = ({ width = '100%', height = '500px' }) => {
  // State for domain selection
  const [selectedDomain, setSelectedDomain] = useState('cybersecurity');
  const [showDetails, setShowDetails] = useState(false);
  
  // Canvas reference for visualization
  const canvasRef = useRef(null);
  
  // Domain data
  const domains = {
    cybersecurity: {
      title: 'Cybersecurity',
      color: '#2196F3',
      indicators: [
        { name: 'Unusual Authentication Patterns', weight: 0.18, critical: true },
        { name: 'Network Traffic Anomalies', weight: 0.16, critical: true },
        { name: 'Privilege Escalation Attempts', weight: 0.15, critical: true },
        { name: 'Suspicious File Operations', weight: 0.14, critical: true },
        { name: 'Command & Control Communications', weight: 0.12, critical: true },
        { name: 'Data Exfiltration Attempts', weight: 0.07, critical: true },
        { name: 'System Configuration Changes', weight: 0.05 },
        { name: 'Application Errors', weight: 0.04 },
        { name: 'Resource Utilization Spikes', weight: 0.03 },
        { name: 'User Behavior Changes', weight: 0.02 },
        { name: 'Endpoint Health Status', weight: 0.02 },
        { name: 'Patch Compliance', weight: 0.01 },
        { name: 'Backup Status', weight: 0.01 }
      ]
    },
    finance: {
      title: 'Finance',
      color: '#4CAF50',
      indicators: [
        { name: 'Cash Flow Patterns', weight: 0.20, critical: true },
        { name: 'Debt-to-Income Ratio', weight: 0.18, critical: true },
        { name: 'Payment History', weight: 0.16, critical: true },
        { name: 'Credit Utilization', weight: 0.15, critical: true },
        { name: 'Account Age', weight: 0.13, critical: true },
        { name: 'Income Stability', weight: 0.05 },
        { name: 'Asset Diversification', weight: 0.03 },
        { name: 'Recent Credit Inquiries', weight: 0.03 },
        { name: 'Types of Credit', weight: 0.02 },
        { name: 'Public Records', weight: 0.02 },
        { name: 'Employment History', weight: 0.01 },
        { name: 'Education Level', weight: 0.01 },
        { name: 'Geographic Location', weight: 0.01 }
      ]
    },
    medicine: {
      title: 'Medicine',
      color: '#9C27B0',
      indicators: [
        { name: 'Vital Signs Trends', weight: 0.19, critical: true },
        { name: 'Lab Test Results', weight: 0.18, critical: true },
        { name: 'Medication Response', weight: 0.17, critical: true },
        { name: 'Symptom Progression', weight: 0.15, critical: true },
        { name: 'Genetic Markers', weight: 0.13, critical: true },
        { name: 'Medical History', weight: 0.05 },
        { name: 'Family History', weight: 0.03 },
        { name: 'Age & Gender', weight: 0.03 },
        { name: 'Lifestyle Factors', weight: 0.02 },
        { name: 'Environmental Exposures', weight: 0.02 },
        { name: 'Psychological State', weight: 0.01 },
        { name: 'Social Determinants', weight: 0.01 },
        { name: 'Treatment Adherence', weight: 0.01 }
      ]
    }
  };
  
  // Calculate critical indicators (top 18% by count)
  const calculateCriticalIndicators = (domain) => {
    const indicators = domains[domain].indicators;
    const criticalCount = Math.ceil(indicators.length * 0.18);
    
    // Sort by weight
    const sortedIndicators = [...indicators].sort((a, b) => b.weight - a.weight);
    
    // Mark top indicators as critical
    for (let i = 0; i < criticalCount; i++) {
      const indicator = sortedIndicators[i];
      const originalIndex = indicators.findIndex(ind => ind.name === indicator.name);
      indicators[originalIndex].critical = true;
    }
    
    // Calculate total weight of critical indicators
    const criticalWeight = indicators
      .filter(ind => ind.critical)
      .reduce((sum, ind) => sum + ind.weight, 0);
    
    return {
      count: criticalCount,
      percentage: (criticalCount / indicators.length) * 100,
      weight: criticalWeight,
      weightPercentage: criticalWeight * 100
    };
  };
  
  // Get critical indicators for current domain
  const getCriticalStats = () => {
    return calculateCriticalIndicators(selectedDomain);
  };
  
  // Initialize and animate the canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const container = canvas.parentElement;
    
    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Animation variables
    let animationFrame;
    let time = 0;
    
    // Animation function
    const animate = () => {
      time += 0.01;
      
      // Clear canvas
      ctx.fillStyle = '#111133';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw 18/82 visualization
      draw1882Visualization(ctx, canvas.width, canvas.height, time);
      
      // Continue animation
      animationFrame = requestAnimationFrame(animate);
    };
    
    animate();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrame);
    };
  }, [selectedDomain]);
  
  // Draw the 18/82 visualization
  const draw1882Visualization = (ctx, width, height, time) => {
    const domain = domains[selectedDomain];
    const indicators = domain.indicators;
    const domainColor = domain.color;
    
    // Calculate critical stats
    const criticalStats = getCriticalStats();
    
    // Draw title
    ctx.font = 'bold 24px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.fillText(`18/82 Principle: ${domain.title}`, width / 2, 40);
    
    // Draw subtitle
    ctx.font = '16px Arial';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText(
      `${criticalStats.count} indicators (${criticalStats.percentage.toFixed(0)}%) account for ${criticalStats.weightPercentage.toFixed(0)}% of predictive power`,
      width / 2, 70
    );
    
    // Draw main visualization
    const centerX = width / 2;
    const centerY = height / 2 + 20;
    const maxRadius = Math.min(width, height) * 0.35;
    
    // Draw outer circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, maxRadius, 0, Math.PI * 2);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Draw indicators
    const totalIndicators = indicators.length;
    const angleStep = (Math.PI * 2) / totalIndicators;
    
    indicators.forEach((indicator, index) => {
      const angle = index * angleStep;
      const radius = maxRadius * (0.3 + indicator.weight * 3);
      
      // Calculate position
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      // Draw connector line
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.strokeStyle = indicator.critical 
        ? domainColor 
        : 'rgba(255, 255, 255, 0.2)';
      ctx.lineWidth = indicator.critical ? 2 : 1;
      ctx.stroke();
      
      // Draw indicator node
      const nodeSize = indicator.critical 
        ? 8 + Math.sin(time * 3 + index) * 2 
        : 4;
      
      ctx.beginPath();
      ctx.arc(x, y, nodeSize, 0, Math.PI * 2);
      ctx.fillStyle = indicator.critical 
        ? domainColor 
        : 'rgba(255, 255, 255, 0.3)';
      ctx.fill();
      
      // Draw indicator label
      if (indicator.critical || showDetails) {
        const labelRadius = radius + 15;
        const labelX = centerX + Math.cos(angle) * labelRadius;
        const labelY = centerY + Math.sin(angle) * labelRadius;
        
        ctx.font = indicator.critical ? 'bold 12px Arial' : '10px Arial';
        ctx.fillStyle = indicator.critical 
          ? 'white' 
          : 'rgba(255, 255, 255, 0.5)';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        
        // Adjust text angle for readability
        ctx.save();
        ctx.translate(labelX, labelY);
        
        // Rotate text based on position
        if (angle > Math.PI / 2 && angle < Math.PI * 3/2) {
          ctx.rotate(angle + Math.PI);
        } else {
          ctx.rotate(angle);
        }
        
        ctx.fillText(indicator.name, 0, 0);
        ctx.restore();
      }
    });
    
    // Draw 18/82 divider
    ctx.beginPath();
    ctx.moveTo(centerX, centerY);
    const dividerAngle = criticalStats.count * angleStep;
    const dividerX = centerX + Math.cos(dividerAngle) * maxRadius * 1.1;
    const dividerY = centerY + Math.sin(dividerAngle) * maxRadius * 1.1;
    ctx.lineTo(dividerX, dividerY);
    ctx.strokeStyle = '#FFC107';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 3]);
    ctx.stroke();
    ctx.setLineDash([]);
    
    // Draw 18% label
    ctx.font = 'bold 16px Arial';
    ctx.fillStyle = '#FFC107';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    const label18X = centerX + Math.cos(dividerAngle / 2) * maxRadius * 0.6;
    const label18Y = centerY + Math.sin(dividerAngle / 2) * maxRadius * 0.6;
    ctx.fillText('18%', label18X, label18Y);
    
    // Draw 82% label
    const label82X = centerX + Math.cos(dividerAngle + (Math.PI * 2 - dividerAngle) / 2) * maxRadius * 0.6;
    const label82Y = centerY + Math.sin(dividerAngle + (Math.PI * 2 - dividerAngle) / 2) * maxRadius * 0.6;
    ctx.fillText('82%', label82X, label82Y);
    
    // Draw pie chart
    const pieRadius = maxRadius * 0.25;
    const pieX = width - pieRadius - 40;
    const pieY = height - pieRadius - 40;
    
    // Draw 18% segment
    ctx.beginPath();
    ctx.moveTo(pieX, pieY);
    ctx.arc(pieX, pieY, pieRadius, 0, Math.PI * 2 * 0.18);
    ctx.lineTo(pieX, pieY);
    ctx.fillStyle = domainColor;
    ctx.fill();
    
    // Draw 82% segment
    ctx.beginPath();
    ctx.moveTo(pieX, pieY);
    ctx.arc(pieX, pieY, pieRadius, Math.PI * 2 * 0.18, Math.PI * 2);
    ctx.lineTo(pieX, pieY);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.fill();
    
    // Draw pie chart labels
    ctx.font = 'bold 12px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.fillText('Indicators', pieX, pieY - pieRadius - 25);
    ctx.fillText('18%', pieX - pieRadius * 0.5, pieY - pieRadius * 0.5);
    ctx.fillText('82%', pieX + pieRadius * 0.5, pieY + pieRadius * 0.5);
    
    // Draw second pie chart for predictive power
    const pie2Radius = pieRadius;
    const pie2X = pieRadius + 40;
    const pie2Y = height - pieRadius - 40;
    
    // Draw 82% segment (predictive power)
    ctx.beginPath();
    ctx.moveTo(pie2X, pie2Y);
    ctx.arc(pie2X, pie2Y, pie2Radius, 0, Math.PI * 2 * 0.82);
    ctx.lineTo(pie2X, pie2Y);
    ctx.fillStyle = domainColor;
    ctx.fill();
    
    // Draw 18% segment (predictive power)
    ctx.beginPath();
    ctx.moveTo(pie2X, pie2Y);
    ctx.arc(pie2X, pie2Y, pie2Radius, Math.PI * 2 * 0.82, Math.PI * 2);
    ctx.lineTo(pie2X, pie2Y);
    ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.fill();
    
    // Draw pie chart labels
    ctx.font = 'bold 12px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.fillText('Predictive Power', pie2X, pie2Y - pie2Radius - 25);
    ctx.fillText('82%', pie2X - pie2Radius * 0.5, pie2Y - pie2Radius * 0.5);
    ctx.fillText('18%', pie2X + pie2Radius * 0.5, pie2Y + pie2Radius * 0.5);
  };
  
  return (
    <div className="eighteen-eightytwo-visualization" style={{ width, height, position: 'relative' }}>
      <div className="visualization-container" style={{ 
        width: '100%', 
        height: '80%', 
        backgroundColor: '#111133',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <canvas ref={canvasRef} style={{ width: '100%', height: '100%' }} />
      </div>
      
      <div className="controls-container" style={{ 
        width: '100%', 
        height: '20%', 
        padding: '15px',
        backgroundColor: '#1a1a2e',
        borderRadius: '8px',
        marginTop: '10px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ margin: 0, color: 'white', fontSize: '16px' }}>18/82 Principle Visualization</h3>
          <button 
            onClick={() => setShowDetails(!showDetails)}
            style={{
              background: 'none',
              border: '1px solid rgba(255,255,255,0.3)',
              color: 'white',
              padding: '5px 10px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            {showDetails ? 'Hide Details' : 'Show All Indicators'}
          </button>
        </div>
        
        <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          {Object.keys(domains).map(domain => (
            <button
              key={domain}
              onClick={() => setSelectedDomain(domain)}
              style={{
                backgroundColor: selectedDomain === domain ? domains[domain].color : 'rgba(255,255,255,0.1)',
                color: 'white',
                border: 'none',
                padding: '8px 15px',
                borderRadius: '4px',
                cursor: 'pointer',
                flex: 1
              }}
            >
              {domains[domain].title}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EighteenEightyTwoVisualization;
