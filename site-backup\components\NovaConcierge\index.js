'use client'

import { useState, useRef, useEffect } from 'react';
import { NovaConciergeService } from '../../services/NovaConciergeService';
import styles from './NovaConcierge.module.css';

// Helper components for rendering different types of content
const ApiSuggestion = ({ suggestion, onSelect }) => (
  <div className={styles.suggestionItem} onClick={() => onSelect(suggestion)}>
    <div className={styles.suggestionTitle}>{suggestion.title}</div>
    <div className={styles.suggestionDescription}>{suggestion.description}</div>
  </div>
);

const UseCase = ({ useCase }) => (
  <div className={styles.useCaseContainer}>
    <div className={styles.useCaseTitle}>{useCase.title}</div>
    
    <div className={styles.useCaseSection}>
      <div className={styles.useCaseSectionTitle}>Challenge:</div>
      <div>{useCase.challenge}</div>
    </div>
    
    <div className={styles.useCaseSection}>
      <div className={styles.useCaseSectionTitle}>Solution:</div>
      <div>{useCase.solution}</div>
    </div>
    
    <div className={styles.useCaseSection}>
      <div className={styles.useCaseSectionTitle}>Results:</div>
      <ul className={styles.useCaseResults}>
        {useCase.results.map((result, index) => (
          <li key={index}>{result}</li>
        ))}
      </ul>
    </div>
  </div>
);

const CompetitorComparison = ({ comparison }) => (
  <div className={styles.comparisonContainer}>
    <div className={styles.comparisonTitle}>NovaFuse vs. {comparison.competitorName}</div>
    
    <div>
      <div className={styles.useCaseSectionTitle}>Key Advantages:</div>
      <ul className={styles.advantagesList}>
        {comparison.advantages.map((advantage, index) => (
          <li key={index}>{advantage}</li>
        ))}
      </ul>
    </div>
    
    <div className={styles.metricsContainer}>
      <div className={styles.metricCard}>
        <div className={styles.metricTitle}>Data Processing Speed</div>
        <div className={styles.metricValue}>
          <span className={styles.metricLabel}>NovaFuse:</span>
          <span className={styles.metricNovaFuse}>{comparison.metrics.dataProcessingSpeed.novaFuse}</span>
        </div>
        <div className={styles.metricValue}>
          <span className={styles.metricLabel}>{comparison.competitorName}:</span>
          <span className={styles.metricCompetitor}>{comparison.metrics.dataProcessingSpeed.competitor}</span>
        </div>
      </div>
      
      <div className={styles.metricCard}>
        <div className={styles.metricTitle}>Remediation Time</div>
        <div className={styles.metricValue}>
          <span className={styles.metricLabel}>NovaFuse:</span>
          <span className={styles.metricNovaFuse}>{comparison.metrics.remediationTime.novaFuse}</span>
        </div>
        <div className={styles.metricValue}>
          <span className={styles.metricLabel}>{comparison.competitorName}:</span>
          <span className={styles.metricCompetitor}>{comparison.metrics.remediationTime.competitor}</span>
        </div>
      </div>
    </div>
    
    <div className={styles.costSavings}>
      <div className={styles.costSavingsAmount}>{comparison.metrics.costSavings.amount}</div>
      <div className={styles.costSavingsDescription}>{comparison.metrics.costSavings.description}</div>
    </div>
  </div>
);

const PartnerEmpowerment = ({ partnerEmpowerment }) => (
  <div className={styles.partnerEmpowermentContainer}>
    <div className={styles.partnerEmpowermentTitle}>{partnerEmpowerment.title}</div>
    <div className={styles.partnerEmpowermentDescription}>{partnerEmpowerment.description}</div>
    
    <div>
      <div className={styles.useCaseSectionTitle}>Revenue Sharing Models:</div>
      <div className={styles.revenueModelsContainer}>
        {partnerEmpowerment.revenueSharing.models.map((model, index) => (
          <div key={index} className={styles.revenueModelCard}>
            <div className={styles.revenueModelName}>{model.name}</div>
            <div className={styles.revenueModelSplit}>{model.split}</div>
            <div className={styles.revenueModelDescription}>{model.description}</div>
          </div>
        ))}
      </div>
    </div>
    
    <div>
      <div className={styles.useCaseSectionTitle}>Partner Benefits:</div>
      <ul className={styles.benefitsList}>
        {partnerEmpowerment.benefits.map((benefit, index) => (
          <li key={index}>{benefit}</li>
        ))}
      </ul>
    </div>
    
    <div className={styles.onboardingContainer}>
      <div className={styles.onboardingTitle}>Onboarding Process</div>
      <ol className={styles.onboardingSteps}>
        {partnerEmpowerment.onboarding.steps.map((step, index) => (
          <li key={index}>{step}</li>
        ))}
      </ol>
      <div>Typical timeline: {partnerEmpowerment.onboarding.timeline}</div>
    </div>
  </div>
);

const ROICalculator = ({ roiCalculator }) => (
  <div className={styles.roiCalculatorContainer}>
    <div className={styles.roiCalculatorTitle}>{roiCalculator.title}</div>
    <div className={styles.roiCalculatorDescription}>{roiCalculator.description}</div>
    
    <form>
      {roiCalculator.inputs.map((input, index) => (
        <div key={index} className={styles.roiInputGroup}>
          <label className={styles.roiInputLabel}>{input.label}</label>
          {input.type === 'select' ? (
            <select className={styles.roiSelect}>
              <option value="">Select {input.label}</option>
              {input.options.map((option, i) => (
                <option key={i} value={option}>{option}</option>
              ))}
            </select>
          ) : input.type === 'multiselect' ? (
            <div>
              {input.options.map((option, i) => (
                <div key={i}>
                  <input type="checkbox" id={`${input.name}-${i}`} name={input.name} value={option} />
                  <label htmlFor={`${input.name}-${i}`}> {option}</label>
                </div>
              ))}
            </div>
          ) : (
            <input 
              type={input.type || 'text'} 
              className={styles.roiInput} 
              placeholder={input.placeholder || `Enter ${input.label}`}
            />
          )}
        </div>
      ))}
      
      <button type="button" className={styles.roiButton}>Calculate ROI</button>
    </form>
  </div>
);

export default function NovaConcierge() {
  const [messages, setMessages] = useState([
    { 
      role: 'assistant', 
      content: 'Hello! I\'m NovaConcierge, your API integration assistant. How can I help you today?',
      timestamp: new Date().toISOString()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const messagesEndRef = useRef(null);
  
  const conciergeService = new NovaConciergeService();
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!inputValue.trim() || isProcessing) return;
    
    // Add user message
    const userMessage = { 
      role: 'user', 
      content: inputValue,
      timestamp: new Date().toISOString()
    };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);
    
    try {
      // Process user query
      const response = await conciergeService.processQuery(userMessage.content);
      
      // Add assistant response
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: response.message,
        timestamp: new Date().toISOString(),
        metadata: response
      }]);
    } catch (error) {
      console.error('Error in NovaConcierge:', error);
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'I encountered an issue while processing your request. Please try again or contact our support team.',
        timestamp: new Date().toISOString(),
        error: true
      }]);
    } finally {
      setIsProcessing(false);
    }
  };
  
  const handleSuggestionSelect = async (suggestion) => {
    // Add user message based on suggestion
    const userMessage = { 
      role: 'user', 
      content: suggestion.title,
      timestamp: new Date().toISOString()
    };
    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);
    
    try {
      // Process the suggestion action
      let response;
      
      switch (suggestion.action) {
        case 'compare_competitors':
          response = await conciergeService.handleCompetitorComparison('Compare NovaFuse to competitors');
          break;
        case 'show_use_cases':
          response = await conciergeService.handleUseCaseQuery('Show me a success story');
          break;
        case 'calculate_roi':
          response = await conciergeService.handleROICalculator('Calculate ROI');
          break;
        case 'partner_empowerment':
          response = await conciergeService.handlePartnerEmpowerment('Tell me about Partner Empowerment');
          break;
        case 'api_recommendation':
          response = await conciergeService.processQuery('Recommend an API for my needs');
          break;
        default:
          response = await conciergeService.processQuery(suggestion.title);
      }
      
      // Add assistant response
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: response.message,
        timestamp: new Date().toISOString(),
        metadata: response
      }]);
    } catch (error) {
      console.error('Error handling suggestion:', error);
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'I encountered an issue while processing your request. Please try again or contact our support team.',
        timestamp: new Date().toISOString(),
        error: true
      }]);
    } finally {
      setIsProcessing(false);
    }
  };
  
  const renderMessageContent = (message) => {
    if (message.role === 'user') {
      return <div>{message.content}</div>;
    }
    
    // For assistant messages, check if there's metadata to render
    if (message.metadata) {
      return (
        <div>
          <div>{message.content}</div>
          
          {/* Render use case if present */}
          {message.metadata.useCase && (
            <UseCase useCase={message.metadata.useCase} />
          )}
          
          {/* Render competitor comparison if present */}
          {message.metadata.comparison && (
            <CompetitorComparison comparison={message.metadata.comparison} />
          )}
          
          {/* Render partner empowerment if present */}
          {message.metadata.partnerEmpowerment && (
            <PartnerEmpowerment partnerEmpowerment={message.metadata.partnerEmpowerment} />
          )}
          
          {/* Render ROI calculator if present */}
          {message.metadata.roiCalculator && (
            <ROICalculator roiCalculator={message.metadata.roiCalculator} />
          )}
          
          {/* Render suggestions if present */}
          {message.metadata.suggestions && message.metadata.suggestions.length > 0 && (
            <div className={styles.suggestionList}>
              {message.metadata.suggestions.map((suggestion, index) => (
                <ApiSuggestion 
                  key={index} 
                  suggestion={suggestion} 
                  onSelect={handleSuggestionSelect} 
                />
              ))}
            </div>
          )}
        </div>
      );
    }
    
    // Default rendering for simple messages
    return <div>{message.content}</div>;
  };
  
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>NovaConcierge</h2>
        <p>Your AI-powered API integration assistant</p>
      </div>
      
      <div className={styles.chatContainer}>
        {messages.map((msg, i) => (
          <div key={i} className={`${styles.message} ${styles[msg.role]}`}>
            {renderMessageContent(msg)}
          </div>
        ))}
        
        {isProcessing && (
          <div className={styles.processingIndicator}>
            <span>NovaConcierge is thinking...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      <form onSubmit={handleSubmit} className={styles.inputForm}>
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Ask about API integration..."
          disabled={isProcessing}
          className={styles.input}
        />
        <button 
          type="submit" 
          disabled={isProcessing || !inputValue.trim()}
          className={styles.button}
        >
          Send
        </button>
      </form>
    </div>
  );
}
