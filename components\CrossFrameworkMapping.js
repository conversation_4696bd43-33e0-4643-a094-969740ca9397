import React, { useState, useEffect } from 'react';
import { getAllFrameworks, getFrameworkToFrameworkMappings } from '../services/mappingService';

/**
 * Component for cross-framework mapping visualization
 */
const CrossFrameworkMapping = () => {
  const [frameworks, setFrameworks] = useState({});
  const [sourceFramework, setSourceFramework] = useState('');
  const [targetFramework, setTargetFramework] = useState('');
  const [mappings, setMappings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch all available frameworks
  useEffect(() => {
    const fetchFrameworks = async () => {
      try {
        setLoading(true);
        const data = await getAllFrameworks();
        setFrameworks(data);
        
        // Set default frameworks if available
        if (Object.keys(data).length > 0) {
          setSourceFramework(Object.keys(data)[0]);
          if (Object.keys(data).length > 1) {
            setTargetFramework(Object.keys(data)[1]);
          } else {
            setTargetFramework(Object.keys(data)[0]);
          }
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching frameworks:', error);
        setError('Failed to load frameworks. Please try again.');
        setLoading(false);
      }
    };
    
    fetchFrameworks();
  }, []);

  // Fetch mappings when source or target framework changes
  useEffect(() => {
    if (sourceFramework && targetFramework) {
      fetchMappings();
    }
  }, [sourceFramework, targetFramework]);

  // Fetch mappings between two frameworks
  const fetchMappings = async () => {
    try {
      setLoading(true);
      setError('');
      
      const data = await getFrameworkToFrameworkMappings(sourceFramework, targetFramework);
      setMappings(data);
      
      setLoading(false);
    } catch (error) {
      console.error('Error fetching mappings:', error);
      setError('Failed to load mappings. Please try again.');
      setLoading(false);
    }
  };

  // Handle source framework change
  const handleSourceFrameworkChange = (e) => {
    setSourceFramework(e.target.value);
  };

  // Handle target framework change
  const handleTargetFrameworkChange = (e) => {
    setTargetFramework(e.target.value);
  };

  // Get confidence color based on mapping strength
  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-400';
    if (confidence >= 0.5) return 'text-yellow-400';
    return 'text-red-400';
  };

  // Get confidence label based on mapping strength
  const getConfidenceLabel = (mappingStrength) => {
    switch (mappingStrength) {
      case 'full':
        return 'Full Match';
      case 'partial':
        return 'Partial Match';
      case 'related':
        return 'Related';
      default:
        return 'No Match';
    }
  };

  return (
    <div className="bg-secondary rounded-lg p-6">
      <h2 className="text-xl font-bold mb-4">Cross-Framework Mapping</h2>
      
      {error && (
        <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 mb-4 text-red-400">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label htmlFor="source-framework" className="block text-sm font-medium mb-1">
            Source Framework
          </label>
          <select
            id="source-framework"
            className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
            value={sourceFramework}
            onChange={handleSourceFrameworkChange}
            disabled={loading || Object.keys(frameworks).length === 0}
          >
            {Object.keys(frameworks).map(id => (
              <option key={id} value={id}>
                {frameworks[id].name} ({id.toUpperCase()})
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label htmlFor="target-framework" className="block text-sm font-medium mb-1">
            Target Framework
          </label>
          <select
            id="target-framework"
            className="w-full bg-gray-800 border border-gray-700 rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-600"
            value={targetFramework}
            onChange={handleTargetFrameworkChange}
            disabled={loading || Object.keys(frameworks).length === 0}
          >
            {Object.keys(frameworks).map(id => (
              <option key={id} value={id}>
                {frameworks[id].name} ({id.toUpperCase()})
              </option>
            ))}
          </select>
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : mappings ? (
        <div>
          <div className="mb-4">
            <h3 className="text-lg font-semibold mb-2">
              Mapping from {frameworks[sourceFramework]?.name} to {frameworks[targetFramework]?.name}
            </h3>
            <p className="text-gray-400">
              {Object.keys(mappings.mappings).length} controls mapped
            </p>
          </div>
          
          {Object.keys(mappings.mappings).length > 0 ? (
            <div className="border border-gray-700 rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-700">
                <thead className="bg-gray-800">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Source Control
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Target Controls
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                      Confidence
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-gray-900 divide-y divide-gray-800">
                  {Object.keys(mappings.mappings).map(sourceControl => (
                    <tr key={sourceControl}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {sourceControl}
                      </td>
                      <td className="px-6 py-4 text-sm">
                        <div className="flex flex-wrap gap-2">
                          {mappings.mappings[sourceControl].targetControls.map(targetControl => (
                            <span key={targetControl} className="bg-gray-800 px-2 py-1 rounded-full text-xs">
                              {targetControl}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <span className={getConfidenceColor(mappings.mappings[sourceControl].confidence)}>
                          {getConfidenceLabel(mappings.mappings[sourceControl].mappingStrength)}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <p>No mappings found between these frameworks.</p>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <p>Select source and target frameworks to view mappings.</p>
        </div>
      )}
    </div>
  );
};

export default CrossFrameworkMapping;
