import React, { useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

/**
 * NovaFuse Demo Template
 * 
 * A flexible, customizable demo template that can be tailored for specific partners
 * while maintaining the NovaFuse brand identity.
 * 
 * Features:
 * - Customizable for specific partners (<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, etc.)
 * - Supports different demo types (War Room, Executive, Technical)
 * - Integrates with the 144-slot ecosystem framework
 * - Highlights the four key pillars: UIs, GRC APIs, UAC, and App Store
 */
const DemoTemplate = ({
  partner = {}, // Partner-specific information
  demoType = 'standard', // 'war-room', 'executive', 'technical'
  ecosystemSlots = [], // Relevant slots from the 144-slot framework
  features = [], // Key features to highlight
  uiComponents = [], // UI components to showcase
  apiEndpoints = [], // API endpoints to demonstrate
  uacCapabilities = [], // UAC capabilities to highlight
  appStoreModules = [], // App Store modules to feature
  children
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [demoStatus, setDemoStatus] = useState('ready'); // ready, running, completed
  const [demoResults, setDemoResults] = useState(null);

  // Partner-specific styling
  const partnerStyles = partner.styles || {
    primaryColor: '#0A84FF', // NovaFuse blue default
    secondaryColor: '#1E3A8A',
    accentColor: '#60A5FA'
  };

  // Demo type configuration
  const demoConfig = {
    'war-room': {
      title: 'Live Compliance War Room',
      description: 'Real-time problem solving with NovaFuse engineers',
      icon: '🔥'
    },
    'executive': {
      title: 'Executive Briefing',
      description: 'Strategic overview for decision makers',
      icon: '📊'
    },
    'technical': {
      title: 'Technical Deep Dive',
      description: 'Detailed exploration of NovaFuse capabilities',
      icon: '⚙️'
    },
    'standard': {
      title: 'NovaFuse Demo',
      description: 'Experience the power of NovaFuse',
      icon: '🔌'
    }
  };

  const currentDemoConfig = demoConfig[demoType];

  // Run demo simulation
  const runDemo = async () => {
    setDemoStatus('running');
    
    // Simulate demo execution
    setTimeout(() => {
      setDemoResults({
        success: true,
        message: 'Demo completed successfully',
        data: {
          complianceScore: 87,
          riskReduction: '43%',
          timeToValue: '72 hours',
          roi: '312%'
        }
      });
      setDemoStatus('completed');
    }, 3000);
  };

  return (
    <div className="demo-template" style={{ 
      '--partner-primary': partnerStyles.primaryColor,
      '--partner-secondary': partnerStyles.secondaryColor,
      '--partner-accent': partnerStyles.accentColor
    }}>
      {/* Header Section */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-gradient-to-r from-blue-900 to-indigo-900 text-white rounded-lg p-8 mb-8 shadow-xl"
      >
        {/* Demo Type Badge */}
        <div className="flex justify-center mb-4">
          <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
            <span className="mr-2">{currentDemoConfig.icon}</span>
            <span>{currentDemoConfig.title.toUpperCase()}</span>
          </div>
        </div>
        
        {/* Partner-Specific Branding (if available) */}
        {partner.name && (
          <div className="flex justify-center items-center mb-6">
            <div className="bg-white bg-opacity-10 px-6 py-3 rounded-lg">
              <div className="flex items-center">
                {partner.logo && (
                  <img 
                    src={partner.logo} 
                    alt={`${partner.name} logo`} 
                    className="h-8 mr-3" 
                  />
                )}
                <div className="text-xl font-semibold">
                  {partner.tagline || `NovaFuse + ${partner.name}`}
                </div>
              </div>
            </div>
          </div>
        )}
        
        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">
          {partner.demoTitle || "Universal API Connector (UAC) Demo"}
        </h1>
        
        <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
          {partner.demoDescription || currentDemoConfig.description}
        </p>
        
        {/* Key Value Proposition */}
        {partner.valueProposition && (
          <div className="bg-blue-800 bg-opacity-30 p-4 rounded-lg max-w-2xl mx-auto mb-6">
            <p className="text-center italic">"{partner.valueProposition}"</p>
          </div>
        )}
        
        {/* Key Features */}
        {features.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
            {features.map((feature, index) => (
              <motion.div 
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                className="bg-blue-800 bg-opacity-50 p-4 rounded-lg"
              >
                <div className="flex items-center mb-2">
                  <span className="text-blue-300 mr-2">{feature.icon || '✓'}</span>
                  <h3 className="font-semibold">{feature.title}</h3>
                </div>
                <p className="text-sm text-blue-100">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Main Demo Content */}
      <div className="bg-gray-900 rounded-lg shadow-xl overflow-hidden mb-8">
        {/* Navigation Tabs */}
        <div className="bg-gray-800 px-4">
          <nav className="flex space-x-4">
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'overview'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'ui'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('ui')}
            >
              UI Components
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'api'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('api')}
            >
              GRC APIs
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'uac'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('uac')}
            >
              UAC
            </button>
            <button
              className={`py-4 px-3 text-sm font-medium border-b-2 ${
                activeTab === 'appstore'
                  ? 'border-blue-500 text-blue-500'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('appstore')}
            >
              App Store
            </button>
            {partner.customTabs && partner.customTabs.map(tab => (
              <button
                key={tab.id}
                className={`py-4 px-3 text-sm font-medium border-b-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-500'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Demo Overview</h2>
              <p className="text-gray-300 mb-6">
                {partner.overviewText || 
                  "This demo showcases NovaFuse's capabilities and how they can integrate with your existing systems to enhance compliance operations."}
              </p>
              
              {/* Ecosystem Slots */}
              {ecosystemSlots.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-xl font-semibold mb-3">Relevant Ecosystem Categories</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {ecosystemSlots.map((slot, index) => (
                      <div key={index} className="bg-gray-800 p-3 rounded-lg border border-gray-700">
                        <div className="flex items-center">
                          <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                            {slot.id}
                          </span>
                          <span className="font-medium">{slot.name}</span>
                        </div>
                        {slot.description && (
                          <p className="text-sm text-gray-400 mt-1">{slot.description}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Run Demo Button */}
              <div className="mt-8">
                <button
                  className={`px-6 py-3 rounded-lg font-bold text-white ${
                    demoStatus === 'running'
                      ? 'bg-gray-600 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                  onClick={runDemo}
                  disabled={demoStatus === 'running'}
                >
                  {demoStatus === 'ready' && 'Run Demo'}
                  {demoStatus === 'running' && (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Running Demo...
                    </span>
                  )}
                  {demoStatus === 'completed' && 'Run Demo Again'}
                </button>
              </div>
              
              {/* Demo Results */}
              {demoResults && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  transition={{ duration: 0.5 }}
                  className="mt-6 bg-gray-800 rounded-lg p-4"
                >
                  <h3 className="text-xl font-semibold mb-3">Demo Results</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-gray-900 p-4 rounded-lg text-center">
                      <div className="text-3xl font-bold text-blue-500 mb-1">{demoResults.data.complianceScore}%</div>
                      <div className="text-sm text-gray-400">Compliance Score</div>
                    </div>
                    <div className="bg-gray-900 p-4 rounded-lg text-center">
                      <div className="text-3xl font-bold text-green-500 mb-1">{demoResults.data.riskReduction}</div>
                      <div className="text-sm text-gray-400">Risk Reduction</div>
                    </div>
                    <div className="bg-gray-900 p-4 rounded-lg text-center">
                      <div className="text-3xl font-bold text-purple-500 mb-1">{demoResults.data.timeToValue}</div>
                      <div className="text-sm text-gray-400">Time to Value</div>
                    </div>
                    <div className="bg-gray-900 p-4 rounded-lg text-center">
                      <div className="text-3xl font-bold text-yellow-500 mb-1">{demoResults.data.roi}</div>
                      <div className="text-sm text-gray-400">ROI</div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          )}
          
          {/* UI Components Tab */}
          {activeTab === 'ui' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">UI Components</h2>
              <p className="text-gray-300 mb-6">
                Explore NovaFuse's user interface components designed for seamless integration with {partner.name || "your platform"}.
              </p>
              
              {uiComponents.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {uiComponents.map((component, index) => (
                    <div key={index} className="border border-gray-700 rounded-lg overflow-hidden">
                      <div className="bg-gray-800 p-3">
                        <h3 className="font-medium">{component.name}</h3>
                      </div>
                      <div className="p-4">
                        {component.image ? (
                          <img 
                            src={component.image} 
                            alt={component.name} 
                            className="w-full rounded-lg mb-3" 
                          />
                        ) : (
                          <div className="bg-gray-800 h-40 rounded-lg flex items-center justify-center mb-3">
                            <span className="text-gray-500">UI Preview</span>
                          </div>
                        )}
                        <p className="text-sm text-gray-400">{component.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800 p-4 rounded-lg">
                  <p className="text-gray-400">UI components will be customized for your specific needs.</p>
                </div>
              )}
            </div>
          )}
          
          {/* GRC APIs Tab */}
          {activeTab === 'api' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">GRC APIs</h2>
              <p className="text-gray-300 mb-6">
                Explore NovaFuse's powerful GRC APIs that can be integrated with {partner.name || "your platform"}.
              </p>
              
              {apiEndpoints.length > 0 ? (
                <div className="space-y-4">
                  {apiEndpoints.map((endpoint, index) => (
                    <div key={index} className="border border-gray-700 rounded-lg overflow-hidden">
                      <div className="bg-gray-800 p-3 flex items-center">
                        <span className={`px-2 py-1 rounded text-xs font-bold mr-3 ${
                          endpoint.method === 'GET' ? 'bg-green-900 text-green-300' :
                          endpoint.method === 'POST' ? 'bg-blue-900 text-blue-300' :
                          endpoint.method === 'PUT' ? 'bg-yellow-900 text-yellow-300' :
                          endpoint.method === 'DELETE' ? 'bg-red-900 text-red-300' :
                          'bg-gray-700 text-gray-300'
                        }`}>
                          {endpoint.method}
                        </span>
                        <code className="font-mono text-sm">{endpoint.path}</code>
                      </div>
                      <div className="p-4">
                        <p className="text-sm text-gray-400 mb-3">{endpoint.description}</p>
                        {endpoint.example && (
                          <div className="bg-gray-900 p-3 rounded-lg">
                            <pre className="text-xs text-gray-300 overflow-x-auto">
                              {JSON.stringify(endpoint.example, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800 p-4 rounded-lg">
                  <p className="text-gray-400">API endpoints will be customized for your specific needs.</p>
                </div>
              )}
            </div>
          )}
          
          {/* UAC Tab */}
          {activeTab === 'uac' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Universal API Connector (UAC)</h2>
              <p className="text-gray-300 mb-6">
                Discover how NovaFuse's Universal API Connector can transform your compliance operations.
              </p>
              
              {uacCapabilities.length > 0 ? (
                <div className="space-y-6">
                  {uacCapabilities.map((capability, index) => (
                    <div key={index} className="bg-gray-800 p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-2">{capability.name}</h3>
                      <p className="text-gray-400 mb-4">{capability.description}</p>
                      
                      {capability.features && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                          {capability.features.map((feature, idx) => (
                            <div key={idx} className="flex items-start">
                              <span className="text-blue-500 mr-2">✓</span>
                              <span className="text-sm text-gray-300">{feature}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800 p-4 rounded-lg">
                  <p className="text-gray-400">UAC capabilities will be customized for your specific needs.</p>
                </div>
              )}
            </div>
          )}
          
          {/* App Store Tab */}
          {activeTab === 'appstore' && (
            <div>
              <h2 className="text-2xl font-bold mb-4">Compliance App Store</h2>
              <p className="text-gray-300 mb-6">
                Explore NovaFuse's Compliance App Store with plug-and-play modules for {partner.name || "your platform"}.
              </p>
              
              {appStoreModules.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {appStoreModules.map((app, index) => (
                    <div key={index} className="border border-gray-700 rounded-lg overflow-hidden">
                      <div className="p-4">
                        <div className="flex items-center mb-3">
                          <span className="text-2xl mr-3">{app.icon || '📱'}</span>
                          <h3 className="font-medium">{app.name}</h3>
                        </div>
                        <p className="text-sm text-gray-400 mb-3">{app.description}</p>
                        <div className="flex flex-wrap gap-1 mb-3">
                          {app.tags && app.tags.map((tag, idx) => (
                            <span key={idx} className="bg-gray-700 text-xs px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                        <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded text-sm">
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-800 p-4 rounded-lg">
                  <p className="text-gray-400">App Store modules will be customized for your specific needs.</p>
                </div>
              )}
            </div>
          )}
          
          {/* Custom Tabs */}
          {partner.customTabs && partner.customTabs.map(tab => (
            activeTab === tab.id && (
              <div key={tab.id}>
                <h2 className="text-2xl font-bold mb-4">{tab.label}</h2>
                <div dangerouslySetInnerHTML={{ __html: tab.content }} />
              </div>
            )
          ))}
          
          {/* Custom Demo Content */}
          {children}
        </div>
      </div>

      {/* Partner-Specific Call to Action */}
      <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-6 text-center">
        <h2 className="text-2xl font-bold mb-3">Ready to Transform Your Compliance Operations?</h2>
        <p className="text-lg mb-6 max-w-3xl mx-auto">
          {partner.ctaText || "Join the NovaFuse ecosystem and revolutionize how you manage compliance."}
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <a 
            href={partner.primaryCta?.url || "/contact"} 
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold"
          >
            {partner.primaryCta?.text || "Schedule a Personalized Demo"}
          </a>
          <a 
            href={partner.secondaryCta?.url || "/partner-program"} 
            className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900"
          >
            {partner.secondaryCta?.text || "Learn About Our Partner Program"}
          </a>
        </div>
      </div>
    </div>
  );
};

export default DemoTemplate;
