/**
 * NovaAssure Dashboard Page
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Flex,
  Heading,
  Text,
  SimpleGrid,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  StatGroup,
  Progress,
  Card,
  CardHeader,
  CardBody,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Badge,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  Icon,
  useColorModeValue
} from '@chakra-ui/react';
import { 
  CheckCircleIcon, 
  WarningIcon, 
  TimeIcon, 
  InfoIcon,
  CalendarIcon
} from '@chakra-ui/icons';
import { controlsApi, testPlansApi, testExecutionApi, evidenceApi } from '../utils/api';

export default function Dashboard() {
  const [stats, setStats] = useState({
    controls: {
      total: 0,
      implemented: 0,
      partiallyImplemented: 0,
      notImplemented: 0
    },
    testPlans: {
      total: 0,
      active: 0,
      completed: 0,
      draft: 0
    },
    testExecutions: {
      total: 0,
      completed: 0,
      inProgress: 0,
      pending: 0,
      failed: 0
    },
    evidence: {
      total: 0,
      verified: 0,
      unverified: 0
    }
  });
  const [recentTestExecutions, setRecentTestExecutions] = useState([]);
  const [upcomingTestPlans, setUpcomingTestPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const cardBg = useColorModeValue('white', 'gray.700');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch controls stats
        const controlsResponse = await controlsApi.getAll({ limit: 1 });
        const controlsTotal = controlsResponse.data.pagination.total;
        
        // Fetch controls by implementation status
        const implementedResponse = await controlsApi.getAll({ 
          implementationStatus: 'implemented',
          limit: 1
        });
        const implementedTotal = implementedResponse.data.pagination.total;
        
        const partiallyImplementedResponse = await controlsApi.getAll({ 
          implementationStatus: 'partially-implemented',
          limit: 1
        });
        const partiallyImplementedTotal = partiallyImplementedResponse.data.pagination.total;
        
        const notImplementedResponse = await controlsApi.getAll({ 
          implementationStatus: 'not-implemented',
          limit: 1
        });
        const notImplementedTotal = notImplementedResponse.data.pagination.total;

        // Fetch test plans stats
        const testPlansResponse = await testPlansApi.getAll({ limit: 1 });
        const testPlansTotal = testPlansResponse.data.pagination.total;
        
        const activeTestPlansResponse = await testPlansApi.getAll({ 
          status: 'active',
          limit: 1
        });
        const activeTestPlansTotal = activeTestPlansResponse.data.pagination.total;
        
        const completedTestPlansResponse = await testPlansApi.getAll({ 
          status: 'completed',
          limit: 1
        });
        const completedTestPlansTotal = completedTestPlansResponse.data.pagination.total;
        
        const draftTestPlansResponse = await testPlansApi.getAll({ 
          status: 'draft',
          limit: 1
        });
        const draftTestPlansTotal = draftTestPlansResponse.data.pagination.total;

        // Fetch test executions stats
        const testExecutionsResponse = await testExecutionApi.getAll({ limit: 1 });
        const testExecutionsTotal = testExecutionsResponse.data.pagination.total;
        
        const completedTestExecutionsResponse = await testExecutionApi.getAll({ 
          status: 'completed',
          limit: 1
        });
        const completedTestExecutionsTotal = completedTestExecutionsResponse.data.pagination.total;
        
        const inProgressTestExecutionsResponse = await testExecutionApi.getAll({ 
          status: 'in-progress',
          limit: 1
        });
        const inProgressTestExecutionsTotal = inProgressTestExecutionsResponse.data.pagination.total;
        
        const pendingTestExecutionsResponse = await testExecutionApi.getAll({ 
          status: 'pending',
          limit: 1
        });
        const pendingTestExecutionsTotal = pendingTestExecutionsResponse.data.pagination.total;
        
        const failedTestExecutionsResponse = await testExecutionApi.getAll({ 
          status: 'failed',
          limit: 1
        });
        const failedTestExecutionsTotal = failedTestExecutionsResponse.data.pagination.total;

        // Fetch evidence stats
        const evidenceResponse = await evidenceApi.getAll({ limit: 1 });
        const evidenceTotal = evidenceResponse.data.pagination.total;
        
        // For verified/unverified, we'd need a specific API endpoint
        // This is a placeholder
        const verifiedTotal = Math.floor(evidenceTotal * 0.8);
        const unverifiedTotal = evidenceTotal - verifiedTotal;

        // Fetch recent test executions
        const recentTestExecutionsResponse = await testExecutionApi.getAll({
          limit: 5,
          sort: '-startedAt'
        });
        setRecentTestExecutions(recentTestExecutionsResponse.data.testExecutions);

        // Fetch upcoming test plans
        const upcomingTestPlansResponse = await testPlansApi.getAll({
          status: 'active',
          limit: 5,
          sort: 'schedule.startDate'
        });
        setUpcomingTestPlans(upcomingTestPlansResponse.data.testPlans);

        // Update stats
        setStats({
          controls: {
            total: controlsTotal,
            implemented: implementedTotal,
            partiallyImplemented: partiallyImplementedTotal,
            notImplemented: notImplementedTotal
          },
          testPlans: {
            total: testPlansTotal,
            active: activeTestPlansTotal,
            completed: completedTestPlansTotal,
            draft: draftTestPlansTotal
          },
          testExecutions: {
            total: testExecutionsTotal,
            completed: completedTestExecutionsTotal,
            inProgress: inProgressTestExecutionsTotal,
            pending: pendingTestExecutionsTotal,
            failed: failedTestExecutionsTotal
          },
          evidence: {
            total: evidenceTotal,
            verified: verifiedTotal,
            unverified: unverifiedTotal
          }
        });
      } catch (err) {
        setError(err.response?.data?.error || err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Calculate implementation percentage
  const implementationPercentage = stats.controls.total > 0
    ? Math.round((stats.controls.implemented / stats.controls.total) * 100)
    : 0;

  // Calculate test execution success rate
  const testExecutionSuccessRate = stats.testExecutions.completed > 0
    ? Math.round((stats.testExecutions.completed / (stats.testExecutions.completed + stats.testExecutions.failed)) * 100)
    : 0;

  // Calculate evidence verification rate
  const evidenceVerificationRate = stats.evidence.total > 0
    ? Math.round((stats.evidence.verified / stats.evidence.total) * 100)
    : 0;

  // Status badge colors
  const statusColors = {
    'draft': 'gray',
    'active': 'blue',
    'completed': 'green',
    'archived': 'purple',
    'pending': 'yellow',
    'in-progress': 'orange',
    'failed': 'red',
    'cancelled': 'red'
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Box p={4}>
      <Heading size="lg" mb={6}>NovaAssure Dashboard</Heading>

      {/* Error message */}
      {error && (
        <Box mb={4} p={3} bg="red.100" color="red.800" borderRadius="md">
          {error}
        </Box>
      )}

      {/* Loading indicator */}
      {loading && (
        <Progress isIndeterminate mb={6} />
      )}

      {/* Stats cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing={6} mb={8}>
        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden">
          <CardHeader pb={0}>
            <Heading size="md">Controls</Heading>
          </CardHeader>
          <CardBody>
            <StatGroup>
              <Stat>
                <StatNumber>{stats.controls.total}</StatNumber>
                <StatLabel>Total Controls</StatLabel>
              </Stat>
              <Stat>
                <StatNumber>{implementationPercentage}%</StatNumber>
                <StatLabel>Implemented</StatLabel>
                <StatHelpText>
                  <StatArrow type="increase" />
                  {stats.controls.implemented} controls
                </StatHelpText>
              </Stat>
            </StatGroup>
            <Progress 
              value={implementationPercentage} 
              colorScheme="green" 
              mt={2}
              borderRadius="md"
            />
          </CardBody>
        </Card>

        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden">
          <CardHeader pb={0}>
            <Heading size="md">Test Plans</Heading>
          </CardHeader>
          <CardBody>
            <StatGroup>
              <Stat>
                <StatNumber>{stats.testPlans.total}</StatNumber>
                <StatLabel>Total Plans</StatLabel>
              </Stat>
              <Stat>
                <StatNumber>{stats.testPlans.active}</StatNumber>
                <StatLabel>Active</StatLabel>
                <StatHelpText>
                  <StatArrow type="increase" />
                  {stats.testPlans.completed} completed
                </StatHelpText>
              </Stat>
            </StatGroup>
            <Flex mt={2} justify="space-between">
              <Badge colorScheme="blue" px={2} py={1} borderRadius="md">
                Active: {stats.testPlans.active}
              </Badge>
              <Badge colorScheme="green" px={2} py={1} borderRadius="md">
                Completed: {stats.testPlans.completed}
              </Badge>
              <Badge colorScheme="gray" px={2} py={1} borderRadius="md">
                Draft: {stats.testPlans.draft}
              </Badge>
            </Flex>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden">
          <CardHeader pb={0}>
            <Heading size="md">Test Executions</Heading>
          </CardHeader>
          <CardBody>
            <StatGroup>
              <Stat>
                <StatNumber>{stats.testExecutions.total}</StatNumber>
                <StatLabel>Total Executions</StatLabel>
              </Stat>
              <Stat>
                <StatNumber>{testExecutionSuccessRate}%</StatNumber>
                <StatLabel>Success Rate</StatLabel>
                <StatHelpText>
                  <StatArrow type="increase" />
                  {stats.testExecutions.completed} passed
                </StatHelpText>
              </Stat>
            </StatGroup>
            <Progress 
              value={testExecutionSuccessRate} 
              colorScheme="blue" 
              mt={2}
              borderRadius="md"
            />
          </CardBody>
        </Card>

        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden">
          <CardHeader pb={0}>
            <Heading size="md">Evidence</Heading>
          </CardHeader>
          <CardBody>
            <StatGroup>
              <Stat>
                <StatNumber>{stats.evidence.total}</StatNumber>
                <StatLabel>Total Evidence</StatLabel>
              </Stat>
              <Stat>
                <StatNumber>{evidenceVerificationRate}%</StatNumber>
                <StatLabel>Verified</StatLabel>
                <StatHelpText>
                  <StatArrow type="increase" />
                  {stats.evidence.verified} items
                </StatHelpText>
              </Stat>
            </StatGroup>
            <Progress 
              value={evidenceVerificationRate} 
              colorScheme="purple" 
              mt={2}
              borderRadius="md"
            />
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Recent activity and upcoming tests */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden">
          <CardHeader>
            <Heading size="md">Recent Test Executions</Heading>
          </CardHeader>
          <CardBody>
            {recentTestExecutions.length === 0 ? (
              <Text>No recent test executions</Text>
            ) : (
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Test Plan</Th>
                    <Th>Status</Th>
                    <Th>Started</Th>
                    <Th>Completed</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {recentTestExecutions.map(execution => (
                    <Tr key={execution._id}>
                      <Td>{execution.testPlan?.name || execution.testPlan}</Td>
                      <Td>
                        <Badge colorScheme={statusColors[execution.status]}>
                          {execution.status}
                        </Badge>
                      </Td>
                      <Td>{formatDate(execution.startedAt)}</Td>
                      <Td>{formatDate(execution.completedAt)}</Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
            <Button size="sm" colorScheme="blue" mt={4} variant="outline">
              View All
            </Button>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden">
          <CardHeader>
            <Heading size="md">Upcoming Test Plans</Heading>
          </CardHeader>
          <CardBody>
            {upcomingTestPlans.length === 0 ? (
              <Text>No upcoming test plans</Text>
            ) : (
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>Test Plan</Th>
                    <Th>Framework</Th>
                    <Th>Status</Th>
                    <Th>Next Run</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {upcomingTestPlans.map(plan => (
                    <Tr key={plan._id}>
                      <Td>{plan.name}</Td>
                      <Td>{plan.framework}</Td>
                      <Td>
                        <Badge colorScheme={statusColors[plan.status]}>
                          {plan.status}
                        </Badge>
                      </Td>
                      <Td>
                        <Flex align="center">
                          <Icon as={CalendarIcon} mr={1} />
                          {formatDate(plan.schedule?.startDate)}
                        </Flex>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
            <Button size="sm" colorScheme="blue" mt={4} variant="outline">
              View All
            </Button>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Compliance status by framework */}
      <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} borderRadius="lg" overflow="hidden" mt={6}>
        <CardHeader>
          <Heading size="md">Compliance Status</Heading>
        </CardHeader>
        <CardBody>
          <Tabs>
            <TabList>
              <Tab>SOC 2</Tab>
              <Tab>GDPR</Tab>
              <Tab>HIPAA</Tab>
              <Tab>ISO 27001</Tab>
            </TabList>
            <TabPanels>
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4}>
                  <Stat>
                    <Flex align="center">
                      <Icon as={CheckCircleIcon} color="green.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Compliant</StatLabel>
                        <StatNumber>75%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={WarningIcon} color="orange.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>At Risk</StatLabel>
                        <StatNumber>15%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={InfoIcon} color="red.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Non-Compliant</StatLabel>
                        <StatNumber>10%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={TimeIcon} color="blue.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Last Tested</StatLabel>
                        <StatNumber>7 days ago</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                </SimpleGrid>
                <Progress 
                  value={75} 
                  colorScheme="green" 
                  mt={4}
                  borderRadius="md"
                  size="lg"
                />
              </TabPanel>
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4}>
                  <Stat>
                    <Flex align="center">
                      <Icon as={CheckCircleIcon} color="green.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Compliant</StatLabel>
                        <StatNumber>82%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={WarningIcon} color="orange.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>At Risk</StatLabel>
                        <StatNumber>10%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={InfoIcon} color="red.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Non-Compliant</StatLabel>
                        <StatNumber>8%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={TimeIcon} color="blue.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Last Tested</StatLabel>
                        <StatNumber>14 days ago</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                </SimpleGrid>
                <Progress 
                  value={82} 
                  colorScheme="green" 
                  mt={4}
                  borderRadius="md"
                  size="lg"
                />
              </TabPanel>
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4}>
                  <Stat>
                    <Flex align="center">
                      <Icon as={CheckCircleIcon} color="green.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Compliant</StatLabel>
                        <StatNumber>68%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={WarningIcon} color="orange.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>At Risk</StatLabel>
                        <StatNumber>22%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={InfoIcon} color="red.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Non-Compliant</StatLabel>
                        <StatNumber>10%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={TimeIcon} color="blue.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Last Tested</StatLabel>
                        <StatNumber>30 days ago</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                </SimpleGrid>
                <Progress 
                  value={68} 
                  colorScheme="green" 
                  mt={4}
                  borderRadius="md"
                  size="lg"
                />
              </TabPanel>
              <TabPanel>
                <SimpleGrid columns={{ base: 1, md: 4 }} spacing={4}>
                  <Stat>
                    <Flex align="center">
                      <Icon as={CheckCircleIcon} color="green.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Compliant</StatLabel>
                        <StatNumber>90%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={WarningIcon} color="orange.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>At Risk</StatLabel>
                        <StatNumber>5%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={InfoIcon} color="red.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Non-Compliant</StatLabel>
                        <StatNumber>5%</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                  <Stat>
                    <Flex align="center">
                      <Icon as={TimeIcon} color="blue.500" boxSize={6} mr={2} />
                      <Box>
                        <StatLabel>Last Tested</StatLabel>
                        <StatNumber>5 days ago</StatNumber>
                      </Box>
                    </Flex>
                  </Stat>
                </SimpleGrid>
                <Progress 
                  value={90} 
                  colorScheme="green" 
                  mt={4}
                  borderRadius="md"
                  size="lg"
                />
              </TabPanel>
            </TabPanels>
          </Tabs>
        </CardBody>
      </Card>
    </Box>
  );
}
