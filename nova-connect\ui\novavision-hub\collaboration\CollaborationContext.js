/**
 * Collaboration Context
 * 
 * This module provides collaboration context and provider for the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { useAuth } from '../auth/AuthContext';
import { usePerformance } from '../performance/usePerformance';

// Create collaboration context
const CollaborationContext = createContext();

/**
 * Collaboration provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.collaborationService] - Collaboration service
 * @param {Function} [props.onCollaborationStateChanged] - Callback when collaboration state changes
 * @returns {React.ReactElement} Collaboration provider component
 */
export const CollaborationProvider = ({
  children,
  collaborationService,
  onCollaborationStateChanged
}) => {
  const { measureOperation } = usePerformance('CollaborationProvider');
  const { user, isAuthenticated, getToken } = useAuth();
  
  // State
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeUsers, setActiveUsers] = useState([]);
  const [activeRooms, setActiveRooms] = useState([]);
  const [currentRoom, setCurrentRoom] = useState(null);
  const [messages, setMessages] = useState([]);
  const [cursors, setCursors] = useState({});
  const [sharedState, setSharedState] = useState({});
  
  // Initialize collaboration
  useEffect(() => {
    if (!collaborationService || !isAuthenticated) {
      setIsLoading(false);
      return;
    }
    
    const initializeCollaboration = async () => {
      try {
        // Get authentication token
        const token = await getToken();
        
        // Initialize collaboration service
        await measureOperation('initializeCollaboration', () => 
          collaborationService.initialize(token, user)
        );
        
        setIsConnected(true);
        
        // Get active rooms
        const rooms = await measureOperation('getActiveRooms', () => 
          collaborationService.getActiveRooms()
        );
        
        setActiveRooms(rooms);
        
        // Set up event listeners
        collaborationService.onConnectionStateChanged((state) => {
          setIsConnected(state.isConnected);
          
          if (onCollaborationStateChanged) {
            onCollaborationStateChanged(state);
          }
        });
        
        collaborationService.onActiveUsersChanged((users) => {
          setActiveUsers(users);
        });
        
        collaborationService.onMessageReceived((message) => {
          setMessages(prevMessages => [...prevMessages, message]);
        });
        
        collaborationService.onCursorMoved((userId, position) => {
          setCursors(prevCursors => ({
            ...prevCursors,
            [userId]: position
          }));
        });
        
        collaborationService.onSharedStateChanged((newState) => {
          setSharedState(newState);
        });
      } catch (err) {
        console.error('Error initializing collaboration:', err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeCollaboration();
    
    // Cleanup
    return () => {
      if (collaborationService) {
        collaborationService.disconnect();
      }
    };
  }, [collaborationService, isAuthenticated, user, getToken, onCollaborationStateChanged, measureOperation]);
  
  /**
   * Join room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Room
   */
  const joinRoom = useCallback(async (roomId) => {
    if (!collaborationService || !isConnected) {
      throw new Error('Collaboration service not connected');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const room = await measureOperation('joinRoom', () => 
        collaborationService.joinRoom(roomId)
      );
      
      setCurrentRoom(room);
      
      // Get room messages
      const roomMessages = await measureOperation('getRoomMessages', () => 
        collaborationService.getRoomMessages(roomId)
      );
      
      setMessages(roomMessages);
      
      // Get room users
      const roomUsers = await measureOperation('getRoomUsers', () => 
        collaborationService.getRoomUsers(roomId)
      );
      
      setActiveUsers(roomUsers);
      
      // Get shared state
      const roomState = await measureOperation('getSharedState', () => 
        collaborationService.getSharedState(roomId)
      );
      
      setSharedState(roomState);
      
      return room;
    } catch (err) {
      console.error('Error joining room:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [collaborationService, isConnected, measureOperation]);
  
  /**
   * Leave room
   * 
   * @returns {Promise<void>}
   */
  const leaveRoom = useCallback(async () => {
    if (!collaborationService || !isConnected || !currentRoom) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await measureOperation('leaveRoom', () => 
        collaborationService.leaveRoom(currentRoom.id)
      );
      
      setCurrentRoom(null);
      setMessages([]);
      setCursors({});
      setSharedState({});
    } catch (err) {
      console.error('Error leaving room:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [collaborationService, isConnected, currentRoom, measureOperation]);
  
  /**
   * Create room
   * 
   * @param {Object} roomData - Room data
   * @returns {Promise<Object>} Room
   */
  const createRoom = useCallback(async (roomData) => {
    if (!collaborationService || !isConnected) {
      throw new Error('Collaboration service not connected');
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const room = await measureOperation('createRoom', () => 
        collaborationService.createRoom(roomData)
      );
      
      setActiveRooms(prevRooms => [...prevRooms, room]);
      
      return room;
    } catch (err) {
      console.error('Error creating room:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [collaborationService, isConnected, measureOperation]);
  
  /**
   * Send message
   * 
   * @param {string} content - Message content
   * @param {string} [type='text'] - Message type
   * @returns {Promise<Object>} Message
   */
  const sendMessage = useCallback(async (content, type = 'text') => {
    if (!collaborationService || !isConnected || !currentRoom) {
      throw new Error('Not connected to a room');
    }
    
    setError(null);
    
    try {
      const message = await measureOperation('sendMessage', () => 
        collaborationService.sendMessage(currentRoom.id, {
          content,
          type,
          timestamp: new Date().toISOString()
        })
      );
      
      setMessages(prevMessages => [...prevMessages, message]);
      
      return message;
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err);
      throw err;
    }
  }, [collaborationService, isConnected, currentRoom, measureOperation]);
  
  /**
   * Update cursor position
   * 
   * @param {Object} position - Cursor position
   * @returns {Promise<void>}
   */
  const updateCursorPosition = useCallback(async (position) => {
    if (!collaborationService || !isConnected || !currentRoom) {
      return;
    }
    
    try {
      await measureOperation('updateCursorPosition', () => 
        collaborationService.updateCursorPosition(currentRoom.id, position)
      );
      
      // Update local cursors
      if (user) {
        setCursors(prevCursors => ({
          ...prevCursors,
          [user.id]: position
        }));
      }
    } catch (err) {
      console.error('Error updating cursor position:', err);
    }
  }, [collaborationService, isConnected, currentRoom, user, measureOperation]);
  
  /**
   * Update shared state
   * 
   * @param {Object} update - State update
   * @returns {Promise<Object>} Updated state
   */
  const updateSharedState = useCallback(async (update) => {
    if (!collaborationService || !isConnected || !currentRoom) {
      throw new Error('Not connected to a room');
    }
    
    setError(null);
    
    try {
      const newState = await measureOperation('updateSharedState', () => 
        collaborationService.updateSharedState(currentRoom.id, update)
      );
      
      setSharedState(newState);
      
      return newState;
    } catch (err) {
      console.error('Error updating shared state:', err);
      setError(err);
      throw err;
    }
  }, [collaborationService, isConnected, currentRoom, measureOperation]);
  
  // Create context value
  const contextValue = {
    isConnected,
    isLoading,
    error,
    activeUsers,
    activeRooms,
    currentRoom,
    messages,
    cursors,
    sharedState,
    joinRoom,
    leaveRoom,
    createRoom,
    sendMessage,
    updateCursorPosition,
    updateSharedState
  };
  
  return (
    <CollaborationContext.Provider value={contextValue}>
      {children}
    </CollaborationContext.Provider>
  );
};

CollaborationProvider.propTypes = {
  children: PropTypes.node.isRequired,
  collaborationService: PropTypes.object,
  onCollaborationStateChanged: PropTypes.func
};

/**
 * Use collaboration hook
 * 
 * @returns {Object} Collaboration context
 */
export const useCollaboration = () => {
  const context = useContext(CollaborationContext);
  
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  
  return context;
};

export default CollaborationContext;
