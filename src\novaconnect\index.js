/**
 * NovaConnect Module
 * 
 * This module provides a mock implementation of NovaConnect for testing purposes.
 */

const EventEmitter = require('events');

/**
 * NovaConnect
 */
class NovaConnect extends EventEmitter {
  /**
   * Constructor
   * 
   * @param {Object} options - NovaConnect options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: options.enableLogging || false,
      ...options
    };
    
    this.subscribers = new Map();
    
    if (this.options.enableLogging) {
      console.log('NovaConnect initialized with options:', this.options);
    }
  }
  
  /**
   * Subscribe to a topic
   * 
   * @param {string} topic - Topic to subscribe to
   * @param {Function} callback - Callback function
   * @returns {Promise} - Promise that resolves when subscribed
   */
  async subscribe(topic, callback) {
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, []);
    }
    
    this.subscribers.get(topic).push(callback);
    
    if (this.options.enableLogging) {
      console.log(`Subscribed to topic: ${topic}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Unsubscribe from a topic
   * 
   * @param {string} topic - Topic to unsubscribe from
   * @param {Function} callback - Callback function
   * @returns {Promise} - Promise that resolves when unsubscribed
   */
  async unsubscribe(topic, callback) {
    if (!this.subscribers.has(topic)) {
      return Promise.resolve();
    }
    
    if (callback) {
      const callbacks = this.subscribers.get(topic);
      const index = callbacks.indexOf(callback);
      
      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.subscribers.delete(topic);
    }
    
    if (this.options.enableLogging) {
      console.log(`Unsubscribed from topic: ${topic}`);
    }
    
    return Promise.resolve();
  }
  
  /**
   * Publish a message to a topic
   * 
   * @param {string} topic - Topic to publish to
   * @param {Object} message - Message to publish
   * @returns {Promise} - Promise that resolves when published
   */
  async publish(topic, message) {
    if (!this.subscribers.has(topic)) {
      return Promise.resolve();
    }
    
    const callbacks = this.subscribers.get(topic);
    
    for (const callback of callbacks) {
      try {
        callback(message, topic);
      } catch (error) {
        console.error(`Error in subscriber callback for topic ${topic}:`, error);
      }
    }
    
    if (this.options.enableLogging) {
      console.log(`Published message to topic: ${topic}`);
    }
    
    return Promise.resolve();
  }
}

// Create a singleton instance
const novaConnect = new NovaConnect();

module.exports = {
  NovaConnect,
  novaConnect
};
