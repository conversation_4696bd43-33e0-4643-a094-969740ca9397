const {
  getAllFrameworks,
  getFramework,
  mapControl,
  mapControls,
  getAllMappingsForControl,
  getFrameworkToFrameworkMappings,
  calculateComplianceCoverage
} = require('../services/crossFrameworkMappingEngine');

/**
 * Get all available frameworks
 */
const getAllFrameworksHandler = async (req, res, next) => {
  try {
    const frameworks = getAllFrameworks();
    res.status(200).json(frameworks);
  } catch (error) {
    next(error);
  }
};

/**
 * Get a specific framework by ID
 */
const getFrameworkHandler = async (req, res, next) => {
  try {
    const { id } = req.params;
    const framework = getFramework(id);
    res.status(200).json(framework);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: true, message: error.message });
    }
    next(error);
  }
};

/**
 * Map a control from one framework to another
 */
const mapControlHandler = async (req, res, next) => {
  try {
    const { sourceFramework, sourceControl, targetFramework } = req.params;
    const mapping = mapControl(sourceFramework, sourceControl, targetFramework);
    res.status(200).json(mapping);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: true, message: error.message });
    }
    next(error);
  }
};

/**
 * Map multiple controls from one framework to another
 */
const mapControlsHandler = async (req, res, next) => {
  try {
    const { sourceFramework, targetFramework } = req.params;
    const { sourceControls } = req.body;
    
    if (!Array.isArray(sourceControls) || sourceControls.length === 0) {
      return res.status(400).json({ error: true, message: 'sourceControls must be a non-empty array' });
    }
    
    const mappings = mapControls(sourceFramework, sourceControls, targetFramework);
    res.status(200).json(mappings);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: true, message: error.message });
    }
    next(error);
  }
};

/**
 * Get all possible mappings for a control
 */
const getAllMappingsForControlHandler = async (req, res, next) => {
  try {
    const { sourceFramework, sourceControl } = req.params;
    const mappings = getAllMappingsForControl(sourceFramework, sourceControl);
    res.status(200).json(mappings);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: true, message: error.message });
    }
    next(error);
  }
};

/**
 * Get all mappings between two frameworks
 */
const getFrameworkToFrameworkMappingsHandler = async (req, res, next) => {
  try {
    const { sourceFramework, targetFramework } = req.params;
    const mappings = getFrameworkToFrameworkMappings(sourceFramework, targetFramework);
    res.status(200).json(mappings);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: true, message: error.message });
    }
    next(error);
  }
};

/**
 * Calculate compliance coverage across frameworks
 */
const calculateComplianceCoverageHandler = async (req, res, next) => {
  try {
    const { sourceFramework, targetFramework } = req.params;
    const { implementedControls } = req.body;
    
    if (!Array.isArray(implementedControls)) {
      return res.status(400).json({ error: true, message: 'implementedControls must be an array' });
    }
    
    const coverage = calculateComplianceCoverage(sourceFramework, implementedControls, targetFramework);
    res.status(200).json(coverage);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: true, message: error.message });
    }
    next(error);
  }
};

module.exports = {
  getAllFrameworksHandler,
  getFrameworkHandler,
  mapControlHandler,
  mapControlsHandler,
  getAllMappingsForControlHandler,
  getFrameworkToFrameworkMappingsHandler,
  calculateComplianceCoverageHandler
};
