html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(37, 99, 235, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(37, 99, 235, 0.7);
}

/* React Flow customizations */
.react-flow__node {
  border-radius: 4px;
  border: 1px solid #2563eb;
  padding: 10px;
  width: 200px;
  font-size: 12px;
  text-align: center;
  background-color: #1e293b;
}

.react-flow__node-input {
  background-color: #1e293b;
  border-color: #2563eb;
  color: white;
}

.react-flow__node-output {
  background-color: #1e293b;
  border-color: #2563eb;
  color: white;
}

.react-flow__node-default {
  background-color: #1e293b;
  border-color: #2563eb;
  color: white;
}

.react-flow__handle {
  background-color: #2563eb;
}

.react-flow__edge-path {
  stroke: #2563eb;
  stroke-width: 2;
}

.react-flow__edge-text {
  fill: white;
}

.react-flow__edge-textbg {
  fill: #1e293b;
}
