# Code of Merit

1. The project creators, lead developers, core team, constitute
the managing members of the project and have final say in every decision
of the project, technical or otherwise, including overruling previous decisions.
There are no limitations to this decisional power.

2. Contributions are an expected result of your membership on the project.
Don't expect others to do your work or help you with your work forever.

3. All members have the same opportunities to seek any challenge they want
within the project.

4. Authority or position in the project will be proportional
to the accrued contribution. Seniority must be earned.

5. Software is evolutive: the better implementations must supersede lesser
implementations. Technical advantage is the primary evaluation metric.

6. This is a space for technical prowess; topics outside of the project
will not be tolerated.

7. Non technical conflicts will be discussed in a separate space. Disruption
of the project will not be allowed.

8. Individual characteristics, including but not limited to,
body, sex, sexual preference, race, language, religion, nationality,
or political preferences are irrelevant in the scope of the project and
will not be taken into account concerning your value or that of your contribution
to the project.

9. Discuss or debate the idea, not the person.

10. There is no room for ambiguity: Ambiguity will be met with questioning;
further ambiguity will be met with silence. It is the responsibility
of the originator to provide requested context.

11. If something is illegal outside the scope of the project, it is illegal
in the scope of the project. This Code of Merit does not take precedence over
governing law.

12. This Code of Merit governs the technical procedures of the project not the
activities outside of it.

13. Participation on the project equates to agreement of this Code of Merit.

14. No objectives beyond the stated objectives of this project are relevant
to the project. Any intent to deviate the project from its original purpose
of existence will constitute grounds for remedial action which may include
expulsion from the project.

This document is the Code of Merit (https://codeofmerit.org/), version 1.0.
