/**
 * Attack Surface Coherence (Ψₐ)
 * 
 * This module implements the Attack Surface Coherence component of the CSFE.
 * It quantifies system vulnerability decay rates and security posture.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * AttackSurfaceCoherence class
 */
class AttackSurfaceCoherence extends EventEmitter {
  /**
   * Create a new AttackSurfaceCoherence instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 10000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      decayRate: 0.05, // Natural decay rate of coherence (5% per interval)
      thresholds: {
        coherence: {
          critical: 0.3, // Critical threshold (low coherence is bad)
          high: 0.5,
          medium: 0.7,
          low: 0.9
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      coherenceScore: 0.8, // Start with good coherence
      vulnerabilities: new Map(), // id -> vulnerability object
      exposures: new Map(), // id -> exposure object
      mitigations: new Map(), // id -> mitigation object
      coherenceHistory: [],
      coherenceStatus: 'medium', // critical, high, medium, low
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      vulnerabilitiesAdded: 0,
      vulnerabilitiesResolved: 0,
      exposuresDetected: 0,
      mitigationsApplied: 0
    };
    
    if (this.options.enableLogging) {
      console.log('AttackSurfaceCoherence initialized');
    }
  }
  
  /**
   * Start the attack surface coherence monitor
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('AttackSurfaceCoherence is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('AttackSurfaceCoherence started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the attack surface coherence monitor
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('AttackSurfaceCoherence is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('AttackSurfaceCoherence stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Add vulnerability
   * @param {Object} vulnerability - Vulnerability object
   * @returns {Object} - Added vulnerability
   */
  addVulnerability(vulnerability) {
    const startTime = performance.now();
    
    if (!vulnerability || typeof vulnerability !== 'object') {
      throw new Error('Vulnerability must be an object');
    }
    
    if (!vulnerability.id) {
      vulnerability.id = `vuln-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    vulnerability = {
      severity: 'medium', // critical, high, medium, low
      impact: 0.5, // 0-1
      exploitability: 0.5, // 0-1
      status: 'open', // open, mitigated, resolved
      discoveredAt: Date.now(),
      ...vulnerability
    };
    
    // Add to state
    this.state.vulnerabilities.set(vulnerability.id, vulnerability);
    
    // Update coherence score
    this._updateCoherenceScore();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.vulnerabilitiesAdded++;
    
    // Emit event
    this.emit('vulnerability-added', vulnerability);
    
    if (this.options.enableLogging) {
      console.log(`AttackSurfaceCoherence: Added ${vulnerability.severity} vulnerability ${vulnerability.id}`);
    }
    
    return vulnerability;
  }
  
  /**
   * Update vulnerability
   * @param {string} id - Vulnerability ID
   * @param {Object} updates - Updates to apply
   * @returns {Object} - Updated vulnerability
   */
  updateVulnerability(id, updates) {
    const startTime = performance.now();
    
    if (!id || !this.state.vulnerabilities.has(id)) {
      throw new Error(`Vulnerability ${id} not found`);
    }
    
    if (!updates || typeof updates !== 'object') {
      throw new Error('Updates must be an object');
    }
    
    // Get current vulnerability
    const vulnerability = this.state.vulnerabilities.get(id);
    
    // Apply updates
    const updatedVulnerability = {
      ...vulnerability,
      ...updates,
      updatedAt: Date.now()
    };
    
    // Check if resolved
    if (vulnerability.status !== 'resolved' && updatedVulnerability.status === 'resolved') {
      updatedVulnerability.resolvedAt = Date.now();
      this.metrics.vulnerabilitiesResolved++;
    }
    
    // Update state
    this.state.vulnerabilities.set(id, updatedVulnerability);
    
    // Update coherence score
    this._updateCoherenceScore();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit event
    this.emit('vulnerability-updated', updatedVulnerability);
    
    if (this.options.enableLogging) {
      console.log(`AttackSurfaceCoherence: Updated vulnerability ${id}`);
    }
    
    return updatedVulnerability;
  }
  
  /**
   * Add exposure
   * @param {Object} exposure - Exposure object
   * @returns {Object} - Added exposure
   */
  addExposure(exposure) {
    const startTime = performance.now();
    
    if (!exposure || typeof exposure !== 'object') {
      throw new Error('Exposure must be an object');
    }
    
    if (!exposure.id) {
      exposure.id = `exp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    exposure = {
      severity: 'medium', // critical, high, medium, low
      impact: 0.5, // 0-1
      visibility: 0.5, // 0-1 (how visible to attackers)
      status: 'active', // active, mitigated, resolved
      discoveredAt: Date.now(),
      ...exposure
    };
    
    // Add to state
    this.state.exposures.set(exposure.id, exposure);
    
    // Update coherence score
    this._updateCoherenceScore();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.exposuresDetected++;
    
    // Emit event
    this.emit('exposure-added', exposure);
    
    if (this.options.enableLogging) {
      console.log(`AttackSurfaceCoherence: Added ${exposure.severity} exposure ${exposure.id}`);
    }
    
    return exposure;
  }
  
  /**
   * Add mitigation
   * @param {Object} mitigation - Mitigation object
   * @returns {Object} - Added mitigation
   */
  addMitigation(mitigation) {
    const startTime = performance.now();
    
    if (!mitigation || typeof mitigation !== 'object') {
      throw new Error('Mitigation must be an object');
    }
    
    if (!mitigation.id) {
      mitigation.id = `mit-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    mitigation = {
      effectiveness: 0.7, // 0-1
      coverage: 0.5, // 0-1 (how much of the attack surface it covers)
      status: 'active', // active, degraded, inactive
      implementedAt: Date.now(),
      ...mitigation
    };
    
    // Add to state
    this.state.mitigations.set(mitigation.id, mitigation);
    
    // Update related vulnerabilities and exposures
    if (mitigation.vulnerabilityIds) {
      for (const vulnId of mitigation.vulnerabilityIds) {
        if (this.state.vulnerabilities.has(vulnId)) {
          this.updateVulnerability(vulnId, { status: 'mitigated', mitigationId: mitigation.id });
        }
      }
    }
    
    if (mitigation.exposureIds) {
      for (const expId of mitigation.exposureIds) {
        if (this.state.exposures.has(expId)) {
          const exposure = this.state.exposures.get(expId);
          this.state.exposures.set(expId, {
            ...exposure,
            status: 'mitigated',
            mitigationId: mitigation.id,
            updatedAt: Date.now()
          });
        }
      }
    }
    
    // Update coherence score
    this._updateCoherenceScore();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.mitigationsApplied++;
    
    // Emit event
    this.emit('mitigation-added', mitigation);
    
    if (this.options.enableLogging) {
      console.log(`AttackSurfaceCoherence: Added mitigation ${mitigation.id}`);
    }
    
    return mitigation;
  }
  
  /**
   * Get coherence score
   * @returns {number} - Coherence score
   */
  getCoherenceScore() {
    return this.state.coherenceScore;
  }
  
  /**
   * Get coherence status
   * @returns {string} - Coherence status
   */
  getCoherenceStatus() {
    return this.state.coherenceStatus;
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      coherenceScore: this.state.coherenceScore,
      coherenceStatus: this.state.coherenceStatus,
      vulnerabilityCount: this.state.vulnerabilities.size,
      exposureCount: this.state.exposures.size,
      mitigationCount: this.state.mitigations.size,
      coherenceHistory: [...this.state.coherenceHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get vulnerabilities
   * @param {string} status - Optional status filter
   * @returns {Array} - Vulnerabilities
   */
  getVulnerabilities(status) {
    const vulnerabilities = Array.from(this.state.vulnerabilities.values());
    
    if (status) {
      return vulnerabilities.filter(v => v.status === status);
    }
    
    return vulnerabilities;
  }
  
  /**
   * Get exposures
   * @param {string} status - Optional status filter
   * @returns {Array} - Exposures
   */
  getExposures(status) {
    const exposures = Array.from(this.state.exposures.values());
    
    if (status) {
      return exposures.filter(e => e.status === status);
    }
    
    return exposures;
  }
  
  /**
   * Get mitigations
   * @param {string} status - Optional status filter
   * @returns {Array} - Mitigations
   */
  getMitigations(status) {
    const mitigations = Array.from(this.state.mitigations.values());
    
    if (status) {
      return mitigations.filter(m => m.status === status);
    }
    
    return mitigations;
  }
  
  /**
   * Update coherence score
   * @private
   */
  _updateCoherenceScore() {
    const startTime = performance.now();
    
    // Calculate vulnerability impact
    let vulnerabilityImpact = 0;
    for (const vuln of this.state.vulnerabilities.values()) {
      if (vuln.status === 'open') {
        // Open vulnerabilities reduce coherence
        const severityWeight = this._getSeverityWeight(vuln.severity);
        vulnerabilityImpact += severityWeight * vuln.impact * vuln.exploitability;
      } else if (vuln.status === 'mitigated') {
        // Mitigated vulnerabilities have reduced impact
        const severityWeight = this._getSeverityWeight(vuln.severity);
        const mitigation = vuln.mitigationId ? this.state.mitigations.get(vuln.mitigationId) : null;
        const mitigationEffectiveness = mitigation ? mitigation.effectiveness : 0.5;
        
        vulnerabilityImpact += severityWeight * vuln.impact * vuln.exploitability * (1 - mitigationEffectiveness);
      }
      // Resolved vulnerabilities don't impact coherence
    }
    
    // Normalize vulnerability impact (0-1)
    vulnerabilityImpact = Math.min(1, vulnerabilityImpact);
    
    // Calculate exposure impact
    let exposureImpact = 0;
    for (const exp of this.state.exposures.values()) {
      if (exp.status === 'active') {
        // Active exposures reduce coherence
        const severityWeight = this._getSeverityWeight(exp.severity);
        exposureImpact += severityWeight * exp.impact * exp.visibility;
      } else if (exp.status === 'mitigated') {
        // Mitigated exposures have reduced impact
        const severityWeight = this._getSeverityWeight(exp.severity);
        const mitigation = exp.mitigationId ? this.state.mitigations.get(exp.mitigationId) : null;
        const mitigationEffectiveness = mitigation ? mitigation.effectiveness : 0.5;
        
        exposureImpact += severityWeight * exp.impact * exp.visibility * (1 - mitigationEffectiveness);
      }
      // Resolved exposures don't impact coherence
    }
    
    // Normalize exposure impact (0-1)
    exposureImpact = Math.min(1, exposureImpact);
    
    // Calculate mitigation benefit
    let mitigationBenefit = 0;
    for (const mit of this.state.mitigations.values()) {
      if (mit.status === 'active') {
        // Active mitigations improve coherence
        mitigationBenefit += mit.effectiveness * mit.coverage;
      } else if (mit.status === 'degraded') {
        // Degraded mitigations have reduced benefit
        mitigationBenefit += mit.effectiveness * mit.coverage * 0.5;
      }
      // Inactive mitigations don't benefit coherence
    }
    
    // Normalize mitigation benefit (0-1)
    mitigationBenefit = Math.min(1, mitigationBenefit);
    
    // Calculate coherence score using 18/82 principle
    // 18% weight to mitigation benefit, 82% weight to the inverse of vulnerability and exposure impact
    const coherenceScore = (
      0.18 * mitigationBenefit +
      0.82 * (1 - ((vulnerabilityImpact + exposureImpact) / 2))
    );
    
    // Apply natural decay
    const timeSinceLastUpdate = (Date.now() - this.state.lastUpdateTime) / this.options.updateInterval;
    const decayFactor = Math.pow(1 - this.options.decayRate, timeSinceLastUpdate);
    
    // Update state
    this.state.coherenceScore = this._clamp(coherenceScore * decayFactor);
    this.state.lastUpdateTime = Date.now();
    
    // Update coherence status
    this._updateCoherenceStatus();
    
    // Add to history
    this.state.coherenceHistory.push({
      coherenceScore: this.state.coherenceScore,
      coherenceStatus: this.state.coherenceStatus,
      vulnerabilityImpact,
      exposureImpact,
      mitigationBenefit,
      timestamp: Date.now()
    });
    
    // Limit history size
    if (this.state.coherenceHistory.length > this.options.historySize) {
      this.state.coherenceHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit update event
    this.emit('coherence-update', {
      coherenceScore: this.state.coherenceScore,
      coherenceStatus: this.state.coherenceStatus,
      timestamp: Date.now()
    });
  }
  
  /**
   * Update coherence status
   * @private
   */
  _updateCoherenceStatus() {
    const { coherenceScore } = this.state;
    const { thresholds } = this.options;
    
    let newStatus = 'low';
    
    if (coherenceScore <= thresholds.coherence.critical) {
      newStatus = 'critical';
    } else if (coherenceScore <= thresholds.coherence.high) {
      newStatus = 'high';
    } else if (coherenceScore <= thresholds.coherence.medium) {
      newStatus = 'medium';
    }
    
    // If status changed, emit event
    if (newStatus !== this.state.coherenceStatus) {
      this.state.coherenceStatus = newStatus;
      
      // Emit status change event
      this.emit('status-change', {
        coherenceStatus: this.state.coherenceStatus,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`AttackSurfaceCoherence: Coherence status changed to ${this.state.coherenceStatus}`);
      }
    }
  }
  
  /**
   * Get severity weight
   * @param {string} severity - Severity level
   * @returns {number} - Severity weight
   * @private
   */
  _getSeverityWeight(severity) {
    switch (severity) {
      case 'critical': return 1.0;
      case 'high': return 0.8;
      case 'medium': return 0.5;
      case 'low': return 0.2;
      default: return 0.5;
    }
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // Update coherence score to apply natural decay
        this._updateCoherenceScore();
        
        // In a real implementation, this would fetch real-time data
        // For now, just simulate some changes
        this._simulateChanges();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate changes
   * @private
   */
  _simulateChanges() {
    // Simulate random changes to attack surface
    const rand = Math.random();
    
    if (rand < 0.3) {
      // Add a new vulnerability
      this.addVulnerability({
        severity: this._randomSeverity(),
        impact: Math.random(),
        exploitability: Math.random()
      });
    } else if (rand < 0.5) {
      // Add a new exposure
      this.addExposure({
        severity: this._randomSeverity(),
        impact: Math.random(),
        visibility: Math.random()
      });
    } else if (rand < 0.7) {
      // Add a new mitigation
      this.addMitigation({
        effectiveness: Math.random(),
        coverage: Math.random()
      });
    } else if (rand < 0.9) {
      // Resolve a random vulnerability
      const vulnerabilities = this.getVulnerabilities('open');
      if (vulnerabilities.length > 0) {
        const vuln = vulnerabilities[Math.floor(Math.random() * vulnerabilities.length)];
        this.updateVulnerability(vuln.id, { status: 'resolved' });
      }
    }
  }
  
  /**
   * Generate random severity
   * @returns {string} - Random severity
   * @private
   */
  _randomSeverity() {
    const rand = Math.random();
    if (rand < 0.1) return 'critical';
    if (rand < 0.3) return 'high';
    if (rand < 0.7) return 'medium';
    return 'low';
  }
  
  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = AttackSurfaceCoherence;
