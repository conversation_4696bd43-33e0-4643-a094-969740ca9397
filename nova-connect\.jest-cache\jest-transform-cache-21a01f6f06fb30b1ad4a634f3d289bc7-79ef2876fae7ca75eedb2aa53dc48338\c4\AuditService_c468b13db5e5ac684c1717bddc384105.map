{"version": 3, "names": ["fs", "require", "promises", "path", "v4", "uuidv4", "<PERSON><PERSON><PERSON><PERSON>", "error", "console", "log", "AuditService", "constructor", "dataDir", "join", "__dirname", "auditDir", "auditFile", "retentionDays", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "process", "env", "BIGQUERY_ENABLED", "projectId", "GCP_PROJECT_ID", "datasetId", "BIGQUERY_DATASET_ID", "tableId", "BIGQUERY_TABLE_ID", "big<PERSON>y", "ensureDataDir", "setInterval", "cleanupOldLogs", "mkdir", "recursive", "access", "code", "writeFile", "JSON", "stringify", "loadAuditLogs", "data", "readFile", "parse", "saveAuditLogs", "logs", "logEvent", "logEntry", "id", "timestamp", "Date", "toISOString", "userId", "action", "resourceType", "resourceId", "details", "ip", "userAgent", "status", "teamId", "environmentId", "tenantId", "logToBigQuery", "big<PERSON><PERSON>y<PERSON><PERSON>r", "auditLogs", "push", "length", "splice", "logTenantEvent", "tenantData", "dataset", "table", "get", "tableError", "schema", "name", "type", "options", "timePartitioning", "field", "createTable", "insert", "event", "getAuditLogs", "filters", "filteredLogs", "startDate", "filter", "endDate", "sort", "a", "b", "page", "limit", "startIndex", "endIndex", "paginatedLogs", "slice", "total", "totalPages", "Math", "ceil", "getAuditLogById", "find", "Error", "getAuditLogsForResource", "resourceLogs", "getAuditLogsForUser", "userLogs", "getAuditLogsForTeam", "teamLogs", "cutoffDate", "setDate", "getDate", "createAuditMiddleware", "req", "res", "next", "originalEnd", "end", "chunk", "encoding", "startsWith", "auditData", "user", "method", "split", "params", "query", "body", "connection", "remoteAddress", "headers", "statusCode", "module", "exports"], "sources": ["AuditService.js"], "sourcesContent": ["/**\n * Audit Service\n *\n * This service handles audit logging for tracking user actions.\n * It supports both local file storage and Google BigQuery integration.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst { v4: uuidv4 } = require('uuid');\n\n// Import BigQuery if available\nlet BigQuery;\ntry {\n  BigQuery = require('@google-cloud/bigquery').BigQuery;\n} catch (error) {\n  // BigQuery package not available, will use local storage only\n  console.log('BigQuery package not available, using local storage only');\n}\n\nclass AuditService {\n  constructor(dataDir = path.join(__dirname, '../data')) {\n    this.dataDir = dataDir;\n    this.auditDir = path.join(this.dataDir, 'audit');\n    this.auditFile = path.join(this.auditDir, 'audit_log.json');\n    this.retentionDays = 90; // Keep audit logs for 90 days\n\n    // Initialize BigQuery if available\n    this.bigQueryEnabled = process.env.BIGQUERY_ENABLED === 'true';\n    this.projectId = process.env.GCP_PROJECT_ID;\n    this.datasetId = process.env.BIGQUERY_DATASET_ID || 'novafuse_audit';\n    this.tableId = process.env.BIGQUERY_TABLE_ID || 'events';\n\n    if (this.bigQueryEnabled && this.projectId && BigQuery) {\n      try {\n        this.bigquery = new BigQuery({\n          projectId: this.projectId\n        });\n        console.log('BigQuery integration enabled');\n      } catch (error) {\n        console.error('Error initializing BigQuery:', error);\n      }\n    }\n\n    this.ensureDataDir();\n\n    // Clean up old audit logs once a day\n    setInterval(() => this.cleanupOldLogs(), 24 * 60 * 60 * 1000);\n  }\n\n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.auditDir, { recursive: true });\n\n      // Initialize audit log file if it doesn't exist\n      try {\n        await fs.access(this.auditFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          // File doesn't exist, create it with empty array\n          await fs.writeFile(this.auditFile, JSON.stringify([]));\n        } else {\n          throw error;\n        }\n      }\n    } catch (error) {\n      console.error('Error creating audit directory:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Load audit logs from file\n   */\n  async loadAuditLogs() {\n    try {\n      const data = await fs.readFile(this.auditFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      if (error.code === 'ENOENT') {\n        // File doesn't exist, return empty array\n        return [];\n      }\n      console.error('Error loading audit logs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Save audit logs to file\n   */\n  async saveAuditLogs(logs) {\n    try {\n      await fs.writeFile(this.auditFile, JSON.stringify(logs, null, 2));\n    } catch (error) {\n      console.error('Error saving audit logs:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Log an audit event\n   */\n  async logEvent(data) {\n    try {\n      // Create audit log entry\n      const logEntry = {\n        id: uuidv4(),\n        timestamp: new Date().toISOString(),\n        userId: data.userId || null,\n        action: data.action,\n        resourceType: data.resourceType,\n        resourceId: data.resourceId,\n        details: data.details || null,\n        ip: data.ip || null,\n        userAgent: data.userAgent || null,\n        status: data.status || 'success',\n        teamId: data.teamId || null,\n        environmentId: data.environmentId || null,\n        tenantId: data.tenantId || null\n      };\n\n      // Log to BigQuery if enabled\n      if (this.bigQueryEnabled && this.bigquery) {\n        try {\n          await this.logToBigQuery(logEntry);\n        } catch (bigQueryError) {\n          console.error('Error logging to BigQuery:', bigQueryError);\n          // Continue with local logging even if BigQuery fails\n        }\n      }\n\n      // Log to local file\n      const auditLogs = await this.loadAuditLogs();\n      auditLogs.push(logEntry);\n\n      // Limit the size of the audit logs\n      if (auditLogs.length > 10000) {\n        auditLogs.splice(0, auditLogs.length - 10000);\n      }\n\n      await this.saveAuditLogs(auditLogs);\n\n      return logEntry;\n    } catch (error) {\n      console.error('Error logging audit event:', error);\n      // Don't throw error to prevent affecting the main request flow\n    }\n  }\n\n  /**\n   * Log a tenant-specific audit event\n   */\n  async logTenantEvent(tenantId, data) {\n    try {\n      // Add tenant ID to data\n      const tenantData = {\n        ...data,\n        tenantId\n      };\n\n      // Log the event\n      const logEntry = await this.logEvent(tenantData);\n\n      // If BigQuery is enabled, also log to tenant-specific table\n      if (this.bigQueryEnabled && this.bigquery && tenantId) {\n        try {\n          // Get tenant-specific dataset\n          const dataset = this.bigquery.dataset(`tenant_${tenantId}`);\n\n          // Get audit table (create if it doesn't exist)\n          let table;\n          try {\n            table = dataset.table('audit_logs');\n            await table.get();\n          } catch (tableError) {\n            // Table doesn't exist, create it\n            const schema = [\n              { name: 'id', type: 'STRING' },\n              { name: 'timestamp', type: 'TIMESTAMP' },\n              { name: 'userId', type: 'STRING' },\n              { name: 'action', type: 'STRING' },\n              { name: 'resourceType', type: 'STRING' },\n              { name: 'resourceId', type: 'STRING' },\n              { name: 'details', type: 'STRING' },\n              { name: 'ip', type: 'STRING' },\n              { name: 'userAgent', type: 'STRING' },\n              { name: 'status', type: 'STRING' },\n              { name: 'teamId', type: 'STRING' },\n              { name: 'environmentId', type: 'STRING' },\n              { name: 'tenantId', type: 'STRING' }\n            ];\n\n            const options = {\n              schema: schema,\n              timePartitioning: {\n                type: 'DAY',\n                field: 'timestamp'\n              }\n            };\n\n            await dataset.createTable('audit_logs', options);\n            table = dataset.table('audit_logs');\n          }\n\n          // Insert into tenant-specific table\n          await table.insert([logEntry]);\n        } catch (bigQueryError) {\n          console.error(`Error logging to tenant-specific BigQuery table for tenant ${tenantId}:`, bigQueryError);\n        }\n      }\n\n      return logEntry;\n    } catch (error) {\n      console.error('Error logging tenant audit event:', error);\n      // Don't throw error to prevent affecting the main request flow\n    }\n  }\n\n  /**\n   * Log an audit event to BigQuery\n   */\n  async logToBigQuery(event) {\n    try {\n      // Get dataset reference\n      const dataset = this.bigquery.dataset(this.datasetId);\n\n      // Get table reference\n      const table = dataset.table(this.tableId);\n\n      // Insert row\n      await table.insert([event]);\n    } catch (error) {\n      console.error('Error logging to BigQuery:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get audit logs\n   */\n  async getAuditLogs(filters = {}) {\n    const auditLogs = await this.loadAuditLogs();\n\n    // Apply filters\n    let filteredLogs = auditLogs;\n\n    if (filters.startDate) {\n      const startDate = new Date(filters.startDate);\n      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);\n    }\n\n    if (filters.endDate) {\n      const endDate = new Date(filters.endDate);\n      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);\n    }\n\n    if (filters.userId) {\n      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);\n    }\n\n    if (filters.action) {\n      filteredLogs = filteredLogs.filter(log => log.action === filters.action);\n    }\n\n    if (filters.resourceType) {\n      filteredLogs = filteredLogs.filter(log => log.resourceType === filters.resourceType);\n    }\n\n    if (filters.resourceId) {\n      filteredLogs = filteredLogs.filter(log => log.resourceId === filters.resourceId);\n    }\n\n    if (filters.status) {\n      filteredLogs = filteredLogs.filter(log => log.status === filters.status);\n    }\n\n    if (filters.teamId) {\n      filteredLogs = filteredLogs.filter(log => log.teamId === filters.teamId);\n    }\n\n    if (filters.environmentId) {\n      filteredLogs = filteredLogs.filter(log => log.environmentId === filters.environmentId);\n    }\n\n    // Sort by timestamp (newest first)\n    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n    // Apply pagination\n    const page = filters.page || 1;\n    const limit = filters.limit || 100;\n    const startIndex = (page - 1) * limit;\n    const endIndex = page * limit;\n\n    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);\n\n    return {\n      logs: paginatedLogs,\n      total: filteredLogs.length,\n      page,\n      limit,\n      totalPages: Math.ceil(filteredLogs.length / limit)\n    };\n  }\n\n  /**\n   * Get audit log by ID\n   */\n  async getAuditLogById(id) {\n    const auditLogs = await this.loadAuditLogs();\n    const log = auditLogs.find(log => log.id === id);\n\n    if (!log) {\n      throw new Error(`Audit log with ID ${id} not found`);\n    }\n\n    return log;\n  }\n\n  /**\n   * Get audit logs for a resource\n   */\n  async getAuditLogsForResource(resourceType, resourceId) {\n    const auditLogs = await this.loadAuditLogs();\n\n    // Filter logs for the resource\n    const resourceLogs = auditLogs.filter(log =>\n      log.resourceType === resourceType && log.resourceId === resourceId\n    );\n\n    // Sort by timestamp (newest first)\n    resourceLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n    return resourceLogs;\n  }\n\n  /**\n   * Get audit logs for a user\n   */\n  async getAuditLogsForUser(userId) {\n    const auditLogs = await this.loadAuditLogs();\n\n    // Filter logs for the user\n    const userLogs = auditLogs.filter(log => log.userId === userId);\n\n    // Sort by timestamp (newest first)\n    userLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n    return userLogs;\n  }\n\n  /**\n   * Get audit logs for a team\n   */\n  async getAuditLogsForTeam(teamId) {\n    const auditLogs = await this.loadAuditLogs();\n\n    // Filter logs for the team\n    const teamLogs = auditLogs.filter(log => log.teamId === teamId);\n\n    // Sort by timestamp (newest first)\n    teamLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));\n\n    return teamLogs;\n  }\n\n  /**\n   * Clean up old audit logs\n   */\n  async cleanupOldLogs() {\n    try {\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);\n\n      const auditLogs = await this.loadAuditLogs();\n      const filteredLogs = auditLogs.filter(log =>\n        new Date(log.timestamp) >= cutoffDate\n      );\n\n      if (filteredLogs.length < auditLogs.length) {\n        await this.saveAuditLogs(filteredLogs);\n        console.log(`Cleaned up audit logs older than ${cutoffDate.toISOString()}`);\n      }\n    } catch (error) {\n      console.error('Error cleaning up old audit logs:', error);\n    }\n  }\n\n  /**\n   * Create audit middleware\n   */\n  createAuditMiddleware() {\n    return (req, res, next) => {\n      // Store original end method\n      const originalEnd = res.end;\n\n      // Override end method to capture response\n      res.end = function(chunk, encoding) {\n        // Restore original end method\n        res.end = originalEnd;\n\n        // Call original end method\n        res.end(chunk, encoding);\n\n        // Skip audit logging for certain paths\n        if (req.path.startsWith('/health') || req.path.startsWith('/api/monitoring/health')) {\n          return;\n        }\n\n        // Log audit event\n        const auditData = {\n          userId: req.user ? req.user.id : null,\n          action: req.method,\n          resourceType: req.path.split('/')[2] || 'unknown',\n          resourceId: req.params.id || null,\n          details: {\n            path: req.path,\n            query: req.query,\n            body: req.method !== 'GET' ? req.body : null\n          },\n          ip: req.ip || req.connection.remoteAddress,\n          userAgent: req.headers['user-agent'],\n          status: res.statusCode >= 400 ? 'failure' : 'success',\n          teamId: req.headers['x-team-id'] || null,\n          environmentId: req.headers['x-environment-id'] || null,\n          tenantId: req.headers['x-tenant-id'] || null\n        };\n\n        // If tenant ID is present, use tenant-specific logging\n        if (req.headers['x-tenant-id']) {\n          this.logTenantEvent(req.headers['x-tenant-id'], auditData);\n        } else {\n          this.logEvent(auditData);\n        }\n      };\n\n      next();\n    };\n  }\n}\n\nmodule.exports = AuditService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAM;EAAEG,EAAE,EAAEC;AAAO,CAAC,GAAGJ,OAAO,CAAC,MAAM,CAAC;;AAEtC;AACA,IAAIK,QAAQ;AACZ,IAAI;EACFA,QAAQ,GAAGL,OAAO,CAAC,wBAAwB,CAAC,CAACK,QAAQ;AACvD,CAAC,CAAC,OAAOC,KAAK,EAAE;EACd;EACAC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;AACzE;AAEA,MAAMC,YAAY,CAAC;EACjBC,WAAWA,CAACC,OAAO,GAAGT,IAAI,CAACU,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAE;IACrD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,QAAQ,GAAGZ,IAAI,CAACU,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,OAAO,CAAC;IAChD,IAAI,CAACI,SAAS,GAAGb,IAAI,CAACU,IAAI,CAAC,IAAI,CAACE,QAAQ,EAAE,gBAAgB,CAAC;IAC3D,IAAI,CAACE,aAAa,GAAG,EAAE,CAAC,CAAC;;IAEzB;IACA,IAAI,CAACC,eAAe,GAAGC,OAAO,CAACC,GAAG,CAACC,gBAAgB,KAAK,MAAM;IAC9D,IAAI,CAACC,SAAS,GAAGH,OAAO,CAACC,GAAG,CAACG,cAAc;IAC3C,IAAI,CAACC,SAAS,GAAGL,OAAO,CAACC,GAAG,CAACK,mBAAmB,IAAI,gBAAgB;IACpE,IAAI,CAACC,OAAO,GAAGP,OAAO,CAACC,GAAG,CAACO,iBAAiB,IAAI,QAAQ;IAExD,IAAI,IAAI,CAACT,eAAe,IAAI,IAAI,CAACI,SAAS,IAAIhB,QAAQ,EAAE;MACtD,IAAI;QACF,IAAI,CAACsB,QAAQ,GAAG,IAAItB,QAAQ,CAAC;UAC3BgB,SAAS,EAAE,IAAI,CAACA;QAClB,CAAC,CAAC;QACFd,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC7C,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF;IAEA,IAAI,CAACsB,aAAa,CAAC,CAAC;;IAEpB;IACAC,WAAW,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC/D;;EAEA;AACF;AACA;EACE,MAAMF,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAM7B,EAAE,CAACgC,KAAK,CAAC,IAAI,CAACjB,QAAQ,EAAE;QAAEkB,SAAS,EAAE;MAAK,CAAC,CAAC;;MAElD;MACA,IAAI;QACF,MAAMjC,EAAE,CAACkC,MAAM,CAAC,IAAI,CAAClB,SAAS,CAAC;MACjC,CAAC,CAAC,OAAOT,KAAK,EAAE;QACd,IAAIA,KAAK,CAAC4B,IAAI,KAAK,QAAQ,EAAE;UAC3B;UACA,MAAMnC,EAAE,CAACoC,SAAS,CAAC,IAAI,CAACpB,SAAS,EAAEqB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;QACxD,CAAC,MAAM;UACL,MAAM/B,KAAK;QACb;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgC,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMxC,EAAE,CAACyC,QAAQ,CAAC,IAAI,CAACzB,SAAS,EAAE,MAAM,CAAC;MACtD,OAAOqB,IAAI,CAACK,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,IAAIA,KAAK,CAAC4B,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACA,OAAO,EAAE;MACX;MACA3B,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoC,aAAaA,CAACC,IAAI,EAAE;IACxB,IAAI;MACF,MAAM5C,EAAE,CAACoC,SAAS,CAAC,IAAI,CAACpB,SAAS,EAAEqB,IAAI,CAACC,SAAS,CAACM,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsC,QAAQA,CAACL,IAAI,EAAE;IACnB,IAAI;MACF;MACA,MAAMM,QAAQ,GAAG;QACfC,EAAE,EAAE1C,MAAM,CAAC,CAAC;QACZ2C,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,MAAM,EAAEX,IAAI,CAACW,MAAM,IAAI,IAAI;QAC3BC,MAAM,EAAEZ,IAAI,CAACY,MAAM;QACnBC,YAAY,EAAEb,IAAI,CAACa,YAAY;QAC/BC,UAAU,EAAEd,IAAI,CAACc,UAAU;QAC3BC,OAAO,EAAEf,IAAI,CAACe,OAAO,IAAI,IAAI;QAC7BC,EAAE,EAAEhB,IAAI,CAACgB,EAAE,IAAI,IAAI;QACnBC,SAAS,EAAEjB,IAAI,CAACiB,SAAS,IAAI,IAAI;QACjCC,MAAM,EAAElB,IAAI,CAACkB,MAAM,IAAI,SAAS;QAChCC,MAAM,EAAEnB,IAAI,CAACmB,MAAM,IAAI,IAAI;QAC3BC,aAAa,EAAEpB,IAAI,CAACoB,aAAa,IAAI,IAAI;QACzCC,QAAQ,EAAErB,IAAI,CAACqB,QAAQ,IAAI;MAC7B,CAAC;;MAED;MACA,IAAI,IAAI,CAAC3C,eAAe,IAAI,IAAI,CAACU,QAAQ,EAAE;QACzC,IAAI;UACF,MAAM,IAAI,CAACkC,aAAa,CAAChB,QAAQ,CAAC;QACpC,CAAC,CAAC,OAAOiB,aAAa,EAAE;UACtBvD,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEwD,aAAa,CAAC;UAC1D;QACF;MACF;;MAEA;MACA,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;MAC5CyB,SAAS,CAACC,IAAI,CAACnB,QAAQ,CAAC;;MAExB;MACA,IAAIkB,SAAS,CAACE,MAAM,GAAG,KAAK,EAAE;QAC5BF,SAAS,CAACG,MAAM,CAAC,CAAC,EAAEH,SAAS,CAACE,MAAM,GAAG,KAAK,CAAC;MAC/C;MAEA,MAAM,IAAI,CAACvB,aAAa,CAACqB,SAAS,CAAC;MAEnC,OAAOlB,QAAQ;IACjB,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAM6D,cAAcA,CAACP,QAAQ,EAAErB,IAAI,EAAE;IACnC,IAAI;MACF;MACA,MAAM6B,UAAU,GAAG;QACjB,GAAG7B,IAAI;QACPqB;MACF,CAAC;;MAED;MACA,MAAMf,QAAQ,GAAG,MAAM,IAAI,CAACD,QAAQ,CAACwB,UAAU,CAAC;;MAEhD;MACA,IAAI,IAAI,CAACnD,eAAe,IAAI,IAAI,CAACU,QAAQ,IAAIiC,QAAQ,EAAE;QACrD,IAAI;UACF;UACA,MAAMS,OAAO,GAAG,IAAI,CAAC1C,QAAQ,CAAC0C,OAAO,CAAC,UAAUT,QAAQ,EAAE,CAAC;;UAE3D;UACA,IAAIU,KAAK;UACT,IAAI;YACFA,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,YAAY,CAAC;YACnC,MAAMA,KAAK,CAACC,GAAG,CAAC,CAAC;UACnB,CAAC,CAAC,OAAOC,UAAU,EAAE;YACnB;YACA,MAAMC,MAAM,GAAG,CACb;cAAEC,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAS,CAAC,EAC9B;cAAED,IAAI,EAAE,WAAW;cAAEC,IAAI,EAAE;YAAY,CAAC,EACxC;cAAED,IAAI,EAAE,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAC,EAClC;cAAED,IAAI,EAAE,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAC,EAClC;cAAED,IAAI,EAAE,cAAc;cAAEC,IAAI,EAAE;YAAS,CAAC,EACxC;cAAED,IAAI,EAAE,YAAY;cAAEC,IAAI,EAAE;YAAS,CAAC,EACtC;cAAED,IAAI,EAAE,SAAS;cAAEC,IAAI,EAAE;YAAS,CAAC,EACnC;cAAED,IAAI,EAAE,IAAI;cAAEC,IAAI,EAAE;YAAS,CAAC,EAC9B;cAAED,IAAI,EAAE,WAAW;cAAEC,IAAI,EAAE;YAAS,CAAC,EACrC;cAAED,IAAI,EAAE,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAC,EAClC;cAAED,IAAI,EAAE,QAAQ;cAAEC,IAAI,EAAE;YAAS,CAAC,EAClC;cAAED,IAAI,EAAE,eAAe;cAAEC,IAAI,EAAE;YAAS,CAAC,EACzC;cAAED,IAAI,EAAE,UAAU;cAAEC,IAAI,EAAE;YAAS,CAAC,CACrC;YAED,MAAMC,OAAO,GAAG;cACdH,MAAM,EAAEA,MAAM;cACdI,gBAAgB,EAAE;gBAChBF,IAAI,EAAE,KAAK;gBACXG,KAAK,EAAE;cACT;YACF,CAAC;YAED,MAAMT,OAAO,CAACU,WAAW,CAAC,YAAY,EAAEH,OAAO,CAAC;YAChDN,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,YAAY,CAAC;UACrC;;UAEA;UACA,MAAMA,KAAK,CAACU,MAAM,CAAC,CAACnC,QAAQ,CAAC,CAAC;QAChC,CAAC,CAAC,OAAOiB,aAAa,EAAE;UACtBvD,OAAO,CAACD,KAAK,CAAC,8DAA8DsD,QAAQ,GAAG,EAAEE,aAAa,CAAC;QACzG;MACF;MAEA,OAAOjB,QAAQ;IACjB,CAAC,CAAC,OAAOvC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAMuD,aAAaA,CAACoB,KAAK,EAAE;IACzB,IAAI;MACF;MACA,MAAMZ,OAAO,GAAG,IAAI,CAAC1C,QAAQ,CAAC0C,OAAO,CAAC,IAAI,CAAC9C,SAAS,CAAC;;MAErD;MACA,MAAM+C,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC7C,OAAO,CAAC;;MAEzC;MACA,MAAM6C,KAAK,CAACU,MAAM,CAAC,CAACC,KAAK,CAAC,CAAC;IAC7B,CAAC,CAAC,OAAO3E,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4E,YAAYA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/B,MAAMpB,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;;IAE5C;IACA,IAAI8C,YAAY,GAAGrB,SAAS;IAE5B,IAAIoB,OAAO,CAACE,SAAS,EAAE;MACrB,MAAMA,SAAS,GAAG,IAAIrC,IAAI,CAACmC,OAAO,CAACE,SAAS,CAAC;MAC7CD,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAI,IAAIwC,IAAI,CAACxC,GAAG,CAACuC,SAAS,CAAC,IAAIsC,SAAS,CAAC;IACjF;IAEA,IAAIF,OAAO,CAACI,OAAO,EAAE;MACnB,MAAMA,OAAO,GAAG,IAAIvC,IAAI,CAACmC,OAAO,CAACI,OAAO,CAAC;MACzCH,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAI,IAAIwC,IAAI,CAACxC,GAAG,CAACuC,SAAS,CAAC,IAAIwC,OAAO,CAAC;IAC/E;IAEA,IAAIJ,OAAO,CAACjC,MAAM,EAAE;MAClBkC,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAAC0C,MAAM,KAAKiC,OAAO,CAACjC,MAAM,CAAC;IAC1E;IAEA,IAAIiC,OAAO,CAAChC,MAAM,EAAE;MAClBiC,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAAC2C,MAAM,KAAKgC,OAAO,CAAChC,MAAM,CAAC;IAC1E;IAEA,IAAIgC,OAAO,CAAC/B,YAAY,EAAE;MACxBgC,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAAC4C,YAAY,KAAK+B,OAAO,CAAC/B,YAAY,CAAC;IACtF;IAEA,IAAI+B,OAAO,CAAC9B,UAAU,EAAE;MACtB+B,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAAC6C,UAAU,KAAK8B,OAAO,CAAC9B,UAAU,CAAC;IAClF;IAEA,IAAI8B,OAAO,CAAC1B,MAAM,EAAE;MAClB2B,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAACiD,MAAM,KAAK0B,OAAO,CAAC1B,MAAM,CAAC;IAC1E;IAEA,IAAI0B,OAAO,CAACzB,MAAM,EAAE;MAClB0B,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAACkD,MAAM,KAAKyB,OAAO,CAACzB,MAAM,CAAC;IAC1E;IAEA,IAAIyB,OAAO,CAACxB,aAAa,EAAE;MACzByB,YAAY,GAAGA,YAAY,CAACE,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAACmD,aAAa,KAAKwB,OAAO,CAACxB,aAAa,CAAC;IACxF;;IAEA;IACAyB,YAAY,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI1C,IAAI,CAAC0C,CAAC,CAAC3C,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACyC,CAAC,CAAC1C,SAAS,CAAC,CAAC;;IAE1E;IACA,MAAM4C,IAAI,GAAGR,OAAO,CAACQ,IAAI,IAAI,CAAC;IAC9B,MAAMC,KAAK,GAAGT,OAAO,CAACS,KAAK,IAAI,GAAG;IAClC,MAAMC,UAAU,GAAG,CAACF,IAAI,GAAG,CAAC,IAAIC,KAAK;IACrC,MAAME,QAAQ,GAAGH,IAAI,GAAGC,KAAK;IAE7B,MAAMG,aAAa,GAAGX,YAAY,CAACY,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;IAE9D,OAAO;MACLnD,IAAI,EAAEoD,aAAa;MACnBE,KAAK,EAAEb,YAAY,CAACnB,MAAM;MAC1B0B,IAAI;MACJC,KAAK;MACLM,UAAU,EAAEC,IAAI,CAACC,IAAI,CAAChB,YAAY,CAACnB,MAAM,GAAG2B,KAAK;IACnD,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMS,eAAeA,CAACvD,EAAE,EAAE;IACxB,MAAMiB,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;IAC5C,MAAM9B,GAAG,GAAGuD,SAAS,CAACuC,IAAI,CAAC9F,GAAG,IAAIA,GAAG,CAACsC,EAAE,KAAKA,EAAE,CAAC;IAEhD,IAAI,CAACtC,GAAG,EAAE;MACR,MAAM,IAAI+F,KAAK,CAAC,qBAAqBzD,EAAE,YAAY,CAAC;IACtD;IAEA,OAAOtC,GAAG;EACZ;;EAEA;AACF;AACA;EACE,MAAMgG,uBAAuBA,CAACpD,YAAY,EAAEC,UAAU,EAAE;IACtD,MAAMU,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;;IAE5C;IACA,MAAMmE,YAAY,GAAG1C,SAAS,CAACuB,MAAM,CAAC9E,GAAG,IACvCA,GAAG,CAAC4C,YAAY,KAAKA,YAAY,IAAI5C,GAAG,CAAC6C,UAAU,KAAKA,UAC1D,CAAC;;IAED;IACAoD,YAAY,CAACjB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI1C,IAAI,CAAC0C,CAAC,CAAC3C,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACyC,CAAC,CAAC1C,SAAS,CAAC,CAAC;IAE1E,OAAO0D,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAMC,mBAAmBA,CAACxD,MAAM,EAAE;IAChC,MAAMa,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;;IAE5C;IACA,MAAMqE,QAAQ,GAAG5C,SAAS,CAACuB,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAAC0C,MAAM,KAAKA,MAAM,CAAC;;IAE/D;IACAyD,QAAQ,CAACnB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI1C,IAAI,CAAC0C,CAAC,CAAC3C,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACyC,CAAC,CAAC1C,SAAS,CAAC,CAAC;IAEtE,OAAO4D,QAAQ;EACjB;;EAEA;AACF;AACA;EACE,MAAMC,mBAAmBA,CAAClD,MAAM,EAAE;IAChC,MAAMK,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;;IAE5C;IACA,MAAMuE,QAAQ,GAAG9C,SAAS,CAACuB,MAAM,CAAC9E,GAAG,IAAIA,GAAG,CAACkD,MAAM,KAAKA,MAAM,CAAC;;IAE/D;IACAmD,QAAQ,CAACrB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAI1C,IAAI,CAAC0C,CAAC,CAAC3C,SAAS,CAAC,GAAG,IAAIC,IAAI,CAACyC,CAAC,CAAC1C,SAAS,CAAC,CAAC;IAEtE,OAAO8D,QAAQ;EACjB;;EAEA;AACF;AACA;EACE,MAAM/E,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMgF,UAAU,GAAG,IAAI9D,IAAI,CAAC,CAAC;MAC7B8D,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAAChG,aAAa,CAAC;MAE7D,MAAM+C,SAAS,GAAG,MAAM,IAAI,CAACzB,aAAa,CAAC,CAAC;MAC5C,MAAM8C,YAAY,GAAGrB,SAAS,CAACuB,MAAM,CAAC9E,GAAG,IACvC,IAAIwC,IAAI,CAACxC,GAAG,CAACuC,SAAS,CAAC,IAAI+D,UAC7B,CAAC;MAED,IAAI1B,YAAY,CAACnB,MAAM,GAAGF,SAAS,CAACE,MAAM,EAAE;QAC1C,MAAM,IAAI,CAACvB,aAAa,CAAC0C,YAAY,CAAC;QACtC7E,OAAO,CAACC,GAAG,CAAC,oCAAoCsG,UAAU,CAAC7D,WAAW,CAAC,CAAC,EAAE,CAAC;MAC7E;IACF,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;EACF;;EAEA;AACF;AACA;EACE2G,qBAAqBA,CAAA,EAAG;IACtB,OAAO,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;MACzB;MACA,MAAMC,WAAW,GAAGF,GAAG,CAACG,GAAG;;MAE3B;MACAH,GAAG,CAACG,GAAG,GAAG,UAASC,KAAK,EAAEC,QAAQ,EAAE;QAClC;QACAL,GAAG,CAACG,GAAG,GAAGD,WAAW;;QAErB;QACAF,GAAG,CAACG,GAAG,CAACC,KAAK,EAAEC,QAAQ,CAAC;;QAExB;QACA,IAAIN,GAAG,CAAChH,IAAI,CAACuH,UAAU,CAAC,SAAS,CAAC,IAAIP,GAAG,CAAChH,IAAI,CAACuH,UAAU,CAAC,wBAAwB,CAAC,EAAE;UACnF;QACF;;QAEA;QACA,MAAMC,SAAS,GAAG;UAChBxE,MAAM,EAAEgE,GAAG,CAACS,IAAI,GAAGT,GAAG,CAACS,IAAI,CAAC7E,EAAE,GAAG,IAAI;UACrCK,MAAM,EAAE+D,GAAG,CAACU,MAAM;UAClBxE,YAAY,EAAE8D,GAAG,CAAChH,IAAI,CAAC2H,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;UACjDxE,UAAU,EAAE6D,GAAG,CAACY,MAAM,CAAChF,EAAE,IAAI,IAAI;UACjCQ,OAAO,EAAE;YACPpD,IAAI,EAAEgH,GAAG,CAAChH,IAAI;YACd6H,KAAK,EAAEb,GAAG,CAACa,KAAK;YAChBC,IAAI,EAAEd,GAAG,CAACU,MAAM,KAAK,KAAK,GAAGV,GAAG,CAACc,IAAI,GAAG;UAC1C,CAAC;UACDzE,EAAE,EAAE2D,GAAG,CAAC3D,EAAE,IAAI2D,GAAG,CAACe,UAAU,CAACC,aAAa;UAC1C1E,SAAS,EAAE0D,GAAG,CAACiB,OAAO,CAAC,YAAY,CAAC;UACpC1E,MAAM,EAAE0D,GAAG,CAACiB,UAAU,IAAI,GAAG,GAAG,SAAS,GAAG,SAAS;UACrD1E,MAAM,EAAEwD,GAAG,CAACiB,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI;UACxCxE,aAAa,EAAEuD,GAAG,CAACiB,OAAO,CAAC,kBAAkB,CAAC,IAAI,IAAI;UACtDvE,QAAQ,EAAEsD,GAAG,CAACiB,OAAO,CAAC,aAAa,CAAC,IAAI;QAC1C,CAAC;;QAED;QACA,IAAIjB,GAAG,CAACiB,OAAO,CAAC,aAAa,CAAC,EAAE;UAC9B,IAAI,CAAChE,cAAc,CAAC+C,GAAG,CAACiB,OAAO,CAAC,aAAa,CAAC,EAAET,SAAS,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAAC9E,QAAQ,CAAC8E,SAAS,CAAC;QAC1B;MACF,CAAC;MAEDN,IAAI,CAAC,CAAC;IACR,CAAC;EACH;AACF;AAEAiB,MAAM,CAACC,OAAO,GAAG7H,YAAY", "ignoreList": []}