import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'dashboard-visualization',
    top: 50,
    left: 350,
    width: 300,
    text: 'User Interface and Visualization',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // Key Visualization Approaches
  {
    id: 'visualization-approaches',
    top: 120,
    left: 350,
    width: 300,
    text: 'Key Visualization Approaches',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  // Real-time Dashboards
  {
    id: 'real-time-dashboards',
    top: 180,
    left: 200,
    width: 200,
    text: 'Real-time Dashboards',
    number: '3',
    bold: true,
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'metric-gauges',
    top: 240,
    left: 50,
    width: 150,
    text: 'Metric Gauges\nCircular or linear gauges',
    number: '4',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'trend-charts',
    top: 240,
    left: 225,
    width: 150,
    text: 'Trend Charts\nLine, bar, or area charts',
    number: '5',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'heatmaps',
    top: 240,
    left: 400,
    width: 150,
    text: 'Heatmaps\nColor-coded representations',
    number: '6',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'status-indicators',
    top: 240,
    left: 575,
    width: 150,
    text: 'Status Indicators\nRed/yellow/green indicators',
    number: '7',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  // Trinity Visualization
  {
    id: 'trinity-visualization',
    top: 180,
    left: 600,
    width: 200,
    text: 'Trinity Visualization',
    number: '8',
    bold: true,
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'trinity-triangle',
    top: 300,
    left: 50,
    width: 150,
    text: 'Trinity Triangle\nG, D, R vertices',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'force-diagram',
    top: 300,
    left: 225,
    width: 150,
    text: 'Force Diagram\nPush and pull between components',
    number: '10',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'vector-field',
    top: 300,
    left: 400,
    width: 150,
    text: 'Vector Field\nCurl and divergence operations',
    number: '11',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  {
    id: 'phase-space',
    top: 300,
    left: 575,
    width: 150,
    text: 'Phase Space Plot\nG-D-R coordinate system',
    number: '12',
    fontSize: '12px',
    backgroundColor: '#fff2e8'
  },
  // Field Coherence Map
  {
    id: 'field-coherence',
    top: 360,
    left: 200,
    width: 200,
    text: 'Field Coherence Map',
    number: '13',
    bold: true,
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'coherence-equation',
    top: 420,
    left: 200,
    width: 200,
    text: 'Ψ(x,t) = ∑ψₙ(x)e^(-iEₙt/ℏ)',
    number: '14',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  // Tensor Field Representation
  {
    id: 'tensor-field',
    top: 360,
    left: 600,
    width: 200,
    text: 'Tensor Field Representation',
    number: '15',
    bold: true,
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'tensor-equation',
    top: 420,
    left: 600,
    width: 200,
    text: '∇×(πG⊗φD) + ∂(eR)/∂t = ℏ(∇×c⁻¹)',
    number: '16',
    fontSize: '12px',
    backgroundColor: '#f9f0ff'
  },
  // User Interaction Models
  {
    id: 'interaction-models',
    top: 480,
    left: 350,
    width: 300,
    text: 'User Interaction Models',
    number: '17',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  // Role-Based Customization
  {
    id: 'role-based',
    top: 540,
    left: 350,
    width: 300,
    text: 'Role-Based Customization',
    number: '18',
    bold: true,
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'executive-view',
    top: 600,
    left: 100,
    width: 150,
    text: 'Executive View\nHigh-level dashboards',
    number: '19',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'analyst-view',
    top: 600,
    left: 300,
    width: 150,
    text: 'Analyst View\nDetailed visualizations',
    number: '20',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'operator-view',
    top: 600,
    left: 500,
    width: 150,
    text: 'Operator View\nOperational controls',
    number: '21',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'compliance-view',
    top: 600,
    left: 700,
    width: 150,
    text: 'Compliance View\nRegulatory tracking',
    number: '22',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  // Interactive Exploration
  {
    id: 'interactive-exploration',
    top: 660,
    left: 200,
    width: 200,
    text: 'Interactive Exploration',
    number: '23',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Automated Insights
  {
    id: 'automated-insights',
    top: 660,
    left: 500,
    width: 200,
    text: 'Automated Insights',
    number: '24',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Implementation
  {
    id: 'implementation',
    top: 720,
    left: 350,
    width: 300,
    text: 'Implementation: NovaView and NovaVision',
    number: '25',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#f9f0ff'
  }
];

const connections = [
  // Connect User Interface to Visualization Approaches
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect Visualization Approaches to specific approaches
  {
    start: { x: 350, y: 170 },
    end: { x: 300, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 170 },
    end: { x: 700, y: 180 },
    type: 'arrow'
  },
  // Connect Real-time Dashboards to components
  {
    start: { x: 175, y: 210 },
    end: { x: 125, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 225, y: 210 },
    end: { x: 300, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 275, y: 210 },
    end: { x: 475, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 325, y: 210 },
    end: { x: 650, y: 240 },
    type: 'arrow'
  },
  // Connect Trinity Visualization to components
  {
    start: { x: 575, y: 210 },
    end: { x: 125, y: 300 },
    type: 'arrow'
  },
  {
    start: { x: 625, y: 210 },
    end: { x: 300, y: 300 },
    type: 'arrow'
  },
  {
    start: { x: 675, y: 210 },
    end: { x: 475, y: 300 },
    type: 'arrow'
  },
  {
    start: { x: 725, y: 210 },
    end: { x: 650, y: 300 },
    type: 'arrow'
  },
  // Connect to Field Coherence Map
  {
    start: { x: 300, y: 330 },
    end: { x: 300, y: 360 },
    type: 'arrow'
  },
  // Connect Field Coherence Map to equation
  {
    start: { x: 300, y: 390 },
    end: { x: 300, y: 420 },
    type: 'line'
  },
  // Connect to Tensor Field Representation
  {
    start: { x: 700, y: 330 },
    end: { x: 700, y: 360 },
    type: 'arrow'
  },
  // Connect Tensor Field Representation to equation
  {
    start: { x: 700, y: 390 },
    end: { x: 700, y: 420 },
    type: 'line'
  },
  // Connect to User Interaction Models
  {
    start: { x: 500, y: 450 },
    end: { x: 500, y: 480 },
    type: 'arrow'
  },
  // Connect User Interaction Models to Role-Based Customization
  {
    start: { x: 500, y: 530 },
    end: { x: 500, y: 540 },
    type: 'arrow'
  },
  // Connect Role-Based Customization to specific roles
  {
    start: { x: 350, y: 590 },
    end: { x: 175, y: 600 },
    type: 'arrow'
  },
  {
    start: { x: 400, y: 590 },
    end: { x: 375, y: 600 },
    type: 'arrow'
  },
  {
    start: { x: 500, y: 590 },
    end: { x: 575, y: 600 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 590 },
    end: { x: 775, y: 600 },
    type: 'arrow'
  },
  // Connect to Interactive Exploration and Automated Insights
  {
    start: { x: 300, y: 630 },
    end: { x: 300, y: 660 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 630 },
    end: { x: 600, y: 660 },
    type: 'arrow'
  },
  // Connect to Implementation
  {
    start: { x: 300, y: 690 },
    end: { x: 350, y: 720 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 690 },
    end: { x: 550, y: 720 },
    type: 'arrow'
  }
];

const DashboardVisualization: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="770px" 
    />
  );
};

export default DashboardVisualization;
