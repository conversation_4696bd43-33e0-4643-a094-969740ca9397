<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>5. Trinity Equation Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 800px;
            height: 600px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .triangle {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
    </style>
</head>
<body>
    <h1>5. Trinity Equation Visualization</h1>
    
    <div class="diagram-container">
        <!-- Trinity Equation -->
        <div class="element" style="top: 50px; left: 250px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 20px;">
            Trinity Equation Visualization
            <div class="element-number">1</div>
        </div>
        
        <!-- Equation -->
        <div class="element" style="top: 120px; left: 250px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            U=T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]
            <div class="element-number">2</div>
        </div>
        
        <!-- Truth -->
        <div class="element" style="top: 200px; left: 100px; width: 150px; background-color: #e6f7ff; font-weight: bold;">
            Truth
            <div class="element-number">3</div>
        </div>
        
        <!-- Trust -->
        <div class="element" style="top: 200px; left: 550px; width: 150px; background-color: #e6f7ff; font-weight: bold;">
            Trust
            <div class="element-number">4</div>
        </div>
        
        <!-- Transparency -->
        <div class="element" style="top: 350px; left: 325px; width: 150px; background-color: #e6f7ff; font-weight: bold;">
            Transparency
            <div class="element-number">5</div>
        </div>
        
        <!-- Truth Description -->
        <div class="element" style="top: 250px; left: 100px; width: 150px; background-color: #f6ffed;">
            Factual accuracy<br>Data integrity<br>Source reliability
            <div class="element-number">6</div>
        </div>
        
        <!-- Trust Description -->
        <div class="element" style="top: 250px; left: 550px; width: 150px; background-color: #f6ffed;">
            System reliability<br>Predictable behavior<br>Consistent outcomes
            <div class="element-number">7</div>
        </div>
        
        <!-- Transparency Description -->
        <div class="element" style="top: 400px; left: 325px; width: 150px; background-color: #f6ffed;">
            Visibility into processes<br>Explainable decisions<br>Audit capability
            <div class="element-number">8</div>
        </div>
        
        <!-- Tensorial Governance -->
        <div class="element" style="top: 480px; left: 250px; width: 300px; background-color: #fff2e8; font-weight: bold; font-size: 16px;">
            Tensorial Governance
            <div class="element-number">9</div>
        </div>
        
        <!-- Connections -->
        <!-- Equation to Truth -->
        <div class="connection" style="top: 170px; left: 250px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 200px; left: 90px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Equation to Trust -->
        <div class="connection" style="top: 170px; left: 550px; width: 150px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 200px; left: 540px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Equation to Transparency -->
        <div class="connection" style="top: 170px; left: 400px; width: 2px; height: 180px; background-color: black;"></div>
        <div class="arrow" style="top: 340px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Truth to Description -->
        <div class="connection" style="top: 230px; left: 175px; width: 2px; height: 20px; background-color: black;"></div>
        
        <!-- Trust to Description -->
        <div class="connection" style="top: 230px; left: 625px; width: 2px; height: 20px; background-color: black;"></div>
        
        <!-- Transparency to Description -->
        <div class="connection" style="top: 380px; left: 400px; width: 2px; height: 20px; background-color: black;"></div>
        
        <!-- Triangle connecting the three principles -->
        <div style="position: absolute; top: 225px; left: 175px; width: 450px; height: 150px; border: 2px solid black; border-top: none; border-left: none; border-right: none; z-index: 1;"></div>
        <div style="position: absolute; top: 225px; left: 175px; width: 225px; height: 150px; border: 2px solid black; border-top: none; border-right: none; transform: rotate(63.4deg); transform-origin: left top; z-index: 1;"></div>
        <div style="position: absolute; top: 225px; left: 625px; width: 225px; height: 150px; border: 2px solid black; border-top: none; border-left: none; transform: rotate(-63.4deg); transform-origin: right top; z-index: 1;"></div>
        
        <!-- All principles to Tensorial Governance -->
        <div class="connection" style="top: 450px; left: 400px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 470px; left: 395px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
    </div>
</body>
</html>
