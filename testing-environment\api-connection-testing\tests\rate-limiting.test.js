/**
 * Rate Limiting Tests for NovaConnect Universal API Connector
 * 
 * These tests verify that the connector can handle rate limiting.
 */

const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { 
  startAllServices, 
  stopAllServices,
  registryUrl,
  authUrl,
  executorUrl,
  mockApiUrl
} = require('../setup');

// Test data
const connector = require('../connectors/rate-limiting-connector.json');

// Test credential
const credential = {
  name: 'Rate Limiting Test Credential',
  authType: 'API_KEY',
  credentials: {
    apiKey: 'valid-api-key',
    headerName: 'X-API-Key'
  }
};

// Store connector and credential IDs
let connectorId;
let credentialId;

describe('Rate Limiting Tests', () => {
  // Start services before all tests
  beforeAll(async () => {
    await startAllServices();
    
    // Clear request history
    await axios.post(`${mockApiUrl}/clear-history`);
    
    // Register connector
    const connResponse = await axios.post(`${registryUrl}/connectors`, connector);
    connectorId = connResponse.data.id;
    
    // Create credential
    credential.connectorId = connectorId;
    const credResponse = await axios.post(`${authUrl}/credentials`, credential);
    credentialId = credResponse.data.id;
  }, 60000);
  
  // Stop services after all tests
  afterAll(async () => {
    // Clean up test data
    try {
      await axios.delete(`${registryUrl}/connectors/${connectorId}`);
    } catch (error) {
      console.error(`Error deleting connector ${connectorId}:`, error.message);
    }
    
    try {
      await axios.delete(`${authUrl}/credentials/${credentialId}`);
    } catch (error) {
      console.error(`Error deleting credential ${credentialId}:`, error.message);
    }
    
    stopAllServices();
  });
  
  // Test rate limiting
  describe('Rate Limiting', () => {
    it('should handle rate-limited endpoints', async () => {
      // Make multiple requests to trigger rate limiting
      const responses = [];
      
      // First 5 requests should succeed
      for (let i = 0; i < 5; i++) {
        const response = await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        responses.push(response);
      }
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('requestCount');
        expect(response.data).toHaveProperty('message', 'Rate limited endpoint');
      });
      
      // The 6th request should fail with a 429 error
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Rate limit');
      }
    });
    
    it('should respect rate limit headers', async () => {
      // Clear request history
      await axios.post(`${mockApiUrl}/clear-history`);
      
      // Make a request to check rate limit headers
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Verify the request was made and rate limit headers were processed
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const rateLimitRequest = historyResponse.data.find(req => 
        req.path === '/rate-limited'
      );
      
      expect(rateLimitRequest).toBeDefined();
      
      // Check that the connector executor processed the rate limit headers
      // This is hard to verify directly, but we can check that the response includes
      // the count from the rate-limited endpoint
      expect(response.data).toHaveProperty('requestCount');
    });
  });
  
  // Test rate limit configuration
  describe('Rate Limit Configuration', () => {
    it('should respect connector rate limit configuration', async () => {
      // Update the connector with a more restrictive rate limit
      const updatedConnector = {
        ...connector,
        configuration: {
          ...connector.configuration,
          rateLimit: {
            requests: 2,
            period: '1m'
          }
        }
      };
      
      await axios.put(`${registryUrl}/connectors/${connectorId}`, updatedConnector);
      
      // Clear request history
      await axios.post(`${mockApiUrl}/clear-history`);
      
      // Make 2 requests (should succeed)
      const responses = [];
      
      for (let i = 0; i < 2; i++) {
        const response = await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        responses.push(response);
      }
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // The 3rd request should be rate limited by the connector's configuration
      // (not by the API's rate limit)
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('Rate limit');
      }
      
      // Verify that only 2 requests were made to the API
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const rateLimitRequests = historyResponse.data.filter(req => 
        req.path === '/rate-limited'
      );
      
      expect(rateLimitRequests.length).toBe(2);
    });
  });
  
  // Test rate limit backoff
  describe('Rate Limit Backoff', () => {
    it('should implement exponential backoff for rate-limited requests', async () => {
      // Update the connector with retry configuration
      const updatedConnector = {
        ...connector,
        configuration: {
          ...connector.configuration,
          retryPolicy: {
            maxRetries: 3,
            backoffStrategy: 'exponential',
            initialDelayMs: 100
          }
        }
      };
      
      await axios.put(`${registryUrl}/connectors/${connectorId}`, updatedConnector);
      
      // Clear request history
      await axios.post(`${mockApiUrl}/clear-history`);
      
      // Make a request that will be rate limited
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
          credentialId,
          parameters: {},
          userId: 'test-user'
        });
        
        // This should succeed because we've reset the rate limit counter
        
        // Make 5 more requests to trigger rate limiting
        for (let i = 0; i < 5; i++) {
          await axios.post(`${executorUrl}/execute/${connectorId}/rateLimitedEndpoint`, {
            credentialId,
            parameters: {},
            userId: 'test-user'
          });
        }
        
        fail('Expected one of the requests to fail');
      } catch (error) {
        expect(error.response.status).toBe(429);
      }
      
      // Verify that retries were attempted with exponential backoff
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const rateLimitRequests = historyResponse.data.filter(req => 
        req.path === '/rate-limited'
      );
      
      // Should have made the original requests plus some retries
      expect(rateLimitRequests.length).toBeGreaterThan(1);
      
      // Check the timestamps to verify exponential backoff
      // This is hard to verify precisely, but we can check that the timestamps are increasing
      const timestamps = rateLimitRequests.map(req => new Date(req.timestamp).getTime());
      
      // Calculate time differences between consecutive requests
      const timeDiffs = [];
      for (let i = 1; i < timestamps.length; i++) {
        timeDiffs.push(timestamps[i] - timestamps[i - 1]);
      }
      
      // For exponential backoff, later time differences should be larger
      // Check if at least one later time difference is larger than the first one
      const hasIncreasingTimeDiffs = timeDiffs.slice(1).some(diff => diff > timeDiffs[0]);
      expect(hasIncreasingTimeDiffs).toBe(true);
    });
  });
});
