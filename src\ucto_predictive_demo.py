"""
Demo script for the Predictive Compliance Intelligence Engine of the Universal Compliance Tracking Optimizer (UCTO).

This script demonstrates how to use the Predictive Engine to predict compliance gaps,
forecast resource requirements, and recommend proactive actions.
"""

import os
import sys
import json
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTO
from ucto import PredictiveEngine, TrackingManager

def generate_sample_data():
    """Generate sample compliance data for demonstration purposes."""
    logger.info("Generating sample compliance data")

    # Create a tracking manager to create requirements and activities
    tracking_manager = TrackingManager()

    # Generate sample requirements
    requirements = []

    # GDPR requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'framework': 'GDPR',
        'category': 'privacy',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'data_subject_rights', 'privacy']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Protection Impact Assessment',
        'description': 'Conduct data protection impact assessments for high-risk processing',
        'framework': 'GDPR',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=60)).isoformat(),
        'assigned_to': 'privacy_officer',
        'tags': ['gdpr', 'dpia', 'risk_assessment']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Data Breach Notification',
        'description': 'Implement processes for notifying authorities of data breaches',
        'framework': 'GDPR',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'pending',
        'due_date': (datetime.now() + timedelta(days=45)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['gdpr', 'breach_notification', 'incident_response']
    }))

    # SOC 2 requirements
    requirements.append(tracking_manager.create_requirement({
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'framework': 'SOC 2',
        'category': 'access_control',
        'priority': 'high',
        'status': 'completed',
        'due_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'access_control', 'security']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Risk Management',
        'description': 'Implement risk management processes to identify and mitigate risks',
        'framework': 'SOC 2',
        'category': 'risk_assessment',
        'priority': 'medium',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=30)).isoformat(),
        'assigned_to': 'risk_manager',
        'tags': ['soc2', 'risk_management', 'risk_assessment']
    }))

    requirements.append(tracking_manager.create_requirement({
        'name': 'Incident Response',
        'description': 'Implement incident response processes to detect and respond to security incidents',
        'framework': 'SOC 2',
        'category': 'incident_response',
        'priority': 'high',
        'status': 'in_progress',
        'due_date': (datetime.now() + timedelta(days=15)).isoformat(),
        'assigned_to': 'security_officer',
        'tags': ['soc2', 'incident_response', 'security']
    }))

    # Generate sample activities
    activities = []

    # Activities for Data Subject Rights
    activities.append(tracking_manager.create_activity({
        'name': 'Document Data Subject Rights Process',
        'description': 'Create documentation for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'assigned_to': 'privacy_officer',
        'notes': 'Documentation completed and reviewed'
    }))

    activities.append(tracking_manager.create_activity({
        'name': 'Implement Data Subject Rights Portal',
        'description': 'Develop a portal for handling data subject rights requests',
        'requirement_id': requirements[0]['id'],
        'type': 'task',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=10)).isoformat(),
        'assigned_to': 'developer',
        'notes': 'Portal development in progress'
    }))

    # Activities for Access Control
    activities.append(tracking_manager.create_activity({
        'name': 'Implement Role-Based Access Control',
        'description': 'Implement role-based access control for all systems',
        'requirement_id': requirements[3]['id'],
        'type': 'task',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=30)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'assigned_to': 'security_engineer',
        'notes': 'RBAC implemented for all systems'
    }))

    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Access Control Audit',
        'description': 'Audit access controls to ensure proper implementation',
        'requirement_id': requirements[3]['id'],
        'type': 'audit',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=15)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'auditor',
        'notes': 'Audit completed with no findings'
    }))

    # Activities for Incident Response
    activities.append(tracking_manager.create_activity({
        'name': 'Develop Incident Response Plan',
        'description': 'Develop a comprehensive incident response plan',
        'requirement_id': requirements[5]['id'],
        'type': 'documentation',
        'status': 'completed',
        'start_date': (datetime.now() - timedelta(days=20)).isoformat(),
        'end_date': (datetime.now() - timedelta(days=10)).isoformat(),
        'assigned_to': 'security_officer',
        'notes': 'Incident response plan developed'
    }))

    activities.append(tracking_manager.create_activity({
        'name': 'Conduct Incident Response Training',
        'description': 'Train staff on incident response procedures',
        'requirement_id': requirements[5]['id'],
        'type': 'meeting',
        'status': 'in_progress',
        'start_date': (datetime.now() - timedelta(days=5)).isoformat(),
        'end_date': (datetime.now() + timedelta(days=5)).isoformat(),
        'assigned_to': 'trainer',
        'notes': 'Training sessions in progress'
    }))

    logger.info(f"Generated {len(requirements)} requirements and {len(activities)} activities")

    return requirements, activities

def enhance_prediction_with_sample_data(prediction: Dict[str, Any], requirements: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Enhance a prediction with sample data for demonstration purposes.

    Args:
        prediction: The prediction to enhance
        requirements: The requirements to use for enhancement

    Returns:
        The enhanced prediction
    """
    # Find requirements that are pending and have high priority
    high_priority_pending = [r for r in requirements if r['status'] == 'pending' and r['priority'] == 'high']

    # Add predicted gaps
    for req in high_priority_pending:
        prediction['predicted_gaps'].append({
            'requirement_id': req['id'],
            'requirement_name': req['name'],
            'framework': req['framework'],
            'category': req['category'],
            'due_date': req['due_date'],
            'risk_level': 'high',
            'confidence': 0.85,
            'reason': 'High priority requirement pending with approaching due date'
        })

    # Find requirements with approaching due dates
    approaching_due_date = [r for r in requirements if r['status'] != 'completed' and
                           datetime.fromisoformat(r['due_date']) < datetime.now() + timedelta(days=30)]

    # Add more predicted gaps
    for req in approaching_due_date:
        if req['id'] not in [g['requirement_id'] for g in prediction['predicted_gaps']]:
            prediction['predicted_gaps'].append({
                'requirement_id': req['id'],
                'requirement_name': req['name'],
                'framework': req['framework'],
                'category': req['category'],
                'due_date': req['due_date'],
                'risk_level': 'medium',
                'confidence': 0.75,
                'reason': 'Requirement with approaching due date'
            })

    # Update confidence score
    prediction['confidence_score'] = 0.8

    return prediction

def enhance_forecast_with_sample_data(forecast: Dict[str, Any], requirements: List[Dict[str, Any]],
                                     activities: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Enhance a forecast with sample data for demonstration purposes.

    Args:
        forecast: The forecast to enhance
        requirements: The requirements to use for enhancement
        activities: The activities to use for enhancement

    Returns:
        The enhanced forecast
    """
    # Calculate resource requirements based on requirements and activities
    staff_resources = {
        'privacy_officer': 0,
        'security_officer': 0,
        'developer': 0,
        'auditor': 0
    }

    # Count requirements by assigned_to
    for req in requirements:
        if req['status'] != 'completed':
            assigned_to = req['assigned_to']
            if assigned_to in staff_resources:
                staff_resources[assigned_to] += 1

    # Count activities by assigned_to
    for act in activities:
        if act['status'] != 'completed':
            assigned_to = act['assigned_to']
            if assigned_to in staff_resources:
                staff_resources[assigned_to] += 1

    # Add forecasted resources
    for role, count in staff_resources.items():
        if count > 0:
            forecast['forecasted_resources'].append({
                'resource_type': 'staff',
                'resource_name': role,
                'estimated_hours': count * 20,  # Assume 20 hours per requirement/activity
                'confidence': 0.8,
                'time_period': 'next_30_days'
            })

    # Add budget resources
    forecast['forecasted_resources'].append({
        'resource_type': 'budget',
        'resource_name': 'compliance_software',
        'estimated_amount': 5000,
        'confidence': 0.7,
        'time_period': 'next_90_days'
    })

    forecast['forecasted_resources'].append({
        'resource_type': 'budget',
        'resource_name': 'consulting_services',
        'estimated_amount': 10000,
        'confidence': 0.6,
        'time_period': 'next_90_days'
    })

    # Update confidence score
    forecast['confidence_score'] = 0.75

    return forecast

def enhance_recommendations_with_sample_data(recommendations: Dict[str, Any],
                                           prediction: Dict[str, Any],
                                           forecast: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enhance recommendations with sample data for demonstration purposes.

    Args:
        recommendations: The recommendations to enhance
        prediction: The prediction to use for enhancement
        forecast: The forecast to use for enhancement

    Returns:
        The enhanced recommendations
    """
    # Add recommendations based on predicted gaps
    for gap in prediction['predicted_gaps']:
        recommendations['recommended_actions'].append({
            'action_id': f"action_{len(recommendations['recommended_actions']) + 1}",
            'action_type': 'address_gap',
            'description': f"Address compliance gap for {gap['requirement_name']}",
            'details': f"Prioritize implementation of {gap['requirement_name']} to avoid compliance gap",
            'priority': gap['risk_level'],
            'due_date': gap['due_date'],
            'assigned_to': None,
            'related_requirement_id': gap['requirement_id']
        })

    # Add recommendations based on forecasted resources
    staff_resources = [r for r in forecast['forecasted_resources'] if r['resource_type'] == 'staff']
    for resource in staff_resources:
        if resource['estimated_hours'] > 40:  # If more than 40 hours needed
            recommendations['recommended_actions'].append({
                'action_id': f"action_{len(recommendations['recommended_actions']) + 1}",
                'action_type': 'resource_allocation',
                'description': f"Allocate additional resources to {resource['resource_name']}",
                'details': f"Consider allocating additional resources to support {resource['resource_name']} who has an estimated workload of {resource['estimated_hours']} hours",
                'priority': 'medium',
                'due_date': None,
                'assigned_to': None,
                'related_resource': resource['resource_name']
            })

    # Add general recommendations
    recommendations['recommended_actions'].append({
        'action_id': f"action_{len(recommendations['recommended_actions']) + 1}",
        'action_type': 'process_improvement',
        'description': "Implement automated compliance monitoring",
        'details': "Consider implementing automated compliance monitoring to reduce manual effort and improve accuracy",
        'priority': 'medium',
        'due_date': None,
        'assigned_to': None,
        'related_resource': None
    })

    # Calculate priority scores
    priority_counts = {'high': 0, 'medium': 0, 'low': 0}
    for action in recommendations['recommended_actions']:
        if action['priority'] in priority_counts:
            priority_counts[action['priority']] += 1

    recommendations['priority_scores'] = priority_counts

    return recommendations

def main():
    """Run the Predictive Compliance Intelligence Engine demo."""
    logger.info("Starting Predictive Compliance Intelligence Engine demo")

    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'predictive_output')
    os.makedirs(output_dir, exist_ok=True)

    # Initialize the Predictive Engine
    data_dir = os.path.join(output_dir, 'prediction_data')
    predictive_engine = PredictiveEngine(data_dir)

    # Generate sample data
    requirements, activities = generate_sample_data()

    # Step 1: Predict compliance gaps
    logger.info("Step 1: Predicting compliance gaps")

    prediction = predictive_engine.predict_compliance_gaps(
        requirements=requirements,
        activities=activities,
        parameters={
            'confidence_threshold': 0.7,
            'time_horizon_days': 90
        }
    )

    # Enhance the prediction with sample data
    prediction = enhance_prediction_with_sample_data(prediction, requirements)

    # Save prediction to file
    with open(os.path.join(output_dir, 'compliance_gaps_prediction.json'), 'w', encoding='utf-8') as f:
        json.dump(prediction, f, indent=2)

    logger.info(f"Saved compliance gaps prediction to {os.path.join(output_dir, 'compliance_gaps_prediction.json')}")

    # Step 2: Forecast resource requirements
    logger.info("Step 2: Forecasting resource requirements")

    forecast = predictive_engine.forecast_resource_requirements(
        requirements=requirements,
        activities=activities,
        parameters={
            'time_horizon_days': 90,
            'resource_types': ['staff', 'budget', 'time']
        }
    )

    # Enhance the forecast with sample data
    forecast = enhance_forecast_with_sample_data(forecast, requirements, activities)

    # Save forecast to file
    with open(os.path.join(output_dir, 'resource_requirements_forecast.json'), 'w', encoding='utf-8') as f:
        json.dump(forecast, f, indent=2)

    logger.info(f"Saved resource requirements forecast to {os.path.join(output_dir, 'resource_requirements_forecast.json')}")

    # Step 3: Recommend proactive actions
    logger.info("Step 3: Recommending proactive actions")

    recommendations = predictive_engine.recommend_actions(
        requirements=requirements,
        activities=activities,
        predictions=[prediction],
        parameters={
            'max_recommendations': 10,
            'priority_threshold': 'medium'
        }
    )

    # Enhance the recommendations with sample data
    recommendations = enhance_recommendations_with_sample_data(recommendations, prediction, forecast)

    # Save recommendations to file
    with open(os.path.join(output_dir, 'proactive_actions_recommendations.json'), 'w', encoding='utf-8') as f:
        json.dump(recommendations, f, indent=2)

    logger.info(f"Saved proactive actions recommendations to {os.path.join(output_dir, 'proactive_actions_recommendations.json')}")

    # Step 4: Generate a summary report
    logger.info("Step 4: Generating a summary report")

    # Create a summary report
    summary_report = {
        'timestamp': datetime.now().isoformat(),
        'prediction_summary': {
            'total_gaps': len(prediction['predicted_gaps']),
            'high_risk_gaps': len([g for g in prediction['predicted_gaps'] if g['risk_level'] == 'high']),
            'medium_risk_gaps': len([g for g in prediction['predicted_gaps'] if g['risk_level'] == 'medium']),
            'low_risk_gaps': len([g for g in prediction['predicted_gaps'] if g['risk_level'] == 'low']),
            'confidence_score': prediction['confidence_score']
        },
        'forecast_summary': {
            'total_resources': len(forecast['forecasted_resources']),
            'staff_resources': len([r for r in forecast['forecasted_resources'] if r['resource_type'] == 'staff']),
            'budget_resources': len([r for r in forecast['forecasted_resources'] if r['resource_type'] == 'budget']),
            'time_resources': len([r for r in forecast['forecasted_resources'] if r['resource_type'] == 'time']),
            'confidence_score': forecast['confidence_score']
        },
        'recommendations_summary': {
            'total_actions': len(recommendations['recommended_actions']),
            'high_priority_actions': recommendations['priority_scores'].get('high', 0),
            'medium_priority_actions': recommendations['priority_scores'].get('medium', 0),
            'low_priority_actions': recommendations['priority_scores'].get('low', 0)
        }
    }

    # Save summary report to file
    with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)

    logger.info(f"Saved summary report to {os.path.join(output_dir, 'summary_report.json')}")

    logger.info("Predictive Compliance Intelligence Engine demo completed successfully")
    logger.info(f"All output files are in: {output_dir}")

if __name__ == '__main__':
    main()
