{"name": "nova-hybrid-verification", "version": "0.1.0", "description": "Hybrid DAG-based Zero-Knowledge System for NovaFuse - A Comphyology-aligned verification framework", "main": "src/index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:performance": "jest --testPathPattern=performance", "lint": "eslint src", "docs": "jsdoc -c jsdoc.json", "build": "babel src -d dist", "start": "node src/index.js"}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-hybrid-verification.git"}, "keywords": ["dag", "zero-knowledge", "verification", "comphyology", "novafuse", "trinity", "cryptography"], "author": "NovaFuse Technologies", "license": "UNLICENSED", "private": true, "bugs": {"url": "https://github.com/Dartan1983/nova-hybrid-verification/issues"}, "homepage": "https://github.com/Dartan1983/nova-hybrid-verification#readme", "dependencies": {"crypto": "^1.0.1", "debug": "^4.3.4", "events": "^3.3.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/cli": "^7.21.0", "@babel/core": "^7.21.4", "@babel/preset-env": "^7.21.4", "eslint": "^8.38.0", "jest": "^29.5.0", "jsdoc": "^4.0.2"}, "engines": {"node": ">=16.0.0"}}