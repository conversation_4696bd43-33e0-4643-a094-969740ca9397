"""
Demo script for NovaFlowX (NUWO) - Universal Workflow Orchestrator.

This script demonstrates how to use NovaFlowX to automate compliance workflows.
"""

import os
import sys
import json
import uuid
import logging
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import NovaTrack and NovaFlowX components
from novatrack import TrackingManager
from novaflowx import (
    AutomationManager,
    ScheduleTrigger,
    EventTrigger,
    CreateRequirementAction,
    CollectEvidenceAction
)

def create_sample_workflow():
    """Create a sample workflow for demonstration purposes."""
    logger.info("Creating sample workflow")

    # Create a workflow ID
    workflow_id = f"workflow-{uuid.uuid4()}"

    # Create a workflow
    workflow = {
        "id": workflow_id,
        "name": "Automated GDPR Compliance Workflow",
        "description": "Automatically create GDPR compliance requirements and collect evidence",
        "version": "1.0.0",
        "enabled": True,
        "trigger": {
            "type": "event",
            "parameters": {
                "eventType": "compliance_framework_added",
                "conditions": {
                    "framework": "GDPR"
                }
            }
        },
        "actions": [
            {
                "id": "create_data_subject_rights_requirement",
                "type": "create_requirement",
                "name": "Create Data Subject Rights Requirement",
                "description": "Create a requirement for implementing data subject rights",
                "parameters": {
                    "name": "Data Subject Rights",
                    "description": "Implement processes for handling data subject rights requests",
                    "framework": "GDPR",
                    "category": "privacy",
                    "priority": "high",
                    "status": "pending",
                    "due_date": (datetime.now() + timedelta(days=30)).isoformat(),
                    "assigned_to": "privacy_officer",
                    "tags": ["gdpr", "data_subject_rights", "privacy"]
                }
            },
            {
                "id": "create_dpia_requirement",
                "type": "create_requirement",
                "name": "Create DPIA Requirement",
                "description": "Create a requirement for conducting data protection impact assessments",
                "parameters": {
                    "name": "Data Protection Impact Assessment",
                    "description": "Conduct data protection impact assessments for high-risk processing",
                    "framework": "GDPR",
                    "category": "risk_assessment",
                    "priority": "medium",
                    "status": "pending",
                    "due_date": (datetime.now() + timedelta(days=45)).isoformat(),
                    "assigned_to": "privacy_officer",
                    "tags": ["gdpr", "dpia", "risk_assessment"]
                }
            },
            {
                "id": "create_breach_notification_requirement",
                "type": "create_requirement",
                "name": "Create Breach Notification Requirement",
                "description": "Create a requirement for implementing data breach notification processes",
                "parameters": {
                    "name": "Data Breach Notification",
                    "description": "Implement processes for notifying authorities of data breaches",
                    "framework": "GDPR",
                    "category": "incident_response",
                    "priority": "high",
                    "status": "pending",
                    "due_date": (datetime.now() + timedelta(days=15)).isoformat(),
                    "assigned_to": "security_officer",
                    "tags": ["gdpr", "breach_notification", "incident_response"]
                }
            },
            {
                "id": "collect_privacy_policy_evidence",
                "type": "collect_evidence",
                "name": "Collect Privacy Policy Evidence",
                "description": "Collect the privacy policy as evidence",
                "parameters": {
                    "name": "Privacy Policy",
                    "description": "Organization's privacy policy document",
                    "requirement_id": "${create_data_subject_rights_requirement.result.requirement.id}",
                    "type": "document",
                    "source": "website",
                    "collection_method": "automated",
                    "url": "https://example.com/privacy-policy"
                }
            }
        ],
        "variables": {
            "organization": "Example Organization",
            "privacy_officer": "John Doe",
            "security_officer": "Jane Smith"
        },
        "settings": {
            "timeout": 3600,
            "maxRetries": 3,
            "retryDelay": 60,
            "logLevel": "info"
        },
        "tags": ["gdpr", "automation", "compliance"],
        "created_by": "admin",
        "created_at": datetime.now().isoformat(),
        "updated_by": "admin",
        "updated_at": datetime.now().isoformat()
    }

    return workflow

def demonstrate_automation():
    """Demonstrate NovaFlowX (NUWO) - Universal Workflow Orchestrator."""
    logger.info("Demonstrating NovaFlowX (NUWO) - Universal Workflow Orchestrator")

    # Initialize the output directory
    output_dir = os.path.join(os.path.dirname(__file__), 'automation_output')
    os.makedirs(output_dir, exist_ok=True)

    # Step 1: Initialize the Tracking Manager
    logger.info("Step 1: Initializing the Tracking Manager")

    tracking_manager = TrackingManager()

    # Step 2: Initialize the Automation Manager
    logger.info("Step 2: Initializing the Automation Manager")

    automation_manager = AutomationManager(
        data_dir=output_dir,
        tracking_manager=tracking_manager
    )

    # Step 3: Register action handlers
    logger.info("Step 3: Registering action handlers")

    create_requirement_action = CreateRequirementAction(tracking_manager)
    collect_evidence_action = CollectEvidenceAction(tracking_manager)

    automation_manager.register_action('create_requirement', create_requirement_action)
    automation_manager.register_action('collect_evidence', collect_evidence_action)

    # Step 4: Register trigger handlers
    logger.info("Step 4: Registering trigger handlers")

    schedule_trigger = ScheduleTrigger(automation_manager)
    event_trigger = EventTrigger(automation_manager)

    automation_manager.register_trigger('schedule', schedule_trigger)
    automation_manager.register_trigger('event', event_trigger)

    # Step 5: Create a sample workflow
    logger.info("Step 5: Creating a sample workflow")

    workflow = create_sample_workflow()

    # Save the workflow to a file
    with open(os.path.join(output_dir, 'sample_workflow.json'), 'w', encoding='utf-8') as f:
        json.dump(workflow, f, indent=2)

    # Step 6: Register the workflow
    logger.info("Step 6: Registering the workflow")

    workflow_id = automation_manager.register_workflow(workflow)

    # Step 7: Register the workflow with the appropriate trigger
    logger.info("Step 7: Registering the workflow with the appropriate trigger")

    if workflow['trigger']['type'] == 'schedule':
        schedule_trigger.register_workflow(workflow)
    elif workflow['trigger']['type'] == 'event':
        event_trigger.register_workflow(workflow)

    # Step 8: Trigger the workflow
    logger.info("Step 8: Triggering the workflow")

    if workflow['trigger']['type'] == 'event':
        event_type = workflow['trigger']['parameters']['eventType']
        event_data = {
            'framework': 'GDPR',
            'organization': workflow['variables']['organization'],
            'timestamp': datetime.now().isoformat()
        }

        results = event_trigger.trigger_event(event_type, event_data)

        # Save the results to a file
        with open(os.path.join(output_dir, 'workflow_results.json'), 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)

    # Step 9: Get all workflows
    logger.info("Step 9: Getting all workflows")

    all_workflows = automation_manager.get_all_workflows()

    # Save the workflows to a file
    with open(os.path.join(output_dir, 'all_workflows.json'), 'w', encoding='utf-8') as f:
        json.dump(all_workflows, f, indent=2)

    # Step 10: Generate a summary report
    logger.info("Step 10: Generating a summary report")

    summary_report = {
        'timestamp': datetime.now().isoformat(),
        'workflows': len(all_workflows),
        'actions_registered': len(automation_manager.actions),
        'triggers_registered': len(automation_manager.triggers),
        'output_directory': output_dir
    }

    with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)

    logger.info("Automation demonstration completed")
    logger.info(f"Output files saved to: {output_dir}")

    return summary_report

def main():
    """Main function."""
    logger.info("Starting NovaFlowX (NUWO) - Universal Workflow Orchestrator demo")

    try:
        # Demonstrate the automation framework
        summary_report = demonstrate_automation()

        logger.info("NovaFlowX demo completed successfully")
        logger.info(f"Summary report: {summary_report}")
        logger.info(f"All output files are in: {os.path.join(os.path.dirname(os.path.abspath(__file__)), 'automation_output')}")

    except Exception as e:
        logger.error(f"Demo failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
