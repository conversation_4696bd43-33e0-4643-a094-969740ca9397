"""
Shared data model for test results.

This module provides a common TestResult class that can be used by both UCTF and UCVF.
"""

import json
import uuid
import datetime
from typing import Dict, List, Any, Optional


class TestResult:
    """
    Common data model for test results.

    This class represents the result of a compliance test and is designed to be
    used by both UCTF and UCVF.
    """

    def __init__(self,
                test_id: str,
                name: str,
                framework: str,
                status: str,
                score: float,
                findings: List[Dict[str, Any]],
                metadata: Optional[Dict[str, Any]] = None,
                result_id: Optional[str] = None,
                timestamp: Optional[str] = None):
        """
        Initialize a TestResult.

        Args:
            test_id: The ID of the test
            name: The name of the test
            framework: The compliance framework (e.g., 'GDPR', 'HIPAA', 'SOC2')
            status: The status of the test (e.g., 'passed', 'failed', 'error')
            score: The compliance score (0-100)
            findings: List of findings from the test
            metadata: Additional metadata about the test
            result_id: The ID of the test result (generated if not provided)
            timestamp: The timestamp of the test result (generated if not provided)
        """
        self.test_id = test_id
        self.name = name
        self.framework = framework
        self.status = status
        self.score = score
        self.findings = findings
        self.metadata = metadata or {}
        self.result_id = result_id or str(uuid.uuid4())
        self.timestamp = timestamp or datetime.datetime.now().isoformat()

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestResult':
        """
        Create a TestResult from a dictionary.

        Args:
            data: Dictionary containing test result data

        Returns:
            A TestResult instance
        """
        return cls(
            test_id=data.get('test_id', ''),
            name=data.get('name', ''),
            framework=data.get('framework', ''),
            status=data.get('status', ''),
            score=data.get('score', 0.0),
            findings=data.get('findings', []),
            metadata=data.get('metadata', {}),
            result_id=data.get('result_id', None),
            timestamp=data.get('timestamp', None)
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the TestResult to a dictionary.

        Returns:
            Dictionary representation of the TestResult
        """
        return {
            'result_id': self.result_id,
            'test_id': self.test_id,
            'name': self.name,
            'framework': self.framework,
            'status': self.status,
            'score': self.score,
            'findings': self.findings,
            'metadata': self.metadata,
            'timestamp': self.timestamp
        }

    def to_json(self) -> str:
        """
        Convert the TestResult to a JSON string.

        Returns:
            JSON string representation of the TestResult
        """
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> 'TestResult':
        """
        Create a TestResult from a JSON string.

        Args:
            json_str: JSON string containing test result data

        Returns:
            A TestResult instance
        """
        data = json.loads(json_str)
        return cls.from_dict(data)

    def is_passing(self) -> bool:
        """
        Check if the test is passing.

        Returns:
            True if the test is passing, False otherwise
        """
        return self.status.lower() == 'passed'

    def get_critical_findings(self) -> List[Dict[str, Any]]:
        """
        Get critical findings from the test.

        Returns:
            List of critical findings
        """
        return [f for f in self.findings if f.get('severity', '').lower() == 'critical']

    def get_high_findings(self) -> List[Dict[str, Any]]:
        """
        Get high severity findings from the test.

        Returns:
            List of high severity findings
        """
        return [f for f in self.findings if f.get('severity', '').lower() == 'high']

    def get_medium_findings(self) -> List[Dict[str, Any]]:
        """
        Get medium severity findings from the test.

        Returns:
            List of medium severity findings
        """
        return [f for f in self.findings if f.get('severity', '').lower() == 'medium']

    def get_low_findings(self) -> List[Dict[str, Any]]:
        """
        Get low severity findings from the test.

        Returns:
            List of low severity findings
        """
        return [f for f in self.findings if f.get('severity', '').lower() == 'low']
