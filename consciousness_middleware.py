#!/usr/bin/env python3
"""
Consciousness Filtering Middleware
NovaShield Ψκ Threshold Enforcement

Based on CHAEONIX CORE results:
- Low-Ψ (< 0.618): BLOCKED (403 FORBIDDEN)
- Mid-Ψ (0.618-1.0): LOGGED for review
- High-Ψ (> 1.0): DIVINE PRIORITY access
"""

from flask import Flask, request, jsonify, abort
import time
import json
from datetime import datetime

app = Flask(__name__)

# Consciousness thresholds
CONSCIOUSNESS_THRESHOLDS = {
    'BLOCK_THRESHOLD': 0.618,  # Golden ratio - below this gets blocked
    'REVIEW_THRESHOLD': 1.0,   # Below this gets logged for review
    'DIVINE_THRESHOLD': 2.0    # Above this gets divine priority
}

# Logging system
consciousness_logs = []

def log_consciousness_event(event_type, psi_level, action, details=""):
    """Log consciousness filtering events"""
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'event_type': event_type,
        'consciousness_level': psi_level,
        'action': action,
        'details': details,
        'source_ip': request.remote_addr if request else 'unknown'
    }
    consciousness_logs.append(log_entry)
    print(f"[CONSCIOUSNESS LOG] {event_type}: Ψ={psi_level} → {action}")
    return log_entry

@app.before_request
def consciousness_filter():
    """
    Consciousness-based request filtering middleware
    Implements CHAEONIX CORE consciousness validation
    """
    # Skip filtering for health and log endpoints
    if request.endpoint in ['health', 'consciousness_logs']:
        return
    
    # Extract consciousness level from headers
    psi_level_str = request.headers.get('X-Consciousness-Level', '0')
    
    try:
        psi_level = float(psi_level_str)
    except ValueError:
        log_consciousness_event(
            'INVALID_CONSCIOUSNESS', 
            psi_level_str, 
            'BLOCKED', 
            'Invalid consciousness level format'
        )
        abort(400, description="Invalid consciousness level format")
    
    # Apply consciousness filtering rules
    if psi_level < CONSCIOUSNESS_THRESHOLDS['BLOCK_THRESHOLD']:
        # Low consciousness - BLOCK
        log_consciousness_event(
            'THREAT_NEUTRALIZED', 
            psi_level, 
            'BLOCKED', 
            'Consciousness threshold violation'
        )
        abort(403, description="THREAT_NEUTRALIZED: Consciousness threshold violation")
    
    elif psi_level < CONSCIOUSNESS_THRESHOLDS['REVIEW_THRESHOLD']:
        # Mid consciousness - LOG for review
        log_consciousness_event(
            'CONSCIOUSNESS_REVIEW', 
            psi_level, 
            'LOGGED', 
            'Service accessible but flagged for review (Ψ < 1.0)'
        )
        # Continue processing but mark for review
        request.consciousness_status = 'REVIEW_REQUIRED'
    
    elif psi_level >= CONSCIOUSNESS_THRESHOLDS['DIVINE_THRESHOLD']:
        # Divine consciousness - PRIORITY
        log_consciousness_event(
            'ACCESS_GRANTED', 
            psi_level, 
            'DIVINE_PRIORITY', 
            'Trinity validation passed - Divine level access'
        )
        request.consciousness_status = 'DIVINE_PRIORITY'
    
    else:
        # High consciousness - NORMAL
        log_consciousness_event(
            'ACCESS_GRANTED', 
            psi_level, 
            'NORMAL_ACCESS', 
            'Trinity validation passed'
        )
        request.consciousness_status = 'NORMAL_ACCESS'

@app.route('/health')
def health():
    """Health check endpoint"""
    consciousness_level = request.headers.get('X-Consciousness-Level', 'unknown')
    status = getattr(request, 'consciousness_status', 'NO_FILTERING')
    
    response = {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'consciousness_level': consciousness_level,
        'access_status': status
    }
    
    # Add divine priority headers for high consciousness
    if status == 'DIVINE_PRIORITY':
        response['priority'] = 'DIVINE'
        response['message'] = 'Trinity validation passed - Divine access granted'
    elif status == 'REVIEW_REQUIRED':
        response['warning'] = 'Access logged for consciousness review'
    
    return jsonify(response)

@app.route('/consciousness-logs')
def get_consciousness_logs():
    """Get consciousness filtering logs"""
    return jsonify({
        'total_events': len(consciousness_logs),
        'recent_events': consciousness_logs[-10:],  # Last 10 events
        'thresholds': CONSCIOUSNESS_THRESHOLDS,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/consciousness-stats')
def consciousness_stats():
    """Get consciousness filtering statistics"""
    if not consciousness_logs:
        return jsonify({'message': 'No consciousness events logged yet'})
    
    # Calculate statistics
    total_events = len(consciousness_logs)
    blocked_events = len([log for log in consciousness_logs if log['action'] == 'BLOCKED'])
    divine_events = len([log for log in consciousness_logs if log['action'] == 'DIVINE_PRIORITY'])
    review_events = len([log for log in consciousness_logs if log['action'] == 'LOGGED'])
    
    stats = {
        'total_requests': total_events,
        'blocked_requests': blocked_events,
        'divine_priority_requests': divine_events,
        'review_flagged_requests': review_events,
        'block_rate': (blocked_events / total_events * 100) if total_events > 0 else 0,
        'divine_rate': (divine_events / total_events * 100) if total_events > 0 else 0,
        'consciousness_thresholds': CONSCIOUSNESS_THRESHOLDS,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(stats)

@app.route('/test-consciousness')
def test_consciousness():
    """Test endpoint for consciousness validation"""
    test_levels = [0.12, 0.52, 0.82, 1.5, 2.847]
    results = []
    
    for psi in test_levels:
        if psi < CONSCIOUSNESS_THRESHOLDS['BLOCK_THRESHOLD']:
            expected_action = 'BLOCKED'
        elif psi < CONSCIOUSNESS_THRESHOLDS['REVIEW_THRESHOLD']:
            expected_action = 'LOGGED'
        elif psi >= CONSCIOUSNESS_THRESHOLDS['DIVINE_THRESHOLD']:
            expected_action = 'DIVINE_PRIORITY'
        else:
            expected_action = 'NORMAL_ACCESS'
        
        results.append({
            'consciousness_level': psi,
            'expected_action': expected_action,
            'threshold_category': 'LOW' if psi < 0.618 else 'MID' if psi < 1.0 else 'HIGH' if psi < 2.0 else 'DIVINE'
        })
    
    return jsonify({
        'test_cases': results,
        'thresholds': CONSCIOUSNESS_THRESHOLDS,
        'message': 'Use these consciousness levels in X-Consciousness-Level header to test filtering'
    })

if __name__ == '__main__':
    print("🚀 Starting Consciousness Filtering Middleware...")
    print("⚛️ NovaShield Ψκ Threshold Enforcement Active")
    print("="*60)
    print(f"🔒 Block Threshold: Ψ < {CONSCIOUSNESS_THRESHOLDS['BLOCK_THRESHOLD']}")
    print(f"⚠️ Review Threshold: Ψ < {CONSCIOUSNESS_THRESHOLDS['REVIEW_THRESHOLD']}")
    print(f"🌟 Divine Threshold: Ψ ≥ {CONSCIOUSNESS_THRESHOLDS['DIVINE_THRESHOLD']}")
    print("="*60)
    
    app.run(host='0.0.0.0', port=5000, debug=True)
