{"version": 3, "names": ["ServiceUsageClient", "require", "CloudBillingClient", "<PERSON><PERSON><PERSON><PERSON>", "v4", "uuidv4", "fs", "promises", "path", "logger", "FeatureService", "ValidationError", "NotFoundError", "BillingService", "constructor", "dataDir", "join", "__dirname", "entitlementsFile", "usageFile", "featureService", "process", "env", "GCP_PROJECT_ID", "serviceUsageClient", "cloudBillingClient", "big<PERSON>y", "projectId", "info", "error", "message", "ensureDataDir", "mkdir", "recursive", "access", "code", "writeFile", "JSON", "stringify", "loadEntitlements", "data", "readFile", "parse", "saveEntitlements", "entitlements", "loadUsage", "saveUsage", "usage", "enableFeatures", "customerId", "entitlement", "status", "createdAt", "Date", "toISOString", "updatedAt", "tier", "plan", "enableFeaturesForTier", "dataset", "table", "insert", "customer_id", "entitlement_id", "id", "created_at", "event_type", "updateFeatures", "warn", "currentTier", "newTier", "updated_at", "disableFeatures", "deletedAt", "deleted_at", "activateFeatures", "suspendFeatures", "getCustomerEntitlements", "getCustomerUsage", "startDate", "endDate", "start", "now", "end", "customerUsage", "filteredUsage", "Object", "entries", "filter", "date", "usageDate", "reduce", "acc", "metrics", "totals", "values", "for<PERSON>ach", "metric", "value", "reportUsage", "metricName", "quantity", "timestamp", "tenantId", "split", "GCP_BILLING_ACCOUNT", "reportRequest", "parent", "resourceName", "usageMetric", "usageValue", "labels", "tenant_id", "reportError", "metric_name", "reportTenantUsage", "module", "exports"], "sources": ["BillingService.js"], "sourcesContent": ["/**\n * NovaConnect UAC Billing Service\n *\n * This service handles billing-related functionality, including\n * GCP Marketplace entitlements and usage reporting.\n */\n\nconst { ServiceUsageClient } = require('@google-cloud/service-usage');\nconst { CloudBillingClient } = require('@google-cloud/billing');\nconst { BigQuery } = require('@google-cloud/bigquery');\nconst { v4: uuidv4 } = require('uuid');\nconst fs = require('fs').promises;\nconst path = require('path');\nconst logger = require('../../config/logger');\nconst FeatureService = require('./FeatureService');\nconst { ValidationError, NotFoundError } = require('../utils/errors');\n\nclass BillingService {\n  constructor() {\n    this.dataDir = path.join(__dirname, '../../data/billing');\n    this.entitlementsFile = path.join(this.dataDir, 'entitlements.json');\n    this.usageFile = path.join(this.dataDir, 'usage.json');\n    this.featureService = new FeatureService();\n\n    // Initialize Google Cloud clients if available\n    try {\n      if (process.env.GCP_PROJECT_ID) {\n        this.serviceUsageClient = new ServiceUsageClient();\n        this.cloudBillingClient = new CloudBillingClient();\n        this.bigquery = new BigQuery({\n          projectId: process.env.GCP_PROJECT_ID\n        });\n        logger.info('Initialized Google Cloud clients for billing');\n      }\n    } catch (error) {\n      logger.error('Error initializing Google Cloud clients for billing', { error: error.message });\n    }\n\n    this.ensureDataDir();\n  }\n\n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.dataDir, { recursive: true });\n\n      // Initialize entitlements file if it doesn't exist\n      try {\n        await fs.access(this.entitlementsFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.entitlementsFile, JSON.stringify({}));\n        } else {\n          throw error;\n        }\n      }\n\n      // Initialize usage file if it doesn't exist\n      try {\n        await fs.access(this.usageFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.usageFile, JSON.stringify({}));\n        } else {\n          throw error;\n        }\n      }\n    } catch (error) {\n      logger.error('Error ensuring billing data directory', { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * Load entitlements from file\n   */\n  async loadEntitlements() {\n    try {\n      const data = await fs.readFile(this.entitlementsFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      logger.error('Error loading entitlements', { error: error.message });\n      return {};\n    }\n  }\n\n  /**\n   * Save entitlements to file\n   */\n  async saveEntitlements(entitlements) {\n    try {\n      await fs.writeFile(this.entitlementsFile, JSON.stringify(entitlements, null, 2));\n    } catch (error) {\n      logger.error('Error saving entitlements', { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * Load usage from file\n   */\n  async loadUsage() {\n    try {\n      const data = await fs.readFile(this.usageFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      logger.error('Error loading usage', { error: error.message });\n      return {};\n    }\n  }\n\n  /**\n   * Save usage to file\n   */\n  async saveUsage(usage) {\n    try {\n      await fs.writeFile(this.usageFile, JSON.stringify(usage, null, 2));\n    } catch (error) {\n      logger.error('Error saving usage', { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * Enable features for a customer\n   */\n  async enableFeatures(customerId, entitlement) {\n    try {\n      logger.info('Enabling features for customer', { customerId, entitlement });\n\n      // Load current entitlements\n      const entitlements = await this.loadEntitlements();\n\n      // Add or update entitlement\n      entitlements[customerId] = {\n        ...entitlement,\n        status: 'ACTIVE',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      // Save entitlements\n      await this.saveEntitlements(entitlements);\n\n      // Enable features based on entitlement\n      const tier = entitlement.plan || 'core';\n      await this.featureService.enableFeaturesForTier(customerId, tier);\n\n      // Log to BigQuery if available\n      if (this.bigquery) {\n        const dataset = this.bigquery.dataset('billing');\n        const table = dataset.table('entitlements');\n\n        await table.insert([{\n          customer_id: customerId,\n          entitlement_id: entitlement.id || `entitlement-${customerId}`,\n          plan: tier,\n          status: 'ACTIVE',\n          created_at: new Date().toISOString(),\n          event_type: 'ENTITLEMENT_CREATED'\n        }]);\n      }\n\n      logger.info('Features enabled for customer', { customerId, tier });\n    } catch (error) {\n      logger.error('Error enabling features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Update features for a customer\n   */\n  async updateFeatures(customerId, entitlement) {\n    try {\n      logger.info('Updating features for customer', { customerId, entitlement });\n\n      // Load current entitlements\n      const entitlements = await this.loadEntitlements();\n\n      // Check if entitlement exists\n      if (!entitlements[customerId]) {\n        logger.warn('Entitlement not found for customer', { customerId });\n        return this.enableFeatures(customerId, entitlement);\n      }\n\n      // Get current tier\n      const currentTier = entitlements[customerId].plan || 'core';\n\n      // Update entitlement\n      entitlements[customerId] = {\n        ...entitlements[customerId],\n        ...entitlement,\n        updatedAt: new Date().toISOString()\n      };\n\n      // Save entitlements\n      await this.saveEntitlements(entitlements);\n\n      // Update features if tier changed\n      const newTier = entitlement.plan || currentTier;\n      if (newTier !== currentTier) {\n        await this.featureService.enableFeaturesForTier(customerId, newTier);\n      }\n\n      // Log to BigQuery if available\n      if (this.bigquery) {\n        const dataset = this.bigquery.dataset('billing');\n        const table = dataset.table('entitlements');\n\n        await table.insert([{\n          customer_id: customerId,\n          entitlement_id: entitlement.id || `entitlement-${customerId}`,\n          plan: newTier,\n          status: entitlements[customerId].status,\n          updated_at: new Date().toISOString(),\n          event_type: 'ENTITLEMENT_UPDATED'\n        }]);\n      }\n\n      logger.info('Features updated for customer', { customerId, tier: newTier });\n    } catch (error) {\n      logger.error('Error updating features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Disable features for a customer\n   */\n  async disableFeatures(customerId, entitlement) {\n    try {\n      logger.info('Disabling features for customer', { customerId });\n\n      // Load current entitlements\n      const entitlements = await this.loadEntitlements();\n\n      // Check if entitlement exists\n      if (!entitlements[customerId]) {\n        logger.warn('Entitlement not found for customer', { customerId });\n        return;\n      }\n\n      // Update entitlement status\n      entitlements[customerId] = {\n        ...entitlements[customerId],\n        status: 'DELETED',\n        updatedAt: new Date().toISOString(),\n        deletedAt: new Date().toISOString()\n      };\n\n      // Save entitlements\n      await this.saveEntitlements(entitlements);\n\n      // Disable features\n      await this.featureService.disableFeatures(customerId);\n\n      // Log to BigQuery if available\n      if (this.bigquery) {\n        const dataset = this.bigquery.dataset('billing');\n        const table = dataset.table('entitlements');\n\n        await table.insert([{\n          customer_id: customerId,\n          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,\n          plan: entitlements[customerId].plan,\n          status: 'DELETED',\n          updated_at: new Date().toISOString(),\n          deleted_at: new Date().toISOString(),\n          event_type: 'ENTITLEMENT_DELETED'\n        }]);\n      }\n\n      logger.info('Features disabled for customer', { customerId });\n    } catch (error) {\n      logger.error('Error disabling features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Activate features for a customer\n   */\n  async activateFeatures(customerId, entitlement) {\n    try {\n      logger.info('Activating features for customer', { customerId });\n\n      // Load current entitlements\n      const entitlements = await this.loadEntitlements();\n\n      // Check if entitlement exists\n      if (!entitlements[customerId]) {\n        logger.warn('Entitlement not found for customer', { customerId });\n        return this.enableFeatures(customerId, entitlement);\n      }\n\n      // Update entitlement status\n      entitlements[customerId] = {\n        ...entitlements[customerId],\n        status: 'ACTIVE',\n        updatedAt: new Date().toISOString()\n      };\n\n      // Save entitlements\n      await this.saveEntitlements(entitlements);\n\n      // Enable features\n      const tier = entitlements[customerId].plan || 'core';\n      await this.featureService.enableFeaturesForTier(customerId, tier);\n\n      // Log to BigQuery if available\n      if (this.bigquery) {\n        const dataset = this.bigquery.dataset('billing');\n        const table = dataset.table('entitlements');\n\n        await table.insert([{\n          customer_id: customerId,\n          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,\n          plan: tier,\n          status: 'ACTIVE',\n          updated_at: new Date().toISOString(),\n          event_type: 'ENTITLEMENT_ACTIVATED'\n        }]);\n      }\n\n      logger.info('Features activated for customer', { customerId, tier });\n    } catch (error) {\n      logger.error('Error activating features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Suspend features for a customer\n   */\n  async suspendFeatures(customerId, entitlement) {\n    try {\n      logger.info('Suspending features for customer', { customerId });\n\n      // Load current entitlements\n      const entitlements = await this.loadEntitlements();\n\n      // Check if entitlement exists\n      if (!entitlements[customerId]) {\n        logger.warn('Entitlement not found for customer', { customerId });\n        return;\n      }\n\n      // Update entitlement status\n      entitlements[customerId] = {\n        ...entitlements[customerId],\n        status: 'SUSPENDED',\n        updatedAt: new Date().toISOString()\n      };\n\n      // Save entitlements\n      await this.saveEntitlements(entitlements);\n\n      // Suspend features\n      await this.featureService.suspendFeatures(customerId);\n\n      // Log to BigQuery if available\n      if (this.bigquery) {\n        const dataset = this.bigquery.dataset('billing');\n        const table = dataset.table('entitlements');\n\n        await table.insert([{\n          customer_id: customerId,\n          entitlement_id: entitlement?.id || entitlements[customerId].id || `entitlement-${customerId}`,\n          plan: entitlements[customerId].plan,\n          status: 'SUSPENDED',\n          updated_at: new Date().toISOString(),\n          event_type: 'ENTITLEMENT_SUSPENDED'\n        }]);\n      }\n\n      logger.info('Features suspended for customer', { customerId });\n    } catch (error) {\n      logger.error('Error suspending features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Get customer entitlements\n   */\n  async getCustomerEntitlements(customerId) {\n    try {\n      // Load entitlements\n      const entitlements = await this.loadEntitlements();\n\n      // Return customer entitlement or empty object\n      return entitlements[customerId] || { status: 'NOT_FOUND' };\n    } catch (error) {\n      logger.error('Error getting customer entitlements', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Get customer usage\n   */\n  async getCustomerUsage(customerId, startDate, endDate) {\n    try {\n      // Parse dates\n      const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to last 30 days\n      const end = endDate ? new Date(endDate) : new Date();\n\n      // Load usage\n      const usage = await this.loadUsage();\n\n      // Get customer usage\n      const customerUsage = usage[customerId] || {};\n\n      // Filter by date range\n      const filteredUsage = Object.entries(customerUsage)\n        .filter(([date]) => {\n          const usageDate = new Date(date);\n          return usageDate >= start && usageDate <= end;\n        })\n        .reduce((acc, [date, metrics]) => {\n          acc[date] = metrics;\n          return acc;\n        }, {});\n\n      // Calculate totals\n      const totals = {};\n      Object.values(filteredUsage).forEach(metrics => {\n        Object.entries(metrics).forEach(([metric, value]) => {\n          totals[metric] = (totals[metric] || 0) + value;\n        });\n      });\n\n      return {\n        customerId,\n        startDate: start.toISOString(),\n        endDate: end.toISOString(),\n        usage: filteredUsage,\n        totals\n      };\n    } catch (error) {\n      logger.error('Error getting customer usage', { error: error.message, customerId });\n      throw error;\n    }\n  }\n\n  /**\n   * Report usage\n   */\n  async reportUsage(customerId, metricName, quantity, timestamp = new Date().toISOString(), tenantId = null) {\n    try {\n      logger.info('Reporting usage', { customerId, metricName, quantity, tenantId });\n\n      // Load usage\n      const usage = await this.loadUsage();\n\n      // Initialize customer usage if needed\n      if (!usage[customerId]) {\n        usage[customerId] = {};\n      }\n\n      // Get date (YYYY-MM-DD)\n      const date = timestamp.split('T')[0];\n\n      // Initialize date usage if needed\n      if (!usage[customerId][date]) {\n        usage[customerId][date] = {};\n      }\n\n      // Update usage\n      usage[customerId][date][metricName] = (usage[customerId][date][metricName] || 0) + quantity;\n\n      // Save usage\n      await this.saveUsage(usage);\n\n      // Report to GCP Marketplace if available\n      if (this.cloudBillingClient && process.env.GCP_BILLING_ACCOUNT) {\n        // Implement GCP Marketplace usage reporting for single-tenant architecture\n        if (tenantId) {\n          try {\n            // For single-tenant architecture, we need to include the tenant ID\n            const reportRequest = {\n              parent: `projects/${process.env.GCP_PROJECT_ID}`,\n              resourceName: `projects/${process.env.GCP_PROJECT_ID}/services/novafuse.googleapis.com`,\n              usageMetric: metricName,\n              usageValue: quantity,\n              timestamp: timestamp,\n              labels: {\n                tenant_id: tenantId\n              }\n            };\n\n            // Report usage to GCP Marketplace\n            await this.cloudBillingClient.reportUsage(reportRequest);\n            logger.info('GCP Marketplace usage reported for tenant', { tenantId, metricName, quantity });\n          } catch (reportError) {\n            logger.error('Error reporting usage to GCP Marketplace', {\n              error: reportError.message,\n              tenantId,\n              customerId,\n              metricName\n            });\n          }\n        } else {\n          logger.warn('Tenant ID not provided for GCP Marketplace usage reporting');\n        }\n      }\n\n      // Log to BigQuery if available\n      if (this.bigquery) {\n        const dataset = this.bigquery.dataset('billing');\n        const table = dataset.table('usage');\n\n        await table.insert([{\n          customer_id: customerId,\n          tenant_id: tenantId,\n          metric_name: metricName,\n          quantity: quantity,\n          timestamp: timestamp,\n          date: date\n        }]);\n      }\n\n      logger.info('Usage reported', { customerId, tenantId, metricName, quantity });\n    } catch (error) {\n      logger.error('Error reporting usage', { error: error.message, customerId, tenantId, metricName });\n      throw error;\n    }\n  }\n\n  /**\n   * Report tenant-specific usage\n   */\n  async reportTenantUsage(tenantId, metricName, quantity, timestamp = new Date().toISOString()) {\n    try {\n      // For tenant-specific usage, we use the tenant ID as the customer ID\n      // This ensures proper isolation in our usage tracking\n      const customerId = `tenant-${tenantId}`;\n\n      // Report usage with tenant ID\n      return this.reportUsage(customerId, metricName, quantity, timestamp, tenantId);\n    } catch (error) {\n      logger.error('Error reporting tenant usage', { error: error.message, tenantId, metricName });\n      throw error;\n    }\n  }\n}\n\nmodule.exports = BillingService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAmB,CAAC,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AACrE,MAAM;EAAEC;AAAmB,CAAC,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC/D,MAAM;EAAEE;AAAS,CAAC,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AACtD,MAAM;EAAEG,EAAE,EAAEC;AAAO,CAAC,GAAGJ,OAAO,CAAC,MAAM,CAAC;AACtC,MAAMK,EAAE,GAAGL,OAAO,CAAC,IAAI,CAAC,CAACM,QAAQ;AACjC,MAAMC,IAAI,GAAGP,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMQ,MAAM,GAAGR,OAAO,CAAC,qBAAqB,CAAC;AAC7C,MAAMS,cAAc,GAAGT,OAAO,CAAC,kBAAkB,CAAC;AAClD,MAAM;EAAEU,eAAe;EAAEC;AAAc,CAAC,GAAGX,OAAO,CAAC,iBAAiB,CAAC;AAErE,MAAMY,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGP,IAAI,CAACQ,IAAI,CAACC,SAAS,EAAE,oBAAoB,CAAC;IACzD,IAAI,CAACC,gBAAgB,GAAGV,IAAI,CAACQ,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,mBAAmB,CAAC;IACpE,IAAI,CAACI,SAAS,GAAGX,IAAI,CAACQ,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,YAAY,CAAC;IACtD,IAAI,CAACK,cAAc,GAAG,IAAIV,cAAc,CAAC,CAAC;;IAE1C;IACA,IAAI;MACF,IAAIW,OAAO,CAACC,GAAG,CAACC,cAAc,EAAE;QAC9B,IAAI,CAACC,kBAAkB,GAAG,IAAIxB,kBAAkB,CAAC,CAAC;QAClD,IAAI,CAACyB,kBAAkB,GAAG,IAAIvB,kBAAkB,CAAC,CAAC;QAClD,IAAI,CAACwB,QAAQ,GAAG,IAAIvB,QAAQ,CAAC;UAC3BwB,SAAS,EAAEN,OAAO,CAACC,GAAG,CAACC;QACzB,CAAC,CAAC;QACFd,MAAM,CAACmB,IAAI,CAAC,8CAA8C,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,qDAAqD,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;IAC/F;IAEA,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACE,MAAMA,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMzB,EAAE,CAAC0B,KAAK,CAAC,IAAI,CAACjB,OAAO,EAAE;QAAEkB,SAAS,EAAE;MAAK,CAAC,CAAC;;MAEjD;MACA,IAAI;QACF,MAAM3B,EAAE,CAAC4B,MAAM,CAAC,IAAI,CAAChB,gBAAgB,CAAC;MACxC,CAAC,CAAC,OAAOW,KAAK,EAAE;QACd,IAAIA,KAAK,CAACM,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAM7B,EAAE,CAAC8B,SAAS,CAAC,IAAI,CAAClB,gBAAgB,EAAEmB,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,CAAC,MAAM;UACL,MAAMT,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMvB,EAAE,CAAC4B,MAAM,CAAC,IAAI,CAACf,SAAS,CAAC;MACjC,CAAC,CAAC,OAAOU,KAAK,EAAE;QACd,IAAIA,KAAK,CAACM,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAM7B,EAAE,CAAC8B,SAAS,CAAC,IAAI,CAACjB,SAAS,EAAEkB,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,CAAC,MAAM;UACL,MAAMT,KAAK;QACb;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,uCAAuC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;MAC/E,MAAMD,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMU,gBAAgBA,CAAA,EAAG;IACvB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMlC,EAAE,CAACmC,QAAQ,CAAC,IAAI,CAACvB,gBAAgB,EAAE,MAAM,CAAC;MAC7D,OAAOmB,IAAI,CAACK,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,4BAA4B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;MACpE,OAAO,CAAC,CAAC;IACX;EACF;;EAEA;AACF;AACA;EACE,MAAMa,gBAAgBA,CAACC,YAAY,EAAE;IACnC,IAAI;MACF,MAAMtC,EAAE,CAAC8B,SAAS,CAAC,IAAI,CAAClB,gBAAgB,EAAEmB,IAAI,CAACC,SAAS,CAACM,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAClF,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,2BAA2B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;MACnE,MAAMD,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgB,SAASA,CAAA,EAAG;IAChB,IAAI;MACF,MAAML,IAAI,GAAG,MAAMlC,EAAE,CAACmC,QAAQ,CAAC,IAAI,CAACtB,SAAS,EAAE,MAAM,CAAC;MACtD,OAAOkB,IAAI,CAACK,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,qBAAqB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;MAC7D,OAAO,CAAC,CAAC;IACX;EACF;;EAEA;AACF;AACA;EACE,MAAMgB,SAASA,CAACC,KAAK,EAAE;IACrB,IAAI;MACF,MAAMzC,EAAE,CAAC8B,SAAS,CAAC,IAAI,CAACjB,SAAS,EAAEkB,IAAI,CAACC,SAAS,CAACS,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,oBAAoB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;MAC5D,MAAMD,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMmB,cAAcA,CAACC,UAAU,EAAEC,WAAW,EAAE;IAC5C,IAAI;MACFzC,MAAM,CAACmB,IAAI,CAAC,gCAAgC,EAAE;QAAEqB,UAAU;QAAEC;MAAY,CAAC,CAAC;;MAE1E;MACA,MAAMN,YAAY,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAAC,CAAC;;MAElD;MACAK,YAAY,CAACK,UAAU,CAAC,GAAG;QACzB,GAAGC,WAAW;QACdC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACX,gBAAgB,CAACC,YAAY,CAAC;;MAEzC;MACA,MAAMY,IAAI,GAAGN,WAAW,CAACO,IAAI,IAAI,MAAM;MACvC,MAAM,IAAI,CAACrC,cAAc,CAACsC,qBAAqB,CAACT,UAAU,EAAEO,IAAI,CAAC;;MAEjE;MACA,IAAI,IAAI,CAAC9B,QAAQ,EAAE;QACjB,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC;QAE3C,MAAMA,KAAK,CAACC,MAAM,CAAC,CAAC;UAClBC,WAAW,EAAEb,UAAU;UACvBc,cAAc,EAAEb,WAAW,CAACc,EAAE,IAAI,eAAef,UAAU,EAAE;UAC7DQ,IAAI,EAAED,IAAI;UACVL,MAAM,EAAE,QAAQ;UAChBc,UAAU,EAAE,IAAIZ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCY,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;MACL;MAEAzD,MAAM,CAACmB,IAAI,CAAC,+BAA+B,EAAE;QAAEqB,UAAU;QAAEO;MAAK,CAAC,CAAC;IACpE,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,yBAAyB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MAC7E,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsC,cAAcA,CAAClB,UAAU,EAAEC,WAAW,EAAE;IAC5C,IAAI;MACFzC,MAAM,CAACmB,IAAI,CAAC,gCAAgC,EAAE;QAAEqB,UAAU;QAAEC;MAAY,CAAC,CAAC;;MAE1E;MACA,MAAMN,YAAY,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAAC,CAAC;;MAElD;MACA,IAAI,CAACK,YAAY,CAACK,UAAU,CAAC,EAAE;QAC7BxC,MAAM,CAAC2D,IAAI,CAAC,oCAAoC,EAAE;UAAEnB;QAAW,CAAC,CAAC;QACjE,OAAO,IAAI,CAACD,cAAc,CAACC,UAAU,EAAEC,WAAW,CAAC;MACrD;;MAEA;MACA,MAAMmB,WAAW,GAAGzB,YAAY,CAACK,UAAU,CAAC,CAACQ,IAAI,IAAI,MAAM;;MAE3D;MACAb,YAAY,CAACK,UAAU,CAAC,GAAG;QACzB,GAAGL,YAAY,CAACK,UAAU,CAAC;QAC3B,GAAGC,WAAW;QACdK,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACX,gBAAgB,CAACC,YAAY,CAAC;;MAEzC;MACA,MAAM0B,OAAO,GAAGpB,WAAW,CAACO,IAAI,IAAIY,WAAW;MAC/C,IAAIC,OAAO,KAAKD,WAAW,EAAE;QAC3B,MAAM,IAAI,CAACjD,cAAc,CAACsC,qBAAqB,CAACT,UAAU,EAAEqB,OAAO,CAAC;MACtE;;MAEA;MACA,IAAI,IAAI,CAAC5C,QAAQ,EAAE;QACjB,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC;QAE3C,MAAMA,KAAK,CAACC,MAAM,CAAC,CAAC;UAClBC,WAAW,EAAEb,UAAU;UACvBc,cAAc,EAAEb,WAAW,CAACc,EAAE,IAAI,eAAef,UAAU,EAAE;UAC7DQ,IAAI,EAAEa,OAAO;UACbnB,MAAM,EAAEP,YAAY,CAACK,UAAU,CAAC,CAACE,MAAM;UACvCoB,UAAU,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCY,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;MACL;MAEAzD,MAAM,CAACmB,IAAI,CAAC,+BAA+B,EAAE;QAAEqB,UAAU;QAAEO,IAAI,EAAEc;MAAQ,CAAC,CAAC;IAC7E,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,yBAAyB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MAC7E,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM2C,eAAeA,CAACvB,UAAU,EAAEC,WAAW,EAAE;IAC7C,IAAI;MACFzC,MAAM,CAACmB,IAAI,CAAC,iCAAiC,EAAE;QAAEqB;MAAW,CAAC,CAAC;;MAE9D;MACA,MAAML,YAAY,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAAC,CAAC;;MAElD;MACA,IAAI,CAACK,YAAY,CAACK,UAAU,CAAC,EAAE;QAC7BxC,MAAM,CAAC2D,IAAI,CAAC,oCAAoC,EAAE;UAAEnB;QAAW,CAAC,CAAC;QACjE;MACF;;MAEA;MACAL,YAAY,CAACK,UAAU,CAAC,GAAG;QACzB,GAAGL,YAAY,CAACK,UAAU,CAAC;QAC3BE,MAAM,EAAE,SAAS;QACjBI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCmB,SAAS,EAAE,IAAIpB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACX,gBAAgB,CAACC,YAAY,CAAC;;MAEzC;MACA,MAAM,IAAI,CAACxB,cAAc,CAACoD,eAAe,CAACvB,UAAU,CAAC;;MAErD;MACA,IAAI,IAAI,CAACvB,QAAQ,EAAE;QACjB,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC;QAE3C,MAAMA,KAAK,CAACC,MAAM,CAAC,CAAC;UAClBC,WAAW,EAAEb,UAAU;UACvBc,cAAc,EAAEb,WAAW,EAAEc,EAAE,IAAIpB,YAAY,CAACK,UAAU,CAAC,CAACe,EAAE,IAAI,eAAef,UAAU,EAAE;UAC7FQ,IAAI,EAAEb,YAAY,CAACK,UAAU,CAAC,CAACQ,IAAI;UACnCN,MAAM,EAAE,SAAS;UACjBoB,UAAU,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCoB,UAAU,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCY,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;MACL;MAEAzD,MAAM,CAACmB,IAAI,CAAC,gCAAgC,EAAE;QAAEqB;MAAW,CAAC,CAAC;IAC/D,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,0BAA0B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MAC9E,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8C,gBAAgBA,CAAC1B,UAAU,EAAEC,WAAW,EAAE;IAC9C,IAAI;MACFzC,MAAM,CAACmB,IAAI,CAAC,kCAAkC,EAAE;QAAEqB;MAAW,CAAC,CAAC;;MAE/D;MACA,MAAML,YAAY,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAAC,CAAC;;MAElD;MACA,IAAI,CAACK,YAAY,CAACK,UAAU,CAAC,EAAE;QAC7BxC,MAAM,CAAC2D,IAAI,CAAC,oCAAoC,EAAE;UAAEnB;QAAW,CAAC,CAAC;QACjE,OAAO,IAAI,CAACD,cAAc,CAACC,UAAU,EAAEC,WAAW,CAAC;MACrD;;MAEA;MACAN,YAAY,CAACK,UAAU,CAAC,GAAG;QACzB,GAAGL,YAAY,CAACK,UAAU,CAAC;QAC3BE,MAAM,EAAE,QAAQ;QAChBI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACX,gBAAgB,CAACC,YAAY,CAAC;;MAEzC;MACA,MAAMY,IAAI,GAAGZ,YAAY,CAACK,UAAU,CAAC,CAACQ,IAAI,IAAI,MAAM;MACpD,MAAM,IAAI,CAACrC,cAAc,CAACsC,qBAAqB,CAACT,UAAU,EAAEO,IAAI,CAAC;;MAEjE;MACA,IAAI,IAAI,CAAC9B,QAAQ,EAAE;QACjB,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC;QAE3C,MAAMA,KAAK,CAACC,MAAM,CAAC,CAAC;UAClBC,WAAW,EAAEb,UAAU;UACvBc,cAAc,EAAEb,WAAW,EAAEc,EAAE,IAAIpB,YAAY,CAACK,UAAU,CAAC,CAACe,EAAE,IAAI,eAAef,UAAU,EAAE;UAC7FQ,IAAI,EAAED,IAAI;UACVL,MAAM,EAAE,QAAQ;UAChBoB,UAAU,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCY,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;MACL;MAEAzD,MAAM,CAACmB,IAAI,CAAC,iCAAiC,EAAE;QAAEqB,UAAU;QAAEO;MAAK,CAAC,CAAC;IACtE,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,2BAA2B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MAC/E,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM+C,eAAeA,CAAC3B,UAAU,EAAEC,WAAW,EAAE;IAC7C,IAAI;MACFzC,MAAM,CAACmB,IAAI,CAAC,kCAAkC,EAAE;QAAEqB;MAAW,CAAC,CAAC;;MAE/D;MACA,MAAML,YAAY,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAAC,CAAC;;MAElD;MACA,IAAI,CAACK,YAAY,CAACK,UAAU,CAAC,EAAE;QAC7BxC,MAAM,CAAC2D,IAAI,CAAC,oCAAoC,EAAE;UAAEnB;QAAW,CAAC,CAAC;QACjE;MACF;;MAEA;MACAL,YAAY,CAACK,UAAU,CAAC,GAAG;QACzB,GAAGL,YAAY,CAACK,UAAU,CAAC;QAC3BE,MAAM,EAAE,WAAW;QACnBI,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACX,gBAAgB,CAACC,YAAY,CAAC;;MAEzC;MACA,MAAM,IAAI,CAACxB,cAAc,CAACwD,eAAe,CAAC3B,UAAU,CAAC;;MAErD;MACA,IAAI,IAAI,CAACvB,QAAQ,EAAE;QACjB,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,cAAc,CAAC;QAE3C,MAAMA,KAAK,CAACC,MAAM,CAAC,CAAC;UAClBC,WAAW,EAAEb,UAAU;UACvBc,cAAc,EAAEb,WAAW,EAAEc,EAAE,IAAIpB,YAAY,CAACK,UAAU,CAAC,CAACe,EAAE,IAAI,eAAef,UAAU,EAAE;UAC7FQ,IAAI,EAAEb,YAAY,CAACK,UAAU,CAAC,CAACQ,IAAI;UACnCN,MAAM,EAAE,WAAW;UACnBoB,UAAU,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACpCY,UAAU,EAAE;QACd,CAAC,CAAC,CAAC;MACL;MAEAzD,MAAM,CAACmB,IAAI,CAAC,iCAAiC,EAAE;QAAEqB;MAAW,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,2BAA2B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MAC/E,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgD,uBAAuBA,CAAC5B,UAAU,EAAE;IACxC,IAAI;MACF;MACA,MAAML,YAAY,GAAG,MAAM,IAAI,CAACL,gBAAgB,CAAC,CAAC;;MAElD;MACA,OAAOK,YAAY,CAACK,UAAU,CAAC,IAAI;QAAEE,MAAM,EAAE;MAAY,CAAC;IAC5D,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,qCAAqC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MACzF,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiD,gBAAgBA,CAAC7B,UAAU,EAAE8B,SAAS,EAAEC,OAAO,EAAE;IACrD,IAAI;MACF;MACA,MAAMC,KAAK,GAAGF,SAAS,GAAG,IAAI1B,IAAI,CAAC0B,SAAS,CAAC,GAAG,IAAI1B,IAAI,CAACA,IAAI,CAAC6B,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;MACjG,MAAMC,GAAG,GAAGH,OAAO,GAAG,IAAI3B,IAAI,CAAC2B,OAAO,CAAC,GAAG,IAAI3B,IAAI,CAAC,CAAC;;MAEpD;MACA,MAAMN,KAAK,GAAG,MAAM,IAAI,CAACF,SAAS,CAAC,CAAC;;MAEpC;MACA,MAAMuC,aAAa,GAAGrC,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC,CAAC;;MAE7C;MACA,MAAMoC,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACH,aAAa,CAAC,CAChDI,MAAM,CAAC,CAAC,CAACC,IAAI,CAAC,KAAK;QAClB,MAAMC,SAAS,GAAG,IAAIrC,IAAI,CAACoC,IAAI,CAAC;QAChC,OAAOC,SAAS,IAAIT,KAAK,IAAIS,SAAS,IAAIP,GAAG;MAC/C,CAAC,CAAC,CACDQ,MAAM,CAAC,CAACC,GAAG,EAAE,CAACH,IAAI,EAAEI,OAAO,CAAC,KAAK;QAChCD,GAAG,CAACH,IAAI,CAAC,GAAGI,OAAO;QACnB,OAAOD,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAER;MACA,MAAME,MAAM,GAAG,CAAC,CAAC;MACjBR,MAAM,CAACS,MAAM,CAACV,aAAa,CAAC,CAACW,OAAO,CAACH,OAAO,IAAI;QAC9CP,MAAM,CAACC,OAAO,CAACM,OAAO,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,MAAM,EAAEC,KAAK,CAAC,KAAK;UACnDJ,MAAM,CAACG,MAAM,CAAC,GAAG,CAACH,MAAM,CAACG,MAAM,CAAC,IAAI,CAAC,IAAIC,KAAK;QAChD,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,OAAO;QACLjD,UAAU;QACV8B,SAAS,EAAEE,KAAK,CAAC3B,WAAW,CAAC,CAAC;QAC9B0B,OAAO,EAAEG,GAAG,CAAC7B,WAAW,CAAC,CAAC;QAC1BP,KAAK,EAAEsC,aAAa;QACpBS;MACF,CAAC;IACH,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,8BAA8B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB;MAAW,CAAC,CAAC;MAClF,MAAMpB,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsE,WAAWA,CAAClD,UAAU,EAAEmD,UAAU,EAAEC,QAAQ,EAAEC,SAAS,GAAG,IAAIjD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAEiD,QAAQ,GAAG,IAAI,EAAE;IACzG,IAAI;MACF9F,MAAM,CAACmB,IAAI,CAAC,iBAAiB,EAAE;QAAEqB,UAAU;QAAEmD,UAAU;QAAEC,QAAQ;QAAEE;MAAS,CAAC,CAAC;;MAE9E;MACA,MAAMxD,KAAK,GAAG,MAAM,IAAI,CAACF,SAAS,CAAC,CAAC;;MAEpC;MACA,IAAI,CAACE,KAAK,CAACE,UAAU,CAAC,EAAE;QACtBF,KAAK,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;MACxB;;MAEA;MACA,MAAMwC,IAAI,GAAGa,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEpC;MACA,IAAI,CAACzD,KAAK,CAACE,UAAU,CAAC,CAACwC,IAAI,CAAC,EAAE;QAC5B1C,KAAK,CAACE,UAAU,CAAC,CAACwC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC9B;;MAEA;MACA1C,KAAK,CAACE,UAAU,CAAC,CAACwC,IAAI,CAAC,CAACW,UAAU,CAAC,GAAG,CAACrD,KAAK,CAACE,UAAU,CAAC,CAACwC,IAAI,CAAC,CAACW,UAAU,CAAC,IAAI,CAAC,IAAIC,QAAQ;;MAE3F;MACA,MAAM,IAAI,CAACvD,SAAS,CAACC,KAAK,CAAC;;MAE3B;MACA,IAAI,IAAI,CAACtB,kBAAkB,IAAIJ,OAAO,CAACC,GAAG,CAACmF,mBAAmB,EAAE;QAC9D;QACA,IAAIF,QAAQ,EAAE;UACZ,IAAI;YACF;YACA,MAAMG,aAAa,GAAG;cACpBC,MAAM,EAAE,YAAYtF,OAAO,CAACC,GAAG,CAACC,cAAc,EAAE;cAChDqF,YAAY,EAAE,YAAYvF,OAAO,CAACC,GAAG,CAACC,cAAc,mCAAmC;cACvFsF,WAAW,EAAET,UAAU;cACvBU,UAAU,EAAET,QAAQ;cACpBC,SAAS,EAAEA,SAAS;cACpBS,MAAM,EAAE;gBACNC,SAAS,EAAET;cACb;YACF,CAAC;;YAED;YACA,MAAM,IAAI,CAAC9E,kBAAkB,CAAC0E,WAAW,CAACO,aAAa,CAAC;YACxDjG,MAAM,CAACmB,IAAI,CAAC,2CAA2C,EAAE;cAAE2E,QAAQ;cAAEH,UAAU;cAAEC;YAAS,CAAC,CAAC;UAC9F,CAAC,CAAC,OAAOY,WAAW,EAAE;YACpBxG,MAAM,CAACoB,KAAK,CAAC,0CAA0C,EAAE;cACvDA,KAAK,EAAEoF,WAAW,CAACnF,OAAO;cAC1ByE,QAAQ;cACRtD,UAAU;cACVmD;YACF,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACL3F,MAAM,CAAC2D,IAAI,CAAC,4DAA4D,CAAC;QAC3E;MACF;;MAEA;MACA,IAAI,IAAI,CAAC1C,QAAQ,EAAE;QACjB,MAAMiC,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAACiC,OAAO,CAAC,SAAS,CAAC;QAChD,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,OAAO,CAAC;QAEpC,MAAMA,KAAK,CAACC,MAAM,CAAC,CAAC;UAClBC,WAAW,EAAEb,UAAU;UACvB+D,SAAS,EAAET,QAAQ;UACnBW,WAAW,EAAEd,UAAU;UACvBC,QAAQ,EAAEA,QAAQ;UAClBC,SAAS,EAAEA,SAAS;UACpBb,IAAI,EAAEA;QACR,CAAC,CAAC,CAAC;MACL;MAEAhF,MAAM,CAACmB,IAAI,CAAC,gBAAgB,EAAE;QAAEqB,UAAU;QAAEsD,QAAQ;QAAEH,UAAU;QAAEC;MAAS,CAAC,CAAC;IAC/E,CAAC,CAAC,OAAOxE,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,uBAAuB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEmB,UAAU;QAAEsD,QAAQ;QAAEH;MAAW,CAAC,CAAC;MACjG,MAAMvE,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsF,iBAAiBA,CAACZ,QAAQ,EAAEH,UAAU,EAAEC,QAAQ,EAAEC,SAAS,GAAG,IAAIjD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;IAC5F,IAAI;MACF;MACA;MACA,MAAML,UAAU,GAAG,UAAUsD,QAAQ,EAAE;;MAEvC;MACA,OAAO,IAAI,CAACJ,WAAW,CAAClD,UAAU,EAAEmD,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,CAAC;IAChF,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACdpB,MAAM,CAACoB,KAAK,CAAC,8BAA8B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC,OAAO;QAAEyE,QAAQ;QAAEH;MAAW,CAAC,CAAC;MAC5F,MAAMvE,KAAK;IACb;EACF;AACF;AAEAuF,MAAM,CAACC,OAAO,GAAGxG,cAAc", "ignoreList": []}