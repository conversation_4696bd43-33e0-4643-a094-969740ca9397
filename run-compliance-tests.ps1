# NovaFuse Universal Platform - Compliance Tests

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )

    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for test results
Write-ColorOutput "Creating directories for test results..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results/compliance" | Out-Null

# Display welcome message
Write-ColorOutput "NovaFuse Universal Platform - Compliance Tests" -ForegroundColor Cyan
Write-ColorOutput "=============================================" -ForegroundColor Cyan
Write-ColorOutput "This script will run compliance tests for NovaFuse against major frameworks:" -ForegroundColor Cyan
Write-ColorOutput "- NIST Cybersecurity Framework (CSF)" -ForegroundColor Cyan
Write-ColorOutput "- General Data Protection Regulation (GDPR)" -ForegroundColor Cyan
Write-ColorOutput "- SOC 2" -ForegroundColor Cyan
Write-ColorOutput "" -ForegroundColor Cyan

# Run individual framework tests
Write-ColorOutput "Running NIST CSF compliance tests..." -ForegroundColor Yellow
node tests/compliance/nist-csf/run-nist-csf-tests.js

Write-ColorOutput "Running GDPR compliance tests..." -ForegroundColor Yellow
node tests/compliance/gdpr/run-gdpr-tests.js

Write-ColorOutput "Running SOC 2 compliance tests..." -ForegroundColor Yellow
node tests/compliance/soc2/run-soc2-tests.js

# Run all compliance tests and generate a comprehensive report
Write-ColorOutput "Generating comprehensive compliance report..." -ForegroundColor Yellow
node tests/compliance/run-all-compliance-tests.js

# Display summary
Write-ColorOutput "`nCompliance testing completed!" -ForegroundColor Green
Write-ColorOutput "Test results are available in the console output above." -ForegroundColor Green
Write-ColorOutput "Compliance reports are available in ./test-results/compliance" -ForegroundColor Green

# Open the compliance summary report
Write-ColorOutput "`nOpening compliance summary report..." -ForegroundColor Green
$latestReport = Get-ChildItem -Path "./test-results/compliance" -Filter "compliance-summary-*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
if ($latestReport) {
    Start-Process $latestReport.FullName
} else {
    Write-ColorOutput "No compliance summary report found." -ForegroundColor Red
}
