/**
 * Database Module
 *
 * This module provides a connection to the database and utility functions
 * for executing queries and transactions.
 */

const { Pool } = require('pg');
const { logger } = require('./utils/logger');

// Get database configuration from environment variables
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'privacy_management',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  max: parseInt(process.env.DB_POOL_MAX || '10', 10),
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000', 10),
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000', 10),
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false
};

// Create a new pool instance
const pool = new Pool(dbConfig);

// Log pool errors
pool.on('error', (err) => {
  logger.error('Unexpected error on idle client', err);
});

/**
 * Execute a database query
 * @param {string} text - SQL query text
 * @param {Array} params - Query parameters
 * @returns {Promise<Object>} - Query result
 */
async function query(text, params = []) {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    logger.debug(`Executed query: ${text} (${duration}ms)`);
    return result;
  } catch (error) {
    logger.error(`Query error: ${text}`, error);
    throw error;
  }
}

/**
 * Execute a transaction with multiple queries
 * @param {Function} callback - Transaction callback function that receives a client
 * @returns {Promise<any>} - Transaction result
 */
async function transaction(callback) {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Transaction error', error);
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Close the database connection pool
 * @returns {Promise<void>}
 */
async function close() {
  try {
    await pool.end();
    logger.info('Database connection pool closed');
  } catch (error) {
    logger.error('Error closing database connection pool', error);
    throw error;
  }
}

module.exports = {
  query,
  transaction,
  close
};
