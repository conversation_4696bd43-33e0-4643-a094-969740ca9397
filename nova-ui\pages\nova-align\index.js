import { useState, useEffect } from 'react';
import Head from 'next/head';
import { ProductProvider, PRODUCTS } from '../../packages/feature-flags/ProductContext';
import Layout from '../../shared/layouts/Layout';

/**
 * NovaAlign Studio - AI Alignment & Safety Monitoring Platform
 * @returns {React.ReactNode} - The rendered component
 */
export default function NovaAlign() {
  const [isMonitoring, setIsMonitoring] = useState(true);
  const [alertLevel, setAlertLevel] = useState('normal');
  const [metrics, setMetrics] = useState({
    psi: 0.87,
    phi: 0.91,
    theta: 0.89,
    alignment: 0.92,
    safety: 0.94
  });

  const [systems, setSystems] = useState([
    { id: 'sys_001', name: 'GPT-4 Production', status: 'aligned', risk: 'low' },
    { id: 'sys_002', name: '<PERSON>', status: 'aligned', risk: 'low' },
    { id: 'sys_003', name: 'Custom LLM Alpha', status: 'monitoring', risk: 'medium' },
    { id: 'sys_004', name: 'Vision AI Beta', status: 'aligned', risk: 'low' }
  ]);

  const [alerts, setAlerts] = useState([
    { id: 1, type: 'info', message: 'System alignment check completed successfully', time: '2 minutes ago' },
    { id: 2, type: 'warning', message: 'Custom LLM Alpha showing minor deviation in theta metrics', time: '15 minutes ago' },
    { id: 3, type: 'success', message: 'All systems passed safety validation', time: '1 hour ago' }
  ]);

  useEffect(() => {
    // Simulate real-time metrics updates
    const interval = setInterval(() => {
      setMetrics(prev => ({
        psi: Math.max(0.7, Math.min(0.99, prev.psi + (Math.random() - 0.5) * 0.02)),
        phi: Math.max(0.7, Math.min(0.99, prev.phi + (Math.random() - 0.5) * 0.02)),
        theta: Math.max(0.7, Math.min(0.99, prev.theta + (Math.random() - 0.5) * 0.02)),
        alignment: Math.max(0.7, Math.min(0.99, prev.alignment + (Math.random() - 0.5) * 0.01)),
        safety: Math.max(0.7, Math.min(0.99, prev.safety + (Math.random() - 0.5) * 0.01))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const runAlignmentCheck = async () => {
    setIsMonitoring(true);
    setAlertLevel('checking');
    
    // Simulate alignment check
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const newAlert = {
      id: Date.now(),
      type: 'success',
      message: 'Comprehensive alignment check completed - All systems within acceptable parameters',
      time: 'Just now'
    };
    
    setAlerts(prev => [newAlert, ...prev.slice(0, 4)]);
    setAlertLevel('normal');
    setIsMonitoring(true);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'aligned': return 'text-green-400';
      case 'monitoring': return 'text-yellow-400';
      case 'warning': return 'text-red-400';
      default: return 'text-slate-400';
    }
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-red-500';
      default: return 'bg-slate-500';
    }
  };

  return (
    <ProductProvider initialProduct={PRODUCTS.NOVA_PRIME}>
      <Layout>
        <Head>
          <title>NovaAlign Studio - AI Alignment & Safety Monitoring | NovaFuse</title>
          <meta name="description" content="Enterprise AI safety monitoring with real-time coherence enforcement" />
        </Head>

        <div className="min-h-screen bg-slate-900 text-slate-100">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-white mb-4">
                  🎯 NovaAlign Studio
                </h1>
                <p className="text-xl text-blue-100 mb-2">
                  AI Alignment & Safety Monitoring Platform
                </p>
                <p className="text-lg text-blue-200">
                  Enterprise AI Safety with Real-Time Coherence Enforcement
                </p>
              </div>
            </div>
          </div>

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Status Overview */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
              {/* Consciousness Metrics */}
              <div className="bg-slate-800 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-400 mb-2">
                    {metrics.psi.toFixed(3)}
                  </div>
                  <div className="text-sm text-slate-400">Ψ (Psi)</div>
                  <div className="text-xs text-slate-500">Consciousness</div>
                </div>
              </div>

              <div className="bg-slate-800 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-400 mb-2">
                    {metrics.phi.toFixed(3)}
                  </div>
                  <div className="text-sm text-slate-400">Φ (Phi)</div>
                  <div className="text-xs text-slate-500">Integration</div>
                </div>
              </div>

              <div className="bg-slate-800 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-400 mb-2">
                    {metrics.theta.toFixed(3)}
                  </div>
                  <div className="text-sm text-slate-400">Θ (Theta)</div>
                  <div className="text-xs text-slate-500">Coherence</div>
                </div>
              </div>

              <div className="bg-slate-800 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-400 mb-2">
                    {metrics.alignment.toFixed(3)}
                  </div>
                  <div className="text-sm text-slate-400">Alignment</div>
                  <div className="text-xs text-slate-500">Human Values</div>
                </div>
              </div>

              <div className="bg-slate-800 rounded-lg p-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-400 mb-2">
                    {metrics.safety.toFixed(3)}
                  </div>
                  <div className="text-sm text-slate-400">Safety</div>
                  <div className="text-xs text-slate-500">Risk Level</div>
                </div>
              </div>
            </div>

            {/* System Status */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* Monitored Systems */}
              <div className="bg-slate-800 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-blue-400 mb-4">
                  🤖 Monitored AI Systems
                </h3>
                
                <div className="space-y-3">
                  {systems.map((system) => (
                    <div key={system.id} className="bg-slate-900 rounded-lg p-4 border border-slate-700">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-white">{system.name}</div>
                          <div className="text-sm text-slate-400">{system.id}</div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${getRiskColor(system.risk)}`}></div>
                          <span className={`text-sm font-medium ${getStatusColor(system.status)}`}>
                            {system.status.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Real-time Alerts */}
              <div className="bg-slate-800 rounded-lg p-6">
                <h3 className="text-xl font-semibold text-blue-400 mb-4">
                  🚨 Real-time Alerts
                </h3>
                
                <div className="space-y-3">
                  {alerts.map((alert) => (
                    <div key={alert.id} className={`bg-slate-900 rounded-lg p-4 border-l-4 ${
                      alert.type === 'success' ? 'border-green-500' :
                      alert.type === 'warning' ? 'border-yellow-500' :
                      alert.type === 'error' ? 'border-red-500' :
                      'border-blue-500'
                    }`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="text-sm text-slate-300">{alert.message}</div>
                          <div className="text-xs text-slate-500 mt-1">{alert.time}</div>
                        </div>
                        <div className={`text-lg ${
                          alert.type === 'success' ? 'text-green-400' :
                          alert.type === 'warning' ? 'text-yellow-400' :
                          alert.type === 'error' ? 'text-red-400' :
                          'text-blue-400'
                        }`}>
                          {alert.type === 'success' ? '✅' :
                           alert.type === 'warning' ? '⚠️' :
                           alert.type === 'error' ? '❌' : 'ℹ️'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Control Panel */}
            <div className="bg-slate-800 rounded-lg p-6 mb-8">
              <h3 className="text-xl font-semibold text-blue-400 mb-4">
                🎛️ Alignment Control Panel
              </h3>
              
              <div className="flex flex-wrap gap-4 justify-center">
                <button
                  onClick={runAlignmentCheck}
                  disabled={alertLevel === 'checking'}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-lg transition-all disabled:opacity-50"
                >
                  {alertLevel === 'checking' ? '🔄 Checking...' : '🔍 Run Alignment Check'}
                </button>
                
                <button
                  onClick={() => setIsMonitoring(!isMonitoring)}
                  className={`px-6 py-3 font-semibold rounded-lg transition-all ${
                    isMonitoring 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  }`}
                >
                  {isMonitoring ? '⏸️ Pause Monitoring' : '▶️ Resume Monitoring'}
                </button>
                
                <button className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 font-semibold rounded-lg transition-colors">
                  📊 Generate Report
                </button>
                
                <button className="px-6 py-3 bg-slate-700 hover:bg-slate-600 text-slate-300 font-semibold rounded-lg transition-colors">
                  ⚙️ Configure Thresholds
                </button>
              </div>
            </div>

            {/* Compliance Status */}
            <div className="bg-slate-800 rounded-lg p-6">
              <h3 className="text-xl font-semibold text-blue-400 mb-4">
                📋 Compliance & Regulatory Status
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-slate-900 rounded-lg p-4 border-l-4 border-green-500">
                  <h4 className="font-semibold text-green-400 mb-2">🏛️ White House EO</h4>
                  <div className="text-sm text-slate-300">
                    Executive Order on AI Safety - Fully Compliant
                  </div>
                  <div className="text-xs text-green-400 mt-1">✅ All requirements met</div>
                </div>

                <div className="bg-slate-900 rounded-lg p-4 border-l-4 border-green-500">
                  <h4 className="font-semibold text-green-400 mb-2">🇪🇺 EU AI Act</h4>
                  <div className="text-sm text-slate-300">
                    European Union AI Regulation - Compliant
                  </div>
                  <div className="text-xs text-green-400 mt-1">✅ High-risk AI systems certified</div>
                </div>

                <div className="bg-slate-900 rounded-lg p-4 border-l-4 border-green-500">
                  <h4 className="font-semibold text-green-400 mb-2">🔬 NIST Framework</h4>
                  <div className="text-sm text-slate-300">
                    NIST AI Risk Management - Implemented
                  </div>
                  <div className="text-xs text-green-400 mt-1">✅ Framework fully deployed</div>
                </div>
              </div>

              <div className="mt-6 bg-blue-900/20 border border-blue-700 rounded-lg p-4">
                <h4 className="font-semibold text-blue-400 mb-3">🎯 NovaAlign Advantages</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
                  <div>
                    <div className="font-medium text-blue-300 mb-1">🔄 Real-time Monitoring</div>
                    <div>Continuous consciousness field monitoring with ∂Ψ=0 enforcement</div>
                  </div>
                  <div>
                    <div className="font-medium text-blue-300 mb-1">🛡️ Proactive Safety</div>
                    <div>Predictive alignment detection before safety incidents occur</div>
                  </div>
                  <div>
                    <div className="font-medium text-blue-300 mb-1">📊 Enterprise Scale</div>
                    <div>Monitor thousands of AI systems with unified consciousness metrics</div>
                  </div>
                  <div>
                    <div className="font-medium text-blue-300 mb-1">💰 Cost Effective</div>
                    <div>$250K-$1.5M annual contracts vs. $10M+ traditional solutions</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    </ProductProvider>
  );
}
