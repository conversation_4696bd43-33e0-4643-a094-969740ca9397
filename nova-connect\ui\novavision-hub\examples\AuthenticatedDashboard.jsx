/**
 * Authenticated Dashboard Example
 * 
 * This example demonstrates how to use the authentication system.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  LoginForm,
  ProtectedRoute,
  UserProfile,
  TabPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';
import { AuthProvider, MockAuthService } from '../auth';

// Create mock auth service
const mockAuthService = new MockAuthService();

/**
 * Authenticated Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Authenticated Dashboard Content component
 */
const AuthenticatedDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Tabs
  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      content: (
        <ProtectedRoute
          fallback={
            <div className="bg-surface p-6 rounded-lg shadow-lg">
              <h2 className="text-xl font-bold text-textPrimary mb-4">
                Authentication Required
              </h2>
              <p className="text-textSecondary mb-4">
                Please sign in to access the dashboard.
              </p>
              <LoginForm />
            </div>
          }
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <DashboardCard
              title="Welcome"
              subtitle="Authenticated Dashboard"
            >
              <div className="p-4">
                <p className="text-textPrimary">
                  Welcome to the authenticated dashboard. You are now signed in.
                </p>
                <p className="text-textSecondary mt-2">
                  This content is only visible to authenticated users.
                </p>
              </div>
            </DashboardCard>
            
            <DashboardCard
              title="Admin Panel"
              subtitle="Admin Only"
            >
              <div className="p-4">
                <ProtectedRoute
                  requiredRole="admin"
                  fallback={
                    <div className="bg-error bg-opacity-10 border border-error text-error px-4 py-3 rounded">
                      You do not have permission to access this content.
                    </div>
                  }
                  redirectToLogin={false}
                >
                  <p className="text-textPrimary">
                    Welcome, Admin! This content is only visible to users with the admin role.
                  </p>
                  <p className="text-textSecondary mt-2">
                    You can manage users and settings here.
                  </p>
                </ProtectedRoute>
              </div>
            </DashboardCard>
            
            <DashboardCard
              title="Compliance Data"
              subtitle="Authenticated Content"
              className="md:col-span-2"
            >
              <div className="p-4">
                <p className="text-textPrimary">
                  This card contains sensitive compliance data that is only visible to authenticated users.
                </p>
                <div className="mt-4 bg-background p-4 rounded border border-divider">
                  <h3 className="font-medium text-textPrimary">Compliance Status</h3>
                  <div className="mt-2 space-y-2">
                    <div className="flex justify-between">
                      <span className="text-textSecondary">PCI DSS</span>
                      <span className="text-success">Compliant</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-textSecondary">HIPAA</span>
                      <span className="text-success">Compliant</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-textSecondary">GDPR</span>
                      <span className="text-warning">Partial</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-textSecondary">SOC 2</span>
                      <span className="text-success">Compliant</span>
                    </div>
                  </div>
                </div>
              </div>
            </DashboardCard>
          </div>
        </ProtectedRoute>
      )
    },
    {
      id: 'profile',
      label: 'Profile',
      content: (
        <ProtectedRoute
          fallback={
            <div className="bg-surface p-6 rounded-lg shadow-lg">
              <h2 className="text-xl font-bold text-textPrimary mb-4">
                Authentication Required
              </h2>
              <p className="text-textSecondary mb-4">
                Please sign in to access your profile.
              </p>
              <LoginForm />
            </div>
          }
        >
          <UserProfile />
        </ProtectedRoute>
      )
    },
    {
      id: 'login',
      label: 'Login',
      content: (
        <div className="flex justify-center">
          <LoginForm
            onSuccess={(user) => {
              console.log('Login successful:', user);
              setActiveTab('dashboard');
            }}
            onError={(error) => {
              console.error('Login error:', error);
            }}
          />
        </div>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Authenticated Dashboard
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={tabs}
          defaultTab="dashboard"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

AuthenticatedDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Authenticated Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Authenticated Dashboard component
 */
const AuthenticatedDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <PreferencesProvider>
        <OfflineProvider>
          <AnimationProvider>
            <AuthProvider authService={mockAuthService}>
              <AuthenticatedDashboardContent
                novaConnect={novaConnect}
                novaShield={novaShield}
                novaTrack={novaTrack}
                enableLogging={enableLogging}
              />
            </AuthProvider>
          </AnimationProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
};

AuthenticatedDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default AuthenticatedDashboard;
