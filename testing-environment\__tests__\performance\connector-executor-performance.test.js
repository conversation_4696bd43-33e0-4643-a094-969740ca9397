const axios = require('axios');
const { spawn } = require('child_process');
const path = require('path');

// Configuration
const mockApiPort = 3005;
const registryPort = 3006;
const authPort = 3007;
const executorPort = 3008;
const mockApiUrl = `http://localhost:${mockApiPort}`;
const registryUrl = `http://localhost:${registryPort}`;
const authUrl = `http://localhost:${authPort}`;
const executorUrl = `http://localhost:${executorPort}`;

// Test data
const testConnector = {
  metadata: {
    name: 'Performance Test Connector',
    version: '1.0.0',
    category: 'Performance',
    description: 'Connector for performance testing',
    author: 'NovaGRC',
    tags: ['performance', 'test']
  },
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true,
        sensitive: true
      }
    },
    testConnection: {
      endpoint: '/health',
      method: 'GET',
      expectedResponse: {
        status: 200
      }
    }
  },
  configuration: {
    baseUrl: mockApiUrl,
    headers: {},
    timeout: 30000,
    retryPolicy: {
      maxRetries: 3,
      backoffStrategy: 'exponential'
    }
  },
  endpoints: [
    {
      id: 'getFindings',
      name: 'Get Findings',
      path: '/aws/securityhub/findings',
      method: 'GET',
      parameters: {
        query: {},
        path: {},
        body: {}
      },
      response: {
        successCode: 200
      }
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getFindings',
      targetSystem: 'NovaGRC',
      targetEntity: 'ComplianceFindings',
      transformations: [
        {
          source: '$.Findings[0].Id',
          target: 'findingId',
          transform: 'identity'
        }
      ]
    }
  ],
  events: {
    webhooks: [],
    polling: []
  }
};

const testCredential = {
  name: 'Performance Test Credential',
  connectorId: '', // Will be set after connector creation
  authType: 'API_KEY',
  credentials: {
    apiKey: 'performance-test-api-key',
    header: 'X-API-Key'
  },
  userId: 'performance-test-user'
};

// Helper function to start a service
const startService = (scriptPath, port) => {
  const service = spawn('node', [scriptPath], {
    env: { ...process.env, PORT: port.toString() },
    stdio: 'pipe'
  });
  
  service.stdout.on('data', (data) => {
    console.log(`[${path.basename(scriptPath)}] ${data.toString().trim()}`);
  });
  
  service.stderr.on('data', (data) => {
    console.error(`[${path.basename(scriptPath)}] ERROR: ${data.toString().trim()}`);
  });
  
  return service;
};

// Helper function to wait for a service to be ready
const waitForService = async (url, maxRetries = 10, retryDelay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await axios.get(`${url}/health`);
      if (response.status === 200) {
        return true;
      }
    } catch (err) {
      console.log(`Waiting for service at ${url}... (${i + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }
  
  throw new Error(`Service at ${url} is not available after ${maxRetries} retries`);
};

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = process.hrtime.bigint();
  const result = await fn();
  const end = process.hrtime.bigint();
  const duration = Number(end - start) / 1e6; // Convert to milliseconds
  return { result, duration };
};

// Performance tests for connector executor service
describe('Connector Executor Performance', () => {
  let mockApiService;
  let registryService;
  let authService;
  let executorService;
  let connectorId;
  let credentialId;
  
  // Start services before all tests
  beforeAll(async () => {
    // Start mock API service
    mockApiService = startService(
      path.resolve(__dirname, '../../test-mock-api.js'),
      mockApiPort
    );
    
    // Start connector registry service
    registryService = startService(
      path.resolve(__dirname, '../../test-connector-registry.js'),
      registryPort
    );
    
    // Start authentication service
    authService = startService(
      path.resolve(__dirname, '../../test-auth-service.js'),
      authPort
    );
    
    // Start connector executor service
    executorService = startService(
      path.resolve(__dirname, '../../test-connector-executor.js'),
      executorPort
    );
    
    // Wait for services to be ready
    await Promise.all([
      waitForService(mockApiUrl),
      waitForService(registryUrl),
      waitForService(authUrl),
      waitForService(executorUrl)
    ]);
    
    console.log('Services are ready for testing');
    
    // Create test connector
    const connectorResponse = await axios.post(`${registryUrl}/connectors`, testConnector);
    connectorId = connectorResponse.data.id;
    console.log(`Created connector with ID: ${connectorId}`);
    
    // Create test credential
    testCredential.connectorId = connectorId;
    const credentialResponse = await axios.post(`${authUrl}/credentials`, testCredential);
    credentialId = credentialResponse.data.id;
    console.log(`Created credential with ID: ${credentialId}`);
  }, 60000);
  
  // Stop services after all tests
  afterAll(() => {
    // Stop services
    if (mockApiService) {
      mockApiService.kill();
    }
    
    if (registryService) {
      registryService.kill();
    }
    
    if (authService) {
      authService.kill();
    }
    
    if (executorService) {
      executorService.kill();
    }
    
    console.log('Services stopped');
  });
  
  // Test single execution performance
  describe('Single Execution Performance', () => {
    it('should execute a connector endpoint within acceptable time', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
          credentialId,
          parameters: {},
          userId: 'performance-test-user'
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(duration).toBeLessThan(1000); // Should execute in less than 1 second
      
      console.log(`Single execution completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test concurrent execution performance
  describe('Concurrent Execution Performance', () => {
    it('should handle 10 concurrent executions within acceptable time', async () => {
      const concurrentRequests = 10;
      const executionFn = () => axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'performance-test-user'
      });
      
      const { result, duration } = await measureExecutionTime(async () => {
        const promises = Array(concurrentRequests).fill(0).map(() => executionFn());
        return await Promise.all(promises);
      });
      
      expect(result).toHaveLength(concurrentRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('success', true);
      });
      
      // Average execution time per request
      const averageTime = duration / concurrentRequests;
      expect(averageTime).toBeLessThan(500); // Average time should be less than 500ms
      
      console.log(`${concurrentRequests} concurrent executions completed in ${duration.toFixed(2)} ms (avg: ${averageTime.toFixed(2)} ms per request)`);
    });
  });
  
  // Test throughput performance
  describe('Throughput Performance', () => {
    it('should achieve acceptable throughput for 100 sequential executions', async () => {
      const totalRequests = 100;
      const executionFn = () => axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'performance-test-user'
      });
      
      const { result, duration } = await measureExecutionTime(async () => {
        const results = [];
        for (let i = 0; i < totalRequests; i++) {
          results.push(await executionFn());
        }
        return results;
      });
      
      expect(result).toHaveLength(totalRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('success', true);
      });
      
      // Calculate throughput (requests per second)
      const throughput = (totalRequests / duration) * 1000;
      expect(throughput).toBeGreaterThan(10); // Should handle at least 10 requests per second
      
      console.log(`${totalRequests} sequential executions completed in ${duration.toFixed(2)} ms (throughput: ${throughput.toFixed(2)} requests/second)`);
    });
  });
  
  // Test response time distribution
  describe('Response Time Distribution', () => {
    it('should have consistent response times across multiple executions', async () => {
      const totalRequests = 50;
      const responseTimes = [];
      
      for (let i = 0; i < totalRequests; i++) {
        const { duration } = await measureExecutionTime(async () => {
          return await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
            credentialId,
            parameters: {},
            userId: 'performance-test-user'
          });
        });
        
        responseTimes.push(duration);
      }
      
      // Calculate statistics
      const average = responseTimes.reduce((sum, time) => sum + time, 0) / totalRequests;
      const sorted = [...responseTimes].sort((a, b) => a - b);
      const median = sorted[Math.floor(totalRequests / 2)];
      const min = sorted[0];
      const max = sorted[totalRequests - 1];
      const p95 = sorted[Math.floor(totalRequests * 0.95)];
      const p99 = sorted[Math.floor(totalRequests * 0.99)];
      
      // Standard deviation
      const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - average, 2), 0) / totalRequests;
      const stdDev = Math.sqrt(variance);
      
      // Log statistics
      console.log(`Response Time Statistics (ms):`);
      console.log(`  Min: ${min.toFixed(2)}`);
      console.log(`  Max: ${max.toFixed(2)}`);
      console.log(`  Average: ${average.toFixed(2)}`);
      console.log(`  Median: ${median.toFixed(2)}`);
      console.log(`  95th Percentile: ${p95.toFixed(2)}`);
      console.log(`  99th Percentile: ${p99.toFixed(2)}`);
      console.log(`  Standard Deviation: ${stdDev.toFixed(2)}`);
      
      // Assertions
      expect(average).toBeLessThan(500); // Average should be less than 500ms
      expect(p95).toBeLessThan(1000); // 95th percentile should be less than 1000ms
      expect(stdDev / average).toBeLessThan(0.5); // Coefficient of variation should be less than 0.5
    });
  });
  
  // Test memory usage
  describe('Memory Usage', () => {
    it('should not have memory leaks during repeated executions', async () => {
      const totalRequests = 200;
      const executionFn = () => axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'performance-test-user'
      });
      
      // Record initial memory usage
      const initialMemory = process.memoryUsage();
      
      // Execute requests
      for (let i = 0; i < totalRequests; i++) {
        await executionFn();
        
        // Force garbage collection every 50 requests
        if (i % 50 === 0 && global.gc) {
          global.gc();
        }
      }
      
      // Record final memory usage
      const finalMemory = process.memoryUsage();
      
      // Calculate memory growth
      const heapGrowth = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024; // MB
      const rssGrowth = (finalMemory.rss - initialMemory.rss) / 1024 / 1024; // MB
      
      console.log(`Memory Usage (MB):`);
      console.log(`  Initial Heap: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)}`);
      console.log(`  Final Heap: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)}`);
      console.log(`  Heap Growth: ${heapGrowth.toFixed(2)}`);
      console.log(`  Initial RSS: ${(initialMemory.rss / 1024 / 1024).toFixed(2)}`);
      console.log(`  Final RSS: ${(finalMemory.rss / 1024 / 1024).toFixed(2)}`);
      console.log(`  RSS Growth: ${rssGrowth.toFixed(2)}`);
      
      // Note: This test is more informative than assertive since we can't force GC in all environments
      // In a real test, we would use a memory profiler to detect leaks
    });
  });
});
