/**
 * Optimized Data Mapper Tests
 * 
 * This file contains tests for the optimized data mapper.
 */

const { expect } = require('chai');
const { OptimizedDataMapper } = require('../src/csde/mapping');

describe('OptimizedDataMapper', () => {
  let mapper;
  
  beforeEach(() => {
    mapper = new OptimizedDataMapper({
      enableCaching: true,
      enableParallelProcessing: false, // Disable for testing
      enableDirectPropertyAccess: true,
      cacheSize: 100,
      cacheTTL: 60 * 60 * 1000 // 1 hour
    });
  });
  
  afterEach(() => {
    if (mapper) {
      mapper.clearCache();
      mapper.shutdown();
    }
  });
  
  describe('Basic Mapping', () => {
    it('should map data according to rules', async () => {
      const data = {
        id: '123',
        name: 'Test',
        value: 42,
        nested: {
          property: 'nested value'
        }
      };
      
      const rules = [
        {
          source: 'id',
          target: 'identifier',
          transform: 'uppercase'
        },
        {
          source: 'name',
          target: 'title',
          transform: null
        },
        {
          source: 'value',
          target: 'amount',
          transform: null
        },
        {
          source: 'nested.property',
          target: 'nestedValue',
          transform: 'trim'
        }
      ];
      
      const result = await mapper.map(data, rules);
      
      expect(result).to.be.an('object');
      expect(result.identifier).to.equal('123');
      expect(result.title).to.equal('Test');
      expect(result.amount).to.equal(42);
      expect(result.nestedValue).to.equal('nested value');
    });
    
    it('should apply transformations correctly', async () => {
      const data = {
        text: ' Hello World ',
        number: '42',
        flag: 1,
        array: ['a', 'b', 'c']
      };
      
      const rules = [
        {
          source: 'text',
          target: 'trimmedText',
          transform: 'trim'
        },
        {
          source: 'text',
          target: 'upperText',
          transform: 'uppercase'
        },
        {
          source: 'text',
          target: 'lowerText',
          transform: 'lowercase'
        },
        {
          source: 'number',
          target: 'parsedNumber',
          transform: 'toNumber'
        },
        {
          source: 'flag',
          target: 'boolFlag',
          transform: 'toBoolean'
        },
        {
          source: 'array',
          target: 'joinedArray',
          transform: 'join'
        }
      ];
      
      const result = await mapper.map(data, rules);
      
      expect(result.trimmedText).to.equal('Hello World');
      expect(result.upperText).to.equal(' HELLO WORLD ');
      expect(result.lowerText).to.equal(' hello world ');
      expect(result.parsedNumber).to.equal(42);
      expect(result.boolFlag).to.equal(true);
      expect(result.joinedArray).to.equal('a,b,c');
    });
    
    it('should handle nested targets', async () => {
      const data = {
        id: '123',
        name: 'Test'
      };
      
      const rules = [
        {
          source: 'id',
          target: 'meta.id',
          transform: null
        },
        {
          source: 'name',
          target: 'meta.details.name',
          transform: null
        }
      ];
      
      const result = await mapper.map(data, rules);
      
      expect(result).to.be.an('object');
      expect(result.meta).to.be.an('object');
      expect(result.meta.id).to.equal('123');
      expect(result.meta.details).to.be.an('object');
      expect(result.meta.details.name).to.equal('Test');
    });
  });
  
  describe('Caching', () => {
    it('should cache results', async () => {
      const data = { id: '123', name: 'Test' };
      const rules = [
        { source: 'id', target: 'identifier', transform: null },
        { source: 'name', target: 'title', transform: null }
      ];
      
      // First call should miss cache
      await mapper.map(data, rules);
      
      // Second call should hit cache
      await mapper.map(data, rules);
      
      expect(mapper.metrics.cacheHits).to.equal(1);
      expect(mapper.metrics.cacheMisses).to.equal(1);
      expect(mapper.metrics.cacheHitRate).to.equal(0.5);
    });
    
    it('should limit cache size', async () => {
      // Create mapper with small cache size
      const smallMapper = new OptimizedDataMapper({
        enableCaching: true,
        cacheSize: 2
      });
      
      // Map three different data objects
      await smallMapper.map({ id: '1' }, [{ source: 'id', target: 'id' }]);
      await smallMapper.map({ id: '2' }, [{ source: 'id', target: 'id' }]);
      await smallMapper.map({ id: '3' }, [{ source: 'id', target: 'id' }]);
      
      // Cache should only contain the last two
      expect(smallMapper.cache.size).to.be.at.most(2);
      
      smallMapper.clearCache();
      smallMapper.shutdown();
    });
  });
  
  describe('Error Handling', () => {
    it('should handle missing source fields gracefully', async () => {
      const data = { id: '123' };
      const rules = [
        { source: 'id', target: 'identifier', transform: null },
        { source: 'name', target: 'title', transform: null } // name doesn't exist
      ];
      
      const result = await mapper.map(data, rules);
      
      expect(result).to.be.an('object');
      expect(result.identifier).to.equal('123');
      expect(result.title).to.be.undefined;
    });
    
    it('should handle invalid transformations gracefully', async () => {
      const data = { id: '123' };
      const rules = [
        { source: 'id', target: 'identifier', transform: 'nonExistentTransform' }
      ];
      
      const result = await mapper.map(data, rules);
      
      expect(result).to.be.an('object');
      expect(result.identifier).to.equal('123'); // Should use original value
    });
  });
  
  describe('Performance', () => {
    it('should track performance metrics', async () => {
      const data = { id: '123', name: 'Test' };
      const rules = [
        { source: 'id', target: 'identifier', transform: null },
        { source: 'name', target: 'title', transform: null }
      ];
      
      await mapper.map(data, rules);
      
      const metrics = mapper.getMetrics();
      
      expect(metrics).to.be.an('object');
      expect(metrics.totalMappings).to.equal(1);
      expect(metrics.successfulMappings).to.equal(1);
      expect(metrics.totalMappingTime).to.be.a('number');
      expect(metrics.averageMappingTime).to.be.a('number');
    });
  });
});
