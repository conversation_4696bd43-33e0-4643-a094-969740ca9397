apiVersion: apps/v1
kind: Deployment
metadata:
  name: novafuse-uac
  namespace: novafuse-staging
  labels:
    app: novafuse-uac
    environment: staging
spec:
  replicas: 3
  selector:
    matchLabels:
      app: novafuse-uac
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novafuse-uac
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3001"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: novafuse-uac
        image: gcr.io/${PROJECT_ID}/novafuse-uac:IMAGE_TAG
        imagePullPolicy: Always
        command: ["node", "cluster.js"]
        ports:
        - containerPort: 3001
          name: http
        envFrom:
        - configMapRef:
            name: novafuse-uac-config
        - secretRef:
            name: novafuse-uac-secrets
        resources:
          requests:
            cpu: "200m"
            memory: "256Mi"
          limits:
            cpu: "1000m"
            memory: "1Gi"
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          successThreshold: 1
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: tmp-volume
          mountPath: /tmp
      volumes:
      - name: config-volume
        configMap:
          name: novafuse-uac-config
      - name: tmp-volume
        emptyDir: {}
      terminationGracePeriodSeconds: 60
