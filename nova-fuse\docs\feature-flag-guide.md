# NovaFuse Feature Flag Guide

This guide provides instructions for using the feature flag system in the NovaFuse platform.

## Overview

The NovaFuse platform uses a feature flag system to enable/disable features based on the product tier. This allows for a single codebase to power multiple products with different feature sets.

## Products

NovaFuse offers the following products, each with a different set of features:

1. **NovaPrime**: Comprehensive GRC platform with all features
2. **NovaCore**: Freemium version with limited functionality
3. **NovaShield**: Security-focused product
4. **NovaLearn**: Gamification and education platform
5. **NovaAssistAI**: AI-powered chatbot assistant
6. **NovaMarketplace**: API marketplace and integration platform

## Feature Flag Configuration

Feature flags are defined in the `nova-ui/packages/feature-flags/featureFlags.js` file. The configuration is organized by product, category, and feature:

```javascript
const featureFlags = {
  // NovaPrime features (all enabled)
  novaPrime: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: true,
      esg: true
    },
    
    // Advanced features
    advanced: {
      aiAssistant: true,
      predictiveAnalytics: true,
      automatedRemediation: true,
      customIntegrations: true
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: true,
      organizationSettings: true,
      auditLogs: true
    },
    
    // Learning features
    learning: {
      gamification: true,
      trainingModules: true,
      certifications: true,
      knowledgeBase: true
    }
  },
  
  // NovaCore features (limited)
  novaCore: {
    // ...
  },
  
  // NovaShield features (security-focused)
  novaShield: {
    // ...
  },
  
  // NovaLearn features (learning-focused)
  novaLearn: {
    // ...
  },
  
  // NovaAssistAI features (AI-focused)
  novaAssistAI: {
    // ...
  },
  
  // NovaMarketplace features
  novaMarketplace: {
    // ...
  }
};
```

## Using Feature Flags in React Components

### Basic Usage

The simplest way to use feature flags in React components is with the `useFeatureFlag` hook:

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function AnalyticsPanel() {
  const isAnalyticsEnabled = useFeatureFlag('dashboard', 'analytics');
  
  if (!isAnalyticsEnabled) {
    return null;
  }
  
  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

### Using with Fallback Content

You can provide fallback content when a feature is disabled:

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function AnalyticsPanel() {
  const isAnalyticsEnabled = useFeatureFlag('dashboard', 'analytics');
  
  if (!isAnalyticsEnabled) {
    return (
      <div className="analytics-panel-disabled">
        <p>Analytics are not available in your current plan.</p>
        <button>Upgrade to access Analytics</button>
      </div>
    );
  }
  
  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

### Using the FeatureFlaggedComponent

For a more declarative approach, you can use the `FeatureFlaggedComponent`:

```jsx
import { FeatureFlaggedComponent, UpgradePrompt } from '@nova-ui/feature-flags';

function Dashboard() {
  return (
    <div className="dashboard">
      <FeatureFlaggedComponent
        category="dashboard"
        feature="analytics"
        fallback={<UpgradePrompt featureName="Analytics" />}
      >
        <AnalyticsPanel />
      </FeatureFlaggedComponent>
      
      <FeatureFlaggedComponent
        category="grc"
        feature="privacy"
        fallback={<UpgradePrompt featureName="Privacy Management" />}
      >
        <PrivacyPanel />
      </FeatureFlaggedComponent>
    </div>
  );
}
```

### Advanced Usage

For more advanced use cases, you can use the `useFeatureFlagAdvanced` hook:

```jsx
import { useFeatureFlagAdvanced } from '@nova-ui/feature-flags';

function AnalyticsPanel() {
  const isAnalyticsEnabled = useFeatureFlagAdvanced({
    category: 'dashboard',
    feature: 'analytics',
    fallback: false,
    condition: () => {
      // Additional condition that must be true for the feature to be enabled
      return user.hasPermission('analytics');
    }
  });
  
  if (!isAnalyticsEnabled) {
    return <UpgradePrompt featureName="Analytics" />;
  }
  
  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

### Checking Multiple Features

You can check if any or all of a set of features are enabled:

```jsx
import { useAnyFeatureEnabled, useAllFeaturesEnabled } from '@nova-ui/feature-flags';

function SecurityPanel() {
  const anySecurityFeatureEnabled = useAnyFeatureEnabled([
    { category: 'grc', feature: 'security' },
    { category: 'advanced', feature: 'automatedRemediation' }
  ]);
  
  const allSecurityFeaturesEnabled = useAllFeaturesEnabled([
    { category: 'grc', feature: 'security' },
    { category: 'advanced', feature: 'automatedRemediation' }
  ]);
  
  if (!anySecurityFeatureEnabled) {
    return <UpgradePrompt featureName="Security Features" />;
  }
  
  return (
    <div className="security-panel">
      {/* Basic security content */}
      
      {allSecurityFeaturesEnabled && (
        <div className="advanced-security">
          {/* Advanced security content */}
        </div>
      )}
    </div>
  );
}
```

### Checking for a Specific Product

You can check if the current product is a specific product:

```jsx
import { useIsProduct, PRODUCTS } from '@nova-ui/feature-flags';

function ProductSpecificPanel() {
  const isPrime = useIsProduct(PRODUCTS.NOVA_PRIME);
  const isCore = useIsProduct(PRODUCTS.NOVA_CORE);
  
  return (
    <div className="product-panel">
      {isPrime && <div className="prime-content">NovaPrime Content</div>}
      {isCore && <div className="core-content">NovaCore Content</div>}
    </div>
  );
}
```

## Setting Up the Feature Flag Provider

To use feature flags in your application, you need to wrap your application with the `FeatureFlagProvider`:

```jsx
import { FeatureFlagProvider, PRODUCTS } from '@nova-ui/feature-flags';

function App() {
  return (
    <FeatureFlagProvider initialProduct={PRODUCTS.NOVA_PRIME}>
      <YourApp />
    </FeatureFlagProvider>
  );
}
```

### Custom Feature Flags

You can provide custom feature flags to override the defaults:

```jsx
import { FeatureFlagProvider, PRODUCTS } from '@nova-ui/feature-flags';

function App() {
  const customFlags = {
    novaPrime: {
      dashboard: {
        betaFeature: true
      }
    }
  };
  
  return (
    <FeatureFlagProvider 
      initialProduct={PRODUCTS.NOVA_PRIME}
      customFlags={customFlags}
    >
      <YourApp />
    </FeatureFlagProvider>
  );
}
```

## Switching Products

You can switch between products using the `ProductContext`:

```jsx
import { useContext } from 'react';
import { ProductContext, PRODUCTS } from '@nova-ui/feature-flags';

function ProductSwitcher() {
  const { product, setProduct, isProductActive } = useContext(ProductContext);
  
  return (
    <div className="product-switcher">
      <button 
        onClick={() => setProduct(PRODUCTS.NOVA_PRIME)}
        className={isProductActive(PRODUCTS.NOVA_PRIME) ? 'active' : ''}
      >
        NovaPrime
      </button>
      
      <button 
        onClick={() => setProduct(PRODUCTS.NOVA_CORE)}
        className={isProductActive(PRODUCTS.NOVA_CORE) ? 'active' : ''}
      >
        NovaCore
      </button>
      
      {/* Other product buttons */}
    </div>
  );
}
```

## Using Feature Flags in API Endpoints

Feature flags can also be used in API endpoints to control access to features:

```javascript
const { isFeatureEnabled } = require('@nova-ui/feature-flags');

// Express route handler
app.get('/api/analytics', (req, res) => {
  const product = req.user.product;
  
  if (!isFeatureEnabled(product, 'dashboard', 'analytics')) {
    return res.status(403).json({
      error: 'Feature not available',
      message: 'Analytics are not available in your current plan'
    });
  }
  
  // Process the request
  // ...
  
  res.json({
    // Analytics data
  });
});
```

## Adding New Features

To add a new feature to the feature flag system:

1. Add the feature to the feature flag configuration in `nova-ui/packages/feature-flags/featureFlags.js`:

```javascript
const featureFlags = {
  novaPrime: {
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true,
      newFeature: true  // Add the new feature
    },
    // ...
  },
  novaCore: {
    dashboard: {
      overview: true,
      analytics: false,
      reports: true,
      customization: false,
      newFeature: false  // Add the new feature (disabled for NovaCore)
    },
    // ...
  },
  // ...
};
```

2. Use the feature flag in your components:

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function NewFeatureComponent() {
  const isNewFeatureEnabled = useFeatureFlag('dashboard', 'newFeature');
  
  if (!isNewFeatureEnabled) {
    return null;
  }
  
  return (
    <div className="new-feature">
      {/* New feature content */}
    </div>
  );
}
```

## Adding New Products

To add a new product to the feature flag system:

1. Add the product to the `PRODUCTS` enum in `nova-ui/packages/feature-flags/ProductContext.js`:

```javascript
export const PRODUCTS = {
  NOVA_PRIME: 'novaPrime',
  NOVA_CORE: 'novaCore',
  NOVA_SHIELD: 'novaShield',
  NOVA_LEARN: 'novaLearn',
  NOVA_ASSIST_AI: 'novaAssistAI',
  NOVA_MARKETPLACE: 'novaMarketplace',
  NEW_PRODUCT: 'newProduct'  // Add the new product
};
```

2. Add the product to the feature flag configuration in `nova-ui/packages/feature-flags/featureFlags.js`:

```javascript
const featureFlags = {
  // ...
  
  // New product features
  newProduct: {
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: false
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: false,
      control: false,
      esg: false
    },
    
    // ...
  }
};
```

## Best Practices

### 1. Use Descriptive Feature Names

Use descriptive names for features that clearly indicate what the feature is:

```javascript
// Good
dashboard: {
  analytics: true,
  reports: true,
  customization: true
}

// Bad
dashboard: {
  feature1: true,
  feature2: true,
  feature3: true
}
```

### 2. Organize Features by Category

Organize features into logical categories:

```javascript
// Good
novaPrime: {
  dashboard: { ... },
  grc: { ... },
  advanced: { ... }
}

// Bad
novaPrime: {
  feature1: true,
  feature2: true,
  feature3: true
}
```

### 3. Provide Fallback Content

Always provide fallback content when a feature is disabled:

```jsx
// Good
<FeatureFlaggedComponent
  category="dashboard"
  feature="analytics"
  fallback={<UpgradePrompt featureName="Analytics" />}
>
  <AnalyticsPanel />
</FeatureFlaggedComponent>

// Bad
<FeatureFlaggedComponent
  category="dashboard"
  feature="analytics"
>
  <AnalyticsPanel />
</FeatureFlaggedComponent>
```

### 4. Use Feature Flags for API Endpoints

Use feature flags to control access to API endpoints:

```javascript
// Good
if (!isFeatureEnabled(product, 'dashboard', 'analytics')) {
  return res.status(403).json({
    error: 'Feature not available',
    message: 'Analytics are not available in your current plan'
  });
}

// Bad
if (product !== 'novaPrime') {
  return res.status(403).json({
    error: 'Feature not available',
    message: 'Analytics are not available in your current plan'
  });
}
```

### 5. Test Feature Flags

Test both enabled and disabled states of feature flags:

```javascript
// Good
test('renders analytics panel when feature is enabled', () => {
  // Mock the feature flag hook
  jest.mock('@nova-ui/feature-flags', () => ({
    useFeatureFlag: () => true
  }));

  render(<AnalyticsPanel />);
  expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
});

test('does not render analytics panel when feature is disabled', () => {
  // Mock the feature flag hook
  jest.mock('@nova-ui/feature-flags', () => ({
    useFeatureFlag: () => false
  }));

  render(<AnalyticsPanel />);
  expect(screen.queryByText('Analytics Dashboard')).not.toBeInTheDocument();
});
```

## Conclusion

The NovaFuse feature flag system provides a flexible way to enable/disable features based on the product tier. By using feature flags, you can maintain a single codebase for multiple products, making development and maintenance more efficient.
