// Testnet configuration for multi-node setup
module.exports = {
  nodes: [
    {
      id: 1,
      name: 'node-1',
      port: 8081,
      rpcPort: 8545,
      wsPort: 8546,
      validator: true,
      peers: [2, 3],
      genesis: {
        chainId: 2025,
        timestamp: Math.floor(Date.now() / 1000),
        gasLimit: '0x7a1200',
        difficulty: '0x1',
        extraData: '0x00'
      }
    },
    {
      id: 2,
      name: 'node-2',
      port: 8082,
      rpcPort: 8547,
      wsPort: 8548,
      validator: true,
      peers: [1, 3],
      genesis: {
        chainId: 2025,
        timestamp: Math.floor(Date.now() / 1000),
        gasLimit: '0x7a1200',
        difficulty: '0x1',
        extraData: '0x00'
      }
    },
    {
      id: 3,
      name: 'node-3',
      port: 8083,
      rpcPort: 8549,
      wsPort: 8550,
      validator: true,
      peers: [1, 2],
      genesis: {
        chainId: 2025,
        timestamp: Math.floor(Date.now() / 1000),
        gasLimit: '0x7a1200',
        difficulty: '0x1',
        extraData: '0x00'
      }
    }
  ],
  // Consensus settings
  consensus: {
    type: 'crown',
    epochLength: 100,
    blockTime: 15, // seconds
    minStake: '1000000000000000000' // 1 KET
  },
  // Network settings
  network: {
    maxPeers: 25,
    discovery: true,
    bootnodes: []
  },
  // Logging
  logging: {
    level: 'debug',
    dir: './logs',
    file: 'kethernet.log'
  }
};

// Generate validator keys and peer info for each node
const { generatePrivateKey, getPublicKey, getAddress } = require('./crypto');

// Add validator keys to each node
module.exports.nodes.forEach(node => {
  const privateKey = generatePrivateKey();
  const publicKey = getPublicKey(privateKey);
  const address = getAddress(publicKey);
  
  node.privateKey = privateKey;
  node.publicKey = publicKey;
  node.address = address;
  
  // Add to bootnodes if it's the first node
  if (node.id === 1) {
    module.exports.network.bootnodes.push(`enode://${publicKey}@127.0.0.1:${node.port}`);
  }
});

// Add peer addresses
module.exports.nodes.forEach(node => {
  node.peerAddresses = node.peers.map(peerId => {
    const peer = module.exports.nodes.find(n => n.id === peerId);
    return `enode://${peer.publicKey}@127.0.0.1:${peer.port}`;
  });
});

// Export node configuration for each node
module.exports.getNodeConfig = (nodeId) => {
  const node = module.exports.nodes.find(n => n.id === nodeId);
  if (!node) throw new Error(`Node ${nodeId} not found`);
  
  return {
    node: {
      id: node.id,
      name: node.name,
      port: node.port,
      rpcPort: node.rpcPort,
      wsPort: node.wsPort,
      validator: node.validator,
      privateKey: node.privateKey,
      publicKey: node.publicKey,
      address: node.address,
      peers: node.peerAddresses
    },
    genesis: node.genesis,
    consensus: module.exports.consensus,
    network: {
      ...module.exports.network,
      bootnodes: module.exports.network.bootnodes
    },
    logging: module.exports.logging
  };
};
