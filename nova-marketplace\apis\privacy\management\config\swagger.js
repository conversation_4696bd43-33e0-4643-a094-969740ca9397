/**
 * Swagger Configuration
 * 
 * This file configures Swagger for the Privacy Management API.
 */

const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Swagger definition
const swaggerDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'NovaFuse Privacy Management API',
    version: '1.0.0',
    description: 'API for managing privacy compliance across multiple systems and jurisdictions',
    license: {
      name: 'MIT',
      url: 'https://opensource.org/licenses/MIT'
    },
    contact: {
      name: 'NovaFuse',
      url: 'https://novafuse.com',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: '/api/privacy/management',
      description: 'Privacy Management API'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT'
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ]
};

// Options for the swagger docs
const options = {
  swaggerDefinition,
  // Paths to files containing OpenAPI definitions
  apis: [
    './routes/*.js',
    './models/*.js',
    './controllers/*.js',
    './middleware/*.js',
    './validation/*.js',
    './docs/**/*.yaml'
  ]
};

// Initialize swagger-jsdoc
const swaggerSpec = swaggerJsdoc(options);

// Swagger setup function
const setupSwagger = (app) => {
  // Serve swagger docs
  app.use('/api/privacy/management/docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
  
  // Serve swagger spec as JSON
  app.get('/api/privacy/management/docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });
  
  return app;
};

module.exports = {
  setupSwagger
};
