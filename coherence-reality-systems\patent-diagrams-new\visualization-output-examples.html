<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualization Output Examples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .visualization-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            padding: 10px;
            box-sizing: border-box;
        }
        .visualization-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            text-align: center;
            color: #555555;
        }
        .visualization-content {
            font-size: 12px;
            line-height: 1.4;
        }
        .dashboard-item {
            margin-bottom: 8px;
            padding: 5px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .metric-label {
            font-weight: bold;
            font-size: 11px;
        }
        .metric-value {
            font-size: 11px;
        }
        .progress-bar {
            height: 8px;
            background-color: #e0e0e0;
            border-radius: 4px;
            margin-top: 2px;
        }
        .progress-fill {
            height: 100%;
            background-color: #555555;
            border-radius: 4px;
        }
        .graph {
            width: 100%;
            height: 100px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .graph-line {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 12: Visualization Output Examples</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">VISUALIZATION OUTPUT EXAMPLES: CYBER-SAFETY STATE</div>
        </div>

        <!-- Cyber-Safety Dashboard -->
        <div class="visualization-box" style="left: 50px; top: 80px; width: 340px; height: 220px;">
            <div class="visualization-title">Cyber-Safety Dashboard</div>
            <div class="visualization-content">
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">System Health Score</div>
                        <div class="metric-value">94.7%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 94.7%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Data Purity Score</div>
                        <div class="metric-value">89.2%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 89.2%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Compliance Coverage</div>
                        <div class="metric-value">97.3%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 97.3%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Threat Detection Rate</div>
                        <div class="metric-value">99.1%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 99.1%;"></div>
                    </div>
                </div>
                <div class="dashboard-item">
                    <div class="metric">
                        <div class="metric-label">Adaptive Response Time</div>
                        <div class="metric-value">3.2ms</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 95%;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Field Coherence Map -->
        <div class="visualization-box" style="left: 410px; top: 80px; width: 340px; height: 220px;">
            <div class="visualization-title">Field Coherence Map</div>
            <div class="visualization-content">
                <svg width="320" height="180">
                    <!-- Grid lines -->
                    <line x1="0" y1="45" x2="320" y2="45" stroke="#ddd" stroke-width="1" />
                    <line x1="0" y1="90" x2="320" y2="90" stroke="#ddd" stroke-width="1" />
                    <line x1="0" y1="135" x2="320" y2="135" stroke="#ddd" stroke-width="1" />
                    <line x1="80" y1="0" x2="80" y2="180" stroke="#ddd" stroke-width="1" />
                    <line x1="160" y1="0" x2="160" y2="180" stroke="#ddd" stroke-width="1" />
                    <line x1="240" y1="0" x2="240" y2="180" stroke="#ddd" stroke-width="1" />
                    
                    <!-- Field nodes -->
                    <circle cx="60" cy="50" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="120" cy="80" r="12" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="180" cy="40" r="18" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="240" cy="100" r="14" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="90" cy="140" r="16" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="280" cy="60" r="10" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="200" cy="130" r="13" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    
                    <!-- Connections -->
                    <line x1="60" y1="50" x2="120" y2="80" stroke="#555555" stroke-width="1.5" />
                    <line x1="120" y1="80" x2="180" y2="40" stroke="#555555" stroke-width="1.5" />
                    <line x1="180" y1="40" x2="240" y2="100" stroke="#555555" stroke-width="1.5" />
                    <line x1="240" y1="100" x2="200" y2="130" stroke="#555555" stroke-width="1.5" />
                    <line x1="200" y1="130" x2="90" y2="140" stroke="#555555" stroke-width="1.5" />
                    <line x1="90" y1="140" x2="60" y2="50" stroke="#555555" stroke-width="1.5" />
                    <line x1="180" y1="40" x2="280" y2="60" stroke="#555555" stroke-width="1.5" />
                    <line x1="280" y1="60" x2="240" y2="100" stroke="#555555" stroke-width="1.5" />
                    
                    <!-- Labels -->
                    <text x="60" y="50" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">A</text>
                    <text x="120" y="80" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">B</text>
                    <text x="180" y="40" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">C</text>
                    <text x="240" y="100" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">D</text>
                    <text x="90" y="140" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">E</text>
                    <text x="280" y="60" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">F</text>
                    <text x="200" y="130" text-anchor="middle" dominant-baseline="middle" font-size="8" fill="#333">G</text>
                </svg>
            </div>
        </div>

        <!-- Trinity Visualization -->
        <div class="visualization-box" style="left: 50px; top: 320px; width: 340px; height: 220px;">
            <div class="visualization-title">Trinity Visualization</div>
            <div class="visualization-content">
                <svg width="320" height="180">
                    <!-- Triangle -->
                    <polygon points="160,20 40,160 280,160" fill="none" stroke="#555555" stroke-width="2" />
                    
                    <!-- Inner circles -->
                    <circle cx="160" cy="90" r="30" fill="none" stroke="#555555" stroke-width="1.5" />
                    
                    <!-- Vertices -->
                    <circle cx="160" cy="20" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="40" cy="160" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    <circle cx="280" cy="160" r="15" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    
                    <!-- Center -->
                    <circle cx="160" cy="90" r="20" fill="#f5f5f5" stroke="#555555" stroke-width="2" />
                    
                    <!-- Labels -->
                    <text x="160" y="25" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#333">Truth</text>
                    <text x="40" y="160" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#333">Trust</text>
                    <text x="280" y="160" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#333">Transparency</text>
                    <text x="160" y="90" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#333">UUFT</text>
                </svg>
            </div>
        </div>

        <!-- Adaptive Compliance Visualization -->
        <div class="visualization-box" style="left: 410px; top: 320px; width: 340px; height: 220px;">
            <div class="visualization-title">Adaptive Compliance Visualization</div>
            <div class="visualization-content">
                <div class="graph">
                    <svg width="100%" height="100%" viewBox="0 0 320 100" preserveAspectRatio="none">
                        <!-- Grid lines -->
                        <line x1="0" y1="25" x2="320" y2="25" stroke="#ddd" stroke-width="1" />
                        <line x1="0" y1="50" x2="320" y2="50" stroke="#ddd" stroke-width="1" />
                        <line x1="0" y1="75" x2="320" y2="75" stroke="#ddd" stroke-width="1" />
                        
                        <!-- Compliance threshold line -->
                        <line x1="0" y1="30" x2="320" y2="30" stroke="#555555" stroke-width="1" stroke-dasharray="5,5" />
                        
                        <!-- Compliance level line -->
                        <polyline 
                            points="0,70 40,65 80,40 120,35 160,25 200,20 240,15 280,20 320,25" 
                            fill="none" 
                            stroke="#555555" 
                            stroke-width="2" 
                        />
                        
                        <!-- Adaptive response markers -->
                        <circle cx="80" cy="40" r="4" fill="#555555" />
                        <circle cx="160" cy="25" r="4" fill="#555555" />
                        <circle cx="240" cy="15" r="4" fill="#555555" />
                        
                        <!-- Labels -->
                        <text x="10" y="95" font-size="8" fill="#333">T-24h</text>
                        <text x="310" y="95" font-size="8" fill="#333">Now</text>
                        <text x="325" y="30" font-size="8" fill="#333">Threshold</text>
                    </svg>
                </div>
                <div style="margin-top: 10px; font-size: 11px;">
                    <div style="display: flex; justify-content: space-between;">
                        <div>Current Compliance Level: 95.8%</div>
                        <div>Threshold: 90.0%</div>
                    </div>
                    <div style="margin-top: 5px;">
                        Adaptive Actions: 3 in last 24 hours
                    </div>
                </div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
