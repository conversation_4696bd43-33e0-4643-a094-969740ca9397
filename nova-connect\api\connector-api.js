/**
 * NovaFuse Universal API Connector API
 *
 * This module provides a REST API for managing and executing connectors.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const http = require('http');
const connectorRegistry = require('../registry/connector-registry');
const authenticationManager = require('../auth/authentication-manager');
const connectorExecutor = require('../executor/connector-executor');
const createGraphQLServer = require('./graphql/server');

// Import routes
const rbacRoutes = require('./routes/rbacRoutes');
const billingRoutes = require('./routes/billingRoutes');
const marketplaceRoutes = require('./routes/marketplaceRoutes');

class ConnectorApi {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3010;

    // Configure middleware
    this.app.use(cors());
    this.app.use(bodyParser.json());

    // Register routes
    this.registerRoutes();
  }

  /**
   * Initialize the API
   */
  async initialize() {
    try {
      // Initialize dependencies
      await connectorRegistry.initialize();
      await authenticationManager.initialize();
      await connectorExecutor.initialize();

      // Initialize services
      const MarketplaceService = require('./services/MarketplaceService');
      const marketplaceService = new MarketplaceService();
      await marketplaceService.initialize();

      const BillingService = require('./services/BillingService');
      const billingService = new BillingService();
      await billingService.initialize();

      // Create HTTP server
      const httpServer = http.createServer(this.app);

      // Create GraphQL server
      const { server: graphqlServer } = await createGraphQLServer(this.app, { httpServer });
      this.graphqlServer = graphqlServer;

      // Start the server
      this.server = httpServer.listen(this.port, () => {
        console.log(`Connector API listening on port ${this.port}`);
        console.log(`GraphQL endpoint: http://localhost:${this.port}${graphqlServer.graphqlPath}`);
        console.log(`GraphQL subscriptions: ws://localhost:${this.port}${graphqlServer.graphqlPath}`);
      });

      return true;
    } catch (error) {
      console.error('Failed to initialize Connector API:', error);
      throw error;
    }
  }

  /**
   * Register API routes
   */
  registerRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.status(200).json({ status: 'ok' });
    });

    // Connector routes
    this.app.get('/connectors', this.getConnectors.bind(this));
    this.app.get('/connectors/:id', this.getConnector.bind(this));
    this.app.post('/connectors', this.registerConnector.bind(this));
    this.app.put('/connectors/:id', this.updateConnector.bind(this));
    this.app.delete('/connectors/:id', this.deleteConnector.bind(this));

    // Credential routes
    this.app.post('/credentials', this.storeCredentials.bind(this));
    this.app.delete('/credentials/:id', this.deleteCredentials.bind(this));
    this.app.post('/credentials/:id/test', this.testConnection.bind(this));

    // Execution routes
    this.app.post('/execute/:connectorId/:endpointId', this.executeEndpoint.bind(this));

    // RBAC routes
    this.app.use('/rbac', rbacRoutes);

    // Billing routes
    this.app.use('/billing', billingRoutes);

    // Marketplace routes
    this.app.use('/marketplace', marketplaceRoutes);
  }

  /**
   * Get all connectors
   */
  getConnectors(req, res) {
    try {
      const { category, query } = req.query;

      let connectors;

      if (category) {
        connectors = connectorRegistry.getConnectorsByCategory(category);
      } else if (query) {
        connectors = connectorRegistry.searchConnectors(query);
      } else {
        connectors = connectorRegistry.getAllConnectors();
      }

      // Remove sensitive information
      const sanitizedConnectors = connectors.map(connector => ({
        ...connector,
        authentication: {
          ...connector.authentication,
          fields: Object.entries(connector.authentication.fields).reduce((acc, [key, field]) => {
            acc[key] = {
              ...field,
              default: field.sensitive ? undefined : field.default
            };
            return acc;
          }, {})
        }
      }));

      res.status(200).json(sanitizedConnectors);
    } catch (error) {
      console.error('Error getting connectors:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Get a connector by ID
   */
  getConnector(req, res) {
    try {
      const { id } = req.params;
      const connector = connectorRegistry.getConnector(id);

      if (!connector) {
        return res.status(404).json({ error: `Connector '${id}' not found` });
      }

      // Remove sensitive information
      const sanitizedConnector = {
        ...connector,
        authentication: {
          ...connector.authentication,
          fields: Object.entries(connector.authentication.fields).reduce((acc, [key, field]) => {
            acc[key] = {
              ...field,
              default: field.sensitive ? undefined : field.default
            };
            return acc;
          }, {})
        }
      };

      res.status(200).json(sanitizedConnector);
    } catch (error) {
      console.error('Error getting connector:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Register a new connector
   */
  async registerConnector(req, res) {
    try {
      const connector = req.body;

      if (!connector) {
        return res.status(400).json({ error: 'Connector template is required' });
      }

      await connectorRegistry.registerConnector(connector);

      res.status(201).json({ message: 'Connector registered successfully' });
    } catch (error) {
      console.error('Error registering connector:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Update an existing connector
   */
  async updateConnector(req, res) {
    try {
      const { id } = req.params;
      const connector = req.body;

      if (!connector) {
        return res.status(400).json({ error: 'Connector template is required' });
      }

      await connectorRegistry.updateConnector(id, connector);

      res.status(200).json({ message: 'Connector updated successfully' });
    } catch (error) {
      console.error('Error updating connector:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete a connector
   */
  async deleteConnector(req, res) {
    try {
      const { id } = req.params;

      await connectorRegistry.deleteConnector(id);

      res.status(200).json({ message: 'Connector deleted successfully' });
    } catch (error) {
      console.error('Error deleting connector:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Store credentials for a connector
   */
  storeCredentials(req, res) {
    try {
      const { connectorId, credentials } = req.body;

      if (!connectorId || !credentials) {
        return res.status(400).json({ error: 'Connector ID and credentials are required' });
      }

      const credentialId = authenticationManager.storeCredentials(connectorId, credentials);

      res.status(201).json({ credentialId });
    } catch (error) {
      console.error('Error storing credentials:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Delete credentials
   */
  deleteCredentials(req, res) {
    try {
      const { id } = req.params;

      const success = authenticationManager.deleteCredentials(id);

      if (!success) {
        return res.status(404).json({ error: `Credentials '${id}' not found` });
      }

      res.status(200).json({ message: 'Credentials deleted successfully' });
    } catch (error) {
      console.error('Error deleting credentials:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Test a connection using stored credentials
   */
  async testConnection(req, res) {
    try {
      const { id } = req.params;
      const { connectorId } = req.body;

      if (!connectorId) {
        return res.status(400).json({ error: 'Connector ID is required' });
      }

      const connector = connectorRegistry.getConnector(connectorId);

      if (!connector) {
        return res.status(404).json({ error: `Connector '${connectorId}' not found` });
      }

      const credentials = authenticationManager.getCredentials(id);

      if (!credentials) {
        return res.status(404).json({ error: `Credentials '${id}' not found` });
      }

      const result = await authenticationManager.testConnection(connector, credentials);

      res.status(200).json(result);
    } catch (error) {
      console.error('Error testing connection:', error);
      res.status(500).json({ error: error.message });
    }
  }

  /**
   * Execute an endpoint
   */
  async executeEndpoint(req, res) {
    try {
      const { connectorId, endpointId } = req.params;
      const { credentialId, parameters } = req.body;

      if (!credentialId) {
        return res.status(400).json({ error: 'Credential ID is required' });
      }

      const result = await connectorExecutor.executeEndpoint(connectorId, endpointId, credentialId, parameters);

      res.status(200).json(result);
    } catch (error) {
      console.error('Error executing endpoint:', error);
      res.status(500).json({ error: error.message });
    }
  }
}

// Create and export a singleton instance
const connectorApi = new ConnectorApi();

module.exports = connectorApi;
