/**
 * Fuzz Testing Framework for NovaConnect Universal API Connector
 * 
 * This module provides fuzz testing capabilities to identify unexpected behaviors
 * by sending random, malformed, or unexpected inputs to API endpoints.
 */

const { randomBytes } = require('crypto');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Ensure reports directory exists
const reportsDir = path.join(__dirname, '../reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir, { recursive: true });
}

/**
 * Generate random data for fuzzing
 * 
 * @param {string} type - Type of data to generate ('string', 'number', 'boolean', 'null', 'object', 'array', 'malformed')
 * @param {Object} options - Options for data generation
 * @returns {*} - Random data
 */
function generateRandomData(type = 'random', options = {}) {
  // If type is random, choose a random type
  if (type === 'random') {
    const types = ['string', 'number', 'boolean', 'null', 'object', 'array', 'malformed'];
    type = types[Math.floor(Math.random() * types.length)];
  }
  
  switch (type) {
    case 'string':
      if (Math.random() < 0.3) {
        // Sometimes generate very long strings
        return randomBytes(options.length || 1000).toString('base64');
      } else if (Math.random() < 0.3) {
        // Sometimes generate special characters
        return '!@#$%^&*()_+{}|:"<>?[]\\;\',./`~'.repeat(Math.floor(Math.random() * 10) + 1);
      } else {
        // Normal random string
        return randomBytes(options.length || 16).toString('hex');
      }
    
    case 'number':
      if (Math.random() < 0.3) {
        // Sometimes generate very large numbers
        return Math.pow(10, Math.floor(Math.random() * 10) + 10);
      } else if (Math.random() < 0.3) {
        // Sometimes generate negative numbers
        return -Math.random() * 1000;
      } else {
        // Normal random number
        return Math.random() * 1000;
      }
    
    case 'boolean':
      return Math.random() < 0.5;
    
    case 'null':
      return null;
    
    case 'object':
      const obj = {};
      const keyCount = options.keyCount || Math.floor(Math.random() * 10) + 1;
      
      for (let i = 0; i < keyCount; i++) {
        const key = randomBytes(8).toString('hex');
        obj[key] = generateRandomData('random', { depth: (options.depth || 0) + 1 });
      }
      
      return obj;
    
    case 'array':
      const arr = [];
      const itemCount = options.itemCount || Math.floor(Math.random() * 10) + 1;
      
      for (let i = 0; i < itemCount; i++) {
        arr.push(generateRandomData('random', { depth: (options.depth || 0) + 1 }));
      }
      
      return arr;
    
    case 'malformed':
      // Generate malformed data that might break JSON parsing
      if (Math.random() < 0.5) {
        return '{invalid:json}';
      } else {
        return 'function() { return "malicious"; }';
      }
    
    default:
      return randomBytes(16).toString('hex');
  }
}

/**
 * Generate a random payload for API testing
 * 
 * @param {Object} template - Template object with field types
 * @returns {Object} - Random payload
 */
function generateRandomPayload(template = {}) {
  const payload = {};
  
  // If template is provided, use it to generate structured random data
  if (Object.keys(template).length > 0) {
    for (const [key, type] of Object.entries(template)) {
      payload[key] = generateRandomData(type);
    }
  } else {
    // Generate completely random payload
    const keyCount = Math.floor(Math.random() * 10) + 1;
    
    for (let i = 0; i < keyCount; i++) {
      const key = randomBytes(8).toString('hex');
      payload[key] = generateRandomData('random');
    }
    
    // Add some common field names that might be handled specially
    if (Math.random() < 0.5) payload.id = generateRandomData('string');
    if (Math.random() < 0.5) payload.name = generateRandomData('string');
    if (Math.random() < 0.5) payload.credentials = generateRandomData('object');
    if (Math.random() < 0.5) payload.parameters = generateRandomData('object');
    if (Math.random() < 0.5) payload.userId = generateRandomData('string');
  }
  
  return payload;
}

/**
 * Fuzz test an endpoint
 * 
 * @param {Object} options - Test options
 * @param {string} options.url - The endpoint URL
 * @param {string} options.method - The HTTP method
 * @param {Object} options.template - Template for payload generation
 * @param {number} options.iterations - Number of test iterations
 * @param {Object} options.headers - Headers to include in requests
 * @returns {Object} - Test results
 */
async function fuzzEndpoint(options) {
  const {
    url,
    method = 'POST',
    template = {},
    iterations = 100,
    headers = {}
  } = options;
  
  const results = {
    url,
    method,
    iterations,
    startTime: new Date().toISOString(),
    endTime: null,
    summary: {
      total: iterations,
      passed: 0,
      clientErrors: 0, // 4xx errors (expected for invalid input)
      serverErrors: 0, // 5xx errors (potential issues)
      timeouts: 0,
      otherErrors: 0
    },
    serverErrors: [], // Detailed info about server errors
    timeouts: [], // Detailed info about timeouts
    otherErrors: [] // Detailed info about other errors
  };
  
  for (let i = 0; i < iterations; i++) {
    try {
      // Generate random payload
      const payload = generateRandomPayload(template);
      
      // Make request
      await axios({
        method,
        url,
        data: payload,
        headers,
        timeout: 5000 // 5 second timeout
      });
      
      // If we get here, the request was successful
      results.summary.passed++;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        if (error.response.status >= 400 && error.response.status < 500) {
          // Client errors (4xx) are expected for invalid input
          results.summary.clientErrors++;
        } else if (error.response.status >= 500) {
          // Server errors (5xx) might indicate issues
          results.summary.serverErrors++;
          results.serverErrors.push({
            iteration: i,
            status: error.response.status,
            statusText: error.response.statusText,
            data: error.response.data
          });
        }
      } else if (error.code === 'ECONNABORTED') {
        // Timeout errors
        results.summary.timeouts++;
        results.timeouts.push({
          iteration: i,
          message: error.message
        });
      } else {
        // Other errors
        results.summary.otherErrors++;
        results.otherErrors.push({
          iteration: i,
          message: error.message,
          code: error.code
        });
      }
    }
  }
  
  results.endTime = new Date().toISOString();
  
  // Calculate success rate
  results.summary.successRate = ((results.summary.passed + results.summary.clientErrors) / results.summary.total) * 100;
  
  return results;
}

/**
 * Run a fuzz test suite against multiple endpoints
 * 
 * @param {Array} endpoints - Array of endpoint configurations
 * @param {Object} options - Global options for all endpoints
 * @returns {Object} - Test results
 */
async function runFuzzTestSuite(endpoints, options = {}) {
  const suiteResults = {
    startTime: new Date().toISOString(),
    endTime: null,
    endpoints: [],
    summary: {
      total: endpoints.length,
      passed: 0,
      failed: 0
    }
  };
  
  for (const endpoint of endpoints) {
    console.log(`Fuzzing endpoint: ${endpoint.method || 'POST'} ${endpoint.url}`);
    
    // Merge global options with endpoint-specific options
    const testOptions = {
      ...options,
      ...endpoint
    };
    
    // Run fuzz test for this endpoint
    const result = await fuzzEndpoint(testOptions);
    suiteResults.endpoints.push(result);
    
    // Check if the endpoint passed
    // We consider an endpoint to have passed if it has no server errors
    if (result.summary.serverErrors === 0) {
      suiteResults.summary.passed++;
    } else {
      suiteResults.summary.failed++;
    }
    
    console.log(`  Results: ${result.summary.passed} passed, ${result.summary.clientErrors} client errors, ${result.summary.serverErrors} server errors`);
  }
  
  suiteResults.endTime = new Date().toISOString();
  
  // Save results to file
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportsDir, `fuzz-test-report-${timestamp}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(suiteResults, null, 2));
  
  console.log(`\nFuzz testing complete. Report saved to: ${reportPath}`);
  console.log(`Summary: ${suiteResults.summary.passed}/${suiteResults.summary.total} endpoints passed`);
  
  return suiteResults;
}

module.exports = {
  generateRandomData,
  generateRandomPayload,
  fuzzEndpoint,
  runFuzzTestSuite
};
