"""
Control Mapping Manager for the Universal Compliance Tracking Optimizer.

This module provides functionality for mapping controls across different compliance frameworks.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Set

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ControlMappingManager:
    """
    Manager for compliance control mapping.
    
    This class is responsible for mapping controls across different compliance frameworks
    to identify overlaps and optimize compliance efforts.
    """
    
    def __init__(self, mapping_dir: Optional[str] = None):
        """
        Initialize the Control Mapping Manager.
        
        Args:
            mapping_dir: Path to a directory for storing mapping information
        """
        logger.info("Initializing Control Mapping Manager")
        
        # Set the mapping directory
        self.mapping_dir = mapping_dir or os.path.join(os.getcwd(), 'mapping_data')
        
        # Create the mapping directory if it doesn't exist
        os.makedirs(self.mapping_dir, exist_ok=True)
        
        # Dictionary to store frameworks in memory
        self.frameworks: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store controls in memory
        self.controls: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store mappings in memory
        self.mappings: Dict[str, Dict[str, Any]] = {}
        
        # Load data from disk
        self._load_data_from_disk()
        
        logger.info(f"Control Mapping Manager initialized with {len(self.frameworks)} frameworks, {len(self.controls)} controls, and {len(self.mappings)} mappings")
    
    def create_framework(self, framework_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new compliance framework.
        
        Args:
            framework_data: The framework data
            
        Returns:
            The created framework
            
        Raises:
            ValueError: If the framework data is invalid
        """
        logger.info("Creating new compliance framework")
        
        # Validate the framework data
        self._validate_framework_data(framework_data)
        
        # Generate a unique framework ID if not provided
        framework_id = framework_data.get('id') or str(uuid.uuid4())
        
        # Create the framework object
        framework = {
            'id': framework_id,
            'name': framework_data['name'],
            'description': framework_data.get('description', ''),
            'version': framework_data.get('version', '1.0'),
            'category': framework_data.get('category', ''),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the framework in memory
        self.frameworks[framework_id] = framework
        
        # Store the framework on disk
        self._save_framework_to_disk(framework)
        
        logger.info(f"Framework created: {framework_id}")
        
        return framework
    
    def get_framework(self, framework_id: str) -> Dict[str, Any]:
        """
        Get a compliance framework.
        
        Args:
            framework_id: The ID of the framework
            
        Returns:
            The framework
            
        Raises:
            ValueError: If the framework does not exist
        """
        logger.info(f"Getting framework: {framework_id}")
        
        if framework_id not in self.frameworks:
            raise ValueError(f"Framework not found: {framework_id}")
        
        return self.frameworks[framework_id]
    
    def create_control(self, control_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new compliance control.
        
        Args:
            control_data: The control data
            
        Returns:
            The created control
            
        Raises:
            ValueError: If the control data is invalid
        """
        logger.info("Creating new compliance control")
        
        # Validate the control data
        self._validate_control_data(control_data)
        
        # Generate a unique control ID if not provided
        control_id = control_data.get('id') or str(uuid.uuid4())
        
        # Create the control object
        control = {
            'id': control_id,
            'framework_id': control_data['framework_id'],
            'name': control_data['name'],
            'description': control_data.get('description', ''),
            'identifier': control_data.get('identifier', ''),
            'category': control_data.get('category', ''),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the control in memory
        self.controls[control_id] = control
        
        # Store the control on disk
        self._save_control_to_disk(control)
        
        logger.info(f"Control created: {control_id}")
        
        return control
    
    def get_control(self, control_id: str) -> Dict[str, Any]:
        """
        Get a compliance control.
        
        Args:
            control_id: The ID of the control
            
        Returns:
            The control
            
        Raises:
            ValueError: If the control does not exist
        """
        logger.info(f"Getting control: {control_id}")
        
        if control_id not in self.controls:
            raise ValueError(f"Control not found: {control_id}")
        
        return self.controls[control_id]
    
    def create_mapping(self, mapping_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new control mapping.
        
        Args:
            mapping_data: The mapping data
            
        Returns:
            The created mapping
            
        Raises:
            ValueError: If the mapping data is invalid
        """
        logger.info("Creating new control mapping")
        
        # Validate the mapping data
        self._validate_mapping_data(mapping_data)
        
        # Generate a unique mapping ID
        mapping_id = str(uuid.uuid4())
        
        # Create the mapping object
        mapping = {
            'id': mapping_id,
            'source_control_id': mapping_data['source_control_id'],
            'target_control_id': mapping_data['target_control_id'],
            'strength': mapping_data.get('strength', 'full'),
            'notes': mapping_data.get('notes', ''),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the mapping in memory
        self.mappings[mapping_id] = mapping
        
        # Store the mapping on disk
        self._save_mapping_to_disk(mapping)
        
        logger.info(f"Mapping created: {mapping_id}")
        
        return mapping
    
    def get_mapping(self, mapping_id: str) -> Dict[str, Any]:
        """
        Get a control mapping.
        
        Args:
            mapping_id: The ID of the mapping
            
        Returns:
            The mapping
            
        Raises:
            ValueError: If the mapping does not exist
        """
        logger.info(f"Getting mapping: {mapping_id}")
        
        if mapping_id not in self.mappings:
            raise ValueError(f"Mapping not found: {mapping_id}")
        
        return self.mappings[mapping_id]
    
    def get_framework_controls(self, framework_id: str) -> List[Dict[str, Any]]:
        """
        Get all controls for a framework.
        
        Args:
            framework_id: The ID of the framework
            
        Returns:
            List of controls for the framework
        """
        logger.info(f"Getting controls for framework: {framework_id}")
        
        return [c for c in self.controls.values() if c.get('framework_id') == framework_id]
    
    def get_control_mappings(self, control_id: str, direction: str = 'both') -> List[Dict[str, Any]]:
        """
        Get all mappings for a control.
        
        Args:
            control_id: The ID of the control
            direction: The mapping direction ('source', 'target', or 'both')
            
        Returns:
            List of mappings for the control
        """
        logger.info(f"Getting mappings for control: {control_id}")
        
        if direction == 'source':
            return [m for m in self.mappings.values() if m.get('source_control_id') == control_id]
        elif direction == 'target':
            return [m for m in self.mappings.values() if m.get('target_control_id') == control_id]
        else:  # 'both'
            return [m for m in self.mappings.values() 
                   if m.get('source_control_id') == control_id or m.get('target_control_id') == control_id]
    
    def get_mapped_controls(self, control_id: str, direction: str = 'both') -> List[Dict[str, Any]]:
        """
        Get all controls mapped to a control.
        
        Args:
            control_id: The ID of the control
            direction: The mapping direction ('source', 'target', or 'both')
            
        Returns:
            List of mapped controls
        """
        logger.info(f"Getting mapped controls for control: {control_id}")
        
        # Get mappings for the control
        mappings = self.get_control_mappings(control_id, direction)
        
        # Get mapped control IDs
        mapped_control_ids = set()
        for mapping in mappings:
            if mapping.get('source_control_id') == control_id:
                mapped_control_ids.add(mapping.get('target_control_id'))
            elif mapping.get('target_control_id') == control_id:
                mapped_control_ids.add(mapping.get('source_control_id'))
        
        # Get mapped controls
        mapped_controls = []
        for mapped_id in mapped_control_ids:
            if mapped_id in self.controls:
                mapped_controls.append(self.controls[mapped_id])
        
        return mapped_controls
    
    def get_framework_mappings(self, source_framework_id: str, target_framework_id: str) -> List[Dict[str, Any]]:
        """
        Get all mappings between two frameworks.
        
        Args:
            source_framework_id: The ID of the source framework
            target_framework_id: The ID of the target framework
            
        Returns:
            List of mappings between the frameworks
        """
        logger.info(f"Getting mappings between frameworks: {source_framework_id} and {target_framework_id}")
        
        # Get controls for each framework
        source_controls = self.get_framework_controls(source_framework_id)
        target_controls = self.get_framework_controls(target_framework_id)
        
        # Get control IDs
        source_control_ids = [c.get('id') for c in source_controls]
        target_control_ids = [c.get('id') for c in target_controls]
        
        # Get mappings between the frameworks
        framework_mappings = []
        for mapping in self.mappings.values():
            source_id = mapping.get('source_control_id')
            target_id = mapping.get('target_control_id')
            
            if source_id in source_control_ids and target_id in target_control_ids:
                framework_mappings.append(mapping)
        
        return framework_mappings
    
    def calculate_framework_coverage(self, source_framework_id: str, target_framework_id: str) -> Dict[str, Any]:
        """
        Calculate the coverage between two frameworks.
        
        Args:
            source_framework_id: The ID of the source framework
            target_framework_id: The ID of the target framework
            
        Returns:
            Coverage information
        """
        logger.info(f"Calculating coverage between frameworks: {source_framework_id} and {target_framework_id}")
        
        # Get controls for each framework
        source_controls = self.get_framework_controls(source_framework_id)
        target_controls = self.get_framework_controls(target_framework_id)
        
        # Get mappings between the frameworks
        framework_mappings = self.get_framework_mappings(source_framework_id, target_framework_id)
        
        # Calculate coverage
        mapped_source_control_ids = set()
        mapped_target_control_ids = set()
        
        for mapping in framework_mappings:
            mapped_source_control_ids.add(mapping.get('source_control_id'))
            mapped_target_control_ids.add(mapping.get('target_control_id'))
        
        source_coverage = len(mapped_source_control_ids) / len(source_controls) if source_controls else 0
        target_coverage = len(mapped_target_control_ids) / len(target_controls) if target_controls else 0
        
        # Create the coverage information
        coverage = {
            'source_framework_id': source_framework_id,
            'target_framework_id': target_framework_id,
            'source_controls': len(source_controls),
            'target_controls': len(target_controls),
            'mapped_source_controls': len(mapped_source_control_ids),
            'mapped_target_controls': len(mapped_target_control_ids),
            'source_coverage': source_coverage,
            'target_coverage': target_coverage,
            'mappings': len(framework_mappings)
        }
        
        return coverage
    
    def _validate_framework_data(self, framework_data: Dict[str, Any]) -> None:
        """
        Validate framework data.
        
        Args:
            framework_data: The framework data to validate
            
        Raises:
            ValueError: If the framework data is invalid
        """
        # Check required fields
        if 'name' not in framework_data:
            raise ValueError("Framework name is required")
    
    def _validate_control_data(self, control_data: Dict[str, Any]) -> None:
        """
        Validate control data.
        
        Args:
            control_data: The control data to validate
            
        Raises:
            ValueError: If the control data is invalid
        """
        # Check required fields
        if 'name' not in control_data:
            raise ValueError("Control name is required")
        
        if 'framework_id' not in control_data:
            raise ValueError("Framework ID is required")
        
        # Check if the framework exists
        framework_id = control_data['framework_id']
        if framework_id not in self.frameworks:
            raise ValueError(f"Framework not found: {framework_id}")
    
    def _validate_mapping_data(self, mapping_data: Dict[str, Any]) -> None:
        """
        Validate mapping data.
        
        Args:
            mapping_data: The mapping data to validate
            
        Raises:
            ValueError: If the mapping data is invalid
        """
        # Check required fields
        if 'source_control_id' not in mapping_data:
            raise ValueError("Source control ID is required")
        
        if 'target_control_id' not in mapping_data:
            raise ValueError("Target control ID is required")
        
        # Check if the controls exist
        source_control_id = mapping_data['source_control_id']
        if source_control_id not in self.controls:
            raise ValueError(f"Source control not found: {source_control_id}")
        
        target_control_id = mapping_data['target_control_id']
        if target_control_id not in self.controls:
            raise ValueError(f"Target control not found: {target_control_id}")
        
        # Check if the mapping already exists
        for mapping in self.mappings.values():
            if (mapping.get('source_control_id') == source_control_id and 
                mapping.get('target_control_id') == target_control_id):
                raise ValueError(f"Mapping already exists between {source_control_id} and {target_control_id}")
        
        # Validate strength if provided
        if 'strength' in mapping_data:
            valid_strengths = ['full', 'partial', 'related']
            if mapping_data['strength'] not in valid_strengths:
                raise ValueError(f"Invalid mapping strength: {mapping_data['strength']}")
    
    def _load_data_from_disk(self) -> None:
        """Load frameworks, controls, and mappings from disk."""
        # Load frameworks
        self._load_frameworks_from_disk()
        
        # Load controls
        self._load_controls_from_disk()
        
        # Load mappings
        self._load_mappings_from_disk()
    
    def _load_frameworks_from_disk(self) -> None:
        """Load frameworks from disk."""
        try:
            # Create the frameworks directory if it doesn't exist
            frameworks_dir = os.path.join(self.mapping_dir, 'frameworks')
            os.makedirs(frameworks_dir, exist_ok=True)
            
            # Get all JSON files in the frameworks directory
            framework_files = [f for f in os.listdir(frameworks_dir) if f.endswith('.json')]
            
            for framework_file in framework_files:
                try:
                    # Load the framework from disk
                    file_path = os.path.join(frameworks_dir, framework_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        framework = json.load(f)
                    
                    # Store the framework in memory
                    framework_id = framework.get('id')
                    
                    if framework_id:
                        self.frameworks[framework_id] = framework
                        logger.info(f"Loaded framework from disk: {framework_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load framework from {framework_file}: {e}")
            
            logger.info(f"Loaded {len(self.frameworks)} frameworks from disk")
        
        except Exception as e:
            logger.error(f"Failed to load frameworks from disk: {e}")
    
    def _load_controls_from_disk(self) -> None:
        """Load controls from disk."""
        try:
            # Create the controls directory if it doesn't exist
            controls_dir = os.path.join(self.mapping_dir, 'controls')
            os.makedirs(controls_dir, exist_ok=True)
            
            # Get all JSON files in the controls directory
            control_files = [f for f in os.listdir(controls_dir) if f.endswith('.json')]
            
            for control_file in control_files:
                try:
                    # Load the control from disk
                    file_path = os.path.join(controls_dir, control_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        control = json.load(f)
                    
                    # Store the control in memory
                    control_id = control.get('id')
                    
                    if control_id:
                        self.controls[control_id] = control
                        logger.info(f"Loaded control from disk: {control_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load control from {control_file}: {e}")
            
            logger.info(f"Loaded {len(self.controls)} controls from disk")
        
        except Exception as e:
            logger.error(f"Failed to load controls from disk: {e}")
    
    def _load_mappings_from_disk(self) -> None:
        """Load mappings from disk."""
        try:
            # Create the mappings directory if it doesn't exist
            mappings_dir = os.path.join(self.mapping_dir, 'mappings')
            os.makedirs(mappings_dir, exist_ok=True)
            
            # Get all JSON files in the mappings directory
            mapping_files = [f for f in os.listdir(mappings_dir) if f.endswith('.json')]
            
            for mapping_file in mapping_files:
                try:
                    # Load the mapping from disk
                    file_path = os.path.join(mappings_dir, mapping_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        mapping = json.load(f)
                    
                    # Store the mapping in memory
                    mapping_id = mapping.get('id')
                    
                    if mapping_id:
                        self.mappings[mapping_id] = mapping
                        logger.info(f"Loaded mapping from disk: {mapping_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load mapping from {mapping_file}: {e}")
            
            logger.info(f"Loaded {len(self.mappings)} mappings from disk")
        
        except Exception as e:
            logger.error(f"Failed to load mappings from disk: {e}")
    
    def _save_framework_to_disk(self, framework: Dict[str, Any]) -> None:
        """
        Save a framework to disk.
        
        Args:
            framework: The framework to save
        """
        try:
            # Get the framework ID
            framework_id = framework.get('id')
            
            if not framework_id:
                raise ValueError("Framework ID is missing")
            
            # Create the frameworks directory if it doesn't exist
            frameworks_dir = os.path.join(self.mapping_dir, 'frameworks')
            os.makedirs(frameworks_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(frameworks_dir, f"{framework_id}.json")
            
            # Save the framework to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(framework, f, indent=2)
            
            logger.info(f"Saved framework to disk: {framework_id}")
        
        except Exception as e:
            logger.error(f"Failed to save framework to disk: {e}")
    
    def _save_control_to_disk(self, control: Dict[str, Any]) -> None:
        """
        Save a control to disk.
        
        Args:
            control: The control to save
        """
        try:
            # Get the control ID
            control_id = control.get('id')
            
            if not control_id:
                raise ValueError("Control ID is missing")
            
            # Create the controls directory if it doesn't exist
            controls_dir = os.path.join(self.mapping_dir, 'controls')
            os.makedirs(controls_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(controls_dir, f"{control_id}.json")
            
            # Save the control to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(control, f, indent=2)
            
            logger.info(f"Saved control to disk: {control_id}")
        
        except Exception as e:
            logger.error(f"Failed to save control to disk: {e}")
    
    def _save_mapping_to_disk(self, mapping: Dict[str, Any]) -> None:
        """
        Save a mapping to disk.
        
        Args:
            mapping: The mapping to save
        """
        try:
            # Get the mapping ID
            mapping_id = mapping.get('id')
            
            if not mapping_id:
                raise ValueError("Mapping ID is missing")
            
            # Create the mappings directory if it doesn't exist
            mappings_dir = os.path.join(self.mapping_dir, 'mappings')
            os.makedirs(mappings_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(mappings_dir, f"{mapping_id}.json")
            
            # Save the mapping to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(mapping, f, indent=2)
            
            logger.info(f"Saved mapping to disk: {mapping_id}")
        
        except Exception as e:
            logger.error(f"Failed to save mapping to disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
