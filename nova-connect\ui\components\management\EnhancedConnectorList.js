/**
 * Enhanced Connector List Component
 * 
 * This component provides an improved interface for viewing and managing connectors,
 * with both table and card views, advanced filtering, and batch operations.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  CardActions,
  Checkbox,
  Chip, 
  Divider, 
  FormControl,
  FormControlLabel,
  Grid, 
  IconButton, 
  InputAdornment, 
  Menu, 
  MenuItem, 
  Paper, 
  Select,
  Snackbar,
  Stack,
  Switch,
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TablePagination, 
  TableRow, 
  TableSortLabel, 
  TextField, 
  Tooltip, 
  Typography,
  Alert,
  LinearProgress,
  Fade,
  Zoom
} from '@mui/material';

// Icons
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import VisibilityIcon from '@mui/icons-material/Visibility';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import GridViewIcon from '@mui/icons-material/GridView';
import ViewListIcon from '@mui/icons-material/ViewList';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import RefreshIcon from '@mui/icons-material/Refresh';
import DownloadIcon from '@mui/icons-material/Download';
import UploadIcon from '@mui/icons-material/Upload';
import DeleteSweepIcon from '@mui/icons-material/DeleteSweep';
import { useRouter } from 'next/router';

const EnhancedConnectorList = ({ connectors, onDelete, onDuplicate, loading = false, onRefresh }) => {
  const router = useRouter();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchQuery, setSearchQuery] = useState('');
  const [orderBy, setOrderBy] = useState('name');
  const [order, setOrder] = useState('asc');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [filters, setFilters] = useState({
    category: 'all',
    status: 'all',
    type: 'all'
  });
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState(null);
  const [selectedConnector, setSelectedConnector] = useState(null);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'card'
  const [selectedConnectors, setSelectedConnectors] = useState([]);
  const [batchMenuAnchorEl, setBatchMenuAnchorEl] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  
  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // Handle search query change
  const handleSearchChange = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };
  
  // Handle sort request
  const handleRequestSort = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };
  
  // Handle filter click
  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };
  
  // Handle filter close
  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };
  
  // Handle filter change
  const handleFilterChange = (event) => {
    setFilters({
      ...filters,
      [event.target.name]: event.target.value
    });
    setPage(0);
  };
  
  // Handle action menu open
  const handleActionMenuOpen = (event, connector) => {
    setActionMenuAnchorEl(event.currentTarget);
    setSelectedConnector(connector);
  };
  
  // Handle action menu close
  const handleActionMenuClose = () => {
    setActionMenuAnchorEl(null);
    setSelectedConnector(null);
  };
  
  // Handle view connector
  const handleView = () => {
    if (selectedConnector) {
      router.push(`/connectors/${selectedConnector.id}`);
    }
    handleActionMenuClose();
  };
  
  // Handle edit connector
  const handleEdit = () => {
    if (selectedConnector) {
      router.push(`/connectors/edit/${selectedConnector.id}`);
    }
    handleActionMenuClose();
  };
  
  // Handle test connector
  const handleTest = () => {
    if (selectedConnector) {
      router.push(`/testing?connector=${selectedConnector.id}`);
    }
    handleActionMenuClose();
  };
  
  // Handle duplicate connector
  const handleDuplicateConnector = () => {
    if (selectedConnector && onDuplicate) {
      onDuplicate(selectedConnector.id);
      setSnackbar({
        open: true,
        message: `Connector "${selectedConnector.name}" duplicated successfully`,
        severity: 'success'
      });
    }
    handleActionMenuClose();
  };
  
  // Handle delete connector
  const handleDeleteConnector = () => {
    if (selectedConnector && onDelete) {
      onDelete(selectedConnector.id);
      setSnackbar({
        open: true,
        message: `Connector "${selectedConnector.name}" deleted successfully`,
        severity: 'success'
      });
    }
    handleActionMenuClose();
  };
  
  // Handle view mode change
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };
  
  // Handle connector selection
  const handleConnectorSelect = (event, connectorId) => {
    if (event.target.checked) {
      setSelectedConnectors([...selectedConnectors, connectorId]);
    } else {
      setSelectedConnectors(selectedConnectors.filter(id => id !== connectorId));
    }
  };
  
  // Handle select all connectors
  const handleSelectAllConnectors = (event) => {
    if (event.target.checked) {
      setSelectedConnectors(filteredConnectors.map(connector => connector.id));
    } else {
      setSelectedConnectors([]);
    }
  };
  
  // Handle batch menu open
  const handleBatchMenuOpen = (event) => {
    setBatchMenuAnchorEl(event.currentTarget);
  };
  
  // Handle batch menu close
  const handleBatchMenuClose = () => {
    setBatchMenuAnchorEl(null);
  };
  
  // Handle batch delete
  const handleBatchDelete = () => {
    if (selectedConnectors.length > 0 && onDelete) {
      selectedConnectors.forEach(id => onDelete(id));
      setSnackbar({
        open: true,
        message: `${selectedConnectors.length} connectors deleted successfully`,
        severity: 'success'
      });
      setSelectedConnectors([]);
    }
    handleBatchMenuClose();
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'draft':
        return 'warning';
      case 'deprecated':
        return 'error';
      default:
        return 'default';
    }
  };
  
  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon fontSize="small" color="success" />;
      case 'draft':
        return <WarningIcon fontSize="small" color="warning" />;
      case 'deprecated':
        return <ErrorIcon fontSize="small" color="error" />;
      default:
        return null;
    }
  };
  
  // Filter connectors
  const filteredConnectors = connectors
    .filter(connector => {
      // Search filter
      const matchesSearch = searchQuery === '' || 
        connector.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        connector.description?.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Category filter
      const matchesCategory = filters.category === 'all' || connector.category === filters.category;
      
      // Status filter
      const matchesStatus = filters.status === 'all' || connector.status === filters.status;
      
      // Type filter
      const matchesType = filters.type === 'all' || connector.type === filters.type;
      
      return matchesSearch && matchesCategory && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      // Sort by orderBy field
      const aValue = a[orderBy] || '';
      const bValue = b[orderBy] || '';
      
      if (order === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    });
  
  // Get unique categories, statuses, and types for filters
  const categories = ['all', ...new Set(connectors.map(connector => connector.category).filter(Boolean))];
  const statuses = ['all', ...new Set(connectors.map(connector => connector.status).filter(Boolean))];
  const types = ['all', ...new Set(connectors.map(connector => connector.type).filter(Boolean))];
  
  return (
    <Box>
      {/* Toolbar */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TextField
            placeholder="Search connectors..."
            variant="outlined"
            size="small"
            value={searchQuery}
            onChange={handleSearchChange}
            sx={{ width: 300, mr: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              )
            }}
          />
          
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterClick}
            sx={{ mr: 2 }}
          >
            Filter
          </Button>
          
          <Box sx={{ display: 'flex', border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
            <Tooltip title="Table View">
              <IconButton 
                color={viewMode === 'table' ? 'primary' : 'default'}
                onClick={() => handleViewModeChange('table')}
              >
                <ViewListIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Card View">
              <IconButton 
                color={viewMode === 'card' ? 'primary' : 'default'}
                onClick={() => handleViewModeChange('card')}
              >
                <GridViewIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        <Box>
          {selectedConnectors.length > 0 && (
            <Button
              variant="outlined"
              startIcon={<MoreVertIcon />}
              onClick={handleBatchMenuOpen}
              sx={{ mr: 2 }}
            >
              Batch Actions ({selectedConnectors.length})
            </Button>
          )}
          
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={onRefresh}
            sx={{ mr: 2 }}
          >
            Refresh
          </Button>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => router.push('/connectors/new')}
          >
            New Connector
          </Button>
        </Box>
      </Box>
      
      {/* Loading indicator */}
      {loading && (
        <Box sx={{ width: '100%', mb: 2 }}>
          <LinearProgress />
        </Box>
      )}
      
      {/* Table View */}
      {viewMode === 'table' && (
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selectedConnectors.length > 0 && selectedConnectors.length < filteredConnectors.length}
                    checked={filteredConnectors.length > 0 && selectedConnectors.length === filteredConnectors.length}
                    onChange={handleSelectAllConnectors}
                  />
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={orderBy === 'name'}
                    direction={orderBy === 'name' ? order : 'asc'}
                    onClick={() => handleRequestSort('name')}
                  >
                    Name
                  </TableSortLabel>
                </TableCell>
                <TableCell>Description</TableCell>
                <TableCell>
                  <TableSortLabel
                    active={orderBy === 'category'}
                    direction={orderBy === 'category' ? order : 'asc'}
                    onClick={() => handleRequestSort('category')}
                  >
                    Category
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={orderBy === 'version'}
                    direction={orderBy === 'version' ? order : 'asc'}
                    onClick={() => handleRequestSort('version')}
                  >
                    Version
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={orderBy === 'status'}
                    direction={orderBy === 'status' ? order : 'asc'}
                    onClick={() => handleRequestSort('status')}
                  >
                    Status
                  </TableSortLabel>
                </TableCell>
                <TableCell>
                  <TableSortLabel
                    active={orderBy === 'updated'}
                    direction={orderBy === 'updated' ? order : 'asc'}
                    onClick={() => handleRequestSort('updated')}
                  >
                    Last Updated
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredConnectors
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map(connector => (
                  <TableRow key={connector.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedConnectors.includes(connector.id)}
                        onChange={(event) => handleConnectorSelect(event, connector.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {getStatusIcon(connector.status)}
                        <Typography sx={{ ml: 1, fontWeight: 'medium' }}>
                          {connector.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{connector.description}</TableCell>
                    <TableCell>{connector.category}</TableCell>
                    <TableCell>{connector.version}</TableCell>
                    <TableCell>
                      <Chip 
                        label={connector.status} 
                        size="small" 
                        color={getStatusColor(connector.status)} 
                      />
                    </TableCell>
                    <TableCell>
                      {new Date(connector.updated).toLocaleDateString()}
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        onClick={(event) => handleActionMenuOpen(event, connector)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              {filteredConnectors.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="textSecondary">
                      No connectors found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredConnectors.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </TableContainer>
      )}
      
      {/* Card View */}
      {viewMode === 'card' && (
        <Box>
          <Grid container spacing={2}>
            {filteredConnectors
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map(connector => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={connector.id}>
                  <Zoom in={true} style={{ transitionDelay: '100ms' }}>
                    <Card variant="outlined" sx={{ 
                      height: '100%', 
                      display: 'flex', 
                      flexDirection: 'column',
                      position: 'relative',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 3
                      }
                    }}>
                      <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
                        <Checkbox
                          checked={selectedConnectors.includes(connector.id)}
                          onChange={(event) => handleConnectorSelect(event, connector.id)}
                        />
                      </Box>
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          {getStatusIcon(connector.status)}
                          <Typography variant="h6" component="div" sx={{ ml: 1 }}>
                            {connector.name}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {connector.description}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Version:
                          </Typography>
                          <Typography variant="body2">
                            {connector.version}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Category:
                          </Typography>
                          <Typography variant="body2">
                            {connector.category}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2" color="text.secondary">
                            Status:
                          </Typography>
                          <Chip 
                            label={connector.status} 
                            size="small" 
                            color={getStatusColor(connector.status)} 
                          />
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">
                            Updated:
                          </Typography>
                          <Typography variant="body2">
                            {new Date(connector.updated).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </CardContent>
                      <Divider />
                      <CardActions>
                        <Tooltip title="View Details">
                          <IconButton 
                            size="small" 
                            onClick={() => {
                              setSelectedConnector(connector);
                              handleView();
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton 
                            size="small"
                            onClick={() => {
                              setSelectedConnector(connector);
                              handleEdit();
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Test">
                          <IconButton 
                            size="small"
                            onClick={() => {
                              setSelectedConnector(connector);
                              handleTest();
                            }}
                          >
                            <PlayArrowIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Box sx={{ flexGrow: 1 }} />
                        <IconButton
                          size="small"
                          onClick={(event) => handleActionMenuOpen(event, connector)}
                        >
                          <MoreVertIcon fontSize="small" />
                        </IconButton>
                      </CardActions>
                    </Card>
                  </Zoom>
                </Grid>
              ))}
          </Grid>
          {filteredConnectors.length === 0 && (
            <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body1" color="textSecondary">
                No connectors found
              </Typography>
            </Paper>
          )}
          <Box sx={{ mt: 2 }}>
            <TablePagination
              rowsPerPageOptions={[4, 8, 12, 20]}
              component="div"
              count={filteredConnectors.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Box>
        </Box>
      )}
      
      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
        PaperProps={{
          sx: { width: 250, p: 2 }
        }}
      >
        <Typography variant="subtitle1" gutterBottom>
          Filter Connectors
        </Typography>
        <Divider sx={{ mb: 2 }} />
        
        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel>Category</InputLabel>
          <Select
            name="category"
            value={filters.category}
            onChange={handleFilterChange}
            label="Category"
          >
            {categories.map(category => (
              <MenuItem key={category} value={category}>
                {category === 'all' ? 'All Categories' : category}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel>Status</InputLabel>
          <Select
            name="status"
            value={filters.status}
            onChange={handleFilterChange}
            label="Status"
          >
            {statuses.map(status => (
              <MenuItem key={status} value={status}>
                {status === 'all' ? 'All Statuses' : status}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel>Type</InputLabel>
          <Select
            name="type"
            value={filters.type}
            onChange={handleFilterChange}
            label="Type"
          >
            {types.map(type => (
              <MenuItem key={type} value={type}>
                {type === 'all' ? 'All Types' : type}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
          <Button 
            variant="outlined" 
            size="small" 
            onClick={() => {
              setFilters({
                category: 'all',
                status: 'all',
                type: 'all'
              });
            }}
            sx={{ mr: 1 }}
          >
            Reset
          </Button>
          <Button 
            variant="contained" 
            size="small"
            onClick={handleFilterClose}
          >
            Apply
          </Button>
        </Box>
      </Menu>
      
      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchorEl}
        open={Boolean(actionMenuAnchorEl)}
        onClose={handleActionMenuClose}
      >
        <MenuItem onClick={handleView}>
          <VisibilityIcon fontSize="small" sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        <MenuItem onClick={handleEdit}>
          <EditIcon fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleTest}>
          <PlayArrowIcon fontSize="small" sx={{ mr: 1 }} />
          Test
        </MenuItem>
        <MenuItem onClick={handleDuplicateConnector}>
          <ContentCopyIcon fontSize="small" sx={{ mr: 1 }} />
          Duplicate
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDeleteConnector} sx={{ color: 'error.main' }}>
          <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
      
      {/* Batch Action Menu */}
      <Menu
        anchorEl={batchMenuAnchorEl}
        open={Boolean(batchMenuAnchorEl)}
        onClose={handleBatchMenuClose}
      >
        <MenuItem onClick={handleBatchDelete}>
          <DeleteSweepIcon fontSize="small" sx={{ mr: 1 }} />
          Delete Selected
        </MenuItem>
        <MenuItem onClick={handleBatchMenuClose}>
          <DownloadIcon fontSize="small" sx={{ mr: 1 }} />
          Export Selected
        </MenuItem>
      </Menu>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleSnackbarClose} 
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default EnhancedConnectorList;
