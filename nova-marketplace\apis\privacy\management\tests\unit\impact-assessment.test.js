/**
 * Impact Assessment Tests
 *
 * This file contains unit tests for the impact assessment module.
 */

const impactAssessment = require('../../impact-assessment');
const { logger } = require('../../utils/logger');
const db = require('../../db');

// Mock dependencies
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('../../db', () => ({
  query: jest.fn(),
  transaction: jest.fn()
}));

describe('Impact Assessment', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('createAssessment', () => {
    it('should create a new impact assessment', async () => {
      // Mock data
      const assessmentData = {
        name: 'Test Assessment',
        description: 'Test Description',
        processingActivity: 'data_collection',
        riskLevel: 'medium',
        mitigationMeasures: ['encryption', 'access_control'],
        status: 'in_progress'
      };

      // Mock database response
      const mockResult = {
        id: '123456',
        ...assessmentData,
        createdAt: '2023-01-01T12:00:00Z',
        updatedAt: '2023-01-01T12:00:00Z'
      };

      db.query.mockResolvedValueOnce({ rows: [mockResult] });

      // Call the function
      const result = await impactAssessment.createAssessment(assessmentData);

      // Verify the result
      expect(result).toEqual(mockResult);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO privacy_impact_assessments'),
        expect.arrayContaining([
          assessmentData.name,
          assessmentData.description,
          assessmentData.processingActivity,
          assessmentData.riskLevel,
          expect.any(String), // JSON.stringify(assessmentData.mitigationMeasures)
          assessmentData.status
        ])
      );

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Created new impact assessment')
      );
    });

    it('should handle errors when creating an assessment', async () => {
      // Mock data
      const assessmentData = {
        name: 'Test Assessment',
        description: 'Test Description',
        processingActivity: 'data_collection',
        riskLevel: 'medium',
        mitigationMeasures: ['encryption', 'access_control'],
        status: 'in_progress'
      };

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.createAssessment(assessmentData)).rejects.toThrow(
        'Failed to create impact assessment'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error creating impact assessment'),
        mockError
      );
    });
  });

  describe('getAssessment', () => {
    it('should retrieve an impact assessment by ID', async () => {
      // Mock data
      const assessmentId = '123456';
      const mockResult = {
        id: assessmentId,
        name: 'Test Assessment',
        description: 'Test Description',
        processingActivity: 'data_collection',
        riskLevel: 'medium',
        mitigationMeasures: ['encryption', 'access_control'],
        status: 'in_progress',
        createdAt: '2023-01-01T12:00:00Z',
        updatedAt: '2023-01-01T12:00:00Z'
      };

      db.query.mockResolvedValueOnce({ rows: [mockResult] });

      // Call the function
      const result = await impactAssessment.getAssessment(assessmentId);

      // Verify the result
      expect(result).toEqual(mockResult);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM privacy_impact_assessments'),
        [assessmentId]
      );
    });

    it('should return null if assessment not found', async () => {
      // Mock data
      const assessmentId = '123456';

      db.query.mockResolvedValueOnce({ rows: [] });

      // Call the function
      const result = await impactAssessment.getAssessment(assessmentId);

      // Verify the result
      expect(result).toBeNull();

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM privacy_impact_assessments'),
        [assessmentId]
      );
    });

    it('should handle errors when retrieving an assessment', async () => {
      // Mock data
      const assessmentId = '123456';

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.getAssessment(assessmentId)).rejects.toThrow(
        'Failed to retrieve impact assessment'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving impact assessment'),
        mockError
      );
    });
  });

  describe('updateAssessment', () => {
    it('should update an existing impact assessment', async () => {
      // Mock data
      const assessmentId = '123456';
      const updateData = {
        name: 'Updated Assessment',
        riskLevel: 'high',
        mitigationMeasures: ['encryption', 'access_control', 'audit_logging'],
        status: 'completed'
      };

      // Mock database response
      const mockResult = {
        id: assessmentId,
        ...updateData,
        description: 'Test Description',
        processingActivity: 'data_collection',
        createdAt: '2023-01-01T12:00:00Z',
        updatedAt: '2023-01-02T12:00:00Z'
      };

      db.query.mockResolvedValueOnce({ rows: [mockResult] });

      // Call the function
      const result = await impactAssessment.updateAssessment(assessmentId, updateData);

      // Verify the result
      expect(result).toEqual(mockResult);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE privacy_impact_assessments'),
        expect.arrayContaining([
          updateData.name,
          updateData.riskLevel,
          expect.any(String), // JSON.stringify(updateData.mitigationMeasures)
          updateData.status,
          assessmentId
        ])
      );

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Updated impact assessment')
      );
    });

    it('should handle errors when updating an assessment', async () => {
      // Mock data
      const assessmentId = '123456';
      const updateData = {
        name: 'Updated Assessment',
        riskLevel: 'high'
      };

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.updateAssessment(assessmentId, updateData)).rejects.toThrow(
        'Failed to update impact assessment'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error updating impact assessment'),
        mockError
      );
    });
  });

  describe('deleteAssessment', () => {
    it('should delete an impact assessment', async () => {
      // Mock data
      const assessmentId = '123456';

      // Mock database response
      db.query.mockResolvedValueOnce({ rowCount: 1 });

      // Call the function
      const result = await impactAssessment.deleteAssessment(assessmentId);

      // Verify the result
      expect(result).toBe(true);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM privacy_impact_assessments'),
        [assessmentId]
      );

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Deleted impact assessment')
      );
    });

    it('should return false if assessment not found', async () => {
      // Mock data
      const assessmentId = '123456';

      // Mock database response (no rows deleted)
      db.query.mockResolvedValueOnce({ rowCount: 0 });

      // Call the function
      const result = await impactAssessment.deleteAssessment(assessmentId);

      // Verify the result
      expect(result).toBe(false);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM privacy_impact_assessments'),
        [assessmentId]
      );
    });

    it('should handle errors when deleting an assessment', async () => {
      // Mock data
      const assessmentId = '123456';

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.deleteAssessment(assessmentId)).rejects.toThrow(
        'Failed to delete impact assessment'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error deleting impact assessment'),
        mockError
      );
    });
  });

  describe('listAssessments', () => {
    it('should list all impact assessments with pagination', async () => {
      // Mock data
      const filters = { status: 'in_progress' };
      const pagination = { page: 2, limit: 10 };

      // Mock database response
      const mockResults = [
        {
          id: '123456',
          name: 'Test Assessment 1',
          description: 'Test Description 1',
          processingActivity: 'data_collection',
          riskLevel: 'medium',
          mitigationMeasures: ['encryption', 'access_control'],
          status: 'in_progress',
          createdAt: '2023-01-01T12:00:00Z',
          updatedAt: '2023-01-01T12:00:00Z'
        },
        {
          id: '789012',
          name: 'Test Assessment 2',
          description: 'Test Description 2',
          processingActivity: 'data_sharing',
          riskLevel: 'high',
          mitigationMeasures: ['encryption', 'access_control', 'audit_logging'],
          status: 'in_progress',
          createdAt: '2023-01-02T12:00:00Z',
          updatedAt: '2023-01-02T12:00:00Z'
        }
      ];

      const mockCountResult = { count: '25' };

      // Mock database queries
      db.query.mockResolvedValueOnce({ rows: mockResults });
      db.query.mockResolvedValueOnce({ rows: [mockCountResult] });

      // Call the function
      const result = await impactAssessment.listAssessments(filters, pagination);

      // Verify the result
      expect(result).toEqual({
        data: mockResults,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: 25,
          pages: 3
        }
      });

      // Verify the database queries
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM privacy_impact_assessments'),
        expect.arrayContaining(['in_progress'])
      );
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT COUNT(*)'),
        expect.arrayContaining(['in_progress'])
      );
    });

    it('should handle default pagination values', async () => {
      // Mock data
      const filters = {};

      // Mock database response
      const mockResults = [
        {
          id: '123456',
          name: 'Test Assessment 1',
          description: 'Test Description 1',
          processingActivity: 'data_collection',
          riskLevel: 'medium',
          mitigationMeasures: ['encryption', 'access_control'],
          status: 'in_progress',
          createdAt: '2023-01-01T12:00:00Z',
          updatedAt: '2023-01-01T12:00:00Z'
        }
      ];

      const mockCountResult = { count: '1' };

      // Mock database queries
      db.query.mockResolvedValueOnce({ rows: mockResults });
      db.query.mockResolvedValueOnce({ rows: [mockCountResult] });

      // Call the function
      const result = await impactAssessment.listAssessments(filters);

      // Verify the result
      expect(result).toEqual({
        data: mockResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          pages: 1
        }
      });

      // Verify the database queries
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM privacy_impact_assessments'),
        []
      );
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT COUNT(*)'),
        []
      );
    });

    it('should handle errors when listing assessments', async () => {
      // Mock data
      const filters = {};

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.listAssessments(filters)).rejects.toThrow(
        'Failed to list impact assessments'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error listing impact assessments'),
        mockError
      );
    });
  });

  describe('addAssessmentFinding', () => {
    it('should add a finding to an assessment', async () => {
      // Mock data
      const assessmentId = '123456';
      const findingData = {
        title: 'Test Finding',
        description: 'Test Description',
        severity: 'high',
        recommendation: 'Implement encryption'
      };

      // Mock database response
      const mockResult = {
        id: '789012',
        assessmentId: assessmentId,
        ...findingData,
        createdAt: '2023-01-01T12:00:00Z',
        updatedAt: '2023-01-01T12:00:00Z'
      };

      db.query.mockResolvedValueOnce({ rows: [mockResult] });

      // Call the function
      const result = await impactAssessment.addAssessmentFinding(assessmentId, findingData);

      // Verify the result
      expect(result).toEqual(mockResult);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO assessment_findings'),
        expect.arrayContaining([
          assessmentId,
          findingData.title,
          findingData.description,
          findingData.severity,
          findingData.recommendation
        ])
      );

      // Verify logging
      expect(logger.info).toHaveBeenCalledWith(
        expect.stringContaining('Added finding to impact assessment')
      );
    });

    it('should handle errors when adding a finding', async () => {
      // Mock data
      const assessmentId = '123456';
      const findingData = {
        title: 'Test Finding',
        description: 'Test Description',
        severity: 'high',
        recommendation: 'Implement encryption'
      };

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.addAssessmentFinding(assessmentId, findingData)).rejects.toThrow(
        'Failed to add finding to impact assessment'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error adding finding to impact assessment'),
        mockError
      );
    });
  });

  describe('getAssessmentFindings', () => {
    it('should retrieve findings for an assessment', async () => {
      // Mock data
      const assessmentId = '123456';

      // Mock database response
      const mockResults = [
        {
          id: '789012',
          assessmentId: assessmentId,
          title: 'Test Finding 1',
          description: 'Test Description 1',
          severity: 'high',
          recommendation: 'Implement encryption',
          createdAt: '2023-01-01T12:00:00Z',
          updatedAt: '2023-01-01T12:00:00Z'
        },
        {
          id: '345678',
          assessmentId: assessmentId,
          title: 'Test Finding 2',
          description: 'Test Description 2',
          severity: 'medium',
          recommendation: 'Implement access controls',
          createdAt: '2023-01-02T12:00:00Z',
          updatedAt: '2023-01-02T12:00:00Z'
        }
      ];

      db.query.mockResolvedValueOnce({ rows: mockResults });

      // Call the function
      const result = await impactAssessment.getAssessmentFindings(assessmentId);

      // Verify the result
      expect(result).toEqual(mockResults);

      // Verify the database query
      expect(db.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT * FROM assessment_findings'),
        [assessmentId]
      );
    });

    it('should handle errors when retrieving findings', async () => {
      // Mock data
      const assessmentId = '123456';

      // Mock database error
      const mockError = new Error('Database error');
      db.query.mockRejectedValueOnce(mockError);

      // Call the function and expect it to throw
      await expect(impactAssessment.getAssessmentFindings(assessmentId)).rejects.toThrow(
        'Failed to retrieve assessment findings'
      );

      // Verify logging
      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error retrieving assessment findings'),
        mockError
      );
    });
  });
});
