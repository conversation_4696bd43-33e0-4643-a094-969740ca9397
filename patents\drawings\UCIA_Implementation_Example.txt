```
+-------------------------------------------------------------------------------------------------------------+
|                     IMPLEMENTATION EXAMPLE: DATA BREACH NOTIFICATION GUIDANCE                               |
+-------------------------------------------------------------------------------------------------------------+
                                                    |
                                                    v
+-------------------------------------------------------------------------------------------------------------+
|                                                                                                             |
|  +-------------------+                                                                                      |
|  | USER QUERY:       |                                                                                      |
|  | "What are the     |                                                                                      |
|  |  requirements for |                                                                                      |
|  |  data breach      |                                                                                      |
|  |  notification?"   |                                                                                      |
|  |     (301)         |                                                                                      |
|  +-------------------+                                                                                      |
|           |                                                                                                 |
|           |E1                                                                                               |
|           v                                                                                                 |
|  +-------------------+                                                                                      |
|  | CONTEXT DETECTION:|                                                                                      |
|  | - Healthcare Org  |                                                                                      |
|  | - US & EU         |                                                                                      |
|  | - CISO Role       |                                                                                      |
|  |     (302)         |                                                                                      |
|  +-------------------+                                                                                      |
|           |                                                                                                 |
|           |E2                                                                                               |
|           v                                                                                                 |
|  +-------------------+      +----------------------+      +--------------------------------+                |
|  |                   |      |                      |      |                                |                |
|  | CONCEPT DETECTION:|      | FRAMEWORK DETECTION: |      | REQUIREMENT RETRIEVAL:        |                |
|  | - Data Breach     |      | - GDPR              |      | - GDPR Art. 33, 34            |                |
|  | - Notification    |      | - HIPAA             |      | - HIPAA 164.308(a)(6),         |                |
|  | - Reporting       |      | - SOC2              |      |   164.404                      |                |
|  |     (303)         |      |     (304)           |      | - SOC2 CC7.4                   |                |
|  +-------------------+      +----------------------+      |     (305)                     |                |
|                                                           +--------------------------------+                |
|                                                                         |                                  |
|                                                                         |E3                                |
|                                                                         v                                  |
|  +-------------------+      +----------------------+      +--------------------------------+                |
|  |                   |      |                      |      |                                |                |
|  | CONFLICT          |      | COMMON THEME         |      | CROSS-FRAMEWORK               |                |
|  | IDENTIFICATION:   |      | EXTRACTION:          |      | MAPPING:                      |                |
|  | - Timeline        |      | - Documentation      |      | - GDPR Art. 33 →              |                |
|  | - Scope           |      | - Notification       |      |   HIPAA 164.404               |                |
|  | - Recipients      |      | - Remediation        |      | - SOC2 CC7.4 →                |                |
|  |     (306)         |      |     (307)            |      |   GDPR Art. 33                |                |
|  +-------------------+      +----------------------+      |     (308)                     |                |
|           |                          |                    +--------------------------------+                |
|           |E4                        |E5                               |E6                                  |
|           v                          v                                 v                                    |
|                             +------------------------------------------------------------------+           |
|                             |                                                                  |           |
|                             |                  UNIFIED GUIDANCE GENERATION (309)               |           |
|                             |                                                                  |           |
|                             +------------------------------------------------------------------+           |
|                                                        |E7                                                 |
|                                                        v                                                   |
|  +-------------------+      +----------------------+      +--------------------------------+                |
|  |                   |      |                      |      |                                |                |
|  | RESPONSE ELEMENTS:|      | CONTEXT TAILORING:   |      | CONFIDENCE SCORING:           |                |
|  | - Framework       |      | - Technical details  |      | - High confidence for         |                |
|  |   summaries       |      |   for CISO           |      |   timeline differences        |                |
|  | - Key differences |      | - Cross-border       |      | - Medium confidence for       |                |
|  | - Common          |      |   considerations     |      |   some interpretations        |                |
|  |   requirements    |      |                      |      |                               |                |
|  |     (310)         |      |     (311)            |      |     (312)                     |                |
|  +-------------------+      +----------------------+      +--------------------------------+                |
|           |                          |                                    |                                 |
|           |E8                        |E9                                  |E10                              |
|           v                          v                                    v                                 |
|                             +------------------------------------------------------------------+           |
|                             |                                                                  |           |
|                             |                  FINAL RESPONSE (313)                            |           |
|                             |                                                                  |           |
|                             | "Based on the frameworks you're subject to (GDPR, HIPAA, SOC2), |           |
|                             |  here's a unified compliance perspective:                        |           |
|                             |                                                                  |           |
|                             |  Key differences to be aware of:                                 |           |
|                             |  - Breach notification timeline:                                 |           |
|                             |    - GDPR: 72 hours                                             |           |
|                             |    - HIPAA: 60 days                                             |           |
|                             |  - Scope of protected data:                                     |           |
|                             |    - GDPR: All personal data                                    |           |
|                             |    - HIPAA: Protected health information                        |           |
|                             |                                                                  |           |
|                             |  Common requirements across frameworks:                          |           |
|                             |  - All frameworks require documentation of incidents             |           |
|                             |  - All frameworks require notification to affected parties       |           |
|                             |  - All frameworks require remediation plans                      |           |
|                             |                                                                  |           |
|                             |  For your healthcare organization operating in both US and EU,   |           |
|                             |  you should implement the most stringent requirements..."        |           |
|                             +------------------------------------------------------------------+           |
|                                                                                                             |
+-------------------------------------------------------------------------------------------------------------+

FIG. 12 - Implementation Example: Data Breach Notification Guidance

DATA FLOW INDICATORS:
E1: User query about data breach notification
E2: Detected user and organization context
E3: Retrieved framework-specific requirements
E4: Identified conflicts between framework requirements
E5: Extracted common themes across frameworks
E6: Mapped related requirements across frameworks
E7: Generated unified cross-framework guidance
E8: Core response elements and structure
E9: Context-specific tailoring for CISO role
E10: Confidence indicators for different guidance elements
```
