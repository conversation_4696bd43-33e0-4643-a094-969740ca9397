/**
 * Pandemic-Depression Pattern Test
 * 
 * This script tests the Depression Prediction engine with data that incorporates
 * the historical pandemic-depression pattern (1919 pandemic → 1929 depression,
 * 2020 pandemic → potential 2029-2030 depression).
 */

const DepressionPredictionEngine = require('./depression/depression_prediction_engine');
const HistoricalPatterns = require('./depression/historical_patterns');
const TimelineProjector = require('./depression/timeline_projector');
const MitigationStrategies = require('./depression/mitigation_strategies');

// Create Depression Prediction Engine instance with pandemic-depression focus
const depressionEngine = new DepressionPredictionEngine({
  targetTimeframe: { start: 2027, end: 2031 },
  enableHistoricalComparison: true,
  enableTimelineProjection: true,
  enableMitigationStrategies: true
});

// Market data adjusted to reflect pandemic-depression pattern indicators
const marketData = {
  price: {
    current: 100,
    moving_average: 110, // Price below moving average - early warning sign
    history: [90, 95, 105, 110, 100]
  },
  volume: {
    current: 1200000,
    average: 1000000,
    trend: 'increasing' // Increasing volume can indicate market instability
  },
  liquidity: {
    value: 0.6, // Reduced liquidity - early warning sign
    trend: 'decreasing'
  },
  volatility: {
    value: 25, // Increased volatility - early warning sign
    trend: 'increasing'
  },
  depth: {
    value: 0.5, // Reduced market depth - early warning sign
    trend: 'decreasing'
  },
  spread: {
    value: 0.8, // Widening spreads - early warning sign
    trend: 'increasing'
  }
};

// Economic data adjusted to reflect pandemic-depression pattern indicators
const economicData = {
  gdp: {
    value: 21000,
    growth: 1.2, // Slowing GDP growth - early warning sign
    trend: 'decreasing'
  },
  inflation: {
    rate: 3.5, // Elevated inflation - early warning sign
    core: 3.0,
    trend: 'increasing'
  },
  unemployment: {
    rate: 4.5, // Rising unemployment - early warning sign
    trend: 'increasing'
  },
  interestRates: {
    fed_funds: 4.5, // Rising interest rates - early warning sign
    ten_year: 4.8,
    trend: 'increasing'
  },
  pmi: {
    value: 48.5, // PMI below 50 indicates contraction - early warning sign
    trend: 'decreasing'
  },
  consumerConfidence: {
    value: 85, // Declining consumer confidence - early warning sign
    trend: 'decreasing'
  },
  buildingPermits: {
    value: 1500000,
    growth: -2.5, // Declining building permits - early warning sign
    trend: 'decreasing'
  },
  // Adding pandemic-depression cycle indicator
  pandemicDepressionCycle: {
    lastPandemic: 2020,
    pandemicSeverity: 0.8, // COVID-19 severity (0-1)
    historicalCycleYears: 10, // Historical years between pandemic and depression
    cycleStrength: 0.85 // Strength of the pandemic-depression pattern (0-1)
  }
};

// Sentiment data adjusted to reflect pandemic-depression pattern indicators
const sentimentData = {
  retail: {
    bullishPercentage: 45, // Declining retail sentiment - early warning sign
    bearishPercentage: 55,
    trend: 'decreasing'
  },
  institutional: {
    bullishPercentage: 40, // Declining institutional sentiment - early warning sign
    bearishPercentage: 60,
    netPositioning: -5, // Negative positioning - early warning sign
    trend: 'decreasing'
  },
  media: {
    sentiment: 0.4, // Declining media sentiment - early warning sign
    volume: 1500,
    trend: 'decreasing'
  },
  social: {
    sentiment: 0.45, // Declining social sentiment - early warning sign
    volume: 8000,
    trend: 'decreasing'
  },
  futures: {
    commercialNetPositioning: -10, // Negative positioning - early warning sign
    nonCommercialNetPositioning: -15,
    trend: 'decreasing'
  },
  // Adding pandemic-depression cycle sentiment
  pandemicCycleSentiment: {
    awarenessFactor: 0.7, // Market awareness of the pandemic-depression cycle (0-1)
    concernLevel: 0.65, // Level of concern about the cycle (0-1)
    historicalPatternRecognition: 0.75 // Recognition of the historical pattern (0-1)
  }
};

// Calculate depression probability with pandemic-depression pattern
console.log('Calculating depression probability with pandemic-depression pattern...');
const result = depressionEngine.calculateDepressionProbability(marketData, economicData, sentimentData);

// Display result
console.log('\nPandemic-Depression Pattern Analysis:');
console.log(`CSFE Value: ${result.csfeValue}`);
console.log(`Depression Probability: ${result.depressionProbability}`);
console.log(`Confidence: ${result.confidence}`);

// Display timeline probability
console.log('\nTimeline Probability:');
Object.entries(result.timelineProbability).forEach(([year, probability]) => {
  console.log(`  ${year}: ${(probability * 100).toFixed(2)}%`);
});

// Display severity distribution
console.log('\nSeverity Distribution:');
Object.entries(result.severityDistribution).forEach(([severity, probability]) => {
  console.log(`  ${severity}: ${(probability * 100).toFixed(2)}%`);
});

// Display key indicators
console.log('\nKey Indicators:');
result.keyIndicators.forEach(indicator => {
  console.log(`  ${indicator.name}: ${indicator.value} (${indicator.impact} impact)`);
});

// Display historical comparison
if (result.historicalComparison) {
  console.log('\nHistorical Comparison:');
  console.log(`  Most Similar Pattern: ${result.historicalComparison.mostSimilarPattern.name}`);
  console.log(`  Similarity: ${(result.historicalComparison.mostSimilarPattern.overallSimilarity * 100).toFixed(2)}%`);
  
  console.log('\n  Key Similarities:');
  result.historicalComparison.mostSimilarPattern.keySimilarities.forEach(similarity => {
    console.log(`    ${similarity.factor}: Current=${similarity.current}, Historical=${similarity.historical}`);
  });
  
  console.log('\n  Key Differences:');
  result.historicalComparison.mostSimilarPattern.keyDifferences.forEach(difference => {
    console.log(`    ${difference.factor}: Current=${difference.current}, Historical=${difference.historical}`);
  });
}

// Display timeline projection
if (result.timelineProjection) {
  console.log('\nTimeline Projection:');
  console.log(`  Onset Year: ${result.timelineProjection.onsetYear}`);
  console.log(`  Peak Year: ${result.timelineProjection.peakYear}`);
  console.log(`  Recovery Year: ${result.timelineProjection.recoveryYear}`);
  
  console.log('\n  Phases:');
  Object.entries(result.timelineProjection.phases).forEach(([phase, data]) => {
    console.log(`    ${data.name}: ${data.startYear.toFixed(1)} to ${data.endYear.toFixed(1)} (${data.duration.toFixed(1)} years)`);
    console.log(`      ${data.description}`);
  });
  
  console.log('\n  Market Projection:');
  Object.entries(result.timelineProjection.marketProjection).forEach(([year, data]) => {
    console.log(`    ${year}: ${data.overall.direction} (${data.overall.decline.toFixed(1)}% decline)`);
  });
}

// Display mitigation strategies
if (result.mitigationStrategies) {
  console.log('\nMitigation Strategies:');
  console.log(`  Intensity: ${result.mitigationStrategies.intensity}`);
  
  console.log('\n  Government Strategies:');
  result.mitigationStrategies.government.slice(0, 3).forEach(strategy => {
    console.log(`    ${strategy.name} (${strategy.priority} priority): ${strategy.description}`);
  });
  
  console.log('\n  Institutional Strategies:');
  result.mitigationStrategies.institutional.slice(0, 3).forEach(strategy => {
    console.log(`    ${strategy.name} (${strategy.priority} priority): ${strategy.description}`);
  });
  
  console.log('\n  Individual Strategies:');
  result.mitigationStrategies.individual.slice(0, 3).forEach(strategy => {
    console.log(`    ${strategy.name} (${strategy.priority} priority): ${strategy.description}`);
  });
  
  console.log('\n  Implementation Timeline:');
  result.mitigationStrategies.implementationTimeline.phases.forEach(phase => {
    console.log(`    ${phase.name}: ${phase.startYear} to ${phase.endYear}`);
    console.log(`      ${phase.description}`);
  });
}

console.log('\nPandemic-Depression Pattern Test completed successfully.');
