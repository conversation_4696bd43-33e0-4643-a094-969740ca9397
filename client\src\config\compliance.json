{"auto_ui_rules": {"soc2": {"required_components": ["evidence_table", "control_coverage_meter", "audit_trail"], "forbidden_actions": ["delete_evidence", "modify_timestamp", "bypass_verification"], "field_rules": {"evidence": {"verification": "blockchain", "retention": "7_years", "export_controls": ["watermark", "audit_log"]}, "controls": {"status_transitions": ["implemented", "partially_implemented", "not_implemented", "not_applicable"], "requires_evidence": true}, "user_activity": {"logging": "required", "retention": "1_year"}}, "roles": {"auditor": {"can_view": ["all_evidence", "all_controls", "all_test_results"], "can_modify": ["test_results", "audit_notes"], "cannot_modify": ["evidence", "controls", "system_settings"]}, "compliance_manager": {"can_view": ["all_evidence", "all_controls", "all_test_results"], "can_modify": ["controls", "test_plans", "remediation_plans"], "cannot_modify": ["verified_evidence"]}}}, "hipaa": {"required_components": ["phi_access_log", "authorization_check", "consent_tracker"], "forbidden_actions": ["mass_export", "unencrypted_storage", "unauthorized_sharing"], "field_rules": {"phi": {"encryption": "required", "masking": "partial", "access_logging": "detailed", "export_controls": ["watermark", "audit_log", "dlp"]}, "consent": {"expiration": true, "revocation": true, "granular_options": true}}, "roles": {"privacy_officer": {"can_view": ["all_phi", "all_consent_records", "access_logs"], "can_modify": ["privacy_settings", "consent_templates", "access_controls"], "cannot_modify": ["audit_logs"]}, "healthcare_provider": {"can_view": ["authorized_phi", "active_consent_records"], "can_modify": ["treatment_notes"], "cannot_modify": ["consent_records", "audit_logs"]}}}, "gdpr": {"required_components": ["consent_manager", "data_inventory", "subject_rights_portal"], "forbidden_actions": ["process_without_consent", "transfer_without_safeguards", "indefinite_retention"], "field_rules": {"personal_data": {"consent_required": true, "purpose_limitation": true, "retention_period": "required", "right_to_access": true, "right_to_erasure": true}, "special_category_data": {"explicit_consent": true, "enhanced_security": true}}, "roles": {"data_protection_officer": {"can_view": ["all_personal_data", "processing_activities", "consent_records"], "can_modify": ["privacy_settings", "dpia", "processing_records"], "cannot_modify": ["audit_logs"]}, "data_subject": {"can_view": ["own_personal_data", "own_consent_records"], "can_modify": ["own_consent_preferences"], "cannot_modify": ["other_subjects_data"]}}}, "pci_dss": {"required_components": ["card_data_scanner", "encryption_validator", "access_control_monitor"], "forbidden_actions": ["store_cvv", "unencrypted_pan", "excessive_retention"], "field_rules": {"cardholder_data": {"masking": "required", "encryption": "required", "tokenization": "preferred", "retention": "minimal"}, "authentication": {"mfa": "required", "password_complexity": true, "session_timeout": true}}, "roles": {"security_officer": {"can_view": ["security_controls", "scan_results", "access_logs"], "can_modify": ["security_settings", "remediation_plans"], "cannot_modify": ["audit_logs", "cardholder_data"]}, "merchant": {"can_view": ["masked_transaction_data"], "can_modify": ["transaction_settings"], "cannot_modify": ["security_controls", "full_pan"]}}}}, "component_library": {"evidence_table": {"type": "data_table", "features": ["sorting", "filtering", "pagination", "export"], "columns": [{"field": "name", "header": "Evidence Name", "sortable": true}, {"field": "type", "header": "Type", "sortable": true, "filterable": true}, {"field": "control", "header": "Control", "sortable": true, "filterable": true}, {"field": "date", "header": "Date Collected", "sortable": true, "type": "date"}, {"field": "verified", "header": "Verified", "type": "boolean", "template": "blockchain_badge"}], "actions": [{"name": "view", "label": "View", "icon": "eye", "permission": "view_evidence"}, {"name": "verify", "label": "Verify", "icon": "shield-check", "permission": "verify_evidence"}, {"name": "download", "label": "Download", "icon": "download", "permission": "download_evidence"}]}, "control_coverage_meter": {"type": "dashboard_widget", "data_source": "/api/v1/controls/coverage", "visualization": "gauge", "thresholds": [{"value": 0.6, "color": "red"}, {"value": 0.8, "color": "yellow"}, {"value": 1.0, "color": "green"}], "features": ["drilldown", "historical_trend"]}, "audit_trail": {"type": "activity_log", "data_source": "/api/v1/audit-logs", "columns": [{"field": "timestamp", "header": "Timestamp", "sortable": true, "type": "datetime"}, {"field": "user", "header": "User", "sortable": true}, {"field": "action", "header": "Action", "sortable": true, "filterable": true}, {"field": "resource", "header": "Resource", "sortable": true, "filterable": true}, {"field": "details", "header": "Details", "expandable": true}], "features": ["filtering", "export", "blockchain_verification"]}, "phi_access_log": {"type": "data_table", "data_source": "/api/v1/phi-access-logs", "columns": [{"field": "timestamp", "header": "Timestamp", "sortable": true, "type": "datetime"}, {"field": "user", "header": "User", "sortable": true}, {"field": "patient", "header": "Patient", "sortable": true}, {"field": "data_accessed", "header": "Data Accessed", "sortable": true}, {"field": "reason", "header": "Reason", "sortable": true}, {"field": "authorized", "header": "Authorized", "type": "boolean"}], "features": ["filtering", "alerting", "anomaly_detection"]}, "consent_manager": {"type": "form_wizard", "steps": [{"id": "purpose", "title": "Purpose of Processing", "fields": [{"name": "purposes", "type": "checkbox_group", "options": ["marketing", "analytics", "service_provision", "research"]}]}, {"id": "data_types", "title": "Data Categories", "fields": [{"name": "data_categories", "type": "checkbox_group", "options": ["contact", "demographic", "behavioral", "financial", "health"]}]}, {"id": "retention", "title": "Retention Period", "fields": [{"name": "retention_period", "type": "select", "options": ["1_year", "3_years", "5_years", "until_revoked"]}]}, {"id": "confirmation", "title": "Confirmation", "template": "consent_summary"}], "features": ["version_tracking", "expiration_management", "revocation"]}}}