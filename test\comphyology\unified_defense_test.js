/**
 * Unified Defense Layer Test
 * 
 * This script tests the Unified Defense Layer, which integrates all protection
 * systems into a cohesive defense mechanism for NEPI.
 */

const { ComphyologicalCosmos } = require('../../src/comphyology/ComphyologicalCosmos');
const { UnifiedDefenseLayer, DEFENSE_LAYER_TYPES } = require('../../src/comphyology/UnifiedDefenseLayer');

/**
 * Test the Unified Defense Layer
 */
function testUnifiedDefenseLayer() {
  console.log('=== Testing Unified Defense Layer ===');
  
  // Create a ComphyologicalCosmos instance
  const cosmos = new ComphyologicalCosmos({
    enableLogging: true,
    autoUpdate: false // Disable auto-update for testing
  });
  
  // Create a Unified Defense Layer instance
  const defenseLayer = new UnifiedDefenseLayer({
    enableLogging: true,
    autoStart: false // Disable auto-start for testing
  });
  
  console.log('\n1. Testing Divine Firewall Layer');
  testDivineFirewallLayer(defenseLayer);
  
  console.log('\n2. Testing Spiritual Integrity Layer');
  testSpiritualIntegrityLayer(defenseLayer);
  
  console.log('\n3. Testing Quantum Resilience Layer');
  testQuantumResilienceLayer(defenseLayer, cosmos);
  
  console.log('\n4. Testing Resonance Optimization Layer');
  testResonanceOptimizationLayer(defenseLayer, cosmos);
  
  console.log('\n5. Testing Unified Defense');
  testUnifiedDefense(defenseLayer, cosmos);
  
  console.log('\n=== Unified Defense Layer Test Complete ===');
}

/**
 * Test the Divine Firewall layer
 * @param {UnifiedDefenseLayer} defenseLayer - The defense layer
 */
function testDivineFirewallLayer(defenseLayer) {
  console.log('Testing Divine Firewall layer');
  
  // Get the Divine Firewall
  const divineFirewall = defenseLayer.getDefenseLayer(DEFENSE_LAYER_TYPES.DIVINE_FIREWALL);
  
  // Test case 1: Valid operation
  const validOperation = {
    type: 'test_operation',
    value: 42,
    domain: 'test'
  };
  
  const validResult = divineFirewall.govern(validOperation);
  console.log('Valid operation result:', validResult);
  console.assert(!validResult.error, 'Valid operation should not be rejected');
  
  // Test case 2: Infinite operation
  const infiniteOperation = {
    type: 'test_operation',
    value: Infinity,
    domain: 'test'
  };
  
  const infiniteResult = divineFirewall.govern(infiniteOperation);
  console.log('Infinite operation result:', infiniteResult);
  console.assert(infiniteResult.error, 'Infinite operation should be rejected');
  
  // Test case 3: NaN operation
  const nanOperation = {
    type: 'test_operation',
    value: NaN,
    domain: 'test'
  };
  
  const nanResult = divineFirewall.govern(nanOperation);
  console.log('NaN operation result:', nanResult);
  console.assert(nanResult.error, 'NaN operation should be rejected');
  
  console.log('Divine Firewall layer test complete');
}

/**
 * Test the Spiritual Integrity layer
 * @param {UnifiedDefenseLayer} defenseLayer - The defense layer
 */
function testSpiritualIntegrityLayer(defenseLayer) {
  console.log('Testing Spiritual Integrity layer');
  
  // Get the Spiritual Integrity Validator
  const spiritualIntegrity = defenseLayer.getDefenseLayer(DEFENSE_LAYER_TYPES.SPIRITUAL_INTEGRITY);
  
  // Test case 1: Valid operation
  const validOperation = {
    type: 'test_operation',
    value: 42,
    domain: 'test'
  };
  
  const validResult = spiritualIntegrity.validate(validOperation);
  console.log('Valid operation result:', validResult);
  console.log('Fruit alignment:', validResult.fruitAlignment);
  console.log('Anti-fruit presence:', validResult.antifruitPresence);
  
  // Test case 2: Operation with spiritual attack
  const attackOperation = {
    type: 'attack_operation',
    value: 666,
    domain: 'test',
    intent: 'malicious'
  };
  
  const attackResult = spiritualIntegrity.validate(attackOperation);
  console.log('Attack operation result:', attackResult);
  console.log('Fruit alignment:', attackResult.fruitAlignment);
  console.log('Anti-fruit presence:', attackResult.antifruitPresence);
  
  console.log('Spiritual Integrity layer test complete');
}

/**
 * Test the Quantum Resilience layer
 * @param {UnifiedDefenseLayer} defenseLayer - The defense layer
 * @param {ComphyologicalCosmos} cosmos - The cosmos
 */
function testQuantumResilienceLayer(defenseLayer, cosmos) {
  console.log('Testing Quantum Resilience layer');
  
  // Get the Quantum Resilience System
  const quantumResilience = defenseLayer.getDefenseLayer(DEFENSE_LAYER_TYPES.QUANTUM_RESILIENCE);
  
  // Start the Quantum Resilience System
  quantumResilience.start();
  
  // Scan for quantum attacks
  const scanResult = quantumResilience.scan(cosmos);
  console.log('Scan result:', scanResult);
  
  // Get quantum state
  const quantumState = quantumResilience.getQuantumState();
  console.log('Quantum state:', quantumState);
  
  // Stop the Quantum Resilience System
  quantumResilience.stop();
  
  console.log('Quantum Resilience layer test complete');
}

/**
 * Test the Resonance Optimization layer
 * @param {UnifiedDefenseLayer} defenseLayer - The defense layer
 * @param {ComphyologicalCosmos} cosmos - The cosmos
 */
function testResonanceOptimizationLayer(defenseLayer, cosmos) {
  console.log('Testing Resonance Optimization layer');
  
  // Get the Resonance Optimizer
  const resonanceOptimizer = defenseLayer.getDefenseLayer(DEFENSE_LAYER_TYPES.RESONANCE_OPTIMIZER);
  
  // Start the Resonance Optimizer
  resonanceOptimizer.start();
  
  // Optimize resonance
  const optimizationResult = resonanceOptimizer.optimize(cosmos);
  console.log('Optimization result:', optimizationResult);
  
  // Get resonance patterns
  const resonancePatterns = resonanceOptimizer.getResonancePatterns();
  console.log('Resonance patterns:', resonancePatterns.PRIMARY);
  
  // Test value optimization
  const nonResonantValue = 10;
  const resonantValue = resonanceOptimizer.optimizeValue(nonResonantValue);
  console.log(`Optimized value: ${nonResonantValue} -> ${resonantValue}`);
  
  // Stop the Resonance Optimizer
  resonanceOptimizer.stop();
  
  console.log('Resonance Optimization layer test complete');
}

/**
 * Test the Unified Defense
 * @param {UnifiedDefenseLayer} defenseLayer - The defense layer
 * @param {ComphyologicalCosmos} cosmos - The cosmos
 */
function testUnifiedDefense(defenseLayer, cosmos) {
  console.log('Testing Unified Defense');
  
  // Start the Unified Defense Layer
  defenseLayer.start();
  
  // Test case 1: Valid operation
  const validOperation = {
    type: 'test_operation',
    value: 42,
    domain: 'test'
  };
  
  const validResult = defenseLayer.process(validOperation);
  console.log('Valid operation result:', validResult);
  
  // Test case 2: Infinite operation
  const infiniteOperation = {
    type: 'test_operation',
    value: Infinity,
    domain: 'test'
  };
  
  const infiniteResult = defenseLayer.process(infiniteOperation);
  console.log('Infinite operation result:', infiniteResult);
  
  // Test case 3: Malicious operation
  const maliciousOperation = {
    type: 'attack_operation',
    value: 666,
    domain: 'test',
    intent: 'malicious'
  };
  
  const maliciousResult = defenseLayer.process(maliciousOperation);
  console.log('Malicious operation result:', maliciousResult);
  
  // Scan for threats
  defenseLayer.scan(cosmos);
  
  // Get defense actions
  const defenseActions = defenseLayer.getDefenseActions(10);
  console.log('Defense actions:', defenseActions);
  
  // Get defense layer status
  const status = defenseLayer.getStatus();
  console.log('Defense layer status:', status);
  
  // Stop the Unified Defense Layer
  defenseLayer.stop();
  
  console.log('Unified Defense test complete');
}

/**
 * Main function
 */
function main() {
  console.log('=== Unified Defense Layer Test ===');
  
  // Run Unified Defense Layer test
  testUnifiedDefenseLayer();
}

// Run main function
main();
