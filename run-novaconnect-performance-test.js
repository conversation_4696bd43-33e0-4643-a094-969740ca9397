/**
 * NovaConnect Performance Test Runner
 * 
 * This script runs the performance tests for NovaConnect without requiring
 * package.json modifications.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  testDir: path.join(__dirname, 'tests', 'performance'),
  resultsDir: path.join(__dirname, 'test-results'),
  jestConfig: path.join(__dirname, 'jest.config.js')
};

// Ensure results directory exists
if (!fs.existsSync(config.resultsDir)) {
  fs.mkdirSync(config.resultsDir, { recursive: true });
}

// Main execution
console.log('Starting NovaConnect Performance Tests...');

try {
  // Run Jest for the performance tests
  const command = `npx jest ${config.testDir} --config=${config.jestConfig} --colors`;
  execSync(command, { stdio: 'inherit' });
  
  console.log('\n✅ Performance tests passed successfully!');
  process.exit(0);
} catch (error) {
  console.error('\n❌ Performance tests failed.');
  process.exit(1);
}
