/**
 * NovaCore API Index
 * 
 * This file exports all API endpoints from the NovaCore system.
 */

import express from 'express';
import evidenceApi from './evidenceApi';
import connectorApi from './connectorApi';
import jobApi from './jobApi';

// Create router
const router = express.Router();

// Mount API endpoints
router.use('/evidence', evidenceApi);
router.use('/connectors', connectorApi);
router.use('/jobs', jobApi);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date() });
});

export default router;
