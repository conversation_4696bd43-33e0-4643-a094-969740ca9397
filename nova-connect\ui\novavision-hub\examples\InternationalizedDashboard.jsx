/**
 * Internationalized Dashboard Example
 * 
 * This example demonstrates how to use the internationalization features.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  LanguageSelector,
  Translation,
  FormattedDate,
  FormattedNumber,
  FormattedCurrency,
  TextDirection,
  TabPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { AnimationProvider } from '../animation';
import { I18nProvider } from '../i18n';

/**
 * Internationalized Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Internationalized Dashboard Content component
 */
const InternationalizedDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Sample data
  const currentDate = new Date();
  const numberValue = 1234567.89;
  const currencyValue = 9876.54;
  
  // Tabs
  const tabs = [
    {
      id: 'dashboard',
      label: <Translation id="dashboard.dashboard" defaultValue="Dashboard" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DashboardCard
            title={<Translation id="dashboard.overview" defaultValue="Overview" />}
            subtitle={<Translation id="dashboard.summary" defaultValue="Summary" />}
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.language" defaultValue="Language" />
              </h3>
              <div className="mb-6">
                <LanguageSelector variant="buttons" showFlags showNames />
              </div>
              
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.dateFormat" defaultValue="Date Format" />
              </h3>
              <div className="space-y-2 mb-6">
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.default" defaultValue="Default" />:
                  </span>
                  <FormattedDate value={currentDate} className="text-textPrimary" />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.short" defaultValue="Short" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ dateStyle: 'short' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.medium" defaultValue="Medium" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ dateStyle: 'medium' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.long" defaultValue="Long" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ dateStyle: 'long' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.full" defaultValue="Full" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ dateStyle: 'full' }}
                    className="text-textPrimary"
                  />
                </div>
              </div>
              
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.timeFormat" defaultValue="Time Format" />
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.default" defaultValue="Default" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ timeStyle: 'medium' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.short" defaultValue="Short" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ timeStyle: 'short' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.medium" defaultValue="Medium" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ timeStyle: 'medium' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.long" defaultValue="Long" />:
                  </span>
                  <FormattedDate
                    value={currentDate}
                    options={{ timeStyle: 'long' }}
                    className="text-textPrimary"
                  />
                </div>
              </div>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="dashboard.formatting" defaultValue="Formatting" />}
            subtitle={<Translation id="dashboard.examples" defaultValue="Examples" />}
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.numberFormat" defaultValue="Number Format" />
              </h3>
              <div className="space-y-2 mb-6">
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.default" defaultValue="Default" />:
                  </span>
                  <FormattedNumber value={numberValue} className="text-textPrimary" />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.integer" defaultValue="Integer" />:
                  </span>
                  <FormattedNumber
                    value={numberValue}
                    options={{ maximumFractionDigits: 0 }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.percent" defaultValue="Percent" />:
                  </span>
                  <FormattedNumber
                    value={0.4567}
                    options={{ style: 'percent' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.scientific" defaultValue="Scientific" />:
                  </span>
                  <FormattedNumber
                    value={numberValue}
                    options={{ notation: 'scientific' }}
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">
                    <Translation id="common.compact" defaultValue="Compact" />:
                  </span>
                  <FormattedNumber
                    value={numberValue}
                    options={{ notation: 'compact' }}
                    className="text-textPrimary"
                  />
                </div>
              </div>
              
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.currencyFormat" defaultValue="Currency Format" />
              </h3>
              <div className="space-y-2 mb-6">
                <div className="flex justify-between">
                  <span className="text-textSecondary">USD:</span>
                  <FormattedCurrency
                    value={currencyValue}
                    currency="USD"
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">EUR:</span>
                  <FormattedCurrency
                    value={currencyValue}
                    currency="EUR"
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">GBP:</span>
                  <FormattedCurrency
                    value={currencyValue}
                    currency="GBP"
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">JPY:</span>
                  <FormattedCurrency
                    value={currencyValue}
                    currency="JPY"
                    className="text-textPrimary"
                  />
                </div>
                <div className="flex justify-between">
                  <span className="text-textSecondary">CNY:</span>
                  <FormattedCurrency
                    value={currencyValue}
                    currency="CNY"
                    className="text-textPrimary"
                  />
                </div>
              </div>
              
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="common.textDirection" defaultValue="Text Direction" />
              </h3>
              <TextDirection className="p-3 border border-divider rounded-md bg-background">
                <p className="text-textPrimary">
                  <Translation id="common.textDirectionExample" defaultValue="This text follows the current locale's text direction." />
                </p>
              </TextDirection>
            </div>
          </DashboardCard>
          
          <DashboardCard
            title={<Translation id="compliance.compliance" defaultValue="Compliance" />}
            subtitle={<Translation id="compliance.frameworks" defaultValue="Frameworks" />}
            className="md:col-span-2"
          >
            <div className="p-4">
              <div className="space-y-4">
                <div className="bg-background p-4 rounded-md border border-divider">
                  <h3 className="font-medium text-textPrimary mb-2">
                    <Translation id="compliance.riskAssessment" defaultValue="Risk Assessment" />
                  </h3>
                  <p className="text-textSecondary mb-4">
                    <Translation
                      id="compliance.riskAssessmentDescription"
                      defaultValue="Risk assessment is the process of identifying, analyzing, and evaluating risks to determine their potential impact on an organization's objectives."
                    />
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-textPrimary">
                      <Translation id="compliance.lastUpdated" defaultValue="Last Updated" />:
                    </span>
                    <FormattedDate
                      value={new Date(currentDate.getTime() - 86400000 * 3)}
                      options={{ dateStyle: 'medium' }}
                      className="text-textSecondary"
                    />
                  </div>
                </div>
                
                <div className="bg-background p-4 rounded-md border border-divider">
                  <h3 className="font-medium text-textPrimary mb-2">
                    <Translation id="compliance.riskManagement" defaultValue="Risk Management" />
                  </h3>
                  <p className="text-textSecondary mb-4">
                    <Translation
                      id="compliance.riskManagementDescription"
                      defaultValue="Risk management is the coordinated activities to direct and control an organization with regard to risk."
                    />
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-textPrimary">
                      <Translation id="compliance.lastUpdated" defaultValue="Last Updated" />:
                    </span>
                    <FormattedDate
                      value={new Date(currentDate.getTime() - 86400000 * 5)}
                      options={{ dateStyle: 'medium' }}
                      className="text-textSecondary"
                    />
                  </div>
                </div>
                
                <div className="bg-background p-4 rounded-md border border-divider">
                  <h3 className="font-medium text-textPrimary mb-2">
                    <Translation id="compliance.riskMitigation" defaultValue="Risk Mitigation" />
                  </h3>
                  <p className="text-textSecondary mb-4">
                    <Translation
                      id="compliance.riskMitigationDescription"
                      defaultValue="Risk mitigation is the process of reducing the impact or likelihood of a risk event."
                    />
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-textPrimary">
                      <Translation id="compliance.lastUpdated" defaultValue="Last Updated" />:
                    </span>
                    <FormattedDate
                      value={new Date(currentDate.getTime() - 86400000 * 7)}
                      options={{ dateStyle: 'medium' }}
                      className="text-textSecondary"
                    />
                  </div>
                </div>
              </div>
            </div>
          </DashboardCard>
        </div>
      )
    },
    {
      id: 'settings',
      label: <Translation id="dashboard.settings" defaultValue="Settings" />,
      content: (
        <DashboardCard
          title={<Translation id="dashboard.languageSettings" defaultValue="Language Settings" />}
          subtitle={<Translation id="dashboard.selectYourLanguage" defaultValue="Select your preferred language" />}
        >
          <div className="p-4">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.language" defaultValue="Language" />
              </h3>
              <LanguageSelector variant="dropdown" size="lg" showFlags showNames className="w-full" />
            </div>
            
            <div className="mb-6">
              <h3 className="text-lg font-medium text-textPrimary mb-4">
                <Translation id="dashboard.availableLanguages" defaultValue="Available Languages" />
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-background p-4 rounded-md border border-divider">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">🇺🇸</span>
                    <span className="text-textPrimary">English (US)</span>
                  </div>
                </div>
                <div className="bg-background p-4 rounded-md border border-divider">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">🇪🇸</span>
                    <span className="text-textPrimary">Español (España)</span>
                  </div>
                </div>
                <div className="bg-background p-4 rounded-md border border-divider">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">🇫🇷</span>
                    <span className="text-textPrimary">Français (France)</span>
                  </div>
                </div>
                <div className="bg-background p-4 rounded-md border border-divider">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">🇩🇪</span>
                    <span className="text-textPrimary">Deutsch (Deutschland)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          <Translation id="dashboard.internationalizedDashboard" defaultValue="Internationalized Dashboard" />
        </h1>
        
        <div className="flex items-center space-x-2">
          <LanguageSelector variant="dropdown" size="sm" showFlags />
          <ThemeSelector variant="dropdown" size="sm" />
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={tabs}
          defaultTab="dashboard"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

InternationalizedDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Internationalized Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Internationalized Dashboard component
 */
const InternationalizedDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <I18nProvider detectBrowserLocale>
        <PreferencesProvider>
          <OfflineProvider>
            <AnimationProvider>
              <InternationalizedDashboardContent
                novaConnect={novaConnect}
                novaShield={novaShield}
                novaTrack={novaTrack}
                enableLogging={enableLogging}
              />
            </AnimationProvider>
          </OfflineProvider>
        </PreferencesProvider>
      </I18nProvider>
    </ThemeProvider>
  );
};

InternationalizedDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default InternationalizedDashboard;
