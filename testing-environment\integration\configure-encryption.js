/**
 * Configuration Script for Encryption
 * 
 * This script sets up the proper environment variables for encryption keys.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuration paths
const CONFIG_DIR = path.resolve(__dirname, '../config');
const ENV_FILE = path.resolve(CONFIG_DIR, '.env');
const ENV_EXAMPLE_FILE = path.resolve(CONFIG_DIR, '.env.example');
const KEY_DIR = path.resolve(CONFIG_DIR, '.keys');

// Environment variables to set
const envVars = {
  // Master encryption key for the authentication service
  AUTH_ENCRYPTION_KEY: crypto.randomBytes(32).toString('hex'),
  
  // Encryption key for connector definitions
  CONNECTOR_ENCRYPTION_KEY: crypto.randomBytes(32).toString('hex'),
  
  // Key directory for key management service
  KEY_DIRECTORY: KEY_DIR,
  
  // Key rotation period in days
  KEY_ROTATION_DAYS: '90',
  
  // TLS configuration
  USE_TLS: 'false', // Set to 'true' in production
  TLS_CERT_PATH: path.resolve(CONFIG_DIR, 'tls/cert.pem'),
  TLS_KEY_PATH: path.resolve(CONFIG_DIR, 'tls/key.pem'),
  
  // Security settings
  SECURE_COOKIES: 'true',
  CORS_ALLOWED_ORIGINS: 'http://localhost:3000,https://novafuse.com',
  RATE_LIMIT_WINDOW_MS: '900000', // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: '100',
  
  // Node environment
  NODE_ENV: 'development' // Set to 'production' in production
};

/**
 * Create a directory if it doesn't exist
 * 
 * @param {string} dir - Directory path
 */
function createDirectoryIfNotExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`${colors.green}Created directory: ${dir}${colors.reset}`);
  }
}

/**
 * Generate a .env file with the environment variables
 * 
 * @param {string} filePath - Path to the .env file
 * @param {Object} variables - Environment variables
 */
function generateEnvFile(filePath, variables) {
  const content = Object.entries(variables)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  fs.writeFileSync(filePath, content, { mode: 0o600 });
  console.log(`${colors.green}Generated ${filePath}${colors.reset}`);
}

/**
 * Generate a .env.example file with placeholders
 * 
 * @param {string} filePath - Path to the .env.example file
 * @param {Object} variables - Environment variables
 */
function generateEnvExampleFile(filePath, variables) {
  const content = Object.entries(variables)
    .map(([key, value]) => {
      // Replace actual keys with placeholders
      if (key.includes('KEY') && !key.includes('PATH')) {
        return `${key}=your-secure-key-here`;
      }
      return `${key}=${value}`;
    })
    .join('\n');
  
  fs.writeFileSync(filePath, content);
  console.log(`${colors.green}Generated ${filePath}${colors.reset}`);
}

/**
 * Generate self-signed TLS certificates for development
 */
function generateTLSCertificates() {
  const TLS_DIR = path.resolve(CONFIG_DIR, 'tls');
  createDirectoryIfNotExists(TLS_DIR);
  
  try {
    console.log(`${colors.yellow}Generating self-signed TLS certificates...${colors.reset}`);
    
    // Generate private key
    execSync(`openssl genrsa -out ${path.resolve(TLS_DIR, 'key.pem')} 2048`);
    
    // Generate certificate signing request
    execSync(`openssl req -new -key ${path.resolve(TLS_DIR, 'key.pem')} -out ${path.resolve(TLS_DIR, 'csr.pem')} -subj "/C=US/ST=State/L=City/O=NovaConnect/CN=localhost"`);
    
    // Generate self-signed certificate
    execSync(`openssl x509 -req -days 365 -in ${path.resolve(TLS_DIR, 'csr.pem')} -signkey ${path.resolve(TLS_DIR, 'key.pem')} -out ${path.resolve(TLS_DIR, 'cert.pem')}`);
    
    // Remove certificate signing request
    fs.unlinkSync(path.resolve(TLS_DIR, 'csr.pem'));
    
    console.log(`${colors.green}✓ TLS certificates generated successfully${colors.reset}`);
  } catch (error) {
    console.log(`${colors.yellow}! Could not generate TLS certificates: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}! You will need to provide your own certificates for TLS.${colors.reset}`);
  }
}

/**
 * Configure encryption
 */
function configureEncryption() {
  console.log(`${colors.bright}${colors.magenta}=== Configuring Encryption ===${colors.reset}\n`);
  
  // Create configuration directory
  createDirectoryIfNotExists(CONFIG_DIR);
  
  // Create key directory
  createDirectoryIfNotExists(KEY_DIR);
  
  // Generate .env file
  generateEnvFile(ENV_FILE, envVars);
  
  // Generate .env.example file
  generateEnvExampleFile(ENV_EXAMPLE_FILE, envVars);
  
  // Generate TLS certificates
  generateTLSCertificates();
  
  console.log(`\n${colors.bright}${colors.green}Configuration complete!${colors.reset}`);
  console.log(`${colors.yellow}Important: Keep your .env file secure and never commit it to version control.${colors.reset}`);
  console.log(`${colors.yellow}In production, use a secure key management service instead of storing keys in environment variables.${colors.reset}`);
}

// Run the configuration
configureEncryption();
