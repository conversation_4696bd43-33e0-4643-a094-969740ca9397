/**
 * ConnectorService.ts
 * 
 * Service for managing data source connectors in the NovaCore system.
 * This service handles the integration with NovaConnect for data collection.
 */

import {
  Connector,
  ConnectorType,
  ConnectorStatus,
  AuthenticationType,
  AuthenticationConfig,
  ConnectorConfig,
  CollectionSchedule,
  HealthCheckResult,
  createConnector,
  updateConnectorStatus,
  updateConnectorHealthCheck,
  calculateNextRunTime,
} from '../models/Connector';

import {
  CollectionJob,
  JobStatus,
  JobPriority,
  JobType,
  createCollectionJob,
  updateJobStatus,
  setJobResult,
} from '../models/CollectionJob';

import {
  Evidence,
  EvidenceType,
  EvidenceSource,
  EvidenceMetadata,
} from '../models/Evidence';

import { EvidenceService } from './EvidenceService';

/**
 * Connector service interface
 */
export interface IConnectorService {
  // Connector management
  createConnector(
    name: string,
    type: ConnectorType,
    authentication: AuthenticationConfig,
    config: ConnectorConfig,
    createdBy: string,
    organization: string,
    description?: string,
    schedule?: CollectionSchedule,
    metadata?: Record<string, any>,
    tags?: string[],
  ): Promise<Connector>;
  
  getConnector(id: string): Promise<Connector>;
  updateConnector(id: string, updates: Partial<Connector>, updatedBy: string): Promise<Connector>;
  deleteConnector(id: string): Promise<boolean>;
  
  // Connector operations
  activateConnector(id: string, updatedBy: string): Promise<Connector>;
  deactivateConnector(id: string, updatedBy: string): Promise<Connector>;
  testConnection(id: string): Promise<HealthCheckResult>;
  
  // Collection jobs
  createCollectionJob(
    connectorId: string,
    name: string,
    priority: JobPriority,
    type: JobType,
    createdBy: string,
    parameters?: Record<string, any>,
    scheduledAt?: Date,
  ): Promise<CollectionJob>;
  
  getCollectionJob(id: string): Promise<CollectionJob>;
  executeCollectionJob(id: string): Promise<CollectionJob>;
  cancelCollectionJob(id: string): Promise<CollectionJob>;
  
  // Scheduling
  scheduleCollection(connectorId: string, schedule: CollectionSchedule, updatedBy: string): Promise<Connector>;
  getScheduledCollections(): Promise<Connector[]>;
  
  // Search and filtering
  searchConnectors(query: any, page?: number, pageSize?: number): Promise<{ items: Connector[], total: number }>;
}

/**
 * Connector service implementation
 */
export class ConnectorService implements IConnectorService {
  private connectorStore: Map<string, Connector> = new Map();
  private jobStore: Map<string, CollectionJob> = new Map();
  private evidenceService: EvidenceService;
  
  constructor(evidenceService?: EvidenceService) {
    this.evidenceService = evidenceService || new EvidenceService();
  }
  
  /**
   * Create new connector
   */
  public async createConnector(
    name: string,
    type: ConnectorType,
    authentication: AuthenticationConfig,
    config: ConnectorConfig,
    createdBy: string,
    organization: string,
    description?: string,
    schedule?: CollectionSchedule,
    metadata?: Record<string, any>,
    tags?: string[],
  ): Promise<Connector> {
    // Create connector
    const connector = createConnector(
      name,
      type,
      authentication,
      config,
      createdBy,
      organization,
      description,
      schedule,
      metadata,
      tags,
    );
    
    // Store connector
    this.connectorStore.set(connector.id, connector);
    
    return connector;
  }
  
  /**
   * Get connector by ID
   */
  public async getConnector(id: string): Promise<Connector> {
    const connector = this.connectorStore.get(id);
    if (!connector) {
      throw new Error(`Connector ${id} not found`);
    }
    return connector;
  }
  
  /**
   * Update connector
   */
  public async updateConnector(id: string, updates: Partial<Connector>, updatedBy: string): Promise<Connector> {
    const connector = await this.getConnector(id);
    
    // Apply updates
    const updatedConnector = {
      ...connector,
      ...updates,
      updatedAt: new Date(),
      updatedBy,
    };
    
    // Store updated connector
    this.connectorStore.set(id, updatedConnector);
    
    return updatedConnector;
  }
  
  /**
   * Delete connector
   */
  public async deleteConnector(id: string): Promise<boolean> {
    // Check if connector exists
    if (!this.connectorStore.has(id)) {
      throw new Error(`Connector ${id} not found`);
    }
    
    // Delete connector
    return this.connectorStore.delete(id);
  }
  
  /**
   * Activate connector
   */
  public async activateConnector(id: string, updatedBy: string): Promise<Connector> {
    const connector = await this.getConnector(id);
    
    // Test connection before activation
    const healthCheck = await this.testConnection(id);
    
    if (healthCheck.status === 'unhealthy') {
      throw new Error(`Cannot activate connector: ${healthCheck.errorMessage}`);
    }
    
    // Update connector status
    const updatedConnector = updateConnectorStatus(
      connector,
      ConnectorStatus.ACTIVE,
      updatedBy,
      { activatedAt: new Date() },
    );
    
    // Update connector health check
    const connectorWithHealth = updateConnectorHealthCheck(
      updatedConnector,
      healthCheck,
    );
    
    // Store updated connector
    this.connectorStore.set(id, connectorWithHealth);
    
    return connectorWithHealth;
  }
  
  /**
   * Deactivate connector
   */
  public async deactivateConnector(id: string, updatedBy: string): Promise<Connector> {
    const connector = await this.getConnector(id);
    
    // Update connector status
    const updatedConnector = updateConnectorStatus(
      connector,
      ConnectorStatus.INACTIVE,
      updatedBy,
      { deactivatedAt: new Date() },
    );
    
    // Store updated connector
    this.connectorStore.set(id, updatedConnector);
    
    return updatedConnector;
  }
  
  /**
   * Test connector connection
   */
  public async testConnection(id: string): Promise<HealthCheckResult> {
    const connector = await this.getConnector(id);
    
    // In a real implementation, this would test the connection to the data source
    // For now, we'll simulate a successful connection
    const startTime = Date.now();
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Create health check result
    const healthCheck: HealthCheckResult = {
      status: 'healthy',
      timestamp: new Date(),
      responseTime,
      details: {
        connectorType: connector.type,
        baseUrl: connector.config.baseUrl,
      },
    };
    
    return healthCheck;
  }
  
  /**
   * Create collection job
   */
  public async createCollectionJob(
    connectorId: string,
    name: string,
    priority: JobPriority,
    type: JobType,
    createdBy: string,
    parameters?: Record<string, any>,
    scheduledAt?: Date,
  ): Promise<CollectionJob> {
    // Get connector
    const connector = await this.getConnector(connectorId);
    
    // Create job
    const job = createCollectionJob(
      name,
      connectorId,
      connector.type,
      priority,
      type,
      createdBy,
      connector.organization,
      `Collection job for ${connector.name}`,
      scheduledAt,
      parameters,
      undefined,
      3, // Max retries
    );
    
    // Store job
    this.jobStore.set(job.id, job);
    
    return job;
  }
  
  /**
   * Get collection job by ID
   */
  public async getCollectionJob(id: string): Promise<CollectionJob> {
    const job = this.jobStore.get(id);
    if (!job) {
      throw new Error(`Collection job ${id} not found`);
    }
    return job;
  }
  
  /**
   * Execute collection job
   */
  public async executeCollectionJob(id: string): Promise<CollectionJob> {
    const job = await this.getCollectionJob(id);
    
    // Update job status to running
    let updatedJob = updateJobStatus(job, JobStatus.RUNNING);
    this.jobStore.set(id, updatedJob);
    
    try {
      // Get connector
      const connector = await this.getConnector(job.connectorId);
      
      // In a real implementation, this would collect data from the source
      // For now, we'll simulate data collection
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create evidence from collected data
      const evidenceIds = await this.createEvidenceFromCollectedData(
        connector,
        job,
      );
      
      // Set job result
      const result = {
        status: 'success',
        evidenceIds,
        stats: {
          totalItems: evidenceIds.length,
          processedItems: evidenceIds.length,
          successfulItems: evidenceIds.length,
          failedItems: 0,
        },
      };
      
      updatedJob = setJobResult(updatedJob, result);
      this.jobStore.set(id, updatedJob);
      
      return updatedJob;
    } catch (error) {
      // Set job result with error
      const result = {
        status: 'failure',
        errorMessage: error.message,
        stats: {
          totalItems: 0,
          processedItems: 0,
          successfulItems: 0,
          failedItems: 1,
        },
      };
      
      updatedJob = setJobResult(updatedJob, result);
      this.jobStore.set(id, updatedJob);
      
      return updatedJob;
    }
  }
  
  /**
   * Cancel collection job
   */
  public async cancelCollectionJob(id: string): Promise<CollectionJob> {
    const job = await this.getCollectionJob(id);
    
    // Check if job can be cancelled
    if (job.status !== JobStatus.PENDING && job.status !== JobStatus.SCHEDULED) {
      throw new Error(`Cannot cancel job with status ${job.status}`);
    }
    
    // Update job status to cancelled
    const updatedJob = updateJobStatus(job, JobStatus.CANCELLED);
    this.jobStore.set(id, updatedJob);
    
    return updatedJob;
  }
  
  /**
   * Schedule collection
   */
  public async scheduleCollection(
    connectorId: string,
    schedule: CollectionSchedule,
    updatedBy: string,
  ): Promise<Connector> {
    const connector = await this.getConnector(connectorId);
    
    // Calculate next run time
    const nextRun = calculateNextRunTime(schedule);
    
    // Update schedule with next run time
    const updatedSchedule = {
      ...schedule,
      nextRun,
    };
    
    // Update connector with schedule
    const updatedConnector = {
      ...connector,
      schedule: updatedSchedule,
      updatedAt: new Date(),
      updatedBy,
    };
    
    // Store updated connector
    this.connectorStore.set(connectorId, updatedConnector);
    
    // Create scheduled job if enabled
    if (schedule.enabled) {
      await this.createCollectionJob(
        connectorId,
        `Scheduled collection for ${connector.name}`,
        JobPriority.MEDIUM,
        JobType.SCHEDULED,
        updatedBy,
        undefined,
        nextRun,
      );
    }
    
    return updatedConnector;
  }
  
  /**
   * Get scheduled collections
   */
  public async getScheduledCollections(): Promise<Connector[]> {
    // Convert Map to array
    const allConnectors = Array.from(this.connectorStore.values());
    
    // Filter connectors with enabled schedules
    return allConnectors.filter(c => c.schedule && c.schedule.enabled);
  }
  
  /**
   * Search connectors
   */
  public async searchConnectors(
    query: any,
    page: number = 1,
    pageSize: number = 10,
  ): Promise<{ items: Connector[], total: number }> {
    // Convert Map to array
    const allConnectors = Array.from(this.connectorStore.values());
    
    // Filter connectors based on query
    const filteredConnectors = this.filterConnectors(allConnectors, query);
    
    // Paginate results
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedConnectors = filteredConnectors.slice(startIndex, endIndex);
    
    return {
      items: paginatedConnectors,
      total: filteredConnectors.length,
    };
  }
  
  /**
   * Filter connectors based on query
   */
  private filterConnectors(connectors: Connector[], query: any): Connector[] {
    if (!query || Object.keys(query).length === 0) {
      return connectors;
    }
    
    return connectors.filter(c => {
      // Check each query parameter
      for (const [key, value] of Object.entries(query)) {
        // Handle special case for metadata
        if (key === 'metadata' && typeof value === 'object') {
          for (const [metaKey, metaValue] of Object.entries(value)) {
            if (c.metadata?.[metaKey] !== metaValue) {
              return false;
            }
          }
          continue;
        }
        
        // Handle array values (OR condition)
        if (Array.isArray(value)) {
          if (!value.includes(c[key])) {
            return false;
          }
          continue;
        }
        
        // Handle regular values
        if (c[key] !== value) {
          return false;
        }
      }
      
      return true;
    });
  }
  
  /**
   * Create evidence from collected data
   * This is a helper method for simulating data collection
   */
  private async createEvidenceFromCollectedData(
    connector: Connector,
    job: CollectionJob,
  ): Promise<string[]> {
    // In a real implementation, this would process the collected data
    // and create evidence records
    
    // For now, we'll create some sample evidence
    const evidenceIds: string[] = [];
    
    // Create 3 sample evidence records
    for (let i = 0; i < 3; i++) {
      const evidenceType = this.getEvidenceTypeForConnector(connector.type);
      const category = this.getCategoryForConnector(connector.type);
      
      const metadata: EvidenceMetadata = {
        source: this.getEvidenceSourceForConnector(connector.type),
        sourceId: `${connector.type}-${Date.now()}-${i}`,
        sourceUrl: connector.config.baseUrl,
        collectionDate: new Date(),
        tags: connector.tags,
      };
      
      // Create evidence
      const evidence = await this.evidenceService.createEvidence(
        `${connector.name} - Evidence ${i + 1}`,
        evidenceType,
        category,
        { data: `Sample data ${i + 1} from ${connector.name}` },
        job.createdBy,
        connector.organization,
        metadata,
        `Evidence collected from ${connector.name}`,
      );
      
      evidenceIds.push(evidence.id);
    }
    
    return evidenceIds;
  }
  
  /**
   * Get evidence type for connector type
   */
  private getEvidenceTypeForConnector(connectorType: ConnectorType): EvidenceType {
    switch (connectorType) {
      case ConnectorType.AWS:
      case ConnectorType.AZURE:
      case ConnectorType.GCP:
        return EvidenceType.CONFIGURATION;
      case ConnectorType.GITHUB:
        return EvidenceType.DOCUMENT;
      case ConnectorType.JIRA:
        return EvidenceType.REPORT;
      case ConnectorType.SLACK:
        return EvidenceType.LOG;
      default:
        return EvidenceType.API_RESPONSE;
    }
  }
  
  /**
   * Get evidence source for connector type
   */
  private getEvidenceSourceForConnector(connectorType: ConnectorType): EvidenceSource {
    switch (connectorType) {
      case ConnectorType.AWS:
        return EvidenceSource.AWS;
      case ConnectorType.AZURE:
        return EvidenceSource.AZURE;
      case ConnectorType.GCP:
        return EvidenceSource.GCP;
      case ConnectorType.GITHUB:
        return EvidenceSource.GITHUB;
      case ConnectorType.JIRA:
        return EvidenceSource.JIRA;
      case ConnectorType.SLACK:
        return EvidenceSource.SLACK;
      default:
        return EvidenceSource.API;
    }
  }
  
  /**
   * Get category for connector type
   */
  private getCategoryForConnector(connectorType: ConnectorType): string {
    switch (connectorType) {
      case ConnectorType.AWS:
      case ConnectorType.AZURE:
      case ConnectorType.GCP:
        return 'Cloud Configuration';
      case ConnectorType.GITHUB:
        return 'Source Code';
      case ConnectorType.JIRA:
        return 'Project Management';
      case ConnectorType.SLACK:
        return 'Communication';
      default:
        return 'External System';
    }
  }
}
