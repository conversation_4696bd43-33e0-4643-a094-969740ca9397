dbe79993c2037a32a92cb2a408e41637
/**
 * Brute Force Protection Service
 * 
 * This service handles brute force attack protection.
 */

const fs = require('fs').promises;
const path = require('path');
const {
  BruteForceError
} = require('../utils/errors');
class BruteForceProtectionService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.attemptsFile = path.join(this.dataDir, 'login_attempts.json');
    this.config = {
      maxAttempts: 5,
      // Maximum number of failed attempts
      windowMs: 15 * 60 * 1000,
      // 15 minutes
      blockDuration: 30 * 60 * 1000 // 30 minutes
    };
    this.ensureDataDir();

    // Clean up expired attempts every 5 minutes
    setInterval(() => this.cleanupExpiredAttempts(), 5 * 60 * 1000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, {
        recursive: true
      });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load login attempts from file
   */
  async loadAttempts() {
    try {
      const data = await fs.readFile(this.attemptsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty object
        return {};
      }
      console.error('Error loading login attempts:', error);
      throw error;
    }
  }

  /**
   * Save login attempts to file
   */
  async saveAttempts(attempts) {
    try {
      await fs.writeFile(this.attemptsFile, JSON.stringify(attempts, null, 2));
    } catch (error) {
      console.error('Error saving login attempts:', error);
      throw error;
    }
  }

  /**
   * Record a failed login attempt
   */
  async recordFailedAttempt(identifier) {
    const attempts = await this.loadAttempts();
    const now = Date.now();

    // Initialize attempts for this identifier if not exists
    if (!attempts[identifier]) {
      attempts[identifier] = {
        count: 0,
        firstAttempt: now,
        lastAttempt: now,
        blocked: false,
        blockedUntil: null
      };
    }

    // Update attempts
    attempts[identifier].count++;
    attempts[identifier].lastAttempt = now;

    // Check if account should be blocked
    if (attempts[identifier].count >= this.config.maxAttempts) {
      attempts[identifier].blocked = true;
      attempts[identifier].blockedUntil = now + this.config.blockDuration;
    }

    // Save attempts
    await this.saveAttempts(attempts);
    return attempts[identifier];
  }

  /**
   * Reset login attempts for an identifier
   */
  async resetAttempts(identifier) {
    const attempts = await this.loadAttempts();
    if (attempts[identifier]) {
      delete attempts[identifier];
      await this.saveAttempts(attempts);
    }
    return true;
  }

  /**
   * Check if an identifier is blocked
   */
  async isBlocked(identifier) {
    const attempts = await this.loadAttempts();
    const now = Date.now();
    if (!attempts[identifier]) {
      return false;
    }

    // Check if blocked and block duration has not expired
    if (attempts[identifier].blocked && attempts[identifier].blockedUntil > now) {
      const remainingTime = Math.ceil((attempts[identifier].blockedUntil - now) / 1000);
      return {
        blocked: true,
        remainingTime,
        attemptsCount: attempts[identifier].count,
        maxAttempts: this.config.maxAttempts
      };
    }

    // If block duration has expired, reset the block
    if (attempts[identifier].blocked && attempts[identifier].blockedUntil <= now) {
      attempts[identifier].blocked = false;
      attempts[identifier].blockedUntil = null;
      attempts[identifier].count = 0;
      await this.saveAttempts(attempts);
    }
    return false;
  }

  /**
   * Check login attempt before processing
   */
  async checkLoginAttempt(identifier) {
    // Check if identifier is blocked
    const blockStatus = await this.isBlocked(identifier);
    if (blockStatus && blockStatus.blocked) {
      throw new BruteForceError(`Too many failed login attempts. Account is temporarily blocked. Try again in ${blockStatus.remainingTime} seconds.`, blockStatus.remainingTime);
    }
    return true;
  }

  /**
   * Handle successful login
   */
  async handleSuccessfulLogin(identifier) {
    return this.resetAttempts(identifier);
  }

  /**
   * Handle failed login
   */
  async handleFailedLogin(identifier) {
    const attempt = await this.recordFailedAttempt(identifier);
    if (attempt.blocked) {
      const remainingTime = Math.ceil((attempt.blockedUntil - Date.now()) / 1000);
      throw new BruteForceError(`Too many failed login attempts. Account is temporarily blocked. Try again in ${remainingTime} seconds.`, remainingTime);
    }
    return {
      attemptsCount: attempt.count,
      maxAttempts: this.config.maxAttempts,
      remainingAttempts: this.config.maxAttempts - attempt.count
    };
  }

  /**
   * Clean up expired attempts
   */
  async cleanupExpiredAttempts() {
    try {
      const attempts = await this.loadAttempts();
      const now = Date.now();
      let modified = false;
      for (const identifier in attempts) {
        // Remove attempts that are older than the window
        if (now - attempts[identifier].lastAttempt > this.config.windowMs && !attempts[identifier].blocked) {
          delete attempts[identifier];
          modified = true;
        }

        // Reset blocks that have expired
        if (attempts[identifier] && attempts[identifier].blocked && attempts[identifier].blockedUntil <= now) {
          attempts[identifier].blocked = false;
          attempts[identifier].blockedUntil = null;
          attempts[identifier].count = 0;
          modified = true;
        }
      }
      if (modified) {
        await this.saveAttempts(attempts);
      }
    } catch (error) {
      console.error('Error cleaning up expired attempts:', error);
    }
  }

  /**
   * Update brute force protection configuration
   */
  async updateConfig(config) {
    this.config = {
      ...this.config,
      ...config
    };
    return this.config;
  }

  /**
   * Get brute force protection configuration
   */
  getConfig() {
    return this.config;
  }
}
module.exports = BruteForceProtectionService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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