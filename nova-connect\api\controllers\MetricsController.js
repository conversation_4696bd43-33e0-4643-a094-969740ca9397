/**
 * Metrics Controller
 * 
 * This controller handles metrics-related endpoints.
 */

const prometheusMetrics = require('../services/PrometheusMetricsService');
const googleCloudMonitoring = require('../services/GoogleCloudMonitoringService');
const logger = require('../utils/logger');

/**
 * Get Prometheus metrics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getMetrics = async (req, res, next) => {
  try {
    res.set('Content-Type', prometheusMetrics.getContentType());
    res.end(await prometheusMetrics.getMetrics());
  } catch (error) {
    logger.error('Error generating metrics', { error });
    next(error);
  }
};

/**
 * Get health status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getHealth = async (req, res, next) => {
  try {
    // Simple health check
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime()
    });
  } catch (error) {
    logger.error('Error checking health', { error });
    next(error);
  }
};

/**
 * Get detailed health status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getDetailedHealth = async (req, res, next) => {
  try {
    // Get memory usage
    const memoryUsage = process.memoryUsage();
    
    // Get CPU usage
    const cpuUsage = process.cpuUsage();
    
    // Get system information
    const systemInfo = {
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version,
      cpus: require('os').cpus().length
    };
    
    // Return detailed health information
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB'
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      system: systemInfo
    });
  } catch (error) {
    logger.error('Error checking detailed health', { error });
    next(error);
  }
};

/**
 * Get readiness status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const getReadiness = async (req, res, next) => {
  try {
    // Check database connection
    // In a real implementation, you would check the database connection
    const databaseConnected = true;
    
    // Check external dependencies
    // In a real implementation, you would check external dependencies
    const dependenciesHealthy = true;
    
    if (databaseConnected && dependenciesHealthy) {
      res.json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        details: {
          database: databaseConnected ? 'connected' : 'disconnected',
          dependencies: dependenciesHealthy ? 'healthy' : 'unhealthy'
        }
      });
    }
  } catch (error) {
    logger.error('Error checking readiness', { error });
    next(error);
  }
};

module.exports = {
  getMetrics,
  getHealth,
  getDetailedHealth,
  getReadiness
};
