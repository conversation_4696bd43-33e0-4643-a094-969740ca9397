/**
 * NovaShield - Threat Intelligence Service
 * 
 * This service provides threat intelligence capabilities.
 */

const { createLogger } = require('../../utils/logger');
const threatAnalyzer = require('../utils/threat-analyzer');

const logger = createLogger('threat-intelligence-service');

// In-memory storage for threats (would be replaced with a database in production)
const threats = [
  {
    id: 'threat-001',
    vendorId: 'vendor-001',
    type: 'data-breach',
    severity: 'high',
    description: 'Potential data breach reported',
    source: 'threat-feed',
    detectedAt: '2023-03-15T00:00:00.000Z',
    status: 'active',
    details: {
      affectedSystems: ['customer-data', 'payment-processing'],
      potentialImpact: 'Exposure of sensitive customer information',
      recommendedActions: [
        'Verify breach with vendor',
        'Assess potential impact on systems',
        'Prepare incident response'
      ]
    }
  },
  {
    id: 'threat-002',
    vendorId: 'vendor-002',
    type: 'vulnerability',
    severity: 'medium',
    description: 'Critical vulnerability in vendor software',
    source: 'cve-database',
    detectedAt: '2023-02-28T00:00:00.000Z',
    status: 'mitigated',
    details: {
      cveId: 'CVE-2023-12345',
      affectedSystems: ['inventory-management'],
      potentialImpact: 'Unauthorized access to inventory data',
      recommendedActions: [
        'Apply vendor patch',
        'Implement additional access controls'
      ]
    }
  },
  {
    id: 'threat-003',
    vendorId: 'vendor-003',
    type: 'compliance',
    severity: 'low',
    description: 'Potential compliance issue with data handling',
    source: 'regulatory-update',
    detectedAt: '2023-04-05T00:00:00.000Z',
    status: 'investigating',
    details: {
      regulation: 'GDPR',
      affectedSystems: ['data-storage'],
      potentialImpact: 'Regulatory fines and penalties',
      recommendedActions: [
        'Review vendor data handling practices',
        'Update data processing agreement'
      ]
    }
  }
];

/**
 * Get threats for a vendor
 * 
 * @param {string} vendorId - Vendor ID
 * @returns {Promise<Array>} - List of threats
 */
async function getVendorThreats(vendorId) {
  logger.debug('Getting vendor threats', { vendorId });
  
  // Find all threats for the vendor
  const vendorThreats = threats.filter(t => t.vendorId === vendorId);
  
  // Analyze threats
  const analyzedThreats = await threatAnalyzer.analyzeThreats(vendorThreats);
  
  return analyzedThreats;
}

/**
 * Get threat summary
 * 
 * @returns {Promise<Object>} - Threat summary
 */
async function getThreatSummary() {
  logger.debug('Getting threat summary');
  
  // Count threats by severity
  const highSeverityCount = threats.filter(t => t.severity === 'high').length;
  const mediumSeverityCount = threats.filter(t => t.severity === 'medium').length;
  const lowSeverityCount = threats.filter(t => t.severity === 'low').length;
  
  // Count threats by type
  const threatTypes = {};
  threats.forEach(threat => {
    threatTypes[threat.type] = (threatTypes[threat.type] || 0) + 1;
  });
  
  // Count threats by status
  const threatStatuses = {};
  threats.forEach(threat => {
    threatStatuses[threat.status] = (threatStatuses[threat.status] || 0) + 1;
  });
  
  return {
    totalThreats: threats.length,
    severityDistribution: {
      high: highSeverityCount,
      medium: mediumSeverityCount,
      low: lowSeverityCount
    },
    typeDistribution: threatTypes,
    statusDistribution: threatStatuses,
    latestThreats: threats
      .sort((a, b) => new Date(b.detectedAt) - new Date(a.detectedAt))
      .slice(0, 5)
      .map(threat => ({
        id: threat.id,
        vendorId: threat.vendorId,
        type: threat.type,
        severity: threat.severity,
        description: threat.description,
        detectedAt: threat.detectedAt,
        status: threat.status
      }))
  };
}

/**
 * Add a new threat
 * 
 * @param {Object} threatData - Threat data
 * @returns {Promise<Object>} - Created threat
 */
async function addThreat(threatData) {
  logger.info('Adding threat', { 
    vendorId: threatData.vendorId,
    type: threatData.type
  });
  
  // Validate required fields
  if (!threatData.vendorId) {
    throw new Error('Vendor ID is required');
  }
  
  if (!threatData.type) {
    throw new Error('Threat type is required');
  }
  
  if (!threatData.description) {
    throw new Error('Threat description is required');
  }
  
  // Create threat object
  const threat = {
    id: threatData.id || `threat-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    vendorId: threatData.vendorId,
    type: threatData.type,
    severity: threatData.severity || 'medium',
    description: threatData.description,
    source: threatData.source || 'manual',
    detectedAt: threatData.detectedAt || new Date().toISOString(),
    status: threatData.status || 'active',
    details: threatData.details || {}
  };
  
  // Add to threats
  threats.push(threat);
  
  return threat;
}

/**
 * Update threat status
 * 
 * @param {string} threatId - Threat ID
 * @param {string} status - New status
 * @param {Object} details - Additional details
 * @returns {Promise<Object>} - Updated threat
 */
async function updateThreatStatus(threatId, status, details = {}) {
  logger.info('Updating threat status', { threatId, status });
  
  // Find threat
  const index = threats.findIndex(t => t.id === threatId);
  
  if (index === -1) {
    logger.warn('Threat not found', { threatId });
    throw new Error(`Threat not found with ID: ${threatId}`);
  }
  
  // Update threat
  const updatedThreat = {
    ...threats[index],
    status,
    details: {
      ...threats[index].details,
      ...details,
      statusHistory: [
        ...(threats[index].details.statusHistory || []),
        {
          status,
          timestamp: new Date().toISOString(),
          notes: details.notes
        }
      ]
    }
  };
  
  threats[index] = updatedThreat;
  
  return updatedThreat;
}

module.exports = {
  getVendorThreats,
  getThreatSummary,
  addThreat,
  updateThreatStatus
};
