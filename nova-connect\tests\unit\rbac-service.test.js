/**
 * RBAC Service Unit Tests
 * 
 * This file contains unit tests for the RBAC service.
 */

const mongoose = require('mongoose');
const RBACService = require('../../api/services/RBACService');
const Role = require('../../api/models/Role');
const Permission = require('../../api/models/Permission');
const UserRole = require('../../api/models/UserRole');
const User = require('../../api/models/User');
const { ValidationError, NotFoundError } = require('../../api/utils/errors');
const { setupTestEnvironment, clearDatabase, disconnectFromDatabase, getTestData } = require('../setup/rbac-test-setup');

// Initialize RBAC service
const rbacService = new RBACService();

// Test data
let adminUser;
let regularUser;
let viewerUser;
let testRole;
let testPermission;

// Setup and teardown
beforeAll(async () => {
  try {
    const testData = await setupTestEnvironment();
    adminUser = testData.adminUser;
    regularUser = testData.regularUser;
    viewerUser = testData.viewerUser;
    testRole = testData.testRole;
    testPermission = testData.testPermission;
  } catch (error) {
    console.error('Error in beforeAll:', error);
    throw error;
  }
});

afterAll(async () => {
  await disconnectFromDatabase();
});

// Reset database between tests
afterEach(async () => {
  // No need to clear database between tests as we're using transactions
});

// Test suites
describe('RBACService', () => {
  // Role tests
  describe('Role Management', () => {
    test('getAllRoles should return all roles', async () => {
      const roles = await rbacService.getAllRoles();
      expect(roles).toBeDefined();
      expect(Array.isArray(roles)).toBe(true);
      expect(roles.length).toBeGreaterThan(0);
      
      // Check if roles have expected properties
      const role = roles[0];
      expect(role).toHaveProperty('_id');
      expect(role).toHaveProperty('name');
      expect(role).toHaveProperty('permissions');
    });
    
    test('getRoleById should return a role by ID', async () => {
      const role = await rbacService.getRoleById(testRole._id);
      expect(role).toBeDefined();
      expect(role._id.toString()).toBe(testRole._id.toString());
      expect(role.name).toBe(testRole.name);
    });
    
    test('getRoleById should throw NotFoundError for non-existent role', async () => {
      const nonExistentId = new mongoose.Types.ObjectId();
      await expect(rbacService.getRoleById(nonExistentId)).rejects.toThrow(NotFoundError);
    });
    
    test('createRole should create a new role', async () => {
      const newRole = {
        name: 'New Test Role',
        description: 'A new role for testing',
        permissions: [testPermission._id]
      };
      
      const createdRole = await rbacService.createRole(newRole);
      expect(createdRole).toBeDefined();
      expect(createdRole.name).toBe(newRole.name);
      expect(createdRole.description).toBe(newRole.description);
      expect(Array.isArray(createdRole.permissions)).toBe(true);
      expect(createdRole.permissions.length).toBe(1);
      
      // Clean up
      await Role.deleteOne({ _id: createdRole._id });
    });
    
    test('createRole should handle string permission format', async () => {
      const newRole = {
        name: 'String Permission Role',
        description: 'A role with string permission format',
        permissions: ['resource:view']
      };
      
      const createdRole = await rbacService.createRole(newRole);
      expect(createdRole).toBeDefined();
      expect(createdRole.name).toBe(newRole.name);
      expect(Array.isArray(createdRole.permissions)).toBe(true);
      expect(createdRole.permissions.length).toBe(1);
      
      // Clean up
      await Role.deleteOne({ _id: createdRole._id });
    });
    
    test('createRole should throw ValidationError for duplicate role name', async () => {
      const duplicateRole = {
        name: testRole.name,
        description: 'This should fail',
        permissions: [testPermission._id]
      };
      
      await expect(rbacService.createRole(duplicateRole)).rejects.toThrow(ValidationError);
    });
    
    test('updateRole should update a role', async () => {
      // Create a role to update
      const roleToUpdate = await Role.create({
        name: 'Role To Update',
        description: 'This role will be updated',
        permissions: [testPermission._id]
      });
      
      const updateData = {
        name: 'Updated Role Name',
        description: 'This role has been updated'
      };
      
      const updatedRole = await rbacService.updateRole(roleToUpdate._id, updateData);
      expect(updatedRole).toBeDefined();
      expect(updatedRole.name).toBe(updateData.name);
      expect(updatedRole.description).toBe(updateData.description);
      
      // Clean up
      await Role.deleteOne({ _id: roleToUpdate._id });
    });
    
    test('deleteRole should delete a role', async () => {
      // Create a role to delete
      const roleToDelete = await Role.create({
        name: 'Role To Delete',
        description: 'This role will be deleted',
        permissions: [testPermission._id]
      });
      
      const result = await rbacService.deleteRole(roleToDelete._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      
      // Verify role is deleted
      const deletedRole = await Role.findById(roleToDelete._id);
      expect(deletedRole).toBeNull();
    });
  });
  
  // Permission tests
  describe('Permission Management', () => {
    test('getAllPermissions should return all permissions', async () => {
      const permissions = await rbacService.getAllPermissions();
      expect(permissions).toBeDefined();
      expect(Array.isArray(permissions)).toBe(true);
      expect(permissions.length).toBeGreaterThan(0);
      
      // Check if permissions have expected properties
      const permission = permissions[0];
      expect(permission).toHaveProperty('_id');
      expect(permission).toHaveProperty('name');
      expect(permission).toHaveProperty('resource');
      expect(permission).toHaveProperty('action');
    });
    
    test('getPermissionById should return a permission by ID', async () => {
      const permission = await rbacService.getPermissionById(testPermission._id);
      expect(permission).toBeDefined();
      expect(permission._id.toString()).toBe(testPermission._id.toString());
      expect(permission.name).toBe(testPermission.name);
    });
    
    test('getPermissionByResourceAction should return a permission by resource and action', async () => {
      const permission = await rbacService.getPermissionByResourceAction('resource', 'view');
      expect(permission).toBeDefined();
      expect(permission.resource).toBe('resource');
      expect(permission.action).toBe('view');
    });
    
    test('createPermission should create a new permission', async () => {
      const newPermission = {
        name: 'New Test Permission',
        description: 'A new permission for testing',
        resource: 'test',
        action: 'test'
      };
      
      const createdPermission = await rbacService.createPermission(newPermission);
      expect(createdPermission).toBeDefined();
      expect(createdPermission.name).toBe(newPermission.name);
      expect(createdPermission.resource).toBe(newPermission.resource);
      expect(createdPermission.action).toBe(newPermission.action);
      
      // Clean up
      await Permission.deleteOne({ _id: createdPermission._id });
    });
  });
  
  // User role tests
  describe('User Role Management', () => {
    test('getUserRoles should return roles for a user', async () => {
      const roles = await rbacService.getUserRoles(adminUser._id);
      expect(roles).toBeDefined();
      expect(Array.isArray(roles)).toBe(true);
      expect(roles.length).toBeGreaterThan(0);
      
      // Admin should have Administrator role
      const adminRole = roles.find(role => role.name === 'Administrator');
      expect(adminRole).toBeDefined();
    });
    
    test('assignRoleToUser should assign a role to a user', async () => {
      // Assign test role to regular user
      const result = await rbacService.assignRoleToUser(regularUser._id, testRole._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      
      // Verify role was assigned
      const userRoles = await rbacService.getUserRoles(regularUser._id);
      const hasTestRole = userRoles.some(role => role._id.toString() === testRole._id.toString());
      expect(hasTestRole).toBe(true);
      
      // Clean up
      await UserRole.deleteOne({ user: regularUser._id, role: testRole._id });
    });
    
    test('removeRoleFromUser should remove a role from a user', async () => {
      // First assign the role
      await UserRole.create({
        user: regularUser._id,
        role: testRole._id
      });
      
      // Then remove it
      const result = await rbacService.removeRoleFromUser(regularUser._id, testRole._id);
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      
      // Verify role was removed
      const userRoles = await rbacService.getUserRoles(regularUser._id);
      const hasTestRole = userRoles.some(role => role._id.toString() === testRole._id.toString());
      expect(hasTestRole).toBe(false);
    });
  });
  
  // Permission checking tests
  describe('Permission Checking', () => {
    test('hasPermission should return true for admin with wildcard permission', async () => {
      const hasPermission = await rbacService.hasPermission(adminUser._id, 'any:permission');
      expect(hasPermission).toBe(true);
    });
    
    test('hasPermission should return true for user with specific permission', async () => {
      const hasPermission = await rbacService.hasPermission(regularUser._id, 'resource:view');
      expect(hasPermission).toBe(true);
    });
    
    test('hasPermission should return false for user without permission', async () => {
      const hasPermission = await rbacService.hasPermission(viewerUser._id, 'resource:delete');
      expect(hasPermission).toBe(false);
    });
    
    test('getUserPermissions should return all permissions for a user', async () => {
      const permissions = await rbacService.getUserPermissions(regularUser._id);
      expect(permissions).toBeDefined();
      expect(Array.isArray(permissions)).toBe(true);
      expect(permissions.length).toBeGreaterThan(0);
      
      // Regular user should have resource:view permission
      const hasViewPermission = permissions.some(p => p === 'resource:view' || p.toString() === testPermission._id.toString());
      expect(hasViewPermission).toBe(true);
    });
  });
});
