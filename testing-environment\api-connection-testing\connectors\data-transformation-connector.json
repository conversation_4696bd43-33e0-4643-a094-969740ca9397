{"metadata": {"name": "Data Transformation Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for data transformation", "author": "NovaGRC", "tags": ["test", "transformation"]}, "authentication": {"type": "API_KEY", "fields": {"apiKey": {"type": "string", "description": "API Key", "required": true, "sensitive": true}, "headerName": {"type": "string", "description": "Header name for the API key", "required": true, "default": "X-API-Key"}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getFindings", "name": "Get Findings", "path": "/transform/findings", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getFindings", "targetSystem": "NovaGRC", "targetEntity": "SecurityFindings", "transformations": [{"source": "$.Findings[*].Id", "target": "findingIds", "transform": "identity"}, {"source": "$.Findings[*].Title", "target": "titles", "transform": "identity"}, {"source": "$.Findings[*].Severity", "target": "severities", "transform": "identity"}, {"source": "$.Findings[*].Status", "target": "statuses", "transform": "identity"}, {"source": "$.Findings[*].Compliance.Status", "target": "complianceStatuses", "transform": "mapComplianceStatus"}, {"source": "$.Findings[*].Severity", "target": "riskLevels", "transform": "mapSeverityToRisk"}, {"source": "$.Findings[*].CreatedAt", "target": "createdDates", "transform": "formatDate", "parameters": {"format": "YYYY-MM-DD"}}]}], "events": {"webhooks": [], "polling": []}}