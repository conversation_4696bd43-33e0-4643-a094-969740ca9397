import os
import uuid
from datetime import datetime, timedelta
from supabase import create_client, Client
from dotenv import load_dotenv
import random

# Load environment variables from .env file
load_dotenv()

# Initialize Supabase client
url = os.getenv('SUPABASE_URL')
key = os.getenv('SUPABASE_KEY')
supabase: Client = create_client(url, key)

def generate_nova_dna():
    """Generate a mock NovaDNA signature"""
    return f"NOVA-{uuid.uuid4().hex[:16].upper()}"

def create_sample_vendors(count=10):
    """Create sample vendors with random Q-Scores"""
    vendors = []
    for i in range(1, count + 1):
        vendor = {
            'name': f'Vendor {i}',
            'current_q_score': round(random.uniform(5.0, 9.9), 1),
            'nova_dna_signature': generate_nova_dna()
        }
        vendors.append(vendor)
    
    result = supabase.table('vendors').insert(vendors).execute()
    print(f"Created {len(result.data)} vendors")
    return result.data

def create_sample_tasks(vendors, count=30):
    """Create sample tasks with random attributes"""
    task_types = ['critical_bugfix', 'feature_dev', 'documentation', 'testing', 'deployment']
    roles = supabase.table('roles').select('*').execute().data
    
    tasks = []
    for i in range(count):
        task = {
            'name': f'Task {i+1}',
            'type': random.choice(task_types),
            'budget': random.randint(500, 10000),
            'deadline_hours': random.randint(1, 168),  # 1 hour to 1 week
            'vendor_id': random.choice(vendors)['id'],
            'role_id': random.choice(roles)['id']
        }
        tasks.append(task)
    
    result = supabase.table('tasks').insert(tasks).execute()
    print(f"Created {len(result.data)} tasks")
    return result.data

def enable_row_level_security():
    """Enable Row Level Security on all tables"""
    tables = ['roles', 'entropy_modifiers', 'vendors', 'tasks', 'decisions']
    
    for table in tables:
        # Enable RLS
        supabase.rpc('enable_rls', {'table': table}).execute()
        print(f"Enabled RLS on {table}")

def main():
    print("Starting database seeding...")
    
    # Create sample data
    vendors = create_sample_vendors()
    tasks = create_sample_tasks(vendors)
    
    # Enable RLS after initial data is loaded
    enable_row_level_security()
    
    print("\nDatabase seeding completed successfully!")
    print("Next steps:")
    print("1. Set up authentication policies in Supabase")
    print("2. Configure storage buckets if needed")
    print("3. Set up database backups")

if __name__ == "__main__":
    main()
