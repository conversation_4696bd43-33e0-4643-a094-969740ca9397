<!DOCTYPE html>
<html>
<head>
    <title>TEE Energy Flow Diagram Preview</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 40px;
            margin-top: 20px;
        }
        .diagram {
            flex: 1;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .legend {
            flex: 0 0 300px;
            padding: 15px;
            background: #f0f7f4;
            border-radius: 8px;
            border-left: 4px solid #2E7D32;
        }
        h1 {
            color: #2E7D32;
            text-align: center;
        }
        .legend-item {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .efficient { background: #E8F5E9; border-left: 4px solid #2E7D32; }
        .inefficient { background: #FFEBEE; border-left: 4px solid #B71C1C; }
        .input { background: #E8F5E9; border-left: 4px solid #1B5E20; }
        .neutral { background: #ECEFF1; border-left: 4px solid #546E7A; }
    </style>
</head>
<body>
    <h1>TEE Energy Flow Diagram</h1>
    
    <div class="container">
        <div class="diagram">
            <div class="mermaid">
                flowchart TD
                    %% Input Energy
                    A[Time & Energy Input] -->|Allocation| B[Task Execution]
                    
                    %% Friction Points
                    B --> C{Friction Analysis}
                    C -->|High Friction| D[Inefficient Path]
                    C -->|Optimized| E[Efficient Path]
                    
                    %% Efficiency Conversion
                    D --> F[Energy Loss: High]
                    E --> G[Energy Gain: High]
                    
                    %% Output
                    F --> H[Low ROI]
                    G --> I[High ROI]
                    
                    %% Styling
                    classDef input fill:#2E7D32,color:white,stroke:#1B5E20
                    classDef efficient fill:#2E7D32,color:white,stroke:#1B5E20
                    classDef inefficient fill:#B71C1C,color:white,stroke:#7F0000
                    classDef neutral fill:#546E7A,color:white,stroke:#263238
                    
                    class A input
                    class B,E,G,I efficient
                    class D,F,H inefficient
                    class C neutral
            </div>
        </div>
        
        <div class="legend">
            <h3>Diagram Legend</h3>
            
            <div class="legend-item input">
                <strong>Input Node</strong><br>
                Starting point of time/energy
            </div>
            
            <div class="legend-item efficient">
                <strong>Efficient Path</strong><br>
                High η (efficiency), Low F (friction)
            </div>
            
            <div class="legend-item inefficient">
                <strong>Inefficient Path</strong><br>
                Low η, High F, Energy drain
            </div>
            
            <div class="legend-item neutral">
                <strong>Decision Point</strong><br>
                Critical analysis juncture
            </div>
            
            <h3>Key Metrics</h3>
            <ul>
                <li>η (eta) = Efficiency</li>
                <li>F = Friction</li>
                <li>ROI = Return on Investment</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
