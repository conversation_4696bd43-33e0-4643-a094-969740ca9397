/**
 * Adaptive Trinity CSDE Demo
 * 
 * This script demonstrates the Adaptive Trinity CSDE with self-optimizing ratios.
 * It shows how the system dynamically adjusts the 18/82 ratio based on empirical results.
 */

const { AdaptiveTrinityCSDE } = require('../src/csde');
const fs = require('fs');
const path = require('path');

// Create a logger
const logger = {
  info: (message) => console.log(`[INFO] ${message}`),
  error: (message) => console.log(`[ERROR] ${message}`),
  warn: (message) => console.log(`[WARN] ${message}`),
  debug: (message) => console.log(`[DEBUG] ${message}`)
};

// Sample data
const governanceData = {
  complianceScore: 0.85,
  auditFrequency: 4,
  timestamp: new Date().toISOString(),
  source: 'official',
  confidence: 0.9,
  policies: [
    { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
    { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
    { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
    { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
    { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 }
  ]
};

const detectionData = {
  detectionCapability: 0.75,
  threatSeverity: 0.8,
  threatConfidence: 0.7,
  baselineSignals: 0.65,
  timestamp: new Date().toISOString(),
  source: 'sensor',
  confidence: 0.85,
  threats: {
    malware: 0.9,
    phishing: 0.8,
    ddos: 0.7,
    insider: 0.6
  }
};

const responseData = {
  baseResponseTime: 50,
  threatSurface: 175,
  systemRadius: 150,
  reactionTime: 0.8,
  mitigationSurface: 0.7,
  timestamp: new Date().toISOString(),
  source: 'verified',
  confidence: 0.8,
  threats: {
    malware: 0.9,
    phishing: 0.8,
    ddos: 0.7,
    insider: 0.6
  }
};

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../adaptive_trinity_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Run a single optimization cycle
 * @param {AdaptiveTrinityCSDE} csdeEngine - Adaptive Trinity CSDE Engine
 * @param {Object} governanceData - Governance data
 * @param {Object} detectionData - Detection data
 * @param {Object} responseData - Response data
 * @returns {Object} - Trinity CSDE result
 */
function runOptimizationCycle(csdeEngine, governanceData, detectionData, responseData) {
  // Calculate Trinity CSDE with adaptive ratios
  return csdeEngine.calculateTrinityCSDE(
    governanceData,
    detectionData,
    responseData,
    true  // optimize ratios
  );
}

/**
 * Demo Adaptive Trinity CSDE with multiple optimization cycles
 * @param {number} cycles - Number of optimization cycles
 */
function demoAdaptiveTrinityCSDE(cycles = 10) {
  logger.info(`Demo: Adaptive Trinity CSDE with ${cycles} optimization cycles`);
  
  try {
    // Initialize Adaptive Trinity CSDE Engine
    const csdeEngine = new AdaptiveTrinityCSDE({
      enableMetrics: true,
      enableCaching: false,  // Disable caching for optimization
      learningRate: 0.05,    // Higher learning rate for faster convergence
      optimizationTarget: 'balanced'
    });
    
    // Store results for each cycle
    const results = [];
    
    // Run initial calculation with default 18/82 ratio
    logger.info('Running initial calculation with default 18/82 ratio');
    const initialResult = csdeEngine.calculateTrinityCSDE(
      governanceData,
      detectionData,
      responseData,
      false  // don't optimize ratios yet
    );
    
    results.push({
      cycle: 0,
      csdeTrinity: initialResult.csdeTrinity,
      adaptiveRatios: { ...initialResult.adaptiveRatios },
      performance: 0
    });
    
    logger.info(`Initial Trinity CSDE Value: ${initialResult.csdeTrinity.toFixed(4)}`);
    logger.info(`Initial Ratios - Father: ${initialResult.adaptiveRatios.father.toFixed(4)}, Son: ${initialResult.adaptiveRatios.son.toFixed(4)}, Spirit: ${initialResult.adaptiveRatios.spirit.toFixed(4)}`);
    
    // Run optimization cycles
    for (let i = 1; i <= cycles; i++) {
      logger.info(`Running optimization cycle ${i}/${cycles}`);
      
      // Run optimization cycle
      const result = runOptimizationCycle(
        csdeEngine,
        governanceData,
        detectionData,
        responseData
      );
      
      // Store results
      results.push({
        cycle: i,
        csdeTrinity: result.csdeTrinity,
        adaptiveRatios: { ...result.adaptiveRatios },
        performance: result.performanceMetrics.currentPerformance
      });
      
      logger.info(`Cycle ${i} - Trinity CSDE Value: ${result.csdeTrinity.toFixed(4)}`);
      logger.info(`Cycle ${i} - Ratios - Father: ${result.adaptiveRatios.father.toFixed(4)}, Son: ${result.adaptiveRatios.son.toFixed(4)}, Spirit: ${result.adaptiveRatios.spirit.toFixed(4)}`);
      logger.info(`Cycle ${i} - Performance: ${result.performanceMetrics.currentPerformance.toFixed(4)}`);
    }
    
    // Get final result
    const finalResult = results[results.length - 1];
    
    // Get best result
    const bestPerformance = csdeEngine.getPerformanceMetrics().bestPerformance;
    const bestRatios = csdeEngine.getPerformanceMetrics().bestRatios;
    
    logger.info('\nOptimization Results:');
    logger.info(`Initial Trinity CSDE Value: ${results[0].csdeTrinity.toFixed(4)}`);
    logger.info(`Initial Ratios - Father: ${results[0].adaptiveRatios.father.toFixed(4)}, Son: ${results[0].adaptiveRatios.son.toFixed(4)}, Spirit: ${results[0].adaptiveRatios.spirit.toFixed(4)}`);
    logger.info(`Final Trinity CSDE Value: ${finalResult.csdeTrinity.toFixed(4)}`);
    logger.info(`Final Ratios - Father: ${finalResult.adaptiveRatios.father.toFixed(4)}, Son: ${finalResult.adaptiveRatios.son.toFixed(4)}, Spirit: ${finalResult.adaptiveRatios.spirit.toFixed(4)}`);
    logger.info(`Best Performance: ${bestPerformance.toFixed(4)}`);
    logger.info(`Best Ratios - Father: ${bestRatios.father.alpha.toFixed(4)}, Son: ${bestRatios.son.beta.toFixed(4)}, Spirit: ${bestRatios.spirit.gamma.toFixed(4)}`);
    
    // Calculate improvement
    const improvement = (finalResult.csdeTrinity - results[0].csdeTrinity) / results[0].csdeTrinity * 100;
    logger.info(`Improvement: ${improvement.toFixed(2)}%`);
    
    // Save results to file
    const resultFile = path.join(RESULTS_DIR, `adaptive_trinity_csde_result_${new Date().toISOString().replace(/:/g, '-')}.json`);
    fs.writeFileSync(resultFile, JSON.stringify({
      results,
      bestPerformance,
      bestRatios,
      improvement
    }, null, 2));
    logger.info(`Results saved to ${resultFile}`);
    
    // Generate and save visualization
    const visualization = generateVisualization(results);
    const visualizationFile = path.join(RESULTS_DIR, `adaptive_trinity_csde_visualization_${new Date().toISOString().replace(/:/g, '-')}.html`);
    fs.writeFileSync(visualizationFile, visualization);
    logger.info(`Visualization saved to ${visualizationFile}`);
    
    return {
      results,
      bestPerformance,
      bestRatios,
      improvement
    };
  } catch (error) {
    logger.error(`Error in Adaptive Trinity CSDE demo: ${error.message}`);
    throw error;
  }
}

/**
 * Generate visualization of optimization results
 * @param {Array} results - Optimization results
 * @returns {string} - HTML visualization
 */
function generateVisualization(results) {
  // Extract data for visualization
  const cycles = results.map(r => r.cycle);
  const csdeValues = results.map(r => r.csdeTrinity);
  const fatherRatios = results.map(r => r.adaptiveRatios.father);
  const sonRatios = results.map(r => r.adaptiveRatios.son);
  const spiritRatios = results.map(r => r.adaptiveRatios.spirit);
  const performances = results.map(r => r.performance);
  
  // Create HTML visualization
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Adaptive Trinity CSDE Optimization</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background-color: #f5f5f5;
        }
        .container {
          max-width: 1200px;
          margin: 0 auto;
          background-color: white;
          padding: 20px;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
          color: #333;
        }
        .chart {
          margin: 20px 0;
          height: 400px;
        }
        .results {
          margin-top: 30px;
          padding: 20px;
          background-color: #f0f0f0;
          border-radius: 8px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 20px;
        }
        th, td {
          padding: 10px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
        }
        .improvement {
          font-size: 24px;
          font-weight: bold;
          color: #4CAF50;
          margin: 20px 0;
        }
      </style>
      <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    </head>
    <body>
      <div class="container">
        <h1>Adaptive Trinity CSDE Optimization Results</h1>
        
        <div class="chart">
          <canvas id="csdeChart"></canvas>
        </div>
        
        <div class="chart">
          <canvas id="ratioChart"></canvas>
        </div>
        
        <div class="chart">
          <canvas id="performanceChart"></canvas>
        </div>
        
        <div class="results">
          <h2>Optimization Summary</h2>
          <div class="improvement">
            Improvement: ${((results[results.length - 1].csdeTrinity - results[0].csdeTrinity) / results[0].csdeTrinity * 100).toFixed(2)}%
          </div>
          
          <h3>Initial vs Final Values</h3>
          <table>
            <tr>
              <th></th>
              <th>Initial (18/82)</th>
              <th>Final (Optimized)</th>
              <th>Change</th>
            </tr>
            <tr>
              <td>Trinity CSDE Value</td>
              <td>${results[0].csdeTrinity.toFixed(4)}</td>
              <td>${results[results.length - 1].csdeTrinity.toFixed(4)}</td>
              <td>${(results[results.length - 1].csdeTrinity - results[0].csdeTrinity).toFixed(4)}</td>
            </tr>
            <tr>
              <td>Father Ratio (α)</td>
              <td>${results[0].adaptiveRatios.father.toFixed(4)}</td>
              <td>${results[results.length - 1].adaptiveRatios.father.toFixed(4)}</td>
              <td>${(results[results.length - 1].adaptiveRatios.father - results[0].adaptiveRatios.father).toFixed(4)}</td>
            </tr>
            <tr>
              <td>Son Ratio (β)</td>
              <td>${results[0].adaptiveRatios.son.toFixed(4)}</td>
              <td>${results[results.length - 1].adaptiveRatios.son.toFixed(4)}</td>
              <td>${(results[results.length - 1].adaptiveRatios.son - results[0].adaptiveRatios.son).toFixed(4)}</td>
            </tr>
            <tr>
              <td>Spirit Ratio (γ)</td>
              <td>${results[0].adaptiveRatios.spirit.toFixed(4)}</td>
              <td>${results[results.length - 1].adaptiveRatios.spirit.toFixed(4)}</td>
              <td>${(results[results.length - 1].adaptiveRatios.spirit - results[0].adaptiveRatios.spirit).toFixed(4)}</td>
            </tr>
          </table>
        </div>
      </div>
      
      <script>
        // CSDE Value Chart
        const csdeCtx = document.getElementById('csdeChart').getContext('2d');
        new Chart(csdeCtx, {
          type: 'line',
          data: {
            labels: ${JSON.stringify(cycles)},
            datasets: [{
              label: 'Trinity CSDE Value',
              data: ${JSON.stringify(csdeValues)},
              borderColor: 'rgb(75, 192, 192)',
              tension: 0.1,
              fill: false
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Trinity CSDE Value Over Optimization Cycles'
              }
            },
            scales: {
              y: {
                beginAtZero: false
              }
            }
          }
        });
        
        // Ratio Chart
        const ratioCtx = document.getElementById('ratioChart').getContext('2d');
        new Chart(ratioCtx, {
          type: 'line',
          data: {
            labels: ${JSON.stringify(cycles)},
            datasets: [
              {
                label: 'Father Ratio (α)',
                data: ${JSON.stringify(fatherRatios)},
                borderColor: 'rgb(255, 99, 132)',
                tension: 0.1,
                fill: false
              },
              {
                label: 'Son Ratio (β)',
                data: ${JSON.stringify(sonRatios)},
                borderColor: 'rgb(54, 162, 235)',
                tension: 0.1,
                fill: false
              },
              {
                label: 'Spirit Ratio (γ)',
                data: ${JSON.stringify(spiritRatios)},
                borderColor: 'rgb(255, 206, 86)',
                tension: 0.1,
                fill: false
              }
            ]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Adaptive Ratios Over Optimization Cycles'
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                max: 1
              }
            }
          }
        });
        
        // Performance Chart
        const perfCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(perfCtx, {
          type: 'line',
          data: {
            labels: ${JSON.stringify(cycles)},
            datasets: [{
              label: 'Performance Score',
              data: ${JSON.stringify(performances)},
              borderColor: 'rgb(153, 102, 255)',
              tension: 0.1,
              fill: false
            }]
          },
          options: {
            responsive: true,
            plugins: {
              title: {
                display: true,
                text: 'Performance Score Over Optimization Cycles'
              }
            },
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      </script>
    </body>
    </html>
  `;
}

/**
 * Main function
 */
function main() {
  logger.info('Adaptive Trinity CSDE Demo');
  
  try {
    // Demo Adaptive Trinity CSDE with 10 optimization cycles
    demoAdaptiveTrinityCSDE(10);
    
    logger.info('Demo completed successfully');
  } catch (error) {
    logger.error(`Error running demo: ${error.message}`);
  }
}

// Run the demo
main();
