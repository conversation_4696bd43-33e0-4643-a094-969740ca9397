"""
Test Result Templates for the Universal Compliance Visualization Framework.

This module provides templates for visualizing test results from UCTF.
"""

import logging
from typing import Dict, List, Any, Optional

from ..core.template_manager import VisualizationTemplate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestResultDashboardTemplate(VisualizationTemplate):
    """Template for visualizing test results in a dashboard."""

    def __init__(self):
        """Initialize the template."""
        super().__init__(
            name="Test Result Dashboard",
            description="Dashboard for visualizing compliance test results"
        )

    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided test result data.

        Args:
            data: The test result data
            context: Additional context for the visualization

        Returns:
            The visualization data
        """
        logger.info(f"Applying Test Result Dashboard template to data: {data.get('result_id', 'unknown')}")

        # Default context
        if context is None:
            context = {}

        # Extract test result data
        result_id = data.get('result_id', '')
        test_id = data.get('test_id', '')
        name = data.get('name', '')
        framework = data.get('framework', '')
        status = data.get('status', '')
        score = data.get('score', 0)
        findings = data.get('findings', [])

        # Count findings by severity
        critical_findings = [f for f in findings if f.get('severity', '').lower() == 'critical']
        high_findings = [f for f in findings if f.get('severity', '').lower() == 'high']
        medium_findings = [f for f in findings if f.get('severity', '').lower() == 'medium']
        low_findings = [f for f in findings if f.get('severity', '').lower() == 'low']

        # Create visualization data
        visualization = {
            'title': f"Test Result: {name}",
            'subtitle': f"Framework: {framework}",
            'timestamp': data.get('timestamp', ''),
            'summary': {
                'result_id': result_id,
                'test_id': test_id,
                'name': name,
                'framework': framework,
                'status': status,
                'score': score,
                'total_findings': len(findings)
            },
            'charts': [
                {
                    'type': 'gauge',
                    'title': 'Compliance Score',
                    'data': {
                        'value': score,
                        'min': 0,
                        'max': 100,
                        'thresholds': [
                            {'value': 60, 'color': 'red'},
                            {'value': 80, 'color': 'yellow'},
                            {'value': 100, 'color': 'green'}
                        ]
                    }
                },
                {
                    'type': 'pie',
                    'title': 'Findings by Severity',
                    'data': {
                        'labels': ['Critical', 'High', 'Medium', 'Low'],
                        'values': [
                            len(critical_findings),
                            len(high_findings),
                            len(medium_findings),
                            len(low_findings)
                        ],
                        'colors': ['#ff0000', '#ff9900', '#ffcc00', '#00cc00']
                    }
                }
            ],
            'tables': [
                {
                    'title': 'Critical Findings',
                    'headers': ['ID', 'Description', 'Status'],
                    'rows': [
                        [f.get('id', ''), f.get('description', ''), f.get('status', '')]
                        for f in critical_findings
                    ]
                },
                {
                    'title': 'High Severity Findings',
                    'headers': ['ID', 'Description', 'Status'],
                    'rows': [
                        [f.get('id', ''), f.get('description', ''), f.get('status', '')]
                        for f in high_findings
                    ]
                }
            ]
        }

        # Add medium and low findings tables if requested in context
        if context.get('show_all_findings', False):
            visualization['tables'].extend([
                {
                    'title': 'Medium Severity Findings',
                    'headers': ['ID', 'Description', 'Status'],
                    'rows': [
                        [f.get('id', ''), f.get('description', ''), f.get('status', '')]
                        for f in medium_findings
                    ]
                },
                {
                    'title': 'Low Severity Findings',
                    'headers': ['ID', 'Description', 'Status'],
                    'rows': [
                        [f.get('id', ''), f.get('description', ''), f.get('status', '')]
                        for f in low_findings
                    ]
                }
            ])

        return visualization


class TestResultReportTemplate(VisualizationTemplate):
    """Template for generating detailed reports from test results."""

    def __init__(self):
        """Initialize the template."""
        super().__init__(
            name="Test Result Report",
            description="Detailed report for compliance test results"
        )

    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided test result data.

        Args:
            data: The test result data
            context: Additional context for the visualization

        Returns:
            The report data
        """
        logger.info(f"Applying Test Result Report template to data: {data.get('result_id', 'unknown')}")

        # Default context
        if context is None:
            context = {}

        # Extract test result data
        result_id = data.get('result_id', '')
        test_id = data.get('test_id', '')
        name = data.get('name', '')
        framework = data.get('framework', '')
        status = data.get('status', '')
        score = data.get('score', 0)
        findings = data.get('findings', [])
        metadata = data.get('metadata', {})

        # Create report data
        report = {
            'title': f"Compliance Test Report: {name}",
            'subtitle': f"Framework: {framework}",
            'timestamp': data.get('timestamp', ''),
            'sections': [
                {
                    'title': 'Executive Summary',
                    'content': f"""
                        This report presents the results of the compliance test "{name}" for the {framework} framework.
                        The test resulted in a compliance score of {score}/100 and a status of {status}.
                        A total of {len(findings)} findings were identified.
                    """
                },
                {
                    'title': 'Test Details',
                    'content': '',
                    'table': {
                        'headers': ['Property', 'Value'],
                        'rows': [
                            ['Test ID', test_id],
                            ['Test Name', name],
                            ['Framework', framework],
                            ['Status', status],
                            ['Score', f"{score}/100"],
                            ['Result ID', result_id],
                            ['Timestamp', data.get('timestamp', '')]
                        ]
                    }
                },
                {
                    'title': 'Findings Summary',
                    'content': f"A total of {len(findings)} findings were identified during the test.",
                    'chart': {
                        'type': 'pie',
                        'title': 'Findings by Severity',
                        'data': {
                            'labels': ['Critical', 'High', 'Medium', 'Low'],
                            'values': [
                                len([f for f in findings if f.get('severity', '').lower() == 'critical']),
                                len([f for f in findings if f.get('severity', '').lower() == 'high']),
                                len([f for f in findings if f.get('severity', '').lower() == 'medium']),
                                len([f for f in findings if f.get('severity', '').lower() == 'low'])
                            ],
                            'colors': ['#ff0000', '#ff9900', '#ffcc00', '#00cc00']
                        }
                    }
                }
            ]
        }

        # Add detailed findings section
        findings_section = {
            'title': 'Detailed Findings',
            'content': 'The following findings were identified during the test:',
            'subsections': []
        }

        # Group findings by severity
        severity_groups = {
            'critical': [f for f in findings if f.get('severity', '').lower() == 'critical'],
            'high': [f for f in findings if f.get('severity', '').lower() == 'high'],
            'medium': [f for f in findings if f.get('severity', '').lower() == 'medium'],
            'low': [f for f in findings if f.get('severity', '').lower() == 'low']
        }

        # Add subsections for each severity level
        for severity, severity_findings in severity_groups.items():
            if severity_findings:
                subsection = {
                    'title': f"{severity.capitalize()} Severity Findings",
                    'content': f"{len(severity_findings)} {severity} severity findings were identified.",
                    'table': {
                        'headers': ['ID', 'Description', 'Status', 'Details'],
                        'rows': [
                            [
                                f.get('id', ''),
                                f.get('description', ''),
                                f.get('status', ''),
                                f.get('details', '')
                            ]
                            for f in severity_findings
                        ]
                    }
                }
                findings_section['subsections'].append(subsection)

        report['sections'].append(findings_section)

        # Add metadata section if available
        if metadata:
            metadata_section = {
                'title': 'Test Metadata',
                'content': 'Additional metadata associated with this test:',
                'table': {
                    'headers': ['Property', 'Value'],
                    'rows': [[k, str(v)] for k, v in metadata.items()]
                }
            }
            report['sections'].append(metadata_section)

        # Add recommendations section if provided in context
        if 'recommendations' in context:
            recommendations_section = {
                'title': 'Recommendations',
                'content': 'Based on the test results, the following recommendations are provided:',
                'list': context['recommendations']
            }
            report['sections'].append(recommendations_section)

        return report