/**
 * Compliance Automation API - Models
 * 
 * This file defines the models for the Compliance Automation API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Automation Rule Schema
 * 
 * Represents a rule for automating compliance tasks.
 */
const AutomationRuleSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['Notification', 'Remediation', 'Documentation', 'Testing', 'Approval', 'Custom']
  },
  trigger: {
    event: {
      type: String,
      required: true,
      enum: ['Schedule', 'DataChange', 'StatusChange', 'ManualTrigger', 'ExternalEvent']
    },
    schedule: {
      frequency: {
        type: String,
        enum: ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually', 'Custom']
      },
      cronExpression: {
        type: String
      },
      startDate: {
        type: Date
      },
      endDate: {
        type: Date
      }
    },
    conditions: [{
      field: {
        type: String
      },
      operator: {
        type: String,
        enum: ['Equals', 'NotEquals', 'Contains', 'NotContains', 'GreaterThan', 'LessThan', 'IsNull', 'IsNotNull']
      },
      value: {
        type: Schema.Types.Mixed
      }
    }]
  },
  actions: [{
    type: {
      type: String,
      required: true,
      enum: ['SendEmail', 'CreateTask', 'UpdateRecord', 'GenerateDocument', 'ExecuteWorkflow', 'APICall', 'Custom']
    },
    config: {
      type: Map,
      of: Schema.Types.Mixed,
      required: true
    }
  }],
  scope: {
    frameworks: [{
      type: Schema.Types.ObjectId,
      ref: 'Framework'
    }],
    controls: [{
      type: Schema.Types.ObjectId,
      ref: 'Control'
    }],
    entities: [{
      type: String
    }]
  },
  priority: {
    type: String,
    required: true,
    enum: ['Low', 'Medium', 'High', 'Critical'],
    default: 'Medium'
  },
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Active', 'Inactive', 'Archived'],
    default: 'Draft'
  },
  owner: {
    type: String,
    required: true
  },
  approvedBy: {
    type: String
  },
  approvalDate: {
    type: Date
  },
  tags: [{
    type: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Automation Execution Schema
 * 
 * Represents an execution of an automation rule.
 */
const AutomationExecutionSchema = new Schema({
  rule: {
    type: Schema.Types.ObjectId,
    ref: 'AutomationRule',
    required: true
  },
  trigger: {
    type: String,
    required: true,
    enum: ['Schedule', 'DataChange', 'StatusChange', 'ManualTrigger', 'ExternalEvent']
  },
  triggerDetails: {
    type: Map,
    of: Schema.Types.Mixed
  },
  startTime: {
    type: Date,
    required: true,
    default: Date.now
  },
  endTime: {
    type: Date
  },
  status: {
    type: String,
    required: true,
    enum: ['Pending', 'Running', 'Completed', 'Failed', 'Cancelled'],
    default: 'Pending'
  },
  actionResults: [{
    actionType: {
      type: String,
      required: true
    },
    status: {
      type: String,
      required: true,
      enum: ['Success', 'Failure', 'Skipped']
    },
    details: {
      type: Map,
      of: Schema.Types.Mixed
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  error: {
    message: {
      type: String
    },
    details: {
      type: String
    },
    timestamp: {
      type: Date
    }
  },
  executedBy: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Workflow Schema
 * 
 * Represents a workflow for compliance processes.
 */
const WorkflowSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: ['Approval', 'Review', 'Remediation', 'Testing', 'Documentation', 'Custom']
  },
  steps: [{
    name: {
      type: String,
      required: true
    },
    description: {
      type: String
    },
    type: {
      type: String,
      required: true,
      enum: ['Task', 'Approval', 'Notification', 'Condition', 'Integration', 'Custom']
    },
    config: {
      type: Map,
      of: Schema.Types.Mixed,
      required: true
    },
    assignee: {
      type: String
    },
    dueDate: {
      type: Date
    },
    dependencies: [{
      step: {
        type: Number
      },
      condition: {
        type: String,
        enum: ['Completed', 'Approved', 'Rejected', 'Any']
      }
    }]
  }],
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Active', 'Inactive', 'Archived'],
    default: 'Draft'
  },
  owner: {
    type: String,
    required: true
  },
  tags: [{
    type: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Workflow Instance Schema
 * 
 * Represents an instance of a workflow.
 */
const WorkflowInstanceSchema = new Schema({
  workflow: {
    type: Schema.Types.ObjectId,
    ref: 'Workflow',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  context: {
    type: Map,
    of: Schema.Types.Mixed
  },
  status: {
    type: String,
    required: true,
    enum: ['Pending', 'In Progress', 'Completed', 'Failed', 'Cancelled'],
    default: 'Pending'
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  steps: [{
    stepIndex: {
      type: Number,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    status: {
      type: String,
      required: true,
      enum: ['Pending', 'In Progress', 'Completed', 'Failed', 'Skipped'],
      default: 'Pending'
    },
    assignee: {
      type: String
    },
    startDate: {
      type: Date
    },
    endDate: {
      type: Date
    },
    result: {
      type: Map,
      of: Schema.Types.Mixed
    },
    comments: {
      type: String
    }
  }],
  initiatedBy: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create and export models
const AutomationRule = mongoose.model('AutomationRule', AutomationRuleSchema);
const AutomationExecution = mongoose.model('AutomationExecution', AutomationExecutionSchema);
const Workflow = mongoose.model('Workflow', WorkflowSchema);
const WorkflowInstance = mongoose.model('WorkflowInstance', WorkflowInstanceSchema);

module.exports = {
  AutomationRule,
  AutomationExecution,
  Workflow,
  WorkflowInstance
};
