{"metadata": {"name": "OAuth2 Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for OAuth2 authentication", "author": "NovaGRC", "tags": ["test", "oauth2"]}, "authentication": {"type": "OAUTH2", "fields": {"clientId": {"type": "string", "description": "Client ID", "required": true}, "clientSecret": {"type": "string", "description": "Client Secret", "required": true, "sensitive": true}}, "oauth2Config": {"tokenUrl": "http://localhost:3005/oauth2/token", "grantType": "client_credentials", "scope": ""}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getResource", "name": "Get Resource", "path": "/oauth2/resource", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getResource", "targetSystem": "NovaGRC", "targetEntity": "Resource", "transformations": [{"source": "$.data.id", "target": "resourceId", "transform": "identity"}, {"source": "$.data.name", "target": "resourceName", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}