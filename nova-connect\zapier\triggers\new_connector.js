/**
 * New Connector Trigger
 * 
 * This trigger fires when a new connector is created.
 */

// Define the trigger
module.exports = {
  key: 'new_connector',
  noun: 'Connector',
  
  // Display information
  display: {
    label: 'New Connector',
    description: 'Triggers when a new connector is created.',
    important: true
  },
  
  // Operation
  operation: {
    // Polling operation
    type: 'polling',
    
    // Perform the operation
    perform: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/triggers/new-connector',
      headers: {
        Authorization: 'Bearer {{bundle.authData.access_token}}'
      }
    },
    
    // Sample data
    sample: {
      id: 'conn-123',
      name: 'Sample Connector',
      type: 'api',
      createdAt: '2023-01-01T00:00:00Z'
    },
    
    // Output fields
    outputFields: [
      { key: 'id', label: 'ID' },
      { key: 'name', label: 'Name' },
      { key: 'type', label: 'Type' },
      { key: 'createdAt', label: 'Created At' }
    ]
  }
};
