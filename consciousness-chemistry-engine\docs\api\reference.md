# API Reference

## Table of Contents
- [ConsciousNovaFold](#consciousnovafold)
- [NovaFoldClient](#novafoldclient)
- [Metrics](#metrics)
  - [PSIScorer](#psiscorer)
  - [FibonacciBiasAnalyzer](#fibonaccibiasanalyzer)
  - [TrinityValidator](#trinityvalidator)

## ConsciousNovaFold

### `class ConsciousNovaFold(novafold_client, config=None)`
Main class for consciousness-enhanced protein folding.

#### Parameters
- `novafold_client`: Instance of NovaFoldClient
- `config`: Optional configuration dictionary

#### Methods

##### `fold(sequence, sequence_id=None, **kwargs)`
Fold a protein sequence with consciousness enhancement.

**Parameters:**
- `sequence` (str): Protein sequence
- `sequence_id` (str, optional): Unique identifier for the sequence
- `**kwargs`: Additional arguments to pass to the folding pipeline

**Returns:**
`dict`: Dictionary containing structure and metrics

**Example:**
```python
folder = ConsciousNovaFold(NovaFoldClient())
result = folder.fold("ACDEFGHIKLMNPQRSTVWY")
```

##### `generate_reports(result, output_dir=None)`
Generate reports for a folding result.

**Parameters:**
- `result` (dict): Result from `fold()`
- `output_dir` (str, optional): Output directory for reports

**Returns:**
`dict`: Paths to generated report files

## NovaFoldClient

### `class NovaFoldClient(api_key=None, config=None)`
Client for interacting with the NovaFold API.

#### Parameters
- `api_key` (str, optional): API key for authentication
- `config` (dict, optional): Configuration options

#### Methods

##### `predict(sequence, **kwargs)`
Predict protein structure using NovaFold.

**Parameters:**
- `sequence` (str): Protein sequence
- `**kwargs`: Additional prediction parameters

**Returns:**
`dict`: Predicted structure and metadata

## Metrics

### PSIScorer

### `class PSIScorer()`
Calculate PSI (Protein Structure Integrity) scores.

#### Methods

##### `calculate(structure, sequence=None)`
Calculate PSI scores for a protein structure.

**Parameters:**
- `structure`: Protein structure object
- `sequence` (str, optional): Protein sequence

**Returns:**
`dict`: PSI scores and metrics

### FibonacciBiasAnalyzer

### `class FibonacciBiasAnalyzer()`
Analyze Fibonacci sequence patterns in protein structures.

#### Methods

##### `analyze(structure, sequence=None)`
Analyze Fibonacci patterns in a protein structure.

**Parameters:**
- `structure`: Protein structure object
- `sequence` (str, optional): Protein sequence

**Returns:**
`dict`: Fibonacci analysis results

### TrinityValidator

### `class TrinityValidator()`
Validate protein structures using the Trinity metrics.

#### Methods

##### `validate(structure, sequence=None)`
Validate a protein structure using NERS, NEPI, and NEFC metrics.

**Parameters:**
- `structure`: Protein structure object
- `sequence` (str, optional): Protein sequence

**Returns:**
`dict`: Validation results and scores

## Configuration

### Configuration Options

```yaml
# config.yaml
debug: false
output:
  directory: "./output"
  save_reports: true
  format: "both"  # json, markdown, or both

novafold:
  model: "default"
  num_models: 5
  enable_relax: true

consciousness:
  enable_quantum_effects: true
  fibonacci_threshold: 0.7
  
metrics:
  psi:
    weights: [0.4, 0.3, 0.3]  # Weights for different components
  fibonacci:
    min_domain_length: 5
    max_domain_length: 100
  trinity:
    ners_threshold: 0.7
    nepi_threshold: 0.5
    nefc_threshold: 0.6
```

## Examples

### Basic Usage

```python
from ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient

# Initialize client and folder
client = NovaFoldClient()
folder = ConsciousNovaFold(client)

# Fold a sequence
result = folder.fold("ACDEFGHIKLMNPQRSTVWY")


# Generate reports
reports = folder.generate_reports(result, "reports")
print(f"Generated reports: {reports}")
```

### Custom Configuration

```python
from ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient

config = {
    "output": {
        "directory": "custom_output",
        "format": "json"
    },
    "consciousness": {
        "enable_quantum_effects": False
    }
}

client = NovaFoldClient()
folder = ConsciousNovaFold(client, config=config)
result = folder.fold("ACDEFGHIKLMNPQRSTVWY")
```

## Error Handling

### Custom Exceptions

#### `class NovaFoldError(Exception)`
Base exception for NovaFold client errors.

#### `class AuthenticationError(NovaFoldError)`
Raised when authentication fails.

#### `class PredictionError(NovaFoldError)`
Raised when a prediction fails.

### Handling Exceptions

```python
from ConsciousNovaFold import NovaFoldClient, AuthenticationError, PredictionError

try:
    client = NovaFoldClient(api_key="your-api-key")
    result = client.predict("INVALIDSEQUENCE")
except AuthenticationError as e:
    print(f"Authentication failed: {e}")
except PredictionError as e:
    print(f"Prediction failed: {e}")
```

## Changelog

### 1.0.0 (2025-06-25)
- Initial release of ConsciousNovaFold
- Integration with NovaFold API
- Consciousness metrics (PSI, Fibonacci, Trinity)
- Report generation in JSON and Markdown formats
