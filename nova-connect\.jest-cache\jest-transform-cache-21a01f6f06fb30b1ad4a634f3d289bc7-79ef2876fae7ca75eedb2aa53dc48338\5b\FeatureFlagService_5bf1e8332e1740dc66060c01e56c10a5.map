{"version": 3, "names": ["fs", "require", "promises", "path", "ValidationError", "NotFoundError", "AuthorizationError", "PackageConfigRegistry", "NodeCache", "FeatureFlagService", "constructor", "dataDir", "join", "__dirname", "featureFlagsDir", "featureFlagsFile", "subscriptionTiersFile", "userEntitlementsFile", "featureUsageFile", "cache", "stdTTL", "checkperiod", "packageRegistry", "ensureDataDir", "tiers", "FREE", "STANDARD", "PROFESSIONAL", "ENTERPRISE", "packageTiers", "CORE", "SECURE", "AI_BOOST", "mkdir", "recursive", "initializeFile", "getDefaultFeatureFlags", "getDefaultSubscriptionTiers", "error", "console", "filePath", "defaultData", "access", "code", "writeFile", "JSON", "stringify", "loadData", "data", "readFile", "parse", "saveData", "id", "name", "description", "category", "enabled", "limits", "connections", "operations_per_day", "workflows", "actions_per_workflow", "scheduled_workflows", "alerts", "generations_per_day", "queries_per_day", "suggestions_per_day", "optimizations_per_day", "active_workflows", "custom_templates", "tracking_depth", "restrictions_per_resource", "encryption_keys", "policies", "detection_frequency", "reports_per_month", "scheduled_reports", "formats", "price", "features", "getAllFeatureFlags", "getFeatureFlagById", "featureFlags", "featureFlag", "find", "f", "updateFeatureFlag", "index", "findIndex", "getAllSubscriptionTiers", "getSubscriptionTierById", "subscriptionTiers", "subscriptionTier", "t", "getUserEntitlement", "userId", "userEntitlements", "userEntitlement", "e", "tierId", "customFeatures", "customLimits", "created", "Date", "toISOString", "updated", "push", "updateUserEntitlement", "length", "hasFeatureAccess", "featureId", "tenantId", "cache<PERSON>ey", "cachedAccess", "get", "undefined", "hasTenantAccess", "hasTenantFeatureAccess", "set", "includes", "hasAccess", "getFeatureLimit", "limit<PERSON>ey", "cachedLimit", "tenantLimit", "getTenantFeatureLimit", "limit", "trackFeatureUsage", "quantity", "now", "date", "split", "featureUsage", "usageRecord", "u", "getFeatureUsageForUser", "startDate", "endDate", "filteredUsage", "filter", "hasReachedFeatureLimit", "today", "usage", "totalUsage", "reduce", "sum", "record", "getUserAvailableFeatures", "cachedFeatures", "tenantFeatures", "getTenantAvailableFeatures", "availableFeatures", "feature", "getTenantPackage", "cachedPackage", "mapping", "getTenantMapping", "packageDetails", "getPackageById", "packageId", "setTenantPackage", "setTenantMapping", "del", "getAllPackages", "createPackage", "packageData", "updatePackage", "deletePackage", "clearCache", "flushAll", "getUserSubscriptionDetails", "tier", "map", "result", "module", "exports"], "sources": ["FeatureFlagService.js"], "sourcesContent": ["/**\n * Feature Flag Service\n *\n * This service manages feature flags and subscription tier access.\n * It has been enhanced to support package-based feature controls and tenant-specific features.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');\nconst PackageConfigRegistry = require('./PackageConfigRegistry');\nconst NodeCache = require('node-cache');\n\nclass FeatureFlagService {\n  constructor(dataDir = path.join(__dirname, '../data')) {\n    this.dataDir = dataDir;\n    this.featureFlagsDir = path.join(this.dataDir, 'feature_flags');\n    this.featureFlagsFile = path.join(this.featureFlagsDir, 'feature_flags.json');\n    this.subscriptionTiersFile = path.join(this.featureFlagsDir, 'subscription_tiers.json');\n    this.userEntitlementsFile = path.join(this.featureFlagsDir, 'user_entitlements.json');\n    this.featureUsageFile = path.join(this.featureFlagsDir, 'feature_usage.json');\n\n    // Initialize cache with 5-minute TTL\n    this.cache = new NodeCache({ stdTTL: 300, checkperiod: 60 });\n\n    // Initialize package config registry\n    this.packageRegistry = new PackageConfigRegistry(dataDir);\n\n    this.ensureDataDir();\n\n    // Define subscription tiers\n    this.tiers = {\n      FREE: 'free',\n      STANDARD: 'standard',\n      PROFESSIONAL: 'professional',\n      ENTERPRISE: 'enterprise'\n    };\n\n    // Define package tiers (aligned with GCP Marketplace)\n    this.packageTiers = {\n      CORE: 'core',\n      SECURE: 'secure',\n      ENTERPRISE: 'enterprise',\n      AI_BOOST: 'ai_boost'\n    };\n  }\n\n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.featureFlagsDir, { recursive: true });\n\n      // Initialize files if they don't exist\n      await this.initializeFile(this.featureFlagsFile, this.getDefaultFeatureFlags());\n      await this.initializeFile(this.subscriptionTiersFile, this.getDefaultSubscriptionTiers());\n      await this.initializeFile(this.userEntitlementsFile, []);\n      await this.initializeFile(this.featureUsageFile, []);\n    } catch (error) {\n      console.error('Error creating feature flags directory:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Initialize a file with default data if it doesn't exist\n   */\n  async initializeFile(filePath, defaultData) {\n    try {\n      await fs.access(filePath);\n    } catch (error) {\n      if (error.code === 'ENOENT') {\n        // File doesn't exist, create it with default data\n        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  /**\n   * Load data from file\n   */\n  async loadData(filePath) {\n    try {\n      const data = await fs.readFile(filePath, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      if (error.code === 'ENOENT') {\n        // File doesn't exist, return empty array or default data\n        if (filePath === this.featureFlagsFile) {\n          return this.getDefaultFeatureFlags();\n        } else if (filePath === this.subscriptionTiersFile) {\n          return this.getDefaultSubscriptionTiers();\n        }\n        return [];\n      }\n      console.error(`Error loading data from ${filePath}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Save data to file\n   */\n  async saveData(filePath, data) {\n    try {\n      await fs.writeFile(filePath, JSON.stringify(data, null, 2));\n    } catch (error) {\n      console.error(`Error saving data to ${filePath}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get default feature flags\n   */\n  getDefaultFeatureFlags() {\n    return [\n      // Core Features - Available in all tiers\n      {\n        id: 'core.basic_connectors',\n        name: 'Basic Connectors',\n        description: 'Connect to basic API endpoints',\n        category: 'core',\n        enabled: true,\n        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.FREE]: { connections: 3 },\n          [this.tiers.STANDARD]: { connections: 10 },\n          [this.tiers.PROFESSIONAL]: { connections: 50 },\n          [this.tiers.ENTERPRISE]: { connections: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'core.manual_execution',\n        name: 'Manual Execution',\n        description: 'Manually execute API operations',\n        category: 'core',\n        enabled: true,\n        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.FREE]: { operations_per_day: 50 },\n          [this.tiers.STANDARD]: { operations_per_day: 500 },\n          [this.tiers.PROFESSIONAL]: { operations_per_day: 5000 },\n          [this.tiers.ENTERPRISE]: { operations_per_day: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'core.basic_monitoring',\n        name: 'Basic Monitoring',\n        description: 'Basic API monitoring capabilities',\n        category: 'core',\n        enabled: true,\n        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n\n      // Workflow Features\n      {\n        id: 'workflow.basic',\n        name: 'Basic Workflows',\n        description: 'Create simple sequential workflows',\n        category: 'workflow',\n        enabled: true,\n        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.FREE]: { workflows: 1, actions_per_workflow: 5 },\n          [this.tiers.STANDARD]: { workflows: 5, actions_per_workflow: 10 },\n          [this.tiers.PROFESSIONAL]: { workflows: 20, actions_per_workflow: 50 },\n          [this.tiers.ENTERPRISE]: { workflows: -1, actions_per_workflow: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'workflow.advanced',\n        name: 'Advanced Workflows',\n        description: 'Create complex workflows with conditions and branching',\n        category: 'workflow',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'workflow.scheduled',\n        name: 'Scheduled Workflows',\n        description: 'Schedule workflows to run automatically',\n        category: 'workflow',\n        enabled: true,\n        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.STANDARD]: { scheduled_workflows: 2 },\n          [this.tiers.PROFESSIONAL]: { scheduled_workflows: 10 },\n          [this.tiers.ENTERPRISE]: { scheduled_workflows: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'workflow.event_triggered',\n        name: 'Event-Triggered Workflows',\n        description: 'Trigger workflows based on events',\n        category: 'workflow',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n\n      // Export/Import Features\n      {\n        id: 'export_import.basic',\n        name: 'Basic Export/Import',\n        description: 'Basic configuration export and import',\n        category: 'export_import',\n        enabled: true,\n        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'export_import.advanced',\n        name: 'Advanced Export/Import',\n        description: 'Advanced configuration export and import with selective options',\n        category: 'export_import',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n\n      // Security Features\n      {\n        id: 'security.basic',\n        name: 'Basic Security',\n        description: 'Basic security features',\n        category: 'security',\n        enabled: true,\n        tiers: [this.tiers.FREE, this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'security.advanced',\n        name: 'Advanced Security',\n        description: 'Advanced security features including IP restrictions',\n        category: 'security',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'security.enterprise',\n        name: 'Enterprise Security',\n        description: 'Enterprise-grade security features',\n        category: 'security',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE]\n      },\n\n      // Monitoring and Alerting Features\n      {\n        id: 'monitoring.advanced',\n        name: 'Advanced Monitoring',\n        description: 'Advanced API monitoring capabilities',\n        category: 'monitoring',\n        enabled: true,\n        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'monitoring.alerting',\n        name: 'Alerting',\n        description: 'Set up alerts for API monitoring',\n        category: 'monitoring',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { alerts: 10 },\n          [this.tiers.ENTERPRISE]: { alerts: -1 } // Unlimited\n        }\n      },\n\n      // Analytics Features\n      {\n        id: 'analytics.basic',\n        name: 'Basic Analytics',\n        description: 'Basic analytics and reporting',\n        category: 'analytics',\n        enabled: true,\n        tiers: [this.tiers.STANDARD, this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'analytics.advanced',\n        name: 'Advanced Analytics',\n        description: 'Advanced analytics and reporting',\n        category: 'analytics',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE]\n      },\n      {\n        id: 'analytics.custom_reports',\n        name: 'Custom Reports',\n        description: 'Create and schedule custom reports',\n        category: 'analytics',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE]\n      },\n\n      // AI Features\n      {\n        id: 'ai.connector_generation',\n        name: 'AI-Assisted Connector Creation',\n        description: 'Generate connector configurations from API documentation',\n        category: 'ai',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { generations_per_day: 5 },\n          [this.tiers.ENTERPRISE]: { generations_per_day: 20 }\n        }\n      },\n      {\n        id: 'ai.natural_language',\n        name: 'Natural Language API Queries',\n        description: 'Create connectors using natural language descriptions',\n        category: 'ai',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { queries_per_day: 10 },\n          [this.tiers.ENTERPRISE]: { queries_per_day: 50 }\n        }\n      },\n      {\n        id: 'ai.error_resolution',\n        name: 'Intelligent Error Resolution',\n        description: 'AI-powered suggestions for fixing API errors',\n        category: 'ai',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { suggestions_per_day: 20 },\n          [this.tiers.ENTERPRISE]: { suggestions_per_day: 100 }\n        }\n      },\n      {\n        id: 'ai.workflow_optimization',\n        name: 'Predictive Workflow Optimization',\n        description: 'AI-powered suggestions for optimizing workflows',\n        category: 'ai',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.ENTERPRISE]: { optimizations_per_day: 10 }\n        }\n      },\n\n      // Governance Features\n      {\n        id: 'governance.approvals',\n        name: 'Approval Workflows',\n        description: 'Create and manage multi-step approval workflows',\n        category: 'governance',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { active_workflows: 5 },\n          [this.tiers.ENTERPRISE]: { active_workflows: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'governance.compliance',\n        name: 'Compliance Templates',\n        description: 'Pre-built compliance templates for various regulations',\n        category: 'governance',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { custom_templates: 2 },\n          [this.tiers.ENTERPRISE]: { custom_templates: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'governance.data_lineage',\n        name: 'Data Lineage Tracking',\n        description: 'Track how data moves through different systems',\n        category: 'governance',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.ENTERPRISE]: { tracking_depth: 3 }\n        }\n      },\n\n      // Security Features\n      {\n        id: 'security.ip_restrictions',\n        name: 'IP Restrictions',\n        description: 'Restrict API access to specific IP addresses',\n        category: 'security',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { restrictions_per_resource: 5 },\n          [this.tiers.ENTERPRISE]: { restrictions_per_resource: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'security.encryption',\n        name: 'Advanced Encryption',\n        description: 'Manage encryption keys and encrypt sensitive data',\n        category: 'security',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { encryption_keys: 10 },\n          [this.tiers.ENTERPRISE]: { encryption_keys: -1 } // Unlimited\n        }\n      },\n      {\n        id: 'security.policies',\n        name: 'Custom Security Policies',\n        description: 'Create and enforce custom security policies',\n        category: 'security',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.ENTERPRISE]: { policies: -1 } // Unlimited\n        }\n      },\n\n      // Advanced Monitoring Features\n      {\n        id: 'monitoring.anomaly_detection',\n        name: 'Anomaly Detection',\n        description: 'Automatically detect unusual patterns in API usage',\n        category: 'monitoring',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { detection_frequency: 'daily' },\n          [this.tiers.ENTERPRISE]: { detection_frequency: 'hourly' }\n        }\n      },\n\n      // Advanced Analytics Features\n      {\n        id: 'analytics.custom_reports',\n        name: 'Custom Reports',\n        description: 'Create and schedule custom reports',\n        category: 'analytics',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.ENTERPRISE]: { reports_per_month: 50 }\n        }\n      },\n      {\n        id: 'analytics.scheduled_reports',\n        name: 'Scheduled Reports',\n        description: 'Schedule reports to run automatically',\n        category: 'analytics',\n        enabled: true,\n        tiers: [this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.ENTERPRISE]: { scheduled_reports: 10 }\n        }\n      },\n      {\n        id: 'analytics.export_formats',\n        name: 'Advanced Export Formats',\n        description: 'Export reports in various formats (PDF, CSV, Excel)',\n        category: 'analytics',\n        enabled: true,\n        tiers: [this.tiers.PROFESSIONAL, this.tiers.ENTERPRISE],\n        limits: {\n          [this.tiers.PROFESSIONAL]: { formats: ['CSV'] },\n          [this.tiers.ENTERPRISE]: { formats: ['CSV', 'PDF', 'Excel'] }\n        }\n      }\n    ];\n  }\n\n  /**\n   * Get default subscription tiers\n   */\n  getDefaultSubscriptionTiers() {\n    return [\n      {\n        id: this.tiers.FREE,\n        name: 'Free',\n        description: 'Basic functionality for small projects',\n        price: 0,\n        features: [\n          'core.basic_connectors',\n          'core.manual_execution',\n          'core.basic_monitoring',\n          'workflow.basic',\n          'security.basic'\n        ],\n        limits: {\n          connections: 3,\n          operations_per_day: 50,\n          workflows: 1,\n          actions_per_workflow: 5\n        }\n      },\n      {\n        id: this.tiers.STANDARD,\n        name: 'Standard',\n        description: 'Standard functionality for growing teams',\n        price: 49,\n        features: [\n          'core.basic_connectors',\n          'core.manual_execution',\n          'core.basic_monitoring',\n          'workflow.basic',\n          'workflow.scheduled',\n          'export_import.basic',\n          'security.basic',\n          'monitoring.advanced',\n          'analytics.basic'\n        ],\n        limits: {\n          connections: 10,\n          operations_per_day: 500,\n          workflows: 5,\n          actions_per_workflow: 10,\n          scheduled_workflows: 2\n        }\n      },\n      {\n        id: this.tiers.PROFESSIONAL,\n        name: 'Professional',\n        description: 'Advanced functionality for professional teams',\n        price: 149,\n        features: [\n          'core.basic_connectors',\n          'core.manual_execution',\n          'core.basic_monitoring',\n          'workflow.basic',\n          'workflow.advanced',\n          'workflow.scheduled',\n          'workflow.event_triggered',\n          'export_import.basic',\n          'export_import.advanced',\n          'security.basic',\n          'security.advanced',\n          'security.ip_restrictions',\n          'security.encryption',\n          'monitoring.advanced',\n          'monitoring.alerting',\n          'monitoring.anomaly_detection',\n          'analytics.basic',\n          'analytics.advanced',\n          'analytics.export_formats',\n          'ai.connector_generation',\n          'ai.natural_language',\n          'ai.error_resolution',\n          'governance.approvals',\n          'governance.compliance'\n        ],\n        limits: {\n          connections: 50,\n          operations_per_day: 5000,\n          workflows: 20,\n          actions_per_workflow: 50,\n          scheduled_workflows: 10,\n          alerts: 10\n        }\n      },\n      {\n        id: this.tiers.ENTERPRISE,\n        name: 'Enterprise',\n        description: 'Enterprise-grade functionality for large organizations',\n        price: 499,\n        features: [\n          'core.basic_connectors',\n          'core.manual_execution',\n          'core.basic_monitoring',\n          'workflow.basic',\n          'workflow.advanced',\n          'workflow.scheduled',\n          'workflow.event_triggered',\n          'export_import.basic',\n          'export_import.advanced',\n          'security.basic',\n          'security.advanced',\n          'security.enterprise',\n          'security.ip_restrictions',\n          'security.encryption',\n          'security.policies',\n          'monitoring.advanced',\n          'monitoring.alerting',\n          'monitoring.anomaly_detection',\n          'analytics.basic',\n          'analytics.advanced',\n          'analytics.custom_reports',\n          'analytics.scheduled_reports',\n          'analytics.export_formats',\n          'ai.connector_generation',\n          'ai.natural_language',\n          'ai.error_resolution',\n          'ai.workflow_optimization',\n          'governance.approvals',\n          'governance.compliance',\n          'governance.data_lineage'\n        ],\n        limits: {\n          connections: -1, // Unlimited\n          operations_per_day: -1, // Unlimited\n          workflows: -1, // Unlimited\n          actions_per_workflow: -1, // Unlimited\n          scheduled_workflows: -1, // Unlimited\n          alerts: -1 // Unlimited\n        }\n      }\n    ];\n  }\n\n  /**\n   * Get all feature flags\n   */\n  async getAllFeatureFlags() {\n    return await this.loadData(this.featureFlagsFile);\n  }\n\n  /**\n   * Get feature flag by ID\n   */\n  async getFeatureFlagById(id) {\n    const featureFlags = await this.loadData(this.featureFlagsFile);\n    const featureFlag = featureFlags.find(f => f.id === id);\n\n    if (!featureFlag) {\n      throw new NotFoundError(`Feature flag with ID ${id} not found`);\n    }\n\n    return featureFlag;\n  }\n\n  /**\n   * Update feature flag\n   */\n  async updateFeatureFlag(id, data) {\n    const featureFlags = await this.loadData(this.featureFlagsFile);\n    const index = featureFlags.findIndex(f => f.id === id);\n\n    if (index === -1) {\n      throw new NotFoundError(`Feature flag with ID ${id} not found`);\n    }\n\n    // Update feature flag\n    featureFlags[index] = {\n      ...featureFlags[index],\n      ...data,\n      id: featureFlags[index].id // Ensure ID doesn't change\n    };\n\n    await this.saveData(this.featureFlagsFile, featureFlags);\n\n    return featureFlags[index];\n  }\n\n  /**\n   * Get all subscription tiers\n   */\n  async getAllSubscriptionTiers() {\n    return await this.loadData(this.subscriptionTiersFile);\n  }\n\n  /**\n   * Get subscription tier by ID\n   */\n  async getSubscriptionTierById(id) {\n    const subscriptionTiers = await this.loadData(this.subscriptionTiersFile);\n    const subscriptionTier = subscriptionTiers.find(t => t.id === id);\n\n    if (!subscriptionTier) {\n      throw new NotFoundError(`Subscription tier with ID ${id} not found`);\n    }\n\n    return subscriptionTier;\n  }\n\n  /**\n   * Get user entitlement\n   */\n  async getUserEntitlement(userId) {\n    const userEntitlements = await this.loadData(this.userEntitlementsFile);\n    let userEntitlement = userEntitlements.find(e => e.userId === userId);\n\n    if (!userEntitlement) {\n      // Create default entitlement for user\n      userEntitlement = {\n        userId,\n        tierId: this.tiers.FREE,\n        customFeatures: [],\n        customLimits: {},\n        created: new Date().toISOString(),\n        updated: new Date().toISOString()\n      };\n\n      userEntitlements.push(userEntitlement);\n      await this.saveData(this.userEntitlementsFile, userEntitlements);\n    }\n\n    return userEntitlement;\n  }\n\n  /**\n   * Update user entitlement\n   */\n  async updateUserEntitlement(userId, data) {\n    const userEntitlements = await this.loadData(this.userEntitlementsFile);\n    const index = userEntitlements.findIndex(e => e.userId === userId);\n\n    if (index === -1) {\n      // Create new entitlement\n      const userEntitlement = {\n        userId,\n        tierId: data.tierId || this.tiers.FREE,\n        customFeatures: data.customFeatures || [],\n        customLimits: data.customLimits || {},\n        created: new Date().toISOString(),\n        updated: new Date().toISOString()\n      };\n\n      userEntitlements.push(userEntitlement);\n    } else {\n      // Update existing entitlement\n      userEntitlements[index] = {\n        ...userEntitlements[index],\n        tierId: data.tierId || userEntitlements[index].tierId,\n        customFeatures: data.customFeatures || userEntitlements[index].customFeatures,\n        customLimits: data.customLimits || userEntitlements[index].customLimits,\n        updated: new Date().toISOString()\n      };\n    }\n\n    await this.saveData(this.userEntitlementsFile, userEntitlements);\n\n    return index === -1 ? userEntitlements[userEntitlements.length - 1] : userEntitlements[index];\n  }\n\n  /**\n   * Check if user has access to feature\n   */\n  async hasFeatureAccess(userId, featureId, tenantId = null) {\n    // If tenant ID is provided, check tenant-specific access first\n    if (tenantId) {\n      // Check cache first\n      const cacheKey = `tenant_feature_${tenantId}_${featureId}`;\n      const cachedAccess = this.cache.get(cacheKey);\n\n      if (cachedAccess !== undefined) {\n        return cachedAccess;\n      }\n\n      // Check tenant-specific access\n      const hasTenantAccess = await this.packageRegistry.hasTenantFeatureAccess(tenantId, featureId);\n\n      // Cache the result\n      this.cache.set(cacheKey, hasTenantAccess);\n\n      if (hasTenantAccess) {\n        return true;\n      }\n    }\n\n    // Check cache for user access\n    const cacheKey = `user_feature_${userId}_${featureId}`;\n    const cachedAccess = this.cache.get(cacheKey);\n\n    if (cachedAccess !== undefined) {\n      return cachedAccess;\n    }\n\n    // Get user entitlement\n    const userEntitlement = await this.getUserEntitlement(userId);\n\n    // Check if user has custom access to this feature\n    if (userEntitlement.customFeatures.includes(featureId)) {\n      // Cache the result\n      this.cache.set(cacheKey, true);\n      return true;\n    }\n\n    // Get user's subscription tier\n    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);\n\n    // Check if feature is included in the subscription tier\n    const hasAccess = subscriptionTier.features.includes(featureId);\n\n    // Cache the result\n    this.cache.set(cacheKey, hasAccess);\n\n    return hasAccess;\n  }\n\n  /**\n   * Get user's feature limit\n   */\n  async getFeatureLimit(userId, featureId, limitKey, tenantId = null) {\n    // If tenant ID is provided, check tenant-specific limit first\n    if (tenantId) {\n      // Check cache first\n      const cacheKey = `tenant_limit_${tenantId}_${featureId}_${limitKey}`;\n      const cachedLimit = this.cache.get(cacheKey);\n\n      if (cachedLimit !== undefined) {\n        return cachedLimit;\n      }\n\n      // Check tenant-specific limit\n      const tenantLimit = await this.packageRegistry.getTenantFeatureLimit(tenantId, limitKey);\n\n      if (tenantLimit !== null) {\n        // Cache the result\n        this.cache.set(cacheKey, tenantLimit);\n        return tenantLimit;\n      }\n    }\n\n    // Check cache for user limit\n    const cacheKey = `user_limit_${userId}_${featureId}_${limitKey}`;\n    const cachedLimit = this.cache.get(cacheKey);\n\n    if (cachedLimit !== undefined) {\n      return cachedLimit;\n    }\n\n    // Get user entitlement\n    const userEntitlement = await this.getUserEntitlement(userId);\n\n    // Check if user has custom limit for this feature\n    if (userEntitlement.customLimits[featureId] &&\n        userEntitlement.customLimits[featureId][limitKey] !== undefined) {\n      const limit = userEntitlement.customLimits[featureId][limitKey];\n      // Cache the result\n      this.cache.set(cacheKey, limit);\n      return limit;\n    }\n\n    // Get feature flag\n    const featureFlag = await this.getFeatureFlagById(featureId);\n\n    // Get user's subscription tier\n    const tierId = userEntitlement.tierId;\n\n    // Check if feature has limits for this tier\n    if (featureFlag.limits &&\n        featureFlag.limits[tierId] &&\n        featureFlag.limits[tierId][limitKey] !== undefined) {\n      const limit = featureFlag.limits[tierId][limitKey];\n      // Cache the result\n      this.cache.set(cacheKey, limit);\n      return limit;\n    }\n\n    // No limit found\n    this.cache.set(cacheKey, null);\n    return null;\n  }\n\n  /**\n   * Track feature usage\n   */\n  async trackFeatureUsage(userId, featureId, quantity = 1) {\n    const now = new Date();\n    const date = now.toISOString().split('T')[0]; // YYYY-MM-DD\n\n    // Load feature usage data\n    const featureUsage = await this.loadData(this.featureUsageFile);\n\n    // Find or create usage record\n    let usageRecord = featureUsage.find(u =>\n      u.userId === userId &&\n      u.featureId === featureId &&\n      u.date === date\n    );\n\n    if (!usageRecord) {\n      // Create new usage record\n      usageRecord = {\n        userId,\n        featureId,\n        date,\n        quantity: 0,\n        created: now.toISOString(),\n        updated: now.toISOString()\n      };\n\n      featureUsage.push(usageRecord);\n    }\n\n    // Update usage quantity\n    usageRecord.quantity += quantity;\n    usageRecord.updated = now.toISOString();\n\n    // Save updated usage data\n    await this.saveData(this.featureUsageFile, featureUsage);\n\n    return usageRecord;\n  }\n\n  /**\n   * Get feature usage for user\n   */\n  async getFeatureUsageForUser(userId, featureId, startDate, endDate) {\n    // Load feature usage data\n    const featureUsage = await this.loadData(this.featureUsageFile);\n\n    // Filter usage records\n    let filteredUsage = featureUsage.filter(u => u.userId === userId);\n\n    if (featureId) {\n      filteredUsage = filteredUsage.filter(u => u.featureId === featureId);\n    }\n\n    if (startDate) {\n      filteredUsage = filteredUsage.filter(u => u.date >= startDate);\n    }\n\n    if (endDate) {\n      filteredUsage = filteredUsage.filter(u => u.date <= endDate);\n    }\n\n    return filteredUsage;\n  }\n\n  /**\n   * Check if user has reached feature limit\n   */\n  async hasReachedFeatureLimit(userId, featureId, limitKey) {\n    // Get feature limit\n    const limit = await this.getFeatureLimit(userId, featureId, limitKey);\n\n    // If limit is null or -1, there is no limit\n    if (limit === null || limit === -1) {\n      return false;\n    }\n\n    // Get current usage\n    const now = new Date();\n    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD\n\n    // For daily limits\n    if (limitKey.includes('per_day')) {\n      const usage = await this.getFeatureUsageForUser(userId, featureId, today, today);\n      const totalUsage = usage.reduce((sum, record) => sum + record.quantity, 0);\n      return totalUsage >= limit;\n    }\n\n    // For other limits, we need to check the actual count\n    // This would typically involve querying the relevant service\n    // For now, we'll just return false\n    return false;\n  }\n\n  /**\n   * Get user's available features\n   */\n  async getUserAvailableFeatures(userId, tenantId = null) {\n    // Check cache first\n    const cacheKey = tenantId ?\n      `tenant_user_features_${tenantId}_${userId}` :\n      `user_features_${userId}`;\n\n    const cachedFeatures = this.cache.get(cacheKey);\n\n    if (cachedFeatures) {\n      return cachedFeatures;\n    }\n\n    // Get tenant-specific features if tenant ID is provided\n    let tenantFeatures = [];\n    if (tenantId) {\n      tenantFeatures = await this.packageRegistry.getTenantAvailableFeatures(tenantId);\n    }\n\n    // Get user entitlement\n    const userEntitlement = await this.getUserEntitlement(userId);\n\n    // Get user's subscription tier\n    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);\n\n    // Get all feature flags\n    const featureFlags = await this.getAllFeatureFlags();\n\n    // Filter features available to user\n    const availableFeatures = featureFlags.filter(feature =>\n      feature.enabled && (\n        subscriptionTier.features.includes(feature.id) ||\n        userEntitlement.customFeatures.includes(feature.id) ||\n        tenantFeatures.includes(feature.id)\n      )\n    );\n\n    // Cache the result\n    this.cache.set(cacheKey, availableFeatures);\n\n    return availableFeatures;\n  }\n\n  /**\n   * Get tenant package information\n   */\n  async getTenantPackage(tenantId) {\n    // Check cache first\n    const cacheKey = `tenant_package_${tenantId}`;\n    const cachedPackage = this.cache.get(cacheKey);\n\n    if (cachedPackage) {\n      return cachedPackage;\n    }\n\n    // Get tenant mapping\n    const mapping = await this.packageRegistry.getTenantMapping(tenantId);\n\n    // Get package details\n    const packageDetails = await this.packageRegistry.getPackageById(mapping.packageId);\n\n    // Cache the result\n    this.cache.set(cacheKey, packageDetails);\n\n    return packageDetails;\n  }\n\n  /**\n   * Set tenant package\n   */\n  async setTenantPackage(tenantId, packageId, customFeatures = [], customLimits = {}) {\n    // Set tenant mapping\n    const mapping = await this.packageRegistry.setTenantMapping(tenantId, packageId, customFeatures, customLimits);\n\n    // Invalidate cache\n    this.cache.del(`tenant_package_${tenantId}`);\n    this.cache.del(`tenant_user_features_${tenantId}_*`);\n\n    return mapping;\n  }\n\n  /**\n   * Get all packages\n   */\n  async getAllPackages() {\n    return await this.packageRegistry.getAllPackages();\n  }\n\n  /**\n   * Get package by ID\n   */\n  async getPackageById(id) {\n    return await this.packageRegistry.getPackageById(id);\n  }\n\n  /**\n   * Create a new package\n   */\n  async createPackage(packageData) {\n    return await this.packageRegistry.createPackage(packageData);\n  }\n\n  /**\n   * Update a package\n   */\n  async updatePackage(id, packageData) {\n    return await this.packageRegistry.updatePackage(id, packageData);\n  }\n\n  /**\n   * Delete a package\n   */\n  async deletePackage(id) {\n    return await this.packageRegistry.deletePackage(id);\n  }\n\n  /**\n   * Clear cache\n   */\n  clearCache() {\n    this.cache.flushAll();\n    this.packageRegistry.clearCache();\n  }\n\n  /**\n   * Get user's subscription details\n   */\n  async getUserSubscriptionDetails(userId) {\n    // Get user entitlement\n    const userEntitlement = await this.getUserEntitlement(userId);\n\n    // Get user's subscription tier\n    const subscriptionTier = await this.getSubscriptionTierById(userEntitlement.tierId);\n\n    // Get user's available features\n    const availableFeatures = await this.getUserAvailableFeatures(userId);\n\n    // Get feature usage\n    const now = new Date();\n    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD\n    const usage = await this.getFeatureUsageForUser(userId, null, today, today);\n\n    // Compile subscription details\n    return {\n      userId,\n      tier: {\n        id: subscriptionTier.id,\n        name: subscriptionTier.name,\n        description: subscriptionTier.description,\n        price: subscriptionTier.price\n      },\n      features: availableFeatures.map(feature => ({\n        id: feature.id,\n        name: feature.name,\n        description: feature.description,\n        category: feature.category\n      })),\n      limits: subscriptionTier.limits,\n      customFeatures: userEntitlement.customFeatures,\n      customLimits: userEntitlement.customLimits,\n      usage: usage.reduce((result, record) => {\n        result[record.featureId] = (result[record.featureId] || 0) + record.quantity;\n        return result;\n      }, {})\n    };\n  }\n}\n\nmodule.exports = FeatureFlagService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAM;EAAEG,eAAe;EAAEC,aAAa;EAAEC;AAAmB,CAAC,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AACzF,MAAMM,qBAAqB,GAAGN,OAAO,CAAC,yBAAyB,CAAC;AAChE,MAAMO,SAAS,GAAGP,OAAO,CAAC,YAAY,CAAC;AAEvC,MAAMQ,kBAAkB,CAAC;EACvBC,WAAWA,CAACC,OAAO,GAAGR,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAE;IACrD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,eAAe,GAAGX,IAAI,CAACS,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,eAAe,CAAC;IAC/D,IAAI,CAACI,gBAAgB,GAAGZ,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE,oBAAoB,CAAC;IAC7E,IAAI,CAACE,qBAAqB,GAAGb,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE,yBAAyB,CAAC;IACvF,IAAI,CAACG,oBAAoB,GAAGd,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE,wBAAwB,CAAC;IACrF,IAAI,CAACI,gBAAgB,GAAGf,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE,oBAAoB,CAAC;;IAE7E;IACA,IAAI,CAACK,KAAK,GAAG,IAAIX,SAAS,CAAC;MAAEY,MAAM,EAAE,GAAG;MAAEC,WAAW,EAAE;IAAG,CAAC,CAAC;;IAE5D;IACA,IAAI,CAACC,eAAe,GAAG,IAAIf,qBAAqB,CAACI,OAAO,CAAC;IAEzD,IAAI,CAACY,aAAa,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACC,KAAK,GAAG;MACXC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,UAAU;MACpBC,YAAY,EAAE,cAAc;MAC5BC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,IAAI,CAACC,YAAY,GAAG;MAClBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,QAAQ;MAChBH,UAAU,EAAE,YAAY;MACxBI,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMT,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMvB,EAAE,CAACiC,KAAK,CAAC,IAAI,CAACnB,eAAe,EAAE;QAAEoB,SAAS,EAAE;MAAK,CAAC,CAAC;;MAEzD;MACA,MAAM,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpB,gBAAgB,EAAE,IAAI,CAACqB,sBAAsB,CAAC,CAAC,CAAC;MAC/E,MAAM,IAAI,CAACD,cAAc,CAAC,IAAI,CAACnB,qBAAqB,EAAE,IAAI,CAACqB,2BAA2B,CAAC,CAAC,CAAC;MACzF,MAAM,IAAI,CAACF,cAAc,CAAC,IAAI,CAAClB,oBAAoB,EAAE,EAAE,CAAC;MACxD,MAAM,IAAI,CAACkB,cAAc,CAAC,IAAI,CAACjB,gBAAgB,EAAE,EAAE,CAAC;IACtD,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMH,cAAcA,CAACK,QAAQ,EAAEC,WAAW,EAAE;IAC1C,IAAI;MACF,MAAMzC,EAAE,CAAC0C,MAAM,CAACF,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOF,KAAK,EAAE;MACd,IAAIA,KAAK,CAACK,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACA,MAAM3C,EAAE,CAAC4C,SAAS,CAACJ,QAAQ,EAAEK,IAAI,CAACC,SAAS,CAACL,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MACpE,CAAC,MAAM;QACL,MAAMH,KAAK;MACb;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAMS,QAAQA,CAACP,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMQ,IAAI,GAAG,MAAMhD,EAAE,CAACiD,QAAQ,CAACT,QAAQ,EAAE,MAAM,CAAC;MAChD,OAAOK,IAAI,CAACK,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOV,KAAK,EAAE;MACd,IAAIA,KAAK,CAACK,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACA,IAAIH,QAAQ,KAAK,IAAI,CAACzB,gBAAgB,EAAE;UACtC,OAAO,IAAI,CAACqB,sBAAsB,CAAC,CAAC;QACtC,CAAC,MAAM,IAAII,QAAQ,KAAK,IAAI,CAACxB,qBAAqB,EAAE;UAClD,OAAO,IAAI,CAACqB,2BAA2B,CAAC,CAAC;QAC3C;QACA,OAAO,EAAE;MACX;MACAE,OAAO,CAACD,KAAK,CAAC,2BAA2BE,QAAQ,GAAG,EAAEF,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMa,QAAQA,CAACX,QAAQ,EAAEQ,IAAI,EAAE;IAC7B,IAAI;MACF,MAAMhD,EAAE,CAAC4C,SAAS,CAACJ,QAAQ,EAAEK,IAAI,CAACC,SAAS,CAACE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,OAAOV,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwBE,QAAQ,GAAG,EAAEF,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEF,sBAAsBA,CAAA,EAAG;IACvB,OAAO;IACL;IACA;MACEgB,EAAE,EAAE,uBAAuB;MAC3BC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,gCAAgC;MAC7CC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MAC7F6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACC,IAAI,GAAG;UAAEiC,WAAW,EAAE;QAAE,CAAC;QACrC,CAAC,IAAI,CAAClC,KAAK,CAACE,QAAQ,GAAG;UAAEgC,WAAW,EAAE;QAAG,CAAC;QAC1C,CAAC,IAAI,CAAClC,KAAK,CAACG,YAAY,GAAG;UAAE+B,WAAW,EAAE;QAAG,CAAC;QAC9C,CAAC,IAAI,CAAClC,KAAK,CAACI,UAAU,GAAG;UAAE8B,WAAW,EAAE,CAAC;QAAE,CAAC,CAAC;MAC/C;IACF,CAAC,EACD;MACEN,EAAE,EAAE,uBAAuB;MAC3BC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,iCAAiC;MAC9CC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MAC7F6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACC,IAAI,GAAG;UAAEkC,kBAAkB,EAAE;QAAG,CAAC;QAC7C,CAAC,IAAI,CAACnC,KAAK,CAACE,QAAQ,GAAG;UAAEiC,kBAAkB,EAAE;QAAI,CAAC;QAClD,CAAC,IAAI,CAACnC,KAAK,CAACG,YAAY,GAAG;UAAEgC,kBAAkB,EAAE;QAAK,CAAC;QACvD,CAAC,IAAI,CAACnC,KAAK,CAACI,UAAU,GAAG;UAAE+B,kBAAkB,EAAE,CAAC;QAAE,CAAC,CAAC;MACtD;IACF,CAAC,EACD;MACEP,EAAE,EAAE,uBAAuB;MAC3BC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,mCAAmC;MAChDC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IAC9F,CAAC;IAED;IACA;MACEwB,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE,oCAAoC;MACjDC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MAC7F6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACC,IAAI,GAAG;UAAEmC,SAAS,EAAE,CAAC;UAAEC,oBAAoB,EAAE;QAAE,CAAC;QAC5D,CAAC,IAAI,CAACrC,KAAK,CAACE,QAAQ,GAAG;UAAEkC,SAAS,EAAE,CAAC;UAAEC,oBAAoB,EAAE;QAAG,CAAC;QACjE,CAAC,IAAI,CAACrC,KAAK,CAACG,YAAY,GAAG;UAAEiC,SAAS,EAAE,EAAE;UAAEC,oBAAoB,EAAE;QAAG,CAAC;QACtE,CAAC,IAAI,CAACrC,KAAK,CAACI,UAAU,GAAG;UAAEgC,SAAS,EAAE,CAAC,CAAC;UAAEC,oBAAoB,EAAE,CAAC;QAAE,CAAC,CAAC;MACvE;IACF,CAAC,EACD;MACET,EAAE,EAAE,mBAAmB;MACvBC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,wDAAwD;MACrEC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IACxD,CAAC,EACD;MACEwB,EAAE,EAAE,oBAAoB;MACxBC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,yCAAyC;MACtDC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MAC5E6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACE,QAAQ,GAAG;UAAEoC,mBAAmB,EAAE;QAAE,CAAC;QACjD,CAAC,IAAI,CAACtC,KAAK,CAACG,YAAY,GAAG;UAAEmC,mBAAmB,EAAE;QAAG,CAAC;QACtD,CAAC,IAAI,CAACtC,KAAK,CAACI,UAAU,GAAG;UAAEkC,mBAAmB,EAAE,CAAC;QAAE,CAAC,CAAC;MACvD;IACF,CAAC,EACD;MACEV,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,2BAA2B;MACjCC,WAAW,EAAE,mCAAmC;MAChDC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IACxD,CAAC;IAED;IACA;MACEwB,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,uCAAuC;MACpDC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IAC7E,CAAC,EACD;MACEwB,EAAE,EAAE,wBAAwB;MAC5BC,IAAI,EAAE,wBAAwB;MAC9BC,WAAW,EAAE,iEAAiE;MAC9EC,QAAQ,EAAE,eAAe;MACzBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IACxD,CAAC;IAED;IACA;MACEwB,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,yBAAyB;MACtCC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACC,IAAI,EAAE,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IAC9F,CAAC,EACD;MACEwB,EAAE,EAAE,mBAAmB;MACvBC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,sDAAsD;MACnEC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IACxD,CAAC,EACD;MACEwB,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,oCAAoC;MACjDC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU;IAC/B,CAAC;IAED;IACA;MACEwB,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,sCAAsC;MACnDC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IAC7E,CAAC,EACD;MACEwB,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,kCAAkC;MAC/CC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAEoC,MAAM,EAAE;QAAG,CAAC;QACzC,CAAC,IAAI,CAACvC,KAAK,CAACI,UAAU,GAAG;UAAEmC,MAAM,EAAE,CAAC;QAAE,CAAC,CAAC;MAC1C;IACF,CAAC;IAED;IACA;MACEX,EAAE,EAAE,iBAAiB;MACrBC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE,+BAA+B;MAC5CC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACE,QAAQ,EAAE,IAAI,CAACF,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IAC7E,CAAC,EACD;MACEwB,EAAE,EAAE,oBAAoB;MACxBC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,kCAAkC;MAC/CC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU;IACxD,CAAC,EACD;MACEwB,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,oCAAoC;MACjDC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU;IAC/B,CAAC;IAED;IACA;MACEwB,EAAE,EAAE,yBAAyB;MAC7BC,IAAI,EAAE,gCAAgC;MACtCC,WAAW,EAAE,0DAA0D;MACvEC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAEqC,mBAAmB,EAAE;QAAE,CAAC;QACrD,CAAC,IAAI,CAACxC,KAAK,CAACI,UAAU,GAAG;UAAEoC,mBAAmB,EAAE;QAAG;MACrD;IACF,CAAC,EACD;MACEZ,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,8BAA8B;MACpCC,WAAW,EAAE,uDAAuD;MACpEC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAEsC,eAAe,EAAE;QAAG,CAAC;QAClD,CAAC,IAAI,CAACzC,KAAK,CAACI,UAAU,GAAG;UAAEqC,eAAe,EAAE;QAAG;MACjD;IACF,CAAC,EACD;MACEb,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,8BAA8B;MACpCC,WAAW,EAAE,8CAA8C;MAC3DC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAEuC,mBAAmB,EAAE;QAAG,CAAC;QACtD,CAAC,IAAI,CAAC1C,KAAK,CAACI,UAAU,GAAG;UAAEsC,mBAAmB,EAAE;QAAI;MACtD;IACF,CAAC,EACD;MACEd,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,kCAAkC;MACxCC,WAAW,EAAE,iDAAiD;MAC9DC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU,CAAC;MAC9B6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACI,UAAU,GAAG;UAAEuC,qBAAqB,EAAE;QAAG;MACvD;IACF,CAAC;IAED;IACA;MACEf,EAAE,EAAE,sBAAsB;MAC1BC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,iDAAiD;MAC9DC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAEyC,gBAAgB,EAAE;QAAE,CAAC;QAClD,CAAC,IAAI,CAAC5C,KAAK,CAACI,UAAU,GAAG;UAAEwC,gBAAgB,EAAE,CAAC;QAAE,CAAC,CAAC;MACpD;IACF,CAAC,EACD;MACEhB,EAAE,EAAE,uBAAuB;MAC3BC,IAAI,EAAE,sBAAsB;MAC5BC,WAAW,EAAE,wDAAwD;MACrEC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAE0C,gBAAgB,EAAE;QAAE,CAAC;QAClD,CAAC,IAAI,CAAC7C,KAAK,CAACI,UAAU,GAAG;UAAEyC,gBAAgB,EAAE,CAAC;QAAE,CAAC,CAAC;MACpD;IACF,CAAC,EACD;MACEjB,EAAE,EAAE,yBAAyB;MAC7BC,IAAI,EAAE,uBAAuB;MAC7BC,WAAW,EAAE,gDAAgD;MAC7DC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU,CAAC;MAC9B6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACI,UAAU,GAAG;UAAE0C,cAAc,EAAE;QAAE;MAC/C;IACF,CAAC;IAED;IACA;MACElB,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE,8CAA8C;MAC3DC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAE4C,yBAAyB,EAAE;QAAE,CAAC;QAC3D,CAAC,IAAI,CAAC/C,KAAK,CAACI,UAAU,GAAG;UAAE2C,yBAAyB,EAAE,CAAC;QAAE,CAAC,CAAC;MAC7D;IACF,CAAC,EACD;MACEnB,EAAE,EAAE,qBAAqB;MACzBC,IAAI,EAAE,qBAAqB;MAC3BC,WAAW,EAAE,mDAAmD;MAChEC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAE6C,eAAe,EAAE;QAAG,CAAC;QAClD,CAAC,IAAI,CAAChD,KAAK,CAACI,UAAU,GAAG;UAAE4C,eAAe,EAAE,CAAC;QAAE,CAAC,CAAC;MACnD;IACF,CAAC,EACD;MACEpB,EAAE,EAAE,mBAAmB;MACvBC,IAAI,EAAE,0BAA0B;MAChCC,WAAW,EAAE,6CAA6C;MAC1DC,QAAQ,EAAE,UAAU;MACpBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU,CAAC;MAC9B6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACI,UAAU,GAAG;UAAE6C,QAAQ,EAAE,CAAC;QAAE,CAAC,CAAC;MAC5C;IACF,CAAC;IAED;IACA;MACErB,EAAE,EAAE,8BAA8B;MAClCC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,oDAAoD;MACjEC,QAAQ,EAAE,YAAY;MACtBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAE+C,mBAAmB,EAAE;QAAQ,CAAC;QAC3D,CAAC,IAAI,CAAClD,KAAK,CAACI,UAAU,GAAG;UAAE8C,mBAAmB,EAAE;QAAS;MAC3D;IACF,CAAC;IAED;IACA;MACEtB,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,oCAAoC;MACjDC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU,CAAC;MAC9B6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACI,UAAU,GAAG;UAAE+C,iBAAiB,EAAE;QAAG;MACnD;IACF,CAAC,EACD;MACEvB,EAAE,EAAE,6BAA6B;MACjCC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,uCAAuC;MACpDC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACI,UAAU,CAAC;MAC9B6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACI,UAAU,GAAG;UAAEgD,iBAAiB,EAAE;QAAG;MACnD;IACF,CAAC,EACD;MACExB,EAAE,EAAE,0BAA0B;MAC9BC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,qDAAqD;MAClEC,QAAQ,EAAE,WAAW;MACrBC,OAAO,EAAE,IAAI;MACbhC,KAAK,EAAE,CAAC,IAAI,CAACA,KAAK,CAACG,YAAY,EAAE,IAAI,CAACH,KAAK,CAACI,UAAU,CAAC;MACvD6B,MAAM,EAAE;QACN,CAAC,IAAI,CAACjC,KAAK,CAACG,YAAY,GAAG;UAAEkD,OAAO,EAAE,CAAC,KAAK;QAAE,CAAC;QAC/C,CAAC,IAAI,CAACrD,KAAK,CAACI,UAAU,GAAG;UAAEiD,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO;QAAE;MAC9D;IACF,CAAC,CACF;EACH;;EAEA;AACF;AACA;EACExC,2BAA2BA,CAAA,EAAG;IAC5B,OAAO,CACL;MACEe,EAAE,EAAE,IAAI,CAAC5B,KAAK,CAACC,IAAI;MACnB4B,IAAI,EAAE,MAAM;MACZC,WAAW,EAAE,wCAAwC;MACrDwB,KAAK,EAAE,CAAC;MACRC,QAAQ,EAAE,CACR,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,gBAAgB,CACjB;MACDtB,MAAM,EAAE;QACNC,WAAW,EAAE,CAAC;QACdC,kBAAkB,EAAE,EAAE;QACtBC,SAAS,EAAE,CAAC;QACZC,oBAAoB,EAAE;MACxB;IACF,CAAC,EACD;MACET,EAAE,EAAE,IAAI,CAAC5B,KAAK,CAACE,QAAQ;MACvB2B,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,0CAA0C;MACvDwB,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,CACR,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,oBAAoB,EACpB,qBAAqB,EACrB,gBAAgB,EAChB,qBAAqB,EACrB,iBAAiB,CAClB;MACDtB,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,GAAG;QACvBC,SAAS,EAAE,CAAC;QACZC,oBAAoB,EAAE,EAAE;QACxBC,mBAAmB,EAAE;MACvB;IACF,CAAC,EACD;MACEV,EAAE,EAAE,IAAI,CAAC5B,KAAK,CAACG,YAAY;MAC3B0B,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,+CAA+C;MAC5DwB,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,CACR,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,oBAAoB,EACpB,0BAA0B,EAC1B,qBAAqB,EACrB,wBAAwB,EACxB,gBAAgB,EAChB,mBAAmB,EACnB,0BAA0B,EAC1B,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,8BAA8B,EAC9B,iBAAiB,EACjB,oBAAoB,EACpB,0BAA0B,EAC1B,yBAAyB,EACzB,qBAAqB,EACrB,qBAAqB,EACrB,sBAAsB,EACtB,uBAAuB,CACxB;MACDtB,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,kBAAkB,EAAE,IAAI;QACxBC,SAAS,EAAE,EAAE;QACbC,oBAAoB,EAAE,EAAE;QACxBC,mBAAmB,EAAE,EAAE;QACvBC,MAAM,EAAE;MACV;IACF,CAAC,EACD;MACEX,EAAE,EAAE,IAAI,CAAC5B,KAAK,CAACI,UAAU;MACzByB,IAAI,EAAE,YAAY;MAClBC,WAAW,EAAE,wDAAwD;MACrEwB,KAAK,EAAE,GAAG;MACVC,QAAQ,EAAE,CACR,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,oBAAoB,EACpB,0BAA0B,EAC1B,qBAAqB,EACrB,wBAAwB,EACxB,gBAAgB,EAChB,mBAAmB,EACnB,qBAAqB,EACrB,0BAA0B,EAC1B,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,EACrB,qBAAqB,EACrB,8BAA8B,EAC9B,iBAAiB,EACjB,oBAAoB,EACpB,0BAA0B,EAC1B,6BAA6B,EAC7B,0BAA0B,EAC1B,yBAAyB,EACzB,qBAAqB,EACrB,qBAAqB,EACrB,0BAA0B,EAC1B,sBAAsB,EACtB,uBAAuB,EACvB,yBAAyB,CAC1B;MACDtB,MAAM,EAAE;QACNC,WAAW,EAAE,CAAC,CAAC;QAAE;QACjBC,kBAAkB,EAAE,CAAC,CAAC;QAAE;QACxBC,SAAS,EAAE,CAAC,CAAC;QAAE;QACfC,oBAAoB,EAAE,CAAC,CAAC;QAAE;QAC1BC,mBAAmB,EAAE,CAAC,CAAC;QAAE;QACzBC,MAAM,EAAE,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CACF;EACH;;EAEA;AACF;AACA;EACE,MAAMiB,kBAAkBA,CAAA,EAAG;IACzB,OAAO,MAAM,IAAI,CAACjC,QAAQ,CAAC,IAAI,CAAChC,gBAAgB,CAAC;EACnD;;EAEA;AACF;AACA;EACE,MAAMkE,kBAAkBA,CAAC7B,EAAE,EAAE;IAC3B,MAAM8B,YAAY,GAAG,MAAM,IAAI,CAACnC,QAAQ,CAAC,IAAI,CAAChC,gBAAgB,CAAC;IAC/D,MAAMoE,WAAW,GAAGD,YAAY,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKA,EAAE,CAAC;IAEvD,IAAI,CAAC+B,WAAW,EAAE;MAChB,MAAM,IAAI9E,aAAa,CAAC,wBAAwB+C,EAAE,YAAY,CAAC;IACjE;IAEA,OAAO+B,WAAW;EACpB;;EAEA;AACF;AACA;EACE,MAAMG,iBAAiBA,CAAClC,EAAE,EAAEJ,IAAI,EAAE;IAChC,MAAMkC,YAAY,GAAG,MAAM,IAAI,CAACnC,QAAQ,CAAC,IAAI,CAAChC,gBAAgB,CAAC;IAC/D,MAAMwE,KAAK,GAAGL,YAAY,CAACM,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKA,EAAE,CAAC;IAEtD,IAAImC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,MAAM,IAAIlF,aAAa,CAAC,wBAAwB+C,EAAE,YAAY,CAAC;IACjE;;IAEA;IACA8B,YAAY,CAACK,KAAK,CAAC,GAAG;MACpB,GAAGL,YAAY,CAACK,KAAK,CAAC;MACtB,GAAGvC,IAAI;MACPI,EAAE,EAAE8B,YAAY,CAACK,KAAK,CAAC,CAACnC,EAAE,CAAC;IAC7B,CAAC;IAED,MAAM,IAAI,CAACD,QAAQ,CAAC,IAAI,CAACpC,gBAAgB,EAAEmE,YAAY,CAAC;IAExD,OAAOA,YAAY,CAACK,KAAK,CAAC;EAC5B;;EAEA;AACF;AACA;EACE,MAAME,uBAAuBA,CAAA,EAAG;IAC9B,OAAO,MAAM,IAAI,CAAC1C,QAAQ,CAAC,IAAI,CAAC/B,qBAAqB,CAAC;EACxD;;EAEA;AACF;AACA;EACE,MAAM0E,uBAAuBA,CAACtC,EAAE,EAAE;IAChC,MAAMuC,iBAAiB,GAAG,MAAM,IAAI,CAAC5C,QAAQ,CAAC,IAAI,CAAC/B,qBAAqB,CAAC;IACzE,MAAM4E,gBAAgB,GAAGD,iBAAiB,CAACP,IAAI,CAACS,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKA,EAAE,CAAC;IAEjE,IAAI,CAACwC,gBAAgB,EAAE;MACrB,MAAM,IAAIvF,aAAa,CAAC,6BAA6B+C,EAAE,YAAY,CAAC;IACtE;IAEA,OAAOwC,gBAAgB;EACzB;;EAEA;AACF;AACA;EACE,MAAME,kBAAkBA,CAACC,MAAM,EAAE;IAC/B,MAAMC,gBAAgB,GAAG,MAAM,IAAI,CAACjD,QAAQ,CAAC,IAAI,CAAC9B,oBAAoB,CAAC;IACvE,IAAIgF,eAAe,GAAGD,gBAAgB,CAACZ,IAAI,CAACc,CAAC,IAAIA,CAAC,CAACH,MAAM,KAAKA,MAAM,CAAC;IAErE,IAAI,CAACE,eAAe,EAAE;MACpB;MACAA,eAAe,GAAG;QAChBF,MAAM;QACNI,MAAM,EAAE,IAAI,CAAC3E,KAAK,CAACC,IAAI;QACvB2E,cAAc,EAAE,EAAE;QAClBC,YAAY,EAAE,CAAC,CAAC;QAChBC,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACjCC,OAAO,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAClC,CAAC;MAEDR,gBAAgB,CAACU,IAAI,CAACT,eAAe,CAAC;MACtC,MAAM,IAAI,CAAC9C,QAAQ,CAAC,IAAI,CAAClC,oBAAoB,EAAE+E,gBAAgB,CAAC;IAClE;IAEA,OAAOC,eAAe;EACxB;;EAEA;AACF;AACA;EACE,MAAMU,qBAAqBA,CAACZ,MAAM,EAAE/C,IAAI,EAAE;IACxC,MAAMgD,gBAAgB,GAAG,MAAM,IAAI,CAACjD,QAAQ,CAAC,IAAI,CAAC9B,oBAAoB,CAAC;IACvE,MAAMsE,KAAK,GAAGS,gBAAgB,CAACR,SAAS,CAACU,CAAC,IAAIA,CAAC,CAACH,MAAM,KAAKA,MAAM,CAAC;IAElE,IAAIR,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB;MACA,MAAMU,eAAe,GAAG;QACtBF,MAAM;QACNI,MAAM,EAAEnD,IAAI,CAACmD,MAAM,IAAI,IAAI,CAAC3E,KAAK,CAACC,IAAI;QACtC2E,cAAc,EAAEpD,IAAI,CAACoD,cAAc,IAAI,EAAE;QACzCC,YAAY,EAAErD,IAAI,CAACqD,YAAY,IAAI,CAAC,CAAC;QACrCC,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACjCC,OAAO,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAClC,CAAC;MAEDR,gBAAgB,CAACU,IAAI,CAACT,eAAe,CAAC;IACxC,CAAC,MAAM;MACL;MACAD,gBAAgB,CAACT,KAAK,CAAC,GAAG;QACxB,GAAGS,gBAAgB,CAACT,KAAK,CAAC;QAC1BY,MAAM,EAAEnD,IAAI,CAACmD,MAAM,IAAIH,gBAAgB,CAACT,KAAK,CAAC,CAACY,MAAM;QACrDC,cAAc,EAAEpD,IAAI,CAACoD,cAAc,IAAIJ,gBAAgB,CAACT,KAAK,CAAC,CAACa,cAAc;QAC7EC,YAAY,EAAErD,IAAI,CAACqD,YAAY,IAAIL,gBAAgB,CAACT,KAAK,CAAC,CAACc,YAAY;QACvEI,OAAO,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAClC,CAAC;IACH;IAEA,MAAM,IAAI,CAACrD,QAAQ,CAAC,IAAI,CAAClC,oBAAoB,EAAE+E,gBAAgB,CAAC;IAEhE,OAAOT,KAAK,KAAK,CAAC,CAAC,GAAGS,gBAAgB,CAACA,gBAAgB,CAACY,MAAM,GAAG,CAAC,CAAC,GAAGZ,gBAAgB,CAACT,KAAK,CAAC;EAC/F;;EAEA;AACF;AACA;EACE,MAAMsB,gBAAgBA,CAACd,MAAM,EAAEe,SAAS,EAAEC,QAAQ,GAAG,IAAI,EAAE;IACzD;IACA,IAAIA,QAAQ,EAAE;MACZ;MACA,MAAMC,QAAQ,GAAG,kBAAkBD,QAAQ,IAAID,SAAS,EAAE;MAC1D,MAAMG,YAAY,GAAG,IAAI,CAAC9F,KAAK,CAAC+F,GAAG,CAACF,QAAQ,CAAC;MAE7C,IAAIC,YAAY,KAAKE,SAAS,EAAE;QAC9B,OAAOF,YAAY;MACrB;;MAEA;MACA,MAAMG,eAAe,GAAG,MAAM,IAAI,CAAC9F,eAAe,CAAC+F,sBAAsB,CAACN,QAAQ,EAAED,SAAS,CAAC;;MAE9F;MACA,IAAI,CAAC3F,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAEI,eAAe,CAAC;MAEzC,IAAIA,eAAe,EAAE;QACnB,OAAO,IAAI;MACb;IACF;;IAEA;IACA,MAAMJ,QAAQ,GAAG,gBAAgBjB,MAAM,IAAIe,SAAS,EAAE;IACtD,MAAMG,YAAY,GAAG,IAAI,CAAC9F,KAAK,CAAC+F,GAAG,CAACF,QAAQ,CAAC;IAE7C,IAAIC,YAAY,KAAKE,SAAS,EAAE;MAC9B,OAAOF,YAAY;IACrB;;IAEA;IACA,MAAMhB,eAAe,GAAG,MAAM,IAAI,CAACH,kBAAkB,CAACC,MAAM,CAAC;;IAE7D;IACA,IAAIE,eAAe,CAACG,cAAc,CAACmB,QAAQ,CAACT,SAAS,CAAC,EAAE;MACtD;MACA,IAAI,CAAC3F,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAE,IAAI,CAAC;MAC9B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMpB,gBAAgB,GAAG,MAAM,IAAI,CAACF,uBAAuB,CAACO,eAAe,CAACE,MAAM,CAAC;;IAEnF;IACA,MAAMqB,SAAS,GAAG5B,gBAAgB,CAACb,QAAQ,CAACwC,QAAQ,CAACT,SAAS,CAAC;;IAE/D;IACA,IAAI,CAAC3F,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAEQ,SAAS,CAAC;IAEnC,OAAOA,SAAS;EAClB;;EAEA;AACF;AACA;EACE,MAAMC,eAAeA,CAAC1B,MAAM,EAAEe,SAAS,EAAEY,QAAQ,EAAEX,QAAQ,GAAG,IAAI,EAAE;IAClE;IACA,IAAIA,QAAQ,EAAE;MACZ;MACA,MAAMC,QAAQ,GAAG,gBAAgBD,QAAQ,IAAID,SAAS,IAAIY,QAAQ,EAAE;MACpE,MAAMC,WAAW,GAAG,IAAI,CAACxG,KAAK,CAAC+F,GAAG,CAACF,QAAQ,CAAC;MAE5C,IAAIW,WAAW,KAAKR,SAAS,EAAE;QAC7B,OAAOQ,WAAW;MACpB;;MAEA;MACA,MAAMC,WAAW,GAAG,MAAM,IAAI,CAACtG,eAAe,CAACuG,qBAAqB,CAACd,QAAQ,EAAEW,QAAQ,CAAC;MAExF,IAAIE,WAAW,KAAK,IAAI,EAAE;QACxB;QACA,IAAI,CAACzG,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAEY,WAAW,CAAC;QACrC,OAAOA,WAAW;MACpB;IACF;;IAEA;IACA,MAAMZ,QAAQ,GAAG,cAAcjB,MAAM,IAAIe,SAAS,IAAIY,QAAQ,EAAE;IAChE,MAAMC,WAAW,GAAG,IAAI,CAACxG,KAAK,CAAC+F,GAAG,CAACF,QAAQ,CAAC;IAE5C,IAAIW,WAAW,KAAKR,SAAS,EAAE;MAC7B,OAAOQ,WAAW;IACpB;;IAEA;IACA,MAAM1B,eAAe,GAAG,MAAM,IAAI,CAACH,kBAAkB,CAACC,MAAM,CAAC;;IAE7D;IACA,IAAIE,eAAe,CAACI,YAAY,CAACS,SAAS,CAAC,IACvCb,eAAe,CAACI,YAAY,CAACS,SAAS,CAAC,CAACY,QAAQ,CAAC,KAAKP,SAAS,EAAE;MACnE,MAAMW,KAAK,GAAG7B,eAAe,CAACI,YAAY,CAACS,SAAS,CAAC,CAACY,QAAQ,CAAC;MAC/D;MACA,IAAI,CAACvG,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAEc,KAAK,CAAC;MAC/B,OAAOA,KAAK;IACd;;IAEA;IACA,MAAM3C,WAAW,GAAG,MAAM,IAAI,CAACF,kBAAkB,CAAC6B,SAAS,CAAC;;IAE5D;IACA,MAAMX,MAAM,GAAGF,eAAe,CAACE,MAAM;;IAErC;IACA,IAAIhB,WAAW,CAAC1B,MAAM,IAClB0B,WAAW,CAAC1B,MAAM,CAAC0C,MAAM,CAAC,IAC1BhB,WAAW,CAAC1B,MAAM,CAAC0C,MAAM,CAAC,CAACuB,QAAQ,CAAC,KAAKP,SAAS,EAAE;MACtD,MAAMW,KAAK,GAAG3C,WAAW,CAAC1B,MAAM,CAAC0C,MAAM,CAAC,CAACuB,QAAQ,CAAC;MAClD;MACA,IAAI,CAACvG,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAEc,KAAK,CAAC;MAC/B,OAAOA,KAAK;IACd;;IAEA;IACA,IAAI,CAAC3G,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAE,IAAI,CAAC;IAC9B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,MAAMe,iBAAiBA,CAAChC,MAAM,EAAEe,SAAS,EAAEkB,QAAQ,GAAG,CAAC,EAAE;IACvD,MAAMC,GAAG,GAAG,IAAI1B,IAAI,CAAC,CAAC;IACtB,MAAM2B,IAAI,GAAGD,GAAG,CAACzB,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE9C;IACA,MAAMC,YAAY,GAAG,MAAM,IAAI,CAACrF,QAAQ,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;;IAE/D;IACA,IAAImH,WAAW,GAAGD,YAAY,CAAChD,IAAI,CAACkD,CAAC,IACnCA,CAAC,CAACvC,MAAM,KAAKA,MAAM,IACnBuC,CAAC,CAACxB,SAAS,KAAKA,SAAS,IACzBwB,CAAC,CAACJ,IAAI,KAAKA,IACb,CAAC;IAED,IAAI,CAACG,WAAW,EAAE;MAChB;MACAA,WAAW,GAAG;QACZtC,MAAM;QACNe,SAAS;QACToB,IAAI;QACJF,QAAQ,EAAE,CAAC;QACX1B,OAAO,EAAE2B,GAAG,CAACzB,WAAW,CAAC,CAAC;QAC1BC,OAAO,EAAEwB,GAAG,CAACzB,WAAW,CAAC;MAC3B,CAAC;MAED4B,YAAY,CAAC1B,IAAI,CAAC2B,WAAW,CAAC;IAChC;;IAEA;IACAA,WAAW,CAACL,QAAQ,IAAIA,QAAQ;IAChCK,WAAW,CAAC5B,OAAO,GAAGwB,GAAG,CAACzB,WAAW,CAAC,CAAC;;IAEvC;IACA,MAAM,IAAI,CAACrD,QAAQ,CAAC,IAAI,CAACjC,gBAAgB,EAAEkH,YAAY,CAAC;IAExD,OAAOC,WAAW;EACpB;;EAEA;AACF;AACA;EACE,MAAME,sBAAsBA,CAACxC,MAAM,EAAEe,SAAS,EAAE0B,SAAS,EAAEC,OAAO,EAAE;IAClE;IACA,MAAML,YAAY,GAAG,MAAM,IAAI,CAACrF,QAAQ,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;;IAE/D;IACA,IAAIwH,aAAa,GAAGN,YAAY,CAACO,MAAM,CAACL,CAAC,IAAIA,CAAC,CAACvC,MAAM,KAAKA,MAAM,CAAC;IAEjE,IAAIe,SAAS,EAAE;MACb4B,aAAa,GAAGA,aAAa,CAACC,MAAM,CAACL,CAAC,IAAIA,CAAC,CAACxB,SAAS,KAAKA,SAAS,CAAC;IACtE;IAEA,IAAI0B,SAAS,EAAE;MACbE,aAAa,GAAGA,aAAa,CAACC,MAAM,CAACL,CAAC,IAAIA,CAAC,CAACJ,IAAI,IAAIM,SAAS,CAAC;IAChE;IAEA,IAAIC,OAAO,EAAE;MACXC,aAAa,GAAGA,aAAa,CAACC,MAAM,CAACL,CAAC,IAAIA,CAAC,CAACJ,IAAI,IAAIO,OAAO,CAAC;IAC9D;IAEA,OAAOC,aAAa;EACtB;;EAEA;AACF;AACA;EACE,MAAME,sBAAsBA,CAAC7C,MAAM,EAAEe,SAAS,EAAEY,QAAQ,EAAE;IACxD;IACA,MAAMI,KAAK,GAAG,MAAM,IAAI,CAACL,eAAe,CAAC1B,MAAM,EAAEe,SAAS,EAAEY,QAAQ,CAAC;;IAErE;IACA,IAAII,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAClC,OAAO,KAAK;IACd;;IAEA;IACA,MAAMG,GAAG,GAAG,IAAI1B,IAAI,CAAC,CAAC;IACtB,MAAMsC,KAAK,GAAGZ,GAAG,CAACzB,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE/C;IACA,IAAIT,QAAQ,CAACH,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,MAAMuB,KAAK,GAAG,MAAM,IAAI,CAACP,sBAAsB,CAACxC,MAAM,EAAEe,SAAS,EAAE+B,KAAK,EAAEA,KAAK,CAAC;MAChF,MAAME,UAAU,GAAGD,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAAClB,QAAQ,EAAE,CAAC,CAAC;MAC1E,OAAOe,UAAU,IAAIjB,KAAK;IAC5B;;IAEA;IACA;IACA;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACE,MAAMqB,wBAAwBA,CAACpD,MAAM,EAAEgB,QAAQ,GAAG,IAAI,EAAE;IACtD;IACA,MAAMC,QAAQ,GAAGD,QAAQ,GACvB,wBAAwBA,QAAQ,IAAIhB,MAAM,EAAE,GAC5C,iBAAiBA,MAAM,EAAE;IAE3B,MAAMqD,cAAc,GAAG,IAAI,CAACjI,KAAK,CAAC+F,GAAG,CAACF,QAAQ,CAAC;IAE/C,IAAIoC,cAAc,EAAE;MAClB,OAAOA,cAAc;IACvB;;IAEA;IACA,IAAIC,cAAc,GAAG,EAAE;IACvB,IAAItC,QAAQ,EAAE;MACZsC,cAAc,GAAG,MAAM,IAAI,CAAC/H,eAAe,CAACgI,0BAA0B,CAACvC,QAAQ,CAAC;IAClF;;IAEA;IACA,MAAMd,eAAe,GAAG,MAAM,IAAI,CAACH,kBAAkB,CAACC,MAAM,CAAC;;IAE7D;IACA,MAAMH,gBAAgB,GAAG,MAAM,IAAI,CAACF,uBAAuB,CAACO,eAAe,CAACE,MAAM,CAAC;;IAEnF;IACA,MAAMjB,YAAY,GAAG,MAAM,IAAI,CAACF,kBAAkB,CAAC,CAAC;;IAEpD;IACA,MAAMuE,iBAAiB,GAAGrE,YAAY,CAACyD,MAAM,CAACa,OAAO,IACnDA,OAAO,CAAChG,OAAO,KACboC,gBAAgB,CAACb,QAAQ,CAACwC,QAAQ,CAACiC,OAAO,CAACpG,EAAE,CAAC,IAC9C6C,eAAe,CAACG,cAAc,CAACmB,QAAQ,CAACiC,OAAO,CAACpG,EAAE,CAAC,IACnDiG,cAAc,CAAC9B,QAAQ,CAACiC,OAAO,CAACpG,EAAE,CAAC,CAEvC,CAAC;;IAED;IACA,IAAI,CAACjC,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAEuC,iBAAiB,CAAC;IAE3C,OAAOA,iBAAiB;EAC1B;;EAEA;AACF;AACA;EACE,MAAME,gBAAgBA,CAAC1C,QAAQ,EAAE;IAC/B;IACA,MAAMC,QAAQ,GAAG,kBAAkBD,QAAQ,EAAE;IAC7C,MAAM2C,aAAa,GAAG,IAAI,CAACvI,KAAK,CAAC+F,GAAG,CAACF,QAAQ,CAAC;IAE9C,IAAI0C,aAAa,EAAE;MACjB,OAAOA,aAAa;IACtB;;IAEA;IACA,MAAMC,OAAO,GAAG,MAAM,IAAI,CAACrI,eAAe,CAACsI,gBAAgB,CAAC7C,QAAQ,CAAC;;IAErE;IACA,MAAM8C,cAAc,GAAG,MAAM,IAAI,CAACvI,eAAe,CAACwI,cAAc,CAACH,OAAO,CAACI,SAAS,CAAC;;IAEnF;IACA,IAAI,CAAC5I,KAAK,CAACmG,GAAG,CAACN,QAAQ,EAAE6C,cAAc,CAAC;IAExC,OAAOA,cAAc;EACvB;;EAEA;AACF;AACA;EACE,MAAMG,gBAAgBA,CAACjD,QAAQ,EAAEgD,SAAS,EAAE3D,cAAc,GAAG,EAAE,EAAEC,YAAY,GAAG,CAAC,CAAC,EAAE;IAClF;IACA,MAAMsD,OAAO,GAAG,MAAM,IAAI,CAACrI,eAAe,CAAC2I,gBAAgB,CAAClD,QAAQ,EAAEgD,SAAS,EAAE3D,cAAc,EAAEC,YAAY,CAAC;;IAE9G;IACA,IAAI,CAAClF,KAAK,CAAC+I,GAAG,CAAC,kBAAkBnD,QAAQ,EAAE,CAAC;IAC5C,IAAI,CAAC5F,KAAK,CAAC+I,GAAG,CAAC,wBAAwBnD,QAAQ,IAAI,CAAC;IAEpD,OAAO4C,OAAO;EAChB;;EAEA;AACF;AACA;EACE,MAAMQ,cAAcA,CAAA,EAAG;IACrB,OAAO,MAAM,IAAI,CAAC7I,eAAe,CAAC6I,cAAc,CAAC,CAAC;EACpD;;EAEA;AACF;AACA;EACE,MAAML,cAAcA,CAAC1G,EAAE,EAAE;IACvB,OAAO,MAAM,IAAI,CAAC9B,eAAe,CAACwI,cAAc,CAAC1G,EAAE,CAAC;EACtD;;EAEA;AACF;AACA;EACE,MAAMgH,aAAaA,CAACC,WAAW,EAAE;IAC/B,OAAO,MAAM,IAAI,CAAC/I,eAAe,CAAC8I,aAAa,CAACC,WAAW,CAAC;EAC9D;;EAEA;AACF;AACA;EACE,MAAMC,aAAaA,CAAClH,EAAE,EAAEiH,WAAW,EAAE;IACnC,OAAO,MAAM,IAAI,CAAC/I,eAAe,CAACgJ,aAAa,CAAClH,EAAE,EAAEiH,WAAW,CAAC;EAClE;;EAEA;AACF;AACA;EACE,MAAME,aAAaA,CAACnH,EAAE,EAAE;IACtB,OAAO,MAAM,IAAI,CAAC9B,eAAe,CAACiJ,aAAa,CAACnH,EAAE,CAAC;EACrD;;EAEA;AACF;AACA;EACEoH,UAAUA,CAAA,EAAG;IACX,IAAI,CAACrJ,KAAK,CAACsJ,QAAQ,CAAC,CAAC;IACrB,IAAI,CAACnJ,eAAe,CAACkJ,UAAU,CAAC,CAAC;EACnC;;EAEA;AACF;AACA;EACE,MAAME,0BAA0BA,CAAC3E,MAAM,EAAE;IACvC;IACA,MAAME,eAAe,GAAG,MAAM,IAAI,CAACH,kBAAkB,CAACC,MAAM,CAAC;;IAE7D;IACA,MAAMH,gBAAgB,GAAG,MAAM,IAAI,CAACF,uBAAuB,CAACO,eAAe,CAACE,MAAM,CAAC;;IAEnF;IACA,MAAMoD,iBAAiB,GAAG,MAAM,IAAI,CAACJ,wBAAwB,CAACpD,MAAM,CAAC;;IAErE;IACA,MAAMkC,GAAG,GAAG,IAAI1B,IAAI,CAAC,CAAC;IACtB,MAAMsC,KAAK,GAAGZ,GAAG,CAACzB,WAAW,CAAC,CAAC,CAAC2B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,MAAMW,KAAK,GAAG,MAAM,IAAI,CAACP,sBAAsB,CAACxC,MAAM,EAAE,IAAI,EAAE8C,KAAK,EAAEA,KAAK,CAAC;;IAE3E;IACA,OAAO;MACL9C,MAAM;MACN4E,IAAI,EAAE;QACJvH,EAAE,EAAEwC,gBAAgB,CAACxC,EAAE;QACvBC,IAAI,EAAEuC,gBAAgB,CAACvC,IAAI;QAC3BC,WAAW,EAAEsC,gBAAgB,CAACtC,WAAW;QACzCwB,KAAK,EAAEc,gBAAgB,CAACd;MAC1B,CAAC;MACDC,QAAQ,EAAEwE,iBAAiB,CAACqB,GAAG,CAACpB,OAAO,KAAK;QAC1CpG,EAAE,EAAEoG,OAAO,CAACpG,EAAE;QACdC,IAAI,EAAEmG,OAAO,CAACnG,IAAI;QAClBC,WAAW,EAAEkG,OAAO,CAAClG,WAAW;QAChCC,QAAQ,EAAEiG,OAAO,CAACjG;MACpB,CAAC,CAAC,CAAC;MACHE,MAAM,EAAEmC,gBAAgB,CAACnC,MAAM;MAC/B2C,cAAc,EAAEH,eAAe,CAACG,cAAc;MAC9CC,YAAY,EAAEJ,eAAe,CAACI,YAAY;MAC1CyC,KAAK,EAAEA,KAAK,CAACE,MAAM,CAAC,CAAC6B,MAAM,EAAE3B,MAAM,KAAK;QACtC2B,MAAM,CAAC3B,MAAM,CAACpC,SAAS,CAAC,GAAG,CAAC+D,MAAM,CAAC3B,MAAM,CAACpC,SAAS,CAAC,IAAI,CAAC,IAAIoC,MAAM,CAAClB,QAAQ;QAC5E,OAAO6C,MAAM;MACf,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;EACH;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGtK,kBAAkB", "ignoreList": []}