/**
 * NovaConnect Performance Tests - Data Transformation
 * 
 * These tests validate the performance of the data transformation engine
 * under various load conditions.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  transformUrl: '/api/transform'
};

// Test data
const smallPayload = {
  source: {
    id: 'test-1',
    name: 'Test Finding',
    severity: 'HIGH',
    category: 'VULNERABILITY',
    createTime: '2023-01-01T00:00:00Z',
    resource: {
      name: 'projects/test-project/instances/test-instance',
      type: 'google.compute.Instance'
    }
  },
  transformationRules: [
    { source: 'source.id', target: 'id' },
    { source: 'source.name', target: 'title' },
    { source: 'source.severity', target: 'severity', transform: 'lowercase' },
    { source: 'source.category', target: 'type', transform: 'lowercase' },
    { source: 'source.createTime', target: 'createdAt', transform: 'isoToUnix' },
    { source: 'source.resource.name', target: 'resourceName' },
    { source: 'source.resource.type', target: 'resourceType' }
  ]
};

// Generate a large payload with many nested objects and arrays
const generateLargePayload = (size) => {
  const source = {
    findings: []
  };
  
  for (let i = 0; i < size; i++) {
    source.findings.push({
      id: `finding-${i}`,
      name: `Finding ${i}`,
      severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],
      category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      resource: {
        name: `projects/test-project/instances/instance-${i}`,
        type: 'google.compute.Instance',
        labels: {
          environment: ['production', 'staging', 'development'][i % 3],
          department: ['security', 'compliance', 'operations'][i % 3]
        },
        tags: [
          `tag-${i}-1`,
          `tag-${i}-2`,
          `tag-${i}-3`
        ]
      },
      sourceProperties: {
        property1: `value-${i}-1`,
        property2: `value-${i}-2`,
        property3: `value-${i}-3`,
        nestedProperty: {
          subProperty1: `sub-value-${i}-1`,
          subProperty2: `sub-value-${i}-2`,
          subProperty3: `sub-value-${i}-3`
        }
      }
    });
  }
  
  // Create complex transformation rules
  const transformationRules = [
    { source: 'findings[*].id', target: 'items[*].id' },
    { source: 'findings[*].name', target: 'items[*].title' },
    { source: 'findings[*].severity', target: 'items[*].severity', transform: 'lowercase' },
    { source: 'findings[*].category', target: 'items[*].type', transform: 'lowercase' },
    { source: 'findings[*].createTime', target: 'items[*].createdAt', transform: 'isoToUnix' },
    { source: 'findings[*].updateTime', target: 'items[*].updatedAt', transform: 'isoToUnix' },
    { source: 'findings[*].resource.name', target: 'items[*].resourceName' },
    { source: 'findings[*].resource.type', target: 'items[*].resourceType' },
    { source: 'findings[*].resource.labels', target: 'items[*].labels' },
    { source: 'findings[*].resource.tags', target: 'items[*].tags' },
    { source: 'findings[*].sourceProperties', target: 'items[*].properties' },
    { source: 'findings[*].sourceProperties.nestedProperty', target: 'items[*].nestedProperties' }
  ];
  
  return { source, transformationRules };
};

describe('Data Transformation Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(60000);
  
  // Test simple transformation performance
  describe('Simple Transformation Performance', () => {
    it('should transform a small payload within acceptable time', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.transformUrl}`, smallPayload);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('id', 'test-1');
      expect(result.data).toHaveProperty('title', 'Test Finding');
      expect(result.data).toHaveProperty('severity', 'high');
      expect(duration).toBeLessThan(100); // Should transform in less than 100ms
      
      console.log(`Simple transformation completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test complex transformation performance
  describe('Complex Transformation Performance', () => {
    it('should transform a large payload (100 items) within acceptable time', async () => {
      const largePayload = generateLargePayload(100);
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.transformUrl}`, largePayload);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('items');
      expect(result.data.items).toHaveLength(100);
      expect(duration).toBeLessThan(500); // Should transform in less than 500ms
      
      console.log(`Complex transformation (100 items) completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should transform a very large payload (1000 items) within acceptable time', async () => {
      const veryLargePayload = generateLargePayload(1000);
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.transformUrl}`, veryLargePayload);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('items');
      expect(result.data.items).toHaveLength(1000);
      expect(duration).toBeLessThan(2000); // Should transform in less than 2 seconds
      
      console.log(`Complex transformation (1000 items) completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test concurrent transformation performance
  describe('Concurrent Transformation Performance', () => {
    it('should handle 10 concurrent transformations within acceptable time', async () => {
      const concurrentRequests = 10;
      const transformationFn = () => axios.post(`${config.baseUrl}${config.transformUrl}`, smallPayload);
      
      const { result, duration } = await measureExecutionTime(async () => {
        const promises = Array(concurrentRequests).fill(0).map(() => transformationFn());
        return await Promise.all(promises);
      });
      
      expect(result).toHaveLength(concurrentRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('id', 'test-1');
      });
      
      // Average time per transformation should be less than 50ms
      const averageTime = duration / concurrentRequests;
      expect(averageTime).toBeLessThan(50);
      
      console.log(`${concurrentRequests} concurrent transformations completed in ${duration.toFixed(2)} ms`);
      console.log(`Average time per transformation: ${averageTime.toFixed(2)} ms`);
    });
  });
  
  // Test transformation throughput
  describe('Transformation Throughput', () => {
    it('should achieve acceptable throughput for sequential transformations', async () => {
      const totalRequests = 100;
      const transformationFn = () => axios.post(`${config.baseUrl}${config.transformUrl}`, smallPayload);
      
      const { result, duration } = await measureExecutionTime(async () => {
        const results = [];
        for (let i = 0; i < totalRequests; i++) {
          results.push(await transformationFn());
        }
        return results;
      });
      
      expect(result).toHaveLength(totalRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('id', 'test-1');
      });
      
      // Calculate throughput (transformations per second)
      const throughput = (totalRequests / duration) * 1000;
      
      // Throughput should be at least 20 transformations per second
      expect(throughput).toBeGreaterThan(20);
      
      console.log(`${totalRequests} sequential transformations completed in ${duration.toFixed(2)} ms`);
      console.log(`Throughput: ${throughput.toFixed(2)} transformations/second`);
    });
  });
});
