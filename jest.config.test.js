module.exports = {
  testEnvironment: 'node',
  testMatch: ['**/*.test.js'],
  collectCoverage: true,
  coverageReporters: ['text', 'lcov', 'html', 'json-summary'],
  coverageThreshold: {
    global: {
      branches: 96,
      functions: 96,
      lines: 96,
      statements: 96
    }
  },
  testPathIgnorePatterns: ['/node_modules/'],
  setupFilesAfterEnv: ['./tests/setup.js'],
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: './test-results',
      outputName: 'junit.xml',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}',
      ancestorSeparator: ' › ',
      suiteNameTemplate: '{filename}',
      includeConsoleOutput: true,
      reportTestSuiteErrors: true
    }],
    ['./tests/reporters/custom-reporter.js', {
      outputFile: './test-results/test-report.md',
      includePerformanceMetrics: true
    }]
  ],
  testTimeout: 30000,
  verbose: true
};
