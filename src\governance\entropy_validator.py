"""
Enhanced Entropy Validator for C-AIaaS

Implements quantum-corrected entropy validation with dynamic thresholds
using NovaConnect and NovaShield integrations.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass, asdict
from enum import Enum

from .quantum_utils import quantum_engine, QuantumEntropyProfile
from .novashield import novashield, RiskLevel, ComplianceLevel

logger = logging.getLogger(__name__)

class DecisionType(str, Enum):
    """Possible decision outcomes for task evaluation"""
    AUTO_APPROVED = "Auto-Approved"
    REQUIRES_APPROVAL = "Requires Approval"
    ESCALATE = "Escalate"
    REJECTED = "Rejected"

@dataclass
class Decision:
    """Represents a governance decision with quantum corrections"""
    task_id: str
    entropy_value: float
    threshold: float
    q_factor: float
    decision: DecisionType
    entropy_signature: str
    risk_level: str = RiskLevel.STANDARD
    compliance_level: str = ComplianceLevel.STANDARD
    quantum_confidence: Tuple[float, float] = (0.0, 0.0)
    is_recurring: bool = False
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert decision to dictionary for serialization"""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        result['quantum_confidence'] = list(self.quantum_confidence)
        return result

class EntropyValidator:
    """
    Enhanced entropy validator with quantum corrections and dynamic thresholds.
    
    Implements the ∂Ψ=0 coherence protocol with:
    - Quantum-corrected entropy calculations
    - Dynamic threshold management via NovaShield
    - Compliance validation and risk assessment
    - Pattern detection for recurring decisions
    """
    
    def __init__(self, supabase=None):
        """Initialize the validator with optional Supabase client"""
        self.supabase = supabase
        self.decision_cache: Dict[str, Decision] = {}
        
    async def calculate_entropy(
        self, 
        task: Dict[str, Any],
        vendor_qscore: float
    ) -> Tuple[float, QuantumEntropyProfile]:
        """
        Calculate quantum-corrected entropy for a task.
        
        Args:
            task: Task data including type, budget, deadline, etc.
            vendor_qscore: Vendor's current Q-Score
            
        Returns:
            Tuple of (entropy_value, quantum_profile)
        """
        # Base entropy calculation (classical component)
        time = max(task.get('deadline_hours', 1), 1)  # Prevent division by zero
        energy = max(min(vendor_qscore, 10.0), 0.1)  # Clamp between 0.1 and 10.0
        base_entropy = (task['budget'] / (time * (energy + 1))) * 100
        
        # Apply quantum corrections
        quantum_profile = await quantum_engine.calculate_quantum_entropy(
            base_entropy=base_entropy,
            vendor_id=task['vendor_id'],
            task_type=task['type'],
            timestamp=datetime.utcnow()
        )
        
        return quantum_profile.adjusted_entropy, quantum_profile
    
    async def evaluate_task(self, task: Dict[str, Any]) -> Decision:
        """
        Evaluate a task using quantum-corrected entropy and dynamic thresholds.
        
        Args:
            task: Task data including vendor_id, role_id, type, budget, etc.
            
        Returns:
            Decision object with evaluation results
        """
        # Verify NovaDNA signature if present
        if not await self._verify_novadna(task):
            return self._create_decision(
                task_id=task['id'],
                entropy_value=0,
                threshold=0,
                q_factor=0,
                decision=DecisionType.REJECTED,
                risk_level=RiskLevel.CRITICAL,
                reason="Invalid NovaDNA signature"
            )
        
        try:
            # Get vendor Q-Score (from cache or database)
            vendor_qscore = await self._get_vendor_qscore(task['vendor_id'])
            
            # Calculate quantum-corrected entropy
            entropy, quantum_profile = await self.calculate_entropy(task, vendor_qscore)
            
            # Get risk profile and dynamic threshold
            risk_level = await novashield.get_risk_profile(task['type'], vendor_qscore)
            compliance_level = ComplianceLevel(task.get('compliance_level', ComplianceLevel.STANDARD))
            threshold = await novashield.get_dynamic_threshold(risk_level, compliance_level)
            
            # Make decision based on entropy and thresholds
            decision = self._make_decision(
                task=task,
                entropy=entropy,
                threshold=threshold,
                q_factor=quantum_profile.quantum_factor,
                risk_level=risk_level,
                compliance_level=compliance_level,
                quantum_confidence=quantum_profile.confidence_interval
            )
            
            # Log decision for pattern detection
            await self._log_decision(decision)
            
            return decision
            
        except Exception as e:
            logger.error(f"Error evaluating task {task.get('id', 'unknown')}: {str(e)}", exc_info=True)
            # Fail safe: require manual review on error
            return self._create_decision(
                task_id=task.get('id', 'unknown'),
                entropy_value=0,
                threshold=0,
                q_factor=0,
                decision=DecisionType.ESCALATE,
                risk_level=RiskLevel.CRITICAL,
                reason=f"Evaluation error: {str(e)}"
            )
    
    def _make_decision(
        self,
        task: Dict[str, Any],
        entropy: float,
        threshold: float,
        q_factor: float,
        risk_level: RiskLevel,
        compliance_level: ComplianceLevel,
        quantum_confidence: Tuple[float, float],
        reason: str = None
    ) -> Decision:
        """Make a decision based on entropy and thresholds"""
        # Check for extreme entropy values
        if entropy > threshold * 1.5:
            decision_type = DecisionType.ESCALATE
        # Check for auto-approval conditions
        elif (entropy <= threshold and 
              task.get('budget', 0) <= task.get('role_spend_limit', float('inf')) and 
              task.get('vendor_score', 0) >= task.get('role_q_threshold', 7.0)):
            decision_type = DecisionType.AUTO_APPROVED
        # Default to requiring approval
        else:
            decision_type = DecisionType.REQUIRES_APPROVAL
        
        # Generate entropy signature for pattern detection
        entropy_sig = self._generate_signature(task, entropy, q_factor)
        
        # Check for recurring patterns
        is_recurring = entropy_sig in self.decision_cache
        
        # Create decision object
        return Decision(
            task_id=task['id'],
            entropy_value=entropy,
            threshold=threshold,
            q_factor=q_factor,
            decision=decision_type,
            entropy_signature=entropy_sig,
            risk_level=risk_level.value,
            compliance_level=compliance_level.value,
            quantum_confidence=quantum_confidence,
            is_recurring=is_recurring,
            metadata={
                'reason': reason,
                'task_type': task.get('type'),
                'vendor_id': task.get('vendor_id'),
                'role_id': task.get('role_id')
            }
        )
    
    async def _verify_novadna(self, task: Dict[str, Any]) -> bool:
        """Verify NovaDNA signature if present"""
        if 'signature' not in task:
            return False
            
        try:
            if self.supabase:
                result = await self.supabase.rpc('verify_quantum_signature', {
                    'dna': task['signature'],
                    'entropy_profile': task.get('entropy_profile', {})
                }).execute()
                return result.data.get('valid', False)
            return True  # Skip verification if no Supabase client
        except Exception as e:
            logger.warning(f"NovaDNA verification failed: {e}")
            return False
    
    async def _get_vendor_qscore(self, vendor_id: str) -> float:
        """Get vendor Q-Score from cache or database"""
        # In a real implementation, this would check cache first, then database
        if self.supabase:
            try:
                result = await self.supabase.table('vendors') \
                    .select('current_q_score') \
                    .eq('id', vendor_id) \
                    .single() \
                    .execute()
                return float(result.data.get('current_q_score', 5.0))
            except Exception as e:
                logger.warning(f"Error fetching Q-Score for vendor {vendor_id}: {e}")
        
        return 5.0  # Default neutral score
    
    def _generate_signature(self, task: Dict[str, Any], entropy: float, q_factor: float) -> str:
        """Generate a unique signature for entropy patterns"""
        import hashlib
        sig_data = f"{task['type']}:{entropy:.2f}:{q_factor:.2f}:{task.get('vendor_id', '')}"
        return hashlib.sha256(sig_data.encode()).hexdigest()
    
    async def _log_decision(self, decision: Decision) -> None:
        """Log decision to cache and optionally to database"""
        # Update cache
        self.decision_cache[decision.entropy_signature] = decision
        
        # Log to database if Supabase is available
        if self.supabase:
            try:
                await self.supabase.table('decisions').insert(decision.to_dict()).execute()
            except Exception as e:
                logger.error(f"Error logging decision: {e}")
    
    @staticmethod
    def _create_decision(
        task_id: str,
        entropy_value: float,
        threshold: float,
        q_factor: float,
        decision: DecisionType,
        risk_level: RiskLevel,
        reason: str = None
    ) -> Decision:
        """Helper to create a decision with error handling"""
        return Decision(
            task_id=task_id,
            entropy_value=entropy_value,
            threshold=threshold,
            q_factor=q_factor,
            decision=decision,
            entropy_signature="",
            risk_level=risk_level.value,
            compliance_level=ComplianceLevel.STANDARD.value,
            metadata={'reason': reason} if reason else {}
        )

# Singleton instance for easy import
validator = EntropyValidator()
