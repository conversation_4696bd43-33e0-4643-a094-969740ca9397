import { useState } from 'react'
import { motion } from 'framer-motion'
import { ProductScanner } from '@/components/ProductScanner'
import { PerformanceMetrics } from '@/components/PerformanceMetrics'
import { TriadicMetrics } from '@/components/TriadicMetrics'
import { ConsciousnessMonitor } from '@/components/ConsciousnessMonitor'

export default function Home() {
  const [products, setProducts] = useState([])
  const [stats, setStats] = useState({
    conversionRate: 0,
    monthlySales: 0,
    totalRevenue: 0
  })

  const handleProductFound = (product: any) => {
    // Update stats based on new product
    setStats(prev => ({
      ...prev,
      monthlySales: prev.monthlySales + 1,
      totalRevenue: prev.totalRevenue + product.price
    }))
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold mb-4">Product Scanner</h2>
        <ProductScanner 
          onProductFound={handleProductFound}
        />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold mb-4">Performance Metrics</h2>
        <PerformanceMetrics stats={stats} products={products} />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold mb-4">Consciousness Monitor</h2>
        <ConsciousnessMonitor />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white/5 backdrop-blur-lg rounded-2xl p-6 border border-white/10"
      >
        <h2 className="text-xl font-bold mb-4">Triadic Impact</h2>
        <TriadicMetrics />
      </motion.div>
    </div>
  )
}
