#!/bin/bash
# NovaConnect UAC Tenant Deprovisioning Script

# Set variables
TENANT_ID=$1
BACKUP=${2:-"true"}
PROJECT_ID=${PROJECT_ID:-"novafuse-marketplace"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Check if tenant ID is provided
if [ -z "$TENANT_ID" ]; then
  echo -e "${RED}Error: Tenant ID is required.${NC}"
  echo "Usage: $0 <tenant_id> [backup=true|false]"
  exit 1
fi

# Confirm deprovisioning
read -p "Are you sure you want to deprovision tenant ${TENANT_ID}? This action cannot be undone. (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo -e "${YELLOW}Deprovisioning cancelled.${NC}"
  exit 0
fi

# Backup tenant data if requested
if [ "$BACKUP" = "true" ]; then
  echo -e "${YELLOW}Backing up tenant data...${NC}"
  
  # Create backup directory
  BACKUP_DIR="backups/tenant-${TENANT_ID}-$(date +%Y%m%d%H%M%S)"
  mkdir -p ${BACKUP_DIR}
  
  # Export Kubernetes resources
  kubectl get all -n tenant-${TENANT_ID} -o yaml > ${BACKUP_DIR}/kubernetes-resources.yaml
  
  # Export BigQuery data
  bq extract --destination_format=NEWLINE_DELIMITED_JSON \
    ${PROJECT_ID}:tenant_${TENANT_ID}.* \
    gs://${PROJECT_ID}-backups/tenant-${TENANT_ID}/$(date +%Y%m%d)/
  
  echo -e "${GREEN}Tenant data backed up to ${BACKUP_DIR} and GCS.${NC}"
fi

# Delete Helm release
echo -e "${YELLOW}Deleting Helm release for tenant ${TENANT_ID}...${NC}"
helm uninstall tenant-${TENANT_ID} --namespace tenant-${TENANT_ID}

# Delete namespace
echo -e "${YELLOW}Deleting namespace for tenant ${TENANT_ID}...${NC}"
kubectl delete namespace tenant-${TENANT_ID}

# Delete service account
echo -e "${YELLOW}Deleting service account for tenant ${TENANT_ID}...${NC}"
gcloud iam service-accounts delete tenant-${TENANT_ID}@${PROJECT_ID}.iam.gserviceaccount.com --quiet

# Delete encryption key
echo -e "${YELLOW}Deleting encryption key for tenant ${TENANT_ID}...${NC}"
gcloud kms keys versions list \
  --location=global \
  --keyring=tenant-${TENANT_ID}-keyring \
  --key=tenant-${TENANT_ID}-key \
  --format="value(name)" | xargs -I{} gcloud kms keys versions destroy {} \
  --location=global \
  --keyring=tenant-${TENANT_ID}-keyring \
  --key=tenant-${TENANT_ID}-key

gcloud kms keys delete tenant-${TENANT_ID}-key \
  --location=global \
  --keyring=tenant-${TENANT_ID}-keyring \
  --quiet

gcloud kms keyrings delete tenant-${TENANT_ID}-keyring \
  --location=global \
  --quiet

# Delete BigQuery dataset (with caution)
echo -e "${YELLOW}Deleting BigQuery dataset for tenant ${TENANT_ID}...${NC}"
bq rm -f -r ${PROJECT_ID}:tenant_${TENANT_ID}

# Delete monitoring dashboard
echo -e "${YELLOW}Deleting monitoring dashboard for tenant ${TENANT_ID}...${NC}"
rm -f monitoring/dashboards/tenant-${TENANT_ID}-dashboard.json

# Delete tenant-specific values file
echo -e "${YELLOW}Deleting tenant-specific values file...${NC}"
rm -f marketplace/chart/values-tenant-${TENANT_ID}.yaml

echo -e "${GREEN}Tenant ${TENANT_ID} deprovisioned successfully!${NC}"

exit 0
