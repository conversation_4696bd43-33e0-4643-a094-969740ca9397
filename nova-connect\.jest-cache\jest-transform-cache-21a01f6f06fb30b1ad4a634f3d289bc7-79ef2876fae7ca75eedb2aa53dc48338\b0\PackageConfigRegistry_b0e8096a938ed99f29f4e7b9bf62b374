3349e81890f65a65d711fd95a243a279
/**
 * Package Configuration Registry
 * 
 * This service manages the package configuration registry for NovaConnect UAC.
 * It provides functionality for defining feature sets by package and mapping tenants to packages.
 */

const fs = require('fs').promises;
const path = require('path');
const {
  v4: uuidv4
} = require('uuid');
const NodeCache = require('node-cache');
class PackageConfigRegistry {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.packagesDir = path.join(this.dataDir, 'packages');
    this.packagesFile = path.join(this.packagesDir, 'packages.json');
    this.tenantMappingFile = path.join(this.packagesDir, 'tenant_package_mapping.json');

    // Initialize cache with 5-minute TTL
    this.cache = new NodeCache({
      stdTTL: 300,
      checkperiod: 60
    });

    // Define package tiers
    this.tiers = {
      CORE: 'core',
      SECURE: 'secure',
      ENTERPRISE: 'enterprise',
      AI_BOOST: 'ai_boost'
    };
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.packagesDir, {
        recursive: true
      });

      // Initialize packages file if it doesn't exist
      try {
        await fs.access(this.packagesFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with default packages
          await fs.writeFile(this.packagesFile, JSON.stringify(this.getDefaultPackages(), null, 2));
        } else {
          throw error;
        }
      }

      // Initialize tenant mapping file if it doesn't exist
      try {
        await fs.access(this.tenantMappingFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with empty array
          await fs.writeFile(this.tenantMappingFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating packages directory:', error);
      throw error;
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get default packages
   */
  getDefaultPackages() {
    return [{
      id: 'core',
      name: 'NovaConnect Core',
      description: 'Basic functionality for connecting APIs and automating workflows',
      tier: this.tiers.CORE,
      features: ['core.basic_connectors', 'core.basic_workflows', 'core.basic_authentication', 'core.basic_monitoring', 'core.basic_logging'],
      limits: {
        connections: 10,
        operations_per_day: 1000,
        workflows: 5,
        actions_per_workflow: 10,
        scheduled_workflows: 2,
        alerts: 5
      }
    }, {
      id: 'secure',
      name: 'NovaConnect Secure',
      description: 'Enhanced security and compliance features',
      tier: this.tiers.SECURE,
      features: ['core.basic_connectors', 'core.basic_workflows', 'core.basic_authentication', 'core.basic_monitoring', 'core.basic_logging', 'security.encryption', 'security.audit_logs', 'security.compliance_checks', 'security.vulnerability_scanning', 'security.access_controls'],
      limits: {
        connections: 25,
        operations_per_day: 5000,
        workflows: 15,
        actions_per_workflow: 20,
        scheduled_workflows: 5,
        alerts: 15
      }
    }, {
      id: 'enterprise',
      name: 'NovaConnect Enterprise',
      description: 'Advanced enterprise features for large-scale deployments',
      tier: this.tiers.ENTERPRISE,
      features: ['core.basic_connectors', 'core.basic_workflows', 'core.basic_authentication', 'core.basic_monitoring', 'core.basic_logging', 'security.encryption', 'security.audit_logs', 'security.compliance_checks', 'security.vulnerability_scanning', 'security.access_controls', 'enterprise.advanced_connectors', 'enterprise.advanced_workflows', 'enterprise.advanced_authentication', 'enterprise.advanced_monitoring', 'enterprise.advanced_logging', 'enterprise.sla', 'enterprise.priority_support'],
      limits: {
        connections: 100,
        operations_per_day: 25000,
        workflows: 50,
        actions_per_workflow: 50,
        scheduled_workflows: 20,
        alerts: 50
      }
    }, {
      id: 'ai_boost',
      name: 'NovaConnect AI Boost',
      description: 'AI-powered features for intelligent automation',
      tier: this.tiers.AI_BOOST,
      features: ['core.basic_connectors', 'core.basic_workflows', 'core.basic_authentication', 'core.basic_monitoring', 'core.basic_logging', 'security.encryption', 'security.audit_logs', 'security.compliance_checks', 'security.vulnerability_scanning', 'security.access_controls', 'enterprise.advanced_connectors', 'enterprise.advanced_workflows', 'enterprise.advanced_authentication', 'enterprise.advanced_monitoring', 'enterprise.advanced_logging', 'enterprise.sla', 'enterprise.priority_support', 'ai.predictive_analytics', 'ai.anomaly_detection', 'ai.natural_language_processing', 'ai.automated_remediation', 'ai.intelligent_recommendations'],
      limits: {
        connections: -1,
        // Unlimited
        operations_per_day: -1,
        // Unlimited
        workflows: -1,
        // Unlimited
        actions_per_workflow: -1,
        // Unlimited
        scheduled_workflows: -1,
        // Unlimited
        alerts: -1 // Unlimited
      }
    }];
  }

  /**
   * Get all packages
   */
  async getAllPackages() {
    // Check cache first
    const cacheKey = 'all_packages';
    const cachedPackages = this.cache.get(cacheKey);
    if (cachedPackages) {
      return cachedPackages;
    }

    // Load from file
    const packages = await this.loadData(this.packagesFile);

    // Cache the result
    this.cache.set(cacheKey, packages);
    return packages;
  }

  /**
   * Get package by ID
   */
  async getPackageById(id) {
    // Check cache first
    const cacheKey = `package_${id}`;
    const cachedPackage = this.cache.get(cacheKey);
    if (cachedPackage) {
      return cachedPackage;
    }

    // Load from file
    const packages = await this.getAllPackages();
    const pkg = packages.find(p => p.id === id);
    if (!pkg) {
      throw new Error(`Package with ID ${id} not found`);
    }

    // Cache the result
    this.cache.set(cacheKey, pkg);
    return pkg;
  }

  /**
   * Create a new package
   */
  async createPackage(packageData) {
    const packages = await this.getAllPackages();

    // Check if package with the same ID already exists
    if (packages.some(p => p.id === packageData.id)) {
      throw new Error(`Package with ID ${packageData.id} already exists`);
    }

    // Add the new package
    packages.push(packageData);

    // Save to file
    await this.saveData(this.packagesFile, packages);

    // Invalidate cache
    this.cache.del('all_packages');
    return packageData;
  }

  /**
   * Update a package
   */
  async updatePackage(id, packageData) {
    const packages = await this.getAllPackages();

    // Find the package
    const index = packages.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error(`Package with ID ${id} not found`);
    }

    // Update the package
    packages[index] = {
      ...packages[index],
      ...packageData
    };

    // Save to file
    await this.saveData(this.packagesFile, packages);

    // Invalidate cache
    this.cache.del('all_packages');
    this.cache.del(`package_${id}`);
    return packages[index];
  }

  /**
   * Delete a package
   */
  async deletePackage(id) {
    const packages = await this.getAllPackages();

    // Find the package
    const index = packages.findIndex(p => p.id === id);
    if (index === -1) {
      throw new Error(`Package with ID ${id} not found`);
    }

    // Remove the package
    packages.splice(index, 1);

    // Save to file
    await this.saveData(this.packagesFile, packages);

    // Invalidate cache
    this.cache.del('all_packages');
    this.cache.del(`package_${id}`);
    return true;
  }

  /**
   * Get all tenant-to-package mappings
   */
  async getAllTenantMappings() {
    // Check cache first
    const cacheKey = 'all_tenant_mappings';
    const cachedMappings = this.cache.get(cacheKey);
    if (cachedMappings) {
      return cachedMappings;
    }

    // Load from file
    const mappings = await this.loadData(this.tenantMappingFile);

    // Cache the result
    this.cache.set(cacheKey, mappings);
    return mappings;
  }

  /**
   * Get tenant-to-package mapping by tenant ID
   */
  async getTenantMapping(tenantId) {
    // Check cache first
    const cacheKey = `tenant_mapping_${tenantId}`;
    const cachedMapping = this.cache.get(cacheKey);
    if (cachedMapping) {
      return cachedMapping;
    }

    // Load from file
    const mappings = await this.getAllTenantMappings();
    const mapping = mappings.find(m => m.tenantId === tenantId);
    if (!mapping) {
      // Return default mapping
      return {
        tenantId,
        packageId: 'core',
        // Default package
        customFeatures: [],
        customLimits: {}
      };
    }

    // Cache the result
    this.cache.set(cacheKey, mapping);
    return mapping;
  }

  /**
   * Create or update tenant-to-package mapping
   */
  async setTenantMapping(tenantId, packageId, customFeatures = [], customLimits = {}) {
    const mappings = await this.getAllTenantMappings();

    // Find the mapping
    const index = mappings.findIndex(m => m.tenantId === tenantId);

    // Create or update the mapping
    const mapping = {
      tenantId,
      packageId,
      customFeatures,
      customLimits
    };
    if (index === -1) {
      // Add new mapping
      mappings.push(mapping);
    } else {
      // Update existing mapping
      mappings[index] = mapping;
    }

    // Save to file
    await this.saveData(this.tenantMappingFile, mappings);

    // Invalidate cache
    this.cache.del('all_tenant_mappings');
    this.cache.del(`tenant_mapping_${tenantId}`);
    return mapping;
  }

  /**
   * Delete tenant-to-package mapping
   */
  async deleteTenantMapping(tenantId) {
    const mappings = await this.getAllTenantMappings();

    // Find the mapping
    const index = mappings.findIndex(m => m.tenantId === tenantId);
    if (index === -1) {
      throw new Error(`Tenant mapping for tenant ${tenantId} not found`);
    }

    // Remove the mapping
    mappings.splice(index, 1);

    // Save to file
    await this.saveData(this.tenantMappingFile, mappings);

    // Invalidate cache
    this.cache.del('all_tenant_mappings');
    this.cache.del(`tenant_mapping_${tenantId}`);
    return true;
  }

  /**
   * Check if tenant has access to feature
   */
  async hasTenantFeatureAccess(tenantId, featureId) {
    // Get tenant mapping
    const mapping = await this.getTenantMapping(tenantId);

    // Check if tenant has custom access to this feature
    if (mapping.customFeatures.includes(featureId)) {
      return true;
    }

    // Get tenant's package
    const pkg = await this.getPackageById(mapping.packageId);

    // Check if feature is included in the package
    return pkg.features.includes(featureId);
  }

  /**
   * Get tenant's feature limit
   */
  async getTenantFeatureLimit(tenantId, limitKey) {
    // Get tenant mapping
    const mapping = await this.getTenantMapping(tenantId);

    // Check if tenant has custom limit
    if (mapping.customLimits[limitKey] !== undefined) {
      return mapping.customLimits[limitKey];
    }

    // Get tenant's package
    const pkg = await this.getPackageById(mapping.packageId);

    // Return package limit
    return pkg.limits[limitKey] !== undefined ? pkg.limits[limitKey] : null;
  }

  /**
   * Get tenant's available features
   */
  async getTenantAvailableFeatures(tenantId) {
    // Get tenant mapping
    const mapping = await this.getTenantMapping(tenantId);

    // Get tenant's package
    const pkg = await this.getPackageById(mapping.packageId);

    // Combine package features and custom features
    const features = [...new Set([...pkg.features, ...mapping.customFeatures])];
    return features;
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.flushAll();
  }
}
module.exports = PackageConfigRegistry;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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