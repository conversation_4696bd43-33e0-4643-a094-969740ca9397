{"id": "apis-ipaas-developer-tools", "name": "APIs, iPaaS & Developer Tools Connector", "description": "Connector for API management, iPaaS, and developer tools platforms", "version": "1.0.0", "category": "apis", "icon": "apis-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/apis", "supportEmail": "<EMAIL>", "authentication": {"type": "api_key", "fields": {"apiKey": {"type": "string", "label": "API Key", "required": true, "sensitive": true, "description": "API Key for authentication"}, "apiKeyHeader": {"type": "string", "label": "API Key Header", "required": false, "sensitive": false, "default": "X-API-Key", "description": "HTTP header name for the API Key"}}}, "endpoints": [{"id": "listApis", "name": "List APIs", "description": "List all APIs", "method": "GET", "url": "https://api.example.com/apis", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["active", "deprecated", "retired", "draft"], "description": "Filter by API status"}, "type": {"type": "string", "label": "Type", "required": false, "enum": ["rest", "soap", "graphql", "grpc", "websocket"], "description": "Filter by API type"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by API status", "enum": ["active", "deprecated", "retired", "draft"]}, "type": {"type": "string", "description": "Filter by API type", "enum": ["rest", "soap", "graphql", "grpc", "websocket"]}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "API ID"}, "name": {"type": "string", "description": "API name"}, "description": {"type": "string", "description": "API description"}, "version": {"type": "string", "description": "API version"}, "type": {"type": "string", "description": "API type", "enum": ["rest", "soap", "graphql", "grpc", "websocket"]}, "status": {"type": "string", "description": "API status", "enum": ["active", "deprecated", "retired", "draft"]}, "baseUrl": {"type": "string", "description": "API base URL"}, "createdAt": {"type": "string", "format": "date-time", "description": "API creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "API last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getApi", "name": "Get API", "description": "Get a specific API", "method": "GET", "url": "https://api.example.com/apis/{apiId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"apiId": {"type": "string", "label": "API ID", "required": true, "description": "ID of the API to retrieve"}}, "inputSchema": {"type": "object", "properties": {"apiId": {"type": "string", "description": "ID of the API to retrieve"}}, "required": ["apiId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "API ID"}, "name": {"type": "string", "description": "API name"}, "description": {"type": "string", "description": "API description"}, "version": {"type": "string", "description": "API version"}, "type": {"type": "string", "description": "API type", "enum": ["rest", "soap", "graphql", "grpc", "websocket"]}, "status": {"type": "string", "description": "API status", "enum": ["active", "deprecated", "retired", "draft"]}, "baseUrl": {"type": "string", "description": "API base URL"}, "documentation": {"type": "string", "description": "API documentation URL"}, "specification": {"type": "object", "description": "API specification (OpenAPI, WSDL, etc.)"}, "endpoints": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Endpoint ID"}, "path": {"type": "string", "description": "Endpoint path"}, "method": {"type": "string", "description": "HTTP method", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]}, "description": {"type": "string", "description": "Endpoint description"}}}}, "security": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Security type", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "oauth2", "basic", "jwt", "none"]}, "description": {"type": "string", "description": "Security description"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "API creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "API last update date"}}}}, {"id": "listIntegrations", "name": "List Integrations", "description": "List all integrations", "method": "GET", "url": "https://api.example.com/integrations", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["active", "inactive", "draft", "error"], "description": "Filter by integration status"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by integration status", "enum": ["active", "inactive", "draft", "error"]}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Integration ID"}, "name": {"type": "string", "description": "Integration name"}, "description": {"type": "string", "description": "Integration description"}, "status": {"type": "string", "description": "Integration status", "enum": ["active", "inactive", "draft", "error"]}, "type": {"type": "string", "description": "Integration type"}, "sourceSystem": {"type": "string", "description": "Source system"}, "targetSystem": {"type": "string", "description": "Target system"}, "lastExecuted": {"type": "string", "format": "date-time", "description": "Last execution time"}, "createdAt": {"type": "string", "format": "date-time", "description": "Integration creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Integration last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getIntegration", "name": "Get Integration", "description": "Get a specific integration", "method": "GET", "url": "https://api.example.com/integrations/{integrationId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"integrationId": {"type": "string", "label": "Integration ID", "required": true, "description": "ID of the integration to retrieve"}}, "inputSchema": {"type": "object", "properties": {"integrationId": {"type": "string", "description": "ID of the integration to retrieve"}}, "required": ["integrationId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Integration ID"}, "name": {"type": "string", "description": "Integration name"}, "description": {"type": "string", "description": "Integration description"}, "status": {"type": "string", "description": "Integration status", "enum": ["active", "inactive", "draft", "error"]}, "type": {"type": "string", "description": "Integration type"}, "sourceSystem": {"type": "object", "properties": {"id": {"type": "string", "description": "Source system ID"}, "name": {"type": "string", "description": "Source system name"}, "type": {"type": "string", "description": "Source system type"}, "connectionDetails": {"type": "object", "description": "Source system connection details"}}}, "targetSystem": {"type": "object", "properties": {"id": {"type": "string", "description": "Target system ID"}, "name": {"type": "string", "description": "Target system name"}, "type": {"type": "string", "description": "Target system type"}, "connectionDetails": {"type": "object", "description": "Target system connection details"}}}, "mappings": {"type": "array", "items": {"type": "object", "properties": {"sourceField": {"type": "string", "description": "Source field"}, "targetField": {"type": "string", "description": "Target field"}, "transformation": {"type": "string", "description": "Transformation logic"}}}}, "schedule": {"type": "object", "properties": {"type": {"type": "string", "description": "Schedule type", "enum": ["manual", "cron", "interval", "event"]}, "expression": {"type": "string", "description": "Schedule expression"}, "timezone": {"type": "string", "description": "Schedule timezone"}}}, "lastExecuted": {"type": "string", "format": "date-time", "description": "Last execution time"}, "executionHistory": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Execution ID"}, "startTime": {"type": "string", "format": "date-time", "description": "Execution start time"}, "endTime": {"type": "string", "format": "date-time", "description": "Execution end time"}, "status": {"type": "string", "description": "Execution status", "enum": ["success", "failure", "in_progress"]}, "recordsProcessed": {"type": "integer", "description": "Number of records processed"}, "errors": {"type": "array", "items": {"type": "string"}, "description": "Execution errors"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Integration creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Integration last update date"}}}}, {"id": "executeIntegration", "name": "Execute Integration", "description": "Execute an integration", "method": "POST", "url": "https://api.example.com/integrations/{integrationId}/execute", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"integrationId": {"type": "string", "label": "Integration ID", "required": true, "description": "ID of the integration to execute"}}, "bodyParameters": {"parameters": {"type": "object", "label": "Parameters", "required": false, "description": "Execution parameters"}, "async": {"type": "boolean", "label": "Async", "required": false, "default": true, "description": "Execute asynchronously"}}, "inputSchema": {"type": "object", "properties": {"integrationId": {"type": "string", "description": "ID of the integration to execute"}, "parameters": {"type": "object", "description": "Execution parameters"}, "async": {"type": "boolean", "description": "Execute asynchronously"}}, "required": ["integrationId"]}, "outputSchema": {"type": "object", "properties": {"executionId": {"type": "string", "description": "Execution ID"}, "status": {"type": "string", "description": "Execution status", "enum": ["queued", "in_progress", "success", "failure"]}, "startTime": {"type": "string", "format": "date-time", "description": "Execution start time"}, "estimatedCompletionTime": {"type": "string", "format": "date-time", "description": "Estimated completion time"}, "statusUrl": {"type": "string", "description": "URL to check execution status"}}}}, {"id": "listDeveloperTools", "name": "List Developer Tools", "description": "List all developer tools", "method": "GET", "url": "https://api.example.com/developer-tools", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "category": {"type": "string", "label": "Category", "required": false, "description": "Filter by tool category"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "category": {"type": "string", "description": "Filter by tool category"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Tool ID"}, "name": {"type": "string", "description": "Tool name"}, "description": {"type": "string", "description": "Tool description"}, "category": {"type": "string", "description": "Tool category"}, "version": {"type": "string", "description": "Tool version"}, "url": {"type": "string", "description": "Tool URL"}, "createdAt": {"type": "string", "format": "date-time", "description": "Tool creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Tool last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}]}