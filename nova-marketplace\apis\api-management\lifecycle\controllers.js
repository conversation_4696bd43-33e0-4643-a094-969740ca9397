const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of APIs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIs = (req, res) => {
  try {
    const { page = 1, limit = 10, status, type, tag, sortBy = 'name', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter APIs based on query parameters
    let filteredAPIs = [...models.apis];
    
    if (status) {
      filteredAPIs = filteredAPIs.filter(api => api.status === status);
    }
    
    if (type) {
      filteredAPIs = filteredAPIs.filter(api => api.type === type);
    }
    
    if (tag) {
      filteredAPIs = filteredAPIs.filter(api => api.tags && api.tags.includes(tag));
    }
    
    // Sort APIs
    filteredAPIs.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedAPIs = filteredAPIs.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalAPIs = filteredAPIs.length;
    const totalPages = Math.ceil(totalAPIs / limitNum);
    
    res.json({
      data: paginatedAPIs,
      pagination: {
        total: totalAPIs,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getAPIs:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific API by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIById = (req, res) => {
  try {
    const { id } = req.params;
    const api = models.apis.find(a => a.id === id);
    
    if (!api) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    res.json({ data: api });
  } catch (error) {
    console.error('Error in getAPIById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createAPI = (req, res) => {
  try {
    const { name, description, version, status, type, owner, team, baseUrl, documentation, repository, tags } = req.body;
    
    // Create a new API with a unique ID
    const newAPI = {
      id: `api-${uuidv4().substring(0, 8)}`,
      name,
      description,
      version,
      status,
      type,
      owner,
      team,
      baseUrl,
      documentation,
      repository,
      tags: tags || [],
      versions: [],
      dependencies: [],
      consumers: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Create an initial version for the API
    const initialVersion = {
      id: `ver-${uuidv4().substring(0, 8)}`,
      apiId: newAPI.id,
      version,
      status,
      releaseDate: status === 'production' ? new Date().toISOString().split('T')[0] : null,
      endOfLifeDate: null,
      changeLog: 'Initial version',
      baseUrl,
      documentation,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the initial version to the API
    newAPI.versions.push(initialVersion);
    
    // Add the new API to the collection
    models.apis.push(newAPI);
    
    // Also add the version to the separate versions collection
    models.apiVersions.push(initialVersion);
    
    res.status(201).json({
      data: newAPI,
      message: 'API created successfully'
    });
  } catch (error) {
    console.error('Error in createAPI:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateAPI = (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, status, owner, team, baseUrl, documentation, repository, tags } = req.body;
    
    // Find the API to update
    const apiIndex = models.apis.findIndex(a => a.id === id);
    
    if (apiIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    // Update the API
    const updatedAPI = {
      ...models.apis[apiIndex],
      name: name || models.apis[apiIndex].name,
      description: description || models.apis[apiIndex].description,
      status: status || models.apis[apiIndex].status,
      owner: owner || models.apis[apiIndex].owner,
      team: team || models.apis[apiIndex].team,
      baseUrl: baseUrl || models.apis[apiIndex].baseUrl,
      documentation: documentation || models.apis[apiIndex].documentation,
      repository: repository || models.apis[apiIndex].repository,
      tags: tags || models.apis[apiIndex].tags,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old API with the updated one
    models.apis[apiIndex] = updatedAPI;
    
    res.json({
      data: updatedAPI,
      message: 'API updated successfully'
    });
  } catch (error) {
    console.error('Error in updateAPI:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete an API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteAPI = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the API to delete
    const apiIndex = models.apis.findIndex(a => a.id === id);
    
    if (apiIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    // Remove the API from the collection
    models.apis.splice(apiIndex, 1);
    
    // Also remove any versions, dependencies, and consumers for this API
    const versionsToRemove = models.apiVersions.filter(v => v.apiId === id);
    versionsToRemove.forEach(v => {
      const index = models.apiVersions.findIndex(version => version.id === v.id);
      if (index !== -1) {
        models.apiVersions.splice(index, 1);
      }
    });
    
    const dependenciesToRemove = models.apiDependencies.filter(d => d.apiId === id);
    dependenciesToRemove.forEach(d => {
      const index = models.apiDependencies.findIndex(dep => dep.id === d.id);
      if (index !== -1) {
        models.apiDependencies.splice(index, 1);
      }
    });
    
    const consumersToRemove = models.apiConsumers.filter(c => c.apiId === id);
    consumersToRemove.forEach(c => {
      const index = models.apiConsumers.findIndex(consumer => consumer.id === c.id);
      if (index !== -1) {
        models.apiConsumers.splice(index, 1);
      }
    });
    
    res.json({
      message: 'API deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteAPI:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get versions for a specific API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIVersions = (req, res) => {
  try {
    const { id } = req.params;
    const api = models.apis.find(a => a.id === id);
    
    if (!api) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    res.json({ data: api.versions });
  } catch (error) {
    console.error('Error in getAPIVersions:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a version to an API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addAPIVersion = (req, res) => {
  try {
    const { id } = req.params;
    const { version, status, releaseDate, endOfLifeDate, changeLog, baseUrl, documentation } = req.body;
    
    // Find the API
    const apiIndex = models.apis.findIndex(a => a.id === id);
    
    if (apiIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    // Check if the version already exists
    const versionExists = models.apis[apiIndex].versions.some(v => v.version === version);
    
    if (versionExists) {
      return res.status(400).json({
        error: 'Bad Request',
        message: `Version ${version} already exists for this API`
      });
    }
    
    // Create a new version
    const newVersion = {
      id: `ver-${uuidv4().substring(0, 8)}`,
      apiId: id,
      version,
      status,
      releaseDate,
      endOfLifeDate,
      changeLog,
      baseUrl,
      documentation,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the version to the API
    models.apis[apiIndex].versions.push(newVersion);
    
    // Update the API's version and status if this is the latest version
    const isLatestVersion = models.apis[apiIndex].versions
      .sort((a, b) => {
        // Simple version comparison (assumes semantic versioning)
        return a.version > b.version ? -1 : 1;
      })[0].version === version;
    
    if (isLatestVersion) {
      models.apis[apiIndex].version = version;
      models.apis[apiIndex].status = status;
    }
    
    // Update the API's updatedAt timestamp
    models.apis[apiIndex].updatedAt = new Date().toISOString();
    
    // Also add to the separate versions collection
    models.apiVersions.push(newVersion);
    
    res.status(201).json({
      data: newVersion,
      message: 'API version added successfully'
    });
  } catch (error) {
    console.error('Error in addAPIVersion:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific API version
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIVersionById = (req, res) => {
  try {
    const { id, versionId } = req.params;
    const api = models.apis.find(a => a.id === id);
    
    if (!api) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    const version = api.versions.find(v => v.id === versionId);
    
    if (!version) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Version with ID ${versionId} not found for API ${id}`
      });
    }
    
    res.json({ data: version });
  } catch (error) {
    console.error('Error in getAPIVersionById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an API version
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateAPIVersion = (req, res) => {
  try {
    const { id, versionId } = req.params;
    const { status, releaseDate, endOfLifeDate, changeLog, baseUrl, documentation } = req.body;
    
    // Find the API
    const apiIndex = models.apis.findIndex(a => a.id === id);
    
    if (apiIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    // Find the version in the API
    const versionIndex = models.apis[apiIndex].versions.findIndex(v => v.id === versionId);
    
    if (versionIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Version with ID ${versionId} not found for API ${id}`
      });
    }
    
    // Update the version
    const updatedVersion = {
      ...models.apis[apiIndex].versions[versionIndex],
      status: status || models.apis[apiIndex].versions[versionIndex].status,
      releaseDate: releaseDate || models.apis[apiIndex].versions[versionIndex].releaseDate,
      endOfLifeDate: endOfLifeDate !== undefined ? endOfLifeDate : models.apis[apiIndex].versions[versionIndex].endOfLifeDate,
      changeLog: changeLog || models.apis[apiIndex].versions[versionIndex].changeLog,
      baseUrl: baseUrl || models.apis[apiIndex].versions[versionIndex].baseUrl,
      documentation: documentation || models.apis[apiIndex].versions[versionIndex].documentation,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old version with the updated one
    models.apis[apiIndex].versions[versionIndex] = updatedVersion;
    
    // Update the API's version and status if this is the latest version
    const isLatestVersion = models.apis[apiIndex].versions
      .sort((a, b) => {
        // Simple version comparison (assumes semantic versioning)
        return a.version > b.version ? -1 : 1;
      })[0].id === versionId;
    
    if (isLatestVersion && status) {
      models.apis[apiIndex].status = status;
    }
    
    // Update the API's updatedAt timestamp
    models.apis[apiIndex].updatedAt = new Date().toISOString();
    
    // Also update in the separate versions collection
    const separateVersionIndex = models.apiVersions.findIndex(v => v.id === versionId);
    if (separateVersionIndex !== -1) {
      models.apiVersions[separateVersionIndex] = updatedVersion;
    }
    
    res.json({
      data: updatedVersion,
      message: 'API version updated successfully'
    });
  } catch (error) {
    console.error('Error in updateAPIVersion:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get dependencies for a specific API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIDependencies = (req, res) => {
  try {
    const { id } = req.params;
    const api = models.apis.find(a => a.id === id);
    
    if (!api) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    res.json({ data: api.dependencies });
  } catch (error) {
    console.error('Error in getAPIDependencies:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a dependency to an API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addAPIDependency = (req, res) => {
  try {
    const { id } = req.params;
    const { dependencyType, name, version, description, criticality } = req.body;
    
    // Find the API
    const apiIndex = models.apis.findIndex(a => a.id === id);
    
    if (apiIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    // Create a new dependency
    const newDependency = {
      id: `dep-${uuidv4().substring(0, 8)}`,
      apiId: id,
      dependencyType,
      name,
      version,
      description,
      criticality,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the dependency to the API
    models.apis[apiIndex].dependencies.push(newDependency);
    
    // Update the API's updatedAt timestamp
    models.apis[apiIndex].updatedAt = new Date().toISOString();
    
    // Also add to the separate dependencies collection
    models.apiDependencies.push(newDependency);
    
    res.status(201).json({
      data: newDependency,
      message: 'API dependency added successfully'
    });
  } catch (error) {
    console.error('Error in addAPIDependency:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get consumers for a specific API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIConsumers = (req, res) => {
  try {
    const { id } = req.params;
    const api = models.apis.find(a => a.id === id);
    
    if (!api) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    res.json({ data: api.consumers });
  } catch (error) {
    console.error('Error in getAPIConsumers:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a consumer to an API
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addAPIConsumer = (req, res) => {
  try {
    const { id } = req.params;
    const { name, type, contact, usageLevel, apiVersions } = req.body;
    
    // Find the API
    const apiIndex = models.apis.findIndex(a => a.id === id);
    
    if (apiIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `API with ID ${id} not found`
      });
    }
    
    // Create a new consumer
    const newConsumer = {
      id: `con-${uuidv4().substring(0, 8)}`,
      apiId: id,
      name,
      type,
      contact,
      usageLevel,
      apiVersions: apiVersions || [models.apis[apiIndex].version],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the consumer to the API
    models.apis[apiIndex].consumers.push(newConsumer);
    
    // Update the API's updatedAt timestamp
    models.apis[apiIndex].updatedAt = new Date().toISOString();
    
    // Also add to the separate consumers collection
    models.apiConsumers.push(newConsumer);
    
    res.status(201).json({
      data: newConsumer,
      message: 'API consumer added successfully'
    });
  } catch (error) {
    console.error('Error in addAPIConsumer:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get API types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPITypes = (req, res) => {
  try {
    res.json({ data: models.apiTypes });
  } catch (error) {
    console.error('Error in getAPITypes:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get API statuses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAPIStatuses = (req, res) => {
  try {
    res.json({ data: models.apiStatuses });
  } catch (error) {
    console.error('Error in getAPIStatuses:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getAPIs,
  getAPIById,
  createAPI,
  updateAPI,
  deleteAPI,
  getAPIVersions,
  addAPIVersion,
  getAPIVersionById,
  updateAPIVersion,
  getAPIDependencies,
  addAPIDependency,
  getAPIConsumers,
  addAPIConsumer,
  getAPITypes,
  getAPIStatuses
};
