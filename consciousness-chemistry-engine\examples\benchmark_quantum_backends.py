"""
Benchmark script for comparing different quantum backends with RoseTTAFold.

This script runs a series of protein folding predictions using different
quantum backends and measures their performance and resource usage.
"""

import os
import sys
import time
import json
import argparse
import logging
import statistics
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import psutil
import GP<PERSON>til
import numpy as np

# Add parent directory to path to import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.rosettafold_engine import RoseTTAFoldEngine
from src.quantum.backend_factory import get_backend_factory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QuantumBackendBenchmark:
    """Benchmark suite for quantum backends with RoseTTAFold."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the benchmark.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.results = []
        self.backend_factory = get_backend_factory()
        self.available_backends = self.backend_factory.get_available_backends()
        
        # Create output directory
        self.output_dir = Path(config.get('output_dir', 'quantum_benchmark_results'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        import platform
        import cpuinfo
        
        # Get CPU info
        cpu_info = cpuinfo.get_cpu_info()
        
        # Get GPU info
        gpus = GPUtil.getGPUs()
        gpu_info = [{
            'name': gpu.name,
            'memory_total': gpu.memoryTotal,
            'memory_free': gpu.memoryFree,
            'memory_used': gpu.memoryUsed,
            'temperature': gpu.temperature,
            'uuid': gpu.uuid
        } for gpu in gpus]
        
        return {
            'system': {
                'platform': platform.platform(),
                'python_version': platform.python_version(),
                'cpu': {
                    'brand': cpu_info.get('brand_raw', 'Unknown'),
                    'cores': psutil.cpu_count(logical=False),
                    'threads': psutil.cpu_count(logical=True),
                    'hz_actual': cpu_info.get('hz_actual_friendly', 'Unknown'),
                    'l3_cache': cpu_info.get('l3_cache_size', 0) // 1024**2 if cpu_info.get('l3_cache_size') else 0
                },
                'memory': {
                    'total': psutil.virtual_memory().total // (1024**3),  # GB
                    'available': psutil.virtual_memory().available // (1024**3)
                },
                'gpus': gpu_info
            }
        }
    
    def run_benchmark(self, sequences: List[Dict[str, Any]]) -> None:
        """Run the benchmark with the given sequences.
        
        Args:
            sequences: List of sequence configurations
        """
        system_info = self.get_system_info()
        logger.info(f"Starting benchmark on {system_info['system']['platform']}")
        
        # Save system info
        with open(self.output_dir / 'system_info.json', 'w') as f:
            json.dump(system_info, f, indent=2)
        
        # Test each backend with each sequence
        for seq_config in sequences:
            sequence = seq_config['sequence']
            seq_name = seq_config.get('name', f"seq_{len(sequence)}")
            
            for backend_name in self.available_backends:
                # Skip if backend is not in the include list (if specified)
                if 'include_backends' in self.config and backend_name not in self.config['include_backends']:
                    continue
                
                # Configure RoseTTAFold with this backend
                config = {
                    'mode': 'hybrid',
                    'rosettafold_path': self.config['rosettafold_path'],
                    'output_dir': str(self.output_dir / seq_name / backend_name),
                    'quantum_backend': backend_name,
                    'quantum_shots': self.config.get('shots', 1000),
                    'quantum_circuit_depth': self.config.get('circuit_depth', 100),
                    'quantum_layers': self.config.get('layers', 2),
                    **seq_config.get('config', {})
                }
                
                # Run the benchmark
                result = self._run_single_benchmark(sequence, seq_name, backend_name, config)
                self.results.append(result)
                
                # Save intermediate results
                self.save_results()
    
    def _run_single_benchmark(
        self, 
        sequence: str, 
        seq_name: str, 
        backend_name: str, 
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run a single benchmark.
        
        Args:
            sequence: Protein sequence to fold
            seq_name: Name of the sequence
            backend_name: Name of the quantum backend to use
            config: Configuration for the RoseTTAFold engine
            
        Returns:
            Dictionary with benchmark results
        """
        logger.info(f"\n{'='*80}")
        logger.info(f"Benchmarking {seq_name} with {backend_name}")
        logger.info(f"Sequence: {sequence[:50]}{'...' if len(sequence) > 50 else ''}")
        logger.info(f"Config: {json.dumps(config, indent=2)}")
        
        # Create output directory
        output_dir = Path(config['output_dir'])
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize metrics
        metrics = {
            'sequence_name': seq_name,
            'sequence_length': len(sequence),
            'backend': backend_name,
            'config': config,
            'start_time': time.time(),
            'end_time': None,
            'duration': None,
            'success': False,
            'error': None,
            'memory_usage': [],
            'gpu_usage': [],
            'cpu_usage': [],
            'result': None
        }
        
        # Track resource usage in a separate thread
        from threading import Event, Thread
        stop_event = Event()
        
        def monitor_resources(interval=0.1):
            while not stop_event.is_set():
                # CPU usage
                metrics['cpu_usage'].append(psutil.cpu_percent(interval=None))
                
                # Memory usage
                process = psutil.Process()
                metrics['memory_usage'].append(process.memory_info().rss / (1024**2))  # MB
                
                # GPU usage
                try:
                    gpu_info = GPUtil.getGPUs()
                    if gpu_info:
                        metrics['gpu_usage'].append({
                            'memory_used': gpu_info[0].memoryUsed,
                            'memory_total': gpu_info[0].memoryTotal,
                            'load': gpu_info[0].load * 100  # percentage
                        })
                except Exception as e:
                    logger.warning(f"Failed to get GPU info: {str(e)}")
                
                stop_event.wait(interval)
        
        # Start monitoring
        monitor_thread = Thread(target=monitor_resources)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            # Initialize engine
            engine = RoseTTAFoldEngine(config=config)
            
            # Run prediction
            start_time = time.time()
            result = engine.predict(sequence)
            end_time = time.time()
            
            # Update metrics
            metrics.update({
                'end_time': end_time,
                'duration': end_time - start_time,
                'success': True,
                'result': {
                    'pdb_path': result.get('pdb_path'),
                    'processing_time_seconds': result.get('processing_time_seconds'),
                    'quantum_info': result.get('quantum_info', {})
                }
            })
            
            logger.info(f"Completed in {metrics['duration']:.2f} seconds")
            
        except Exception as e:
            metrics.update({
                'end_time': time.time(),
                'duration': time.time() - metrics['start_time'],
                'success': False,
                'error': str(e)
            })
            logger.error(f"Failed: {str(e)}", exc_info=True)
            
        finally:
            # Stop monitoring
            stop_event.set()
            monitor_thread.join(timeout=5)
            
            # Calculate aggregate metrics
            if metrics['cpu_usage']:
                metrics['cpu_avg'] = statistics.mean(metrics['cpu_usage'])
                metrics['cpu_max'] = max(metrics['cpu_usage'])
            
            if metrics['memory_usage']:
                metrics['memory_avg_mb'] = statistics.mean(metrics['memory_usage'])
                metrics['memory_max_mb'] = max(metrics['memory_usage'])
            
            if metrics['gpu_usage']:
                metrics['gpu_memory_avg'] = statistics.mean(g['memory_used'] for g in metrics['gpu_usage'])
                metrics['gpu_memory_max'] = max(g['memory_used'] for g in metrics['gpu_usage'])
                metrics['gpu_load_avg'] = statistics.mean(g['load'] for g in metrics['gpu_usage'])
            
            return metrics
    
    def save_results(self, filename: str = 'benchmark_results.json') -> None:
        """Save benchmark results to a file."""
        output_file = self.output_dir / filename
        
        # Convert to serializable format
        results = []
        for r in self.results:
            result = r.copy()
            
            # Convert numpy types to native Python types
            for k, v in result.items():
                if isinstance(v, (np.integer, np.floating, np.bool_)):
                    result[k] = v.item()
                elif isinstance(v, np.ndarray):
                    result[k] = v.tolist()
            
            # Remove large data that's not needed in the summary
            for field in ['memory_usage', 'gpu_usage', 'cpu_usage']:
                if field in result:
                    del result[field]
            
            results.append(result)
        
        # Save to file
        with open(output_file, 'w') as f:
            json.dump({
                'timestamp': time.time(),
                'config': self.config,
                'results': results
            }, f, indent=2)
        
        logger.info(f"Results saved to {output_file}")

def main():
    """Main function to run the benchmark."""
    parser = argparse.ArgumentParser(description='Benchmark quantum backends with RoseTTAFold')
    parser.add_argument('--rosettafold-path', type=str, required=True,
                       help='Path to local RoseTTAFold installation')
    parser.add_argument('--output-dir', type=str, default='quantum_benchmark_results',
                       help='Output directory for results')
    parser.add_argument('--sequences', type=str, default='default',
                       help='Path to JSON file with sequence configurations or "default"')
    parser.add_argument('--backends', type=str, nargs='+', default=[],
                       help='List of backends to test (default: all available)')
    parser.add_argument('--shots', type=int, default=1000,
                       help='Number of quantum shots')
    parser.add_argument('--circuit-depth', type=int, default=100,
                       help='Depth of quantum circuit')
    parser.add_argument('--layers', type=int, default=2,
                       help='Number of quantum layers')
    
    args = parser.parse_args()
    
    # Default sequences if not provided
    if args.sequences == 'default':
        sequences = [
            {
                'name': 'short_sequence',
                'sequence': 'ACDEFGHIKLMNPQRSTVWY',  # 20 residues
                'config': {}
            },
            {
                'name': 'medium_sequence',
                'sequence': 'MVLSEGEWQLVLHVWAKVEADVAGHGQDILIRLFKSHPETLEKFDRVKHLKTEAEMKASEDLKKHGVTVLTALGAILKKKGHHEAELKPLAQSHATKHKIPIKYLEFISEAIIHVLHSRHPGNFGADAQGAMNKALELFRKDIAAKYKELGYQG',  # 100 residues
                'config': {}
            },
            # Add more test sequences as needed
        ]
    else:
        # Load sequences from file
        with open(args.sequences) as f:
            sequences = json.load(f)
    
    # Configure benchmark
    config = {
        'rosettafold_path': args.rosettafold_path,
        'output_dir': args.output_dir,
        'shots': args.shots,
        'circuit_depth': args.circuit_depth,
        'layers': args.layers,
    }
    
    if args.backends:
        config['include_backends'] = args.backends
    
    # Run benchmark
    benchmark = QuantumBackendBenchmark(config)
    benchmark.run_benchmark(sequences)
    
    # Save final results
    benchmark.save_results()
    
    # Print summary
    print("\nBenchmark Summary:")
    print("=" * 50)
    for result in benchmark.results:
        status = "SUCCESS" if result['success'] else f"FAILED: {result.get('error', 'Unknown error')}"
        print(f"{result['sequence_name']} - {result['backend']}: {status}")
        if result['success']:
            print(f"  Duration: {result['duration']:.2f}s")
            if 'cpu_avg' in result:
                print(f"  CPU: avg={result['cpu_avg']:.1f}%, max={result['cpu_max']:.1f}%")
            if 'memory_avg_mb' in result:
                print(f"  Memory: avg={result['memory_avg_mb']:.1f}MB, max={result['memory_max_mb']:.1f}MB")
            if 'gpu_memory_avg' in result:
                print(f"  GPU Memory: avg={result['gpu_memory_avg']:.1f}MB, max={result['gpu_memory_max']:.1f}MB")
                print(f"  GPU Load: avg={result['gpu_load_avg']:.1f}%")
        print()

if __name__ == "__main__":
    main()
