# Test NovaFuse API Endpoints
# This script tests the NovaFuse API endpoints directly

Write-Host "Testing NovaFuse API Endpoints" -ForegroundColor Yellow

# Test NovaFuse UI health endpoint
Write-Host "`nTesting NovaFuse UI health endpoint:" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3003/health" -Method Get
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test NovaFuse UI SCC health endpoint
Write-Host "`nTesting NovaFuse UI SCC health endpoint:" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3003/scc/health" -Method Get
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test NovaFuse UI IAM health endpoint
Write-Host "`nTesting NovaFuse UI IAM health endpoint:" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3003/iam/health" -Method Get
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test NovaFuse UI BigQuery health endpoint
Write-Host "`nTesting NovaFuse UI BigQuery health endpoint:" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3003/bigquery/health" -Method Get
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test NovaFuse API health endpoint
Write-Host "`nTesting NovaFuse API health endpoint:" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3001/health" -Method Get
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test NovaConnect UAC health endpoint
Write-Host "`nTesting NovaConnect UAC health endpoint:" -ForegroundColor Green
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3002/health" -Method Get
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nAPI Endpoint Test Complete" -ForegroundColor Green
