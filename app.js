const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const { authenticate } = require('./apis/privacy/management/auth');
const logger = require('./apis/privacy/management/logging');
const monitoring = require('./apis/privacy/management/monitoring');

// Import routes
const privacyManagementRoutes = require('./apis/privacy/management/routes');
const authRoutes = require('./apis/privacy/management/auth-routes');
const integrationRoutes = require('./apis/privacy/management/integration-routes');

// Create Express app
const app = express();

// Serve static files from the public directory
app.use(express.static('public'));

// Apply middleware
app.use(helmet()); // Security headers

// Configure CORS with more restrictive options
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-Total-Count'],
  credentials: true,
  maxAge: 86400 // 24 hours
};
app.use(cors(corsOptions));

// Parse JSON request bodies with size limit
app.use(bodyParser.json({ limit: '1mb' }));

// Parse URL-encoded request bodies with size limit
app.use(bodyParser.urlencoded({ extended: true, limit: '1mb' }));

// Logging
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined')); // Log requests using Morgan
  app.use(logger.httpLogger); // Log requests using Winston
  app.use(monitoring.httpMonitoringMiddleware); // Monitor requests
}

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: 'Too many requests from this IP, please try again after 15 minutes'
});

// Apply rate limiting to all routes
app.use(limiter);

// Security headers
app.use((req, res, next) => {
  // Content Security Policy
  res.setHeader('Content-Security-Policy', "default-src 'self'");

  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');

  // Clickjacking protection
  res.setHeader('X-Frame-Options', 'DENY');

  // XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');

  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
});

// Authentication routes (no authentication required)
app.use('/privacy/management/auth', authRoutes);

// Metrics endpoint (no authentication required)
app.get('/privacy/management/metrics', monitoring.metricsHandler);

// Health check endpoint (no authentication required)
app.get('/privacy/management/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Protected API routes (authentication required)
if (process.env.NODE_ENV === 'test') {
  // Skip authentication in test mode
  app.use('/privacy/management', privacyManagementRoutes);
  app.use('/privacy/management/integrations', integrationRoutes);
} else {
  // Use authentication in production/development
  app.use('/privacy/management', authenticate, privacyManagementRoutes);
  app.use('/privacy/management/integrations', authenticate, integrationRoutes);
}

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : err.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.method} ${req.url} not found`
  });
});

module.exports = app;
