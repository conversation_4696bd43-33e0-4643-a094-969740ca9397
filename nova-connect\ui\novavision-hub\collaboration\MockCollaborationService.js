/**
 * Mock Collaboration Service
 * 
 * This module provides a mock implementation of the collaboration service for testing and development.
 */

import CollaborationService from './CollaborationService';

/**
 * Mock collaboration service
 * 
 * @extends {CollaborationService}
 */
class MockCollaborationService extends CollaborationService {
  /**
   * Constructor
   * 
   * @param {Object} [options] - Options
   * @param {Object} [options.storage=localStorage] - Storage
   * @param {string} [options.storageKey='novavision_collaboration'] - Storage key
   * @param {number} [options.simulatedLatency=200] - Simulated latency in milliseconds
   * @param {Object} [options.mockRooms] - Mock rooms
   * @param {Object} [options.mockUsers] - Mock users
   */
  constructor(options = {}) {
    super();
    
    this.storage = options.storage || (typeof localStorage !== 'undefined' ? localStorage : null);
    this.storageKey = options.storageKey || 'novavision_collaboration';
    this.simulatedLatency = options.simulatedLatency || 200;
    
    // Mock data
    this.mockRooms = options.mockRooms || [
      {
        id: 'room-1',
        name: 'Compliance Dashboard',
        description: 'Collaborative compliance dashboard',
        createdAt: new Date().toISOString(),
        createdBy: 'user-1',
        type: 'dashboard',
        isPublic: true,
        members: ['user-1', 'user-2']
      },
      {
        id: 'room-2',
        name: 'Security Analysis',
        description: 'Security analysis workspace',
        createdAt: new Date().toISOString(),
        createdBy: 'user-2',
        type: 'analysis',
        isPublic: false,
        members: ['user-2', 'user-3']
      }
    ];
    
    this.mockUsers = options.mockUsers || [
      {
        id: 'user-1',
        displayName: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        photoURL: 'https://via.placeholder.com/150',
        status: 'online',
        lastActive: new Date().toISOString()
      },
      {
        id: 'user-2',
        displayName: 'Regular User',
        email: '<EMAIL>',
        role: 'user',
        photoURL: 'https://via.placeholder.com/150',
        status: 'online',
        lastActive: new Date().toISOString()
      },
      {
        id: 'user-3',
        displayName: 'Guest User',
        email: '<EMAIL>',
        role: 'guest',
        photoURL: 'https://via.placeholder.com/150',
        status: 'offline',
        lastActive: new Date(Date.now() - 3600000).toISOString()
      }
    ];
    
    // State
    this.isInitialized = false;
    this.isConnected = false;
    this.currentUser = null;
    this.currentRoom = null;
    this.activeUsers = [];
    this.messages = {};
    this.cursors = {};
    this.sharedState = {};
    
    // Event listeners
    this.connectionStateListeners = [];
    this.activeUsersListeners = [];
    this.messageListeners = [];
    this.cursorListeners = [];
    this.sharedStateListeners = [];
    
    // Initialize from storage
    this.initializeFromStorage();
  }
  
  /**
   * Initialize from storage
   */
  initializeFromStorage() {
    if (!this.storage) return;
    
    try {
      const data = this.storage.getItem(this.storageKey);
      
      if (data) {
        const parsedData = JSON.parse(data);
        
        this.messages = parsedData.messages || {};
        this.sharedState = parsedData.sharedState || {};
      }
    } catch (error) {
      console.error('Error initializing collaboration from storage:', error);
    }
  }
  
  /**
   * Save to storage
   */
  saveToStorage() {
    if (!this.storage) return;
    
    try {
      this.storage.setItem(this.storageKey, JSON.stringify({
        messages: this.messages,
        sharedState: this.sharedState
      }));
    } catch (error) {
      console.error('Error saving collaboration to storage:', error);
    }
  }
  
  /**
   * Simulate network delay
   * 
   * @returns {Promise<void>}
   */
  async simulateNetworkDelay() {
    await new Promise(resolve => setTimeout(resolve, this.simulatedLatency));
  }
  
  /**
   * Notify connection state listeners
   */
  notifyConnectionStateListeners() {
    const state = {
      isConnected: this.isConnected,
      timestamp: new Date().toISOString()
    };
    
    this.connectionStateListeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in connection state listener:', error);
      }
    });
  }
  
  /**
   * Notify active users listeners
   */
  notifyActiveUsersListeners() {
    this.activeUsersListeners.forEach(listener => {
      try {
        listener(this.activeUsers);
      } catch (error) {
        console.error('Error in active users listener:', error);
      }
    });
  }
  
  /**
   * Notify message listeners
   * 
   * @param {Object} message - Message
   */
  notifyMessageListeners(message) {
    this.messageListeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('Error in message listener:', error);
      }
    });
  }
  
  /**
   * Notify cursor listeners
   * 
   * @param {string} userId - User ID
   * @param {Object} position - Cursor position
   */
  notifyCursorListeners(userId, position) {
    this.cursorListeners.forEach(listener => {
      try {
        listener(userId, position);
      } catch (error) {
        console.error('Error in cursor listener:', error);
      }
    });
  }
  
  /**
   * Notify shared state listeners
   * 
   * @param {Object} state - Shared state
   */
  notifySharedStateListeners(state) {
    this.sharedStateListeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Error in shared state listener:', error);
      }
    });
  }
  
  /**
   * Initialize collaboration service
   * 
   * @param {string} token - Authentication token
   * @param {Object} user - User object
   * @returns {Promise<void>}
   */
  async initialize(token, user) {
    await this.simulateNetworkDelay();
    
    // Find or create user
    const existingUser = this.mockUsers.find(u => u.id === user.id);
    
    if (existingUser) {
      this.currentUser = {
        ...existingUser,
        status: 'online',
        lastActive: new Date().toISOString()
      };
    } else {
      this.currentUser = {
        id: user.id,
        displayName: user.displayName || user.email,
        email: user.email,
        role: user.role || 'user',
        photoURL: user.photoURL || 'https://via.placeholder.com/150',
        status: 'online',
        lastActive: new Date().toISOString()
      };
      
      this.mockUsers.push(this.currentUser);
    }
    
    // Update active users
    this.activeUsers = this.mockUsers.filter(u => u.status === 'online');
    
    this.isInitialized = true;
    this.isConnected = true;
    
    // Notify listeners
    this.notifyConnectionStateListeners();
    this.notifyActiveUsersListeners();
  }
  
  /**
   * Disconnect from collaboration service
   * 
   * @returns {Promise<void>}
   */
  async disconnect() {
    await this.simulateNetworkDelay();
    
    if (this.currentUser) {
      // Update user status
      this.currentUser.status = 'offline';
      this.currentUser.lastActive = new Date().toISOString();
      
      // Update active users
      this.activeUsers = this.mockUsers.filter(u => u.status === 'online');
    }
    
    this.isConnected = false;
    this.currentRoom = null;
    
    // Notify listeners
    this.notifyConnectionStateListeners();
    this.notifyActiveUsersListeners();
  }
  
  /**
   * Get active rooms
   * 
   * @returns {Promise<Array>} Active rooms
   */
  async getActiveRooms() {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    // Filter rooms based on user access
    return this.mockRooms.filter(room => 
      room.isPublic || room.members.includes(this.currentUser.id)
    );
  }
  
  /**
   * Create room
   * 
   * @param {Object} roomData - Room data
   * @returns {Promise<Object>} Room
   */
  async createRoom(roomData) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    if (!this.currentUser) {
      throw new Error('Not authenticated');
    }
    
    // Create new room
    const newRoom = {
      id: `room-${this.mockRooms.length + 1}`,
      name: roomData.name,
      description: roomData.description || '',
      createdAt: new Date().toISOString(),
      createdBy: this.currentUser.id,
      type: roomData.type || 'dashboard',
      isPublic: roomData.isPublic || false,
      members: [this.currentUser.id, ...(roomData.members || [])]
    };
    
    this.mockRooms.push(newRoom);
    
    // Initialize room data
    this.messages[newRoom.id] = [];
    this.cursors[newRoom.id] = {};
    this.sharedState[newRoom.id] = roomData.initialState || {};
    
    // Save to storage
    this.saveToStorage();
    
    return newRoom;
  }
  
  /**
   * Join room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Room
   */
  async joinRoom(roomId) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    if (!this.currentUser) {
      throw new Error('Not authenticated');
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      throw new Error('Room not found');
    }
    
    // Check access
    if (!room.isPublic && !room.members.includes(this.currentUser.id)) {
      throw new Error('Access denied');
    }
    
    // Add user to room if not already a member
    if (!room.members.includes(this.currentUser.id)) {
      room.members.push(this.currentUser.id);
    }
    
    this.currentRoom = room;
    
    // Initialize room data if not exists
    if (!this.messages[roomId]) {
      this.messages[roomId] = [];
    }
    
    if (!this.cursors[roomId]) {
      this.cursors[roomId] = {};
    }
    
    if (!this.sharedState[roomId]) {
      this.sharedState[roomId] = {};
    }
    
    // Add system message
    const joinMessage = {
      id: `msg-${Date.now()}`,
      roomId,
      userId: 'system',
      content: `${this.currentUser.displayName} joined the room`,
      type: 'system',
      timestamp: new Date().toISOString()
    };
    
    this.messages[roomId].push(joinMessage);
    
    // Notify listeners
    this.notifyMessageListeners(joinMessage);
    
    // Save to storage
    this.saveToStorage();
    
    return room;
  }
  
  /**
   * Leave room
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<void>}
   */
  async leaveRoom(roomId) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      return;
    }
    
    if (!this.currentUser) {
      return;
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      return;
    }
    
    // Add system message
    const leaveMessage = {
      id: `msg-${Date.now()}`,
      roomId,
      userId: 'system',
      content: `${this.currentUser.displayName} left the room`,
      type: 'system',
      timestamp: new Date().toISOString()
    };
    
    if (this.messages[roomId]) {
      this.messages[roomId].push(leaveMessage);
    }
    
    // Remove cursor
    if (this.cursors[roomId]) {
      delete this.cursors[roomId][this.currentUser.id];
    }
    
    this.currentRoom = null;
    
    // Notify listeners
    this.notifyMessageListeners(leaveMessage);
    
    // Save to storage
    this.saveToStorage();
  }
  
  /**
   * Get room users
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Array>} Room users
   */
  async getRoomUsers(roomId) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      throw new Error('Room not found');
    }
    
    // Get users in room
    return this.mockUsers.filter(user => room.members.includes(user.id));
  }
  
  /**
   * Get room messages
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Array>} Room messages
   */
  async getRoomMessages(roomId) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      throw new Error('Room not found');
    }
    
    return this.messages[roomId] || [];
  }
  
  /**
   * Send message
   * 
   * @param {string} roomId - Room ID
   * @param {Object} messageData - Message data
   * @returns {Promise<Object>} Message
   */
  async sendMessage(roomId, messageData) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    if (!this.currentUser) {
      throw new Error('Not authenticated');
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      throw new Error('Room not found');
    }
    
    // Create message
    const message = {
      id: `msg-${Date.now()}`,
      roomId,
      userId: this.currentUser.id,
      userDisplayName: this.currentUser.displayName,
      userPhotoURL: this.currentUser.photoURL,
      ...messageData
    };
    
    // Add message to room
    if (!this.messages[roomId]) {
      this.messages[roomId] = [];
    }
    
    this.messages[roomId].push(message);
    
    // Notify listeners
    this.notifyMessageListeners(message);
    
    // Save to storage
    this.saveToStorage();
    
    return message;
  }
  
  /**
   * Update cursor position
   * 
   * @param {string} roomId - Room ID
   * @param {Object} position - Cursor position
   * @returns {Promise<void>}
   */
  async updateCursorPosition(roomId, position) {
    if (!this.isConnected) {
      return;
    }
    
    if (!this.currentUser) {
      return;
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      return;
    }
    
    // Update cursor position
    if (!this.cursors[roomId]) {
      this.cursors[roomId] = {};
    }
    
    this.cursors[roomId][this.currentUser.id] = {
      ...position,
      timestamp: new Date().toISOString()
    };
    
    // Notify listeners
    this.notifyCursorListeners(this.currentUser.id, this.cursors[roomId][this.currentUser.id]);
  }
  
  /**
   * Get shared state
   * 
   * @param {string} roomId - Room ID
   * @returns {Promise<Object>} Shared state
   */
  async getSharedState(roomId) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      throw new Error('Room not found');
    }
    
    return this.sharedState[roomId] || {};
  }
  
  /**
   * Update shared state
   * 
   * @param {string} roomId - Room ID
   * @param {Object} update - State update
   * @returns {Promise<Object>} Updated state
   */
  async updateSharedState(roomId, update) {
    await this.simulateNetworkDelay();
    
    if (!this.isConnected) {
      throw new Error('Not connected');
    }
    
    if (!this.currentUser) {
      throw new Error('Not authenticated');
    }
    
    // Find room
    const room = this.mockRooms.find(r => r.id === roomId);
    
    if (!room) {
      throw new Error('Room not found');
    }
    
    // Update shared state
    if (!this.sharedState[roomId]) {
      this.sharedState[roomId] = {};
    }
    
    this.sharedState[roomId] = {
      ...this.sharedState[roomId],
      ...update,
      lastUpdated: new Date().toISOString(),
      lastUpdatedBy: this.currentUser.id
    };
    
    // Notify listeners
    this.notifySharedStateListeners(this.sharedState[roomId]);
    
    // Save to storage
    this.saveToStorage();
    
    return this.sharedState[roomId];
  }
  
  /**
   * Set up connection state change listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onConnectionStateChanged(callback) {
    this.connectionStateListeners.push(callback);
    
    // Call immediately with current state
    if (callback) {
      callback({
        isConnected: this.isConnected,
        timestamp: new Date().toISOString()
      });
    }
    
    // Return unsubscribe function
    return () => {
      this.connectionStateListeners = this.connectionStateListeners.filter(listener => listener !== callback);
    };
  }
  
  /**
   * Set up active users change listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onActiveUsersChanged(callback) {
    this.activeUsersListeners.push(callback);
    
    // Call immediately with current active users
    if (callback) {
      callback(this.activeUsers);
    }
    
    // Return unsubscribe function
    return () => {
      this.activeUsersListeners = this.activeUsersListeners.filter(listener => listener !== callback);
    };
  }
  
  /**
   * Set up message received listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onMessageReceived(callback) {
    this.messageListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.messageListeners = this.messageListeners.filter(listener => listener !== callback);
    };
  }
  
  /**
   * Set up cursor moved listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onCursorMoved(callback) {
    this.cursorListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.cursorListeners = this.cursorListeners.filter(listener => listener !== callback);
    };
  }
  
  /**
   * Set up shared state change listener
   * 
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  onSharedStateChanged(callback) {
    this.sharedStateListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.sharedStateListeners = this.sharedStateListeners.filter(listener => listener !== callback);
    };
  }
}

export default MockCollaborationService;
