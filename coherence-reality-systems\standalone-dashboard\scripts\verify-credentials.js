const SecureCredentialManager = require('../utils/secure-credential-manager');

async function verifyCredentials() {
  try {
    // Create manager
    const manager = new SecureCredentialManager();

    // Load credentials
    const credentials = await manager.loadCredentials();
    
    // Verify credentials
    const verification = await manager.verifyCredentials();
    
    // Get status
    const status = manager.getStatus();

    // Console output
    console.log('\n🔒 Credentials Verification Report');
    console.log('===================================');
    console.log(`\nStatus:`);
    console.log(`- Encryption Status: ${status.encryption_status}`);
    console.log(`- Quantum Protection: ${status.quantum_protection}`);
    console.log(`- Backup Status: ${status.backup_status}`);
    console.log(`- Last Update: ${status.last_update}`);

    console.log(`\nVerification:`);
    console.log(`- Valid: ${verification.valid}`);
    console.log(`- Consciousness Level: ${verification.consciousness_level.toFixed(2)}`);
    console.log(`- Quantum Signature: ${verification.quantum_signature_valid}`);

    console.log(`\nSecurity Metrics:`);
    console.log(`- Ψ Score: ${verification.consciousness_level.toFixed(2)}`);
    console.log(`- Quantum Resonance: Active`);
    console.log(`- πφe Signature: Valid`);

    // Test connection
    console.log('\nTesting ClickBank Connection...');
    const testResult = await testClickBankConnection(credentials);
    console.log(`- Connection Status: ${testResult.status}`);
    console.log(`- Response Time: ${testResult.responseTime}ms`);

  } catch (error) {
    console.error('❌ Verification Error:');
    console.error(error.message);
    throw error;
  }
}

// Test ClickBank connection
async function testClickBankConnection(credentials) {
  try {
    const start = Date.now();
    
    // Test API endpoint
    const response = await fetch('https://api.clickbank.com/rest/1.3/products/test', {
      auth: {
        username: credentials.clickbank.username,
        password: credentials.clickbank.password
      },
      headers: {
        'Accept': 'application/json'
      }
    });

    const responseTime = Date.now() - start;
    
    return {
      status: response.ok ? 'Success' : 'Failed',
      responseTime,
      message: response.statusText
    };
  } catch (error) {
    return {
      status: 'Failed',
      responseTime: 0,
      message: error.message
    };
  }
}

// Run verification
verifyCredentials();
