#!/usr/bin/env python3
# WMAP Data Preparation for UUFT Testing
# This script downloads and prepares WMAP data for use with UUFT testing scripts

import os
import sys
import numpy as np
import pandas as pd
import requests
import io
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("wmap_data_prep.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("WMAP_Data_Prep")

# Base directory for all data
BASE_DIR = "D:/Archives"
COSMOLOGICAL_DIR = os.path.join(BASE_DIR, "Cosmological")

# WMAP data URLs
WMAP_BINNED_TT_URL = "https://lambda.gsfc.nasa.gov/data/map/powspec/wmap_binned_tt_powspec_yr1_v1p1.txt"
WMAP_LCDM_MODEL_URL = "https://lambda.gsfc.nasa.gov/data/map/powspec/wmap_lcdm_bf_model_yr1_v1.txt"

def ensure_directories():
    """Ensure all necessary directories exist."""
    os.makedirs(BASE_DIR, exist_ok=True)
    os.makedirs(COSMOLOGICAL_DIR, exist_ok=True)
    logger.info(f"Ensured directories exist: {BASE_DIR}, {COSMOLOGICAL_DIR}")

def download_wmap_data():
    """Download WMAP data from NASA's LAMBDA site."""
    # Download binned TT power spectrum
    try:
        logger.info(f"Downloading WMAP binned TT power spectrum from {WMAP_BINNED_TT_URL}")
        response = requests.get(WMAP_BINNED_TT_URL)
        response.raise_for_status()
        
        binned_tt_path = os.path.join(COSMOLOGICAL_DIR, "wmap_binned_tt.txt")
        with open(binned_tt_path, 'w') as f:
            f.write(response.text)
        logger.info(f"Saved WMAP binned TT power spectrum to {binned_tt_path}")
        
        # Parse the data
        binned_tt_data = parse_wmap_binned_tt(response.text)
        
        # Save as CSV for easier processing
        binned_tt_csv_path = os.path.join(COSMOLOGICAL_DIR, "wmap_binned_tt.csv")
        binned_tt_data.to_csv(binned_tt_csv_path, index=False)
        logger.info(f"Saved WMAP binned TT power spectrum as CSV to {binned_tt_csv_path}")
    except Exception as e:
        logger.error(f"Error downloading WMAP binned TT power spectrum: {e}")
    
    # Download LCDM model
    try:
        logger.info(f"Downloading WMAP LCDM model from {WMAP_LCDM_MODEL_URL}")
        response = requests.get(WMAP_LCDM_MODEL_URL)
        response.raise_for_status()
        
        lcdm_path = os.path.join(COSMOLOGICAL_DIR, "wmap_lcdm_model.txt")
        with open(lcdm_path, 'w') as f:
            f.write(response.text)
        logger.info(f"Saved WMAP LCDM model to {lcdm_path}")
        
        # Parse the data
        lcdm_data = parse_wmap_lcdm(response.text)
        
        # Save as CSV for easier processing
        lcdm_csv_path = os.path.join(COSMOLOGICAL_DIR, "wmap_lcdm_model.csv")
        lcdm_data.to_csv(lcdm_csv_path, index=False)
        logger.info(f"Saved WMAP LCDM model as CSV to {lcdm_csv_path}")
    except Exception as e:
        logger.error(f"Error downloading WMAP LCDM model: {e}")

def parse_wmap_binned_tt(text_data):
    """Parse the WMAP binned TT power spectrum data."""
    # Skip comment lines
    lines = text_data.strip().split('\n')
    data_lines = [line for line in lines if not line.startswith('#')]
    
    # Parse data
    data = []
    for line in data_lines:
        values = line.strip().split()
        if len(values) >= 7:
            data.append({
                'mean_l': float(values[0]),
                'min_l': float(values[1]),
                'max_l': float(values[2]),
                'power': float(values[3]),
                'error': float(values[4]),
                'measurement_error': float(values[5]),
                'cosmic_variance': float(values[6])
            })
    
    return pd.DataFrame(data)

def parse_wmap_lcdm(text_data):
    """Parse the WMAP LCDM model data."""
    # Skip comment lines
    lines = text_data.strip().split('\n')
    data_lines = [line for line in lines if not line.startswith('#')]
    
    # Parse data
    data = []
    for line in data_lines:
        values = line.strip().split()
        if len(values) >= 4:
            data.append({
                'l': float(values[0]),
                'tt_power': float(values[1]),
                'te_power': float(values[2]),
                'ee_power': float(values[3])
            })
    
    return pd.DataFrame(data)

def create_wmap_params_file():
    """Create a file with WMAP cosmological parameters for UUFT testing."""
    # WMAP parameters from the LCDM best-fit model
    params = {
        'omega_b_h2': 0.02262,  # Baryon density
        'omega_cdm_h2': 0.10861,  # Cold dark matter density
        'n_s': 1.04173,  # Spectral index
        'exp_minus_2tau': 0.69879,  # Optical depth
        'dn_dlnk': -0.01618,  # Running of spectral index
        'amp': 0.86746,  # Amplitude
        'h': 0.73070,  # Hubble constant
        'omega_m': 0.27,  # Matter density
        'omega_lambda': 0.73,  # Dark energy density
    }
    
    # Create a DataFrame with these parameters
    df = pd.DataFrame([params])
    
    # Save to a text file in the format expected by the UUFT testing scripts
    wmap_params_path = os.path.join(COSMOLOGICAL_DIR, "wmap_params.txt")
    
    # Create a formatted string with the parameters
    header = "# WMAP Cosmological Parameters\n"
    header += "# Format: omega_m omega_lambda h\n"
    header += "# omega_m: matter density\n"
    header += "# omega_lambda: dark energy density\n"
    header += "# h: Hubble constant\n"
    
    with open(wmap_params_path, 'w') as f:
        f.write(header)
        f.write(f"{params['omega_m']} {params['omega_lambda']} {params['h']}\n")
    
    logger.info(f"Created WMAP parameters file at {wmap_params_path}")
    
    # Also save the full parameters as CSV
    full_params_path = os.path.join(COSMOLOGICAL_DIR, "wmap_full_params.csv")
    df.to_csv(full_params_path, index=False)
    logger.info(f"Saved full WMAP parameters to {full_params_path}")

def create_wmap_power_spectrum_for_uuft():
    """Create a formatted power spectrum file for UUFT testing."""
    try:
        # Load the binned TT power spectrum
        binned_tt_csv_path = os.path.join(COSMOLOGICAL_DIR, "wmap_binned_tt.csv")
        if not os.path.exists(binned_tt_csv_path):
            logger.error(f"WMAP binned TT CSV file not found at {binned_tt_csv_path}")
            return
        
        binned_tt = pd.read_csv(binned_tt_csv_path)
        
        # Format for UUFT testing
        # We'll create a file with l, power, and error columns
        uuft_power_path = os.path.join(COSMOLOGICAL_DIR, "wmap_power_for_uuft.txt")
        
        with open(uuft_power_path, 'w') as f:
            f.write("# WMAP Power Spectrum for UUFT Testing\n")
            f.write("# l power error\n")
            for _, row in binned_tt.iterrows():
                f.write(f"{row['mean_l']} {row['power']} {row['error']}\n")
        
        logger.info(f"Created WMAP power spectrum for UUFT testing at {uuft_power_path}")
    except Exception as e:
        logger.error(f"Error creating WMAP power spectrum for UUFT testing: {e}")

def main():
    """Main function to prepare WMAP data for UUFT testing."""
    logger.info("Starting WMAP data preparation for UUFT testing")
    
    # Ensure directories exist
    ensure_directories()
    
    # Download WMAP data
    download_wmap_data()
    
    # Create WMAP parameters file
    create_wmap_params_file()
    
    # Create WMAP power spectrum file for UUFT testing
    create_wmap_power_spectrum_for_uuft()
    
    logger.info("WMAP data preparation complete")

if __name__ == "__main__":
    main()
