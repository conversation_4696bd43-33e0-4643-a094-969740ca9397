/**
 * DashboardCard Component
 *
 * A reusable card component for dashboard displays with enhanced styling and animations.
 * Now with mobile-friendly features.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * DashboardCard component
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {React.ReactNode} props.children - Card content
 * @param {string} [props.variant='default'] - Card variant (default, primary, success, warning, danger, info)
 * @param {boolean} [props.collapsible=false] - Whether the card is collapsible
 * @param {boolean} [props.defaultCollapsed=false] - Whether the card is collapsed by default
 * @param {boolean} [props.mobileCollapsible=true] - Whether the card is collapsible on mobile
 * @param {boolean} [props.mobileDefaultCollapsed=true] - Whether the card is collapsed by default on mobile
 * @param {boolean} [props.loading=false] - Whether the card is in loading state
 * @param {Object} [props.actions] - Card actions
 * @param {Function} [props.onRefresh] - Function to call when the refresh button is clicked
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} DashboardCard component
 */
const DashboardCard = ({
  title,
  children,
  variant = 'default',
  collapsible = false,
  defaultCollapsed = false,
  mobileCollapsible = true,
  mobileDefaultCollapsed = true,
  loading = false,
  actions,
  onRefresh,
  className = '',
  style = {}
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [isHovered, setIsHovered] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if device is mobile
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      // Set collapsed state based on device type and props
      if (mobile && mobileCollapsible) {
        setCollapsed(mobileDefaultCollapsed);
      } else if (!mobile && collapsible) {
        setCollapsed(defaultCollapsed);
      }
    };

    // Initial check
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [collapsible, defaultCollapsed, mobileCollapsible, mobileDefaultCollapsed]);

  // Determine card variant class
  const variantClass = {
    default: 'bg-white border-gray-200',
    primary: 'bg-blue-50 border-blue-200',
    success: 'bg-green-50 border-green-200',
    warning: 'bg-yellow-50 border-yellow-200',
    danger: 'bg-red-50 border-red-200',
    info: 'bg-cyan-50 border-cyan-200'
  }[variant] || 'bg-white border-gray-200';

  // Determine header variant class
  const headerVariantClass = {
    default: 'bg-gray-50 text-gray-700',
    primary: 'bg-blue-100 text-blue-700',
    success: 'bg-green-100 text-green-700',
    warning: 'bg-yellow-100 text-yellow-700',
    danger: 'bg-red-100 text-red-700',
    info: 'bg-cyan-100 text-cyan-700'
  }[variant] || 'bg-gray-50 text-gray-700';

  // Determine if card is collapsible based on device type
  const isCollapsible = isMobile ? mobileCollapsible : collapsible;

  // Toggle collapse state
  const toggleCollapse = () => {
    if (isCollapsible) {
      setCollapsed(!collapsed);
    }
  };

  return (
    <div
      className={`rounded-lg border shadow-sm transition-all duration-300 ${variantClass} ${className} ${isHovered ? 'shadow-md' : ''}`}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      data-testid="dashboard-card"
      data-mobile={isMobile ? 'true' : 'false'}
    >
      <div
        className={`flex items-center justify-between px-4 py-3 border-b ${headerVariantClass} ${isCollapsible ? 'cursor-pointer' : ''}`}
        onClick={isCollapsible ? toggleCollapse : undefined}
      >
        <h3 className={`font-semibold ${isMobile ? 'text-base' : 'text-lg'}`}>{title}</h3>
        <div className="flex items-center space-x-2">
          {actions && (
            <div className="flex items-center space-x-1">
              {actions}
            </div>
          )}
          {onRefresh && (
            <button
              className="p-1 rounded-full hover:bg-white/50 transition-colors duration-200"
              onClick={(e) => {
                e.stopPropagation();
                onRefresh();
              }}
              aria-label="Refresh"
              data-testid="refresh-button"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
              </svg>
            </button>
          )}
          {isCollapsible && (
            <button
              className="p-1 rounded-full hover:bg-white/50 transition-colors duration-200"
              onClick={(e) => {
                e.stopPropagation();
                toggleCollapse();
              }}
              aria-label={collapsed ? 'Expand' : 'Collapse'}
              data-testid="collapse-button"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'} transition-transform duration-200 ${collapsed ? 'rotate-180' : ''}`} viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>
      <div
        className={`overflow-hidden transition-all duration-300 ${collapsed ? 'max-h-0 p-0 opacity-0' : `${isMobile ? 'p-3' : 'p-4'} max-h-[2000px] opacity-100`}`}
        data-testid="card-content"
      >
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className={`animate-spin rounded-full ${isMobile ? 'h-6 w-6' : 'h-8 w-8'} border-b-2 border-gray-900`}></div>
          </div>
        ) : (
          children
        )}
      </div>
    </div>
  );
};

DashboardCard.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['default', 'primary', 'success', 'warning', 'danger', 'info']),
  collapsible: PropTypes.bool,
  defaultCollapsed: PropTypes.bool,
  mobileCollapsible: PropTypes.bool,
  mobileDefaultCollapsed: PropTypes.bool,
  loading: PropTypes.bool,
  actions: PropTypes.node,
  onRefresh: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default DashboardCard;
