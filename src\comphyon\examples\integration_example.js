/**
 * Comphyon Integration Example
 * 
 * This example demonstrates how to integrate the Comphyon measurement system
 * with existing NovaFuse components (CSDE, CSFE, CSME).
 */

// Import required modules
const { ComphyonMeter } = require('../exports');
const { TrinityCSDEEngine } = require('../../csde');
const { TrinityCSFEEngine } = require('../../csfe');

/**
 * Mock CSME Engine for example purposes
 * In a real implementation, this would be imported from the CSME module
 */
class MockCSMEEngine {
  constructor() {
    this.ethicalScore = 0.82;
  }
  
  getEthicalScore() {
    return this.ethicalScore;
  }
  
  setEthicalScore(score) {
    this.ethicalScore = Math.max(0, Math.min(1, score));
  }
}

/**
 * Run the integration example
 */
async function runExample() {
  console.log('=== Comphyon Integration Example ===');
  console.log('This example demonstrates how to integrate the Comphyon measurement system with NovaFuse components.');
  
  try {
    // Initialize CSDE engine
    console.log('Initializing CSDE engine...');
    const csdeEngine = new TrinityCSDEEngine({
      enableLogging: true,
      enableMetrics: true
    });
    
    // Add getPredictionRate method to CSDE engine if it doesn't exist
    if (!csdeEngine.getPredictionRate) {
      csdeEngine.getPredictionRate = function() {
        // In a real implementation, this would return the actual prediction rate
        // For this example, we'll return a simulated rate based on the performanceFactor
        return this.performanceFactor / 10;
      };
    }
    
    // Initialize CSFE engine
    console.log('Initializing CSFE engine...');
    const csfeEngine = new TrinityCSFEEngine({
      enableLogging: true,
      enableMetrics: true
    });
    
    // Add getPredictionRate method to CSFE engine if it doesn't exist
    if (!csfeEngine.getPredictionRate) {
      csfeEngine.getPredictionRate = function() {
        // In a real implementation, this would return the actual prediction rate
        // For this example, we'll return a simulated rate based on the performanceFactor
        return (this.performanceFactor || 3142) * 0.8 / 10;
      };
    }
    
    // Initialize CSME engine
    console.log('Initializing CSME engine...');
    const csmeEngine = new MockCSMEEngine();
    
    // Initialize Comphyon meter
    console.log('Initializing Comphyon meter...');
    const comphyonMeter = new ComphyonMeter({
      csdeEngine,
      csfeEngine,
      csmeEngine,
      enableLogging: true,
      updateInterval: 1000
    });
    
    // Listen for updates
    comphyonMeter.on('update', (data) => {
      console.log(`Comphyon: ${data.cph.toFixed(4)} Cph (CSDE: ${data.csdeRate.toFixed(2)}/s, CSFE: ${data.csfeRate.toFixed(2)}/s, CSME: ${data.csmeScore.toFixed(2)})`);
    });
    
    // Start the Comphyon meter
    console.log('Starting Comphyon meter...');
    comphyonMeter.start();
    
    // Simulate some activity
    console.log('Simulating activity...');
    
    // Run for 10 seconds
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    // Stop the Comphyon meter
    console.log('Stopping Comphyon meter...');
    comphyonMeter.stop();
    
    console.log('Example completed successfully!');
  } catch (error) {
    console.error('Error running example:', error);
  }
}

// If this script is run directly, run the example
if (require.main === module) {
  runExample().catch(console.error);
}

module.exports = {
  runExample
};
