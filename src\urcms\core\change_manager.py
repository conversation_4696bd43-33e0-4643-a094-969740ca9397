"""
Change Manager for the Universal Regulatory Change Management System.

This module provides the main manager for handling regulatory changes.
"""

import logging
from typing import Dict, List, Any, Optional, Callable

from .regulation_monitor import RegulationMonitor
from .impact_analyzer import ImpactAnalyzer
from .notification_service import NotificationService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChangeManager:
    """
    Main manager for handling regulatory changes.
    
    This class orchestrates the monitoring of regulatory changes, analyzing their
    impact, and notifying stakeholders.
    """
    
    def __init__(self):
        """Initialize the Change Manager."""
        logger.info("Initializing Change Manager")
        
        # Initialize the regulation monitor
        self.regulation_monitor = RegulationMonitor()
        
        # Initialize the impact analyzer
        self.impact_analyzer = ImpactAnalyzer()
        
        # Initialize the notification service
        self.notification_service = NotificationService()
        
        # Dictionary to store registered change handlers
        self.change_handlers: Dict[str, List[Callable]] = {}
        
        logger.info("Change Manager initialized")
    
    def register_change_handler(self, regulation_type: str, handler: Callable) -> None:
        """
        Register a handler for a specific regulation type.
        
        Args:
            regulation_type: The type of regulation
            handler: The handler function
        """
        if regulation_type not in self.change_handlers:
            self.change_handlers[regulation_type] = []
        
        self.change_handlers[regulation_type].append(handler)
        logger.info(f"Registered change handler for regulation type: {regulation_type}")
    
    def unregister_change_handler(self, regulation_type: str, handler: Callable) -> None:
        """
        Unregister a handler for a specific regulation type.
        
        Args:
            regulation_type: The type of regulation
            handler: The handler function
        """
        if regulation_type in self.change_handlers and handler in self.change_handlers[regulation_type]:
            self.change_handlers[regulation_type].remove(handler)
            logger.info(f"Unregistered change handler for regulation type: {regulation_type}")
    
    def start_monitoring(self) -> None:
        """Start monitoring for regulatory changes."""
        logger.info("Starting regulatory change monitoring")
        
        # Register a change handler with the regulation monitor
        self.regulation_monitor.register_change_handler(self._handle_regulatory_change)
        
        # Start the regulation monitor
        self.regulation_monitor.start()
    
    def stop_monitoring(self) -> None:
        """Stop monitoring for regulatory changes."""
        logger.info("Stopping regulatory change monitoring")
        
        # Stop the regulation monitor
        self.regulation_monitor.stop()
    
    def check_for_changes(self) -> List[Dict[str, Any]]:
        """
        Check for regulatory changes.
        
        Returns:
            List of regulatory changes
        """
        logger.info("Checking for regulatory changes")
        
        # Get regulatory changes
        changes = self.regulation_monitor.get_changes()
        
        # Process each change
        for change in changes:
            self._process_regulatory_change(change)
        
        return changes
    
    def _handle_regulatory_change(self, change: Dict[str, Any]) -> None:
        """
        Handle a regulatory change.
        
        Args:
            change: The regulatory change
        """
        logger.info(f"Handling regulatory change: {change.get('id')}")
        
        # Process the regulatory change
        self._process_regulatory_change(change)
    
    def _process_regulatory_change(self, change: Dict[str, Any]) -> None:
        """
        Process a regulatory change.
        
        Args:
            change: The regulatory change
        """
        logger.info(f"Processing regulatory change: {change.get('id')}")
        
        # Analyze the impact of the change
        impact = self.impact_analyzer.analyze_impact(change)
        
        # Notify stakeholders
        self.notification_service.send_notification(change, impact)
        
        # Call registered change handlers
        regulation_type = change.get('regulation_type')
        if regulation_type in self.change_handlers:
            for handler in self.change_handlers[regulation_type]:
                try:
                    handler(change, impact)
                except Exception as e:
                    logger.error(f"Error in change handler for {regulation_type}: {e}")
    
    def get_regulation_types(self) -> List[str]:
        """
        Get all supported regulation types.
        
        Returns:
            List of regulation types
        """
        return self.regulation_monitor.get_regulation_types()
    
    def get_regulations(self, regulation_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all regulations, optionally filtered by type.
        
        Args:
            regulation_type: Optional regulation type filter
            
        Returns:
            List of regulations
        """
        return self.regulation_monitor.get_regulations(regulation_type)
    
    def get_changes(self, regulation_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all regulatory changes, optionally filtered by type.
        
        Args:
            regulation_type: Optional regulation type filter
            
        Returns:
            List of regulatory changes
        """
        return self.regulation_monitor.get_changes(regulation_type)
    
    def get_impact_analysis(self, change_id: str) -> Dict[str, Any]:
        """
        Get the impact analysis for a specific regulatory change.
        
        Args:
            change_id: The ID of the regulatory change
            
        Returns:
            The impact analysis
            
        Raises:
            ValueError: If the change does not exist
        """
        # Get the change
        change = self.regulation_monitor.get_change(change_id)
        
        # Analyze the impact
        return self.impact_analyzer.analyze_impact(change)
