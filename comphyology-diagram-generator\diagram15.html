<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>15. Hardware Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 900px;
            height: 770px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>15. Hardware Architecture</h1>
    
    <div class="diagram-container">
        <!-- Hardware Architecture -->
        <div class="element" style="top: 50px; left: 350px; width: 300px; background-color: #e6f7ff; font-weight: bold; font-size: 20px;">
            Hardware Architecture
            <div class="element-number">1</div>
        </div>
        
        <!-- Tensor Processing Units -->
        <div class="element" style="top: 120px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Tensor Processing Units (TPUs)
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 180px; left: 100px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Tensor Core Array<br>Massively parallel processing units
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 180px; left: 350px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            High-Precision Arithmetic Units<br>64-bit floating-point units
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 180px; left: 600px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Tensor Memory Cache<br>Specialized memory architecture
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 240px; left: 350px; width: 200px; background-color: #f6ffed; font-size: 14px;">
            Tensor Instruction Set<br>Custom instructions for tensor operations
            <div class="element-number">6</div>
        </div>
        
        <!-- Fusion Processing Engines -->
        <div class="element" style="top: 300px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Fusion Processing Engines (FPEs)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 360px; left: 100px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Fusion Core<br>Implements fusion operator (⊕)
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 360px; left: 350px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Pattern Matching Units<br>Identifies related data patterns
            <div class="element-number">9</div>
        </div>
        
        <div class="element" style="top: 360px; left: 600px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Fusion Memory<br>Stores intermediate fusion results
            <div class="element-number">10</div>
        </div>
        
        <!-- Scaling Circuits -->
        <div class="element" style="top: 420px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Scaling Circuits
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 480px; left: 100px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            π10³ Constant Storage<br>High-precision constant memory
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 480px; left: 350px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            Multiplication Engine<br>High-throughput scaling operations
            <div class="element-number">13</div>
        </div>
        
        <div class="element" style="top: 480px; left: 600px; width: 200px; background-color: #f9f0ff; font-size: 14px;">
            Normalization Unit<br>Standardizes output values
            <div class="element-number">14</div>
        </div>
        
        <!-- System Interconnect -->
        <div class="element" style="top: 540px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            System Interconnect
            <div class="element-number">15</div>
        </div>
        
        <div class="element" style="top: 600px; left: 100px; width: 200px; background-color: #fff2e8; font-size: 14px;">
            High-Bandwidth Fabric<br>1.2 TB/s inter-component bandwidth
            <div class="element-number">16</div>
        </div>
        
        <div class="element" style="top: 600px; left: 350px; width: 200px; background-color: #fff2e8; font-size: 14px;">
            Low-Latency Switching<br><100ns component-to-component latency
            <div class="element-number">17</div>
        </div>
        
        <div class="element" style="top: 600px; left: 600px; width: 200px; background-color: #fff2e8; font-size: 14px;">
            Coherent Cache System<br>Maintains data consistency
            <div class="element-number">18</div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="element" style="top: 660px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Performance Metrics
            <div class="element-number">19</div>
        </div>
        
        <div class="element" style="top: 720px; left: 100px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Processing Power<br>3,142 TFLOPS tensor operations
            <div class="element-number">20</div>
        </div>
        
        <div class="element" style="top: 720px; left: 350px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Energy Efficiency<br>0.5 TFLOPS/watt
            <div class="element-number">21</div>
        </div>
        
        <div class="element" style="top: 720px; left: 600px; width: 200px; background-color: #fffbe6; font-size: 14px;">
            Accuracy<br>95% across all domains
            <div class="element-number">22</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect Hardware Architecture to Tensor Processing Units -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 110px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Tensor Processing Units to components -->
        <div class="connection" style="top: 170px; left: 350px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 190px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 170px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 170px; left: 650px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 180px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 210px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 240px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Fusion Processing Engines -->
        <div class="connection" style="top: 270px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 290px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Fusion Processing Engines to components -->
        <div class="connection" style="top: 350px; left: 350px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 190px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 350px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 350px; left: 650px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 360px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Scaling Circuits -->
        <div class="connection" style="top: 390px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 410px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Scaling Circuits to components -->
        <div class="connection" style="top: 470px; left: 350px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 480px; left: 190px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 470px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 480px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 470px; left: 650px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 480px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to System Interconnect -->
        <div class="connection" style="top: 510px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 530px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect System Interconnect to components -->
        <div class="connection" style="top: 590px; left: 350px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 600px; left: 190px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 590px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 600px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 590px; left: 650px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 600px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Performance Metrics -->
        <div class="connection" style="top: 630px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 650px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Performance Metrics to components -->
        <div class="connection" style="top: 710px; left: 350px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 190px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 710px; left: 500px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 440px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 710px; left: 650px; width: 50px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 720px; left: 690px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
    </div>
</body>
</html>
