/**
 * NovaFuse Universal API Connector - Batch Processor
 * 
 * This module provides utilities for batch processing operations.
 */

const { createLogger } = require('./logger');
const requestQueue = require('./request-queue');

const logger = createLogger('batch-processor');

/**
 * Batch processor for handling large operations
 */
class BatchProcessor {
  constructor(options = {}) {
    this.options = {
      batchSize: options.batchSize || 100,
      concurrency: options.concurrency || 5,
      retries: options.retries || 3,
      ...options
    };
    
    logger.info('Batch processor initialized', {
      batchSize: this.options.batchSize,
      concurrency: this.options.concurrency,
      retries: this.options.retries
    });
  }
  
  /**
   * Process items in batches
   * 
   * @param {Array} items - The items to process
   * @param {Function} processFn - The function to process each item
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - The processing results
   */
  async processBatch(items, processFn, options = {}) {
    const batchSize = options.batchSize || this.options.batchSize;
    const concurrency = options.concurrency || this.options.concurrency;
    const retries = options.retries !== undefined ? options.retries : this.options.retries;
    
    logger.info('Starting batch processing', {
      itemCount: items.length,
      batchSize,
      concurrency,
      retries
    });
    
    const results = {
      total: items.length,
      processed: 0,
      succeeded: 0,
      failed: 0,
      errors: [],
      items: []
    };
    
    // Split items into batches
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    // Process batches with concurrency limit
    let batchIndex = 0;
    const batchPromises = [];
    
    while (batchIndex < batches.length) {
      // Fill up to concurrency limit
      while (batchPromises.length < concurrency && batchIndex < batches.length) {
        const batch = batches[batchIndex];
        
        batchPromises.push(
          this._processBatchItems(batch, processFn, retries)
            .then(batchResults => {
              // Update results
              results.processed += batchResults.processed;
              results.succeeded += batchResults.succeeded;
              results.failed += batchResults.failed;
              results.errors = [...results.errors, ...batchResults.errors];
              results.items = [...results.items, ...batchResults.items];
              
              logger.debug('Batch completed', {
                batchIndex,
                processed: batchResults.processed,
                succeeded: batchResults.succeeded,
                failed: batchResults.failed
              });
              
              // Remove from active promises
              const index = batchPromises.indexOf(batchPromises.find(p => p.batchIndex === batchIndex));
              if (index !== -1) {
                batchPromises.splice(index, 1);
              }
            })
            .catch(error => {
              logger.error('Batch processing error', {
                batchIndex,
                error: error.message
              });
              
              // Update results for entire batch as failed
              results.processed += batch.length;
              results.failed += batch.length;
              results.errors.push({
                batchIndex,
                error: error.message
              });
              
              // Add failed items
              for (const item of batch) {
                results.items.push({
                  item,
                  success: false,
                  error: error.message
                });
              }
              
              // Remove from active promises
              const index = batchPromises.indexOf(batchPromises.find(p => p.batchIndex === batchIndex));
              if (index !== -1) {
                batchPromises.splice(index, 1);
              }
            })
        );
        
        // Add batch index to promise for tracking
        batchPromises[batchPromises.length - 1].batchIndex = batchIndex;
        
        batchIndex++;
      }
      
      // Wait for at least one batch to complete
      if (batchPromises.length > 0) {
        await Promise.race(batchPromises);
      }
    }
    
    // Wait for all remaining batches to complete
    if (batchPromises.length > 0) {
      await Promise.all(batchPromises);
    }
    
    logger.info('Batch processing completed', {
      total: results.total,
      processed: results.processed,
      succeeded: results.succeeded,
      failed: results.failed
    });
    
    return results;
  }
  
  /**
   * Process a batch of items
   * 
   * @param {Array} batch - The batch of items
   * @param {Function} processFn - The function to process each item
   * @param {number} retries - Number of retries
   * @returns {Promise<Object>} - The batch results
   * @private
   */
  async _processBatchItems(batch, processFn, retries) {
    const batchResults = {
      processed: 0,
      succeeded: 0,
      failed: 0,
      errors: [],
      items: []
    };
    
    // Process each item in the batch
    const itemPromises = batch.map(item => {
      return requestQueue.enqueue(
        async () => {
          try {
            const result = await processFn(item);
            
            batchResults.processed++;
            batchResults.succeeded++;
            
            batchResults.items.push({
              item,
              success: true,
              result
            });
            
            return result;
          } catch (error) {
            batchResults.processed++;
            batchResults.failed++;
            
            batchResults.errors.push({
              item,
              error: error.message
            });
            
            batchResults.items.push({
              item,
              success: false,
              error: error.message
            });
            
            throw error;
          }
        },
        { retries }
      ).catch(error => {
        // Error already handled in the task
        logger.debug('Item processing error', {
          error: error.message
        });
      });
    });
    
    // Wait for all items to complete
    await Promise.all(itemPromises);
    
    return batchResults;
  }
}

// Create singleton instance
const batchProcessor = new BatchProcessor();

module.exports = batchProcessor;
