/**
 * Branding Controller
 * 
 * This controller handles API requests related to organization branding.
 */

const BrandingService = require('../services/BrandingService');
const { ValidationError } = require('../utils/errors');

class BrandingController {
  constructor() {
    this.brandingService = new BrandingService();
  }

  /**
   * Get organization branding
   */
  async getOrganizationBranding(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const branding = await this.brandingService.getOrganizationBranding(organizationId);
      res.json(branding);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update organization branding
   */
  async updateOrganizationBranding(req, res, next) {
    try {
      const { organizationId } = req.params;
      const data = req.body;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Branding data is required');
      }
      
      const branding = await this.brandingService.updateOrganizationBranding(
        organizationId, 
        data, 
        req.user.id
      );
      
      res.json(branding);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reset organization branding to default
   */
  async resetOrganizationBranding(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const result = await this.brandingService.resetOrganizationBranding(
        organizationId, 
        req.user.id
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Upload branding asset
   */
  async uploadBrandingAsset(req, res, next) {
    try {
      const { organizationId } = req.params;
      const { assetType } = req.query;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!assetType) {
        throw new ValidationError('Asset type is required');
      }
      
      if (!req.file) {
        throw new ValidationError('File is required');
      }
      
      const result = await this.brandingService.uploadBrandingAsset(
        organizationId,
        assetType,
        req.file.buffer,
        req.file.originalname,
        req.user.id
      );
      
      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get branding asset
   */
  async getBrandingAsset(req, res, next) {
    try {
      const { fileName } = req.params;
      
      if (!fileName) {
        throw new ValidationError('File name is required');
      }
      
      const asset = await this.brandingService.getBrandingAsset(fileName);
      
      // Set content type
      res.set('Content-Type', asset.contentType);
      res.send(asset.buffer);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete branding asset
   */
  async deleteBrandingAsset(req, res, next) {
    try {
      const { organizationId, fileName } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!fileName) {
        throw new ValidationError('File name is required');
      }
      
      const result = await this.brandingService.deleteBrandingAsset(
        organizationId,
        fileName,
        req.user.id
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get complete branding package
   */
  async getBrandingPackage(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const package = await this.brandingService.getBrandingPackage(organizationId);
      res.json(package);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new BrandingController();
