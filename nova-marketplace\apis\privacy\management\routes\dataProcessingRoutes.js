/**
 * Data Processing Activity Routes
 * 
 * This file defines the routes for data processing activities.
 */

const express = require('express');
const router = express.Router();
const { dataProcessingController } = require('../controllers');

// Get all data processing activities
router.get('/', dataProcessingController.getAllDataProcessingActivities);

// Get a specific data processing activity by ID
router.get('/:id', dataProcessingController.getDataProcessingActivityById);

// Create a new data processing activity
router.post('/', dataProcessingController.createDataProcessingActivity);

// Update a data processing activity
router.put('/:id', dataProcessingController.updateDataProcessingActivity);

// Delete a data processing activity
router.delete('/:id', dataProcessingController.deleteDataProcessingActivity);

module.exports = router;
