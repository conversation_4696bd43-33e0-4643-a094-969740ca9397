# NovaDNA API Documentation

This document provides an overview of the NovaDNA API endpoints and how to use them.

## Authentication

### Service Authentication

```
POST /api/auth/service
```

Authenticate as an emergency service to access the API.

**Request Body:**
```json
{
  "apiKey": "your-api-key",
  "apiSecret": "your-api-secret",
  "context": {
    "location": {
      "type": "HOSPITAL",
      "name": "General Hospital"
    },
    "device": {
      "type": "TABLET",
      "id": "device-123"
    }
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "token": {
      "tokenId": "token-123",
      "value": "service-token-123",
      "expiresIn": 3600
    },
    "service": {
      "serviceId": "service-123",
      "name": "Emergency Service",
      "type": "PARAMEDIC"
    }
  }
}
```

### User Authentication

```
POST /api/auth/user
```

Authenticate as a user to access the API.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "token": "user-token-123",
    "user": {
      "id": "user-123",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "expiresIn": 3600
  }
}
```

## Emergency Profiles

### Create Profile

```
POST /api/profiles
```

Create a new emergency medical profile.

**Request Body:**
```json
{
  "fullName": "John Doe",
  "dateOfBirth": "1980-01-01",
  "bloodType": "A+",
  "emergencyContacts": [
    {
      "name": "Jane Doe",
      "relationship": "Spouse",
      "phone": "************"
    }
  ],
  "allergies": [
    {
      "name": "Peanuts",
      "severity": "Severe"
    }
  ],
  "medications": [
    {
      "name": "Aspirin",
      "dosage": "100mg",
      "frequency": "Daily"
    }
  ],
  "medicalConditions": [
    {
      "name": "Hypertension",
      "diagnosisDate": "2010-05-15"
    }
  ],
  "dnr": false,
  "organDonor": true
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "profileId": "profile-123",
    "createdAt": "2023-04-27T12:34:56.789Z",
    "verificationId": "verification-123"
  }
}
```

### Get Profile

```
GET /api/profiles/:profileId
```

Get an emergency profile by ID.

**Response:**
```json
{
  "status": "success",
  "data": {
    "profileId": "profile-123",
    "fullName": "John Doe",
    "dateOfBirth": "1980-01-01",
    "bloodType": "A+",
    "emergencyContacts": [
      {
        "name": "Jane Doe",
        "relationship": "Spouse",
        "phone": "************"
      }
    ],
    "allergies": [
      {
        "name": "Peanuts",
        "severity": "Severe"
      }
    ],
    "medications": [
      {
        "name": "Aspirin",
        "dosage": "100mg",
        "frequency": "Daily"
      }
    ],
    "medicalConditions": [
      {
        "name": "Hypertension",
        "diagnosisDate": "2010-05-15"
      }
    ],
    "dnr": false,
    "organDonor": true
  }
}
```

### Update Profile

```
PUT /api/profiles/:profileId
```

Update an existing emergency profile.

**Request Body:**
```json
{
  "bloodType": "B+",
  "medications": [
    {
      "name": "Aspirin",
      "dosage": "100mg",
      "frequency": "Daily"
    },
    {
      "name": "Lisinopril",
      "dosage": "10mg",
      "frequency": "Daily"
    }
  ]
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "profileId": "profile-123",
    "updatedAt": "2023-04-27T13:45:56.789Z",
    "verificationId": "verification-456"
  }
}
```

### Get Access Logs

```
GET /api/profiles/:profileId/access-logs
```

Get access logs for a profile.

**Query Parameters:**
- `startDate` - Start date for filtering logs
- `endDate` - End date for filtering logs
- `serviceId` - Filter by service ID
- `accessLevel` - Filter by access level
- `limit` - Limit the number of logs returned

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "accessId": "access-123",
      "profileId": "profile-123",
      "timestamp": "2023-04-27T12:34:56.789Z",
      "serviceId": "service-123",
      "contextType": "MEDICAL",
      "accessLevel": "STANDARD",
      "success": true
    }
  ]
}
```

## Emergency Access

### Access Profile

```
POST /api/access/emergency
```

Access a profile in an emergency.

**Request Body:**
```json
{
  "formFactorId": "form-factor-123",
  "accessCode": "ABC123",
  "context": {
    "emergencyType": "MEDICAL",
    "emergencySeverity": "HIGH",
    "responderType": "PARAMEDIC",
    "locationType": "AMBULANCE",
    "location": {
      "coordinates": [40.7128, -74.0060],
      "address": "123 Main St"
    }
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "profile": {
      "profileId": "profile-123",
      "fullName": "John Doe",
      "dateOfBirth": "1980-01-01",
      "bloodType": "A+",
      "emergencyContacts": [
        {
          "name": "Jane Doe",
          "relationship": "Spouse",
          "phone": "************"
        }
      ],
      "allergies": [
        {
          "name": "Peanuts",
          "severity": "Severe"
        }
      ],
      "medications": [
        {
          "name": "Aspirin",
          "dosage": "100mg",
          "frequency": "Daily"
        }
      ],
      "medicalConditions": [
        {
          "name": "Hypertension",
          "diagnosisDate": "2010-05-15"
        }
      ],
      "dnr": false,
      "organDonor": true
    },
    "accessedAt": "2023-04-27T12:34:56.789Z"
  }
}
```

### Emergency Override

```
POST /api/access/override
```

Emergency override access to a profile.

**Request Body:**
```json
{
  "profileId": "profile-123",
  "reason": "Patient unconscious, immediate access needed",
  "emergencyType": "TRAUMA",
  "severityLevel": "CRITICAL",
  "location": {
    "type": "EMERGENCY_SCENE",
    "coordinates": [40.7128, -74.0060],
    "address": "123 Main St"
  },
  "deviceInfo": {
    "type": "TABLET",
    "id": "device-123"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "profile": {
      "profileId": "profile-123",
      "fullName": "John Doe",
      "dateOfBirth": "1980-01-01",
      "bloodType": "A+",
      "emergencyContacts": [
        {
          "name": "Jane Doe",
          "relationship": "Spouse",
          "phone": "************"
        }
      ],
      "allergies": [
        {
          "name": "Peanuts",
          "severity": "Severe"
        }
      ],
      "medications": [
        {
          "name": "Aspirin",
          "dosage": "100mg",
          "frequency": "Daily"
        }
      ],
      "medicalConditions": [
        {
          "name": "Hypertension",
          "diagnosisDate": "2010-05-15"
        }
      ],
      "dnr": false,
      "organDonor": true,
      "insuranceInfo": {
        "provider": "Health Insurance Co",
        "policyNumber": "12345678"
      },
      "primaryCareProvider": {
        "name": "Dr. Smith",
        "phone": "************"
      }
    },
    "override": {
      "overrideId": "override-123",
      "expiresAt": "2023-04-27T13:34:56.789Z",
      "status": "ACTIVE"
    },
    "accessedAt": "2023-04-27T12:34:56.789Z"
  }
}
```

### Complete Override

```
POST /api/access/override/:overrideId/complete
```

Complete an emergency override.

**Request Body:**
```json
{
  "token": "override-token-123",
  "result": {
    "profileAccessed": true,
    "accessLevel": "FULL",
    "accessDuration": 120
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "overrideId": "override-123",
    "status": "COMPLETED",
    "completedAt": "2023-04-27T12:36:56.789Z"
  }
}
```

### Review Override

```
POST /api/access/override/:overrideId/review
```

Review an emergency override.

**Request Body:**
```json
{
  "status": "APPROVED",
  "notes": "Appropriate emergency access"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "overrideId": "override-123",
    "reviewId": "review-123",
    "status": "APPROVED",
    "reviewedAt": "2023-04-27T14:34:56.789Z"
  }
}
```

## Form Factors

### Generate QR Code

```
POST /api/form-factors/qr
```

Generate a QR code for a profile.

**Request Body:**
```json
{
  "profileId": "profile-123",
  "accessLevel": "standard",
  "metadata": {
    "name": "Primary QR Code",
    "description": "For wallet or phone case"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "formFactorId": "form-factor-123",
    "profileId": "profile-123",
    "type": "QR_CODE",
    "qrCodeDataUrl": "data:image/png;base64,...",
    "accessCode": "ABC123",
    "createdAt": "2023-04-27T12:34:56.789Z"
  }
}
```

### Generate Wristband

```
POST /api/form-factors/wristband
```

Generate a wristband for a profile.

**Request Body:**
```json
{
  "profileId": "profile-123",
  "accessLevel": "standard",
  "metadata": {
    "name": "Primary Wristband",
    "color": "Blue"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "formFactorId": "form-factor-456",
    "profileId": "profile-123",
    "type": "WRISTBAND",
    "qrCodeDataUrl": "data:image/png;base64,...",
    "ndefMessage": {
      "records": [
        {
          "recordType": "uri",
          "data": "https://novadna.novafuse.io/emergency/..."
        }
      ]
    },
    "accessCode": "DEF456",
    "createdAt": "2023-04-27T12:34:56.789Z"
  }
}
```

### Generate Vehicle Sticker

```
POST /api/form-factors/vehicle
```

Generate a vehicle sticker for a profile.

**Request Body:**
```json
{
  "profileId": "profile-123",
  "accessLevel": "standard",
  "vehicleInfo": {
    "make": "Toyota",
    "model": "Camry",
    "year": 2020,
    "licensePlate": "ABC123"
  },
  "metadata": {
    "name": "Primary Vehicle",
    "position": "Windshield"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "formFactorId": "form-factor-789",
    "profileId": "profile-123",
    "type": "VEHICLE_STICKER",
    "vehicleInfo": {
      "make": "Toyota",
      "model": "Camry",
      "year": 2020,
      "licensePlate": "ABC123"
    },
    "accessCode": "GHI789",
    "createdAt": "2023-04-27T12:34:56.789Z"
  }
}
```

### Get Form Factors

```
GET /api/form-factors/profile/:profileId
```

Get all form factors for a profile.

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "formFactorId": "form-factor-123",
      "type": "QR_CODE",
      "accessLevel": "standard",
      "createdAt": "2023-04-27T12:34:56.789Z",
      "metadata": {
        "name": "Primary QR Code",
        "description": "For wallet or phone case"
      }
    },
    {
      "formFactorId": "form-factor-456",
      "type": "WRISTBAND",
      "accessLevel": "standard",
      "createdAt": "2023-04-27T12:34:56.789Z",
      "metadata": {
        "name": "Primary Wristband",
        "color": "Blue"
      }
    }
  ]
}
```

### Revoke Form Factor

```
DELETE /api/form-factors/:formFactorId
```

Revoke a form factor.

**Response:**
```json
{
  "status": "success",
  "data": {
    "formFactorId": "form-factor-123",
    "revoked": true,
    "revokedAt": "2023-04-27T14:34:56.789Z"
  }
}
```

## Security

### Get Security Incidents

```
GET /api/security/incidents
```

Get security incidents.

**Query Parameters:**
- `startDate` - Start date for filtering incidents
- `endDate` - End date for filtering incidents
- `severity` - Filter by severity
- `type` - Filter by type
- `limit` - Limit the number of incidents returned

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "incidentId": "incident-123",
      "type": "ANOMALOUS_ACCESS",
      "description": "Anomalous access detected for profile profile-123",
      "severity": "MEDIUM",
      "timestamp": "2023-04-27T12:34:56.789Z",
      "status": "OPEN",
      "details": {
        "profileId": "profile-123",
        "anomalyScore": 0.85
      }
    }
  ]
}
```

### Track Security Event

```
POST /api/security/track-event
```

Track a security event.

**Request Body:**
```json
{
  "eventType": "ACCESS",
  "profileId": "profile-123",
  "accessType": "STANDARD",
  "location": {
    "city": "New York",
    "country": "USA"
  },
  "device": {
    "type": "TABLET",
    "model": "iPad Pro"
  }
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "eventId": "event-123",
    "anomalyScore": 0.2,
    "threatDetected": false,
    "timestamp": "2023-04-27T12:34:56.789Z"
  }
}
```

### Get Security Dashboard

```
GET /api/security/dashboard
```

Get security dashboard data.

**Response:**
```json
{
  "status": "success",
  "data": {
    "activeIncidents": 3,
    "accessAttempts": 1245,
    "anomaliesDetected": 37,
    "securityScore": 0.92,
    "incidents": [
      {
        "incidentId": "incident-123",
        "type": "ANOMALOUS_ACCESS",
        "description": "Anomalous access detected for profile profile-123",
        "severity": "MEDIUM",
        "timestamp": "2023-04-27T12:34:56.789Z",
        "status": "OPEN"
      }
    ],
    "accessLog": [
      {
        "eventId": "event-123",
        "profileId": "profile-123",
        "timestamp": "2023-04-27T12:34:56.789Z",
        "serviceId": "service-123",
        "accessType": "STANDARD",
        "anomalyScore": 0.2
      }
    ]
  }
}
```
