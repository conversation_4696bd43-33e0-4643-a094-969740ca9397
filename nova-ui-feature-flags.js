/**
 * NovaUI Feature Flags Configuration
 * 
 * This file defines the feature flags for each NovaFuse product.
 * Feature flags are used to enable/disable features based on the product tier.
 */

const featureFlags = {
  // NovaPrime features (all enabled)
  novaPrime: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: true,
      esg: true
    },
    
    // Advanced features
    advanced: {
      aiAssistant: true,
      predictiveAnalytics: true,
      automatedRemediation: true,
      customIntegrations: true
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: true,
      organizationSettings: true,
      auditLogs: true
    },
    
    // Learning features
    learning: {
      gamification: true,
      trainingModules: true,
      certifications: true,
      knowledgeBase: true
    }
  },
  
  // NovaCore features (limited)
  novaCore: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: false,
      reports: true,
      customization: false
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: false,
      esg: false
    },
    
    // Advanced features
    advanced: {
      aiAssistant: false,
      predictiveAnalytics: false,
      automatedRemediation: false,
      customIntegrations: false
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: false,
      organizationSettings: false,
      auditLogs: false
    },
    
    // Learning features
    learning: {
      gamification: false,
      trainingModules: false,
      certifications: false,
      knowledgeBase: true
    }
  },
  
  // NovaShield features (security-focused)
  novaShield: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true
    },
    
    // GRC features
    grc: {
      privacy: false,
      security: true,
      compliance: true,
      control: true,
      esg: false
    },
    
    // Advanced features
    advanced: {
      aiAssistant: false,
      predictiveAnalytics: true,
      automatedRemediation: true,
      customIntegrations: false
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: true,
      organizationSettings: true,
      auditLogs: true
    },
    
    // Learning features
    learning: {
      gamification: false,
      trainingModules: true,
      certifications: false,
      knowledgeBase: true
    }
  },
  
  // NovaLearn features (learning-focused)
  novaLearn: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: false
    },
    
    // GRC features
    grc: {
      privacy: false,
      security: false,
      compliance: false,
      control: false,
      esg: false
    },
    
    // Advanced features
    advanced: {
      aiAssistant: true,
      predictiveAnalytics: false,
      automatedRemediation: false,
      customIntegrations: false
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: false,
      organizationSettings: false,
      auditLogs: false
    },
    
    // Learning features
    learning: {
      gamification: true,
      trainingModules: true,
      certifications: true,
      knowledgeBase: true
    }
  },
  
  // NovaAssistAI features (AI-focused)
  novaAssistAI: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: false
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: true,
      esg: true
    },
    
    // Advanced features
    advanced: {
      aiAssistant: true,
      predictiveAnalytics: true,
      automatedRemediation: true,
      customIntegrations: true
    },
    
    // Administration features
    administration: {
      userManagement: false,
      roleManagement: false,
      organizationSettings: false,
      auditLogs: false
    },
    
    // Learning features
    learning: {
      gamification: false,
      trainingModules: false,
      certifications: false,
      knowledgeBase: true
    }
  }
};

/**
 * Get feature flag value
 * @param {string} product - The product name (novaPrime, novaCore, etc.)
 * @param {string} category - The feature category (dashboard, grc, etc.)
 * @param {string} feature - The feature name (overview, analytics, etc.)
 * @returns {boolean} - Whether the feature is enabled
 */
function getFeatureFlag(product, category, feature) {
  if (!featureFlags[product]) {
    console.warn(`Product "${product}" not found in feature flags`);
    return false;
  }
  
  if (!featureFlags[product][category]) {
    console.warn(`Category "${category}" not found in product "${product}"`);
    return false;
  }
  
  if (typeof featureFlags[product][category][feature] === 'undefined') {
    console.warn(`Feature "${feature}" not found in category "${category}" of product "${product}"`);
    return false;
  }
  
  return featureFlags[product][category][feature];
}

/**
 * Check if a feature is enabled
 * @param {string} product - The product name (novaPrime, novaCore, etc.)
 * @param {string} category - The feature category (dashboard, grc, etc.)
 * @param {string} feature - The feature name (overview, analytics, etc.)
 * @returns {boolean} - Whether the feature is enabled
 */
function isFeatureEnabled(product, category, feature) {
  return getFeatureFlag(product, category, feature);
}

module.exports = {
  featureFlags,
  getFeatureFlag,
  isFeatureEnabled
};
