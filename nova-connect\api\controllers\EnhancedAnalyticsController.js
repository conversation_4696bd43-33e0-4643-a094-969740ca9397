/**
 * Enhanced Analytics Controller
 * 
 * This controller provides advanced analytics functionality including:
 * - User analytics
 * - Connector analytics
 * - Compliance analytics
 * - Predictive analytics
 * - Real-time analytics
 * - Custom analytics
 */

const AnalyticsService = require('../services/AnalyticsService');
const EnhancedAnalyticsService = require('../services/EnhancedAnalyticsService');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const logger = require('../../config/logger');

class EnhancedAnalyticsController {
  constructor() {
    this.analyticsService = new AnalyticsService();
    this.enhancedAnalyticsService = new EnhancedAnalyticsService();
  }

  /**
   * Get user analytics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getUserAnalytics(req, res, next) {
    try {
      const {
        startDate,
        endDate,
        userId,
        teamId,
        groupBy = 'day',
        metrics = ['logins', 'api_calls', 'active_time']
      } = req.query;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Validate groupBy parameter
      const validGroupBy = ['hour', 'day', 'week', 'month'];
      if (!validGroupBy.includes(groupBy)) {
        throw new ValidationError(`Invalid groupBy parameter. Must be one of: ${validGroupBy.join(', ')}`);
      }

      // Get user analytics
      const analytics = await this.enhancedAnalyticsService.getUserAnalytics({
        startDate,
        endDate,
        userId,
        teamId,
        groupBy,
        metrics
      });

      res.json(analytics);
    } catch (error) {
      logger.error('Error getting user analytics', { error: error.message });
      next(error);
    }
  }

  /**
   * Get connector analytics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getConnectorAnalytics(req, res, next) {
    try {
      const {
        startDate,
        endDate,
        connectorId,
        connectorType,
        groupBy = 'day',
        metrics = ['executions', 'success_rate', 'average_duration', 'error_rate']
      } = req.query;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Validate groupBy parameter
      const validGroupBy = ['hour', 'day', 'week', 'month'];
      if (!validGroupBy.includes(groupBy)) {
        throw new ValidationError(`Invalid groupBy parameter. Must be one of: ${validGroupBy.join(', ')}`);
      }

      // Get connector analytics
      const analytics = await this.enhancedAnalyticsService.getConnectorAnalytics({
        startDate,
        endDate,
        connectorId,
        connectorType,
        groupBy,
        metrics
      });

      res.json(analytics);
    } catch (error) {
      logger.error('Error getting connector analytics', { error: error.message });
      next(error);
    }
  }

  /**
   * Get compliance analytics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getComplianceAnalytics(req, res, next) {
    try {
      const {
        startDate,
        endDate,
        framework,
        controlId,
        groupBy = 'day',
        metrics = ['compliance_score', 'findings', 'remediation_rate']
      } = req.query;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Validate groupBy parameter
      const validGroupBy = ['day', 'week', 'month', 'quarter'];
      if (!validGroupBy.includes(groupBy)) {
        throw new ValidationError(`Invalid groupBy parameter. Must be one of: ${validGroupBy.join(', ')}`);
      }

      // Get compliance analytics
      const analytics = await this.enhancedAnalyticsService.getComplianceAnalytics({
        startDate,
        endDate,
        framework,
        controlId,
        groupBy,
        metrics
      });

      res.json(analytics);
    } catch (error) {
      logger.error('Error getting compliance analytics', { error: error.message });
      next(error);
    }
  }

  /**
   * Get predictive analytics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getPredictiveAnalytics(req, res, next) {
    try {
      const {
        metric,
        horizon = 30,
        confidenceInterval = 0.95,
        modelType = 'auto'
      } = req.query;

      // Validate required parameters
      if (!metric) {
        throw new ValidationError('Metric parameter is required');
      }

      // Validate horizon parameter
      if (isNaN(parseInt(horizon)) || parseInt(horizon) <= 0) {
        throw new ValidationError('Horizon must be a positive integer');
      }

      // Validate confidence interval parameter
      if (isNaN(parseFloat(confidenceInterval)) || parseFloat(confidenceInterval) <= 0 || parseFloat(confidenceInterval) >= 1) {
        throw new ValidationError('Confidence interval must be between 0 and 1');
      }

      // Validate model type parameter
      const validModelTypes = ['auto', 'arima', 'exponential_smoothing', 'prophet', 'neural_network'];
      if (!validModelTypes.includes(modelType)) {
        throw new ValidationError(`Invalid modelType parameter. Must be one of: ${validModelTypes.join(', ')}`);
      }

      // Get predictive analytics
      const analytics = await this.enhancedAnalyticsService.getPredictiveAnalytics({
        metric,
        horizon: parseInt(horizon),
        confidenceInterval: parseFloat(confidenceInterval),
        modelType
      });

      res.json(analytics);
    } catch (error) {
      logger.error('Error getting predictive analytics', { error: error.message });
      next(error);
    }
  }

  /**
   * Get real-time analytics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getRealTimeAnalytics(req, res, next) {
    try {
      const {
        metrics = ['active_users', 'api_calls_per_minute', 'error_rate', 'response_time']
      } = req.query;

      // Get real-time analytics
      const analytics = await this.enhancedAnalyticsService.getRealTimeAnalytics({
        metrics
      });

      res.json(analytics);
    } catch (error) {
      logger.error('Error getting real-time analytics', { error: error.message });
      next(error);
    }
  }

  /**
   * Get custom analytics
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getCustomAnalytics(req, res, next) {
    try {
      const {
        metrics = [],
        dimensions = [],
        filters = {},
        startDate,
        endDate,
        groupBy = 'day',
        limit = 1000,
        sort = {}
      } = req.body;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Validate groupBy parameter
      const validGroupBy = ['hour', 'day', 'week', 'month', 'quarter', 'year'];
      if (!validGroupBy.includes(groupBy)) {
        throw new ValidationError(`Invalid groupBy parameter. Must be one of: ${validGroupBy.join(', ')}`);
      }

      // Get custom analytics
      const analytics = await this.enhancedAnalyticsService.getCustomAnalytics({
        metrics,
        dimensions,
        filters,
        startDate,
        endDate,
        groupBy,
        limit,
        sort
      });

      res.json(analytics);
    } catch (error) {
      logger.error('Error getting custom analytics', { error: error.message });
      next(error);
    }
  }

  /**
   * Get analytics dashboard
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAnalyticsDashboard(req, res, next) {
    try {
      const {
        startDate,
        endDate,
        refresh = false
      } = req.query;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Get analytics dashboard
      const dashboard = await this.enhancedAnalyticsService.getAnalyticsDashboard({
        startDate,
        endDate,
        refresh: refresh === 'true'
      });

      res.json(dashboard);
    } catch (error) {
      logger.error('Error getting analytics dashboard', { error: error.message });
      next(error);
    }
  }

  /**
   * Export analytics data
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async exportAnalytics(req, res, next) {
    try {
      const {
        type,
        format = 'csv',
        startDate,
        endDate,
        ...params
      } = req.query;

      // Validate required parameters
      if (!type) {
        throw new ValidationError('Type parameter is required');
      }

      // Validate type parameter
      const validTypes = ['user', 'connector', 'compliance', 'custom'];
      if (!validTypes.includes(type)) {
        throw new ValidationError(`Invalid type parameter. Must be one of: ${validTypes.join(', ')}`);
      }

      // Validate format parameter
      const validFormats = ['csv', 'json', 'excel'];
      if (!validFormats.includes(format)) {
        throw new ValidationError(`Invalid format parameter. Must be one of: ${validFormats.join(', ')}`);
      }

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Export analytics
      const { data, filename, contentType } = await this.enhancedAnalyticsService.exportAnalytics({
        type,
        format,
        startDate,
        endDate,
        ...params
      });

      // Set headers for file download
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      // Send data
      res.send(data);
    } catch (error) {
      logger.error('Error exporting analytics', { error: error.message });
      next(error);
    }
  }
}

module.exports = new EnhancedAnalyticsController();
