<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse UAC Error Handling Demo</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      color: #333;
    }
    h1 {
      color: #0066cc;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }
    h2 {
      color: #0066cc;
      margin-top: 30px;
    }
    .error-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .error-card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 15px;
      background-color: #f9f9f9;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      transition: transform 0.2s, box-shadow 0.2s;
    }
    .error-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .error-card h3 {
      margin-top: 0;
      color: #0066cc;
    }
    .error-card p {
      margin-bottom: 15px;
      color: #666;
    }
    button {
      background-color: #0066cc;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
      transition: background-color 0.2s;
    }
    button:hover {
      background-color: #0055aa;
    }
    .response {
      margin-top: 30px;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      background-color: #f5f5f5;
      display: none;
    }
    .response h2 {
      margin-top: 0;
    }
    pre {
      background-color: #f0f0f0;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
    }
    .success {
      border-color: #4CAF50;
      background-color: #f0fff0;
    }
    .error {
      border-color: #f44336;
      background-color: #fff0f0;
    }
  </style>
</head>
<body>
  <h1>NovaFuse UAC Error Handling Demo</h1>
  
  <p>
    This page demonstrates the comprehensive error handling capabilities of the NovaFuse Universal API Connector.
    Click on any of the buttons below to trigger different types of errors and see how they are handled.
  </p>
  
  <div class="error-grid">
    <div class="error-card">
      <h3>Validation Error</h3>
      <p>Demonstrates how validation errors are handled with detailed field information.</p>
      <button onclick="testEndpoint('/error-demo/validation-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Missing Required Field</h3>
      <p>Shows how missing required fields are reported with clear messages.</p>
      <button onclick="testEndpoint('/error-demo/missing-field')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Authentication Error</h3>
      <p>Demonstrates how authentication failures are handled securely.</p>
      <button onclick="testEndpoint('/error-demo/auth-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Connection Error</h3>
      <p>Shows how network and connection issues are reported.</p>
      <button onclick="testEndpoint('/error-demo/connection-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Timeout Error</h3>
      <p>Demonstrates how request timeouts are handled gracefully.</p>
      <button onclick="testEndpoint('/error-demo/timeout-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Resource Not Found</h3>
      <p>Shows how missing resources are reported with context.</p>
      <button onclick="testEndpoint('/error-demo/not-found-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Rate Limit Exceeded</h3>
      <p>Demonstrates how rate limiting is handled with retry information.</p>
      <button onclick="testEndpoint('/error-demo/rate-limit-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Transformation Error</h3>
      <p>Shows how data transformation failures are reported.</p>
      <button onclick="testEndpoint('/error-demo/transformation-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Connector Error</h3>
      <p>Demonstrates how connector-specific errors are handled.</p>
      <button onclick="testEndpoint('/error-demo/connector-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Generic Error</h3>
      <p>Shows how unexpected errors are handled safely.</p>
      <button onclick="testEndpoint('/error-demo/generic-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Async Error</h3>
      <p>Demonstrates how errors in asynchronous operations are caught.</p>
      <button onclick="testEndpoint('/error-demo/async-error')">Trigger Error</button>
    </div>
    
    <div class="error-card">
      <h3>Success Response</h3>
      <p>Shows a successful response for comparison.</p>
      <button onclick="testEndpoint('/error-demo/success')">Test Success</button>
    </div>
  </div>
  
  <div id="response" class="response">
    <h2>Response</h2>
    <div>
      <strong>Status:</strong> <span id="status"></span>
    </div>
    <div>
      <strong>Headers:</strong>
      <pre id="headers"></pre>
    </div>
    <div>
      <strong>Body:</strong>
      <pre id="body"></pre>
    </div>
  </div>
  
  <script>
    async function testEndpoint(url) {
      try {
        const response = await fetch(url);
        const responseEl = document.getElementById('response');
        const statusEl = document.getElementById('status');
        const headersEl = document.getElementById('headers');
        const bodyEl = document.getElementById('body');
        
        // Get response headers
        const headers = {};
        response.headers.forEach((value, key) => {
          headers[key] = value;
        });
        
        // Get response body
        let body;
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          body = await response.json();
        } else {
          body = await response.text();
        }
        
        // Update response display
        responseEl.style.display = 'block';
        responseEl.className = response.ok ? 'response success' : 'response error';
        statusEl.textContent = `${response.status} ${response.statusText}`;
        headersEl.textContent = JSON.stringify(headers, null, 2);
        bodyEl.textContent = typeof body === 'object' ? JSON.stringify(body, null, 2) : body;
        
        // Scroll to response
        responseEl.scrollIntoView({ behavior: 'smooth' });
      } catch (error) {
        console.error('Error fetching endpoint:', error);
        
        const responseEl = document.getElementById('response');
        const statusEl = document.getElementById('status');
        const headersEl = document.getElementById('headers');
        const bodyEl = document.getElementById('body');
        
        responseEl.style.display = 'block';
        responseEl.className = 'response error';
        statusEl.textContent = 'Client Error';
        headersEl.textContent = '{}';
        bodyEl.textContent = error.message;
        
        responseEl.scrollIntoView({ behavior: 'smooth' });
      }
    }
  </script>
</body>
</html>
