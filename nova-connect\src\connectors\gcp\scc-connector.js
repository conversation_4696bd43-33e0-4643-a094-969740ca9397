/**
 * Google Security Command Center (SCC) Connector
 * 
 * Connects to Google SCC API and normalizes security findings
 * for use with NovaConnect's remediation engine.
 */

const { SecurityCenterClient } = require('@google-cloud/security-center');
const TransformationEngine = require('../../engines/transformation-engine');

class SCCConnector {
  constructor(options = {}) {
    this.options = {
      enableMetrics: true,
      maxConcurrentRequests: 10,
      pageSize: 1000,
      ...options
    };
    
    this.transformationEngine = new TransformationEngine({
      enableMetrics: this.options.enableMetrics,
      enableCaching: true
    });
    
    // Register SCC-specific transformers
    this.transformationEngine.registerTransformer('parseSCCResourceName', this._parseSCCResourceName);
    this.transformationEngine.registerTransformer('mapSCCSeverity', this._mapSCCSeverity);
    this.transformationEngine.registerTransformer('extractSCCCategory', this._extractSCCCategory);
    
    // Initialize metrics
    this.metrics = {
      findingsRetrieved: 0,
      findingsNormalized: 0,
      apiCalls: 0,
      totalApiLatency: 0,
      averageApiLatency: 0,
      totalNormalizationTime: 0,
      averageNormalizationTime: 0
    };
  }
  
  /**
   * Initialize the SCC client with credentials
   * @param {Object} credentials - GCP credentials
   */
  async initialize(credentials) {
    try {
      this.client = new SecurityCenterClient({
        credentials: credentials,
        projectId: credentials.project_id
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error initializing SCC client:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }
  
  /**
   * Get findings from Security Command Center
   * @param {Object} params - Query parameters
   * @returns {Object} - Findings and metadata
   */
  async getFindings(params = {}) {
    if (!this.client) {
      throw new Error('SCC client not initialized. Call initialize() first.');
    }
    
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    
    try {
      const {
        organizationId,
        projectId,
        folderId,
        filter = '',
        pageSize = this.options.pageSize,
        pageToken,
        orderBy
      } = params;
      
      let parent;
      if (organizationId) {
        parent = `organizations/${organizationId}`;
      } else if (projectId) {
        parent = `projects/${projectId}`;
      } else if (folderId) {
        parent = `folders/${folderId}`;
      } else {
        throw new Error('One of organizationId, projectId, or folderId must be provided');
      }
      
      // Build the request
      const request = {
        parent,
        filter,
        pageSize,
        pageToken,
        orderBy
      };
      
      // Make the API call
      const [findings, metadata] = await this.client.listFindings(request);
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        this.metrics.apiCalls++;
        this.metrics.findingsRetrieved += findings.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      
      return {
        findings,
        nextPageToken: metadata.nextPageToken,
        totalSize: metadata.totalSize
      };
    } catch (error) {
      console.error('Error retrieving findings from SCC:', error);
      throw error;
    }
  }
  
  /**
   * Normalize SCC findings to NovaConnect format
   * @param {Array} findings - SCC findings
   * @returns {Array} - Normalized findings
   */
  normalizeFindings(findings) {
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    
    try {
      // Define transformation rules for SCC findings
      const rules = [
        { source: 'name', target: 'id', transform: 'parseSCCResourceName' },
        { source: 'category', target: 'category', transform: 'extractSCCCategory' },
        { source: 'severity', target: 'severity', transform: 'mapSCCSeverity' },
        { source: 'resourceName', target: 'resourceName' },
        { source: 'state', target: 'state', transform: 'lowercase' },
        { source: 'eventTime', target: 'eventTime' },
        { source: 'createTime', target: 'createdAt', transform: 'isoToUnix' },
        { source: 'sourceProperties.finding_type', target: 'type' },
        { source: 'sourceProperties.finding_description', target: 'description' },
        { source: 'sourceProperties.finding_id', target: 'externalId' },
        { source: 'securityMarks.marks', target: 'tags' }
      ];
      
      // Transform each finding
      const normalizedFindings = findings.map(finding => 
        this.transformationEngine.transform(finding, rules)
      );
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        this.metrics.findingsNormalized += findings.length;
        this.metrics.totalNormalizationTime += duration;
        this.metrics.averageNormalizationTime = 
          this.metrics.totalNormalizationTime / this.metrics.findingsNormalized;
      }
      
      return normalizedFindings;
    } catch (error) {
      console.error('Error normalizing SCC findings:', error);
      throw error;
    }
  }
  
  /**
   * Get and normalize findings in a single call
   * @param {Object} params - Query parameters
   * @returns {Object} - Normalized findings and metadata
   */
  async getFindingsNormalized(params = {}) {
    const { findings, nextPageToken, totalSize } = await this.getFindings(params);
    const normalizedFindings = this.normalizeFindings(findings);
    
    return {
      findings: normalizedFindings,
      nextPageToken,
      totalSize
    };
  }
  
  /**
   * Get all findings with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All findings
   */
  async getAllFindings(params = {}) {
    let allFindings = [];
    let nextPageToken = null;
    
    do {
      const { findings, nextPageToken: token } = await this.getFindings({
        ...params,
        pageToken: nextPageToken
      });
      
      allFindings = allFindings.concat(findings);
      nextPageToken = token;
    } while (nextPageToken);
    
    return allFindings;
  }
  
  /**
   * Get all normalized findings with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All normalized findings
   */
  async getAllFindingsNormalized(params = {}) {
    const findings = await this.getAllFindings(params);
    return this.normalizeFindings(findings);
  }
  
  /**
   * Get metrics for the connector
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      transformationMetrics: this.transformationEngine.getMetrics()
    };
  }
  
  /**
   * Parse SCC resource name to extract ID
   * @private
   */
  _parseSCCResourceName(name) {
    if (!name) return '';
    
    // Extract the finding ID from the name
    // Format: organizations/{organization_id}/sources/{source_id}/findings/{finding_id}
    const parts = name.split('/');
    return parts[parts.length - 1];
  }
  
  /**
   * Map SCC severity to normalized severity
   * @private
   */
  _mapSCCSeverity(severity) {
    const severityMap = {
      'CRITICAL': 'critical',
      'HIGH': 'high',
      'MEDIUM': 'medium',
      'LOW': 'low'
    };
    
    return severityMap[severity] || 'unknown';
  }
  
  /**
   * Extract category from SCC finding
   * @private
   */
  _extractSCCCategory(category) {
    if (!category) return 'unknown';
    
    // Convert to lowercase and remove spaces
    return category.toLowerCase().replace(/\s+/g, '_');
  }
}

module.exports = SCCConnector;
