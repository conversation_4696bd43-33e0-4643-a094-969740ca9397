# Nova UI for Google Cloud Marketplace

This document provides information about deploying and using Nova UI through Google Cloud Marketplace.

## Overview

Nova UI provides a modern, responsive user interface for NovaFuse GRC Suite. When deployed through Google Cloud Marketplace, Nova UI is fully integrated with Google Cloud services, providing a seamless experience for Google Cloud users.

## Deployment Options

Nova UI can be deployed through Google Cloud Marketplace in the following ways:

1. **Standalone Deployment**: Deploy Nova UI as a standalone application
2. **Integrated Deployment**: Deploy Nova UI as part of the NovaFuse GRC Suite

## Subscription Tiers

Nova UI is available in the following subscription tiers:

### NovaPrime Core

Basic functionality for small projects:

- Dashboard
- Connector Management
- Data Normalization
- Basic Reports

### NovaPrime Enterprise

Advanced functionality for professional teams:

- All Core features
- Advanced analytics
- Custom dashboards
- Custom reports
- Integration with Google Cloud Security Command Center
- Integration with Google Cloud Compliance

### NovaPrime AI Boost

Enterprise-grade functionality with AI capabilities:

- All Enterprise features
- AI-powered insights
- AI-powered recommendations
- AI-powered risk assessment
- AI-powered compliance monitoring

## Integration with Google Cloud Services

Nova UI integrates with the following Google Cloud services:

- **Google Cloud Security Command Center**: Display security findings from Security Command Center
- **Google Cloud Logging**: Display logs from Google Cloud Logging
- **Google Cloud Monitoring**: Display metrics from Google Cloud Monitoring
- **Google Cloud IAM**: Use Google Cloud IAM for authentication and authorization

## Configuration

Nova UI can be configured through the Google Cloud Marketplace UI during deployment. The following configuration options are available:

- **Subscription Tier**: Select the subscription tier
- **API Endpoint**: Configure the API endpoint
- **Authentication**: Configure authentication options
- **Theme**: Configure the UI theme
- **Branding**: Configure branding options
- **Feature Flags**: Configure feature flags

## Usage

Nova UI can be accessed through the following URL:

```
https://<deployment-name>.<region>.r.appspot.com
```

## Documentation

Nova UI documentation is available at the following URL:

```
https://<deployment-name>.<region>.r.appspot.com/docs
```

## Support

For support, please contact [<EMAIL>](mailto:<EMAIL>) or visit [novafuse.io/support](https://novafuse.io/support).

## Conclusion

Nova UI provides a modern, responsive user interface for NovaFuse GRC Suite. When deployed through Google Cloud Marketplace, Nova UI is fully integrated with Google Cloud services, providing a seamless experience for Google Cloud users.
