import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Divider,
  Paper,
  Switch,
  FormControlLabel,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

const authTypes = [
  { value: 'API_KEY', label: 'API Key' },
  { value: 'BASIC', label: 'Basic Authentication' },
  { value: 'OAUTH2', label: 'OAuth 2.0' },
  { value: 'JWT', label: 'JWT' },
  { value: 'AWS_SIG_V4', label: 'AWS Signature V4' },
  { value: 'CUSTOM', label: 'Custom' }
];

const fieldTypes = [
  { value: 'string', label: 'String' },
  { value: 'number', label: 'Number' },
  { value: 'boolean', label: '<PERSON>olean' },
  { value: 'object', label: 'Object' },
  { value: 'array', label: 'Array' }
];

const httpMethods = [
  'GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'
];

export default function AuthenticationForm({ connector, updateConnector }) {
  const [fields, setFields] = useState(connector.authentication.fields || {});
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldType, setNewFieldType] = useState('string');
  const [newFieldDescription, setNewFieldDescription] = useState('');
  const [newFieldRequired, setNewFieldRequired] = useState(false);
  const [newFieldSensitive, setNewFieldSensitive] = useState(false);

  const { control, handleSubmit, watch, setValue, formState: { errors } } = useForm({
    defaultValues: {
      type: connector.authentication.type,
      testConnection: connector.authentication.testConnection
    }
  });

  const authType = watch('type');

  useEffect(() => {
    // Set default test connection endpoint based on auth type
    if (authType === 'API_KEY' && !connector.authentication.testConnection.endpoint) {
      setValue('testConnection.endpoint', '/status');
    } else if (authType === 'OAUTH2' && !connector.authentication.testConnection.endpoint) {
      setValue('testConnection.endpoint', '/me');
    }
  }, [authType, connector.authentication.testConnection.endpoint, setValue]);

  const onSubmit = (data) => {
    updateConnector('authentication', {
      ...data,
      fields
    });
  };

  const handleAddField = () => {
    if (newFieldName.trim()) {
      const updatedFields = {
        ...fields,
        [newFieldName.trim()]: {
          type: newFieldType,
          description: newFieldDescription.trim() || `${newFieldName.trim()} field`,
          required: newFieldRequired,
          sensitive: newFieldSensitive
        }
      };
      
      setFields(updatedFields);
      updateConnector('authentication', { fields: updatedFields });
      
      // Reset form
      setNewFieldName('');
      setNewFieldType('string');
      setNewFieldDescription('');
      setNewFieldRequired(false);
      setNewFieldSensitive(false);
    }
  };

  const handleDeleteField = (fieldName) => {
    const { [fieldName]: _, ...remainingFields } = fields;
    setFields(remainingFields);
    updateConnector('authentication', { fields: remainingFields });
  };

  const getAuthTypeDescription = (type) => {
    switch (type) {
      case 'API_KEY':
        return 'API Key authentication uses a single key or token in the request header, query parameter, or cookie.';
      case 'BASIC':
        return 'Basic authentication uses a username and password encoded in base64 format.';
      case 'OAUTH2':
        return 'OAuth 2.0 is an authorization framework that enables third-party applications to obtain limited access to a user\'s account.';
      case 'JWT':
        return 'JWT (JSON Web Token) authentication uses a token that contains encoded JSON data.';
      case 'AWS_SIG_V4':
        return 'AWS Signature Version 4 is the process to add authentication information to AWS API requests.';
      case 'CUSTOM':
        return 'Custom authentication allows you to define your own authentication method.';
      default:
        return '';
    }
  };

  const getDefaultFieldsForAuthType = (type) => {
    switch (type) {
      case 'API_KEY':
        return {
          apiKey: {
            type: 'string',
            description: 'API Key',
            required: true,
            sensitive: true
          }
        };
      case 'BASIC':
        return {
          username: {
            type: 'string',
            description: 'Username',
            required: true,
            sensitive: false
          },
          password: {
            type: 'string',
            description: 'Password',
            required: true,
            sensitive: true
          }
        };
      case 'OAUTH2':
        return {
          clientId: {
            type: 'string',
            description: 'Client ID',
            required: true,
            sensitive: false
          },
          clientSecret: {
            type: 'string',
            description: 'Client Secret',
            required: true,
            sensitive: true
          },
          accessToken: {
            type: 'string',
            description: 'Access Token',
            required: false,
            sensitive: true
          },
          refreshToken: {
            type: 'string',
            description: 'Refresh Token',
            required: false,
            sensitive: true
          }
        };
      case 'JWT':
        return {
          token: {
            type: 'string',
            description: 'JWT Token',
            required: true,
            sensitive: true
          }
        };
      case 'AWS_SIG_V4':
        return {
          accessKeyId: {
            type: 'string',
            description: 'AWS Access Key ID',
            required: true,
            sensitive: false
          },
          secretAccessKey: {
            type: 'string',
            description: 'AWS Secret Access Key',
            required: true,
            sensitive: true
          },
          region: {
            type: 'string',
            description: 'AWS Region',
            required: true,
            sensitive: false
          }
        };
      default:
        return {};
    }
  };

  const handleAuthTypeChange = (newType) => {
    // If there are no fields defined yet, set default fields for the auth type
    if (Object.keys(fields).length === 0) {
      const defaultFields = getDefaultFieldsForAuthType(newType);
      setFields(defaultFields);
      updateConnector('authentication', { fields: defaultFields });
    }
  };

  return (
    <Box component="form" onChange={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Controller
            name="type"
            control={control}
            rules={{ required: 'Authentication type is required' }}
            render={({ field }) => (
              <FormControl fullWidth required error={!!errors.type}>
                <InputLabel id="auth-type-label">Authentication Type</InputLabel>
                <Select
                  {...field}
                  labelId="auth-type-label"
                  label="Authentication Type"
                  onChange={(e) => {
                    field.onChange(e);
                    handleAuthTypeChange(e.target.value);
                  }}
                >
                  {authTypes.map((type) => (
                    <MenuItem key={type.value} value={type.value}>
                      {type.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          {authType && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {getAuthTypeDescription(authType)}
            </Typography>
          )}
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Authentication Fields
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Define the fields required for authentication.
          </Typography>

          {/* Existing Fields */}
          {Object.keys(fields).length > 0 ? (
            <Paper variant="outlined" sx={{ p: 2, mb: 3, backgroundColor: 'background.default' }}>
              <Grid container spacing={2}>
                {Object.entries(fields).map(([fieldName, fieldConfig]) => (
                  <Grid item xs={12} key={fieldName}>
                    <Paper sx={{ p: 2, backgroundColor: 'background.paper' }}>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={3}>
                          <Typography variant="subtitle2">{fieldName}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {fieldConfig.type}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={7}>
                          <Typography variant="body2">{fieldConfig.description}</Typography>
                          <Box sx={{ mt: 1 }}>
                            {fieldConfig.required && (
                              <Chip 
                                label="Required" 
                                size="small" 
                                color="primary" 
                                variant="outlined" 
                                sx={{ mr: 1 }}
                              />
                            )}
                            {fieldConfig.sensitive && (
                              <Chip 
                                label="Sensitive" 
                                size="small" 
                                color="error" 
                                variant="outlined" 
                              />
                            )}
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={2} sx={{ textAlign: 'right' }}>
                          <IconButton 
                            color="error" 
                            onClick={() => handleDeleteField(fieldName)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          ) : (
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 3, 
                mb: 3, 
                backgroundColor: 'background.default',
                textAlign: 'center'
              }}
            >
              <Typography color="text.secondary">
                No authentication fields defined yet. Add fields below.
              </Typography>
            </Paper>
          )}

          {/* Add New Field */}
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom>
              Add New Field
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Field Name"
                  value={newFieldName}
                  onChange={(e) => setNewFieldName(e.target.value)}
                  fullWidth
                  placeholder="e.g., apiKey, username"
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="field-type-label">Field Type</InputLabel>
                  <Select
                    labelId="field-type-label"
                    value={newFieldType}
                    onChange={(e) => setNewFieldType(e.target.value)}
                    label="Field Type"
                  >
                    {fieldTypes.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  label="Description"
                  value={newFieldDescription}
                  onChange={(e) => setNewFieldDescription(e.target.value)}
                  fullWidth
                  placeholder="e.g., API Key for authentication"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newFieldRequired}
                      onChange={(e) => setNewFieldRequired(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Required"
                />
                <Tooltip title="Mark this field as required for authentication">
                  <IconButton size="small">
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newFieldSensitive}
                      onChange={(e) => setNewFieldSensitive(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Sensitive"
                />
                <Tooltip title="Mark this field as sensitive (e.g., passwords, tokens)">
                  <IconButton size="small">
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Grid>
              <Grid item xs={12}>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleAddField}
                  disabled={!newFieldName.trim()}
                >
                  Add Field
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Test Connection
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Define how to test the connection to the API.
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Controller
                name="testConnection.endpoint"
                control={control}
                rules={{ required: 'Test endpoint is required' }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Test Endpoint"
                    fullWidth
                    required
                    error={!!errors.testConnection?.endpoint}
                    helperText={errors.testConnection?.endpoint?.message || 'Endpoint to test the connection'}
                    placeholder="/status"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="testConnection.method"
                control={control}
                rules={{ required: 'HTTP method is required' }}
                render={({ field }) => (
                  <FormControl fullWidth required error={!!errors.testConnection?.method}>
                    <InputLabel id="test-method-label">HTTP Method</InputLabel>
                    <Select
                      {...field}
                      labelId="test-method-label"
                      label="HTTP Method"
                    >
                      {httpMethods.map((method) => (
                        <MenuItem key={method} value={method}>
                          {method}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="testConnection.expectedResponse.status"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Expected Status Code"
                    fullWidth
                    type="number"
                    helperText="HTTP status code expected from a successful test"
                    placeholder="200"
                  />
                )}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
