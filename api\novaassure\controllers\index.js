/**
 * Controllers Index
 * 
 * This file exports all controllers for the NovaAssure API.
 */

const controlController = require('./controlController');
const testPlanController = require('./testPlanController');
const testExecutionController = require('./testExecutionController');
const evidenceController = require('./evidenceController');
const reportController = require('./reportController');

module.exports = {
  controlController,
  testPlanController,
  testExecutionController,
  evidenceController,
  reportController
};
