{"version": 3, "names": ["ChronicleConnector", "require", "RemediationEngine", "performance", "fs", "hasCredentials", "process", "env", "GOOGLE_APPLICATION_CREDENTIALS", "existsSync", "config", "startTime", "Date", "now", "toISOString", "endTime", "max<PERSON><PERSON><PERSON>", "describe", "chronicleConnector", "remediationEngine", "beforeAll", "registerAction", "parameters", "console", "log", "success", "credentials<PERSON>ath", "credentials", "JSON", "parse", "readFileSync", "initialize", "error", "conditionalTest", "it", "skip", "alerts", "nextPageToken", "get<PERSON><PERSON><PERSON>", "pageSize", "duration", "length", "toFixed", "Math", "max", "expect", "Array", "isArray", "toBe", "first<PERSON><PERSON><PERSON>", "toHaveProperty", "normalizedAlerts", "normalizeAlerts", "firstNormalized", "createdAt", "toBeLessThan", "alertWithTechniques", "find", "alert", "attackTechniques", "mockTechniques", "nistControls", "_mapAttackToNist", "toBeGreaterThan", "toContain", "events", "searchEvents", "query", "firstEvent", "<PERSON><PERSON><PERSON><PERSON>", "id", "type", "severity", "createdTime", "indicators", "value", "normalized<PERSON>lert", "remediationScenario", "resource", "provider", "finding", "remediationSteps", "action", "ip<PERSON><PERSON><PERSON>", "reason", "remediationResult", "executeRemediation", "steps", "ipIndicator", "ind"], "sources": ["chronicle-live-integration.test.js"], "sourcesContent": ["/**\n * NovaConnect - Chronicle Live Integration Test\n * \n * This test connects to the actual Google Chronicle API\n * to validate threat-to-compliance mapping capabilities.\n * \n * NOTE: This test requires valid GCP credentials with Chronicle access.\n * Set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point\n * to a service account key file with appropriate permissions.\n */\n\nconst { ChronicleConnector } = require('../../../src/connectors/gcp/chronicle-connector');\nconst { RemediationEngine } = require('../../../src/engines/remediation-engine');\nconst { performance } = require('perf_hooks');\nconst fs = require('fs');\n\n// Skip tests if credentials are not available\nconst hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS && \n  fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);\n\n// Test configuration\nconst config = {\n  startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago\n  endTime: new Date().toISOString(),\n  maxAlerts: 100\n};\n\ndescribe('Chronicle Live Integration', () => {\n  let chronicleConnector;\n  let remediationEngine;\n  \n  beforeAll(async () => {\n    // Initialize the connectors and engines\n    chronicleConnector = new ChronicleConnector();\n    remediationEngine = new RemediationEngine();\n    \n    // Register a test remediation action\n    remediationEngine.registerAction('block-ip-address', async ({ parameters }) => {\n      console.log('Mock blocking IP address:', parameters);\n      return { success: true };\n    });\n    \n    // Initialize the Chronicle connector with credentials\n    if (hasCredentials) {\n      try {\n        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;\n        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));\n        await chronicleConnector.initialize(credentials);\n      } catch (error) {\n        console.error('Error initializing Chronicle connector:', error);\n      }\n    }\n  });\n  \n  // Skip tests if credentials are not available\n  const conditionalTest = hasCredentials ? it : it.skip;\n  \n  conditionalTest('should retrieve alerts from Chronicle', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Get alerts from Chronicle\n    const { alerts, nextPageToken } = await chronicleConnector.getAlerts({\n      startTime: config.startTime,\n      endTime: config.endTime,\n      pageSize: 10\n    });\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Retrieved ${alerts.length} alerts in ${duration.toFixed(2)}ms`);\n    console.log(`Average time per alert: ${(duration / Math.max(1, alerts.length)).toFixed(2)}ms`);\n    \n    // Verify alerts\n    expect(Array.isArray(alerts)).toBe(true);\n    if (alerts.length > 0) {\n      const firstAlert = alerts[0];\n      expect(firstAlert).toHaveProperty('id');\n      expect(firstAlert).toHaveProperty('type');\n      expect(firstAlert).toHaveProperty('createdTime');\n    }\n  }, 30000);\n  \n  conditionalTest('should normalize alerts efficiently', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get alerts from Chronicle\n    const { alerts } = await chronicleConnector.getAlerts({\n      startTime: config.startTime,\n      endTime: config.endTime,\n      pageSize: 10\n    });\n    \n    if (alerts.length === 0) {\n      console.log('No alerts to normalize');\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Normalize alerts\n    const normalizedAlerts = chronicleConnector.normalizeAlerts(alerts);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Normalized ${alerts.length} alerts in ${duration.toFixed(2)}ms`);\n    console.log(`Average time per alert: ${(duration / alerts.length).toFixed(2)}ms`);\n    \n    // Verify normalized alerts\n    expect(Array.isArray(normalizedAlerts)).toBe(true);\n    expect(normalizedAlerts.length).toBe(alerts.length);\n    \n    if (normalizedAlerts.length > 0) {\n      const firstNormalized = normalizedAlerts[0];\n      expect(firstNormalized).toHaveProperty('id');\n      expect(firstNormalized).toHaveProperty('title');\n      expect(firstNormalized).toHaveProperty('severity');\n      expect(firstNormalized).toHaveProperty('createdAt');\n      expect(typeof firstNormalized.createdAt).toBe('number');\n    }\n    \n    // Verify normalization speed\n    expect(duration / alerts.length).toBeLessThan(1); // Less than 1ms per alert\n  }, 30000);\n  \n  conditionalTest('should map MITRE ATT&CK techniques to NIST controls', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get alerts from Chronicle\n    const { alerts } = await chronicleConnector.getAlerts({\n      startTime: config.startTime,\n      endTime: config.endTime,\n      pageSize: 10\n    });\n    \n    if (alerts.length === 0) {\n      console.log('No alerts to map');\n      return;\n    }\n    \n    // Find an alert with ATT&CK techniques\n    const alertWithTechniques = alerts.find(alert => \n      alert.attackTechniques && alert.attackTechniques.length > 0\n    );\n    \n    if (!alertWithTechniques) {\n      console.log('No alerts with ATT&CK techniques found');\n      \n      // Create a mock alert with techniques for testing\n      const mockTechniques = ['T1059', 'T1190', 'T1133'];\n      \n      const startTime = performance.now();\n      \n      // Map techniques to NIST controls\n      const nistControls = chronicleConnector._mapAttackToNist(mockTechniques);\n      \n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // Log performance metrics\n      console.log(`Mapped ${mockTechniques.length} techniques to ${nistControls.length} NIST controls in ${duration.toFixed(2)}ms`);\n      \n      // Verify mapping\n      expect(Array.isArray(nistControls)).toBe(true);\n      expect(nistControls.length).toBeGreaterThan(0);\n      \n      // Verify specific mappings\n      expect(nistControls).toContain('AC-4'); // T1190 maps to AC-4\n      expect(nistControls).toContain('CM-7'); // T1059 maps to CM-7\n      \n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Map techniques to NIST controls\n    const nistControls = chronicleConnector._mapAttackToNist(alertWithTechniques.attackTechniques);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Mapped ${alertWithTechniques.attackTechniques.length} techniques to ${nistControls.length} NIST controls in ${duration.toFixed(2)}ms`);\n    \n    // Verify mapping\n    expect(Array.isArray(nistControls)).toBe(true);\n    expect(nistControls.length).toBeGreaterThan(0);\n  }, 30000);\n  \n  conditionalTest('should search for events in Chronicle', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    const startTime = performance.now();\n    \n    // Search for events in Chronicle\n    const { events, nextPageToken } = await chronicleConnector.searchEvents({\n      query: 'principal.ip = \"********\" OR target.ip = \"********\"',\n      startTime: config.startTime,\n      endTime: config.endTime,\n      pageSize: 10\n    });\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Searched for events in ${duration.toFixed(2)}ms`);\n    console.log(`Found ${events ? events.length : 0} events`);\n    \n    // Verify events (if any)\n    if (events && events.length > 0) {\n      const firstEvent = events[0];\n      expect(firstEvent).toHaveProperty('id');\n      expect(firstEvent).toHaveProperty('metadata');\n    }\n  }, 30000);\n  \n  conditionalTest('should create and execute threat remediation workflow', async () => {\n    // Skip if no credentials\n    if (!hasCredentials) {\n      return;\n    }\n    \n    // Get alerts from Chronicle\n    const { alerts } = await chronicleConnector.getAlerts({\n      startTime: config.startTime,\n      endTime: config.endTime,\n      pageSize: 1\n    });\n    \n    if (alerts.length === 0) {\n      console.log('No alerts to remediate');\n      \n      // Create a mock alert for testing\n      const mockAlert = {\n        id: 'mock-alert-1',\n        type: 'BRUTE_FORCE',\n        severity: 'HIGH',\n        createdTime: new Date().toISOString(),\n        attackTechniques: ['T1110'],\n        indicators: [\n          { type: 'IP_ADDRESS', value: '*************' }\n        ]\n      };\n      \n      const normalizedAlert = {\n        id: 'mock-alert-1',\n        type: 'brute_force',\n        severity: 'high',\n        createdAt: Date.now(),\n        attackTechniques: ['T1110'],\n        nistControls: ['AC-7', 'IA-5'],\n        indicators: [\n          { type: 'IP_ADDRESS', value: '*************' }\n        ]\n      };\n      \n      // Create a remediation scenario\n      const remediationScenario = {\n        id: `threat-remediation-${Date.now()}`,\n        type: 'security',\n        severity: normalizedAlert.severity,\n        resource: {\n          id: normalizedAlert.indicators[0].value,\n          type: 'ip_address',\n          provider: 'gcp'\n        },\n        finding: normalizedAlert,\n        remediationSteps: [\n          {\n            id: 'step-1',\n            action: 'block-ip-address',\n            parameters: {\n              ipAddress: normalizedAlert.indicators[0].value,\n              duration: '24h',\n              reason: 'Brute force attack detected'\n            }\n          }\n        ]\n      };\n      \n      const startTime = performance.now();\n      \n      // Execute the remediation\n      const remediationResult = await remediationEngine.executeRemediation(remediationScenario);\n      \n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // Log performance metrics\n      console.log(`Executed threat remediation in ${duration.toFixed(2)}ms`);\n      \n      // Verify remediation result\n      expect(remediationResult).toHaveProperty('id');\n      expect(remediationResult).toHaveProperty('status');\n      expect(remediationResult).toHaveProperty('steps');\n      expect(remediationResult.steps.length).toBe(1);\n      expect(remediationResult.steps[0].success).toBe(true);\n      \n      // Verify remediation speed\n      expect(duration).toBeLessThan(8000); // Less than 8 seconds\n      \n      return;\n    }\n    \n    // Normalize the alert\n    const normalizedAlerts = chronicleConnector.normalizeAlerts(alerts);\n    const alert = normalizedAlerts[0];\n    \n    // Check if the alert has indicators\n    if (!alert.indicators || alert.indicators.length === 0) {\n      console.log('Alert has no indicators to remediate');\n      return;\n    }\n    \n    // Find an IP address indicator\n    const ipIndicator = alert.indicators.find(ind => ind.type === 'IP_ADDRESS');\n    \n    if (!ipIndicator) {\n      console.log('No IP address indicator found');\n      return;\n    }\n    \n    // Create a remediation scenario\n    const remediationScenario = {\n      id: `threat-remediation-${Date.now()}`,\n      type: 'security',\n      severity: alert.severity,\n      resource: {\n        id: ipIndicator.value,\n        type: 'ip_address',\n        provider: 'gcp'\n      },\n      finding: alert,\n      remediationSteps: [\n        {\n          id: 'step-1',\n          action: 'block-ip-address',\n          parameters: {\n            ipAddress: ipIndicator.value,\n            duration: '24h',\n            reason: `${alert.type} alert detected`\n          }\n        }\n      ]\n    };\n    \n    const startTime = performance.now();\n    \n    // Execute the remediation\n    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Log performance metrics\n    console.log(`Executed threat remediation in ${duration.toFixed(2)}ms`);\n    \n    // Verify remediation result\n    expect(remediationResult).toHaveProperty('id');\n    expect(remediationResult).toHaveProperty('status');\n    expect(remediationResult).toHaveProperty('steps');\n    expect(remediationResult.steps.length).toBe(1);\n    expect(remediationResult.steps[0].success).toBe(true);\n    \n    // Verify remediation speed\n    expect(duration).toBeLessThan(8000); // Less than 8 seconds\n  }, 30000);\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAmB,CAAC,GAAGC,OAAO,CAAC,iDAAiD,CAAC;AACzF,MAAM;EAAEC;AAAkB,CAAC,GAAGD,OAAO,CAAC,yCAAyC,CAAC;AAChF,MAAM;EAAEE;AAAY,CAAC,GAAGF,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAMG,EAAE,GAAGH,OAAO,CAAC,IAAI,CAAC;;AAExB;AACA,MAAMI,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,8BAA8B,IAC/DJ,EAAE,CAACK,UAAU,CAACH,OAAO,CAACC,GAAG,CAACC,8BAA8B,CAAC;;AAE3D;AACA,MAAME,MAAM,GAAG;EACbC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;EAAE;EACzEC,OAAO,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;EACjCE,SAAS,EAAE;AACb,CAAC;AAEDC,QAAQ,CAAC,4BAA4B,EAAE,MAAM;EAC3C,IAAIC,kBAAkB;EACtB,IAAIC,iBAAiB;EAErBC,SAAS,CAAC,YAAY;IACpB;IACAF,kBAAkB,GAAG,IAAIlB,kBAAkB,CAAC,CAAC;IAC7CmB,iBAAiB,GAAG,IAAIjB,iBAAiB,CAAC,CAAC;;IAE3C;IACAiB,iBAAiB,CAACE,cAAc,CAAC,kBAAkB,EAAE,OAAO;MAAEC;IAAW,CAAC,KAAK;MAC7EC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,UAAU,CAAC;MACpD,OAAO;QAAEG,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC;;IAEF;IACA,IAAIpB,cAAc,EAAE;MAClB,IAAI;QACF,MAAMqB,eAAe,GAAGpB,OAAO,CAACC,GAAG,CAACC,8BAA8B;QAClE,MAAMmB,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACzB,EAAE,CAAC0B,YAAY,CAACJ,eAAe,EAAE,MAAM,CAAC,CAAC;QACxE,MAAMR,kBAAkB,CAACa,UAAU,CAACJ,WAAW,CAAC;MAClD,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MACjE;IACF;EACF,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG5B,cAAc,GAAG6B,EAAE,GAAGA,EAAE,CAACC,IAAI;EAErDF,eAAe,CAAC,uCAAuC,EAAE,YAAY;IACnE;IACA,IAAI,CAAC5B,cAAc,EAAE;MACnB;IACF;IAEA,MAAMM,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAM;MAAEuB,MAAM;MAAEC;IAAc,CAAC,GAAG,MAAMnB,kBAAkB,CAACoB,SAAS,CAAC;MACnE3B,SAAS,EAAED,MAAM,CAACC,SAAS;MAC3BI,OAAO,EAAEL,MAAM,CAACK,OAAO;MACvBwB,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,MAAMxB,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;IACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;IAEpC;IACAY,OAAO,CAACC,GAAG,CAAC,aAAaY,MAAM,CAACK,MAAM,cAAcD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5EnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAACgB,QAAQ,GAAGG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACK,MAAM,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAE9F;IACAG,MAAM,CAACC,KAAK,CAACC,OAAO,CAACX,MAAM,CAAC,CAAC,CAACY,IAAI,CAAC,IAAI,CAAC;IACxC,IAAIZ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;MACrB,MAAMQ,UAAU,GAAGb,MAAM,CAAC,CAAC,CAAC;MAC5BS,MAAM,CAACI,UAAU,CAAC,CAACC,cAAc,CAAC,IAAI,CAAC;MACvCL,MAAM,CAACI,UAAU,CAAC,CAACC,cAAc,CAAC,MAAM,CAAC;MACzCL,MAAM,CAACI,UAAU,CAAC,CAACC,cAAc,CAAC,aAAa,CAAC;IAClD;EACF,CAAC,EAAE,KAAK,CAAC;EAETjB,eAAe,CAAC,qCAAqC,EAAE,YAAY;IACjE;IACA,IAAI,CAAC5B,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAE+B;IAAO,CAAC,GAAG,MAAMlB,kBAAkB,CAACoB,SAAS,CAAC;MACpD3B,SAAS,EAAED,MAAM,CAACC,SAAS;MAC3BI,OAAO,EAAEL,MAAM,CAACK,OAAO;MACvBwB,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;MACvBlB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC;IACF;IAEA,MAAMb,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMsC,gBAAgB,GAAGjC,kBAAkB,CAACkC,eAAe,CAAChB,MAAM,CAAC;IAEnE,MAAMrB,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;IACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;IAEpC;IACAY,OAAO,CAACC,GAAG,CAAC,cAAcY,MAAM,CAACK,MAAM,cAAcD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC7EnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAACgB,QAAQ,GAAGJ,MAAM,CAACK,MAAM,EAAEC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAEjF;IACAG,MAAM,CAACC,KAAK,CAACC,OAAO,CAACI,gBAAgB,CAAC,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC;IAClDH,MAAM,CAACM,gBAAgB,CAACV,MAAM,CAAC,CAACO,IAAI,CAACZ,MAAM,CAACK,MAAM,CAAC;IAEnD,IAAIU,gBAAgB,CAACV,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAMY,eAAe,GAAGF,gBAAgB,CAAC,CAAC,CAAC;MAC3CN,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,IAAI,CAAC;MAC5CL,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,OAAO,CAAC;MAC/CL,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,UAAU,CAAC;MAClDL,MAAM,CAACQ,eAAe,CAAC,CAACH,cAAc,CAAC,WAAW,CAAC;MACnDL,MAAM,CAAC,OAAOQ,eAAe,CAACC,SAAS,CAAC,CAACN,IAAI,CAAC,QAAQ,CAAC;IACzD;;IAEA;IACAH,MAAM,CAACL,QAAQ,GAAGJ,MAAM,CAACK,MAAM,CAAC,CAACc,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;EACpD,CAAC,EAAE,KAAK,CAAC;EAETtB,eAAe,CAAC,qDAAqD,EAAE,YAAY;IACjF;IACA,IAAI,CAAC5B,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAE+B;IAAO,CAAC,GAAG,MAAMlB,kBAAkB,CAACoB,SAAS,CAAC;MACpD3B,SAAS,EAAED,MAAM,CAACC,SAAS;MAC3BI,OAAO,EAAEL,MAAM,CAACK,OAAO;MACvBwB,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;MACvBlB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B;IACF;;IAEA;IACA,MAAMgC,mBAAmB,GAAGpB,MAAM,CAACqB,IAAI,CAACC,KAAK,IAC3CA,KAAK,CAACC,gBAAgB,IAAID,KAAK,CAACC,gBAAgB,CAAClB,MAAM,GAAG,CAC5D,CAAC;IAED,IAAI,CAACe,mBAAmB,EAAE;MACxBjC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,MAAMoC,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;MAElD,MAAMjD,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;MAEnC;MACA,MAAMgD,YAAY,GAAG3C,kBAAkB,CAAC4C,gBAAgB,CAACF,cAAc,CAAC;MAExE,MAAM7C,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;MACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;MAEpC;MACAY,OAAO,CAACC,GAAG,CAAC,UAAUoC,cAAc,CAACnB,MAAM,kBAAkBoB,YAAY,CAACpB,MAAM,qBAAqBD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAE7H;MACAG,MAAM,CAACC,KAAK,CAACC,OAAO,CAACc,YAAY,CAAC,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;MAC9CH,MAAM,CAACgB,YAAY,CAACpB,MAAM,CAAC,CAACsB,eAAe,CAAC,CAAC,CAAC;;MAE9C;MACAlB,MAAM,CAACgB,YAAY,CAAC,CAACG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;MACxCnB,MAAM,CAACgB,YAAY,CAAC,CAACG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;;MAExC;IACF;IAEA,MAAMrD,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMgD,YAAY,GAAG3C,kBAAkB,CAAC4C,gBAAgB,CAACN,mBAAmB,CAACG,gBAAgB,CAAC;IAE9F,MAAM5C,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;IACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;IAEpC;IACAY,OAAO,CAACC,GAAG,CAAC,UAAUgC,mBAAmB,CAACG,gBAAgB,CAAClB,MAAM,kBAAkBoB,YAAY,CAACpB,MAAM,qBAAqBD,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAEnJ;IACAG,MAAM,CAACC,KAAK,CAACC,OAAO,CAACc,YAAY,CAAC,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;IAC9CH,MAAM,CAACgB,YAAY,CAACpB,MAAM,CAAC,CAACsB,eAAe,CAAC,CAAC,CAAC;EAChD,CAAC,EAAE,KAAK,CAAC;EAET9B,eAAe,CAAC,uCAAuC,EAAE,YAAY;IACnE;IACA,IAAI,CAAC5B,cAAc,EAAE;MACnB;IACF;IAEA,MAAMM,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAM;MAAEoD,MAAM;MAAE5B;IAAc,CAAC,GAAG,MAAMnB,kBAAkB,CAACgD,YAAY,CAAC;MACtEC,KAAK,EAAE,qDAAqD;MAC5DxD,SAAS,EAAED,MAAM,CAACC,SAAS;MAC3BI,OAAO,EAAEL,MAAM,CAACK,OAAO;MACvBwB,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,MAAMxB,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;IACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;IAEpC;IACAY,OAAO,CAACC,GAAG,CAAC,0BAA0BgB,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9DnB,OAAO,CAACC,GAAG,CAAC,SAASyC,MAAM,GAAGA,MAAM,CAACxB,MAAM,GAAG,CAAC,SAAS,CAAC;;IAEzD;IACA,IAAIwB,MAAM,IAAIA,MAAM,CAACxB,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAM2B,UAAU,GAAGH,MAAM,CAAC,CAAC,CAAC;MAC5BpB,MAAM,CAACuB,UAAU,CAAC,CAAClB,cAAc,CAAC,IAAI,CAAC;MACvCL,MAAM,CAACuB,UAAU,CAAC,CAAClB,cAAc,CAAC,UAAU,CAAC;IAC/C;EACF,CAAC,EAAE,KAAK,CAAC;EAETjB,eAAe,CAAC,uDAAuD,EAAE,YAAY;IACnF;IACA,IAAI,CAAC5B,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAM;MAAE+B;IAAO,CAAC,GAAG,MAAMlB,kBAAkB,CAACoB,SAAS,CAAC;MACpD3B,SAAS,EAAED,MAAM,CAACC,SAAS;MAC3BI,OAAO,EAAEL,MAAM,CAACK,OAAO;MACvBwB,QAAQ,EAAE;IACZ,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACK,MAAM,KAAK,CAAC,EAAE;MACvBlB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;;MAErC;MACA,MAAM6C,SAAS,GAAG;QAChBC,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,MAAM;QAChBC,WAAW,EAAE,IAAI7D,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;QACrC6C,gBAAgB,EAAE,CAAC,OAAO,CAAC;QAC3Be,UAAU,EAAE,CACV;UAAEH,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAgB,CAAC;MAElD,CAAC;MAED,MAAMC,eAAe,GAAG;QACtBN,EAAE,EAAE,cAAc;QAClBC,IAAI,EAAE,aAAa;QACnBC,QAAQ,EAAE,MAAM;QAChBlB,SAAS,EAAE1C,IAAI,CAACC,GAAG,CAAC,CAAC;QACrB8C,gBAAgB,EAAE,CAAC,OAAO,CAAC;QAC3BE,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;QAC9Ba,UAAU,EAAE,CACV;UAAEH,IAAI,EAAE,YAAY;UAAEI,KAAK,EAAE;QAAgB,CAAC;MAElD,CAAC;;MAED;MACA,MAAME,mBAAmB,GAAG;QAC1BP,EAAE,EAAE,sBAAsB1D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QACtC0D,IAAI,EAAE,UAAU;QAChBC,QAAQ,EAAEI,eAAe,CAACJ,QAAQ;QAClCM,QAAQ,EAAE;UACRR,EAAE,EAAEM,eAAe,CAACF,UAAU,CAAC,CAAC,CAAC,CAACC,KAAK;UACvCJ,IAAI,EAAE,YAAY;UAClBQ,QAAQ,EAAE;QACZ,CAAC;QACDC,OAAO,EAAEJ,eAAe;QACxBK,gBAAgB,EAAE,CAChB;UACEX,EAAE,EAAE,QAAQ;UACZY,MAAM,EAAE,kBAAkB;UAC1B5D,UAAU,EAAE;YACV6D,SAAS,EAAEP,eAAe,CAACF,UAAU,CAAC,CAAC,CAAC,CAACC,KAAK;YAC9CnC,QAAQ,EAAE,KAAK;YACf4C,MAAM,EAAE;UACV;QACF,CAAC;MAEL,CAAC;MAED,MAAMzE,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;MAEnC;MACA,MAAMwE,iBAAiB,GAAG,MAAMlE,iBAAiB,CAACmE,kBAAkB,CAACT,mBAAmB,CAAC;MAEzF,MAAM9D,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;MACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;MAEpC;MACAY,OAAO,CAACC,GAAG,CAAC,kCAAkCgB,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEtE;MACAG,MAAM,CAACwC,iBAAiB,CAAC,CAACnC,cAAc,CAAC,IAAI,CAAC;MAC9CL,MAAM,CAACwC,iBAAiB,CAAC,CAACnC,cAAc,CAAC,QAAQ,CAAC;MAClDL,MAAM,CAACwC,iBAAiB,CAAC,CAACnC,cAAc,CAAC,OAAO,CAAC;MACjDL,MAAM,CAACwC,iBAAiB,CAACE,KAAK,CAAC9C,MAAM,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC;MAC9CH,MAAM,CAACwC,iBAAiB,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC9D,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;;MAErD;MACAH,MAAM,CAACL,QAAQ,CAAC,CAACe,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;MAErC;IACF;;IAEA;IACA,MAAMJ,gBAAgB,GAAGjC,kBAAkB,CAACkC,eAAe,CAAChB,MAAM,CAAC;IACnE,MAAMsB,KAAK,GAAGP,gBAAgB,CAAC,CAAC,CAAC;;IAEjC;IACA,IAAI,CAACO,KAAK,CAACgB,UAAU,IAAIhB,KAAK,CAACgB,UAAU,CAACjC,MAAM,KAAK,CAAC,EAAE;MACtDlB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD;IACF;;IAEA;IACA,MAAMgE,WAAW,GAAG9B,KAAK,CAACgB,UAAU,CAACjB,IAAI,CAACgC,GAAG,IAAIA,GAAG,CAAClB,IAAI,KAAK,YAAY,CAAC;IAE3E,IAAI,CAACiB,WAAW,EAAE;MAChBjE,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C;IACF;;IAEA;IACA,MAAMqD,mBAAmB,GAAG;MAC1BP,EAAE,EAAE,sBAAsB1D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACtC0D,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAEd,KAAK,CAACc,QAAQ;MACxBM,QAAQ,EAAE;QACRR,EAAE,EAAEkB,WAAW,CAACb,KAAK;QACrBJ,IAAI,EAAE,YAAY;QAClBQ,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAEtB,KAAK;MACduB,gBAAgB,EAAE,CAChB;QACEX,EAAE,EAAE,QAAQ;QACZY,MAAM,EAAE,kBAAkB;QAC1B5D,UAAU,EAAE;UACV6D,SAAS,EAAEK,WAAW,CAACb,KAAK;UAC5BnC,QAAQ,EAAE,KAAK;UACf4C,MAAM,EAAE,GAAG1B,KAAK,CAACa,IAAI;QACvB;MACF,CAAC;IAEL,CAAC;IAED,MAAM5D,SAAS,GAAGR,WAAW,CAACU,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMwE,iBAAiB,GAAG,MAAMlE,iBAAiB,CAACmE,kBAAkB,CAACT,mBAAmB,CAAC;IAEzF,MAAM9D,OAAO,GAAGZ,WAAW,CAACU,GAAG,CAAC,CAAC;IACjC,MAAM2B,QAAQ,GAAGzB,OAAO,GAAGJ,SAAS;;IAEpC;IACAY,OAAO,CAACC,GAAG,CAAC,kCAAkCgB,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAEtE;IACAG,MAAM,CAACwC,iBAAiB,CAAC,CAACnC,cAAc,CAAC,IAAI,CAAC;IAC9CL,MAAM,CAACwC,iBAAiB,CAAC,CAACnC,cAAc,CAAC,QAAQ,CAAC;IAClDL,MAAM,CAACwC,iBAAiB,CAAC,CAACnC,cAAc,CAAC,OAAO,CAAC;IACjDL,MAAM,CAACwC,iBAAiB,CAACE,KAAK,CAAC9C,MAAM,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC;IAC9CH,MAAM,CAACwC,iBAAiB,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC9D,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC;;IAErD;IACAH,MAAM,CAACL,QAAQ,CAAC,CAACe,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,CAAC,EAAE,KAAK,CAAC;AACX,CAAC,CAAC", "ignoreList": []}