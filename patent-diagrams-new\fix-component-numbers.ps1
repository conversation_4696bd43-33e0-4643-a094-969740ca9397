# Script to fix component numbers in all patent diagram HTML files
# This script ensures component numbers are rendered correctly in all diagrams

# List of specific patent diagram files
$patentDiagrams = @(
    "cyber-safety-architecture-fixed.html",
    "unified-field-theory.html",
    "alignment-architecture-final.html",
    "novafuse-components.html",
    "hardware-software-implementation.html",
    "detailed-data-flow.html",
    "18-82-principle.html",
    "cyber-safety-incident-response.html",
    "adaptive-compliance-process.html",
    "comphyology-universal-application-framework.html",
    "financial-services-architecture.html",
    "healthcare-architecture.html",
    "visualization-output-examples.html",
    "patent-claims.html",
    "patent-shield.html",
    "partner-empowerment-flywheel.html"
)

# Improved Component Number CSS
$componentNumberCSS = @"
.component-number {
    position: absolute !important;
    top: -10px !important;
    left: -10px !important;
    width: 20px !important;
    height: 20px !important;
    background-color: #555555 !important; /* Grey color for patent compliance */
    color: white !important;
    border-radius: 50% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    font-size: 12px !important;
    font-weight: bold !important;
    z-index: 5 !important; /* Increased z-index to ensure visibility */
    box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important; /* Subtle shadow for better visibility */
}
"@

# Process each patent diagram file
foreach ($fileName in $patentDiagrams) {
    $filePath = Join-Path -Path "D:\novafuse-api-superstore\patent-diagrams-new" -ChildPath $fileName
    
    # Check if file exists
    if (Test-Path $filePath) {
        Write-Host "Processing $fileName..."
        
        # Read the file content
        $content = Get-Content -Path $filePath -Raw
        
        # Create a backup of the original file
        Copy-Item -Path $filePath -Destination "$filePath.numbers.bak"
        
        # Find the style section and add the component number CSS
        if ($content -match "<style>(.*?)</style>") {
            $styleSection = $Matches[0]
            $styleContent = $Matches[1]
            
            # Check if component-number class already exists
            if ($styleContent -match "\.component-number\s*\{") {
                # Replace existing component-number class
                $newStyleContent = $styleContent -replace "\.component-number\s*\{[^}]*\}", $componentNumberCSS
                $newStyleSection = "<style>$newStyleContent</style>"
                $content = $content -replace [regex]::Escape($styleSection), $newStyleSection
            } else {
                # Add component-number class if it doesn't exist
                $newStyleSection = "<style>$styleContent`n$componentNumberCSS</style>"
                $content = $content -replace [regex]::Escape($styleSection), $newStyleSection
            }
        }
        
        # Fix any inline styles on component numbers
        $content = $content -replace '<div class="component-number"([^>]*)style="([^"]*)"', '<div class="component-number"$1'
        
        # Fix any component numbers with non-standard positioning
        $content = $content -replace '<div class="component-number" style="left: -15px;">', '<div class="component-number">'
        
        # Write the updated content back to the file
        Set-Content -Path $filePath -Value $content
        
        Write-Host "Updated component numbers in $fileName successfully."
    } else {
        Write-Host "File not found: $fileName"
    }
}

Write-Host "Component numbers have been fixed in all patent diagram files."
