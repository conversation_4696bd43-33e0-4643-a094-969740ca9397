/**
 * NovaFuse Universal API Connector User Model
 * 
 * This model defines the schema for users in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const Schema = mongoose.Schema;

const userSchema = new Schema({
  username: { 
    type: String, 
    required: true, 
    unique: true, 
    trim: true 
  },
  email: { 
    type: String, 
    required: true, 
    unique: true, 
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please provide a valid email address']
  },
  password: { 
    type: String, 
    required: true 
  },
  firstName: { 
    type: String, 
    trim: true 
  },
  lastName: { 
    type: String, 
    trim: true 
  },
  role: { 
    type: String, 
    enum: ['admin', 'user', 'readonly'], 
    default: 'user' 
  },
  permissions: [{ 
    type: String 
  }],
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'suspended', 'pending'], 
    default: 'pending' 
  },
  lastLogin: { 
    type: Date 
  },
  failedLoginAttempts: { 
    type: Number, 
    default: 0 
  },
  lockUntil: { 
    type: Date 
  },
  passwordResetToken: { 
    type: String 
  },
  passwordResetExpires: { 
    type: Date 
  },
  emailVerificationToken: { 
    type: String 
  },
  emailVerified: { 
    type: Boolean, 
    default: false 
  },
  twoFactorEnabled: { 
    type: Boolean, 
    default: false 
  },
  twoFactorSecret: { 
    type: String 
  },
  authProvider: { 
    type: String, 
    enum: ['local', 'google', 'microsoft', 'saml', 'oidc'], 
    default: 'local' 
  },
  authProviderId: { 
    type: String 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add pre-save hook to hash password
userSchema.pre('save', async function(next) {
  // Only hash the password if it's modified or new
  if (!this.isModified('password')) return next();
  
  try {
    // Generate salt and hash password
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Add method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Add method to check if account is locked
userSchema.methods.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// Add method to get full name
userSchema.methods.getFullName = function() {
  return `${this.firstName || ''} ${this.lastName || ''}`.trim() || this.username;
};

// Add virtual for full name
userSchema.virtual('fullName').get(function() {
  return this.getFullName();
});

// Add method to check if user has a specific role
userSchema.methods.hasRole = function(role) {
  if (Array.isArray(role)) {
    return role.includes(this.role);
  }
  return this.role === role;
};

// Add method to check if user has a specific permission
userSchema.methods.hasPermission = function(permission) {
  // Admin role has all permissions
  if (this.role === 'admin') {
    return true;
  }
  
  if (Array.isArray(permission)) {
    return permission.some(p => this.permissions.includes(p));
  }
  
  return this.permissions.includes(permission);
};

// Add indexes
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ status: 1 });
userSchema.index({ authProvider: 1, authProviderId: 1 });

// Create model
const User = mongoose.model('User', userSchema);

module.exports = User;
