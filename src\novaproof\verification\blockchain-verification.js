/**
 * blockchain-verification.js
 *
 * This file implements the blockchain verification system for NovaProof.
 * It provides functions for verifying evidence on the blockchain and generating
 * cryptographic proofs of verification.
 */

const crypto = require('crypto');
const { Evidence, EvidenceStatus } = require('../models/Evidence');
const { MerkleTree } = require('../merkle/merkle-tree');
const {
  createBlockchainProvider,
  BlockchainType,
  TransactionStatus
} = require('../blockchain/blockchain-provider');

/**
 * Verification status enum
 * @enum {string}
 */
const VerificationStatus = {
  PENDING: 'PENDING',
  VERIFIED: 'VERIFIED',
  INVALID: 'INVALID',
  FAILED: 'FAILED'
};

/**
 * Blockchain verification manager
 */
class BlockchainVerificationManager {
  /**
   * Create a new blockchain verification manager
   * @param {Object} options - Manager options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      defaultBlockchainType: options.defaultBlockchainType || BlockchainType.ETHEREUM,
      providers: options.providers || {},
      ...options
    };

    this.providers = new Map();

    // Initialize providers if specified
    Object.entries(this.options.providers).forEach(([type, providerOptions]) => {
      this.registerProvider(type, providerOptions);
    });

    this.log('BlockchainVerificationManager initialized with options:', this.options);
  }

  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[BlockchainVerificationManager ${new Date().toISOString()}]`, ...args);
    }
  }

  /**
   * Register a blockchain provider
   * @param {string} type - The blockchain type
   * @param {Object} options - Provider options
   * @returns {BlockchainVerificationManager} - The manager instance
   */
  registerProvider(type, options = {}) {
    try {
      const provider = createBlockchainProvider(type, {
        ...options,
        enableLogging: this.options.enableLogging
      });

      this.providers.set(type, provider);
      this.log(`Registered ${type} provider`);

      return this;
    } catch (error) {
      this.log(`Error registering ${type} provider:`, error);
      throw error;
    }
  }

  /**
   * Get a blockchain provider
   * @param {string} type - The blockchain type
   * @returns {BlockchainProvider} - The blockchain provider
   */
  getProvider(type) {
    const provider = this.providers.get(type);

    if (!provider) {
      throw new Error(`Provider for blockchain type ${type} not registered`);
    }

    return provider;
  }

  /**
   * Connect to a blockchain network
   * @param {string} type - The blockchain type
   * @returns {Promise<void>} - A promise that resolves when connected
   */
  async connect(type) {
    const provider = this.getProvider(type);

    if (provider.isConnected) {
      this.log(`Already connected to ${type} blockchain`);
      return;
    }

    try {
      await provider.connect();
      this.log(`Connected to ${type} blockchain`);
    } catch (error) {
      this.log(`Error connecting to ${type} blockchain:`, error);
      throw error;
    }
  }

  /**
   * Disconnect from a blockchain network
   * @param {string} type - The blockchain type
   * @returns {Promise<void>} - A promise that resolves when disconnected
   */
  async disconnect(type) {
    const provider = this.getProvider(type);

    if (!provider.isConnected) {
      this.log(`Already disconnected from ${type} blockchain`);
      return;
    }

    try {
      await provider.disconnect();
      this.log(`Disconnected from ${type} blockchain`);
    } catch (error) {
      this.log(`Error disconnecting from ${type} blockchain:`, error);
      throw error;
    }
  }

  /**
   * Verify evidence on the blockchain
   * @param {Evidence} evidence - The evidence to verify
   * @param {Object} options - Verification options
   * @returns {Promise<Object>} - A promise that resolves to the verification result
   */
  async verifyEvidence(evidence, options = {}) {
    if (!(evidence instanceof Evidence)) {
      throw new Error('First argument must be an Evidence instance');
    }

    const blockchainType = options.blockchainType || this.options.defaultBlockchainType;
    const contentHash = evidence.hash();

    try {
      // Get the provider for the specified blockchain type
      const provider = this.getProvider(blockchainType);

      // Connect to the blockchain if not already connected
      if (!provider.isConnected) {
        await provider.connect();
      }

      this.log(`Verifying evidence ${evidence.id} on ${blockchainType} blockchain...`);

      // Submit the content hash to the blockchain
      const result = await provider.submitData(contentHash, {
        evidenceId: evidence.id,
        ...options
      });

      // Create a verification record
      const verification = {
        id: crypto.randomUUID(),
        blockchainReference: {
          type: blockchainType,
          transactionId: result.transactionId,
          blockNumber: null // Will be updated when confirmed
        },
        contentHash,
        timestamp: result.timestamp,
        status: result.status === TransactionStatus.CONFIRMED
          ? VerificationStatus.VERIFIED
          : VerificationStatus.PENDING
      };

      // Add the verification to the evidence
      evidence.addVerification(verification);

      // If the transaction is not yet confirmed, set up a listener for confirmation
      if (result.status !== TransactionStatus.CONFIRMED) {
        provider.once('transactionConfirmed', (transaction) => {
          if (transaction.id === result.transactionId) {
            // Update the verification record
            verification.status = VerificationStatus.VERIFIED;
            verification.blockchainReference.blockNumber = transaction.blockNumber;

            // Update the evidence
            evidence.addVerification(verification);

            this.log(`Evidence ${evidence.id} verification confirmed on ${blockchainType} blockchain`);
          }
        });
      }

      return {
        evidenceId: evidence.id,
        verificationId: verification.id,
        contentHash,
        verified: verification.status === VerificationStatus.VERIFIED,
        timestamp: verification.timestamp,
        blockchainReference: verification.blockchainReference
      };
    } catch (error) {
      this.log(`Error verifying evidence ${evidence.id}:`, error);

      // Add a failed verification to the evidence
      evidence.addVerification({
        id: crypto.randomUUID(),
        contentHash,
        timestamp: new Date().toISOString(),
        status: VerificationStatus.FAILED,
        error: error.message
      });

      throw error;
    }
  }

  /**
   * Generate a Merkle proof for a verification
   * @param {Evidence} evidence - The evidence
   * @param {Object} transaction - The blockchain transaction
   * @returns {Object} - The Merkle proof
   */
  generateProof(evidence, transaction) {
    if (!(evidence instanceof Evidence)) {
      throw new Error('First argument must be an Evidence instance');
    }

    if (!transaction || !transaction.contentHash) {
      throw new Error('Invalid transaction');
    }

    try {
      this.log(`Generating proof for evidence ${evidence.id}...`);

      // Create a Merkle tree with a single item
      const merkleTree = new MerkleTree([transaction.contentHash]);

      // Get the proof for the item
      const merkleProof = merkleTree.getProof(0);
      const merkleRoot = merkleTree.getRootHash();

      return {
        evidenceId: evidence.id,
        transactionId: transaction.transactionId,
        contentHash: transaction.contentHash,
        merkleRoot,
        merkleProof,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.log(`Error generating proof for evidence ${evidence.id}:`, error);
      throw error;
    }
  }

  /**
   * Verify a Merkle proof
   * @param {string} contentHash - The content hash to verify
   * @param {string} merkleRoot - The Merkle root
   * @param {Array} merkleProof - The Merkle proof
   * @returns {boolean} - Whether the proof is valid
   */
  verifyProof(contentHash, merkleRoot, merkleProof) {
    if (!contentHash || !merkleRoot || !Array.isArray(merkleProof)) {
      throw new Error('Invalid proof parameters');
    }

    try {
      // Create a Merkle tree instance
      const merkleTree = new MerkleTree();

      // Verify the proof
      return merkleTree.verifyProof(contentHash, merkleProof, merkleRoot);
    } catch (error) {
      this.log('Error verifying proof:', error);
      return false;
    }
  }

  /**
   * Batch verify multiple evidence items
   * @param {Array<Evidence>} evidenceItems - The evidence items to verify
   * @param {Object} options - Verification options
   * @returns {Promise<Array<Object>>} - A promise that resolves to an array of verification results
   */
  async batchVerifyEvidence(evidenceItems, options = {}) {
    if (!Array.isArray(evidenceItems)) {
      throw new Error('First argument must be an array of Evidence instances');
    }

    // Validate that all items are Evidence instances
    evidenceItems.forEach(item => {
      if (!(item instanceof Evidence)) {
        throw new Error('All items must be Evidence instances');
      }
    });

    const blockchainType = options.blockchainType || this.options.defaultBlockchainType;

    try {
      // Get the provider for the specified blockchain type
      const provider = this.getProvider(blockchainType);

      // Connect to the blockchain if not already connected
      if (!provider.isConnected) {
        await provider.connect();
      }

      this.log(`Batch verifying ${evidenceItems.length} evidence items on ${blockchainType} blockchain...`);

      // Calculate content hashes for all evidence items
      const contentHashes = evidenceItems.map(item => ({
        evidenceId: item.id,
        contentHash: item.hash()
      }));

      // Create a Merkle tree from the content hashes
      const merkleTree = new MerkleTree(contentHashes.map(item => item.contentHash));
      const merkleRoot = merkleTree.getRootHash();

      // Submit the Merkle root to the blockchain
      const result = await provider.submitData(merkleRoot, {
        batchSize: evidenceItems.length,
        ...options
      });

      // Create verification results for each evidence item
      const results = [];

      for (let i = 0; i < evidenceItems.length; i++) {
        const evidence = evidenceItems[i];
        const { contentHash } = contentHashes[i];
        const merkleProof = merkleTree.getProof(i);

        // Create a verification record
        const verification = {
          id: crypto.randomUUID(),
          blockchainReference: {
            type: blockchainType,
            transactionId: result.transactionId,
            blockNumber: null // Will be updated when confirmed
          },
          contentHash,
          merkleRoot,
          merkleProof,
          timestamp: result.timestamp,
          status: result.status === TransactionStatus.CONFIRMED
            ? VerificationStatus.VERIFIED
            : VerificationStatus.PENDING
        };

        // Add the verification to the evidence
        evidence.addVerification(verification);

        results.push({
          evidenceId: evidence.id,
          verificationId: verification.id,
          contentHash,
          verified: verification.status === VerificationStatus.VERIFIED,
          timestamp: verification.timestamp,
          blockchainReference: verification.blockchainReference,
          merkleRoot,
          merkleProofLength: merkleProof.length
        });
      }

      // If the transaction is not yet confirmed, set up a listener for confirmation
      if (result.status !== TransactionStatus.CONFIRMED) {
        provider.once('transactionConfirmed', (transaction) => {
          if (transaction.id === result.transactionId) {
            // Update all verification records
            for (let i = 0; i < evidenceItems.length; i++) {
              const evidence = evidenceItems[i];
              const verification = evidence.getLatestVerification();

              if (verification && verification.blockchainReference.transactionId === result.transactionId) {
                verification.status = VerificationStatus.VERIFIED;
                verification.blockchainReference.blockNumber = transaction.blockNumber;

                // Update the evidence
                evidence.addVerification(verification);
              }
            }

            this.log(`Batch verification of ${evidenceItems.length} evidence items confirmed on ${blockchainType} blockchain`);
          }
        });
      }

      return results;
    } catch (error) {
      this.log(`Error batch verifying evidence:`, error);

      // Add failed verifications to all evidence items
      for (const evidence of evidenceItems) {
        evidence.addVerification({
          id: crypto.randomUUID(),
          contentHash: evidence.hash(),
          timestamp: new Date().toISOString(),
          status: VerificationStatus.FAILED,
          error: error.message
        });
      }

      throw error;
    }
  }
}

// Create a singleton instance
const verificationManager = new BlockchainVerificationManager();

/**
 * Verify evidence on the blockchain
 * @param {Evidence} evidence - The evidence to verify
 * @param {Object} options - Verification options
 * @returns {Promise<Object>} - A promise that resolves to the verification result
 */
async function verifyEvidence(evidence, options = {}) {
  return verificationManager.verifyEvidence(evidence, options);
}

/**
 * Generate a Merkle proof for a verification
 * @param {Evidence} evidence - The evidence
 * @param {Object} transaction - The blockchain transaction
 * @returns {Object} - The Merkle proof
 */
function generateProof(evidence, transaction) {
  return verificationManager.generateProof(evidence, transaction);
}

/**
 * Verify a Merkle proof
 * @param {string} contentHash - The content hash to verify
 * @param {string} merkleRoot - The Merkle root
 * @param {Array} merkleProof - The Merkle proof
 * @returns {boolean} - Whether the proof is valid
 */
function verifyProof(contentHash, merkleRoot, merkleProof) {
  return verificationManager.verifyProof(contentHash, merkleRoot, merkleProof);
}

/**
 * Batch verify multiple evidence items
 * @param {Array<Evidence>} evidenceItems - The evidence items to verify
 * @param {Object} options - Verification options
 * @returns {Promise<Array<Object>>} - A promise that resolves to an array of verification results
 */
async function batchVerifyEvidence(evidenceItems, options = {}) {
  return verificationManager.batchVerifyEvidence(evidenceItems, options);
}

module.exports = {
  verifyEvidence,
  generateProof,
  verifyProof,
  batchVerifyEvidence,
  BlockchainVerificationManager,
  VerificationStatus,
  BlockchainType
};
