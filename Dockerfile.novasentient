# NovaPi Benchmark Edition - REAL AI Performance Optimization
# Real AI model benchmarking with π-coherence timing optimization

FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Install system dependencies for AI libraries
RUN apt-get update && apt-get install -y \
    curl \
    git \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy source code and benchmark suite
COPY src/ ./src/
COPY novapi-benchmark/ ./novapi-benchmark/
COPY test_novapi.py ./

# Install essential libraries first
RUN pip install --no-cache-dir -r ./novapi-benchmark/requirements.txt

# Install AI libraries (CPU versions for faster installation)
RUN pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
RUN pip install transformers

# Make benchmark script executable
RUN chmod +x ./novapi-benchmark/run_benchmark.sh

# Create output directory with proper permissions
RUN mkdir -p /app/output && chmod 777 /app/output

# Expose port for potential web interface
EXPOSE 8080

# Health check using NovaPi
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from src.novasentientx.nova_pi import NovaPi; pi = NovaPi(); print('Health: π-Coherence OK')" || exit 1

# Set environment variables
ENV PYTHONPATH=/app/src
ENV NOVASENTIENT_MODE=production
ENV PI_COHERENCE_ENABLED=true
ENV CONSCIOUSNESS_THRESHOLD=0.618
ENV PERFORMANCE_MULTIPLIER=3.142

# Labels for container identification
LABEL maintainer="David Nigel Irvin <<EMAIL>>"
LABEL version="1.0.0-REAL_AI_BENCHMARK"
LABEL description="NovaPi Benchmark Edition - Real AI Performance Optimization"
LABEL org.opencontainers.image.title="NovaPi-Benchmark"
LABEL org.opencontainers.image.description="Real AI model benchmarking with π-coherence optimization"
LABEL org.opencontainers.image.vendor="NovaFuse Technologies"
LABEL org.opencontainers.image.version="1.0.0"
LABEL pi.coherence.sequence="31,42,53,64,75,86"
LABEL ai.models="distilbert-base-uncased,resnet18"
LABEL benchmark.type="real_ai_performance"

# Start NovaPi Benchmark Edition
CMD ["bash", "/app/novapi-benchmark/run_benchmark.sh"]
