/**
 * Google Chronicle Connector
 * 
 * Connects to Google Chronicle API and normalizes security events
 * for use with NovaConnect's remediation engine.
 * 
 * This connector enables mapping between MITRE ATT&CK and NIST frameworks
 * for comprehensive threat-to-compliance correlation.
 */

const axios = require('axios');
const { GoogleAuth } = require('google-auth-library');
const TransformationEngine = require('../../engines/transformation-engine');

class ChronicleConnector {
  constructor(options = {}) {
    this.options = {
      enableMetrics: true,
      maxConcurrentRequests: 10,
      pageSize: 1000,
      baseUrl: 'https://backstory.googleapis.com/v1',
      ...options
    };
    
    this.transformationEngine = new TransformationEngine({
      enableMetrics: this.options.enableMetrics,
      enableCaching: true
    });
    
    // Register Chronicle-specific transformers
    this.transformationEngine.registerTransformer('mapAttackToNist', this._mapAttackToNist.bind(this));
    this.transformationEngine.registerTransformer('extractIoc', this._extractIoc);
    this.transformationEngine.registerTransformer('normalizeChronicleTime', this._normalizeChronicleTime);
    
    // Initialize MITRE ATT&CK to NIST mapping
    this._initializeAttackToNistMapping();
    
    // Initialize metrics
    this.metrics = {
      eventsRetrieved: 0,
      eventsNormalized: 0,
      apiCalls: 0,
      totalApiLatency: 0,
      averageApiLatency: 0,
      totalNormalizationTime: 0,
      averageNormalizationTime: 0
    };
  }
  
  /**
   * Initialize the Chronicle client with credentials
   * @param {Object} credentials - GCP credentials
   */
  async initialize(credentials) {
    try {
      this.auth = new GoogleAuth({
        credentials: credentials,
        scopes: ['https://www.googleapis.com/auth/chronicle-backstory']
      });
      
      this.client = axios.create({
        baseURL: this.options.baseUrl,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // Set up request interceptor to add authentication
      this.client.interceptors.request.use(async (config) => {
        const token = await this.auth.getAccessToken();
        config.headers.Authorization = `Bearer ${token}`;
        return config;
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error initializing Chronicle client:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }
  
  /**
   * Get alerts from Chronicle
   * @param {Object} params - Query parameters
   * @returns {Object} - Alerts and metadata
   */
  async getAlerts(params = {}) {
    if (!this.client) {
      throw new Error('Chronicle client not initialized. Call initialize() first.');
    }
    
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    
    try {
      const {
        startTime: alertStartTime,
        endTime: alertEndTime,
        pageSize = this.options.pageSize,
        pageToken
      } = params;
      
      // Build the request
      const request = {
        pageSize,
        pageToken
      };
      
      if (alertStartTime) {
        request.startTime = alertStartTime;
      }
      
      if (alertEndTime) {
        request.endTime = alertEndTime;
      }
      
      // Make the API call
      const response = await this.client.get('/alerts', { params: request });
      
      const alerts = response.data.alerts || [];
      const nextPageToken = response.data.nextPageToken;
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        this.metrics.apiCalls++;
        this.metrics.eventsRetrieved += alerts.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      
      return {
        alerts,
        nextPageToken
      };
    } catch (error) {
      console.error('Error retrieving alerts from Chronicle:', error);
      throw error;
    }
  }
  
  /**
   * Get IOCs from Chronicle
   * @param {Object} params - Query parameters
   * @returns {Object} - IOCs and metadata
   */
  async getIocs(params = {}) {
    if (!this.client) {
      throw new Error('Chronicle client not initialized. Call initialize() first.');
    }
    
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    
    try {
      const {
        iocType,
        value,
        pageSize = this.options.pageSize,
        pageToken
      } = params;
      
      // Build the request
      const request = {
        pageSize,
        pageToken
      };
      
      if (iocType) {
        request.iocType = iocType;
      }
      
      if (value) {
        request.value = value;
      }
      
      // Make the API call
      const response = await this.client.get('/iocs', { params: request });
      
      const iocs = response.data.iocs || [];
      const nextPageToken = response.data.nextPageToken;
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        this.metrics.apiCalls++;
        this.metrics.eventsRetrieved += iocs.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      
      return {
        iocs,
        nextPageToken
      };
    } catch (error) {
      console.error('Error retrieving IOCs from Chronicle:', error);
      throw error;
    }
  }
  
  /**
   * Search for events in Chronicle
   * @param {Object} params - Query parameters
   * @returns {Object} - Events and metadata
   */
  async searchEvents(params = {}) {
    if (!this.client) {
      throw new Error('Chronicle client not initialized. Call initialize() first.');
    }
    
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    
    try {
      const {
        query,
        startTime: eventStartTime,
        endTime: eventEndTime,
        pageSize = this.options.pageSize,
        pageToken
      } = params;
      
      if (!query) {
        throw new Error('Query parameter is required');
      }
      
      // Build the request
      const request = {
        query,
        pageSize,
        pageToken
      };
      
      if (eventStartTime) {
        request.startTime = eventStartTime;
      }
      
      if (eventEndTime) {
        request.endTime = eventEndTime;
      }
      
      // Make the API call
      const response = await this.client.post('/events:search', request);
      
      const events = response.data.events || [];
      const nextPageToken = response.data.nextPageToken;
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        this.metrics.apiCalls++;
        this.metrics.eventsRetrieved += events.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      
      return {
        events,
        nextPageToken
      };
    } catch (error) {
      console.error('Error searching events in Chronicle:', error);
      throw error;
    }
  }
  
  /**
   * Normalize Chronicle alerts to NovaConnect format
   * @param {Array} alerts - Chronicle alerts
   * @returns {Array} - Normalized alerts
   */
  normalizeAlerts(alerts) {
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    
    try {
      // Define transformation rules for Chronicle alerts
      const rules = [
        { source: 'id', target: 'id' },
        { source: 'type', target: 'type' },
        { source: 'createdTime', target: 'createdAt', transform: 'normalizeChronicleTime' },
        { source: 'severity', target: 'severity', transform: 'lowercase' },
        { source: 'name', target: 'title' },
        { source: 'description', target: 'description' },
        { source: 'asset.hostname', target: 'resourceName' },
        { source: 'asset.assetType', target: 'resourceType' },
        { source: 'attackTechniques', target: 'attackTechniques' },
        { source: 'attackTechniques', target: 'nistControls', transform: 'mapAttackToNist' },
        { source: 'indicators', target: 'indicators', transform: 'extractIoc' }
      ];
      
      // Transform each alert
      const normalizedAlerts = alerts.map(alert => 
        this.transformationEngine.transform(alert, rules)
      );
      
      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        this.metrics.eventsNormalized += alerts.length;
        this.metrics.totalNormalizationTime += duration;
        this.metrics.averageNormalizationTime = 
          this.metrics.totalNormalizationTime / this.metrics.eventsNormalized;
      }
      
      return normalizedAlerts;
    } catch (error) {
      console.error('Error normalizing Chronicle alerts:', error);
      throw error;
    }
  }
  
  /**
   * Get and normalize alerts in a single call
   * @param {Object} params - Query parameters
   * @returns {Object} - Normalized alerts and metadata
   */
  async getAlertsNormalized(params = {}) {
    const { alerts, nextPageToken } = await this.getAlerts(params);
    const normalizedAlerts = this.normalizeAlerts(alerts);
    
    return {
      alerts: normalizedAlerts,
      nextPageToken
    };
  }
  
  /**
   * Get all alerts with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All alerts
   */
  async getAllAlerts(params = {}) {
    let allAlerts = [];
    let nextPageToken = null;
    
    do {
      const { alerts, nextPageToken: token } = await this.getAlerts({
        ...params,
        pageToken: nextPageToken
      });
      
      allAlerts = allAlerts.concat(alerts);
      nextPageToken = token;
    } while (nextPageToken);
    
    return allAlerts;
  }
  
  /**
   * Get all normalized alerts with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All normalized alerts
   */
  async getAllAlertsNormalized(params = {}) {
    const alerts = await this.getAllAlerts(params);
    return this.normalizeAlerts(alerts);
  }
  
  /**
   * Get metrics for the connector
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      transformationMetrics: this.transformationEngine.getMetrics()
    };
  }
  
  /**
   * Initialize MITRE ATT&CK to NIST mapping
   * @private
   */
  _initializeAttackToNistMapping() {
    // This is a simplified mapping - in a real implementation, this would be more comprehensive
    this.attackToNistMapping = {
      // Initial Access
      'T1189': ['AC-4', 'SI-3'], // Drive-by Compromise
      'T1190': ['AC-4', 'SI-3', 'SI-10'], // Exploit Public-Facing Application
      'T1133': ['AC-2', 'AC-3', 'AC-17'], // External Remote Services
      
      // Execution
      'T1059': ['CM-7', 'SI-3'], // Command and Scripting Interpreter
      'T1203': ['SI-3', 'SI-4'], // Exploitation for Client Execution
      
      // Persistence
      'T1136': ['AC-2'], // Create Account
      'T1098': ['AC-2', 'AC-3'], // Account Manipulation
      
      // Privilege Escalation
      'T1068': ['SI-2', 'RA-5'], // Exploitation for Privilege Escalation
      'T1078': ['AC-2', 'AC-3', 'AC-6'], // Valid Accounts
      
      // Defense Evasion
      'T1070': ['AU-9', 'SI-4'], // Indicator Removal on Host
      'T1027': ['SI-3', 'SI-4'], // Obfuscated Files or Information
      
      // Credential Access
      'T1110': ['AC-7', 'IA-5'], // Brute Force
      'T1003': ['IA-5', 'SI-4'], // OS Credential Dumping
      
      // Discovery
      'T1087': ['AC-2', 'SI-4'], // Account Discovery
      'T1082': ['CM-8', 'SI-4'], // System Information Discovery
      
      // Lateral Movement
      'T1021': ['AC-17', 'SI-4'], // Remote Services
      'T1091': ['AC-4', 'SI-3'], // Replication Through Removable Media
      
      // Collection
      'T1005': ['AC-3', 'SI-4'], // Data from Local System
      'T1039': ['AC-3', 'SI-4'], // Data from Network Shared Drive
      
      // Command and Control
      'T1071': ['SC-7', 'SI-4'], // Application Layer Protocol
      'T1105': ['SI-3', 'SI-4'], // Ingress Tool Transfer
      
      // Exfiltration
      'T1048': ['AC-4', 'SI-4'], // Exfiltration Over Alternative Protocol
      'T1041': ['AC-4', 'SI-4'], // Exfiltration Over C2 Channel
      
      // Impact
      'T1485': ['CP-9', 'CP-10'], // Data Destruction
      'T1486': ['CP-9', 'CP-10', 'SI-4'] // Data Encrypted for Impact
    };
  }
  
  /**
   * Map MITRE ATT&CK techniques to NIST controls
   * @param {Array} techniques - ATT&CK techniques
   * @returns {Array} - NIST controls
   * @private
   */
  _mapAttackToNist(techniques) {
    if (!techniques || !Array.isArray(techniques)) {
      return [];
    }
    
    const nistControls = new Set();
    
    for (const technique of techniques) {
      const techniqueId = technique.split('.')[0]; // Handle sub-techniques
      const controls = this.attackToNistMapping[techniqueId] || [];
      
      for (const control of controls) {
        nistControls.add(control);
      }
    }
    
    return Array.from(nistControls);
  }
  
  /**
   * Extract IOCs from indicators
   * @param {Array} indicators - Chronicle indicators
   * @returns {Array} - Extracted IOCs
   * @private
   */
  _extractIoc(indicators) {
    if (!indicators || !Array.isArray(indicators)) {
      return [];
    }
    
    return indicators.map(indicator => ({
      type: indicator.type,
      value: indicator.value,
      confidence: indicator.confidence
    }));
  }
  
  /**
   * Normalize Chronicle timestamp to Unix timestamp
   * @param {string} timestamp - Chronicle timestamp
   * @returns {number} - Unix timestamp
   * @private
   */
  _normalizeChronicleTime(timestamp) {
    if (!timestamp) return null;
    
    // Chronicle uses RFC 3339 format
    return new Date(timestamp).getTime();
  }
}

module.exports = ChronicleConnector;
