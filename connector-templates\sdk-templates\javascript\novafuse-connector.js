﻿/**
 * NovaFuse API Connector - JavaScript SDK
 * 
 * This SDK provides a simple interface to connect to the NovaFuse API Superstore.
 * 
 * @version 1.0.0
 */

class NovaFuseConnector {
  /**
   * Create a new NovaFuse connector
   * 
   * @param {Object} config - Configuration object
   * @param {string} config.apiKey - Your NovaFuse API key
   * @param {string} config.baseUrl - Base URL for the API (default: http://localhost:8000)
   * @param {string} config.category - API category (legal, security, etc.)
   */
  constructor(config) {
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl || "http://localhost:8000";
    this.category = config.category;
    
    if (!this.apiKey) {
      throw new Error("API key is required");
    }
    
    if (!this.category) {
      throw new Error("Category is required");
    }
  }
  
  /**
   * Make a request to the NovaFuse API
   * 
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method (GET, POST, etc.)
   * @param {Object} data - Request data (for POST, PUT, etc.)
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - API response
   */
  async request(endpoint, method = "GET", data = null, params = {}) {
    const url = new URL(`${this.baseUrl}/${this.category}${endpoint}`);
    
    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, params[key]);
      }
    });
    
    const options = {
      method,
      headers: {
        "Content-Type": "application/json",
        "apikey": this.apiKey
      }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error("NovaFuse API request failed:", error);
      throw error;
    }
  }
  
  /**
   * Check if the API is healthy
   * 
   * @returns {Promise<Object>} - Health status
   */
  async checkHealth() {
    return this.request("/health");
  }
}
