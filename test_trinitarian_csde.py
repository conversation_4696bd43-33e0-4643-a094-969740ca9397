#!/usr/bin/env python3
"""
Trinitarian CSDE Test

This script tests the Trinitarian CSDE implementation, verifying:
1. The Father (Governance) component with π-based compliance cycles
2. The Son (Detection) component with ϕ-based threat weighting
3. The Spirit (Response) component with (ℏ, c)-based adaptive response
4. The complete Trinitized CSDE formula
"""

import os
import sys
import json
import math
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Add the parent directory to the path so we can import the CSDE module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Import the Trinitarian CSDE
from csde.trinitarian_csde import TrinitarianCSDECore

# Create results directory
RESULTS_DIR = "trinitarian_csde_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_father_component():
    """
    Test the Father (Governance) component
    """
    print("\n=== Testing Father (Governance) Component ===")
    
    # Initialize Trinitarian CSDE
    csde = TrinitarianCSDECore()
    
    # Create test compliance data
    compliance_data = {
        "compliance_score": 0.85,
        "audit_frequency": 4,
        "controls": {
            "access_control": 0.9,
            "data_protection": 0.8,
            "incident_response": 0.85,
            "risk_management": 0.75,
            "governance": 0.9
        }
    }
    
    # Process Father component
    father_result = csde.father_component(compliance_data)
    
    # Print results
    print(f"Compliance Score: {compliance_data['compliance_score']}")
    print(f"Audit Frequency: {compliance_data['audit_frequency']}")
    print(f"π-based Compliance Cycles: {father_result['compliance_cycles']:.4f}")
    print(f"Governance Score: {father_result['governance_score']:.4f}")
    print(f"Father Component Result: {father_result['result']:.4f}")
    
    # Verify that π is correctly applied
    expected_cycles = math.pi * compliance_data["audit_frequency"]
    assert abs(father_result["compliance_cycles"] - expected_cycles) < 1e-6, "π-based compliance cycles incorrect"
    
    # Verify that the governance score is correctly calculated
    expected_score = compliance_data["compliance_score"] * expected_cycles
    assert abs(father_result["governance_score"] - expected_score) < 1e-6, "Governance score incorrect"
    
    # Verify that π scaling is correctly applied
    expected_result = expected_score * math.pi
    assert abs(father_result["result"] - expected_result) < 1e-6, "Father component result incorrect"
    
    print("Father component test PASSED")
    return father_result

def test_son_component():
    """
    Test the Son (Detection) component
    """
    print("\n=== Testing Son (Detection) Component ===")
    
    # Initialize Trinitarian CSDE
    csde = TrinitarianCSDECore()
    
    # Create test infrastructure data
    infrastructure_data = {
        "detection_capability": 0.75,
        "systems": {
            "firewall": 0.9,
            "ids": 0.8,
            "siem": 0.7,
            "endpoint": 0.6
        }
    }
    
    # Create test threat intelligence
    threat_intelligence = {
        "severity": 0.8,
        "confidence": 0.7,
        "threats": {
            "malware": 0.9,
            "phishing": 0.8,
            "ddos": 0.7,
            "insider": 0.6
        }
    }
    
    # Process Son component
    son_result = csde.son_component(infrastructure_data, threat_intelligence)
    
    # Print results
    print(f"Detection Capability: {infrastructure_data['detection_capability']}")
    print(f"Threat Severity: {threat_intelligence['severity']}")
    print(f"Threat Confidence: {threat_intelligence['confidence']}")
    print(f"ϕ-based Threat Weight: {son_result['threat_weight']:.4f}")
    print(f"Detection Score: {son_result['detection_score']:.4f}")
    print(f"Son Component Result: {son_result['result']:.4f}")
    
    # Verify that ϕ is correctly applied to threat weighting
    phi = (1 + math.sqrt(5)) / 2  # Golden ratio
    expected_weight = phi * threat_intelligence["severity"] + (1 - phi) * threat_intelligence["confidence"]
    assert abs(son_result["threat_weight"] - expected_weight) < 1e-6, "ϕ-based threat weight incorrect"
    
    # Verify that the detection score is correctly calculated
    expected_score = infrastructure_data["detection_capability"] * expected_weight
    assert abs(son_result["detection_score"] - expected_score) < 1e-6, "Detection score incorrect"
    
    # Verify that ϕ scaling is correctly applied
    expected_result = expected_score * phi
    assert abs(son_result["result"] - expected_result) < 1e-6, "Son component result incorrect"
    
    print("Son component test PASSED")
    return son_result

def test_spirit_component():
    """
    Test the Spirit (Response) component
    """
    print("\n=== Testing Spirit (Response) Component ===")
    
    # Initialize Trinitarian CSDE
    csde = TrinitarianCSDECore()
    
    # Create test threat data
    threat_data = {
        "severity": 0.9,
        "confidence": 0.8,
        "surface": {
            "endpoints": 100,
            "servers": 20,
            "networks": 5,
            "applications": 50
        }
    }
    
    # Process Spirit component
    spirit_result = csde.spirit_component(threat_data)
    
    # Print results
    print(f"Spirit Formula Results:")
    for key, value in spirit_result["spirit_formula_result"].items():
        print(f"  {key}: {value}")
    print(f"Response Score: {spirit_result['response_score']:.4f}")
    print(f"Spirit Component Result: {spirit_result['result']:.4f}")
    
    # Verify that the Spirit Formula is correctly applied
    formula_result = spirit_result["spirit_formula_result"]
    
    # Verify that c is correctly applied to response time limit
    assert formula_result["max_response_time"] > 0, "Speed of light constraint incorrect"
    
    # Verify that ℏ is correctly applied to entropy threshold
    assert formula_result["entropy_threshold"] > 0, "Quantum entropy threshold incorrect"
    
    # Verify that the target response time is within the speed of light constraint
    assert formula_result["target_response_time"] <= formula_result["max_response_time"], "Target response time exceeds speed of light constraint"
    
    # Verify that the response score is between 0 and 1
    assert 0 <= spirit_result["response_score"] <= 1, "Response score out of range"
    
    print("Spirit component test PASSED")
    return spirit_result

def test_trinitized_csde():
    """
    Test the complete Trinitized CSDE formula
    """
    print("\n=== Testing Trinitized CSDE Formula ===")
    
    # Initialize Trinitarian CSDE
    csde = TrinitarianCSDECore()
    
    # Create test data
    compliance_data = {
        "compliance_score": 0.85,
        "audit_frequency": 4,
        "controls": {
            "access_control": 0.9,
            "data_protection": 0.8,
            "incident_response": 0.85,
            "risk_management": 0.75,
            "governance": 0.9
        }
    }
    
    infrastructure_data = {
        "detection_capability": 0.75,
        "systems": {
            "firewall": 0.9,
            "ids": 0.8,
            "siem": 0.7,
            "endpoint": 0.6
        }
    }
    
    threat_intelligence = {
        "severity": 0.8,
        "confidence": 0.7,
        "threats": {
            "malware": 0.9,
            "phishing": 0.8,
            "ddos": 0.7,
            "insider": 0.6
        },
        "surface": {
            "endpoints": 100,
            "servers": 20,
            "networks": 5,
            "applications": 50
        }
    }
    
    # Process Trinitized CSDE
    trinitized_result = csde.process_trinitized(compliance_data, infrastructure_data, threat_intelligence)
    
    # Print results
    print(f"Trinitized CSDE Value: {trinitized_result['csde_trinitized']:.4f}")
    print(f"Father Component Result: {trinitized_result['father_component']['result']:.4f}")
    print(f"Son Component Result: {trinitized_result['son_component']['result']:.4f}")
    print(f"Spirit Component Result: {trinitized_result['spirit_component']['result']:.4f}")
    print(f"Performance Factor: {trinitized_result['performance_factor']}x")
    
    # Verify that the Trinitized CSDE value is the sum of the three components
    expected_value = (
        trinitized_result["father_component"]["result"] + 
        trinitized_result["son_component"]["result"] + 
        trinitized_result["spirit_component"]["result"]
    )
    assert abs(trinitized_result["csde_trinitized"] - expected_value) < 1e-6, "Trinitized CSDE value incorrect"
    
    # Save results to file
    result_file = os.path.join(RESULTS_DIR, f"trinitized_csde_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(result_file, 'w') as f:
        json.dump(trinitized_result, f, indent=2, default=lambda x: float(x) if isinstance(x, np.float32) else x)
    
    print(f"Results saved to {result_file}")
    print("Trinitized CSDE test PASSED")
    return trinitized_result

def visualize_trinity_components(father_result, son_result, spirit_result):
    """
    Visualize the Trinity components
    """
    print("\n=== Visualizing Trinity Components ===")
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Create data
    components = ['Father (Governance)', 'Son (Detection)', 'Spirit (Response)']
    values = [
        father_result['result'],
        son_result['result'],
        spirit_result['result']
    ]
    
    # Normalize values for better visualization
    total = sum(values)
    normalized_values = [v / total for v in values]
    
    # Create bar chart
    plt.bar(components, normalized_values, color=['#FF6B6B', '#4ECDC4', '#45B7D1'])
    
    # Add labels
    plt.title('Trinitarian CSDE Components', fontsize=16)
    plt.ylabel('Normalized Contribution', fontsize=14)
    plt.ylim(0, max(normalized_values) * 1.2)
    
    # Add value labels
    for i, v in enumerate(normalized_values):
        plt.text(i, v + 0.01, f"{v:.2f}", ha='center', fontsize=12)
    
    # Add constants labels
    plt.text(0, normalized_values[0] / 2, f"π = {math.pi:.4f}", ha='center', fontsize=12, color='white')
    plt.text(1, normalized_values[1] / 2, f"ϕ = {(1 + math.sqrt(5)) / 2:.4f}", ha='center', fontsize=12, color='white')
    plt.text(2, normalized_values[2] / 2, f"ℏ, c", ha='center', fontsize=12, color='white')
    
    # Save figure
    figure_file = os.path.join(RESULTS_DIR, f"trinity_components_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.savefig(figure_file, dpi=300, bbox_inches='tight')
    
    print(f"Visualization saved to {figure_file}")

def main():
    """
    Main test function
    """
    print("=== Trinitarian CSDE Test ===")
    print("Testing the Trinitarian CSDE implementation")
    
    # Run tests
    father_result = test_father_component()
    son_result = test_son_component()
    spirit_result = test_spirit_component()
    trinitized_result = test_trinitized_csde()
    
    # Visualize results
    try:
        visualize_trinity_components(father_result, son_result, spirit_result)
    except Exception as e:
        print(f"Visualization failed: {e}")
    
    # Summarize results
    print("\n=== Test Results Summary ===")
    print(f"Father (Governance) Component: PASS")
    print(f"Son (Detection) Component: PASS")
    print(f"Spirit (Response) Component: PASS")
    print(f"Trinitized CSDE Formula: PASS")
    
    print("\nCONCLUSION: Trinitarian CSDE implementation VALIDATED")

if __name__ == "__main__":
    main()
