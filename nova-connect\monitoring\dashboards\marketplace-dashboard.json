{"displayName": "NovaConnect UAC Marketplace Dashboard", "gridLayout": {"columns": 2, "widgets": [{"title": "CPU Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"kubernetes.io/container/cpu/utilization\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.\"pod_name\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Memory Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"kubernetes.io/container/memory/used_bytes\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["resource.label.\"pod_name\""]}}, "unitOverride": "By"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "HTTP Request Count", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/http_requests_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"method\"", "metric.label.\"status\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "HTTP Response Time", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/http_request_duration_seconds\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_PERCENTILE_99", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"path\""]}}, "unitOverride": "s"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "API Connector Executions", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/connector_executions_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"connector\"", "metric.label.\"status\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "API Connector Execution Time", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/connector_execution_duration_seconds\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"connector\""]}}, "unitOverride": "s"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Data Normalization Operations", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/normalization_operations_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"source\"", "metric.label.\"status\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Data Normalization Time", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/normalization_duration_seconds\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"source\""]}}, "unitOverride": "s"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "MongoDB Operations", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/mongodb_operations_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"operation\"", "metric.label.\"collection\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Redis Operations", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/redis_operations_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"operation\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "<PERSON><PERSON>", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/cache_hit_ratio\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN", "groupByFields": ["metric.label.\"cache\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}, {"title": "Error Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "metric.type=\"custom.googleapis.com/novafuse/errors_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregation": {"perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM", "groupByFields": ["metric.label.\"type\""]}}, "unitOverride": "1"}, "plotType": "LINE", "minAlignmentPeriod": "60s"}], "yAxis": {"label": "y1Axis", "scale": "LINEAR"}, "chartOptions": {"mode": "COLOR"}}}]}}