/**
 * IP Restriction Service
 * 
 * This service handles IP-based access restrictions.
 */

const fs = require('fs').promises;
const path = require('path');
const ipRangeCheck = require('ip-range-check');
const { ValidationError, AuthorizationError } = require('../utils/errors');

class IpRestrictionService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.ipRestrictionsFile = path.join(this.dataDir, 'ip_restrictions.json');
    this.defaultConfig = {
      enabled: false,
      mode: 'allowlist', // 'allowlist' or 'blocklist'
      allowlist: [],
      blocklist: [],
      rules: []
    };
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load IP restrictions from file
   */
  async loadRestrictions() {
    try {
      const data = await fs.readFile(this.ipRestrictionsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default config
        return this.defaultConfig;
      }
      console.error('Error loading IP restrictions:', error);
      throw error;
    }
  }

  /**
   * Save IP restrictions to file
   */
  async saveRestrictions(restrictions) {
    try {
      await fs.writeFile(this.ipRestrictionsFile, JSON.stringify(restrictions, null, 2));
    } catch (error) {
      console.error('Error saving IP restrictions:', error);
      throw error;
    }
  }

  /**
   * Check if an IP address is allowed
   */
  async isAllowed(ip) {
    const restrictions = await this.loadRestrictions();
    
    // If IP restrictions are disabled, allow all IPs
    if (!restrictions.enabled) {
      return true;
    }
    
    // Check if IP matches any rule
    for (const rule of restrictions.rules) {
      if (this.matchesRule(ip, rule)) {
        return rule.action === 'allow';
      }
    }
    
    // If no rule matches, check the mode
    if (restrictions.mode === 'allowlist') {
      // In allowlist mode, IP must be in the allowlist
      return this.isInList(ip, restrictions.allowlist);
    } else {
      // In blocklist mode, IP must not be in the blocklist
      return !this.isInList(ip, restrictions.blocklist);
    }
  }

  /**
   * Check if an IP address matches a rule
   */
  matchesRule(ip, rule) {
    // Check if IP matches the rule's IP range
    if (rule.ipRange) {
      return ipRangeCheck(ip, rule.ipRange);
    }
    
    // Check if IP matches the rule's IP address
    if (rule.ip) {
      return ip === rule.ip;
    }
    
    return false;
  }

  /**
   * Check if an IP address is in a list
   */
  isInList(ip, list) {
    for (const item of list) {
      // Check if item is an IP range
      if (item.includes('/') || item.includes('-')) {
        if (ipRangeCheck(ip, item)) {
          return true;
        }
      } else {
        // Check if item is an exact IP match
        if (ip === item) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * Add an IP address or range to the allowlist
   */
  async addToAllowlist(ipOrRange) {
    const restrictions = await this.loadRestrictions();
    
    // Validate IP or range
    if (!this.isValidIpOrRange(ipOrRange)) {
      throw new ValidationError('Invalid IP address or range');
    }
    
    // Check if IP or range is already in the allowlist
    if (restrictions.allowlist.includes(ipOrRange)) {
      return restrictions;
    }
    
    // Add IP or range to the allowlist
    restrictions.allowlist.push(ipOrRange);
    
    // Remove from blocklist if present
    const blocklist = restrictions.blocklist.filter(item => item !== ipOrRange);
    restrictions.blocklist = blocklist;
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Add an IP address or range to the blocklist
   */
  async addToBlocklist(ipOrRange) {
    const restrictions = await this.loadRestrictions();
    
    // Validate IP or range
    if (!this.isValidIpOrRange(ipOrRange)) {
      throw new ValidationError('Invalid IP address or range');
    }
    
    // Check if IP or range is already in the blocklist
    if (restrictions.blocklist.includes(ipOrRange)) {
      return restrictions;
    }
    
    // Add IP or range to the blocklist
    restrictions.blocklist.push(ipOrRange);
    
    // Remove from allowlist if present
    const allowlist = restrictions.allowlist.filter(item => item !== ipOrRange);
    restrictions.allowlist = allowlist;
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Remove an IP address or range from the allowlist
   */
  async removeFromAllowlist(ipOrRange) {
    const restrictions = await this.loadRestrictions();
    
    // Check if IP or range is in the allowlist
    if (!restrictions.allowlist.includes(ipOrRange)) {
      return restrictions;
    }
    
    // Remove IP or range from the allowlist
    const allowlist = restrictions.allowlist.filter(item => item !== ipOrRange);
    restrictions.allowlist = allowlist;
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Remove an IP address or range from the blocklist
   */
  async removeFromBlocklist(ipOrRange) {
    const restrictions = await this.loadRestrictions();
    
    // Check if IP or range is in the blocklist
    if (!restrictions.blocklist.includes(ipOrRange)) {
      return restrictions;
    }
    
    // Remove IP or range from the blocklist
    const blocklist = restrictions.blocklist.filter(item => item !== ipOrRange);
    restrictions.blocklist = blocklist;
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Add a rule
   */
  async addRule(rule) {
    const restrictions = await this.loadRestrictions();
    
    // Validate rule
    if (!rule.name) {
      throw new ValidationError('Rule name is required');
    }
    
    if (!rule.action || !['allow', 'block'].includes(rule.action)) {
      throw new ValidationError('Rule action must be "allow" or "block"');
    }
    
    if (!rule.ipRange && !rule.ip) {
      throw new ValidationError('Rule must have an IP address or range');
    }
    
    if (rule.ipRange && !this.isValidIpOrRange(rule.ipRange)) {
      throw new ValidationError('Invalid IP range');
    }
    
    if (rule.ip && !this.isValidIp(rule.ip)) {
      throw new ValidationError('Invalid IP address');
    }
    
    // Check if rule with the same name already exists
    const existingRuleIndex = restrictions.rules.findIndex(r => r.name === rule.name);
    
    if (existingRuleIndex !== -1) {
      // Update existing rule
      restrictions.rules[existingRuleIndex] = rule;
    } else {
      // Add new rule
      restrictions.rules.push(rule);
    }
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Remove a rule
   */
  async removeRule(ruleName) {
    const restrictions = await this.loadRestrictions();
    
    // Check if rule exists
    const ruleIndex = restrictions.rules.findIndex(r => r.name === ruleName);
    
    if (ruleIndex === -1) {
      return restrictions;
    }
    
    // Remove rule
    restrictions.rules.splice(ruleIndex, 1);
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Update IP restrictions configuration
   */
  async updateConfig(config) {
    const restrictions = await this.loadRestrictions();
    
    // Update config
    if (config.enabled !== undefined) {
      restrictions.enabled = !!config.enabled;
    }
    
    if (config.mode !== undefined) {
      if (!['allowlist', 'blocklist'].includes(config.mode)) {
        throw new ValidationError('Mode must be "allowlist" or "blocklist"');
      }
      
      restrictions.mode = config.mode;
    }
    
    // Save restrictions
    await this.saveRestrictions(restrictions);
    
    return restrictions;
  }

  /**
   * Reset IP restrictions to default
   */
  async resetToDefault() {
    await this.saveRestrictions(this.defaultConfig);
    return this.defaultConfig;
  }

  /**
   * Validate an IP address or range
   */
  isValidIpOrRange(ipOrRange) {
    // Check if it's an IP range with CIDR notation (e.g., ***********/24)
    if (ipOrRange.includes('/')) {
      const [ip, cidr] = ipOrRange.split('/');
      return this.isValidIp(ip) && this.isValidCidr(cidr);
    }
    
    // Check if it's an IP range with hyphen notation (e.g., ***********-************)
    if (ipOrRange.includes('-')) {
      const [startIp, endIp] = ipOrRange.split('-');
      return this.isValidIp(startIp) && this.isValidIp(endIp);
    }
    
    // Check if it's a single IP address
    return this.isValidIp(ipOrRange);
  }

  /**
   * Validate an IP address
   */
  isValidIp(ip) {
    // Simple IPv4 validation
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    
    if (!ipv4Regex.test(ip)) {
      return false;
    }
    
    // Check if each octet is between 0 and 255
    const octets = ip.split('.');
    
    for (const octet of octets) {
      const num = parseInt(octet, 10);
      if (num < 0 || num > 255) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Validate a CIDR value
   */
  isValidCidr(cidr) {
    const num = parseInt(cidr, 10);
    return !isNaN(num) && num >= 0 && num <= 32;
  }
}

module.exports = IpRestrictionService;
