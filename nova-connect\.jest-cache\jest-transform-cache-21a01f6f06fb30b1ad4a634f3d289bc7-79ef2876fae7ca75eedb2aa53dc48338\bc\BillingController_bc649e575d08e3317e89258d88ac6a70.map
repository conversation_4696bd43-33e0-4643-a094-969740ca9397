{"version": 3, "names": ["BillingService", "require", "logger", "billingService", "handleEntitlementCreation", "req", "res", "next", "customerId", "entitlement", "body", "status", "json", "error", "message", "enableFeatures", "handleEntitlementUpdate", "updateFeatures", "handleEntitlementDeletion", "disableFeatures", "handleEntitlementActivation", "activateFeatures", "handleEntitlementSuspension", "suspendFeatures", "getCustomerEntitlements", "params", "getCustomerUsage", "startDate", "endDate", "query", "usage", "reportUsage", "metricName", "quantity", "timestamp", "tenantId", "undefined", "reportTenantUsage", "handleMarketplaceWebhook", "event", "resource", "info", "customer_id", "warn", "module", "exports"], "sources": ["BillingController.js"], "sourcesContent": ["/**\n * Billing Controller\n * \n * This controller handles billing-related operations, including\n * GCP Marketplace entitlements and usage reporting.\n */\n\nconst BillingService = require('../services/BillingService');\nconst logger = require('../../config/logger');\n\n// Initialize services\nconst billingService = new BillingService();\n\n/**\n * Handle GCP Marketplace entitlement creation\n */\nconst handleEntitlementCreation = async (req, res, next) => {\n  try {\n    const { customerId, entitlement } = req.body;\n    \n    if (!customerId || !entitlement) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID and entitlement are required'\n      });\n    }\n    \n    await billingService.enableFeatures(customerId, entitlement);\n    \n    res.status(200).json({\n      message: 'Entitlement created successfully',\n      customerId,\n      entitlement: {\n        ...entitlement,\n        status: 'ACTIVE'\n      }\n    });\n  } catch (error) {\n    logger.error('Error handling entitlement creation', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Handle GCP Marketplace entitlement update\n */\nconst handleEntitlementUpdate = async (req, res, next) => {\n  try {\n    const { customerId, entitlement } = req.body;\n    \n    if (!customerId || !entitlement) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID and entitlement are required'\n      });\n    }\n    \n    await billingService.updateFeatures(customerId, entitlement);\n    \n    res.status(200).json({\n      message: 'Entitlement updated successfully',\n      customerId,\n      entitlement\n    });\n  } catch (error) {\n    logger.error('Error handling entitlement update', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Handle GCP Marketplace entitlement deletion\n */\nconst handleEntitlementDeletion = async (req, res, next) => {\n  try {\n    const { customerId, entitlement } = req.body;\n    \n    if (!customerId) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID is required'\n      });\n    }\n    \n    await billingService.disableFeatures(customerId, entitlement);\n    \n    res.status(200).json({\n      message: 'Entitlement deleted successfully',\n      customerId\n    });\n  } catch (error) {\n    logger.error('Error handling entitlement deletion', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Handle GCP Marketplace entitlement activation\n */\nconst handleEntitlementActivation = async (req, res, next) => {\n  try {\n    const { customerId, entitlement } = req.body;\n    \n    if (!customerId) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID is required'\n      });\n    }\n    \n    await billingService.activateFeatures(customerId, entitlement);\n    \n    res.status(200).json({\n      message: 'Entitlement activated successfully',\n      customerId\n    });\n  } catch (error) {\n    logger.error('Error handling entitlement activation', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Handle GCP Marketplace entitlement suspension\n */\nconst handleEntitlementSuspension = async (req, res, next) => {\n  try {\n    const { customerId, entitlement } = req.body;\n    \n    if (!customerId) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID is required'\n      });\n    }\n    \n    await billingService.suspendFeatures(customerId, entitlement);\n    \n    res.status(200).json({\n      message: 'Entitlement suspended successfully',\n      customerId\n    });\n  } catch (error) {\n    logger.error('Error handling entitlement suspension', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Get customer entitlements\n */\nconst getCustomerEntitlements = async (req, res, next) => {\n  try {\n    const { customerId } = req.params;\n    \n    if (!customerId) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID is required'\n      });\n    }\n    \n    const entitlement = await billingService.getCustomerEntitlements(customerId);\n    \n    res.status(200).json(entitlement);\n  } catch (error) {\n    logger.error('Error getting customer entitlements', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Get customer usage\n */\nconst getCustomerUsage = async (req, res, next) => {\n  try {\n    const { customerId } = req.params;\n    const { startDate, endDate } = req.query;\n    \n    if (!customerId) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID is required'\n      });\n    }\n    \n    const usage = await billingService.getCustomerUsage(customerId, startDate, endDate);\n    \n    res.status(200).json(usage);\n  } catch (error) {\n    logger.error('Error getting customer usage', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Report usage\n */\nconst reportUsage = async (req, res, next) => {\n  try {\n    const { customerId, metricName, quantity, timestamp, tenantId } = req.body;\n    \n    if (!customerId || !metricName || quantity === undefined) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID, metric name, and quantity are required'\n      });\n    }\n    \n    await billingService.reportUsage(customerId, metricName, quantity, timestamp, tenantId);\n    \n    res.status(200).json({\n      message: 'Usage reported successfully',\n      customerId,\n      metricName,\n      quantity\n    });\n  } catch (error) {\n    logger.error('Error reporting usage', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Report tenant usage\n */\nconst reportTenantUsage = async (req, res, next) => {\n  try {\n    const { tenantId, metricName, quantity, timestamp } = req.body;\n    \n    if (!tenantId || !metricName || quantity === undefined) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Tenant ID, metric name, and quantity are required'\n      });\n    }\n    \n    await billingService.reportTenantUsage(tenantId, metricName, quantity, timestamp);\n    \n    res.status(200).json({\n      message: 'Tenant usage reported successfully',\n      tenantId,\n      metricName,\n      quantity\n    });\n  } catch (error) {\n    logger.error('Error reporting tenant usage', { error: error.message });\n    next(error);\n  }\n};\n\n/**\n * Handle GCP Marketplace webhook\n */\nconst handleMarketplaceWebhook = async (req, res, next) => {\n  try {\n    const { event, resource } = req.body;\n    \n    if (!event || !resource) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Event and resource are required'\n      });\n    }\n    \n    logger.info('Received GCP Marketplace webhook', { event, resource });\n    \n    // Extract customer ID from resource\n    const customerId = resource.customerId || resource.customer_id;\n    \n    if (!customerId) {\n      return res.status(400).json({\n        error: 'Bad Request',\n        message: 'Customer ID is required in resource'\n      });\n    }\n    \n    // Handle different event types\n    switch (event) {\n      case 'ENTITLEMENT_CREATION':\n        await billingService.enableFeatures(customerId, resource);\n        break;\n      case 'ENTITLEMENT_UPDATE':\n        await billingService.updateFeatures(customerId, resource);\n        break;\n      case 'ENTITLEMENT_DELETION':\n        await billingService.disableFeatures(customerId, resource);\n        break;\n      case 'ENTITLEMENT_ACTIVATION':\n        await billingService.activateFeatures(customerId, resource);\n        break;\n      case 'ENTITLEMENT_SUSPENSION':\n        await billingService.suspendFeatures(customerId, resource);\n        break;\n      default:\n        logger.warn('Unknown event type', { event });\n        return res.status(400).json({\n          error: 'Bad Request',\n          message: `Unknown event type: ${event}`\n        });\n    }\n    \n    res.status(200).json({\n      message: 'Webhook processed successfully',\n      event,\n      customerId\n    });\n  } catch (error) {\n    logger.error('Error handling marketplace webhook', { error: error.message });\n    next(error);\n  }\n};\n\nmodule.exports = {\n  handleEntitlementCreation,\n  handleEntitlementUpdate,\n  handleEntitlementDeletion,\n  handleEntitlementActivation,\n  handleEntitlementSuspension,\n  getCustomerEntitlements,\n  getCustomerUsage,\n  reportUsage,\n  reportTenantUsage,\n  handleMarketplaceWebhook\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,cAAc,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAC5D,MAAMC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;;AAE7C;AACA,MAAME,cAAc,GAAG,IAAIH,cAAc,CAAC,CAAC;;AAE3C;AACA;AACA;AACA,MAAMI,yBAAyB,GAAG,MAAAA,CAAOC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC1D,IAAI;IACF,MAAM;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAGJ,GAAG,CAACK,IAAI;IAE5C,IAAI,CAACF,UAAU,IAAI,CAACC,WAAW,EAAE;MAC/B,OAAOH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAACY,cAAc,CAACP,UAAU,EAAEC,WAAW,CAAC;IAE5DH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,kCAAkC;MAC3CN,UAAU;MACVC,WAAW,EAAE;QACX,GAAGA,WAAW;QACdE,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,qCAAqC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC7EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMG,uBAAuB,GAAG,MAAAA,CAAOX,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACxD,IAAI;IACF,MAAM;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAGJ,GAAG,CAACK,IAAI;IAE5C,IAAI,CAACF,UAAU,IAAI,CAACC,WAAW,EAAE;MAC/B,OAAOH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAACc,cAAc,CAACT,UAAU,EAAEC,WAAW,CAAC;IAE5DH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,kCAAkC;MAC3CN,UAAU;MACVC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,mCAAmC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC3EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMK,yBAAyB,GAAG,MAAAA,CAAOb,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC1D,IAAI;IACF,MAAM;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAGJ,GAAG,CAACK,IAAI;IAE5C,IAAI,CAACF,UAAU,EAAE;MACf,OAAOF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAACgB,eAAe,CAACX,UAAU,EAAEC,WAAW,CAAC;IAE7DH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,kCAAkC;MAC3CN;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,qCAAqC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC7EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMO,2BAA2B,GAAG,MAAAA,CAAOf,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC5D,IAAI;IACF,MAAM;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAGJ,GAAG,CAACK,IAAI;IAE5C,IAAI,CAACF,UAAU,EAAE;MACf,OAAOF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAACkB,gBAAgB,CAACb,UAAU,EAAEC,WAAW,CAAC;IAE9DH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,oCAAoC;MAC7CN;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,uCAAuC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC/EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMS,2BAA2B,GAAG,MAAAA,CAAOjB,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC5D,IAAI;IACF,MAAM;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAGJ,GAAG,CAACK,IAAI;IAE5C,IAAI,CAACF,UAAU,EAAE;MACf,OAAOF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAACoB,eAAe,CAACf,UAAU,EAAEC,WAAW,CAAC;IAE7DH,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,oCAAoC;MAC7CN;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,uCAAuC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC/EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMW,uBAAuB,GAAG,MAAAA,CAAOnB,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACxD,IAAI;IACF,MAAM;MAAEC;IAAW,CAAC,GAAGH,GAAG,CAACoB,MAAM;IAEjC,IAAI,CAACjB,UAAU,EAAE;MACf,OAAOF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAML,WAAW,GAAG,MAAMN,cAAc,CAACqB,uBAAuB,CAAChB,UAAU,CAAC;IAE5EF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAACH,WAAW,CAAC;EACnC,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,qCAAqC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC7EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMa,gBAAgB,GAAG,MAAAA,CAAOrB,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACjD,IAAI;IACF,MAAM;MAAEC;IAAW,CAAC,GAAGH,GAAG,CAACoB,MAAM;IACjC,MAAM;MAAEE,SAAS;MAAEC;IAAQ,CAAC,GAAGvB,GAAG,CAACwB,KAAK;IAExC,IAAI,CAACrB,UAAU,EAAE;MACf,OAAOF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMgB,KAAK,GAAG,MAAM3B,cAAc,CAACuB,gBAAgB,CAAClB,UAAU,EAAEmB,SAAS,EAAEC,OAAO,CAAC;IAEnFtB,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAACkB,KAAK,CAAC;EAC7B,CAAC,CAAC,OAAOjB,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,8BAA8B,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IACtEP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMkB,WAAW,GAAG,MAAAA,CAAO1B,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAC5C,IAAI;IACF,MAAM;MAAEC,UAAU;MAAEwB,UAAU;MAAEC,QAAQ;MAAEC,SAAS;MAAEC;IAAS,CAAC,GAAG9B,GAAG,CAACK,IAAI;IAE1E,IAAI,CAACF,UAAU,IAAI,CAACwB,UAAU,IAAIC,QAAQ,KAAKG,SAAS,EAAE;MACxD,OAAO9B,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAAC4B,WAAW,CAACvB,UAAU,EAAEwB,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,CAAC;IAEvF7B,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,6BAA6B;MACtCN,UAAU;MACVwB,UAAU;MACVC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOpB,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,uBAAuB,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC/DP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMwB,iBAAiB,GAAG,MAAAA,CAAOhC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EAClD,IAAI;IACF,MAAM;MAAE4B,QAAQ;MAAEH,UAAU;MAAEC,QAAQ;MAAEC;IAAU,CAAC,GAAG7B,GAAG,CAACK,IAAI;IAE9D,IAAI,CAACyB,QAAQ,IAAI,CAACH,UAAU,IAAIC,QAAQ,KAAKG,SAAS,EAAE;MACtD,OAAO9B,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEA,MAAMX,cAAc,CAACkC,iBAAiB,CAACF,QAAQ,EAAEH,UAAU,EAAEC,QAAQ,EAAEC,SAAS,CAAC;IAEjF5B,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,oCAAoC;MAC7CqB,QAAQ;MACRH,UAAU;MACVC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOpB,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,8BAA8B,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IACtEP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMyB,wBAAwB,GAAG,MAAAA,CAAOjC,GAAG,EAAEC,GAAG,EAAEC,IAAI,KAAK;EACzD,IAAI;IACF,MAAM;MAAEgC,KAAK;MAAEC;IAAS,CAAC,GAAGnC,GAAG,CAACK,IAAI;IAEpC,IAAI,CAAC6B,KAAK,IAAI,CAACC,QAAQ,EAAE;MACvB,OAAOlC,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;IAEAZ,MAAM,CAACuC,IAAI,CAAC,kCAAkC,EAAE;MAAEF,KAAK;MAAEC;IAAS,CAAC,CAAC;;IAEpE;IACA,MAAMhC,UAAU,GAAGgC,QAAQ,CAAChC,UAAU,IAAIgC,QAAQ,CAACE,WAAW;IAE9D,IAAI,CAAClC,UAAU,EAAE;MACf,OAAOF,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QAC1BC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;;IAEA;IACA,QAAQyB,KAAK;MACX,KAAK,sBAAsB;QACzB,MAAMpC,cAAc,CAACY,cAAc,CAACP,UAAU,EAAEgC,QAAQ,CAAC;QACzD;MACF,KAAK,oBAAoB;QACvB,MAAMrC,cAAc,CAACc,cAAc,CAACT,UAAU,EAAEgC,QAAQ,CAAC;QACzD;MACF,KAAK,sBAAsB;QACzB,MAAMrC,cAAc,CAACgB,eAAe,CAACX,UAAU,EAAEgC,QAAQ,CAAC;QAC1D;MACF,KAAK,wBAAwB;QAC3B,MAAMrC,cAAc,CAACkB,gBAAgB,CAACb,UAAU,EAAEgC,QAAQ,CAAC;QAC3D;MACF,KAAK,wBAAwB;QAC3B,MAAMrC,cAAc,CAACoB,eAAe,CAACf,UAAU,EAAEgC,QAAQ,CAAC;QAC1D;MACF;QACEtC,MAAM,CAACyC,IAAI,CAAC,oBAAoB,EAAE;UAAEJ;QAAM,CAAC,CAAC;QAC5C,OAAOjC,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;UAC1BC,KAAK,EAAE,aAAa;UACpBC,OAAO,EAAE,uBAAuByB,KAAK;QACvC,CAAC,CAAC;IACN;IAEAjC,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;MACnBE,OAAO,EAAE,gCAAgC;MACzCyB,KAAK;MACL/B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdX,MAAM,CAACW,KAAK,CAAC,oCAAoC,EAAE;MAAEA,KAAK,EAAEA,KAAK,CAACC;IAAQ,CAAC,CAAC;IAC5EP,IAAI,CAACM,KAAK,CAAC;EACb;AACF,CAAC;AAED+B,MAAM,CAACC,OAAO,GAAG;EACfzC,yBAAyB;EACzBY,uBAAuB;EACvBE,yBAAyB;EACzBE,2BAA2B;EAC3BE,2BAA2B;EAC3BE,uBAAuB;EACvBE,gBAAgB;EAChBK,WAAW;EACXM,iBAAiB;EACjBC;AACF,CAAC", "ignoreList": []}