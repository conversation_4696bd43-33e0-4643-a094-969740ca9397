/**
 * HelpButton Component
 * 
 * A button for triggering help functionality.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useHelp } from '../help/HelpContext';

/**
 * HelpButton component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.helpId] - Help ID to show when clicked
 * @param {string} [props.variant='icon'] - Button variant (icon, text, or fab)
 * @param {string} [props.size='medium'] - Button size (small, medium, or large)
 * @param {string} [props.label] - Button label (for text variant)
 * @param {boolean} [props.showBadge=false] - Whether to show a badge for unread help
 * @param {Function} [props.onClick] - Function to call when button is clicked
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} HelpButton component
 */
const HelpButton = ({
  helpId,
  variant = 'icon',
  size = 'medium',
  label,
  showBadge = false,
  onClick,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const {
    openHelpPanel,
    isHelpViewed
  } = useHelp();
  
  // Determine if help has been viewed
  const isViewed = helpId ? isHelpViewed(helpId) : true;
  
  // Handle click
  const handleClick = (e) => {
    if (onClick) {
      onClick(e);
    } else if (helpId) {
      openHelpPanel(helpId);
    } else {
      openHelpPanel();
    }
  };
  
  // Get button label
  const buttonLabel = label || translate('help.helpButton', 'Help');
  
  // Get button classes
  const buttonClasses = [
    'help-button',
    `help-button--${variant}`,
    `help-button--${size}`,
    showBadge && !isViewed ? 'help-button--unread' : '',
    className
  ].filter(Boolean).join(' ');
  
  // Render icon variant
  if (variant === 'icon') {
    return (
      <button
        className={buttonClasses}
        onClick={handleClick}
        aria-label={buttonLabel}
        style={style}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
        
        {showBadge && !isViewed && (
          <span className="help-button__badge" aria-hidden="true" />
        )}
      </button>
    );
  }
  
  // Render floating action button variant
  if (variant === 'fab') {
    return (
      <button
        className={buttonClasses}
        onClick={handleClick}
        aria-label={buttonLabel}
        style={style}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
        
        {showBadge && !isViewed && (
          <span className="help-button__badge" aria-hidden="true" />
        )}
      </button>
    );
  }
  
  // Render text variant
  return (
    <button
      className={buttonClasses}
      onClick={handleClick}
      style={style}
    >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
        <line x1="12" y1="17" x2="12.01" y2="17"></line>
      </svg>
      
      <span className="help-button__label">
        {buttonLabel}
      </span>
      
      {showBadge && !isViewed && (
        <span className="help-button__badge" aria-hidden="true" />
      )}
    </button>
  );
};

HelpButton.propTypes = {
  helpId: PropTypes.string,
  variant: PropTypes.oneOf(['icon', 'text', 'fab']),
  size: PropTypes.oneOf(['small', 'medium', 'large']),
  label: PropTypes.string,
  showBadge: PropTypes.bool,
  onClick: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default HelpButton;
