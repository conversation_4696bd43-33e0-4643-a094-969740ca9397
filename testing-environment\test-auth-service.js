const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const crypto = require('crypto');

const app = express();
const port = 3007;

// In-memory storage for credentials
const credentials = [];

// Encryption key (in a real app, this would be stored securely)
const encryptionKey = 'test-encryption-key';

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Encryption/decryption functions
const encrypt = (data) => {
  const cipher = crypto.createCipher('aes-256-cbc', encryptionKey);
  let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};

const decrypt = (data) => {
  const decipher = crypto.createDecipher('aes-256-cbc', encryptionKey);
  let decrypted = decipher.update(data, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return JSON.parse(decrypted);
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Get all credentials (without sensitive data)
app.get('/credentials', (req, res) => {
  const userId = req.query.userId;
  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }
  
  const userCredentials = credentials
    .filter(c => c.userId === userId)
    .map(({ encryptedCredentials, ...rest }) => rest);
  
  res.json(userCredentials);
});

// Get credential by ID (without sensitive data)
app.get('/credentials/:id', (req, res) => {
  const credential = credentials.find(c => c.id === req.params.id);
  if (!credential) {
    return res.status(404).json({ error: 'Credential not found' });
  }
  
  const { encryptedCredentials, ...rest } = credential;
  res.json(rest);
});

// Create a new credential
app.post('/credentials', (req, res) => {
  const { name, connectorId, authType, credentials: creds, userId } = req.body;
  
  if (!name || !connectorId || !authType || !creds || !userId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  
  const credential = {
    id: `credential-${Date.now()}`,
    name,
    connectorId,
    authType,
    encryptedCredentials: encrypt(creds),
    userId,
    created: new Date().toISOString(),
    updated: new Date().toISOString()
  };
  
  credentials.push(credential);
  
  const { encryptedCredentials, ...rest } = credential;
  res.status(201).json(rest);
});

// Update a credential
app.put('/credentials/:id', (req, res) => {
  const index = credentials.findIndex(c => c.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Credential not found' });
  }
  
  const { name, credentials: creds } = req.body;
  const updates = { updated: new Date().toISOString() };
  
  if (name) {
    updates.name = name;
  }
  
  if (creds) {
    updates.encryptedCredentials = encrypt(creds);
  }
  
  credentials[index] = {
    ...credentials[index],
    ...updates
  };
  
  const { encryptedCredentials, ...rest } = credentials[index];
  res.json(rest);
});

// Delete a credential
app.delete('/credentials/:id', (req, res) => {
  const index = credentials.findIndex(c => c.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ error: 'Credential not found' });
  }
  
  credentials.splice(index, 1);
  res.status(204).send();
});

// Get decrypted credentials (for internal use by connector executor)
app.get('/credentials/:id/decrypt', (req, res) => {
  const credential = credentials.find(c => c.id === req.params.id);
  if (!credential) {
    return res.status(404).json({ error: 'Credential not found' });
  }
  
  res.json({
    id: credential.id,
    authType: credential.authType,
    credentials: decrypt(credential.encryptedCredentials)
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Authentication service running on port ${port}`);
});
