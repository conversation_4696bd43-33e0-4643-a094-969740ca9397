/**
 * ESG API - Entry Point
 * 
 * This is the entry point for the ESG API.
 */

const express = require('express');
const mongoose = require('mongoose');
const logger = require('../../utils/logger');

// Import routes
const metricsRoutes = require('./metrics/routes');
const reportingRoutes = require('./reporting/routes');
const initiativesRoutes = require('./initiatives/routes');

// Create Express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/esg/metrics', metricsRoutes);
app.use('/api/esg/reporting', reportingRoutes);
app.use('/api/esg/initiatives', initiativesRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'esg-api',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`, { service: 'esg-api', error: err });
  res.status(err.status || 500).json({
    success: false,
    error: err.name || 'Internal Server Error',
    message: err.message || 'Something went wrong'
  });
});

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/novagrc', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    logger.info('Connected to MongoDB', { service: 'esg-api' });
  } catch (error) {
    logger.error(`Error connecting to MongoDB: ${error.message}`, { service: 'esg-api', error });
    process.exit(1);
  }
};

// Start server
const PORT = process.env.PORT || 3006;
const startServer = async () => {
  await connectDB();
  app.listen(PORT, () => {
    logger.info(`ESG API running on port ${PORT}`, { service: 'esg-api' });
  });
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = app;
