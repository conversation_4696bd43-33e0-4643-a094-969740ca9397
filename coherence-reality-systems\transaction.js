const { keccak256 } = require('ethereum-cryptography/keccak');
const { toHex, utf8ToBytes } = require('ethereum-cryptography/utils');
const { sign, recoverPublicKey, recoverAddress } = require('./crypto');

class Transaction {
  /**
   * Create a new transaction
   * @param {Object} tx - Transaction data
   * @param {string} tx.from - Sender address
   * @param {string} tx.to - Recipient address
   * @param {string|BN} tx.value - Amount to transfer (in wei)
   * @param {string|BN} tx.nonce - Sender's transaction count
   * @param {string|Buffer} tx.data - Transaction data
   * @param {string|BN} tx.gasPrice - Gas price in wei
   * @param {string|BN} tx.gasLimit - Gas limit
   */
  constructor({ from, to, value, nonce, data = '0x', gasPrice = '0', gasLimit = '21000' }) {
    this.from = from;
    this.to = to;
    this.value = typeof value === 'string' ? value : value.toString();
    this.nonce = typeof nonce === 'string' ? nonce : nonce.toString();
    this.data = data;
    this.gasPrice = typeof gasPrice === 'string' ? gasPrice : gasPrice.toString();
    this.gasLimit = typeof gasLimit === 'string' ? gasLimit : gasLimit.toString();
    this.signature = null;
  }

  /**
   * Sign the transaction with a private key
   * @param {string} privateKey - Sender's private key
   * @returns {string} - Signature
   */
  sign(privateKey) {
    const txHash = this.hash();
    this.signature = sign(txHash, privateKey);
    return this.signature;
  }

  /**
   * Verify the transaction signature
   * @returns {boolean} - True if signature is valid
   */
  verify() {
    if (!this.signature) return false;
    
    const txHash = this.hash();
    const publicKey = recoverPublicKey(txHash, this.signature);
    const recoveredAddress = recoverAddress(publicKey);
    
    return recoveredAddress.toLowerCase() === this.from.toLowerCase();
  }

  /**
   * Calculate transaction hash
   * @returns {string} - Transaction hash
   */
  hash() {
    const txData = {
      from: this.from,
      to: this.to,
      value: this.value,
      nonce: this.nonce,
      data: this.data,
      gasPrice: this.gasPrice,
      gasLimit: this.gasLimit
    };
    
    const serializedTx = JSON.stringify(txData, Object.keys(txData).sort());
    return toHex(keccak256(utf8ToBytes(serializedTx)));
  }

  /**
   * Serialize transaction to JSON
   * @returns {Object} - Serialized transaction
   */
  toJSON() {
    return {
      from: this.from,
      to: this.to,
      value: this.value,
      nonce: this.nonce,
      data: this.data,
      gasPrice: this.gasPrice,
      gasLimit: this.gasLimit,
      signature: this.signature
    };
  }

  /**
   * Create Transaction from JSON data
   * @param {Object} json - Serialized transaction data
   * @returns {Transaction} - Transaction instance
   */
  static fromJSON(json) {
    const tx = new Transaction({
      from: json.from,
      to: json.to,
      value: json.value,
      nonce: json.nonce,
      data: json.data,
      gasPrice: json.gasPrice,
      gasLimit: json.gasLimit
    });
    
    if (json.signature) {
      tx.signature = json.signature;
    }
    
    return tx;
  }
}

module.exports = Transaction;
