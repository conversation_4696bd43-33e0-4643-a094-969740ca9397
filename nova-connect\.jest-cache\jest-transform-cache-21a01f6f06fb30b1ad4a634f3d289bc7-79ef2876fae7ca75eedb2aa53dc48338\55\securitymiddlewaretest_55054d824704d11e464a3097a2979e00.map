{"version": 3, "names": ["createRateLimiter", "createInputValidator", "createSecurityHeaders", "require", "express", "request", "describe", "app", "beforeEach", "use", "json", "windowMs", "maxRequests", "get", "req", "res", "status", "message", "test", "i", "response", "expect", "toBe", "body", "toEqual", "headers", "parseInt", "toBeLessThanOrEqual", "error", "toBeDefined", "Promise", "resolve", "setTimeout", "newResponse", "id", "params", "query", "post", "name", "age", "send", "command", "user", "profile", "bio"], "sources": ["security-middleware.test.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Security Middleware Tests\n * \n * This module contains tests for the security middleware.\n */\n\nconst { createRateLimiter, createInputValidator, createSecurityHeaders } = require('../../../api/middleware/security-middleware');\nconst express = require('express');\nconst request = require('supertest');\n\ndescribe('Security Middleware', () => {\n  describe('Rate Limiter', () => {\n    let app;\n    \n    beforeEach(() => {\n      app = express();\n      app.use(express.json());\n      \n      // Add rate limiter middleware\n      app.use(createRateLimiter({\n        windowMs: 1000, // 1 second\n        maxRequests: 3 // 3 requests per second\n      }));\n      \n      // Add test route\n      app.get('/test', (req, res) => {\n        res.status(200).json({ message: 'Success' });\n      });\n    });\n    \n    test('should allow requests within rate limit', async () => {\n      // Make 3 requests (within limit)\n      for (let i = 0; i < 3; i++) {\n        const response = await request(app).get('/test');\n        expect(response.status).toBe(200);\n        expect(response.body).toEqual({ message: 'Success' });\n        expect(response.headers['x-ratelimit-limit']).toBe('3');\n        expect(parseInt(response.headers['x-ratelimit-remaining'])).toBeLessThanOrEqual(3 - i - 1);\n      }\n    });\n    \n    test('should block requests exceeding rate limit', async () => {\n      // Make 3 requests (within limit)\n      for (let i = 0; i < 3; i++) {\n        const response = await request(app).get('/test');\n        expect(response.status).toBe(200);\n      }\n      \n      // Make 4th request (exceeding limit)\n      const response = await request(app).get('/test');\n      expect(response.status).toBe(429);\n      expect(response.body).toEqual({\n        error: 'Too many requests',\n        message: 'Rate limit exceeded. Please try again later.'\n      });\n      expect(response.headers['x-ratelimit-limit']).toBe('3');\n      expect(response.headers['x-ratelimit-remaining']).toBe('0');\n      expect(response.headers['retry-after']).toBeDefined();\n    });\n    \n    test('should reset rate limit after window', async () => {\n      // Make 3 requests (within limit)\n      for (let i = 0; i < 3; i++) {\n        const response = await request(app).get('/test');\n        expect(response.status).toBe(200);\n      }\n      \n      // Make 4th request (exceeding limit)\n      const response = await request(app).get('/test');\n      expect(response.status).toBe(429);\n      \n      // Wait for rate limit window to reset\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Make another request (should be allowed)\n      const newResponse = await request(app).get('/test');\n      expect(newResponse.status).toBe(200);\n      expect(newResponse.body).toEqual({ message: 'Success' });\n    });\n  });\n  \n  describe('Input Validator', () => {\n    let app;\n    \n    beforeEach(() => {\n      app = express();\n      app.use(express.json());\n      \n      // Add input validator middleware\n      app.use(createInputValidator());\n      \n      // Add test routes\n      app.get('/test/:id', (req, res) => {\n        res.status(200).json({ id: req.params.id });\n      });\n      \n      app.get('/query', (req, res) => {\n        res.status(200).json({ query: req.query });\n      });\n      \n      app.post('/body', (req, res) => {\n        res.status(200).json({ body: req.body });\n      });\n    });\n    \n    test('should allow valid path parameters', async () => {\n      const response = await request(app).get('/test/123');\n      expect(response.status).toBe(200);\n      expect(response.body).toEqual({ id: '123' });\n    });\n    \n    test('should block path parameters with XSS', async () => {\n      const response = await request(app).get('/test/<script>alert(1)</script>');\n      expect(response.status).toBe(400);\n      expect(response.body.error).toBe('Invalid parameter');\n    });\n    \n    test('should allow valid query parameters', async () => {\n      const response = await request(app).get('/query?name=test&age=25');\n      expect(response.status).toBe(200);\n      expect(response.body).toEqual({ query: { name: 'test', age: '25' } });\n    });\n    \n    test('should block query parameters with XSS', async () => {\n      const response = await request(app).get('/query?name=<script>alert(1)</script>');\n      expect(response.status).toBe(400);\n      expect(response.body.error).toBe('Invalid parameter');\n    });\n    \n    test('should allow valid body parameters', async () => {\n      const response = await request(app)\n        .post('/body')\n        .send({ name: 'test', age: 25 });\n      \n      expect(response.status).toBe(200);\n      expect(response.body).toEqual({ body: { name: 'test', age: 25 } });\n    });\n    \n    test('should block body parameters with XSS', async () => {\n      const response = await request(app)\n        .post('/body')\n        .send({ name: '<script>alert(1)</script>' });\n      \n      expect(response.status).toBe(400);\n      expect(response.body.error).toBe('Invalid request body');\n    });\n    \n    test('should block body parameters with command injection', async () => {\n      const response = await request(app)\n        .post('/body')\n        .send({ command: 'rm -rf /' });\n      \n      expect(response.status).toBe(400);\n      expect(response.body.error).toBe('Invalid request body');\n    });\n    \n    test('should validate nested objects', async () => {\n      const response = await request(app)\n        .post('/body')\n        .send({\n          user: {\n            name: 'test',\n            profile: {\n              bio: '<script>alert(1)</script>'\n            }\n          }\n        });\n      \n      expect(response.status).toBe(400);\n      expect(response.body.error).toBe('Invalid request body');\n    });\n  });\n  \n  describe('Security Headers', () => {\n    let app;\n    \n    beforeEach(() => {\n      app = express();\n      \n      // Add security headers middleware\n      app.use(createSecurityHeaders());\n      \n      // Add test route\n      app.get('/test', (req, res) => {\n        res.status(200).json({ message: 'Success' });\n      });\n    });\n    \n    test('should add security headers to response', async () => {\n      const response = await request(app).get('/test');\n      \n      expect(response.status).toBe(200);\n      expect(response.headers['x-content-type-options']).toBe('nosniff');\n      expect(response.headers['x-frame-options']).toBe('DENY');\n      expect(response.headers['x-xss-protection']).toBe('1; mode=block');\n      expect(response.headers['strict-transport-security']).toBe('max-age=31536000; includeSubDomains');\n      expect(response.headers['content-security-policy']).toBe(\"default-src 'self'\");\n      expect(response.headers['referrer-policy']).toBe('no-referrer');\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA,iBAAiB;EAAEC,oBAAoB;EAAEC;AAAsB,CAAC,GAAGC,OAAO,CAAC,6CAA6C,CAAC;AACjI,MAAMC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAClC,MAAME,OAAO,GAAGF,OAAO,CAAC,WAAW,CAAC;AAEpCG,QAAQ,CAAC,qBAAqB,EAAE,MAAM;EACpCA,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7B,IAAIC,GAAG;IAEPC,UAAU,CAAC,MAAM;MACfD,GAAG,GAAGH,OAAO,CAAC,CAAC;MACfG,GAAG,CAACE,GAAG,CAACL,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC;;MAEvB;MACAH,GAAG,CAACE,GAAG,CAACT,iBAAiB,CAAC;QACxBW,QAAQ,EAAE,IAAI;QAAE;QAChBC,WAAW,EAAE,CAAC,CAAC;MACjB,CAAC,CAAC,CAAC;;MAEH;MACAL,GAAG,CAACM,GAAG,CAAC,OAAO,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;QAC7BA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACN,IAAI,CAAC;UAAEO,OAAO,EAAE;QAAU,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFC,IAAI,CAAC,yCAAyC,EAAE,YAAY;MAC1D;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;QAChDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;QACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC;UAAEP,OAAO,EAAE;QAAU,CAAC,CAAC;QACrDI,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;QACvDD,MAAM,CAACK,QAAQ,CAACN,QAAQ,CAACK,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAACE,mBAAmB,CAAC,CAAC,GAAGR,CAAC,GAAG,CAAC,CAAC;MAC5F;IACF,CAAC,CAAC;IAEFD,IAAI,CAAC,4CAA4C,EAAE,YAAY;MAC7D;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;QAChDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACnC;;MAEA;MACA,MAAMF,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;MAChDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC;QAC5BI,KAAK,EAAE,mBAAmB;QAC1BX,OAAO,EAAE;MACX,CAAC,CAAC;MACFI,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;MACvDD,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAACH,IAAI,CAAC,GAAG,CAAC;MAC3DD,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,aAAa,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;IACvD,CAAC,CAAC;IAEFX,IAAI,CAAC,sCAAsC,EAAE,YAAY;MACvD;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;QAChDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACnC;;MAEA;MACA,MAAMF,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;MAChDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;;MAEjC;MACA,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,WAAW,GAAG,MAAM5B,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;MACnDQ,MAAM,CAACY,WAAW,CAACjB,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACpCD,MAAM,CAACY,WAAW,CAACV,IAAI,CAAC,CAACC,OAAO,CAAC;QAAEP,OAAO,EAAE;MAAU,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChC,IAAIC,GAAG;IAEPC,UAAU,CAAC,MAAM;MACfD,GAAG,GAAGH,OAAO,CAAC,CAAC;MACfG,GAAG,CAACE,GAAG,CAACL,OAAO,CAACM,IAAI,CAAC,CAAC,CAAC;;MAEvB;MACAH,GAAG,CAACE,GAAG,CAACR,oBAAoB,CAAC,CAAC,CAAC;;MAE/B;MACAM,GAAG,CAACM,GAAG,CAAC,WAAW,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;QACjCA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACN,IAAI,CAAC;UAAEwB,EAAE,EAAEpB,GAAG,CAACqB,MAAM,CAACD;QAAG,CAAC,CAAC;MAC7C,CAAC,CAAC;MAEF3B,GAAG,CAACM,GAAG,CAAC,QAAQ,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;QAC9BA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACN,IAAI,CAAC;UAAE0B,KAAK,EAAEtB,GAAG,CAACsB;QAAM,CAAC,CAAC;MAC5C,CAAC,CAAC;MAEF7B,GAAG,CAAC8B,IAAI,CAAC,OAAO,EAAE,CAACvB,GAAG,EAAEC,GAAG,KAAK;QAC9BA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACN,IAAI,CAAC;UAAEa,IAAI,EAAET,GAAG,CAACS;QAAK,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFL,IAAI,CAAC,oCAAoC,EAAE,YAAY;MACrD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,WAAW,CAAC;MACpDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC;QAAEU,EAAE,EAAE;MAAM,CAAC,CAAC;IAC9C,CAAC,CAAC;IAEFhB,IAAI,CAAC,uCAAuC,EAAE,YAAY;MACxD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,iCAAiC,CAAC;MAC1EQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAACK,KAAK,CAAC,CAACN,IAAI,CAAC,mBAAmB,CAAC;IACvD,CAAC,CAAC;IAEFJ,IAAI,CAAC,qCAAqC,EAAE,YAAY;MACtD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,yBAAyB,CAAC;MAClEQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC;QAAEY,KAAK,EAAE;UAAEE,IAAI,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAK;MAAE,CAAC,CAAC;IACvE,CAAC,CAAC;IAEFrB,IAAI,CAAC,wCAAwC,EAAE,YAAY;MACzD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,uCAAuC,CAAC;MAChFQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAACK,KAAK,CAAC,CAACN,IAAI,CAAC,mBAAmB,CAAC;IACvD,CAAC,CAAC;IAEFJ,IAAI,CAAC,oCAAoC,EAAE,YAAY;MACrD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAChC8B,IAAI,CAAC,OAAO,CAAC,CACbG,IAAI,CAAC;QAAEF,IAAI,EAAE,MAAM;QAAEC,GAAG,EAAE;MAAG,CAAC,CAAC;MAElClB,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAAC,CAACC,OAAO,CAAC;QAAED,IAAI,EAAE;UAAEe,IAAI,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAG;MAAE,CAAC,CAAC;IACpE,CAAC,CAAC;IAEFrB,IAAI,CAAC,uCAAuC,EAAE,YAAY;MACxD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAChC8B,IAAI,CAAC,OAAO,CAAC,CACbG,IAAI,CAAC;QAAEF,IAAI,EAAE;MAA4B,CAAC,CAAC;MAE9CjB,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAACK,KAAK,CAAC,CAACN,IAAI,CAAC,sBAAsB,CAAC;IAC1D,CAAC,CAAC;IAEFJ,IAAI,CAAC,qDAAqD,EAAE,YAAY;MACtE,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAChC8B,IAAI,CAAC,OAAO,CAAC,CACbG,IAAI,CAAC;QAAEC,OAAO,EAAE;MAAW,CAAC,CAAC;MAEhCpB,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAACK,KAAK,CAAC,CAACN,IAAI,CAAC,sBAAsB,CAAC;IAC1D,CAAC,CAAC;IAEFJ,IAAI,CAAC,gCAAgC,EAAE,YAAY;MACjD,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAChC8B,IAAI,CAAC,OAAO,CAAC,CACbG,IAAI,CAAC;QACJE,IAAI,EAAE;UACJJ,IAAI,EAAE,MAAM;UACZK,OAAO,EAAE;YACPC,GAAG,EAAE;UACP;QACF;MACF,CAAC,CAAC;MAEJvB,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACG,IAAI,CAACK,KAAK,CAAC,CAACN,IAAI,CAAC,sBAAsB,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFhB,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjC,IAAIC,GAAG;IAEPC,UAAU,CAAC,MAAM;MACfD,GAAG,GAAGH,OAAO,CAAC,CAAC;;MAEf;MACAG,GAAG,CAACE,GAAG,CAACP,qBAAqB,CAAC,CAAC,CAAC;;MAEhC;MACAK,GAAG,CAACM,GAAG,CAAC,OAAO,EAAE,CAACC,GAAG,EAAEC,GAAG,KAAK;QAC7BA,GAAG,CAACC,MAAM,CAAC,GAAG,CAAC,CAACN,IAAI,CAAC;UAAEO,OAAO,EAAE;QAAU,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFC,IAAI,CAAC,yCAAyC,EAAE,YAAY;MAC1D,MAAME,QAAQ,GAAG,MAAMf,OAAO,CAACE,GAAG,CAAC,CAACM,GAAG,CAAC,OAAO,CAAC;MAEhDQ,MAAM,CAACD,QAAQ,CAACJ,MAAM,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC;MACjCD,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAACH,IAAI,CAAC,SAAS,CAAC;MAClED,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAACH,IAAI,CAAC,MAAM,CAAC;MACxDD,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAACH,IAAI,CAAC,eAAe,CAAC;MAClED,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,2BAA2B,CAAC,CAAC,CAACH,IAAI,CAAC,qCAAqC,CAAC;MACjGD,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAACH,IAAI,CAAC,oBAAoB,CAAC;MAC9ED,MAAM,CAACD,QAAQ,CAACK,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAACH,IAAI,CAAC,aAAa,CAAC;IACjE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}