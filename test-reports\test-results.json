{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 6, "numPassedTests": 81, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 6, "numTotalTests": 81, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1744147763170, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Privacy Management API - Integration Tests", "Data Subject Request Lifecycle"], "duration": 101, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Integration Tests Data Subject Request Lifecycle should handle the complete lifecycle of a data subject request", "invocations": 1, "location": null, "numPassingAsserts": 20, "retryReasons": [], "status": "passed", "title": "should handle the complete lifecycle of a data subject request"}, {"ancestorTitles": ["Privacy Management API - Integration Tests", "Consent Management Lifecycle"], "duration": 46, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Integration Tests Consent Management Lifecycle should handle the complete lifecycle of consent management", "invocations": 1, "location": null, "numPassingAsserts": 36, "retryReasons": [], "status": "passed", "title": "should handle the complete lifecycle of consent management"}, {"ancestorTitles": ["Privacy Management API - Integration Tests", "Notification System Integration"], "duration": 33, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Integration Tests Notification System Integration should handle the complete lifecycle of notifications", "invocations": 1, "location": null, "numPassingAsserts": 27, "retryReasons": [], "status": "passed", "title": "should handle the complete lifecycle of notifications"}], "endTime": 1744147764946, "message": "", "name": "D:\\novafuse-api-superstore\\tests\\privacy\\management\\integration.test.js", "startTime": 1744147763434, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Privacy Management API", "GET /privacy/management/processing-activities"], "duration": 59, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/processing-activities should return a list of data processing activities", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a list of data processing activities"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/processing-activities/:id"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/processing-activities/:id should return a specific data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a specific data processing activity"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/processing-activities/:id"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/processing-activities/:id should return 404 for non-existent data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent data processing activity"}, {"ancestorTitles": ["Privacy Management API", "POST /privacy/management/processing-activities"], "duration": 26, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API POST /privacy/management/processing-activities should create a new data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should create a new data processing activity"}, {"ancestorTitles": ["Privacy Management API", "PUT /privacy/management/processing-activities/:id"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API PUT /privacy/management/processing-activities/:id should update an existing data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should update an existing data processing activity"}, {"ancestorTitles": ["Privacy Management API", "PUT /privacy/management/processing-activities/:id"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API PUT /privacy/management/processing-activities/:id should return 404 for non-existent data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent data processing activity"}, {"ancestorTitles": ["Privacy Management API", "DELETE /privacy/management/processing-activities/:id"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API DELETE /privacy/management/processing-activities/:id should delete a data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should delete a data processing activity"}, {"ancestorTitles": ["Privacy Management API", "DELETE /privacy/management/processing-activities/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API DELETE /privacy/management/processing-activities/:id should return 404 for non-existent data processing activity", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent data processing activity"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/subject-requests"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/subject-requests should return a list of data subject requests", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a list of data subject requests"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/subject-requests/:id"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/subject-requests/:id should return a specific data subject request", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a specific data subject request"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/subject-requests/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/subject-requests/:id should return 404 for non-existent data subject request", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent data subject request"}, {"ancestorTitles": ["Privacy Management API", "POST /privacy/management/subject-requests"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API POST /privacy/management/subject-requests should create a new data subject request", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should create a new data subject request"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/consent-records"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/consent-records should return a list of consent records", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a list of consent records"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/consent-records/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/consent-records/:id should return a specific consent record", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a specific consent record"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/consent-records/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/consent-records/:id should return 404 for non-existent consent record", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent consent record"}, {"ancestorTitles": ["Privacy Management API", "POST /privacy/management/consent-records"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API POST /privacy/management/consent-records should create a new consent record", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should create a new consent record"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/privacy-notices"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/privacy-notices should return a list of privacy notices", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a list of privacy notices"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/privacy-notices/:id"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/privacy-notices/:id should return a specific privacy notice", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a specific privacy notice"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/privacy-notices/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/privacy-notices/:id should return 404 for non-existent privacy notice", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent privacy notice"}, {"ancestorTitles": ["Privacy Management API", "POST /privacy/management/privacy-notices"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API POST /privacy/management/privacy-notices should create a new privacy notice", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should create a new privacy notice"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/data-breaches"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/data-breaches should return a list of data breaches", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a list of data breaches"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/data-breaches/:id"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/data-breaches/:id should return a specific data breach", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a specific data breach"}, {"ancestorTitles": ["Privacy Management API", "GET /privacy/management/data-breaches/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API GET /privacy/management/data-breaches/:id should return 404 for non-existent data breach", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent data breach"}, {"ancestorTitles": ["Privacy Management API", "POST /privacy/management/data-breaches"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API POST /privacy/management/data-breaches should create a new data breach", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should create a new data breach"}], "endTime": 1744147764958, "message": "", "name": "D:\\novafuse-api-superstore\\tests\\privacy\\management\\api.test.js", "startTime": 1744147763432, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/data-systems"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/data-systems should return a list of data systems", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should return a list of data systems"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/data-systems"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/data-systems should filter data systems by request type", "invocations": 1, "location": null, "numPassingAsserts": 8, "retryReasons": [], "status": "passed", "title": "should filter data systems by request type"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/data-systems/:id"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/data-systems/:id should return a specific data system", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should return a specific data system"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/data-systems/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/data-systems/:id should return 404 for non-existent data system", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent data system"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "POST /privacy/management/subject-requests/:id/process"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation POST /privacy/management/subject-requests/:id/process should process a data subject request", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should process a data subject request"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "POST /privacy/management/subject-requests/:id/process"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation POST /privacy/management/subject-requests/:id/process should return 400 for non-existent request", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for non-existent request"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/subject-requests/:id/export"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/subject-requests/:id/export should generate a data export", "invocations": 1, "location": null, "numPassingAsserts": 9, "retryReasons": [], "status": "passed", "title": "should generate a data export"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/subject-requests/:id/export"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/subject-requests/:id/export should return 400 for non-access request", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return 400 for non-access request"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/subject-requests/:id/affected-systems"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/subject-requests/:id/affected-systems should determine affected systems for a request", "invocations": 1, "location": null, "numPassingAsserts": 10, "retryReasons": [], "status": "passed", "title": "should determine affected systems for a request"}, {"ancestorTitles": ["Privacy Management API - DSR Automation", "GET /privacy/management/subject-requests/:id/affected-systems"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - DSR Automation GET /privacy/management/subject-requests/:id/affected-systems should return 404 for non-existent request", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent request"}], "endTime": 1744147765319, "message": "", "name": "D:\\novafuse-api-superstore\\tests\\privacy\\management\\dsr-automation.test.js", "startTime": 1744147764968, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications should return a list of notifications", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return a list of notifications"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications should filter notifications by recipient", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should filter notifications by recipient"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications should filter notifications by status", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should filter notifications by status"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications should filter notifications by type", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should filter notifications by type"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications should filter notifications by priority", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should filter notifications by priority"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications should filter notifications by date range", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should filter notifications by date range"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications/:id"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications/:id should return a specific notification", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should return a specific notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "GET /privacy/management/notifications/:id"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications GET /privacy/management/notifications/:id should return 404 for non-existent notification", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications should create a new notification", "invocations": 1, "location": null, "numPassingAsserts": 14, "retryReasons": [], "status": "passed", "title": "should create a new notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications should return 400 if required fields are missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 if required fields are missing"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/:id/read"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/:id/read should mark a notification as read", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should mark a notification as read"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/:id/read"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/:id/read should return 404 for non-existent notification", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/:id/send"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/:id/send should send a notification", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should send a notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/:id/send"], "duration": 15, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/:id/send should return 400 for already sent notification", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for already sent notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/:id/send"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/:id/send should return 400 for non-existent notification", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for non-existent notification"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/generate"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/generate should generate notifications", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should generate notifications"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/generate"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/generate should generate DSR notifications", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should generate DSR notifications"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/generate"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/generate should generate data breach notifications", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should generate data breach notifications"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/generate"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/generate should generate DPIA notifications", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should generate DPIA notifications"}, {"ancestorTitles": ["Privacy Management API - Notifications", "POST /privacy/management/notifications/send-all"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Notifications POST /privacy/management/notifications/send-all should send all pending notifications", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should send all pending notifications"}], "endTime": 1744147765456, "message": "", "name": "D:\\novafuse-api-superstore\\tests\\privacy\\management\\notifications.test.js", "startTime": 1744147765026, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 43, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should generate a DSR summary report", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should generate a DSR summary report"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should generate a consent management report", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should generate a consent management report"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should generate a data breach report", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should generate a data breach report"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should generate a processing activities report", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should generate a processing activities report"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should generate a compliance status report", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should generate a compliance status report"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should support custom date ranges", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should support custom date ranges"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 17, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should support grouping by different attributes", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should support grouping by different attributes"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should return 400 for invalid report type", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for invalid report type"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should return 400 for invalid period", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for invalid period"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/reports/:reportType"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/reports/:reportType should return 400 for custom period without dates", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for custom period without dates"}, {"ancestorTitles": ["Privacy Management API - Analytics and Reporting", "GET /privacy/management/dashboard/metrics"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Analytics and Reporting GET /privacy/management/dashboard/metrics should return dashboard metrics", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should return dashboard metrics"}], "endTime": 1744147765733, "message": "", "name": "D:\\novafuse-api-superstore\\tests\\privacy\\management\\analytics.test.js", "startTime": 1744147765351, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["Privacy Management API - Consent Management", "GET /privacy/management/consent/forms"], "duration": 33, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management GET /privacy/management/consent/forms should generate a consent form", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "should generate a consent form"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "GET /privacy/management/consent/forms"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management GET /privacy/management/consent/forms should return 400 if consent type is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 if consent type is missing"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "GET /privacy/management/consent/:id/validity"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management GET /privacy/management/consent/:id/validity should verify consent validity", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should verify consent validity"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "GET /privacy/management/consent/:id/validity"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management GET /privacy/management/consent/:id/validity should return invalid for non-existent consent", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should return invalid for non-existent consent"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/:id/withdraw"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/:id/withdraw should withdraw consent", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "should withdraw consent"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/:id/withdraw"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/:id/withdraw should return 400 if withdrawal method is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 if withdrawal method is missing"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/:id/withdraw"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/:id/withdraw should return 404 for non-existent consent", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 404 for non-existent consent"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "GET /privacy/management/consent/data-subjects/:dataSubjectId"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management GET /privacy/management/consent/data-subjects/:dataSubjectId should get consent records by data subject", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should get consent records by data subject"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "GET /privacy/management/consent/emails/:email"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management GET /privacy/management/consent/emails/:email should get consent records by email", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should get consent records by email"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/proof/verify"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/proof/verify should verify consent proof", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should verify consent proof"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/proof/verify"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/proof/verify should return 400 if consent proof is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 if consent proof is missing"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/proof/generate"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/proof/generate should generate consent proof", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should generate consent proof"}, {"ancestorTitles": ["Privacy Management API - Consent Management", "POST /privacy/management/consent/proof/generate"], "duration": 4, "failureDetails": [], "failureMessages": [], "fullName": "Privacy Management API - Consent Management POST /privacy/management/consent/proof/generate should return 400 if required data is missing", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 if required data is missing"}], "endTime": 1744147765866, "message": "", "name": "D:\\novafuse-api-superstore\\tests\\privacy\\management\\consent.test.js", "startTime": 1744147765502, "status": "passed", "summary": ""}], "wasInterrupted": false}