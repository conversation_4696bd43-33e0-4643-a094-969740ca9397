/**
 * Data Breach Routes
 * 
 * Routes for managing data breach incidents.
 */

const express = require('express');
const { check } = require('express-validator');
const DataBreachController = require('../controllers/DataBreachController');
const auth = require('../../../middleware/auth');

const router = express.Router();

/**
 * @route   GET /api/privacy/management/breach
 * @desc    Get all data breaches
 * @access  Private
 */
router.get('/', auth, DataBreachController.getAllDataBreaches);

/**
 * @route   GET /api/privacy/management/breach/:id
 * @desc    Get a single data breach
 * @access  Private
 */
router.get('/:id', auth, DataBreachController.getDataBreach);

/**
 * @route   POST /api/privacy/management/breach
 * @desc    Create a new data breach
 * @access  Private
 */
router.post('/', [
  auth,
  [
    check('discoveryDate', 'Discovery date is required').not().isEmpty(),
    check('discoveryMethod', 'Discovery method is required').not().isEmpty(),
    check('breachDetails.description', 'Breach description is required').not().isEmpty(),
    check('breachDetails.type', 'Breach type is required').not().isEmpty(),
    check('breachDetails.cause', 'Breach cause is required').not().isEmpty(),
    check('affectedData.dataCategories', 'Data categories must be an array').isArray(),
    check('affectedData.dataSubjectCategories', 'Data subject categories must be an array').isArray()
  ]
], DataBreachController.createDataBreach);

/**
 * @route   PUT /api/privacy/management/breach/:id
 * @desc    Update a data breach
 * @access  Private
 */
router.put('/:id', [
  auth,
  [
    check('status', 'Invalid status').optional().isIn([
      'Draft', 'Under Investigation', 'Contained', 'Remediated', 'Closed', 'Reopened'
    ]),
    check('discoveryDate', 'Discovery date is required').optional().not().isEmpty(),
    check('breachDetails.description', 'Breach description is required').optional().not().isEmpty(),
    check('breachDetails.type', 'Breach type is required').optional().not().isEmpty(),
    check('breachDetails.cause', 'Breach cause is required').optional().not().isEmpty()
  ]
], DataBreachController.updateDataBreach);

/**
 * @route   DELETE /api/privacy/management/breach/:id
 * @desc    Delete a data breach
 * @access  Private
 */
router.delete('/:id', auth, DataBreachController.deleteDataBreach);

/**
 * @route   POST /api/privacy/management/breach/:id/risk-assessment
 * @desc    Add or update risk assessment for a data breach
 * @access  Private
 */
router.post('/:id/risk-assessment', [
  auth,
  [
    check('performedBy.name', 'Assessor name is required').not().isEmpty(),
    check('performedBy.role', 'Assessor role is required').not().isEmpty(),
    check('confidentialityImpact', 'Confidentiality impact is required').not().isEmpty(),
    check('integrityImpact', 'Integrity impact is required').not().isEmpty(),
    check('availabilityImpact', 'Availability impact is required').not().isEmpty(),
    check('overallRisk', 'Overall risk is required').not().isEmpty()
  ]
], DataBreachController.updateRiskAssessment);

/**
 * @route   POST /api/privacy/management/breach/:id/notifications/authority
 * @desc    Add or update supervisory authority notification
 * @access  Private
 */
router.post('/:id/notifications/authority', [
  auth,
  [
    check('required', 'Required flag is required').isBoolean(),
    check('authority', 'Authority name is required').not().isEmpty(),
    check('notificationDate', 'Notification date is required').not().isEmpty(),
    check('notificationMethod', 'Notification method is required').not().isEmpty()
  ]
], DataBreachController.updateAuthorityNotification);

/**
 * @route   POST /api/privacy/management/breach/:id/notifications/data-subjects
 * @desc    Add or update data subject notification
 * @access  Private
 */
router.post('/:id/notifications/data-subjects', [
  auth,
  [
    check('required', 'Required flag is required').isBoolean(),
    check('notificationDate', 'Notification date is required').not().isEmpty(),
    check('notificationMethod', 'Notification method is required').not().isEmpty(),
    check('numberOfNotified', 'Number of notified subjects is required').isNumeric()
  ]
], DataBreachController.updateDataSubjectNotification);

/**
 * @route   POST /api/privacy/management/breach/:id/remediation/actions
 * @desc    Add a remediation action to a data breach
 * @access  Private
 */
router.post('/:id/remediation/actions', [
  auth,
  [
    check('description', 'Description is required').not().isEmpty(),
    check('type', 'Type is required').not().isEmpty(),
    check('assignedTo', 'Assignee is required').not().isEmpty(),
    check('dueDate', 'Due date is required').not().isEmpty()
  ]
], DataBreachController.addRemediationAction);

/**
 * @route   PUT /api/privacy/management/breach/:id/remediation/actions/:actionId
 * @desc    Update a remediation action
 * @access  Private
 */
router.put('/:id/remediation/actions/:actionId', [
  auth,
  [
    check('description', 'Description is required').optional().not().isEmpty(),
    check('status', 'Invalid status').optional().isIn([
      'Planned', 'In Progress', 'Completed', 'Deferred', 'Cancelled'
    ])
  ]
], DataBreachController.updateRemediationAction);

/**
 * @route   DELETE /api/privacy/management/breach/:id/remediation/actions/:actionId
 * @desc    Delete a remediation action
 * @access  Private
 */
router.delete('/:id/remediation/actions/:actionId', auth, DataBreachController.deleteRemediationAction);

/**
 * @route   POST /api/privacy/management/breach/:id/documents
 * @desc    Add a document to a data breach
 * @access  Private
 */
router.post('/:id/documents', [
  auth,
  [
    check('name', 'Name is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty(),
    check('url', 'URL is required').not().isEmpty()
  ]
], DataBreachController.addDocument);

/**
 * @route   DELETE /api/privacy/management/breach/:id/documents/:documentId
 * @desc    Delete a document from a data breach
 * @access  Private
 */
router.delete('/:id/documents/:documentId', auth, DataBreachController.deleteDocument);

/**
 * @route   GET /api/privacy/management/breach/:id/notification-deadline
 * @desc    Calculate notification deadline for a data breach
 * @access  Private
 */
router.get('/:id/notification-deadline', auth, DataBreachController.calculateNotificationDeadline);

/**
 * @route   GET /api/privacy/management/breach/:id/notification-required
 * @desc    Check if authority notification is required for a data breach
 * @access  Private
 */
router.get('/:id/notification-required', auth, DataBreachController.checkNotificationRequired);

/**
 * @route   POST /api/privacy/management/breach/:id/history
 * @desc    Add a history entry to a data breach
 * @access  Private
 */
router.post('/:id/history', [
  auth,
  [
    check('action', 'Action is required').not().isEmpty(),
    check('details', 'Details are required').not().isEmpty()
  ]
], DataBreachController.addHistoryEntry);

module.exports = router;
