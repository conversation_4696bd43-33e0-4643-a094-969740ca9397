/**
 * Simple Schema Routes
 * 
 * This file defines the routes for simple schema management.
 */

const express = require('express');
const router = express.Router();
const schemaController = require('./simpleSchemaController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/simple-schemas:
 *   get:
 *     summary: List all schemas
 *     description: Get a list of all available schemas
 *     tags: [Simple Schemas]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of schemas
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, schemaController.listSchemas);

/**
 * @swagger
 * /api/v1/simple-schemas/{entity}:
 *   get:
 *     summary: Get schema
 *     description: Get schema for entity
 *     tags: [Simple Schemas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entity
 *         required: true
 *         schema:
 *           type: string
 *         description: Entity name
 *     responses:
 *       200:
 *         description: Schema
 *       404:
 *         description: Schema not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:entity', authMiddleware.authenticate, schemaController.getSchema);

/**
 * @swagger
 * /api/v1/simple-schemas:
 *   post:
 *     summary: Save schema
 *     description: Save schema for entity
 *     tags: [Simple Schemas]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - entity
 *               - fields
 *             properties:
 *               entity:
 *                 type: string
 *                 description: Entity name
 *               entityPlural:
 *                 type: string
 *                 description: Plural entity name
 *               apiEndpoint:
 *                 type: string
 *                 description: API endpoint
 *               fields:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                     - type
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: Field name
 *                     type:
 *                       type: string
 *                       description: Field type
 *                     required:
 *                       type: boolean
 *                       description: Whether field is required
 *                     options:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: Options for dropdown fields
 *                     label:
 *                       type: string
 *                       description: Display label
 *                     placeholder:
 *                       type: string
 *                       description: Placeholder text
 *                     description:
 *                       type: string
 *                       description: Field description
 *                     readOnly:
 *                       type: boolean
 *                       description: Whether field is read-only
 *                     hidden:
 *                       type: boolean
 *                       description: Whether field is hidden
 *     responses:
 *       200:
 *         description: Schema saved
 *       400:
 *         description: Invalid schema
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', 
  authMiddleware.authenticate, 
  authMiddleware.hasRole(['admin']), 
  schemaController.saveSchema
);

/**
 * @swagger
 * /api/v1/simple-schemas/{entity}:
 *   delete:
 *     summary: Delete schema
 *     description: Delete schema for entity
 *     tags: [Simple Schemas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: entity
 *         required: true
 *         schema:
 *           type: string
 *         description: Entity name
 *     responses:
 *       200:
 *         description: Schema deleted
 *       404:
 *         description: Schema not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete('/:entity', 
  authMiddleware.authenticate, 
  authMiddleware.hasRole(['admin']), 
  schemaController.deleteSchema
);

module.exports = router;
