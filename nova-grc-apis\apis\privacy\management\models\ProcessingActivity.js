/**
 * Processing Activity Model
 * 
 * Represents a data processing activity within an organization.
 * This model captures details about how personal data is processed,
 * including the purpose, data categories, legal basis, and data flows.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const processingActivitySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  purpose: {
    type: String,
    required: true
  },
  dataController: {
    name: String,
    contact: String,
    address: String,
    dpo: {
      name: String,
      email: String,
      phone: String
    }
  },
  dataProcessor: {
    name: String,
    contact: String,
    address: String,
    dpa: {
      exists: Boolean,
      reference: String,
      expiryDate: Date
    }
  },
  dataCategories: [{
    type: String,
    enum: [
      'Personal', 
      'Special', 
      'Criminal', 
      'Children', 
      'Financial', 
      'Location', 
      'Biometric', 
      'Health', 
      'Genetic'
    ]
  }],
  dataSubjects: [{
    type: String,
    enum: [
      'Customers', 
      'Employees', 
      'Vendors', 
      'Partners', 
      'Website Visitors', 
      'App Users', 
      'Children', 
      'Patients', 
      'Other'
    ]
  }],
  legalBasis: {
    type: String,
    required: true,
    enum: [
      'Consent', 
      'Contract', 
      'Legal Obligation', 
      'Vital Interests', 
      'Public Task', 
      'Legitimate Interests'
    ]
  },
  legalBasisDetails: {
    type: String
  },
  dataRetention: {
    period: Number,
    unit: {
      type: String,
      enum: ['Days', 'Months', 'Years']
    },
    justification: String
  },
  crossBorderTransfers: {
    exists: {
      type: Boolean,
      default: false
    },
    countries: [String],
    safeguards: {
      type: String,
      enum: [
        'None', 
        'Standard Contractual Clauses', 
        'Binding Corporate Rules', 
        'Adequacy Decision', 
        'Explicit Consent', 
        'Necessary for Contract', 
        'Public Interest'
      ]
    }
  },
  securityMeasures: [{
    type: String,
    enum: [
      'Encryption', 
      'Pseudonymization', 
      'Access Controls', 
      'Backup', 
      'Audit Logs', 
      'Data Minimization', 
      'Staff Training', 
      'Incident Response Plan'
    ]
  }],
  dataFlow: {
    collection: {
      methods: [String],
      directFromSubject: Boolean
    },
    storage: {
      locations: [String],
      systems: [String]
    },
    access: {
      internalDepartments: [String],
      externalRecipients: [String]
    }
  },
  risks: [{
    description: String,
    likelihood: {
      type: String,
      enum: ['Low', 'Medium', 'High']
    },
    impact: {
      type: String,
      enum: ['Low', 'Medium', 'High']
    },
    mitigations: [String]
  }],
  dpia: {
    required: Boolean,
    completed: Boolean,
    reference: String,
    date: Date
  },
  status: {
    type: String,
    enum: ['Active', 'Planned', 'Retired', 'Under Review'],
    default: 'Active'
  },
  lastReviewDate: Date,
  nextReviewDate: Date,
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add text index for search functionality
processingActivitySchema.index({
  name: 'text',
  description: 'text',
  purpose: 'text'
});

// Add method to check if DPIA is required
processingActivitySchema.methods.isDpiaRequired = function() {
  // DPIA is required if processing involves special categories of data
  // or if it involves systematic monitoring or automated decision making
  const specialCategories = ['Special', 'Criminal', 'Children', 'Biometric', 'Health', 'Genetic'];
  const hasSpecialCategories = this.dataCategories.some(category => specialCategories.includes(category));
  
  return hasSpecialCategories || 
         this.risks.some(risk => risk.impact === 'High') ||
         this.dpia.required;
};

const ProcessingActivity = mongoose.model('ProcessingActivity', processingActivitySchema);

module.exports = ProcessingActivity;
