{"baseUrl": "http://localhost:3000", "viewportWidth": 1280, "viewportHeight": 720, "video": false, "screenshotOnRunFailure": true, "defaultCommandTimeout": 10000, "requestTimeout": 10000, "responseTimeout": 30000, "pageLoadTimeout": 60000, "env": {"registryApiUrl": "http://localhost:3001", "authServiceUrl": "http://localhost:3002", "connectorExecutorUrl": "http://localhost:3003", "usageMeteringUrl": "http://localhost:3004", "mockApisUrl": "http://localhost:3005"}}