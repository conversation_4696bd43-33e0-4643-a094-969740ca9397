/**
 * Environment Connector Service
 * 
 * This service handles environment-specific connector management.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError } = require('../utils/errors');
const EnvironmentService = require('./EnvironmentService');

class EnvironmentConnectorService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.environmentService = new EnvironmentService(dataDir);
  }

  /**
   * Get connectors file path for a specific environment
   */
  getConnectorsFilePath(environmentId) {
    const envDataDir = this.environmentService.getEnvironmentDataDir(environmentId);
    return path.join(envDataDir, 'connectors.json');
  }

  /**
   * Load connectors from file
   */
  async loadConnectors(environmentId) {
    try {
      // Ensure environment exists
      await this.environmentService.getEnvironmentById(environmentId);
      
      // Ensure environment data directory exists
      await this.environmentService.ensureEnvironmentDataDir(environmentId);
      
      const connectorsFile = this.getConnectorsFilePath(environmentId);
      
      try {
        const data = await fs.readFile(connectorsFile, 'utf8');
        return JSON.parse(data);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, return empty array
          return [];
        }
        throw error;
      }
    } catch (error) {
      console.error(`Error loading connectors for environment ${environmentId}:`, error);
      throw error;
    }
  }

  /**
   * Save connectors to file
   */
  async saveConnectors(environmentId, connectors) {
    try {
      // Ensure environment exists
      await this.environmentService.getEnvironmentById(environmentId);
      
      // Ensure environment data directory exists
      await this.environmentService.ensureEnvironmentDataDir(environmentId);
      
      const connectorsFile = this.getConnectorsFilePath(environmentId);
      
      await fs.writeFile(connectorsFile, JSON.stringify(connectors, null, 2));
      
      return connectors;
    } catch (error) {
      console.error(`Error saving connectors for environment ${environmentId}:`, error);
      throw error;
    }
  }

  /**
   * Get all connectors for an environment
   */
  async getAllConnectors(environmentId) {
    return this.loadConnectors(environmentId);
  }

  /**
   * Get connector by ID for an environment
   */
  async getConnectorById(environmentId, connectorId) {
    const connectors = await this.loadConnectors(environmentId);
    const connector = connectors.find(c => c.id === connectorId);
    
    if (!connector) {
      throw new NotFoundError(`Connector with ID ${connectorId} not found in environment ${environmentId}`);
    }
    
    return connector;
  }

  /**
   * Create a new connector for an environment
   */
  async createConnector(environmentId, connectorData) {
    if (!connectorData.name) {
      throw new ValidationError('Connector name is required');
    }
    
    const connectors = await this.loadConnectors(environmentId);
    
    // Create new connector
    const newConnector = {
      id: uuidv4(),
      ...connectorData,
      environmentId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    connectors.push(newConnector);
    await this.saveConnectors(environmentId, connectors);
    
    return newConnector;
  }

  /**
   * Update a connector for an environment
   */
  async updateConnector(environmentId, connectorId, connectorData) {
    const connectors = await this.loadConnectors(environmentId);
    const index = connectors.findIndex(c => c.id === connectorId);
    
    if (index === -1) {
      throw new NotFoundError(`Connector with ID ${connectorId} not found in environment ${environmentId}`);
    }
    
    // Update connector
    const updatedConnector = {
      ...connectors[index],
      ...connectorData,
      id: connectorId, // Don't allow changing the ID
      environmentId, // Don't allow changing the environment
      updated: new Date().toISOString()
    };
    
    connectors[index] = updatedConnector;
    await this.saveConnectors(environmentId, connectors);
    
    return updatedConnector;
  }

  /**
   * Delete a connector for an environment
   */
  async deleteConnector(environmentId, connectorId) {
    const connectors = await this.loadConnectors(environmentId);
    const index = connectors.findIndex(c => c.id === connectorId);
    
    if (index === -1) {
      throw new NotFoundError(`Connector with ID ${connectorId} not found in environment ${environmentId}`);
    }
    
    // Remove the connector
    connectors.splice(index, 1);
    await this.saveConnectors(environmentId, connectors);
    
    return { success: true, message: `Connector ${connectorId} deleted from environment ${environmentId}` };
  }

  /**
   * Clone a connector between environments
   */
  async cloneConnector(sourceEnvironmentId, targetEnvironmentId, connectorId) {
    // Get connector from source environment
    const connector = await this.getConnectorById(sourceEnvironmentId, connectorId);
    
    // Create a new connector in the target environment
    const { id, created, updated, environmentId, ...connectorData } = connector;
    
    // Create the cloned connector
    const clonedConnector = await this.createConnector(targetEnvironmentId, {
      ...connectorData,
      name: `${connectorData.name} (Cloned)`,
      sourceConnectorId: connectorId,
      sourceEnvironmentId
    });
    
    return clonedConnector;
  }

  /**
   * Promote all connectors from one environment to another
   */
  async promoteConnectors(sourceEnvironmentId, targetEnvironmentId) {
    // Get all connectors from source environment
    const sourceConnectors = await this.loadConnectors(sourceEnvironmentId);
    
    // Get all connectors from target environment
    const targetConnectors = await this.loadConnectors(targetEnvironmentId);
    
    // Create a map of target connectors by name for quick lookup
    const targetConnectorMap = {};
    targetConnectors.forEach(connector => {
      targetConnectorMap[connector.name] = connector;
    });
    
    // Track promotion results
    const results = {
      created: [],
      updated: [],
      unchanged: [],
      errors: []
    };
    
    // Process each source connector
    for (const sourceConnector of sourceConnectors) {
      try {
        const targetConnector = targetConnectorMap[sourceConnector.name];
        
        if (targetConnector) {
          // Connector exists in target environment, update it
          const { id, created, updated, environmentId, ...connectorData } = sourceConnector;
          
          // Check if the connector has changed
          const hasChanged = JSON.stringify(connectorData) !== JSON.stringify({
            ...targetConnector,
            id: undefined,
            created: undefined,
            updated: undefined,
            environmentId: undefined
          });
          
          if (hasChanged) {
            // Update the target connector
            const updatedConnector = await this.updateConnector(
              targetEnvironmentId,
              targetConnector.id,
              connectorData
            );
            
            results.updated.push(updatedConnector);
          } else {
            results.unchanged.push(targetConnector);
          }
        } else {
          // Connector doesn't exist in target environment, create it
          const { id, created, updated, environmentId, ...connectorData } = sourceConnector;
          
          const newConnector = await this.createConnector(
            targetEnvironmentId,
            connectorData
          );
          
          results.created.push(newConnector);
        }
      } catch (error) {
        console.error(`Error promoting connector ${sourceConnector.id}:`, error);
        results.errors.push({
          connector: sourceConnector,
          error: error.message
        });
      }
    }
    
    return results;
  }
}

module.exports = EnvironmentConnectorService;
