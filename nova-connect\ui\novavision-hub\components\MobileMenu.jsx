/**
 * MobileMenu Component
 * 
 * A mobile-friendly menu component for navigation.
 */

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';

/**
 * MobileMenu component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.items - Menu items
 * @param {string} props.items[].id - Item ID
 * @param {string} props.items[].label - Item label
 * @param {string} [props.items[].icon] - Item icon
 * @param {Function} [props.items[].onClick] - Function to call when item is clicked
 * @param {Array} [props.items[].children] - Submenu items
 * @param {string} [props.title='Menu'] - Menu title
 * @param {string} [props.activeItemId] - Active item ID
 * @param {Function} [props.onItemClick] - Function to call when an item is clicked
 * @param {boolean} [props.collapsible=true] - Whether the menu is collapsible
 * @param {boolean} [props.defaultCollapsed=true] - Whether the menu is collapsed by default on mobile
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} MobileMenu component
 */
const MobileMenu = ({
  items,
  title = 'Menu',
  activeItemId,
  onItemClick,
  collapsible = true,
  defaultCollapsed = true,
  className = '',
  style = {}
}) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);
  const [expandedItems, setExpandedItems] = useState({});
  const [isMobile, setIsMobile] = useState(false);
  const menuRef = useRef(null);
  
  // Check if device is mobile
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setCollapsed(true);
      }
    };
    
    // Add event listener
    if (isMobile && !collapsed) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    // Cleanup
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobile, collapsed]);
  
  // Toggle menu collapse
  const toggleCollapse = () => {
    if (collapsible) {
      setCollapsed(!collapsed);
    }
  };
  
  // Toggle item expansion
  const toggleItemExpansion = (itemId) => {
    setExpandedItems(prevState => ({
      ...prevState,
      [itemId]: !prevState[itemId]
    }));
  };
  
  // Handle item click
  const handleItemClick = (item) => {
    // Call item's onClick handler if provided
    if (item.onClick) {
      item.onClick(item);
    }
    
    // Call onItemClick prop if provided
    if (onItemClick) {
      onItemClick(item);
    }
    
    // Collapse menu on mobile after item click
    if (isMobile) {
      setCollapsed(true);
    }
  };
  
  // Render menu item
  const renderMenuItem = (item, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = item.id === activeItemId;
    const isExpanded = expandedItems[item.id];
    
    return (
      <li key={item.id} className="relative">
        <div
          className={`
            flex items-center justify-between px-4 py-2 text-sm rounded-md cursor-pointer transition-colors duration-200
            ${isActive ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}
            ${level > 0 ? 'ml-4' : ''}
          `}
          onClick={() => hasChildren ? toggleItemExpansion(item.id) : handleItemClick(item)}
          data-testid={`menu-item-${item.id}`}
        >
          <div className="flex items-center">
            {item.icon && <span className="mr-2">{item.icon}</span>}
            <span>{item.label}</span>
          </div>
          {hasChildren && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>
        
        {hasChildren && isExpanded && (
          <ul className="mt-1 space-y-1">
            {item.children.map(child => renderMenuItem(child, level + 1))}
          </ul>
        )}
      </li>
    );
  };
  
  return (
    <div
      ref={menuRef}
      className={`relative ${className}`}
      style={style}
      data-testid="mobile-menu"
    >
      {/* Mobile menu button */}
      {isMobile && (
        <button
          className="flex items-center justify-center w-10 h-10 rounded-md bg-white border border-gray-200 shadow-sm"
          onClick={toggleCollapse}
          aria-label={collapsed ? 'Open menu' : 'Close menu'}
          data-testid="mobile-menu-button"
        >
          {collapsed ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          )}
        </button>
      )}
      
      {/* Menu content */}
      <div
        className={`
          ${isMobile ? 'absolute top-12 left-0 z-50 w-64 bg-white rounded-md border border-gray-200 shadow-lg' : 'w-full'}
          ${isMobile && collapsed ? 'hidden' : 'block'}
          transition-all duration-200
        `}
        data-testid="menu-content"
      >
        {/* Menu header */}
        <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 rounded-t-md">
          <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
        </div>
        
        {/* Menu items */}
        <ul className="p-2 space-y-1">
          {items.map(item => renderMenuItem(item))}
        </ul>
      </div>
    </div>
  );
};

MobileMenu.propTypes = {
  items: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      icon: PropTypes.node,
      onClick: PropTypes.func,
      children: PropTypes.array
    })
  ).isRequired,
  title: PropTypes.string,
  activeItemId: PropTypes.string,
  onItemClick: PropTypes.func,
  collapsible: PropTypes.bool,
  defaultCollapsed: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default MobileMenu;
