import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const PatentShield = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">PATENT SHIELD & STRATEGIC MOAT</ContainerLabel>
      </ContainerBox>

      {/* Central Shield */}
      <svg width="800" height="600" style={{ position: 'absolute', top: 0, left: 0, zIndex: 0 }}>
        <path
          d="M 400,80 L 600,130 L 600,300 C 600,400 500,480 400,500 C 300,480 200,400 200,300 L 200,130 Z"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
      </svg>

      {/* Core Patent Components */}
      <ComponentBox left="325px" top="100px" width="150px" height="60px">
        <ComponentNumber>601</ComponentNumber>
        <ComponentLabel fontSize="16px">God Patent</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Cyber-Safety Framework</span>
      </ComponentBox>

      {/* Key Innovations */}
      <ComponentBox left="250px" top="180px" width="300px" height="40px">
        <ComponentNumber>602</ComponentNumber>
        <ComponentLabel fontSize="16px" color="#555555">48 Foundational Patents</ComponentLabel>
      </ComponentBox>

      {/* Patent Categories */}
      <ComponentBox left="150px" top="240px" width="150px" height="60px">
        <ComponentNumber>603</ComponentNumber>
        <ComponentLabel fontSize="14px">Universal Architecture</ComponentLabel>
        <span style={{ fontSize: '12px' }}>12 Pillars</span>
      </ComponentBox>

      <ComponentBox left="325px" top="240px" width="150px" height="60px">
        <ComponentNumber>604</ComponentNumber>
        <ComponentLabel fontSize="14px">AI/ML Compliance</ComponentLabel>
        <span style={{ fontSize: '12px' }}>12 Novas</span>
      </ComponentBox>

      <ComponentBox left="500px" top="240px" width="150px" height="60px">
        <ComponentNumber>605</ComponentNumber>
        <ComponentLabel fontSize="14px">Verifiable Identity</ComponentLabel>
        <span style={{ fontSize: '12px' }}>NovaDNA</span>
      </ComponentBox>

      {/* Key Innovations */}
      <ComponentBox left="150px" top="320px" width="150px" height="60px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>606</ComponentNumber>
        <ComponentLabel fontSize="12px">Self-Destructing Servers</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Pillar 3</span>
      </ComponentBox>

      <ComponentBox left="325px" top="320px" width="150px" height="60px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>607</ComponentNumber>
        <ComponentLabel fontSize="12px">GDPR-by-Default Compiler</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Pillar 4</span>
      </ComponentBox>

      <ComponentBox left="500px" top="320px" width="150px" height="60px" style={{ border: '1px dashed #333' }}>
        <ComponentNumber>608</ComponentNumber>
        <ComponentLabel fontSize="12px">Post-Quantum Journal</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Pillar 9</span>
      </ComponentBox>

      {/* Strategic Moat */}
      <ContainerBox width="700px" height="120px" left="50px" top="400px">
        <ContainerLabel fontSize="16px">STRATEGIC MOAT</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="440px" width="180px" height="60px">
        <ComponentNumber>609</ComponentNumber>
        <ComponentLabel fontSize="14px">Zero Legal Risk</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Full IP Protection</span>
      </ComponentBox>

      <ComponentBox left="310px" top="440px" width="180px" height="60px">
        <ComponentNumber>610</ComponentNumber>
        <ComponentLabel fontSize="14px">First-Mover Rights</ComponentLabel>
        <span style={{ fontSize: '12px', color: '#555555' }}>$1.2T Compliance-Cloud Market</span>
      </ComponentBox>

      <ComponentBox left="520px" top="440px" width="180px" height="60px">
        <ComponentNumber>611</ComponentNumber>
        <ComponentLabel fontSize="14px">Cross-Domain Scaling</ComponentLabel>
        <span style={{ fontSize: '12px' }}>Consistent Performance</span>
      </ComponentBox>

      {/* Competitive Advantage */}
      <ContainerBox width="700px" height="80px" left="50px" top="520px">
        <ContainerLabel fontSize="16px">COMPETITIVE ADVANTAGE</ContainerLabel>
      </ContainerBox>

      <ComponentBox left="100px" top="540px" width="600px" height="40px">
        <ComponentNumber>612</ComponentNumber>
        <ComponentLabel fontSize="14px" color="#555555">AWS and Azure Cannot Replicate Due to Architectural Incompatibility</ComponentLabel>
      </ComponentBox>

      {/* Patent Holder Information */}
      <div style={{
        position: 'absolute',
        bottom: '10px',
        left: '0',
        width: '100%',
        textAlign: 'center',
        fontSize: '12px',
        fontStyle: 'italic'
      }}>
        Patent Pending Holder: David Nigel Irvin
      </div>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Patent Components</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" style={{ border: '1px dashed #333' }} />
          <LegendText>Key Innovations</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default PatentShield;
