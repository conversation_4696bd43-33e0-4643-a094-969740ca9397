# Enhanced Error Handling

NovaConnect provides comprehensive error handling to ensure a consistent and informative error experience for API consumers. This document describes the enhanced error handling features and how to use them.

## Overview

The enhanced error handling system provides the following features:

- **Consistent Error Format**: All errors follow a consistent JSON format
- **Detailed Error Information**: Errors include detailed information for debugging
- **Error Codes**: Each error has a unique code for programmatic handling
- **Correlation IDs**: Errors include correlation IDs for tracking across systems
- **Request Validation**: Comprehensive request validation with detailed error messages
- **Error Logging**: All errors are logged with appropriate context
- **Error Recovery**: Support for retrying failed requests

## Error Response Format

All API errors follow a consistent JSON format:

```json
{
  "error": {
    "type": "Validation Error",
    "message": "Invalid input data",
    "code": "VALIDATION_ERROR",
    "status": 400,
    "correlationId": "550e8400-e29b-41d4-a716-446655440000",
    "details": {
      "body": [
        {
          "path": "name",
          "message": "Name is required",
          "type": "any.required"
        }
      ]
    }
  }
}
```

### Error Properties

| Property | Type | Description |
|----------|------|-------------|
| type | String | Error type (e.g., Validation Error, Authentication Error) |
| message | String | Human-readable error message |
| code | String | Error code for programmatic handling |
| status | Number | HTTP status code |
| correlationId | String | Unique ID for tracking the error across systems |
| details | Object | Additional error details (only included when relevant) |

## Error Types and Codes

NovaConnect uses the following error types and codes:

| HTTP Status | Error Type | Error Code | Description |
|-------------|------------|------------|-------------|
| 400 | Validation Error | VALIDATION_ERROR | Invalid input data |
| 400 | Bad Request | INVALID_JSON | Invalid JSON in request body |
| 401 | Authentication Error | AUTHENTICATION_ERROR | Authentication failed |
| 401 | Authentication Error | INVALID_TOKEN | Invalid authentication token |
| 401 | Authentication Error | TOKEN_EXPIRED | Authentication token expired |
| 403 | Authorization Error | AUTHORIZATION_ERROR | Insufficient permissions |
| 404 | Not Found Error | NOT_FOUND_ERROR | Resource not found |
| 404 | Not Found Error | RESOURCE_NOT_FOUND | Specific resource not found |
| 409 | Conflict Error | CONFLICT_ERROR | Resource conflict |
| 409 | Conflict Error | DUPLICATE_KEY | Duplicate key in database |
| 429 | Rate Limit Error | RATE_LIMIT_ERROR | Too many requests |
| 500 | Internal Server Error | INTERNAL_SERVER_ERROR | Server error |
| 502 | External Service Error | EXTERNAL_SERVICE_ERROR | External service error |
| 504 | Timeout Error | TIMEOUT_ERROR | Request timeout |

## Request Validation

NovaConnect uses Joi for request validation. When a request fails validation, a detailed error response is returned:

```json
{
  "error": {
    "type": "Validation Error",
    "message": "Validation failed",
    "code": "VALIDATION_ERROR",
    "status": 400,
    "correlationId": "550e8400-e29b-41d4-a716-446655440000",
    "details": {
      "body": [
        {
          "path": "name",
          "message": "Name is required",
          "type": "any.required"
        },
        {
          "path": "email",
          "message": "Email must be a valid email address",
          "type": "string.email"
        }
      ]
    }
  }
}
```

### Validation Details

The `details` object contains validation errors for different parts of the request:

- `body`: Validation errors in the request body
- `query`: Validation errors in the query parameters
- `params`: Validation errors in the URL parameters

Each validation error includes:

- `path`: The path to the invalid field
- `message`: A human-readable error message
- `type`: The type of validation error

## Correlation IDs

NovaConnect uses correlation IDs to track requests across systems. The correlation ID is included in:

- Error responses in the `correlationId` field
- Response headers as `X-Correlation-ID`
- Log entries

You can provide your own correlation ID by including the `X-Correlation-ID` header in your request. If you don't provide one, NovaConnect will generate a unique ID for you.

## Error Recovery

NovaConnect supports automatic retry for certain types of errors:

- Timeout errors
- External service errors
- Database connection errors

You can configure retry behavior using the `retryHandler` middleware:

```javascript
const { retryHandler } = require('../middleware/enhancedErrorHandlingMiddleware');

// Retry up to 3 times with exponential backoff
app.use(retryHandler({
  maxRetries: 3,
  retryDelay: 1000,
  shouldRetry: (err) => err instanceof TimeoutError
}));
```

## Error Handling in Controllers

When implementing controllers, you can use the enhanced error classes to throw consistent errors:

```javascript
const { NotFoundError, ValidationError } = require('../utils/enhancedErrors');

// Example controller method
async function getUser(req, res, next) {
  try {
    const { userId } = req.params;
    
    // Validate user ID
    if (!userId) {
      throw new ValidationError('User ID is required');
    }
    
    // Get user from database
    const user = await userService.getUserById(userId);
    
    // Check if user exists
    if (!user) {
      throw new NotFoundError(`User not found with ID: ${userId}`);
    }
    
    // Return user
    res.json(user);
  } catch (error) {
    // Pass error to error handling middleware
    next(error);
  }
}
```

## Request Validation in Routes

You can use the request validator to validate requests in routes:

```javascript
const { validate } = require('../utils/requestValidator');
const Joi = require('joi');

// Define validation schema
const createUserSchema = {
  body: Joi.object({
    name: Joi.string().required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required()
  })
};

// Apply validation middleware
router.post('/users', validate(createUserSchema), userController.createUser);
```

## Error Handling Best Practices

1. **Use Specific Error Classes**: Use the most specific error class for each error case
2. **Include Detailed Error Messages**: Provide clear and detailed error messages
3. **Add Context to Errors**: Include relevant context in error details
4. **Handle Async Errors**: Use `asyncHandler` or try/catch blocks to handle async errors
5. **Validate Request Input**: Always validate request input using the request validator
6. **Log Errors Appropriately**: Ensure errors are logged with appropriate severity
7. **Don't Expose Sensitive Information**: Sanitize error details in production
8. **Use Correlation IDs**: Include correlation IDs in logs and error responses
9. **Implement Retry Logic**: Use retry logic for transient errors
10. **Test Error Handling**: Write tests for error handling code

## Error Handling Middleware

NovaConnect provides the following error handling middleware:

- `errorHandler`: Main error handling middleware
- `notFoundHandler`: Handles 404 errors
- `asyncHandler`: Handles async errors
- `correlationIdMiddleware`: Adds correlation IDs to requests
- `validateRequest`: Validates requests against schemas
- `retryHandler`: Implements retry logic for failed requests
- `timeoutMiddleware`: Implements request timeouts

## Error Classes

NovaConnect provides the following error classes:

- `BaseError`: Base error class
- `ValidationError`: Validation errors
- `AuthenticationError`: Authentication errors
- `AuthorizationError`: Authorization errors
- `NotFoundError`: Not found errors
- `ConflictError`: Conflict errors
- `RateLimitError`: Rate limit errors
- `TimeoutError`: Timeout errors
- `DatabaseError`: Database errors
- `ExternalServiceError`: External service errors
- `InternalServerError`: Internal server errors
