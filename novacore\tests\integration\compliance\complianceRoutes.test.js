/**
 * Compliance Routes Integration Tests
 */

const request = require('supertest');
const { expect } = require('chai');
const sinon = require('sinon');
const app = require('../../../api/app');
const badgeSystem = require('../../../api/services/badgeSystem');
const complianceKit = require('../../../api/services/complianceKit');
const jwt = require('jsonwebtoken');
const config = require('../../../config');

describe('Compliance Routes', () => {
  let authToken;
  
  before(() => {
    // Create a test auth token
    authToken = jwt.sign(
      {
        id: 'user123',
        role: 'admin',
        organizationId: 'org123'
      },
      config.jwt.secretKey,
      { expiresIn: '1h' }
    );
  });
  
  describe('GET /api/v1/compliance/badges/:organizationId/:badgeType', () => {
    let generateBadgeStub;
    let getComplianceStatusStub;
    
    beforeEach(() => {
      // Stub the generateBadge method
      generateBadgeStub = sinon.stub(badgeSystem, 'generateBadge').resolves(Buffer.from('badge'));
      
      // Stub the getComplianceStatus method
      getComplianceStatusStub = sinon.stub(badgeSystem, 'getComplianceStatus').resolves('compliant');
    });
    
    afterEach(() => {
      // Restore the stubs
      generateBadgeStub.restore();
      getComplianceStatusStub.restore();
    });
    
    it('should generate a badge', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges/org123/soc2');
      
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.equal('image/png');
      expect(response.body).to.be.an.instanceof(Buffer);
      
      // Verify that the stubs were called
      expect(generateBadgeStub.calledOnce).to.be.true;
      expect(getComplianceStatusStub.calledOnce).to.be.true;
    });
    
    it('should support custom status', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges/org123/soc2?status=partial');
      
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.equal('image/png');
      expect(response.body).to.be.an.instanceof(Buffer);
      
      // Verify that the stubs were called
      expect(generateBadgeStub.calledOnce).to.be.true;
      expect(generateBadgeStub.firstCall.args[0]).to.have.property('status', 'partial');
      
      // getComplianceStatus should not be called when status is provided
      expect(getComplianceStatusStub.called).to.be.false;
    });
    
    it('should support custom style and size', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges/org123/soc2?style=gradient&size=large');
      
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.equal('image/png');
      expect(response.body).to.be.an.instanceof(Buffer);
      
      // Verify that the stubs were called
      expect(generateBadgeStub.calledOnce).to.be.true;
      expect(generateBadgeStub.firstCall.args[0]).to.have.property('style', 'gradient');
      expect(generateBadgeStub.firstCall.args[0]).to.have.property('size', 'large');
    });
    
    it('should validate parameters', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges//soc2');
      
      expect(response.status).to.equal(400);
    });
  });
  
  describe('POST /api/v1/compliance/badges/verify', () => {
    let verifyBadgeStub;
    
    beforeEach(() => {
      // Stub the verifyBadge method
      verifyBadgeStub = sinon.stub(badgeSystem, 'verifyBadge').resolves({
        verified: true,
        organizationId: 'org123',
        badgeType: 'soc2',
        timestamp: new Date().toISOString(),
        status: 'compliant'
      });
    });
    
    afterEach(() => {
      // Restore the stub
      verifyBadgeStub.restore();
    });
    
    it('should verify a badge', async () => {
      // Create a mock file
      const badge = Buffer.from('badge');
      
      const response = await request(app)
        .post('/api/v1/compliance/badges/verify')
        .attach('badge', badge, 'badge.png');
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('verified', true);
      expect(response.body).to.have.property('organizationId', 'org123');
      expect(response.body).to.have.property('badgeType', 'soc2');
      expect(response.body).to.have.property('timestamp');
      expect(response.body).to.have.property('status', 'compliant');
      
      // Verify that the stub was called
      expect(verifyBadgeStub.calledOnce).to.be.true;
    });
    
    it('should validate request body', async () => {
      const response = await request(app)
        .post('/api/v1/compliance/badges/verify');
      
      expect(response.status).to.equal(400);
    });
  });
  
  describe('GET /api/v1/compliance/badges/:organizationId', () => {
    let getBadgeUrlsStub;
    
    beforeEach(() => {
      // Stub the getBadgeUrls method
      getBadgeUrlsStub = sinon.stub(complianceKit, 'getBadgeUrls').resolves({
        soc2: {
          flat: {
            small: 'https://api.novafuse.com/badges/org123/soc2?style=flat&size=small',
            medium: 'https://api.novafuse.com/badges/org123/soc2?style=flat&size=medium',
            large: 'https://api.novafuse.com/badges/org123/soc2?style=flat&size=large'
          },
          gradient: {
            small: 'https://api.novafuse.com/badges/org123/soc2?style=gradient&size=small',
            medium: 'https://api.novafuse.com/badges/org123/soc2?style=gradient&size=medium',
            large: 'https://api.novafuse.com/badges/org123/soc2?style=gradient&size=large'
          },
          '3d': {
            small: 'https://api.novafuse.com/badges/org123/soc2?style=3d&size=small',
            medium: 'https://api.novafuse.com/badges/org123/soc2?style=3d&size=medium',
            large: 'https://api.novafuse.com/badges/org123/soc2?style=3d&size=large'
          }
        }
      });
    });
    
    afterEach(() => {
      // Restore the stub
      getBadgeUrlsStub.restore();
    });
    
    it('should get badge URLs', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges/org123')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('soc2');
      expect(response.body.soc2).to.have.property('flat');
      expect(response.body.soc2.flat).to.have.property('small');
      expect(response.body.soc2.flat).to.have.property('medium');
      expect(response.body.soc2.flat).to.have.property('large');
      
      // Verify that the stub was called
      expect(getBadgeUrlsStub.calledOnce).to.be.true;
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges/org123');
      
      expect(response.status).to.equal(401);
    });
    
    it('should validate parameters', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/badges/')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(404);
    });
  });
  
  describe('GET /api/v1/compliance/verify/:organizationId/:badgeType', () => {
    let createVerificationPageStub;
    
    beforeEach(() => {
      // Stub the createVerificationPage method
      createVerificationPageStub = sinon.stub(badgeSystem, 'createVerificationPage').resolves('<html>Verification Page</html>');
    });
    
    afterEach(() => {
      // Restore the stub
      createVerificationPageStub.restore();
    });
    
    it('should get a verification page', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/verify/org123/soc2');
      
      expect(response.status).to.equal(200);
      expect(response.headers['content-type']).to.equal('text/html; charset=utf-8');
      expect(response.text).to.equal('<html>Verification Page</html>');
      
      // Verify that the stub was called
      expect(createVerificationPageStub.calledOnce).to.be.true;
    });
    
    it('should validate parameters', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/verify//soc2');
      
      expect(response.status).to.equal(400);
    });
  });
  
  describe('GET /api/v1/compliance/page/:organizationId', () => {
    let generateCompliancePageStub;
    
    beforeEach(() => {
      // Stub the generateCompliancePage method
      generateCompliancePageStub = sinon.stub(complianceKit, 'generateCompliancePage').resolves('# Compliance Page');
    });
    
    afterEach(() => {
      // Restore the stub
      generateCompliancePageStub.restore();
    });
    
    it('should generate a compliance page', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/page/org123')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('markdown', '# Compliance Page');
      
      // Verify that the stub was called
      expect(generateCompliancePageStub.calledOnce).to.be.true;
    });
    
    it('should support custom frameworks', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/page/org123?frameworks=soc2,gdpr')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('markdown', '# Compliance Page');
      
      // Verify that the stub was called
      expect(generateCompliancePageStub.calledOnce).to.be.true;
      expect(generateCompliancePageStub.firstCall.args[0]).to.have.property('frameworks');
      expect(generateCompliancePageStub.firstCall.args[0].frameworks).to.deep.equal(['soc2', 'gdpr']);
    });
    
    it('should support includeHistory and includeEvidence', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/page/org123?includeHistory=false&includeEvidence=false')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('markdown', '# Compliance Page');
      
      // Verify that the stub was called
      expect(generateCompliancePageStub.calledOnce).to.be.true;
      expect(generateCompliancePageStub.firstCall.args[0]).to.have.property('includeHistory', false);
      expect(generateCompliancePageStub.firstCall.args[0]).to.have.property('includeEvidence', false);
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/page/org123');
      
      expect(response.status).to.equal(401);
    });
    
    it('should validate parameters', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/page/')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(404);
    });
  });
  
  describe('GET /api/v1/compliance/overview/:organizationId', () => {
    let generateComplianceOverviewStub;
    
    beforeEach(() => {
      // Stub the generateComplianceOverview method
      generateComplianceOverviewStub = sinon.stub(complianceKit, 'generateComplianceOverview').resolves({
        organization: {
          id: 'org123',
          name: 'Test Organization'
        },
        compliance: {
          overall: 'compliant',
          frameworks: [
            {
              name: 'SOC 2',
              status: 'compliant',
              lastAssessment: new Date().toISOString()
            }
          ],
          history: [],
          evidence: {}
        },
        verification: {
          url: 'https://novafuse.com/verify/org123',
          lastVerified: new Date().toISOString()
        }
      });
    });
    
    afterEach(() => {
      // Restore the stub
      generateComplianceOverviewStub.restore();
    });
    
    it('should generate a compliance overview', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/overview/org123')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('organization');
      expect(response.body.organization).to.have.property('id', 'org123');
      expect(response.body).to.have.property('compliance');
      expect(response.body.compliance).to.have.property('overall', 'compliant');
      expect(response.body).to.have.property('verification');
      
      // Verify that the stub was called
      expect(generateComplianceOverviewStub.calledOnce).to.be.true;
    });
    
    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/overview/org123');
      
      expect(response.status).to.equal(401);
    });
    
    it('should validate parameters', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/overview/')
        .set('Authorization', `Bearer ${authToken}`);
      
      expect(response.status).to.equal(404);
    });
  });
  
  describe('GET /api/v1/compliance/charter', () => {
    let getComplianceCharterStub;
    
    beforeEach(() => {
      // Stub the getComplianceCharter method
      getComplianceCharterStub = sinon.stub(complianceKit, 'getComplianceCharter').resolves('# Compliance Charter');
    });
    
    afterEach(() => {
      // Restore the stub
      getComplianceCharterStub.restore();
    });
    
    it('should get the compliance charter', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/charter');
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('markdown', '# Compliance Charter');
      
      // Verify that the stub was called
      expect(getComplianceCharterStub.calledOnce).to.be.true;
    });
  });
  
  describe('GET /api/v1/compliance/framework', () => {
    let getCyberSafetyFrameworkStub;
    
    beforeEach(() => {
      // Stub the getCyberSafetyFramework method
      getCyberSafetyFrameworkStub = sinon.stub(complianceKit, 'getCyberSafetyFramework').resolves({
        name: 'Cyber-Safety Framework',
        version: '1.0.0',
        description: 'Framework description',
        pillars: [],
        benefits: [],
        implementation: {
          phases: []
        }
      });
    });
    
    afterEach(() => {
      // Restore the stub
      getCyberSafetyFrameworkStub.restore();
    });
    
    it('should get the Cyber-Safety Framework', async () => {
      const response = await request(app)
        .get('/api/v1/compliance/framework');
      
      expect(response.status).to.equal(200);
      expect(response.body).to.be.an('object');
      expect(response.body).to.have.property('name', 'Cyber-Safety Framework');
      expect(response.body).to.have.property('version', '1.0.0');
      expect(response.body).to.have.property('description', 'Framework description');
      expect(response.body).to.have.property('pillars');
      expect(response.body).to.have.property('benefits');
      expect(response.body).to.have.property('implementation');
      
      // Verify that the stub was called
      expect(getCyberSafetyFrameworkStub.calledOnce).to.be.true;
    });
  });
});
