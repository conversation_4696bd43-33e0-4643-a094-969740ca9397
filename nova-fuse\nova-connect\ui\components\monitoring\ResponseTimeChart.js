/**
 * Response Time Chart Component
 * 
 * This component displays a chart of connector response times over time.
 */

import React from 'react';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { Box, Typography } from '@mui/material';

const ResponseTimeChart = ({ data }) => {
  // Process data for the chart
  const processChartData = () => {
    // Create an array of the last 24 hours
    const hours = Array(24).fill(0).map((_, i) => {
      const date = new Date();
      date.setHours(date.getHours() - 23 + i);
      return date.getHours();
    });
    
    // Initialize data for each hour
    const chartData = hours.map(hour => ({
      hour: `${hour}:00`,
      // Initialize with empty arrays to collect all response times
      responseTimes: []
    }));
    
    // Collect response times for each hour
    data.forEach(connector => {
      if (connector.history && connector.history.responseTimes) {
        connector.history.responseTimes.forEach((time, i) => {
          chartData[i].responseTimes.push(time);
        });
      }
    });
    
    // Calculate average, min, and max for each hour
    chartData.forEach(hourData => {
      if (hourData.responseTimes.length > 0) {
        hourData.average = Math.round(
          hourData.responseTimes.reduce((sum, time) => sum + time, 0) / hourData.responseTimes.length
        );
        hourData.min = Math.min(...hourData.responseTimes);
        hourData.max = Math.max(...hourData.responseTimes);
      } else {
        hourData.average = 0;
        hourData.min = 0;
        hourData.max = 0;
      }
      
      // Remove the original array to clean up the data
      delete hourData.responseTimes;
    });
    
    return chartData;
  };
  
  const chartData = processChartData();
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Box sx={{ bgcolor: 'background.paper', p: 2, border: '1px solid #ccc', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              <Box 
                sx={{ 
                  width: 12, 
                  height: 12, 
                  bgcolor: entry.color, 
                  mr: 1, 
                  borderRadius: '50%' 
                }} 
              />
              <Typography variant="body2">
                {entry.name}: {entry.value} ms
              </Typography>
            </Box>
          ))}
        </Box>
      );
    }
    
    return null;
  };
  
  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={chartData}
        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="hour" />
        <YAxis />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Line 
          type="monotone" 
          dataKey="average" 
          name="Average" 
          stroke="#2196f3" 
          strokeWidth={2} 
          dot={{ r: 3 }} 
          activeDot={{ r: 5 }} 
        />
        <Line 
          type="monotone" 
          dataKey="min" 
          name="Minimum" 
          stroke="#4caf50" 
          strokeWidth={1} 
          dot={{ r: 2 }} 
          strokeDasharray="5 5" 
        />
        <Line 
          type="monotone" 
          dataKey="max" 
          name="Maximum" 
          stroke="#f44336" 
          strokeWidth={1} 
          dot={{ r: 2 }} 
          strokeDasharray="5 5" 
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default ResponseTimeChart;
