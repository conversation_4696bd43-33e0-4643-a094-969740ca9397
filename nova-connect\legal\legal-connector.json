﻿{
  "extends": "base-connector",
  "name": "novafuse-legal-connector",
  "version": "1.0.0",
  "description": "Legal connector template for NovaFuse API Superstore",
  "category": "legal",
  "base_url": "http://localhost:8000/legal",
  "endpoints": [
    {
      "name": "regulations",
      "path": "/regulations",
      "method": "GET",
      "description": "Get a list of regulations",
      "parameters": [
        {
          "name": "jurisdiction",
          "in": "query",
          "required": false,
          "description": "Filter by jurisdiction"
        },
        {
          "name": "category",
          "in": "query",
          "required": false,
          "description": "Filter by category"
        }
      ]
    },
    {
      "name": "compliance_check",
      "path": "/compliance/check",
      "method": "POST",
      "description": "Check compliance against regulations"
    }
  ]
}
