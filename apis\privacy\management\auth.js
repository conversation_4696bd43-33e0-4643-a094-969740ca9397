/**
 * Authentication and Authorization Module
 * 
 * This module provides functionality for authenticating and authorizing API requests.
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// Secret key for JWT signing (in a real app, this would be in environment variables)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Token expiration time
const TOKEN_EXPIRATION = '24h';

// Sample users (in a real app, these would be in a database)
const users = [
  {
    id: 'user-001',
    username: 'admin',
    password: '$2b$10$X7o4.KM6XRxQ5hGKp1EZpOH8Cd4/6S9Bp9xWw6.9BdFD1OCaIBHDe', // hashed 'admin123'
    role: 'admin'
  },
  {
    id: 'user-002',
    username: 'privacy-officer',
    password: '$2b$10$X7o4.KM6XRxQ5hGKp1EZpOH8Cd4/6S9Bp9xWw6.9BdFD1OCaIBHDe', // hashed 'admin123'
    role: 'privacy-officer'
  },
  {
    id: 'user-003',
    username: 'data-processor',
    password: '$2b$10$X7o4.KM6XRxQ5hGKp1EZpOH8Cd4/6S9Bp9xWw6.9BdFD1OCaIBHDe', // hashed 'admin123'
    role: 'data-processor'
  }
];

// Role-based permissions
const permissions = {
  'admin': ['read', 'write', 'delete'],
  'privacy-officer': ['read', 'write'],
  'data-processor': ['read']
};

/**
 * Generate a JWT token for a user
 * @param {Object} user - User object
 * @returns {string} - JWT token
 */
const generateToken = (user) => {
  const payload = {
    id: user.id,
    username: user.username,
    role: user.role
  };

  return jwt.sign(payload, JWT_SECRET, { expiresIn: TOKEN_EXPIRATION });
};

/**
 * Verify a JWT token
 * @param {string} token - JWT token
 * @returns {Object|null} - Decoded token payload or null if invalid
 */
const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

/**
 * Authenticate a user
 * @param {string} username - Username
 * @param {string} password - Password
 * @returns {Object} - Authentication result
 */
const authenticateUser = async (username, password) => {
  // Find user by username
  const user = users.find(u => u.username === username);

  if (!user) {
    return {
      success: false,
      message: 'User not found'
    };
  }

  // Verify password
  const passwordMatch = await bcrypt.compare(password, user.password);

  if (!passwordMatch) {
    return {
      success: false,
      message: 'Invalid password'
    };
  }

  // Generate token
  const token = generateToken(user);

  return {
    success: true,
    token,
    user: {
      id: user.id,
      username: user.username,
      role: user.role
    }
  };
};

/**
 * Check if a user has permission for an action
 * @param {string} role - User role
 * @param {string} action - Action to check
 * @returns {boolean} - Whether the user has permission
 */
const hasPermission = (role, action) => {
  if (!role || !action) {
    return false;
  }

  const rolePermissions = permissions[role] || [];
  return rolePermissions.includes(action);
};

/**
 * Authentication middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticate = (req, res, next) => {
  // Get token from Authorization header
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Authentication token is required'
    });
  }

  const token = authHeader.split(' ')[1];
  
  // Verify token
  const decoded = verifyToken(token);
  
  if (!decoded) {
    return res.status(401).json({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    });
  }

  // Add user to request object
  req.user = decoded;
  
  next();
};

/**
 * Authorization middleware
 * @param {string} action - Required action permission
 * @returns {Function} - Express middleware
 */
const authorize = (action) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }

    if (!hasPermission(req.user.role, action)) {
      return res.status(403).json({
        error: 'Forbidden',
        message: 'You do not have permission to perform this action'
      });
    }

    next();
  };
};

module.exports = {
  generateToken,
  verifyToken,
  authenticateUser,
  hasPermission,
  authenticate,
  authorize
};
