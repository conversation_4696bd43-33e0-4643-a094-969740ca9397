'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiPlus, FiFilter, FiDownload, FiTrash2, FiEdit, FiEye } from 'react-icons/fi';

// Mock data for evidence
const mockEvidence = [
  { id: 'ev-001', name: 'Password Policy', category: 'Policies', status: 'Valid', date: '2023-10-15' },
  { id: 'ev-002', name: 'Firewall Configuration', category: 'Configurations', status: 'Valid', date: '2023-10-14' },
  { id: 'ev-003', name: 'User Access Review', category: 'Reports', status: 'Pending', date: '2023-10-13' },
  { id: 'ev-004', name: 'Incident Response Plan', category: 'Procedures', status: 'Invalid', date: '2023-10-12' },
  { id: 'ev-005', name: 'System Logs', category: 'Logs', status: 'Valid', date: '2023-10-11' },
  { id: 'ev-006', name: 'Backup Procedure', category: 'Procedures', status: 'Valid', date: '2023-10-10' },
  { id: 'ev-007', name: 'Vulnerability Scan', category: 'Reports', status: 'Valid', date: '2023-10-09' },
  { id: 'ev-008', name: 'Security Awareness Training', category: 'Policies', status: 'Pending', date: '2023-10-08' },
  { id: 'ev-009', name: 'Network Diagram', category: 'Configurations', status: 'Valid', date: '2023-10-07' },
  { id: 'ev-010', name: 'Audit Logs', category: 'Logs', status: 'Invalid', date: '2023-10-06' },
];

export default function EvidencePage() {
  const [evidence, setEvidence] = useState(mockEvidence);
  const [selectedEvidence, setSelectedEvidence] = useState<string[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    category: '',
    status: '',
  });

  // In a real application, we would fetch the data from the API
  useEffect(() => {
    // Fetch data from API
    // For now, we'll use the mock data
    setEvidence(mockEvidence);
  }, []);

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedEvidence(evidence.map((item) => item.id));
    } else {
      setSelectedEvidence([]);
    }
  };

  const handleSelectEvidence = (id: string) => {
    if (selectedEvidence.includes(id)) {
      setSelectedEvidence(selectedEvidence.filter((item) => item !== id));
    } else {
      setSelectedEvidence([...selectedEvidence, id]);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
    });
  };

  const applyFilters = () => {
    let filteredEvidence = [...mockEvidence];

    if (filters.category) {
      filteredEvidence = filteredEvidence.filter((item) => item.category === filters.category);
    }

    if (filters.status) {
      filteredEvidence = filteredEvidence.filter((item) => item.status === filters.status);
    }

    setEvidence(filteredEvidence);
    setIsFilterOpen(false);
  };

  const resetFilters = () => {
    setFilters({
      category: '',
      status: '',
    });
    setEvidence(mockEvidence);
    setIsFilterOpen(false);
  };

  return (
    <MainLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Evidence</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage your compliance evidence</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="btn btn-outline flex items-center"
          >
            <FiFilter className="mr-2" />
            Filter
          </button>
          <button className="btn btn-primary flex items-center">
            <FiPlus className="mr-2" />
            Add Evidence
          </button>
        </div>
      </div>

      {/* Filter panel */}
      {isFilterOpen && (
        <div className="card mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter Evidence</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="category" className="label">
                Category
              </label>
              <select
                id="category"
                name="category"
                value={filters.category}
                onChange={handleFilterChange}
                className="input"
              >
                <option value="">All Categories</option>
                <option value="Policies">Policies</option>
                <option value="Procedures">Procedures</option>
                <option value="Configurations">Configurations</option>
                <option value="Logs">Logs</option>
                <option value="Reports">Reports</option>
              </select>
            </div>
            <div>
              <label htmlFor="status" className="label">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="input"
              >
                <option value="">All Statuses</option>
                <option value="Valid">Valid</option>
                <option value="Invalid">Invalid</option>
                <option value="Pending">Pending</option>
              </select>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <button onClick={resetFilters} className="btn btn-outline">
              Reset
            </button>
            <button onClick={applyFilters} className="btn btn-primary">
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Evidence table */}
      <div className="card">
        <div className="flex justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Evidence List</h2>
          {selectedEvidence.length > 0 && (
            <div className="flex space-x-2">
              <button className="btn btn-outline flex items-center text-sm">
                <FiDownload className="mr-1" />
                Export
              </button>
              <button className="btn btn-outline flex items-center text-sm text-red-500 border-red-500 hover:bg-red-500 hover:text-white">
                <FiTrash2 className="mr-1" />
                Delete
              </button>
            </div>
          )}
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    onChange={handleSelectAll}
                    checked={selectedEvidence.length === evidence.length && evidence.length > 0}
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
              {evidence.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      checked={selectedEvidence.includes(item.id)}
                      onChange={() => handleSelectEvidence(item.id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {item.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        item.status === 'Valid'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : item.status === 'Invalid'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                      }`}
                    >
                      {item.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.date}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button className="text-primary hover:text-primary-dark">
                        <FiEye />
                      </button>
                      <button className="text-primary hover:text-primary-dark">
                        <FiEdit />
                      </button>
                      <button className="text-red-500 hover:text-red-700">
                        <FiTrash2 />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">{evidence.length}</span> of{' '}
            <span className="font-medium">{mockEvidence.length}</span> evidence items
          </div>
          <div className="flex space-x-2">
            <button className="btn btn-outline">Previous</button>
            <button className="btn btn-outline">Next</button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
