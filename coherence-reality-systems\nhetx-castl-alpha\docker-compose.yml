# AL<PERSON>HA OBSERVER-CLASS COHERENCE ENGINE
# Docker Compose Configuration for Full Calibration Mode
#
# DIRECTIVE: All resources allocated to precision-tuning ALPHA's coherence engines
# NO DISTRACTIONS—ONLY OPTIMIZATION

version: '3.8'

services:
  # ALPHA Calibration Engine
  alpha-calibration:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: alpha-observer-class-calibration
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - ALPHA_CALIBRATION_MODE=true
      - ALPHA_LOG_LEVEL=info
      - ALPHA_UPDATE_INTERVAL=72h
      - ALPHA_MAX_CYCLES=10
      # Calibration Targets
      - NEFC_TARGET_WIN_RATE=0.95
      - NHETX_TARGET_C_SCORE=0.97
      - KAPPA_TARGET_LIFT=0.01
      # Resource Allocation
      - NEFC_NHETX_ALLOCATION=0.70
      - <PERSON><PERSON><PERSON>_LAB_ALLOCATION=0.20
      - <PERSON><PERSON>_MEDICAL_ALLOCATION=0.10
    volumes:
      - alpha-calibration-data:/app/alpha-calibration/data
      - alpha-logs:/app/alpha-calibration/logs
    networks:
      - alpha-network
    healthcheck:
      test: ["CMD", "node", "-e", "console.log('ALPHA: OPERATIONAL')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.alpha.rule=Host(`alpha.localhost`)"
      - "traefik.http.services.alpha.loadbalancer.server.port=3000"
      - "alpha.calibration.mode=FULL_CALIBRATION"
      - "alpha.priority=PRECISION_TUNING"

  # ALPHA Monitoring Dashboard
  alpha-dashboard:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: alpha-calibration-dashboard
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
      - ALPHA_DASHBOARD_MODE=true
      - ALPHA_MONITORING_INTERVAL=72h
    command: ["node", "alpha-calibration-dashboard.js"]
    volumes:
      - alpha-calibration-data:/app/alpha-calibration/data:ro
      - alpha-logs:/app/alpha-calibration/logs:ro
    networks:
      - alpha-network
    depends_on:
      - alpha-calibration
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.alpha-dashboard.rule=Host(`dashboard.alpha.localhost`)"
      - "traefik.http.services.alpha-dashboard.loadbalancer.server.port=3000"

  # Redis for calibration state persistence
  alpha-redis:
    image: redis:7-alpine
    container_name: alpha-calibration-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - alpha-redis-data:/data
    networks:
      - alpha-network
    command: redis-server --appendonly yes --appendfsync everysec
    labels:
      - "alpha.component=state-persistence"

  # Nginx reverse proxy for ALPHA services
  alpha-proxy:
    image: nginx:alpine
    container_name: alpha-calibration-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - alpha-ssl-certs:/etc/nginx/ssl:ro
    networks:
      - alpha-network
    depends_on:
      - alpha-calibration
      - alpha-dashboard
    labels:
      - "alpha.component=reverse-proxy"

# Persistent volumes for ALPHA data
volumes:
  alpha-calibration-data:
    driver: local
    labels:
      - "alpha.data.type=calibration-state"
  alpha-logs:
    driver: local
    labels:
      - "alpha.data.type=system-logs"
  alpha-redis-data:
    driver: local
    labels:
      - "alpha.data.type=redis-persistence"
  alpha-ssl-certs:
    driver: local
    labels:
      - "alpha.data.type=ssl-certificates"

# ALPHA network
networks:
  alpha-network:
    driver: bridge
    labels:
      - "alpha.network=calibration-cluster"
    ipam:
      config:
        - subnet: **********/16
