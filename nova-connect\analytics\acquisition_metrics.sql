-- NovaConnect UAC Acquisition Metrics
-- This SQL script generates metrics for the acquisition valuation dashboard

-- Daily active customers and usage
WITH daily_usage AS (
  SELECT
    DATE(timestamp) AS date,
    customer_id,
    SUM(CASE WHEN metric_name = 'grc_api_calls' THEN quantity ELSE 0 END) AS grc_checks,
    SUM(CASE WHEN metric_name = 'connector_executions' THEN quantity ELSE 0 END) AS connector_executions,
    COUNT(DISTINCT metric_name) AS distinct_metrics_used
  FROM
    `${PROJECT_ID}.billing.usage`
  WHERE
    timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY)
  GROUP BY
    date, customer_id
),

-- Customer tiers
customer_tiers AS (
  SELECT
    customer_id,
    plan AS tier,
    CASE
      WHEN plan = 'core' THEN 0.001
      WHEN plan = 'secure' THEN 0.002
      WHEN plan = 'enterprise' THEN 0.005
      WHEN plan = 'ai_boost' THEN 0.01
      ELSE 0.001
    END AS grc_check_price,
    CASE
      WHEN plan = 'core' THEN 0.005
      WHEN plan = 'secure' THEN 0.01
      WHEN plan = 'enterprise' THEN 0.02
      WHEN plan = 'ai_boost' THEN 0.05
      ELSE 0.005
    END AS connector_execution_price
  FROM
    `${PROJECT_ID}.billing.entitlements`
  WHERE
    status = 'ACTIVE'
    AND event_type = 'ENTITLEMENT_CREATED'
    OR event_type = 'ENTITLEMENT_UPDATED'
    OR event_type = 'ENTITLEMENT_ACTIVATED'
  QUALIFY ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY timestamp DESC) = 1
),

-- Daily revenue
daily_revenue AS (
  SELECT
    u.date,
    u.customer_id,
    t.tier,
    u.grc_checks,
    u.connector_executions,
    u.grc_checks * t.grc_check_price AS grc_checks_revenue,
    u.connector_executions * t.connector_execution_price AS connector_executions_revenue,
    (u.grc_checks * t.grc_check_price) + (u.connector_executions * t.connector_execution_price) AS total_revenue
  FROM
    daily_usage u
  LEFT JOIN
    customer_tiers t
  ON
    u.customer_id = t.customer_id
),

-- Monthly aggregates
monthly_aggregates AS (
  SELECT
    DATE_TRUNC(date, MONTH) AS month,
    COUNT(DISTINCT customer_id) AS active_customers,
    SUM(grc_checks) AS total_grc_checks,
    SUM(connector_executions) AS total_connector_executions,
    SUM(total_revenue) AS monthly_revenue,
    SUM(total_revenue) * 12 AS annualized_revenue
  FROM
    daily_revenue
  GROUP BY
    month
  ORDER BY
    month DESC
),

-- Customer growth
customer_growth AS (
  SELECT
    DATE_TRUNC(timestamp, MONTH) AS month,
    COUNT(DISTINCT customer_id) AS new_customers
  FROM
    `${PROJECT_ID}.billing.entitlements`
  WHERE
    event_type = 'ENTITLEMENT_CREATED'
  GROUP BY
    month
  ORDER BY
    month DESC
),

-- Customer retention
customer_retention AS (
  SELECT
    current_month,
    previous_month,
    current_customers,
    previous_customers,
    retained_customers,
    SAFE_DIVIDE(retained_customers, previous_customers) AS retention_rate
  FROM (
    SELECT
      current_month,
      previous_month,
      current_customers,
      previous_customers,
      COUNT(DISTINCT CASE WHEN current_flag AND previous_flag THEN customer_id END) AS retained_customers
    FROM (
      SELECT
        customer_id,
        month AS current_month,
        LAG(month) OVER (PARTITION BY customer_id ORDER BY month) AS previous_month,
        TRUE AS current_flag,
        LAG(TRUE) OVER (PARTITION BY customer_id ORDER BY month) AS previous_flag,
        COUNT(DISTINCT CASE WHEN month = month THEN customer_id END) OVER (PARTITION BY month) AS current_customers,
        COUNT(DISTINCT CASE WHEN month = LAG(month) OVER (PARTITION BY customer_id ORDER BY month) THEN customer_id END) OVER (PARTITION BY month) AS previous_customers
      FROM (
        SELECT
          customer_id,
          DATE_TRUNC(date, MONTH) AS month
        FROM
          daily_usage
        GROUP BY
          customer_id, month
      )
    )
    GROUP BY
      current_month, previous_month, current_customers, previous_customers
  )
  WHERE
    previous_month IS NOT NULL
  ORDER BY
    current_month DESC
),

-- Tier distribution
tier_distribution AS (
  SELECT
    tier,
    COUNT(DISTINCT customer_id) AS customer_count,
    ROUND(COUNT(DISTINCT customer_id) * 100.0 / (SELECT COUNT(DISTINCT customer_id) FROM customer_tiers), 2) AS percentage
  FROM
    customer_tiers
  GROUP BY
    tier
  ORDER BY
    customer_count DESC
),

-- Usage patterns
usage_patterns AS (
  SELECT
    DATE_TRUNC(date, WEEK) AS week,
    AVG(grc_checks) AS avg_grc_checks_per_customer,
    AVG(connector_executions) AS avg_connector_executions_per_customer,
    MAX(grc_checks) AS max_grc_checks,
    MAX(connector_executions) AS max_connector_executions
  FROM
    daily_usage
  GROUP BY
    week
  ORDER BY
    week DESC
),

-- Valuation metrics
valuation_metrics AS (
  SELECT
    CURRENT_DATE() AS valuation_date,
    (SELECT COUNT(DISTINCT customer_id) FROM customer_tiers) AS total_customers,
    (SELECT SUM(monthly_revenue) FROM monthly_aggregates WHERE month = DATE_TRUNC(CURRENT_DATE(), MONTH)) AS current_mrr,
    (SELECT SUM(monthly_revenue) * 12 FROM monthly_aggregates WHERE month = DATE_TRUNC(CURRENT_DATE(), MONTH)) AS current_arr,
    (SELECT AVG(total_revenue) FROM daily_revenue) AS average_daily_revenue,
    (SELECT AVG(retention_rate) FROM customer_retention) AS average_retention_rate,
    (SELECT COUNT(DISTINCT customer_id) FROM daily_usage WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)) AS monthly_active_customers,
    (SELECT COUNT(DISTINCT customer_id) FROM daily_usage WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)) AS weekly_active_customers,
    (SELECT COUNT(DISTINCT customer_id) FROM daily_usage WHERE date = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)) AS daily_active_customers
)

-- Final acquisition dashboard query
SELECT
  vm.valuation_date,
  vm.total_customers,
  vm.current_mrr,
  vm.current_arr,
  vm.average_daily_revenue,
  vm.average_retention_rate,
  vm.monthly_active_customers,
  vm.weekly_active_customers,
  vm.daily_active_customers,
  
  -- Growth metrics
  (SELECT new_customers FROM customer_growth WHERE month = DATE_TRUNC(CURRENT_DATE(), MONTH)) AS new_customers_this_month,
  (SELECT annualized_revenue FROM monthly_aggregates WHERE month = DATE_TRUNC(CURRENT_DATE(), MONTH)) AS current_annualized_revenue,
  
  -- Valuation multipliers
  vm.current_arr * 5 AS conservative_valuation,
  vm.current_arr * 10 AS target_valuation,
  vm.current_arr * 15 AS optimistic_valuation,
  
  -- Projected growth
  vm.current_arr * 1.5 AS projected_arr_6_months,
  vm.current_arr * 2.5 AS projected_arr_12_months,
  vm.current_arr * 5 AS projected_arr_24_months,
  
  -- Acquisition readiness score (0-100)
  CASE
    WHEN vm.current_arr >= 1000000 AND vm.average_retention_rate >= 0.9 THEN 100
    WHEN vm.current_arr >= 500000 AND vm.average_retention_rate >= 0.8 THEN 80
    WHEN vm.current_arr >= 250000 AND vm.average_retention_rate >= 0.7 THEN 60
    WHEN vm.current_arr >= 100000 AND vm.average_retention_rate >= 0.6 THEN 40
    ELSE 20
  END AS acquisition_readiness_score
FROM
  valuation_metrics vm;
