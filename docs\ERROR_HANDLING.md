# NovaConnect UAC Error Handling

This document provides information on the error handling system used in NovaConnect Universal API Connector (UAC).

## Overview

NovaConnect UAC uses a centralized error handling system to ensure consistent error responses across the API. This system includes:

1. Error handling middleware
2. Error handling service
3. Custom error types
4. Error logging
5. Resilience patterns (retry, circuit breaker, timeout, bulkhead)

## Error Response Format

All API errors are returned in a consistent format:

```json
{
  "error": {
    "type": "error_type",
    "message": "Error message",
    "status": 400,
    "details": {
      "field": "name",
      "message": "Name is required"
    }
  }
}
```

The error response includes the following fields:

- `type`: A machine-readable error type
- `message`: A human-readable error message
- `status`: The HTTP status code
- `details`: Additional error details (optional)

## Error Types

NovaConnect UAC defines the following error types:

### Client Errors (4xx)

| Error Type | Status Code | Description |
| --- | --- | --- |
| `bad_request` | 400 | The request is invalid |
| `validation_error` | 400 | The request data failed validation |
| `authentication_error` | 401 | Authentication is required or failed |
| `authorization_error` | 403 | The user does not have permission to perform the action |
| `not_found_error` | 404 | The requested resource was not found |
| `method_not_allowed` | 405 | The HTTP method is not allowed for the resource |
| `conflict_error` | 409 | The request conflicts with the current state of the resource |
| `rate_limit_error` | 429 | The user has exceeded the rate limit |
| `feature_access_denied` | 403 | The user does not have access to the requested feature |
| `feature_limit_reached` | 429 | The user has reached the limit for the requested feature |

### Server Errors (5xx)

| Error Type | Status Code | Description |
| --- | --- | --- |
| `internal_error` | 500 | An internal server error occurred |
| `database_error` | 500 | A database error occurred |
| `external_service_error` | 502 | An external service error occurred |
| `service_unavailable` | 503 | The service is temporarily unavailable |
| `timeout_error` | 504 | The request timed out |
| `circuit_breaker_error` | 503 | The circuit breaker is open |
| `bulkhead_error` | 503 | The bulkhead limit has been reached |

## Error Handling Middleware

NovaConnect UAC uses the following error handling middleware:

### Error Handler

The error handler middleware catches all errors and returns a consistent error response:

```javascript
const errorHandler = (err, req, res, next) => {
  // Create error context
  const context = {
    user: req.user,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    resource: req.originalUrl,
    retryCount: req.retryCount || 0,
    tags: {
      route: req.route ? req.route.path : 'unknown',
      controller: req.controller || 'unknown',
      action: req.action || 'unknown'
    },
    extra: {
      query: req.query,
      params: req.params,
      headers: req.headers
    }
  };
  
  // Handle the error
  const errorResponse = errorHandlingService.handleError(err, context);
  
  // Send error response
  res.status(errorResponse.error.status).json(errorResponse);
};
```

### Not Found Handler

The not found handler middleware catches requests to non-existent routes:

```javascript
const notFoundHandler = (req, res, next) => {
  const notFoundError = new Error(`Resource not found: ${req.originalUrl}`);
  notFoundError.name = 'NotFoundError';
  next(notFoundError);
};
```

### Async Handler

The async handler middleware catches errors in async route handlers:

```javascript
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
```

## Resilience Patterns

NovaConnect UAC implements the following resilience patterns:

### Retry

The retry handler middleware adds retry functionality to routes:

```javascript
const retryHandler = (options = {}) => {
  return (req, res, next) => {
    // Initialize retry count
    req.retryCount = req.retryCount || 0;
    
    // Add retry-after header if retry count is greater than 0
    if (req.retryCount > 0) {
      const retryAfter = options.retryAfter || 1;
      res.set('Retry-After', retryAfter.toString());
    }
    
    next();
  };
};
```

### Circuit Breaker

The circuit breaker middleware adds circuit breaker functionality to routes:

```javascript
const circuitBreakerHandler = (options = {}) => {
  const resource = options.resource || 'default';
  
  // Register circuit breaker
  errorHandlingService.registerCircuitBreaker(resource, options);
  
  return (req, res, next) => {
    const circuitBreaker = errorHandlingService.circuitBreakers.get(resource);
    
    // Check circuit breaker state
    if (circuitBreaker.state === 'open') {
      const circuitBreakerError = new Error(`Circuit breaker for ${resource} is open`);
      circuitBreakerError.name = 'CircuitBreakerError';
      return next(circuitBreakerError);
    }
    
    // Set resource on request
    req.resource = resource;
    
    // Add response listener to update circuit breaker state
    res.on('finish', () => {
      if (res.statusCode >= 500) {
        // Update circuit breaker state
        circuitBreaker.failureCount++;
        circuitBreaker.lastFailureTime = Date.now();
        
        // Check if circuit breaker should trip
        if (circuitBreaker.state === 'closed' && circuitBreaker.failureCount >= circuitBreaker.failureThreshold) {
          circuitBreaker.state = 'open';
          
          // Schedule circuit breaker reset
          setTimeout(() => {
            circuitBreaker.state = 'half-open';
            circuitBreaker.failureCount = 0;
          }, circuitBreaker.resetTimeout);
          
          logger.warn(`Circuit breaker for ${resource} tripped`, {
            failureCount: circuitBreaker.failureCount,
            failureThreshold: circuitBreaker.failureThreshold,
            resetTimeout: circuitBreaker.resetTimeout
          });
        }
      } else if (circuitBreaker.state === 'half-open') {
        // If circuit breaker is half-open and the call succeeded, close it
        circuitBreaker.state = 'closed';
        circuitBreaker.failureCount = 0;
        
        logger.info(`Circuit breaker for ${resource} closed`, {
          state: circuitBreaker.state
        });
      }
    });
    
    next();
  };
};
```

### Timeout

The timeout middleware adds timeout functionality to routes:

```javascript
const timeoutHandler = (options = {}) => {
  const timeoutMs = options.timeoutMs || 30000;
  
  return (req, res, next) => {
    // Set timeout on request
    req.timeout = setTimeout(() => {
      const timeoutError = new Error(`Request timed out after ${timeoutMs}ms`);
      timeoutError.name = 'TimeoutError';
      next(timeoutError);
    }, timeoutMs);
    
    // Clear timeout when response is sent
    res.on('finish', () => {
      clearTimeout(req.timeout);
    });
    
    next();
  };
};
```

### Bulkhead

The bulkhead middleware adds bulkhead functionality to routes:

```javascript
const bulkheadHandler = (options = {}) => {
  const resource = options.resource || 'default';
  const limit = options.limit || 10;
  let activeCount = 0;
  
  // Register bulkhead limit
  errorHandlingService.registerBulkheadLimit(resource, limit);
  
  return (req, res, next) => {
    // Check if bulkhead limit is reached
    if (activeCount >= limit) {
      const bulkheadError = new Error(`Bulkhead limit reached for ${resource}`);
      bulkheadError.name = 'BulkheadError';
      return next(bulkheadError);
    }
    
    // Increment active count
    activeCount++;
    
    // Decrement active count when response is sent
    res.on('finish', () => {
      activeCount--;
    });
    
    next();
  };
};
```

## Error Handling Service

NovaConnect UAC uses an error handling service to centralize error handling logic:

```javascript
class ErrorHandlingService {
  constructor() {
    // Circuit breakers
    this.circuitBreakers = new Map();
    
    // Bulkhead limits
    this.bulkheadLimits = new Map();
    
    // Error types
    this.errorTypes = {
      // Client errors
      ValidationError: {
        status: 400,
        type: 'validation_error',
        message: 'Validation error'
      },
      AuthenticationError: {
        status: 401,
        type: 'authentication_error',
        message: 'Authentication error'
      },
      // ... other error types
    };
  }
  
  /**
   * Handle error
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   * @returns {Object} - Error response
   */
  handleError(error, context = {}) {
    // Get error type
    const errorType = this.getErrorType(error);
    
    // Create error response
    const errorResponse = {
      error: {
        type: errorType.type,
        message: error.message || errorType.message,
        status: errorType.status,
        details: error.details || null
      }
    };
    
    // Log error
    this.logError(error, errorType, context);
    
    return errorResponse;
  }
  
  // ... other methods
}
```

## Custom Error Types

NovaConnect UAC defines custom error types for common error scenarios:

```javascript
// Validation error
const validationError = new Error('Validation error');
validationError.name = 'ValidationError';
validationError.details = {
  field: 'name',
  message: 'Name is required'
};

// Authentication error
const authenticationError = new Error('Authentication error');
authenticationError.name = 'AuthenticationError';

// Authorization error
const authorizationError = new Error('Authorization error');
authorizationError.name = 'AuthorizationError';

// Not found error
const notFoundError = new Error('Resource not found');
notFoundError.name = 'NotFoundError';

// Conflict error
const conflictError = new Error('Resource conflict');
conflictError.name = 'ConflictError';

// Rate limit error
const rateLimitError = new Error('Rate limit exceeded');
rateLimitError.name = 'RateLimitError';

// Internal error
const internalError = new Error('Internal server error');
internalError.name = 'InternalError';

// Database error
const databaseError = new Error('Database error');
databaseError.name = 'DatabaseError';

// External service error
const externalServiceError = new Error('External service error');
externalServiceError.name = 'ExternalServiceError';

// Timeout error
const timeoutError = new Error('Request timeout');
timeoutError.name = 'TimeoutError';

// Circuit breaker error
const circuitBreakerError = new Error('Circuit breaker is open');
circuitBreakerError.name = 'CircuitBreakerError';

// Bulkhead error
const bulkheadError = new Error('Bulkhead limit reached');
bulkheadError.name = 'BulkheadError';
```

## Error Logging

NovaConnect UAC logs all errors using a centralized logging system:

```javascript
/**
 * Log error
 * @param {Error} error - Error object
 * @param {Object} errorType - Error type
 * @param {Object} context - Error context
 */
logError(error, errorType, context) {
  // Create log data
  const logData = {
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      type: errorType.type,
      status: errorType.status,
      details: error.details || null
    },
    context
  };
  
  // Log error based on status code
  if (errorType.status >= 500) {
    logger.error('Server error', logData);
  } else if (errorType.status >= 400) {
    logger.warn('Client error', logData);
  } else {
    logger.info('Error', logData);
  }
}
```

## Best Practices

### Use Async Handler

Always use the `asyncHandler` middleware for async route handlers:

```javascript
router.get('/api/connectors', asyncHandler(async (req, res) => {
  // This route handler can throw errors without crashing the server
}));
```

### Use Custom Error Types

Use custom error types to provide more context for errors:

```javascript
if (!user) {
  const authenticationError = new Error('User not found');
  authenticationError.name = 'AuthenticationError';
  throw authenticationError;
}
```

### Use Error Details

Provide detailed error information for validation errors:

```javascript
if (!name) {
  const validationError = new Error('Validation error');
  validationError.name = 'ValidationError';
  validationError.details = {
    field: 'name',
    message: 'Name is required'
  };
  throw validationError;
}
```

### Use Resilience Patterns

Use resilience patterns to improve the reliability of your API:

```javascript
// Retry handler
router.get('/api/connectors', retryHandler({ retryAfter: 1 }), asyncHandler(async (req, res) => {
  // This route handler will include a Retry-After header if retried
}));

// Circuit breaker
router.get('/api/connectors', circuitBreakerHandler({ resource: 'connectors', failureThreshold: 5, resetTimeout: 30000 }), asyncHandler(async (req, res) => {
  // This route handler will be protected by a circuit breaker
}));

// Timeout
router.get('/api/connectors', timeoutHandler({ timeoutMs: 5000 }), asyncHandler(async (req, res) => {
  // This route handler will timeout after 5 seconds
}));

// Bulkhead
router.get('/api/connectors', bulkheadHandler({ resource: 'connectors', limit: 10 }), asyncHandler(async (req, res) => {
  // This route handler will be limited to 10 concurrent requests
}));
```

### Log Errors

Log all errors with context:

```javascript
try {
  // Do something
} catch (error) {
  logger.error('Error doing something', {
    error,
    user: req.user,
    path: req.path,
    method: req.method
  });
  throw error;
}
```

## Conclusion

NovaConnect UAC's error handling system provides a consistent and reliable way to handle errors in your API. By following the best practices outlined in this document, you can ensure that your API provides clear and helpful error messages to clients while maintaining the reliability and stability of your service.
