/**
 * CSDE ML Test Script
 * 
 * This script tests the ML components of the CSDE engine.
 */

const { CSDEEngine } = require('../index');
const fs = require('fs');
const path = require('path');

// Create output directory if it doesn't exist
const outputDir = path.join(__dirname, 'output');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Sample data for training
const trainingData = [];

// Generate synthetic training data
for (let i = 0; i < 100; i++) {
  // Generate random compliance data
  const complianceScore = Math.random();
  const complianceControls = [];
  
  for (let j = 0; j < 5; j++) {
    const controlId = `C${j + 1}`;
    const status = Math.random() > 0.7 ? 'compliant' : (Math.random() > 0.5 ? 'partial' : 'non-compliant');
    const severity = Math.random() > 0.7 ? 'high' : (Math.random() > 0.5 ? 'medium' : 'low');
    
    complianceControls.push({
      id: controlId,
      name: `Control ${controlId}`,
      description: `Description for control ${controlId}`,
      severity,
      status,
      framework: 'NIST 800-53'
    });
  }
  
  // Generate random GCP data
  const gcpScore = Math.random();
  const gcpServices = [];
  
  for (let j = 0; j < 5; j++) {
    const serviceId = `S${j + 1}`;
    const status = Math.random() > 0.7 ? 'optimal' : (Math.random() > 0.5 ? 'partial' : 'non-optimal');
    const severity = Math.random() > 0.7 ? 'high' : (Math.random() > 0.5 ? 'medium' : 'low');
    
    gcpServices.push({
      id: serviceId,
      name: `Service ${serviceId}`,
      description: `Description for service ${serviceId}`,
      severity,
      status,
      service: `GCP Service ${j + 1}`
    });
  }
  
  // Generate random Cyber-Safety data
  const cyberSafetyScore = Math.random();
  const cyberSafetyControls = [];
  
  for (let j = 0; j < 5; j++) {
    const controlId = `CS${j + 1}`;
    const status = Math.random() > 0.7 ? 'implemented' : (Math.random() > 0.5 ? 'partial' : 'not-implemented');
    const severity = Math.random() > 0.7 ? 'high' : (Math.random() > 0.5 ? 'medium' : 'low');
    
    cyberSafetyControls.push({
      id: controlId,
      name: `Cyber-Safety Control ${controlId}`,
      description: `Description for Cyber-Safety control ${controlId}`,
      severity,
      status,
      pillar: `Pillar ${(j % 12) + 1}`
    });
  }
  
  // Create training sample
  trainingData.push({
    input: {
      complianceData: {
        complianceScore,
        controls: complianceControls
      },
      gcpData: {
        integrationScore: gcpScore,
        services: gcpServices
      },
      cyberSafetyData: {
        safetyScore: cyberSafetyScore,
        controls: cyberSafetyControls
      }
    },
    // Expected output is the CSDE value
    // In a real implementation, this would be calculated using a reference implementation
    // For now, use a simplified formula
    output: {
      csdeValue: (complianceScore * 10) * (gcpScore * 10) * (cyberSafetyScore * 31.42) * 3141.59
    }
  });
}

// Save training data to file
fs.writeFileSync(
  path.join(outputDir, 'training_data.json'),
  JSON.stringify(trainingData, null, 2)
);

console.log(`Generated ${trainingData.length} training samples`);

// Initialize CSDE Engine
const csdeEngine = new CSDEEngine();

// Test ML components with training data
console.log('Testing ML components with training data...');

// Track performance metrics
const metrics = {
  totalSamples: trainingData.length,
  correctPredictions: 0,
  totalError: 0,
  maxError: 0,
  minError: Number.MAX_VALUE,
  startTime: Date.now()
};

// Process each training sample
trainingData.forEach((sample, index) => {
  try {
    // Calculate CSDE
    const result = csdeEngine.calculate(
      sample.input.complianceData,
      sample.input.gcpData,
      sample.input.cyberSafetyData
    );
    
    // Calculate error
    const expectedValue = sample.output.csdeValue;
    const actualValue = result.csdeValue;
    const error = Math.abs(expectedValue - actualValue) / expectedValue;
    
    // Update metrics
    metrics.totalError += error;
    metrics.maxError = Math.max(metrics.maxError, error);
    metrics.minError = Math.min(metrics.minError, error);
    
    // Consider a prediction correct if error is less than 10%
    if (error < 0.1) {
      metrics.correctPredictions++;
    }
    
    // Log progress
    if ((index + 1) % 10 === 0) {
      console.log(`Processed ${index + 1}/${trainingData.length} samples`);
    }
  } catch (error) {
    console.error(`Error processing sample ${index}:`, error);
  }
});

// Calculate final metrics
metrics.accuracy = metrics.correctPredictions / metrics.totalSamples;
metrics.averageError = metrics.totalError / metrics.totalSamples;
metrics.processingTime = Date.now() - metrics.startTime;
metrics.samplesPerSecond = metrics.totalSamples / (metrics.processingTime / 1000);

// Save metrics to file
fs.writeFileSync(
  path.join(outputDir, 'ml_metrics.json'),
  JSON.stringify(metrics, null, 2)
);

// Display results
console.log('\nML Test Results:');
console.log('----------------');
console.log(`Total Samples: ${metrics.totalSamples}`);
console.log(`Correct Predictions: ${metrics.correctPredictions}`);
console.log(`Accuracy: ${(metrics.accuracy * 100).toFixed(2)}%`);
console.log(`Average Error: ${(metrics.averageError * 100).toFixed(2)}%`);
console.log(`Max Error: ${(metrics.maxError * 100).toFixed(2)}%`);
console.log(`Min Error: ${(metrics.minError * 100).toFixed(2)}%`);
console.log(`Processing Time: ${metrics.processingTime} ms`);
console.log(`Samples Per Second: ${metrics.samplesPerSecond.toFixed(2)}`);

console.log('\nTest completed successfully!');
console.log(`Results saved to ${outputDir}`);

// Export metrics for use in other scripts
module.exports = {
  metrics,
  trainingData
};
