# Script to organize MD files into logical categories
$baseDir = "d:\novafuse-api-superstore\coherence-reality-systems"

# Create category directories
$categories = @{
    "Architecture" = "architecture"
    "Documentation" = "documentation"
    "Technical" = "technical"
    "Research" = "research"
    "Patents" = "patents"
    "Strategic" = "strategic"
    "Marketing" = "marketing"
    "Testing" = "testing"
}

# Create directories if they don't exist
foreach ($category in $categories.Values) {
    $path = Join-Path $baseDir $category
    if (-not (Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force
    }
}

# Get all MD files
$mdFiles = Get-ChildItem -Path $baseDir -Recurse -File -Include *.md

# Create a log file
$logFile = Join-Path $baseDir "organization-log.txt"
"Organizing MD files into categories" | Out-File -FilePath $logFile

# Function to categorize files based on content
function Get-Category {
    param($fileName)
    
    if ($fileName -like "*ARCHITECTURE*" -or $fileName -like "*architecture*") {
        return "Architecture"
    }
    elseif ($fileName -like "*documentation*" -or $fileName -like "*guide.md" -or $fileName -like "*spec.md") {
        return "Documentation"
    }
    elseif ($fileName -like "*technical*" -or $fileName -like "*implementation*" -or $fileName -like "*analysis*") {
        return "Technical"
    }
    elseif ($fileName -like "*research*" -or $fileName -like "*study*" -or $fileName -like "*results*") {
        return "Research"
    }
    elseif ($fileName -like "*patent*" -or $fileName -like "*treatise*") {
        return "Patents"
    }
    elseif ($fileName -like "*strategy*" -or $fileName -like "*plan*" -or $fileName -like "*framework*") {
        return "Strategic"
    }
    elseif ($fileName -like "*marketing*" -or $fileName -like "*briefing*" -or $fileName -like "*demo*") {
        return "Marketing"
    }
    elseif ($fileName -like "*test*" -or $fileName -like "*validation*") {
        return "Testing"
    }
    else {
        return "Documentation"
    }
}

# Move files to appropriate categories
foreach ($file in $mdFiles) {
    if ($file.DirectoryName -eq $baseDir) {  # Only move files from root
        $category = Get-Category $file.Name
        $targetDir = Join-Path $baseDir $categories[$category]
        $targetPath = Join-Path $targetDir $file.Name
        
        Move-Item -Path $file.FullName -Destination $targetPath -Force
        "$($file.Name) moved to $($categories[$category])" | Out-File -FilePath $logFile -Append
    }
}

"Organization completed" | Out-File -FilePath $logFile -Append
