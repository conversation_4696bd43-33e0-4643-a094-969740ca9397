"""
Monitoring Manager for the Universal Vendor Risk Management System.

This module provides functionality for monitoring vendor risks.
"""

import os
import json
import logging
import uuid
import threading
import time
from typing import Dict, List, Any, Optional, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MonitoringManager:
    """
    Manager for monitoring vendor risks.
    
    This class is responsible for setting up and managing continuous monitoring
    of vendor risks.
    """
    
    def __init__(self, monitoring_dir: Optional[str] = None):
        """
        Initialize the Monitoring Manager.
        
        Args:
            monitoring_dir: Path to a directory for storing monitoring information
        """
        logger.info("Initializing Monitoring Manager")
        
        # Set the monitoring directory
        self.monitoring_dir = monitoring_dir or os.path.join(os.getcwd(), 'monitoring_data')
        
        # Create the monitoring directory if it doesn't exist
        os.makedirs(self.monitoring_dir, exist_ok=True)
        
        # Dictionary to store monitoring configurations in memory
        self.monitoring_configs: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store monitoring results in memory
        self.monitoring_results: Dict[str, List[Dict[str, Any]]] = {}
        
        # Dictionary to store monitoring handlers
        self.monitoring_handlers: Dict[str, Callable] = {}
        
        # Dictionary to store alert handlers
        self.alert_handlers: Dict[str, List[Callable]] = {}
        
        # Dictionary to store monitoring threads
        self.monitoring_threads: Dict[str, threading.Thread] = {}
        
        # Dictionary to store monitoring status
        self.monitoring_status: Dict[str, bool] = {}
        
        # Load monitoring configurations from disk
        self._load_monitoring_configs_from_disk()
        
        # Register default monitoring handlers
        self._register_default_monitoring_handlers()
        
        logger.info(f"Monitoring Manager initialized with {len(self.monitoring_configs)} configurations")
    
    def _register_default_monitoring_handlers(self) -> None:
        """Register default monitoring handlers."""
        # Security monitoring handler
        self.register_monitoring_handler('security_monitoring', self._security_monitoring_handler)
        
        # Financial monitoring handler
        self.register_monitoring_handler('financial_monitoring', self._financial_monitoring_handler)
        
        # Compliance monitoring handler
        self.register_monitoring_handler('compliance_monitoring', self._compliance_monitoring_handler)
        
        # Operational monitoring handler
        self.register_monitoring_handler('operational_monitoring', self._operational_monitoring_handler)
    
    def register_monitoring_handler(self, handler_id: str, handler_func: Callable) -> None:
        """
        Register a monitoring handler.
        
        Args:
            handler_id: The ID of the handler
            handler_func: The handler function
        """
        self.monitoring_handlers[handler_id] = handler_func
        logger.info(f"Registered monitoring handler: {handler_id}")
    
    def register_alert_handler(self, alert_type: str, handler_func: Callable) -> None:
        """
        Register an alert handler.
        
        Args:
            alert_type: The type of alert
            handler_func: The handler function
        """
        if alert_type not in self.alert_handlers:
            self.alert_handlers[alert_type] = []
        
        self.alert_handlers[alert_type].append(handler_func)
        logger.info(f"Registered alert handler for type: {alert_type}")
    
    def create_monitoring_config(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new monitoring configuration.
        
        Args:
            config_data: The configuration data
            
        Returns:
            The created monitoring configuration
            
        Raises:
            ValueError: If the configuration data is invalid
        """
        logger.info("Creating new monitoring configuration")
        
        # Validate the configuration data
        self._validate_monitoring_config(config_data)
        
        # Generate a unique configuration ID
        config_id = str(uuid.uuid4())
        
        # Create the configuration object
        config = {
            'id': config_id,
            'vendor_id': config_data['vendor_id'],
            'name': config_data['name'],
            'description': config_data.get('description', ''),
            'handler_id': config_data['handler_id'],
            'parameters': config_data.get('parameters', {}),
            'frequency': config_data.get('frequency', 'daily'),
            'alert_threshold': config_data.get('alert_threshold', 'medium'),
            'status': config_data.get('status', 'active'),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the configuration in memory
        self.monitoring_configs[config_id] = config
        
        # Initialize the results list
        self.monitoring_results[config_id] = []
        
        # Store the configuration on disk
        self._save_monitoring_config_to_disk(config)
        
        logger.info(f"Monitoring configuration created: {config_id}")
        
        return config
    
    def get_monitoring_config(self, config_id: str) -> Dict[str, Any]:
        """
        Get a monitoring configuration.
        
        Args:
            config_id: The ID of the configuration
            
        Returns:
            The monitoring configuration
            
        Raises:
            ValueError: If the configuration does not exist
        """
        logger.info(f"Getting monitoring configuration: {config_id}")
        
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        return self.monitoring_configs[config_id]
    
    def update_monitoring_config(self, config_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a monitoring configuration.
        
        Args:
            config_id: The ID of the configuration
            config_data: The updated configuration data
            
        Returns:
            The updated monitoring configuration
            
        Raises:
            ValueError: If the configuration does not exist
            ValueError: If the configuration data is invalid
        """
        logger.info(f"Updating monitoring configuration: {config_id}")
        
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        # Get the existing configuration
        config = self.monitoring_configs[config_id]
        
        # Update the configuration data
        if 'name' in config_data:
            config['name'] = config_data['name']
        
        if 'description' in config_data:
            config['description'] = config_data['description']
        
        if 'handler_id' in config_data:
            # Validate the handler ID
            if config_data['handler_id'] not in self.monitoring_handlers:
                raise ValueError(f"Invalid handler ID: {config_data['handler_id']}")
            
            config['handler_id'] = config_data['handler_id']
        
        if 'parameters' in config_data:
            config['parameters'] = config_data['parameters']
        
        if 'frequency' in config_data:
            config['frequency'] = config_data['frequency']
        
        if 'alert_threshold' in config_data:
            config['alert_threshold'] = config_data['alert_threshold']
        
        if 'status' in config_data:
            config['status'] = config_data['status']
        
        # Update the updated_at timestamp
        config['updated_at'] = self._get_current_timestamp()
        
        # Store the updated configuration on disk
        self._save_monitoring_config_to_disk(config)
        
        logger.info(f"Monitoring configuration updated: {config_id}")
        
        return config
    
    def delete_monitoring_config(self, config_id: str) -> None:
        """
        Delete a monitoring configuration.
        
        Args:
            config_id: The ID of the configuration
            
        Raises:
            ValueError: If the configuration does not exist
        """
        logger.info(f"Deleting monitoring configuration: {config_id}")
        
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        # Stop monitoring if it's running
        if self.is_monitoring_active(config_id):
            self.stop_monitoring(config_id)
        
        # Remove the configuration from memory
        del self.monitoring_configs[config_id]
        
        # Remove the results from memory
        if config_id in self.monitoring_results:
            del self.monitoring_results[config_id]
        
        # Remove the configuration from disk
        self._delete_monitoring_config_from_disk(config_id)
        
        logger.info(f"Monitoring configuration deleted: {config_id}")
    
    def get_vendor_monitoring_configs(self, vendor_id: str) -> List[Dict[str, Any]]:
        """
        Get all monitoring configurations for a vendor.
        
        Args:
            vendor_id: The ID of the vendor
            
        Returns:
            List of monitoring configurations for the vendor
        """
        logger.info(f"Getting monitoring configurations for vendor: {vendor_id}")
        
        return [c for c in self.monitoring_configs.values() if c['vendor_id'] == vendor_id]
    
    def start_monitoring(self, config_id: str) -> None:
        """
        Start monitoring for a configuration.
        
        Args:
            config_id: The ID of the configuration
            
        Raises:
            ValueError: If the configuration does not exist
            ValueError: If monitoring is already active
        """
        logger.info(f"Starting monitoring for configuration: {config_id}")
        
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        # Check if monitoring is already active
        if self.is_monitoring_active(config_id):
            raise ValueError(f"Monitoring is already active for configuration: {config_id}")
        
        # Get the configuration
        config = self.monitoring_configs[config_id]
        
        # Check if the handler exists
        handler_id = config['handler_id']
        if handler_id not in self.monitoring_handlers:
            raise ValueError(f"Monitoring handler not found: {handler_id}")
        
        # Set the monitoring status
        self.monitoring_status[config_id] = True
        
        # Create and start the monitoring thread
        thread = threading.Thread(target=self._monitoring_thread, args=(config_id,))
        thread.daemon = True
        thread.start()
        
        # Store the thread
        self.monitoring_threads[config_id] = thread
        
        logger.info(f"Monitoring started for configuration: {config_id}")
    
    def stop_monitoring(self, config_id: str) -> None:
        """
        Stop monitoring for a configuration.
        
        Args:
            config_id: The ID of the configuration
            
        Raises:
            ValueError: If the configuration does not exist
            ValueError: If monitoring is not active
        """
        logger.info(f"Stopping monitoring for configuration: {config_id}")
        
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        # Check if monitoring is active
        if not self.is_monitoring_active(config_id):
            raise ValueError(f"Monitoring is not active for configuration: {config_id}")
        
        # Set the monitoring status
        self.monitoring_status[config_id] = False
        
        # Wait for the thread to terminate
        if config_id in self.monitoring_threads:
            self.monitoring_threads[config_id].join(timeout=5.0)
            del self.monitoring_threads[config_id]
        
        logger.info(f"Monitoring stopped for configuration: {config_id}")
    
    def is_monitoring_active(self, config_id: str) -> bool:
        """
        Check if monitoring is active for a configuration.
        
        Args:
            config_id: The ID of the configuration
            
        Returns:
            True if monitoring is active, False otherwise
            
        Raises:
            ValueError: If the configuration does not exist
        """
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        return config_id in self.monitoring_status and self.monitoring_status[config_id]
    
    def get_monitoring_results(self, config_id: str) -> List[Dict[str, Any]]:
        """
        Get monitoring results for a configuration.
        
        Args:
            config_id: The ID of the configuration
            
        Returns:
            List of monitoring results
            
        Raises:
            ValueError: If the configuration does not exist
        """
        logger.info(f"Getting monitoring results for configuration: {config_id}")
        
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        # Get the results
        if config_id not in self.monitoring_results:
            self.monitoring_results[config_id] = []
        
        return self.monitoring_results[config_id]
    
    def add_monitoring_result(self, config_id: str, result: Dict[str, Any]) -> None:
        """
        Add a monitoring result for a configuration.
        
        Args:
            config_id: The ID of the configuration
            result: The monitoring result
            
        Raises:
            ValueError: If the configuration does not exist
        """
        logger.info(f"Adding monitoring result for configuration: {config_id}")
        
        # Check if the configuration exists
        if config_id not in self.monitoring_configs:
            raise ValueError(f"Monitoring configuration not found: {config_id}")
        
        # Get the configuration
        config = self.monitoring_configs[config_id]
        
        # Add the result
        if config_id not in self.monitoring_results:
            self.monitoring_results[config_id] = []
        
        # Add timestamp if not present
        if 'timestamp' not in result:
            result['timestamp'] = self._get_current_timestamp()
        
        self.monitoring_results[config_id].append(result)
        
        # Check if the result should trigger an alert
        if self._should_trigger_alert(config, result):
            self._trigger_alert(config, result)
        
        logger.info(f"Monitoring result added for configuration: {config_id}")
    
    def _monitoring_thread(self, config_id: str) -> None:
        """
        Monitoring thread for a configuration.
        
        Args:
            config_id: The ID of the configuration
        """
        logger.info(f"Monitoring thread started for configuration: {config_id}")
        
        # Get the configuration
        config = self.monitoring_configs[config_id]
        
        # Get the handler
        handler_id = config['handler_id']
        handler = self.monitoring_handlers[handler_id]
        
        # Calculate the sleep interval based on the frequency
        interval = self._calculate_monitoring_interval(config['frequency'])
        
        # Run the monitoring loop
        while self.monitoring_status.get(config_id, False):
            try:
                # Execute the monitoring handler
                result = handler(config)
                
                # Add the result
                self.add_monitoring_result(config_id, result)
                
                # Sleep for the interval
                time.sleep(interval)
            
            except Exception as e:
                logger.error(f"Error in monitoring thread for configuration {config_id}: {e}")
                
                # Sleep for a short time before retrying
                time.sleep(5.0)
        
        logger.info(f"Monitoring thread stopped for configuration: {config_id}")
    
    def _calculate_monitoring_interval(self, frequency: str) -> float:
        """
        Calculate the monitoring interval based on the frequency.
        
        Args:
            frequency: The monitoring frequency
            
        Returns:
            The monitoring interval in seconds
        """
        if frequency == 'hourly':
            return 3600.0  # 1 hour
        elif frequency == 'daily':
            return 86400.0  # 24 hours
        elif frequency == 'weekly':
            return 604800.0  # 7 days
        elif frequency == 'monthly':
            return 2592000.0  # 30 days
        else:
            return 86400.0  # Default to daily
    
    def _should_trigger_alert(self, config: Dict[str, Any], result: Dict[str, Any]) -> bool:
        """
        Check if a monitoring result should trigger an alert.
        
        Args:
            config: The monitoring configuration
            result: The monitoring result
            
        Returns:
            True if an alert should be triggered, False otherwise
        """
        # Get the alert threshold
        threshold = config['alert_threshold']
        
        # Get the result severity
        severity = result.get('severity', 'low')
        
        # Check if the severity is at or above the threshold
        if threshold == 'low':
            return severity in ['low', 'medium', 'high']
        elif threshold == 'medium':
            return severity in ['medium', 'high']
        elif threshold == 'high':
            return severity == 'high'
        else:
            return False
    
    def _trigger_alert(self, config: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        Trigger an alert for a monitoring result.
        
        Args:
            config: The monitoring configuration
            result: The monitoring result
        """
        logger.info(f"Triggering alert for configuration: {config['id']}")
        
        # Create the alert
        alert = {
            'id': str(uuid.uuid4()),
            'config_id': config['id'],
            'vendor_id': config['vendor_id'],
            'result': result,
            'timestamp': self._get_current_timestamp()
        }
        
        # Get the alert type
        alert_type = result.get('type', 'general')
        
        # Call the alert handlers
        if alert_type in self.alert_handlers:
            for handler in self.alert_handlers[alert_type]:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"Error in alert handler for type {alert_type}: {e}")
        
        # Call the general alert handlers
        if 'general' in self.alert_handlers:
            for handler in self.alert_handlers['general']:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"Error in general alert handler: {e}")
        
        logger.info(f"Alert triggered for configuration: {config['id']}")
    
    def _validate_monitoring_config(self, config_data: Dict[str, Any]) -> None:
        """
        Validate monitoring configuration data.
        
        Args:
            config_data: The configuration data to validate
            
        Raises:
            ValueError: If the configuration data is invalid
        """
        # Check required fields
        if 'vendor_id' not in config_data:
            raise ValueError("Vendor ID is required")
        
        if 'name' not in config_data:
            raise ValueError("Configuration name is required")
        
        if 'handler_id' not in config_data:
            raise ValueError("Handler ID is required")
        
        # Validate handler ID
        if config_data['handler_id'] not in self.monitoring_handlers:
            raise ValueError(f"Invalid handler ID: {config_data['handler_id']}")
        
        # Validate frequency if provided
        if 'frequency' in config_data:
            valid_frequencies = ['hourly', 'daily', 'weekly', 'monthly']
            if config_data['frequency'] not in valid_frequencies:
                raise ValueError(f"Invalid frequency: {config_data['frequency']}")
        
        # Validate alert threshold if provided
        if 'alert_threshold' in config_data:
            valid_thresholds = ['low', 'medium', 'high']
            if config_data['alert_threshold'] not in valid_thresholds:
                raise ValueError(f"Invalid alert threshold: {config_data['alert_threshold']}")
        
        # Validate status if provided
        if 'status' in config_data:
            valid_statuses = ['active', 'inactive', 'suspended']
            if config_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid status: {config_data['status']}")
    
    def _load_monitoring_configs_from_disk(self) -> None:
        """Load monitoring configurations from disk."""
        try:
            # Get all JSON files in the monitoring directory
            config_files = [f for f in os.listdir(self.monitoring_dir) if f.endswith('.json')]
            
            for config_file in config_files:
                try:
                    # Load the configuration from disk
                    file_path = os.path.join(self.monitoring_dir, config_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    # Store the configuration in memory
                    config_id = config.get('id')
                    
                    if config_id:
                        self.monitoring_configs[config_id] = config
                        logger.info(f"Loaded monitoring configuration from disk: {config_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load monitoring configuration from {config_file}: {e}")
            
            logger.info(f"Loaded {len(self.monitoring_configs)} monitoring configurations from disk")
        
        except Exception as e:
            logger.error(f"Failed to load monitoring configurations from disk: {e}")
    
    def _save_monitoring_config_to_disk(self, config: Dict[str, Any]) -> None:
        """
        Save a monitoring configuration to disk.
        
        Args:
            config: The configuration to save
        """
        try:
            # Get the configuration ID
            config_id = config.get('id')
            
            if not config_id:
                raise ValueError("Configuration ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.monitoring_dir, f"{config_id}.json")
            
            # Save the configuration to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Saved monitoring configuration to disk: {config_id}")
        
        except Exception as e:
            logger.error(f"Failed to save monitoring configuration to disk: {e}")
    
    def _delete_monitoring_config_from_disk(self, config_id: str) -> None:
        """
        Delete a monitoring configuration from disk.
        
        Args:
            config_id: The ID of the configuration
        """
        try:
            # Create the file path
            file_path = os.path.join(self.monitoring_dir, f"{config_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted monitoring configuration from disk: {config_id}")
            else:
                logger.warning(f"Monitoring configuration file not found on disk: {config_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete monitoring configuration from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
    
    # Default monitoring handlers
    
    def _security_monitoring_handler(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Security monitoring handler.
        
        Args:
            config: The monitoring configuration
            
        Returns:
            The monitoring result
        """
        logger.info(f"Executing security monitoring for configuration: {config['id']}")
        
        # In a real implementation, this would perform actual security monitoring
        # For now, return a placeholder result
        return {
            'type': 'security',
            'severity': 'medium',
            'findings': [
                {
                    'id': 'SEC-001',
                    'description': 'Vendor security certificate is expiring soon',
                    'details': 'The SSL certificate will expire in 30 days'
                }
            ],
            'timestamp': self._get_current_timestamp()
        }
    
    def _financial_monitoring_handler(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Financial monitoring handler.
        
        Args:
            config: The monitoring configuration
            
        Returns:
            The monitoring result
        """
        logger.info(f"Executing financial monitoring for configuration: {config['id']}")
        
        # In a real implementation, this would perform actual financial monitoring
        # For now, return a placeholder result
        return {
            'type': 'financial',
            'severity': 'low',
            'findings': [
                {
                    'id': 'FIN-001',
                    'description': 'Vendor financial status is stable',
                    'details': 'No significant changes in financial indicators'
                }
            ],
            'timestamp': self._get_current_timestamp()
        }
    
    def _compliance_monitoring_handler(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compliance monitoring handler.
        
        Args:
            config: The monitoring configuration
            
        Returns:
            The monitoring result
        """
        logger.info(f"Executing compliance monitoring for configuration: {config['id']}")
        
        # In a real implementation, this would perform actual compliance monitoring
        # For now, return a placeholder result
        return {
            'type': 'compliance',
            'severity': 'high',
            'findings': [
                {
                    'id': 'COMP-001',
                    'description': 'Vendor compliance certification has expired',
                    'details': 'The SOC 2 certification expired 10 days ago'
                }
            ],
            'timestamp': self._get_current_timestamp()
        }
    
    def _operational_monitoring_handler(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Operational monitoring handler.
        
        Args:
            config: The monitoring configuration
            
        Returns:
            The monitoring result
        """
        logger.info(f"Executing operational monitoring for configuration: {config['id']}")
        
        # In a real implementation, this would perform actual operational monitoring
        # For now, return a placeholder result
        return {
            'type': 'operational',
            'severity': 'low',
            'findings': [
                {
                    'id': 'OPS-001',
                    'description': 'Vendor service availability is normal',
                    'details': '99.9% uptime in the last 30 days'
                }
            ],
            'timestamp': self._get_current_timestamp()
        }
