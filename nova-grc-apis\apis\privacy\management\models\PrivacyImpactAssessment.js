/**
 * Privacy Impact Assessment Model
 * 
 * Represents a privacy impact assessment (PIA) for evaluating
 * the privacy risks associated with data processing activities.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const privacyImpactAssessmentSchema = new Schema({
  reference: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  processingActivity: {
    type: Schema.Types.ObjectId,
    ref: 'ProcessingActivity',
    required: true
  },
  status: {
    type: String,
    enum: ['Draft', 'In Progress', 'Completed', 'Approved', 'Rejected', 'Review Required'],
    default: 'Draft'
  },
  initiatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assessmentTeam: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    role: String
  }],
  dpoConsulted: {
    type: Boolean,
    default: false
  },
  dpoComments: String,
  startDate: {
    type: Date,
    default: Date.now
  },
  completionDate: Date,
  nextReviewDate: Date,
  systemsInvolved: [{
    name: String,
    description: String,
    dataFlows: String
  }],
  necessityAndProportionality: {
    necessity: {
      description: String,
      alternatives: String,
      justification: String
    },
    proportionality: {
      description: String,
      dataMinimization: String,
      justification: String
    }
  },
  consultations: [{
    stakeholderType: {
      type: String,
      enum: ['Data Subjects', 'Internal Teams', 'External Experts', 'Supervisory Authority', 'Other']
    },
    details: String,
    date: Date,
    outcome: String,
    documents: [{
      name: String,
      url: String
    }]
  }],
  risks: [{
    description: {
      type: String,
      required: true
    },
    category: {
      type: String,
      enum: ['Confidentiality', 'Integrity', 'Availability', 'Rights and Freedoms']
    },
    likelihood: {
      type: String,
      enum: ['Low', 'Medium', 'High'],
      required: true
    },
    impact: {
      type: String,
      enum: ['Low', 'Medium', 'High'],
      required: true
    },
    riskLevel: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Critical'],
      required: true
    },
    existingControls: [String],
    proposedMitigations: [String],
    residualRisk: {
      likelihood: {
        type: String,
        enum: ['Low', 'Medium', 'High']
      },
      impact: {
        type: String,
        enum: ['Low', 'Medium', 'High']
      },
      riskLevel: {
        type: String,
        enum: ['Low', 'Medium', 'High', 'Critical']
      }
    },
    owner: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['Identified', 'Assessed', 'Mitigated', 'Accepted', 'Transferred'],
      default: 'Identified'
    }
  }],
  mitigationPlan: [{
    action: String,
    description: String,
    owner: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    dueDate: Date,
    status: {
      type: String,
      enum: ['Planned', 'In Progress', 'Completed', 'Delayed', 'Cancelled'],
      default: 'Planned'
    },
    completionDate: Date,
    notes: String
  }],
  conclusion: {
    summary: String,
    recommendation: {
      type: String,
      enum: ['Proceed', 'Proceed with Modifications', 'Do Not Proceed', 'Consult Supervisory Authority']
    },
    justification: String,
    conditions: [String]
  },
  approvals: [{
    role: String,
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    decision: {
      type: String,
      enum: ['Approved', 'Rejected', 'Pending'],
      default: 'Pending'
    },
    date: Date,
    comments: String
  }],
  documents: [{
    name: String,
    description: String,
    url: String,
    uploadDate: {
      type: Date,
      default: Date.now
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  history: [{
    action: String,
    date: {
      type: Date,
      default: Date.now
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    details: String
  }],
  supervisoryAuthorityConsultation: {
    required: Boolean,
    initiated: Boolean,
    date: Date,
    reference: String,
    outcome: String,
    documents: [{
      name: String,
      url: String
    }]
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add text index for search functionality
privacyImpactAssessmentSchema.index({
  reference: 'text',
  name: 'text',
  description: 'text'
});

// Method to calculate overall risk level
privacyImpactAssessmentSchema.methods.calculateOverallRiskLevel = function() {
  if (!this.risks || this.risks.length === 0) return 'Unknown';
  
  const riskLevels = this.risks.map(risk => risk.riskLevel);
  
  if (riskLevels.includes('Critical')) return 'Critical';
  if (riskLevels.includes('High')) return 'High';
  if (riskLevels.includes('Medium')) return 'Medium';
  return 'Low';
};

// Method to check if supervisory authority consultation is required
privacyImpactAssessmentSchema.methods.isSupervisoryConsultationRequired = function() {
  // Consultation is required if there are any high or critical risks that cannot be mitigated
  const highRisks = this.risks.filter(risk => 
    (risk.riskLevel === 'High' || risk.riskLevel === 'Critical') && 
    (!risk.residualRisk || risk.residualRisk.riskLevel === 'High' || risk.residualRisk.riskLevel === 'Critical')
  );
  
  return highRisks.length > 0;
};

// Method to add a history entry
privacyImpactAssessmentSchema.methods.addHistoryEntry = function(action, user, details) {
  this.history.push({
    action,
    date: new Date(),
    user,
    details
  });
};

const PrivacyImpactAssessment = mongoose.model('PrivacyImpactAssessment', privacyImpactAssessmentSchema);

module.exports = PrivacyImpactAssessment;
