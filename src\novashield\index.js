/**
 * NovaShield - Universal Vendor Risk Management (NUVR)
 * 
 * This module provides vendor risk management capabilities with active defense
 * and threat intelligence integration.
 */

const { createLogger } = require('../utils/logger');
const riskAssessmentService = require('./services/risk-assessment-service');
const vendorManagementService = require('./services/vendor-management-service');
const threatIntelligenceService = require('./services/threat-intelligence-service');

const logger = createLogger('novashield');

/**
 * NovaShield class for vendor risk management
 */
class NovaShield {
  constructor(options = {}) {
    this.options = {
      riskThreshold: options.riskThreshold || 75,
      autoRemediation: options.autoRemediation || false,
      threatIntelligenceEnabled: options.threatIntelligenceEnabled !== false,
      ...options
    };

    this.riskAssessmentService = riskAssessmentService;
    this.vendorManagementService = vendorManagementService;
    this.threatIntelligenceService = threatIntelligenceService;

    logger.info('NovaShield initialized', {
      riskThreshold: this.options.riskThreshold,
      autoRemediation: this.options.autoRemediation,
      threatIntelligenceEnabled: this.options.threatIntelligenceEnabled
    });
  }

  /**
   * Assess vendor risk
   * 
   * @param {string} vendorId - Vendor ID
   * @param {Object} options - Assessment options
   * @returns {Promise<Object>} - Risk assessment result
   */
  async assessVendorRisk(vendorId, options = {}) {
    logger.info('Assessing vendor risk', { vendorId });
    
    try {
      // Get vendor details
      const vendor = await this.vendorManagementService.getVendor(vendorId);
      
      // Get threat intelligence if enabled
      let threatIntelligence = null;
      if (this.options.threatIntelligenceEnabled) {
        threatIntelligence = await this.threatIntelligenceService.getVendorThreats(vendorId);
      }
      
      // Perform risk assessment
      const assessment = await this.riskAssessmentService.assessVendor(vendor, {
        threatIntelligence,
        ...options
      });
      
      // Check if risk exceeds threshold
      if (assessment.riskScore > this.options.riskThreshold) {
        logger.warn('Vendor risk exceeds threshold', {
          vendorId,
          riskScore: assessment.riskScore,
          threshold: this.options.riskThreshold
        });
        
        // Auto-remediate if enabled
        if (this.options.autoRemediation) {
          await this.remediateVendorRisk(vendorId, assessment);
        }
      }
      
      return assessment;
    } catch (error) {
      logger.error('Error assessing vendor risk', {
        vendorId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Remediate vendor risk
   * 
   * @param {string} vendorId - Vendor ID
   * @param {Object} assessment - Risk assessment
   * @returns {Promise<Object>} - Remediation result
   */
  async remediateVendorRisk(vendorId, assessment) {
    logger.info('Remediating vendor risk', { vendorId });
    
    try {
      // Generate remediation plan
      const remediationPlan = await this.riskAssessmentService.generateRemediationPlan(assessment);
      
      // Apply remediation actions
      const result = await this.riskAssessmentService.applyRemediationPlan(vendorId, remediationPlan);
      
      return result;
    } catch (error) {
      logger.error('Error remediating vendor risk', {
        vendorId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get vendor risk dashboard
   * 
   * @returns {Promise<Object>} - Risk dashboard data
   */
  async getVendorRiskDashboard() {
    logger.info('Getting vendor risk dashboard');
    
    try {
      // Get all vendors
      const vendors = await this.vendorManagementService.getAllVendors();
      
      // Get risk assessments for all vendors
      const assessments = await Promise.all(
        vendors.map(vendor => this.riskAssessmentService.getLatestAssessment(vendor.id))
      );
      
      // Calculate dashboard metrics
      const highRiskCount = assessments.filter(a => a && a.riskScore > 75).length;
      const mediumRiskCount = assessments.filter(a => a && a.riskScore > 50 && a.riskScore <= 75).length;
      const lowRiskCount = assessments.filter(a => a && a.riskScore <= 50).length;
      
      // Get threat intelligence summary if enabled
      let threatSummary = null;
      if (this.options.threatIntelligenceEnabled) {
        threatSummary = await this.threatIntelligenceService.getThreatSummary();
      }
      
      return {
        vendorCount: vendors.length,
        assessmentCount: assessments.filter(a => a).length,
        riskDistribution: {
          highRisk: highRiskCount,
          mediumRisk: mediumRiskCount,
          lowRisk: lowRiskCount
        },
        averageRiskScore: assessments.reduce((sum, a) => sum + (a ? a.riskScore : 0), 0) / 
                          assessments.filter(a => a).length,
        threatSummary
      };
    } catch (error) {
      logger.error('Error getting vendor risk dashboard', {
        error: error.message
      });
      throw error;
    }
  }
}

// Create singleton instance
const novaShield = new NovaShield();

module.exports = {
  NovaShield,
  novaShield,
  riskAssessmentService,
  vendorManagementService,
  threatIntelligenceService
};
