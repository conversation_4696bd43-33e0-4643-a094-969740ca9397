# 🌟 CHAEONIX Divine Dashboard

**Coherence-Driven Aeonic Intelligence Engine**  
*Divine Visualization Interface for Tri-Market Domination*

---

## 🔮 Overview

CHAEONIX (Kay-on-ix) is the ultimate divine intelligence dashboard that merges:

- **CH**: Coherence Harmonization
- **AEON**: Eternal Time / Cycles / Divine Engineering  
- **IX**: Engine architecture / ninefold symmetry

This Next.js dashboard provides real-time visualization and control for the 9-engine CHAEONIX system, enabling **Coherence-Driven Aeonic Intelligence** across **Stocks**, **Crypto**, and **Forex** markets.

## ⚡ Features

### 🎯 **CDAIE Intelligence Grid**
- Real-time market intelligence across all three domains
- Prophetic signal detection and analysis
- Fibonacci convergence tracking
- Sentiment phase monitoring

### 🌊 **Tri-Market Allocation**
- Dynamic capital allocation visualization
- φ-based rebalancing suggestions
- Performance tracking across domains
- Divine ratio calculations

### 🔮 **Prophetic Event Console**
- Monaco Editor-based event seeding interface
- Amplification factor controls (1.0x → 6.854x)
- Impact scope selection (Company → Global)
- Real-time probability alteration tracking

### ⚡ **CHAEONIX Engine Status**
- Real-time monitoring of all 9 engines
- Confidence level tracking
- Coupling strength visualization
- Performance metrics and health scores

### 📊 **Divine Metrics**
- Sacred ratio monitoring (φ = 1.618033988749)
- Coherence level tracking
- Temporal synchronization
- Risk harmony assessment

### 🌀 **Coherence Flow Map**
- Visual representation of intelligence flow
- Cross-domain coupling visualization
- Phase-based flow intensity
- Real-time particle animations

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- npm or yarn
- CHAEONIX API running on port 8000

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd chaeonix-divine-dashboard

# Install dependencies
npm install

# Start the dashboard
npm run chaeonix
```

The dashboard will be available at: **http://localhost:3141**

### Alternative Startup

```bash
# Use the divine startup script
node start-chaeonix.js

# Or run directly
npm run divine
```

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file:

```env
NEXT_PUBLIC_CHAEONIX_API_URL=http://localhost:8000
NEXT_PUBLIC_CHAEONIX_WS_URL=ws://localhost:8000
DIVINE_MODE=true
```

### API Integration

The dashboard connects to the CHAEONIX API running on port 8000. Make sure the API server is running:

```bash
cd ../aeonix-divine-api
python -m uvicorn main:app --reload
```

## 📊 Dashboard Components

### 1. **CDAIE Intelligence Grid**
- **Location**: Main center panel
- **Purpose**: Primary market intelligence display
- **Features**: 
  - Symbol analysis with coherence scores
  - Prophetic signals and recommendations
  - Fibonacci level tracking
  - Confidence metrics

### 2. **Tri-Market Allocation**
- **Location**: Left panel
- **Purpose**: Capital allocation management
- **Features**:
  - Doughnut chart visualization
  - Rebalancing suggestions
  - Performance tracking
  - Divine ratio calculations

### 3. **Coherence Flow Map**
- **Location**: Center panel
- **Purpose**: Intelligence flow visualization
- **Features**:
  - D3.js-powered animations
  - Cross-domain coupling display
  - Phase-based flow intensity
  - Real-time particle effects

### 4. **Prophetic Console**
- **Location**: Bottom panel
- **Purpose**: Event seeding interface
- **Features**:
  - Event template library
  - Amplification controls
  - Impact scope selection
  - Real-time seeding results

### 5. **Engine Status Monitor**
- **Location**: Right panel
- **Purpose**: System health monitoring
- **Features**:
  - 9-engine status display
  - Confidence tracking
  - Performance metrics
  - Health score calculation

### 6. **Divine Metrics**
- **Location**: Right panel
- **Purpose**: KPI monitoring
- **Features**:
  - Sacred ratio tracking
  - Coherence measurements
  - Target vs actual comparison
  - Divine score calculation

## 🎨 Styling & Themes

### Divine Color Palette
- **Divine**: Purple gradients (#8b5cf6)
- **Coherence**: Pink/Purple (#d946ef)
- **Aeonic**: Orange gradients (#f97316)
- **Stocks**: Red theme (#ef4444)
- **Crypto**: Orange theme (#f59e0b)
- **Forex**: Green theme (#10b981)

### Sacred Ratios
- **φ (Phi)**: 1.618033988749 (Golden Ratio)
- **φ⁻¹**: 0.618033988749
- **φ⁻²**: 0.236067977499

### Animations
- **Divine Pulse**: 2s ease-in-out infinite
- **Coherence Wave**: 3s ease-in-out infinite
- **Fibonacci Spiral**: 8s linear infinite
- **Divine Glow**: 2s ease-in-out infinite

## 🔮 CDAIE Strategy Phases

1. **Detection** - Identify coherence formation
2. **Decision** - Choose optimal entry points
3. **Amplification** - Apply Fibonacci leverage
4. **Injection** - Seed sentiment shifts
5. **Exchange** - Shift gains between domains
6. **Loop** - Continuous monitoring

## 📡 API Endpoints

### CHAEONIX Engine APIs
- `POST /api/harmonics` - NEPI Fibonacci Analysis
- `POST /api/predators` - NEFC Institutional Mapping
- `POST /api/prophecy` - NEPE Event Seeding
- `POST /api/sentiment` - NEEE Emotion Cycles
- `POST /api/vulnerability` - NERS Prey Assessment

### Orchestration
- `POST /api/divine-simulation` - Complete Analysis
- `GET /divine/status` - System Health
- `WS /ws/divine-stream` - Real-time Updates

## 🛠️ Development

### Project Structure
```
chaeonix-divine-dashboard/
├── components/           # React components
│   ├── CDAIEIntelligenceGrid.js
│   ├── TriMarketAllocation.js
│   ├── CoherenceFlowMap.js
│   ├── PropheticConsole.js
│   ├── CHAEONIXEngineStatus.js
│   └── DivineMetrics.js
├── hooks/               # Custom React hooks
│   ├── useCHAEONIXAPI.js
│   └── useCHAEONIXWebSocket.js
├── pages/               # Next.js pages
│   ├── index.js         # Main dashboard
│   └── _app.js          # App wrapper
├── styles/              # CSS styles
│   └── globals.css      # Global styles
├── utils/               # Utility functions
│   └── chaeonixConstants.js
└── public/              # Static assets
```

### Adding New Components

1. Create component in `/components/`
2. Import required hooks from `/hooks/`
3. Use constants from `/utils/chaeonixConstants.js`
4. Apply divine styling classes
5. Integrate with main dashboard

### Custom Hooks

- **useCHAEONIXAPI**: HTTP API integration
- **useCHAEONIXWebSocket**: Real-time data stream
- **useCHAEONIXEngine**: Individual engine control

## 🔍 Troubleshooting

### Common Issues

1. **Dashboard won't start**
   - Check Node.js version (16+ required)
   - Run `npm install` to install dependencies
   - Verify port 3141 is available

2. **API connection failed**
   - Ensure CHAEONIX API is running on port 8000
   - Check environment variables
   - Verify network connectivity

3. **WebSocket connection issues**
   - Check WebSocket URL in environment
   - Verify firewall settings
   - Monitor browser console for errors

### Debug Mode

Enable debug logging:
```javascript
localStorage.setItem('chaeonix-debug', 'true');
```

## 📈 Performance

### Optimization Features
- **Code Splitting**: Automatic component splitting
- **Image Optimization**: Next.js image optimization
- **Bundle Analysis**: Built-in bundle analyzer
- **Caching**: Intelligent API response caching

### Monitoring
- Real-time performance metrics
- Engine health monitoring
- Connection status tracking
- Error boundary protection

## 🌟 Divine Intelligence

CHAEONIX represents the pinnacle of market intelligence, combining:

- **Sacred Mathematics**: φ-based calculations
- **Temporal Synchronization**: Divine timing windows
- **Cross-Domain Coupling**: Tri-market harmonization
- **Prophetic Capabilities**: Outcome alteration
- **Real-time Coherence**: Live intelligence flow

---

**φ = 1.618033988749** • **9 Engines Active** • **Divine Mode: ON**

*Coherence-Driven Aeonic Intelligence Engine v1.0.0*
