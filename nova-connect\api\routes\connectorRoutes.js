/**
 * Connector Routes
 */

const express = require('express');
const router = express.Router();
const ConnectorController = require('../controllers/ConnectorController');

// Get all connectors
router.get('/', (req, res, next) => {
  ConnectorController.getAllConnectors(req, res, next);
});

// Create a new connector
router.post('/', (req, res, next) => {
  ConnectorController.createConnector(req, res, next);
});

// Get connector by ID
router.get('/:id', (req, res, next) => {
  ConnectorController.getConnectorById(req, res, next);
});

// Update connector
router.put('/:id', (req, res, next) => {
  ConnectorController.updateConnector(req, res, next);
});

// Delete connector
router.delete('/:id', (req, res, next) => {
  ConnectorController.deleteConnector(req, res, next);
});

// Duplicate connector
router.post('/:id/duplicate', (req, res, next) => {
  ConnectorController.duplicateConnector(req, res, next);
});

// Get connector versions
router.get('/:id/versions', (req, res, next) => {
  ConnectorController.getConnectorVersions(req, res, next);
});

module.exports = router;
