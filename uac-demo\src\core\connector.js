/**
 * Universal API Connector (UAC) Core
 * 
 * This module handles the connection to external APIs and services.
 * It provides a unified interface for making API calls, handling authentication,
 * and transforming data between different formats.
 */

const axios = require('axios');
const { getLogger } = require('./logger');

const logger = getLogger('connector');

// Supported API types
const API_TYPES = {
  REST: 'rest',
  GRAPHQL: 'graphql',
  SOAP: 'soap',
  GRPC: 'grpc',
  WEBHOOK: 'webhook'
};

// Supported authentication methods
const AUTH_METHODS = {
  NONE: 'none',
  API_KEY: 'api_key',
  OAUTH2: 'oauth2',
  JWT: 'jwt',
  BASIC: 'basic'
};

// Supported data formats
const DATA_FORMATS = {
  JSON: 'json',
  XML: 'xml',
  FORM: 'form',
  BINARY: 'binary'
};

/**
 * Initialize the Universal API Connector
 * @returns {Object} The connector instance
 */
function initializeConnector() {
  // In-memory store for API configurations
  const apiConfigurations = {};
  
  // In-memory store for connection pools
  const connectionPools = {};

  /**
   * Register a new API configuration
   * @param {String} apiId Unique identifier for the API
   * @param {Object} config API configuration
   * @returns {Boolean} Success status
   */
  function registerApi(apiId, config) {
    logger.info(`Registering API: ${apiId}`);
    
    // Validate configuration
    if (!config.baseUrl) {
      logger.error(`Invalid configuration for API ${apiId}: Missing baseUrl`);
      return false;
    }
    
    // Store configuration
    apiConfigurations[apiId] = {
      ...config,
      type: config.type || API_TYPES.REST,
      authMethod: config.authMethod || AUTH_METHODS.NONE,
      dataFormat: config.dataFormat || DATA_FORMATS.JSON,
      headers: config.headers || {},
      createdAt: new Date()
    };
    
    logger.info(`API ${apiId} registered successfully`);
    return true;
  }

  /**
   * Get API configuration
   * @param {String} apiId API identifier
   * @returns {Object|null} API configuration or null if not found
   */
  function getApiConfig(apiId) {
    return apiConfigurations[apiId] || null;
  }

  /**
   * List all registered APIs
   * @returns {Array} Array of API configurations
   */
  function listApis() {
    return Object.keys(apiConfigurations).map(apiId => ({
      id: apiId,
      ...apiConfigurations[apiId]
    }));
  }

  /**
   * Make an API call
   * @param {String} apiId API identifier
   * @param {Object} options Request options
   * @returns {Promise} API response
   */
  async function callApi(apiId, options = {}) {
    const apiConfig = getApiConfig(apiId);
    
    if (!apiConfig) {
      logger.error(`API ${apiId} not found`);
      throw new Error(`API ${apiId} not found`);
    }
    
    logger.info(`Calling API: ${apiId}, endpoint: ${options.endpoint || '/'}`);
    
    try {
      // Prepare request configuration
      const requestConfig = {
        method: options.method || 'GET',
        url: `${apiConfig.baseUrl}${options.endpoint || ''}`,
        headers: {
          ...apiConfig.headers,
          ...options.headers
        }
      };
      
      // Add request body if provided
      if (options.data) {
        requestConfig.data = options.data;
      }
      
      // Add query parameters if provided
      if (options.params) {
        requestConfig.params = options.params;
      }
      
      // Make the API call
      const response = await axios(requestConfig);
      
      logger.info(`API call successful: ${apiId}`);
      return {
        status: response.status,
        data: response.data,
        headers: response.headers
      };
    } catch (error) {
      logger.error(`API call failed: ${apiId}`, error);
      throw error;
    }
  }

  /**
   * Apply a transformation to API data
   * @param {Object} data Input data
   * @param {Function} transformer Transformation function
   * @returns {Object} Transformed data
   */
  function transformData(data, transformer) {
    if (typeof transformer !== 'function') {
      return data;
    }
    
    try {
      return transformer(data);
    } catch (error) {
      logger.error('Data transformation failed', error);
      throw error;
    }
  }

  // Return the connector interface
  return {
    registerApi,
    getApiConfig,
    listApis,
    callApi,
    transformData,
    API_TYPES,
    AUTH_METHODS,
    DATA_FORMATS
  };
}

module.exports = {
  initializeConnector
};
