/**
 * Main JavaScript for Quantum Protein Folding Dashboard
 * Handles navigation, WebSocket connections, and global UI updates
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Handle navigation
    setupNavigation();
    
    // Initialize WebSocket connection
    initializeWebSocket();
    
    // Load initial data
    updateDashboard();
});

/**
 * Set up navigation event listeners
 */
function setupNavigation() {
    // Handle sidebar navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Update active state
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // Get the target view ID
            const targetId = this.id.replace('nav-', '');
            loadView(targetId);
        });
    });
}

/**
 * Load a specific view into the main content area
 * @param {string} viewId - The ID of the view to load
 */
async function loadView(viewId) {
    const mainContent = document.querySelector('.main-content');
    
    // Show loading state
    mainContent.innerHTML = `
        <div class="d-flex justify-content-center align-items-center" style="height: 80vh;">
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading ${viewId}...</p>
            </div>
        </div>
    `;
    
    try {
        // In a real app, this would fetch the view content from the server
        // For now, we'll just show a placeholder
        let content = '';
        
        switch(viewId) {
            case 'dashboard':
                content = '<h2>Dashboard View</h2><p>Dashboard content will be loaded here.</p>';
                break;
            case 'qaoa':
                content = '<h2>QAOA Experiments</h2><p>QAOA experiments will be listed here.</p>';
                break;
            case 'vqe':
                content = '<h2>VQE Experiments</h2><p>VQE experiments will be listed here.</p>';
                break;
            case 'qml':
                content = '<h2>Quantum ML Models</h2><p>Quantum ML models will be managed here.</p>';
                break;
            default:
                content = '<h2>Page Not Found</h2><p>The requested page could not be found.</p>';
        }
        
        mainContent.innerHTML = content;
    } catch (error) {
        console.error('Error loading view:', error);
        showAlert('Failed to load the requested view. Please try again.', 'danger');
    }
}

/**
 * Initialize WebSocket connection for real-time updates
 */
function initializeWebSocket() {
    // The Socket.IO client is already included in base.html
    const socket = io();
    
    // Connection established
    socket.on('connect', () => {
        console.log('Connected to WebSocket server');
        updateConnectionStatus(true);
    });
    
    // Connection lost
    socket.on('disconnect', () => {
        console.log('Disconnected from WebSocket server');
        updateConnectionStatus(false);
    });
    
    // Handle experiment updates
    socket.on('experiment_update', (data) => {
        console.log('Experiment update received:', data);
        handleExperimentUpdate(data);
    });
    
    // Handle system status updates
    socket.on('system_status', (data) => {
        updateSystemStatus(data);
    });
    
    // Handle quantum backends update
    socket.on('quantum_backends', (data) => {
        updateQuantumBackends(data);
    });
    
    // Handle errors
    socket.on('error', (error) => {
        console.error('WebSocket error:', error);
        showAlert(`WebSocket error: ${error.message}`, 'danger');
    });
}

/**
 * Update the connection status indicator
 * @param {boolean} isConnected - Whether the connection is active
 */
function updateConnectionStatus(isConnected) {
    const statusElement = document.getElementById('connection-status');
    if (!statusElement) return;
    
    if (isConnected) {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Connected';
        statusElement.className = 'text-success';
    } else {
        statusElement.innerHTML = '<i class="fas fa-circle"></i> Disconnected';
        statusElement.className = 'text-danger';
    }
}

/**
 * Update the dashboard with the latest data
 */
async function updateDashboard() {
    try {
        // Fetch experiments
        const experimentsResponse = await fetch('/api/experiments');
        if (!experimentsResponse.ok) throw new Error('Failed to fetch experiments');
        const experiments = await experimentsResponse.json();
        
        // Update experiment list
        updateExperimentList(experiments);
        
        // Fetch system status
        const statusResponse = await fetch('/api/system/status');
        if (!statusResponse.ok) throw new Error('Failed to fetch system status');
        const status = await statusResponse.json();
        
        // Update system status
        updateSystemStatus(status);
        
        // Fetch quantum backends
        const backendsResponse = await fetch('/api/quantum/backends');
        if (!backendsResponse.ok) throw new Error('Failed to fetch quantum backends');
        const backends = await backendsResponse.json();
        
        // Update quantum backends
        updateQuantumBackends(backends);
        
    } catch (error) {
        console.error('Error updating dashboard:', error);
        showAlert(`Error updating dashboard: ${error.message}`, 'danger');
    }
}

/**
 * Update the experiment list in the UI
 * @param {Array} experiments - Array of experiment objects
 */
function updateExperimentList(experiments) {
    const tbody = document.getElementById('recent-experiments');
    if (!tbody) return;
    
    if (!experiments || experiments.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">No experiments found</td></tr>';
        return;
    }
    
    // Sort by creation date (newest first)
    experiments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    
    // Take only the 5 most recent
    const recentExperiments = experiments.slice(0, 5);
    
    // Update stats
    updateExperimentStats(experiments);
    
    // Update table
    tbody.innerHTML = recentExperiments.map(exp => `
        <tr class="experiment-row" data-id="${exp.id}">
            <td><code>${exp.id.substring(0, 8)}</code></td>
            <td>${exp.name || 'Unnamed Experiment'}</td>
            <td><span class="badge ${getExperimentTypeClass(exp.type)}">${exp.type.toUpperCase()}</span></td>
            <td><span class="badge ${getStatusBadgeClass(exp.status)}">${exp.status}</span></td>
            <td>${formatDate(exp.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-experiment" data-id="${exp.id}">
                    <i class="fas fa-eye"></i> View
                </button>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to view buttons
    document.querySelectorAll('.view-experiment').forEach(button => {
        button.addEventListener('click', (e) => {
            const experimentId = e.target.closest('button').dataset.id;
            viewExperiment(experimentId);
        });
    });
}

/**
 * Update the experiment statistics in the dashboard
 * @param {Array} experiments - Array of experiment objects
 */
function updateExperimentStats(experiments) {
    const total = experiments.length;
    const completed = experiments.filter(e => e.status === 'completed').length;
    const running = experiments.filter(e => e.status === 'running').length;
    const failed = experiments.filter(e => e.status === 'failed').length;
    
    const totalEl = document.getElementById('total-experiments');
    const completedEl = document.getElementById('completed-experiments');
    const runningEl = document.getElementById('running-experiments');
    const failedEl = document.getElementById('failed-experiments');
    
    if (totalEl) totalEl.textContent = total;
    if (completedEl) completedEl.textContent = completed;
    if (runningEl) runningEl.textContent = running;
    if (failedEl) failedEl.textContent = failed;
}

/**
 * Update the system status in the UI
 * @param {Object} status - System status object
 */
function updateSystemStatus(status) {
    // Update CPU usage
    if (status.cpu) {
        const cpuPercent = Math.round(status.cpu.percent);
        const cpuUsageEl = document.getElementById('cpu-usage');
        const cpuProgressEl = document.getElementById('cpu-progress');
        
        if (cpuUsageEl) cpuUsageEl.textContent = `${cpuPercent}%`;
        if (cpuProgressEl) {
            cpuProgressEl.style.width = `${cpuPercent}%`;
            cpuProgressEl.className = `progress-bar ${getProgressBarClass(cpuPercent)}`;
        }
    }
    
    // Update memory usage
    if (status.memory) {
        const memoryPercent = Math.round((status.memory.used / status.memory.total) * 100);
        const memoryUsageEl = document.getElementById('memory-usage');
        const memoryProgressEl = document.getElementById('memory-progress');
        
        if (memoryUsageEl) memoryUsageEl.textContent = `${memoryPercent}%`;
        if (memoryProgressEl) {
            memoryProgressEl.style.width = `${memoryPercent}%`;
            memoryProgressEl.className = `progress-bar ${getProgressBarClass(memoryPercent)}`;
        }
    }
    
    // Update GPU usage if available
    const gpuUsageEl = document.getElementById('gpu-usage');
    const gpuProgressEl = document.getElementById('gpu-progress');
    
    if (status.gpu) {
        const gpuPercent = status.gpu.utilization || 0;
        
        if (gpuUsageEl) gpuUsageEl.textContent = `${gpuPercent}%`;
        if (gpuProgressEl) {
            gpuProgressEl.style.width = `${gpuPercent}%`;
            gpuProgressEl.className = `progress-bar ${getProgressBarClass(gpuPercent)}`;
        }
    } else if (gpuUsageEl && gpuProgressEl) {
        gpuUsageEl.textContent = 'N/A';
        gpuProgressEl.style.width = '0%';
        gpuProgressEl.className = 'progress-bar bg-secondary';
    }
}

/**
 * Update the quantum backends section in the UI
 * @param {Array} backends - Array of quantum backend objects
 */
function updateQuantumBackends(backends) {
    const container = document.getElementById('quantum-backends');
    if (!container) return;
    
    if (!backends || backends.length === 0) {
        container.innerHTML = `
            <div class="alert alert-warning mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                No quantum backends available
            </div>
        `;
        return;
    }
    
    container.innerHTML = `
        <div class="list-group">
            ${backends.map(backend => `
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${backend.name}</h6>
                            <small class="text-muted">${backend.provider} • ${backend.type}</small>
                        </div>
                        <span class="badge ${backend.status === 'online' ? 'bg-success' : 'bg-secondary'}">
                            ${backend.status}
                        </span>
                    </div>
                    ${backend.qubits ? `<small class="text-muted">Qubits: ${backend.qubits}</small>` : ''}
                </div>
            `).join('')}
        </div>
    `;
}

/**
 * Handle experiment update from WebSocket
 * @param {Object} data - Experiment update data
 */
function handleExperimentUpdate(data) {
    // Update the specific experiment in the UI
    const row = document.querySelector(`.experiment-row[data-id="${data.id}"]`);
    
    if (row) {
        // Update the status badge
        const statusBadge = row.querySelector('.badge');
        if (statusBadge) {
            statusBadge.className = `badge ${getStatusBadgeClass(data.status)}`;
            statusBadge.textContent = data.status;
        }
        
        // If the experiment is completed, update the view button
        if (data.status === 'completed') {
            const viewButton = row.querySelector('.view-experiment');
            if (viewButton) {
                viewButton.classList.remove('btn-outline-primary');
                viewButton.classList.add('btn-success');
            }
        }
    }
    
    // Refresh the dashboard to get the latest data
    updateDashboard();
}

/**
 * View details of a specific experiment
 * @param {string} experimentId - ID of the experiment to view
 */
function viewExperiment(experimentId) {
    // In a real app, this would navigate to a detailed view of the experiment
    console.log('Viewing experiment:', experimentId);
    
    // For now, show a modal with the experiment ID
    const modalHtml = `
        <div class="modal fade" id="experimentModal" tabindex="-1" aria-labelledby="experimentModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="experimentModalLabel">Experiment Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Detailed view for experiment: <code>${experimentId}</code></p>
                        <p>This would show detailed information about the experiment, including:</p>
                        <ul>
                            <li>Parameters and configuration</li>
                            <li>Execution status and progress</li>
                            <li>Results and visualizations</li>
                            <li>Logs and error messages</li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary">
                            <i class="fas fa-download me-1"></i> Export Results
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove any existing modals
    const existingModal = document.getElementById('experimentModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add the new modal to the DOM and show it
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('experimentModal'));
    modal.show();
}

/**
 * Show a notification/alert to the user
 * @param {string} message - The message to display
 * @param {string} type - The type of alert (success, danger, warning, info)
 * @param {number} [duration=5000] - How long to show the alert in milliseconds
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to the alerts container if it exists, otherwise to the top of the main content
    const alertsContainer = document.getElementById('alerts-container') || document.querySelector('.main-content');
    alertsContainer.prepend(alertDiv);
    
    // Auto-dismiss after the specified duration
    if (duration > 0) {
        setTimeout(() => {
            const alert = bootstrap.Alert.getOrCreateInstance(alertDiv);
            alert.close();
        }, duration);
    }
}

/**
 * Get the appropriate CSS class for a status badge
 * @param {string} status - The status to get the class for
 * @returns {string} The appropriate CSS class
 */
function getStatusBadgeClass(status) {
    const statusClasses = {
        'queued': 'bg-secondary',
        'running': 'bg-warning text-dark',
        'completed': 'bg-success',
        'failed': 'bg-danger',
        'cancelled': 'bg-secondary'
    };
    return statusClasses[status.toLowerCase()] || 'bg-secondary';
}

/**
 * Get the appropriate CSS class for an experiment type badge
 * @param {string} type - The experiment type
 * @returns {string} The appropriate CSS class
 */
function getExperimentTypeClass(type) {
    const typeClasses = {
        'qaoa': 'bg-primary',
        'vqe': 'bg-info text-dark',
        'quantum_ml': 'bg-purple',
        'default': 'bg-secondary'
    };
    return typeClasses[type.toLowerCase()] || typeClasses['default'];
}

/**
 * Get the appropriate CSS class for a progress bar based on the percentage
 * @param {number} percent - The percentage value (0-100)
 * @returns {string} The appropriate CSS class
 */
function getProgressBarClass(percent) {
    if (percent < 50) return 'bg-success';
    if (percent < 80) return 'bg-warning';
    return 'bg-danger';
}

/**
 * Format a date string to a more readable format
 * @param {string} dateString - The date string to format
 * @returns {string} Formatted date string
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleString();
}

// Make functions available globally
window.showAlert = showAlert;
window.updateDashboard = updateDashboard;
