<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner Knowledge Base | NovaFuse</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .kb-card {
            background-color: var(--secondary-bg);
            border-radius: 0.5rem;
            padding: 1.5rem;
            height: 100%;
            transition: transform 0.3s ease;
            border-left: 4px solid var(--accent-color);
        }

        .kb-card:hover {
            transform: translateY(-5px);
        }

        .kb-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--accent-color);
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto 2rem auto;
        }

        .search-input {
            width: 100%;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: var(--secondary-bg);
            border: 1px solid var(--border-color);
            color: var(--primary-text);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
        }

        .search-icon {
            position: absolute;
            top: 50%;
            right: 1rem;
            transform: translateY(-50%);
            color: var(--secondary-text);
            pointer-events: none;
        }

        .quick-links {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .quick-link {
            background-color: var(--secondary-bg);
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .quick-link:hover {
            background-color: var(--accent-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Include Header and Navigation -->
    <div id="header-container"></div>

    <main class="container">
        <!-- Breadcrumbs -->
        <div id="breadcrumbs-container"></div>

        <!-- Hero Section -->
        <div class="hero">
            <h2>Partner Knowledge Base</h2>
            <p>
                Your central resource for all NovaFuse partner information, documentation, and support.
            </p>
        </div>

        <!-- Search -->
        <section class="mb-12">
            <div class="search-container relative">
                <input type="text" class="search-input" placeholder="Search the knowledge base...">
                <div class="search-icon">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <div class="quick-links">
                <a href="#program-overview" class="quick-link">Program Overview</a>
                <a href="#integration-support" class="quick-link">Integration Support</a>
                <a href="#compliance-enablement" class="quick-link">Compliance Enablement</a>
                <a href="#sales-marketing" class="quick-link">Sales & Marketing</a>
                <a href="#revenue-growth" class="quick-link">Revenue & Growth</a>
                <a href="#faqs" class="quick-link">FAQs</a>
            </div>
        </section>

        <!-- Program Overview -->
        <section class="mb-12" id="program-overview">
            <h2 class="text-2xl font-bold mb-6">Program Overview</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Partner Program Guide -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-handshake"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Partner Program Guide</h3>
                    <p class="mb-4">
                        Comprehensive overview of the NovaFuse Partner Program, including benefits, requirements, and support resources.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Guide →</a>
                </div>

                <!-- Partner Empowerment Model -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Partner Empowerment Model</h3>
                    <p class="mb-4">
                        Detailed explanation of our revolutionary partner empowerment model and how it differs from traditional vendor approaches.
                    </p>
                    <a href="partner-empowerment.html" class="text-blue-400 hover:text-blue-300">Learn More →</a>
                </div>

                <!-- Partner Journey -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-road"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Partner Journey</h3>
                    <p class="mb-4">
                        Step-by-step guide to the partner journey, from onboarding to certification and beyond.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Journey →</a>
                </div>
            </div>
        </section>

        <!-- Integration Support -->
        <section class="mb-12" id="integration-support">
            <h2 class="text-2xl font-bold mb-6">Integration Support</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- API Documentation -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">API Documentation</h3>
                    <p class="mb-4">
                        Comprehensive API reference documentation for all NovaFuse products and services.
                    </p>
                    <a href="api-docs.html" class="text-blue-400 hover:text-blue-300">View Documentation →</a>
                </div>

                <!-- Integration Guides -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Integration Guides</h3>
                    <p class="mb-4">
                        Step-by-step guides for integrating with NovaFuse products and services.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Guides →</a>
                </div>

                <!-- Developer Resources -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Developer Resources</h3>
                    <p class="mb-4">
                        SDKs, sample code, and other resources for developers working with NovaFuse APIs.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Resources →</a>
                </div>
            </div>
        </section>

        <!-- Compliance Enablement -->
        <section class="mb-12" id="compliance-enablement">
            <h2 class="text-2xl font-bold mb-6">Compliance Enablement</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Compliance Frameworks -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Compliance Frameworks</h3>
                    <p class="mb-4">
                        Overview of supported compliance frameworks and how NovaFuse helps address their requirements.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Frameworks →</a>
                </div>

                <!-- Control Mapping -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Control Mapping</h3>
                    <p class="mb-4">
                        Detailed mapping of controls across different compliance frameworks and how they relate to NovaFuse features.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Mapping →</a>
                </div>

                <!-- Compliance Best Practices -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Compliance Best Practices</h3>
                    <p class="mb-4">
                        Best practices for implementing and maintaining compliance using NovaFuse products.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Best Practices →</a>
                </div>
            </div>
        </section>

        <!-- Sales & Marketing Resources -->
        <section class="mb-12" id="sales-marketing">
            <h2 class="text-2xl font-bold mb-6">Sales & Marketing Resources</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Sales Enablement -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Sales Enablement</h3>
                    <p class="mb-4">
                        Sales scripts, objection handling, and other resources to help you sell NovaFuse solutions.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Resources →</a>
                </div>

                <!-- Marketing Materials -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Marketing Materials</h3>
                    <p class="mb-4">
                        Co-branded marketing materials, case studies, and other resources to help you promote NovaFuse solutions.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Materials →</a>
                </div>

                <!-- Demo Resources -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Demo Resources</h3>
                    <p class="mb-4">
                        Demo scripts, environments, and other resources to help you demonstrate NovaFuse solutions.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Resources →</a>
                </div>
            </div>
        </section>

        <!-- Revenue & Growth -->
        <section class="mb-12" id="revenue-growth">
            <h2 class="text-2xl font-bold mb-6">Revenue & Growth</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Revenue Model -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Revenue Model</h3>
                    <p class="mb-4">
                        Detailed explanation of the NovaFuse revenue model, including revenue sharing, pricing, and billing.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Model →</a>
                </div>

                <!-- Growth Strategies -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Growth Strategies</h3>
                    <p class="mb-4">
                        Strategies and best practices for growing your business with NovaFuse solutions.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Strategies →</a>
                </div>

                <!-- Success Stories -->
                <div class="kb-card">
                    <div class="kb-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Success Stories</h3>
                    <p class="mb-4">
                        Case studies and success stories from NovaFuse partners who have achieved significant growth.
                    </p>
                    <a href="#" class="text-blue-400 hover:text-blue-300">View Stories →</a>
                </div>
            </div>
        </section>

        <!-- FAQs -->
        <section class="mb-12" id="faqs">
            <h2 class="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
            <div class="bg-secondary-bg p-6 rounded-lg">
                <div class="mb-6">
                    <h3 class="text-xl font-bold mb-2">What is the NovaFuse Partner Empowerment model?</h3>
                    <p>
                        The NovaFuse Partner Empowerment model is a revolutionary approach to partnerships that focuses on empowering partners rather than competing with them. It includes a unique 18% value-based implementation fee, up to 85% revenue sharing, and comprehensive technical and go-to-market support.
                    </p>
                </div>

                <div class="mb-6">
                    <h3 class="text-xl font-bold mb-2">How does the 18% value-based implementation fee work?</h3>
                    <p>
                        Unlike traditional vendors that charge high implementation fees or compete with partners for services revenue, we charge just 18% of the typical market cost for implementation. This means partners can offer competitive pricing while maintaining healthy margins, and customers get enterprise-grade technology at a fraction of the cost of traditional solutions.
                    </p>
                </div>

                <div class="mb-6">
                    <h3 class="text-xl font-bold mb-2">What technical support do partners receive?</h3>
                    <p>
                        Partners receive comprehensive technical support, including dedicated partner support channels with priority response times, access to our technical experts, and extensive documentation and training resources.
                    </p>
                </div>

                <div class="mb-6">
                    <h3 class="text-xl font-bold mb-2">How does the UAC Demo → 48-Hour Journey work?</h3>
                    <p>
                        Our unique two-part demo strategy starts with the UAC Demo (Day 0), which shows the universal integration capabilities and baseline compliance logic as a proof of concept. This is followed by a 48-hour customization period (Days 1-2), where we work with the client to understand their specific systems, frameworks, and workflows, and deliver a customized solution that integrates with their actual systems.
                    </p>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-2">How do I become a NovaFuse partner?</h3>
                    <p>
                        To become a NovaFuse partner, you can apply through our <a href="partner-onboarding.html" class="text-blue-400 hover:text-blue-300">Partner Onboarding</a> page. Our team will review your application and contact you to discuss the next steps.
                    </p>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="faq.html" class="btn btn-primary">View All FAQs</a>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="mb-12">
            <div class="bg-blue-900 rounded-lg p-8 text-center">
                <h2 class="text-2xl font-bold mb-4">Need Additional Support?</h2>
                <p class="mb-6 max-w-3xl mx-auto">
                    Our partner support team is here to help you with any questions or issues you may have.
                </p>
                <div class="flex justify-center space-x-4">
                    <a href="contact.html" class="btn btn-primary">Contact Support</a>
                    <a href="partner-portal.html" class="btn btn-outline">Partner Portal</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Include Footer -->
    <div id="footer-container"></div>

    <script src="js/main.js"></script>
    <script>
        // Load header and footer components
        document.addEventListener('DOMContentLoaded', function() {
            // Load header
            fetch('components/header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header-container').innerHTML = data;
                });

            // Load footer
            fetch('components/footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer-container').innerHTML = data;
                });

            // Load breadcrumbs
            fetch('components/breadcrumbs.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('breadcrumbs-container').innerHTML = data;
                    // Add current page to breadcrumbs
                    const breadcrumbs = document.querySelector('.breadcrumbs');
                    const li = document.createElement('li');
                    const a = document.createElement('a');
                    a.href = 'partner-knowledge-base.html';
                    a.textContent = 'Partner Knowledge Base';
                    li.appendChild(a);
                    breadcrumbs.appendChild(li);
                });
        });
    </script>
</body>
</html>
