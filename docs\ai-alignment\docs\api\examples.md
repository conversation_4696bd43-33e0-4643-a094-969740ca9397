# API Examples

This document provides practical examples of how to interact with the NovaAlign API using various programming languages and tools.

## Table of Contents
- [Authentication](#authentication)
- [Systems](#systems)
- [Metrics](#metrics)
- [Alerts](#alerts)
- [Webhooks](#webhooks)
- [Erro<PERSON> Handling](#error-handling)
- [Rate Limiting](#rate-limiting)

## Authentication

### Get API Token

```bash
# Using curl
curl -X POST https://api.novaalign.ai/auth/token \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword"}'
```

```javascript
// Using fetch in JavaScript
const response = await fetch('https://api.novaalign.ai/auth/token', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'yourpassword'
  })
});
const data = await response.json();
console.log(data.access_token);
```

### Using the Access Token

```bash
# Using the token in subsequent requests
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  https://api.novaalign.ai/api/v1/systems
```

## Systems

### List All Systems

```bash
curl -H "Authorization: Bearer $TOKEN" \
  "https://api.novaalign.ai/api/v1/systems?limit=10&offset=0"
```

```python
# Python example using requests
import requests

headers = {
    'Authorization': f'Bearer {TOKEN}'
}

response = requests.get(
    'https://api.novaalign.ai/api/v1/systems',
    headers=headers,
    params={'limit': 10, 'offset': 0}
)
print(response.json())
```

### Get System Details

```bash
curl -H "Authorization: Bearer $TOKEN" \
  "https://api.novaalign.ai/api/v1/systems/sys_123"
```

### Create a New System

```bash
curl -X POST https://api.novaalign.ai/api/v1/systems \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Customer Support AI",
    "description": "AI system for customer support",
    "type": "nlp",
    "monitoring_enabled": true
  }'
```

## Metrics

### Submit Metrics

```bash
curl -X POST https://api.novaalign.ai/api/v1/metrics \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "system_id": "sys_123",
    "timestamp": "2025-06-25T08:00:00Z",
    "metrics": {
      "psi": 0.87,
      "phi": 0.91,
      "theta": 0.89
    },
    "metadata": {
      "model_version": "1.2.3",
      "environment": "production"
    }
  }'
```

### Query Metrics

```bash
# Get metrics for a system
curl -G https://api.novaalign.ai/api/v1/metrics \
  -H "Authorization: Bearer $TOKEN" \
  -d "system_id=sys_123" \
  -d "start_time=2025-06-24T00:00:00Z" \
  -d "end_time=2025-06-25T00:00:00Z" \
  -d "interval=1h"
```

## Alerts

### List Alerts

```bash
curl -G https://api.novaalign.ai/api/v1/alerts \
  -H "Authorization: Bearer $TOKEN" \
  -d "status=open" \
  -d "severity=high,critical"
```

### Acknowledge an Alert

```bash
curl -X POST https://api.novaalign.ai/api/v1/alerts/alt_123/acknowledge \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"notes":"Investigating the issue"}'
```

## Webhooks

### Create a Webhook

```bash
curl -X POST https://api.novaalign.ai/api/v1/webhooks \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Slack Alerts",
    "url": "https://hooks.slack.com/services/...",
    "events": ["alert.created", "alert.updated"],
    "secret": "your-signing-secret"
  }'
```

### Verify Webhook Signature

```python
# Python example to verify webhook signature
import hmac
import hashlib

def verify_webhook(payload, signature, secret):
    hmac_digest = hmac.new(
        secret.encode('utf-8'),
        payload,
        hashlib.sha256
    ).hexdigest()
    
    return hmac.compare_digest(f'sha256={hmac_digest}', signature)

# Example usage
is_valid = verify_webhook(
    request.data,
    request.headers.get('X-NovaAlign-Signature'),
    'your-signing-secret'
)
```

## Error Handling

### Handling Rate Limits

```python
import requests
import time

def make_request(url, token, max_retries=3):
    headers = {'Authorization': f'Bearer {token}'}
    
    for attempt in range(max_retries):
        response = requests.get(url, headers=headers)
        
        if response.status_code == 429:  # Rate limited
            retry_after = int(response.headers.get('Retry-After', 5))
            time.sleep(retry_after)
            continue
            
        response.raise_for_status()
        return response.json()
    
    raise Exception('Max retries exceeded')
```

### Error Response Example

```json
{
  "error": {
    "code": "RESOURCE_NOT_FOUND",
    "message": "The requested resource was not found",
    "details": {
      "resource": "system",
      "id": "nonexistent_id"
    }
  }
}
```

## Rate Limiting

### Checking Rate Limits

```bash
curl -I https://api.novaalign.ai/api/v1/systems \
  -H "Authorization: Bearer $TOKEN"

# Response Headers:
# X-RateLimit-Limit: 1000
# X-RateLimit-Remaining: 999
# X-RateLimit-Reset: 1624608000
```

### Handling Rate Limits in Code

```javascript
async function makeRequest(url, token) {
  const response = await fetch(url, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });

  if (response.status === 429) {
    const resetTime = parseInt(response.headers.get('X-RateLimit-Reset'), 10);
    const waitTime = Math.max(0, resetTime * 1000 - Date.now()) + 1000; // Add 1s buffer
    
    console.log(`Rate limited. Waiting ${waitTime}ms before retry...`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
    return makeRequest(url, token); // Retry
  }

  if (!response.ok) {
    throw new Error(`Request failed: ${response.status}`);
  }

  return response.json();
}
```

## SDKs and Client Libraries

### Official SDKs

#### Python
```python
from novaalign import NovaAlignClient

client = NovaAlignClient(api_key='your-api-key')

# List systems
systems = client.systems.list()

# Submit metrics
client.metrics.submit(
    system_id='sys_123',
    metrics={'psi': 0.87, 'phi': 0.91, 'theta': 0.89}
)
```

#### JavaScript/Node.js
```javascript
const { NovaAlignClient } = require('novaalign');

const client = new NovaAlignClient({
  apiKey: 'your-api-key'
});

// Create a webhook
const webhook = await client.webhooks.create({
  name: 'Slack Alerts',
  url: 'https://hooks.slack.com/...',
  events: ['alert.created']
});
```

## Best Practices

1. **Authentication**
   - Never hardcode API keys in source code
   - Use environment variables for sensitive data
   - Rotate API keys regularly

2. **Error Handling**
   - Always check for error responses
   - Implement retry logic with exponential backoff
   - Log errors for debugging

3. **Performance**
   - Use pagination for large datasets
   - Cache responses when appropriate
   - Batch requests when possible

4. **Security**
   - Use HTTPS for all requests
   - Validate all input data
   - Sanitize output to prevent XSS

## Support

For additional help, please contact our support <NAME_EMAIL> or visit our [developer portal](https://developers.novaalign.ai).
