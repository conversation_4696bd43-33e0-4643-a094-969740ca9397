/**
 * OAuth2 Authentication Controller
 *
 * This controller handles API requests related to OAuth2 authentication.
 */

const OAuth2Service = require('../services/OAuth2Service');
const AuthService = require('../services/AuthService');
const { ValidationError } = require('../utils/errors');

class OAuth2Controller {
  constructor() {
    this.oauth2Service = new OAuth2Service();
    this.authService = new AuthService();
  }

  /**
   * Get all OAuth2 providers
   */
  async getProviders(req, res, next) {
    try {
      const providers = await this.oauth2Service.loadProviders();

      // Remove sensitive information
      const sanitizedProviders = providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        enabled: provider.enabled,
        authorizationUrl: provider.authorizationUrl,
        scope: provider.scope
      }));

      res.json(sanitizedProviders);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get OAuth2 provider by ID
   */
  async getProviderById(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Provider ID is required');
      }

      const provider = await this.oauth2Service.getProviderById(id);

      // Remove sensitive information
      const sanitizedProvider = {
        id: provider.id,
        name: provider.name,
        enabled: provider.enabled,
        authorizationUrl: provider.authorizationUrl,
        scope: provider.scope
      };

      res.json(sanitizedProvider);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initiate OAuth2 authentication
   */
  async initiateAuth(req, res, next) {
    try {
      const { providerId } = req.params;
      const { redirectUri } = req.query;

      if (!providerId) {
        throw new ValidationError('Provider ID is required');
      }

      if (!redirectUri) {
        throw new ValidationError('Redirect URI is required');
      }

      const result = await this.oauth2Service.generateAuthUrl(providerId, redirectUri);

      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process OAuth2 callback
   */
  async processCallback(req, res, next) {
    try {
      const { code, state } = req.query;
      const { redirectUri } = req.body;

      if (!code) {
        throw new ValidationError('Authorization code is required');
      }

      if (!state) {
        throw new ValidationError('State parameter is required');
      }

      // Get OAuth2 user info
      const oauthResult = await this.oauth2Service.processCallback(code, state, redirectUri);

      // Login or register user with OAuth2 info
      const result = await this.authService.loginWithOAuth2(
        {
          email: oauthResult.user.email,
          username: oauthResult.user.username,
          firstName: oauthResult.user.firstName,
          lastName: oauthResult.user.lastName
        },
        oauthResult.provider.id,
        oauthResult.user.id
      );

      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new OAuth2Controller();
