{"version": 3, "names": ["EncryptionService", "require", "crypto", "performance", "describe", "encryptionService", "keyId", "beforeAll", "algorithm", "<PERSON><PERSON><PERSON><PERSON>", "iv<PERSON><PERSON><PERSON>", "saltLength", "iterations", "digest", "keyInfo", "<PERSON><PERSON>ey", "it", "keys", "i", "push", "uniqueKeys", "Set", "expect", "size", "toBe", "length", "key", "keyCache", "get", "toBeDefined", "toBeInstanceOf", "<PERSON><PERSON><PERSON>", "keyBuffers", "j", "compare", "not", "entropy", "calculateEntropy", "toBeGreaterThan", "testData", "encrypted", "encrypt", "toHaveProperty", "decrypted", "decrypt", "toString", "testObject", "id", "name", "email", "ssn", "creditCard", "decryptedObject", "JSON", "parse", "toEqual", "newKeyInfo", "modifiedEncrypted", "rejects", "toThrow", "tamperedEncrypted", "data", "substring", "authTag", "password", "encryptWithPassword", "decryptWithPassword", "wrongPassword", "salt", "randomBytes", "startTime", "now", "pbkdf2Sync", "endTime", "duration", "initialKeys", "Array", "from", "rotateKeys", "toContain", "oldKeyId", "encryptionTimes", "avgEncryptionTime", "reduce", "sum", "time", "toBeLessThan", "decryptionTimes", "avgDecryptionTime", "console", "log", "toFixed", "largeData", "encryptStartTime", "encryptEndTime", "encryptDuration", "decryptStartTime", "decryptEndTime", "decryptDuration", "buffer", "frequencies", "fill", "byte", "count", "probability", "Math", "log2"], "sources": ["encryption-security.test.js"], "sourcesContent": ["/**\n * NovaConnect Encryption Service Security Tests\n * \n * This test suite validates the security properties of the encryption service,\n * including FIPS 140-3 compliance, key management, and cryptographic operations.\n */\n\nconst { EncryptionService } = require('../../src/security/encryption-service');\nconst crypto = require('crypto');\nconst { performance } = require('perf_hooks');\n\ndescribe('Encryption Service Security', () => {\n  let encryptionService;\n  let keyId;\n  \n  beforeAll(async () => {\n    // Initialize encryption service\n    encryptionService = new EncryptionService({\n      algorithm: 'aes-256-gcm',\n      keyLength: 32,\n      ivLength: 16,\n      saltLength: 32,\n      iterations: 100000,\n      digest: 'sha256'\n    });\n    \n    // Generate a key for testing\n    const keyInfo = await encryptionService.generateKey();\n    keyId = keyInfo.keyId;\n  });\n  \n  describe('Key Generation', () => {\n    it('should generate cryptographically secure keys', async () => {\n      // Generate multiple keys and check for uniqueness\n      const keys = [];\n      for (let i = 0; i < 10; i++) {\n        const keyInfo = await encryptionService.generateKey();\n        keys.push(keyInfo.keyId);\n      }\n      \n      // Check that all keys are unique\n      const uniqueKeys = new Set(keys);\n      expect(uniqueKeys.size).toBe(keys.length);\n      \n      // Check that keys are stored securely in the cache\n      for (const key of keys) {\n        const keyInfo = encryptionService.keyCache.get(key);\n        expect(keyInfo).toBeDefined();\n        expect(keyInfo.key).toBeInstanceOf(Buffer);\n        expect(keyInfo.key.length).toBe(32); // 256 bits\n      }\n    });\n    \n    it('should use secure random number generation', async () => {\n      // Generate multiple keys and check for randomness\n      const keyBuffers = [];\n      for (let i = 0; i < 10; i++) {\n        const keyInfo = await encryptionService.generateKey();\n        const key = encryptionService.keyCache.get(keyInfo.keyId).key;\n        keyBuffers.push(key);\n      }\n      \n      // Check that keys are different\n      for (let i = 0; i < keyBuffers.length; i++) {\n        for (let j = i + 1; j < keyBuffers.length; j++) {\n          expect(Buffer.compare(keyBuffers[i], keyBuffers[j])).not.toBe(0);\n        }\n      }\n      \n      // Check entropy of keys\n      for (const key of keyBuffers) {\n        const entropy = calculateEntropy(key);\n        expect(entropy).toBeGreaterThan(7.5); // High entropy expected for random data\n      }\n    });\n  });\n  \n  describe('Encryption and Decryption', () => {\n    it('should encrypt and decrypt data correctly', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted';\n      \n      // Encrypt the data\n      const encrypted = await encryptionService.encrypt(testData, keyId);\n      \n      // Check encrypted package structure\n      expect(encrypted).toHaveProperty('keyId', keyId);\n      expect(encrypted).toHaveProperty('iv');\n      expect(encrypted).toHaveProperty('authTag');\n      expect(encrypted).toHaveProperty('data');\n      expect(encrypted).toHaveProperty('algorithm', 'aes-256-gcm');\n      expect(encrypted).toHaveProperty('encryptedAt');\n      \n      // Decrypt the data\n      const decrypted = await encryptionService.decrypt(encrypted);\n      \n      // Check that decrypted data matches original\n      expect(decrypted.toString()).toBe(testData);\n    });\n    \n    it('should encrypt and decrypt objects correctly', async () => {\n      const testObject = {\n        id: '12345',\n        name: 'Test User',\n        email: '<EMAIL>',\n        ssn: '***********',\n        creditCard: '4111-1111-1111-1111'\n      };\n      \n      // Encrypt the object\n      const encrypted = await encryptionService.encrypt(testObject, keyId);\n      \n      // Decrypt the object\n      const decrypted = await encryptionService.decrypt(encrypted);\n      \n      // Parse the decrypted data\n      const decryptedObject = JSON.parse(decrypted.toString());\n      \n      // Check that decrypted object matches original\n      expect(decryptedObject).toEqual(testObject);\n    });\n    \n    it('should fail to decrypt with wrong key', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted';\n      \n      // Encrypt the data\n      const encrypted = await encryptionService.encrypt(testData, keyId);\n      \n      // Generate a new key\n      const newKeyInfo = await encryptionService.generateKey();\n      \n      // Modify the encrypted package to use the wrong key\n      const modifiedEncrypted = {\n        ...encrypted,\n        keyId: newKeyInfo.keyId\n      };\n      \n      // Attempt to decrypt with wrong key\n      await expect(encryptionService.decrypt(modifiedEncrypted)).rejects.toThrow();\n    });\n    \n    it('should fail to decrypt with tampered data', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted';\n      \n      // Encrypt the data\n      const encrypted = await encryptionService.encrypt(testData, keyId);\n      \n      // Tamper with the encrypted data\n      const tamperedEncrypted = {\n        ...encrypted,\n        data: encrypted.data.substring(0, encrypted.data.length - 5) + 'XXXXX'\n      };\n      \n      // Attempt to decrypt tampered data\n      await expect(encryptionService.decrypt(tamperedEncrypted)).rejects.toThrow();\n    });\n    \n    it('should fail to decrypt with tampered authentication tag', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted';\n      \n      // Encrypt the data\n      const encrypted = await encryptionService.encrypt(testData, keyId);\n      \n      // Tamper with the authentication tag\n      const tamperedEncrypted = {\n        ...encrypted,\n        authTag: 'AAAA' + encrypted.authTag.substring(4)\n      };\n      \n      // Attempt to decrypt with tampered authentication tag\n      await expect(encryptionService.decrypt(tamperedEncrypted)).rejects.toThrow();\n    });\n  });\n  \n  describe('Password-Based Encryption', () => {\n    it('should encrypt and decrypt data with password', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted with a password';\n      const password = 'StrongP@ssw0rd!';\n      \n      // Encrypt the data with password\n      const encrypted = await encryptionService.encryptWithPassword(testData, password);\n      \n      // Check encrypted package structure\n      expect(encrypted).toHaveProperty('salt');\n      expect(encrypted).toHaveProperty('iv');\n      expect(encrypted).toHaveProperty('authTag');\n      expect(encrypted).toHaveProperty('data');\n      expect(encrypted).toHaveProperty('algorithm', 'aes-256-gcm');\n      expect(encrypted).toHaveProperty('iterations', 100000);\n      expect(encrypted).toHaveProperty('keyLength', 32);\n      expect(encrypted).toHaveProperty('digest', 'sha256');\n      expect(encrypted).toHaveProperty('encryptedAt');\n      \n      // Decrypt the data with password\n      const decrypted = await encryptionService.decryptWithPassword(encrypted, password);\n      \n      // Check that decrypted data matches original\n      expect(decrypted.toString()).toBe(testData);\n    });\n    \n    it('should fail to decrypt with wrong password', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted with a password';\n      const password = 'StrongP@ssw0rd!';\n      const wrongPassword = 'WrongP@ssw0rd!';\n      \n      // Encrypt the data with password\n      const encrypted = await encryptionService.encryptWithPassword(testData, password);\n      \n      // Attempt to decrypt with wrong password\n      await expect(encryptionService.decryptWithPassword(encrypted, wrongPassword)).rejects.toThrow();\n    });\n    \n    it('should use secure key derivation', async () => {\n      const password = 'StrongP@ssw0rd!';\n      const salt = crypto.randomBytes(32);\n      \n      // Derive key with different iterations\n      const startTime = performance.now();\n      const key = await crypto.pbkdf2Sync(\n        password,\n        salt,\n        100000,\n        32,\n        'sha256'\n      );\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // Check that key derivation takes a reasonable amount of time\n      // Should be slow enough to prevent brute force, but not too slow for normal use\n      expect(duration).toBeGreaterThan(10); // At least 10ms\n      \n      // Check key properties\n      expect(key).toBeInstanceOf(Buffer);\n      expect(key.length).toBe(32); // 256 bits\n      \n      // Check entropy of derived key\n      const entropy = calculateEntropy(key);\n      expect(entropy).toBeGreaterThan(7.5); // High entropy expected\n    });\n  });\n  \n  describe('Key Rotation', () => {\n    it('should rotate keys securely', async () => {\n      // Get current keys\n      const initialKeys = Array.from(encryptionService.keyCache.keys());\n      \n      // Rotate keys\n      const newKeyInfo = await encryptionService.rotateKeys();\n      \n      // Check that a new key was generated\n      expect(newKeyInfo).toHaveProperty('keyId');\n      expect(initialKeys).not.toContain(newKeyInfo.keyId);\n      \n      // Check that old keys are marked as rotated\n      for (const oldKeyId of initialKeys) {\n        const keyInfo = encryptionService.keyCache.get(oldKeyId);\n        expect(keyInfo).toHaveProperty('rotatedAt');\n        expect(keyInfo).toHaveProperty('replacedBy', newKeyInfo.keyId);\n      }\n      \n      // Encrypt data with the new key\n      const testData = 'Data encrypted with the new key';\n      const encrypted = await encryptionService.encrypt(testData, newKeyInfo.keyId);\n      \n      // Decrypt the data\n      const decrypted = await encryptionService.decrypt(encrypted);\n      \n      // Check that decrypted data matches original\n      expect(decrypted.toString()).toBe(testData);\n    });\n  });\n  \n  describe('Performance and Security Tradeoffs', () => {\n    it('should have acceptable performance for encryption operations', async () => {\n      const testData = 'This is sensitive data that needs to be encrypted';\n      \n      // Measure encryption performance\n      const encryptionTimes = [];\n      for (let i = 0; i < 100; i++) {\n        const startTime = performance.now();\n        await encryptionService.encrypt(testData, keyId);\n        const endTime = performance.now();\n        encryptionTimes.push(endTime - startTime);\n      }\n      \n      // Calculate average encryption time\n      const avgEncryptionTime = encryptionTimes.reduce((sum, time) => sum + time, 0) / encryptionTimes.length;\n      \n      // Check that encryption is reasonably fast\n      expect(avgEncryptionTime).toBeLessThan(10); // Less than 10ms per operation\n      \n      // Measure decryption performance\n      const encrypted = await encryptionService.encrypt(testData, keyId);\n      const decryptionTimes = [];\n      for (let i = 0; i < 100; i++) {\n        const startTime = performance.now();\n        await encryptionService.decrypt(encrypted);\n        const endTime = performance.now();\n        decryptionTimes.push(endTime - startTime);\n      }\n      \n      // Calculate average decryption time\n      const avgDecryptionTime = decryptionTimes.reduce((sum, time) => sum + time, 0) / decryptionTimes.length;\n      \n      // Check that decryption is reasonably fast\n      expect(avgDecryptionTime).toBeLessThan(10); // Less than 10ms per operation\n      \n      console.log(`Average encryption time: ${avgEncryptionTime.toFixed(2)}ms`);\n      console.log(`Average decryption time: ${avgDecryptionTime.toFixed(2)}ms`);\n    });\n    \n    it('should handle large data efficiently', async () => {\n      // Generate 1MB of random data\n      const largeData = crypto.randomBytes(1024 * 1024);\n      \n      // Measure encryption time\n      const encryptStartTime = performance.now();\n      const encrypted = await encryptionService.encrypt(largeData, keyId);\n      const encryptEndTime = performance.now();\n      const encryptDuration = encryptEndTime - encryptStartTime;\n      \n      // Measure decryption time\n      const decryptStartTime = performance.now();\n      const decrypted = await encryptionService.decrypt(encrypted);\n      const decryptEndTime = performance.now();\n      const decryptDuration = decryptEndTime - decryptStartTime;\n      \n      // Check that decrypted data matches original\n      expect(Buffer.compare(decrypted, largeData)).toBe(0);\n      \n      // Check performance\n      console.log(`Encryption of 1MB: ${encryptDuration.toFixed(2)}ms`);\n      console.log(`Decryption of 1MB: ${decryptDuration.toFixed(2)}ms`);\n      \n      // Performance should be reasonable for large data\n      expect(encryptDuration).toBeLessThan(1000); // Less than 1 second\n      expect(decryptDuration).toBeLessThan(1000); // Less than 1 second\n    });\n  });\n});\n\n/**\n * Calculate Shannon entropy of a buffer\n * @param {Buffer} buffer - Buffer to calculate entropy for\n * @returns {number} - Entropy value (bits per byte)\n */\nfunction calculateEntropy(buffer) {\n  const frequencies = new Array(256).fill(0);\n  \n  // Count byte frequencies\n  for (const byte of buffer) {\n    frequencies[byte]++;\n  }\n  \n  // Calculate entropy\n  let entropy = 0;\n  for (const count of frequencies) {\n    if (count > 0) {\n      const probability = count / buffer.length;\n      entropy -= probability * Math.log2(probability);\n    }\n  }\n  \n  return entropy;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAkB,CAAC,GAAGC,OAAO,CAAC,uCAAuC,CAAC;AAC9E,MAAMC,MAAM,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAM;EAAEE;AAAY,CAAC,GAAGF,OAAO,CAAC,YAAY,CAAC;AAE7CG,QAAQ,CAAC,6BAA6B,EAAE,MAAM;EAC5C,IAAIC,iBAAiB;EACrB,IAAIC,KAAK;EAETC,SAAS,CAAC,YAAY;IACpB;IACAF,iBAAiB,GAAG,IAAIL,iBAAiB,CAAC;MACxCQ,SAAS,EAAE,aAAa;MACxBC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,UAAU,EAAE,MAAM;MAClBC,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,MAAMC,OAAO,GAAG,MAAMT,iBAAiB,CAACU,WAAW,CAAC,CAAC;IACrDT,KAAK,GAAGQ,OAAO,CAACR,KAAK;EACvB,CAAC,CAAC;EAEFF,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BY,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D;MACA,MAAMC,IAAI,GAAG,EAAE;MACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMJ,OAAO,GAAG,MAAMT,iBAAiB,CAACU,WAAW,CAAC,CAAC;QACrDE,IAAI,CAACE,IAAI,CAACL,OAAO,CAACR,KAAK,CAAC;MAC1B;;MAEA;MACA,MAAMc,UAAU,GAAG,IAAIC,GAAG,CAACJ,IAAI,CAAC;MAChCK,MAAM,CAACF,UAAU,CAACG,IAAI,CAAC,CAACC,IAAI,CAACP,IAAI,CAACQ,MAAM,CAAC;;MAEzC;MACA,KAAK,MAAMC,GAAG,IAAIT,IAAI,EAAE;QACtB,MAAMH,OAAO,GAAGT,iBAAiB,CAACsB,QAAQ,CAACC,GAAG,CAACF,GAAG,CAAC;QACnDJ,MAAM,CAACR,OAAO,CAAC,CAACe,WAAW,CAAC,CAAC;QAC7BP,MAAM,CAACR,OAAO,CAACY,GAAG,CAAC,CAACI,cAAc,CAACC,MAAM,CAAC;QAC1CT,MAAM,CAACR,OAAO,CAACY,GAAG,CAACD,MAAM,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;MACvC;IACF,CAAC,CAAC;IAEFR,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D;MACA,MAAMgB,UAAU,GAAG,EAAE;MACrB,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMJ,OAAO,GAAG,MAAMT,iBAAiB,CAACU,WAAW,CAAC,CAAC;QACrD,MAAMW,GAAG,GAAGrB,iBAAiB,CAACsB,QAAQ,CAACC,GAAG,CAACd,OAAO,CAACR,KAAK,CAAC,CAACoB,GAAG;QAC7DM,UAAU,CAACb,IAAI,CAACO,GAAG,CAAC;MACtB;;MAEA;MACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,UAAU,CAACP,MAAM,EAAEP,CAAC,EAAE,EAAE;QAC1C,KAAK,IAAIe,CAAC,GAAGf,CAAC,GAAG,CAAC,EAAEe,CAAC,GAAGD,UAAU,CAACP,MAAM,EAAEQ,CAAC,EAAE,EAAE;UAC9CX,MAAM,CAACS,MAAM,CAACG,OAAO,CAACF,UAAU,CAACd,CAAC,CAAC,EAAEc,UAAU,CAACC,CAAC,CAAC,CAAC,CAAC,CAACE,GAAG,CAACX,IAAI,CAAC,CAAC,CAAC;QAClE;MACF;;MAEA;MACA,KAAK,MAAME,GAAG,IAAIM,UAAU,EAAE;QAC5B,MAAMI,OAAO,GAAGC,gBAAgB,CAACX,GAAG,CAAC;QACrCJ,MAAM,CAACc,OAAO,CAAC,CAACE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;MACxC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1CY,EAAE,CAAC,2CAA2C,EAAE,YAAY;MAC1D,MAAMuB,QAAQ,GAAG,mDAAmD;;MAEpE;MACA,MAAMC,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEjC,KAAK,CAAC;;MAElE;MACAgB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAEpC,KAAK,CAAC;MAChDgB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,IAAI,CAAC;MACtCpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,SAAS,CAAC;MAC3CpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,MAAM,CAAC;MACxCpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,WAAW,EAAE,aAAa,CAAC;MAC5DpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,aAAa,CAAC;;MAE/C;MACA,MAAMC,SAAS,GAAG,MAAMtC,iBAAiB,CAACuC,OAAO,CAACJ,SAAS,CAAC;;MAE5D;MACAlB,MAAM,CAACqB,SAAS,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACrB,IAAI,CAACe,QAAQ,CAAC;IAC7C,CAAC,CAAC;IAEFvB,EAAE,CAAC,8CAA8C,EAAE,YAAY;MAC7D,MAAM8B,UAAU,GAAG;QACjBC,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,kBAAkB;QACzBC,GAAG,EAAE,aAAa;QAClBC,UAAU,EAAE;MACd,CAAC;;MAED;MACA,MAAMX,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACK,UAAU,EAAExC,KAAK,CAAC;;MAEpE;MACA,MAAMqC,SAAS,GAAG,MAAMtC,iBAAiB,CAACuC,OAAO,CAACJ,SAAS,CAAC;;MAE5D;MACA,MAAMY,eAAe,GAAGC,IAAI,CAACC,KAAK,CAACX,SAAS,CAACE,QAAQ,CAAC,CAAC,CAAC;;MAExD;MACAvB,MAAM,CAAC8B,eAAe,CAAC,CAACG,OAAO,CAACT,UAAU,CAAC;IAC7C,CAAC,CAAC;IAEF9B,EAAE,CAAC,uCAAuC,EAAE,YAAY;MACtD,MAAMuB,QAAQ,GAAG,mDAAmD;;MAEpE;MACA,MAAMC,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEjC,KAAK,CAAC;;MAElE;MACA,MAAMkD,UAAU,GAAG,MAAMnD,iBAAiB,CAACU,WAAW,CAAC,CAAC;;MAExD;MACA,MAAM0C,iBAAiB,GAAG;QACxB,GAAGjB,SAAS;QACZlC,KAAK,EAAEkD,UAAU,CAAClD;MACpB,CAAC;;MAED;MACA,MAAMgB,MAAM,CAACjB,iBAAiB,CAACuC,OAAO,CAACa,iBAAiB,CAAC,CAAC,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC;IAC9E,CAAC,CAAC;IAEF3C,EAAE,CAAC,2CAA2C,EAAE,YAAY;MAC1D,MAAMuB,QAAQ,GAAG,mDAAmD;;MAEpE;MACA,MAAMC,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEjC,KAAK,CAAC;;MAElE;MACA,MAAMsD,iBAAiB,GAAG;QACxB,GAAGpB,SAAS;QACZqB,IAAI,EAAErB,SAAS,CAACqB,IAAI,CAACC,SAAS,CAAC,CAAC,EAAEtB,SAAS,CAACqB,IAAI,CAACpC,MAAM,GAAG,CAAC,CAAC,GAAG;MACjE,CAAC;;MAED;MACA,MAAMH,MAAM,CAACjB,iBAAiB,CAACuC,OAAO,CAACgB,iBAAiB,CAAC,CAAC,CAACF,OAAO,CAACC,OAAO,CAAC,CAAC;IAC9E,CAAC,CAAC;IAEF3C,EAAE,CAAC,yDAAyD,EAAE,YAAY;MACxE,MAAMuB,QAAQ,GAAG,mDAAmD;;MAEpE;MACA,MAAMC,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEjC,KAAK,CAAC;;MAElE;MACA,MAAMsD,iBAAiB,GAAG;QACxB,GAAGpB,SAAS;QACZuB,OAAO,EAAE,MAAM,GAAGvB,SAAS,CAACuB,OAAO,CAACD,SAAS,CAAC,CAAC;MACjD,CAAC;;MAED;MACA,MAAMxC,MAAM,CAACjB,iBAAiB,CAACuC,OAAO,CAACgB,iBAAiB,CAAC,CAAC,CAACF,OAAO,CAACC,OAAO,CAAC,CAAC;IAC9E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvD,QAAQ,CAAC,2BAA2B,EAAE,MAAM;IAC1CY,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D,MAAMuB,QAAQ,GAAG,mEAAmE;MACpF,MAAMyB,QAAQ,GAAG,iBAAiB;;MAElC;MACA,MAAMxB,SAAS,GAAG,MAAMnC,iBAAiB,CAAC4D,mBAAmB,CAAC1B,QAAQ,EAAEyB,QAAQ,CAAC;;MAEjF;MACA1C,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,MAAM,CAAC;MACxCpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,IAAI,CAAC;MACtCpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,SAAS,CAAC;MAC3CpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,MAAM,CAAC;MACxCpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,WAAW,EAAE,aAAa,CAAC;MAC5DpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,YAAY,EAAE,MAAM,CAAC;MACtDpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,WAAW,EAAE,EAAE,CAAC;MACjDpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC;MACpDpB,MAAM,CAACkB,SAAS,CAAC,CAACE,cAAc,CAAC,aAAa,CAAC;;MAE/C;MACA,MAAMC,SAAS,GAAG,MAAMtC,iBAAiB,CAAC6D,mBAAmB,CAAC1B,SAAS,EAAEwB,QAAQ,CAAC;;MAElF;MACA1C,MAAM,CAACqB,SAAS,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACrB,IAAI,CAACe,QAAQ,CAAC;IAC7C,CAAC,CAAC;IAEFvB,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D,MAAMuB,QAAQ,GAAG,mEAAmE;MACpF,MAAMyB,QAAQ,GAAG,iBAAiB;MAClC,MAAMG,aAAa,GAAG,gBAAgB;;MAEtC;MACA,MAAM3B,SAAS,GAAG,MAAMnC,iBAAiB,CAAC4D,mBAAmB,CAAC1B,QAAQ,EAAEyB,QAAQ,CAAC;;MAEjF;MACA,MAAM1C,MAAM,CAACjB,iBAAiB,CAAC6D,mBAAmB,CAAC1B,SAAS,EAAE2B,aAAa,CAAC,CAAC,CAACT,OAAO,CAACC,OAAO,CAAC,CAAC;IACjG,CAAC,CAAC;IAEF3C,EAAE,CAAC,kCAAkC,EAAE,YAAY;MACjD,MAAMgD,QAAQ,GAAG,iBAAiB;MAClC,MAAMI,IAAI,GAAGlE,MAAM,CAACmE,WAAW,CAAC,EAAE,CAAC;;MAEnC;MACA,MAAMC,SAAS,GAAGnE,WAAW,CAACoE,GAAG,CAAC,CAAC;MACnC,MAAM7C,GAAG,GAAG,MAAMxB,MAAM,CAACsE,UAAU,CACjCR,QAAQ,EACRI,IAAI,EACJ,MAAM,EACN,EAAE,EACF,QACF,CAAC;MACD,MAAMK,OAAO,GAAGtE,WAAW,CAACoE,GAAG,CAAC,CAAC;MACjC,MAAMG,QAAQ,GAAGD,OAAO,GAAGH,SAAS;;MAEpC;MACA;MACAhD,MAAM,CAACoD,QAAQ,CAAC,CAACpC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEtC;MACAhB,MAAM,CAACI,GAAG,CAAC,CAACI,cAAc,CAACC,MAAM,CAAC;MAClCT,MAAM,CAACI,GAAG,CAACD,MAAM,CAAC,CAACD,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE7B;MACA,MAAMY,OAAO,GAAGC,gBAAgB,CAACX,GAAG,CAAC;MACrCJ,MAAM,CAACc,OAAO,CAAC,CAACE,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFlC,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7BY,EAAE,CAAC,6BAA6B,EAAE,YAAY;MAC5C;MACA,MAAM2D,WAAW,GAAGC,KAAK,CAACC,IAAI,CAACxE,iBAAiB,CAACsB,QAAQ,CAACV,IAAI,CAAC,CAAC,CAAC;;MAEjE;MACA,MAAMuC,UAAU,GAAG,MAAMnD,iBAAiB,CAACyE,UAAU,CAAC,CAAC;;MAEvD;MACAxD,MAAM,CAACkC,UAAU,CAAC,CAACd,cAAc,CAAC,OAAO,CAAC;MAC1CpB,MAAM,CAACqD,WAAW,CAAC,CAACxC,GAAG,CAAC4C,SAAS,CAACvB,UAAU,CAAClD,KAAK,CAAC;;MAEnD;MACA,KAAK,MAAM0E,QAAQ,IAAIL,WAAW,EAAE;QAClC,MAAM7D,OAAO,GAAGT,iBAAiB,CAACsB,QAAQ,CAACC,GAAG,CAACoD,QAAQ,CAAC;QACxD1D,MAAM,CAACR,OAAO,CAAC,CAAC4B,cAAc,CAAC,WAAW,CAAC;QAC3CpB,MAAM,CAACR,OAAO,CAAC,CAAC4B,cAAc,CAAC,YAAY,EAAEc,UAAU,CAAClD,KAAK,CAAC;MAChE;;MAEA;MACA,MAAMiC,QAAQ,GAAG,iCAAiC;MAClD,MAAMC,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEiB,UAAU,CAAClD,KAAK,CAAC;;MAE7E;MACA,MAAMqC,SAAS,GAAG,MAAMtC,iBAAiB,CAACuC,OAAO,CAACJ,SAAS,CAAC;;MAE5D;MACAlB,MAAM,CAACqB,SAAS,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACrB,IAAI,CAACe,QAAQ,CAAC;IAC7C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnC,QAAQ,CAAC,oCAAoC,EAAE,MAAM;IACnDY,EAAE,CAAC,8DAA8D,EAAE,YAAY;MAC7E,MAAMuB,QAAQ,GAAG,mDAAmD;;MAEpE;MACA,MAAM0C,eAAe,GAAG,EAAE;MAC1B,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMoD,SAAS,GAAGnE,WAAW,CAACoE,GAAG,CAAC,CAAC;QACnC,MAAMlE,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEjC,KAAK,CAAC;QAChD,MAAMmE,OAAO,GAAGtE,WAAW,CAACoE,GAAG,CAAC,CAAC;QACjCU,eAAe,CAAC9D,IAAI,CAACsD,OAAO,GAAGH,SAAS,CAAC;MAC3C;;MAEA;MACA,MAAMY,iBAAiB,GAAGD,eAAe,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,EAAE,CAAC,CAAC,GAAGJ,eAAe,CAACxD,MAAM;;MAEvG;MACAH,MAAM,CAAC4D,iBAAiB,CAAC,CAACI,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE5C;MACA,MAAM9C,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACF,QAAQ,EAAEjC,KAAK,CAAC;MAClE,MAAMiF,eAAe,GAAG,EAAE;MAC1B,KAAK,IAAIrE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMoD,SAAS,GAAGnE,WAAW,CAACoE,GAAG,CAAC,CAAC;QACnC,MAAMlE,iBAAiB,CAACuC,OAAO,CAACJ,SAAS,CAAC;QAC1C,MAAMiC,OAAO,GAAGtE,WAAW,CAACoE,GAAG,CAAC,CAAC;QACjCgB,eAAe,CAACpE,IAAI,CAACsD,OAAO,GAAGH,SAAS,CAAC;MAC3C;;MAEA;MACA,MAAMkB,iBAAiB,GAAGD,eAAe,CAACJ,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,EAAE,CAAC,CAAC,GAAGE,eAAe,CAAC9D,MAAM;;MAEvG;MACAH,MAAM,CAACkE,iBAAiB,CAAC,CAACF,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;;MAE5CG,OAAO,CAACC,GAAG,CAAC,4BAA4BR,iBAAiB,CAACS,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACzEF,OAAO,CAACC,GAAG,CAAC,4BAA4BF,iBAAiB,CAACG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC3E,CAAC,CAAC;IAEF3E,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrD;MACA,MAAM4E,SAAS,GAAG1F,MAAM,CAACmE,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;;MAEjD;MACA,MAAMwB,gBAAgB,GAAG1F,WAAW,CAACoE,GAAG,CAAC,CAAC;MAC1C,MAAM/B,SAAS,GAAG,MAAMnC,iBAAiB,CAACoC,OAAO,CAACmD,SAAS,EAAEtF,KAAK,CAAC;MACnE,MAAMwF,cAAc,GAAG3F,WAAW,CAACoE,GAAG,CAAC,CAAC;MACxC,MAAMwB,eAAe,GAAGD,cAAc,GAAGD,gBAAgB;;MAEzD;MACA,MAAMG,gBAAgB,GAAG7F,WAAW,CAACoE,GAAG,CAAC,CAAC;MAC1C,MAAM5B,SAAS,GAAG,MAAMtC,iBAAiB,CAACuC,OAAO,CAACJ,SAAS,CAAC;MAC5D,MAAMyD,cAAc,GAAG9F,WAAW,CAACoE,GAAG,CAAC,CAAC;MACxC,MAAM2B,eAAe,GAAGD,cAAc,GAAGD,gBAAgB;;MAEzD;MACA1E,MAAM,CAACS,MAAM,CAACG,OAAO,CAACS,SAAS,EAAEiD,SAAS,CAAC,CAAC,CAACpE,IAAI,CAAC,CAAC,CAAC;;MAEpD;MACAiE,OAAO,CAACC,GAAG,CAAC,sBAAsBK,eAAe,CAACJ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACjEF,OAAO,CAACC,GAAG,CAAC,sBAAsBQ,eAAe,CAACP,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAEjE;MACArE,MAAM,CAACyE,eAAe,CAAC,CAACT,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;MAC5ChE,MAAM,CAAC4E,eAAe,CAAC,CAACZ,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,SAASjD,gBAAgBA,CAAC8D,MAAM,EAAE;EAChC,MAAMC,WAAW,GAAG,IAAIxB,KAAK,CAAC,GAAG,CAAC,CAACyB,IAAI,CAAC,CAAC,CAAC;;EAE1C;EACA,KAAK,MAAMC,IAAI,IAAIH,MAAM,EAAE;IACzBC,WAAW,CAACE,IAAI,CAAC,EAAE;EACrB;;EAEA;EACA,IAAIlE,OAAO,GAAG,CAAC;EACf,KAAK,MAAMmE,KAAK,IAAIH,WAAW,EAAE;IAC/B,IAAIG,KAAK,GAAG,CAAC,EAAE;MACb,MAAMC,WAAW,GAAGD,KAAK,GAAGJ,MAAM,CAAC1E,MAAM;MACzCW,OAAO,IAAIoE,WAAW,GAAGC,IAAI,CAACC,IAAI,CAACF,WAAW,CAAC;IACjD;EACF;EAEA,OAAOpE,OAAO;AAChB", "ignoreList": []}