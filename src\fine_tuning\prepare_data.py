"""
Prepare data for fine-tuning a compliance-specific language model.

This script processes compliance Q&A data and formats it for fine-tuning.
"""

import os
import json
import argparse
import random
from typing import List, Dict, Any

def load_framework_requirements(framework_dir: str) -> List[Dict[str, Any]]:
    """
    Load requirements for a specific framework.
    
    Args:
        framework_dir: Path to the framework data directory
        
    Returns:
        List of requirement dictionaries
    """
    requirements_file = os.path.join(framework_dir, 'requirements.json')
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('requirements', [])
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error loading requirements from {requirements_file}: {e}")
        return []

def generate_qa_pairs(requirements: List[Dict[str, Any]], framework_name: str) -> List[Dict[str, str]]:
    """
    Generate question-answer pairs from requirements.
    
    Args:
        requirements: List of requirement dictionaries
        framework_name: Name of the framework
        
    Returns:
        List of dictionaries with 'question' and 'answer' keys
    """
    qa_pairs = []
    
    for req in requirements:
        # Generate questions about the requirement
        questions = [
            f"What does {framework_name} say about {req['title'].lower()}?",
            f"What are the {framework_name} requirements for {req['title'].lower()}?",
            f"How does {framework_name} define {req['title'].lower()}?",
            f"What is {req['reference']} in {framework_name}?",
            f"Explain {req['reference']} of {framework_name}."
        ]
        
        # Create an answer that includes the reference, title, and text
        answer = f"{framework_name} {req['reference']} - {req['title']}: {req['text']}"
        
        # Add each question-answer pair
        for question in questions:
            qa_pairs.append({
                'question': question,
                'answer': answer
            })
        
        # Add category-specific questions if categories are available
        if 'categories' in req and req['categories']:
            for category in req['categories']:
                category_question = f"What does {framework_name} say about {category.replace('_', ' ')}?"
                qa_pairs.append({
                    'question': category_question,
                    'answer': answer
                })
    
    return qa_pairs

def generate_cross_framework_qa(frameworks_data: Dict[str, List[Dict[str, Any]]]) -> List[Dict[str, str]]:
    """
    Generate cross-framework question-answer pairs.
    
    Args:
        frameworks_data: Dictionary mapping framework names to requirements
        
    Returns:
        List of dictionaries with 'question' and 'answer' keys
    """
    qa_pairs = []
    
    # Get all unique categories across frameworks
    all_categories = set()
    for framework, requirements in frameworks_data.items():
        for req in requirements:
            if 'categories' in req and req['categories']:
                all_categories.update(req['categories'])
    
    # Generate questions comparing frameworks for each category
    for category in all_categories:
        category_display = category.replace('_', ' ')
        
        # Get requirements for this category from each framework
        framework_reqs = {}
        for framework, requirements in frameworks_data.items():
            framework_reqs[framework] = [req for req in requirements if 'categories' in req and category in req['categories']]
        
        # Only proceed if at least two frameworks have requirements for this category
        frameworks_with_reqs = [fw for fw, reqs in framework_reqs.items() if reqs]
        if len(frameworks_with_reqs) >= 2:
            for i in range(len(frameworks_with_reqs)):
                for j in range(i+1, len(frameworks_with_reqs)):
                    fw1 = frameworks_with_reqs[i]
                    fw2 = frameworks_with_reqs[j]
                    
                    # Generate comparison questions
                    questions = [
                        f"What are the differences between {fw1} and {fw2} for {category_display}?",
                        f"How do {fw1} and {fw2} compare regarding {category_display}?",
                        f"What are the key differences between {fw1} and {fw2} {category_display} requirements?"
                    ]
                    
                    # Generate a comparison answer
                    answer = f"Comparing {fw1} and {fw2} for {category_display}:\n\n"
                    
                    # Add requirements from first framework
                    answer += f"{fw1} requirements:\n"
                    for req in framework_reqs[fw1]:
                        answer += f"- {req['reference']}: {req['title']} - {req['text'][:100]}...\n"
                    
                    # Add requirements from second framework
                    answer += f"\n{fw2} requirements:\n"
                    for req in framework_reqs[fw2]:
                        answer += f"- {req['reference']}: {req['title']} - {req['text'][:100]}...\n"
                    
                    # Add key differences if known
                    if category == 'data_breach':
                        if ('GDPR' in [fw1, fw2]) and ('HIPAA' in [fw1, fw2]):
                            answer += "\nKey differences:\n"
                            answer += "- Timeline: GDPR requires notification within 72 hours, while HIPAA allows up to 60 days.\n"
                            answer += "- Scope: GDPR covers all personal data, while HIPAA only covers protected health information.\n"
                    
                    # Add each question-answer pair
                    for question in questions:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer
                        })
    
    return qa_pairs

def format_for_fine_tuning(qa_pairs: List[Dict[str, str]], output_format: str = 'jsonl') -> List[Dict[str, Any]]:
    """
    Format QA pairs for fine-tuning.
    
    Args:
        qa_pairs: List of dictionaries with 'question' and 'answer' keys
        output_format: Format to use ('jsonl' or 'chat')
        
    Returns:
        List of formatted examples
    """
    formatted_examples = []
    
    if output_format == 'jsonl':
        # Format for basic fine-tuning
        for pair in qa_pairs:
            formatted_examples.append({
                'prompt': f"Question: {pair['question']}\nAnswer:",
                'completion': f" {pair['answer']}"
            })
    elif output_format == 'chat':
        # Format for chat fine-tuning
        for pair in qa_pairs:
            formatted_examples.append({
                'messages': [
                    {'role': 'system', 'content': 'You are a compliance expert assistant that provides accurate and helpful information about regulatory frameworks.'},
                    {'role': 'user', 'content': pair['question']},
                    {'role': 'assistant', 'content': pair['answer']}
                ]
            })
    
    return formatted_examples

def main():
    parser = argparse.ArgumentParser(description='Prepare data for fine-tuning a compliance-specific language model')
    parser.add_argument('--data_dir', type=str, default='../ucia/data', help='Path to the data directory')
    parser.add_argument('--output_dir', type=str, default='./data', help='Path to the output directory')
    parser.add_argument('--output_format', type=str, default='jsonl', choices=['jsonl', 'chat'], help='Format of the output data')
    parser.add_argument('--train_split', type=float, default=0.8, help='Proportion of data to use for training')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load requirements for each framework
    frameworks_data = {}
    for framework in ['gdpr', 'hipaa', 'soc2']:
        framework_dir = os.path.join(args.data_dir, framework)
        if os.path.isdir(framework_dir):
            requirements = load_framework_requirements(framework_dir)
            if requirements:
                frameworks_data[framework.upper()] = requirements
                print(f"Loaded {len(requirements)} requirements for {framework.upper()}")
    
    # Generate QA pairs for each framework
    all_qa_pairs = []
    for framework, requirements in frameworks_data.items():
        framework_qa_pairs = generate_qa_pairs(requirements, framework)
        all_qa_pairs.extend(framework_qa_pairs)
        print(f"Generated {len(framework_qa_pairs)} QA pairs for {framework}")
    
    # Generate cross-framework QA pairs
    cross_framework_qa = generate_cross_framework_qa(frameworks_data)
    all_qa_pairs.extend(cross_framework_qa)
    print(f"Generated {len(cross_framework_qa)} cross-framework QA pairs")
    
    # Shuffle the QA pairs
    random.shuffle(all_qa_pairs)
    
    # Split into training and validation sets
    split_idx = int(len(all_qa_pairs) * args.train_split)
    train_qa_pairs = all_qa_pairs[:split_idx]
    val_qa_pairs = all_qa_pairs[split_idx:]
    
    # Format for fine-tuning
    train_examples = format_for_fine_tuning(train_qa_pairs, args.output_format)
    val_examples = format_for_fine_tuning(val_qa_pairs, args.output_format)
    
    # Save to files
    train_file = os.path.join(args.output_dir, f"train.{args.output_format}")
    val_file = os.path.join(args.output_dir, f"val.{args.output_format}")
    
    if args.output_format == 'jsonl':
        with open(train_file, 'w', encoding='utf-8') as f:
            for example in train_examples:
                f.write(json.dumps(example) + '\n')
        
        with open(val_file, 'w', encoding='utf-8') as f:
            for example in val_examples:
                f.write(json.dumps(example) + '\n')
    else:  # chat format
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_examples, f, indent=2)
        
        with open(val_file, 'w', encoding='utf-8') as f:
            json.dump(val_examples, f, indent=2)
    
    print(f"Saved {len(train_examples)} training examples to {train_file}")
    print(f"Saved {len(val_examples)} validation examples to {val_file}")

if __name__ == "__main__":
    main()
