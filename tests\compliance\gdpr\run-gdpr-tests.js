/**
 * Run GDPR Compliance Tests
 * 
 * This script runs the GDPR compliance tests and generates a report.
 */

const path = require('path');
const { createGdprFramework } = require('./gdpr-framework');
const { generateHtmlReport } = require('../framework/compliance-test-framework');

/**
 * Run the GDPR compliance tests
 */
async function runGdprTests() {
  console.log('Running GDPR Compliance Tests...');
  
  // Create the GDPR framework
  const framework = createGdprFramework();
  
  // Run the tests
  const results = await framework.runTests();
  
  // Generate the report
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const reportPath = path.join(__dirname, '..', '..', '..', 'test-results', 'compliance', `gdpr-report-${timestamp}.json`);
  await framework.saveReport(reportPath);
  
  // Generate the HTML report
  const htmlReportPath = path.join(__dirname, '..', '..', '..', 'test-results', 'compliance', `gdpr-report-${timestamp}.html`);
  await generateHtmlReport(results, htmlReportPath);
  
  console.log('GDPR Compliance Tests completed.');
  console.log(`Report saved to: ${reportPath}`);
  console.log(`HTML Report saved to: ${htmlReportPath}`);
}

// Run the tests if this script is executed directly
if (require.main === module) {
  runGdprTests().catch(error => {
    console.error('Error running GDPR tests:', error);
    process.exit(1);
  });
}

module.exports = {
  runGdprTests
};
