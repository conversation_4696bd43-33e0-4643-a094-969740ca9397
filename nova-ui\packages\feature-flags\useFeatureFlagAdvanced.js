/**
 * useFeatureFlagAdvanced Hook
 * 
 * An advanced hook for working with feature flags, including support for
 * overrides, fallbacks, and conditional logic.
 */

import { useContext, useMemo } from 'react';
import { ProductContext } from './ProductContext';
import { isFeatureEnabled } from './index';

/**
 * Advanced hook for checking if a feature is enabled
 * @param {object} options - Options for the hook
 * @param {string} [options.product] - The product to check (defaults to current product from context)
 * @param {string} options.category - The feature category
 * @param {string} options.feature - The feature name
 * @param {boolean} [options.override] - Override the feature flag value
 * @param {boolean} [options.fallback=false] - Fallback value if the feature flag is not found
 * @param {function} [options.condition] - Additional condition function that must return true for the feature to be enabled
 * @returns {boolean} - Whether the feature is enabled
 */
export function useFeatureFlagAdvanced({
  product: explicitProduct,
  category,
  feature,
  override,
  fallback = false,
  condition
}) {
  const { product: contextProduct } = useContext(ProductContext);
  
  // Use the explicit product if provided, otherwise use the product from context
  const product = explicitProduct || contextProduct;
  
  // If override is explicitly set (true or false), use that value
  if (typeof override === 'boolean') {
    return override;
  }
  
  // Check if the feature is enabled based on the feature flags
  const isEnabled = useMemo(() => {
    if (!product) {
      console.warn('No product found in ProductContext and no explicit product provided');
      return fallback;
    }
    
    const featureEnabled = isFeatureEnabled(product, category, feature);
    
    // If the feature is not found, use the fallback value
    if (typeof featureEnabled !== 'boolean') {
      return fallback;
    }
    
    // If the feature is enabled and there's a condition function, check the condition
    if (featureEnabled && typeof condition === 'function') {
      return condition();
    }
    
    return featureEnabled;
  }, [product, category, feature, fallback, condition]);
  
  return isEnabled;
}

/**
 * Hook to check if any of the specified features are enabled
 * @param {Array<object>} features - Array of feature objects with category and feature properties
 * @param {string} [product] - The product to check (defaults to current product from context)
 * @returns {boolean} - Whether any of the features are enabled
 */
export function useAnyFeatureEnabled(features, product) {
  return features.some(({ category, feature }) => 
    useFeatureFlagAdvanced({ product, category, feature })
  );
}

/**
 * Hook to check if all of the specified features are enabled
 * @param {Array<object>} features - Array of feature objects with category and feature properties
 * @param {string} [product] - The product to check (defaults to current product from context)
 * @returns {boolean} - Whether all of the features are enabled
 */
export function useAllFeaturesEnabled(features, product) {
  return features.every(({ category, feature }) => 
    useFeatureFlagAdvanced({ product, category, feature })
  );
}

export default useFeatureFlagAdvanced;
