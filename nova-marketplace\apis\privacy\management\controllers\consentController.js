/**
 * Consent Record Controller
 * 
 * This controller handles operations related to consent records.
 */

const { ConsentRecord } = require('../models');

// Get all consent records with pagination and filtering
const getAllConsentRecords = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build filter object
    const filter = {};
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.consentType) {
      filter.consentType = req.query.consentType;
    }
    
    if (req.query.dataSubjectId) {
      filter.dataSubjectId = req.query.dataSubjectId;
    }
    
    if (req.query.email) {
      filter.dataSubjectEmail = req.query.email;
    }
    
    if (req.query.search) {
      filter.$text = { $search: req.query.search };
    }
    
    // Build sort object
    const sort = {};
    
    if (req.query.sortBy) {
      sort[req.query.sortBy] = req.query.sortOrder === 'desc' ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by creation date descending
    }
    
    // Execute query with pagination
    const consentRecords = await ConsentRecord
      .find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit);
    
    // Get total count for pagination
    const total = await ConsentRecord.countDocuments(filter);
    
    res.json({
      data: consentRecords,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    next(error);
  }
};

// Get a specific consent record by ID
const getConsentRecordById = async (req, res, next) => {
  try {
    const consentRecord = await ConsentRecord.findById(req.params.id);
    
    if (!consentRecord) {
      const error = new Error('Consent record not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    res.json({
      data: consentRecord
    });
  } catch (error) {
    next(error);
  }
};

// Create a new consent record
const createConsentRecord = async (req, res, next) => {
  try {
    const consentRecord = new ConsentRecord(req.body);
    await consentRecord.save();
    
    res.status(201).json({
      data: consentRecord,
      message: 'Consent record created successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Update a consent record
const updateConsentRecord = async (req, res, next) => {
  try {
    const consentRecord = await ConsentRecord.findById(req.params.id);
    
    if (!consentRecord) {
      const error = new Error('Consent record not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // Update only the fields that are provided in the request body
    Object.keys(req.body).forEach(key => {
      consentRecord[key] = req.body[key];
    });
    
    await consentRecord.save();
    
    res.json({
      data: consentRecord,
      message: 'Consent record updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Withdraw consent
const withdrawConsent = async (req, res, next) => {
  try {
    const consentRecord = await ConsentRecord.findById(req.params.id);
    
    if (!consentRecord) {
      const error = new Error('Consent record not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    if (consentRecord.status === 'withdrawn') {
      const error = new Error('Consent has already been withdrawn');
      error.name = 'ValidationError';
      throw error;
    }
    
    consentRecord.status = 'withdrawn';
    consentRecord.withdrawalTimestamp = new Date();
    consentRecord.withdrawalMethod = req.body.withdrawalMethod;
    consentRecord.withdrawalReason = req.body.withdrawalReason;
    
    await consentRecord.save();
    
    res.json({
      data: consentRecord,
      message: 'Consent withdrawn successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Verify consent validity
const verifyConsentValidity = async (req, res, next) => {
  try {
    const consentRecord = await ConsentRecord.findById(req.params.id);
    
    if (!consentRecord) {
      const error = new Error('Consent record not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // Check if consent is valid
    const isValid = consentRecord.status === 'active' && !consentRecord.isExpired;
    
    res.json({
      data: {
        id: consentRecord._id,
        isValid,
        status: consentRecord.status,
        expiryDate: consentRecord.expiryDate,
        isExpired: consentRecord.isExpired,
        timeRemaining: consentRecord.timeRemaining
      },
      message: isValid ? 'Consent is valid' : 'Consent is not valid'
    });
  } catch (error) {
    next(error);
  }
};

// Get consent records by data subject
const getConsentRecordsByDataSubject = async (req, res, next) => {
  try {
    const dataSubjectId = req.params.dataSubjectId;
    
    const consentRecords = await ConsentRecord.find({ dataSubjectId });
    
    res.json({
      data: consentRecords
    });
  } catch (error) {
    next(error);
  }
};

// Get consent records by email
const getConsentRecordsByEmail = async (req, res, next) => {
  try {
    const email = req.params.email;
    
    const consentRecords = await ConsentRecord.find({ dataSubjectEmail: email });
    
    res.json({
      data: consentRecords
    });
  } catch (error) {
    next(error);
  }
};

// Generate a consent form
const generateConsentForm = async (req, res, next) => {
  try {
    const consentType = req.query.consentType;
    
    if (!consentType) {
      const error = new Error('Consent type is required');
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would generate a consent form based on the consent type
    // For now, we'll just return a mock consent form
    
    const consentForm = {
      title: `${consentType.charAt(0).toUpperCase() + consentType.slice(1)} Consent Form`,
      content: `This form is used to collect consent for ${consentType} purposes.`,
      version: '1.0',
      effectiveDate: new Date(),
      purposes: ['Purpose 1', 'Purpose 2'],
      dataCategories: ['Category 1', 'Category 2'],
      thirdParties: [
        {
          name: 'Third Party 1',
          purpose: 'Purpose 1',
          location: 'Location 1'
        }
      ],
      contactInformation: {
        name: 'Privacy Team',
        email: '<EMAIL>',
        phone: '******-123-4567'
      }
    };
    
    res.json({
      data: consentForm,
      message: 'Consent form generated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Verify consent proof
const verifyConsentProof = async (req, res, next) => {
  try {
    const { consentProof } = req.body;
    
    if (!consentProof) {
      const error = new Error('Consent proof is required');
      error.name = 'ValidationError';
      throw error;
    }
    
    // In a real implementation, this would verify the consent proof
    // For now, we'll just return a mock verification result
    
    res.json({
      data: {
        isValid: true,
        consentId: 'consent-123456',
        verificationTimestamp: new Date()
      },
      message: 'Consent proof verified successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Generate consent proof
const generateConsentProof = async (req, res, next) => {
  try {
    const { consentId, proofType } = req.body;
    
    if (!consentId) {
      const error = new Error('Consent ID is required');
      error.name = 'ValidationError';
      throw error;
    }
    
    if (!proofType) {
      const error = new Error('Proof type is required');
      error.name = 'ValidationError';
      throw error;
    }
    
    const consentRecord = await ConsentRecord.findById(consentId);
    
    if (!consentRecord) {
      const error = new Error('Consent record not found');
      error.name = 'NotFoundError';
      throw error;
    }
    
    // In a real implementation, this would generate a proof of consent
    // For now, we'll just return a mock proof
    
    res.json({
      data: {
        consentId: consentRecord._id,
        proofType,
        proof: `proof-${Date.now()}`,
        generationTimestamp: new Date()
      },
      message: 'Consent proof generated successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllConsentRecords,
  getConsentRecordById,
  createConsentRecord,
  updateConsentRecord,
  withdrawConsent,
  verifyConsentValidity,
  getConsentRecordsByDataSubject,
  getConsentRecordsByEmail,
  generateConsentForm,
  verifyConsentProof,
  generateConsentProof
};
