/**
 * CSDE CUDA Kernels Header
 * 
 * This file contains the declarations for the CUDA kernel implementations
 * of the Cyber-Safety Dominance Equation (CSDE).
 */

#ifndef CSDE_KERNELS_CUH
#define CSDE_KERNELS_CUH

#include <cuda_runtime.h>

/**
 * Tensor Product Kernel (N ⊗ G)
 * 
 * @param N Compliance data tensor
 * @param G Cloud data tensor
 * @param output Result tensor
 * @param n_dims Number of dimensions in N
 * @param g_dims Number of dimensions in G
 */
__global__ void tensor_product_kernel(const float* N, const float* G, float* output, 
                                     int n_dims, int g_dims);

/**
 * Fusion Operator Kernel ((N ⊗ G) ⊕ C)
 * 
 * @param NG Result of tensor product (N ⊗ G)
 * @param C Threat intelligence tensor
 * @param output Result tensor
 * @param ng_dims Number of dimensions in NG
 * @param c_dims Number of dimensions in C
 */
__global__ void fusion_operator_kernel(const float* NG, const float* C, float* output,
                                      int ng_dims, int c_dims);

/**
 * π10³ Scaling Kernel
 * 
 * @param fused Result of fusion operation
 * @param output Result tensor
 * @param total_dims Total number of dimensions
 */
__global__ void pi_cubed_scaling_kernel(const float* fused, float* output, int total_dims);

/**
 * φ-Gradient Descent Kernel
 * 
 * @param fused_tensor Fused tensor
 * @param gradients Gradient tensor
 * @param output Result tensor
 * @param total_dims Total number of dimensions
 * @param learning_rate Learning rate for gradient descent
 */
__global__ void phi_gradient_descent_kernel(const float* fused_tensor, float* gradients, 
                                           float* output, int total_dims, float learning_rate);

/**
 * Generate Remediation Actions Kernel
 * 
 * @param csde_values CSDE values
 * @param remediation_counts Number of remediation actions per threat
 * @param remediation_types Types of remediation actions
 * @param total_threats Total number of threats
 */
__global__ void generate_remediation_actions_kernel(const float* csde_values, 
                                                  int* remediation_counts,
                                                  int* remediation_types,
                                                  int total_threats);

// Host wrapper functions

/**
 * Launch Tensor Product Kernel
 * 
 * @param N Compliance data tensor
 * @param G Cloud data tensor
 * @param output Result tensor
 * @param n_dims Number of dimensions in N
 * @param g_dims Number of dimensions in G
 * @param stream CUDA stream
 * @return CUDA error code
 */
cudaError_t launch_tensor_product(const float* N, const float* G, float* output,
                                 int n_dims, int g_dims, cudaStream_t stream = 0);

/**
 * Launch Fusion Operator Kernel
 * 
 * @param NG Result of tensor product (N ⊗ G)
 * @param C Threat intelligence tensor
 * @param output Result tensor
 * @param ng_dims Number of dimensions in NG
 * @param c_dims Number of dimensions in C
 * @param stream CUDA stream
 * @return CUDA error code
 */
cudaError_t launch_fusion_operator(const float* NG, const float* C, float* output,
                                  int ng_dims, int c_dims, cudaStream_t stream = 0);

/**
 * Launch π10³ Scaling Kernel
 * 
 * @param fused Result of fusion operation
 * @param output Result tensor
 * @param total_dims Total number of dimensions
 * @param stream CUDA stream
 * @return CUDA error code
 */
cudaError_t launch_pi_cubed_scaling(const float* fused, float* output,
                                   int total_dims, cudaStream_t stream = 0);

/**
 * Launch φ-Gradient Descent Kernel
 * 
 * @param fused_tensor Fused tensor
 * @param gradients Gradient tensor
 * @param output Result tensor
 * @param total_dims Total number of dimensions
 * @param learning_rate Learning rate for gradient descent
 * @param stream CUDA stream
 * @return CUDA error code
 */
cudaError_t launch_phi_gradient_descent(const float* fused_tensor, float* gradients,
                                       float* output, int total_dims, float learning_rate,
                                       cudaStream_t stream = 0);

/**
 * Launch Generate Remediation Actions Kernel
 * 
 * @param csde_values CSDE values
 * @param remediation_counts Number of remediation actions per threat
 * @param remediation_types Types of remediation actions
 * @param total_threats Total number of threats
 * @param stream CUDA stream
 * @return CUDA error code
 */
cudaError_t launch_generate_remediation_actions(const float* csde_values,
                                              int* remediation_counts,
                                              int* remediation_types,
                                              int total_threats,
                                              cudaStream_t stream = 0);

#endif // CSDE_KERNELS_CUH
