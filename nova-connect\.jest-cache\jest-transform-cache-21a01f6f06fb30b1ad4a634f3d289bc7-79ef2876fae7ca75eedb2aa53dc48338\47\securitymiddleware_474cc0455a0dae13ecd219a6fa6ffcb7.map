{"version": 3, "names": ["RateLimiter", "InputValidator", "require", "createRateLimiter", "options", "rateLimiter", "rateLimiterMiddleware", "req", "res", "next", "isAllowed", "<PERSON><PERSON><PERSON><PERSON>", "maxRequests", "getRemainingRequests", "Math", "floor", "getResetTime", "ceil", "Date", "now", "status", "json", "error", "message", "createInputValidator", "inputValidatorMiddleware", "param", "params", "isXssSafe", "isCommandSafe", "query", "body", "validateObject", "obj", "path", "key", "value", "currentPath", "Error", "createSecurityHeaders", "securityHeadersMiddleware", "module", "exports"], "sources": ["security-middleware.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Security Middleware\n * \n * This module provides security middleware for the connector API.\n */\n\nconst { RateLimiter, InputValidator } = require('../../src/security');\n\n/**\n * Create rate limiting middleware\n * @param {Object} options - Rate limiter options\n * @returns {Function} - Express middleware\n */\nfunction createRateLimiter(options = {}) {\n  const rateLimiter = new RateLimiter(options);\n  \n  return function rateLimiterMiddleware(req, res, next) {\n    if (rateLimiter.isAllowed(req)) {\n      // Add rate limit headers\n      res.setHeader('X-RateLimit-Limit', rateLimiter.options.maxRequests);\n      res.setHeader('X-RateLimit-Remaining', rateLimiter.getRemainingRequests(req));\n      res.setHeader('X-RateLimit-Reset', Math.floor(rateLimiter.getResetTime(req) / 1000));\n      \n      next();\n    } else {\n      // Rate limit exceeded\n      res.setHeader('X-RateLimit-Limit', rateLimiter.options.maxRequests);\n      res.setHeader('X-RateLimit-Remaining', 0);\n      res.setHeader('X-RateLimit-Reset', Math.floor(rateLimiter.getResetTime(req) / 1000));\n      res.setHeader('Retry-After', Math.ceil((rateLimiter.getResetTime(req) - Date.now()) / 1000));\n      \n      res.status(429).json({\n        error: 'Too many requests',\n        message: 'Rate limit exceeded. Please try again later.'\n      });\n    }\n  };\n}\n\n/**\n * Create input validation middleware\n * @returns {Function} - Express middleware\n */\nfunction createInputValidator() {\n  return function inputValidatorMiddleware(req, res, next) {\n    try {\n      // Validate URL parameters\n      for (const param in req.params) {\n        if (!InputValidator.isXssSafe(req.params[param]) || \n            !InputValidator.isCommandSafe(req.params[param])) {\n          return res.status(400).json({\n            error: 'Invalid parameter',\n            message: `Parameter '${param}' contains invalid characters`\n          });\n        }\n      }\n      \n      // Validate query parameters\n      for (const param in req.query) {\n        if (!InputValidator.isXssSafe(req.query[param]) || \n            !InputValidator.isCommandSafe(req.query[param])) {\n          return res.status(400).json({\n            error: 'Invalid parameter',\n            message: `Query parameter '${param}' contains invalid characters`\n          });\n        }\n      }\n      \n      // Validate request body\n      if (req.body && typeof req.body === 'object') {\n        const validateObject = (obj, path = '') => {\n          for (const key in obj) {\n            const value = obj[key];\n            const currentPath = path ? `${path}.${key}` : key;\n            \n            if (typeof value === 'string') {\n              if (!InputValidator.isXssSafe(value) || \n                  !InputValidator.isCommandSafe(value)) {\n                throw new Error(`Field '${currentPath}' contains invalid characters`);\n              }\n            } else if (typeof value === 'object' && value !== null) {\n              validateObject(value, currentPath);\n            }\n          }\n        };\n        \n        try {\n          validateObject(req.body);\n        } catch (error) {\n          return res.status(400).json({\n            error: 'Invalid request body',\n            message: error.message\n          });\n        }\n      }\n      \n      next();\n    } catch (error) {\n      next(error);\n    }\n  };\n}\n\n/**\n * Create security headers middleware\n * @returns {Function} - Express middleware\n */\nfunction createSecurityHeaders() {\n  return function securityHeadersMiddleware(req, res, next) {\n    // Set security headers\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n    res.setHeader('X-Frame-Options', 'DENY');\n    res.setHeader('X-XSS-Protection', '1; mode=block');\n    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');\n    res.setHeader('Content-Security-Policy', \"default-src 'self'\");\n    res.setHeader('Referrer-Policy', 'no-referrer');\n    \n    next();\n  };\n}\n\nmodule.exports = {\n  createRateLimiter,\n  createInputValidator,\n  createSecurityHeaders\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA,WAAW;EAAEC;AAAe,CAAC,GAAGC,OAAO,CAAC,oBAAoB,CAAC;;AAErE;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;EACvC,MAAMC,WAAW,GAAG,IAAIL,WAAW,CAACI,OAAO,CAAC;EAE5C,OAAO,SAASE,qBAAqBA,CAACC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACpD,IAAIJ,WAAW,CAACK,SAAS,CAACH,GAAG,CAAC,EAAE;MAC9B;MACAC,GAAG,CAACG,SAAS,CAAC,mBAAmB,EAAEN,WAAW,CAACD,OAAO,CAACQ,WAAW,CAAC;MACnEJ,GAAG,CAACG,SAAS,CAAC,uBAAuB,EAAEN,WAAW,CAACQ,oBAAoB,CAACN,GAAG,CAAC,CAAC;MAC7EC,GAAG,CAACG,SAAS,CAAC,mBAAmB,EAAEG,IAAI,CAACC,KAAK,CAACV,WAAW,CAACW,YAAY,CAACT,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;MAEpFE,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACL;MACAD,GAAG,CAACG,SAAS,CAAC,mBAAmB,EAAEN,WAAW,CAACD,OAAO,CAACQ,WAAW,CAAC;MACnEJ,GAAG,CAACG,SAAS,CAAC,uBAAuB,EAAE,CAAC,CAAC;MACzCH,GAAG,CAACG,SAAS,CAAC,mBAAmB,EAAEG,IAAI,CAACC,KAAK,CAACV,WAAW,CAACW,YAAY,CAACT,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;MACpFC,GAAG,CAACG,SAAS,CAAC,aAAa,EAAEG,IAAI,CAACG,IAAI,CAAC,CAACZ,WAAW,CAACW,YAAY,CAACT,GAAG,CAAC,GAAGW,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;MAE5FX,GAAG,CAACY,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;QACnBC,KAAK,EAAE,mBAAmB;QAC1BC,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC9B,OAAO,SAASC,wBAAwBA,CAAClB,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACvD,IAAI;MACF;MACA,KAAK,MAAMiB,KAAK,IAAInB,GAAG,CAACoB,MAAM,EAAE;QAC9B,IAAI,CAAC1B,cAAc,CAAC2B,SAAS,CAACrB,GAAG,CAACoB,MAAM,CAACD,KAAK,CAAC,CAAC,IAC5C,CAACzB,cAAc,CAAC4B,aAAa,CAACtB,GAAG,CAACoB,MAAM,CAACD,KAAK,CAAC,CAAC,EAAE;UACpD,OAAOlB,GAAG,CAACY,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;YAC1BC,KAAK,EAAE,mBAAmB;YAC1BC,OAAO,EAAE,cAAcG,KAAK;UAC9B,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,KAAK,MAAMA,KAAK,IAAInB,GAAG,CAACuB,KAAK,EAAE;QAC7B,IAAI,CAAC7B,cAAc,CAAC2B,SAAS,CAACrB,GAAG,CAACuB,KAAK,CAACJ,KAAK,CAAC,CAAC,IAC3C,CAACzB,cAAc,CAAC4B,aAAa,CAACtB,GAAG,CAACuB,KAAK,CAACJ,KAAK,CAAC,CAAC,EAAE;UACnD,OAAOlB,GAAG,CAACY,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;YAC1BC,KAAK,EAAE,mBAAmB;YAC1BC,OAAO,EAAE,oBAAoBG,KAAK;UACpC,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAInB,GAAG,CAACwB,IAAI,IAAI,OAAOxB,GAAG,CAACwB,IAAI,KAAK,QAAQ,EAAE;QAC5C,MAAMC,cAAc,GAAGA,CAACC,GAAG,EAAEC,IAAI,GAAG,EAAE,KAAK;UACzC,KAAK,MAAMC,GAAG,IAAIF,GAAG,EAAE;YACrB,MAAMG,KAAK,GAAGH,GAAG,CAACE,GAAG,CAAC;YACtB,MAAME,WAAW,GAAGH,IAAI,GAAG,GAAGA,IAAI,IAAIC,GAAG,EAAE,GAAGA,GAAG;YAEjD,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;cAC7B,IAAI,CAACnC,cAAc,CAAC2B,SAAS,CAACQ,KAAK,CAAC,IAChC,CAACnC,cAAc,CAAC4B,aAAa,CAACO,KAAK,CAAC,EAAE;gBACxC,MAAM,IAAIE,KAAK,CAAC,UAAUD,WAAW,+BAA+B,CAAC;cACvE;YACF,CAAC,MAAM,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;cACtDJ,cAAc,CAACI,KAAK,EAAEC,WAAW,CAAC;YACpC;UACF;QACF,CAAC;QAED,IAAI;UACFL,cAAc,CAACzB,GAAG,CAACwB,IAAI,CAAC;QAC1B,CAAC,CAAC,OAAOT,KAAK,EAAE;UACd,OAAOd,GAAG,CAACY,MAAM,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC;YAC1BC,KAAK,EAAE,sBAAsB;YAC7BC,OAAO,EAAED,KAAK,CAACC;UACjB,CAAC,CAAC;QACJ;MACF;MAEAd,IAAI,CAAC,CAAC;IACR,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdb,IAAI,CAACa,KAAK,CAAC;IACb;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASiB,qBAAqBA,CAAA,EAAG;EAC/B,OAAO,SAASC,yBAAyBA,CAACjC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAE;IACxD;IACAD,GAAG,CAACG,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;IAClDH,GAAG,CAACG,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC;IACxCH,GAAG,CAACG,SAAS,CAAC,kBAAkB,EAAE,eAAe,CAAC;IAClDH,GAAG,CAACG,SAAS,CAAC,2BAA2B,EAAE,qCAAqC,CAAC;IACjFH,GAAG,CAACG,SAAS,CAAC,yBAAyB,EAAE,oBAAoB,CAAC;IAC9DH,GAAG,CAACG,SAAS,CAAC,iBAAiB,EAAE,aAAa,CAAC;IAE/CF,IAAI,CAAC,CAAC;EACR,CAAC;AACH;AAEAgC,MAAM,CAACC,OAAO,GAAG;EACfvC,iBAAiB;EACjBqB,oBAAoB;EACpBe;AACF,CAAC", "ignoreList": []}