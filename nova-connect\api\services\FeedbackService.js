/**
 * Feedback Service
 * 
 * This service handles user feedback collection and management.
 */

const mongoose = require('mongoose');
const { ValidationError, NotFoundError } = require('../utils/errors');
const logger = require('../utils/logger');

class FeedbackService {
  constructor() {
    // Define feedback schema
    const feedbackSchema = new mongoose.Schema({
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      type: {
        type: String,
        enum: ['general', 'feature', 'bug', 'suggestion', 'usability', 'performance', 'other'],
        required: true
      },
      component: {
        type: String,
        required: true
      },
      rating: {
        type: Number,
        min: 1,
        max: 5,
        required: true
      },
      title: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
      },
      description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 5000
      },
      metadata: {
        type: Map,
        of: mongoose.Schema.Types.Mixed,
        default: {}
      },
      status: {
        type: String,
        enum: ['new', 'in_review', 'planned', 'in_progress', 'completed', 'declined'],
        default: 'new'
      },
      response: {
        type: String,
        trim: true,
        maxlength: 5000
      },
      tags: [{
        type: String,
        trim: true
      }],
      createdAt: {
        type: Date,
        default: Date.now
      },
      updatedAt: {
        type: Date,
        default: Date.now
      }
    });

    // Add index for efficient querying
    feedbackSchema.index({ userId: 1, createdAt: -1 });
    feedbackSchema.index({ component: 1, type: 1 });
    feedbackSchema.index({ status: 1 });
    feedbackSchema.index({ tags: 1 });

    // Create model if it doesn't exist
    this.FeedbackModel = mongoose.models.Feedback || mongoose.model('Feedback', feedbackSchema);
  }

  /**
   * Submit feedback
   * 
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise<Object>} - Created feedback
   */
  async submitFeedback(feedbackData) {
    try {
      logger.info('Submitting feedback', { type: feedbackData.type, component: feedbackData.component });

      // Create feedback
      const feedback = new this.FeedbackModel(feedbackData);
      await feedback.save();

      logger.info('Feedback submitted successfully', { feedbackId: feedback._id });

      return feedback;
    } catch (error) {
      logger.error('Error submitting feedback', error);
      throw error;
    }
  }

  /**
   * Get feedback by ID
   * 
   * @param {string} feedbackId - Feedback ID
   * @returns {Promise<Object>} - Feedback
   */
  async getFeedbackById(feedbackId) {
    try {
      const feedback = await this.FeedbackModel.findById(feedbackId);

      if (!feedback) {
        throw new NotFoundError(`Feedback with ID ${feedbackId} not found`);
      }

      return feedback;
    } catch (error) {
      logger.error(`Error getting feedback with ID ${feedbackId}`, error);
      throw error;
    }
  }

  /**
   * Get feedback by user
   * 
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Feedback list with pagination
   */
  async getFeedbackByUser(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        type,
        component,
        status
      } = options;

      // Build query
      const query = { userId };

      if (type) {
        query.type = type;
      }

      if (component) {
        query.component = component;
      }

      if (status) {
        query.status = status;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build sort
      const sort = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query
      const [feedback, total] = await Promise.all([
        this.FeedbackModel.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        this.FeedbackModel.countDocuments(query)
      ]);

      return {
        feedback,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error(`Error getting feedback for user ${userId}`, error);
      throw error;
    }
  }

  /**
   * Get all feedback
   * 
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Feedback list with pagination
   */
  async getAllFeedback(options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        type,
        component,
        status,
        tag,
        search
      } = options;

      // Build query
      const query = {};

      if (type) {
        query.type = type;
      }

      if (component) {
        query.component = component;
      }

      if (status) {
        query.status = status;
      }

      if (tag) {
        query.tags = tag;
      }

      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Build sort
      const sort = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query
      const [feedback, total] = await Promise.all([
        this.FeedbackModel.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        this.FeedbackModel.countDocuments(query)
      ]);

      return {
        feedback,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting all feedback', error);
      throw error;
    }
  }

  /**
   * Update feedback status
   * 
   * @param {string} feedbackId - Feedback ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} - Updated feedback
   */
  async updateFeedbackStatus(feedbackId, updateData) {
    try {
      const { status, response } = updateData;

      const feedback = await this.FeedbackModel.findById(feedbackId);

      if (!feedback) {
        throw new NotFoundError(`Feedback with ID ${feedbackId} not found`);
      }

      // Update fields
      feedback.status = status;
      
      if (response) {
        feedback.response = response;
      }
      
      feedback.updatedAt = new Date();

      // Save changes
      await feedback.save();

      return feedback;
    } catch (error) {
      logger.error(`Error updating feedback status for ID ${feedbackId}`, error);
      throw error;
    }
  }

  /**
   * Get feedback statistics
   * 
   * @returns {Promise<Object>} - Feedback statistics
   */
  async getFeedbackStatistics() {
    try {
      const [
        totalCount,
        typeStats,
        componentStats,
        statusStats,
        ratingStats
      ] = await Promise.all([
        this.FeedbackModel.countDocuments(),
        this.FeedbackModel.aggregate([
          { $group: { _id: '$type', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        this.FeedbackModel.aggregate([
          { $group: { _id: '$component', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        this.FeedbackModel.aggregate([
          { $group: { _id: '$status', count: { $sum: 1 } } },
          { $sort: { count: -1 } }
        ]),
        this.FeedbackModel.aggregate([
          { $group: { _id: '$rating', count: { $sum: 1 } } },
          { $sort: { _id: 1 } }
        ])
      ]);

      // Calculate average rating
      const totalRating = ratingStats.reduce((sum, stat) => sum + (stat._id * stat.count), 0);
      const totalRatingCount = ratingStats.reduce((sum, stat) => sum + stat.count, 0);
      const averageRating = totalRatingCount > 0 ? totalRating / totalRatingCount : 0;

      return {
        totalCount,
        typeStats: typeStats.map(stat => ({ type: stat._id, count: stat.count })),
        componentStats: componentStats.map(stat => ({ component: stat._id, count: stat.count })),
        statusStats: statusStats.map(stat => ({ status: stat._id, count: stat.count })),
        ratingStats: ratingStats.map(stat => ({ rating: stat._id, count: stat.count })),
        averageRating: parseFloat(averageRating.toFixed(2))
      };
    } catch (error) {
      logger.error('Error getting feedback statistics', error);
      throw error;
    }
  }
}

module.exports = FeedbackService;
