"""
Example script demonstrating the LocalAlphaFoldEngine with various configurations.

This script shows how to use the LocalAlphaFoldEngine with different presets
and custom configurations, including consciousness optimization.
"""
import os
import sys
import logging
import argparse
from pathlib import Path

# Add parent directory to path to import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.folding_engines import LocalAlphaFoldEngine, create_engine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_prediction(sequence: str, config: dict, output_dir: str):
    """Run prediction with the given configuration."""
    logger.info(f"Running prediction with config: {config}")
    
    try:
        # Create engine instance
        engine = LocalAlphaFoldEngine(config=config)
        
        # Run prediction
        result = engine.predict(sequence)
        
        # Log results
        logger.info(f"Prediction completed in {result['processing_time_seconds']:.2f} seconds")
        logger.info(f"Output PDB: {result['pdb_path']}")
        
        # Log GPU info
        gpu_info = result['gpu_info']
        logger.info("GPU Memory Usage:")
        logger.info(f"  Before: {gpu_info['before_prediction']['used_mb']}MB / {gpu_info['before_prediction']['total_mb']}MB")
        logger.info(f"  After:  {gpu_info['after_prediction']['used_mb']}MB / {gpu_info['after_prediction']['total_mb']}MB")
        logger.info(f"  Batch size: {gpu_info['batch_size']}")
        
        return result
        
    except Exception as e:
        logger.error(f"Prediction failed: {str(e)}", exc_info=True)
        raise

def main():
    """Main function to run the example."""
    parser = argparse.ArgumentParser(description='Run LocalAlphaFoldEngine with different configurations')
    parser.add_argument('--sequence', type=str, default='ACDEFGHIKLMNPQRSTVWY',
                       help='Protein sequence to predict')
    parser.add_argument('--output-dir', type=str, default='./output',
                       help='Output directory for results')
    parser.add_argument('--alphafold-path', type=str, required=True,
                       help='Path to local AlphaFold installation')
    parser.add_argument('--gpu-id', type=int, default=0,
                       help='GPU device ID to use')
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Example configurations
    configs = {
        'standard': {
            'mode': 'standard',
            'alphafold_path': args.alphafold_path,
            'output_dir': os.path.join(args.output_dir, 'standard'),
            'gpu_id': args.gpu_id,
        },
        'therapeutic': {
            'mode': 'therapeutic',
            'alphafold_path': args.alphafold_path,
            'output_dir': os.path.join(args.output_dir, 'therapeutic'),
            'gpu_id': args.gpu_id,
            'psi_optimization': True,
            'fib_constraints': {
                'enabled': True,
                'tolerance': 0.1
            },
            'model_preset': 'monomer_ptm',
            'db_preset': 'full_dbs',
        },
        'speed': {
            'mode': 'speed',
            'alphafold_path': args.alphafold_path,
            'output_dir': os.path.join(args.output_dir, 'speed'),
            'gpu_id': args.gpu_id,
            'use_precomputed_msas': True,
            'num_predictions': 1,
        }
    }
    
    # Run predictions with different configurations
    results = {}
    for name, config in configs.items():
        logger.info(f"\n{'='*40}")
        logger.info(f"Running prediction with {name} configuration")
        logger.info(f"{'='*40}")
        
        try:
            results[name] = run_prediction(args.sequence, config, args.output_dir)
            logger.info(f"{name} prediction completed successfully")
        except Exception as e:
            logger.error(f"{name} prediction failed: {str(e)}")
    
    logger.info("\nAll predictions completed!")
    return results

if __name__ == "__main__":
    main()
