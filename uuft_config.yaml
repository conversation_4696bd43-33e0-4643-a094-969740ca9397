# Configuration for Universal Unified Field Theory (UUFT) Engine

environment: "docker"  # options: docker | gcp

resource_allocation:
  method: entropy_weighted            # Options: 'equal', 'entropy_weighted', 'adaptive'
  critical_task_weight: 1.25          # Multiplier for critical tasks (baseline 1.0)
  standard_task_floor: 0.35           # Minimum acceptable completion % for standard tasks
  feedback_loop_enabled: true         # Dynamic feedback to rebalance on underperformance
  optimization_strategy: pareto       # Options: 'weighted_sum', 'pareto', 'custom_curve'

pattern_preservation:
  use_skip_connections: true
  skip_connection_weight: 0.75        # Higher values = more preservation
  attention_enabled: true             # Enable domain-aware attention for pattern mapping
  attention_type: cross_domain        # Options: 'self', 'cross_domain', 'multi_head'
  contrastive_loss_weight: 0.6        # Helps preserve feature integrity

pi10_scaling:
  enabled: true
  base_multiplier: 3141.59            # π * 10³
  dynamic_scaling_enabled: true
  variance_threshold: 0.05            # Adjust scaling if variance exceeds this
  scaling_factor_adjustment: adaptive # Options: 'static', 'adaptive', 'inverse_variance'

fusion:
  operator_chain: "(A ⊗ B ⊕ C) × π"
  normalization: "layer_norm"         # Options: 'batch_norm', 'layer_norm', 'none'
  domain_encoders:
    cybersecurity: 0.618              # Golden ratio for cybersecurity
    financial: 0.667                  # 2/3 for financial
    healthcare: 0.6                   # 3/5 for healthcare
    physics: 0.577                    # 1/√3 for physics
  feature_bifurcation: true           # Split processing into content and structure paths

logging:
  log_level: debug                    # Options: 'debug', 'info', 'warn', 'error'
  save_outputs: true
  output_dir: "/logs/uuft_test_runs"
  versioning_enabled: true

hardware:
  use_gpu: true                       # For GCP deployment
  parallel_processing: true
  max_threads: 16

metadata:
  experiment_id: "uuft-test-004"
  tester: "Auggie"
  timestamp: "2025-05-10T15:00:00Z"
  notes: "Integrated Carl's optimizations + parameterized scaling"
