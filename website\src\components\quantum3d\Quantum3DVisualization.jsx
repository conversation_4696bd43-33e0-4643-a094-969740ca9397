import React, { Suspense, useRef, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Stars, Environment } from '@react-three/drei';
import * as THREE from 'three';
import { useQuantumField } from '../../contexts/QuantumFieldContext';
import QuantumStateSphere from './QuantumStateSphere';

// Scene setup for better performance
const SceneSetup = ({ children }) => {
  const { gl } = useThree();
  
  // Configure renderer
  useMemo(() => {
    gl.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    gl.shadowMap.enabled = true;
    gl.shadowMap.type = THREE.PCFSoftShadowMap;
    gl.toneMapping = THREE.ACESFilmicToneMapping;
    gl.toneMappingExposure = 1.0;
  }, [gl]);
  
  return (
    <>
      <color attach="background" args={['#0a0b1e']} />
      <fog attach="fog" args={['#0a0b1e', 10, 30]} />
      <ambientLight intensity={0.25} />
      <pointLight position={[10, 10, 10]} intensity={1} castShadow />
      <Stars radius={100} depth={50} count={5000} factor={4} saturation={0} fade speed={1} />
      <Environment preset="night" />
      {children}
    </>
  );
};

// Component to handle quantum state visualization
const QuantumStateVisualization = () => {
  const { quantumData, entropyHistory } = useQuantumField();
  const groupRef = useRef();
  
  // Process quantum data for visualization
  const quantumStates = useMemo(() => {
    if (!quantumData) return [];
    
    return [
      {
        id: 'current',
        position: [0, 0, 0],
        entropy: quantumData.entropy,
        coherence: quantumData.coherence,
        color: '#8a2be2'
      },
      // Add historical states or related quantum states
      ...entropyHistory
        .filter((_, i) => i % 5 === 0) // Sample history for performance
        .map((entry, i) => ({
          id: `history-${i}`,
          position: [
            Math.sin(i) * 5,
            Math.cos(i) * 5,
            Math.sin(i * 0.5) * 3
          ],
          entropy: entry.entropy,
          coherence: entry.coherence || 0.5,
          color: '#4a90e2'
        }))
    ];
  }, [quantumData, entropyHistory]);
  
  // Animate the scene
  useFrame(({ clock }) => {
    if (groupRef.current) {
      // Subtle overall animation
      groupRef.current.rotation.y = clock.getElapsedTime() * 0.05;
    }
  });
  
  return (
    <group ref={groupRef}>
      {quantumStates.map((state) => (
        <QuantumStateSphere
          key={state.id}
          position={state.position}
          entropy={state.entropy}
          coherence={state.coherence}
          color={state.color}
        />
      ))}
      
      {/* Connection lines between quantum states */}
      <Connections states={quantumStates} />
    </group>
  );
};

// Component to draw lines between quantum states
const Connections = ({ states }) => {
  const lines = useMemo(() => {
    const result = [];
    if (states.length < 2) return result;
    
    // Connect each state to a few others
    for (let i = 0; i < states.length - 1; i++) {
      const start = states[i];
      const end = states[(i + 1) % states.length];
      
      result.push({
        start: start.position,
        end: end.position,
        opacity: Math.min(start.coherence, end.coherence) * 0.5
      });
    }
    
    return result;
  }, [states]);
  
  return (
    <group>
      {lines.map((line, i) => (
        <line key={`line-${i}`}>
          <bufferGeometry
            attach="geometry"
            onUpdate={(self) => {
              self.setFromPoints([
                new THREE.Vector3(...line.start),
                new THREE.Vector3(...line.end)
              ]);
            }}
          />
          <lineBasicMaterial
            attach="material"
            color="#8a2be2"
            opacity={line.opacity}
            transparent
          />
        </line>
      ))}
    </group>
  );
};

// Main 3D Visualization Component
const Quantum3DVisualization = ({ className }) => {
  const canvasRef = useRef();
  
  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current) {
        const { width, height } = canvasRef.current.getBoundingClientRect();
        canvasRef.current.setAttribute('width', width);
        canvasRef.current.setAttribute('height', height);
      }
    };
    
    window.addEventListener('resize', handleResize);
    handleResize();
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  return (
    <div 
      className={`quantum-3d-container ${className || ''}`}
      style={{
        width: '100%',
        height: '600px',
        position: 'relative',
        borderRadius: '8px',
        overflow: 'hidden',
        background: '#0a0b1e'
      }}
    >
      <Canvas
        ref={canvasRef}
        shadows
        dpr={[1, 2]}
        camera={{ position: [0, 0, 15], fov: 45 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: 'high-performance'
        }}
        onCreated={({ gl }) => {
          gl.setClearColor('#0a0b1e', 1);
        }}
      >
        <Suspense fallback={null}>
          <SceneSetup>
            <QuantumStateVisualization />
            <OrbitControls 
              enableZoom={true}
              enablePan={true}
              enableRotate={true}
              maxDistance={30}
              minDistance={5}
            />
          </SceneSetup>
        </Suspense>
      </Canvas>
      
      {/* Loading overlay */}
      <div className="loading-overlay">
        <div className="loading-spinner" />
        <div>Initializing Quantum Visualization...</div>
      </div>
    </div>
  );
};

export default React.memo(Quantum3DVisualization);
