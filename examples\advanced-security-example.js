/**
 * Advanced Security Example
 *
 * This example demonstrates how to use the advanced security features
 * for the Finite Universe Principle defense system, including multi-factor
 * authentication, IP-based access control, and advanced threat detection.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Security components
  createRBAC,
  createAuditLogger,
  createSecureStorage,
  createMFAService,
  createIPAccessControl,
  createThreatDetector,
  createSecurityComponents
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Multi-Factor Authentication
 * 
 * This example demonstrates how to use the multi-factor authentication service.
 */
async function example1() {
  console.log('\n=== Example 1: Multi-Factor Authentication ===\n');

  // Create MFA service
  const mfaService = createMFAService({
    enableLogging: true,
    tokenExpiration: 3600, // 1 hour
    maxFailedAttempts: 5,
    lockoutDuration: 900, // 15 minutes
    requiredFactors: 2,
    supportedFactors: ['password', 'totp', 'email']
  });

  // Register event listeners
  mfaService.on('user-registered', (data) => {
    console.log(`User registered: ${data.username}`);
  });

  mfaService.on('factor-registered', (data) => {
    console.log(`Factor registered: ${data.factorType} for user ${data.username}`);
  });

  mfaService.on('auth-initiated', (data) => {
    console.log(`Authentication initiated: ${data.username} (Session: ${data.sessionId})`);
  });

  mfaService.on('factor-verified', (data) => {
    console.log(`Factor verified: ${data.factorType} for user ${data.username}`);
  });

  mfaService.on('auth-completed', (data) => {
    console.log(`Authentication completed: ${data.username}`);
  });

  // Register a user
  console.log('\nRegistering user:');
  
  const user = mfaService.registerUser({
    username: 'john.doe',
    factors: {}
  });
  
  console.log('User registered:', user);
  
  // Register password factor
  console.log('\nRegistering password factor:');
  
  const passwordData = {
    hash: '5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8', // 'password'
    salt: 'salt123'
  };
  
  mfaService.registerFactor('john.doe', 'password', passwordData);
  
  // Register TOTP factor
  console.log('\nRegistering TOTP factor:');
  
  const totpData = {
    secret: 'JBSWY3DPEHPK3PXP'
  };
  
  mfaService.registerFactor('john.doe', 'totp', totpData);
  
  // Initiate authentication
  console.log('\nInitiating authentication:');
  
  const authSession = mfaService.initiateAuth('john.doe');
  console.log('Authentication session:', authSession);
  
  // Verify password factor
  console.log('\nVerifying password factor:');
  
  const passwordResult = mfaService.verifyFactor(authSession.sessionId, 'password', {
    password: 'password'
  });
  
  console.log('Password verification result:', passwordResult);
  
  // Verify TOTP factor
  console.log('\nVerifying TOTP factor:');
  
  const totpResult = mfaService.verifyFactor(authSession.sessionId, 'totp', {
    code: '123456' // In a real implementation, this would be a valid TOTP code
  });
  
  console.log('TOTP verification result:', totpResult);
  
  // Verify token
  if (totpResult.token) {
    console.log('\nVerifying token:');
    
    const tokenResult = mfaService.verifyToken(totpResult.token);
    console.log('Token verification result:', tokenResult);
    
    // Invalidate token
    console.log('\nInvalidating token:');
    
    const invalidateResult = mfaService.invalidateToken(totpResult.token);
    console.log('Token invalidation result:', invalidateResult);
  }

  // Dispose resources
  mfaService.dispose();
}

/**
 * Example 2: IP-Based Access Control
 * 
 * This example demonstrates how to use the IP-based access control service.
 */
async function example2() {
  console.log('\n=== Example 2: IP-Based Access Control ===\n');

  // Create IP access control
  const ipAccessControl = createIPAccessControl({
    enableLogging: true,
    defaultPolicy: 'deny',
    whitelistEnabled: true,
    blacklistEnabled: true,
    rateLimitEnabled: true,
    geoRestrictionEnabled: false,
    rateLimitWindow: 60000, // 1 minute
    rateLimitMax: 100, // 100 requests per minute
    rateLimitBurstMax: 200 // 200 requests burst
  });

  // Register event listeners
  ipAccessControl.on('blacklist-hit', (data) => {
    console.log(`Blacklist hit: ${data.ip}`);
  });

  ipAccessControl.on('rate-limit-hit', (data) => {
    console.log(`Rate limit hit: ${data.ip} (${data.count}/${data.limit})`);
  });

  // Add IPs to whitelist
  console.log('\nAdding IPs to whitelist:');
  
  ipAccessControl.addToWhitelist('***********');
  ipAccessControl.addToWhitelist('********');
  
  // Add IPs to blacklist
  console.log('\nAdding IPs to blacklist:');
  
  ipAccessControl.addToBlacklist('*******');
  ipAccessControl.addToBlacklist('*******');
  
  // Add CIDR to whitelist
  console.log('\nAdding CIDR to whitelist:');
  
  ipAccessControl.addCidrToWhitelist('***********/16');
  ipAccessControl.addCidrToWhitelist('10.0.0.0/8');
  
  // Add CIDR to blacklist
  console.log('\nAdding CIDR to blacklist:');
  
  ipAccessControl.addCidrToBlacklist('*******/16');
  ipAccessControl.addCidrToBlacklist('*******/16');
  
  // Check access for different IPs
  console.log('\nChecking access for different IPs:');
  
  const ip1Result = ipAccessControl.checkAccess('***********');
  console.log('IP *********** access result:', ip1Result);
  
  const ip2Result = ipAccessControl.checkAccess('*******');
  console.log('IP ******* access result:', ip2Result);
  
  const ip3Result = ipAccessControl.checkAccess('***********');
  console.log('IP *********** access result:', ip3Result);
  
  const ip4Result = ipAccessControl.checkAccess('*******');
  console.log('IP ******* access result:', ip4Result);
  
  // Simulate rate limiting
  console.log('\nSimulating rate limiting:');
  
  const ip = '***********';
  
  for (let i = 0; i < 150; i++) {
    ipAccessControl.checkAccess(ip);
  }
  
  const rateLimitResult = ipAccessControl.checkAccess(ip);
  console.log(`After 150 requests, IP ${ip} access result:`, rateLimitResult);

  // Dispose resources
  ipAccessControl.dispose();
}

/**
 * Example 3: Advanced Threat Detection
 * 
 * This example demonstrates how to use the advanced threat detection service.
 */
async function example3() {
  console.log('\n=== Example 3: Advanced Threat Detection ===\n');

  // Create threat detector
  const threatDetector = createThreatDetector({
    enableLogging: true,
    behaviorAnalysisEnabled: true,
    anomalyDetectionEnabled: true,
    threatIntelligenceEnabled: true,
    behaviorAnalysisThreshold: 0.7,
    anomalyDetectionThreshold: 3.0,
    threatIntelligenceThreshold: 0.5
  });

  // Register event listeners
  threatDetector.on('threat-detected', (data) => {
    console.log(`Threat detected: ${data.threatType} (Level: ${data.threatLevel})`);
  });

  threatDetector.on('behavior-threat-detected', (data) => {
    console.log(`Behavior threat detected: ${data.threatType} (Level: ${data.threatLevel})`);
  });

  threatDetector.on('anomaly-threat-detected', (data) => {
    console.log(`Anomaly threat detected: ${data.threatType} (Level: ${data.threatLevel})`);
  });

  // Start threat detector
  threatDetector.start();

  // Register a simple threat intelligence provider
  console.log('\nRegistering threat intelligence provider:');
  
  const threatIntelProvider = {
    name: 'Simple Threat Intel',
    checkThreat: async (event) => {
      // Check if source IP is known bad
      if (event.source.ip === '*******') {
        return {
          threatDetected: true,
          threatLevel: 0.9,
          threatType: 'known-bad-ip',
          details: 'IP is on known bad list'
        };
      }
      
      // Check if user is known bad
      if (event.source.userId === 'bad-user') {
        return {
          threatDetected: true,
          threatLevel: 0.8,
          threatType: 'known-bad-user',
          details: 'User is on known bad list'
        };
      }
      
      return {
        threatDetected: false,
        threatLevel: 0,
        threatType: null,
        details: 'No threat detected'
      };
    }
  };
  
  threatDetector.registerThreatIntelligenceProvider(threatIntelProvider);
  
  // Process normal events
  console.log('\nProcessing normal events:');
  
  for (let i = 0; i < 10; i++) {
    const event = {
      type: 'login',
      source: {
        ip: '***********',
        userId: 'john.doe'
      },
      timestamp: Date.now() - (i * 60000) // Events 1 minute apart
    };
    
    await threatDetector.processEvent(event);
  }
  
  // Process suspicious behavior event
  console.log('\nProcessing suspicious behavior event:');
  
  const suspiciousEvent = {
    type: 'admin-access',
    source: {
      ip: '***********',
      userId: 'john.doe'
    },
    timestamp: Date.now()
  };
  
  const suspiciousResult = await threatDetector.processEvent(suspiciousEvent);
  console.log('Suspicious event result:', suspiciousResult);
  
  // Process anomalous event
  console.log('\nProcessing anomalous event:');
  
  const anomalousEvent = {
    type: 'login',
    source: {
      ip: '***********',
      userId: 'john.doe'
    },
    timestamp: Date.now() - 1000 // Very soon after previous event
  };
  
  const anomalousResult = await threatDetector.processEvent(anomalousEvent);
  console.log('Anomalous event result:', anomalousResult);
  
  // Process known bad event
  console.log('\nProcessing known bad event:');
  
  const badEvent = {
    type: 'login',
    source: {
      ip: '*******',
      userId: 'bad-user'
    },
    timestamp: Date.now()
  };
  
  const badResult = await threatDetector.processEvent(badEvent);
  console.log('Known bad event result:', badResult);

  // Stop threat detector
  threatDetector.stop();

  // Dispose resources
  threatDetector.dispose();
}

/**
 * Example 4: Integrated Security Components
 * 
 * This example demonstrates how to use all security components together.
 */
async function example4() {
  console.log('\n=== Example 4: Integrated Security Components ===\n');

  // Create security components
  const securityComponents = createSecurityComponents({
    enableLogging: true
  });

  // Start components
  securityComponents.threatDetector.start();

  // Process a request
  console.log('\nProcessing a request:');
  
  const ip = '***********';
  const username = 'john.doe';
  const token = 'valid-token';
  
  // Check IP access
  const ipResult = securityComponents.ipAccessControl.checkAccess(ip);
  
  if (!ipResult.allowed) {
    console.log(`IP ${ip} access denied: ${ipResult.reason}`);
    return;
  }
  
  console.log(`IP ${ip} access allowed: ${ipResult.reason}`);
  
  // Verify token
  const tokenResult = securityComponents.mfaService.verifyToken(token);
  
  if (!tokenResult.verified) {
    console.log(`Token verification failed: ${tokenResult.error}`);
    return;
  }
  
  console.log(`Token verified for user: ${tokenResult.username}`);
  
  // Check permissions
  const permissionResult = securityComponents.rbac.checkPermission(username, 'read', 'resource');
  
  if (!permissionResult.allowed) {
    console.log(`Permission denied: ${permissionResult.reason}`);
    return;
  }
  
  console.log(`Permission granted: ${permissionResult.reason}`);
  
  // Process event for threat detection
  const event = {
    type: 'resource-access',
    source: {
      ip,
      userId: username
    },
    resource: 'resource',
    action: 'read',
    timestamp: Date.now()
  };
  
  const threatResult = await securityComponents.threatDetector.processEvent(event);
  
  if (threatResult.threatDetected) {
    console.log(`Threat detected: ${threatResult.threatType} (Level: ${threatResult.threatLevel})`);
    
    // Log security event
    securityComponents.auditLogger.logSecurityEvent({
      type: 'security-threat',
      source: {
        ip,
        userId: username
      },
      details: threatResult
    });
    
    return;
  }
  
  console.log('No threats detected');
  
  // Log access event
  securityComponents.auditLogger.logAccessEvent({
    type: 'resource-access',
    source: {
      ip,
      userId: username
    },
    resource: 'resource',
    action: 'read',
    result: 'success'
  });
  
  console.log('Access event logged');
  
  // Store sensitive data
  const sensitiveData = {
    key: 'value'
  };
  
  const encryptedData = securityComponents.secureStorage.encrypt(JSON.stringify(sensitiveData));
  console.log('Data encrypted and stored');
  
  // Retrieve and decrypt data
  const decryptedData = JSON.parse(securityComponents.secureStorage.decrypt(encryptedData));
  console.log('Data retrieved and decrypted:', decryptedData);

  // Stop components
  securityComponents.threatDetector.stop();

  // Dispose resources
  Object.values(securityComponents).forEach(component => {
    if (component.dispose) {
      component.dispose();
    }
  });
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
