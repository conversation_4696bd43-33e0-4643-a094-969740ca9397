/**
 * Human-System Coherence Interface
 * 
 * This module implements the Human-System Coherence Interface, which optimizes UI safety
 * via the Human Coherence Index (Ψₕ).
 * 
 * Key features include:
 * - Cognitive load-adjusted alert delivery system
 * - Stress-testing interfaces with biofeedback loops (linking to CSME)
 * - Adaptive interface complexity based on human coherence
 */

const { performance } = require('perf_hooks');
const { EventEmitter } = require('events');

/**
 * HumanSystemCoherenceInterface class
 */
class HumanSystemCoherenceInterface extends EventEmitter {
  /**
   * Create a new HumanSystemCoherenceInterface instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      alertDelivery: {
        enabled: true, // Enable cognitive load-adjusted alert delivery
        maxAlertRate: 5, // Maximum alerts per minute
        priorityThresholds: {
          critical: 0.9, // Critical alert threshold
          high: 0.7, // High alert threshold
          medium: 0.5, // Medium alert threshold
          low: 0.3 // Low alert threshold
        }
      },
      interfaceAdaptation: {
        enabled: true, // Enable adaptive interface complexity
        complexityLevels: {
          expert: 0.8, // Expert complexity threshold
          advanced: 0.6, // Advanced complexity threshold
          intermediate: 0.4, // Intermediate complexity threshold
          beginner: 0.2 // Beginner complexity threshold
        }
      },
      biofeedback: {
        enabled: false, // Enable biofeedback integration (disabled by default)
        updateFrequency: 5000 // 5 seconds in milliseconds
      },
      csmeIntegration: {
        enabled: false, // Enable CSME integration (disabled by default)
        updateFrequency: 10000 // 10 seconds in milliseconds
      },
      enableLogging: true, // Enable logging
      enableMetrics: true, // Enable performance metrics
      ...options
    };
    
    // Initialize state
    this.state = {
      humanCoherenceIndex: 0.7, // Ψₕ (Human Coherence Index)
      cognitiveLoad: 0.3, // Current cognitive load
      stressLevel: 0.2, // Current stress level
      alertQueue: [], // Queued alerts
      activeAlerts: [], // Active alerts
      interfaceComplexity: 'intermediate', // Current interface complexity
      lastUpdateTime: Date.now(),
      isRunning: false
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      alertsDelivered: 0,
      alertsThrottled: 0,
      complexityChanges: 0
    };
    
    console.log('HumanSystemCoherenceInterface initialized');
  }
  
  /**
   * Start the interface
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      console.log('HumanSystemCoherenceInterface is already running');
      return false;
    }
    
    this.state.isRunning = true;
    
    // Start alert processing
    this._startAlertProcessing();
    
    // Start biofeedback integration if enabled
    if (this.options.biofeedback.enabled) {
      this._startBiofeedbackIntegration();
    }
    
    // Start CSME integration if enabled
    if (this.options.csmeIntegration.enabled) {
      this._startCSMEIntegration();
    }
    
    console.log('HumanSystemCoherenceInterface started');
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the interface
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      console.log('HumanSystemCoherenceInterface is not running');
      return false;
    }
    
    this.state.isRunning = false;
    
    // Stop alert processing
    this._stopAlertProcessing();
    
    // Stop biofeedback integration
    this._stopBiofeedbackIntegration();
    
    // Stop CSME integration
    this._stopCSMEIntegration();
    
    console.log('HumanSystemCoherenceInterface stopped');
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Update human coherence index (Ψₕ)
   * @param {Object} humanData - Human data
   * @returns {number} - Human coherence index
   */
  updateHumanCoherenceIndex(humanData) {
    const startTime = performance.now();
    this.metrics.totalUpdates++;
    
    // Extract human metrics
    const {
      cognitiveLoad = 0.3,
      stressLevel = 0.2,
      fatigue = 0.3,
      attention = 0.8,
      expertise = 0.7
    } = humanData;
    
    // Update state
    this.state.cognitiveLoad = cognitiveLoad;
    this.state.stressLevel = stressLevel;
    
    // Apply 18/82 principle: 18% weight to positive factors, 82% to negative factors
    const positiveFactors = (attention + expertise) / 2;
    const negativeFactors = (cognitiveLoad + stressLevel + fatigue) / 3;
    
    const humanCoherenceIndex = (0.18 * positiveFactors) + (0.82 * (1 - negativeFactors));
    
    // Update state
    this.state.humanCoherenceIndex = humanCoherenceIndex;
    this.state.lastUpdateTime = Date.now();
    
    // Adapt interface complexity based on human coherence
    this._adaptInterfaceComplexity();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit update event
    this.emit('coherence-update', {
      humanCoherenceIndex,
      cognitiveLoad,
      stressLevel,
      fatigue,
      attention,
      expertise,
      timestamp: this.state.lastUpdateTime
    });
    
    return humanCoherenceIndex;
  }
  
  /**
   * Queue alert for delivery
   * @param {Object} alert - Alert data
   * @returns {Object} - Queue result
   */
  queueAlert(alert) {
    // Validate alert
    if (!alert || !alert.message || !alert.priority) {
      return { success: false, reason: 'Invalid alert data' };
    }
    
    // Create alert object
    const alertObject = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      message: alert.message,
      priority: alert.priority,
      source: alert.source || 'unknown',
      data: alert.data || {},
      createdAt: Date.now(),
      status: 'queued'
    };
    
    // Add to queue
    this.state.alertQueue.push(alertObject);
    
    // Emit queue event
    this.emit('alert-queued', alertObject);
    
    if (this.options.enableLogging) {
      console.log(`HumanSystemCoherenceInterface: Queued ${alert.priority} alert from ${alert.source || 'unknown'}`);
    }
    
    return { success: true, alertId: alertObject.id };
  }
  
  /**
   * Get human coherence index
   * @returns {number} - Human coherence index
   */
  getHumanCoherenceIndex() {
    return this.state.humanCoherenceIndex;
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return { ...this.state };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Adapt interface complexity based on human coherence
   * @private
   */
  _adaptInterfaceComplexity() {
    if (!this.options.interfaceAdaptation.enabled) {
      return;
    }
    
    const humanCoherenceIndex = this.state.humanCoherenceIndex;
    let newComplexity = 'intermediate';
    
    // Determine complexity level based on human coherence
    if (humanCoherenceIndex >= this.options.interfaceAdaptation.complexityLevels.expert) {
      newComplexity = 'expert';
    } else if (humanCoherenceIndex >= this.options.interfaceAdaptation.complexityLevels.advanced) {
      newComplexity = 'advanced';
    } else if (humanCoherenceIndex >= this.options.interfaceAdaptation.complexityLevels.intermediate) {
      newComplexity = 'intermediate';
    } else if (humanCoherenceIndex >= this.options.interfaceAdaptation.complexityLevels.beginner) {
      newComplexity = 'beginner';
    } else {
      newComplexity = 'beginner';
    }
    
    // Check if complexity changed
    if (newComplexity !== this.state.interfaceComplexity) {
      const previousComplexity = this.state.interfaceComplexity;
      this.state.interfaceComplexity = newComplexity;
      
      // Update metrics
      this.metrics.complexityChanges++;
      
      // Emit complexity change event
      this.emit('complexity-change', {
        previousComplexity,
        newComplexity,
        humanCoherenceIndex,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`HumanSystemCoherenceInterface: Changed interface complexity from ${previousComplexity} to ${newComplexity}`);
      }
    }
  }
  
  /**
   * Start alert processing
   * @private
   */
  _startAlertProcessing() {
    if (this._alertInterval) {
      clearInterval(this._alertInterval);
    }
    
    this._alertInterval = setInterval(() => {
      if (this.state.isRunning && this.options.alertDelivery.enabled) {
        this._processAlertQueue();
      }
    }, 1000); // Process queue every second
  }
  
  /**
   * Stop alert processing
   * @private
   */
  _stopAlertProcessing() {
    if (this._alertInterval) {
      clearInterval(this._alertInterval);
      this._alertInterval = null;
    }
  }
  
  /**
   * Process alert queue
   * @private
   */
  _processAlertQueue() {
    if (this.state.alertQueue.length === 0) {
      return;
    }
    
    // Calculate maximum alerts based on human coherence
    const maxAlerts = this._calculateMaxAlerts();
    
    // Get active alerts in the last minute
    const now = Date.now();
    const activeAlertsLastMinute = this.state.activeAlerts.filter(a => a.deliveredAt > now - 60000).length;
    
    // Check if we can deliver more alerts
    if (activeAlertsLastMinute >= maxAlerts) {
      // Too many active alerts, throttle
      this.metrics.alertsThrottled++;
      return;
    }
    
    // Sort queue by priority
    this.state.alertQueue.sort((a, b) => {
      const priorityA = this._getPriorityValue(a.priority);
      const priorityB = this._getPriorityValue(b.priority);
      return priorityB - priorityA; // Higher priority first
    });
    
    // Get next alert
    const nextAlert = this.state.alertQueue.shift();
    
    // Deliver alert
    this._deliverAlert(nextAlert);
  }
  
  /**
   * Calculate maximum alerts based on human coherence
   * @returns {number} - Maximum alerts
   * @private
   */
  _calculateMaxAlerts() {
    // Base max alerts on human coherence
    // Lower coherence = fewer alerts
    const baseMax = this.options.alertDelivery.maxAlertRate;
    const coherenceFactor = this.state.humanCoherenceIndex;
    
    // Apply cognitive load adjustment
    // Higher cognitive load = fewer alerts
    const cognitiveLoadFactor = 1 - this.state.cognitiveLoad;
    
    // Calculate adjusted max alerts
    const adjustedMax = Math.max(1, Math.round(baseMax * coherenceFactor * cognitiveLoadFactor));
    
    return adjustedMax;
  }
  
  /**
   * Deliver alert
   * @param {Object} alert - Alert to deliver
   * @private
   */
  _deliverAlert(alert) {
    // Update alert
    alert.status = 'delivered';
    alert.deliveredAt = Date.now();
    
    // Add to active alerts
    this.state.activeAlerts.push(alert);
    
    // Limit active alerts
    if (this.state.activeAlerts.length > 100) {
      this.state.activeAlerts.shift();
    }
    
    // Update metrics
    this.metrics.alertsDelivered++;
    
    // Emit alert delivered event
    this.emit('alert-delivered', alert);
    
    if (this.options.enableLogging) {
      console.log(`HumanSystemCoherenceInterface: Delivered ${alert.priority} alert from ${alert.source}`);
    }
  }
  
  /**
   * Get priority value
   * @param {string} priority - Priority string
   * @returns {number} - Priority value
   * @private
   */
  _getPriorityValue(priority) {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 4;
      case 'high':
        return 3;
      case 'medium':
        return 2;
      case 'low':
        return 1;
      default:
        return 0;
    }
  }
  
  /**
   * Start biofeedback integration
   * @private
   */
  _startBiofeedbackIntegration() {
    if (this._biofeedbackInterval) {
      clearInterval(this._biofeedbackInterval);
    }
    
    this._biofeedbackInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would fetch biofeedback data
        // For now, just simulate data
        this._processBiofeedbackData();
      }
    }, this.options.biofeedback.updateFrequency);
  }
  
  /**
   * Stop biofeedback integration
   * @private
   */
  _stopBiofeedbackIntegration() {
    if (this._biofeedbackInterval) {
      clearInterval(this._biofeedbackInterval);
      this._biofeedbackInterval = null;
    }
  }
  
  /**
   * Process biofeedback data
   * @private
   */
  _processBiofeedbackData() {
    // In a real implementation, this would process actual biofeedback data
    // For now, just simulate data
    
    // Generate simulated biofeedback data
    const biofeedbackData = {
      heartRate: 70 + Math.random() * 20,
      respirationRate: 12 + Math.random() * 6,
      skinConductance: 2 + Math.random() * 3,
      pupilDilation: 3 + Math.random() * 2
    };
    
    // Calculate stress level from biofeedback
    const stressLevel = this._calculateStressFromBiofeedback(biofeedbackData);
    
    // Update human data
    this.updateHumanCoherenceIndex({
      stressLevel,
      cognitiveLoad: this.state.cognitiveLoad,
      fatigue: 0.3,
      attention: 0.8,
      expertise: 0.7
    });
  }
  
  /**
   * Calculate stress level from biofeedback
   * @param {Object} biofeedbackData - Biofeedback data
   * @returns {number} - Stress level
   * @private
   */
  _calculateStressFromBiofeedback(biofeedbackData) {
    // Normalize heart rate (60-100 bpm range)
    const heartRateNorm = Math.max(0, Math.min(1, (biofeedbackData.heartRate - 60) / 40));
    
    // Normalize respiration rate (8-20 breaths per minute range)
    const respirationRateNorm = Math.max(0, Math.min(1, (biofeedbackData.respirationRate - 8) / 12));
    
    // Normalize skin conductance (1-8 µS range)
    const skinConductanceNorm = Math.max(0, Math.min(1, (biofeedbackData.skinConductance - 1) / 7));
    
    // Normalize pupil dilation (2-8 mm range)
    const pupilDilationNorm = Math.max(0, Math.min(1, (biofeedbackData.pupilDilation - 2) / 6));
    
    // Calculate stress level (weighted average)
    return (heartRateNorm * 0.3) + (respirationRateNorm * 0.2) + (skinConductanceNorm * 0.3) + (pupilDilationNorm * 0.2);
  }
  
  /**
   * Start CSME integration
   * @private
   */
  _startCSMEIntegration() {
    if (this._csmeInterval) {
      clearInterval(this._csmeInterval);
    }
    
    this._csmeInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would integrate with CSME
        // For now, just simulate integration
        this._processCSMEData();
      }
    }, this.options.csmeIntegration.updateFrequency);
  }
  
  /**
   * Stop CSME integration
   * @private
   */
  _stopCSMEIntegration() {
    if (this._csmeInterval) {
      clearInterval(this._csmeInterval);
      this._csmeInterval = null;
    }
  }
  
  /**
   * Process CSME data
   * @private
   */
  _processCSMEData() {
    // In a real implementation, this would process actual CSME data
    // For now, just simulate data
    
    // Generate simulated CSME data
    const csmeData = {
      coherence: 0.7 + (Math.random() * 0.2 - 0.1),
      entropyGradient: Math.random() * 0.1 - 0.05
    };
    
    // Emit CSME integration event
    this.emit('csme-integration', {
      csmeCoherence: csmeData.coherence,
      csmeEntropyGradient: csmeData.entropyGradient,
      humanCoherenceIndex: this.state.humanCoherenceIndex,
      timestamp: Date.now()
    });
  }
}

module.exports = HumanSystemCoherenceInterface;
