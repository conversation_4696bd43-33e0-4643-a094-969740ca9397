/**
 * Integration Model
 * 
 * Represents an integration with an external system where personal data
 * might be stored, enabling automated data subject request processing,
 * consent management, and more.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const integrationSchema = new Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['crm', 'marketing', 'analytics', 'cloud_storage', 'email', 'communication', 'custom'],
    trim: true
  },
  provider: {
    type: String,
    required: true,
    trim: true
  },
  capabilities: [{
    type: String,
    required: true,
    enum: ['data-export', 'data-deletion', 'data-update', 'notifications'],
    trim: true
  }],
  dataTypes: [{
    type: String,
    required: true,
    trim: true
  }],
  authType: {
    type: String,
    required: true,
    enum: ['oauth2', 'api_key', 'basic_auth', 'custom'],
    trim: true
  },
  configSchema: {
    type: Object,
    required: true
  },
  config: {
    type: Object,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'error'],
    default: 'inactive'
  },
  lastChecked: {
    type: Date
  },
  lastError: {
    message: {
      type: String,
      trim: true
    },
    timestamp: {
      type: Date
    },
    details: {
      type: Object
    }
  },
  handlers: {
    'data-export': {
      endpoint: {
        type: String,
        trim: true
      },
      method: {
        type: String,
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        default: 'POST'
      }
    },
    'data-deletion': {
      endpoint: {
        type: String,
        trim: true
      },
      method: {
        type: String,
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        default: 'POST'
      }
    },
    'data-update': {
      endpoint: {
        type: String,
        trim: true
      },
      method: {
        type: String,
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        default: 'POST'
      }
    },
    'notifications': {
      endpoint: {
        type: String,
        trim: true
      },
      method: {
        type: String,
        enum: ['GET', 'POST', 'PUT', 'DELETE'],
        default: 'POST'
      }
    }
  },
  metrics: {
    totalRequests: {
      type: Number,
      default: 0
    },
    successfulRequests: {
      type: Number,
      default: 0
    },
    failedRequests: {
      type: Number,
      default: 0
    },
    averageResponseTime: {
      type: Number,
      default: 0
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
integrationSchema.index({
  name: 'text',
  description: 'text',
  provider: 'text'
});

// Pre-save hook to update the updatedAt field
integrationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for health status
integrationSchema.virtual('healthStatus').get(function() {
  if (this.status === 'error') return 'unhealthy';
  if (!this.lastChecked) return 'unknown';
  
  const now = new Date();
  const lastChecked = new Date(this.lastChecked);
  const diffTime = now - lastChecked;
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  
  // If the integration hasn't been checked in the last 24 hours, consider it unknown
  if (diffHours > 24) return 'unknown';
  
  return 'healthy';
});

// Virtual for success rate
integrationSchema.virtual('successRate').get(function() {
  if (this.metrics.totalRequests === 0) return 0;
  return (this.metrics.successfulRequests / this.metrics.totalRequests) * 100;
});

const Integration = mongoose.model('Integration', integrationSchema);

module.exports = Integration;
