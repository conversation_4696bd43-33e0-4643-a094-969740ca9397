/**
 * EmergencyAuthenticator.js
 * 
 * This module provides authentication for emergency access to medical profiles.
 * It ensures that only legitimate emergency services can access sensitive medical data.
 */

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');

/**
 * EmergencyAuthenticator class for verifying legitimate emergency access
 */
class EmergencyAuthenticator extends EventEmitter {
  constructor(options = {}) {
    super();
    this.tokenExpirationTime = options.tokenExpirationTime || 3600; // 1 hour default
    this.maxFailedAttempts = options.maxFailedAttempts || 5;
    this.lockoutDuration = options.lockoutDuration || 1800; // 30 minutes default
    this.trustedServices = options.trustedServices || [];
    this.activeTokens = new Map();
    this.accessLog = [];
    this.failedAttempts = new Map();
    this.lockedOut = new Map();
    
    // Initialize AI anomaly detection if enabled
    this.anomalyDetectionEnabled = options.anomalyDetectionEnabled !== false;
    this.anomalyThreshold = options.anomalyThreshold || 0.85;
    
    // Set up automatic cleanup of expired tokens
    setInterval(() => this._cleanupExpiredTokens(), 60000); // Every minute
  }

  /**
   * Register a trusted emergency service
   * @param {Object} service - The emergency service to register
   * @returns {Object} - The registered service with credentials
   */
  registerTrustedService(service) {
    if (!service.name || !service.type) {
      throw new Error('Service must have a name and type');
    }
    
    const serviceId = uuidv4();
    const apiKey = this._generateApiKey();
    const apiSecret = this._generateApiSecret();
    
    const trustedService = {
      serviceId,
      name: service.name,
      type: service.type,
      credentials: {
        apiKey,
        apiSecret: this._hashSecret(apiSecret)
      },
      registeredAt: new Date().toISOString(),
      status: 'ACTIVE',
      metadata: service.metadata || {}
    };
    
    this.trustedServices.push(trustedService);
    
    // Return the service with the unhashed secret for initial setup
    return {
      ...trustedService,
      credentials: {
        apiKey,
        apiSecret
      }
    };
  }

  /**
   * Authenticate an emergency service
   * @param {String} apiKey - The API key
   * @param {String} apiSecret - The API secret
   * @param {Object} context - Additional context about the authentication attempt
   * @returns {Object} - Authentication result
   */
  authenticateService(apiKey, apiSecret, context = {}) {
    // Check for lockout
    if (this.lockedOut.has(apiKey)) {
      const lockoutExpiry = this.lockedOut.get(apiKey);
      if (Date.now() < lockoutExpiry) {
        const remainingSeconds = Math.ceil((lockoutExpiry - Date.now()) / 1000);
        
        this._logAccessAttempt({
          apiKey,
          success: false,
          reason: 'ACCOUNT_LOCKED',
          timestamp: new Date().toISOString(),
          context
        });
        
        return {
          authenticated: false,
          error: 'Account locked due to too many failed attempts',
          lockoutRemaining: remainingSeconds
        };
      } else {
        // Lockout expired
        this.lockedOut.delete(apiKey);
        this.failedAttempts.delete(apiKey);
      }
    }
    
    // Find the service
    const service = this.trustedServices.find(s => s.credentials.apiKey === apiKey);
    
    if (!service) {
      this._logAccessAttempt({
        apiKey,
        success: false,
        reason: 'INVALID_API_KEY',
        timestamp: new Date().toISOString(),
        context
      });
      
      return {
        authenticated: false,
        error: 'Invalid API key'
      };
    }
    
    // Check if service is active
    if (service.status !== 'ACTIVE') {
      this._logAccessAttempt({
        apiKey,
        serviceId: service.serviceId,
        success: false,
        reason: 'SERVICE_INACTIVE',
        timestamp: new Date().toISOString(),
        context
      });
      
      return {
        authenticated: false,
        error: 'Service is not active'
      };
    }
    
    // Verify the secret
    const isValidSecret = this._verifySecret(apiSecret, service.credentials.apiSecret);
    
    if (!isValidSecret) {
      // Increment failed attempts
      const currentFailedAttempts = (this.failedAttempts.get(apiKey) || 0) + 1;
      this.failedAttempts.set(apiKey, currentFailedAttempts);
      
      this._logAccessAttempt({
        apiKey,
        serviceId: service.serviceId,
        success: false,
        reason: 'INVALID_SECRET',
        timestamp: new Date().toISOString(),
        context
      });
      
      // Check if we should lock the account
      if (currentFailedAttempts >= this.maxFailedAttempts) {
        const lockoutExpiry = Date.now() + (this.lockoutDuration * 1000);
        this.lockedOut.set(apiKey, lockoutExpiry);
        
        return {
          authenticated: false,
          error: 'Account locked due to too many failed attempts',
          lockoutRemaining: this.lockoutDuration
        };
      }
      
      return {
        authenticated: false,
        error: 'Invalid API secret',
        remainingAttempts: this.maxFailedAttempts - currentFailedAttempts
      };
    }
    
    // Reset failed attempts
    this.failedAttempts.delete(apiKey);
    
    // Check for anomalies if enabled
    if (this.anomalyDetectionEnabled) {
      const anomalyScore = this._detectAnomaly(service, context);
      
      if (anomalyScore > this.anomalyThreshold) {
        this._logAccessAttempt({
          apiKey,
          serviceId: service.serviceId,
          success: false,
          reason: 'ANOMALY_DETECTED',
          anomalyScore,
          timestamp: new Date().toISOString(),
          context
        });
        
        this.emit('anomaly:detected', {
          serviceId: service.serviceId,
          anomalyScore,
          context
        });
        
        return {
          authenticated: false,
          error: 'Suspicious activity detected',
          requiresAdditionalVerification: true
        };
      }
    }
    
    // Generate access token
    const token = this._generateAccessToken(service);
    
    this._logAccessAttempt({
      apiKey,
      serviceId: service.serviceId,
      success: true,
      tokenId: token.tokenId,
      timestamp: new Date().toISOString(),
      context
    });
    
    return {
      authenticated: true,
      service: {
        serviceId: service.serviceId,
        name: service.name,
        type: service.type
      },
      token
    };
  }

  /**
   * Verify an emergency access token
   * @param {String} tokenId - The token ID
   * @param {String} tokenValue - The token value
   * @returns {Object} - Verification result
   */
  verifyToken(tokenId, tokenValue) {
    const tokenData = this.activeTokens.get(tokenId);
    
    if (!tokenData) {
      return {
        valid: false,
        error: 'Token not found or expired'
      };
    }
    
    if (tokenData.value !== tokenValue) {
      return {
        valid: false,
        error: 'Invalid token value'
      };
    }
    
    if (Date.now() > tokenData.expiresAt) {
      this.activeTokens.delete(tokenId);
      return {
        valid: false,
        error: 'Token expired'
      };
    }
    
    return {
      valid: true,
      serviceId: tokenData.serviceId,
      expiresIn: Math.floor((tokenData.expiresAt - Date.now()) / 1000)
    };
  }

  /**
   * Revoke an emergency access token
   * @param {String} tokenId - The token ID
   * @returns {Boolean} - Whether the token was successfully revoked
   */
  revokeToken(tokenId) {
    const tokenExists = this.activeTokens.has(tokenId);
    
    if (tokenExists) {
      this.activeTokens.delete(tokenId);
      this.emit('token:revoked', { tokenId });
      return true;
    }
    
    return false;
  }

  /**
   * Get access logs for a service
   * @param {String} serviceId - The service ID
   * @param {Object} options - Options for filtering logs
   * @returns {Array} - The access logs
   */
  getServiceAccessLogs(serviceId, options = {}) {
    const { startDate, endDate, success, limit } = options;
    
    let filteredLogs = this.accessLog.filter(log => log.serviceId === serviceId);
    
    if (startDate) {
      const startTimestamp = new Date(startDate).getTime();
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp).getTime() >= startTimestamp
      );
    }
    
    if (endDate) {
      const endTimestamp = new Date(endDate).getTime();
      filteredLogs = filteredLogs.filter(log => 
        new Date(log.timestamp).getTime() <= endTimestamp
      );
    }
    
    if (success !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.success === success);
    }
    
    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    if (limit) {
      filteredLogs = filteredLogs.slice(0, limit);
    }
    
    return filteredLogs;
  }

  /**
   * Generate an API key
   * @returns {String} - The generated API key
   * @private
   */
  _generateApiKey() {
    return `ems_${uuidv4().replace(/-/g, '')}`;
  }

  /**
   * Generate an API secret
   * @returns {String} - The generated API secret
   * @private
   */
  _generateApiSecret() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Hash an API secret
   * @param {String} secret - The secret to hash
   * @returns {String} - The hashed secret
   * @private
   */
  _hashSecret(secret) {
    return crypto
      .createHash('sha256')
      .update(secret)
      .digest('hex');
  }

  /**
   * Verify an API secret against a hash
   * @param {String} secret - The secret to verify
   * @param {String} hash - The hash to verify against
   * @returns {Boolean} - Whether the secret is valid
   * @private
   */
  _verifySecret(secret, hash) {
    const calculatedHash = this._hashSecret(secret);
    return calculatedHash === hash;
  }

  /**
   * Generate an access token
   * @param {Object} service - The service to generate a token for
   * @returns {Object} - The generated token
   * @private
   */
  _generateAccessToken(service) {
    const tokenId = uuidv4();
    const tokenValue = crypto.randomBytes(32).toString('hex');
    const expiresAt = Date.now() + (this.tokenExpirationTime * 1000);
    
    const token = {
      tokenId,
      value: tokenValue,
      serviceId: service.serviceId,
      issuedAt: Date.now(),
      expiresAt
    };
    
    this.activeTokens.set(tokenId, token);
    
    return {
      tokenId,
      value: tokenValue,
      expiresIn: this.tokenExpirationTime
    };
  }

  /**
   * Clean up expired tokens
   * @private
   */
  _cleanupExpiredTokens() {
    const now = Date.now();
    
    for (const [tokenId, token] of this.activeTokens.entries()) {
      if (now > token.expiresAt) {
        this.activeTokens.delete(tokenId);
      }
    }
  }

  /**
   * Log an access attempt
   * @param {Object} logEntry - The log entry
   * @private
   */
  _logAccessAttempt(logEntry) {
    this.accessLog.push(logEntry);
    
    // Emit events based on the log entry
    if (logEntry.success) {
      this.emit('auth:success', logEntry);
    } else {
      this.emit('auth:failure', logEntry);
    }
    
    // Limit log size
    if (this.accessLog.length > 1000) {
      this.accessLog = this.accessLog.slice(-1000);
    }
  }

  /**
   * Detect anomalies in authentication attempts
   * @param {Object} service - The service being authenticated
   * @param {Object} context - The authentication context
   * @returns {Number} - Anomaly score (0-1, higher is more anomalous)
   * @private
   */
  _detectAnomaly(service, context) {
    // In a real implementation, this would use AI/ML for anomaly detection
    // For now, we'll implement a simple heuristic-based approach
    
    let anomalyScore = 0;
    
    // Get recent successful logins for this service
    const recentLogins = this.accessLog
      .filter(log => 
        log.serviceId === service.serviceId && 
        log.success === true &&
        new Date(log.timestamp).getTime() > Date.now() - (7 * 24 * 60 * 60 * 1000) // Last 7 days
      )
      .map(log => log.context);
    
    if (recentLogins.length > 0) {
      // Check for unusual location
      if (context.location) {
        const usualLocations = new Set(
          recentLogins
            .filter(c => c.location)
            .map(c => c.location.city || c.location.region || c.location.country)
        );
        
        const currentLocation = context.location.city || context.location.region || context.location.country;
        
        if (currentLocation && !usualLocations.has(currentLocation)) {
          anomalyScore += 0.3;
        }
      }
      
      // Check for unusual device
      if (context.device) {
        const usualDevices = new Set(
          recentLogins
            .filter(c => c.device)
            .map(c => c.device.type || c.device.model || c.device.id)
        );
        
        const currentDevice = context.device.type || context.device.model || context.device.id;
        
        if (currentDevice && !usualDevices.has(currentDevice)) {
          anomalyScore += 0.2;
        }
      }
      
      // Check for unusual time
      if (context.timestamp) {
        const loginHours = recentLogins
          .filter(c => c.timestamp)
          .map(c => new Date(c.timestamp).getHours());
        
        const currentHour = new Date(context.timestamp).getHours();
        const unusualTime = !loginHours.some(hour => Math.abs(hour - currentHour) <= 2);
        
        if (unusualTime) {
          anomalyScore += 0.2;
        }
      }
    }
    
    // Check for high frequency of access
    const recentAttempts = this.accessLog.filter(log => 
      log.apiKey === service.credentials.apiKey &&
      new Date(log.timestamp).getTime() > Date.now() - (60 * 60 * 1000) // Last hour
    );
    
    if (recentAttempts.length > 20) { // More than 20 attempts in an hour
      anomalyScore += 0.3;
    }
    
    return Math.min(anomalyScore, 1.0);
  }
}

module.exports = EmergencyAuthenticator;
