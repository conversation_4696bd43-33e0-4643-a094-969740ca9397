# NovaDNA Healthcare Integration

This module provides integration between NovaDNA and healthcare systems using NovaConnect. It enables secure access to emergency medical data from various healthcare providers.

## Overview

The healthcare integration consists of several components:

1. **EmergencyDataPipeline** - A secure data pipeline for emergency medical data
2. **ProviderConnector** - Specific connectors for major healthcare providers
3. **DataSourcePrioritization** - Logic for prioritizing data sources
4. **SecureTemporaryCache** - Secure temporary caching for emergency scenarios
5. **HealthcareIntegration** - Main integration module that brings everything together

## Key Features

- **Secure Data Pipeline**: Ensures data is securely transferred and only accessible during emergencies
- **Provider-Specific Connectors**: Optimized connectors for major EHR systems like Epic and Cerner
- **Data Source Prioritization**: Intelligent prioritization of data sources based on emergency context
- **Secure Temporary Caching**: Encrypted caching with automatic expiration for emergency scenarios
- **Comprehensive Logging**: Detailed audit trail of all data access

## Usage

### Basic Integration

```javascript
const HealthcareIntegration = require('./HealthcareIntegration');

// Initialize healthcare integration
const healthcareIntegration = new HealthcareIntegration({
  apiUrl: 'http://localhost:3000/api/novaconnect',
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret',
  cacheEnabled: true,
  encryptionEnabled: true
});

// Initialize with provider credentials
await healthcareIntegration.initialize({
  providers: {
    Epic: {
      clientId: 'your-epic-client-id',
      clientSecret: 'your-epic-client-secret',
      redirectUri: 'http://localhost:3000/callback'
    },
    Cerner: {
      clientId: 'your-cerner-client-id',
      clientSecret: 'your-cerner-client-secret',
      redirectUri: 'http://localhost:3000/callback'
    }
  }
});
```

### Emergency Data Access

```javascript
// Start an emergency session
const session = healthcareIntegration.startEmergencySession({
  emergencyType: 'CARDIAC',
  emergencySeverity: 'HIGH',
  responderType: 'PARAMEDIC',
  locationType: 'AMBULANCE'
});

// Get emergency medical data
const patientId = 'patient-123';
const emergencyData = await healthcareIntegration.getEmergencyMedicalData(patientId, session.sessionId);

// Use the data for emergency response
// ...

// End the emergency session when done
healthcareIntegration.endEmergencySession(session.sessionId);
```

### Integration with NovaDNA API

```javascript
// In your API route handler
async function handleEmergencyDataRequest(req, res, next) {
  try {
    // Get NovaDNA instance from request
    const { novaDNA } = req;
    
    // Get healthcare integration from NovaDNA
    const healthcareIntegration = novaDNA.healthcareIntegration;
    
    // Get emergency context from request
    const { emergencyType, emergencySeverity } = req.body.context || {};
    
    // Start an emergency session
    const session = healthcareIntegration.startEmergencySession({
      emergencyType,
      emergencySeverity
    });
    
    // Get patient ID from request
    const { patientId } = req.body;
    
    // Get emergency medical data
    const emergencyData = await healthcareIntegration.getEmergencyMedicalData(patientId, session.sessionId);
    
    // Return the data
    res.json({
      status: 'success',
      data: {
        profile: emergencyData,
        session: {
          sessionId: session.sessionId,
          expiresAt: session.expiresAt
        }
      }
    });
  } catch (error) {
    next(error);
  }
}
```

## Components

### EmergencyDataPipeline

The `EmergencyDataPipeline` class provides a secure data pipeline for emergency medical data. It creates secure transfer sessions, fetches data from connected EHR systems, and manages the data lifecycle.

```javascript
const EmergencyDataPipeline = require('./EmergencyDataPipeline');
const NovaConnectAdapter = require('../NovaConnectAdapter');

// Initialize NovaConnectAdapter
const novaConnectAdapter = new NovaConnectAdapter({
  apiUrl: 'http://localhost:3000/api/novaconnect',
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});

// Initialize EmergencyDataPipeline
const emergencyDataPipeline = new EmergencyDataPipeline({
  novaConnectAdapter,
  cacheEnabled: true,
  cacheTimeout: 300000 // 5 minutes
});

// Create a transfer session
const session = emergencyDataPipeline.createTransferSession({
  emergencyType: 'CARDIAC',
  emergencySeverity: 'HIGH'
});

// Fetch emergency data
const patientId = 'patient-123';
const emergencyData = await emergencyDataPipeline.fetchEmergencyData(patientId, session.sessionId);

// Close the session when done
emergencyDataPipeline.closeSession(session.sessionId);
```

### ProviderConnector

The `ProviderConnector` class provides specific connectors for major healthcare providers. It handles authentication, data fetching, and connection management.

```javascript
const ProviderConnector = require('./ProviderConnector');
const NovaConnectAdapter = require('../NovaConnectAdapter');

// Initialize NovaConnectAdapter
const novaConnectAdapter = new NovaConnectAdapter({
  apiUrl: 'http://localhost:3000/api/novaconnect',
  apiKey: 'your-api-key',
  apiSecret: 'your-api-secret'
});

// Initialize ProviderConnector
const providerConnector = new ProviderConnector({
  novaConnectAdapter
});

// Connect to Epic
await providerConnector.connectToEpic({
  clientId: 'your-epic-client-id',
  clientSecret: 'your-epic-client-secret',
  redirectUri: 'http://localhost:3000/callback'
});

// Fetch patient data from Epic
const patientId = 'patient-123';
const patientData = await providerConnector.fetchPatientDataFromProvider('Epic', patientId, {
  dataTypes: ['demographics', 'allergies', 'medications']
});

// Check connection status
const status = providerConnector.getConnectionStatus();
```

### DataSourcePrioritization

The `DataSourcePrioritization` class provides logic for prioritizing data sources based on emergency context. It helps determine which data sources to use and in what order.

```javascript
const DataSourcePrioritization = require('./DataSourcePrioritization');

// Initialize DataSourcePrioritization
const dataSourcePrioritization = new DataSourcePrioritization();

// Define data sources
const dataSources = [
  {
    id: 'epic-1',
    type: 'EHR',
    provider: 'Epic',
    availableDataTypes: ['demographics', 'allergies', 'medications', 'conditions'],
    lastUpdated: '2023-04-27T12:34:56.789Z'
  },
  {
    id: 'cerner-1',
    type: 'EHR',
    provider: 'Cerner',
    availableDataTypes: ['demographics', 'allergies', 'medications'],
    lastUpdated: '2023-04-26T10:20:30.456Z'
  }
];

// Define emergency context
const context = {
  emergencyType: 'CARDIAC',
  emergencySeverity: 'HIGH'
};

// Prioritize data sources
const prioritizedSources = dataSourcePrioritization.prioritizeDataSources(dataSources, context);
```

### SecureTemporaryCache

The `SecureTemporaryCache` class provides secure temporary caching for emergency medical data. It ensures that data is only cached for the duration of an emergency and is securely deleted afterward.

```javascript
const SecureTemporaryCache = require('./SecureTemporaryCache');

// Initialize SecureTemporaryCache
const secureTemporaryCache = new SecureTemporaryCache({
  enabled: true,
  encryptionEnabled: true,
  defaultTTL: 300000, // 5 minutes
  maxTTL: 3600000 // 1 hour
});

// Store data in the cache
const key = 'patient-123:session-456';
const data = { /* patient data */ };
secureTemporaryCache.store(key, data, {
  ttl: 300000, // 5 minutes
  context: {
    sessionId: 'session-456',
    patientId: 'patient-123',
    emergencyType: 'CARDIAC'
  }
});

// Retrieve data from the cache
const cachedData = secureTemporaryCache.retrieve(key, {
  sessionId: 'session-456',
  action: 'RETRIEVE'
});

// Remove data from the cache
secureTemporaryCache.remove(key, {
  sessionId: 'session-456',
  reason: 'SESSION_CLOSED'
});

// Get cache statistics
const stats = secureTemporaryCache.getStats();
```

## Integration with NovaFuse Ecosystem

This healthcare integration leverages the existing NovaFuse ecosystem:

1. Uses NovaConnect (NUAC) for healthcare system integration
2. Implements secure data pipeline for emergency scenarios
3. Provides context-aware data prioritization
4. Ensures data is only accessible during emergencies

By using this integration, you can quickly implement healthcare data access for NovaDNA without having to build everything from scratch.
