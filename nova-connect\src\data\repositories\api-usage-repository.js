/**
 * NovaFuse Universal API Connector - API Usage Repository
 * 
 * This module provides data access methods for API usage tracking.
 */

const ApiUsage = require('../models/api-usage');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('api-usage-repository');

/**
 * API Usage Repository
 * 
 * Provides methods for accessing and manipulating API usage data.
 */
class ApiUsageRepository {
  /**
   * Record API usage
   * @param {Object} usageData - API usage data
   * @returns {Promise<Object>} Recorded API usage
   */
  async recordUsage(usageData) {
    try {
      logger.debug('Recording API usage');
      return await ApiUsage.recordUsage(usageData);
    } catch (error) {
      logger.error('Error recording API usage:', error);
      // Don't throw - we don't want API usage tracking to break the main functionality
      return null;
    }
  }

  /**
   * Get usage statistics
   * @param {Object} filter - Filter criteria
   * @param {string} timeframe - Timeframe (day, week, month, year)
   * @returns {Promise<Object>} Usage statistics
   */
  async getStatistics(filter = {}, timeframe = 'month') {
    try {
      logger.debug(`Getting API usage statistics for timeframe: ${timeframe}`);
      return await ApiUsage.getStatistics(filter, timeframe);
    } catch (error) {
      logger.error('Error getting API usage statistics:', error);
      throw error;
    }
  }

  /**
   * Get usage by partner
   * @param {string} partnerId - Partner ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} API usage records
   */
  async getUsageByPartner(partnerId, options = {}) {
    try {
      const { limit = 100, skip = 0, startDate, endDate } = options;
      
      logger.debug(`Getting API usage for partner: ${partnerId}`);
      
      const query = { partnerId };
      
      // Add date range if provided
      if (startDate || endDate) {
        query.timestamp = {};
        
        if (startDate) {
          query.timestamp.$gte = new Date(startDate);
        }
        
        if (endDate) {
          query.timestamp.$lte = new Date(endDate);
        }
      }
      
      return await ApiUsage.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting API usage for partner ${partnerId}:`, error);
      throw error;
    }
  }

  /**
   * Get usage by connector
   * @param {string} connectorId - Connector ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} API usage records
   */
  async getUsageByConnector(connectorId, options = {}) {
    try {
      const { limit = 100, skip = 0, startDate, endDate, partnerId } = options;
      
      logger.debug(`Getting API usage for connector: ${connectorId}`);
      
      const query = { connectorId };
      
      // Add partner filter if provided
      if (partnerId) {
        query.partnerId = partnerId;
      }
      
      // Add date range if provided
      if (startDate || endDate) {
        query.timestamp = {};
        
        if (startDate) {
          query.timestamp.$gte = new Date(startDate);
        }
        
        if (endDate) {
          query.timestamp.$lte = new Date(endDate);
        }
      }
      
      return await ApiUsage.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting API usage for connector ${connectorId}:`, error);
      throw error;
    }
  }

  /**
   * Get usage by endpoint
   * @param {string} connectorId - Connector ID
   * @param {string} endpointId - Endpoint ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} API usage records
   */
  async getUsageByEndpoint(connectorId, endpointId, options = {}) {
    try {
      const { limit = 100, skip = 0, startDate, endDate, partnerId } = options;
      
      logger.debug(`Getting API usage for endpoint: ${connectorId}/${endpointId}`);
      
      const query = { connectorId, endpointId };
      
      // Add partner filter if provided
      if (partnerId) {
        query.partnerId = partnerId;
      }
      
      // Add date range if provided
      if (startDate || endDate) {
        query.timestamp = {};
        
        if (startDate) {
          query.timestamp.$gte = new Date(startDate);
        }
        
        if (endDate) {
          query.timestamp.$lte = new Date(endDate);
        }
      }
      
      return await ApiUsage.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting API usage for endpoint ${connectorId}/${endpointId}:`, error);
      throw error;
    }
  }

  /**
   * Get usage by credential
   * @param {string} credentialId - Credential ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} API usage records
   */
  async getUsageByCredential(credentialId, options = {}) {
    try {
      const { limit = 100, skip = 0, startDate, endDate } = options;
      
      logger.debug(`Getting API usage for credential: ${credentialId}`);
      
      const query = { credentialId };
      
      // Add date range if provided
      if (startDate || endDate) {
        query.timestamp = {};
        
        if (startDate) {
          query.timestamp.$gte = new Date(startDate);
        }
        
        if (endDate) {
          query.timestamp.$lte = new Date(endDate);
        }
      }
      
      return await ApiUsage.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit);
    } catch (error) {
      logger.error(`Error getting API usage for credential ${credentialId}:`, error);
      throw error;
    }
  }

  /**
   * Get billable usage
   * @param {string} partnerId - Partner ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Billable API usage records
   */
  async getBillableUsage(partnerId, options = {}) {
    try {
      const { startDate, endDate } = options;
      
      logger.debug(`Getting billable API usage for partner: ${partnerId}`);
      
      const query = { partnerId, billable: true };
      
      // Add date range if provided
      if (startDate || endDate) {
        query.timestamp = {};
        
        if (startDate) {
          query.timestamp.$gte = new Date(startDate);
        }
        
        if (endDate) {
          query.timestamp.$lte = new Date(endDate);
        }
      }
      
      return await ApiUsage.find(query).sort({ timestamp: -1 });
    } catch (error) {
      logger.error(`Error getting billable API usage for partner ${partnerId}:`, error);
      throw error;
    }
  }

  /**
   * Get usage summary
   * @param {string} partnerId - Partner ID
   * @param {string} timeframe - Timeframe (day, week, month, year)
   * @returns {Promise<Object>} Usage summary
   */
  async getUsageSummary(partnerId, timeframe = 'month') {
    try {
      logger.debug(`Getting API usage summary for partner: ${partnerId}`);
      
      // Get overall statistics
      const statistics = await this.getStatistics({ partnerId }, timeframe);
      
      // Get connector breakdown
      const connectorBreakdown = await ApiUsage.aggregate([
        { $match: { partnerId, timestamp: statistics._timeframeFilter } },
        { 
          $group: {
            _id: '$connectorId',
            totalRequests: { $sum: 1 },
            successfulRequests: { $sum: { $cond: [{ $eq: ['$success', true] }, 1, 0] } },
            totalDuration: { $sum: '$duration' },
            totalCost: { $sum: '$cost' }
          }
        },
        { $sort: { totalRequests: -1 } }
      ]);
      
      // Get endpoint breakdown
      const endpointBreakdown = await ApiUsage.aggregate([
        { $match: { partnerId, timestamp: statistics._timeframeFilter } },
        { 
          $group: {
            _id: { connectorId: '$connectorId', endpointId: '$endpointId' },
            totalRequests: { $sum: 1 },
            successfulRequests: { $sum: { $cond: [{ $eq: ['$success', true] }, 1, 0] } },
            totalDuration: { $sum: '$duration' },
            totalCost: { $sum: '$cost' }
          }
        },
        { $sort: { totalRequests: -1 } },
        { $limit: 10 }
      ]);
      
      return {
        statistics,
        connectorBreakdown: connectorBreakdown.map(item => ({
          connectorId: item._id,
          totalRequests: item.totalRequests,
          successfulRequests: item.successfulRequests,
          successRate: item.totalRequests > 0 ? (item.successfulRequests / item.totalRequests) * 100 : 0,
          totalDuration: item.totalDuration,
          averageDuration: item.totalRequests > 0 ? item.totalDuration / item.totalRequests : 0,
          totalCost: item.totalCost
        })),
        endpointBreakdown: endpointBreakdown.map(item => ({
          connectorId: item._id.connectorId,
          endpointId: item._id.endpointId,
          totalRequests: item.totalRequests,
          successfulRequests: item.successfulRequests,
          successRate: item.totalRequests > 0 ? (item.successfulRequests / item.totalRequests) * 100 : 0,
          totalDuration: item.totalDuration,
          averageDuration: item.totalRequests > 0 ? item.totalDuration / item.totalRequests : 0,
          totalCost: item.totalCost
        }))
      };
    } catch (error) {
      logger.error(`Error getting API usage summary for partner ${partnerId}:`, error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new ApiUsageRepository();
