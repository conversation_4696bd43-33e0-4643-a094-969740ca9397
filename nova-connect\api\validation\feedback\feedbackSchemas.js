/**
 * Feedback Validation Schemas
 * 
 * This file defines the validation schemas for feedback-related API requests.
 */

const Joi = require('joi');

/**
 * Submit feedback schema
 */
const submitFeedbackSchema = {
  body: Joi.object({
    type: Joi.string()
      .valid('general', 'feature', 'bug', 'suggestion', 'usability', 'performance', 'other')
      .required()
      .messages({
        'any.required': 'Feedback type is required',
        'any.only': 'Invalid feedback type'
      }),
    component: Joi.string()
      .required()
      .messages({
        'any.required': 'Component is required'
      }),
    rating: Joi.number()
      .integer()
      .min(1)
      .max(5)
      .required()
      .messages({
        'any.required': 'Rating is required',
        'number.base': 'Rating must be a number',
        'number.min': 'Rating must be at least 1',
        'number.max': 'Rating must be at most 5'
      }),
    title: Joi.string()
      .required()
      .trim()
      .max(200)
      .messages({
        'any.required': 'Title is required',
        'string.max': 'Title must be at most 200 characters'
      }),
    description: Joi.string()
      .required()
      .trim()
      .max(5000)
      .messages({
        'any.required': 'Description is required',
        'string.max': 'Description must be at most 5000 characters'
      }),
    metadata: Joi.object()
      .optional(),
    tags: Joi.array()
      .items(Joi.string().trim())
      .optional()
  })
};

/**
 * Get feedback by ID schema
 */
const getFeedbackByIdSchema = {
  params: Joi.object({
    id: Joi.string()
      .required()
      .messages({
        'any.required': 'Feedback ID is required'
      })
  })
};

/**
 * Get my feedback schema
 */
const getMyFeedbackSchema = {
  query: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.min': 'Page must be at least 1'
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit must be at most 100'
      }),
    sortBy: Joi.string()
      .valid('createdAt', 'updatedAt', 'rating', 'status')
      .optional()
      .default('createdAt')
      .messages({
        'any.only': 'Invalid sort field'
      }),
    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .optional()
      .default('desc')
      .messages({
        'any.only': 'Sort order must be either "asc" or "desc"'
      }),
    type: Joi.string()
      .valid('general', 'feature', 'bug', 'suggestion', 'usability', 'performance', 'other')
      .optional()
      .messages({
        'any.only': 'Invalid feedback type'
      }),
    component: Joi.string()
      .optional(),
    status: Joi.string()
      .valid('new', 'in_review', 'planned', 'in_progress', 'completed', 'declined')
      .optional()
      .messages({
        'any.only': 'Invalid status'
      })
  })
};

/**
 * Get all feedback schema
 */
const getAllFeedbackSchema = {
  query: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.min': 'Page must be at least 1'
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit must be at most 100'
      }),
    sortBy: Joi.string()
      .valid('createdAt', 'updatedAt', 'rating', 'status')
      .optional()
      .default('createdAt')
      .messages({
        'any.only': 'Invalid sort field'
      }),
    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .optional()
      .default('desc')
      .messages({
        'any.only': 'Sort order must be either "asc" or "desc"'
      }),
    type: Joi.string()
      .valid('general', 'feature', 'bug', 'suggestion', 'usability', 'performance', 'other')
      .optional()
      .messages({
        'any.only': 'Invalid feedback type'
      }),
    component: Joi.string()
      .optional(),
    status: Joi.string()
      .valid('new', 'in_review', 'planned', 'in_progress', 'completed', 'declined')
      .optional()
      .messages({
        'any.only': 'Invalid status'
      }),
    tag: Joi.string()
      .optional(),
    search: Joi.string()
      .optional()
      .trim()
      .max(100)
      .messages({
        'string.max': 'Search query must be at most 100 characters'
      })
  })
};

/**
 * Update feedback status schema
 */
const updateFeedbackStatusSchema = {
  params: Joi.object({
    id: Joi.string()
      .required()
      .messages({
        'any.required': 'Feedback ID is required'
      })
  }),
  body: Joi.object({
    status: Joi.string()
      .valid('new', 'in_review', 'planned', 'in_progress', 'completed', 'declined')
      .required()
      .messages({
        'any.required': 'Status is required',
        'any.only': 'Invalid status'
      }),
    response: Joi.string()
      .optional()
      .trim()
      .max(5000)
      .messages({
        'string.max': 'Response must be at most 5000 characters'
      })
  })
};

// Export schemas
const feedbackSchemas = {
  submitFeedbackSchema,
  getFeedbackByIdSchema,
  getMyFeedbackSchema,
  getAllFeedbackSchema,
  updateFeedbackStatusSchema
};

module.exports = {
  feedbackSchemas
};
