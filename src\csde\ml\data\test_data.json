[{"input": {"complianceData": {"complianceScore": 0.7568406045310452, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "partial", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "medium", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.1600994513998646, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.6275930210629075, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "high", "status": "implemented", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "high", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 358994.6877540936, "performanceFactor": 3142, "nistComponent": 7.568406045310452, "gcpComponent": 1.600994513998646, "cyberSafetyComponent": 19.718972721796554, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.7568406045310452, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "partial", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "medium", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 7.568406045310452}, "componentB": {"originalData": {"integrationScore": 0.1600994513998646, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "medium", "status": "non-optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 1.600994513998646}, "tensorMatrix": [[12.116976558256221, 24.233953116512442, 36.350929674768665], [24.233953116512442, 48.467906233024884, 72.70185934953733], [36.35092967476866, 72.70185934953732, 109.05278902430598]], "tensorValue": 48.46790623302488, "normalizedValue": 48.46790623302488, "dimensions": 3}}, "output": {"csdeValue": 358994.6877540936, "remediationPriorities": [{"id": "RA-GDPR-1", "priority": "critical", "automationPotential": "high"}, {"id": "RA-GDPR-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-2", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-GCP-1", "priority": "medium", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.8740281409086059, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "high", "status": "compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "high", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "high", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "low", "status": "partial", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.7406202198262986, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.9861631079381574, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 1492630.8486599282, "performanceFactor": 3142, "nistComponent": 8.740281409086059, "gcpComponent": 7.406202198262985, "cyberSafetyComponent": 30.98524485141691, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.8740281409086059, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "high", "status": "compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "high", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "high", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "low", "status": "partial", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 8.740281409086059}, "componentB": {"originalData": {"integrationScore": 0.7406202198262986, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "non-optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 7.406202198262985}, "tensorMatrix": [[64.73229138541026, 129.46458277082053, 194.1968741562308], [129.46458277082053, 258.92916554164105, 388.3937483124616], [194.19687415623082, 388.39374831246164, 582.5906224686925]], "tensorValue": 258.9291655416411, "normalizedValue": 258.9291655416411, "dimensions": 3}}, "output": {"csdeValue": 1492630.8486599282, "remediationPriorities": [{"id": "RA-HIPAA-2", "priority": "critical", "automationPotential": "high"}, {"id": "RA-GDPR-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-1", "priority": "low", "automationPotential": "high"}, {"id": "RA-HIPAA-3", "priority": "low", "automationPotential": "high"}, {"id": "RA-CS-P1", "priority": "low", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.7971976920137385, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "partial", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "high", "status": "non-compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "low", "status": "partial", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.010354160942672497, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.39561921638778563, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "high", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "low", "status": "partial", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 86571.50575793104, "performanceFactor": 3142, "nistComponent": 7.971976920137385, "gcpComponent": 0.10354160942672497, "cyberSafetyComponent": 12.430355778904225, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.7971976920137385, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "partial", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "high", "status": "non-compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "low", "status": "partial", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 7.971976920137385}, "componentB": {"originalData": {"integrationScore": 0.010354160942672497, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "medium", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 0.10354160942672497}, "tensorMatrix": [[0.8254313206237309, 1.6508626412474618, 2.4762939618711926], [1.6508626412474618, 3.3017252824949237, 4.952587923742385], [2.4762939618711926, 4.952587923742385, 7.428881885613578]], "tensorValue": 3.3017252824949233, "normalizedValue": 3.3017252824949233, "dimensions": 3}}, "output": {"csdeValue": 86571.50575793104, "remediationPriorities": [{"id": "RA-GDPR-2", "priority": "critical", "automationPotential": "high"}, {"id": "RA-NIST-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-NIST-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-2", "priority": "high", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.48903086107495763, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "compliant", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.9961880989354379, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.5681320343859997, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "medium", "status": "partial", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 1097311.4291941368, "performanceFactor": 3142, "nistComponent": 4.890308610749576, "gcpComponent": 9.961880989354379, "cyberSafetyComponent": 17.850708520408112, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.48903086107495763, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "compliant", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 4.890308610749576}, "componentB": {"originalData": {"integrationScore": 0.9961880989354379, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "medium", "status": "partial", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "medium", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 9.961880989354379}, "tensorMatrix": [[48.71667238150223, 97.43334476300446, 146.15001714450668], [97.43334476300446, 194.8666895260089, 292.30003428901335], [146.15001714450668, 292.30003428901335, 438.45005143352]], "tensorValue": 194.86668952600888, "normalizedValue": 194.86668952600888, "dimensions": 3}}, "output": {"csdeValue": 1097311.4291941368, "remediationPriorities": [{"id": "RA-NIST-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-NIST-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-GCP-1", "priority": "low", "automationPotential": "high"}, {"id": "RA-GCP-2", "priority": "low", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.4567165370960977, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "high", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "medium", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "high", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "medium", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "high", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "partial", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.15398030621582404, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.6548273645666598, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "low", "status": "partial", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 258782.47122340012, "performanceFactor": 3142, "nistComponent": 4.567165370960977, "gcpComponent": 1.5398030621582404, "cyberSafetyComponent": 20.574675794684453, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.4567165370960977, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "high", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "medium", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "high", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "medium", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "high", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "partial", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 4.567165370960977}, "componentB": {"originalData": {"integrationScore": 0.15398030621582404, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 1.5398030621582404}, "tensorMatrix": [[7.032535223588788, 14.065070447177575, 21.097605670766363], [14.065070447177575, 28.13014089435515, 42.195211341532726], [21.097605670766363, 42.195211341532726, 63.29281701229909]], "tensorValue": 28.13014089435515, "normalizedValue": 28.13014089435515, "dimensions": 3}}, "output": {"csdeValue": 258782.47122340012, "remediationPriorities": [{"id": "RA-NIST-1", "priority": "critical", "automationPotential": "high"}, {"id": "RA-NIST-3", "priority": "critical", "automationPotential": "high"}, {"id": "RA-GDPR-3", "priority": "critical", "automationPotential": "high"}, {"id": "RA-HIPAA-2", "priority": "critical", "automationPotential": "high"}, {"id": "RA-HIPAA-3", "priority": "high", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.27321694818523823, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "partial", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "medium", "status": "compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "high", "status": "compliant", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.17798607748959605, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.1689909822481701, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "low", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 133806.14128923995, "performanceFactor": 3142, "nistComponent": 2.7321694818523823, "gcpComponent": 1.7798607748959605, "cyberSafetyComponent": 5.3096966622375055, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.27321694818523823, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "partial", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "medium", "status": "compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "high", "status": "compliant", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 2.7321694818523823}, "componentB": {"originalData": {"integrationScore": 0.17798607748959605, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 1.7798607748959605}, "tensorMatrix": [[4.862881291116876, 9.725762582233752, 14.588643873350629], [9.725762582233752, 19.451525164467505, 29.177287746701257], [14.588643873350627, 29.177287746701253, 43.76593162005188]], "tensorValue": 19.4515251644675, "normalizedValue": 19.4515251644675, "dimensions": 3}}, "output": {"csdeValue": 133806.14128923995, "remediationPriorities": [{"id": "RA-GDPR-1", "priority": "critical", "automationPotential": "high"}, {"id": "RA-NIST-2", "priority": "high", "automationPotential": "high"}, {"id": "RA-NIST-1", "priority": "low", "automationPotential": "high"}, {"id": "RA-HIPAA-1", "priority": "low", "automationPotential": "high"}, {"id": "RA-HIPAA-2", "priority": "low", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.2658680106216942, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "medium", "status": "partial", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "medium", "status": "non-compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "medium", "status": "compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "low", "status": "partial", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.7014652886197124, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.43964015426425873, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "high", "status": "partial", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 461990.8375366626, "performanceFactor": 3142, "nistComponent": 2.658680106216942, "gcpComponent": 7.014652886197124, "cyberSafetyComponent": 13.81349364698301, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.2658680106216942, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "medium", "status": "partial", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "medium", "status": "non-compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "partial", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "medium", "status": "compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "low", "status": "partial", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 2.658680106216942}, "componentB": {"originalData": {"integrationScore": 0.7014652886197124, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "high", "status": "non-optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 7.014652886197124}, "tensorMatrix": [[18.649718080549547, 37.299436161099095, 55.94915424164864], [37.299436161099095, 74.59887232219819, 111.89830848329728], [55.94915424164864, 111.89830848329728, 167.84746272494593]], "tensorValue": 74.59887232219819, "normalizedValue": 74.59887232219819, "dimensions": 3}}, "output": {"csdeValue": 461990.8375366626, "remediationPriorities": [{"id": "RA-GDPR-1", "priority": "critical", "automationPotential": "high"}, {"id": "RA-GDPR-3", "priority": "critical", "automationPotential": "high"}, {"id": "RA-NIST-2", "priority": "high", "automationPotential": "high"}, {"id": "RA-NIST-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-GDPR-2", "priority": "high", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.95471868014315, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "partial", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "high", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "high", "status": "non-compliant", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.9553073980973534, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.3362005108151578, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "medium", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "high", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 1924490.4093514143, "performanceFactor": 3142, "nistComponent": 9.5471868014315, "gcpComponent": 9.553073980973535, "cyberSafetyComponent": 10.563420049812258, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.95471868014315, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "partial", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "high", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "medium", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "high", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "high", "status": "non-compliant", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 9.5471868014315}, "componentB": {"originalData": {"integrationScore": 0.9553073980973534, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "partial", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "partial", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 9.553073980973535}, "tensorMatrix": [[91.20498182424922, 182.40996364849843, 273.61494547274765], [182.40996364849843, 364.81992729699687, 547.2298909454953], [273.61494547274765, 547.2298909454953, 820.844836418243]], "tensorValue": 364.81992729699687, "normalizedValue": 364.81992729699687, "dimensions": 3}}, "output": {"csdeValue": 1924490.4093514143, "remediationPriorities": [{"id": "RA-GDPR-3", "priority": "critical", "automationPotential": "high"}, {"id": "RA-HIPAA-2", "priority": "critical", "automationPotential": "high"}, {"id": "RA-HIPAA-3", "priority": "critical", "automationPotential": "high"}, {"id": "RA-NIST-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-GDPR-1", "priority": "high", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.14042777315352595, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "high", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "high", "status": "partial", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.5181970033939076, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.515180123602424, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "low", "status": "not-implemented", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "low", "status": "implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "medium", "status": "implemented", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 240996.45851007552, "performanceFactor": 3142, "nistComponent": 1.4042777315352595, "gcpComponent": 5.181970033939076, "cyberSafetyComponent": 16.186959483588165, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.14042777315352595, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "high", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "low", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "non-compliant", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "high", "status": "partial", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 1.4042777315352595}, "componentB": {"originalData": {"integrationScore": 0.5181970033939076, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "high", "status": "partial", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "low", "status": "partial", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "low", "status": "optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 5.181970033939076}, "tensorMatrix": [[7.276925124143657, 14.553850248287315, 21.83077537243097], [14.553850248287315, 29.10770049657463, 43.66155074486194], [21.830775372430974, 43.66155074486195, 65.49232611729292]], "tensorValue": 29.10770049657463, "normalizedValue": 29.10770049657463, "dimensions": 3}}, "output": {"csdeValue": 240996.45851007552, "remediationPriorities": [{"id": "RA-NIST-2", "priority": "critical", "automationPotential": "high"}, {"id": "RA-HIPAA-3", "priority": "critical", "automationPotential": "high"}, {"id": "RA-GDPR-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-GCP-1", "priority": "medium", "automationPotential": "high"}, {"id": "RA-NIST-1", "priority": "low", "automationPotential": "high"}]}}, {"input": {"complianceData": {"complianceScore": 0.6575530888564607, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "partial", "framework": "HIPAA"}]}, "gcpData": {"integrationScore": 0.8853867475620951, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}]}, "cyberSafetyData": {"safetyScore": 0.9543545613268092, "controls": [{"id": "CS-P1", "name": "Cyber-Safety Control 1", "description": "Description for Cyber-Safety control 1", "severity": "low", "status": "partial", "pillar": "Pillar 1"}, {"id": "CS-P2", "name": "Cyber-Safety Control 2", "description": "Description for Cyber-Safety control 2", "severity": "medium", "status": "not-implemented", "pillar": "Pillar 2"}, {"id": "CS-P3", "name": "Cyber-Safety Control 3", "description": "Description for Cyber-Safety control 3", "severity": "high", "status": "not-implemented", "pillar": "Pillar 3"}]}}, "csdeFeatures": {"csdeValue": 1354598.0113055424, "performanceFactor": 3142, "nistComponent": 6.575530888564607, "gcpComponent": 8.853867475620952, "cyberSafetyComponent": 29.985820316888347, "tensorProduct": {"componentA": {"originalData": {"complianceScore": 0.6575530888564607, "controls": [{"id": "NIST-1", "name": "NIST Control 1", "description": "Description for NIST control 1", "severity": "low", "status": "non-compliant", "framework": "NIST"}, {"id": "NIST-2", "name": "NIST Control 2", "description": "Description for NIST control 2", "severity": "low", "status": "compliant", "framework": "NIST"}, {"id": "NIST-3", "name": "NIST Control 3", "description": "Description for NIST control 3", "severity": "medium", "status": "non-compliant", "framework": "NIST"}, {"id": "GDPR-1", "name": "GDPR Control 1", "description": "Description for GDPR control 1", "severity": "medium", "status": "partial", "framework": "GDPR"}, {"id": "GDPR-2", "name": "GDPR Control 2", "description": "Description for GDPR control 2", "severity": "high", "status": "compliant", "framework": "GDPR"}, {"id": "GDPR-3", "name": "GDPR Control 3", "description": "Description for GDPR control 3", "severity": "low", "status": "non-compliant", "framework": "GDPR"}, {"id": "HIPAA-1", "name": "HIPAA Control 1", "description": "Description for HIPAA control 1", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-2", "name": "HIPAA Control 2", "description": "Description for HIPAA control 2", "severity": "low", "status": "partial", "framework": "HIPAA"}, {"id": "HIPAA-3", "name": "HIPAA Control 3", "description": "Description for HIPAA control 3", "severity": "medium", "status": "partial", "framework": "HIPAA"}]}, "multiplier": 10, "processedValue": 6.575530888564607}, "componentB": {"originalData": {"integrationScore": 0.8853867475620951, "services": [{"id": "GCP-1", "name": "GCP Service 1", "description": "Description for GCP service 1", "severity": "low", "status": "non-optimal", "service": "GCP Service 1"}, {"id": "GCP-2", "name": "GCP Service 2", "description": "Description for GCP service 2", "severity": "high", "status": "non-optimal", "service": "GCP Service 2"}, {"id": "GCP-3", "name": "GCP Service 3", "description": "Description for GCP service 3", "severity": "medium", "status": "optimal", "service": "GCP Service 3"}]}, "multiplier": 10, "processedValue": 8.853867475620952}, "tensorMatrix": [[58.21887906920311, 116.43775813840622, 174.65663720760932], [116.43775813840622, 232.87551627681245, 349.31327441521864], [174.65663720760935, 349.3132744152187, 523.969911622828]], "tensorValue": 232.87551627681245, "normalizedValue": 232.87551627681245, "dimensions": 3}}, "output": {"csdeValue": 1354598.0113055424, "remediationPriorities": [{"id": "RA-NIST-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-GDPR-1", "priority": "high", "automationPotential": "high"}, {"id": "RA-HIPAA-3", "priority": "high", "automationPotential": "high"}, {"id": "RA-GCP-2", "priority": "medium", "automationPotential": "high"}, {"id": "RA-CS-P3", "priority": "low", "automationPotential": "high"}]}}]