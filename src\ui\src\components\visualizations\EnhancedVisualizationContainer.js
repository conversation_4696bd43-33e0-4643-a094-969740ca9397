import React, { useState, useRef, useEffect } from 'react';
import { Box, Paper, Typography, Divider, CircularProgress } from '@mui/material';
import VisualizationRenderer from './VisualizationRenderer';
import VisualizationExport from './VisualizationExport';
import VisualizationInteraction from './VisualizationInteraction';
import VisualizationPerformance from './VisualizationPerformance';
import VisualizationPreferences from './VisualizationPreferences';
import useTensorData from '../../hooks/useTensorData';
import userPreferencesService from '../../services/UserPreferencesService';

/**
 * EnhancedVisualizationContainer component
 * 
 * A container for visualizations with enhanced features like export, interaction, performance monitoring, and user preferences
 */
function EnhancedVisualizationContainer({
  tensorId,
  visualizationType: initialVisualizationType,
  dimensions: initialDimensions,
  options: initialOptions,
  width = '100%',
  height = '500px',
  title,
  showControls = true,
  useRealtime = false,
  realtimeInterval = 1000
}) {
  // Get default preferences
  const defaultPreferences = userPreferencesService.getVisualizationPreferences();
  
  // State
  const [visualizationType, setVisualizationType] = useState(
    initialVisualizationType || defaultPreferences.defaultType || '3d_tensor_visualization'
  );
  const [dimensions, setDimensions] = useState(
    initialDimensions || [10, 10, 1]
  );
  const [options, setOptions] = useState({
    ...defaultPreferences,
    ...initialOptions
  });
  const [renderer, setRenderer] = useState(null);
  const [scene, setScene] = useState(null);
  
  // Refs
  const containerRef = useRef(null);
  
  // Get tensor data
  const { tensor, isLoading, error, refresh } = useTensorData(tensorId, {
    useRealtime,
    realtimeInterval,
    useFallback: true,
    fallbackOptions: {
      dataType: 'sine',
      dataSize: 100,
      noiseLevel: 0.1,
      dimensions
    }
  });
  
  // Handle renderer and scene references
  const handleRendererRef = (ref) => {
    if (ref && ref.renderer && ref.scene) {
      setRenderer(ref.renderer);
      setScene(ref.scene);
    }
  };
  
  // Handle options change
  const handleOptionsChange = (newOptions) => {
    setOptions(prevOptions => ({
      ...prevOptions,
      ...newOptions
    }));
  };
  
  // Handle preset load
  const handlePresetLoad = (preset) => {
    if (preset) {
      setVisualizationType(preset.visualizationType);
      setOptions(preset.options);
    }
  };
  
  // Handle interaction change
  const handleInteractionChange = (interaction) => {
    // Update options based on interaction
    handleOptionsChange({
      rotationSpeed: interaction.rotationSpeed,
      showAxes: interaction.showAxes,
      showGrid: interaction.showGrid
    });
  };
  
  // Handle selection change
  const handleSelectionChange = (selection) => {
    // Handle selection
    console.log('Selection changed:', selection);
  };
  
  // Handle filter change
  const handleFilterChange = (filter) => {
    // Handle filter
    console.log('Filter changed:', filter);
  };

  return (
    <Paper
      elevation={3}
      sx={{
        width,
        height,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Header */}
      {title && (
        <>
          <Box
            sx={{
              p: 2,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}
          >
            <Typography variant="h6">{title}</Typography>
            
            {showControls && (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <VisualizationExport
                  canvasRef={containerRef}
                  visualizationType={visualizationType}
                />
                
                <VisualizationPreferences
                  visualizationType={visualizationType}
                  options={options}
                  onOptionsChange={handleOptionsChange}
                  onPresetLoad={handlePresetLoad}
                />
                
                <VisualizationPerformance
                  renderer={renderer}
                  scene={scene}
                />
              </Box>
            )}
          </Box>
          <Divider />
        </>
      )}
      
      {/* Content */}
      <Box
        sx={{
          display: 'flex',
          flexGrow: 1,
          overflow: 'hidden'
        }}
      >
        {/* Visualization */}
        <Box
          ref={containerRef}
          sx={{
            flexGrow: 1,
            position: 'relative'
          }}
        >
          {isLoading ? (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%'
              }}
            >
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                p: 2,
                textAlign: 'center'
              }}
            >
              <Typography color="error">
                Error loading tensor data: {error}
              </Typography>
            </Box>
          ) : (
            <VisualizationRenderer
              visualizationType={visualizationType}
              tensor={tensor}
              dimensions={dimensions}
              options={options}
              height="100%"
              onRendererRef={handleRendererRef}
            />
          )}
        </Box>
        
        {/* Controls */}
        {showControls && (
          <Box
            sx={{
              width: '60px',
              p: 1,
              borderLeft: '1px solid',
              borderColor: 'divider',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}
          >
            <VisualizationInteraction
              canvasRef={containerRef}
              visualizationType={visualizationType}
              onInteractionChange={handleInteractionChange}
              onSelectionChange={handleSelectionChange}
              onFilterChange={handleFilterChange}
            />
          </Box>
        )}
      </Box>
    </Paper>
  );
}

export default EnhancedVisualizationContainer;
