613d43fe9175665b4f421a28500ac187
/**
 * NovaFuse Universal API Connector SSRF Protection
 * 
 * This module provides SSRF (Server-Side Request Forgery) protection for the UAC.
 */

const {
  URL
} = require('url');
const dns = require('dns').promises;
const net = require('net');

/**
 * SSRF Protection Utility
 * 
 * Checks if a URL is safe to connect to by validating against a whitelist
 * and checking for private IP addresses.
 */
class SSRFProtection {
  constructor(options = {}) {
    this.options = {
      allowedHosts: [],
      allowedDomains: [],
      allowedProtocols: ['https:'],
      allowPrivateIPs: false,
      ...options
    };
  }

  /**
   * Check if a URL is safe to connect to
   * @param {string} url - URL to check
   * @returns {Promise<boolean>} - Whether the URL is safe
   */
  async isSafeUrl(url) {
    try {
      // Parse URL
      const parsedUrl = new URL(url);

      // Check protocol
      if (!this.options.allowedProtocols.includes(parsedUrl.protocol)) {
        console.warn(`SSRF Protection: Blocked URL with disallowed protocol: ${parsedUrl.protocol}`);
        return false;
      }

      // Check hostname against whitelist if enabled
      if (this.options.allowedHosts.length > 0) {
        const isHostAllowed = this.options.allowedHosts.some(host => parsedUrl.hostname === host);
        if (!isHostAllowed) {
          // Check if hostname matches any allowed domain patterns
          const isDomainAllowed = this.options.allowedDomains.some(domain => {
            // Allow wildcards (e.g., *.example.com)
            if (domain.startsWith('*.')) {
              const baseDomain = domain.substring(2);
              return parsedUrl.hostname.endsWith(baseDomain);
            }
            return parsedUrl.hostname === domain;
          });
          if (!isDomainAllowed) {
            console.warn(`SSRF Protection: Blocked URL with disallowed host: ${parsedUrl.hostname}`);
            return false;
          }
        }
      }

      // Check for private IP addresses
      if (!this.options.allowPrivateIPs) {
        // Check if hostname is an IP address
        if (net.isIP(parsedUrl.hostname)) {
          if (this.isPrivateIP(parsedUrl.hostname)) {
            console.warn(`SSRF Protection: Blocked URL with private IP: ${parsedUrl.hostname}`);
            return false;
          }
        } else {
          // Resolve hostname to IP address
          try {
            const addresses = await dns.resolve(parsedUrl.hostname);

            // Check if any resolved IP is private
            for (const address of addresses) {
              if (this.isPrivateIP(address)) {
                console.warn(`SSRF Protection: Blocked URL with hostname resolving to private IP: ${address}`);
                return false;
              }
            }
          } catch (error) {
            console.error(`SSRF Protection: Error resolving hostname: ${parsedUrl.hostname}`, error);
            return false;
          }
        }
      }
      return true;
    } catch (error) {
      console.error('SSRF Protection: Error checking URL:', error);
      return false;
    }
  }

  /**
   * Check if an IP address is private
   * @param {string} ip - IP address to check
   * @returns {boolean} - Whether the IP is private
   */
  isPrivateIP(ip) {
    // Check for loopback addresses
    if (ip === '127.0.0.1' || ip === '::1') {
      return true;
    }

    // Check for private IPv4 ranges
    if (ip.startsWith('10.') || ip.startsWith('172.16.') || ip.startsWith('172.17.') || ip.startsWith('172.18.') || ip.startsWith('172.19.') || ip.startsWith('172.20.') || ip.startsWith('172.21.') || ip.startsWith('172.22.') || ip.startsWith('172.23.') || ip.startsWith('172.24.') || ip.startsWith('172.25.') || ip.startsWith('172.26.') || ip.startsWith('172.27.') || ip.startsWith('172.28.') || ip.startsWith('172.29.') || ip.startsWith('172.30.') || ip.startsWith('172.31.') || ip.startsWith('192.168.')) {
      return true;
    }

    // Check for link-local addresses
    if (ip.startsWith('169.254.')) {
      return true;
    }

    // Check for private IPv6 ranges
    if (ip.startsWith('fc00:') || ip.startsWith('fd00:')) {
      return true;
    }
    return false;
  }

  /**
   * Add allowed hosts to the whitelist
   * @param {string|string[]} hosts - Host(s) to add
   */
  addAllowedHosts(hosts) {
    if (Array.isArray(hosts)) {
      this.options.allowedHosts = [...this.options.allowedHosts, ...hosts];
    } else {
      this.options.allowedHosts.push(hosts);
    }
  }

  /**
   * Add allowed domains to the whitelist
   * @param {string|string[]} domains - Domain(s) to add
   */
  addAllowedDomains(domains) {
    if (Array.isArray(domains)) {
      this.options.allowedDomains = [...this.options.allowedDomains, ...domains];
    } else {
      this.options.allowedDomains.push(domains);
    }
  }

  /**
   * Set allowed protocols
   * @param {string|string[]} protocols - Protocol(s) to allow
   */
  setAllowedProtocols(protocols) {
    if (Array.isArray(protocols)) {
      this.options.allowedProtocols = protocols;
    } else {
      this.options.allowedProtocols = [protocols];
    }
  }

  /**
   * Allow or disallow private IP addresses
   * @param {boolean} allow - Whether to allow private IPs
   */
  setAllowPrivateIPs(allow) {
    this.options.allowPrivateIPs = allow;
  }
}
module.exports = SSRFProtection;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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