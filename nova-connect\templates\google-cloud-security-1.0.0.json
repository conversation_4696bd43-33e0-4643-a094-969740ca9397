{"metadata": {"name": "Google Cloud Security", "version": "1.0.0", "category": "Cloud Security", "description": "Connect to Google Cloud Security Command Center API for security findings and asset inventory", "author": "NovaFuse", "tags": ["google", "cloud", "security", "gcp", "scc"], "created": "2023-06-01T00:00:00Z", "updated": "2023-06-01T00:00:00Z", "icon": "https://storage.googleapis.com/novafuse-icons/gcp-security.png", "documentationUrl": "https://cloud.google.com/security-command-center/docs"}, "authentication": {"type": "OAUTH2", "fields": {"clientId": {"type": "string", "description": "OAuth 2.0 Client ID", "required": true}, "clientSecret": {"type": "string", "description": "OAuth 2.0 Client Secret", "required": true, "sensitive": true}, "projectId": {"type": "string", "description": "Google Cloud Project ID", "required": true}}, "testConnection": {"endpoint": "organizations/{organizationId}/assets", "method": "GET", "expectedResponse": {"status": 200}}, "oauth2Config": {"authorizationUrl": "https://accounts.google.com/o/oauth2/auth", "tokenUrl": "https://oauth2.googleapis.com/token", "scopes": ["https://www.googleapis.com/auth/cloud-platform"], "grantType": "client_credentials"}}, "configuration": {"baseUrl": "https://securitycenter.googleapis.com/v1", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "rateLimit": {"requests": 100, "period": "1m"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "list-findings", "name": "List Security Findings", "description": "List security findings from Security Command Center", "path": "organizations/{organizationId}/sources/{sourceId}/findings", "method": "GET", "parameters": {"path": {"organizationId": {"type": "string", "description": "Organization ID", "required": true}, "sourceId": {"type": "string", "description": "Source ID", "required": true}}, "query": {"filter": {"type": "string", "description": "Filter expression"}, "orderBy": {"type": "string", "description": "Order by expression"}, "pageSize": {"type": "integer", "description": "Page size", "default": 100}, "pageToken": {"type": "string", "description": "Page token"}}}, "pagination": {"type": "token", "parameters": {"nextPageToken": "nextPageToken", "pageToken": "pageToken"}}, "response": {"successCode": 200, "dataPath": "findings", "schema": {"type": "object", "properties": {"findings": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "parent": {"type": "string"}, "resourceName": {"type": "string"}, "state": {"type": "string"}, "category": {"type": "string"}, "severity": {"type": "string"}, "eventTime": {"type": "string"}, "createTime": {"type": "string"}}}}, "nextPageToken": {"type": "string"}}}}}, {"id": "get-finding", "name": "Get Security Finding", "description": "Get a specific security finding by name", "path": "{name}", "method": "GET", "parameters": {"path": {"name": {"type": "string", "description": "Finding name", "required": true}}}, "response": {"successCode": 200, "schema": {"type": "object", "properties": {"name": {"type": "string"}, "parent": {"type": "string"}, "resourceName": {"type": "string"}, "state": {"type": "string"}, "category": {"type": "string"}, "severity": {"type": "string"}, "eventTime": {"type": "string"}, "createTime": {"type": "string"}}}}}, {"id": "list-assets", "name": "List Assets", "description": "List assets from Security Command Center", "path": "organizations/{organizationId}/assets", "method": "GET", "parameters": {"path": {"organizationId": {"type": "string", "description": "Organization ID", "required": true}}, "query": {"filter": {"type": "string", "description": "Filter expression"}, "orderBy": {"type": "string", "description": "Order by expression"}, "pageSize": {"type": "integer", "description": "Page size", "default": 100}, "pageToken": {"type": "string", "description": "Page token"}}}, "pagination": {"type": "token", "parameters": {"nextPageToken": "nextPageToken", "pageToken": "pageToken"}}, "response": {"successCode": 200, "dataPath": "assets", "schema": {"type": "object", "properties": {"assets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "securityCenterProperties": {"type": "object"}, "resourceProperties": {"type": "object"}, "createTime": {"type": "string"}, "updateTime": {"type": "string"}}}}, "nextPageToken": {"type": "string"}}}}}], "mappings": [{"sourceEndpoint": "list-findings", "targetSystem": "NovaGRC", "targetEntity": "SecurityFindings", "transformations": [{"source": "$.name", "target": "id", "transform": "identity"}, {"source": "$.category", "target": "category", "transform": "identity"}, {"source": "$.severity", "target": "severity", "transform": "mapSeverity", "parameters": {"mapping": {"CRITICAL": "critical", "HIGH": "high", "MEDIUM": "medium", "LOW": "low"}}}, {"source": "$.resourceName", "target": "resourceId", "transform": "identity"}, {"source": "$.eventTime", "target": "discoveredAt", "transform": "identity"}, {"source": "$.state", "target": "status", "transform": "mapStatus", "parameters": {"mapping": {"ACTIVE": "open", "INACTIVE": "closed"}}}]}], "events": {"polling": [{"endpoint": "list-findings", "interval": "15m", "condition": "hasNewFindings"}]}}