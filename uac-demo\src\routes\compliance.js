/**
 * Compliance Routes
 * 
 * This module defines the compliance routes for the UAC demo.
 */

const express = require('express');
const router = express.Router();
const { getLogger } = require('../core/logger');

const logger = getLogger('compliance-routes');

// Middleware to check authentication
const checkAuth = (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    const decoded = req.uac.authManager.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    logger.error('Authentication failed', error);
    return res.status(401).json({ error: 'Invalid token' });
  }
};

// Register a compliance rule
router.post('/rules', checkAuth, (req, res) => {
  const { ruleId, framework, description, check, logData } = req.body;
  
  if (!ruleId || !framework || !check) {
    return res.status(400).json({ error: 'Rule ID, framework, and check function are required' });
  }
  
  try {
    // Convert check function from string to function
    const checkFunction = new Function('data', check);
    
    const success = req.uac.complianceEngine.registerComplianceRule(ruleId, {
      framework,
      description,
      check: checkFunction,
      logData: logData || false
    });
    
    if (success) {
      res.status(201).json({ message: `Compliance rule ${ruleId} registered successfully` });
    } else {
      res.status(400).json({ error: `Failed to register compliance rule ${ruleId}` });
    }
  } catch (error) {
    logger.error('Compliance rule registration failed', error);
    res.status(400).json({ error: error.message });
  }
});

// Get a compliance rule
router.get('/rules/:ruleId', checkAuth, (req, res) => {
  const { ruleId } = req.params;
  
  const rule = req.uac.complianceEngine.getComplianceRule(ruleId);
  
  if (!rule) {
    return res.status(404).json({ error: `Compliance rule ${ruleId} not found` });
  }
  
  // Convert function to string for JSON response
  const ruleResponse = {
    ...rule,
    check: rule.check.toString()
  };
  
  res.json(ruleResponse);
});

// List all compliance rules
router.get('/rules', checkAuth, (req, res) => {
  const { framework } = req.query;
  
  const rules = req.uac.complianceEngine.listComplianceRules(framework);
  
  // Convert functions to strings for JSON response
  const rulesResponse = rules.map(rule => ({
    ...rule,
    check: rule.check.toString()
  }));
  
  res.json(rulesResponse);
});

// Check compliance with a specific rule
router.post('/check/:ruleId', checkAuth, (req, res) => {
  const { ruleId } = req.params;
  const { data } = req.body;
  
  if (!data) {
    return res.status(400).json({ error: 'Data is required' });
  }
  
  try {
    const result = req.uac.complianceEngine.checkCompliance(ruleId, data);
    res.json(result);
  } catch (error) {
    logger.error(`Compliance check for rule ${ruleId} failed`, error);
    res.status(400).json({ error: error.message });
  }
});

// Check compliance with all rules for a framework
router.post('/check-framework/:framework', checkAuth, (req, res) => {
  const { framework } = req.params;
  const { data } = req.body;
  
  if (!data) {
    return res.status(400).json({ error: 'Data is required' });
  }
  
  try {
    const result = req.uac.complianceEngine.checkFrameworkCompliance(framework, data);
    res.json(result);
  } catch (error) {
    logger.error(`Framework compliance check for ${framework} failed`, error);
    res.status(400).json({ error: error.message });
  }
});

// Classify data
router.post('/classify', checkAuth, (req, res) => {
  const { data } = req.body;
  
  if (!data) {
    return res.status(400).json({ error: 'Data is required' });
  }
  
  try {
    const classification = req.uac.complianceEngine.classifyData(data);
    res.json({ classification });
  } catch (error) {
    logger.error('Data classification failed', error);
    res.status(400).json({ error: error.message });
  }
});

// Get compliance audit logs
router.get('/audit-logs', checkAuth, (req, res) => {
  const { ruleId, startDate, endDate } = req.query;
  
  const logs = req.uac.complianceEngine.getComplianceAuditLogs({
    ruleId,
    startDate,
    endDate
  });
  
  res.json(logs);
});

// Get supported compliance frameworks
router.get('/frameworks', (req, res) => {
  res.json(req.uac.complianceEngine.COMPLIANCE_FRAMEWORKS);
});

// Get data classification levels
router.get('/classifications', (req, res) => {
  res.json(req.uac.complianceEngine.DATA_CLASSIFICATION);
});

module.exports = router;
