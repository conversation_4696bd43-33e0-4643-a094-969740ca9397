const { v4: uuidv4 } = require('uuid');
const models = require('./models');

/**
 * Get a list of controls
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControls = (req, res) => {
  try {
    const { page = 1, limit = 10, type, category, status, framework, riskLevel, sortBy = 'title', sortOrder = 'asc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter controls based on query parameters
    let filteredControls = [...models.controls];
    
    if (type) {
      filteredControls = filteredControls.filter(control => control.type === type);
    }
    
    if (category) {
      filteredControls = filteredControls.filter(control => control.category === category);
    }
    
    if (status) {
      filteredControls = filteredControls.filter(control => control.status === status);
    }
    
    if (framework) {
      filteredControls = filteredControls.filter(control => control.framework === framework);
    }
    
    if (riskLevel) {
      filteredControls = filteredControls.filter(control => control.riskLevel === riskLevel);
    }
    
    // Sort controls
    filteredControls.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedControls = filteredControls.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalControls = filteredControls.length;
    const totalPages = Math.ceil(totalControls / limitNum);
    
    res.json({
      data: paginatedControls,
      pagination: {
        total: totalControls,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getControls:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific control by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlById = (req, res) => {
  try {
    const { id } = req.params;
    const control = models.controls.find(c => c.id === id);
    
    if (!control) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Control with ID ${id} not found`
      });
    }
    
    res.json({ data: control });
  } catch (error) {
    console.error('Error in getControlById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Create a new control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createControl = (req, res) => {
  try {
    const { title, description, type, category, status, owner, framework, riskLevel, testFrequency, testProcedure } = req.body;
    
    // Create a new control with a unique ID
    const newControl = {
      id: `ctrl-${uuidv4().substring(0, 8)}`,
      title,
      description,
      type,
      category,
      status,
      owner,
      framework,
      riskLevel,
      testFrequency,
      testProcedure,
      lastTestedDate: null,
      nextTestDate: null,
      testResults: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the new control to the collection
    models.controls.push(newControl);
    
    res.status(201).json({
      data: newControl,
      message: 'Control created successfully'
    });
  } catch (error) {
    console.error('Error in createControl:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update an existing control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateControl = (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, type, category, status, owner, framework, riskLevel, testFrequency, testProcedure } = req.body;
    
    // Find the control to update
    const controlIndex = models.controls.findIndex(c => c.id === id);
    
    if (controlIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Control with ID ${id} not found`
      });
    }
    
    // Update the control
    const updatedControl = {
      ...models.controls[controlIndex],
      title: title || models.controls[controlIndex].title,
      description: description || models.controls[controlIndex].description,
      type: type || models.controls[controlIndex].type,
      category: category || models.controls[controlIndex].category,
      status: status || models.controls[controlIndex].status,
      owner: owner || models.controls[controlIndex].owner,
      framework: framework || models.controls[controlIndex].framework,
      riskLevel: riskLevel || models.controls[controlIndex].riskLevel,
      testFrequency: testFrequency || models.controls[controlIndex].testFrequency,
      testProcedure: testProcedure || models.controls[controlIndex].testProcedure,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old control with the updated one
    models.controls[controlIndex] = updatedControl;
    
    res.json({
      data: updatedControl,
      message: 'Control updated successfully'
    });
  } catch (error) {
    console.error('Error in updateControl:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Delete a control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteControl = (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the control to delete
    const controlIndex = models.controls.findIndex(c => c.id === id);
    
    if (controlIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Control with ID ${id} not found`
      });
    }
    
    // Remove the control from the collection
    models.controls.splice(controlIndex, 1);
    
    // Also remove any test results for this control
    const testResultsToRemove = models.controlTestResults.filter(tr => tr.controlId === id);
    testResultsToRemove.forEach(tr => {
      const index = models.controlTestResults.findIndex(r => r.id === tr.id);
      if (index !== -1) {
        models.controlTestResults.splice(index, 1);
      }
    });
    
    res.json({
      message: 'Control deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteControl:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get test results for a specific control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlTestResults = (req, res) => {
  try {
    const { id } = req.params;
    const control = models.controls.find(c => c.id === id);
    
    if (!control) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Control with ID ${id} not found`
      });
    }
    
    res.json({ data: control.testResults });
  } catch (error) {
    console.error('Error in getControlTestResults:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Add a test result to a control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addTestResult = (req, res) => {
  try {
    const { id } = req.params;
    const { testDate, tester, result, evidence, notes, remediation, remediationDueDate, remediationStatus } = req.body;
    
    // Find the control
    const controlIndex = models.controls.findIndex(c => c.id === id);
    
    if (controlIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Control with ID ${id} not found`
      });
    }
    
    // Create a new test result
    const newTestResult = {
      id: `test-${uuidv4().substring(0, 8)}`,
      controlId: id,
      testDate,
      tester,
      result,
      evidence,
      notes,
      remediation: remediation || '',
      remediationDueDate: remediationDueDate || '',
      remediationStatus: remediationStatus || 'not-required',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Add the test result to the control
    models.controls[controlIndex].testResults.push(newTestResult);
    
    // Update the control's lastTestedDate and nextTestDate
    models.controls[controlIndex].lastTestedDate = testDate;
    
    // Calculate the next test date based on the test frequency
    const lastTestDate = new Date(testDate);
    let nextTestDate = new Date(lastTestDate);
    
    switch (models.controls[controlIndex].testFrequency) {
      case 'daily':
        nextTestDate.setDate(lastTestDate.getDate() + 1);
        break;
      case 'weekly':
        nextTestDate.setDate(lastTestDate.getDate() + 7);
        break;
      case 'monthly':
        nextTestDate.setMonth(lastTestDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextTestDate.setMonth(lastTestDate.getMonth() + 3);
        break;
      case 'semi-annually':
        nextTestDate.setMonth(lastTestDate.getMonth() + 6);
        break;
      case 'annually':
        nextTestDate.setFullYear(lastTestDate.getFullYear() + 1);
        break;
      default:
        // For 'as-needed', don't set a next test date
        nextTestDate = null;
    }
    
    models.controls[controlIndex].nextTestDate = nextTestDate ? nextTestDate.toISOString().split('T')[0] : null;
    models.controls[controlIndex].updatedAt = new Date().toISOString();
    
    // Also add to the separate test results collection
    models.controlTestResults.push(newTestResult);
    
    res.status(201).json({
      data: newTestResult,
      message: 'Test result added successfully'
    });
  } catch (error) {
    console.error('Error in addTestResult:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a list of all test results
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTestResults = (req, res) => {
  try {
    const { page = 1, limit = 10, controlId, result, sortBy = 'testDate', sortOrder = 'desc' } = req.query;
    
    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    
    // Filter test results based on query parameters
    let filteredResults = [...models.controlTestResults];
    
    if (controlId) {
      filteredResults = filteredResults.filter(tr => tr.controlId === controlId);
    }
    
    if (result) {
      filteredResults = filteredResults.filter(tr => tr.result === result);
    }
    
    // Sort test results
    filteredResults.sort((a, b) => {
      if (sortOrder.toLowerCase() === 'desc') {
        return a[sortBy] > b[sortBy] ? -1 : 1;
      }
      return a[sortBy] > b[sortBy] ? 1 : -1;
    });
    
    // Paginate results
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = pageNum * limitNum;
    const paginatedResults = filteredResults.slice(startIndex, endIndex);
    
    // Calculate pagination info
    const totalResults = filteredResults.length;
    const totalPages = Math.ceil(totalResults / limitNum);
    
    res.json({
      data: paginatedResults,
      pagination: {
        total: totalResults,
        page: pageNum,
        limit: limitNum,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in getTestResults:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get a specific test result by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getTestResultById = (req, res) => {
  try {
    const { id } = req.params;
    const testResult = models.controlTestResults.find(tr => tr.id === id);
    
    if (!testResult) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Test result with ID ${id} not found`
      });
    }
    
    res.json({ data: testResult });
  } catch (error) {
    console.error('Error in getTestResultById:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Update a test result
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateTestResult = (req, res) => {
  try {
    const { id } = req.params;
    const { testDate, tester, result, evidence, notes, remediation, remediationDueDate, remediationStatus } = req.body;
    
    // Find the test result to update
    const testResultIndex = models.controlTestResults.findIndex(tr => tr.id === id);
    
    if (testResultIndex === -1) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Test result with ID ${id} not found`
      });
    }
    
    // Update the test result
    const updatedTestResult = {
      ...models.controlTestResults[testResultIndex],
      testDate: testDate || models.controlTestResults[testResultIndex].testDate,
      tester: tester || models.controlTestResults[testResultIndex].tester,
      result: result || models.controlTestResults[testResultIndex].result,
      evidence: evidence || models.controlTestResults[testResultIndex].evidence,
      notes: notes || models.controlTestResults[testResultIndex].notes,
      remediation: remediation !== undefined ? remediation : models.controlTestResults[testResultIndex].remediation,
      remediationDueDate: remediationDueDate !== undefined ? remediationDueDate : models.controlTestResults[testResultIndex].remediationDueDate,
      remediationStatus: remediationStatus || models.controlTestResults[testResultIndex].remediationStatus,
      updatedAt: new Date().toISOString()
    };
    
    // Replace the old test result with the updated one
    models.controlTestResults[testResultIndex] = updatedTestResult;
    
    // Also update the test result in the control
    const controlId = updatedTestResult.controlId;
    const controlIndex = models.controls.findIndex(c => c.id === controlId);
    
    if (controlIndex !== -1) {
      const testResultInControlIndex = models.controls[controlIndex].testResults.findIndex(tr => tr.id === id);
      
      if (testResultInControlIndex !== -1) {
        models.controls[controlIndex].testResults[testResultInControlIndex] = updatedTestResult;
        models.controls[controlIndex].updatedAt = new Date().toISOString();
      }
    }
    
    res.json({
      data: updatedTestResult,
      message: 'Test result updated successfully'
    });
  } catch (error) {
    console.error('Error in updateTestResult:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get control types
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlTypes = (req, res) => {
  try {
    res.json({ data: models.controlTypes });
  } catch (error) {
    console.error('Error in getControlTypes:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

/**
 * Get control categories
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getControlCategories = (req, res) => {
  try {
    res.json({ data: models.controlCategories });
  } catch (error) {
    console.error('Error in getControlCategories:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: error.message
    });
  }
};

module.exports = {
  getControls,
  getControlById,
  createControl,
  updateControl,
  deleteControl,
  getControlTestResults,
  addTestResult,
  getTestResults,
  getTestResultById,
  updateTestResult,
  getControlTypes,
  getControlCategories
};
