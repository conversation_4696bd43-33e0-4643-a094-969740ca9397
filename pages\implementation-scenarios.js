import React from 'react';
import PageWithSidebar from '../components/PageWithSidebar';
import Link from 'next/link';

export default function ImplementationScenarios() {
  const sidebarItems = [
    { label: 'Overview', href: '#overview' },
    { label: 'Implementation Calculator', href: '#calculator' },
    { label: 'Partner Revenue Calculator', href: '#partner-calculator' },
    { label: 'Enterprise Scenario', href: '#enterprise' },
    { label: 'Mid-Market Scenario', href: '#mid-market' },
    { label: 'Healthcare Scenario', href: '#healthcare' },
    { label: 'Financial Services Scenario', href: '#financial' },
    { label: 'Back to Home', href: '/' }
  ];

  return (
    <PageWithSidebar title="Implementation Scenarios" sidebarItems={sidebarItems}>
      {/* Hero Section */}
      <div id="overview" className="bg-secondary rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-4">The 18% Implementation Model in Action</h1>
        <p className="text-xl mb-6">
          See how our revolutionary 18% value-based implementation fee creates significant savings
          for organizations across different industries and sizes.
        </p>
      </div>

      {/* How It Works */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">How the 18% Model Works</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <p className="mb-4">
            Traditional GRC implementations typically cost between $250,000 and $2 million, depending on the
            organization's size and complexity. These high costs create barriers to adoption and limit the
            ROI of compliance solutions.
          </p>
          <p className="mb-4">
            NovaFuse's 18% value-based implementation fee means we charge just 18% of what traditional
            implementations would cost. This revolutionary approach:
          </p>
          <ul className="list-disc list-inside mb-4 space-y-2">
            <li>Makes enterprise-grade compliance accessible to more organizations</li>
            <li>Accelerates time-to-value by removing financial barriers</li>
            <li>Enables partners to offer competitive pricing while maintaining healthy margins</li>
            <li>Creates a win-win-win scenario for NovaFuse, partners, and end customers</li>
          </ul>
          <div className="bg-blue-900 p-4 rounded-lg border border-blue-700 mt-6">
            <h3 className="text-lg font-bold mb-2">The Formula</h3>
            <p className="italic">
              NovaFuse Implementation Fee = Traditional Implementation Cost × 18%
            </p>
          </div>
        </div>
      </div>

      {/* Enterprise Scenario */}
      <div id="enterprise" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Enterprise Implementation Scenario</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-bold mb-4">Fortune 500 Financial Services Company</h3>
              <p className="mb-4">
                A large financial services company needed to implement a comprehensive GRC solution to
                manage compliance across multiple regulatory frameworks (SOX, GDPR, PCI-DSS, GLBA).
              </p>
              <div className="space-y-2 mb-4">
                <p><strong>Company Size:</strong> 50,000+ employees</p>
                <p><strong>Annual Revenue:</strong> $12 billion</p>
                <p><strong>Compliance Complexity:</strong> Very High (Multiple frameworks, global operations)</p>
              </div>
            </div>
            <div className="bg-blue-900 p-5 rounded-lg border border-blue-700">
              <h3 className="text-xl font-bold mb-4">Cost Comparison</h3>
              <div className="space-y-4">
                <div>
                  <p className="font-semibold">Traditional Implementation:</p>
                  <p className="text-2xl font-bold">$1,850,000</p>
                  <ul className="text-sm text-gray-300 list-disc list-inside">
                    <li>Software customization: $750,000</li>
                    <li>Integration services: $450,000</li>
                    <li>Data migration: $350,000</li>
                    <li>Training & change management: $300,000</li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">NovaFuse Implementation (18%):</p>
                  <p className="text-2xl font-bold text-green-400">$333,000</p>
                  <p className="text-green-400 font-bold">Total Savings: $1,517,000</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 p-4 bg-gray-800 rounded-lg">
            <h4 className="font-bold mb-2">Outcome</h4>
            <p>
              The company was able to redirect over $1.5 million in savings to other strategic initiatives
              while still implementing a comprehensive GRC solution. The implementation was completed in
              4 months instead of the projected 9-12 months with a traditional approach.
            </p>
          </div>
        </div>
      </div>

      {/* Mid-Market Scenario */}
      <div id="mid-market" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Mid-Market Implementation Scenario</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-bold mb-4">Growing Technology Company</h3>
              <p className="mb-4">
                A mid-sized SaaS company needed to implement a GRC solution to prepare for SOC 2
                certification and manage GDPR compliance as they expanded into European markets.
              </p>
              <div className="space-y-2 mb-4">
                <p><strong>Company Size:</strong> 250 employees</p>
                <p><strong>Annual Revenue:</strong> $45 million</p>
                <p><strong>Compliance Complexity:</strong> Medium (SOC 2, GDPR)</p>
              </div>
            </div>
            <div className="bg-blue-900 p-5 rounded-lg border border-blue-700">
              <h3 className="text-xl font-bold mb-4">Cost Comparison</h3>
              <div className="space-y-4">
                <div>
                  <p className="font-semibold">Traditional Implementation:</p>
                  <p className="text-2xl font-bold">$425,000</p>
                  <ul className="text-sm text-gray-300 list-disc list-inside">
                    <li>Software customization: $175,000</li>
                    <li>Integration services: $125,000</li>
                    <li>Data migration: $75,000</li>
                    <li>Training & change management: $50,000</li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">NovaFuse Implementation (18%):</p>
                  <p className="text-2xl font-bold text-green-400">$76,500</p>
                  <p className="text-green-400 font-bold">Total Savings: $348,500</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 p-4 bg-gray-800 rounded-lg">
            <h4 className="font-bold mb-2">Outcome</h4>
            <p>
              The company achieved SOC 2 compliance 3 months ahead of schedule and successfully entered
              the European market with full GDPR compliance. The savings allowed them to hire two additional
              security engineers to further strengthen their security posture.
            </p>
          </div>
        </div>
      </div>

      {/* Healthcare Scenario */}
      <div id="healthcare" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Healthcare Implementation Scenario</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-bold mb-4">Regional Healthcare Provider</h3>
              <p className="mb-4">
                A healthcare provider with multiple facilities needed to implement a comprehensive
                GRC solution to manage HIPAA compliance and prepare for HITRUST certification.
              </p>
              <div className="space-y-2 mb-4">
                <p><strong>Company Size:</strong> 3,500 employees</p>
                <p><strong>Annual Revenue:</strong> $280 million</p>
                <p><strong>Compliance Complexity:</strong> High (HIPAA, HITRUST)</p>
              </div>
            </div>
            <div className="bg-blue-900 p-5 rounded-lg border border-blue-700">
              <h3 className="text-xl font-bold mb-4">Cost Comparison</h3>
              <div className="space-y-4">
                <div>
                  <p className="font-semibold">Traditional Implementation:</p>
                  <p className="text-2xl font-bold">$650,000</p>
                  <ul className="text-sm text-gray-300 list-disc list-inside">
                    <li>Software customization: $275,000</li>
                    <li>Integration services: $175,000</li>
                    <li>Data migration: $125,000</li>
                    <li>Training & change management: $75,000</li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">NovaFuse Implementation (18%):</p>
                  <p className="text-2xl font-bold text-green-400">$117,000</p>
                  <p className="text-green-400 font-bold">Total Savings: $533,000</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 p-4 bg-gray-800 rounded-lg">
            <h4 className="font-bold mb-2">Outcome</h4>
            <p>
              The healthcare provider achieved HIPAA compliance across all facilities and successfully
              completed HITRUST certification. The implementation was completed in 3 months, and the
              savings were reinvested in patient care initiatives.
            </p>
          </div>
        </div>
      </div>

      {/* Financial Services Scenario */}
      <div id="financial" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Financial Services Implementation Scenario</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-bold mb-4">Community Bank with Regional Presence</h3>
              <p className="mb-4">
                A community bank with multiple branches needed to implement a GRC solution to manage
                compliance with banking regulations (GLBA, SOX) and strengthen their cybersecurity posture.
              </p>
              <div className="space-y-2 mb-4">
                <p><strong>Company Size:</strong> 800 employees</p>
                <p><strong>Annual Revenue:</strong> $120 million</p>
                <p><strong>Compliance Complexity:</strong> Medium-High (GLBA, SOX, FFIEC)</p>
              </div>
            </div>
            <div className="bg-blue-900 p-5 rounded-lg border border-blue-700">
              <h3 className="text-xl font-bold mb-4">Cost Comparison</h3>
              <div className="space-y-4">
                <div>
                  <p className="font-semibold">Traditional Implementation:</p>
                  <p className="text-2xl font-bold">$520,000</p>
                  <ul className="text-sm text-gray-300 list-disc list-inside">
                    <li>Software customization: $225,000</li>
                    <li>Integration services: $150,000</li>
                    <li>Data migration: $95,000</li>
                    <li>Training & change management: $50,000</li>
                  </ul>
                </div>
                <div>
                  <p className="font-semibold">NovaFuse Implementation (18%):</p>
                  <p className="text-2xl font-bold text-green-400">$93,600</p>
                  <p className="text-green-400 font-bold">Total Savings: $426,400</p>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-6 p-4 bg-gray-800 rounded-lg">
            <h4 className="font-bold mb-2">Outcome</h4>
            <p>
              The bank achieved full compliance with banking regulations and significantly improved their
              cybersecurity posture. The implementation was completed in 2.5 months, and the savings
              allowed them to expand their digital banking initiatives.
            </p>
          </div>
        </div>
      </div>

      {/* Implementation Calculator */}
      <div id="calculator" className="mb-12 scroll-mt-20" data-testid="implementation-calculator">
        <h2 className="text-2xl font-bold mb-6">18/82 Model Implementation Savings Calculator</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <div className="mb-6 bg-blue-900 bg-opacity-30 p-4 rounded-lg border border-blue-700">
            <h3 className="text-xl font-semibold mb-3">The 18/82 Model Explained</h3>
            <p className="mb-4">
              NovaFuse's revolutionary 18/82 model means:
            </p>
            <ul className="list-disc list-inside space-y-2 mb-4">
              <li><span className="font-semibold text-blue-400">For Customers:</span> Pay only 18% of traditional implementation costs, saving 82%</li>
              <li><span className="font-semibold text-blue-400">For Partners:</span> Earn 82% of all revenue generated, 3.28x more than traditional partner programs</li>
            </ul>
            <p>
              This symmetrical approach creates a win-win-win scenario for customers, partners, and NovaFuse.
            </p>
          </div>

          <p className="mb-6">
            Use this calculator to estimate your potential savings with NovaFuse's 18% value-based
            implementation fee model.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-900 p-5 rounded-lg border border-blue-700">
              <h3 className="text-xl font-bold mb-4">Calculator</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="traditional-cost">
                    Traditional Implementation Cost ($)
                  </label>
                  <input
                    type="number"
                    id="traditional-cost"
                    className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
                    placeholder="Enter estimated traditional cost"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="nova-cost">
                    NovaFuse Implementation Cost (18%)
                  </label>
                  <input
                    type="text"
                    id="nova-cost"
                    className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
                    placeholder="Calculated automatically"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="savings">
                    Your Potential Savings
                  </label>
                  <input
                    type="text"
                    id="savings"
                    className="w-full p-2 bg-gray-800 border border-gray-700 rounded text-white"
                    placeholder="Calculated automatically"
                    readOnly
                  />
                </div>
                <button
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded font-bold hover:from-blue-700 hover:to-purple-700"
                  onClick={() => {
                    const traditionalCost = parseFloat(document.getElementById('traditional-cost').value);
                    if (!isNaN(traditionalCost)) {
                      const novaCost = traditionalCost * 0.18;
                      const savings = traditionalCost - novaCost;
                      const savingsPercentage = 82;
                      document.getElementById('nova-cost').value = '$' + novaCost.toLocaleString(undefined, {maximumFractionDigits: 2});
                      document.getElementById('savings').value = '$' + savings.toLocaleString(undefined, {maximumFractionDigits: 2}) + ' (' + savingsPercentage + '%)';

                      // Show the results section
                      const resultsSection = document.getElementById('calculator-results');
                      if (resultsSection) {
                        resultsSection.style.display = 'block';
                        document.getElementById('result-traditional').textContent = '$' + traditionalCost.toLocaleString(undefined, {maximumFractionDigits: 2});
                        document.getElementById('result-nova').textContent = '$' + novaCost.toLocaleString(undefined, {maximumFractionDigits: 2});
                        document.getElementById('result-savings').textContent = '$' + savings.toLocaleString(undefined, {maximumFractionDigits: 2});
                        document.getElementById('result-percentage').textContent = savingsPercentage + '%';
                      }
                    }
                  }}
                >
                  Calculate Savings
                </button>
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Typical Implementation Costs</h3>
              <p className="mb-4">
                Traditional GRC implementation costs vary based on organization size and complexity:
              </p>
              <ul className="space-y-3">
                <li className="bg-gray-800 p-3 rounded">
                  <span className="font-bold">Small Business (50-250 employees):</span>
                  <br />$150,000 - $350,000
                </li>
                <li className="bg-gray-800 p-3 rounded">
                  <span className="font-bold">Mid-Market (250-1,000 employees):</span>
                  <br />$350,000 - $750,000
                </li>
                <li className="bg-gray-800 p-3 rounded">
                  <span className="font-bold">Enterprise (1,000-5,000 employees):</span>
                  <br />$750,000 - $1,500,000
                </li>
                <li className="bg-gray-800 p-3 rounded">
                  <span className="font-bold">Large Enterprise (5,000+ employees):</span>
                  <br />$1,500,000 - $3,000,000+
                </li>
              </ul>

              {/* Detailed Results Section */}
              <div id="calculator-results" className="mt-8 bg-blue-900 bg-opacity-40 p-5 rounded-lg border border-blue-700" style={{display: 'none'}} data-testid="calculator-results">
                <h3 className="text-xl font-bold mb-4 text-center">Your 18/82 Model Savings</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-800 p-3 rounded-lg">
                      <p className="text-sm font-medium">Traditional Implementation</p>
                      <p id="result-traditional" className="text-xl font-bold">$0</p>
                    </div>
                    <div className="bg-blue-800 p-3 rounded-lg">
                      <p className="text-sm font-medium">NovaFuse Implementation (18%)</p>
                      <p id="result-nova" className="text-xl font-bold">$0</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-green-800 p-3 rounded-lg">
                      <p className="text-sm font-medium">Your Total Savings</p>
                      <p id="result-savings" className="text-xl font-bold">$0</p>
                    </div>
                    <div className="bg-green-800 p-3 rounded-lg">
                      <p className="text-sm font-medium">Savings Percentage</p>
                      <p id="result-percentage" className="text-xl font-bold">82%</p>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg text-center">
                    <p className="font-bold mb-1">What could you do with these savings?</p>
                    <p className="text-sm">Reinvest in your business, accelerate digital transformation, or improve your bottom line.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Partner Revenue Calculator */}
      <div id="partner-calculator" className="mb-12 scroll-mt-20">
        <h2 className="text-2xl font-bold mb-6">Partner Revenue Calculator</h2>
        <div className="bg-secondary p-6 rounded-lg">
          <div className="mb-6 bg-blue-900 bg-opacity-30 p-4 rounded-lg border border-blue-700">
            <h3 className="text-xl font-semibold mb-3">Partner Revenue Comparison</h3>
            <p className="mb-4">
              See how much more you can earn with NovaFuse's 82% revenue share compared to traditional partner programs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-blue-900 p-5 rounded-lg border border-blue-700">
              <h3 className="text-xl font-bold mb-4">Revenue Calculator</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="annual-revenue">
                    Annual Revenue Generated ($)
                  </label>
                  <input
                    type="number"
                    id="annual-revenue"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2"
                    placeholder="e.g., 500000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="traditional-share">
                    Traditional Partner Share (25%)
                  </label>
                  <input
                    type="text"
                    id="traditional-share"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="nova-share">
                    NovaFuse Partner Share (82%)
                  </label>
                  <input
                    type="text"
                    id="nova-share"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2"
                    readOnly
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1" htmlFor="additional-earnings">
                    Additional Earnings with NovaFuse
                  </label>
                  <input
                    type="text"
                    id="additional-earnings"
                    className="w-full bg-gray-800 border border-gray-700 rounded p-2"
                    readOnly
                  />
                </div>
                <button
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded font-bold hover:from-blue-700 hover:to-purple-700"
                  onClick={() => {
                    const annualRevenue = parseFloat(document.getElementById('annual-revenue').value);
                    if (!isNaN(annualRevenue)) {
                      const traditionalShare = annualRevenue * 0.25;
                      const novaShare = annualRevenue * 0.82;
                      const additionalEarnings = novaShare - traditionalShare;
                      const percentageMore = ((novaShare / traditionalShare) - 1) * 100;

                      document.getElementById('traditional-share').value = '$' + traditionalShare.toLocaleString(undefined, {maximumFractionDigits: 2});
                      document.getElementById('nova-share').value = '$' + novaShare.toLocaleString(undefined, {maximumFractionDigits: 2});
                      document.getElementById('additional-earnings').value = '$' + additionalEarnings.toLocaleString(undefined, {maximumFractionDigits: 2}) + ' (' + percentageMore.toFixed(0) + '% more)';

                      // Show the results section
                      const resultsSection = document.getElementById('partner-results');
                      if (resultsSection) {
                        resultsSection.style.display = 'block';
                      }
                    }
                  }}
                >
                  Calculate Partner Revenue
                </button>
              </div>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Partner Revenue Comparison</h3>
              <p className="mb-4 text-gray-300">
                See how NovaFuse's 82% revenue share compares to traditional partner programs:
              </p>

              <div className="bg-blue-900 p-4 rounded-lg mb-4">
                <h4 className="font-bold mb-2">Traditional Partner Programs</h4>
                <ul className="space-y-2">
                  <li><span className="font-semibold">Reseller Programs:</span> 15-25% margins</li>
                  <li><span className="font-semibold">Referral Programs:</span> 10-20% of first year</li>
                  <li><span className="font-semibold">Implementation Partners:</span> 20-30% of services</li>
                </ul>
              </div>

              <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg">
                <h4 className="font-bold mb-2">NovaFuse Partner Empowerment</h4>
                <ul className="space-y-2">
                  <li><span className="font-semibold">Revenue Share:</span> 82% of all revenue</li>
                  <li><span className="font-semibold">Implementation:</span> 82% of implementation fees</li>
                  <li><span className="font-semibold">Recurring Revenue:</span> 82% of ongoing subscriptions</li>
                </ul>
              </div>

              {/* Partner Results Section */}
              <div id="partner-results" className="mt-8 bg-blue-900 bg-opacity-40 p-5 rounded-lg border border-blue-700" style={{display: 'none'}}>
                <h3 className="text-xl font-bold mb-4 text-center">What This Means For You</h3>
                <p className="mb-4 text-center">
                  With NovaFuse's 82% revenue share, you earn 3.28x more than traditional partner programs.
                </p>
                <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-4 rounded-lg text-center">
                  <p className="font-bold mb-1">Ready to maximize your revenue?</p>
                  <Link href="/partner-onboarding" className="mt-2 inline-block bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100">
                    Become a Partner
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-700">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Transform Your GRC Implementation?</h2>
          <p className="mb-6 max-w-3xl mx-auto">
            Contact us today to discuss how NovaFuse's 18% value-based implementation fee can help your
            organization achieve compliance goals while significantly reducing costs.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/contact" className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-700 hover:to-purple-700 inline-block shadow-md">
              Contact Us
            </Link>
            <Link href="/" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md">
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
