/**
 * Themes
 * 
 * This module provides theme definitions for the NovaVision Hub.
 */

/**
 * Default theme
 */
export const defaultTheme = {
  name: 'Default',
  colors: {
    // Primary colors
    primary: '#1976d2',
    primaryLight: '#4791db',
    primaryDark: '#115293',
    primaryContrast: '#ffffff',
    
    // Secondary colors
    secondary: '#dc004e',
    secondaryLight: '#e33371',
    secondaryDark: '#9a0036',
    secondaryContrast: '#ffffff',
    
    // Success colors
    success: '#4caf50',
    successLight: '#80e27e',
    successDark: '#087f23',
    successContrast: '#ffffff',
    
    // Warning colors
    warning: '#ff9800',
    warningLight: '#ffc947',
    warningDark: '#c66900',
    warningContrast: '#000000',
    
    // Error colors
    error: '#f44336',
    errorLight: '#e57373',
    errorDark: '#d32f2f',
    errorContrast: '#ffffff',
    
    // Info colors
    info: '#2196f3',
    infoLight: '#64b5f6',
    infoDark: '#0d47a1',
    infoContrast: '#ffffff',
    
    // Neutral colors
    background: '#ffffff',
    surface: '#f5f5f5',
    divider: '#e0e0e0',
    
    // Text colors
    textPrimary: '#212121',
    textSecondary: '#757575',
    textDisabled: '#9e9e9e',
    textHint: '#9e9e9e',
    
    // Action colors
    actionActive: 'rgba(0, 0, 0, 0.54)',
    actionHover: 'rgba(0, 0, 0, 0.04)',
    actionSelected: 'rgba(0, 0, 0, 0.08)',
    actionDisabled: 'rgba(0, 0, 0, 0.26)',
    actionDisabledBackground: 'rgba(0, 0, 0, 0.12)',
    actionFocus: 'rgba(0, 0, 0, 0.12)'
  },
  
  // Typography
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    fontFamilyCode: '"Roboto Mono", monospace',
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
    
    // Font sizes
    fontSize: {
      xs: '0.75rem',    // 12px
      sm: '0.875rem',   // 14px
      md: '1rem',       // 16px
      lg: '1.125rem',   // 18px
      xl: '1.25rem',    // 20px
      '2xl': '1.5rem',  // 24px
      '3xl': '1.875rem', // 30px
      '4xl': '2.25rem',  // 36px
      '5xl': '3rem',     // 48px
      '6xl': '3.75rem',  // 60px
      '7xl': '4.5rem',   // 72px
      '8xl': '6rem',     // 96px
      '9xl': '8rem'      // 128px
    },
    
    // Line heights
    lineHeight: {
      none: 1,
      tight: 1.25,
      snug: 1.375,
      normal: 1.5,
      relaxed: 1.625,
      loose: 2
    }
  },
  
  // Spacing
  spacing: {
    unit: 8,
    xs: '0.25rem',    // 4px
    sm: '0.5rem',     // 8px
    md: '1rem',       // 16px
    lg: '1.5rem',     // 24px
    xl: '2rem',       // 32px
    '2xl': '2.5rem',  // 40px
    '3xl': '3rem',    // 48px
    '4xl': '4rem',    // 64px
    '5xl': '5rem'     // 80px
  },
  
  // Breakpoints
  breakpoints: {
    xs: 0,
    sm: 600,
    md: 960,
    lg: 1280,
    xl: 1920
  },
  
  // Shadows
  shadows: {
    none: 'none',
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)'
  },
  
  // Border radius
  radii: {
    none: '0',
    xs: '0.125rem',   // 2px
    sm: '0.25rem',    // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px'
  },
  
  // Z-index
  zIndices: {
    hide: -1,
    auto: 'auto',
    base: 0,
    docked: 10,
    dropdown: 1000,
    sticky: 1100,
    banner: 1200,
    overlay: 1300,
    modal: 1400,
    popover: 1500,
    skipLink: 1600,
    toast: 1700,
    tooltip: 1800
  },
  
  // Transitions
  transitions: {
    easing: {
      easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
      easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
      sharp: 'cubic-bezier(0.4, 0, 0.6, 1)'
    },
    duration: {
      shortest: 150,
      shorter: 200,
      short: 250,
      standard: 300,
      complex: 375,
      enteringScreen: 225,
      leavingScreen: 195
    }
  },
  
  // Dark mode
  modes: {
    dark: {
      colors: {
        // Neutral colors
        background: '#121212',
        surface: '#1e1e1e',
        divider: '#424242',
        
        // Text colors
        textPrimary: '#ffffff',
        textSecondary: '#b0b0b0',
        textDisabled: '#6c6c6c',
        textHint: '#6c6c6c',
        
        // Action colors
        actionActive: 'rgba(255, 255, 255, 0.7)',
        actionHover: 'rgba(255, 255, 255, 0.08)',
        actionSelected: 'rgba(255, 255, 255, 0.16)',
        actionDisabled: 'rgba(255, 255, 255, 0.3)',
        actionDisabledBackground: 'rgba(255, 255, 255, 0.12)',
        actionFocus: 'rgba(255, 255, 255, 0.12)'
      }
    }
  }
};

/**
 * Nova theme
 */
export const novaTheme = {
  name: 'Nova',
  colors: {
    // Primary colors
    primary: '#0056b3',
    primaryLight: '#3378c5',
    primaryDark: '#003b7a',
    primaryContrast: '#ffffff',
    
    // Secondary colors
    secondary: '#6c63ff',
    secondaryLight: '#9f97ff',
    secondaryDark: '#4b44b5',
    secondaryContrast: '#ffffff',
    
    // Success colors
    success: '#28a745',
    successLight: '#48c664',
    successDark: '#1e7e34',
    successContrast: '#ffffff',
    
    // Warning colors
    warning: '#ffc107',
    warningLight: '#ffcd38',
    warningDark: '#d39e00',
    warningContrast: '#000000',
    
    // Error colors
    error: '#dc3545',
    errorLight: '#e45c6a',
    errorDark: '#bd2130',
    errorContrast: '#ffffff',
    
    // Info colors
    info: '#17a2b8',
    infoLight: '#3ab7cc',
    infoDark: '#117a8b',
    infoContrast: '#ffffff',
    
    // Neutral colors
    background: '#f8f9fa',
    surface: '#ffffff',
    divider: '#dee2e6',
    
    // Text colors
    textPrimary: '#343a40',
    textSecondary: '#6c757d',
    textDisabled: '#adb5bd',
    textHint: '#adb5bd'
  },
  
  // Typography
  typography: {
    fontFamily: '"Poppins", "Helvetica", "Arial", sans-serif',
    fontFamilyCode: '"Fira Code", monospace'
  },
  
  // Border radius
  radii: {
    none: '0',
    xs: '0.125rem',   // 2px
    sm: '0.25rem',    // 4px
    md: '0.5rem',     // 8px
    lg: '0.75rem',    // 12px
    xl: '1rem',       // 16px
    '2xl': '1.5rem',  // 24px
    '3xl': '2rem',    // 32px
    full: '9999px'
  },
  
  // Dark mode
  modes: {
    dark: {
      colors: {
        // Neutral colors
        background: '#1a1a2e',
        surface: '#16213e',
        divider: '#424242',
        
        // Text colors
        textPrimary: '#ffffff',
        textSecondary: '#b0b0b0',
        textDisabled: '#6c6c6c',
        textHint: '#6c6c6c'
      }
    }
  }
};

/**
 * High contrast theme
 */
export const highContrastTheme = {
  name: 'High Contrast',
  colors: {
    // Primary colors
    primary: '#0000ff',
    primaryLight: '#3333ff',
    primaryDark: '#0000cc',
    primaryContrast: '#ffffff',
    
    // Secondary colors
    secondary: '#9900cc',
    secondaryLight: '#b84ddb',
    secondaryDark: '#660099',
    secondaryContrast: '#ffffff',
    
    // Success colors
    success: '#008000',
    successLight: '#33a333',
    successDark: '#006600',
    successContrast: '#ffffff',
    
    // Warning colors
    warning: '#ff8000',
    warningLight: '#ff9933',
    warningDark: '#cc6600',
    warningContrast: '#000000',
    
    // Error colors
    error: '#ff0000',
    errorLight: '#ff3333',
    errorDark: '#cc0000',
    errorContrast: '#ffffff',
    
    // Info colors
    info: '#0080ff',
    infoLight: '#3399ff',
    infoDark: '#0066cc',
    infoContrast: '#ffffff',
    
    // Neutral colors
    background: '#ffffff',
    surface: '#f0f0f0',
    divider: '#000000',
    
    // Text colors
    textPrimary: '#000000',
    textSecondary: '#333333',
    textDisabled: '#666666',
    textHint: '#666666'
  },
  
  // Typography
  typography: {
    fontFamily: '"Arial", sans-serif',
    fontFamilyCode: '"Courier New", monospace',
    fontSize: {
      xs: '0.875rem',   // 14px
      sm: '1rem',       // 16px
      md: '1.125rem',   // 18px
      lg: '1.25rem',    // 20px
      xl: '1.5rem',     // 24px
      '2xl': '1.75rem', // 28px
      '3xl': '2rem',    // 32px
      '4xl': '2.5rem',  // 40px
      '5xl': '3rem',    // 48px
      '6xl': '3.75rem', // 60px
      '7xl': '4.5rem',  // 72px
      '8xl': '6rem',    // 96px
      '9xl': '8rem'     // 128px
    }
  },
  
  // Dark mode
  modes: {
    dark: {
      colors: {
        // Neutral colors
        background: '#000000',
        surface: '#1a1a1a',
        divider: '#ffffff',
        
        // Text colors
        textPrimary: '#ffffff',
        textSecondary: '#cccccc',
        textDisabled: '#999999',
        textHint: '#999999'
      }
    }
  }
};

/**
 * Get all themes
 * 
 * @returns {Object[]} All themes
 */
export const getAllThemes = () => [
  defaultTheme,
  novaTheme,
  highContrastTheme
];

/**
 * Get theme by name
 * 
 * @param {string} name - Theme name
 * @returns {Object} Theme
 */
export const getThemeByName = (name) => {
  const themes = getAllThemes();
  return themes.find(theme => theme.name === name) || defaultTheme;
};
