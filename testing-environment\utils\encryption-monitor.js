/**
 * Encryption Monitoring Module for NovaConnect Universal API Connector
 * 
 * This module provides monitoring capabilities for encryption operations.
 */

const fs = require('fs');
const path = require('path');
const winston = require('winston');
const { createHash } = require('crypto');

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'encryption-monitor' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ 
      filename: path.join(process.env.LOG_DIR || 'logs', 'encryption.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true
    })
  ]
});

// Metrics storage
const metrics = {
  operations: {
    encrypt: { count: 0, errors: 0, totalTime: 0 },
    decrypt: { count: 0, errors: 0, totalTime: 0 },
    keyDerivation: { count: 0, errors: 0, totalTime: 0 },
    keyRotation: { count: 0, errors: 0, lastRotation: null },
    hash: { count: 0, errors: 0, totalTime: 0 }
  },
  keyUsage: {}
};

// Alert thresholds
const alertThresholds = {
  errorRate: 0.05, // 5% error rate
  decryptionTime: 500, // 500ms
  keyRotationAge: 100 * 24 * 60 * 60 * 1000 // 100 days
};

/**
 * Record an encryption operation
 * 
 * @param {string} operation - The operation type (encrypt, decrypt, keyDerivation, keyRotation, hash)
 * @param {Object} details - Operation details
 * @param {string} details.keyId - The key ID used (if applicable)
 * @param {number} details.startTime - The operation start time
 * @param {number} details.endTime - The operation end time
 * @param {boolean} details.success - Whether the operation was successful
 * @param {string} details.error - Error message (if applicable)
 * @param {Object} details.metadata - Additional metadata
 */
function recordOperation(operation, details) {
  // Calculate operation time
  const operationTime = details.endTime - details.startTime;
  
  // Update operation metrics
  metrics.operations[operation].count++;
  
  if (!details.success) {
    metrics.operations[operation].errors++;
  } else {
    metrics.operations[operation].totalTime += operationTime;
  }
  
  // Update key usage metrics
  if (details.keyId) {
    if (!metrics.keyUsage[details.keyId]) {
      metrics.keyUsage[details.keyId] = { count: 0, errors: 0 };
    }
    
    metrics.keyUsage[details.keyId].count++;
    
    if (!details.success) {
      metrics.keyUsage[details.keyId].errors++;
    }
  }
  
  // Update last key rotation time
  if (operation === 'keyRotation' && details.success) {
    metrics.operations.keyRotation.lastRotation = details.endTime;
  }
  
  // Log the operation
  const logLevel = details.success ? 'info' : 'error';
  
  logger[logLevel](`Encryption operation: ${operation}`, {
    operation,
    durationMs: operationTime,
    success: details.success,
    keyId: details.keyId,
    error: details.error,
    metadata: details.metadata
  });
  
  // Check for alerts
  checkAlerts(operation, details, operationTime);
}

/**
 * Check for alert conditions
 * 
 * @param {string} operation - The operation type
 * @param {Object} details - Operation details
 * @param {number} operationTime - Operation time in milliseconds
 */
function checkAlerts(operation, details, operationTime) {
  // Check for high error rate
  const errorRate = metrics.operations[operation].errors / metrics.operations[operation].count;
  
  if (errorRate > alertThresholds.errorRate) {
    logger.warn(`High error rate for ${operation} operations: ${(errorRate * 100).toFixed(2)}%`, {
      operation,
      errorRate,
      threshold: alertThresholds.errorRate,
      alert: 'HIGH_ERROR_RATE'
    });
  }
  
  // Check for slow decryption
  if (operation === 'decrypt' && operationTime > alertThresholds.decryptionTime) {
    logger.warn(`Slow decryption operation: ${operationTime}ms`, {
      operation,
      durationMs: operationTime,
      threshold: alertThresholds.decryptionTime,
      alert: 'SLOW_DECRYPTION'
    });
  }
  
  // Check for key rotation age
  if (metrics.operations.keyRotation.lastRotation) {
    const keyAge = Date.now() - metrics.operations.keyRotation.lastRotation;
    
    if (keyAge > alertThresholds.keyRotationAge) {
      logger.warn(`Key rotation overdue: ${Math.floor(keyAge / (24 * 60 * 60 * 1000))} days`, {
        keyAge,
        threshold: alertThresholds.keyRotationAge,
        alert: 'KEY_ROTATION_OVERDUE'
      });
    }
  }
  
  // Check for key-specific errors
  if (details.keyId && metrics.keyUsage[details.keyId]) {
    const keyErrorRate = metrics.keyUsage[details.keyId].errors / metrics.keyUsage[details.keyId].count;
    
    if (keyErrorRate > alertThresholds.errorRate) {
      logger.warn(`High error rate for key ${details.keyId}: ${(keyErrorRate * 100).toFixed(2)}%`, {
        keyId: details.keyId,
        errorRate: keyErrorRate,
        threshold: alertThresholds.errorRate,
        alert: 'KEY_HIGH_ERROR_RATE'
      });
    }
  }
}

/**
 * Get encryption metrics
 * 
 * @returns {Object} - Encryption metrics
 */
function getMetrics() {
  const result = { ...metrics };
  
  // Calculate average operation times
  Object.keys(result.operations).forEach(operation => {
    const { count, errors, totalTime } = result.operations[operation];
    const successCount = count - errors;
    
    if (successCount > 0) {
      result.operations[operation].averageTime = totalTime / successCount;
    }
  });
  
  return result;
}

/**
 * Reset encryption metrics
 */
function resetMetrics() {
  Object.keys(metrics.operations).forEach(operation => {
    metrics.operations[operation].count = 0;
    metrics.operations[operation].errors = 0;
    metrics.operations[operation].totalTime = 0;
  });
  
  metrics.keyUsage = {};
  
  logger.info('Encryption metrics reset');
}

/**
 * Monitor an encryption function
 * 
 * @param {Function} fn - The function to monitor
 * @param {string} operation - The operation type
 * @returns {Function} - The monitored function
 */
function monitorFunction(fn, operation) {
  return function(...args) {
    const startTime = Date.now();
    let success = true;
    let error = null;
    let result = null;
    
    try {
      result = fn.apply(this, args);
      return result;
    } catch (err) {
      success = false;
      error = err.message;
      throw err;
    } finally {
      const endTime = Date.now();
      
      // Extract key ID if available
      let keyId = null;
      if (args[1] && typeof args[1] === 'object' && args[1].keyId) {
        keyId = args[1].keyId;
      } else if (args[1] && typeof args[1] === 'string') {
        // Create a hash of the key for tracking without exposing it
        keyId = `key-${createHash('sha256').update(args[1]).digest('hex').substring(0, 8)}`;
      }
      
      // Record the operation
      recordOperation(operation, {
        keyId,
        startTime,
        endTime,
        success,
        error,
        metadata: {
          hasResult: result !== null,
          argTypes: args.map(arg => typeof arg)
        }
      });
    }
  };
}

/**
 * Create a monitored version of the encryption module
 * 
 * @param {Object} encryptionModule - The encryption module to monitor
 * @returns {Object} - The monitored encryption module
 */
function createMonitoredEncryption(encryptionModule) {
  return {
    encrypt: monitorFunction(encryptionModule.encrypt, 'encrypt'),
    decrypt: monitorFunction(encryptionModule.decrypt, 'decrypt'),
    generateEncryptionKey: monitorFunction(encryptionModule.generateEncryptionKey, 'keyDerivation'),
    hashData: monitorFunction(encryptionModule.hashData, 'hash'),
    verifyHash: monitorFunction(encryptionModule.verifyHash, 'hash'),
    generateToken: encryptionModule.generateToken
  };
}

/**
 * Create a monitored version of the key management module
 * 
 * @param {Object} keyManagementModule - The key management module to monitor
 * @returns {Object} - The monitored key management module
 */
function createMonitoredKeyManagement(keyManagementModule) {
  return {
    initialize: keyManagementModule.initialize,
    getCurrentKey: keyManagementModule.getCurrentKey,
    getKeyById: keyManagementModule.getKeyById,
    forceKeyRotation: monitorFunction(keyManagementModule.forceKeyRotation, 'keyRotation')
  };
}

module.exports = {
  recordOperation,
  getMetrics,
  resetMetrics,
  createMonitoredEncryption,
  createMonitoredKeyManagement
};
