describe('Products Page', () => {
  beforeEach(() => {
    // Visit the products page
    cy.visit('/products');
  });

  it('should display the products page heading', () => {
    cy.get('h1').contains('NovaFuse Products').should('be.visible');
  });

  it('should display NovaConnect UAC section', () => {
    cy.contains('NovaConnect UAC').should('be.visible');
    cy.contains('Universal API Connector').should('be.visible');
  });

  it('should display NovaGRC Suite section', () => {
    cy.contains('NovaGRC Suite').should('be.visible');
  });

  it('should have navigation to product details', () => {
    cy.contains('NovaConnect UAC').click();
    cy.url().should('include', '/novaconnect-uac');
    
    cy.go('back');
    
    cy.contains('NovaGRC Suite').click();
    cy.url().should('include', '/novagrc-suite');
  });

  it('should display product features', () => {
    cy.get('[data-testid="product-features"]').should('be.visible');
  });

  it('should display pricing information', () => {
    cy.get('[data-testid="pricing-section"]').should('be.visible');
  });

  it('should have call-to-action buttons', () => {
    cy.contains('Get Started').should('be.visible');
    cy.contains('Learn More').should('be.visible');
  });
});
