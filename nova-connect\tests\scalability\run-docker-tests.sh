#!/bin/bash
# Run NovaConnect UAC Scalability Tests in Docker

# Set variables
RESULTS_DIR="./results"
REPORT_FILE="$RESULTS_DIR/scalability-report.md"

# Create results directory
mkdir -p $RESULTS_DIR

# Print banner
echo "=================================================="
echo "NovaConnect UAC Scalability Tests"
echo "=================================================="
echo

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo "Error: Docker is not running. Please start Docker and try again."
  exit 1
fi

# Check if Docker Compose is installed
if ! docker-compose --version > /dev/null 2>&1; then
  echo "Error: Docker Compose is not installed. Please install Docker Compose and try again."
  exit 1
fi

# Start the test environment
echo "Starting test environment..."
docker-compose up -d nova-connect mongo redis
echo "Waiting for services to start..."
sleep 10

# Run API load test
echo "Running API Load Test..."
docker-compose run --rm k6 run /tests/api-load-test.js \
  --out json=/results/api-load-test.json \
  --summary-export=/results/api-load-test-summary.json

# Run normalization load test
echo "Running Normalization Load Test..."
docker-compose run --rm k6 run /tests/normalization-load-test.js \
  --out json=/results/normalization-load-test.json \
  --summary-export=/results/normalization-load-test-summary.json

# Run connector load test
echo "Running Connector Load Test..."
docker-compose run --rm k6 run /tests/connector-load-test.js \
  --out json=/results/connector-load-test.json \
  --summary-export=/results/connector-load-test-summary.json

# Generate report
echo "Generating report..."
node -e "
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  resultsDir: '$RESULTS_DIR',
  reportFile: '$REPORT_FILE',
  tests: [
    {
      name: 'API Load Test',
      script: 'api-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC API endpoints'
    },
    {
      name: 'Normalization Load Test',
      script: 'normalization-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC data normalization'
    },
    {
      name: 'Connector Load Test',
      script: 'connector-load-test.js',
      description: 'Tests the performance and scalability of NovaConnect UAC connectors'
    }
  ]
};

// Generate report
function generateReport() {
  console.log('Generating report...');
  
  // Read test results
  const results = [];
  
  for (const test of config.tests) {
    const summaryFile = path.join(config.resultsDir, `${test.script.replace('.js', '')}-summary.json`);
    
    try {
      if (fs.existsSync(summaryFile)) {
        const summary = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
        results.push({
          test,
          summary
        });
      }
    } catch (error) {
      console.error(`Error reading summary file for ${test.name}: ${error.message}`);
    }
  }
  
  // Create report content
  let report = \`# NovaConnect UAC Scalability Test Report\n\n\`;
  report += \`Generated on: ${new Date().toISOString()}\n\n\`;
  
  // Add system information
  report += \`## System Information\n\n\`;
  report += \`- Test Environment: Docker\n\`;
  report += \`- API Base URL: http://nova-connect:3001\n\n\`;
  
  // Add summary
  report += \`## Summary\n\n\`;
  report += \`| Test | HTTP Requests | Failed Requests | Avg Response Time | P95 Response Time | RPS |\n\`;
  report += \`| --- | --- | --- | --- | --- | --- |\n\`;
  
  for (const result of results) {
    const metrics = result.summary.metrics;
    const httpReqs = metrics['http_reqs'] ? metrics['http_reqs'].values.count : 0;
    const failedReqs = metrics['http_req_failed'] ? metrics['http_req_failed'].values.passes : 0;
    const avgResponseTime = metrics['http_req_duration'] ? metrics['http_req_duration'].values.avg.toFixed(2) : 0;
    const p95ResponseTime = metrics['http_req_duration'] ? metrics['http_req_duration'].values['p(95)'].toFixed(2) : 0;
    const rps = httpReqs / (result.summary.testRunDurationMs / 1000);
    
    report += \`| ${result.test.name} | ${httpReqs} | ${failedReqs} | ${avgResponseTime}ms | ${p95ResponseTime}ms | ${rps.toFixed(2)} |\n\`;
  }
  
  report += \`\n\`;
  
  // Add detailed results
  report += \`## Detailed Results\n\n\`;
  
  for (const result of results) {
    report += \`### ${result.test.name}\n\n\`;
    report += \`${result.test.description}\n\n\`;
    
    // Add metrics
    report += \`#### Metrics\n\n\`;
    report += \`| Metric | Avg | Min | Med | P90 | P95 | P99 | Max |\n\`;
    report += \`| --- | --- | --- | --- | --- | --- | --- | --- |\n\`;
    
    const metrics = result.summary.metrics;
    
    // HTTP request duration
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration) {
      report += \`| HTTP Request Duration | ${httpReqDuration.values.avg.toFixed(2)}ms | ${httpReqDuration.values.min.toFixed(2)}ms | ${httpReqDuration.values.med.toFixed(2)}ms | ${httpReqDuration.values['p(90)'].toFixed(2)}ms | ${httpReqDuration.values['p(95)'].toFixed(2)}ms | ${httpReqDuration.values['p(99)'].toFixed(2)}ms | ${httpReqDuration.values.max.toFixed(2)}ms |\n\`;
    }
    
    // Data normalization duration
    const dataNormalizationDuration = metrics['data_normalization_duration'];
    if (dataNormalizationDuration) {
      report += \`| Data Normalization Duration | ${dataNormalizationDuration.values.avg.toFixed(2)}ms | ${dataNormalizationDuration.values.min.toFixed(2)}ms | ${dataNormalizationDuration.values.med.toFixed(2)}ms | ${dataNormalizationDuration.values['p(90)'].toFixed(2)}ms | ${dataNormalizationDuration.values['p(95)'].toFixed(2)}ms | ${dataNormalizationDuration.values['p(99)'].toFixed(2)}ms | ${dataNormalizationDuration.values.max.toFixed(2)}ms |\n\`;
    }
    
    // Connector execution duration
    const connectorExecutionDuration = metrics['connector_execution_duration'];
    if (connectorExecutionDuration) {
      report += \`| Connector Execution Duration | ${connectorExecutionDuration.values.avg.toFixed(2)}ms | ${connectorExecutionDuration.values.min.toFixed(2)}ms | ${connectorExecutionDuration.values.med.toFixed(2)}ms | ${connectorExecutionDuration.values['p(90)'].toFixed(2)}ms | ${connectorExecutionDuration.values['p(95)'].toFixed(2)}ms | ${connectorExecutionDuration.values['p(99)'].toFixed(2)}ms | ${connectorExecutionDuration.values.max.toFixed(2)}ms |\n\`;
    }
    
    // Error rate
    const errorRate = metrics['error_rate'];
    if (errorRate) {
      report += \`| Error Rate | ${(errorRate.values.rate * 100).toFixed(2)}% | - | - | - | - | - | - |\n\`;
    }
    
    report += \`\n\`;
    
    // Add checks
    report += \`#### Checks\n\n\`;
    report += \`| Check | Pass Rate |\n\`;
    report += \`| --- | --- |\n\`;
    
    for (const [name, check] of Object.entries(result.summary.root_group.checks)) {
      const passRate = (check.passes / (check.passes + check.fails) * 100).toFixed(2);
      report += \`| ${name} | ${passRate}% |\n\`;
    }
    
    report += \`\n\`;
  }
  
  // Add recommendations
  report += \`## Recommendations\n\n\`;
  
  // Check for performance issues
  let hasPerformanceIssues = false;
  let hasErrorIssues = false;
  
  for (const result of results) {
    const metrics = result.summary.metrics;
    
    // Check HTTP request duration
    const httpReqDuration = metrics['http_req_duration'];
    if (httpReqDuration && httpReqDuration.values['p(95)'] > 500) {
      hasPerformanceIssues = true;
    }
    
    // Check error rate
    const errorRate = metrics['error_rate'];
    if (errorRate && errorRate.values.rate > 0.01) {
      hasErrorIssues = true;
    }
  }
  
  if (hasPerformanceIssues) {
    report += \`### Performance Issues\n\n\`;
    report += \`Some API endpoints have high response times (P95 > 500ms). Consider the following optimizations:\n\n\`;
    report += \`- Implement caching for frequently accessed data\n\`;
    report += \`- Optimize database queries\n\`;
    report += \`- Use connection pooling for external services\n\`;
    report += \`- Implement request batching for high-volume operations\n\n\`;
  }
  
  if (hasErrorIssues) {
    report += \`### Error Issues\n\n\`;
    report += \`The error rate is higher than expected (> 1%). Consider the following improvements:\n\n\`;
    report += \`- Enhance error handling and recovery mechanisms\n\`;
    report += \`- Implement circuit breakers for external services\n\`;
    report += \`- Add retry logic for transient failures\n\`;
    report += \`- Improve input validation\n\n\`;
  }
  
  if (!hasPerformanceIssues && !hasErrorIssues) {
    report += \`No significant issues were found during the scalability tests. The NovaConnect UAC is performing well under the tested load.\n\n\`;
    report += \`To further enhance performance and scalability, consider the following proactive measures:\n\n\`;
    report += \`- Implement a CDN for static assets\n\`;
    report += \`- Use a distributed cache for frequently accessed data\n\`;
    report += \`- Implement database read replicas\n\`;
    report += \`- Set up auto-scaling based on load metrics\n\`;
    report += \`- Implement a monitoring and alerting system\n\n\`;
  }
  
  // Write report to file
  fs.writeFileSync(config.reportFile, report);
  
  console.log(\`Report generated: ${config.reportFile}\`);
}

// Run the report generation
generateReport();
"

# Stop the test environment
echo "Stopping test environment..."
docker-compose down

echo "Scalability tests completed. Report generated: $REPORT_FILE"
