/**
 * Marketplace Controller
 * 
 * This controller handles Google Cloud Marketplace operations.
 */

const MarketplaceService = require('../services/MarketplaceService');
const logger = require('../../config/logger');
const { ValidationError, NotFoundError } = require('../utils/errors');

// Initialize services
const marketplaceService = new MarketplaceService();

/**
 * Get all marketplace plans
 */
const getMarketplacePlans = async (req, res, next) => {
  try {
    const plans = marketplaceService.getMarketplacePlans();
    res.json(plans);
  } catch (error) {
    logger.error('Error getting marketplace plans', { error: error.message });
    next(error);
  }
};

/**
 * Get marketplace plan by ID
 */
const getMarketplacePlan = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Plan ID is required'
      });
    }
    
    const plan = marketplaceService.getMarketplacePlan(id);
    
    if (!plan) {
      return res.status(404).json({
        error: 'Not Found',
        message: `Plan '${id}' not found`
      });
    }
    
    res.json(plan);
  } catch (error) {
    logger.error('Error getting marketplace plan', { error: error.message });
    next(error);
  }
};

/**
 * Provision a new tenant
 */
const provisionTenant = async (req, res, next) => {
  try {
    const tenant = await marketplaceService.provisionTenant(req.body);
    res.status(201).json(tenant);
  } catch (error) {
    logger.error('Error provisioning tenant', { error: error.message });
    next(error);
  }
};

/**
 * Update tenant plan
 */
const updateTenantPlan = async (req, res, next) => {
  try {
    const { tenantId } = req.params;
    const { planId } = req.body;
    
    if (!tenantId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Tenant ID is required'
      });
    }
    
    if (!planId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Plan ID is required'
      });
    }
    
    const result = await marketplaceService.updateTenantPlan(tenantId, planId);
    res.json(result);
  } catch (error) {
    logger.error('Error updating tenant plan', { error: error.message });
    next(error);
  }
};

/**
 * Deprovision a tenant
 */
const deprovisionTenant = async (req, res, next) => {
  try {
    const { tenantId } = req.params;
    
    if (!tenantId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Tenant ID is required'
      });
    }
    
    const result = await marketplaceService.deprovisionTenant(tenantId);
    res.json(result);
  } catch (error) {
    logger.error('Error deprovisioning tenant', { error: error.message });
    next(error);
  }
};

/**
 * Handle GCP Marketplace notification
 */
const handleMarketplaceNotification = async (req, res, next) => {
  try {
    const { type, data } = req.body;
    
    logger.info('Received GCP Marketplace notification', { type });
    
    switch (type) {
      case 'ENTITLEMENT_CREATION':
        // Handle entitlement creation
        await marketplaceService.provisionTenant({
          tenantId: data.customer.id,
          name: data.customer.name,
          planId: data.plan.id
        });
        break;
        
      case 'ENTITLEMENT_PLAN_CHANGE':
        // Handle plan change
        await marketplaceService.updateTenantPlan(data.customer.id, data.plan.id);
        break;
        
      case 'ENTITLEMENT_CANCELLATION':
        // Handle cancellation
        await marketplaceService.deprovisionTenant(data.customer.id);
        break;
        
      case 'ENTITLEMENT_SUSPENSION':
        // Handle suspension
        // This would typically suspend the tenant without deprovisioning
        break;
        
      case 'ENTITLEMENT_REACTIVATION':
        // Handle reactivation
        // This would typically reactivate a suspended tenant
        break;
        
      default:
        logger.warn('Unknown GCP Marketplace notification type', { type });
    }
    
    res.status(200).json({ status: 'success' });
  } catch (error) {
    logger.error('Error handling GCP Marketplace notification', { error: error.message });
    next(error);
  }
};

/**
 * Get tenant status
 */
const getTenantStatus = async (req, res, next) => {
  try {
    const { tenantId } = req.params;
    
    if (!tenantId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Tenant ID is required'
      });
    }
    
    // Get tenant entitlements
    const entitlement = await marketplaceService.billingService.getCustomerEntitlements(tenantId);
    
    // Get tenant usage
    const usage = await marketplaceService.billingService.getCustomerUsage(tenantId);
    
    res.json({
      tenantId,
      entitlement,
      usage
    });
  } catch (error) {
    logger.error('Error getting tenant status', { error: error.message });
    next(error);
  }
};

module.exports = {
  getMarketplacePlans,
  getMarketplacePlan,
  provisionTenant,
  updateTenantPlan,
  deprovisionTenant,
  handleMarketplaceNotification,
  getTenantStatus
};
