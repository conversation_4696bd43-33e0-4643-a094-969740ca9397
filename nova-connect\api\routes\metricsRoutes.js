/**
 * Metrics Routes
 * 
 * This file defines routes for metrics and health endpoints.
 */

const express = require('express');
const router = express.Router();
const MetricsController = require('../controllers/MetricsController');
const { metricsEndpoint } = require('../middleware/metricsMiddleware');

// Prometheus metrics endpoint
router.get('/metrics', metricsEndpoint);

// Health check endpoints
router.get('/health', MetricsController.getHealth);
router.get('/health/detailed', MetricsController.getDetailedHealth);
router.get('/health/ready', MetricsController.getReadiness);

module.exports = router;
