<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inside Number Template</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }
        
        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow: hidden; /* Prevents content from spilling out */
        }
        
        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: hidden; /* Prevents content from spilling out */
        }
        
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }
        
        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            overflow: hidden; /* Prevents content from spilling out */
        }
        
        /* Inside Component Number Style */
        .component-number-inside {
            font-weight: bold;
            font-size: 12px;
            color: #555555; /* Grey color for patent compliance */
            margin-bottom: 5px;
        }
        
        .component-label {
            font-weight: bold;
            margin-bottom: 6px;
            font-size: 14px;
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }
        
        .component-content {
            font-size: 12px;
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: hidden; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }
        
        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }
        
        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }
        
        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }
        
        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }
        
        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
        
        /* SVG Styles */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }
        
        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>FIG. X: Inside Number Template</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">MAIN DIAGRAM TITLE</div>
        </div>
        
        <!-- Example Component Boxes with Inside Numbers -->
        <div class="component-box" style="left: 100px; top: 100px; width: 150px; height: 100px;">
            <div class="component-number-inside">1</div>
            <div class="component-label">Component 1</div>
            <div class="component-content">
                This is a sample component with number inside the box.
            </div>
        </div>
        
        <div class="component-box" style="left: 300px; top: 100px; width: 150px; height: 100px;">
            <div class="component-number-inside">2</div>
            <div class="component-label">Component 2</div>
            <div class="component-content">
                Another sample component with number inside.
            </div>
        </div>
        
        <div class="component-box" style="left: 500px; top: 100px; width: 150px; height: 100px;">
            <div class="component-number-inside">3</div>
            <div class="component-label">Component 3</div>
            <div class="component-content">
                A third sample component with number inside.
            </div>
        </div>
        
        <!-- Example Arrows -->
        <div class="arrow-line" style="left: 250px; top: 140px; width: 50px; height: 2px;"></div>
        <div class="arrow-head" style="left: 300px; top: 136px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>
        
        <div class="arrow-line" style="left: 450px; top: 140px; width: 50px; height: 2px;"></div>
        <div class="arrow-head" style="left: 500px; top: 136px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>
        
        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #555555;"></div>
                <div>Component Type 1</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: white; border: 2px solid #555555;"></div>
                <div>Component Type 2</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: white; border: 1px dashed #555555;"></div>
                <div>Component Type 3</div>
            </div>
        </div>
        
        <!-- Inventor Label -->
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
