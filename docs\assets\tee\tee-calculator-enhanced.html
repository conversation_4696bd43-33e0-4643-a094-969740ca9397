<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TEE Calculator - Time-Energy-Efficiency Optimization Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .tee-calculator {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        h2, h3, h4 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        h2 {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            margin: 0;
            font-size: 1.5em;
        }
        
        .input-section, .results-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #34495e;
        }
        
        input[type="text"], 
        input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="range"] {
            width: 70%;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: background 0.3s;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        .optimization-tip, .water-efficiency {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
        }
        
        .result-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #3498db;
        }
        
        .water-metrics {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
        }
        
        .metric {
            text-align: center;
            flex: 1;
        }
        
        .metric-value {
            display: block;
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .metric-label {
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        .water-visualization {
            margin-top: 20px;
        }
        
        .water-bar {
            height: 30px;
            background-color: #ecf0f1;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            margin: 10px 0;
        }
        
        .water-fill {
            height: 100%;
            width: 50%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.5s ease;
        }
        
        .water-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #7f8c8d;
        }
        
        #tee-score {
            font-size: 24px;
            font-weight: bold;
            color: #2ecc71;
        }
        
        #efficiency-percent {
            font-weight: bold;
            color: #3498db;
        }
        
        @media (min-width: 600px) {
            .input-section, .results-section {
                padding: 25px 30px;
            }
            
            .input-group {
                display: flex;
                align-items: center;
            }
            
            .input-group label {
                flex: 1;
                margin-bottom: 0;
            }
            
            .input-group input[type="range"] {
                flex: 3;
            }
            
            .input-group span {
                flex: 0 0 30px;
                text-align: center;
                font-weight: bold;
                color: #3498db;
            }
        }
    </style>
</head>
<body>
    <div class="tee-calculator">
        <h2>TEE Calculator</h2>
        
        <!-- Input Parameters -->
        <div class="input-section">
            <h3>Input Parameters</h3>
            
            <div class="input-group">
                <label for="task-name">Task/Activity Name</label>
                <input type="text" id="task-name" placeholder="e.g., Weekly Team Meeting">
            </div>
            
            <div class="input-group">
                <label for="time-spent">Time Spent (hours)</label>
                <input type="number" id="time-spent" min="0" step="0.1" value="2">
            </div>
            
            <div class="input-group">
                <label for="energy">Energy Level (1-10)</label>
                <input type="range" id="energy" min="1" max="10" value="5">
                <span id="energy-value">5</span>
            </div>
            
            <div class="input-group">
                <label for="efficiency">Efficiency (1-10)</label>
                <input type="range" id="efficiency" min="1" max="10" value="6">
                <span id="efficiency-value">6</span>
            </div>
            
            <div class="input-group">
                <label for="friction">Friction (1-10)</label>
                <input type="range" id="friction" min="1" max="10" value="4">
                <span id="friction-value">4</span>
            </div>
            
            <button id="calculate-btn">Calculate TEE Score</button>
        </div>
        
        <!-- Results -->
        <div class="results-section">
            <h3>Results</h3>
            <div class="result-item">
                <h4>TEE Analysis: <span id="activity-name">Current Activity</span></h4>
                <p>TEE Score: <span id="tee-score">0.0</span></p>
                <p>Efficiency: <span id="efficiency-percent">0%</span></p>
            </div>
            
            <div class="optimization-tip">
                <h4>Optimization Tip</h4>
                <p id="optimization-advice">Enter your task to get personalized advice.</p>
            </div>
            
            <!-- Water Efficiency Section -->
            <div class="water-efficiency">
                <h4>Water Efficiency (NEFC)</h4>
                <div class="water-metrics">
                    <div class="metric">
                        <span class="metric-value" id="water-usage">0</span>
                        <span class="metric-label">Liters Used</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="water-savings">0</span>
                        <span class="metric-label">Liters Saved</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="efficiency-gain">0%</span>
                        <span class="metric-label">Efficiency Gain</span>
                    </div>
                </div>
                <div class="water-visualization">
                    <div class="water-bar">
                        <div class="water-fill" id="water-fill"></div>
                    </div>
                    <div class="water-labels">
                        <span>Standard AI</span>
                        <span>Comphyology AI</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update slider values
        document.getElementById('energy').addEventListener('input', function() {
            document.getElementById('energy-value').textContent = this.value;
        });
        
        document.getElementById('efficiency').addEventListener('input', function() {
            document.getElementById('efficiency-value').textContent = this.value;
        });
        
        document.getElementById('friction').addEventListener('input', function() {
            document.getElementById('friction-value').textContent = this.value;
        });
        
        // Calculate water metrics based on efficiency
        function calculateWaterMetrics(efficiency) {
            // Base water usage for standard AI (liters per hour)
            const BASE_WATER_USAGE = 10; // Example: 10L per hour for standard AI
            
            // Calculate actual water usage (scales with efficiency)
            const waterUsage = BASE_WATER_USAGE * (1 - (efficiency * 0.9)); // Up to 90% reduction
            const waterSavings = BASE_WATER_USAGE - waterUsage;
            const efficiencyGain = ((1 - waterUsage/BASE_WATER_USAGE) * 100).toFixed(0);
            
            // Update UI
            document.getElementById('water-usage').textContent = waterUsage.toFixed(2);
            document.getElementById('water-savings').textContent = waterSavings.toFixed(2);
            document.getElementById('efficiency-gain').textContent = efficiencyGain + '%';
            
            // Update visualization
            document.getElementById('water-fill').style.width = (waterUsage/BASE_WATER_USAGE * 100) + '%';
            
            return { waterUsage, waterSavings, efficiencyGain };
        }
        
        // Calculate TEE score
        function calculateTEEScore() {
            const time = parseFloat(document.getElementById('time-spent').value);
            const energy = parseInt(document.getElementById('energy').value) / 10; // Scale to 0-1
            const efficiency = parseInt(document.getElementById('efficiency').value) / 10;
            const friction = parseInt(document.getElementById('friction').value) / 10;
            
            // TEE formula: (Time * Energy * Efficiency) - Friction
            const teeScore = (time * energy * efficiency) - friction;
            
            // Update UI
            document.getElementById('tee-score').textContent = teeScore.toFixed(1);
            document.getElementById('efficiency-percent').textContent = Math.round(efficiency * 100) + '%';
            
            // Calculate and display water metrics
            calculateWaterMetrics(efficiency);
            
            // Update optimization advice
            updateOptimizationAdvice(teeScore, efficiency, friction);
        }
        
        // Update optimization advice
        function updateOptimizationAdvice(teeScore, efficiency, friction) {
            let advice = '';
            if (efficiency >= 0.7 && friction <= 0.3) {
                advice = '✅ Optimal Zone! Protect these conditions and replicate.';
            } else if (efficiency >= 0.7 && friction > 0.3) {
                advice = '🟡 Friction Zone: Maintain efficiency but reduce friction points.';
            } else if (efficiency < 0.7 && friction <= 0.3) {
                advice = '🟡 Friction Zone: Maintain efficiency but reduce friction points.';
            } else if (efficiency < 0.7 && friction <= 0.3) {
                advice = '🔵 Inefficiency Zone: Improve task structure or delegate.';
            } else if (efficiency < 0.7 && friction > 0.3) {
                advice = '🔴 Danger Zone: High friction and low efficiency!';
            }
            
            document.getElementById('optimization-advice').textContent = advice;
        });
    </script>
</body>
</html>
