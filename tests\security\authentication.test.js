/**
 * NovaConnect Security Tests - Authentication and Authorization
 * 
 * These tests validate the authentication and authorization capabilities
 * of NovaConnect, ensuring proper access control.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics if available
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  authUrl: '/api/auth'
};

// Test users with different roles
const testUsers = {
  admin: {
    username: '<EMAIL>',
    password: 'admin-password',
    roles: ['admin']
  },
  complianceOfficer: {
    username: '<EMAIL>',
    password: 'compliance-password',
    roles: ['compliance_officer']
  },
  securityAnalyst: {
    username: '<EMAIL>',
    password: 'security-password',
    roles: ['security_analyst']
  },
  readonly: {
    username: '<EMAIL>',
    password: 'readonly-password',
    roles: ['readonly']
  }
};

describe('Authentication and Authorization Security Tests', () => {
  // Set a longer timeout for security tests
  jest.setTimeout(30000);
  
  // Store tokens for each user
  const tokens = {};
  
  // Test authentication
  describe('Authentication', () => {
    it('should authenticate users with valid credentials', async () => {
      for (const [role, user] of Object.entries(testUsers)) {
        const { result, duration } = await measureExecutionTime(async () => {
          return await axios.post(`${config.baseUrl}${config.authUrl}/login`, {
            username: user.username,
            password: user.password
          });
        });
        
        expect(result.status).toBe(200);
        expect(result.data).toHaveProperty('token');
        expect(result.data).toHaveProperty('user');
        expect(result.data.user).toHaveProperty('username', user.username);
        expect(result.data.user).toHaveProperty('roles');
        expect(result.data.user.roles).toEqual(expect.arrayContaining(user.roles));
        
        // Store token for authorization tests
        tokens[role] = result.data.token;
        
        console.log(`Authentication for ${role} completed in ${duration.toFixed(2)} ms`);
      }
    });
    
    it('should reject authentication with invalid credentials', async () => {
      try {
        await axios.post(`${config.baseUrl}${config.authUrl}/login`, {
          username: '<EMAIL>',
          password: 'wrong-password'
        });
        
        // If we get here, the test failed
        fail('Authentication should have failed with invalid credentials');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toMatch(/invalid credentials/i);
      }
    });
    
    it('should handle brute force protection', async () => {
      // Attempt multiple failed logins
      const maxAttempts = 5;
      
      for (let i = 0; i < maxAttempts; i++) {
        try {
          await axios.post(`${config.baseUrl}${config.authUrl}/login`, {
            username: '<EMAIL>',
            password: `wrong-password-${i}`
          });
        } catch (error) {
          expect(error.response.status).toBe(401);
        }
      }
      
      // Next attempt should trigger rate limiting
      try {
        await axios.post(`${config.baseUrl}${config.authUrl}/login`, {
          username: '<EMAIL>',
          password: 'wrong-password-again'
        });
        
        // If we get here, the test failed
        fail('Rate limiting should have been triggered after multiple failed attempts');
      } catch (error) {
        expect(error.response.status).toBe(429);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toMatch(/too many attempts/i);
      }
    });
  });
  
  // Test authorization
  describe('Authorization', () => {
    // Define endpoints with expected access by role
    const endpoints = [
      {
        name: 'Get all connectors',
        method: 'get',
        url: '/api/connectors',
        access: {
          admin: true,
          complianceOfficer: true,
          securityAnalyst: true,
          readonly: true
        }
      },
      {
        name: 'Create new connector',
        method: 'post',
        url: '/api/connectors',
        data: {
          name: 'Test Connector',
          type: 'api',
          config: {}
        },
        access: {
          admin: true,
          complianceOfficer: false,
          securityAnalyst: false,
          readonly: false
        }
      },
      {
        name: 'Delete connector',
        method: 'delete',
        url: '/api/connectors/test-connector',
        access: {
          admin: true,
          complianceOfficer: false,
          securityAnalyst: false,
          readonly: false
        }
      },
      {
        name: 'View compliance reports',
        method: 'get',
        url: '/api/compliance/reports',
        access: {
          admin: true,
          complianceOfficer: true,
          securityAnalyst: true,
          readonly: true
        }
      },
      {
        name: 'Create compliance remediation',
        method: 'post',
        url: '/api/compliance/remediation',
        data: {
          finding: 'test-finding',
          action: 'fix-issue'
        },
        access: {
          admin: true,
          complianceOfficer: true,
          securityAnalyst: false,
          readonly: false
        }
      }
    ];
    
    it('should enforce proper access control for different roles', async () => {
      for (const endpoint of endpoints) {
        for (const [role, hasAccess] of Object.entries(endpoint.access)) {
          const token = tokens[role];
          
          try {
            const config = {
              headers: {
                Authorization: `Bearer ${token}`
              }
            };
            
            let response;
            if (endpoint.method === 'get') {
              response = await axios.get(`${config.baseUrl}${endpoint.url}`, config);
            } else if (endpoint.method === 'post') {
              response = await axios.post(`${config.baseUrl}${endpoint.url}`, endpoint.data, config);
            } else if (endpoint.method === 'delete') {
              response = await axios.delete(`${config.baseUrl}${endpoint.url}`, config);
            }
            
            if (hasAccess) {
              expect(response.status).toBe(200);
              console.log(`${role} successfully accessed ${endpoint.name}`);
            } else {
              // If we get here and the user shouldn't have access, the test failed
              fail(`${role} should not have access to ${endpoint.name}`);
            }
          } catch (error) {
            if (hasAccess) {
              // If we get here and the user should have access, the test failed
              fail(`${role} should have access to ${endpoint.name} but got ${error.response?.status}`);
            } else {
              expect(error.response.status).toBe(403);
              console.log(`${role} correctly denied access to ${endpoint.name}`);
            }
          }
        }
      }
    });
    
    it('should reject requests with invalid tokens', async () => {
      try {
        await axios.get(`${config.baseUrl}/api/connectors`, {
          headers: {
            Authorization: 'Bearer invalid-token'
          }
        });
        
        // If we get here, the test failed
        fail('Request should have been rejected with invalid token');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toMatch(/invalid token/i);
      }
    });
    
    it('should reject requests with expired tokens', async () => {
      // Create an expired token (this is a simulation - in reality we'd use a proper JWT library)
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.4Adcj3UFYzPUVaVF43FmMab6RlaQD8A9V8wFzzht-KQ';
      
      try {
        await axios.get(`${config.baseUrl}/api/connectors`, {
          headers: {
            Authorization: `Bearer ${expiredToken}`
          }
        });
        
        // If we get here, the test failed
        fail('Request should have been rejected with expired token');
      } catch (error) {
        expect(error.response.status).toBe(401);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toMatch(/expired token/i);
      }
    });
  });
  
  // Test token management
  describe('Token Management', () => {
    it('should refresh tokens', async () => {
      // Get initial token
      const { result: loginResult } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.authUrl}/login`, {
          username: testUsers.admin.username,
          password: testUsers.admin.password
        });
      });
      
      const initialToken = loginResult.data.token;
      const refreshToken = loginResult.data.refreshToken;
      
      // Wait a moment to ensure tokens are different
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Refresh token
      const { result: refreshResult, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.authUrl}/refresh`, {
          refreshToken
        });
      });
      
      expect(refreshResult.status).toBe(200);
      expect(refreshResult.data).toHaveProperty('token');
      expect(refreshResult.data.token).not.toBe(initialToken);
      
      console.log(`Token refresh completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should invalidate tokens on logout', async () => {
      // Get token
      const { result: loginResult } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.authUrl}/login`, {
          username: testUsers.admin.username,
          password: testUsers.admin.password
        });
      });
      
      const token = loginResult.data.token;
      
      // Logout
      const { result: logoutResult } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.authUrl}/logout`, {}, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
      });
      
      expect(logoutResult.status).toBe(200);
      
      // Try to use the token after logout
      try {
        await axios.get(`${config.baseUrl}/api/connectors`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        // If we get here, the test failed
        fail('Token should have been invalidated after logout');
      } catch (error) {
        expect(error.response.status).toBe(401);
      }
    });
  });
});
