import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Alert,
  IconButton,
  CircularProgress,
  Divider,
  Tooltip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Help as HelpIcon,
  Close as CloseIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import AuthTypeSelector from './AuthTypeSelector';
import DynamicAuthFields from './DynamicAuthFields';
import TestConnectionResult from './TestConnectionResult';
import AuthenticationHelp from './AuthenticationHelp';

/**
 * Authentication Configuration Component
 *
 * This component allows users to configure authentication for an API connector.
 * It supports multiple authentication types and provides real-time validation and testing.
 */
const AuthenticationConfig = ({
  connector,
  updateConnector,
  testConnection,
  isPartOfWizard = false,
  onNext = () => {}
}) => {
  // State for authentication configuration
  const [authType, setAuthType] = useState(connector.authentication?.type || 'API_KEY');
  const [authFields, setAuthFields] = useState(connector.authentication?.fields || {});
  const [testEndpoint, setTestEndpoint] = useState(connector.authentication?.testConnection?.endpoint || '');
  const [testMethod, setTestMethod] = useState(connector.authentication?.testConnection?.method || 'GET');
  const [expectedStatus, setExpectedStatus] = useState(connector.authentication?.testConnection?.expectedResponse?.status || 200);

  // State for testing and validation
  const [credentials, setCredentials] = useState({});
  const [testResult, setTestResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [validationErrors, setValidationErrors] = useState([]);

  // Update connector when authentication configuration changes
  useEffect(() => {
    updateConnector('authentication', {
      type: authType,
      fields: authFields,
      testConnection: {
        endpoint: testEndpoint,
        method: testMethod,
        expectedResponse: {
          status: expectedStatus
        }
      }
    });

    // Validate the configuration
    validateConfiguration();
  }, [authType, authFields, testEndpoint, testMethod, expectedStatus]);

  // Handle authentication type change
  const handleAuthTypeChange = (newType) => {
    setAuthType(newType);

    // Set default fields based on auth type if no fields are defined
    if (Object.keys(authFields).length === 0) {
      setAuthFields(getDefaultFieldsForAuthType(newType));
    }

    // Set default test endpoint based on auth type
    if (!testEndpoint) {
      setTestEndpoint(getDefaultTestEndpoint(newType));
    }
  };

  // Get default fields for authentication type
  const getDefaultFieldsForAuthType = (type) => {
    switch (type) {
      case 'API_KEY':
        return {
          apiKey: {
            type: 'string',
            description: 'API Key',
            required: true,
            sensitive: true
          }
        };
      case 'BASIC':
        return {
          username: {
            type: 'string',
            description: 'Username',
            required: true,
            sensitive: false
          },
          password: {
            type: 'string',
            description: 'Password',
            required: true,
            sensitive: true
          }
        };
      case 'OAUTH2':
        return {
          clientId: {
            type: 'string',
            description: 'Client ID',
            required: true,
            sensitive: false
          },
          clientSecret: {
            type: 'string',
            description: 'Client Secret',
            required: true,
            sensitive: true
          },
          accessToken: {
            type: 'string',
            description: 'Access Token',
            required: false,
            sensitive: true
          },
          refreshToken: {
            type: 'string',
            description: 'Refresh Token',
            required: false,
            sensitive: true
          }
        };
      case 'JWT':
        return {
          token: {
            type: 'string',
            description: 'JWT Token',
            required: true,
            sensitive: true
          }
        };
      case 'AWS_SIG_V4':
        return {
          accessKeyId: {
            type: 'string',
            description: 'AWS Access Key ID',
            required: true,
            sensitive: false
          },
          secretAccessKey: {
            type: 'string',
            description: 'AWS Secret Access Key',
            required: true,
            sensitive: true
          },
          region: {
            type: 'string',
            description: 'AWS Region',
            required: true,
            sensitive: false
          }
        };
      default:
        return {};
    }
  };

  // Get default test endpoint for authentication type
  const getDefaultTestEndpoint = (type) => {
    switch (type) {
      case 'API_KEY':
        return '/status';
      case 'OAUTH2':
        return '/me';
      case 'BASIC':
        return '/api/v1/user';
      case 'JWT':
        return '/api/v1/verify';
      case 'AWS_SIG_V4':
        return '/';
      default:
        return '/status';
    }
  };

  // Handle test connection
  const handleTestConnection = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      // In a real implementation, this would call an API to test the connection
      const result = await testConnection({
        authType,
        credentials,
        endpoint: testEndpoint,
        method: testMethod,
        expectedStatus
      });

      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        message: error.message,
        error: error
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Validate the authentication configuration
  const validateConfiguration = () => {
    const errors = [];

    // Check if auth type is selected
    if (!authType) {
      errors.push('Authentication type is required');
    }

    // Check if fields are defined
    if (Object.keys(authFields).length === 0) {
      errors.push('At least one authentication field is required');
    }

    // Check if test endpoint is defined
    if (!testEndpoint) {
      errors.push('Test endpoint is required');
    }

    setValidationErrors(errors);
  };

  // Check if configuration is valid
  const isConfigurationValid = () => {
    return validationErrors.length === 0;
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h5" component="h2">
          Authentication Configuration
        </Typography>
        <Button
          variant="outlined"
          startIcon={<HelpIcon />}
          onClick={() => setShowHelp(!showHelp)}
        >
          {showHelp ? 'Hide Help' : 'Show Help'}
        </Button>
      </Box>

      {showHelp && (
        <AuthenticationHelp authType={authType} />
      )}

      {validationErrors.length > 0 && (
        <Alert
          severity="warning"
          sx={{ mb: 3 }}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => setValidationErrors([])}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
        >
          <Typography variant="subtitle2">Please fix the following issues:</Typography>
          <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Authentication Type Selection */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom>
              Authentication Type
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Select the authentication method required by the API.
            </Typography>

            <AuthTypeSelector
              selectedType={authType}
              onChange={handleAuthTypeChange}
            />
          </Paper>
        </Grid>

        {/* Authentication Fields */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom>
              Authentication Fields
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Configure the fields required for {authType.replace('_', ' ')} authentication.
            </Typography>

            <DynamicAuthFields
              authType={authType}
              fields={authFields}
              setFields={setAuthFields}
              credentials={credentials}
              setCredentials={setCredentials}
            />
          </Paper>
        </Grid>

        {/* Test Connection Configuration */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom>
              Test Connection
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Configure how to test the connection to the API.
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Test Endpoint"
                  value={testEndpoint}
                  onChange={(e) => setTestEndpoint(e.target.value)}
                  fullWidth
                  required
                  error={!testEndpoint}
                  helperText={!testEndpoint ? 'Test endpoint is required' : 'Endpoint to test the connection'}
                  placeholder="/status"
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth required>
                  <InputLabel id="test-method-label">HTTP Method</InputLabel>
                  <Select
                    labelId="test-method-label"
                    value={testMethod}
                    onChange={(e) => setTestMethod(e.target.value)}
                    label="HTTP Method"
                  >
                    {['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'].map((method) => (
                      <MenuItem key={method} value={method}>
                        {method}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  label="Expected Status Code"
                  value={expectedStatus}
                  onChange={(e) => setExpectedStatus(parseInt(e.target.value) || 200)}
                  fullWidth
                  type="number"
                  helperText="HTTP status code expected from a successful test"
                  placeholder="200"
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <CheckCircleIcon />}
                onClick={handleTestConnection}
                disabled={isLoading || !isConfigurationValid() || Object.keys(credentials).length === 0}
              >
                {isLoading ? 'Testing...' : 'Test Connection'}
              </Button>

              <Tooltip title="Enter credentials in the fields above before testing">
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <InfoIcon fontSize="small" color="info" sx={{ mr: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    {Object.keys(credentials).length === 0
                      ? 'Enter credentials to test'
                      : `Ready to test with ${Object.keys(credentials).length} credential fields`}
                  </Typography>
                </Box>
              </Tooltip>
            </Box>

            {testResult && (
              <Box sx={{ mt: 3 }}>
                <Divider sx={{ mb: 2 }} />
                <TestConnectionResult result={testResult} onClose={() => setTestResult(null)} />
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {isPartOfWizard && (
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={onNext}
            disabled={!isConfigurationValid()}
          >
            Next: Configure Endpoints
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default AuthenticationConfig;
