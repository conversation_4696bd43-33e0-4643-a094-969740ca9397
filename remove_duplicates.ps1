$diagramDir = "D:\novafuse-api-superstore\Comphyology Diagrams\Mermaid"
$files = Get-ChildItem -Path $diagramDir -Filter "*.mmd" -File
$fileHashes = @{}
$duplicates = @()

Write-Host "Scanning for duplicate files..." -ForegroundColor Cyan

# First pass: Calculate hashes
foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $hash = $content.GetHashCode()
    
    if ($fileHashes.ContainsKey($hash)) {
        $duplicates += $file
    } else {
        $fileHashes[$hash] = $file
    }
}

# Remove duplicates
$duplicates | ForEach-Object {
    Write-Host "Removing duplicate: $($_.Name)" -ForegroundColor Yellow
    Remove-Item -Path $_.FullName -Force
}

Write-Host "`nRemoved $($duplicates.Count) duplicate files" -ForegroundColor Green
