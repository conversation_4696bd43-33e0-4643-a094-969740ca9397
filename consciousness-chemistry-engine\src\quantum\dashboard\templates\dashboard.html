{% extends "base.html" %}

{% block extra_head %}
<!-- 3DMol.js for protein visualization -->
<script src="https://3Dmol.csb.pitt.edu/build/3Dmol-min.js"></script>
<style>
    #protein-viewer {
        width: 100%;
        height: 400px;
        position: relative;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        margin-bottom: 1rem;
    }
    .viewer-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        display: flex;
        gap: 5px;
    }
    .viewer-controls .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .viewer-style-btn.active {
        background-color: #0d6efd;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- Header with Stats -->
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <div>
            <h1 class="h2 mb-1">Quantum Protein Folding Dashboard</h1>
            <p class="text-muted mb-0">Monitor and manage quantum protein folding experiments in real-time</p>
        </div>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="refresh-btn">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#newExperimentModal">
                <i class="fas fa-plus"></i> New Experiment
            </button>
        </div>
    </div>
    
    <!-- System Status Row -->
    <div class="row mb-4 g-4">
        <!-- CPU Card -->
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">CPU Usage</h6>
                        <i class="fas fa-microchip text-primary"></i>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress flex-grow-1 me-3" style="height: 8px;">
                            <div id="cpu-usage" class="progress-bar bg-gradient-primary" role="progressbar" style="width: 0%"></div>
                        </div>
                        <h4 class="mb-0" id="cpu-percent">0%</h4>
                    </div>
                    <div class="mt-2 text-muted small" id="cpu-details">
                        <span id="cpu-cores">0</span> Cores
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Memory Card -->
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">Memory Usage</h6>
                        <i class="fas fa-memory text-info"></i>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress flex-grow-1 me-3" style="height: 8px;">
                            <div id="memory-usage" class="progress-bar bg-gradient-info" role="progressbar" style="width: 0%"></div>
                        </div>
                        <h4 class="mb-0" id="memory-percent">0%</h4>
                    </div>
                    <div class="mt-2 text-muted small" id="memory-details">
                        <span id="memory-used">0</span> / <span id="memory-total">0 GB</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- GPU Card (if available) -->
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">GPU Status</h6>
                        <i class="fas fa-microchip text-warning"></i>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress flex-grow-1 me-3" style="height: 8px;">
                            <div id="gpu-usage" class="progress-bar bg-gradient-warning" role="progressbar" style="width: 0%"></div>
                        </div>
                        <h4 class="mb-0" id="gpu-percent">N/A</h4>
                    </div>
                    <div class="mt-2 text-muted small" id="gpu-details">
                        <span id="gpu-name">Not detected</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Experiments Summary -->
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="card-title text-muted mb-0">Experiments</h6>
                        <i class="fas fa-flask text-success"></i>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-center">
                            <h2 class="mb-0" id="total-experiments">0</h2>
                            <small class="text-muted">Total</small>
                        </div>
                        <div class="text-center">
                            <h2 class="mb-0 text-success" id="active-experiments">0</h2>
                            <small class="text-muted">Active</small>
                        </div>
                        <div class="text-center">
                            <h2 class="mb-0 text-primary" id="completed-experiments">0</h2>
                            <small class="text-muted">Completed</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity and System Info Row -->
    <div class="row mb-4">
        <!-- Recent Experiments -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Experiments</h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary filter-btn active" data-filter="all">All</button>
                        <button type="button" class="btn btn-outline-primary filter-btn" data-filter="running">Running</button>
                        <button type="button" class="btn btn-outline-success filter-btn" data-filter="completed">Completed</button>
                        <button type="button" class="btn btn-outline-danger filter-btn" data-filter="failed">Failed</button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0" id="experiments-table">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Started</th>
                                    <th class="text-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="experiments-list">
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 mb-0">Loading experiments...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Showing <span id="showing-count">0</span> of <span id="total-count">0</span> experiments</small>
                        <nav aria-label="Experiments pagination">
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- Pagination will be added by JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Information -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-server me-2 text-muted"></i> Hostname</span>
                            <span class="text-muted" id="system-hostname">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fab fa-python me-2 text-muted"></i> Python</span>
                            <span class="text-muted" id="system-python">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-plug me-2 text-muted"></i> Backend</span>
                            <span class="badge bg-primary" id="system-backend">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-clock me-2 text-muted"></i> Uptime</span>
                            <span class="text-muted" id="system-uptime">-</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-network-wired me-2 text-muted"></i> Network</span>
                            <div class="text-end">
                                <div class="text-muted small" id="network-upload">↑ 0 B/s</div>
                                <div class="text-muted small" id="network-download">↓ 0 B/s</div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">Last updated: <span id="last-updated">Just now</span></small>
                        <button class="btn btn-sm btn-outline-secondary" id="system-refresh">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Protein Visualization -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Protein Structure Visualization</h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary viewer-style-btn active" data-style="cartoon">Cartoon</button>
                        <button type="button" class="btn btn-outline-secondary viewer-style-btn" data-style="stick">Stick</button>
                        <button type="button" class="btn btn-outline-secondary viewer-style-btn" data-style="sphere">Sphere</button>
                        <button type="button" class="btn btn-outline-secondary viewer-style-btn" data-style="line">Line</button>
                        <button type="button" class="btn btn-outline-secondary" id="reset-view">
                            <i class="fas fa-sync-alt"></i> Reset View
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="protein-viewer"></div>
                    <div class="p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-primary me-2" id="current-protein">No protein loaded</span>
                                <span class="text-muted small" id="protein-stats">0 atoms | 0 residues</span>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-outline-primary" id="load-example">
                                    <i class="fas fa-download me-1"></i> Load Example
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" id="export-png">
                                    <i class="fas fa-image me-1"></i> Export PNG
                                </button>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 5px;">
                                <div id="visualization-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quantum Backends -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Available Quantum Backends</h5>
                    <button class="btn btn-sm btn-outline-secondary" id="refresh-backends">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div class="row g-3" id="quantum-backends">
                        <div class="col-12 text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 mb-0">Loading quantum backends...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Experiment Modal -->
    <div class="modal fade" id="newExperimentModal" tabindex="-1" aria-labelledby="newExperimentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="newExperimentModalLabel">New Quantum Experiment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <ul class="nav nav-tabs nav-fill border-0" id="experimentTabs" role="tablist">
                        <li class="nav-item m-0" role="presentation">
                            <button class="nav-link active" id="qaoa-tab" data-bs-toggle="tab" data-bs-target="#qaoa" type="button" role="tab">
                                <i class="fas fa-project-diagram me-2"></i> QAOA
                            </button>
                        </li>
                        <li class="nav-item m-0" role="presentation">
                            <button class="nav-link" id="vqe-tab" data-bs-toggle="tab" data-bs-target="#vqe" type="button" role="tab">
                                <i class="fas fa-atom me-2"></i> VQE
                            </button>
                        </li>
                        <li class="nav-item m-0" role="presentation">
                            <button class="nav-link" id="qml-tab" data-bs-toggle="tab" data-bs-target="#qml" type="button" role="tab">
                                <i class="fas fa-brain me-2"></i> Quantum ML
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content p-4" id="experimentTabsContent">
                        <div class="tab-pane fade show active" id="qaoa" role="tabpanel" aria-labelledby="qaoa-tab">
                            {% include 'experiments/qaoa_form.html' %}
                        </div>
                        <div class="tab-pane fade" id="vqe" role="tabpanel" aria-labelledby="vqe-tab">
                            {% include 'experiments/vqe_form.html' %}
                        </div>
                        <div class="tab-pane fade" id="qml" role="tabpanel" aria-labelledby="qml-tab">
                            {% include 'experiments/qml_form.html' %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
    // Initialize 3DMol viewer
    let viewer = null;
    let currentPdbId = null;
    
    // Initialize the viewer when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Create 3DMol viewer
        const element = document.getElementById('protein-viewer');
        const config = { backgroundColor: 'white' };
        viewer = $3Dmol.createViewer(element, config);
        
        // Set initial style
        updateViewerStyle('cartoon');
        
        // Handle style buttons
        document.querySelectorAll('.viewer-style-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.viewer-style-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                updateViewerStyle(this.dataset.style);
            });
        });
        
        // Reset view button
        document.getElementById('reset-view').addEventListener('click', function() {
            if (viewer) {
                viewer.zoomTo();
                viewer.render();
            }
        });
        
        // Load example protein
        document.getElementById('load-example').addEventListener('click', function() {
            loadProtein('1CRN'); // Example: Crambin protein
        });
        
        // Export PNG
        document.getElementById('export-png').addEventListener('click', function() {
            if (viewer) {
                const pngData = viewer.pngURI();
                const link = document.createElement('a');
                link.download = currentPdbId ? `${currentPdbId}.png` : 'protein.png';
                link.href = pngData;
                link.click();
            }
        });
    });
    
    // Update viewer style
    function updateViewerStyle(style) {
        if (!viewer) return;
        
        viewer.setStyle({}, { [style]: { color: 'spectrum' } });
        viewer.render();
    }
    
    // Load protein from PDB ID
    async function loadProtein(pdbId) {
        if (!viewer) return;
        
        const progressBar = document.getElementById('visualization-progress');
        const proteinName = document.getElementById('current-protein');
        const proteinStats = document.getElementById('protein-stats');
        
        try {
            // Show loading state
            progressBar.style.width = '30%';
            progressBar.classList.remove('bg-success', 'bg-danger');
            progressBar.classList.add('bg-info');
            proteinName.textContent = 'Loading...';
            proteinName.className = 'badge bg-info me-2';
            proteinStats.textContent = 'Fetching structure...';
            
            // Clear previous structure
            viewer.removeAllModels();
            
            // Fetch PDB file
            const url = `https://files.rcsb.org/download/${pdbId}.pdb`;
            const response = await fetch(url);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch PDB: ${response.statusText}`);
            }
            
            const pdbData = await response.text();
            progressBar.style.width = '70%';
            proteinStats.textContent = 'Rendering...';
            
            // Create model from PDB data
            viewer.addModel(pdbData, 'pdb');
            
            // Set style and center
            updateViewerStyle(document.querySelector('.viewer-style-btn.active').dataset.style);
            viewer.zoomTo();
            viewer.zoom(1.2, 1000);
            viewer.render();
            
            // Update UI
            currentPdbId = pdbId;
            proteinName.textContent = pdbId;
            proteinName.className = 'badge bg-success me-2';
            
            // Get stats
            const atomCount = viewer.selectedAtoms({}).length;
            const residueCount = new Set(
                viewer.selectedAtoms({}).map(atom => `${atom.chain}${atom.resi}`)
            ).size;
            
            proteinStats.textContent = `${atomCount.toLocaleString()} atoms | ${residueCount.toLocaleString()} residues`;
            progressBar.style.width = '100%';
            progressBar.classList.remove('bg-info');
            progressBar.classList.add('bg-success');
            
            // Reset progress bar after delay
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 2000);
            
        } catch (error) {
            console.error('Error loading protein:', error);
            proteinName.textContent = 'Error';
            proteinName.className = 'badge bg-danger me-2';
            proteinStats.textContent = error.message || 'Failed to load protein';
            progressBar.style.width = '100%';
            progressBar.classList.remove('bg-info');
            progressBar.classList.add('bg-danger');
            
            // Reset progress bar after delay
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 3000);
        }
    }
    
    // Dashboard specific JavaScript will go here
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard
        console.log('Dashboard initialized');
        
        // Connect to WebSocket
        const socket = io();
        
        // Handle connection status
        socket.on('connect', () => {
            console.log('Connected to WebSocket server');
            document.getElementById('connection-status').innerHTML = '<i class="fas fa-circle"></i> Connected';
            document.getElementById('connection-status').className = 'text-success';
        });
        
        socket.on('disconnect', () => {
            console.log('Disconnected from WebSocket server');
            document.getElementById('connection-status').innerHTML = '<i class="fas fa-circle"></i> Disconnected';
            document.getElementById('connection-status').className = 'text-danger';
        });
        
        // Handle experiment updates
        socket.on('experiment_update', (data) => {
            console.log('Experiment update:', data);
            updateExperimentList();
        });
        
        // Handle system status updates
        socket.on('system_status', (data) => {
            console.log('System status update:', data);
            updateSystemStatus(data);
        });
        
        // Handle quantum backends update
        socket.on('quantum_backends', (data) => {
            console.log('Quantum backends update:', data);
            updateQuantumBackends(data);
        });
        
        // Refresh dashboard button
        document.getElementById('refresh-dashboard').addEventListener('click', () => {
            updateDashboard();
        });
        
        // New experiment button
        document.getElementById('new-experiment').addEventListener('click', () => {
            const modal = new bootstrap.Modal(document.getElementById('newExperimentModal'));
            modal.show();
        });
        
        // View all experiments button
        document.getElementById('view-all-experiments').addEventListener('click', () => {
            // Navigate to experiments list view
            console.log('View all experiments');
        });
        
        // Initial data load
        updateDashboard();
    });
    
    function updateDashboard() {
        // Fetch latest data from server
        fetch('/api/experiments')
            .then(response => response.json())
            .then(data => {
                updateExperimentList(data);
            })
            .catch(error => {
                console.error('Error fetching experiments:', error);
            });
        
        // Request system status update
        fetch('/api/system/status')
            .then(response => response.json())
            .then(data => {
                updateSystemStatus(data);
            })
            .catch(error => {
                console.error('Error fetching system status:', error);
            });
        
        // Request quantum backends update
        fetch('/api/quantum/backends')
            .then(response => response.json())
            .then(data => {
                updateQuantumBackends(data);
            })
            .catch(error => {
                console.error('Error fetching quantum backends:', error);
            });
    }
    
    function updateExperimentList(experiments = []) {
        const tbody = document.getElementById('recent-experiments');
        
        if (experiments.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">No experiments found</td></tr>';
            return;
        }
        
        // Sort by creation date (newest first)
        experiments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        
        // Take only the 5 most recent
        const recentExperiments = experiments.slice(0, 5);
        
        // Update stats
        document.getElementById('total-experiments').textContent = experiments.length;
        document.getElementById('completed-experiments').textContent = 
            experiments.filter(e => e.status === 'completed').length;
        document.getElementById('running-experiments').textContent = 
            experiments.filter(e => e.status === 'running').length;
        document.getElementById('failed-experiments').textContent = 
            experiments.filter(e => e.status === 'failed').length;
        
        // Update table
        tbody.innerHTML = recentExperiments.map(exp => `
            <tr class="experiment-row" data-id="${exp.id}">
                <td><code>${exp.id.substring(0, 8)}</code></td>
                <td>${exp.name}</td>
                <td><span class="badge bg-primary">${exp.type.toUpperCase()}</span></td>
                <td><span class="badge ${getStatusBadgeClass(exp.status)}">${exp.status}</span></td>
                <td>${new Date(exp.created_at).toLocaleString()}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary view-experiment" data-id="${exp.id}">
                        <i class="fas fa-eye"></i> View
                    </button>
                </td>
            </tr>
        `).join('');
        
        // Add event listeners to view buttons
        document.querySelectorAll('.view-experiment').forEach(button => {
            button.addEventListener('click', (e) => {
                const experimentId = e.target.closest('button').dataset.id;
                viewExperiment(experimentId);
            });
        });
    }
    
    function getStatusBadgeClass(status) {
        const statusClasses = {
            'queued': 'bg-secondary',
            'running': 'bg-warning text-dark',
            'completed': 'bg-success',
            'failed': 'bg-danger'
        };
        return statusClasses[status] || 'bg-secondary';
    }
    
    function updateSystemStatus(status) {
        if (status.cpu) {
            const cpuPercent = Math.round(status.cpu.percent);
            document.getElementById('cpu-usage').textContent = `${cpuPercent}%`;
            document.getElementById('cpu-progress').style.width = `${cpuPercent}%`;
            document.getElementById('cpu-progress').className = `progress-bar ${getProgressBarClass(cpuPercent)}`;
        }
        
        if (status.memory) {
            const memoryPercent = Math.round((status.memory.used / status.memory.total) * 100);
            document.getElementById('memory-usage').textContent = `${memoryPercent}%`;
            document.getElementById('memory-progress').style.width = `${memoryPercent}%`;
            document.getElementById('memory-progress').className = `progress-bar ${getProgressBarClass(memoryPercent)}`;
        }
        
        if (status.gpu) {
            const gpuPercent = status.gpu.utilization || 0;
            document.getElementById('gpu-usage').textContent = `${gpuPercent}%`;
            document.getElementById('gpu-progress').style.width = `${gpuPercent}%`;
            document.getElementById('gpu-progress').className = `progress-bar ${getProgressBarClass(gpuPercent)}`;
        } else {
            document.getElementById('gpu-usage').textContent = 'N/A';
            document.getElementById('gpu-progress').style.width = '0%';
            document.getElementById('gpu-progress').className = 'progress-bar bg-secondary';
        }
    }
    
    function getProgressBarClass(percent) {
        if (percent < 50) return 'bg-success';
        if (percent < 80) return 'bg-warning';
        return 'bg-danger';
    }
    
    function updateQuantumBackends(backends) {
        const container = document.getElementById('quantum-backends');
        
        if (!backends || backends.length === 0) {
            container.innerHTML = `
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    No quantum backends available
                </div>
            `;
            return;
        }
        
        container.innerHTML = `
            <div class="list-group">
                ${backends.map(backend => `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${backend.name}</h6>
                                <small class="text-muted">${backend.provider} • ${backend.type}</small>
                            </div>
                            <span class="badge ${backend.status === 'online' ? 'bg-success' : 'bg-secondary'}">
                                ${backend.status}
                            </span>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    function viewExperiment(experimentId) {
        // Navigate to experiment detail view
        console.log('Viewing experiment:', experimentId);
        // This would typically load a new view or update the current view
        // to show detailed information about the experiment
    }
</script>
{% endblock %}
