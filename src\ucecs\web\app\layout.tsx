import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { AuthProvider } from '@/contexts/AuthContext';
import { FeatureFlagProvider } from '@/contexts/FeatureFlagContext';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'NovaFuse NovaSphere',
  description: 'Digital Trust Layer for Compliance Evidence',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <FeatureFlagProvider>
            {children}
          </FeatureFlagProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
