/**
 * Simple Unit Tests for NovaTrack TrackingManager
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

// Import test data generator
const { generateRequirement } = require('../../data/novatrack-test-data');

describe('NovaTrack TrackingManager - Simple Tests', () => {
  let trackingManager;
  let tempDir;

  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'novatrack-test-'));

    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });

  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });

  describe('create_requirement', () => {
    it('should create a new requirement with valid data', () => {
      // Arrange
      const requirementData = generateRequirement();

      // Act
      const requirement = trackingManager.create_requirement(requirementData);

      // Assert
      expect(requirement).toBeDefined();
      expect(requirement.id).toBeDefined();
      expect(requirement.name).toBe(requirementData.name);
      expect(requirement.description).toBe(requirementData.description);
      expect(requirement.framework).toBe(requirementData.framework);
      expect(requirement.category).toBe(requirementData.category);
      expect(requirement.priority).toBe(requirementData.priority);
      expect(requirement.status).toBe(requirementData.status);
      expect(requirement.due_date).toBe(requirementData.due_date);
      expect(requirement.assigned_to).toBe(requirementData.assigned_to);
      expect(requirement.tags).toEqual(requirementData.tags);
      expect(requirement.created_at).toBeDefined();
      expect(requirement.updated_at).toBeDefined();
    });
  });
});
