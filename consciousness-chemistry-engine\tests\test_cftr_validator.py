"""
Tests for the CFTR validator module.
"""

import unittest
import os
import sys
import tempfile
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.metrics.cftr_validator import CFTRValidator, COMMON_CF_MUTATIONS

class TestCFTRValidator(unittest.TestCase):
    """Test cases for the CFTR validator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.validator = CFTRValidator()
        self.wild_type_sequence = self.validator._load_wild_type_sequence()
        
        # Create a delta F508del mutant
        self.delta_f508_sequence = (
            self.wild_type_sequence[:507] +  # Up to position 507 (0-based)
            self.wild_type_sequence[508:]    # Skip position 508 (F508)
        )
        
        # Create a G551D mutant
        self.g551d_sequence = (
            self.wild_type_sequence[:550] +  # Up to position 550 (0-based)
            'D' +                           # G551D mutation (G->D at 551)
            self.wild_type_sequence[551:]    # Rest of sequence
        )
    
    def test_wild_type_validation(self):
        """Test validation of wild-type CFTR sequence."""
        result = self.validator.validate(self.wild_type_sequence)
        
        self.assertTrue(result.is_cftr)
        self.assertFalse(result.delta_f508_present)
        self.assertTrue(result.validation_passed)
        
        # Check domain integrity scores
        for domain, score in result.domain_integrity.items():
            self.assertGreaterEqual(score, 0.95, f"Domain {domain} has low identity")
        
        # Check that no common mutations are present
        for mut_id, status in result.mutation_status.items():
            self.assertFalse(status.get('present', False), 
                           f"Mutation {mut_id} should not be present in wild-type")
    
    def test_delta_f508_detection(self):
        """Test detection of delta F508 mutation."""
        result = self.validator.validate(self.delta_f508_sequence)
        
        self.assertTrue(result.is_cftr)
        self.assertTrue(result.delta_f508_present)
        self.assertFalse(result.validation_passed)
        
        # Check that F508del is reported as present
        self.assertTrue(result.mutation_status['F508del']['present'])
    
    def test_g551d_detection(self):
        """Test detection of G551D mutation."""
        result = self.validator.validate(self.g551d_sequence)
        
        self.assertTrue(result.is_cftr)
        self.assertFalse(result.delta_f508_present)
        self.assertFalse(result.validation_passed)
        
        # Check that G551D is reported as present
        self.assertTrue(result.mutation_status['G551D']['present'])
        
        # Check that the actual residue is D
        self.assertEqual(result.mutation_status['G551D']['actual'], 'D')
    
    def test_short_sequence(self):
        """Test validation of a sequence that's too short to be CFTR."""
        short_sequence = "ACDEFGHIKL" * 5  # 50 residues
        result = self.validator.validate(short_sequence)
        
        self.assertFalse(result.is_cftr)
        self.assertFalse(result.validation_passed)
    
    def test_mutation_impact(self):
        """Test getting impact information for known mutations."""
        # Test F508del
        impact = self.validator.get_mutation_impact('F508del')
        self.assertEqual(impact['name'], 'Phe508del')
        self.assertEqual(impact['domain'], 'NBD1')
        self.assertIn('Ivacaftor', ' '.join(impact['therapies']))
        
        # Test G551D
        impact = self.validator.get_mutation_impact('G551D')
        self.assertEqual(impact['name'], 'Gly551Asp')
        self.assertEqual(impact['domain'], 'NBD1')
        self.assertIn('Ivacaftor', impact['therapies'])
        
        # Test unknown mutation
        impact = self.validator.get_mutation_impact('X999Y')
        self.assertIn('error', impact)

if __name__ == '__main__':
    unittest.main()
