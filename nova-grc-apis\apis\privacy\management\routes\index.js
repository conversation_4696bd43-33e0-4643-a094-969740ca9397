/**
 * Privacy Management API Routes
 * 
 * This module exports all routes for the Privacy Management API.
 */

const express = require('express');
const router = express.Router();

// Import route modules
const dataProcessingRoutes = require('./dataProcessingRoutes');
const dataSubjectRequestRoutes = require('./dataSubjectRequestRoutes');
const consentRoutes = require('./consentRoutes');
const privacyNoticeRoutes = require('./privacyNoticeRoutes');
const dataBreachRoutes = require('./dataBreachRoutes');
const integrationRoutes = require('./integrationRoutes');
const notificationRoutes = require('./notificationRoutes');
const reportRoutes = require('./reportRoutes');
const regulatoryRoutes = require('./regulatoryRoutes');

// Use route modules
router.use('/processing-activities', dataProcessingRoutes);
router.use('/subject-requests', dataSubjectRequestRoutes);
router.use('/consent', consentRoutes);
router.use('/privacy-notices', privacyNoticeRoutes);
router.use('/data-breaches', dataBreachRoutes);
router.use('/integrations', integrationRoutes);
router.use('/notifications', notificationRoutes);
router.use('/reports', reportRoutes);
router.use('/regulatory', regulatoryRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'privacy-management-api'
  });
});

module.exports = router;
