<!DOCTYPE html>
<html>
<head>
    <title>TEE Optimization Matrix Preview</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f9f9f9;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-top: 20px;
        }
        .diagram {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .legend {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            align-self: start;
        }
        h1 {
            color: #2E7D32;
            text-align: center;
            margin-bottom: 30px;
        }
        .quadrant {
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 5px solid;
        }
        .q1 { background: #E8F5E9; border-color: #2E7D32; }
        .q2 { background: #FFF8E1; border-color: #FFA000; }
        .q3 { background: #FFEBEE; border-color: #B71C1C; }
        .q4 { background: #E3F2FD; border-color: #1565C0; }
        .quadrant h3 { margin-top: 0; }
        .quadrant ul { margin: 10px 0 0 20px; }
        .matrix-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .matrix-cell {
            padding: 15px;
            border-radius: 6px;
            min-height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .matrix-cell h3 { margin-top: 0; }
    </style>
</head>
<body>
    <h1>TEE Optimization Matrix</h1>
    
    <div class="container">
        <div class="diagram">
            <div class="mermaid">
                graph TD
                    %% Matrix Grid
                    subgraph Optimization_Matrix["TEE Optimization Matrix"]
                        direction TB
                        
                        %% Quadrant Labels
                        Q1["Q1: High η, Low F<br>Optimal Zone"]
                        Q2["Q2: High η, High F<br>Friction Zone"]
                        Q3["Q3: Low η, High F<br>Danger Zone"]
                        Q4["Q4: Low η, Low F<br>Inefficiency Zone"]
                        
                        %% Axis Labels
                        Y["Efficiency (η)"]
                        X["Friction (F)"]
                        
                        %% Grid Lines
                        classDef axis stroke:#666,stroke-width:2px
                        classDef q1 fill:#E8F5E9,stroke:#2E7D32,stroke-width:2px
                        classDef q2 fill:#FFF8E1,stroke:#FFA000,stroke-width:2px
                        classDef q3 fill:#FFEBEE,stroke:#B71C1C,stroke-width:2px
                        classDef q4 fill:#E3F2FD,stroke:#1565C0,stroke-width:2px
                        
                        class Q1 q1
                        class Q2 q2
                        class Q3 q3
                        class Q4 q4
                        class Y,X axis
                    end
            </div>
            
            <div class="matrix-grid">
                <div class="matrix-cell q1">
                    <h3>🟢 Q1: Optimal Zone</h3>
                    <p>High η, Low F</p>
                    <p>Maximize and protect</p>
                </div>
                <div class="matrix-cell q2">
                    <h3>🟡 Q2: Friction Zone</h3>
                    <p>High η, High F</p>
                    <p>Reduce friction</p>
                </div>
                <div class="matrix-cell q4">
                    <h3>🔵 Q4: Inefficiency Zone</h3>
                    <p>Low η, Low F</p>
                    <p>Improve efficiency</p>
                </div>
                <div class="matrix-cell q3">
                    <h3>🔴 Q3: Danger Zone</h3>
                    <p>Low η, High F</p>
                    <p>Eliminate/redesign</p>
                </div>
            </div>
        </div>
        
        <div class="legend">
            <h3>Quadrant Actions</h3>
            
            <div class="quadrant q1">
                <h4>🟢 Q1: Optimal Zone</h4>
                <p><strong>High η, Low F</strong></p>
                <ul>
                    <li>Maximize these activities</li>
                    <li>Document best practices</li>
                    <li>Consider automation</li>
                </ul>
            </div>
            
            <div class="quadrant q2">
                <h4>🟡 Q2: Friction Zone</h4>
                <p><strong>High η, High F</strong></p>
                <ul>
                    <li>Identify friction points</li>
                    <li>Streamline workflows</li>
                    <li>Automate where possible</li>
                </ul>
            </div>
            
            <div class="quadrant q4">
                <h4>🔵 Q4: Inefficiency Zone</h4>
                <p><strong>Low η, Low F</strong></p>
                <ul>
                    <li>Question value</li>
                    <li>Batch process</li>
                    <li>Set time limits</li>
                </ul>
            </div>
            
            <div class="quadrant q3">
                <h4>🔴 Q3: Danger Zone</h4>
                <p><strong>Low η, High F</strong></p>
                <ul>
                    <li>Eliminate</li>
                    <li>Redesign completely</li>
                    <li>Question necessity</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
