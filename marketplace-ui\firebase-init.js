import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";
import { firebaseConfig } from "./firebase-config";

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const firestore = getFirestore(app);
const functions = getFunctions(app);

// Connect to emulators in development
if (process.env.NODE_ENV === "development") {
  connectAuthEmulator(auth, "http://localhost:9599");
  connectFirestoreEmulator(firestore, "localhost", 8580);
  connectFunctionsEmulator(functions, "localhost", 5501);
}

export { app, auth, firestore, functions };
