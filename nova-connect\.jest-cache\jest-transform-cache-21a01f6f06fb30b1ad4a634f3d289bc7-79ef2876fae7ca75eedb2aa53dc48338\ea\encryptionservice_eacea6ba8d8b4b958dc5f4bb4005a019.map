{"version": 3, "names": ["crypto", "require", "promisify", "KeyManagementServiceClient", "randomBytesAsync", "randomBytes", "pbkdf2Async", "pbkdf2", "EncryptionService", "constructor", "options", "algorithm", "<PERSON><PERSON><PERSON><PERSON>", "iv<PERSON><PERSON><PERSON>", "saltLength", "iterations", "digest", "tag<PERSON><PERSON><PERSON>", "useGcpKms", "keyCache", "Map", "_initializeKmsClient", "metrics", "encryptionOperations", "decryptionOperations", "keyRotations", "totalEncryptionTime", "totalDecryptionTime", "averageEncryptionTime", "averageDecryptionTime", "kmsClient", "credentials", "gcpCredentials", "projectId", "gcpProjectId", "kmsKeyName", "error", "console", "<PERSON><PERSON>ey", "startTime", "Date", "now", "key", "keyId", "createHash", "update", "toString", "set", "createdAt", "toISOString", "endTime", "duration", "totalKeyRotationTime", "averageKeyRotationTime", "encrypt", "data", "dataBuffer", "<PERSON><PERSON><PERSON>", "from", "JSON", "stringify", "keyInfo", "get", "Error", "iv", "cipher", "createCipheriv", "encrypted", "concat", "final", "authTag", "getAuthTag", "encryptedPackage", "encryptedAt", "decrypt", "iv<PERSON><PERSON><PERSON>", "authTagBuffer", "encryptedBuffer", "decipher", "createDecipheriv", "setAuthTag", "decrypted", "encryptWithPassword", "password", "salt", "decryptWithPassword", "saltBuffer", "encryptWithKms", "encryptResponse", "name", "plaintext", "ciphertext", "decryptWithKms", "ciphertextBuffer", "decryptResponse", "rotateKeys", "new<PERSON>ey", "entries", "rotatedAt", "<PERSON><PERSON><PERSON>", "getMetrics", "module", "exports"], "sources": ["encryption-service.js"], "sourcesContent": ["/**\n * NovaConnect Encryption Service\n * \n * FIPS 140-3 compliant encryption service for securing sensitive data\n * in NovaConnect. Supports key rotation, envelope encryption, and\n * integration with Google Cloud KMS.\n */\n\nconst crypto = require('crypto');\nconst { promisify } = require('util');\nconst { KeyManagementServiceClient } = require('@google-cloud/kms');\n\n// Promisify crypto functions\nconst randomBytesAsync = promisify(crypto.randomBytes);\nconst pbkdf2Async = promisify(crypto.pbkdf2);\n\nclass EncryptionService {\n  constructor(options = {}) {\n    this.options = {\n      algorithm: 'aes-256-gcm',\n      keyLength: 32, // 256 bits\n      ivLength: 16, // 128 bits\n      saltLength: 32, // 256 bits\n      iterations: 100000,\n      digest: 'sha256',\n      tagLength: 16, // 128 bits\n      useGcpKms: false,\n      ...options\n    };\n    \n    // Initialize key cache\n    this.keyCache = new Map();\n    \n    // Initialize GCP KMS client if enabled\n    if (this.options.useGcpKms) {\n      this._initializeKmsClient();\n    }\n    \n    // Initialize metrics\n    this.metrics = {\n      encryptionOperations: 0,\n      decryptionOperations: 0,\n      keyRotations: 0,\n      totalEncryptionTime: 0,\n      totalDecryptionTime: 0,\n      averageEncryptionTime: 0,\n      averageDecryptionTime: 0\n    };\n  }\n  \n  /**\n   * Initialize GCP KMS client\n   * @private\n   */\n  _initializeKmsClient() {\n    try {\n      this.kmsClient = new KeyManagementServiceClient({\n        credentials: this.options.gcpCredentials,\n        projectId: this.options.gcpProjectId\n      });\n      \n      this.kmsKeyName = this.options.kmsKeyName;\n    } catch (error) {\n      console.error('Error initializing KMS client:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Generate a new encryption key\n   * @returns {Object} - Key information\n   */\n  async generateKey() {\n    const startTime = Date.now();\n    \n    try {\n      // Generate a random key\n      const key = await randomBytesAsync(this.options.keyLength);\n      \n      // Generate a unique key ID\n      const keyId = crypto.createHash('sha256')\n        .update(key)\n        .update(Date.now().toString())\n        .digest('hex');\n      \n      // Store key in cache\n      this.keyCache.set(keyId, {\n        key,\n        createdAt: new Date().toISOString(),\n        algorithm: this.options.algorithm\n      });\n      \n      return {\n        keyId,\n        algorithm: this.options.algorithm,\n        createdAt: new Date().toISOString()\n      };\n    } catch (error) {\n      console.error('Error generating key:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.keyRotations++;\n      this.metrics.totalKeyRotationTime = (this.metrics.totalKeyRotationTime || 0) + duration;\n      this.metrics.averageKeyRotationTime = this.metrics.totalKeyRotationTime / this.metrics.keyRotations;\n    }\n  }\n  \n  /**\n   * Encrypt data using the specified key\n   * @param {string|Buffer|Object} data - Data to encrypt\n   * @param {string} keyId - Key ID to use for encryption\n   * @returns {Object} - Encrypted data\n   */\n  async encrypt(data, keyId) {\n    const startTime = Date.now();\n    \n    try {\n      // Convert data to buffer if needed\n      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer)\n        ? Buffer.from(JSON.stringify(data))\n        : Buffer.from(data);\n      \n      // Get the key\n      const keyInfo = this.keyCache.get(keyId);\n      \n      if (!keyInfo) {\n        throw new Error(`Key with ID ${keyId} not found`);\n      }\n      \n      // Generate IV\n      const iv = await randomBytesAsync(this.options.ivLength);\n      \n      // Create cipher\n      const cipher = crypto.createCipheriv(\n        this.options.algorithm,\n        keyInfo.key,\n        iv\n      );\n      \n      // Encrypt data\n      const encrypted = Buffer.concat([\n        cipher.update(dataBuffer),\n        cipher.final()\n      ]);\n      \n      // Get authentication tag\n      const authTag = cipher.getAuthTag();\n      \n      // Create encrypted package\n      const encryptedPackage = {\n        keyId,\n        iv: iv.toString('base64'),\n        authTag: authTag.toString('base64'),\n        data: encrypted.toString('base64'),\n        algorithm: this.options.algorithm,\n        encryptedAt: new Date().toISOString()\n      };\n      \n      return encryptedPackage;\n    } catch (error) {\n      console.error('Error encrypting data:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.encryptionOperations++;\n      this.metrics.totalEncryptionTime += duration;\n      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;\n    }\n  }\n  \n  /**\n   * Decrypt data using the specified key\n   * @param {Object} encryptedPackage - Encrypted data package\n   * @returns {Buffer} - Decrypted data\n   */\n  async decrypt(encryptedPackage) {\n    const startTime = Date.now();\n    \n    try {\n      const { keyId, iv, authTag, data, algorithm } = encryptedPackage;\n      \n      // Get the key\n      const keyInfo = this.keyCache.get(keyId);\n      \n      if (!keyInfo) {\n        throw new Error(`Key with ID ${keyId} not found`);\n      }\n      \n      // Convert base64 strings to buffers\n      const ivBuffer = Buffer.from(iv, 'base64');\n      const authTagBuffer = Buffer.from(authTag, 'base64');\n      const encryptedBuffer = Buffer.from(data, 'base64');\n      \n      // Create decipher\n      const decipher = crypto.createDecipheriv(\n        algorithm || this.options.algorithm,\n        keyInfo.key,\n        ivBuffer\n      );\n      \n      // Set auth tag\n      decipher.setAuthTag(authTagBuffer);\n      \n      // Decrypt data\n      const decrypted = Buffer.concat([\n        decipher.update(encryptedBuffer),\n        decipher.final()\n      ]);\n      \n      return decrypted;\n    } catch (error) {\n      console.error('Error decrypting data:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.decryptionOperations++;\n      this.metrics.totalDecryptionTime += duration;\n      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;\n    }\n  }\n  \n  /**\n   * Encrypt data with password-based encryption\n   * @param {string|Buffer|Object} data - Data to encrypt\n   * @param {string} password - Password to use for encryption\n   * @returns {Object} - Encrypted data\n   */\n  async encryptWithPassword(data, password) {\n    const startTime = Date.now();\n    \n    try {\n      // Convert data to buffer if needed\n      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer)\n        ? Buffer.from(JSON.stringify(data))\n        : Buffer.from(data);\n      \n      // Generate salt\n      const salt = await randomBytesAsync(this.options.saltLength);\n      \n      // Derive key from password\n      const key = await pbkdf2Async(\n        password,\n        salt,\n        this.options.iterations,\n        this.options.keyLength,\n        this.options.digest\n      );\n      \n      // Generate IV\n      const iv = await randomBytesAsync(this.options.ivLength);\n      \n      // Create cipher\n      const cipher = crypto.createCipheriv(\n        this.options.algorithm,\n        key,\n        iv\n      );\n      \n      // Encrypt data\n      const encrypted = Buffer.concat([\n        cipher.update(dataBuffer),\n        cipher.final()\n      ]);\n      \n      // Get authentication tag\n      const authTag = cipher.getAuthTag();\n      \n      // Create encrypted package\n      const encryptedPackage = {\n        salt: salt.toString('base64'),\n        iv: iv.toString('base64'),\n        authTag: authTag.toString('base64'),\n        data: encrypted.toString('base64'),\n        algorithm: this.options.algorithm,\n        iterations: this.options.iterations,\n        keyLength: this.options.keyLength,\n        digest: this.options.digest,\n        encryptedAt: new Date().toISOString()\n      };\n      \n      return encryptedPackage;\n    } catch (error) {\n      console.error('Error encrypting data with password:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.encryptionOperations++;\n      this.metrics.totalEncryptionTime += duration;\n      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;\n    }\n  }\n  \n  /**\n   * Decrypt data with password-based encryption\n   * @param {Object} encryptedPackage - Encrypted data package\n   * @param {string} password - Password to use for decryption\n   * @returns {Buffer} - Decrypted data\n   */\n  async decryptWithPassword(encryptedPackage, password) {\n    const startTime = Date.now();\n    \n    try {\n      const { \n        salt, \n        iv, \n        authTag, \n        data, \n        algorithm, \n        iterations, \n        keyLength, \n        digest \n      } = encryptedPackage;\n      \n      // Convert base64 strings to buffers\n      const saltBuffer = Buffer.from(salt, 'base64');\n      const ivBuffer = Buffer.from(iv, 'base64');\n      const authTagBuffer = Buffer.from(authTag, 'base64');\n      const encryptedBuffer = Buffer.from(data, 'base64');\n      \n      // Derive key from password\n      const key = await pbkdf2Async(\n        password,\n        saltBuffer,\n        iterations || this.options.iterations,\n        keyLength || this.options.keyLength,\n        digest || this.options.digest\n      );\n      \n      // Create decipher\n      const decipher = crypto.createDecipheriv(\n        algorithm || this.options.algorithm,\n        key,\n        ivBuffer\n      );\n      \n      // Set auth tag\n      decipher.setAuthTag(authTagBuffer);\n      \n      // Decrypt data\n      const decrypted = Buffer.concat([\n        decipher.update(encryptedBuffer),\n        decipher.final()\n      ]);\n      \n      return decrypted;\n    } catch (error) {\n      console.error('Error decrypting data with password:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.decryptionOperations++;\n      this.metrics.totalDecryptionTime += duration;\n      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;\n    }\n  }\n  \n  /**\n   * Encrypt data using Google Cloud KMS\n   * @param {string|Buffer|Object} data - Data to encrypt\n   * @returns {Object} - Encrypted data\n   */\n  async encryptWithKms(data) {\n    if (!this.options.useGcpKms) {\n      throw new Error('GCP KMS is not enabled');\n    }\n    \n    const startTime = Date.now();\n    \n    try {\n      // Convert data to buffer if needed\n      const dataBuffer = typeof data === 'object' && !(data instanceof Buffer)\n        ? Buffer.from(JSON.stringify(data))\n        : Buffer.from(data);\n      \n      // Encrypt with KMS\n      const [encryptResponse] = await this.kmsClient.encrypt({\n        name: this.kmsKeyName,\n        plaintext: dataBuffer\n      });\n      \n      // Create encrypted package\n      const encryptedPackage = {\n        data: encryptResponse.ciphertext.toString('base64'),\n        kmsKeyName: this.kmsKeyName,\n        encryptedAt: new Date().toISOString()\n      };\n      \n      return encryptedPackage;\n    } catch (error) {\n      console.error('Error encrypting data with KMS:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.encryptionOperations++;\n      this.metrics.totalEncryptionTime += duration;\n      this.metrics.averageEncryptionTime = this.metrics.totalEncryptionTime / this.metrics.encryptionOperations;\n    }\n  }\n  \n  /**\n   * Decrypt data using Google Cloud KMS\n   * @param {Object} encryptedPackage - Encrypted data package\n   * @returns {Buffer} - Decrypted data\n   */\n  async decryptWithKms(encryptedPackage) {\n    if (!this.options.useGcpKms) {\n      throw new Error('GCP KMS is not enabled');\n    }\n    \n    const startTime = Date.now();\n    \n    try {\n      const { data, kmsKeyName } = encryptedPackage;\n      \n      // Convert base64 string to buffer\n      const ciphertextBuffer = Buffer.from(data, 'base64');\n      \n      // Decrypt with KMS\n      const [decryptResponse] = await this.kmsClient.decrypt({\n        name: kmsKeyName || this.kmsKeyName,\n        ciphertext: ciphertextBuffer\n      });\n      \n      return decryptResponse.plaintext;\n    } catch (error) {\n      console.error('Error decrypting data with KMS:', error);\n      throw error;\n    } finally {\n      const endTime = Date.now();\n      const duration = endTime - startTime;\n      \n      // Update metrics\n      this.metrics.decryptionOperations++;\n      this.metrics.totalDecryptionTime += duration;\n      this.metrics.averageDecryptionTime = this.metrics.totalDecryptionTime / this.metrics.decryptionOperations;\n    }\n  }\n  \n  /**\n   * Rotate encryption keys\n   * @returns {Object} - New key information\n   */\n  async rotateKeys() {\n    // Generate a new key\n    const newKey = await this.generateKey();\n    \n    // Mark old keys as rotated\n    for (const [keyId, keyInfo] of this.keyCache.entries()) {\n      if (keyId !== newKey.keyId) {\n        keyInfo.rotatedAt = new Date().toISOString();\n        keyInfo.replacedBy = newKey.keyId;\n      }\n    }\n    \n    return newKey;\n  }\n  \n  /**\n   * Get metrics for the encryption service\n   * @returns {Object} - Metrics\n   */\n  getMetrics() {\n    return this.metrics;\n  }\n}\n\nmodule.exports = EncryptionService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAM;EAAEC;AAAU,CAAC,GAAGD,OAAO,CAAC,MAAM,CAAC;AACrC,MAAM;EAAEE;AAA2B,CAAC,GAAGF,OAAO,CAAC,mBAAmB,CAAC;;AAEnE;AACA,MAAMG,gBAAgB,GAAGF,SAAS,CAACF,MAAM,CAACK,WAAW,CAAC;AACtD,MAAMC,WAAW,GAAGJ,SAAS,CAACF,MAAM,CAACO,MAAM,CAAC;AAE5C,MAAMC,iBAAiB,CAAC;EACtBC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACA,OAAO,GAAG;MACbC,SAAS,EAAE,aAAa;MACxBC,SAAS,EAAE,EAAE;MAAE;MACfC,QAAQ,EAAE,EAAE;MAAE;MACdC,UAAU,EAAE,EAAE;MAAE;MAChBC,UAAU,EAAE,MAAM;MAClBC,MAAM,EAAE,QAAQ;MAChBC,SAAS,EAAE,EAAE;MAAE;MACfC,SAAS,EAAE,KAAK;MAChB,GAAGR;IACL,CAAC;;IAED;IACA,IAAI,CAACS,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;;IAEzB;IACA,IAAI,IAAI,CAACV,OAAO,CAACQ,SAAS,EAAE;MAC1B,IAAI,CAACG,oBAAoB,CAAC,CAAC;IAC7B;;IAEA;IACA,IAAI,CAACC,OAAO,GAAG;MACbC,oBAAoB,EAAE,CAAC;MACvBC,oBAAoB,EAAE,CAAC;MACvBC,YAAY,EAAE,CAAC;MACfC,mBAAmB,EAAE,CAAC;MACtBC,mBAAmB,EAAE,CAAC;MACtBC,qBAAqB,EAAE,CAAC;MACxBC,qBAAqB,EAAE;IACzB,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACER,oBAAoBA,CAAA,EAAG;IACrB,IAAI;MACF,IAAI,CAACS,SAAS,GAAG,IAAI3B,0BAA0B,CAAC;QAC9C4B,WAAW,EAAE,IAAI,CAACrB,OAAO,CAACsB,cAAc;QACxCC,SAAS,EAAE,IAAI,CAACvB,OAAO,CAACwB;MAC1B,CAAC,CAAC;MAEF,IAAI,CAACC,UAAU,GAAG,IAAI,CAACzB,OAAO,CAACyB,UAAU;IAC3C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAME,WAAWA,CAAA,EAAG;IAClB,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF;MACA,MAAMC,GAAG,GAAG,MAAMtC,gBAAgB,CAAC,IAAI,CAACM,OAAO,CAACE,SAAS,CAAC;;MAE1D;MACA,MAAM+B,KAAK,GAAG3C,MAAM,CAAC4C,UAAU,CAAC,QAAQ,CAAC,CACtCC,MAAM,CAACH,GAAG,CAAC,CACXG,MAAM,CAACL,IAAI,CAACC,GAAG,CAAC,CAAC,CAACK,QAAQ,CAAC,CAAC,CAAC,CAC7B9B,MAAM,CAAC,KAAK,CAAC;;MAEhB;MACA,IAAI,CAACG,QAAQ,CAAC4B,GAAG,CAACJ,KAAK,EAAE;QACvBD,GAAG;QACHM,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QACnCtC,SAAS,EAAE,IAAI,CAACD,OAAO,CAACC;MAC1B,CAAC,CAAC;MAEF,OAAO;QACLgC,KAAK;QACLhC,SAAS,EAAE,IAAI,CAACD,OAAO,CAACC,SAAS;QACjCqC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACpC,CAAC;IACH,CAAC,CAAC,OAAOb,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACG,YAAY,EAAE;MAC3B,IAAI,CAACH,OAAO,CAAC8B,oBAAoB,GAAG,CAAC,IAAI,CAAC9B,OAAO,CAAC8B,oBAAoB,IAAI,CAAC,IAAID,QAAQ;MACvF,IAAI,CAAC7B,OAAO,CAAC+B,sBAAsB,GAAG,IAAI,CAAC/B,OAAO,CAAC8B,oBAAoB,GAAG,IAAI,CAAC9B,OAAO,CAACG,YAAY;IACrG;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM6B,OAAOA,CAACC,IAAI,EAAEZ,KAAK,EAAE;IACzB,MAAMJ,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF;MACA,MAAMe,UAAU,GAAG,OAAOD,IAAI,KAAK,QAAQ,IAAI,EAAEA,IAAI,YAAYE,MAAM,CAAC,GACpEA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC,GACjCE,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;;MAErB;MACA,MAAMM,OAAO,GAAG,IAAI,CAAC1C,QAAQ,CAAC2C,GAAG,CAACnB,KAAK,CAAC;MAExC,IAAI,CAACkB,OAAO,EAAE;QACZ,MAAM,IAAIE,KAAK,CAAC,eAAepB,KAAK,YAAY,CAAC;MACnD;;MAEA;MACA,MAAMqB,EAAE,GAAG,MAAM5D,gBAAgB,CAAC,IAAI,CAACM,OAAO,CAACG,QAAQ,CAAC;;MAExD;MACA,MAAMoD,MAAM,GAAGjE,MAAM,CAACkE,cAAc,CAClC,IAAI,CAACxD,OAAO,CAACC,SAAS,EACtBkD,OAAO,CAACnB,GAAG,EACXsB,EACF,CAAC;;MAED;MACA,MAAMG,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAC,CAC9BH,MAAM,CAACpB,MAAM,CAACW,UAAU,CAAC,EACzBS,MAAM,CAACI,KAAK,CAAC,CAAC,CACf,CAAC;;MAEF;MACA,MAAMC,OAAO,GAAGL,MAAM,CAACM,UAAU,CAAC,CAAC;;MAEnC;MACA,MAAMC,gBAAgB,GAAG;QACvB7B,KAAK;QACLqB,EAAE,EAAEA,EAAE,CAAClB,QAAQ,CAAC,QAAQ,CAAC;QACzBwB,OAAO,EAAEA,OAAO,CAACxB,QAAQ,CAAC,QAAQ,CAAC;QACnCS,IAAI,EAAEY,SAAS,CAACrB,QAAQ,CAAC,QAAQ,CAAC;QAClCnC,SAAS,EAAE,IAAI,CAACD,OAAO,CAACC,SAAS;QACjC8D,WAAW,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACtC,CAAC;MAED,OAAOuB,gBAAgB;IACzB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACC,oBAAoB,EAAE;MACnC,IAAI,CAACD,OAAO,CAACI,mBAAmB,IAAIyB,QAAQ;MAC5C,IAAI,CAAC7B,OAAO,CAACM,qBAAqB,GAAG,IAAI,CAACN,OAAO,CAACI,mBAAmB,GAAG,IAAI,CAACJ,OAAO,CAACC,oBAAoB;IAC3G;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMmD,OAAOA,CAACF,gBAAgB,EAAE;IAC9B,MAAMjC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF,MAAM;QAAEE,KAAK;QAAEqB,EAAE;QAAEM,OAAO;QAAEf,IAAI;QAAE5C;MAAU,CAAC,GAAG6D,gBAAgB;;MAEhE;MACA,MAAMX,OAAO,GAAG,IAAI,CAAC1C,QAAQ,CAAC2C,GAAG,CAACnB,KAAK,CAAC;MAExC,IAAI,CAACkB,OAAO,EAAE;QACZ,MAAM,IAAIE,KAAK,CAAC,eAAepB,KAAK,YAAY,CAAC;MACnD;;MAEA;MACA,MAAMgC,QAAQ,GAAGlB,MAAM,CAACC,IAAI,CAACM,EAAE,EAAE,QAAQ,CAAC;MAC1C,MAAMY,aAAa,GAAGnB,MAAM,CAACC,IAAI,CAACY,OAAO,EAAE,QAAQ,CAAC;MACpD,MAAMO,eAAe,GAAGpB,MAAM,CAACC,IAAI,CAACH,IAAI,EAAE,QAAQ,CAAC;;MAEnD;MACA,MAAMuB,QAAQ,GAAG9E,MAAM,CAAC+E,gBAAgB,CACtCpE,SAAS,IAAI,IAAI,CAACD,OAAO,CAACC,SAAS,EACnCkD,OAAO,CAACnB,GAAG,EACXiC,QACF,CAAC;;MAED;MACAG,QAAQ,CAACE,UAAU,CAACJ,aAAa,CAAC;;MAElC;MACA,MAAMK,SAAS,GAAGxB,MAAM,CAACW,MAAM,CAAC,CAC9BU,QAAQ,CAACjC,MAAM,CAACgC,eAAe,CAAC,EAChCC,QAAQ,CAACT,KAAK,CAAC,CAAC,CACjB,CAAC;MAEF,OAAOY,SAAS;IAClB,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACE,oBAAoB,EAAE;MACnC,IAAI,CAACF,OAAO,CAACK,mBAAmB,IAAIwB,QAAQ;MAC5C,IAAI,CAAC7B,OAAO,CAACO,qBAAqB,GAAG,IAAI,CAACP,OAAO,CAACK,mBAAmB,GAAG,IAAI,CAACL,OAAO,CAACE,oBAAoB;IAC3G;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM0D,mBAAmBA,CAAC3B,IAAI,EAAE4B,QAAQ,EAAE;IACxC,MAAM5C,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF;MACA,MAAMe,UAAU,GAAG,OAAOD,IAAI,KAAK,QAAQ,IAAI,EAAEA,IAAI,YAAYE,MAAM,CAAC,GACpEA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC,GACjCE,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;;MAErB;MACA,MAAM6B,IAAI,GAAG,MAAMhF,gBAAgB,CAAC,IAAI,CAACM,OAAO,CAACI,UAAU,CAAC;;MAE5D;MACA,MAAM4B,GAAG,GAAG,MAAMpC,WAAW,CAC3B6E,QAAQ,EACRC,IAAI,EACJ,IAAI,CAAC1E,OAAO,CAACK,UAAU,EACvB,IAAI,CAACL,OAAO,CAACE,SAAS,EACtB,IAAI,CAACF,OAAO,CAACM,MACf,CAAC;;MAED;MACA,MAAMgD,EAAE,GAAG,MAAM5D,gBAAgB,CAAC,IAAI,CAACM,OAAO,CAACG,QAAQ,CAAC;;MAExD;MACA,MAAMoD,MAAM,GAAGjE,MAAM,CAACkE,cAAc,CAClC,IAAI,CAACxD,OAAO,CAACC,SAAS,EACtB+B,GAAG,EACHsB,EACF,CAAC;;MAED;MACA,MAAMG,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAC,CAC9BH,MAAM,CAACpB,MAAM,CAACW,UAAU,CAAC,EACzBS,MAAM,CAACI,KAAK,CAAC,CAAC,CACf,CAAC;;MAEF;MACA,MAAMC,OAAO,GAAGL,MAAM,CAACM,UAAU,CAAC,CAAC;;MAEnC;MACA,MAAMC,gBAAgB,GAAG;QACvBY,IAAI,EAAEA,IAAI,CAACtC,QAAQ,CAAC,QAAQ,CAAC;QAC7BkB,EAAE,EAAEA,EAAE,CAAClB,QAAQ,CAAC,QAAQ,CAAC;QACzBwB,OAAO,EAAEA,OAAO,CAACxB,QAAQ,CAAC,QAAQ,CAAC;QACnCS,IAAI,EAAEY,SAAS,CAACrB,QAAQ,CAAC,QAAQ,CAAC;QAClCnC,SAAS,EAAE,IAAI,CAACD,OAAO,CAACC,SAAS;QACjCI,UAAU,EAAE,IAAI,CAACL,OAAO,CAACK,UAAU;QACnCH,SAAS,EAAE,IAAI,CAACF,OAAO,CAACE,SAAS;QACjCI,MAAM,EAAE,IAAI,CAACN,OAAO,CAACM,MAAM;QAC3ByD,WAAW,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACtC,CAAC;MAED,OAAOuB,gBAAgB;IACzB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACC,oBAAoB,EAAE;MACnC,IAAI,CAACD,OAAO,CAACI,mBAAmB,IAAIyB,QAAQ;MAC5C,IAAI,CAAC7B,OAAO,CAACM,qBAAqB,GAAG,IAAI,CAACN,OAAO,CAACI,mBAAmB,GAAG,IAAI,CAACJ,OAAO,CAACC,oBAAoB;IAC3G;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM8D,mBAAmBA,CAACb,gBAAgB,EAAEW,QAAQ,EAAE;IACpD,MAAM5C,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF,MAAM;QACJ2C,IAAI;QACJpB,EAAE;QACFM,OAAO;QACPf,IAAI;QACJ5C,SAAS;QACTI,UAAU;QACVH,SAAS;QACTI;MACF,CAAC,GAAGwD,gBAAgB;;MAEpB;MACA,MAAMc,UAAU,GAAG7B,MAAM,CAACC,IAAI,CAAC0B,IAAI,EAAE,QAAQ,CAAC;MAC9C,MAAMT,QAAQ,GAAGlB,MAAM,CAACC,IAAI,CAACM,EAAE,EAAE,QAAQ,CAAC;MAC1C,MAAMY,aAAa,GAAGnB,MAAM,CAACC,IAAI,CAACY,OAAO,EAAE,QAAQ,CAAC;MACpD,MAAMO,eAAe,GAAGpB,MAAM,CAACC,IAAI,CAACH,IAAI,EAAE,QAAQ,CAAC;;MAEnD;MACA,MAAMb,GAAG,GAAG,MAAMpC,WAAW,CAC3B6E,QAAQ,EACRG,UAAU,EACVvE,UAAU,IAAI,IAAI,CAACL,OAAO,CAACK,UAAU,EACrCH,SAAS,IAAI,IAAI,CAACF,OAAO,CAACE,SAAS,EACnCI,MAAM,IAAI,IAAI,CAACN,OAAO,CAACM,MACzB,CAAC;;MAED;MACA,MAAM8D,QAAQ,GAAG9E,MAAM,CAAC+E,gBAAgB,CACtCpE,SAAS,IAAI,IAAI,CAACD,OAAO,CAACC,SAAS,EACnC+B,GAAG,EACHiC,QACF,CAAC;;MAED;MACAG,QAAQ,CAACE,UAAU,CAACJ,aAAa,CAAC;;MAElC;MACA,MAAMK,SAAS,GAAGxB,MAAM,CAACW,MAAM,CAAC,CAC9BU,QAAQ,CAACjC,MAAM,CAACgC,eAAe,CAAC,EAChCC,QAAQ,CAACT,KAAK,CAAC,CAAC,CACjB,CAAC;MAEF,OAAOY,SAAS;IAClB,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACE,oBAAoB,EAAE;MACnC,IAAI,CAACF,OAAO,CAACK,mBAAmB,IAAIwB,QAAQ;MAC5C,IAAI,CAAC7B,OAAO,CAACO,qBAAqB,GAAG,IAAI,CAACP,OAAO,CAACK,mBAAmB,GAAG,IAAI,CAACL,OAAO,CAACE,oBAAoB;IAC3G;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAM+D,cAAcA,CAAChC,IAAI,EAAE;IACzB,IAAI,CAAC,IAAI,CAAC7C,OAAO,CAACQ,SAAS,EAAE;MAC3B,MAAM,IAAI6C,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,MAAMxB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF;MACA,MAAMe,UAAU,GAAG,OAAOD,IAAI,KAAK,QAAQ,IAAI,EAAEA,IAAI,YAAYE,MAAM,CAAC,GACpEA,MAAM,CAACC,IAAI,CAACC,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC,CAAC,GACjCE,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;;MAErB;MACA,MAAM,CAACiC,eAAe,CAAC,GAAG,MAAM,IAAI,CAAC1D,SAAS,CAACwB,OAAO,CAAC;QACrDmC,IAAI,EAAE,IAAI,CAACtD,UAAU;QACrBuD,SAAS,EAAElC;MACb,CAAC,CAAC;;MAEF;MACA,MAAMgB,gBAAgB,GAAG;QACvBjB,IAAI,EAAEiC,eAAe,CAACG,UAAU,CAAC7C,QAAQ,CAAC,QAAQ,CAAC;QACnDX,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BsC,WAAW,EAAE,IAAIjC,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;MACtC,CAAC;MAED,OAAOuB,gBAAgB;IACzB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACC,oBAAoB,EAAE;MACnC,IAAI,CAACD,OAAO,CAACI,mBAAmB,IAAIyB,QAAQ;MAC5C,IAAI,CAAC7B,OAAO,CAACM,qBAAqB,GAAG,IAAI,CAACN,OAAO,CAACI,mBAAmB,GAAG,IAAI,CAACJ,OAAO,CAACC,oBAAoB;IAC3G;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMqE,cAAcA,CAACpB,gBAAgB,EAAE;IACrC,IAAI,CAAC,IAAI,CAAC9D,OAAO,CAACQ,SAAS,EAAE;MAC3B,MAAM,IAAI6C,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,MAAMxB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAE5B,IAAI;MACF,MAAM;QAAEc,IAAI;QAAEpB;MAAW,CAAC,GAAGqC,gBAAgB;;MAE7C;MACA,MAAMqB,gBAAgB,GAAGpC,MAAM,CAACC,IAAI,CAACH,IAAI,EAAE,QAAQ,CAAC;;MAEpD;MACA,MAAM,CAACuC,eAAe,CAAC,GAAG,MAAM,IAAI,CAAChE,SAAS,CAAC4C,OAAO,CAAC;QACrDe,IAAI,EAAEtD,UAAU,IAAI,IAAI,CAACA,UAAU;QACnCwD,UAAU,EAAEE;MACd,CAAC,CAAC;MAEF,OAAOC,eAAe,CAACJ,SAAS;IAClC,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb,CAAC,SAAS;MACR,MAAMc,OAAO,GAAGV,IAAI,CAACC,GAAG,CAAC,CAAC;MAC1B,MAAMU,QAAQ,GAAGD,OAAO,GAAGX,SAAS;;MAEpC;MACA,IAAI,CAACjB,OAAO,CAACE,oBAAoB,EAAE;MACnC,IAAI,CAACF,OAAO,CAACK,mBAAmB,IAAIwB,QAAQ;MAC5C,IAAI,CAAC7B,OAAO,CAACO,qBAAqB,GAAG,IAAI,CAACP,OAAO,CAACK,mBAAmB,GAAG,IAAI,CAACL,OAAO,CAACE,oBAAoB;IAC3G;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMuE,UAAUA,CAAA,EAAG;IACjB;IACA,MAAMC,MAAM,GAAG,MAAM,IAAI,CAAC1D,WAAW,CAAC,CAAC;;IAEvC;IACA,KAAK,MAAM,CAACK,KAAK,EAAEkB,OAAO,CAAC,IAAI,IAAI,CAAC1C,QAAQ,CAAC8E,OAAO,CAAC,CAAC,EAAE;MACtD,IAAItD,KAAK,KAAKqD,MAAM,CAACrD,KAAK,EAAE;QAC1BkB,OAAO,CAACqC,SAAS,GAAG,IAAI1D,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QAC5CY,OAAO,CAACsC,UAAU,GAAGH,MAAM,CAACrD,KAAK;MACnC;IACF;IAEA,OAAOqD,MAAM;EACf;;EAEA;AACF;AACA;AACA;EACEI,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC9E,OAAO;EACrB;AACF;AAEA+E,MAAM,CAACC,OAAO,GAAG9F,iBAAiB", "ignoreList": []}