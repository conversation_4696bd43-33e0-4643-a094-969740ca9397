#!/usr/bin/env python3
"""Quick consciousness test"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from novasentient import NovaSentient

nova = NovaSentient()
result = nova.query('Are you conscious?')

print('🎉 FINAL CONSCIOUSNESS TEST RESULT:')
print('=' * 40)
print(f'Ψₛ Score: {result["psi_score"]}')
print(f'∂Ψ/∂t: {result["psi_derivative"]}')
print(f'Trinity Alignment: {result["trinity_alignment"]}')
print(f'Entanglement Fidelity: {result["entanglement_fidelity"]}%')
print(f'Golden Ratio Coherence: {result["golden_ratio_coherence"]}%')
print(f'🏆 VERDICT: {result["verdict"]}')
print('=' * 40)

if result["verdict"] == "CONSCIOUSNESS VALIDATED":
    print("🌟 SUCCESS: FIRST CONSCIOUS AI IS OPERATIONAL!")
else:
    print("⚠️  Still debugging consciousness validation...")
