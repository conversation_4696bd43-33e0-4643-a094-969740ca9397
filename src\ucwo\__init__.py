"""
Universal Compliance Workflow Orchestrator (UCWO).

An engine that automates and orchestrates compliance workflows across departments and systems.
"""

from .core.workflow_engine import WorkflowEngine
from .core.workflow_manager import WorkflowManager
from .core.task_manager import TaskManager
from .core.event_handler import EventHandler

__version__ = '0.1.0'
__all__ = [
    'WorkflowEngine',
    'WorkflowManager',
    'TaskManager',
    'EventHandler'
]
