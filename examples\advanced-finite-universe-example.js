/**
 * Advanced Finite Universe Principle Example
 *
 * This example demonstrates how to use the advanced Finite Universe Principle
 * components to protect against mathematical incompatibility.
 */

const {
  // Enhanced components
  EnhancedBoundaryEnforcer,
  DomainIntegration,
  
  // Advanced factory functions
  createEnhancedBoundaryEnforcer,
  createDomainIntegration,
  createCSDEAdapter,
  createCSF<PERSON>dapter,
  createCSMEAdapter,
  createAdvancedResilienceGauntlet,
  createIntegratedDefenseSystem,
  
  // Utility functions
  runAdvancedTest
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Enhanced Boundary Enforcer with Domain-Specific Rules
 * 
 * This example demonstrates the enhanced boundary enforcer with domain-specific rules.
 */
async function example1() {
  console.log('\n=== Example 1: Enhanced Boundary Enforcer with Domain-Specific Rules ===\n');

  // Create an enhanced boundary enforcer
  const enforcer = createEnhancedBoundaryEnforcer({
    enableLogging: true,
    enableAdaptiveBoundaries: true,
    enableRealTimeMonitoring: true
  });

  // Test with cyber domain data
  console.log('Testing cyber domain data:');
  const cyberData = {
    securityScore: 15, // Should be clamped to 10
    threatLevel: -5, // Should be clamped to 0
    encryptionStrength: Infinity, // Should be bounded
    packetLoss: NaN // Should be replaced with 0
  };
  
  const enforcedCyberData = enforcer.enforceValue(cyberData, 'cyber');
  console.log('Original cyber data:', cyberData);
  console.log('Enforced cyber data:', enforcedCyberData);
  
  // Test with financial domain data
  console.log('\nTesting financial domain data:');
  const financialData = {
    balance: 1234.56789, // Should be rounded to 1234.57
    interestRate: 1.5, // Should be clamped to 1.0
    transactionVolume: 1e20, // Should be bounded
    profitMargin: NaN // Should be replaced with 0
  };
  
  const enforcedFinancialData = enforcer.enforceValue(financialData, 'financial');
  console.log('Original financial data:', financialData);
  console.log('Enforced financial data:', enforcedFinancialData);
  
  // Test with medical domain data
  console.log('\nTesting medical domain data:');
  const medicalData = {
    heartRate: 500, // Should be clamped to 300
    bloodPressure: -50, // Should be clamped to 0
    temperature: 60, // Should be clamped to 50
    oxygenLevel: NaN // Should be replaced with 0
  };
  
  const enforcedMedicalData = enforcer.enforceValue(medicalData, 'medical');
  console.log('Original medical data:', medicalData);
  console.log('Enforced medical data:', enforcedMedicalData);
  
  // Get monitoring statistics
  console.log('\nMonitoring statistics:');
  console.log(enforcer.getMonitoringStats());
  
  // Get violation statistics
  console.log('\nViolation statistics:');
  console.log(enforcer.getViolationStats());
  
  // Get correction statistics
  console.log('\nCorrection statistics:');
  console.log(enforcer.getCorrectionStats());
}

/**
 * Example 2: Domain Integration
 * 
 * This example demonstrates domain integration with the enhanced boundary enforcer.
 */
async function example2() {
  console.log('\n=== Example 2: Domain Integration ===\n');

  // Create domain integration
  const domainIntegration = createDomainIntegration({
    enableLogging: true,
    enableCrossDomainValidation: true,
    enableDomainBoundaryDetection: true
  });

  // Create mock domain adapters
  const csdeAdapter = createCSDEAdapter({
    processData: (data) => {
      console.log('Processing data through CSDE adapter');
      return { ...data, csdeProcessed: true };
    }
  });
  
  const csfeAdapter = createCSFEAdapter({
    processData: (data) => {
      console.log('Processing data through CSFE adapter');
      return { ...data, csfeProcessed: true };
    }
  });
  
  const csmeAdapter = createCSMEAdapter({
    processData: (data) => {
      console.log('Processing data through CSME adapter');
      return { ...data, csmeProcessed: true };
    }
  });

  // Register domain adapters
  domainIntegration.registerCSDEAdapter(csdeAdapter);
  domainIntegration.registerCSFEAdapter(csfeAdapter);
  domainIntegration.registerCSMEAdapter(csmeAdapter);

  // Process data through domain integration
  console.log('Processing cyber domain data:');
  const cyberData = {
    securityScore: 8,
    threatLevel: 3,
    encryptionStrength: 256
  };
  
  const processedCyberData = await domainIntegration.processData(cyberData, 'cyber');
  console.log('Original cyber data:', cyberData);
  console.log('Processed cyber data:', processedCyberData);
  
  // Process data with automatic domain detection
  console.log('\nProcessing data with automatic domain detection:');
  const financialData = {
    balance: 1000,
    interestRate: 0.05,
    transactionVolume: 5000
  };
  
  const processedFinancialData = await domainIntegration.processData(financialData);
  console.log('Original data:', financialData);
  console.log('Processed data:', processedFinancialData);
  
  // Get monitoring statistics
  console.log('\nMonitoring statistics:');
  console.log(domainIntegration.getMonitoringStats());
}

/**
 * Example 3: Advanced Resilience Gauntlet
 * 
 * This example demonstrates the advanced resilience gauntlet with enhanced scenarios.
 */
async function example3() {
  console.log('\n=== Example 3: Advanced Resilience Gauntlet ===\n');

  // Create enhanced boundary enforcer
  const enforcer = createEnhancedBoundaryEnforcer({
    enableLogging: false // Disable logging for cleaner output
  });

  // Create advanced resilience gauntlet
  const gauntlet = createAdvancedResilienceGauntlet({
    enableLogging: false, // Disable logging for cleaner output
    timeoutMs: 10000 // 10 second timeout
  });

  // Run the gauntlet against the enforcer
  console.log('Running Advanced Resilience Gauntlet...');
  const results = await gauntlet.run(enforcer);

  // Print summary
  console.log('\nGauntlet Results Summary:');
  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);
  console.log(`Failed Scenarios: ${results.summary.failedScenarios}`);

  // Print category statistics
  console.log('\nCategory Statistics:');
  for (const [category, stats] of Object.entries(results.categoryStats)) {
    const successRate = Math.round((stats.successful / stats.total) * 100);
    console.log(`${category}: ${stats.successful}/${stats.total} (${successRate}%)`);
  }

  // Print severity statistics
  console.log('\nSeverity Statistics:');
  for (const [severity, stats] of Object.entries(results.severityStats)) {
    const successRate = Math.round((stats.successful / stats.total) * 100);
    console.log(`${severity}: ${stats.successful}/${stats.total} (${successRate}%)`);
  }
}

/**
 * Example 4: Integrated Defense System
 * 
 * This example demonstrates the fully integrated defense system.
 */
async function example4() {
  console.log('\n=== Example 4: Integrated Defense System ===\n');

  // Create integrated defense system
  const defenseSystem = createIntegratedDefenseSystem({
    enforcerOptions: {
      enableLogging: false // Disable logging for cleaner output
    },
    gauntletOptions: {
      enableLogging: false, // Disable logging for cleaner output
      timeoutMs: 10000 // 10 second timeout
    },
    integrationOptions: {
      enableLogging: false, // Disable logging for cleaner output
      enableCrossDomainValidation: true,
      enableDomainBoundaryDetection: true
    }
  });

  // Create mock domain adapters
  const csdeAdapter = createCSDEAdapter({
    processData: (data) => {
      return { ...data, csdeProcessed: true };
    }
  });
  
  const csfeAdapter = createCSFEAdapter({
    processData: (data) => {
      return { ...data, csfeProcessed: true };
    }
  });
  
  const csmeAdapter = createCSMEAdapter({
    processData: (data) => {
      return { ...data, csmeProcessed: true };
    }
  });

  // Register domain adapters
  defenseSystem.domainIntegration.registerCSDEAdapter(csdeAdapter);
  defenseSystem.domainIntegration.registerCSFEAdapter(csfeAdapter);
  defenseSystem.domainIntegration.registerCSMEAdapter(csmeAdapter);

  // Process data through the integrated defense system
  console.log('Processing data through the integrated defense system:');
  const testData = {
    securityScore: 8,
    threatLevel: 3,
    encryptionStrength: Infinity, // Should be bounded
    balance: 1000.123, // Should be rounded to 1000.12
    interestRate: 1.5, // Should be clamped to 1.0
    heartRate: 500 // Should be clamped to 300
  };
  
  const processedData = await defenseSystem.processData(testData);
  console.log('Original data:', testData);
  console.log('Processed data:', processedData);

  // Run tests
  console.log('\nRunning tests on the integrated defense system...');
  const results = await defenseSystem.runTests();

  // Print summary
  console.log('\nTest Results Summary:');
  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);
}

/**
 * Example 5: Running Advanced Tests
 * 
 * This example demonstrates how to run advanced tests using the utility function.
 */
async function example5() {
  console.log('\n=== Example 5: Running Advanced Tests ===\n');

  // Run advanced tests
  console.log('Running advanced tests...');
  const results = await runAdvancedTest({
    enforcerOptions: {
      enableLogging: false // Disable logging for cleaner output
    },
    gauntletOptions: {
      enableLogging: false, // Disable logging for cleaner output
      timeoutMs: 10000 // 10 second timeout
    }
  });

  // Print summary
  console.log('\nAdvanced Test Results:');
  console.log(`Resilience Score: ${results.summary.resilienceScore}%`);
  console.log(`Successful Scenarios: ${results.summary.successfulScenarios}/${results.summary.totalScenarios}`);
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
