/**
 * Anomaly Classifier
 *
 * This module provides anomaly classification capabilities for the Finite Universe
 * Principle defense system, enabling categorization of detected anomalies.
 */

const EventEmitter = require('events');

/**
 * AnomalyClassifier class
 * 
 * Provides anomaly classification capabilities for categorizing detected anomalies.
 */
class AnomalyClassifier extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      classificationInterval: options.classificationInterval || 30000, // 30 seconds
      severityLevels: options.severityLevels || ['low', 'medium', 'high', 'critical'],
      severityThresholds: options.severityThresholds || [3, 5, 8], // Z-score thresholds for severity levels
      anomalyCategories: options.anomalyCategories || [
        'spike', // Sudden increase
        'drop', // Sudden decrease
        'trend', // Gradual change
        'level_shift', // Permanent change in level
        'outlier', // Isolated anomaly
        'volatility', // Increased variance
        'seasonality_change', // Change in seasonal pattern
        'missing_seasonality', // Expected seasonal pattern not observed
        'boundary_violation', // Value exceeds boundary
        'cross_domain_anomaly' // Anomaly across multiple domains
      ],
      ...options
    };

    // Initialize anomaly history
    this.anomalyHistory = [];
    
    // Initialize classification results
    this.classificationResults = {
      byCategory: {},
      bySeverity: {},
      byDomain: {},
      recent: []
    };
    
    // Initialize classification interval
    this.classificationInterval = null;
    
    // Initialize anomaly categories
    this._initializeCategories();

    if (this.options.enableLogging) {
      console.log('AnomalyClassifier initialized with options:', this.options);
    }
  }

  /**
   * Initialize anomaly categories
   * @private
   */
  _initializeCategories() {
    // Initialize category counts
    for (const category of this.options.anomalyCategories) {
      this.classificationResults.byCategory[category] = 0;
    }
    
    // Initialize severity counts
    for (const severity of this.options.severityLevels) {
      this.classificationResults.bySeverity[severity] = 0;
    }
    
    // Initialize domain counts
    for (const domain of ['cyber', 'financial', 'medical']) {
      this.classificationResults.byDomain[domain] = 0;
    }
  }

  /**
   * Start anomaly classification
   */
  start() {
    if (this.classificationInterval) {
      return;
    }

    // Start classification interval
    this.classificationInterval = setInterval(() => {
      this.classifyAnomalies();
    }, this.options.classificationInterval);

    if (this.options.enableLogging) {
      console.log('AnomalyClassifier started');
    }
    
    // Emit start event
    this.emit('start');
  }

  /**
   * Stop anomaly classification
   */
  stop() {
    if (!this.classificationInterval) {
      return;
    }

    // Clear classification interval
    clearInterval(this.classificationInterval);
    this.classificationInterval = null;

    if (this.options.enableLogging) {
      console.log('AnomalyClassifier stopped');
    }
    
    // Emit stop event
    this.emit('stop');
  }

  /**
   * Process anomaly for classification
   * @param {Object} anomaly - Anomaly to process
   * @returns {Object} - Classification results
   */
  processAnomaly(anomaly) {
    // Add anomaly to history
    this._addToHistory(anomaly);
    
    // Classify anomaly
    const classification = this._classifyAnomaly(anomaly);
    
    // Update classification results
    this._updateClassificationResults(classification);
    
    // Emit classification event
    this.emit('classification', classification);
    
    return classification;
  }

  /**
   * Add anomaly to history
   * @param {Object} anomaly - Anomaly to add
   * @private
   */
  _addToHistory(anomaly) {
    // Add timestamp if not present
    if (!anomaly.timestamp) {
      anomaly.timestamp = new Date();
    }
    
    // Add anomaly to history
    this.anomalyHistory.push(anomaly);
    
    // Limit history size
    if (this.anomalyHistory.length > 1000) {
      this.anomalyHistory.shift();
    }
  }

  /**
   * Classify anomaly
   * @param {Object} anomaly - Anomaly to classify
   * @returns {Object} - Classification results
   * @private
   */
  _classifyAnomaly(anomaly) {
    // Determine severity
    const severity = this._determineSeverity(anomaly);
    
    // Determine category
    const category = this._determineCategory(anomaly);
    
    // Determine domain
    const domain = anomaly.domain || 'unknown';
    
    // Create classification
    const classification = {
      anomaly,
      severity,
      category,
      domain,
      timestamp: anomaly.timestamp || new Date()
    };
    
    return classification;
  }

  /**
   * Determine severity of anomaly
   * @param {Object} anomaly - Anomaly to classify
   * @returns {string} - Severity level
   * @private
   */
  _determineSeverity(anomaly) {
    // Get Z-score if available
    const zScore = anomaly.zScore || 0;
    
    // Determine severity based on Z-score
    if (zScore >= this.options.severityThresholds[2]) {
      return this.options.severityLevels[3]; // critical
    } else if (zScore >= this.options.severityThresholds[1]) {
      return this.options.severityLevels[2]; // high
    } else if (zScore >= this.options.severityThresholds[0]) {
      return this.options.severityLevels[1]; // medium
    } else {
      return this.options.severityLevels[0]; // low
    }
  }

  /**
   * Determine category of anomaly
   * @param {Object} anomaly - Anomaly to classify
   * @returns {string} - Anomaly category
   * @private
   */
  _determineCategory(anomaly) {
    // Check for boundary violation
    if (anomaly.boundaryViolation) {
      return 'boundary_violation';
    }
    
    // Check for cross-domain anomaly
    if (anomaly.crossDomain) {
      return 'cross_domain_anomaly';
    }
    
    // Check for spike or drop
    if (anomaly.value !== undefined && anomaly.mean !== undefined) {
      const difference = anomaly.value - anomaly.mean;
      
      if (Math.abs(difference) > 3 * (anomaly.stdDev || 1)) {
        return difference > 0 ? 'spike' : 'drop';
      }
    }
    
    // Check for trend
    if (anomaly.trend) {
      return 'trend';
    }
    
    // Check for level shift
    if (anomaly.levelShift) {
      return 'level_shift';
    }
    
    // Check for volatility
    if (anomaly.volatility) {
      return 'volatility';
    }
    
    // Check for seasonality change
    if (anomaly.seasonalityChange) {
      return 'seasonality_change';
    }
    
    // Check for missing seasonality
    if (anomaly.missingSeasonality) {
      return 'missing_seasonality';
    }
    
    // Default to outlier
    return 'outlier';
  }

  /**
   * Update classification results
   * @param {Object} classification - Classification results
   * @private
   */
  _updateClassificationResults(classification) {
    // Update category count
    if (this.classificationResults.byCategory[classification.category] !== undefined) {
      this.classificationResults.byCategory[classification.category]++;
    }
    
    // Update severity count
    if (this.classificationResults.bySeverity[classification.severity] !== undefined) {
      this.classificationResults.bySeverity[classification.severity]++;
    }
    
    // Update domain count
    if (this.classificationResults.byDomain[classification.domain] !== undefined) {
      this.classificationResults.byDomain[classification.domain]++;
    }
    
    // Update recent classifications
    this.classificationResults.recent.unshift(classification);
    
    // Limit recent classifications
    if (this.classificationResults.recent.length > 10) {
      this.classificationResults.recent.pop();
    }
  }

  /**
   * Classify all anomalies in history
   */
  classifyAnomalies() {
    if (this.anomalyHistory.length === 0) {
      if (this.options.enableLogging) {
        console.log('No anomalies to classify');
      }
      return;
    }
    
    // Reset classification results
    this._initializeCategories();
    this.classificationResults.recent = [];
    
    // Classify each anomaly
    for (const anomaly of this.anomalyHistory) {
      const classification = this._classifyAnomaly(anomaly);
      this._updateClassificationResults(classification);
    }
    
    // Emit classification-complete event
    this.emit('classification-complete', { ...this.classificationResults });
    
    if (this.options.enableLogging) {
      console.log('Anomalies classified');
    }
  }

  /**
   * Get classification results
   * @returns {Object} - Classification results
   */
  getClassificationResults() {
    return { ...this.classificationResults };
  }

  /**
   * Get anomaly history
   * @returns {Array} - Anomaly history
   */
  getAnomalyHistory() {
    return [...this.anomalyHistory];
  }

  /**
   * Dispose resources
   */
  dispose() {
    this.stop();
    
    // Clear history
    this.anomalyHistory = [];
    
    if (this.options.enableLogging) {
      console.log('AnomalyClassifier disposed');
    }
  }
}

/**
 * Create an anomaly classifier with recommended settings
 * @param {Object} options - Configuration options
 * @returns {AnomalyClassifier} - Configured anomaly classifier
 */
function createAnomalyClassifier(options = {}) {
  return new AnomalyClassifier({
    enableLogging: true,
    classificationInterval: 30000,
    severityLevels: ['low', 'medium', 'high', 'critical'],
    severityThresholds: [3, 5, 8],
    ...options
  });
}

module.exports = {
  AnomalyClassifier,
  createAnomalyClassifier
};
