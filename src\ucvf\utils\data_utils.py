"""
Data utilities for the Universal Compliance Visualization Framework.

This module provides utility functions for handling compliance data.
"""

import json
import logging
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_compliance_data(file_path: str) -> Dict[str, Any]:
    """
    Load compliance data from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The compliance data
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Loaded compliance data from {file_path}")
        return data
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {file_path}")
        raise

def save_visualization(visualization: Dict[str, Any], file_path: str) -> None:
    """
    Save a visualization to a JSON file.
    
    Args:
        visualization: The visualization data
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(visualization, f, indent=2)
        
        logger.info(f"Saved visualization to {file_path}")
    except IOError:
        logger.error(f"Failed to save visualization to {file_path}")
        raise

def merge_data(base_data: Dict[str, Any], overlay_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge two data dictionaries, with overlay_data taking precedence.
    
    Args:
        base_data: The base data
        overlay_data: The overlay data
        
    Returns:
        The merged data
    """
    merged_data = base_data.copy()
    
    for key, value in overlay_data.items():
        if key in merged_data and isinstance(merged_data[key], dict) and isinstance(value, dict):
            merged_data[key] = merge_data(merged_data[key], value)
        else:
            merged_data[key] = value
    
    return merged_data

def filter_sensitive_data(data: Dict[str, Any], sensitive_fields: Optional[list] = None) -> Dict[str, Any]:
    """
    Filter sensitive data from a data dictionary.
    
    Args:
        data: The data to filter
        sensitive_fields: List of sensitive field names to remove
        
    Returns:
        The filtered data
    """
    if sensitive_fields is None:
        sensitive_fields = ['credentials', 'personal_data', 'raw_evidence']
    
    filtered_data = data.copy()
    
    for field in sensitive_fields:
        if field in filtered_data:
            del filtered_data[field]
    
    # Recursively filter nested dictionaries
    for key, value in filtered_data.items():
        if isinstance(value, dict):
            filtered_data[key] = filter_sensitive_data(value, sensitive_fields)
        elif isinstance(value, list):
            filtered_data[key] = [
                filter_sensitive_data(item, sensitive_fields) if isinstance(item, dict) else item
                for item in value
            ]
    
    return filtered_data
