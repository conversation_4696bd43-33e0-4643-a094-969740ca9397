import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import CrossFrameworkMapping from '../../components/CrossFrameworkMapping';

// Mock the mapping service
jest.mock('../../services/mappingService', () => ({
  getAllFrameworks: jest.fn(),
  getFrameworkToFrameworkMappings: jest.fn()
}));

// Import the mocked service
const mappingService = require('../../services/mappingService');
const { getAllFrameworks, getFrameworkToFrameworkMappings } = mappingService;

describe('CrossFrameworkMapping', () => {
  it('renders the component with default frameworks', async () => {
    // Ensure the mock functions are properly set up
    getAllFrameworks.mockResolvedValue({
      'gdpr': { name: 'GDPR', description: 'General Data Protection Regulation' },
      'hipaa': { name: 'HIPAA', description: 'Health Insurance Portability and Accountability Act' }
    });

    getFrameworkToFrameworkMappings.mockResolvedValue({
      sourceFramework: 'gdpr',
      targetFramework: 'hipaa',
      mappings: {
        'Data Protection by Design': {
          targetControls: ['Security Management Process'],
          confidence: 0.85,
          mappingStrength: 'full'
        }
      }
    });

    render(<CrossFrameworkMapping />);

    // Check if the component title is displayed
    expect(screen.getByText('Cross-Framework Mapping')).toBeInTheDocument();

    // Wait for frameworks to be loaded
    await waitFor(() => {
      // Check if framework selectors are displayed with options
      const sourceFrameworkSelect = screen.getByLabelText('Source Framework');
      const targetFrameworkSelect = screen.getByLabelText('Target Framework');

      expect(sourceFrameworkSelect).toBeInTheDocument();
      expect(targetFrameworkSelect).toBeInTheDocument();

      // Check if mappings are displayed
      expect(screen.getByText(/Data Protection by Design/i)).toBeInTheDocument();
      expect(screen.getByText(/Security Management Process/i)).toBeInTheDocument();
    });
  });

  it('changes mappings when frameworks are changed', async () => {
    // Set up mock responses for different framework combinations
    getAllFrameworks.mockResolvedValue({
      'gdpr': { name: 'GDPR', description: 'General Data Protection Regulation' },
      'hipaa': { name: 'HIPAA', description: 'Health Insurance Portability and Accountability Act' },
      'nist': { name: 'NIST', description: 'National Institute of Standards and Technology' }
    });

    // Mock implementation for getFrameworkToFrameworkMappings
    getFrameworkToFrameworkMappings.mockImplementation((source, target) => {
      // First call with default frameworks (gdpr, hipaa)
      if (source === 'gdpr' && target === 'hipaa') {
        return Promise.resolve({
          sourceFramework: 'gdpr',
          targetFramework: 'hipaa',
          mappings: {
            'Data Protection by Design': {
              targetControls: ['Security Management Process'],
              confidence: 0.85,
              mappingStrength: 'full'
            }
          }
        });
      }

      // Second call with changed frameworks (hipaa, nist)
      if (source === 'hipaa' && target === 'nist') {
        return Promise.resolve({
          sourceFramework: 'hipaa',
          targetFramework: 'nist',
          mappings: {
            'Security Management Process': {
              targetControls: ['Risk Assessment'],
              confidence: 0.75,
              mappingStrength: 'partial'
            }
          }
        });
      }

      // Default fallback
      return Promise.resolve({
        sourceFramework: source,
        targetFramework: target,
        mappings: {}
      });
    });

    render(<CrossFrameworkMapping />);

    // Wait for initial mappings to be loaded
    await waitFor(() => {
      expect(screen.getByText(/Data Protection by Design/i)).toBeInTheDocument();
    });

    // Change source framework to HIPAA and target to NIST
    const sourceFrameworkSelect = screen.getByLabelText('Source Framework');
    const targetFrameworkSelect = screen.getByLabelText('Target Framework');

    fireEvent.change(sourceFrameworkSelect, { target: { value: 'hipaa' } });
    fireEvent.change(targetFrameworkSelect, { target: { value: 'nist' } });

    // Wait for new mappings to be displayed
    await waitFor(() => {
      expect(screen.getByText(/Security Management Process/i)).toBeInTheDocument();
      expect(screen.getByText(/Risk Assessment/i)).toBeInTheDocument();
    });

    // Verify that the service was called with the correct frameworks
    expect(getFrameworkToFrameworkMappings).toHaveBeenCalledWith('hipaa', 'nist');
  });
});
