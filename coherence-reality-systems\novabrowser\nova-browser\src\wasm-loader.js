// /nova-browser/src/wasm-loader.js - Following Exact Guide
import init, { NovaAgent } from './pkg/nova_agent.js';

async function loadWASM() {
    await init();
    const agent = new NovaAgent();
    
    // Hook into DOM parsing
    document.addEventListener('DOMContentLoaded', () => {
        const scanResult = agent.scan_html(document.documentElement.outerHTML);
        console.log("Ψ-Scan Complete:", scanResult);
    });
}

loadWASM();
