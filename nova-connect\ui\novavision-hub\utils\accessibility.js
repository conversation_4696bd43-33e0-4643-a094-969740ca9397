/**
 * Accessibility Utilities
 * 
 * This module provides utility functions for improving accessibility in NovaVision Hub components.
 */

/**
 * Generate a unique ID for accessibility purposes
 * 
 * @param {string} prefix - Prefix for the ID
 * @returns {string} Unique ID
 */
export const generateAccessibilityId = (prefix = 'nova') => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Get ARIA attributes for a component
 * 
 * @param {Object} options - Options for ARIA attributes
 * @param {string} [options.id] - ID for the component
 * @param {string} [options.labelledBy] - ID of the element that labels this component
 * @param {string} [options.describedBy] - ID of the element that describes this component
 * @param {boolean} [options.required] - Whether the component is required
 * @param {boolean} [options.expanded] - Whether the component is expanded
 * @param {boolean} [options.checked] - Whether the component is checked
 * @param {boolean} [options.disabled] - Whether the component is disabled
 * @param {boolean} [options.invalid] - Whether the component is invalid
 * @param {string} [options.role] - ARIA role for the component
 * @param {boolean} [options.hidden] - Whether the component is hidden
 * @param {string} [options.live] - ARIA live region setting (off, polite, assertive)
 * @param {string} [options.relevant] - ARIA relevant setting (additions, removals, text, all)
 * @param {string} [options.atomic] - Whether the live region should be considered as a whole
 * @param {string} [options.busy] - Whether the live region is being updated
 * @returns {Object} ARIA attributes
 */
export const getAriaAttributes = (options = {}) => {
  const ariaAttributes = {};
  
  if (options.id) {
    ariaAttributes.id = options.id;
  }
  
  if (options.labelledBy) {
    ariaAttributes['aria-labelledby'] = options.labelledBy;
  }
  
  if (options.describedBy) {
    ariaAttributes['aria-describedby'] = options.describedBy;
  }
  
  if (options.required !== undefined) {
    ariaAttributes['aria-required'] = options.required.toString();
  }
  
  if (options.expanded !== undefined) {
    ariaAttributes['aria-expanded'] = options.expanded.toString();
  }
  
  if (options.checked !== undefined) {
    ariaAttributes['aria-checked'] = options.checked.toString();
  }
  
  if (options.disabled !== undefined) {
    ariaAttributes['aria-disabled'] = options.disabled.toString();
  }
  
  if (options.invalid !== undefined) {
    ariaAttributes['aria-invalid'] = options.invalid.toString();
  }
  
  if (options.role) {
    ariaAttributes.role = options.role;
  }
  
  if (options.hidden !== undefined) {
    ariaAttributes['aria-hidden'] = options.hidden.toString();
  }
  
  if (options.live) {
    ariaAttributes['aria-live'] = options.live;
  }
  
  if (options.relevant) {
    ariaAttributes['aria-relevant'] = options.relevant;
  }
  
  if (options.atomic !== undefined) {
    ariaAttributes['aria-atomic'] = options.atomic.toString();
  }
  
  if (options.busy !== undefined) {
    ariaAttributes['aria-busy'] = options.busy.toString();
  }
  
  return ariaAttributes;
};

/**
 * Get keyboard navigation attributes for a component
 * 
 * @param {Object} options - Options for keyboard navigation
 * @param {Function} [options.onKeyDown] - Function to call on key down
 * @param {number} [options.tabIndex] - Tab index for the component
 * @returns {Object} Keyboard navigation attributes
 */
export const getKeyboardNavigationAttributes = (options = {}) => {
  const keyboardAttributes = {};
  
  if (options.onKeyDown) {
    keyboardAttributes.onKeyDown = options.onKeyDown;
  }
  
  if (options.tabIndex !== undefined) {
    keyboardAttributes.tabIndex = options.tabIndex;
  }
  
  return keyboardAttributes;
};

/**
 * Handle keyboard navigation for interactive elements
 * 
 * @param {Object} event - Keyboard event
 * @param {Object} options - Options for keyboard navigation
 * @param {Function} [options.onEnter] - Function to call on Enter key
 * @param {Function} [options.onSpace] - Function to call on Space key
 * @param {Function} [options.onEscape] - Function to call on Escape key
 * @param {Function} [options.onArrowUp] - Function to call on Arrow Up key
 * @param {Function} [options.onArrowDown] - Function to call on Arrow Down key
 * @param {Function} [options.onArrowLeft] - Function to call on Arrow Left key
 * @param {Function} [options.onArrowRight] - Function to call on Arrow Right key
 * @param {Function} [options.onTab] - Function to call on Tab key
 * @param {Function} [options.onShiftTab] - Function to call on Shift+Tab key
 * @param {Function} [options.onHome] - Function to call on Home key
 * @param {Function} [options.onEnd] - Function to call on End key
 * @param {Function} [options.onPageUp] - Function to call on Page Up key
 * @param {Function} [options.onPageDown] - Function to call on Page Down key
 * @returns {boolean} Whether the event was handled
 */
export const handleKeyboardNavigation = (event, options = {}) => {
  // Check if event is a keyboard event
  if (!event || !event.key) {
    return false;
  }
  
  // Handle different keys
  switch (event.key) {
    case 'Enter':
      if (options.onEnter) {
        options.onEnter(event);
        return true;
      }
      break;
    
    case ' ':
      if (options.onSpace) {
        options.onSpace(event);
        return true;
      }
      break;
    
    case 'Escape':
      if (options.onEscape) {
        options.onEscape(event);
        return true;
      }
      break;
    
    case 'ArrowUp':
      if (options.onArrowUp) {
        options.onArrowUp(event);
        return true;
      }
      break;
    
    case 'ArrowDown':
      if (options.onArrowDown) {
        options.onArrowDown(event);
        return true;
      }
      break;
    
    case 'ArrowLeft':
      if (options.onArrowLeft) {
        options.onArrowLeft(event);
        return true;
      }
      break;
    
    case 'ArrowRight':
      if (options.onArrowRight) {
        options.onArrowRight(event);
        return true;
      }
      break;
    
    case 'Tab':
      if (event.shiftKey && options.onShiftTab) {
        options.onShiftTab(event);
        return true;
      } else if (!event.shiftKey && options.onTab) {
        options.onTab(event);
        return true;
      }
      break;
    
    case 'Home':
      if (options.onHome) {
        options.onHome(event);
        return true;
      }
      break;
    
    case 'End':
      if (options.onEnd) {
        options.onEnd(event);
        return true;
      }
      break;
    
    case 'PageUp':
      if (options.onPageUp) {
        options.onPageUp(event);
        return true;
      }
      break;
    
    case 'PageDown':
      if (options.onPageDown) {
        options.onPageDown(event);
        return true;
      }
      break;
    
    default:
      return false;
  }
  
  return false;
};

/**
 * Create a focus trap for modal dialogs and other components that need to trap focus
 * 
 * @param {HTMLElement} containerElement - Container element to trap focus within
 * @returns {Object} Focus trap methods
 */
export const createFocusTrap = (containerElement) => {
  if (!containerElement) {
    console.warn('No container element provided for focus trap');
    return {
      activate: () => {},
      deactivate: () => {}
    };
  }
  
  // Store the element that had focus before the trap was activated
  let previouslyFocusedElement = null;
  
  // Get all focusable elements within the container
  const getFocusableElements = () => {
    return Array.from(
      containerElement.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      )
    ).filter(
      element => !element.hasAttribute('disabled') && !element.getAttribute('aria-hidden')
    );
  };
  
  // Handle tab key to keep focus within the container
  const handleTabKey = (event) => {
    const focusableElements = getFocusableElements();
    
    if (focusableElements.length === 0) {
      event.preventDefault();
      return;
    }
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (event.shiftKey) {
      // If shift+tab and the active element is the first focusable element,
      // move focus to the last focusable element
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      }
    } else {
      // If tab and the active element is the last focusable element,
      // move focus to the first focusable element
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  };
  
  // Handle keydown events
  const handleKeyDown = (event) => {
    if (event.key === 'Tab') {
      handleTabKey(event);
    } else if (event.key === 'Escape') {
      deactivate();
    }
  };
  
  // Activate the focus trap
  const activate = () => {
    previouslyFocusedElement = document.activeElement;
    
    // Add event listener for keydown
    document.addEventListener('keydown', handleKeyDown);
    
    // Focus the first focusable element
    const focusableElements = getFocusableElements();
    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }
  };
  
  // Deactivate the focus trap
  const deactivate = () => {
    // Remove event listener for keydown
    document.removeEventListener('keydown', handleKeyDown);
    
    // Restore focus to the previously focused element
    if (previouslyFocusedElement) {
      previouslyFocusedElement.focus();
    }
  };
  
  return {
    activate,
    deactivate
  };
};

/**
 * Check if high contrast mode is enabled
 * 
 * @returns {boolean} Whether high contrast mode is enabled
 */
export const isHighContrastMode = () => {
  // Check if the browser supports the forced-colors media query
  if (window.matchMedia) {
    return window.matchMedia('(forced-colors: active)').matches;
  }
  
  return false;
};

/**
 * Get color contrast ratio between two colors
 * 
 * @param {string} color1 - First color (hex format)
 * @param {string} color2 - Second color (hex format)
 * @returns {number} Contrast ratio (1-21)
 */
export const getContrastRatio = (color1, color2) => {
  // Convert hex to RGB
  const hexToRgb = (hex) => {
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
    const fullHex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(fullHex);
    
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };
  
  // Calculate relative luminance
  const getLuminance = (rgb) => {
    const a = [rgb.r, rgb.g, rgb.b].map(v => {
      v /= 255;
      return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
    });
    
    return a[0] * 0.2126 + a[1] * 0.7152 + a[2] * 0.0722;
  };
  
  // Parse colors
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);
  
  if (!rgb1 || !rgb2) {
    return 1;
  }
  
  // Calculate luminance
  const luminance1 = getLuminance(rgb1);
  const luminance2 = getLuminance(rgb2);
  
  // Calculate contrast ratio
  const brightest = Math.max(luminance1, luminance2);
  const darkest = Math.min(luminance1, luminance2);
  
  return (brightest + 0.05) / (darkest + 0.05);
};

/**
 * Check if a color combination meets WCAG contrast requirements
 * 
 * @param {string} foreground - Foreground color (hex format)
 * @param {string} background - Background color (hex format)
 * @param {string} [level='AA'] - WCAG level (AA or AAA)
 * @param {string} [size='normal'] - Text size (normal or large)
 * @returns {boolean} Whether the contrast meets the requirements
 */
export const meetsContrastRequirements = (foreground, background, level = 'AA', size = 'normal') => {
  const ratio = getContrastRatio(foreground, background);
  
  if (level === 'AAA') {
    return size === 'large' ? ratio >= 4.5 : ratio >= 7;
  }
  
  return size === 'large' ? ratio >= 3 : ratio >= 4.5;
};
