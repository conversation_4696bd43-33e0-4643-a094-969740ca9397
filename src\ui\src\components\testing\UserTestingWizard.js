import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Stepper,
  Step,
  Step<PERSON><PERSON>l,
  <PERSON>ton,
  Typography,
  Paper,
  TextField,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  FormControlLabel,
  Checkbox,
  Rating,
  Divider,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar
} from '@mui/material';
import {
  Check as CheckIcon,
  Close as CloseIcon,
  NavigateNext as NextIcon,
  NavigateBefore as BackIcon,
  Save as SaveIcon,
  Info as InfoIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import userTestingService, { TEST_TYPES, DIFFICULTY_LEVELS } from '../../services/userTestingService';
import testSessionRecordingService from '../../services/TestSessionRecordingService';
import useTestSession from '../../hooks/useTestSession';
import VisualizationTestContainer from './VisualizationTestContainer';

/**
 * User Testing Wizard
 *
 * This component guides users through a testing session for Cyber-Safety visualizations.
 */
function UserTestingWizard({ visualizationType, testType = TEST_TYPES.USABILITY, onComplete }) {
  // State for active step
  const [activeStep, setActiveStep] = useState(0);

  // Use test session hook
  const {
    session: testSession,
    loading: sessionLoading,
    error: sessionError,
    startSession,
    endSession,
    recordInteraction,
    recordTaskCompletion,
    recordFeedback,
    recordFinalFeedback
  } = useTestSession();

  // State for current task
  const [currentTask, setCurrentTask] = useState(null);

  // State for participant information
  const [participantInfo, setParticipantInfo] = useState({
    name: '',
    email: '',
    age: '',
    role: '',
    experience: 'beginner',
    consentGiven: false
  });

  // State for task feedback
  const [taskFeedback, setTaskFeedback] = useState({
    difficulty: 3,
    satisfaction: 3,
    comments: ''
  });

  // State for final feedback
  const [finalFeedback, setFinalFeedback] = useState({
    overallSatisfaction: 3,
    usability: 3,
    visualAppeal: 3,
    usefulness: 3,
    likelyToRecommend: 3,
    improvements: '',
    additionalComments: ''
  });

  // State for loading
  const [loading, setLoading] = useState(false);

  // State for error
  const [error, setError] = useState(null);

  // State for task timer
  const [taskTimer, setTaskTimer] = useState(0);

  // State for task timer interval
  const [taskTimerInterval, setTaskTimerInterval] = useState(null);

  // State for confirmation dialog
  const [confirmDialog, setConfirmDialog] = useState({
    open: false,
    title: '',
    message: '',
    onConfirm: null
  });

  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });

  // Steps for the wizard
  const steps = [
    'Introduction',
    'Participant Information',
    'Task Instructions',
    'Task Execution',
    'Task Feedback',
    'Final Feedback',
    'Completion'
  ];

  // Initialize test session
  useEffect(() => {
    // Check if there's an existing session
    const existingSession = userTestingService.getCurrentTestSession();

    if (existingSession) {
      setTestSession(existingSession);

      // Set current task if available
      if (existingSession.currentTaskIndex < existingSession.tasks.length) {
        setCurrentTask(existingSession.tasks[existingSession.currentTaskIndex]);
      }

      // Skip to appropriate step
      if (existingSession.endTime) {
        // Session is complete, go to completion step
        setActiveStep(6);
      } else if (existingSession.currentTaskIndex < existingSession.tasks.length) {
        // Task in progress, go to task execution step
        setActiveStep(3);
      }
    }
  }, []);

  // Handle task timer
  useEffect(() => {
    // Clear existing interval when component unmounts or task changes
    return () => {
      if (taskTimerInterval) {
        clearInterval(taskTimerInterval);
      }
    };
  }, [taskTimerInterval]);

  // Start task timer
  const startTaskTimer = () => {
    // Clear existing interval
    if (taskTimerInterval) {
      clearInterval(taskTimerInterval);
    }

    // Reset timer
    setTaskTimer(0);

    // Start new interval
    const interval = setInterval(() => {
      setTaskTimer(prevTimer => prevTimer + 1);
    }, 1000);

    setTaskTimerInterval(interval);
  };

  // Stop task timer
  const stopTaskTimer = () => {
    if (taskTimerInterval) {
      clearInterval(taskTimerInterval);
      setTaskTimerInterval(null);
    }
  };

  // Format timer as MM:SS
  const formatTimer = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle next step
  const handleNext = async () => {
    try {
      setLoading(true);
      setError(null);

      // Handle step-specific actions
      switch (activeStep) {
        case 1: // Participant Information
          // Validate participant information
          if (!participantInfo.name || !participantInfo.email || !participantInfo.consentGiven) {
            setError('Please fill in all required fields and give consent to proceed.');
            setLoading(false);
            return;
          }

          // Create test session
          const testConfig = userTestingService.createTestScenario(visualizationType, testType);
          testConfig.participantInfo = participantInfo;

          const session = await userTestingService.startTestSession(testConfig);
          setTestSession(session);

          // Set current task
          if (session.tasks.length > 0) {
            setCurrentTask(session.tasks[0]);
          }
          break;

        case 3: // Task Execution
          // Complete current task
          if (currentTask) {
            // Stop task timer
            stopTaskTimer();

            // Record task completion
            const taskIndex = testSession.currentTaskIndex;
            const success = true; // Assume success for now
            const results = {
              timeSpent: taskTimer
            };

            const updatedTask = userTestingService.completeTask(taskIndex, success, results);

            // Update current task
            if (taskIndex < testSession.tasks.length - 1) {
              setCurrentTask(testSession.tasks[taskIndex + 1]);
            } else {
              setCurrentTask(null);
            }
          }
          break;

        case 4: // Task Feedback
          // Add task feedback
          if (currentTask) {
            userTestingService.addFeedback({
              type: 'task',
              taskId: currentTask.id,
              visualizationType,
              ...taskFeedback
            });

            // Reset task feedback
            setTaskFeedback({
              difficulty: 3,
              satisfaction: 3,
              comments: ''
            });

            // Check if there are more tasks
            const updatedSession = userTestingService.getCurrentTestSession();

            if (updatedSession.currentTaskIndex < updatedSession.tasks.length) {
              // More tasks available, go back to task instructions
              setActiveStep(2);
              setLoading(false);
              return;
            }
          }
          break;

        case 5: // Final Feedback
          // End test session with final feedback
          await userTestingService.endTestSession(finalFeedback);

          // Call onComplete callback if provided
          if (onComplete) {
            onComplete();
          }
          break;
      }

      // Move to next step
      setActiveStep(prevStep => prevStep + 1);
    } catch (error) {
      console.error('Error in test wizard:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle back step
  const handleBack = () => {
    // Handle step-specific actions
    switch (activeStep) {
      case 3: // Task Execution
        // Confirm task cancellation
        setConfirmDialog({
          open: true,
          title: 'Cancel Task?',
          message: 'Are you sure you want to cancel this task? Your progress will be lost.',
          onConfirm: () => {
            // Stop task timer
            stopTaskTimer();

            // Move back to task instructions
            setActiveStep(2);

            // Close dialog
            setConfirmDialog({ ...confirmDialog, open: false });
          }
        });
        return;
    }

    // Move to previous step
    setActiveStep(prevStep => prevStep - 1);
  };

  // Handle start task
  const handleStartTask = () => {
    try {
      // Start task
      const taskIndex = testSession.currentTaskIndex;
      userTestingService.startTask(taskIndex);

      // Start task timer
      startTaskTimer();

      // Move to task execution step
      setActiveStep(3);
    } catch (error) {
      console.error('Error starting task:', error);
      setError(error.message);
    }
  };

  // Handle task interaction
  const handleTaskInteraction = (interactionType, details = {}) => {
    try {
      if (currentTask) {
        const taskIndex = testSession.currentTaskIndex;
        userTestingService.recordTaskInteraction(taskIndex, interactionType, details);
      }
    } catch (error) {
      console.error('Error recording task interaction:', error);
    }
  };

  // Handle participant info change
  const handleParticipantInfoChange = (field, value) => {
    setParticipantInfo(prevInfo => ({
      ...prevInfo,
      [field]: value
    }));
  };

  // Handle task feedback change
  const handleTaskFeedbackChange = (field, value) => {
    setTaskFeedback(prevFeedback => ({
      ...prevFeedback,
      [field]: value
    }));
  };

  // Handle final feedback change
  const handleFinalFeedbackChange = (field, value) => {
    setFinalFeedback(prevFeedback => ({
      ...prevFeedback,
      [field]: value
    }));
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Render introduction step
  const renderIntroduction = () => {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Welcome to the Cyber-Safety Visualization User Testing
        </Typography>

        <Typography paragraph>
          Thank you for participating in this user testing session. Your feedback will help us improve the Cyber-Safety visualizations.
        </Typography>

        <Typography paragraph>
          During this session, you will be asked to complete a series of tasks using the {visualizationType} visualization. After each task, you will provide feedback on your experience.
        </Typography>

        <Typography paragraph>
          The session will take approximately 15-20 minutes to complete. You can pause or exit the session at any time.
        </Typography>

        <Typography paragraph>
          Let's get started!
        </Typography>
      </Box>
    );
  };

  // Render participant information step
  const renderParticipantInfo = () => {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Participant Information
        </Typography>

        <Typography paragraph>
          Please provide some information about yourself. This information will be used for research purposes only.
        </Typography>

        <Box component="form" sx={{ mt: 2 }}>
          <TextField
            label="Name"
            value={participantInfo.name}
            onChange={(e) => handleParticipantInfoChange('name', e.target.value)}
            fullWidth
            margin="normal"
            required
          />

          <TextField
            label="Email"
            type="email"
            value={participantInfo.email}
            onChange={(e) => handleParticipantInfoChange('email', e.target.value)}
            fullWidth
            margin="normal"
            required
          />

          <TextField
            label="Age"
            type="number"
            value={participantInfo.age}
            onChange={(e) => handleParticipantInfoChange('age', e.target.value)}
            fullWidth
            margin="normal"
          />

          <TextField
            label="Role/Position"
            value={participantInfo.role}
            onChange={(e) => handleParticipantInfoChange('role', e.target.value)}
            fullWidth
            margin="normal"
          />

          <FormControl component="fieldset" margin="normal">
            <FormLabel component="legend">Experience with data visualizations</FormLabel>
            <RadioGroup
              value={participantInfo.experience}
              onChange={(e) => handleParticipantInfoChange('experience', e.target.value)}
            >
              <FormControlLabel value="beginner" control={<Radio />} label="Beginner" />
              <FormControlLabel value="intermediate" control={<Radio />} label="Intermediate" />
              <FormControlLabel value="advanced" control={<Radio />} label="Advanced" />
            </RadioGroup>
          </FormControl>

          <FormControlLabel
            control={
              <Checkbox
                checked={participantInfo.consentGiven}
                onChange={(e) => handleParticipantInfoChange('consentGiven', e.target.checked)}
                required
              />
            }
            label="I consent to participate in this user testing session and allow my feedback to be used for research and product improvement."
          />
        </Box>
      </Box>
    );
  };

  // Render task instructions step
  const renderTaskInstructions = () => {
    if (!testSession || !currentTask) {
      return (
        <Box>
          <Typography variant="h5" gutterBottom>
            No tasks available
          </Typography>

          <Typography paragraph>
            There are no tasks available for this test session.
          </Typography>
        </Box>
      );
    }

    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Task: {currentTask.name}
        </Typography>

        <Typography paragraph>
          {currentTask.description}
        </Typography>

        <Box sx={{ mt: 2, mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            Difficulty: {currentTask.difficulty}
          </Typography>

          <Typography variant="subtitle2">
            Expected time: {Math.floor(currentTask.expectedTime / 60)}:{(currentTask.expectedTime % 60).toString().padStart(2, '0')}
          </Typography>
        </Box>

        <Typography paragraph>
          Click "Start Task" when you are ready to begin. The timer will start automatically.
        </Typography>

        <Button
          variant="contained"
          color="primary"
          onClick={handleStartTask}
          disabled={loading}
        >
          Start Task
        </Button>
      </Box>
    );
  };

  // Render task execution step
  const renderTaskExecution = () => {
    if (!testSession || !currentTask) {
      return (
        <Box>
          <Typography variant="h5" gutterBottom>
            No task in progress
          </Typography>

          <Typography paragraph>
            There is no task currently in progress.
          </Typography>
        </Box>
      );
    }

    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">
            Task: {currentTask.name}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle1" sx={{ mr: 1 }}>
              Time: {formatTimer(taskTimer)}
            </Typography>

            <Button
              variant="outlined"
              color="primary"
              onClick={() => handleTaskInteraction('help_requested')}
              sx={{ mr: 1 }}
            >
              Need Help
            </Button>

            <Button
              variant="contained"
              color="primary"
              onClick={handleNext}
            >
              Complete Task
            </Button>
          </Box>
        </Box>

        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="subtitle1" gutterBottom>
            Task Description:
          </Typography>

          <Typography paragraph>
            {currentTask.description}
          </Typography>
        </Paper>

        <Alert severity="info" sx={{ mb: 2 }}>
          Please complete the task and click "Complete Task" when you are finished. If you need help, click "Need Help".
        </Alert>

        {/* Render the actual visualization */}
        <VisualizationTestContainer
          visualizationType={visualizationType}
          testSession={testSession}
          currentTask={currentTask}
          onInteraction={handleTaskInteraction}
          onTaskComplete={handleNext}
          onNeedHelp={() => handleTaskInteraction('help_requested')}
        />
      </Box>
    );
  };

  // Render task feedback step
  const renderTaskFeedback = () => {
    if (!testSession || !currentTask) {
      return (
        <Box>
          <Typography variant="h5" gutterBottom>
            No task to provide feedback for
          </Typography>

          <Typography paragraph>
            There is no task currently available to provide feedback for.
          </Typography>
        </Box>
      );
    }

    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Task Feedback: {currentTask.name}
        </Typography>

        <Typography paragraph>
          Please provide feedback on the task you just completed.
        </Typography>

        <Box sx={{ mt: 2 }}>
          <Typography component="legend">How difficult was this task?</Typography>
          <Rating
            name="difficulty"
            value={taskFeedback.difficulty}
            onChange={(event, newValue) => handleTaskFeedbackChange('difficulty', newValue)}
            max={5}
          />

          <Typography component="legend" sx={{ mt: 2 }}>How satisfied are you with the visualization for this task?</Typography>
          <Rating
            name="satisfaction"
            value={taskFeedback.satisfaction}
            onChange={(event, newValue) => handleTaskFeedbackChange('satisfaction', newValue)}
            max={5}
          />

          <TextField
            label="Comments"
            multiline
            rows={4}
            value={taskFeedback.comments}
            onChange={(e) => handleTaskFeedbackChange('comments', e.target.value)}
            fullWidth
            margin="normal"
          />
        </Box>
      </Box>
    );
  };

  // Render final feedback step
  const renderFinalFeedback = () => {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Final Feedback
        </Typography>

        <Typography paragraph>
          Please provide your overall feedback on the {visualizationType} visualization.
        </Typography>

        <Box sx={{ mt: 2 }}>
          <Typography component="legend">Overall satisfaction</Typography>
          <Rating
            name="overallSatisfaction"
            value={finalFeedback.overallSatisfaction}
            onChange={(event, newValue) => handleFinalFeedbackChange('overallSatisfaction', newValue)}
            max={5}
          />

          <Typography component="legend" sx={{ mt: 2 }}>Usability</Typography>
          <Rating
            name="usability"
            value={finalFeedback.usability}
            onChange={(event, newValue) => handleFinalFeedbackChange('usability', newValue)}
            max={5}
          />

          <Typography component="legend" sx={{ mt: 2 }}>Visual appeal</Typography>
          <Rating
            name="visualAppeal"
            value={finalFeedback.visualAppeal}
            onChange={(event, newValue) => handleFinalFeedbackChange('visualAppeal', newValue)}
            max={5}
          />

          <Typography component="legend" sx={{ mt: 2 }}>Usefulness</Typography>
          <Rating
            name="usefulness"
            value={finalFeedback.usefulness}
            onChange={(event, newValue) => handleFinalFeedbackChange('usefulness', newValue)}
            max={5}
          />

          <Typography component="legend" sx={{ mt: 2 }}>How likely are you to recommend this visualization?</Typography>
          <Rating
            name="likelyToRecommend"
            value={finalFeedback.likelyToRecommend}
            onChange={(event, newValue) => handleFinalFeedbackChange('likelyToRecommend', newValue)}
            max={5}
          />

          <TextField
            label="What improvements would you suggest?"
            multiline
            rows={4}
            value={finalFeedback.improvements}
            onChange={(e) => handleFinalFeedbackChange('improvements', e.target.value)}
            fullWidth
            margin="normal"
          />

          <TextField
            label="Additional comments"
            multiline
            rows={4}
            value={finalFeedback.additionalComments}
            onChange={(e) => handleFinalFeedbackChange('additionalComments', e.target.value)}
            fullWidth
            margin="normal"
          />
        </Box>
      </Box>
    );
  };

  // Render completion step
  const renderCompletion = () => {
    return (
      <Box sx={{ textAlign: 'center' }}>
        <CheckIcon color="success" sx={{ fontSize: 64, mb: 2 }} />

        <Typography variant="h5" gutterBottom>
          Thank You!
        </Typography>

        <Typography paragraph>
          Your feedback has been submitted successfully. Thank you for participating in this user testing session.
        </Typography>

        <Typography paragraph>
          Your input will help us improve the Cyber-Safety visualizations.
        </Typography>

        <Button
          variant="contained"
          color="primary"
          onClick={onComplete}
          sx={{ mt: 2 }}
        >
          Finish
        </Button>
      </Box>
    );
  };

  // Render step content
  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return renderIntroduction();
      case 1:
        return renderParticipantInfo();
      case 2:
        return renderTaskInstructions();
      case 3:
        return renderTaskExecution();
      case 4:
        return renderTaskFeedback();
      case 5:
        return renderFinalFeedback();
      case 6:
        return renderCompletion();
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box sx={{ width: '100%', p: 2 }}>
      <Stepper activeStep={activeStep} alternativeLabel>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Paper sx={{ p: 3, mt: 3 }}>
        {getStepContent(activeStep)}

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            variant="outlined"
            onClick={handleBack}
            disabled={activeStep === 0 || activeStep === 6 || loading}
            startIcon={<BackIcon />}
          >
            Back
          </Button>

          <Button
            variant="contained"
            onClick={handleNext}
            disabled={activeStep === 6 || loading}
            endIcon={activeStep === 5 ? <SaveIcon /> : <NextIcon />}
          >
            {loading ? (
              <CircularProgress size={24} />
            ) : activeStep === 5 ? (
              'Submit'
            ) : (
              'Next'
            )}
          </Button>
        </Box>
      </Paper>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ ...confirmDialog, open: false })}
      >
        <DialogTitle>{confirmDialog.title}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            {confirmDialog.message}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog({ ...confirmDialog, open: false })}>
            Cancel
          </Button>
          <Button onClick={confirmDialog.onConfirm} color="primary" autoFocus>
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default UserTestingWizard;
