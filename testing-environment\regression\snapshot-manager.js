/**
 * Snapshot Testing Framework for NovaConnect Universal API Connector
 * 
 * This module provides snapshot testing capabilities to detect regressions.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class SnapshotManager {
  /**
   * Create a new SnapshotManager
   * 
   * @param {string} snapshotDir - Directory to store snapshots
   */
  constructor(snapshotDir = 'snapshots') {
    this.snapshotDir = path.resolve(process.cwd(), snapshotDir);
    if (!fs.existsSync(this.snapshotDir)) {
      fs.mkdirSync(this.snapshotDir, { recursive: true });
    }
  }

  /**
   * Create a snapshot of data
   * 
   * @param {string} testName - Name of the test
   * @param {*} data - Data to snapshot
   * @param {Object} options - Options
   * @param {boolean} options.update - Whether to update existing snapshots
   * @returns {Object} - Snapshot information
   */
  createSnapshot(testName, data, options = {}) {
    const snapshotPath = path.join(this.snapshotDir, `${testName}.snap.json`);
    const exists = fs.existsSync(snapshotPath);
    
    // If snapshot exists and update is not enabled, don't overwrite
    if (exists && !options.update) {
      return this.getSnapshot(testName);
    }
    
    // Compute hash of data
    const hash = crypto.createHash('sha256')
      .update(JSON.stringify(data))
      .digest('hex');
    
    // Create snapshot object
    const snapshot = {
      testName,
      data,
      hash,
      timestamp: new Date().toISOString(),
      version: exists ? this.getSnapshot(testName).version + 1 : 1
    };
    
    // Write snapshot to file
    fs.writeFileSync(snapshotPath, JSON.stringify(snapshot, null, 2));
    
    return snapshot;
  }

  /**
   * Get a snapshot
   * 
   * @param {string} testName - Name of the test
   * @returns {Object|null} - Snapshot or null if not found
   */
  getSnapshot(testName) {
    const snapshotPath = path.join(this.snapshotDir, `${testName}.snap.json`);
    
    if (!fs.existsSync(snapshotPath)) {
      return null;
    }
    
    try {
      return JSON.parse(fs.readFileSync(snapshotPath, 'utf8'));
    } catch (error) {
      console.error(`Error reading snapshot ${testName}:`, error.message);
      return null;
    }
  }

  /**
   * Compare data with a snapshot
   * 
   * @param {string} testName - Name of the test
   * @param {*} currentData - Current data to compare
   * @returns {Object} - Comparison result
   */
  compareWithSnapshot(testName, currentData) {
    const snapshot = this.getSnapshot(testName);
    
    if (!snapshot) {
      return { 
        matches: false, 
        reason: 'No snapshot exists',
        diff: null
      };
    }
    
    // Compute hash of current data
    const currentHash = crypto.createHash('sha256')
      .update(JSON.stringify(currentData))
      .digest('hex');
    
    // Compare hashes
    const matches = snapshot.hash === currentHash;
    
    return {
      matches,
      reason: matches ? null : 'Data mismatch',
      diff: matches ? null : this.generateDiff(snapshot.data, currentData),
      snapshot
    };
  }

  /**
   * Generate a diff between two objects
   * 
   * @param {*} snapshotData - Snapshot data
   * @param {*} currentData - Current data
   * @returns {Object} - Diff object
   */
  generateDiff(snapshotData, currentData) {
    // Handle non-object data
    if (typeof snapshotData !== 'object' || typeof currentData !== 'object' || 
        snapshotData === null || currentData === null) {
      return {
        type: 'value_change',
        old: snapshotData,
        new: currentData
      };
    }
    
    // Handle arrays
    if (Array.isArray(snapshotData) && Array.isArray(currentData)) {
      return this.generateArrayDiff(snapshotData, currentData);
    }
    
    // Handle objects
    const diff = {};
    
    // Find added or changed properties
    for (const [key, value] of Object.entries(currentData)) {
      if (!(key in snapshotData)) {
        diff[key] = { 
          type: 'added', 
          value 
        };
      } else if (JSON.stringify(snapshotData[key]) !== JSON.stringify(value)) {
        if (typeof snapshotData[key] === 'object' && typeof value === 'object' && 
            snapshotData[key] !== null && value !== null) {
          // Recursively diff nested objects
          diff[key] = {
            type: 'object_change',
            diff: this.generateDiff(snapshotData[key], value)
          };
        } else {
          diff[key] = {
            type: 'value_change',
            old: snapshotData[key],
            new: value
          };
        }
      }
    }
    
    // Find removed properties
    for (const key of Object.keys(snapshotData)) {
      if (!(key in currentData)) {
        diff[key] = { 
          type: 'removed', 
          value: snapshotData[key] 
        };
      }
    }
    
    return diff;
  }

  /**
   * Generate a diff between two arrays
   * 
   * @param {Array} snapshotArray - Snapshot array
   * @param {Array} currentArray - Current array
   * @returns {Object} - Diff object
   */
  generateArrayDiff(snapshotArray, currentArray) {
    const diff = {
      type: 'array_change',
      lengthChange: currentArray.length - snapshotArray.length,
      items: {}
    };
    
    // Compare items at each index
    const maxLength = Math.max(snapshotArray.length, currentArray.length);
    
    for (let i = 0; i < maxLength; i++) {
      if (i >= snapshotArray.length) {
        // Item added
        diff.items[i] = {
          type: 'added',
          value: currentArray[i]
        };
      } else if (i >= currentArray.length) {
        // Item removed
        diff.items[i] = {
          type: 'removed',
          value: snapshotArray[i]
        };
      } else if (JSON.stringify(snapshotArray[i]) !== JSON.stringify(currentArray[i])) {
        // Item changed
        if (typeof snapshotArray[i] === 'object' && typeof currentArray[i] === 'object' && 
            snapshotArray[i] !== null && currentArray[i] !== null) {
          // Recursively diff nested objects
          diff.items[i] = {
            type: 'object_change',
            diff: this.generateDiff(snapshotArray[i], currentArray[i])
          };
        } else {
          diff.items[i] = {
            type: 'value_change',
            old: snapshotArray[i],
            new: currentArray[i]
          };
        }
      }
    }
    
    return diff;
  }

  /**
   * List all snapshots
   * 
   * @returns {Array} - List of snapshot names
   */
  listSnapshots() {
    if (!fs.existsSync(this.snapshotDir)) {
      return [];
    }
    
    return fs.readdirSync(this.snapshotDir)
      .filter(file => file.endsWith('.snap.json'))
      .map(file => file.replace('.snap.json', ''));
  }

  /**
   * Delete a snapshot
   * 
   * @param {string} testName - Name of the test
   * @returns {boolean} - Whether the snapshot was deleted
   */
  deleteSnapshot(testName) {
    const snapshotPath = path.join(this.snapshotDir, `${testName}.snap.json`);
    
    if (!fs.existsSync(snapshotPath)) {
      return false;
    }
    
    fs.unlinkSync(snapshotPath);
    return true;
  }

  /**
   * Format a diff for display
   * 
   * @param {Object} diff - Diff object
   * @param {number} indent - Indentation level
   * @returns {string} - Formatted diff
   */
  formatDiff(diff, indent = 0) {
    const indentStr = '  '.repeat(indent);
    let result = '';
    
    if (!diff || Object.keys(diff).length === 0) {
      return `${indentStr}No differences\n`;
    }
    
    if (diff.type === 'array_change') {
      result += `${indentStr}Array length changed: ${diff.lengthChange > 0 ? '+' : ''}${diff.lengthChange}\n`;
      
      for (const [index, itemDiff] of Object.entries(diff.items)) {
        result += `${indentStr}[${index}]: `;
        
        if (itemDiff.type === 'added') {
          result += `Added: ${JSON.stringify(itemDiff.value)}\n`;
        } else if (itemDiff.type === 'removed') {
          result += `Removed: ${JSON.stringify(itemDiff.value)}\n`;
        } else if (itemDiff.type === 'value_change') {
          result += `Changed: ${JSON.stringify(itemDiff.old)} -> ${JSON.stringify(itemDiff.new)}\n`;
        } else if (itemDiff.type === 'object_change') {
          result += `Changed:\n${this.formatDiff(itemDiff.diff, indent + 1)}`;
        }
      }
      
      return result;
    }
    
    for (const [key, valueDiff] of Object.entries(diff)) {
      result += `${indentStr}${key}: `;
      
      if (valueDiff.type === 'added') {
        result += `Added: ${JSON.stringify(valueDiff.value)}\n`;
      } else if (valueDiff.type === 'removed') {
        result += `Removed: ${JSON.stringify(valueDiff.value)}\n`;
      } else if (valueDiff.type === 'value_change') {
        result += `Changed: ${JSON.stringify(valueDiff.old)} -> ${JSON.stringify(valueDiff.new)}\n`;
      } else if (valueDiff.type === 'object_change') {
        result += `Changed:\n${this.formatDiff(valueDiff.diff, indent + 1)}`;
      }
    }
    
    return result;
  }
}

module.exports = SnapshotManager;
