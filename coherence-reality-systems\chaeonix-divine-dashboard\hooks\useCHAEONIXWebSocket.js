/**
 * CHAEONIX WEBSOCKET HOOK
 * Real-time connection to the divine intelligence stream
 * Handles engine status, market data, and coherence updates
 */

import { useState, useEffect, useRef } from 'react';
import useWebSocket, { ReadyState } from 'react-use-websocket';
import { CHAEONIX_ENGINES, REFRESH_RATES } from '../utils/chaeonixConstants';

const WEBSOCKET_URL = process.env.NEXT_PUBLIC_CHAEONIX_WS_URL || 'ws://localhost:8000/ws/divine-stream';

export function useCHAEONIXWebSocket() {
  const [engineStatus, setEngineStatus] = useState({});
  const [marketData, setMarketData] = useState({});
  const [coherenceLevel, setCoherenceLevel] = useState(0.75);
  const [couplingMatrix, setCouplingMatrix] = useState({});
  const [lastUpdate, setLastUpdate] = useState(null);
  const [messageHistory, setMessageHistory] = useState([]);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  const {
    sendMessage,
    lastMessage,
    readyState,
    getWebSocket
  } = useWebSocket(WEBSOCKET_URL, {
    onOpen: () => {
      console.log('🌟 CHAEONIX Divine Stream Connected');
      setReconnectAttempts(0);
      
      // Send initial handshake
      sendMessage(JSON.stringify({
        type: 'handshake',
        client: 'chaeonix-dashboard',
        version: '1.0.0',
        timestamp: new Date().toISOString()
      }));
    },
    onClose: () => {
      console.log('🔌 CHAEONIX Divine Stream Disconnected');
    },
    onError: (error) => {
      console.error('❌ CHAEONIX WebSocket Error:', error);
    },
    shouldReconnect: (closeEvent) => {
      // Reconnect with exponential backoff
      const maxAttempts = 10;
      if (reconnectAttempts < maxAttempts) {
        setReconnectAttempts(prev => prev + 1);
        return true;
      }
      return false;
    },
    reconnectInterval: (attemptNumber) => {
      // Exponential backoff: 1s, 2s, 4s, 8s, etc.
      return Math.min(1000 * Math.pow(2, attemptNumber), 30000);
    },
    reconnectAttempts: 10,
    share: false,
    filter: () => true,
    retryOnError: true,
  });

  // Process incoming messages
  useEffect(() => {
    if (lastMessage !== null) {
      try {
        const data = JSON.parse(lastMessage.data);
        processIncomingMessage(data);
        
        // Add to message history (keep last 100 messages)
        setMessageHistory(prev => {
          const newHistory = [...prev, { ...data, timestamp: new Date().toISOString() }];
          return newHistory.slice(-100);
        });
        
        setLastUpdate(new Date().toISOString());
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    }
  }, [lastMessage]);

  const processIncomingMessage = (data) => {
    switch (data.type) {
      case 'engine_status':
        updateEngineStatus(data.payload);
        break;
      
      case 'market_data':
        updateMarketData(data.payload);
        break;
      
      case 'coherence_update':
        setCoherenceLevel(data.payload.level);
        break;
      
      case 'coupling_matrix':
        setCouplingMatrix(data.payload);
        break;
      
      case 'divine_simulation_result':
        handleDivineSimulationResult(data.payload);
        break;
      
      case 'prophetic_event':
        handlePropheticEvent(data.payload);
        break;
      
      case 'heartbeat':
        // Server heartbeat - update connection status
        break;
      
      default:
        console.log('📡 Unknown message type:', data.type);
    }
  };

  const updateEngineStatus = (statusData) => {
    setEngineStatus(prev => ({
      ...prev,
      ...statusData,
      last_update: new Date().toISOString()
    }));
  };

  const updateMarketData = (marketUpdate) => {
    setMarketData(prev => ({
      ...prev,
      [marketUpdate.symbol]: {
        ...prev[marketUpdate.symbol],
        ...marketUpdate,
        timestamp: new Date().toISOString()
      }
    }));
  };

  const handleDivineSimulationResult = (result) => {
    console.log('🔮 Divine Simulation Result:', result);
    // Emit custom event for components to listen to
    window.dispatchEvent(new CustomEvent('chaeonix:simulation_result', { 
      detail: result 
    }));
  };

  const handlePropheticEvent = (event) => {
    console.log('⚡ Prophetic Event:', event);
    // Emit custom event for prophetic console
    window.dispatchEvent(new CustomEvent('chaeonix:prophetic_event', { 
      detail: event 
    }));
  };

  // Send commands to CHAEONIX
  const sendCommand = (command, payload = {}) => {
    if (readyState === ReadyState.OPEN) {
      const message = {
        type: 'command',
        command: command,
        payload: payload,
        timestamp: new Date().toISOString(),
        client_id: 'chaeonix-dashboard'
      };
      
      sendMessage(JSON.stringify(message));
      return true;
    } else {
      console.warn('⚠️ WebSocket not connected, command queued:', command);
      return false;
    }
  };

  // Request engine analysis
  const requestEngineAnalysis = (engineCode, inputData) => {
    return sendCommand('analyze_engine', {
      engine: engineCode,
      input: inputData
    });
  };

  // Request divine simulation
  const requestDivineSimulation = (simulationParams) => {
    return sendCommand('divine_simulation', simulationParams);
  };

  // Seed prophetic event
  const seedPropheticEvent = (eventData) => {
    return sendCommand('seed_prophetic_event', eventData);
  };

  // Subscribe to specific data streams
  const subscribeToStream = (streamType, params = {}) => {
    return sendCommand('subscribe', {
      stream: streamType,
      params: params
    });
  };

  // Unsubscribe from data streams
  const unsubscribeFromStream = (streamType) => {
    return sendCommand('unsubscribe', {
      stream: streamType
    });
  };

  // Get connection status string
  const getConnectionStatus = () => {
    switch (readyState) {
      case ReadyState.CONNECTING:
        return 'Connecting';
      case ReadyState.OPEN:
        return 'Open';
      case ReadyState.CLOSING:
        return 'Closing';
      case ReadyState.CLOSED:
        return 'Closed';
      default:
        return 'Unknown';
    }
  };

  // Calculate engine health score
  const getEngineHealthScore = () => {
    const engines = Object.keys(CHAEONIX_ENGINES);
    const activeEngines = Object.keys(engineStatus).filter(
      engine => engineStatus[engine]?.status === 'operational'
    );
    
    return engines.length > 0 ? activeEngines.length / engines.length : 0;
  };

  // Get latest market data for symbol
  const getMarketDataForSymbol = (symbol) => {
    return marketData[symbol] || null;
  };

  // Get engine status for specific engine
  const getEngineStatus = (engineCode) => {
    return engineStatus[engineCode] || {
      status: 'unknown',
      confidence: 0,
      last_analysis: null
    };
  };

  // Calculate overall system coherence
  const getSystemCoherence = () => {
    const engineHealthScore = getEngineHealthScore();
    const connectionScore = readyState === ReadyState.OPEN ? 1 : 0;
    const dataFreshnessScore = lastUpdate ? 
      Math.max(0, 1 - (Date.now() - new Date(lastUpdate)) / (5 * 60 * 1000)) : 0; // 5 min decay
    
    return (engineHealthScore * 0.5 + connectionScore * 0.3 + dataFreshnessScore * 0.2);
  };

  return {
    // Connection state
    connectionStatus: getConnectionStatus(),
    readyState,
    isConnected: readyState === ReadyState.OPEN,
    reconnectAttempts,
    lastUpdate,
    
    // Data state
    engineStatus,
    marketData,
    coherenceLevel,
    couplingMatrix,
    messageHistory,
    lastMessage,
    
    // Computed values
    engineHealthScore: getEngineHealthScore(),
    systemCoherence: getSystemCoherence(),
    
    // Commands
    sendCommand,
    requestEngineAnalysis,
    requestDivineSimulation,
    seedPropheticEvent,
    subscribeToStream,
    unsubscribeFromStream,
    
    // Getters
    getMarketDataForSymbol,
    getEngineStatus,
    
    // Raw WebSocket
    sendMessage,
    getWebSocket
  };
}

// Hook for subscribing to specific CHAEONIX events
export function useCHAEONIXEvent(eventType, callback) {
  useEffect(() => {
    const handleEvent = (event) => {
      callback(event.detail);
    };

    window.addEventListener(`chaeonix:${eventType}`, handleEvent);
    
    return () => {
      window.removeEventListener(`chaeonix:${eventType}`, handleEvent);
    };
  }, [eventType, callback]);
}

// Hook for engine-specific data
export function useCHAEONIXEngine(engineCode) {
  const { engineStatus, requestEngineAnalysis } = useCHAEONIXWebSocket();
  
  const engine = engineStatus[engineCode] || {
    status: 'unknown',
    confidence: 0,
    last_analysis: null,
    frequency: CHAEONIX_ENGINES[engineCode]?.frequency || 0,
    coupling_strength: CHAEONIX_ENGINES[engineCode]?.coupling_strength || 0
  };

  const analyzeEngine = (inputData) => {
    return requestEngineAnalysis(engineCode, inputData);
  };

  return {
    engine,
    analyzeEngine,
    isOperational: engine.status === 'operational',
    confidence: engine.confidence,
    lastAnalysis: engine.last_analysis
  };
}

export default useCHAEONIXWebSocket;
