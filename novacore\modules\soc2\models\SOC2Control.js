/**
 * NovaCore SOC 2 Control Model
 * 
 * This model defines the schema for SOC 2 controls.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define implementation status schema
const implementationStatusSchema = new Schema({
  status: { 
    type: String, 
    enum: ['not_implemented', 'partially_implemented', 'implemented', 'not_applicable'], 
    default: 'not_implemented' 
  },
  completeness: { 
    type: Number, 
    min: 0, 
    max: 100, 
    default: 0 
  },
  notes: { 
    type: String, 
    trim: true 
  },
  lastAssessedAt: { 
    type: Date 
  },
  lastAssessedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, { _id: false });

// Define evidence requirement schema
const evidenceRequirementSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: ['document', 'screenshot', 'configuration', 'log', 'report', 'other'], 
    default: 'document' 
  },
  required: { 
    type: Boolean, 
    default: true 
  },
  frequency: { 
    type: String, 
    enum: ['one_time', 'daily', 'weekly', 'monthly', 'quarterly', 'annually'], 
    default: 'one_time' 
  },
  automationPossible: { 
    type: Boolean, 
    default: false 
  },
  automationSource: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define SOC 2 control schema
const soc2ControlSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true, 
    unique: true 
  },
  reference: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true, 
    trim: true 
  },
  category: { 
    type: String, 
    required: true, 
    enum: [
      'control_environment', 
      'communication_information', 
      'risk_assessment', 
      'monitoring_activities', 
      'control_activities', 
      'logical_physical_access', 
      'system_operations', 
      'change_management', 
      'risk_mitigation'
    ]
  },
  trustServiceCriteria: [{ 
    type: String, 
    enum: ['security', 'availability', 'processing_integrity', 'confidentiality', 'privacy'], 
    default: ['security'] 
  }],
  implementationGuidance: { 
    type: String, 
    trim: true 
  },
  evidenceRequirements: [evidenceRequirementSchema],
  relatedControls: [{ 
    type: String, 
    trim: true 
  }],
  frameworks: [{ 
    framework: { 
      type: String, 
      required: true, 
      trim: true 
    },
    controls: [{ 
      type: String, 
      trim: true 
    }]
  }],
  status: { 
    type: String, 
    enum: ['active', 'deprecated'], 
    default: 'active' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Define organization-specific SOC 2 control implementation schema
const soc2ControlImplementationSchema = new Schema({
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  controlId: { 
    type: String, 
    required: true, 
    trim: true 
  },
  implementationStatus: { 
    type: implementationStatusSchema, 
    default: () => ({}) 
  },
  responsibleParty: { 
    type: String, 
    trim: true 
  },
  customPolicies: [{ 
    type: String, 
    trim: true 
  }],
  customProcedures: [{ 
    type: String, 
    trim: true 
  }],
  evidenceIds: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'SOC2Evidence' 
  }],
  notes: { 
    type: String, 
    trim: true 
  },
  lastUpdatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
soc2ControlSchema.index({ reference: 1 });
soc2ControlSchema.index({ category: 1 });
soc2ControlSchema.index({ trustServiceCriteria: 1 });
soc2ControlSchema.index({ status: 1 });

soc2ControlImplementationSchema.index({ organizationId: 1, controlId: 1 }, { unique: true });
soc2ControlImplementationSchema.index({ 'implementationStatus.status': 1 });
soc2ControlImplementationSchema.index({ responsibleParty: 1 });

// Add methods to implementation schema
soc2ControlImplementationSchema.methods.isImplemented = function() {
  return this.implementationStatus.status === 'implemented';
};

soc2ControlImplementationSchema.methods.getCompleteness = function() {
  return this.implementationStatus.completeness;
};

// Add statics to control schema
soc2ControlSchema.statics.findByCategory = function(category) {
  return this.find({ category, status: 'active' });
};

soc2ControlSchema.statics.findByTrustServiceCriteria = function(criteria) {
  return this.find({ 
    trustServiceCriteria: criteria, 
    status: 'active' 
  });
};

// Add statics to implementation schema
soc2ControlImplementationSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

soc2ControlImplementationSchema.statics.findImplemented = function(organizationId) {
  return this.find({ 
    organizationId, 
    'implementationStatus.status': 'implemented' 
  });
};

soc2ControlImplementationSchema.statics.findNotImplemented = function(organizationId) {
  return this.find({ 
    organizationId, 
    'implementationStatus.status': 'not_implemented' 
  });
};

// Create models
const SOC2Control = mongoose.model('SOC2Control', soc2ControlSchema);
const SOC2ControlImplementation = mongoose.model('SOC2ControlImplementation', soc2ControlImplementationSchema);

module.exports = {
  SOC2Control,
  SOC2ControlImplementation
};
