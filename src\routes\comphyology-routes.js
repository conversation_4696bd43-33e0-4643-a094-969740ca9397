/**
 * Advanced Analytics Engine API Routes
 *
 * This file contains the API routes for the Advanced Analytics Engine components,
 * including Analytics Engine, Multi-Layer Processing Framework, Resource Optimization Algorithm,
 * Tri-Factor Assessment Model, and Bounded Computation System.
 *
 * INTERNAL NOTE: These are obfuscated names for Comphyology Framework components:
 * - Advanced Analytics Engine = UUFT
 * - Multi-Layer Processing Framework = Nested Trinity
 * - Resource Optimization Algorithm = 18/82 Principle
 * - Tri-Factor Assessment Model = πφe Scoring System
 * - Bounded Computation System = Finite Universe Math
 */

const express = require('express');
const router = express.Router();
const { ComphyologyFrameworkIntegration } = require('../comphyology/integration/comphyology_framework_integration');
const uuftIntegration = require('../comphyology/integration/uuft_integration');
const nestedTrinityIntegration = require('../comphyology/integration/nested_trinity_integration');
const principle1882Integration = require('../comphyology/integration/principle_1882_integration');
const piPhiEScoringIntegration = require('../comphyology/integration/pi_phi_e_scoring_integration');
const finiteUniverseMathIntegration = require('../comphyology/integration/finite_universe_math_integration');
const componentAlignmentIntegration = require('../comphyology/integration/component_alignment_integration');

/**
 * @swagger
 * /api/system-analytics/status:
 *   get:
 *     summary: Get Advanced Analytics Engine status
 *     description: Returns the status of all Advanced Analytics Engine components
 *     tags: [SystemAnalytics]
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 components:
 *                   type: object
 *                 overall:
 *                   type: object
 */
router.get('/status', (req, res) => {
  res.json({
    status: 'active',
    components: {
      analyticsEngine: uuftIntegration.getPerformanceMetrics(),
      multiLayerProcessing: nestedTrinityIntegration.getPerformanceMetrics(),
      resourceOptimization: principle1882Integration.getPerformanceMetrics(),
      triFactorAssessment: piPhiEScoringIntegration.getPerformanceMetrics(),
      boundedComputation: finiteUniverseMathIntegration.getPerformanceMetrics(),
      integrationOrchestration: componentAlignmentIntegration.getPerformanceMetrics()
    },
    overall: req.comphyologyFramework ? req.comphyologyFramework.getPerformanceMetrics() : {}
  });
});

/**
 * @swagger
 * /api/system-analytics/engine/analyze:
 *   post:
 *     summary: Apply Advanced Analytics Engine
 *     description: Apply the Advanced Analytics Engine formula to the provided values
 *     tags: [SystemAnalytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - A
 *               - B
 *               - C
 *             properties:
 *               A:
 *                 type: number
 *               B:
 *                 type: number
 *               C:
 *                 type: number
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 result:
 *                   type: number
 */
router.post('/engine/analyze', (req, res) => {
  const { A, B, C } = req.body;

  if (typeof A !== 'number' || typeof B !== 'number' || typeof C !== 'number') {
    return res.status(400).json({ error: 'Invalid input. A, B, and C must be numbers.' });
  }

  try {
    // Internally still uses the UUFT equation
    const result = req.comphyologyFramework.applyUUFTEquation(A, B, C);
    res.json({ result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /api/system-analytics/resource/optimize:
 *   post:
 *     summary: Apply Resource Optimization Algorithm
 *     description: Apply the Resource Optimization Algorithm to the provided key and complementary components
 *     tags: [SystemAnalytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - keyComponent
 *               - complementaryComponent
 *             properties:
 *               keyComponent:
 *                 type: number
 *               complementaryComponent:
 *                 type: number
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 result:
 *                   type: number
 */
router.post('/resource/optimize', (req, res) => {
  const { keyComponent, complementaryComponent } = req.body;

  if (typeof keyComponent !== 'number' || typeof complementaryComponent !== 'number') {
    return res.status(400).json({ error: 'Invalid input. keyComponent and complementaryComponent must be numbers.' });
  }

  try {
    // Internally still uses the 18/82 Principle
    const result = req.comphyologyFramework.apply1882Principle(keyComponent, complementaryComponent);
    res.json({ result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /api/system-analytics/assessment/calculate:
 *   post:
 *     summary: Calculate Tri-Factor Assessment score
 *     description: Calculate the Tri-Factor Assessment score for the provided governance, resonance, and adaptation values
 *     tags: [SystemAnalytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - piScore
 *               - phiScore
 *               - eScore
 *             properties:
 *               piScore:
 *                 type: number
 *               phiScore:
 *                 type: number
 *               eScore:
 *                 type: number
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 result:
 *                   type: number
 */
router.post('/assessment/calculate', (req, res) => {
  // Rename parameters to use the obfuscated naming convention
  const { governanceScore, resonanceScore, adaptationScore } = req.body;

  // Map to internal parameter names
  const piScore = governanceScore;
  const phiScore = resonanceScore;
  const eScore = adaptationScore;

  if (typeof piScore !== 'number' || typeof phiScore !== 'number' || typeof eScore !== 'number') {
    return res.status(400).json({ error: 'Invalid input. governanceScore, resonanceScore, and adaptationScore must be numbers.' });
  }

  try {
    // Internally still uses the πφe Scoring System
    const result = req.comphyologyFramework.calculatePiPhiEScore(piScore, phiScore, eScore);
    res.json({ result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /api/system-analytics/multilayer/communicate:
 *   post:
 *     summary: Send cross-layer data
 *     description: Send data from one layer to another in the Multi-Layer Processing Framework
 *     tags: [SystemAnalytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sourceLayer
 *               - targetLayer
 *               - sourceId
 *               - data
 *             properties:
 *               sourceLayer:
 *                 type: string
 *                 enum: [micro, meso, macro]
 *               targetLayer:
 *                 type: string
 *                 enum: [micro, meso, macro]
 *               sourceId:
 *                 type: string
 *               data:
 *                 type: object
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
router.post('/multilayer/communicate', (req, res) => {
  // Rename parameters to use the obfuscated naming convention
  const { sourceLayer, targetLayer, sourceId, data } = req.body;

  if (!sourceLayer || !targetLayer || !sourceId || !data) {
    return res.status(400).json({ error: 'Invalid input. sourceLayer, targetLayer, sourceId, and data are required.' });
  }

  // Internally still uses the Nested Trinity layer names
  if (!['micro', 'meso', 'macro'].includes(sourceLayer) || !['micro', 'meso', 'macro'].includes(targetLayer)) {
    return res.status(400).json({ error: 'Invalid layer. sourceLayer and targetLayer must be one of: micro, meso, macro.' });
  }

  try {
    // Internally still uses the Nested Trinity integration
    const result = nestedTrinityIntegration.sendCrossLayerData(sourceLayer, targetLayer, sourceId, data);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * @swagger
 * /api/system-analytics/computation/applyBoundary:
 *   post:
 *     summary: Apply boundary to a value
 *     description: Apply a boundary condition to a value using the Bounded Computation System
 *     tags: [SystemAnalytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - value
 *               - boundaryType
 *             properties:
 *               value:
 *                 type: number
 *               boundaryType:
 *                 type: string
 *                 enum: [computational, temporal, spatial, energetic]
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 result:
 *                   type: number
 */
router.post('/computation/applyBoundary', (req, res) => {
  const { value, boundaryType } = req.body;

  if (typeof value !== 'number' || !boundaryType) {
    return res.status(400).json({ error: 'Invalid input. value must be a number and boundaryType is required.' });
  }

  // Internally still uses the Finite Universe Math boundary types
  if (!['computational', 'temporal', 'spatial', 'energetic'].includes(boundaryType)) {
    return res.status(400).json({ error: 'Invalid boundaryType. Must be one of: computational, temporal, spatial, energetic.' });
  }

  try {
    // Internally still uses the Finite Universe Math integration
    const result = finiteUniverseMathIntegration.boundedModels.applyBoundary(value, boundaryType);
    res.json({ result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
