<!DOCTYPE html>
<html>
<head>
    <title>TEE Calculator</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #2E7D32;
            --primary-dark: #1B5E20;
            --secondary: #1565C0;
            --accent: #FF8F00;
            --bg: #f5f7fa;
            --card: #ffffff;
            --text: #333;
            --text-light: #666;
            --border: #e0e0e0;
        }
        
        * { box-sizing: border-box; margin: 0; padding: 0; }
        
        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: var(--bg);
            color: var(--text);
            line-height: 1.6;
            padding: 20px;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--primary);
            color: white;
            border-radius: 10px;
        }
        
        h1 { font-size: 2em; margin-bottom: 10px; }
        .subtitle { opacity: 0.9; }
        
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: var(--card);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: var(--primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border);
        }
        
        .form-group { margin-bottom: 15px; }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input[type="number"],
        input[type="text"],
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 1em;
        }
        
        .slider-container {
            margin: 15px 0 5px;
        }
        
        .slider {
            width: 100%;
            margin: 10px 0;
        }
        
        .slider-value {
            display: inline-block;
            width: 30px;
            text-align: center;
            font-weight: bold;
            color: var(--primary);
        }
        
        button {
            background: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
            width: 100%;
            margin-top: 10px;
            transition: background 0.2s;
        }
        
        button:hover { background: var(--primary-dark); }
        
        .results {
            display: none;
            margin-top: 20px;
            padding: 15px;
            background: #f0f7f0;
            border-left: 4px solid var(--primary);
            border-radius: 4px;
        }
        
        .metric {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }
        
        .metric-title {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 5px;
        }
        
        .progress-bar {
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            margin-top: 5px;
            overflow: hidden;
        }
        
        .progress {
            height: 100%;
            background: var(--primary);
            width: 0%;
            transition: width 0.5s;
        }
        
        .chart-container {
            margin-top: 20px;
            height: 250px;
        }
        
        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>TEE Calculator</h1>
        <p class="subtitle">Time-Energy-Efficiency Optimization Tool</p>
    </header>
    
    <div class="container">
        <div class="card">
            <h2>Input Parameters</h2>
            
            <div class="form-group">
                <label for="taskName">Task/Activity Name</label>
                <input type="text" id="taskName" placeholder="e.g., Weekly Team Meeting">
            </div>
            
            <div class="form-group">
                <label for="timeSpent">Time Spent (hours)</label>
                <input type="number" id="timeSpent" min="0.1" step="0.1" value="2">
            </div>
            
            <div class="form-group">
                <label for="energyLevel">Energy Level (1-10)</label>
                <div class="slider-container">
                    <input type="range" min="1" max="10" value="5" class="slider" id="energyLevel">
                    <span class="slider-value" id="energyValue">5</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="efficiency">Efficiency (1-10)</label>
                <div class="slider-container">
                    <input type="range" min="1" max="10" value="6" class="slider" id="efficiency">
                    <span class="slider-value" id="efficiencyValue">6</span>
                </div>
            </div>
            
            <div class="form-group">
                <label for="friction">Friction (1-10)</label>
                <div class="slider-container">
                    <input type="range" min="1" max="10" value="4" class="slider" id="friction">
                    <span class="slider-value" id="frictionValue">4</span>
                </div>
            </div>
            
            <button id="calculateBtn">Calculate TEE Score</button>
        </div>
        
        <div class="card">
            <h2>Results</h2>
            <div id="results" class="results">
                <h3 id="resultTitle">TEE Analysis</h3>
                
                <div class="metric">
                    <div class="metric-title">TEE Score: <span id="teeScore">-</span></div>
                    <div class="progress-bar">
                        <div class="progress" id="teeProgress"></div>
                    </div>
                </div>
                
                <div class="metric">
                    <div class="metric-title">Efficiency: <span id="efficiencyScore">-</span></div>
                    <div class="progress-bar">
                        <div class="progress" id="effProgress"></div>
                    </div>
                </div>
                
                <div class="chart-container">
                    <canvas id="teeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // DOM Elements
        const taskName = document.getElementById('taskName');
        const timeSpent = document.getElementById('timeSpent');
        const energyLevel = document.getElementById('energyLevel');
        const energyValue = document.getElementById('energyValue');
        const efficiency = document.getElementById('efficiency');
        const efficiencyValue = document.getElementById('efficiencyValue');
        const friction = document.getElementById('friction');
        const frictionValue = document.getElementById('frictionValue');
        const calculateBtn = document.getElementById('calculateBtn');
        const results = document.getElementById('results');
        const resultTitle = document.getElementById('resultTitle');
        const teeScore = document.getElementById('teeScore');
        const efficiencyScore = document.getElementById('efficiencyScore');
        const teeProgress = document.getElementById('teeProgress');
        const effProgress = document.getElementById('effProgress');
        
        // Chart instance
        let teeChart = null;
        
        // Initialize sliders
        energyLevel.addEventListener('input', () => {
            energyValue.textContent = energyLevel.value;
        });
        
        efficiency.addEventListener('input', () => {
            efficiencyValue.textContent = efficiency.value;
        });
        
        friction.addEventListener('input', () => {
            frictionValue.textContent = friction.value;
        });
        
        // Calculate TEE
        calculateBtn.addEventListener('click', calculateTEE);
        
        function calculateTEE() {
            // Get values
            const task = taskName.value || 'Current Activity';
            const time = parseFloat(timeSpent.value) || 1;
            const energy = parseFloat(energyLevel.value) || 5;
            const eff = parseFloat(efficiency.value) / 10; // Convert to 0-1 scale
            const fric = parseFloat(friction.value) || 1;
            
            // Calculate TEE score (simplified formula)
            const rawTee = (time * energy * eff) - fric;
            const normalizedTee = Math.max(0, Math.min(100, rawTee)); // Clamp between 0-100
            
            // Update UI
            resultTitle.textContent = `TEE Analysis: ${task}`;
            teeScore.textContent = normalizedTee.toFixed(1);
            efficiencyScore.textContent = (eff * 100).toFixed(0) + '%';
            
            // Update progress bars
            teeProgress.style.width = `${normalizedTee}%`;
            effProgress.style.width = `${eff * 100}%`;
            
            // Show results
            results.style.display = 'block';
            
            // Update chart
            updateChart(energy, eff, fric);
            
            // Scroll to results
            results.scrollIntoView({ behavior: 'smooth' });
        }
        
        function updateChart(energy, efficiency, friction) {
            const ctx = document.getElementById('teeChart').getContext('2d');
            
            // Destroy previous chart if exists
            if (teeChart) {
                teeChart.destroy();
            }
            
            // Create new chart
            teeChart = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['Energy', 'Efficiency', 'Friction', 'Time', 'Output'],
                    datasets: [{
                        label: 'TEE Metrics',
                        data: [
                            energy * 10, // Scale to 100
                            efficiency * 100,
                            100 - (friction * 10), // Invert friction
                            parseFloat(timeSpent.value) * 5, // Scale time
                            (efficiency * 10) * (energy / 2) // Output score
                        ],
                        backgroundColor: 'rgba(46, 125, 50, 0.2)',
                        borderColor: 'rgba(46, 125, 50, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(46, 125, 50, 1)',
                        pointRadius: 4
                    }]
                },
                options: {
                    scale: {
                        angleLines: { display: true },
                        ticks: { beginAtZero: true, max: 100 },
                        pointLabels: { fontSize: 12 }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw}%`;
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // Initial calculation
        calculateTEE();
    </script>
</body>
</html>
