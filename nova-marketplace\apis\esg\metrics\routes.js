/**
 * ESG Metrics API - Routes
 *
 * This file defines the routes for the ESG Metrics API.
 */

const express = require('express');
const router = express.Router();
const { metricController, metricValueController, targetController } = require('./controllers');
const validate = require('../../../middleware/validate');
const validationSchemas = require('./validation');
const auth = require('../../../middleware/auth');

/**
 * @swagger
 * /esg/metrics/metrics:
 *   get:
 *     summary: Get a list of ESG metrics
 *     description: Returns a paginated list of ESG metrics with optional filtering
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by ESG category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance]
 *       - name: framework
 *         in: query
 *         description: Filter by ESG framework
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by metric status
 *         schema:
 *           type: string
 *           enum: [active, inactive, archived]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGMetric'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/metrics', controllers.getMetrics);

/**
 * @swagger
 * /esg/metrics/metrics/{id}:
 *   get:
 *     summary: Get a specific ESG metric
 *     description: Returns a specific ESG metric by ID
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGMetric'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/metrics/:id', controllers.getMetricById);

/**
 * @swagger
 * /esg/metrics/metrics:
 *   post:
 *     summary: Create a new ESG metric
 *     description: Creates a new ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGMetricInput'
 *     responses:
 *       201:
 *         description: ESG metric created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGMetric'
 *                 message:
 *                   type: string
 *                   example: ESG metric created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/metrics', validateRequest('createMetric'), controllers.createMetric);

/**
 * @swagger
 * /esg/metrics/metrics/{id}:
 *   put:
 *     summary: Update an ESG metric
 *     description: Updates an existing ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGMetricInput'
 *     responses:
 *       200:
 *         description: ESG metric updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGMetric'
 *                 message:
 *                   type: string
 *                   example: ESG metric updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/metrics/:id', validateRequest('updateMetric'), controllers.updateMetric);

/**
 * @swagger
 * /esg/metrics/metrics/{id}:
 *   delete:
 *     summary: Delete an ESG metric
 *     description: Deletes an existing ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: ESG metric deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG metric deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/metrics/:id', controllers.deleteMetric);

/**
 * @swagger
 * /esg/metrics/metrics/{id}/data-points:
 *   get:
 *     summary: Get data points for an ESG metric
 *     description: Returns data points for a specific ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: startDate
 *         in: query
 *         description: Filter by start date (YYYY-MM-DD)
 *         schema:
 *           type: string
 *       - name: endDate
 *         in: query
 *         description: Filter by end date (YYYY-MM-DD)
 *         schema:
 *           type: string
 *       - name: period
 *         in: query
 *         description: Filter by reporting period
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, annually]
 *       - name: verificationStatus
 *         in: query
 *         description: Filter by verification status
 *         schema:
 *           type: string
 *           enum: [unverified, verified, rejected]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGDataPoint'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/metrics/:id/data-points', controllers.getMetricDataPoints);

/**
 * @swagger
 * /esg/metrics/metrics/{id}/data-points:
 *   post:
 *     summary: Add a data point to an ESG metric
 *     description: Adds a new data point to an existing ESG metric
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG metric ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGDataPointInput'
 *     responses:
 *       201:
 *         description: Data point added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGDataPoint'
 *                 message:
 *                   type: string
 *                   example: Data point added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/metrics/:id/data-points', validateRequest('createDataPoint'), controllers.addDataPoint);

/**
 * @swagger
 * /esg/metrics/initiatives:
 *   get:
 *     summary: Get a list of ESG initiatives
 *     description: Returns a paginated list of ESG initiatives with optional filtering
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by ESG category
 *         schema:
 *           type: string
 *           enum: [environmental, social, governance]
 *       - name: status
 *         in: query
 *         description: Filter by initiative status
 *         schema:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ESGInitiative'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/initiatives', controllers.getInitiatives);

/**
 * @swagger
 * /esg/metrics/initiatives/{id}:
 *   get:
 *     summary: Get a specific ESG initiative
 *     description: Returns a specific ESG initiative by ID
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG initiative ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGInitiative'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/initiatives/:id', controllers.getInitiativeById);

/**
 * @swagger
 * /esg/metrics/initiatives:
 *   post:
 *     summary: Create a new ESG initiative
 *     description: Creates a new ESG initiative
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGInitiativeInput'
 *     responses:
 *       201:
 *         description: ESG initiative created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGInitiative'
 *                 message:
 *                   type: string
 *                   example: ESG initiative created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/initiatives', validateRequest('createInitiative'), controllers.createInitiative);

/**
 * @swagger
 * /esg/metrics/initiatives/{id}:
 *   put:
 *     summary: Update an ESG initiative
 *     description: Updates an existing ESG initiative
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG initiative ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ESGInitiativeInput'
 *     responses:
 *       200:
 *         description: ESG initiative updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ESGInitiative'
 *                 message:
 *                   type: string
 *                   example: ESG initiative updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/initiatives/:id', validateRequest('updateInitiative'), controllers.updateInitiative);

/**
 * @swagger
 * /esg/metrics/initiatives/{id}:
 *   delete:
 *     summary: Delete an ESG initiative
 *     description: Deletes an existing ESG initiative
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: ESG initiative ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: ESG initiative deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: ESG initiative deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/initiatives/:id', controllers.deleteInitiative);

/**
 * @swagger
 * /esg/metrics/categories:
 *   get:
 *     summary: Get ESG categories
 *     description: Returns a list of ESG categories
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       subcategories:
 *                         type: array
 *                         items:
 *                           type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/categories', controllers.getCategories);

/**
 * @swagger
 * /esg/metrics/frameworks:
 *   get:
 *     summary: Get ESG frameworks
 *     description: Returns a list of ESG frameworks
 *     tags: [ESG Metrics]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks', controllers.getFrameworks);

module.exports = router;
