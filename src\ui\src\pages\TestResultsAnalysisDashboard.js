import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import axios from 'axios';

// API base URL
const API_BASE_URL = '/api';

/**
 * Test Results Analysis Dashboard
 *
 * This dashboard displays analysis of user testing results.
 */
function TestResultsAnalysisDashboard() {
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);

  // State for test results
  const [testResults, setTestResults] = useState([]);

  // State for loading
  const [loading, setLoading] = useState(false);

  // State for error
  const [error, setError] = useState(null);

  // State for filters
  const [filters, setFilters] = useState({
    visualizationType: 'all',
    testType: 'all',
    timeRange: '30d'
  });

  // Load test results on component mount and when filters change
  useEffect(() => {
    loadTestResults();
  }, [filters]);

  // Load test results
  const loadTestResults = async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepare query parameters
      const params = {};

      if (filters.visualizationType !== 'all') {
        params.visualizationType = filters.visualizationType;
      }

      if (filters.testType !== 'all') {
        params.testType = filters.testType;
      }

      params.timeRange = filters.timeRange;

      // Fetch test results
      const response = await axios.get(`${API_BASE_URL}/user-testing/results/analysis`, { params });

      setTestResults(response.data);
    } catch (error) {
      console.error('Error loading test results:', error);
      setError('Failed to load test results. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Handle filter change
  const handleFilterChange = (field, value) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      [field]: value
    }));
  };

  // Handle refresh
  const handleRefresh = () => {
    loadTestResults();
  };

  // Handle export
  const handleExport = async () => {
    try {
      setLoading(true);

      // Prepare query parameters
      const params = { ...filters };

      // Fetch export
      const response = await axios.get(`${API_BASE_URL}/user-testing/results/export`, {
        params,
        responseType: 'blob'
      });

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `test-results-analysis-${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting test results:', error);
      setError('Failed to export test results. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Render filters
  const renderFilters = () => {
    return (
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Visualization Type</InputLabel>
          <Select
            value={filters.visualizationType}
            onChange={(e) => handleFilterChange('visualizationType', e.target.value)}
            label="Visualization Type"
          >
            <MenuItem value="all">All Types</MenuItem>
            <MenuItem value="triDomainTensor">Tri-Domain Tensor</MenuItem>
            <MenuItem value="harmonyIndex">Harmony Index</MenuItem>
            <MenuItem value="riskControlFusion">Risk-Control Fusion</MenuItem>
            <MenuItem value="resonanceSpectrogram">Resonance Spectrogram</MenuItem>
            <MenuItem value="unifiedComplianceSecurity">Unified Compliance-Security</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Test Type</InputLabel>
          <Select
            value={filters.testType}
            onChange={(e) => handleFilterChange('testType', e.target.value)}
            label="Test Type"
          >
            <MenuItem value="all">All Types</MenuItem>
            <MenuItem value="usability">Usability</MenuItem>
            <MenuItem value="comprehension">Comprehension</MenuItem>
            <MenuItem value="accessibility">Accessibility</MenuItem>
            <MenuItem value="performance">Performance</MenuItem>
            <MenuItem value="mobile">Mobile</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel>Time Range</InputLabel>
          <Select
            value={filters.timeRange}
            onChange={(e) => handleFilterChange('timeRange', e.target.value)}
            label="Time Range"
          >
            <MenuItem value="7d">Last 7 Days</MenuItem>
            <MenuItem value="30d">Last 30 Days</MenuItem>
            <MenuItem value="90d">Last 90 Days</MenuItem>
            <MenuItem value="all">All Time</MenuItem>
          </Select>
        </FormControl>

        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          Refresh
        </Button>

        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleExport}
          disabled={loading}
        >
          Export
        </Button>
      </Box>
    );
  };

  // Render overview tab
  const renderOverviewTab = () => {
    // Check if we have test results
    if (!testResults || !testResults.overview) {
      return (
        <Paper sx={{ p: 3 }}>
          <Typography variant="body1">
            No test results available for the selected filters.
          </Typography>
        </Paper>
      );
    }

    const { overview } = testResults;

    return (
      <Box>
        <Grid container spacing={3}>
          {/* Summary metrics */}
          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Total Sessions
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {overview.totalSessions}
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Participants
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {overview.uniqueParticipants}
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Avg. Success Rate
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {Math.round(overview.avgSuccessRate * 100)}%
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Avg. Satisfaction
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {overview.avgSatisfaction.toFixed(1)}/5
              </Typography>
            </Paper>
          </Grid>

          {/* Visualization type breakdown */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Sessions by Visualization Type
              </Typography>

              {overview.sessionsByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(overview.sessionsByVisualizationType).map(([type, count]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: 'primary.main',
                          borderRadius: 1,
                          width: `${(count / overview.totalSessions) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {count} ({Math.round((count / overview.totalSessions) * 100)}%)
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Test type breakdown */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Sessions by Test Type
              </Typography>

              {overview.sessionsByTestType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(overview.sessionsByTestType).map(([type, count]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: 'secondary.main',
                          borderRadius: 1,
                          width: `${(count / overview.totalSessions) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {count} ({Math.round((count / overview.totalSessions) * 100)}%)
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Success rate by visualization */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Success Rate by Visualization Type
              </Typography>

              {overview.successRateByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(overview.successRateByVisualizationType).map(([type, rate]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: rate >= 0.8 ? 'success.main' : rate >= 0.6 ? 'warning.main' : 'error.main',
                          borderRadius: 1,
                          width: `${rate * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {Math.round(rate * 100)}%
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render task analysis tab
  const renderTaskAnalysisTab = () => {
    // Check if we have test results
    if (!testResults || !testResults.taskAnalysis) {
      return (
        <Paper sx={{ p: 3 }}>
          <Typography variant="body1">
            No task analysis data available for the selected filters.
          </Typography>
        </Paper>
      );
    }

    const { taskAnalysis } = testResults;

    return (
      <Box>
        <Grid container spacing={3}>
          {/* Task completion metrics */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Task Completion Rate
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {Math.round(taskAnalysis.overallCompletionRate * 100)}%
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                {taskAnalysis.totalCompletedTasks} of {taskAnalysis.totalTasks} tasks completed
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Average Task Duration
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {taskAnalysis.avgTaskDuration}s
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Range: {taskAnalysis.minTaskDuration}s - {taskAnalysis.maxTaskDuration}s
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Help Requests
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {taskAnalysis.totalHelpRequests}
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                {Math.round(taskAnalysis.helpRequestRate * 100)}% of tasks needed help
              </Typography>
            </Paper>
          </Grid>

          {/* Task completion by visualization type */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Task Completion by Visualization Type
              </Typography>

              {taskAnalysis.completionRateByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(taskAnalysis.completionRateByVisualizationType).map(([type, rate]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: rate >= 0.8 ? 'success.main' : rate >= 0.6 ? 'warning.main' : 'error.main',
                          borderRadius: 1,
                          width: `${rate * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {Math.round(rate * 100)}%
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Task duration by visualization type */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Task Duration by Visualization Type
              </Typography>

              {taskAnalysis.durationByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(taskAnalysis.durationByVisualizationType).map(([type, duration]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: 'info.main',
                          borderRadius: 1,
                          width: `${(duration / taskAnalysis.maxTaskDuration) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {duration}s
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Task difficulty analysis */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Task Difficulty Analysis
              </Typography>

              {taskAnalysis.taskDifficultyAnalysis && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    {Object.entries(taskAnalysis.taskDifficultyAnalysis).map(([taskId, analysis]) => (
                      <Grid item xs={12} key={taskId}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle1" gutterBottom>
                            {analysis.taskName}
                          </Typography>

                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={3}>
                              <Typography variant="body2" color="text.secondary">
                                Completion Rate:
                              </Typography>
                              <Typography variant="body1">
                                {Math.round(analysis.completionRate * 100)}%
                              </Typography>
                            </Grid>

                            <Grid item xs={12} sm={3}>
                              <Typography variant="body2" color="text.secondary">
                                Avg. Duration:
                              </Typography>
                              <Typography variant="body1">
                                {analysis.avgDuration}s
                              </Typography>
                            </Grid>

                            <Grid item xs={12} sm={3}>
                              <Typography variant="body2" color="text.secondary">
                                Help Requests:
                              </Typography>
                              <Typography variant="body1">
                                {analysis.helpRequests}
                              </Typography>
                            </Grid>

                            <Grid item xs={12} sm={3}>
                              <Typography variant="body2" color="text.secondary">
                                Difficulty Rating:
                              </Typography>
                              <Typography variant="body1">
                                {analysis.difficultyRating.toFixed(1)}/5
                              </Typography>
                            </Grid>
                          </Grid>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render feedback analysis tab
  const renderFeedbackAnalysisTab = () => {
    // Check if we have test results
    if (!testResults || !testResults.feedbackAnalysis) {
      return (
        <Paper sx={{ p: 3 }}>
          <Typography variant="body1">
            No feedback analysis data available for the selected filters.
          </Typography>
        </Paper>
      );
    }

    const { feedbackAnalysis } = testResults;

    return (
      <Box>
        <Grid container spacing={3}>
          {/* Overall satisfaction metrics */}
          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Overall Satisfaction
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {feedbackAnalysis.overallSatisfaction.toFixed(1)}/5
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Usability Rating
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {feedbackAnalysis.usabilityRating.toFixed(1)}/5
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Visual Appeal
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {feedbackAnalysis.visualAppealRating.toFixed(1)}/5
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={3}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Recommendation Score
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {feedbackAnalysis.recommendationScore.toFixed(1)}/5
              </Typography>
            </Paper>
          </Grid>

          {/* Satisfaction by visualization type */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Satisfaction by Visualization Type
              </Typography>

              {feedbackAnalysis.satisfactionByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(feedbackAnalysis.satisfactionByVisualizationType).map(([type, rating]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: rating >= 4 ? 'success.main' : rating >= 3 ? 'warning.main' : 'error.main',
                          borderRadius: 1,
                          width: `${(rating / 5) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {rating.toFixed(1)}/5
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Usability by visualization type */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Usability by Visualization Type
              </Typography>

              {feedbackAnalysis.usabilityByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(feedbackAnalysis.usabilityByVisualizationType).map(([type, rating]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: rating >= 4 ? 'success.main' : rating >= 3 ? 'warning.main' : 'error.main',
                          borderRadius: 1,
                          width: `${(rating / 5) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {rating.toFixed(1)}/5
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Common feedback themes */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Common Feedback Themes
              </Typography>

              {feedbackAnalysis.commonThemes && (
                <Grid container spacing={2} sx={{ mt: 1 }}>
                  {Object.entries(feedbackAnalysis.commonThemes).map(([theme, count]) => (
                    <Grid item xs={12} sm={6} md={4} key={theme}>
                      <Paper variant="outlined" sx={{ p: 2 }}>
                        <Typography variant="subtitle1" gutterBottom>
                          {theme}
                        </Typography>
                        <Typography variant="h4" align="center" sx={{ my: 1 }}>
                          {count}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" align="center">
                          mentions
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Paper>
          </Grid>

          {/* Improvement suggestions */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Top Improvement Suggestions
              </Typography>

              {feedbackAnalysis.improvementSuggestions && (
                <Box sx={{ mt: 2 }}>
                  {feedbackAnalysis.improvementSuggestions.map((suggestion, index) => (
                    <Paper variant="outlined" sx={{ p: 2, mb: 2 }} key={index}>
                      <Typography variant="subtitle1" gutterBottom>
                        {suggestion.category}
                      </Typography>
                      <Typography variant="body1">
                        "{suggestion.text}"
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        Visualization: {suggestion.visualizationType} | Test Type: {suggestion.testType}
                      </Typography>
                    </Paper>
                  ))}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render interaction analysis tab
  const renderInteractionAnalysisTab = () => {
    // Check if we have test results
    if (!testResults || !testResults.interactionAnalysis) {
      return (
        <Paper sx={{ p: 3 }}>
          <Typography variant="body1">
            No interaction analysis data available for the selected filters.
          </Typography>
        </Paper>
      );
    }

    const { interactionAnalysis } = testResults;

    return (
      <Box>
        <Grid container spacing={3}>
          {/* Interaction metrics */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Total Interactions
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {interactionAnalysis.totalInteractions}
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Avg. {interactionAnalysis.avgInteractionsPerSession} per session
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Most Common Interaction
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {interactionAnalysis.mostCommonInteraction.type}
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                {interactionAnalysis.mostCommonInteraction.count} occurrences
              </Typography>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" gutterBottom>
                Interaction Diversity
              </Typography>
              <Typography variant="h3" align="center" sx={{ my: 2 }}>
                {interactionAnalysis.interactionDiversity.toFixed(2)}
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Higher values indicate more diverse interactions
              </Typography>
            </Paper>
          </Grid>

          {/* Interaction types breakdown */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Interaction Types
              </Typography>

              {interactionAnalysis.interactionTypeBreakdown && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(interactionAnalysis.interactionTypeBreakdown).map(([type, count]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: 'primary.main',
                          borderRadius: 1,
                          width: `${(count / interactionAnalysis.totalInteractions) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {count} ({Math.round((count / interactionAnalysis.totalInteractions) * 100)}%)
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Interactions by visualization type */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Interactions by Visualization Type
              </Typography>

              {interactionAnalysis.interactionsByVisualizationType && (
                <Box sx={{ height: 300 }}>
                  {/* This would be a chart in a real implementation */}
                  <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                    {Object.entries(interactionAnalysis.interactionsByVisualizationType).map(([type, count]) => (
                      <Box key={type} sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Typography variant="body2" sx={{ width: 200 }}>
                          {type}:
                        </Typography>
                        <Box sx={{
                          flexGrow: 1,
                          height: 20,
                          bgcolor: 'secondary.main',
                          borderRadius: 1,
                          width: `${(count / interactionAnalysis.totalInteractions) * 100}%`
                        }} />
                        <Typography variant="body2" sx={{ ml: 2 }}>
                          {count} ({Math.round((count / interactionAnalysis.totalInteractions) * 100)}%)
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Interaction patterns */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Common Interaction Patterns
              </Typography>

              {interactionAnalysis.commonPatterns && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    {interactionAnalysis.commonPatterns.map((pattern, index) => (
                      <Grid item xs={12} key={index}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle1" gutterBottom>
                            Pattern #{index + 1}
                          </Typography>

                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                            {pattern.sequence.map((step, stepIndex) => (
                              <Box key={stepIndex} sx={{ display: 'flex', alignItems: 'center' }}>
                                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                                  {step.type}
                                </Typography>
                                {stepIndex < pattern.sequence.length - 1 && (
                                  <Typography variant="body2" sx={{ mx: 1 }}>→</Typography>
                                )}
                              </Box>
                            ))}
                          </Box>

                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" color="text.secondary">
                                Frequency:
                              </Typography>
                              <Typography variant="body1">
                                {pattern.frequency} occurrences
                              </Typography>
                            </Grid>

                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" color="text.secondary">
                                Avg. Duration:
                              </Typography>
                              <Typography variant="body1">
                                {pattern.avgDuration}s
                              </Typography>
                            </Grid>

                            <Grid item xs={12} sm={4}>
                              <Typography variant="body2" color="text.secondary">
                                Success Rate:
                              </Typography>
                              <Typography variant="body1">
                                {Math.round(pattern.successRate * 100)}%
                              </Typography>
                            </Grid>
                          </Grid>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Interaction heatmap */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Interaction Heatmap Summary
              </Typography>

              {interactionAnalysis.heatmapSummary && (
                <Box sx={{ mt: 2 }}>
                  <Grid container spacing={2}>
                    {Object.entries(interactionAnalysis.heatmapSummary).map(([visualizationType, areas]) => (
                      <Grid item xs={12} md={6} key={visualizationType}>
                        <Paper variant="outlined" sx={{ p: 2 }}>
                          <Typography variant="subtitle1" gutterBottom>
                            {visualizationType}
                          </Typography>

                          <Typography variant="body2" sx={{ mb: 2 }}>
                            Top interaction areas:
                          </Typography>

                          <Box>
                            {areas.map((area, index) => (
                              <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                <Typography variant="body2">
                                  {area.name}:
                                </Typography>
                                <Typography variant="body2" fontWeight="bold">
                                  {area.interactionCount} interactions
                                </Typography>
                              </Box>
                            ))}
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Test Results Analysis
      </Typography>

      {renderFilters()}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : (
        <>
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
            <Tab label="Overview" />
            <Tab label="Task Analysis" />
            <Tab label="Feedback Analysis" />
            <Tab label="Interaction Analysis" />
          </Tabs>

          {activeTab === 0 && renderOverviewTab()}
          {activeTab === 1 && renderTaskAnalysisTab()}
          {activeTab === 2 && renderFeedbackAnalysisTab()}
          {activeTab === 3 && renderInteractionAnalysisTab()}
        </>
      )}
    </Box>
  );
}

export default TestResultsAnalysisDashboard;
