db2813a2d7670528dfdce1f11d8fcc08
/**
 * Package Controller
 * 
 * This controller handles package management operations.
 */

const FeatureFlagService = require('../services/FeatureFlagService');
const {
  ValidationError,
  NotFoundError,
  AuthorizationError
} = require('../utils/errors');

// Initialize services
const featureFlagService = new FeatureFlagService();

/**
 * Get all packages
 */
const getAllPackages = async (req, res, next) => {
  try {
    const packages = await featureFlagService.getAllPackages();
    res.json(packages);
  } catch (error) {
    next(error);
  }
};

/**
 * Get package by ID
 */
const getPackageById = async (req, res, next) => {
  try {
    const {
      id
    } = req.params;
    const pkg = await featureFlagService.getPackageById(id);
    res.json(pkg);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Create a new package
 */
const createPackage = async (req, res, next) => {
  try {
    const packageData = req.body;

    // Validate required fields
    if (!packageData.id) {
      throw new ValidationError('Package ID is required');
    }
    if (!packageData.name) {
      throw new ValidationError('Package name is required');
    }
    if (!packageData.tier) {
      throw new ValidationError('Package tier is required');
    }
    if (!packageData.features || !Array.isArray(packageData.features)) {
      throw new ValidationError('Package features must be an array');
    }
    if (!packageData.limits || typeof packageData.limits !== 'object') {
      throw new ValidationError('Package limits must be an object');
    }
    const newPackage = await featureFlagService.createPackage(packageData);
    res.status(201).json(newPackage);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Update a package
 */
const updatePackage = async (req, res, next) => {
  try {
    const {
      id
    } = req.params;
    const packageData = req.body;
    const updatedPackage = await featureFlagService.updatePackage(id, packageData);
    res.json(updatedPackage);
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Delete a package
 */
const deletePackage = async (req, res, next) => {
  try {
    const {
      id
    } = req.params;
    await featureFlagService.deletePackage(id);
    res.status(204).end();
  } catch (error) {
    if (error.message.includes('not found')) {
      return res.status(404).json({
        error: 'Not Found',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Get tenant package
 */
const getTenantPackage = async (req, res, next) => {
  try {
    const {
      tenantId
    } = req.params;
    const pkg = await featureFlagService.getTenantPackage(tenantId);
    res.json(pkg);
  } catch (error) {
    next(error);
  }
};

/**
 * Set tenant package
 */
const setTenantPackage = async (req, res, next) => {
  try {
    const {
      tenantId
    } = req.params;
    const {
      packageId,
      customFeatures,
      customLimits
    } = req.body;

    // Validate required fields
    if (!packageId) {
      throw new ValidationError('Package ID is required');
    }
    const mapping = await featureFlagService.setTenantPackage(tenantId, packageId, customFeatures || [], customLimits || {});
    res.json(mapping);
  } catch (error) {
    if (error instanceof ValidationError) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.message
      });
    }
    next(error);
  }
};

/**
 * Clear cache
 */
const clearCache = async (req, res, next) => {
  try {
    featureFlagService.clearCache();
    res.json({
      message: 'Cache cleared successfully'
    });
  } catch (error) {
    next(error);
  }
};
module.exports = {
  getAllPackages,
  getPackageById,
  createPackage,
  updatePackage,
  deletePackage,
  getTenantPackage,
  setTenantPackage,
  clearCache
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJGZWF0dXJlRmxhZ1NlcnZpY2UiLCJyZXF1aXJlIiwiVmFsaWRhdGlvbkVycm9yIiwiTm90Rm91bmRFcnJvciIsIkF1dGhvcml6YXRpb25FcnJvciIsImZlYXR1cmVGbGFnU2VydmljZSIsImdldEFsbFBhY2thZ2VzIiwicmVxIiwicmVzIiwibmV4dCIsInBhY2thZ2VzIiwianNvbiIsImVycm9yIiwiZ2V0UGFja2FnZUJ5SWQiLCJpZCIsInBhcmFtcyIsInBrZyIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsInN0YXR1cyIsImNyZWF0ZVBhY2thZ2UiLCJwYWNrYWdlRGF0YSIsImJvZHkiLCJuYW1lIiwidGllciIsImZlYXR1cmVzIiwiQXJyYXkiLCJpc0FycmF5IiwibGltaXRzIiwibmV3UGFja2FnZSIsInVwZGF0ZVBhY2thZ2UiLCJ1cGRhdGVkUGFja2FnZSIsImRlbGV0ZVBhY2thZ2UiLCJlbmQiLCJnZXRUZW5hbnRQYWNrYWdlIiwidGVuYW50SWQiLCJzZXRUZW5hbnRQYWNrYWdlIiwicGFja2FnZUlkIiwiY3VzdG9tRmVhdHVyZXMiLCJjdXN0b21MaW1pdHMiLCJtYXBwaW5nIiwiY2xlYXJDYWNoZSIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlcyI6WyJQYWNrYWdlQ29udHJvbGxlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFBhY2thZ2UgQ29udHJvbGxlclxuICogXG4gKiBUaGlzIGNvbnRyb2xsZXIgaGFuZGxlcyBwYWNrYWdlIG1hbmFnZW1lbnQgb3BlcmF0aW9ucy5cbiAqL1xuXG5jb25zdCBGZWF0dXJlRmxhZ1NlcnZpY2UgPSByZXF1aXJlKCcuLi9zZXJ2aWNlcy9GZWF0dXJlRmxhZ1NlcnZpY2UnKTtcbmNvbnN0IHsgVmFsaWRhdGlvbkVycm9yLCBOb3RGb3VuZEVycm9yLCBBdXRob3JpemF0aW9uRXJyb3IgfSA9IHJlcXVpcmUoJy4uL3V0aWxzL2Vycm9ycycpO1xuXG4vLyBJbml0aWFsaXplIHNlcnZpY2VzXG5jb25zdCBmZWF0dXJlRmxhZ1NlcnZpY2UgPSBuZXcgRmVhdHVyZUZsYWdTZXJ2aWNlKCk7XG5cbi8qKlxuICogR2V0IGFsbCBwYWNrYWdlc1xuICovXG5jb25zdCBnZXRBbGxQYWNrYWdlcyA9IGFzeW5jIChyZXEsIHJlcywgbmV4dCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHBhY2thZ2VzID0gYXdhaXQgZmVhdHVyZUZsYWdTZXJ2aWNlLmdldEFsbFBhY2thZ2VzKCk7XG4gICAgcmVzLmpzb24ocGFja2FnZXMpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIG5leHQoZXJyb3IpO1xuICB9XG59O1xuXG4vKipcbiAqIEdldCBwYWNrYWdlIGJ5IElEXG4gKi9cbmNvbnN0IGdldFBhY2thZ2VCeUlkID0gYXN5bmMgKHJlcSwgcmVzLCBuZXh0KSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBpZCB9ID0gcmVxLnBhcmFtcztcbiAgICBjb25zdCBwa2cgPSBhd2FpdCBmZWF0dXJlRmxhZ1NlcnZpY2UuZ2V0UGFja2FnZUJ5SWQoaWQpO1xuICAgIHJlcy5qc29uKHBrZyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ25vdCBmb3VuZCcpKSB7XG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDQpLmpzb24oe1xuICAgICAgICBlcnJvcjogJ05vdCBGb3VuZCcsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICAgIH0pO1xuICAgIH1cbiAgICBuZXh0KGVycm9yKTtcbiAgfVxufTtcblxuLyoqXG4gKiBDcmVhdGUgYSBuZXcgcGFja2FnZVxuICovXG5jb25zdCBjcmVhdGVQYWNrYWdlID0gYXN5bmMgKHJlcSwgcmVzLCBuZXh0KSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcGFja2FnZURhdGEgPSByZXEuYm9keTtcbiAgICBcbiAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICBpZiAoIXBhY2thZ2VEYXRhLmlkKSB7XG4gICAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKCdQYWNrYWdlIElEIGlzIHJlcXVpcmVkJyk7XG4gICAgfVxuICAgIFxuICAgIGlmICghcGFja2FnZURhdGEubmFtZSkge1xuICAgICAgdGhyb3cgbmV3IFZhbGlkYXRpb25FcnJvcignUGFja2FnZSBuYW1lIGlzIHJlcXVpcmVkJyk7XG4gICAgfVxuICAgIFxuICAgIGlmICghcGFja2FnZURhdGEudGllcikge1xuICAgICAgdGhyb3cgbmV3IFZhbGlkYXRpb25FcnJvcignUGFja2FnZSB0aWVyIGlzIHJlcXVpcmVkJyk7XG4gICAgfVxuICAgIFxuICAgIGlmICghcGFja2FnZURhdGEuZmVhdHVyZXMgfHwgIUFycmF5LmlzQXJyYXkocGFja2FnZURhdGEuZmVhdHVyZXMpKSB7XG4gICAgICB0aHJvdyBuZXcgVmFsaWRhdGlvbkVycm9yKCdQYWNrYWdlIGZlYXR1cmVzIG11c3QgYmUgYW4gYXJyYXknKTtcbiAgICB9XG4gICAgXG4gICAgaWYgKCFwYWNrYWdlRGF0YS5saW1pdHMgfHwgdHlwZW9mIHBhY2thZ2VEYXRhLmxpbWl0cyAhPT0gJ29iamVjdCcpIHtcbiAgICAgIHRocm93IG5ldyBWYWxpZGF0aW9uRXJyb3IoJ1BhY2thZ2UgbGltaXRzIG11c3QgYmUgYW4gb2JqZWN0Jyk7XG4gICAgfVxuICAgIFxuICAgIGNvbnN0IG5ld1BhY2thZ2UgPSBhd2FpdCBmZWF0dXJlRmxhZ1NlcnZpY2UuY3JlYXRlUGFja2FnZShwYWNrYWdlRGF0YSk7XG4gICAgcmVzLnN0YXR1cygyMDEpLmpzb24obmV3UGFja2FnZSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVmFsaWRhdGlvbkVycm9yKSB7XG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oe1xuICAgICAgICBlcnJvcjogJ1ZhbGlkYXRpb24gRXJyb3InLFxuICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlXG4gICAgICB9KTtcbiAgICB9XG4gICAgbmV4dChlcnJvcik7XG4gIH1cbn07XG5cbi8qKlxuICogVXBkYXRlIGEgcGFja2FnZVxuICovXG5jb25zdCB1cGRhdGVQYWNrYWdlID0gYXN5bmMgKHJlcSwgcmVzLCBuZXh0KSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgeyBpZCB9ID0gcmVxLnBhcmFtcztcbiAgICBjb25zdCBwYWNrYWdlRGF0YSA9IHJlcS5ib2R5O1xuICAgIFxuICAgIGNvbnN0IHVwZGF0ZWRQYWNrYWdlID0gYXdhaXQgZmVhdHVyZUZsYWdTZXJ2aWNlLnVwZGF0ZVBhY2thZ2UoaWQsIHBhY2thZ2VEYXRhKTtcbiAgICByZXMuanNvbih1cGRhdGVkUGFja2FnZSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ25vdCBmb3VuZCcpKSB7XG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDQpLmpzb24oe1xuICAgICAgICBlcnJvcjogJ05vdCBGb3VuZCcsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICAgIH0pO1xuICAgIH1cbiAgICBuZXh0KGVycm9yKTtcbiAgfVxufTtcblxuLyoqXG4gKiBEZWxldGUgYSBwYWNrYWdlXG4gKi9cbmNvbnN0IGRlbGV0ZVBhY2thZ2UgPSBhc3luYyAocmVxLCByZXMsIG5leHQpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IGlkIH0gPSByZXEucGFyYW1zO1xuICAgIGF3YWl0IGZlYXR1cmVGbGFnU2VydmljZS5kZWxldGVQYWNrYWdlKGlkKTtcbiAgICByZXMuc3RhdHVzKDIwNCkuZW5kKCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgaWYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ25vdCBmb3VuZCcpKSB7XG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDQpLmpzb24oe1xuICAgICAgICBlcnJvcjogJ05vdCBGb3VuZCcsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICAgIH0pO1xuICAgIH1cbiAgICBuZXh0KGVycm9yKTtcbiAgfVxufTtcblxuLyoqXG4gKiBHZXQgdGVuYW50IHBhY2thZ2VcbiAqL1xuY29uc3QgZ2V0VGVuYW50UGFja2FnZSA9IGFzeW5jIChyZXEsIHJlcywgbmV4dCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHsgdGVuYW50SWQgfSA9IHJlcS5wYXJhbXM7XG4gICAgY29uc3QgcGtnID0gYXdhaXQgZmVhdHVyZUZsYWdTZXJ2aWNlLmdldFRlbmFudFBhY2thZ2UodGVuYW50SWQpO1xuICAgIHJlcy5qc29uKHBrZyk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbmV4dChlcnJvcik7XG4gIH1cbn07XG5cbi8qKlxuICogU2V0IHRlbmFudCBwYWNrYWdlXG4gKi9cbmNvbnN0IHNldFRlbmFudFBhY2thZ2UgPSBhc3luYyAocmVxLCByZXMsIG5leHQpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHRlbmFudElkIH0gPSByZXEucGFyYW1zO1xuICAgIGNvbnN0IHsgcGFja2FnZUlkLCBjdXN0b21GZWF0dXJlcywgY3VzdG9tTGltaXRzIH0gPSByZXEuYm9keTtcbiAgICBcbiAgICAvLyBWYWxpZGF0ZSByZXF1aXJlZCBmaWVsZHNcbiAgICBpZiAoIXBhY2thZ2VJZCkge1xuICAgICAgdGhyb3cgbmV3IFZhbGlkYXRpb25FcnJvcignUGFja2FnZSBJRCBpcyByZXF1aXJlZCcpO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCBtYXBwaW5nID0gYXdhaXQgZmVhdHVyZUZsYWdTZXJ2aWNlLnNldFRlbmFudFBhY2thZ2UoXG4gICAgICB0ZW5hbnRJZCxcbiAgICAgIHBhY2thZ2VJZCxcbiAgICAgIGN1c3RvbUZlYXR1cmVzIHx8IFtdLFxuICAgICAgY3VzdG9tTGltaXRzIHx8IHt9XG4gICAgKTtcbiAgICBcbiAgICByZXMuanNvbihtYXBwaW5nKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBWYWxpZGF0aW9uRXJyb3IpIHtcbiAgICAgIHJldHVybiByZXMuc3RhdHVzKDQwMCkuanNvbih7XG4gICAgICAgIGVycm9yOiAnVmFsaWRhdGlvbiBFcnJvcicsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICAgIH0pO1xuICAgIH1cbiAgICBuZXh0KGVycm9yKTtcbiAgfVxufTtcblxuLyoqXG4gKiBDbGVhciBjYWNoZVxuICovXG5jb25zdCBjbGVhckNhY2hlID0gYXN5bmMgKHJlcSwgcmVzLCBuZXh0KSA9PiB7XG4gIHRyeSB7XG4gICAgZmVhdHVyZUZsYWdTZXJ2aWNlLmNsZWFyQ2FjaGUoKTtcbiAgICByZXMuanNvbih7IG1lc3NhZ2U6ICdDYWNoZSBjbGVhcmVkIHN1Y2Nlc3NmdWxseScgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbmV4dChlcnJvcik7XG4gIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBnZXRBbGxQYWNrYWdlcyxcbiAgZ2V0UGFja2FnZUJ5SWQsXG4gIGNyZWF0ZVBhY2thZ2UsXG4gIHVwZGF0ZVBhY2thZ2UsXG4gIGRlbGV0ZVBhY2thZ2UsXG4gIGdldFRlbmFudFBhY2thZ2UsXG4gIHNldFRlbmFudFBhY2thZ2UsXG4gIGNsZWFyQ2FjaGVcbn07XG4iXSwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTUEsa0JBQWtCLEdBQUdDLE9BQU8sQ0FBQyxnQ0FBZ0MsQ0FBQztBQUNwRSxNQUFNO0VBQUVDLGVBQWU7RUFBRUMsYUFBYTtFQUFFQztBQUFtQixDQUFDLEdBQUdILE9BQU8sQ0FBQyxpQkFBaUIsQ0FBQzs7QUFFekY7QUFDQSxNQUFNSSxrQkFBa0IsR0FBRyxJQUFJTCxrQkFBa0IsQ0FBQyxDQUFDOztBQUVuRDtBQUNBO0FBQ0E7QUFDQSxNQUFNTSxjQUFjLEdBQUcsTUFBQUEsQ0FBT0MsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLElBQUksS0FBSztFQUMvQyxJQUFJO0lBQ0YsTUFBTUMsUUFBUSxHQUFHLE1BQU1MLGtCQUFrQixDQUFDQyxjQUFjLENBQUMsQ0FBQztJQUMxREUsR0FBRyxDQUFDRyxJQUFJLENBQUNELFFBQVEsQ0FBQztFQUNwQixDQUFDLENBQUMsT0FBT0UsS0FBSyxFQUFFO0lBQ2RILElBQUksQ0FBQ0csS0FBSyxDQUFDO0VBQ2I7QUFDRixDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBLE1BQU1DLGNBQWMsR0FBRyxNQUFBQSxDQUFPTixHQUFHLEVBQUVDLEdBQUcsRUFBRUMsSUFBSSxLQUFLO0VBQy9DLElBQUk7SUFDRixNQUFNO01BQUVLO0lBQUcsQ0FBQyxHQUFHUCxHQUFHLENBQUNRLE1BQU07SUFDekIsTUFBTUMsR0FBRyxHQUFHLE1BQU1YLGtCQUFrQixDQUFDUSxjQUFjLENBQUNDLEVBQUUsQ0FBQztJQUN2RE4sR0FBRyxDQUFDRyxJQUFJLENBQUNLLEdBQUcsQ0FBQztFQUNmLENBQUMsQ0FBQyxPQUFPSixLQUFLLEVBQUU7SUFDZCxJQUFJQSxLQUFLLENBQUNLLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFdBQVcsQ0FBQyxFQUFFO01BQ3ZDLE9BQU9WLEdBQUcsQ0FBQ1csTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUFDUixJQUFJLENBQUM7UUFDMUJDLEtBQUssRUFBRSxXQUFXO1FBQ2xCSyxPQUFPLEVBQUVMLEtBQUssQ0FBQ0s7TUFDakIsQ0FBQyxDQUFDO0lBQ0o7SUFDQVIsSUFBSSxDQUFDRyxLQUFLLENBQUM7RUFDYjtBQUNGLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0EsTUFBTVEsYUFBYSxHQUFHLE1BQUFBLENBQU9iLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxJQUFJLEtBQUs7RUFDOUMsSUFBSTtJQUNGLE1BQU1ZLFdBQVcsR0FBR2QsR0FBRyxDQUFDZSxJQUFJOztJQUU1QjtJQUNBLElBQUksQ0FBQ0QsV0FBVyxDQUFDUCxFQUFFLEVBQUU7TUFDbkIsTUFBTSxJQUFJWixlQUFlLENBQUMsd0JBQXdCLENBQUM7SUFDckQ7SUFFQSxJQUFJLENBQUNtQixXQUFXLENBQUNFLElBQUksRUFBRTtNQUNyQixNQUFNLElBQUlyQixlQUFlLENBQUMsMEJBQTBCLENBQUM7SUFDdkQ7SUFFQSxJQUFJLENBQUNtQixXQUFXLENBQUNHLElBQUksRUFBRTtNQUNyQixNQUFNLElBQUl0QixlQUFlLENBQUMsMEJBQTBCLENBQUM7SUFDdkQ7SUFFQSxJQUFJLENBQUNtQixXQUFXLENBQUNJLFFBQVEsSUFBSSxDQUFDQyxLQUFLLENBQUNDLE9BQU8sQ0FBQ04sV0FBVyxDQUFDSSxRQUFRLENBQUMsRUFBRTtNQUNqRSxNQUFNLElBQUl2QixlQUFlLENBQUMsbUNBQW1DLENBQUM7SUFDaEU7SUFFQSxJQUFJLENBQUNtQixXQUFXLENBQUNPLE1BQU0sSUFBSSxPQUFPUCxXQUFXLENBQUNPLE1BQU0sS0FBSyxRQUFRLEVBQUU7TUFDakUsTUFBTSxJQUFJMUIsZUFBZSxDQUFDLGtDQUFrQyxDQUFDO0lBQy9EO0lBRUEsTUFBTTJCLFVBQVUsR0FBRyxNQUFNeEIsa0JBQWtCLENBQUNlLGFBQWEsQ0FBQ0MsV0FBVyxDQUFDO0lBQ3RFYixHQUFHLENBQUNXLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQ1IsSUFBSSxDQUFDa0IsVUFBVSxDQUFDO0VBQ2xDLENBQUMsQ0FBQyxPQUFPakIsS0FBSyxFQUFFO0lBQ2QsSUFBSUEsS0FBSyxZQUFZVixlQUFlLEVBQUU7TUFDcEMsT0FBT00sR0FBRyxDQUFDVyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUNSLElBQUksQ0FBQztRQUMxQkMsS0FBSyxFQUFFLGtCQUFrQjtRQUN6QkssT0FBTyxFQUFFTCxLQUFLLENBQUNLO01BQ2pCLENBQUMsQ0FBQztJQUNKO0lBQ0FSLElBQUksQ0FBQ0csS0FBSyxDQUFDO0VBQ2I7QUFDRixDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBLE1BQU1rQixhQUFhLEdBQUcsTUFBQUEsQ0FBT3ZCLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxJQUFJLEtBQUs7RUFDOUMsSUFBSTtJQUNGLE1BQU07TUFBRUs7SUFBRyxDQUFDLEdBQUdQLEdBQUcsQ0FBQ1EsTUFBTTtJQUN6QixNQUFNTSxXQUFXLEdBQUdkLEdBQUcsQ0FBQ2UsSUFBSTtJQUU1QixNQUFNUyxjQUFjLEdBQUcsTUFBTTFCLGtCQUFrQixDQUFDeUIsYUFBYSxDQUFDaEIsRUFBRSxFQUFFTyxXQUFXLENBQUM7SUFDOUViLEdBQUcsQ0FBQ0csSUFBSSxDQUFDb0IsY0FBYyxDQUFDO0VBQzFCLENBQUMsQ0FBQyxPQUFPbkIsS0FBSyxFQUFFO0lBQ2QsSUFBSUEsS0FBSyxDQUFDSyxPQUFPLENBQUNDLFFBQVEsQ0FBQyxXQUFXLENBQUMsRUFBRTtNQUN2QyxPQUFPVixHQUFHLENBQUNXLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQ1IsSUFBSSxDQUFDO1FBQzFCQyxLQUFLLEVBQUUsV0FBVztRQUNsQkssT0FBTyxFQUFFTCxLQUFLLENBQUNLO01BQ2pCLENBQUMsQ0FBQztJQUNKO0lBQ0FSLElBQUksQ0FBQ0csS0FBSyxDQUFDO0VBQ2I7QUFDRixDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBLE1BQU1vQixhQUFhLEdBQUcsTUFBQUEsQ0FBT3pCLEdBQUcsRUFBRUMsR0FBRyxFQUFFQyxJQUFJLEtBQUs7RUFDOUMsSUFBSTtJQUNGLE1BQU07TUFBRUs7SUFBRyxDQUFDLEdBQUdQLEdBQUcsQ0FBQ1EsTUFBTTtJQUN6QixNQUFNVixrQkFBa0IsQ0FBQzJCLGFBQWEsQ0FBQ2xCLEVBQUUsQ0FBQztJQUMxQ04sR0FBRyxDQUFDVyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUNjLEdBQUcsQ0FBQyxDQUFDO0VBQ3ZCLENBQUMsQ0FBQyxPQUFPckIsS0FBSyxFQUFFO0lBQ2QsSUFBSUEsS0FBSyxDQUFDSyxPQUFPLENBQUNDLFFBQVEsQ0FBQyxXQUFXLENBQUMsRUFBRTtNQUN2QyxPQUFPVixHQUFHLENBQUNXLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQ1IsSUFBSSxDQUFDO1FBQzFCQyxLQUFLLEVBQUUsV0FBVztRQUNsQkssT0FBTyxFQUFFTCxLQUFLLENBQUNLO01BQ2pCLENBQUMsQ0FBQztJQUNKO0lBQ0FSLElBQUksQ0FBQ0csS0FBSyxDQUFDO0VBQ2I7QUFDRixDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBLE1BQU1zQixnQkFBZ0IsR0FBRyxNQUFBQSxDQUFPM0IsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLElBQUksS0FBSztFQUNqRCxJQUFJO0lBQ0YsTUFBTTtNQUFFMEI7SUFBUyxDQUFDLEdBQUc1QixHQUFHLENBQUNRLE1BQU07SUFDL0IsTUFBTUMsR0FBRyxHQUFHLE1BQU1YLGtCQUFrQixDQUFDNkIsZ0JBQWdCLENBQUNDLFFBQVEsQ0FBQztJQUMvRDNCLEdBQUcsQ0FBQ0csSUFBSSxDQUFDSyxHQUFHLENBQUM7RUFDZixDQUFDLENBQUMsT0FBT0osS0FBSyxFQUFFO0lBQ2RILElBQUksQ0FBQ0csS0FBSyxDQUFDO0VBQ2I7QUFDRixDQUFDOztBQUVEO0FBQ0E7QUFDQTtBQUNBLE1BQU13QixnQkFBZ0IsR0FBRyxNQUFBQSxDQUFPN0IsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLElBQUksS0FBSztFQUNqRCxJQUFJO0lBQ0YsTUFBTTtNQUFFMEI7SUFBUyxDQUFDLEdBQUc1QixHQUFHLENBQUNRLE1BQU07SUFDL0IsTUFBTTtNQUFFc0IsU0FBUztNQUFFQyxjQUFjO01BQUVDO0lBQWEsQ0FBQyxHQUFHaEMsR0FBRyxDQUFDZSxJQUFJOztJQUU1RDtJQUNBLElBQUksQ0FBQ2UsU0FBUyxFQUFFO01BQ2QsTUFBTSxJQUFJbkMsZUFBZSxDQUFDLHdCQUF3QixDQUFDO0lBQ3JEO0lBRUEsTUFBTXNDLE9BQU8sR0FBRyxNQUFNbkMsa0JBQWtCLENBQUMrQixnQkFBZ0IsQ0FDdkRELFFBQVEsRUFDUkUsU0FBUyxFQUNUQyxjQUFjLElBQUksRUFBRSxFQUNwQkMsWUFBWSxJQUFJLENBQUMsQ0FDbkIsQ0FBQztJQUVEL0IsR0FBRyxDQUFDRyxJQUFJLENBQUM2QixPQUFPLENBQUM7RUFDbkIsQ0FBQyxDQUFDLE9BQU81QixLQUFLLEVBQUU7SUFDZCxJQUFJQSxLQUFLLFlBQVlWLGVBQWUsRUFBRTtNQUNwQyxPQUFPTSxHQUFHLENBQUNXLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQ1IsSUFBSSxDQUFDO1FBQzFCQyxLQUFLLEVBQUUsa0JBQWtCO1FBQ3pCSyxPQUFPLEVBQUVMLEtBQUssQ0FBQ0s7TUFDakIsQ0FBQyxDQUFDO0lBQ0o7SUFDQVIsSUFBSSxDQUFDRyxLQUFLLENBQUM7RUFDYjtBQUNGLENBQUM7O0FBRUQ7QUFDQTtBQUNBO0FBQ0EsTUFBTTZCLFVBQVUsR0FBRyxNQUFBQSxDQUFPbEMsR0FBRyxFQUFFQyxHQUFHLEVBQUVDLElBQUksS0FBSztFQUMzQyxJQUFJO0lBQ0ZKLGtCQUFrQixDQUFDb0MsVUFBVSxDQUFDLENBQUM7SUFDL0JqQyxHQUFHLENBQUNHLElBQUksQ0FBQztNQUFFTSxPQUFPLEVBQUU7SUFBNkIsQ0FBQyxDQUFDO0VBQ3JELENBQUMsQ0FBQyxPQUFPTCxLQUFLLEVBQUU7SUFDZEgsSUFBSSxDQUFDRyxLQUFLLENBQUM7RUFDYjtBQUNGLENBQUM7QUFFRDhCLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHO0VBQ2ZyQyxjQUFjO0VBQ2RPLGNBQWM7RUFDZE8sYUFBYTtFQUNiVSxhQUFhO0VBQ2JFLGFBQWE7RUFDYkUsZ0JBQWdCO0VBQ2hCRSxnQkFBZ0I7RUFDaEJLO0FBQ0YsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==