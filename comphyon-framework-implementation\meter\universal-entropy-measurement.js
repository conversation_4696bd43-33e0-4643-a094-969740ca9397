/**
 * Universal Entropy Measurement
 * 
 * This module implements the Universal Entropy Measurement component of the Meter.
 * It measures entropy across cyber, financial, and biological domains.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * UniversalEntropyMeasurement class
 */
class UniversalEntropyMeasurement extends EventEmitter {
  /**
   * Create a new UniversalEntropyMeasurement instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 5000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      domainWeights: {
        cyber: 0.33,
        financial: 0.33,
        biological: 0.34
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      universalEntropy: 0.5,
      domainEntropy: {
        cyber: {
          policyEntropy: 0.5,
          auditEntropy: 0.5,
          regulatoryEntropy: 0.5,
          overallEntropy: 0.5
        },
        financial: {
          transactionEntropy: 0.5,
          attackSurfaceCoherence: 0.5,
          marketStress: 0.5,
          overallEntropy: 0.5
        },
        biological: {
          telomereLength: 0.5,
          mtorActivation: 0.5,
          inflammationLevel: 0.5,
          overallEntropy: 0.5
        }
      },
      entropyHistory: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      domainUpdates: {
        cyber: 0,
        financial: 0,
        biological: 0
      },
      entropyCalculations: 0
    };
    
    if (this.options.enableLogging) {
      console.log('UniversalEntropyMeasurement initialized');
    }
  }
  
  /**
   * Start the universal entropy measurement
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('UniversalEntropyMeasurement is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('UniversalEntropyMeasurement started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the universal entropy measurement
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('UniversalEntropyMeasurement is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('UniversalEntropyMeasurement stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Update domain metric
   * @param {string} domain - Domain (cyber, financial, biological)
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @param {Object} metadata - Additional metadata
   * @returns {number} - Updated universal entropy
   */
  updateDomainMetric(domain, metric, value, metadata = {}) {
    const startTime = performance.now();
    
    // Validate domain
    if (!['cyber', 'financial', 'biological'].includes(domain)) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    // Validate value
    if (typeof value !== 'number' || value < 0 || value > 1) {
      throw new Error('Metric value must be a number between 0 and 1');
    }
    
    // Update domain metric
    if (this.state.domainEntropy[domain][metric] !== undefined) {
      this.state.domainEntropy[domain][metric] = value;
    } else {
      throw new Error(`Invalid metric for ${domain} domain: ${metric}`);
    }
    
    // Update domain overall entropy
    this._updateDomainOverallEntropy(domain);
    
    // Update universal entropy
    this._updateUniversalEntropy();
    
    // Add to history
    this.state.entropyHistory.push({
      universalEntropy: this.state.universalEntropy,
      domainEntropy: JSON.parse(JSON.stringify(this.state.domainEntropy)),
      updatedDomain: domain,
      updatedMetric: metric,
      timestamp: Date.now(),
      metadata
    });
    
    // Limit history size
    if (this.state.entropyHistory.length > this.options.historySize) {
      this.state.entropyHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.domainUpdates[domain]++;
    this.metrics.entropyCalculations++;
    
    // Emit update event
    this.emit('entropy-update', {
      universalEntropy: this.state.universalEntropy,
      domainEntropy: JSON.parse(JSON.stringify(this.state.domainEntropy)),
      updatedDomain: domain,
      updatedMetric: metric,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`UniversalEntropyMeasurement: Updated ${domain}.${metric} to ${value.toFixed(4)}, universal entropy is ${this.state.universalEntropy.toFixed(4)}`);
    }
    
    return this.state.universalEntropy;
  }
  
  /**
   * Get universal entropy
   * @returns {number} - Universal entropy
   */
  getUniversalEntropy() {
    return this.state.universalEntropy;
  }
  
  /**
   * Get domain entropy
   * @param {string} domain - Domain (cyber, financial, biological)
   * @returns {Object} - Domain entropy
   */
  getDomainEntropy(domain) {
    if (domain && !this.state.domainEntropy[domain]) {
      throw new Error(`Invalid domain: ${domain}`);
    }
    
    if (domain) {
      return { ...this.state.domainEntropy[domain] };
    }
    
    return JSON.parse(JSON.stringify(this.state.domainEntropy));
  }
  
  /**
   * Get entropy history
   * @param {number} limit - Maximum number of history items to return
   * @returns {Array} - Entropy history
   */
  getEntropyHistory(limit = 10) {
    return this.state.entropyHistory.slice(0, limit);
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      universalEntropy: this.state.universalEntropy,
      domainEntropy: JSON.parse(JSON.stringify(this.state.domainEntropy)),
      entropyHistory: [...this.state.entropyHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Calculate Comphyon value
   * @returns {number} - Comphyon value (in Cph units)
   */
  calculateComphyon() {
    // Get domain energies
    const cyberEnergy = this._calculateDomainEnergy('cyber');
    const financialEnergy = this._calculateDomainEnergy('financial');
    const biologicalEnergy = this._calculateDomainEnergy('biological');
    
    // Calculate energy gradients (simplified for this implementation)
    const cyberGradient = cyberEnergy * 0.1;
    const financialGradient = financialEnergy * 0.1;
    const biologicalGradient = biologicalEnergy * 0.1;
    
    // Apply the Comphyon formula: Cph = ((dE_CSDE × dE_CSFE) × log(E_CSME))/166000
    const comphyonValue = ((cyberGradient * financialGradient) * Math.log(biologicalEnergy + 1)) / 166000;
    
    // Convert to Cph units (1 Cph = 3,142 predictions/sec)
    return comphyonValue * 3142;
  }
  
  /**
   * Update domain overall entropy
   * @param {string} domain - Domain to update
   * @private
   */
  _updateDomainOverallEntropy(domain) {
    const domainMetrics = this.state.domainEntropy[domain];
    
    // Calculate overall entropy based on domain-specific metrics
    switch (domain) {
      case 'cyber':
        // Apply 18/82 principle for cyber domain
        domainMetrics.overallEntropy = (
          0.18 * domainMetrics.policyEntropy +
          0.82 * ((domainMetrics.auditEntropy + domainMetrics.regulatoryEntropy) / 2)
        );
        break;
        
      case 'financial':
        // For financial domain, attack surface coherence is inverted (higher coherence = lower entropy)
        domainMetrics.overallEntropy = (
          0.18 * domainMetrics.transactionEntropy +
          0.82 * (((1 - domainMetrics.attackSurfaceCoherence) + domainMetrics.marketStress) / 2)
        );
        break;
        
      case 'biological':
        // For biological domain, telomere length is inverted (higher length = lower entropy)
        domainMetrics.overallEntropy = (
          0.18 * (1 - domainMetrics.telomereLength) +
          0.82 * ((domainMetrics.mtorActivation + domainMetrics.inflammationLevel) / 2)
        );
        break;
        
      default:
        throw new Error(`Invalid domain: ${domain}`);
    }
    
    // Ensure entropy is between 0 and 1
    this.state.domainEntropy[domain].overallEntropy = this._clamp(domainMetrics.overallEntropy);
  }
  
  /**
   * Update universal entropy
   * @private
   */
  _updateUniversalEntropy() {
    const { domainWeights } = this.options;
    
    // Calculate weighted average of domain entropies
    const universalEntropy = (
      domainWeights.cyber * this.state.domainEntropy.cyber.overallEntropy +
      domainWeights.financial * this.state.domainEntropy.financial.overallEntropy +
      domainWeights.biological * this.state.domainEntropy.biological.overallEntropy
    );
    
    // Update state
    this.state.universalEntropy = this._clamp(universalEntropy);
    this.state.lastUpdateTime = Date.now();
  }
  
  /**
   * Calculate domain energy
   * @param {string} domain - Domain to calculate energy for
   * @returns {number} - Domain energy
   * @private
   */
  _calculateDomainEnergy(domain) {
    switch (domain) {
      case 'cyber':
        // E_CSDE = A1×D (Audit × Dominance)
        return this.state.domainEntropy.cyber.auditEntropy * this.state.domainEntropy.cyber.regulatoryEntropy;
        
      case 'financial':
        // E_CSFE = A2×P (Attack surface × Patterns)
        return (1 - this.state.domainEntropy.financial.attackSurfaceCoherence) * this.state.domainEntropy.financial.transactionEntropy;
        
      case 'biological':
        // E_CSME = T×I (Telomere × Inflammation)
        return (1 - this.state.domainEntropy.biological.telomereLength) * this.state.domainEntropy.biological.inflammationLevel;
        
      default:
        throw new Error(`Invalid domain: ${domain}`);
    }
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // Calculate Comphyon value
        const comphyonValue = this.calculateComphyon();
        
        // Emit Comphyon update event
        this.emit('comphyon-update', {
          comphyonValue,
          universalEntropy: this.state.universalEntropy,
          timestamp: Date.now()
        });
        
        if (this.options.enableLogging) {
          console.log(`UniversalEntropyMeasurement: Comphyon value is ${comphyonValue.toFixed(4)} Cph`);
        }
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Clamp value between 0 and 1
   * @param {number} value - Value to clamp
   * @returns {number} - Clamped value
   * @private
   */
  _clamp(value) {
    return Math.max(0, Math.min(1, value));
  }
}

module.exports = UniversalEntropyMeasurement;
