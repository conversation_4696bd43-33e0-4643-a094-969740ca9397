const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/reports/routes');
const models = require('../../../../apis/esg/reports/models');

// Mock the models
jest.mock('../../../../apis/esg/reports/models', () => ({
  esgReports: [
    {
      id: 'esg-r-12345678',
      title: 'Annual Sustainability Report 2023',
      description: 'Comprehensive report on our sustainability initiatives and performance',
      reportType: 'sustainability',
      status: 'published',
      publishDate: '2023-03-15',
      reportingPeriod: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      frameworks: ['GRI', 'SASB'],
      metrics: ['carbon-emissions', 'water-usage', 'diversity-metrics'],
      owner: 'Sustainability Team',
      contributors: ['<PERSON>', '<PERSON>'],
      reviewers: ['Executive Board'],
      attachments: [
        {
          id: 'att-12345',
          name: 'Full Report PDF',
          type: 'pdf',
          url: 'https://example.com/reports/sustainability-2023.pdf',
          uploadedBy: '<PERSON>',
          uploadedAt: '2023-03-10T00:00:00Z'
        }
      ],
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-03-15T00:00:00Z'
    },
    {
      id: 'esg-r-87654321',
      title: 'Q1 2023 ESG Update',
      description: 'Quarterly update on ESG metrics and initiatives',
      reportType: 'quarterly-update',
      status: 'draft',
      reportingPeriod: {
        startDate: '2023-01-01',
        endDate: '2023-03-31'
      },
      frameworks: ['SASB'],
      metrics: ['carbon-emissions', 'energy-usage'],
      owner: 'ESG Reporting Team',
      contributors: ['Jane Smith'],
      reviewers: [],
      attachments: [],
      createdAt: '2023-04-01T00:00:00Z',
      updatedAt: '2023-04-05T00:00:00Z'
    }
  ],
  reportTemplates: [
    {
      id: 'template-12345',
      name: 'Annual Sustainability Report Template',
      description: 'Template for annual sustainability reporting',
      reportType: 'sustainability',
      frameworks: ['GRI', 'SASB'],
      sections: [
        {
          title: 'Executive Summary',
          description: 'Overview of key sustainability achievements',
          required: true,
          order: 1
        },
        {
          title: 'Environmental Performance',
          description: 'Details of environmental metrics and initiatives',
          required: true,
          order: 2
        },
        {
          title: 'Social Impact',
          description: 'Details of social initiatives and metrics',
          required: true,
          order: 3
        },
        {
          title: 'Governance',
          description: 'Overview of governance structure and policies',
          required: true,
          order: 4
        }
      ],
      createdAt: '2022-12-01T00:00:00Z',
      updatedAt: '2022-12-01T00:00:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/reports', router);

describe('ESG Reports API Integration Tests', () => {
  describe('GET /governance/esg/reports', () => {
    it('should return all reports with default pagination', async () => {
      const response = await request(app).get('/governance/esg/reports');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter reports by status', async () => {
      const response = await request(app).get('/governance/esg/reports?status=published');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('published');
    });

    it('should filter reports by reportType', async () => {
      const response = await request(app).get('/governance/esg/reports?reportType=quarterly-update');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].reportType).toBe('quarterly-update');
    });
  });

  describe('GET /governance/esg/reports/:id', () => {
    it('should return a specific report by ID', async () => {
      const response = await request(app).get('/governance/esg/reports/esg-r-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-r-12345678');
      expect(response.body.data.title).toBe('Annual Sustainability Report 2023');
    });

    it('should return 404 if report not found', async () => {
      const response = await request(app).get('/governance/esg/reports/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/reports', () => {
    it('should create a new report', async () => {
      const newReport = {
        title: 'New ESG Report',
        description: 'Description of new report',
        reportType: 'sustainability',
        status: 'draft',
        reportingPeriod: {
          startDate: '2023-01-01',
          endDate: '2023-12-31'
        },
        frameworks: ['GRI'],
        metrics: ['carbon-emissions'],
        owner: 'Test Owner'
      };

      const response = await request(app)
        .post('/governance/esg/reports')
        .send(newReport);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG report created successfully');
      expect(response.body.data.title).toBe('New ESG Report');
      expect(response.body.data.reportType).toBe('sustainability');
    });

    it('should return 400 for invalid input', async () => {
      const invalidReport = {
        // Missing required fields
        description: 'Invalid report'
      };

      const response = await request(app)
        .post('/governance/esg/reports')
        .send(invalidReport);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/reports/:id', () => {
    it('should update an existing report', async () => {
      const updatedReport = {
        title: 'Updated Report Title',
        status: 'published'
      };

      const response = await request(app)
        .put('/governance/esg/reports/esg-r-12345678')
        .send(updatedReport);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG report updated successfully');
      expect(response.body.data.title).toBe('Updated Report Title');
      expect(response.body.data.status).toBe('published');
    });

    it('should return 404 if report not found', async () => {
      const response = await request(app)
        .put('/governance/esg/reports/non-existent-id')
        .send({ title: 'Updated Title' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/reports/:id', () => {
    it('should delete an existing report', async () => {
      const response = await request(app).delete('/governance/esg/reports/esg-r-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'ESG report deleted successfully');
    });

    it('should return 404 if report not found', async () => {
      const response = await request(app).delete('/governance/esg/reports/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /governance/esg/reports/templates', () => {
    it('should return all report templates', async () => {
      const response = await request(app).get('/governance/esg/reports/templates');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].name).toBe('Annual Sustainability Report Template');
    });
  });

  describe('GET /governance/esg/reports/templates/:id', () => {
    it('should return a specific report template by ID', async () => {
      const response = await request(app).get('/governance/esg/reports/templates/template-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('template-12345');
      expect(response.body.data.name).toBe('Annual Sustainability Report Template');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app).get('/governance/esg/reports/templates/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/reports/templates', () => {
    it('should create a new report template', async () => {
      const newTemplate = {
        name: 'New Report Template',
        description: 'Description of new template',
        reportType: 'quarterly-update',
        frameworks: ['SASB'],
        sections: [
          {
            title: 'Executive Summary',
            description: 'Overview of key metrics',
            required: true,
            order: 1
          }
        ]
      };

      const response = await request(app)
        .post('/governance/esg/reports/templates')
        .send(newTemplate);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Report template created successfully');
      expect(response.body.data.name).toBe('New Report Template');
      expect(response.body.data.reportType).toBe('quarterly-update');
    });

    it('should return 400 for invalid input', async () => {
      const invalidTemplate = {
        // Missing required fields
        description: 'Invalid template'
      };

      const response = await request(app)
        .post('/governance/esg/reports/templates')
        .send(invalidTemplate);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /governance/esg/reports/templates/:id', () => {
    it('should update an existing report template', async () => {
      const updatedTemplate = {
        name: 'Updated Template Name',
        sections: [
          {
            title: 'New Section',
            description: 'New section description',
            required: true,
            order: 5
          }
        ]
      };

      const response = await request(app)
        .put('/governance/esg/reports/templates/template-12345')
        .send(updatedTemplate);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Report template updated successfully');
      expect(response.body.data.name).toBe('Updated Template Name');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app)
        .put('/governance/esg/reports/templates/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /governance/esg/reports/templates/:id', () => {
    it('should delete an existing report template', async () => {
      const response = await request(app).delete('/governance/esg/reports/templates/template-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Report template deleted successfully');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app).delete('/governance/esg/reports/templates/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });
});
