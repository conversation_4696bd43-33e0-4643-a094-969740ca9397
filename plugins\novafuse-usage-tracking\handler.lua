local plugin = {
  PRIORITY = 900,
  VERSION = "1.0.0",
}

-- Initialize the plugin
function plugin:init_worker()
  kong.log.debug("Initializing NovaFuse usage tracking plugin")
end

-- Execute the plugin on each request
function plugin:access(conf)
  -- Get the consumer (partner) information
  local consumer = kong.client.get_consumer()
  local consumer_id = consumer and consumer.id or "anonymous"
  local consumer_username = consumer and consumer.username or "anonymous"
  
  -- Get the API information
  local service = kong.router.get_service()
  local service_id = service and service.id or "unknown"
  local service_name = service and service.name or "unknown"
  
  -- Get the route information
  local route = kong.router.get_route()
  local route_id = route and route.id or "unknown"
  local route_name = route and route.name or "unknown"
  
  -- Get request information
  local request_method = kong.request.get_method()
  local request_path = kong.request.get_path()
  local request_size = kong.request.get_header("content-length") or 0
  
  -- Create usage record
  local usage_record = {
    consumer_id = consumer_id,
    consumer_username = consumer_username,
    service_id = service_id,
    service_name = service_name,
    route_id = route_id,
    route_name = route_name,
    request_method = request_method,
    request_path = request_path,
    request_size = request_size,
    timestamp = os.time(),
    subscription_tier = conf.subscription_tier or "standard"
  }
  
  -- Log the usage record
  kong.log.notice("NovaFuse Usage: " .. require("cjson").encode(usage_record))
  
  -- Store the usage record in Kong's shared dictionary for later processing
  local usage_storage = ngx.shared.novafuse_usage_storage
  if usage_storage then
    local usage_key = consumer_id .. ":" .. service_id .. ":" .. os.time()
    usage_storage:set(usage_key, require("cjson").encode(usage_record))
  end
  
  -- Add usage information to the request context for later processing
  ngx.ctx.novafuse_usage = usage_record
end

-- Execute the plugin after the response is sent
function plugin:log(conf)
  -- Get the usage record from the context
  local usage_record = ngx.ctx.novafuse_usage
  if not usage_record then
    return
  end
  
  -- Add response information
  usage_record.response_status = kong.response.get_status()
  usage_record.response_size = kong.response.get_header("content-length") or 0
  usage_record.response_time = ngx.var.request_time
  
  -- Log the complete usage record
  kong.log.notice("NovaFuse Complete Usage: " .. require("cjson").encode(usage_record))
  
  -- In a production environment, we would send this data to a database or analytics service
  -- For this demo, we'll just log it
end

return plugin
