version: '3.8'

services:
  privacy-management-api:
    build:
      context: ../../..
      dockerfile: apis/privacy/management/Dockerfile
    container_name: novafuse-privacy-management-api
    ports:
      - "3004:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=mongodb://privacy-mongo:27017/privacy-management
      - JWT_SECRET=your-secret-key
    volumes:
      - ../../..:/app
      - /app/node_modules
    depends_on:
      - privacy-mongo
    networks:
      - privacy-net
    restart: unless-stopped

  privacy-mongo:
    image: mongo:4.4
    container_name: novafuse-privacy-mongo
    ports:
      - "27018:27017"  # Changed from 27017:27017 to avoid conflict
    volumes:
      - privacy-mongo-data:/data/db
    networks:
      - privacy-net
    restart: unless-stopped

networks:
  privacy-net:
    driver: bridge

volumes:
  privacy-mongo-data:
