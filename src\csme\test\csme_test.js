/**
 * CSME Test Suite
 * 
 * This file contains tests for the CSME (Cyber-Safety Medical Engine) components.
 */

const assert = require('assert');

// Import CSME components
const { 
  CSMEEngine,
  BioEntropicTensor,
  EnvironmentalContextProcessor,
  PsiRevertProtocolEngine,
  NeuroEthicalFilterBank,
  DecayRateCalculator,
  CSMEController,
  createCSMESystem
} = require('../index');

/**
 * Sample data for testing
 */
const sampleGenomicData = {
  geneticRiskFactors: {
    alzheimers: 0.2,
    heartDisease: 0.3,
    diabetes: 0.1
  },
  geneticProtectiveFactors: {
    longevity: 0.4,
    immuneResponse: 0.5
  },
  telomereLength: 0.8
};

const sampleProteomicData = {
  proteinExpression: {
    p53: 0.7,
    mTOR: 0.6,
    FOXO3: 0.8
  },
  inflammatoryMarkers: {
    crp: 0.3,
    il6: 0.2,
    tnfAlpha: 0.1
  }
};

const sampleClinicalData = {
  symptoms: {
    fatigue: 0.3,
    pain: 0.2,
    cognitiveFog: 0.1
  },
  vitalSigns: {
    bloodPressure: 0.8,
    heartRate: 0.7,
    oxygenSaturation: 0.9
  }
};

const sampleEnvironmentalData = {
  temperature: 22,
  humidity: 45,
  oxygenLevel: 21,
  atmosphericPressure: 1013,
  toxinLevel: 0.1
};

/**
 * Test CSMEEngine
 */
function testCSMEEngine() {
  console.log('Testing CSMEEngine...');
  
  // Create CSMEEngine instance
  const csmeEngine = new CSMEEngine();
  
  // Test calculate method
  const result = csmeEngine.calculate(
    sampleGenomicData,
    sampleProteomicData,
    sampleClinicalData
  );
  
  // Verify result properties
  assert(result.csmeValue !== undefined, 'csmeValue should be defined');
  assert(result.performanceFactor !== undefined, 'performanceFactor should be defined');
  assert(result.calculatedAt !== undefined, 'calculatedAt should be defined');
  
  // Test getEthicalScore method
  const ethicalScore = csmeEngine.getEthicalScore();
  assert(ethicalScore >= 0 && ethicalScore <= 1, 'ethicalScore should be between 0 and 1');
  
  console.log('CSMEEngine tests passed.');
}

/**
 * Test BioEntropicTensor
 */
function testBioEntropicTensor() {
  console.log('Testing BioEntropicTensor...');
  
  // Create BioEntropicTensor instance
  const bioEntropicTensor = new BioEntropicTensor();
  
  // Test processBiologicalData method
  const result = bioEntropicTensor.processBiologicalData({
    genomicData: sampleGenomicData,
    proteomicData: sampleProteomicData,
    clinicalData: sampleClinicalData,
    environmentalData: sampleEnvironmentalData
  });
  
  // Verify result properties
  assert(result.coherence !== undefined, 'coherence should be defined');
  assert(result.entropyGradient !== undefined, 'entropyGradient should be defined');
  assert(result.tensor !== undefined, 'tensor should be defined');
  assert(result.components !== undefined, 'components should be defined');
  
  // Verify coherence is between 0 and 1
  assert(result.coherence >= 0 && result.coherence <= 1, 'coherence should be between 0 and 1');
  
  // Verify tensor has 4 elements
  assert(result.tensor.length === 4, 'tensor should have 4 elements');
  
  // Test calculateCoherenceDynamics method
  const coherenceHistory = [0.8, 0.79, 0.78, 0.77, 0.76];
  const dynamics = bioEntropicTensor.calculateCoherenceDynamics(coherenceHistory);
  
  // Verify dynamics properties
  assert(dynamics.velocity !== undefined, 'velocity should be defined');
  assert(dynamics.acceleration !== undefined, 'acceleration should be defined');
  assert(dynamics.isDecaying !== undefined, 'isDecaying should be defined');
  assert(dynamics.decayRate !== undefined, 'decayRate should be defined');
  
  console.log('BioEntropicTensor tests passed.');
}

/**
 * Test EnvironmentalContextProcessor
 */
function testEnvironmentalContextProcessor() {
  console.log('Testing EnvironmentalContextProcessor...');
  
  // Create EnvironmentalContextProcessor instance
  const environmentalContextProcessor = new EnvironmentalContextProcessor();
  
  // Test processEnvironmentalContext method
  const result = environmentalContextProcessor.processEnvironmentalContext(sampleEnvironmentalData);
  
  // Verify result properties
  assert(result.overallImpact !== undefined, 'overallImpact should be defined');
  assert(result.edenicDistance !== undefined, 'edenicDistance should be defined');
  assert(result.decayRateAdjustment !== undefined, 'decayRateAdjustment should be defined');
  
  // Verify edenicDistance is between 0 and 1
  assert(result.edenicDistance >= 0 && result.edenicDistance <= 1, 'edenicDistance should be between 0 and 1');
  
  // Test calculateEdenicDistance method
  const edenicDistance = environmentalContextProcessor.calculateEdenicDistance(sampleEnvironmentalData);
  
  // Verify edenicDistance is between 0 and 1
  assert(edenicDistance >= 0 && edenicDistance <= 1, 'edenicDistance should be between 0 and 1');
  
  console.log('EnvironmentalContextProcessor tests passed.');
}

/**
 * Test PsiRevertProtocolEngine
 */
function testPsiRevertProtocolEngine() {
  console.log('Testing PsiRevertProtocolEngine...');
  
  // Create PsiRevertProtocolEngine instance
  const psiRevertProtocolEngine = new PsiRevertProtocolEngine();
  
  // Test selectProtocol method
  const subjectState = {
    coherence: 0.7,
    entropyGradient: -0.01,
    components: {
      genomicEntropy: 0.3,
      proteomicEntropy: 0.3,
      clinicalEntropy: 0.3,
      environmentalEntropy: 0.3
    },
    environmentalContext: {
      overallImpact: 0,
      edenicDistance: 0.3
    },
    currentInterventions: []
  };
  
  const result = psiRevertProtocolEngine.selectProtocol(subjectState);
  
  // Verify result properties
  assert(result !== undefined, 'result should be defined');
  
  // Test calculateProtocolPriorities method
  const protocols = [
    { id: 'protocol-1', basePriority: 0.7, type: 'environmental' },
    { id: 'protocol-2', basePriority: 0.5, type: 'dietary' }
  ];
  
  const prioritizedProtocols = psiRevertProtocolEngine.calculateProtocolPriorities(protocols, subjectState);
  
  // Verify prioritizedProtocols is an array
  assert(Array.isArray(prioritizedProtocols), 'prioritizedProtocols should be an array');
  
  console.log('PsiRevertProtocolEngine tests passed.');
}

/**
 * Test DecayRateCalculator
 */
function testDecayRateCalculator() {
  console.log('Testing DecayRateCalculator...');
  
  // Create DecayRateCalculator instance
  const decayRateCalculator = new DecayRateCalculator();
  
  // Test calculateDecayRate method
  const subjectData = {
    age: 45,
    geneticProfile: sampleGenomicData,
    lifestyleFactors: {
      diet: 0.7,
      exercise: 0.6,
      sleep: 0.8,
      smoking: 0.1,
      alcohol: 0.2
    }
  };
  
  const result = decayRateCalculator.calculateDecayRate(subjectData, sampleEnvironmentalData);
  
  // Verify result properties
  assert(result.decayRate !== undefined, 'decayRate should be defined');
  assert(result.adjustments !== undefined, 'adjustments should be defined');
  
  // Verify decayRate is between minDecayRate and maxDecayRate
  assert(
    result.decayRate >= decayRateCalculator.options.minDecayRate && 
    result.decayRate <= decayRateCalculator.options.maxDecayRate,
    'decayRate should be between minDecayRate and maxDecayRate'
  );
  
  // Test projectCoherenceDecay method
  const initialCoherence = 0.8;
  const decayRate = 0.05;
  const timeSteps = 10;
  
  const projection = decayRateCalculator.projectCoherenceDecay(initialCoherence, decayRate, timeSteps);
  
  // Verify projection is an array with timeSteps + 1 elements
  assert(Array.isArray(projection), 'projection should be an array');
  assert(projection.length === timeSteps + 1, 'projection should have timeSteps + 1 elements');
  
  // Verify first element is initialCoherence
  assert(projection[0] === initialCoherence, 'first element should be initialCoherence');
  
  console.log('DecayRateCalculator tests passed.');
}

/**
 * Test CSMEController
 */
function testCSMEController() {
  console.log('Testing CSMEController...');
  
  // Create CSMEController instance
  const csmeController = new CSMEController({
    bioEntropicTensor: new BioEntropicTensor(),
    environmentalContextProcessor: new EnvironmentalContextProcessor(),
    psiRevertProtocolEngine: new PsiRevertProtocolEngine(),
    neuroEthicalFilterBank: new NeuroEthicalFilterBank(),
    decayRateCalculator: new DecayRateCalculator(),
    csmeEngine: new CSMEEngine()
  });
  
  // Test processBiologicalData method
  const result = csmeController.processBiologicalData(
    {
      genomicData: sampleGenomicData,
      proteomicData: sampleProteomicData,
      clinicalData: sampleClinicalData
    },
    sampleEnvironmentalData
  );
  
  // Verify result properties
  assert(result.coherence !== undefined, 'coherence should be defined');
  assert(result.entropyGradient !== undefined, 'entropyGradient should be defined');
  assert(result.tensor !== undefined, 'tensor should be defined');
  assert(result.components !== undefined, 'components should be defined');
  
  // Test getCurrentSubjectState method
  const subjectState = csmeController.getCurrentSubjectState();
  
  // Verify subjectState properties
  assert(subjectState.currentCoherence !== undefined, 'currentCoherence should be defined');
  assert(subjectState.decayRate !== undefined, 'decayRate should be defined');
  
  // Test getEthicalScore method
  const ethicalScore = csmeController.getEthicalScore();
  
  // Verify ethicalScore is between 0 and 1
  assert(ethicalScore >= 0 && ethicalScore <= 1, 'ethicalScore should be between 0 and 1');
  
  // Test recommendProtocols method
  const recommendations = csmeController.recommendProtocols();
  
  // Verify recommendations properties
  assert(recommendations.protocols !== undefined, 'protocols should be defined');
  assert(Array.isArray(recommendations.protocols), 'protocols should be an array');
  
  // Test projectCoherenceTrajectory method
  const projection = csmeController.projectCoherenceTrajectory(10);
  
  // Verify projection properties
  assert(projection.baselineProjection !== undefined, 'baselineProjection should be defined');
  assert(Array.isArray(projection.baselineProjection), 'baselineProjection should be an array');
  assert(projection.baselineProjection.length === 11, 'baselineProjection should have 11 elements');
  
  console.log('CSMEController tests passed.');
}

/**
 * Test createCSMESystem factory function
 */
function testCreateCSMESystem() {
  console.log('Testing createCSMESystem...');
  
  // Create CSME system
  const csmeSystem = createCSMESystem();
  
  // Verify system components
  assert(csmeSystem.csmeEngine !== undefined, 'csmeEngine should be defined');
  assert(csmeSystem.bioEntropicTensor !== undefined, 'bioEntropicTensor should be defined');
  assert(csmeSystem.environmentalContextProcessor !== undefined, 'environmentalContextProcessor should be defined');
  assert(csmeSystem.psiRevertProtocolEngine !== undefined, 'psiRevertProtocolEngine should be defined');
  assert(csmeSystem.neuroEthicalFilterBank !== undefined, 'neuroEthicalFilterBank should be defined');
  assert(csmeSystem.decayRateCalculator !== undefined, 'decayRateCalculator should be defined');
  assert(csmeSystem.csmeController !== undefined, 'csmeController should be defined');
  assert(csmeSystem.meterIntegrationInterface !== undefined, 'meterIntegrationInterface should be defined');
  assert(csmeSystem.governorIntegrationInterface !== undefined, 'governorIntegrationInterface should be defined');
  
  console.log('createCSMESystem tests passed.');
}

/**
 * Run all tests
 */
function runAllTests() {
  console.log('=== Running CSME Tests ===\n');
  
  try {
    testCSMEEngine();
    testBioEntropicTensor();
    testEnvironmentalContextProcessor();
    testPsiRevertProtocolEngine();
    testDecayRateCalculator();
    testCSMEController();
    testCreateCSMESystem();
    
    console.log('\nAll tests passed successfully!');
  } catch (error) {
    console.error('\nTest failed:', error);
  }
}

// Run all tests
runAllTests();
