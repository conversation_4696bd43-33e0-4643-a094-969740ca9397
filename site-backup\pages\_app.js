import '../styles/globals.css';
import Head from 'next/head';
import Navigation from '../components/Navigation';
import FloatingNovaConcierge from '../components/FloatingNovaConcierge';
import Link from 'next/link';

function MyApp({ Component, pageProps }) {
  return (
    <>
      <Head>
        <title>{pageProps.title || 'NovaFuse API Superstore'}</title>
        <meta name="description" content="NovaFuse API Superstore: A comprehensive marketplace for GRC APIs" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="min-h-screen flex flex-col bg-primary text-primary">
        <Navigation />

        <main className="flex-grow container mx-auto px-4 py-8">
          <Component {...pageProps} />
        </main>

        <footer className="bg-secondary text-white py-12 mt-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">NovaFuse API Superstore</h3>
                <p className="text-gray-400">
                  Connect your GRC workflows with premium partners and services.
                </p>
              </div>
              <div>
                <h4 className="text-md font-semibold mb-4">For Partners</h4>
                <ul className="space-y-2">
                  <li><Link href="/become-partner" className="text-gray-400 hover:text-white">Become a Partner</Link></li>
                  <li><Link href="/partner-portal" className="text-gray-400 hover:text-white">Partner Portal</Link></li>
                  <li><Link href="/api-docs" className="text-gray-400 hover:text-white">API Documentation</Link></li>
                  <li><Link href="/support" className="text-gray-400 hover:text-white">Support</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="text-md font-semibold mb-4">For Customers</h4>
                <ul className="space-y-2">
                  <li><Link href="/integration-guides" className="text-gray-400 hover:text-white">Integration Guides</Link></li>
                  <li><Link href="/api-reference" className="text-gray-400 hover:text-white">API Reference</Link></li>
                  <li><Link href="/support-center" className="text-gray-400 hover:text-white">Support Center</Link></li>
                  <li><Link href="/contact" className="text-gray-400 hover:text-white">Contact Us</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="text-md font-semibold mb-4">Legal</h4>
                <ul className="space-y-2">
                  <li><Link href="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link></li>
                  <li><Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
                  <li><Link href="/cookie-policy" className="text-gray-400 hover:text-white">Cookie Policy</Link></li>
                  <li><Link href="/gdpr" className="text-gray-400 hover:text-white">GDPR Compliance</Link></li>
                </ul>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
              <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>

      <FloatingNovaConcierge />
    </>
  );
}

export default MyApp;
