/**
 * LazyLoad Component
 * 
 * A component for lazily loading content when it becomes visible.
 */

import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';

/**
 * LazyLoad component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {React.ReactNode} [props.placeholder] - Placeholder to show while loading
 * @param {number} [props.threshold=0.1] - Intersection threshold (0-1)
 * @param {string} [props.rootMargin='0px'] - Root margin
 * @param {boolean} [props.once=true] - Whether to only load once
 * @param {boolean} [props.unmountIfInvisible=false] - Whether to unmount if invisible
 * @param {Function} [props.onVisible] - Function to call when visible
 * @param {Function} [props.onHidden] - Function to call when hidden
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} LazyLoad component
 */
const LazyLoad = ({
  children,
  placeholder = null,
  threshold = 0.1,
  rootMargin = '0px',
  once = true,
  unmountIfInvisible = false,
  onVisible,
  onHidden,
  className = '',
  style = {}
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasBeenVisible, setHasBeenVisible] = useState(false);
  const containerRef = useRef(null);
  const observerRef = useRef(null);
  
  // Initialize intersection observer
  useEffect(() => {
    // Skip if already visible and only loading once
    if (once && hasBeenVisible) return;
    
    // Create observer
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        const isEntryVisible = entry.isIntersecting;
        
        // Update visibility
        setIsVisible(isEntryVisible);
        
        // Update has been visible
        if (isEntryVisible) {
          setHasBeenVisible(true);
          
          // Call onVisible callback
          if (onVisible) {
            onVisible();
          }
          
          // Disconnect observer if only loading once
          if (once) {
            observerRef.current.disconnect();
          }
        } else if (onHidden) {
          // Call onHidden callback
          onHidden();
        }
      },
      {
        root: null,
        rootMargin,
        threshold
      }
    );
    
    // Observe container
    if (containerRef.current) {
      observerRef.current.observe(containerRef.current);
    }
    
    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [once, hasBeenVisible, rootMargin, threshold, onVisible, onHidden]);
  
  // Determine whether to render children
  const shouldRenderChildren = isVisible || (hasBeenVisible && !unmountIfInvisible);
  
  return (
    <div
      ref={containerRef}
      className={`lazy-load ${className}`}
      style={style}
      data-testid="lazy-load"
      data-visible={isVisible}
    >
      {shouldRenderChildren ? children : placeholder}
    </div>
  );
};

LazyLoad.propTypes = {
  children: PropTypes.node.isRequired,
  placeholder: PropTypes.node,
  threshold: PropTypes.number,
  rootMargin: PropTypes.string,
  once: PropTypes.bool,
  unmountIfInvisible: PropTypes.bool,
  onVisible: PropTypes.func,
  onHidden: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default LazyLoad;
