# NovaConnect Universal API Connector

NovaConnect UAC is a powerful Universal API Connector for plug-and-play integration, designed to be the central nervous system for Google Cloud compliance.

## Features

- **Universal Data Fabric**: Normalize data from any source
- **Connector Registry**: Manage API connections in one place
- **Authentication Service**: Secure access to all connectors
- **Transformation Engine**: Map data between different formats
- **Remediation Engine**: Automate workflows for compliance

## Getting Started

### Prerequisites

- Node.js 18 or higher
- MongoDB
- Redis
- Docker and Docker Compose (optional)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/novagrc/nova-connect.git
cd nova-connect
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file based on the `.env.example` file:

```bash
cp .env.example .env
```

4. Start the server:

```bash
npm start
```

### Using Docker

1. Start the services using Docker Compose:

```bash
npm run docker:deploy
```

2. Access the API at http://localhost:3001

3. Access MongoDB Express at http://localhost:8081

4. Access Redis Commander at http://localhost:8082

## Development

### Running in Development Mode

```bash
npm run dev
```

### Running in Cluster Mode

```bash
npm run start:cluster
```

### Debugging

```bash
npm run debug
```

### Profiling

```bash
npm run profile
```

## Testing

### Running All Tests

```bash
npm run test:all
```

### Running Unit Tests

```bash
npm run test:unit
```

### Running Integration Tests

```bash
npm run test:integration
```

### Running Performance Tests

```bash
npm run test:performance
```

### Running Security Tests

```bash
npm run test:security
```

### Running Stress Tests

```bash
npm run test:stress
```

### Checking Test Coverage

```bash
npm run test:coverage
```

## Monitoring

NovaConnect UAC includes comprehensive monitoring capabilities:

- **Prometheus Metrics**: Available at `/metrics`
- **Health Checks**: Available at `/health`, `/health/detailed`, and `/health/ready`
- **Google Cloud Operations Integration**: Automatic integration with Google Cloud Monitoring, Logging, and Trace
- **Distributed Tracing**: Using OpenTelemetry

## Deployment

### Google Cloud Platform

1. Set up the required environment variables:

```bash
export GOOGLE_CLOUD_PROJECT=your-project-id
export GCP_REGION=us-central1
export GCP_ZONE=us-central1-a
```

2. Deploy to Google Cloud Platform:

```bash
./scripts/gcp-deploy.sh
```

3. Validate the deployment:

```bash
node scripts/gcp-validation.js
```

## Google Cloud Marketplace

NovaConnect UAC is designed to be deployed on Google Cloud Marketplace. The following tiers are available:

- **NovaConnect Core**: Basic API integration capabilities
- **NovaConnect Secure**: Enhanced security and compliance features
- **NovaConnect Enterprise**: Advanced features for enterprise customers
- **NovaConnect AI Boost**: AI-powered features for intelligent automation

## Security

NovaConnect UAC includes several security features:

- **JWT Authentication**: Secure API access
- **Rate Limiting**: Prevent abuse
- **CSRF Protection**: Prevent cross-site request forgery
- **Helmet**: Secure HTTP headers
- **Content Security Policy**: Prevent XSS attacks
- **HTTPS**: Secure communication

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
