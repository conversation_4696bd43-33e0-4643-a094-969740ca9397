/**
 * Regulation Validator for NovaRollups
 * 
 * This module handles the validation of compliance against specific regulations,
 * ensuring that transactions meet the requirements of each regulatory framework.
 */

/**
 * Regulation Validator
 * @class RegulationValidator
 */
class RegulationValidator {
  /**
   * Create a new RegulationValidator
   * @param {Object} options - Configuration options
   * @param {Object} [options.regulationConfig] - Regulation-specific configuration
   * @param {boolean} [options.strictMode=false] - Strict validation mode
   */
  constructor(options = {}) {
    this.options = {
      strictMode: false,
      ...options
    };
    
    // Initialize regulations
    this.regulations = new Map();
    this._initRegulations(options.regulationConfig);
  }
  
  /**
   * Initialize supported regulations
   * @param {Object} config - Regulation configuration
   * @private
   */
  _initRegulations(config = {}) {
    // Initialize GDPR
    this.regulations.set('GDPR', {
      name: 'General Data Protection Regulation',
      version: config.gdprVersion || '2016/679',
      active: true,
      validationRules: this._getGDPRRules(),
      metadata: {
        region: 'EU',
        dataSubjectRights: true,
        consentRequired: true,
        dataMinimization: true
      }
    });
    
    // Initialize HIPAA
    this.regulations.set('HIPAA', {
      name: 'Health Insurance Portability and Accountability Act',
      version: config.hipaaVersion || '1996',
      active: true,
      validationRules: this._getHIPAARules(),
      metadata: {
        region: 'US',
        protectedHealthInformation: true,
        securityRule: true,
        privacyRule: true
      }
    });
    
    // Initialize PCI-DSS
    this.regulations.set('PCI-DSS', {
      name: 'Payment Card Industry Data Security Standard',
      version: config.pciVersion || '4.0',
      active: true,
      validationRules: this._getPCIDSSRules(),
      metadata: {
        global: true,
        cardholderData: true,
        sensitiveAuthenticationData: true,
        merchantLevels: 4
      }
    });
    
    // Initialize CCPA
    this.regulations.set('CCPA', {
      name: 'California Consumer Privacy Act',
      version: config.ccpaVersion || '2018',
      active: true,
      validationRules: this._getCCPARules(),
      metadata: {
        region: 'US-CA',
        consumerRights: true,
        optOut: true,
        dataDisclosure: true
      }
    });
    
    // Add custom regulations from config
    if (config && config.customRegulations) {
      for (const [code, regulation] of Object.entries(config.customRegulations)) {
        this.regulations.set(code, {
          ...regulation,
          active: regulation.active !== false
        });
      }
    }
  }
  
  /**
   * Get GDPR validation rules
   * @returns {Array} - GDPR validation rules
   * @private
   */
  _getGDPRRules() {
    return [
      {
        id: 'gdpr-consent',
        name: 'Consent Verification',
        description: 'Verify that valid consent has been obtained',
        validate: (data) => {
          return data.consent === true || data.legalBasis === 'legitimate_interest';
        }
      },
      {
        id: 'gdpr-data-minimization',
        name: 'Data Minimization',
        description: 'Ensure only necessary data is processed',
        validate: (data) => {
          return !data.excessiveData;
        }
      },
      {
        id: 'gdpr-subject-rights',
        name: 'Data Subject Rights',
        description: 'Support for data subject rights',
        validate: (data) => {
          return data.subjectRightsSupported !== false;
        }
      }
    ];
  }
  
  /**
   * Get HIPAA validation rules
   * @returns {Array} - HIPAA validation rules
   * @private
   */
  _getHIPAARules() {
    return [
      {
        id: 'hipaa-phi',
        name: 'Protected Health Information',
        description: 'Verify PHI is properly protected',
        validate: (data) => {
          return !data.containsPHI || data.phiProtected === true;
        }
      },
      {
        id: 'hipaa-minimum-necessary',
        name: 'Minimum Necessary Standard',
        description: 'Ensure only minimum necessary information is used',
        validate: (data) => {
          return data.minimumNecessary !== false;
        }
      },
      {
        id: 'hipaa-authorization',
        name: 'Patient Authorization',
        description: 'Verify patient authorization for disclosure',
        validate: (data) => {
          return !data.requiresAuthorization || data.authorization === true;
        }
      }
    ];
  }
  
  /**
   * Get PCI-DSS validation rules
   * @returns {Array} - PCI-DSS validation rules
   * @private
   */
  _getPCIDSSRules() {
    return [
      {
        id: 'pci-card-data',
        name: 'Cardholder Data Protection',
        description: 'Verify cardholder data is protected',
        validate: (data) => {
          return !data.containsCardData || data.cardDataProtected === true;
        }
      },
      {
        id: 'pci-encryption',
        name: 'Encryption Verification',
        description: 'Ensure data is properly encrypted',
        validate: (data) => {
          return !data.requiresEncryption || data.encrypted === true;
        }
      },
      {
        id: 'pci-scope',
        name: 'PCI Scope Verification',
        description: 'Verify if transaction is in PCI scope',
        validate: (data) => {
          return data.pciScope !== 'in_scope' || data.pciCompliant === true;
        }
      }
    ];
  }
  
  /**
   * Get CCPA validation rules
   * @returns {Array} - CCPA validation rules
   * @private
   */
  _getCCPARules() {
    return [
      {
        id: 'ccpa-disclosure',
        name: 'Disclosure Verification',
        description: 'Verify proper disclosure of data collection',
        validate: (data) => {
          return data.disclosureProvided !== false;
        }
      },
      {
        id: 'ccpa-opt-out',
        name: 'Opt-Out Right',
        description: 'Support for opt-out rights',
        validate: (data) => {
          return !data.optOutRequested || data.optOutHonored === true;
        }
      },
      {
        id: 'ccpa-deletion',
        name: 'Deletion Request',
        description: 'Support for deletion requests',
        validate: (data) => {
          return !data.deletionRequested || data.deletionHonored === true;
        }
      }
    ];
  }
  
  /**
   * Validate a regulation
   * @param {string} regulation - Regulation code
   * @returns {Promise<Object>} - Validation result
   */
  async validateRegulation(regulation) {
    const regulationData = this.regulations.get(regulation);
    
    if (!regulationData) {
      throw new Error(`Unsupported regulation: ${regulation}`);
    }
    
    if (!regulationData.active) {
      throw new Error(`Regulation is not active: ${regulation}`);
    }
    
    return {
      valid: true,
      regulation,
      name: regulationData.name,
      version: regulationData.version
    };
  }
  
  /**
   * Validate compliance against a specific regulation
   * @param {Object} data - Data to validate
   * @param {string} regulation - Regulation code
   * @returns {Promise<Object>} - Validation result
   */
  async validateCompliance(data, regulation) {
    const regulationData = this.regulations.get(regulation);
    
    if (!regulationData) {
      throw new Error(`Unsupported regulation: ${regulation}`);
    }
    
    if (!regulationData.active) {
      throw new Error(`Regulation is not active: ${regulation}`);
    }
    
    // Apply validation rules
    const validationResults = regulationData.validationRules.map(rule => {
      try {
        const valid = rule.validate(data);
        return {
          ruleId: rule.id,
          name: rule.name,
          valid,
          error: valid ? null : `Failed validation: ${rule.description}`
        };
      } catch (error) {
        return {
          ruleId: rule.id,
          name: rule.name,
          valid: false,
          error: `Error during validation: ${error.message}`
        };
      }
    });
    
    // Check if all rules passed
    const allValid = validationResults.every(result => result.valid);
    
    // In strict mode, all rules must pass
    if (this.options.strictMode && !allValid) {
      throw new Error(`Compliance validation failed for ${regulation}`);
    }
    
    return {
      valid: allValid,
      regulation,
      name: regulationData.name,
      version: regulationData.version,
      timestamp: new Date(),
      results: validationResults
    };
  }
  
  /**
   * Get the status of a regulation
   * @param {string} regulation - Regulation code
   * @returns {Promise<Object>} - Regulation status
   */
  async getRegulationStatus(regulation) {
    const regulationData = this.regulations.get(regulation);
    
    if (!regulationData) {
      throw new Error(`Unsupported regulation: ${regulation}`);
    }
    
    return {
      regulation,
      name: regulationData.name,
      version: regulationData.version,
      active: regulationData.active,
      metadata: regulationData.metadata
    };
  }
  
  /**
   * Get all supported regulations
   * @returns {Promise<Array>} - Supported regulations
   */
  async getSupportedRegulations() {
    const regulations = [];
    
    for (const [code, data] of this.regulations.entries()) {
      regulations.push({
        code,
        name: data.name,
        version: data.version,
        active: data.active
      });
    }
    
    return regulations;
  }
}

module.exports = RegulationValidator;
