/**
 * ThemeSelector Component
 * 
 * A component that allows users to select a theme.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme/ThemeContext';
import { getAllThemes } from '../theme/themes';
import { AccessibleTooltip } from './index';

/**
 * ThemeSelector component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.variant='dropdown'] - Selector variant (dropdown, buttons, menu)
 * @param {boolean} [props.showColorModeToggle=true] - Whether to show color mode toggle
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} ThemeSelector component
 */
const ThemeSelector = ({
  variant = 'dropdown',
  showColorModeToggle = true,
  className = '',
  style = {}
}) => {
  const { theme, setTheme, colorMode, toggleColorMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const themes = getAllThemes();
  
  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  
  // Close dropdown
  const closeDropdown = () => {
    setIsOpen(false);
  };
  
  // Handle theme change
  const handleThemeChange = (newTheme) => {
    setTheme(newTheme);
    closeDropdown();
  };
  
  // Handle color mode toggle
  const handleColorModeToggle = () => {
    toggleColorMode();
  };
  
  // Render dropdown variant
  const renderDropdown = () => {
    return (
      <div className="relative">
        <button
          className="flex items-center px-3 py-2 rounded-md bg-surface border border-divider text-textPrimary hover:bg-actionHover focus:outline-none focus:ring-2 focus:ring-primary"
          onClick={toggleDropdown}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          data-testid="theme-selector-dropdown-button"
        >
          <span className="mr-2">{theme.name}</span>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
        
        {isOpen && (
          <ul
            className="absolute z-10 mt-1 w-48 rounded-md bg-surface border border-divider shadow-lg"
            role="listbox"
            aria-labelledby="theme-selector-dropdown-button"
            data-testid="theme-selector-dropdown-menu"
          >
            {themes.map((themeOption) => (
              <li
                key={themeOption.name}
                className={`
                  px-4 py-2 cursor-pointer hover:bg-actionHover
                  ${theme.name === themeOption.name ? 'bg-actionSelected text-primary' : 'text-textPrimary'}
                `}
                role="option"
                aria-selected={theme.name === themeOption.name}
                onClick={() => handleThemeChange(themeOption)}
                data-testid={`theme-option-${themeOption.name.toLowerCase().replace(/\s+/g, '-')}`}
              >
                {themeOption.name}
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };
  
  // Render buttons variant
  const renderButtons = () => {
    return (
      <div className="flex flex-wrap gap-2">
        {themes.map((themeOption) => (
          <button
            key={themeOption.name}
            className={`
              px-3 py-1 rounded-md border transition-colors duration-200
              ${theme.name === themeOption.name
                ? 'bg-primary text-primaryContrast border-primary'
                : 'bg-surface text-textPrimary border-divider hover:bg-actionHover'
              }
              focus:outline-none focus:ring-2 focus:ring-primary
            `}
            onClick={() => handleThemeChange(themeOption)}
            aria-pressed={theme.name === themeOption.name}
            data-testid={`theme-button-${themeOption.name.toLowerCase().replace(/\s+/g, '-')}`}
          >
            {themeOption.name}
          </button>
        ))}
      </div>
    );
  };
  
  // Render menu variant
  const renderMenu = () => {
    return (
      <div className="flex flex-col space-y-1 p-2 rounded-md bg-surface border border-divider">
        <div className="px-3 py-2 font-medium text-textPrimary">Themes</div>
        {themes.map((themeOption) => (
          <button
            key={themeOption.name}
            className={`
              flex items-center px-3 py-2 rounded-md text-left transition-colors duration-200
              ${theme.name === themeOption.name
                ? 'bg-actionSelected text-primary'
                : 'text-textPrimary hover:bg-actionHover'
              }
              focus:outline-none focus:ring-2 focus:ring-primary
            `}
            onClick={() => handleThemeChange(themeOption)}
            aria-pressed={theme.name === themeOption.name}
            data-testid={`theme-menu-item-${themeOption.name.toLowerCase().replace(/\s+/g, '-')}`}
          >
            <span className="flex-1">{themeOption.name}</span>
            {theme.name === themeOption.name && (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </button>
        ))}
      </div>
    );
  };
  
  // Render color mode toggle
  const renderColorModeToggle = () => {
    return (
      <AccessibleTooltip content={`Switch to ${colorMode === 'light' ? 'dark' : 'light'} mode`}>
        <button
          className="p-2 rounded-md bg-surface border border-divider text-textPrimary hover:bg-actionHover focus:outline-none focus:ring-2 focus:ring-primary"
          onClick={handleColorModeToggle}
          aria-pressed={colorMode === 'dark'}
          data-testid="color-mode-toggle"
        >
          {colorMode === 'light' ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </AccessibleTooltip>
    );
  };
  
  return (
    <div
      className={`flex items-center space-x-2 ${className}`}
      style={style}
      data-testid="theme-selector"
    >
      {variant === 'dropdown' && renderDropdown()}
      {variant === 'buttons' && renderButtons()}
      {variant === 'menu' && renderMenu()}
      
      {showColorModeToggle && renderColorModeToggle()}
    </div>
  );
};

ThemeSelector.propTypes = {
  variant: PropTypes.oneOf(['dropdown', 'buttons', 'menu']),
  showColorModeToggle: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default ThemeSelector;
