FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    netcat \
    iputils-ping \
    traceroute \
    nmap \
    apache2-utils \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements-simulator.txt /tmp/
RUN pip install --no-cache-dir -r /tmp/requirements-simulator.txt

# Create app directory
WORKDIR /app

# Copy simulation scripts
COPY kethernet-simulation-suite.py /app/simulator.py
COPY simulation-utils/ /app/utils/
COPY consciousness-validation/ /app/validation/

# Create results directory
RUN mkdir -p /app/results

# Set environment variables
ENV PYTHONPATH=/app
ENV SIMULATION_MODE=full
ENV CONSCIOUSNESS_THRESHOLD=2847
ENV COHERENCE_VALIDATION=true
ENV TRINITY_STACK_ENABLED=true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8080/health')" || exit 1

# Default command
CMD ["python", "/app/simulator.py"]
