{"version": 3, "names": ["mongoose", "require", "MongoMemoryServer", "RBACService", "Role", "Permission", "UserRole", "User", "mongoServer", "rbacService", "testUser", "adminRole", "userRole", "viewPermission", "editPermission", "beforeAll", "create", "uri", "get<PERSON><PERSON>", "connect", "useNewUrlParser", "useUnifiedTopology", "initializeDatabase", "afterAll", "disconnect", "stop", "beforeEach", "username", "email", "password", "firstName", "lastName", "status", "save", "findOne", "name", "description", "resource", "action", "isSystem", "after<PERSON>ach", "deleteMany", "deleteOne", "describe", "test", "roles", "getAllRoles", "expect", "toBeDefined", "length", "toBeGreaterThanOrEqual", "map", "r", "toContain", "role", "getRoleById", "_id", "toBe", "permissions", "newRole", "createRole", "updatedRole", "updateRole", "result", "deleteRole", "success", "deletedRole", "findById", "toBeNull", "getAllPermissions", "p", "permission", "getPermissionById", "getPermissionByResourceAction", "assignRoleToUser", "userRoles", "getUserRoles", "toString", "removeRoleFromUser", "hasViewPermission", "hasPermission", "hasEditPermission", "getUserPermissions", "not"], "sources": ["rbac.test.js"], "sourcesContent": ["/**\n * RBAC Tests\n * \n * This file contains tests for the RBAC system.\n */\n\nconst mongoose = require('mongoose');\nconst { MongoMemoryServer } = require('mongodb-memory-server');\nconst RBACService = require('../../api/services/RBACService');\nconst Role = require('../../api/models/Role');\nconst Permission = require('../../api/models/Permission');\nconst UserRole = require('../../api/models/UserRole');\nconst User = require('../../api/models/User');\n\nlet mongoServer;\nlet rbacService;\nlet testUser;\nlet adminRole;\nlet userRole;\nlet viewPermission;\nlet editPermission;\n\n// Connect to in-memory MongoDB\nbeforeAll(async () => {\n  mongoServer = await MongoMemoryServer.create();\n  const uri = mongoServer.getUri();\n  \n  await mongoose.connect(uri, {\n    useNewUrlParser: true,\n    useUnifiedTopology: true\n  });\n  \n  // Create RBAC service\n  rbacService = new RBACService();\n  \n  // Initialize database with default roles and permissions\n  await rbacService.initializeDatabase();\n});\n\n// Disconnect from in-memory MongoDB\nafterAll(async () => {\n  await mongoose.disconnect();\n  await mongoServer.stop();\n});\n\n// Clear database before each test\nbeforeEach(async () => {\n  // Create test user\n  testUser = new User({\n    username: 'testuser',\n    email: '<EMAIL>',\n    password: 'password123',\n    firstName: 'Test',\n    lastName: 'User',\n    status: 'active'\n  });\n  \n  await testUser.save();\n  \n  // Get roles\n  adminRole = await Role.findOne({ name: 'Administrator' });\n  userRole = await Role.findOne({ name: 'User' });\n  \n  // Create test permissions\n  viewPermission = new Permission({\n    name: 'View Test',\n    description: 'View test resources',\n    resource: 'test',\n    action: 'view',\n    isSystem: false\n  });\n  \n  editPermission = new Permission({\n    name: 'Edit Test',\n    description: 'Edit test resources',\n    resource: 'test',\n    action: 'edit',\n    isSystem: false\n  });\n  \n  await viewPermission.save();\n  await editPermission.save();\n});\n\n// Clear database after each test\nafterEach(async () => {\n  await User.deleteMany({});\n  await UserRole.deleteMany({});\n  \n  // Delete test permissions\n  await Permission.deleteOne({ resource: 'test', action: 'view' });\n  await Permission.deleteOne({ resource: 'test', action: 'edit' });\n});\n\ndescribe('RBAC Service', () => {\n  describe('Role Management', () => {\n    test('should get all roles', async () => {\n      const roles = await rbacService.getAllRoles();\n      \n      expect(roles).toBeDefined();\n      expect(roles.length).toBeGreaterThanOrEqual(4); // At least 4 default roles\n      expect(roles.map(r => r.name)).toContain('Administrator');\n      expect(roles.map(r => r.name)).toContain('Manager');\n      expect(roles.map(r => r.name)).toContain('User');\n      expect(roles.map(r => r.name)).toContain('Viewer');\n    });\n    \n    test('should get role by ID', async () => {\n      const role = await rbacService.getRoleById(adminRole._id);\n      \n      expect(role).toBeDefined();\n      expect(role.name).toBe('Administrator');\n      expect(role.permissions).toContain('*');\n    });\n    \n    test('should create a new role', async () => {\n      const newRole = await rbacService.createRole({\n        name: 'Test Role',\n        description: 'Test role description',\n        permissions: ['test:view', 'test:edit']\n      });\n      \n      expect(newRole).toBeDefined();\n      expect(newRole.name).toBe('Test Role');\n      expect(newRole.description).toBe('Test role description');\n      expect(newRole.permissions).toContain('test:view');\n      expect(newRole.permissions).toContain('test:edit');\n      \n      // Clean up\n      await Role.deleteOne({ _id: newRole._id });\n    });\n    \n    test('should update a role', async () => {\n      // Create a role to update\n      const role = new Role({\n        name: 'Role to Update',\n        description: 'Original description',\n        permissions: ['test:view']\n      });\n      \n      await role.save();\n      \n      // Update the role\n      const updatedRole = await rbacService.updateRole(role._id, {\n        name: 'Updated Role',\n        description: 'Updated description',\n        permissions: ['test:view', 'test:edit']\n      });\n      \n      expect(updatedRole).toBeDefined();\n      expect(updatedRole.name).toBe('Updated Role');\n      expect(updatedRole.description).toBe('Updated description');\n      expect(updatedRole.permissions).toContain('test:view');\n      expect(updatedRole.permissions).toContain('test:edit');\n      \n      // Clean up\n      await Role.deleteOne({ _id: role._id });\n    });\n    \n    test('should delete a role', async () => {\n      // Create a role to delete\n      const role = new Role({\n        name: 'Role to Delete',\n        description: 'This role will be deleted',\n        permissions: ['test:view']\n      });\n      \n      await role.save();\n      \n      // Delete the role\n      const result = await rbacService.deleteRole(role._id);\n      \n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      \n      // Verify role is deleted\n      const deletedRole = await Role.findById(role._id);\n      expect(deletedRole).toBeNull();\n    });\n  });\n  \n  describe('Permission Management', () => {\n    test('should get all permissions', async () => {\n      const permissions = await rbacService.getAllPermissions();\n      \n      expect(permissions).toBeDefined();\n      expect(permissions.length).toBeGreaterThanOrEqual(2); // At least our 2 test permissions\n      expect(permissions.map(p => p.name)).toContain('View Test');\n      expect(permissions.map(p => p.name)).toContain('Edit Test');\n    });\n    \n    test('should get permission by ID', async () => {\n      const permission = await rbacService.getPermissionById(viewPermission._id);\n      \n      expect(permission).toBeDefined();\n      expect(permission.name).toBe('View Test');\n      expect(permission.resource).toBe('test');\n      expect(permission.action).toBe('view');\n    });\n    \n    test('should get permission by resource and action', async () => {\n      const permission = await rbacService.getPermissionByResourceAction('test', 'view');\n      \n      expect(permission).toBeDefined();\n      expect(permission.name).toBe('View Test');\n      expect(permission.resource).toBe('test');\n      expect(permission.action).toBe('view');\n    });\n  });\n  \n  describe('User Role Management', () => {\n    test('should assign role to user', async () => {\n      const result = await rbacService.assignRoleToUser(testUser._id, adminRole._id);\n      \n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      \n      // Verify user has role\n      const userRoles = await rbacService.getUserRoles(testUser._id);\n      expect(userRoles).toBeDefined();\n      expect(userRoles.length).toBe(1);\n      expect(userRoles[0]._id.toString()).toBe(adminRole._id.toString());\n    });\n    \n    test('should remove role from user', async () => {\n      // Assign role to user\n      await rbacService.assignRoleToUser(testUser._id, adminRole._id);\n      \n      // Remove role from user\n      const result = await rbacService.removeRoleFromUser(testUser._id, adminRole._id);\n      \n      expect(result).toBeDefined();\n      expect(result.success).toBe(true);\n      \n      // Verify user does not have role\n      const userRoles = await rbacService.getUserRoles(testUser._id);\n      expect(userRoles).toBeDefined();\n      expect(userRoles.length).toBe(0);\n    });\n  });\n  \n  describe('Permission Checking', () => {\n    test('should check if user has permission', async () => {\n      // Assign role to user\n      await rbacService.assignRoleToUser(testUser._id, userRole._id);\n      \n      // Check if user has permission\n      const hasViewPermission = await rbacService.hasPermission(testUser._id, 'connector:view');\n      const hasEditPermission = await rbacService.hasPermission(testUser._id, 'connector:edit');\n      \n      expect(hasViewPermission).toBe(true);\n      expect(hasEditPermission).toBe(false);\n    });\n    \n    test('should get user permissions', async () => {\n      // Assign role to user\n      await rbacService.assignRoleToUser(testUser._id, userRole._id);\n      \n      // Get user permissions\n      const permissions = await rbacService.getUserPermissions(testUser._id);\n      \n      expect(permissions).toBeDefined();\n      expect(permissions).toContain('connector:view');\n      expect(permissions).toContain('connector:use');\n      expect(permissions).toContain('workflow:view');\n      expect(permissions).toContain('workflow:use');\n      expect(permissions).toContain('normalization:view');\n      expect(permissions).toContain('normalization:use');\n      expect(permissions).toContain('monitoring:view');\n      expect(permissions).not.toContain('connector:edit');\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AACpC,MAAM;EAAEC;AAAkB,CAAC,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAC9D,MAAME,WAAW,GAAGF,OAAO,CAAC,gCAAgC,CAAC;AAC7D,MAAMG,IAAI,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAC7C,MAAMI,UAAU,GAAGJ,OAAO,CAAC,6BAA6B,CAAC;AACzD,MAAMK,QAAQ,GAAGL,OAAO,CAAC,2BAA2B,CAAC;AACrD,MAAMM,IAAI,GAAGN,OAAO,CAAC,uBAAuB,CAAC;AAE7C,IAAIO,WAAW;AACf,IAAIC,WAAW;AACf,IAAIC,QAAQ;AACZ,IAAIC,SAAS;AACb,IAAIC,QAAQ;AACZ,IAAIC,cAAc;AAClB,IAAIC,cAAc;;AAElB;AACAC,SAAS,CAAC,YAAY;EACpBP,WAAW,GAAG,MAAMN,iBAAiB,CAACc,MAAM,CAAC,CAAC;EAC9C,MAAMC,GAAG,GAAGT,WAAW,CAACU,MAAM,CAAC,CAAC;EAEhC,MAAMlB,QAAQ,CAACmB,OAAO,CAACF,GAAG,EAAE;IAC1BG,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACAZ,WAAW,GAAG,IAAIN,WAAW,CAAC,CAAC;;EAE/B;EACA,MAAMM,WAAW,CAACa,kBAAkB,CAAC,CAAC;AACxC,CAAC,CAAC;;AAEF;AACAC,QAAQ,CAAC,YAAY;EACnB,MAAMvB,QAAQ,CAACwB,UAAU,CAAC,CAAC;EAC3B,MAAMhB,WAAW,CAACiB,IAAI,CAAC,CAAC;AAC1B,CAAC,CAAC;;AAEF;AACAC,UAAU,CAAC,YAAY;EACrB;EACAhB,QAAQ,GAAG,IAAIH,IAAI,CAAC;IAClBoB,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,aAAa;IACvBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMtB,QAAQ,CAACuB,IAAI,CAAC,CAAC;;EAErB;EACAtB,SAAS,GAAG,MAAMP,IAAI,CAAC8B,OAAO,CAAC;IAAEC,IAAI,EAAE;EAAgB,CAAC,CAAC;EACzDvB,QAAQ,GAAG,MAAMR,IAAI,CAAC8B,OAAO,CAAC;IAAEC,IAAI,EAAE;EAAO,CAAC,CAAC;;EAE/C;EACAtB,cAAc,GAAG,IAAIR,UAAU,CAAC;IAC9B8B,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFzB,cAAc,GAAG,IAAIT,UAAU,CAAC;IAC9B8B,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM1B,cAAc,CAACoB,IAAI,CAAC,CAAC;EAC3B,MAAMnB,cAAc,CAACmB,IAAI,CAAC,CAAC;AAC7B,CAAC,CAAC;;AAEF;AACAO,SAAS,CAAC,YAAY;EACpB,MAAMjC,IAAI,CAACkC,UAAU,CAAC,CAAC,CAAC,CAAC;EACzB,MAAMnC,QAAQ,CAACmC,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE7B;EACA,MAAMpC,UAAU,CAACqC,SAAS,CAAC;IAAEL,QAAQ,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,CAAC;EAChE,MAAMjC,UAAU,CAACqC,SAAS,CAAC;IAAEL,QAAQ,EAAE,MAAM;IAAEC,MAAM,EAAE;EAAO,CAAC,CAAC;AAClE,CAAC,CAAC;AAEFK,QAAQ,CAAC,cAAc,EAAE,MAAM;EAC7BA,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCC,IAAI,CAAC,sBAAsB,EAAE,YAAY;MACvC,MAAMC,KAAK,GAAG,MAAMpC,WAAW,CAACqC,WAAW,CAAC,CAAC;MAE7CC,MAAM,CAACF,KAAK,CAAC,CAACG,WAAW,CAAC,CAAC;MAC3BD,MAAM,CAACF,KAAK,CAACI,MAAM,CAAC,CAACC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;MAChDH,MAAM,CAACF,KAAK,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC,CAACkB,SAAS,CAAC,eAAe,CAAC;MACzDN,MAAM,CAACF,KAAK,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC,CAACkB,SAAS,CAAC,SAAS,CAAC;MACnDN,MAAM,CAACF,KAAK,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC,CAACkB,SAAS,CAAC,MAAM,CAAC;MAChDN,MAAM,CAACF,KAAK,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,CAAC,CAAC,CAACkB,SAAS,CAAC,QAAQ,CAAC;IACpD,CAAC,CAAC;IAEFT,IAAI,CAAC,uBAAuB,EAAE,YAAY;MACxC,MAAMU,IAAI,GAAG,MAAM7C,WAAW,CAAC8C,WAAW,CAAC5C,SAAS,CAAC6C,GAAG,CAAC;MAEzDT,MAAM,CAACO,IAAI,CAAC,CAACN,WAAW,CAAC,CAAC;MAC1BD,MAAM,CAACO,IAAI,CAACnB,IAAI,CAAC,CAACsB,IAAI,CAAC,eAAe,CAAC;MACvCV,MAAM,CAACO,IAAI,CAACI,WAAW,CAAC,CAACL,SAAS,CAAC,GAAG,CAAC;IACzC,CAAC,CAAC;IAEFT,IAAI,CAAC,0BAA0B,EAAE,YAAY;MAC3C,MAAMe,OAAO,GAAG,MAAMlD,WAAW,CAACmD,UAAU,CAAC;QAC3CzB,IAAI,EAAE,WAAW;QACjBC,WAAW,EAAE,uBAAuB;QACpCsB,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW;MACxC,CAAC,CAAC;MAEFX,MAAM,CAACY,OAAO,CAAC,CAACX,WAAW,CAAC,CAAC;MAC7BD,MAAM,CAACY,OAAO,CAACxB,IAAI,CAAC,CAACsB,IAAI,CAAC,WAAW,CAAC;MACtCV,MAAM,CAACY,OAAO,CAACvB,WAAW,CAAC,CAACqB,IAAI,CAAC,uBAAuB,CAAC;MACzDV,MAAM,CAACY,OAAO,CAACD,WAAW,CAAC,CAACL,SAAS,CAAC,WAAW,CAAC;MAClDN,MAAM,CAACY,OAAO,CAACD,WAAW,CAAC,CAACL,SAAS,CAAC,WAAW,CAAC;;MAElD;MACA,MAAMjD,IAAI,CAACsC,SAAS,CAAC;QAAEc,GAAG,EAAEG,OAAO,CAACH;MAAI,CAAC,CAAC;IAC5C,CAAC,CAAC;IAEFZ,IAAI,CAAC,sBAAsB,EAAE,YAAY;MACvC;MACA,MAAMU,IAAI,GAAG,IAAIlD,IAAI,CAAC;QACpB+B,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,sBAAsB;QACnCsB,WAAW,EAAE,CAAC,WAAW;MAC3B,CAAC,CAAC;MAEF,MAAMJ,IAAI,CAACrB,IAAI,CAAC,CAAC;;MAEjB;MACA,MAAM4B,WAAW,GAAG,MAAMpD,WAAW,CAACqD,UAAU,CAACR,IAAI,CAACE,GAAG,EAAE;QACzDrB,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,qBAAqB;QAClCsB,WAAW,EAAE,CAAC,WAAW,EAAE,WAAW;MACxC,CAAC,CAAC;MAEFX,MAAM,CAACc,WAAW,CAAC,CAACb,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACc,WAAW,CAAC1B,IAAI,CAAC,CAACsB,IAAI,CAAC,cAAc,CAAC;MAC7CV,MAAM,CAACc,WAAW,CAACzB,WAAW,CAAC,CAACqB,IAAI,CAAC,qBAAqB,CAAC;MAC3DV,MAAM,CAACc,WAAW,CAACH,WAAW,CAAC,CAACL,SAAS,CAAC,WAAW,CAAC;MACtDN,MAAM,CAACc,WAAW,CAACH,WAAW,CAAC,CAACL,SAAS,CAAC,WAAW,CAAC;;MAEtD;MACA,MAAMjD,IAAI,CAACsC,SAAS,CAAC;QAAEc,GAAG,EAAEF,IAAI,CAACE;MAAI,CAAC,CAAC;IACzC,CAAC,CAAC;IAEFZ,IAAI,CAAC,sBAAsB,EAAE,YAAY;MACvC;MACA,MAAMU,IAAI,GAAG,IAAIlD,IAAI,CAAC;QACpB+B,IAAI,EAAE,gBAAgB;QACtBC,WAAW,EAAE,2BAA2B;QACxCsB,WAAW,EAAE,CAAC,WAAW;MAC3B,CAAC,CAAC;MAEF,MAAMJ,IAAI,CAACrB,IAAI,CAAC,CAAC;;MAEjB;MACA,MAAM8B,MAAM,GAAG,MAAMtD,WAAW,CAACuD,UAAU,CAACV,IAAI,CAACE,GAAG,CAAC;MAErDT,MAAM,CAACgB,MAAM,CAAC,CAACf,WAAW,CAAC,CAAC;MAC5BD,MAAM,CAACgB,MAAM,CAACE,OAAO,CAAC,CAACR,IAAI,CAAC,IAAI,CAAC;;MAEjC;MACA,MAAMS,WAAW,GAAG,MAAM9D,IAAI,CAAC+D,QAAQ,CAACb,IAAI,CAACE,GAAG,CAAC;MACjDT,MAAM,CAACmB,WAAW,CAAC,CAACE,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFzB,QAAQ,CAAC,uBAAuB,EAAE,MAAM;IACtCC,IAAI,CAAC,4BAA4B,EAAE,YAAY;MAC7C,MAAMc,WAAW,GAAG,MAAMjD,WAAW,CAAC4D,iBAAiB,CAAC,CAAC;MAEzDtB,MAAM,CAACW,WAAW,CAAC,CAACV,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACW,WAAW,CAACT,MAAM,CAAC,CAACC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;MACtDH,MAAM,CAACW,WAAW,CAACP,GAAG,CAACmB,CAAC,IAAIA,CAAC,CAACnC,IAAI,CAAC,CAAC,CAACkB,SAAS,CAAC,WAAW,CAAC;MAC3DN,MAAM,CAACW,WAAW,CAACP,GAAG,CAACmB,CAAC,IAAIA,CAAC,CAACnC,IAAI,CAAC,CAAC,CAACkB,SAAS,CAAC,WAAW,CAAC;IAC7D,CAAC,CAAC;IAEFT,IAAI,CAAC,6BAA6B,EAAE,YAAY;MAC9C,MAAM2B,UAAU,GAAG,MAAM9D,WAAW,CAAC+D,iBAAiB,CAAC3D,cAAc,CAAC2C,GAAG,CAAC;MAE1ET,MAAM,CAACwB,UAAU,CAAC,CAACvB,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACwB,UAAU,CAACpC,IAAI,CAAC,CAACsB,IAAI,CAAC,WAAW,CAAC;MACzCV,MAAM,CAACwB,UAAU,CAAClC,QAAQ,CAAC,CAACoB,IAAI,CAAC,MAAM,CAAC;MACxCV,MAAM,CAACwB,UAAU,CAACjC,MAAM,CAAC,CAACmB,IAAI,CAAC,MAAM,CAAC;IACxC,CAAC,CAAC;IAEFb,IAAI,CAAC,8CAA8C,EAAE,YAAY;MAC/D,MAAM2B,UAAU,GAAG,MAAM9D,WAAW,CAACgE,6BAA6B,CAAC,MAAM,EAAE,MAAM,CAAC;MAElF1B,MAAM,CAACwB,UAAU,CAAC,CAACvB,WAAW,CAAC,CAAC;MAChCD,MAAM,CAACwB,UAAU,CAACpC,IAAI,CAAC,CAACsB,IAAI,CAAC,WAAW,CAAC;MACzCV,MAAM,CAACwB,UAAU,CAAClC,QAAQ,CAAC,CAACoB,IAAI,CAAC,MAAM,CAAC;MACxCV,MAAM,CAACwB,UAAU,CAACjC,MAAM,CAAC,CAACmB,IAAI,CAAC,MAAM,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFd,QAAQ,CAAC,sBAAsB,EAAE,MAAM;IACrCC,IAAI,CAAC,4BAA4B,EAAE,YAAY;MAC7C,MAAMmB,MAAM,GAAG,MAAMtD,WAAW,CAACiE,gBAAgB,CAAChE,QAAQ,CAAC8C,GAAG,EAAE7C,SAAS,CAAC6C,GAAG,CAAC;MAE9ET,MAAM,CAACgB,MAAM,CAAC,CAACf,WAAW,CAAC,CAAC;MAC5BD,MAAM,CAACgB,MAAM,CAACE,OAAO,CAAC,CAACR,IAAI,CAAC,IAAI,CAAC;;MAEjC;MACA,MAAMkB,SAAS,GAAG,MAAMlE,WAAW,CAACmE,YAAY,CAAClE,QAAQ,CAAC8C,GAAG,CAAC;MAC9DT,MAAM,CAAC4B,SAAS,CAAC,CAAC3B,WAAW,CAAC,CAAC;MAC/BD,MAAM,CAAC4B,SAAS,CAAC1B,MAAM,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC;MAChCV,MAAM,CAAC4B,SAAS,CAAC,CAAC,CAAC,CAACnB,GAAG,CAACqB,QAAQ,CAAC,CAAC,CAAC,CAACpB,IAAI,CAAC9C,SAAS,CAAC6C,GAAG,CAACqB,QAAQ,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;IAEFjC,IAAI,CAAC,8BAA8B,EAAE,YAAY;MAC/C;MACA,MAAMnC,WAAW,CAACiE,gBAAgB,CAAChE,QAAQ,CAAC8C,GAAG,EAAE7C,SAAS,CAAC6C,GAAG,CAAC;;MAE/D;MACA,MAAMO,MAAM,GAAG,MAAMtD,WAAW,CAACqE,kBAAkB,CAACpE,QAAQ,CAAC8C,GAAG,EAAE7C,SAAS,CAAC6C,GAAG,CAAC;MAEhFT,MAAM,CAACgB,MAAM,CAAC,CAACf,WAAW,CAAC,CAAC;MAC5BD,MAAM,CAACgB,MAAM,CAACE,OAAO,CAAC,CAACR,IAAI,CAAC,IAAI,CAAC;;MAEjC;MACA,MAAMkB,SAAS,GAAG,MAAMlE,WAAW,CAACmE,YAAY,CAAClE,QAAQ,CAAC8C,GAAG,CAAC;MAC9DT,MAAM,CAAC4B,SAAS,CAAC,CAAC3B,WAAW,CAAC,CAAC;MAC/BD,MAAM,CAAC4B,SAAS,CAAC1B,MAAM,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFd,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpCC,IAAI,CAAC,qCAAqC,EAAE,YAAY;MACtD;MACA,MAAMnC,WAAW,CAACiE,gBAAgB,CAAChE,QAAQ,CAAC8C,GAAG,EAAE5C,QAAQ,CAAC4C,GAAG,CAAC;;MAE9D;MACA,MAAMuB,iBAAiB,GAAG,MAAMtE,WAAW,CAACuE,aAAa,CAACtE,QAAQ,CAAC8C,GAAG,EAAE,gBAAgB,CAAC;MACzF,MAAMyB,iBAAiB,GAAG,MAAMxE,WAAW,CAACuE,aAAa,CAACtE,QAAQ,CAAC8C,GAAG,EAAE,gBAAgB,CAAC;MAEzFT,MAAM,CAACgC,iBAAiB,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC;MACpCV,MAAM,CAACkC,iBAAiB,CAAC,CAACxB,IAAI,CAAC,KAAK,CAAC;IACvC,CAAC,CAAC;IAEFb,IAAI,CAAC,6BAA6B,EAAE,YAAY;MAC9C;MACA,MAAMnC,WAAW,CAACiE,gBAAgB,CAAChE,QAAQ,CAAC8C,GAAG,EAAE5C,QAAQ,CAAC4C,GAAG,CAAC;;MAE9D;MACA,MAAME,WAAW,GAAG,MAAMjD,WAAW,CAACyE,kBAAkB,CAACxE,QAAQ,CAAC8C,GAAG,CAAC;MAEtET,MAAM,CAACW,WAAW,CAAC,CAACV,WAAW,CAAC,CAAC;MACjCD,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,gBAAgB,CAAC;MAC/CN,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,eAAe,CAAC;MAC9CN,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,eAAe,CAAC;MAC9CN,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,cAAc,CAAC;MAC7CN,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,oBAAoB,CAAC;MACnDN,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,mBAAmB,CAAC;MAClDN,MAAM,CAACW,WAAW,CAAC,CAACL,SAAS,CAAC,iBAAiB,CAAC;MAChDN,MAAM,CAACW,WAAW,CAAC,CAACyB,GAAG,CAAC9B,SAAS,CAAC,gBAAgB,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}