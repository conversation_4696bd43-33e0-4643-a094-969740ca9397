/**
 * Environment Context
 * 
 * This context provides environment state and functions to components.
 */

import React, { createContext, useState, useEffect, useContext } from 'react';
import { environmentApi } from '../services/api';

// Create context
const EnvironmentContext = createContext();

// Environment provider component
export const EnvironmentProvider = ({ children }) => {
  const [environments, setEnvironments] = useState([]);
  const [currentEnvironment, setCurrentEnvironment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Load environments on mount
  useEffect(() => {
    loadEnvironments();
  }, []);
  
  // Load environments from API
  const loadEnvironments = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get all environments
      const response = await environmentApi.getAllEnvironments();
      setEnvironments(response.data);
      
      // Get default environment
      const defaultResponse = await environmentApi.getDefaultEnvironment();
      setCurrentEnvironment(defaultResponse.data);
      
      // Store current environment in localStorage
      localStorage.setItem('currentEnvironment', JSON.stringify(defaultResponse.data));
    } catch (error) {
      console.error('Error loading environments:', error);
      setError(error.response?.data?.message || error.message || 'Failed to load environments');
      
      // Try to load from localStorage
      const storedEnvironment = localStorage.getItem('currentEnvironment');
      if (storedEnvironment) {
        try {
          setCurrentEnvironment(JSON.parse(storedEnvironment));
        } catch (e) {
          console.error('Error parsing stored environment:', e);
        }
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Switch to a different environment
  const switchEnvironment = async (environmentId) => {
    try {
      setLoading(true);
      setError(null);
      
      // Get environment by ID
      const response = await environmentApi.getEnvironmentById(environmentId);
      setCurrentEnvironment(response.data);
      
      // Store current environment in localStorage
      localStorage.setItem('currentEnvironment', JSON.stringify(response.data));
      
      return response.data;
    } catch (error) {
      console.error('Error switching environment:', error);
      setError(error.response?.data?.message || error.message || 'Failed to switch environment');
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Create a new environment
  const createEnvironment = async (data) => {
    try {
      setLoading(true);
      setError(null);
      
      // Create environment
      const response = await environmentApi.createEnvironment(data);
      
      // Update environments list
      setEnvironments([...environments, response.data]);
      
      // If this is the first environment or it's set as default, set it as current
      if (environments.length === 0 || response.data.isDefault) {
        setCurrentEnvironment(response.data);
        localStorage.setItem('currentEnvironment', JSON.stringify(response.data));
      }
      
      return response.data;
    } catch (error) {
      console.error('Error creating environment:', error);
      setError(error.response?.data?.message || error.message || 'Failed to create environment');
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Update an environment
  const updateEnvironment = async (id, data) => {
    try {
      setLoading(true);
      setError(null);
      
      // Update environment
      const response = await environmentApi.updateEnvironment(id, data);
      
      // Update environments list
      setEnvironments(environments.map(env => 
        env.id === id ? response.data : env
      ));
      
      // If this is the current environment, update it
      if (currentEnvironment && currentEnvironment.id === id) {
        setCurrentEnvironment(response.data);
        localStorage.setItem('currentEnvironment', JSON.stringify(response.data));
      }
      
      // If this environment is now the default, update other environments
      if (response.data.isDefault) {
        setEnvironments(environments.map(env => 
          env.id !== id ? { ...env, isDefault: false } : env
        ));
      }
      
      return response.data;
    } catch (error) {
      console.error('Error updating environment:', error);
      setError(error.response?.data?.message || error.message || 'Failed to update environment');
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Delete an environment
  const deleteEnvironment = async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      // Delete environment
      await environmentApi.deleteEnvironment(id);
      
      // Update environments list
      const updatedEnvironments = environments.filter(env => env.id !== id);
      setEnvironments(updatedEnvironments);
      
      // If this was the current environment, switch to the default
      if (currentEnvironment && currentEnvironment.id === id) {
        const defaultEnv = updatedEnvironments.find(env => env.isDefault);
        if (defaultEnv) {
          setCurrentEnvironment(defaultEnv);
          localStorage.setItem('currentEnvironment', JSON.stringify(defaultEnv));
        }
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting environment:', error);
      setError(error.response?.data?.message || error.message || 'Failed to delete environment');
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Set default environment
  const setDefaultEnvironment = async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      // Set default environment
      const response = await environmentApi.setDefaultEnvironment(id);
      
      // Update environments list
      setEnvironments(environments.map(env => 
        env.id === id ? { ...env, isDefault: true } : { ...env, isDefault: false }
      ));
      
      return response.data;
    } catch (error) {
      console.error('Error setting default environment:', error);
      setError(error.response?.data?.message || error.message || 'Failed to set default environment');
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Promote environment
  const promoteEnvironment = async (sourceId, targetId) => {
    try {
      setLoading(true);
      setError(null);
      
      // Promote environment
      const response = await environmentApi.promoteEnvironment(sourceId, targetId);
      
      return response.data;
    } catch (error) {
      console.error('Error promoting environment:', error);
      setError(error.response?.data?.message || error.message || 'Failed to promote environment');
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Context value
  const value = {
    environments,
    currentEnvironment,
    loading,
    error,
    loadEnvironments,
    switchEnvironment,
    createEnvironment,
    updateEnvironment,
    deleteEnvironment,
    setDefaultEnvironment,
    promoteEnvironment
  };
  
  return (
    <EnvironmentContext.Provider value={value}>
      {children}
    </EnvironmentContext.Provider>
  );
};

// Custom hook to use the environment context
export const useEnvironment = () => {
  const context = useContext(EnvironmentContext);
  
  if (!context) {
    throw new Error('useEnvironment must be used within an EnvironmentProvider');
  }
  
  return context;
};

export default EnvironmentContext;
