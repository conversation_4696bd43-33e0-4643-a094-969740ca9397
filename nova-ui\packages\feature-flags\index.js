/**
 * Feature Flags Package
 *
 * This package provides a feature flag system for the NovaFuse platform.
 */

// Export the feature flag configuration
exports.featureFlags = require('./featureFlags').featureFlags;
exports.getFeatureFlag = require('./featureFlags').getFeatureFlag;
exports.isFeatureEnabled = require('./featureFlags').isFeatureEnabled;

// Export the ProductContext
exports.ProductContext = require('./ProductContext').ProductContext;
exports.ProductProvider = require('./ProductContext').ProductProvider;
exports.PRODUCTS = require('./ProductContext').PRODUCTS;

// Export the useFeatureFlag hook
exports.useFeatureFlag = require('./useFeatureFlag').useFeatureFlag;
exports.useProductFeatureFlag = require('./useFeatureFlag').useProductFeatureFlag;
exports.useIsProduct = require('./useFeatureFlag').useIsProduct;

// Export the advanced feature flag hooks
exports.useFeatureFlagAdvanced = require('./useFeatureFlagAdvanced').useFeatureFlagAdvanced;
exports.useAnyFeatureEnabled = require('./useFeatureFlagAdvanced').useAnyFeatureEnabled;
exports.useAllFeaturesEnabled = require('./useFeatureFlagAdvanced').useAllFeaturesEnabled;

// Export the FeatureFlagProvider
exports.FeatureFlagProvider = require('./FeatureFlagProvider').FeatureFlagProvider;

// Default export
module.exports.default = {
  FeatureFlagProvider: exports.FeatureFlagProvider,
  useFeatureFlag: exports.useFeatureFlag,
  useFeatureFlagAdvanced: exports.useFeatureFlagAdvanced,
  useAnyFeatureEnabled: exports.useAnyFeatureEnabled,
  useAllFeaturesEnabled: exports.useAllFeaturesEnabled,
  PRODUCTS: exports.PRODUCTS
};
