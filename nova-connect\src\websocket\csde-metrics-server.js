/**
 * CSDE Metrics WebSocket Server
 * 
 * This module provides a WebSocket server for streaming real-time CSDE metrics
 * to the dashboard.
 */

const WebSocket = require('ws');
const http = require('http');
const url = require('url');
const { performance } = require('perf_hooks');

class CSEDMetricsServer {
  /**
   * Create a new CSDE Metrics WebSocket Server
   * @param {Object} options - Server options
   * @param {number} options.port - Port to listen on
   * @param {Object} options.connector - CSDE connector instance
   * @param {Object} options.logger - Logger instance
   */
  constructor(options = {}) {
    this.options = {
      port: options.port || process.env.METRICS_PORT || 3002,
      updateInterval: options.updateInterval || 1000,
      connector: options.connector,
      logger: options.logger || console
    };
    
    this.clients = new Set();
    this.server = null;
    this.wss = null;
    this.updateInterval = null;
    this.metrics = {
      timestamp: Date.now(),
      connector: {
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        averageDuration: 0
      },
      csde: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageLatency: 0,
        cacheHits: 0,
        cacheMisses: 0
      },
      performance: {
        requestRate: 0,
        errorRate: 0,
        cacheHitRate: 0,
        performanceFactor: 0
      },
      history: {
        requestRates: [],
        latencies: [],
        cacheHitRates: [],
        errorRates: []
      }
    };
    
    this.lastMetricsUpdate = Date.now();
    this.lastRequestCount = 0;
    this.lastErrorCount = 0;
    this.lastCacheHitCount = 0;
    this.lastCacheMissCount = 0;
  }
  
  /**
   * Start the WebSocket server
   * @returns {Promise<void>}
   */
  async start() {
    this.options.logger.info(`Starting CSDE Metrics WebSocket Server on port ${this.options.port}...`);
    
    // Create HTTP server
    this.server = http.createServer((req, res) => {
      const parsedUrl = url.parse(req.url, true);
      
      if (parsedUrl.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'ok' }));
        return;
      }
      
      res.writeHead(404);
      res.end();
    });
    
    // Create WebSocket server
    this.wss = new WebSocket.Server({ server: this.server });
    
    // Handle WebSocket connections
    this.wss.on('connection', (ws, req) => {
      this.options.logger.debug('Client connected to CSDE Metrics WebSocket Server');
      
      // Add client to set
      this.clients.add(ws);
      
      // Send initial metrics
      ws.send(JSON.stringify(this.metrics));
      
      // Handle client disconnect
      ws.on('close', () => {
        this.options.logger.debug('Client disconnected from CSDE Metrics WebSocket Server');
        this.clients.delete(ws);
      });
      
      // Handle client messages
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          
          if (data.type === 'ping') {
            ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
          }
        } catch (error) {
          this.options.logger.error('Error handling WebSocket message:', error);
        }
      });
    });
    
    // Start HTTP server
    await new Promise((resolve) => {
      this.server.listen(this.options.port, () => {
        this.options.logger.info(`CSDE Metrics WebSocket Server listening on port ${this.options.port}`);
        resolve();
      });
    });
    
    // Start metrics update interval
    this.updateInterval = setInterval(() => {
      this.updateMetrics();
      this.broadcastMetrics();
    }, this.options.updateInterval);
    
    return this;
  }
  
  /**
   * Stop the WebSocket server
   * @returns {Promise<void>}
   */
  async stop() {
    this.options.logger.info('Stopping CSDE Metrics WebSocket Server...');
    
    // Clear update interval
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    
    // Close all WebSocket connections
    for (const client of this.clients) {
      client.close();
    }
    
    this.clients.clear();
    
    // Close WebSocket server
    if (this.wss) {
      await new Promise((resolve) => {
        this.wss.close(() => {
          this.wss = null;
          resolve();
        });
      });
    }
    
    // Close HTTP server
    if (this.server) {
      await new Promise((resolve) => {
        this.server.close(() => {
          this.server = null;
          resolve();
        });
      });
    }
    
    this.options.logger.info('CSDE Metrics WebSocket Server stopped');
    
    return this;
  }
  
  /**
   * Update metrics from connector
   */
  updateMetrics() {
    if (!this.options.connector) {
      return;
    }
    
    try {
      // Get current timestamp
      const now = Date.now();
      const timeDiff = (now - this.lastMetricsUpdate) / 1000; // in seconds
      
      // Get connector metrics
      const connectorMetrics = this.options.connector.metrics || {};
      
      // Get CSDE metrics
      const csdeMetrics = this.options.connector.csdeIntegration?.getMetrics() || {};
      
      // Calculate rates
      const requestCount = csdeMetrics.totalRequests || 0;
      const errorCount = csdeMetrics.failedRequests || 0;
      const cacheHitCount = csdeMetrics.cacheHits || 0;
      const cacheMissCount = csdeMetrics.cacheMisses || 0;
      
      const requestRate = timeDiff > 0 ? (requestCount - this.lastRequestCount) / timeDiff : 0;
      const errorRate = timeDiff > 0 ? (errorCount - this.lastErrorCount) / timeDiff : 0;
      const cacheHitRate = (cacheHitCount + cacheMissCount) > 0 ? 
        cacheHitCount / (cacheHitCount + cacheMissCount) : 0;
      
      // Update history (keep last 60 data points)
      this.metrics.history.requestRates.push(requestRate);
      this.metrics.history.latencies.push(csdeMetrics.averageLatency || 0);
      this.metrics.history.cacheHitRates.push(cacheHitRate);
      this.metrics.history.errorRates.push(errorRate);
      
      if (this.metrics.history.requestRates.length > 60) {
        this.metrics.history.requestRates.shift();
        this.metrics.history.latencies.shift();
        this.metrics.history.cacheHitRates.shift();
        this.metrics.history.errorRates.shift();
      }
      
      // Update metrics
      this.metrics = {
        timestamp: now,
        connector: {
          totalOperations: connectorMetrics.totalOperations || 0,
          successfulOperations: connectorMetrics.successfulOperations || 0,
          failedOperations: connectorMetrics.failedOperations || 0,
          averageDuration: connectorMetrics.averageDuration || 0
        },
        csde: {
          totalRequests: requestCount,
          successfulRequests: csdeMetrics.successfulRequests || 0,
          failedRequests: errorCount,
          averageLatency: csdeMetrics.averageLatency || 0,
          cacheHits: cacheHitCount,
          cacheMisses: cacheMissCount
        },
        performance: {
          requestRate,
          errorRate,
          cacheHitRate,
          performanceFactor: csdeMetrics.performanceFactor || 0
        },
        history: this.metrics.history
      };
      
      // Update last values
      this.lastMetricsUpdate = now;
      this.lastRequestCount = requestCount;
      this.lastErrorCount = errorCount;
      this.lastCacheHitCount = cacheHitCount;
      this.lastCacheMissCount = cacheMissCount;
    } catch (error) {
      this.options.logger.error('Error updating metrics:', error);
    }
  }
  
  /**
   * Broadcast metrics to all connected clients
   */
  broadcastMetrics() {
    if (this.clients.size === 0) {
      return;
    }
    
    const message = JSON.stringify(this.metrics);
    
    for (const client of this.clients) {
      if (client.readyState === WebSocket.OPEN) {
        client.send(message);
      }
    }
  }
  
  /**
   * Get current metrics
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
}

module.exports = CSEDMetricsServer;
