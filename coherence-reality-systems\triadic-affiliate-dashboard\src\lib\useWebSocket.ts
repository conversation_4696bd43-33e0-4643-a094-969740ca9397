import { useEffect, useState } from 'react'
import { WebSocketData } from '@/types/websocket'

export function useWebSocket(userId: string) {
  const [socket, setSocket] = useState<WebSocket | null>(null)
  const [data, setData] = useState<WebSocketData | null>(null)
  const [connected, setConnected] = useState(false)

  useEffect(() => {
    if (!userId) return

    const ws = new WebSocket(`${process.env.WS_URL}/ws?userId=${userId}`)

    ws.onopen = () => {
      setConnected(true)
    }

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data) as WebSocketData
        setData(message)
      } catch (error) {
        console.error('Error parsing WebSocket message:', error)
      }
    }

    ws.onclose = () => {
      setConnected(false)
    }

    ws.onerror = (error) => {
      console.error('WebSocket error:', error)
      setConnected(false)
    }

    setSocket(ws)

    return () => {
      ws.close()
    }
  }, [userId])

  return { socket, data, connected }
}

export function emitConversion(conversion: any) {
  const ws = new WebSocket(`${process.env.WS_URL}/ws`)
  ws.onopen = () => {
    ws.send(JSON.stringify({ type: 'conversion', data: conversion }))
    ws.close()
  }
}

export function emitProduct(product: any) {
  const socket = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3000')
  socket.emit('product', product)
  socket.disconnect()
}

export function emitNetwork(network: any) {
  const socket = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3000')
  socket.emit('network', network)
  socket.disconnect()
}
