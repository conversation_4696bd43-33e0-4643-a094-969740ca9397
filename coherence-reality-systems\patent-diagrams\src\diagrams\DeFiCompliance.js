import React from 'react';
import {
  <PERSON>agram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow
} from '../components/DiagramComponents';

const DeFiCompliance = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>DEFI SMART CONTRACT COMPLIANCE LAYER</ContainerLabel>
      </ContainerBox>
      
      {/* Main flow components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>401</ComponentNumber>
        <ComponentLabel>Blockchain Monitor</ComponentLabel>
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>402</ComponentNumber>
        <ComponentLabel>Smart Contract</ComponentLabel>
        Analysis
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>403</ComponentNumber>
        <ComponentLabel>Compliance Layer</ComponentLabel>
      </ComponentBox>
      
      <Arrow left="625px" top="160px" width="2px" height="100px" />
      
      <ComponentBox left="560px" top="260px" width="130px">
        <ComponentNumber>404</ComponentNumber>
        <ComponentLabel>Regulatory Report</ComponentLabel>
        Generator
      </ComponentBox>
      
      {/* FATF Travel Rule Engine */}
      <ComponentBox left="320px" top="260px" width="130px">
        <ComponentNumber>405</ComponentNumber>
        <ComponentLabel>FATF Travel Rule</ComponentLabel>
        Engine
      </ComponentBox>
      
      {/* Connecting arrows */}
      <VerticalArrow left="145px" top="160px" height="100px" />
      
      <Arrow left="145px" top="260px" width="175px" />
      
      <Arrow left="450px" top="290px" width="110px" />
      
      {/* Curved arrow from Compliance Layer to FATF Travel Rule */}
      <CurvedArrow width="240" height="160" left="560" top="160">
        <path
          d="M 65,0 Q 0,80 -110,100"
          fill="none"
          stroke="#333"
          strokeWidth="2"
        />
        <polygon
          points="-110,100 -100,92 -103,102"
          fill="#333"
        />
      </CurvedArrow>
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>406</ComponentNumber>
        <ComponentLabel>Decentralized Identity</ComponentLabel>
        Verification
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>407</ComponentNumber>
        <ComponentLabel>Compliance Oracle</ComponentLabel>
        Network
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>408</ComponentNumber>
        <ComponentLabel>Smart Contract</ComponentLabel>
        Validation
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>409</ComponentNumber>
        <ComponentLabel>MiCA Compliance</ComponentLabel>
        Module
      </ComponentBox>
    </DiagramFrame>
  );
};

export default DeFiCompliance;
