/**
 * CHAEONIX DIVINE DASHBOARD - MAIN INTERFACE
 * Coherence-Driven Aeonic Intelligence Engine
 * Tri-Market Domination: Stocks | Crypto | Forex
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { motion } from 'framer-motion';
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  BoltIcon,
  EyeIcon,
  StarIcon
} from '@heroicons/react/24/outline';

import CDAIEIntelligenceGrid from '../components/CDAIEIntelligenceGrid';
import TestComponent from '../components/TestComponent';
import SimpleCDAIE from '../components/SimpleCDAIE';
import <PERSON>malCDAIE from '../components/MinimalCDAIE';
import Debug<PERSON><PERSON>IE from '../components/DebugCDAIE';
import TriMarketAllocation from '../components/TriMarketAllocation';
import CoherenceFlowMap from '../components/CoherenceFlowMap';
import PredictiveConsole from '../components/PropheticConsole';
import CHAEONIXEngineStatus from '../components/CHAEONIXEngineStatus';
import FundamentalMetrics from '../components/DivineMetrics';
import CHAEONIXCommandConsole from '../components/DivineCommandConsole';
import MT5ConnectionStatus from '../components/MT5ConnectionStatus';
import NovaSentientStackDemo from '../components/NovaSentientStackDemo';
import CHAEONIXPerformanceTracker from '../components/CHAEONIXPerformanceTracker';
import AggressionControlPanel from '../components/AggressionControlPanel';
import HourlyTargetTracker from '../components/HourlyTargetTracker';
import SimpleProfitTracker from '../components/SimpleProfitTracker';
import CompphyologicalUpgradePanel from '../components/CompphyologicalUpgradePanel';
import N3C_ComphyologicalPanel from '../components/N3C_ComphyologicalPanel';
import SystemIntegrationTestPanel from '../components/SystemIntegrationTestPanel';


import { useCHAEONIXWebSocket } from '../hooks/useCHAEONIXWebSocket';
import { useCHAEONIXAPI } from '../hooks/useCHAEONIXAPI';
import { useUnifiedData, useHourlyRevenue, useMT5Status } from '../hooks/useUnifiedData';
import { TRI_MARKET_DOMAINS, CDAIE_PHASES } from '../utils/chaeonixConstants';

export default function CHAEONIXDashboard() {
  const [activePhase, setActivePhase] = useState('DETECTION');
  const [selectedMarket, setSelectedMarket] = useState('STOCKS');
  const [coherenceLevel, setCoherenceLevel] = useState(0.75);
  const [divineMode, setDivineMode] = useState(true);
  const [mt5Status, setMT5Status] = useState('disconnected');
  const [mt5AccountInfo, setMT5AccountInfo] = useState(null);

  // Dynamic Phase Lighting System
  const [activePhases, setActivePhases] = useState({
    DETECTION: false,
    DECISION: false,
    AMPLIFICATION: false,
    INJECTION: false,
    EXCHANGE: false,
    LOOP: true // Always active
  });

  const [marketCondition, setMarketCondition] = useState('BALANCED');
  const [phaseIntensity, setPhaseIntensity] = useState({});

  // CHAEONIX WebSocket connection for real-time updates (ENABLED for live trading)
  const {
    connectionStatus,
    lastMessage,
    engineStatus: wsEngineStatus,
    marketData
  } = useCHAEONIXWebSocket();

  // Enable WebSocket data for live MT5 integration
  const disableWebSocket = false;

  // Local engine status state (overrides WebSocket when bootstrap completes)
  const [localEngineStatus, setLocalEngineStatus] = useState({});

  // Mock 1,230-point system data with Penta Trinity allocation (David's Option B)
  const mock1230PointData = {
    // BIG 3 CORE (150 points each - Optimized for 1,230 total - Financial Dominance, Pattern God Mode, Emotional Singularity)
    NEFC: { status: 'operational', confidence: 1.50, frequency: 1800, last_analysis: new Date().toISOString(), analysis_count: 52, final_percentage: 150, engine_category: 'big_3', points: 150, role: 'Financial Dominance' },
    NEPI: { status: 'operational', confidence: 1.50, frequency: 1500, last_analysis: new Date().toISOString(), analysis_count: 45, final_percentage: 150, engine_category: 'big_3', points: 150, role: 'Pattern God Mode' },
    NERS: { status: 'operational', confidence: 1.50, frequency: 1400, last_analysis: new Date().toISOString(), analysis_count: 38, final_percentage: 150, engine_category: 'big_3', points: 150, role: 'Emotional Singularity' },

    // TRINITY SUPPORT (110 points each - Optimized allocation - Harmonic Amplifier, Cognitive Overwatch)
    NERE: { status: 'operational', confidence: 1.10, frequency: 1200, last_analysis: new Date().toISOString(), analysis_count: 34, final_percentage: 110, engine_category: 'trinity_support', points: 110, role: 'Harmonic Amplifier' },
    NECE: { status: 'operational', confidence: 1.10, frequency: 1300, last_analysis: new Date().toISOString(), analysis_count: 41, final_percentage: 110, engine_category: 'trinity_support', points: 110, role: 'Cognitive Overwatch' },

    // STANDARD ENGINES (82 points each)
    NEPE: { status: 'operational', confidence: 0.82, frequency: 1200, last_analysis: new Date().toISOString(), analysis_count: 27, final_percentage: 82, engine_category: 'standard', points: 82, role: 'Physical Reality' },
    NECO: { status: 'operational', confidence: 0.82, frequency: 1100, last_analysis: new Date().toISOString(), analysis_count: 29, final_percentage: 82, engine_category: 'standard', points: 82, role: 'Cosmic Timing' },
    NEBE: { status: 'operational', confidence: 0.82, frequency: 1150, last_analysis: new Date().toISOString(), analysis_count: 31, final_percentage: 82, engine_category: 'standard', points: 82, role: 'Biological Response' },
    NEEE: { status: 'operational', confidence: 0.82, frequency: 1250, last_analysis: new Date().toISOString(), analysis_count: 36, final_percentage: 82, engine_category: 'standard', points: 82, role: 'Emotive Interpretation' },

    // CARL'S TRINITY (82 points each)
    NEUE: { status: 'operational', confidence: 0.82, frequency: 1111, last_analysis: new Date().toISOString(), analysis_count: 42, final_percentage: 82, engine_category: 'carl_trinity', points: 82, role: 'Universal Entanglement' },
    NEAE: { status: 'operational', confidence: 0.82, frequency: 888, last_analysis: new Date().toISOString(), analysis_count: 39, final_percentage: 82, engine_category: 'carl_trinity', points: 82, role: 'Aeonic Evolution' },
    NEGR: { status: 'operational', confidence: 0.82, frequency: 777, last_analysis: new Date().toISOString(), analysis_count: 48, final_percentage: 82, engine_category: 'carl_trinity', points: 82, role: 'Governance & Risk' },

    // ENHANCEMENT ENGINES (82 points each)
    NEKH: { status: 'operational', confidence: 0.82, frequency: 1234, last_analysis: new Date().toISOString(), analysis_count: 33, final_percentage: 82, engine_category: 'enhancement', points: 82, role: 'Knowledge Harmonizer' },
    NEQI: { status: 'operational', confidence: 0.82, frequency: 1618, last_analysis: new Date().toISOString(), analysis_count: 44, final_percentage: 82, engine_category: 'enhancement', points: 82, role: 'Quantum Integration' },

    // META-ENGINE (82 points each)
    CASTL: { status: 'operational', confidence: 0.97, frequency: 2000, last_analysis: new Date().toISOString(), analysis_count: 156, final_percentage: 97, engine_category: 'meta', points: 82, role: 'Meta-Enhancement', coherium_balance: 1089.78, accuracy_target: 97.83 }
  };

  // CHAEONIX API integration
  const {
    runDivineSimulation,
    getEngineStatus,
    isLoading
  } = useCHAEONIXAPI();

  // Unified Data Integration - Single Source of Truth
  const unifiedData = useUnifiedData();
  const hourlyRevenue = useHourlyRevenue();
  const mt5Connection = useMT5Status();

  // Update coherence level from real-time data
  useEffect(() => {
    if (lastMessage?.coherence_level) {
      setCoherenceLevel(lastMessage.coherence_level);
    }
  }, [lastMessage]);

  // Dynamic Phase Lighting System
  useEffect(() => {
    const updatePhases = () => {
      // Simulate market condition detection
      const conditions = ['VOLATILE', 'TRENDING', 'RANGING', 'BREAKOUT', 'REVERSAL', 'BALANCED'];
      const currentCondition = conditions[Math.floor(Math.random() * conditions.length)];
      setMarketCondition(currentCondition);

      // Define phase combinations for different market conditions
      const phasePatterns = {
        VOLATILE: {
          DETECTION: { active: true, intensity: 'high', timing: '3-6 min' },
          DECISION: { active: true, intensity: 'ultra', timing: '1-3 min' },
          AMPLIFICATION: { active: true, intensity: 'max', timing: '30sec-1min' },
          INJECTION: { active: true, intensity: 'rapid', timing: '5-15 min' },
          EXCHANGE: { active: true, intensity: 'aggressive', timing: '3-8 min' },
          LOOP: { active: true, intensity: 'continuous', timing: 'continuous' }
        },
        TRENDING: {
          DETECTION: { active: true, intensity: 'steady', timing: '8-12 min' },
          DECISION: { active: true, intensity: 'confident', timing: '3-6 min' },
          AMPLIFICATION: { active: true, intensity: 'strong', timing: '2-4 min' },
          INJECTION: { active: true, intensity: 'sustained', timing: '20-40 min' },
          EXCHANGE: { active: true, intensity: 'patient', timing: '15-25 min' },
          LOOP: { active: true, intensity: 'continuous', timing: 'continuous' }
        },
        RANGING: {
          DETECTION: { active: true, intensity: 'cautious', timing: '10-15 min' },
          DECISION: { active: true, intensity: 'selective', timing: '5-8 min' },
          AMPLIFICATION: { active: false, intensity: 'minimal', timing: '3-5 min' },
          INJECTION: { active: true, intensity: 'conservative', timing: '30-50 min' },
          EXCHANGE: { active: true, intensity: 'quick', timing: '8-15 min' },
          LOOP: { active: true, intensity: 'continuous', timing: 'continuous' }
        },
        BREAKOUT: {
          DETECTION: { active: true, intensity: 'ultra', timing: '2-4 min' },
          DECISION: { active: true, intensity: 'instant', timing: '30sec-2min' },
          AMPLIFICATION: { active: true, intensity: 'explosive', timing: '15-60sec' },
          INJECTION: { active: true, intensity: 'immediate', timing: '3-10 min' },
          EXCHANGE: { active: true, intensity: 'dynamic', timing: '2-6 min' },
          LOOP: { active: true, intensity: 'continuous', timing: 'continuous' }
        },
        REVERSAL: {
          DETECTION: { active: true, intensity: 'precise', timing: '6-10 min' },
          DECISION: { active: true, intensity: 'careful', timing: '4-7 min' },
          AMPLIFICATION: { active: true, intensity: 'targeted', timing: '2-3 min' },
          INJECTION: { active: true, intensity: 'strategic', timing: '15-35 min' },
          EXCHANGE: { active: true, intensity: 'adaptive', timing: '10-20 min' },
          LOOP: { active: true, intensity: 'continuous', timing: 'continuous' }
        },
        BALANCED: {
          DETECTION: { active: true, intensity: 'moderate', timing: '5-15 min' },
          DECISION: { active: true, intensity: 'balanced', timing: '2-10 min' },
          AMPLIFICATION: { active: true, intensity: 'standard', timing: '1-5 min' },
          INJECTION: { active: true, intensity: 'measured', timing: '10-60 min' },
          EXCHANGE: { active: true, intensity: 'steady', timing: '5-30 min' },
          LOOP: { active: true, intensity: 'continuous', timing: 'continuous' }
        }
      };

      const pattern = phasePatterns[currentCondition];
      setActivePhases({
        DETECTION: pattern.DETECTION.active,
        DECISION: pattern.DECISION.active,
        AMPLIFICATION: pattern.AMPLIFICATION.active,
        INJECTION: pattern.INJECTION.active,
        EXCHANGE: pattern.EXCHANGE.active,
        LOOP: pattern.LOOP.active
      });

      setPhaseIntensity(pattern);
    };

    // Update phases every 6-12 seconds to show dynamic behavior
    const interval = setInterval(updatePhases, 6000 + Math.random() * 6000);
    updatePhases(); // Initial update

    return () => clearInterval(interval);
  }, []);

  // Get intensity color based on phase intensity
  const getIntensityColor = (intensity) => {
    const colors = {
      minimal: 'from-gray-400 to-gray-500',
      cautious: 'from-yellow-400 to-yellow-500',
      moderate: 'from-blue-400 to-blue-500',
      balanced: 'from-green-400 to-green-500',
      steady: 'from-teal-400 to-teal-500',
      confident: 'from-indigo-400 to-indigo-500',
      strong: 'from-purple-400 to-purple-500',
      high: 'from-pink-400 to-pink-500',
      aggressive: 'from-red-400 to-red-500',
      ultra: 'from-orange-400 to-orange-500',
      max: 'from-yellow-300 to-red-500',
      explosive: 'from-red-300 to-yellow-400',
      instant: 'from-white to-yellow-300',
      continuous: 'from-emerald-400 to-emerald-500'
    };
    return colors[intensity] || 'from-gray-400 to-gray-500';
  };

  // Get market condition color
  const getMarketConditionColor = () => {
    const colors = {
      VOLATILE: 'text-red-400',
      TRENDING: 'text-green-400',
      RANGING: 'text-yellow-400',
      BREAKOUT: 'text-orange-400',
      REVERSAL: 'text-purple-400',
      BALANCED: 'text-blue-400'
    };
    return colors[marketCondition] || 'text-gray-400';
  };

  const handlePhaseChange = (phase) => {
    setActivePhase(phase);
  };

  const handleMarketSelect = (market) => {
    setSelectedMarket(market);
  };

  const toggleDivineMode = () => {
    setDivineMode(!divineMode);
  };

  const handleMT5ConnectionChange = (status, accountInfo) => {
    setMT5Status(status);
    setMT5AccountInfo(accountInfo);
  };

  const handleSystemRestart = async () => {
    console.log('🔄 SYSTEM RESTART INITIATED');

    try {
      // Step 1: Reset Profit Tracker
      await fetch('/api/analytics/profit-tracker', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'RESET_FOR_LIVE' })
      });
      console.log('✅ Profit tracker reset');

      // Step 2: Restart N³C Consciousness
      await fetch('/api/engines/n3c-comphyological-engine', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'RESTART_CONSCIOUSNESS' })
      });
      console.log('✅ N³C consciousness restarted');

      // Step 3: Force restart Live Bot with MAXIMUM frequency settings
      await fetch('/api/trading/live-bot', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'FORCE_RESTART' })
      });
      console.log('✅ Live bot FORCE RESTARTED with MAXIMUM frequency (30 seconds, 45% threshold)');
      console.log('🚀 SYSTEM RESTART COMPLETE - All components refreshed with HIGH FREQUENCY TRADING');
      console.log('📊 Expected: 60-120 trades/hour (was 2 trades total)');

    } catch (error) {
      console.error('❌ System restart failed:', error);
    }
  };

  const handleForceTradeGenerator = async () => {
    console.log('🚀 FORCE TRADE GENERATOR INITIATED');

    try {
      const response = await fetch('/api/trading/force-trades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'START_FORCE_TRADES' })
      });

      const result = await response.json();

      if (result.success) {
        console.log('✅ FORCE TRADE GENERATOR STARTED - Trades every 3 seconds');
        console.log('🎯 Watch trade count increase rapidly!');
      } else {
        console.error('❌ Failed to start force trade generator:', result.message);
      }

    } catch (error) {
      console.error('❌ Force trade generator failed:', error);
    }
  };

  const handleMetricsReset = async () => {
    console.log('💰 METRICS RESET INITIATED');

    try {
      const response = await fetch('/api/analytics/profit-tracker', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'RESET_METRICS' })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ All CHAEONIX metrics reset to $0');
        console.log(`📊 Total trades: ${result.total_trades}`);
        console.log(`💵 Total profit: $${result.total_profit}`);
        console.log(`⏰ Reset timestamp: ${result.reset_timestamp}`);
      } else {
        throw new Error(`Reset failed with status: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Metrics reset failed:', error);
    }
  };

  return (
    <>
      <Head>
        <title>CHAEONIX - Coherence-Driven Aeonic Intelligence Engine</title>
        <meta name="description" content="Divine Intelligence for Tri-Market Domination" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* CHAEONIX Header */}
        <header className="border-b border-purple-500/20 bg-black/20 backdrop-blur-xl">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Logo & Title */}
              <div className="flex items-center space-x-4">
                <motion.div
                  className="w-10 h-10 bg-divine-gradient rounded-divine flex items-center justify-center"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                >
                  <StarIcon className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                    CHAEONIX
                  </h1>
                  <p className="text-xs text-purple-300">
                    Coherence-Driven Aeonic Intelligence Engine
                  </p>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="flex items-center space-x-6">
                {/* Connection Status - Unified MT5 Integration */}
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    mt5Connection.isConnected ? 'bg-green-400' : 'bg-red-400'
                  }`} />
                  <span className="text-sm text-gray-300">
                    {mt5Connection.isConnected ? `MT5 LIVE (${mt5Connection.mode})` : 'RECONNECTING...'}
                  </span>
                  {mt5Connection.isConnected && (
                    <span className="text-xs text-green-400 font-mono">
                      ${mt5Connection.balance?.toLocaleString() || '0'}
                    </span>
                  )}
                </div>

                {/* Coherence Level */}
                <div className="flex items-center space-x-2">
                  <BoltIcon className="w-4 h-4 text-yellow-400" />
                  <span className="text-sm text-gray-300">
                    Coherence: {(coherenceLevel * 100).toFixed(1)}%
                  </span>
                </div>

                {/* System Restart Button */}
                <button
                  onClick={handleSystemRestart}
                  className="px-3 py-1 rounded-full text-xs font-medium bg-red-600 hover:bg-red-700 text-white transition-all"
                >
                  🔄 SYSTEM RESTART
                </button>

                {/* Force Trade Generator Button */}
                <button
                  onClick={handleForceTradeGenerator}
                  className="px-3 py-1 rounded-full text-xs font-medium bg-orange-600 hover:bg-orange-700 text-white transition-all"
                >
                  🚀 FORCE TRADES
                </button>

                {/* Metrics Reset Button */}
                <button
                  onClick={handleMetricsReset}
                  className="px-3 py-1 rounded-full text-xs font-medium bg-yellow-600 hover:bg-yellow-700 text-white transition-all"
                >
                  💰 RESET METRICS
                </button>

                {/* Magnificent Seven Button */}
                <button
                  onClick={() => window.open('/magnificent-seven', '_blank')}
                  className="px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white transition-all shadow-lg"
                >
                  🏆 MAGNIFICENT SEVEN
                </button>

                {/* Divine Mode Toggle */}
                <button
                  onClick={toggleDivineMode}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-all ${
                    divineMode
                      ? 'bg-purple-600 text-white shadow-divine'
                      : 'bg-gray-600 text-gray-300'
                  }`}
                >
                  {divineMode ? 'DIVINE MODE' : 'STANDARD'}
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Dashboard */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* CDAIE Phase Selector with Dynamic Lighting */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">
                CDAIE Strategy Phases
              </h2>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400">Market Condition:</span>
                <span className={`text-sm font-medium ${getMarketConditionColor()}`}>
                  {marketCondition}
                </span>
              </div>
            </div>
            <div className="grid grid-cols-6 gap-4">
              {Object.entries(CDAIE_PHASES).map(([key, phase]) => {
                const isPhaseActive = activePhases[key];
                const phaseData = phaseIntensity[key];
                const intensityGradient = phaseData ? getIntensityColor(phaseData.intensity) : 'from-gray-400 to-gray-500';

                return (
                  <motion.button
                    key={key}
                    onClick={() => handlePhaseChange(key)}
                    className={`p-4 rounded-lg border transition-all relative overflow-hidden ${
                      activePhase === key
                        ? 'border-purple-400 bg-purple-500/20 shadow-divine'
                        : isPhaseActive
                        ? 'border-green-400 bg-green-500/10 shadow-lg'
                        : 'border-gray-600 bg-gray-800/50 hover:border-gray-500'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    animate={isPhaseActive ? {
                      boxShadow: [
                        '0 0 0 rgba(34, 197, 94, 0)',
                        '0 0 20px rgba(34, 197, 94, 0.3)',
                        '0 0 0 rgba(34, 197, 94, 0)'
                      ]
                    } : {}}
                    transition={{ duration: 2, repeat: isPhaseActive ? Infinity : 0 }}
                  >
                    {/* Dynamic Background Gradient */}
                    {isPhaseActive && (
                      <div
                        className={`absolute inset-0 bg-gradient-to-r ${intensityGradient} opacity-10 animate-pulse`}
                      />
                    )}

                    <div className="text-center relative z-10">
                      <div className="flex items-center justify-center mb-2">
                        <div
                          className={`w-3 h-3 rounded-full transition-all ${
                            isPhaseActive
                              ? 'animate-pulse shadow-lg'
                              : ''
                          }`}
                          style={{
                            backgroundColor: isPhaseActive ? '#22c55e' : phase.color,
                            boxShadow: isPhaseActive ? '0 0 10px #22c55e' : 'none'
                          }}
                        />
                        {isPhaseActive && (
                          <div className="ml-1 text-xs text-green-400 font-bold">●</div>
                        )}
                      </div>
                      <h3 className={`text-sm font-medium ${
                        isPhaseActive ? 'text-green-300' : 'text-white'
                      }`}>
                        {phase.name}
                      </h3>
                      <p className="text-xs text-gray-400 mt-1">
                        {phaseData ? phaseData.timing : phase.duration}
                      </p>
                      {phaseData && (
                        <p className="text-xs mt-1 capitalize" style={{
                          color: isPhaseActive ? '#22c55e' : '#6b7280'
                        }}>
                          {phaseData.intensity}
                        </p>
                      )}
                    </div>
                  </motion.button>
                );
              })}
            </div>
          </div>

          {/* Tri-Market Selector */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">
              Tri-Market Domains
            </h2>
            <div className="grid grid-cols-3 gap-6">
              {Object.entries(TRI_MARKET_DOMAINS).map(([key, market]) => (
                <motion.button
                  key={key}
                  onClick={() => handleMarketSelect(key)}
                  className={`p-6 rounded-lg border transition-all ${
                    selectedMarket === key
                      ? 'border-purple-400 bg-purple-500/20 shadow-divine'
                      : 'border-gray-600 bg-gray-800/50 hover:border-gray-500'
                  }`}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="text-center">
                    <div className="flex justify-center mb-3">
                      {key === 'STOCKS' && <ChartBarIcon className="w-8 h-8" style={{ color: market.color }} />}
                      {key === 'CRYPTO' && <CurrencyDollarIcon className="w-8 h-8" style={{ color: market.color }} />}
                      {key === 'FOREX' && <GlobeAltIcon className="w-8 h-8" style={{ color: market.color }} />}
                    </div>
                    <h3 className="text-lg font-semibold text-white">
                      {market.name}
                    </h3>
                    <p className="text-sm text-gray-400 mt-1">
                      Allocation: {(market.allocation_range[0] * 100).toFixed(0)}%-{(market.allocation_range[1] * 100).toFixed(0)}%
                    </p>
                    <div className="flex justify-center space-x-1 mt-2">
                      {market.examples.slice(0, 3).map((symbol, i) => (
                        <span key={i} className="text-xs bg-gray-700 px-2 py-1 rounded">
                          {symbol}
                        </span>
                      ))}
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>

          {/* Main Dashboard Grid */}
          <div className="grid grid-cols-12 gap-6">
            {/* CDAIE Intelligence Grid - Full Width */}
            <div className="col-span-12">
              <CDAIEIntelligenceGrid
                activePhase={activePhase}
                selectedMarket={selectedMarket}
                coherenceLevel={coherenceLevel}
                divineMode={divineMode}
              />
            </div>

            {/* CHAEONIX Engine Status - Directly Under CDAIE Intelligence Grid */}
            <div className="col-span-12">
              <CHAEONIXEngineStatus
                engineStatus={
                  disableWebSocket
                    ? (Object.keys(localEngineStatus).length > 0 ? localEngineStatus : mock1230PointData)
                    : (Object.keys(localEngineStatus).length > 0 ? localEngineStatus : wsEngineStatus)
                }
                connectionStatus={disableWebSocket ? "1,230-Point System Active" : connectionStatus}
                onEngineUpdate={(newEngineData) => {
                  console.log('🔄 Dashboard received engine update:', newEngineData);
                  setLocalEngineStatus(newEngineData);
                }}
              />
            </div>

            {/* THREE-COLUMN OPTIMIZED LAYOUT */}

            {/* COLUMN 1: Predictive Console + Fundamental Metrics + Coherence Flow Map */}
            <div className="col-span-12 lg:col-span-4 space-y-6">
              {/* Predictive Console */}
              <PredictiveConsole
                onEventSeed={(event) => {
                  console.log('Seeding predictive event:', event);
                  // Handle predictive event seeding
                }}
              />

              {/* Fundamental Metrics */}
              <FundamentalMetrics
                engineStatus={
                  disableWebSocket
                    ? (Object.keys(localEngineStatus).length > 0 ? localEngineStatus : mock1230PointData)
                    : (Object.keys(localEngineStatus).length > 0 ? localEngineStatus : wsEngineStatus)
                }
                marketData={marketData}
              />

              {/* Coherence Flow Map */}
              <CoherenceFlowMap
                coherenceLevel={coherenceLevel}
                activePhase={activePhase}
              />
            </div>

            {/* COLUMN 2: Tri-Market Domains (TAS™) */}
            <div className="col-span-12 lg:col-span-4">
              <TriMarketAllocation
                selectedMarket={selectedMarket}
                onMarketSelect={handleMarketSelect}
              />
            </div>

            {/* COLUMN 3: Hourly Revenue Target */}
            <div className="col-span-12 lg:col-span-4">
              <HourlyTargetTracker />
            </div>





            {/* CHAEONIX Performance Tracker - Below Engine Status */}
            <div className="col-span-12">
              <CHAEONIXPerformanceTracker />
            </div>

            {/* N³C + Comphyological Foundation Panel */}
            <div className="col-span-12">
              <N3C_ComphyologicalPanel />
            </div>

            {/* System Integration Testing Panel */}
            <div className="col-span-12">
              <SystemIntegrationTestPanel />
            </div>

            {/* NovaSentient Stack Demo - The World's First Conscious AI Defense Grid™ */}
            <div className="col-span-12">
              <NovaSentientStackDemo />
            </div>

            {/* Compphyological Upgrade Panel */}
            <div className="col-span-12">
              <CompphyologicalUpgradePanel />
            </div>

            {/* Aggression Control Panel */}
            <div className="col-span-6">
              <AggressionControlPanel />
            </div>

            {/* CHAEONIX Command Console */}
            <div className="col-span-6">
              <CHAEONIXCommandConsole
                onCommandExecute={(command) => {
                  console.log('CHAEONIX command executed:', command);
                  // Handle command execution results
                }}
              />
            </div>

            {/* Simple Profit Tracker - Comprehensive profit analytics at bottom */}
            <div className="col-span-12">
              <SimpleProfitTracker />
            </div>

            {/* MT5 Connection Status - At very bottom */}
            <div className="col-span-12">
              <MT5ConnectionStatus
                onConnectionChange={handleMT5ConnectionChange}
              />
            </div>
          </div>
        </main>

        {/* Footer */}
        <footer className="border-t border-purple-500/20 bg-black/20 backdrop-blur-xl mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-400">
                CHAEONIX v1.0.0 - Coherence-Driven Aeonic Intelligence Engine
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-400">
                <span>φ = 1.618033988749</span>
                <span>•</span>
                <span>15 Engines Active</span>
                <span>•</span>
                <span>Divine Mode: {divineMode ? 'ON' : 'OFF'}</span>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
