/**
 * NovaFuse Universal API Connector - API Error
 * 
 * This module defines API-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for API failures
 * @class ApiError
 * @extends UAConnectorError
 */
class ApiError extends UAConnectorError {
  /**
   * Create a new ApiError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   * @param {number} options.statusCode - HTTP status code
   * @param {Object} options.response - API response data
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'API_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
    
    this.statusCode = options.statusCode;
    this.response = options.response;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred while communicating with the external service. Please try again later.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    
    if (this.statusCode) {
      json.statusCode = this.statusCode;
    }
    
    if (this.response) {
      json.response = this.response;
    }
    
    return json;
  }
}

/**
 * Error class for rate limit exceeded errors
 * @class RateLimitExceededError
 * @extends ApiError
 */
class RateLimitExceededError extends ApiError {
  /**
   * Create a new RateLimitExceededError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {number} options.retryAfter - Seconds to wait before retrying
   */
  constructor(message = 'Rate limit exceeded', options = {}) {
    super(message, {
      code: options.code || 'API_RATE_LIMIT_EXCEEDED',
      severity: options.severity || 'warning',
      context: options.context || {},
      cause: options.cause,
      statusCode: options.statusCode || 429,
      response: options.response
    });
    
    this.retryAfter = options.retryAfter;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    if (this.retryAfter) {
      return `Rate limit exceeded. Please try again after ${this.retryAfter} seconds.`;
    }
    return 'Rate limit exceeded. Please try again later.';
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    
    if (this.retryAfter) {
      json.retryAfter = this.retryAfter;
    }
    
    return json;
  }
}

/**
 * Error class for resource not found errors
 * @class ResourceNotFoundError
 * @extends ApiError
 */
class ResourceNotFoundError extends ApiError {
  /**
   * Create a new ResourceNotFoundError
   * 
   * @param {string} resourceType - The type of resource
   * @param {string} resourceId - The ID of the resource
   * @param {Object} options - Error options
   */
  constructor(resourceType, resourceId, options = {}) {
    const message = `${resourceType} not found with ID: ${resourceId}`;
    
    super(message, {
      code: options.code || 'API_RESOURCE_NOT_FOUND',
      severity: options.severity || 'error',
      context: { ...options.context, resourceType, resourceId },
      cause: options.cause,
      statusCode: options.statusCode || 404,
      response: options.response
    });
    
    this.resourceType = resourceType;
    this.resourceId = resourceId;
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return `The requested ${this.resourceType.toLowerCase()} could not be found.`;
  }

  /**
   * Convert the error to a JSON object
   * 
   * @param {boolean} includeStack - Whether to include the stack trace
   * @returns {Object} - JSON representation of the error
   */
  toJSON(includeStack = false) {
    const json = super.toJSON(includeStack);
    json.resourceType = this.resourceType;
    json.resourceId = this.resourceId;
    return json;
  }
}

/**
 * Error class for bad request errors
 * @class BadRequestError
 * @extends ApiError
 */
class BadRequestError extends ApiError {
  /**
   * Create a new BadRequestError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Bad request', options = {}) {
    super(message, {
      code: options.code || 'API_BAD_REQUEST',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause,
      statusCode: options.statusCode || 400,
      response: options.response
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'The request was invalid. Please check your input and try again.';
  }
}

/**
 * Error class for server errors
 * @class ServerError
 * @extends ApiError
 */
class ServerError extends ApiError {
  /**
   * Create a new ServerError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Server error', options = {}) {
    super(message, {
      code: options.code || 'API_SERVER_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause,
      statusCode: options.statusCode || 500,
      response: options.response
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'An error occurred on the server. Please try again later or contact support if the issue persists.';
  }
}

module.exports = {
  ApiError,
  RateLimitExceededError,
  ResourceNotFoundError,
  BadRequestError,
  ServerError
};
