const BN = require('bn.js');
const crypto = require('crypto');

/**
 * Crown Consensus Engine for KetherNet
 * Implements a Proof-of-Stake based consensus mechanism with slashing conditions
 */
class CrownConsensus {
  /**
   * Initialize a new Crown Consensus instance
   * @param {Object} options - Configuration options
   * @param {BN} options.minStake - Minimum stake required to become a validator (in wei)
   * @param {number} options.epochLength - Number of blocks per epoch
   * @param {number} options.blockTime - Target block time in seconds
   */
  constructor({
    minStake = new BN('1000000000000000000'), // 1 COH
    epochLength = 100,
    blockTime = 2
  } = {}) {
    this.validators = new Map();
    this.minStake = new BN(minStake);
    this.epochLength = epochLength;
    this.blockTime = blockTime;
    this.epoch = 0;
    this.lastEpochBlock = 0;
    this.slashingConditions = [];
    
    // Initialize default slashing conditions
    this._initializeDefaultSlashingConditions();
  }

  /**
   * Initialize with genesis validators
   * @param {Array} genesisValidators - Array of initial validator configurations
   * @param {string} genesisValidators[].address - Validator address
   * @param {string|BN} genesisValidators[].stake - Initial stake amount
   */
  initialize(genesisValidators = []) {
    if (this.validators.size > 0) {
      throw new Error('Consensus already initialized');
    }

    genesisValidators.forEach(({ address, stake }) => {
      const stakeBN = new BN(stake.toString());
      if (stakeBN.lt(this.minStake)) {
        throw new Error(`Validator ${address} stake (${stakeBN.toString()}) is below minimum ${this.minStake.toString()}`);
      }

      this.validators.set(address, {
        stake: stakeBN,
        active: true,
        lastProposedBlock: 0,
        slashingCount: 0,
        rewards: new BN(0)
      });
    });

    if (this.validators.size === 0) {
      throw new Error('Must provide at least one genesis validator');
    }

    console.log(`👑 Crown Consensus initialized with ${this.validators.size} validators`);
    return this;
  }

  /**
   * Get the current validator set
   * @returns {Array} List of active validators with their details
   */
  getValidators() {
    return Array.from(this.validators.entries())
      .filter(([_, v]) => v.active)
      .map(([address, data]) => ({
        address,
        stake: data.stake.toString(),
        rewards: data.rewards.toString(),
        slashingCount: data.slashingCount,
        active: data.active,
        lastProposedBlock: data.lastProposedBlock
      }));
  }

  /**
   * Select the next block proposer based on weighted random selection
   * @param {number} blockNumber - Current block number
   * @returns {string} Selected validator address
   */
  selectProposer(blockNumber) {
    const activeValidators = Array.from(this.validators.entries())
      .filter(([_, v]) => v.active)
      .map(([address, data]) => ({
        address,
        stake: data.stake,
        weight: 0
      }));

    if (activeValidators.length === 0) {
      throw new Error('No active validators available');
    }

    // Simple round-robin for now - will be replaced with weighted random selection
    const index = blockNumber % activeValidators.length;
    return activeValidators[index].address;
  }

  /**
   * Validate a block proposal
   * @param {Object} block - Proposed block
   * @param {string} proposer - Address of the block proposer
   * @returns {boolean} True if block is valid
   */
  validateBlockProposal(block, proposer) {
    // 1. Verify proposer is a valid active validator
    const validator = this.validators.get(proposer);
    if (!validator || !validator.active) {
      throw new Error('Invalid or inactive validator');
    }

    // 2. Check if it's validator's turn to propose
    const expectedProposer = this.selectProposer(block.number);
    if (expectedProposer !== proposer) {
      throw new Error('Invalid block proposer');
    }

    // 3. Validate block structure
    if (!this._validateBlockStructure(block)) {
      throw new Error('Invalid block structure');
    }

    // 4. Validate transactions (to be implemented)
    if (!this._validateTransactions(block.transactions)) {
      throw new Error('Invalid transactions');
    }

    return true;
  }

  /**
   * Process epoch transition
   * @param {number} blockNumber - Current block number
   * @returns {Object} Epoch transition details
   */
  processEpoch(blockNumber) {
    if (blockNumber - this.lastEpochBlock < this.epochLength) {
      return { newEpoch: false };
    }

    this.epoch++;
    this.lastEpochBlock = blockNumber;

    // Distribute rewards and process slashing
    const epochDetails = this._distributeRewards();
    this._processSlashing();

    return {
      newEpoch: true,
      epoch: this.epoch,
      ...epochDetails
    };
  }

  // ========== PRIVATE METHODS ========== //

  /**
   * Initialize default slashing conditions
   * @private
   */
  _initializeDefaultSlashingConditions() {
    this.slashingConditions = [
      {
        name: 'double-proposal',
        description: 'Validator proposed multiple blocks at the same height',
        slashAmount: new BN('1000000000000000000') // 1 COH
      },
      {
        name: 'invalid-block',
        description: 'Validator proposed an invalid block',
        slashAmount: new BN('500000000000000000') // 0.5 COH
      }
    ];
  }

  /**
   * Validate block structure
   * @private
   */
  _validateBlockStructure(block) {
    // Basic block structure validation
    const requiredFields = ['number', 'timestamp', 'transactions', 'parentHash'];
    return requiredFields.every(field => field in block);
  }

  /**
   * Validate transactions in a block
   * @private
   */
  _validateTransactions(transactions) {
    // Basic transaction validation
    // TODO: Implement comprehensive transaction validation
    return Array.isArray(transactions);
  }

  /**
   * Distribute rewards to validators
   * @private
   */
  _distributeRewards() {
    // TODO: Implement reward distribution logic
    // This is a placeholder - actual implementation will calculate
    // and distribute rewards based on stake and participation
    return {
      totalDistributed: '0',
      validatorsRewarded: 0
    };
  }

  /**
   * Process slashing conditions
   * @private
   */
  _processSlashing() {
    // TODO: Implement slashing logic
    // This will check for slashable offenses and apply penalties
    return {
      validatorsSlashed: 0,
      totalSlashed: '0'
    };
  }
}

module.exports = CrownConsensus;
