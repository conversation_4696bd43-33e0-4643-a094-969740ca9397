openapi: 3.0.0
info:
  title: NovaFuse Privacy Management API
  description: |
    A comprehensive API for managing privacy compliance across multiple systems and jurisdictions.
    Provides functionality for handling data subject requests, consent management, privacy notices,
    data breach management, and regulatory compliance.
  version: 1.0.0
  contact:
    name: NovaFuse Support
    email: <EMAIL>
    url: https://support.novafuse.com
servers:
  - url: https://api.novafuse.com/v1
    description: Production server
  - url: https://staging-api.novafuse.com/v1
    description: Staging server
  - url: http://localhost:3000
    description: Local development server

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Error:
      type: object
      properties:
        error:
          type: string
          description: Error type
        message:
          type: string
          description: Error message
      required:
        - error
        - message
    DataProcessingActivity:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier
        name:
          type: string
          description: Name of the processing activity
        description:
          type: string
          description: Description of the processing activity
        purpose:
          type: string
          description: Purpose of the processing activity
        dataCategories:
          type: array
          items:
            type: string
          description: Categories of data being processed
        dataSubjects:
          type: array
          items:
            type: string
          description: Categories of data subjects
        legalBasis:
          type: string
          enum: [consent, contract, legal-obligation, vital-interests, public-interest, legitimate-interests]
          description: Legal basis for processing
        retentionPeriod:
          type: string
          description: Data retention period
        status:
          type: string
          enum: [active, inactive, archived]
          description: Status of the processing activity
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
      required:
        - name
        - description
        - purpose
        - dataCategories
        - dataSubjects
        - legalBasis
        - retentionPeriod
        - status

    DataSubjectRequest:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier
        requestType:
          type: string
          enum: [access, rectification, erasure, restriction, portability, objection, automated-decision, withdraw-consent, other]
          description: Type of data subject request
        dataSubjectName:
          type: string
          description: Name of the data subject
        dataSubjectEmail:
          type: string
          format: email
          description: Email of the data subject
        requestDetails:
          type: string
          description: Details of the request
        status:
          type: string
          enum: [pending, in-progress, completed, rejected, withdrawn]
          description: Status of the request
        dueDate:
          type: string
          format: date-time
          description: Due date for request completion
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
      required:
        - requestType
        - dataSubjectName
        - dataSubjectEmail
        - requestDetails
        - status

paths:
  /auth/login:
    post:
      summary: Authenticate user
      description: Authenticates a user and returns a JWT token
      tags:
        - Authentication
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
              required:
                - username
                - password
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                type: object
                properties:
                  token:
                    type: string
                    description: JWT token
                  user:
                    type: object
                    properties:
                      id:
                        type: string
                      username:
                        type: string
                      role:
                        type: string
        '401':
          description: Authentication failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /processing-activities:
    get:
      summary: Get data processing activities
      description: Returns a list of data processing activities
      tags:
        - Data Processing Activities
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum: [active, inactive, archived]
        - name: search
          in: query
          description: Search term
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/DataProcessingActivity'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      page:
                        type: integer
                      limit:
                        type: integer
                      pages:
                        type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create data processing activity
      description: Creates a new data processing activity
      tags:
        - Data Processing Activities
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataProcessingActivity'
      responses:
        '201':
          description: Successfully created
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/DataProcessingActivity'
                  message:
                    type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /subject-requests:
    get:
      summary: Get data subject requests
      description: Returns a list of data subject requests
      tags:
        - Data Subject Requests
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
            enum: [pending, in-progress, completed, rejected, withdrawn]
        - name: requestType
          in: query
          description: Filter by request type
          schema:
            type: string
            enum: [access, rectification, erasure, restriction, portability, objection, automated-decision, withdraw-consent, other]
        - name: search
          in: query
          description: Search term
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/DataSubjectRequest'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      page:
                        type: integer
                      limit:
                        type: integer
                      pages:
                        type: integer
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    post:
      summary: Create data subject request
      description: Creates a new data subject request
      tags:
        - Data Subject Requests
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DataSubjectRequest'
      responses:
        '201':
          description: Successfully created
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/DataSubjectRequest'
                  message:
                    type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /integrations:
    get:
      summary: Get available integrations
      description: Returns a list of available integrations
      tags:
        - Integrations
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                        name:
                          type: string
                        description:
                          type: string
                        status:
                          type: string
                        version:
                          type: string
                        capabilities:
                          type: array
                          items:
                            type: string
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /integrations/{id}/{action}:
    post:
      summary: Execute integration action
      description: Executes an action on a specific integration
      tags:
        - Integrations
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Integration ID
          schema:
            type: string
        - name: action
          in: path
          required: true
          description: Action to execute
          schema:
            type: string
            enum: [data-export, data-deletion, data-update, notifications]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                  message:
                    type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /dashboard/metrics:
    get:
      summary: Get dashboard metrics
      description: Returns metrics for the dashboard
      tags:
        - Dashboard
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      dsrMetrics:
                        type: object
                      consentMetrics:
                        type: object
                      dataBreachMetrics:
                        type: object
                      complianceMetrics:
                        type: object
                      complianceGaps:
                        type: array
                        items:
                          type: object
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /reports/{reportType}:
    get:
      summary: Generate report
      description: Generates a report of the specified type
      tags:
        - Reports
      security:
        - BearerAuth: []
      parameters:
        - name: reportType
          in: path
          required: true
          description: Report type
          schema:
            type: string
            enum: [dsr-summary, consent-management, data-breach, processing-activities, compliance-status]
        - name: period
          in: query
          required: true
          description: Time period
          schema:
            type: string
            enum: [last-7-days, last-30-days, last-90-days, last-12-months, year-to-date, custom]
        - name: startDate
          in: query
          description: Start date (required if period is custom)
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: End date (required if period is custom)
          schema:
            type: string
            format: date
        - name: groupBy
          in: query
          description: Group by field
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
