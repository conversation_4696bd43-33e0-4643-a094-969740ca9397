/**
 * Feature Flag Controller
 * 
 * This controller handles API requests related to feature flags and subscription tiers.
 */

const FeatureFlagService = require('../services/FeatureFlagService');
const { ValidationError } = require('../utils/errors');

class FeatureFlagController {
  constructor() {
    this.featureFlagService = new FeatureFlagService();
  }

  /**
   * Get all feature flags
   */
  async getAllFeatureFlags(req, res, next) {
    try {
      const featureFlags = await this.featureFlagService.getAllFeatureFlags();
      res.json(featureFlags);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get feature flag by ID
   */
  async getFeatureFlagById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Feature flag ID is required');
      }
      
      const featureFlag = await this.featureFlagService.getFeatureFlagById(id);
      res.json(featureFlag);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update feature flag
   */
  async updateFeatureFlag(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Feature flag ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Feature flag data is required');
      }
      
      const featureFlag = await this.featureFlagService.updateFeatureFlag(id, data);
      res.json(featureFlag);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all subscription tiers
   */
  async getAllSubscriptionTiers(req, res, next) {
    try {
      const subscriptionTiers = await this.featureFlagService.getAllSubscriptionTiers();
      res.json(subscriptionTiers);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get subscription tier by ID
   */
  async getSubscriptionTierById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Subscription tier ID is required');
      }
      
      const subscriptionTier = await this.featureFlagService.getSubscriptionTierById(id);
      res.json(subscriptionTier);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user entitlement
   */
  async getUserEntitlement(req, res, next) {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      const userEntitlement = await this.featureFlagService.getUserEntitlement(userId);
      res.json(userEntitlement);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user entitlement
   */
  async updateUserEntitlement(req, res, next) {
    try {
      const { userId } = req.params;
      const data = req.body;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!data) {
        throw new ValidationError('User entitlement data is required');
      }
      
      const userEntitlement = await this.featureFlagService.updateUserEntitlement(userId, data);
      res.json(userEntitlement);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if user has access to feature
   */
  async hasFeatureAccess(req, res, next) {
    try {
      const { userId, featureId } = req.params;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!featureId) {
        throw new ValidationError('Feature ID is required');
      }
      
      const hasAccess = await this.featureFlagService.hasFeatureAccess(userId, featureId);
      res.json({ hasAccess });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's feature limit
   */
  async getFeatureLimit(req, res, next) {
    try {
      const { userId, featureId, limitKey } = req.params;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!featureId) {
        throw new ValidationError('Feature ID is required');
      }
      
      if (!limitKey) {
        throw new ValidationError('Limit key is required');
      }
      
      const limit = await this.featureFlagService.getFeatureLimit(userId, featureId, limitKey);
      res.json({ limit });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(req, res, next) {
    try {
      const { userId, featureId } = req.params;
      const { quantity } = req.body;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!featureId) {
        throw new ValidationError('Feature ID is required');
      }
      
      const usage = await this.featureFlagService.trackFeatureUsage(userId, featureId, quantity);
      res.json(usage);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get feature usage for user
   */
  async getFeatureUsageForUser(req, res, next) {
    try {
      const { userId } = req.params;
      const { featureId, startDate, endDate } = req.query;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      const usage = await this.featureFlagService.getFeatureUsageForUser(userId, featureId, startDate, endDate);
      res.json(usage);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if user has reached feature limit
   */
  async hasReachedFeatureLimit(req, res, next) {
    try {
      const { userId, featureId, limitKey } = req.params;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      if (!featureId) {
        throw new ValidationError('Feature ID is required');
      }
      
      if (!limitKey) {
        throw new ValidationError('Limit key is required');
      }
      
      const hasReached = await this.featureFlagService.hasReachedFeatureLimit(userId, featureId, limitKey);
      res.json({ hasReached });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's available features
   */
  async getUserAvailableFeatures(req, res, next) {
    try {
      const { userId } = req.params;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      const features = await this.featureFlagService.getUserAvailableFeatures(userId);
      res.json(features);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's subscription details
   */
  async getUserSubscriptionDetails(req, res, next) {
    try {
      const userId = req.params.userId || req.user.id;
      
      if (!userId) {
        throw new ValidationError('User ID is required');
      }
      
      const details = await this.featureFlagService.getUserSubscriptionDetails(userId);
      res.json(details);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user's subscription details
   */
  async getCurrentUserSubscriptionDetails(req, res, next) {
    try {
      if (!req.user || !req.user.id) {
        throw new ValidationError('User is not authenticated');
      }
      
      const details = await this.featureFlagService.getUserSubscriptionDetails(req.user.id);
      res.json(details);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new FeatureFlagController();
