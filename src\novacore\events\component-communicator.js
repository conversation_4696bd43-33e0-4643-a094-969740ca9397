/**
 * component-communicator.js
 * 
 * This file implements the cross-component communication system for NovaCore.
 * The component communicator enables different components to communicate with each other
 * through a publish-subscribe pattern, with support for message routing and filtering.
 */

const EventEmitter = require('events');
const { Event, EventProcessor, EventPriority, EventStatus } = require('./event-processor');
const crypto = require('crypto');

/**
 * Message type enum
 * @enum {string}
 */
const MessageType = {
  REQUEST: 'REQUEST',
  RESPONSE: 'RESPONSE',
  NOTIFICATION: 'NOTIFICATION',
  COMMAND: 'COMMAND',
  EVENT: 'EVENT'
};

/**
 * Component status enum
 * @enum {string}
 */
const ComponentStatus = {
  DISCONNECTED: 'DISCONNECTED',
  CONNECTING: 'CONNECTING',
  CONNECTED: 'CONNECTED',
  ERROR: 'ERROR'
};

/**
 * Message class representing a communication message between components
 */
class Message {
  /**
   * Create a new message
   * @param {string} type - The message type
   * @param {string} topic - The message topic
   * @param {Object} payload - The message payload
   * @param {Object} options - Message options
   */
  constructor(type, topic, payload = {}, options = {}) {
    this.id = options.id || crypto.randomUUID();
    this.type = type;
    this.topic = topic;
    this.payload = payload;
    this.timestamp = options.timestamp || new Date().toISOString();
    this.sender = options.sender || 'unknown';
    this.recipient = options.recipient || null; // null means broadcast
    this.priority = options.priority !== undefined ? options.priority : EventPriority.NORMAL;
    this.metadata = options.metadata || {};
    this.correlationId = options.correlationId || null;
    this.replyTo = options.replyTo || null;
    this.ttl = options.ttl || 60000; // Default: 1 minute
    this.expiresAt = options.expiresAt || new Date(Date.now() + (options.ttl || 60000)).toISOString();
  }

  /**
   * Check if the message has expired
   * @returns {boolean} - Whether the message has expired
   */
  isExpired() {
    return new Date() > new Date(this.expiresAt);
  }

  /**
   * Create a response message
   * @param {Object} payload - The response payload
   * @param {Object} options - Response options
   * @returns {Message} - A new response message
   */
  createResponse(payload = {}, options = {}) {
    return new Message(
      MessageType.RESPONSE,
      `response.${this.topic}`,
      payload,
      {
        sender: options.sender || 'response',
        recipient: this.sender,
        priority: this.priority,
        metadata: {
          isResponse: true,
          originalMessageType: this.type,
          originalMessageTopic: this.topic,
          ...options.metadata
        },
        correlationId: this.correlationId || this.id,
        replyTo: null,
        ttl: options.ttl || this.ttl,
        ...options
      }
    );
  }

  /**
   * Convert the message to an event
   * @returns {Event} - An event representing the message
   */
  toEvent() {
    return new Event(`message.${this.type.toLowerCase()}.${this.topic}`, {
      message: this
    }, {
      priority: this.priority,
      source: this.sender,
      metadata: {
        isMessage: true,
        messageType: this.type,
        messageTopic: this.topic,
        ...this.metadata
      },
      correlationId: this.correlationId
    });
  }

  /**
   * Convert the message to a JSON-serializable object
   * @returns {Object} - A JSON-serializable representation of the message
   */
  toJSON() {
    return {
      id: this.id,
      type: this.type,
      topic: this.topic,
      payload: this.payload,
      timestamp: this.timestamp,
      sender: this.sender,
      recipient: this.recipient,
      priority: this.priority,
      metadata: this.metadata,
      correlationId: this.correlationId,
      replyTo: this.replyTo,
      ttl: this.ttl,
      expiresAt: this.expiresAt
    };
  }

  /**
   * Create a message from a JSON object
   * @param {Object} json - The JSON representation of a message
   * @returns {Message} - A new message created from the JSON
   */
  static fromJSON(json) {
    return new Message(json.type, json.topic, json.payload, {
      id: json.id,
      timestamp: json.timestamp,
      sender: json.sender,
      recipient: json.recipient,
      priority: json.priority,
      metadata: json.metadata,
      correlationId: json.correlationId,
      replyTo: json.replyTo,
      ttl: json.ttl,
      expiresAt: json.expiresAt
    });
  }
}

/**
 * ComponentCommunicator class for cross-component communication
 * @extends EventEmitter
 */
class ComponentCommunicator extends EventEmitter {
  /**
   * Create a new component communicator
   * @param {Object} options - Component communicator options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      componentId: options.componentId || `component-${crypto.randomUUID().substring(0, 8)}`,
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      eventProcessor: options.eventProcessor || new EventProcessor({
        enableLogging: options.enableLogging,
        maxConcurrentEvents: options.maxConcurrentEvents || 20
      }),
      ...options
    };
    
    this.subscriptions = new Map();
    this.pendingRequests = new Map();
    this.components = new Map();
    this.status = ComponentStatus.DISCONNECTED;
    
    // Register event handlers
    this.options.eventProcessor.on('eventCompleted', (event) => {
      if (event.data && event.data.message) {
        this.emit('messageSent', { message: event.data.message });
      }
    });
    
    this.options.eventProcessor.on('eventFailed', ({ event, error }) => {
      if (event.data && event.data.message) {
        this.emit('messageError', { message: event.data.message, error });
      }
    });
    
    this.log('ComponentCommunicator initialized with options:', this.options);
  }
  
  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[ComponentCommunicator ${this.options.componentId} ${new Date().toISOString()}]`, ...args);
    }
  }
  
  /**
   * Connect to the communication system
   * @returns {Promise<void>} - A promise that resolves when connected
   */
  async connect() {
    this.status = ComponentStatus.CONNECTING;
    this.emit('connecting');
    
    try {
      // Register this component
      this.components.set(this.options.componentId, {
        id: this.options.componentId,
        status: ComponentStatus.CONNECTED,
        connectedAt: new Date().toISOString()
      });
      
      this.status = ComponentStatus.CONNECTED;
      this.emit('connected');
      
      // Publish a connection notification
      await this.publish(
        MessageType.NOTIFICATION,
        'system.component.connected',
        {
          componentId: this.options.componentId,
          timestamp: new Date().toISOString()
        }
      );
      
      this.log('Connected to communication system');
    } catch (error) {
      this.status = ComponentStatus.ERROR;
      this.emit('error', error);
      this.log('Error connecting to communication system:', error);
      throw error;
    }
  }
  
  /**
   * Disconnect from the communication system
   * @returns {Promise<void>} - A promise that resolves when disconnected
   */
  async disconnect() {
    try {
      // Publish a disconnection notification
      await this.publish(
        MessageType.NOTIFICATION,
        'system.component.disconnected',
        {
          componentId: this.options.componentId,
          timestamp: new Date().toISOString()
        }
      );
      
      // Clear subscriptions and pending requests
      this.subscriptions.clear();
      this.pendingRequests.clear();
      
      // Remove this component
      this.components.delete(this.options.componentId);
      
      this.status = ComponentStatus.DISCONNECTED;
      this.emit('disconnected');
      
      this.log('Disconnected from communication system');
    } catch (error) {
      this.status = ComponentStatus.ERROR;
      this.emit('error', error);
      this.log('Error disconnecting from communication system:', error);
      throw error;
    }
  }
  
  /**
   * Subscribe to a topic
   * @param {string} topic - The topic to subscribe to
   * @param {Function} handler - The message handler function
   * @param {Object} options - Subscription options
   * @returns {string} - The subscription ID
   */
  subscribe(topic, handler, options = {}) {
    const subscriptionId = options.subscriptionId || crypto.randomUUID();
    
    // Create a subscription object
    const subscription = {
      id: subscriptionId,
      topic,
      handler,
      options: {
        priority: options.priority !== undefined ? options.priority : EventPriority.NORMAL,
        filter: options.filter || null,
        ...options
      }
    };
    
    // Add to subscriptions
    this.subscriptions.set(subscriptionId, subscription);
    
    // Register event handler for this topic
    this.options.eventProcessor.registerHandler(
      `message.${MessageType.EVENT.toLowerCase()}.${topic}`,
      async (event) => {
        const message = event.data.message;
        
        // Check if the message has expired
        if (message.isExpired()) {
          this.log(`Dropped expired message: ${message.id}`);
          return;
        }
        
        // Check if the message is for this component or is a broadcast
        if (message.recipient && message.recipient !== this.options.componentId) {
          return;
        }
        
        // Apply filter if specified
        if (subscription.options.filter && !subscription.options.filter(message)) {
          return;
        }
        
        try {
          await handler(message);
          this.emit('messageReceived', { message, subscription: subscriptionId });
        } catch (error) {
          this.emit('handlerError', { message, subscription: subscriptionId, error });
          this.log(`Error in message handler for topic ${topic}:`, error);
          throw error;
        }
      },
      { priority: subscription.options.priority }
    );
    
    this.log(`Subscribed to topic: ${topic} with ID: ${subscriptionId}`);
    this.emit('subscribed', { topic, subscriptionId });
    
    return subscriptionId;
  }
  
  /**
   * Unsubscribe from a topic
   * @param {string} subscriptionId - The subscription ID to unsubscribe
   * @returns {boolean} - Whether the subscription was successfully unsubscribed
   */
  unsubscribe(subscriptionId) {
    const subscription = this.subscriptions.get(subscriptionId);
    
    if (!subscription) {
      return false;
    }
    
    // Remove from subscriptions
    this.subscriptions.delete(subscriptionId);
    
    this.log(`Unsubscribed from topic: ${subscription.topic} with ID: ${subscriptionId}`);
    this.emit('unsubscribed', { topic: subscription.topic, subscriptionId });
    
    return true;
  }
  
  /**
   * Publish a message to a topic
   * @param {string|Message} type - The message type or a Message instance
   * @param {string} topic - The topic to publish to
   * @param {Object} payload - The message payload
   * @param {Object} options - Publish options
   * @returns {Promise<Message>} - A promise that resolves to the published message
   */
  async publish(type, topic, payload = {}, options = {}) {
    // If the first argument is a Message, use it directly
    const message = type instanceof Message
      ? type
      : new Message(type, topic, payload, {
          sender: this.options.componentId,
          ...options
        });
    
    // Convert the message to an event and process it
    const event = message.toEvent();
    await this.options.eventProcessor.processEvent(event);
    
    this.log(`Published message to topic: ${message.topic}`);
    return message;
  }
  
  /**
   * Send a request and wait for a response
   * @param {string} topic - The topic to send the request to
   * @param {Object} payload - The request payload
   * @param {Object} options - Request options
   * @returns {Promise<Message>} - A promise that resolves to the response message
   */
  async request(topic, payload = {}, options = {}) {
    const timeout = options.timeout || 30000; // Default: 30 seconds
    
    // Create a request message
    const requestMessage = new Message(
      MessageType.REQUEST,
      topic,
      payload,
      {
        sender: this.options.componentId,
        correlationId: options.correlationId || crypto.randomUUID(),
        ttl: timeout,
        ...options
      }
    );
    
    // Create a promise that will resolve when the response is received
    const responsePromise = new Promise((resolve, reject) => {
      // Set a timeout to reject the promise if no response is received
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestMessage.id);
        reject(new Error(`Request timed out after ${timeout}ms: ${requestMessage.id}`));
      }, timeout);
      
      // Store the pending request
      this.pendingRequests.set(requestMessage.id, {
        request: requestMessage,
        resolve,
        reject,
        timeoutId
      });
    });
    
    // Subscribe to the response topic
    const responseTopic = `response.${topic}`;
    const subscriptionId = this.subscribe(
      responseTopic,
      (responseMessage) => {
        // Check if this is a response to our request
        if (responseMessage.correlationId === requestMessage.correlationId) {
          // Get the pending request
          const pendingRequest = this.pendingRequests.get(requestMessage.id);
          
          if (pendingRequest) {
            // Clear the timeout
            clearTimeout(pendingRequest.timeoutId);
            
            // Remove the pending request
            this.pendingRequests.delete(requestMessage.id);
            
            // Unsubscribe from the response topic
            this.unsubscribe(subscriptionId);
            
            // Resolve the promise with the response message
            pendingRequest.resolve(responseMessage);
          }
        }
      },
      { autoUnsubscribe: true }
    );
    
    // Publish the request message
    await this.publish(requestMessage);
    
    // Wait for the response
    return responsePromise;
  }
  
  /**
   * Get the communicator state
   * @returns {Object} - The current state of the component communicator
   */
  getState() {
    return {
      componentId: this.options.componentId,
      status: this.status,
      subscriptions: Array.from(this.subscriptions.values()).map(s => ({
        id: s.id,
        topic: s.topic
      })),
      pendingRequests: this.pendingRequests.size,
      components: Array.from(this.components.values()),
      eventProcessorState: this.options.eventProcessor.getState()
    };
  }
}

module.exports = {
  ComponentCommunicator,
  Message,
  MessageType,
  ComponentStatus
};
