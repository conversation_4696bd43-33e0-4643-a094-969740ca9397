# NovaFuse Universal API Connector - Authentication

This document provides an overview of the authentication system for the NovaFuse Universal API Connector (UAC).

## Overview

The authentication system is responsible for:

1. Securely storing API credentials
2. Authenticating API requests
3. Testing API connections
4. Supporting multiple authentication methods

## Authentication Manager

The Authentication Manager (`src/auth/authentication-manager.js`) is the central component of the authentication system. It provides the following features:

- Secure credential storage and retrieval
- Request authentication for different authentication types
- Connection testing
- Error handling

## Authentication Types

The UAC supports the following authentication types:

### API Key Authentication

API key authentication is the simplest form of authentication. It involves adding an API key to the request, either in a header, query parameter, or as a Bearer token.

Example connector configuration:

```javascript
{
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true,
        sensitive: true
      }
    },
    placement: 'header',
    paramName: 'x-api-key'
  }
}
```

### Basic Authentication

Basic authentication involves sending a username and password with the request. The credentials are Base64 encoded and added to the Authorization header.

Example connector configuration:

```javascript
{
  authentication: {
    type: 'BASIC',
    fields: {
      username: {
        type: 'string',
        description: 'Username',
        required: true
      },
      password: {
        type: 'string',
        description: 'Password',
        required: true,
        sensitive: true
      }
    }
  }
}
```

### OAuth2 Authentication

OAuth2 authentication involves obtaining an access token from an authorization server and using it to authenticate requests. The UAC supports the following OAuth2 grant types:

- Client Credentials
- Password
- (Future) Authorization Code

Example connector configuration:

```javascript
{
  authentication: {
    type: 'OAUTH2',
    fields: {
      client_id: {
        type: 'string',
        description: 'Client ID',
        required: true
      },
      client_secret: {
        type: 'string',
        description: 'Client Secret',
        required: true,
        sensitive: true
      }
    },
    oauth2Config: {
      tokenUrl: 'https://api.example.com/oauth2/token',
      authorizationUrl: 'https://api.example.com/oauth2/authorize',
      scopes: ['read', 'write'],
      grantType: 'client_credentials'
    }
  }
}
```

### JWT Authentication

JWT authentication involves using a JSON Web Token (JWT) to authenticate requests. The token is added to the Authorization header as a Bearer token.

Example connector configuration:

```javascript
{
  authentication: {
    type: 'JWT',
    fields: {
      token: {
        type: 'string',
        description: 'JWT Token',
        required: true,
        sensitive: true
      }
    }
  }
}
```

### AWS Signature V4 Authentication

AWS Signature V4 authentication is used for authenticating requests to AWS services. It involves signing the request with an access key and secret key.

Example connector configuration:

```javascript
{
  authentication: {
    type: 'AWS_SIG_V4',
    fields: {
      accessKeyId: {
        type: 'string',
        description: 'AWS Access Key ID',
        required: true
      },
      secretAccessKey: {
        type: 'string',
        description: 'AWS Secret Access Key',
        required: true,
        sensitive: true
      },
      region: {
        type: 'string',
        description: 'AWS Region',
        required: true
      },
      service: {
        type: 'string',
        description: 'AWS Service',
        required: false,
        default: 'execute-api'
      }
    }
  }
}
```

### Custom Authentication

Custom authentication allows for connector-specific authentication methods. The UAC provides a placeholder for custom authentication, but the actual implementation is left to the connector.

### No Authentication

Some APIs don't require authentication. The UAC supports this with the `NONE` authentication type.

## Credential Storage

API credentials are stored securely in the database using the Credential model. Sensitive fields are encrypted using AES-256-CBC encryption.

The credential storage process:

1. Identify sensitive fields based on field names and configuration
2. Encrypt sensitive fields using AES-256-CBC
3. Store encrypted credentials in the database
4. Generate a unique credential ID for future reference

## Request Authentication

The request authentication process:

1. Retrieve the connector configuration
2. Retrieve the credentials
3. Determine the authentication type
4. Apply the appropriate authentication handler
5. Return the authenticated request

## Connection Testing

The UAC includes a connection testing feature that allows users to verify their credentials before using them. The connection testing process:

1. Retrieve the connector configuration
2. Retrieve the credentials
3. Build a test request based on the connector's test configuration
4. Authenticate the request
5. Send the request to the API
6. Verify the response against the expected response
7. Return the test result

## Security Considerations

The authentication system includes the following security features:

- Encryption of sensitive credential fields
- Secure credential storage and retrieval
- Credential access control based on ownership
- Credential usage tracking

## Error Handling

The authentication system includes comprehensive error handling:

- Detailed error logging
- Graceful error recovery
- Consistent error patterns
- Validation before authentication operations

## Testing

The authentication system includes comprehensive tests:

- Unit tests for authentication handlers
- Integration tests for credential storage and retrieval
- Mock testing for external dependencies
- Coverage targets of 80% or higher

## Environment Variables

The authentication system uses the following environment variables:

- `CREDENTIALS_ENCRYPTION_KEY` - Key for encrypting sensitive credential data
- `JWT_SECRET` - Secret for JWT operations

## Future Enhancements

Planned enhancements for the authentication system include:

- Support for additional OAuth2 grant types
- Enhanced JWT handling with token validation
- Improved AWS Signature V4 implementation
- Support for mutual TLS authentication
- Support for API key rotation
