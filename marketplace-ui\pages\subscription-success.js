import Head from "next/head";
import { useRouter } from "next/router";

export default function SubscriptionSuccess() {
  const router = useRouter();

  // Mock subscription details
  const subscriptionDetails = {
    plan: "Professional",
    price: 499,
    billingCycle: "monthly",
    startDate: "2025-01-01",
    nextBillingDate: "2025-02-01",
    apiKey: "nf_test_api_key_12345678901234567890"
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Subscription Activated | NovaFuse API Superstore</title>
        <meta name="description" content="Your NovaFuse API Superstore subscription has been activated" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <h1 className="text-3xl font-bold text-gray-900">Subscription Activated</h1>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="p-6 bg-green-50 border-b border-green-100">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <h2 className="text-xl font-bold text-green-800">
                  Your subscription has been activated!
                </h2>
                <p className="text-green-700 mt-1">
                  Thank you for subscribing to the NovaFuse API Superstore.
                </p>
              </div>
            </div>
          </div>

          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Subscription Details</h3>

            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500">Plan</p>
                  <p className="font-medium">{subscriptionDetails.plan}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Price</p>
                  <p className="font-medium">${subscriptionDetails.price}/{subscriptionDetails.billingCycle}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p className="font-medium">{subscriptionDetails.startDate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Next Billing Date</p>
                  <p className="font-medium">{subscriptionDetails.nextBillingDate}</p>
                </div>
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-4">Your API Key</h3>
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-500 mb-2">
                Use this API key to authenticate your requests to the NovaFuse API Superstore.
              </p>
              <div className="flex items-center">
                <code className="bg-gray-100 px-3 py-2 rounded font-mono text-sm flex-grow overflow-x-auto">
                  {subscriptionDetails.apiKey}
                </code>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(subscriptionDetails.apiKey);
                    alert("API key copied to clipboard!");
                  }}
                  className="ml-2 p-2 text-gray-500 hover:text-gray-700"
                  title="Copy to clipboard"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                  </svg>
                </button>
              </div>
              <p className="text-sm text-red-500 mt-2">
                Keep this key secure! Do not share it publicly.
              </p>
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-4">Next Steps</h3>
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <ol className="list-decimal list-inside space-y-2 text-blue-800">
                <li>
                  <span className="font-medium">Explore the API documentation</span>
                  <p className="text-sm text-blue-700 ml-5 mt-1">
                    Learn how to use the NovaFuse API Superstore with our comprehensive documentation.
                  </p>
                </li>
                <li>
                  <span className="font-medium">Integrate with your application</span>
                  <p className="text-sm text-blue-700 ml-5 mt-1">
                    Use our SDKs and code examples to quickly integrate with your application.
                  </p>
                </li>
                <li>
                  <span className="font-medium">Monitor your usage</span>
                  <p className="text-sm text-blue-700 ml-5 mt-1">
                    Keep track of your API usage and billing through your account dashboard.
                  </p>
                </li>
                <li>
                  <span className="font-medium">Get support</span>
                  <p className="text-sm text-blue-700 ml-5 mt-1">
                    Our support team is available to help you with any questions or issues.
                  </p>
                </li>
              </ol>
            </div>

            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 mt-8">
              <button
                onClick={() => router.push("/api-docs")}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700"
              >
                View API Documentation
              </button>
              <button
                onClick={() => router.push("/dashboard")}
                className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-blue-50"
              >
                Go to Dashboard
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Need Help?</h3>
          <p className="text-gray-600 mb-4">
            Our support team is available to help you get started with the NovaFuse API Superstore.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Documentation</h4>
              <p className="text-sm text-gray-600 mb-3">
                Explore our comprehensive documentation to learn how to use the API.
              </p>
              <button
                onClick={() => router.push("/api-docs")}
                className="text-blue-600 text-sm font-medium hover:text-blue-800"
              >
                View Documentation →
              </button>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Support</h4>
              <p className="text-sm text-gray-600 mb-3">
                Contact our support team for help with any issues or questions.
              </p>
              <button
                onClick={() => window.open("mailto:<EMAIL>")}
                className="text-blue-600 text-sm font-medium hover:text-blue-800"
              >
                Contact Support →
              </button>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Community</h4>
              <p className="text-sm text-gray-600 mb-3">
                Join our community forum to connect with other developers.
              </p>
              <button
                onClick={() => router.push("/community")}
                className="text-blue-600 text-sm font-medium hover:text-blue-800"
              >
                Join Community →
              </button>
            </div>
          </div>
        </div>
      </main>

      <footer className="bg-gray-800 text-white py-12 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
