const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const port = 3009;

// In-memory storage for usage data
const usageData = [];

// In-memory storage for subscriptions
const subscriptions = [];

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Track usage
app.post('/track', (req, res) => {
  const { userId, connectorId, endpointId, timestamp, status, responseTime } = req.body;
  
  if (!userId || !connectorId || !endpointId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  
  const usage = {
    id: `usage-${Date.now()}`,
    userId,
    connectorId,
    endpointId,
    timestamp: timestamp || new Date().toISOString(),
    status: status || 'success',
    responseTime: responseTime || 0
  };
  
  usageData.push(usage);
  
  console.log(`Tracked usage: ${JSON.stringify(usage)}`);
  
  res.status(201).json({ success: true });
});

// Get usage data
app.get('/usage', (req, res) => {
  const { userId, startDate, endDate } = req.query;
  
  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }
  
  let filteredUsage = usageData.filter(usage => usage.userId === userId);
  
  if (startDate) {
    filteredUsage = filteredUsage.filter(usage => new Date(usage.timestamp) >= new Date(startDate));
  }
  
  if (endDate) {
    filteredUsage = filteredUsage.filter(usage => new Date(usage.timestamp) <= new Date(endDate));
  }
  
  // Group by connector and endpoint
  const groupedUsage = filteredUsage.reduce((acc, item) => {
    const key = `${item.connectorId}:${item.endpointId}`;
    if (!acc[key]) {
      acc[key] = {
        connectorId: item.connectorId,
        endpointId: item.endpointId,
        count: 0,
        successCount: 0,
        errorCount: 0
      };
    }
    
    acc[key].count++;
    if (item.status === 'success') {
      acc[key].successCount++;
    } else {
      acc[key].errorCount++;
    }
    
    return acc;
  }, {});
  
  res.json(Object.values(groupedUsage));
});

// Get usage summary
app.get('/usage/summary', (req, res) => {
  const { userId, period } = req.query;
  
  if (!userId) {
    return res.status(400).json({ error: 'userId is required' });
  }
  
  // Calculate date range based on period
  const endDate = new Date();
  let startDate = new Date();
  
  switch (period) {
    case 'day':
      startDate.setDate(startDate.getDate() - 1);
      break;
    case 'week':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'year':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate.setMonth(startDate.getMonth() - 1); // Default to month
  }
  
  const filteredUsage = usageData.filter(usage => 
    usage.userId === userId && 
    new Date(usage.timestamp) >= startDate && 
    new Date(usage.timestamp) <= endDate
  );
  
  // Get subscription
  const subscription = subscriptions.find(sub => sub.userId === userId && sub.status === 'active');
  
  // Calculate usage metrics
  const totalCalls = filteredUsage.length;
  const successCalls = filteredUsage.filter(item => item.status === 'success').length;
  const errorCalls = filteredUsage.filter(item => item.status === 'error').length;
  
  // Get unique connectors
  const uniqueConnectors = [...new Set(filteredUsage.map(item => item.connectorId))];
  
  // Calculate usage percentage
  let usagePercentage = 0;
  let remainingCalls = 0;
  
  if (subscription) {
    usagePercentage = (totalCalls / subscription.limits.apiCalls) * 100;
    remainingCalls = subscription.limits.apiCalls - totalCalls;
  }
  
  res.json({
    totalCalls,
    successCalls,
    errorCalls,
    uniqueConnectors: uniqueConnectors.length,
    subscription: subscription ? {
      plan: subscription.plan,
      apiCallsLimit: subscription.limits.apiCalls,
      connectorsLimit: subscription.limits.connectors,
      usagePercentage,
      remainingCalls
    } : null,
    period
  });
});

// Create or update subscription
app.post('/subscriptions', (req, res) => {
  const { userId, plan, limits, endDate } = req.body;
  
  if (!userId || !plan || !limits) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  
  // Check if user already has an active subscription
  const existingIndex = subscriptions.findIndex(sub => sub.userId === userId && sub.status === 'active');
  
  if (existingIndex !== -1) {
    // Update existing subscription
    subscriptions[existingIndex] = {
      ...subscriptions[existingIndex],
      plan,
      limits,
      endDate: endDate ? new Date(endDate) : undefined,
      updated: new Date().toISOString()
    };
    
    res.json(subscriptions[existingIndex]);
  } else {
    // Create new subscription
    const subscription = {
      id: `subscription-${Date.now()}`,
      userId,
      plan,
      limits,
      startDate: new Date().toISOString(),
      endDate: endDate ? new Date(endDate).toISOString() : undefined,
      status: 'active',
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    subscriptions.push(subscription);
    
    res.status(201).json(subscription);
  }
});

// Get subscription
app.get('/subscriptions/:userId', (req, res) => {
  const { userId } = req.params;
  
  const subscription = subscriptions.find(sub => sub.userId === userId && sub.status === 'active');
  
  if (!subscription) {
    return res.status(404).json({ error: 'No active subscription found' });
  }
  
  res.json(subscription);
});

// Start the server
app.listen(port, () => {
  console.log(`Usage Metering service running on port ${port}`);
});
