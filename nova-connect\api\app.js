/**
 * NovaConnect API Server
 *
 * This is the main entry point for the NovaConnect API server.
 */

const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const errorHandler = require('./middleware/errorHandler');
const databaseManager = require('./config/database');
const logger = require('./config/logger');
const initializeDatabase = require('./config/init-db');
const UserAuthenticationService = require('../auth/user-authentication-service');

// Import routes
const connectorRoutes = require('./routes/connectorRoutes');
const testingRoutes = require('./routes/testingRoutes');
const monitoringRoutes = require('./routes/monitoringRoutes');
const credentialRoutes = require('./routes/credentialRoutes');
const graphqlRoutes = require('./routes/graphqlRoutes');
const graphqlSubscriptionRoutes = require('./routes/graphqlSubscriptionRoutes');
const authRoutes = require('./routes/authRoutes');
const oauth2Routes = require('./routes/oauth2Routes');
const twoFactorRoutes = require('./routes/twoFactorRoutes');
const authTwoFactorRoutes = require('./routes/authTwoFactorRoutes');
const authAuditRoutes = require('./routes/authAuditRoutes');
const apiKeyRoutes = require('./routes/apiKeyRoutes');
const rateLimitRoutes = require('./routes/rateLimitRoutes');
const bruteForceRoutes = require('./routes/bruteForceRoutes');
const ipRestrictionRoutes = require('./routes/ipRestrictionRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const environmentRoutes = require('./routes/environmentRoutes');
const teamRoutes = require('./routes/teamRoutes');
const rolePermissionRoutes = require('./routes/rolePermissionRoutes');
const auditRoutes = require('./routes/auditRoutes');
const enhancedAuditRoutes = require('./routes/enhancedAuditRoutes');
const reportRoutes = require('./routes/reportRoutes');
const enhancedReportRoutes = require('./routes/enhancedReportRoutes');
const enhancedAnalyticsRoutes = require('./routes/enhancedAnalyticsRoutes');
const changeRequestRoutes = require('./routes/changeRequestRoutes');
const policyRoutes = require('./routes/policyRoutes');
const resourceLockRoutes = require('./routes/resourceLockRoutes');
const identityProviderRoutes = require('./routes/identityProviderRoutes');
const ssoAuthRoutes = require('./routes/ssoAuthRoutes');
const themeRoutes = require('./routes/themeRoutes');
const brandingRoutes = require('./routes/brandingRoutes');
const whiteLabelRoutes = require('./routes/whiteLabelRoutes');

const exportImportRoutes = require('./routes/exportImportRoutes');
const workflowRoutes = require('./routes/workflowRoutes');
const featureFlagRoutes = require('./routes/featureFlagRoutes');
const packageRoutes = require('./routes/packageRoutes');
const billingRoutes = require('./routes/billingRoutes');
const zapierRoutes = require('./routes/zapierRoutes');
const aiAssistRoutes = require('./routes/aiAssistRoutes');
const governanceRoutes = require('./routes/governanceRoutes');
const securityRoutes = require('./routes/securityRoutes');

// Import middleware
const { optionalAuth } = require('./middleware/authMiddleware');
const { trackApiUsage, trackApiErrors } = require('./middleware/analyticsMiddleware');
const { globalLimiter, authLimiter, apiLimiter } = require('./middleware/rateLimitMiddleware');
const createIpRestrictionMiddleware = require('./middleware/ipRestrictionMiddleware');
const ipRestrictionMiddleware = createIpRestrictionMiddleware();
const AuditService = require('./services/AuditService');
const auditService = new AuditService();
const auditMiddleware = auditService.createAuditMiddleware();

// Import enhanced error handling middleware
const {
  errorHandler: enhancedErrorHandler,
  notFoundHandler: enhancedNotFoundHandler,
  correlationIdMiddleware,
  timeoutMiddleware
} = require('./middleware/enhancedErrorHandlingMiddleware');

// Initialize authentication service
const userAuthService = new UserAuthenticationService();

// Create Express app
const app = express();

// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // Enable CORS
app.use(morgan('dev')); // Logging
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Apply global rate limiter to all requests
app.use(globalLimiter);

// Apply IP restriction middleware to all requests
app.use(ipRestrictionMiddleware);

// Apply analytics middleware to track API usage
app.use(trackApiUsage);

// Apply audit middleware to log user actions
app.use(auditMiddleware);

// Apply optional authentication to all routes
app.use(optionalAuth);

// API routes
// Apply stricter rate limiting to authentication routes
app.use('/api/auth', authLimiter, authRoutes);
app.use('/api/auth/2fa', authLimiter, authTwoFactorRoutes);
app.use('/api/auth/audit', apiLimiter, authAuditRoutes);
app.use('/api/oauth2', authLimiter, oauth2Routes);

// Apply standard API rate limiting to other routes
app.use('/api/2fa', apiLimiter, twoFactorRoutes);
app.use('/api/api-keys', apiLimiter, apiKeyRoutes);
app.use('/api/analytics', apiLimiter, analyticsRoutes);
app.use('/api/enhanced-analytics', apiLimiter, enhancedAnalyticsRoutes);
app.use('/api/environments', apiLimiter, environmentRoutes);
app.use('/api/teams', apiLimiter, teamRoutes);
app.use('/api/roles', apiLimiter, rolePermissionRoutes);
app.use('/api/audit', apiLimiter, auditRoutes);
app.use('/api/enhanced-audit', apiLimiter, enhancedAuditRoutes);
app.use('/api/change-requests', apiLimiter, changeRequestRoutes);
app.use('/api/policies', apiLimiter, policyRoutes);
app.use('/api/resource-locks', apiLimiter, resourceLockRoutes);
app.use('/api/identity-providers', apiLimiter, identityProviderRoutes);
app.use('/api/sso', apiLimiter, ssoAuthRoutes);
app.use('/api/rate-limits', apiLimiter, rateLimitRoutes);
app.use('/api/brute-force', apiLimiter, bruteForceRoutes);
app.use('/api/ip-restrictions', apiLimiter, ipRestrictionRoutes);
app.use('/api/themes', apiLimiter, themeRoutes);
app.use('/api/branding', apiLimiter, brandingRoutes);
app.use('/api/white-label', apiLimiter, whiteLabelRoutes);
app.use('/api/reports', apiLimiter, reportRoutes);
app.use('/api/enhanced-reports', apiLimiter, enhancedReportRoutes);
app.use('/api/export-import', apiLimiter, exportImportRoutes);
app.use('/api/workflows', apiLimiter, workflowRoutes);
app.use('/api/subscription', apiLimiter, featureFlagRoutes);
app.use('/api/packages', apiLimiter, packageRoutes);
app.use('/api/billing', apiLimiter, billingRoutes);
app.use('/api/zapier', apiLimiter, zapierRoutes);
app.use('/api/ai-assist', apiLimiter, aiAssistRoutes);
app.use('/api/governance', apiLimiter, governanceRoutes);
app.use('/api/security', apiLimiter, securityRoutes);
app.use('/api/connectors', apiLimiter, connectorRoutes);
app.use('/api/testing', apiLimiter, testingRoutes);
app.use('/api/monitoring', apiLimiter, monitoringRoutes);
app.use('/api/credentials', apiLimiter, credentialRoutes);
app.use('/api/graphql', apiLimiter, graphqlRoutes);
app.use('/api/graphql/subscriptions', apiLimiter, graphqlSubscriptionRoutes);

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Apply correlation ID middleware
app.use(correlationIdMiddleware);

// Apply timeout middleware (30 seconds)
app.use(timeoutMiddleware(30000));

// Apply error tracking middleware
app.use(trackApiErrors);

// Apply 404 handler for undefined routes
app.use(enhancedNotFoundHandler);

// Apply enhanced error handling
app.use(enhancedErrorHandler);

// Start server
const PORT = process.env.PORT || 3001;

// Connect to database and initialize server
async function startServer() {
  try {
    logger.info('Starting NovaConnect API server...');

    // Connect to database
    logger.info('Connecting to database...');
    await databaseManager.connect();

    // Initialize database with default data
    await initializeDatabase();

    // Initialize authentication service
    await userAuthService.initialize(databaseManager.connection);

    // Start server
    app.listen(PORT, () => {
      logger.info(`NovaConnect API server running on port ${PORT}`);
      logger.info('Available routes:');
      logger.info('- /api/auth/login');
      logger.info('- /api/auth/register');
      logger.info('- /api/connectors');
      logger.info('- /api/testing');
      logger.info('- /api/graphql/execute');
      logger.info('- /api/enhanced-audit/logs');
      logger.info('- /api/enhanced-audit/logs/export');
      logger.info('- /api/enhanced-audit/logs/stats');
      logger.info('- /api/enhanced-reports/compliance');
      logger.info('- /api/enhanced-reports/performance');
      logger.info('- /api/enhanced-reports/security');
      logger.info('- /api/enhanced-reports/custom');
      logger.info('- /api/enhanced-analytics/user');
      logger.info('- /api/enhanced-analytics/connector');
      logger.info('- /api/enhanced-analytics/compliance');
      logger.info('- /api/enhanced-analytics/predictive');
    });
  } catch (error) {
    logger.error('Failed to start server:', { error: error.message });
    process.exit(1);
  }
}

// Start the server
startServer();

module.exports = app;
