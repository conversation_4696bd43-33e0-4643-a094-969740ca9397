/**
 * NovaFuse GCP Simulation Data Models
 * 
 * This file exports all data models for the NovaFuse GCP simulation.
 */

const complianceFrameworks = require('./compliance-frameworks');
const { riskScenarios, riskHeatmapData } = require('./risk-scenarios');
const userPersonas = require('./user-personas');
const { evidenceRecords, evidenceBinder } = require('./evidence-records');

module.exports = {
  complianceFrameworks,
  riskScenarios,
  riskHeatmapData,
  userPersonas,
  evidenceRecords,
  evidenceBinder
};
