/**
 * Business Intelligence & Workflow Connector Implementation
 * 
 * This module implements the connector for business intelligence platforms and workflow automation systems.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('business-intelligence-workflow-connector');

/**
 * Business Intelligence & Workflow Connector
 */
class BusinessIntelligenceWorkflowConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing Business Intelligence & Workflow connector');
    
    // Authenticate if needed
    if (this.credentials.clientId && this.credentials.clientSecret) {
      await this.authenticate();
    }
  }

  /**
   * Authenticate with the API
   * 
   * @returns {Promise<void>}
   */
  async authenticate() {
    logger.info('Authenticating with Business Intelligence & Workflow API');
    
    try {
      const response = await axios.post(`${this.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: this.credentials.clientId,
        client_secret: this.credentials.clientSecret,
        scope: 'read:dashboards write:dashboards read:reports write:reports read:workflows write:workflows'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      
      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);
      
      logger.info('Authentication successful');
    } catch (error) {
      logger.error('Authentication failed', { error: error.message });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Promise<Object>} - Authentication headers
   */
  async getAuthHeaders() {
    // Check if token is expired or about to expire (within 5 minutes)
    if (!this.accessToken || (this.tokenExpiry && this.tokenExpiry - Date.now() < 300000)) {
      await this.authenticate();
    }
    
    return {
      'Authorization': `Bearer ${this.accessToken}`
    };
  }

  /**
   * List dashboards
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of dashboards
   */
  async listDashboards(params = {}) {
    logger.info('Listing dashboards', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/dashboards`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing dashboards', { error: error.message });
      throw new Error(`Error listing dashboards: ${error.message}`);
    }
  }

  /**
   * Get a dashboard
   * 
   * @param {string} dashboardId - Dashboard ID
   * @returns {Promise<Object>} - Dashboard details
   */
  async getDashboard(dashboardId) {
    logger.info('Getting dashboard', { dashboardId });
    
    if (!dashboardId) {
      throw new Error('Dashboard ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/dashboards/${dashboardId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting dashboard', { dashboardId, error: error.message });
      throw new Error(`Error getting dashboard: ${error.message}`);
    }
  }

  /**
   * Create a dashboard
   * 
   * @param {Object} dashboardData - Dashboard data
   * @returns {Promise<Object>} - Created dashboard
   */
  async createDashboard(dashboardData) {
    logger.info('Creating dashboard');
    
    if (!dashboardData) {
      throw new Error('Dashboard data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name'];
    for (const field of requiredFields) {
      if (!dashboardData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/dashboards`, dashboardData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating dashboard', { error: error.message });
      throw new Error(`Error creating dashboard: ${error.message}`);
    }
  }

  /**
   * Update a dashboard
   * 
   * @param {string} dashboardId - Dashboard ID
   * @param {Object} dashboardData - Dashboard data
   * @returns {Promise<Object>} - Updated dashboard
   */
  async updateDashboard(dashboardId, dashboardData) {
    logger.info('Updating dashboard', { dashboardId });
    
    if (!dashboardId) {
      throw new Error('Dashboard ID is required');
    }
    
    if (!dashboardData) {
      throw new Error('Dashboard data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/dashboards/${dashboardId}`, dashboardData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating dashboard', { dashboardId, error: error.message });
      throw new Error(`Error updating dashboard: ${error.message}`);
    }
  }

  /**
   * Delete a dashboard
   * 
   * @param {string} dashboardId - Dashboard ID
   * @returns {Promise<void>}
   */
  async deleteDashboard(dashboardId) {
    logger.info('Deleting dashboard', { dashboardId });
    
    if (!dashboardId) {
      throw new Error('Dashboard ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      await axios.delete(`${this.baseUrl}/dashboards/${dashboardId}`, {
        headers: {
          ...authHeaders,
          'Accept': 'application/json'
        }
      });
      
      logger.info('Dashboard deleted successfully', { dashboardId });
    } catch (error) {
      logger.error('Error deleting dashboard', { dashboardId, error: error.message });
      throw new Error(`Error deleting dashboard: ${error.message}`);
    }
  }

  /**
   * List reports
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of reports
   */
  async listReports(params = {}) {
    logger.info('Listing reports', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/reports`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing reports', { error: error.message });
      throw new Error(`Error listing reports: ${error.message}`);
    }
  }

  /**
   * Get a report
   * 
   * @param {string} reportId - Report ID
   * @returns {Promise<Object>} - Report details
   */
  async getReport(reportId) {
    logger.info('Getting report', { reportId });
    
    if (!reportId) {
      throw new Error('Report ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/reports/${reportId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting report', { reportId, error: error.message });
      throw new Error(`Error getting report: ${error.message}`);
    }
  }

  /**
   * Execute a report
   * 
   * @param {string} reportId - Report ID
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} - Execution result
   */
  async executeReport(reportId, options = {}) {
    logger.info('Executing report', { reportId, options });
    
    if (!reportId) {
      throw new Error('Report ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/reports/${reportId}/execute`, options, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error executing report', { reportId, error: error.message });
      throw new Error(`Error executing report: ${error.message}`);
    }
  }

  /**
   * List workflows
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of workflows
   */
  async listWorkflows(params = {}) {
    logger.info('Listing workflows', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/workflows`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing workflows', { error: error.message });
      throw new Error(`Error listing workflows: ${error.message}`);
    }
  }

  /**
   * Get a workflow
   * 
   * @param {string} workflowId - Workflow ID
   * @returns {Promise<Object>} - Workflow details
   */
  async getWorkflow(workflowId) {
    logger.info('Getting workflow', { workflowId });
    
    if (!workflowId) {
      throw new Error('Workflow ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/workflows/${workflowId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting workflow', { workflowId, error: error.message });
      throw new Error(`Error getting workflow: ${error.message}`);
    }
  }

  /**
   * Create a workflow
   * 
   * @param {Object} workflowData - Workflow data
   * @returns {Promise<Object>} - Created workflow
   */
  async createWorkflow(workflowData) {
    logger.info('Creating workflow');
    
    if (!workflowData) {
      throw new Error('Workflow data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name', 'trigger'];
    for (const field of requiredFields) {
      if (!workflowData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/workflows`, workflowData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating workflow', { error: error.message });
      throw new Error(`Error creating workflow: ${error.message}`);
    }
  }

  /**
   * Update a workflow
   * 
   * @param {string} workflowId - Workflow ID
   * @param {Object} workflowData - Workflow data
   * @returns {Promise<Object>} - Updated workflow
   */
  async updateWorkflow(workflowId, workflowData) {
    logger.info('Updating workflow', { workflowId });
    
    if (!workflowId) {
      throw new Error('Workflow ID is required');
    }
    
    if (!workflowData) {
      throw new Error('Workflow data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/workflows/${workflowId}`, workflowData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating workflow', { workflowId, error: error.message });
      throw new Error(`Error updating workflow: ${error.message}`);
    }
  }

  /**
   * Execute a workflow
   * 
   * @param {string} workflowId - Workflow ID
   * @param {Object} options - Execution options
   * @returns {Promise<Object>} - Execution result
   */
  async executeWorkflow(workflowId, options = {}) {
    logger.info('Executing workflow', { workflowId, options });
    
    if (!workflowId) {
      throw new Error('Workflow ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/workflows/${workflowId}/execute`, options, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error executing workflow', { workflowId, error: error.message });
      throw new Error(`Error executing workflow: ${error.message}`);
    }
  }
}

module.exports = BusinessIntelligenceWorkflowConnector;
