name: NovaConnect UAC CI/CD

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}
  GKE_CLUSTER: ${{ secrets.GKE_CLUSTER }}
  GKE_ZONE: ${{ secrets.GKE_ZONE }}
  IMAGE: novafuse-uac
  REGISTRY: gcr.io

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo:4.4
        ports:
          - 27017:27017
      redis:
        image: redis:6-alpine
        ports:
          - 6379:6379
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Lint code
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit

      - name: Run integration tests
        run: npm run test:integration

      - name: Run security tests
        run: npm run test:security

      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: test-results/
        if: always()

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true

      - name: Configure Docker for GCR
        run: gcloud auth configure-docker

      - name: Get short SHA
        id: sha
        run: echo "::set-output name=sha::$(git rev-parse --short HEAD)"

      - name: Build Docker image
        run: |
          docker build -t $REGISTRY/$PROJECT_ID/$IMAGE:${{ steps.sha.outputs.sha }} .
          docker tag $REGISTRY/$PROJECT_ID/$IMAGE:${{ steps.sha.outputs.sha }} $REGISTRY/$PROJECT_ID/$IMAGE:latest

      - name: Push Docker image
        run: |
          docker push $REGISTRY/$PROJECT_ID/$IMAGE:${{ steps.sha.outputs.sha }}
          docker push $REGISTRY/$PROJECT_ID/$IMAGE:latest

      - name: Save image info
        run: |
          echo "${{ steps.sha.outputs.sha }}" > image-tag.txt
          echo "$REGISTRY/$PROJECT_ID/$IMAGE:${{ steps.sha.outputs.sha }}" > image-url.txt

      - name: Upload image info
        uses: actions/upload-artifact@v3
        with:
          name: image-info
          path: |
            image-tag.txt
            image-url.txt

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: (github.event_name == 'push' && github.ref == 'refs/heads/develop') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true

      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}

      - name: Download image info
        uses: actions/download-artifact@v3
        with:
          name: image-info

      - name: Set image tag
        run: |
          IMAGE_TAG=$(cat image-tag.txt)
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

      - name: Update Kubernetes manifests
        run: |
          sed -i "s|IMAGE_TAG|$IMAGE_TAG|g" k8s/staging/*.yaml
          sed -i "s|\${PROJECT_ID}|$PROJECT_ID|g" k8s/staging/*.yaml

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f k8s/staging/namespace.yaml
          kubectl apply -f k8s/staging/configmap.yaml
          kubectl apply -f k8s/staging/secret.yaml
          kubectl apply -f k8s/staging/deployment.yaml
          kubectl apply -f k8s/staging/service.yaml
          kubectl apply -f k8s/staging/ingress.yaml
          kubectl apply -f k8s/staging/hpa.yaml

      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/novafuse-uac -n novafuse-staging --timeout=300s

      - name: Run post-deployment tests
        run: |
          echo "Running post-deployment tests..."
          # Add post-deployment test commands here

      - name: Notify on success
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Staging Deployment Successful"
          SLACK_MESSAGE: "NovaConnect UAC has been deployed to staging environment. Image tag: ${{ env.IMAGE_TAG }}"
          SLACK_COLOR: good
        if: success()

      - name: Notify on failure
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Staging Deployment Failed"
          SLACK_MESSAGE: "NovaConnect UAC deployment to staging environment failed. See GitHub Actions for details."
          SLACK_COLOR: danger
        if: failure()

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: (github.event_name == 'push' && github.ref == 'refs/heads/main') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          project_id: ${{ env.PROJECT_ID }}
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          export_default_credentials: true

      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{ env.GKE_CLUSTER }}
          location: ${{ env.GKE_ZONE }}

      - name: Download image info
        uses: actions/download-artifact@v3
        with:
          name: image-info

      - name: Set image tag
        run: |
          IMAGE_TAG=$(cat image-tag.txt)
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

      - name: Update Kubernetes manifests
        run: |
          sed -i "s|IMAGE_TAG|$IMAGE_TAG|g" k8s/production/*.yaml
          sed -i "s|\${PROJECT_ID}|$PROJECT_ID|g" k8s/production/*.yaml

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f k8s/production/namespace.yaml
          kubectl apply -f k8s/production/configmap.yaml
          kubectl apply -f k8s/production/secret.yaml
          kubectl apply -f k8s/production/deployment.yaml
          kubectl apply -f k8s/production/service.yaml
          kubectl apply -f k8s/production/ingress.yaml
          kubectl apply -f k8s/production/hpa.yaml

      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/novafuse-uac -n novafuse-production --timeout=300s

      - name: Run post-deployment tests
        run: |
          echo "Running post-deployment tests..."
          # Add post-deployment test commands here

      - name: Notify on success
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Production Deployment Successful"
          SLACK_MESSAGE: "NovaConnect UAC has been deployed to production environment. Image tag: ${{ env.IMAGE_TAG }}"
          SLACK_COLOR: good
        if: success()

      - name: Notify on failure
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_CHANNEL: deployments
          SLACK_TITLE: "Production Deployment Failed"
          SLACK_MESSAGE: "NovaConnect UAC deployment to production environment failed. See GitHub Actions for details."
          SLACK_COLOR: danger
        if: failure()
