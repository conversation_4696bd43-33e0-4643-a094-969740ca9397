/**
 * Execute Workflow Action
 * 
 * This action executes a workflow.
 */

// Define the action
module.exports = {
  key: 'execute_workflow',
  noun: 'Workflow',
  
  // Display information
  display: {
    label: 'Execute Workflow',
    description: 'Executes a workflow.',
    important: true
  },
  
  // Operation
  operation: {
    // Perform operation
    type: 'perform',
    
    // Perform the operation
    perform: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/actions/execute-workflow',
      method: 'POST',
      headers: {
        Authorization: 'Bearer {{bundle.authData.access_token}}',
        'Content-Type': 'application/json'
      },
      body: {
        workflowId: '{{bundle.inputData.workflowId}}',
        inputs: '{{bundle.inputData.inputs}}'
      }
    },
    
    // Input fields
    inputFields: [
      {
        key: 'workflowId',
        label: 'Workflow ID',
        type: 'string',
        required: true,
        helpText: 'The ID of the workflow to execute.'
      },
      {
        key: 'inputs',
        label: 'Inputs',
        type: 'text',
        required: false,
        helpText: 'The inputs for the workflow in JSON format.'
      }
    ],
    
    // Sample data
    sample: {
      id: 'exec-123',
      workflowId: 'wf-123',
      status: 'completed',
      result: {
        success: true,
        data: {}
      },
      startedAt: '2023-01-01T00:00:00Z',
      completedAt: '2023-01-01T00:00:01Z'
    },
    
    // Output fields
    outputFields: [
      { key: 'id', label: 'ID' },
      { key: 'workflowId', label: 'Workflow ID' },
      { key: 'status', label: 'Status' },
      { key: 'result.success', label: 'Success' },
      { key: 'result.data', label: 'Result Data' },
      { key: 'startedAt', label: 'Started At' },
      { key: 'completedAt', label: 'Completed At' }
    ]
  }
};
