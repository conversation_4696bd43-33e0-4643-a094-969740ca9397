/**
 * Real-Time Data Flow Example
 * 
 * This example demonstrates how to use the WebSocket-based real-time data flow
 * to connect the Self-Healing Tensor, 3D Tensor Visualization, and Analytics Dashboard.
 */

// Import the WebSocket module
const { createRealTimeCommunication } = require('../index');

// Import the unified integration module
const { createUnifiedIntegration } = require('../../integration');

// Import the Self-Healing Tensor
const SelfHealingTensor = require('../../quantum/self-healing-tensor');

// Import visualization components (mock for this example)
const VisualizationSystem = {
  on: (event, callback) => {
    // Mock event listener
    console.log(`Registered event listener for: ${event}`);
  },
  
  getVisualizationTypes: () => {
    return [
      '3d_tensor_visualization',
      'resonance_spectrogram',
      'phase_space_visualization',
      'harmonic_pattern_explorer'
    ];
  },
  
  create3dTensorVisualization: (data, options) => {
    console.log('Creating 3D Tensor Visualization', { data, options });
    return {
      id: `viz-${Date.now()}`,
      type: '3d_tensor_visualization',
      data
    };
  }
};

// Import analytics components (mock for this example)
const AnalyticsDashboard = {
  on: (event, callback) => {
    // Mock event listener
    console.log(`Registered event listener for: ${event}`);
  },
  
  getMetrics: () => {
    return {
      'tensor.health': 0.95,
      'tensor.entropyContainment': 0.02,
      'tensor.healingCycles': 3,
      'visualization.updateRate': 30,
      'system.performance': 0.87
    };
  },
  
  getDashboards: () => {
    return [
      {
        id: 'tensor-health-dashboard',
        name: 'Tensor Health Dashboard',
        metrics: ['tensor.health', 'tensor.entropyContainment', 'tensor.healingCycles']
      }
    ];
  }
};

// Run the example
async function runExample() {
  try {
    console.log('Starting Real-Time Data Flow Example...');
    
    // Create Self-Healing Tensor instance
    const selfHealingTensor = new SelfHealingTensor({
      healingThreshold: 0.6,
      healingFactor: 0.6,
      maxHealingCycles: 6,
      autoHeal: true
    });
    
    // Create unified integration
    const unifiedIntegration = createUnifiedIntegration({
      enableLogging: true,
      enableMetrics: true,
      autoConnect: true,
      updateInterval: 1000,
      tensor: selfHealingTensor,
      visualization: VisualizationSystem,
      analytics: AnalyticsDashboard
    });
    
    console.log('\nUnified Integration created successfully!');
    
    // Create real-time communication system
    const realTimeCommunication = createRealTimeCommunication({
      port: 3001,
      enableLogging: true,
      enableMetrics: true,
      tensorAdapter: unifiedIntegration.adapters.tensor,
      visualizationAdapter: unifiedIntegration.adapters.visualization,
      analyticsAdapter: unifiedIntegration.adapters.analytics
    });
    
    console.log('\nReal-Time Communication system created successfully!');
    
    // Create clients for each component
    const tensorClient = realTimeCommunication.createClient('tensor');
    const visualizationClient = realTimeCommunication.createClient('visualization');
    const analyticsClient = realTimeCommunication.createClient('analytics');
    
    // Connect clients
    await tensorClient.connect();
    await visualizationClient.connect();
    await analyticsClient.connect();
    
    console.log('\nClients connected successfully!');
    
    // Register a tensor
    console.log('\nRegistering a tensor...');
    const registerResponse = await tensorClient.send({
      component: 'tensor',
      type: 'register-tensor',
      id: 'example-tensor',
      tensor: {
        values: [0.5, 0.6, 0.7, 0.8, 0.9]
      },
      domain: 'universal'
    });
    
    console.log('Register tensor response:', registerResponse);
    
    // Create a visualization
    console.log('\nCreating a visualization...');
    const createVisualizationResponse = await visualizationClient.send({
      component: 'visualization',
      type: 'create-visualization',
      visualizationType: '3d_tensor_visualization',
      data: {
        tensor: registerResponse.result.tensor,
        dimensions: [5, 1, 1]
      },
      options: {
        renderMode: 'high-quality'
      }
    });
    
    console.log('Create visualization response:', createVisualizationResponse);
    
    // Get metrics
    console.log('\nGetting metrics...');
    const getMetricsResponse = await analyticsClient.send({
      component: 'analytics',
      type: 'get-metrics'
    });
    
    console.log('Get metrics response:', getMetricsResponse);
    
    // Simulate tensor damage
    console.log('\nSimulating tensor damage...');
    const damageTensorResponse = await tensorClient.send({
      component: 'tensor',
      type: 'damage-tensor',
      id: 'example-tensor',
      damageLevel: 0.5
    });
    
    console.log('Damage tensor response:', damageTensorResponse);
    
    // Update visualization with damaged tensor
    console.log('\nUpdating visualization with damaged tensor...');
    const updateVisualizationResponse = await visualizationClient.send({
      component: 'visualization',
      type: 'update-visualization',
      id: createVisualizationResponse.result.id,
      data: {
        tensor: damageTensorResponse.result.tensor,
        dimensions: [5, 1, 1]
      }
    });
    
    console.log('Update visualization response:', updateVisualizationResponse);
    
    // Heal the tensor
    console.log('\nHealing the tensor...');
    const healTensorResponse = await tensorClient.send({
      component: 'tensor',
      type: 'heal-tensor',
      id: 'example-tensor'
    });
    
    console.log('Heal tensor response:', healTensorResponse);
    
    // Update visualization with healed tensor
    console.log('\nUpdating visualization with healed tensor...');
    const updateVisualizationResponse2 = await visualizationClient.send({
      component: 'visualization',
      type: 'update-visualization',
      id: createVisualizationResponse.result.id,
      data: {
        tensor: healTensorResponse.result.tensor,
        dimensions: [5, 1, 1]
      }
    });
    
    console.log('Update visualization response:', updateVisualizationResponse2);
    
    // Get healing history
    console.log('\nGetting healing history...');
    const getHealingHistoryResponse = await tensorClient.send({
      component: 'tensor',
      type: 'get-healing-history',
      id: 'example-tensor'
    });
    
    console.log('Get healing history response:', getHealingHistoryResponse);
    
    // Set up real-time updates
    console.log('\nSetting up real-time updates...');
    
    // Subscribe to tensor updates
    await tensorClient.subscribe('tensor-updates');
    
    // Subscribe to visualization updates
    await visualizationClient.subscribe('visualization-updates');
    
    // Subscribe to analytics updates
    await analyticsClient.subscribe('analytics-updates');
    
    // Set up event handlers
    tensorClient.on('message:tensor-updates', (data) => {
      console.log('Received tensor update:', data);
      
      // Update visualization with new tensor data
      visualizationClient.publish('visualization-updates', {
        type: 'update-visualization',
        id: createVisualizationResponse.result.id,
        data: {
          tensor: data.tensor,
          dimensions: [5, 1, 1]
        }
      }).catch((error) => {
        console.error('Error publishing visualization update:', error);
      });
    });
    
    visualizationClient.on('message:visualization-updates', (data) => {
      console.log('Received visualization update:', data);
    });
    
    analyticsClient.on('message:analytics-updates', (data) => {
      console.log('Received analytics update:', data);
    });
    
    // Simulate real-time updates
    console.log('\nSimulating real-time updates...');
    
    // Publish tensor updates every 2 seconds
    const updateInterval = setInterval(() => {
      // Generate random tensor values
      const values = Array.from({ length: 5 }, () => Math.random());
      
      // Publish tensor update
      tensorClient.publish('tensor-updates', {
        type: 'tensor-updated',
        id: 'example-tensor',
        tensor: {
          values,
          health: Math.random(),
          entropyContainment: Math.random() * 0.1
        }
      }).catch((error) => {
        console.error('Error publishing tensor update:', error);
      });
      
      // Publish analytics update
      analyticsClient.publish('analytics-updates', {
        type: 'metrics-updated',
        metrics: {
          'tensor.health': Math.random(),
          'tensor.entropyContainment': Math.random() * 0.1,
          'tensor.healingCycles': Math.floor(Math.random() * 10),
          'visualization.updateRate': 30 + Math.floor(Math.random() * 10),
          'system.performance': 0.8 + Math.random() * 0.2
        }
      }).catch((error) => {
        console.error('Error publishing analytics update:', error);
      });
    }, 2000);
    
    // Run for 10 seconds
    console.log('\nRunning real-time updates for 10 seconds...');
    
    await new Promise((resolve) => setTimeout(resolve, 10000));
    
    // Clean up
    console.log('\nCleaning up...');
    
    clearInterval(updateInterval);
    
    // Disconnect clients
    await tensorClient.disconnect();
    await visualizationClient.disconnect();
    await analyticsClient.disconnect();
    
    // Stop real-time communication system
    await realTimeCommunication.stop();
    
    console.log('\nReal-Time Data Flow Example completed successfully!');
  } catch (error) {
    console.error('Error running Real-Time Data Flow Example:', error);
  }
}

// Run the example
runExample();
