import React, { useState, useEffect } from 'react';
import { UUICProvider } from './UUICProvider';
import { UUICSchemaRenderer } from './UUICRenderer';
import { uuicConfig } from './uuicConfig';

/**
 * UUICBridge - Data normalization and lifecycle handler
 * 
 * This component serves as a bridge between the backend NovaVision
 * and the frontend React components. It handles data normalization,
 * lifecycle events, and provides the necessary context for rendering.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - UI schema from NovaVision backend
 * @param {Object} props.data - Data for rendering
 * @param {Function} props.onSubmit - Form submission handler
 * @param {Function} props.onChange - Data change handler
 * @param {Function} props.onFilterChange - Dashboard filter change handler
 * @param {Function} props.onParameterChange - Report parameter change handler
 * @param {Function} props.onExport - Report export handler
 * @param {Object} props.options - Additional rendering options
 * @returns {React.ReactElement} Rendered UI
 */
const UUICBridge = ({
  schema,
  data: initialData = {},
  onSubmit,
  onChange,
  onFilterChange,
  onParameterChange,
  onExport,
  options = {}
}) => {
  // State for internal data management
  const [data, setData] = useState(initialData);
  
  // Update internal data when initialData changes
  useEffect(() => {
    setData(initialData);
  }, [initialData]);
  
  // Handle data changes
  const handleDataChange = (newData) => {
    setData(newData);
    if (onChange) {
      onChange(newData);
    }
  };
  
  // Handle filter changes
  const handleFilterChange = (newData) => {
    setData(newData);
    if (onFilterChange) {
      onFilterChange(newData);
    }
  };
  
  // Handle parameter changes
  const handleParameterChange = (newData) => {
    setData(newData);
    if (onParameterChange) {
      onParameterChange(newData);
    }
  };
  
  // Create module config for the provider
  const moduleConfig = {
    components: uuicConfig.components,
    componentConfigs: uuicConfig.componentConfigs,
    data: data
  };
  
  // Create rendering options
  const renderOptions = {
    ...options,
    onSubmit,
    onChange: handleDataChange,
    onFilterChange: handleFilterChange,
    onParameterChange: handleParameterChange,
    onExport
  };
  
  return (
    <UUICProvider moduleConfig={moduleConfig}>
      <UUICSchemaRenderer
        schema={schema}
        data={data}
        options={renderOptions}
      />
    </UUICProvider>
  );
};

export default UUICBridge;
