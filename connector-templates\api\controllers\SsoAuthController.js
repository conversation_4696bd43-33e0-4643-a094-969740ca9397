/**
 * SSO Authentication Controller
 * 
 * This controller handles API requests related to SSO authentication.
 */

const SsoAuthService = require('../services/SsoAuthService');
const { ValidationError } = require('../utils/errors');

class SsoAuthController {
  constructor() {
    this.ssoAuthService = new SsoAuthService();
  }

  /**
   * Initiate SSO authentication
   */
  async initiateAuth(req, res, next) {
    try {
      const { providerId } = req.params;
      const { redirectUri, relayState, nonce } = req.query;
      
      if (!providerId) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.ssoAuthService.initiateAuth(
        providerId,
        redirectUri,
        relayState,
        nonce
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process SAML callback
   */
  async processSamlCallback(req, res, next) {
    try {
      const { SAMLResponse, RelayState } = req.body;
      
      if (!SAMLResponse) {
        throw new ValidationError('SAML response is required');
      }
      
      const result = await this.ssoAuthService.processCallback({
        SAMLResponse,
        RelayState
      });
      
      // Redirect to frontend with token
      const redirectUrl = RelayState || '/';
      const url = new URL(redirectUrl);
      url.searchParams.set('token', result.token);
      
      res.redirect(url.toString());
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process OIDC callback
   */
  async processOidcCallback(req, res, next) {
    try {
      const { code, state } = req.query;
      const { redirectUri } = req.body;
      
      if (!code || !state) {
        throw new ValidationError('Code and state parameters are required');
      }
      
      const result = await this.ssoAuthService.processCallback({
        code,
        state,
        redirectUri
      });
      
      // Get state data to determine redirect URL
      const stateData = await this.ssoAuthService.oidcAuthService.getState(state);
      const redirectUrl = stateData?.redirectUri || '/';
      
      // Redirect to frontend with token
      const url = new URL(redirectUrl);
      url.searchParams.set('token', result.token);
      
      res.redirect(url.toString());
    } catch (error) {
      next(error);
    }
  }

  /**
   * Initiate SSO logout
   */
  async initiateLogout(req, res, next) {
    try {
      const { providerId } = req.params;
      const { idToken, postLogoutRedirectUri } = req.body;
      
      if (!providerId) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.ssoAuthService.initiateLogout(
        req.user.id,
        providerId,
        idToken,
        postLogoutRedirectUri
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process SAML logout callback
   */
  async processSamlLogoutCallback(req, res, next) {
    try {
      const { SAMLResponse, RelayState } = req.body;
      
      if (!SAMLResponse) {
        throw new ValidationError('SAML response is required');
      }
      
      const result = await this.ssoAuthService.processLogoutCallback({
        SAMLResponse,
        RelayState
      });
      
      // Redirect to frontend
      const redirectUrl = RelayState || '/';
      res.redirect(redirectUrl);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Refresh OIDC tokens
   */
  async refreshTokens(req, res, next) {
    try {
      const { refreshToken, providerId } = req.body;
      
      if (!refreshToken) {
        throw new ValidationError('Refresh token is required');
      }
      
      if (!providerId) {
        throw new ValidationError('Provider ID is required');
      }
      
      const result = await this.ssoAuthService.refreshTokens(refreshToken, providerId);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get provider by domain
   */
  async getProviderByDomain(req, res, next) {
    try {
      const { domain } = req.params;
      
      if (!domain) {
        throw new ValidationError('Domain is required');
      }
      
      const provider = await this.ssoAuthService.getProviderByDomain(domain);
      
      if (!provider) {
        return res.status(404).json({ error: 'No provider found for this domain' });
      }
      
      res.json(provider);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Clean up expired data
   */
  async cleanup(req, res, next) {
    try {
      const result = await this.ssoAuthService.cleanup();
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new SsoAuthController();
