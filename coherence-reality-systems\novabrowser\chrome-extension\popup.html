<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
        }
        
        .header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            background: linear-gradient(45deg, #00ff96, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .subtitle {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }
        
        .metrics {
            padding: 20px;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .metric-title {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .metric-details {
            font-size: 11px;
            opacity: 0.7;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-high { background: #00ff96; }
        .status-medium { background: #ffa726; }
        .status-low { background: #ff4757; }
        
        .actions {
            padding: 0 20px 20px;
        }
        
        .action-btn {
            width: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 10px;
            transition: transform 0.2s;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
        
        .action-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .violations {
            max-height: 120px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .violation-item {
            background: rgba(255, 71, 87, 0.1);
            border: 1px solid #ff4757;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 5px;
            font-size: 11px;
        }
        
        .loading {
            text-align: center;
            padding: 40px 20px;
        }
        
        .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-left: 3px solid #00ff96;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🧬</div>
        <div class="title">NovaBrowser</div>
        <div class="subtitle">Coherence-First Web Gateway</div>
    </div>
    
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <div>Analyzing page coherence...</div>
    </div>
    
    <div id="content" style="display: none;">
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-title">Overall Coherence</div>
                <div class="metric-value" id="coherence-value">--</div>
                <div class="metric-details">
                    <span class="status-indicator" id="coherence-indicator"></span>
                    <span id="coherence-status">Calculating...</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Accessibility Score</div>
                <div class="metric-value" id="accessibility-value">--</div>
                <div class="metric-details">
                    <span class="status-indicator" id="accessibility-indicator"></span>
                    <span id="accessibility-status">WCAG 2.1 Analysis</span>
                </div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Security Level</div>
                <div class="metric-value" id="security-value">--</div>
                <div class="metric-details">
                    <span class="status-indicator" id="security-indicator"></span>
                    <span id="security-status">Threat Assessment</span>
                </div>
            </div>
        </div>
        
        <div class="actions">
            <button class="action-btn" id="auto-fix-btn" style="display: none;">
                🔧 Auto-Fix Violations
            </button>
            <button class="action-btn secondary" id="refresh-btn">
                🔄 Refresh Analysis
            </button>
            <button class="action-btn secondary" id="toggle-overlay-btn">
                👁️ Toggle Overlay
            </button>
        </div>
        
        <div id="violations-section" style="display: none;">
            <div style="padding: 0 20px;">
                <div style="font-weight: bold; margin-bottom: 10px; color: #ff4757;">
                    Violations Found:
                </div>
                <div class="violations" id="violations-list"></div>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
