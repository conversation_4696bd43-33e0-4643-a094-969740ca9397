/**
 * Cache Manager
 *
 * This module provides caching capabilities for the Finite Universe Principle
 * defense system, optimizing performance for high-throughput scenarios.
 */

const EventEmitter = require('events');
const crypto = require('crypto');

/**
 * CacheManager class
 * 
 * Provides caching capabilities for the defense system.
 */
class CacheManager extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      maxCacheSize: 1000, // Maximum number of items in cache
      ttl: 60 * 1000, // Time to live in milliseconds (default: 1 minute)
      cleanupInterval: 5 * 60 * 1000, // Cleanup interval in milliseconds (default: 5 minutes)
      ...options
    };

    // Initialize cache
    this.cache = new Map();
    this.cacheHits = 0;
    this.cacheMisses = 0;
    this.cacheEvictions = 0;

    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this._cleanup();
    }, this.options.cleanupInterval);

    if (this.options.enableLogging) {
      console.log('CacheManager initialized with options:', this.options);
    }
  }

  /**
   * Get item from cache
   * @param {string} key - Cache key
   * @returns {*} - Cached item or undefined if not found
   */
  get(key) {
    // Get item from cache
    const item = this.cache.get(key);

    // Check if item exists and is not expired
    if (item && item.expiresAt > Date.now()) {
      this.cacheHits++;
      
      // Update access time
      item.lastAccessed = Date.now();
      
      return item.value;
    }

    // Item not found or expired
    this.cacheMisses++;
    
    // Remove expired item if it exists
    if (item) {
      this.cache.delete(key);
    }
    
    return undefined;
  }

  /**
   * Set item in cache
   * @param {string} key - Cache key
   * @param {*} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds (optional, defaults to options.ttl)
   */
  set(key, value, ttl = this.options.ttl) {
    // Check if cache is full
    if (this.cache.size >= this.options.maxCacheSize && !this.cache.has(key)) {
      this._evictLeastRecentlyUsed();
    }

    // Set item in cache
    this.cache.set(key, {
      value,
      createdAt: Date.now(),
      lastAccessed: Date.now(),
      expiresAt: Date.now() + ttl
    });

    // Emit event
    this.emit('cache-set', { key, value });
  }

  /**
   * Delete item from cache
   * @param {string} key - Cache key
   * @returns {boolean} - True if item was deleted, false otherwise
   */
  delete(key) {
    const deleted = this.cache.delete(key);
    
    if (deleted) {
      this.emit('cache-delete', { key });
    }
    
    return deleted;
  }

  /**
   * Clear cache
   */
  clear() {
    this.cache.clear();
    this.emit('cache-clear');
  }

  /**
   * Get cache size
   * @returns {number} - Number of items in cache
   */
  size() {
    return this.cache.size;
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.options.maxCacheSize,
      hits: this.cacheHits,
      misses: this.cacheMisses,
      evictions: this.cacheEvictions,
      hitRatio: this.cacheHits / (this.cacheHits + this.cacheMisses) || 0
    };
  }

  /**
   * Generate cache key from data
   * @param {*} data - Data to generate key from
   * @param {string} domain - Domain of the data
   * @returns {string} - Cache key
   */
  generateKey(data, domain = '') {
    // Convert data to string
    const dataString = JSON.stringify(data);
    
    // Generate hash
    const hash = crypto.createHash('md5').update(dataString).digest('hex');
    
    // Return key with domain prefix
    return domain ? `${domain}:${hash}` : hash;
  }

  /**
   * Cleanup expired items
   * @private
   */
  _cleanup() {
    const now = Date.now();
    let expiredCount = 0;
    
    // Remove expired items
    for (const [key, item] of this.cache.entries()) {
      if (item.expiresAt <= now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }
    
    if (expiredCount > 0 && this.options.enableLogging) {
      console.log(`Cleaned up ${expiredCount} expired items from cache`);
    }
    
    // Emit event
    if (expiredCount > 0) {
      this.emit('cache-cleanup', { expiredCount });
    }
  }

  /**
   * Evict least recently used item
   * @private
   */
  _evictLeastRecentlyUsed() {
    let oldestKey = null;
    let oldestAccess = Infinity;
    
    // Find least recently used item
    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestAccess) {
        oldestAccess = item.lastAccessed;
        oldestKey = key;
      }
    }
    
    // Evict item
    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.cacheEvictions++;
      
      // Emit event
      this.emit('cache-eviction', { key: oldestKey });
      
      if (this.options.enableLogging) {
        console.log(`Evicted least recently used item from cache: ${oldestKey}`);
      }
    }
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // Clear cache
    this.clear();
    
    if (this.options.enableLogging) {
      console.log('CacheManager disposed');
    }
  }
}

/**
 * Create a cache manager with recommended settings
 * @param {Object} options - Configuration options
 * @returns {CacheManager} - Configured cache manager
 */
function createCacheManager(options = {}) {
  return new CacheManager({
    enableLogging: true,
    maxCacheSize: 1000,
    ttl: 60 * 1000,
    cleanupInterval: 5 * 60 * 1000,
    ...options
  });
}

module.exports = {
  CacheManager,
  createCacheManager
};
