const Joi = require('joi');

// Validation schemas
const schemas = {
  createReport: Joi.object({
    title: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    framework: Joi.string().required(),
    reportingYear: Joi.number().integer().required().min(2000).max(2100),
    status: Joi.string().valid('draft', 'in-progress', 'completed', 'published').default('draft'),
    metrics: Joi.array().items(
      Joi.object({
        category: Joi.string().valid('environmental', 'social', 'governance').required(),
        name: Joi.string().required(),
        value: Joi.number().required(),
        unit: Joi.string().required()
      })
    ).optional()
  }),
  
  updateReport: Joi.object({
    title: Joi.string().min(3).max(100).optional(),
    description: Joi.string().max(500).optional(),
    framework: Joi.string().optional(),
    reportingYear: Joi.number().integer().min(2000).max(2100).optional(),
    status: Joi.string().valid('draft', 'in-progress', 'completed', 'published').optional(),
    metrics: Joi.array().items(
      Joi.object({
        category: Joi.string().valid('environmental', 'social', 'governance').required(),
        name: Joi.string().required(),
        value: Joi.number().required(),
        unit: Joi.string().required()
      })
    ).optional()
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
