#!/bin/bash
# install-novalift.sh
# NovaLift Enterprise System Booster Installer - Linux/macOS
# NovaFuse Coherence Operating System
# Cross-platform installer for Unix-based systems

set -euo pipefail

# NovaLift Configuration
NOVALIFT_VERSION="1.0.0"
NOVALIFT_NAME="NovaLift Enterprise System Booster"
NOVALIFT_PUBLISHER="NovaFuse Technologies"
INSTALL_PATH="${1:-/opt/novafuse}"
MODE="${2:-Enterprise}"
SKIP_DEPS="${3:-false}"

# Color functions for output
novalift_info() {
    echo -e "\033[1;36m🚀 [NovaLift] $1\033[0m"
}

novalift_success() {
    echo -e "\033[1;32m✅ [NovaLift] $1\033[0m"
}

novalift_warning() {
    echo -e "\033[1;33m⚠️ [NovaLift] $1\033[0m"
}

novalift_error() {
    echo -e "\033[1;31m❌ [NovaLift] $1\033[0m"
}

# Banner
show_banner() {
    echo ""
    echo -e "\033[1;35m╔══════════════════════════════════════════════════════════════════════════════╗\033[0m"
    echo -e "\033[1;35m║                           NOVALIFT ENTERPRISE INSTALLER                     ║\033[0m"
    echo -e "\033[1;35m║                        NovaFuse Coherence Operating System                   ║\033[0m"
    echo -e "\033[1;35m║                                Version $NOVALIFT_VERSION                                ║\033[0m"
    echo -e "\033[1;35m╚══════════════════════════════════════════════════════════════════════════════╝\033[0m"
    echo ""
}

# Detect OS and distribution
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        if command -v apt-get >/dev/null 2>&1; then
            DISTRO="debian"
            PKG_MANAGER="apt-get"
        elif command -v yum >/dev/null 2>&1; then
            DISTRO="rhel"
            PKG_MANAGER="yum"
        elif command -v dnf >/dev/null 2>&1; then
            DISTRO="fedora"
            PKG_MANAGER="dnf"
        elif command -v pacman >/dev/null 2>&1; then
            DISTRO="arch"
            PKG_MANAGER="pacman"
        else
            DISTRO="unknown"
            PKG_MANAGER="unknown"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        DISTRO="macos"
        PKG_MANAGER="brew"
    else
        OS="unknown"
        DISTRO="unknown"
        PKG_MANAGER="unknown"
    fi
    
    novalift_info "Detected OS: $OS ($DISTRO)"
}

# Check prerequisites
check_prerequisites() {
    novalift_info "Checking system prerequisites..."
    
    local missing_deps=()
    
    # Check bash version
    if [[ ${BASH_VERSION%%.*} -lt 4 ]]; then
        missing_deps+=("bash 4.0+")
    fi
    
    # Check curl
    if ! command -v curl >/dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    # Check Node.js
    if ! command -v node >/dev/null 2>&1; then
        missing_deps+=("nodejs (16.0+)")
    else
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ $node_version -lt 16 ]]; then
            missing_deps+=("nodejs 16.0+ (current: v$node_version)")
        fi
    fi
    
    # Check Python
    if ! command -v python3 >/dev/null 2>&1; then
        missing_deps+=("python3 (3.8+)")
    else
        local python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1-2)
        if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)" 2>/dev/null; then
            missing_deps+=("python3 3.8+ (current: $python_version)")
        fi
    fi
    
    # Check Docker (optional)
    if command -v docker >/dev/null 2>&1; then
        novalift_success "Docker detected: $(docker --version)"
    else
        novalift_warning "Docker not found (optional for advanced features)"
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]] && [[ "$SKIP_DEPS" != "true" ]]; then
        novalift_error "Missing prerequisites:"
        for dep in "${missing_deps[@]}"; do
            echo "  - $dep"
        done
        echo ""
        echo "Install missing dependencies or run with --skip-deps flag"
        return 1
    fi
    
    novalift_success "Prerequisites check completed"
    return 0
}

# Install dependencies
install_dependencies() {
    if [[ "$SKIP_DEPS" == "true" ]]; then
        novalift_info "Skipping dependency installation"
        return 0
    fi
    
    novalift_info "Installing NovaFuse dependencies..."
    
    case $PKG_MANAGER in
        "apt-get")
            sudo apt-get update
            sudo apt-get install -y curl wget git nodejs npm python3 python3-pip
            ;;
        "yum")
            sudo yum install -y curl wget git nodejs npm python3 python3-pip
            ;;
        "dnf")
            sudo dnf install -y curl wget git nodejs npm python3 python3-pip
            ;;
        "pacman")
            sudo pacman -S --noconfirm curl wget git nodejs npm python python-pip
            ;;
        "brew")
            brew install node python3
            ;;
        *)
            novalift_warning "Unknown package manager, skipping automatic dependency installation"
            ;;
    esac
    
    # Install Python packages
    if command -v pip3 >/dev/null 2>&1; then
        pip3 install --user fastapi uvicorn aiohttp requests
    fi
    
    novalift_success "Dependencies installation completed"
}

# Create directory structure
create_directories() {
    novalift_info "Creating NovaFuse directory structure..."
    
    local directories=(
        "$INSTALL_PATH"
        "$INSTALL_PATH/NovaCore"
        "$INSTALL_PATH/NovaAgent"
        "$INSTALL_PATH/NovaBridge"
        "$INSTALL_PATH/NovaConsole"
        "$INSTALL_PATH/NovaLift"
        "$INSTALL_PATH/Config"
        "$INSTALL_PATH/Logs"
        "$INSTALL_PATH/Data"
    )
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            sudo mkdir -p "$dir"
            novalift_info "Created directory: $dir"
        fi
    done
    
    # Set permissions
    sudo chown -R $USER:$USER "$INSTALL_PATH"
    sudo chmod -R 755 "$INSTALL_PATH"
    
    novalift_success "Directory structure created"
}

# Install NovaCore engines
install_novacore() {
    novalift_info "Installing NovaCore engines..."
    
    local novacore_source="nhetx-castl-alpha"
    local novacore_target="$INSTALL_PATH/NovaCore"
    
    if [[ -d "$novacore_source" ]]; then
        cp -r "$novacore_source"/* "$novacore_target/"
        novalift_success "NovaCore engines installed"
    else
        novalift_warning "NovaCore source not found, creating placeholder"
        echo "# NovaCore placeholder - engines will be installed separately" > "$novacore_target/README.md"
    fi
    
    # Install Node.js dependencies
    if [[ -f "$novacore_target/package.json" ]]; then
        cd "$novacore_target"
        npm install
        cd - >/dev/null
        novalift_success "NovaCore dependencies installed"
    fi
}

# Install NovaAgent
install_novaagent() {
    novalift_info "Installing NovaAgent runtime..."
    
    local novaagent_target="$INSTALL_PATH/NovaAgent"
    
    # Copy NovaAgent executable or source
    if [[ -f "nova-agent" ]]; then
        cp "nova-agent" "$novaagent_target/nova-agent"
        chmod +x "$novaagent_target/nova-agent"
    elif [[ -f "nova-agent.go" ]]; then
        # Build from source if Go is available
        if command -v go >/dev/null 2>&1; then
            novalift_info "Building NovaAgent from source..."
            cd "$novaagent_target"
            cp ../../../nova-agent.go .
            go mod init nova-agent
            go build -o nova-agent nova-agent.go
            cd - >/dev/null
        else
            novalift_warning "NovaAgent executable not found, creating placeholder"
            echo "#!/bin/bash" > "$novaagent_target/nova-agent"
            echo "echo 'NovaAgent placeholder - build from source required'" >> "$novaagent_target/nova-agent"
            chmod +x "$novaagent_target/nova-agent"
        fi
    else
        novalift_warning "NovaAgent source not found, creating placeholder"
        echo "#!/bin/bash" > "$novaagent_target/nova-agent"
        echo "echo 'NovaAgent placeholder - executable will be provided separately'" >> "$novaagent_target/nova-agent"
        chmod +x "$novaagent_target/nova-agent"
    fi
    
    novalift_success "NovaAgent installed"
}

# Install NovaBridge
install_novabridge() {
    novalift_info "Installing NovaBridge API layer..."
    
    local novabridge_source="aeonix-divine-api"
    local novabridge_target="$INSTALL_PATH/NovaBridge"
    
    if [[ -d "$novabridge_source" ]]; then
        cp -r "$novabridge_source"/* "$novabridge_target/"
        novalift_success "NovaBridge installed"
    else
        novalift_warning "NovaBridge source not found, creating placeholder"
        echo "# NovaBridge placeholder - API layer will be installed separately" > "$novabridge_target/README.md"
    fi
    
    # Install Python dependencies
    if [[ -f "$novabridge_target/requirements.txt" ]]; then
        pip3 install --user -r "$novabridge_target/requirements.txt"
        novalift_success "NovaBridge dependencies installed"
    fi
}

# Install NovaConsole
install_novaconsole() {
    novalift_info "Installing NovaFuse Platform Console..."
    
    local novaconsole_source="chaeonix-divine-dashboard"
    local novaconsole_target="$INSTALL_PATH/NovaConsole"
    
    if [[ -d "$novaconsole_source" ]]; then
        cp -r "$novaconsole_source"/* "$novaconsole_target/"
        novalift_success "NovaConsole installed"
    else
        novalift_warning "NovaConsole source not found, creating placeholder"
        echo "# NovaConsole placeholder - dashboard will be installed separately" > "$novaconsole_target/README.md"
    fi
    
    # Install npm dependencies
    if [[ -f "$novaconsole_target/package.json" ]]; then
        cd "$novaconsole_target"
        npm install
        cd - >/dev/null
        novalift_success "NovaConsole dependencies installed"
    fi
}

# Create configuration files
create_configuration() {
    novalift_info "Creating NovaFuse configuration..."
    
    local config_path="$INSTALL_PATH/Config"
    
    # Main configuration
    cat > "$config_path/novafuse-config.json" << EOF
{
  "novafuse": {
    "version": "$NOVALIFT_VERSION",
    "install_path": "$INSTALL_PATH",
    "mode": "$MODE",
    "components": {
      "novacore": {
        "enabled": true,
        "path": "$INSTALL_PATH/NovaCore",
        "port": 8000
      },
      "novaagent": {
        "enabled": true,
        "path": "$INSTALL_PATH/NovaAgent",
        "executable": "nova-agent"
      },
      "novabridge": {
        "enabled": true,
        "path": "$INSTALL_PATH/NovaBridge",
        "port": 8001
      },
      "novaconsole": {
        "enabled": true,
        "path": "$INSTALL_PATH/NovaConsole",
        "port": 3000
      }
    }
  }
}
EOF
    
    # NovaLift configuration
    cat > "$config_path/novalift-config.json" << EOF
{
  "novalift": {
    "version": "$NOVALIFT_VERSION",
    "mode": "$MODE",
    "auto_start": true,
    "monitoring": {
      "enabled": true,
      "interval": 30,
      "health_checks": true
    },
    "logging": {
      "level": "INFO",
      "path": "$INSTALL_PATH/Logs",
      "max_size": "100MB"
    }
  }
}
EOF
    
    novalift_success "Configuration files created"
}

# Create systemd service
create_service() {
    novalift_info "Creating NovaFuse systemd service..."
    
    local service_file="/etc/systemd/system/novafuse.service"
    
    sudo tee "$service_file" > /dev/null << EOF
[Unit]
Description=NovaFuse Coherence Operating System
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$INSTALL_PATH/NovaAgent
ExecStart=$INSTALL_PATH/NovaAgent/nova-agent
Restart=always
RestartSec=10
Environment=NOVA_INSTALL_PATH=$INSTALL_PATH
Environment=NOVA_MODE=$MODE

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable novafuse
    
    novalift_success "Systemd service created and enabled"
}

# Create desktop shortcuts (if GUI available)
create_shortcuts() {
    if [[ -n "${DISPLAY:-}" ]] && [[ -d "$HOME/Desktop" ]]; then
        novalift_info "Creating desktop shortcuts..."
        
        cat > "$HOME/Desktop/NovaFuse Console.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Link
Name=NovaFuse Console
Comment=NovaFuse Platform Console
URL=http://localhost:3000
Icon=web-browser
EOF
        
        chmod +x "$HOME/Desktop/NovaFuse Console.desktop"
        novalift_success "Desktop shortcuts created"
    fi
}

# Main installation function
install_novalift() {
    show_banner
    
    novalift_info "Starting NovaLift installation..."
    novalift_info "Install Path: $INSTALL_PATH"
    novalift_info "Mode: $MODE"
    
    # Detect OS
    detect_os
    
    # Check prerequisites
    if ! check_prerequisites; then
        novalift_error "Prerequisites check failed. Installation aborted."
        exit 1
    fi
    
    # Install dependencies
    install_dependencies
    
    # Create directories
    create_directories
    
    # Install components
    install_novacore
    install_novaagent
    install_novabridge
    install_novaconsole
    
    # Create configuration
    create_configuration
    
    # Create service
    create_service
    
    # Create shortcuts
    create_shortcuts
    
    novalift_success "NovaLift installation completed successfully!"
    echo ""
    echo -e "\033[1;33m🎯 Next Steps:\033[0m"
    echo "  1. Start NovaFuse: sudo systemctl start novafuse"
    echo "  2. Check status: sudo systemctl status novafuse"
    echo "  3. View logs: journalctl -u novafuse -f"
    echo "  4. Open console: http://localhost:3000"
    echo ""
    echo -e "\033[1;32m🚀 NovaFuse Coherence Operating System is ready!\033[0m"
    
    return 0
}

# Uninstall function
uninstall_novalift() {
    novalift_info "Uninstalling NovaLift..."
    
    # Stop and disable service
    sudo systemctl stop novafuse 2>/dev/null || true
    sudo systemctl disable novafuse 2>/dev/null || true
    sudo rm -f /etc/systemd/system/novafuse.service
    sudo systemctl daemon-reload
    
    # Remove installation directory
    if [[ -d "$INSTALL_PATH" ]]; then
        sudo rm -rf "$INSTALL_PATH"
        novalift_success "Installation directory removed"
    fi
    
    # Remove shortcuts
    rm -f "$HOME/Desktop/NovaFuse Console.desktop" 2>/dev/null || true
    
    novalift_success "NovaLift uninstalled successfully"
}

# Command line argument parsing
case "${1:-install}" in
    "install")
        install_novalift
        ;;
    "uninstall")
        uninstall_novalift
        ;;
    "--help"|"-h")
        echo "Usage: $0 [install|uninstall] [install_path] [mode] [--skip-deps]"
        echo ""
        echo "Arguments:"
        echo "  install_path    Installation directory (default: /opt/novafuse)"
        echo "  mode           Operating mode (default: Enterprise)"
        echo "  --skip-deps    Skip dependency installation"
        echo ""
        echo "Examples:"
        echo "  $0 install /opt/novafuse Enterprise"
        echo "  $0 install /home/<USER>/novafuse Development --skip-deps"
        echo "  $0 uninstall"
        ;;
    *)
        novalift_error "Unknown command: $1"
        echo "Use '$0 --help' for usage information"
        exit 1
        ;;
esac
