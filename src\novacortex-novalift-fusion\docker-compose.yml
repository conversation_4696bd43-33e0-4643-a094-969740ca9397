version: '3.8'

services:
  # NovaCortex Consciousness Testing Server
  novacortex:
    build:
      context: ../novacortex
      dockerfile: Dockerfile
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=fusion
      - PORT=3010
      - MONGODB_URI=mongodb://mongodb:27017/novacortex-fusion
      - REDIS_URI=redis://redis:6379/0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - fusion-network

  # NovaCortex-NovaLift Fusion Server
  fusion-server:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3015:3015"
    environment:
      - NODE_ENV=production
      - PORT=3015
      - NOVACORTEX_URL=http://novacortex:3010
      - CONSCIOUSNESS_MODE=true
      - ETHICAL_CONSTRAINTS=true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3015/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    depends_on:
      novacortex:
        condition: service_healthy
    networks:
      - fusion-network

  # MongoDB for data persistence
  mongodb:
    image: mongo:6.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=fusion123
      - MONGO_INITDB_DATABASE=novacortex-fusion
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - fusion-network

  # Redis for caching and pub/sub
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - fusion-network

  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - fusion-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=fusion123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
      - ./grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - prometheus
    networks:
      - fusion-network

volumes:
  mongodb_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  fusion-network:
    driver: bridge
