#!/usr/bin/env node

/**
 * NovaFuse Test API Server
 * Integrates with NovaConnect to provide real test execution capabilities
 * Bridges the test dashboard with actual test execution through NovaConnect
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const path = require('path');

// Import NovaConnect and Service Connectors
const connectorApi = require('./nova-connect/api/connector-api');
const TestServiceConnector = require('./nova-connect/connectors/test-service-connector');
const DeploymentServiceConnector = require('./nova-connect/connectors/deployment-service-connector');
const DemoExecutionConnector = require('./nova-connect/connectors/demo-execution-connector');
const DocumentationServiceConnector = require('./nova-connect/connectors/documentation-service-connector');

class NovaFuseTestAPIServer {
    constructor(options = {}) {
        this.options = {
            port: options.port || 3100,
            corsOrigin: options.corsOrigin || "*",
            enableWebSocket: options.enableWebSocket !== false,
            testRootPath: options.testRootPath || process.cwd(),
            ...options
        };
        
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = this.options.enableWebSocket ? socketIo(this.server, {
            cors: {
                origin: this.options.corsOrigin,
                methods: ["GET", "POST"]
            }
        }) : null;
        
        this.novaConnect = null;
        this.testConnector = null;
        this.deploymentConnector = null;
        this.demoConnector = null;
        this.docsConnector = null;
        this.connectedClients = new Set();
        
        this.setupMiddleware();
        this.setupRoutes();
        if (this.io) {
            this.setupWebSocket();
        }
    }
    
    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        this.app.use(cors({
            origin: this.options.corsOrigin
        }));
        this.app.use(express.json());
        this.app.use(express.urlencoded({ extended: true }));
        
        // Serve static files (test dashboard)
        this.app.use('/dashboard', express.static(path.join(__dirname, 'public')));
        
        // Request logging
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });
    }
    
    /**
     * Setup API routes
     */
    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                service: 'novafuse-test-api',
                timestamp: new Date().toISOString(),
                novaConnect: this.novaConnect ? 'connected' : 'disconnected',
                testConnector: this.testConnector ? 'registered' : 'not-registered',
                deploymentConnector: this.deploymentConnector ? 'registered' : 'not-registered',
                demoConnector: this.demoConnector ? 'registered' : 'not-registered',
                docsConnector: this.docsConnector ? 'registered' : 'not-registered'
            });
        });
        
        // Test discovery endpoint
        this.app.get('/api/tests/discover', async (req, res) => {
            try {
                const { category, includeMetadata } = req.query;
                
                const result = await this.executeNovaConnectRequest('novafuse-test-service', 'discover-tests', {
                    category,
                    includeMetadata: includeMetadata === 'true'
                });
                
                res.json({
                    success: true,
                    data: result
                });
                
            } catch (error) {
                console.error('Test discovery error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
        
        // Test execution endpoint
        this.app.post('/api/tests/run', async (req, res) => {
            try {
                const { categories, files, options } = req.body;
                const requestId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                
                const result = await this.executeNovaConnectRequest('novafuse-test-service', 'run-tests', {
                    categories: categories || [],
                    files: files || [],
                    options: options || {},
                    requestId
                });
                
                res.json({
                    success: true,
                    data: result
                });
                
            } catch (error) {
                console.error('Test execution error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
        
        // Test status endpoint
        this.app.get('/api/tests/status/:executionId?', async (req, res) => {
            try {
                const { executionId } = req.params;
                
                const result = await this.executeNovaConnectRequest('novafuse-test-service', 'test-status', {
                    executionId
                });
                
                res.json({
                    success: true,
                    data: result
                });
                
            } catch (error) {
                console.error('Test status error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
        
        // Test results endpoint
        this.app.get('/api/tests/results/:executionId?', async (req, res) => {
            try {
                const { executionId } = req.params;
                const { limit, offset, status, category, startDate, endDate } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-test-service', 'test-results', {
                    executionId,
                    limit: limit ? parseInt(limit) : undefined,
                    offset: offset ? parseInt(offset) : undefined,
                    status,
                    category,
                    startDate,
                    endDate
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Test results error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Test analytics endpoint
        this.app.get('/api/tests/analytics', async (req, res) => {
            try {
                const { days, category } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-test-service', 'test-analytics', {
                    days: days ? parseInt(days) : undefined,
                    category
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Test analytics error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
        
        // Stop tests endpoint
        this.app.post('/api/tests/stop/:executionId?', async (req, res) => {
            try {
                const { executionId } = req.params;

                const result = await this.executeNovaConnectRequest('novafuse-test-service', 'stop-tests', {
                    executionId
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Stop tests error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // ===== DEPLOYMENT API ENDPOINTS =====

        // Deploy services endpoint
        this.app.post('/api/deployment/deploy', async (req, res) => {
            try {
                const { categories, services, options } = req.body;

                const result = await this.executeNovaConnectRequest('novafuse-deployment-service', 'deploy-services', {
                    categories: categories || [],
                    services: services || [],
                    options: options || {}
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Deployment error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Stop services endpoint
        this.app.post('/api/deployment/stop', async (req, res) => {
            try {
                const { categories, services } = req.body;

                const result = await this.executeNovaConnectRequest('novafuse-deployment-service', 'stop-services', {
                    categories: categories || [],
                    services: services || []
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Stop services error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Service status endpoint
        this.app.get('/api/deployment/status', async (req, res) => {
            try {
                const { services, detailed } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-deployment-service', 'service-status', {
                    services: services ? services.split(',') : [],
                    detailed: detailed === 'true'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Service status error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Deployment logs endpoint
        this.app.get('/api/deployment/logs/:service?', async (req, res) => {
            try {
                const { service } = req.params;
                const { lines } = req.query;

                const services = service ? [service] : [];

                const result = await this.executeNovaConnectRequest('novafuse-deployment-service', 'deployment-logs', {
                    services,
                    lines: lines ? parseInt(lines) : 100
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Deployment logs error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Health check endpoint
        this.app.get('/api/deployment/health', async (req, res) => {
            try {
                const { services } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-deployment-service', 'health-check', {
                    services: services ? services.split(',') : []
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Health check error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Scale services endpoint
        this.app.post('/api/deployment/scale', async (req, res) => {
            try {
                const { services, options } = req.body;

                const result = await this.executeNovaConnectRequest('novafuse-deployment-service', 'scale-services', {
                    services: services || {},
                    options: options || {}
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Scale services error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // ===== DEMO EXECUTION API ENDPOINTS =====

        // Discover demos endpoint
        this.app.get('/api/demos/discover', async (req, res) => {
            try {
                const { category, includeMetadata } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-demo-execution', 'discover-demos', {
                    category,
                    includeMetadata: includeMetadata === 'true'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Demo discovery error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Launch demo endpoint
        this.app.post('/api/demos/launch', async (req, res) => {
            try {
                const { category, demoName, demoFile, options } = req.body;

                const result = await this.executeNovaConnectRequest('novafuse-demo-execution', 'launch-demo', {
                    category,
                    demoName,
                    demoFile,
                    options: options || {}
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Demo launch error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Demo status endpoint
        this.app.get('/api/demos/status/:executionId?', async (req, res) => {
            try {
                const { executionId } = req.params;

                const result = await this.executeNovaConnectRequest('novafuse-demo-execution', 'demo-status', {
                    executionId
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Demo status error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Demo results endpoint
        this.app.get('/api/demos/results/:executionId?', async (req, res) => {
            try {
                const { executionId } = req.params;
                const { includeOutput } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-demo-execution', 'demo-results', {
                    executionId,
                    includeOutput: includeOutput === 'true'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Demo results error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Stop demo endpoint
        this.app.post('/api/demos/stop/:executionId', async (req, res) => {
            try {
                const { executionId } = req.params;

                const result = await this.executeNovaConnectRequest('novafuse-demo-execution', 'stop-demo', {
                    executionId
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Stop demo error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Demo recommendations endpoint
        this.app.get('/api/demos/recommendations', async (req, res) => {
            try {
                const { userInterests, previousDemos, difficulty } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-demo-execution', 'demo-recommendations', {
                    userInterests: userInterests ? userInterests.split(',') : [],
                    previousDemos: previousDemos ? previousDemos.split(',') : [],
                    difficulty: difficulty || 'intermediate'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Demo recommendations error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // ===== DOCUMENTATION API ENDPOINTS =====

        // Discover documents endpoint
        this.app.get('/api/docs/discover', async (req, res) => {
            try {
                const { category, includeContent, forceRefresh } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-documentation-service', 'discover-documents', {
                    category,
                    includeContent: includeContent === 'true',
                    forceRefresh: forceRefresh === 'true'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Document discovery error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Search documents endpoint
        this.app.get('/api/docs/search', async (req, res) => {
            try {
                const { query, category, type, limit, includeContent } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-documentation-service', 'search-documents', {
                    query,
                    category,
                    type,
                    limit: limit ? parseInt(limit) : undefined,
                    includeContent: includeContent === 'true'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Document search error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Get document content endpoint
        this.app.get('/api/docs/content', async (req, res) => {
            try {
                const { path: docPath, includeMetadata } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-documentation-service', 'get-document', {
                    path: docPath,
                    includeMetadata: includeMetadata !== 'false'
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Get document error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Index documents endpoint
        this.app.post('/api/docs/index', async (req, res) => {
            try {
                const result = await this.executeNovaConnectRequest('novafuse-documentation-service', 'index-documents', {});

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Document indexing error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Document statistics endpoint
        this.app.get('/api/docs/stats', async (req, res) => {
            try {
                const result = await this.executeNovaConnectRequest('novafuse-documentation-service', 'document-stats', {});

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Document stats error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // Related documents endpoint
        this.app.get('/api/docs/related', async (req, res) => {
            try {
                const { path: docPath, limit } = req.query;

                const result = await this.executeNovaConnectRequest('novafuse-documentation-service', 'related-documents', {
                    path: docPath,
                    limit: limit ? parseInt(limit) : undefined
                });

                res.json({
                    success: true,
                    data: result
                });

            } catch (error) {
                console.error('Related documents error:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
        
        // Serve test dashboard
        this.app.get('/', (req, res) => {
            res.redirect('/dashboard');
        });
        
        this.app.get('/dashboard', (req, res) => {
            res.sendFile(path.join(__dirname, 'novafuse-test-dashboard.html'));
        });

        this.app.get('/report', (req, res) => {
            res.sendFile(path.join(__dirname, 'novafuse-test-report-dynamic.html'));
        });

        this.app.get('/deployment', (req, res) => {
            res.sendFile(path.join(__dirname, 'novafuse-deployment-dashboard.html'));
        });

        this.app.get('/demos', (req, res) => {
            res.sendFile(path.join(__dirname, 'novafuse-demo-selector.html'));
        });

        this.app.get('/docs', (req, res) => {
            res.sendFile(path.join(__dirname, 'novafuse-docs-portal.html'));
        });
    }
    
    /**
     * Setup WebSocket for real-time updates
     */
    setupWebSocket() {
        this.io.on('connection', (socket) => {
            console.log(`Client connected: ${socket.id}`);
            this.connectedClients.add(socket);
            
            // Send initial status
            socket.emit('server-status', {
                connected: true,
                novaConnect: this.novaConnect ? 'connected' : 'disconnected',
                testConnector: this.testConnector ? 'registered' : 'not-registered',
                deploymentConnector: this.deploymentConnector ? 'registered' : 'not-registered',
                demoConnector: this.demoConnector ? 'registered' : 'not-registered',
                docsConnector: this.docsConnector ? 'registered' : 'not-registered'
            });
            
            socket.on('disconnect', () => {
                console.log(`Client disconnected: ${socket.id}`);
                this.connectedClients.delete(socket);
            });
            
            // Handle test execution requests from WebSocket
            socket.on('run-tests', async (data) => {
                try {
                    const result = await this.executeNovaConnectRequest('novafuse-test-service', 'run-tests', data);
                    socket.emit('test-execution-started', result);
                } catch (error) {
                    socket.emit('test-execution-error', { error: error.message });
                }
            });
            
            socket.on('get-test-status', async (data) => {
                try {
                    const result = await this.executeNovaConnectRequest('novafuse-test-service', 'test-status', data);
                    socket.emit('test-status-update', result);
                } catch (error) {
                    socket.emit('test-status-error', { error: error.message });
                }
            });
        });
    }
    
    /**
     * Execute a request through NovaConnect
     */
    async executeNovaConnectRequest(connectorId, endpointId, parameters) {
        if (!this.novaConnect) {
            throw new Error('NovaConnect not initialized');
        }
        
        return new Promise((resolve, reject) => {
            const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const timeout = setTimeout(() => {
                reject(new Error('Request timeout'));
            }, 30000); // 30 second timeout
            
            // Listen for response
            const responseHandler = (event) => {
                if (event.requestId === requestId) {
                    clearTimeout(timeout);
                    this.novaConnect.off('connector.response', responseHandler);
                    
                    if (event.success) {
                        resolve(event.data);
                    } else {
                        reject(new Error(event.error.message));
                    }
                }
            };
            
            this.novaConnect.on('connector.response', responseHandler);
            
            // Send request
            this.novaConnect.emit('connector.execute', {
                connectorId,
                endpointId,
                parameters,
                requestId
            });
        });
    }
    
    /**
     * Broadcast real-time updates to connected clients
     */
    broadcastUpdate(event, data) {
        if (this.io) {
            this.io.emit(event, data);
        }
    }
    
    /**
     * Initialize NovaConnect and Test Service Connector
     */
    async initializeNovaConnect() {
        try {
            // Initialize NovaConnect using existing API
            this.novaConnect = new MockNovaConnect();
            await this.novaConnect.initialize();
            
            // Initialize Test Service Connector
            this.testConnector = new TestServiceConnector(this.novaConnect, {
                testRootPath: this.options.testRootPath,
                enableRealTimeUpdates: true
            });

            // Initialize Deployment Service Connector
            this.deploymentConnector = new DeploymentServiceConnector(this.novaConnect, {
                dockerComposePath: './docker-compose.unified.yml',
                enableRealTimeUpdates: true
            });

            // Initialize Demo Execution Connector
            this.demoConnector = new DemoExecutionConnector(this.novaConnect, {
                demoRootPath: this.options.testRootPath,
                enableRealTimeUpdates: true
            });

            // Initialize Documentation Service Connector
            this.docsConnector = new DocumentationServiceConnector(this.novaConnect, {
                docsRootPath: './imported-docs',
                enableRealTimeUpdates: true
            });
            
            // Set up real-time event forwarding
            this.testConnector.on('testStarted', (data) => {
                this.broadcastUpdate('test-started', data);
            });
            
            this.testConnector.on('testProgress', (data) => {
                this.broadcastUpdate('test-progress', data);
            });
            
            this.testConnector.on('testCompleted', (data) => {
                this.broadcastUpdate('test-completed', data);
            });
            
            this.testConnector.on('testFailed', (data) => {
                this.broadcastUpdate('test-failed', data);
            });

            // Set up deployment event forwarding
            this.deploymentConnector.on('deploymentStarted', (data) => {
                this.broadcastUpdate('deployment-started', data);
            });

            this.deploymentConnector.on('deploymentProgress', (data) => {
                this.broadcastUpdate('deployment-progress', data);
            });

            this.deploymentConnector.on('deploymentCompleted', (data) => {
                this.broadcastUpdate('deployment-completed', data);
            });

            this.deploymentConnector.on('deploymentFailed', (data) => {
                this.broadcastUpdate('deployment-failed', data);
            });

            this.deploymentConnector.on('serviceHealthChanged', (data) => {
                this.broadcastUpdate('service-health-changed', data);
            });

            // Set up demo event forwarding
            this.demoConnector.on('demoStarted', (data) => {
                this.broadcastUpdate('demo-started', data);
            });

            this.demoConnector.on('demoProgress', (data) => {
                this.broadcastUpdate('demo-progress', data);
            });

            this.demoConnector.on('demoOutput', (data) => {
                this.broadcastUpdate('demo-output', data);
            });

            this.demoConnector.on('demoCompleted', (data) => {
                this.broadcastUpdate('demo-completed', data);
            });

            this.demoConnector.on('demoFailed', (data) => {
                this.broadcastUpdate('demo-failed', data);
            });

            // Set up documentation event forwarding
            this.docsConnector.on('docsIndexed', (data) => {
                this.broadcastUpdate('docs-indexed', data);
            });

            this.docsConnector.on('searchCompleted', (data) => {
                this.broadcastUpdate('docs-search-completed', data);
            });

            console.log('✅ NovaConnect and ALL Service Connectors initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize NovaConnect:', error);
            throw error;
        }
    }
    
    /**
     * Start the server
     */
    async start() {
        try {
            // Initialize NovaConnect first
            await this.initializeNovaConnect();
            
            // Start the server
            this.server.listen(this.options.port, () => {
                console.log(`🚀 NovaFuse Test API Server running on port ${this.options.port}`);
                console.log(`📊 Test Dashboard: http://localhost:${this.options.port}/dashboard`);
                console.log(`🔗 API Endpoint: http://localhost:${this.options.port}/api`);
                console.log(`⚡ WebSocket: ${this.options.enableWebSocket ? 'Enabled' : 'Disabled'}`);
            });
            
        } catch (error) {
            console.error('Failed to start NovaFuse Test API Server:', error);
            process.exit(1);
        }
    }
    
    /**
     * Stop the server
     */
    async stop() {
        if (this.server) {
            this.server.close();
            console.log('NovaFuse Test API Server stopped');
        }
    }
}

/**
 * Mock NovaConnect implementation for development
 * In production, this would be replaced with the actual NovaConnect instance
 */
class MockNovaConnect {
    constructor() {
        this.connectors = new Map();
        this.eventHandlers = new Map();
    }
    
    async initialize() {
        console.log('Mock NovaConnect initialized');
    }
    
    async registerConnector(config) {
        this.connectors.set(config.id, config);
        console.log(`Registered connector: ${config.name}`);
    }
    
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }
}

// CLI interface
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        console.log(`
NovaFuse Test API Server

Usage:
  node novafuse-test-api-server.js [options]

Options:
  --port <number>        Server port (default: 3100)
  --no-websocket        Disable WebSocket support
  --test-root <path>    Root path for test discovery
  --help, -h            Show this help message

Examples:
  node novafuse-test-api-server.js
  node novafuse-test-api-server.js --port 3200
  node novafuse-test-api-server.js --test-root /path/to/tests
        `);
        process.exit(0);
    }
    
    const options = {};
    
    // Parse command line arguments
    for (let i = 0; i < args.length; i++) {
        switch (args[i]) {
            case '--port':
                options.port = parseInt(args[++i]);
                break;
            case '--no-websocket':
                options.enableWebSocket = false;
                break;
            case '--test-root':
                options.testRootPath = args[++i];
                break;
        }
    }
    
    const server = new NovaFuseTestAPIServer(options);
    server.start();
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\nShutting down gracefully...');
        await server.stop();
        process.exit(0);
    });
}

module.exports = NovaFuseTestAPIServer;
