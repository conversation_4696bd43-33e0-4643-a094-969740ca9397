/**
 * NovaPulse+ - Universal Regulatory Change Management (NURC)
 * 
 * This module provides regulatory change management capabilities with
 * predictive impact analysis.
 */

const { createLogger } = require('../utils/logger');
const regulatoryChangeService = require('./services/regulatory-change-service');
const impactAnalysisService = require('./services/impact-analysis-service');
const notificationService = require('./services/notification-service');

const logger = createLogger('novapulse');

/**
 * NovaPulse+ class for regulatory change management
 */
class NovaPulse {
  constructor(options = {}) {
    this.options = {
      autoAnalysis: options.autoAnalysis !== false,
      notificationEnabled: options.notificationEnabled !== false,
      priorityThreshold: options.priorityThreshold || 'medium',
      ...options
    };

    this.regulatoryChangeService = regulatoryChangeService;
    this.impactAnalysisService = impactAnalysisService;
    this.notificationService = notificationService;

    logger.info('NovaPulse+ initialized', {
      autoAnalysis: this.options.autoAnalysis,
      notificationEnabled: this.options.notificationEnabled,
      priorityThreshold: this.options.priorityThreshold
    });
  }

  /**
   * Track regulatory change
   * 
   * @param {Object} changeData - Regulatory change data
   * @returns {Promise<Object>} - Tracked regulatory change
   */
  async trackRegulatoryChange(changeData) {
    logger.info('Tracking regulatory change', { 
      regulation: changeData.regulation,
      title: changeData.title
    });
    
    try {
      // Create regulatory change
      const regulatoryChange = await this.regulatoryChangeService.createRegulatoryChange(changeData);
      
      // Perform impact analysis if auto-analysis is enabled
      if (this.options.autoAnalysis) {
        await this.analyzeRegulatoryChange(regulatoryChange.id);
      }
      
      return regulatoryChange;
    } catch (error) {
      logger.error('Error tracking regulatory change', {
        regulation: changeData.regulation,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Analyze regulatory change impact
   * 
   * @param {string} changeId - Regulatory change ID
   * @returns {Promise<Object>} - Impact analysis result
   */
  async analyzeRegulatoryChange(changeId) {
    logger.info('Analyzing regulatory change impact', { changeId });
    
    try {
      // Get regulatory change
      const regulatoryChange = await this.regulatoryChangeService.getRegulatoryChange(changeId);
      
      // Perform impact analysis
      const impactAnalysis = await this.impactAnalysisService.analyzeImpact(regulatoryChange);
      
      // Send notifications if enabled and priority meets threshold
      if (this.options.notificationEnabled && 
          this.isPriorityAboveThreshold(impactAnalysis.priority)) {
        await this.notificationService.sendImpactNotification(regulatoryChange, impactAnalysis);
      }
      
      return impactAnalysis;
    } catch (error) {
      logger.error('Error analyzing regulatory change impact', {
        changeId,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Check if priority is above threshold
   * 
   * @param {string} priority - Priority level
   * @returns {boolean} - Whether priority is above threshold
   * @private
   */
  isPriorityAboveThreshold(priority) {
    const priorityLevels = {
      'critical': 4,
      'high': 3,
      'medium': 2,
      'low': 1
    };
    
    return priorityLevels[priority] >= priorityLevels[this.options.priorityThreshold];
  }

  /**
   * Get regulatory change dashboard
   * 
   * @returns {Promise<Object>} - Dashboard data
   */
  async getRegulatoryChangeDashboard() {
    logger.info('Getting regulatory change dashboard');
    
    try {
      // Get all regulatory changes
      const regulatoryChanges = await this.regulatoryChangeService.getAllRegulatoryChanges();
      
      // Get impact analyses
      const impactAnalyses = await Promise.all(
        regulatoryChanges.map(change => 
          this.impactAnalysisService.getLatestImpactAnalysis(change.id)
        )
      );
      
      // Calculate dashboard metrics
      const criticalCount = impactAnalyses.filter(a => a && a.priority === 'critical').length;
      const highCount = impactAnalyses.filter(a => a && a.priority === 'high').length;
      const mediumCount = impactAnalyses.filter(a => a && a.priority === 'medium').length;
      const lowCount = impactAnalyses.filter(a => a && a.priority === 'low').length;
      
      // Group by regulation
      const regulationGroups = {};
      regulatoryChanges.forEach(change => {
        if (!regulationGroups[change.regulation]) {
          regulationGroups[change.regulation] = 0;
        }
        regulationGroups[change.regulation]++;
      });
      
      // Group by status
      const statusGroups = {};
      regulatoryChanges.forEach(change => {
        if (!statusGroups[change.status]) {
          statusGroups[change.status] = 0;
        }
        statusGroups[change.status]++;
      });
      
      return {
        totalChanges: regulatoryChanges.length,
        impactDistribution: {
          critical: criticalCount,
          high: highCount,
          medium: mediumCount,
          low: lowCount
        },
        regulationDistribution: regulationGroups,
        statusDistribution: statusGroups,
        upcomingDeadlines: regulatoryChanges
          .filter(change => change.deadline && new Date(change.deadline) > new Date())
          .sort((a, b) => new Date(a.deadline) - new Date(b.deadline))
          .slice(0, 5)
          .map(change => ({
            id: change.id,
            title: change.title,
            regulation: change.regulation,
            deadline: change.deadline,
            status: change.status
          }))
      };
    } catch (error) {
      logger.error('Error getting regulatory change dashboard', {
        error: error.message
      });
      throw error;
    }
  }
}

// Create singleton instance
const novaPulse = new NovaPulse();

module.exports = {
  NovaPulse,
  novaPulse,
  regulatoryChangeService,
  impactAnalysisService,
  notificationService
};
