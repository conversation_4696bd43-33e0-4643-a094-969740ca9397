/**
 * Animated Component
 * 
 * A component for adding animations to any element.
 */

import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { useAnimation } from '../animation/useAnimation';

/**
 * Animated component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string|Object} props.animation - Animation name or configuration
 * @param {boolean} [props.autoPlay=true] - Whether to play the animation automatically
 * @param {Function} [props.onStart] - Callback when animation starts
 * @param {Function} [props.onFinish] - Callback when animation finishes
 * @param {Function} [props.onCancel] - Callback when animation is cancelled
 * @param {string} [props.as='div'] - Element type
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} Animated component
 */
const Animated = forwardRef(({
  children,
  animation,
  autoPlay = true,
  onStart,
  onFinish,
  onCancel,
  as: Component = 'div',
  className = '',
  style = {},
  ...rest
}, externalRef) => {
  const {
    ref,
    play,
    pause,
    resume,
    cancel,
    finish,
    reverse,
    setPlaybackRate,
    status,
    progress
  } = useAnimation(animation, {
    autoPlay,
    onStart,
    onFinish,
    onCancel
  });
  
  // Combine refs
  const setRefs = (element) => {
    ref.current = element;
    
    if (externalRef) {
      if (typeof externalRef === 'function') {
        externalRef(element);
      } else {
        externalRef.current = element;
      }
    }
  };
  
  return (
    <Component
      ref={setRefs}
      className={className}
      style={style}
      data-animation-status={status}
      data-animation-progress={progress}
      {...rest}
    >
      {children}
    </Component>
  );
});

Animated.displayName = 'Animated';

Animated.propTypes = {
  children: PropTypes.node,
  animation: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]).isRequired,
  autoPlay: PropTypes.bool,
  onStart: PropTypes.func,
  onFinish: PropTypes.func,
  onCancel: PropTypes.func,
  as: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object
};

export default Animated;
