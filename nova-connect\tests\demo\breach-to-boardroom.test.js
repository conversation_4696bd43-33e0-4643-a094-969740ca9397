/**
 * NovaConnect "Breach to Boardroom" Demo Test
 * 
 * This test simulates the demo scenario described in the strategic document:
 * 1. Simulate PHI leak via misconfigured BigQuery dataset
 * 2. Auto-containment in under 8 seconds
 * 3. Boardroom-ready reporting with compliance score maintained at 99.9%
 */

const { TransformationEngine } = require('../../src/engines/transformation-engine');
const { RemediationEngine } = require('../../src/engines/remediation-engine');
const { SCCConnector } = require('../../src/connectors/gcp/scc-connector');
const { EncryptionService } = require('../../src/security/encryption-service');

// Mock GCP services
const mockBigQueryService = {
  getDataset: jest.fn(),
  updateDataset: jest.fn(),
  encryptDataset: jest.fn(),
  getAccessLogs: jest.fn()
};

// Mock Looker service
const mockLookerService = {
  updateDashboard: jest.fn(),
  getComplianceScore: jest.fn()
};

describe('Breach to Boardroom Demo', () => {
  let transformationEngine;
  let remediationEngine;
  let sccConnector;
  let encryptionService;
  
  beforeAll(async () => {
    // Initialize engines and services
    transformationEngine = new TransformationEngine();
    remediationEngine = new RemediationEngine();
    sccConnector = new SCCConnector();
    encryptionService = new EncryptionService();
    
    // Register remediation actions
    remediationEngine.registerAction('encrypt-dataset', async ({ parameters }) => {
      const { datasetId, projectId } = parameters;
      await mockBigQueryService.encryptDataset(projectId, datasetId);
      return { success: true, datasetId, projectId };
    });
    
    remediationEngine.registerAction('update-access-controls', async ({ parameters }) => {
      const { datasetId, projectId, accessLevel } = parameters;
      const dataset = await mockBigQueryService.getDataset(projectId, datasetId);
      dataset.access = accessLevel;
      await mockBigQueryService.updateDataset(projectId, datasetId, dataset);
      return { success: true, datasetId, projectId };
    });
    
    remediationEngine.registerAction('update-compliance-dashboard', async ({ parameters }) => {
      const { dashboardId } = parameters;
      await mockLookerService.updateDashboard(dashboardId);
      return { success: true, dashboardId };
    });
  });
  
  it('should detect and remediate PHI leak in under 8 seconds', async () => {
    // 1. Simulate the finding from SCC
    const finding = {
      name: 'organizations/123/sources/456/findings/phi-leak-789',
      category: 'DATA_LEAK',
      severity: 'HIGH',
      resourceName: 'projects/healthcare-demo/datasets/patient_records',
      state: 'ACTIVE',
      eventTime: new Date().toISOString(),
      createTime: new Date().toISOString(),
      sourceProperties: {
        finding_type: 'Sensitive Data Exposure',
        finding_description: 'PHI data exposed in BigQuery dataset',
        data_type: 'PHI',
        compliance_frameworks: ['HIPAA', 'GDPR']
      }
    };
    
    // 2. Normalize the finding
    const startTime = Date.now();
    
    const normalizedFinding = {
      id: 'phi-leak-789',
      type: 'data_leak',
      severity: 'high',
      resourceName: 'projects/healthcare-demo/datasets/patient_records',
      resourceType: 'bigquery.dataset',
      createdAt: new Date().getTime(),
      description: 'PHI data exposed in BigQuery dataset',
      dataType: 'PHI',
      complianceFrameworks: ['HIPAA', 'GDPR']
    };
    
    // 3. Create remediation scenario
    const remediationScenario = {
      id: 'phi-leak-remediation-789',
      type: 'compliance',
      framework: 'HIPAA',
      control: '164.312(a)(1)',
      severity: 'high',
      resource: {
        id: 'patient_records',
        type: 'bigquery.dataset',
        name: 'patient_records',
        provider: 'gcp',
        projectId: 'healthcare-demo'
      },
      finding: normalizedFinding,
      remediationSteps: [
        {
          id: 'step-1',
          action: 'encrypt-dataset',
          parameters: {
            projectId: 'healthcare-demo',
            datasetId: 'patient_records',
            encryptionType: 'AES-256',
            keyRotationPeriod: '90d'
          }
        },
        {
          id: 'step-2',
          action: 'update-access-controls',
          parameters: {
            projectId: 'healthcare-demo',
            datasetId: 'patient_records',
            accessLevel: 'restricted',
            allowedRoles: ['healthcare-admin', 'compliance-officer']
          }
        },
        {
          id: 'step-3',
          action: 'update-compliance-dashboard',
          parameters: {
            dashboardId: 'hipaa-compliance-dashboard',
            findingId: 'phi-leak-789',
            remediationId: 'phi-leak-remediation-789'
          }
        }
      ]
    };
    
    // Mock service responses
    mockBigQueryService.getDataset.mockResolvedValue({
      id: 'patient_records',
      access: 'public'
    });
    
    mockBigQueryService.updateDataset.mockResolvedValue({
      id: 'patient_records',
      access: 'restricted'
    });
    
    mockBigQueryService.encryptDataset.mockResolvedValue({
      id: 'patient_records',
      encrypted: true
    });
    
    mockBigQueryService.getAccessLogs.mockResolvedValue([
      { timestamp: new Date().toISOString(), user: 'system', action: 'encrypt' },
      { timestamp: new Date().toISOString(), user: 'system', action: 'update_access' }
    ]);
    
    mockLookerService.getComplianceScore.mockResolvedValue({
      overall: 99.9,
      hipaa: 99.8,
      gdpr: 100
    });
    
    // 4. Execute remediation
    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 5. Verify results
    expect(remediationResult.success).toBe(true);
    expect(remediationResult.steps.length).toBe(3);
    expect(remediationResult.steps.every(step => step.success)).toBe(true);
    
    // Verify encryption was called
    expect(mockBigQueryService.encryptDataset).toHaveBeenCalledWith(
      'healthcare-demo',
      'patient_records'
    );
    
    // Verify access controls were updated
    expect(mockBigQueryService.updateDataset).toHaveBeenCalledWith(
      'healthcare-demo',
      'patient_records',
      expect.objectContaining({
        access: 'restricted'
      })
    );
    
    // Verify dashboard was updated
    expect(mockLookerService.updateDashboard).toHaveBeenCalledWith(
      'hipaa-compliance-dashboard'
    );
    
    // Verify compliance score is maintained
    const complianceScore = await mockLookerService.getComplianceScore();
    expect(complianceScore.overall).toBeGreaterThanOrEqual(99.9);
    
    // Verify remediation completed in under 8 seconds
    expect(duration).toBeLessThan(8000);
    console.log(`Remediation completed in ${duration}ms`);
    
    // Verify access logs were updated
    const accessLogs = await mockBigQueryService.getAccessLogs();
    expect(accessLogs.length).toBeGreaterThanOrEqual(2);
  });
  
  it('should generate boardroom-ready report', async () => {
    // Mock compliance score
    mockLookerService.getComplianceScore.mockResolvedValue({
      overall: 99.9,
      hipaa: 99.8,
      gdpr: 100,
      details: {
        controls: {
          total: 500,
          compliant: 499,
          nonCompliant: 1,
          remediated: 1
        },
        frameworks: [
          { name: 'HIPAA', score: 99.8, controlsCompliant: 249, controlsTotal: 250 },
          { name: 'GDPR', score: 100, controlsCompliant: 250, controlsTotal: 250 }
        ]
      }
    });
    
    // Get compliance score
    const complianceScore = await mockLookerService.getComplianceScore();
    
    // Verify compliance score
    expect(complianceScore.overall).toBeGreaterThanOrEqual(99.9);
    expect(complianceScore.details.controls.remediated).toBeGreaterThanOrEqual(1);
    
    // Calculate cost savings
    const manualRemediationCostPerIncident = 4200; // $4,200 per incident
    const annualIncidents = 1000; // Estimated annual incidents
    const automatedRemediationRate = 0.92; // 92% of incidents automated
    
    const annualSavings = manualRemediationCostPerIncident * annualIncidents * automatedRemediationRate;
    
    // Verify cost savings
    expect(annualSavings).toBeGreaterThanOrEqual(3800000); // $3.8M+
    
    console.log(`Annual cost savings: $${annualSavings.toLocaleString()}`);
    console.log(`Compliance score maintained at ${complianceScore.overall}%`);
  });
});
