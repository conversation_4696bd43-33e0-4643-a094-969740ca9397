/**
 * Performance Optimization Components
 *
 * This module exports performance optimization components for the Finite Universe
 * Principle defense system, including caching and performance optimization.
 */

const { CacheManager, createCacheManager } = require('./cache-manager');
const { PerformanceOptimizer, createPerformanceOptimizer } = require('./performance-optimizer');

/**
 * Create all performance optimization components
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all performance optimization components
 */
function createPerformanceComponents(options = {}) {
  // Create cache manager
  const cacheManager = createCacheManager(options.cacheManagerOptions);
  
  // Create performance optimizer with the cache manager
  const performanceOptimizer = createPerformanceOptimizer({
    ...options.performanceOptimizerOptions,
    cacheManager
  });
  
  return {
    cacheManager,
    performanceOptimizer
  };
}

module.exports = {
  // Cache manager
  CacheManager,
  createCacheManager,
  
  // Performance optimizer
  PerformanceOptimizer,
  createPerformanceOptimizer,
  
  // Factory function
  createPerformanceComponents
};
