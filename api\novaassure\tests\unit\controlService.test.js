/**
 * Unit tests for the control service
 */

const controlService = require('../../services/controlService');
const { Control } = require('../../models');

// Mock the models
jest.mock('../../models', () => ({
  Control: {
    find: jest.fn(),
    findById: jest.fn(),
    countDocuments: jest.fn(),
    getById: jest.fn(),
    getMapping: jest.fn()
  }
}));

// Mock the logger
jest.mock('../../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

describe('Control Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getAllControls', () => {
    it('should get all controls with pagination', async () => {
      // Mock data
      const mockControls = [
        { _id: '1', name: 'Control 1' },
        { _id: '2', name: 'Control 2' }
      ];
      const mockTotal = 2;

      // Setup mocks
      Control.countDocuments.mockResolvedValue(mockTotal);
      Control.find.mockReturnValue({
        skip: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            sort: jest.fn().mockResolvedValue(mockControls)
          })
        })
      });

      // Call the service
      const result = await controlService.getAllControls({}, 1, 10);

      // Assertions
      expect(Control.countDocuments).toHaveBeenCalled();
      expect(Control.find).toHaveBeenCalled();
      expect(result).toEqual({
        controls: mockControls,
        pagination: {
          page: 1,
          limit: 10,
          total: mockTotal,
          pages: 1
        }
      });
    });

    it('should apply filters correctly', async () => {
      // Mock data
      const mockControls = [
        { _id: '1', name: 'Control 1', framework: 'soc2' }
      ];
      const mockTotal = 1;
      const filters = { framework: 'soc2', category: 'access-control', search: 'control' };

      // Setup mocks
      Control.countDocuments.mockResolvedValue(mockTotal);
      Control.find.mockReturnValue({
        skip: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            sort: jest.fn().mockResolvedValue(mockControls)
          })
        })
      });

      // Call the service
      const result = await controlService.getAllControls(filters, 1, 10);

      // Assertions
      expect(Control.countDocuments).toHaveBeenCalledWith({
        framework: 'soc2',
        category: 'access-control',
        $text: { $search: 'control' }
      });
      expect(Control.find).toHaveBeenCalledWith({
        framework: 'soc2',
        category: 'access-control',
        $text: { $search: 'control' }
      });
      expect(result).toEqual({
        controls: mockControls,
        pagination: {
          page: 1,
          limit: 10,
          total: mockTotal,
          pages: 1
        }
      });
    });

    it('should handle errors', async () => {
      // Setup mocks
      const error = new Error('Database error');
      Control.countDocuments.mockRejectedValue(error);

      // Call the service and expect it to throw
      await expect(controlService.getAllControls({}, 1, 10)).rejects.toThrow(error);
    });
  });

  describe('getControlById', () => {
    it('should get a control by ID', async () => {
      // Mock data
      const mockControl = { _id: '1', name: 'Control 1' };

      // Setup mocks
      Control.getById.mockResolvedValue(mockControl);

      // Call the service
      const result = await controlService.getControlById('1');

      // Assertions
      expect(Control.getById).toHaveBeenCalledWith('1');
      expect(result).toEqual(mockControl);
    });

    it('should throw an error if control not found', async () => {
      // Setup mocks
      Control.getById.mockResolvedValue(null);

      // Call the service and expect it to throw
      await expect(controlService.getControlById('1')).rejects.toThrow('Control not found');
    });

    it('should handle errors', async () => {
      // Setup mocks
      const error = new Error('Database error');
      Control.getById.mockRejectedValue(error);

      // Call the service and expect it to throw
      await expect(controlService.getControlById('1')).rejects.toThrow(error);
    });
  });

  describe('getControlMapping', () => {
    it('should get control mapping between frameworks', async () => {
      // Mock data
      const mockMapping = [
        {
          sourceControl: { _id: '1', name: 'Control 1', framework: 'soc2' },
          targetControls: [
            { _id: '2', name: 'Control 2', framework: 'gdpr' }
          ]
        }
      ];

      // Setup mocks
      Control.getMapping.mockResolvedValue(mockMapping);

      // Call the service
      const result = await controlService.getControlMapping('soc2', 'gdpr');

      // Assertions
      expect(Control.getMapping).toHaveBeenCalledWith('soc2', 'gdpr');
      expect(result).toEqual(mockMapping);
    });

    it('should handle errors', async () => {
      // Setup mocks
      const error = new Error('Database error');
      Control.getMapping.mockRejectedValue(error);

      // Call the service and expect it to throw
      await expect(controlService.getControlMapping('soc2', 'gdpr')).rejects.toThrow(error);
    });
  });
});
