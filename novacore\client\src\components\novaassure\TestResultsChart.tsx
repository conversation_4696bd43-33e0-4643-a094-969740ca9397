/**
 * Test Results Chart Component
 * 
 * This component displays a chart showing test results.
 */

import React, { useEffect, useRef } from 'react';

interface TestResultsChartProps {
  passed: number;
  failed: number;
  pending: number;
  inconclusive: number;
}

export const TestResultsChart: React.FC<TestResultsChartProps> = ({
  passed,
  failed,
  pending,
  inconclusive,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw pie chart
    drawPieChart(ctx, canvas.width, canvas.height);
    
  }, [passed, failed, pending, inconclusive]);
  
  const drawPieChart = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    const total = passed + failed + pending + inconclusive;
    if (total === 0) {
      // Draw empty state
      ctx.font = '14px Arial';
      ctx.fillStyle = '#6B7280';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText('No test data available', width / 2, height / 2);
      return;
    }
    
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) * 0.4;
    
    // Data for the pie chart
    const data = [
      { value: passed, color: '#10B981', label: 'Passed' },
      { value: failed, color: '#EF4444', label: 'Failed' },
      { value: pending, color: '#3B82F6', label: 'Pending' },
      { value: inconclusive, color: '#F59E0B', label: 'Inconclusive' },
    ];
    
    // Calculate total for percentages
    let startAngle = 0;
    
    // Draw pie slices
    data.forEach(item => {
      if (item.value === 0) return;
      
      const sliceAngle = (item.value / total) * 2 * Math.PI;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, startAngle + sliceAngle);
      ctx.closePath();
      
      ctx.fillStyle = item.color;
      ctx.fill();
      
      // Calculate position for the label
      const labelAngle = startAngle + sliceAngle / 2;
      const labelRadius = radius * 0.7;
      const labelX = centerX + Math.cos(labelAngle) * labelRadius;
      const labelY = centerY + Math.sin(labelAngle) * labelRadius;
      
      // Draw percentage label if slice is big enough
      if (sliceAngle > 0.2) {
        ctx.font = 'bold 12px Arial';
        ctx.fillStyle = '#FFFFFF';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(`${Math.round((item.value / total) * 100)}%`, labelX, labelY);
      }
      
      startAngle += sliceAngle;
    });
    
    // Draw center circle (donut hole)
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);
    ctx.fillStyle = '#FFFFFF';
    ctx.fill();
    
    // Draw total in center
    ctx.font = 'bold 16px Arial';
    ctx.fillStyle = '#111827';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(total.toString(), centerX, centerY - 10);
    
    ctx.font = '12px Arial';
    ctx.fillStyle = '#6B7280';
    ctx.fillText('Total Tests', centerX, centerY + 10);
    
    // Draw legend
    const legendX = width * 0.1;
    const legendY = height * 0.8;
    const legendSpacing = 20;
    
    data.forEach((item, index) => {
      const y = legendY + index * legendSpacing;
      
      // Draw color box
      ctx.fillStyle = item.color;
      ctx.fillRect(legendX, y, 12, 12);
      
      // Draw label
      ctx.font = '12px Arial';
      ctx.fillStyle = '#111827';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'middle';
      ctx.fillText(`${item.label}: ${item.value}`, legendX + 20, y + 6);
    });
  };
  
  return (
    <div className="h-64">
      <canvas 
        ref={canvasRef} 
        className="w-full h-full"
      />
    </div>
  );
};

export default TestResultsChart;
