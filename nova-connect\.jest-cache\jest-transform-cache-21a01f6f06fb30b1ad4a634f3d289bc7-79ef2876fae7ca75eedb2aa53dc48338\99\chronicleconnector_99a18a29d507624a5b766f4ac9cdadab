1c50d2a091df50e1e7179d6782b488f1
/**
 * Google Chronicle Connector
 * 
 * Connects to Google Chronicle API and normalizes security events
 * for use with NovaConnect's remediation engine.
 * 
 * This connector enables mapping between MITRE ATT&CK and NIST frameworks
 * for comprehensive threat-to-compliance correlation.
 */

const axios = require('axios');
const {
  GoogleAuth
} = require('google-auth-library');
const TransformationEngine = require('../../engines/transformation-engine');
class ChronicleConnector {
  constructor(options = {}) {
    this.options = {
      enableMetrics: true,
      maxConcurrentRequests: 10,
      pageSize: 1000,
      baseUrl: 'https://backstory.googleapis.com/v1',
      ...options
    };
    this.transformationEngine = new TransformationEngine({
      enableMetrics: this.options.enableMetrics,
      enableCaching: true
    });

    // Register Chronicle-specific transformers
    this.transformationEngine.registerTransformer('mapAttackToNist', this._mapAttackToNist.bind(this));
    this.transformationEngine.registerTransformer('extractIoc', this._extractIoc);
    this.transformationEngine.registerTransformer('normalizeChronicleTime', this._normalizeChronicleTime);

    // Initialize MITRE ATT&CK to NIST mapping
    this._initializeAttackToNistMapping();

    // Initialize metrics
    this.metrics = {
      eventsRetrieved: 0,
      eventsNormalized: 0,
      apiCalls: 0,
      totalApiLatency: 0,
      averageApiLatency: 0,
      totalNormalizationTime: 0,
      averageNormalizationTime: 0
    };
  }

  /**
   * Initialize the Chronicle client with credentials
   * @param {Object} credentials - GCP credentials
   */
  async initialize(credentials) {
    try {
      this.auth = new GoogleAuth({
        credentials: credentials,
        scopes: ['https://www.googleapis.com/auth/chronicle-backstory']
      });
      this.client = axios.create({
        baseURL: this.options.baseUrl,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Set up request interceptor to add authentication
      this.client.interceptors.request.use(async config => {
        const token = await this.auth.getAccessToken();
        config.headers.Authorization = `Bearer ${token}`;
        return config;
      });
      return {
        success: true
      };
    } catch (error) {
      console.error('Error initializing Chronicle client:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get alerts from Chronicle
   * @param {Object} params - Query parameters
   * @returns {Object} - Alerts and metadata
   */
  async getAlerts(params = {}) {
    if (!this.client) {
      throw new Error('Chronicle client not initialized. Call initialize() first.');
    }
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    try {
      const {
        startTime: alertStartTime,
        endTime: alertEndTime,
        pageSize = this.options.pageSize,
        pageToken
      } = params;

      // Build the request
      const request = {
        pageSize,
        pageToken
      };
      if (alertStartTime) {
        request.startTime = alertStartTime;
      }
      if (alertEndTime) {
        request.endTime = alertEndTime;
      }

      // Make the API call
      const response = await this.client.get('/alerts', {
        params: request
      });
      const alerts = response.data.alerts || [];
      const nextPageToken = response.data.nextPageToken;

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        this.metrics.apiCalls++;
        this.metrics.eventsRetrieved += alerts.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      return {
        alerts,
        nextPageToken
      };
    } catch (error) {
      console.error('Error retrieving alerts from Chronicle:', error);
      throw error;
    }
  }

  /**
   * Get IOCs from Chronicle
   * @param {Object} params - Query parameters
   * @returns {Object} - IOCs and metadata
   */
  async getIocs(params = {}) {
    if (!this.client) {
      throw new Error('Chronicle client not initialized. Call initialize() first.');
    }
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    try {
      const {
        iocType,
        value,
        pageSize = this.options.pageSize,
        pageToken
      } = params;

      // Build the request
      const request = {
        pageSize,
        pageToken
      };
      if (iocType) {
        request.iocType = iocType;
      }
      if (value) {
        request.value = value;
      }

      // Make the API call
      const response = await this.client.get('/iocs', {
        params: request
      });
      const iocs = response.data.iocs || [];
      const nextPageToken = response.data.nextPageToken;

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        this.metrics.apiCalls++;
        this.metrics.eventsRetrieved += iocs.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      return {
        iocs,
        nextPageToken
      };
    } catch (error) {
      console.error('Error retrieving IOCs from Chronicle:', error);
      throw error;
    }
  }

  /**
   * Search for events in Chronicle
   * @param {Object} params - Query parameters
   * @returns {Object} - Events and metadata
   */
  async searchEvents(params = {}) {
    if (!this.client) {
      throw new Error('Chronicle client not initialized. Call initialize() first.');
    }
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    try {
      const {
        query,
        startTime: eventStartTime,
        endTime: eventEndTime,
        pageSize = this.options.pageSize,
        pageToken
      } = params;
      if (!query) {
        throw new Error('Query parameter is required');
      }

      // Build the request
      const request = {
        query,
        pageSize,
        pageToken
      };
      if (eventStartTime) {
        request.startTime = eventStartTime;
      }
      if (eventEndTime) {
        request.endTime = eventEndTime;
      }

      // Make the API call
      const response = await this.client.post('/events:search', request);
      const events = response.data.events || [];
      const nextPageToken = response.data.nextPageToken;

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        this.metrics.apiCalls++;
        this.metrics.eventsRetrieved += events.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      return {
        events,
        nextPageToken
      };
    } catch (error) {
      console.error('Error searching events in Chronicle:', error);
      throw error;
    }
  }

  /**
   * Normalize Chronicle alerts to NovaConnect format
   * @param {Array} alerts - Chronicle alerts
   * @returns {Array} - Normalized alerts
   */
  normalizeAlerts(alerts) {
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    try {
      // Define transformation rules for Chronicle alerts
      const rules = [{
        source: 'id',
        target: 'id'
      }, {
        source: 'type',
        target: 'type'
      }, {
        source: 'createdTime',
        target: 'createdAt',
        transform: 'normalizeChronicleTime'
      }, {
        source: 'severity',
        target: 'severity',
        transform: 'lowercase'
      }, {
        source: 'name',
        target: 'title'
      }, {
        source: 'description',
        target: 'description'
      }, {
        source: 'asset.hostname',
        target: 'resourceName'
      }, {
        source: 'asset.assetType',
        target: 'resourceType'
      }, {
        source: 'attackTechniques',
        target: 'attackTechniques'
      }, {
        source: 'attackTechniques',
        target: 'nistControls',
        transform: 'mapAttackToNist'
      }, {
        source: 'indicators',
        target: 'indicators',
        transform: 'extractIoc'
      }];

      // Transform each alert
      const normalizedAlerts = alerts.map(alert => this.transformationEngine.transform(alert, rules));

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.metrics.eventsNormalized += alerts.length;
        this.metrics.totalNormalizationTime += duration;
        this.metrics.averageNormalizationTime = this.metrics.totalNormalizationTime / this.metrics.eventsNormalized;
      }
      return normalizedAlerts;
    } catch (error) {
      console.error('Error normalizing Chronicle alerts:', error);
      throw error;
    }
  }

  /**
   * Get and normalize alerts in a single call
   * @param {Object} params - Query parameters
   * @returns {Object} - Normalized alerts and metadata
   */
  async getAlertsNormalized(params = {}) {
    const {
      alerts,
      nextPageToken
    } = await this.getAlerts(params);
    const normalizedAlerts = this.normalizeAlerts(alerts);
    return {
      alerts: normalizedAlerts,
      nextPageToken
    };
  }

  /**
   * Get all alerts with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All alerts
   */
  async getAllAlerts(params = {}) {
    let allAlerts = [];
    let nextPageToken = null;
    do {
      const {
        alerts,
        nextPageToken: token
      } = await this.getAlerts({
        ...params,
        pageToken: nextPageToken
      });
      allAlerts = allAlerts.concat(alerts);
      nextPageToken = token;
    } while (nextPageToken);
    return allAlerts;
  }

  /**
   * Get all normalized alerts with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All normalized alerts
   */
  async getAllAlertsNormalized(params = {}) {
    const alerts = await this.getAllAlerts(params);
    return this.normalizeAlerts(alerts);
  }

  /**
   * Get metrics for the connector
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      transformationMetrics: this.transformationEngine.getMetrics()
    };
  }

  /**
   * Initialize MITRE ATT&CK to NIST mapping
   * @private
   */
  _initializeAttackToNistMapping() {
    // This is a simplified mapping - in a real implementation, this would be more comprehensive
    this.attackToNistMapping = {
      // Initial Access
      'T1189': ['AC-4', 'SI-3'],
      // Drive-by Compromise
      'T1190': ['AC-4', 'SI-3', 'SI-10'],
      // Exploit Public-Facing Application
      'T1133': ['AC-2', 'AC-3', 'AC-17'],
      // External Remote Services

      // Execution
      'T1059': ['CM-7', 'SI-3'],
      // Command and Scripting Interpreter
      'T1203': ['SI-3', 'SI-4'],
      // Exploitation for Client Execution

      // Persistence
      'T1136': ['AC-2'],
      // Create Account
      'T1098': ['AC-2', 'AC-3'],
      // Account Manipulation

      // Privilege Escalation
      'T1068': ['SI-2', 'RA-5'],
      // Exploitation for Privilege Escalation
      'T1078': ['AC-2', 'AC-3', 'AC-6'],
      // Valid Accounts

      // Defense Evasion
      'T1070': ['AU-9', 'SI-4'],
      // Indicator Removal on Host
      'T1027': ['SI-3', 'SI-4'],
      // Obfuscated Files or Information

      // Credential Access
      'T1110': ['AC-7', 'IA-5'],
      // Brute Force
      'T1003': ['IA-5', 'SI-4'],
      // OS Credential Dumping

      // Discovery
      'T1087': ['AC-2', 'SI-4'],
      // Account Discovery
      'T1082': ['CM-8', 'SI-4'],
      // System Information Discovery

      // Lateral Movement
      'T1021': ['AC-17', 'SI-4'],
      // Remote Services
      'T1091': ['AC-4', 'SI-3'],
      // Replication Through Removable Media

      // Collection
      'T1005': ['AC-3', 'SI-4'],
      // Data from Local System
      'T1039': ['AC-3', 'SI-4'],
      // Data from Network Shared Drive

      // Command and Control
      'T1071': ['SC-7', 'SI-4'],
      // Application Layer Protocol
      'T1105': ['SI-3', 'SI-4'],
      // Ingress Tool Transfer

      // Exfiltration
      'T1048': ['AC-4', 'SI-4'],
      // Exfiltration Over Alternative Protocol
      'T1041': ['AC-4', 'SI-4'],
      // Exfiltration Over C2 Channel

      // Impact
      'T1485': ['CP-9', 'CP-10'],
      // Data Destruction
      'T1486': ['CP-9', 'CP-10', 'SI-4'] // Data Encrypted for Impact
    };
  }

  /**
   * Map MITRE ATT&CK techniques to NIST controls
   * @param {Array} techniques - ATT&CK techniques
   * @returns {Array} - NIST controls
   * @private
   */
  _mapAttackToNist(techniques) {
    if (!techniques || !Array.isArray(techniques)) {
      return [];
    }
    const nistControls = new Set();
    for (const technique of techniques) {
      const techniqueId = technique.split('.')[0]; // Handle sub-techniques
      const controls = this.attackToNistMapping[techniqueId] || [];
      for (const control of controls) {
        nistControls.add(control);
      }
    }
    return Array.from(nistControls);
  }

  /**
   * Extract IOCs from indicators
   * @param {Array} indicators - Chronicle indicators
   * @returns {Array} - Extracted IOCs
   * @private
   */
  _extractIoc(indicators) {
    if (!indicators || !Array.isArray(indicators)) {
      return [];
    }
    return indicators.map(indicator => ({
      type: indicator.type,
      value: indicator.value,
      confidence: indicator.confidence
    }));
  }

  /**
   * Normalize Chronicle timestamp to Unix timestamp
   * @param {string} timestamp - Chronicle timestamp
   * @returns {number} - Unix timestamp
   * @private
   */
  _normalizeChronicleTime(timestamp) {
    if (!timestamp) return null;

    // Chronicle uses RFC 3339 format
    return new Date(timestamp).getTime();
  }
}
module.exports = ChronicleConnector;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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