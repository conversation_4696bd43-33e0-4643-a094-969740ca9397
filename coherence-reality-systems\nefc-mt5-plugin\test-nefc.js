/**
 * NEFC API Test Script
 * Shows how to use the NEFC API plugin with your MT5 dashboard
 */

const fetch = require('node-fetch');

const NEFC_API_URL = 'http://localhost:3143';

async function testNEFC() {
  console.log('🧪 Testing NEFC MT5 API Plugin...\n');

  try {
    // 1. Check health
    console.log('1. Health Check...');
    const healthResponse = await fetch(`${NEFC_API_URL}/health`);
    const health = await healthResponse.json();
    console.log(`   ✅ Status: ${health.status}`);
    console.log(`   ⏰ Uptime: ${health.uptime.toFixed(1)}s\n`);

    // 2. Get initial status
    console.log('2. Initial Status...');
    const statusResponse = await fetch(`${NEFC_API_URL}/status`);
    const status = await statusResponse.json();
    console.log(`   📊 NEFC Active: ${status.nefc.active}`);
    console.log(`   📈 Signals Generated: ${status.nefc.signals_generated}\n`);

    // 3. Analyze a symbol
    console.log('3. Analyze EURUSD...');
    const analyzeResponse = await fetch(`${NEFC_API_URL}/analyze`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ symbol: 'EURUSD' })
    });
    const analysis = await analyzeResponse.json();
    
    if (analysis.success) {
      console.log(`   🎯 Symbol: ${analysis.analysis.symbol}`);
      console.log(`   📊 Coherence: ${(analysis.analysis.coherence_score * 100).toFixed(1)}%`);
      console.log(`   🔍 Confidence: ${(analysis.analysis.confidence * 100).toFixed(1)}%`);
      console.log(`   💡 Recommendation: ${analysis.analysis.recommendation}`);
      console.log(`   🔱 Solutions Applied:`);
      console.log(`      SMILE: ${(analysis.analysis.solutions.smile * 100).toFixed(1)}%`);
      console.log(`      Vol→Vl: ${(analysis.analysis.solutions.vol_to_vl * 100).toFixed(1)}%`);
      console.log(`      Equity: ${(analysis.analysis.solutions.equity_problem * 100).toFixed(1)}%\n`);
    }

    // 4. Start NEFC
    console.log('4. Starting NEFC...');
    const startResponse = await fetch(`${NEFC_API_URL}/start`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    const startResult = await startResponse.json();
    
    if (startResult.success) {
      console.log(`   ✅ NEFC Started`);
      console.log(`   📊 Symbols: ${startResult.config.symbols.join(', ')}`);
      console.log(`   ⏱️ Interval: ${startResult.config.interval / 1000}s`);
      console.log(`   🎯 Threshold: ${(startResult.config.confidence_threshold * 100)}%\n`);
    }

    // 5. Wait and check status again
    console.log('5. Waiting 5 seconds for analysis...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const finalStatusResponse = await fetch(`${NEFC_API_URL}/status`);
    const finalStatus = await finalStatusResponse.json();
    console.log(`   📈 Signals Generated: ${finalStatus.nefc.signals_generated}`);
    
    if (finalStatus.nefc.last_analysis) {
      console.log(`   🔍 Last Analysis: ${finalStatus.nefc.last_analysis.symbol}`);
      console.log(`   💡 Recommendation: ${finalStatus.nefc.last_analysis.recommendation}\n`);
    }

    // 6. Stop NEFC
    console.log('6. Stopping NEFC...');
    const stopResponse = await fetch(`${NEFC_API_URL}/stop`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    const stopResult = await stopResponse.json();
    
    if (stopResult.success) {
      console.log(`   ✅ NEFC Stopped`);
      console.log(`   📊 Final Stats:`);
      console.log(`      Signals: ${stopResult.final_stats.signals_generated}`);
      console.log(`      Trades Attempted: ${stopResult.final_stats.trades_attempted}`);
      console.log(`      Trades Successful: ${stopResult.final_stats.trades_successful}\n`);
    }

    console.log('🎉 NEFC API Test Complete!');
    console.log('\n📋 Integration Instructions:');
    console.log('1. Start NEFC API: npm start');
    console.log('2. Call from MT5 dashboard: fetch("http://localhost:3143/analyze", {...})');
    console.log('3. Use real analysis data in your trading decisions');
    console.log('4. No simulation - only honest market analysis');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Make sure NEFC API is running: npm start');
  }
}

// Run the test
testNEFC();
