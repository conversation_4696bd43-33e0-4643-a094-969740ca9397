/**
 * Cache Service
 * 
 * This service provides caching functionality for NovaConnect UAC.
 * It supports multiple cache providers (in-memory, Redis) and implements
 * various caching strategies.
 */

const NodeCache = require('node-cache');
const Redis = require('redis');
const { promisify } = require('util');
const logger = require('../utils/logger');

// Cache providers
const PROVIDERS = {
  MEMORY: 'memory',
  REDIS: 'redis'
};

// Cache strategies
const STRATEGIES = {
  SIMPLE: 'simple',
  TIERED: 'tiered',
  WRITE_THROUGH: 'write-through',
  WRITE_BEHIND: 'write-behind',
  WRITE_AROUND: 'write-around'
};

class CacheService {
  constructor() {
    // Default configuration
    this.config = {
      provider: process.env.CACHE_PROVIDER || PROVIDERS.MEMORY,
      redisUrl: process.env.REDIS_URI || 'redis://localhost:6379',
      defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL || 300, 10), // 5 minutes
      checkPeriod: parseInt(process.env.CACHE_CHECK_PERIOD || 60, 10), // 1 minute
      maxKeys: parseInt(process.env.CACHE_MAX_KEYS || 10000, 10),
      strategy: process.env.CACHE_STRATEGY || STRATEGIES.SIMPLE,
      namespace: process.env.CACHE_NAMESPACE || 'novafuse:',
      enabled: process.env.CACHE_ENABLED !== 'false'
    };
    
    // Cache clients
    this.memoryCache = null;
    this.redisClient = null;
    
    // Cache metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0
    };
    
    // Initialize cache
    this._initialize();
  }
  
  /**
   * Initialize cache
   * @private
   */
  _initialize() {
    if (!this.config.enabled) {
      logger.info('Cache is disabled');
      return;
    }
    
    try {
      // Initialize memory cache
      this.memoryCache = new NodeCache({
        stdTTL: this.config.defaultTtl,
        checkperiod: this.config.checkPeriod,
        maxKeys: this.config.maxKeys,
        useClones: false
      });
      
      // Initialize Redis cache if needed
      if (this.config.provider === PROVIDERS.REDIS) {
        this._initializeRedis();
      }
      
      logger.info(`Cache initialized with provider: ${this.config.provider}, strategy: ${this.config.strategy}`);
    } catch (error) {
      logger.error('Failed to initialize cache', { error });
      this.metrics.errors++;
    }
  }
  
  /**
   * Initialize Redis cache
   * @private
   */
  async _initializeRedis() {
    try {
      // Create Redis client
      this.redisClient = Redis.createClient({
        url: this.config.redisUrl,
        socket: {
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('Redis connection failed, falling back to memory cache');
              this.config.provider = PROVIDERS.MEMORY;
              return false;
            }
            return Math.min(retries * 100, 3000);
          }
        }
      });
      
      // Promisify Redis methods
      this.redisGet = promisify(this.redisClient.get).bind(this.redisClient);
      this.redisSet = promisify(this.redisClient.set).bind(this.redisClient);
      this.redisDel = promisify(this.redisClient.del).bind(this.redisClient);
      this.redisKeys = promisify(this.redisClient.keys).bind(this.redisClient);
      this.redisFlushAll = promisify(this.redisClient.flushall).bind(this.redisClient);
      
      // Set up event handlers
      this.redisClient.on('error', (error) => {
        logger.error('Redis error', { error });
        this.metrics.errors++;
      });
      
      this.redisClient.on('connect', () => {
        logger.info('Connected to Redis');
      });
      
      this.redisClient.on('reconnecting', () => {
        logger.warn('Reconnecting to Redis');
      });
      
      // Connect to Redis
      await this.redisClient.connect();
    } catch (error) {
      logger.error('Failed to initialize Redis cache', { error });
      this.metrics.errors++;
      this.config.provider = PROVIDERS.MEMORY;
    }
  }
  
  /**
   * Get a value from cache
   * @param {string} key - Cache key
   * @returns {Promise<any>} - Cached value or null
   */
  async get(key) {
    if (!this.config.enabled) {
      return null;
    }
    
    try {
      const namespacedKey = this._getNamespacedKey(key);
      
      // Get from cache based on provider
      let value = null;
      
      if (this.config.provider === PROVIDERS.REDIS) {
        // Try to get from Redis
        const redisValue = await this.redisGet(namespacedKey);
        
        if (redisValue) {
          value = JSON.parse(redisValue);
        }
      } else {
        // Get from memory cache
        value = this.memoryCache.get(namespacedKey);
      }
      
      // Update metrics
      if (value !== undefined && value !== null) {
        this.metrics.hits++;
        return value;
      } else {
        this.metrics.misses++;
        return null;
      }
    } catch (error) {
      logger.error('Cache get error', { error, key });
      this.metrics.errors++;
      return null;
    }
  }
  
  /**
   * Set a value in cache
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds (optional)
   * @returns {Promise<boolean>} - Whether the value was set
   */
  async set(key, value, ttl = this.config.defaultTtl) {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      const namespacedKey = this._getNamespacedKey(key);
      
      // Set in cache based on provider and strategy
      if (this.config.provider === PROVIDERS.REDIS) {
        // Set in Redis
        await this.redisSet(
          namespacedKey,
          JSON.stringify(value),
          'EX',
          ttl
        );
        
        // Set in memory cache if using tiered strategy
        if (this.config.strategy === STRATEGIES.TIERED) {
          this.memoryCache.set(namespacedKey, value, ttl);
        }
      } else {
        // Set in memory cache
        this.memoryCache.set(namespacedKey, value, ttl);
      }
      
      // Update metrics
      this.metrics.sets++;
      return true;
    } catch (error) {
      logger.error('Cache set error', { error, key });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Delete a value from cache
   * @param {string} key - Cache key
   * @returns {Promise<boolean>} - Whether the value was deleted
   */
  async delete(key) {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      const namespacedKey = this._getNamespacedKey(key);
      
      // Delete from cache based on provider
      if (this.config.provider === PROVIDERS.REDIS) {
        // Delete from Redis
        await this.redisDel(namespacedKey);
        
        // Delete from memory cache if using tiered strategy
        if (this.config.strategy === STRATEGIES.TIERED) {
          this.memoryCache.del(namespacedKey);
        }
      } else {
        // Delete from memory cache
        this.memoryCache.del(namespacedKey);
      }
      
      // Update metrics
      this.metrics.deletes++;
      return true;
    } catch (error) {
      logger.error('Cache delete error', { error, key });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Clear all cache
   * @returns {Promise<boolean>} - Whether the cache was cleared
   */
  async clear() {
    if (!this.config.enabled) {
      return false;
    }
    
    try {
      // Clear cache based on provider
      if (this.config.provider === PROVIDERS.REDIS) {
        // Clear Redis cache
        await this.redisFlushAll();
        
        // Clear memory cache if using tiered strategy
        if (this.config.strategy === STRATEGIES.TIERED) {
          this.memoryCache.flushAll();
        }
      } else {
        // Clear memory cache
        this.memoryCache.flushAll();
      }
      
      return true;
    } catch (error) {
      logger.error('Cache clear error', { error });
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Get cache metrics
   * @returns {Object} - Cache metrics
   */
  getMetrics() {
    const hitRatio = this.metrics.hits + this.metrics.misses > 0
      ? this.metrics.hits / (this.metrics.hits + this.metrics.misses)
      : 0;
    
    return {
      ...this.metrics,
      hitRatio,
      provider: this.config.provider,
      strategy: this.config.strategy,
      enabled: this.config.enabled
    };
  }
  
  /**
   * Get namespaced key
   * @param {string} key - Original key
   * @returns {string} - Namespaced key
   * @private
   */
  _getNamespacedKey(key) {
    return `${this.config.namespace}${key}`;
  }
  
  /**
   * Create a cache middleware
   * @param {Object} options - Middleware options
   * @returns {Function} - Express middleware
   */
  middleware(options = {}) {
    const {
      ttl = this.config.defaultTtl,
      methods = ['GET'],
      getKey = (req) => `${req.method}:${req.originalUrl}`,
      shouldCache = (req) => true,
      cacheNullValues = false
    } = options;
    
    return async (req, res, next) => {
      if (!this.config.enabled || !methods.includes(req.method) || !shouldCache(req)) {
        return next();
      }
      
      const key = getKey(req);
      
      try {
        // Try to get from cache
        const cachedValue = await this.get(key);
        
        if (cachedValue !== null) {
          // Return cached value
          return res.json(cachedValue);
        }
        
        // Store original res.json method
        const originalJson = res.json;
        
        // Override res.json method
        res.json = async (data) => {
          // Restore original res.json method
          res.json = originalJson;
          
          // Cache response if not null or cacheNullValues is true
          if (data !== null || cacheNullValues) {
            await this.set(key, data, ttl);
          }
          
          // Call original res.json method
          return res.json(data);
        };
        
        next();
      } catch (error) {
        logger.error('Cache middleware error', { error, key });
        this.metrics.errors++;
        next();
      }
    };
  }
  
  /**
   * Create a cache decorator for functions
   * @param {Object} options - Decorator options
   * @returns {Function} - Function decorator
   */
  decorator(options = {}) {
    const {
      ttl = this.config.defaultTtl,
      getKey = (...args) => JSON.stringify(args),
      shouldCache = (...args) => true,
      cacheNullValues = false
    } = options;
    
    return (fn) => {
      return async (...args) => {
        if (!this.config.enabled || !shouldCache(...args)) {
          return fn(...args);
        }
        
        const key = getKey(...args);
        
        try {
          // Try to get from cache
          const cachedValue = await this.get(key);
          
          if (cachedValue !== null) {
            return cachedValue;
          }
          
          // Call original function
          const result = await fn(...args);
          
          // Cache result if not null or cacheNullValues is true
          if (result !== null || cacheNullValues) {
            await this.set(key, result, ttl);
          }
          
          return result;
        } catch (error) {
          logger.error('Cache decorator error', { error, key });
          this.metrics.errors++;
          return fn(...args);
        }
      };
    };
  }
}

module.exports = new CacheService();
