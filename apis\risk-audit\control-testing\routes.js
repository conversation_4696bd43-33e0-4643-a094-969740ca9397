const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /risk-audit/control-testing/controls:
 *   get:
 *     summary: Get a list of controls
 *     description: Returns a paginated list of controls with optional filtering
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by control type
 *         schema:
 *           type: string
 *           enum: [preventive, detective, corrective, directive]
 *       - name: category
 *         in: query
 *         description: Filter by control category
 *         schema:
 *           type: string
 *           enum: [administrative, technical, physical]
 *       - name: status
 *         in: query
 *         description: Filter by control status
 *         schema:
 *           type: string
 *           enum: [draft, implemented, under-review, approved, deprecated]
 *       - name: framework
 *         in: query
 *         description: Filter by compliance framework
 *         schema:
 *           type: string
 *       - name: riskLevel
 *         in: query
 *         description: Filter by risk level
 *         schema:
 *           type: string
 *           enum: [critical, high, medium, low]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Control'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/controls', controllers.getControls);

/**
 * @swagger
 * /risk-audit/control-testing/controls/{id}:
 *   get:
 *     summary: Get a specific control
 *     description: Returns a specific control by ID
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Control ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Control'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/controls/:id', controllers.getControlById);

/**
 * @swagger
 * /risk-audit/control-testing/controls:
 *   post:
 *     summary: Create a new control
 *     description: Creates a new control
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ControlInput'
 *     responses:
 *       201:
 *         description: Control created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Control'
 *                 message:
 *                   type: string
 *                   example: Control created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/controls', validateRequest('createControl'), controllers.createControl);

/**
 * @swagger
 * /risk-audit/control-testing/controls/{id}:
 *   put:
 *     summary: Update a control
 *     description: Updates an existing control
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Control ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ControlInput'
 *     responses:
 *       200:
 *         description: Control updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Control'
 *                 message:
 *                   type: string
 *                   example: Control updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/controls/:id', validateRequest('updateControl'), controllers.updateControl);

/**
 * @swagger
 * /risk-audit/control-testing/controls/{id}:
 *   delete:
 *     summary: Delete a control
 *     description: Deletes an existing control
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Control ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Control deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Control deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/controls/:id', controllers.deleteControl);

/**
 * @swagger
 * /risk-audit/control-testing/controls/{id}/test-results:
 *   get:
 *     summary: Get test results for a control
 *     description: Returns all test results associated with a specific control
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Control ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ControlTestResult'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/controls/:id/test-results', controllers.getControlTestResults);

/**
 * @swagger
 * /risk-audit/control-testing/controls/{id}/test-results:
 *   post:
 *     summary: Add a test result to a control
 *     description: Adds a new test result to an existing control
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Control ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ControlTestResultInput'
 *     responses:
 *       201:
 *         description: Test result added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ControlTestResult'
 *                 message:
 *                   type: string
 *                   example: Test result added successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/controls/:id/test-results', validateRequest('createTestResult'), controllers.addTestResult);

/**
 * @swagger
 * /risk-audit/control-testing/test-results:
 *   get:
 *     summary: Get a list of test results
 *     description: Returns a paginated list of test results with optional filtering
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: controlId
 *         in: query
 *         description: Filter by control ID
 *         schema:
 *           type: string
 *       - name: result
 *         in: query
 *         description: Filter by test result
 *         schema:
 *           type: string
 *           enum: [pass, fail, inconclusive, not-applicable]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ControlTestResult'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/test-results', controllers.getTestResults);

/**
 * @swagger
 * /risk-audit/control-testing/test-results/{id}:
 *   get:
 *     summary: Get a specific test result
 *     description: Returns a specific test result by ID
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Test result ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ControlTestResult'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/test-results/:id', controllers.getTestResultById);

/**
 * @swagger
 * /risk-audit/control-testing/test-results/{id}:
 *   put:
 *     summary: Update a test result
 *     description: Updates an existing test result
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Test result ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ControlTestResultInput'
 *     responses:
 *       200:
 *         description: Test result updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ControlTestResult'
 *                 message:
 *                   type: string
 *                   example: Test result updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/test-results/:id', validateRequest('updateTestResult'), controllers.updateTestResult);

/**
 * @swagger
 * /risk-audit/control-testing/types:
 *   get:
 *     summary: Get control types
 *     description: Returns a list of control types
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/types', controllers.getControlTypes);

/**
 * @swagger
 * /risk-audit/control-testing/categories:
 *   get:
 *     summary: Get control categories
 *     description: Returns a list of control categories
 *     tags: [Control Testing]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/categories', controllers.getControlCategories);

module.exports = router;
