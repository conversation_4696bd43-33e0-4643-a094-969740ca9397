/**
 * NovaUI API Client
 * 
 * A unified API client for interacting with NovaFuse APIs.
 */

import axios from 'axios';

/**
 * Create an API client instance
 * @param {object} options - Client options
 * @param {string} [options.baseUrl] - Base URL for API requests
 * @param {object} [options.headers] - Default headers for all requests
 * @param {number} [options.timeout] - Request timeout in milliseconds
 * @param {function} [options.onError] - Error handler function
 * @param {function} [options.onResponse] - Response handler function
 * @returns {object} - API client instance
 */
export function createApiClient(options = {}) {
  const {
    baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
    headers = {},
    timeout = 30000,
    onError = (error) => console.error('API Error:', error),
    onResponse = (response) => response
  } = options;
  
  // Create axios instance
  const instance = axios.create({
    baseURL: baseUrl,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    timeout
  });
  
  // Add request interceptor
  instance.interceptors.request.use(
    (config) => {
      // Add authorization header if token exists
      const token = typeof localStorage !== 'undefined' ? localStorage.getItem('token') : null;
      
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      
      return config;
    },
    (error) => {
      onError(error);
      return Promise.reject(error);
    }
  );
  
  // Add response interceptor
  instance.interceptors.response.use(
    (response) => {
      return onResponse(response);
    },
    (error) => {
      // Handle unauthorized errors
      if (error.response && error.response.status === 401) {
        // Clear token and redirect to login
        if (typeof localStorage !== 'undefined') {
          localStorage.removeItem('token');
        }
        
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
      
      onError(error);
      return Promise.reject(error);
    }
  );
  
  // Return API client methods
  return {
    /**
     * Make a GET request
     * @param {string} url - Request URL
     * @param {object} [params] - Query parameters
     * @param {object} [config] - Additional axios config
     * @returns {Promise} - Axios promise
     */
    get: (url, params = {}, config = {}) => {
      return instance.get(url, { ...config, params });
    },
    
    /**
     * Make a POST request
     * @param {string} url - Request URL
     * @param {object} [data] - Request body
     * @param {object} [config] - Additional axios config
     * @returns {Promise} - Axios promise
     */
    post: (url, data = {}, config = {}) => {
      return instance.post(url, data, config);
    },
    
    /**
     * Make a PUT request
     * @param {string} url - Request URL
     * @param {object} [data] - Request body
     * @param {object} [config] - Additional axios config
     * @returns {Promise} - Axios promise
     */
    put: (url, data = {}, config = {}) => {
      return instance.put(url, data, config);
    },
    
    /**
     * Make a PATCH request
     * @param {string} url - Request URL
     * @param {object} [data] - Request body
     * @param {object} [config] - Additional axios config
     * @returns {Promise} - Axios promise
     */
    patch: (url, data = {}, config = {}) => {
      return instance.patch(url, data, config);
    },
    
    /**
     * Make a DELETE request
     * @param {string} url - Request URL
     * @param {object} [config] - Additional axios config
     * @returns {Promise} - Axios promise
     */
    delete: (url, config = {}) => {
      return instance.delete(url, config);
    },
    
    /**
     * Get the underlying axios instance
     * @returns {object} - Axios instance
     */
    getInstance: () => instance
  };
}

// Create default API client
const apiClient = createApiClient();

export default apiClient;
