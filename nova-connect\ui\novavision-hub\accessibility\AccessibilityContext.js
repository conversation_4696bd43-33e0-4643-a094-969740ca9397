/**
 * Accessibility Context
 * 
 * This module provides accessibility context and provider for the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { usePerformance } from '../performance/usePerformance';
import { usePreferences } from '../preferences/PreferencesContext';

// Create accessibility context
const AccessibilityContext = createContext();

/**
 * Accessibility provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.initialSettings] - Initial accessibility settings
 * @param {Function} [props.onSettingsChanged] - Callback when settings change
 * @returns {React.ReactElement} Accessibility provider component
 */
export const AccessibilityProvider = ({
  children,
  initialSettings,
  onSettingsChanged
}) => {
  const { measureOperation } = usePerformance('AccessibilityProvider');
  const { savePreference, getPreference } = usePreferences();
  
  // State
  const [settings, setSettings] = useState({
    highContrast: false,
    largeText: false,
    reducedMotion: false,
    screenReader: false,
    keyboardNavigation: true,
    focusVisible: true,
    ...initialSettings
  });
  
  // Load settings from preferences
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedSettings = await getPreference('accessibility');
        
        if (savedSettings) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ...savedSettings
          }));
        }
      } catch (error) {
        console.error('Error loading accessibility settings:', error);
      }
    };
    
    loadSettings();
  }, [getPreference]);
  
  // Detect system preferences
  useEffect(() => {
    const detectSystemPreferences = () => {
      try {
        // Detect reduced motion
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        
        // Detect high contrast
        const prefersHighContrast = window.matchMedia('(forced-colors: active)').matches;
        
        // Update settings
        setSettings(prevSettings => ({
          ...prevSettings,
          reducedMotion: prefersReducedMotion || prevSettings.reducedMotion,
          highContrast: prefersHighContrast || prevSettings.highContrast
        }));
      } catch (error) {
        console.error('Error detecting system preferences:', error);
      }
    };
    
    detectSystemPreferences();
    
    // Add media query listeners
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(forced-colors: active)');
    
    const handleReducedMotionChange = (e) => {
      setSettings(prevSettings => ({
        ...prevSettings,
        reducedMotion: e.matches || prevSettings.reducedMotion
      }));
    };
    
    const handleHighContrastChange = (e) => {
      setSettings(prevSettings => ({
        ...prevSettings,
        highContrast: e.matches || prevSettings.highContrast
      }));
    };
    
    // Add event listeners
    if (reducedMotionQuery.addEventListener) {
      reducedMotionQuery.addEventListener('change', handleReducedMotionChange);
      highContrastQuery.addEventListener('change', handleHighContrastChange);
    } else {
      // Fallback for older browsers
      reducedMotionQuery.addListener(handleReducedMotionChange);
      highContrastQuery.addListener(handleHighContrastChange);
    }
    
    // Cleanup
    return () => {
      if (reducedMotionQuery.removeEventListener) {
        reducedMotionQuery.removeEventListener('change', handleReducedMotionChange);
        highContrastQuery.removeEventListener('change', handleHighContrastChange);
      } else {
        // Fallback for older browsers
        reducedMotionQuery.removeListener(handleReducedMotionChange);
        highContrastQuery.removeListener(handleHighContrastChange);
      }
    };
  }, []);
  
  // Save settings to preferences when changed
  useEffect(() => {
    const saveSettings = async () => {
      try {
        await savePreference('accessibility', settings);
        
        if (onSettingsChanged) {
          onSettingsChanged(settings);
        }
      } catch (error) {
        console.error('Error saving accessibility settings:', error);
      }
    };
    
    saveSettings();
  }, [settings, savePreference, onSettingsChanged]);
  
  // Apply accessibility styles
  useEffect(() => {
    const applyAccessibilityStyles = () => {
      try {
        // Apply high contrast
        if (settings.highContrast) {
          document.documentElement.classList.add('high-contrast');
        } else {
          document.documentElement.classList.remove('high-contrast');
        }
        
        // Apply large text
        if (settings.largeText) {
          document.documentElement.classList.add('large-text');
        } else {
          document.documentElement.classList.remove('large-text');
        }
        
        // Apply reduced motion
        if (settings.reducedMotion) {
          document.documentElement.classList.add('reduced-motion');
        } else {
          document.documentElement.classList.remove('reduced-motion');
        }
        
        // Apply focus visible
        if (settings.focusVisible) {
          document.documentElement.classList.add('focus-visible');
        } else {
          document.documentElement.classList.remove('focus-visible');
        }
      } catch (error) {
        console.error('Error applying accessibility styles:', error);
      }
    };
    
    applyAccessibilityStyles();
  }, [settings]);
  
  /**
   * Update accessibility settings
   * 
   * @param {Object} newSettings - New settings
   */
  const updateSettings = useCallback((newSettings) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      ...newSettings
    }));
  }, []);
  
  /**
   * Toggle setting
   * 
   * @param {string} setting - Setting to toggle
   */
  const toggleSetting = useCallback((setting) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      [setting]: !prevSettings[setting]
    }));
  }, []);
  
  /**
   * Reset settings to defaults
   */
  const resetSettings = useCallback(() => {
    setSettings({
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      screenReader: false,
      keyboardNavigation: true,
      focusVisible: true
    });
  }, []);
  
  /**
   * Check if color contrast meets WCAG requirements
   * 
   * @param {string} foreground - Foreground color
   * @param {string} background - Background color
   * @param {string} [level='AA'] - WCAG level ('A', 'AA', or 'AAA')
   * @param {boolean} [isLargeText=false] - Whether the text is large
   * @returns {boolean} Whether the contrast meets requirements
   */
  const meetsContrastRequirements = useCallback((foreground, background, level = 'AA', isLargeText = false) => {
    try {
      // Convert colors to RGB
      const getRGB = (color) => {
        // Handle hex colors
        if (color.startsWith('#')) {
          const hex = color.slice(1);
          const r = parseInt(hex.slice(0, 2), 16);
          const g = parseInt(hex.slice(2, 4), 16);
          const b = parseInt(hex.slice(4, 6), 16);
          return [r, g, b];
        }
        
        // Handle rgb/rgba colors
        if (color.startsWith('rgb')) {
          const values = color.match(/\d+/g);
          return [parseInt(values[0]), parseInt(values[1]), parseInt(values[2])];
        }
        
        // Handle named colors
        const tempElement = document.createElement('div');
        tempElement.style.color = color;
        document.body.appendChild(tempElement);
        const computedColor = getComputedStyle(tempElement).color;
        document.body.removeChild(tempElement);
        const values = computedColor.match(/\d+/g);
        return [parseInt(values[0]), parseInt(values[1]), parseInt(values[2])];
      };
      
      // Calculate relative luminance
      const getLuminance = (rgb) => {
        const [r, g, b] = rgb.map(value => {
          value /= 255;
          return value <= 0.03928 ? value / 12.92 : Math.pow((value + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
      };
      
      // Calculate contrast ratio
      const foregroundRGB = getRGB(foreground);
      const backgroundRGB = getRGB(background);
      const foregroundLuminance = getLuminance(foregroundRGB);
      const backgroundLuminance = getLuminance(backgroundRGB);
      const contrastRatio = (Math.max(foregroundLuminance, backgroundLuminance) + 0.05) / (Math.min(foregroundLuminance, backgroundLuminance) + 0.05);
      
      // Check against WCAG requirements
      if (level === 'A') {
        return isLargeText ? contrastRatio >= 3 : contrastRatio >= 4.5;
      } else if (level === 'AA') {
        return isLargeText ? contrastRatio >= 4.5 : contrastRatio >= 7;
      } else if (level === 'AAA') {
        return isLargeText ? contrastRatio >= 7 : contrastRatio >= 9;
      }
      
      return false;
    } catch (error) {
      console.error('Error checking contrast requirements:', error);
      return false;
    }
  }, []);
  
  /**
   * Get contrast ratio between two colors
   * 
   * @param {string} foreground - Foreground color
   * @param {string} background - Background color
   * @returns {number} Contrast ratio
   */
  const getContrastRatio = useCallback((foreground, background) => {
    try {
      // Convert colors to RGB
      const getRGB = (color) => {
        // Handle hex colors
        if (color.startsWith('#')) {
          const hex = color.slice(1);
          const r = parseInt(hex.slice(0, 2), 16);
          const g = parseInt(hex.slice(2, 4), 16);
          const b = parseInt(hex.slice(4, 6), 16);
          return [r, g, b];
        }
        
        // Handle rgb/rgba colors
        if (color.startsWith('rgb')) {
          const values = color.match(/\d+/g);
          return [parseInt(values[0]), parseInt(values[1]), parseInt(values[2])];
        }
        
        // Handle named colors
        const tempElement = document.createElement('div');
        tempElement.style.color = color;
        document.body.appendChild(tempElement);
        const computedColor = getComputedStyle(tempElement).color;
        document.body.removeChild(tempElement);
        const values = computedColor.match(/\d+/g);
        return [parseInt(values[0]), parseInt(values[1]), parseInt(values[2])];
      };
      
      // Calculate relative luminance
      const getLuminance = (rgb) => {
        const [r, g, b] = rgb.map(value => {
          value /= 255;
          return value <= 0.03928 ? value / 12.92 : Math.pow((value + 0.055) / 1.055, 2.4);
        });
        return 0.2126 * r + 0.7152 * g + 0.0722 * b;
      };
      
      // Calculate contrast ratio
      const foregroundRGB = getRGB(foreground);
      const backgroundRGB = getRGB(background);
      const foregroundLuminance = getLuminance(foregroundRGB);
      const backgroundLuminance = getLuminance(backgroundRGB);
      return (Math.max(foregroundLuminance, backgroundLuminance) + 0.05) / (Math.min(foregroundLuminance, backgroundLuminance) + 0.05);
    } catch (error) {
      console.error('Error getting contrast ratio:', error);
      return 1;
    }
  }, []);
  
  // Create context value
  const contextValue = {
    settings,
    updateSettings,
    toggleSetting,
    resetSettings,
    meetsContrastRequirements,
    getContrastRatio
  };
  
  return (
    <AccessibilityContext.Provider value={contextValue}>
      {children}
    </AccessibilityContext.Provider>
  );
};

AccessibilityProvider.propTypes = {
  children: PropTypes.node.isRequired,
  initialSettings: PropTypes.object,
  onSettingsChanged: PropTypes.func
};

/**
 * Use accessibility hook
 * 
 * @returns {Object} Accessibility context
 */
export const useAccessibility = () => {
  const context = useContext(AccessibilityContext);
  
  if (!context) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  
  return context;
};

export default AccessibilityContext;
