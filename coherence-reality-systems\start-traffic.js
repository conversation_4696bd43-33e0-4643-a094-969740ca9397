#!/usr/bin/env node

const KetherNetTrafficSimulator = require('./kethernet-traffic-simulator');
const KetherNetLoadTester = require('./kethernet-load-test');

console.log('🔥 KETHERNET TRAFFIC SIMULATION SUITE');
console.log('=====================================');
console.log('Choose simulation type:');
console.log('1. Light Traffic (5 req/s for 60s)');
console.log('2. Medium Traffic (15 req/s for 60s)');
console.log('3. Heavy Traffic (50 req/s for 30s)');
console.log('4. Load Test (100 concurrent requests)');
console.log('5. Stress Test (500 concurrent requests)');
console.log('6. Custom Traffic');

const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Enter choice (1-6): ', async (choice) => {
  rl.close();
  
  switch(choice) {
    case '1':
      console.log('\n🌱 Starting Light Traffic Simulation...');
      const lightSim = new KetherNetTrafficSimulator();
      await lightSim.runTrafficSimulation(60000, 5);
      break;
      
    case '2':
      console.log('\n🚀 Starting Medium Traffic Simulation...');
      const mediumSim = new KetherNetTrafficSimulator();
      await mediumSim.runTrafficSimulation(60000, 15);
      break;
      
    case '3':
      console.log('\n🔥 Starting Heavy Traffic Simulation...');
      const heavySim = new KetherNetTrafficSimulator();
      await heavySim.runTrafficSimulation(30000, 50);
      break;
      
    case '4':
      console.log('\n⚡ Starting Load Test...');
      const loadTester = new KetherNetLoadTester();
      await loadTester.runLoadTest(100, 10);
      break;
      
    case '5':
      console.log('\n💥 Starting Stress Test...');
      const stressTester = new KetherNetLoadTester();
      await stressTester.runLoadTest(500, 25);
      break;
      
    case '6':
      console.log('\n🛠️  Custom Traffic - Edit the script for custom parameters');
      const customSim = new KetherNetTrafficSimulator();
      await customSim.runTrafficSimulation(30000, 10);
      break;
      
    default:
      console.log('❌ Invalid choice. Running default light traffic...');
      const defaultSim = new KetherNetTrafficSimulator();
      await defaultSim.runTrafficSimulation(60000, 5);
  }
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n🛑 Traffic simulation stopped by user');
  process.exit(0);
});
