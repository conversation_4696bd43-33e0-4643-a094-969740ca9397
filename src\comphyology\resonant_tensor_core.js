/**
 * Resonant Tensor Core
 * 
 * This module provides a resonance-locked wrapper for tensor operations,
 * enforcing the 3-6-9-12-13 pattern across all tensor operations.
 * It implements the Law of Resonant Encapsulation:
 * 
 * "No system shall externalize what its resonance core has not first harmonized."
 */

const EventEmitter = require('events');
const ResonanceValidator = require('./resonance_validator');

/**
 * Resonant Tensor Core class
 */
class ResonantTensorCore extends EventEmitter {
  /**
   * Constructor
   * @param {Object} tensorCore - The tensor core to wrap
   * @param {Object} options - Configuration options
   */
  constructor(tensorCore, options = {}) {
    super();
    
    if (!tensorCore) {
      throw new Error('Tensor core is required');
    }
    
    this.tensorCore = tensorCore;
    this.options = {
      strictMode: false, // Whether to reject non-resonant values (true) or harmonize them (false)
      logValidation: true, // Whether to log validation results
      resonanceLock: true, // Whether to enforce resonance constraints
      trackDrift: true, // Whether to track resonance drift
      ...options
    };
    
    // Initialize resonance validator
    this.validator = new ResonanceValidator({
      strictMode: this.options.strictMode,
      logValidation: this.options.logValidation,
      resonanceLock: this.options.resonanceLock
    });
    
    // Initialize resonance metrics
    this.metrics = {
      validations: 0,
      harmonizations: 0,
      rejections: 0,
      totalDrift: 0,
      averageDrift: 0,
      operations: {
        tensorProduct: 0,
        directSum: 0,
        fusion: 0,
        scaling: 0
      }
    };
  }
  
  /**
   * Create a tensor with resonance validation
   * @param {Array} dimensions - Tensor dimensions
   * @param {Array} values - Tensor values
   * @param {string} domain - Tensor domain
   * @returns {Object} - Resonance-validated tensor
   */
  createTensor(dimensions, values, domain = 'universal') {
    // Validate values against resonance constraints
    const validationResult = this.validator.validateTensor({
      dimensions,
      values,
      domain
    });
    
    // Update metrics
    this.metrics.validations++;
    if (validationResult.wasHarmonized) {
      this.metrics.harmonizations++;
      this.metrics.totalDrift += validationResult.resonanceDrift;
      this.metrics.averageDrift = this.metrics.totalDrift / this.metrics.harmonizations;
    } else if (!validationResult.isValid) {
      this.metrics.rejections++;
    }
    
    // If validation failed and strict mode is enabled, throw an error
    if (!validationResult.isValid && this.options.strictMode) {
      const error = new Error(`Tensor validation failed: ${validationResult.error}`);
      error.validationResult = validationResult;
      
      this.emit('validation-error', {
        error,
        validationResult
      });
      
      throw error;
    }
    
    // Create tensor with harmonized values if needed
    const tensor = this.tensorCore.createTensor(
      dimensions,
      validationResult.harmonizedTensor ? validationResult.harmonizedTensor.values : values,
      domain
    );
    
    // Add resonance metadata
    tensor.resonance = {
      isResonant: validationResult.isResonant,
      wasHarmonized: validationResult.wasHarmonized,
      resonanceDrift: validationResult.resonanceDrift
    };
    
    // Emit validation event
    this.emit('tensor-validated', {
      tensor,
      validationResult
    });
    
    return tensor;
  }
  
  /**
   * Apply tensor product with resonance validation
   * @param {Object} tensorA - First tensor
   * @param {Object} tensorB - Second tensor
   * @returns {Object} - Resonance-validated tensor product
   */
  tensorProduct(tensorA, tensorB) {
    // Apply tensor product
    const resultTensor = this.tensorCore.tensorProduct(tensorA, tensorB);
    
    // Validate result against resonance constraints
    const validationResult = this.validator.validateTensor(resultTensor);
    
    // Update metrics
    this.metrics.validations++;
    this.metrics.operations.tensorProduct++;
    if (validationResult.wasHarmonized) {
      this.metrics.harmonizations++;
      this.metrics.totalDrift += validationResult.resonanceDrift;
      this.metrics.averageDrift = this.metrics.totalDrift / this.metrics.harmonizations;
    } else if (!validationResult.isValid) {
      this.metrics.rejections++;
    }
    
    // If validation failed and strict mode is enabled, throw an error
    if (!validationResult.isValid && this.options.strictMode) {
      const error = new Error(`Tensor product validation failed: ${validationResult.error}`);
      error.validationResult = validationResult;
      
      this.emit('validation-error', {
        error,
        validationResult,
        operation: 'tensorProduct',
        tensorA,
        tensorB
      });
      
      throw error;
    }
    
    // Use harmonized tensor if needed
    const finalTensor = validationResult.harmonizedTensor || resultTensor;
    
    // Add resonance metadata
    finalTensor.resonance = {
      isResonant: validationResult.isResonant,
      wasHarmonized: validationResult.wasHarmonized,
      resonanceDrift: validationResult.resonanceDrift,
      operation: 'tensorProduct'
    };
    
    // Emit validation event
    this.emit('tensor-validated', {
      tensor: finalTensor,
      validationResult,
      operation: 'tensorProduct',
      tensorA,
      tensorB
    });
    
    return finalTensor;
  }
  
  /**
   * Apply direct sum with resonance validation
   * @param {Object} tensorA - First tensor
   * @param {Object} tensorB - Second tensor
   * @returns {Object} - Resonance-validated direct sum
   */
  directSum(tensorA, tensorB) {
    // Apply direct sum
    const resultTensor = this.tensorCore.directSum(tensorA, tensorB);
    
    // Validate result against resonance constraints
    const validationResult = this.validator.validateTensor(resultTensor);
    
    // Update metrics
    this.metrics.validations++;
    this.metrics.operations.directSum++;
    if (validationResult.wasHarmonized) {
      this.metrics.harmonizations++;
      this.metrics.totalDrift += validationResult.resonanceDrift;
      this.metrics.averageDrift = this.metrics.totalDrift / this.metrics.harmonizations;
    } else if (!validationResult.isValid) {
      this.metrics.rejections++;
    }
    
    // If validation failed and strict mode is enabled, throw an error
    if (!validationResult.isValid && this.options.strictMode) {
      const error = new Error(`Direct sum validation failed: ${validationResult.error}`);
      error.validationResult = validationResult;
      
      this.emit('validation-error', {
        error,
        validationResult,
        operation: 'directSum',
        tensorA,
        tensorB
      });
      
      throw error;
    }
    
    // Use harmonized tensor if needed
    const finalTensor = validationResult.harmonizedTensor || resultTensor;
    
    // Add resonance metadata
    finalTensor.resonance = {
      isResonant: validationResult.isResonant,
      wasHarmonized: validationResult.wasHarmonized,
      resonanceDrift: validationResult.resonanceDrift,
      operation: 'directSum'
    };
    
    // Emit validation event
    this.emit('tensor-validated', {
      tensor: finalTensor,
      validationResult,
      operation: 'directSum',
      tensorA,
      tensorB
    });
    
    return finalTensor;
  }
  
  /**
   * Fuse engines with resonance validation
   * @param {Object} csde_tensor - CSDE tensor
   * @param {Object} csfe_tensor - CSFE tensor
   * @param {Object} csme_tensor - CSME tensor
   * @returns {Object} - Resonance-validated fused tensor
   */
  fuseEngines(csde_tensor, csfe_tensor, csme_tensor) {
    // Apply engine fusion
    const resultTensor = this.tensorCore.fuseEngines(csde_tensor, csfe_tensor, csme_tensor);
    
    // Validate result against resonance constraints
    const validationResult = this.validator.validateTensor(resultTensor);
    
    // Update metrics
    this.metrics.validations++;
    this.metrics.operations.fusion++;
    if (validationResult.wasHarmonized) {
      this.metrics.harmonizations++;
      this.metrics.totalDrift += validationResult.resonanceDrift;
      this.metrics.averageDrift = this.metrics.totalDrift / this.metrics.harmonizations;
    } else if (!validationResult.isValid) {
      this.metrics.rejections++;
    }
    
    // If validation failed and strict mode is enabled, throw an error
    if (!validationResult.isValid && this.options.strictMode) {
      const error = new Error(`Engine fusion validation failed: ${validationResult.error}`);
      error.validationResult = validationResult;
      
      this.emit('validation-error', {
        error,
        validationResult,
        operation: 'fuseEngines',
        csde_tensor,
        csfe_tensor,
        csme_tensor
      });
      
      throw error;
    }
    
    // Use harmonized tensor if needed
    const finalTensor = validationResult.harmonizedTensor || resultTensor;
    
    // Add resonance metadata
    finalTensor.resonance = {
      isResonant: validationResult.isResonant,
      wasHarmonized: validationResult.wasHarmonized,
      resonanceDrift: validationResult.resonanceDrift,
      operation: 'fuseEngines'
    };
    
    // Emit validation event
    this.emit('tensor-validated', {
      tensor: finalTensor,
      validationResult,
      operation: 'fuseEngines',
      csde_tensor,
      csfe_tensor,
      csme_tensor
    });
    
    return finalTensor;
  }
  
  /**
   * Get resonance metrics
   * @returns {Object} - Resonance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      resonanceLockEnabled: this.options.resonanceLock,
      strictModeEnabled: this.options.strictMode
    };
  }
}

module.exports = ResonantTensorCore;
