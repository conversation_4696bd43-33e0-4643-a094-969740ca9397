/**
 * CSDE GCP Integration Test
 *
 * This script tests the integration of the CSDE engine with the GCP environment.
 * It can run against either a simulation environment or a live GCP environment.
 *
 * Usage:
 *   node gcp_integration_test.js             # Run with simulation environment
 *   node gcp_integration_test.js --live      # Run with live GCP environment
 *   node gcp_integration_test.js --project=my-project  # Specify GCP project
 */

const axios = require('axios');
const { CSDEEngine } = require('./index');

// Parse command line arguments
const args = process.argv.slice(2);
const useLiveGcp = args.includes('--live');
const projectArg = args.find(arg => arg.startsWith('--project='));
const projectId = projectArg ? projectArg.split('=')[1] : process.env.GCP_PROJECT_ID || 'novafuse-dev';

// GCP environment configuration
const config = {
  simulation: {
    sccUrl: 'http://localhost:8081', // Security Command Center
    iamUrl: 'http://localhost:8082', // Cloud IAM
    bqUrl: 'http://localhost:8083',  // BigQuery
  },
  live: {
    sccUrl: `https://securitycenter.googleapis.com/v1/projects/${projectId}`,
    iamUrl: `https://iam.googleapis.com/v1/projects/${projectId}`,
    bqUrl: `https://bigquery.googleapis.com/bigquery/v2/projects/${projectId}`,
  }
};

// Use live or simulation configuration
const gcpConfig = useLiveGcp ? config.live : config.simulation;

console.log(`Using ${useLiveGcp ? 'LIVE' : 'SIMULATION'} GCP environment`);
if (useLiveGcp) {
  console.log(`Project ID: ${projectId}`);
}

// Initialize CSDE Engine
const csdeEngine = new CSDEEngine();

// Fetch data from GCP environment
async function fetchGcpData() {
  try {
    console.log(`Fetching data from GCP ${useLiveGcp ? 'live' : 'simulation'} environment...`);

    // Configure axios headers for live environment
    const axiosConfig = useLiveGcp ? {
      headers: {
        'Authorization': `Bearer ${process.env.GOOGLE_APPLICATION_CREDENTIALS || ''}`,
        'Content-Type': 'application/json'
      }
    } : {};

    // Fetch Security Command Center findings
    let sccEndpoint = useLiveGcp ? '/findings' : '/findings';
    const sccResponse = await axios.get(`${gcpConfig.sccUrl}${sccEndpoint}`, axiosConfig).catch(err => {
      console.log(`Error fetching SCC findings: ${err.message}`);
      console.log('Using mock data for SCC findings');
      return { data: { findings: [] } };
    });

    // Fetch IAM roles and permissions
    let iamEndpoint = useLiveGcp ? '/roles' : '/roles';
    const iamResponse = await axios.get(`${gcpConfig.iamUrl}${iamEndpoint}`, axiosConfig).catch(err => {
      console.log(`Error fetching IAM roles: ${err.message}`);
      console.log('Using mock data for IAM roles');
      return { data: { roles: [] } };
    });

    // Fetch BigQuery datasets
    let bqEndpoint = useLiveGcp ? '/datasets' : '/datasets';
    const bqResponse = await axios.get(`${gcpConfig.bqUrl}${bqEndpoint}`, axiosConfig).catch(err => {
      console.log(`Error fetching BigQuery datasets: ${err.message}`);
      console.log('Using mock data for BigQuery datasets');
      return { data: { datasets: [] } };
    });

    // Process SCC findings
    const findings = sccResponse.data.findings || [];
    const securityIssues = findings.map(finding => ({
      id: finding.id,
      name: finding.name,
      description: finding.description,
      severity: finding.severity,
      status: finding.status,
      service: finding.service
    }));

    // Process IAM roles
    const roles = iamResponse.data.roles || [];
    const iamIssues = roles
      .filter(role => role.status !== 'optimal')
      .map(role => ({
        id: role.id,
        name: role.name,
        description: role.description,
        severity: role.severity || 'medium',
        status: role.status,
        service: 'Cloud IAM'
      }));

    // Process BigQuery datasets
    const datasets = bqResponse.data.datasets || [];
    const bqIssues = datasets
      .filter(dataset => dataset.status !== 'optimal')
      .map(dataset => ({
        id: dataset.id,
        name: dataset.name,
        description: dataset.description,
        severity: dataset.severity || 'medium',
        status: dataset.status,
        service: 'BigQuery'
      }));

    // If no real data is available, use mock data
    if (securityIssues.length === 0 && iamIssues.length === 0 && bqIssues.length === 0) {
      console.log('No real data available, using mock data');

      return {
        integrationScore: 0.85,
        services: [
          {
            id: 'GCP-IAM-1',
            name: 'IAM Role Configuration',
            description: 'IAM roles need to be configured with least privilege',
            severity: 'high',
            status: 'non-optimal',
            service: 'Cloud IAM'
          },
          {
            id: 'GCP-VPC-1',
            name: 'VPC Network Security',
            description: 'VPC network security needs to be enhanced',
            severity: 'medium',
            status: 'partial',
            service: 'VPC Network'
          },
          {
            id: 'GCP-KMS-1',
            name: 'Key Management',
            description: 'Cloud KMS keys need to be properly managed',
            severity: 'high',
            status: 'optimal',
            service: 'Cloud KMS'
          }
        ]
      };
    }

    // Combine all issues
    const allIssues = [...securityIssues, ...iamIssues, ...bqIssues];

    // Calculate integration score based on issues
    const totalIssues = allIssues.length;
    const criticalIssues = allIssues.filter(issue => issue.severity === 'critical').length;
    const highIssues = allIssues.filter(issue => issue.severity === 'high').length;

    // Higher score is better (1.0 = perfect)
    const integrationScore = totalIssues === 0 ?
      1.0 :
      Math.max(0.1, 1.0 - (criticalIssues * 0.2 + highIssues * 0.1 + (totalIssues - criticalIssues - highIssues) * 0.05));

    return {
      integrationScore,
      services: allIssues
    };
  } catch (error) {
    console.error('Error fetching GCP data:', error.message);
    throw error;
  }
}

// Mock compliance data
const complianceData = {
  complianceScore: 0.75,
  controls: [
    {
      id: 'AC-2',
      name: 'Account Management',
      description: 'The organization needs to implement account management procedures',
      severity: 'high',
      status: 'non-compliant',
      framework: 'NIST 800-53'
    },
    {
      id: 'CM-7',
      name: 'Least Functionality',
      description: 'The organization needs to configure systems to provide only essential capabilities',
      severity: 'medium',
      status: 'partial',
      framework: 'NIST 800-53'
    },
    {
      id: 'SC-7',
      name: 'Boundary Protection',
      description: 'The organization needs to implement boundary protection mechanisms',
      severity: 'high',
      status: 'compliant',
      framework: 'NIST 800-53'
    }
  ]
};

// Mock Cyber-Safety data
const cyberSafetyData = {
  safetyScore: 0.65,
  controls: [
    {
      id: 'CS-P3-1',
      name: 'Self-Destructing Compliance Servers',
      description: 'Implement self-destructing compliance servers with hardware-enforced geo-fencing',
      severity: 'high',
      status: 'not-implemented',
      pillar: 'Pillar 3'
    },
    {
      id: 'CS-P9-1',
      name: 'Post-Quantum Immutable Compliance Journal',
      description: 'Implement post-quantum immutable compliance journal',
      severity: 'medium',
      status: 'partial',
      pillar: 'Pillar 9'
    },
    {
      id: 'CS-P12-1',
      name: 'C-Suite Directive to Code Compiler',
      description: 'Implement C-Suite Directive to Code Compiler',
      severity: 'medium',
      status: 'implemented',
      pillar: 'Pillar 12'
    }
  ]
};

// Run the test
async function runTest() {
  try {
    console.log('Starting CSDE GCP Integration Test...');

    // Fetch GCP data
    const gcpData = await fetchGcpData();

    // Calculate CSDE
    console.log('Calculating CSDE...');
    const result = csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);

    // Display results
    console.log('\nCSDE Calculation Results:');
    console.log('-------------------------');
    console.log(`CSDE Value: ${result.csdeValue.toFixed(2)}`);
    console.log(`Performance Factor: ${result.performanceFactor}x`);
    console.log('\nComponent Values:');
    console.log(`NIST Component: ${result.nistComponent.processedValue.toFixed(2)}`);
    console.log(`GCP Component: ${result.gcpComponent.processedValue.toFixed(2)}`);
    console.log(`Cyber-Safety Component: ${result.cyberSafetyComponent.processedValue.toFixed(2)}`);
    console.log(`Tensor Product: ${result.tensorProduct.normalizedValue.toFixed(2)}`);
    console.log(`Fusion Result: ${result.fusionResult.fusionValue.toFixed(2)}`);

    // Display remediation actions
    console.log('\nRemediation Actions:');
    console.log('-------------------');
    result.remediationActions.forEach((action, index) => {
      console.log(`\n${index + 1}. ${action.title} (${action.priority.toUpperCase()})`);
      console.log(`   Type: ${action.type}`);
      console.log(`   Description: ${action.description}`);
      console.log(`   Automation Potential: ${action.automationPotential}`);
      console.log(`   Estimated Effort: ${action.estimatedEffort}`);
      console.log('   Steps:');
      action.steps.forEach((step, stepIndex) => {
        console.log(`     ${stepIndex + 1}. ${step}`);
      });
    });

    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
runTest();
