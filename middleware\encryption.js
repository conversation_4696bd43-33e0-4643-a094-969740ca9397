/**
 * NovaFuse API Superstore Encryption Middleware
 * 
 * This middleware provides encryption capabilities for API requests and responses.
 * It can be configured to encrypt specific fields in requests and responses,
 * or to encrypt the entire request/response body.
 */

const encryption = require('../utils/encryption');
const config = require('../config');

/**
 * Middleware to encrypt sensitive fields in the response
 * 
 * @param {Array<string>} fields - Array of field paths to encrypt (e.g., ['user.password', 'payment.cardNumber'])
 * @returns {Function} - Express middleware function
 */
function encryptResponseFields(fields = []) {
  return (req, res, next) => {
    // Store the original res.json function
    const originalJson = res.json;
    
    // Override res.json to encrypt fields before sending
    res.json = function(data) {
      // If no data or no fields to encrypt, proceed normally
      if (!data || fields.length === 0) {
        return originalJson.call(this, data);
      }
      
      // Clone the data to avoid modifying the original
      const encryptedData = JSON.parse(JSON.stringify(data));
      
      // Encrypt each specified field
      for (const fieldPath of fields) {
        const parts = fieldPath.split('.');
        let current = encryptedData;
        
        // Navigate to the parent object of the field
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]] === undefined) {
            break;
          }
          current = current[parts[i]];
        }
        
        // Get the field name (last part of the path)
        const fieldName = parts[parts.length - 1];
        
        // If the field exists, encrypt it
        if (current && current[fieldName] !== undefined) {
          const value = current[fieldName];
          
          // Skip if the value is null or already encrypted
          if (value === null || (typeof value === 'string' && value.includes(':'))) {
            continue;
          }
          
          // Encrypt the field value
          const { encrypted, iv, authTag } = encryption.encryptSymmetric(
            value,
            config.encryption.responseKey
          );
          
          // Replace the field value with the encrypted value
          current[fieldName] = `${encrypted}:${iv}:${authTag}`;
        }
      }
      
      // Call the original json method with the encrypted data
      return originalJson.call(this, encryptedData);
    };
    
    next();
  };
}

/**
 * Middleware to decrypt sensitive fields in the request
 * 
 * @param {Array<string>} fields - Array of field paths to decrypt (e.g., ['user.password', 'payment.cardNumber'])
 * @returns {Function} - Express middleware function
 */
function decryptRequestFields(fields = []) {
  return (req, res, next) => {
    // If no fields to decrypt, proceed normally
    if (fields.length === 0) {
      return next();
    }
    
    // Decrypt fields in request body
    if (req.body) {
      for (const fieldPath of fields) {
        const parts = fieldPath.split('.');
        let current = req.body;
        
        // Navigate to the parent object of the field
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]] === undefined) {
            break;
          }
          current = current[parts[i]];
        }
        
        // Get the field name (last part of the path)
        const fieldName = parts[parts.length - 1];
        
        // If the field exists and is encrypted, decrypt it
        if (current && typeof current[fieldName] === 'string') {
          const value = current[fieldName];
          
          // Check if the value is encrypted (contains two colons)
          if (value.split(':').length === 3) {
            const [encrypted, iv, authTag] = value.split(':');
            
            try {
              // Decrypt the field value
              const decrypted = encryption.decryptSymmetric(
                encrypted,
                iv,
                authTag,
                config.encryption.requestKey
              );
              
              // Replace the field value with the decrypted value
              current[fieldName] = decrypted;
            } catch (error) {
              // If decryption fails, leave the value as is
              console.error(`Failed to decrypt field ${fieldPath}:`, error);
            }
          }
        }
      }
    }
    
    next();
  };
}

/**
 * Middleware to encrypt the entire response body
 * 
 * @returns {Function} - Express middleware function
 */
function encryptResponse() {
  return (req, res, next) => {
    // Store the original res.json function
    const originalJson = res.json;
    
    // Override res.json to encrypt the entire response
    res.json = function(data) {
      // If no data, proceed normally
      if (!data) {
        return originalJson.call(this, data);
      }
      
      // Encrypt the entire response
      const { encrypted, iv, authTag } = encryption.encryptSymmetric(
        JSON.stringify(data),
        config.encryption.responseKey
      );
      
      // Create the encrypted response
      const encryptedResponse = {
        encrypted,
        iv,
        authTag,
        isEncrypted: true
      };
      
      // Call the original json method with the encrypted response
      return originalJson.call(this, encryptedResponse);
    };
    
    next();
  };
}

/**
 * Middleware to decrypt the entire request body
 * 
 * @returns {Function} - Express middleware function
 */
function decryptRequest() {
  return (req, res, next) => {
    // If no body or not encrypted, proceed normally
    if (!req.body || !req.body.isEncrypted) {
      return next();
    }
    
    const { encrypted, iv, authTag } = req.body;
    
    try {
      // Decrypt the request body
      const decrypted = encryption.decryptSymmetric(
        encrypted,
        iv,
        authTag,
        config.encryption.requestKey
      );
      
      // Parse the decrypted JSON
      req.body = JSON.parse(decrypted);
    } catch (error) {
      // If decryption fails, return an error
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Failed to decrypt request body'
      });
    }
    
    next();
  };
}

/**
 * Middleware to encrypt sensitive data in logs
 * 
 * @param {Array<string>} fields - Array of field paths to mask in logs
 * @returns {Function} - Express middleware function
 */
function maskSensitiveData(fields = []) {
  return (req, res, next) => {
    // If no fields to mask, proceed normally
    if (fields.length === 0) {
      return next();
    }
    
    // Clone the request body to avoid modifying the original
    if (req.body) {
      const maskedBody = JSON.parse(JSON.stringify(req.body));
      
      // Mask each specified field
      for (const fieldPath of fields) {
        const parts = fieldPath.split('.');
        let current = maskedBody;
        
        // Navigate to the parent object of the field
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]] === undefined) {
            break;
          }
          current = current[parts[i]];
        }
        
        // Get the field name (last part of the path)
        const fieldName = parts[parts.length - 1];
        
        // If the field exists, mask it
        if (current && current[fieldName] !== undefined) {
          current[fieldName] = encryption.maskSensitiveData(current[fieldName].toString());
        }
      }
      
      // Attach the masked body to the request for logging
      req.maskedBody = maskedBody;
    }
    
    next();
  };
}

module.exports = {
  encryptResponseFields,
  decryptRequestFields,
  encryptResponse,
  decryptRequest,
  maskSensitiveData
};
