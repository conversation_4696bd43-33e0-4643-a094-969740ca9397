/**
 * NovaConnect Test Runner
 * 
 * This script runs all tests for NovaConnect, including performance tests,
 * integration tests, and security tests.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testCategories: [
    { name: 'Performance Tests', dir: 'tests/performance', pattern: 'tests/performance/**/*.test.js' },
    { name: 'Integration Tests', dir: 'tests/integration', pattern: 'tests/integration/**/*.test.js' },
    { name: 'Security Tests', dir: 'tests/security', pattern: 'tests/security/**/*.test.js' }
  ],
  resultsDir: './test-results',
  jestConfig: './jest.config.js'
};

// Ensure results directory exists
if (!fs.existsSync(config.resultsDir)) {
  fs.mkdirSync(config.resultsDir, { recursive: true });
}

// Initialize results
const results = {
  startTime: new Date(),
  endTime: null,
  categories: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
  }
};

/**
 * Run tests for a specific category
 */
function runTestCategory(category) {
  console.log(`\n=== Running ${category.name} ===\n`);
  
  const startTime = new Date();
  const categoryResult = {
    name: category.name,
    startTime,
    endTime: null,
    success: false,
    skipped: false,
    testCount: 0,
    passedCount: 0,
    failedCount: 0,
    skippedCount: 0,
    duration: 0
  };
  
  try {
    // Run Jest for the specific test pattern
    const command = `npx jest --config=${config.jestConfig} ${category.pattern} --colors`;
    execSync(command, { stdio: 'inherit' });
    
    categoryResult.success = true;
    
    console.log(`✅ ${category.name} passed`);
  } catch (error) {
    categoryResult.success = false;
    console.error(`❌ ${category.name} failed`);
  }
  
  categoryResult.endTime = new Date();
  categoryResult.duration = (categoryResult.endTime - categoryResult.startTime) / 1000;
  
  return categoryResult;
}

/**
 * Generate a summary report
 */
function generateSummaryReport(results) {
  const duration = (results.endTime - results.startTime) / 1000;
  
  let report = `# NovaConnect Test Summary\n\n`;
  report += `## Overview\n\n`;
  report += `- **Start Time**: ${results.startTime.toISOString()}\n`;
  report += `- **End Time**: ${results.endTime.toISOString()}\n`;
  report += `- **Duration**: ${duration.toFixed(2)} seconds\n\n`;
  
  report += `## Test Categories\n\n`;
  report += `| Category | Status | Duration |\n`;
  report += `|----------|--------|----------|\n`;
  
  results.categories.forEach(category => {
    const status = category.success ? '✅ PASS' : category.skipped ? '⏭️ SKIP' : '❌ FAIL';
    report += `| ${category.name} | ${status} | ${category.duration.toFixed(2)}s |\n`;
  });
  
  return report;
}

// Main execution
console.log('Starting NovaConnect test suite...');

// Run each test category
for (const category of config.testCategories) {
  const categoryResult = runTestCategory(category);
  results.categories.push(categoryResult);
}

results.endTime = new Date();

// Generate summary report
const summaryReport = generateSummaryReport(results);
fs.writeFileSync(path.join(config.resultsDir, 'summary.md'), summaryReport);

// Print summary
console.log('\n=== Test Summary ===\n');
console.log(summaryReport);

// Exit with appropriate code
const hasFailures = results.categories.some(category => !category.success && !category.skipped);
process.exit(hasFailures ? 1 : 0);
