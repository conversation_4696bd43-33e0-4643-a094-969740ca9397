/**
 * Standalone API Connection Test for NovaConnect Universal API Connector
 * 
 * This test uses mocks to test the API connection functionality without relying on external services.
 */

const axios = require('axios');
const MockAdapter = require('axios-mock-adapter');

// Create a mock for axios
const mock = new MockAdapter(axios);

// Test data
const testConnector = {
  metadata: {
    name: 'Test Connector',
    version: '1.0.0',
    category: 'Test',
    description: 'Test connector',
    author: 'NovaGRC',
    tags: ['test']
  },
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true,
        sensitive: true
      },
      headerName: {
        type: 'string',
        description: 'Header name for the API key',
        required: true,
        default: 'X-API-Key'
      }
    },
    testConnection: {
      endpoint: '/health',
      method: 'GET',
      expectedResponse: {
        status: 200
      }
    }
  },
  configuration: {
    baseUrl: 'http://localhost:3005',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 30000,
    retryPolicy: {
      maxRetries: 3,
      backoffStrategy: 'exponential'
    }
  },
  endpoints: [
    {
      id: 'getResource',
      name: 'Get Resource',
      path: '/api-key/resource',
      method: 'GET',
      parameters: {
        query: {},
        path: {},
        body: {}
      },
      response: {
        successCode: 200
      }
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getResource',
      targetSystem: 'NovaGRC',
      targetEntity: 'Resource',
      transformations: [
        {
          source: '$.data.id',
          target: 'resourceId',
          transform: 'identity'
        },
        {
          source: '$.data.name',
          target: 'resourceName',
          transform: 'identity'
        }
      ]
    }
  ]
};

const testCredential = {
  name: 'Test Credential',
  connectorId: 'test-connector-id',
  authType: 'API_KEY',
  credentials: {
    apiKey: 'valid-api-key',
    headerName: 'X-API-Key'
  }
};

// Mock API responses
mock.onGet('http://localhost:3005/api-key/resource').reply((config) => {
  const apiKey = config.headers['X-API-Key'];
  
  if (!apiKey) {
    return [401, { error: 'API key is required' }];
  }
  
  if (apiKey !== 'valid-api-key') {
    return [403, { error: 'Invalid API key' }];
  }
  
  return [200, { success: true, data: { id: 'resource-1', name: 'Test Resource' } }];
});

// Mock connector registry
mock.onGet('/connectors/test-connector-id').reply(200, testConnector);

// Mock auth service
mock.onGet('/credentials/test-credential-id/decrypt').reply(200, {
  id: 'test-credential-id',
  authType: 'API_KEY',
  credentials: testCredential.credentials
});

// Mock connector executor
mock.onPost('/execute/test-connector-id/getResource').reply(async (config) => {
  const data = JSON.parse(config.data);
  const { credentialId } = data;
  
  if (credentialId !== 'test-credential-id') {
    return [404, { error: 'Credential not found' }];
  }
  
  try {
    // Get the connector
    const connectorResponse = await axios.get('/connectors/test-connector-id');
    const connector = connectorResponse.data;
    
    // Get the endpoint
    const endpoint = connector.endpoints.find(e => e.id === 'getResource');
    if (!endpoint) {
      return [404, { error: 'Endpoint not found' }];
    }
    
    // Get the credentials
    const credentialResponse = await axios.get(`/credentials/${credentialId}/decrypt`);
    const credentials = credentialResponse.data.credentials;
    
    // Build the request URL
    const url = `${connector.configuration.baseUrl}${endpoint.path}`;
    
    // Make the request to the API
    const apiResponse = await axios.get(url, {
      headers: {
        [credentials.headerName]: credentials.apiKey
      }
    });
    
    // Transform the response
    const mapping = connector.mappings.find(m => m.sourceEndpoint === 'getResource');
    if (mapping) {
      const transformedData = {
        resourceId: apiResponse.data.data.id,
        resourceName: apiResponse.data.data.name
      };
      
      return [200, transformedData];
    }
    
    return [200, apiResponse.data];
  } catch (error) {
    return [error.response?.status || 500, { error: error.message }];
  }
});

// Test function
async function runTest() {
  console.log('Running API Connection Test...');
  
  try {
    // Execute the connector endpoint
    const response = await axios.post('/execute/test-connector-id/getResource', {
      credentialId: 'test-credential-id',
      parameters: {},
      userId: 'test-user'
    });
    
    console.log('Test Result:', response.status === 200 ? 'PASS' : 'FAIL');
    console.log('Status Code:', response.status);
    console.log('Response Data:', JSON.stringify(response.data, null, 2));
    
    // Verify the response
    if (response.status === 200 && 
        response.data.resourceId === 'resource-1' && 
        response.data.resourceName === 'Test Resource') {
      console.log('\n✅ API Connection Test PASSED');
      console.log('The Universal API Connector successfully connected to the API and transformed the response.');
    } else {
      console.log('\n❌ API Connection Test FAILED');
      console.log('The Universal API Connector did not return the expected response.');
    }
  } catch (error) {
    console.log('Test Result: FAIL');
    console.log('Error:', error.message);
    console.log('Response:', error.response?.data);
    
    console.log('\n❌ API Connection Test FAILED');
    console.log('The Universal API Connector encountered an error:', error.message);
  }
}

// Run the test
runTest();
