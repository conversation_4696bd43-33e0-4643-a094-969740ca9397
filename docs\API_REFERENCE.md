# NovaConnect UAC API Reference

This document provides a reference for the NovaConnect Universal API Connector (UAC) API.

## Authentication

All API requests require authentication using an API key. Include the API key in the `X-API-Key` header:

```
X-API-Key: your-api-key
```

## Base URL

The base URL for all API endpoints is:

```
https://api.novafuse.io
```

## Endpoints

### Health Check

```
GET /health
```

Returns the health status of the API.

#### Response

```json
{
  "status": "ok",
  "uptime": 3600,
  "timestamp": 1619712000000,
  "version": "1.0.0"
}
```

### Connectors

#### List Connectors

```
GET /api/connectors
```

Returns a list of all connectors.

#### Get Connector

```
GET /api/connectors/{id}
```

Returns a specific connector by ID.

#### Create Connector

```
POST /api/connectors
```

Creates a new connector.

#### Update Connector

```
PUT /api/connectors/{id}
```

Updates a specific connector by ID.

#### Delete Connector

```
DELETE /api/connectors/{id}
```

Deletes a specific connector by ID.

### Endpoints

#### List Endpoints

```
GET /api/connectors/{connectorId}/endpoints
```

Returns a list of all endpoints for a specific connector.

#### Get Endpoint

```
GET /api/connectors/{connectorId}/endpoints/{id}
```

Returns a specific endpoint by ID.

#### Create Endpoint

```
POST /api/connectors/{connectorId}/endpoints
```

Creates a new endpoint for a specific connector.

#### Update Endpoint

```
PUT /api/connectors/{connectorId}/endpoints/{id}
```

Updates a specific endpoint by ID.

#### Delete Endpoint

```
DELETE /api/connectors/{connectorId}/endpoints/{id}
```

Deletes a specific endpoint by ID.

### Credentials

#### List Credentials

```
GET /api/credentials
```

Returns a list of all credentials.

#### Get Credential

```
GET /api/credentials/{id}
```

Returns a specific credential by ID.

#### Create Credential

```
POST /api/credentials
```

Creates a new credential.

#### Update Credential

```
PUT /api/credentials/{id}
```

Updates a specific credential by ID.

#### Delete Credential

```
DELETE /api/credentials/{id}
```

Deletes a specific credential by ID.

### Executions

#### Execute Endpoint

```
POST /api/connectors/{connectorId}/endpoints/{endpointId}/execute
```

Executes a specific endpoint.

#### Get Execution

```
GET /api/executions/{id}
```

Returns a specific execution by ID.

#### List Executions

```
GET /api/executions
```

Returns a list of all executions.

### Data Normalization

#### Normalize Data

```
POST /api/normalize
```

Normalizes data according to specified rules.

#### List Normalization Rules

```
GET /api/normalization/rules
```

Returns a list of all normalization rules.

#### Get Normalization Rule

```
GET /api/normalization/rules/{id}
```

Returns a specific normalization rule by ID.

#### Create Normalization Rule

```
POST /api/normalization/rules
```

Creates a new normalization rule.

#### Update Normalization Rule

```
PUT /api/normalization/rules/{id}
```

Updates a specific normalization rule by ID.

#### Delete Normalization Rule

```
DELETE /api/normalization/rules/{id}
```

Deletes a specific normalization rule by ID.

### Workflows

#### List Workflows

```
GET /api/workflows
```

Returns a list of all workflows.

#### Get Workflow

```
GET /api/workflows/{id}
```

Returns a specific workflow by ID.

#### Create Workflow

```
POST /api/workflows
```

Creates a new workflow.

#### Update Workflow

```
PUT /api/workflows/{id}
```

Updates a specific workflow by ID.

#### Delete Workflow

```
DELETE /api/workflows/{id}
```

Deletes a specific workflow by ID.

#### Execute Workflow

```
POST /api/workflows/{id}/execute
```

Executes a specific workflow.

### Monitoring

#### Get Metrics

```
GET /metrics
```

Returns metrics in Prometheus format.

#### Get Cluster Health

```
GET /cluster/health
```

Returns the health status of the cluster.

#### Get Cache Metrics

```
GET /cache/metrics
```

Returns cache metrics.

#### Clear Cache

```
POST /cache/clear
```

Clears the cache.

### Feature Flags

#### Get Subscription

```
GET /subscription
```

Returns the current subscription.

#### Get Available Features

```
GET /features
```

Returns a list of available features.

#### Get Feature Usage

```
GET /features/usage
```

Returns feature usage statistics.

#### Update Subscription

```
PUT /subscription/{userId}
```

Updates a user's subscription.

### Optimization

#### Get Database Optimization Stats

```
GET /optimization/database/stats
```

Returns database optimization statistics.

#### Run Database Optimization

```
POST /optimization/database/optimize
```

Runs database optimization.

#### Run Performance Analysis

```
POST /optimization/performance/analyze
```

Runs performance analysis.

## Error Handling

All API endpoints return standard HTTP status codes. In case of an error, the response body will contain an error object:

```json
{
  "error": {
    "type": "validation_error",
    "message": "Invalid input",
    "status": 400,
    "details": {
      "field": "name",
      "message": "Name is required"
    }
  }
}
```

## Rate Limiting

API requests are rate limited to 100 requests per minute per API key. If you exceed the rate limit, you will receive a 429 Too Many Requests response.

## Pagination

List endpoints support pagination using the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Number of items per page (default: 10, max: 100)

Example:

```
GET /api/connectors?page=2&limit=20
```

Response:

```json
{
  "data": [...],
  "pagination": {
    "page": 2,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

## Filtering

List endpoints support filtering using query parameters:

Example:

```
GET /api/connectors?status=active&category=security
```

## Sorting

List endpoints support sorting using the `sort` query parameter:

Example:

```
GET /api/connectors?sort=name
GET /api/connectors?sort=-createdAt
```

Use a minus sign (`-`) to sort in descending order.

## Field Selection

List endpoints support field selection using the `fields` query parameter:

Example:

```
GET /api/connectors?fields=id,name,status
```
