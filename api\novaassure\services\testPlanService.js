/**
 * Test Plan Service
 * 
 * This service provides functionality for test plan management.
 */

const { TestPlan, Control } = require('../models');
const logger = require('../utils/logger');

/**
 * Get all test plans
 * @param {Object} filters - Filters
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} - Test plans with pagination
 */
async function getAllTestPlans(filters = {}, page = 1, limit = 10) {
  try {
    // Build query
    const query = {};
    
    if (filters.framework) {
      query.framework = filters.framework;
    }
    
    if (filters.status) {
      query.status = filters.status;
    }
    
    if (filters.search) {
      query.$text = { $search: filters.search };
    }
    
    // Count total
    const total = await TestPlan.countDocuments(query);
    
    // Get test plans
    const testPlans = await TestPlan.find(query)
      .populate('controls')
      .populate('assignees')
      .skip((page - 1) * limit)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    return {
      testPlans,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Failed to get test plans', error);
    throw error;
  }
}

/**
 * Get test plan by ID
 * @param {string} id - Test plan ID
 * @returns {Promise<Object>} - Test plan object
 */
async function getTestPlanById(id) {
  try {
    const testPlan = await TestPlan.getById(id);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    return testPlan;
  } catch (error) {
    logger.error(`Failed to get test plan ${id}`, error);
    throw error;
  }
}

/**
 * Create test plan
 * @param {Object} testPlanData - Test plan data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Created test plan
 */
async function createTestPlan(testPlanData, userId) {
  try {
    // Validate controls
    if (testPlanData.controls && testPlanData.controls.length > 0) {
      for (const controlId of testPlanData.controls) {
        const control = await Control.findById(controlId);
        if (!control) {
          throw new Error(`Control ${controlId} not found`);
        }
      }
    }
    
    // Create test plan
    const testPlan = new TestPlan({
      ...testPlanData,
      createdBy: userId,
      updatedBy: userId
    });
    
    // Save test plan
    await testPlan.save();
    
    logger.info(`Test plan ${testPlan._id} created by ${userId}`);
    
    return testPlan;
  } catch (error) {
    logger.error('Failed to create test plan', error);
    throw error;
  }
}

/**
 * Update test plan
 * @param {string} id - Test plan ID
 * @param {Object} testPlanData - Test plan data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated test plan
 */
async function updateTestPlan(id, testPlanData, userId) {
  try {
    // Get test plan
    const testPlan = await TestPlan.getById(id);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Validate controls
    if (testPlanData.controls && testPlanData.controls.length > 0) {
      for (const controlId of testPlanData.controls) {
        const control = await Control.findById(controlId);
        if (!control) {
          throw new Error(`Control ${controlId} not found`);
        }
      }
    }
    
    // Update test plan
    Object.keys(testPlanData).forEach(key => {
      if (key !== '_id' && key !== 'createdBy' && key !== 'createdAt') {
        testPlan[key] = testPlanData[key];
      }
    });
    
    testPlan.updatedBy = userId;
    
    // Save test plan
    await testPlan.save();
    
    logger.info(`Test plan ${id} updated by ${userId}`);
    
    return testPlan;
  } catch (error) {
    logger.error(`Failed to update test plan ${id}`, error);
    throw error;
  }
}

/**
 * Delete test plan
 * @param {string} id - Test plan ID
 * @returns {Promise<Object>} - Deleted test plan
 */
async function deleteTestPlan(id) {
  try {
    // Get test plan
    const testPlan = await TestPlan.getById(id);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Delete test plan
    await testPlan.remove();
    
    logger.info(`Test plan ${id} deleted`);
    
    return testPlan;
  } catch (error) {
    logger.error(`Failed to delete test plan ${id}`, error);
    throw error;
  }
}

/**
 * Schedule test plan
 * @param {string} id - Test plan ID
 * @param {Object} schedule - Schedule data
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated test plan
 */
async function scheduleTestPlan(id, schedule, userId) {
  try {
    // Get test plan
    const testPlan = await TestPlan.getById(id);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Validate schedule
    if (!schedule.frequency) {
      throw new Error('Schedule frequency is required');
    }
    
    if (!schedule.startDate) {
      throw new Error('Schedule start date is required');
    }
    
    // Update test plan
    testPlan.schedule = schedule;
    testPlan.updatedBy = userId;
    
    // Save test plan
    await testPlan.save();
    
    logger.info(`Test plan ${id} scheduled by ${userId}`);
    
    return testPlan;
  } catch (error) {
    logger.error(`Failed to schedule test plan ${id}`, error);
    throw error;
  }
}

/**
 * Assign test plan
 * @param {string} id - Test plan ID
 * @param {string[]} assignees - Assignee IDs
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Updated test plan
 */
async function assignTestPlan(id, assignees, userId) {
  try {
    // Get test plan
    const testPlan = await TestPlan.getById(id);
    
    if (!testPlan) {
      throw new Error('Test plan not found');
    }
    
    // Update test plan
    testPlan.assignees = assignees;
    testPlan.updatedBy = userId;
    
    // Save test plan
    await testPlan.save();
    
    logger.info(`Test plan ${id} assigned by ${userId}`);
    
    return testPlan;
  } catch (error) {
    logger.error(`Failed to assign test plan ${id}`, error);
    throw error;
  }
}

/**
 * Clone test plan
 * @param {string} id - Test plan ID
 * @param {string} name - New test plan name
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Cloned test plan
 */
async function cloneTestPlan(id, name, userId) {
  try {
    // Clone test plan
    const clonedTestPlan = await TestPlan.clone(id, name, userId);
    
    logger.info(`Test plan ${id} cloned to ${clonedTestPlan._id} by ${userId}`);
    
    return clonedTestPlan;
  } catch (error) {
    logger.error(`Failed to clone test plan ${id}`, error);
    throw error;
  }
}

/**
 * Get test plans due for execution
 * @returns {Promise<Array>} - Test plans due for execution
 */
async function getTestPlansDueForExecution() {
  try {
    const testPlans = await TestPlan.getDueForExecution();
    
    logger.info(`Found ${testPlans.length} test plans due for execution`);
    
    return testPlans;
  } catch (error) {
    logger.error('Failed to get test plans due for execution', error);
    throw error;
  }
}

module.exports = {
  getAllTestPlans,
  getTestPlanById,
  createTestPlan,
  updateTestPlan,
  deleteTestPlan,
  scheduleTestPlan,
  assignTestPlan,
  cloneTestPlan,
  getTestPlansDueForExecution
};
