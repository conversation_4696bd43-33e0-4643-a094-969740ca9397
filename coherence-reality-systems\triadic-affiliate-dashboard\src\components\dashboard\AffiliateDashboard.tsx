import { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend } from 'recharts'
import { TriangleIcon } from '@radix-ui/react-icons'

interface Metric {
  psi: number
  phi: number
  kappa: number
  timestamp: string
}

export function AffiliateDashboard() {
  const [metrics, setMetrics] = useState<Metric[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch('/api/affiliates/metrics')
        const data = await response.json()
        setMetrics(data)
      } catch (error) {
        console.error('Error fetching metrics:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchMetrics()
  }, [])

  const getTriadicHealth = (metrics: Metric[]) => {
    if (metrics.length === 0) return 0
    
    const avgPsi = metrics.reduce((sum, m) => sum + m.psi, 0) / metrics.length
    const avgPhi = metrics.reduce((sum, m) => sum + m.phi, 0) / metrics.length
    const avgKappa = metrics.reduce((sum, m) => sum + m.kappa, 0) / metrics.length

    return (avgPsi + avgPhi + avgKappa) / 3
  }

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/5 backdrop-blur-lg rounded-lg p-6 border border-white/10"
      >
        <h2 className="text-2xl font-bold mb-4">Triadic Performance</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-purple-500/10 rounded-lg">
            <div className="flex items-center">
              <TriangleIcon className="w-6 h-6 mr-2 text-purple-500" />
              <div>
                <p className="text-sm text-gray-400">PSI</p>
                <p className="text-xl font-bold">{metrics.length > 0 ? metrics[metrics.length - 1].psi.toFixed(2) : '0'}%</p>
              </div>
            </div>
          </div>
          <div className="p-4 bg-blue-500/10 rounded-lg">
            <div className="flex items-center">
              <TriangleIcon className="w-6 h-6 mr-2 text-blue-500" />
              <div>
                <p className="text-sm text-gray-400">PHI</p>
                <p className="text-xl font-bold">{metrics.length > 0 ? metrics[metrics.length - 1].phi.toFixed(2) : '0'}%</p>
              </div>
            </div>
          </div>
          <div className="p-4 bg-green-500/10 rounded-lg">
            <div className="flex items-center">
              <TriangleIcon className="w-6 h-6 mr-2 text-green-500" />
              <div>
                <p className="text-sm text-gray-400">KAPPA</p>
                <p className="text-xl font-bold">{metrics.length > 0 ? metrics[metrics.length - 1].kappa.toFixed(2) : '0'}%</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/5 backdrop-blur-lg rounded-lg p-6 border border-white/10"
      >
        <h3 className="text-xl font-semibold mb-4">Performance Trends</h3>
        <div className="h-[300px]">
          <BarChart width={800} height={300} data={metrics}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="timestamp" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="psi" fill="#8B5CF6" name="PSI" />
            <Bar dataKey="phi" fill="#3B82F6" name="PHI" />
            <Bar dataKey="kappa" fill="#10B981" name="KAPPA" />
          </BarChart>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white/5 backdrop-blur-lg rounded-lg p-6 border border-white/10"
      >
        <h3 className="text-xl font-semibold mb-4">Triadic Health Score</h3>
        <div className="space-y-2">
          <p className="text-xl font-bold">{getTriadicHealth(metrics).toFixed(2)}%</p>
          <p className="text-sm text-gray-400">
            The triadic health score represents the overall balance and performance of your affiliate activities across all three dimensions.
          </p>
        </div>
      </motion.div>
    </div>
  )
}
