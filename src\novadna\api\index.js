/**
 * NovaDNA API
 * 
 * This module provides a RESTful API for the NovaDNA system.
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { v4: uuidv4 } = require('uuid');

// Import route handlers
const profileRoutes = require('./routes/profiles');
const accessRoutes = require('./routes/access');
const authRoutes = require('./routes/auth');
const formFactorRoutes = require('./routes/formFactors');
const securityRoutes = require('./routes/security');

/**
 * Create the NovaDNA API
 * @param {Object} novaDNA - The NovaDNA instance
 * @param {Object} options - API configuration options
 * @returns {Object} - The Express app
 */
function createAPI(novaDNA, options = {}) {
  const app = express();
  
  // Configure middleware
  app.use(helmet());
  app.use(cors(options.cors || {}));
  app.use(express.json());
  
  // Add request ID
  app.use((req, res, next) => {
    req.id = uuidv4();
    res.setHeader('X-Request-ID', req.id);
    next();
  });
  
  // Add rate limiting
  const limiter = rateLimit({
    windowMs: options.rateLimitWindowMs || 15 * 60 * 1000, // 15 minutes
    max: options.rateLimitMax || 100, // 100 requests per window
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      status: 429,
      error: 'Too many requests, please try again later.'
    }
  });
  app.use(limiter);
  
  // Add NovaDNA instance to request
  app.use((req, res, next) => {
    req.novaDNA = novaDNA;
    next();
  });
  
  // Add logging middleware
  app.use((req, res, next) => {
    const start = Date.now();
    
    // Log request
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - Request ID: ${req.id}`);
    
    // Log response
    res.on('finish', () => {
      const duration = Date.now() - start;
      console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms - Request ID: ${req.id}`);
    });
    
    next();
  });
  
  // Mount routes
  app.use('/api/profiles', profileRoutes);
  app.use('/api/access', accessRoutes);
  app.use('/api/auth', authRoutes);
  app.use('/api/form-factors', formFactorRoutes);
  app.use('/api/security', securityRoutes);
  
  // Add error handling middleware
  app.use((err, req, res, next) => {
    console.error(`[${new Date().toISOString()}] Error: ${err.message} - Request ID: ${req.id}`);
    console.error(err.stack);
    
    res.status(err.status || 500).json({
      status: err.status || 500,
      error: err.message || 'Internal Server Error',
      requestId: req.id
    });
  });
  
  // Add 404 handler
  app.use((req, res) => {
    res.status(404).json({
      status: 404,
      error: 'Not Found',
      requestId: req.id
    });
  });
  
  return app;
}

module.exports = createAPI;
