/**
 * Schema Routes
 * 
 * This file defines the routes for schema management.
 */

const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');
const authMiddleware = require('../middleware/authMiddleware');

// Schema directory
const SCHEMA_DIR = path.join(__dirname, '../schemas');

// Ensure schema directory exists
(async () => {
  try {
    await fs.mkdir(SCHEMA_DIR, { recursive: true });
  } catch (err) {
    if (err.code !== 'EEXIST') {
      console.error('Failed to create schema directory:', err);
    }
  }
})();

/**
 * Get schema by entity
 * @route GET /api/v1/schemas/:entity
 */
router.get('/:entity', async (req, res) => {
  try {
    const { entity } = req.params;
    const schemaPath = path.join(SCHEMA_DIR, `${entity.toLowerCase()}.json`);
    
    try {
      const schemaContent = await fs.readFile(schemaPath, 'utf8');
      const schema = JSON.parse(schemaContent);
      
      res.json({
        success: true,
        data: schema
      });
    } catch (err) {
      if (err.code === 'ENOENT') {
        return res.status(404).json({
          success: false,
          error: `No schema found for ${entity}`
        });
      }
      throw err;
    }
  } catch (error) {
    logger.error(`Error getting schema for ${req.params.entity}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to get schema'
    });
  }
});

/**
 * List all schemas
 * @route GET /api/v1/schemas
 */
router.get('/', async (req, res) => {
  try {
    const schemas = [];
    
    try {
      const files = await fs.readdir(SCHEMA_DIR);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          const content = await fs.readFile(path.join(SCHEMA_DIR, file), 'utf8');
          const schema = JSON.parse(content);
          
          schemas.push({
            entity: schema.entity,
            entityPlural: schema.entityPlural || `${schema.entity}s`,
            apiEndpoint: schema.apiEndpoint || `/api/v1/${schema.entity.toLowerCase()}s`,
            fieldCount: schema.fields.length
          });
        }
      }
    } catch (err) {
      if (err.code !== 'ENOENT') {
        throw err;
      }
    }
    
    res.json({
      success: true,
      data: schemas
    });
  } catch (error) {
    logger.error('Error listing schemas', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to list schemas'
    });
  }
});

/**
 * Save schema
 * @route POST /api/v1/schemas
 */
router.post('/', authMiddleware.authenticate, authMiddleware.hasRole(['admin']), async (req, res) => {
  try {
    const schema = req.body;
    
    // Validate schema
    if (!schema.entity || !Array.isArray(schema.fields)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid schema format'
      });
    }
    
    // Set default values
    if (!schema.entityPlural) {
      schema.entityPlural = `${schema.entity}s`;
    }
    
    if (!schema.apiEndpoint) {
      schema.apiEndpoint = `/api/v1/${schema.entity.toLowerCase()}s`;
    }
    
    // Save schema
    const schemaPath = path.join(SCHEMA_DIR, `${schema.entity.toLowerCase()}.json`);
    await fs.writeFile(schemaPath, JSON.stringify(schema, null, 2), 'utf8');
    
    res.json({
      success: true,
      message: `Schema for ${schema.entity} saved successfully`,
      data: schema
    });
  } catch (error) {
    logger.error('Error saving schema', error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to save schema'
    });
  }
});

/**
 * Delete schema
 * @route DELETE /api/v1/schemas/:entity
 */
router.delete('/:entity', authMiddleware.authenticate, authMiddleware.hasRole(['admin']), async (req, res) => {
  try {
    const { entity } = req.params;
    const schemaPath = path.join(SCHEMA_DIR, `${entity.toLowerCase()}.json`);
    
    try {
      await fs.access(schemaPath);
    } catch (err) {
      return res.status(404).json({
        success: false,
        error: `No schema found for ${entity}`
      });
    }
    
    await fs.unlink(schemaPath);
    
    res.json({
      success: true,
      message: `Schema for ${entity} deleted successfully`
    });
  } catch (error) {
    logger.error(`Error deleting schema for ${req.params.entity}`, error);
    
    res.status(500).json({
      success: false,
      error: 'Failed to delete schema'
    });
  }
});

module.exports = router;
