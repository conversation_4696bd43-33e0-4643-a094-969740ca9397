/**
 * RBAC API Integration Tests
 * 
 * These tests verify that the RBAC API endpoints work correctly.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../../app');
const User = require('../../api/models/User');
const Role = require('../../api/models/Role');
const Permission = require('../../api/models/Permission');
const UserRole = require('../../api/models/UserRole');
const { hashPassword } = require('../../api/utils/auth');

let mongoServer;
let adminToken;
let userToken;
let adminUser;
let regularUser;
let testRole;
let testPermission;

/**
 * Setup test environment before all tests
 */
beforeAll(async () => {
  // Set up in-memory MongoDB
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
  
  // Create test users
  adminUser = new User({
    username: 'admin',
    email: '<EMAIL>',
    password: await hashPassword('password123'),
    firstName: 'Admin',
    lastName: 'User',
    isAdmin: true
  });
  
  regularUser = new User({
    username: 'user',
    email: '<EMAIL>',
    password: await hashPassword('password123'),
    firstName: 'Regular',
    lastName: 'User',
    isAdmin: false
  });
  
  await adminUser.save();
  await regularUser.save();
  
  // Create test permission
  testPermission = new Permission({
    name: 'Test Permission',
    description: 'Test permission for integration tests',
    resource: 'test',
    action: 'read',
    isSystem: false
  });
  
  await testPermission.save();
  
  // Create test role
  testRole = new Role({
    name: 'Test Role',
    description: 'Test role for integration tests',
    permissions: [testPermission._id],
    isSystem: false,
    isDefault: false
  });
  
  await testRole.save();
  
  // Assign role to regular user
  const userRole = new UserRole({
    userId: regularUser._id,
    roleId: testRole._id
  });
  
  await userRole.save();
  
  // Get authentication tokens
  const adminLoginResponse = await request(app)
    .post('/api/auth/login')
    .send({ email: '<EMAIL>', password: 'password123' });
  
  const userLoginResponse = await request(app)
    .post('/api/auth/login')
    .send({ email: '<EMAIL>', password: 'password123' });
  
  adminToken = adminLoginResponse.body.token;
  userToken = userLoginResponse.body.token;
});

/**
 * Clean up after all tests
 */
afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

/**
 * Role API Tests
 */
describe('Role API', () => {
  let createdRoleId;
  
  test('GET /api/rbac/roles should return all roles', async () => {
    const response = await request(app)
      .get('/api/rbac/roles')
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body.find(role => role.name === 'Test Role')).toBeTruthy();
  });
  
  test('GET /api/rbac/roles/:id should return a specific role', async () => {
    const response = await request(app)
      .get(`/api/rbac/roles/${testRole._id}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Test Role');
    expect(response.body.description).toBe('Test role for integration tests');
    expect(Array.isArray(response.body.permissions)).toBe(true);
  });
  
  test('POST /api/rbac/roles should create a new role', async () => {
    const newRole = {
      name: 'New Test Role',
      description: 'New test role created via API',
      permissions: [testPermission._id.toString()]
    };
    
    const response = await request(app)
      .post('/api/rbac/roles')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(newRole);
    
    expect(response.status).toBe(201);
    expect(response.body.name).toBe('New Test Role');
    expect(response.body.description).toBe('New test role created via API');
    expect(Array.isArray(response.body.permissions)).toBe(true);
    
    createdRoleId = response.body._id;
  });
  
  test('POST /api/rbac/roles should handle string permission format', async () => {
    const newRole = {
      name: 'String Permission Role',
      description: 'Role with string permission format',
      permissions: ['test:read']
    };
    
    const response = await request(app)
      .post('/api/rbac/roles')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(newRole);
    
    expect(response.status).toBe(201);
    expect(response.body.name).toBe('String Permission Role');
    expect(Array.isArray(response.body.permissions)).toBe(true);
  });
  
  test('PUT /api/rbac/roles/:id should update a role', async () => {
    const updatedRole = {
      name: 'Updated Test Role',
      description: 'Updated test role via API'
    };
    
    const response = await request(app)
      .put(`/api/rbac/roles/${createdRoleId}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send(updatedRole);
    
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Updated Test Role');
    expect(response.body.description).toBe('Updated test role via API');
  });
  
  test('DELETE /api/rbac/roles/:id should delete a role', async () => {
    const response = await request(app)
      .delete(`/api/rbac/roles/${createdRoleId}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    
    // Verify role is deleted
    const getResponse = await request(app)
      .get(`/api/rbac/roles/${createdRoleId}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(getResponse.status).toBe(404);
  });
});

/**
 * Permission API Tests
 */
describe('Permission API', () => {
  let createdPermissionId;
  
  test('GET /api/rbac/permissions should return all permissions', async () => {
    const response = await request(app)
      .get('/api/rbac/permissions')
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body.find(perm => perm.name === 'Test Permission')).toBeTruthy();
  });
  
  test('GET /api/rbac/permissions/:id should return a specific permission', async () => {
    const response = await request(app)
      .get(`/api/rbac/permissions/${testPermission._id}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Test Permission');
    expect(response.body.resource).toBe('test');
    expect(response.body.action).toBe('read');
  });
  
  test('POST /api/rbac/permissions should create a new permission', async () => {
    const newPermission = {
      name: 'New Test Permission',
      description: 'New test permission created via API',
      resource: 'test',
      action: 'write'
    };
    
    const response = await request(app)
      .post('/api/rbac/permissions')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(newPermission);
    
    expect(response.status).toBe(201);
    expect(response.body.name).toBe('New Test Permission');
    expect(response.body.resource).toBe('test');
    expect(response.body.action).toBe('write');
    
    createdPermissionId = response.body._id;
  });
  
  test('PUT /api/rbac/permissions/:id should update a permission', async () => {
    const updatedPermission = {
      name: 'Updated Test Permission',
      description: 'Updated test permission via API'
    };
    
    const response = await request(app)
      .put(`/api/rbac/permissions/${createdPermissionId}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send(updatedPermission);
    
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Updated Test Permission');
    expect(response.body.description).toBe('Updated test permission via API');
  });
  
  test('DELETE /api/rbac/permissions/:id should delete a permission', async () => {
    const response = await request(app)
      .delete(`/api/rbac/permissions/${createdPermissionId}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    
    // Verify permission is deleted
    const getResponse = await request(app)
      .get(`/api/rbac/permissions/${createdPermissionId}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(getResponse.status).toBe(404);
  });
});

/**
 * User Role API Tests
 */
describe('User Role API', () => {
  test('GET /api/rbac/users/:userId/roles should return user roles', async () => {
    const response = await request(app)
      .get(`/api/rbac/users/${regularUser._id}/roles`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBe(1);
    expect(response.body[0].name).toBe('Test Role');
  });
  
  test('POST /api/rbac/users/:userId/roles should assign role to user', async () => {
    // Create a new role to assign
    const newRole = new Role({
      name: 'Assign Test Role',
      description: 'Role to test assignment',
      permissions: [testPermission._id],
      isSystem: false,
      isDefault: false
    });
    
    await newRole.save();
    
    const response = await request(app)
      .post(`/api/rbac/users/${regularUser._id}/roles`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({ roleId: newRole._id.toString() });
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    
    // Verify role was assigned
    const getRolesResponse = await request(app)
      .get(`/api/rbac/users/${regularUser._id}/roles`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(getRolesResponse.body.length).toBe(2);
    expect(getRolesResponse.body.find(role => role.name === 'Assign Test Role')).toBeTruthy();
  });
  
  test('DELETE /api/rbac/users/:userId/roles/:roleId should remove role from user', async () => {
    const role = await Role.findOne({ name: 'Assign Test Role' });
    
    const response = await request(app)
      .delete(`/api/rbac/users/${regularUser._id}/roles/${role._id}`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);
    
    // Verify role was removed
    const getRolesResponse = await request(app)
      .get(`/api/rbac/users/${regularUser._id}/roles`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(getRolesResponse.body.length).toBe(1);
    expect(getRolesResponse.body.find(r => r.name === 'Assign Test Role')).toBeFalsy();
  });
  
  test('GET /api/rbac/users/:userId/permissions should return user permissions', async () => {
    const response = await request(app)
      .get(`/api/rbac/users/${regularUser._id}/permissions`)
      .set('Authorization', `Bearer ${adminToken}`);
    
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body.find(perm => perm.resource === 'test' && perm.action === 'read')).toBeTruthy();
  });
});
