import React, { useState, useEffect, useRef } from 'react';

/**
 * Comphyology Field Visualization
 * 
 * This component visualizes the field effects of Comphyology (Ψᶜ),
 * showing how the Ripple Effect propagates across systems.
 */
const ComphyologyFieldVisualization = ({ width = '100%', height = '600px' }) => {
  // State for visualization parameters
  const [fieldStrength, setFieldStrength] = useState(0.75);
  const [resonanceLevel, setResonanceLevel] = useState(0.82);
  const [coherenceIndex, setCoherenceIndex] = useState(0.68);
  const [rippleMode, setRippleMode] = useState('standard'); // 'standard', 'enhanced', 'quantum'
  const [showLabels, setShowLabels] = useState(true);
  
  // Canvas reference for visualization
  const canvasRef = useRef(null);
  
  // System nodes
  const systems = [
    { id: 'core', name: 'NovaFuse Core', x: 0.5, y: 0.5, size: 0.15, color: '#9C27B0', strength: 1.0 },
    { id: 'security', name: 'Security Systems', x: 0.3, y: 0.3, size: 0.08, color: '#F44336', strength: 0.9 },
    { id: 'compliance', name: 'Compliance Systems', x: 0.7, y: 0.3, size: 0.08, color: '#4CAF50', strength: 0.85 },
    { id: 'it', name: 'IT Systems', x: 0.3, y: 0.7, size: 0.08, color: '#2196F3', strength: 0.8 },
    { id: 'business', name: 'Business Systems', x: 0.7, y: 0.7, size: 0.08, color: '#FF9800', strength: 0.75 },
    { id: 'partner1', name: 'Partner System 1', x: 0.15, y: 0.5, size: 0.06, color: '#9E9E9E', strength: 0.7 },
    { id: 'partner2', name: 'Partner System 2', x: 0.85, y: 0.5, size: 0.06, color: '#9E9E9E', strength: 0.65 },
    { id: 'external1', name: 'External System 1', x: 0.5, y: 0.15, size: 0.05, color: '#607D8B', strength: 0.6 },
    { id: 'external2', name: 'External System 2', x: 0.5, y: 0.85, size: 0.05, color: '#607D8B', strength: 0.55 }
  ];
  
  // Field effect parameters
  const fieldParams = {
    standard: {
      waveCount: 5,
      waveSpeed: 0.5,
      waveAmplitude: 0.3,
      particleCount: 100,
      particleSpeed: 0.5,
      particleSize: 2
    },
    enhanced: {
      waveCount: 8,
      waveSpeed: 0.8,
      waveAmplitude: 0.5,
      particleCount: 200,
      particleSpeed: 0.8,
      particleSize: 3
    },
    quantum: {
      waveCount: 12,
      waveSpeed: 1.2,
      waveAmplitude: 0.7,
      particleCount: 300,
      particleSpeed: 1.2,
      particleSize: 4
    }
  };
  
  // Initialize and animate the canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const container = canvas.parentElement;
    
    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Create particles
    const particles = [];
    const params = fieldParams[rippleMode];
    
    for (let i = 0; i < params.particleCount; i++) {
      particles.push({
        x: Math.random(),
        y: Math.random(),
        size: Math.random() * params.particleSize + 1,
        speed: Math.random() * params.particleSpeed + 0.2,
        angle: Math.random() * Math.PI * 2,
        color: getRandomColor(0.5),
        life: Math.random()
      });
    }
    
    // Animation variables
    let animationFrame;
    let time = 0;
    
    // Animation function
    const animate = () => {
      time += 0.01;
      
      // Clear canvas
      ctx.fillStyle = '#111133';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw field visualization
      drawFieldVisualization(ctx, canvas.width, canvas.height, time, particles);
      
      // Continue animation
      animationFrame = requestAnimationFrame(animate);
    };
    
    animate();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrame);
    };
  }, [rippleMode, fieldStrength, resonanceLevel, coherenceIndex, showLabels]);
  
  // Get random color with opacity
  const getRandomColor = (opacity = 1) => {
    const colors = ['#F44336', '#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#00BCD4'];
    const color = colors[Math.floor(Math.random() * colors.length)];
    return color + Math.floor(opacity * 255).toString(16).padStart(2, '0');
  };
  
  // Draw the field visualization
  const drawFieldVisualization = (ctx, width, height, time, particles) => {
    const params = fieldParams[rippleMode];
    
    // Draw field waves
    drawFieldWaves(ctx, width, height, time, params);
    
    // Draw connections between systems
    drawConnections(ctx, width, height);
    
    // Draw system nodes
    drawSystemNodes(ctx, width, height);
    
    // Draw particles
    updateAndDrawParticles(ctx, width, height, time, particles, params);
    
    // Draw field metrics
    drawFieldMetrics(ctx, width, height);
  };
  
  // Draw field waves
  const drawFieldWaves = (ctx, width, height, time, params) => {
    const centerX = width / 2;
    const centerY = height / 2;
    const maxRadius = Math.sqrt(width * width + height * height) / 2;
    
    // Draw ripple waves
    for (let i = 0; i < params.waveCount; i++) {
      const progress = ((time * params.waveSpeed) + i / params.waveCount) % 1;
      const radius = progress * maxRadius;
      const opacity = (1 - progress) * fieldStrength;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`;
      ctx.lineWidth = 2;
      ctx.stroke();
    }
    
    // Draw resonance patterns
    for (let i = 0; i < params.waveCount / 2; i++) {
      const progress = ((time * params.waveSpeed * 0.7) + i / (params.waveCount / 2)) % 1;
      const radius = progress * maxRadius;
      const opacity = (1 - progress) * resonanceLevel * 0.5;
      
      // Draw phi-based spiral
      ctx.beginPath();
      const spiralPoints = 100;
      const spiralTurns = 3;
      const goldenRatio = 1.618033988749895;
      
      for (let j = 0; j <= spiralPoints; j++) {
        const t = j / spiralPoints;
        const spiralRadius = radius * t;
        const angle = 2 * Math.PI * spiralTurns * t * goldenRatio;
        
        const x = centerX + Math.cos(angle) * spiralRadius;
        const y = centerY + Math.sin(angle) * spiralRadius;
        
        if (j === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.strokeStyle = `rgba(156, 39, 176, ${opacity})`;
      ctx.lineWidth = 2;
      ctx.stroke();
    }
    
    // Draw coherence field
    const fieldOpacity = coherenceIndex * 0.3;
    const gradient = ctx.createRadialGradient(
      centerX, centerY, 0,
      centerX, centerY, maxRadius * 0.8
    );
    
    gradient.addColorStop(0, `rgba(33, 150, 243, ${fieldOpacity})`);
    gradient.addColorStop(0.5, `rgba(156, 39, 176, ${fieldOpacity * 0.7})`);
    gradient.addColorStop(1, `rgba(76, 175, 80, ${fieldOpacity * 0.3})`);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  };
  
  // Draw connections between systems
  const drawConnections = (ctx, width, height) => {
    // Define connections
    const connections = [
      { from: 'core', to: 'security', strength: 0.9 },
      { from: 'core', to: 'compliance', strength: 0.85 },
      { from: 'core', to: 'it', strength: 0.8 },
      { from: 'core', to: 'business', strength: 0.75 },
      { from: 'core', to: 'partner1', strength: 0.7 },
      { from: 'core', to: 'partner2', strength: 0.65 },
      { from: 'core', to: 'external1', strength: 0.6 },
      { from: 'core', to: 'external2', strength: 0.55 },
      { from: 'security', to: 'compliance', strength: 0.7 },
      { from: 'security', to: 'it', strength: 0.65 },
      { from: 'compliance', to: 'business', strength: 0.6 },
      { from: 'it', to: 'business', strength: 0.55 },
      { from: 'partner1', to: 'security', strength: 0.5 },
      { from: 'partner2', to: 'business', strength: 0.45 },
      { from: 'external1', to: 'compliance', strength: 0.4 },
      { from: 'external2', to: 'it', strength: 0.35 }
    ];
    
    // Draw connections
    connections.forEach(connection => {
      const fromSystem = systems.find(s => s.id === connection.from);
      const toSystem = systems.find(s => s.id === connection.to);
      
      if (fromSystem && toSystem) {
        const fromX = fromSystem.x * width;
        const fromY = fromSystem.y * height;
        const toX = toSystem.x * width;
        const toY = toSystem.y * height;
        
        // Calculate connection strength based on field parameters
        const connectionStrength = connection.strength * fieldStrength * resonanceLevel;
        
        // Draw connection line
        ctx.beginPath();
        ctx.moveTo(fromX, fromY);
        ctx.lineTo(toX, toY);
        ctx.strokeStyle = `rgba(255, 255, 255, ${connectionStrength * 0.5})`;
        ctx.lineWidth = 1 + connectionStrength * 3;
        ctx.stroke();
      }
    });
  };
  
  // Draw system nodes
  const drawSystemNodes = (ctx, width, height) => {
    systems.forEach(system => {
      const x = system.x * width;
      const y = system.y * height;
      const size = system.size * Math.min(width, height);
      
      // Calculate system strength based on field parameters
      const systemStrength = system.strength * fieldStrength * (system.id === 'core' ? 1 : resonanceLevel);
      
      // Draw system node
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      
      // Create gradient
      const gradient = ctx.createRadialGradient(
        x, y, 0,
        x, y, size
      );
      
      gradient.addColorStop(0, system.color);
      gradient.addColorStop(1, `${system.color}80`);
      
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Draw system pulse
      ctx.beginPath();
      ctx.arc(x, y, size * (1 + systemStrength * 0.5), 0, Math.PI * 2);
      ctx.strokeStyle = `${system.color}40`;
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // Draw system label
      if (showLabels) {
        ctx.font = 'bold 12px Arial';
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(system.name, x, y + size + 15);
      }
    });
  };
  
  // Update and draw particles
  const updateAndDrawParticles = (ctx, width, height, time, particles, params) => {
    particles.forEach(particle => {
      // Update particle position
      particle.x += Math.cos(particle.angle) * particle.speed * 0.005;
      particle.y += Math.sin(particle.angle) * particle.speed * 0.005;
      
      // Wrap particles around edges
      if (particle.x < 0) particle.x = 1;
      if (particle.x > 1) particle.x = 0;
      if (particle.y < 0) particle.y = 1;
      if (particle.y > 1) particle.y = 0;
      
      // Update particle life
      particle.life += 0.005;
      if (particle.life > 1) {
        particle.life = 0;
        particle.x = Math.random();
        particle.y = Math.random();
        particle.angle = Math.random() * Math.PI * 2;
      }
      
      // Calculate field influence
      const fieldInfluence = calculateFieldInfluence(particle.x, particle.y);
      
      // Adjust particle angle based on field
      particle.angle += (Math.sin(time * 5 + particle.x * 10) * Math.cos(time * 5 + particle.y * 10)) * fieldInfluence * 0.1;
      
      // Draw particle
      const x = particle.x * width;
      const y = particle.y * height;
      const size = particle.size * (1 + fieldInfluence * params.waveAmplitude);
      
      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fillStyle = particle.color;
      ctx.fill();
    });
  };
  
  // Calculate field influence at a point
  const calculateFieldInfluence = (x, y) => {
    let influence = 0;
    
    // Calculate influence from each system
    systems.forEach(system => {
      const dx = x - system.x;
      const dy = y - system.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      // Inverse square law with system strength
      const systemInfluence = system.strength / (1 + distance * 10);
      influence += systemInfluence;
    });
    
    // Normalize and apply field parameters
    influence = Math.min(1, influence * 2) * fieldStrength * resonanceLevel;
    
    return influence;
  };
  
  // Draw field metrics
  const drawFieldMetrics = (ctx, width, height) => {
    const metrics = [
      { name: 'Field Strength', value: fieldStrength, color: '#4CAF50' },
      { name: 'Resonance Level', value: resonanceLevel, color: '#2196F3' },
      { name: 'Coherence Index', value: coherenceIndex, color: '#9C27B0' }
    ];
    
    const metricWidth = 150;
    const metricHeight = 30;
    const metricSpacing = 10;
    const startX = 20;
    const startY = 20;
    
    metrics.forEach((metric, index) => {
      const x = startX;
      const y = startY + index * (metricHeight + metricSpacing);
      
      // Draw metric background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      ctx.fillRect(x, y, metricWidth, metricHeight);
      
      // Draw metric value
      ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.fillRect(x + 5, y + metricHeight - 12, metricWidth - 10, 7);
      
      ctx.fillStyle = metric.color;
      ctx.fillRect(x + 5, y + metricHeight - 12, (metricWidth - 10) * metric.value, 7);
      
      // Draw metric label
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';
      ctx.fillText(metric.name, x + 5, y + 5);
      
      // Draw metric value
      ctx.font = '12px Arial';
      ctx.textAlign = 'right';
      ctx.fillText((metric.value * 100).toFixed(0) + '%', x + metricWidth - 5, y + 5);
    });
    
    // Draw ripple mode
    const modeX = width - 150;
    const modeY = 20;
    
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(modeX, modeY, 130, 30);
    
    ctx.font = 'bold 12px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillText('Ripple Mode:', modeX + 10, modeY + 15);
    
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(rippleMode.charAt(0).toUpperCase() + rippleMode.slice(1), modeX + 120, modeY + 15);
  };
  
  // Format percentage
  const formatPercentage = (value) => {
    return (value * 100).toFixed(0) + '%';
  };
  
  return (
    <div className="comphyology-field-visualization" style={{ width, height, position: 'relative' }}>
      <div className="visualization-container" style={{ 
        width: '100%', 
        height: '80%', 
        backgroundColor: '#111133',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <canvas ref={canvasRef} style={{ width: '100%', height: '100%' }} />
      </div>
      
      <div className="controls-container" style={{ 
        width: '100%', 
        height: '20%', 
        padding: '15px',
        backgroundColor: '#1a1a2e',
        borderRadius: '8px',
        marginTop: '10px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h3 style={{ margin: 0, color: 'white', fontSize: '16px' }}>Comphyology Field Controls</h3>
          
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={() => setShowLabels(!showLabels)}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                border: 'none',
                padding: '5px 10px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {showLabels ? 'Hide Labels' : 'Show Labels'}
            </button>
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '20px', marginTop: '10px' }}>
          <div style={{ flex: 1 }}>
            <label style={{ display: 'flex', justifyContent: 'space-between', color: '#4CAF50', marginBottom: '5px', fontSize: '12px' }}>
              <span>Field Strength:</span>
              <span>{formatPercentage(fieldStrength)}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01" 
              value={fieldStrength} 
              onChange={(e) => setFieldStrength(parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
          
          <div style={{ flex: 1 }}>
            <label style={{ display: 'flex', justifyContent: 'space-between', color: '#2196F3', marginBottom: '5px', fontSize: '12px' }}>
              <span>Resonance Level:</span>
              <span>{formatPercentage(resonanceLevel)}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01" 
              value={resonanceLevel} 
              onChange={(e) => setResonanceLevel(parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
          
          <div style={{ flex: 1 }}>
            <label style={{ display: 'flex', justifyContent: 'space-between', color: '#9C27B0', marginBottom: '5px', fontSize: '12px' }}>
              <span>Coherence Index:</span>
              <span>{formatPercentage(coherenceIndex)}</span>
            </label>
            <input 
              type="range" 
              min="0" 
              max="1" 
              step="0.01" 
              value={coherenceIndex} 
              onChange={(e) => setCoherenceIndex(parseFloat(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>
        </div>
        
        <div style={{ display: 'flex', gap: '10px', marginTop: '10px' }}>
          {['standard', 'enhanced', 'quantum'].map(mode => (
            <button
              key={mode}
              onClick={() => setRippleMode(mode)}
              style={{
                backgroundColor: rippleMode === mode ? '#9C27B0' : 'rgba(255,255,255,0.1)',
                color: 'white',
                border: 'none',
                padding: '8px 15px',
                borderRadius: '4px',
                cursor: 'pointer',
                flex: 1,
                fontSize: '12px',
                textTransform: 'capitalize'
              }}
            >
              {mode} Ripple
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ComphyologyFieldVisualization;
