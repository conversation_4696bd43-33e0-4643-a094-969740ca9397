/**
 * NovaVision - UI Schema Service
 * 
 * This service provides UI schema generation and validation capabilities.
 */

const { createLogger } = require('../../utils/logger');
const schemaValidator = require('../utils/schema-validator');

const logger = createLogger('ui-schema-service');

/**
 * Generate schema from API response
 * 
 * @param {Object} apiResponse - API response
 * @param {string} schemaType - Schema type (form, dashboard, report)
 * @returns {Object} - Generated UI schema
 */
function generateSchemaFromApiResponse(apiResponse, schemaType = 'form') {
  logger.info('Generating schema from API response', { schemaType });
  
  // Check if API response already contains a UI schema
  if (apiResponse.ui_schema) {
    logger.debug('Using existing UI schema from API response');
    return apiResponse.ui_schema;
  }
  
  // Generate schema based on schema type
  switch (schemaType) {
    case 'form':
      return generateFormSchema(apiResponse);
    case 'dashboard':
      return generateDashboardSchema(apiResponse);
    case 'report':
      return generateReportSchema(apiResponse);
    default:
      logger.warn('Unknown schema type', { schemaType });
      throw new Error(`Unknown schema type: ${schemaType}`);
  }
}

/**
 * Generate form schema from API response
 * 
 * @param {Object} apiResponse - API response
 * @returns {Object} - Generated form schema
 * @private
 */
function generateFormSchema(apiResponse) {
  logger.debug('Generating form schema from API response');
  
  // Extract metadata
  const metadata = apiResponse.metadata || {};
  
  // Create base schema
  const schema = {
    type: 'form',
    id: metadata.formId || `form-${Date.now()}`,
    title: metadata.title || 'Form',
    description: metadata.description,
    submitUrl: metadata.submitUrl || '/api/submit',
    method: metadata.method || 'POST',
    sections: [],
    actions: [],
    metadata: {
      createdAt: new Date().toISOString(),
      version: '1.0',
      source: 'auto-generated'
    }
  };
  
  // Generate sections and fields based on data structure
  if (apiResponse.data && typeof apiResponse.data === 'object') {
    // Group fields into sections if the API response has a clear structure
    const sections = [];
    
    // Check if data has clear sections
    if (Array.isArray(apiResponse.data)) {
      // Each array item becomes a section
      apiResponse.data.forEach((sectionData, index) => {
        if (typeof sectionData === 'object') {
          sections.push(generateSection(sectionData, index));
        }
      });
    } else {
      // Each top-level property becomes a section
      Object.keys(apiResponse.data).forEach((key, index) => {
        const sectionData = apiResponse.data[key];
        if (typeof sectionData === 'object' && !Array.isArray(sectionData)) {
          sections.push(generateSection(sectionData, index, key));
        }
      });
      
      // If no sections were created, create a default section with all fields
      if (sections.length === 0) {
        sections.push({
          id: 'default-section',
          title: 'Information',
          fields: generateFieldsFromObject(apiResponse.data)
        });
      }
    }
    
    schema.sections = sections;
  }
  
  // Generate actions
  schema.actions = [
    {
      id: 'submit',
      type: 'submit',
      label: 'Submit',
      primary: true
    },
    {
      id: 'cancel',
      type: 'button',
      label: 'Cancel',
      primary: false
    }
  ];
  
  return schema;
}

/**
 * Generate section from object
 * 
 * @param {Object} sectionData - Section data
 * @param {number} index - Section index
 * @param {string} key - Section key
 * @returns {Object} - Generated section
 * @private
 */
function generateSection(sectionData, index, key = null) {
  const sectionId = key || `section-${index}`;
  const sectionTitle = key ? key.charAt(0).toUpperCase() + key.slice(1) : `Section ${index + 1}`;
  
  return {
    id: sectionId,
    title: sectionTitle,
    fields: generateFieldsFromObject(sectionData)
  };
}

/**
 * Generate fields from object
 * 
 * @param {Object} data - Data object
 * @returns {Array} - Generated fields
 * @private
 */
function generateFieldsFromObject(data) {
  const fields = [];
  
  Object.keys(data).forEach(key => {
    const value = data[key];
    const field = generateField(key, value);
    
    if (field) {
      fields.push(field);
    }
  });
  
  return fields;
}

/**
 * Generate field from key and value
 * 
 * @param {string} key - Field key
 * @param {*} value - Field value
 * @returns {Object|null} - Generated field or null if field cannot be generated
 * @private
 */
function generateField(key, value) {
  // Skip complex objects and arrays
  if (typeof value === 'object' && value !== null) {
    return null;
  }
  
  // Create field ID and label
  const fieldId = key;
  const fieldLabel = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  
  // Determine field type based on value
  let fieldType = 'text';
  let fieldProps = {};
  
  if (typeof value === 'number') {
    fieldType = 'number';
  } else if (typeof value === 'boolean') {
    fieldType = 'checkbox';
  } else if (typeof value === 'string') {
    // Try to determine more specific field types
    if (/^\d{4}-\d{2}-\d{2}/.test(value)) {
      fieldType = 'date';
    } else if (/@/.test(value)) {
      fieldType = 'email';
    } else if (/^https?:\/\//.test(value)) {
      fieldType = 'url';
    } else if (value.length > 100) {
      fieldType = 'textarea';
      fieldProps.rows = 3;
    }
  }
  
  // Create field
  return {
    id: fieldId,
    type: fieldType,
    label: fieldLabel,
    defaultValue: value,
    required: false,
    ...fieldProps
  };
}

/**
 * Generate dashboard schema from API response
 * 
 * @param {Object} apiResponse - API response
 * @returns {Object} - Generated dashboard schema
 * @private
 */
function generateDashboardSchema(apiResponse) {
  logger.debug('Generating dashboard schema from API response');
  
  // Extract metadata
  const metadata = apiResponse.metadata || {};
  
  // Create base schema
  const schema = {
    type: 'dashboard',
    id: metadata.dashboardId || `dashboard-${Date.now()}`,
    title: metadata.title || 'Dashboard',
    description: metadata.description,
    sections: [],
    filters: [],
    metadata: {
      createdAt: new Date().toISOString(),
      version: '1.0',
      source: 'auto-generated'
    }
  };
  
  // Generate widgets based on data structure
  if (apiResponse.data && typeof apiResponse.data === 'object') {
    // Create a default section
    const defaultSection = {
      id: 'default-section',
      title: 'Overview',
      widgets: []
    };
    
    // Generate widgets based on data structure
    if (Array.isArray(apiResponse.data)) {
      // Generate a table widget for array data
      defaultSection.widgets.push(generateTableWidget(apiResponse.data));
    } else {
      // Generate widgets for each property
      Object.keys(apiResponse.data).forEach((key, index) => {
        const widgetData = apiResponse.data[key];
        const widget = generateWidget(key, widgetData, index);
        
        if (widget) {
          defaultSection.widgets.push(widget);
        }
      });
    }
    
    schema.sections.push(defaultSection);
  }
  
  return schema;
}

/**
 * Generate widget from key and value
 * 
 * @param {string} key - Widget key
 * @param {*} value - Widget value
 * @param {number} index - Widget index
 * @returns {Object|null} - Generated widget or null if widget cannot be generated
 * @private
 */
function generateWidget(key, value, index) {
  const widgetId = `widget-${index}`;
  const widgetTitle = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  
  // Determine widget type based on value
  if (Array.isArray(value)) {
    // Generate table widget for arrays
    return generateTableWidget(value, widgetId, widgetTitle);
  } else if (typeof value === 'object' && value !== null) {
    // Generate chart widget for objects with numeric values
    const hasNumericValues = Object.values(value).some(v => typeof v === 'number');
    
    if (hasNumericValues) {
      return generateChartWidget(value, widgetId, widgetTitle);
    } else {
      return generateMetricWidget(value, widgetId, widgetTitle);
    }
  } else if (typeof value === 'number') {
    // Generate metric widget for numbers
    return generateMetricWidget({ value }, widgetId, widgetTitle);
  }
  
  return null;
}

/**
 * Generate table widget
 * 
 * @param {Array} data - Table data
 * @param {string} id - Widget ID
 * @param {string} title - Widget title
 * @returns {Object} - Generated table widget
 * @private
 */
function generateTableWidget(data, id = 'table-widget', title = 'Table') {
  // Extract columns from the first item
  const columns = [];
  
  if (data.length > 0) {
    const firstItem = data[0];
    
    if (typeof firstItem === 'object' && firstItem !== null) {
      Object.keys(firstItem).forEach(key => {
        columns.push({
          id: key,
          label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
          type: typeof firstItem[key]
        });
      });
    }
  }
  
  return {
    id,
    type: 'table',
    title,
    tableConfig: {
      columns,
      pagination: true,
      pageSize: 10
    }
  };
}

/**
 * Generate chart widget
 * 
 * @param {Object} data - Chart data
 * @param {string} id - Widget ID
 * @param {string} title - Widget title
 * @returns {Object} - Generated chart widget
 * @private
 */
function generateChartWidget(data, id = 'chart-widget', title = 'Chart') {
  // Determine chart type based on data structure
  const chartType = Object.keys(data).length > 5 ? 'bar' : 'pie';
  
  return {
    id,
    type: 'chart',
    title,
    chartConfig: {
      type: chartType,
      data: {
        labels: Object.keys(data),
        datasets: [{
          data: Object.values(data)
        }]
      },
      options: {
        responsive: true
      }
    }
  };
}

/**
 * Generate metric widget
 * 
 * @param {Object} data - Metric data
 * @param {string} id - Widget ID
 * @param {string} title - Widget title
 * @returns {Object} - Generated metric widget
 * @private
 */
function generateMetricWidget(data, id = 'metric-widget', title = 'Metric') {
  // Extract value and trend
  const value = data.value || Object.values(data)[0] || 0;
  const previousValue = data.previousValue || 0;
  const trend = previousValue ? ((value - previousValue) / previousValue) * 100 : 0;
  
  return {
    id,
    type: 'metric',
    title,
    metricConfig: {
      value,
      previousValue,
      trend,
      format: typeof value === 'number' ? 'number' : 'text'
    }
  };
}

/**
 * Generate report schema from API response
 * 
 * @param {Object} apiResponse - API response
 * @returns {Object} - Generated report schema
 * @private
 */
function generateReportSchema(apiResponse) {
  logger.debug('Generating report schema from API response');
  
  // Extract metadata
  const metadata = apiResponse.metadata || {};
  
  // Create base schema
  const schema = {
    type: 'report',
    id: metadata.reportId || `report-${Date.now()}`,
    title: metadata.title || 'Report',
    description: metadata.description,
    sections: [],
    parameters: [],
    metadata: {
      createdAt: new Date().toISOString(),
      version: '1.0',
      source: 'auto-generated'
    }
  };
  
  // Generate sections and elements based on data structure
  if (apiResponse.data && typeof apiResponse.data === 'object') {
    // Create a default section
    const defaultSection = {
      id: 'default-section',
      title: 'Report Content',
      elements: []
    };
    
    // Add title element
    defaultSection.elements.push({
      id: 'title-element',
      type: 'text',
      textConfig: {
        content: schema.title,
        format: 'heading'
      }
    });
    
    // Add description element if available
    if (schema.description) {
      defaultSection.elements.push({
        id: 'description-element',
        type: 'text',
        textConfig: {
          content: schema.description,
          format: 'paragraph'
        }
      });
    }
    
    // Generate elements based on data structure
    if (Array.isArray(apiResponse.data)) {
      // Generate a table element for array data
      defaultSection.elements.push(generateTableElement(apiResponse.data));
    } else {
      // Generate elements for each property
      Object.keys(apiResponse.data).forEach((key, index) => {
        const elementData = apiResponse.data[key];
        const element = generateReportElement(key, elementData, index);
        
        if (element) {
          defaultSection.elements.push(element);
        }
      });
    }
    
    schema.sections.push(defaultSection);
  }
  
  return schema;
}

/**
 * Generate report element from key and value
 * 
 * @param {string} key - Element key
 * @param {*} value - Element value
 * @param {number} index - Element index
 * @returns {Object|null} - Generated element or null if element cannot be generated
 * @private
 */
function generateReportElement(key, value, index) {
  const elementId = `element-${index}`;
  const elementTitle = key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1');
  
  // Determine element type based on value
  if (Array.isArray(value)) {
    // Generate table element for arrays
    return generateTableElement(value, elementId, elementTitle);
  } else if (typeof value === 'object' && value !== null) {
    // Generate chart element for objects with numeric values
    const hasNumericValues = Object.values(value).some(v => typeof v === 'number');
    
    if (hasNumericValues) {
      return generateChartElement(value, elementId, elementTitle);
    } else {
      return generateTextElement(JSON.stringify(value, null, 2), elementId, elementTitle);
    }
  } else {
    // Generate text element for primitive values
    return generateTextElement(value.toString(), elementId, elementTitle);
  }
}

/**
 * Generate table element
 * 
 * @param {Array} data - Table data
 * @param {string} id - Element ID
 * @param {string} title - Element title
 * @returns {Object} - Generated table element
 * @private
 */
function generateTableElement(data, id = 'table-element', title = 'Table') {
  // Extract columns from the first item
  const columns = [];
  
  if (data.length > 0) {
    const firstItem = data[0];
    
    if (typeof firstItem === 'object' && firstItem !== null) {
      Object.keys(firstItem).forEach(key => {
        columns.push({
          id: key,
          label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
          type: typeof firstItem[key]
        });
      });
    }
  }
  
  return {
    id,
    type: 'table',
    title,
    tableConfig: {
      columns,
      data
    }
  };
}

/**
 * Generate chart element
 * 
 * @param {Object} data - Chart data
 * @param {string} id - Element ID
 * @param {string} title - Element title
 * @returns {Object} - Generated chart element
 * @private
 */
function generateChartElement(data, id = 'chart-element', title = 'Chart') {
  // Determine chart type based on data structure
  const chartType = Object.keys(data).length > 5 ? 'bar' : 'pie';
  
  return {
    id,
    type: 'chart',
    title,
    chartConfig: {
      type: chartType,
      data: {
        labels: Object.keys(data),
        datasets: [{
          data: Object.values(data)
        }]
      },
      options: {
        responsive: true
      }
    }
  };
}

/**
 * Generate text element
 * 
 * @param {string} content - Text content
 * @param {string} id - Element ID
 * @param {string} title - Element title
 * @returns {Object} - Generated text element
 * @private
 */
function generateTextElement(content, id = 'text-element', title = null) {
  return {
    id,
    type: 'text',
    title,
    textConfig: {
      content,
      format: 'paragraph'
    }
  };
}

/**
 * Validate schema
 * 
 * @param {Object} schema - Schema to validate
 * @returns {Object} - Validation result
 */
function validateSchema(schema) {
  logger.debug('Validating schema', { schemaType: schema.type });
  
  // Validate schema based on type
  switch (schema.type) {
    case 'form':
      return schemaValidator.validateFormSchema(schema);
    case 'dashboard':
      return schemaValidator.validateDashboardSchema(schema);
    case 'report':
      return schemaValidator.validateReportSchema(schema);
    default:
      logger.warn('Unknown schema type', { schemaType: schema.type });
      throw new Error(`Unknown schema type: ${schema.type}`);
  }
}

module.exports = {
  generateSchemaFromApiResponse,
  validateSchema
};
