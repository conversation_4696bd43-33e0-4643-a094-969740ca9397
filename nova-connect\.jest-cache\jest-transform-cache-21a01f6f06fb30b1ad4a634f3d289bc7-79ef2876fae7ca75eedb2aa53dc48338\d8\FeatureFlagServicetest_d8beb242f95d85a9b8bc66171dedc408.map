{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "jest", "require", "FeatureFlagService", "PackageConfigRegistry", "fs", "promises", "path", "os", "describe", "featureFlagService", "testDir", "beforeEach", "join", "tmpdir", "Date", "now", "mkdir", "recursive", "mockImplementation", "hasTenantFeatureAccess", "fn", "mockResolvedValue", "getTenantFeatureLimit", "getTenantAvailableFeatures", "getTenantMapping", "tenantId", "packageId", "customFeatures", "customLimits", "connections", "getPackageById", "id", "name", "features", "limits", "getAllPackages", "setTenantMapping", "clearCache", "loadData", "saveData", "getUserEntitlement", "userId", "tierId", "getSubscriptionTierById", "getFeatureFlagById", "enabled", "professional", "requests_per_day", "getAllFeatureFlags", "after<PERSON>ach", "rm", "force", "error", "console", "clearAllMocks", "test", "hasAccessWithTenant", "hasFeatureAccess", "expect", "packageRegistry", "toHaveBeenCalledWith", "toBe", "mockResolvedValueOnce", "hasAccessWithTenantFallback", "hasAccess", "not", "toHaveBeenCalled", "limitWithTenant", "getFeatureLimit", "limitWithTenantFallback", "limit", "featuresWithTenant", "getUserAvailableFeatures", "toHave<PERSON>ength", "pkg", "getTenantPackage", "mapping", "setTenantPackage", "packages"], "sources": ["FeatureFlagService.test.js"], "sourcesContent": ["/**\n * Feature Flag Service Tests\n */\n\nconst FeatureFlagService = require('../../../api/services/FeatureFlagService');\nconst PackageConfigRegistry = require('../../../api/services/PackageConfigRegistry');\nconst fs = require('fs').promises;\nconst path = require('path');\nconst os = require('os');\n\n// Mock the PackageConfigRegistry\njest.mock('../../../api/services/PackageConfigRegistry');\n\ndescribe('FeatureFlagService', () => {\n  let featureFlagService;\n  let testDir;\n  \n  beforeEach(async () => {\n    // Create a temporary directory for testing\n    testDir = path.join(os.tmpdir(), `nova-connect-test-${Date.now()}`);\n    await fs.mkdir(testDir, { recursive: true });\n    \n    // Mock PackageConfigRegistry methods\n    PackageConfigRegistry.mockImplementation(() => ({\n      hasTenantFeatureAccess: jest.fn().mockResolvedValue(true),\n      getTenantFeatureLimit: jest.fn().mockResolvedValue(100),\n      getTenantAvailableFeatures: jest.fn().mockResolvedValue(['feature1', 'feature2']),\n      getTenantMapping: jest.fn().mockResolvedValue({\n        tenantId: 'test-tenant',\n        packageId: 'enterprise',\n        customFeatures: ['custom.feature1'],\n        customLimits: { connections: 200 }\n      }),\n      getPackageById: jest.fn().mockResolvedValue({\n        id: 'enterprise',\n        name: 'Enterprise Package',\n        features: ['feature1', 'feature2', 'feature3'],\n        limits: { connections: 100 }\n      }),\n      getAllPackages: jest.fn().mockResolvedValue([\n        { id: 'core', name: 'Core Package' },\n        { id: 'enterprise', name: 'Enterprise Package' }\n      ]),\n      setTenantMapping: jest.fn().mockResolvedValue({\n        tenantId: 'test-tenant',\n        packageId: 'enterprise',\n        customFeatures: ['custom.feature1'],\n        customLimits: { connections: 200 }\n      }),\n      clearCache: jest.fn()\n    }));\n    \n    // Initialize the feature flag service with the test directory\n    featureFlagService = new FeatureFlagService(testDir);\n    \n    // Mock methods to avoid file system operations\n    featureFlagService.loadData = jest.fn().mockResolvedValue([]);\n    featureFlagService.saveData = jest.fn().mockResolvedValue();\n    \n    // Mock methods for user entitlements and subscription tiers\n    featureFlagService.getUserEntitlement = jest.fn().mockResolvedValue({\n      userId: 'test-user',\n      tierId: 'professional',\n      customFeatures: ['custom.user.feature'],\n      customLimits: {}\n    });\n    \n    featureFlagService.getSubscriptionTierById = jest.fn().mockResolvedValue({\n      id: 'professional',\n      name: 'Professional',\n      features: ['feature1', 'feature2']\n    });\n    \n    featureFlagService.getFeatureFlagById = jest.fn().mockResolvedValue({\n      id: 'feature1',\n      name: 'Feature 1',\n      enabled: true,\n      limits: {\n        professional: {\n          requests_per_day: 1000\n        }\n      }\n    });\n    \n    featureFlagService.getAllFeatureFlags = jest.fn().mockResolvedValue([\n      {\n        id: 'feature1',\n        name: 'Feature 1',\n        enabled: true\n      },\n      {\n        id: 'feature2',\n        name: 'Feature 2',\n        enabled: true\n      },\n      {\n        id: 'feature3',\n        name: 'Feature 3',\n        enabled: false\n      }\n    ]);\n  });\n  \n  afterEach(async () => {\n    // Clean up the test directory\n    try {\n      await fs.rm(testDir, { recursive: true, force: true });\n    } catch (error) {\n      console.error('Error cleaning up test directory:', error);\n    }\n    \n    // Clear all mocks\n    jest.clearAllMocks();\n  });\n  \n  test('should check if user has feature access with tenant ID', async () => {\n    // Test with tenant ID\n    const hasAccessWithTenant = await featureFlagService.hasFeatureAccess('test-user', 'feature1', 'test-tenant');\n    \n    // Should check tenant access first\n    expect(featureFlagService.packageRegistry.hasTenantFeatureAccess).toHaveBeenCalledWith('test-tenant', 'feature1');\n    expect(hasAccessWithTenant).toBe(true);\n    \n    // Mock tenant access to return false\n    featureFlagService.packageRegistry.hasTenantFeatureAccess.mockResolvedValueOnce(false);\n    \n    // Should fall back to user access\n    const hasAccessWithTenantFallback = await featureFlagService.hasFeatureAccess('test-user', 'feature1', 'test-tenant');\n    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');\n    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');\n    expect(hasAccessWithTenantFallback).toBe(true);\n  });\n  \n  test('should check if user has feature access without tenant ID', async () => {\n    // Test without tenant ID\n    const hasAccess = await featureFlagService.hasFeatureAccess('test-user', 'feature1');\n    \n    // Should not check tenant access\n    expect(featureFlagService.packageRegistry.hasTenantFeatureAccess).not.toHaveBeenCalled();\n    \n    // Should check user access\n    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');\n    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');\n    expect(hasAccess).toBe(true);\n  });\n  \n  test('should get feature limit with tenant ID', async () => {\n    // Test with tenant ID\n    const limitWithTenant = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'connections', 'test-tenant');\n    \n    // Should check tenant limit first\n    expect(featureFlagService.packageRegistry.getTenantFeatureLimit).toHaveBeenCalledWith('test-tenant', 'connections');\n    expect(limitWithTenant).toBe(100);\n    \n    // Mock tenant limit to return null\n    featureFlagService.packageRegistry.getTenantFeatureLimit.mockResolvedValueOnce(null);\n    \n    // Should fall back to user limit\n    const limitWithTenantFallback = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'requests_per_day', 'test-tenant');\n    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');\n    expect(featureFlagService.getFeatureFlagById).toHaveBeenCalledWith('feature1');\n    expect(limitWithTenantFallback).toBe(1000);\n  });\n  \n  test('should get feature limit without tenant ID', async () => {\n    // Test without tenant ID\n    const limit = await featureFlagService.getFeatureLimit('test-user', 'feature1', 'requests_per_day');\n    \n    // Should not check tenant limit\n    expect(featureFlagService.packageRegistry.getTenantFeatureLimit).not.toHaveBeenCalled();\n    \n    // Should check user limit\n    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');\n    expect(featureFlagService.getFeatureFlagById).toHaveBeenCalledWith('feature1');\n    expect(limit).toBe(1000);\n  });\n  \n  test('should get user available features with tenant ID', async () => {\n    // Test with tenant ID\n    const featuresWithTenant = await featureFlagService.getUserAvailableFeatures('test-user', 'test-tenant');\n    \n    // Should get tenant features\n    expect(featureFlagService.packageRegistry.getTenantAvailableFeatures).toHaveBeenCalledWith('test-tenant');\n    \n    // Should also get user features\n    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');\n    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');\n    expect(featureFlagService.getAllFeatureFlags).toHaveBeenCalled();\n    \n    // Should return enabled features that are in tenant features, user features, or subscription features\n    expect(featuresWithTenant).toHaveLength(2);\n  });\n  \n  test('should get user available features without tenant ID', async () => {\n    // Test without tenant ID\n    const features = await featureFlagService.getUserAvailableFeatures('test-user');\n    \n    // Should not get tenant features\n    expect(featureFlagService.packageRegistry.getTenantAvailableFeatures).not.toHaveBeenCalled();\n    \n    // Should get user features\n    expect(featureFlagService.getUserEntitlement).toHaveBeenCalledWith('test-user');\n    expect(featureFlagService.getSubscriptionTierById).toHaveBeenCalledWith('professional');\n    expect(featureFlagService.getAllFeatureFlags).toHaveBeenCalled();\n    \n    // Should return enabled features that are in user features or subscription features\n    expect(features).toHaveLength(2);\n  });\n  \n  test('should get tenant package', async () => {\n    // Test getting tenant package\n    const pkg = await featureFlagService.getTenantPackage('test-tenant');\n    \n    // Should get tenant mapping\n    expect(featureFlagService.packageRegistry.getTenantMapping).toHaveBeenCalledWith('test-tenant');\n    \n    // Should get package details\n    expect(featureFlagService.packageRegistry.getPackageById).toHaveBeenCalledWith('enterprise');\n    \n    // Should return package details\n    expect(pkg.id).toBe('enterprise');\n    expect(pkg.name).toBe('Enterprise Package');\n  });\n  \n  test('should set tenant package', async () => {\n    // Test setting tenant package\n    const mapping = await featureFlagService.setTenantPackage('test-tenant', 'enterprise', ['custom.feature1'], { connections: 200 });\n    \n    // Should set tenant mapping\n    expect(featureFlagService.packageRegistry.setTenantMapping).toHaveBeenCalledWith('test-tenant', 'enterprise', ['custom.feature1'], { connections: 200 });\n    \n    // Should return mapping\n    expect(mapping.tenantId).toBe('test-tenant');\n    expect(mapping.packageId).toBe('enterprise');\n  });\n  \n  test('should get all packages', async () => {\n    // Test getting all packages\n    const packages = await featureFlagService.getAllPackages();\n    \n    // Should get all packages\n    expect(featureFlagService.packageRegistry.getAllPackages).toHaveBeenCalled();\n    \n    // Should return packages\n    expect(packages).toHaveLength(2);\n    expect(packages[0].id).toBe('core');\n    expect(packages[1].id).toBe('enterprise');\n  });\n  \n  test('should get package by ID', async () => {\n    // Test getting package by ID\n    const pkg = await featureFlagService.getPackageById('enterprise');\n    \n    // Should get package by ID\n    expect(featureFlagService.packageRegistry.getPackageById).toHaveBeenCalledWith('enterprise');\n    \n    // Should return package\n    expect(pkg.id).toBe('enterprise');\n    expect(pkg.name).toBe('Enterprise Package');\n  });\n  \n  test('should clear cache', async () => {\n    // Test clearing cache\n    featureFlagService.clearCache();\n    \n    // Should clear package registry cache\n    expect(featureFlagService.packageRegistry.clearCache).toHaveBeenCalled();\n  });\n});\n"], "mappings": "AAUA;AACAA,WAAA,GAAKC,IAAI,CAAC,6CAA6C,CAAC;AAAC,SAAAD,YAAA;EAAA;IAAAE;EAAA,IAAAC,OAAA;EAAAH,WAAA,GAAAA,CAAA,KAAAE,IAAA;EAAA,OAAAA,IAAA;AAAA;AAXzD;AACA;AACA;;AAEA,MAAME,kBAAkB,GAAGD,OAAO,CAAC,0CAA0C,CAAC;AAC9E,MAAME,qBAAqB,GAAGF,OAAO,CAAC,6CAA6C,CAAC;AACpF,MAAMG,EAAE,GAAGH,OAAO,CAAC,IAAI,CAAC,CAACI,QAAQ;AACjC,MAAMC,IAAI,GAAGL,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMM,EAAE,GAAGN,OAAO,CAAC,IAAI,CAAC;AAKxBO,QAAQ,CAAC,oBAAoB,EAAE,MAAM;EACnC,IAAIC,kBAAkB;EACtB,IAAIC,OAAO;EAEXC,UAAU,CAAC,YAAY;IACrB;IACAD,OAAO,GAAGJ,IAAI,CAACM,IAAI,CAACL,EAAE,CAACM,MAAM,CAAC,CAAC,EAAE,qBAAqBC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC;IACnE,MAAMX,EAAE,CAACY,KAAK,CAACN,OAAO,EAAE;MAAEO,SAAS,EAAE;IAAK,CAAC,CAAC;;IAE5C;IACAd,qBAAqB,CAACe,kBAAkB,CAAC,OAAO;MAC9CC,sBAAsB,EAAEnB,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,IAAI,CAAC;MACzDC,qBAAqB,EAAEtB,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,GAAG,CAAC;MACvDE,0BAA0B,EAAEvB,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;MACjFG,gBAAgB,EAAExB,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;QAC5CI,QAAQ,EAAE,aAAa;QACvBC,SAAS,EAAE,YAAY;QACvBC,cAAc,EAAE,CAAC,iBAAiB,CAAC;QACnCC,YAAY,EAAE;UAAEC,WAAW,EAAE;QAAI;MACnC,CAAC,CAAC;MACFC,cAAc,EAAE9B,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;QAC1CU,EAAE,EAAE,YAAY;QAChBC,IAAI,EAAE,oBAAoB;QAC1BC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC;QAC9CC,MAAM,EAAE;UAAEL,WAAW,EAAE;QAAI;MAC7B,CAAC,CAAC;MACFM,cAAc,EAAEnC,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAC1C;QAAEU,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAe,CAAC,EACpC;QAAED,EAAE,EAAE,YAAY;QAAEC,IAAI,EAAE;MAAqB,CAAC,CACjD,CAAC;MACFI,gBAAgB,EAAEpC,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;QAC5CI,QAAQ,EAAE,aAAa;QACvBC,SAAS,EAAE,YAAY;QACvBC,cAAc,EAAE,CAAC,iBAAiB,CAAC;QACnCC,YAAY,EAAE;UAAEC,WAAW,EAAE;QAAI;MACnC,CAAC,CAAC;MACFQ,UAAU,EAAErC,IAAI,CAACoB,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC;;IAEH;IACAX,kBAAkB,GAAG,IAAIP,kBAAkB,CAACQ,OAAO,CAAC;;IAEpD;IACAD,kBAAkB,CAAC6B,QAAQ,GAAGtC,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,EAAE,CAAC;IAC7DZ,kBAAkB,CAAC8B,QAAQ,GAAGvC,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC;;IAE3D;IACAZ,kBAAkB,CAAC+B,kBAAkB,GAAGxC,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;MAClEoB,MAAM,EAAE,WAAW;MACnBC,MAAM,EAAE,cAAc;MACtBf,cAAc,EAAE,CAAC,qBAAqB,CAAC;MACvCC,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC;IAEFnB,kBAAkB,CAACkC,uBAAuB,GAAG3C,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;MACvEU,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU;IACnC,CAAC,CAAC;IAEFxB,kBAAkB,CAACmC,kBAAkB,GAAG5C,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;MAClEU,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,WAAW;MACjBa,OAAO,EAAE,IAAI;MACbX,MAAM,EAAE;QACNY,YAAY,EAAE;UACZC,gBAAgB,EAAE;QACpB;MACF;IACF,CAAC,CAAC;IAEFtC,kBAAkB,CAACuC,kBAAkB,GAAGhD,IAAI,CAACoB,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAClE;MACEU,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,WAAW;MACjBa,OAAO,EAAE;IACX,CAAC,EACD;MACEd,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,WAAW;MACjBa,OAAO,EAAE;IACX,CAAC,EACD;MACEd,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,WAAW;MACjBa,OAAO,EAAE;IACX,CAAC,CACF,CAAC;EACJ,CAAC,CAAC;EAEFI,SAAS,CAAC,YAAY;IACpB;IACA,IAAI;MACF,MAAM7C,EAAE,CAAC8C,EAAE,CAACxC,OAAO,EAAE;QAAEO,SAAS,EAAE,IAAI;QAAEkC,KAAK,EAAE;MAAK,CAAC,CAAC;IACxD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC3D;;IAEA;IACApD,IAAI,CAACsD,aAAa,CAAC,CAAC;EACtB,CAAC,CAAC;EAEFC,IAAI,CAAC,wDAAwD,EAAE,YAAY;IACzE;IACA,MAAMC,mBAAmB,GAAG,MAAM/C,kBAAkB,CAACgD,gBAAgB,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC;;IAE7G;IACAC,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACxC,sBAAsB,CAAC,CAACyC,oBAAoB,CAAC,aAAa,EAAE,UAAU,CAAC;IACjHF,MAAM,CAACF,mBAAmB,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC;;IAEtC;IACApD,kBAAkB,CAACkD,eAAe,CAACxC,sBAAsB,CAAC2C,qBAAqB,CAAC,KAAK,CAAC;;IAEtF;IACA,MAAMC,2BAA2B,GAAG,MAAMtD,kBAAkB,CAACgD,gBAAgB,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,CAAC;IACrHC,MAAM,CAACjD,kBAAkB,CAAC+B,kBAAkB,CAAC,CAACoB,oBAAoB,CAAC,WAAW,CAAC;IAC/EF,MAAM,CAACjD,kBAAkB,CAACkC,uBAAuB,CAAC,CAACiB,oBAAoB,CAAC,cAAc,CAAC;IACvFF,MAAM,CAACK,2BAA2B,CAAC,CAACF,IAAI,CAAC,IAAI,CAAC;EAChD,CAAC,CAAC;EAEFN,IAAI,CAAC,2DAA2D,EAAE,YAAY;IAC5E;IACA,MAAMS,SAAS,GAAG,MAAMvD,kBAAkB,CAACgD,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC;;IAEpF;IACAC,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACxC,sBAAsB,CAAC,CAAC8C,GAAG,CAACC,gBAAgB,CAAC,CAAC;;IAExF;IACAR,MAAM,CAACjD,kBAAkB,CAAC+B,kBAAkB,CAAC,CAACoB,oBAAoB,CAAC,WAAW,CAAC;IAC/EF,MAAM,CAACjD,kBAAkB,CAACkC,uBAAuB,CAAC,CAACiB,oBAAoB,CAAC,cAAc,CAAC;IACvFF,MAAM,CAACM,SAAS,CAAC,CAACH,IAAI,CAAC,IAAI,CAAC;EAC9B,CAAC,CAAC;EAEFN,IAAI,CAAC,yCAAyC,EAAE,YAAY;IAC1D;IACA,MAAMY,eAAe,GAAG,MAAM1D,kBAAkB,CAAC2D,eAAe,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,CAAC;;IAEvH;IACAV,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACrC,qBAAqB,CAAC,CAACsC,oBAAoB,CAAC,aAAa,EAAE,aAAa,CAAC;IACnHF,MAAM,CAACS,eAAe,CAAC,CAACN,IAAI,CAAC,GAAG,CAAC;;IAEjC;IACApD,kBAAkB,CAACkD,eAAe,CAACrC,qBAAqB,CAACwC,qBAAqB,CAAC,IAAI,CAAC;;IAEpF;IACA,MAAMO,uBAAuB,GAAG,MAAM5D,kBAAkB,CAAC2D,eAAe,CAAC,WAAW,EAAE,UAAU,EAAE,kBAAkB,EAAE,aAAa,CAAC;IACpIV,MAAM,CAACjD,kBAAkB,CAAC+B,kBAAkB,CAAC,CAACoB,oBAAoB,CAAC,WAAW,CAAC;IAC/EF,MAAM,CAACjD,kBAAkB,CAACmC,kBAAkB,CAAC,CAACgB,oBAAoB,CAAC,UAAU,CAAC;IAC9EF,MAAM,CAACW,uBAAuB,CAAC,CAACR,IAAI,CAAC,IAAI,CAAC;EAC5C,CAAC,CAAC;EAEFN,IAAI,CAAC,4CAA4C,EAAE,YAAY;IAC7D;IACA,MAAMe,KAAK,GAAG,MAAM7D,kBAAkB,CAAC2D,eAAe,CAAC,WAAW,EAAE,UAAU,EAAE,kBAAkB,CAAC;;IAEnG;IACAV,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACrC,qBAAqB,CAAC,CAAC2C,GAAG,CAACC,gBAAgB,CAAC,CAAC;;IAEvF;IACAR,MAAM,CAACjD,kBAAkB,CAAC+B,kBAAkB,CAAC,CAACoB,oBAAoB,CAAC,WAAW,CAAC;IAC/EF,MAAM,CAACjD,kBAAkB,CAACmC,kBAAkB,CAAC,CAACgB,oBAAoB,CAAC,UAAU,CAAC;IAC9EF,MAAM,CAACY,KAAK,CAAC,CAACT,IAAI,CAAC,IAAI,CAAC;EAC1B,CAAC,CAAC;EAEFN,IAAI,CAAC,mDAAmD,EAAE,YAAY;IACpE;IACA,MAAMgB,kBAAkB,GAAG,MAAM9D,kBAAkB,CAAC+D,wBAAwB,CAAC,WAAW,EAAE,aAAa,CAAC;;IAExG;IACAd,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACpC,0BAA0B,CAAC,CAACqC,oBAAoB,CAAC,aAAa,CAAC;;IAEzG;IACAF,MAAM,CAACjD,kBAAkB,CAAC+B,kBAAkB,CAAC,CAACoB,oBAAoB,CAAC,WAAW,CAAC;IAC/EF,MAAM,CAACjD,kBAAkB,CAACkC,uBAAuB,CAAC,CAACiB,oBAAoB,CAAC,cAAc,CAAC;IACvFF,MAAM,CAACjD,kBAAkB,CAACuC,kBAAkB,CAAC,CAACkB,gBAAgB,CAAC,CAAC;;IAEhE;IACAR,MAAM,CAACa,kBAAkB,CAAC,CAACE,YAAY,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EAEFlB,IAAI,CAAC,sDAAsD,EAAE,YAAY;IACvE;IACA,MAAMtB,QAAQ,GAAG,MAAMxB,kBAAkB,CAAC+D,wBAAwB,CAAC,WAAW,CAAC;;IAE/E;IACAd,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACpC,0BAA0B,CAAC,CAAC0C,GAAG,CAACC,gBAAgB,CAAC,CAAC;;IAE5F;IACAR,MAAM,CAACjD,kBAAkB,CAAC+B,kBAAkB,CAAC,CAACoB,oBAAoB,CAAC,WAAW,CAAC;IAC/EF,MAAM,CAACjD,kBAAkB,CAACkC,uBAAuB,CAAC,CAACiB,oBAAoB,CAAC,cAAc,CAAC;IACvFF,MAAM,CAACjD,kBAAkB,CAACuC,kBAAkB,CAAC,CAACkB,gBAAgB,CAAC,CAAC;;IAEhE;IACAR,MAAM,CAACzB,QAAQ,CAAC,CAACwC,YAAY,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC;EAEFlB,IAAI,CAAC,2BAA2B,EAAE,YAAY;IAC5C;IACA,MAAMmB,GAAG,GAAG,MAAMjE,kBAAkB,CAACkE,gBAAgB,CAAC,aAAa,CAAC;;IAEpE;IACAjB,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACnC,gBAAgB,CAAC,CAACoC,oBAAoB,CAAC,aAAa,CAAC;;IAE/F;IACAF,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAAC7B,cAAc,CAAC,CAAC8B,oBAAoB,CAAC,YAAY,CAAC;;IAE5F;IACAF,MAAM,CAACgB,GAAG,CAAC3C,EAAE,CAAC,CAAC8B,IAAI,CAAC,YAAY,CAAC;IACjCH,MAAM,CAACgB,GAAG,CAAC1C,IAAI,CAAC,CAAC6B,IAAI,CAAC,oBAAoB,CAAC;EAC7C,CAAC,CAAC;EAEFN,IAAI,CAAC,2BAA2B,EAAE,YAAY;IAC5C;IACA,MAAMqB,OAAO,GAAG,MAAMnE,kBAAkB,CAACoE,gBAAgB,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,iBAAiB,CAAC,EAAE;MAAEhD,WAAW,EAAE;IAAI,CAAC,CAAC;;IAEjI;IACA6B,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACvB,gBAAgB,CAAC,CAACwB,oBAAoB,CAAC,aAAa,EAAE,YAAY,EAAE,CAAC,iBAAiB,CAAC,EAAE;MAAE/B,WAAW,EAAE;IAAI,CAAC,CAAC;;IAExJ;IACA6B,MAAM,CAACkB,OAAO,CAACnD,QAAQ,CAAC,CAACoC,IAAI,CAAC,aAAa,CAAC;IAC5CH,MAAM,CAACkB,OAAO,CAAClD,SAAS,CAAC,CAACmC,IAAI,CAAC,YAAY,CAAC;EAC9C,CAAC,CAAC;EAEFN,IAAI,CAAC,yBAAyB,EAAE,YAAY;IAC1C;IACA,MAAMuB,QAAQ,GAAG,MAAMrE,kBAAkB,CAAC0B,cAAc,CAAC,CAAC;;IAE1D;IACAuB,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACxB,cAAc,CAAC,CAAC+B,gBAAgB,CAAC,CAAC;;IAE5E;IACAR,MAAM,CAACoB,QAAQ,CAAC,CAACL,YAAY,CAAC,CAAC,CAAC;IAChCf,MAAM,CAACoB,QAAQ,CAAC,CAAC,CAAC,CAAC/C,EAAE,CAAC,CAAC8B,IAAI,CAAC,MAAM,CAAC;IACnCH,MAAM,CAACoB,QAAQ,CAAC,CAAC,CAAC,CAAC/C,EAAE,CAAC,CAAC8B,IAAI,CAAC,YAAY,CAAC;EAC3C,CAAC,CAAC;EAEFN,IAAI,CAAC,0BAA0B,EAAE,YAAY;IAC3C;IACA,MAAMmB,GAAG,GAAG,MAAMjE,kBAAkB,CAACqB,cAAc,CAAC,YAAY,CAAC;;IAEjE;IACA4B,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAAC7B,cAAc,CAAC,CAAC8B,oBAAoB,CAAC,YAAY,CAAC;;IAE5F;IACAF,MAAM,CAACgB,GAAG,CAAC3C,EAAE,CAAC,CAAC8B,IAAI,CAAC,YAAY,CAAC;IACjCH,MAAM,CAACgB,GAAG,CAAC1C,IAAI,CAAC,CAAC6B,IAAI,CAAC,oBAAoB,CAAC;EAC7C,CAAC,CAAC;EAEFN,IAAI,CAAC,oBAAoB,EAAE,YAAY;IACrC;IACA9C,kBAAkB,CAAC4B,UAAU,CAAC,CAAC;;IAE/B;IACAqB,MAAM,CAACjD,kBAAkB,CAACkD,eAAe,CAACtB,UAAU,CAAC,CAAC6B,gBAAgB,CAAC,CAAC;EAC1E,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}