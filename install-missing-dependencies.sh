#!/bin/bash
# Script to install missing dependencies for NovaConnect UAC

echo "Installing missing dependencies for NovaConnect UAC..."

# Navigate to the nova-connect directory
cd nova-connect

# Install core dependencies
npm install --save node-cache @google-cloud/logging @google-cloud/monitoring @google-cloud/opentelemetry-cloud-trace-exporter @opentelemetry/sdk-trace-node @google-cloud/service-usage zapier-platform-core

# Install testing dependencies
npm install --save-dev jest-mock-extended

echo "Dependencies installed successfully!"
