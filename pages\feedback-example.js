/**
 * Feedback Example Page
 * 
 * This page demonstrates the feedback functionality.
 */

import React, { useState } from 'react';
import { 
  FeedbackButton, 
  FeedbackDialog,
  ThemeProvider,
  PreferencesProvider,
  OfflineProvider,
  I18nProvider,
  AccessibilityProvider,
  FeedbackProvider
} from '../nova-connect/ui/novavision-hub';

/**
 * Feedback Example Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function FeedbackExamplePage() {
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const handleOpenDialog = () => {
    setDialogOpen(true);
  };
  
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };
  
  return (
    <ThemeProvider>
      <PreferencesProvider>
        <OfflineProvider>
          <I18nProvider>
            <AccessibilityProvider>
              <FeedbackProvider>
                <div className="p-8">
                  <h1 className="text-3xl font-bold mb-6">Feedback Example</h1>
                  
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Feedback Button Variants</h2>
                    
                    <div className="flex flex-wrap gap-4">
                      <div>
                        <h3 className="text-lg mb-2">Floating Button (Bottom Right)</h3>
                        <FeedbackButton 
                          variant="floating" 
                          position="bottom-right" 
                          component="feedback-example"
                        />
                      </div>
                      
                      <div>
                        <h3 className="text-lg mb-2">Inline Button</h3>
                        <FeedbackButton 
                          variant="inline" 
                          component="feedback-example"
                        />
                      </div>
                      
                      <div>
                        <h3 className="text-lg mb-2">Icon Button</h3>
                        <FeedbackButton 
                          variant="icon" 
                          component="feedback-example"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Feedback Dialog</h2>
                    
                    <button
                      className="px-4 py-2 bg-primary text-white rounded-md"
                      onClick={handleOpenDialog}
                    >
                      Open Feedback Dialog
                    </button>
                    
                    <FeedbackDialog
                      open={dialogOpen}
                      onClose={handleCloseDialog}
                      component="feedback-example"
                      type="general"
                    />
                  </div>
                  
                  <div className="mb-8">
                    <h2 className="text-xl font-semibold mb-4">Feedback Types</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {[
                        { type: 'general', label: 'General Feedback' },
                        { type: 'feature', label: 'Feature Request' },
                        { type: 'bug', label: 'Bug Report' },
                        { type: 'suggestion', label: 'Suggestion' },
                        { type: 'usability', label: 'Usability Issue' },
                        { type: 'performance', label: 'Performance Issue' },
                        { type: 'other', label: 'Other' }
                      ].map((item) => (
                        <div key={item.type} className="p-4 border border-divider rounded-md">
                          <h3 className="text-lg font-medium mb-2">{item.label}</h3>
                          <FeedbackButton
                            variant="inline"
                            component="feedback-example"
                            type={item.type}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </FeedbackProvider>
            </AccessibilityProvider>
          </I18nProvider>
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
}
