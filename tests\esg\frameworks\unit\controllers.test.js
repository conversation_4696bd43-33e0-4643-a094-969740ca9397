const controllers = require('../../../../apis/esg/frameworks/controllers');
const models = require('../../../../apis/esg/frameworks/models');

// Mock the models
jest.mock('../../../../apis/esg/frameworks/models', () => ({
  esgFrameworks: [
    {
      id: 'esg-f-********',
      name: 'Global Reporting Initiative (GRI)',
      description: 'The GRI Standards create a common language for organizations to report on their sustainability impacts.',
      version: '2021',
      category: 'reporting',
      website: 'https://www.globalreporting.org',
      status: 'active',
      elements: [
        {
          id: 'gri-201',
          code: 'GRI 201',
          name: 'Economic Performance',
          description: 'Economic value generated and distributed',
          category: 'economic',
          metrics: ['revenue', 'operating-costs', 'employee-wages']
        },
        {
          id: 'gri-305',
          code: 'GRI 305',
          name: 'Emissions',
          description: 'Direct and indirect greenhouse gas emissions',
          category: 'environmental',
          metrics: ['scope-1-emissions', 'scope-2-emissions']
        }
      ],
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'esg-f-********',
      name: 'Sustainability Accounting Standards Board (SASB)',
      description: 'SASB Standards guide the disclosure of financially material sustainability information.',
      version: '2022',
      category: 'reporting',
      website: 'https://www.sasb.org',
      status: 'active',
      elements: [
        {
          id: 'sasb-ghg',
          code: 'GHG-1',
          name: 'Greenhouse Gas Emissions',
          description: 'Accounting metrics for greenhouse gas emissions',
          category: 'environmental',
          metrics: ['scope-1-emissions', 'scope-2-emissions', 'emissions-reduction']
        }
      ],
      createdAt: '2023-01-15T00:00:00Z',
      updatedAt: '2023-01-15T00:00:00Z'
    }
  ],
  frameworkMappings: [
    {
      id: 'mapping-12345',
      sourceFrameworkId: 'esg-f-********',
      sourceElementId: 'gri-305',
      targetFrameworkId: 'esg-f-********',
      targetElementId: 'sasb-ghg',
      mappingType: 'equivalent',
      description: 'GRI 305 Emissions maps to SASB GHG-1',
      confidence: 'high',
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-01T00:00:00Z'
    }
  ]
}));

// Mock Express request and response
const mockRequest = (params = {}, query = {}, body = {}) => ({
  params,
  query,
  body
});

const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('ESG Frameworks Controllers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getFrameworks', () => {
    it('should return all frameworks with default pagination', () => {
      const req = mockRequest({}, {});
      const res = mockResponse();

      controllers.getFrameworks(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgFrameworks,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should filter frameworks by category', () => {
      const req = mockRequest({}, { category: 'reporting' });
      const res = mockResponse();

      controllers.getFrameworks(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgFrameworks,
        pagination: {
          total: 2,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    });

    it('should handle errors', () => {
      const req = mockRequest();
      const res = mockResponse();

      // Force an error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      jest.spyOn(Array.prototype, 'filter').mockImplementation(() => {
        throw new Error('Test error');
      });

      controllers.getFrameworks(req, res);

      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Internal Server Error',
        message: 'Test error'
      });

      // Restore console.error
      console.error.mockRestore();
    });
  });

  describe('getFrameworkById', () => {
    it('should return a specific framework by ID', () => {
      const req = mockRequest({ id: 'esg-f-********' });
      const res = mockResponse();

      controllers.getFrameworkById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgFrameworks[0]
      });
    });

    it('should return 404 if framework not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getFrameworkById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG framework with ID non-existent-id not found'
      });
    });
  });

  describe('createFramework', () => {
    it('should create a new framework', () => {
      const newFramework = {
        name: 'Task Force on Climate-related Financial Disclosures (TCFD)',
        description: 'Framework for climate-related financial disclosures',
        version: '2021',
        category: 'reporting',
        website: 'https://www.fsb-tcfd.org',
        status: 'active',
        elements: [
          {
            code: 'TCFD-1',
            name: 'Governance',
            description: 'Organization\'s governance around climate-related risks and opportunities',
            category: 'governance',
            metrics: ['board-oversight', 'management-role']
          }
        ]
      };

      const req = mockRequest({}, {}, newFramework);
      const res = mockResponse();

      // Mock Date and UUID
      const originalDateNow = Date.now;
      Date.now = jest.fn(() => 1577836800000); // 2020-01-01T00:00:00Z
      global.Date = jest.fn(() => ({
        toISOString: () => '2020-01-01T00:00:00Z'
      }));
      jest.mock('uuid', () => ({
        v4: jest.fn(() => '00000000-0000-0000-0000-000000000000')
      }));

      controllers.createFramework(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          name: 'Task Force on Climate-related Financial Disclosures (TCFD)',
          category: 'reporting'
        }),
        message: 'ESG framework created successfully'
      }));

      // Restore Date
      Date.now = originalDateNow;
    });
  });

  describe('updateFramework', () => {
    it('should update an existing framework', () => {
      const updatedFramework = {
        name: 'Updated Framework Name',
        status: 'inactive'
      };

      const req = mockRequest({ id: 'esg-f-********' }, {}, updatedFramework);
      const res = mockResponse();

      controllers.updateFramework(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'esg-f-********',
          name: 'Updated Framework Name',
          status: 'inactive'
        }),
        message: 'ESG framework updated successfully'
      }));
    });

    it('should return 404 if framework not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateFramework(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG framework with ID non-existent-id not found'
      });
    });
  });

  describe('deleteFramework', () => {
    it('should delete an existing framework', () => {
      const req = mockRequest({ id: 'esg-f-********' });
      const res = mockResponse();

      controllers.deleteFramework(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'ESG framework deleted successfully'
      });
    });

    it('should return 404 if framework not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteFramework(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG framework with ID non-existent-id not found'
      });
    });
  });

  describe('getFrameworkElements', () => {
    it('should return elements for a specific framework', () => {
      const req = mockRequest({ id: 'esg-f-********' });
      const res = mockResponse();

      controllers.getFrameworkElements(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgFrameworks[0].elements
      });
    });

    it('should return 404 if framework not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getFrameworkElements(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG framework with ID non-existent-id not found'
      });
    });
  });

  describe('getFrameworkElementById', () => {
    it('should return a specific element by ID', () => {
      const req = mockRequest({ id: 'esg-f-********', elementId: 'gri-201' });
      const res = mockResponse();

      controllers.getFrameworkElementById(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: models.esgFrameworks[0].elements[0]
      });
    });

    it('should return 404 if element not found', () => {
      const req = mockRequest({ id: 'esg-f-********', elementId: 'non-existent-id' });
      const res = mockResponse();

      controllers.getFrameworkElementById(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Framework element with ID non-existent-id not found'
      });
    });
  });

  describe('addFrameworkElement', () => {
    it('should add a new element to a framework', () => {
      const newElement = {
        code: 'GRI 403',
        name: 'Occupational Health and Safety',
        description: 'Management approach and metrics for occupational health and safety',
        category: 'social',
        metrics: ['injury-rate', 'lost-day-rate']
      };

      const req = mockRequest({ id: 'esg-f-********' }, {}, newElement);
      const res = mockResponse();

      controllers.addFrameworkElement(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          code: 'GRI 403',
          name: 'Occupational Health and Safety'
        }),
        message: 'Framework element added successfully'
      }));
    });

    it('should return 404 if framework not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { name: 'New Element' });
      const res = mockResponse();

      controllers.addFrameworkElement(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'ESG framework with ID non-existent-id not found'
      });
    });
  });

  describe('updateFrameworkElement', () => {
    it('should update an existing element', () => {
      const updatedElement = {
        name: 'Updated Element Name',
        description: 'Updated description'
      };

      const req = mockRequest({ id: 'esg-f-********', elementId: 'gri-201' }, {}, updatedElement);
      const res = mockResponse();

      controllers.updateFrameworkElement(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'gri-201',
          name: 'Updated Element Name',
          description: 'Updated description'
        }),
        message: 'Framework element updated successfully'
      }));
    });

    it('should return 404 if element not found', () => {
      const req = mockRequest({ id: 'esg-f-********', elementId: 'non-existent-id' }, {}, { name: 'Updated Name' });
      const res = mockResponse();

      controllers.updateFrameworkElement(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Framework element with ID non-existent-id not found'
      });
    });
  });

  describe('removeFrameworkElement', () => {
    it('should remove an element from a framework', () => {
      const req = mockRequest({ id: 'esg-f-********', elementId: 'gri-201' });
      const res = mockResponse();

      controllers.removeFrameworkElement(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Framework element removed successfully'
      });
    });

    it('should return 404 if element not found', () => {
      const req = mockRequest({ id: 'esg-f-********', elementId: 'non-existent-id' });
      const res = mockResponse();

      controllers.removeFrameworkElement(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Framework element with ID non-existent-id not found'
      });
    });
  });

  describe('getFrameworkMappings', () => {
    it('should return mappings for a specific framework', () => {
      const req = mockRequest({ id: 'esg-f-********' });
      const res = mockResponse();

      controllers.getFrameworkMappings(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: [models.frameworkMappings[0]]
      });
    });

    it('should return empty array if no mappings found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.getFrameworkMappings(req, res);

      expect(res.json).toHaveBeenCalledWith({
        data: []
      });
    });
  });

  describe('createFrameworkMapping', () => {
    it('should create a new mapping between frameworks', () => {
      const newMapping = {
        sourceFrameworkId: 'esg-f-********',
        sourceElementId: 'gri-201',
        targetFrameworkId: 'esg-f-********',
        targetElementId: 'sasb-ghg',
        mappingType: 'related',
        description: 'Economic performance relates to GHG emissions',
        confidence: 'medium'
      };

      const req = mockRequest({}, {}, newMapping);
      const res = mockResponse();

      controllers.createFrameworkMapping(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          sourceFrameworkId: 'esg-f-********',
          targetFrameworkId: 'esg-f-********',
          mappingType: 'related'
        }),
        message: 'Framework mapping created successfully'
      }));
    });
  });

  describe('updateFrameworkMapping', () => {
    it('should update an existing mapping', () => {
      const updatedMapping = {
        mappingType: 'partial',
        confidence: 'low'
      };

      const req = mockRequest({ id: 'mapping-12345' }, {}, updatedMapping);
      const res = mockResponse();

      controllers.updateFrameworkMapping(req, res);

      expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
        data: expect.objectContaining({
          id: 'mapping-12345',
          mappingType: 'partial',
          confidence: 'low'
        }),
        message: 'Framework mapping updated successfully'
      }));
    });

    it('should return 404 if mapping not found', () => {
      const req = mockRequest({ id: 'non-existent-id' }, {}, { mappingType: 'partial' });
      const res = mockResponse();

      controllers.updateFrameworkMapping(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Framework mapping with ID non-existent-id not found'
      });
    });
  });

  describe('deleteFrameworkMapping', () => {
    it('should delete an existing mapping', () => {
      const req = mockRequest({ id: 'mapping-12345' });
      const res = mockResponse();

      controllers.deleteFrameworkMapping(req, res);

      expect(res.json).toHaveBeenCalledWith({
        message: 'Framework mapping deleted successfully'
      });
    });

    it('should return 404 if mapping not found', () => {
      const req = mockRequest({ id: 'non-existent-id' });
      const res = mockResponse();

      controllers.deleteFrameworkMapping(req, res);

      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Not Found',
        message: 'Framework mapping with ID non-existent-id not found'
      });
    });
  });
});
