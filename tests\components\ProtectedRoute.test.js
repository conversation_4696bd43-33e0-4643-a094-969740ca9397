import React from 'react';
import { render, screen } from '@testing-library/react';
import ProtectedRoute from '../../components/ProtectedRoute';

// Mock the AuthContext module
jest.mock('../../contexts/AuthContext');

// Import the mocked useAuth
const { useAuth } = require('../../contexts/AuthContext');

// Mock the useRouter hook
const mockPush = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: mockPush,
    pathname: '/protected-page'
  })
}));

describe('ProtectedRoute', () => {
  beforeEach(() => {
    // Reset mocks
    mockPush.mockReset();
  });

  it('renders children when user is authenticated', () => {
    // Set up the mock to return authenticated user
    useAuth.mockReturnValue({
      isAuthenticated: true,
      user: { name: 'Test User' },
      loading: false
    });

    render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    // Check if protected content is rendered
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Protected Content')).toBeInTheDocument();

    // Router should not redirect
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('redirects to login page when user is not authenticated', () => {
    // Set up the mock to return unauthenticated user
    useAuth.mockReturnValue({
      isAuthenticated: false,
      user: null,
      loading: false
    });

    render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    // Protected content should not be rendered
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();

    // Router should redirect to login page
    expect(mockPush).toHaveBeenCalled();
  });

  it('shows loading state while checking authentication', () => {
    // Set up the mock to return loading state
    useAuth.mockReturnValue({
      isAuthenticated: null,
      user: null,
      loading: true
    });

    render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    // Protected content should not be rendered
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();

    // Loading indicator should be shown (the actual component shows a spinner, not text)
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();

    // Router should not redirect yet
    expect(mockPush).not.toHaveBeenCalled();
  });
});
