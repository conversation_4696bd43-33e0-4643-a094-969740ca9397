import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

const Resources = () => {
  const sidebarItems = [
    { label: 'Documentation Repository', href: '/resources/documentation' },
    { label: 'White Papers', href: '/resources/white-papers' },
    { label: 'API Documentation', href: '/api-docs' },
    { label: 'UAC Demo', href: '/uac-demo' },
    { label: 'Partner Knowledge Base', href: '/partner-knowledge-base' },
    { label: 'Back to Home', href: '/' },
  ];

  return (
    <PageWithSidebar title="Resources" sidebarItems={sidebarItems}>
      <div className="space-y-12">
        {/* Hero Section */}
        <div className="bg-blue-900 p-8 rounded-lg shadow-lg">
          <h1 className="text-3xl font-bold mb-4">NovaFuse Resources</h1>
          <p className="text-xl mb-6">
            Access our comprehensive library of resources to learn more about NovaFuse products, solutions, and partnership opportunities.
          </p>
        </div>

        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Documentation Repository */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4 flex items-center">
              <span className="bg-blue-600 text-white p-1 rounded mr-2 text-sm">NEW</span>
              Documentation Repository
            </h2>
            <p className="text-sm text-blue-100 mb-6">
              Access comprehensive documentation for the NovaFuse platform, including the 144-Slot Ecosystem Handbook,
              white papers, technical specifications, and legal documents.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">NovaFuse 144-Slot Ecosystem Handbook</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Elite Developer Domain Allocation Guide</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">From 1882 to 18/82: Partnership Revolution</span>
              </li>
            </ul>
            <Link href="/resources/documentation" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              Browse Documentation
            </Link>
          </div>

          {/* White Papers */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">White Papers</h2>
            <p className="text-sm text-blue-100 mb-6">
              Explore our thought leadership content on compliance, integration, and partner empowerment.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">The Universal API Connector Revolution</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">The Partner Empowerment Model</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">From Cybersecurity to Cyber-Safety</span>
              </li>
            </ul>
            <Link href="/resources/white-papers" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              View White Papers
            </Link>
          </div>

          {/* API Documentation */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">API Documentation</h2>
            <p className="text-sm text-blue-100 mb-6">
              Comprehensive documentation for our APIs, including endpoints, parameters, and example requests.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">NovaGRC Suite APIs</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Universal API Connector</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Integration Guides</span>
              </li>
            </ul>
            <Link href="/api-docs" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              View API Documentation
            </Link>
          </div>

          {/* UAC Demo */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">UAC Demo</h2>
            <p className="text-sm text-blue-100 mb-6">
              See the Universal API Connector in action with our interactive demo.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">100ms Data Normalization</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Bidirectional Control</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Cross-Domain Mapping</span>
              </li>
            </ul>
            <Link href="/uac-demo" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              View UAC Demo
            </Link>
          </div>

          {/* Partner Knowledge Base */}
          <div className="bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg shadow-md border border-blue-400">
            <h2 className="text-xl font-bold mb-4">Partner Knowledge Base</h2>
            <p className="text-sm text-blue-100 mb-6">
              Comprehensive resources for NovaFuse partners, including onboarding guides, marketing materials, and technical documentation.
            </p>
            <ul className="space-y-2 mb-6">
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Partner Program Overview</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Integration Support</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-white">•</span>
                <span className="text-white">Sales & Marketing Resources</span>
              </li>
            </ul>
            <Link href="/partner-knowledge-base" className="bg-white text-blue-700 px-4 py-2 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
              View Partner Knowledge Base
            </Link>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-8 rounded-lg shadow-lg border border-blue-400">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Need Additional Resources?</h2>
            <p className="mb-6">
              Contact us to request specific resources or to discuss how NovaFuse can help with your compliance needs.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/contact" className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Contact Us
              </Link>
              <Link href="/partner-empowerment" className="bg-white text-blue-700 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400">
                Partner With Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
};

export default Resources;
