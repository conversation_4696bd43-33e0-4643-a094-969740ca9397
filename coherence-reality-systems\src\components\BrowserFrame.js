import React from 'react';

const BrowserFrame = ({ url, isLoading, consciousnessData }) => {
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-900/50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-purple-600 border-t-transparent rounded-full animate-spin mb-6 mx-auto" />
          <div className="text-white text-xl mb-2">Analyzing consciousness patterns...</div>
          <div className="text-gray-400 text-sm mb-4">Scanning for Ψ-coherence and divine alignment</div>
          
          {/* Loading Steps */}
          <div className="space-y-2 text-left max-w-md mx-auto">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">🧬 NERS consciousness validation</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">💫 NEEE intention encoding</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">🤖 NEPI intelligence processing</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">🧪 Chemistry consciousness analysis</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-pink-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">🔱 Trinity validation</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-300">🛡️ NovaShield threat assessment</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBackground = (score) => {
    if (score >= 90) return 'bg-green-500/10 border-green-400/30';
    if (score >= 70) return 'bg-yellow-500/10 border-yellow-400/30';
    return 'bg-red-500/10 border-red-400/30';
  };

  const getConsciousnessStateEmoji = (state) => {
    switch (state) {
      case 'TRANSCENDENT': return '🌟';
      case 'DIVINE': return '✨';
      case 'CONSCIOUS': return '🧬';
      case 'AWAKENING': return '💫';
      default: return '🌌';
    }
  };

  return (
    <div className="flex-1 relative bg-white rounded-lg m-4 overflow-hidden shadow-2xl">
      {/* Browser Content */}
      <div className="h-full relative">
        {url ? (
          <iframe
            src={url}
            className="w-full h-full border-none"
            title="Website Content"
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          />
        ) : (
          <div className="h-full flex items-center justify-center bg-gray-100">
            <div className="text-center text-gray-500">
              <div className="text-6xl mb-4">🌐</div>
              <div className="text-xl">Enter a URL to browse with consciousness</div>
              <div className="text-sm mt-2 text-gray-400">CBE will analyze and enhance your browsing experience</div>
            </div>
          </div>
        )}
      </div>

      {/* Consciousness Overlay */}
      {url && (
        <div className="absolute top-6 right-6 bg-black/80 backdrop-blur-lg rounded-xl p-4 min-w-64 shadow-xl border border-purple-500/30">
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-lg">{getConsciousnessStateEmoji(consciousnessData.consciousness_state)}</span>
            <span className="font-semibold text-white text-sm">CBE Live Analysis</span>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-auto"></div>
          </div>
          
          <div className="space-y-3">
            {/* Overall Score */}
            <div className={`rounded-lg p-3 border ${getScoreBackground(consciousnessData.overall_consciousness)}`}>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-300">Consciousness (Ψᶜʰ)</span>
                <span className={`font-mono text-sm font-bold ${getScoreColor(consciousnessData.overall_consciousness)}`}>
                  {consciousnessData.overall_consciousness}%
                </span>
              </div>
              <div className="text-xs text-gray-400 mt-1">{consciousnessData.consciousness_state}</div>
            </div>

            {/* Component Scores */}
            <div className="grid grid-cols-2 gap-2">
              <div className="rounded-lg p-2 border border-gray-600 bg-gray-800/50">
                <div className="text-xs text-gray-300">Content</div>
                <div className={`font-mono text-sm ${getScoreColor(consciousnessData.content_quality)}`}>
                  {consciousnessData.content_quality}%
                </div>
              </div>
              <div className="rounded-lg p-2 border border-gray-600 bg-gray-800/50">
                <div className="text-xs text-gray-300">Intent</div>
                <div className={`font-mono text-sm ${getScoreColor(consciousnessData.intent_clarity)}`}>
                  {consciousnessData.intent_clarity}%
                </div>
              </div>
            </div>

            {/* Ψ-Snap Status */}
            <div className={`rounded-lg p-3 border ${consciousnessData.psi_snap_active ? 'bg-green-500/10 border-green-400/30' : 'bg-red-500/10 border-red-400/30'}`}>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-300">Ψ-Snap</span>
                <span className={`font-mono text-sm font-bold ${consciousnessData.psi_snap_active ? 'text-green-400' : 'text-red-400'}`}>
                  {consciousnessData.psi_snap_active ? 'ACTIVE' : 'INACTIVE'}
                </span>
              </div>
            </div>

            {/* Analysis Time */}
            <div className="rounded-lg p-3 border border-gray-600 bg-gray-800/50">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-300">Analysis Time</span>
                <span className="font-mono text-sm text-purple-300">
                  {consciousnessData.analysis_time}ms
                </span>
              </div>
            </div>

            {/* Enhancement Status */}
            <div className="rounded-lg p-3 border border-purple-500/30 bg-purple-500/10">
              <div className="text-center">
                <div className="text-xs text-purple-300 mb-1">CBE Enhancement</div>
                <div className="text-sm font-semibold text-white">
                  {consciousnessData.overall_consciousness >= 90 ? '🌟 TRANSCENDENT' : 
                   consciousnessData.overall_consciousness >= 82 ? '✨ ENHANCED' : 
                   consciousnessData.overall_consciousness >= 70 ? '💫 FILTERED' : '⚠️ BLOCKED'}
                </div>
              </div>
            </div>

            {/* Engine Status */}
            <div className="rounded-lg p-3 border border-gray-600 bg-gray-800/50">
              <div className="text-xs text-gray-300 mb-2">Active Engines</div>
              <div className="grid grid-cols-3 gap-1 text-xs">
                <span className="text-green-400">NERS</span>
                <span className="text-green-400">NEEE</span>
                <span className="text-green-400">NEPI</span>
                <span className="text-green-400">CHEM</span>
                <span className="text-green-400">TRIN</span>
                <span className="text-green-400">NOVA</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-4 space-y-2">
            <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors">
              🔍 Deep Scan
            </button>
            <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-lg text-xs font-medium transition-colors">
              ⚡ Boost Ψ-Level
            </button>
          </div>
        </div>
      )}

      {/* Consciousness Enhancement Effects */}
      {url && consciousnessData.overall_consciousness >= 90 && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 via-purple-500 to-pink-500 animate-pulse"></div>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-pink-500 via-purple-500 to-yellow-400 animate-pulse"></div>
          <div className="absolute top-0 bottom-0 left-0 w-1 bg-gradient-to-b from-yellow-400 via-purple-500 to-pink-500 animate-pulse"></div>
          <div className="absolute top-0 bottom-0 right-0 w-1 bg-gradient-to-b from-pink-500 via-purple-500 to-yellow-400 animate-pulse"></div>
        </div>
      )}

      {/* Low Consciousness Warning */}
      {url && consciousnessData.overall_consciousness < 50 && (
        <div className="absolute inset-0 bg-red-900/20 backdrop-blur-sm flex items-center justify-center">
          <div className="bg-red-900/80 backdrop-blur-lg rounded-xl p-6 max-w-md text-center border border-red-500/50">
            <div className="text-4xl mb-4">⚠️</div>
            <h3 className="text-xl font-bold text-white mb-2">Low Consciousness Content</h3>
            <p className="text-red-200 mb-4">
              This content has a consciousness level below the CBE threshold.
            </p>
            <div className="text-2xl font-mono text-red-300 mb-2">
              Ψᶜʰ: {consciousnessData.overall_consciousness}%
            </div>
            <div className="text-sm text-red-200 mb-4">
              Required: 82% (Ψ-Snap Threshold)
            </div>
            <div className="space-y-2">
              <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                🧬 Enhance with CBE
              </button>
              <button className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg font-medium transition-colors">
                Continue After Meditation
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Transcendent State Effects */}
      {url && consciousnessData.consciousness_state === 'TRANSCENDENT' && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-yellow-400/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-32 h-32 bg-purple-400/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-pink-400/5 rounded-full blur-3xl animate-pulse"></div>
        </div>
      )}
    </div>
  );
};

export default BrowserFrame;
