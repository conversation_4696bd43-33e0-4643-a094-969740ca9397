﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comphyology Diagram Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .diagram-container {
            margin-top: 20px;
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 8px;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0060df;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>3. Three-Body Problem Reframing</h1>
        
        <div class="diagram-container" id="diagram-container">
            <div style='text-align: center; padding: 50px;'><h2>Three-Body Problem Reframing</h2><p>This diagram compares classical and Comphyological approaches to the three-body problem.</p></div>
        </div>
        
        <div class="navigation">
            <button id="prev-button" onclick="prevDiagram()">Previous</button>
            <button id="next-button" onclick="nextDiagram()">Next</button>
        </div>
    </div>

    <script>
        function prevDiagram() {
            window.location.href = 'finite-universe.html';
        }
        
        function nextDiagram() {
            window.location.href = 'uuft-equation-flow.html';
        }
        
        // Disable buttons if needed
        document.addEventListener('DOMContentLoaded', function() {
            if ('finite-universe.html' === '#') {
                document.getElementById('prev-button').disabled = true;
            }
            if ('uuft-equation-flow.html' === '#') {
                document.getElementById('next-button').disabled = true;
            }
        });
    </script>
</body>
</html>
