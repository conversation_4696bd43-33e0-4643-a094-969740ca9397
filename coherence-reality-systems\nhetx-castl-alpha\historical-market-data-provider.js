/**
 * HISTORICAL MARKET DATA PROVIDER
 * 
 * Fetches and caches historical market data for ALPHA 90-day simulation
 * Supports multiple data sources: Yahoo Finance, Alpha Vantage, Polygon
 * 
 * Mission: Provide accurate historical data for backtesting ALPHA trading strategy
 */

const axios = require('axios');
const moment = require('moment');
const fs = require('fs').promises;
const path = require('path');

console.log('\n📊 HISTORICAL MARKET DATA PROVIDER INITIALIZING');
console.log('='.repeat(70));
console.log('🔄 Mission: Fetch 90-day historical data for ALPHA simulation');
console.log('📈 Sources: Yahoo Finance, Alpha Vantage, Polygon');
console.log('💾 Caching: Enabled for performance optimization');
console.log('='.repeat(70));

// DATA PROVIDER CONFIGURATION
const DATA_CONFIG = {
  // Data Sources
  yahoo_finance_enabled: process.env.YAHOO_FINANCE_ENABLED === 'true',
  alpha_vantage_enabled: process.env.ALPHA_VANTAGE_ENABLED === 'true',
  polygon_enabled: process.env.POLYGON_ENABLED === 'true',
  
  // API Keys (set via environment variables)
  alpha_vantage_api_key: process.env.ALPHA_VANTAGE_API_KEY || 'demo',
  polygon_api_key: process.env.POLYGON_API_KEY || 'demo',
  
  // Data Parameters
  symbols: (process.env.SYMBOLS || 'SPY,QQQ,AAPL,MSFT,GOOGL,TSLA,NVDA,EURUSD,GBPUSD,USDJPY').split(','),
  start_date: process.env.SIMULATION_START_DATE || '2024-01-01',
  end_date: process.env.SIMULATION_END_DATE || '2024-03-31',
  
  // Cache Settings
  cache_enabled: process.env.DATA_CACHE_ENABLED === 'true',
  cache_directory: '/app/cache',
  cache_expiry_hours: 24,
  
  // Rate Limiting
  request_delay_ms: 200, // Delay between API requests
  max_retries: 3
};

// HISTORICAL MARKET DATA PROVIDER
class HistoricalMarketDataProvider {
  constructor() {
    this.name = 'Historical Market Data Provider';
    this.version = '1.0.0-SIMULATION';
    
    // Data cache
    this.data_cache = new Map();
    this.cache_timestamps = new Map();
    
    // Request tracking
    this.request_count = 0;
    this.failed_requests = 0;
    this.cached_responses = 0;
    
    console.log(`📊 ${this.name} v${this.version} initialized`);
    console.log(`📈 Symbols to fetch: ${DATA_CONFIG.symbols.length}`);
    console.log(`📅 Date range: ${DATA_CONFIG.start_date} to ${DATA_CONFIG.end_date}`);
  }

  // FETCH ALL HISTORICAL DATA
  async fetchAllHistoricalData() {
    console.log('\n🔄 FETCHING ALL HISTORICAL DATA');
    console.log('='.repeat(50));
    
    const results = {
      successful: [],
      failed: [],
      cached: [],
      total_symbols: DATA_CONFIG.symbols.length
    };
    
    try {
      // Create cache directory if it doesn't exist
      if (DATA_CONFIG.cache_enabled) {
        await this.ensureCacheDirectory();
      }
      
      // Fetch data for each symbol
      for (const symbol of DATA_CONFIG.symbols) {
        console.log(`\n📈 Fetching data for ${symbol}...`);
        
        try {
          const data = await this.fetchSymbolData(symbol);
          
          if (data && data.length > 0) {
            results.successful.push(symbol);
            console.log(`   ✅ ${symbol}: ${data.length} data points fetched`);
          } else {
            results.failed.push(symbol);
            console.log(`   ❌ ${symbol}: No data received`);
          }
          
          // Rate limiting delay
          await this.delay(DATA_CONFIG.request_delay_ms);
          
        } catch (error) {
          results.failed.push(symbol);
          console.error(`   ❌ ${symbol}: ${error.message}`);
        }
      }
      
      // Generate summary report
      this.generateDataFetchReport(results);
      
      return results;
      
    } catch (error) {
      console.error('\n❌ DATA FETCH ERROR:', error.message);
      return { success: false, error: error.message };
    }
  }

  // FETCH SYMBOL DATA
  async fetchSymbolData(symbol) {
    // Check cache first
    if (DATA_CONFIG.cache_enabled) {
      const cached_data = await this.getCachedData(symbol);
      if (cached_data) {
        this.cached_responses++;
        return cached_data;
      }
    }
    
    let data = null;
    
    // Try different data sources in order of preference
    if (DATA_CONFIG.yahoo_finance_enabled && !data) {
      data = await this.fetchFromYahooFinance(symbol);
    }
    
    if (DATA_CONFIG.alpha_vantage_enabled && !data) {
      data = await this.fetchFromAlphaVantage(symbol);
    }
    
    if (DATA_CONFIG.polygon_enabled && !data) {
      data = await this.fetchFromPolygon(symbol);
    }
    
    // If no data from APIs, generate simulated data
    if (!data) {
      console.log(`   🔄 Generating simulated data for ${symbol}...`);
      data = this.generateSimulatedData(symbol);
    }
    
    // Cache the data
    if (data && DATA_CONFIG.cache_enabled) {
      await this.cacheData(symbol, data);
    }
    
    this.request_count++;
    return data;
  }

  // FETCH FROM YAHOO FINANCE
  async fetchFromYahooFinance(symbol) {
    try {
      console.log(`   🌐 Fetching ${symbol} from Yahoo Finance...`);
      
      // Convert forex symbols to Yahoo Finance format
      const yahoo_symbol = this.convertToYahooSymbol(symbol);
      
      // Calculate timestamps
      const start_timestamp = moment(DATA_CONFIG.start_date).unix();
      const end_timestamp = moment(DATA_CONFIG.end_date).unix();
      
      // Yahoo Finance API endpoint
      const url = `https://query1.finance.yahoo.com/v7/finance/download/${yahoo_symbol}` +
                  `?period1=${start_timestamp}&period2=${end_timestamp}&interval=1d&events=history`;
      
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      if (response.status === 200 && response.data) {
        return this.parseYahooFinanceData(response.data);
      }
      
      return null;
      
    } catch (error) {
      console.log(`   ⚠️  Yahoo Finance failed for ${symbol}: ${error.message}`);
      return null;
    }
  }

  // FETCH FROM ALPHA VANTAGE
  async fetchFromAlphaVantage(symbol) {
    try {
      console.log(`   🌐 Fetching ${symbol} from Alpha Vantage...`);
      
      if (DATA_CONFIG.alpha_vantage_api_key === 'demo') {
        console.log(`   ⚠️  Alpha Vantage API key not configured`);
        return null;
      }
      
      // Determine function based on symbol type
      const is_forex = symbol.includes('USD') || symbol.length === 6;
      const function_name = is_forex ? 'FX_DAILY' : 'TIME_SERIES_DAILY';
      
      let url;
      if (is_forex) {
        const from_symbol = symbol.substring(0, 3);
        const to_symbol = symbol.substring(3, 6);
        url = `https://www.alphavantage.co/query?function=${function_name}&from_symbol=${from_symbol}&to_symbol=${to_symbol}&apikey=${DATA_CONFIG.alpha_vantage_api_key}`;
      } else {
        url = `https://www.alphavantage.co/query?function=${function_name}&symbol=${symbol}&apikey=${DATA_CONFIG.alpha_vantage_api_key}`;
      }
      
      const response = await axios.get(url, { timeout: 10000 });
      
      if (response.status === 200 && response.data) {
        return this.parseAlphaVantageData(response.data, is_forex);
      }
      
      return null;
      
    } catch (error) {
      console.log(`   ⚠️  Alpha Vantage failed for ${symbol}: ${error.message}`);
      return null;
    }
  }

  // FETCH FROM POLYGON
  async fetchFromPolygon(symbol) {
    try {
      console.log(`   🌐 Fetching ${symbol} from Polygon...`);
      
      if (DATA_CONFIG.polygon_api_key === 'demo') {
        console.log(`   ⚠️  Polygon API key not configured`);
        return null;
      }
      
      // Polygon API endpoint
      const url = `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/1/day/${DATA_CONFIG.start_date}/${DATA_CONFIG.end_date}?apikey=${DATA_CONFIG.polygon_api_key}`;
      
      const response = await axios.get(url, { timeout: 10000 });
      
      if (response.status === 200 && response.data && response.data.results) {
        return this.parsePolygonData(response.data.results);
      }
      
      return null;
      
    } catch (error) {
      console.log(`   ⚠️  Polygon failed for ${symbol}: ${error.message}`);
      return null;
    }
  }

  // GENERATE SIMULATED DATA
  generateSimulatedData(symbol) {
    console.log(`   🎲 Generating simulated data for ${symbol}...`);
    
    const data = [];
    const start_date = moment(DATA_CONFIG.start_date);
    const end_date = moment(DATA_CONFIG.end_date);
    
    // Base price based on symbol
    let base_price = 100;
    if (symbol === 'SPY') base_price = 450;
    else if (symbol === 'QQQ') base_price = 380;
    else if (symbol === 'AAPL') base_price = 180;
    else if (symbol === 'MSFT') base_price = 350;
    else if (symbol === 'GOOGL') base_price = 140;
    else if (symbol === 'TSLA') base_price = 200;
    else if (symbol === 'NVDA') base_price = 500;
    else if (symbol.includes('USD')) base_price = 1.1; // Forex rates
    
    let current_price = base_price;
    let current_date = start_date.clone();
    
    while (current_date.isSameOrBefore(end_date)) {
      // Skip weekends for stock data
      if (current_date.day() !== 0 && current_date.day() !== 6) {
        // Generate realistic price movement
        const volatility = symbol.includes('USD') ? 0.01 : 0.02; // Lower volatility for forex
        const drift = 0.0003; // Small upward drift
        const random_change = (Math.random() - 0.5) * volatility * 2;
        
        current_price = current_price * (1 + drift + random_change);
        
        const open = current_price * (1 + (Math.random() - 0.5) * 0.005);
        const high = Math.max(open, current_price) * (1 + Math.random() * 0.01);
        const low = Math.min(open, current_price) * (1 - Math.random() * 0.01);
        const close = current_price;
        const volume = Math.floor(1000000 + Math.random() * 5000000);
        
        data.push({
          date: current_date.format('YYYY-MM-DD'),
          open: parseFloat(open.toFixed(symbol.includes('USD') ? 5 : 2)),
          high: parseFloat(high.toFixed(symbol.includes('USD') ? 5 : 2)),
          low: parseFloat(low.toFixed(symbol.includes('USD') ? 5 : 2)),
          close: parseFloat(close.toFixed(symbol.includes('USD') ? 5 : 2)),
          volume: volume
        });
      }
      
      current_date.add(1, 'day');
    }
    
    return data;
  }

  // CONVERT TO YAHOO SYMBOL
  convertToYahooSymbol(symbol) {
    // Convert forex symbols to Yahoo Finance format
    if (symbol === 'EURUSD') return 'EURUSD=X';
    if (symbol === 'GBPUSD') return 'GBPUSD=X';
    if (symbol === 'USDJPY') return 'USDJPY=X';
    
    return symbol;
  }

  // PARSE YAHOO FINANCE DATA
  parseYahooFinanceData(csv_data) {
    const lines = csv_data.trim().split('\n');
    const data = [];
    
    // Skip header line
    for (let i = 1; i < lines.length; i++) {
      const columns = lines[i].split(',');
      
      if (columns.length >= 6) {
        data.push({
          date: columns[0],
          open: parseFloat(columns[1]),
          high: parseFloat(columns[2]),
          low: parseFloat(columns[3]),
          close: parseFloat(columns[4]),
          volume: parseInt(columns[6]) || 0
        });
      }
    }
    
    return data;
  }

  // PARSE ALPHA VANTAGE DATA
  parseAlphaVantageData(json_data, is_forex) {
    const data = [];
    const time_series_key = is_forex ? 'Time Series FX (Daily)' : 'Time Series (Daily)';
    const time_series = json_data[time_series_key];
    
    if (!time_series) return data;
    
    for (const [date, values] of Object.entries(time_series)) {
      const open_key = is_forex ? '1. open' : '1. open';
      const high_key = is_forex ? '2. high' : '2. high';
      const low_key = is_forex ? '3. low' : '3. low';
      const close_key = is_forex ? '4. close' : '4. close';
      const volume_key = is_forex ? null : '5. volume';
      
      data.push({
        date: date,
        open: parseFloat(values[open_key]),
        high: parseFloat(values[high_key]),
        low: parseFloat(values[low_key]),
        close: parseFloat(values[close_key]),
        volume: volume_key ? parseInt(values[volume_key]) : 0
      });
    }
    
    // Sort by date
    return data.sort((a, b) => new Date(a.date) - new Date(b.date));
  }

  // PARSE POLYGON DATA
  parsePolygonData(results) {
    const data = [];
    
    for (const result of results) {
      data.push({
        date: moment(result.t).format('YYYY-MM-DD'),
        open: result.o,
        high: result.h,
        low: result.l,
        close: result.c,
        volume: result.v
      });
    }
    
    return data;
  }

  // ENSURE CACHE DIRECTORY
  async ensureCacheDirectory() {
    try {
      await fs.mkdir(DATA_CONFIG.cache_directory, { recursive: true });
    } catch (error) {
      console.log(`   ⚠️  Cache directory creation failed: ${error.message}`);
    }
  }

  // GET CACHED DATA
  async getCachedData(symbol) {
    try {
      const cache_file = path.join(DATA_CONFIG.cache_directory, `${symbol}.json`);
      const cache_timestamp_file = path.join(DATA_CONFIG.cache_directory, `${symbol}.timestamp`);

      // Check if cache files exist
      const cache_exists = await fs.access(cache_file).then(() => true).catch(() => false);
      const timestamp_exists = await fs.access(cache_timestamp_file).then(() => true).catch(() => false);

      if (!cache_exists || !timestamp_exists) {
        return null;
      }

      // Check cache expiry
      const timestamp_data = await fs.readFile(cache_timestamp_file, 'utf8');
      const cache_time = moment(timestamp_data);
      const now = moment();

      if (now.diff(cache_time, 'hours') > DATA_CONFIG.cache_expiry_hours) {
        console.log(`   🕒 Cache expired for ${symbol}`);
        return null;
      }

      // Load cached data
      const cached_data = await fs.readFile(cache_file, 'utf8');
      console.log(`   💾 Using cached data for ${symbol}`);
      return JSON.parse(cached_data);

    } catch (error) {
      console.log(`   ⚠️  Cache read failed for ${symbol}: ${error.message}`);
      return null;
    }
  }

  // CACHE DATA
  async cacheData(symbol, data) {
    try {
      const cache_file = path.join(DATA_CONFIG.cache_directory, `${symbol}.json`);
      const cache_timestamp_file = path.join(DATA_CONFIG.cache_directory, `${symbol}.timestamp`);

      await fs.writeFile(cache_file, JSON.stringify(data, null, 2));
      await fs.writeFile(cache_timestamp_file, moment().toISOString());

      console.log(`   💾 Data cached for ${symbol}`);

    } catch (error) {
      console.log(`   ⚠️  Cache write failed for ${symbol}: ${error.message}`);
    }
  }

  // GENERATE DATA FETCH REPORT
  generateDataFetchReport(results) {
    console.log('\n📋 DATA FETCH REPORT');
    console.log('='.repeat(50));

    console.log(`📊 SUMMARY:`);
    console.log(`   ✅ Successful: ${results.successful.length}/${results.total_symbols}`);
    console.log(`   ❌ Failed: ${results.failed.length}/${results.total_symbols}`);
    console.log(`   💾 Cached: ${this.cached_responses}`);
    console.log(`   🌐 API Requests: ${this.request_count}`);

    if (results.successful.length > 0) {
      console.log(`\n✅ SUCCESSFUL SYMBOLS:`);
      results.successful.forEach(symbol => console.log(`   📈 ${symbol}`));
    }

    if (results.failed.length > 0) {
      console.log(`\n❌ FAILED SYMBOLS:`);
      results.failed.forEach(symbol => console.log(`   📉 ${symbol}`));
    }

    const success_rate = (results.successful.length / results.total_symbols) * 100;
    console.log(`\n📊 SUCCESS RATE: ${success_rate.toFixed(1)}%`);

    if (success_rate >= 80) {
      console.log('🌟 DATA FETCH COMPLETED SUCCESSFULLY!');
    } else {
      console.log('⚠️  DATA FETCH PARTIALLY SUCCESSFUL - Some symbols failed');
    }

    console.log('='.repeat(50));
  }

  // DELAY UTILITY
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // GET DATA FOR SYMBOL
  async getDataForSymbol(symbol) {
    if (this.data_cache.has(symbol)) {
      return this.data_cache.get(symbol);
    }

    const data = await this.fetchSymbolData(symbol);
    if (data) {
      this.data_cache.set(symbol, data);
    }

    return data;
  }

  // VALIDATE DATA QUALITY
  validateDataQuality(symbol, data) {
    if (!data || data.length === 0) {
      return { valid: false, reason: 'No data' };
    }

    // Check for minimum data points
    const expected_days = moment(DATA_CONFIG.end_date).diff(moment(DATA_CONFIG.start_date), 'days');
    const min_expected_points = Math.floor(expected_days * 0.7); // Allow for weekends/holidays

    if (data.length < min_expected_points) {
      return {
        valid: false,
        reason: `Insufficient data points: ${data.length} < ${min_expected_points}`
      };
    }

    // Check for data integrity
    let valid_points = 0;
    for (const point of data) {
      if (point.open > 0 && point.high > 0 && point.low > 0 && point.close > 0 &&
          point.high >= point.low && point.high >= point.open && point.high >= point.close &&
          point.low <= point.open && point.low <= point.close) {
        valid_points++;
      }
    }

    const data_quality = valid_points / data.length;
    if (data_quality < 0.95) {
      return {
        valid: false,
        reason: `Poor data quality: ${(data_quality * 100).toFixed(1)}% valid points`
      };
    }

    return { valid: true, quality: data_quality };
  }

  // GET AVAILABLE SYMBOLS
  getAvailableSymbols() {
    return Array.from(this.data_cache.keys());
  }

  // GET DATA STATISTICS
  getDataStatistics() {
    const stats = {
      total_symbols: this.data_cache.size,
      total_data_points: 0,
      date_range: {
        start: null,
        end: null
      },
      symbols: []
    };

    for (const [symbol, data] of this.data_cache) {
      if (data && data.length > 0) {
        stats.total_data_points += data.length;

        const symbol_stats = {
          symbol: symbol,
          data_points: data.length,
          start_date: data[0].date,
          end_date: data[data.length - 1].date,
          price_range: {
            min: Math.min(...data.map(d => d.low)),
            max: Math.max(...data.map(d => d.high))
          }
        };

        stats.symbols.push(symbol_stats);

        // Update overall date range
        if (!stats.date_range.start || symbol_stats.start_date < stats.date_range.start) {
          stats.date_range.start = symbol_stats.start_date;
        }
        if (!stats.date_range.end || symbol_stats.end_date > stats.date_range.end) {
          stats.date_range.end = symbol_stats.end_date;
        }
      }
    }

    return stats;
  }
}

// Export for use
module.exports = {
  HistoricalMarketDataProvider,
  DATA_CONFIG
};

// Execute data fetch if run directly
if (require.main === module) {
  const provider = new HistoricalMarketDataProvider();
  provider.fetchAllHistoricalData();
}
