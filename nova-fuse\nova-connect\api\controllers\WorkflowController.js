/**
 * Workflow Controller
 * 
 * This controller handles API requests related to workflows.
 */

const WorkflowService = require('../services/WorkflowService');
const { ValidationError } = require('../utils/errors');

class WorkflowController {
  constructor() {
    this.workflowService = new WorkflowService();
  }

  /**
   * Get all workflows
   */
  async getAllWorkflows(req, res, next) {
    try {
      const filters = req.query;
      const workflows = await this.workflowService.getAllWorkflows(filters);
      res.json(workflows);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my workflows
   */
  async getMyWorkflows(req, res, next) {
    try {
      const filters = req.query;
      const workflows = await this.workflowService.getWorkflowsForUser(req.user.id, filters);
      res.json(workflows);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get workflow by ID
   */
  async getWorkflowById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      const workflow = await this.workflowService.getWorkflowById(id);
      res.json(workflow);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new workflow
   */
  async createWorkflow(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Workflow data is required');
      }
      
      const workflow = await this.workflowService.createWorkflow(data, req.user.id);
      res.status(201).json(workflow);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a workflow
   */
  async updateWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Workflow data is required');
      }
      
      const workflow = await this.workflowService.updateWorkflow(id, data, req.user.id);
      res.json(workflow);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a workflow
   */
  async deleteWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      const result = await this.workflowService.deleteWorkflow(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Enable a workflow
   */
  async enableWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      const workflow = await this.workflowService.enableWorkflow(id, req.user.id);
      res.json(workflow);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Disable a workflow
   */
  async disableWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      const workflow = await this.workflowService.disableWorkflow(id, req.user.id);
      res.json(workflow);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      const inputs = req.body || {};
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      const run = await this.workflowService.executeWorkflow(id, inputs, req.user.id);
      res.status(202).json(run);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all workflow runs
   */
  async getAllWorkflowRuns(req, res, next) {
    try {
      const filters = req.query;
      const runs = await this.workflowService.getAllWorkflowRuns(filters);
      res.json(runs);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get workflow runs for a workflow
   */
  async getWorkflowRunsForWorkflow(req, res, next) {
    try {
      const { id } = req.params;
      const filters = req.query;
      
      if (!id) {
        throw new ValidationError('Workflow ID is required');
      }
      
      const runs = await this.workflowService.getWorkflowRunsForWorkflow(id, filters);
      res.json(runs);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get workflow run by ID
   */
  async getWorkflowRunById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Workflow run ID is required');
      }
      
      const run = await this.workflowService.getWorkflowRunById(id);
      res.json(run);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Process scheduled workflows
   */
  async processScheduledWorkflows(req, res, next) {
    try {
      const result = await this.workflowService.processScheduledWorkflows();
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Trigger event workflows
   */
  async triggerEventWorkflows(req, res, next) {
    try {
      const { eventType } = req.params;
      const eventData = req.body || {};
      
      if (!eventType) {
        throw new ValidationError('Event type is required');
      }
      
      const result = await this.workflowService.triggerEventWorkflows(eventType, eventData);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new WorkflowController();
