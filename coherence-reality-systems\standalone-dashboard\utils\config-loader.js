import dotenv from 'dotenv';

dotenv.config();

export const loadConfig = () => {
  const config = {
    platform: process.env.PLATFORM || 'sharesale',
    publicOnly: process.env.PUBLIC_ONLY === 'true',
    credentials: {
      apiKey: process.env.SHAREASALE_API_KEY || null,
      affiliateId: process.env.SHAREASALE_AFFILIATE_ID || null
    }
  };

  // If publicOnly is true, don't use credentials
  if (config.publicOnly) {
    config.credentials = null;
  }

  return config;
};
