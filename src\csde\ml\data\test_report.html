
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE-Enhanced ML Test Results</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    .chart-container {
      width: 80%;
      margin: 20px auto;
      height: 400px;
    }
    h1, h2 {
      text-align: center;
    }
    .metrics {
      width: 80%;
      margin: 20px auto;
      border-collapse: collapse;
    }
    .metrics th, .metrics td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .metrics th {
      background-color: #f2f2f2;
    }
    .comparison {
      display: flex;
      justify-content: space-around;
      margin: 20px 0;
    }
    .comparison-item {
      text-align: center;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      width: 30%;
    }
    .comparison-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .improvement {
      color: green;
    }
  </style>
</head>
<body>
  <h1>CSDE-Enhanced ML Test Results</h1>

  <div class="comparison">
    <div class="comparison-item">
      <h3>Original ML Accuracy</h3>
      <div class="comparison-value">6.00%</div>
    </div>
    <div class="comparison-item">
      <h3>CSDE-Enhanced ML Accuracy</h3>
      <div class="comparison-value">0.00%</div>
      <div class="improvement">
        No improvement
      </div>
    </div>
  </div>

  <div class="comparison">
    <div class="comparison-item">
      <h3>Original ML Error</h3>
      <div class="comparison-value">221.55%</div>
    </div>
    <div class="comparison-item">
      <h3>CSDE-Enhanced ML Error</h3>
      <div class="comparison-value">NaN%</div>
      <div class="improvement">
        No improvement
      </div>
    </div>
  </div>

  <h2>Test Metrics</h2>
  <table class="metrics">
    <tr>
      <th>Metric</th>
      <th>Value</th>
    </tr>
    <tr>
      <td>Accuracy</td>
      <td>0.00%</td>
    </tr>
    <tr>
      <td>Average Error</td>
      <td>NaN%</td>
    </tr>
    <tr>
      <td>Max Error</td>
      <td>NaN%</td>
    </tr>
    <tr>
      <td>Min Error</td>
      <td>NaN%</td>
    </tr>
    <tr>
      <td>Test Samples</td>
      <td>10</td>
    </tr>
  </table>

  <div class="chart-container">
    <canvas id="accuracyChart"></canvas>
  </div>

  <div class="chart-container">
    <canvas id="errorDistributionChart"></canvas>
  </div>

  <script>
    // Accuracy chart
    const accuracyCtx = document.getElementById('accuracyChart').getContext('2d');
    new Chart(accuracyCtx, {
      type: 'line',
      data: {
        labels: [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100],
        datasets: [
          {
            label: 'Training Accuracy',
            data: [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1,
            fill: false
          },
          {
            label: 'Validation Accuracy',
            data: [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
            borderColor: 'rgb(255, 99, 132)',
            tension: 0.1,
            fill: false
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Accuracy (%)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Epoch'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Training and Validation Accuracy'
          }
        }
      }
    });

    // Error distribution chart
    const errors = [null,null,null,null,null,null,null,null,null,null];
    const errorBins = [0, 1, 2, 5, 10, 20, 50, 100];
    const errorCounts = Array(errorBins.length).fill(0);

    errors.forEach(error => {
      for (let i = 0; i < errorBins.length; i++) {
        if (error <= errorBins[i] || i === errorBins.length - 1) {
          errorCounts[i]++;
          break;
        }
      }
    });

    const errorCtx = document.getElementById('errorDistributionChart').getContext('2d');
    new Chart(errorCtx, {
      type: 'bar',
      data: {
        labels: errorBins.map((bin, index) => {
          if (index === 0) return '0%';
          const prevBin = errorBins[index - 1];
          return `${prevBin}% - ${bin}%`;
        }),
        datasets: [{
          label: 'Number of Predictions',
          data: errorCounts,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Predictions'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Error Range'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Distribution of Prediction Errors'
          }
        }
      }
    });
  </script>
</body>
</html>
