"""
Analytics Manager for the Universal Compliance Tracking Optimizer.

This module provides functionality for analyzing compliance data.
"""

import logging
from typing import Dict, List, Any, Optional, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AnalyticsManager:
    """
    Manager for compliance analytics.
    
    This class is responsible for analyzing compliance data to provide insights
    and trends.
    """
    
    def __init__(self):
        """Initialize the Analytics Manager."""
        logger.info("Initializing Analytics Manager")
        
        # Dictionary to store analytics functions
        self.analytics_functions: Dict[str, Callable] = {}
        
        # Register default analytics functions
        self._register_default_analytics_functions()
        
        logger.info(f"Analytics Manager initialized with {len(self.analytics_functions)} functions")
    
    def _register_default_analytics_functions(self) -> None:
        """Register default analytics functions."""
        # Completion trend analysis
        self.register_analytics_function('completion_trend', self._analyze_completion_trend)
        
        # Framework comparison analysis
        self.register_analytics_function('framework_comparison', self._analyze_framework_comparison)
        
        # Resource utilization analysis
        self.register_analytics_function('resource_utilization', self._analyze_resource_utilization)
        
        # Risk analysis
        self.register_analytics_function('risk', self._analyze_risk)
        
        # Efficiency analysis
        self.register_analytics_function('efficiency', self._analyze_efficiency)
    
    def register_analytics_function(self, function_id: str, analytics_func: Callable) -> None:
        """
        Register an analytics function.
        
        Args:
            function_id: The ID of the function
            analytics_func: The analytics function
        """
        self.analytics_functions[function_id] = analytics_func
        logger.info(f"Registered analytics function: {function_id}")
    
    def analyze(self, 
               function_id: str, 
               requirements: List[Dict[str, Any]], 
               activities: List[Dict[str, Any]], 
               parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply an analytics function.
        
        Args:
            function_id: The ID of the function to apply
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Optional parameters for the analysis
            
        Returns:
            The analysis result
            
        Raises:
            ValueError: If the function does not exist
        """
        logger.info(f"Applying analytics function: {function_id}")
        
        # Check if the function exists
        if function_id not in self.analytics_functions:
            raise ValueError(f"Analytics function not found: {function_id}")
        
        # Get the analytics function
        analytics_func = self.analytics_functions[function_id]
        
        # Apply the function
        result = analytics_func(requirements, activities, parameters or {})
        
        logger.info(f"Analytics function applied: {function_id}")
        
        return result
    
    def analyze_all(self, 
                   requirements: List[Dict[str, Any]], 
                   activities: List[Dict[str, Any]], 
                   parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply all analytics functions.
        
        Args:
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Optional parameters for the analysis
            
        Returns:
            The analysis result
        """
        logger.info("Applying all analytics functions")
        
        # Initialize the result
        result = {
            'functions_applied': [],
            'analyses': {}
        }
        
        # Apply each function
        for function_id in self.analytics_functions:
            try:
                # Apply the function
                function_result = self.analyze(function_id, requirements, activities, parameters)
                
                # Add the function to the list of applied functions
                result['functions_applied'].append(function_id)
                
                # Add the function result to the analyses
                result['analyses'][function_id] = function_result
            
            except Exception as e:
                logger.error(f"Failed to apply analytics function {function_id}: {e}")
        
        logger.info(f"Applied {len(result['functions_applied'])} analytics functions")
        
        return result
    
    def get_analytics_functions(self) -> List[str]:
        """
        Get all analytics functions.
        
        Returns:
            List of analytics function IDs
        """
        return list(self.analytics_functions.keys())
    
    # Default analytics functions
    
    def _analyze_completion_trend(self, 
                                 requirements: List[Dict[str, Any]], 
                                 activities: List[Dict[str, Any]], 
                                 parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze completion trend.
        
        Args:
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Parameters for the analysis
            
        Returns:
            The analysis result
        """
        logger.info("Analyzing completion trend")
        
        # In a real implementation, this would analyze historical data
        # For now, return a placeholder result
        
        # Group activities by month
        activities_by_month = {}
        for activity in activities:
            if activity.get('status') == 'completed' and activity.get('end_date'):
                # Extract month from date (assuming ISO format)
                end_date = activity.get('end_date', '')
                if end_date:
                    month = end_date[:7]  # YYYY-MM
                    if month not in activities_by_month:
                        activities_by_month[month] = []
                    activities_by_month[month].append(activity)
        
        # Calculate completion counts by month
        completion_counts = {}
        for month, month_activities in activities_by_month.items():
            completion_counts[month] = len(month_activities)
        
        # Sort months
        sorted_months = sorted(completion_counts.keys())
        
        # Create the analysis result
        result = {
            'function': 'completion_trend',
            'total_activities': len(activities),
            'completed_activities': len([a for a in activities if a.get('status') == 'completed']),
            'months': sorted_months,
            'completion_counts': [completion_counts.get(month, 0) for month in sorted_months],
            'trend': 'increasing' if sorted_months and completion_counts.get(sorted_months[-1], 0) > completion_counts.get(sorted_months[0], 0) else 'decreasing'
        }
        
        logger.info("Completion trend analysis completed")
        
        return result
    
    def _analyze_framework_comparison(self, 
                                     requirements: List[Dict[str, Any]], 
                                     activities: List[Dict[str, Any]], 
                                     parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze framework comparison.
        
        Args:
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Parameters for the analysis
            
        Returns:
            The analysis result
        """
        logger.info("Analyzing framework comparison")
        
        # In a real implementation, this would perform a more sophisticated analysis
        # For now, return a placeholder result
        
        # Get all frameworks
        frameworks = set()
        for requirement in requirements:
            framework = requirement.get('framework')
            if framework:
                frameworks.add(framework)
        
        # Calculate completion percentage by framework
        framework_completion = {}
        for framework in frameworks:
            framework_requirements = [r for r in requirements if r.get('framework') == framework]
            completed_requirements = [r for r in framework_requirements if r.get('status') == 'completed']
            
            total = len(framework_requirements)
            completed = len(completed_requirements)
            
            completion_percentage = (completed / total * 100) if total > 0 else 0
            
            framework_completion[framework] = {
                'total': total,
                'completed': completed,
                'completion_percentage': completion_percentage
            }
        
        # Create the analysis result
        result = {
            'function': 'framework_comparison',
            'total_frameworks': len(frameworks),
            'frameworks': list(frameworks),
            'framework_completion': framework_completion
        }
        
        logger.info("Framework comparison analysis completed")
        
        return result
    
    def _analyze_resource_utilization(self, 
                                     requirements: List[Dict[str, Any]], 
                                     activities: List[Dict[str, Any]], 
                                     parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze resource utilization.
        
        Args:
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Parameters for the analysis
            
        Returns:
            The analysis result
        """
        logger.info("Analyzing resource utilization")
        
        # In a real implementation, this would perform a more sophisticated analysis
        # For now, return a placeholder result
        
        # Get all assigned people
        assigned_people = set()
        for activity in activities:
            assigned_to = activity.get('assigned_to')
            if assigned_to:
                assigned_people.add(assigned_to)
        
        # Calculate activities per person
        activities_per_person = {}
        for person in assigned_people:
            person_activities = [a for a in activities if a.get('assigned_to') == person]
            completed_activities = [a for a in person_activities if a.get('status') == 'completed']
            
            activities_per_person[person] = {
                'total': len(person_activities),
                'completed': len(completed_activities),
                'completion_percentage': (len(completed_activities) / len(person_activities) * 100) if person_activities else 0
            }
        
        # Calculate average activities per person
        total_activities = sum(data['total'] for data in activities_per_person.values())
        avg_activities = total_activities / len(assigned_people) if assigned_people else 0
        
        # Create the analysis result
        result = {
            'function': 'resource_utilization',
            'total_people': len(assigned_people),
            'total_activities': total_activities,
            'average_activities_per_person': avg_activities,
            'activities_per_person': activities_per_person
        }
        
        logger.info("Resource utilization analysis completed")
        
        return result
    
    def _analyze_risk(self, 
                     requirements: List[Dict[str, Any]], 
                     activities: List[Dict[str, Any]], 
                     parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze risk.
        
        Args:
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Parameters for the analysis
            
        Returns:
            The analysis result
        """
        logger.info("Analyzing risk")
        
        # In a real implementation, this would perform a more sophisticated analysis
        # For now, return a placeholder result
        
        # Count high priority requirements
        high_priority_requirements = [r for r in requirements if r.get('priority') == 'high']
        
        # Count overdue activities
        import datetime
        today = datetime.datetime.now().isoformat()[:10]  # YYYY-MM-DD
        overdue_activities = [a for a in activities if a.get('end_date') and a.get('end_date') < today and a.get('status') != 'completed']
        
        # Count requirements without activities
        requirements_without_activities = []
        for requirement in requirements:
            requirement_id = requirement.get('id')
            if requirement_id:
                requirement_activities = [a for a in activities if a.get('requirement_id') == requirement_id]
                if not requirement_activities:
                    requirements_without_activities.append(requirement)
        
        # Calculate risk score (simple example)
        risk_score = (
            len(high_priority_requirements) * 2 +
            len(overdue_activities) * 3 +
            len(requirements_without_activities)
        )
        
        # Normalize risk score to 0-100 scale
        max_possible_score = len(requirements) * 2 + len(activities) * 3 + len(requirements)
        normalized_risk_score = (risk_score / max_possible_score * 100) if max_possible_score > 0 else 0
        
        # Determine risk level
        if normalized_risk_score >= 70:
            risk_level = 'high'
        elif normalized_risk_score >= 30:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        # Create the analysis result
        result = {
            'function': 'risk',
            'high_priority_requirements': len(high_priority_requirements),
            'overdue_activities': len(overdue_activities),
            'requirements_without_activities': len(requirements_without_activities),
            'risk_score': normalized_risk_score,
            'risk_level': risk_level,
            'risk_factors': [
                {
                    'factor': 'high_priority_requirements',
                    'count': len(high_priority_requirements),
                    'weight': 2
                },
                {
                    'factor': 'overdue_activities',
                    'count': len(overdue_activities),
                    'weight': 3
                },
                {
                    'factor': 'requirements_without_activities',
                    'count': len(requirements_without_activities),
                    'weight': 1
                }
            ]
        }
        
        logger.info("Risk analysis completed")
        
        return result
    
    def _analyze_efficiency(self, 
                           requirements: List[Dict[str, Any]], 
                           activities: List[Dict[str, Any]], 
                           parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze efficiency.
        
        Args:
            requirements: List of requirements to analyze
            activities: List of activities to analyze
            parameters: Parameters for the analysis
            
        Returns:
            The analysis result
        """
        logger.info("Analyzing efficiency")
        
        # In a real implementation, this would perform a more sophisticated analysis
        # For now, return a placeholder result
        
        # Calculate average activities per requirement
        requirement_ids = [r.get('id') for r in requirements if r.get('id')]
        activities_per_requirement = {}
        
        for req_id in requirement_ids:
            req_activities = [a for a in activities if a.get('requirement_id') == req_id]
            activities_per_requirement[req_id] = len(req_activities)
        
        avg_activities = sum(activities_per_requirement.values()) / len(requirement_ids) if requirement_ids else 0
        
        # Calculate completion time for activities
        completion_times = []
        for activity in activities:
            if activity.get('status') == 'completed' and activity.get('start_date') and activity.get('end_date'):
                start_date = activity.get('start_date')
                end_date = activity.get('end_date')
                
                # Simple date difference calculation (assuming ISO format)
                try:
                    import datetime
                    start = datetime.datetime.fromisoformat(start_date)
                    end = datetime.datetime.fromisoformat(end_date)
                    days = (end - start).days
                    completion_times.append(days)
                except (ValueError, TypeError):
                    pass
        
        avg_completion_time = sum(completion_times) / len(completion_times) if completion_times else 0
        
        # Create the analysis result
        result = {
            'function': 'efficiency',
            'average_activities_per_requirement': avg_activities,
            'average_completion_time_days': avg_completion_time,
            'efficiency_score': 100 - (avg_activities * 5 + avg_completion_time),  # Simple inverse score
            'recommendations': []
        }
        
        # Generate recommendations
        if avg_activities > 5:
            result['recommendations'].append({
                'type': 'reduce_activities',
                'message': 'Consider consolidating activities to reduce overhead'
            })
        
        if avg_completion_time > 14:
            result['recommendations'].append({
                'type': 'improve_completion_time',
                'message': 'Look for ways to reduce activity completion time'
            })
        
        logger.info("Efficiency analysis completed")
        
        return result
