name: CI All Repositories

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

jobs:
  lint-and-test:
    name: <PERSON>t and Test All Repositories
    runs-on: ubuntu-latest
    strategy:
      matrix:
        repo: [nova-connect, nova-grc-apis, nova-ui, nova-gateway]
    steps:
      - name: Checkout nova-fuse
        uses: actions/checkout@v3
        with:
          repository: ${{ github.repository_owner }}/nova-fuse
          path: nova-fuse
      
      - name: Checkout ${{ matrix.repo }}
        uses: actions/checkout@v3
        with:
          repository: ${{ github.repository_owner }}/${{ matrix.repo }}
          path: ${{ matrix.repo }}
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: 'npm'
          cache-dependency-path: ${{ matrix.repo }}/package-lock.json
      
      - name: Install dependencies
        run: |
          cd ${{ matrix.repo }}
          npm ci
      
      - name: Run linting
        run: |
          cd ${{ matrix.repo }}
          npm run lint
      
      - name: Run tests
        run: |
          cd ${{ matrix.repo }}
          npm test
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.repo }}-test-results
          path: ${{ matrix.repo }}/coverage/
        if: always()

  build:
    name: Build All Repositories
    runs-on: ubuntu-latest
    needs: lint-and-test
    strategy:
      matrix:
        repo: [nova-connect, nova-grc-apis, nova-ui, nova-gateway]
    steps:
      - name: Checkout ${{ matrix.repo }}
        uses: actions/checkout@v3
        with:
          repository: ${{ github.repository_owner }}/${{ matrix.repo }}
          path: ${{ matrix.repo }}
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: 'npm'
          cache-dependency-path: ${{ matrix.repo }}/package-lock.json
      
      - name: Install dependencies
        run: |
          cd ${{ matrix.repo }}
          npm ci
      
      - name: Build
        run: |
          cd ${{ matrix.repo }}
          npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.repo }}-build
          path: |
            ${{ matrix.repo }}/dist/
            ${{ matrix.repo }}/.next/
        if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

  docker-build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    strategy:
      matrix:
        repo: [nova-connect, nova-grc-apis, nova-ui, nova-gateway]
    steps:
      - name: Checkout ${{ matrix.repo }}
        uses: actions/checkout@v3
        with:
          repository: ${{ github.repository_owner }}/${{ matrix.repo }}
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ghcr.io/${{ github.repository_owner }}/${{ matrix.repo }}:latest,ghcr.io/${{ github.repository_owner }}/${{ matrix.repo }}:${{ github.sha }}
