/**
 * Feature Flag Demo Page
 * 
 * This page demonstrates the feature flag system by showing which features
 * are enabled/disabled for each product tier. It provides a visual representation
 * of how the feature flag system controls access to features based on the product tier.
 */

import React from 'react';
import { useContext } from 'react';
import { ProductContext } from '../packages/feature-flags/ProductContext';
import { useFeatureFlag } from '../packages/feature-flags/useFeatureFlag';
import ProductSwitcher from '../components/common/ProductSwitcher';

/**
 * Feature Flag Demo Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function FeatureFlagDemo() {
  const { product } = useContext(ProductContext);
  
  // Define all features to test
  const features = [
    // Dashboard features
    { category: 'dashboard', feature: 'overview', name: 'Dashboard Overview' },
    { category: 'dashboard', feature: 'analytics', name: 'Analytics Dashboard' },
    { category: 'dashboard', feature: 'reports', name: 'Reports' },
    { category: 'dashboard', feature: 'customization', name: 'Dashboard Customization' },
    
    // GRC features
    { category: 'grc', feature: 'privacy', name: 'Privacy Management' },
    { category: 'grc', feature: 'security', name: 'Security Assessment' },
    { category: 'grc', feature: 'compliance', name: 'Regulatory Compliance' },
    { category: 'grc', feature: 'control', name: 'Control Testing' },
    { category: 'grc', feature: 'esg', name: 'ESG Management' },
    
    // Advanced features
    { category: 'advanced', feature: 'aiAssistant', name: 'AI Assistant' },
    { category: 'advanced', feature: 'predictiveAnalytics', name: 'Predictive Analytics' },
    { category: 'advanced', feature: 'automatedRemediation', name: 'Automated Remediation' },
    { category: 'advanced', feature: 'customIntegrations', name: 'Custom Integrations' },
    
    // Administration features
    { category: 'administration', feature: 'userManagement', name: 'User Management' },
    { category: 'administration', feature: 'roleManagement', name: 'Role Management' },
    { category: 'administration', feature: 'organizationSettings', name: 'Organization Settings' },
    { category: 'administration', feature: 'auditLogs', name: 'Audit Logs' },
    
    // Learning features
    { category: 'learning', feature: 'gamification', name: 'Gamification' },
    { category: 'learning', feature: 'trainingModules', name: 'Training Modules' },
    { category: 'learning', feature: 'certifications', name: 'Certifications' },
    { category: 'learning', feature: 'knowledgeBase', name: 'Knowledge Base' },
  ];
  
  // Format product name for display (e.g., novaPrime -> Nova Prime)
  const formatProductName = (productKey) => {
    return productKey
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase());
  };
  
  // Format category name for display (e.g., grc -> GRC)
  const formatCategoryName = (category) => {
    if (category.toLowerCase() === 'grc') {
      return 'GRC';
    }
    return category.charAt(0).toUpperCase() + category.slice(1);
  };
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Feature Flag Demo</h1>
      
      <ProductSwitcher />
      
      <div className="bg-white p-6 rounded shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">
          Current Product: {formatProductName(product)}
        </h2>
        <p className="text-gray-600">
          This page demonstrates which features are enabled for each product tier.
          Use the product switcher above to change the product tier and see how the
          available features change.
        </p>
      </div>
      
      {/* Feature categories */}
      {['dashboard', 'grc', 'advanced', 'administration', 'learning'].map((category) => (
        <div key={category} className="mb-8">
          <h2 className="text-xl font-semibold mb-4 border-b pb-2">
            {formatCategoryName(category)} Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {features
              .filter((f) => f.category === category)
              .map((feature) => (
                <FeatureCard
                  key={`${feature.category}-${feature.feature}`}
                  category={feature.category}
                  feature={feature.feature}
                  name={feature.name}
                />
              ))}
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * Feature Card Component
 * @param {object} props - Component props
 * @param {string} props.category - Feature category
 * @param {string} props.feature - Feature name
 * @param {string} props.name - Display name
 * @returns {React.ReactNode} - The rendered component
 */
function FeatureCard({ category, feature, name }) {
  const isEnabled = useFeatureFlag(category, feature);
  
  return (
    <div
      className={`p-4 rounded shadow ${
        isEnabled
          ? 'bg-green-50 border border-green-200'
          : 'bg-red-50 border border-red-200'
      }`}
    >
      <h3 className="font-semibold">{name}</h3>
      <div className="flex items-center mt-2">
        <span
          className={`inline-block w-3 h-3 rounded-full mr-2 ${
            isEnabled ? 'bg-green-500' : 'bg-red-500'
          }`}
        ></span>
        <span className={isEnabled ? 'text-green-700' : 'text-red-700'}>
          {isEnabled ? 'Enabled' : 'Disabled'}
        </span>
      </div>
    </div>
  );
}
