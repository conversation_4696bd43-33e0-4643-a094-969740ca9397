{"metadata": {"name": "Basic Auth Connector", "version": "1.0.0", "category": "Test", "description": "Test connector for Basic authentication", "author": "NovaGRC", "tags": ["test", "basic-auth"]}, "authentication": {"type": "BASIC", "fields": {"username": {"type": "string", "description": "Username", "required": true}, "password": {"type": "string", "description": "Password", "required": true, "sensitive": true}}, "testConnection": {"endpoint": "/health", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "http://localhost:3005", "headers": {"Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "getResource", "name": "Get Resource", "path": "/basic-auth/resource", "method": "GET", "parameters": {"query": {}, "path": {}, "body": {}}, "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "getResource", "targetSystem": "NovaGRC", "targetEntity": "Resource", "transformations": [{"source": "$.data.id", "target": "resourceId", "transform": "identity"}, {"source": "$.data.name", "target": "resourceName", "transform": "identity"}]}], "events": {"webhooks": [], "polling": []}}