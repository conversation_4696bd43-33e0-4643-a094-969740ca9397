# Quantum Protein Folding Dashboard

## Overview
The Quantum Protein Folding Dashboard is a real-time monitoring and management interface for quantum protein folding experiments. It provides visualization of protein structures, system metrics, and experiment status through an intuitive web-based interface.

## Features

### 1. Real-time System Monitoring
- CPU, Memory, GPU, and Storage usage metrics
- Network statistics and system information
- Visual indicators for system health

### 2. Protein Structure Visualization
- Interactive 3D visualization using 3DMol.js
- Multiple representation styles (Cartoon, Stick, Sphere, Line)
- Real-time rendering and manipulation
- Export functionality for images

### 3. Experiment Management
- Create and monitor quantum protein folding experiments
- Real-time status updates via WebSocket
- Filtering and pagination of experiment history
- Detailed experiment views

### 4. Quantum Backend Integration
- Support for multiple quantum backends (Qiskit, PennyLane, Amazon Braket)
- Backend status and capabilities display
- Resource estimation

## Architecture

### Frontend
- **Framework**: Bootstrap 5 with jQuery
- **Visualization**: 3DMol.js for protein structure rendering
- **Real-time Updates**: Socket.IO for WebSocket communication
- **Build System**: Static assets served via Flask

### Backend
- **Web Framework**: Flask
- **API**: RESTful endpoints for data retrieval
- **WebSockets**: Real-time event broadcasting
- **Task Queue**: Asynchronous job processing

## Getting Started

### Prerequisites
- Python 3.8+
- Node.js 14+ (for development)
- Modern web browser (Chrome, Firefox, Edge, or Safari)

### Installation

1. Clone the repository:
   ```bash
git clone https://github.com/yourusername/consciousness-chemistry-engine.git
   cd consciousness-chemistry-engine
```

2. Create and activate a virtual environment:
   ```bash
python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
   ```bash
pip install -r requirements.txt
```

4. Install development dependencies:
   ```bash
pip install -r requirements-dev.txt
```

### Running the Dashboard

1. Start the Flask development server:
   ```bash
python -m quantum.dashboard.app
```

2. Open your browser and navigate to:
   ```
http://localhost:5000
```

## User Guide

### Dashboard Layout

1. **Navigation Bar**
   - Quick access to main sections
   - System status indicators
   - User profile and settings

2. **System Status Cards**
   - Real-time resource usage metrics
   - Visual indicators for system health
   - Quick actions for system management

3. **Protein Visualization**
   - 3D viewport for protein structures
   - Style selector (Cartoon, Stick, Sphere, Line)
   - View controls and export options

4. **Experiment Management**
   - List of recent experiments with status indicators
   - Filtering and search functionality
   - Detailed experiment views

5. **Quantum Backends**
   - Available quantum computing backends
   - Status and capabilities
   - Resource utilization

### Using the Protein Viewer

1. **Loading a Structure**
   - Click "Load Example" to load a sample protein
   - Or enter a PDB ID in the search box

2. **Controlling the View**
   - **Rotate**: Left-click and drag
   - **Zoom**: Scroll up/down
   - **Pan**: Right-click and drag
   - **Reset View**: Click the reset button

3. **Changing Visualization Style**
   - Use the style buttons to switch between representations
   - Toggle between different color schemes

## API Reference

### REST API Endpoints

#### GET /api/experiments
List all experiments with optional filtering.

**Query Parameters:**
- `status`: Filter by status (pending, running, completed, failed)
- `limit`: Maximum number of results to return (default: 10)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
{
  "experiments": [
    {
      "id": "exp_123",
      "name": "My Experiment",
      "status": "running",
      "created_at": "2025-06-27T12:00:00Z",
      "progress": 45
    }
  ],
  "total": 42,
  "limit": 10,
  "offset": 0
}
```

#### POST /api/experiments
Create a new experiment.

**Request Body:**
```json
{
  "name": "My New Experiment",
  "parameters": {
    "sequence": "ACDEFGHIKLMNPQRSTVWY",
    "method": "qaoa",
    "iterations": 1000
  }
}
```

**Response:**
```json
{
  "id": "exp_124",
  "status": "pending",
  "created_at": "2025-06-27T12:05:00Z"
}
```

#### GET /api/experiments/{experiment_id}
Get details of a specific experiment.

**Response:**
```json
{
  "id": "exp_123",
  "name": "My Experiment",
  "status": "completed",
  "created_at": "2025-06-27T12:00:00Z",
  "completed_at": "2025-06-27T12:30:00Z",
  "parameters": {
    "sequence": "ACDEFGHIKLMNPQRSTVWY",
    "method": "qaoa",
    "iterations": 1000
  },
  "results": {
    "energy": -42.0,
    "structure": "...pdb data...",
    "metrics": {
      "accuracy": 0.95,
      "confidence": 0.92
    }
  }
}
```

### WebSocket Events

#### Server Events
- `system_status`: System metrics update
  ```json
{
    "cpu_percent": 24.5,
    "memory_percent": 36.2,
    "gpu_percent": 15.8,
    "disk_percent": 42.1,
    "timestamp": "2025-06-27T12:00:01Z"
  }
```

- `experiment_update`: Experiment status update
  ```json
{
    "experiment_id": "exp_123",
    "status": "running",
    "progress": 75,
    "message": "Optimizing quantum circuit..."
  }
```

#### Client Events
- `subscribe_system_updates`: Subscribe to system status updates
  ```json
{
    "interval": 5  // Update interval in seconds
  }
```

- `unsubscribe_system_updates`: Unsubscribe from system status updates

## Development

### Project Structure

```
quantum/dashboard/
├── app.py                 # Flask application
├── static/
│   ├── css/
│   │   └── styles.css    # Custom styles
│   └── js/
│       ├── dashboard.js # Main dashboard logic
│       └── main.js       # Shared utilities
└── templates/
    ├── base.html         # Base template
    ├── dashboard.html    # Main dashboard view
    └── experiments/      # Experiment-related templates
```

### Adding a New Visualization

1. Create a new JavaScript file in `static/js/` for your visualization
2. Add the script to the dashboard template
3. Initialize the visualization in `dashboard.js`
4. Add any necessary styles to `static/css/styles.css`

### Running Tests

```bash
pytest tests/test_dashboard.py
```

### Building for Production

1. Minify JavaScript and CSS:
   ```bash
npm install -g uglify-js clean-css-cli
   uglifyjs static/js/*.js -o static/js/bundle.min.js
   cleancss -o static/css/bundle.min.css static/css/*.css
```

2. Configure production settings in `config.py`

3. Deploy using a production WSGI server:
   ```bash
gunicorn -w 4 -b 0.0.0.0:5000 quantum.dashboard.app:app
```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Ensure the WebSocket server is running
   - Check for any proxy or firewall issues
   - Verify the WebSocket URL in the JavaScript console

2. **3D Rendering Issues**
   - Ensure WebGL is enabled in your browser
   - Try a different visualization style if performance is poor
   - Check the browser console for any WebGL errors

3. **API Authentication Errors**
   - Verify your API key is correctly set
   - Check the authentication headers in the request
   - Ensure your user account has the necessary permissions

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [3DMol.js](https://3dmol.org/) - For the molecular visualization library
- [Bootstrap](https://getbootstrap.com/) - For the responsive UI components
- [Flask](https://flask.palletsprojects.com/) - For the web framework
- [Socket.IO](https://socket.io/) - For real-time communication
# Quantum Backend Benchmarking

This guide explains how to benchmark and visualize the performance of different quantum backends with the RoseTTAFold engine in the ConsciousNovaFold system.

## Prerequisites

1. Python 3.8+
2. RoseTTAFold installed and configured
3. Required Python packages (install with `pip install -r requirements-visualization.txt`)

## Quick Start

### 1. Run a Benchmark

```bash
# Basic benchmark with default settings
python examples/run_quantum_benchmark.py --rosettafold-path /path/to/rosettafold

# Custom benchmark with specific parameters
python examples/run_quantum_benchmark.py \
    --rosettafold-path /path/to/rosettafold \
    --sequence ACEDGFIHKMLNQPSRTWV \
    --backends qiskit cirq \
    --shots 1000 \
    --depth 100 \
    --layers 2 \
    --output-dir my_benchmark_results
```

### 2. Visualize Results

Visualizations are generated automatically after the benchmark completes. You can also generate them manually:

```bash
# Visualize existing benchmark results
python scripts/visualize_benchmark.py path/to/benchmark_results

# Specify output directory
python scripts/visualize_benchmark.py path/to/benchmark_results -o path/to/output_dir
```

## Available Quantum Backends

### Qiskit Backend
- Uses IBM Quantum or local simulators
- Supports both CPU and GPU execution
- Configuration options:
  - `use_simulator`: Use local simulator (default: True)
  - `api_token`: IBM Quantum API token (required for real quantum hardware)
  - `backend_name`: Name of the IBM Quantum backend
  - `optimization_level`: Circuit optimization level (0-3)

### Cirq Backend
- Uses Google's Cirq and TensorFlow Quantum
- Supports hybrid quantum-classical computation
- Configuration options:
  - `use_tensorflow`: Use TensorFlow Quantum (default: True)
  - `simulator_type`: Type of simulator ('density_matrix' or 'wavefunction')
  - `noise_model`: Optional noise model configuration

## Benchmark Results

Benchmark results are saved in JSON format with the following structure:

```json
{
  "timestamp": "20230501_143000",
  "results": [
    {
      "status": "COMPLETED",
      "pdb_path": "/path/to/output.pdb",
      "output_dir": "/path/to/output/dir",
      "processing_time_seconds": 123.45,
      "sequence_length": 20,
      "quantum_info": {
        "used_quantum": true,
        "quantum_backend": "qiskit",
        "quantum_circuit_depth": 100,
        "quantum_shots": 1000,
        "quantum_layers": 2
      },
      "benchmark_metrics": {
        "backend": "qiskit",
        "sequence_length": 20,
        "duration": 123.45,
        "timestamp": "2023-05-01T14:30:00.123456",
        "config": { ... }
      }
    }
  ]
}
```

## Visualization Features

The visualization tools generate the following plots:

1. **Performance Comparison**
   - Processing time by backend and sequence length
   - Log-scale y-axis for better comparison of different time scales

2. **Resource Usage**
   - CPU usage (%)
   - Memory usage (MB)
   - GPU memory usage (MB, if available)
   - GPU load (%, if available)

3. **Quantum Metrics**
   - Circuit depth by backend
   - Number of shots by backend
   - Number of quantum layers by backend
   - Quantum backend distribution (pie chart)

## Advanced Usage

### Custom Benchmark Script

For more control over the benchmarking process, you can create a custom script:

```python
from src.rosettafold_engine import RoseTTAFoldEngine
from src.visualization.benchmark_visualizer import visualize_benchmark_results

def run_custom_benchmark():
    config = {
        'mode': 'hybrid',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': 'custom_benchmark',
        'quantum_backend': 'qiskit',
        'quantum_shots': 1000,
        'quantum_circuit_depth': 100,
        'quantum_layers': 2,
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
    
    # Initialize engine
    engine = RoseTTAFoldEngine(config=config)
    
    # Run prediction
    sequence = 'ACDEFGHIKLMNPQRSTVWY'
    result = engine.predict(sequence)
    
    return result

if __name__ == "__main__":
    # Run benchmark
    result = run_custom_benchmark()
    
    # Generate visualizations
    visualize_benchmark_results(
        results_dir='path/to/benchmark/results',
        output_dir='path/to/output/visualizations'
    )
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies**
   ```bash
pip install -r requirements-visualization.txt
```

2. **RoseTTAFold Not Found**
   - Ensure the `--rosettafold-path` points to the correct directory
   - Verify that RoseTTAFold is properly installed

3. **GPU Not Detected**
   - Install the appropriate GPU drivers
   - Make sure CUDA is properly configured
   - Check that TensorFlow/PyTorch can detect the GPU

4. **Visualization Errors**
   - Install the required visualization dependencies
   - Ensure you have write permissions to the output directory

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
# RoseTTAFold Engine

A high-performance, hybrid quantum-classical protein folding engine based on RoseTTAFold with consciousness-aware optimizations.

## Features

- **Hybrid Quantum-Classical Computation**: Leverage quantum computing for enhanced protein structure prediction
- **Multiple Operation Modes**:
  - `classical`: Standard RoseTTAFold execution
  - `hybrid`: Quantum-enhanced classical computation (recommended)
  - `quantum`: Full quantum mode (experimental)
- **Consciousness-Aware Folding**: Ψ-score optimization for biologically relevant structures
- **Fibonacci Constraints**: Ensure structural stability using golden ratio constraints
- **GPU Acceleration**: Optimized for NVIDIA GPUs with CUDA support
- **Flexible Backend Support**: Compatible with multiple quantum backends (qsim, qiskit, etc.)

## Installation

1. Install the required dependencies:
   ```bash
pip install -r requirements.txt
```

2. Set up RoseTTAFold:
   ```bash
git clone https://github.com/RosettaCommons/RoseTTAFold.git
   cd RoseTTAFold
   ./install_dependencies.sh
```

3. Set environment variables:
   ```bash
export ROSETTAFOLD_PATH=/path/to/rosettafold
```

## Quick Start

```python
from src.rosettafold_engine import RoseTTAFoldEngine

# Initialize with hybrid mode (quantum-enhanced classical computation)
engine = RoseTTAFoldEngine(
    config={
        'mode': 'hybrid',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './output',
        'gpu_id': 0,
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
)

# Run prediction
result = engine.predict("ACDEFGHIKLMNPQRSTVWY")
print(f"Predicted structure saved to: {result['pdb_path']}")
```

## Configuration Options

### Core Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `mode` | str | 'hybrid' | Operation mode: 'classical', 'hybrid', or 'quantum' |
| `rosettafold_path` | str | Required | Path to local RoseTTAFold installation |
| `output_dir` | str | './rosettafold_output' | Directory to save output files |
| `gpu_id` | int | 0 | ID of the GPU to use |
| `debug` | bool | False | Enable debug mode (keeps temporary files) |

### Quantum Computing Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `use_quantum` | bool | True | Enable quantum computation |
| `quantum_backend` | str | 'qsim' | Quantum backend to use ('qsim', 'qiskit', etc.) |
| `quantum_circuit_depth` | int | 100 (hybrid), 500 (quantum) | Depth of quantum circuit |
| `quantum_shots` | int | 1000 (hybrid), 5000 (quantum) | Number of quantum measurements |
| `quantum_layers` | int | 2 (hybrid), 4 (quantum) | Number of quantum layers |

### Consciousness Optimization

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `psi_optimization` | bool | False | Enable consciousness optimization |
| `fib_constraints.enabled` | bool | False | Enable Fibonacci constraints |
| `fib_constraints.tolerance` | float | 0.1 | Tolerance for Fibonacci constraints |

## Example: Running with Different Modes

### Classical Mode

```python
engine = RoseTTAFoldEngine(
    config={
        'mode': 'classical',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './classical_output'
    }
)
```

### Hybrid Mode (Recommended)

```python
engine = RoseTTAFoldEngine(
    config={
        'mode': 'hybrid',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './hybrid_output',
        'quantum_backend': 'qsim',
        'quantum_circuit_depth': 100,
        'quantum_shots': 1000,
        'quantum_layers': 2,
        'psi_optimization': True,
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        }
    }
)
```

### Quantum Mode (Experimental)

```python
engine = RoseTTAFoldEngine(
    config={
        'mode': 'quantum',
        'rosettafold_path': '/path/to/rosettafold',
        'output_dir': './quantum_output',
        'quantum_backend': 'qsim',
        'quantum_circuit_depth': 500,
        'quantum_shots': 5000,
        'quantum_layers': 4,
        'psi_optimization': True
    }
)
```

## Command Line Interface

Run predictions directly from the command line:

```bash
python examples/run_rosettafold.py \
  --sequence ACEDGFIHKMLNQPSRTWV \
  --output-dir ./results \
  --rosettafold-path /path/to/rosettafold \
  --mode hybrid
```

## Output Format

The prediction result is returned as a dictionary with the following structure:

```python
{
    'status': 'COMPLETED',  # or 'FAILED'
    'pdb_path': '/path/to/output.pdb',  # Path to predicted structure
    'output_dir': '/path/to/output/dir',  # Output directory
    'processing_time_seconds': 123.45,  # Time taken for prediction
    'sequence_length': 20,  # Length of input sequence
    'quantum_info': {  # Quantum computation details
        'used_quantum': True,
        'quantum_backend': 'qsim',
        'quantum_circuit_depth': 100,
        'quantum_shots': 1000,
        'quantum_layers': 2
    },
    'config_used': {  # Configuration used for this prediction
        'mode': 'hybrid',
        'psi_optimization': True,
        'fib_constraints': {'enabled': True, 'tolerance': 0.1}
    },
    'metadata': {  # Additional metadata
        'job_name': 'rosettafold_1234567890',
        'timestamp': '2023-01-01T12:00:00.000000',
        'parameters': {}  # Any additional parameters passed to predict()
    }
}
```

## Troubleshooting

### Common Issues

1. **Missing Dependencies**:
   ```bash
# Install required Python packages
   pip install -r requirements.txt
```

2. **CUDA Errors**:
   - Ensure CUDA is properly installed
   - Check that your GPU has sufficient memory
   - Try reducing batch size or using a smaller model

3. **Quantum Backend Not Found**:
   - Install the required quantum backend (e.g., `pip install qsimcirq`)
   - Check that the backend is properly configured

4. **RoseTTAFold Not Found**:
   - Set the `ROSETTAFOLD_PATH` environment variable
   - Or provide the path in the configuration

### Debugging

Enable debug mode for more detailed logs:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance Tips

1. **GPU Memory**:
   - Use smaller batch sizes for long sequences
   - Enable mixed precision training with `use_mixed_precision: True`

2. **Quantum Computation**:
   - Start with hybrid mode before trying full quantum mode
   - Reduce `quantum_circuit_depth` for faster but potentially less accurate results
   - Increase `quantum_shots` for more accurate quantum measurements

3. **Caching**:
   - Enable caching to avoid redundant computations
   - Use `force_refresh=True` to force recomputation when needed

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
# LocalAlphaFoldEngine

A high-performance, GPU-optimized wrapper for running AlphaFold locally with consciousness-aware optimizations.

## Features

- **GPU Memory Optimization**: Automatic memory management for different GPU configurations
- **Consciousness-Aware Folding**: Ψ-score optimization for biologically relevant structures
- **Fibonacci Constraints**: Ensure structural stability using golden ratio constraints
- **Caching System**: Avoid redundant computations with intelligent caching
- **Real-time Monitoring**: Track GPU usage and prediction progress

## Installation

1. Ensure you have AlphaFold installed locally
2. Install the required dependencies:
   ```bash
pip install -r requirements.txt
```
3. Set the `ALPHAFOLD_PATH` environment variable to your AlphaFold installation directory

## Usage

### Basic Usage

```python
from src.folding_engines import LocalAlphaFoldEngine

# Initialize with default settings
engine = LocalAlphaFoldEngine(
    alphafold_path="/path/to/alphafold",
    output_dir="./output"
)

# Run prediction
result = engine.predict("ACDEFGHIKLMNPQRSTVWY")
print(f"Predicted structure saved to: {result['pdb_path']}")
```

### Advanced Configuration

```python
engine = LocalAlphaFoldEngine(
    config={
        'mode': 'therapeutic',  # Standard, therapeutic, or speed
        'alphafold_path': "/path/to/alphafold",
        'output_dir': "./output",
        'gpu_id': 0,
        'psi_optimization': True,  # Enable consciousness optimization
        'fib_constraints': {
            'enabled': True,
            'tolerance': 0.1
        },
        'model_preset': 'monomer_ptm',
        'db_preset': 'full_dbs'
    }
)
```

### Using Presets

```python
# Using a preset configuration
from src.folding_engines import create_engine

# Create engine with therapeutic preset
engine = create_engine(
    'local_alphafold',
    mode='therapeutic',
    alphafold_path="/path/to/alphafold",
    output_dir="./output"
)
```

## Configuration Options

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `mode` | str | 'standard' | Operation mode: 'standard', 'therapeutic', or 'speed' |
| `alphafold_path` | str | Required | Path to local AlphaFold installation |
| `output_dir` | str | './output' | Directory to save output files |
| `gpu_id` | int | 0 | ID of the GPU to use |
| `model_preset` | str | 'monomer' | AlphaFold model preset |
| `db_preset` | str | 'reduced_dbs' | Database preset ('reduced_dbs' or 'full_dbs') |
| `psi_optimization` | bool | False | Enable consciousness optimization |
| `fib_constraints` | dict | {'enabled': False} | Fibonacci constraint settings |
| `use_precomputed_msas` | bool | False | Use precomputed MSAs |
| `num_predictions` | int | 1 | Number of predictions to generate |

## Example Script

Run the example script to test different configurations:

```bash
python examples/run_local_alphafold.py \
  --alphafold-path /path/to/alphafold \
  --output-dir ./output \
  --gpu-id 0 \
  --sequence "ACDEFGHIKLMNPQRSTVWY"
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Try reducing the batch size or using a smaller model
2. **Missing Dependencies**: Ensure all AlphaFold dependencies are installed
3. **Incorrect Paths**: Verify `ALPHAFOLD_PATH` and other paths are correct

### Logging

Set the log level to `DEBUG` for detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
# AlphaFold Integration with NovaFoldClient

This example demonstrates how to integrate the AlphaFold protein structure prediction service with the NovaFoldClient for advanced protein structure analysis with comphyological metrics.

## Prerequisites

1. Python 3.8 or higher
2. Required Python packages (install with `pip install -r requirements.txt`)
3. An AlphaFold API key (optional, but required for real predictions)

## Setup

1. Clone the repository:
   ```bash
git clone https://github.com/yourusername/consciousness-chemistry-engine.git
   cd consciousness-chemistry-engine
```

2. Install the required dependencies:
   ```bash
pip install -r examples/requirements.txt
```

3. (Optional) Set your AlphaFold API key as an environment variable:
   - Linux/macOS:
     ```bash
export ALPHAFOLD_API_KEY='your_api_key_here'
```
   - Windows (Command Prompt):
     ```cmd
set ALPHAFOLD_API_KEY=your_api_key_here
```
   - Windows (PowerShell):
     ```powershell
$env:ALPHAFOLD_API_KEY='your_api_key_here'
```

## Running the Example

```bash
python examples/alphafold_integration.py
```

## Example Output

```
Initializing NovaFoldClient with AlphaFold engine...

Predicting structure for sequence (length: 63):
MKALTARQQEVFDLIRDHISQTGMPPTRAEIAQRLGFRSPNADKRVNGQTYAQQARKAFQERIDKSKEA

Prediction complete!
Prediction source: external_engine
Engine: AlphaFoldEngine
Job ID: af2_1234567890

Validation results:
- sequence_length: 63
- invalid_amino_acids: []
- amino_acid_distribution:
  - A: 0.12698412698412698
  - C: 0.0
  - D: 0.07936507936507936
  - E: 0.1111111111111111
  - F: 0.047619047619047616
  - G: 0.06349206349206349
  - H: 0.031746031746031744
  - I: 0.09523809523809523
  - K: 0.1111111111111111
  - L: 0.047619047619047616
  - M: 0.031746031746031744
  - N: 0.031746031746031744
  - P: 0.06349206349206349
  - Q: 0.1111111111111111
  - R: 0.1111111111111111
  - S: 0.047619047619047616
  - T: 0.06349206349206349
  - V: 0.015873015873015872
  - W: 0.0
  - Y: 0.015873015873015872
```

## How It Works

The example script performs the following steps:

1. Initializes a `NovaFoldClient` with the AlphaFold engine
2. Submits a protein sequence for structure prediction
3. Processes the prediction results
4. Applies comphyological metrics and validations
5. Displays the results

## Customization

You can modify the example to:

1. Use a different protein sequence
2. Apply folding variants (e.g., 'misfolded', 'thermostable')
3. Validate against known PDB structures
4. Save the results to a file

## Troubleshooting

- **API Key Issues**: Ensure your AlphaFold API key is set correctly as an environment variable
- **Network Errors**: Check your internet connection and firewall settings
- **Dependency Issues**: Make sure all required packages are installed with the correct versions

## License

This example is part of the Consciousness Chemistry Engine project and is available under the MIT License.
"""
Test script for folding archetypes in NovaFoldClient.
"""

import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from ConsciousNovaFold import NovaFoldClient

def test_folding_archetypes():
    print("Testing folding archetypes in NovaFoldClient...\n")
    
    # Initialize the client
    client = NovaFoldClient(enable_benchmark=False)
    
    # Test sequence
    sequence = "ACDEFGHIKLMNPQRSTVWY"  # 20 amino acids
    
    # List available archetypes
    print("Available archetypes:")
    archetypes = client.list_archetypes()
    for archetype in archetypes:
        info = client.get_archetype_info(archetype)
        print(f"- {archetype}: {info.get('description', 'No description')}")
    
    # Test each archetype
    print("\nTesting predictions with different archetypes:")
    for archetype in archetypes:
        print(f"\n--- {archetype.upper()} ---")
        
        # Get prediction
        result = client.predict(
            sequence=sequence,
            folding_variant=archetype,
            validate_against=None
        )
        
        # Extract and display relevant information
        metadata = result['structure']['metadata']
        ss = result['structure']['secondary_structure']
        ss_counts = {s: ss.count(s) for s in set(ss)}
        plddt_avg = sum(result['structure']['plddt']) / len(result['structure']['plddt'])
        
        print(f"Description: {metadata.get('archetype_description', 'N/A')}")
        print(f"Consciousness impact: {metadata.get('consciousness_impact', 'N/A')}")
        print(f"Secondary structure: {ss}")
        print(f"SS counts: {ss_counts}")
        print(f"Avg pLDDT: {plddt_avg:.2f}")
        
        # Print special properties
        if 'cross_beta_pattern' in metadata:
            print("Cross-beta pattern detected (amyloid)")
        if 'quantum_entangled' in metadata:
            print(f"Quantum properties: {metadata.get('quantum_entropy', 'N/A')}")
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    test_folding_archetypes()
