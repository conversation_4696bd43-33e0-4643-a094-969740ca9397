# Regulatory Compliance Module

The Regulatory Compliance module provides functionality for managing regulatory frameworks, compliance requirements, and compliance status tracking in the Privacy Management API.

## Features

- **Regulatory Frameworks**: Manage regulatory frameworks such as GDPR, CCPA, HIPAA, etc.
- **Compliance Requirements**: Track compliance requirements for each regulatory framework
- **Compliance Status**: Track compliance status for entities against regulatory frameworks
- **Compliance Reports**: Generate compliance reports for entities
- **Requirement Mapping**: Map requirements between different regulatory frameworks
- **Regulatory Updates**: Track regulatory updates and their impact on compliance

## API Endpoints

### Regulatory Frameworks

- `GET /compliance/frameworks`: Get all regulatory frameworks
- `GET /compliance/frameworks/{id}`: Get regulatory framework by ID
- `GET /compliance/frameworks/code/{code}`: Get regulatory framework by code

### Compliance Requirements

- `GET /compliance/frameworks/{frameworkId}/requirements`: Get compliance requirements for a framework
- `GET /compliance/requirements/{id}`: Get compliance requirement by ID
- `GET /compliance/mapping/{sourceFrameworkId}/{targetFrameworkId}`: Map requirements between frameworks

### Compliance Status

- `GET /compliance/status/{entityType}/{entityId}/{frameworkId}`: Get compliance status for an entity
- `PATCH /compliance/status/{statusId}/requirements/{requirementId}`: Update compliance status for a requirement

### Compliance Reports

- `GET /compliance/reports/{entityType}/{entityId}/{frameworkId}`: Generate compliance report for an entity

### Regulatory Updates

- `GET /compliance/updates`: Get regulatory updates

## Models

### RegulatoryFramework

Represents a regulatory framework such as GDPR, CCPA, HIPAA, etc.

```javascript
{
  name: String,           // Framework name
  code: String,           // Framework code
  version: String,        // Framework version
  description: String,    // Framework description
  category: String,       // Framework category
  regions: [String],      // Regions where the framework applies
  countries: [String],    // Countries where the framework applies
  effectiveDate: Date,    // Date when the framework became effective
  lastUpdated: Date,      // Date when the framework was last updated
  authority: String,      // Authority responsible for the framework
  authorityUrl: String,   // URL of the authority
  documentUrl: String,    // URL of the framework document
  status: String,         // Framework status
  supersededBy: ObjectId, // ID of the framework that supersedes this one
  relatedFrameworks: [{   // Related frameworks
    framework: ObjectId,  // Related framework ID
    relationship: String, // Relationship type
    notes: String         // Notes about the relationship
  }]
}
```

### ComplianceRequirement

Represents a compliance requirement for a regulatory framework.

```javascript
{
  framework: ObjectId,    // Framework ID
  section: String,        // Section identifier
  number: String,         // Requirement number
  title: String,          // Requirement title
  description: String,    // Requirement description
  guidance: String,       // Implementation guidance
  category: String,       // Requirement category
  subCategory: String,    // Requirement sub-category
  applicability: String,  // Applicability of the requirement
  applicabilityCondition: String, // Condition for applicability
  risk: String,           // Risk level
  implementationEffort: String, // Implementation effort
  keywords: [String],     // Keywords related to the requirement
  relatedRequirements: [{ // Related requirements
    framework: ObjectId,  // Related framework ID
    requirement: ObjectId, // Related requirement ID
    requirementCode: String, // Related requirement code
    requirementTitle: String, // Related requirement title
    mappingStrength: String, // Strength of the mapping
    notes: String         // Notes about the relationship
  }],
  implementationGuidance: String, // Detailed implementation guidance
  evidenceRequired: [String], // Evidence required for compliance
  status: String          // Requirement status
}
```

### ComplianceStatus

Represents the compliance status of an entity against a regulatory framework.

```javascript
{
  entityType: String,     // Entity type
  entityId: String,       // Entity ID
  framework: ObjectId,    // Framework ID
  frameworkName: String,  // Framework name
  frameworkCode: String,  // Framework code
  frameworkVersion: String, // Framework version
  status: String,         // Overall compliance status
  progress: Number,       // Compliance progress percentage
  requirementStatuses: [{ // Status of individual requirements
    requirement: ObjectId, // Requirement ID
    requirementCode: String, // Requirement code
    requirementTitle: String, // Requirement title
    status: String,       // Compliance status
    evidence: [{          // Evidence supporting the compliance status
      type: String,       // Evidence type
      title: String,      // Evidence title
      description: String, // Evidence description
      url: String,        // URL to evidence
      fileId: String,     // File ID
      fileName: String,   // File name
      fileType: String,   // File type
      fileSize: Number,   // File size in bytes
      content: String,    // Text content
      addedBy: ObjectId,  // User ID who added the evidence
      addedAt: Date       // Date when the evidence was added
    }],
    notes: String,        // Notes about the compliance status
    assignedTo: ObjectId, // User ID of the person assigned to this requirement
    dueDate: Date,        // Due date for compliance
    completedDate: Date,  // Date when compliance was achieved
    lastUpdatedBy: ObjectId, // User ID who last updated the status
    lastUpdatedAt: Date,  // Date when the status was last updated
    history: [{           // History of status changes
      status: String,     // Previous status
      notes: String,      // Notes about the status change
      updatedBy: ObjectId, // User ID who updated the status
      updatedAt: Date     // Date when the status was updated
    }]
  }],
  lastAssessment: Date,   // Date of the last assessment
  nextAssessment: Date,   // Date of the next scheduled assessment
  assessmentFrequency: String, // Assessment frequency
  assessmentFrequencyDays: Number, // Custom assessment frequency in days
  notes: String           // Notes about the compliance status
}
```

## Usage Examples

### Get All Regulatory Frameworks

```javascript
const response = await fetch('/api/privacy/management/compliance/frameworks');
const data = await response.json();
console.log(data.data); // Array of regulatory frameworks
```

### Get Compliance Requirements for GDPR

```javascript
const gdprId = '60d5ec9f8e1f8c001f3c9a1d'; // Example GDPR framework ID
const response = await fetch(`/api/privacy/management/compliance/frameworks/${gdprId}/requirements`);
const data = await response.json();
console.log(data.data); // Array of GDPR compliance requirements
```

### Get Compliance Status for an Organization

```javascript
const entityType = 'organization';
const entityId = 'org-123';
const frameworkId = '60d5ec9f8e1f8c001f3c9a1d'; // Example GDPR framework ID
const response = await fetch(`/api/privacy/management/compliance/status/${entityType}/${entityId}/${frameworkId}`);
const data = await response.json();
console.log(data.data); // Compliance status for the organization
```

### Update Compliance Status for a Requirement

```javascript
const statusId = '60d5ec9f8e1f8c001f3c9a2d'; // Example compliance status ID
const requirementId = '60d5ec9f8e1f8c001f3c9a3d'; // Example requirement ID
const response = await fetch(`/api/privacy/management/compliance/status/${statusId}/requirements/${requirementId}`, {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    status: 'compliant',
    notes: 'Implemented data processing principles',
    evidence: [
      {
        type: 'document',
        title: 'Data Processing Policy',
        description: 'Company policy on data processing principles',
        fileId: 'file-123'
      }
    ]
  })
});
const data = await response.json();
console.log(data.data); // Updated compliance status
```

### Generate Compliance Report

```javascript
const entityType = 'organization';
const entityId = 'org-123';
const frameworkId = '60d5ec9f8e1f8c001f3c9a1d'; // Example GDPR framework ID
const response = await fetch(`/api/privacy/management/compliance/reports/${entityType}/${entityId}/${frameworkId}`);
const data = await response.json();
console.log(data.data); // Compliance report for the organization
```

## Implementation Details

The Regulatory Compliance module is implemented using the following components:

- **Models**: MongoDB schemas for regulatory frameworks, compliance requirements, and compliance status
- **Services**: Business logic for managing regulatory compliance
- **Controllers**: API endpoints for interacting with the regulatory compliance module
- **Routes**: Express routes for the API endpoints
- **Validations**: Joi validation schemas for API requests
- **Middleware**: Authentication and validation middleware
- **Tests**: Unit and integration tests for the module

## Future Enhancements

- **Compliance Automation**: Automate compliance checks and assessments
- **Risk Assessment**: Add risk assessment functionality for compliance requirements
- **Compliance Calendar**: Add a calendar for compliance deadlines and assessments
- **Compliance Workflows**: Add workflows for compliance tasks and approvals
- **Compliance Analytics**: Add analytics and dashboards for compliance status
- **Compliance Notifications**: Add notifications for compliance deadlines and updates
