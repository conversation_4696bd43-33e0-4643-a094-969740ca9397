# Quick Start Guide

This guide will help you get started with NovaAlign in minutes.

## Prerequisites
- Node.js 16.x or later
- npm 8.x or later
- Python 3.8+ (for certain components)
- Docker (for containerized deployment)

## Installation

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/nova-align.git
cd nova-align
```

### 2. Install Dependencies
```bash
# Install frontend dependencies
cd dashboard
npm install

# Install backend dependencies
cd ../server
pip install -r requirements.txt
```

### 3. Configure Environment
Create a `.env` file in the project root:
```env
NODE_ENV=development
API_URL=http://localhost:3001
AUTH_SECRET=your-secret-key
```

### 4. Start the Services
```bash
# Start the frontend
cd dashboard
npm run dev

# In a new terminal, start the backend
cd ../server
python app.py
```

## First Run
1. Open `http://localhost:3000` in your browser
2. Log in with the default credentials:
   - Email: <EMAIL>
   - Password: changeme
3. Follow the setup wizard to configure your first AI system

## Next Steps
- [Configure your first AI system](./user-guide/dashboard.md)
- [Explore the API documentation](./api/endpoints.md)
- [Learn about deployment options](./deployment/installation.md)
