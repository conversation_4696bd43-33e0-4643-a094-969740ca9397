<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse API Superstore - API Documentation</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swagger-ui-dist@4.5.0/swagger-ui.css">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    }
    .header {
      background-color: #1a365d;
      color: white;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 24px;
    }
    .header p {
      margin: 10px 0 0;
      font-size: 16px;
    }
    .content {
      padding: 20px;
    }
    .service-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-top: 20px;
    }
    .service-card {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      flex: 1 1 300px;
    }
    .service-card h3 {
      margin-top: 0;
      color: #1a365d;
    }
    .service-card p {
      margin-bottom: 15px;
    }
    .service-card a {
      display: inline-block;
      background-color: #1a365d;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      text-decoration: none;
      font-weight: bold;
    }
    .service-card a:hover {
      background-color: #2c4c7c;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>NovaFuse API Superstore</h1>
    <p>Comprehensive API Documentation</p>
  </div>

  <div class="content">
    <h2>Available Services</h2>

    <div class="service-list">
      <div class="service-card">
        <h3>NovaConnect</h3>
        <p>Universal API Connector for connecting, testing, and monitoring APIs from various sources.</p>
        <a href="/api/novaconnect/docs">View Documentation</a>
      </div>

      <div class="service-card">
        <h3>Privacy Management</h3>
        <p>API for managing privacy-related operations and compliance.</p>
        <a href="/api/privacy/management/docs">View Documentation</a>
      </div>

      <div class="service-card">
        <h3>Regulatory Compliance</h3>
        <p>API for managing regulatory compliance frameworks and requirements.</p>
        <a href="/api/compliance/docs">View Documentation</a>
      </div>

      <div class="service-card">
        <h3>Security Assessment</h3>
        <p>API for security assessment and management.</p>
        <a href="/api/security/assessment/docs">View Documentation</a>
      </div>

      <div class="service-card">
        <h3>Control Testing</h3>
        <p>API for control testing and management.</p>
        <a href="/api/control/testing/docs">View Documentation</a>
      </div>

      <div class="service-card">
        <h3>ESG</h3>
        <p>API for Environmental, Social, and Governance management.</p>
        <a href="/api/esg/docs">View Documentation</a>
      </div>

      <div class="service-card">
        <h3>Compliance Automation</h3>
        <p>API for automating compliance processes and workflows.</p>
        <a href="/api/compliance/automation/docs">View Documentation</a>
      </div>
    </div>

    <h2>Authentication</h2>
    <p>The NovaFuse API Superstore uses JWT authentication. To authenticate, send a POST request to <code>/auth/login</code> with your username and password.</p>
    <pre><code>
POST /auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
    </code></pre>

    <p>The response will include a JWT token that you can use for subsequent requests:</p>
    <pre><code>
{
  "success": true,
  "token": "your-jwt-token",
  "user": {
    "id": "user-id",
    "username": "your-username",
    "role": "your-role"
  }
}
    </code></pre>

    <p>Include the token in the Authorization header for authenticated requests:</p>
    <pre><code>
Authorization: Bearer your-jwt-token
    </code></pre>

    <h2>API Key Authentication</h2>
    <p>Some services also support API key authentication. To use API key authentication, include your API key in the X-API-Key header:</p>
    <pre><code>
X-API-Key: your-api-key
    </code></pre>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/swagger-ui-dist@4.5.0/swagger-ui-bundle.js"></script>
  <script>
    // You can add JavaScript here to enhance the documentation page
  </script>
</body>
</html>
