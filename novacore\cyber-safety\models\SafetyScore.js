/**
 * NovaCore Safety Score Model
 * 
 * This model defines the schema for safety scores in the Cyber-Safety platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define components schema
const componentsSchema = new Schema({
  risk: { 
    type: Number, 
    required: true, 
    min: 0, 
    max: 100 
  },
  compliance: { 
    type: Number, 
    required: true, 
    min: 0, 
    max: 100 
  },
  policy: { 
    type: Number, 
    required: true, 
    min: 0, 
    max: 100 
  }
}, { _id: false });

// Define context schema
const contextSchema = new Schema({
  endpoint: { 
    type: String, 
    required: true, 
    trim: true 
  },
  method: { 
    type: String, 
    required: true, 
    trim: true 
  },
  user: { 
    type: String, 
    trim: true 
  },
  metadata: { 
    type: Map, 
    of: Schema.Types.Mixed 
  }
}, { _id: false });

// Define safety score schema
const safetyScoreSchema = new Schema({
  operationId: { 
    type: String, 
    required: true, 
    trim: true, 
    index: true 
  },
  timestamp: { 
    type: Date, 
    default: Date.now 
  },
  overallScore: { 
    type: Number, 
    required: true, 
    min: 0, 
    max: 100 
  },
  safetyLevel: { 
    type: String, 
    required: true, 
    enum: ['excellent', 'good', 'moderate', 'fair', 'poor'], 
    default: 'moderate' 
  },
  components: { 
    type: componentsSchema, 
    required: true 
  },
  context: { 
    type: contextSchema, 
    required: true 
  },
  evidenceIds: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'Evidence' 
  }],
  verificationIds: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'BlockchainVerification' 
  }]
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
safetyScoreSchema.index({ 'context.endpoint': 1, 'context.method': 1 });
safetyScoreSchema.index({ 'context.user': 1 });
safetyScoreSchema.index({ overallScore: 1 });
safetyScoreSchema.index({ safetyLevel: 1 });
safetyScoreSchema.index({ createdAt: 1 });

// Add methods
safetyScoreSchema.methods.isExcellent = function() {
  return this.safetyLevel === 'excellent';
};

safetyScoreSchema.methods.isGood = function() {
  return this.safetyLevel === 'good' || this.safetyLevel === 'excellent';
};

safetyScoreSchema.methods.isPoor = function() {
  return this.safetyLevel === 'poor' || this.safetyLevel === 'fair';
};

// Add statics
safetyScoreSchema.statics.findByEndpoint = function(endpoint, method) {
  return this.find({
    'context.endpoint': endpoint,
    'context.method': method
  }).sort({ createdAt: -1 });
};

safetyScoreSchema.statics.findByUser = function(user) {
  return this.find({
    'context.user': user
  }).sort({ createdAt: -1 });
};

safetyScoreSchema.statics.findBySafetyLevel = function(level) {
  return this.find({
    safetyLevel: level
  }).sort({ createdAt: -1 });
};

// Create model
const SafetyScore = mongoose.model('SafetyScore', safetyScoreSchema);

module.exports = SafetyScore;
