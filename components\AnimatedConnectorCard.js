import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';

/**
 * Animated connector card component
 * @param {Object} props - Component props
 * @param {Object} props.connector - Connector data
 * @param {number} props.index - Index for staggered animation
 */
const AnimatedConnectorCard = ({ connector, index }) => {
  // Card animation
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: index * 0.1
      }
    },
    hover: {
      y: -10,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3)',
      transition: { duration: 0.3 }
    }
  };

  return (
    <motion.div
      className="bg-secondary rounded-lg overflow-hidden shadow-lg"
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
    >
      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{connector.name}</h3>
        <p className="text-sm text-gray-400 mb-4">by {connector.vendor}</p>

        <div className="flex flex-wrap gap-2 mb-4">
          <span className="bg-blue-600 text-xs px-2 py-1 rounded-full">{connector.framework.toUpperCase()}</span>
          <span className="bg-gray-700 text-xs px-2 py-1 rounded-full">{connector.category.replace('-', ' ')}</span>
        </div>

        <p className="mb-4 overflow-hidden" style={{ display: '-webkit-box', WebkitLineClamp: 3, WebkitBoxOrient: 'vertical' }}>{connector.description}</p>

        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <span className="text-yellow-400 mr-1">★</span>
            <span>{connector.rating}</span>
          </div>
          <span className="text-green-400 font-bold">{connector.price}</span>
        </div>
      </div>

      <div className="border-t border-gray-700 p-4">
        <Link href={`/compliance-store/connectors/${connector.id}`} className="block w-full">
          <motion.div
            className="w-full text-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View Details
          </motion.div>
        </Link>
      </div>
    </motion.div>
  );
};

export default AnimatedConnectorCard;
