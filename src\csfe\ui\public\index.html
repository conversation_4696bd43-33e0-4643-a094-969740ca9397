<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSFE NovaVision Dashboard</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.min.css">
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <img src="img/novafuse-logo.png" alt="NovaFuse Logo" height="30" class="d-inline-block align-text-top me-2">
        CSFE NovaVision Dashboard
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link active" href="#overview">Overview</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#market-predictions">Market Predictions</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#asset-allocation">Asset Allocation</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#risk-assessment">Risk Assessment</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#timeline-predictions">Timeline Predictions</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#depression-prediction">Depression Prediction</a>
          </li>
        </ul>
        <div class="d-flex">
          <select class="form-select me-2" id="engine-selector">
            <option value="standard">Standard CSFE</option>
            <option value="trinity">Trinity CSFE</option>
            <option value="trinity-1882">Trinity CSFE 18/82</option>
          </select>
          <button class="btn btn-light" id="calculate-btn">Calculate</button>
        </div>
      </div>
    </div>
  </nav>

  <div class="container-fluid mt-4">
    <div class="row">
      <div class="col-md-3">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Input Data</h5>
          </div>
          <div class="card-body">
            <ul class="nav nav-tabs" id="inputTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="market-tab" data-bs-toggle="tab" data-bs-target="#market-data" type="button" role="tab" aria-controls="market-data" aria-selected="true">Market</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="economic-tab" data-bs-toggle="tab" data-bs-target="#economic-data" type="button" role="tab" aria-controls="economic-data" aria-selected="false">Economic</button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="sentiment-tab" data-bs-toggle="tab" data-bs-target="#sentiment-data" type="button" role="tab" aria-controls="sentiment-data" aria-selected="false">Sentiment</button>
              </li>
            </ul>
            <div class="tab-content mt-3" id="inputTabContent">
              <div class="tab-pane fade show active" id="market-data" role="tabpanel" aria-labelledby="market-tab">
                <div id="market-data-form">
                  <!-- Market data form will be dynamically generated -->
                </div>
              </div>
              <div class="tab-pane fade" id="economic-data" role="tabpanel" aria-labelledby="economic-tab">
                <div id="economic-data-form">
                  <!-- Economic data form will be dynamically generated -->
                </div>
              </div>
              <div class="tab-pane fade" id="sentiment-data" role="tabpanel" aria-labelledby="sentiment-tab">
                <div id="sentiment-data-form">
                  <!-- Sentiment data form will be dynamically generated -->
                </div>
              </div>
            </div>
            <div class="d-grid gap-2 mt-3">
              <button class="btn btn-primary" id="load-sample-data-btn">Load Sample Data</button>
              <button class="btn btn-success" id="calculate-btn-bottom">Calculate CSFE</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-9">
        <div class="row">
          <div class="col-md-12 mb-4">
            <div class="card" id="overview">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">CSFE Overview</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="csfe-value-container text-center">
                      <h2 class="mb-0">CSFE Value</h2>
                      <div class="csfe-value" id="csfe-value">--</div>
                      <div class="csfe-performance">Performance Factor: <span id="performance-factor">--</span>x</div>
                      <div class="csfe-timestamp">Calculated at: <span id="calculation-timestamp">--</span></div>
                    </div>
                  </div>
                  <div class="col-md-8">
                    <canvas id="csfe-components-chart"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card" id="market-predictions">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Market Predictions</h5>
              </div>
              <div class="card-body">
                <div class="market-direction">
                  <h4>Market Direction: <span id="market-direction">--</span></h4>
                  <div class="progress mb-3">
                    <div class="progress-bar" id="market-strength-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                  </div>
                </div>
                <canvas id="asset-class-predictions-chart"></canvas>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="card" id="asset-allocation">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Asset Allocation</h5>
              </div>
              <div class="card-body">
                <h4>Risk Level: <span id="risk-level">--</span></h4>
                <canvas id="asset-allocation-chart"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="card" id="risk-assessment">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Risk Assessment</h5>
              </div>
              <div class="card-body">
                <h4>Overall Risk: <span id="overall-risk">--</span></h4>
                <div class="progress mb-3">
                  <div class="progress-bar bg-danger" id="overall-risk-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                </div>
                <div id="risk-factors-container">
                  <!-- Risk factors will be dynamically generated -->
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="card" id="timeline-predictions">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Timeline Predictions</h5>
              </div>
              <div class="card-body">
                <canvas id="timeline-predictions-chart"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 mb-4">
            <div class="card" id="depression-prediction">
              <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Depression Prediction (2027-2031)</h5>
              </div>
              <div class="card-body">
                <div class="alert alert-warning">
                  <strong>Note:</strong> Depression prediction module is under development. This feature will be available in the next release.
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <h4>Depression Probability</h4>
                    <div class="progress mb-3">
                      <div class="progress-bar bg-warning" id="depression-probability-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <h4>Timeline Probability</h4>
                    <canvas id="depression-timeline-chart"></canvas>
                  </div>
                  <div class="col-md-6">
                    <h4>Key Indicators</h4>
                    <ul class="list-group" id="depression-indicators">
                      <li class="list-group-item">Depression indicators will be displayed here</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer class="footer mt-auto py-3 bg-light">
    <div class="container text-center">
      <span class="text-muted">© 2025 NovaFuse | CSFE NovaVision Dashboard | Powered by NovaVision Universal UI Connector</span>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
  <script src="js/dashboard.js"></script>
</body>
</html>
