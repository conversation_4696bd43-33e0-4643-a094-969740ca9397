/**
 * ResonanceOptimizer.js
 * 
 * This module implements the Resonance Optimization System, which ensures that
 * all domains maintain optimal alignment with truth and natural order.
 * 
 * Resonance in Comphyology refers to the degree of alignment between a system's
 * state and the natural harmonic patterns that govern reality. Perfect resonance
 * (Cph = 0) represents complete alignment with truth and natural order.
 */

const { v4: uuidv4 } = require('uuid');
const { FiniteUniverse } = require('../core/FiniteUniverse');

/**
 * Resonance patterns based on the 3-6-9-12-13 pattern
 */
const RESONANCE_PATTERNS = {
  // Primary resonance anchors (3-6-9)
  PRIMARY: [3, 6, 9],
  
  // Secondary resonance anchors (12-13)
  SECONDARY: [12, 13],
  
  // Extended resonance anchors
  EXTENDED: [3, 6, 9, 12, 13, 18, 24, 27, 36, 39, 48, 52, 72, 78, 81, 91, 108, 144, 156, 216, 312, 396],
  
  // Harmonic multipliers
  HARMONIC_MULTIPLIERS: [1, 10, 100, 1000],
  
  // Get all resonance values
  getAllValues() {
    const values = new Set();
    
    // Add all primary and secondary values
    [...this.PRIMARY, ...this.SECONDARY].forEach(value => {
      // Add base value
      values.add(value);
      
      // Add harmonic multipliers
      this.HARMONIC_MULTIPLIERS.forEach(multiplier => {
        if (multiplier > 1) {
          values.add(value * multiplier);
        }
      });
    });
    
    return Array.from(values).sort((a, b) => a - b);
  },
  
  // Check if a value is resonant
  isResonant(value, tolerance = 0.01) {
    // Get all resonance values
    const resonanceValues = this.getAllValues();
    
    // Check if value is close to any resonance value
    return resonanceValues.some(resonanceValue => {
      return Math.abs(value - resonanceValue) / resonanceValue < tolerance;
    });
  },
  
  // Find the closest resonant value
  findClosestResonantValue(value) {
    // Get all resonance values
    const resonanceValues = this.getAllValues();
    
    // Find the closest resonance value
    let closestValue = resonanceValues[0];
    let minDistance = Math.abs(value - closestValue);
    
    for (let i = 1; i < resonanceValues.length; i++) {
      const distance = Math.abs(value - resonanceValues[i]);
      
      if (distance < minDistance) {
        minDistance = distance;
        closestValue = resonanceValues[i];
      }
    }
    
    return closestValue;
  }
};

/**
 * Resonance Optimizer
 * 
 * Ensures that all domains maintain optimal alignment with truth and natural order.
 */
class ResonanceOptimizer {
  /**
   * Create a new Resonance Optimizer
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      optimizationInterval: 1000, // 1 second
      resonanceThreshold: 0.7, // Minimum acceptable resonance
      healingRate: 0.05, // Rate at which resonance is improved
      logOptimization: true, // Whether to log optimization actions
      ...options
    };
    
    // Initialize optimizer state
    this.state = {
      optimizations: [],
      creationTime: Date.now(),
      lastOptimizationTime: Date.now(),
      running: false
    };
    
    if (this.options.logOptimization) {
      console.log('Resonance Optimizer initialized');
    }
  }
  
  /**
   * Start the optimizer
   */
  start() {
    if (this.state.running) {
      return;
    }
    
    this.state.running = true;
    
    // Start optimization interval
    this.optimizationInterval = setInterval(() => {
      this.optimize();
    }, this.options.optimizationInterval);
    
    if (this.options.logOptimization) {
      console.log('Resonance Optimizer started');
    }
  }
  
  /**
   * Stop the optimizer
   */
  stop() {
    if (!this.state.running) {
      return;
    }
    
    this.state.running = false;
    
    // Clear optimization interval
    clearInterval(this.optimizationInterval);
    
    if (this.options.logOptimization) {
      console.log('Resonance Optimizer stopped');
    }
  }
  
  /**
   * Optimize resonance across all domains
   * @param {ComphyologicalCosmos} cosmos - The cosmos to optimize
   */
  optimize(cosmos) {
    if (!cosmos) {
      return;
    }
    
    const now = Date.now();
    const deltaTime = (now - this.state.lastOptimizationTime) / 1000; // Convert to seconds
    
    // Get all domains
    const domains = [
      cosmos.getDomain('biological'),
      cosmos.getDomain('financial'),
      cosmos.getDomain('cyber')
    ];
    
    // Optimize each domain
    const optimizations = domains.map(domain => {
      if (!domain) {
        return null;
      }
      
      return this.optimizeDomain(domain, deltaTime);
    }).filter(Boolean);
    
    // Record optimizations
    optimizations.forEach(optimization => {
      this.state.optimizations.push(optimization);
    });
    
    // Limit optimizations history
    if (this.state.optimizations.length > 1000) {
      this.state.optimizations.shift();
    }
    
    // Update last optimization time
    this.state.lastOptimizationTime = now;
    
    return optimizations;
  }
  
  /**
   * Optimize resonance for a domain
   * @param {DomainUniverse} domain - The domain to optimize
   * @param {number} deltaTime - The time elapsed since the last optimization
   * @returns {Object} - The optimization result
   */
  optimizeDomain(domain, deltaTime) {
    // Get domain state
    const domainState = domain.getState();
    
    // Check if resonance is below threshold
    if (domainState.resonance >= this.options.resonanceThreshold) {
      return null; // No optimization needed
    }
    
    // Calculate resonance improvement
    const resonanceImprovement = Math.min(
      this.options.healingRate * deltaTime,
      this.options.resonanceThreshold - domainState.resonance
    );
    
    // Apply resonance improvement
    domain.state.resonance += resonanceImprovement;
    
    // Create optimization record
    const optimization = {
      id: uuidv4(),
      domainId: domain.id,
      domainName: domain.name,
      initialResonance: domainState.resonance,
      finalResonance: domain.state.resonance,
      improvement: resonanceImprovement,
      timestamp: Date.now()
    };
    
    // Log optimization
    if (this.options.logOptimization) {
      console.log(`Optimized resonance for ${domain.name} domain: ${optimization.initialResonance.toFixed(4)} -> ${optimization.finalResonance.toFixed(4)}`);
    }
    
    return optimization;
  }
  
  /**
   * Optimize a specific value to be resonant
   * @param {number} value - The value to optimize
   * @param {number} tolerance - The tolerance for resonance
   * @returns {number} - The optimized value
   */
  optimizeValue(value, tolerance = 0.01) {
    // Check if value is already resonant
    if (RESONANCE_PATTERNS.isResonant(value, tolerance)) {
      return value;
    }
    
    // Find the closest resonant value
    const resonantValue = RESONANCE_PATTERNS.findClosestResonantValue(value);
    
    // Log optimization
    if (this.options.logOptimization) {
      console.log(`Optimized value: ${value} -> ${resonantValue}`);
    }
    
    return resonantValue;
  }
  
  /**
   * Optimize an entity to be resonant
   * @param {Object} entity - The entity to optimize
   * @param {Array} resonantProperties - The properties to optimize
   * @param {number} tolerance - The tolerance for resonance
   * @returns {Object} - The optimized entity
   */
  optimizeEntity(entity, resonantProperties = [], tolerance = 0.01) {
    // Create a copy of the entity
    const optimizedEntity = { ...entity };
    
    // Optimize resonant properties
    resonantProperties.forEach(property => {
      if (typeof optimizedEntity[property] === 'number') {
        optimizedEntity[property] = this.optimizeValue(optimizedEntity[property], tolerance);
      }
    });
    
    // If entity has a resonance property, ensure it's high
    if (optimizedEntity.resonance !== undefined) {
      optimizedEntity.resonance = Math.max(optimizedEntity.resonance, this.options.resonanceThreshold);
    }
    
    return optimizedEntity;
  }
  
  /**
   * Get optimization history
   * @param {number} limit - Maximum number of optimizations to return
   * @returns {Array} - Array of optimizations
   */
  getOptimizationHistory(limit = 100) {
    return this.state.optimizations.slice(-limit);
  }
  
  /**
   * Get the resonance patterns
   * @returns {Object} - The resonance patterns
   */
  getResonancePatterns() {
    return RESONANCE_PATTERNS;
  }
}

module.exports = {
  ResonanceOptimizer,
  RESONANCE_PATTERNS
};
