<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyon Framework Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .summary-item {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
    }
    .total {
      background-color: #e6f2ff;
    }
    .passed {
      background-color: #e6ffe6;
    }
    .failed {
      background-color: #ffe6e6;
    }
    .skipped {
      background-color: #fff9e6;
    }
    .duration {
      background-color: #e6e6ff;
    }
    .pass-rate {
      background-color: #e6ffe6;
    }
    .suite {
      margin-bottom: 30px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .suite-header {
      background-color: #0066cc;
      color: white;
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .suite-stats {
      display: flex;
      gap: 15px;
    }
    .suite-body {
      padding: 0;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .status {
      font-weight: bold;
      padding: 5px 10px;
      border-radius: 3px;
      display: inline-block;
      min-width: 80px;
      text-align: center;
    }
    .status-passed {
      background-color: #dff0d8;
      color: #3c763d;
    }
    .status-failed {
      background-color: #f2dede;
      color: #a94442;
    }
    .status-skipped {
      background-color: #fcf8e3;
      color: #8a6d3b;
    }
    .error {
      color: #a94442;
      background-color: #f2dede;
      padding: 10px;
      border-radius: 3px;
      margin-top: 5px;
      white-space: pre-wrap;
      font-family: monospace;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #777;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Comphyon Framework Test Report</h1>
  <p>Generated on: 5/16/2025, 10:18:48 AM</p>
  
  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-grid">
      <div class="summary-item total">
        <h3>Total Tests</h3>
        <p>18</p>
      </div>
      <div class="summary-item passed">
        <h3>Passed</h3>
        <p>18</p>
      </div>
      <div class="summary-item failed">
        <h3>Failed</h3>
        <p>0</p>
      </div>
      <div class="summary-item skipped">
        <h3>Skipped</h3>
        <p>0</p>
      </div>
      <div class="summary-item duration">
        <h3>Duration</h3>
        <p>0.20s</p>
      </div>
      <div class="summary-item pass-rate">
        <h3>Pass Rate</h3>
        <p>100%</p>
      </div>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Integration Layer Tests</h3>
        <div class="suite-stats">
          <span>Passed: 9/9</span>
          <span>Failed: 0</span>
          <span>Skipped: 0</span>
          <span>Duration: 0.01s</span>
        </div>
      </div>
      <div class="suite-body">
        <table>
          <thead>
            <tr>
              <th>Test</th>
              <th>Status</th>
              <th>Duration</th>
            </tr>
          </thead>
          <tbody>
            
          </tbody>
        </table>
      </div>
    </div>
  
    <div class="suite">
      <div class="suite-header">
        <h3>Comphyon System Tests</h3>
        <div class="suite-stats">
          <span>Passed: 9/9</span>
          <span>Failed: 0</span>
          <span>Skipped: 0</span>
          <span>Duration: 0.19s</span>
        </div>
      </div>
      <div class="suite-body">
        <table>
          <thead>
            <tr>
              <th>Test</th>
              <th>Status</th>
              <th>Duration</th>
            </tr>
          </thead>
          <tbody>
            
          </tbody>
        </table>
      </div>
    </div>
  
  
  <footer>
    <p>Comphyon Framework - Powered by Comphyology</p>
  </footer>
</body>
</html>