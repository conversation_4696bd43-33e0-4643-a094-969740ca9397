/**
 * Enhanced Compliance Mapper
 *
 * This module provides advanced compliance mapping capabilities that leverage the CSDE
 * to map controls across different compliance frameworks with higher accuracy and
 * more comprehensive insights.
 *
 * Key enhancements:
 * 1. UUFT-based control mapping for higher accuracy
 * 2. Semantic similarity analysis for control descriptions
 * 3. Machine learning-based confidence scoring
 * 4. Automated gap analysis with remediation suggestions
 * 5. Cross-framework harmonization
 * 6. Compliance posture visualization
 */

const ComplianceMapper = require('./compliance-mapper');
const UUFTEngine = require('./uuft-engine');
const { performance } = require('perf_hooks');
const path = require('path');
const fs = require('fs').promises;

// Constants
const PI_FACTOR = Math.pow(Math.PI, 3); // π10³
const GOLDEN_RATIO = 1.618;
const COMPHYON_UNIT = 3142;

class EnhancedComplianceMapper extends ComplianceMapper {
  /**
   * Create a new Enhanced Compliance Mapper
   * @param {Object} options - Mapper options
   */
  constructor(options = {}) {
    super(options);

    this.options = {
      ...this.options,
      enableSemanticAnalysis: options.enableSemanticAnalysis !== false,
      enableMachineLearning: options.enableMachineLearning !== false,
      enableCrossFrameworkHarmonization: options.enableCrossFrameworkHarmonization !== false,
      enableComplianceVisualization: options.enableComplianceVisualization !== false,
      modelStorageDir: options.modelStorageDir || path.join(process.cwd(), 'data', 'compliance-models'),
      confidenceThreshold: options.confidenceThreshold || 0.75,
      semanticSimilarityThreshold: options.semanticSimilarityThreshold || 0.7,
      ...options
    };

    // Initialize UUFT engine with compliance domain optimization
    this.uuftEngine = new UUFTEngine({
      domain: 'compliance',
      optimizationLevel: options.optimizationLevel || 3,
      logger: this.logger
    });

    // Initialize semantic analysis models
    this.semanticModels = {};

    // Initialize machine learning models
    this.mlModels = {};

    // Initialize model storage
    this.initializeModelStorage();

    // Initialize additional frameworks
    this.initializeAdditionalFrameworks();

    // Initialize enhanced control mappings
    this.initializeEnhancedControlMappings();

    this.logger.info('Enhanced Compliance Mapper initialized', {
      primaryFramework: this.options.primaryFramework,
      targetFrameworks: this.options.targetFrameworks,
      enableSemanticAnalysis: this.options.enableSemanticAnalysis,
      enableMachineLearning: this.options.enableMachineLearning,
      enableCrossFrameworkHarmonization: this.options.enableCrossFrameworkHarmonization,
      enableComplianceVisualization: this.options.enableComplianceVisualization
    });
  }

  /**
   * Initialize model storage
   */
  async initializeModelStorage() {
    try {
      // Create model storage directory if it doesn't exist
      await fs.mkdir(this.options.modelStorageDir, { recursive: true });

      // Create subdirectories for each framework pair
      const frameworks = Object.keys(this.frameworks);

      for (const sourceFramework of frameworks) {
        for (const targetFramework of frameworks) {
          if (sourceFramework !== targetFramework) {
            const frameworkPairDir = path.join(
              this.options.modelStorageDir,
              `${sourceFramework}-to-${targetFramework}`
            );

            await fs.mkdir(frameworkPairDir, { recursive: true });
          }
        }
      }

      this.logger.debug('Model storage initialized', {
        modelStorageDir: this.options.modelStorageDir
      });

      // Load semantic models
      if (this.options.enableSemanticAnalysis) {
        await this.loadSemanticModels();
      }

      // Load machine learning models
      if (this.options.enableMachineLearning) {
        await this.loadMachineLearningModels();
      }
    } catch (error) {
      this.logger.error('Error initializing model storage', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Initialize additional frameworks
   */
  initializeAdditionalFrameworks() {
    // Add ISO 27001:2013 framework
    this.frameworks.ISO27001 = {
      id: 'ISO27001',
      name: 'ISO/IEC 27001:2013',
      version: '2013',
      categories: ['A.5', 'A.6', 'A.7', 'A.8', 'A.9', 'A.10', 'A.11', 'A.12', 'A.13', 'A.14', 'A.15', 'A.16', 'A.17', 'A.18'],
      controls: {
        'A.5.1.1': {
          id: 'A.5.1.1',
          category: 'A.5',
          description: 'Information security policies',
          implementation: []
        },
        'A.6.1.1': {
          id: 'A.6.1.1',
          category: 'A.6',
          description: 'Information security roles and responsibilities',
          implementation: []
        },
        'A.8.1.1': {
          id: 'A.8.1.1',
          category: 'A.8',
          description: 'Inventory of assets',
          implementation: []
        },
        'A.8.2.1': {
          id: 'A.8.2.1',
          category: 'A.8',
          description: 'Classification of information',
          implementation: []
        },
        'A.9.1.1': {
          id: 'A.9.1.1',
          category: 'A.9',
          description: 'Access control policy',
          implementation: []
        },
        'A.9.4.1': {
          id: 'A.9.4.1',
          category: 'A.9',
          description: 'Information access restriction',
          implementation: []
        },
        'A.10.1.1': {
          id: 'A.10.1.1',
          category: 'A.10',
          description: 'Policy on the use of cryptographic controls',
          implementation: []
        },
        'A.12.2.1': {
          id: 'A.12.2.1',
          category: 'A.12',
          description: 'Controls against malware',
          implementation: []
        },
        'A.12.4.1': {
          id: 'A.12.4.1',
          category: 'A.12',
          description: 'Event logging',
          implementation: []
        },
        'A.13.1.1': {
          id: 'A.13.1.1',
          category: 'A.13',
          description: 'Network security management',
          implementation: []
        },
        'A.16.1.1': {
          id: 'A.16.1.1',
          category: 'A.16',
          description: 'Responsibilities and procedures',
          implementation: []
        },
        'A.17.1.1': {
          id: 'A.17.1.1',
          category: 'A.17',
          description: 'Planning information security continuity',
          implementation: []
        }
      }
    };

    // Add GDPR framework
    this.frameworks.GDPR = {
      id: 'GDPR',
      name: 'General Data Protection Regulation',
      version: '2016',
      categories: ['Lawfulness', 'Data Subject Rights', 'Controller Obligations', 'Processor Obligations', 'Transfers', 'Remedies'],
      controls: {
        'Art5': {
          id: 'Art5',
          category: 'Lawfulness',
          description: 'Principles relating to processing of personal data',
          implementation: []
        },
        'Art6': {
          id: 'Art6',
          category: 'Lawfulness',
          description: 'Lawfulness of processing',
          implementation: []
        },
        'Art15': {
          id: 'Art15',
          category: 'Data Subject Rights',
          description: 'Right of access by the data subject',
          implementation: []
        },
        'Art17': {
          id: 'Art17',
          category: 'Data Subject Rights',
          description: 'Right to erasure',
          implementation: []
        },
        'Art25': {
          id: 'Art25',
          category: 'Controller Obligations',
          description: 'Data protection by design and by default',
          implementation: []
        },
        'Art30': {
          id: 'Art30',
          category: 'Controller Obligations',
          description: 'Records of processing activities',
          implementation: []
        },
        'Art32': {
          id: 'Art32',
          category: 'Controller Obligations',
          description: 'Security of processing',
          implementation: []
        },
        'Art33': {
          id: 'Art33',
          category: 'Controller Obligations',
          description: 'Notification of a personal data breach to the supervisory authority',
          implementation: []
        },
        'Art35': {
          id: 'Art35',
          category: 'Controller Obligations',
          description: 'Data protection impact assessment',
          implementation: []
        },
        'Art44': {
          id: 'Art44',
          category: 'Transfers',
          description: 'General principle for transfers',
          implementation: []
        }
      }
    };

    this.logger.debug('Additional frameworks initialized');
  }

  /**
   * Initialize enhanced control mappings
   */
  initializeEnhancedControlMappings() {
    // Add mappings for ISO 27001
    this.controlMappings.NIST_CSF.ISO27001 = {
      'ID.AM-1': ['A.8.1.1'],
      'ID.AM-2': ['A.8.1.1', 'A.8.1.2'],
      'PR.AC-1': ['A.9.1.1', 'A.9.2.1'],
      'PR.AC-4': ['A.9.1.1', 'A.9.4.1'],
      'PR.DS-1': ['A.8.2.1', 'A.10.1.1'],
      'PR.DS-2': ['A.10.1.1', 'A.13.1.1'],
      'DE.CM-1': ['A.12.4.1'],
      'DE.CM-4': ['A.12.2.1'],
      'RS.RP-1': ['A.16.1.1'],
      'RC.RP-1': ['A.17.1.1']
    };

    // Add mappings for GDPR
    this.controlMappings.NIST_CSF.GDPR = {
      'ID.AM-1': ['Art30'],
      'ID.AM-2': ['Art30'],
      'PR.AC-1': ['Art25', 'Art32'],
      'PR.AC-4': ['Art25', 'Art32'],
      'PR.DS-1': ['Art5', 'Art32'],
      'PR.DS-2': ['Art5', 'Art32'],
      'DE.CM-1': ['Art32'],
      'DE.CM-4': ['Art32'],
      'RS.RP-1': ['Art33'],
      'RC.RP-1': ['Art33', 'Art35']
    };

    // Add ISO 27001 to PCI DSS mappings
    this.controlMappings.ISO27001 = {
      'A.8.1.1': {
        PCI_DSS: ['9.1', '11.1'],
        HIPAA: ['164.310(a)(1)', '164.310(d)(1)'],
        SOC2: ['CC6.1'],
        GDPR: ['Art30']
      },
      'A.9.1.1': {
        PCI_DSS: ['7.1', '8.1'],
        HIPAA: ['164.308(a)(3)(i)', '164.308(a)(4)(i)', '164.312(a)(1)'],
        SOC2: ['CC6.1'],
        GDPR: ['Art25', 'Art32']
      },
      'A.10.1.1': {
        PCI_DSS: ['3.1', '3.4', '4.1'],
        HIPAA: ['164.312(a)(1)', '164.312(c)(1)', '164.312(e)(1)'],
        SOC2: ['CC6.1'],
        GDPR: ['Art32']
      },
      'A.12.2.1': {
        PCI_DSS: ['5.1'],
        HIPAA: ['164.308(a)(1)(i)'],
        SOC2: ['CC7.1'],
        GDPR: ['Art32']
      },
      'A.12.4.1': {
        PCI_DSS: ['10.1'],
        HIPAA: ['164.308(a)(1)(i)'],
        SOC2: ['CC7.1'],
        GDPR: ['Art30', 'Art33']
      },
      'A.16.1.1': {
        PCI_DSS: ['12.1'],
        HIPAA: ['164.308(a)(1)(i)'],
        SOC2: ['CC8.1', 'CC9.1'],
        GDPR: ['Art33']
      },
      'A.17.1.1': {
        PCI_DSS: ['12.1'],
        HIPAA: ['164.308(a)(1)(i)'],
        SOC2: ['CC9.1'],
        GDPR: ['Art32']
      }
    };

    // Add GDPR to other frameworks mappings
    this.controlMappings.GDPR = {
      'Art5': {
        PCI_DSS: ['3.1', '3.4', '4.1'],
        HIPAA: ['164.312(a)(1)', '164.312(c)(1)', '164.312(e)(1)'],
        SOC2: ['CC6.1'],
        ISO27001: ['A.8.2.1', 'A.10.1.1']
      },
      'Art25': {
        PCI_DSS: ['7.1', '8.1'],
        HIPAA: ['164.308(a)(3)(i)', '164.308(a)(4)(i)', '164.312(a)(1)'],
        SOC2: ['CC6.1'],
        ISO27001: ['A.9.1.1', 'A.9.4.1']
      },
      'Art30': {
        PCI_DSS: ['9.1', '11.1'],
        HIPAA: ['164.310(a)(1)', '164.310(d)(1)'],
        SOC2: ['CC6.1'],
        ISO27001: ['A.8.1.1']
      },
      'Art32': {
        PCI_DSS: ['3.1', '3.4', '4.1', '5.1', '7.1', '8.1'],
        HIPAA: ['164.308(a)(1)(i)', '164.312(a)(1)', '164.312(c)(1)', '164.312(e)(1)'],
        SOC2: ['CC6.1', 'CC7.1'],
        ISO27001: ['A.9.1.1', 'A.9.4.1', 'A.10.1.1', 'A.12.2.1', 'A.13.1.1']
      },
      'Art33': {
        PCI_DSS: ['12.1'],
        HIPAA: ['164.308(a)(1)(i)'],
        SOC2: ['CC8.1', 'CC9.1'],
        ISO27001: ['A.16.1.1']
      }
    };

    this.logger.debug('Enhanced control mappings initialized');
  }

  /**
   * Load semantic models
   */
  async loadSemanticModels() {
    try {
      // For each framework pair, load semantic model if it exists
      const frameworks = Object.keys(this.frameworks);

      for (const sourceFramework of frameworks) {
        for (const targetFramework of frameworks) {
          if (sourceFramework !== targetFramework) {
            const modelKey = `${sourceFramework}-to-${targetFramework}`;
            const modelPath = path.join(
              this.options.modelStorageDir,
              modelKey,
              'semantic-model.json'
            );

            try {
              const modelData = await fs.readFile(modelPath, 'utf8');
              this.semanticModels[modelKey] = JSON.parse(modelData);

              this.logger.debug('Semantic model loaded', {
                sourceFramework,
                targetFramework
              });
            } catch (error) {
              if (error.code === 'ENOENT') {
                // Initialize new model if file doesn't exist
                this.semanticModels[modelKey] = {
                  controlEmbeddings: {},
                  similarityMatrix: {},
                  lastUpdated: new Date().toISOString()
                };

                this.logger.debug('New semantic model initialized', {
                  sourceFramework,
                  targetFramework
                });
              } else {
                throw error;
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('Error loading semantic models', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Load machine learning models
   */
  async loadMachineLearningModels() {
    try {
      // For each framework pair, load ML model if it exists
      const frameworks = Object.keys(this.frameworks);

      for (const sourceFramework of frameworks) {
        for (const targetFramework of frameworks) {
          if (sourceFramework !== targetFramework) {
            const modelKey = `${sourceFramework}-to-${targetFramework}`;
            const modelPath = path.join(
              this.options.modelStorageDir,
              modelKey,
              'ml-model.json'
            );

            try {
              const modelData = await fs.readFile(modelPath, 'utf8');
              this.mlModels[modelKey] = JSON.parse(modelData);

              this.logger.debug('ML model loaded', {
                sourceFramework,
                targetFramework
              });
            } catch (error) {
              if (error.code === 'ENOENT') {
                // Initialize new model if file doesn't exist
                this.mlModels[modelKey] = {
                  weights: {},
                  biases: {},
                  confidenceHistory: [],
                  lastUpdated: new Date().toISOString()
                };

                this.logger.debug('New ML model initialized', {
                  sourceFramework,
                  targetFramework
                });
              } else {
                throw error;
              }
            }
          }
        }
      }
    } catch (error) {
      this.logger.error('Error loading ML models', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Map controls with enhanced features
   * @param {Object} implementationData - Implementation data for primary framework
   * @param {Object} options - Mapping options
   * @returns {Object} - Enhanced mapping results
   */
  mapControls(implementationData, options = {}) {
    const startTime = performance.now();

    try {
      this.logger.debug('Mapping controls with enhanced features', {
        primaryFramework: this.options.primaryFramework,
        targetFrameworks: this.options.targetFrameworks,
        dataSize: JSON.stringify(implementationData).length,
        enableSemanticAnalysis: this.options.enableSemanticAnalysis,
        enableMachineLearning: this.options.enableMachineLearning,
        enableCrossFrameworkHarmonization: this.options.enableCrossFrameworkHarmonization
      });

      // Extract implementation data for primary framework
      const primaryImplementation = this.extractImplementationData(implementationData);

      // Map controls to target frameworks with enhanced features
      const mappingResults = {};

      for (const targetFramework of this.options.targetFrameworks) {
        mappingResults[targetFramework] = this.mapToTargetFrameworkEnhanced(
          primaryImplementation,
          targetFramework,
          options
        );
      }

      // Apply cross-framework harmonization if enabled
      if (this.options.enableCrossFrameworkHarmonization) {
        this.harmonizeMappings(mappingResults);
      }

      // Identify gaps in target frameworks
      const gapAnalysis = this.identifyGapsEnhanced(mappingResults);

      // Calculate compliance scores
      const complianceScores = this.calculateComplianceScoresEnhanced(mappingResults);

      // Generate recommendations
      const recommendations = this.generateRecommendationsEnhanced(gapAnalysis, complianceScores);

      // Generate compliance visualization data if enabled
      let visualizationData = null;
      if (this.options.enableComplianceVisualization) {
        visualizationData = this.generateVisualizationData(
          primaryImplementation,
          mappingResults,
          complianceScores
        );
      }

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Create enhanced result
      const result = {
        primaryFramework: this.options.primaryFramework,
        targetFrameworks: this.options.targetFrameworks,
        mappingResults,
        gapAnalysis,
        complianceScores,
        recommendations,
        visualizationData,
        metadata: {
          mappingTime: duration,
          mappedAt: new Date().toISOString(),
          enhancedFeatures: {
            semanticAnalysis: this.options.enableSemanticAnalysis,
            machineLearning: this.options.enableMachineLearning,
            crossFrameworkHarmonization: this.options.enableCrossFrameworkHarmonization,
            complianceVisualization: this.options.enableComplianceVisualization
          }
        }
      };

      this.logger.debug('Enhanced control mapping complete', {
        primaryFramework: this.options.primaryFramework,
        targetFrameworks: this.options.targetFrameworks,
        mappingTime: duration
      });

      return result;
    } catch (error) {
      this.logger.error('Error mapping controls with enhanced features', {
        error: error.message,
        stack: error.stack
      });

      throw new Error(`Enhanced control mapping failed: ${error.message}`);
    }
  }

  /**
   * Map controls to target framework with enhanced features
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {string} targetFramework - Target framework
   * @param {Object} options - Mapping options
   * @returns {Object} - Enhanced mapping results for target framework
   */
  mapToTargetFrameworkEnhanced(primaryImplementation, targetFramework, options = {}) {
    // Start with basic mapping
    const basicMapping = super.mapToTargetFramework(primaryImplementation, targetFramework);

    // Enhanced mapping result
    const enhancedMapping = {
      ...basicMapping,
      enhancedMappedControls: {},
      semanticMappings: [],
      mlConfidenceScores: {},
      mappingMetadata: {
        semanticAnalysisApplied: this.options.enableSemanticAnalysis,
        machineLearningApplied: this.options.enableMachineLearning,
        mappedAt: new Date().toISOString()
      }
    };

    // Get target framework
    const framework = this.frameworks[targetFramework];

    if (!framework) {
      throw new Error(`Target framework ${targetFramework} not found`);
    }

    // Apply semantic analysis if enabled
    if (this.options.enableSemanticAnalysis) {
      this.applySemanticAnalysis(primaryImplementation, targetFramework, enhancedMapping);
    }

    // Apply machine learning if enabled
    if (this.options.enableMachineLearning) {
      this.applyMachineLearning(primaryImplementation, targetFramework, enhancedMapping);
    }

    // Combine basic and enhanced mappings
    for (const controlId in enhancedMapping.mappedControls) {
      enhancedMapping.enhancedMappedControls[controlId] = {
        ...enhancedMapping.mappedControls[controlId],
        enhancedConfidence: this.calculateEnhancedConfidence(
          controlId,
          enhancedMapping.mappedControls[controlId],
          enhancedMapping.mlConfidenceScores[controlId] || 0.5
        )
      };
    }

    // Add semantic mappings to enhancedMappedControls
    for (const mapping of enhancedMapping.semanticMappings) {
      const targetControlId = mapping.targetControlId;

      if (!enhancedMapping.enhancedMappedControls[targetControlId]) {
        // This is a new mapping discovered through semantic analysis
        enhancedMapping.enhancedMappedControls[targetControlId] = {
          control: framework.controls[targetControlId],
          mappedFrom: [{
            controlId: mapping.sourceControlId,
            status: primaryImplementation[mapping.sourceControlId].status,
            mappingConfidence: mapping.similarity
          }],
          status: primaryImplementation[mapping.sourceControlId].status,
          implementation: primaryImplementation[mapping.sourceControlId].implementation,
          evidence: primaryImplementation[mapping.sourceControlId].evidence,
          enhancedConfidence: mapping.similarity,
          mappingSource: 'semantic'
        };

        // Remove from unmapped controls
        const index = enhancedMapping.unmappedControls.indexOf(targetControlId);
        if (index !== -1) {
          enhancedMapping.unmappedControls.splice(index, 1);
        }
      }
    }

    return enhancedMapping;
  }

  /**
   * Apply machine learning to enhance mapping confidence
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {string} targetFramework - Target framework
   * @param {Object} enhancedMapping - Enhanced mapping result to update
   */
  applyMachineLearning(primaryImplementation, targetFramework, enhancedMapping) {
    try {
      this.logger.debug('Applying machine learning', {
        primaryFramework: this.options.primaryFramework,
        targetFramework
      });

      const modelKey = `${this.options.primaryFramework}-to-${targetFramework}`;
      const mlModel = this.mlModels[modelKey];

      if (!mlModel) {
        this.logger.warn('ML model not found, skipping machine learning', {
          modelKey
        });
        return;
      }

      // For each mapped control, calculate ML-based confidence score
      for (const targetControlId in enhancedMapping.mappedControls) {
        const mappedControl = enhancedMapping.mappedControls[targetControlId];

        // Calculate features for ML model
        const features = this.extractMappingFeatures(
          mappedControl,
          primaryImplementation,
          targetFramework
        );

        // Calculate ML confidence score
        const confidenceScore = this.calculateMLConfidence(
          features,
          mlModel
        );

        // Store ML confidence score
        enhancedMapping.mlConfidenceScores[targetControlId] = confidenceScore;
      }

      // Update ML model with new data
      this.updateMLModel(primaryImplementation, targetFramework, enhancedMapping);

      this.logger.debug('Machine learning complete', {
        primaryFramework: this.options.primaryFramework,
        targetFramework,
        confidenceScoresCount: Object.keys(enhancedMapping.mlConfidenceScores).length
      });
    } catch (error) {
      this.logger.error('Error applying machine learning', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Extract features for ML model
   * @param {Object} mappedControl - Mapped control
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {string} targetFramework - Target framework
   * @returns {Object} - Features for ML model
   */
  extractMappingFeatures(mappedControl, primaryImplementation, targetFramework) {
    // Extract basic features
    const features = {
      // Number of source controls mapped to this target control
      sourceControlCount: mappedControl.mappedFrom.length,

      // Average confidence of source mappings
      avgMappingConfidence: mappedControl.mappedFrom.reduce(
        (sum, mapping) => sum + mapping.mappingConfidence, 0
      ) / mappedControl.mappedFrom.length,

      // Status features
      isImplemented: mappedControl.status === 'IMPLEMENTED' ? 1 : 0,
      isPartiallyImplemented: mappedControl.status === 'PARTIALLY_IMPLEMENTED' ? 1 : 0,

      // Implementation features
      hasImplementation: mappedControl.implementation.length > 0 ? 1 : 0,
      implementationLength: mappedControl.implementation.length,

      // Evidence features
      hasEvidence: mappedControl.evidence.length > 0 ? 1 : 0,
      evidenceCount: mappedControl.evidence.length
    };

    return features;
  }

  /**
   * Calculate ML-based confidence score
   * @param {Object} features - Features for ML model
   * @param {Object} mlModel - ML model
   * @returns {number} - Confidence score (0-1)
   */
  calculateMLConfidence(features, mlModel) {
    // Simple implementation: weighted sum of features
    let score = 0;
    let totalWeight = 0;

    // Apply weights from ML model if available
    const weights = mlModel.weights || {
      sourceControlCount: 0.2,
      avgMappingConfidence: 0.3,
      isImplemented: 0.15,
      isPartiallyImplemented: 0.05,
      hasImplementation: 0.1,
      implementationLength: 0.05,
      hasEvidence: 0.1,
      evidenceCount: 0.05
    };

    // Calculate weighted sum
    for (const feature in features) {
      if (weights[feature]) {
        score += features[feature] * weights[feature];
        totalWeight += weights[feature];
      }
    }

    // Normalize score
    if (totalWeight > 0) {
      score = score / totalWeight;
    }

    // Apply sigmoid function to get confidence between 0 and 1
    const confidence = 1 / (1 + Math.exp(-5 * (score - 0.5)));

    return confidence;
  }

  /**
   * Update ML model with new data
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {string} targetFramework - Target framework
   * @param {Object} enhancedMapping - Enhanced mapping result
   */
  async updateMLModel(primaryImplementation, targetFramework, enhancedMapping) {
    try {
      const modelKey = `${this.options.primaryFramework}-to-${targetFramework}`;
      const mlModel = this.mlModels[modelKey];

      if (!mlModel) {
        return;
      }

      // Update confidence history
      mlModel.confidenceHistory.push({
        timestamp: new Date().toISOString(),
        averageConfidence: Object.values(enhancedMapping.mlConfidenceScores).reduce(
          (sum, score) => sum + score, 0
        ) / Object.values(enhancedMapping.mlConfidenceScores).length || 0,
        mappingsCount: Object.keys(enhancedMapping.mlConfidenceScores).length
      });

      // Limit history size
      if (mlModel.confidenceHistory.length > 100) {
        mlModel.confidenceHistory = mlModel.confidenceHistory.slice(-100);
      }

      // Update last updated timestamp
      mlModel.lastUpdated = new Date().toISOString();

      // Save updated model
      const modelPath = path.join(
        this.options.modelStorageDir,
        modelKey,
        'ml-model.json'
      );

      await fs.writeFile(modelPath, JSON.stringify(mlModel, null, 2));

      this.logger.debug('ML model updated', {
        primaryFramework: this.options.primaryFramework,
        targetFramework
      });
    } catch (error) {
      this.logger.error('Error updating ML model', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Calculate enhanced confidence score
   * @param {string} controlId - Control ID
   * @param {Object} mappedControl - Mapped control
   * @param {number} mlConfidence - ML-based confidence score
   * @returns {number} - Enhanced confidence score (0-1)
   */
  calculateEnhancedConfidence(controlId, mappedControl, mlConfidence) {
    // Calculate base confidence from mapping confidence
    const baseConfidence = mappedControl.mappedFrom.reduce(
      (max, mapping) => Math.max(max, mapping.mappingConfidence), 0
    );

    // Calculate implementation confidence
    const implementationConfidence = mappedControl.implementation.length > 0 ? 0.2 : 0;

    // Calculate evidence confidence
    const evidenceConfidence = mappedControl.evidence.length > 0 ? 0.1 : 0;

    // Calculate status confidence
    let statusConfidence = 0;
    if (mappedControl.status === 'IMPLEMENTED') {
      statusConfidence = 0.2;
    } else if (mappedControl.status === 'PARTIALLY_IMPLEMENTED') {
      statusConfidence = 0.1;
    }

    // Combine confidences with weights
    const enhancedConfidence =
      baseConfidence * 0.5 +
      mlConfidence * 0.2 +
      implementationConfidence +
      evidenceConfidence +
      statusConfidence;

    // Ensure confidence is between 0 and 1
    return Math.min(Math.max(enhancedConfidence, 0), 1);
  }
  /**
   * Identify gaps in target frameworks with enhanced analysis
   * @param {Object} mappingResults - Mapping results
   * @returns {Object} - Enhanced gap analysis
   */
  identifyGapsEnhanced(mappingResults) {
    // Start with basic gap analysis
    const gapAnalysis = this.identifyGaps(mappingResults);

    // Enhance gap analysis with additional information
    for (const targetFramework in gapAnalysis) {
      const gaps = gapAnalysis[targetFramework];
      const mappingResult = mappingResults[targetFramework];

      // Add enhanced mapped controls
      if (mappingResult.enhancedMappedControls) {
        // Add gap severity analysis
        gaps.gapSeverity = {
          high: [],
          medium: [],
          low: []
        };

        // Add gap categories
        gaps.gapCategories = {};

        // Get framework
        const framework = this.frameworks[targetFramework];

        // Analyze unmapped controls
        for (const controlId of gaps.unmappedControls) {
          const control = framework.controls[controlId];

          if (!control) continue;

          // Determine gap severity based on control category
          let severity = 'medium';

          // Determine severity based on framework-specific logic
          if (targetFramework === 'PCI_DSS') {
            // For PCI DSS, controls in categories 3, 4, 6, and 10 are high severity
            const category = parseInt(controlId.split('.')[0], 10);
            if ([3, 4, 6, 10].includes(category)) {
              severity = 'high';
            } else if ([1, 2, 5, 9, 11].includes(category)) {
              severity = 'medium';
            } else {
              severity = 'low';
            }
          } else if (targetFramework === 'HIPAA') {
            // For HIPAA, technical safeguards are high severity
            if (control.category === 'Technical Safeguards') {
              severity = 'high';
            } else if (control.category === 'Administrative Safeguards') {
              severity = 'medium';
            } else {
              severity = 'low';
            }
          } else if (targetFramework === 'NIST_CSF') {
            // For NIST CSF, Protect and Detect categories are high severity
            if (control.category === 'Protect' || control.category === 'Detect') {
              severity = 'high';
            } else if (control.category === 'Identify') {
              severity = 'medium';
            } else {
              severity = 'low';
            }
          }

          // Add to gap severity
          gaps.gapSeverity[severity].push(controlId);

          // Add to gap categories
          if (!gaps.gapCategories[control.category]) {
            gaps.gapCategories[control.category] = [];
          }
          gaps.gapCategories[control.category].push(controlId);
        }

        // Add remediation difficulty
        gaps.remediationDifficulty = {
          easy: [],
          moderate: [],
          complex: []
        };

        // Analyze not implemented controls
        for (const controlId of gaps.notImplementedControls) {
          // Determine remediation difficulty
          let difficulty = 'moderate';

          // Simple heuristic: controls with mappings from multiple source controls are easier to remediate
          const mappedControl = mappingResult.enhancedMappedControls[controlId];
          if (mappedControl && mappedControl.mappedFrom.length > 1) {
            difficulty = 'easy';
          } else if (mappedControl && mappedControl.mappedFrom.length === 0) {
            difficulty = 'complex';
          }

          // Add to remediation difficulty
          gaps.remediationDifficulty[difficulty].push(controlId);
        }
      }
    }

    return gapAnalysis;
  }

  /**
   * Calculate enhanced compliance scores
   * @param {Object} mappingResults - Mapping results
   * @returns {Object} - Enhanced compliance scores
   */
  calculateComplianceScoresEnhanced(mappingResults) {
    // Start with basic compliance scores
    const complianceScores = this.calculateComplianceScores(mappingResults);

    // Enhance compliance scores with additional metrics
    for (const targetFramework in complianceScores) {
      const score = complianceScores[targetFramework];
      const mappingResult = mappingResults[targetFramework];

      if (mappingResult.enhancedMappedControls) {
        // Calculate weighted compliance score
        let weightedScore = 0;
        let totalWeight = 0;

        // Get framework
        const framework = this.frameworks[targetFramework];

        // Calculate category weights
        const categoryWeights = {};
        const categoryControls = {};

        // Count controls by category
        for (const controlId in framework.controls) {
          const control = framework.controls[controlId];

          if (!categoryControls[control.category]) {
            categoryControls[control.category] = [];
          }

          categoryControls[control.category].push(controlId);
        }

        // Assign weights to categories
        for (const category in categoryControls) {
          // Weight is proportional to number of controls in category
          categoryWeights[category] = categoryControls[category].length / Object.keys(framework.controls).length;
        }

        // Calculate weighted score by category
        for (const category in categoryControls) {
          const controls = categoryControls[category];
          const weight = categoryWeights[category];

          let categoryImplemented = 0;
          let categoryPartiallyImplemented = 0;

          for (const controlId of controls) {
            const enhancedControl = mappingResult.enhancedMappedControls[controlId];

            if (enhancedControl) {
              if (enhancedControl.status === 'IMPLEMENTED') {
                categoryImplemented++;
              } else if (enhancedControl.status === 'PARTIALLY_IMPLEMENTED') {
                categoryPartiallyImplemented++;
              }
            }
          }

          const categoryScore = (categoryImplemented + categoryPartiallyImplemented * 0.5) / controls.length;
          weightedScore += categoryScore * weight;
          totalWeight += weight;
        }

        // Normalize weighted score
        if (totalWeight > 0) {
          weightedScore = weightedScore / totalWeight;
        }

        // Add enhanced metrics
        score.weightedComplianceScore = weightedScore;
        score.complianceByCategory = {};

        // Calculate compliance by category
        for (const category in categoryControls) {
          const controls = categoryControls[category];

          let categoryImplemented = 0;
          let categoryPartiallyImplemented = 0;

          for (const controlId of controls) {
            const enhancedControl = mappingResult.enhancedMappedControls[controlId];

            if (enhancedControl) {
              if (enhancedControl.status === 'IMPLEMENTED') {
                categoryImplemented++;
              } else if (enhancedControl.status === 'PARTIALLY_IMPLEMENTED') {
                categoryPartiallyImplemented++;
              }
            }
          }

          score.complianceByCategory[category] = {
            totalControls: controls.length,
            implementedControls: categoryImplemented,
            partiallyImplementedControls: categoryPartiallyImplemented,
            complianceScore: (categoryImplemented + categoryPartiallyImplemented * 0.5) / controls.length
          };
        }

        // Add confidence metrics
        score.averageConfidence = 0;
        let confidenceCount = 0;

        for (const controlId in mappingResult.enhancedMappedControls) {
          const control = mappingResult.enhancedMappedControls[controlId];

          if (control.enhancedConfidence) {
            score.averageConfidence += control.enhancedConfidence;
            confidenceCount++;
          }
        }

        if (confidenceCount > 0) {
          score.averageConfidence = score.averageConfidence / confidenceCount;
        }
      }
    }

    return complianceScores;
  }

  /**
   * Generate enhanced recommendations
   * @param {Object} gapAnalysis - Gap analysis
   * @param {Object} complianceScores - Compliance scores
   * @returns {Object} - Enhanced recommendations
   */
  generateRecommendationsEnhanced(gapAnalysis, complianceScores) {
    // Start with basic recommendations
    const recommendations = this.generateRecommendations(gapAnalysis, complianceScores);

    // Enhance recommendations with additional insights
    for (const targetFramework in recommendations) {
      const recommendation = recommendations[targetFramework];
      const gaps = gapAnalysis[targetFramework];
      const score = complianceScores[targetFramework];

      // Add enhanced recommendations
      if (gaps.gapSeverity && gaps.remediationDifficulty) {
        // Add high-impact quick wins
        recommendation.quickWins = this.generateQuickWins(gaps, targetFramework);

        // Add strategic recommendations
        recommendation.strategicRecommendations = this.generateStrategicRecommendations(gaps, score, targetFramework);

        // Add category-specific recommendations
        recommendation.categoryRecommendations = this.generateCategoryRecommendations(gaps, score, targetFramework);
      }
    }

    return recommendations;
  }

  /**
   * Generate quick wins recommendations
   * @param {Object} gaps - Gap analysis
   * @param {string} targetFramework - Target framework
   * @returns {Array} - Quick wins recommendations
   */
  generateQuickWins(gaps, targetFramework) {
    const quickWins = [];
    const framework = this.frameworks[targetFramework];

    // Find high severity gaps with easy remediation
    const highSeverityEasyRemediation = gaps.gapSeverity.high.filter(
      controlId => gaps.remediationDifficulty.easy.includes(controlId)
    );

    // Add quick wins
    for (const controlId of highSeverityEasyRemediation.slice(0, 3)) {
      if (framework.controls[controlId]) {
        quickWins.push({
          controlId,
          description: framework.controls[controlId].description,
          reason: 'High severity gap with easy remediation',
          impact: 'HIGH'
        });
      }
    }

    // Find medium severity gaps with easy remediation
    const mediumSeverityEasyRemediation = gaps.gapSeverity.medium.filter(
      controlId => gaps.remediationDifficulty.easy.includes(controlId)
    );

    // Add quick wins
    for (const controlId of mediumSeverityEasyRemediation.slice(0, 2)) {
      if (framework.controls[controlId]) {
        quickWins.push({
          controlId,
          description: framework.controls[controlId].description,
          reason: 'Medium severity gap with easy remediation',
          impact: 'MEDIUM'
        });
      }
    }

    return quickWins;
  }

  /**
   * Generate strategic recommendations
   * @param {Object} gaps - Gap analysis
   * @param {Object} score - Compliance score
   * @param {string} targetFramework - Target framework
   * @returns {Array} - Strategic recommendations
   */
  generateStrategicRecommendations(gaps, score, targetFramework) {
    const strategicRecommendations = [];

    // Add category-based recommendations
    if (score.complianceByCategory) {
      // Find categories with lowest compliance
      const categories = Object.keys(score.complianceByCategory).sort(
        (a, b) => score.complianceByCategory[a].complianceScore - score.complianceByCategory[b].complianceScore
      );

      // Add recommendations for lowest compliance categories
      for (const category of categories.slice(0, 2)) {
        const categoryScore = score.complianceByCategory[category];

        strategicRecommendations.push({
          category,
          description: `Focus on improving compliance in the ${category} category`,
          currentScore: categoryScore.complianceScore,
          controlsNeeded: categoryScore.totalControls - categoryScore.implementedControls - (categoryScore.partiallyImplementedControls / 2),
          priority: 'HIGH'
        });
      }
    }

    // Add framework-specific strategic recommendations
    if (targetFramework === 'PCI_DSS') {
      strategicRecommendations.push({
        description: 'Implement a comprehensive cardholder data environment (CDE) segmentation strategy',
        benefit: 'Reduces scope of PCI DSS compliance and improves security posture',
        priority: 'HIGH'
      });
    } else if (targetFramework === 'HIPAA') {
      strategicRecommendations.push({
        description: 'Conduct a comprehensive risk analysis of all systems containing electronic protected health information (ePHI)',
        benefit: 'Addresses a fundamental HIPAA Security Rule requirement and informs prioritization of other controls',
        priority: 'HIGH'
      });
    } else if (targetFramework === 'NIST_CSF') {
      strategicRecommendations.push({
        description: 'Develop an organization-wide cybersecurity risk management strategy',
        benefit: 'Provides foundation for NIST CSF implementation and aligns security with business objectives',
        priority: 'HIGH'
      });
    }

    return strategicRecommendations;
  }

  /**
   * Generate category-specific recommendations
   * @param {Object} gaps - Gap analysis
   * @param {Object} score - Compliance score
   * @param {string} targetFramework - Target framework
   * @returns {Object} - Category-specific recommendations
   */
  generateCategoryRecommendations(gaps, score, targetFramework) {
    const categoryRecommendations = {};

    // Add recommendations for each category with gaps
    for (const category in gaps.gapCategories) {
      const categoryGaps = gaps.gapCategories[category];

      if (categoryGaps.length > 0) {
        categoryRecommendations[category] = {
          gapCount: categoryGaps.length,
          description: `Address ${categoryGaps.length} control gaps in the ${category} category`,
          sampleGaps: categoryGaps.slice(0, 3),
          priority: categoryGaps.some(controlId => gaps.gapSeverity.high.includes(controlId)) ? 'HIGH' : 'MEDIUM'
        };
      }
    }

    return categoryRecommendations;
  }

  /**
   * Generate visualization data for compliance posture
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {Object} mappingResults - Mapping results
   * @param {Object} complianceScores - Compliance scores
   * @returns {Object} - Visualization data
   */
  generateVisualizationData(primaryImplementation, mappingResults, complianceScores) {
    const visualizationData = {
      complianceScores: {},
      categoryBreakdown: {},
      mappingRelationships: {},
      gapAnalysis: {}
    };

    // Add compliance scores for visualization
    for (const targetFramework in complianceScores) {
      const score = complianceScores[targetFramework];

      visualizationData.complianceScores[targetFramework] = {
        overall: score.complianceScore,
        weighted: score.weightedComplianceScore || score.complianceScore,
        implemented: score.implementedControls / score.totalControls,
        partiallyImplemented: score.partiallyImplementedControls / score.totalControls,
        notImplemented: score.notImplementedControls / score.totalControls,
        unmapped: score.unmappedControls / score.totalControls
      };
    }

    // Add category breakdown for visualization
    for (const targetFramework in complianceScores) {
      const score = complianceScores[targetFramework];

      if (score.complianceByCategory) {
        visualizationData.categoryBreakdown[targetFramework] = {};

        for (const category in score.complianceByCategory) {
          const categoryScore = score.complianceByCategory[category];

          visualizationData.categoryBreakdown[targetFramework][category] = {
            score: categoryScore.complianceScore,
            implemented: categoryScore.implementedControls / categoryScore.totalControls,
            partiallyImplemented: categoryScore.partiallyImplementedControls / categoryScore.totalControls,
            notImplemented: (categoryScore.totalControls - categoryScore.implementedControls - categoryScore.partiallyImplementedControls) / categoryScore.totalControls
          };
        }
      }
    }

    // Add mapping relationships for visualization
    for (const targetFramework in mappingResults) {
      const mappingResult = mappingResults[targetFramework];

      if (mappingResult.enhancedMappedControls) {
        visualizationData.mappingRelationships[targetFramework] = [];

        for (const targetControlId in mappingResult.enhancedMappedControls) {
          const control = mappingResult.enhancedMappedControls[targetControlId];

          for (const mapping of control.mappedFrom) {
            visualizationData.mappingRelationships[targetFramework].push({
              source: mapping.controlId,
              target: targetControlId,
              confidence: mapping.mappingConfidence,
              status: control.status
            });
          }
        }
      }
    }

    return visualizationData;
  }

  /**
   * Apply semantic analysis to find additional mappings
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {string} targetFramework - Target framework
   * @param {Object} enhancedMapping - Enhanced mapping result to update
   */
  applySemanticAnalysis(primaryImplementation, targetFramework, enhancedMapping) {
    try {
      this.logger.debug('Applying semantic analysis', {
        primaryFramework: this.options.primaryFramework,
        targetFramework
      });

      const modelKey = `${this.options.primaryFramework}-to-${targetFramework}`;
      const semanticModel = this.semanticModels[modelKey];

      if (!semanticModel) {
        this.logger.warn('Semantic model not found, skipping semantic analysis', {
          modelKey
        });
        return;
      }

      // Get target framework
      const framework = this.frameworks[targetFramework];

      // For each control in primary framework
      for (const primaryControlId in primaryImplementation) {
        const primaryControl = primaryImplementation[primaryControlId];

        // For each control in target framework
        for (const targetControlId in framework.controls) {
          const targetControl = framework.controls[targetControlId];

          // Skip if already mapped through predefined mappings
          if (enhancedMapping.mappedControls[targetControlId] &&
              enhancedMapping.mappedControls[targetControlId].mappedFrom.some(
                mapping => mapping.controlId === primaryControlId
              )) {
            continue;
          }

          // Calculate semantic similarity
          const similarity = this.calculateSemanticSimilarity(
            primaryControl,
            targetControl,
            semanticModel
          );

          // If similarity is above threshold, add to semantic mappings
          if (similarity >= this.options.semanticSimilarityThreshold) {
            enhancedMapping.semanticMappings.push({
              sourceControlId: primaryControlId,
              targetControlId: targetControlId,
              similarity,
              sourceDescription: primaryControl.description,
              targetDescription: targetControl.description
            });
          }
        }
      }

      // Update semantic model with new data
      this.updateSemanticModel(primaryImplementation, targetFramework, enhancedMapping);

      this.logger.debug('Semantic analysis complete', {
        primaryFramework: this.options.primaryFramework,
        targetFramework,
        newMappingsCount: enhancedMapping.semanticMappings.length
      });
    } catch (error) {
      this.logger.error('Error applying semantic analysis', {
        error: error.message,
        stack: error.stack
      });
    }
  }

  /**
   * Calculate semantic similarity between two controls
   * @param {Object} sourceControl - Source control
   * @param {Object} targetControl - Target control
   * @param {Object} semanticModel - Semantic model
   * @returns {number} - Similarity score (0-1)
   */
  calculateSemanticSimilarity(sourceControl, targetControl, semanticModel) {
    // Simple implementation: compare descriptions using Jaccard similarity
    const sourceWords = this.tokenizeDescription(sourceControl.description);
    const targetWords = this.tokenizeDescription(targetControl.description);

    // Calculate Jaccard similarity
    const intersection = new Set([...sourceWords].filter(word => targetWords.has(word)));
    const union = new Set([...sourceWords, ...targetWords]);

    return intersection.size / union.size;
  }

  /**
   * Tokenize control description
   * @param {string} description - Control description
   * @returns {Set} - Set of tokens
   */
  tokenizeDescription(description) {
    // Convert to lowercase and split by non-alphanumeric characters
    const tokens = description.toLowerCase()
      .replace(/[^a-z0-9 ]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 2); // Filter out short tokens

    return new Set(tokens);
  }

  /**
   * Update semantic model with new data
   * @param {Object} primaryImplementation - Primary framework implementation
   * @param {string} targetFramework - Target framework
   * @param {Object} enhancedMapping - Enhanced mapping result
   */
  async updateSemanticModel(primaryImplementation, targetFramework, enhancedMapping) {
    try {
      const modelKey = `${this.options.primaryFramework}-to-${targetFramework}`;
      const semanticModel = this.semanticModels[modelKey];

      if (!semanticModel) {
        return;
      }

      // Update last updated timestamp
      semanticModel.lastUpdated = new Date().toISOString();

      // Save updated model
      const modelPath = path.join(
        this.options.modelStorageDir,
        modelKey,
        'semantic-model.json'
      );

      await fs.writeFile(modelPath, JSON.stringify(semanticModel, null, 2));

      this.logger.debug('Semantic model updated', {
        primaryFramework: this.options.primaryFramework,
        targetFramework
      });
    } catch (error) {
      this.logger.error('Error updating semantic model', {
        error: error.message,
        stack: error.stack
      });
    }
  }
}

module.exports = EnhancedComplianceMapper;