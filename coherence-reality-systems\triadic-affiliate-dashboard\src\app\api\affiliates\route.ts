import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs'
import { prisma } from '@/lib/prisma'

export async function GET(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const affiliates = await prisma.affiliate.findMany({
      where: {
        userId: userId
      },
      include: {
        metrics: true,
        products: true
      }
    })

    return NextResponse.json(affiliates)
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch affiliates' }, { status: 500 })
  }
}

export async function POST(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const body = await req.json()
    const affiliate = await prisma.affiliate.create({
      data: {
        userId,
        ...body
      }
    })

    return NextResponse.json(affiliate)
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create affiliate' }, { status: 500 })
  }
}
