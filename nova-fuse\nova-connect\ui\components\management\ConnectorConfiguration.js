/**
 * Connector Configuration Component
 * 
 * This component displays and allows editing of connector configuration.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  Tab, 
  Tabs, 
  TextField, 
  Typography 
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

const ConnectorConfiguration = ({ connector }) => {
  const [activeTab, setActiveTab] = useState('general');
  const [config, setConfig] = useState({
    general: {
      baseUrl: connector.baseUrl || '',
      timeout: connector.configuration?.timeout || 30000,
      retryCount: connector.configuration?.retryPolicy?.maxRetries || 3,
      retryStrategy: connector.configuration?.retryPolicy?.backoffStrategy || 'exponential'
    },
    authentication: {
      type: connector.authentication?.type || 'API_KEY',
      fields: connector.authentication?.fields || {}
    },
    headers: connector.configuration?.headers || {},
    advanced: {
      rateLimit: connector.configuration?.rateLimit || 10,
      cacheEnabled: connector.configuration?.cacheEnabled || false,
      cacheExpiration: connector.configuration?.cacheExpiration || 300
    }
  });
  
  const [newHeaderKey, setNewHeaderKey] = useState('');
  const [newHeaderValue, setNewHeaderValue] = useState('');
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const handleConfigChange = (section, field, value) => {
    setConfig({
      ...config,
      [section]: {
        ...config[section],
        [field]: value
      }
    });
  };
  
  const handleHeaderChange = (key, value) => {
    setConfig({
      ...config,
      headers: {
        ...config.headers,
        [key]: value
      }
    });
  };
  
  const handleAddHeader = () => {
    if (newHeaderKey.trim() === '') return;
    
    setConfig({
      ...config,
      headers: {
        ...config.headers,
        [newHeaderKey]: newHeaderValue
      }
    });
    
    setNewHeaderKey('');
    setNewHeaderValue('');
  };
  
  const handleRemoveHeader = (key) => {
    const newHeaders = { ...config.headers };
    delete newHeaders[key];
    
    setConfig({
      ...config,
      headers: newHeaders
    });
  };
  
  const handleSaveConfig = () => {
    // In a real implementation, this would save the configuration to the server
    alert('Configuration saved!');
  };
  
  const renderConfigAsJson = () => {
    const configJson = {
      baseUrl: config.general.baseUrl,
      authentication: {
        type: config.authentication.type,
        fields: config.authentication.fields
      },
      configuration: {
        timeout: config.general.timeout,
        headers: config.headers,
        retryPolicy: {
          maxRetries: config.general.retryCount,
          backoffStrategy: config.general.retryStrategy
        },
        rateLimit: config.advanced.rateLimit,
        cacheEnabled: config.advanced.cacheEnabled,
        cacheExpiration: config.advanced.cacheExpiration
      }
    };
    
    return JSON.stringify(configJson, null, 2);
  };
  
  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="configuration tabs">
          <Tab label="General" value="general" />
          <Tab label="Authentication" value="authentication" />
          <Tab label="Headers" value="headers" />
          <Tab label="Advanced" value="advanced" />
          <Tab label="JSON" value="json" />
        </Tabs>
      </Box>
      
      {activeTab === 'general' && (
        <Card variant="outlined">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              General Configuration
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Base URL"
                  value={config.general.baseUrl}
                  onChange={(e) => handleConfigChange('general', 'baseUrl', e.target.value)}
                  helperText="The base URL for all API requests"
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Timeout (ms)"
                  type="number"
                  value={config.general.timeout}
                  onChange={(e) => handleConfigChange('general', 'timeout', parseInt(e.target.value) || 30000)}
                  helperText="Request timeout in milliseconds"
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Retry Count"
                  type="number"
                  value={config.general.retryCount}
                  onChange={(e) => handleConfigChange('general', 'retryCount', parseInt(e.target.value) || 3)}
                  helperText="Number of retry attempts for failed requests"
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="retry-strategy-label">Retry Strategy</InputLabel>
                  <Select
                    labelId="retry-strategy-label"
                    value={config.general.retryStrategy}
                    label="Retry Strategy"
                    onChange={(e) => handleConfigChange('general', 'retryStrategy', e.target.value)}
                  >
                    <MenuItem value="exponential">Exponential Backoff</MenuItem>
                    <MenuItem value="linear">Linear Backoff</MenuItem>
                    <MenuItem value="fixed">Fixed Delay</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {activeTab === 'authentication' && (
        <Card variant="outlined">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Authentication Configuration
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="auth-type-label">Authentication Type</InputLabel>
                  <Select
                    labelId="auth-type-label"
                    value={config.authentication.type}
                    label="Authentication Type"
                    onChange={(e) => handleConfigChange('authentication', 'type', e.target.value)}
                  >
                    <MenuItem value="API_KEY">API Key</MenuItem>
                    <MenuItem value="BASIC">Basic Auth</MenuItem>
                    <MenuItem value="OAUTH2">OAuth 2.0</MenuItem>
                    <MenuItem value="JWT">JWT</MenuItem>
                    <MenuItem value="AWS_SIG_V4">AWS Signature V4</MenuItem>
                    <MenuItem value="CUSTOM">Custom</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              {config.authentication.type === 'API_KEY' && (
                <>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Header Name"
                      value={config.authentication.fields.headerName || 'X-API-Key'}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        headerName: e.target.value
                      })}
                      helperText="HTTP header name for the API key"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Query Parameter Name"
                      value={config.authentication.fields.queryParamName || 'api_key'}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        queryParamName: e.target.value
                      })}
                      helperText="Query parameter name for the API key (optional)"
                    />
                  </Grid>
                </>
              )}
              
              {config.authentication.type === 'BASIC' && (
                <>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Basic Authentication uses username and password credentials.
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Credentials will be provided at runtime and are not stored in the connector configuration.
                    </Typography>
                  </Grid>
                </>
              )}
              
              {config.authentication.type === 'OAUTH2' && (
                <>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Token URL"
                      value={config.authentication.fields.tokenUrl || ''}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        tokenUrl: e.target.value
                      })}
                      helperText="URL to obtain OAuth tokens"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel id="oauth-grant-type-label">Grant Type</InputLabel>
                      <Select
                        labelId="oauth-grant-type-label"
                        value={config.authentication.fields.grantType || 'client_credentials'}
                        label="Grant Type"
                        onChange={(e) => handleConfigChange('authentication', 'fields', {
                          ...config.authentication.fields,
                          grantType: e.target.value
                        })}
                      >
                        <MenuItem value="client_credentials">Client Credentials</MenuItem>
                        <MenuItem value="authorization_code">Authorization Code</MenuItem>
                        <MenuItem value="password">Password</MenuItem>
                        <MenuItem value="refresh_token">Refresh Token</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Scope"
                      value={config.authentication.fields.scope || ''}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        scope: e.target.value
                      })}
                      helperText="OAuth scopes (space-separated)"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Authorization URL"
                      value={config.authentication.fields.authorizationUrl || ''}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        authorizationUrl: e.target.value
                      })}
                      helperText="Required for authorization_code grant type"
                    />
                  </Grid>
                </>
              )}
              
              {config.authentication.type === 'JWT' && (
                <>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      JWT Authentication uses a JSON Web Token for authentication.
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      The JWT token will be provided at runtime and is not stored in the connector configuration.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Header Name"
                      value={config.authentication.fields.headerName || 'Authorization'}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        headerName: e.target.value
                      })}
                      helperText="HTTP header name for the JWT token"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Token Prefix"
                      value={config.authentication.fields.tokenPrefix || 'Bearer'}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        tokenPrefix: e.target.value
                      })}
                      helperText="Prefix for the JWT token (e.g., 'Bearer')"
                    />
                  </Grid>
                </>
              )}
              
              {config.authentication.type === 'AWS_SIG_V4' && (
                <>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      AWS Signature V4 Authentication for AWS services.
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      AWS credentials will be provided at runtime and are not stored in the connector configuration.
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Service Name"
                      value={config.authentication.fields.service || ''}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        service: e.target.value
                      })}
                      helperText="AWS service name (e.g., 's3', 'dynamodb')"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Region"
                      value={config.authentication.fields.region || ''}
                      onChange={(e) => handleConfigChange('authentication', 'fields', {
                        ...config.authentication.fields,
                        region: e.target.value
                      })}
                      helperText="AWS region (e.g., 'us-east-1')"
                    />
                  </Grid>
                </>
              )}
              
              {config.authentication.type === 'CUSTOM' && (
                <>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Custom Authentication allows you to define your own authentication method.
                    </Typography>
                    <Typography variant="body2" color="textSecondary" paragraph>
                      You'll need to implement the authentication logic in your connector code.
                    </Typography>
                    
                    <TextField
                      fullWidth
                      label="Custom Authentication Configuration"
                      multiline
                      rows={4}
                      value={JSON.stringify(config.authentication.fields.custom || {}, null, 2)}
                      onChange={(e) => {
                        try {
                          const customConfig = JSON.parse(e.target.value);
                          handleConfigChange('authentication', 'fields', {
                            ...config.authentication.fields,
                            custom: customConfig
                          });
                        } catch (error) {
                          // Invalid JSON, ignore
                        }
                      }}
                      helperText="Enter custom authentication configuration as JSON"
                    />
                  </Grid>
                </>
              )}
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {activeTab === 'headers' && (
        <Card variant="outlined">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Default Headers
            </Typography>
            
            <Typography variant="body2" color="textSecondary" paragraph>
              These headers will be included in all requests made by the connector.
            </Typography>
            
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={5}>
                <TextField
                  fullWidth
                  label="Header Name"
                  value={newHeaderKey}
                  onChange={(e) => setNewHeaderKey(e.target.value)}
                  placeholder="Content-Type"
                />
              </Grid>
              
              <Grid item xs={12} sm={5}>
                <TextField
                  fullWidth
                  label="Header Value"
                  value={newHeaderValue}
                  onChange={(e) => setNewHeaderValue(e.target.value)}
                  placeholder="application/json"
                />
              </Grid>
              
              <Grid item xs={12} sm={2}>
                <Button
                  fullWidth
                  variant="contained"
                  onClick={handleAddHeader}
                  disabled={!newHeaderKey.trim()}
                  sx={{ height: '100%' }}
                >
                  <AddIcon />
                </Button>
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 2 }} />
            
            {Object.keys(config.headers).length > 0 ? (
              <Grid container spacing={2}>
                {Object.entries(config.headers).map(([key, value]) => (
                  <Grid item xs={12} key={key} sx={{ display: 'flex', alignItems: 'center' }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={5}>
                        <TextField
                          fullWidth
                          label="Header Name"
                          value={key}
                          InputProps={{ readOnly: true }}
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={5}>
                        <TextField
                          fullWidth
                          label="Header Value"
                          value={value}
                          onChange={(e) => handleHeaderChange(key, e.target.value)}
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={2}>
                        <Button
                          fullWidth
                          variant="outlined"
                          color="error"
                          onClick={() => handleRemoveHeader(key)}
                          sx={{ height: '100%' }}
                        >
                          <DeleteIcon />
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 2 }}>
                No headers defined. Add a header using the form above.
              </Typography>
            )}
          </CardContent>
        </Card>
      )}
      
      {activeTab === 'advanced' && (
        <Card variant="outlined">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Advanced Configuration
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Rate Limit (requests per second)"
                  type="number"
                  value={config.advanced.rateLimit}
                  onChange={(e) => handleConfigChange('advanced', 'rateLimit', parseInt(e.target.value) || 10)}
                  helperText="Maximum number of requests per second"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="cache-enabled-label">Cache Enabled</InputLabel>
                  <Select
                    labelId="cache-enabled-label"
                    value={config.advanced.cacheEnabled}
                    label="Cache Enabled"
                    onChange={(e) => handleConfigChange('advanced', 'cacheEnabled', e.target.value)}
                  >
                    <MenuItem value={true}>Yes</MenuItem>
                    <MenuItem value={false}>No</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              {config.advanced.cacheEnabled && (
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Cache Expiration (seconds)"
                    type="number"
                    value={config.advanced.cacheExpiration}
                    onChange={(e) => handleConfigChange('advanced', 'cacheExpiration', parseInt(e.target.value) || 300)}
                    helperText="Time in seconds before cached responses expire"
                  />
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      )}
      
      {activeTab === 'json' && (
        <Card variant="outlined">
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Configuration JSON
            </Typography>
            
            <Typography variant="body2" color="textSecondary" paragraph>
              This is the JSON representation of your connector configuration.
            </Typography>
            
            <Paper variant="outlined" sx={{ overflow: 'auto' }}>
              <SyntaxHighlighter language="json" style={vscDarkPlus} showLineNumbers>
                {renderConfigAsJson()}
              </SyntaxHighlighter>
            </Paper>
          </CardContent>
        </Card>
      )}
      
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSaveConfig}
        >
          Save Configuration
        </Button>
      </Box>
    </Box>
  );
};

export default ConnectorConfiguration;
