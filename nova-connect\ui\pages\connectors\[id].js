/**
 * Connector Details Page
 * 
 * This page displays detailed information about a specific connector.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Container, 
  CircularProgress, 
  Typography,
  Alert,
  Snackbar,
  Fade
} from '@mui/material';
import { useRouter } from 'next/router';
import DashboardLayout from '../../layouts/DashboardLayout';
import EnhancedConnectorDetails from '../../components/management/EnhancedConnectorDetails';

const ConnectorDetailsPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [connector, setConnector] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  useEffect(() => {
    if (!id) return;
    
    const fetchConnector = async () => {
      setLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockConnector = {
          id,
          name: 'GitHub API Connector',
          description: 'Connect to GitHub API for repository management',
          category: 'development',
          type: 'api',
          version: '1.0.0',
          status: 'active',
          created: '2023-01-15T12:00:00Z',
          updated: '2023-03-20T14:30:00Z',
          baseUrl: 'https://api.github.com',
          usage: 1250,
          successRate: '99.8%',
          avgResponseTime: '120ms',
          lastUsed: '2023-05-25T09:15:00Z',
          author: 'John Doe',
          tags: ['github', 'development', 'api'],
          endpoints: [
            {
              id: 'ep1',
              name: 'Get Repositories',
              description: 'Retrieve a list of repositories for the authenticated user',
              path: '/user/repos',
              method: 'GET',
              parameters: [
                { name: 'visibility', type: 'enum', required: false, description: 'Filter by repository visibility', options: ['all', 'public', 'private'] },
                { name: 'sort', type: 'enum', required: false, description: 'Sort by field', options: ['created', 'updated', 'pushed', 'full_name'] },
                { name: 'per_page', type: 'integer', required: false, description: 'Results per page (max 100)' }
              ],
              headers: [
                { name: 'Accept', value: 'application/vnd.github.v3+json', required: true }
              ]
            },
            {
              id: 'ep2',
              name: 'Create Repository',
              description: 'Create a new repository for the authenticated user',
              path: '/user/repos',
              method: 'POST',
              parameters: [],
              headers: [
                { name: 'Accept', value: 'application/vnd.github.v3+json', required: true },
                { name: 'Content-Type', value: 'application/json', required: true }
              ],
              requestSchema: {
                type: 'object',
                required: ['name'],
                properties: {
                  name: { type: 'string', description: 'Repository name' },
                  description: { type: 'string', description: 'Repository description' },
                  private: { type: 'boolean', description: 'Whether the repository is private' },
                  auto_init: { type: 'boolean', description: 'Initialize with README' }
                }
              }
            },
            {
              id: 'ep3',
              name: 'Get Repository',
              description: 'Get a repository by owner and repo name',
              path: '/repos/{owner}/{repo}',
              method: 'GET',
              parameters: [
                { name: 'owner', type: 'string', required: true, description: 'Repository owner', in: 'path' },
                { name: 'repo', type: 'string', required: true, description: 'Repository name', in: 'path' }
              ],
              headers: [
                { name: 'Accept', value: 'application/vnd.github.v3+json', required: true }
              ]
            }
          ],
          versions: [
            {
              version: '1.0.0',
              date: '2023-01-15T12:00:00Z',
              author: 'John Doe',
              changes: [
                { type: 'add', description: 'Initial version' }
              ],
              status: 'active'
            },
            {
              version: '0.9.0',
              date: '2022-12-10T10:30:00Z',
              author: 'Jane Smith',
              changes: [
                { type: 'add', description: 'Beta release' },
                { type: 'add', description: 'Added repository endpoints' }
              ],
              status: 'deprecated'
            },
            {
              version: '0.5.0',
              date: '2022-11-05T14:45:00Z',
              author: 'John Doe',
              changes: [
                { type: 'add', description: 'Alpha release' },
                { type: 'add', description: 'Basic GitHub API integration' }
              ],
              status: 'deprecated'
            }
          ],
          authentication: {
            type: 'OAUTH2',
            fields: {
              tokenUrl: 'https://github.com/login/oauth/access_token',
              authorizationUrl: 'https://github.com/login/oauth/authorize',
              scope: 'repo user'
            }
          },
          configuration: {
            timeout: 30000,
            retryPolicy: {
              maxRetries: 3,
              backoffStrategy: 'exponential'
            },
            headers: {
              'Accept': 'application/vnd.github.v3+json',
              'User-Agent': 'NovaConnect'
            },
            rateLimit: 60,
            cacheEnabled: true,
            cacheExpiration: 300
          }
        };
        
        setConnector(mockConnector);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching connector:', error);
        setError('Failed to load connector details. Please try again.');
        setLoading(false);
      }
    };
    
    fetchConnector();
  }, [id]);
  
  const handleDelete = () => {
    // In a real implementation, this would call an API to delete the connector
    setSnackbar({
      open: true,
      message: 'Connector deleted successfully',
      severity: 'success'
    });
    
    // Navigate back to the connector list after a short delay
    setTimeout(() => {
      router.push('/management');
    }, 1500);
  };
  
  const handleDuplicate = () => {
    // In a real implementation, this would call an API to duplicate the connector
    setSnackbar({
      open: true,
      message: 'Connector duplicated successfully',
      severity: 'success'
    });
  };
  
  const handleRefresh = async () => {
    setLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setLoading(false);
    setSnackbar({
      open: true,
      message: 'Connector refreshed successfully',
      severity: 'info'
    });
  };
  
  const handleSnackbarClose = () => {
    setSnackbar({
      ...snackbar,
      open: false
    });
  };
  
  return (
    <DashboardLayout>
      <Container maxWidth="xl">
        {loading && !connector ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mt: 3 }}>
            {error}
          </Alert>
        ) : connector ? (
          <Fade in={true} timeout={500}>
            <Box>
              <EnhancedConnectorDetails 
                connector={connector}
                onDelete={handleDelete}
                onDuplicate={handleDuplicate}
                loading={loading}
                onRefresh={handleRefresh}
              />
            </Box>
          </Fade>
        ) : (
          <Box sx={{ mt: 3 }}>
            <Typography variant="h5" color="text.secondary" align="center">
              Connector not found
            </Typography>
          </Box>
        )}
        
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert 
            onClose={handleSnackbarClose} 
            severity={snackbar.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Container>
    </DashboardLayout>
  );
};

export default ConnectorDetailsPage;
