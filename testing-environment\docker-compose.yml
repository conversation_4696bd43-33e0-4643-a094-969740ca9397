version: '3.8'

services:
  # Connector Registry API
  connector-registry:
    build:
      context: ./connector-registry
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/connector-registry
    depends_on:
      - mongodb
    volumes:
      - ./connector-registry:/app
      - /app/node_modules

  # Authentication Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/auth-service
      - ENCRYPTION_KEY=test-encryption-key
    depends_on:
      - mongodb
    volumes:
      - ./auth-service:/app
      - /app/node_modules

  # Connector Executor
  connector-executor:
    build:
      context: ./connector-executor
      dockerfile: Dockerfile
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=development
      - REGISTRY_API_URL=http://connector-registry:3000
      - AUTH_SERVICE_URL=http://auth-service:3000
    volumes:
      - ./connector-executor:/app
      - /app/node_modules

  # Usage Metering Service
  usage-metering:
    build:
      context: ./usage-metering
      dockerfile: Dockerfile
    ports:
      - "3004:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=mongodb://mongodb:27017/usage-metering
    depends_on:
      - mongodb
    volumes:
      - ./usage-metering:/app
      - /app/node_modules

  # Mock API Services (for testing connectors)
  mock-apis:
    build:
      context: ./mock-apis
      dockerfile: Dockerfile
    ports:
      - "3005:3000"
    volumes:
      - ./mock-apis:/app
      - /app/node_modules

  # Frontend UI
  frontend:
    build:
      context: ./ui
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_REGISTRY_API_URL=http://localhost:3001
      - REACT_APP_AUTH_SERVICE_URL=http://localhost:3002
      - REACT_APP_EXECUTOR_URL=http://localhost:3003
      - REACT_APP_USAGE_URL=http://localhost:3004
    volumes:
      - ./ui:/app
      - /app/node_modules

  # Database
  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

volumes:
  mongodb_data:
