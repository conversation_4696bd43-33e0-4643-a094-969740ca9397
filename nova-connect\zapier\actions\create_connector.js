/**
 * Create Connector Action
 * 
 * This action creates a new connector.
 */

// Define the action
module.exports = {
  key: 'create_connector',
  noun: 'Connector',
  
  // Display information
  display: {
    label: 'Create Connector',
    description: 'Creates a new connector.',
    important: true
  },
  
  // Operation
  operation: {
    // Perform operation
    type: 'perform',
    
    // Perform the operation
    perform: {
      url: '{{process.env.API_BASE_URL}}/api/zapier/actions/create-connector',
      method: 'POST',
      headers: {
        Authorization: 'Bearer {{bundle.authData.access_token}}',
        'Content-Type': 'application/json'
      },
      body: {
        name: '{{bundle.inputData.name}}',
        type: '{{bundle.inputData.type}}',
        config: '{{bundle.inputData.config}}'
      }
    },
    
    // Input fields
    inputFields: [
      {
        key: 'name',
        label: 'Name',
        type: 'string',
        required: true,
        helpText: 'The name of the connector.'
      },
      {
        key: 'type',
        label: 'Type',
        type: 'string',
        required: true,
        choices: {
          api: 'API',
          database: 'Database',
          file: 'File'
        },
        helpText: 'The type of the connector.'
      },
      {
        key: 'config',
        label: 'Configuration',
        type: 'text',
        required: true,
        helpText: 'The configuration of the connector in JSON format.'
      }
    ],
    
    // Sample data
    sample: {
      id: 'conn-123',
      name: 'Sample Connector',
      type: 'api',
      createdAt: '2023-01-01T00:00:00Z'
    },
    
    // Output fields
    outputFields: [
      { key: 'id', label: 'ID' },
      { key: 'name', label: 'Name' },
      { key: 'type', label: 'Type' },
      { key: 'createdAt', label: 'Created At' }
    ]
  }
};
