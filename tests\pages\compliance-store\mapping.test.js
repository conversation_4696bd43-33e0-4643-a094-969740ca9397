import React from 'react';
import { render, screen } from '@testing-library/react';
import MappingPage from '../../../pages/compliance-store/mapping';
import { AuthContext } from '../../../contexts/AuthContext';

// Mock the CrossFrameworkMapping component
jest.mock('../../../components/CrossFrameworkMapping', () => {
  return function MockCrossFrameworkMapping() {
    return <div data-testid="cross-framework-mapping">Cross Framework Mapping Component</div>;
  };
});

// Mock the ProtectedRoute component
jest.mock('../../../components/ProtectedRoute', () => {
  return function MockProtectedRoute({ children }) {
    return <div data-testid="protected-route">{children}</div>;
  };
});

describe('Mapping Page', () => {
  it('renders the mapping page with protected route', () => {
    render(
      <AuthContext.Provider value={{ isAuthenticated: true, user: { name: 'Test User' } }}>
        <MappingPage />
      </AuthContext.Provider>
    );
    
    // Check if protected route is used
    expect(screen.getByTestId('protected-route')).toBeInTheDocument();
    
    // Check if cross framework mapping component is rendered
    expect(screen.getByTestId('cross-framework-mapping')).toBeInTheDocument();
  });
  
  it('renders the page title and description', () => {
    render(
      <AuthContext.Provider value={{ isAuthenticated: true, user: { name: 'Test User' } }}>
        <MappingPage />
      </AuthContext.Provider>
    );
    
    // Check if page title is rendered
    expect(screen.getByText('Cross-Framework Mapping')).toBeInTheDocument();
    
    // Check if description is rendered
    expect(screen.getByText(/Map controls between different compliance frameworks/i)).toBeInTheDocument();
  });
  
  it('renders the call to action section', () => {
    render(
      <AuthContext.Provider value={{ isAuthenticated: true, user: { name: 'Test User' } }}>
        <MappingPage />
      </AuthContext.Provider>
    );
    
    // Check if CTA title is rendered
    expect(screen.getByText('Ready to simplify compliance mapping?')).toBeInTheDocument();
    
    // Check if CTA description is rendered
    expect(screen.getByText(/Get early access to NovaFuse's Cross-Framework Mapping Engine/i)).toBeInTheDocument();
    
    // Check if CTA button is rendered
    expect(screen.getByText('Get Started')).toBeInTheDocument();
  });
  
  it('renders the confidentiality notice', () => {
    render(
      <AuthContext.Provider value={{ isAuthenticated: true, user: { name: 'Test User' } }}>
        <MappingPage />
      </AuthContext.Provider>
    );
    
    // Check if confidentiality notice is rendered
    expect(screen.getByText('Confidentiality Notice')).toBeInTheDocument();
    expect(screen.getByText(/This feature is currently in preview/i)).toBeInTheDocument();
  });
});
