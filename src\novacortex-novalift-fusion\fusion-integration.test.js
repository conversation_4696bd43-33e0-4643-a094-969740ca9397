// NovaCortex-NovaLift Fusion Integration Test Suite
// Run with: node fusion-integration.test.js (or integrate with Jest/Mocha)

const axios = require('axios');

const FUSION_BASE = 'http://localhost:3015/fusion';

async function testHealth() {
  const res = await axios.get(`${FUSION_BASE}/health`);
  console.log('Health:', res.data);
}

async function testMetrics() {
  const res = await axios.get(`${FUSION_BASE}/metrics`);
  console.log('Metrics:', res.data);
}

async function testConsciousness() {
  const res = await axios.get(`${FUSION_BASE}/consciousness`);
  console.log('Consciousness:', res.data);
}

async function testOptimization() {
  const payload = {
    system_metrics: {
      target_performance: 0.95,
      downtime_risk: 0.02,
      cpu: 0.7,
      memory: 0.6
    },
    optimization_params: {
      energy_efficiency: 0.9,
      notify_users: true
    }
  };
  const res = await axios.post(`${FUSION_BASE}/optimize`, payload);
  console.log('Optimization:', res.data);
}

async function testDecision() {
  const payload = {
    scenario: {
      type: 'infrastructure_optimization',
      options: ['aggressive_optimization', 'balanced_optimization', 'conservative_optimization'],
      constraints: {
        coherence_threshold: 0.95,
        pi_deviation_max: 0.1,
        performance_target: 0.9,
        energy_efficiency: 0.8,
        user_notification: true,
        downtime_risk: 0.05,
        benefits_users: true
      }
    },
    context: {}
  };
  const res = await axios.post(`${FUSION_BASE}/decide`, payload);
  console.log('Decision:', res.data);
}

async function runAllTests() {
  try {
    console.log('--- Fusion Health Test ---');
    await testHealth();
    console.log('--- Fusion Metrics Test ---');
    await testMetrics();
    console.log('--- Consciousness Assessment Test ---');
    await testConsciousness();
    console.log('--- Optimization Test ---');
    await testOptimization();
    console.log('--- Ethical Decision Test ---');
    await testDecision();
    console.log('All tests completed.');
  } catch (err) {
    console.error('Test failed:', err.message);
  }
}

if (require.main === module) {
  runAllTests();
}
