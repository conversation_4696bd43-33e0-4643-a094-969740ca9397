import React, { useState, useEffect } from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import RegulatoryContextProvider, { useRegulatoryContext } from '../RegulatoryContextProvider';
import { novaVision } from '../../NovaVision';

export default {
  title: 'UUIC/RegulationSwitcher',
  component: RegulationSwitcher
} as ComponentMeta<typeof RegulationSwitcher>;

// Create a template
const Template: ComponentStory<typeof RegulationSwitcher> = (args) => (
  <RegulationSwitcher {...args} />
);

// Regulation Switcher component
function RegulationSwitcher() {
  const [userId] = useState('user-001');
  const [sessionId] = useState(`session-${Date.now()}`);
  const [jurisdiction, setJurisdiction] = useState('us-general');
  const [switching, setSwitching] = useState(false);
  const [switchResult, setSwitchResult] = useState<any>(null);
  const [switchHistory, setSwitchHistory] = useState<any[]>([]);
  
  // Initialize user session
  useEffect(() => {
    const initSession = async () => {
      try {
        await novaVision.regulationOrchestrator.createUserSession(
          userId,
          sessionId,
          jurisdiction
        );
        
        // Subscribe to UI updates
        const unsubscribe = novaVision.regulationOrchestrator.subscribeToUIUpdates(
          sessionId,
          (message) => {
            console.log('UI Update Received:', message);
          }
        );
        
        return () => {
          unsubscribe();
        };
      } catch (error) {
        console.error('Error initializing session:', error);
      }
    };
    
    initSession();
  }, [userId, sessionId, jurisdiction]);
  
  // Handle jurisdiction change
  const handleJurisdictionChange = async (newJurisdiction: string) => {
    if (newJurisdiction === jurisdiction) {
      return;
    }
    
    setSwitching(true);
    
    try {
      const result = await novaVision.handleJurisdictionChange(
        userId,
        sessionId,
        newJurisdiction
      );
      
      setSwitchResult(result);
      setJurisdiction(newJurisdiction);
      
      // Add to history
      setSwitchHistory(prev => [
        {
          timestamp: new Date().toISOString(),
          from: jurisdiction,
          to: newJurisdiction,
          latency: result.latency
        },
        ...prev
      ]);
    } catch (error) {
      console.error('Error switching jurisdiction:', error);
    } finally {
      setSwitching(false);
    }
  };
  
  return (
    <div style={{ fontFamily: 'Arial, sans-serif', maxWidth: '800px', margin: '0 auto', padding: '20px' }}>
      <h2>Real-Time Regulation Switching</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '10px' }}>
          <strong>User ID:</strong> {userId}
        </div>
        <div style={{ marginBottom: '10px' }}>
          <strong>Session ID:</strong> {sessionId}
        </div>
        <div style={{ marginBottom: '10px' }}>
          <strong>Current Jurisdiction:</strong> {jurisdiction}
        </div>
      </div>
      
      <div style={{ marginBottom: '30px' }}>
        <h3>Switch Jurisdiction</h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={() => handleJurisdictionChange('eu')}
            disabled={switching || jurisdiction === 'eu'}
            style={{
              padding: '10px 15px',
              backgroundColor: jurisdiction === 'eu' ? '#e6f7ff' : '#fff',
              border: '1px solid #1890ff',
              borderRadius: '4px',
              cursor: jurisdiction === 'eu' ? 'default' : 'pointer'
            }}
          >
            European Union (GDPR)
          </button>
          <button
            onClick={() => handleJurisdictionChange('us-healthcare')}
            disabled={switching || jurisdiction === 'us-healthcare'}
            style={{
              padding: '10px 15px',
              backgroundColor: jurisdiction === 'us-healthcare' ? '#e6f7ff' : '#fff',
              border: '1px solid #1890ff',
              borderRadius: '4px',
              cursor: jurisdiction === 'us-healthcare' ? 'default' : 'pointer'
            }}
          >
            US Healthcare (HIPAA)
          </button>
          <button
            onClick={() => handleJurisdictionChange('us-finance')}
            disabled={switching || jurisdiction === 'us-finance'}
            style={{
              padding: '10px 15px',
              backgroundColor: jurisdiction === 'us-finance' ? '#e6f7ff' : '#fff',
              border: '1px solid #1890ff',
              borderRadius: '4px',
              cursor: jurisdiction === 'us-finance' ? 'default' : 'pointer'
            }}
          >
            US Finance (PCI DSS, SOX)
          </button>
          <button
            onClick={() => handleJurisdictionChange('us-general')}
            disabled={switching || jurisdiction === 'us-general'}
            style={{
              padding: '10px 15px',
              backgroundColor: jurisdiction === 'us-general' ? '#e6f7ff' : '#fff',
              border: '1px solid #1890ff',
              borderRadius: '4px',
              cursor: jurisdiction === 'us-general' ? 'default' : 'pointer'
            }}
          >
            US General (CCPA)
          </button>
          <button
            onClick={() => handleJurisdictionChange('global')}
            disabled={switching || jurisdiction === 'global'}
            style={{
              padding: '10px 15px',
              backgroundColor: jurisdiction === 'global' ? '#e6f7ff' : '#fff',
              border: '1px solid #1890ff',
              borderRadius: '4px',
              cursor: jurisdiction === 'global' ? 'default' : 'pointer'
            }}
          >
            Global (Multiple Regulations)
          </button>
        </div>
      </div>
      
      {switching && (
        <div style={{ 
          padding: '15px', 
          backgroundColor: '#fffbe6', 
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #ffe58f'
        }}>
          <strong>Switching jurisdiction...</strong>
        </div>
      )}
      
      {switchResult && !switching && (
        <div style={{ 
          padding: '15px', 
          backgroundColor: '#f6ffed', 
          borderRadius: '4px',
          marginBottom: '20px',
          border: '1px solid #b7eb8f'
        }}>
          <div><strong>Status:</strong> {switchResult.status}</div>
          <div><strong>Latency:</strong> {switchResult.latency}ms</div>
        </div>
      )}
      
      <RegulatoryContextProvider jurisdiction={jurisdiction} userRole="compliance-officer">
        <DynamicUI />
      </RegulatoryContextProvider>
      
      {switchHistory.length > 0 && (
        <div style={{ marginTop: '30px' }}>
          <h3>Switch History</h3>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr>
                <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>Timestamp</th>
                <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>From</th>
                <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>To</th>
                <th style={{ textAlign: 'left', padding: '8px', borderBottom: '1px solid #ddd' }}>Latency</th>
              </tr>
            </thead>
            <tbody>
              {switchHistory.map((item, index) => (
                <tr key={index}>
                  <td style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>
                    {new Date(item.timestamp).toLocaleTimeString()}
                  </td>
                  <td style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>{item.from}</td>
                  <td style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>{item.to}</td>
                  <td style={{ padding: '8px', borderBottom: '1px solid #ddd' }}>{item.latency}ms</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}

// Dynamic UI component that adapts to regulatory context
function DynamicUI() {
  const { activeRegulations, loading } = useRegulatoryContext();
  
  if (loading) {
    return <div>Loading regulations...</div>;
  }
  
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: '#f5f5f5', 
      borderRadius: '4px',
      marginTop: '20px'
    }}>
      <h3>Dynamic UI (Zero-Reboot Regulation Switching)</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <strong>Active Regulations:</strong> {activeRegulations.join(', ')}
      </div>
      
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '15px' }}>
        {/* Base UI - Always shown */}
        <div style={{ 
          flex: '1 1 200px',
          padding: '15px',
          backgroundColor: 'white',
          borderRadius: '4px',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
        }}>
          <h4>Base UI</h4>
          <p>This component is always shown regardless of jurisdiction.</p>
        </div>
        
        {/* GDPR-specific UI */}
        {activeRegulations.includes('GDPR') && (
          <div style={{ 
            flex: '1 1 200px',
            padding: '15px',
            backgroundColor: '#e6f7ff',
            borderRadius: '4px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <h4>GDPR Module</h4>
            <p>This component is only shown when GDPR is active.</p>
            <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
              <li>Data Subject Rights</li>
              <li>Consent Management</li>
              <li>Data Protection</li>
            </ul>
          </div>
        )}
        
        {/* HIPAA-specific UI */}
        {activeRegulations.includes('HIPAA') && (
          <div style={{ 
            flex: '1 1 200px',
            padding: '15px',
            backgroundColor: '#f6ffed',
            borderRadius: '4px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <h4>HIPAA Module</h4>
            <p>This component is only shown when HIPAA is active.</p>
            <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
              <li>PHI Protection</li>
              <li>Security Rules</li>
              <li>Privacy Rules</li>
            </ul>
          </div>
        )}
        
        {/* PCI DSS-specific UI */}
        {activeRegulations.includes('PCI_DSS') && (
          <div style={{ 
            flex: '1 1 200px',
            padding: '15px',
            backgroundColor: '#fff7e6',
            borderRadius: '4px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <h4>PCI DSS Module</h4>
            <p>This component is only shown when PCI DSS is active.</p>
            <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
              <li>Card Data Security</li>
              <li>Security Controls</li>
              <li>Compliance Monitoring</li>
            </ul>
          </div>
        )}
        
        {/* SOX-specific UI */}
        {activeRegulations.includes('SOX') && (
          <div style={{ 
            flex: '1 1 200px',
            padding: '15px',
            backgroundColor: '#f9f0ff',
            borderRadius: '4px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <h4>SOX Module</h4>
            <p>This component is only shown when SOX is active.</p>
            <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
              <li>Financial Reporting</li>
              <li>Audit Trails</li>
              <li>Corporate Governance</li>
            </ul>
          </div>
        )}
        
        {/* CCPA-specific UI */}
        {activeRegulations.includes('CCPA') && (
          <div style={{ 
            flex: '1 1 200px',
            padding: '15px',
            backgroundColor: '#fff2f0',
            borderRadius: '4px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <h4>CCPA Module</h4>
            <p>This component is only shown when CCPA is active.</p>
            <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
              <li>Consumer Privacy</li>
              <li>Opt-Out Rights</li>
              <li>Data Disclosure</li>
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

// Create story
export const Default = Template.bind({});
