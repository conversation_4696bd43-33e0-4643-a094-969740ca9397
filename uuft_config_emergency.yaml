# UUFT Configuration - Emergency Patch
# Based on Orion's emergency fixes

environment: "docker"  # options: docker | gcp

resource_allocation:
  method: entropy_weighted
  critical_task_weight: 1.0           # Base weight (will be adjusted by entropy)
  standard_task_floor: 0.65           # Absolute minimum for standard tasks
  feedback_loop_enabled: true         # Allow auto-adjustment during runtime
  optimization_strategy: pareto       # Continue using Pareto optimization

pattern_preservation:
  use_skip_connections: true
  skip_connection_weight: 0.15        # Further reduced to 0.15 per Orion's final fix
  attention_enabled: false            # Disable faulty attention mechanism
  attention_type: none                # Not used but required by config loader
  contrastive_loss_weight: 0.1        # Minimal contrastive adjustment

pi10_scaling:
  enabled: true
  base_multiplier: 3141.59
  dynamic_scaling_enabled: true
  variance_threshold: 0.82            # Updated to Orion's recommended value
  scaling_factor_adjustment: iqr      # Using IQR-based approach instead of CV
  domain_specific_calibration: false  # Disabled until future refinement

fusion:
  operator_chain: "(A ⊗ B ⊕ C) × π"
  normalization: "none"               # Disable normalization for now
  domain_encoders:
    cybersecurity: 0.618
    financial: 0.667
    healthcare: 0.6
    physics: 0.577
  feature_bifurcation: false          # Disable complex bifurcation

logging:
  log_level: debug                    # Detailed logging for debugging
  save_outputs: true
  output_dir: "/logs/uuft_emergency"
  versioning_enabled: true

hardware:
  use_gpu: false                      # Disable GPU for consistent testing
  parallel_processing: false          # Disable parallel processing for stability
  max_threads: 1                      # Single-threaded for deterministic results

metadata:
  experiment_id: "uuft_emergency_patch"
  tester: "Auggie"
  timestamp: "2025-05-10T18:00:00Z"
  notes: "Emergency patch based on Orion's recommendations. Simplified approach for stability."
