/**
 * Secure Authentication Service for NovaConnect Universal API Connector
 * 
 * This service provides secure credential storage and management with strong encryption.
 */

const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');
const winston = require('winston');
const path = require('path');

// Import encryption utilities
const { encrypt, decrypt } = require('../utils/encryption');
const keyManagement = require('../utils/key-management');

const app = express();
const port = process.env.PORT || 3000;

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/auth-service', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => {
  logger.info('Connected to MongoDB');
})
.catch((err) => {
  logger.error('Error connecting to MongoDB', { error: err.message });
});

// Define credential schema with enhanced security
const credentialSchema = new mongoose.Schema({
  name: { type: String, required: true },
  connectorId: { type: String, required: true },
  authType: { type: String, required: true },
  credentials: { type: String, required: true }, // Encrypted credentials
  keyId: { type: String, required: true }, // ID of the key used for encryption
  userId: { type: String, required: true },
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now },
  lastAccessed: { type: Date }
});

const Credential = mongoose.model('Credential', credentialSchema);

// Initialize key management service
(async () => {
  try {
    await keyManagement.initialize();
    logger.info('Key management service initialized');
  } catch (error) {
    logger.error('Error initializing key management service', { error: error.message });
    process.exit(1);
  }
})();

// Enhanced encryption/decryption functions
const encryptCredentials = (credentials) => {
  // Get current encryption key
  const { id: keyId, key } = keyManagement.getCurrentKey();
  
  // Encrypt credentials with the key
  const encryptedData = encrypt(credentials, key);
  
  // Add key ID to encrypted data for later decryption
  encryptedData.keyId = keyId;
  
  return {
    encryptedData: JSON.stringify(encryptedData),
    keyId
  };
};

const decryptCredentials = (encryptedCredentialsStr, keyId) => {
  // Parse encrypted data
  const encryptedData = JSON.parse(encryptedCredentialsStr);
  
  // Get the key used for encryption
  const { key } = keyManagement.getKeyById(keyId);
  
  if (!key) {
    throw new Error(`Encryption key not found: ${keyId}`);
  }
  
  // Decrypt credentials
  return decrypt(encryptedData, key, { parseJson: true });
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// API endpoints
app.get('/credentials', async (req, res) => {
  try {
    const userId = req.query.userId;
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    const credentials = await Credential.find({ userId }, '-credentials');
    res.json(credentials);
  } catch (err) {
    logger.error('Error fetching credentials', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/credentials/:id', async (req, res) => {
  try {
    const credential = await Credential.findById(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    
    // Return credential without the encrypted credentials
    const { credentials, ...credentialData } = credential.toObject();
    res.json(credentialData);
  } catch (err) {
    logger.error('Error fetching credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/credentials', async (req, res) => {
  try {
    const { name, connectorId, authType, credentials, userId } = req.body;
    
    if (!name || !connectorId || !authType || !credentials || !userId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Encrypt credentials with enhanced security
    const { encryptedData, keyId } = encryptCredentials(credentials);
    
    const credential = new Credential({
      name,
      connectorId,
      authType,
      credentials: encryptedData,
      keyId,
      userId
    });
    
    await credential.save();
    
    // Return credential without the encrypted credentials
    const { credentials: _, ...credentialData } = credential.toObject();
    res.status(201).json(credentialData);
  } catch (err) {
    logger.error('Error creating credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.put('/credentials/:id', async (req, res) => {
  try {
    const { name, credentials } = req.body;
    const updateData = { name, updated: Date.now() };
    
    if (credentials) {
      // Encrypt credentials with enhanced security
      const { encryptedData, keyId } = encryptCredentials(credentials);
      updateData.credentials = encryptedData;
      updateData.keyId = keyId;
    }
    
    const credential = await Credential.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    );
    
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    
    // Return credential without the encrypted credentials
    const { credentials: _, ...credentialData } = credential.toObject();
    res.json(credentialData);
  } catch (err) {
    logger.error('Error updating credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.delete('/credentials/:id', async (req, res) => {
  try {
    const credential = await Credential.findByIdAndDelete(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    res.status(204).send();
  } catch (err) {
    logger.error('Error deleting credential', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get decrypted credentials (for internal use by connector executor)
app.get('/credentials/:id/decrypt', async (req, res) => {
  try {
    const credential = await Credential.findById(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'Credential not found' });
    }
    
    // Update last accessed timestamp
    credential.lastAccessed = Date.now();
    await credential.save();
    
    // Decrypt credentials with enhanced security
    const decryptedCredentials = decryptCredentials(credential.credentials, credential.keyId);
    
    res.json({
      id: credential._id,
      authType: credential.authType,
      credentials: decryptedCredentials
    });
  } catch (err) {
    logger.error('Error decrypting credentials', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Key management endpoints (protected, for admin use only)
app.post('/keys/rotate', async (req, res) => {
  try {
    // In production, this would require admin authentication
    const newKeyId = keyManagement.forceKeyRotation();
    
    // Return success message
    res.json({ 
      message: 'Encryption keys rotated successfully',
      keyId: newKeyId
    });
  } catch (err) {
    logger.error('Error rotating encryption keys', { error: err.message });
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Start the server
app.listen(port, () => {
  logger.info(`Secure authentication service running on port ${port}`);
});

module.exports = app;
