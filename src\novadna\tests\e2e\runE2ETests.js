/**
 * End-to-End Test Runner for NovaDNA
 * 
 * This script runs all end-to-end tests for the NovaDNA system.
 */

const { runEmergencyAccessScenario, runEmergencyOverrideScenario } = require('./EmergencyAccessScenario');
const { runEpicIntegrationScenario, runMultiProviderScenario } = require('./HealthcareIntegrationScenario');

/**
 * Run all end-to-end tests
 */
async function runAllE2ETests() {
  console.log('=== NovaDNA End-to-End Test Runner ===');
  console.log('Running all end-to-end tests...\n');
  
  let allTestsPassed = true;
  
  try {
    // Run emergency access scenarios
    console.log('=== Emergency Access Scenarios ===');
    
    // Run emergency access scenario
    console.log('\n=== Emergency Access Scenario ===');
    const accessResult = await runEmergencyAccessScenario();
    
    if (accessResult) {
      console.log('✅ Emergency access scenario passed');
    } else {
      console.error('❌ Emergency access scenario failed');
      allTestsPassed = false;
    }
    
    // Run emergency override scenario
    console.log('\n=== Emergency Override Scenario ===');
    const overrideResult = await runEmergencyOverrideScenario();
    
    if (overrideResult) {
      console.log('✅ Emergency override scenario passed');
    } else {
      console.error('❌ Emergency override scenario failed');
      allTestsPassed = false;
    }
    
    console.log('');
    
    // Run healthcare integration scenarios
    console.log('=== Healthcare Integration Scenarios ===');
    
    // Run Epic integration scenario
    console.log('\n=== Epic Integration Scenario ===');
    const epicResult = await runEpicIntegrationScenario();
    
    if (epicResult) {
      console.log('✅ Epic integration scenario passed');
    } else {
      console.error('❌ Epic integration scenario failed');
      allTestsPassed = false;
    }
    
    // Run multi-provider scenario
    console.log('\n=== Multi-Provider Integration Scenario ===');
    const multiProviderResult = await runMultiProviderScenario();
    
    if (multiProviderResult) {
      console.log('✅ Multi-provider integration scenario passed');
    } else {
      console.error('❌ Multi-provider integration scenario failed');
      allTestsPassed = false;
    }
    
    console.log('');
    
    // Summary
    if (allTestsPassed) {
      console.log('=== Test Summary ===');
      console.log('✅ All end-to-end tests passed successfully!');
    } else {
      console.log('=== Test Summary ===');
      console.error('❌ Some end-to-end tests failed. See above for details.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running end-to-end tests:', error);
    process.exit(1);
  }
}

/**
 * Run a specific end-to-end test
 * @param {String} testName - The name of the test to run
 */
async function runSpecificE2ETest(testName) {
  console.log(`=== Running ${testName} Scenario ===\n`);
  
  try {
    let result = false;
    
    switch (testName.toLowerCase()) {
      case 'access':
        result = await runEmergencyAccessScenario();
        break;
        
      case 'override':
        result = await runEmergencyOverrideScenario();
        break;
        
      case 'epic':
        result = await runEpicIntegrationScenario();
        break;
        
      case 'multiprovider':
        result = await runMultiProviderScenario();
        break;
        
      default:
        console.error(`Unknown test: ${testName}`);
        console.log('Available tests:');
        console.log('- access: Emergency access scenario');
        console.log('- override: Emergency override scenario');
        console.log('- epic: Epic integration scenario');
        console.log('- multiprovider: Multi-provider integration scenario');
        console.log('- (no argument): Run all end-to-end tests');
        process.exit(1);
    }
    
    if (result) {
      console.log(`\n✅ ${testName} scenario passed`);
    } else {
      console.error(`\n❌ ${testName} scenario failed`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`Error running ${testName} scenario:`, error);
    process.exit(1);
  }
}

// Get command line arguments
const args = process.argv.slice(2);
const testName = args[0];

// Run specific test or all tests
if (!testName) {
  runAllE2ETests();
} else {
  runSpecificE2ETest(testName);
}
