// TypeScript Bridge to Go NovaAgent - Hybrid Approach
// Real API calls to working Go backend

interface NovaAgentResponse {
  status: string;
  version: string;
  coherence: number;
  uptime: string;
  psi_snap: boolean;
  timestamp: string;
}

interface CoherenceAnalysis {
  overall: number;
  structural: number;
  functional: number;
  relational: number;
  psi_snap: boolean;
  violations: string[];
}

interface ComplianceResult {
  accessibility_score: number;
  wcag_violations: string[];
  ada_compliance: boolean;
  suggested_fixes: string[];
}

interface ThreatAssessment {
  risk_level: string;
  threat_score: number;
  detected_threats: string[];
  recommended_actions: string[];
}

class NovaAgentBridge {
  private apiBase: string;
  private wsUrl: string;
  private ws: WebSocket | null = null;

  constructor(apiBase = 'http://localhost:8090') {
    this.apiBase = apiBase;
    this.wsUrl = apiBase.replace('http', 'ws') + '/ws';
  }

  // Real API call to Go NovaAgent
  async getStatus(): Promise<NovaAgentResponse> {
    try {
      const response = await fetch(`${this.apiBase}/status`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('NovaAgent status check failed:', error);
      throw error;
    }
  }

  // Real coherence analysis via Go backend
  async analyzeCoherence(): Promise<CoherenceAnalysis> {
    try {
      const response = await fetch(`${this.apiBase}/coherence`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('Coherence analysis failed:', error);
      throw error;
    }
  }

  // Real DOM analysis with actual metrics
  analyzePageCoherence(): CoherenceAnalysis {
    console.log('🧬 Analyzing page coherence...');
    
    // REAL DOM analysis - no simulation
    const elements = document.querySelectorAll('*');
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const paragraphs = document.querySelectorAll('p');
    const links = document.querySelectorAll('a');
    const buttons = document.querySelectorAll('button');
    const images = document.querySelectorAll('img');
    const semanticElements = document.querySelectorAll('main, section, article, aside, nav, header, footer');

    // Structural coherence - DOM hierarchy analysis
    const structuralScore = this.calculateStructuralCoherence(elements, headings, paragraphs, semanticElements);
    
    // Functional coherence - interactivity analysis
    const functionalScore = this.calculateFunctionalCoherence(links, buttons, elements);
    
    // Relational coherence - content relationship analysis
    const relationalScore = this.calculateRelationalCoherence(headings, paragraphs, images);

    const overall = (structuralScore + functionalScore + relationalScore) / 3;
    const psi_snap = overall >= 0.82;

    const violations: string[] = [];
    if (structuralScore < 0.7) violations.push('Poor DOM structure detected');
    if (functionalScore < 0.7) violations.push('Limited interactivity');
    if (relationalScore < 0.7) violations.push('Weak content relationships');

    return {
      overall,
      structural: structuralScore,
      functional: functionalScore,
      relational: relationalScore,
      psi_snap,
      violations
    };
  }

  // Real accessibility analysis
  analyzeAccessibility(): ComplianceResult {
    console.log('👁️ Analyzing accessibility compliance...');
    
    const violations: string[] = [];
    const fixes: string[] = [];
    
    // Check images without alt text
    const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
    if (imagesWithoutAlt.length > 0) {
      violations.push(`${imagesWithoutAlt.length} images missing alt text`);
      fixes.push('Add descriptive alt attributes to all images');
    }

    // Check heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const h1Count = document.querySelectorAll('h1').length;
    if (h1Count !== 1) {
      violations.push(`Found ${h1Count} H1 elements (should be exactly 1)`);
      fixes.push('Ensure single H1 per page');
    }

    // Check for poor contrast (simplified detection)
    const poorContrastElements = document.querySelectorAll('[style*="color: #ffffff"][style*="background: #ffff00"], [style*="color: #ff0000"][style*="background: #ff0000"]');
    if (poorContrastElements.length > 0) {
      violations.push(`${poorContrastElements.length} elements with poor color contrast`);
      fixes.push('Improve color contrast ratios to meet WCAG standards');
    }

    // Check for missing form labels
    const inputsWithoutLabels = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
    const unlabeledInputs = Array.from(inputsWithoutLabels).filter(input => {
      const id = input.getAttribute('id');
      return !id || !document.querySelector(`label[for="${id}"]`);
    });
    if (unlabeledInputs.length > 0) {
      violations.push(`${unlabeledInputs.length} form inputs without proper labels`);
      fixes.push('Add labels or aria-label attributes to form inputs');
    }

    const accessibilityScore = Math.max(0, 1 - (violations.length * 0.2));
    const adaCompliance = violations.length === 0;

    return {
      accessibility_score: accessibilityScore,
      wcag_violations: violations,
      ada_compliance: adaCompliance,
      suggested_fixes: fixes
    };
  }

  // Real threat assessment
  assessThreats(): ThreatAssessment {
    console.log('🛡️ Assessing security threats...');
    
    const threats: string[] = [];
    const actions: string[] = [];
    
    // Check protocol security
    if (location.protocol === 'http:' && !location.hostname.includes('localhost')) {
      threats.push('Insecure HTTP connection');
      actions.push('Upgrade to HTTPS');
    }

    // Check for external scripts
    const externalScripts = document.querySelectorAll('script[src]');
    let externalCount = 0;
    externalScripts.forEach(script => {
      const src = script.getAttribute('src');
      if (src && !src.includes(location.hostname) && !src.startsWith('data:') && !src.startsWith('/')) {
        externalCount++;
      }
    });
    if (externalCount > 0) {
      threats.push(`${externalCount} external scripts detected`);
      actions.push('Review and validate external script sources');
    }

    // Check for tracking indicators
    const trackingIndicators = document.querySelectorAll('[src*="analytics"], [src*="tracking"], [src*="facebook"], [src*="google-analytics"]');
    if (trackingIndicators.length > 0) {
      threats.push(`${trackingIndicators.length} tracking scripts detected`);
      actions.push('Enable privacy protection');
    }

    const threatScore = Math.min(1, threats.length * 0.3);
    const riskLevel = threatScore >= 0.7 ? 'HIGH' : threatScore >= 0.4 ? 'MEDIUM' : 'LOW';

    return {
      risk_level: riskLevel,
      threat_score: threatScore,
      detected_threats: threats,
      recommended_actions: actions
    };
  }

  // Real WebSocket connection to Go backend
  connectWebSocket(onMessage?: (data: any) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.wsUrl);
        
        this.ws.onopen = () => {
          console.log('🔌 Connected to NovaAgent WebSocket');
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (onMessage) onMessage(data);
          } catch (error) {
            console.error('WebSocket message parse error:', error);
          }
        };
        
        this.ws.onclose = () => {
          console.log('🔌 NovaAgent WebSocket disconnected');
        };
        
        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  // Send command to Go backend
  async sendCommand(type: string, payload: any = {}): Promise<any> {
    try {
      const response = await fetch(`${this.apiBase}/command`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, payload })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Command execution failed:', error);
      throw error;
    }
  }

  // Helper methods for coherence calculation
  private calculateStructuralCoherence(elements: NodeListOf<Element>, headings: NodeListOf<Element>, paragraphs: NodeListOf<Element>, semantic: NodeListOf<Element>): number {
    const elementCount = elements.length;
    const headingRatio = headings.length / Math.max(1, paragraphs.length);
    const semanticRatio = semantic.length / Math.max(1, elementCount / 20);
    
    // Penalize excessive DOM complexity
    const complexityPenalty = Math.max(0, 1 - (elementCount / 500));
    
    return Math.min(1, (headingRatio * 0.4 + semanticRatio * 0.4 + complexityPenalty * 0.2));
  }

  private calculateFunctionalCoherence(links: NodeListOf<Element>, buttons: NodeListOf<Element>, elements: NodeListOf<Element>): number {
    const interactiveElements = links.length + buttons.length;
    const totalElements = elements.length;
    
    // Optimal ratio of interactive to total elements
    const interactivityRatio = interactiveElements / Math.max(1, totalElements);
    const optimalRatio = 0.1; // 10% interactive elements is good
    
    return Math.max(0.3, 1 - Math.abs(interactivityRatio - optimalRatio) * 5);
  }

  private calculateRelationalCoherence(headings: NodeListOf<Element>, paragraphs: NodeListOf<Element>, images: NodeListOf<Element>): number {
    const contentBalance = Math.min(1, paragraphs.length / Math.max(1, headings.length * 3));
    const mediaBalance = images.length > 0 ? Math.min(1, paragraphs.length / images.length) : 0.8;
    
    return (contentBalance * 0.6 + mediaBalance * 0.4);
  }
}

// Export for use in browser
export default NovaAgentBridge;
