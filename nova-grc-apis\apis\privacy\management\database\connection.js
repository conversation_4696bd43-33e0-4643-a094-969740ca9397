/**
 * Database Connection Module
 * 
 * This module provides a connection pool for MongoDB.
 * It uses connection pooling to improve performance and reliability.
 */

const { MongoClient } = require('mongodb');
const logger = require('../logging');
const { createIndexes } = require('./indexes');

// Default connection options
const DEFAULT_POOL_SIZE = 10;
const DEFAULT_CONNECT_TIMEOUT_MS = 30000;
const DEFAULT_SOCKET_TIMEOUT_MS = 45000;

// Connection instance
let client;
let db;

/**
 * Initialize the database connection
 * @param {Object} options - Connection options
 * @param {string} options.uri - MongoDB connection URI
 * @param {string} options.dbName - Database name
 * @param {number} options.poolSize - Connection pool size
 * @param {number} options.connectTimeoutMS - Connection timeout in milliseconds
 * @param {number} options.socketTimeoutMS - Socket timeout in milliseconds
 * @param {boolean} options.createIndexes - Whether to create indexes on startup
 * @returns {Promise<Object>} - MongoDB database instance
 */
async function connect(options = {}) {
  try {
    const {
      uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/privacy-management',
      dbName = process.env.MONGODB_DB_NAME || 'privacy-management',
      poolSize = parseInt(process.env.MONGODB_POOL_SIZE) || DEFAULT_POOL_SIZE,
      connectTimeoutMS = parseInt(process.env.MONGODB_CONNECT_TIMEOUT_MS) || DEFAULT_CONNECT_TIMEOUT_MS,
      socketTimeoutMS = parseInt(process.env.MONGODB_SOCKET_TIMEOUT_MS) || DEFAULT_SOCKET_TIMEOUT_MS,
      createIndexesOnStartup = process.env.MONGODB_CREATE_INDEXES === 'true' || false
    } = options;
    
    // Create MongoDB client
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      poolSize,
      connectTimeoutMS,
      socketTimeoutMS
    });
    
    // Connect to MongoDB
    await client.connect();
    
    // Get database instance
    db = client.db(dbName);
    
    logger.info('Connected to MongoDB', { uri, dbName, poolSize });
    
    // Create indexes if enabled
    if (createIndexesOnStartup) {
      await createIndexes(db);
    }
    
    return db;
  } catch (error) {
    logger.error('Failed to connect to MongoDB', { error: error.message });
    throw error;
  }
}

/**
 * Get the database instance
 * @returns {Object} - MongoDB database instance
 */
function getDb() {
  if (!db) {
    throw new Error('Database not initialized. Call connect() first.');
  }
  return db;
}

/**
 * Close the database connection
 * @returns {Promise<void>}
 */
async function close() {
  if (client) {
    await client.close();
    logger.info('Disconnected from MongoDB');
  }
}

/**
 * Get database statistics
 * @returns {Promise<Object>} - Database statistics
 */
async function getStats() {
  try {
    if (!db) {
      throw new Error('Database not initialized. Call connect() first.');
    }
    
    const stats = await db.stats();
    return stats;
  } catch (error) {
    logger.error('Failed to get database statistics', { error: error.message });
    throw error;
  }
}

/**
 * Check database connection health
 * @returns {Promise<Object>} - Health check result
 */
async function healthCheck() {
  try {
    if (!db) {
      return {
        status: 'error',
        message: 'Database not initialized'
      };
    }
    
    // Ping the database
    await db.command({ ping: 1 });
    
    return {
      status: 'ok',
      message: 'Database connection is healthy'
    };
  } catch (error) {
    logger.error('Database health check failed', { error: error.message });
    
    return {
      status: 'error',
      message: `Database connection error: ${error.message}`
    };
  }
}

module.exports = {
  connect,
  getDb,
  close,
  getStats,
  healthCheck
};
