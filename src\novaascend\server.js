const express = require('express');
const app = express();
const port = 3020;

const api = require('./throne-api');

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'NovaAscend',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// Mount the main API
app.use('/', api);

app.listen(port, () => {
  console.log(`NovaAscend server running on port ${port}`);
});
