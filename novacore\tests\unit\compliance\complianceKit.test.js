/**
 * Compliance Kit Unit Tests
 */

const { expect } = require('chai');
const sinon = require('sinon');
const complianceKit = require('../../../api/services/complianceKit');
const badgeSystem = require('../../../api/services/badgeSystem');

describe('Compliance Kit Service', () => {
  describe('generateCompliancePage', () => {
    let getOrganizationStub;
    let getComplianceStatusStub;
    
    beforeEach(() => {
      // Stub the getOrganization method
      getOrganizationStub = sinon.stub(complianceKit, 'getOrganization').resolves({
        id: 'org123',
        name: 'Test Organization',
        website: 'https://example.com',
        industry: 'Technology',
        size: 'Small',
        createdAt: new Date().toISOString()
      });
      
      // Stub the getComplianceStatus method
      getComplianceStatusStub = sinon.stub(badgeSystem, 'getComplianceStatus').resolves('compliant');
    });
    
    afterEach(() => {
      // Restore the stubs
      getOrganizationStub.restore();
      getComplianceStatusStub.restore();
    });
    
    it('should generate a compliance page with default options', async () => {
      const options = {
        organizationId: 'org123'
      };
      
      const markdown = await complianceKit.generateCompliancePage(options);
      
      expect(markdown).to.be.a('string');
      expect(markdown).to.include('# Test Organization Compliance Status');
      expect(markdown).to.include('## Introduction');
      expect(markdown).to.include('## Overall Compliance Status');
      expect(markdown).to.include('## Framework Compliance');
      expect(markdown).to.include('## Verification');
      
      // Verify that the stubs were called
      expect(getOrganizationStub.calledOnce).to.be.true;
      expect(getComplianceStatusStub.called).to.be.true;
    });
    
    it('should include compliance history if requested', async () => {
      const options = {
        organizationId: 'org123',
        includeHistory: true
      };
      
      const markdown = await complianceKit.generateCompliancePage(options);
      
      expect(markdown).to.include('## Compliance History');
      expect(markdown).to.include('| Date | Overall Status | Details |');
    });
    
    it('should include evidence if requested', async () => {
      const options = {
        organizationId: 'org123',
        includeEvidence: true
      };
      
      const markdown = await complianceKit.generateCompliancePage(options);
      
      expect(markdown).to.include('## Compliance Evidence');
      expect(markdown).to.include('The following evidence is available to authorized parties:');
    });
    
    it('should include custom sections if provided', async () => {
      const options = {
        organizationId: 'org123',
        customSections: [
          {
            title: 'Custom Section',
            content: 'This is a custom section.'
          }
        ]
      };
      
      const markdown = await complianceKit.generateCompliancePage(options);
      
      expect(markdown).to.include('## Custom Section');
      expect(markdown).to.include('This is a custom section.');
    });
    
    it('should throw an error for invalid options', async () => {
      try {
        await complianceKit.generateCompliancePage(null);
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid compliance page options');
      }
      
      try {
        await complianceKit.generateCompliancePage({});
        // Should not reach here
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Invalid compliance page options');
      }
    });
  });
  
  describe('generateComplianceOverview', () => {
    let getOrganizationStub;
    let getComplianceStatusStub;
    let getComplianceHistoryStub;
    let getEvidenceSummaryStub;
    
    beforeEach(() => {
      // Stub the getOrganization method
      getOrganizationStub = sinon.stub(complianceKit, 'getOrganization').resolves({
        id: 'org123',
        name: 'Test Organization',
        website: 'https://example.com',
        industry: 'Technology',
        size: 'Small',
        createdAt: new Date().toISOString()
      });
      
      // Stub the getComplianceStatus method
      getComplianceStatusStub = sinon.stub(badgeSystem, 'getComplianceStatus').resolves('compliant');
      
      // Stub the getComplianceHistory method
      getComplianceHistoryStub = sinon.stub(complianceKit, 'getComplianceHistory').resolves([
        {
          id: 'history-1',
          date: new Date().toISOString(),
          status: 'compliant',
          details: 'Compliance assessment'
        }
      ]);
      
      // Stub the getEvidenceSummary method
      getEvidenceSummaryStub = sinon.stub(complianceKit, 'getEvidenceSummary').resolves({
        total: 45,
        verified: 42,
        pending: 3,
        categories: {
          policies: 12,
          controls: 18,
          assessments: 15
        },
        lastUpdated: new Date().toISOString()
      });
    });
    
    afterEach(() => {
      // Restore the stubs
      getOrganizationStub.restore();
      getComplianceStatusStub.restore();
      getComplianceHistoryStub.restore();
      getEvidenceSummaryStub.restore();
    });
    
    it('should generate a compliance overview', async () => {
      const organizationId = 'org123';
      
      const overview = await complianceKit.generateComplianceOverview(organizationId);
      
      expect(overview).to.be.an('object');
      expect(overview).to.have.property('organization');
      expect(overview.organization).to.have.property('id', 'org123');
      expect(overview.organization).to.have.property('name', 'Test Organization');
      
      expect(overview).to.have.property('compliance');
      expect(overview.compliance).to.have.property('overall');
      expect(overview.compliance).to.have.property('frameworks');
      expect(overview.compliance.frameworks).to.be.an('array');
      expect(overview.compliance).to.have.property('history');
      expect(overview.compliance.history).to.be.an('array');
      expect(overview.compliance).to.have.property('evidence');
      
      expect(overview).to.have.property('verification');
      expect(overview.verification).to.have.property('url');
      expect(overview.verification).to.have.property('lastVerified');
      
      // Verify that the stubs were called
      expect(getOrganizationStub.calledOnce).to.be.true;
      expect(getComplianceStatusStub.called).to.be.true;
      expect(getComplianceHistoryStub.calledOnce).to.be.true;
      expect(getEvidenceSummaryStub.calledOnce).to.be.true;
    });
  });
  
  describe('getComplianceCharter', () => {
    it('should get the compliance charter', async () => {
      const charter = await complianceKit.getComplianceCharter();
      
      expect(charter).to.be.a('string');
      expect(charter).to.include('# NovaFuse Compliance Charter');
      expect(charter).to.include('## Our Commitment');
      expect(charter).to.include('## Core Principles');
      expect(charter).to.include('## Compliance Commitments');
      expect(charter).to.include('## Verification');
      expect(charter).to.include('## Continuous Improvement');
      expect(charter).to.include('## Accountability');
    });
  });
  
  describe('getCyberSafetyFramework', () => {
    it('should get the Cyber-Safety Framework', async () => {
      const framework = await complianceKit.getCyberSafetyFramework();
      
      expect(framework).to.be.an('object');
      expect(framework).to.have.property('name', 'Cyber-Safety Framework');
      expect(framework).to.have.property('version');
      expect(framework).to.have.property('description');
      expect(framework).to.have.property('pillars');
      expect(framework.pillars).to.be.an('array');
      expect(framework.pillars.length).to.equal(4);
      
      // Check pillars
      const pillarNames = framework.pillars.map(p => p.name);
      expect(pillarNames).to.include('Autonomous Policy Enforcement');
      expect(pillarNames).to.include('Evidence-Backed Trust');
      expect(pillarNames).to.include('Self-Healing Infrastructure');
      expect(pillarNames).to.include('Universal Compliance');
      
      expect(framework).to.have.property('benefits');
      expect(framework.benefits).to.be.an('array');
      
      expect(framework).to.have.property('implementation');
      expect(framework.implementation).to.have.property('phases');
      expect(framework.implementation.phases).to.be.an('array');
    });
  });
  
  describe('getBadgeUrls', () => {
    let getComplianceStatusStub;
    
    beforeEach(() => {
      // Stub the getComplianceStatus method
      getComplianceStatusStub = sinon.stub(badgeSystem, 'getComplianceStatus').resolves('compliant');
    });
    
    afterEach(() => {
      // Restore the stub
      getComplianceStatusStub.restore();
    });
    
    it('should get badge URLs for an organization', async () => {
      const organizationId = 'org123';
      
      const badgeUrls = await complianceKit.getBadgeUrls(organizationId);
      
      expect(badgeUrls).to.be.an('object');
      
      // Check frameworks
      const frameworks = Object.keys(badgeUrls);
      expect(frameworks).to.include('soc2');
      expect(frameworks).to.include('gdpr');
      expect(frameworks).to.include('hipaa');
      expect(frameworks).to.include('iso27001');
      expect(frameworks).to.include('nist');
      expect(frameworks).to.include('overall');
      
      // Check styles and sizes
      for (const framework of frameworks) {
        expect(badgeUrls[framework]).to.have.property('flat');
        expect(badgeUrls[framework].flat).to.have.property('small');
        expect(badgeUrls[framework].flat).to.have.property('medium');
        expect(badgeUrls[framework].flat).to.have.property('large');
        
        expect(badgeUrls[framework]).to.have.property('gradient');
        expect(badgeUrls[framework].gradient).to.have.property('small');
        expect(badgeUrls[framework].gradient).to.have.property('medium');
        expect(badgeUrls[framework].gradient).to.have.property('large');
        
        expect(badgeUrls[framework]).to.have.property('3d');
        expect(badgeUrls[framework]['3d']).to.have.property('small');
        expect(badgeUrls[framework]['3d']).to.have.property('medium');
        expect(badgeUrls[framework]['3d']).to.have.property('large');
      }
      
      // Verify that the stub was called
      expect(getComplianceStatusStub.called).to.be.true;
    });
  });
  
  describe('calculateOverallStatus', () => {
    it('should calculate overall status correctly', () => {
      // All compliant
      expect(complianceKit.calculateOverallStatus(['compliant', 'compliant', 'compliant'])).to.equal('compliant');
      
      // Some partial
      expect(complianceKit.calculateOverallStatus(['compliant', 'partial', 'compliant'])).to.equal('partial');
      
      // Some unknown
      expect(complianceKit.calculateOverallStatus(['compliant', 'unknown', 'compliant'])).to.equal('partial');
      
      // Some non-compliant
      expect(complianceKit.calculateOverallStatus(['compliant', 'noncompliant', 'compliant'])).to.equal('noncompliant');
      
      // Mixed
      expect(complianceKit.calculateOverallStatus(['compliant', 'partial', 'noncompliant'])).to.equal('noncompliant');
    });
  });
});
