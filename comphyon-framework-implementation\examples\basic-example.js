/**
 * Basic Example
 * 
 * This example demonstrates the basic usage of the Comphyon Framework.
 */

const {
  TensorOperator,
  FusionOperator,
  CircularTrustTopology,
  UUFT,
  createFrameworkSystem,
  calculateComphyon,
  calculateSimplifiedComphyon,
  applyUUFT,
  apply1882Principle
} = require('../index');

// Create framework components
console.log('Creating framework components...');
const tensorOperator = new TensorOperator();
const fusionOperator = new FusionOperator();
const circularTrustTopology = new CircularTrustTopology();
const uuft = new UUFT();

// Example vectors
const vectorA = [0.7, 0.8, 0.9];
const vectorB = [0.6, 0.5, 0.4];
const vectorC = [0.3, 0.2, 0.1];

// Example domain data
const domainData = {
  cyber: 0.7,
  financial: 0.8,
  biological: 0.9
};

// Example domain energies
const domainEnergies = {
  csde: 1.05, // A1×D
  csfe: 1.6,  // A2×P
  csme: 1.62  // T×I
};

// Example 1: Tensor operations
console.log('\nExample 1: Tensor operations');
const tensorProduct = tensorOperator.product(vectorA, vectorB);
console.log('Tensor product:', tensorProduct);

const outerProduct = tensorOperator.outerProduct(vectorA, vectorB);
console.log('Outer product:');
outerProduct.forEach(row => console.log(row));

const innerProduct = tensorOperator.innerProduct(vectorA, vectorB);
console.log('Inner product:', innerProduct);

// Example 2: Fusion operations
console.log('\nExample 2: Fusion operations');
const fusionResult = fusionOperator.fuse(vectorA, vectorB);
console.log('Fusion result:', fusionResult);

const synergy = fusionOperator.synergy(vectorA, vectorB);
console.log('Synergy score:', synergy);

const correlation = fusionOperator.correlation(vectorA, vectorB);
console.log('Correlation:', correlation);

// Example 3: Circular trust topology
console.log('\nExample 3: Circular trust topology');
const trustScore = circularTrustTopology.calculate(vectorA);
console.log('Trust score:', trustScore);

const trustMatrix = circularTrustTopology.calculateMatrix([vectorA, vectorB, vectorC]);
console.log('Trust matrix:');
trustMatrix.forEach(row => console.log(row));

const wilsonLoop = circularTrustTopology.wilsonLoop(vectorA);
console.log('Wilson loop:', wilsonLoop);

// Example 4: UUFT formula
console.log('\nExample 4: UUFT formula');
const uuftResult = uuft.apply(vectorA, vectorB, vectorC);
console.log('UUFT result:', uuftResult);

const uuft1882Result = uuft.applyWith1882Principle(vectorA, vectorB, vectorC);
console.log('UUFT with 18/82 principle:', uuft1882Result);

// Example 5: Comphyon calculation
console.log('\nExample 5: Comphyon calculation');
const comphyonValue = uuft.calculateComphyon(domainEnergies);
console.log('Comphyon value:', comphyonValue);

const simplifiedComphyonValue = uuft.calculateSimplifiedComphyon(domainData);
console.log('Simplified Comphyon value:', simplifiedComphyonValue);

// Example 6: Nested trinity
console.log('\nExample 6: Nested trinity');
const nestedTrinityData = {
  micro: {
    cyber: vectorA,
    financial: vectorB,
    biological: vectorC
  },
  meso: {
    cyber: [0.65, 0.75, 0.85],
    financial: [0.55, 0.45, 0.35],
    biological: [0.25, 0.15, 0.05]
  },
  macro: {
    cyber: [0.6, 0.7, 0.8],
    financial: [0.5, 0.4, 0.3],
    biological: [0.2, 0.1, 0.0]
  }
};

const nestedTrinityMetrics = uuft.calculateNestedTrinity(nestedTrinityData);
console.log('Nested trinity metrics:');
console.log(JSON.stringify(nestedTrinityMetrics, null, 2));

// Example 7: Using convenience functions
console.log('\nExample 7: Using convenience functions');
const comphyonValueDirect = calculateComphyon(domainEnergies);
console.log('Comphyon value (direct):', comphyonValueDirect);

const simplifiedComphyonValueDirect = calculateSimplifiedComphyon(domainData);
console.log('Simplified Comphyon value (direct):', simplifiedComphyonValueDirect);

const uuftResultDirect = applyUUFT(vectorA, vectorB, vectorC);
console.log('UUFT result (direct):', uuftResultDirect);

const principle1882Result = apply1882Principle(vectorA, vectorB);
console.log('18/82 principle result:', principle1882Result);

// Example 8: Using the framework system
console.log('\nExample 8: Using the framework system');
const frameworkSystem = createFrameworkSystem();

const tensorProductSystem = frameworkSystem.tensorOperator.product(vectorA, vectorB);
console.log('Tensor product (system):', tensorProductSystem);

const fusionResultSystem = frameworkSystem.fusionOperator.fuse(vectorA, vectorB);
console.log('Fusion result (system):', fusionResultSystem);

const trustScoreSystem = frameworkSystem.circularTrustTopology.calculate(vectorA);
console.log('Trust score (system):', trustScoreSystem);

const uuftResultSystem = frameworkSystem.uuft.apply(vectorA, vectorB, vectorC);
console.log('UUFT result (system):', uuftResultSystem);

console.log('\nBasic example completed successfully!');
