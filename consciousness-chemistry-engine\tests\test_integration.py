"""
Integration tests for the ConsciousNovaFold pipeline.
"""
import os
import sys
import unittest
import tempfile
import numpy as np
from pathlib import Path
from Bio.PDB import PDBIO, PDBParser, Structure, Model, Chain, Residue, Atom

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.ConsciousNovaFold import ConsciousNovaFold, NovaFoldClient
from src.evolutionary_analysis import EvolutionaryAnalyzer
from src.domain_analysis import DomainAnalyzer
from src.visualization import ProteinVisualizer

class TestConsciousNovaFoldIntegration(unittest.TestCase):
    """Integration tests for the full pipeline."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.test_sequence = "ACDEFGHIKLMNPQRSTVWY"
        cls.temp_dir = tempfile.mkdtemp()
        
        # Initialize components
        cls.novafold = NovaFoldClient()
        cls.cn = ConsciousNovaFold(cls.novafold)
        cls.evo_analyzer = EvolutionaryAnalyzer()
        cls.domain_analyzer = DomainAnalyzer()
        cls.visualizer = ProteinVisualizer()
    
    def test_full_pipeline(self):
        """Test the full pipeline from sequence to analysis."""
        # 1. Run ConsciousNovaFold
        result = self.cn.fold(self.test_sequence)
        
        # Verify basic structure
        self.assertIn('sequence', result)
        self.assertIn('structure', result)
        self.assertIn('metrics', result)
        
        # Check metrics structure
        metrics = result['metrics']
        self.assertIn('psi_score', metrics)
        self.assertIn('fibonacci_analysis', metrics)
        self.assertIn('trinity_validation', metrics)
        
        # 2. Test evolutionary analysis
        psi_scores = self.evo_analyzer.predict_functional_sites(self.test_sequence)
        self.assertEqual(len(psi_scores), len(self.test_sequence))
        self.assertTrue(all(0 <= v <= 1 for v in psi_scores.values()))
        
        # 3. Test domain analysis
        domain_analysis = self.domain_analyzer.analyze_domain_fibonacci(self.test_sequence)
        self.assertIn('domains', domain_analysis)
        self.assertIn('average_alignment', domain_analysis)
        
        # 4. Test visualization (just that it runs without errors)
        # Skip PyMOL tests if not available
        try:
            # Create a temporary PDB file for testing
            pdb_file = os.path.join(self.temp_dir, "test.pdb")
            with open(pdb_file, 'w') as f:
                f.write("""
HEADER    TEST
COMPND    TEST
AUTHOR    TEST
KEYWDS    TEST
EXPDTA    THEORETICAL MODEL, MODELLER 10.4
REMARK   1 FILE GENERATED BY TEST
REMARK   1
REMARK   1  REL;   1
REMARK 350 BIOMOLECULE: 1
REMARK 350 APPLY THE FOLLOWING TO CHAINS: A
REMARK 350   BIOMT1   1  1.000000  0.000000  0.000000        0.00000
REMARK 350   BIOMT2   1  0.000000  1.000000  0.000000        0.00000
REMARK 350   BIOMT3   1  0.000000  0.000000  1.000000        0.00000
REMARK 350   BIOMT1   2 -1.000000  0.000000  0.000000        0.00000
REMARK 350   BIOMT2   2  0.000000  1.000000  0.000000        0.00000
REMARK 350   BIOMT3   2  0.000000  0.000000 -1.00000        0.00000
SEQRES   1 A   20  ALA CYS ASP GLU PHE GLY HIS ILE LYS LEU MET ASN PRO GLN ARG SER THR VAL TRP TYR
MODEL        1
ATOM      1  N   ALA A   1       1.000   1.000   1.000  1.00  0.00           N  
ATOM      2  CA  ALA A   1       1.000   1.000   1.000  1.00  0.00           C  
ATOM      3  C   ALA A   1       1.000   1.000   1.000  1.00  0.00           C  
ATOM      4  O   ALA A   1       1.000   1.000   1.000  1.00  0.00           O  
ATOM      5  CB  ALA A   1       1.000   1.000   1.000  1.00  0.00           C  
TER       6      ALA A   1
ENDMDL
""")
            
            # Test visualization with mock psi scores
            # This will test if the visualization function can be called
            # without actually requiring PyMOL to be installed
            mock_psi_scores = {i+1: 0.5 for i in range(len(self.test_sequence))}
            try:
                obj_name = self.visualizer.color_by_psi_scores(pdb_file, mock_psi_scores)
                self.assertIsNotNone(obj_name)
            except ImportError:
                # PyMOL not available, which is fine for testing
                pass
                
        except Exception as e:
            # Skip any errors in the visualization test
            print(f"Visualization test skipped: {str(e)}")
    
    def test_consciousness_metrics(self):
        """Test that consciousness metrics are calculated correctly."""
        result = self.cn.fold(self.test_sequence)
        
        # Check that the result has the expected structure
        self.assertIn('sequence', result)
        self.assertIn('structure', result)
        self.assertIn('metrics', result)
        
        metrics = result['metrics']
        
        # Check that all expected metrics are present
        self.assertIn('psi_score', metrics)
        self.assertIn('fibonacci_analysis', metrics)
        self.assertIn('fibonacci_alignment', metrics)
        self.assertIn('trinity_validation', metrics)
        self.assertIn('trinity_report', metrics)
        
        # Check that metrics have valid values
        self.assertIsInstance(metrics['psi_score'], (int, float, np.floating))
        self.assertIsInstance(metrics['fibonacci_analysis'], dict)
        self.assertIsInstance(metrics['fibonacci_alignment'], dict)
        self.assertIsInstance(metrics['trinity_validation'], dict)
        self.assertIsInstance(metrics['trinity_report'], (str, dict))
        
        fib = metrics['fibonacci_alignment']
        self.assertIn('difference', fib)
        self.assertIn('alignment_score', fib)
        self.assertTrue(0 <= fib['alignment_score'] <= 1)
        
        # Check Trinity validation
        trinity = metrics['trinity_validation']
        for key in ['ners', 'nepi', 'nefc']:
            self.assertIn(key, trinity)
            # Check if the value is a dictionary (old format) or direct value (new format)
            if isinstance(trinity[key], dict):
                self.assertIn('score', trinity[key])
                self.assertIn('passed', trinity[key])
                self.assertIsInstance(trinity[key]['score'], (int, float, np.floating))
                self.assertIsInstance(trinity[key]['passed'], bool)
            else:
                # New format - direct numeric values
                self.assertIsInstance(trinity[key], (int, float, np.floating))
    
    def test_domain_analysis(self):
        """Test domain analysis functionality."""
        # Test with a sequence that should have multiple domains
        long_sequence = self.test_sequence * 5
        analysis = self.domain_analyzer.analyze_domain_fibonacci(long_sequence)
        
        # Should find multiple domains
        self.assertGreaterEqual(len(analysis['domains']), 2)
        
        # Check domain structure
        for domain in analysis['domains']:
            self.assertIn('name', domain)
            self.assertIn('start', domain)
            self.assertIn('end', domain)
            self.assertIn('alignment_score', domain)
            self.assertTrue(0 <= domain['alignment_score'] <= 1)
    
    def test_evolutionary_analysis(self):
        """Test evolutionary analysis functionality."""
        # Test with a known sequence pattern
        conserved_sequence = "ACDEFGHIKLMNPQRSTVWY" * 5
        scores = self.evo_analyzer.predict_functional_sites(conserved_sequence)
        
        # Should return scores for all positions
        self.assertEqual(len(scores), len(conserved_sequence))
        
        # Check that some positions are predicted as functional
        # (with mock data, this will be random, so just check structure)
        self.assertTrue(all(0 <= v <= 1 for v in scores.values()))

if __name__ == "__main__":
    unittest.main()
