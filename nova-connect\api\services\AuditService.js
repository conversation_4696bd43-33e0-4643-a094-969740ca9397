/**
 * Audit Service
 *
 * This service handles audit logging for tracking user actions.
 * It supports both local file storage and Google BigQuery integration.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Import BigQuery if available
let BigQuery;
try {
  BigQuery = require('@google-cloud/bigquery').BigQuery;
} catch (error) {
  // BigQuery package not available, will use local storage only
  console.log('BigQuery package not available, using local storage only');
}

class AuditService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.auditDir = path.join(this.dataDir, 'audit');
    this.auditFile = path.join(this.auditDir, 'audit_log.json');
    this.retentionDays = 90; // Keep audit logs for 90 days

    // Initialize BigQuery if available
    this.bigQueryEnabled = process.env.BIGQUERY_ENABLED === 'true';
    this.projectId = process.env.GCP_PROJECT_ID;
    this.datasetId = process.env.BIGQUERY_DATASET_ID || 'novafuse_audit';
    this.tableId = process.env.BIGQUERY_TABLE_ID || 'events';

    if (this.bigQueryEnabled && this.projectId && BigQuery) {
      try {
        this.bigquery = new BigQuery({
          projectId: this.projectId
        });
        console.log('BigQuery integration enabled');
      } catch (error) {
        console.error('Error initializing BigQuery:', error);
      }
    }

    this.ensureDataDir();

    // Clean up old audit logs once a day
    setInterval(() => this.cleanupOldLogs(), 24 * 60 * 60 * 1000);
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.auditDir, { recursive: true });

      // Initialize audit log file if it doesn't exist
      try {
        await fs.access(this.auditFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, create it with empty array
          await fs.writeFile(this.auditFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.error('Error creating audit directory:', error);
      throw error;
    }
  }

  /**
   * Load audit logs from file
   */
  async loadAuditLogs() {
    try {
      const data = await fs.readFile(this.auditFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading audit logs:', error);
      throw error;
    }
  }

  /**
   * Save audit logs to file
   */
  async saveAuditLogs(logs) {
    try {
      await fs.writeFile(this.auditFile, JSON.stringify(logs, null, 2));
    } catch (error) {
      console.error('Error saving audit logs:', error);
      throw error;
    }
  }

  /**
   * Log an audit event
   */
  async logEvent(data) {
    try {
      // Create audit log entry
      const logEntry = {
        id: uuidv4(),
        timestamp: new Date().toISOString(),
        userId: data.userId || null,
        action: data.action,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        details: data.details || null,
        ip: data.ip || null,
        userAgent: data.userAgent || null,
        status: data.status || 'success',
        teamId: data.teamId || null,
        environmentId: data.environmentId || null,
        tenantId: data.tenantId || null
      };

      // Log to BigQuery if enabled
      if (this.bigQueryEnabled && this.bigquery) {
        try {
          await this.logToBigQuery(logEntry);
        } catch (bigQueryError) {
          console.error('Error logging to BigQuery:', bigQueryError);
          // Continue with local logging even if BigQuery fails
        }
      }

      // Log to local file
      const auditLogs = await this.loadAuditLogs();
      auditLogs.push(logEntry);

      // Limit the size of the audit logs
      if (auditLogs.length > 10000) {
        auditLogs.splice(0, auditLogs.length - 10000);
      }

      await this.saveAuditLogs(auditLogs);

      return logEntry;
    } catch (error) {
      console.error('Error logging audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Log a tenant-specific audit event
   */
  async logTenantEvent(tenantId, data) {
    try {
      // Add tenant ID to data
      const tenantData = {
        ...data,
        tenantId
      };

      // Log the event
      const logEntry = await this.logEvent(tenantData);

      // If BigQuery is enabled, also log to tenant-specific table
      if (this.bigQueryEnabled && this.bigquery && tenantId) {
        try {
          // Get tenant-specific dataset
          const dataset = this.bigquery.dataset(`tenant_${tenantId}`);

          // Get audit table (create if it doesn't exist)
          let table;
          try {
            table = dataset.table('audit_logs');
            await table.get();
          } catch (tableError) {
            // Table doesn't exist, create it
            const schema = [
              { name: 'id', type: 'STRING' },
              { name: 'timestamp', type: 'TIMESTAMP' },
              { name: 'userId', type: 'STRING' },
              { name: 'action', type: 'STRING' },
              { name: 'resourceType', type: 'STRING' },
              { name: 'resourceId', type: 'STRING' },
              { name: 'details', type: 'STRING' },
              { name: 'ip', type: 'STRING' },
              { name: 'userAgent', type: 'STRING' },
              { name: 'status', type: 'STRING' },
              { name: 'teamId', type: 'STRING' },
              { name: 'environmentId', type: 'STRING' },
              { name: 'tenantId', type: 'STRING' }
            ];

            const options = {
              schema: schema,
              timePartitioning: {
                type: 'DAY',
                field: 'timestamp'
              }
            };

            await dataset.createTable('audit_logs', options);
            table = dataset.table('audit_logs');
          }

          // Insert into tenant-specific table
          await table.insert([logEntry]);
        } catch (bigQueryError) {
          console.error(`Error logging to tenant-specific BigQuery table for tenant ${tenantId}:`, bigQueryError);
        }
      }

      return logEntry;
    } catch (error) {
      console.error('Error logging tenant audit event:', error);
      // Don't throw error to prevent affecting the main request flow
    }
  }

  /**
   * Log an audit event to BigQuery
   */
  async logToBigQuery(event) {
    try {
      // Get dataset reference
      const dataset = this.bigquery.dataset(this.datasetId);

      // Get table reference
      const table = dataset.table(this.tableId);

      // Insert row
      await table.insert([event]);
    } catch (error) {
      console.error('Error logging to BigQuery:', error);
      throw error;
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(filters = {}) {
    const auditLogs = await this.loadAuditLogs();

    // Apply filters
    let filteredLogs = auditLogs;

    if (filters.startDate) {
      const startDate = new Date(filters.startDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
    }

    if (filters.endDate) {
      const endDate = new Date(filters.endDate);
      filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
    }

    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters.action) {
      filteredLogs = filteredLogs.filter(log => log.action === filters.action);
    }

    if (filters.resourceType) {
      filteredLogs = filteredLogs.filter(log => log.resourceType === filters.resourceType);
    }

    if (filters.resourceId) {
      filteredLogs = filteredLogs.filter(log => log.resourceId === filters.resourceId);
    }

    if (filters.status) {
      filteredLogs = filteredLogs.filter(log => log.status === filters.status);
    }

    if (filters.teamId) {
      filteredLogs = filteredLogs.filter(log => log.teamId === filters.teamId);
    }

    if (filters.environmentId) {
      filteredLogs = filteredLogs.filter(log => log.environmentId === filters.environmentId);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    // Apply pagination
    const page = filters.page || 1;
    const limit = filters.limit || 100;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;

    const paginatedLogs = filteredLogs.slice(startIndex, endIndex);

    return {
      logs: paginatedLogs,
      total: filteredLogs.length,
      page,
      limit,
      totalPages: Math.ceil(filteredLogs.length / limit)
    };
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(id) {
    const auditLogs = await this.loadAuditLogs();
    const log = auditLogs.find(log => log.id === id);

    if (!log) {
      throw new Error(`Audit log with ID ${id} not found`);
    }

    return log;
  }

  /**
   * Get audit logs for a resource
   */
  async getAuditLogsForResource(resourceType, resourceId) {
    const auditLogs = await this.loadAuditLogs();

    // Filter logs for the resource
    const resourceLogs = auditLogs.filter(log =>
      log.resourceType === resourceType && log.resourceId === resourceId
    );

    // Sort by timestamp (newest first)
    resourceLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return resourceLogs;
  }

  /**
   * Get audit logs for a user
   */
  async getAuditLogsForUser(userId) {
    const auditLogs = await this.loadAuditLogs();

    // Filter logs for the user
    const userLogs = auditLogs.filter(log => log.userId === userId);

    // Sort by timestamp (newest first)
    userLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return userLogs;
  }

  /**
   * Get audit logs for a team
   */
  async getAuditLogsForTeam(teamId) {
    const auditLogs = await this.loadAuditLogs();

    // Filter logs for the team
    const teamLogs = auditLogs.filter(log => log.teamId === teamId);

    // Sort by timestamp (newest first)
    teamLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    return teamLogs;
  }

  /**
   * Clean up old audit logs
   */
  async cleanupOldLogs() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionDays);

      const auditLogs = await this.loadAuditLogs();
      const filteredLogs = auditLogs.filter(log =>
        new Date(log.timestamp) >= cutoffDate
      );

      if (filteredLogs.length < auditLogs.length) {
        await this.saveAuditLogs(filteredLogs);
        console.log(`Cleaned up audit logs older than ${cutoffDate.toISOString()}`);
      }
    } catch (error) {
      console.error('Error cleaning up old audit logs:', error);
    }
  }

  /**
   * Create audit middleware
   */
  createAuditMiddleware() {
    return (req, res, next) => {
      // Store original end method
      const originalEnd = res.end;

      // Override end method to capture response
      res.end = function(chunk, encoding) {
        // Restore original end method
        res.end = originalEnd;

        // Call original end method
        res.end(chunk, encoding);

        // Skip audit logging for certain paths
        if (req.path.startsWith('/health') || req.path.startsWith('/api/monitoring/health')) {
          return;
        }

        // Log audit event
        const auditData = {
          userId: req.user ? req.user.id : null,
          action: req.method,
          resourceType: req.path.split('/')[2] || 'unknown',
          resourceId: req.params.id || null,
          details: {
            path: req.path,
            query: req.query,
            body: req.method !== 'GET' ? req.body : null
          },
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          status: res.statusCode >= 400 ? 'failure' : 'success',
          teamId: req.headers['x-team-id'] || null,
          environmentId: req.headers['x-environment-id'] || null,
          tenantId: req.headers['x-tenant-id'] || null
        };

        // If tenant ID is present, use tenant-specific logging
        if (req.headers['x-tenant-id']) {
          this.logTenantEvent(req.headers['x-tenant-id'], auditData);
        } else {
          this.logEvent(auditData);
        }
      };

      next();
    };
  }
}

module.exports = AuditService;
