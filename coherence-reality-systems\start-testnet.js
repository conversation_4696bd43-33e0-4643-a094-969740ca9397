const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const config = require('./testnet-config');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Start each node in a separate process
config.nodes.forEach(node => {
  const nodeConfig = config.getNodeConfig(node.id);
  
  // Create a directory for this node's data
  const nodeDir = path.join(__dirname, `data/node-${node.id}`);
  if (!fs.existsSync(nodeDir)) {
    fs.mkdirSync(nodeDir, { recursive: true });
  }
  
  // Save node config to a file
  const configPath = path.join(nodeDir, 'config.json');
  fs.writeFileSync(configPath, JSON.stringify(nodeConfig, null, 2));
  
  // Start the node
  const nodeProcess = exec(
    `node kether-server-enhanced.js --config ${configPath}`,
    {
      cwd: __dirname,
      env: {
        ...process.env,
        NODE_ID: node.id,
        PORT: node.port,
        RPC_PORT: node.rpcPort,
        WS_PORT: node.wsPort,
        PRIVATE_KEY: node.privateKey,
        VALIDATOR_ADDRESS: node.address,
        PEERS: node.peerAddresses.join(',')
      }
    }
  );
  
  // Log output to files
  const logStream = fs.createWriteStream(path.join(logsDir, `node-${node.id}.log`));
  nodeProcess.stdout.pipe(logStream);
  nodeProcess.stderr.pipe(logStream);
  
  // Handle process exit
  nodeProcess.on('exit', (code) => {
    console.log(`Node ${node.id} exited with code ${code}`);
  });
  
  console.log(`Started node ${node.id} (${node.name}) on port ${node.port}`);
  console.log(`  RPC: http://localhost:${node.rpcPort}`);
  console.log(`  WS: ws://localhost:${node.wsPort}`);
  console.log(`  Logs: ${path.join(logsDir, `node-${node.id}.log`)}`);
  console.log(`  Data: ${nodeDir}`);
  console.log('');
});

console.log('Testnet started! Press Ctrl+C to stop all nodes.');

// Handle process termination
process.on('SIGINT', () => {
  console.log('\nStopping testnet...');
  process.exit(0);
});
