/**
 * TreemapVisualization Component
 * 
 * A component for visualizing hierarchical data as a treemap.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme';

/**
 * TreemapVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Treemap data
 * @param {string} props.data.name - Root node name
 * @param {number} [props.data.value] - Root node value
 * @param {Array} [props.data.children] - Child nodes
 * @param {Object} [props.options] - Visualization options
 * @param {string} [props.options.title] - Treemap title
 * @param {Array} [props.options.colorScale] - Color scale for the treemap
 * @param {boolean} [props.options.showTooltip=true] - Whether to show tooltips
 * @param {boolean} [props.options.showLabels=true] - Whether to show cell labels
 * @param {Function} [props.options.formatValue] - Function to format cell values
 * @param {Function} [props.onCellClick] - Function to call when a cell is clicked
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} TreemapVisualization component
 */
const TreemapVisualization = ({
  data,
  options = {},
  onCellClick,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const containerRef = useRef(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [tooltip, setTooltip] = useState({ visible: false, x: 0, y: 0, content: '' });
  const [treemapData, setTreemapData] = useState(null);
  
  // Default options
  const {
    title,
    colorScale = [
      theme.colors.primary,
      theme.colors.secondary,
      theme.colors.success,
      theme.colors.warning,
      theme.colors.error,
      theme.colors.info
    ],
    showTooltip = true,
    showLabels = true,
    formatValue = (value) => value.toLocaleString()
  } = options;
  
  // Update dimensions when container size changes
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      const { width, height } = containerRef.current.getBoundingClientRect();
      setDimensions({ width, height });
    };
    
    // Initial update
    updateDimensions();
    
    // Add resize observer
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);
    
    // Cleanup
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);
  
  // Process data
  useEffect(() => {
    if (!data || !dimensions.width || !dimensions.height) return;
    
    // Calculate treemap layout
    const treemap = calculateTreemap(data, {
      width: dimensions.width,
      height: dimensions.height,
      padding: 2
    });
    
    setTreemapData(treemap);
  }, [data, dimensions]);
  
  // Calculate treemap layout
  const calculateTreemap = (rootNode, options) => {
    const { width, height, padding } = options;
    
    // Clone data to avoid modifying the original
    const root = { ...rootNode };
    
    // Calculate total value
    const calculateValue = (node) => {
      if (!node.children || node.children.length === 0) {
        return node.value || 0;
      }
      
      const childrenValue = node.children.reduce((sum, child) => sum + calculateValue(child), 0);
      node.value = childrenValue;
      return childrenValue;
    };
    
    // Calculate value if not provided
    if (!root.value) {
      calculateValue(root);
    }
    
    // Squarify algorithm
    const squarify = (children, rectangle) => {
      if (!children || children.length === 0) return [];
      
      const { x, y, width, height } = rectangle;
      
      // Sort children by value (descending)
      const sortedChildren = [...children].sort((a, b) => b.value - a.value);
      
      // Calculate total value
      const totalValue = sortedChildren.reduce((sum, child) => sum + child.value, 0);
      
      // Layout direction
      const isVertical = width < height;
      
      // Available space
      const availableSpace = isVertical ? width : height;
      
      // Result
      const result = [];
      
      // Current position
      let currentX = x;
      let currentY = y;
      let remainingWidth = width;
      let remainingHeight = height;
      
      // Process children
      let currentRow = [];
      let currentRowValue = 0;
      
      for (let i = 0; i < sortedChildren.length; i++) {
        const child = sortedChildren[i];
        const childValue = child.value;
        
        // Check if adding this child improves the aspect ratio
        const newRowValue = currentRowValue + childValue;
        const worstRatio = calculateWorstRatio(currentRow, currentRowValue, availableSpace, totalValue);
        const newWorstRatio = calculateWorstRatio([...currentRow, child], newRowValue, availableSpace, totalValue);
        
        if (currentRow.length > 0 && newWorstRatio > worstRatio) {
          // Layout current row
          layoutRow(currentRow, currentRowValue, {
            x: currentX,
            y: currentY,
            width: isVertical ? remainingWidth : width,
            height: isVertical ? height : remainingHeight
          }, totalValue, isVertical, padding, result);
          
          // Update position
          if (isVertical) {
            currentY += (currentRowValue / totalValue) * height;
            remainingHeight -= (currentRowValue / totalValue) * height;
          } else {
            currentX += (currentRowValue / totalValue) * width;
            remainingWidth -= (currentRowValue / totalValue) * width;
          }
          
          // Reset row
          currentRow = [child];
          currentRowValue = childValue;
        } else {
          // Add to current row
          currentRow.push(child);
          currentRowValue += childValue;
        }
      }
      
      // Layout remaining row
      if (currentRow.length > 0) {
        layoutRow(currentRow, currentRowValue, {
          x: currentX,
          y: currentY,
          width: isVertical ? remainingWidth : width,
          height: isVertical ? height : remainingHeight
        }, totalValue, isVertical, padding, result);
      }
      
      return result;
    };
    
    // Calculate worst aspect ratio for a row
    const calculateWorstRatio = (row, rowValue, availableSpace, totalValue) => {
      if (row.length === 0) return Infinity;
      
      const rowSize = (rowValue / totalValue) * availableSpace;
      let minArea = Infinity;
      let maxArea = 0;
      
      for (const child of row) {
        const area = child.value / rowValue;
        minArea = Math.min(minArea, area);
        maxArea = Math.max(maxArea, area);
      }
      
      const minSide = minArea * rowSize;
      const maxSide = maxArea * rowSize;
      
      return Math.max(maxSide / minSide, minSide / maxSide);
    };
    
    // Layout a row of rectangles
    const layoutRow = (row, rowValue, rectangle, totalValue, isVertical, padding, result) => {
      const { x, y, width, height } = rectangle;
      
      // Calculate row size
      const rowSize = (rowValue / totalValue) * (isVertical ? height : width);
      
      // Current position
      let currentPosition = isVertical ? y : x;
      
      // Layout children
      for (const child of row) {
        const childSize = (child.value / rowValue) * rowSize;
        
        // Calculate rectangle
        const childRect = isVertical
          ? {
              x: x + padding,
              y: currentPosition + padding,
              width: width - padding * 2,
              height: childSize - padding * 2
            }
          : {
              x: currentPosition + padding,
              y: y + padding,
              width: childSize - padding * 2,
              height: height - padding * 2
            };
        
        // Add to result
        result.push({
          name: child.name,
          value: child.value,
          ...childRect,
          children: child.children
            ? squarify(child.children, childRect)
            : []
        });
        
        // Update position
        currentPosition += childSize;
      }
    };
    
    // Calculate treemap
    return {
      name: root.name,
      value: root.value,
      x: 0,
      y: 0,
      width,
      height,
      children: squarify(root.children, { x: 0, y: 0, width, height })
    };
  };
  
  // Get color for a node
  const getColor = (node, depth = 0) => {
    if (!node) return theme.colors.divider;
    
    // Use color from node if provided
    if (node.color) return node.color;
    
    // Use color from scale
    return colorScale[depth % colorScale.length];
  };
  
  // Handle cell click
  const handleCellClick = (node) => {
    if (onCellClick) {
      onCellClick(node);
    }
  };
  
  // Handle cell mouse enter
  const handleCellMouseEnter = (event, node) => {
    if (!showTooltip) return;
    
    const rect = event.target.getBoundingClientRect();
    const containerRect = containerRef.current.getBoundingClientRect();
    
    setTooltip({
      visible: true,
      x: rect.left - containerRect.left + rect.width / 2,
      y: rect.top - containerRect.top,
      content: `${node.name}: ${formatValue(node.value)}`
    });
  };
  
  // Handle cell mouse leave
  const handleCellMouseLeave = () => {
    if (!showTooltip) return;
    
    setTooltip({ ...tooltip, visible: false });
  };
  
  // Render treemap cells recursively
  const renderCells = (node, depth = 0) => {
    if (!node) return null;
    
    return (
      <g key={`${node.name}-${depth}`}>
        {/* Current node */}
        <rect
          x={node.x}
          y={node.y}
          width={node.width}
          height={node.height}
          fill={getColor(node, depth)}
          stroke={theme.colors.background}
          strokeWidth={1}
          onClick={() => handleCellClick(node)}
          onMouseEnter={(e) => handleCellMouseEnter(e, node)}
          onMouseLeave={handleCellMouseLeave}
          className="cursor-pointer transition-colors duration-200 hover:opacity-80"
          data-testid={`treemap-cell-${node.name}`}
        />
        
        {/* Label */}
        {showLabels && node.width > 50 && node.height > 30 && (
          <text
            x={node.x + node.width / 2}
            y={node.y + node.height / 2}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-xs pointer-events-none fill-current"
            style={{
              fill: depth % 2 === 0 ? 'white' : 'black'
            }}
          >
            <tspan x={node.x + node.width / 2} dy="-0.5em" fontWeight="bold">
              {node.name}
            </tspan>
            <tspan x={node.x + node.width / 2} dy="1.2em">
              {formatValue(node.value)}
            </tspan>
          </text>
        )}
        
        {/* Child nodes */}
        {node.children && node.children.map(child => renderCells(child, depth + 1))}
      </g>
    );
  };
  
  // Check if data is valid
  if (!data || !data.name) {
    return (
      <div
        ref={containerRef}
        className={`flex items-center justify-center bg-surface border border-divider rounded-md ${className}`}
        style={{ minHeight: '300px', ...style }}
        data-testid="treemap-visualization-empty"
      >
        <p className="text-textSecondary">No data available</p>
      </div>
    );
  }
  
  return (
    <div
      ref={containerRef}
      className={`relative bg-background border border-divider rounded-md overflow-hidden ${className}`}
      style={{ minHeight: '300px', ...style }}
      data-testid="treemap-visualization"
    >
      {/* Title */}
      {title && (
        <div className="absolute top-2 left-0 right-0 text-center">
          <h3 className="text-lg font-medium text-textPrimary">{title}</h3>
        </div>
      )}
      
      {/* SVG Container */}
      <svg
        width={dimensions.width}
        height={dimensions.height}
        className="font-sans text-textPrimary"
      >
        {/* Treemap cells */}
        {treemapData && renderCells(treemapData)}
      </svg>
      
      {/* Tooltip */}
      {tooltip.visible && (
        <div
          className="absolute bg-surface text-textPrimary px-2 py-1 rounded shadow-md text-xs pointer-events-none z-10 border border-divider"
          style={{
            left: tooltip.x,
            top: tooltip.y - 30,
            transform: 'translateX(-50%)'
          }}
          data-testid="treemap-tooltip"
        >
          {tooltip.content}
        </div>
      )}
    </div>
  );
};

TreemapVisualization.propTypes = {
  data: PropTypes.shape({
    name: PropTypes.string.isRequired,
    value: PropTypes.number,
    children: PropTypes.arrayOf(PropTypes.object)
  }),
  options: PropTypes.shape({
    title: PropTypes.string,
    colorScale: PropTypes.arrayOf(PropTypes.string),
    showTooltip: PropTypes.bool,
    showLabels: PropTypes.bool,
    formatValue: PropTypes.func
  }),
  onCellClick: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default TreemapVisualization;
