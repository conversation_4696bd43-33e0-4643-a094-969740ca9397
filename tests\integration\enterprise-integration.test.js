/**
 * NovaConnect Integration Tests - Enterprise Integration
 * 
 * These tests validate NovaConnect's integration with enterprise systems
 * such as Active Directory, SIEM, and change management systems.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics if available
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  authUrl: '/api/auth',
  siemUrl: '/api/integrations/siem',
  changeManagementUrl: '/api/integrations/change-management'
};

// Test data for Active Directory integration
const adTestUser = {
  username: '<EMAIL>',
  groups: ['GRC-Admins', 'Compliance-Officers', 'IT-Security'],
  roles: ['ComplianceAdmin', 'SecurityAuditor']
};

// Test data for SIEM integration
const siemEvent = {
  id: 'siem-event-123',
  type: 'security',
  source: 'firewall',
  severity: 'high',
  timestamp: new Date().toISOString(),
  details: {
    sourceIp: '*************',
    destinationIp: '********',
    port: 22,
    protocol: 'SSH',
    action: 'BLOCK',
    reason: 'Unauthorized access attempt'
  }
};

// Test data for change management integration
const changeRequest = {
  id: 'cr-456',
  type: 'remediation',
  title: 'Update firewall rules for PCI compliance',
  description: 'Add required firewall rules to meet PCI-DSS requirement 1.2.1',
  priority: 'high',
  requester: 'compliance-system',
  affectedSystems: ['payment-gateway', 'card-processing-api'],
  changes: [
    {
      resourceType: 'firewall',
      resourceId: 'fw-123',
      action: 'add-rule',
      parameters: {
        protocol: 'tcp',
        ports: [443],
        sourceRanges: ['10.0.0.0/24'],
        targetTags: ['payment-processing']
      }
    }
  ]
};

describe('Enterprise Integration Tests', () => {
  // Set a longer timeout for integration tests
  jest.setTimeout(30000);
  
  // Test Active Directory integration
  describe('Active Directory Integration', () => {
    it('should authenticate and authorize based on AD groups', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.authUrl}/ad/login`, {
          username: adTestUser.username,
          password: 'test-password' // In a real test, this would be handled securely
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('token');
      expect(result.data).toHaveProperty('user');
      expect(result.data.user).toHaveProperty('groups');
      expect(result.data.user).toHaveProperty('roles');
      
      // Verify groups and roles are correctly mapped
      expect(result.data.user.groups).toEqual(expect.arrayContaining(adTestUser.groups));
      expect(result.data.user.roles).toEqual(expect.arrayContaining(adTestUser.roles));
      
      console.log(`AD authentication completed in ${duration.toFixed(2)} ms`);
      
      // Store token for subsequent tests
      const token = result.data.token;
      
      // Test authorization based on AD groups
      const { result: authzResult, duration: authzDuration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.authUrl}/check-permission`, {
          permission: 'compliance.remediation.approve',
          token
        });
      });
      
      expect(authzResult.status).toBe(200);
      expect(authzResult.data).toHaveProperty('allowed', true);
      expect(authzResult.data).toHaveProperty('reason');
      
      console.log(`AD authorization check completed in ${authzDuration.toFixed(2)} ms`);
    });
    
    it('should handle role-based access control correctly', async () => {
      // First authenticate to get a token
      const authResponse = await axios.post(`${config.baseUrl}${config.authUrl}/ad/login`, {
        username: adTestUser.username,
        password: 'test-password'
      });
      
      const token = authResponse.data.token;
      
      // Test access to different resources based on roles
      const resources = [
        { name: 'compliance-dashboard', expectedAccess: true },
        { name: 'security-controls', expectedAccess: true },
        { name: 'billing-admin', expectedAccess: false }
      ];
      
      for (const resource of resources) {
        const { result, duration } = await measureExecutionTime(async () => {
          return await axios.post(`${config.baseUrl}${config.authUrl}/check-access`, {
            resource: resource.name,
            token
          });
        });
        
        expect(result.status).toBe(200);
        expect(result.data).toHaveProperty('allowed', resource.expectedAccess);
        
        console.log(`Access check for ${resource.name} completed in ${duration.toFixed(2)} ms`);
      }
    });
  });
  
  // Test SIEM integration
  describe('SIEM Integration', () => {
    it('should send compliance events to SIEM', async () => {
      const complianceEvent = {
        id: 'compliance-event-789',
        type: 'compliance',
        framework: 'PCI-DSS',
        control: '1.2.1',
        status: 'failed',
        resource: {
          id: 'fw-123',
          type: 'firewall',
          name: 'payment-gateway-fw'
        },
        timestamp: new Date().toISOString(),
        details: {
          finding: 'Firewall rules do not restrict inbound traffic to only necessary protocols',
          severity: 'high',
          remediation: 'Update firewall rules to restrict traffic to required protocols only'
        }
      };
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.siemUrl}/send-event`, complianceEvent);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('eventId');
      expect(result.data).toHaveProperty('siem');
      
      console.log(`SIEM event sending completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should receive and process security events from SIEM', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.siemUrl}/process-event`, siemEvent);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('processed', true);
      expect(result.data).toHaveProperty('complianceImpact');
      
      // Check if the security event was properly mapped to compliance frameworks
      expect(result.data.complianceImpact).toHaveProperty('frameworks');
      expect(result.data.complianceImpact.frameworks).toContainEqual(
        expect.objectContaining({
          name: expect.stringMatching(/PCI-DSS|NIST|ISO27001/),
          controls: expect.arrayContaining([expect.any(String)])
        })
      );
      
      console.log(`SIEM event processing completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should handle bidirectional SIEM integration efficiently', async () => {
      // Simulate a real-world scenario where a security event triggers compliance actions
      // and those actions are reported back to the SIEM
      
      // Step 1: Receive security event from SIEM
      const { result: processResult } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.siemUrl}/process-event`, siemEvent);
      });
      
      expect(processResult.status).toBe(200);
      expect(processResult.data).toHaveProperty('success', true);
      
      // Step 2: Generate compliance response
      const complianceResponse = {
        id: 'compliance-response-456',
        sourceEventId: siemEvent.id,
        type: 'compliance-action',
        actions: [
          {
            type: 'remediation',
            resource: {
              id: 'fw-123',
              type: 'firewall',
              name: 'internal-fw'
            },
            action: 'update-rules',
            details: {
              ruleUpdates: [
                {
                  action: 'add',
                  protocol: 'tcp',
                  port: 22,
                  sourceIp: '*************',
                  action: 'deny'
                }
              ]
            }
          }
        ],
        timestamp: new Date().toISOString()
      };
      
      // Step 3: Send compliance response back to SIEM
      const { result: responseResult, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.siemUrl}/send-event`, complianceResponse);
      });
      
      expect(responseResult.status).toBe(200);
      expect(responseResult.data).toHaveProperty('success', true);
      
      console.log(`Bidirectional SIEM integration completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test change management integration
  describe('Change Management Integration', () => {
    it('should create change requests for manual approval', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.changeManagementUrl}/create-request`, changeRequest);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('requestId');
      expect(result.data).toHaveProperty('ticketUrl');
      expect(result.data).toHaveProperty('status', 'pending_approval');
      
      console.log(`Change request creation completed in ${duration.toFixed(2)} ms`);
      
      // Store request ID for subsequent tests
      const requestId = result.data.requestId;
      
      // Check change request status
      const { result: statusResult } = await measureExecutionTime(async () => {
        return await axios.get(`${config.baseUrl}${config.changeManagementUrl}/request-status/${requestId}`);
      });
      
      expect(statusResult.status).toBe(200);
      expect(statusResult.data).toHaveProperty('status', 'pending_approval');
      expect(statusResult.data).toHaveProperty('approvers');
      expect(statusResult.data).toHaveProperty('nextApprovalDeadline');
    });
    
    it('should track change request approval workflow', async () => {
      // First create a change request
      const createResponse = await axios.post(`${config.baseUrl}${config.changeManagementUrl}/create-request`, changeRequest);
      const requestId = createResponse.data.requestId;
      
      // Simulate approval process
      const approvalAction = {
        requestId,
        action: 'approve',
        approver: '<EMAIL>',
        comments: 'Approved after security review',
        timestamp: new Date().toISOString()
      };
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.changeManagementUrl}/process-approval`, approvalAction);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('status', 'approved');
      expect(result.data).toHaveProperty('auditTrail');
      expect(result.data.auditTrail).toContainEqual(
        expect.objectContaining({
          action: 'approve',
          approver: approvalAction.approver
        })
      );
      
      console.log(`Change request approval completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should execute approved changes and update status', async () => {
      // First create and approve a change request
      const createResponse = await axios.post(`${config.baseUrl}${config.changeManagementUrl}/create-request`, changeRequest);
      const requestId = createResponse.data.requestId;
      
      await axios.post(`${config.baseUrl}${config.changeManagementUrl}/process-approval`, {
        requestId,
        action: 'approve',
        approver: '<EMAIL>',
        comments: 'Approved after security review',
        timestamp: new Date().toISOString()
      });
      
      // Execute the approved changes
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.changeManagementUrl}/execute-change`, {
          requestId
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('success', true);
      expect(result.data).toHaveProperty('status', 'completed');
      expect(result.data).toHaveProperty('executionDetails');
      expect(result.data).toHaveProperty('complianceEvidence');
      
      console.log(`Change execution completed in ${duration.toFixed(2)} ms`);
    });
  });
});
