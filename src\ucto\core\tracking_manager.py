"""
Tracking Manager for the Universal Compliance Tracking Optimizer.

This module provides functionality for tracking compliance activities and requirements.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrackingManager:
    """
    Manager for compliance tracking.
    
    This class is responsible for tracking compliance activities, requirements,
    and their status.
    """
    
    def __init__(self, tracking_dir: Optional[str] = None):
        """
        Initialize the Tracking Manager.
        
        Args:
            tracking_dir: Path to a directory for storing tracking information
        """
        logger.info("Initializing Tracking Manager")
        
        # Set the tracking directory
        self.tracking_dir = tracking_dir or os.path.join(os.getcwd(), 'tracking_data')
        
        # Create the tracking directory if it doesn't exist
        os.makedirs(self.tracking_dir, exist_ok=True)
        
        # Dictionary to store requirements in memory
        self.requirements: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store activities in memory
        self.activities: Dict[str, Dict[str, Any]] = {}
        
        # Load data from disk
        self._load_data_from_disk()
        
        logger.info(f"Tracking Manager initialized with {len(self.requirements)} requirements and {len(self.activities)} activities")
    
    def create_requirement(self, requirement_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new compliance requirement.
        
        Args:
            requirement_data: The requirement data
            
        Returns:
            The created requirement
            
        Raises:
            ValueError: If the requirement data is invalid
        """
        logger.info("Creating new compliance requirement")
        
        # Validate the requirement data
        self._validate_requirement_data(requirement_data)
        
        # Generate a unique requirement ID
        requirement_id = str(uuid.uuid4())
        
        # Create the requirement object
        requirement = {
            'id': requirement_id,
            'name': requirement_data['name'],
            'description': requirement_data.get('description', ''),
            'framework': requirement_data['framework'],
            'category': requirement_data.get('category', ''),
            'priority': requirement_data.get('priority', 'medium'),
            'status': requirement_data.get('status', 'pending'),
            'due_date': requirement_data.get('due_date'),
            'assigned_to': requirement_data.get('assigned_to'),
            'tags': requirement_data.get('tags', []),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the requirement in memory
        self.requirements[requirement_id] = requirement
        
        # Store the requirement on disk
        self._save_requirement_to_disk(requirement)
        
        logger.info(f"Requirement created: {requirement_id}")
        
        return requirement
    
    def get_requirement(self, requirement_id: str) -> Dict[str, Any]:
        """
        Get a compliance requirement.
        
        Args:
            requirement_id: The ID of the requirement
            
        Returns:
            The requirement
            
        Raises:
            ValueError: If the requirement does not exist
        """
        logger.info(f"Getting requirement: {requirement_id}")
        
        if requirement_id not in self.requirements:
            raise ValueError(f"Requirement not found: {requirement_id}")
        
        return self.requirements[requirement_id]
    
    def update_requirement(self, requirement_id: str, requirement_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a compliance requirement.
        
        Args:
            requirement_id: The ID of the requirement
            requirement_data: The updated requirement data
            
        Returns:
            The updated requirement
            
        Raises:
            ValueError: If the requirement does not exist
        """
        logger.info(f"Updating requirement: {requirement_id}")
        
        # Check if the requirement exists
        if requirement_id not in self.requirements:
            raise ValueError(f"Requirement not found: {requirement_id}")
        
        # Get the existing requirement
        requirement = self.requirements[requirement_id]
        
        # Update the requirement data
        if 'name' in requirement_data:
            requirement['name'] = requirement_data['name']
        
        if 'description' in requirement_data:
            requirement['description'] = requirement_data['description']
        
        if 'framework' in requirement_data:
            requirement['framework'] = requirement_data['framework']
        
        if 'category' in requirement_data:
            requirement['category'] = requirement_data['category']
        
        if 'priority' in requirement_data:
            requirement['priority'] = requirement_data['priority']
        
        if 'status' in requirement_data:
            requirement['status'] = requirement_data['status']
        
        if 'due_date' in requirement_data:
            requirement['due_date'] = requirement_data['due_date']
        
        if 'assigned_to' in requirement_data:
            requirement['assigned_to'] = requirement_data['assigned_to']
        
        if 'tags' in requirement_data:
            requirement['tags'] = requirement_data['tags']
        
        # Update the updated_at timestamp
        requirement['updated_at'] = self._get_current_timestamp()
        
        # Store the updated requirement on disk
        self._save_requirement_to_disk(requirement)
        
        logger.info(f"Requirement updated: {requirement_id}")
        
        return requirement
    
    def delete_requirement(self, requirement_id: str) -> None:
        """
        Delete a compliance requirement.
        
        Args:
            requirement_id: The ID of the requirement
            
        Raises:
            ValueError: If the requirement does not exist
        """
        logger.info(f"Deleting requirement: {requirement_id}")
        
        # Check if the requirement exists
        if requirement_id not in self.requirements:
            raise ValueError(f"Requirement not found: {requirement_id}")
        
        # Remove the requirement from memory
        del self.requirements[requirement_id]
        
        # Remove the requirement from disk
        self._delete_requirement_from_disk(requirement_id)
        
        logger.info(f"Requirement deleted: {requirement_id}")
    
    def create_activity(self, activity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new compliance activity.
        
        Args:
            activity_data: The activity data
            
        Returns:
            The created activity
            
        Raises:
            ValueError: If the activity data is invalid
        """
        logger.info("Creating new compliance activity")
        
        # Validate the activity data
        self._validate_activity_data(activity_data)
        
        # Generate a unique activity ID
        activity_id = str(uuid.uuid4())
        
        # Create the activity object
        activity = {
            'id': activity_id,
            'name': activity_data['name'],
            'description': activity_data.get('description', ''),
            'requirement_id': activity_data.get('requirement_id'),
            'type': activity_data.get('type', 'task'),
            'status': activity_data.get('status', 'pending'),
            'start_date': activity_data.get('start_date'),
            'end_date': activity_data.get('end_date'),
            'assigned_to': activity_data.get('assigned_to'),
            'notes': activity_data.get('notes', ''),
            'created_at': self._get_current_timestamp(),
            'updated_at': self._get_current_timestamp()
        }
        
        # Store the activity in memory
        self.activities[activity_id] = activity
        
        # Store the activity on disk
        self._save_activity_to_disk(activity)
        
        logger.info(f"Activity created: {activity_id}")
        
        return activity
    
    def get_activity(self, activity_id: str) -> Dict[str, Any]:
        """
        Get a compliance activity.
        
        Args:
            activity_id: The ID of the activity
            
        Returns:
            The activity
            
        Raises:
            ValueError: If the activity does not exist
        """
        logger.info(f"Getting activity: {activity_id}")
        
        if activity_id not in self.activities:
            raise ValueError(f"Activity not found: {activity_id}")
        
        return self.activities[activity_id]
    
    def update_activity(self, activity_id: str, activity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a compliance activity.
        
        Args:
            activity_id: The ID of the activity
            activity_data: The updated activity data
            
        Returns:
            The updated activity
            
        Raises:
            ValueError: If the activity does not exist
        """
        logger.info(f"Updating activity: {activity_id}")
        
        # Check if the activity exists
        if activity_id not in self.activities:
            raise ValueError(f"Activity not found: {activity_id}")
        
        # Get the existing activity
        activity = self.activities[activity_id]
        
        # Update the activity data
        if 'name' in activity_data:
            activity['name'] = activity_data['name']
        
        if 'description' in activity_data:
            activity['description'] = activity_data['description']
        
        if 'requirement_id' in activity_data:
            activity['requirement_id'] = activity_data['requirement_id']
        
        if 'type' in activity_data:
            activity['type'] = activity_data['type']
        
        if 'status' in activity_data:
            activity['status'] = activity_data['status']
        
        if 'start_date' in activity_data:
            activity['start_date'] = activity_data['start_date']
        
        if 'end_date' in activity_data:
            activity['end_date'] = activity_data['end_date']
        
        if 'assigned_to' in activity_data:
            activity['assigned_to'] = activity_data['assigned_to']
        
        if 'notes' in activity_data:
            activity['notes'] = activity_data['notes']
        
        # Update the updated_at timestamp
        activity['updated_at'] = self._get_current_timestamp()
        
        # Store the updated activity on disk
        self._save_activity_to_disk(activity)
        
        logger.info(f"Activity updated: {activity_id}")
        
        return activity
    
    def delete_activity(self, activity_id: str) -> None:
        """
        Delete a compliance activity.
        
        Args:
            activity_id: The ID of the activity
            
        Raises:
            ValueError: If the activity does not exist
        """
        logger.info(f"Deleting activity: {activity_id}")
        
        # Check if the activity exists
        if activity_id not in self.activities:
            raise ValueError(f"Activity not found: {activity_id}")
        
        # Remove the activity from memory
        del self.activities[activity_id]
        
        # Remove the activity from disk
        self._delete_activity_from_disk(activity_id)
        
        logger.info(f"Activity deleted: {activity_id}")
    
    def get_requirement_activities(self, requirement_id: str) -> List[Dict[str, Any]]:
        """
        Get all activities for a requirement.
        
        Args:
            requirement_id: The ID of the requirement
            
        Returns:
            List of activities for the requirement
        """
        logger.info(f"Getting activities for requirement: {requirement_id}")
        
        return [a for a in self.activities.values() if a.get('requirement_id') == requirement_id]
    
    def get_requirements_by_framework(self, framework: str) -> List[Dict[str, Any]]:
        """
        Get all requirements for a framework.
        
        Args:
            framework: The framework
            
        Returns:
            List of requirements for the framework
        """
        logger.info(f"Getting requirements for framework: {framework}")
        
        return [r for r in self.requirements.values() if r.get('framework') == framework]
    
    def get_requirements_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Get all requirements with a specific status.
        
        Args:
            status: The status
            
        Returns:
            List of requirements with the specified status
        """
        logger.info(f"Getting requirements with status: {status}")
        
        return [r for r in self.requirements.values() if r.get('status') == status]
    
    def get_activities_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        Get all activities with a specific status.
        
        Args:
            status: The status
            
        Returns:
            List of activities with the specified status
        """
        logger.info(f"Getting activities with status: {status}")
        
        return [a for a in self.activities.values() if a.get('status') == status]
    
    def get_activities_by_assigned_to(self, assigned_to: str) -> List[Dict[str, Any]]:
        """
        Get all activities assigned to a specific person.
        
        Args:
            assigned_to: The person assigned to the activities
            
        Returns:
            List of activities assigned to the specified person
        """
        logger.info(f"Getting activities assigned to: {assigned_to}")
        
        return [a for a in self.activities.values() if a.get('assigned_to') == assigned_to]
    
    def _validate_requirement_data(self, requirement_data: Dict[str, Any]) -> None:
        """
        Validate requirement data.
        
        Args:
            requirement_data: The requirement data to validate
            
        Raises:
            ValueError: If the requirement data is invalid
        """
        # Check required fields
        if 'name' not in requirement_data:
            raise ValueError("Requirement name is required")
        
        if 'framework' not in requirement_data:
            raise ValueError("Requirement framework is required")
        
        # Validate priority if provided
        if 'priority' in requirement_data:
            valid_priorities = ['low', 'medium', 'high']
            if requirement_data['priority'] not in valid_priorities:
                raise ValueError(f"Invalid priority: {requirement_data['priority']}")
        
        # Validate status if provided
        if 'status' in requirement_data:
            valid_statuses = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled']
            if requirement_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid status: {requirement_data['status']}")
    
    def _validate_activity_data(self, activity_data: Dict[str, Any]) -> None:
        """
        Validate activity data.
        
        Args:
            activity_data: The activity data to validate
            
        Raises:
            ValueError: If the activity data is invalid
        """
        # Check required fields
        if 'name' not in activity_data:
            raise ValueError("Activity name is required")
        
        # Validate requirement_id if provided
        if 'requirement_id' in activity_data:
            requirement_id = activity_data['requirement_id']
            if requirement_id and requirement_id not in self.requirements:
                raise ValueError(f"Invalid requirement ID: {requirement_id}")
        
        # Validate type if provided
        if 'type' in activity_data:
            valid_types = ['task', 'meeting', 'review', 'audit', 'documentation', 'other']
            if activity_data['type'] not in valid_types:
                raise ValueError(f"Invalid type: {activity_data['type']}")
        
        # Validate status if provided
        if 'status' in activity_data:
            valid_statuses = ['pending', 'in_progress', 'completed', 'deferred', 'cancelled']
            if activity_data['status'] not in valid_statuses:
                raise ValueError(f"Invalid status: {activity_data['status']}")
    
    def _load_data_from_disk(self) -> None:
        """Load requirements and activities from disk."""
        # Load requirements
        self._load_requirements_from_disk()
        
        # Load activities
        self._load_activities_from_disk()
    
    def _load_requirements_from_disk(self) -> None:
        """Load requirements from disk."""
        try:
            # Create the requirements directory if it doesn't exist
            requirements_dir = os.path.join(self.tracking_dir, 'requirements')
            os.makedirs(requirements_dir, exist_ok=True)
            
            # Get all JSON files in the requirements directory
            requirement_files = [f for f in os.listdir(requirements_dir) if f.endswith('.json')]
            
            for requirement_file in requirement_files:
                try:
                    # Load the requirement from disk
                    file_path = os.path.join(requirements_dir, requirement_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        requirement = json.load(f)
                    
                    # Store the requirement in memory
                    requirement_id = requirement.get('id')
                    
                    if requirement_id:
                        self.requirements[requirement_id] = requirement
                        logger.info(f"Loaded requirement from disk: {requirement_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load requirement from {requirement_file}: {e}")
            
            logger.info(f"Loaded {len(self.requirements)} requirements from disk")
        
        except Exception as e:
            logger.error(f"Failed to load requirements from disk: {e}")
    
    def _load_activities_from_disk(self) -> None:
        """Load activities from disk."""
        try:
            # Create the activities directory if it doesn't exist
            activities_dir = os.path.join(self.tracking_dir, 'activities')
            os.makedirs(activities_dir, exist_ok=True)
            
            # Get all JSON files in the activities directory
            activity_files = [f for f in os.listdir(activities_dir) if f.endswith('.json')]
            
            for activity_file in activity_files:
                try:
                    # Load the activity from disk
                    file_path = os.path.join(activities_dir, activity_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        activity = json.load(f)
                    
                    # Store the activity in memory
                    activity_id = activity.get('id')
                    
                    if activity_id:
                        self.activities[activity_id] = activity
                        logger.info(f"Loaded activity from disk: {activity_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load activity from {activity_file}: {e}")
            
            logger.info(f"Loaded {len(self.activities)} activities from disk")
        
        except Exception as e:
            logger.error(f"Failed to load activities from disk: {e}")
    
    def _save_requirement_to_disk(self, requirement: Dict[str, Any]) -> None:
        """
        Save a requirement to disk.
        
        Args:
            requirement: The requirement to save
        """
        try:
            # Get the requirement ID
            requirement_id = requirement.get('id')
            
            if not requirement_id:
                raise ValueError("Requirement ID is missing")
            
            # Create the requirements directory if it doesn't exist
            requirements_dir = os.path.join(self.tracking_dir, 'requirements')
            os.makedirs(requirements_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(requirements_dir, f"{requirement_id}.json")
            
            # Save the requirement to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(requirement, f, indent=2)
            
            logger.info(f"Saved requirement to disk: {requirement_id}")
        
        except Exception as e:
            logger.error(f"Failed to save requirement to disk: {e}")
    
    def _save_activity_to_disk(self, activity: Dict[str, Any]) -> None:
        """
        Save an activity to disk.
        
        Args:
            activity: The activity to save
        """
        try:
            # Get the activity ID
            activity_id = activity.get('id')
            
            if not activity_id:
                raise ValueError("Activity ID is missing")
            
            # Create the activities directory if it doesn't exist
            activities_dir = os.path.join(self.tracking_dir, 'activities')
            os.makedirs(activities_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(activities_dir, f"{activity_id}.json")
            
            # Save the activity to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(activity, f, indent=2)
            
            logger.info(f"Saved activity to disk: {activity_id}")
        
        except Exception as e:
            logger.error(f"Failed to save activity to disk: {e}")
    
    def _delete_requirement_from_disk(self, requirement_id: str) -> None:
        """
        Delete a requirement from disk.
        
        Args:
            requirement_id: The ID of the requirement
        """
        try:
            # Create the requirements directory if it doesn't exist
            requirements_dir = os.path.join(self.tracking_dir, 'requirements')
            os.makedirs(requirements_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(requirements_dir, f"{requirement_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted requirement from disk: {requirement_id}")
            else:
                logger.warning(f"Requirement file not found on disk: {requirement_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete requirement from disk: {e}")
    
    def _delete_activity_from_disk(self, activity_id: str) -> None:
        """
        Delete an activity from disk.
        
        Args:
            activity_id: The ID of the activity
        """
        try:
            # Create the activities directory if it doesn't exist
            activities_dir = os.path.join(self.tracking_dir, 'activities')
            os.makedirs(activities_dir, exist_ok=True)
            
            # Create the file path
            file_path = os.path.join(activities_dir, f"{activity_id}.json")
            
            # Check if the file exists
            if os.path.exists(file_path):
                # Delete the file
                os.remove(file_path)
                logger.info(f"Deleted activity from disk: {activity_id}")
            else:
                logger.warning(f"Activity file not found on disk: {activity_id}")
        
        except Exception as e:
            logger.error(f"Failed to delete activity from disk: {e}")
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
