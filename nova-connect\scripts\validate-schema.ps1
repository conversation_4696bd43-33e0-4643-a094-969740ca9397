# PowerShell script to validate schema.yaml file

# Function to validate YAML file
function Test-YamlFile {
    param (
        [string]$FilePath
    )
    
    try {
        # Read the YAML file
        $content = Get-Content -Path $FilePath -Raw
        
        # Try to convert from YAML
        $yaml = ConvertFrom-Json -InputObject $content -ErrorAction Stop
        
        Write-Host "YAML file is valid: $FilePath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error validating YAML file: $FilePath" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        return $false
    }
}

# Function to check required fields in schema.yaml
function Test-SchemaRequiredFields {
    param (
        [string]$FilePath
    )
    
    try {
        # Read the YAML file
        $content = Get-Content -Path $FilePath -Raw
        
        # Convert from YAML
        $schema = ConvertFrom-Json -InputObject $content -ErrorAction Stop
        
        # Check required sections
        $requiredSections = @(
            "x-google-marketplace.schemaVersion",
            "x-google-marketplace.applicationApiVersion",
            "x-google-marketplace.publishedVersion",
            "x-google-marketplace.publishedVersionMetadata",
            "x-google-marketplace.managedUpdates",
            "x-google-marketplace.images",
            "x-google-marketplace.clusterConstraints",
            "properties",
            "required",
            "form"
        )
        
        $missingFields = @()
        
        foreach ($section in $requiredSections) {
            $parts = $section.Split(".")
            $obj = $schema
            
            foreach ($part in $parts) {
                if ($obj.$part -eq $null) {
                    $missingFields += $section
                    break
                }
                $obj = $obj.$part
            }
        }
        
        if ($missingFields.Count -eq 0) {
            Write-Host "All required fields are present in schema.yaml" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "Missing required fields in schema.yaml:" -ForegroundColor Red
            foreach ($field in $missingFields) {
                Write-Host "  - $field" -ForegroundColor Red
            }
            return $false
        }
    }
    catch {
        Write-Host "Error checking required fields in schema.yaml" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        return $false
    }
}

# Function to check property definitions in schema.yaml
function Test-SchemaProperties {
    param (
        [string]$FilePath
    )
    
    try {
        # Read the YAML file
        $content = Get-Content -Path $FilePath -Raw
        
        # Convert from YAML
        $schema = ConvertFrom-Json -InputObject $content -ErrorAction Stop
        
        # Check properties
        $properties = $schema.properties
        
        if ($properties -eq $null) {
            Write-Host "No properties defined in schema.yaml" -ForegroundColor Red
            return $false
        }
        
        $requiredProperties = @(
            "name",
            "namespace",
            "tier",
            "mongodb.uri",
            "redis.uri"
        )
        
        $missingProperties = @()
        
        foreach ($prop in $requiredProperties) {
            if ($properties.$prop -eq $null) {
                $missingProperties += $prop
            }
        }
        
        if ($missingProperties.Count -eq 0) {
            Write-Host "All required properties are defined in schema.yaml" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "Missing required properties in schema.yaml:" -ForegroundColor Red
            foreach ($prop in $missingProperties) {
                Write-Host "  - $prop" -ForegroundColor Red
            }
            return $false
        }
    }
    catch {
        Write-Host "Error checking properties in schema.yaml" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        return $false
    }
}

# Function to check form UI in schema.yaml
function Test-SchemaForm {
    param (
        [string]$FilePath
    )
    
    try {
        # Read the YAML file
        $content = Get-Content -Path $FilePath -Raw
        
        # Convert from YAML
        $schema = ConvertFrom-Json -InputObject $content -ErrorAction Stop
        
        # Check form
        $form = $schema.form
        
        if ($form -eq $null -or $form.Count -eq 0) {
            Write-Host "No form UI defined in schema.yaml" -ForegroundColor Red
            return $false
        }
        
        $widgetTypes = @()
        
        foreach ($widget in $form) {
            if ($widget.widget -ne $null) {
                $widgetTypes += $widget.widget
            }
        }
        
        $requiredWidgets = @(
            "help",
            "selector",
            "input"
        )
        
        $missingWidgets = @()
        
        foreach ($widget in $requiredWidgets) {
            if ($widgetTypes -notcontains $widget) {
                $missingWidgets += $widget
            }
        }
        
        if ($missingWidgets.Count -eq 0) {
            Write-Host "All required widget types are used in form UI" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "Missing widget types in form UI:" -ForegroundColor Red
            foreach ($widget in $missingWidgets) {
                Write-Host "  - $widget" -ForegroundColor Red
            }
            return $false
        }
    }
    catch {
        Write-Host "Error checking form UI in schema.yaml" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        return $false
    }
}

# Main script
Write-Host "Validating schema.yaml file..." -ForegroundColor Yellow

$schemaPath = "nova-connect/marketplace/schema.yaml"

# Check if file exists
if (-not (Test-Path -Path $schemaPath)) {
    Write-Host "schema.yaml file not found at: $schemaPath" -ForegroundColor Red
    exit 1
}

# Validate YAML syntax
$yamlValid = Test-YamlFile -FilePath $schemaPath

if (-not $yamlValid) {
    Write-Host "YAML validation failed. Please fix the syntax errors." -ForegroundColor Red
    exit 1
}

# Check required fields
$fieldsValid = Test-SchemaRequiredFields -FilePath $schemaPath

if (-not $fieldsValid) {
    Write-Host "Required fields validation failed. Please add the missing fields." -ForegroundColor Red
    exit 1
}

# Check properties
$propertiesValid = Test-SchemaProperties -FilePath $schemaPath

if (-not $propertiesValid) {
    Write-Host "Properties validation failed. Please add the missing properties." -ForegroundColor Red
    exit 1
}

# Check form UI
$formValid = Test-SchemaForm -FilePath $schemaPath

if (-not $formValid) {
    Write-Host "Form UI validation failed. Please add the missing widget types." -ForegroundColor Red
    exit 1
}

Write-Host "schema.yaml validation completed successfully!" -ForegroundColor Green
Write-Host "The schema.yaml file is ready for Google Cloud Marketplace integration." -ForegroundColor Green
