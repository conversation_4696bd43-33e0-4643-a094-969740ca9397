<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comphyology Patent Diagrams</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .diagram-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .diagram-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .diagram-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .diagram-title {
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
            color: #0070f3;
        }
        .diagram-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .view-button {
            display: inline-block;
            background-color: #0070f3;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        .view-button:hover {
            background-color: #0051a8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Comphyology Patent Diagrams</h1>

        <div class="diagram-list">
            <div class="diagram-card">
                <div class="diagram-title">1. High-Level System Architecture</div>
                <div class="diagram-description">Overview of the NovaFuse Platform and Comphyology Framework</div>
                <a href="diagram1.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">2. Finite Universe Paradigm</div>
                <div class="diagram-description">Visualization of the Finite Universe Paradigm concept</div>
                <a href="diagram2.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">3. Three-Body Problem Reframing</div>
                <div class="diagram-description">Comparison of classical and Comphyological approaches</div>
                <a href="diagram3.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">4. UUFT Equation Flow</div>
                <div class="diagram-description">Visualization of the UUFT equation (A⊗B⊕C)×π10³</div>
                <a href="diagram4.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">5. Trinity Equation Visualization</div>
                <div class="diagram-description">Visualization of the Trinity Equation concept</div>
                <a href="diagram5.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">6. Meta-Field Schema</div>
                <div class="diagram-description">Representation of the Meta-Field Schema</div>
                <a href="diagram6.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">8. Pattern Translation Process</div>
                <div class="diagram-description">Diagram of the Universal Pattern Language</div>
                <a href="diagram8.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">9. 13 NovaFuse Components</div>
                <div class="diagram-description">Overview of the 13 Universal NovaFuse Components</div>
                <a href="diagram9.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">10. 3-6-9-12-13 Alignment Architecture</div>
                <div class="diagram-description">Visualization of the Alignment Architecture</div>
                <a href="diagram10.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">11. Cross-Module Data Processing Pipeline</div>
                <div class="diagram-description">Diagram of the data processing pipeline</div>
                <a href="diagram11.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">12. Cyber-Safety Incident Response Workflow</div>
                <div class="diagram-description">Workflow diagram for incident response</div>
                <a href="diagram12.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">13. Healthcare Implementation</div>
                <div class="diagram-description">Implementation diagram for healthcare industry</div>
                <a href="diagram13.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">14. Dashboard and Visualization Examples</div>
                <div class="diagram-description">Examples of dashboards and visualizations</div>
                <a href="diagram14.html" class="view-button" target="_blank">View Diagram</a>
            </div>

            <div class="diagram-card">
                <div class="diagram-title">15. Hardware Architecture</div>
                <div class="diagram-description">Diagram of the hardware architecture</div>
                <a href="diagram15.html" class="view-button" target="_blank">View Diagram</a>
            </div>
        </div>
    </div>
</body>
</html>
