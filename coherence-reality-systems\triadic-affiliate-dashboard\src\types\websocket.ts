export interface WebSocketData {
  type: 'conversion' | 'product' | 'network'
  data: Conversion | Product | Network
}

export interface Conversion {
  id: string
  categoryId: string
  productId: string
  networkId: string
  revenue: number
  status: 'pending' | 'completed' | 'failed'
  createdAt: string
  triadicMetrics: {
    psi: number
    phi: number
    kappa: number
  }
}

export interface Product {
  id: string
  categoryId: string
  name: string
  price: number
  status: 'active' | 'inactive'
  conversions: number
}

export interface Network {
  id: string
  name: string
  apiKey: string
  status: 'active' | 'inactive'
}
