/**
 * Database Optimization Service
 * 
 * This service provides database optimization functionality for NovaConnect UAC.
 */

const mongoose = require('mongoose');
const logger = require('../utils/logger');

class DatabaseOptimizationService {
  constructor() {
    // Configuration
    this.config = {
      enabled: process.env.DB_OPTIMIZATION_ENABLED === 'true',
      interval: parseInt(process.env.DB_OPTIMIZATION_INTERVAL || '86400000', 10), // 24 hours
      indexesEnabled: process.env.DB_OPTIMIZATION_INDEXES_ENABLED === 'true',
      vacuumEnabled: process.env.DB_OPTIMIZATION_VACUUM_ENABLED === 'true',
      statsEnabled: process.env.DB_OPTIMIZATION_STATS_ENABLED === 'true',
      maxQueryTime: parseInt(process.env.DB_OPTIMIZATION_MAX_QUERY_TIME || '1000', 10), // 1 second
      slowQueryThreshold: parseInt(process.env.DB_OPTIMIZATION_SLOW_QUERY_THRESHOLD || '500', 10), // 500 ms
      indexThreshold: parseInt(process.env.DB_OPTIMIZATION_INDEX_THRESHOLD || '1000', 10) // 1000 documents
    };
    
    // Optimization timer
    this.optimizationTimer = null;
    
    // Optimization stats
    this.stats = {
      lastOptimization: null,
      indexesCreated: 0,
      indexesRemoved: 0,
      collectionsVacuumed: 0,
      slowQueries: []
    };
    
    // Initialize optimization
    if (this.config.enabled) {
      this._initialize();
    }
  }
  
  /**
   * Initialize optimization
   * @private
   */
  _initialize() {
    logger.info('Database optimization initialized');
    
    // Start optimization timer
    this.optimizationTimer = setInterval(() => {
      this.optimizeDatabase().catch(error => {
        logger.error('Error optimizing database', { error });
      });
    }, this.config.interval);
    
    // Run initial optimization
    this.optimizeDatabase().catch(error => {
      logger.error('Error optimizing database', { error });
    });
  }
  
  /**
   * Optimize database
   * @returns {Promise<Object>} Optimization results
   */
  async optimizeDatabase() {
    if (!this.config.enabled) {
      logger.warn('Database optimization is disabled');
      return { enabled: false };
    }
    
    logger.info('Starting database optimization');
    
    try {
      // Get MongoDB connection
      const connection = mongoose.connection;
      
      if (!connection || connection.readyState !== 1) {
        logger.warn('MongoDB connection is not ready');
        return { error: 'MongoDB connection is not ready' };
      }
      
      const db = connection.db;
      const results = {
        indexes: null,
        vacuum: null,
        stats: null,
        slowQueries: null
      };
      
      // Optimize indexes
      if (this.config.indexesEnabled) {
        results.indexes = await this._optimizeIndexes(db);
      }
      
      // Vacuum collections
      if (this.config.vacuumEnabled) {
        results.vacuum = await this._vacuumCollections(db);
      }
      
      // Collect stats
      if (this.config.statsEnabled) {
        results.stats = await this._collectStats(db);
      }
      
      // Analyze slow queries
      results.slowQueries = await this._analyzeSlowQueries(db);
      
      // Update optimization stats
      this.stats.lastOptimization = new Date();
      this.stats.indexesCreated += results.indexes?.created || 0;
      this.stats.indexesRemoved += results.indexes?.removed || 0;
      this.stats.collectionsVacuumed += results.vacuum?.count || 0;
      this.stats.slowQueries = results.slowQueries?.queries || [];
      
      logger.info('Database optimization completed', { results });
      
      return results;
    } catch (error) {
      logger.error('Error optimizing database', { error });
      throw error;
    }
  }
  
  /**
   * Optimize indexes
   * @param {Object} db - MongoDB database
   * @returns {Promise<Object>} Index optimization results
   * @private
   */
  async _optimizeIndexes(db) {
    logger.info('Optimizing indexes');
    
    try {
      const results = {
        created: 0,
        removed: 0,
        collections: []
      };
      
      // Get collections
      const collections = await db.listCollections().toArray();
      
      for (const collection of collections) {
        const collectionName = collection.name;
        const collectionObj = db.collection(collectionName);
        
        // Skip system collections
        if (collectionName.startsWith('system.')) {
          continue;
        }
        
        // Get collection stats
        const stats = await db.command({ collStats: collectionName });
        const documentCount = stats.count;
        
        // Get current indexes
        const indexes = await collectionObj.indexes();
        
        // Check if collection needs indexes
        if (documentCount > this.config.indexThreshold) {
          // Get most frequent queries
          const pipeline = [
            { $collStats: { latencyStats: { histograms: true } } }
          ];
          
          let frequentQueries = [];
          
          try {
            const latencyStats = await collectionObj.aggregate(pipeline).toArray();
            
            if (latencyStats.length > 0 && latencyStats[0].latencyStats && latencyStats[0].latencyStats.reads) {
              frequentQueries = latencyStats[0].latencyStats.reads.ops;
            }
          } catch (error) {
            logger.warn(`Error getting latency stats for collection ${collectionName}`, { error });
          }
          
          // Create indexes for frequent queries
          for (const query of frequentQueries) {
            if (query.query && Object.keys(query.query).length > 0) {
              const indexFields = Object.keys(query.query);
              
              // Check if index already exists
              const indexExists = indexes.some(index => {
                const indexKeys = Object.keys(index.key);
                return indexFields.every(field => indexKeys.includes(field));
              });
              
              if (!indexExists) {
                // Create index
                const indexSpec = {};
                indexFields.forEach(field => {
                  indexSpec[field] = 1;
                });
                
                try {
                  await collectionObj.createIndex(indexSpec);
                  results.created++;
                  logger.info(`Created index on ${collectionName}`, { fields: indexFields });
                } catch (error) {
                  logger.warn(`Error creating index on ${collectionName}`, { error, fields: indexFields });
                }
              }
            }
          }
        }
        
        // Remove unused indexes
        for (const index of indexes) {
          // Skip _id index
          if (Object.keys(index.key).length === 1 && Object.keys(index.key)[0] === '_id') {
            continue;
          }
          
          // Check if index is used
          const indexStats = await db.command({
            aggregate: collectionName,
            pipeline: [
              { $indexStats: {} },
              { $match: { name: index.name } }
            ],
            cursor: {}
          });
          
          const indexUsage = indexStats.cursor.firstBatch[0];
          
          if (indexUsage && indexUsage.accesses && indexUsage.accesses.ops === 0 && indexUsage.accesses.since.getTime() < Date.now() - 30 * 24 * 60 * 60 * 1000) {
            // Index has not been used in 30 days
            try {
              await collectionObj.dropIndex(index.name);
              results.removed++;
              logger.info(`Removed unused index ${index.name} on ${collectionName}`);
            } catch (error) {
              logger.warn(`Error removing index ${index.name} on ${collectionName}`, { error });
            }
          }
        }
        
        // Add collection results
        results.collections.push({
          name: collectionName,
          documentCount,
          indexCount: indexes.length
        });
      }
      
      return results;
    } catch (error) {
      logger.error('Error optimizing indexes', { error });
      return {
        error: error.message,
        created: 0,
        removed: 0,
        collections: []
      };
    }
  }
  
  /**
   * Vacuum collections
   * @param {Object} db - MongoDB database
   * @returns {Promise<Object>} Vacuum results
   * @private
   */
  async _vacuumCollections(db) {
    logger.info('Vacuuming collections');
    
    try {
      const results = {
        count: 0,
        collections: []
      };
      
      // Get collections
      const collections = await db.listCollections().toArray();
      
      for (const collection of collections) {
        const collectionName = collection.name;
        
        // Skip system collections
        if (collectionName.startsWith('system.')) {
          continue;
        }
        
        // Get collection stats
        const stats = await db.command({ collStats: collectionName });
        
        // Check if collection needs vacuum
        if (stats.wasted && stats.wasted > 0) {
          try {
            // Run compact command
            await db.command({ compact: collectionName });
            
            results.count++;
            results.collections.push({
              name: collectionName,
              wastedSize: stats.wasted
            });
            
            logger.info(`Vacuumed collection ${collectionName}`);
          } catch (error) {
            logger.warn(`Error vacuuming collection ${collectionName}`, { error });
          }
        }
      }
      
      return results;
    } catch (error) {
      logger.error('Error vacuuming collections', { error });
      return {
        error: error.message,
        count: 0,
        collections: []
      };
    }
  }
  
  /**
   * Collect database stats
   * @param {Object} db - MongoDB database
   * @returns {Promise<Object>} Database stats
   * @private
   */
  async _collectStats(db) {
    logger.info('Collecting database stats');
    
    try {
      // Get database stats
      const dbStats = await db.command({ dbStats: 1 });
      
      // Get collection stats
      const collections = await db.listCollections().toArray();
      const collectionStats = [];
      
      for (const collection of collections) {
        const collectionName = collection.name;
        
        // Skip system collections
        if (collectionName.startsWith('system.')) {
          continue;
        }
        
        // Get collection stats
        const stats = await db.command({ collStats: collectionName });
        
        collectionStats.push({
          name: collectionName,
          count: stats.count,
          size: stats.size,
          avgObjSize: stats.avgObjSize,
          storageSize: stats.storageSize,
          indexes: stats.nindexes,
          indexSize: stats.totalIndexSize,
          wasted: stats.wasted || 0
        });
      }
      
      return {
        database: dbStats,
        collections: collectionStats
      };
    } catch (error) {
      logger.error('Error collecting database stats', { error });
      return {
        error: error.message
      };
    }
  }
  
  /**
   * Analyze slow queries
   * @param {Object} db - MongoDB database
   * @returns {Promise<Object>} Slow query analysis results
   * @private
   */
  async _analyzeSlowQueries(db) {
    logger.info('Analyzing slow queries');
    
    try {
      // Get profiling level
      const profilingLevel = await db.command({ profile: -1 });
      
      // Enable profiling if not already enabled
      if (profilingLevel.was !== 2) {
        await db.command({
          profile: 2,
          slowms: this.config.slowQueryThreshold
        });
      }
      
      // Get slow queries
      const slowQueries = await db.collection('system.profile').find({
        millis: { $gt: this.config.slowQueryThreshold }
      }).sort({ millis: -1 }).limit(20).toArray();
      
      // Analyze slow queries
      const analyzedQueries = slowQueries.map(query => {
        return {
          collection: query.ns.split('.')[1],
          operation: query.op,
          query: query.query || query.command,
          duration: query.millis,
          timestamp: query.ts,
          planSummary: query.planSummary,
          keysExamined: query.keysExamined,
          docsExamined: query.docsExamined,
          nreturned: query.nreturned
        };
      });
      
      // Group by collection
      const queryByCollection = {};
      
      for (const query of analyzedQueries) {
        if (!queryByCollection[query.collection]) {
          queryByCollection[query.collection] = [];
        }
        
        queryByCollection[query.collection].push(query);
      }
      
      // Generate recommendations
      const recommendations = [];
      
      for (const [collection, queries] of Object.entries(queryByCollection)) {
        for (const query of queries) {
          // Check if query is inefficient
          if (query.keysExamined > 0 && query.docsExamined > query.keysExamined * 2) {
            // Query is examining too many documents
            recommendations.push({
              collection,
              query: query.query,
              recommendation: 'Create an index for this query to reduce the number of documents examined.'
            });
          } else if (query.keysExamined === 0 && query.docsExamined > 0) {
            // Query is not using an index
            recommendations.push({
              collection,
              query: query.query,
              recommendation: 'Create an index for this query to improve performance.'
            });
          } else if (query.nreturned > 0 && query.docsExamined > query.nreturned * 10) {
            // Query is examining too many documents for the number of results
            recommendations.push({
              collection,
              query: query.query,
              recommendation: 'Refine this query to reduce the number of documents examined.'
            });
          }
        }
      }
      
      return {
        queries: analyzedQueries,
        recommendations
      };
    } catch (error) {
      logger.error('Error analyzing slow queries', { error });
      return {
        error: error.message,
        queries: [],
        recommendations: []
      };
    }
  }
  
  /**
   * Get optimization stats
   * @returns {Object} Optimization stats
   */
  getStats() {
    return {
      ...this.stats,
      config: this.config
    };
  }
  
  /**
   * Stop optimization
   */
  stop() {
    if (this.optimizationTimer) {
      clearInterval(this.optimizationTimer);
      this.optimizationTimer = null;
    }
    
    logger.info('Database optimization stopped');
  }
}

module.exports = new DatabaseOptimizationService();
