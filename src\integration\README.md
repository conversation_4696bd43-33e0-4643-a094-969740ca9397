# Unified Integration Module

This module provides a unified integration system for connecting the Self-Healing Tensor, 3D Tensor Visualization, Analytics Dashboard, and Real-Time Data Flow components.

## Overview

The Unified Integration Module serves as a central hub for component communication, enabling seamless data flow between different parts of the system. It provides:

- Component registration and discovery
- Data flow management
- Event-based communication
- Adapters for different component types
- Metrics collection and monitoring

## Architecture

The integration system consists of the following components:

### Core Components

- **UnifiedIntegrationModule**: The main integration module that provides component registration, connection creation, and data flow management.
- **ComponentRegistry**: A registry for components that allows them to register themselves and discover other components.
- **DataFlowManager**: A manager for data flows between components that handles the creation, management, and execution of data flows.

### Adapters

- **TensorAdapter**: An adapter for the Self-Healing Tensor system.
- **VisualizationAdapter**: An adapter for the visualization system.
- **AnalyticsAdapter**: An adapter for the analytics dashboard.

## Usage

### Basic Usage

```javascript
const { createUnifiedIntegration } = require('./integration');
const SelfHealingTensor = require('./quantum/self-healing-tensor');
const VisualizationSystem = require('./visualization/visualization-system');
const AnalyticsDashboard = require('./analytics/analytics-dashboard');

// Create component instances
const selfHealingTensor = new SelfHealingTensor();
const visualizationSystem = new VisualizationSystem();
const analyticsDashboard = new AnalyticsDashboard();

// Create unified integration
const unifiedIntegration = createUnifiedIntegration({
  tensor: selfHealingTensor,
  visualization: visualizationSystem,
  analytics: analyticsDashboard
});

// Use the unified integration
const tensorId = 'example-tensor';
const tensor = { values: [0.5, 0.6, 0.7, 0.8, 0.9] };

// Register a tensor
const registeredTensor = unifiedIntegration.adapters.tensor.registerTensor(tensorId, tensor, 'universal');

// Create a visualization
const visualization = unifiedIntegration.adapters.visualization.createVisualization(
  '3d_tensor_visualization',
  { tensor: registeredTensor, dimensions: [5, 1, 1] }
);

// Create a data flow from tensor to visualization
const flowId = unifiedIntegration.dataFlowManager.createFlow(
  'tensor-adapter',
  'visualization-adapter',
  {
    dataType: 'tensor',
    transformations: [
      (data) => {
        // Transform tensor data for visualization
        return {
          ...data,
          dimensions: [data.values.length, 1, 1],
          transformed: true
        };
      }
    ]
  }
);

// Execute the data flow
const transformedData = unifiedIntegration.dataFlowManager.executeFlow(
  flowId,
  registeredTensor
);
```

### Advanced Usage

For more advanced usage, see the example in `examples/unified-integration-example.js`.

## API Reference

### UnifiedIntegrationModule

The main integration module that provides component registration, connection creation, and data flow management.

#### Methods

- `registerComponent(name, component, metadata)`: Register a component with the integration module.
- `getComponent(name)`: Get a registered component.
- `getComponentMetadata(name)`: Get component metadata.
- `getComponents(type)`: Get all registered components of a specific type.
- `createConnection(sourceComponent, targetComponent, options)`: Connect two components.
- `createDataFlow(sourceComponent, targetComponent, options)`: Create a data flow between components.
- `processDataFlow(flowId, data)`: Process data through a data flow.
- `getMetrics()`: Get metrics about the integration module.
- `stop()`: Stop the integration module.

### ComponentRegistry

A registry for components that allows them to register themselves and discover other components.

#### Methods

- `register(id, component, metadata)`: Register a component.
- `get(id)`: Get a component by ID.
- `getMetadata(id)`: Get component metadata.
- `has(id)`: Check if a component exists.
- `unregister(id)`: Unregister a component.
- `getAll(type)`: Get all components of a specific type.
- `getByType(type)`: Get all components of a specific type.
- `getTensors()`: Get all tensor components.
- `getVisualizations()`: Get all visualization components.
- `getAnalytics()`: Get all analytics components.
- `getDataFlows()`: Get all data flow components.
- `getCount(type)`: Get component count.
- `clear()`: Clear the registry.

### DataFlowManager

A manager for data flows between components that handles the creation, management, and execution of data flows.

#### Methods

- `createFlow(sourceId, targetId, options)`: Create a data flow between components.
- `executeFlow(flowId, data)`: Execute a data flow.
- `getFlow(flowId)`: Get a data flow.
- `getFlows(sourceId, targetId)`: Get all data flows.
- `getFlowsBySource(sourceId)`: Get flows by source component.
- `getFlowsByTarget(targetId)`: Get flows by target component.
- `updateFlow(flowId, updates)`: Update a data flow.
- `deleteFlow(flowId)`: Delete a data flow.
- `getMetrics()`: Get metrics about the data flow manager.
- `resetMetrics()`: Reset metrics.

### TensorAdapter

An adapter for the Self-Healing Tensor system.

#### Methods

- `connect()`: Connect to the tensor system.
- `disconnect()`: Disconnect from the tensor system.
- `registerTensor(id, tensor, domain)`: Register a tensor.
- `getTensor(id)`: Get a tensor.
- `updateTensor(id, updatedTensor)`: Update a tensor.
- `healTensor(id)`: Heal a tensor.
- `getHealingHistory(id)`: Get healing history for a tensor.

### VisualizationAdapter

An adapter for the visualization system.

#### Methods

- `connect()`: Connect to the visualization system.
- `disconnect()`: Disconnect from the visualization system.
- `getVisualizationTypes()`: Get available visualization types.
- `createVisualization(type, data, options)`: Create a visualization.
- `updateVisualization(id, data)`: Update a visualization.
- `deleteVisualization(id)`: Delete a visualization.

### AnalyticsAdapter

An adapter for the analytics dashboard.

#### Methods

- `connect()`: Connect to the analytics system.
- `disconnect()`: Disconnect from the analytics system.
- `getMetrics()`: Get available metrics.
- `getDashboards()`: Get available dashboards.
- `getDashboard(id)`: Get a dashboard by ID.
- `executeQuery(query, params)`: Execute a query.

## Events

The integration system uses events to communicate between components. Here are the main events:

### UnifiedIntegrationModule Events

- `component-registered`: Emitted when a component is registered.
- `connection-created`: Emitted when a connection is created.
- `data-flow-created`: Emitted when a data flow is created.
- `data-flow-processed`: Emitted when data is processed through a data flow.
- `update`: Emitted periodically with metrics.

### ComponentRegistry Events

- `component-registered`: Emitted when a component is registered.
- `component-unregistered`: Emitted when a component is unregistered.
- `registry-cleared`: Emitted when the registry is cleared.

### DataFlowManager Events

- `flow-created`: Emitted when a data flow is created.
- `flow-executed`: Emitted when a data flow is executed.
- `flow-updated`: Emitted when a data flow is updated.
- `flow-deleted`: Emitted when a data flow is deleted.

### Adapter Events

- `connected`: Emitted when an adapter connects to its system.
- `disconnected`: Emitted when an adapter disconnects from its system.
- Various system-specific events (e.g., `tensor-registered`, `visualization-created`, `metrics-updated`).

## Examples

See the `examples` directory for usage examples:

- `unified-integration-example.js`: A complete example that demonstrates how to use the unified integration module to connect the Self-Healing Tensor, 3D Tensor Visualization, Analytics Dashboard, and Real-Time Data Flow components.
