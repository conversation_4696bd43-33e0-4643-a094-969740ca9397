/**
 * Comphyological Trinity Test
 * 
 * This script tests the complete Comphyological Trinity - the three laws
 * of Comphyological Governance:
 * 
 * 1. First Law (Boundary Condition):
 *    "A system shall neither externalize non-resonant states nor propagate unmeasured energy transitions."
 * 
 * 2. Second Law (Internal Coherence):
 *    "A system shall sustain resonance through self-similar, energy-minimizing transitions."
 * 
 * 3. Third Law (Cross-Domain Harmony):
 *    "Systems shall interact through translational resonance, preserving integrity across domains."
 */

const fs = require('fs');
const path = require('path');
const { ComphyologicalTrinity } = require('../../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test the First Law - Boundary Condition
 */
function testFirstLaw(trinity) {
  console.log('\n=== Testing First Law - Boundary Condition ===');
  
  // Test with resonant values
  console.log('\nTesting with resonant values:');
  const resonantValues = [0.03, 0.06, 0.09, 0.12, 0.3, 0.6, 0.9, 3, 6, 9, 12];
  
  const resonantResults = resonantValues.map(value => {
    try {
      const result = trinity.govern(value, { enforceFirstLaw: true, enforceSecondLaw: false, enforceThirdLaw: false });
      console.log(`Value ${value} -> ${result} (Valid)`);
      return { value, result, valid: true };
    } catch (error) {
      console.log(`Value ${value} -> Error: ${error.message}`);
      return { value, error: error.message, valid: false };
    }
  });
  
  // Test with non-resonant values
  console.log('\nTesting with non-resonant values:');
  const nonResonantValues = [0.1, 0.2, 0.4, 0.5, 0.7, 0.8, 1.0, 2, 4, 5, 7, 8, 10];
  
  const nonResonantResults = nonResonantValues.map(value => {
    try {
      const result = trinity.govern(value, { enforceFirstLaw: true, enforceSecondLaw: false, enforceThirdLaw: false });
      console.log(`Value ${value} -> ${result} (Valid)`);
      return { value, result, valid: true };
    } catch (error) {
      console.log(`Value ${value} -> Error: ${error.message}`);
      return { value, error: error.message, valid: false };
    }
  });
  
  return {
    resonantValues,
    resonantResults,
    nonResonantValues,
    nonResonantResults
  };
}

/**
 * Test the Second Law - Internal Coherence
 */
function testSecondLaw(trinity) {
  console.log('\n=== Testing Second Law - Internal Coherence ===');
  
  // Test with values that need optimization
  console.log('\nTesting with values that need optimization:');
  const testValues = [0.1, 0.2, 0.4, 0.5, 0.7, 0.8, 1.0, 2, 4, 5, 7, 8, 10];
  
  const optimizationResults = testValues.map(value => {
    try {
      const result = trinity.govern(value, { enforceFirstLaw: false, enforceSecondLaw: true, enforceThirdLaw: false });
      console.log(`Value ${value} -> ${result} (Optimized)`);
      return { value, result, optimized: value !== result };
    } catch (error) {
      console.log(`Value ${value} -> Error: ${error.message}`);
      return { value, error: error.message, optimized: false };
    }
  });
  
  // Test with complex state object
  console.log('\nTesting with complex state object:');
  const complexState = {
    cyber: {
      access: 0.7,
      risk: 0.4
    },
    financial: {
      transaction: 0.5,
      volatility: 0.8
    },
    medical: {
      care: 0.2,
      severity: 0.9
    }
  };
  
  try {
    const optimizedState = trinity.govern(complexState, { enforceFirstLaw: false, enforceSecondLaw: true, enforceThirdLaw: false });
    console.log('Complex state optimized:');
    console.log('Original:', JSON.stringify(complexState, null, 2));
    console.log('Optimized:', JSON.stringify(optimizedState, null, 2));
  } catch (error) {
    console.log(`Complex state optimization error: ${error.message}`);
  }
  
  return {
    testValues,
    optimizationResults
  };
}

/**
 * Test the Third Law - Cross-Domain Harmony
 */
function testThirdLaw(trinity) {
  console.log('\n=== Testing Third Law - Cross-Domain Harmony ===');
  
  // Test cross-domain translations
  console.log('\nTesting cross-domain translations:');
  
  const testValues = [0.03, 0.06, 0.09, 0.3, 0.6, 0.9];
  const domains = ['cyber', 'financial', 'medical'];
  
  const translationResults = [];
  
  for (const value of testValues) {
    for (const sourceDomain of domains) {
      for (const targetDomain of domains) {
        if (sourceDomain !== targetDomain) {
          try {
            const result = trinity.govern(value, {
              enforceFirstLaw: false,
              enforceSecondLaw: false,
              enforceThirdLaw: true,
              sourceDomain,
              targetDomain
            });
            
            console.log(`Value ${value} from ${sourceDomain} to ${targetDomain} -> ${result.value} (${result.representation})`);
            translationResults.push({
              value,
              sourceDomain,
              targetDomain,
              result,
              valid: true
            });
          } catch (error) {
            console.log(`Value ${value} from ${sourceDomain} to ${targetDomain} -> Error: ${error.message}`);
            translationResults.push({
              value,
              sourceDomain,
              targetDomain,
              error: error.message,
              valid: false
            });
          }
        }
      }
    }
  }
  
  return {
    testValues,
    domains,
    translationResults
  };
}

/**
 * Test the Complete Trinity
 */
function testCompleteTrinity(trinity) {
  console.log('\n=== Testing Complete Comphyological Trinity ===');
  
  // Test with values that need governance across all three laws
  console.log('\nTesting with values that need governance across all three laws:');
  
  const testValues = [0.1, 0.3, 0.5, 0.6, 0.7, 0.9];
  const domains = ['cyber', 'financial', 'medical'];
  
  const trinityResults = [];
  
  for (const value of testValues) {
    for (const sourceDomain of domains) {
      for (const targetDomain of domains) {
        if (sourceDomain !== targetDomain) {
          try {
            const result = trinity.govern(value, {
              enforceFirstLaw: true,
              enforceSecondLaw: true,
              enforceThirdLaw: true,
              sourceDomain,
              targetDomain
            });
            
            console.log(`Value ${value} from ${sourceDomain} to ${targetDomain} -> ${result.value} (${result.representation})`);
            trinityResults.push({
              value,
              sourceDomain,
              targetDomain,
              result,
              valid: true
            });
          } catch (error) {
            console.log(`Value ${value} from ${sourceDomain} to ${targetDomain} -> Error: ${error.message}`);
            trinityResults.push({
              value,
              sourceDomain,
              targetDomain,
              error: error.message,
              valid: false
            });
          }
        }
      }
    }
  }
  
  return {
    testValues,
    domains,
    trinityResults
  };
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyological Trinity Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .trinity-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .first-law {
      border-left-color: #cc0000;
    }
    .second-law {
      border-left-color: #009900;
    }
    .third-law {
      border-left-color: #9900cc;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .valid {
      color: #009900;
      font-weight: bold;
    }
    .invalid {
      color: #cc0000;
    }
    .optimized {
      color: #0066cc;
      font-style: italic;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Comphyological Trinity Demo</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="trinity-info">
    <h2>The Comphyological Trinity</h2>
    <p>The three laws of Comphyological Governance form a complete framework for complex systems:</p>
    <ol>
      <li><strong>First Law (Boundary Condition):</strong> "A system shall neither externalize non-resonant states nor propagate unmeasured energy transitions."</li>
      <li><strong>Second Law (Internal Coherence):</strong> "A system shall sustain resonance through self-similar, energy-minimizing transitions."</li>
      <li><strong>Third Law (Cross-Domain Harmony):</strong> "Systems shall interact through translational resonance, preserving integrity across domains."</li>
    </ol>
  </div>
  
  <h2>First Law - Boundary Condition</h2>
  
  <div class="trinity-info first-law">
    <h3>First Law Summary</h3>
    <p>The First Law establishes boundary conditions - what a system must not do. It prevents the externalization of non-resonant states and unmeasured energy transitions.</p>
  </div>
  
  <div class="container">
    <div class="card">
      <h3>Resonant Values</h3>
      <table>
        <tr>
          <th>Value</th>
          <th>Result</th>
          <th>Status</th>
        </tr>
        ${results.firstLaw.resonantResults.map(result => `
        <tr>
          <td>${result.value}</td>
          <td>${result.valid ? result.result : 'Error'}</td>
          <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
        </tr>
        `).join('')}
      </table>
    </div>
    
    <div class="card">
      <h3>Non-Resonant Values</h3>
      <table>
        <tr>
          <th>Value</th>
          <th>Result</th>
          <th>Status</th>
        </tr>
        ${results.firstLaw.nonResonantResults.map(result => `
        <tr>
          <td>${result.value}</td>
          <td>${result.valid ? result.result : 'Error'}</td>
          <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
        </tr>
        `).join('')}
      </table>
    </div>
  </div>
  
  <h2>Second Law - Internal Coherence</h2>
  
  <div class="trinity-info second-law">
    <h3>Second Law Summary</h3>
    <p>The Second Law governs how systems maintain their internal harmony and coherence. It ensures that all state transitions maintain internal coherence by favoring self-similar, energy-efficient transitions that reinforce harmonic states.</p>
  </div>
  
  <div class="card">
    <h3>Optimization Results</h3>
    <table>
      <tr>
        <th>Original Value</th>
        <th>Optimized Value</th>
        <th>Status</th>
      </tr>
      ${results.secondLaw.optimizationResults.map(result => `
      <tr>
        <td>${result.value}</td>
        <td>${result.optimized ? result.result : 'No change'}</td>
        <td class="${result.optimized ? 'optimized' : ''}">${result.optimized ? 'Optimized' : 'No change needed'}</td>
      </tr>
      `).join('')}
    </table>
  </div>
  
  <h2>Third Law - Cross-Domain Harmony</h2>
  
  <div class="trinity-info third-law">
    <h3>Third Law Summary</h3>
    <p>The Third Law governs cross-domain interactions and system interoperability. It ensures that cross-domain interactions maintain resonance and integrity by providing resonant mappings between different domains.</p>
  </div>
  
  <div class="card">
    <h3>Cross-Domain Translation Results</h3>
    <table>
      <tr>
        <th>Value</th>
        <th>Source Domain</th>
        <th>Target Domain</th>
        <th>Result</th>
        <th>Status</th>
      </tr>
      ${results.thirdLaw.translationResults.slice(0, 10).map(result => `
      <tr>
        <td>${result.value}</td>
        <td>${result.sourceDomain}</td>
        <td>${result.targetDomain}</td>
        <td>${result.valid ? `${result.result.value} (${result.result.representation})` : 'Error'}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
    <p><em>Showing 10 of ${results.thirdLaw.translationResults.length} translations</em></p>
  </div>
  
  <h2>Complete Trinity</h2>
  
  <div class="trinity-info">
    <h3>Complete Trinity Summary</h3>
    <p>The complete Comphyological Trinity integrates all three laws to provide a comprehensive governance framework for complex systems.</p>
  </div>
  
  <div class="card">
    <h3>Complete Trinity Results</h3>
    <table>
      <tr>
        <th>Value</th>
        <th>Source Domain</th>
        <th>Target Domain</th>
        <th>Result</th>
        <th>Status</th>
      </tr>
      ${results.completeTrinity.trinityResults.slice(0, 10).map(result => `
      <tr>
        <td>${result.value}</td>
        <td>${result.sourceDomain}</td>
        <td>${result.targetDomain}</td>
        <td>${result.valid ? `${result.result.value} (${result.result.representation})` : 'Error'}</td>
        <td class="${result.valid ? 'valid' : 'invalid'}">${result.valid ? 'Valid' : 'Invalid'}</td>
      </tr>
      `).join('')}
    </table>
    <p><em>Showing 10 of ${results.completeTrinity.trinityResults.length} results</em></p>
  </div>
  
  <footer>
    <p>NovaFuse Comphyological Trinity Demo - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'comphyological_trinity_demo_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
function main() {
  console.log('=== Comphyological Trinity Demo ===');
  
  // Create Comphyological Trinity
  const trinity = new ComphyologicalTrinity({
    // First Law options
    strictMode: false,
    resonanceLock: true,
    
    // Second Law options
    energyMinimization: true,
    selfSimilarityPreservation: true,
    
    // Third Law options
    domains: ['cyber', 'financial', 'medical'],
    preserveResonance: true,
    
    // General options
    logGovernance: true
  });
  
  // Test each law
  const firstLawResults = testFirstLaw(trinity);
  const secondLawResults = testSecondLaw(trinity);
  const thirdLawResults = testThirdLaw(trinity);
  const completeTrinityResults = testCompleteTrinity(trinity);
  
  // Get metrics
  const metrics = trinity.getMetrics();
  console.log('\nTrinity Metrics:', JSON.stringify(metrics.trinity, null, 2));
  
  // Generate HTML report
  const results = {
    firstLaw: firstLawResults,
    secondLaw: secondLawResults,
    thirdLaw: thirdLawResults,
    completeTrinity: completeTrinityResults,
    metrics
  };
  
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'comphyological_trinity_demo_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'comphyological_trinity_demo_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
