// NovaFuse Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Mobile Menu Toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (menuToggle && navMenu) {
        menuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('open');
        });
    }

    // Sidebar Toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const contentWithSidebar = document.querySelector('.content-with-sidebar');

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('sidebar-visible');
            if (contentWithSidebar) {
                contentWithSidebar.classList.toggle('sidebar-shifted');
            }
        });
    }

    // Close sidebar when clicking outside
    document.addEventListener('click', function(event) {
        if (sidebar && sidebar.classList.contains('sidebar-visible') &&
            !sidebar.contains(event.target) &&
            !sidebarToggle.contains(event.target)) {
            sidebar.classList.remove('sidebar-visible');
            if (contentWithSidebar) {
                contentWithSidebar.classList.remove('sidebar-shifted');
            }
        }
    });

    // Sidebar Submenu Toggle
    const submenuToggles = document.querySelectorAll('.submenu-toggle');

    if (submenuToggles.length > 0) {
        submenuToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const submenu = this.nextElementSibling;
                submenu.classList.toggle('open');
            });
        });
    }

    // Use Case Carousel
    const carouselItems = document.querySelectorAll('.carousel-item');
    const carouselIndicators = document.querySelectorAll('.carousel-indicator');
    const prevButton = document.querySelector('.carousel-prev');
    const nextButton = document.querySelector('.carousel-next');

    if (carouselItems.length > 0) {
        let currentIndex = 0;

        function showSlide(index) {
            // Hide all slides
            carouselItems.forEach(item => {
                item.classList.remove('active');
            });

            // Deactivate all indicators
            if (carouselIndicators.length > 0) {
                carouselIndicators.forEach(indicator => {
                    indicator.classList.remove('active');
                });
            }

            // Show the current slide
            carouselItems[index].classList.add('active');

            // Activate the current indicator
            if (carouselIndicators.length > 0) {
                carouselIndicators[index].classList.add('active');
            }
        }

        function nextSlide() {
            currentIndex = (currentIndex + 1) % carouselItems.length;
            showSlide(currentIndex);
        }

        function prevSlide() {
            currentIndex = (currentIndex - 1 + carouselItems.length) % carouselItems.length;
            showSlide(currentIndex);
        }

        // Set up event listeners for carousel controls
        if (nextButton) {
            nextButton.addEventListener('click', nextSlide);
        }

        if (prevButton) {
            prevButton.addEventListener('click', prevSlide);
        }

        // Set up event listeners for carousel indicators
        if (carouselIndicators.length > 0) {
            carouselIndicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => {
                    currentIndex = index;
                    showSlide(currentIndex);
                });
            });
        }

        // Auto-advance the carousel every 5 seconds
        setInterval(nextSlide, 5000);
    }

    // Form Validation
    const forms = document.querySelectorAll('form');

    if (forms.length > 0) {
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();

                // Basic validation
                let isValid = true;
                const requiredFields = form.querySelectorAll('[required]');

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('error');
                    } else {
                        field.classList.remove('error');
                    }
                });

                if (isValid) {
                    // Show success message
                    const successMessage = document.createElement('div');
                    successMessage.className = 'alert alert-success';
                    successMessage.textContent = 'Form submitted successfully!';

                    form.appendChild(successMessage);

                    // Reset form after 3 seconds
                    setTimeout(() => {
                        form.reset();
                        successMessage.remove();
                    }, 3000);
                }
            });
        });
    }

    // Back to top button
    const backToTopButton = document.querySelector('.back-to-top');

    if (backToTopButton) {
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('show');
            } else {
                backToTopButton.classList.remove('show');
            }
        });

        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
});
