/**
 * GovernorIntegrationInterface
 * 
 * This module implements the integration interface between the CSME engine and the Comphyon Governor.
 * It provides methods for processing control actions from the Governor and translating them into
 * Ψ-Revert protocols.
 * 
 * The GovernorIntegrationInterface is responsible for:
 * 1. Processing control actions from the Governor
 * 2. Translating control actions into Ψ-Revert protocols
 * 3. Providing feedback on intervention effectiveness
 * 4. Supporting multi-level control action mapping
 */

const { EventEmitter } = require('events');

/**
 * GovernorIntegrationInterface class
 */
class GovernorIntegrationInterface extends EventEmitter {
  /**
   * Create a new GovernorIntegrationInterface instance
   * @param {Object} csmeController - CSME controller instance
   * @param {Object} psiRevertProtocolEngine - PsiRevertProtocolEngine instance
   * @param {Object} options - Configuration options
   */
  constructor(csmeController, psiRevertProtocolEngine, options = {}) {
    super();
    
    if (!csmeController) {
      throw new Error('CSME controller is required');
    }
    
    if (!psiRevertProtocolEngine) {
      throw new Error('PsiRevertProtocolEngine is required');
    }
    
    this.csmeController = csmeController;
    this.psiRevertProtocolEngine = psiRevertProtocolEngine;
    this.options = {
      enableLogging: false,
      controlActionMappings: {
        // Default mappings from Governor control actions to protocol types
        'resource_throttling': 'environmental',
        'circuit_breaking': 'emergency',
        'forced_diversity': 'genetic',
        'ethical_guardrails': 'behavioral',
        'gradient_dampening': 'dietary',
        'coupling_modulation': 'cognitive'
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      isConnected: false,
      lastUpdateTime: null,
      lastControlAction: null,
      activeInterventions: [],
      interventionHistory: []
    };
    
    console.log('GovernorIntegrationInterface initialized');
  }
  
  /**
   * Connect to the Comphyon Governor
   * @param {Object} governor - Comphyon Governor instance
   * @returns {boolean} - Connection success status
   */
  connect(governor) {
    if (!governor) {
      console.error('Governor instance is required');
      return false;
    }
    
    this.governor = governor;
    this.state.isConnected = true;
    this.state.lastUpdateTime = Date.now();
    
    // Subscribe to governor events
    if (this.governor.on) {
      this.governor.on('control', this._handleGovernorControl.bind(this));
      this.governor.on('update', this._handleGovernorUpdate.bind(this));
    }
    
    if (this.options.enableLogging) {
      console.log('Connected to Comphyon Governor');
    }
    
    return true;
  }
  
  /**
   * Disconnect from the Comphyon Governor
   */
  disconnect() {
    if (!this.state.isConnected) {
      return;
    }
    
    // Unsubscribe from governor events
    if (this.governor && this.governor.removeListener) {
      this.governor.removeListener('control', this._handleGovernorControl.bind(this));
      this.governor.removeListener('update', this._handleGovernorUpdate.bind(this));
    }
    
    this.governor = null;
    this.state.isConnected = false;
    
    if (this.options.enableLogging) {
      console.log('Disconnected from Comphyon Governor');
    }
  }
  
  /**
   * Process control action from the Governor
   * @param {Object} controlAction - Control action from the Governor
   * @returns {Object} - Processing result
   */
  processControlAction(controlAction) {
    if (!controlAction) {
      return { success: false, reason: 'No control action provided' };
    }
    
    // Store control action
    this.state.lastControlAction = controlAction;
    this.state.lastUpdateTime = Date.now();
    
    // Log control action if logging is enabled
    if (this.options.enableLogging) {
      console.log('Processing control action:', controlAction.type, controlAction.severity);
    }
    
    // Translate control action to protocol
    const protocol = this.translateToProtocol(controlAction);
    
    // If no protocol could be translated, return failure
    if (!protocol) {
      return { success: false, reason: 'Could not translate control action to protocol' };
    }
    
    // Apply protocol through CSME controller
    let result;
    try {
      result = this.csmeController.applyProtocol(protocol);
      
      // Store intervention in active interventions
      if (result.success) {
        const intervention = {
          id: `intervention-${Date.now()}`,
          controlAction,
          protocol,
          appliedAt: new Date().toISOString(),
          status: 'active',
          effectiveness: null
        };
        
        this.state.activeInterventions.push(intervention);
        
        // Emit intervention event
        this.emit('intervention', intervention);
      }
    } catch (error) {
      console.error('Error applying protocol:', error);
      result = { success: false, reason: error.message };
    }
    
    return result;
  }
  
  /**
   * Translate control action into Ψ-Revert protocol
   * @param {Object} controlAction - Control action from the Governor
   * @returns {Object} - Translated protocol
   */
  translateToProtocol(controlAction) {
    if (!controlAction || !controlAction.type) {
      return null;
    }
    
    // Extract control action details
    const { type, severity, actions, reason, trinity_levels } = controlAction;
    
    // Skip if no actions
    if (!actions || actions.length === 0) {
      return null;
    }
    
    // Map control action type to protocol type
    const protocolType = this.options.controlActionMappings[type] || 'standard';
    
    // Prepare subject state for protocol selection
    const subjectState = this.csmeController.getCurrentSubjectState();
    
    // Override coherence based on severity
    let coherence = subjectState.coherence;
    switch (severity) {
      case 'critical':
        coherence = 0.2; // Critical severity -> very low coherence
        break;
      case 'high':
        coherence = 0.4; // High severity -> low coherence
        break;
      case 'medium':
        coherence = 0.6; // Medium severity -> medium coherence
        break;
      case 'low':
        coherence = 0.7; // Low severity -> moderately high coherence
        break;
    }
    
    // Create modified subject state with control action context
    const modifiedSubjectState = {
      ...subjectState,
      coherence,
      controlAction: {
        type,
        severity,
        trinityLevels: trinity_levels
      }
    };
    
    // Select protocol using PsiRevertProtocolEngine
    const protocolResult = this.psiRevertProtocolEngine.selectProtocol(modifiedSubjectState);
    
    // If no protocol was selected, return null
    if (!protocolResult || !protocolResult.protocol) {
      return null;
    }
    
    // Enhance protocol with control action context
    const enhancedProtocol = {
      ...protocolResult.protocol,
      controlActionContext: {
        type,
        severity,
        reason,
        trinityLevels: trinity_levels,
        actions: actions.map(a => a.type).join(', ')
      },
      instructions: {
        ...protocolResult.instructions,
        governorNote: `This protocol was triggered by the Comphyon Governor due to ${reason}.`
      }
    };
    
    return enhancedProtocol;
  }
  
  /**
   * Provide feedback on intervention effectiveness
   * @param {string} interventionId - Intervention ID
   * @param {Object} effectivenessMetrics - Effectiveness metrics
   * @returns {boolean} - Success status
   */
  provideFeedback(interventionId, effectivenessMetrics) {
    // Find intervention in active interventions
    const interventionIndex = this.state.activeInterventions.findIndex(i => i.id === interventionId);
    if (interventionIndex === -1) {
      return false;
    }
    
    // Update intervention with effectiveness metrics
    const intervention = this.state.activeInterventions[interventionIndex];
    intervention.effectiveness = effectivenessMetrics;
    intervention.status = 'completed';
    intervention.completedAt = new Date().toISOString();
    
    // Move intervention to history
    this.state.interventionHistory.push(intervention);
    this.state.activeInterventions.splice(interventionIndex, 1);
    
    // Send feedback to Governor if connected
    if (this.state.isConnected && this.governor && this.governor.receiveFeedback) {
      this.governor.receiveFeedback(intervention.controlAction, effectivenessMetrics);
    }
    
    // Emit feedback event
    this.emit('feedback', { intervention, effectivenessMetrics });
    
    return true;
  }
  
  /**
   * Get current integration status
   * @returns {Object} - Integration status
   */
  getStatus() {
    return {
      isConnected: this.state.isConnected,
      lastUpdateTime: this.state.lastUpdateTime,
      lastControlAction: this.state.lastControlAction,
      activeInterventions: this.state.activeInterventions.length,
      interventionHistory: this.state.interventionHistory.length
    };
  }
  
  /**
   * Handle control action from the Governor
   * @param {Object} controlAction - Control action from the Governor
   * @private
   */
  _handleGovernorControl(controlAction) {
    this.processControlAction(controlAction);
  }
  
  /**
   * Handle update from the Governor
   * @param {Object} updateData - Update data from the Governor
   * @private
   */
  _handleGovernorUpdate(updateData) {
    // Store update time
    this.state.lastUpdateTime = Date.now();
    
    // Emit update event
    this.emit('governor-update', updateData);
  }
  
  /**
   * Get active interventions
   * @returns {Array} - Active interventions
   */
  getActiveInterventions() {
    return [...this.state.activeInterventions];
  }
  
  /**
   * Get intervention history
   * @returns {Array} - Intervention history
   */
  getInterventionHistory() {
    return [...this.state.interventionHistory];
  }
  
  /**
   * Clear intervention history
   */
  clearInterventionHistory() {
    this.state.interventionHistory = [];
  }
}

module.exports = GovernorIntegrationInterface;
