/**
 * Certifications & Accreditation Connector Implementation
 * 
 * This module implements the connector for certification and accreditation management systems.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('certifications-accreditation-connector');

/**
 * Certifications & Accreditation Connector
 */
class CertificationsAccreditationConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing Certifications & Accreditation connector');
    
    // Authenticate if needed
    if (this.credentials.clientId && this.credentials.clientSecret) {
      await this.authenticate();
    }
  }

  /**
   * Authenticate with the API
   * 
   * @returns {Promise<void>}
   */
  async authenticate() {
    logger.info('Authenticating with Certifications & Accreditation API');
    
    try {
      const response = await axios.post(`${this.baseUrl}/oauth2/token`, {
        grant_type: 'client_credentials',
        client_id: this.credentials.clientId,
        client_secret: this.credentials.clientSecret,
        scope: 'read:certifications write:certifications read:assessments write:assessments read:evidence write:evidence'
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json'
        }
      });
      
      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);
      
      logger.info('Authentication successful');
    } catch (error) {
      logger.error('Authentication failed', { error: error.message });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Promise<Object>} - Authentication headers
   */
  async getAuthHeaders() {
    // Check if token is expired or about to expire (within 5 minutes)
    if (!this.accessToken || (this.tokenExpiry && this.tokenExpiry - Date.now() < 300000)) {
      await this.authenticate();
    }
    
    return {
      'Authorization': `Bearer ${this.accessToken}`
    };
  }

  /**
   * List certifications
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of certifications
   */
  async listCertifications(params = {}) {
    logger.info('Listing certifications', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/certifications`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing certifications', { error: error.message });
      throw new Error(`Error listing certifications: ${error.message}`);
    }
  }

  /**
   * Get a certification
   * 
   * @param {string} certificationId - Certification ID
   * @returns {Promise<Object>} - Certification details
   */
  async getCertification(certificationId) {
    logger.info('Getting certification', { certificationId });
    
    if (!certificationId) {
      throw new Error('Certification ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/certifications/${certificationId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting certification', { certificationId, error: error.message });
      throw new Error(`Error getting certification: ${error.message}`);
    }
  }

  /**
   * Create a certification
   * 
   * @param {Object} certificationData - Certification data
   * @returns {Promise<Object>} - Created certification
   */
  async createCertification(certificationData) {
    logger.info('Creating certification');
    
    if (!certificationData) {
      throw new Error('Certification data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name', 'type'];
    for (const field of requiredFields) {
      if (!certificationData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/certifications`, certificationData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating certification', { error: error.message });
      throw new Error(`Error creating certification: ${error.message}`);
    }
  }

  /**
   * Update a certification
   * 
   * @param {string} certificationId - Certification ID
   * @param {Object} certificationData - Certification data
   * @returns {Promise<Object>} - Updated certification
   */
  async updateCertification(certificationId, certificationData) {
    logger.info('Updating certification', { certificationId });
    
    if (!certificationId) {
      throw new Error('Certification ID is required');
    }
    
    if (!certificationData) {
      throw new Error('Certification data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/certifications/${certificationId}`, certificationData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating certification', { certificationId, error: error.message });
      throw new Error(`Error updating certification: ${error.message}`);
    }
  }

  /**
   * Delete a certification
   * 
   * @param {string} certificationId - Certification ID
   * @returns {Promise<void>}
   */
  async deleteCertification(certificationId) {
    logger.info('Deleting certification', { certificationId });
    
    if (!certificationId) {
      throw new Error('Certification ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      await axios.delete(`${this.baseUrl}/certifications/${certificationId}`, {
        headers: {
          ...authHeaders,
          'Accept': 'application/json'
        }
      });
      
      logger.info('Certification deleted successfully', { certificationId });
    } catch (error) {
      logger.error('Error deleting certification', { certificationId, error: error.message });
      throw new Error(`Error deleting certification: ${error.message}`);
    }
  }

  /**
   * List assessments
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of assessments
   */
  async listAssessments(params = {}) {
    logger.info('Listing assessments', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/assessments`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing assessments', { error: error.message });
      throw new Error(`Error listing assessments: ${error.message}`);
    }
  }

  /**
   * Get an assessment
   * 
   * @param {string} assessmentId - Assessment ID
   * @returns {Promise<Object>} - Assessment details
   */
  async getAssessment(assessmentId) {
    logger.info('Getting assessment', { assessmentId });
    
    if (!assessmentId) {
      throw new Error('Assessment ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/assessments/${assessmentId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting assessment', { assessmentId, error: error.message });
      throw new Error(`Error getting assessment: ${error.message}`);
    }
  }

  /**
   * Create an assessment
   * 
   * @param {Object} assessmentData - Assessment data
   * @returns {Promise<Object>} - Created assessment
   */
  async createAssessment(assessmentData) {
    logger.info('Creating assessment');
    
    if (!assessmentData) {
      throw new Error('Assessment data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name', 'certificationId'];
    for (const field of requiredFields) {
      if (!assessmentData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/assessments`, assessmentData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating assessment', { error: error.message });
      throw new Error(`Error creating assessment: ${error.message}`);
    }
  }

  /**
   * Update an assessment
   * 
   * @param {string} assessmentId - Assessment ID
   * @param {Object} assessmentData - Assessment data
   * @returns {Promise<Object>} - Updated assessment
   */
  async updateAssessment(assessmentId, assessmentData) {
    logger.info('Updating assessment', { assessmentId });
    
    if (!assessmentId) {
      throw new Error('Assessment ID is required');
    }
    
    if (!assessmentData) {
      throw new Error('Assessment data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/assessments/${assessmentId}`, assessmentData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating assessment', { assessmentId, error: error.message });
      throw new Error(`Error updating assessment: ${error.message}`);
    }
  }

  /**
   * List evidence
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of evidence
   */
  async listEvidence(params = {}) {
    logger.info('Listing evidence', { params });
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/evidence`, {
        params,
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing evidence', { error: error.message });
      throw new Error(`Error listing evidence: ${error.message}`);
    }
  }

  /**
   * Get evidence
   * 
   * @param {string} evidenceId - Evidence ID
   * @returns {Promise<Object>} - Evidence details
   */
  async getEvidence(evidenceId) {
    logger.info('Getting evidence', { evidenceId });
    
    if (!evidenceId) {
      throw new Error('Evidence ID is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.get(`${this.baseUrl}/evidence/${evidenceId}`, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting evidence', { evidenceId, error: error.message });
      throw new Error(`Error getting evidence: ${error.message}`);
    }
  }

  /**
   * Create evidence
   * 
   * @param {Object} evidenceData - Evidence data
   * @returns {Promise<Object>} - Created evidence
   */
  async createEvidence(evidenceData) {
    logger.info('Creating evidence');
    
    if (!evidenceData) {
      throw new Error('Evidence data is required');
    }
    
    // Validate required fields
    const requiredFields = ['name', 'assessmentId', 'controlId'];
    for (const field of requiredFields) {
      if (!evidenceData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.post(`${this.baseUrl}/evidence`, evidenceData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating evidence', { error: error.message });
      throw new Error(`Error creating evidence: ${error.message}`);
    }
  }

  /**
   * Update evidence
   * 
   * @param {string} evidenceId - Evidence ID
   * @param {Object} evidenceData - Evidence data
   * @returns {Promise<Object>} - Updated evidence
   */
  async updateEvidence(evidenceId, evidenceData) {
    logger.info('Updating evidence', { evidenceId });
    
    if (!evidenceId) {
      throw new Error('Evidence ID is required');
    }
    
    if (!evidenceData) {
      throw new Error('Evidence data is required');
    }
    
    try {
      const authHeaders = await this.getAuthHeaders();
      
      const response = await axios.put(`${this.baseUrl}/evidence/${evidenceId}`, evidenceData, {
        headers: {
          ...authHeaders,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating evidence', { evidenceId, error: error.message });
      throw new Error(`Error updating evidence: ${error.message}`);
    }
  }
}

module.exports = CertificationsAccreditationConnector;
