import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  <PERSON>per, 
  Step, 
  StepLabel, 
  Button, 
  Paper,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  IconButton,
  Alert
} from '@mui/material';
import {
  Save as SaveIcon,
  PlayArrow as TestIcon,
  Add as AddIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

// Step components
import MetadataForm from '../components/builder/MetadataForm';
import AuthenticationForm from '../components/builder/AuthenticationForm';
import ConfigurationForm from '../components/builder/ConfigurationForm';
import EndpointsForm from '../components/builder/EndpointsForm';
import MappingsForm from '../components/builder/MappingsForm';
import EventsForm from '../components/builder/EventsForm';
import ReviewForm from '../components/builder/ReviewForm';

const steps = [
  'Metadata',
  'Authentication',
  'Configuration',
  'Endpoints',
  'Mappings',
  'Events',
  'Review'
];

export default function ConnectorBuilder() {
  const [activeStep, setActiveStep] = useState(0);
  const [connector, setConnector] = useState({
    metadata: {
      name: '',
      version: '1.0.0',
      category: '',
      description: '',
      author: 'NovaGRC',
      tags: [],
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    },
    authentication: {
      type: 'API_KEY',
      fields: {},
      testConnection: {
        endpoint: '',
        method: 'GET',
        expectedResponse: {
          status: 200
        }
      }
    },
    configuration: {
      baseUrl: '',
      headers: {},
      timeout: 30000,
      retryPolicy: {
        maxRetries: 3,
        backoffStrategy: 'exponential'
      }
    },
    endpoints: [],
    mappings: [],
    events: {
      webhooks: [],
      polling: []
    }
  });
  const [testResult, setTestResult] = useState(null);
  const [saving, setSaving] = useState(false);
  const [saved, setSaved] = useState(false);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  const handleSave = async () => {
    setSaving(true);
    
    try {
      // In a real implementation, this would call an API to save the connector
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    } catch (error) {
      console.error('Error saving connector:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleTest = async () => {
    try {
      // In a real implementation, this would call an API to test the connector
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setTestResult({
        success: true,
        message: 'Connection test successful',
        data: {
          status: 200,
          statusText: 'OK'
        }
      });
    } catch (error) {
      console.error('Error testing connector:', error);
      
      setTestResult({
        success: false,
        message: 'Connection test failed',
        error: error.message
      });
    }
  };

  const updateConnector = (section, data) => {
    setConnector(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        ...data
      }
    }));
  };

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return <MetadataForm connector={connector} updateConnector={updateConnector} />;
      case 1:
        return <AuthenticationForm connector={connector} updateConnector={updateConnector} />;
      case 2:
        return <ConfigurationForm connector={connector} updateConnector={updateConnector} />;
      case 3:
        return <EndpointsForm connector={connector} updateConnector={updateConnector} />;
      case 4:
        return <MappingsForm connector={connector} updateConnector={updateConnector} />;
      case 5:
        return <EventsForm connector={connector} updateConnector={updateConnector} />;
      case 6:
        return <ReviewForm connector={connector} />;
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" gutterBottom>
          Connector Builder
        </Typography>
        <Box>
          <Button 
            variant="outlined" 
            startIcon={<TestIcon />}
            onClick={handleTest}
            sx={{ mr: 2 }}
          >
            Test Connector
          </Button>
          <Button 
            variant="contained" 
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : 'Save Connector'}
          </Button>
        </Box>
      </Box>

      {saved && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Connector saved successfully!
        </Alert>
      )}

      {testResult && (
        <Alert 
          severity={testResult.success ? "success" : "error"} 
          sx={{ mb: 2 }}
          onClose={() => setTestResult(null)}
        >
          {testResult.message}
        </Alert>
      )}

      <Paper sx={{ p: 3, mb: 4, backgroundColor: 'background.paper' }}>
        <Stepper activeStep={activeStep} alternativeLabel>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>
      </Paper>

      <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
        {activeStep === steps.length ? (
          <Box>
            <Typography sx={{ mt: 2, mb: 1 }}>
              All steps completed - your connector is ready to use!
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
              <Box sx={{ flex: '1 1 auto' }} />
              <Button onClick={handleReset}>Create Another Connector</Button>
            </Box>
          </Box>
        ) : (
          <Box>
            <Typography variant="h6" gutterBottom>
              {steps[activeStep]}
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            {getStepContent(activeStep)}
            
            <Box sx={{ display: 'flex', flexDirection: 'row', pt: 4 }}>
              <Button
                color="inherit"
                disabled={activeStep === 0}
                onClick={handleBack}
                sx={{ mr: 1 }}
              >
                Back
              </Button>
              <Box sx={{ flex: '1 1 auto' }} />
              <Button onClick={handleNext}>
                {activeStep === steps.length - 1 ? 'Finish' : 'Next'}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>
    </Box>
  );
}
