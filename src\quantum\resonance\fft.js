/**
 * Fast Fourier Transform (FFT) Implementation
 * 
 * This module provides a simple FFT implementation for the resonance detector.
 * It is optimized for detecting harmonic patterns in the Comphyological Tensor Core.
 */

/**
 * FFT class
 */
class FFT {
  /**
   * Constructor
   * @param {number} size - FFT size (must be a power of 2)
   * @param {number} sampleRate - Sample rate in Hz
   */
  constructor(size, sampleRate) {
    // Ensure size is a power of 2
    this.size = this.nextPowerOf2(size);
    this.sampleRate = sampleRate;
    
    // Initialize buffers
    this.real = new Float64Array(this.size);
    this.imag = new Float64Array(this.size);
    this.spectrum = new Float64Array(this.size);
    
    // Precompute twiddle factors
    this.precomputeTwiddleFactors();
    
    // Precompute bit-reversal table
    this.precomputeBitReversal();
  }
  
  /**
   * Get the next power of 2
   * @param {number} n - Number
   * @returns {number} Next power of 2
   */
  nextPowerOf2(n) {
    return Math.pow(2, Math.ceil(Math.log2(n)));
  }
  
  /**
   * Precompute twiddle factors
   */
  precomputeTwiddleFactors() {
    this.twiddleReal = new Float64Array(this.size / 2);
    this.twiddleImag = new Float64Array(this.size / 2);
    
    for (let i = 0; i < this.size / 2; i++) {
      const angle = -2 * Math.PI * i / this.size;
      this.twiddleReal[i] = Math.cos(angle);
      this.twiddleImag[i] = Math.sin(angle);
    }
  }
  
  /**
   * Precompute bit-reversal table
   */
  precomputeBitReversal() {
    this.bitReversal = new Uint32Array(this.size);
    
    const bits = Math.log2(this.size);
    
    for (let i = 0; i < this.size; i++) {
      let reversed = 0;
      let j = i;
      
      for (let b = 0; b < bits; b++) {
        reversed = (reversed << 1) | (j & 1);
        j >>= 1;
      }
      
      this.bitReversal[i] = reversed;
    }
  }
  
  /**
   * Perform forward FFT
   * @param {Array|Float64Array} input - Input signal
   * @returns {Float64Array} Spectrum
   */
  forward(input) {
    // Copy input to real buffer
    for (let i = 0; i < this.size; i++) {
      this.real[i] = i < input.length ? input[i] : 0;
      this.imag[i] = 0;
    }
    
    // Apply window function (Hann window)
    this.applyWindow();
    
    // Perform bit-reversal
    this.bitReversalPermutation();
    
    // Perform FFT
    this.performFFT();
    
    // Calculate spectrum
    this.calculateSpectrum();
    
    return this.spectrum;
  }
  
  /**
   * Apply window function (Hann window)
   */
  applyWindow() {
    for (let i = 0; i < this.size; i++) {
      const window = 0.5 * (1 - Math.cos(2 * Math.PI * i / (this.size - 1)));
      this.real[i] *= window;
    }
  }
  
  /**
   * Perform bit-reversal permutation
   */
  bitReversalPermutation() {
    for (let i = 0; i < this.size; i++) {
      const j = this.bitReversal[i];
      
      if (j > i) {
        // Swap real
        const tempReal = this.real[i];
        this.real[i] = this.real[j];
        this.real[j] = tempReal;
        
        // Swap imag
        const tempImag = this.imag[i];
        this.imag[i] = this.imag[j];
        this.imag[j] = tempImag;
      }
    }
  }
  
  /**
   * Perform FFT
   */
  performFFT() {
    const n = this.size;
    
    // Perform FFT
    for (let stage = 1; stage <= Math.log2(n); stage++) {
      const m = Math.pow(2, stage);
      const mHalf = m / 2;
      
      for (let k = 0; k < n; k += m) {
        for (let j = 0; j < mHalf; j++) {
          const twiddle = j * (n / m);
          const tReal = this.twiddleReal[twiddle];
          const tImag = this.twiddleImag[twiddle];
          
          const aIndex = k + j;
          const bIndex = k + j + mHalf;
          
          const aReal = this.real[aIndex];
          const aImag = this.imag[aIndex];
          const bReal = this.real[bIndex];
          const bImag = this.imag[bIndex];
          
          // Complex multiplication
          const cReal = bReal * tReal - bImag * tImag;
          const cImag = bReal * tImag + bImag * tReal;
          
          // Butterfly operation
          this.real[aIndex] = aReal + cReal;
          this.imag[aIndex] = aImag + cImag;
          this.real[bIndex] = aReal - cReal;
          this.imag[bIndex] = aImag - cImag;
        }
      }
    }
  }
  
  /**
   * Calculate spectrum
   */
  calculateSpectrum() {
    const n = this.size;
    
    for (let i = 0; i < n; i++) {
      // Calculate magnitude
      this.spectrum[i] = Math.sqrt(
        this.real[i] * this.real[i] + 
        this.imag[i] * this.imag[i]
      ) / n;
    }
  }
  
  /**
   * Get frequency for bin
   * @param {number} bin - Bin index
   * @returns {number} Frequency in Hz
   */
  getFrequency(bin) {
    return bin * this.sampleRate / this.size;
  }
  
  /**
   * Get bin for frequency
   * @param {number} frequency - Frequency in Hz
   * @returns {number} Bin index
   */
  getBin(frequency) {
    return Math.round(frequency * this.size / this.sampleRate);
  }
  
  /**
   * Get magnitude at frequency
   * @param {number} frequency - Frequency in Hz
   * @returns {number} Magnitude
   */
  getMagnitude(frequency) {
    const bin = this.getBin(frequency);
    
    if (bin < 0 || bin >= this.size) {
      return 0;
    }
    
    return this.spectrum[bin];
  }
  
  /**
   * Get phase at frequency
   * @param {number} frequency - Frequency in Hz
   * @returns {number} Phase in radians
   */
  getPhase(frequency) {
    const bin = this.getBin(frequency);
    
    if (bin < 0 || bin >= this.size) {
      return 0;
    }
    
    return Math.atan2(this.imag[bin], this.real[bin]);
  }
  
  /**
   * Find peaks in spectrum
   * @param {number} threshold - Threshold
   * @param {number} minFrequency - Minimum frequency
   * @param {number} maxFrequency - Maximum frequency
   * @returns {Array} Peaks
   */
  findPeaks(threshold = 0.1, minFrequency = 0, maxFrequency = this.sampleRate / 2) {
    const minBin = this.getBin(minFrequency);
    const maxBin = this.getBin(maxFrequency);
    const peaks = [];
    
    for (let i = Math.max(2, minBin); i < Math.min(this.size / 2 - 2, maxBin); i++) {
      if (
        this.spectrum[i] > threshold &&
        this.spectrum[i] > this.spectrum[i - 1] &&
        this.spectrum[i] > this.spectrum[i - 2] &&
        this.spectrum[i] > this.spectrum[i + 1] &&
        this.spectrum[i] > this.spectrum[i + 2]
      ) {
        peaks.push({
          bin: i,
          frequency: this.getFrequency(i),
          magnitude: this.spectrum[i],
          phase: Math.atan2(this.imag[i], this.real[i])
        });
      }
    }
    
    return peaks;
  }
  
  /**
   * Find harmonics of fundamental frequency
   * @param {number} fundamental - Fundamental frequency
   * @param {number} threshold - Threshold
   * @param {number} maxHarmonics - Maximum number of harmonics
   * @returns {Array} Harmonics
   */
  findHarmonics(fundamental, threshold = 0.1, maxHarmonics = 10) {
    const harmonics = [];
    
    for (let i = 1; i <= maxHarmonics; i++) {
      const frequency = fundamental * i;
      
      if (frequency > this.sampleRate / 2) {
        break;
      }
      
      const bin = this.getBin(frequency);
      const magnitude = this.spectrum[bin];
      
      if (magnitude > threshold) {
        harmonics.push({
          harmonic: i,
          frequency,
          magnitude,
          phase: Math.atan2(this.imag[bin], this.real[bin])
        });
      }
    }
    
    return harmonics;
  }
}

module.exports = { FFT };
