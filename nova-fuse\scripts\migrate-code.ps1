# NovaFuse Code Migration Script
# This script automates the process of migrating code from the existing repositories to the new structure.

# Configuration
$sourceRoot = "D:\novafuse-api-superstore"  # Root directory of the source code
$destinationRoot = "D:\"  # Root directory where the new repositories are located
$logFile = "D:\nova-fuse\scripts\migration-log.txt"  # Log file path

# Create log file if it doesn't exist
if (-not (Test-Path $logFile)) {
    New-Item -Path $logFile -ItemType File -Force | Out-Null
}

# Function to log messages
function Log-Message {
    param (
        [string]$message,
        [string]$type = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$type] $message"
    
    # Write to console
    if ($type -eq "ERROR") {
        Write-Host $logMessage -ForegroundColor Red
    }
    elseif ($type -eq "WARNING") {
        Write-Host $logMessage -ForegroundColor Yellow
    }
    elseif ($type -eq "SUCCESS") {
        Write-Host $logMessage -ForegroundColor Green
    }
    else {
        Write-Host $logMessage
    }
    
    # Write to log file
    Add-Content -Path $logFile -Value $logMessage
}

# Function to create directory if it doesn't exist
function Ensure-Directory {
    param (
        [string]$path
    )
    
    if (-not (Test-Path $path)) {
        New-Item -Path $path -ItemType Directory -Force | Out-Null
        Log-Message "Created directory: $path"
    }
}

# Function to copy files with directory structure
function Copy-Files {
    param (
        [string]$sourcePath,
        [string]$destinationPath,
        [string]$filter = "*.*"
    )
    
    # Ensure destination directory exists
    Ensure-Directory $destinationPath
    
    # Check if source path exists
    if (-not (Test-Path $sourcePath)) {
        Log-Message "Source path does not exist: $sourcePath" "ERROR"
        return
    }
    
    # Get all files in the source directory and subdirectories
    $files = Get-ChildItem -Path $sourcePath -Filter $filter -Recurse -File
    
    # Copy each file to the destination directory
    foreach ($file in $files) {
        # Get the relative path of the file
        $relativePath = $file.FullName.Substring($sourcePath.Length)
        
        # Create the destination path
        $destinationFilePath = Join-Path -Path $destinationPath -ChildPath $relativePath
        
        # Ensure the destination directory exists
        $destinationFileDir = Split-Path -Path $destinationFilePath -Parent
        Ensure-Directory $destinationFileDir
        
        # Copy the file
        Copy-Item -Path $file.FullName -Destination $destinationFilePath -Force
        Log-Message "Copied file: $($file.FullName) -> $destinationFilePath"
    }
    
    Log-Message "Copied all files from $sourcePath to $destinationPath" "SUCCESS"
}

# Function to update import paths in JavaScript/TypeScript files
function Update-ImportPaths {
    param (
        [string]$directory,
        [hashtable]$replacements
    )
    
    # Get all JavaScript/TypeScript files in the directory and subdirectories
    $files = Get-ChildItem -Path $directory -Include "*.js", "*.jsx", "*.ts", "*.tsx" -Recurse -File
    
    # Update import paths in each file
    foreach ($file in $files) {
        $content = Get-Content -Path $file.FullName -Raw
        $updated = $false
        
        # Apply each replacement
        foreach ($key in $replacements.Keys) {
            $oldPath = $key
            $newPath = $replacements[$key]
            
            # Check if the file contains the old path
            if ($content -match $oldPath) {
                $content = $content -replace $oldPath, $newPath
                $updated = $true
                Log-Message "Updated import path in $($file.FullName): $oldPath -> $newPath"
            }
        }
        
        # Save the file if it was updated
        if ($updated) {
            Set-Content -Path $file.FullName -Value $content
            Log-Message "Saved updated file: $($file.FullName)" "SUCCESS"
        }
    }
}

# Migration mappings
$migrationMappings = @(
    # NovaConnect Migration
    @{
        SourcePath = "$sourceRoot\connector-templates"
        DestinationPath = "$destinationRoot\nova-connect"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/connector-templates" = "from '@nova-connect"
            "require\('\.\.\/connector-templates" = "require('@nova-connect"
        }
    },
    
    # Privacy Management API Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\apis\privacy\management"
        DestinationPath = "$destinationRoot\nova-grc-apis\privacy-management"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/\.\.\/\.\.\/\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/\.\.\/\.\.\/\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # Regulatory Compliance API Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\apis\compliance"
        DestinationPath = "$destinationRoot\nova-grc-apis\regulatory-compliance"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/\.\.\/\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/\.\.\/\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # Security Assessment API Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\apis\security\assessment"
        DestinationPath = "$destinationRoot\nova-grc-apis\security-assessment"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/\.\.\/\.\.\/\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/\.\.\/\.\.\/\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # Control Testing API Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\apis\control\testing"
        DestinationPath = "$destinationRoot\nova-grc-apis\control-testing"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/\.\.\/\.\.\/\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/\.\.\/\.\.\/\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # ESG API Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\apis\esg"
        DestinationPath = "$destinationRoot\nova-grc-apis\esg"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/\.\.\/\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/\.\.\/\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # Compliance Automation API Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\apis\compliance\automation"
        DestinationPath = "$destinationRoot\nova-grc-apis\compliance-automation"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/\.\.\/\.\.\/\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/\.\.\/\.\.\/\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # Common Utilities Migration
    @{
        SourcePath = "$sourceRoot\nova-marketplace\common"
        DestinationPath = "$destinationRoot\nova-grc-apis\shared"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/common" = "from '@nova-grc-apis/shared"
            "require\('\.\.\/common" = "require('@nova-grc-apis/shared"
        }
    },
    
    # API Gateway Migration
    @{
        SourcePath = "$sourceRoot\api-gateway"
        DestinationPath = "$destinationRoot\nova-gateway"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/api-gateway" = "from '@nova-gateway"
            "require\('\.\.\/api-gateway" = "require('@nova-gateway"
        }
    },
    
    # UI Components Migration (if available)
    @{
        SourcePath = "$sourceRoot\marketplace-ui\components"
        DestinationPath = "$destinationRoot\nova-ui\packages\ui-components"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/components" = "from '@nova-ui/ui-components"
            "import \{ (.*) \} from '\.\.\/utils" = "import { $1 } from '@nova-ui/utils"
        }
    },
    
    # UI Utils Migration (if available)
    @{
        SourcePath = "$sourceRoot\marketplace-ui\utils"
        DestinationPath = "$destinationRoot\nova-ui\packages\utils"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/utils" = "from '@nova-ui/utils"
        }
    },
    
    # UI Pages Migration (if available)
    @{
        SourcePath = "$sourceRoot\marketplace-ui\pages"
        DestinationPath = "$destinationRoot\nova-ui\pages"
        Filter = "*.*"
        ImportPathReplacements = @{
            "from '\.\.\/components" = "from '@nova-ui/ui-components"
            "from '\.\.\/utils" = "from '@nova-ui/utils"
        }
    }
)

# Main migration process
Log-Message "Starting NovaFuse code migration..." "INFO"
Log-Message "Source root: $sourceRoot" "INFO"
Log-Message "Destination root: $destinationRoot" "INFO"

# Process each migration mapping
foreach ($mapping in $migrationMappings) {
    Log-Message "Processing migration: $($mapping.SourcePath) -> $($mapping.DestinationPath)" "INFO"
    
    # Copy files
    Copy-Files -sourcePath $mapping.SourcePath -destinationPath $mapping.DestinationPath -filter $mapping.Filter
    
    # Update import paths
    if ($mapping.ImportPathReplacements) {
        Update-ImportPaths -directory $mapping.DestinationPath -replacements $mapping.ImportPathReplacements
    }
}

Log-Message "NovaFuse code migration completed successfully!" "SUCCESS"
