# NovaFuse Feature Flag System

This document provides a comprehensive guide to the NovaFuse feature flag system.

## Overview

The NovaFuse feature flag system allows us to control access to features based on product tier. This enables us to:

1. Maintain a single codebase for all product tiers
2. Provide different user experiences based on the product tier
3. Offer upgrade paths for premium features

## Product Tiers

NovaFuse offers the following product tiers:

- **NovaPrime**: Premium tier with all features enabled
- **NovaCore**: Basic tier with essential features
- **NovaShield**: Security-focused tier
- **NovaLearn**: Learning-focused tier
- **NovaAssistAI**: AI-focused tier
- **NovaMarketplace**: Marketplace-focused tier

## Feature Categories

Features are organized into the following categories:

- **Dashboard**: Overview, analytics, reports, customization
- **GRC**: Privacy, security, compliance, control, ESG
- **Advanced**: AI assistant, predictive analytics, automated remediation, custom integrations
- **Administration**: User management, role management, organization settings, audit logs
- **Learning**: Gamification, training modules, certifications, knowledge base

## Feature Flag Configuration

The feature flag configuration is defined in `nova-ui/packages/feature-flags/featureFlags.js` and includes:

```javascript
const featureFlags = {
  // NovaPrime features (all enabled)
  novaPrime: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: true,
      esg: true
    },
    
    // Advanced features
    advanced: {
      aiAssistant: true,
      predictiveAnalytics: true,
      automatedRemediation: true,
      customIntegrations: true
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: true,
      organizationSettings: true,
      auditLogs: true
    },
    
    // Learning features
    learning: {
      gamification: true,
      trainingModules: true,
      certifications: true,
      knowledgeBase: true
    }
  },
  
  // NovaCore features (limited)
  novaCore: {
    // Dashboard features
    dashboard: {
      overview: true,
      analytics: false,
      reports: true,
      customization: false
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: true,
      control: false,
      esg: false
    },
    
    // Advanced features
    advanced: {
      aiAssistant: false,
      predictiveAnalytics: false,
      automatedRemediation: false,
      customIntegrations: false
    },
    
    // Administration features
    administration: {
      userManagement: true,
      roleManagement: false,
      organizationSettings: false,
      auditLogs: false
    },
    
    // Learning features
    learning: {
      gamification: false,
      trainingModules: false,
      certifications: false,
      knowledgeBase: true
    }
  },
  
  // NovaShield features (security-focused)
  novaShield: {
    // ...
  },
  
  // NovaLearn features (learning-focused)
  novaLearn: {
    // ...
  },
  
  // NovaAssistAI features (AI-focused)
  novaAssistAI: {
    // ...
  }
};
```

## Using Feature Flags

### In React Components

The simplest way to use feature flags in React components is with the `useFeatureFlag` hook:

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function AnalyticsPanel() {
  const isAnalyticsEnabled = useFeatureFlag('dashboard', 'analytics');
  
  if (!isAnalyticsEnabled) {
    return null;
  }
  
  return (
    <div className="analytics-panel">
      {/* Analytics content */}
    </div>
  );
}
```

### With FeatureFlaggedComponent

For a more declarative approach, you can use the `FeatureFlaggedComponent`:

```jsx
import { FeatureFlaggedComponent, UpgradePrompt } from '@nova-ui/feature-flags';

function Dashboard() {
  return (
    <div className="dashboard">
      <FeatureFlaggedComponent
        category="dashboard"
        feature="analytics"
        fallback={<UpgradePrompt featureName="Analytics" />}
      >
        <AnalyticsPanel />
      </FeatureFlaggedComponent>
      
      <FeatureFlaggedComponent
        category="grc"
        feature="privacy"
        fallback={<UpgradePrompt featureName="Privacy Management" />}
      >
        <PrivacyPanel />
      </FeatureFlaggedComponent>
    </div>
  );
}
```

### In API Routes

Feature flags are applied to API routes using the middleware:

```javascript
const { checkFeatureAccess } = require('../middleware/featureFlags');

router.get('/analytics', 
  checkFeatureAccess('dashboard-analytics'),
  analyticsController.getAnalytics
);
```

## Setting Up the Feature Flag Provider

To use feature flags in your application, you need to wrap your application with the `FeatureFlagProvider`:

```jsx
import { FeatureFlagProvider, PRODUCTS } from '@nova-ui/feature-flags';

function App() {
  return (
    <FeatureFlagProvider initialProduct={PRODUCTS.NOVA_PRIME}>
      <YourApp />
    </FeatureFlagProvider>
  );
}
```

### Custom Feature Flags

You can provide custom feature flags to override the defaults:

```jsx
import { FeatureFlagProvider, PRODUCTS } from '@nova-ui/feature-flags';

function App() {
  const customFlags = {
    novaPrime: {
      dashboard: {
        betaFeature: true
      }
    }
  };
  
  return (
    <FeatureFlagProvider 
      initialProduct={PRODUCTS.NOVA_PRIME}
      customFlags={customFlags}
    >
      <YourApp />
    </FeatureFlagProvider>
  );
}
```

## Switching Products

You can switch between products using the `ProductContext`:

```jsx
import { useContext } from 'react';
import { ProductContext, PRODUCTS } from '@nova-ui/feature-flags';

function ProductSwitcher() {
  const { product, setProduct, isProductActive } = useContext(ProductContext);
  
  return (
    <div className="product-switcher">
      <button 
        onClick={() => setProduct(PRODUCTS.NOVA_PRIME)}
        className={isProductActive(PRODUCTS.NOVA_PRIME) ? 'active' : ''}
      >
        NovaPrime
      </button>
      
      <button 
        onClick={() => setProduct(PRODUCTS.NOVA_CORE)}
        className={isProductActive(PRODUCTS.NOVA_CORE) ? 'active' : ''}
      >
        NovaCore
      </button>
      
      {/* Other product buttons */}
    </div>
  );
}
```

## Adding New Features

To add a new feature to the feature flag system:

1. Add the feature to the feature flag configuration in `nova-ui/packages/feature-flags/featureFlags.js`:

```javascript
const featureFlags = {
  novaPrime: {
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: true,
      newFeature: true  // Add the new feature
    },
    // ...
  },
  novaCore: {
    dashboard: {
      overview: true,
      analytics: false,
      reports: true,
      customization: false,
      newFeature: false  // Add the new feature (disabled for NovaCore)
    },
    // ...
  },
  // ...
};
```

2. Use the feature flag in your components:

```jsx
import { useFeatureFlag } from '@nova-ui/feature-flags';

function NewFeatureComponent() {
  const isNewFeatureEnabled = useFeatureFlag('dashboard', 'newFeature');
  
  if (!isNewFeatureEnabled) {
    return null;
  }
  
  return (
    <div className="new-feature">
      {/* New feature content */}
    </div>
  );
}
```

## Adding New Products

To add a new product to the feature flag system:

1. Add the product to the `PRODUCTS` object in `nova-ui/packages/feature-flags/ProductContext.js`:

```javascript
export const PRODUCTS = {
  NOVA_PRIME: 'novaPrime',
  NOVA_CORE: 'novaCore',
  NOVA_SHIELD: 'novaShield',
  NOVA_LEARN: 'novaLearn',
  NOVA_ASSIST_AI: 'novaAssistAI',
  NEW_PRODUCT: 'newProduct'  // Add the new product
};
```

2. Add the product to the feature flag configuration in `nova-ui/packages/feature-flags/featureFlags.js`:

```javascript
const featureFlags = {
  // ...
  
  // New product features
  newProduct: {
    dashboard: {
      overview: true,
      analytics: true,
      reports: true,
      customization: false
    },
    
    // GRC features
    grc: {
      privacy: true,
      security: true,
      compliance: false,
      control: false,
      esg: false
    },
    
    // ...
  }
};
```

## Testing Feature Flags

Use the Feature Flag Demo page to test and verify feature flags:

1. Navigate to `/feature-flag-demo`
2. Use the Product Switcher to switch between product tiers
3. Verify that features are enabled/disabled as expected

## Best Practices

### 1. Use Descriptive Feature Names

Use descriptive names for features that clearly indicate what the feature is:

```javascript
// Good
dashboard: {
  analytics: true,
  reports: true,
  customization: true
}

// Bad
dashboard: {
  feature1: true,
  feature2: true,
  feature3: true
}
```

### 2. Group Related Features

Group related features together in the same category:

```javascript
// Good
grc: {
  privacy: true,
  security: true,
  compliance: true
}

// Bad
features: {
  privacy: true,
  security: true,
  compliance: true
}
```

### 3. Use the FeatureFlaggedComponent for UI Elements

Use the `FeatureFlaggedComponent` for UI elements that should be conditionally rendered:

```jsx
// Good
<FeatureFlaggedComponent
  category="dashboard"
  feature="analytics"
  fallback={<UpgradePrompt featureName="Analytics" />}
>
  <AnalyticsPanel />
</FeatureFlaggedComponent>

// Bad
{isAnalyticsEnabled && <AnalyticsPanel />}
```

### 4. Use Feature Flags for API Endpoints

Use feature flags to control access to API endpoints:

```javascript
// Good
if (!isFeatureEnabled(product, 'dashboard', 'analytics')) {
  return res.status(403).json({
    error: 'Feature not available',
    message: 'Analytics are not available in your current plan'
  });
}

// Bad
if (product !== 'novaPrime') {
  return res.status(403).json({
    error: 'Feature not available',
    message: 'Analytics are not available in your current plan'
  });
}
```

## Troubleshooting

### Feature Not Showing Up

If a feature is not showing up even though it should be enabled:

1. Check the feature flag configuration in `nova-ui/packages/feature-flags/featureFlags.js`
2. Verify that the feature is enabled for the current product tier
3. Check that the component is using the correct category and feature name
4. Use the Feature Flag Demo page to verify that the feature is enabled

### Feature Showing Up When It Shouldn't

If a feature is showing up when it should be disabled:

1. Check the feature flag configuration in `nova-ui/packages/feature-flags/featureFlags.js`
2. Verify that the feature is disabled for the current product tier
3. Check that the component is using the correct category and feature name
4. Use the Feature Flag Demo page to verify that the feature is disabled

### Product Tier Not Changing

If the product tier is not changing when using the Product Switcher:

1. Check that the `ProductContext` is properly set up
2. Verify that the `setProduct` function is being called with the correct product
3. Check that the component is wrapped with the `ProductProvider`
