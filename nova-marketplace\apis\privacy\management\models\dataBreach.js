/**
 * Data Breach Model
 * 
 * Represents a data breach incident that may affect personal data.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const dataBreachSchema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true,
    trim: true
  },
  detectionDate: {
    type: Date,
    required: true
  },
  breachDate: {
    type: Date
  },
  affectedDataCategories: [{
    type: String,
    required: true,
    trim: true
  }],
  affectedDataSubjects: [{
    type: String,
    required: true,
    trim: true
  }],
  approximateSubjectsCount: {
    type: Number
  },
  potentialImpact: {
    type: String,
    required: true,
    trim: true
  },
  rootCause: {
    type: String,
    trim: true
  },
  containmentMeasures: {
    type: String,
    trim: true
  },
  remediationMeasures: {
    type: String,
    trim: true
  },
  preventativeMeasures: {
    type: String,
    trim: true
  },
  severity: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  status: {
    type: String,
    required: true,
    enum: ['detected', 'investigating', 'contained', 'remediated', 'resolved'],
    default: 'detected'
  },
  notificationRequired: {
    type: Boolean,
    default: false
  },
  notificationThreshold: {
    type: String,
    enum: ['72-hours', 'without-undue-delay', 'not-required'],
    default: 'not-required'
  },
  supervisoryAuthorityNotified: {
    type: Boolean,
    default: false
  },
  supervisoryAuthorityNotificationDate: {
    type: Date
  },
  supervisoryAuthorityReference: {
    type: String,
    trim: true
  },
  dataSubjectsNotified: {
    type: Boolean,
    default: false
  },
  dataSubjectNotificationDate: {
    type: Date
  },
  dataSubjectNotificationMethod: {
    type: String,
    enum: ['email', 'mail', 'phone', 'public-notice', 'other'],
    trim: true
  },
  dataSubjectNotificationContent: {
    type: String,
    trim: true
  },
  containmentDate: {
    type: Date
  },
  remediationDate: {
    type: Date
  },
  resolutionDate: {
    type: Date
  },
  resolutionMeasures: {
    type: String,
    trim: true
  },
  affectedSystems: [{
    systemId: {
      type: String,
      required: true,
      trim: true
    },
    systemName: {
      type: String,
      required: true,
      trim: true
    },
    impact: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    }
  }],
  involvedParties: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    role: {
      type: String,
      required: true,
      trim: true
    },
    contactInformation: {
      type: String,
      trim: true
    }
  }],
  evidenceFiles: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      required: true,
      trim: true
    },
    url: {
      type: String,
      required: true,
      trim: true
    },
    uploadedBy: {
      type: String,
      required: true,
      trim: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  notes: [{
    content: {
      type: String,
      required: true,
      trim: true
    },
    createdBy: {
      type: String,
      required: true,
      trim: true
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create a text index for searching
dataBreachSchema.index({
  title: 'text',
  description: 'text',
  potentialImpact: 'text'
});

// Pre-save hook to update the updatedAt field
dataBreachSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for time since detection
dataBreachSchema.virtual('timeSinceDetection').get(function() {
  const now = new Date();
  const detectionDate = new Date(this.detectionDate);
  const diffTime = now - detectionDate;
  const diffHours = Math.ceil(diffTime / (1000 * 60 * 60));
  return diffHours;
});

// Virtual for notification deadline
dataBreachSchema.virtual('notificationDeadline').get(function() {
  if (!this.notificationRequired || this.notificationThreshold === 'not-required') return null;
  
  const detectionDate = new Date(this.detectionDate);
  let deadline = new Date(detectionDate);
  
  if (this.notificationThreshold === '72-hours') {
    deadline.setHours(deadline.getHours() + 72);
  } else {
    // For 'without-undue-delay', we'll use 5 days as a guideline
    deadline.setDate(deadline.getDate() + 5);
  }
  
  return deadline;
});

// Virtual for notification status
dataBreachSchema.virtual('notificationStatus').get(function() {
  if (!this.notificationRequired) return 'not-required';
  if (this.supervisoryAuthorityNotified) return 'completed';
  
  const now = new Date();
  const deadline = this.notificationDeadline;
  
  if (!deadline) return 'unknown';
  
  return now > deadline ? 'overdue' : 'pending';
});

const DataBreach = mongoose.model('DataBreach', dataBreachSchema);

module.exports = DataBreach;
