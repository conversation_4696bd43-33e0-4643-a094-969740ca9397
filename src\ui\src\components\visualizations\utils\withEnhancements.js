import React from 'react';
import { withPerformanceOptimization } from './withPerformanceOptimization';
import { withAccessibility } from './accessibilityUtils';
import withAnalyticsTracking from './withAnalyticsTracking';
import withErrorBoundary from './withErrorBoundary';
import withResponsiveness from './withResponsiveness';
import { withMobileOptimization } from './mobileOptimizationUtils';

/**
 * Higher-Order Component (HOC) that combines all enhancements for visualizations
 * @param {React.Component} WrappedComponent - The component to wrap
 * @param {Object} options - Additional options for the HOC
 * @returns {React.Component} - The wrapped component with all enhancements
 */
const withEnhancements = (WrappedComponent, options = {}) => {
  // Apply enhancements in a specific order
  // 1. Error Boundary (outermost)
  // 2. Analytics Tracking
  // 3. Accessibility
  // 4. Performance Optimization
  // 5. Mobile Optimization
  // 6. Responsiveness (innermost)

  // Start with the innermost enhancement
  let EnhancedComponent = withResponsiveness(WrappedComponent, options);

  // Apply mobile optimization
  EnhancedComponent = withMobileOptimization(EnhancedComponent);

  // Apply performance optimization
  EnhancedComponent = withPerformanceOptimization(EnhancedComponent, options);

  // Apply accessibility
  EnhancedComponent = withAccessibility(EnhancedComponent);

  // Apply analytics tracking
  EnhancedComponent = withAnalyticsTracking(EnhancedComponent, options);

  // Apply error boundary (outermost)
  EnhancedComponent = withErrorBoundary(EnhancedComponent, options);

  // Return the fully enhanced component
  return EnhancedComponent;
};

export default withEnhancements;
