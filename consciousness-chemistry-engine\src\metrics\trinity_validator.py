"""
Trinity Validator Module

This module implements the Trinity Validation framework for evaluating protein
structures based on three key metrics: NERS, NEPI, and NEFC.
"""

from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Union
import numpy as np
from .psi_score import PSIScore
from .fibonacci_bias import FibonacciBiasAnalyzer
from .cftr_validator import CFTRValidator  # Add CFTR validator import

@dataclass
class TrinityScores:
    """Data class to store Trinity validation scores."""
    ners: float  # Neural-Emotional Resonance Score
    nepi: float   # Neural-Emotional Potential Index
    nefc: float   # Neural-Emotional Field Coherence
    overall: float  # Combined score
    passed: bool    # Whether all scores pass thresholds
    
    def to_dict(self) -> Dict[str, Union[float, bool]]:
        """Convert to dictionary for serialization."""
        return {
            'ners': self.ners,
            'nepi': self.nepi,
            'nefc': self.nefc,
            'overall': self.overall,
            'passed': self.passed
        }

class TrinityValidator:
    """Validate protein structures using the Trinity framework."""
    
    # Default thresholds for each metric
    DEFAULT_THRESHOLDS = {
        'ners': 0.7,  # Neural-Emotional Resonance Score
        'nepi': 0.5,  # Neural-Emotional Potential Index
        'nefc': 0.6   # Neural-Emotional Field Coherence
    }
    
    # Weights for combining scores (sum to 1.0)
    SCORE_WEIGHTS = {
        'ners': 0.4,
        'nepi': 0.3,
        'nefc': 0.3
    }
    
    def __init__(self, thresholds: Optional[Dict[str, float]] = None, enable_cftr_validation: bool = False):
        """Initialize the validator with optional custom thresholds.
        
        Args:
            thresholds: Optional dictionary of custom thresholds for NERS, NEPI, and NEFC.
            enable_cftr_validation: If True, enables CFTR-specific validation.
        """
        self.thresholds = {**self.DEFAULT_THRESHOLDS, **(thresholds or {})}
        self.psi_scorer = PSIScore()
        self.fib_analyzer = FibonacciBiasAnalyzer()
        self.enable_cftr_validation = enable_cftr_validation
        self.cftr_validator = CFTRValidator() if enable_cftr_validation else None
    
    def validate(self, structure_data: Dict) -> Dict:
        """Validate a protein structure using the Trinity framework.
        
        Args:
            structure_data: Dictionary containing protein structure information
                          (must contain 'sequence' and 'structure' keys)
        
        Returns:
            Dictionary containing validation results
        """
        # Initialize result dictionary
        result = {
            'scores': {},
            'thresholds': self.thresholds,
            'validation': {},
            'sequence_analysis': {},
            'cftr_validation': None
        }
        
        # Run CFTR validation if enabled and sequence is provided
        if self.enable_cftr_validation and 'sequence' in structure_data:
            cftr_result = self.cftr_validator.validate(structure_data['sequence'])
            result['cftr_validation'] = cftr_result.to_dict()
            
            # If this is a CFTR protein and validation failed, return early with CFTR-specific results
            if cftr_result.is_cftr and not cftr_result.validation_passed:
                result['scores'] = {
                    'ners': 0.0,
                    'nepi': 0.0,
                    'nefc': 0.0,
                    'overall': 0.0,
                    'passed': False,
                    'cftr_validation_failed': True
                }
                result['validation'] = {
                    'ners': {'score': 0.0, 'threshold': self.thresholds['ners'], 'passed': False},
                    'nepi': {'score': 0.0, 'threshold': self.thresholds['nepi'], 'passed': False},
                    'nefc': {'score': 0.0, 'threshold': self.thresholds['nefc'], 'passed': False}
                }
                result['sequence_analysis']['is_cftr'] = True
                result['sequence_analysis']['cftr_mutations'] = [
                    mut_id for mut_id, status in cftr_result.mutation_status.items() 
                    if status.get('present', False)
                ]
                return result
        
        # Calculate individual scores for non-CFTR or passed CFTR validation
        ners = self._calculate_ners(structure_data)
        nepi = self._calculate_nepi(structure_data)
        nefc = self._calculate_nefc(structure_data)
        
        # Calculate overall score (weighted average)
        overall = sum([
            self.SCORE_WEIGHTS['ners'] * ners,
            self.SCORE_WEIGHTS['nepi'] * nepi,
            self.SCORE_WEIGHTS['nefc'] * nefc
        ])
        
        # Check against thresholds
        passed = all([
            ners >= self.thresholds['ners'],
            nepi >= self.thresholds['nepi'],
            nefc >= self.thresholds['nefc']
        ])
        
        # Update result with scores
        result['scores'] = {
            'ners': ners,
            'nepi': nepi,
            'nefc': nefc,
            'overall': overall,
            'passed': passed
        }
        
        # Add validation details
        result['validation'] = {
            'ners': {
                'score': ners,
                'threshold': self.thresholds['ners'],
                'passed': ners >= self.thresholds['ners']
            },
            'nepi': {
                'score': nepi,
                'threshold': self.thresholds['nepi'],
                'passed': nepi >= self.thresholds['nepi']
            },
            'nefc': {
                'score': nefc,
                'threshold': self.thresholds['nefc'],
                'passed': nefc >= self.thresholds['nefc']
            }
        }
        
        # Add sequence analysis if sequence is available
        if 'sequence' in structure_data:
            result['sequence_analysis'].update({
                'length': len(structure_data['sequence']),
                'fibonacci_alignment': self.fib_analyzer.analyze_sequence(structure_data['sequence']),
                'is_cftr': result.get('cftr_validation', {}).get('is_cftr', False)
            })
        
        return result
    
    def _calculate_ners(self, structure_data: Dict) -> float:
        """Calculate Neural-Emotional Resonance Score.
        
        This score measures how well the protein structure resonates with
        consciousness-related patterns and harmonics.
        """
        # Calculate PSI score as a base
        psi_score = self.psi_scorer.calculate(structure_data)
        
        # Analyze Fibonacci patterns
        fib_analysis = self.fib_analyzer.analyze_protein_structure(structure_data)
        fib_score = fib_analysis.get('overall_score', 0.5)  # Default to neutral
        
        # Combine scores (weighted average)
        ners = 0.7 * psi_score + 0.3 * fib_score
        
        return max(0.0, min(1.0, ners))
    
    def _calculate_nepi(self, structure_data: Dict) -> float:
        """Calculate Neural-Emotional Potential Index.
        
        This score measures the protein's potential for functional
        consciousness-related properties.
        """
        # In a real implementation, this would analyze:
        # 1. Functional site conservation
        # 2. Binding site predictions
        # 3. Evolutionary conservation patterns
        
        # For now, use a simplified version based on structure data
        conservation = structure_data.get('conservation_scores', [])
        binding_sites = structure_data.get('binding_sites', [])
        
        # Calculate conservation component
        if conservation:
            cons_score = float(np.mean(conservation))
        else:
            cons_score = 0.5  # Neutral if no data
        
        # Calculate binding site component
        if binding_sites:
            # More binding sites suggest higher functional potential
            site_density = len(binding_sites) / max(1, structure_data.get('sequence_length', 100))
            site_score = min(1.0, site_density * 10)  # Scale to [0,1]
        else:
            site_score = 0.3  # Penalize lack of predicted binding sites
        
        # Combine components
        nepi = 0.6 * cons_score + 0.4 * site_score
        return max(0.0, min(1.0, nepi))
    
    def _calculate_nefc(self, structure_data: Dict) -> float:
        """Calculate Neural-Emotional Field Coherence.
        
        This score measures the coherence of the protein's electromagnetic
        and quantum fields, which are hypothesized to relate to consciousness.
        """
        # In a real implementation, this would analyze:
        # 1. Vibrational modes
        # 2. Electronic structure
        # 3. Quantum coherence properties
        
        # For now, use a simplified version based on structure data
        stability = structure_data.get('stability_metrics', {})
        
        # Calculate stability component
        stability_score = stability.get('ddg', 0.5)  # ΔΔG stability
        
        # Calculate symmetry component (more symmetric = potentially more coherent)
        symmetry = structure_data.get('symmetry', {})
        if symmetry:
            sym_score = symmetry.get('score', 0.5)
        else:
            sym_score = 0.3  # Penalize unknown symmetry
        
        # Combine components
        nefc = 0.7 * stability_score + 0.3 * sym_score
        return max(0.0, min(1.0, nefc))
    
    def batch_validate(self, structures: List[Dict]) -> Dict[str, TrinityScores]:
        """Validate multiple protein structures.
        
        Args:
            structures: List of structure data dictionaries
            
        Returns:
            Dictionary mapping structure IDs to validation results
        """
        results = {}
        for i, structure in enumerate(structures):
            struct_id = structure.get('id', f'structure_{i}')
            results[struct_id] = self.validate(structure)
        return results
    
    def get_validation_report(self, scores: TrinityScores) -> Dict:
        """Generate a detailed validation report.
        
        Args:
            scores: TrinityScores object
            
        Returns:
            Dictionary with detailed validation report
        """
        return {
            'scores': scores.to_dict(),
            'thresholds': self.thresholds,
            'weights': self.SCORE_WEIGHTS,
            'validation': {
                'ners': {
                    'score': scores.ners,
                    'threshold': self.thresholds['ners'],
                    'passed': scores.ners >= self.thresholds['ners'],
                    'description': 'Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance.'
                },
                'nepi': {
                    'score': scores.nepi,
                    'threshold': self.thresholds['nepi'],
                    'passed': scores.nepi >= self.thresholds['nepi'],
                    'description': 'Neural-Emotional Potential Index: Evaluates functional potential and adaptability.'
                },
                'nefc': {
                    'score': scores.nefc,
                    'threshold': self.thresholds['nefc'],
                    'passed': scores.nefc >= self.thresholds['nefc'],
                    'description': 'Neural-Emotional Field Coherence: Assesses field coherence and quantum effects.'
                },
                'overall': {
                    'score': scores.overall,
                    'passed': scores.passed,
                    'description': 'Overall validation status based on all metrics.'
                }
            }
        }
