/**
 * Advanced Visualization Styles
 */

/* Base styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f0f2f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    background-color: #1a237e;
    color: white;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
}

/* Visualization containers */
.visualization-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .visualization-container {
        grid-template-columns: 1fr;
    }
}

.visualization-card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.visualization-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.visualization-header {
    background-color: #3f51b5;
    color: white;
    padding: 10px 15px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.visualization-header .controls {
    display: flex;
    gap: 10px;
}

.visualization-header .control-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.visualization-header .control-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.visualization-content {
    height: 400px;
    position: relative;
}

/* Controls */
.controls {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.control-group {
    margin-bottom: 15px;
}

.control-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.control-row {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.control-row label {
    margin-bottom: 0;
    flex: 1;
    font-weight: normal;
}

input[type="range"] {
    flex: 3;
    height: 5px;
    -webkit-appearance: none;
    appearance: none;
    background: #ddd;
    outline: none;
    border-radius: 5px;
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #3f51b5;
    cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #3f51b5;
    cursor: pointer;
    border: none;
}

.value-display {
    flex: 1;
    text-align: right;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 5px 10px;
    border-radius: 3px;
    min-width: 60px;
}

button {
    background-color: #3f51b5;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
}

button:hover {
    background-color: #303f9f;
}

button:active {
    transform: translateY(1px);
}

.button-group {
    display: flex;
    gap: 10px;
}

/* Status panel */
.status {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.status-row {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
}

.status-label {
    flex: 1;
    font-weight: bold;
    color: #555;
}

.status-value {
    flex: 2;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 5px 10px;
    border-radius: 3px;
}

.quantum-silence-yes {
    color: #4caf50;
    font-weight: bold;
    background-color: rgba(76, 175, 80, 0.1);
}

.quantum-silence-no {
    color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
}

/* Domain-specific colors */
.csde-color {
    color: #0088ff;
}

.csfe-color {
    color: #22cc44;
}

.csme-color {
    color: #8844ff;
}

.fused-color {
    color: #ff7700;
}

/* Tensor visualization specific styles */
.tensor-cell {
    stroke: #333;
    stroke-width: 0.5;
}

.tensor-cell-csde {
    fill: rgba(0, 136, 255, 0.7);
}

.tensor-cell-csfe {
    fill: rgba(34, 204, 68, 0.7);
}

.tensor-cell-csme {
    fill: rgba(136, 68, 255, 0.7);
}

.tensor-cell-fused {
    fill: rgba(255, 119, 0, 0.7);
}

/* Resonance spectrogram specific styles */
.frequency-line {
    stroke-width: 2;
    fill: none;
}

.target-line {
    stroke: #ff0000;
    stroke-width: 1;
    stroke-dasharray: 5, 5;
}

.harmonic-line {
    stroke-width: 1;
    stroke-dasharray: 2, 2;
}

/* Tooltip */
.tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    max-width: 200px;
}

/* Loading indicator */
.loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #3f51b5;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .control-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-row label {
        margin-bottom: 5px;
    }
    
    .value-display {
        text-align: left;
        margin-top: 5px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .status-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .status-label {
        margin-bottom: 5px;
    }
    
    .status-value {
        width: 100%;
    }
}
