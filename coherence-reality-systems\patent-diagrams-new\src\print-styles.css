/* Print-specific styles */
@media print {
  /* Hide navigation and other UI elements */
  nav, header, footer, .instructions {
    display: none !important;
  }
  
  /* Ensure each diagram fits on a page */
  body {
    margin: 0;
    padding: 0;
    background-color: white;
  }
  
  /* Remove unnecessary styling */
  * {
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  /* Ensure SVG elements print correctly */
  svg {
    max-width: 100%;
    height: auto;
  }
  
  /* Ensure proper page breaks */
  .diagram-container {
    page-break-after: always;
    margin: 0;
    padding: 0;
  }
  
  /* Last diagram container should not have a page break after */
  .diagram-container:last-child {
    page-break-after: avoid;
  }
  
  /* Ensure text is black for better printing */
  * {
    color: black !important;
  }
  
  /* Ensure background colors print */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
}
