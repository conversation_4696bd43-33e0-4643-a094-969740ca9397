/**
 * User Service
 *
 * This service handles user management operations.
 */

const fs = require('fs').promises;
const path = require('path');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { ValidationError } = require('../utils/errors');

class UserService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.usersFile = path.join(this.dataDir, 'users.json');
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load users from file
   */
  async loadUsers() {
    try {
      const data = await fs.readFile(this.usersFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return default admin user
        return [
          {
            id: '1',
            username: 'admin',
            password: await bcrypt.hash('admin', 10),
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            role: 'admin',
            permissions: ['*'],
            created: new Date().toISOString(),
            lastLogin: null,
            providers: []
          }
        ];
      }
      console.error('Error loading users:', error);
      throw error;
    }
  }

  /**
   * Save users to file
   */
  async saveUsers(users) {
    try {
      await fs.writeFile(this.usersFile, JSON.stringify(users, null, 2));
    } catch (error) {
      console.error('Error saving users:', error);
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  async findUserById(id) {
    const users = await this.loadUsers();
    return users.find(user => user.id === id);
  }

  /**
   * Find user by username
   */
  async findUserByUsername(username) {
    const users = await this.loadUsers();
    return users.find(user => user.username === username);
  }

  /**
   * Find user by email
   */
  async findUserByEmail(email) {
    const users = await this.loadUsers();
    return users.find(user => user.email === email);
  }

  /**
   * Find user by provider info
   */
  async findUserByProviderInfo(providerId, providerUserId) {
    const users = await this.loadUsers();
    return users.find(user =>
      user.providers &&
      user.providers.some(p => p.providerId === providerId && p.providerUserId === providerUserId)
    );
  }

  /**
   * Create a new user
   */
  async createUser(userData) {
    if (!userData.username) {
      throw new ValidationError('Username is required');
    }

    if (!userData.email) {
      throw new ValidationError('Email is required');
    }

    const users = await this.loadUsers();

    // Check if username already exists
    if (users.some(user => user.username === userData.username)) {
      throw new ValidationError('Username already exists');
    }

    // Check if email already exists
    if (users.some(user => user.email === userData.email)) {
      throw new ValidationError('Email already exists');
    }

    // Create new user
    const newUser = {
      id: uuidv4(),
      username: userData.username,
      email: userData.email,
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      role: userData.role || 'user',
      permissions: userData.permissions || ['read'],
      created: new Date().toISOString(),
      lastLogin: new Date().toISOString(),
      providers: [],
      twoFactorEnabled: false,
      twoFactorSecret: null,
      twoFactorTempSecret: null,
      twoFactorTempSecretCreated: null,
      twoFactorBackupCodes: []
    };

    // Add password if provided
    if (userData.password) {
      newUser.password = await bcrypt.hash(userData.password, 10);
    }

    // Add provider info if provided
    if (userData.providerId && userData.providerUserId) {
      newUser.providers = [
        {
          providerId: userData.providerId,
          providerUserId: userData.providerUserId,
          linked: new Date().toISOString()
        }
      ];
    }

    users.push(newUser);
    await this.saveUsers(users);

    // Return user without password
    const { password, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  /**
   * Update user
   */
  async updateUser(id, userData) {
    const users = await this.loadUsers();
    const index = users.findIndex(user => user.id === id);

    if (index === -1) {
      throw new ValidationError(`User with ID ${id} not found`);
    }

    // Don't allow updating username if it already exists
    if (userData.username && userData.username !== users[index].username) {
      const usernameExists = users.some(user => user.username === userData.username && user.id !== id);
      if (usernameExists) {
        throw new ValidationError('Username already exists');
      }
    }

    // Don't allow updating email if it already exists
    if (userData.email && userData.email !== users[index].email) {
      const emailExists = users.some(user => user.email === userData.email && user.id !== id);
      if (emailExists) {
        throw new ValidationError('Email already exists');
      }
    }

    // Hash password if provided
    if (userData.password) {
      userData.password = await bcrypt.hash(userData.password, 10);
    }

    // Update user
    const updatedUser = {
      ...users[index],
      ...userData,
      updated: new Date().toISOString()
    };

    users[index] = updatedUser;
    await this.saveUsers(users);

    // Return user without password
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  /**
   * Link provider to user
   */
  async linkProviderToUser(userId, providerId, providerUserId) {
    const users = await this.loadUsers();
    const index = users.findIndex(user => user.id === userId);

    if (index === -1) {
      throw new ValidationError(`User with ID ${userId} not found`);
    }

    // Check if provider is already linked to another user
    const providerLinked = users.some(user =>
      user.id !== userId &&
      user.providers &&
      user.providers.some(p => p.providerId === providerId && p.providerUserId === providerUserId)
    );

    if (providerLinked) {
      throw new ValidationError(`Provider ${providerId} is already linked to another user`);
    }

    // Initialize providers array if it doesn't exist
    if (!users[index].providers) {
      users[index].providers = [];
    }

    // Check if provider is already linked to this user
    const alreadyLinked = users[index].providers.some(p =>
      p.providerId === providerId && p.providerUserId === providerUserId
    );

    if (!alreadyLinked) {
      // Add provider to user
      users[index].providers.push({
        providerId,
        providerUserId,
        linked: new Date().toISOString()
      });

      // Update user
      users[index].updated = new Date().toISOString();
      await this.saveUsers(users);
    }

    // Return user without password
    const { password, ...userWithoutPassword } = users[index];
    return userWithoutPassword;
  }

  /**
   * Unlink provider from user
   */
  async unlinkProviderFromUser(userId, providerId) {
    const users = await this.loadUsers();
    const index = users.findIndex(user => user.id === userId);

    if (index === -1) {
      throw new ValidationError(`User with ID ${userId} not found`);
    }

    // Check if user has providers
    if (!users[index].providers || users[index].providers.length === 0) {
      throw new ValidationError(`User with ID ${userId} has no linked providers`);
    }

    // Check if user has password (required for unlinking)
    if (!users[index].password) {
      throw new ValidationError(`Cannot unlink provider: User with ID ${userId} has no password set`);
    }

    // Remove provider from user
    users[index].providers = users[index].providers.filter(p => p.providerId !== providerId);

    // Update user
    users[index].updated = new Date().toISOString();
    await this.saveUsers(users);

    // Return user without password
    const { password, ...userWithoutPassword } = users[index];
    return userWithoutPassword;
  }

  /**
   * Delete user
   */
  async deleteUser(id) {
    const users = await this.loadUsers();
    const filteredUsers = users.filter(user => user.id !== id);

    if (users.length === filteredUsers.length) {
      throw new ValidationError(`User with ID ${id} not found`);
    }

    await this.saveUsers(filteredUsers);

    return { success: true, message: `User with ID ${id} deleted` };
  }
}

module.exports = UserService;
