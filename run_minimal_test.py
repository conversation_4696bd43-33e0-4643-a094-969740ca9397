#!/usr/bin/env python3
"""
Minimal CSDE Test Script

This script runs a simplified version of the CSDE tests without requiring <PERSON><PERSON> or <PERSON><PERSON>.
It demonstrates the core optimizations suggested by <PERSON>.
"""

import numpy as np
import time
import math
from datetime import datetime
import os
import json

# Create results directory
os.makedirs('results', exist_ok=True)

# Simple implementation of the core CSDE components
class MinimalCSDECore:
    def __init__(self):
        # Initialize scaling factors
        self.scaling_factors = {
            "cybersecurity": 3141.59,  # π × 1000
            "financial": 3141.59,      # π × 1000
            "healthcare": 2718.28,     # e × 1000
            "physics": 2997.92,        # c/100 (speed of light)
            "default": 3141.59         # Default to π × 1000
        }

        # Initialize pattern preservation parameters
        self.skip_connection_weight = 0.2
        self.contrastive_loss_weight = 0.1
        self.attention_enabled = True
        self.attention_type = "cross_domain"

        # Initialize hardware parameters
        self.use_gpu = False
        self.parallel_processing = False

    def tensor_product(self, A, B):
        """Simplified tensor product implementation"""
        # Use numpy's outer product as a simple tensor product
        if len(A.shape) == 1 and len(B.shape) == 1:
            return np.outer(A, B)
        else:
            # For multi-dimensional inputs, reshape and use matrix multiplication
            A_flat = A.reshape(A.shape[0], -1)
            B_flat = B.reshape(B.shape[0], -1)
            return np.matmul(A_flat, B_flat.T)

    def calculate_similarity(self, A, B):
        """Calculate cosine similarity between flattened arrays"""
        A_flat = A.flatten()
        B_flat = B.flatten()

        # Ensure compatible shapes
        min_dim = min(A_flat.shape[0], B_flat.shape[0])
        A_flat = A_flat[:min_dim]
        B_flat = B_flat[:min_dim]

        # Calculate cosine similarity
        norm_A = np.linalg.norm(A_flat)
        norm_B = np.linalg.norm(B_flat)

        if norm_A == 0 or norm_B == 0:
            return 0.0

        dot_product = np.dot(A_flat, B_flat)
        similarity = dot_product / (norm_A * norm_B)

        return similarity

    def fusion_operator(self, tensor_result, threat_intelligence, domain="cybersecurity"):
        """
        Implements fusion operator with simplified skip connections
        Based on Orion's emergency patch rollback
        """
        # Step 1: Apply domain-aware encoding
        domain_encoders = {
            "cybersecurity": 0.618,  # Golden ratio for cybersecurity
            "financial": 0.667,      # 2/3 for financial
            "healthcare": 0.6,       # 3/5 for healthcare
            "physics": 0.577,        # 1/√3 for physics
            "default": 0.618         # Default to golden ratio
        }

        # Get domain-specific phi value
        base_phi = domain_encoders.get(domain, domain_encoders["default"])

        # Step 2: Calculate similarity for basic adjustment
        similarity_score = self.calculate_similarity(tensor_result, threat_intelligence)

        # Step 3: Apply simple phi adjustment based on similarity
        if similarity_score < 0.3:
            # Very dissimilar - adjust phi to bring representations closer
            phi = base_phi * 0.8  # Reduce phi for dissimilar inputs
        elif similarity_score > 0.7:
            # Very similar - use base phi
            phi = base_phi
        else:
            # Moderate similarity - linear interpolation
            normalized_sim = (similarity_score - 0.3) / 0.4  # Range: 0-1
            phi = base_phi * (0.8 + 0.2 * normalized_sim)  # Range: 0.8*base_phi to base_phi

        # Reshape threat_intelligence to match tensor_result if needed
        if tensor_result.shape != threat_intelligence.shape:
            # For simplicity, just use the first elements of threat_intelligence
            # In a real implementation, we would use a more sophisticated approach
            threat_reshaped = np.zeros_like(tensor_result)
            min_rows = min(tensor_result.shape[0], threat_intelligence.shape[0])
            min_cols = min(tensor_result.shape[1], threat_intelligence.shape[1])
            threat_reshaped[:min_rows, :min_cols] = threat_intelligence[:min_rows, :min_cols]
            threat_intelligence = threat_reshaped

        # Step 4: Apply basic fusion with phi weighting
        fusion_result = tensor_result * phi + threat_intelligence * (1 - phi)

        # Step 5: Apply Orion's Hybrid Skip-Connection with reduced weight
        # Transformer dominance: 0.85, Skip weight: 0.15
        skip_weight = min(0.15, self.skip_connection_weight)  # Cap at 0.15 as per Orion's final fix
        transformer_dominance = 0.85  # Increased from 0.75

        # Apply hybrid skip connection with transformer dominance
        fusion_result = fusion_result * transformer_dominance + tensor_result * skip_weight

        # Step 6: Apply minimal contrastive adjustment for stability
        if similarity_score < 0.3 and self.contrastive_loss_weight > 0:
            # Only apply to very dissimilar inputs and if enabled
            contrastive_weight = self.contrastive_loss_weight * (0.3 - similarity_score) / 0.3
            fusion_result = fusion_result * (1 - contrastive_weight) + tensor_result * contrastive_weight

        return fusion_result

    def pi_scaling(self, fusion_result, domain="cybersecurity"):
        """
        Implements IQR-based π10³ scaling
        Based on Orion's final fix - using robust statistics instead of coefficient of variation
        """
        # Step 1: Get base scaling factor for the domain
        base_pi_factor = self.scaling_factors.get(domain, self.scaling_factors["default"])

        # Step 2: Calculate robust statistics of the fusion result
        # Using IQR (Interquartile Range) which is less sensitive to outliers
        if fusion_result.size > 0:
            # Calculate percentiles for IQR
            q1 = np.percentile(fusion_result, 25)
            q3 = np.percentile(fusion_result, 75)
            iqr = q3 - q1  # Interquartile Range

            # Calculate median (more robust than mean)
            median = np.median(fusion_result)

            # Avoid division by zero
            if abs(median) < 1e-10:
                median = 1e-10

            # Calculate robust scaling factor using IQR/median ratio
            # This is similar to coefficient of variation but more robust to outliers
            robust_ratio = iqr / abs(median)

            # Step 3: Apply robust adjustment based on IQR/median ratio
            # Higher ratio = lower scaling to prevent explosion
            # Lower ratio = higher scaling to amplify patterns
            variance_threshold = 0.82  # As specified by Orion

            if robust_ratio > variance_threshold:
                # High variability - reduce scaling
                adjustment_factor = 0.5 + 0.5 * (1.0 - min(robust_ratio/variance_threshold, 1.0))
            else:
                # Low variability - increase scaling
                adjustment_factor = 1.0 + 0.5 * (1.0 - robust_ratio / variance_threshold)

            # Step 4: Apply dynamic range limiter with tighter bounds
            # Ensure scaling stays within reasonable bounds
            adjusted_pi_factor = base_pi_factor * adjustment_factor
            adjusted_pi_factor = max(1000, min(4000, adjusted_pi_factor))
        else:
            # Default if fusion_result is empty
            adjusted_pi_factor = base_pi_factor

        return fusion_result * adjusted_pi_factor

    def process(self, compliance_data, infrastructure_data, threat_intelligence, domain="cybersecurity", optimize_speed=True):
        """Implements the full CSDE equation with optimizations"""
        # Apply tensor product
        tensor_result = self.tensor_product(compliance_data, infrastructure_data)

        # Apply fusion operator
        fusion_result = self.fusion_operator(tensor_result, threat_intelligence, domain)

        # Apply π10³ scaling
        final_result = self.pi_scaling(fusion_result, domain)

        # Apply re-injection layer for pattern preservation
        if optimize_speed:
            # Simplified re-injection
            pattern_preservation_weight = 0.05
            final_result = final_result * (1 - pattern_preservation_weight) + tensor_result * pattern_preservation_weight

        return final_result

# Task class for resource allocation testing
class Task:
    def __init__(self, id, is_critical=False):
        self.id = id
        self.is_critical = is_critical
        self.resources = 0
        self.completion = 0

    def calculate_completion(self):
        """Calculate completion percentage based on resources"""
        if self.is_critical:
            # Critical tasks need more resources to be effective
            self.completion = 100 * (1 / (1 + math.exp(-0.2 * (self.resources - 20))))
        else:
            # Standard tasks benefit more from small resource amounts
            self.completion = 100 * (1 / (1 + math.exp(-0.3 * (self.resources - 5))))
        return self.completion

# Minimal benchmark implementation
def run_tests(config=None):
    print("Running Minimal CSDE Tests with Carl's Optimizations")

    # Initialize CSDE core with configuration
    csde = MinimalCSDECore()

    # Apply configuration if provided
    if config:
        # Configure hardware
        hardware_config = config.get("hardware", {})
        use_gpu = hardware_config.get("use_gpu", False)

        # Configure pattern preservation
        pattern_config = config.get("pattern_preservation", {})
        skip_weight = pattern_config.get("skip_connection_weight", 0.2)
        contrastive_weight = pattern_config.get("contrastive_loss_weight", 0.1)

        # Configure pi10 scaling
        scaling_config = config.get("pi10_scaling", {})
        base_multiplier = scaling_config.get("base_multiplier", 3141.59)

        # Update CSDE core with configuration
        csde.skip_connection_weight = skip_weight
        csde.contrastive_loss_weight = contrastive_weight
        csde.scaling_factors = {
            "cybersecurity": base_multiplier,
            "financial": base_multiplier,
            "healthcare": base_multiplier,
            "physics": base_multiplier,
            "default": base_multiplier
        }

    results = {}

    # Test 1: Resource Allocation with Entropy-Based Approach
    print("\n=== Testing Resource Allocation ===")

    # Create tasks (18% critical, 82% standard)
    num_tasks = 100
    critical_count = int(num_tasks * 0.18)
    tasks = []

    for i in range(num_tasks):
        is_critical = i < critical_count
        tasks.append(Task(i, is_critical))

    # Total resources
    total_resources = num_tasks * 10

    # Test 1a: Equal distribution (baseline)
    equal_tasks = tasks.copy()
    for task in equal_tasks:
        task.resources = total_resources / num_tasks
        task.calculate_completion()

    # Calculate equal distribution metrics
    equal_critical_completion = sum(t.completion for t in equal_tasks if t.is_critical) / critical_count
    equal_standard_completion = sum(t.completion for t in equal_tasks if not t.is_critical) / (num_tasks - critical_count)
    equal_overall_completion = sum(t.completion for t in equal_tasks) / num_tasks

    # Test 1b: Optimized distribution with entropy-based approach
    optimized_tasks = tasks.copy()

    # Calculate entropy-based allocation
    critical_tasks = [t for t in optimized_tasks if t.is_critical]
    standard_tasks = [t for t in optimized_tasks if not t.is_critical]

    # Calculate Shannon entropy
    p_critical = len(critical_tasks) / num_tasks
    p_standard = len(standard_tasks) / num_tasks
    entropy = -p_critical * math.log2(p_critical) - p_standard * math.log2(p_standard)
    normalized_entropy = entropy / 1.0

    # Implement Orion's Emergency Patch v2.1
    # UUFT core ratio with reduced dynamic adjustment (Orion's final fix)
    base_critical = 0.18  # UUFT core ratio
    dynamic_adjustment = min(normalized_entropy * 0.05, 0.05)  # 0-5% buffer (reduced from 0.07)
    critical_weight = base_critical + dynamic_adjustment

    # Floor protection for standard tasks
    standard_floor = 0.65  # Absolute minimum as specified by Orion
    standard_weight = 1.0 - critical_weight

    # Ensure standard tasks get at least the floor percentage
    if standard_weight < standard_floor:
        standard_weight = standard_floor
        critical_weight = 1.0 - standard_weight

    # Calculate resources with floor enforcement
    critical_resources = total_resources * critical_weight
    standard_resources = total_resources * standard_weight

    # Additional safety check to ensure we don't exceed total resources
    if critical_resources + standard_resources > total_resources:
        # Adjust to maintain proportions but fit within total
        total_allocated = critical_resources + standard_resources
        scaling_factor = total_resources / total_allocated
        critical_resources *= scaling_factor
        standard_resources *= scaling_factor

    # Distribute resources
    for task in optimized_tasks:
        if task.is_critical:
            task.resources = critical_resources / len(critical_tasks)
        else:
            task.resources = standard_resources / len(standard_tasks)
        task.calculate_completion()

    # Calculate optimized metrics
    optimized_critical_completion = sum(t.completion for t in optimized_tasks if t.is_critical) / critical_count
    optimized_standard_completion = sum(t.completion for t in optimized_tasks if not t.is_critical) / (num_tasks - critical_count)
    optimized_overall_completion = sum(t.completion for t in optimized_tasks) / num_tasks

    # Calculate improvement
    improvement_percentage = ((optimized_overall_completion / equal_overall_completion) - 1) * 100

    print(f"Equal Distribution Results:")
    print(f"  Critical task completion: {equal_critical_completion:.2f}%")
    print(f"  Standard task completion: {equal_standard_completion:.2f}%")
    print(f"  Overall system completion: {equal_overall_completion:.2f}%")

    print(f"\nOptimized Distribution Results:")
    print(f"  Critical task completion: {optimized_critical_completion:.2f}%")
    print(f"  Standard task completion: {optimized_standard_completion:.2f}%")
    print(f"  Overall system completion: {optimized_overall_completion:.2f}%")
    print(f"  Improvement: {improvement_percentage:.2f}%")

    # Save results
    results["resource_allocation"] = {
        "equal_distribution": {
            "critical_task_completion": equal_critical_completion,
            "standard_task_completion": equal_standard_completion,
            "overall_system_completion": equal_overall_completion
        },
        "optimized_distribution": {
            "critical_task_completion": optimized_critical_completion,
            "standard_task_completion": optimized_standard_completion,
            "overall_system_completion": optimized_overall_completion
        },
        "improvement_percentage": improvement_percentage,
        "meets_target": bool(improvement_percentage > 0),
        "timestamp": datetime.now().isoformat()
    }

    # Test 2: Pattern Preservation with Skip Connections
    print("\n=== Testing Pattern Preservation ===")

    # Create synthetic patterns
    pattern_A = np.random.normal(0, 1, (10, 5))
    pattern_B = np.random.normal(0, 1, (10, 5))
    context = np.random.normal(0, 1, (10, 5))

    # Test 2a: Process without skip connections
    # Modify the fusion operator temporarily to disable skip connections
    original_fusion = csde.fusion_operator

    def fusion_without_skip(tensor_result, threat_intelligence, domain="cybersecurity"):
        # Get domain-specific phi value
        domain_encoders = {
            "cybersecurity": 0.618,
            "financial": 0.667,
            "healthcare": 0.6,
            "physics": 0.577,
            "default": 0.618
        }
        base_phi = domain_encoders.get(domain, domain_encoders["default"])

        # Reshape threat_intelligence to match tensor_result if needed
        if tensor_result.shape != threat_intelligence.shape:
            # For simplicity, just use the first elements of threat_intelligence
            # In a real implementation, we would use a more sophisticated approach
            threat_reshaped = np.zeros_like(tensor_result)
            min_rows = min(tensor_result.shape[0], threat_intelligence.shape[0])
            min_cols = min(tensor_result.shape[1], threat_intelligence.shape[1])
            threat_reshaped[:min_rows, :min_cols] = threat_intelligence[:min_rows, :min_cols]
            threat_intelligence = threat_reshaped

        # Simple fusion without skip connections
        return tensor_result * base_phi + threat_intelligence * (1 - base_phi)

    # Replace fusion operator temporarily
    csde.fusion_operator = fusion_without_skip

    # Process without skip connections
    result_without_skip = csde.process(pattern_A, pattern_B, context, "cybersecurity", False)

    # Restore original fusion operator
    csde.fusion_operator = original_fusion

    # Test 2b: Process with skip connections
    result_with_skip = csde.process(pattern_A, pattern_B, context, "cybersecurity", False)

    # Measure pattern preservation
    similarity_A_without = csde.calculate_similarity(pattern_A, result_without_skip)
    similarity_B_without = csde.calculate_similarity(pattern_B, result_without_skip)
    avg_without = (similarity_A_without + similarity_B_without) / 2

    similarity_A_with = csde.calculate_similarity(pattern_A, result_with_skip)
    similarity_B_with = csde.calculate_similarity(pattern_B, result_with_skip)
    avg_with = (similarity_A_with + similarity_B_with) / 2

    # Calculate improvement
    pattern_improvement = ((avg_with / avg_without) - 1) * 100

    print(f"Pattern Preservation without Skip Connections: {avg_without:.4f}")
    print(f"Pattern Preservation with Skip Connections: {avg_with:.4f}")
    print(f"Improvement: {pattern_improvement:.2f}%")

    # Save results
    results["pattern_preservation"] = {
        "without_skip_connections": {
            "pattern_A_preservation": similarity_A_without,
            "pattern_B_preservation": similarity_B_without,
            "average_preservation": avg_without
        },
        "with_skip_connections": {
            "pattern_A_preservation": similarity_A_with,
            "pattern_B_preservation": similarity_B_with,
            "average_preservation": avg_with
        },
        "improvement_percentage": pattern_improvement,
        "meets_target": bool(avg_with >= 0.5),  # Lower threshold for minimal test
        "timestamp": datetime.now().isoformat()
    }

    # Test 3: Parameterized π10³ Scaling
    print("\n=== Testing Parameterized π10³ Scaling ===")

    # Create synthetic data with different characteristics
    low_variance_data = np.random.normal(10, 0.1, (10, 5))  # Mean 10, low std dev
    high_variance_data = np.random.normal(10, 10, (10, 5))  # Mean 10, high std dev

    # Process with fixed scaling
    def fixed_scaling(fusion_result, domain="cybersecurity"):
        return fusion_result * 3141.59  # Fixed π10³

    # Save original scaling function
    original_scaling = csde.pi_scaling

    # Replace with fixed scaling
    csde.pi_scaling = fixed_scaling

    # Process with fixed scaling
    result_fixed_low = csde.process(low_variance_data, pattern_B, context)
    result_fixed_high = csde.process(high_variance_data, pattern_B, context)

    # Restore parameterized scaling
    csde.pi_scaling = original_scaling

    # Process with parameterized scaling
    result_param_low = csde.process(low_variance_data, pattern_B, context)
    result_param_high = csde.process(high_variance_data, pattern_B, context)

    # Calculate output statistics
    fixed_low_std = np.std(result_fixed_low)
    fixed_high_std = np.std(result_fixed_high)
    param_low_std = np.std(result_param_low)
    param_high_std = np.std(result_param_high)

    # Calculate improvement in stability (lower std dev is better for high variance)
    high_variance_improvement = ((fixed_high_std / param_high_std) - 1) * 100

    print(f"Fixed Scaling - Low Variance Output Std Dev: {fixed_low_std:.2f}")
    print(f"Fixed Scaling - High Variance Output Std Dev: {fixed_high_std:.2f}")
    print(f"Parameterized Scaling - Low Variance Output Std Dev: {param_low_std:.2f}")
    print(f"Parameterized Scaling - High Variance Output Std Dev: {param_high_std:.2f}")
    print(f"High Variance Stability Improvement: {high_variance_improvement:.2f}%")

    # Save results
    results["parameterized_scaling"] = {
        "fixed_scaling": {
            "low_variance_std_dev": float(fixed_low_std),
            "high_variance_std_dev": float(fixed_high_std)
        },
        "parameterized_scaling": {
            "low_variance_std_dev": float(param_low_std),
            "high_variance_std_dev": float(param_high_std)
        },
        "high_variance_improvement": float(high_variance_improvement),
        "meets_target": bool(high_variance_improvement > 0),
        "timestamp": datetime.now().isoformat()
    }

    # Generate summary
    tests_passed = sum(1 for result in results.values() if result.get("meets_target", False))
    total_tests = len(results)
    success_rate = (tests_passed / total_tests) * 100

    summary = {
        "timestamp": datetime.now().isoformat(),
        "tests_passed": tests_passed,
        "total_tests": total_tests,
        "success_rate": success_rate,
        "test_results": results
    }

    # Save results
    with open('results/minimal_test_results.json', 'w') as f:
        json.dump(summary, f, indent=2)

    # Print summary
    print("\n=== Test Summary ===")
    print(f"Tests Passed: {tests_passed}/{total_tests} ({success_rate:.1f}%)")

    if success_rate >= 80:
        print("\nCONCLUSION: CSDE OPTIMIZATIONS VALIDATED")
        print("Carl's suggestions have successfully improved the CSDE implementation.")
    else:
        print("\nCONCLUSION: FURTHER REFINEMENT NEEDED")
        print("Some optimizations show promise but require additional tuning.")

    return summary

def main():
    """Main function for running tests"""
    return run_tests()

if __name__ == "__main__":
    main()
