<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>8. Pattern Translation Process</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 950px;
            height: 700px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .dashed {
            border-style: dashed;
        }
    </style>
</head>
<body>
    <h1>8. Pattern Translation Process</h1>
    
    <div class="diagram-container">
        <!-- Universal Pattern Language -->
        <div class="element" style="top: 50px; left: 350px; width: 300px; background-color: #f9f0ff; font-weight: bold; font-size: 20px;">
            Universal Pattern Language
            <div class="element-number">1</div>
        </div>
        
        <!-- Pattern Translation Equation -->
        <div class="element" style="top: 120px; left: 350px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 16px;">
            Pattern Translation = T(Pₐ → Pᵦ) = ∫(Pₐ⊗G)·dM
            <div class="element-number">2</div>
        </div>
        
        <!-- Domains -->
        <div class="element" style="top: 200px; left: 150px; width: 200px; background-color: #e6f7ff;">
            Domain A<br>(e.g., Cybersecurity)
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 200px; left: 650px; width: 200px; background-color: #e6f7ff;">
            Domain B<br>(e.g., Healthcare)
            <div class="element-number">4</div>
        </div>
        
        <!-- Pattern Primitives -->
        <div class="element" style="top: 280px; left: 350px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Pattern Primitives
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 340px; left: 150px; width: 150px; background-color: #f6ffed; font-size: 12px;">
            Oscillators<br>(periodic patterns)
            <div class="element-number">6</div>
        </div>
        
        <div class="element" style="top: 340px; left: 350px; width: 150px; background-color: #f6ffed; font-size: 12px;">
            Attractors<br>(convergent patterns)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 340px; left: 550px; width: 150px; background-color: #f6ffed; font-size: 12px;">
            Bifurcators<br>(divergent patterns)
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 340px; left: 750px; width: 150px; background-color: #f6ffed; font-size: 12px;">
            Resonators<br>(amplifying patterns)
            <div class="element-number">9</div>
        </div>
        
        <!-- Transformation Operators -->
        <div class="element" style="top: 400px; left: 350px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Transformation Operators
            <div class="element-number">10</div>
        </div>
        
        <div class="element" style="top: 460px; left: 150px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Tensor product (⊗)<br>Combines patterns
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 460px; left: 350px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Fusion operator (⊕)<br>Merges patterns
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 460px; left: 550px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Curl operator (∇×)<br>Detects rotational patterns
            <div class="element-number">13</div>
        </div>
        
        <div class="element" style="top: 460px; left: 750px; width: 150px; background-color: #fff2e8; font-size: 12px;">
            Divergence operator (∇·)<br>Detects expansive patterns
            <div class="element-number">14</div>
        </div>
        
        <!-- Grammar Rules -->
        <div class="element" style="top: 520px; left: 350px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Grammar Rules
            <div class="element-number">15</div>
        </div>
        
        <div class="element" style="top: 580px; left: 150px; width: 150px; background-color: #e6f7ff; font-size: 12px;">
            Nesting<br>Patterns within patterns
            <div class="element-number">16</div>
        </div>
        
        <div class="element" style="top: 580px; left: 350px; width: 150px; background-color: #e6f7ff; font-size: 12px;">
            Scaling<br>Patterns across scales
            <div class="element-number">17</div>
        </div>
        
        <div class="element" style="top: 580px; left: 550px; width: 150px; background-color: #e6f7ff; font-size: 12px;">
            Resonance<br>Patterns in harmony
            <div class="element-number">18</div>
        </div>
        
        <div class="element" style="top: 580px; left: 750px; width: 150px; background-color: #e6f7ff; font-size: 12px;">
            Interference<br>Patterns in conflict
            <div class="element-number">19</div>
        </div>
        
        <!-- Implementation -->
        <div class="element" style="top: 650px; left: 350px; width: 300px; background-color: #f9f0ff; font-weight: bold; font-size: 16px;">
            Technical Implementation: Language Processing System
            <div class="element-number">20</div>
        </div>
        
        <!-- Connections -->
        <!-- Universal Pattern Language to Translation Equation -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 20px; background-color: black;"></div>
        <div class="arrow" style="top: 110px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Translation Equation to Domains -->
        <div class="connection" style="top: 170px; left: 400px; width: 150px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 200px; left: 240px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 170px; left: 600px; width: 150px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 200px; left: 740px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Pattern Primitives -->
        <div class="connection" style="top: 170px; left: 500px; width: 2px; height: 110px; background-color: black;"></div>
        <div class="arrow" style="top: 270px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Pattern Primitives to specific primitives -->
        <div class="connection" style="top: 330px; left: 350px; width: 125px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 340px; left: 215px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 330px; left: 425px; width: 2px; height: 10px; background-color: black;"></div>
        <div class="arrow" style="top: 330px; left: 420px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 330px; left: 500px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 340px; left: 615px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 330px; left: 575px; width: 250px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 340px; left: 815px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Transformation Operators -->
        <div class="connection" style="top: 370px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 390px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Transformation Operators to specific operators -->
        <div class="connection" style="top: 450px; left: 350px; width: 125px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 460px; left: 215px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 450px; left: 425px; width: 2px; height: 10px; background-color: black;"></div>
        <div class="arrow" style="top: 450px; left: 420px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 450px; left: 500px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 460px; left: 615px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 450px; left: 575px; width: 250px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 460px; left: 815px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Grammar Rules -->
        <div class="connection" style="top: 490px; left: 500px; width: 2px; height: 30px; background-color: black;"></div>
        <div class="arrow" style="top: 510px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Grammar Rules to specific rules -->
        <div class="connection" style="top: 570px; left: 350px; width: 125px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 580px; left: 215px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 570px; left: 425px; width: 2px; height: 10px; background-color: black;"></div>
        <div class="arrow" style="top: 570px; left: 420px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <div class="connection" style="top: 570px; left: 500px; width: 125px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 580px; left: 615px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <div class="connection" style="top: 570px; left: 575px; width: 250px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 580px; left: 815px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Connect to Implementation -->
        <div class="connection" style="top: 610px; left: 500px; width: 2px; height: 40px; background-color: black;"></div>
        <div class="arrow" style="top: 640px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
        
        <!-- Connect Domains across the diagram -->
        <div class="connection" style="top: 200px; left: 350px; width: 300px; height: 2px; background-color: black; border-style: dashed;"></div>
        <div class="arrow" style="top: 200px; left: 640px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
    </div>
</body>
</html>
