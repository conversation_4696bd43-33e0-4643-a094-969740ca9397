/**
 * NovaVision NovaStore
 * 
 * This module exports all NovaVision components for the NovaStore marketplace.
 */

const ComponentCard = require('./component_card');
const MarketplaceDashboard = require('./marketplace_dashboard');
const VerificationDetails = require('./verification_details');

/**
 * NovaStore UI
 * @param {Object} options - Configuration options
 * @returns {Object} - NovaStore UI components
 */
function createNovaStoreUI(options = {}) {
  return {
    ComponentCard,
    MarketplaceDashboard,
    VerificationDetails,
    
    /**
     * Render the NovaStore UI
     * @param {Object} props - Component props
     * @returns {JSX.Element} - NovaStore UI
     */
    render(props) {
      const { 
        container, 
        components, 
        verificationMetrics, 
        adaptiveRatios,
        marketplaceMetrics 
      } = props;
      
      // Create React element
      const React = require('react');
      const ReactDOM = require('react-dom');
      
      // Render dashboard
      ReactDOM.render(
        React.createElement(MarketplaceDashboard, {
          components,
          verificationMetrics,
          adaptiveRatios,
          marketplaceMetrics
        }),
        container
      );
    }
  };
}

module.exports = {
  ComponentCard,
  MarketplaceDashboard,
  VerificationDetails,
  createNovaStoreUI
};
