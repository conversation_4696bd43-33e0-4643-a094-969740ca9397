# TEE (Time-Energy-Effort) Equation
# File: tee_equation.mmd
# Description: Visual representation of the Comphyological TEE Equation for system coherence measurement
# Created: 2025-07-06

%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph TEE_Equation["Comphyological TEE Equation"]
        OV["Outcome Value (V)"]
        CF["Coherence Factor (Ψᶜ)"]
        T["Time (T)"]
        E["Energy (E)"]
        EF["Effort (F)"]

        Numerator(Numerator: V × Ψᶜ)
        Denominator(Denominator: T × E × F)

        OV & CF --> Numerator
        T & E & EF --> Denominator

        Numerator --> TEE_C["TEE_Coherence = V × Ψᶜ / (T × E × F)"]
        Denominator --> TEE_C
    end

    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef variable fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:ellipse
    classDef operation fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:rectangle
    classDef result fill:#fff,stroke:#000,stroke-width:3px,color:#000,shape:box3d
    
    class OV,CF,T,E,EF variable
    class Numerator,Denominator operation
    class TEE_C result
