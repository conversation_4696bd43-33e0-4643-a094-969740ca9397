/**
 * Performance Optimizer
 *
 * This module provides performance optimization capabilities for the Finite Universe
 * Principle defense system, including caching, batching, and throttling.
 */

const EventEmitter = require('events');
const { CacheManager, createCacheManager } = require('./cache-manager');

/**
 * PerformanceOptimizer class
 * 
 * Provides performance optimization capabilities for the defense system.
 */
class PerformanceOptimizer extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      enableCaching: true,
      enableBatching: true,
      enableThrottling: true,
      batchSize: 10,
      batchInterval: 100, // milliseconds
      throttleLimit: 100, // requests per second
      throttleWindow: 1000, // milliseconds
      ...options
    };

    // Initialize cache manager
    this.cacheManager = options.cacheManager || createCacheManager({
      enableLogging: this.options.enableLogging,
      maxCacheSize: options.maxCacheSize || 1000,
      ttl: options.cacheTTL || 60 * 1000
    });

    // Initialize batching
    this.batch = [];
    this.batchTimer = null;
    this.batchPromises = [];
    this.batchResolvers = [];

    // Initialize throttling
    this.requestTimestamps = [];
    this.throttled = false;

    if (this.options.enableLogging) {
      console.log('PerformanceOptimizer initialized with options:', this.options);
    }
  }

  /**
   * Process data with performance optimizations
   * @param {Function} processFn - Function to process data
   * @param {*} data - Data to process
   * @param {string} domain - Domain of the data
   * @returns {Promise<*>} - Processed data
   */
  async process(processFn, data, domain = '') {
    // Check if caching is enabled
    if (this.options.enableCaching) {
      // Generate cache key
      const cacheKey = this.cacheManager.generateKey(data, domain);
      
      // Check if data is in cache
      const cachedResult = this.cacheManager.get(cacheKey);
      if (cachedResult !== undefined) {
        this.emit('cache-hit', { domain, data });
        return cachedResult;
      }
      
      this.emit('cache-miss', { domain, data });
    }

    // Check if throttling is enabled and if we're throttled
    if (this.options.enableThrottling && this._isThrottled()) {
      this.emit('throttled', { domain, data });
      
      // Wait for throttle to clear
      await this._waitForThrottle();
    }

    // Check if batching is enabled
    if (this.options.enableBatching) {
      return this._processBatch(processFn, data, domain);
    }

    // Process data directly
    const result = await processFn(data, domain);

    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      const cacheKey = this.cacheManager.generateKey(data, domain);
      this.cacheManager.set(cacheKey, result);
    }

    return result;
  }

  /**
   * Process data in batch
   * @param {Function} processFn - Function to process data
   * @param {*} data - Data to process
   * @param {string} domain - Domain of the data
   * @returns {Promise<*>} - Processed data
   * @private
   */
  _processBatch(processFn, data, domain) {
    return new Promise((resolve) => {
      // Add data to batch
      this.batch.push({ data, domain });
      this.batchPromises.push(processFn);
      this.batchResolvers.push(resolve);
      
      // Start batch timer if not already started
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this._executeBatch();
        }, this.options.batchInterval);
      }
      
      // Execute batch immediately if batch size is reached
      if (this.batch.length >= this.options.batchSize) {
        clearTimeout(this.batchTimer);
        this.batchTimer = null;
        this._executeBatch();
      }
    });
  }

  /**
   * Execute batch
   * @private
   */
  async _executeBatch() {
    // Get current batch
    const currentBatch = [...this.batch];
    const currentPromises = [...this.batchPromises];
    const currentResolvers = [...this.batchResolvers];
    
    // Clear batch
    this.batch = [];
    this.batchPromises = [];
    this.batchResolvers = [];
    this.batchTimer = null;
    
    // Process batch
    this.emit('batch-execute', { size: currentBatch.length });
    
    if (this.options.enableLogging) {
      console.log(`Executing batch of ${currentBatch.length} items`);
    }
    
    // Process each item in batch
    for (let i = 0; i < currentBatch.length; i++) {
      const { data, domain } = currentBatch[i];
      const processFn = currentPromises[i];
      const resolve = currentResolvers[i];
      
      try {
        // Process data
        const result = await processFn(data, domain);
        
        // Cache result if caching is enabled
        if (this.options.enableCaching) {
          const cacheKey = this.cacheManager.generateKey(data, domain);
          this.cacheManager.set(cacheKey, result);
        }
        
        // Resolve promise
        resolve(result);
      } catch (error) {
        // Reject promise
        resolve(Promise.reject(error));
      }
    }
  }

  /**
   * Check if requests are throttled
   * @returns {boolean} - True if throttled, false otherwise
   * @private
   */
  _isThrottled() {
    const now = Date.now();
    
    // Remove old timestamps
    this.requestTimestamps = this.requestTimestamps.filter(
      timestamp => now - timestamp < this.options.throttleWindow
    );
    
    // Add current timestamp
    this.requestTimestamps.push(now);
    
    // Check if throttled
    const throttled = this.requestTimestamps.length > this.options.throttleLimit;
    
    if (throttled && !this.throttled) {
      this.throttled = true;
      
      if (this.options.enableLogging) {
        console.log(`Throttling activated: ${this.requestTimestamps.length} requests in ${this.options.throttleWindow}ms`);
      }
    } else if (!throttled && this.throttled) {
      this.throttled = false;
      
      if (this.options.enableLogging) {
        console.log('Throttling deactivated');
      }
    }
    
    return throttled;
  }

  /**
   * Wait for throttle to clear
   * @returns {Promise<void>} - Promise that resolves when throttle is cleared
   * @private
   */
  _waitForThrottle() {
    return new Promise(resolve => {
      const checkThrottle = () => {
        if (!this._isThrottled()) {
          resolve();
        } else {
          setTimeout(checkThrottle, 100);
        }
      };
      
      setTimeout(checkThrottle, 100);
    });
  }

  /**
   * Get cache manager
   * @returns {CacheManager} - Cache manager
   */
  getCacheManager() {
    return this.cacheManager;
  }

  /**
   * Get performance statistics
   * @returns {Object} - Performance statistics
   */
  getStats() {
    return {
      cache: this.cacheManager.getStats(),
      batching: {
        enabled: this.options.enableBatching,
        batchSize: this.options.batchSize,
        currentBatchSize: this.batch.length
      },
      throttling: {
        enabled: this.options.enableThrottling,
        throttleLimit: this.options.throttleLimit,
        throttleWindow: this.options.throttleWindow,
        currentRequestCount: this.requestTimestamps.length,
        isThrottled: this.throttled
      }
    };
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Execute any pending batch
    if (this.batch.length > 0) {
      clearTimeout(this.batchTimer);
      this._executeBatch();
    }
    
    // Dispose cache manager
    this.cacheManager.dispose();
    
    if (this.options.enableLogging) {
      console.log('PerformanceOptimizer disposed');
    }
  }
}

/**
 * Create a performance optimizer with recommended settings
 * @param {Object} options - Configuration options
 * @returns {PerformanceOptimizer} - Configured performance optimizer
 */
function createPerformanceOptimizer(options = {}) {
  return new PerformanceOptimizer({
    enableLogging: true,
    enableCaching: true,
    enableBatching: true,
    enableThrottling: true,
    batchSize: 10,
    batchInterval: 100,
    throttleLimit: 100,
    throttleWindow: 1000,
    ...options
  });
}

module.exports = {
  PerformanceOptimizer,
  createPerformanceOptimizer
};
