import React, { useState, useEffect, useRef } from 'react';

/**
 * Universal Ripple Stack Dashboard
 * 
 * This component visualizes the operational metrics and system health
 * of the NovaFuse Universal Ripple Stack.
 */
const UniversalRippleDashboard = ({ width = '100%', height = '600px' }) => {
  // State for dashboard data
  const [systemHealth, setSystemHealth] = useState(0.92);
  const [trinityBalance, setTrinityBalance] = useState(0.88);
  const [rippleStrength, setRippleStrength] = useState(0.76);
  const [activeConnections, setActiveConnections] = useState(42);
  const [predictionConfidence, setPredictionConfidence] = useState(0.85);
  const [selectedComponent, setSelectedComponent] = useState('all');
  const [showDetails, setShowDetails] = useState(false);
  const [timeRange, setTimeRange] = useState('1h');
  
  // Canvas reference for visualization
  const canvasRef = useRef(null);
  
  // Component data
  const components = {
    novaConnect: {
      name: 'NovaConnect',
      description: 'Integration and Interoperability',
      health: 0.95,
      color: '#2196F3',
      metrics: {
        activeConnections: 42,
        dataFlowRate: 1842,
        latency: 0.018,
        errorRate: 0.003
      }
    },
    novaThink: {
      name: 'NovaThink',
      description: 'AI-driven Decision Making',
      health: 0.91,
      color: '#9C27B0',
      metrics: {
        decisionsPerSecond: 182,
        accuracyRate: 0.982,
        confidenceAverage: 0.85,
        ethicalTensorValue: 0.918
      }
    },
    novaPulse: {
      name: 'NovaPulse+',
      description: 'Real-time Compliance Monitoring',
      health: 0.89,
      color: '#4CAF50',
      metrics: {
        complianceRate: 0.982,
        controlsCovered: 1842,
        alertsGenerated: 18,
        remediationTime: 82
      }
    },
    novaFlow: {
      name: 'NovaFlowX',
      description: 'Workflow Automation',
      health: 0.93,
      color: '#FF9800',
      metrics: {
        workflowsActive: 82,
        automationRate: 0.918,
        completionTime: 18,
        userSatisfaction: 0.95
      }
    }
  };
  
  // Ripple effect layers
  const rippleLayers = {
    directImpact: {
      name: 'Direct Impact',
      strength: 0.92,
      color: '#F44336'
    },
    adjacentResonance: {
      name: 'Adjacent Resonance',
      strength: 0.76,
      color: '#FF9800'
    },
    fieldSaturation: {
      name: 'Field Saturation',
      strength: 0.61,
      color: '#FFEB3B'
    }
  };
  
  // Trinity components
  const trinityComponents = {
    governance: {
      name: 'Governance',
      balance: 0.91,
      color: '#4CAF50'
    },
    detection: {
      name: 'Detection',
      balance: 0.88,
      color: '#2196F3'
    },
    response: {
      name: 'Response',
      balance: 0.85,
      color: '#9C27B0'
    }
  };
  
  // Time series data (simulated)
  const generateTimeSeriesData = (baseValue, volatility, timeRange) => {
    const points = timeRange === '1h' ? 60 : timeRange === '24h' ? 144 : 168;
    const data = [];
    
    for (let i = 0; i < points; i++) {
      const time = Date.now() - (points - i) * (timeRange === '1h' ? 60000 : timeRange === '24h' ? 600000 : 3600000);
      const value = Math.max(0, Math.min(1, baseValue + (Math.random() - 0.5) * volatility));
      data.push({ time, value });
    }
    
    return data;
  };
  
  // Generate time series data for metrics
  const timeSeriesData = {
    systemHealth: generateTimeSeriesData(0.92, 0.1, timeRange),
    trinityBalance: generateTimeSeriesData(0.88, 0.15, timeRange),
    rippleStrength: generateTimeSeriesData(0.76, 0.2, timeRange),
    predictionConfidence: generateTimeSeriesData(0.85, 0.18, timeRange)
  };
  
  // Simulate real-time updates
  useEffect(() => {
    const updateInterval = setInterval(() => {
      setSystemHealth(prevHealth => Math.max(0, Math.min(1, prevHealth + (Math.random() - 0.5) * 0.05)));
      setTrinityBalance(prevBalance => Math.max(0, Math.min(1, prevBalance + (Math.random() - 0.5) * 0.03)));
      setRippleStrength(prevStrength => Math.max(0, Math.min(1, prevStrength + (Math.random() - 0.5) * 0.08)));
      setActiveConnections(prevConnections => Math.max(0, prevConnections + Math.floor((Math.random() - 0.3) * 5)));
      setPredictionConfidence(prevConfidence => Math.max(0, Math.min(1, prevConfidence + (Math.random() - 0.5) * 0.04)));
    }, 3000);
    
    return () => clearInterval(updateInterval);
  }, []);
  
  // Initialize and animate the canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const container = canvas.parentElement;
    
    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Animation variables
    let animationFrame;
    let time = 0;
    
    // Animation function
    const animate = () => {
      time += 0.01;
      
      // Clear canvas
      ctx.fillStyle = '#111133';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Draw ripple visualization
      drawRippleVisualization(ctx, canvas.width, canvas.height, time);
      
      // Continue animation
      animationFrame = requestAnimationFrame(animate);
    };
    
    animate();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrame);
    };
  }, [selectedComponent, timeRange]);
  
  // Draw the ripple visualization
  const drawRippleVisualization = (ctx, width, height, time) => {
    const centerX = width / 2;
    const centerY = height / 2;
    
    // Draw ripple effect
    drawRippleEffect(ctx, centerX, centerY, time);
    
    // Draw component nodes
    if (selectedComponent === 'all' || selectedComponent === 'components') {
      drawComponentNodes(ctx, centerX, centerY, time);
    }
    
    // Draw trinity balance
    if (selectedComponent === 'all' || selectedComponent === 'trinity') {
      drawTrinityBalance(ctx, centerX, centerY, time);
    }
    
    // Draw metrics
    drawMetrics(ctx, width, height);
  };
  
  // Draw ripple effect
  const drawRippleEffect = (ctx, centerX, centerY, time) => {
    const maxRadius = Math.min(centerX, centerY) * 0.8;
    
    // Draw ripple layers
    Object.entries(rippleLayers).forEach(([key, layer], index) => {
      const baseRadius = maxRadius * (0.4 + index * 0.2);
      const waveAmplitude = 10 * layer.strength;
      const waveFrequency = 10 - index * 2;
      const wavePhase = time * (3 - index * 0.5);
      
      // Draw ripple circle
      ctx.beginPath();
      
      for (let angle = 0; angle < Math.PI * 2; angle += 0.05) {
        const radius = baseRadius + Math.sin(angle * waveFrequency + wavePhase) * waveAmplitude;
        const x = centerX + Math.cos(angle) * radius;
        const y = centerY + Math.sin(angle) * radius;
        
        if (angle === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      
      ctx.closePath();
      
      // Create gradient
      const gradient = ctx.createRadialGradient(
        centerX, centerY, baseRadius - waveAmplitude,
        centerX, centerY, baseRadius + waveAmplitude
      );
      
      gradient.addColorStop(0, `${layer.color}00`);
      gradient.addColorStop(0.5, `${layer.color}40`);
      gradient.addColorStop(1, `${layer.color}00`);
      
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Draw layer label
      const labelAngle = Math.PI / 4;
      const labelRadius = baseRadius;
      const labelX = centerX + Math.cos(labelAngle) * labelRadius;
      const labelY = centerY + Math.sin(labelAngle) * labelRadius;
      
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = layer.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(layer.name, labelX, labelY);
      
      // Draw strength indicator
      const strengthX = labelX + 70;
      const strengthY = labelY;
      
      ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.fillRect(strengthX - 25, strengthY - 5, 50, 10);
      
      ctx.fillStyle = layer.color;
      ctx.fillRect(strengthX - 25, strengthY - 5, 50 * layer.strength, 10);
    });
  };
  
  // Draw component nodes
  const drawComponentNodes = (ctx, centerX, centerY, time) => {
    const radius = Math.min(centerX, centerY) * 0.6;
    const componentEntries = Object.entries(components);
    
    componentEntries.forEach(([key, component], index) => {
      const angle = (index / componentEntries.length) * Math.PI * 2;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      // Draw connection to center
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.strokeStyle = `${component.color}80`;
      ctx.lineWidth = 2 + 3 * component.health;
      ctx.stroke();
      
      // Draw pulse along connection
      const pulseCount = 3;
      for (let i = 0; i < pulseCount; i++) {
        const pulseProgress = (time * 0.5 + i / pulseCount) % 1;
        const pulseX = centerX + Math.cos(angle) * radius * pulseProgress;
        const pulseY = centerY + Math.sin(angle) * radius * pulseProgress;
        
        ctx.beginPath();
        ctx.arc(pulseX, pulseY, 3, 0, Math.PI * 2);
        ctx.fillStyle = component.color;
        ctx.fill();
      }
      
      // Draw component node
      const nodeSize = 15 + 10 * component.health;
      ctx.beginPath();
      ctx.arc(x, y, nodeSize, 0, Math.PI * 2);
      
      // Create gradient
      const gradient = ctx.createRadialGradient(
        x, y, 0,
        x, y, nodeSize
      );
      
      gradient.addColorStop(0, component.color);
      gradient.addColorStop(1, `${component.color}80`);
      
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Draw component label
      ctx.font = 'bold 14px Arial';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // Position label based on angle
      const labelDistance = nodeSize + 15;
      const labelX = x + Math.cos(angle) * labelDistance;
      const labelY = y + Math.sin(angle) * labelDistance;
      
      ctx.fillText(component.name, labelX, labelY);
      
      // Draw health indicator
      const healthY = labelY + 15;
      
      ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.fillRect(labelX - 25, healthY, 50, 5);
      
      ctx.fillStyle = component.color;
      ctx.fillRect(labelX - 25, healthY, 50 * component.health, 5);
    });
  };
  
  // Draw trinity balance
  const drawTrinityBalance = (ctx, centerX, centerY, time) => {
    const radius = Math.min(centerX, centerY) * 0.3;
    const trinityEntries = Object.entries(trinityComponents);
    
    // Draw trinity circle
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Draw trinity components
    trinityEntries.forEach(([key, component], index) => {
      const angle = (index / trinityEntries.length) * Math.PI * 2;
      const innerRadius = radius * 0.6;
      const outerRadius = radius * (0.8 + 0.2 * component.balance);
      
      // Draw component segment
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(
        centerX, 
        centerY, 
        outerRadius,
        angle - Math.PI / trinityEntries.length + 0.1,
        angle + Math.PI / trinityEntries.length - 0.1
      );
      ctx.lineTo(centerX, centerY);
      
      // Create gradient
      const gradient = ctx.createRadialGradient(
        centerX, centerY, innerRadius,
        centerX, centerY, outerRadius
      );
      
      gradient.addColorStop(0, `${component.color}40`);
      gradient.addColorStop(1, `${component.color}80`);
      
      ctx.fillStyle = gradient;
      ctx.fill();
      
      // Draw component label
      const labelRadius = radius * 1.2;
      const labelX = centerX + Math.cos(angle) * labelRadius;
      const labelY = centerY + Math.sin(angle) * labelRadius;
      
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = component.color;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(component.name, labelX, labelY);
      
      // Draw balance value
      const valueRadius = radius * 0.7;
      const valueX = centerX + Math.cos(angle) * valueRadius;
      const valueY = centerY + Math.sin(angle) * valueRadius;
      
      ctx.font = '10px Arial';
      ctx.fillStyle = 'white';
      ctx.fillText((component.balance * 100).toFixed(0) + '%', valueX, valueY);
    });
    
    // Draw trinity balance in center
    ctx.font = 'bold 16px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText((trinityBalance * 100).toFixed(0) + '%', centerX, centerY);
    
    ctx.font = '10px Arial';
    ctx.fillText('Trinity Balance', centerX, centerY + 15);
  };
  
  // Draw metrics
  const drawMetrics = (ctx, width, height) => {
    const metrics = [
      { name: 'System Health', value: systemHealth, color: '#4CAF50' },
      { name: 'Trinity Balance', value: trinityBalance, color: '#2196F3' },
      { name: 'Ripple Strength', value: rippleStrength, color: '#FF9800' },
      { name: 'Prediction Confidence', value: predictionConfidence, color: '#9C27B0' }
    ];
    
    const metricWidth = 150;
    const metricHeight = 40;
    const metricSpacing = 10;
    const startX = 20;
    const startY = 20;
    
    metrics.forEach((metric, index) => {
      const x = startX;
      const y = startY + index * (metricHeight + metricSpacing);
      
      // Draw metric background
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.fillRect(x, y, metricWidth, metricHeight);
      
      // Draw metric value
      ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.fillRect(x + 5, y + metricHeight - 15, metricWidth - 10, 10);
      
      ctx.fillStyle = metric.color;
      ctx.fillRect(x + 5, y + metricHeight - 15, (metricWidth - 10) * metric.value, 10);
      
      // Draw metric label
      ctx.font = 'bold 12px Arial';
      ctx.fillStyle = 'white';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';
      ctx.fillText(metric.name, x + 5, y + 5);
      
      // Draw metric value
      ctx.font = '12px Arial';
      ctx.textAlign = 'right';
      ctx.fillText((metric.value * 100).toFixed(0) + '%', x + metricWidth - 5, y + 5);
    });
    
    // Draw active connections
    const connectionsX = startX;
    const connectionsY = startY + metrics.length * (metricHeight + metricSpacing);
    
    ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
    ctx.fillRect(connectionsX, connectionsY, metricWidth, metricHeight);
    
    ctx.font = 'bold 12px Arial';
    ctx.fillStyle = 'white';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'top';
    ctx.fillText('Active Connections', connectionsX + 5, connectionsY + 5);
    
    ctx.font = '16px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(activeConnections.toString(), connectionsX + metricWidth - 5, connectionsY + 15);
  };
  
  // Format percentage
  const formatPercentage = (value) => {
    return (value * 100).toFixed(0) + '%';
  };
  
  return (
    <div className="universal-ripple-dashboard" style={{ width, height, position: 'relative' }}>
      <div className="dashboard-visualization" style={{ 
        width: '100%', 
        height: '100%', 
        backgroundColor: '#111133',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <canvas ref={canvasRef} style={{ width: '100%', height: '100%' }} />
      </div>
      
      <div className="dashboard-controls" style={{ 
        position: 'absolute',
        bottom: 20,
        right: 20,
        display: 'flex',
        gap: '10px'
      }}>
        <div className="component-selector" style={{
          display: 'flex',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          borderRadius: '4px',
          overflow: 'hidden'
        }}>
          {['all', 'components', 'trinity', 'ripple'].map(component => (
            <button
              key={component}
              onClick={() => setSelectedComponent(component)}
              style={{
                backgroundColor: selectedComponent === component ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                cursor: 'pointer',
                fontSize: '12px',
                textTransform: 'capitalize'
              }}
            >
              {component}
            </button>
          ))}
        </div>
        
        <div className="time-selector" style={{
          display: 'flex',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          borderRadius: '4px',
          overflow: 'hidden'
        }}>
          {['1h', '24h', '7d'].map(range => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              style={{
                backgroundColor: timeRange === range ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                color: 'white',
                border: 'none',
                padding: '8px 12px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {range}
            </button>
          ))}
        </div>
        
        <button
          onClick={() => setShowDetails(!showDetails)}
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            color: 'white',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </button>
      </div>
      
      {showDetails && (
        <div className="dashboard-details" style={{
          position: 'absolute',
          top: 20,
          right: 20,
          width: '300px',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderRadius: '8px',
          padding: '15px',
          color: 'white'
        }}>
          <h3 style={{ margin: '0 0 10px 0', fontSize: '16px' }}>Universal Ripple Stack</h3>
          
          <div style={{ marginBottom: '15px' }}>
            <h4 style={{ margin: '0 0 5px 0', fontSize: '14px', color: '#4CAF50' }}>System Health</h4>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Overall Health:</span>
              <span>{formatPercentage(systemHealth)}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Trinity Balance:</span>
              <span>{formatPercentage(trinityBalance)}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Ripple Strength:</span>
              <span>{formatPercentage(rippleStrength)}</span>
            </div>
          </div>
          
          <div style={{ marginBottom: '15px' }}>
            <h4 style={{ margin: '0 0 5px 0', fontSize: '14px', color: '#2196F3' }}>Component Status</h4>
            {Object.entries(components).map(([key, component]) => (
              <div key={key} style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', marginBottom: '3px' }}>
                <span>{component.name}:</span>
                <span style={{ color: component.color }}>{formatPercentage(component.health)}</span>
              </div>
            ))}
          </div>
          
          <div>
            <h4 style={{ margin: '0 0 5px 0', fontSize: '14px', color: '#FF9800' }}>System Metrics</h4>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Active Connections:</span>
              <span>{activeConnections}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Prediction Confidence:</span>
              <span>{formatPercentage(predictionConfidence)}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Response Time:</span>
              <span>18ms</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px' }}>
              <span>Compliance Rate:</span>
              <span>98.2%</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UniversalRippleDashboard;
