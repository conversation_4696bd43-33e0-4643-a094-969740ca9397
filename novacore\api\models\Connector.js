/**
 * NovaCore Connector Model
 * 
 * This model defines the schema for API connectors in the NovaCore API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define parameter schema
const parameterSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    required: true, 
    enum: ['string', 'number', 'boolean', 'object', 'array'], 
    default: 'string' 
  },
  required: { 
    type: Boolean, 
    default: false 
  },
  default: { 
    type: Schema.Types.Mixed 
  },
  enum: [{ 
    type: Schema.Types.Mixed 
  }],
  format: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define authentication field schema
const authFieldSchema = new Schema({
  type: { 
    type: String, 
    required: true, 
    enum: ['string', 'number', 'boolean', 'object', 'array'], 
    default: 'string' 
  },
  description: { 
    type: String, 
    trim: true 
  },
  required: { 
    type: Boolean, 
    default: false 
  },
  sensitive: { 
    type: Boolean, 
    default: false 
  },
  default: { 
    type: Schema.Types.Mixed 
  }
}, { _id: false });

// Define authentication schema
const authenticationSchema = new Schema({
  type: { 
    type: String, 
    required: true, 
    enum: ['API_KEY', 'BASIC', 'OAUTH2', 'CUSTOM', 'NONE'], 
    default: 'API_KEY' 
  },
  fields: { 
    type: Map, 
    of: authFieldSchema 
  },
  testConnection: {
    endpoint: { 
      type: String, 
      trim: true 
    },
    method: { 
      type: String, 
      enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'], 
      default: 'GET' 
    },
    expectedResponse: {
      status: { 
        type: Number 
      }
    }
  },
  oauth2Config: {
    authorizationUrl: { 
      type: String, 
      trim: true 
    },
    tokenUrl: { 
      type: String, 
      trim: true 
    },
    scopes: [{ 
      type: String, 
      trim: true 
    }],
    grantType: { 
      type: String, 
      trim: true 
    }
  }
}, { _id: false });

// Define endpoint schema
const endpointSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  path: { 
    type: String, 
    required: true, 
    trim: true 
  },
  method: { 
    type: String, 
    required: true, 
    enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'], 
    default: 'GET' 
  },
  parameters: [parameterSchema],
  headers: { 
    type: Map, 
    of: String 
  },
  body: { 
    type: Schema.Types.Mixed 
  },
  responseSchema: { 
    type: Schema.Types.Mixed 
  },
  pagination: {
    type: { 
      type: String, 
      enum: ['offset', 'cursor', 'page', 'none'], 
      default: 'none' 
    },
    limitParam: { 
      type: String, 
      trim: true 
    },
    offsetParam: { 
      type: String, 
      trim: true 
    },
    pageParam: { 
      type: String, 
      trim: true 
    },
    cursorParam: { 
      type: String, 
      trim: true 
    },
    resultsPath: { 
      type: String, 
      trim: true 
    },
    nextCursorPath: { 
      type: String, 
      trim: true 
    }
  }
}, { _id: false });

// Define transformation schema
const transformationSchema = new Schema({
  source: { 
    type: String, 
    required: true, 
    trim: true 
  },
  target: { 
    type: String, 
    required: true, 
    trim: true 
  },
  transform: { 
    type: String, 
    required: true, 
    trim: true 
  },
  parameters: { 
    type: Schema.Types.Mixed 
  }
}, { _id: false });

// Define mapping schema
const mappingSchema = new Schema({
  sourceEndpoint: { 
    type: String, 
    required: true, 
    trim: true 
  },
  targetSystem: { 
    type: String, 
    required: true, 
    trim: true 
  },
  targetEntity: { 
    type: String, 
    required: true, 
    trim: true 
  },
  transformations: [transformationSchema]
}, { _id: false });

// Define polling event schema
const pollingEventSchema = new Schema({
  endpoint: { 
    type: String, 
    required: true, 
    trim: true 
  },
  interval: { 
    type: String, 
    required: true, 
    trim: true 
  },
  condition: { 
    type: String, 
    trim: true 
  }
}, { _id: false });

// Define events schema
const eventsSchema = new Schema({
  polling: [pollingEventSchema]
}, { _id: false });

// Define connector schema
const connectorSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  version: { 
    type: String, 
    required: true, 
    trim: true 
  },
  category: { 
    type: String, 
    required: true, 
    trim: true 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  baseUrl: { 
    type: String, 
    required: true, 
    trim: true 
  },
  authentication: { 
    type: authenticationSchema, 
    default: () => ({}) 
  },
  endpoints: [endpointSchema],
  mappings: [mappingSchema],
  events: { 
    type: eventsSchema, 
    default: () => ({}) 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'deprecated'], 
    default: 'active' 
  },
  isPublic: { 
    type: Boolean, 
    default: false 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
connectorSchema.index({ name: 1, version: 1 }, { unique: true });
connectorSchema.index({ category: 1 });
connectorSchema.index({ tags: 1 });
connectorSchema.index({ status: 1 });
connectorSchema.index({ isPublic: 1 });
connectorSchema.index({ createdAt: 1 });

// Add methods
connectorSchema.methods.getEndpoint = function(endpointId) {
  return this.endpoints.find(endpoint => endpoint.id === endpointId);
};

// Add statics
connectorSchema.statics.findByCategory = function(category) {
  return this.find({ category });
};

connectorSchema.statics.findByTags = function(tags) {
  return this.find({ tags: { $all: tags } });
};

connectorSchema.statics.findActive = function() {
  return this.find({ status: 'active' });
};

// Create model
const Connector = mongoose.model('Connector', connectorSchema);

module.exports = Connector;
