299a9ea53b87fe2e24575a5f53dc2cb8
/**
 * Custom error classes
 */

class ValidationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ValidationError';
    this.statusCode = 400;
  }
}
class AuthenticationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthenticationError';
    this.statusCode = 401;
  }
}
class AuthorizationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthorizationError';
    this.statusCode = 403;
  }
}
class NotFoundError extends Error {
  constructor(message) {
    super(message);
    this.name = 'NotFoundError';
    this.statusCode = 404;
  }
}
class ConflictError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ConflictError';
    this.statusCode = 409;
  }
}
class RateLimitError extends Error {
  constructor(message, retryAfter) {
    super(message);
    this.name = 'RateLimitError';
    this.statusCode = 429;
    this.retryAfter = retryAfter;
  }
}
class BruteForceError extends Error {
  constructor(message, retryAfter) {
    super(message);
    this.name = 'BruteForceError';
    this.statusCode = 429;
    this.retryAfter = retryAfter;
  }
}
class ServerError extends Error {
  constructor(message) {
    super(message);
    this.name = 'ServerError';
    this.statusCode = 500;
  }
}
module.exports = {
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  BruteForceError,
  ServerError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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