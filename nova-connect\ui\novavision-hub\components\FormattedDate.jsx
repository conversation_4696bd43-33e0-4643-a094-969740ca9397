/**
 * FormattedDate Component
 * 
 * A component for formatting dates according to the current locale.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n/I18nContext';

/**
 * FormattedDate component
 * 
 * @param {Object} props - Component props
 * @param {Date|string|number} props.value - Date value
 * @param {Object} [props.options] - Date format options
 * @param {string} [props.component='span'] - Component to render
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} FormattedDate component
 */
const FormattedDate = ({
  value,
  options,
  component: Component = 'span',
  className = '',
  style = {},
  ...rest
}) => {
  const { formatDate } = useI18n();
  
  // Format date
  const formattedDate = formatDate(value, options);
  
  return (
    <Component
      className={className}
      style={style}
      {...rest}
    >
      {formattedDate}
    </Component>
  );
};

FormattedDate.propTypes = {
  value: PropTypes.oneOfType([
    PropTypes.instanceOf(Date),
    PropTypes.string,
    PropTypes.number
  ]).isRequired,
  options: PropTypes.object,
  component: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object
};

export default FormattedDate;
