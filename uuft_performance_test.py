#!/usr/bin/env python3
"""
UUFT Equation Performance Test

This script tests the performance of the UUFT equation in predicting outcomes
across different domains, comparing it to traditional domain-specific approaches.
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import json
from datetime import datetime
from sklearn.metrics import mean_squared_error, r2_score
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor

# Create results directory
RESULTS_DIR = "uuft_test_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Constants
PI = np.pi
GOLDEN_RATIO = (1 + np.sqrt(5)) / 2  # φ ≈ 1.618
PI_10_CUBED = PI * 10**3

def generate_test_data():
    """
    Generate test data for different domains.
    
    In a real test, this would use actual domain data.
    For this simple test, we'll generate synthetic data with known relationships.
    
    Returns:
        dict: Test data for different domains
    """
    print("Generating test data for different domains")
    
    np.random.seed(42)  # For reproducibility
    
    # Cybersecurity domain
    n_samples = 100
    
    # Cybersecurity: NIST compliance, GCP integration, and Cyber-Safety measures
    # predict security incidents
    nist_compliance = np.random.uniform(0, 10, n_samples)  # 0-10 scale
    gcp_integration = np.random.uniform(0, 10, n_samples)  # 0-10 scale
    cyber_safety = np.random.uniform(0, 50, n_samples)     # 0-50 scale
    
    # True relationship with some noise
    security_incidents = 100 - ((nist_compliance * gcp_integration * GOLDEN_RATIO + 
                               cyber_safety * (1/GOLDEN_RATIO)) * PI_10_CUBED / 10000) + np.random.normal(0, 5, n_samples)
    
    cybersecurity_data = {
        "inputs": {
            "A": nist_compliance,
            "B": gcp_integration,
            "C": cyber_safety
        },
        "output": security_incidents,
        "description": "Security incidents prediction"
    }
    
    # Medical domain: genomic, proteomic, and clinical data predict treatment efficacy
    genomic_data = np.random.uniform(0, 1, n_samples)
    proteomic_data = np.random.uniform(0, 1, n_samples)
    clinical_data = np.random.uniform(0, 1, n_samples)
    
    # True relationship with some noise
    treatment_efficacy = (genomic_data * proteomic_data * GOLDEN_RATIO + 
                         clinical_data * (1/GOLDEN_RATIO)) * PI_10_CUBED / 10000 + np.random.normal(0, 0.05, n_samples)
    
    medical_data = {
        "inputs": {
            "A": genomic_data,
            "B": proteomic_data,
            "C": clinical_data
        },
        "output": treatment_efficacy,
        "description": "Treatment efficacy prediction"
    }
    
    # Financial domain: market, economic, and sentiment data predict returns
    market_data = np.random.uniform(-1, 1, n_samples)
    economic_data = np.random.uniform(-1, 1, n_samples)
    sentiment_data = np.random.uniform(-1, 1, n_samples)
    
    # True relationship with some noise
    returns = (market_data * economic_data * GOLDEN_RATIO + 
              sentiment_data * (1/GOLDEN_RATIO)) * PI_10_CUBED / 1000 + np.random.normal(0, 1, n_samples)
    
    financial_data = {
        "inputs": {
            "A": market_data,
            "B": economic_data,
            "C": sentiment_data
        },
        "output": returns,
        "description": "Financial returns prediction"
    }
    
    # Physics domain: strong, electromagnetic, and weak forces predict gravitational effects
    strong_force = np.random.uniform(0, 10, n_samples)
    em_force = np.random.uniform(0, 10, n_samples)
    weak_force = np.random.uniform(0, 10, n_samples)
    
    # True relationship with some noise
    gravity = (strong_force * em_force * GOLDEN_RATIO + 
              weak_force * (1/GOLDEN_RATIO)) * PI_10_CUBED / 5000 + np.random.normal(0, 0.01, n_samples)
    
    physics_data = {
        "inputs": {
            "A": strong_force,
            "B": em_force,
            "C": weak_force
        },
        "output": gravity,
        "description": "Gravitational force prediction"
    }
    
    return {
        "cybersecurity": cybersecurity_data,
        "medical": medical_data,
        "financial": financial_data,
        "physics": physics_data
    }

def apply_uuft_equation(A, B, C):
    """
    Apply the UUFT equation: (A ⊗ B ⊕ C) × π10³
    
    Args:
        A: First input component
        B: Second input component
        C: Third input component
        
    Returns:
        float: Result of applying the UUFT equation
    """
    # Tensor product (⊗) with golden ratio
    tensor_product = A * B * GOLDEN_RATIO
    
    # Fusion operator (⊕) with inverse golden ratio
    fusion_result = tensor_product + (C * (1 / GOLDEN_RATIO))
    
    # Apply pi factor
    result = fusion_result * PI_10_CUBED
    
    return result

def apply_traditional_model(X, y, X_test):
    """
    Apply a traditional domain-specific model.
    
    For simplicity, we'll use a Random Forest regressor as our "traditional" model.
    In a real test, this would be replaced with actual domain-specific models.
    
    Args:
        X: Training features
        y: Training target
        X_test: Test features
        
    Returns:
        array: Predictions
    """
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X, y)
    return model.predict(X_test)

def evaluate_models(domain_data, domain_name):
    """
    Evaluate the UUFT equation and traditional models on a domain.
    
    Args:
        domain_data: Data for the domain
        domain_name: Name of the domain
        
    Returns:
        dict: Evaluation results
    """
    print(f"Evaluating models for {domain_name} domain")
    
    # Extract inputs and output
    A = domain_data["inputs"]["A"]
    B = domain_data["inputs"]["B"]
    C = domain_data["inputs"]["C"]
    y_true = domain_data["output"]
    
    # Split data into training and testing sets
    n_samples = len(y_true)
    n_train = int(0.7 * n_samples)
    
    A_train, A_test = A[:n_train], A[n_train:]
    B_train, B_test = B[:n_train], B[n_train:]
    C_train, C_test = C[:n_train], C[n_train:]
    y_train, y_test = y_true[:n_train], y_true[n_train:]
    
    # Apply UUFT equation
    y_pred_uuft = np.array([apply_uuft_equation(a, b, c) for a, b, c in zip(A_test, B_test, C_test)])
    
    # Scale UUFT predictions to match the output range
    # This is necessary because the UUFT equation might produce values in a different scale
    scale_factor = np.mean(y_test) / np.mean(y_pred_uuft) if np.mean(y_pred_uuft) != 0 else 1.0
    y_pred_uuft_scaled = y_pred_uuft * scale_factor
    
    # Apply traditional model
    X_train = np.column_stack((A_train, B_train, C_train))
    X_test = np.column_stack((A_test, B_test, C_test))
    y_pred_traditional = apply_traditional_model(X_train, y_train, X_test)
    
    # Calculate metrics
    mse_uuft = mean_squared_error(y_test, y_pred_uuft_scaled)
    mse_traditional = mean_squared_error(y_test, y_pred_traditional)
    
    r2_uuft = r2_score(y_test, y_pred_uuft_scaled)
    r2_traditional = r2_score(y_test, y_pred_traditional)
    
    # Calculate performance improvement
    if mse_traditional > 0:
        performance_improvement = mse_traditional / mse_uuft
    else:
        performance_improvement = float('inf')
    
    # Calculate accuracy (1 - normalized error)
    accuracy_uuft = 1.0 - np.sqrt(mse_uuft) / (np.max(y_test) - np.min(y_test))
    accuracy_traditional = 1.0 - np.sqrt(mse_traditional) / (np.max(y_test) - np.min(y_test))
    
    # Store results
    results = {
        "domain": domain_name,
        "description": domain_data["description"],
        "metrics": {
            "uuft": {
                "mse": float(mse_uuft),
                "r2": float(r2_uuft),
                "accuracy": float(accuracy_uuft)
            },
            "traditional": {
                "mse": float(mse_traditional),
                "r2": float(r2_traditional),
                "accuracy": float(accuracy_traditional)
            }
        },
        "performance_improvement": float(performance_improvement),
        "predictions": {
            "y_test": y_test.tolist(),
            "y_pred_uuft": y_pred_uuft_scaled.tolist(),
            "y_pred_traditional": y_pred_traditional.tolist()
        }
    }
    
    return results

def plot_results(all_results):
    """
    Plot the results of the performance evaluation.
    
    Args:
        all_results: Results from evaluate_models() for all domains
    """
    # Plot performance improvement
    domains = list(all_results.keys())
    improvements = [all_results[d]["performance_improvement"] for d in domains]
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(domains, improvements)
    
    # Add a horizontal line at 3142 (expected improvement)
    plt.axhline(y=3142, color='r', linestyle='--', label='Expected (3,142x)')
    
    plt.xlabel('Domain')
    plt.ylabel('Performance Improvement (Traditional / UUFT)')
    plt.title('UUFT Performance Test: Improvement Over Traditional Models')
    plt.yscale('log')  # Use log scale for better visualization
    plt.legend()
    
    # Add improvement values on top of bars
    for bar, improvement in zip(bars, improvements):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() * 1.1,
                f'{improvement:.1f}x', ha='center', va='bottom')
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_performance_improvement.png"))
    plt.close()
    
    # Plot accuracy comparison
    accuracies_uuft = [all_results[d]["metrics"]["uuft"]["accuracy"] for d in domains]
    accuracies_traditional = [all_results[d]["metrics"]["traditional"]["accuracy"] for d in domains]
    
    plt.figure(figsize=(10, 6))
    
    x = np.arange(len(domains))
    width = 0.35
    
    plt.bar(x - width/2, accuracies_uuft, width, label='UUFT')
    plt.bar(x + width/2, accuracies_traditional, width, label='Traditional')
    
    plt.xlabel('Domain')
    plt.ylabel('Accuracy')
    plt.title('UUFT Performance Test: Accuracy Comparison')
    plt.xticks(x, domains)
    plt.ylim(0, 1)
    plt.legend()
    
    # Add a horizontal line at 0.95 (expected UUFT accuracy)
    plt.axhline(y=0.95, color='r', linestyle='--', label='Expected UUFT (95%)')
    
    plt.savefig(os.path.join(RESULTS_DIR, "uuft_accuracy_comparison.png"))
    plt.close()

def save_results(all_results):
    """
    Save the results to a text file.
    
    Args:
        all_results: Results from evaluate_models() for all domains
    """
    with open(os.path.join(RESULTS_DIR, "uuft_performance_results.txt"), "w") as f:
        f.write("UUFT Equation Performance Test Results\n")
        f.write("====================================\n")
        f.write(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Write summary
        f.write("Summary:\n")
        for domain, results in all_results.items():
            f.write(f"  {domain.capitalize()}: {results['description']}\n")
            f.write(f"    UUFT Accuracy: {results['metrics']['uuft']['accuracy']:.4f}\n")
            f.write(f"    Traditional Accuracy: {results['metrics']['traditional']['accuracy']:.4f}\n")
            f.write(f"    Performance Improvement: {results['performance_improvement']:.1f}x\n")
        
        f.write("\n")
        
        # Write detailed results
        f.write("Detailed Results by Domain:\n")
        for domain, results in all_results.items():
            f.write(f"\n{domain.capitalize()}: {results['description']}\n")
            
            f.write("  UUFT Model:\n")
            f.write(f"    MSE: {results['metrics']['uuft']['mse']:.6f}\n")
            f.write(f"    R²: {results['metrics']['uuft']['r2']:.4f}\n")
            f.write(f"    Accuracy: {results['metrics']['uuft']['accuracy']:.4f}\n")
            
            f.write("  Traditional Model:\n")
            f.write(f"    MSE: {results['metrics']['traditional']['mse']:.6f}\n")
            f.write(f"    R²: {results['metrics']['traditional']['r2']:.4f}\n")
            f.write(f"    Accuracy: {results['metrics']['traditional']['accuracy']:.4f}\n")
            
            f.write(f"  Performance Improvement: {results['performance_improvement']:.1f}x\n")

def main():
    """Run the UUFT equation performance test."""
    print("Running UUFT Equation Performance Test")
    print("=====================================")
    
    # Generate test data
    test_data = generate_test_data()
    
    # Evaluate models for each domain
    all_results = {}
    for domain, data in test_data.items():
        all_results[domain] = evaluate_models(data, domain)
    
    # Plot the results
    plot_results(all_results)
    
    # Save the results
    save_results(all_results)
    
    print("\nTest complete. Results saved to the uuft_test_results directory.")
    
    # Calculate average performance improvement
    avg_improvement = np.mean([results["performance_improvement"] for results in all_results.values()])
    print(f"\nAverage performance improvement: {avg_improvement:.1f}x")
    
    # Calculate average UUFT accuracy
    avg_accuracy_uuft = np.mean([results["metrics"]["uuft"]["accuracy"] for results in all_results.values()])
    print(f"Average UUFT accuracy: {avg_accuracy_uuft:.4f}")
    
    # Check if results are close to expected values
    is_improvement_close = abs(avg_improvement - 3142) / 3142 < 0.5  # Within 50% of expected
    is_accuracy_close = abs(avg_accuracy_uuft - 0.95) < 0.1  # Within 10% of expected
    
    print(f"\nIs improvement close to 3,142x? {'Yes' if is_improvement_close else 'No'}")
    print(f"Is accuracy close to 95%? {'Yes' if is_accuracy_close else 'No'}")
    
    return all_results

if __name__ == "__main__":
    main()
