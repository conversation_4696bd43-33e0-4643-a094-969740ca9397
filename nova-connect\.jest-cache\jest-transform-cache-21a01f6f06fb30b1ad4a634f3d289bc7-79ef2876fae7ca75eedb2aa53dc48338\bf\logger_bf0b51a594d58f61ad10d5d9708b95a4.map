{"version": 3, "names": ["logger", "info", "message", "meta", "console", "log", "error", "warn", "debug", "module", "exports"], "sources": ["logger.js"], "sourcesContent": ["/**\n * Logger Configuration\n * \n * This module provides a simple logger for the application.\n */\n\nconst logger = {\n  info: (message, meta = {}) => {\n    console.log(`[INFO] ${message}`, meta);\n  },\n  \n  error: (message, meta = {}) => {\n    console.error(`[ERROR] ${message}`, meta);\n  },\n  \n  warn: (message, meta = {}) => {\n    console.warn(`[WARN] ${message}`, meta);\n  },\n  \n  debug: (message, meta = {}) => {\n    console.debug(`[DEBUG] ${message}`, meta);\n  }\n};\n\nmodule.exports = logger;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,MAAM,GAAG;EACbC,IAAI,EAAEA,CAACC,OAAO,EAAEC,IAAI,GAAG,CAAC,CAAC,KAAK;IAC5BC,OAAO,CAACC,GAAG,CAAC,UAAUH,OAAO,EAAE,EAAEC,IAAI,CAAC;EACxC,CAAC;EAEDG,KAAK,EAAEA,CAACJ,OAAO,EAAEC,IAAI,GAAG,CAAC,CAAC,KAAK;IAC7BC,OAAO,CAACE,KAAK,CAAC,WAAWJ,OAAO,EAAE,EAAEC,IAAI,CAAC;EAC3C,CAAC;EAEDI,IAAI,EAAEA,CAACL,OAAO,EAAEC,IAAI,GAAG,CAAC,CAAC,KAAK;IAC5BC,OAAO,CAACG,IAAI,CAAC,UAAUL,OAAO,EAAE,EAAEC,IAAI,CAAC;EACzC,CAAC;EAEDK,KAAK,EAAEA,CAACN,OAAO,EAAEC,IAAI,GAAG,CAAC,CAAC,KAAK;IAC7BC,OAAO,CAACI,KAAK,CAAC,WAAWN,OAAO,EAAE,EAAEC,IAAI,CAAC;EAC3C;AACF,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAGV,MAAM", "ignoreList": []}