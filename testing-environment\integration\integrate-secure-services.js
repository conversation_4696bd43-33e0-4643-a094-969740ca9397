/**
 * Integration Script for Secure Services
 * 
 * This script integrates the secure versions of services into the main application.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Service paths
const services = [
  {
    name: 'Authentication Service',
    source: path.resolve(__dirname, '../auth-service/server-secure.js'),
    destination: path.resolve(__dirname, '../auth-service/server.js'),
    backup: path.resolve(__dirname, '../auth-service/server.js.bak')
  },
  {
    name: 'Connector Executor',
    source: path.resolve(__dirname, '../connector-executor/server-secure.js'),
    destination: path.resolve(__dirname, '../connector-executor/server.js'),
    backup: path.resolve(__dirname, '../connector-executor/server.js.bak')
  },
  {
    name: 'Connector Registry',
    source: path.resolve(__dirname, '../connector-registry/server-secure.js'),
    destination: path.resolve(__dirname, '../connector-registry/server.js'),
    backup: path.resolve(__dirname, '../connector-registry/server.js.bak')
  }
];

// Utility directories
const utilDirs = [
  {
    name: 'Encryption Utilities',
    source: path.resolve(__dirname, '../utils'),
    destinations: [
      path.resolve(__dirname, '../auth-service/utils'),
      path.resolve(__dirname, '../connector-executor/utils'),
      path.resolve(__dirname, '../connector-registry/utils')
    ]
  }
];

/**
 * Create a backup of a file
 * 
 * @param {string} source - Source file path
 * @param {string} backup - Backup file path
 * @returns {boolean} - Whether the backup was successful
 */
function createBackup(source, backup) {
  try {
    if (fs.existsSync(source)) {
      fs.copyFileSync(source, backup);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`${colors.red}Error creating backup: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Copy a file
 * 
 * @param {string} source - Source file path
 * @param {string} destination - Destination file path
 * @returns {boolean} - Whether the copy was successful
 */
function copyFile(source, destination) {
  try {
    fs.copyFileSync(source, destination);
    return true;
  } catch (error) {
    console.error(`${colors.red}Error copying file: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Copy a directory recursively
 * 
 * @param {string} source - Source directory path
 * @param {string} destination - Destination directory path
 * @returns {boolean} - Whether the copy was successful
 */
function copyDirectory(source, destination) {
  try {
    // Create destination directory if it doesn't exist
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }
    
    // Get all files and directories in the source directory
    const entries = fs.readdirSync(source, { withFileTypes: true });
    
    // Copy each entry
    for (const entry of entries) {
      const sourcePath = path.join(source, entry.name);
      const destinationPath = path.join(destination, entry.name);
      
      if (entry.isDirectory()) {
        // Recursively copy directory
        copyDirectory(sourcePath, destinationPath);
      } else {
        // Copy file
        fs.copyFileSync(sourcePath, destinationPath);
      }
    }
    
    return true;
  } catch (error) {
    console.error(`${colors.red}Error copying directory: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Integrate secure services
 */
function integrateSecureServices() {
  console.log(`${colors.bright}${colors.magenta}=== Integrating Secure Services ===${colors.reset}\n`);
  
  // Integrate services
  for (const service of services) {
    console.log(`${colors.bright}${colors.blue}Integrating ${service.name}...${colors.reset}`);
    
    // Create backup
    console.log(`${colors.yellow}Creating backup...${colors.reset}`);
    const backupCreated = createBackup(service.destination, service.backup);
    
    if (backupCreated) {
      console.log(`${colors.green}✓ Backup created at ${service.backup}${colors.reset}`);
    } else {
      console.log(`${colors.yellow}! No backup created (source file may not exist)${colors.reset}`);
    }
    
    // Copy secure service
    console.log(`${colors.yellow}Copying secure service...${colors.reset}`);
    const copySuccessful = copyFile(service.source, service.destination);
    
    if (copySuccessful) {
      console.log(`${colors.green}✓ Secure service integrated successfully${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Failed to integrate secure service${colors.reset}`);
    }
    
    console.log();
  }
  
  // Copy utility directories
  for (const utilDir of utilDirs) {
    console.log(`${colors.bright}${colors.blue}Copying ${utilDir.name}...${colors.reset}`);
    
    for (const destination of utilDir.destinations) {
      console.log(`${colors.yellow}Copying to ${destination}...${colors.reset}`);
      const copySuccessful = copyDirectory(utilDir.source, destination);
      
      if (copySuccessful) {
        console.log(`${colors.green}✓ Utilities copied successfully${colors.reset}`);
      } else {
        console.log(`${colors.red}✗ Failed to copy utilities${colors.reset}`);
      }
    }
    
    console.log();
  }
  
  console.log(`${colors.bright}${colors.green}Integration complete!${colors.reset}`);
}

// Run the integration
integrateSecureServices();
