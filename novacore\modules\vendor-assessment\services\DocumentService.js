/**
 * NovaCore Document Service
 * 
 * This service provides functionality for managing vendor documents.
 */

const { Document } = require('../models');
const { BlockchainService } = require('../../../api/services');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

class DocumentService {
  /**
   * Create a new document
   * @param {Object} data - Document data
   * @param {Buffer|string} fileContent - File content
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created document
   */
  async createDocument(data, fileContent, userId) {
    try {
      logger.info('Creating new document', { 
        organizationId: data.organizationId,
        vendorId: data.vendorId
      });
      
      // Generate hash for content
      const hash = this._generateContentHash(fileContent);
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Set content and hash
      data.content = fileContent;
      data.hash = hash;
      
      // Create document
      const document = new Document(data);
      await document.save();
      
      logger.info('Document created successfully', { id: document._id });
      
      return document;
    } catch (error) {
      logger.error('Error creating document', { error });
      throw error;
    }
  }
  
  /**
   * Get all documents for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Documents with pagination info
   */
  async getAllDocuments(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.vendorId) {
        query.vendorId = filter.vendorId;
      }
      
      if (filter.assessmentId) {
        query.assessmentId = filter.assessmentId;
      }
      
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      if (filter.tags) {
        query.tags = { $all: Array.isArray(filter.tags) ? filter.tags : [filter.tags] };
      }
      
      if (filter.frameworks) {
        query.frameworks = { $all: Array.isArray(filter.frameworks) ? filter.frameworks : [filter.frameworks] };
      }
      
      if (filter.blockchainVerified) {
        query.blockchainVerified = filter.blockchainVerified === 'true';
      }
      
      if (filter.search) {
        query.$or = [
          { name: { $regex: filter.search, $options: 'i' } },
          { description: { $regex: filter.search, $options: 'i' } }
        ];
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [documents, total] = await Promise.all([
        Document.find(query)
          .select('-content') // Exclude content field
          .sort(sort)
          .skip(skip)
          .limit(limit),
        Document.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: documents,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting documents', { error });
      throw error;
    }
  }
  
  /**
   * Get document by ID
   * @param {string} id - Document ID
   * @param {boolean} includeContent - Whether to include content
   * @returns {Promise<Object>} - Document
   */
  async getDocumentById(id, includeContent = false) {
    try {
      const projection = includeContent ? {} : { content: 0 };
      const document = await Document.findById(id, projection);
      
      if (!document) {
        throw new NotFoundError(`Document with ID ${id} not found`);
      }
      
      return document;
    } catch (error) {
      logger.error('Error getting document by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Get document content
   * @param {string} id - Document ID
   * @returns {Promise<Buffer>} - Document content
   */
  async getDocumentContent(id) {
    try {
      const document = await Document.findById(id, { content: 1 });
      
      if (!document) {
        throw new NotFoundError(`Document with ID ${id} not found`);
      }
      
      if (!document.content) {
        throw new NotFoundError(`Content for document with ID ${id} not found`);
      }
      
      return document.content;
    } catch (error) {
      logger.error('Error getting document content', { id, error });
      throw error;
    }
  }
  
  /**
   * Update document
   * @param {string} id - Document ID
   * @param {Object} data - Updated document data
   * @param {Buffer|string} fileContent - Updated file content (optional)
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated document
   */
  async updateDocument(id, data, fileContent, userId) {
    try {
      // Get existing document
      const document = await this.getDocumentById(id, true);
      
      // Update content and hash if provided
      if (fileContent) {
        data.content = fileContent;
        data.hash = this._generateContentHash(fileContent);
        
        // Reset blockchain verification if content changed
        if (document.blockchainVerified) {
          data.blockchainVerified = false;
          data.blockchainVerificationId = null;
        }
      }
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update document
      Object.assign(document, data);
      await document.save();
      
      logger.info('Document updated successfully', { id });
      
      // Return document without content
      const updatedDocument = await this.getDocumentById(id);
      return updatedDocument;
    } catch (error) {
      logger.error('Error updating document', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete document
   * @param {string} id - Document ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteDocument(id) {
    try {
      const result = await Document.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`Document with ID ${id} not found`);
      }
      
      logger.info('Document deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting document', { id, error });
      throw error;
    }
  }
  
  /**
   * Verify document with blockchain
   * @param {string} id - Document ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Verification result
   */
  async verifyDocument(id, userId) {
    try {
      // Get document
      const document = await this.getDocumentById(id, true);
      
      // Verify with blockchain service
      const verification = await BlockchainService.verifyEvidence(id, userId);
      
      // Update document verification status
      document.blockchainVerified = true;
      document.blockchainVerificationId = verification._id;
      document.updatedBy = userId;
      
      await document.save();
      
      logger.info('Document verification initiated', { id, verificationId: verification._id });
      
      return verification;
    } catch (error) {
      logger.error('Error verifying document', { id, error });
      throw error;
    }
  }
  
  /**
   * Check verification status for document
   * @param {string} id - Document ID
   * @returns {Promise<Object>} - Verification status
   */
  async checkVerificationStatus(id) {
    try {
      // Get document
      const document = await this.getDocumentById(id);
      
      if (!document.blockchainVerificationId) {
        return {
          documentId: id,
          status: 'not_verified',
          message: 'Document has not been submitted for verification'
        };
      }
      
      // Check verification status
      const verification = await BlockchainService.checkVerificationStatus(
        document.blockchainVerificationId
      );
      
      return {
        documentId: id,
        status: document.blockchainVerified ? 'verified' : 'pending',
        verificationId: document.blockchainVerificationId,
        blockchainStatus: verification.status,
        transaction: verification.transaction
      };
    } catch (error) {
      logger.error('Error checking document verification status', { id, error });
      throw error;
    }
  }
  
  /**
   * Import document from file
   * @param {Object} fileData - File data (name, path, etc.)
   * @param {Object} metadata - Document metadata
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Imported document
   */
  async importDocumentFromFile(fileData, metadata, userId) {
    try {
      logger.info('Importing document from file', { 
        organizationId: metadata.organizationId,
        fileName: fileData.originalname
      });
      
      // Read file content
      const fileContent = await fs.readFile(fileData.path);
      
      // Create document data
      const documentData = {
        name: metadata.name || fileData.originalname,
        description: metadata.description,
        organizationId: metadata.organizationId,
        vendorId: metadata.vendorId,
        assessmentId: metadata.assessmentId,
        type: metadata.type || this._guessDocumentType(fileData.originalname),
        format: this._getFileFormat(fileData.originalname),
        mimeType: fileData.mimetype,
        size: fileData.size,
        tags: metadata.tags,
        frameworks: metadata.frameworks,
        status: metadata.status || 'active',
        expirationDate: metadata.expirationDate
      };
      
      // Create document
      const document = await this.createDocument(documentData, fileContent, userId);
      
      // Clean up temporary file
      try {
        await fs.unlink(fileData.path);
      } catch (unlinkError) {
        logger.warn('Error deleting temporary file', { path: fileData.path, error: unlinkError });
      }
      
      logger.info('Document imported successfully', { id: document._id });
      
      // Return document without content
      return await this.getDocumentById(document._id);
    } catch (error) {
      logger.error('Error importing document from file', { error });
      throw error;
    }
  }
  
  /**
   * Generate hash for content
   * @param {Buffer|string} content - Content
   * @returns {string} - Content hash
   * @private
   */
  _generateContentHash(content) {
    return crypto.createHash('sha256').update(content).digest('hex');
  }
  
  /**
   * Get file format from filename
   * @param {string} filename - Filename
   * @returns {string} - File format
   * @private
   */
  _getFileFormat(filename) {
    const ext = path.extname(filename).toLowerCase().substring(1);
    
    const formatMap = {
      'pdf': 'pdf',
      'doc': 'doc',
      'docx': 'docx',
      'xls': 'xls',
      'xlsx': 'xlsx',
      'ppt': 'ppt',
      'pptx': 'pptx',
      'txt': 'txt',
      'csv': 'csv',
      'json': 'json',
      'xml': 'xml',
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image'
    };
    
    return formatMap[ext] || 'other';
  }
  
  /**
   * Guess document type from filename
   * @param {string} filename - Filename
   * @returns {string} - Document type
   * @private
   */
  _guessDocumentType(filename) {
    const lowerFilename = filename.toLowerCase();
    
    if (lowerFilename.includes('contract')) {
      return 'contract';
    } else if (lowerFilename.includes('sla')) {
      return 'sla';
    } else if (lowerFilename.includes('dpa') || lowerFilename.includes('data processing')) {
      return 'dpa';
    } else if (lowerFilename.includes('questionnaire') || lowerFilename.includes('assessment')) {
      return 'security_questionnaire';
    } else if (lowerFilename.includes('audit') || lowerFilename.includes('report')) {
      return 'audit_report';
    } else if (lowerFilename.includes('certification') || lowerFilename.includes('certificate')) {
      return 'certification';
    } else if (lowerFilename.includes('policy')) {
      return 'policy';
    } else if (lowerFilename.includes('procedure')) {
      return 'procedure';
    } else if (lowerFilename.includes('evidence')) {
      return 'evidence';
    } else {
      return 'other';
    }
  }
}

module.exports = new DocumentService();
