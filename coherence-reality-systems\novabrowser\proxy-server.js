// Simple proxy server to enable iframe browsing
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Enable CORS for all routes
app.use(cors());

// Proxy middleware that strips frame-blocking headers
const proxyMiddleware = createProxyMiddleware({
  target: 'https://httpbin.org', // Default target
  changeOrigin: true,
  router: (req) => {
    // Extract target URL from query parameter
    const targetUrl = req.query.url;
    if (targetUrl) {
      try {
        const url = new URL(targetUrl);
        return `${url.protocol}//${url.host}`;
      } catch (e) {
        return 'https://httpbin.org';
      }
    }
    return 'https://httpbin.org';
  },
  pathRewrite: (path, req) => {
    const targetUrl = req.query.url;
    if (targetUrl) {
      try {
        const url = new URL(targetUrl);
        return url.pathname + url.search;
      } catch (e) {
        return '/';
      }
    }
    return '/';
  },
  onProxyRes: (proxyRes, req, res) => {
    // Remove headers that block iframe embedding
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    delete proxyRes.headers['content-security-policy-report-only'];
    
    // Add headers to allow iframe embedding
    proxyRes.headers['x-frame-options'] = 'ALLOWALL';
    
    console.log(`Proxied: ${req.query.url}`);
  },
  onError: (err, req, res) => {
    console.error('Proxy error:', err);
    res.status(500).send('Proxy error: ' + err.message);
  }
});

// Proxy route
app.use('/proxy', proxyMiddleware);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'NovaBrowser proxy server running' });
});

app.listen(PORT, () => {
  console.log(`🌐 NovaBrowser Proxy Server running on http://localhost:${PORT}`);
  console.log(`📋 Usage: http://localhost:${PORT}/proxy?url=https://example.com`);
});

module.exports = app;
