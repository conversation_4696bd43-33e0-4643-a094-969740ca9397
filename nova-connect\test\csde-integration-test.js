/**
 * CSDE Integration Test
 * 
 * This test demonstrates how to integrate NovaConnect with the Cyber-Safety Domain Engine (CSDE)
 * to leverage the UUFT equation for enhanced data transformation and processing.
 */

const CSEDIntegration = require('../src/integrations/csde-integration');
const axios = require('axios');
const { performance } = require('perf_hooks');

// Mock axios for testing
jest.mock('axios');

// Sample security finding data
const securityFinding = {
  id: 'finding-123456',
  source: 'gcp-security-command-center',
  severity: 'HIGH',
  category: 'VULNERABILITY',
  createTime: '2025-05-08T12:34:56.789Z',
  updateTime: '2025-05-08T13:45:12.345Z',
  resourceName: 'projects/my-project/zones/us-central1-a/instances/my-instance',
  resourceType: 'google.compute.Instance',
  state: 'ACTIVE',
  externalUri: 'https://console.cloud.google.com/security/command-center/findings?project=my-project',
  sourceProperties: {
    scanConfigId: 'scan-config-12345',
    scanRunId: 'scan-run-67890',
    vulnerabilityType: 'XSS',
    vulnerabilityDetails: {
      cvssScore: 8.5,
      cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H',
      cve: 'CVE-2025-12345',
      description: 'Cross-site scripting vulnerability in web application',
      references: [
        'https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-12345',
        'https://nvd.nist.gov/vuln/detail/CVE-2025-12345'
      ],
      fixAvailable: true,
      exploitAvailable: true
    }
  },
  securityMarks: {
    marks: {
      criticality: 'p0',
      compliance: 'pci-dss,hipaa,soc2',
      dataClassification: 'confidential'
    }
  },
  nextSteps: [
    'Update web application to latest version',
    'Apply security patch',
    'Implement input validation'
  ],
  complianceState: 'NON_COMPLIANT'
};

// Transformation rules
const transformationRules = [
  {
    source: 'id',
    target: 'finding_id',
    transform: 'uppercase'
  },
  {
    source: 'source',
    target: 'source_system',
    transform: 'lowercase'
  },
  {
    source: 'severity',
    target: 'risk_level',
    transform: ['uppercase', 'trim']
  },
  {
    source: 'category',
    target: 'finding_type',
    transform: 'lowercase'
  },
  {
    source: 'createTime',
    target: 'created_at'
  },
  {
    source: 'updateTime',
    target: 'updated_at'
  },
  {
    source: 'resourceName',
    target: 'asset.full_path'
  },
  {
    source: 'resourceType',
    target: 'asset.type',
    transform: 'split',
    transformParams: '.'
  },
  {
    source: 'state',
    target: 'status',
    transform: 'lowercase'
  },
  {
    source: 'externalUri',
    target: 'links.console'
  },
  {
    source: 'sourceProperties.vulnerabilityType',
    target: 'details.vulnerability_type',
    transform: 'lowercase'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssScore',
    target: 'details.cvss_score',
    transform: 'toNumber'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssVector',
    target: 'details.cvss_vector'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cve',
    target: 'details.cve_id'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.description',
    target: 'details.description'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.references',
    target: 'details.references'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.fixAvailable',
    target: 'details.fix_available'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.exploitAvailable',
    target: 'details.exploit_available'
  },
  {
    source: 'securityMarks.marks.criticality',
    target: 'metadata.criticality'
  },
  {
    source: 'securityMarks.marks.compliance',
    target: 'metadata.compliance_frameworks',
    transform: 'split',
    transformParams: ','
  },
  {
    source: 'securityMarks.marks.dataClassification',
    target: 'metadata.data_classification'
  },
  {
    source: 'nextSteps',
    target: 'remediation.steps'
  },
  {
    source: 'complianceState',
    target: 'compliance_status',
    transform: 'lowercase'
  }
];

// Mock CSDE API response
const mockCsdeResponse = {
  data: {
    result: {
      csdeValue: 3142.59,
      performanceFactor: 3142,
      nistComponent: {
        processedValue: 10
      },
      gcpComponent: {
        processedValue: 10
      },
      cyberSafetyComponent: {
        processedValue: 31.42
      },
      tensorProduct: 100,
      fusionResult: 100.31,
      remediationActions: [
        {
          id: 'action-1',
          title: 'Update Web Application',
          description: 'Update the web application to the latest version to fix the XSS vulnerability',
          severity: 'HIGH',
          targetField: 'sourceProperties.vulnerabilityDetails.fixAvailable',
          enhancedValue: true
        },
        {
          id: 'action-2',
          title: 'Implement Input Validation',
          description: 'Implement proper input validation to prevent XSS attacks',
          severity: 'MEDIUM',
          targetField: 'nextSteps',
          enhancedValue: [
            'Update web application to latest version',
            'Apply security patch',
            'Implement input validation',
            'Add Content Security Policy (CSP) headers'
          ]
        }
      ],
      calculatedAt: new Date().toISOString()
    }
  }
};

// Test the CSDE integration
describe('CSDE Integration', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock axios.post to return the mock CSDE response
    axios.post.mockResolvedValue(mockCsdeResponse);
  });
  
  test('should transform data using CSDE', async () => {
    // Create CSDE integration
    const csdeIntegration = new CSEDIntegration({
      csdeApiUrl: 'http://localhost:3010'
    });
    
    // Transform data using CSDE
    const result = await csdeIntegration.transform(securityFinding, transformationRules);
    
    // Verify that axios.post was called with the correct arguments
    expect(axios.post).toHaveBeenCalledWith(
      'http://localhost:3010/calculate',
      expect.objectContaining({
        complianceData: expect.any(Object),
        gcpData: expect.any(Object),
        cyberSafetyData: expect.any(Object)
      }),
      expect.objectContaining({
        headers: {
          'Content-Type': 'application/json'
        }
      })
    );
    
    // Verify that the result contains the expected fields
    expect(result).toHaveProperty('finding_id', 'FINDING-123456');
    expect(result).toHaveProperty('source_system', 'gcp-security-command-center');
    expect(result).toHaveProperty('risk_level', 'HIGH');
    expect(result).toHaveProperty('finding_type', 'vulnerability');
    expect(result).toHaveProperty('created_at', '2025-05-08T12:34:56.789Z');
    expect(result).toHaveProperty('updated_at', '2025-05-08T13:45:12.345Z');
    expect(result).toHaveProperty('asset.full_path', 'projects/my-project/zones/us-central1-a/instances/my-instance');
    expect(result).toHaveProperty('asset.type');
    expect(result.asset.type).toEqual(['google', 'compute', 'Instance']);
    expect(result).toHaveProperty('status', 'active');
    expect(result).toHaveProperty('links.console', 'https://console.cloud.google.com/security/command-center/findings?project=my-project');
    expect(result).toHaveProperty('details.vulnerability_type', 'xss');
    expect(result).toHaveProperty('details.cvss_score', 8.5);
    expect(result).toHaveProperty('details.cvss_vector', 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H');
    expect(result).toHaveProperty('details.cve_id', 'CVE-2025-12345');
    expect(result).toHaveProperty('details.description', 'Cross-site scripting vulnerability in web application');
    expect(result).toHaveProperty('details.references');
    expect(result.details.references).toHaveLength(2);
    expect(result).toHaveProperty('details.fix_available', true);
    expect(result).toHaveProperty('details.exploit_available', true);
    expect(result).toHaveProperty('metadata.criticality', 'p0');
    expect(result).toHaveProperty('metadata.compliance_frameworks');
    expect(result.metadata.compliance_frameworks).toEqual(['pci-dss', 'hipaa', 'soc2']);
    expect(result).toHaveProperty('metadata.data_classification', 'confidential');
    expect(result).toHaveProperty('remediation.steps');
    expect(result.remediation.steps).toHaveLength(3);
    expect(result).toHaveProperty('compliance_status', 'non_compliant');
    
    // Verify that the result contains CSDE metadata
    expect(result).toHaveProperty('_csde');
    expect(result._csde).toHaveProperty('csdeValue', 3142.59);
    expect(result._csde).toHaveProperty('performanceFactor', 3142);
    expect(result._csde).toHaveProperty('calculatedAt');
    
    // Verify that the result contains remediation actions
    expect(result).toHaveProperty('_remediation');
    expect(result._remediation).toHaveLength(2);
    expect(result._remediation[0]).toHaveProperty('id', 'action-1');
    expect(result._remediation[0]).toHaveProperty('title', 'Update Web Application');
    expect(result._remediation[1]).toHaveProperty('id', 'action-2');
    expect(result._remediation[1]).toHaveProperty('title', 'Implement Input Validation');
  });
  
  test('should handle CSDE API errors', async () => {
    // Mock axios.post to throw an error
    axios.post.mockRejectedValue(new Error('CSDE API error'));
    
    // Create CSDE integration
    const csdeIntegration = new CSEDIntegration({
      csdeApiUrl: 'http://localhost:3010'
    });
    
    // Expect transform to throw an error
    await expect(csdeIntegration.transform(securityFinding, transformationRules))
      .rejects.toThrow('CSDE API call failed: CSDE API error');
  });
  
  test('should use cache for repeated transformations', async () => {
    // Create CSDE integration
    const csdeIntegration = new CSEDIntegration({
      csdeApiUrl: 'http://localhost:3010',
      enableCaching: true
    });
    
    // Transform data using CSDE
    await csdeIntegration.transform(securityFinding, transformationRules);
    
    // Transform the same data again
    await csdeIntegration.transform(securityFinding, transformationRules);
    
    // Verify that axios.post was called only once
    expect(axios.post).toHaveBeenCalledTimes(1);
    
    // Verify that the cache metrics were updated
    const metrics = csdeIntegration.getMetrics();
    expect(metrics.cacheHits).toBe(1);
    expect(metrics.cacheMisses).toBe(1);
  });
  
  test('should not use cache when disabled', async () => {
    // Create CSDE integration with caching disabled
    const csdeIntegration = new CSEDIntegration({
      csdeApiUrl: 'http://localhost:3010',
      enableCaching: false
    });
    
    // Transform data using CSDE
    await csdeIntegration.transform(securityFinding, transformationRules);
    
    // Transform the same data again
    await csdeIntegration.transform(securityFinding, transformationRules);
    
    // Verify that axios.post was called twice
    expect(axios.post).toHaveBeenCalledTimes(2);
  });
});

// If running this file directly
if (require.main === module) {
  // Create CSDE integration
  const csdeIntegration = new CSEDIntegration({
    csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010'
  });
  
  // Mock axios.post for direct execution
  axios.post = jest.fn().mockResolvedValue(mockCsdeResponse);
  
  // Transform data using CSDE
  console.log('Transforming data using CSDE...');
  csdeIntegration.transform(securityFinding, transformationRules)
    .then(result => {
      console.log('Transformation complete!');
      console.log('Result:', JSON.stringify(result, null, 2));
      console.log('Metrics:', csdeIntegration.getMetrics());
    })
    .catch(error => {
      console.error('Error:', error);
    });
}
