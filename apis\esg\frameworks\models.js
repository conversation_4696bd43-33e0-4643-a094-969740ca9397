/**
 * @swagger
 * components:
 *   schemas:
 *     ESGFramework:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the ESG framework
 *         code:
 *           type: string
 *           description: Short code for the framework
 *         name:
 *           type: string
 *           description: Name of the ESG framework
 *         description:
 *           type: string
 *           description: Description of the ESG framework
 *         version:
 *           type: string
 *           description: Version of the framework
 *         organization:
 *           type: string
 *           description: Organization that developed the framework
 *         website:
 *           type: string
 *           description: Official website for the framework
 *         category:
 *           type: string
 *           enum: [environmental, social, governance, general, industry-specific]
 *           description: Category of the framework
 *         industry:
 *           type: string
 *           description: Industry the framework is specific to (if applicable)
 *         region:
 *           type: string
 *           description: Region the framework is specific to (if applicable)
 *         isActive:
 *           type: boolean
 *           description: Whether the framework is active
 *         effectiveDate:
 *           type: string
 *           format: date
 *           description: Date when the framework became effective
 *         expirationDate:
 *           type: string
 *           format: date
 *           description: Date when the framework expires (if applicable)
 *         regulatoryStatus:
 *           type: string
 *           enum: [mandatory, voluntary, recommended]
 *           description: Regulatory status of the framework
 *         applicableRegulations:
 *           type: array
 *           items:
 *             type: string
 *           description: List of regulations this framework helps comply with
 *         maturityLevel:
 *           type: string
 *           enum: [emerging, established, mature, legacy]
 *           description: Maturity level of the framework
 *         adoptionRate:
 *           type: string
 *           description: Estimated adoption rate in the industry
 *         implementationCost:
 *           type: string
 *           enum: [low, medium, high]
 *           description: Estimated cost to implement the framework
 *         implementationComplexity:
 *           type: string
 *           enum: [low, medium, high]
 *           description: Complexity level to implement the framework
 *         updateFrequency:
 *           type: string
 *           description: How often the framework is typically updated
 *         lastUpdatedBy:
 *           type: string
 *           description: Organization that last updated the framework
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Tags for categorizing and searching frameworks
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the framework was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the framework was last updated
 *       required:
 *         - id
 *         - code
 *         - name
 *         - description
 *         - version
 *         - organization
 *         - category
 *         - isActive
 *         - createdAt
 *         - updatedAt
 *
 *     FrameworkCategory:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the category
 *         name:
 *           type: string
 *           description: Name of the category
 *         description:
 *           type: string
 *           description: Description of the category
 *       required:
 *         - id
 *         - name
 *         - description
 *
 *     FrameworkElement:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the framework element
 *         frameworkId:
 *           type: string
 *           description: ID of the associated framework
 *         code:
 *           type: string
 *           description: Code or reference number for the element
 *         name:
 *           type: string
 *           description: Name of the element
 *         description:
 *           type: string
 *           description: Description of the element
 *         category:
 *           type: string
 *           description: Category of the element within the framework
 *         parentId:
 *           type: string
 *           description: ID of the parent element (if hierarchical)
 *         level:
 *           type: integer
 *           description: Level in the hierarchy (if applicable)
 *         metrics:
 *           type: array
 *           items:
 *             type: string
 *           description: List of associated metric IDs
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the element was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the element was last updated
 *       required:
 *         - id
 *         - frameworkId
 *         - code
 *         - name
 *         - description
 *         - level
 *         - createdAt
 *         - updatedAt
 *
 *     FrameworkMapping:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the mapping
 *         sourceFrameworkId:
 *           type: string
 *           description: ID of the source framework
 *         sourceElementId:
 *           type: string
 *           description: ID of the source framework element
 *         targetFrameworkId:
 *           type: string
 *           description: ID of the target framework
 *         targetElementId:
 *           type: string
 *           description: ID of the target framework element
 *         mappingType:
 *           type: string
 *           enum: [exact, partial, related, superset, subset]
 *           description: Type of mapping relationship
 *         confidenceLevel:
 *           type: string
 *           enum: [low, medium, high]
 *           description: Confidence level of the mapping
 *         mappingMethod:
 *           type: string
 *           enum: [manual, automated, ai-assisted, expert-reviewed]
 *           description: Method used to create the mapping
 *         mappingVersion:
 *           type: string
 *           description: Version of the mapping
 *         validFrom:
 *           type: string
 *           format: date
 *           description: Date from which the mapping is valid
 *         validTo:
 *           type: string
 *           format: date
 *           description: Date until which the mapping is valid
 *         verifiedBy:
 *           type: string
 *           description: Person or organization that verified the mapping
 *         verifiedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the mapping was verified
 *         notes:
 *           type: string
 *           description: Additional notes about the mapping
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the mapping was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the mapping was last updated
 *       required:
 *         - id
 *         - sourceFrameworkId
 *         - sourceElementId
 *         - targetFrameworkId
 *         - targetElementId
 *         - mappingType
 *         - createdAt
 *         - updatedAt
 */

// Sample ESG frameworks
const esgFrameworks = [
  {
    id: 'frm-001',
    code: 'GRI',
    name: 'Global Reporting Initiative',
    description: 'The GRI Standards create a common language for organizations to report on their sustainability impacts in a consistent and credible way.',
    version: '2021',
    organization: 'Global Reporting Initiative',
    website: 'https://www.globalreporting.org',
    category: 'general',
    industry: null,
    region: null,
    isActive: true,
    effectiveDate: '2021-01-01',
    expirationDate: null,
    regulatoryStatus: 'voluntary',
    applicableRegulations: ['EU CSRD', 'EU NFRD'],
    maturityLevel: 'mature',
    adoptionRate: '80%',
    implementationCost: 'medium',
    implementationComplexity: 'medium',
    updateFrequency: 'Every 3-5 years',
    lastUpdatedBy: 'Global Reporting Initiative',
    tags: ['sustainability', 'reporting', 'global', 'comprehensive'],
    createdAt: '2023-01-10T08:00:00Z',
    updatedAt: '2023-01-10T08:00:00Z'
  },
  {
    id: 'frm-002',
    code: 'SASB',
    name: 'Sustainability Accounting Standards Board',
    description: 'SASB Standards guide the disclosure of financially material sustainability information by companies to their investors.',
    version: '2022',
    organization: 'Value Reporting Foundation',
    website: 'https://www.sasb.org',
    category: 'industry-specific',
    industry: null,
    region: null,
    isActive: true,
    effectiveDate: '2022-01-01',
    expirationDate: null,
    regulatoryStatus: 'recommended',
    applicableRegulations: ['SEC Climate Disclosure'],
    maturityLevel: 'established',
    adoptionRate: '65%',
    implementationCost: 'medium',
    implementationComplexity: 'high',
    updateFrequency: 'Every 2-3 years',
    lastUpdatedBy: 'Value Reporting Foundation',
    tags: ['financial', 'materiality', 'industry-specific', 'investors'],
    createdAt: '2023-01-10T08:15:00Z',
    updatedAt: '2023-01-10T08:15:00Z'
  },
  {
    id: 'frm-003',
    code: 'TCFD',
    name: 'Task Force on Climate-related Financial Disclosures',
    description: 'The TCFD framework helps organizations disclose climate-related financial information.',
    version: '2017',
    organization: 'Financial Stability Board',
    website: 'https://www.fsb-tcfd.org',
    category: 'environmental',
    industry: null,
    region: null,
    isActive: true,
    createdAt: '2023-01-10T08:30:00Z',
    updatedAt: '2023-01-10T08:30:00Z'
  },
  {
    id: 'frm-004',
    code: 'CDP',
    name: 'Carbon Disclosure Project',
    description: 'CDP runs a global disclosure system for investors, companies, cities, states and regions to manage their environmental impacts.',
    version: '2023',
    organization: 'CDP',
    website: 'https://www.cdp.net',
    category: 'environmental',
    industry: null,
    region: null,
    isActive: true,
    createdAt: '2023-01-10T08:45:00Z',
    updatedAt: '2023-01-10T08:45:00Z'
  },
  {
    id: 'frm-005',
    code: 'UNGC',
    name: 'United Nations Global Compact',
    description: 'The UN Global Compact is a voluntary initiative based on CEO commitments to implement universal sustainability principles.',
    version: '2000',
    organization: 'United Nations',
    website: 'https://www.unglobalcompact.org',
    category: 'general',
    industry: null,
    region: null,
    isActive: true,
    createdAt: '2023-01-10T09:00:00Z',
    updatedAt: '2023-01-10T09:00:00Z'
  }
];

// Sample framework categories
const frameworkCategories = [
  {
    id: 'cat-001',
    name: 'Environmental',
    description: 'Frameworks focused on environmental impacts and performance'
  },
  {
    id: 'cat-002',
    name: 'Social',
    description: 'Frameworks focused on social impacts and performance'
  },
  {
    id: 'cat-003',
    name: 'Governance',
    description: 'Frameworks focused on governance structures and practices'
  },
  {
    id: 'cat-004',
    name: 'General',
    description: 'Comprehensive frameworks covering multiple ESG aspects'
  },
  {
    id: 'cat-005',
    name: 'Industry-specific',
    description: 'Frameworks tailored to specific industries'
  }
];

// Sample framework elements
const frameworkElements = [
  // GRI elements
  {
    id: 'ele-001',
    frameworkId: 'frm-001',
    code: 'GRI 102',
    name: 'General Disclosures',
    description: 'Contextual information about an organization and its sustainability reporting practices',
    category: 'General',
    parentId: null,
    level: 1,
    metrics: ['met-001', 'met-002'],
    createdAt: '2023-01-15T10:00:00Z',
    updatedAt: '2023-01-15T10:00:00Z'
  },
  {
    id: 'ele-002',
    frameworkId: 'frm-001',
    code: 'GRI 102-1',
    name: 'Organizational Profile',
    description: 'Disclosures about an organization's name, activities, brands, products, and services',
    category: 'General',
    parentId: 'ele-001',
    level: 2,
    metrics: ['met-001'],
    createdAt: '2023-01-15T10:05:00Z',
    updatedAt: '2023-01-15T10:05:00Z'
  },
  {
    id: 'ele-003',
    frameworkId: 'frm-001',
    code: 'GRI 300',
    name: 'Environmental Standards',
    description: 'Topic-specific standards used to report information on an organization's environmental impacts',
    category: 'Environmental',
    parentId: null,
    level: 1,
    metrics: ['met-003', 'met-004'],
    createdAt: '2023-01-15T10:10:00Z',
    updatedAt: '2023-01-15T10:10:00Z'
  },
  {
    id: 'ele-004',
    frameworkId: 'frm-001',
    code: 'GRI 305',
    name: 'Emissions',
    description: 'Disclosures about an organization's emissions of greenhouse gases and other significant air emissions',
    category: 'Environmental',
    parentId: 'ele-003',
    level: 2,
    metrics: ['met-003'],
    createdAt: '2023-01-15T10:15:00Z',
    updatedAt: '2023-01-15T10:15:00Z'
  },

  // SASB elements
  {
    id: 'ele-005',
    frameworkId: 'frm-002',
    code: 'SASB-ENV',
    name: 'Environment',
    description: 'Environmental dimension of SASB standards',
    category: 'Environmental',
    parentId: null,
    level: 1,
    metrics: ['met-003', 'met-005'],
    createdAt: '2023-01-15T11:00:00Z',
    updatedAt: '2023-01-15T11:00:00Z'
  },
  {
    id: 'ele-006',
    frameworkId: 'frm-002',
    code: 'SASB-SOC',
    name: 'Social Capital',
    description: 'Social capital dimension of SASB standards',
    category: 'Social',
    parentId: null,
    level: 1,
    metrics: ['met-006'],
    createdAt: '2023-01-15T11:05:00Z',
    updatedAt: '2023-01-15T11:05:00Z'
  },

  // TCFD elements
  {
    id: 'ele-007',
    frameworkId: 'frm-003',
    code: 'TCFD-GOV',
    name: 'Governance',
    description: 'Governance around climate-related risks and opportunities',
    category: 'Governance',
    parentId: null,
    level: 1,
    metrics: ['met-007'],
    createdAt: '2023-01-15T12:00:00Z',
    updatedAt: '2023-01-15T12:00:00Z'
  },
  {
    id: 'ele-008',
    frameworkId: 'frm-003',
    code: 'TCFD-STR',
    name: 'Strategy',
    description: 'Actual and potential impacts of climate-related risks and opportunities on the organization's businesses, strategy, and financial planning',
    category: 'Strategy',
    parentId: null,
    level: 1,
    metrics: ['met-008'],
    createdAt: '2023-01-15T12:05:00Z',
    updatedAt: '2023-01-15T12:05:00Z'
  }
];

// Sample framework mappings
const frameworkMappings = [
  {
    id: 'map-001',
    sourceFrameworkId: 'frm-001',
    sourceElementId: 'ele-004',
    targetFrameworkId: 'frm-003',
    targetElementId: 'ele-008',
    mappingType: 'related',
    confidenceLevel: 'high',
    mappingMethod: 'expert-reviewed',
    mappingVersion: '1.0',
    validFrom: '2023-01-01',
    validTo: null,
    verifiedBy: 'ESG Standards Expert Group',
    verifiedAt: '2023-01-15T14:30:00Z',
    notes: 'GRI emissions disclosures relate to TCFD strategy considerations',
    createdAt: '2023-02-01T09:00:00Z',
    updatedAt: '2023-02-01T09:00:00Z'
  },
  {
    id: 'map-002',
    sourceFrameworkId: 'frm-001',
    sourceElementId: 'ele-003',
    targetFrameworkId: 'frm-002',
    targetElementId: 'ele-005',
    mappingType: 'partial',
    confidenceLevel: 'medium',
    mappingMethod: 'ai-assisted',
    mappingVersion: '1.0',
    validFrom: '2023-01-01',
    validTo: null,
    verifiedBy: 'ESG Standards Expert Group',
    verifiedAt: '2023-01-20T10:30:00Z',
    notes: 'GRI environmental standards partially overlap with SASB environmental dimension',
    createdAt: '2023-02-01T09:15:00Z',
    updatedAt: '2023-02-01T09:15:00Z'
  },
  {
    id: 'map-003',
    sourceFrameworkId: 'frm-002',
    sourceElementId: 'ele-005',
    targetFrameworkId: 'frm-003',
    targetElementId: 'ele-008',
    mappingType: 'related',
    confidenceLevel: 'medium',
    mappingMethod: 'manual',
    mappingVersion: '1.0',
    validFrom: '2023-01-01',
    validTo: null,
    verifiedBy: 'ESG Standards Expert Group',
    verifiedAt: '2023-01-25T11:45:00Z',
    notes: 'SASB environmental metrics relate to TCFD strategy considerations',
    createdAt: '2023-02-01T09:30:00Z',
    updatedAt: '2023-02-01T09:30:00Z'
  }
];

module.exports = {
  esgFrameworks,
  frameworkCategories,
  frameworkElements,
  frameworkMappings
};
