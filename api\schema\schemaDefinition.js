/**
 * Schema Definition
 * 
 * This file defines the schema format for the Universal UI Generator.
 * It serves as both documentation and a reference for schema validation.
 */

/**
 * @typedef {Object} FieldValidation
 * @property {number} [minLength] - Minimum length for string fields
 * @property {number} [maxLength] - Maximum length for string fields
 * @property {number} [min] - Minimum value for number fields
 * @property {number} [max] - Maximum value for number fields
 * @property {string} [pattern] - Regex pattern for validation
 * @property {string} [message] - Custom error message
 */

/**
 * @typedef {Object} FieldOption
 * @property {string} value - Option value
 * @property {string} label - Option display label
 */

/**
 * @typedef {Object} FieldOptions
 * @property {string} [source] - Source of options ('static', 'api', 'function')
 * @property {string} [endpoint] - API endpoint for fetching options
 * @property {string} [valueField] - Field to use as option value
 * @property {string} [labelField] - Field to use as option label
 * @property {FieldOption[]} [values] - Static option values
 * @property {string} [function] - Function name to call for dynamic options
 */

/**
 * @typedef {Object} SchemaField
 * @property {string} name - Field name (matches the API model property)
 * @property {string} type - Field data type (string, number, boolean, date, array, object)
 * @property {string} label - Display label for the field
 * @property {boolean} [required] - Whether the field is required
 * @property {string} uiComponent - UI component to use for rendering
 * @property {FieldValidation} [validation] - Validation rules
 * @property {FieldOptions} [options] - Options for select, radio, checkbox components
 * @property {string} [description] - Field description for tooltips
 * @property {string} [placeholder] - Placeholder text
 * @property {boolean} [readOnly] - Whether the field is read-only
 * @property {boolean} [hidden] - Whether the field is hidden
 * @property {Object} [dependencies] - Field dependencies (show/hide based on other fields)
 * @property {Object} [permissions] - Permission requirements for the field
 * @property {Object} [style] - Custom styling for the field
 * @property {SchemaField[]} [fields] - Nested fields for object types
 */

/**
 * @typedef {Object} ListViewColumn
 * @property {string} field - Field name to display
 * @property {string} [label] - Column header label (defaults to field's label)
 * @property {string} [width] - Column width
 * @property {string} [align] - Text alignment
 * @property {string} [format] - Format function name
 * @property {boolean} [sortable] - Whether column is sortable
 * @property {boolean} [filterable] - Whether column is filterable
 */

/**
 * @typedef {Object} ListView
 * @property {ListViewColumn[]} columns - Columns to display in list view
 * @property {string[]} actions - Available actions (view, edit, delete)
 * @property {string} [defaultSort] - Default sort field
 * @property {string} [defaultSortDirection] - Default sort direction (asc, desc)
 * @property {number} [pageSize] - Default page size
 * @property {boolean} [searchable] - Whether list is searchable
 * @property {string[]} [searchFields] - Fields to search
 * @property {Object} [filters] - Default filters
 */

/**
 * @typedef {Object} DetailViewSection
 * @property {string} title - Section title
 * @property {string[]} fields - Fields to include in section
 * @property {boolean} [collapsible] - Whether section is collapsible
 * @property {boolean} [collapsed] - Whether section is collapsed by default
 */

/**
 * @typedef {Object} DetailView
 * @property {DetailViewSection[]} sections - Sections in detail view
 * @property {string[]} actions - Available actions (edit, delete)
 * @property {Object} [relatedEntities] - Related entities to display
 */

/**
 * @typedef {Object} FormView
 * @property {DetailViewSection[]} sections - Form sections
 * @property {string} [submitLabel] - Label for submit button
 * @property {string} [cancelLabel] - Label for cancel button
 * @property {boolean} [showReset] - Whether to show reset button
 * @property {string} [resetLabel] - Label for reset button
 */

/**
 * @typedef {Object} EntitySchema
 * @property {string} entityName - Entity name (singular)
 * @property {string} entityNamePlural - Entity name (plural)
 * @property {string} apiEndpoint - API endpoint for entity
 * @property {SchemaField[]} fields - Entity fields
 * @property {ListView} listView - List view configuration
 * @property {DetailView} detailView - Detail view configuration
 * @property {FormView} formView - Form view configuration
 * @property {Object} [permissions] - Permission requirements for entity
 * @property {Object} [hooks] - Lifecycle hooks
 */

// Example schema (for documentation purposes)
const exampleSchema = {
  entityName: "Control",
  entityNamePlural: "Controls",
  apiEndpoint: "/api/v1/novaassure/controls",
  fields: [
    {
      name: "name",
      type: "string",
      label: "Control Name",
      required: true,
      uiComponent: "text-input",
      validation: {
        minLength: 3,
        maxLength: 100,
        message: "Control name must be between 3 and 100 characters"
      },
      placeholder: "Enter control name",
      description: "The name of the control"
    },
    {
      name: "description",
      type: "string",
      label: "Description",
      required: true,
      uiComponent: "textarea",
      validation: {
        minLength: 10
      },
      placeholder: "Enter control description",
      description: "Detailed description of the control"
    },
    {
      name: "framework",
      type: "string",
      label: "Framework",
      required: true,
      uiComponent: "select",
      options: {
        source: "api",
        endpoint: "/api/v1/frameworks",
        valueField: "id",
        labelField: "name"
      },
      description: "The compliance framework this control belongs to"
    },
    {
      name: "category",
      type: "string",
      label: "Category",
      required: true,
      uiComponent: "select",
      options: {
        source: "static",
        values: [
          { value: "access-control", label: "Access Control" },
          { value: "data-protection", label: "Data Protection" },
          { value: "network-security", label: "Network Security" },
          { value: "logging-monitoring", label: "Logging & Monitoring" },
          { value: "incident-response", label: "Incident Response" },
          { value: "business-continuity", label: "Business Continuity" },
          { value: "compliance", label: "Compliance" }
        ]
      },
      description: "The category of the control"
    },
    {
      name: "status",
      type: "string",
      label: "Status",
      required: true,
      uiComponent: "radio-group",
      options: {
        source: "static",
        values: [
          { value: "active", label: "Active" },
          { value: "inactive", label: "Inactive" },
          { value: "draft", label: "Draft" }
        ]
      },
      description: "The current status of the control"
    },
    {
      name: "requirements",
      type: "array",
      label: "Requirements",
      uiComponent: "array-input",
      fields: [
        {
          name: "requirement",
          type: "string",
          label: "Requirement",
          uiComponent: "text-input",
          required: true
        }
      ],
      description: "List of requirements for this control"
    },
    {
      name: "testProcedures",
      type: "array",
      label: "Test Procedures",
      uiComponent: "array-input",
      fields: [
        {
          name: "procedure",
          type: "string",
          label: "Procedure",
          uiComponent: "textarea",
          required: true
        }
      ],
      description: "Test procedures for validating this control"
    }
  ],
  listView: {
    columns: [
      { field: "name", sortable: true, filterable: true },
      { field: "framework", sortable: true, filterable: true },
      { field: "category", sortable: true, filterable: true },
      { field: "status", sortable: true, filterable: true }
    ],
    actions: ["view", "edit", "delete"],
    defaultSort: "name",
    defaultSortDirection: "asc",
    pageSize: 10,
    searchable: true,
    searchFields: ["name", "description"]
  },
  detailView: {
    sections: [
      {
        title: "Basic Information",
        fields: ["name", "description", "framework", "category", "status"]
      },
      {
        title: "Requirements",
        fields: ["requirements"],
        collapsible: true
      },
      {
        title: "Test Procedures",
        fields: ["testProcedures"],
        collapsible: true
      }
    ],
    actions: ["edit", "delete"],
    relatedEntities: {
      testPlans: {
        label: "Test Plans",
        endpoint: "/api/v1/novaassure/test-plans?controlId={{id}}",
        displayField: "name"
      },
      evidence: {
        label: "Evidence",
        endpoint: "/api/v1/novaassure/evidence?controlId={{id}}",
        displayField: "name"
      }
    }
  },
  formView: {
    sections: [
      {
        title: "Basic Information",
        fields: ["name", "description", "framework", "category", "status"]
      },
      {
        title: "Requirements",
        fields: ["requirements"],
        collapsible: true
      },
      {
        title: "Test Procedures",
        fields: ["testProcedures"],
        collapsible: true
      }
    ],
    submitLabel: "Save Control",
    cancelLabel: "Cancel",
    showReset: true,
    resetLabel: "Reset Form"
  },
  permissions: {
    create: ["admin", "compliance-manager"],
    read: ["admin", "compliance-manager", "auditor"],
    update: ["admin", "compliance-manager"],
    delete: ["admin"]
  },
  hooks: {
    beforeCreate: "validateControlBeforeCreate",
    afterCreate: "notifyControlCreated",
    beforeUpdate: "validateControlBeforeUpdate",
    afterUpdate: "notifyControlUpdated",
    beforeDelete: "validateControlBeforeDelete",
    afterDelete: "notifyControlDeleted"
  }
};

module.exports = {
  exampleSchema
};
