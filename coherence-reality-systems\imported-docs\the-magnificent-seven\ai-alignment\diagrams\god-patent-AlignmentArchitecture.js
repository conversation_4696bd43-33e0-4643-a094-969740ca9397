import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const AlignmentArchitecture = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>CYBER-SAFETY FRAMEWORK: 3-6-9-12-13 ALIGNMENT ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* 3 Core Infrastructure Components */}
      <ContainerBox width="700px" height="100px" left="50px" top="70px">
        <ContainerLabel>3 CORE INFRASTRUCTURE COMPONENTS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="150px" top="110px" width="150px" height="40px">
        <ComponentNumber>501</ComponentNumber>
        <ComponentLabel>Pillar 3</ComponentLabel>
        Self-Destructing Servers
      </ComponentBox>
      
      <ComponentBox left="325px" top="110px" width="150px" height="40px">
        <ComponentNumber>502</ComponentNumber>
        <ComponentLabel>Pillar 9</ComponentLabel>
        Post-Quantum Journal
      </ComponentBox>
      
      <ComponentBox left="500px" top="110px" width="150px" height="40px">
        <ComponentNumber>503</ComponentNumber>
        <ComponentLabel>Pillar 12</ComponentLabel>
        C-Suite to Code Compiler
      </ComponentBox>
      
      {/* 6 Data Processing Components */}
      <ContainerBox width="700px" height="100px" left="50px" top="190px">
        <ContainerLabel>6 DATA PROCESSING COMPONENTS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="230px" width="100px" height="40px">
        <ComponentNumber>504</ComponentNumber>
        <ComponentLabel>Nova 3</ComponentLabel>
        NovaTrack
      </ComponentBox>
      
      <ComponentBox left="210px" top="230px" width="100px" height="40px">
        <ComponentNumber>505</ComponentNumber>
        <ComponentLabel>Nova 6</ComponentLabel>
        NovaFlowX
      </ComponentBox>
      
      <ComponentBox left="320px" top="230px" width="100px" height="40px">
        <ComponentNumber>506</ComponentNumber>
        <ComponentLabel>Nova 9</ComponentLabel>
        NovaThink
      </ComponentBox>
      
      <ComponentBox left="430px" top="230px" width="100px" height="40px">
        <ComponentNumber>507</ComponentNumber>
        <ComponentLabel>Nova 12</ComponentLabel>
        NovaDNA
      </ComponentBox>
      
      <ComponentBox left="540px" top="230px" width="100px" height="40px">
        <ComponentNumber>508</ComponentNumber>
        <ComponentLabel>Pillar 6</ComponentLabel>
        Cost Optimizer
      </ComponentBox>
      
      <ComponentBox left="650px" top="230px" width="100px" height="40px">
        <ComponentNumber>509</ComponentNumber>
        <ComponentLabel>Pillar 7</ComponentLabel>
        Training Data
      </ComponentBox>
      
      {/* 9 Continuances */}
      <ContainerBox width="700px" height="100px" left="50px" top="310px">
        <ContainerLabel>9 INDUSTRY-SPECIFIC CONTINUANCES</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="70px" top="350px" width="70px" height="40px">
        <ComponentNumber>510</ComponentNumber>
        <ComponentLabel>C1</ComponentLabel>
        Financial
      </ComponentBox>
      
      <ComponentBox left="145px" top="350px" width="70px" height="40px">
        <ComponentNumber>511</ComponentNumber>
        <ComponentLabel>C2</ComponentLabel>
        Healthcare
      </ComponentBox>
      
      <ComponentBox left="220px" top="350px" width="70px" height="40px">
        <ComponentNumber>512</ComponentNumber>
        <ComponentLabel>C3</ComponentLabel>
        Education
      </ComponentBox>
      
      <ComponentBox left="295px" top="350px" width="70px" height="40px">
        <ComponentNumber>513</ComponentNumber>
        <ComponentLabel>C4</ComponentLabel>
        Government
      </ComponentBox>
      
      <ComponentBox left="370px" top="350px" width="70px" height="40px">
        <ComponentNumber>514</ComponentNumber>
        <ComponentLabel>C5</ComponentLabel>
        Infrastructure
      </ComponentBox>
      
      <ComponentBox left="445px" top="350px" width="70px" height="40px">
        <ComponentNumber>515</ComponentNumber>
        <ComponentLabel>C6</ComponentLabel>
        AI Governance
      </ComponentBox>
      
      <ComponentBox left="520px" top="350px" width="70px" height="40px">
        <ComponentNumber>516</ComponentNumber>
        <ComponentLabel>C7</ComponentLabel>
        Supply Chain
      </ComponentBox>
      
      <ComponentBox left="595px" top="350px" width="70px" height="40px">
        <ComponentNumber>517</ComponentNumber>
        <ComponentLabel>C8</ComponentLabel>
        Insurance
      </ComponentBox>
      
      <ComponentBox left="670px" top="350px" width="70px" height="40px">
        <ComponentNumber>518</ComponentNumber>
        <ComponentLabel>C9</ComponentLabel>
        Mobile/IoT
      </ComponentBox>
      
      {/* 12 Pillars */}
      <ContainerBox width="700px" height="60px" left="50px" top="430px">
        <ContainerLabel>12 PILLARS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="375px" top="450px" width="50px" height="20px">
        <ComponentNumber>519</ComponentNumber>
        <ComponentLabel>P1-P12</ComponentLabel>
      </ComponentBox>
      
      {/* 12+1 Novas */}
      <ContainerBox width="700px" height="60px" left="50px" top="510px">
        <ContainerLabel>12+1 UNIVERSAL NOVAS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="375px" top="530px" width="50px" height="20px">
        <ComponentNumber>520</ComponentNumber>
        <ComponentLabel>N1-N13</ComponentLabel>
      </ComponentBox>
      
      {/* Connecting arrows */}
      <Arrow left="225px" top="170px" width="2px" height="20px" />
      <Arrow left="400px" top="170px" width="2px" height="20px" />
      <Arrow left="575px" top="170px" width="2px" height="20px" />
      
      <Arrow left="150px" top="290px" width="2px" height="20px" />
      <Arrow left="260px" top="290px" width="2px" height="20px" />
      <Arrow left="370px" top="290px" width="2px" height="20px" />
      <Arrow left="480px" top="290px" width="2px" height="20px" />
      <Arrow left="590px" top="290px" width="2px" height="20px" />
      <Arrow left="700px" top="290px" width="2px" height="20px" />
      
      <Arrow left="105px" top="390px" width="2px" height="40px" />
      <Arrow left="180px" top="390px" width="2px" height="40px" />
      <Arrow left="255px" top="390px" width="2px" height="40px" />
      <Arrow left="330px" top="390px" width="2px" height="40px" />
      <Arrow left="405px" top="390px" width="2px" height="40px" />
      <Arrow left="480px" top="390px" width="2px" height="40px" />
      <Arrow left="555px" top="390px" width="2px" height="40px" />
      <Arrow left="630px" top="390px" width="2px" height="40px" />
      <Arrow left="705px" top="390px" width="2px" height="40px" />
      
      <Arrow left="400px" top="490px" width="2px" height="20px" />
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Core Infrastructure (3)</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Data Processing (6)</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Continuances (9)</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Pillars (12)</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#000" />
          <LegendText>Novas (12+1)</LegendText>
        </LegendItem>
      </DiagramLegend>
      
      {/* Inventor Label */}
      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default AlignmentArchitecture;
