/**
 * Report Controller
 * 
 * This controller handles API requests related to reports.
 */

const ReportService = require('../services/ReportService');
const { ValidationError } = require('../utils/errors');

class ReportController {
  constructor() {
    this.reportService = new ReportService();
  }

  /**
   * Get all report templates
   */
  async getAllReportTemplates(req, res, next) {
    try {
      const templates = await this.reportService.getAllReportTemplates();
      res.json(templates);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get report templates by type
   */
  async getReportTemplatesByType(req, res, next) {
    try {
      const { type } = req.params;
      
      if (!type) {
        throw new ValidationError('Report type is required');
      }
      
      const templates = await this.reportService.getReportTemplatesByType(type);
      res.json(templates);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get report template by ID
   */
  async getReportTemplateById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Template ID is required');
      }
      
      const template = await this.reportService.getReportTemplateById(id);
      res.json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a new report template
   */
  async createReportTemplate(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Template data is required');
      }
      
      const template = await this.reportService.createReportTemplate(data, req.user.id);
      res.status(201).json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a report template
   */
  async updateReportTemplate(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Template ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Template data is required');
      }
      
      const template = await this.reportService.updateReportTemplate(id, data, req.user.id);
      res.json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a report template
   */
  async deleteReportTemplate(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Template ID is required');
      }
      
      const result = await this.reportService.deleteReportTemplate(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Clone a report template
   */
  async cloneReportTemplate(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body || {};
      
      if (!id) {
        throw new ValidationError('Template ID is required');
      }
      
      const template = await this.reportService.cloneReportTemplate(id, data, req.user.id);
      res.status(201).json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate a report
   */
  async generateReport(req, res, next) {
    try {
      const { templateId } = req.params;
      const parameters = req.body || {};
      
      if (!templateId) {
        throw new ValidationError('Template ID is required');
      }
      
      const report = await this.reportService.generateReport(templateId, parameters, req.user.id);
      res.status(201).json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all reports
   */
  async getAllReports(req, res, next) {
    try {
      const filters = req.query;
      const reports = await this.reportService.getAllReports(filters);
      res.json(reports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my reports
   */
  async getMyReports(req, res, next) {
    try {
      const filters = req.query;
      const reports = await this.reportService.getReportsForUser(req.user.id, filters);
      res.json(reports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get report by ID
   */
  async getReportById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Report ID is required');
      }
      
      const report = await this.reportService.getReportById(id);
      res.json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a report
   */
  async deleteReport(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Report ID is required');
      }
      
      const result = await this.reportService.deleteReport(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Export a report
   */
  async exportReport(req, res, next) {
    try {
      const { id, format } = req.params;
      
      if (!id) {
        throw new ValidationError('Report ID is required');
      }
      
      if (!format) {
        throw new ValidationError('Export format is required');
      }
      
      const result = await this.reportService.exportReport(id, format);
      
      // Set content type based on format
      res.set('Content-Type', result.contentType);
      
      // Set filename for download
      res.set('Content-Disposition', `attachment; filename="report-${id}.${format}"`);
      
      res.send(result.data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all scheduled reports
   */
  async getAllScheduledReports(req, res, next) {
    try {
      const filters = req.query;
      const reports = await this.reportService.getAllScheduledReports(filters);
      res.json(reports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my scheduled reports
   */
  async getMyScheduledReports(req, res, next) {
    try {
      const filters = req.query;
      const reports = await this.reportService.getScheduledReportsForUser(req.user.id, filters);
      res.json(reports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get scheduled report by ID
   */
  async getScheduledReportById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Scheduled report ID is required');
      }
      
      const report = await this.reportService.getScheduledReportById(id);
      res.json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create a scheduled report
   */
  async createScheduledReport(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Scheduled report data is required');
      }
      
      const report = await this.reportService.createScheduledReport(data, req.user.id);
      res.status(201).json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update a scheduled report
   */
  async updateScheduledReport(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Scheduled report ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Scheduled report data is required');
      }
      
      const report = await this.reportService.updateScheduledReport(id, data, req.user.id);
      res.json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete a scheduled report
   */
  async deleteScheduledReport(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Scheduled report ID is required');
      }
      
      const result = await this.reportService.deleteScheduledReport(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Run scheduled reports
   */
  async runScheduledReports(req, res, next) {
    try {
      const result = await this.reportService.runScheduledReports();
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new ReportController();
