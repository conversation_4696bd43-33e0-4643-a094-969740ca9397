/**
 * Mock Server for API Tests
 *
 * This file creates a mock server for API testing without requiring a real database connection.
 */

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const jwt = require('jsonwebtoken');

// Create mock server
const createMockServer = () => {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));

  // Mock data
  const mockData = {
    connectors: [
      {
        id: 'connector-1',
        name: 'AWS Security Hub',
        description: 'AWS Security Hub connector for NovaConnect',
        version: '1.0.0',
        category: 'Security',
        tags: ['aws', 'security', 'cloud'],
        baseUrl: 'https://securityhub.amazonaws.com',
        authentication: {
          type: 'AWS_SIG_V4',
          region: 'us-east-1'
        }
      },
      {
        id: 'connector-2',
        name: 'Azure Security Center',
        description: 'Azure Security Center connector for NovaConnect',
        version: '1.0.0',
        category: 'Security',
        tags: ['azure', 'security', 'cloud'],
        baseUrl: 'https://management.azure.com',
        authentication: {
          type: 'OAUTH2',
          tokenUrl: 'https://login.microsoftonline.com/common/oauth2/token'
        }
      }
    ],
    users: [
      {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        password: '$2b$10$X7o4.KTXb4xLNU/8/7NWmeEHYS.RUcj.M97qQJlzjbBCIGw1uDKXa', // hashed 'Password123!'
        company: 'Test Company'
      }
    ],
    tokens: []
  };

  // Root endpoint
  app.get('/', (req, res) => {
    res.status(200).json({
      message: 'NovaFuse API Superstore',
      version: '1.0.0',
      components: {
        novaConnect: 'active',
        novaMarketplace: 'active'
      }
    });
  });

  // Marketplace API health check
  app.get('/api/marketplace', (req, res) => {
    res.status(200).json({
      message: 'NovaMarketplace API is operational',
      categories: ['Security', 'Compliance', 'Privacy', 'Risk', 'ESG']
    });
  });

  // Connect API health check
  app.get('/api/connect', (req, res) => {
    res.status(200).json({
      message: 'NovaConnect API is operational',
      status: 'active',
      features: ['Universal API Connector', 'Cross-Framework Mapping', 'Evidence Collection'],
      endpoints: ['/connectors', '/mappings', '/evidence']
    });
  });

  // Connector endpoints
  app.get('/api/connect/connectors', (req, res) => {
    res.status(200).json(mockData.connectors);
  });

  app.post('/api/connect/connectors', (req, res) => {
    const { name, description, version, category } = req.body;

    // Validate required fields
    if (!name || !description || !version || !category) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create new connector
    const newConnector = {
      id: `connector-${Date.now()}`,
      ...req.body
    };

    // Add to mock data
    mockData.connectors.push(newConnector);

    res.status(201).json(newConnector);
  });

  app.get('/api/connect/connectors/:id', (req, res) => {
    const connector = mockData.connectors.find(c => c.id === req.params.id);

    if (!connector) {
      return res.status(404).json({ error: 'Connector not found' });
    }

    res.status(200).json(connector);
  });

  app.put('/api/connect/connectors/:id', (req, res) => {
    const index = mockData.connectors.findIndex(c => c.id === req.params.id);

    if (index === -1) {
      return res.status(404).json({ error: 'Connector not found' });
    }

    // Update connector
    mockData.connectors[index] = {
      ...mockData.connectors[index],
      ...req.body,
      id: req.params.id // Ensure ID doesn't change
    };

    res.status(200).json(mockData.connectors[index]);
  });

  app.delete('/api/connect/connectors/:id', (req, res) => {
    const index = mockData.connectors.findIndex(c => c.id === req.params.id);

    if (index === -1) {
      return res.status(404).json({ error: 'Connector not found' });
    }

    // Remove connector
    mockData.connectors.splice(index, 1);

    res.status(200).json({ message: 'Connector deleted successfully' });
  });

  // Authentication endpoints
  app.post('/api/auth/register', (req, res) => {
    const { email, firstName, lastName, password, company } = req.body;

    // Validate required fields
    if (!email || !firstName || !lastName || !password) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Check if email already exists
    if (mockData.users.some(u => u.email === email)) {
      return res.status(409).json({ error: 'Email already registered' });
    }

    // Create new user with hashed password (in a real app)
    // For testing, we'll just store it as is
    const newUser = {
      id: `user-${Date.now()}`,
      email,
      firstName,
      lastName,
      password, // In a real app, this would be properly hashed
      company
    };

    // Add to mock data
    mockData.users.push(newUser);

    // Return user without password
    const { password: _, ...userWithoutPassword } = newUser;
    res.status(201).json(userWithoutPassword);
  });

  app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;

    // Find user
    const user = mockData.users.find(u => u.email === email);

    // For testing purposes, we'll accept any password for the test user
    // In a real app, we would properly compare hashed passwords
    const isValidPassword = user && (user.email === '<EMAIL>' || password === user.password);

    if (!user || !isValidPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate token
    const token = jwt.sign(
      { userId: user.id, email: user.email },
      'mock-secret-key',
      { expiresIn: '1h' }
    );

    // Store token
    mockData.tokens.push(token);

    // Return user without password and token
    const { password: _, ...userWithoutPassword } = user;
    res.status(200).json({
      token,
      user: userWithoutPassword
    });
  });

  app.get('/api/auth/me', (req, res) => {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];

    // Check if token exists
    if (!mockData.tokens.includes(token)) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, 'mock-secret-key');

      // Find user
      const user = mockData.users.find(u => u.id === decoded.userId);

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      res.status(200).json(userWithoutPassword);
    } catch (error) {
      return res.status(401).json({ error: 'Invalid token' });
    }
  });

  app.post('/api/auth/logout', (req, res) => {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];

    // Remove token
    const tokenIndex = mockData.tokens.indexOf(token);
    if (tokenIndex !== -1) {
      mockData.tokens.splice(tokenIndex, 1);
    }

    res.status(200).json({ message: 'Logged out successfully' });
  });

  return app;
};

module.exports = createMockServer;
