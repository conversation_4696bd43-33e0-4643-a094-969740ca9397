/**
 * GDPR Compliance Tests - Data Subject Rights
 * 
 * These tests verify that NovaTrack supports GDPR data subject rights requirements
 */

const { describe, it, beforeEach, afterEach, expect } = require('@jest/globals');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Import the TrackingManager mock
const { TrackingManager } = require('../../mocks/tracking-manager.mock');

// Import test data generator
const { generateRequirement, generateActivity } = require('../../data/novatrack-test-data');

describe('GDPR Compliance - Data Subject Rights', () => {
  let trackingManager;
  let tempDir;
  
  beforeEach(() => {
    // Create a temporary directory for test data
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'gdpr-compliance-test-'));
    
    // Initialize the TrackingManager with the temporary directory
    trackingManager = new TrackingManager(tempDir);
  });
  
  afterEach(() => {
    // Clean up the temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  describe('Right to Access (Article 15)', () => {
    it('should allow retrieval of all data related to a data subject', () => {
      // Create requirements and activities for a data subject
      const dataSubject = '<EMAIL>';
      
      // Create requirements assigned to the data subject
      const requirement1 = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement 1',
        framework: 'GDPR',
        assigned_to: dataSubject
      }));
      
      const requirement2 = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement 2',
        framework: 'GDPR',
        assigned_to: dataSubject
      }));
      
      // Create activities assigned to the data subject
      const activity1 = trackingManager.create_activity(generateActivity({
        name: 'GDPR Activity 1',
        requirement_id: requirement1.id,
        assigned_to: dataSubject
      }));
      
      const activity2 = trackingManager.create_activity(generateActivity({
        name: 'GDPR Activity 2',
        requirement_id: requirement2.id,
        assigned_to: dataSubject
      }));
      
      // Create data for another data subject (should not be included in results)
      const otherRequirement = trackingManager.create_requirement(generateRequirement({
        name: 'Other Requirement',
        framework: 'GDPR',
        assigned_to: '<EMAIL>'
      }));
      
      const otherActivity = trackingManager.create_activity(generateActivity({
        name: 'Other Activity',
        requirement_id: otherRequirement.id,
        assigned_to: '<EMAIL>'
      }));
      
      // Get all data for the data subject
      const dataSubjectRequirements = Object.values(trackingManager.requirements)
        .filter(req => req.assigned_to === dataSubject);
      
      const dataSubjectActivities = Object.values(trackingManager.activities)
        .filter(act => act.assigned_to === dataSubject);
      
      // Verify that all data for the data subject is retrieved
      expect(dataSubjectRequirements.length).toBe(2);
      expect(dataSubjectRequirements).toContainEqual(requirement1);
      expect(dataSubjectRequirements).toContainEqual(requirement2);
      
      expect(dataSubjectActivities.length).toBe(2);
      expect(dataSubjectActivities).toContainEqual(activity1);
      expect(dataSubjectActivities).toContainEqual(activity2);
      
      // Verify that data for other data subjects is not included
      expect(dataSubjectRequirements).not.toContainEqual(otherRequirement);
      expect(dataSubjectActivities).not.toContainEqual(otherActivity);
    });
  });
  
  describe('Right to Rectification (Article 16)', () => {
    it('should allow correction of inaccurate personal data', () => {
      // Create a requirement with incorrect data
      const dataSubject = '<EMAIL>';
      
      const requirement = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement',
        framework: 'GDPR',
        assigned_to: dataSubject,
        description: 'Requirement with incorrect data'
      }));
      
      // Update the requirement with corrected data
      const updatedRequirement = trackingManager.update_requirement(requirement.id, {
        description: 'Requirement with corrected data'
      });
      
      // Verify that the data was corrected
      expect(updatedRequirement.description).toBe('Requirement with corrected data');
      
      // Retrieve the requirement to verify the correction was persisted
      const retrievedRequirement = trackingManager.get_requirement(requirement.id);
      expect(retrievedRequirement.description).toBe('Requirement with corrected data');
    });
    
    it('should allow correction of inaccurate personal data in activities', () => {
      // Create an activity with incorrect data
      const dataSubject = '<EMAIL>';
      
      const activity = trackingManager.create_activity(generateActivity({
        name: 'GDPR Activity',
        assigned_to: dataSubject,
        notes: 'Activity with incorrect data'
      }));
      
      // Update the activity with corrected data
      const updatedActivity = trackingManager.update_activity(activity.id, {
        notes: 'Activity with corrected data'
      });
      
      // Verify that the data was corrected
      expect(updatedActivity.notes).toBe('Activity with corrected data');
      
      // Retrieve the activity to verify the correction was persisted
      const retrievedActivity = trackingManager.get_activity(activity.id);
      expect(retrievedActivity.notes).toBe('Activity with corrected data');
    });
  });
  
  describe('Right to Erasure (Article 17)', () => {
    it('should allow deletion of personal data', () => {
      // Create a requirement with personal data
      const dataSubject = '<EMAIL>';
      
      const requirement = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement',
        framework: 'GDPR',
        assigned_to: dataSubject
      }));
      
      // Create an activity with personal data
      const activity = trackingManager.create_activity(generateActivity({
        name: 'GDPR Activity',
        requirement_id: requirement.id,
        assigned_to: dataSubject
      }));
      
      // Delete the requirement and activity
      trackingManager.delete_requirement(requirement.id);
      trackingManager.delete_activity(activity.id);
      
      // Verify that the data was deleted
      expect(() => trackingManager.get_requirement(requirement.id)).toThrow();
      expect(() => trackingManager.get_activity(activity.id)).toThrow();
      
      // Verify that the data was deleted from disk
      const requirementFilePath = path.join(tempDir, `${requirement.id}.json`);
      const activityFilePath = path.join(tempDir, `${activity.id}.json`);
      
      expect(fs.existsSync(requirementFilePath)).toBe(false);
      expect(fs.existsSync(activityFilePath)).toBe(false);
    });
  });
  
  describe('Right to Restriction of Processing (Article 18)', () => {
    it('should allow restriction of processing by marking data as restricted', () => {
      // Create a requirement
      const dataSubject = '<EMAIL>';
      
      const requirement = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement',
        framework: 'GDPR',
        assigned_to: dataSubject,
        status: 'in_progress'
      }));
      
      // Restrict processing by updating the status to 'deferred'
      const updatedRequirement = trackingManager.update_requirement(requirement.id, {
        status: 'deferred',
        tags: [...requirement.tags, 'processing_restricted']
      });
      
      // Verify that the requirement was marked as restricted
      expect(updatedRequirement.status).toBe('deferred');
      expect(updatedRequirement.tags).toContain('processing_restricted');
    });
  });
  
  describe('Right to Data Portability (Article 20)', () => {
    it('should allow export of personal data in a structured format', () => {
      // Create requirements and activities for a data subject
      const dataSubject = '<EMAIL>';
      
      const requirement1 = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement 1',
        framework: 'GDPR',
        assigned_to: dataSubject
      }));
      
      const requirement2 = trackingManager.create_requirement(generateRequirement({
        name: 'GDPR Requirement 2',
        framework: 'GDPR',
        assigned_to: dataSubject
      }));
      
      const activity1 = trackingManager.create_activity(generateActivity({
        name: 'GDPR Activity 1',
        requirement_id: requirement1.id,
        assigned_to: dataSubject
      }));
      
      const activity2 = trackingManager.create_activity(generateActivity({
        name: 'GDPR Activity 2',
        requirement_id: requirement2.id,
        assigned_to: dataSubject
      }));
      
      // Get all data for the data subject
      const dataSubjectRequirements = Object.values(trackingManager.requirements)
        .filter(req => req.assigned_to === dataSubject);
      
      const dataSubjectActivities = Object.values(trackingManager.activities)
        .filter(act => act.assigned_to === dataSubject);
      
      // Create a structured export of the data
      const exportData = {
        requirements: dataSubjectRequirements,
        activities: dataSubjectActivities
      };
      
      // Convert to JSON (simulating export)
      const exportJson = JSON.stringify(exportData, null, 2);
      
      // Verify that the export contains all the data
      const parsedExport = JSON.parse(exportJson);
      
      expect(parsedExport.requirements.length).toBe(2);
      expect(parsedExport.requirements[0].id).toBe(requirement1.id);
      expect(parsedExport.requirements[1].id).toBe(requirement2.id);
      
      expect(parsedExport.activities.length).toBe(2);
      expect(parsedExport.activities[0].id).toBe(activity1.id);
      expect(parsedExport.activities[1].id).toBe(activity2.id);
    });
  });
});
