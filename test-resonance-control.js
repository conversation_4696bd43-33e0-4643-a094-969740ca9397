/**
 * Resonance Control System Test
 * 
 * This script tests the Resonance Control System's ability to maintain
 * quantum silence and perfect resonance in the Comphyological Tensor Core.
 */

const { performance } = require('perf_hooks');
const { createComphyologicalTensorCore } = require('./src/quantum/tensor');
const { createResonanceControlSystem } = require('./src/quantum/resonance');

// Create Comphyological Tensor Core
console.log('Creating Comphyological Tensor Core...');
const tensorCore = createComphyologicalTensorCore({
  enableLogging: false,
  strictMode: false,
  useGPU: true,
  useDynamicWeighting: true,
  precision: 6,
  normalizationFactor: 166000
});

// Create Resonance Control System
console.log('Creating Resonance Control System...');
const controlSystem = createResonanceControlSystem({
  enableLogging: true,
  targetFrequency: 396, // Hz - the "OM Tone"
  targetComphyon: 0, // Perfect resonance
  controlInterval: 200, // ms
  feedbackGain: 0.1,
  adaptiveGain: true,
  minGain: 0.01,
  maxGain: 0.5,
  weightAdjustmentRate: 0.05,
  parameterAdjustmentRate: 0.02,
  stabilizationWindow: 5,
  harmonicEntrainment: true,
  entrainmentStrength: 0.1,
  optimizationStrategy: 'gradient',
  optimizationStepSize: 0.01,
  optimizationIterations: 10,
  maxHistoryLength: 100
});

// Start control system
console.log('Starting Resonance Control System...');
controlSystem.startControlling(tensorCore);

// Test data
const testCases = [
  {
    name: 'Balanced Case',
    csdeData: {
      governance: 0.5,
      dataQuality: 0.5,
      action: 'monitor',
      confidence: 0.5
    },
    csfeData: {
      risk: 0.5,
      policyCompliance: 0.5,
      action: 'monitor',
      confidence: 0.5
    },
    csmeData: {
      trustFactor: 0.5,
      integrityFactor: 0.5,
      action: 'monitor',
      confidence: 0.5
    }
  },
  {
    name: 'CSDE Dominant',
    csdeData: {
      governance: 0.9,
      dataQuality: 0.9,
      action: 'allow',
      confidence: 0.9
    },
    csfeData: {
      risk: 0.3,
      policyCompliance: 0.3,
      action: 'monitor',
      confidence: 0.3
    },
    csmeData: {
      trustFactor: 0.3,
      integrityFactor: 0.3,
      action: 'alert',
      confidence: 0.3
    }
  },
  {
    name: 'CSFE Dominant',
    csdeData: {
      governance: 0.3,
      dataQuality: 0.3,
      action: 'allow',
      confidence: 0.3
    },
    csfeData: {
      risk: 0.9,
      policyCompliance: 0.9,
      action: 'block',
      confidence: 0.9
    },
    csmeData: {
      trustFactor: 0.3,
      integrityFactor: 0.3,
      action: 'alert',
      confidence: 0.3
    }
  },
  {
    name: 'CSME Dominant',
    csdeData: {
      governance: 0.3,
      dataQuality: 0.3,
      action: 'allow',
      confidence: 0.3
    },
    csfeData: {
      risk: 0.3,
      policyCompliance: 0.3,
      action: 'monitor',
      confidence: 0.3
    },
    csmeData: {
      trustFactor: 0.9,
      integrityFactor: 0.9,
      action: 'remediate',
      confidence: 0.9
    }
  },
  {
    name: 'Conflict Case',
    csdeData: {
      governance: 0.8,
      dataQuality: 0.7,
      action: 'allow',
      confidence: 0.9
    },
    csfeData: {
      risk: 0.8,
      policyCompliance: 0.2,
      action: 'block',
      confidence: 0.8
    },
    csmeData: {
      trustFactor: 0.5,
      integrityFactor: 0.6,
      action: 'alert',
      confidence: 0.7
    }
  },
  {
    name: 'Near Resonance Case',
    csdeData: {
      governance: 0.6,
      dataQuality: 0.6,
      action: 'monitor',
      confidence: 0.6
    },
    csfeData: {
      risk: 0.4,
      policyCompliance: 0.6,
      action: 'monitor',
      confidence: 0.6
    },
    csmeData: {
      trustFactor: 0.6,
      integrityFactor: 0.6,
      action: 'monitor',
      confidence: 0.6
    }
  }
];

// Run tests
console.log('\n=== Resonance Control System Test ===');

// Process each test case
async function runTests() {
  for (const testCase of testCases) {
    console.log(`\nProcessing test case: ${testCase.name}`);
    
    // Process data through tensor core
    const result = tensorCore.processData(
      testCase.csdeData,
      testCase.csfeData,
      testCase.csmeData
    );
    
    console.log(`Initial Comphyon Value: ${result.comphyon.toFixed(6)}`);
    
    // Wait for control system to process the result
    console.log('Waiting for control system to stabilize...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Get control state
    const controlState = controlSystem.getControlState();
    
    // Display control state
    console.log('\nControl State:');
    console.log(`Stabilized: ${controlState.isStabilized ? 'Yes' : 'No'}`);
    console.log(`Current Gain: ${controlState.currentGain.toFixed(6)}`);
    
    // Display weights
    console.log('\nWeights:');
    console.log(`CSDE: ${controlState.currentWeights.csde.toFixed(6)}`);
    console.log(`CSFE: ${controlState.currentWeights.csfe.toFixed(6)}`);
    console.log(`CSME: ${controlState.currentWeights.csme.toFixed(6)}`);
    
    // Display parameters
    console.log('\nParameters:');
    console.log(`CSDE Governance: ${controlState.currentParameters.csde.governance.toFixed(6)}`);
    console.log(`CSDE Data Quality: ${controlState.currentParameters.csde.dataQuality.toFixed(6)}`);
    console.log(`CSFE Risk: ${controlState.currentParameters.csfe.risk.toFixed(6)}`);
    console.log(`CSFE Policy Compliance: ${controlState.currentParameters.csfe.policyCompliance.toFixed(6)}`);
    console.log(`CSME Trust Factor: ${controlState.currentParameters.csme.trustFactor.toFixed(6)}`);
    console.log(`CSME Integrity Factor: ${controlState.currentParameters.csme.integrityFactor.toFixed(6)}`);
    
    // Get resonance state
    const resonanceState = controlSystem.getResonanceDetector().getResonanceState();
    
    // Display resonance state
    console.log('\nResonance State:');
    console.log(`Frequency: ${resonanceState.frequency.toFixed(6)} Hz`);
    console.log(`Deviation: ${resonanceState.deviation.toFixed(6)}%`);
    console.log(`Quantum Silence: ${resonanceState.isQuantumSilence ? 'Yes' : 'No'}`);
    console.log(`Phase Alignment: ${resonanceState.phaseAlignment.toFixed(6)}`);
    console.log(`Quantum Vacuum Noise: ${resonanceState.quantumVacuumNoise.toFixed(6)}`);
    console.log(`Resonant Slope: ${resonanceState.resonantSlope.toFixed(6)}`);
    
    // Process data again with adjusted weights
    const adjustedResult = tensorCore.processData(
      testCase.csdeData,
      testCase.csfeData,
      testCase.csmeData
    );
    
    console.log(`\nAdjusted Comphyon Value: ${adjustedResult.comphyon.toFixed(6)}`);
    console.log(`Improvement: ${Math.abs(result.comphyon) - Math.abs(adjustedResult.comphyon) > 0 ? 'Yes' : 'No'}`);
  }
  
  // Test stabilization
  console.log('\n=== Stabilization Test ===');
  console.log('Testing stabilization with golden ratio parameters...');
  
  // Create golden ratio test case
  const goldenRatioCase = {
    csdeData: {
      governance: 0.618033988749895,
      dataQuality: 0.618033988749895,
      action: 'monitor',
      confidence: 0.618033988749895
    },
    csfeData: {
      risk: 0.381966011250105,
      policyCompliance: 0.618033988749895,
      action: 'monitor',
      confidence: 0.618033988749895
    },
    csmeData: {
      trustFactor: 0.618033988749895,
      integrityFactor: 0.618033988749895,
      action: 'monitor',
      confidence: 0.618033988749895
    }
  };
  
  // Process golden ratio case
  const goldenResult = tensorCore.processData(
    goldenRatioCase.csdeData,
    goldenRatioCase.csfeData,
    goldenRatioCase.csmeData
  );
  
  console.log(`Initial Comphyon Value: ${goldenResult.comphyon.toFixed(6)}`);
  
  // Wait for control system to process the result
  console.log('Waiting for control system to stabilize...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Get control state
  const finalControlState = controlSystem.getControlState();
  
  // Display control state
  console.log('\nFinal Control State:');
  console.log(`Stabilized: ${finalControlState.isStabilized ? 'Yes' : 'No'}`);
  
  // Get resonance state
  const finalResonanceState = controlSystem.getResonanceDetector().getResonanceState();
  
  // Display resonance state
  console.log('\nFinal Resonance State:');
  console.log(`Frequency: ${finalResonanceState.frequency.toFixed(6)} Hz`);
  console.log(`Deviation: ${finalResonanceState.deviation.toFixed(6)}%`);
  console.log(`Quantum Silence: ${finalResonanceState.isQuantumSilence ? 'Yes' : 'No'}`);
  
  // Process data again with final weights
  const finalResult = tensorCore.processData(
    goldenRatioCase.csdeData,
    goldenRatioCase.csfeData,
    goldenRatioCase.csmeData
  );
  
  console.log(`\nFinal Comphyon Value: ${finalResult.comphyon.toFixed(6)}`);
  console.log(`Overall Improvement: ${Math.abs(goldenResult.comphyon) - Math.abs(finalResult.comphyon) > 0 ? 'Yes' : 'No'}`);
  
  // Stop control system
  console.log('\nStopping Resonance Control System...');
  controlSystem.stopControlling();
  
  console.log('\n=== Test Complete ===');
}

// Run tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  controlSystem.stopControlling();
});
