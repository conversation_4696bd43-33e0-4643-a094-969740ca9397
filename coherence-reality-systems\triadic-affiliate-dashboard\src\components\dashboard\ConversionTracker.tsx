import { motion } from 'framer-motion'
import { useState, useEffect } from 'react'
import { useWebSocket } from '@/lib/useWebSocket'
import { auth } from '@clerk/nextjs'

interface Conversion {
  id: string
  productId: string
  networkId: string
  revenue: number
  status: 'pending' | 'completed' | 'failed'
  createdAt: string
  triadicMetrics: {
    psi: number
    phi: number
    kappa: number
  }
}

export function ConversionTracker() {
  const { userId } = auth()
  const { socket, data, connected } = useWebSocket(userId || '')
  const [conversions, setConversions] = useState<Conversion[]>([])
  const [selectedConversion, setSelectedConversion] = useState<Conversion | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchConversions = async () => {
      try {
        const response = await fetch('/api/conversions')
        const data = await response.json()
        setConversions(data)
      } catch (error) {
        console.error('Error fetching conversions:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchConversions()
  }, [])

  useEffect(() => {
    if (data?.type === 'conversion' && data.data) {
      setConversions(prev => {
        const existingIndex = prev.findIndex(c => c.id === data.data.id)
        if (existingIndex !== -1) {
          return [...prev.slice(0, existingIndex), data.data, ...prev.slice(existingIndex + 1)]
        }
        return [data.data, ...prev]
      })
    }
  }, [data])

  useEffect(() => {
    if (socket) {
      socket.on('data', (data: { type: 'conversion'; data: Conversion }) => {
        if (data.type === 'conversion') {
          setConversions(prev => {
            const existingIndex = prev.findIndex(c => c.id === data.data.id)
            if (existingIndex !== -1) {
              return [...prev.slice(0, existingIndex), data.data, ...prev.slice(existingIndex + 1)]
            }
            return [data.data, ...prev]
          })
        }
      })

      return () => {
        socket.off('data')
      }
    }
  }, [socket])

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map(index => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 rounded-lg border bg-gray-50 animate-pulse"
          >
            <div className="h-8 w-32 bg-gray-200 rounded mb-4" />
            <div className="h-4 w-24 bg-gray-200 rounded" />
          </motion.div>
        ))}
      </div>
    )
  }

  const calculateConversionRate = () => {
    const completed = conversions.filter(c => c.status === 'completed').length
    const total = conversions.length
    return total > 0 ? (completed / total) * 100 : 0
  }

  const calculateAverageRevenue = () => {
    const totalRevenue = conversions.reduce((sum, c) => sum + c.revenue, 0)
    return conversions.length > 0 ? totalRevenue / conversions.length : 0
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Conversion Tracker</h2>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Conversion Rate:</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
              {calculateConversionRate().toFixed(1)}%
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Avg Revenue:</span>
            <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
              ${calculateAverageRevenue().toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {conversions.map((conversion) => (
          <motion.div
            key={conversion.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 rounded-lg border cursor-pointer hover:border-purple-500 transition-all ${
              selectedConversion?.id === conversion.id ? 'border-purple-500' : 'border-white/10'
            }`}
            onClick={() => setSelectedConversion(conversion)}
          >
            <div className="flex items-center space-x-4">
              <div className={`w-4 h-4 rounded-full ${
                conversion.status === 'completed' ? 'bg-green-500' :
                conversion.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
              }`} />
              <div>
                <h3 className="font-semibold">${conversion.revenue.toFixed(2)}</h3>
                <p className="text-sm text-gray-400">
                  {new Date(conversion.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex space-x-2">
                <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                  PSI: {conversion.triadicMetrics.psi.toFixed(1)}%
                </span>
                <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                  PHI: {conversion.triadicMetrics.phi.toFixed(1)}%
                </span>
                <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                  KAPPA: {conversion.triadicMetrics.kappa.toFixed(1)}%
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {selectedConversion && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
        >
          <h3 className="text-lg font-semibold mb-4">Conversion Details</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className={`w-8 h-8 rounded-full ${
                selectedConversion.status === 'completed' ? 'bg-green-500' :
                selectedConversion.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
              }`}>
                <span className="text-white">{selectedConversion.status[0].toUpperCase()}</span>
              </div>
              <div>
                <h4 className="font-semibold">${selectedConversion.revenue.toFixed(2)}</h4>
                <div className="mt-2 space-y-2">
                  <p className="text-sm text-gray-400">
                    Product: {selectedConversion.productId}
                  </p>
                  <p className="text-sm text-gray-400">
                    Network: {selectedConversion.networkId}
                    Status: {selectedConversion.status.toUpperCase()}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <button
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                onClick={() => setSelectedConversion(null)}
              >
                Close Details
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
