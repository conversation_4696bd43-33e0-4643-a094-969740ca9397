const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/disclosures/routes');
const models = require('../../../../apis/esg/disclosures/models');

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/disclosures', router);

// Generate a large dataset for performance testing
const generateLargeDisclosureDataset = (count) => {
  const disclosures = [];
  for (let i = 0; i < count; i++) {
    disclosures.push({
      id: `esg-d-${i.toString().padStart(8, '0')}`,
      title: `Test Disclosure ${i}`,
      description: `Description for test disclosure ${i}`,
      category: i % 3 === 0 ? 'environmental' : i % 3 === 1 ? 'social' : 'governance',
      type: i % 2 === 0 ? 'regulatory' : 'voluntary',
      framework: i % 3 === 0 ? 'GRI' : i % 3 === 1 ? 'SASB' : 'TCFD',
      frameworkReference: `${i % 3 === 0 ? 'GRI' : i % 3 === 1 ? 'SASB' : 'TCFD'}-${i % 10}`,
      status: i % 4 === 0 ? 'draft' : i % 4 === 1 ? 'in-review' : i % 4 === 2 ? 'approved' : 'published',
      period: {
        startDate: '2022-01-01',
        endDate: '2022-12-31'
      },
      publishDate: i % 4 === 3 ? '2023-03-15' : null,
      content: {
        summary: `Summary for disclosure ${i}`,
        metrics: generateMetrics(i, 2 + (i % 5)), // Each disclosure has 2-6 metrics
        narrative: `Narrative for disclosure ${i}...`,
        attachments: generateAttachments(i, i % 3) // Each disclosure has 0-2 attachments
      },
      owner: `Team ${i % 10}`,
      contributors: [`Contributor ${i % 5}`, `Contributor ${(i + 1) % 5}`],
      reviewers: i % 3 === 0 ? [`Reviewer ${i % 3}`] : [],
      relatedTargets: [`esg-t-${i.toString().padStart(8, '0')}`],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return disclosures;
};

// Generate metrics for a disclosure
const generateMetrics = (disclosureIndex, count) => {
  const metrics = [];
  for (let i = 0; i < count; i++) {
    const value = 1000 + (i * 100);
    const previousValue = value * (1 + (Math.random() * 0.2 - 0.1)); // +/- 10%
    const change = ((value - previousValue) / previousValue) * 100;
    
    metrics.push({
      name: `Metric ${i} of Disclosure ${disclosureIndex}`,
      value,
      unit: i % 4 === 0 ? 'metric-tons' : i % 4 === 1 ? 'percentage' : i % 4 === 2 ? 'count' : 'currency',
      previousValue,
      change
    });
  }
  return metrics;
};

// Generate attachments for a disclosure
const generateAttachments = (disclosureIndex, count) => {
  const attachments = [];
  for (let i = 0; i < count; i++) {
    attachments.push({
      id: `att-${disclosureIndex}-${i}`,
      name: `Attachment ${i} of Disclosure ${disclosureIndex}`,
      type: i % 3 === 0 ? 'pdf' : i % 3 === 1 ? 'xlsx' : 'docx',
      url: `https://example.com/reports/disclosure-${disclosureIndex}-attachment-${i}.${i % 3 === 0 ? 'pdf' : i % 3 === 1 ? 'xlsx' : 'docx'}`
    });
  }
  return attachments;
};

// Generate disclosure templates
const generateTemplates = (count) => {
  const templates = [];
  for (let i = 0; i < count; i++) {
    templates.push({
      id: `template-${i.toString().padStart(5, '0')}`,
      name: `Disclosure Template ${i}`,
      description: `Description for disclosure template ${i}`,
      category: i % 3 === 0 ? 'environmental' : i % 3 === 1 ? 'social' : 'governance',
      framework: i % 3 === 0 ? 'GRI' : i % 3 === 1 ? 'SASB' : 'TCFD',
      frameworkReference: `${i % 3 === 0 ? 'GRI' : i % 3 === 1 ? 'SASB' : 'TCFD'}-${i % 10}`,
      structure: {
        sections: generateSections(i, 3 + (i % 3)), // Each template has 3-5 sections
        metrics: generateTemplateMetrics(i, 2 + (i % 4)) // Each template has 2-5 metrics
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return templates;
};

// Generate sections for a template
const generateSections = (templateIndex, count) => {
  const sections = [];
  for (let i = 0; i < count; i++) {
    sections.push({
      title: `Section ${i} of Template ${templateIndex}`,
      description: `Description for section ${i} of template ${templateIndex}`,
      required: i < 2, // First two sections are required
      order: i + 1
    });
  }
  return sections;
};

// Generate metrics for a template
const generateTemplateMetrics = (templateIndex, count) => {
  const metrics = [];
  for (let i = 0; i < count; i++) {
    metrics.push({
      name: `Metric ${i} of Template ${templateIndex}`,
      unit: i % 4 === 0 ? 'metric-tons' : i % 4 === 1 ? 'percentage' : i % 4 === 2 ? 'count' : 'currency',
      required: i < 2 // First two metrics are required
    });
  }
  return metrics;
};

// Mock the models with a large dataset
const DISCLOSURE_COUNT = 500;
const TEMPLATE_COUNT = 50;

jest.mock('../../../../apis/esg/disclosures/models', () => {
  const disclosures = generateLargeDisclosureDataset(DISCLOSURE_COUNT);
  const templates = generateTemplates(TEMPLATE_COUNT);
  return {
    esgDisclosures: disclosures,
    disclosureTemplates: templates
  };
});

describe('ESG Disclosures API Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);

  describe('GET /governance/esg/disclosures', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/disclosures?page=1&limit=20');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(20);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Pagination response time: ${responseTime}ms`);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/disclosures?category=environmental&status=published');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Filtering response time: ${responseTime}ms`);
    });

    it('should handle multiple filters efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/disclosures?category=environmental&status=published&framework=GRI');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Multiple filters response time: ${responseTime}ms`);
    });
  });

  describe('GET /governance/esg/disclosures/:id', () => {
    it('should retrieve a specific disclosure efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/disclosures/esg-d-00000050');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-d-00000050');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get disclosure response time: ${responseTime}ms`);
    });
  });

  describe('GET /governance/esg/disclosures/:id/attachments', () => {
    it('should retrieve attachments for a disclosure efficiently', async () => {
      // Find a disclosure with attachments
      const disclosureWithAttachments = models.esgDisclosures.find(d => d.content.attachments.length > 0);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/governance/esg/disclosures/${disclosureWithAttachments.id}/attachments`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.length).toBeGreaterThan(0);
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get attachments response time: ${responseTime}ms`);
    });
  });

  describe('GET /governance/esg/disclosures/templates', () => {
    it('should retrieve disclosure templates efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/disclosures/templates');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.length).toBe(TEMPLATE_COUNT);
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Get templates response time: ${responseTime}ms`);
    });
  });

  describe('POST /governance/esg/disclosures', () => {
    it('should create a new disclosure efficiently', async () => {
      const newDisclosure = {
        title: 'Performance Test Disclosure',
        description: 'Disclosure for performance testing',
        category: 'environmental',
        type: 'voluntary',
        framework: 'GRI',
        frameworkReference: 'GRI-TEST',
        status: 'draft',
        period: {
          startDate: '2022-01-01',
          endDate: '2022-12-31'
        },
        content: {
          summary: 'Summary for performance test disclosure',
          metrics: [
            {
              name: 'Test Metric',
              value: 1000,
              unit: 'count',
              previousValue: 1100,
              change: -9.1
            }
          ],
          narrative: 'Narrative for performance test disclosure...'
        },
        owner: 'Test Team'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/governance/esg/disclosures')
        .send(newDisclosure);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create disclosure response time: ${responseTime}ms`);
    });
  });

  describe('POST /governance/esg/disclosures/:id/attachments', () => {
    it('should add a new attachment efficiently', async () => {
      const newAttachment = {
        name: 'Performance Test Attachment',
        type: 'pdf',
        url: 'https://example.com/reports/performance-test.pdf'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/governance/esg/disclosures/esg-d-00000025/attachments')
        .send(newAttachment);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Add attachment response time: ${responseTime}ms`);
    });
  });

  describe('POST /governance/esg/disclosures/templates', () => {
    it('should create a new template efficiently', async () => {
      const newTemplate = {
        name: 'Performance Test Template',
        description: 'Template for performance testing',
        category: 'social',
        framework: 'SASB',
        frameworkReference: 'SASB-TEST',
        structure: {
          sections: [
            {
              title: 'Test Section 1',
              description: 'Description for test section 1',
              required: true,
              order: 1
            },
            {
              title: 'Test Section 2',
              description: 'Description for test section 2',
              required: false,
              order: 2
            }
          ],
          metrics: [
            {
              name: 'Test Metric 1',
              unit: 'percentage',
              required: true
            },
            {
              name: 'Test Metric 2',
              unit: 'count',
              required: false
            }
          ]
        }
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/governance/esg/disclosures/templates')
        .send(newTemplate);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create template response time: ${responseTime}ms`);
    });
  });

  describe('Concurrent requests', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const startTime = Date.now();
      
      // Make 10 concurrent requests
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(request(app).get(`/governance/esg/disclosures?page=${i+1}&limit=10`));
      }
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });
      
      // Total response time for 10 concurrent requests should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Concurrent requests response time: ${totalResponseTime}ms`);
    });

    it('should handle concurrent read and write operations efficiently', async () => {
      const startTime = Date.now();
      
      // Create a mix of read and write operations
      const requests = [];
      
      // Read operations
      requests.push(request(app).get('/governance/esg/disclosures?page=1&limit=10'));
      requests.push(request(app).get('/governance/esg/disclosures/esg-d-00000005'));
      requests.push(request(app).get('/governance/esg/disclosures/esg-d-00000005/attachments'));
      requests.push(request(app).get('/governance/esg/disclosures/templates'));
      
      // Write operations
      const newDisclosure = {
        title: 'Concurrent Test Disclosure',
        description: 'Disclosure for concurrent testing',
        category: 'environmental',
        type: 'voluntary',
        framework: 'GRI',
        frameworkReference: 'GRI-TEST',
        status: 'draft',
        period: {
          startDate: '2022-01-01',
          endDate: '2022-12-31'
        },
        content: {
          summary: 'Summary for concurrent test disclosure',
          metrics: [
            {
              name: 'Test Metric',
              value: 1000,
              unit: 'count',
              previousValue: 1100,
              change: -9.1
            }
          ],
          narrative: 'Narrative for concurrent test disclosure...'
        },
        owner: 'Test Team'
      };
      
      const newAttachment = {
        name: 'Concurrent Test Attachment',
        type: 'pdf',
        url: 'https://example.com/reports/concurrent-test.pdf'
      };
      
      requests.push(request(app).post('/governance/esg/disclosures').send(newDisclosure));
      requests.push(request(app).post('/governance/esg/disclosures/esg-d-00000010/attachments').send(newAttachment));
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200) || expect(response.status).toBe(201);
      });
      
      // Total response time for mixed operations should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Mixed operations response time: ${totalResponseTime}ms`);
    });
  });

  describe('Load testing', () => {
    it('should handle a large number of sequential requests', async () => {
      const requestCount = 50;
      const startTime = Date.now();
      
      // Make sequential requests
      for (let i = 0; i < requestCount; i++) {
        const response = await request(app).get(`/governance/esg/disclosures?page=1&limit=5`);
        expect(response.status).toBe(200);
      }
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      const averageResponseTime = totalResponseTime / requestCount;
      
      // Average response time should be under 20ms
      expect(averageResponseTime).toBeLessThan(20);
      console.log(`Sequential requests average response time: ${averageResponseTime}ms`);
    });
  });

  describe('Performance with large datasets', () => {
    it('should handle retrieving a disclosure with many metrics efficiently', async () => {
      // Find a disclosure with many metrics
      const disclosureWithManyMetrics = models.esgDisclosures.find(d => d.content.metrics.length > 4);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/governance/esg/disclosures/${disclosureWithManyMetrics.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.content.metrics.length).toBeGreaterThan(4);
      
      // Response time should be under 150ms even with many metrics
      expect(responseTime).toBeLessThan(150);
      console.log(`Disclosure with many metrics response time: ${responseTime}ms`);
    });

    it('should handle retrieving a template with many sections efficiently', async () => {
      // Find a template with many sections
      const templateWithManySections = models.disclosureTemplates.find(t => t.structure.sections.length > 4);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/governance/esg/disclosures/templates/${templateWithManySections.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.structure.sections.length).toBeGreaterThan(4);
      
      // Response time should be under 150ms even with many sections
      expect(responseTime).toBeLessThan(150);
      console.log(`Template with many sections response time: ${responseTime}ms`);
    });
  });
});
