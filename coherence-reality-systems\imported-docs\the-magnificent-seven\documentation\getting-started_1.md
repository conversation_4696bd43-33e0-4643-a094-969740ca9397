# Getting Started with the ComphyonΨᶜ Framework

This guide provides instructions for getting started with the ComphyonΨᶜ Framework.

## Prerequisites

Before you begin, make sure you have the following:

- Python 3.8 or higher
- Git
- pip (Python package manager)

## Installation

### 1. Clone the Repositories

First, clone the ComphyonΨᶜ Framework repositories:

```bash
# Clone the repositories
git clone https://github.com/Dartan1983/comphyology-core.git
git clone https://github.com/Dartan1983/comphyon-meter.git
git clone https://github.com/Dartan1983/comphyon-governor.git
git clone https://github.com/Dartan1983/cognitive-metrology.git
git clone https://github.com/Dartan1983/comphyon-framework.git
```

### 2. Install the Packages

Next, install the ComphyonΨᶜ Meter and ComphyonΨᶜ Governor packages:

```bash
# Install the ComphyonΨᶜ Meter
cd comphyon-meter
pip install -e .

# Install the ComphyonΨᶜ Governor
cd ../comphyon-governor
pip install -e .
```

### 3. Install Additional Dependencies

For visualization and advanced examples, install additional dependencies:

```bash
pip install dash plotly pandas matplotlib
```

## Basic Usage

### 1. Initialize the ComphyonΨᶜ Meter

```python
from comphyon_meter import ComphyonMeter

# Initialize the meter
meter = ComphyonMeter()
```

### 2. Calculate ComphyonΨᶜ Metrics

```python
# Generate tensor data
csde_tensor = [0.75, 0.85, 0.65, 0.90]  # [G, D, A₁, c₁]
csfe_tensor = [0.65, 0.70, 0.80, 0.80]  # [F₁, P, A₂, c₂]
csme_tensor = [0.70, 0.90, 0.60, 0.85]  # [T, I, E, c₃]

# Calculate ComphyonΨᶜ metrics
metrics = meter.calculate(csde_tensor, csfe_tensor, csme_tensor)

# Print the metrics
print(f"ComphyonΨᶜ Velocity: {metrics['velocity']:.4f} Cph-Flux")
print(f"ComphyonΨᶜ Acceleration: {metrics['acceleration']:.4f} Cph")
```

### 3. Initialize the ComphyonΨᶜ Governor

```python
from comphyon_governor import ComphyonGovernor

# Initialize the governor with custom thresholds
governor = ComphyonGovernor(
    thresholds={
        'acceleration': 1.5,  # Trigger control at 1.5 Cph
        'velocity': 60.0      # Trigger control at 60.0 Cph-Flux
    }
)
```

### 4. Apply Control Actions

```python
# Apply control actions if needed
control_actions = governor.regulate(metrics)

# Print the control actions
if control_actions['type'] != 'none':
    print(f"Control action applied: {control_actions['type']}")
    print(f"Reason: {control_actions.get('reason', 'N/A')}")
    print(f"Actions: {len(control_actions['actions'])} actions applied")
else:
    print("No control actions needed.")
```

## Running Examples

The ComphyonΨᶜ Framework includes several examples:

### Basic Examples

```bash
cd comphyon-framework/examples/basic
python simple_integration.py
```

### Advanced Examples

```bash
cd comphyon-framework/examples/advanced
python real_time_monitoring.py
```

### Visualization Examples

```bash
cd comphyon-framework/examples/visualization
python dashboard_example.py
```

## Next Steps

- Read the [Integration Guide](integration-guide.md) for detailed instructions on integrating the ComphyonΨᶜ Framework with existing systems
- Explore the [Examples](../../examples) directory for more examples
- Read the [Architecture](../overview/architecture.md) documentation for a detailed description of the ComphyonΨᶜ Framework architecture
