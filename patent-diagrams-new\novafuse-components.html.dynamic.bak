<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse 13 Universal Components</title>
    <style>

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 800px;
            height: 70px; /* Extended height for adequate space */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Prevents content from spilling out */
        }

        /* Ensure Universal Pattern Elements stay within main container */
        .universal-pattern-elements {
            position: relative;
            width: 650px;
            margin-top: 450px; /* Position it near the bottom of the main container */
            margin-left: 50px; /* Center it within the main container */
            z-index: 1;
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Prevents content from spilling out */
            text-overflow: ellipsis; /* Shows ellipsis for overflowing text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */

        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow-line {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            z-index: 0;
        }

        .arrow-head {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }

        /* SVG Styles */
        svg {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 0;
        }

        /* Equation Styles */
        .equation {
            font-weight: bold;
            font-size: 14px;
            margin-top: 5px;
            text-align: center;
        }
    
    </style>
</head>
<body>
    <h1>FIG. 4: NovaFuse 13 Universal Components</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 580px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">NOVAFUSE 13 UNIVERSAL COMPONENTS - COMPHYOLOGY (Ψᶜ) FRAMEWORK</div>
        </div>

        <h2 style="position: absolute; top: 80px; left: 50%; transform: translateX(-50%); width: 80%;">
            The Immutable Backbone of Intelligent Compliance
        </h2>

        <div class="grid-container" style="position: absolute; top: 130px; left: 50px; width: 650px;">
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">1. NovaCore</div>
                <div class="component-desc">Universal Compliance Testing Framework</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">2. NovaShield</div>
                <div class="component-desc">Universal Vendor Risk Management</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">3. NovaTrack</div>
                <div class="component-desc">Universal Compliance Tracking</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">4. NovaLearn</div>
                <div class="component-desc">Universal Adaptive Learning</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">5. NovaView</div>
                <div class="component-desc">Universal Visualization</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">6. NovaFlowX</div>
                <div class="component-desc">Universal Workflow Automation</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">7. NovaPulse+</div>
                <div class="component-desc">Universal Regulatory Change Management</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">8. NovaProof</div>
                <div class="component-desc">Universal Compliance Evidence</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">9. NovaThink</div>
                <div class="component-desc">Universal Compliance Intelligence</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">10. NovaConnect</div>
                <div class="component-desc">Universal API Connector</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">11. NovaVision</div>
                <div class="component-desc">Universal UI Framework</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;">
                <div class="component-name">12. NovaDNA</div>
                <div class="component-desc">Universal Identity Graph</div>
            </div>
            <div class="component-box" style="overflow: visible; min-height: 50px; min-width: 100px;" style="grid-column: span 2; text-align: center;">
                <div class="component-name">13. NovaStore</div>
                <div class="component-desc">Universal API Marketplace</div>
            </div>
        </div>

        <div class="equation" style="position: absolute; top: 500px; left: 50%; transform: translateX(-50%); width: 80%;">
            Universal Unified Field Theory (UUFT): (A⊗B⊕C)×π10³
        </div>

        <div class="footer-text" style="position: absolute; top: 540px; left: 50%; transform: translateX(-50%); width: 80%;">
            These components unify GRC, AI, and User Empowerment into a cohesive system.
        </div>

        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 10px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
        <div class="inventor-label">Inventor: David Nigel Irvin</div>
</div>
</body>
</html>





