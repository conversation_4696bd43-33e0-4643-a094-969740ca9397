<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>MathML vs Classical Formatting</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .column {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 8px;
        }
        .column h2 {
            margin-top: 0;
            text-align: center;
        }
        .example {
            margin: 20px 0;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 4px;
        }
        .classic {
            font-family: 'Cambria Math', serif;
        }
    </style>
</head>
<body>
    <h1>MathML vs Classical Formatting Comparison</h1>
    
    <div class="comparison">
        <div class="column">
            <h2>MathML Format</h2>
            <div class="example">
                <math xmlns="http://www.w3.org/1998/Math/MathML">
                    <mrow>
                        <mi>T</mi>
                        <mo>=</mo>
                        <mfrac>
                            <mrow>
                                <mi>C</mi>
                                <mo>×</mo>
                                <mi>R</mi>
                                <mo>×</mo>
                                <mi>I</mi>
                            </mrow>
                            <mi>S</mi>
                        </mfrac>
                    </mrow>
                </math>
            </div>
            <p>Advantages:</p>
            <ul>
                <li>Native browser support</li>
                <li>Better accessibility</li>
                <li>More semantic structure</li>
                <li>Works without additional libraries</li>
            </ul>
            <p>Disadvantages:</p>
            <ul>
                <li>More verbose syntax</li>
                <li>Harder to write and edit</li>
                <li>Less readable source code</li>
            </ul>
        </div>
        
        <div class="column">
            <h2>Classical Format</h2>
            <div class="example classic">
                \[ T = \frac{C \times R \times I}{S} \]
            </div>
            <p>Advantages:</p>
            <ul>
                <li>More readable source code</li>
                <li>Easier to write and edit</li>
                <li>Looks more like traditional math notation</li>
                <li>Preferred in academic papers</li>
            </ul>
            <p>Disadvantages:</p>
            <ul>
                <li>Requires MathJax or similar library</li>
                <li>Less semantic structure</li>
                <li>Less accessible to screen readers</li>
            </ul>
        </div>
    </div>

    <h2>When to Use Each:</h2>
    <div class="comparison">
        <div class="column">
            <h3>Use MathML When:</h3>
            <ul>
                <li>Creating web content that needs to be accessible</li>
                <li>Working with assistive technologies</li>
                <li>Building educational websites</li>
                <li>Creating content that needs semantic structure</li>
            </ul>
        </div>
        <div class="column">
            <h3>Use Classical When:</h3>
            <ul>
                <li>Writing academic papers</li>
                <li>Creating mathematical documents</li>
                <li>Working with LaTeX</li>
                <li>Wanting more traditional math notation</li>
            </ul>
        </div>
    </div>
</body>
</html>
