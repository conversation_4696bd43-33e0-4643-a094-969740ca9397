/**
 * Dynamic Weighting Protocol
 * 
 * This module implements the dynamic weighting protocol for the Comphyological Tensor Core.
 * It applies the 18/82 principle to determine the weights of different engines.
 */

const { performance } = require('perf_hooks');

/**
 * DynamicWeightingProtocol class
 * 
 * Implements the dynamic weighting protocol for the Comphyological Tensor Core.
 */
class DynamicWeightingProtocol {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: true,
      defaultWeights: {
        csde: 0.33,
        csfe: 0.33,
        csme: 0.34
      },
      dominanceThreshold: 0.6, // Threshold for engine dominance
      minorWeight: 0.18, // 18% weight for non-dominant engines
      majorWeight: 0.82, // 82% weight for dominant engine
      ...options
    };

    // Initialize weights
    this.weights = { ...this.options.defaultWeights };

    if (this.options.enableLogging) {
      console.log('DynamicWeightingProtocol initialized with options:', {
        dominanceThreshold: this.options.dominanceThreshold,
        minorWeight: this.options.minorWeight,
        majorWeight: this.options.majorWeight
      });
    }
  }

  /**
   * Calculate weights based on engine metrics
   * @param {Object} csdeMetrics - CSDE metrics
   * @param {Object} csfeMetrics - CSFE metrics
   * @param {Object} csmeMetrics - CSME metrics
   * @returns {Object} - Calculated weights
   */
  calculateWeights(csdeMetrics, csfeMetrics, csmeMetrics) {
    const startTime = performance.now();

    // Calculate dominance scores
    const dominanceScores = this._calculateDominanceScores(csdeMetrics, csfeMetrics, csmeMetrics);

    // Determine dominant engine
    const dominantEngine = this._determineDominantEngine(dominanceScores);

    // Apply 18/82 principle
    const weights = this._apply1882Principle(dominantEngine);

    if (this.options.enableLogging) {
      const endTime = performance.now();
      console.log(`Weights calculated in ${endTime - startTime}ms:`, weights);
      console.log(`Dominant engine: ${dominantEngine}`);
    }

    // Update internal weights
    this.weights = weights;

    return {
      weights,
      dominantEngine,
      dominanceScores
    };
  }

  /**
   * Calculate dominance scores for each engine
   * @param {Object} csdeMetrics - CSDE metrics
   * @param {Object} csfeMetrics - CSFE metrics
   * @param {Object} csmeMetrics - CSME metrics
   * @returns {Object} - Dominance scores
   * @private
   */
  _calculateDominanceScores(csdeMetrics, csfeMetrics, csmeMetrics) {
    // Extract relevant metrics
    const csdeScore = this._extractDominanceScore(csdeMetrics, 'csde');
    const csfeScore = this._extractDominanceScore(csfeMetrics, 'csfe');
    const csmeScore = this._extractDominanceScore(csmeMetrics, 'csme');

    // Normalize scores
    const total = csdeScore + csfeScore + csmeScore;
    const normalizedScores = {
      csde: total > 0 ? csdeScore / total : this.options.defaultWeights.csde,
      csfe: total > 0 ? csfeScore / total : this.options.defaultWeights.csfe,
      csme: total > 0 ? csmeScore / total : this.options.defaultWeights.csme
    };

    return normalizedScores;
  }

  /**
   * Extract dominance score from engine metrics
   * @param {Object} metrics - Engine metrics
   * @param {string} domain - Domain name
   * @returns {number} - Dominance score
   * @private
   */
  _extractDominanceScore(metrics, domain) {
    if (!metrics) {
      return 0;
    }

    let score = 0;

    switch (domain) {
      case 'csde':
        // Extract CSDE dominance score
        score = (
          (metrics.governanceScore || 0) * 0.3 +
          (metrics.dataQualityScore || 0) * 0.3 +
          (metrics.actionConfidence || 0) * 0.4
        );
        break;
      case 'csfe':
        // Extract CSFE dominance score
        score = (
          (metrics.riskScore || 0) * 0.3 +
          (metrics.financialScore || 0) * 0.3 +
          (metrics.actionConfidence || 0) * 0.4
        );
        break;
      case 'csme':
        // Extract CSME dominance score
        score = (
          (metrics.bioScore || 0) * 0.3 +
          (metrics.medComplianceScore || 0) * 0.3 +
          (metrics.actionConfidence || 0) * 0.4
        );
        break;
      default:
        throw new Error(`Unknown domain: ${domain}`);
    }

    return score;
  }

  /**
   * Determine the dominant engine based on dominance scores
   * @param {Object} dominanceScores - Dominance scores
   * @returns {string|null} - Dominant engine or null if no dominant engine
   * @private
   */
  _determineDominantEngine(dominanceScores) {
    // Find the engine with the highest dominance score
    const entries = Object.entries(dominanceScores);
    entries.sort((a, b) => b[1] - a[1]);

    const [topEngine, topScore] = entries[0];

    // Check if the top engine exceeds the dominance threshold
    if (topScore >= this.options.dominanceThreshold) {
      return topEngine;
    }

    return null;
  }

  /**
   * Apply the 18/82 principle to weights
   * @param {string|null} dominantEngine - Dominant engine
   * @returns {Object} - Weights after applying 18/82 principle
   * @private
   */
  _apply1882Principle(dominantEngine) {
    const weights = {};

    if (!dominantEngine) {
      // No dominant engine, use default weights
      return { ...this.options.defaultWeights };
    }

    // Get all engine domains
    const domains = Object.keys(this.options.defaultWeights);

    // Calculate weights based on 18/82 principle
    const nonDominantCount = domains.length - 1;
    const nonDominantWeight = this.options.minorWeight / nonDominantCount;

    for (const domain of domains) {
      if (domain === dominantEngine) {
        weights[domain] = this.options.majorWeight;
      } else {
        weights[domain] = nonDominantWeight;
      }
    }

    return weights;
  }

  /**
   * Get the current weights
   * @returns {Object} - Current weights
   */
  getWeights() {
    return { ...this.weights };
  }

  /**
   * Apply weights to tensors
   * @param {Object} csdeTensor - CSDE tensor
   * @param {Object} csfeTensor - CSFE tensor
   * @param {Object} csmeTensor - CSME tensor
   * @returns {Object} - Weighted tensors
   */
  applyWeights(csdeTensor, csfeTensor, csmeTensor) {
    // Apply weights to tensors
    const weightedCsdeTensor = this._applyWeightToTensor(csdeTensor, this.weights.csde);
    const weightedCsfeTensor = this._applyWeightToTensor(csfeTensor, this.weights.csfe);
    const weightedCsmeTensor = this._applyWeightToTensor(csmeTensor, this.weights.csme);

    return {
      csdeTensor: weightedCsdeTensor,
      csfeTensor: weightedCsfeTensor,
      csmeTensor: weightedCsmeTensor
    };
  }

  /**
   * Apply weight to a tensor
   * @param {Object} tensor - Tensor
   * @param {number} weight - Weight
   * @returns {Object} - Weighted tensor
   * @private
   */
  _applyWeightToTensor(tensor, weight) {
    if (typeof tensor === 'number') {
      return tensor * weight;
    }

    if (Array.isArray(tensor)) {
      return tensor.map(val => val * weight);
    }

    if (typeof tensor === 'object' && tensor.values) {
      return {
        ...tensor,
        values: tensor.values.map(val => val * weight)
      };
    }

    return tensor;
  }
}

module.exports = DynamicWeightingProtocol;
