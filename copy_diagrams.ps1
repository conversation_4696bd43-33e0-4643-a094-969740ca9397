# Create destination directories if they don't exist
$destBase = "D:\novafuse-api-superstore\Comphyology Diagrams"
$mermaidDest = Join-Path $destBase "Mermaid"
$htmlDest = Join-Path $destBase "HTML"
$reactDest = Join-Path $destBase "React"
$exportsDest = Join-Path $destBase "Exports"
$strategicDest = Join-Path $destBase "Strategic_Framework"

# Create directories if they don't exist
@($mermaidDest, $htmlDest, $reactDest, $exportsDest, $strategicDest) | ForEach-Object {
    if (-not (Test-Path $_)) {
        New-Item -ItemType Directory -Path $_ | Out-Null
    }
}

# Copy Mermaid files
$mermaidFiles = Get-ChildItem -Path "D:\novafuse-api-superstore" -Recurse -Filter "*.mmd" -File
foreach ($file in $mermaidFiles) {
    $destPath = Join-Path $mermaidDest $file.Name
    Copy-Item -Path $file.FullName -Destination $destPath -Force
}

# Copy HTML files (limit to likely diagram files)
$htmlFiles = Get-ChildItem -Path "D:\novafuse-api-superstore" -Recurse -Include "*diagram*.html", "*visualization*.html" -File
foreach ($file in $htmlFiles) {
    $destPath = Join-Path $htmlDest $file.Name
    Copy-Item -Path $file.FullName -Destination $destPath -Force
}

# Copy React components with diagrams
$reactFiles = Get-ChildItem -Path "D:\novafuse-api-superstore" -Recurse -Include "*.jsx", "*.tsx" -File | 
    Where-Object { $_.FullName -match "diagram|visualization|chart" }
foreach ($file in $reactFiles) {
    $destPath = Join-Path $reactDest $file.Name
    Copy-Item -Path $file.FullName -Destination $destPath -Force
}

Write-Host "Diagram files have been copied to: $destBase"
