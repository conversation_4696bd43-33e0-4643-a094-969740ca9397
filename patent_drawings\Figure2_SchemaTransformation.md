```
+------------------------+
| Original API Schema    |
|                        |
| {                      |
|   "entity": "Patient", |
|   "fields": [          |
|     {"name": "name"},  |
|     {"name": "ssn"},   |
|     {"name": "dob"},   |
|     {"name": "diag"}   |
|   ]                    |
| }                      |
+----------+-------------+
           |
           | Step 1: Apply Compliance Rules
           v
+------------------------+
| Compliance-Enhanced    |
| Schema                 |
|                        |
| {                      |
|   "entity": "Patient", |
|   "fields": [          |
|     {"name": "name"},  |
|     {"name": "ssn",    |
|      "sensitivity":    |
|      "high"},          |
|     {"name": "dob"},   |
|     {"name": "diag",   |
|      "sensitivity":    |
|      "phi"}            |
|   ],                   |
|   "components": [      |
|     {"type":           |
|      "disclaimer"}     |
|   ]                    |
| }                      |
+----------+-------------+
           |
           | Step 2: Apply Role-Based Rules
           v
+------------------------+
| Role-Adapted Schema    |
| (for Nurse role)       |
|                        |
| {                      |
|   "entity": "Patient", |
|   "fields": [          |
|     {"name": "name"},  |
|     {"name": "dob"},   |
|     {"name": "diag",   |
|      "readOnly": true} |
|   ],                   |
|   "components": [      |
|     {"type":           |
|      "disclaimer"},    |
|     {"type":           |
|      "certification"}  |
|   ]                    |
| }                      |
+----------+-------------+
           |
           | Step 3: Add Verification Requirements
           v
+------------------------+
| Final Transformed      |
| Schema                 |
|                        |
| {                      |
|   "entity": "Patient", |
|   "fields": [          |
|     {"name": "name"},  |
|     {"name": "dob"},   |
|     {"name": "diag",   |
|      "readOnly": true} |
|   ],                   |
|   "components": [      |
|     {"type":           |
|      "disclaimer"},    |
|     {"type":           |
|      "certification"}  |
|   ],                   |
|   "verification": {    |
|     "type":            |
|     "blockchain",      |
|     "auditTrail": true |
|   }                    |
| }                      |
+------------------------+

Figure 2: Schema Transformation Process
```
