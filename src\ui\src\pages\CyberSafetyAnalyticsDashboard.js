import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Typography, 
  CircularProgress, 
  Button, 
  Tabs, 
  Tab, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem,
  Snackbar,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import { 
  Refresh as RefreshIcon, 
  BarChart as BarChartI<PERSON>,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  Timeline as TimelineIcon,
  B<PERSON>bleChart as B<PERSON><PERSON>C<PERSON>Icon
} from '@mui/icons-material';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  Treemap
} from 'recharts';
import axios from 'axios';

// API base URL
const API_BASE_URL = '/api';

// Colors for charts
const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#0088fe', '#00C49F', '#FFBB28', '#FF8042'];

/**
 * CyberSafetyAnalyticsDashboard
 * 
 * This component displays a dashboard for analytics on the Cyber-Safety visualizations.
 */
function CyberSafetyAnalyticsDashboard() {
  // State for analytics data
  const [analyticsData, setAnalyticsData] = useState(null);
  
  // State for loading status
  const [loading, setLoading] = useState(false);
  
  // State for error
  const [error, setError] = useState(null);
  
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);
  
  // State for time range
  const [timeRange, setTimeRange] = useState('7d');
  
  // State for visualization type filter
  const [visualizationTypeFilter, setVisualizationTypeFilter] = useState('all');
  
  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Load analytics data on component mount and when time range or filter changes
  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, visualizationTypeFilter]);
  
  // Load analytics data
  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`${API_BASE_URL}/analytics/visualization`, {
        params: {
          timeRange,
          visualizationType: visualizationTypeFilter !== 'all' ? visualizationTypeFilter : undefined
        }
      });
      
      setAnalyticsData(response.data);
      setLoading(false);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      setError(error.message);
      setLoading(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle time range change
  const handleTimeRangeChange = (event) => {
    setTimeRange(event.target.value);
  };
  
  // Handle visualization type filter change
  const handleVisualizationTypeFilterChange = (event) => {
    setVisualizationTypeFilter(event.target.value);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    loadAnalyticsData();
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };
  
  // Render usage overview
  const renderUsageOverview = () => {
    if (!analyticsData) return null;
    
    const { summary, visualizationUsage, eventCounts, timeSeriesData } = analyticsData;
    
    // Prepare data for visualization usage pie chart
    const visualizationUsageData = Object.entries(visualizationUsage).map(([key, value]) => ({
      name: key,
      value
    }));
    
    // Prepare data for event counts bar chart
    const eventCountsData = Object.entries(eventCounts).map(([key, value]) => ({
      name: key,
      value
    }));
    
    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Summary</Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box>
                <Typography variant="subtitle2">Total Events</Typography>
                <Typography variant="h4">{summary.totalEvents}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2">Unique Users</Typography>
                <Typography variant="h4">{summary.uniqueUsers}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2">Unique Sessions</Typography>
                <Typography variant="h4">{summary.uniqueSessions}</Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Visualization Usage</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={visualizationUsageData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {visualizationUsageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [value, name]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Event Types</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={eventCountsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value, name) => [value, name]} />
                <Legend />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Usage Over Time</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value, name) => [value, name]} />
                <Legend />
                <Line type="monotone" dataKey="total" stroke="#8884d8" name="Total Events" />
                <Line type="monotone" dataKey="viewCount" stroke="#82ca9d" name="Views" />
                <Line type="monotone" dataKey="interactCount" stroke="#ffc658" name="Interactions" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    );
  };
  
  // Render interaction analysis
  const renderInteractionAnalysis = () => {
    if (!analyticsData) return null;
    
    const { interactionTypes, visualizationUsage } = analyticsData;
    
    // Prepare data for interaction types bar chart
    const interactionTypesData = Object.entries(interactionTypes || {}).map(([key, value]) => ({
      name: key,
      value
    }));
    
    // Prepare data for treemap
    const treemapData = Object.entries(visualizationUsage || {}).map(([key, value]) => ({
      name: key,
      size: value
    }));
    
    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Interaction Types</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={interactionTypesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value, name) => [value, name]} />
                <Legend />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Visualization Usage Treemap</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <Treemap
                data={treemapData}
                dataKey="size"
                nameKey="name"
                aspectRatio={4/3}
                stroke="#fff"
                fill="#8884d8"
              >
                {treemapData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Treemap>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>View Duration</Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Visualization Type</TableCell>
                    <TableCell align="right">Average Duration (seconds)</TableCell>
                    <TableCell align="right">Total Duration (seconds)</TableCell>
                    <TableCell align="right">View Count</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Object.entries(analyticsData.viewDuration || {}).map(([key, value]) => (
                    <TableRow key={key}>
                      <TableCell component="th" scope="row">{key}</TableCell>
                      <TableCell align="right">{value.average.toFixed(2)}</TableCell>
                      <TableCell align="right">{value.total}</TableCell>
                      <TableCell align="right">{value.count}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>
    );
  };
  
  // Render error analysis
  const renderErrorAnalysis = () => {
    if (!analyticsData) return null;
    
    const { errorTypes } = analyticsData;
    
    // Prepare data for error types pie chart
    const errorTypesData = Object.entries(errorTypes || {}).map(([key, value]) => ({
      name: key,
      value
    }));
    
    return (
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Error Types</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={errorTypesData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {errorTypesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [value, name]} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: '100%' }}>
            <Typography variant="h6" gutterBottom>Error Rate Over Time</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.timeSeriesData || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip formatter={(value, name) => [value, name]} />
                <Legend />
                <Line type="monotone" dataKey="errorCount" stroke="#ff8042" name="Errors" />
                <Line type="monotone" dataKey="total" stroke="#8884d8" name="Total Events" />
              </LineChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>
      </Grid>
    );
  };
  
  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h4">Cyber-Safety Analytics Dashboard</Typography>
        <Button 
          variant="contained" 
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          disabled={loading}
        >
          {loading ? 'Refreshing...' : 'Refresh'}
        </Button>
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab icon={<BarChartIcon />} label="Usage Overview" />
          <Tab icon={<BubbleChartIcon />} label="Interaction Analysis" />
          <Tab icon={<TimelineIcon />} label="Error Analysis" />
        </Tabs>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Time Range</InputLabel>
            <Select
              value={timeRange}
              onChange={handleTimeRangeChange}
              label="Time Range"
            >
              <MenuItem value="1d">Last Day</MenuItem>
              <MenuItem value="7d">Last 7 Days</MenuItem>
              <MenuItem value="30d">Last 30 Days</MenuItem>
              <MenuItem value="90d">Last 90 Days</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 180 }}>
            <InputLabel>Visualization Type</InputLabel>
            <Select
              value={visualizationTypeFilter}
              onChange={handleVisualizationTypeFilterChange}
              label="Visualization Type"
            >
              <MenuItem value="all">All Types</MenuItem>
              <MenuItem value="triDomainTensor">Tri-Domain Tensor</MenuItem>
              <MenuItem value="harmonyIndex">Harmony Index</MenuItem>
              <MenuItem value="riskControlFusion">Risk-Control Fusion</MenuItem>
              <MenuItem value="resonanceSpectrogram">Resonance Spectrogram</MenuItem>
              <MenuItem value="unifiedComplianceSecurity">Compliance-Security</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>
      
      {/* Show loading indicator or error */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      {/* Show active tab content */}
      {!loading && !error && analyticsData && (
        <>
          {activeTab === 0 && renderUsageOverview()}
          {activeTab === 1 && renderInteractionAnalysis()}
          {activeTab === 2 && renderErrorAnalysis()}
        </>
      )}
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default CyberSafetyAnalyticsDashboard;
