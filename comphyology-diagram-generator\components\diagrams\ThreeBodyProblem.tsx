import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'three-body-problem',
    top: 50,
    left: 300,
    width: 300,
    text: 'Three-Body Problem Reframing',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#fff0f6'
  },
  // Classical vs Comphyological
  {
    id: 'classical-physics',
    top: 120,
    left: 150,
    width: 250,
    text: 'Classical Physics Lens',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-lens',
    top: 120,
    left: 500,
    width: 250,
    text: 'Comphyological Lens',
    number: '3',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#f6ffed'
  },
  // System Boundaries
  {
    id: 'classical-boundaries',
    top: 180,
    left: 150,
    width: 250,
    text: 'System Boundaries:\nPotentially infinite, open',
    number: '4',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-boundaries',
    top: 180,
    left: 500,
    width: 250,
    text: 'System Boundaries:\nFinite, closed, nested',
    number: '5',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Predictability
  {
    id: 'classical-predictability',
    top: 240,
    left: 150,
    width: 250,
    text: 'Predictability:\nChaotic, sensitive to initial conditions',
    number: '6',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-predictability',
    top: 240,
    left: 500,
    width: 250,
    text: 'Predictability:\nStable under nested constraints',
    number: '7',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Mathematical Approach
  {
    id: 'classical-math',
    top: 300,
    left: 150,
    width: 250,
    text: 'Mathematical Approach:\nDifferential equations with diverging solutions',
    number: '8',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-math',
    top: 300,
    left: 500,
    width: 250,
    text: 'Mathematical Approach:\nTensor fields with boundary conditions',
    number: '9',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Interaction Model
  {
    id: 'classical-interaction',
    top: 360,
    left: 150,
    width: 250,
    text: 'Interaction Model:\nPoint-to-point forces',
    number: '10',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-interaction',
    top: 360,
    left: 500,
    width: 250,
    text: 'Interaction Model:\nField-to-field tensorial relationships',
    number: '11',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Stability Mechanism
  {
    id: 'classical-stability',
    top: 420,
    left: 150,
    width: 250,
    text: 'Stability Mechanism:\nNone (inherently unstable)',
    number: '12',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-stability',
    top: 420,
    left: 500,
    width: 250,
    text: 'Stability Mechanism:\nGovernance through nested constraints',
    number: '13',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Practical Application
  {
    id: 'classical-application',
    top: 480,
    left: 150,
    width: 250,
    text: 'Practical Application:\nLimited to specific initial conditions',
    number: '14',
    fontSize: '14px',
    backgroundColor: '#e6f7ff'
  },
  {
    id: 'comphyological-application',
    top: 480,
    left: 500,
    width: 250,
    text: 'Practical Application:\nUniversal across domains with 95% accuracy',
    number: '15',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Three-Body Solution Equation
  {
    id: 'solution-equation',
    top: 550,
    left: 300,
    width: 300,
    text: 'Three-Body Solution = ∮(T⊗G)·dS',
    number: '16',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fffbe6'
  },
  // Implementation
  {
    id: 'implementation',
    top: 620,
    left: 300,
    width: 300,
    text: 'Technical Implementation: Three-Body Problem Reframing',
    number: '17',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#f9f0ff'
  }
];

const connections = [
  // Connect Three-Body Problem to Classical and Comphyological
  {
    start: { x: 375, y: 100 },
    end: { x: 275, y: 120 },
    type: 'arrow'
  },
  {
    start: { x: 525, y: 100 },
    end: { x: 625, y: 120 },
    type: 'arrow'
  },
  // Connect Classical to its components
  {
    start: { x: 275, y: 150 },
    end: { x: 275, y: 180 },
    type: 'line'
  },
  {
    start: { x: 275, y: 210 },
    end: { x: 275, y: 240 },
    type: 'line'
  },
  {
    start: { x: 275, y: 270 },
    end: { x: 275, y: 300 },
    type: 'line'
  },
  {
    start: { x: 275, y: 330 },
    end: { x: 275, y: 360 },
    type: 'line'
  },
  {
    start: { x: 275, y: 390 },
    end: { x: 275, y: 420 },
    type: 'line'
  },
  {
    start: { x: 275, y: 450 },
    end: { x: 275, y: 480 },
    type: 'line'
  },
  // Connect Comphyological to its components
  {
    start: { x: 625, y: 150 },
    end: { x: 625, y: 180 },
    type: 'line'
  },
  {
    start: { x: 625, y: 210 },
    end: { x: 625, y: 240 },
    type: 'line'
  },
  {
    start: { x: 625, y: 270 },
    end: { x: 625, y: 300 },
    type: 'line'
  },
  {
    start: { x: 625, y: 330 },
    end: { x: 625, y: 360 },
    type: 'line'
  },
  {
    start: { x: 625, y: 390 },
    end: { x: 625, y: 420 },
    type: 'line'
  },
  {
    start: { x: 625, y: 450 },
    end: { x: 625, y: 480 },
    type: 'line'
  },
  // Connect to Solution Equation
  {
    start: { x: 275, y: 510 },
    end: { x: 375, y: 550 },
    type: 'arrow'
  },
  {
    start: { x: 625, y: 510 },
    end: { x: 525, y: 550 },
    type: 'arrow'
  },
  // Connect to Implementation
  {
    start: { x: 450, y: 580 },
    end: { x: 450, y: 620 },
    type: 'arrow'
  }
];

const ThreeBodyProblem: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="900px" 
      height="700px" 
    />
  );
};

export default ThreeBodyProblem;
