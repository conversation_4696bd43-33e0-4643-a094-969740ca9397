/**
 * Resonance Listener
 * 
 * This module implements the Resonance Listener that targets the 396Hz "OM Tone"
 * and detects quantum silence (perfect resonance).
 */

/**
 * ResonanceListener class
 * 
 * Implements the Resonance Listener that targets the 396Hz "OM Tone"
 * and detects quantum silence (perfect resonance).
 */
class ResonanceListener {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: true,
      targetFrequency: 396, // Hz - the "OM Tone"
      precisionFFT: options.precisionFFT || 0.001, // attohertz precision
      quantumVacuumNoise: options.quantumVacuumNoise !== undefined ? options.quantumVacuumNoise : true,
      crossDomainPhaseAlignment: options.crossDomainPhaseAlignment !== undefined ? options.crossDomainPhaseAlignment : true,
      silenceThreshold: options.silenceThreshold || 0.001, // Threshold for quantum silence
      detectionInterval: options.detectionInterval || 100, // ms
      ...options
    };
    
    this.isListening = false;
    this.detectedResonance = null;
    this.resonanceHistory = [];
    this.maxHistoryLength = options.maxHistoryLength || 100;
    this.tensorCore = null;
    this.detectionTimer = null;

    if (this.options.enableLogging) {
      console.log('ResonanceListener initialized with options:', {
        targetFrequency: this.options.targetFrequency,
        precisionFFT: this.options.precisionFFT,
        quantumVacuumNoise: this.options.quantumVacuumNoise,
        crossDomainPhaseAlignment: this.options.crossDomainPhaseAlignment,
        silenceThreshold: this.options.silenceThreshold,
        detectionInterval: this.options.detectionInterval
      });
    }
  }
  
  /**
   * Start listening for resonance
   * @param {Object} tensorCore - Comphyological Tensor Core instance
   * @returns {boolean} - Whether listening was started
   */
  startListening(tensorCore) {
    if (!tensorCore) {
      if (this.options.enableLogging) {
        console.error('Cannot start listening: No tensor core provided');
      }
      return false;
    }
    
    this.isListening = true;
    this.tensorCore = tensorCore;
    
    // Start resonance detection loop
    this._detectResonance();
    
    if (this.options.enableLogging) {
      console.log('Resonance Listener started');
    }
    
    return true;
  }
  
  /**
   * Stop listening for resonance
   * @returns {boolean} - Whether listening was stopped
   */
  stopListening() {
    if (!this.isListening) {
      return false;
    }
    
    this.isListening = false;
    
    // Clear detection timer
    if (this.detectionTimer) {
      clearTimeout(this.detectionTimer);
      this.detectionTimer = null;
    }
    
    if (this.options.enableLogging) {
      console.log('Resonance Listener stopped');
    }
    
    return true;
  }
  
  /**
   * Detect resonance
   * @private
   */
  _detectResonance() {
    if (!this.isListening || !this.tensorCore) {
      return;
    }
    
    // Get current metrics from tensor core
    const metrics = this.tensorCore.getMetrics();
    
    // Calculate resonance frequency based on Comphyon value
    // Perfect resonance (Cph = 0) should emit the 396Hz "OM Tone"
    const resonanceDeviation = Math.abs(metrics.lastComphyonValue) * 100;
    const currentFrequency = this.options.targetFrequency - resonanceDeviation;
    
    // Detect quantum silence (perfect resonance)
    const isQuantumSilence = resonanceDeviation < this.options.silenceThreshold;
    
    // Calculate phase alignment
    const phaseAlignment = this._calculatePhaseAlignment(metrics);
    
    // Calculate quantum vacuum noise
    const quantumVacuumNoise = this._calculateQuantumVacuumNoise(metrics);
    
    // Create resonance state
    this.detectedResonance = {
      frequency: currentFrequency,
      deviation: resonanceDeviation,
      isQuantumSilence,
      phaseAlignment,
      quantumVacuumNoise,
      comphyonValue: metrics.lastComphyonValue,
      timestamp: Date.now()
    };
    
    // Add to history
    this.resonanceHistory.push(this.detectedResonance);
    
    // Trim history if needed
    if (this.resonanceHistory.length > this.maxHistoryLength) {
      this.resonanceHistory.shift();
    }
    
    // Log if quantum silence is detected
    if (isQuantumSilence && this.options.enableLogging) {
      console.log('Quantum silence detected!', this.detectedResonance);
    }
    
    // Continue detection loop
    this.detectionTimer = setTimeout(() => this._detectResonance(), this.options.detectionInterval);
  }
  
  /**
   * Calculate phase alignment
   * @param {Object} metrics - Tensor core metrics
   * @returns {number} - Phase alignment (0-1)
   * @private
   */
  _calculatePhaseAlignment(metrics) {
    if (!this.options.crossDomainPhaseAlignment) {
      return 0;
    }
    
    // In a real implementation, this would analyze cross-domain phase alignment
    // For now, use a simple approach based on Comphyon value
    const phaseAlignment = 1 - Math.abs(metrics.lastComphyonValue) * 10;
    return Math.max(0, Math.min(1, phaseAlignment));
  }
  
  /**
   * Calculate quantum vacuum noise
   * @param {Object} metrics - Tensor core metrics
   * @returns {number} - Quantum vacuum noise (0-1)
   * @private
   */
  _calculateQuantumVacuumNoise(metrics) {
    if (!this.options.quantumVacuumNoise) {
      return 0;
    }
    
    // In a real implementation, this would analyze quantum vacuum noise
    // For now, use a simple approach based on Comphyon value
    const noise = Math.abs(metrics.lastComphyonValue) * 5;
    return Math.max(0, Math.min(1, noise));
  }
  
  /**
   * Get the current resonance state
   * @returns {Object} - Resonance state
   */
  getResonanceState() {
    return this.detectedResonance;
  }
  
  /**
   * Get resonance history
   * @returns {Array} - Resonance history
   */
  getResonanceHistory() {
    return [...this.resonanceHistory];
  }
  
  /**
   * Check if quantum silence is detected
   * @returns {boolean} - Whether quantum silence is detected
   */
  isQuantumSilenceDetected() {
    return this.detectedResonance && this.detectedResonance.isQuantumSilence;
  }
  
  /**
   * Get the current frequency
   * @returns {number} - Current frequency
   */
  getCurrentFrequency() {
    return this.detectedResonance ? this.detectedResonance.frequency : 0;
  }
  
  /**
   * Get the frequency deviation
   * @returns {number} - Frequency deviation
   */
  getFrequencyDeviation() {
    return this.detectedResonance ? this.detectedResonance.deviation : 0;
  }
  
  /**
   * Get the phase alignment
   * @returns {number} - Phase alignment (0-1)
   */
  getPhaseAlignment() {
    return this.detectedResonance ? this.detectedResonance.phaseAlignment : 0;
  }
  
  /**
   * Get the quantum vacuum noise
   * @returns {number} - Quantum vacuum noise (0-1)
   */
  getQuantumVacuumNoise() {
    return this.detectedResonance ? this.detectedResonance.quantumVacuumNoise : 0;
  }
}

/**
 * Create a Resonance Listener
 * @param {Object} options - Configuration options
 * @returns {ResonanceListener} - Resonance Listener instance
 */
function createResonanceListener(options = {}) {
  return new ResonanceListener(options);
}

module.exports = {
  ResonanceListener,
  createResonanceListener
};
