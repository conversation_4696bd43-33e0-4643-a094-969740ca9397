# NovaCaia API Documentation

## Overview

The NovaCaia API provides programmatic access to the AI Governance Engine, enabling developers to integrate advanced AI alignment and governance capabilities into their applications.

## Base URL

```
https://api.novacaia.com/v1
```

## Authentication

All API requests require authentication using API keys:

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     https://api.novacaia.com/v1/status
```

## Endpoints

### System Management

#### GET /status
Get system status and health information.

**Response:**
```json
{
  "status": "operational",
  "version": "1.0.0-ENTERPRISE",
  "active": true,
  "production_mode": true,
  "uptime_seconds": 86400,
  "performance": {
    "avg_response_time_ms": 245,
    "accuracy_score": 0.9783,
    "throughput_rps": 1250
  }
}
```

#### POST /activate
Activate the NovaCaia governance engine.

**Request:**
```json
{
  "configuration": {
    "accuracy_target": 0.9783,
    "processing_timeout": 30,
    "enforcement_level": "strict"
  }
}
```

**Response:**
```json
{
  "success": true,
  "status": "AI_GOVERNANCE_ACTIVE",
  "activation_time": "2025-01-10T12:00:00Z",
  "configuration": {
    "accuracy_target": 0.9783,
    "processing_timeout": 30,
    "enforcement_level": "strict"
  }
}
```

### AI Processing

#### POST /process
Process AI input through the governance pipeline.

**Request:**
```json
{
  "input_data": {
    "text": "Analyze market trends for Q1 2025",
    "context": "business_analysis",
    "user_id": "user123",
    "session_id": "session456"
  },
  "options": {
    "include_detailed_metrics": true,
    "enforcement_level": "strict"
  }
}
```

**Response:**
```json
{
  "success": true,
  "processing_time_ms": 342.5,
  "request_id": "req_abc123",
  "consciousness_validation": {
    "valid": true,
    "consciousness_level": 2847,
    "resonance_frequency": 0.92,
    "sentience_score": 0.94,
    "validation_confidence": 0.93
  },
  "truth_processing": {
    "truth_coherence": 0.94,
    "progressive_factor": 0.91,
    "intelligence_amplification": 0.93,
    "authority_validation": true,
    "false_authority_detected": false
  },
  "financial_optimization": {
    "mandatory_allocation": 10.0,
    "performance_allocation": 8.0,
    "total_platform_allocation": 18.0,
    "enterprise_retention": 82.0,
    "coherence_score": 0.91,
    "optimization_factor": 1.15
  },
  "consciousness_score": {
    "score": 0.94,
    "consciousness_level": "HIGH",
    "threshold_met": true,
    "validation_confidence": 0.96
  },
  "boundary_enforced": true,
  "reality_signature": "Ψ⊗Φ⊕Θ",
  "governance_level": "Strict Boundary Enforcement",
  "ai_governance_status": "ACTIVE"
}
```

#### POST /batch-process
Process multiple AI inputs in a single request.

**Request:**
```json
{
  "batch_data": [
    {
      "id": "item1",
      "input_data": {
        "text": "First analysis request",
        "context": "analysis"
      }
    },
    {
      "id": "item2", 
      "input_data": {
        "text": "Second analysis request",
        "context": "analysis"
      }
    }
  ],
  "options": {
    "parallel_processing": true,
    "max_concurrent": 10
  }
}
```

**Response:**
```json
{
  "success": true,
  "batch_id": "batch_xyz789",
  "total_items": 2,
  "processed_items": 2,
  "failed_items": 0,
  "total_processing_time_ms": 456.7,
  "results": [
    {
      "id": "item1",
      "success": true,
      "processing_time_ms": 234.5,
      "consciousness_score": 0.94,
      "boundary_enforced": true
    },
    {
      "id": "item2",
      "success": true,
      "processing_time_ms": 222.2,
      "consciousness_score": 0.96,
      "boundary_enforced": true
    }
  ]
}
```

### Chat Proxy

#### POST /chat-proxy/activate
Activate chat proxy for AI provider integration.

**Request:**
```json
{
  "provider": "openai",
  "enforcement_level": "strict",
  "configuration": {
    "model": "gpt-4",
    "max_tokens": 2000,
    "temperature": 0.7
  }
}
```

**Response:**
```json
{
  "success": true,
  "proxy_active": true,
  "provider": "openai",
  "proxy_endpoint": "https://api.novacaia.com/v1/chat-proxy/openai",
  "configuration": {
    "model": "gpt-4",
    "max_tokens": 2000,
    "temperature": 0.7,
    "governance_enabled": true
  }
}
```

#### POST /chat-proxy/{provider}
Send chat requests through the governance proxy.

**Request:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Explain quantum computing applications in finance"
    }
  ],
  "governance_options": {
    "consciousness_threshold": 0.91,
    "truth_validation": true,
    "false_authority_detection": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "governance_applied": true,
  "processing_time_ms": 567.8,
  "governance_metrics": {
    "consciousness_score": 0.94,
    "truth_coherence": 0.93,
    "boundary_enforced": true
  },
  "response": {
    "role": "assistant",
    "content": "Quantum computing applications in finance include...",
    "governance_validated": true
  }
}
```

### Analytics and Monitoring

#### GET /analytics/performance
Get performance analytics and metrics.

**Query Parameters:**
- `start_date` (ISO 8601): Start date for analytics period
- `end_date` (ISO 8601): End date for analytics period
- `granularity` (string): Data granularity (hour, day, week)

**Response:**
```json
{
  "period": {
    "start_date": "2025-01-01T00:00:00Z",
    "end_date": "2025-01-10T23:59:59Z",
    "granularity": "day"
  },
  "performance_metrics": {
    "avg_response_time_ms": 342.5,
    "accuracy_score": 0.9783,
    "throughput_rps": 1250,
    "uptime_percentage": 99.95
  },
  "consciousness_metrics": {
    "avg_consciousness_score": 0.94,
    "boundary_violations": 0,
    "false_authority_detections": 3,
    "validation_confidence": 0.96
  },
  "financial_metrics": {
    "total_processed_value": 1500000.00,
    "platform_allocation": 270000.00,
    "enterprise_retention": 1230000.00,
    "optimization_factor": 1.15
  }
}
```

#### GET /analytics/consciousness
Get consciousness-specific analytics.

**Response:**
```json
{
  "consciousness_distribution": {
    "high": 0.75,
    "medium": 0.20,
    "low": 0.05
  },
  "validation_accuracy": 0.9783,
  "false_positive_rate": 0.001,
  "false_negative_rate": 0.002,
  "trending_patterns": [
    {
      "pattern": "increased_consciousness_scores",
      "trend": "upward",
      "confidence": 0.87
    }
  ]
}
```

### Configuration Management

#### GET /config
Get current system configuration.

**Response:**
```json
{
  "financial_model": {
    "mandatory_allocation": 0.10,
    "performance_allocation": 0.08,
    "total_platform_allocation": 0.18,
    "enterprise_retention": 0.82
  },
  "governance_settings": {
    "boundary_enforcement": true,
    "consciousness_threshold": 0.91,
    "truth_validation": true,
    "false_authority_detection": true
  },
  "performance_settings": {
    "accuracy_target": 0.9783,
    "processing_timeout": 30,
    "max_concurrent_requests": 1000
  }
}
```

#### PUT /config
Update system configuration.

**Request:**
```json
{
  "governance_settings": {
    "consciousness_threshold": 0.92,
    "processing_timeout": 25
  }
}
```

**Response:**
```json
{
  "success": true,
  "updated_fields": [
    "governance_settings.consciousness_threshold",
    "governance_settings.processing_timeout"
  ],
  "effective_date": "2025-01-10T12:00:00Z"
}
```

## Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Input validation failed",
    "details": {
      "field": "consciousness_threshold",
      "reason": "Value must be between 0.0 and 1.0"
    },
    "request_id": "req_abc123",
    "timestamp": "2025-01-10T12:00:00Z"
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `INVALID_API_KEY` | API key is invalid or expired |
| `RATE_LIMIT_EXCEEDED` | Request rate limit exceeded |
| `VALIDATION_FAILED` | Input validation failed |
| `PROCESSING_TIMEOUT` | Processing timeout exceeded |
| `SYSTEM_UNAVAILABLE` | System temporarily unavailable |
| `CONSCIOUSNESS_THRESHOLD_NOT_MET` | Input did not meet consciousness threshold |
| `BOUNDARY_VIOLATION` | Input violated boundary conditions |
| `FALSE_AUTHORITY_DETECTED` | False authority pattern detected |

## Rate Limits

| Endpoint | Rate Limit | Burst Limit |
|----------|------------|-------------|
| `/process` | 100 req/min | 200 req/min |
| `/batch-process` | 10 req/min | 20 req/min |
| `/chat-proxy/*` | 60 req/min | 120 req/min |
| `/analytics/*` | 30 req/min | 60 req/min |
| `/config` | 10 req/min | 20 req/min |

## SDKs and Libraries

### Python SDK

```bash
pip install novacaia-sdk
```

```python
from novacaia import NovaCaiaClient

client = NovaCaiaClient(api_key="your_api_key")

result = await client.process({
    "text": "Analyze market trends",
    "context": "business_analysis"
})

print(f"Consciousness Score: {result.consciousness_score}")
```

### JavaScript SDK

```bash
npm install @novacaia/sdk
```

```javascript
import { NovaCaiaClient } from '@novacaia/sdk';

const client = new NovaCaiaClient({ apiKey: 'your_api_key' });

const result = await client.process({
  text: 'Analyze market trends',
  context: 'business_analysis'
});

console.log(`Consciousness Score: ${result.consciousnessScore}`);
```

## Webhooks

Configure webhooks to receive real-time notifications:

### Webhook Events

- `processing.completed` - AI processing completed
- `boundary.violation` - Boundary violation detected
- `false_authority.detected` - False authority pattern detected
- `system.alert` - System alert or warning

### Webhook Payload

```json
{
  "event": "processing.completed",
  "timestamp": "2025-01-10T12:00:00Z",
  "data": {
    "request_id": "req_abc123",
    "consciousness_score": 0.94,
    "processing_time_ms": 342.5,
    "boundary_enforced": true
  }
}
```

## Support

For API support and technical assistance:
- API Documentation: https://docs.novacaia.com/api
- Support Email: <EMAIL>
- Status Page: https://status.novacaia.com
