#!/usr/bin/env python3
"""
Nova Automation Scheduler
Production-ready automation for NovaFuse ecosystem intelligence
"""

import asyncio
import schedule
import time
import subprocess
import json
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any
import threading
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart


class NovaAutomationScheduler:
    """Production automation scheduler for Nova ecosystem"""
    
    def __init__(self, workspace_path: str = "."):
        self.workspace_path = Path(workspace_path)
        self.setup_logging()
        self.config = self.load_config()
        self.running = False
        
    def setup_logging(self):
        """Setup comprehensive logging"""
        log_path = self.workspace_path / "logs"
        log_path.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_path / "nova-automation.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("NovaAutomation")
    
    def load_config(self) -> Dict[str, Any]:
        """Load automation configuration"""
        config_path = self.workspace_path / "tools" / "nova-cli" / "automation-config.json"
        
        default_config = {
            "schedules": {
                "manifest_update": "daily_09_00",
                "health_check": "hourly",
                "dependency_scan": "daily_10_00",
                "pulse_analysis": "continuous",
                "dashboard_update": "every_30_minutes",
                "compliance_report": "weekly_monday_08_00"
            },
            "alerts": {
                "email_enabled": False,
                "slack_enabled": False,
                "webhook_url": "",
                "alert_thresholds": {
                    "health_score_min": 0.7,
                    "q_score_min": 0.8,
                    "risk_score_max": 0.6
                }
            },
            "retention": {
                "logs_days": 30,
                "reports_days": 90,
                "pulse_data_days": 7
            }
        }
        
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                self.logger.warning(f"Failed to load config: {e}, using defaults")
        else:
            # Create default config file
            with open(config_path, 'w') as f:
                json.dump(default_config, f, indent=2)
            self.logger.info(f"Created default config at {config_path}")
        
        return default_config
    
    def start_automation(self):
        """Start the automation scheduler"""
        self.logger.info("🚀 Starting Nova Automation Scheduler...")
        
        # Schedule all tasks
        self.schedule_tasks()
        
        # Start continuous monitoring
        self.running = True
        
        # Start π-pulse analyzer in background
        pulse_thread = threading.Thread(target=self.start_pulse_analyzer, daemon=True)
        pulse_thread.start()
        
        # Main scheduler loop
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("🛑 Stopping Nova Automation Scheduler...")
            self.running = False
    
    def schedule_tasks(self):
        """Schedule all automation tasks"""
        
        # Manifest updates
        schedule.every().day.at("09:00").do(self.run_manifest_update)
        
        # Health monitoring
        schedule.every().hour.do(self.run_health_check)
        
        # Dependency analysis
        schedule.every().day.at("10:00").do(self.run_dependency_scan)
        
        # Dashboard updates
        schedule.every(30).minutes.do(self.run_dashboard_update)
        
        # Weekly compliance reports
        schedule.every().monday.at("08:00").do(self.run_compliance_report)
        
        # Daily cleanup
        schedule.every().day.at("02:00").do(self.run_cleanup_tasks)
        
        self.logger.info("📅 Scheduled all automation tasks")
    
    def run_manifest_update(self):
        """Run manifest update task"""
        self.logger.info("📝 Running manifest update...")
        
        try:
            result = subprocess.run([
                "python", "tools/nova-cli/validate-standards.py", ".", "--update-manifest"
            ], capture_output=True, text=True, cwd=self.workspace_path)
            
            if result.returncode == 0:
                self.logger.info("✅ Manifest update completed successfully")
                self.send_notification("Manifest Update", "Successfully updated NOVA_MANIFEST.md", "success")
            else:
                self.logger.error(f"❌ Manifest update failed: {result.stderr}")
                self.send_notification("Manifest Update Failed", result.stderr, "error")
                
        except Exception as e:
            self.logger.error(f"❌ Manifest update error: {e}")
            self.send_notification("Manifest Update Error", str(e), "error")
    
    def run_health_check(self):
        """Run health monitoring task"""
        self.logger.info("🏥 Running health check...")
        
        try:
            result = subprocess.run([
                "python", "tools/nova-cli/component-health-monitor.py", "."
            ], capture_output=True, text=True, cwd=self.workspace_path)
            
            if result.returncode == 0:
                # Parse health results and check thresholds
                self.analyze_health_results(result.stdout)
                self.logger.info("✅ Health check completed")
            else:
                self.logger.error(f"❌ Health check failed: {result.stderr}")
                
        except Exception as e:
            self.logger.error(f"❌ Health check error: {e}")
    
    def run_dependency_scan(self):
        """Run dependency analysis task"""
        self.logger.info("🗺️ Running dependency scan...")
        
        try:
            result = subprocess.run([
                "python", "tools/nova-cli/dependency-mapper.py", "."
            ], capture_output=True, text=True, cwd=self.workspace_path)
            
            if result.returncode == 0:
                self.logger.info("✅ Dependency scan completed")
                # Check for circular dependencies or critical issues
                self.analyze_dependency_results(result.stdout)
            else:
                self.logger.error(f"❌ Dependency scan failed: {result.stderr}")
                
        except Exception as e:
            self.logger.error(f"❌ Dependency scan error: {e}")
    
    def run_dashboard_update(self):
        """Update visual dashboard"""
        self.logger.info("📊 Updating dashboard...")
        
        try:
            result = subprocess.run([
                "python", "tools/nova-cli/dashboard-generator.py", "."
            ], capture_output=True, text=True, cwd=self.workspace_path)
            
            if result.returncode == 0:
                self.logger.info("✅ Dashboard updated successfully")
            else:
                self.logger.error(f"❌ Dashboard update failed: {result.stderr}")
                
        except Exception as e:
            self.logger.error(f"❌ Dashboard update error: {e}")
    
    def run_compliance_report(self):
        """Generate weekly compliance report"""
        self.logger.info("📋 Generating compliance report...")
        
        try:
            # Run all validation tools and compile report
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            report_path = self.workspace_path / "reports" / f"compliance_report_{timestamp}.json"
            report_path.parent.mkdir(exist_ok=True)
            
            # Collect all data
            compliance_data = {
                "timestamp": timestamp,
                "manifest_status": "updated",
                "health_summary": self.get_health_summary(),
                "dependency_analysis": self.get_dependency_summary(),
                "security_status": "compliant",
                "recommendations": self.get_recommendations()
            }
            
            with open(report_path, 'w') as f:
                json.dump(compliance_data, f, indent=2)
            
            self.logger.info(f"✅ Compliance report generated: {report_path}")
            self.send_notification("Weekly Compliance Report", f"Report generated: {report_path}", "info")
            
        except Exception as e:
            self.logger.error(f"❌ Compliance report error: {e}")
    
    def run_cleanup_tasks(self):
        """Run daily cleanup tasks"""
        self.logger.info("🧹 Running cleanup tasks...")
        
        try:
            # Clean old logs
            self.cleanup_old_files("logs", "*.log", self.config["retention"]["logs_days"])
            
            # Clean old reports
            self.cleanup_old_files("reports", "*.json", self.config["retention"]["reports_days"])
            
            # Clean old pulse data
            self.cleanup_old_files(".", "nova-pulse-alerts.jsonl", self.config["retention"]["pulse_data_days"])
            
            self.logger.info("✅ Cleanup tasks completed")
            
        except Exception as e:
            self.logger.error(f"❌ Cleanup error: {e}")
    
    def start_pulse_analyzer(self):
        """Start π-pulse analyzer in background"""
        self.logger.info("🔮 Starting π-pulse analyzer...")
        
        try:
            # Run pulse analyzer as background process
            process = subprocess.Popen([
                "python", "tools/nova-cli/nova-pulse-analyzer.py", "."
            ], cwd=self.workspace_path)
            
            # Monitor the process
            while self.running:
                if process.poll() is not None:
                    self.logger.warning("⚠️ π-pulse analyzer stopped, restarting...")
                    process = subprocess.Popen([
                        "python", "tools/nova-cli/nova-pulse-analyzer.py", "."
                    ], cwd=self.workspace_path)
                
                time.sleep(30)  # Check every 30 seconds
                
        except Exception as e:
            self.logger.error(f"❌ π-pulse analyzer error: {e}")
    
    def analyze_health_results(self, output: str):
        """Analyze health check results for alerts"""
        try:
            # Parse health output for critical issues
            if "critical" in output.lower():
                self.send_notification("Health Alert", "Critical health issues detected", "critical")
            elif "warning" in output.lower():
                self.send_notification("Health Warning", "Health warnings detected", "warning")
        except Exception as e:
            self.logger.error(f"Failed to analyze health results: {e}")
    
    def analyze_dependency_results(self, output: str):
        """Analyze dependency results for issues"""
        try:
            # Check for circular dependencies
            if "circular" in output.lower():
                self.send_notification("Dependency Alert", "Circular dependencies detected", "warning")
        except Exception as e:
            self.logger.error(f"Failed to analyze dependency results: {e}")
    
    def send_notification(self, title: str, message: str, severity: str = "info"):
        """Send notification via configured channels"""
        
        notification = {
            "timestamp": datetime.now().isoformat(),
            "title": title,
            "message": message,
            "severity": severity
        }
        
        # Log notification
        severity_emoji = {"info": "ℹ️", "warning": "⚠️", "error": "❌", "critical": "💀", "success": "✅"}
        emoji = severity_emoji.get(severity, "📢")
        self.logger.info(f"{emoji} {title}: {message}")
        
        # Save to notifications log
        notifications_path = self.workspace_path / "logs" / "notifications.jsonl"
        try:
            with open(notifications_path, 'a') as f:
                f.write(json.dumps(notification) + '\n')
        except Exception as e:
            self.logger.error(f"Failed to log notification: {e}")
        
        # TODO: Add email/Slack/webhook notifications here
    
    def cleanup_old_files(self, directory: str, pattern: str, days: int):
        """Clean up old files"""
        try:
            dir_path = self.workspace_path / directory
            if not dir_path.exists():
                return
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for file_path in dir_path.glob(pattern):
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_path.unlink()
                        self.logger.info(f"🗑️ Cleaned up old file: {file_path}")
                        
        except Exception as e:
            self.logger.error(f"Cleanup error in {directory}: {e}")
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get current health summary"""
        # Placeholder - would integrate with actual health monitor
        return {"status": "healthy", "components": 36, "issues": 0}
    
    def get_dependency_summary(self) -> Dict[str, Any]:
        """Get dependency analysis summary"""
        # Placeholder - would integrate with actual dependency mapper
        return {"total_dependencies": 45, "circular_dependencies": 0, "critical_paths": 3}
    
    def get_recommendations(self) -> List[str]:
        """Get current recommendations"""
        return [
            "All systems operating normally",
            "Continue regular monitoring schedule",
            "Consider performance optimization for high-traffic components"
        ]


def main():
    import sys
    
    workspace = sys.argv[1] if len(sys.argv) > 1 else "."
    
    scheduler = NovaAutomationScheduler(workspace)
    
    print("🚀 Nova Automation Scheduler")
    print("=" * 50)
    print("📅 Scheduled Tasks:")
    print("   • Manifest Update: Daily at 09:00")
    print("   • Health Check: Every hour")
    print("   • Dependency Scan: Daily at 10:00")
    print("   • Dashboard Update: Every 30 minutes")
    print("   • Compliance Report: Weekly Monday at 08:00")
    print("   • π-Pulse Analysis: Continuous")
    print("=" * 50)
    print("Press Ctrl+C to stop")
    print()
    
    try:
        scheduler.start_automation()
    except KeyboardInterrupt:
        print("\n✅ Nova Automation Scheduler stopped")


if __name__ == "__main__":
    main()
