/**
 * NovaFuse Universal API Connector - Credential Model
 * 
 * This module defines the MongoDB schema for API credentials.
 * Credentials are stored with encryption for sensitive fields.
 */

const mongoose = require('mongoose');
const { Schema } = mongoose;
const crypto = require('crypto');

// Get encryption key from environment
const ENCRYPTION_KEY = process.env.CREDENTIALS_ENCRYPTION_KEY;
if (!ENCRYPTION_KEY) {
  console.warn('WARNING: CREDENTIALS_ENCRYPTION_KEY not set. Using development key.');
}

// Encryption/decryption utilities
const encrypt = (text) => {
  if (!text) return text;
  
  try {
    const key = ENCRYPTION_KEY || 'development-key-only-for-testing-not-for-prod';
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'hex'), iv);
    
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    
    return `${iv.toString('hex')}:${encrypted.toString('hex')}`;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt credential');
  }
};

const decrypt = (text) => {
  if (!text) return text;
  
  try {
    const key = ENCRYPTION_KEY || 'development-key-only-for-testing-not-for-prod';
    const parts = text.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const encryptedText = Buffer.from(parts[1], 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'hex'), iv);
    
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    
    return decrypted.toString();
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt credential');
  }
};

// Define credential schema
const CredentialSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    unique: true,
    index: true
  },
  name: { 
    type: String, 
    required: true 
  },
  description: String,
  connectorId: { 
    type: String, 
    required: true,
    index: true
  },
  credentials: {
    type: Map,
    of: String,
    required: true
  },
  ownerId: { 
    type: String,
    required: true,
    index: true
  },
  isActive: { 
    type: Boolean, 
    default: true 
  },
  lastUsed: Date,
  lastTested: Date,
  testResult: {
    success: Boolean,
    message: String,
    timestamp: Date
  },
  expiresAt: Date,
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, {
  timestamps: true,
  versionKey: true,
  toJSON: {
    transform: function(doc, ret) {
      // Don't expose encrypted credentials in JSON
      if (ret.credentials) {
        ret.credentials = Object.keys(ret.credentials).reduce((acc, key) => {
          acc[key] = '********';
          return acc;
        }, {});
      }
      return ret;
    }
  }
});

// Pre-save hook to encrypt sensitive fields
CredentialSchema.pre('save', function(next) {
  if (!this.id) {
    // Generate ID
    this.id = `cred-${crypto.randomBytes(8).toString('hex')}`;
  }
  
  // Encrypt credentials
  if (this.isModified('credentials')) {
    const encryptedCredentials = new Map();
    
    for (const [key, value] of this.credentials.entries()) {
      encryptedCredentials.set(key, encrypt(value));
    }
    
    this.credentials = encryptedCredentials;
  }
  
  next();
});

// Method to get decrypted credentials
CredentialSchema.methods.getDecryptedCredentials = function() {
  const decryptedCredentials = {};
  
  for (const [key, value] of this.credentials.entries()) {
    decryptedCredentials[key] = decrypt(value);
  }
  
  return decryptedCredentials;
};

// Method to update last used timestamp
CredentialSchema.methods.updateLastUsed = async function() {
  this.lastUsed = new Date();
  return this.save();
};

// Method to update test result
CredentialSchema.methods.updateTestResult = async function(success, message) {
  this.lastTested = new Date();
  this.testResult = {
    success,
    message,
    timestamp: new Date()
  };
  return this.save();
};

// Create the model
const Credential = mongoose.model('Credential', CredentialSchema);

module.exports = Credential;
