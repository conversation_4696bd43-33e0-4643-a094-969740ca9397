/**
 * Telomere Erosion Prediction
 * 
 * This module implements the Telomere Erosion Prediction component of the CSME.
 * It models cellular aging and predicts telomere length changes over time.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * TelomereErosionPrediction class
 */
class TelomereErosionPrediction extends EventEmitter {
  /**
   * Create a new TelomereErosionPrediction instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 10000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      baseErosionRate: 0.005, // Base erosion rate per time unit
      thresholds: {
        telomereLength: {
          critical: 0.2, // Critical threshold (short telomeres)
          warning: 0.4,
          normal: 0.6,
          optimal: 0.8
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      telomereLength: 0.75, // Normalized telomere length (0-1)
      erosionRate: this.options.baseErosionRate,
      erosionFactors: new Map(), // id -> factor object
      telomereHistory: [],
      telomereStatus: 'normal', // critical, warning, normal, optimal
      biologicalAge: 40, // Default biological age
      chronologicalAge: 40, // Default chronological age
      ageAcceleration: 0, // Difference between biological and chronological age
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      factorsAdded: 0,
      factorsRemoved: 0,
      statusChanges: 0
    };
    
    if (this.options.enableLogging) {
      console.log('TelomereErosionPrediction initialized');
    }
  }
  
  /**
   * Start the telomere erosion prediction
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('TelomereErosionPrediction is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('TelomereErosionPrediction started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the telomere erosion prediction
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('TelomereErosionPrediction is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('TelomereErosionPrediction stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Set telomere length
   * @param {number} length - Telomere length (0-1)
   * @param {Object} metadata - Additional metadata
   * @returns {number} - Updated telomere length
   */
  setTelomereLength(length, metadata = {}) {
    const startTime = performance.now();
    
    if (typeof length !== 'number' || length < 0 || length > 1) {
      throw new Error('Telomere length must be a number between 0 and 1');
    }
    
    // Update state
    this.state.telomereLength = length;
    this.state.lastUpdateTime = Date.now();
    
    // Update telomere status
    this._updateTelomereStatus();
    
    // Add to history
    this.state.telomereHistory.push({
      telomereLength: this.state.telomereLength,
      telomereStatus: this.state.telomereStatus,
      timestamp: Date.now(),
      metadata
    });
    
    // Limit history size
    if (this.state.telomereHistory.length > this.options.historySize) {
      this.state.telomereHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('telomere-update', {
      telomereLength: this.state.telomereLength,
      telomereStatus: this.state.telomereStatus,
      timestamp: Date.now()
    });
    
    return this.state.telomereLength;
  }
  
  /**
   * Set biological age
   * @param {number} age - Biological age
   * @returns {Object} - Updated age data
   */
  setBiologicalAge(age) {
    const startTime = performance.now();
    
    if (typeof age !== 'number' || age < 0) {
      throw new Error('Biological age must be a positive number');
    }
    
    // Update state
    this.state.biologicalAge = age;
    
    // Calculate age acceleration
    this.state.ageAcceleration = this.state.biologicalAge - this.state.chronologicalAge;
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('age-update', {
      biologicalAge: this.state.biologicalAge,
      chronologicalAge: this.state.chronologicalAge,
      ageAcceleration: this.state.ageAcceleration,
      timestamp: Date.now()
    });
    
    return {
      biologicalAge: this.state.biologicalAge,
      chronologicalAge: this.state.chronologicalAge,
      ageAcceleration: this.state.ageAcceleration
    };
  }
  
  /**
   * Set chronological age
   * @param {number} age - Chronological age
   * @returns {Object} - Updated age data
   */
  setChronologicalAge(age) {
    const startTime = performance.now();
    
    if (typeof age !== 'number' || age < 0) {
      throw new Error('Chronological age must be a positive number');
    }
    
    // Update state
    this.state.chronologicalAge = age;
    
    // Calculate age acceleration
    this.state.ageAcceleration = this.state.biologicalAge - this.state.chronologicalAge;
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('age-update', {
      biologicalAge: this.state.biologicalAge,
      chronologicalAge: this.state.chronologicalAge,
      ageAcceleration: this.state.ageAcceleration,
      timestamp: Date.now()
    });
    
    return {
      biologicalAge: this.state.biologicalAge,
      chronologicalAge: this.state.chronologicalAge,
      ageAcceleration: this.state.ageAcceleration
    };
  }
  
  /**
   * Add erosion factor
   * @param {Object} factor - Erosion factor object
   * @returns {Object} - Added factor
   */
  addErosionFactor(factor) {
    const startTime = performance.now();
    
    if (!factor || typeof factor !== 'object') {
      throw new Error('Factor must be an object');
    }
    
    if (!factor.id) {
      factor.id = `factor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    factor = {
      type: 'generic', // oxidative_stress, inflammation, etc.
      impact: 0.5, // 0-1 (impact on erosion rate)
      duration: 30, // days
      status: 'active', // active, inactive
      startedAt: Date.now(),
      ...factor
    };
    
    // Add to state
    this.state.erosionFactors.set(factor.id, factor);
    
    // Update erosion rate
    this._updateErosionRate();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.factorsAdded++;
    
    // Emit event
    this.emit('factor-added', factor);
    
    if (this.options.enableLogging) {
      console.log(`TelomereErosionPrediction: Added ${factor.type} factor ${factor.id}`);
    }
    
    return factor;
  }
  
  /**
   * Remove erosion factor
   * @param {string} factorId - Factor ID
   * @returns {boolean} - Success status
   */
  removeErosionFactor(factorId) {
    const startTime = performance.now();
    
    if (!factorId || !this.state.erosionFactors.has(factorId)) {
      return false;
    }
    
    // Get factor
    const factor = this.state.erosionFactors.get(factorId);
    
    // Remove from state
    this.state.erosionFactors.delete(factorId);
    
    // Update erosion rate
    this._updateErosionRate();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.factorsRemoved++;
    
    // Emit event
    this.emit('factor-removed', factor);
    
    if (this.options.enableLogging) {
      console.log(`TelomereErosionPrediction: Removed factor ${factorId}`);
    }
    
    return true;
  }
  
  /**
   * Predict telomere length
   * @param {number} timeUnits - Number of time units to predict
   * @returns {Array} - Predicted telomere length over time
   */
  predictTelomereLength(timeUnits) {
    const startTime = performance.now();
    
    if (typeof timeUnits !== 'number' || timeUnits <= 0) {
      throw new Error('Time units must be a positive number');
    }
    
    const predictions = [];
    let currentLength = this.state.telomereLength;
    
    // Calculate predicted length for each time unit
    for (let i = 1; i <= timeUnits; i++) {
      // Apply erosion
      currentLength -= this.state.erosionRate;
      
      // Ensure length is not negative
      currentLength = Math.max(0, currentLength);
      
      // Add prediction
      predictions.push({
        timeUnit: i,
        telomereLength: currentLength,
        timestamp: Date.now() + (i * this.options.updateInterval)
      });
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    return predictions;
  }
  
  /**
   * Get telomere length
   * @returns {number} - Current telomere length
   */
  getTelomereLength() {
    return this.state.telomereLength;
  }
  
  /**
   * Get telomere status
   * @returns {string} - Current telomere status
   */
  getTelomereStatus() {
    return this.state.telomereStatus;
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      telomereLength: this.state.telomereLength,
      telomereStatus: this.state.telomereStatus,
      erosionRate: this.state.erosionRate,
      biologicalAge: this.state.biologicalAge,
      chronologicalAge: this.state.chronologicalAge,
      ageAcceleration: this.state.ageAcceleration,
      factorCount: this.state.erosionFactors.size,
      telomereHistory: [...this.state.telomereHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get erosion factors
   * @param {string} type - Optional type filter
   * @returns {Array} - Erosion factors
   */
  getErosionFactors(type) {
    const factors = Array.from(this.state.erosionFactors.values());
    
    if (type) {
      return factors.filter(f => f.type === type);
    }
    
    return factors;
  }
  
  /**
   * Update telomere status
   * @private
   */
  _updateTelomereStatus() {
    const { telomereLength } = this.state;
    const { thresholds } = this.options;
    
    let newStatus = 'normal';
    
    if (telomereLength <= thresholds.telomereLength.critical) {
      newStatus = 'critical';
    } else if (telomereLength <= thresholds.telomereLength.warning) {
      newStatus = 'warning';
    } else if (telomereLength >= thresholds.telomereLength.optimal) {
      newStatus = 'optimal';
    }
    
    // If status changed, emit event
    if (newStatus !== this.state.telomereStatus) {
      this.state.telomereStatus = newStatus;
      this.metrics.statusChanges++;
      
      // Emit status change event
      this.emit('status-change', {
        telomereStatus: this.state.telomereStatus,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`TelomereErosionPrediction: Telomere status changed to ${this.state.telomereStatus}`);
      }
    }
  }
  
  /**
   * Update erosion rate
   * @private
   */
  _updateErosionRate() {
    // Start with base erosion rate
    let erosionRate = this.options.baseErosionRate;
    
    // Apply factor impacts
    for (const factor of this.state.erosionFactors.values()) {
      if (factor.status === 'active') {
        // Active factors increase erosion rate
        erosionRate += this.options.baseErosionRate * factor.impact;
      }
    }
    
    // Update state
    this.state.erosionRate = erosionRate;
    
    // Emit update event
    this.emit('erosion-rate-update', {
      erosionRate: this.state.erosionRate,
      timestamp: Date.now()
    });
  }
  
  /**
   * Apply telomere erosion
   * @private
   */
  _applyTelomereErosion() {
    // Calculate time since last update
    const timeSinceLastUpdate = (Date.now() - this.state.lastUpdateTime) / this.options.updateInterval;
    
    // Calculate erosion amount
    const erosionAmount = this.state.erosionRate * timeSinceLastUpdate;
    
    // Apply erosion
    const newLength = Math.max(0, this.state.telomereLength - erosionAmount);
    
    // Update telomere length
    this.setTelomereLength(newLength, { source: 'natural_erosion' });
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // Apply telomere erosion
        this._applyTelomereErosion();
        
        // In a real implementation, this would fetch real-time data
        // For now, just simulate some changes
        this._simulateChanges();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate changes
   * @private
   */
  _simulateChanges() {
    // Simulate random changes to erosion factors
    const rand = Math.random();
    
    if (rand < 0.1) {
      // Add a new erosion factor
      this.addErosionFactor({
        type: this._randomFactorType(),
        impact: Math.random() * 0.5,
        duration: Math.floor(Math.random() * 30) + 1
      });
    } else if (rand < 0.2 && this.state.erosionFactors.size > 0) {
      // Remove a random erosion factor
      const factors = Array.from(this.state.erosionFactors.keys());
      const randomFactorId = factors[Math.floor(Math.random() * factors.length)];
      this.removeErosionFactor(randomFactorId);
    }
  }
  
  /**
   * Generate random factor type
   * @returns {string} - Random factor type
   * @private
   */
  _randomFactorType() {
    const types = [
      'oxidative_stress',
      'inflammation',
      'dna_damage',
      'mitochondrial_dysfunction',
      'cellular_senescence'
    ];
    
    return types[Math.floor(Math.random() * types.length)];
  }
}

module.exports = TelomereErosionPrediction;
