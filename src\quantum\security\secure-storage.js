/**
 * Secure Storage
 *
 * This module provides secure storage capabilities for the Finite Universe
 * Principle defense system, enabling secure storage of sensitive data.
 */

const EventEmitter = require('events');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * SecureStorage class
 * 
 * Provides secure storage capabilities for sensitive data.
 */
class SecureStorage extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      storageType: options.storageType || 'memory', // 'memory', 'file', 'encrypted-file'
      storagePath: options.storagePath || './secure-storage',
      encryptionKey: options.encryptionKey || this._generateEncryptionKey(),
      encryptionAlgorithm: options.encryptionAlgorithm || 'aes-256-gcm',
      autoSave: options.autoSave !== undefined ? options.autoSave : true,
      autoSaveInterval: options.autoSaveInterval || 60000, // 1 minute
      ...options
    };

    // Initialize storage
    this.storage = new Map();
    
    // Initialize auto-save interval
    this.autoSaveInterval = null;

    // Initialize storage
    this._initializeStorage();

    if (this.options.enableLogging) {
      console.log('SecureStorage initialized with options:', {
        ...this.options,
        encryptionKey: this.options.encryptionKey ? '***' : undefined
      });
    }
  }

  /**
   * Initialize storage
   * @private
   */
  _initializeStorage() {
    // Initialize storage based on type
    switch (this.options.storageType) {
      case 'file':
      case 'encrypted-file':
        this._initializeFileStorage();
        break;
      case 'memory':
      default:
        // Memory storage is already initialized
        break;
    }

    // Start auto-save interval if enabled
    if (this.options.autoSave && (this.options.storageType === 'file' || this.options.storageType === 'encrypted-file')) {
      this.autoSaveInterval = setInterval(() => {
        this.save();
      }, this.options.autoSaveInterval);
    }
  }

  /**
   * Initialize file storage
   * @private
   */
  _initializeFileStorage() {
    try {
      // Create storage directory if not exists
      if (!fs.existsSync(this.options.storagePath)) {
        fs.mkdirSync(this.options.storagePath, { recursive: true });
      }

      // Get storage file path
      const storageFilePath = path.join(this.options.storagePath, 'secure-storage.json');

      // Check if storage file exists
      if (fs.existsSync(storageFilePath)) {
        // Read storage file
        const data = fs.readFileSync(storageFilePath, 'utf8');

        // Parse storage data
        let storageData;
        if (this.options.storageType === 'encrypted-file') {
          // Decrypt data
          storageData = this._decryptData(data);
        } else {
          // Parse JSON data
          storageData = JSON.parse(data);
        }

        // Load storage data
        if (storageData && typeof storageData === 'object') {
          for (const [key, value] of Object.entries(storageData)) {
            this.storage.set(key, value);
          }
        }

        if (this.options.enableLogging) {
          console.log(`Storage loaded from file: ${storageFilePath}`);
        }
      }
    } catch (error) {
      console.error('Error initializing file storage:', error);
    }
  }

  /**
   * Save storage to file
   * @returns {boolean} - True if storage was saved, false otherwise
   */
  save() {
    // Skip if not using file storage
    if (this.options.storageType !== 'file' && this.options.storageType !== 'encrypted-file') {
      return false;
    }

    try {
      // Get storage file path
      const storageFilePath = path.join(this.options.storagePath, 'secure-storage.json');

      // Convert storage to object
      const storageData = {};
      for (const [key, value] of this.storage.entries()) {
        storageData[key] = value;
      }

      // Serialize storage data
      let data;
      if (this.options.storageType === 'encrypted-file') {
        // Encrypt data
        data = this._encryptData(storageData);
      } else {
        // Stringify JSON data
        data = JSON.stringify(storageData, null, 2);
      }

      // Write storage file
      fs.writeFileSync(storageFilePath, data);

      if (this.options.enableLogging) {
        console.log(`Storage saved to file: ${storageFilePath}`);
      }

      // Emit save event
      this.emit('save');

      return true;
    } catch (error) {
      console.error('Error saving storage to file:', error);
      return false;
    }
  }

  /**
   * Encrypt data
   * @param {Object} data - Data to encrypt
   * @returns {string} - Encrypted data
   * @private
   */
  _encryptData(data) {
    // Convert data to string
    const dataString = JSON.stringify(data);

    // Generate initialization vector
    const iv = crypto.randomBytes(16);

    // Create cipher
    const cipher = crypto.createCipheriv(
      this.options.encryptionAlgorithm,
      Buffer.from(this.options.encryptionKey, 'hex'),
      iv
    );

    // Encrypt data
    let encrypted = cipher.update(dataString, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get authentication tag
    const authTag = cipher.getAuthTag().toString('hex');

    // Return encrypted data with IV and auth tag
    return JSON.stringify({
      iv: iv.toString('hex'),
      authTag,
      data: encrypted
    });
  }

  /**
   * Decrypt data
   * @param {string} encryptedData - Encrypted data
   * @returns {Object} - Decrypted data
   * @private
   */
  _decryptData(encryptedData) {
    // Parse encrypted data
    const { iv, authTag, data } = JSON.parse(encryptedData);

    // Create decipher
    const decipher = crypto.createDecipheriv(
      this.options.encryptionAlgorithm,
      Buffer.from(this.options.encryptionKey, 'hex'),
      Buffer.from(iv, 'hex')
    );

    // Set authentication tag
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));

    // Decrypt data
    let decrypted = decipher.update(data, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    // Parse decrypted data
    return JSON.parse(decrypted);
  }

  /**
   * Generate encryption key
   * @returns {string} - Encryption key
   * @private
   */
  _generateEncryptionKey() {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Set a value
   * @param {string} key - Key
   * @param {*} value - Value
   * @returns {boolean} - True if value was set, false otherwise
   */
  set(key, value) {
    // Validate key
    if (!key || typeof key !== 'string') {
      if (this.options.enableLogging) {
        console.log('Invalid key');
      }
      return false;
    }

    // Set value
    this.storage.set(key, value);

    if (this.options.enableLogging) {
      console.log(`Value set for key: ${key}`);
    }

    // Save storage if auto-save is enabled
    if (this.options.autoSave && (this.options.storageType === 'file' || this.options.storageType === 'encrypted-file')) {
      this.save();
    }

    // Emit set event
    this.emit('set', { key });

    return true;
  }

  /**
   * Get a value
   * @param {string} key - Key
   * @returns {*} - Value
   */
  get(key) {
    // Validate key
    if (!key || typeof key !== 'string') {
      if (this.options.enableLogging) {
        console.log('Invalid key');
      }
      return undefined;
    }

    // Get value
    const value = this.storage.get(key);

    // Emit get event
    this.emit('get', { key });

    return value;
  }

  /**
   * Delete a value
   * @param {string} key - Key
   * @returns {boolean} - True if value was deleted, false otherwise
   */
  delete(key) {
    // Validate key
    if (!key || typeof key !== 'string') {
      if (this.options.enableLogging) {
        console.log('Invalid key');
      }
      return false;
    }

    // Check if key exists
    if (!this.storage.has(key)) {
      if (this.options.enableLogging) {
        console.log(`Key not found: ${key}`);
      }
      return false;
    }

    // Delete value
    this.storage.delete(key);

    if (this.options.enableLogging) {
      console.log(`Value deleted for key: ${key}`);
    }

    // Save storage if auto-save is enabled
    if (this.options.autoSave && (this.options.storageType === 'file' || this.options.storageType === 'encrypted-file')) {
      this.save();
    }

    // Emit delete event
    this.emit('delete', { key });

    return true;
  }

  /**
   * Check if a key exists
   * @param {string} key - Key
   * @returns {boolean} - True if key exists, false otherwise
   */
  has(key) {
    // Validate key
    if (!key || typeof key !== 'string') {
      if (this.options.enableLogging) {
        console.log('Invalid key');
      }
      return false;
    }

    return this.storage.has(key);
  }

  /**
   * Clear storage
   * @returns {boolean} - True if storage was cleared, false otherwise
   */
  clear() {
    // Clear storage
    this.storage.clear();

    if (this.options.enableLogging) {
      console.log('Storage cleared');
    }

    // Save storage if auto-save is enabled
    if (this.options.autoSave && (this.options.storageType === 'file' || this.options.storageType === 'encrypted-file')) {
      this.save();
    }

    // Emit clear event
    this.emit('clear');

    return true;
  }

  /**
   * Get all keys
   * @returns {Array} - Array of keys
   */
  keys() {
    return Array.from(this.storage.keys());
  }

  /**
   * Get all values
   * @returns {Array} - Array of values
   */
  values() {
    return Array.from(this.storage.values());
  }

  /**
   * Get storage size
   * @returns {number} - Storage size
   */
  size() {
    return this.storage.size;
  }

  /**
   * Dispose resources
   */
  dispose() {
    // Save storage if using file storage
    if (this.options.storageType === 'file' || this.options.storageType === 'encrypted-file') {
      this.save();
    }

    // Clear auto-save interval
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval);
      this.autoSaveInterval = null;
    }

    // Clear storage
    this.storage.clear();

    if (this.options.enableLogging) {
      console.log('SecureStorage disposed');
    }
  }
}

/**
 * Create a secure storage with recommended settings
 * @param {Object} options - Configuration options
 * @returns {SecureStorage} - Configured secure storage
 */
function createSecureStorage(options = {}) {
  return new SecureStorage({
    enableLogging: true,
    storageType: 'encrypted-file',
    autoSave: true,
    ...options
  });
}

module.exports = {
  SecureStorage,
  createSecureStorage
};
