{"version": 3, "names": ["request", "require", "app", "errorHandlingService", "describe", "it", "response", "get", "expect", "body", "toHaveProperty", "error", "post", "send", "Error", "name", "context", "user", "id", "path", "method", "errorResponse", "handleError", "resource", "retryCount", "recovery", "registerCircuitBreaker", "failureT<PERSON><PERSON>old", "resetTimeout", "attempts", "testFunction", "withRetry", "maxRetries", "initialDelay", "max<PERSON><PERSON><PERSON>", "retryableErrors", "result", "toBe", "rejects", "toThrow", "circuitBreaker", "circuitBreakers", "state", "withCircuitBreaker", "failureCount", "Promise", "resolve", "setTimeout", "successFunction", "withTimeout", "timeoutMs", "primaryFunction", "fallbackFunction", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["errorHandling.test.js"], "sourcesContent": ["/**\n * Error Handling Tests\n * \n * This file contains tests for the error handling infrastructure.\n */\n\nconst request = require('supertest');\nconst app = require('../../server');\nconst errorHandlingService = require('../../api/services/ErrorHandlingService');\n\ndescribe('Error Handling Infrastructure', () => {\n  describe('Error Handler Middleware', () => {\n    it('should handle 404 errors', async () => {\n      const response = await request(app)\n        .get('/non-existent-route')\n        .expect('Content-Type', /json/)\n        .expect(404);\n      \n      expect(response.body).toHaveProperty('error');\n      expect(response.body.error).toHaveProperty('type', 'not_found_error');\n      expect(response.body.error).toHaveProperty('status', 404);\n    });\n    \n    it('should handle validation errors', async () => {\n      const response = await request(app)\n        .post('/api/connectors')\n        .send({}) // Missing required fields\n        .expect('Content-Type', /json/)\n        .expect(400);\n      \n      expect(response.body).toHaveProperty('error');\n      expect(response.body.error).toHaveProperty('type', 'validation_error');\n      expect(response.body.error).toHaveProperty('status', 400);\n    });\n  });\n  \n  describe('Error Handling Service', () => {\n    it('should create error responses with appropriate status codes', () => {\n      const error = new Error('Test error');\n      error.name = 'ValidationError';\n      \n      const context = {\n        user: { id: 'test-user' },\n        path: '/test',\n        method: 'GET'\n      };\n      \n      const errorResponse = errorHandlingService.handleError(error, context);\n      \n      expect(errorResponse).toHaveProperty('error');\n      expect(errorResponse.error).toHaveProperty('type', 'validation_error');\n      expect(errorResponse.error).toHaveProperty('status', 400);\n      expect(errorResponse.error).toHaveProperty('message', 'Test error');\n    });\n    \n    it('should apply retry strategy for network errors', () => {\n      const error = new Error('Connection refused');\n      error.name = 'NetworkError';\n      \n      const context = {\n        resource: 'test-api',\n        retryCount: 1\n      };\n      \n      const errorResponse = errorHandlingService.handleError(error, context);\n      \n      expect(errorResponse).toHaveProperty('error');\n      expect(errorResponse.error).toHaveProperty('type', 'network_error');\n      expect(errorResponse.error).toHaveProperty('status', 503);\n      expect(errorResponse).toHaveProperty('recovery');\n      expect(errorResponse.recovery).toHaveProperty('strategy', 'retry');\n      expect(errorResponse.recovery).toHaveProperty('retryCount', 1);\n    });\n    \n    it('should apply circuit breaker strategy for rate limit errors', () => {\n      const error = new Error('Rate limit exceeded');\n      error.name = 'RateLimitError';\n      \n      const context = {\n        resource: 'test-api'\n      };\n      \n      // Register circuit breaker for test\n      errorHandlingService.registerCircuitBreaker('test-api', {\n        failureThreshold: 5,\n        resetTimeout: 30000\n      });\n      \n      const errorResponse = errorHandlingService.handleError(error, context);\n      \n      expect(errorResponse).toHaveProperty('error');\n      expect(errorResponse.error).toHaveProperty('type', 'rate_limit_error');\n      expect(errorResponse.error).toHaveProperty('status', 429);\n      expect(errorResponse).toHaveProperty('recovery');\n      expect(errorResponse.recovery).toHaveProperty('strategy', 'circuit_breaker');\n      expect(errorResponse.recovery).toHaveProperty('state', 'closed');\n    });\n  });\n  \n  describe('Retry Mechanism', () => {\n    it('should retry failed operations', async () => {\n      let attempts = 0;\n      \n      const testFunction = errorHandlingService.withRetry(async () => {\n        attempts++;\n        if (attempts < 3) {\n          const error = new Error('Temporary failure');\n          error.name = 'NetworkError';\n          throw error;\n        }\n        return 'success';\n      }, {\n        maxRetries: 3,\n        initialDelay: 10,\n        maxDelay: 100,\n        retryableErrors: ['network_error']\n      });\n      \n      const result = await testFunction();\n      \n      expect(result).toBe('success');\n      expect(attempts).toBe(3);\n    });\n    \n    it('should not retry non-retryable errors', async () => {\n      let attempts = 0;\n      \n      const testFunction = errorHandlingService.withRetry(async () => {\n        attempts++;\n        const error = new Error('Validation error');\n        error.name = 'ValidationError';\n        throw error;\n      }, {\n        maxRetries: 3,\n        initialDelay: 10,\n        maxDelay: 100,\n        retryableErrors: ['network_error', 'timeout_error']\n      });\n      \n      await expect(testFunction()).rejects.toThrow('Validation error');\n      expect(attempts).toBe(1);\n    });\n  });\n  \n  describe('Circuit Breaker', () => {\n    it('should trip circuit breaker after threshold failures', async () => {\n      const resource = 'test-circuit-breaker';\n      \n      // Register circuit breaker\n      errorHandlingService.registerCircuitBreaker(resource, {\n        failureThreshold: 2,\n        resetTimeout: 100\n      });\n      \n      const circuitBreaker = errorHandlingService.circuitBreakers.get(resource);\n      expect(circuitBreaker.state).toBe('closed');\n      \n      // Create test function\n      const testFunction = errorHandlingService.withCircuitBreaker(async () => {\n        throw new Error('Service unavailable');\n      }, { resource });\n      \n      // First failure\n      await expect(testFunction()).rejects.toThrow('Service unavailable');\n      expect(circuitBreaker.state).toBe('closed');\n      expect(circuitBreaker.failureCount).toBe(1);\n      \n      // Second failure - should trip circuit breaker\n      await expect(testFunction()).rejects.toThrow('Service unavailable');\n      expect(circuitBreaker.state).toBe('open');\n      expect(circuitBreaker.failureCount).toBe(2);\n      \n      // Third attempt - circuit breaker is open\n      await expect(testFunction()).rejects.toThrow('Circuit breaker for test-circuit-breaker is open');\n      \n      // Wait for circuit breaker to reset\n      await new Promise(resolve => setTimeout(resolve, 150));\n      \n      // Circuit breaker should be half-open\n      expect(circuitBreaker.state).toBe('half-open');\n      \n      // Successful call should close circuit breaker\n      const successFunction = errorHandlingService.withCircuitBreaker(async () => {\n        return 'success';\n      }, { resource });\n      \n      await successFunction();\n      expect(circuitBreaker.state).toBe('closed');\n      expect(circuitBreaker.failureCount).toBe(0);\n    });\n  });\n  \n  describe('Timeout Mechanism', () => {\n    it('should timeout long-running operations', async () => {\n      const testFunction = errorHandlingService.withTimeout(async () => {\n        await new Promise(resolve => setTimeout(resolve, 100));\n        return 'success';\n      }, { timeoutMs: 50 });\n      \n      await expect(testFunction()).rejects.toThrow('Operation timed out after 50ms');\n    });\n    \n    it('should not timeout quick operations', async () => {\n      const testFunction = errorHandlingService.withTimeout(async () => {\n        await new Promise(resolve => setTimeout(resolve, 10));\n        return 'success';\n      }, { timeoutMs: 50 });\n      \n      const result = await testFunction();\n      expect(result).toBe('success');\n    });\n  });\n  \n  describe('Fallback Mechanism', () => {\n    it('should use fallback when primary function fails', async () => {\n      const primaryFunction = async () => {\n        throw new Error('Primary function failed');\n      };\n      \n      const fallbackFunction = async () => {\n        return 'fallback result';\n      };\n      \n      const testFunction = errorHandlingService.withFallback(primaryFunction, fallbackFunction);\n      \n      const result = await testFunction();\n      expect(result).toBe('fallback result');\n    });\n    \n    it('should use primary function when it succeeds', async () => {\n      const primaryFunction = async () => {\n        return 'primary result';\n      };\n      \n      const fallbackFunction = async () => {\n        return 'fallback result';\n      };\n      \n      const testFunction = errorHandlingService.withFallback(primaryFunction, fallbackFunction);\n      \n      const result = await testFunction();\n      expect(result).toBe('primary result');\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMC,GAAG,GAAGD,OAAO,CAAC,cAAc,CAAC;AACnC,MAAME,oBAAoB,GAAGF,OAAO,CAAC,yCAAyC,CAAC;AAE/EG,QAAQ,CAAC,+BAA+B,EAAE,MAAM;EAC9CA,QAAQ,CAAC,0BAA0B,EAAE,MAAM;IACzCC,EAAE,CAAC,0BAA0B,EAAE,YAAY;MACzC,MAAMC,QAAQ,GAAG,MAAMN,OAAO,CAACE,GAAG,CAAC,CAChCK,GAAG,CAAC,qBAAqB,CAAC,CAC1BC,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAC9BA,MAAM,CAAC,GAAG,CAAC;MAEdA,MAAM,CAACF,QAAQ,CAACG,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MAC7CF,MAAM,CAACF,QAAQ,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,cAAc,CAAC,MAAM,EAAE,iBAAiB,CAAC;MACrEF,MAAM,CAACF,QAAQ,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC3D,CAAC,CAAC;IAEFL,EAAE,CAAC,iCAAiC,EAAE,YAAY;MAChD,MAAMC,QAAQ,GAAG,MAAMN,OAAO,CAACE,GAAG,CAAC,CAChCU,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAAA,CACTL,MAAM,CAAC,cAAc,EAAE,MAAM,CAAC,CAC9BA,MAAM,CAAC,GAAG,CAAC;MAEdA,MAAM,CAACF,QAAQ,CAACG,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MAC7CF,MAAM,CAACF,QAAQ,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC;MACtEF,MAAM,CAACF,QAAQ,CAACG,IAAI,CAACE,KAAK,CAAC,CAACD,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFN,QAAQ,CAAC,wBAAwB,EAAE,MAAM;IACvCC,EAAE,CAAC,6DAA6D,EAAE,MAAM;MACtE,MAAMM,KAAK,GAAG,IAAIG,KAAK,CAAC,YAAY,CAAC;MACrCH,KAAK,CAACI,IAAI,GAAG,iBAAiB;MAE9B,MAAMC,OAAO,GAAG;QACdC,IAAI,EAAE;UAAEC,EAAE,EAAE;QAAY,CAAC;QACzBC,IAAI,EAAE,OAAO;QACbC,MAAM,EAAE;MACV,CAAC;MAED,MAAMC,aAAa,GAAGlB,oBAAoB,CAACmB,WAAW,CAACX,KAAK,EAAEK,OAAO,CAAC;MAEtER,MAAM,CAACa,aAAa,CAAC,CAACX,cAAc,CAAC,OAAO,CAAC;MAC7CF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC;MACtEF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC;MACzDF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,SAAS,EAAE,YAAY,CAAC;IACrE,CAAC,CAAC;IAEFL,EAAE,CAAC,gDAAgD,EAAE,MAAM;MACzD,MAAMM,KAAK,GAAG,IAAIG,KAAK,CAAC,oBAAoB,CAAC;MAC7CH,KAAK,CAACI,IAAI,GAAG,cAAc;MAE3B,MAAMC,OAAO,GAAG;QACdO,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE;MACd,CAAC;MAED,MAAMH,aAAa,GAAGlB,oBAAoB,CAACmB,WAAW,CAACX,KAAK,EAAEK,OAAO,CAAC;MAEtER,MAAM,CAACa,aAAa,CAAC,CAACX,cAAc,CAAC,OAAO,CAAC;MAC7CF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC;MACnEF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC;MACzDF,MAAM,CAACa,aAAa,CAAC,CAACX,cAAc,CAAC,UAAU,CAAC;MAChDF,MAAM,CAACa,aAAa,CAACI,QAAQ,CAAC,CAACf,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC;MAClEF,MAAM,CAACa,aAAa,CAACI,QAAQ,CAAC,CAACf,cAAc,CAAC,YAAY,EAAE,CAAC,CAAC;IAChE,CAAC,CAAC;IAEFL,EAAE,CAAC,6DAA6D,EAAE,MAAM;MACtE,MAAMM,KAAK,GAAG,IAAIG,KAAK,CAAC,qBAAqB,CAAC;MAC9CH,KAAK,CAACI,IAAI,GAAG,gBAAgB;MAE7B,MAAMC,OAAO,GAAG;QACdO,QAAQ,EAAE;MACZ,CAAC;;MAED;MACApB,oBAAoB,CAACuB,sBAAsB,CAAC,UAAU,EAAE;QACtDC,gBAAgB,EAAE,CAAC;QACnBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,MAAMP,aAAa,GAAGlB,oBAAoB,CAACmB,WAAW,CAACX,KAAK,EAAEK,OAAO,CAAC;MAEtER,MAAM,CAACa,aAAa,CAAC,CAACX,cAAc,CAAC,OAAO,CAAC;MAC7CF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC;MACtEF,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC,CAACD,cAAc,CAAC,QAAQ,EAAE,GAAG,CAAC;MACzDF,MAAM,CAACa,aAAa,CAAC,CAACX,cAAc,CAAC,UAAU,CAAC;MAChDF,MAAM,CAACa,aAAa,CAACI,QAAQ,CAAC,CAACf,cAAc,CAAC,UAAU,EAAE,iBAAiB,CAAC;MAC5EF,MAAM,CAACa,aAAa,CAACI,QAAQ,CAAC,CAACf,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFN,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCC,EAAE,CAAC,gCAAgC,EAAE,YAAY;MAC/C,IAAIwB,QAAQ,GAAG,CAAC;MAEhB,MAAMC,YAAY,GAAG3B,oBAAoB,CAAC4B,SAAS,CAAC,YAAY;QAC9DF,QAAQ,EAAE;QACV,IAAIA,QAAQ,GAAG,CAAC,EAAE;UAChB,MAAMlB,KAAK,GAAG,IAAIG,KAAK,CAAC,mBAAmB,CAAC;UAC5CH,KAAK,CAACI,IAAI,GAAG,cAAc;UAC3B,MAAMJ,KAAK;QACb;QACA,OAAO,SAAS;MAClB,CAAC,EAAE;QACDqB,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE,CAAC,eAAe;MACnC,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,MAAMN,YAAY,CAAC,CAAC;MAEnCtB,MAAM,CAAC4B,MAAM,CAAC,CAACC,IAAI,CAAC,SAAS,CAAC;MAC9B7B,MAAM,CAACqB,QAAQ,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC;IAEFhC,EAAE,CAAC,uCAAuC,EAAE,YAAY;MACtD,IAAIwB,QAAQ,GAAG,CAAC;MAEhB,MAAMC,YAAY,GAAG3B,oBAAoB,CAAC4B,SAAS,CAAC,YAAY;QAC9DF,QAAQ,EAAE;QACV,MAAMlB,KAAK,GAAG,IAAIG,KAAK,CAAC,kBAAkB,CAAC;QAC3CH,KAAK,CAACI,IAAI,GAAG,iBAAiB;QAC9B,MAAMJ,KAAK;MACb,CAAC,EAAE;QACDqB,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,EAAE;QAChBC,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE,CAAC,eAAe,EAAE,eAAe;MACpD,CAAC,CAAC;MAEF,MAAM3B,MAAM,CAACsB,YAAY,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAChE/B,MAAM,CAACqB,QAAQ,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCC,EAAE,CAAC,sDAAsD,EAAE,YAAY;MACrE,MAAMkB,QAAQ,GAAG,sBAAsB;;MAEvC;MACApB,oBAAoB,CAACuB,sBAAsB,CAACH,QAAQ,EAAE;QACpDI,gBAAgB,EAAE,CAAC;QACnBC,YAAY,EAAE;MAChB,CAAC,CAAC;MAEF,MAAMY,cAAc,GAAGrC,oBAAoB,CAACsC,eAAe,CAAClC,GAAG,CAACgB,QAAQ,CAAC;MACzEf,MAAM,CAACgC,cAAc,CAACE,KAAK,CAAC,CAACL,IAAI,CAAC,QAAQ,CAAC;;MAE3C;MACA,MAAMP,YAAY,GAAG3B,oBAAoB,CAACwC,kBAAkB,CAAC,YAAY;QACvE,MAAM,IAAI7B,KAAK,CAAC,qBAAqB,CAAC;MACxC,CAAC,EAAE;QAAES;MAAS,CAAC,CAAC;;MAEhB;MACA,MAAMf,MAAM,CAACsB,YAAY,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACC,OAAO,CAAC,qBAAqB,CAAC;MACnE/B,MAAM,CAACgC,cAAc,CAACE,KAAK,CAAC,CAACL,IAAI,CAAC,QAAQ,CAAC;MAC3C7B,MAAM,CAACgC,cAAc,CAACI,YAAY,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;;MAE3C;MACA,MAAM7B,MAAM,CAACsB,YAAY,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACC,OAAO,CAAC,qBAAqB,CAAC;MACnE/B,MAAM,CAACgC,cAAc,CAACE,KAAK,CAAC,CAACL,IAAI,CAAC,MAAM,CAAC;MACzC7B,MAAM,CAACgC,cAAc,CAACI,YAAY,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;;MAE3C;MACA,MAAM7B,MAAM,CAACsB,YAAY,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACC,OAAO,CAAC,kDAAkD,CAAC;;MAEhG;MACA,MAAM,IAAIM,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACAtC,MAAM,CAACgC,cAAc,CAACE,KAAK,CAAC,CAACL,IAAI,CAAC,WAAW,CAAC;;MAE9C;MACA,MAAMW,eAAe,GAAG7C,oBAAoB,CAACwC,kBAAkB,CAAC,YAAY;QAC1E,OAAO,SAAS;MAClB,CAAC,EAAE;QAAEpB;MAAS,CAAC,CAAC;MAEhB,MAAMyB,eAAe,CAAC,CAAC;MACvBxC,MAAM,CAACgC,cAAc,CAACE,KAAK,CAAC,CAACL,IAAI,CAAC,QAAQ,CAAC;MAC3C7B,MAAM,CAACgC,cAAc,CAACI,YAAY,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,mBAAmB,EAAE,MAAM;IAClCC,EAAE,CAAC,wCAAwC,EAAE,YAAY;MACvD,MAAMyB,YAAY,GAAG3B,oBAAoB,CAAC8C,WAAW,CAAC,YAAY;QAChE,MAAM,IAAIJ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;QACtD,OAAO,SAAS;MAClB,CAAC,EAAE;QAAEI,SAAS,EAAE;MAAG,CAAC,CAAC;MAErB,MAAM1C,MAAM,CAACsB,YAAY,CAAC,CAAC,CAAC,CAACQ,OAAO,CAACC,OAAO,CAAC,gCAAgC,CAAC;IAChF,CAAC,CAAC;IAEFlC,EAAE,CAAC,qCAAqC,EAAE,YAAY;MACpD,MAAMyB,YAAY,GAAG3B,oBAAoB,CAAC8C,WAAW,CAAC,YAAY;QAChE,MAAM,IAAIJ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;QACrD,OAAO,SAAS;MAClB,CAAC,EAAE;QAAEI,SAAS,EAAE;MAAG,CAAC,CAAC;MAErB,MAAMd,MAAM,GAAG,MAAMN,YAAY,CAAC,CAAC;MACnCtB,MAAM,CAAC4B,MAAM,CAAC,CAACC,IAAI,CAAC,SAAS,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFjC,QAAQ,CAAC,oBAAoB,EAAE,MAAM;IACnCC,EAAE,CAAC,iDAAiD,EAAE,YAAY;MAChE,MAAM8C,eAAe,GAAG,MAAAA,CAAA,KAAY;QAClC,MAAM,IAAIrC,KAAK,CAAC,yBAAyB,CAAC;MAC5C,CAAC;MAED,MAAMsC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,OAAO,iBAAiB;MAC1B,CAAC;MAED,MAAMtB,YAAY,GAAG3B,oBAAoB,CAACkD,YAAY,CAACF,eAAe,EAAEC,gBAAgB,CAAC;MAEzF,MAAMhB,MAAM,GAAG,MAAMN,YAAY,CAAC,CAAC;MACnCtB,MAAM,CAAC4B,MAAM,CAAC,CAACC,IAAI,CAAC,iBAAiB,CAAC;IACxC,CAAC,CAAC;IAEFhC,EAAE,CAAC,8CAA8C,EAAE,YAAY;MAC7D,MAAM8C,eAAe,GAAG,MAAAA,CAAA,KAAY;QAClC,OAAO,gBAAgB;MACzB,CAAC;MAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;QACnC,OAAO,iBAAiB;MAC1B,CAAC;MAED,MAAMtB,YAAY,GAAG3B,oBAAoB,CAACkD,YAAY,CAACF,eAAe,EAAEC,gBAAgB,CAAC;MAEzF,MAAMhB,MAAM,GAAG,MAAMN,YAAY,CAAC,CAAC;MACnCtB,MAAM,CAAC4B,MAAM,CAAC,CAACC,IAAI,CAAC,gBAAgB,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}