// NovaBrowser - JavaScript Integration Layer
// Bridges WASM NovaAgent with Browser UI

import init, { NovaAgent } from './pkg/novabrowser.js';

class NovaBrowserEngine {
    constructor() {
        this.novaAgent = null;
        this.coherenceThreshold = 0.82;
        this.autoFilter = true;
        this.visionEnabled = true;
        this.shieldActive = true;
        this.currentUrl = '';
        this.coherenceHistory = [];
    }

    async initialize() {
        console.log('🚀 Initializing NovaBrowser Engine...');
        
        try {
            await init();
            this.novaAgent = new NovaAgent();
            
            this.setupPageMonitoring();
            this.setupUIOverlays();
            this.setupEventListeners();
            
            console.log('✅ NovaBrowser Engine initialized successfully');
            return true;
        } catch (error) {
            console.error('❌ Failed to initialize NovaBrowser:', error);
            return false;
        }
    }

    setupPageMonitoring() {
        // Monitor page navigation
        const originalPushState = history.pushState;
        const originalReplaceState = history.replaceState;
        
        history.pushState = (...args) => {
            originalPushState.apply(history, args);
            this.onNavigationChange();
        };
        
        history.replaceState = (...args) => {
            originalReplaceState.apply(history, args);
            this.onNavigationChange();
        };
        
        window.addEventListener('popstate', () => this.onNavigationChange());
        window.addEventListener('load', () => this.onPageLoad());
        
        // Monitor DOM changes
        const observer = new MutationObserver(() => this.onDOMChange());
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true
        });
    }

    setupUIOverlays() {
        // Create coherence status indicator
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'nova-coherence-indicator';
        statusIndicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
            z-index: 9999;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        `;
        statusIndicator.innerHTML = '🧬';
        statusIndicator.title = 'Nova Coherence Status';
        
        statusIndicator.addEventListener('click', () => this.showCoherencePanel());
        
        document.body.appendChild(statusIndicator);
        
        // Create floating action button for NovaVision
        const visionButton = document.createElement('div');
        visionButton.id = 'nova-vision-button';
        visionButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00ff96, #00d4aa);
            color: black;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            z-index: 9998;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,255,150,0.3);
            transition: all 0.3s ease;
        `;
        visionButton.innerHTML = '👁️';
        visionButton.title = 'NovaVision UI Compliance';
        
        visionButton.addEventListener('click', () => this.toggleVisionOverlay());
        
        document.body.appendChild(visionButton);
    }

    setupEventListeners() {
        // Listen for security events
        window.addEventListener('beforeunload', (e) => {
            if (this.hasSecurityWarnings()) {
                e.preventDefault();
                e.returnValue = 'Security warnings detected. Are you sure you want to leave?';
            }
        });
        
        // Listen for form submissions
        document.addEventListener('submit', (e) => {
            if (!this.validateFormSecurity(e.target)) {
                e.preventDefault();
                this.showSecurityWarning('Form submission blocked due to security concerns');
            }
        });
    }

    async onNavigationChange() {
        const newUrl = window.location.href;
        if (newUrl !== this.currentUrl) {
            this.currentUrl = newUrl;
            console.log(`🌐 Navigation to: ${newUrl}`);
            
            await this.analyzeCurrentPage();
        }
    }

    async onPageLoad() {
        console.log('📄 Page loaded, starting analysis...');
        await this.analyzeCurrentPage();
    }

    onDOMChange() {
        // Debounced DOM analysis
        clearTimeout(this.domChangeTimeout);
        this.domChangeTimeout = setTimeout(() => {
            this.analyzeCurrentPage();
        }, 1000);
    }

    async analyzeCurrentPage() {
        if (!this.novaAgent) return;
        
        try {
            // Get coherence analysis
            const coherenceJson = this.novaAgent.analyze_page_coherence(this.currentUrl);
            const coherenceData = JSON.parse(coherenceJson);
            
            // Get UI compliance analysis
            const visionJson = this.novaAgent.analyze_ui_compliance();
            const visionData = JSON.parse(visionJson);
            
            // Get threat assessment
            const threatJson = this.novaAgent.assess_threats(this.currentUrl);
            const threatData = JSON.parse(threatJson);
            
            // Update UI indicators
            this.updateCoherenceIndicator(coherenceData);
            this.updateVisionIndicator(visionData);
            this.updateThreatIndicator(threatData);
            
            // Apply filtering if enabled
            if (this.autoFilter) {
                const passed = this.novaAgent.apply_coherence_filter(this.coherenceThreshold);
                if (!passed) {
                    console.log('⚠️ Page filtered due to low coherence');
                }
            }
            
            // Store in history
            this.coherenceHistory.push({
                url: this.currentUrl,
                timestamp: Date.now(),
                coherence: coherenceData,
                vision: visionData,
                threats: threatData
            });
            
            // Keep only last 100 entries
            if (this.coherenceHistory.length > 100) {
                this.coherenceHistory.shift();
            }
            
        } catch (error) {
            console.error('❌ Analysis failed:', error);
        }
    }

    updateCoherenceIndicator(data) {
        const indicator = document.getElementById('nova-coherence-indicator');
        if (!indicator) return;
        
        const percentage = Math.round(data.overall * 100);
        indicator.innerHTML = `${percentage}%`;
        
        // Color coding based on coherence level
        if (data.psi_snap) {
            indicator.style.background = 'linear-gradient(45deg, #00ff96, #00d4aa)';
            indicator.style.color = 'black';
        } else if (percentage >= 70) {
            indicator.style.background = 'linear-gradient(45deg, #ffa726, #ff9800)';
            indicator.style.color = 'white';
        } else {
            indicator.style.background = 'linear-gradient(45deg, #ff4757, #ff3742)';
            indicator.style.color = 'white';
        }
        
        indicator.title = `Coherence: ${percentage}% (Ψ-Snap: ${data.psi_snap ? 'Active' : 'Building'})`;
    }

    updateVisionIndicator(data) {
        const button = document.getElementById('nova-vision-button');
        if (!button) return;
        
        if (data.ada_compliance) {
            button.style.background = 'linear-gradient(45deg, #00ff96, #00d4aa)';
            button.innerHTML = '✅';
        } else {
            button.style.background = 'linear-gradient(45deg, #ff4757, #ff3742)';
            button.innerHTML = '⚠️';
        }
        
        const score = Math.round(data.accessibility_score * 100);
        button.title = `UI Compliance: ${score}% (ADA: ${data.ada_compliance ? 'Pass' : 'Fail'})`;
    }

    updateThreatIndicator(data) {
        // Could add a third indicator for threats, or integrate into existing ones
        console.log(`🛡️ Threat Level: ${data.risk_level} (${Math.round(data.threat_score * 100)}%)`);
    }

    showCoherencePanel() {
        const panel = document.createElement('div');
        panel.id = 'nova-coherence-panel';
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            max-height: 80vh;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: white;
            border-radius: 15px;
            padding: 30px;
            z-index: 10001;
            box-shadow: 0 20px 60px rgba(0,0,0,0.5);
            overflow-y: auto;
        `;
        
        const lastAnalysis = this.coherenceHistory[this.coherenceHistory.length - 1];
        if (!lastAnalysis) {
            panel.innerHTML = '<h2>No analysis data available</h2>';
        } else {
            panel.innerHTML = this.generateCoherencePanelHTML(lastAnalysis);
        }
        
        // Add backdrop
        const backdrop = document.createElement('div');
        backdrop.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 10000;
        `;
        backdrop.addEventListener('click', () => {
            document.body.removeChild(backdrop);
            document.body.removeChild(panel);
        });
        
        document.body.appendChild(backdrop);
        document.body.appendChild(panel);
    }

    generateCoherencePanelHTML(analysis) {
        const c = analysis.coherence;
        const v = analysis.vision;
        const t = analysis.threats;
        
        return `
            <div style="text-align: center; margin-bottom: 30px;">
                <h2 style="color: #00ff96; margin-bottom: 10px;">🧬 Nova Coherence Analysis</h2>
                <p style="opacity: 0.8;">${analysis.url}</p>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h3 style="color: #00ff96;">Overall</h3>
                    <div style="font-size: 24px; font-weight: bold;">${Math.round(c.overall * 100)}%</div>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h3 style="color: #667eea;">Structural</h3>
                    <div style="font-size: 24px; font-weight: bold;">${Math.round(c.structural * 100)}%</div>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h3 style="color: #764ba2;">Functional</h3>
                    <div style="font-size: 24px; font-weight: bold;">${Math.round(c.functional * 100)}%</div>
                </div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3 style="color: #00ff96;">👁️ NovaVision UI Compliance</h3>
                <p>Accessibility Score: ${Math.round(v.accessibility_score * 100)}%</p>
                <p>ADA Compliance: ${v.ada_compliance ? '✅ Pass' : '❌ Fail'}</p>
                ${v.wcag_violations.length > 0 ? `
                    <div style="margin-top: 10px;">
                        <strong>Violations:</strong>
                        <ul>${v.wcag_violations.map(violation => `<li>${violation}</li>`).join('')}</ul>
                    </div>
                ` : ''}
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3 style="color: #ff4757;">🛡️ NovaShield Threat Assessment</h3>
                <p>Risk Level: <strong style="color: ${t.risk_level === 'HIGH' ? '#ff4757' : t.risk_level === 'MEDIUM' ? '#ffa726' : '#00ff96'}">${t.risk_level}</strong></p>
                <p>Threat Score: ${Math.round(t.threat_score * 100)}%</p>
                ${t.detected_threats.length > 0 ? `
                    <div style="margin-top: 10px;">
                        <strong>Detected Threats:</strong>
                        <ul>${t.detected_threats.map(threat => `<li>${threat}</li>`).join('')}</ul>
                    </div>
                ` : ''}
            </div>
            
            <div style="text-align: center;">
                <button onclick="this.parentElement.parentElement.parentElement.removeChild(this.parentElement.parentElement.parentElement.previousElementSibling); this.parentElement.parentElement.parentElement.removeChild(this.parentElement.parentElement.parentElement);" 
                        style="background: #00ff96; color: black; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-weight: bold;">
                    Close
                </button>
            </div>
        `;
    }

    toggleVisionOverlay() {
        const existingOverlay = document.getElementById('nova-vision-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
            return;
        }
        
        // Create vision overlay that highlights compliance issues
        const overlay = document.createElement('div');
        overlay.id = 'nova-vision-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9997;
        `;
        
        // Highlight images without alt text
        const images = document.querySelectorAll('img:not([alt])');
        images.forEach(img => {
            const rect = img.getBoundingClientRect();
            const highlight = document.createElement('div');
            highlight.style.cssText = `
                position: absolute;
                top: ${rect.top + window.scrollY}px;
                left: ${rect.left + window.scrollX}px;
                width: ${rect.width}px;
                height: ${rect.height}px;
                border: 3px solid #ff4757;
                background: rgba(255, 71, 87, 0.2);
                pointer-events: none;
            `;
            overlay.appendChild(highlight);
        });
        
        document.body.appendChild(overlay);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.remove();
            }
        }, 5000);
    }

    hasSecurityWarnings() {
        const lastAnalysis = this.coherenceHistory[this.coherenceHistory.length - 1];
        return lastAnalysis && lastAnalysis.threats.risk_level === 'HIGH';
    }

    validateFormSecurity(form) {
        // Check if form is submitting to HTTPS
        const action = form.action || window.location.href;
        return action.startsWith('https://') || action.startsWith('http://localhost');
    }

    showSecurityWarning(message) {
        alert(`🛡️ NovaShield Security Warning: ${message}`);
    }
}

// Initialize NovaBrowser when DOM is ready
let novaBrowser = null;

document.addEventListener('DOMContentLoaded', async () => {
    novaBrowser = new NovaBrowserEngine();
    const initialized = await novaBrowser.initialize();
    
    if (initialized) {
        console.log('🌐 NovaBrowser is now protecting your browsing experience!');
    } else {
        console.error('❌ Failed to initialize NovaBrowser protection');
    }
});

// Export for external access
window.NovaBrowser = novaBrowser;
