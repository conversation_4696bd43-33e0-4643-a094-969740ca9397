/**
 * CSFE Adapter
 * 
 * This module provides an adapter for connecting the CSFE (Cyber-Safety Financial Engine)
 * with the Comphyological Tensor Core.
 */

const { createComphyologicalTensorCore } = require('../tensor');

/**
 * CSFEAdapter class
 * 
 * Provides an adapter for connecting the CSFE with the Comphyological Tensor Core.
 */
class CSFEAdapter {
  /**
   * Constructor
   * @param {Object} csfeEngine - CSFE engine instance
   * @param {Object} options - Configuration options
   */
  constructor(csfeEngine, options = {}) {
    this.options = {
      enableLogging: true,
      strictMode: false,
      useGPU: false,
      useDynamicWeighting: true,
      ...options
    };

    this.csfeEngine = csfeEngine;
    this.tensorCore = createComphyologicalTensorCore(this.options);
    this.lastResult = null;
    this.metrics = {
      processCount: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastProcessingTime: 0,
      startTime: Date.now()
    };

    if (this.options.enableLogging) {
      console.log('CSFEAdapter initialized with options:', {
        strictMode: this.options.strictMode,
        useGPU: this.options.useGPU,
        useDynamicWeighting: this.options.useDynamicWeighting
      });
    }
  }

  /**
   * Process data through the CSFE and Comphyological Tensor Core
   * @param {Object} csdeData - CSDE data
   * @param {Object} csfeData - CSFE data
   * @param {Object} csmeData - CSME data
   * @returns {Object} - Processing result
   */
  processData(csdeData, csfeData, csmeData) {
    const startTime = Date.now();

    try {
      // Process data through CSFE engine if available
      let processedCsfeData = csfeData;
      if (this.csfeEngine && typeof this.csfeEngine.processData === 'function') {
        processedCsfeData = this.csfeEngine.processData(csfeData);
      }

      // Transform CSFE data
      const transformedCsfeData = this._transformCsfeData(processedCsfeData);

      // Process data through tensor core
      this.lastResult = this.tensorCore.processData(
        csdeData,
        transformedCsfeData,
        csmeData
      );

      // Update metrics
      this._updateMetrics(startTime);

      return this.lastResult;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Error processing data through CSFEAdapter:', error);
      }

      if (this.options.strictMode) {
        throw error;
      }

      return {
        error: error.message,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Transform CSFE data to format expected by tensor core
   * @param {Object} csfeData - CSFE data
   * @returns {Object} - Transformed CSFE data
   * @private
   */
  _transformCsfeData(csfeData) {
    return {
      risk: csfeData.riskScore || csfeData.risk || 0.5,
      policyCompliance: csfeData.complianceScore || csfeData.policyCompliance || csfeData.finance || 0.5,
      action: csfeData.recommendedAction || csfeData.action || 'allow',
      confidence: csfeData.confidenceScore || csfeData.confidence || 0.5
    };
  }

  /**
   * Update metrics
   * @param {number} startTime - Start time
   * @private
   */
  _updateMetrics(startTime) {
    const processingTime = Date.now() - startTime;

    // Update metrics
    this.metrics.processCount++;
    this.metrics.lastProcessingTime = processingTime;
    this.metrics.totalProcessingTime += processingTime;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.processCount;
  }

  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      tensorCoreMetrics: this.tensorCore.getMetrics()
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      processCount: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastProcessingTime: 0,
      startTime: Date.now()
    };

    this.tensorCore.resetMetrics();
  }

  /**
   * Get the last result
   * @returns {Object} - Last result
   */
  getLastResult() {
    return this.lastResult;
  }

  /**
   * Get the tensor core
   * @returns {Object} - Tensor core
   */
  getTensorCore() {
    return this.tensorCore;
  }
}

/**
 * Create a CSFE adapter
 * @param {Object} csfeEngine - CSFE engine instance
 * @param {Object} options - Configuration options
 * @returns {CSFEAdapter} - CSFE adapter instance
 */
function createCSFEAdapter(csfeEngine, options = {}) {
  return new CSFEAdapter(csfeEngine, options);
}

module.exports = {
  CSFEAdapter,
  createCSFEAdapter
};
