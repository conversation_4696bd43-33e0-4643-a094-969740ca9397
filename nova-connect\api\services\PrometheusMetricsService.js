/**
 * Prometheus Metrics Service
 * 
 * This service provides Prometheus metrics collection for NovaConnect UAC.
 * It integrates with Google Cloud Monitoring for metrics visualization and alerting.
 */

const promClient = require('prom-client');
const logger = require('../utils/logger');

class PrometheusMetricsService {
  constructor() {
    // Create a Registry to register metrics
    this.register = new promClient.Registry();
    
    // Add default metrics (CPU, memory, etc.)
    promClient.collectDefaultMetrics({ register: this.register });
    
    // Initialize custom metrics
    this._initializeMetrics();
    
    logger.info('Prometheus metrics service initialized');
  }
  
  /**
   * Initialize custom metrics
   */
  _initializeMetrics() {
    // HTTP request counter
    this.httpRequestsTotal = new promClient.Counter({
      name: 'novaconnect_http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code'],
      registers: [this.register],
    });
    
    // HTTP request duration histogram
    this.httpRequestDurationSeconds = new promClient.Histogram({
      name: 'novaconnect_http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
      registers: [this.register],
    });
    
    // API error counter
    this.apiErrorsTotal = new promClient.Counter({
      name: 'novaconnect_api_errors_total',
      help: 'Total number of API errors',
      labelNames: ['method', 'route', 'error_type'],
      registers: [this.register],
    });
    
    // Connector request counter
    this.connectorRequestsTotal = new promClient.Counter({
      name: 'novaconnect_connector_requests_total',
      help: 'Total number of connector requests',
      labelNames: ['connector', 'endpoint', 'status'],
      registers: [this.register],
    });
    
    // Connector request duration histogram
    this.connectorRequestDurationSeconds = new promClient.Histogram({
      name: 'novaconnect_connector_request_duration_seconds',
      help: 'Connector request duration in seconds',
      labelNames: ['connector', 'endpoint'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
      registers: [this.register],
    });
    
    // Data normalization duration histogram
    this.dataNormalizationDurationSeconds = new promClient.Histogram({
      name: 'novaconnect_data_normalization_duration_seconds',
      help: 'Data normalization duration in seconds',
      labelNames: ['connector', 'data_type'],
      buckets: [0.0001, 0.001, 0.01, 0.1, 0.5, 1],
      registers: [this.register],
    });
    
    // Remediation workflow counter
    this.remediationWorkflowsTotal = new promClient.Counter({
      name: 'novaconnect_remediation_workflows_total',
      help: 'Total number of remediation workflows',
      labelNames: ['connector', 'workflow_type', 'status'],
      registers: [this.register],
    });
    
    // Remediation workflow duration histogram
    this.remediationWorkflowDurationSeconds = new promClient.Histogram({
      name: 'novaconnect_remediation_workflow_duration_seconds',
      help: 'Remediation workflow duration in seconds',
      labelNames: ['connector', 'workflow_type'],
      buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60],
      registers: [this.register],
    });
    
    // Connector health gauge
    this.connectorHealthGauge = new promClient.Gauge({
      name: 'novaconnect_connector_health',
      help: 'Connector health status (1 = healthy, 0 = unhealthy)',
      labelNames: ['connector'],
      registers: [this.register],
    });
    
    // Connector response time gauge
    this.connectorResponseTimeGauge = new promClient.Gauge({
      name: 'novaconnect_connector_response_time',
      help: 'Connector response time in milliseconds',
      labelNames: ['connector', 'endpoint'],
      registers: [this.register],
    });
    
    // Authentication success counter
    this.authenticationSuccessTotal = new promClient.Counter({
      name: 'novaconnect_authentication_success_total',
      help: 'Total number of successful authentications',
      labelNames: ['auth_type'],
      registers: [this.register],
    });
    
    // Authentication failure counter
    this.authenticationFailureTotal = new promClient.Counter({
      name: 'novaconnect_authentication_failure_total',
      help: 'Total number of failed authentications',
      labelNames: ['auth_type', 'reason'],
      registers: [this.register],
    });
    
    // Cache hit counter
    this.cacheHitsTotal = new promClient.Counter({
      name: 'novaconnect_cache_hits_total',
      help: 'Total number of cache hits',
      labelNames: ['cache_type'],
      registers: [this.register],
    });
    
    // Cache miss counter
    this.cacheMissesTotal = new promClient.Counter({
      name: 'novaconnect_cache_misses_total',
      help: 'Total number of cache misses',
      labelNames: ['cache_type'],
      registers: [this.register],
    });
    
    // Active connections gauge
    this.activeConnectionsGauge = new promClient.Gauge({
      name: 'novaconnect_active_connections',
      help: 'Number of active connections',
      registers: [this.register],
    });
    
    // Rate limit exceeded counter
    this.rateLimitExceededTotal = new promClient.Counter({
      name: 'novaconnect_rate_limit_exceeded_total',
      help: 'Total number of rate limit exceeded events',
      labelNames: ['endpoint'],
      registers: [this.register],
    });
  }
  
  /**
   * Record HTTP request
   * @param {string} method - HTTP method
   * @param {string} route - Route path
   * @param {number} statusCode - HTTP status code
   * @param {number} duration - Request duration in seconds
   */
  recordHttpRequest(method, route, statusCode, duration) {
    this.httpRequestsTotal.inc({ method, route, status_code: statusCode });
    this.httpRequestDurationSeconds.observe({ method, route, status_code: statusCode }, duration);
  }
  
  /**
   * Record API error
   * @param {string} method - HTTP method
   * @param {string} route - Route path
   * @param {string} errorType - Error type
   */
  recordApiError(method, route, errorType) {
    this.apiErrorsTotal.inc({ method, route, error_type: errorType });
  }
  
  /**
   * Record connector request
   * @param {string} connector - Connector name
   * @param {string} endpoint - Endpoint name
   * @param {string} status - Status (success or error)
   * @param {number} duration - Request duration in seconds
   */
  recordConnectorRequest(connector, endpoint, status, duration) {
    this.connectorRequestsTotal.inc({ connector, endpoint, status });
    this.connectorRequestDurationSeconds.observe({ connector, endpoint }, duration);
  }
  
  /**
   * Record data normalization
   * @param {string} connector - Connector name
   * @param {string} dataType - Data type
   * @param {number} duration - Normalization duration in seconds
   */
  recordDataNormalization(connector, dataType, duration) {
    this.dataNormalizationDurationSeconds.observe({ connector, data_type: dataType }, duration);
  }
  
  /**
   * Record remediation workflow
   * @param {string} connector - Connector name
   * @param {string} workflowType - Workflow type
   * @param {string} status - Status (success or error)
   * @param {number} duration - Workflow duration in seconds
   */
  recordRemediationWorkflow(connector, workflowType, status, duration) {
    this.remediationWorkflowsTotal.inc({ connector, workflow_type: workflowType, status });
    this.remediationWorkflowDurationSeconds.observe({ connector, workflow_type: workflowType }, duration);
  }
  
  /**
   * Update connector health
   * @param {string} connector - Connector name
   * @param {boolean} isHealthy - Whether the connector is healthy
   */
  updateConnectorHealth(connector, isHealthy) {
    this.connectorHealthGauge.set({ connector }, isHealthy ? 1 : 0);
  }
  
  /**
   * Update connector response time
   * @param {string} connector - Connector name
   * @param {string} endpoint - Endpoint name
   * @param {number} responseTime - Response time in milliseconds
   */
  updateConnectorResponseTime(connector, endpoint, responseTime) {
    this.connectorResponseTimeGauge.set({ connector, endpoint }, responseTime);
  }
  
  /**
   * Record authentication success
   * @param {string} authType - Authentication type
   */
  recordAuthenticationSuccess(authType) {
    this.authenticationSuccessTotal.inc({ auth_type: authType });
  }
  
  /**
   * Record authentication failure
   * @param {string} authType - Authentication type
   * @param {string} reason - Failure reason
   */
  recordAuthenticationFailure(authType, reason) {
    this.authenticationFailureTotal.inc({ auth_type: authType, reason });
  }
  
  /**
   * Record cache hit
   * @param {string} cacheType - Cache type
   */
  recordCacheHit(cacheType) {
    this.cacheHitsTotal.inc({ cache_type: cacheType });
  }
  
  /**
   * Record cache miss
   * @param {string} cacheType - Cache type
   */
  recordCacheMiss(cacheType) {
    this.cacheMissesTotal.inc({ cache_type: cacheType });
  }
  
  /**
   * Update active connections
   * @param {number} count - Number of active connections
   */
  updateActiveConnections(count) {
    this.activeConnectionsGauge.set(count);
  }
  
  /**
   * Record rate limit exceeded
   * @param {string} endpoint - Endpoint
   */
  recordRateLimitExceeded(endpoint) {
    this.rateLimitExceededTotal.inc({ endpoint });
  }
  
  /**
   * Get metrics
   * @returns {Promise<string>} - Prometheus metrics
   */
  async getMetrics() {
    return this.register.metrics();
  }
  
  /**
   * Get content type
   * @returns {string} - Prometheus content type
   */
  getContentType() {
    return this.register.contentType;
  }
}

module.exports = new PrometheusMetricsService();
