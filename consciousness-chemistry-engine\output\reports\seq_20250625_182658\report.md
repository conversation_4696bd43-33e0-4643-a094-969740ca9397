# ConsciousNovaFold Analysis Report

## Sequence Information

* Sequence ID: seq_20250625_182658
* Sequence: ACDEFGHIKLMNPQRSTVWY

## Consciousness Metrics

* PSI Score: 0.660
* Fibonacci Analysis: {'residue_distances': [{'start': 0, 'end': 5, 'score': 0.9424458958871853, 'ratio': 1.6279151391748665, 'golden_ratio_diff': 0.006106886810582928}, {'start': 11, 'end': 13, 'score': 0.93137692291096, 'ratio': 1.6299555286132232, 'golden_ratio_diff': 0.007367916833773687}, {'start': 3, 'end': 6, 'score': 0.8074669396351137, 'ratio': 1.5794534574195105, 'golden_ratio_diff': 0.023844079666207767}, {'start': 3, 'end': 8, 'score': 0.5160939527147788, 'ratio': 1.466321993724482, 'golden_ratio_diff': 0.09376316942676012}, {'start': 0, 'end': 6, 'score': 0.5130050661372026, 'ratio': 1.4644342667681494, 'golden_ratio_diff': 0.0949298488472531}], 'torsion_angles': [{'start': 9, 'end': 13, 'score': 0.68981608114034, 'ratio': 1.6907907912078624, 'golden_ratio_diff': 0.044966176831785776}, {'start': 11, 'end': 15, 'score': 0.658950777960525, 'ratio': 1.534290369111944, 'golden_ratio_diff': 0.051756403277196786}, {'start': 8, 'end': 19, 'score': 0.6012469767123769, 'ratio': 1.5107243523132863, 'golden_ratio_diff': 0.06632100263821827}, {'start': 10, 'end': 14, 'score': 0.5570802833514243, 'ratio': 1.4893884097835368, 'golden_ratio_diff': 0.07950734030361786}, {'start': 0, 'end': 5, 'score': 0.5375640371796289, 'ratio': 1.4788436683948862, 'golden_ratio_diff': 0.08602434888438168}, {'start': 9, 'end': 14, 'score': 0.5275742831351392, 'ratio': 1.4731442753043522, 'golden_ratio_diff': 0.08954676752957802}], 'overall_score': 0.6620564742513341}
* Fibonacci Alignment: {'closest_fibonacci': 21, 'difference': 1, 'ratio': 0.9523809523809523, 'alignment_score': 0.9545454545454545}
* Trinity Validation: {'ners': np.float64(0.6606169422754002), 'nepi': 0.5389999999999999, 'nefc': 0.694, 'overall': np.float64(0.6341467769101601), 'passed': False}
* Trinity Report: {'scores': {'ners': np.float64(0.6606169422754002), 'nepi': 0.5389999999999999, 'nefc': 0.694, 'overall': np.float64(0.6341467769101601), 'passed': False}, 'thresholds': {'ners': 0.7, 'nepi': 0.5, 'nefc': 0.6}, 'weights': {'ners': 0.4, 'nepi': 0.3, 'nefc': 0.3}, 'validation': {'ners': {'score': np.float64(0.6606169422754002), 'threshold': 0.7, 'passed': np.False_, 'description': 'Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance.'}, 'nepi': {'score': 0.5389999999999999, 'threshold': 0.5, 'passed': True, 'description': 'Neural-Emotional Potential Index: Evaluates functional potential and adaptability.'}, 'nefc': {'score': 0.694, 'threshold': 0.6, 'passed': True, 'description': 'Neural-Emotional Field Coherence: Assesses field coherence and quantum effects.'}, 'overall': {'score': np.float64(0.6341467769101601), 'passed': False, 'description': 'Overall validation status based on all metrics.'}}}

## Structure Information

* Structure information not available
