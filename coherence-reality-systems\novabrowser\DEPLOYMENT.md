# 🚀 NovaBrowser Deployment Guide

## 🎯 **Deployment Overview**

**NovaBrowser supports multiple deployment strategies** for different use cases, from local development to enterprise-scale production deployments.

### **Deployment Options**
1. **Local Development** - Single machine setup
2. **Chrome Web Store** - Public extension distribution
3. **Enterprise Deployment** - Managed Chrome extension
4. **Self-hosted Backend** - Private API server
5. **Cloud Deployment** - Scalable infrastructure

---

## 🖥️ **Local Development Deployment**

### **Prerequisites**
- Windows 10/11 (tested)
- Chrome/Edge browser
- Admin rights (for backend)
- 8GB RAM minimum
- 2GB free disk space

### **Quick Start (5 minutes)**
```bash
# 1. Start backend
cd coherence-reality-systems
./nova-agent-api.exe

# 2. Install Chrome extension
# - Open chrome://extensions/
# - Enable Developer Mode
# - Load unpacked: chrome-extension/

# 3. Test browser UI
# - Open browser-ui.html in browser
# - Navigate to test sites

# 4. Verify functionality
# - Check extension icon shows coherence %
# - Test auto-fix on example.com
# - Confirm backend connectivity
```

### **Validation Checklist**
- ✅ Backend responds at http://localhost:8090/status
- ✅ Extension icon appears in Chrome toolbar
- ✅ Browser UI loads without errors
- ✅ Analysis works on test websites
- ✅ Auto-fix functionality operational

---

## 🌐 **Chrome Web Store Deployment**

### **Preparation Steps**
1. **Create developer account** - $5 registration fee
2. **Prepare extension package** - ZIP all extension files
3. **Create store listing** - Screenshots, description, etc.
4. **Submit for review** - Google review process (1-3 days)

### **Extension Package Structure**
```
novabrowser-extension.zip
├── manifest.json
├── content-script.js
├── popup.html
├── popup.js
├── background.js
├── overlay.css
├── icons/
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md
```

### **Store Listing Requirements**
```yaml
Name: "NovaBrowser - Coherence Gateway"
Description: "Real-time consciousness validation and accessibility auto-remediation"
Category: "Productivity"
Screenshots: 5 required (1280x800)
Privacy Policy: Required for data access
Permissions Justification: Explain <all_urls> usage
```

### **Review Process**
- **Automated checks** - Malware, policy compliance
- **Manual review** - Functionality, user experience
- **Timeline** - 1-3 business days typical
- **Approval rate** - 95%+ for compliant extensions

---

## 🏢 **Enterprise Deployment**

### **Google Admin Console Setup**
```yaml
# Chrome Enterprise Policy
ExtensionInstallForcelist:
  - "extension_id;https://clients2.google.com/service/update2/crx"

ExtensionSettings:
  extension_id:
    installation_mode: "force_installed"
    update_url: "https://clients2.google.com/service/update2/crx"
    minimum_version_required: "1.0.0"
```

### **Enterprise Backend Deployment**
```bash
# Windows Server deployment
# 1. Copy backend binary
copy nova-agent-api.exe C:\Program Files\NovaBrowser\

# 2. Install as Windows service
sc create NovaAgent binPath="C:\Program Files\NovaBrowser\nova-agent-api.exe"
sc config NovaAgent start=auto
sc start NovaAgent

# 3. Configure firewall
netsh advfirewall firewall add rule name="NovaAgent" dir=in action=allow protocol=TCP localport=8090

# 4. Verify service
curl http://localhost:8090/status
```

### **Group Policy Configuration**
```xml
<!-- Chrome extension policy -->
<policy name="ExtensionInstallForcelist" class="Machine" displayName="Force-installed extensions">
  <parentCategory ref="google:cat_googlechrome"/>
  <supportedOn ref="SUPPORTED_WIN7"/>
  <elements>
    <list id="ExtensionInstallForcelistDesc" key="Software\Policies\Google\Chrome\ExtensionInstallForcelist" valueName="1">
      <item displayName="NovaBrowser Extension">
        <value>
          <string>novabrowser_extension_id;https://clients2.google.com/service/update2/crx</string>
        </value>
      </item>
    </list>
  </elements>
</policy>
```

---

## ☁️ **Cloud Deployment**

### **AWS Deployment**
```yaml
# docker-compose.yml
version: '3.8'
services:
  nova-agent:
    image: novabrowser/nova-agent:latest
    ports:
      - "8090:8090"
    environment:
      - ENV=production
      - LOG_LEVEL=info
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

### **Kubernetes Deployment**
```yaml
# nova-agent-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nova-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nova-agent
  template:
    metadata:
      labels:
        app: nova-agent
    spec:
      containers:
      - name: nova-agent
        image: novabrowser/nova-agent:latest
        ports:
        - containerPort: 8090
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8090
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: nova-agent-service
spec:
  selector:
    app: nova-agent
  ports:
  - port: 80
    targetPort: 8090
  type: LoadBalancer
```

### **GCP Deployment**
```bash
# Google Cloud Run deployment
gcloud run deploy nova-agent \
  --image gcr.io/project-id/nova-agent:latest \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8090 \
  --memory 512Mi \
  --cpu 1 \
  --min-instances 1 \
  --max-instances 10
```

---

## 🔧 **Configuration Management**

### **Environment Variables**
```bash
# Backend configuration
export NOVA_PORT=8090
export NOVA_LOG_LEVEL=info
export NOVA_CORS_ORIGIN=*
export NOVA_WEBSOCKET_ENABLED=true
export NOVA_METRICS_ENABLED=true

# Extension configuration
export NOVA_BACKEND_URL=http://localhost:8090
export NOVA_AUTO_ANALYSIS=true
export NOVA_OVERLAY_ENABLED=true
export NOVA_NOTIFICATIONS=true
```

### **Configuration Files**
```json
// config/production.json
{
  "server": {
    "port": 8090,
    "host": "0.0.0.0",
    "cors": {
      "origin": "*",
      "methods": ["GET", "POST", "OPTIONS"]
    }
  },
  "analysis": {
    "coherence_threshold": 0.82,
    "auto_fix_enabled": true,
    "real_time_updates": true
  },
  "monitoring": {
    "metrics_enabled": true,
    "performance_tracking": true,
    "error_reporting": true
  }
}
```

---

## 📊 **Monitoring & Observability**

### **Health Checks**
```bash
# Backend health
curl -f http://localhost:8090/health || exit 1

# Extension health (via Chrome DevTools)
chrome.runtime.sendMessage({type: 'HEALTH_CHECK'})

# Performance monitoring
curl http://localhost:8090/metrics
```

### **Logging Configuration**
```json
{
  "logging": {
    "level": "info",
    "format": "json",
    "outputs": ["console", "file"],
    "file": {
      "path": "/var/log/nova-agent.log",
      "max_size": "100MB",
      "max_files": 5
    }
  }
}
```

### **Metrics Collection**
```javascript
// Prometheus metrics endpoint
app.get('/metrics', (req, res) => {
  const metrics = {
    nova_requests_total: requestCounter,
    nova_response_time_seconds: responseTimeHistogram,
    nova_coherence_score: coherenceGauge,
    nova_violations_fixed: violationsCounter
  };
  res.send(formatPrometheusMetrics(metrics));
});
```

---

## 🔐 **Security Considerations**

### **Production Security**
```yaml
# Security checklist
- [ ] HTTPS only in production
- [ ] API rate limiting enabled
- [ ] Input validation on all endpoints
- [ ] CORS properly configured
- [ ] No sensitive data in logs
- [ ] Regular security updates
- [ ] Vulnerability scanning
```

### **Network Security**
```bash
# Firewall configuration
# Allow only necessary ports
ufw allow 8090/tcp  # NovaAgent API
ufw allow 443/tcp   # HTTPS
ufw deny 22/tcp     # SSH (if not needed)

# SSL/TLS configuration
certbot --nginx -d novabrowser.example.com
```

### **Extension Security**
```json
// Content Security Policy
{
  "content_security_policy": {
    "extension_pages": "script-src 'self'; object-src 'self'"
  },
  "permissions": [
    "activeTab",
    "storage"
  ],
  "host_permissions": [
    "http://localhost:8090/*"
  ]
}
```

---

## 🚀 **Scaling Strategies**

### **Horizontal Scaling**
```yaml
# Load balancer configuration
upstream nova_backend {
  server nova-agent-1:8090;
  server nova-agent-2:8090;
  server nova-agent-3:8090;
}

server {
  listen 80;
  location / {
    proxy_pass http://nova_backend;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
  }
}
```

### **Auto-scaling**
```yaml
# Kubernetes HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nova-agent-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nova-agent
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

---

## 📈 **Deployment Validation**

### **Post-deployment Checklist**
- ✅ Backend API responding
- ✅ Extension installing correctly
- ✅ Performance targets met
- ✅ Security scans passed
- ✅ Monitoring active
- ✅ Logs flowing correctly
- ✅ Auto-scaling working
- ✅ Backup procedures tested

### **Rollback Procedures**
```bash
# Quick rollback steps
1. Stop new deployment
2. Restore previous backend version
3. Revert extension to previous version
4. Verify functionality
5. Monitor for issues
```

**NovaBrowser is designed for reliable, scalable deployment across all environments from development to enterprise production.**
