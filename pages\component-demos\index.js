import React from 'react';
import <PERSON> from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function ComponentDemos() {
  // SEO metadata
  const pageProps = {
    title: 'NovaFuse Core Component Demos - NovaFuse',
    description: 'Explore NovaFuse\'s core component demos showcasing our Universal API Connector, Partner Network, and Partner Empowerment Model.',
    keywords: 'NovaFuse demos, UAC demo, NovaFuse Partner Network, Partner Empowerment Model, core components',
    canonical: 'https://novafuse.io/component-demos',
    ogImage: '/images/component-demos-og.png'
  };

  // Component demo data
  const componentDemos = [
    {
      id: 'trinity-visualization-demo',
      title: 'Trinity Visualization Framework',
      description: 'Experience the "wheels within wheels" concept of NovaFuse\'s nested Trinity architecture through an interactive 3D visualization powered by Comphyology (Ψᶜ).',
      icon: '🔄',
      image: '/images/demos/trinity-visualization-demo.png',
      url: '/component-demos/trinity-visualization-demo',
      featured: true
    },
    {
      id: 'csde-trinity-equation-demo',
      title: 'CSDE Trinity Equation',
      description: 'Explore the mathematical foundation of NovaFuse\'s Cyber-Safety Dynamic Engine through an interactive visualization of the CSDE Trinity Equation: πG + ϕD + (ℏ+c⁻¹)R.',
      icon: '🧮',
      image: '/images/demos/csde-trinity-equation-demo.png',
      url: '/component-demos/csde-trinity-equation-demo',
      featured: true
    },
    {
      id: 'eighteen-eightytwo-demo',
      title: '18/82 Principle',
      description: 'Discover the fundamental 18/82 pattern where 18% of indicators account for 82% of predictive power, consistently observed across cybersecurity, medicine, and finance.',
      icon: '📊',
      image: '/images/demos/eighteen-eightytwo-demo.png',
      url: '/component-demos/eighteen-eightytwo-demo',
      featured: true
    },
    {
      id: 'universal-ripple-dashboard-demo',
      title: 'Universal Ripple Stack Dashboard',
      description: 'Monitor the real-time metrics and system health of the NovaFuse ecosystem through an interactive dashboard showing the operational status of the Universal Ripple Stack.',
      icon: '📈',
      image: '/images/demos/universal-ripple-dashboard-demo.png',
      url: '/component-demos/universal-ripple-dashboard-demo',
      featured: true
    },
    {
      id: 'comphyology-field-demo',
      title: 'Comphyology Field Visualization',
      description: 'Experience the Comphyology (Ψᶜ) field effects as they propagate across systems, creating the three-layer Ripple Effect that enhances all connected components.',
      icon: '🌊',
      image: '/images/demos/comphyology-field-demo.png',
      url: '/component-demos/comphyology-field-demo',
      featured: true
    },
    {
      id: 'uac-demo',
      title: 'Universal API Connector (UAC)',
      description: 'Experience the power of NovaFuse\'s patent-pending Universal API Connector technology. Connect, normalize, and control data across your entire ecosystem in real-time.',
      icon: '🔌',
      image: '/images/demos/uac-demo.png',
      url: '/component-demos/uac-demo',
      featured: true
    },
    {
      id: 'partner-network-demo',
      title: 'NovaFuse Partner Network',
      description: 'Discover the NovaFuse Partner Network that organizes the GRC landscape into a structured, scalable platform for partners and developers.',
      icon: '🧩',
      image: '/images/demos/partner-network-demo.png',
      url: '/component-demos/partner-network-demo',
      featured: true
    },
    {
      id: 'partner-empowerment-demo',
      title: '18/82 Partner Empowerment Model',
      description: 'Experience NovaFuse\'s revolutionary 18/82 Partner Empowerment Model with interactive calculators showing how it transforms the GRC landscape with unprecedented revenue sharing.',
      icon: '💰',
      image: '/images/demos/partner-empowerment-demo.png',
      url: '/component-demos/partner-empowerment-demo',
      featured: true
    },
    {
      id: 'novaconcierge-demo',
      title: 'NovaConcierge AI',
      description: 'Meet CiCi, your API integration assistant. Experience how NovaConcierge AI simplifies complex integration tasks and provides real-time guidance.',
      icon: '🤖',
      image: '/images/demos/novaconcierge-demo.png',
      url: '/component-demos/novaconcierge-demo',
      comingSoon: true
    },
    {
      id: 'compliance-war-room-demo',
      title: 'Compliance War Room',
      description: 'Experience NovaFuse\'s live Compliance War Room where our engineers solve real compliance challenges in real-time.',
      icon: '🔥',
      image: '/images/demos/compliance-war-room-demo.png',
      url: '/component-demos/compliance-war-room-demo',
      comingSoon: true
    },
    {
      id: 'solutions-marketplace-demo',
      title: 'Solutions Marketplace',
      description: 'Explore NovaFuse\'s Solutions Marketplace with plug-and-play modules for extending your compliance capabilities.',
      icon: '🛒',
      image: '/images/demos/app-store-demo.png',
      url: '/component-demos/app-store-demo',
      comingSoon: true
    }
  ];

  // Featured components
  const featuredComponents = componentDemos.filter(demo => demo.featured);

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Component Demos', items: [
      { label: 'All Components', href: '/component-demos' },
      { label: 'Featured Components', href: '#featured' },
      { label: 'Coming Soon', href: '#coming-soon' }
    ]},
    { type: 'category', label: 'Featured Components', items:
      featuredComponents.map(component => ({
        label: component.title,
        href: component.url
      }))
    },
    { type: 'category', label: 'Related Demos', items: [
      { label: 'Partner Demos', href: '/partner-demos' },
      { label: 'API Demos', href: '/api-demos' },
      { label: 'Demo Hub', href: '/demo-hub' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <div className="component-demos">
        {/* Header Section */}
        <div className="bg-gradient-to-r from-blue-900 to-indigo-900 text-white rounded-lg p-8 mb-8">
          <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">NovaFuse Core Component Demos</h1>
          <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
            Explore NovaFuse's revolutionary core components that power our GRC platform and partner ecosystem.
          </p>
          <p className="text-center text-blue-200">
            These components form the foundation of NovaFuse's "Trust, Automated" vision.
          </p>
        </div>

        {/* Featured Components Section */}
        <div id="featured" className="mb-12 scroll-mt-16">
          <h2 className="text-2xl font-bold mb-6">Featured Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {featuredComponents.map(component => (
              <div key={component.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-blue-500 transition-all">
                {/* Component Image/Icon */}
                <div className="h-40 bg-gray-900 relative">
                  {component.image ? (
                    <img
                      src={component.image}
                      alt={component.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-900 to-purple-900">
                      <span className="text-5xl">{component.icon}</span>
                    </div>
                  )}

                  {/* Featured Badge */}
                  <div className="absolute top-2 right-2 bg-blue-600 px-2 py-1 rounded text-xs font-bold">
                    FEATURED
                  </div>
                </div>

                {/* Component Info */}
                <div className="p-5">
                  <h3 className="text-xl font-bold mb-2">{component.title}</h3>
                  <p className="text-gray-400 text-sm mb-4">{component.description}</p>

                  {/* Action Button */}
                  <Link href={component.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                    View Demo
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Coming Soon Section */}
        <div id="coming-soon" className="mb-12 scroll-mt-16">
          <h2 className="text-2xl font-bold mb-6">Coming Soon</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {componentDemos.filter(demo => demo.comingSoon).map(component => (
              <div key={component.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700">
                {/* Component Image/Icon */}
                <div className="h-40 bg-gray-900 relative">
                  {component.image ? (
                    <div className="relative w-full h-full">
                      <img
                        src={component.image}
                        alt={component.title}
                        className="w-full h-full object-cover opacity-50"
                      />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-blue-900 bg-opacity-80 px-4 py-2 rounded-full text-sm font-bold">
                          COMING SOON
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-r from-blue-900 to-purple-900">
                      <span className="text-5xl opacity-50">{component.icon}</span>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="bg-blue-900 bg-opacity-80 px-4 py-2 rounded-full text-sm font-bold">
                          COMING SOON
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Component Info */}
                <div className="p-5">
                  <h3 className="text-xl font-bold mb-2">{component.title}</h3>
                  <p className="text-gray-400 text-sm mb-4">{component.description}</p>

                  {/* Action Button */}
                  <button className="block w-full bg-gray-700 text-gray-400 text-center py-2 rounded-lg font-medium cursor-not-allowed">
                    Coming Soon
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* All Components Section */}
        <div>
          <h2 className="text-2xl font-bold mb-6">All Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {componentDemos.map(component => (
              <div key={component.id} className="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-blue-500 transition-all">
                {/* Component Info */}
                <div className="p-5">
                  <div className="flex items-center mb-3">
                    <span className="text-3xl mr-3">{component.icon}</span>
                    <h3 className="text-xl font-bold">{component.title}</h3>
                  </div>

                  <p className="text-gray-400 text-sm mb-4">{component.description}</p>

                  {/* Action Button */}
                  {component.comingSoon ? (
                    <button className="block w-full bg-gray-700 text-gray-400 text-center py-2 rounded-lg font-medium cursor-not-allowed">
                      Coming Soon
                    </button>
                  ) : (
                    <Link href={component.url} className="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded-lg font-medium">
                      View Demo
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-12 bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-3">Ready to Experience NovaFuse?</h2>
          <p className="text-lg mb-6 max-w-3xl mx-auto">
            Join the NovaFuse Partner Network and be among the first to leverage the power of NovaFuse's revolutionary components.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/partner-program" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold">
              Become a Partner
            </Link>
            <Link href="/contact" className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900">
              Schedule a Demo
            </Link>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
