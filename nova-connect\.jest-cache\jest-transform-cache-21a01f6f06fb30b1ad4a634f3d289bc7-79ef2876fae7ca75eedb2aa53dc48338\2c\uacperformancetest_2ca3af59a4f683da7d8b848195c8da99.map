{"version": 3, "names": ["performance", "require", "TransformationEngine", "RemediationEngine", "ConnectorRegistry", "THRESHOLDS", "transformation", "singleTransform", "batchTransform", "complexTransform", "remediation", "simpleRemediation", "complexRemediation", "connector", "registration", "execution", "sampleData", "id", "type", "attributes", "firstName", "lastName", "email", "createdAt", "updatedAt", "status", "roles", "preferences", "theme", "notifications", "push", "relationships", "organization", "data", "generateBatchData", "count", "items", "i", "describe", "transformationEngine", "beforeEach", "enableMetrics", "enableCaching", "test", "rules", "source", "target", "transform", "startTime", "now", "endTime", "averageTime", "console", "log", "toFixed", "expect", "toBeLessThan", "batchData", "totalTime", "complexRules", "startTimeMiss", "endTimeMiss", "missTime", "startTimeHit", "endTimeHit", "hitTime", "remediationEngine", "maxConcurrentRemediations", "registerAction", "parameters", "success", "Promise", "resolve", "setTimeout", "delay", "scenario", "framework", "control", "resource", "finding", "severity", "remediationSteps", "action", "param1", "param2", "executeRemediation", "param3", "connectorRegistry", "registerConnector", "execute", "operation", "params", "result", "executeConnector", "path", "filter"], "sources": ["uac-performance.test.js"], "sourcesContent": ["/**\n * NovaConnect UAC Performance Tests\n * \n * This test suite validates the performance of the Universal API Connector.\n */\n\nconst { performance } = require('perf_hooks');\nconst { TransformationEngine } = require('../../src/engines/transformation-engine');\nconst { RemediationEngine } = require('../../src/engines/remediation-engine');\nconst { ConnectorRegistry } = require('../../src/registry/connector-registry');\n\n// Performance thresholds\nconst THRESHOLDS = {\n  transformation: {\n    singleTransform: 1, // ms\n    batchTransform: 5, // ms\n    complexTransform: 10 // ms\n  },\n  remediation: {\n    simpleRemediation: 50, // ms\n    complexRemediation: 200 // ms\n  },\n  connector: {\n    registration: 5, // ms\n    execution: 20 // ms\n  }\n};\n\n// Test data\nconst sampleData = {\n  id: '12345',\n  type: 'user',\n  attributes: {\n    firstName: 'John',\n    lastName: 'Doe',\n    email: '<EMAIL>',\n    createdAt: '2023-01-01T00:00:00Z',\n    updatedAt: '2023-01-02T00:00:00Z',\n    status: 'active',\n    roles: ['user', 'admin'],\n    preferences: {\n      theme: 'dark',\n      notifications: {\n        email: true,\n        push: false\n      }\n    }\n  },\n  relationships: {\n    organization: {\n      data: {\n        id: '67890',\n        type: 'organization'\n      }\n    }\n  }\n};\n\n// Generate large batch data\nfunction generateBatchData(count) {\n  const items = [];\n  for (let i = 0; i < count; i++) {\n    items.push({\n      ...sampleData,\n      id: `user-${i}`,\n      attributes: {\n        ...sampleData.attributes,\n        firstName: `User${i}`,\n        email: `user${i}@example.com`\n      }\n    });\n  }\n  return { data: items };\n}\n\ndescribe('NovaConnect UAC Performance Tests', () => {\n  describe('TransformationEngine Performance', () => {\n    let transformationEngine;\n    \n    beforeEach(() => {\n      transformationEngine = new TransformationEngine({\n        enableMetrics: true,\n        enableCaching: true\n      });\n    });\n    \n    test('Single transformation performance', () => {\n      const rules = [\n        {\n          source: 'id',\n          target: 'userId',\n          transform: 'toString'\n        },\n        {\n          source: 'attributes.firstName',\n          target: 'firstName',\n          transform: 'uppercase'\n        },\n        {\n          source: 'attributes.lastName',\n          target: 'lastName',\n          transform: 'uppercase'\n        },\n        {\n          source: 'attributes.email',\n          target: 'email',\n          transform: 'lowercase'\n        },\n        {\n          source: 'attributes.createdAt',\n          target: 'createdTimestamp',\n          transform: 'isoToUnix'\n        }\n      ];\n      \n      const startTime = performance.now();\n      \n      // Run transformation multiple times to get average\n      for (let i = 0; i < 1000; i++) {\n        transformationEngine.transform(sampleData, rules);\n      }\n      \n      const endTime = performance.now();\n      const averageTime = (endTime - startTime) / 1000;\n      \n      console.log(`Single transformation average time: ${averageTime.toFixed(3)}ms`);\n      expect(averageTime).toBeLessThan(THRESHOLDS.transformation.singleTransform);\n    });\n    \n    test('Batch transformation performance', () => {\n      const batchData = generateBatchData(100);\n      \n      const rules = [\n        {\n          source: 'data[*].id',\n          target: 'users[*].userId',\n          transform: 'toString'\n        },\n        {\n          source: 'data[*].attributes.firstName',\n          target: 'users[*].firstName',\n          transform: 'uppercase'\n        },\n        {\n          source: 'data[*].attributes.lastName',\n          target: 'users[*].lastName',\n          transform: 'uppercase'\n        },\n        {\n          source: 'data[*].attributes.email',\n          target: 'users[*].email',\n          transform: 'lowercase'\n        },\n        {\n          source: 'data[*].attributes.createdAt',\n          target: 'users[*].createdTimestamp',\n          transform: 'isoToUnix'\n        }\n      ];\n      \n      const startTime = performance.now();\n      \n      // Run batch transformation\n      transformationEngine.batchTransform(batchData, rules);\n      \n      const endTime = performance.now();\n      const totalTime = endTime - startTime;\n      \n      console.log(`Batch transformation time (100 items): ${totalTime.toFixed(3)}ms`);\n      expect(totalTime).toBeLessThan(THRESHOLDS.transformation.batchTransform);\n    });\n    \n    test('Complex transformation performance', () => {\n      const complexRules = [\n        {\n          source: 'id',\n          target: 'userId',\n          transform: 'toString'\n        },\n        {\n          source: 'attributes.firstName',\n          target: 'name.first',\n          transform: 'uppercase'\n        },\n        {\n          source: 'attributes.lastName',\n          target: 'name.last',\n          transform: 'uppercase'\n        },\n        {\n          source: 'attributes.email',\n          target: 'contact.email',\n          transform: 'lowercase'\n        },\n        {\n          source: 'attributes.createdAt',\n          target: 'timestamps.created',\n          transform: 'isoToUnix'\n        },\n        {\n          source: 'attributes.updatedAt',\n          target: 'timestamps.updated',\n          transform: 'isoToUnix'\n        },\n        {\n          source: 'attributes.roles',\n          target: 'security.roles',\n          transform: 'join'\n        },\n        {\n          source: 'attributes.preferences',\n          target: 'settings',\n          transform: 'toJson'\n        },\n        {\n          source: 'relationships.organization.data.id',\n          target: 'organization.id',\n          transform: 'toString'\n        },\n        {\n          source: 'relationships.organization.data.type',\n          target: 'organization.type',\n          transform: 'lowercase'\n        }\n      ];\n      \n      const startTime = performance.now();\n      \n      // Run complex transformation multiple times\n      for (let i = 0; i < 100; i++) {\n        transformationEngine.transform(sampleData, complexRules);\n      }\n      \n      const endTime = performance.now();\n      const averageTime = (endTime - startTime) / 100;\n      \n      console.log(`Complex transformation average time: ${averageTime.toFixed(3)}ms`);\n      expect(averageTime).toBeLessThan(THRESHOLDS.transformation.complexTransform);\n    });\n    \n    test('Transformation cache performance', () => {\n      const rules = [\n        {\n          source: 'id',\n          target: 'userId',\n          transform: 'toString'\n        },\n        {\n          source: 'attributes.firstName',\n          target: 'firstName',\n          transform: 'uppercase'\n        },\n        {\n          source: 'attributes.lastName',\n          target: 'lastName',\n          transform: 'uppercase'\n        }\n      ];\n      \n      // First transformation (cache miss)\n      const startTimeMiss = performance.now();\n      transformationEngine.transform(sampleData, rules);\n      const endTimeMiss = performance.now();\n      const missTime = endTimeMiss - startTimeMiss;\n      \n      // Second transformation (cache hit)\n      const startTimeHit = performance.now();\n      transformationEngine.transform(sampleData, rules);\n      const endTimeHit = performance.now();\n      const hitTime = endTimeHit - startTimeHit;\n      \n      console.log(`Transformation cache miss time: ${missTime.toFixed(3)}ms`);\n      console.log(`Transformation cache hit time: ${hitTime.toFixed(3)}ms`);\n      \n      // Cache hit should be significantly faster than miss\n      expect(hitTime).toBeLessThan(missTime * 0.5);\n    });\n  });\n  \n  describe('RemediationEngine Performance', () => {\n    let remediationEngine;\n    \n    beforeEach(() => {\n      remediationEngine = new RemediationEngine({\n        enableMetrics: true,\n        maxConcurrentRemediations: 10\n      });\n      \n      // Register test actions\n      remediationEngine.registerAction('test-action', async ({ parameters }) => {\n        return { success: true, parameters };\n      });\n      \n      remediationEngine.registerAction('delay-action', async ({ parameters }) => {\n        await new Promise(resolve => setTimeout(resolve, parameters.delay || 10));\n        return { success: true, parameters };\n      });\n    });\n    \n    test('Simple remediation performance', async () => {\n      const scenario = {\n        id: 'test-remediation',\n        framework: 'test-framework',\n        control: 'test-control',\n        resource: {\n          id: 'resource-123',\n          type: 'test-resource'\n        },\n        finding: {\n          id: 'finding-123',\n          severity: 'medium'\n        },\n        remediationSteps: [\n          {\n            id: 'step-1',\n            action: 'test-action',\n            parameters: {\n              param1: 'value1',\n              param2: 'value2'\n            }\n          }\n        ]\n      };\n      \n      const startTime = performance.now();\n      \n      await remediationEngine.executeRemediation(scenario);\n      \n      const endTime = performance.now();\n      const totalTime = endTime - startTime;\n      \n      console.log(`Simple remediation time: ${totalTime.toFixed(3)}ms`);\n      expect(totalTime).toBeLessThan(THRESHOLDS.remediation.simpleRemediation);\n    });\n    \n    test('Complex remediation performance', async () => {\n      const scenario = {\n        id: 'complex-remediation',\n        framework: 'test-framework',\n        control: 'test-control',\n        resource: {\n          id: 'resource-123',\n          type: 'test-resource'\n        },\n        finding: {\n          id: 'finding-123',\n          severity: 'high'\n        },\n        remediationSteps: [\n          {\n            id: 'step-1',\n            action: 'test-action',\n            parameters: {\n              param1: 'value1'\n            }\n          },\n          {\n            id: 'step-2',\n            action: 'delay-action',\n            parameters: {\n              delay: 50\n            }\n          },\n          {\n            id: 'step-3',\n            action: 'test-action',\n            parameters: {\n              param2: 'value2'\n            }\n          },\n          {\n            id: 'step-4',\n            action: 'delay-action',\n            parameters: {\n              delay: 50\n            }\n          },\n          {\n            id: 'step-5',\n            action: 'test-action',\n            parameters: {\n              param3: 'value3'\n            }\n          }\n        ]\n      };\n      \n      const startTime = performance.now();\n      \n      await remediationEngine.executeRemediation(scenario);\n      \n      const endTime = performance.now();\n      const totalTime = endTime - startTime;\n      \n      console.log(`Complex remediation time: ${totalTime.toFixed(3)}ms`);\n      expect(totalTime).toBeLessThan(THRESHOLDS.remediation.complexRemediation);\n    });\n  });\n  \n  describe('ConnectorRegistry Performance', () => {\n    let connectorRegistry;\n    \n    beforeEach(() => {\n      connectorRegistry = new ConnectorRegistry();\n      \n      // Register test connector\n      connectorRegistry.registerConnector('test-connector', {\n        execute: async (operation, params) => {\n          return { operation, params, result: 'success' };\n        }\n      });\n    });\n    \n    test('Connector registration performance', () => {\n      const startTime = performance.now();\n      \n      // Register multiple connectors\n      for (let i = 0; i < 100; i++) {\n        connectorRegistry.registerConnector(`connector-${i}`, {\n          execute: async (operation, params) => {\n            return { operation, params, result: `success-${i}` };\n          }\n        });\n      }\n      \n      const endTime = performance.now();\n      const totalTime = endTime - startTime;\n      const averageTime = totalTime / 100;\n      \n      console.log(`Connector registration average time: ${averageTime.toFixed(3)}ms`);\n      expect(averageTime).toBeLessThan(THRESHOLDS.connector.registration);\n    });\n    \n    test('Connector execution performance', async () => {\n      const startTime = performance.now();\n      \n      // Execute connector multiple times\n      for (let i = 0; i < 100; i++) {\n        await connectorRegistry.executeConnector('test-connector', 'GET', {\n          path: `/resource/${i}`,\n          params: { filter: 'active' }\n        });\n      }\n      \n      const endTime = performance.now();\n      const totalTime = endTime - startTime;\n      const averageTime = totalTime / 100;\n      \n      console.log(`Connector execution average time: ${averageTime.toFixed(3)}ms`);\n      expect(averageTime).toBeLessThan(THRESHOLDS.connector.execution);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAY,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAM;EAAEC;AAAqB,CAAC,GAAGD,OAAO,CAAC,yCAAyC,CAAC;AACnF,MAAM;EAAEE;AAAkB,CAAC,GAAGF,OAAO,CAAC,sCAAsC,CAAC;AAC7E,MAAM;EAAEG;AAAkB,CAAC,GAAGH,OAAO,CAAC,uCAAuC,CAAC;;AAE9E;AACA,MAAMI,UAAU,GAAG;EACjBC,cAAc,EAAE;IACdC,eAAe,EAAE,CAAC;IAAE;IACpBC,cAAc,EAAE,CAAC;IAAE;IACnBC,gBAAgB,EAAE,EAAE,CAAC;EACvB,CAAC;EACDC,WAAW,EAAE;IACXC,iBAAiB,EAAE,EAAE;IAAE;IACvBC,kBAAkB,EAAE,GAAG,CAAC;EAC1B,CAAC;EACDC,SAAS,EAAE;IACTC,YAAY,EAAE,CAAC;IAAE;IACjBC,SAAS,EAAE,EAAE,CAAC;EAChB;AACF,CAAC;;AAED;AACA,MAAMC,UAAU,GAAG;EACjBC,EAAE,EAAE,OAAO;EACXC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE;IACVC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,sBAAsB;IAC7BC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE,sBAAsB;IACjCC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IACxBC,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACbC,aAAa,EAAE;QACbP,KAAK,EAAE,IAAI;QACXQ,IAAI,EAAE;MACR;IACF;EACF,CAAC;EACDC,aAAa,EAAE;IACbC,YAAY,EAAE;MACZC,IAAI,EAAE;QACJhB,EAAE,EAAE,OAAO;QACXC,IAAI,EAAE;MACR;IACF;EACF;AACF,CAAC;;AAED;AACA,SAASgB,iBAAiBA,CAACC,KAAK,EAAE;EAChC,MAAMC,KAAK,GAAG,EAAE;EAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAEE,CAAC,EAAE,EAAE;IAC9BD,KAAK,CAACN,IAAI,CAAC;MACT,GAAGd,UAAU;MACbC,EAAE,EAAE,QAAQoB,CAAC,EAAE;MACflB,UAAU,EAAE;QACV,GAAGH,UAAU,CAACG,UAAU;QACxBC,SAAS,EAAE,OAAOiB,CAAC,EAAE;QACrBf,KAAK,EAAE,OAAOe,CAAC;MACjB;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IAAEJ,IAAI,EAAEG;EAAM,CAAC;AACxB;AAEAE,QAAQ,CAAC,mCAAmC,EAAE,MAAM;EAClDA,QAAQ,CAAC,kCAAkC,EAAE,MAAM;IACjD,IAAIC,oBAAoB;IAExBC,UAAU,CAAC,MAAM;MACfD,oBAAoB,GAAG,IAAIrC,oBAAoB,CAAC;QAC9CuC,aAAa,EAAE,IAAI;QACnBC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFC,IAAI,CAAC,mCAAmC,EAAE,MAAM;MAC9C,MAAMC,KAAK,GAAG,CACZ;QACEC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,qBAAqB;QAC7BC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,kBAAkB;QAC1BC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,kBAAkB;QAC1BC,SAAS,EAAE;MACb,CAAC,CACF;MAED,MAAMC,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;MAEnC;MACA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,EAAEA,CAAC,EAAE,EAAE;QAC7BE,oBAAoB,CAACQ,SAAS,CAAC/B,UAAU,EAAE4B,KAAK,CAAC;MACnD;MAEA,MAAMM,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAME,WAAW,GAAG,CAACD,OAAO,GAAGF,SAAS,IAAI,IAAI;MAEhDI,OAAO,CAACC,GAAG,CAAC,uCAAuCF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAC9EC,MAAM,CAACJ,WAAW,CAAC,CAACK,YAAY,CAACnD,UAAU,CAACC,cAAc,CAACC,eAAe,CAAC;IAC7E,CAAC,CAAC;IAEFoC,IAAI,CAAC,kCAAkC,EAAE,MAAM;MAC7C,MAAMc,SAAS,GAAGvB,iBAAiB,CAAC,GAAG,CAAC;MAExC,MAAMU,KAAK,GAAG,CACZ;QACEC,MAAM,EAAE,YAAY;QACpBC,MAAM,EAAE,iBAAiB;QACzBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,8BAA8B;QACtCC,MAAM,EAAE,oBAAoB;QAC5BC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,6BAA6B;QACrCC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,0BAA0B;QAClCC,MAAM,EAAE,gBAAgB;QACxBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,8BAA8B;QACtCC,MAAM,EAAE,2BAA2B;QACnCC,SAAS,EAAE;MACb,CAAC,CACF;MAED,MAAMC,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;MAEnC;MACAV,oBAAoB,CAAC/B,cAAc,CAACiD,SAAS,EAAEb,KAAK,CAAC;MAErD,MAAMM,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAMS,SAAS,GAAGR,OAAO,GAAGF,SAAS;MAErCI,OAAO,CAACC,GAAG,CAAC,0CAA0CK,SAAS,CAACJ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAC/EC,MAAM,CAACG,SAAS,CAAC,CAACF,YAAY,CAACnD,UAAU,CAACC,cAAc,CAACE,cAAc,CAAC;IAC1E,CAAC,CAAC;IAEFmC,IAAI,CAAC,oCAAoC,EAAE,MAAM;MAC/C,MAAMgB,YAAY,GAAG,CACnB;QACEd,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,YAAY;QACpBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,qBAAqB;QAC7BC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,kBAAkB;QAC1BC,MAAM,EAAE,eAAe;QACvBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,oBAAoB;QAC5BC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,oBAAoB;QAC5BC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,kBAAkB;QAC1BC,MAAM,EAAE,gBAAgB;QACxBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,wBAAwB;QAChCC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,oCAAoC;QAC5CC,MAAM,EAAE,iBAAiB;QACzBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sCAAsC;QAC9CC,MAAM,EAAE,mBAAmB;QAC3BC,SAAS,EAAE;MACb,CAAC,CACF;MAED,MAAMC,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;MAEnC;MACA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BE,oBAAoB,CAACQ,SAAS,CAAC/B,UAAU,EAAE2C,YAAY,CAAC;MAC1D;MAEA,MAAMT,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAME,WAAW,GAAG,CAACD,OAAO,GAAGF,SAAS,IAAI,GAAG;MAE/CI,OAAO,CAACC,GAAG,CAAC,wCAAwCF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAC/EC,MAAM,CAACJ,WAAW,CAAC,CAACK,YAAY,CAACnD,UAAU,CAACC,cAAc,CAACG,gBAAgB,CAAC;IAC9E,CAAC,CAAC;IAEFkC,IAAI,CAAC,kCAAkC,EAAE,MAAM;MAC7C,MAAMC,KAAK,GAAG,CACZ;QACEC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,sBAAsB;QAC9BC,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE;MACb,CAAC,EACD;QACEF,MAAM,EAAE,qBAAqB;QAC7BC,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE;MACb,CAAC,CACF;;MAED;MACA,MAAMa,aAAa,GAAG5D,WAAW,CAACiD,GAAG,CAAC,CAAC;MACvCV,oBAAoB,CAACQ,SAAS,CAAC/B,UAAU,EAAE4B,KAAK,CAAC;MACjD,MAAMiB,WAAW,GAAG7D,WAAW,CAACiD,GAAG,CAAC,CAAC;MACrC,MAAMa,QAAQ,GAAGD,WAAW,GAAGD,aAAa;;MAE5C;MACA,MAAMG,YAAY,GAAG/D,WAAW,CAACiD,GAAG,CAAC,CAAC;MACtCV,oBAAoB,CAACQ,SAAS,CAAC/B,UAAU,EAAE4B,KAAK,CAAC;MACjD,MAAMoB,UAAU,GAAGhE,WAAW,CAACiD,GAAG,CAAC,CAAC;MACpC,MAAMgB,OAAO,GAAGD,UAAU,GAAGD,YAAY;MAEzCX,OAAO,CAACC,GAAG,CAAC,mCAAmCS,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACvEF,OAAO,CAACC,GAAG,CAAC,kCAAkCY,OAAO,CAACX,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;MAErE;MACAC,MAAM,CAACU,OAAO,CAAC,CAACT,YAAY,CAACM,QAAQ,GAAG,GAAG,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFxB,QAAQ,CAAC,+BAA+B,EAAE,MAAM;IAC9C,IAAI4B,iBAAiB;IAErB1B,UAAU,CAAC,MAAM;MACf0B,iBAAiB,GAAG,IAAI/D,iBAAiB,CAAC;QACxCsC,aAAa,EAAE,IAAI;QACnB0B,yBAAyB,EAAE;MAC7B,CAAC,CAAC;;MAEF;MACAD,iBAAiB,CAACE,cAAc,CAAC,aAAa,EAAE,OAAO;QAAEC;MAAW,CAAC,KAAK;QACxE,OAAO;UAAEC,OAAO,EAAE,IAAI;UAAED;QAAW,CAAC;MACtC,CAAC,CAAC;MAEFH,iBAAiB,CAACE,cAAc,CAAC,cAAc,EAAE,OAAO;QAAEC;MAAW,CAAC,KAAK;QACzE,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEH,UAAU,CAACK,KAAK,IAAI,EAAE,CAAC,CAAC;QACzE,OAAO;UAAEJ,OAAO,EAAE,IAAI;UAAED;QAAW,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF1B,IAAI,CAAC,gCAAgC,EAAE,YAAY;MACjD,MAAMgC,QAAQ,GAAG;QACf1D,EAAE,EAAE,kBAAkB;QACtB2D,SAAS,EAAE,gBAAgB;QAC3BC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;UACR7D,EAAE,EAAE,cAAc;UAClBC,IAAI,EAAE;QACR,CAAC;QACD6D,OAAO,EAAE;UACP9D,EAAE,EAAE,aAAa;UACjB+D,QAAQ,EAAE;QACZ,CAAC;QACDC,gBAAgB,EAAE,CAChB;UACEhE,EAAE,EAAE,QAAQ;UACZiE,MAAM,EAAE,aAAa;UACrBb,UAAU,EAAE;YACVc,MAAM,EAAE,QAAQ;YAChBC,MAAM,EAAE;UACV;QACF,CAAC;MAEL,CAAC;MAED,MAAMpC,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;MAEnC,MAAMiB,iBAAiB,CAACmB,kBAAkB,CAACV,QAAQ,CAAC;MAEpD,MAAMzB,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAMS,SAAS,GAAGR,OAAO,GAAGF,SAAS;MAErCI,OAAO,CAACC,GAAG,CAAC,4BAA4BK,SAAS,CAACJ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MACjEC,MAAM,CAACG,SAAS,CAAC,CAACF,YAAY,CAACnD,UAAU,CAACK,WAAW,CAACC,iBAAiB,CAAC;IAC1E,CAAC,CAAC;IAEFgC,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD,MAAMgC,QAAQ,GAAG;QACf1D,EAAE,EAAE,qBAAqB;QACzB2D,SAAS,EAAE,gBAAgB;QAC3BC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;UACR7D,EAAE,EAAE,cAAc;UAClBC,IAAI,EAAE;QACR,CAAC;QACD6D,OAAO,EAAE;UACP9D,EAAE,EAAE,aAAa;UACjB+D,QAAQ,EAAE;QACZ,CAAC;QACDC,gBAAgB,EAAE,CAChB;UACEhE,EAAE,EAAE,QAAQ;UACZiE,MAAM,EAAE,aAAa;UACrBb,UAAU,EAAE;YACVc,MAAM,EAAE;UACV;QACF,CAAC,EACD;UACElE,EAAE,EAAE,QAAQ;UACZiE,MAAM,EAAE,cAAc;UACtBb,UAAU,EAAE;YACVK,KAAK,EAAE;UACT;QACF,CAAC,EACD;UACEzD,EAAE,EAAE,QAAQ;UACZiE,MAAM,EAAE,aAAa;UACrBb,UAAU,EAAE;YACVe,MAAM,EAAE;UACV;QACF,CAAC,EACD;UACEnE,EAAE,EAAE,QAAQ;UACZiE,MAAM,EAAE,cAAc;UACtBb,UAAU,EAAE;YACVK,KAAK,EAAE;UACT;QACF,CAAC,EACD;UACEzD,EAAE,EAAE,QAAQ;UACZiE,MAAM,EAAE,aAAa;UACrBb,UAAU,EAAE;YACViB,MAAM,EAAE;UACV;QACF,CAAC;MAEL,CAAC;MAED,MAAMtC,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;MAEnC,MAAMiB,iBAAiB,CAACmB,kBAAkB,CAACV,QAAQ,CAAC;MAEpD,MAAMzB,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAMS,SAAS,GAAGR,OAAO,GAAGF,SAAS;MAErCI,OAAO,CAACC,GAAG,CAAC,6BAA6BK,SAAS,CAACJ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAClEC,MAAM,CAACG,SAAS,CAAC,CAACF,YAAY,CAACnD,UAAU,CAACK,WAAW,CAACE,kBAAkB,CAAC;IAC3E,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF0B,QAAQ,CAAC,+BAA+B,EAAE,MAAM;IAC9C,IAAIiD,iBAAiB;IAErB/C,UAAU,CAAC,MAAM;MACf+C,iBAAiB,GAAG,IAAInF,iBAAiB,CAAC,CAAC;;MAE3C;MACAmF,iBAAiB,CAACC,iBAAiB,CAAC,gBAAgB,EAAE;QACpDC,OAAO,EAAE,MAAAA,CAAOC,SAAS,EAAEC,MAAM,KAAK;UACpC,OAAO;YAAED,SAAS;YAAEC,MAAM;YAAEC,MAAM,EAAE;UAAU,CAAC;QACjD;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFjD,IAAI,CAAC,oCAAoC,EAAE,MAAM;MAC/C,MAAMK,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;MAEnC;MACA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BkD,iBAAiB,CAACC,iBAAiB,CAAC,aAAanD,CAAC,EAAE,EAAE;UACpDoD,OAAO,EAAE,MAAAA,CAAOC,SAAS,EAAEC,MAAM,KAAK;YACpC,OAAO;cAAED,SAAS;cAAEC,MAAM;cAAEC,MAAM,EAAE,WAAWvD,CAAC;YAAG,CAAC;UACtD;QACF,CAAC,CAAC;MACJ;MAEA,MAAMa,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAMS,SAAS,GAAGR,OAAO,GAAGF,SAAS;MACrC,MAAMG,WAAW,GAAGO,SAAS,GAAG,GAAG;MAEnCN,OAAO,CAACC,GAAG,CAAC,wCAAwCF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAC/EC,MAAM,CAACJ,WAAW,CAAC,CAACK,YAAY,CAACnD,UAAU,CAACQ,SAAS,CAACC,YAAY,CAAC;IACrE,CAAC,CAAC;IAEF6B,IAAI,CAAC,iCAAiC,EAAE,YAAY;MAClD,MAAMK,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;MAEnC;MACA,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5B,MAAMkD,iBAAiB,CAACM,gBAAgB,CAAC,gBAAgB,EAAE,KAAK,EAAE;UAChEC,IAAI,EAAE,aAAazD,CAAC,EAAE;UACtBsD,MAAM,EAAE;YAAEI,MAAM,EAAE;UAAS;QAC7B,CAAC,CAAC;MACJ;MAEA,MAAM7C,OAAO,GAAGlD,WAAW,CAACiD,GAAG,CAAC,CAAC;MACjC,MAAMS,SAAS,GAAGR,OAAO,GAAGF,SAAS;MACrC,MAAMG,WAAW,GAAGO,SAAS,GAAG,GAAG;MAEnCN,OAAO,CAACC,GAAG,CAAC,qCAAqCF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;MAC5EC,MAAM,CAACJ,WAAW,CAAC,CAACK,YAAY,CAACnD,UAAU,CAACQ,SAAS,CAACE,SAAS,CAAC;IAClE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}