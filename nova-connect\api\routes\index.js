/**
 * API Routes Index
 *
 * This file exports all API routes for the NovaConnect UAC.
 */

const express = require('express');
const router = express.Router();

// Import route modules
const connectorRoutes = require('./connectorRoutes');
const testingRoutes = require('./testingRoutes');
const monitoringRoutes = require('./monitoringRoutes');
const feedbackRoutes = require('./feedbackRoutes');

// Mount routes
router.use('/connectors', connectorRoutes);
router.use('/testing', testingRoutes);
router.use('/monitoring', monitoringRoutes);
router.use('/feedback', feedbackRoutes);

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'nova-connect-api',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

module.exports = router;
