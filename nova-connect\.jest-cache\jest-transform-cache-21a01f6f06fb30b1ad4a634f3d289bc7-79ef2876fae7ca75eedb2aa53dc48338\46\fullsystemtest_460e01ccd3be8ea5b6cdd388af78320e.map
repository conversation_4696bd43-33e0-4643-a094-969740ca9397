{"version": 3, "names": ["axios", "require", "performance", "fs", "path", "config", "apiUrl", "process", "env", "SYSTEM_TEST_API_URL", "<PERSON><PERSON><PERSON><PERSON>", "SYSTEM_TEST_API_KEY", "outputDir", "join", "__dirname", "testDataDir", "normalizationBatchSize", "remediationConcurrency", "maxConcurrentRequests", "existsSync", "mkdirSync", "recursive", "apiClient", "create", "baseURL", "headers", "describe", "apiAvailable", "beforeAll", "get", "error", "console", "warn", "conditionalTest", "it", "skip", "testData<PERSON><PERSON>", "testData", "JSON", "parse", "readFileSync", "generateTestData", "writeFileSync", "stringify", "batches", "i", "length", "push", "slice", "log", "startTime", "now", "results", "batch", "response", "post", "source", "data", "endTime", "duration", "totalFindings", "reduce", "sum", "result", "throughput", "toFixed", "expect", "toBeGreaterThan", "toBeLessThan", "outputFile", "averageTimePerFinding", "generateRemediationScenarios", "promises", "map", "scenario", "batchResults", "Promise", "all", "totalSteps", "steps", "successfulRemediations", "filter", "status", "toBeGreaterThanOrEqual", "totalRemediations", "averageTimePerRemediation", "successRate", "concurrentRequests", "requestsPerEndpoint", "endpoints", "method", "requests", "endpoint", "request", "message", "successfulRequests", "toBe", "totalRequests", "averageTimePerRequest", "batchSize", "batchPayload", "estimatedTimeFor50K", "estimatedTimeInMinutes", "toBeLessThanOrEqual", "breachScenario", "id", "Date", "type", "severity", "resource", "name", "provider", "projectId", "finding", "resourceName", "resourceType", "createdAt", "description", "dataType", "complianceFrameworks", "remediationSteps", "action", "parameters", "datasetId", "encryptionType", "keyRotationPeriod", "accessLevel", "allowedRoles", "dashboardId", "findingId", "remediationId", "toHaveProperty", "every", "step", "success", "metrics", "summaryFile", "summary", "toISOString", "transformation", "averageDuration", "transformations", "batchTransformations", "rulesApplied", "remediation", "failedRemediations", "averageRemediationTime", "successfulSteps", "failedSteps", "averageStepTime", "system", "cpu", "memory", "uptime", "connections", "api", "averageResponseTime", "requestsPerSecond", "errorRate", "count", "findings", "parent", "state", "category", "eventTime", "createTime", "sourceProperties", "finding_type", "finding_id", "finding_description", "scenarios", "framework", "control", "resourceId", "evidenceType"], "sources": ["full-system.test.js"], "sourcesContent": ["/**\n * NovaConnect Full System Test\n * \n * This test validates the end-to-end performance of the NovaConnect system,\n * including data normalization, remediation workflows, and integration with\n * Google Cloud services.\n * \n * NOTE: This test requires a running NovaConnect API server.\n * Set the SYSTEM_TEST_API_URL environment variable to point to the API server.\n */\n\nconst axios = require('axios');\nconst { performance } = require('perf_hooks');\nconst fs = require('fs');\nconst path = require('path');\n\n// Configuration\nconst config = {\n  apiUrl: process.env.SYSTEM_TEST_API_URL || 'http://localhost:3000',\n  apiKey: process.env.SYSTEM_TEST_API_KEY || 'test-api-key',\n  outputDir: path.join(__dirname, '../../test-results/system'),\n  testDataDir: path.join(__dirname, '../data'),\n  normalizationBatchSize: 100,\n  remediationConcurrency: 10,\n  maxConcurrentRequests: 50\n};\n\n// Ensure output directory exists\nif (!fs.existsSync(config.outputDir)) {\n  fs.mkdirSync(config.outputDir, { recursive: true });\n}\n\n// Create API client\nconst apiClient = axios.create({\n  baseURL: config.apiUrl,\n  headers: {\n    'Authorization': `Bearer ${config.apiKey}`,\n    'Content-Type': 'application/json'\n  }\n});\n\ndescribe('NovaConnect Full System Test', () => {\n  // Skip tests if API is not available\n  let apiAvailable = false;\n  \n  beforeAll(async () => {\n    try {\n      // Check if API is available\n      await apiClient.get('/health');\n      apiAvailable = true;\n    } catch (error) {\n      console.warn(`API not available at ${config.apiUrl}. Skipping system tests.`);\n    }\n  });\n  \n  // Skip tests if API is not available\n  const conditionalTest = apiAvailable ? it : it.skip;\n  \n  conditionalTest('should normalize data at high throughput', async () => {\n    // Load test data\n    const testDataPath = path.join(config.testDataDir, 'scc-findings.json');\n    let testData;\n    \n    try {\n      testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));\n    } catch (error) {\n      // Generate test data if file doesn't exist\n      testData = generateTestData(1000);\n      fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));\n    }\n    \n    // Split data into batches\n    const batches = [];\n    for (let i = 0; i < testData.length; i += config.normalizationBatchSize) {\n      batches.push(testData.slice(i, i + config.normalizationBatchSize));\n    }\n    \n    console.log(`Normalizing ${testData.length} findings in ${batches.length} batches`);\n    \n    const startTime = performance.now();\n    \n    // Process each batch\n    const results = [];\n    for (const batch of batches) {\n      const response = await apiClient.post('/api/transform/normalize', {\n        source: 'scc',\n        data: batch\n      });\n      \n      results.push(response.data);\n    }\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Calculate metrics\n    const totalFindings = results.reduce((sum, result) => sum + result.data.length, 0);\n    const throughput = (totalFindings / duration) * 1000; // findings per second\n    \n    console.log(`Normalized ${totalFindings} findings in ${duration.toFixed(2)}ms`);\n    console.log(`Throughput: ${throughput.toFixed(2)} findings/second`);\n    console.log(`Average time per finding: ${(duration / totalFindings).toFixed(2)}ms`);\n    \n    // Verify performance\n    expect(throughput).toBeGreaterThan(1000); // At least 1000 findings per second\n    expect(duration / totalFindings).toBeLessThan(1); // Less than 1ms per finding\n    \n    // Write results to file\n    const outputFile = path.join(config.outputDir, 'normalization-performance.json');\n    fs.writeFileSync(outputFile, JSON.stringify({\n      totalFindings,\n      duration,\n      throughput,\n      averageTimePerFinding: duration / totalFindings\n    }, null, 2));\n  }, 60000);\n  \n  conditionalTest('should execute remediation workflows efficiently', async () => {\n    // Load test data\n    const testDataPath = path.join(config.testDataDir, 'remediation-scenarios.json');\n    let testData;\n    \n    try {\n      testData = JSON.parse(fs.readFileSync(testDataPath, 'utf8'));\n    } catch (error) {\n      // Generate test data if file doesn't exist\n      testData = generateRemediationScenarios(100);\n      fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));\n    }\n    \n    console.log(`Executing ${testData.length} remediation workflows`);\n    \n    const startTime = performance.now();\n    \n    // Execute remediation workflows with concurrency control\n    const results = [];\n    for (let i = 0; i < testData.length; i += config.remediationConcurrency) {\n      const batch = testData.slice(i, i + config.remediationConcurrency);\n      const promises = batch.map(scenario => \n        apiClient.post('/api/remediate', scenario)\n      );\n      \n      const batchResults = await Promise.all(promises);\n      results.push(...batchResults.map(response => response.data));\n    }\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Calculate metrics\n    const totalSteps = results.reduce((sum, result) => sum + result.steps.length, 0);\n    const successfulRemediations = results.filter(result => result.status === 'completed').length;\n    const throughput = (testData.length / duration) * 60000; // remediations per minute\n    \n    console.log(`Executed ${testData.length} remediation workflows (${totalSteps} steps) in ${duration.toFixed(2)}ms`);\n    console.log(`Success rate: ${(successfulRemediations / testData.length * 100).toFixed(2)}%`);\n    console.log(`Throughput: ${throughput.toFixed(2)} remediations/minute`);\n    console.log(`Average time per remediation: ${(duration / testData.length).toFixed(2)}ms`);\n    \n    // Verify performance\n    expect(throughput).toBeGreaterThan(30); // At least 30 remediations per minute\n    expect(duration / testData.length).toBeLessThan(8000); // Less than 8 seconds per remediation\n    expect(successfulRemediations / testData.length).toBeGreaterThanOrEqual(0.95); // At least 95% success rate\n    \n    // Write results to file\n    const outputFile = path.join(config.outputDir, 'remediation-performance.json');\n    fs.writeFileSync(outputFile, JSON.stringify({\n      totalRemediations: testData.length,\n      successfulRemediations,\n      totalSteps,\n      duration,\n      throughput,\n      averageTimePerRemediation: duration / testData.length,\n      successRate: successfulRemediations / testData.length\n    }, null, 2));\n  }, 300000);\n  \n  conditionalTest('should handle concurrent requests at scale', async () => {\n    // Generate concurrent requests\n    const concurrentRequests = config.maxConcurrentRequests;\n    const requestsPerEndpoint = 10;\n    \n    const endpoints = [\n      { path: '/health', method: 'get' },\n      { path: '/api/connectors', method: 'get' },\n      { path: '/api/transform/capabilities', method: 'get' },\n      { path: '/api/remediate/actions', method: 'get' }\n    ];\n    \n    const requests = [];\n    for (const endpoint of endpoints) {\n      for (let i = 0; i < requestsPerEndpoint; i++) {\n        requests.push({\n          path: endpoint.path,\n          method: endpoint.method\n        });\n      }\n    }\n    \n    console.log(`Executing ${requests.length} concurrent requests`);\n    \n    const startTime = performance.now();\n    \n    // Execute requests in batches to avoid overwhelming the server\n    const results = [];\n    for (let i = 0; i < requests.length; i += concurrentRequests) {\n      const batch = requests.slice(i, i + concurrentRequests);\n      const promises = batch.map(request => \n        apiClient[request.method](request.path)\n      );\n      \n      try {\n        const batchResults = await Promise.all(promises);\n        results.push(...batchResults);\n      } catch (error) {\n        console.error(`Error executing batch: ${error.message}`);\n      }\n    }\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Calculate metrics\n    const successfulRequests = results.length;\n    const throughput = (successfulRequests / duration) * 1000; // requests per second\n    \n    console.log(`Executed ${successfulRequests} requests in ${duration.toFixed(2)}ms`);\n    console.log(`Throughput: ${throughput.toFixed(2)} requests/second`);\n    console.log(`Average time per request: ${(duration / successfulRequests).toFixed(2)}ms`);\n    \n    // Verify performance\n    expect(throughput).toBeGreaterThan(50); // At least 50 requests per second\n    expect(successfulRequests).toBe(requests.length); // All requests should succeed\n    \n    // Write results to file\n    const outputFile = path.join(config.outputDir, 'concurrency-performance.json');\n    fs.writeFileSync(outputFile, JSON.stringify({\n      totalRequests: requests.length,\n      successfulRequests,\n      duration,\n      throughput,\n      averageTimePerRequest: duration / successfulRequests\n    }, null, 2));\n  }, 60000);\n  \n  conditionalTest('should simulate peak load of 50K events', async () => {\n    // For this test, we'll use a smaller batch and extrapolate\n    // to avoid running an actual test with 50K events\n    const batchSize = 500; // Use 500 items for the simulation\n    \n    // Generate batch payload\n    const batchPayload = {\n      source: 'scc',\n      data: generateTestData(batchSize)\n    };\n    \n    console.log(`Simulating peak load with ${batchSize} events`);\n    \n    const startTime = performance.now();\n    \n    // Process the batch\n    const response = await apiClient.post('/api/transform/normalize', batchPayload);\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Calculate metrics\n    const throughput = (batchSize / duration) * 1000; // events per second\n    const estimatedTimeFor50K = (50000 / throughput) * 1000; // ms\n    const estimatedTimeInMinutes = estimatedTimeFor50K / (1000 * 60); // minutes\n    \n    console.log(`Processed ${batchSize} events in ${duration.toFixed(2)}ms`);\n    console.log(`Throughput: ${throughput.toFixed(2)} events/second`);\n    console.log(`Estimated time to process 50,000 events: ${estimatedTimeInMinutes.toFixed(2)} minutes`);\n    \n    // Verify performance\n    expect(throughput).toBeGreaterThan(1000); // At least 1000 events per second\n    expect(estimatedTimeInMinutes).toBeLessThanOrEqual(15); // Less than 15 minutes for 50K events\n    \n    // Write results to file\n    const outputFile = path.join(config.outputDir, 'peak-load-simulation.json');\n    fs.writeFileSync(outputFile, JSON.stringify({\n      batchSize,\n      duration,\n      throughput,\n      estimatedTimeFor50K,\n      estimatedTimeInMinutes\n    }, null, 2));\n  }, 60000);\n  \n  conditionalTest('should execute end-to-end breach remediation workflow', async () => {\n    // Create a breach scenario\n    const breachScenario = {\n      id: `breach-${Date.now()}`,\n      type: 'data_leak',\n      severity: 'high',\n      resource: {\n        id: 'patient_records',\n        type: 'bigquery.dataset',\n        name: 'patient_records',\n        provider: 'gcp',\n        projectId: 'healthcare-demo'\n      },\n      finding: {\n        id: `finding-${Date.now()}`,\n        type: 'data_leak',\n        severity: 'high',\n        resourceName: 'projects/healthcare-demo/datasets/patient_records',\n        resourceType: 'bigquery.dataset',\n        createdAt: Date.now(),\n        description: 'PHI data exposed in BigQuery dataset',\n        dataType: 'PHI',\n        complianceFrameworks: ['HIPAA', 'GDPR']\n      },\n      remediationSteps: [\n        {\n          id: 'step-1',\n          action: 'encrypt-dataset',\n          parameters: {\n            projectId: 'healthcare-demo',\n            datasetId: 'patient_records',\n            encryptionType: 'AES-256',\n            keyRotationPeriod: '90d'\n          }\n        },\n        {\n          id: 'step-2',\n          action: 'update-access-controls',\n          parameters: {\n            projectId: 'healthcare-demo',\n            datasetId: 'patient_records',\n            accessLevel: 'restricted',\n            allowedRoles: ['healthcare-admin', 'compliance-officer']\n          }\n        },\n        {\n          id: 'step-3',\n          action: 'update-compliance-dashboard',\n          parameters: {\n            dashboardId: 'hipaa-compliance-dashboard',\n            findingId: `finding-${Date.now()}`,\n            remediationId: `breach-${Date.now()}`\n          }\n        }\n      ]\n    };\n    \n    console.log('Executing end-to-end breach remediation workflow');\n    \n    const startTime = performance.now();\n    \n    // Execute the remediation\n    const response = await apiClient.post('/api/remediate', breachScenario);\n    const result = response.data;\n    \n    const endTime = performance.now();\n    const duration = endTime - startTime;\n    \n    // Verify remediation result\n    expect(result).toHaveProperty('id');\n    expect(result).toHaveProperty('status');\n    expect(result).toHaveProperty('steps');\n    expect(result.steps.length).toBe(3);\n    expect(result.steps.every(step => step.success)).toBe(true);\n    \n    console.log(`Executed breach remediation in ${duration.toFixed(2)}ms`);\n    console.log(`Status: ${result.status}`);\n    \n    // Verify performance\n    expect(duration).toBeLessThan(8000); // Less than 8 seconds\n    \n    // Write results to file\n    const outputFile = path.join(config.outputDir, 'breach-remediation.json');\n    fs.writeFileSync(outputFile, JSON.stringify({\n      scenario: breachScenario,\n      result,\n      duration\n    }, null, 2));\n  }, 30000);\n  \n  conditionalTest('should generate system performance report', async () => {\n    // Get system metrics\n    const response = await apiClient.get('/api/metrics');\n    const metrics = response.data;\n    \n    // Write metrics to file\n    const outputFile = path.join(config.outputDir, 'system-metrics.json');\n    fs.writeFileSync(outputFile, JSON.stringify(metrics, null, 2));\n    \n    // Generate summary report\n    const summaryFile = path.join(config.outputDir, 'system-performance-summary.md');\n    \n    let summary = `# NovaConnect System Performance Summary\\n\\n`;\n    summary += `Generated on: ${new Date().toISOString()}\\n\\n`;\n    \n    // Add transformation metrics\n    if (metrics.transformation) {\n      summary += `## Data Normalization Performance\\n\\n`;\n      summary += `- Average normalization time: ${metrics.transformation.averageDuration.toFixed(2)}ms\\n`;\n      summary += `- Total transformations: ${metrics.transformation.transformations}\\n`;\n      summary += `- Batch transformations: ${metrics.transformation.batchTransformations}\\n`;\n      summary += `- Rules applied: ${metrics.transformation.rulesApplied}\\n\\n`;\n    }\n    \n    // Add remediation metrics\n    if (metrics.remediation) {\n      summary += `## Remediation Performance\\n\\n`;\n      summary += `- Total remediations: ${metrics.remediation.totalRemediations}\\n`;\n      summary += `- Successful remediations: ${metrics.remediation.successfulRemediations}\\n`;\n      summary += `- Failed remediations: ${metrics.remediation.failedRemediations}\\n`;\n      summary += `- Average remediation time: ${metrics.remediation.averageRemediationTime.toFixed(2)}ms\\n`;\n      summary += `- Total steps: ${metrics.remediation.totalSteps}\\n`;\n      summary += `- Successful steps: ${metrics.remediation.successfulSteps}\\n`;\n      summary += `- Failed steps: ${metrics.remediation.failedSteps}\\n`;\n      summary += `- Average step time: ${metrics.remediation.averageStepTime.toFixed(2)}ms\\n\\n`;\n    }\n    \n    // Add system metrics\n    if (metrics.system) {\n      summary += `## System Performance\\n\\n`;\n      summary += `- CPU usage: ${metrics.system.cpu.toFixed(2)}%\\n`;\n      summary += `- Memory usage: ${(metrics.system.memory / (1024 * 1024)).toFixed(2)} MB\\n`;\n      summary += `- Uptime: ${(metrics.system.uptime / (60 * 60)).toFixed(2)} hours\\n`;\n      summary += `- Active connections: ${metrics.system.connections}\\n\\n`;\n    }\n    \n    // Add API metrics\n    if (metrics.api) {\n      summary += `## API Performance\\n\\n`;\n      summary += `- Total requests: ${metrics.api.totalRequests}\\n`;\n      summary += `- Average response time: ${metrics.api.averageResponseTime.toFixed(2)}ms\\n`;\n      summary += `- Requests per second: ${metrics.api.requestsPerSecond.toFixed(2)}\\n`;\n      summary += `- Error rate: ${(metrics.api.errorRate * 100).toFixed(2)}%\\n\\n`;\n    }\n    \n    // Write summary to file\n    fs.writeFileSync(summaryFile, summary);\n    \n    console.log(`System performance report generated: ${summaryFile}`);\n  }, 30000);\n});\n\n/**\n * Generate test data for SCC findings\n * @param {number} count - Number of findings to generate\n * @returns {Array} - Array of test findings\n */\nfunction generateTestData(count) {\n  const findings = [];\n  \n  for (let i = 0; i < count; i++) {\n    findings.push({\n      name: `organizations/123/sources/456/findings/finding-${i}`,\n      parent: 'organizations/123/sources/456',\n      resourceName: `//compute.googleapis.com/projects/test-project/zones/us-central1-a/instances/instance-${i}`,\n      state: 'ACTIVE',\n      category: ['VULNERABILITY', 'MISCONFIGURATION', 'THREAT'][i % 3],\n      severity: ['HIGH', 'MEDIUM', 'LOW'][i % 3],\n      eventTime: new Date().toISOString(),\n      createTime: new Date().toISOString(),\n      sourceProperties: {\n        finding_type: ['Vulnerability', 'Misconfiguration', 'Threat'][i % 3],\n        finding_id: `finding-${i}`,\n        finding_description: `Mock finding ${i} description`\n      }\n    });\n  }\n  \n  return findings;\n}\n\n/**\n * Generate test data for remediation scenarios\n * @param {number} count - Number of scenarios to generate\n * @returns {Array} - Array of test scenarios\n */\nfunction generateRemediationScenarios(count) {\n  const scenarios = [];\n  \n  for (let i = 0; i < count; i++) {\n    scenarios.push({\n      id: `scenario-${i}`,\n      type: 'compliance',\n      framework: ['HIPAA', 'PCI-DSS', 'GDPR'][i % 3],\n      control: ['164.312(a)(1)', 'Requirement 3.4', 'Article 32'][i % 3],\n      severity: ['high', 'medium', 'low'][i % 3],\n      resource: {\n        id: `resource-${i}`,\n        type: ['compute.instance', 'storage.bucket', 'bigquery.dataset'][i % 3],\n        name: `resource-${i}`,\n        provider: 'gcp'\n      },\n      finding: {\n        id: `finding-${i}`,\n        type: ['vulnerability', 'misconfiguration', 'threat'][i % 3],\n        severity: ['high', 'medium', 'low'][i % 3],\n        resourceName: `projects/test-project/resources/resource-${i}`,\n        resourceType: ['compute.instance', 'storage.bucket', 'bigquery.dataset'][i % 3],\n        createdAt: Date.now(),\n        description: `Mock finding ${i} description`\n      },\n      remediationSteps: [\n        {\n          id: `step-${i}-1`,\n          action: ['update-firewall-rule', 'encrypt-bucket', 'update-access-controls'][i % 3],\n          parameters: {\n            resourceId: `resource-${i}`,\n            action: 'restrict'\n          }\n        },\n        {\n          id: `step-${i}-2`,\n          action: 'generate-evidence',\n          parameters: {\n            findingId: `finding-${i}`,\n            evidenceType: 'remediation'\n          }\n        }\n      ]\n    });\n  }\n  \n  return scenarios;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC9B,MAAM;EAAEC;AAAY,CAAC,GAAGD,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAME,EAAE,GAAGF,OAAO,CAAC,IAAI,CAAC;AACxB,MAAMG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;;AAE5B;AACA,MAAMI,MAAM,GAAG;EACbC,MAAM,EAAEC,OAAO,CAACC,GAAG,CAACC,mBAAmB,IAAI,uBAAuB;EAClEC,MAAM,EAAEH,OAAO,CAACC,GAAG,CAACG,mBAAmB,IAAI,cAAc;EACzDC,SAAS,EAAER,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,2BAA2B,CAAC;EAC5DC,WAAW,EAAEX,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC;EAC5CE,sBAAsB,EAAE,GAAG;EAC3BC,sBAAsB,EAAE,EAAE;EAC1BC,qBAAqB,EAAE;AACzB,CAAC;;AAED;AACA,IAAI,CAACf,EAAE,CAACgB,UAAU,CAACd,MAAM,CAACO,SAAS,CAAC,EAAE;EACpCT,EAAE,CAACiB,SAAS,CAACf,MAAM,CAACO,SAAS,EAAE;IAAES,SAAS,EAAE;EAAK,CAAC,CAAC;AACrD;;AAEA;AACA,MAAMC,SAAS,GAAGtB,KAAK,CAACuB,MAAM,CAAC;EAC7BC,OAAO,EAAEnB,MAAM,CAACC,MAAM;EACtBmB,OAAO,EAAE;IACP,eAAe,EAAE,UAAUpB,MAAM,CAACK,MAAM,EAAE;IAC1C,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEFgB,QAAQ,CAAC,8BAA8B,EAAE,MAAM;EAC7C;EACA,IAAIC,YAAY,GAAG,KAAK;EAExBC,SAAS,CAAC,YAAY;IACpB,IAAI;MACF;MACA,MAAMN,SAAS,CAACO,GAAG,CAAC,SAAS,CAAC;MAC9BF,YAAY,GAAG,IAAI;IACrB,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,wBAAwB3B,MAAM,CAACC,MAAM,0BAA0B,CAAC;IAC/E;EACF,CAAC,CAAC;;EAEF;EACA,MAAM2B,eAAe,GAAGN,YAAY,GAAGO,EAAE,GAAGA,EAAE,CAACC,IAAI;EAEnDF,eAAe,CAAC,0CAA0C,EAAE,YAAY;IACtE;IACA,MAAMG,YAAY,GAAGhC,IAAI,CAACS,IAAI,CAACR,MAAM,CAACU,WAAW,EAAE,mBAAmB,CAAC;IACvE,IAAIsB,QAAQ;IAEZ,IAAI;MACFA,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACpC,EAAE,CAACqC,YAAY,CAACJ,YAAY,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAON,KAAK,EAAE;MACd;MACAO,QAAQ,GAAGI,gBAAgB,CAAC,IAAI,CAAC;MACjCtC,EAAE,CAACuC,aAAa,CAACN,YAAY,EAAEE,IAAI,CAACK,SAAS,CAACN,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACnE;;IAEA;IACA,MAAMO,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACS,MAAM,EAAED,CAAC,IAAIxC,MAAM,CAACW,sBAAsB,EAAE;MACvE4B,OAAO,CAACG,IAAI,CAACV,QAAQ,CAACW,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGxC,MAAM,CAACW,sBAAsB,CAAC,CAAC;IACpE;IAEAe,OAAO,CAACkB,GAAG,CAAC,eAAeZ,QAAQ,CAACS,MAAM,gBAAgBF,OAAO,CAACE,MAAM,UAAU,CAAC;IAEnF,MAAMI,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMC,KAAK,IAAIT,OAAO,EAAE;MAC3B,MAAMU,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,IAAI,CAAC,0BAA0B,EAAE;QAChEC,MAAM,EAAE,KAAK;QACbC,IAAI,EAAEJ;MACR,CAAC,CAAC;MAEFD,OAAO,CAACL,IAAI,CAACO,QAAQ,CAACG,IAAI,CAAC;IAC7B;IAEA,MAAMC,OAAO,GAAGxD,WAAW,CAACiD,GAAG,CAAC,CAAC;IACjC,MAAMQ,QAAQ,GAAGD,OAAO,GAAGR,SAAS;;IAEpC;IACA,MAAMU,aAAa,GAAGR,OAAO,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACN,IAAI,CAACX,MAAM,EAAE,CAAC,CAAC;IAClF,MAAMkB,UAAU,GAAIJ,aAAa,GAAGD,QAAQ,GAAI,IAAI,CAAC,CAAC;;IAEtD5B,OAAO,CAACkB,GAAG,CAAC,cAAcW,aAAa,gBAAgBD,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC/ElC,OAAO,CAACkB,GAAG,CAAC,eAAee,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;IACnElC,OAAO,CAACkB,GAAG,CAAC,6BAA6B,CAACU,QAAQ,GAAGC,aAAa,EAAEK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAEnF;IACAC,MAAM,CAACF,UAAU,CAAC,CAACG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1CD,MAAM,CAACP,QAAQ,GAAGC,aAAa,CAAC,CAACQ,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElD;IACA,MAAMC,UAAU,GAAGjE,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,gCAAgC,CAAC;IAChFT,EAAE,CAACuC,aAAa,CAAC2B,UAAU,EAAE/B,IAAI,CAACK,SAAS,CAAC;MAC1CiB,aAAa;MACbD,QAAQ;MACRK,UAAU;MACVM,qBAAqB,EAAEX,QAAQ,GAAGC;IACpC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,EAAE,KAAK,CAAC;EAET3B,eAAe,CAAC,kDAAkD,EAAE,YAAY;IAC9E;IACA,MAAMG,YAAY,GAAGhC,IAAI,CAACS,IAAI,CAACR,MAAM,CAACU,WAAW,EAAE,4BAA4B,CAAC;IAChF,IAAIsB,QAAQ;IAEZ,IAAI;MACFA,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACpC,EAAE,CAACqC,YAAY,CAACJ,YAAY,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAON,KAAK,EAAE;MACd;MACAO,QAAQ,GAAGkC,4BAA4B,CAAC,GAAG,CAAC;MAC5CpE,EAAE,CAACuC,aAAa,CAACN,YAAY,EAAEE,IAAI,CAACK,SAAS,CAACN,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACnE;IAEAN,OAAO,CAACkB,GAAG,CAAC,aAAaZ,QAAQ,CAACS,MAAM,wBAAwB,CAAC;IAEjE,MAAMI,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,QAAQ,CAACS,MAAM,EAAED,CAAC,IAAIxC,MAAM,CAACY,sBAAsB,EAAE;MACvE,MAAMoC,KAAK,GAAGhB,QAAQ,CAACW,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGxC,MAAM,CAACY,sBAAsB,CAAC;MAClE,MAAMuD,QAAQ,GAAGnB,KAAK,CAACoB,GAAG,CAACC,QAAQ,IACjCpD,SAAS,CAACiC,IAAI,CAAC,gBAAgB,EAAEmB,QAAQ,CAC3C,CAAC;MAED,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAChDpB,OAAO,CAACL,IAAI,CAAC,GAAG4B,YAAY,CAACF,GAAG,CAACnB,QAAQ,IAAIA,QAAQ,CAACG,IAAI,CAAC,CAAC;IAC9D;IAEA,MAAMC,OAAO,GAAGxD,WAAW,CAACiD,GAAG,CAAC,CAAC;IACjC,MAAMQ,QAAQ,GAAGD,OAAO,GAAGR,SAAS;;IAEpC;IACA,MAAM4B,UAAU,GAAG1B,OAAO,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,MAAM,KAAKD,GAAG,GAAGC,MAAM,CAACgB,KAAK,CAACjC,MAAM,EAAE,CAAC,CAAC;IAChF,MAAMkC,sBAAsB,GAAG5B,OAAO,CAAC6B,MAAM,CAAClB,MAAM,IAAIA,MAAM,CAACmB,MAAM,KAAK,WAAW,CAAC,CAACpC,MAAM;IAC7F,MAAMkB,UAAU,GAAI3B,QAAQ,CAACS,MAAM,GAAGa,QAAQ,GAAI,KAAK,CAAC,CAAC;;IAEzD5B,OAAO,CAACkB,GAAG,CAAC,YAAYZ,QAAQ,CAACS,MAAM,2BAA2BgC,UAAU,cAAcnB,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAClHlC,OAAO,CAACkB,GAAG,CAAC,iBAAiB,CAAC+B,sBAAsB,GAAG3C,QAAQ,CAACS,MAAM,GAAG,GAAG,EAAEmB,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC;IAC5FlC,OAAO,CAACkB,GAAG,CAAC,eAAee,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC;IACvElC,OAAO,CAACkB,GAAG,CAAC,iCAAiC,CAACU,QAAQ,GAAGtB,QAAQ,CAACS,MAAM,EAAEmB,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAEzF;IACAC,MAAM,CAACF,UAAU,CAAC,CAACG,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACxCD,MAAM,CAACP,QAAQ,GAAGtB,QAAQ,CAACS,MAAM,CAAC,CAACsB,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IACvDF,MAAM,CAACc,sBAAsB,GAAG3C,QAAQ,CAACS,MAAM,CAAC,CAACqC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE/E;IACA,MAAMd,UAAU,GAAGjE,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,8BAA8B,CAAC;IAC9ET,EAAE,CAACuC,aAAa,CAAC2B,UAAU,EAAE/B,IAAI,CAACK,SAAS,CAAC;MAC1CyC,iBAAiB,EAAE/C,QAAQ,CAACS,MAAM;MAClCkC,sBAAsB;MACtBF,UAAU;MACVnB,QAAQ;MACRK,UAAU;MACVqB,yBAAyB,EAAE1B,QAAQ,GAAGtB,QAAQ,CAACS,MAAM;MACrDwC,WAAW,EAAEN,sBAAsB,GAAG3C,QAAQ,CAACS;IACjD,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,EAAE,MAAM,CAAC;EAEVb,eAAe,CAAC,4CAA4C,EAAE,YAAY;IACxE;IACA,MAAMsD,kBAAkB,GAAGlF,MAAM,CAACa,qBAAqB;IACvD,MAAMsE,mBAAmB,GAAG,EAAE;IAE9B,MAAMC,SAAS,GAAG,CAChB;MAAErF,IAAI,EAAE,SAAS;MAAEsF,MAAM,EAAE;IAAM,CAAC,EAClC;MAAEtF,IAAI,EAAE,iBAAiB;MAAEsF,MAAM,EAAE;IAAM,CAAC,EAC1C;MAAEtF,IAAI,EAAE,6BAA6B;MAAEsF,MAAM,EAAE;IAAM,CAAC,EACtD;MAAEtF,IAAI,EAAE,wBAAwB;MAAEsF,MAAM,EAAE;IAAM,CAAC,CAClD;IAED,MAAMC,QAAQ,GAAG,EAAE;IACnB,KAAK,MAAMC,QAAQ,IAAIH,SAAS,EAAE;MAChC,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,mBAAmB,EAAE3C,CAAC,EAAE,EAAE;QAC5C8C,QAAQ,CAAC5C,IAAI,CAAC;UACZ3C,IAAI,EAAEwF,QAAQ,CAACxF,IAAI;UACnBsF,MAAM,EAAEE,QAAQ,CAACF;QACnB,CAAC,CAAC;MACJ;IACF;IAEA3D,OAAO,CAACkB,GAAG,CAAC,aAAa0C,QAAQ,CAAC7C,MAAM,sBAAsB,CAAC;IAE/D,MAAMI,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,QAAQ,CAAC7C,MAAM,EAAED,CAAC,IAAI0C,kBAAkB,EAAE;MAC5D,MAAMlC,KAAK,GAAGsC,QAAQ,CAAC3C,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAG0C,kBAAkB,CAAC;MACvD,MAAMf,QAAQ,GAAGnB,KAAK,CAACoB,GAAG,CAACoB,OAAO,IAChCvE,SAAS,CAACuE,OAAO,CAACH,MAAM,CAAC,CAACG,OAAO,CAACzF,IAAI,CACxC,CAAC;MAED,IAAI;QACF,MAAMuE,YAAY,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;QAChDpB,OAAO,CAACL,IAAI,CAAC,GAAG4B,YAAY,CAAC;MAC/B,CAAC,CAAC,OAAO7C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0BA,KAAK,CAACgE,OAAO,EAAE,CAAC;MAC1D;IACF;IAEA,MAAMpC,OAAO,GAAGxD,WAAW,CAACiD,GAAG,CAAC,CAAC;IACjC,MAAMQ,QAAQ,GAAGD,OAAO,GAAGR,SAAS;;IAEpC;IACA,MAAM6C,kBAAkB,GAAG3C,OAAO,CAACN,MAAM;IACzC,MAAMkB,UAAU,GAAI+B,kBAAkB,GAAGpC,QAAQ,GAAI,IAAI,CAAC,CAAC;;IAE3D5B,OAAO,CAACkB,GAAG,CAAC,YAAY8C,kBAAkB,gBAAgBpC,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAClFlC,OAAO,CAACkB,GAAG,CAAC,eAAee,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC;IACnElC,OAAO,CAACkB,GAAG,CAAC,6BAA6B,CAACU,QAAQ,GAAGoC,kBAAkB,EAAE9B,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;;IAExF;IACAC,MAAM,CAACF,UAAU,CAAC,CAACG,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC;IACxCD,MAAM,CAAC6B,kBAAkB,CAAC,CAACC,IAAI,CAACL,QAAQ,CAAC7C,MAAM,CAAC,CAAC,CAAC;;IAElD;IACA,MAAMuB,UAAU,GAAGjE,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,8BAA8B,CAAC;IAC9ET,EAAE,CAACuC,aAAa,CAAC2B,UAAU,EAAE/B,IAAI,CAACK,SAAS,CAAC;MAC1CsD,aAAa,EAAEN,QAAQ,CAAC7C,MAAM;MAC9BiD,kBAAkB;MAClBpC,QAAQ;MACRK,UAAU;MACVkC,qBAAqB,EAAEvC,QAAQ,GAAGoC;IACpC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,EAAE,KAAK,CAAC;EAET9D,eAAe,CAAC,yCAAyC,EAAE,YAAY;IACrE;IACA;IACA,MAAMkE,SAAS,GAAG,GAAG,CAAC,CAAC;;IAEvB;IACA,MAAMC,YAAY,GAAG;MACnB5C,MAAM,EAAE,KAAK;MACbC,IAAI,EAAEhB,gBAAgB,CAAC0D,SAAS;IAClC,CAAC;IAEDpE,OAAO,CAACkB,GAAG,CAAC,6BAA6BkD,SAAS,SAAS,CAAC;IAE5D,MAAMjD,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMG,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,IAAI,CAAC,0BAA0B,EAAE6C,YAAY,CAAC;IAE/E,MAAM1C,OAAO,GAAGxD,WAAW,CAACiD,GAAG,CAAC,CAAC;IACjC,MAAMQ,QAAQ,GAAGD,OAAO,GAAGR,SAAS;;IAEpC;IACA,MAAMc,UAAU,GAAImC,SAAS,GAAGxC,QAAQ,GAAI,IAAI,CAAC,CAAC;IAClD,MAAM0C,mBAAmB,GAAI,KAAK,GAAGrC,UAAU,GAAI,IAAI,CAAC,CAAC;IACzD,MAAMsC,sBAAsB,GAAGD,mBAAmB,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;;IAElEtE,OAAO,CAACkB,GAAG,CAAC,aAAakD,SAAS,cAAcxC,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IACxElC,OAAO,CAACkB,GAAG,CAAC,eAAee,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC;IACjElC,OAAO,CAACkB,GAAG,CAAC,4CAA4CqD,sBAAsB,CAACrC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;;IAEpG;IACAC,MAAM,CAACF,UAAU,CAAC,CAACG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1CD,MAAM,CAACoC,sBAAsB,CAAC,CAACC,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;;IAExD;IACA,MAAMlC,UAAU,GAAGjE,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,2BAA2B,CAAC;IAC3ET,EAAE,CAACuC,aAAa,CAAC2B,UAAU,EAAE/B,IAAI,CAACK,SAAS,CAAC;MAC1CwD,SAAS;MACTxC,QAAQ;MACRK,UAAU;MACVqC,mBAAmB;MACnBC;IACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,EAAE,KAAK,CAAC;EAETrE,eAAe,CAAC,uDAAuD,EAAE,YAAY;IACnF;IACA,MAAMuE,cAAc,GAAG;MACrBC,EAAE,EAAE,UAAUC,IAAI,CAACvD,GAAG,CAAC,CAAC,EAAE;MAC1BwD,IAAI,EAAE,WAAW;MACjBC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;QACRJ,EAAE,EAAE,iBAAiB;QACrBE,IAAI,EAAE,kBAAkB;QACxBG,IAAI,EAAE,iBAAiB;QACvBC,QAAQ,EAAE,KAAK;QACfC,SAAS,EAAE;MACb,CAAC;MACDC,OAAO,EAAE;QACPR,EAAE,EAAE,WAAWC,IAAI,CAACvD,GAAG,CAAC,CAAC,EAAE;QAC3BwD,IAAI,EAAE,WAAW;QACjBC,QAAQ,EAAE,MAAM;QAChBM,YAAY,EAAE,mDAAmD;QACjEC,YAAY,EAAE,kBAAkB;QAChCC,SAAS,EAAEV,IAAI,CAACvD,GAAG,CAAC,CAAC;QACrBkE,WAAW,EAAE,sCAAsC;QACnDC,QAAQ,EAAE,KAAK;QACfC,oBAAoB,EAAE,CAAC,OAAO,EAAE,MAAM;MACxC,CAAC;MACDC,gBAAgB,EAAE,CAChB;QACEf,EAAE,EAAE,QAAQ;QACZgB,MAAM,EAAE,iBAAiB;QACzBC,UAAU,EAAE;UACVV,SAAS,EAAE,iBAAiB;UAC5BW,SAAS,EAAE,iBAAiB;UAC5BC,cAAc,EAAE,SAAS;UACzBC,iBAAiB,EAAE;QACrB;MACF,CAAC,EACD;QACEpB,EAAE,EAAE,QAAQ;QACZgB,MAAM,EAAE,wBAAwB;QAChCC,UAAU,EAAE;UACVV,SAAS,EAAE,iBAAiB;UAC5BW,SAAS,EAAE,iBAAiB;UAC5BG,WAAW,EAAE,YAAY;UACzBC,YAAY,EAAE,CAAC,kBAAkB,EAAE,oBAAoB;QACzD;MACF,CAAC,EACD;QACEtB,EAAE,EAAE,QAAQ;QACZgB,MAAM,EAAE,6BAA6B;QACrCC,UAAU,EAAE;UACVM,WAAW,EAAE,4BAA4B;UACzCC,SAAS,EAAE,WAAWvB,IAAI,CAACvD,GAAG,CAAC,CAAC,EAAE;UAClC+E,aAAa,EAAE,UAAUxB,IAAI,CAACvD,GAAG,CAAC,CAAC;QACrC;MACF,CAAC;IAEL,CAAC;IAEDpB,OAAO,CAACkB,GAAG,CAAC,kDAAkD,CAAC;IAE/D,MAAMC,SAAS,GAAGhD,WAAW,CAACiD,GAAG,CAAC,CAAC;;IAEnC;IACA,MAAMG,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,IAAI,CAAC,gBAAgB,EAAEiD,cAAc,CAAC;IACvE,MAAMzC,MAAM,GAAGT,QAAQ,CAACG,IAAI;IAE5B,MAAMC,OAAO,GAAGxD,WAAW,CAACiD,GAAG,CAAC,CAAC;IACjC,MAAMQ,QAAQ,GAAGD,OAAO,GAAGR,SAAS;;IAEpC;IACAgB,MAAM,CAACH,MAAM,CAAC,CAACoE,cAAc,CAAC,IAAI,CAAC;IACnCjE,MAAM,CAACH,MAAM,CAAC,CAACoE,cAAc,CAAC,QAAQ,CAAC;IACvCjE,MAAM,CAACH,MAAM,CAAC,CAACoE,cAAc,CAAC,OAAO,CAAC;IACtCjE,MAAM,CAACH,MAAM,CAACgB,KAAK,CAACjC,MAAM,CAAC,CAACkD,IAAI,CAAC,CAAC,CAAC;IACnC9B,MAAM,CAACH,MAAM,CAACgB,KAAK,CAACqD,KAAK,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,CAACtC,IAAI,CAAC,IAAI,CAAC;IAE3DjE,OAAO,CAACkB,GAAG,CAAC,kCAAkCU,QAAQ,CAACM,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IACtElC,OAAO,CAACkB,GAAG,CAAC,WAAWc,MAAM,CAACmB,MAAM,EAAE,CAAC;;IAEvC;IACAhB,MAAM,CAACP,QAAQ,CAAC,CAACS,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;;IAErC;IACA,MAAMC,UAAU,GAAGjE,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,yBAAyB,CAAC;IACzET,EAAE,CAACuC,aAAa,CAAC2B,UAAU,EAAE/B,IAAI,CAACK,SAAS,CAAC;MAC1C+B,QAAQ,EAAE8B,cAAc;MACxBzC,MAAM;MACNJ;IACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACd,CAAC,EAAE,KAAK,CAAC;EAET1B,eAAe,CAAC,2CAA2C,EAAE,YAAY;IACvE;IACA,MAAMqB,QAAQ,GAAG,MAAMhC,SAAS,CAACO,GAAG,CAAC,cAAc,CAAC;IACpD,MAAM0G,OAAO,GAAGjF,QAAQ,CAACG,IAAI;;IAE7B;IACA,MAAMY,UAAU,GAAGjE,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,qBAAqB,CAAC;IACrET,EAAE,CAACuC,aAAa,CAAC2B,UAAU,EAAE/B,IAAI,CAACK,SAAS,CAAC4F,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;IAE9D;IACA,MAAMC,WAAW,GAAGpI,IAAI,CAACS,IAAI,CAACR,MAAM,CAACO,SAAS,EAAE,+BAA+B,CAAC;IAEhF,IAAI6H,OAAO,GAAG,8CAA8C;IAC5DA,OAAO,IAAI,iBAAiB,IAAI/B,IAAI,CAAC,CAAC,CAACgC,WAAW,CAAC,CAAC,MAAM;;IAE1D;IACA,IAAIH,OAAO,CAACI,cAAc,EAAE;MAC1BF,OAAO,IAAI,uCAAuC;MAClDA,OAAO,IAAI,iCAAiCF,OAAO,CAACI,cAAc,CAACC,eAAe,CAAC3E,OAAO,CAAC,CAAC,CAAC,MAAM;MACnGwE,OAAO,IAAI,4BAA4BF,OAAO,CAACI,cAAc,CAACE,eAAe,IAAI;MACjFJ,OAAO,IAAI,4BAA4BF,OAAO,CAACI,cAAc,CAACG,oBAAoB,IAAI;MACtFL,OAAO,IAAI,oBAAoBF,OAAO,CAACI,cAAc,CAACI,YAAY,MAAM;IAC1E;;IAEA;IACA,IAAIR,OAAO,CAACS,WAAW,EAAE;MACvBP,OAAO,IAAI,gCAAgC;MAC3CA,OAAO,IAAI,yBAAyBF,OAAO,CAACS,WAAW,CAAC5D,iBAAiB,IAAI;MAC7EqD,OAAO,IAAI,8BAA8BF,OAAO,CAACS,WAAW,CAAChE,sBAAsB,IAAI;MACvFyD,OAAO,IAAI,0BAA0BF,OAAO,CAACS,WAAW,CAACC,kBAAkB,IAAI;MAC/ER,OAAO,IAAI,+BAA+BF,OAAO,CAACS,WAAW,CAACE,sBAAsB,CAACjF,OAAO,CAAC,CAAC,CAAC,MAAM;MACrGwE,OAAO,IAAI,kBAAkBF,OAAO,CAACS,WAAW,CAAClE,UAAU,IAAI;MAC/D2D,OAAO,IAAI,uBAAuBF,OAAO,CAACS,WAAW,CAACG,eAAe,IAAI;MACzEV,OAAO,IAAI,mBAAmBF,OAAO,CAACS,WAAW,CAACI,WAAW,IAAI;MACjEX,OAAO,IAAI,wBAAwBF,OAAO,CAACS,WAAW,CAACK,eAAe,CAACpF,OAAO,CAAC,CAAC,CAAC,QAAQ;IAC3F;;IAEA;IACA,IAAIsE,OAAO,CAACe,MAAM,EAAE;MAClBb,OAAO,IAAI,2BAA2B;MACtCA,OAAO,IAAI,gBAAgBF,OAAO,CAACe,MAAM,CAACC,GAAG,CAACtF,OAAO,CAAC,CAAC,CAAC,KAAK;MAC7DwE,OAAO,IAAI,mBAAmB,CAACF,OAAO,CAACe,MAAM,CAACE,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEvF,OAAO,CAAC,CAAC,CAAC,OAAO;MACvFwE,OAAO,IAAI,aAAa,CAACF,OAAO,CAACe,MAAM,CAACG,MAAM,IAAI,EAAE,GAAG,EAAE,CAAC,EAAExF,OAAO,CAAC,CAAC,CAAC,UAAU;MAChFwE,OAAO,IAAI,yBAAyBF,OAAO,CAACe,MAAM,CAACI,WAAW,MAAM;IACtE;;IAEA;IACA,IAAInB,OAAO,CAACoB,GAAG,EAAE;MACflB,OAAO,IAAI,wBAAwB;MACnCA,OAAO,IAAI,qBAAqBF,OAAO,CAACoB,GAAG,CAAC1D,aAAa,IAAI;MAC7DwC,OAAO,IAAI,4BAA4BF,OAAO,CAACoB,GAAG,CAACC,mBAAmB,CAAC3F,OAAO,CAAC,CAAC,CAAC,MAAM;MACvFwE,OAAO,IAAI,0BAA0BF,OAAO,CAACoB,GAAG,CAACE,iBAAiB,CAAC5F,OAAO,CAAC,CAAC,CAAC,IAAI;MACjFwE,OAAO,IAAI,iBAAiB,CAACF,OAAO,CAACoB,GAAG,CAACG,SAAS,GAAG,GAAG,EAAE7F,OAAO,CAAC,CAAC,CAAC,OAAO;IAC7E;;IAEA;IACA9D,EAAE,CAACuC,aAAa,CAAC8F,WAAW,EAAEC,OAAO,CAAC;IAEtC1G,OAAO,CAACkB,GAAG,CAAC,wCAAwCuF,WAAW,EAAE,CAAC;EACpE,CAAC,EAAE,KAAK,CAAC;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,SAAS/F,gBAAgBA,CAACsH,KAAK,EAAE;EAC/B,MAAMC,QAAQ,GAAG,EAAE;EAEnB,KAAK,IAAInH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,KAAK,EAAElH,CAAC,EAAE,EAAE;IAC9BmH,QAAQ,CAACjH,IAAI,CAAC;MACZ+D,IAAI,EAAE,kDAAkDjE,CAAC,EAAE;MAC3DoH,MAAM,EAAE,+BAA+B;MACvC/C,YAAY,EAAE,yFAAyFrE,CAAC,EAAE;MAC1GqH,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAACtH,CAAC,GAAG,CAAC,CAAC;MAChE+D,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC/D,CAAC,GAAG,CAAC,CAAC;MAC1CuH,SAAS,EAAE,IAAI1D,IAAI,CAAC,CAAC,CAACgC,WAAW,CAAC,CAAC;MACnC2B,UAAU,EAAE,IAAI3D,IAAI,CAAC,CAAC,CAACgC,WAAW,CAAC,CAAC;MACpC4B,gBAAgB,EAAE;QAChBC,YAAY,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC1H,CAAC,GAAG,CAAC,CAAC;QACpE2H,UAAU,EAAE,WAAW3H,CAAC,EAAE;QAC1B4H,mBAAmB,EAAE,gBAAgB5H,CAAC;MACxC;IACF,CAAC,CAAC;EACJ;EAEA,OAAOmH,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASzF,4BAA4BA,CAACwF,KAAK,EAAE;EAC3C,MAAMW,SAAS,GAAG,EAAE;EAEpB,KAAK,IAAI7H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkH,KAAK,EAAElH,CAAC,EAAE,EAAE;IAC9B6H,SAAS,CAAC3H,IAAI,CAAC;MACb0D,EAAE,EAAE,YAAY5D,CAAC,EAAE;MACnB8D,IAAI,EAAE,YAAY;MAClBgE,SAAS,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC9H,CAAC,GAAG,CAAC,CAAC;MAC9C+H,OAAO,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,YAAY,CAAC,CAAC/H,CAAC,GAAG,CAAC,CAAC;MAClE+D,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC/D,CAAC,GAAG,CAAC,CAAC;MAC1CgE,QAAQ,EAAE;QACRJ,EAAE,EAAE,YAAY5D,CAAC,EAAE;QACnB8D,IAAI,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CAAC9D,CAAC,GAAG,CAAC,CAAC;QACvEiE,IAAI,EAAE,YAAYjE,CAAC,EAAE;QACrBkE,QAAQ,EAAE;MACZ,CAAC;MACDE,OAAO,EAAE;QACPR,EAAE,EAAE,WAAW5D,CAAC,EAAE;QAClB8D,IAAI,EAAE,CAAC,eAAe,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC9D,CAAC,GAAG,CAAC,CAAC;QAC5D+D,QAAQ,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC/D,CAAC,GAAG,CAAC,CAAC;QAC1CqE,YAAY,EAAE,4CAA4CrE,CAAC,EAAE;QAC7DsE,YAAY,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,kBAAkB,CAAC,CAACtE,CAAC,GAAG,CAAC,CAAC;QAC/EuE,SAAS,EAAEV,IAAI,CAACvD,GAAG,CAAC,CAAC;QACrBkE,WAAW,EAAE,gBAAgBxE,CAAC;MAChC,CAAC;MACD2E,gBAAgB,EAAE,CAChB;QACEf,EAAE,EAAE,QAAQ5D,CAAC,IAAI;QACjB4E,MAAM,EAAE,CAAC,sBAAsB,EAAE,gBAAgB,EAAE,wBAAwB,CAAC,CAAC5E,CAAC,GAAG,CAAC,CAAC;QACnF6E,UAAU,EAAE;UACVmD,UAAU,EAAE,YAAYhI,CAAC,EAAE;UAC3B4E,MAAM,EAAE;QACV;MACF,CAAC,EACD;QACEhB,EAAE,EAAE,QAAQ5D,CAAC,IAAI;QACjB4E,MAAM,EAAE,mBAAmB;QAC3BC,UAAU,EAAE;UACVO,SAAS,EAAE,WAAWpF,CAAC,EAAE;UACzBiI,YAAY,EAAE;QAChB;MACF,CAAC;IAEL,CAAC,CAAC;EACJ;EAEA,OAAOJ,SAAS;AAClB", "ignoreList": []}