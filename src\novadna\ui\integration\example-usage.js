/**
 * Example usage of NovaVisionIntegration with Next.js
 * 
 * This is a sample Next.js page that demonstrates how to use the NovaVisionIntegration
 * to render NovaDNA UI components using NovaVision.
 */

import React, { useEffect, useState } from 'react';
import NovaVisionComponents from '../NovaVisionComponents';
import NovaVisionIntegration from './NovaVisionIntegration';
import NovaVisionRenderer from '@nova-ui/ui-components/NovaVisionRenderer';

/**
 * Emergency Access Page
 * @returns {React.ReactNode} - The rendered component
 */
export default function EmergencyAccessPage() {
  const [uiSchema, setUiSchema] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    try {
      // Initialize NovaVisionComponents
      const novaVisionComponents = new NovaVisionComponents({
        baseUrl: '/novadna',
        theme: 'emergency'
      });
      
      // Initialize NovaVisionIntegration
      const novaVisionIntegration = new NovaVisionIntegration({
        apiBaseUrl: '/api',
        novaVisionComponents
      });
      
      // Get the emergency access UI schema
      const schema = novaVisionIntegration.getEmergencyAccessUI();
      
      // Set the UI schema
      setUiSchema(schema);
      setLoading(false);
    } catch (err) {
      console.error('Failed to initialize NovaVision integration:', err);
      setError(err.message);
      setLoading(false);
    }
  }, []);
  
  // Handle loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800">Loading Emergency Access...</h1>
          <p className="mt-2 text-gray-600">Please wait while we initialize the emergency access interface.</p>
        </div>
      </div>
    );
  }
  
  // Handle error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-red-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-800">Error</h1>
          <p className="mt-2 text-red-600">{error}</p>
          <button
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // Render the NovaVision UI
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <NovaVisionRenderer
          schema={uiSchema}
          onAction={handleAction}
          onSubmit={handleSubmit}
          onError={handleError}
        />
      </div>
    </div>
  );
  
  /**
   * Handle NovaVision actions
   * @param {String} actionId - The action ID
   * @param {Object} actionData - The action data
   */
  function handleAction(actionId, actionData) {
    console.log('Action:', actionId, actionData);
    
    // Handle specific actions
    switch (actionId) {
      case 'displayProfile':
        // Navigate to profile view page
        window.location.href = `/emergency/profile/${actionData.profileId}`;
        break;
      
      case 'displayQRCode':
        // Show QR code in modal
        // This would be implemented using your UI framework
        break;
      
      default:
        // Handle other actions
        break;
    }
  }
  
  /**
   * Handle form submissions
   * @param {String} formId - The form ID
   * @param {Object} formData - The form data
   */
  function handleSubmit(formId, formData) {
    console.log('Form submission:', formId, formData);
    
    // This would typically make an API call to the NovaDNA API
    // For example:
    fetch('/api/access/emergency', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(formData)
    })
      .then(response => response.json())
      .then(data => {
        if (data.status === 'success') {
          // Handle successful response
          handleAction('displayProfile', { profileId: data.data.profile.profileId });
        } else {
          // Handle error
          handleError(data.error);
        }
      })
      .catch(err => {
        // Handle fetch error
        handleError(err.message);
      });
  }
  
  /**
   * Handle errors
   * @param {String} errorMessage - The error message
   */
  function handleError(errorMessage) {
    console.error('Error:', errorMessage);
    
    // Show error message to user
    // This would be implemented using your UI framework
    alert(`Error: ${errorMessage}`);
  }
}
