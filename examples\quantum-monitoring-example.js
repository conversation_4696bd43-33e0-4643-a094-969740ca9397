/**
 * Quantum Monitoring Example
 * 
 * This example demonstrates how to use the Quantum Monitoring Dashboard
 * to monitor quantum resilience metrics in real-time.
 */

const QuantumMonitoringDashboard = require('../src/novavision/components/quantum-monitoring-dashboard');
const fs = require('fs');
const path = require('path');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../monitoring_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

// Create dashboard
const dashboard = new QuantumMonitoringDashboard({
  refreshInterval: 100, // 100ms for demo purposes
  predictiveWindow: 1000, // 1s for demo purposes
  alertThresholds: {
    piDrift: {
      warning: 0.00000005,
      critical: 0.0000001
    },
    entanglementHealth: {
      warning: 0.85,
      critical: 0.75
    },
    psiContainment: {
      warning: 0.015,
      critical: 0.02
    }
  }
});

// Set up event listeners
dashboard.on('metrics-updated', (metrics) => {
  console.log(`Metrics updated: π10³ drift=${metrics.piDrift.current.toExponential(6)}, entanglement=${metrics.entanglementHealth.current.toFixed(3)}, Ψₓ=${metrics.psiContainment.current.toFixed(3)}`);
});

dashboard.on('predictions-updated', (predictions) => {
  console.log(`Predictions updated: π10³ drift=${predictions.piDrift.toExponential(6)}, entanglement=${predictions.entanglementHealth.toFixed(3)}, Ψₓ=${predictions.psiContainment.toFixed(3)}`);
});

dashboard.on('alert', (alert) => {
  console.log(`ALERT [${alert.type}] ${alert.category}: ${alert.message}`);
});

dashboard.on('playbook-generated', (playbook) => {
  console.log(`Playbook generated: ${playbook.title}`);
  playbook.steps.forEach(step => {
    console.log(`  ${step.id}. ${step.action}`);
  });
});

// Generate HTML report
function generateHTMLReport() {
  const data = dashboard.getDashboardData();
  
  // Generate HTML
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Quantum Monitoring Dashboard</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .dashboard {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 20px;
    }
    .card {
      background-color: #fff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .card.full-width {
      grid-column: span 2;
    }
    .metric {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      margin-right: 10px;
    }
    .metric-label {
      font-size: 14px;
      color: #666;
    }
    .gauge {
      width: 100%;
      height: 20px;
      background-color: #eee;
      border-radius: 10px;
      overflow: hidden;
      margin-bottom: 5px;
    }
    .gauge-fill {
      height: 100%;
      border-radius: 10px;
      transition: width 0.3s ease;
    }
    .gauge-fill.good {
      background-color: #4CAF50;
    }
    .gauge-fill.warning {
      background-color: #FF9800;
    }
    .gauge-fill.critical {
      background-color: #F44336;
    }
    .alert {
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
    }
    .alert.warning {
      background-color: #FFF3CD;
      border-left: 5px solid #FFD700;
    }
    .alert.critical {
      background-color: #F8D7DA;
      border-left: 5px solid #DC3545;
    }
    .alert.predicted {
      background-color: #D1ECF1;
      border-left: 5px solid #0DCAF0;
    }
    .playbook {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #E8F4FD;
      border-radius: 5px;
    }
    .playbook-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    .playbook-step {
      margin-bottom: 5px;
      padding: 5px;
      background-color: #fff;
      border-radius: 3px;
    }
    .timestamp {
      font-size: 12px;
      color: #666;
    }
  </style>
</head>
<body>
  <h1>Quantum Monitoring Dashboard</h1>
  <p class="timestamp">Generated: ${new Date().toLocaleString()}</p>
  
  <div class="dashboard">
    <div class="card">
      <h2>π10³ Drift</h2>
      <div class="metric">
        <div class="metric-value">${data.metrics.piDrift.current.toExponential(6)}</div>
        <div class="metric-label">Current</div>
      </div>
      <div class="gauge">
        <div class="gauge-fill ${data.metrics.piDrift.current > data.thresholds.piDrift.critical ? 'critical' : data.metrics.piDrift.current > data.thresholds.piDrift.warning ? 'warning' : 'good'}" style="width: ${Math.min(100, data.metrics.piDrift.current / data.thresholds.piDrift.critical * 100)}%;"></div>
      </div>
      <div class="metric">
        <div class="metric-value">${data.metrics.piDrift.predicted.toExponential(6)}</div>
        <div class="metric-label">Predicted</div>
      </div>
      <div class="gauge">
        <div class="gauge-fill ${data.metrics.piDrift.predicted > data.thresholds.piDrift.critical ? 'critical' : data.metrics.piDrift.predicted > data.thresholds.piDrift.warning ? 'warning' : 'good'}" style="width: ${Math.min(100, data.metrics.piDrift.predicted / data.thresholds.piDrift.critical * 100)}%;"></div>
      </div>
    </div>
    
    <div class="card">
      <h2>Entanglement Health</h2>
      <div class="metric">
        <div class="metric-value">${data.metrics.entanglementHealth.current.toFixed(3)}</div>
        <div class="metric-label">Current</div>
      </div>
      <div class="gauge">
        <div class="gauge-fill ${data.metrics.entanglementHealth.current < data.thresholds.entanglementHealth.critical ? 'critical' : data.metrics.entanglementHealth.current < data.thresholds.entanglementHealth.warning ? 'warning' : 'good'}" style="width: ${data.metrics.entanglementHealth.current * 100}%;"></div>
      </div>
      <div class="metric">
        <div class="metric-value">${data.metrics.entanglementHealth.predicted.toFixed(3)}</div>
        <div class="metric-label">Predicted</div>
      </div>
      <div class="gauge">
        <div class="gauge-fill ${data.metrics.entanglementHealth.predicted < data.thresholds.entanglementHealth.critical ? 'critical' : data.metrics.entanglementHealth.predicted < data.thresholds.entanglementHealth.warning ? 'warning' : 'good'}" style="width: ${data.metrics.entanglementHealth.predicted * 100}%;"></div>
      </div>
    </div>
    
    <div class="card">
      <h2>Ψₓ Containment</h2>
      <div class="metric">
        <div class="metric-value">${data.metrics.psiContainment.current.toFixed(3)}</div>
        <div class="metric-label">Current</div>
      </div>
      <div class="gauge">
        <div class="gauge-fill ${data.metrics.psiContainment.current > data.thresholds.psiContainment.critical ? 'critical' : data.metrics.psiContainment.current > data.thresholds.psiContainment.warning ? 'warning' : 'good'}" style="width: ${Math.min(100, data.metrics.psiContainment.current / 0.05 * 100)}%;"></div>
      </div>
      <div class="metric">
        <div class="metric-value">${data.metrics.psiContainment.predicted.toFixed(3)}</div>
        <div class="metric-label">Predicted</div>
      </div>
      <div class="gauge">
        <div class="gauge-fill ${data.metrics.psiContainment.predicted > data.thresholds.psiContainment.critical ? 'critical' : data.metrics.psiContainment.predicted > data.thresholds.psiContainment.warning ? 'warning' : 'good'}" style="width: ${Math.min(100, data.metrics.psiContainment.predicted / 0.05 * 100)}%;"></div>
      </div>
    </div>
    
    <div class="card">
      <h2>Alerts</h2>
      ${data.alerts.length > 0 ? data.alerts.map(alert => `
        <div class="alert ${alert.type}">
          <div><strong>${alert.category}</strong>: ${alert.message}</div>
          <div class="timestamp">${new Date(alert.timestamp).toLocaleString()}</div>
        </div>
      `).join('') : '<p>No alerts</p>'}
    </div>
    
    <div class="card full-width">
      <h2>Remediation Playbooks</h2>
      ${data.playbooks.length > 0 ? data.playbooks.map(playbook => `
        <div class="playbook">
          <div class="playbook-title">${playbook.title}</div>
          <div class="timestamp">${new Date(playbook.timestamp).toLocaleString()}</div>
          ${playbook.steps.map(step => `
            <div class="playbook-step">
              ${step.id}. ${step.action}
            </div>
          `).join('')}
        </div>
      `).join('') : '<p>No playbooks</p>'}
    </div>
  </div>
  
  <footer>
    <p>NovaFuse Quantum Resilience - Copyright © ${new Date().getFullYear()}</p>
  </footer>
</body>
</html>
  `;
  
  // Save HTML file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'quantum-monitoring-dashboard.html'),
    html
  );
  
  console.log(`HTML report saved to: ${path.join(RESULTS_DIR, 'quantum-monitoring-dashboard.html')}`);
}

// Run for 10 seconds
console.log('Starting quantum monitoring...');
setTimeout(() => {
  console.log('Generating HTML report...');
  generateHTMLReport();
  
  console.log('Stopping quantum monitoring...');
  dashboard.stopMonitoring();
  
  console.log('Done!');
}, 10000);

// Display system status
console.log(`
[QUANTUM SHIELD] 
  ├─ Core: ✅ 100% operational 
  ├─ Operations: ⚠️ Needs patching
  └─ Adversarial: 🚨 Critical fixes required
`);

console.log('Monitoring quantum resilience metrics...');
console.log('Press Ctrl+C to stop');
