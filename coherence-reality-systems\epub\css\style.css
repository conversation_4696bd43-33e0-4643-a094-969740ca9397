/* Base Styles */
body {
    font-family: "Georgia", serif;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    color: #333;
    background: #f5f5f5;
}

/* Headings */
h1, h2, h3 {
    font-family: "Arial", sans-serif;
    color: #2c3e50;
    margin-top: 2rem;
    text-align: center;
}

h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 0.8rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Dictionary Entry Styles */
.term-entry {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.definition {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

/* Table Styles */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

th, td {
    padding: 1rem;
    text-align: left;
    border: 1px solid #e0e0e0;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Diagrams and Equations */
.diagram {
    margin: 2rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

.equation {
    display: block;
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    font-family: 'Cambria Math', serif;
    font-size: 1.2rem;
}

/* Code and JSON */
pre {
    background: #2d2d2d;
    color: #fff;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

pre code {
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    margin: 1rem 0;
    display: block;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

/* Navigation */
nav {
    margin: 2rem 0;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

nav ol {
    list-style-type: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
}

nav li {
    margin: 0.5rem 0;
    flex: 0 1 200px;
}

/* Print Styles */
@media print {
    body {
        max-width: none;
        padding: 0;
        background: white;
    }
    
    .term-entry {
        page-break-inside: avoid;
    }
    
    nav {
        page-break-before: always;
    }
    
    img {
        box-shadow: none;
    }
    
    pre {
        page-break-inside: avoid;
        break-inside: avoid;
    }
}
