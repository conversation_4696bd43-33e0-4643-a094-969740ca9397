/**
 * Services Index
 *
 * This file exports all services for the NovaAssure API.
 */

const controlService = require('./controlService');
const testPlanService = require('./testPlanService');
const testExecutionService = require('./testExecutionService');
const evidenceService = require('./evidenceService');
const reportService = require('./reportService');
const blockchainEvidence = require('./blockchainEvidence');
const testEngine = require('./testEngine');
const reportTemplates = require('./reportTemplates');
const integrationService = require('./integrationService');

module.exports = {
  controlService,
  testPlanService,
  testExecutionService,
  evidenceService,
  reportService,
  blockchainEvidence,
  testEngine,
  reportTemplates,
  integrationService
};
