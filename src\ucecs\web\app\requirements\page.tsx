'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiPlus, FiFilter, FiDownload, FiTrash2, FiEdit, FiEye, FiLink } from 'react-icons/fi';

// Mock data for requirements
const mockRequirements = [
  { 
    id: 'req-001', 
    name: 'Password Policy', 
    framework: 'ISO 27001', 
    control: 'A.9.4.3', 
    status: 'Compliant',
    lastAssessed: '2023-10-15',
    evidenceCount: 3
  },
  { 
    id: 'req-002', 
    name: 'Access Control', 
    framework: 'NIST CSF', 
    control: 'PR.AC-1', 
    status: 'Partially Compliant',
    lastAssessed: '2023-10-14',
    evidenceCount: 2
  },
  { 
    id: 'req-003', 
    name: 'Data Backup', 
    framework: 'ISO 27001', 
    control: 'A.12.3.1', 
    status: 'Compliant',
    lastAssessed: '2023-10-13',
    evidenceCount: 4
  },
  { 
    id: 'req-004', 
    name: 'Incident Response', 
    framework: 'NIST CSF', 
    control: 'RS.CO-1', 
    status: 'Non-Compliant',
    lastAssessed: '2023-10-12',
    evidenceCount: 1
  },
  { 
    id: 'req-005', 
    name: 'Security Awareness Training', 
    framework: 'ISO 27001', 
    control: 'A.7.2.2', 
    status: 'Compliant',
    lastAssessed: '2023-10-11',
    evidenceCount: 5
  },
  { 
    id: 'req-006', 
    name: 'Vulnerability Management', 
    framework: 'NIST CSF', 
    control: 'ID.RA-1', 
    status: 'Partially Compliant',
    lastAssessed: '2023-10-10',
    evidenceCount: 2
  },
  { 
    id: 'req-007', 
    name: 'Network Security', 
    framework: 'ISO 27001', 
    control: 'A.13.1.1', 
    status: 'Compliant',
    lastAssessed: '2023-10-09',
    evidenceCount: 3
  },
  { 
    id: 'req-008', 
    name: 'Physical Security', 
    framework: 'ISO 27001', 
    control: 'A.11.1.1', 
    status: 'Compliant',
    lastAssessed: '2023-10-08',
    evidenceCount: 2
  },
  { 
    id: 'req-009', 
    name: 'Business Continuity', 
    framework: 'ISO 27001', 
    control: 'A.17.1.1', 
    status: 'Partially Compliant',
    lastAssessed: '2023-10-07',
    evidenceCount: 1
  },
  { 
    id: 'req-010', 
    name: 'Cryptography', 
    framework: 'ISO 27001', 
    control: 'A.10.1.1', 
    status: 'Non-Compliant',
    lastAssessed: '2023-10-06',
    evidenceCount: 0
  },
];

export default function RequirementsPage() {
  const [requirements, setRequirements] = useState(mockRequirements);
  const [selectedRequirements, setSelectedRequirements] = useState<string[]>([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    framework: '',
    status: '',
  });

  // In a real application, we would fetch the data from the API
  useEffect(() => {
    // Fetch data from API
    // For now, we'll use the mock data
    setRequirements(mockRequirements);
  }, []);

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      setSelectedRequirements(requirements.map((item) => item.id));
    } else {
      setSelectedRequirements([]);
    }
  };

  const handleSelectRequirement = (id: string) => {
    if (selectedRequirements.includes(id)) {
      setSelectedRequirements(selectedRequirements.filter((item) => item !== id));
    } else {
      setSelectedRequirements([...selectedRequirements, id]);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
    });
  };

  const applyFilters = () => {
    let filteredRequirements = [...mockRequirements];

    if (filters.framework) {
      filteredRequirements = filteredRequirements.filter((item) => item.framework === filters.framework);
    }

    if (filters.status) {
      filteredRequirements = filteredRequirements.filter((item) => item.status === filters.status);
    }

    setRequirements(filteredRequirements);
    setIsFilterOpen(false);
  };

  const resetFilters = () => {
    setFilters({
      framework: '',
      status: '',
    });
    setRequirements(mockRequirements);
    setIsFilterOpen(false);
  };

  // Get unique frameworks for filter dropdown
  const frameworks = [...new Set(mockRequirements.map(item => item.framework))];
  
  // Get unique statuses for filter dropdown
  const statuses = [...new Set(mockRequirements.map(item => item.status))];

  return (
    <MainLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Requirements</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage your compliance requirements</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="btn btn-outline flex items-center"
          >
            <FiFilter className="mr-2" />
            Filter
          </button>
          <button className="btn btn-primary flex items-center">
            <FiPlus className="mr-2" />
            Add Requirement
          </button>
        </div>
      </div>

      {/* Filter panel */}
      {isFilterOpen && (
        <div className="card mb-6">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Filter Requirements</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="framework" className="label">
                Framework
              </label>
              <select
                id="framework"
                name="framework"
                value={filters.framework}
                onChange={handleFilterChange}
                className="input"
              >
                <option value="">All Frameworks</option>
                {frameworks.map(framework => (
                  <option key={framework} value={framework}>{framework}</option>
                ))}
              </select>
            </div>
            <div>
              <label htmlFor="status" className="label">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="input"
              >
                <option value="">All Statuses</option>
                {statuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <button onClick={resetFilters} className="btn btn-outline">
              Reset
            </button>
            <button onClick={applyFilters} className="btn btn-primary">
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Requirements table */}
      <div className="card">
        <div className="flex justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Requirements List</h2>
          {selectedRequirements.length > 0 && (
            <div className="flex space-x-2">
              <button className="btn btn-outline flex items-center text-sm">
                <FiDownload className="mr-1" />
                Export
              </button>
              <button className="btn btn-outline flex items-center text-sm text-red-500 border-red-500 hover:bg-red-500 hover:text-white">
                <FiTrash2 className="mr-1" />
                Delete
              </button>
            </div>
          )}
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    onChange={handleSelectAll}
                    checked={selectedRequirements.length === requirements.length && requirements.length > 0}
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  ID
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Framework
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Control
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Last Assessed
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Evidence
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-secondary-light divide-y divide-gray-200 dark:divide-gray-700">
              {requirements.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      checked={selectedRequirements.includes(item.id)}
                      onChange={() => handleSelectRequirement(item.id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {item.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.framework}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.control}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        item.status === 'Compliant'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : item.status === 'Non-Compliant'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                      }`}
                    >
                      {item.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    {item.lastAssessed}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-full">
                      {item.evidenceCount}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button className="text-primary hover:text-primary-dark" title="View">
                        <FiEye />
                      </button>
                      <button className="text-primary hover:text-primary-dark" title="Edit">
                        <FiEdit />
                      </button>
                      <button className="text-primary hover:text-primary-dark" title="Link Evidence">
                        <FiLink />
                      </button>
                      <button className="text-red-500 hover:text-red-700" title="Delete">
                        <FiTrash2 />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">{requirements.length}</span> of{' '}
            <span className="font-medium">{mockRequirements.length}</span> requirements
          </div>
          <div className="flex space-x-2">
            <button className="btn btn-outline">Previous</button>
            <button className="btn btn-outline">Next</button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
