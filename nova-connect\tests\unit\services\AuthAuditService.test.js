/**
 * Authentication Audit Service Tests
 */

const AuthAuditService = require('../../../api/services/AuthAuditService');
const AuditService = require('../../../api/services/AuditService');
const path = require('path');

// Mock AuditService
jest.mock('../../../api/services/AuditService');

describe('AuthAuditService', () => {
  let authAuditService;
  const testDataDir = path.join(__dirname, 'test-data');
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock AuditService implementation
    AuditService.mockImplementation(() => ({
      logEvent: jest.fn().mockResolvedValue({ id: 'test-log-id' }),
      getAuditLogs: jest.fn().mockResolvedValue({
        logs: [],
        total: 0,
        page: 1,
        limit: 10
      })
    }));
    
    // Create a new instance for each test
    authAuditService = new AuthAuditService(testDataDir);
  });
  
  describe('constructor', () => {
    it('should initialize with the correct data directory', () => {
      expect(AuditService).toHaveBeenCalledWith(testDataDir);
      expect(authAuditService.resourceType).toBe('auth');
    });
  });
  
  describe('logLoginAttempt', () => {
    it('should log successful login attempt', async () => {
      const loginData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        method: 'password'
      };
      
      await authAuditService.logLoginAttempt(loginData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'LOGIN',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          method: 'password',
          success: true,
          reason: null
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
    
    it('should log failed login attempt', async () => {
      const loginData = {
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: false,
        reason: 'Invalid password',
        method: 'password'
      };
      
      await authAuditService.logLoginAttempt(loginData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: null,
        action: 'LOGIN',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          method: 'password',
          success: false,
          reason: 'Invalid password'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'failure',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
    
    it('should include additional details', async () => {
      const loginData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        method: 'oauth2',
        details: {
          provider: 'google',
          email: '<EMAIL>'
        }
      };
      
      await authAuditService.logLoginAttempt(loginData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'LOGIN',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          method: 'oauth2',
          success: true,
          reason: null,
          provider: 'google',
          email: '<EMAIL>'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  
  describe('logLogout', () => {
    it('should log logout event', async () => {
      const logoutData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0'
      };
      
      await authAuditService.logLogout(logoutData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'LOGOUT',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {},
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  
  describe('logRegistration', () => {
    it('should log successful registration', async () => {
      const registrationData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        details: {
          email: '<EMAIL>'
        }
      };
      
      await authAuditService.logRegistration(registrationData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: 'REGISTER',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          success: true,
          reason: null,
          email: '<EMAIL>'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  
  describe('logTwoFactorAuth', () => {
    it('should log two-factor authentication setup', async () => {
      const twoFactorData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        action: 'setup',
        success: true
      };
      
      await authAuditService.logTwoFactorAuth(twoFactorData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: '2FA_SETUP',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          success: true,
          reason: null
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'success',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
    
    it('should log failed two-factor authentication verification', async () => {
      const twoFactorData = {
        userId: 'user-123',
        username: 'testuser',
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        action: 'verify',
        success: false,
        reason: 'Invalid token'
      };
      
      await authAuditService.logTwoFactorAuth(twoFactorData);
      
      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({
        userId: 'user-123',
        action: '2FA_VERIFY',
        resourceType: 'auth',
        resourceId: 'testuser',
        details: {
          success: false,
          reason: 'Invalid token'
        },
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        status: 'failure',
        teamId: null,
        environmentId: null,
        tenantId: null
      });
    });
  });
  
  describe('getAuthAuditLogs', () => {
    it('should get authentication audit logs with filters', async () => {
      const filters = {
        userId: 'user-123',
        startDate: '2023-01-01',
        endDate: '2023-01-31',
        action: 'LOGIN',
        status: 'success',
        page: 1,
        limit: 10
      };
      
      await authAuditService.getAuthAuditLogs(filters);
      
      expect(authAuditService.auditService.getAuditLogs).toHaveBeenCalledWith({
        userId: 'user-123',
        startDate: '2023-01-01',
        endDate: '2023-01-31',
        action: 'LOGIN',
        status: 'success',
        page: 1,
        limit: 10,
        resourceType: 'auth'
      });
    });
  });
});
