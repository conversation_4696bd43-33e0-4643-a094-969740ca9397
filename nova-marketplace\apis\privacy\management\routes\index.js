/**
 * Privacy Management API Routes
 * 
 * This file exports all routes for the Privacy Management API.
 */

const express = require('express');
const router = express.Router();

// Import route modules
const dataProcessingActivityRoutes = require('./dataProcessingActivityRoutes');
const subjectRequestRoutes = require('./subjectRequestRoutes');
const consentRecordRoutes = require('./consentRecordRoutes');
const privacyNoticeRoutes = require('./privacyNoticeRoutes');
const dataBreachRoutes = require('./dataBreachRoutes');
const integrationRoutes = require('./integrationRoutes');
const notificationRoutes = require('./notificationRoutes');
const regulatoryComplianceRoutes = require('./regulatoryComplianceRoutes');

// Mount routes
router.use('/data-processing', dataProcessingActivityRoutes);
router.use('/subject-requests', subjectRequestRoutes);
router.use('/consent', consentRecordRoutes);
router.use('/notices', privacyNoticeRoutes);
router.use('/breaches', dataBreachRoutes);
router.use('/integrations', integrationRoutes);
router.use('/notifications', notificationRoutes);
router.use('/compliance', regulatoryComplianceRoutes);

// Health check route
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'privacy-management-api',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
