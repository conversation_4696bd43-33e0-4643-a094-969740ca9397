/**
 * NovaCore SOC 2 Assessment Service
 * 
 * This service provides functionality for managing SOC 2 assessments.
 */

const { SOC2Assessment, SOC2Control, SOC2ControlImplementation } = require('../models');
const logger = require('../../../config/logger');
const { ValidationError, NotFoundError } = require('../../../api/utils/errors');

class SOC2AssessmentService {
  /**
   * Create a new SOC 2 assessment
   * @param {Object} data - Assessment data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Created assessment
   */
  async createAssessment(data, userId) {
    try {
      logger.info('Creating new SOC 2 assessment', { organizationId: data.organizationId });
      
      // Set created by
      data.createdBy = userId;
      data.updatedBy = userId;
      
      // Create assessment
      const assessment = new SOC2Assessment(data);
      await assessment.save();
      
      logger.info('SOC 2 assessment created successfully', { id: assessment._id });
      
      return assessment;
    } catch (error) {
      logger.error('Error creating SOC 2 assessment', { error });
      throw error;
    }
  }
  
  /**
   * Get all SOC 2 assessments for an organization
   * @param {string} organizationId - Organization ID
   * @param {Object} filter - Filter criteria
   * @param {Object} options - Query options (pagination, sorting)
   * @returns {Promise<Object>} - Assessments with pagination info
   */
  async getAllAssessments(organizationId, filter = {}, options = {}) {
    try {
      const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
      
      // Build query
      const query = { organizationId };
      
      // Apply filters
      if (filter.type) {
        query.type = filter.type;
      }
      
      if (filter.status) {
        query.status = filter.status;
      }
      
      // Execute query with pagination
      const skip = (page - 1) * limit;
      
      const [assessments, total] = await Promise.all([
        SOC2Assessment.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit),
        SOC2Assessment.countDocuments(query)
      ]);
      
      // Calculate pagination info
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;
      
      return {
        data: assessments,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrev
        }
      };
    } catch (error) {
      logger.error('Error getting SOC 2 assessments', { error });
      throw error;
    }
  }
  
  /**
   * Get SOC 2 assessment by ID
   * @param {string} id - Assessment ID
   * @returns {Promise<Object>} - Assessment
   */
  async getAssessmentById(id) {
    try {
      const assessment = await SOC2Assessment.findById(id);
      
      if (!assessment) {
        throw new NotFoundError(`SOC 2 assessment with ID ${id} not found`);
      }
      
      return assessment;
    } catch (error) {
      logger.error('Error getting SOC 2 assessment by ID', { id, error });
      throw error;
    }
  }
  
  /**
   * Update SOC 2 assessment
   * @param {string} id - Assessment ID
   * @param {Object} data - Updated assessment data
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Updated assessment
   */
  async updateAssessment(id, data, userId) {
    try {
      // Get existing assessment
      const assessment = await this.getAssessmentById(id);
      
      // Set updated by
      data.updatedBy = userId;
      
      // Update assessment
      Object.assign(assessment, data);
      await assessment.save();
      
      logger.info('SOC 2 assessment updated successfully', { id });
      
      return assessment;
    } catch (error) {
      logger.error('Error updating SOC 2 assessment', { id, error });
      throw error;
    }
  }
  
  /**
   * Delete SOC 2 assessment
   * @param {string} id - Assessment ID
   * @returns {Promise<boolean>} - Deletion success
   */
  async deleteAssessment(id) {
    try {
      const result = await SOC2Assessment.findByIdAndDelete(id);
      
      if (!result) {
        throw new NotFoundError(`SOC 2 assessment with ID ${id} not found`);
      }
      
      logger.info('SOC 2 assessment deleted successfully', { id });
      
      return true;
    } catch (error) {
      logger.error('Error deleting SOC 2 assessment', { id, error });
      throw error;
    }
  }
}

module.exports = new SOC2AssessmentService();
