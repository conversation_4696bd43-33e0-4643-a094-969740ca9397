/**
 * Comphyological Tensor Core Dashboard
 *
 * This script provides the functionality for the dashboard.
 */

// API base URL
const API_BASE_URL = 'http://localhost:3000/api/tensor';

// Dashboard state
let dashboardState = {
    comphyonHistory: [],
    resonanceHistory: [],
    lastResult: null,
    lastResonance: null,
    isUpdating: false,
    updateInterval: 2000, // 2 seconds
    maxHistoryLength: 50
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Initialize range input display values
    document.querySelectorAll('.form-range').forEach(range => {
        const valueDisplay = range.nextElementSibling;
        valueDisplay.textContent = range.value;

        range.addEventListener('input', function() {
            valueDisplay.textContent = this.value;
        });
    });

    // Initialize form submission
    document.getElementById('data-form').addEventListener('submit', function(event) {
        event.preventDefault();
        processData();
    });

    // Initialize reset button
    document.getElementById('reset-button').addEventListener('click', function() {
        resetMetrics();
    });

    // Start dashboard updates
    startDashboardUpdates();

    // Initial data processing
    processData();
});

/**
 * Start dashboard updates
 */
function startDashboardUpdates() {
    if (dashboardState.isUpdating) {
        return;
    }

    dashboardState.isUpdating = true;

    // Update dashboard every updateInterval milliseconds
    setInterval(function() {
        updateDashboard();
    }, dashboardState.updateInterval);
}

/**
 * Update dashboard
 */
async function updateDashboard() {
    try {
        // Get metrics
        const metricsResponse = await axios.get(`${API_BASE_URL}/metrics`);

        // Get resonance
        const resonanceResponse = await axios.get(`${API_BASE_URL}/resonance`);

        // Update dashboard state
        updateDashboardState(metricsResponse.data.metrics, resonanceResponse.data.resonance);

        // Update visualizations
        updateVisualizations();
    } catch (error) {
        console.error('Error updating dashboard:', error);
    }
}

/**
 * Update dashboard state
 * @param {Object} metrics - Metrics data
 * @param {Object} resonance - Resonance data
 */
function updateDashboardState(metrics, resonance) {
    // Update comphyon history
    if (metrics && metrics.lastComphyonValue !== undefined) {
        dashboardState.comphyonHistory.push({
            timestamp: Date.now(),
            value: metrics.lastComphyonValue
        });

        // Trim history if needed
        if (dashboardState.comphyonHistory.length > dashboardState.maxHistoryLength) {
            dashboardState.comphyonHistory.shift();
        }
    }

    // Update resonance history
    if (resonance) {
        dashboardState.resonanceHistory.push({
            timestamp: Date.now(),
            frequency: resonance.frequency,
            deviation: resonance.deviation,
            isQuantumSilence: resonance.isQuantumSilence,
            phaseAlignment: resonance.phaseAlignment,
            quantumVacuumNoise: resonance.quantumVacuumNoise
        });

        // Trim history if needed
        if (dashboardState.resonanceHistory.length > dashboardState.maxHistoryLength) {
            dashboardState.resonanceHistory.shift();
        }

        // Update last resonance
        dashboardState.lastResonance = resonance;
    }
}

/**
 * Update visualizations
 */
function updateVisualizations() {
    // Update status values
    updateStatusValues();

    // Update charts
    updateComphyonChart();
    updateResonanceChart();

    // Update other visualizations if result is available
    if (dashboardState.lastResult) {
        updateEnergyChart();
        updateWeightChart();
        updateResonanceRadar();
        updateTensorVisualizations();
    }
}

/**
 * Update status values
 */
function updateStatusValues() {
    // Update comphyon value
    const comphyonValue = document.getElementById('comphyon-value');
    if (dashboardState.comphyonHistory.length > 0) {
        const lastComphyon = dashboardState.comphyonHistory[dashboardState.comphyonHistory.length - 1].value;
        comphyonValue.textContent = lastComphyon.toFixed(4);
    }

    // Update resonance frequency
    const resonanceFrequency = document.getElementById('resonance-frequency');
    if (dashboardState.lastResonance) {
        resonanceFrequency.textContent = `${dashboardState.lastResonance.frequency.toFixed(2)} Hz`;
    }

    // Update quantum silence
    const quantumSilence = document.getElementById('quantum-silence');
    if (dashboardState.lastResonance) {
        if (dashboardState.lastResonance.isQuantumSilence) {
            quantumSilence.textContent = 'Yes';
            quantumSilence.className = 'status-value quantum-silence-yes';
        } else {
            quantumSilence.textContent = 'No';
            quantumSilence.className = 'status-value quantum-silence-no';
        }
    }

    // Update dominant engine
    const dominantEngine = document.getElementById('dominant-engine');
    if (dashboardState.lastResult && dashboardState.lastResult.weights) {
        const weights = dashboardState.lastResult.weights;
        const entries = Object.entries(weights);
        entries.sort((a, b) => b[1] - a[1]);

        const [topEngine, topWeight] = entries[0];

        if (topWeight >= 0.6) {
            dominantEngine.textContent = topEngine.toUpperCase();
            dominantEngine.className = `status-value dominant-engine-${topEngine}`;
        } else {
            dominantEngine.textContent = 'None';
            dominantEngine.className = 'status-value dominant-engine-none';
        }
    }
}

/**
 * Process data
 */
async function processData() {
    try {
        // Get form data
        const csdeData = {
            governance: parseFloat(document.getElementById('csde-governance').value),
            dataQuality: parseFloat(document.getElementById('csde-data').value),
            action: document.getElementById('csde-action').value,
            confidence: parseFloat(document.getElementById('csde-confidence').value)
        };

        const csfeData = {
            risk: parseFloat(document.getElementById('csfe-risk').value),
            policyCompliance: parseFloat(document.getElementById('csfe-finance').value),
            action: document.getElementById('csfe-action').value,
            confidence: parseFloat(document.getElementById('csfe-confidence').value)
        };

        const csmeData = {
            trustFactor: parseFloat(document.getElementById('csme-bio').value),
            integrityFactor: parseFloat(document.getElementById('csme-med').value),
            action: document.getElementById('csme-action').value,
            confidence: parseFloat(document.getElementById('csme-confidence').value)
        };

        // Process data through API
        const response = await axios.post(`${API_BASE_URL}/process`, {
            csdeData,
            csfeData,
            csmeData
        });

        // Update last result
        dashboardState.lastResult = response.data.result;

        // Update dashboard
        updateDashboard();
    } catch (error) {
        console.error('Error processing data:', error);
    }
}

/**
 * Reset metrics
 */
async function resetMetrics() {
    try {
        // Reset metrics through API
        await axios.post(`${API_BASE_URL}/reset-metrics`);

        // Clear dashboard state
        dashboardState.comphyonHistory = [];
        dashboardState.resonanceHistory = [];

        // Update dashboard
        updateDashboard();
    } catch (error) {
        console.error('Error resetting metrics:', error);
    }
}

// Placeholder functions for chart updates - these will be implemented in the next steps
function updateComphyonChart() {
    // Get container
    const container = document.getElementById('comphyon-chart');

    // Clear container
    container.innerHTML = '';

    // Check if we have data
    if (dashboardState.comphyonHistory.length === 0) {
        return;
    }

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 50 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Set scales
    const x = d3.scaleTime()
        .domain(d3.extent(dashboardState.comphyonHistory, d => d.timestamp))
        .range([0, width]);

    const y = d3.scaleLinear()
        .domain([
            d3.min(dashboardState.comphyonHistory, d => d.value) * 1.1,
            d3.max(dashboardState.comphyonHistory, d => d.value) * 1.1
        ])
        .range([height, 0]);

    // Create line
    const line = d3.line()
        .x(d => x(d.timestamp))
        .y(d => y(d.value))
        .curve(d3.curveMonotoneX);

    // Add X axis
    svg.append('g')
        .attr('class', 'axis')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(x).ticks(5).tickFormat(d3.timeFormat('%H:%M:%S')));

    // Add Y axis
    svg.append('g')
        .attr('class', 'axis')
        .call(d3.axisLeft(y));

    // Add line
    svg.append('path')
        .datum(dashboardState.comphyonHistory)
        .attr('class', 'line line-comphyon')
        .attr('d', line);

    // Add points
    svg.selectAll('.dot')
        .data(dashboardState.comphyonHistory)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', d => x(d.timestamp))
        .attr('cy', d => y(d.value))
        .attr('r', 3)
        .attr('fill', '#007bff');

    // Add title
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#343a40')
        .text('Comphyon Value Over Time');
}

function updateResonanceChart() {
    // Get container
    const container = document.getElementById('resonance-chart');

    // Clear container
    container.innerHTML = '';

    // Check if we have data
    if (dashboardState.resonanceHistory.length === 0) {
        return;
    }

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 50 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Set scales
    const x = d3.scaleTime()
        .domain(d3.extent(dashboardState.resonanceHistory, d => d.timestamp))
        .range([0, width]);

    const y = d3.scaleLinear()
        .domain([
            d3.min(dashboardState.resonanceHistory, d => d.frequency) * 0.99,
            d3.max(dashboardState.resonanceHistory, d => d.frequency) * 1.01
        ])
        .range([height, 0]);

    // Create line
    const line = d3.line()
        .x(d => x(d.timestamp))
        .y(d => y(d.frequency))
        .curve(d3.curveMonotoneX);

    // Add X axis
    svg.append('g')
        .attr('class', 'axis')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(x).ticks(5).tickFormat(d3.timeFormat('%H:%M:%S')));

    // Add Y axis
    svg.append('g')
        .attr('class', 'axis')
        .call(d3.axisLeft(y));

    // Add line
    svg.append('path')
        .datum(dashboardState.resonanceHistory)
        .attr('class', 'line line-resonance')
        .attr('d', line);

    // Add points
    svg.selectAll('.dot')
        .data(dashboardState.resonanceHistory)
        .enter()
        .append('circle')
        .attr('class', 'dot')
        .attr('cx', d => x(d.timestamp))
        .attr('cy', d => y(d.frequency))
        .attr('r', 3)
        .attr('fill', d => d.isQuantumSilence ? '#28a745' : '#6f42c1');

    // Add target frequency line
    svg.append('line')
        .attr('x1', 0)
        .attr('y1', y(396))
        .attr('x2', width)
        .attr('y2', y(396))
        .attr('stroke', '#dc3545')
        .attr('stroke-width', 1)
        .attr('stroke-dasharray', '5,5');

    // Add title
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#343a40')
        .text('Resonance Frequency (Hz)');
}

function updateEnergyChart() {
    // Get container
    const container = document.getElementById('energy-chart');

    // Clear container
    container.innerHTML = '';

    // Check if we have data
    if (!dashboardState.lastResult || !dashboardState.lastResult.energies) {
        return;
    }

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 50, left: 50 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Prepare data
    const energies = dashboardState.lastResult.energies;
    const data = [
        { domain: 'CSDE', energy: energies.csde },
        { domain: 'CSFE', energy: energies.csfe },
        { domain: 'CSME', energy: energies.csme }
    ];

    // Set scales
    const x = d3.scaleBand()
        .domain(data.map(d => d.domain))
        .range([0, width])
        .padding(0.3);

    const y = d3.scaleLinear()
        .domain([0, d3.max(data, d => d.energy) * 1.2])
        .range([height, 0]);

    // Add X axis
    svg.append('g')
        .attr('class', 'axis')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(x));

    // Add Y axis
    svg.append('g')
        .attr('class', 'axis')
        .call(d3.axisLeft(y));

    // Add bars
    svg.selectAll('.bar')
        .data(data)
        .enter()
        .append('rect')
        .attr('class', d => `bar bar-${d.domain.toLowerCase()}`)
        .attr('x', d => x(d.domain))
        .attr('width', x.bandwidth())
        .attr('y', d => y(d.energy))
        .attr('height', d => height - y(d.energy));

    // Add labels
    svg.selectAll('.label')
        .data(data)
        .enter()
        .append('text')
        .attr('class', 'label')
        .attr('x', d => x(d.domain) + x.bandwidth() / 2)
        .attr('y', d => y(d.energy) - 5)
        .attr('text-anchor', 'middle')
        .text(d => d.energy.toFixed(2));

    // Add title
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#343a40')
        .text('Domain Energies');

    // Add X axis label
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', height + margin.bottom - 10)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#6c757d')
        .text('Domain');

    // Add Y axis label
    svg.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('x', -height / 2)
        .attr('y', -margin.left + 15)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#6c757d')
        .text('Energy');
}

function updateWeightChart() {
    // Get container
    const container = document.getElementById('weight-chart');

    // Clear container
    container.innerHTML = '';

    // Check if we have data
    if (!dashboardState.lastResult || !dashboardState.lastResult.weights) {
        return;
    }

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 50, left: 50 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Prepare data
    const weights = dashboardState.lastResult.weights;
    const data = [
        { domain: 'CSDE', weight: weights.csde },
        { domain: 'CSFE', weight: weights.csfe },
        { domain: 'CSME', weight: weights.csme }
    ];

    // Set scales
    const x = d3.scaleBand()
        .domain(data.map(d => d.domain))
        .range([0, width])
        .padding(0.3);

    const y = d3.scaleLinear()
        .domain([0, 1])
        .range([height, 0]);

    // Add X axis
    svg.append('g')
        .attr('class', 'axis')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(x));

    // Add Y axis
    svg.append('g')
        .attr('class', 'axis')
        .call(d3.axisLeft(y).ticks(5).tickFormat(d3.format('.0%')));

    // Add bars
    svg.selectAll('.bar')
        .data(data)
        .enter()
        .append('rect')
        .attr('class', d => `bar bar-${d.domain.toLowerCase()}`)
        .attr('x', d => x(d.domain))
        .attr('width', x.bandwidth())
        .attr('y', d => y(d.weight))
        .attr('height', d => height - y(d.weight));

    // Add labels
    svg.selectAll('.label')
        .data(data)
        .enter()
        .append('text')
        .attr('class', 'label')
        .attr('x', d => x(d.domain) + x.bandwidth() / 2)
        .attr('y', d => y(d.weight) - 5)
        .attr('text-anchor', 'middle')
        .text(d => d3.format('.0%')(d.weight));

    // Add 18/82 threshold line
    svg.append('line')
        .attr('x1', 0)
        .attr('y1', y(0.18))
        .attr('x2', width)
        .attr('y2', y(0.18))
        .attr('stroke', '#dc3545')
        .attr('stroke-width', 1)
        .attr('stroke-dasharray', '5,5');

    // Add 18/82 threshold label
    svg.append('text')
        .attr('x', width - 5)
        .attr('y', y(0.18) - 5)
        .attr('text-anchor', 'end')
        .style('font-size', '10px')
        .style('fill', '#dc3545')
        .text('18% Threshold');

    // Add title
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#343a40')
        .text('Domain Weights');

    // Add X axis label
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', height + margin.bottom - 10)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#6c757d')
        .text('Domain');

    // Add Y axis label
    svg.append('text')
        .attr('transform', 'rotate(-90)')
        .attr('x', -height / 2)
        .attr('y', -margin.left + 15)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#6c757d')
        .text('Weight');
}

function updateResonanceRadar() {
    // Get container
    const container = document.getElementById('resonance-radar');

    // Clear container
    container.innerHTML = '';

    // Check if we have data
    if (!dashboardState.lastResult || !dashboardState.lastResonance) {
        return;
    }

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 30 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;
    const radius = Math.min(width, height) / 2;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${width / 2 + margin.left},${height / 2 + margin.top})`);

    // Prepare data
    const features = [
        { name: 'CSDE', value: dashboardState.lastResult.weights.csde },
        { name: 'CSFE', value: dashboardState.lastResult.weights.csfe },
        { name: 'CSME', value: dashboardState.lastResult.weights.csme },
        { name: 'Resonance', value: 1 - (dashboardState.lastResonance.deviation / 100) },
        { name: 'Phase', value: dashboardState.lastResonance.phaseAlignment || 0.5 },
        { name: 'Quantum', value: 1 - (dashboardState.lastResonance.quantumVacuumNoise || 0.5) }
    ];

    // Number of axes
    const numAxes = features.length;

    // Angle between axes
    const angleSlice = Math.PI * 2 / numAxes;

    // Scale for the radius
    const rScale = d3.scaleLinear()
        .domain([0, 1])
        .range([0, radius]);

    // Create the straight lines radiating outward from the center
    const axis = svg.selectAll('.axis')
        .data(features)
        .enter()
        .append('g')
        .attr('class', 'axis');

    // Append the lines
    axis.append('line')
        .attr('x1', 0)
        .attr('y1', 0)
        .attr('x2', (d, i) => rScale(1) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('y2', (d, i) => rScale(1) * Math.sin(angleSlice * i - Math.PI / 2))
        .attr('class', 'radar-axis')
        .style('stroke', '#e9ecef')
        .style('stroke-width', '1px');

    // Append the labels at each axis
    axis.append('text')
        .attr('class', 'legend')
        .attr('text-anchor', 'middle')
        .attr('dy', '0.35em')
        .attr('x', (d, i) => rScale(1.1) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('y', (d, i) => rScale(1.1) * Math.sin(angleSlice * i - Math.PI / 2))
        .text(d => d.name);

    // Create the circular segments
    for (let j = 0; j < 5; j++) {
        const levelFactor = radius * ((j + 1) / 5);

        // Create the circles
        svg.selectAll('.radar-circle-' + j)
            .data([1])
            .enter()
            .append('circle')
            .attr('class', 'radar-circle')
            .attr('r', levelFactor)
            .style('fill', 'none')
            .style('stroke', '#e9ecef')
            .style('stroke-width', '1px');

        // Add level labels
        if (j === 0) {
            svg.append('text')
                .attr('x', 5)
                .attr('y', -levelFactor + 5)
                .attr('class', 'radar-level')
                .style('font-size', '10px')
                .style('fill', '#6c757d')
                .text((j + 1) * 0.2);
        }
    }

    // Create the radar chart areas
    const radarLine = d3.lineRadial()
        .radius(d => rScale(d.value))
        .angle((d, i) => i * angleSlice)
        .curve(d3.curveLinearClosed);

    // Create the radar area
    svg.append('path')
        .datum(features)
        .attr('class', 'radar-area')
        .attr('d', radarLine)
        .style('fill', '#007bff')
        .style('fill-opacity', 0.5)
        .style('stroke', '#007bff')
        .style('stroke-width', '2px');

    // Create the radar points
    svg.selectAll('.radar-point')
        .data(features)
        .enter()
        .append('circle')
        .attr('class', 'radar-point')
        .attr('r', 5)
        .attr('cx', (d, i) => rScale(d.value) * Math.cos(angleSlice * i - Math.PI / 2))
        .attr('cy', (d, i) => rScale(d.value) * Math.sin(angleSlice * i - Math.PI / 2))
        .style('fill', '#007bff')
        .style('stroke', '#fff')
        .style('stroke-width', '2px');

    // Add title
    svg.append('text')
        .attr('x', 0)
        .attr('y', -radius - 10)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .style('fill', '#343a40')
        .text('Cross-Domain Resonance');
}

function updateTensorVisualizations() {
    // Update CSDE tensor visualization
    updateTensorVisualization('csde-tensor', 'CSDE', [
        dashboardState.lastResult.fusedTensor.tensorA.governance || 0,
        dashboardState.lastResult.fusedTensor.tensorA.data || 0,
        dashboardState.lastResult.fusedTensor.tensorA.action || 0,
        dashboardState.lastResult.fusedTensor.tensorA.confidence || 0
    ]);

    // Update CSFE tensor visualization
    updateTensorVisualization('csfe-tensor', 'CSFE', [
        dashboardState.lastResult.fusedTensor.tensorB.risk || 0,
        dashboardState.lastResult.fusedTensor.tensorB.finance || 0,
        dashboardState.lastResult.fusedTensor.tensorB.action || 0,
        dashboardState.lastResult.fusedTensor.tensorB.confidence || 0
    ]);

    // Update CSME tensor visualization
    updateTensorVisualization('csme-tensor', 'CSME', [
        dashboardState.lastResult.fusedTensor.tensorC.bio || 0,
        dashboardState.lastResult.fusedTensor.tensorC.medCompliance || 0,
        dashboardState.lastResult.fusedTensor.tensorC.action || 0,
        dashboardState.lastResult.fusedTensor.tensorC.confidence || 0
    ]);

    // Update fused tensor visualization
    updateFusedTensorVisualization('fused-tensor', dashboardState.lastResult.fusedTensor.values || []);
}

/**
 * Update tensor visualization
 * @param {string} containerId - Container ID
 * @param {string} domain - Domain name
 * @param {Array} values - Tensor values
 */
function updateTensorVisualization(containerId, domain, values) {
    // Get container
    const container = document.getElementById(containerId);

    // Clear container
    container.innerHTML = '';

    // Set dimensions
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Set scales
    const x = d3.scaleBand()
        .domain([0, 1, 2, 3])
        .range([0, width])
        .padding(0.1);

    const y = d3.scaleLinear()
        .domain([0, 1])
        .range([height, 0]);

    // Add bars
    svg.selectAll('.bar')
        .data(values)
        .enter()
        .append('rect')
        .attr('class', `tensor-cell tensor-cell-${domain.toLowerCase()}`)
        .attr('x', (d, i) => x(i))
        .attr('width', x.bandwidth())
        .attr('y', d => y(d))
        .attr('height', d => height - y(d))
        .attr('opacity', d => 0.3 + d * 0.7);

    // Add labels
    svg.selectAll('.label')
        .data(values)
        .enter()
        .append('text')
        .attr('class', 'label')
        .attr('x', (d, i) => x(i) + x.bandwidth() / 2)
        .attr('y', d => y(d) - 5)
        .attr('text-anchor', 'middle')
        .text(d => d.toFixed(2));

    // Add X axis labels
    const labels = domain === 'CSDE' ? ['G', 'D', 'A', 'C'] :
                  domain === 'CSFE' ? ['R', 'F', 'A', 'C'] :
                  ['B', 'M', 'A', 'C'];

    svg.selectAll('.x-label')
        .data(labels)
        .enter()
        .append('text')
        .attr('class', 'x-label')
        .attr('x', (d, i) => x(i) + x.bandwidth() / 2)
        .attr('y', height + 15)
        .attr('text-anchor', 'middle')
        .style('font-size', '10px')
        .style('fill', '#6c757d')
        .text(d => d);

    // Add title
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#343a40')
        .text(`${domain} Tensor`);
}

/**
 * Update fused tensor visualization
 * @param {string} containerId - Container ID
 * @param {Array} values - Tensor values
 */
function updateFusedTensorVisualization(containerId, values) {
    // Get container
    const container = document.getElementById(containerId);

    // Clear container
    container.innerHTML = '';

    // Check if we have data
    if (!values || values.length === 0) {
        return;
    }

    // Set dimensions
    const margin = { top: 20, right: 20, bottom: 30, left: 40 };
    const width = container.clientWidth - margin.left - margin.right;
    const height = container.clientHeight - margin.top - margin.bottom;

    // Create SVG
    const svg = d3.select(container)
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom)
        .append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);

    // Limit the number of values to display
    const displayValues = values.slice(0, 20);

    // Set scales
    const x = d3.scaleBand()
        .domain(displayValues.map((d, i) => i))
        .range([0, width])
        .padding(0.1);

    const y = d3.scaleLinear()
        .domain([
            d3.min(displayValues) * 0.9,
            d3.max(displayValues) * 1.1
        ])
        .range([height, 0]);

    // Add bars
    svg.selectAll('.bar')
        .data(displayValues)
        .enter()
        .append('rect')
        .attr('class', 'tensor-cell tensor-cell-fused')
        .attr('x', (d, i) => x(i))
        .attr('width', x.bandwidth())
        .attr('y', d => y(d))
        .attr('height', d => height - y(d))
        .attr('opacity', (d, i) => 0.3 + (i / displayValues.length) * 0.7);

    // Add X axis
    svg.append('g')
        .attr('class', 'axis')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(x).tickValues([]));

    // Add Y axis
    svg.append('g')
        .attr('class', 'axis')
        .call(d3.axisLeft(y).ticks(5));

    // Add title
    svg.append('text')
        .attr('x', width / 2)
        .attr('y', 0)
        .attr('text-anchor', 'middle')
        .style('font-size', '12px')
        .style('fill', '#343a40')
        .text(`Fused Tensor (${values.length} values, showing first ${displayValues.length})`);
}
