import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

// Define the diagram data
const elements = [
  {
    id: 'incident-response',
    top: 50,
    left: 350,
    width: 300,
    text: 'Cyber-Safety Incident Response Workflow',
    number: '1',
    bold: true,
    fontSize: '20px',
    backgroundColor: '#e6f7ff'
  },
  // Detection Phase
  {
    id: 'detection-phase',
    top: 120,
    left: 350,
    width: 300,
    text: 'Detection Phase',
    number: '2',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'continuous-monitoring',
    top: 180,
    left: 150,
    width: 200,
    text: 'Continuous Monitoring (NovaShield)',
    number: '3',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'resonance-index',
    top: 240,
    left: 50,
    width: 150,
    text: 'Resonance Index (φindex)\nIdentifies anomalies',
    number: '4',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'signal-noise',
    top: 240,
    left: 225,
    width: 150,
    text: 'Signal-to-Noise Ratio\nφ-harmonic sensing',
    number: '5',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'trinity-equation',
    top: 240,
    left: 400,
    width: 150,
    text: 'Trinity Equation\nAssesses G, D, R aspects',
    number: '6',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  // Automated Triage
  {
    id: 'automated-triage',
    top: 180,
    left: 600,
    width: 200,
    text: 'Automated Triage (NovaCore)',
    number: '7',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'uuft-analysis',
    top: 240,
    left: 500,
    width: 150,
    text: 'UUFT Equation\nAnalyzes characteristics',
    number: '8',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'data-purity',
    top: 240,
    left: 675,
    width: 150,
    text: 'Data Purity Score\nEvaluates reliability',
    number: '9',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'system-health',
    top: 240,
    left: 850,
    width: 150,
    text: 'System Health Score\nAssesses impact',
    number: '10',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  // Alert Generation
  {
    id: 'alert-generation',
    top: 300,
    left: 350,
    width: 300,
    text: 'Alert Generation (NovaShield)',
    number: '11',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'trust-equation',
    top: 360,
    left: 200,
    width: 150,
    text: 'Trust Equation\nEvaluates credibility',
    number: '12',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: 'adaptive-coherence',
    top: 360,
    left: 400,
    width: 150,
    text: 'Adaptive Coherence\nDetermines urgency',
    number: '13',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  {
    id: '18-82-principle',
    top: 360,
    left: 600,
    width: 150,
    text: '18/82 Principle\nPrioritizes high-impact',
    number: '14',
    fontSize: '12px',
    backgroundColor: '#fffbe6'
  },
  // Analysis Phase
  {
    id: 'analysis-phase',
    top: 420,
    left: 350,
    width: 300,
    text: 'Analysis Phase',
    number: '15',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'pattern-recognition',
    top: 480,
    left: 200,
    width: 200,
    text: 'Pattern Recognition (NovaThink)',
    number: '16',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'impact-assessment',
    top: 480,
    left: 500,
    width: 200,
    text: 'Impact Assessment (NovaTrack)',
    number: '17',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Containment Phase
  {
    id: 'containment-phase',
    top: 540,
    left: 350,
    width: 300,
    text: 'Containment Phase',
    number: '18',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'automated-response',
    top: 600,
    left: 200,
    width: 200,
    text: 'Automated Response (NovaFlow)',
    number: '19',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'isolation-actions',
    top: 600,
    left: 500,
    width: 200,
    text: 'Isolation Actions (NovaConnect)',
    number: '20',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  // Recovery Phase
  {
    id: 'recovery-phase',
    top: 660,
    left: 350,
    width: 300,
    text: 'Recovery Phase',
    number: '21',
    bold: true,
    fontSize: '16px',
    backgroundColor: '#fff0f6'
  },
  {
    id: 'system-restoration',
    top: 720,
    left: 200,
    width: 200,
    text: 'System Restoration',
    number: '22',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  },
  {
    id: 'adaptive-learning',
    top: 720,
    left: 500,
    width: 200,
    text: 'Adaptive Learning (NovaLearn)',
    number: '23',
    fontSize: '14px',
    backgroundColor: '#f6ffed'
  }
];

const connections = [
  // Connect Incident Response to Detection Phase
  {
    start: { x: 500, y: 100 },
    end: { x: 500, y: 120 },
    type: 'arrow'
  },
  // Connect Detection Phase to components
  {
    start: { x: 350, y: 170 },
    end: { x: 250, y: 180 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 170 },
    end: { x: 700, y: 180 },
    type: 'arrow'
  },
  // Connect Continuous Monitoring to subcomponents
  {
    start: { x: 175, y: 210 },
    end: { x: 125, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 250, y: 210 },
    end: { x: 300, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 350, y: 210 },
    end: { x: 475, y: 240 },
    type: 'arrow'
  },
  // Connect Automated Triage to subcomponents
  {
    start: { x: 550, y: 210 },
    end: { x: 575, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 650, y: 210 },
    end: { x: 750, y: 240 },
    type: 'arrow'
  },
  {
    start: { x: 750, y: 210 },
    end: { x: 925, y: 240 },
    type: 'arrow'
  },
  // Connect to Alert Generation
  {
    start: { x: 250, y: 270 },
    end: { x: 350, y: 300 },
    type: 'arrow'
  },
  {
    start: { x: 700, y: 270 },
    end: { x: 500, y: 300 },
    type: 'arrow'
  },
  // Connect Alert Generation to subcomponents
  {
    start: { x: 300, y: 330 },
    end: { x: 275, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 450, y: 330 },
    end: { x: 475, y: 360 },
    type: 'arrow'
  },
  {
    start: { x: 600, y: 330 },
    end: { x: 675, y: 360 },
    type: 'arrow'
  },
  // Connect to Analysis Phase
  {
    start: { x: 500, y: 390 },
    end: { x: 500, y: 420 },
    type: 'arrow'
  },
  // Connect Analysis Phase to components
  {
    start: { x: 350, y: 470 },
    end: { x: 300, y: 480 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 470 },
    end: { x: 600, y: 480 },
    type: 'arrow'
  },
  // Connect to Containment Phase
  {
    start: { x: 500, y: 510 },
    end: { x: 500, y: 540 },
    type: 'arrow'
  },
  // Connect Containment Phase to components
  {
    start: { x: 350, y: 590 },
    end: { x: 300, y: 600 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 590 },
    end: { x: 600, y: 600 },
    type: 'arrow'
  },
  // Connect to Recovery Phase
  {
    start: { x: 500, y: 630 },
    end: { x: 500, y: 660 },
    type: 'arrow'
  },
  // Connect Recovery Phase to components
  {
    start: { x: 350, y: 710 },
    end: { x: 300, y: 720 },
    type: 'arrow'
  },
  {
    start: { x: 550, y: 710 },
    end: { x: 600, y: 720 },
    type: 'arrow'
  }
];

const IncidentResponse: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="1000px" 
      height="770px" 
    />
  );
};

export default IncidentResponse;
