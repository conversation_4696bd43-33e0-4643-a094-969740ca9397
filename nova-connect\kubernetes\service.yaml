apiVersion: v1
kind: Service
metadata:
  name: novafuse-uac
  namespace: novafuse
  labels:
    app: novafuse-uac
    component: api
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/app-protocols: '{"http": "HTTP"}'
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 3001
    protocol: TCP
    name: http
  selector:
    app: novafuse-uac
    component: api
