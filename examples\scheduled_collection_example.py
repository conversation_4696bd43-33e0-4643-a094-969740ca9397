"""
Example of using scheduled collection with the Collector Manager.

This example demonstrates how to set up and use scheduled collection
with the enhanced Collector Manager.
"""

import os
import sys
import json
import time
import datetime
import logging

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.collector_manager import CollectorManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the scheduled collection example."""
    # Create a collector manager
    collector_manager = CollectorManager()
    
    # Create a collection schedule for Jira issues
    schedule_id = "jira_issues_hourly"
    schedule_name = "Jira Issues Hourly"
    schedule_description = "Collect Jira issues hourly"
    frequency = collector_manager.SCHEDULE_FREQUENCIES['CUSTOM']  # Use custom frequency for demo
    collector_id = "jira"
    parameters = {
        "url": "https://your-jira-instance.atlassian.net",
        "username": "your-username",
        "api_token": "YOUR_JIRA_API_TOKEN",  # Replace with your Jira API token
        "project_key": "PROJ",
        "issue_type": "Bug"
    }
    
    try:
        # Create the schedule with a custom interval of 60 seconds for demo purposes
        schedule = collector_manager.create_schedule(
            schedule_id=schedule_id,
            name=schedule_name,
            description=schedule_description,
            frequency=frequency,
            collector_id=collector_id,
            parameters=parameters,
            strategy=collector_manager.COLLECTION_STRATEGIES['INCREMENTAL'],
            enabled=True
        )
        
        # Set a custom interval of 60 seconds for demo purposes
        schedule['interval_seconds'] = 60
        collector_manager._save_schedule(schedule_id)
        
        logger.info(f"Created schedule: {json.dumps(schedule, indent=2)}")
        
        # Wait for a few collection cycles
        logger.info("Waiting for collection cycles to run (press Ctrl+C to stop)...")
        try:
            # Run for 5 minutes
            end_time = time.time() + 300  # 5 minutes
            while time.time() < end_time:
                time.sleep(10)
                
                # Get the collection history
                if schedule_id in collector_manager.collection_history:
                    history = collector_manager.collection_history[schedule_id]
                    logger.info(f"Collection history for {schedule_id}: {len(history)} entries")
                    
                    # Print the latest entry if available
                    if history:
                        latest = history[-1]
                        logger.info(f"Latest collection: {latest['timestamp']}")
        
        except KeyboardInterrupt:
            logger.info("Interrupted by user")
        
        # Get the schedule
        schedule = collector_manager.get_schedule(schedule_id)
        logger.info(f"Schedule: {json.dumps(schedule, indent=2)}")
        
        # Disable the schedule
        updated_schedule = collector_manager.update_schedule(
            schedule_id=schedule_id,
            enabled=False
        )
        
        logger.info(f"Disabled schedule: {json.dumps(updated_schedule, indent=2)}")
        
        # Delete the schedule
        collector_manager.delete_schedule(schedule_id)
        logger.info(f"Deleted schedule: {schedule_id}")
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
