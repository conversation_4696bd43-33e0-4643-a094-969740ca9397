#!/usr/bin/env python3
"""
NovaPi AI Performance Optimization Test
Real performance benchmarking with π-coherence timing
"""

import sys
import os
import time
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ai_performance():
    print("🚀 Testing NovaPi AI Performance Optimization")
    print("=" * 60)
    
    try:
        from novasentientx.nova_pi import NovaPi, TimingMode
        
        # Initialize NovaPi
        print("🔧 Initializing NovaPi AI Performance Engine...")
        pi_engine = NovaPi()
        
        print("\n🧭 π-Coherence Intervals for AI Operations:")
        for mode in TimingMode:
            interval = pi_engine.get_pi_interval(mode)
            print(f"   {mode.value}: {interval}ms")
        
        print("\n🔬 Testing GPU Batch Optimization...")
        def gpu_operation(data):
            # Simulate GPU computation
            return [x * 3.14159 + x**2 for x in data]
        
        gpu_result = pi_engine.optimize_gpu_batch(1000, gpu_operation)
        print(f"   Optimized Batch Size: {gpu_result['optimized_batch_size']}")
        print(f"   Throughput: {gpu_result['throughput_ops_per_sec']:.2f} ops/sec")
        print(f"   Total Time: {gpu_result['total_time_sec']:.3f} seconds")
        
        print("\n💾 Testing Memory Allocation Optimization...")
        def memory_operation(size_mb):
            # Simulate memory allocation
            return f"Allocated {size_mb:.2f}MB"
        
        memory_result = pi_engine.optimize_memory_allocation(100.0, memory_operation)
        print(f"   Optimal Chunk Size: {memory_result['optimal_chunk_size_mb']:.2f}MB")
        print(f"   Memory Efficiency: {memory_result['memory_efficiency']:.2%}")
        print(f"   Memory Throughput: {memory_result['memory_throughput_mb_per_sec']:.2f} MB/sec")
        
        print("\n📊 Running Individual AI Operation Benchmarks...")
        
        # Test different AI operations
        def neural_inference(data): return [1 / (1 + 2.718**(-x)) for x in data]  # Sigmoid
        def matrix_multiply(data): return [x * 2 + sum(data[:10]) for x in data]
        def token_generation(data): return [str(x) + "_token" for x in data]
        
        # Benchmark different operations
        inference_bench = pi_engine.benchmark_ai_operation(neural_inference, 500, TimingMode.INFERENCE)
        gpu_bench = pi_engine.benchmark_ai_operation(matrix_multiply, 1000, TimingMode.GPU_BATCH)
        token_bench = pi_engine.benchmark_ai_operation(token_generation, 300, TimingMode.TOKEN_GEN)
        
        print("\n🏆 Running Comprehensive Performance Benchmark...")
        comprehensive_results = pi_engine.run_comprehensive_benchmark()
        
        print("\n📈 FINAL PERFORMANCE SUMMARY:")
        summary = pi_engine.get_performance_summary()
        
        print(f"   Total Benchmarks: {summary['total_benchmarks']}")
        print(f"   Average Performance Improvement: {summary['performance_improvements']['average_multiplier']:.2f}x")
        print(f"   Maximum Performance Improvement: {summary['performance_improvements']['maximum_multiplier']:.2f}x")
        print(f"   Optimization Success Rate: {summary['optimization_success_rate_percent']:.1f}%")
        print(f"   Best Performing Mode: {summary['best_performing_operation']['mode']}")
        print(f"   π-Optimization Factor: {summary['performance_improvements']['pi_optimization_factor']}")
        
        # Check if targets were met
        targets = summary['targets']
        print(f"\n🎯 TARGET ACHIEVEMENT:")
        print(f"   Throughput Target (2.0x): {'✅ ACHIEVED' if targets['throughput_achieved'] else '❌ NOT MET'}")
        print(f"   Memory Efficiency Target: {'✅ ACHIEVED' if targets['memory_target_achieved'] else '❌ NOT MET'}")
        
        print(f"\n✅ AI Performance Optimization Test COMPLETE!")
        print(f"🧭 π-Coherence Status: {summary['pi_coherence_status']}")
        print(f"🚀 AI Optimization: {'ENABLED' if summary['ai_optimization_enabled'] else 'DISABLED'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ai_performance()
    sys.exit(0 if success else 1)
