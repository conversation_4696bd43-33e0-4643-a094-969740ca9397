/**
 * Comphyological Governance Test
 * 
 * This script tests the Comphyological Governance system, which implements
 * the First Law of Comphyological Governance:
 * 
 * "A system shall neither externalize non-resonant states (Harmonic Enforcement) 
 * nor propagate unmeasured energy transitions (Ψₑ Dynamics)."
 * 
 * It demonstrates the dual-axis operational framework:
 * 1. Harmonic Enforcement (3-6-9-12-13) - What states are allowed
 * 2. Ψₑ Dynamics (Velocity + Acceleration) - How states evolve
 */

const fs = require('fs');
const path = require('path');
const { ComphyologicalGovernance } = require('../../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Comphyological Governance
 */
function testComphyologicalGovernance() {
  console.log('=== Testing Comphyological Governance ===');
  
  // Create Comphyological Governance system
  const governance = new ComphyologicalGovernance({
    // Harmonic Enforcement options
    resonanceLock: true,
    strictMode: false,
    logValidation: true,
    
    // Ψₑ Dynamics options
    slopeMonitoring: true,
    slopeThreshold: 0.13,
    criticalSlopeThreshold: 0.3,
    velocityThreshold: 0.6,
    accelerationThreshold: 0.09,
    samplingRate: 0, // manual sampling
    historyLength: 10,
    resonancePatternEnforcement: true,
    autoModeSwitch: true,
    
    // Governance options
    autoHarmonization: true,
    preemptiveHarmonization: true,
    crossDomainEnforcement: true
  });
  
  // Register event listeners
  governance.on('validation', (data) => {
    console.log(`Validation: ${data.value} -> ${data.result.isResonant ? 'Resonant' : 'Non-resonant'}${data.result.wasHarmonized ? ` (Harmonized to ${data.result.harmonizedValue})` : ''}`);
  });
  
  governance.on('slope-warning', (warning) => {
    console.log(`Slope Warning: ${warning.message}`);
  });
  
  governance.on('mode-change', (data) => {
    console.log(`Mode Change: ${data.previousMode} -> ${data.newMode}`);
  });
  
  governance.on('preemptive-harmonization', (data) => {
    console.log(`Preemptive Harmonization: ${data.warning.message}`);
  });
  
  // Test with different values
  console.log('\nTesting with different values:');
  
  const testValues = [
    0.3, 0.6, 0.9,  // Resonant thresholds
    0.03, 0.06, 0.09, 0.12, 0.13,  // Resonant decay rates
    3, 6, 9, 12,  // Resonant cycles
    0.7, 0.8, 0.4, 0.07  // Non-resonant values
  ];
  
  // Validate values
  const results = testValues.map(value => {
    return governance.validate(value);
  });
  
  // Calculate statistics
  const resonantCount = results.filter(r => r.isResonant).length;
  const harmonizedCount = results.filter(r => r.wasHarmonized).length;
  
  console.log(`\nResonant values: ${resonantCount}/${results.length} (${(resonantCount/results.length*100).toFixed(2)}%)`);
  console.log(`Harmonized values: ${harmonizedCount}/${results.length} (${(harmonizedCount/results.length*100).toFixed(2)}%)`);
  
  // Test with increasing values to trigger slope warnings
  console.log('\nTesting with increasing values to trigger slope warnings:');
  
  const increasingValues = [
    0.3, 0.35, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7, 0.75,
    0.8, 0.85, 0.9, 0.95, 1.0, 1.05, 1.1, 1.15, 1.2, 1.25
  ];
  
  // Validate increasing values
  const increasingResults = increasingValues.map(value => {
    return governance.validate(value);
  });
  
  // Test with decreasing values to trigger negative slope warnings
  console.log('\nTesting with decreasing values to trigger negative slope warnings:');
  
  const decreasingValues = [
    1.2, 1.15, 1.1, 1.05, 1.0, 0.95, 0.9, 0.85, 0.8, 0.75,
    0.7, 0.65, 0.6, 0.55, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25
  ];
  
  // Validate decreasing values
  const decreasingResults = decreasingValues.map(value => {
    return governance.validate(value);
  });
  
  // Test with oscillating values to trigger acceleration warnings
  console.log('\nTesting with oscillating values to trigger acceleration warnings:');
  
  const oscillatingValues = [
    0.3, 0.6, 0.3, 0.6, 0.3, 0.6, 0.3, 0.6, 0.3, 0.6,
    0.3, 0.9, 0.3, 0.9, 0.3, 0.9, 0.3, 0.9, 0.3, 0.9
  ];
  
  // Validate oscillating values
  const oscillatingResults = oscillatingValues.map(value => {
    return governance.validate(value);
  });
  
  // Test with different modes
  console.log('\nTesting with different modes:');
  
  // Standard mode
  console.log('\nStandard mode:');
  governance.switchMode('Standard');
  const standardResult = governance.validate(0.7);
  console.log(`Value 0.7 in Standard mode: ${standardResult.isResonant ? 'Resonant' : 'Non-resonant'}${standardResult.wasHarmonized ? ` (Harmonized to ${standardResult.harmonizedValue})` : ''}`);
  
  // Strict Enforcement mode
  console.log('\nStrict Enforcement mode:');
  governance.switchMode('Strict Enforcement');
  try {
    const strictResult = governance.validate(0.7);
    console.log(`Value 0.7 in Strict Enforcement mode: ${strictResult.isResonant ? 'Resonant' : 'Non-resonant'}${strictResult.wasHarmonized ? ` (Harmonized to ${strictResult.harmonizedValue})` : ''}`);
  } catch (error) {
    console.log(`Error in Strict Enforcement mode: ${error.message}`);
  }
  
  // Harmonizing Filter mode
  console.log('\nHarmonizing Filter mode:');
  governance.switchMode('Harmonizing Filter');
  const harmonizingResult = governance.validate(0.7);
  console.log(`Value 0.7 in Harmonizing Filter mode: ${harmonizingResult.isResonant ? 'Resonant' : 'Non-resonant'}${harmonizingResult.wasHarmonized ? ` (Harmonized to ${harmonizingResult.harmonizedValue})` : ''}`);
  
  // Velocity Control mode
  console.log('\nVelocity Control mode:');
  governance.switchMode('Velocity Control');
  const velocityResult = governance.validate(0.7);
  console.log(`Value 0.7 in Velocity Control mode: ${velocityResult.isResonant ? 'Resonant' : 'Non-resonant'}${velocityResult.wasHarmonized ? ` (Harmonized to ${velocityResult.harmonizedValue})` : ''}`);
  
  // Acceleration Control mode
  console.log('\nAcceleration Control mode:');
  governance.switchMode('Acceleration Control');
  const accelerationResult = governance.validate(0.7);
  console.log(`Value 0.7 in Acceleration Control mode: ${accelerationResult.isResonant ? 'Resonant' : 'Non-resonant'}${accelerationResult.wasHarmonized ? ` (Harmonized to ${accelerationResult.harmonizedValue})` : ''}`);
  
  // Get metrics
  console.log('\nGovernance Metrics:');
  const metrics = governance.getMetrics();
  console.log(JSON.stringify(metrics, null, 2));
  
  return {
    governance,
    testValues,
    results,
    increasingValues,
    increasingResults,
    decreasingValues,
    decreasingResults,
    oscillatingValues,
    oscillatingResults,
    standardResult,
    harmonizingResult,
    velocityResult,
    accelerationResult,
    metrics
  };
}

/**
 * Generate HTML report
 */
function generateHtmlReport(results) {
  console.log('\n=== Generating HTML Report ===');
  
  const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyological Governance Demo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
      flex: 1;
      min-width: 300px;
    }
    .governance-info {
      background-color: #f0f8ff;
      border-radius: 5px;
      padding: 20px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      margin-bottom: 20px;
      border-left: 5px solid #0066cc;
    }
    .chart {
      width: 100%;
      height: 300px;
      margin-top: 20px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .resonant {
      color: #009900;
      font-weight: bold;
    }
    .non-resonant {
      color: #cc0000;
    }
    .harmonized {
      color: #0066cc;
      font-style: italic;
    }
    footer {
      margin-top: 40px;
      text-align: center;
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>Comphyological Governance Demo</h1>
  <p>Generated: ${new Date().toLocaleString()}</p>
  
  <div class="governance-info">
    <h2>The First Law of Comphyological Governance</h2>
    <p><em>"A system shall neither externalize non-resonant states (Harmonic Enforcement) nor propagate unmeasured energy transitions (Ψₑ Dynamics)."</em></p>
    <p>This demo demonstrates the dual-axis operational framework:</p>
    <ol>
      <li><strong>Harmonic Enforcement (3-6-9-12-13)</strong> - What states are allowed</li>
      <li><strong>Ψₑ Dynamics (Velocity + Acceleration)</strong> - How states evolve</li>
    </ol>
  </div>
  
  <h2>Governance Metrics</h2>
  
  <div class="card">
    <h3>Overall Metrics</h3>
    <p>Total Operations: ${results.metrics.totalOperations}</p>
    <p>Validations: ${results.metrics.validations}</p>
    <p>Harmonizations: ${results.metrics.harmonizations}</p>
    <p>Rejections: ${results.metrics.rejections}</p>
    <p>Preemptive Harmonizations: ${results.metrics.preemptiveHarmonizations}</p>
    <p>Slope Warnings: ${results.metrics.slopeWarnings}</p>
    <p>Critical Slope Warnings: ${results.metrics.criticalSlopeWarnings}</p>
    <p>Mode Changes: ${results.metrics.modeChanges}</p>
  </div>
  
  <h2>Slope Metrics</h2>
  
  <div class="card">
    <h3>Ψₑ Dynamics</h3>
    <p>Current PsiE: ${results.metrics.slopeMetrics.currentPsiE}</p>
    <p>Current Velocity: ${results.metrics.slopeMetrics.currentVelocity}</p>
    <p>Current Acceleration: ${results.metrics.slopeMetrics.currentAcceleration}</p>
    <p>Current Slope: ${results.metrics.slopeMetrics.currentSlope}</p>
    <p>Current Mode: ${results.metrics.slopeMetrics.currentMode}</p>
    <p>Average Slope: ${results.metrics.slopeMetrics.averageSlope}</p>
    <p>Max Slope: ${results.metrics.slopeMetrics.maxSlope}</p>
    <p>Min Slope: ${results.metrics.slopeMetrics.minSlope}</p>
    <p>Resonant Slope Percentage: ${results.metrics.slopeMetrics.resonantSlopePercentage}%</p>
  </div>
  
  <footer>
    <p>NovaFuse Comphyological Governance Demo - Copyright © ${new Date().getFullYear()}</p>
    <p><em>"The universe counts in 3s. Now, so do we."</em></p>
  </footer>
</body>
</html>`;
  
  // Save HTML report
  const reportPath = path.join(RESULTS_DIR, 'comphyological_governance_demo_report.html');
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`HTML report saved to ${reportPath}`);
  
  return {
    htmlContent,
    reportPath
  };
}

/**
 * Main function
 */
function main() {
  console.log('=== Comphyological Governance Demo ===');
  
  // Run test
  const results = testComphyologicalGovernance();
  
  // Generate HTML report
  const reportResults = generateHtmlReport(results);
  
  // Save results to JSON file
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'comphyological_governance_demo_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nResults saved to ${path.join(RESULTS_DIR, 'comphyological_governance_demo_results.json')}`);
  console.log(`HTML report saved to ${reportResults.reportPath}`);
  console.log('\nOpen the HTML report to view the results in a browser.');
}

// Run main function
main();
