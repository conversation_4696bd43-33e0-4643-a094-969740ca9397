@echo off
REM Simple NovaActuary™ Container Run Script

echo.
echo 🚀 Running NovaActuary™ Container
echo The ∂Ψ=0 Underwriting Revolution
echo ================================
echo.

REM Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker not found. Please install Docker Desktop.
    pause
    exit /b 1
)

echo ✅ Docker found
echo.

REM Start the container
echo 🚀 Starting NovaActuary™ container...
echo.

docker-compose up -d novaactuary

if %errorlevel% equ 0 (
    echo.
    echo ✅ NovaActuary™ container started successfully!
    echo.
    echo 📊 Container Status:
    docker-compose ps novaactuary
    echo.
    echo 🧪 To run health check:
    echo   docker-compose exec novaactuary node novaactuary/health-check.js
    echo.
    echo 🔍 To view logs:
    echo   docker-compose logs novaactuary
    echo.
    echo ⏹️  To stop the container:
    echo   docker-compose down
    echo.
) else (
    echo.
    echo ❌ Container start failed!
    echo Please check the error messages above.
    echo.
)

echo 🧠 Running health check...
timeout /t 5 /nobreak >nul
docker-compose exec novaactuary node novaactuary/health-check.js

pause
