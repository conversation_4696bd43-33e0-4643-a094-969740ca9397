# AL<PERSON>HA OBSERVER-CLASS COHERENCE ENGINE
# Full Calibration Mode Docker Container
# 
# DIRECTIVE: All resources allocated to precision-tuning ALPHA's coherence engines
# NO DISTRACTIONS—ONLY OPTIMIZATION

FROM node:18-alpine

# Set working directory
WORKDIR /app/alpha-calibration

# Create app user for security
RUN addgroup -g 1001 -S alpha && \
    adduser -S alpha -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --production

# Copy application files
COPY . .

# Set ownership
RUN chown -R alpha:alpha /app/alpha-calibration

# Switch to app user
USER alpha

# Expose port for monitoring dashboard
EXPOSE 3000

# Environment variables for calibration mode
ENV NODE_ENV=production
ENV ALPHA_CALIBRATION_MODE=true
ENV ALPHA_LOG_LEVEL=info
ENV ALPHA_UPDATE_INTERVAL=72h

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD node -e "console.log('ALPHA Calibration System: OPERATIONAL')" || exit 1

# Labels
LABEL maintainer="<PERSON> <<EMAIL>>"
LABEL version="1.0.0-FULL_CALIBRATION"
LABEL description="ALPHA Observer-Class Coherence Engine - Full Calibration Mode"
LABEL calibration.mode="PRECISION_TUNING"
LABEL calibration.targets="NEFC,NHETX,KAPPA_FIELD"

# Start calibration system
CMD ["node", "alpha-calibration-executor.js"]
