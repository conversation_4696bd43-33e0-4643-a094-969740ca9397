/**
 * NovaFuse Universal API Connector - Error Demo Routes
 * 
 * This module provides routes to demonstrate the error handling capabilities of the UAC.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../utils/async-handler');
const {
  ValidationError,
  MissingRequiredFieldError,
  AuthenticationError,
  ConnectionError,
  TimeoutError,
  ResourceNotFoundError,
  RateLimitExceededError,
  TransformationError,
  ConnectorError
} = require('../errors');

/**
 * Throw a validation error
 */
router.get('/validation-error', asyncHandler(async (req, res) => {
  throw new ValidationError('Validation failed for the request', {
    validationErrors: [
      { field: 'name', message: 'Name is required' },
      { field: 'email', message: 'Email is invalid' }
    ]
  });
}));

/**
 * Throw a missing required field error
 */
router.get('/missing-field', asyncHandler(async (req, res) => {
  throw new MissingRequiredFieldError(['name', 'email']);
}));

/**
 * Throw an authentication error
 */
router.get('/auth-error', asyncHandler(async (req, res) => {
  throw new AuthenticationError('Authentication failed');
}));

/**
 * Throw a connection error
 */
router.get('/connection-error', asyncHandler(async (req, res) => {
  throw new ConnectionError('Failed to connect to the service');
}));

/**
 * Throw a timeout error
 */
router.get('/timeout-error', asyncHandler(async (req, res) => {
  throw new TimeoutError('The request timed out after 30 seconds');
}));

/**
 * Throw a resource not found error
 */
router.get('/not-found-error', asyncHandler(async (req, res) => {
  throw new ResourceNotFoundError('User', '123');
}));

/**
 * Throw a rate limit exceeded error
 */
router.get('/rate-limit-error', asyncHandler(async (req, res) => {
  throw new RateLimitExceededError('Rate limit exceeded', {
    retryAfter: 60
  });
}));

/**
 * Throw a transformation error
 */
router.get('/transformation-error', asyncHandler(async (req, res) => {
  throw new TransformationError('Failed to transform data', {
    transformationId: 'user-transform',
    sourceData: { id: 123, name: 'John Doe' }
  });
}));

/**
 * Throw a connector error
 */
router.get('/connector-error', asyncHandler(async (req, res) => {
  throw new ConnectorError('Failed to execute connector operation', {
    connectorId: 'salesforce'
  });
}));

/**
 * Throw a generic error
 */
router.get('/generic-error', asyncHandler(async (req, res) => {
  throw new Error('This is a generic error');
}));

/**
 * Simulate an async error
 */
router.get('/async-error', asyncHandler(async (req, res) => {
  await new Promise(resolve => setTimeout(resolve, 100));
  throw new Error('Async operation failed');
}));

/**
 * Return success
 */
router.get('/success', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    message: 'Operation completed successfully'
  });
}));

module.exports = router;
