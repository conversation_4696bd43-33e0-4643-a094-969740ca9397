const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/security/controls/routes');
const models = require('../../../../apis/security/controls/models');

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/security/controls', router);

// Generate a large dataset for performance testing
const generateLargeFrameworkDataset = (count) => {
  const frameworks = [];
  for (let i = 0; i < count; i++) {
    frameworks.push({
      id: `cf-${i.toString().padStart(8, '0')}`,
      name: `Test Framework ${i}`,
      description: `Description for test framework ${i}`,
      version: `${2020 + (i % 5)}.${i % 10}`,
      category: i % 3 === 0 ? 'government' : i % 3 === 1 ? 'international' : 'industry',
      status: i % 5 === 0 ? 'draft' : 'active',
      controls: generateControls(i, 10 + (i % 20)), // Each framework has 10-29 controls
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return frameworks;
};

// Generate controls for a framework
const generateControls = (frameworkIndex, count) => {
  const controls = [];
  for (let i = 0; i < count; i++) {
    controls.push({
      id: `control-${frameworkIndex}-${i}`,
      identifier: `C-${frameworkIndex}-${i}`,
      title: `Control ${i} of Framework ${frameworkIndex}`,
      description: `Description for control ${i} of framework ${frameworkIndex}`,
      category: i % 5 === 0 ? 'access-control' : i % 5 === 1 ? 'authentication' : i % 5 === 2 ? 'encryption' : i % 5 === 3 ? 'audit' : 'configuration',
      priority: i % 3 === 0 ? 'high' : i % 3 === 1 ? 'medium' : 'low',
      status: 'active'
    });
  }
  return controls;
};

// Generate control tests
const generateControlTests = (frameworkCount, controlsPerFramework, count) => {
  const tests = [];
  for (let i = 0; i < count; i++) {
    const frameworkIndex = i % frameworkCount;
    const controlIndex = i % controlsPerFramework;
    
    tests.push({
      id: `ct-${i.toString().padStart(8, '0')}`,
      name: `Test ${i}`,
      description: `Description for test ${i}`,
      controlId: `control-${frameworkIndex}-${controlIndex}`,
      frameworkId: `cf-${frameworkIndex.toString().padStart(8, '0')}`,
      type: i % 2 === 0 ? 'manual' : 'automated',
      frequency: i % 4 === 0 ? 'annual' : i % 4 === 1 ? 'quarterly' : i % 4 === 2 ? 'monthly' : 'weekly',
      status: i % 10 === 0 ? 'inactive' : 'active',
      testSteps: generateTestSteps(i, 2 + (i % 5)), // Each test has 2-6 steps
      lastExecuted: i % 3 === 0 ? new Date().toISOString() : null,
      nextScheduled: new Date(Date.now() + (i % 30) * 24 * 60 * 60 * 1000).toISOString(),
      owner: `Team ${i % 5}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return tests;
};

// Generate test steps for a control test
const generateTestSteps = (testIndex, count) => {
  const steps = [];
  for (let i = 0; i < count; i++) {
    steps.push({
      id: `step-${testIndex}-${i}`,
      order: i + 1,
      description: `Step ${i + 1} of Test ${testIndex}`,
      expectedResult: `Expected result for step ${i + 1} of test ${testIndex}`,
      evidence: `Evidence for step ${i + 1}`
    });
  }
  return steps;
};

// Generate test results
const generateTestResults = (testCount, count) => {
  const results = [];
  for (let i = 0; i < count; i++) {
    const testIndex = i % testCount;
    const test = models.controlTests[testIndex];
    
    results.push({
      id: `tr-${i.toString().padStart(8, '0')}`,
      testId: test.id,
      executionDate: new Date(Date.now() - (i % 90) * 24 * 60 * 60 * 1000).toISOString(),
      status: i % 3 === 0 ? 'passed' : i % 3 === 1 ? 'failed' : 'partial',
      summary: `Summary for test result ${i}`,
      stepResults: generateStepResults(test.testSteps, i % 3 === 0),
      tester: i % 2 === 0 ? `User ${i % 10}` : 'Automated System',
      reviewedBy: i % 4 === 0 ? `Reviewer ${i % 5}` : null,
      reviewDate: i % 4 === 0 ? new Date(Date.now() - (i % 30) * 24 * 60 * 60 * 1000).toISOString() : null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return results;
};

// Generate step results for a test result
const generateStepResults = (steps, allPassed) => {
  return steps.map((step, index) => ({
    stepId: step.id,
    status: allPassed ? 'passed' : index === 0 ? 'failed' : 'passed',
    actualResult: `Actual result for ${step.id}`,
    evidence: `https://example.com/evidence/${step.id}.png`,
    notes: `Notes for ${step.id}`
  }));
};

// Mock the models with a large dataset
const FRAMEWORK_COUNT = 50;
const CONTROLS_PER_FRAMEWORK = 20;
const TEST_COUNT = 500;
const RESULT_COUNT = 1000;

jest.mock('../../../../apis/security/controls/models', () => {
  const frameworks = generateLargeFrameworkDataset(FRAMEWORK_COUNT);
  const tests = generateControlTests(FRAMEWORK_COUNT, CONTROLS_PER_FRAMEWORK, TEST_COUNT);
  const results = generateTestResults(TEST_COUNT, RESULT_COUNT);
  return {
    controlFrameworks: frameworks,
    controlTests: tests,
    testResults: results
  };
});

describe('Control Testing API Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);

  describe('GET /security/controls/frameworks', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/frameworks?page=1&limit=20');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(20);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Frameworks pagination response time: ${responseTime}ms`);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/frameworks?category=government&status=active');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Frameworks filtering response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/controls/frameworks/:id', () => {
    it('should retrieve a specific framework efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/frameworks/cf-00000010');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('cf-00000010');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get framework response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/controls/frameworks/:frameworkId/controls/:controlId', () => {
    it('should retrieve a specific control efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/frameworks/cf-00000010/controls/control-10-5');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('control-10-5');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get control response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/controls/tests', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/tests?page=1&limit=20');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(20);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Tests pagination response time: ${responseTime}ms`);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/tests?type=manual&frequency=annual');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Tests filtering response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/controls/tests/:id', () => {
    it('should retrieve a specific test efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/tests/ct-00000050');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('ct-00000050');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get test response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/controls/results', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/results?page=1&limit=20');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(20);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Results pagination response time: ${responseTime}ms`);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/results?status=passed&testId=ct-00000010');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
      console.log(`Results filtering response time: ${responseTime}ms`);
    });
  });

  describe('GET /security/controls/results/:id', () => {
    it('should retrieve a specific result efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/security/controls/results/tr-00000050');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('tr-00000050');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
      console.log(`Get result response time: ${responseTime}ms`);
    });
  });

  describe('POST /security/controls/tests', () => {
    it('should create a new control test efficiently', async () => {
      const newTest = {
        name: 'Performance Test',
        description: 'Test for performance testing',
        controlId: 'control-10-5',
        frameworkId: 'cf-00000010',
        type: 'manual',
        frequency: 'annual',
        status: 'active',
        testSteps: [
          {
            order: 1,
            description: 'Test step 1',
            expectedResult: 'Expected result 1',
            evidence: 'Evidence 1'
          },
          {
            order: 2,
            description: 'Test step 2',
            expectedResult: 'Expected result 2',
            evidence: 'Evidence 2'
          }
        ],
        owner: 'Test Team'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/security/controls/tests')
        .send(newTest);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create test response time: ${responseTime}ms`);
    });
  });

  describe('POST /security/controls/results', () => {
    it('should create a new test result efficiently', async () => {
      const newResult = {
        testId: 'ct-00000050',
        status: 'passed',
        summary: 'All test steps passed successfully',
        stepResults: [
          {
            stepId: 'step-50-0',
            status: 'passed',
            actualResult: 'Actual result 1',
            evidence: 'https://example.com/evidence/test.png',
            notes: 'Test notes'
          },
          {
            stepId: 'step-50-1',
            status: 'passed',
            actualResult: 'Actual result 2',
            evidence: 'https://example.com/evidence/test2.png',
            notes: 'Test notes 2'
          }
        ],
        tester: 'Test User'
      };

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/security/controls/results')
        .send(newResult);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 150ms
      expect(responseTime).toBeLessThan(150);
      console.log(`Create result response time: ${responseTime}ms`);
    });
  });

  describe('Concurrent requests', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const startTime = Date.now();
      
      // Make 10 concurrent requests
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(request(app).get(`/security/controls/tests?page=${i+1}&limit=10`));
      }
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });
      
      // Total response time for 10 concurrent requests should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Concurrent requests response time: ${totalResponseTime}ms`);
    });

    it('should handle concurrent read and write operations efficiently', async () => {
      const startTime = Date.now();
      
      // Create a mix of read and write operations
      const requests = [];
      
      // Read operations
      requests.push(request(app).get('/security/controls/frameworks?page=1&limit=10'));
      requests.push(request(app).get('/security/controls/tests?page=1&limit=10'));
      requests.push(request(app).get('/security/controls/results?page=1&limit=10'));
      requests.push(request(app).get('/security/controls/frameworks/cf-00000005'));
      
      // Write operations
      const newTest = {
        name: 'Concurrent Test',
        description: 'Test for concurrent testing',
        controlId: 'control-5-5',
        frameworkId: 'cf-00000005',
        type: 'manual',
        frequency: 'annual',
        status: 'active',
        testSteps: [
          {
            order: 1,
            description: 'Test step 1',
            expectedResult: 'Expected result 1',
            evidence: 'Evidence 1'
          }
        ],
        owner: 'Test Team'
      };
      
      const newResult = {
        testId: 'ct-00000010',
        status: 'passed',
        summary: 'All test steps passed successfully',
        stepResults: [
          {
            stepId: 'step-10-0',
            status: 'passed',
            actualResult: 'Actual result 1',
            evidence: 'https://example.com/evidence/test.png',
            notes: 'Test notes'
          }
        ],
        tester: 'Test User'
      };
      
      requests.push(request(app).post('/security/controls/tests').send(newTest));
      requests.push(request(app).post('/security/controls/results').send(newResult));
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200) || expect(response.status).toBe(201);
      });
      
      // Total response time for mixed operations should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
      console.log(`Mixed operations response time: ${totalResponseTime}ms`);
    });
  });

  describe('Load testing', () => {
    it('should handle a large number of sequential requests', async () => {
      const requestCount = 50;
      const startTime = Date.now();
      
      // Make sequential requests
      for (let i = 0; i < requestCount; i++) {
        const response = await request(app).get(`/security/controls/tests?page=1&limit=5`);
        expect(response.status).toBe(200);
      }
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      const averageResponseTime = totalResponseTime / requestCount;
      
      // Average response time should be under 20ms
      expect(averageResponseTime).toBeLessThan(20);
      console.log(`Sequential requests average response time: ${averageResponseTime}ms`);
    });
  });

  describe('Performance with large datasets', () => {
    it('should handle retrieving a framework with many controls efficiently', async () => {
      // Find a framework with many controls
      const frameworkWithManyControls = models.controlFrameworks.find(f => f.controls.length > 25);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/controls/frameworks/${frameworkWithManyControls.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.controls.length).toBeGreaterThan(25);
      
      // Response time should be under 150ms even with many controls
      expect(responseTime).toBeLessThan(150);
      console.log(`Framework with many controls response time: ${responseTime}ms`);
    });

    it('should handle retrieving a test with many steps efficiently', async () => {
      // Find a test with many steps
      const testWithManySteps = models.controlTests.find(t => t.testSteps.length > 5);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/controls/tests/${testWithManySteps.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.testSteps.length).toBeGreaterThan(5);
      
      // Response time should be under 150ms even with many steps
      expect(responseTime).toBeLessThan(150);
      console.log(`Test with many steps response time: ${responseTime}ms`);
    });

    it('should handle retrieving a result with many step results efficiently', async () => {
      // Find a result with many step results
      const resultWithManyStepResults = models.testResults.find(r => r.stepResults.length > 5);
      
      const startTime = Date.now();
      
      const response = await request(app).get(`/security/controls/results/${resultWithManyStepResults.id}`);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.stepResults.length).toBeGreaterThan(5);
      
      // Response time should be under 150ms even with many step results
      expect(responseTime).toBeLessThan(150);
      console.log(`Result with many step results response time: ${responseTime}ms`);
    });
  });
});
