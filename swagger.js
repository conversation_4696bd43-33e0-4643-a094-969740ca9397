/**
 * @swagger
 * components:
 *   securitySchemes:
 *     ApiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: apikey
 *   schemas:
 *     Error:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 *           description: Error type
 *         message:
 *           type: string
 *           description: Error message
 *       required:
 *         - error
 *         - message
 *     Pagination:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total number of items
 *         page:
 *           type: integer
 *           description: Current page number
 *         limit:
 *           type: integer
 *           description: Number of items per page
 *         pages:
 *           type: integer
 *           description: Total number of pages
 *       required:
 *         - total
 *         - page
 *         - limit
 *         - pages
 *   parameters:
 *     page:
 *       name: page
 *       in: query
 *       description: Page number
 *       schema:
 *         type: integer
 *         default: 1
 *     limit:
 *       name: limit
 *       in: query
 *       description: Number of items per page
 *       schema:
 *         type: integer
 *         default: 10
 *     sortBy:
 *       name: sortBy
 *       in: query
 *       description: Field to sort by
 *       schema:
 *         type: string
 *     sortOrder:
 *       name: sortOrder
 *       in: query
 *       description: Sort order (asc or desc)
 *       schema:
 *         type: string
 *         enum: [asc, desc]
 *         default: asc
 *   responses:
 *     Unauthorized:
 *       description: Unauthorized - Invalid or missing API key
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Error'
 *     NotFound:
 *       description: Resource not found
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Error'
 *     BadRequest:
 *       description: Bad request - Invalid input
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Error'
 *     InternalError:
 *       description: Internal server error
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Error'
 */
