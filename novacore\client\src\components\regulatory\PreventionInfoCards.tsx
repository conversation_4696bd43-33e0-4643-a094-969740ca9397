/**
 * Prevention Info Cards Component
 * 
 * This component displays cards with information about regulatory changes.
 */

import React from 'react';
import { AlertTriangle, Clock, CheckCircle, Calendar } from 'lucide-react';

interface PreventionInfoCardsProps {
  pending: number;
  overdue: number;
  upcoming: number;
  completed: number;
}

export const PreventionInfoCards: React.FC<PreventionInfoCardsProps> = ({
  pending,
  overdue,
  upcoming,
  completed,
}) => {
  const cards = [
    {
      title: 'Pending Changes',
      value: pending,
      icon: <Clock className="h-5 w-5 text-blue-500" />,
      color: 'bg-blue-50 border-blue-200',
      textColor: 'text-blue-700',
    },
    {
      title: 'Overdue Changes',
      value: overdue,
      icon: <AlertTriangle className="h-5 w-5 text-red-500" />,
      color: 'bg-red-50 border-red-200',
      textColor: 'text-red-700',
    },
    {
      title: 'Upcoming Changes',
      value: upcoming,
      icon: <Calendar className="h-5 w-5 text-yellow-500" />,
      color: 'bg-yellow-50 border-yellow-200',
      textColor: 'text-yellow-700',
    },
    {
      title: 'Completed Changes',
      value: completed,
      icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      color: 'bg-green-50 border-green-200',
      textColor: 'text-green-700',
    },
  ];
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {cards.map((card, index) => (
        <div 
          key={index} 
          className={`p-4 rounded-lg border ${card.color} flex items-center`}
        >
          <div className="mr-4">{card.icon}</div>
          <div>
            <div className={`text-sm font-medium ${card.textColor}`}>{card.title}</div>
            <div className="text-2xl font-bold">{card.value}</div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PreventionInfoCards;
