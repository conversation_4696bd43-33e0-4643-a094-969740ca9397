/**
 * Monitoring Tests
 * 
 * This file contains tests for the monitoring infrastructure.
 */

const request = require('supertest');
const app = require('../../server');
const prometheusMetrics = require('../../api/services/PrometheusMetricsService');

describe('Monitoring Infrastructure', () => {
  describe('Metrics Endpoints', () => {
    it('should return metrics in Prometheus format', async () => {
      const response = await request(app)
        .get('/metrics')
        .expect('Content-Type', /text\/plain/)
        .expect(200);
      
      // Verify that the response contains Prometheus metrics
      expect(response.text).toContain('# HELP');
      expect(response.text).toContain('# TYPE');
    });
    
    it('should return 200 OK for health check', async () => {
      const response = await request(app)
        .get('/health')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
    
    it('should return detailed health information', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('memory');
      expect(response.body).toHaveProperty('cpu');
      expect(response.body).toHaveProperty('system');
    });
    
    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/ready')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('status', 'ready');
    });
  });
  
  describe('Prometheus Metrics', () => {
    it('should record HTTP requests', () => {
      // Mock the observe method
      const observeSpy = jest.spyOn(prometheusMetrics.httpRequestDurationSeconds, 'observe');
      const incSpy = jest.spyOn(prometheusMetrics.httpRequestsTotal, 'inc');
      
      // Record a request
      prometheusMetrics.recordHttpRequest('GET', '/test', 200, 0.1);
      
      // Verify that the metrics were recorded
      expect(observeSpy).toHaveBeenCalledWith({ method: 'GET', route: '/test', status_code: 200 }, 0.1);
      expect(incSpy).toHaveBeenCalledWith({ method: 'GET', route: '/test', status_code: 200 });
      
      // Restore the original methods
      observeSpy.mockRestore();
      incSpy.mockRestore();
    });
    
    it('should record API errors', () => {
      // Mock the inc method
      const incSpy = jest.spyOn(prometheusMetrics.apiErrorsTotal, 'inc');
      
      // Record an error
      prometheusMetrics.recordApiError('GET', '/test', 'validation_error');
      
      // Verify that the metric was recorded
      expect(incSpy).toHaveBeenCalledWith({ method: 'GET', route: '/test', error_type: 'validation_error' });
      
      // Restore the original method
      incSpy.mockRestore();
    });
    
    it('should update connector health', () => {
      // Mock the set method
      const setSpy = jest.spyOn(prometheusMetrics.connectorHealthGauge, 'set');
      
      // Update connector health
      prometheusMetrics.updateConnectorHealth('test-connector', true);
      
      // Verify that the metric was updated
      expect(setSpy).toHaveBeenCalledWith({ connector: 'test-connector' }, 1);
      
      // Restore the original method
      setSpy.mockRestore();
    });
  });
});
