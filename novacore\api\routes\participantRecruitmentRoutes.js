/**
 * Participant Recruitment Routes
 * 
 * This file defines the routes for the participant recruitment API.
 */

const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/asyncHandler');
const { authenticate, authorize } = require('../middleware/authMiddleware');
const participantRecruitmentController = require('../controllers/ParticipantRecruitmentController');

/**
 * @route   POST /api/user-testing/participants
 * @desc    Create a new participant
 * @access  Public
 */
router.post(
  '/participants',
  asyncHandler(participantRecruitmentController.createParticipant)
);

/**
 * @route   GET /api/user-testing/participants
 * @desc    Get all participants
 * @access  Private
 */
router.get(
  '/participants',
  authenticate,
  authorize('read:user_testing'),
  async<PERSON>andler(participantRecruitmentController.getParticipants)
);

/**
 * @route   GET /api/user-testing/participants/:id
 * @desc    Get a participant by ID
 * @access  Private
 */
router.get(
  '/participants/:id',
  authenticate,
  authorize('read:user_testing'),
  async<PERSON><PERSON><PERSON>(participantRecruitmentController.getParticipantById)
);

/**
 * @route   PUT /api/user-testing/participants/:id
 * @desc    Update a participant
 * @access  Private
 */
router.put(
  '/participants/:id',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(participantRecruitmentController.updateParticipant)
);

/**
 * @route   DELETE /api/user-testing/participants/:id
 * @desc    Delete a participant
 * @access  Private
 */
router.delete(
  '/participants/:id',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(participantRecruitmentController.deleteParticipant)
);

/**
 * @route   POST /api/user-testing/recruitment-campaigns
 * @desc    Create a recruitment campaign
 * @access  Private
 */
router.post(
  '/recruitment-campaigns',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(participantRecruitmentController.createRecruitmentCampaign)
);

/**
 * @route   POST /api/user-testing/recruitment-campaigns/:campaignId/send
 * @desc    Send recruitment emails
 * @access  Private
 */
router.post(
  '/recruitment-campaigns/:campaignId/send',
  authenticate,
  authorize('write:user_testing'),
  asyncHandler(participantRecruitmentController.sendRecruitmentEmails)
);

module.exports = router;
