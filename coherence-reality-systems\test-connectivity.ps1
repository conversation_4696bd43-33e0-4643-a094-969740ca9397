# Test-Connectivity.ps1
# KetherNet Server Connectivity Test Script

# Set execution policy if needed (requires admin)
$currentPolicy = Get-ExecutionPolicy -Scope CurrentUser
if ($currentPolicy -eq "Restricted") {
    Write-Host "⚠️  Current execution policy is Restricted. Attempting to set to RemoteSigned..."
    try {
        Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Host "✅ Execution policy set to RemoteSigned"
    } catch {
        Write-Host "❌ Failed to set execution policy. Please run as Administrator."
        exit 1
    }
}

# Configuration
$serverUrl = "http://127.0.0.1:8080"
$testIterations = 3
$timeoutSec = 5

# Check if port is open
function Test-Port {
    param (
        [string]$hostname = "127.0.0.1",
        [int]$port = 8080,
        [int]$timeout = 2000
    )
    
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $connect = $tcpClient.BeginConnect($hostname, $port, $null, $null)
    $success = $connect.AsyncWaitHandle.WaitOne($timeout, $false)
    
    if ($success) {
        $tcpClient.EndConnect($connect) | Out-Null
        $tcpClient.Close()
        return $true
    } else {
        $tcpClient.Close()
        return $false
    }
}

# Test server connectivity
Write-Host "🔍 Testing KetherNet Server Connectivity..."
Write-Host "   Server: $serverUrl"
Write-Host "   Iterations: $testIterations"
Write-Host "   Timeout: ${timeoutSec}s per request\n"

# Test 1: Check if port is open
Write-Host "[1/3] 🔌 Testing port 8080..." -NoNewline
if (Test-Port -port 8080) {
    Write-Host "✅ Port 8080 is open" -ForegroundColor Green
} else {
    Write-Host "❌ Port 8080 is not responding" -ForegroundColor Red
    Write-Host "   Make sure the KetherNet server is running"
    exit 1
}

# Test 2: Test HTTP connectivity
Write-Host "\n[2/3] 🌐 Testing HTTP connectivity..."
$successCount = 0

for ($i = 1; $i -le $testIterations; $i++) {
    try {
        $startTime = Get-Date
        $response = Invoke-WebRequest -Uri "${serverUrl}/health" -TimeoutSec $timeoutSec -UseBasicParsing
        $duration = ((Get-Date) - $startTime).TotalMilliseconds
        
        $status = $response.StatusCode
        $data = $response.Content | ConvertFrom-Json
        
        Write-Host "   ✅ [$i/$testIterations] HTTP $status - ${duration}ms" -ForegroundColor Green
        Write-Host "      Status: $($data.status)"
        Write-Host "      Coherium: $($data.coherium)"
        Write-Host "      Block: $($data.lastBlock)"
        
        $successCount++
    } catch {
        Write-Host "   ❌ [$i/$testIterations] Failed: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode.value__
            $statusDesc = $_.Exception.Response.StatusDescription
            Write-Host "      Status: $statusCode $statusDesc"
        }
    }
    
    if ($i -lt $testIterations) {
        Start-Sleep -Milliseconds 500
    }
}

# Test 3: Check network configuration
Write-Host "\n[3/3] 🌍 Checking network configuration..."
$ipv4Address = (Test-Connection -ComputerName $env:COMPUTERNAME -Count 1).IPV4Address.IPAddressToString
$dnsServers = (Get-DnsClientServerAddress -AddressFamily IPv4 | Where-Object { $_.InterfaceAlias -notlike "*Loopback*" }).ServerAddresses

Write-Host "   Hostname: $env:COMPUTERNAME"
Write-Host "   IPv4 Address: $ipv4Address"
Write-Host "   DNS Servers: $($dnsServers -join ', ')"

# Summary
Write-Host "\n📊 Test Summary:"
Write-Host "   Port 8080: $(if (Test-Port -port 8080) { '✅ Open' } else { '❌ Closed' })"
Write-Host "   HTTP Requests: $successCount/$testIterations successful"
Write-Host "   Network: $(if ($ipv4Address) { '✅ Configured' } else { '❌ No IPv4 Address' })"

if ($successCount -eq 0) {
    Write-Host "\n❌ KetherNet Server is not responding. Please check if the server is running." -ForegroundColor Red
    exit 1
} elseif ($successCount -lt $testIterations) {
    Write-Host "\n⚠️  KetherNet Server is responding but some tests failed. Check for intermittent network issues." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "\n✅ All tests completed successfully! KetherNet Server is running and accessible." -ForegroundColor Green
    exit 0
}
