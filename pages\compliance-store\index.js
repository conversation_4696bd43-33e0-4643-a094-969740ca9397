import React from 'react';
import Link from 'next/link';
import PageWithSidebar from '../../components/PageWithSidebar';

export default function ComplianceStore() {
  // Define sidebar items for the Compliance App Store
  const sidebarItems = [
    { type: 'category', label: 'Compliance Store', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Browse Connectors', href: '/compliance-store/browse' },
      { label: 'My Dashboard', href: '/compliance-store/dashboard' },
      { label: 'Submit a Connector', href: '/compliance-store/submit-connector' },
      { label: 'Partner Benefits', href: '#partner-benefits' }
    ]},
    { type: 'category', label: 'Tools', items: [
      { label: 'Cross-Framework Mapping', href: '/compliance-store/mapping' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'API Documentation', href: '/api-docs' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' },
      { label: 'UAC Demo', href: '/uac-demo' }
    ]}
  ];

  // SEO metadata
  const pageProps = {
    title: 'NovaFuse Compliance App Store',
    description: "The world's first Compliance App Store, powered by NovaFuse's Universal API Connector technology.",
    keywords: 'compliance app store, regulatory compliance, GDPR, HIPAA, SOC2, PCI DSS, compliance solutions',
    canonical: 'https://novafuse.io/compliance-store',
    ogImage: '/images/compliance-store-og-image.png'
  };

  return (
    <PageWithSidebar title={pageProps.title} sidebarItems={sidebarItems} {...pageProps}>
      {/* Hero Section */}
      <div id="overview" className="accent-bg text-white rounded-lg p-8 mb-8">
        <div className="flex justify-center mb-4">
          <div className="bg-blue-800 bg-opacity-70 px-4 py-2 rounded-full text-sm font-bold inline-flex items-center">
            <span className="mr-2">🛡️</span>
            <span>PATENT PENDING</span>
          </div>
        </div>

        <h1 className="text-3xl md:text-4xl font-bold mb-4 text-center">NovaFuse Compliance App Store</h1>

        <p className="text-xl mb-6 text-center max-w-3xl mx-auto">
          The world's first Compliance App Store, powered by NovaFuse's Universal API Connector technology.
        </p>

        <div className="flex justify-center gap-4">
          <Link href="/compliance-store/browse" className="bg-white text-blue-700 hover:bg-gray-100 font-bold py-2 px-6 rounded-lg">
            Browse Connectors
          </Link>
          <Link href="#partner-benefits" className="bg-transparent hover:bg-blue-700 text-white border border-white font-bold py-2 px-6 rounded-lg">
            Become a Partner
          </Link>
        </div>

        <div className="mt-8 pt-6 border-t border-blue-700 border-opacity-30">
          <p className="text-center text-sm opacity-80">
            Protected by multiple patent applications covering our innovative compliance technologies.
          </p>
        </div>
      </div>

      {/* Preview Notice */}
      <div className="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-8 mb-12">
        <h2 className="text-2xl font-bold mb-4 text-center">Preview Mode</h2>
        <p className="text-center text-lg mb-6">
          Welcome to the preview of the world's first Compliance App Store. Explore our growing catalog of compliance connectors!
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link href="/compliance-store/browse" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg">
            Browse Connectors
          </Link>
          <Link href="/compliance-store/dashboard" className="bg-transparent hover:bg-blue-700 text-white border border-white font-bold py-2 px-6 rounded-lg">
            My Dashboard
          </Link>
          <Link href="/compliance-store/mapping" className="bg-transparent hover:bg-blue-700 text-white border border-white font-bold py-2 px-6 rounded-lg">
            Cross-Framework Mapping
          </Link>
          <Link href="/compliance-store/submit-connector" className="bg-transparent hover:bg-blue-700 text-white border border-white font-bold py-2 px-6 rounded-lg">
            Submit a Connector
          </Link>
        </div>
      </div>

      {/* Value Proposition */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Why Choose the Compliance App Store?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-secondary rounded-lg p-6">
            <div className="flex items-center mb-3">
              <div className="text-blue-400 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold">Cross-Framework Mapping</h3>
            </div>
            <p>Our patented technology automatically translates compliance evidence across different regulatory frameworks, saving countless hours of manual work.</p>
          </div>
          <div className="bg-secondary rounded-lg p-6">
            <div className="flex items-center mb-3">
              <div className="text-blue-400 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold">Guaranteed-Latency Processing</h3>
            </div>
            <p>Meet regulatory deadlines with our guaranteed-latency compliance processing system. Critical compliance operations are prioritized to ensure SLA compliance.</p>
          </div>
          <div className="bg-secondary rounded-lg p-6">
            <div className="flex items-center mb-3">
              <div className="text-blue-400 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold">Tamper-Evident Execution</h3>
            </div>
            <p>Our innovative tamper-evident execution system ensures the integrity and auditability of all compliance operations with cryptographically verifiable audit trails.</p>
          </div>
        </div>
      </div>

      {/* Partner Benefits Section */}
      <div id="partner-benefits" className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Partner Benefits</h2>
        <div className="bg-secondary rounded-lg p-6">
          <div className="md:flex">
            <div className="md:w-2/3 md:pr-6">
              <ul className="space-y-4">
                <li className="flex items-start">
                  <span className="text-green-400 mr-2">✓</span>
                  <span><strong>Up to 85% revenue share</strong> on your connectors</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 mr-2">✓</span>
                  <span><strong>Reach thousands</strong> of organizations seeking compliance solutions</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 mr-2">✓</span>
                  <span><strong>Technical support</strong> and development resources</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-400 mr-2">✓</span>
                  <span><strong>Co-marketing opportunities</strong> and lead generation</span>
                </li>
              </ul>
            </div>
            <div className="md:w-1/3 mt-6 md:mt-0 flex items-center justify-center">
              <Link href="/compliance-store/submit-connector" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg text-center">
                Submit Your Connector
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Confidentiality Notice */}
      <div className="mb-12 border border-blue-800 bg-blue-900 bg-opacity-20 rounded-lg p-4">
        <div className="flex items-start">
          <div className="text-yellow-400 mr-3 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm">
              <strong>CONFIDENTIAL:</strong> The NovaFuse Compliance App Store is currently under IP protection review.
              All content is considered confidential and proprietary. Unauthorized access or sharing is prohibited.
            </p>
          </div>
        </div>
      </div>
    </PageWithSidebar>
  );
}
