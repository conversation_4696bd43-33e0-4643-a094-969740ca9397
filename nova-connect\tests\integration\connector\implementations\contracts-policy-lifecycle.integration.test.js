/**
 * Integration tests for the Contracts & Policy Lifecycle Connector
 */

const nock = require('nock');
const ContractsPolicyLifecycleConnector = require('../../../../connector/implementations/contracts-policy-lifecycle');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('ContractsPolicyLifecycleConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();
    
    // Create connector instance
    connector = new ContractsPolicyLifecycleConnector({
      baseUrl
    }, {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    });
    
    // Mock authentication
    nock(baseUrl)
      .post('/oauth2/token')
      .reply(200, {
        access_token: 'test-access-token',
        expires_in: 3600
      });
  });
  
  describe('Contract Management', () => {
    it('should list contracts', async () => {
      // Mock contracts endpoint
      const mockContracts = {
        data: [
          {
            id: 'contract-1',
            title: 'Contract 1',
            status: 'active',
            startDate: '2023-01-01',
            endDate: '2023-12-31'
          },
          {
            id: 'contract-2',
            title: 'Contract 2',
            status: 'active',
            startDate: '2023-02-01',
            endDate: '2023-12-31'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/contracts')
        .query({ status: 'active' })
        .reply(200, mockContracts);
      
      // Initialize connector
      await connector.initialize();
      
      // List contracts
      const result = await connector.listContracts({ status: 'active' });
      
      // Verify result
      expect(result).toEqual(mockContracts);
    });
    
    it('should get a specific contract', async () => {
      // Mock contract endpoint
      const mockContract = {
        id: 'contract-123',
        title: 'Test Contract',
        description: 'Test Description',
        status: 'active',
        type: 'service',
        parties: [
          {
            id: 'party-1',
            name: 'Acme Corp',
            role: 'client'
          },
          {
            id: 'party-2',
            name: 'Tech Solutions Inc',
            role: 'vendor'
          }
        ],
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        value: 50000,
        currency: 'USD'
      };
      
      nock(baseUrl)
        .get('/contracts/contract-123')
        .reply(200, mockContract);
      
      // Initialize connector
      await connector.initialize();
      
      // Get contract
      const result = await connector.getContract('contract-123');
      
      // Verify result
      expect(result).toEqual(mockContract);
    });
    
    it('should create a new contract', async () => {
      // Contract data
      const contractData = {
        title: 'New Contract',
        description: 'New contract description',
        type: 'service',
        parties: [
          {
            name: 'Acme Corp',
            role: 'client'
          },
          {
            name: 'New Tech Solutions',
            role: 'vendor'
          }
        ],
        startDate: '2023-07-01',
        endDate: '2024-06-30',
        value: 75000,
        currency: 'USD'
      };
      
      // Mock response
      const mockResponse = {
        id: 'contract-new',
        ...contractData,
        status: 'draft',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/contracts', contractData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create contract
      const result = await connector.createContract(contractData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should update an existing contract', async () => {
      // Contract update data
      const contractId = 'contract-123';
      const updateData = {
        title: 'Updated Contract',
        description: 'Updated description',
        status: 'active'
      };
      
      // Mock response
      const mockResponse = {
        id: contractId,
        title: 'Updated Contract',
        description: 'Updated description',
        status: 'active',
        type: 'service',
        parties: [
          {
            id: 'party-1',
            name: 'Acme Corp',
            role: 'client'
          },
          {
            id: 'party-2',
            name: 'Tech Solutions Inc',
            role: 'vendor'
          }
        ],
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        value: 50000,
        currency: 'USD',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      
      nock(baseUrl)
        .put(`/contracts/${contractId}`, updateData)
        .reply(200, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Update contract
      const result = await connector.updateContract(contractId, updateData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should delete a contract', async () => {
      // Contract ID
      const contractId = 'contract-123';
      
      nock(baseUrl)
        .delete(`/contracts/${contractId}`)
        .reply(204);
      
      // Initialize connector
      await connector.initialize();
      
      // Delete contract
      await connector.deleteContract(contractId);
      
      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  
  describe('Policy Management', () => {
    it('should list policies', async () => {
      // Mock policies endpoint
      const mockPolicies = {
        data: [
          {
            id: 'policy-1',
            title: 'Information Security Policy',
            status: 'active',
            category: 'security',
            version: '1.2'
          },
          {
            id: 'policy-2',
            title: 'Data Protection Policy',
            status: 'active',
            category: 'privacy',
            version: '1.0'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/policies')
        .query({ status: 'active' })
        .reply(200, mockPolicies);
      
      // Initialize connector
      await connector.initialize();
      
      // List policies
      const result = await connector.listPolicies({ status: 'active' });
      
      // Verify result
      expect(result).toEqual(mockPolicies);
    });
    
    it('should get a specific policy', async () => {
      // Mock policy endpoint
      const mockPolicy = {
        id: 'policy-123',
        title: 'Information Security Policy',
        description: 'Policy governing information security practices',
        content: '# Information Security Policy\n\n## 1. Introduction\n\nThis policy...',
        status: 'active',
        category: 'security',
        version: '1.2',
        effectiveDate: '2023-01-15',
        reviewDate: '2024-01-15'
      };
      
      nock(baseUrl)
        .get('/policies/policy-123')
        .reply(200, mockPolicy);
      
      // Initialize connector
      await connector.initialize();
      
      // Get policy
      const result = await connector.getPolicy('policy-123');
      
      // Verify result
      expect(result).toEqual(mockPolicy);
    });
    
    it('should create a new policy', async () => {
      // Policy data
      const policyData = {
        title: 'New Security Policy',
        description: 'New security policy description',
        content: '# New Security Policy\n\n## 1. Introduction\n\nThis policy...',
        category: 'security'
      };
      
      // Mock response
      const mockResponse = {
        id: 'policy-new',
        ...policyData,
        status: 'draft',
        version: '1.0',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/policies', policyData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create policy
      const result = await connector.createPolicy(policyData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Clean previous nock mocks
      nock.cleanAll();
      
      // Mock authentication error
      nock(baseUrl)
        .post('/oauth2/token')
        .reply(401, {
          error: 'invalid_client',
          error_description: 'Invalid client credentials'
        });
      
      // Try to initialize connector
      await expect(connector.initialize()).rejects.toThrow('Authentication failed');
    });
    
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl)
        .get('/contracts/non-existent')
        .reply(404, {
          error: 'not_found',
          error_description: 'Contract not found'
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to get non-existent contract
      await expect(connector.getContract('non-existent')).rejects.toThrow('Error getting contract');
    });
    
    it('should handle validation errors', async () => {
      // Contract data with missing required fields
      const invalidData = {
        title: 'Invalid Contract'
        // Missing required fields: type, parties, startDate
      };
      
      // Mock validation error
      nock(baseUrl)
        .post('/contracts', invalidData)
        .reply(400, {
          error: 'validation_error',
          error_description: 'Validation failed',
          errors: [
            { field: 'type', message: 'Type is required' },
            { field: 'parties', message: 'Parties are required' },
            { field: 'startDate', message: 'Start date is required' }
          ]
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to create invalid contract
      await expect(connector.createContract(invalidData)).rejects.toThrow('type is required');
    });
  });
});
