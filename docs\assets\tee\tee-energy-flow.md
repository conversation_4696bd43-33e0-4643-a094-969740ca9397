```mermaid
flowchart TD
    %% Input Energy
    A[Time & Energy Input] -->|Allocation| B[Task Execution]
    
    %% Friction Points
    B --> C{Friction Analysis}
    C -->|High Friction| D[Inefficient Path]
    C -->|Optimized| E[Efficient Path]
    
    %% Efficiency Conversion
    D --> F[Energy Loss: High]
    E --> G[Energy Gain: High]
    
    %% Output
    F --> H[Low ROI]
    G --> I[High ROI]
    
    %% Styling
    classDef input fill:#2E7D32,color:white,stroke:#1B5E20
    classDef efficient fill:#2E7D32,color:white,stroke:#1B5E20
    classDef inefficient fill:#B71C1C,color:white,stroke:#7F0000
    classDef neutral fill:#546E7A,color:white,stroke:#263238
    
    class A input
    class B,E,G,I efficient
    class D,F,H inefficient
    class C neutral
```

## TEE Energy Flow Diagram

### Key Components:
1. **Input Energy**
   - Represents the initial time and energy investment
   - Starting point for all task execution

2. **Friction Analysis**
   - Decision point where energy flow is optimized
   - Determines path efficiency

3. **Efficient Path**
   - High η (efficiency)
   - Low F (friction)
   - Results in maximum ROI

4. **Inefficient Path**
   - Low η (efficiency)
   - High F (friction)
   - Results in energy drain

### How to Read:
- Green paths show optimal energy flow
- Red paths indicate energy loss
- Gray nodes are decision/analysis points

### Usage:
1. Identify which path your current tasks follow
2. Apply TEE principles to shift right
3. Monitor energy flow for continuous optimization
