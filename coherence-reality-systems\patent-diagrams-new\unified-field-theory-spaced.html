<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Unified Field Theory Architecture</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 2: Universal Unified Field Theory (UUFT) Architecture</h1>

    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 800px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">UNIVERSAL UNIFIED FIELD THEORY (UUFT) ARCHITECTURE</div>
        </div>

        <!-- Input Components -->
        <div class="component-box" style="left: 25px; top: 80px; width: 200px; height: 100px;">
            <div class="component-number-inside">601</div>
            <div class="component-label">Input A</div>
            <div style="font-size: 12px; text-align: center;">
                Domain-Specific Data
            </div>
        </div>

        <div class="component-box" style="left: 525px; top: 80px; width: 200px; height: 100px;">
            <div class="component-number-inside">602</div>
            <div class="component-label">Input B</div>
            <div style="font-size: 12px; text-align: center;">
                Domain-Specific Data
            </div>
        </div>

        <!-- Tensor Operator -->
        <div class="component-box" style="left: 275px; top: 180px; width: 200px; height: 100px;">
            <div class="component-number-inside">603</div>
            <div class="component-label">Tensor Operator ⊗</div>
            <div style="font-size: 12px; text-align: center;">
                Multi-dimensional Integration
            </div>
        </div>

        <!-- Arrows to Tensor Operator -->
        <div class="arrow-line" style="left: 125px; top: 125px; width: 150px; height: 800px; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow-head" style="left: 275px; top: 225px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>

        <div class="arrow-line" style="left: 625px; top: 125px; width: 150px; height: 800px; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow-head" style="left: 475px; top: 225px; border-width: 4px 8px 4px 0; border-color: transparent #555555 transparent transparent;"></div>

        <!-- Tensor Product -->
        <div class="component-box" style="left: 275px; top: 280px; width: 200px; height: 100px;">
            <div class="component-number-inside">604</div>
            <div class="component-label">Tensor Product</div>
            <div style="font-size: 12px; text-align: center;">
                Integrated Data
            </div>
        </div>

        <!-- Arrow from Tensor Operator to Tensor Product -->
        <div class="arrow-line" style="left: 375px; top: 270px; width: 2px; height: 800px;"></div>
        <div class="arrow-head" style="left: 371px; top: 280px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <!-- Input C -->
        <div class="component-box" style="left: 25px; top: 280px; width: 200px; height: 100px;">
            <div class="component-number-inside">605</div>
            <div class="component-label">Input C</div>
            <div style="font-size: 12px; text-align: center;">
                Domain-Specific Data
            </div>
        </div>

        <!-- Fusion Operator -->
        <div class="component-box" style="left: 275px; top: 380px; width: 200px; height: 100px;">
            <div class="component-number-inside">606</div>
            <div class="component-label">Fusion Operator ⊕</div>
            <div style="font-size: 12px; text-align: center;">
                Non-linear Synergy
            </div>
        </div>

        <!-- Arrows to Fusion Operator -->
        <div class="arrow-line" style="left: 375px; top: 370px; width: 2px; height: 800px;"></div>
        <div class="arrow-head" style="left: 371px; top: 380px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <div class="arrow-line" style="left: 125px; top: 325px; width: 150px; height: 800px; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow-head" style="left: 275px; top: 425px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>

        <!-- Circular Trust Topology -->
        <div class="component-box" style="left: 275px; top: 480px; width: 200px; height: 100px;">
            <div class="component-number-inside">607</div>
            <div class="component-label">Circular Trust Topology</div>
            <div style="font-size: 12px; text-align: center;">
                π10³
            </div>
        </div>

        <!-- Arrow from Fusion Operator to Circular Trust Topology -->
        <div class="arrow-line" style="left: 375px; top: 470px; width: 2px; height: 800px;"></div>
        <div class="arrow-head" style="left: 371px; top: 480px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <!-- 18/82 Principle -->
        <div class="component-box" style="left: 525px; top: 380px; width: 200px; height: 100px;">
            <div class="component-number-inside">608</div>
            <div class="component-label">18/82 Principle</div>
            <div style="font-size: 12px; text-align: center;">
                Optimal Resource Allocation
            </div>
        </div>

        <!-- Final Result -->
        <div class="component-box" style="left: 525px; top: 480px; width: 200px; height: 100px;">
            <div class="component-number-inside">609</div>
            <div class="component-label">Final Result</div>
            <div style="font-size: 12px; text-align: center;">
                3,142x Performance Improvement
            </div>
        </div>

        <!-- Arrow from Circular Trust Topology to Final Result -->
        <div class="arrow-line" style="left: 475px; top: 525px; width: 50px; height: 800px;"></div>
        <div class="arrow-head" style="left: 525px; top: 521px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #555555;"></div>

        <!-- Arrow from 18/82 Principle to Final Result -->
        <div class="arrow-line" style="left: 625px; top: 470px; width: 2px; height: 800px;"></div>
        <div class="arrow-head" style="left: 621px; top: 480px; border-width: 8px 4px 0 4px; border-color: #555555 transparent transparent transparent;"></div>

        <!-- Legend -->
        <div class="legend" style="position: absolute; right: 10px; bottom: 10px; background-color: white; border: 1px solid #ddd; border-radius: 4px; padding: 5px; z-index: 10; width: 200px;">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Input Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Processing Operators</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Output Components</div>
            </div>
        </div>

        <div class="inventor-label" style="position: absolute; left: 10px; bottom: 30px; font-size: 12px; font-style: italic; color: #333;">Inventor: David Nigel Irvin</div>
</div>

</body>
</html>
