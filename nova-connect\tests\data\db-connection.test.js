/**
 * NovaFuse Universal API Connector - Database Connection Tests
 */

const mongoose = require('mongoose');
const { dbConnection } = require('../../src/data');

// Mock mongoose
jest.mock('mongoose', () => ({
  connect: jest.fn().mockResolvedValue(true),
  disconnect: jest.fn().mockResolvedValue(true),
  connection: {
    on: jest.fn(),
    readyState: 1
  }
}));

describe('Database Connection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should connect to MongoDB', async () => {
    const connection = await dbConnection.connect();
    
    expect(mongoose.connect).toHaveBeenCalled();
    expect(connection).toBeDefined();
  });
  
  it('should reuse existing connection', async () => {
    // First connection
    await dbConnection.connect();
    
    // Second connection should reuse the first
    await dbConnection.connect();
    
    expect(mongoose.connect).toHaveBeenCalledTimes(1);
  });
  
  it('should disconnect from MongoDB', async () => {
    await dbConnection.connect();
    await dbConnection.disconnect();
    
    expect(mongoose.disconnect).toHaveBeenCalled();
  });
  
  it('should check if connected to MongoDB', async () => {
    await dbConnection.connect();
    
    const isConnected = dbConnection.isConnected();
    
    expect(isConnected).toBe(true);
  });
  
  it('should handle connection errors', async () => {
    mongoose.connect.mockRejectedValueOnce(new Error('Connection error'));
    
    await expect(dbConnection.connect()).rejects.toThrow('Connection error');
  });
  
  it('should handle disconnection errors', async () => {
    mongoose.disconnect.mockRejectedValueOnce(new Error('Disconnection error'));
    
    await dbConnection.connect();
    await expect(dbConnection.disconnect()).rejects.toThrow('Disconnection error');
  });
});
