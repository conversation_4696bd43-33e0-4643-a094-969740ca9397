{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "circuitBreaker", "require", "describe", "it", "req", "res", "next", "jest", "fn", "handler", "expect", "not", "toHaveBeenCalled", "error", "Error", "toHaveBeenCalledWith", "beforeEach", "useFakeTimers", "after<PERSON>ach", "useRealTimers", "mockResolvedValue", "promise", "resolves", "toBe", "toHaveBeenCalledTimes", "mockRejectedValueOnce", "mockResolvedValueOnce", "initialDelay", "advanceTimersByTime", "mockRejectedValue", "maxRetries", "rejects", "toThrow", "retryError", "noRetryError", "shouldRetry", "protectedFn", "failureT<PERSON><PERSON>old", "isFailure"], "sources": ["async-handler.test.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Async Handler Tests\n * \n * This module tests the async handler utilities for the UAC.\n */\n\nconst { asyncHandler, retryWithBackoff, circuitBreaker } = require('../../src/utils/async-handler');\n\ndescribe('Async Handler', () => {\n  describe('asyncHandler', () => {\n    it('should pass the result to the next middleware on success', async () => {\n      const req = {};\n      const res = {};\n      const next = jest.fn();\n      \n      const handler = asyncHandler(async (req, res) => {\n        return 'success';\n      });\n      \n      await handler(req, res, next);\n      \n      expect(next).not.toHaveBeenCalled();\n    });\n    \n    it('should pass the error to the next middleware on failure', async () => {\n      const req = {};\n      const res = {};\n      const next = jest.fn();\n      const error = new Error('Test error');\n      \n      const handler = asyncHandler(async (req, res) => {\n        throw error;\n      });\n      \n      await handler(req, res, next);\n      \n      expect(next).toHaveBeenCalledWith(error);\n    });\n  });\n  \n  describe('retryWithBackoff', () => {\n    beforeEach(() => {\n      jest.useFakeTimers();\n    });\n    \n    afterEach(() => {\n      jest.useRealTimers();\n    });\n    \n    it('should return the result if the function succeeds on first try', async () => {\n      const fn = jest.fn().mockResolvedValue('success');\n      \n      const promise = retryWithBackoff(fn);\n      \n      await expect(promise).resolves.toBe('success');\n      expect(fn).toHaveBeenCalledTimes(1);\n    });\n    \n    it('should retry the function if it fails', async () => {\n      const error = new Error('Test error');\n      const fn = jest.fn()\n        .mockRejectedValueOnce(error)\n        .mockResolvedValueOnce('success');\n      \n      const promise = retryWithBackoff(fn, { initialDelay: 100 });\n      \n      // Fast-forward time to trigger retry\n      jest.advanceTimersByTime(200);\n      \n      await expect(promise).resolves.toBe('success');\n      expect(fn).toHaveBeenCalledTimes(2);\n    });\n    \n    it('should throw the error if max retries is reached', async () => {\n      const error = new Error('Test error');\n      const fn = jest.fn().mockRejectedValue(error);\n      \n      const promise = retryWithBackoff(fn, { maxRetries: 2, initialDelay: 100 });\n      \n      // Fast-forward time to trigger retries\n      jest.advanceTimersByTime(300);\n      \n      await expect(promise).rejects.toThrow(error);\n      expect(fn).toHaveBeenCalledTimes(3); // Initial + 2 retries\n    });\n    \n    it('should respect shouldRetry function', async () => {\n      const retryError = new Error('Retry error');\n      const noRetryError = new Error('No retry error');\n      const fn = jest.fn()\n        .mockRejectedValueOnce(retryError)\n        .mockRejectedValueOnce(noRetryError);\n      \n      const shouldRetry = jest.fn(error => error === retryError);\n      \n      const promise = retryWithBackoff(fn, { \n        maxRetries: 2, \n        initialDelay: 100,\n        shouldRetry\n      });\n      \n      // Fast-forward time to trigger retry\n      jest.advanceTimersByTime(200);\n      \n      await expect(promise).rejects.toThrow(noRetryError);\n      expect(fn).toHaveBeenCalledTimes(2);\n      expect(shouldRetry).toHaveBeenCalledTimes(2);\n    });\n  });\n  \n  describe('circuitBreaker', () => {\n    it('should pass through the result if the function succeeds', async () => {\n      const fn = jest.fn().mockResolvedValue('success');\n      \n      const protectedFn = circuitBreaker(fn);\n      \n      await expect(protectedFn()).resolves.toBe('success');\n      expect(fn).toHaveBeenCalledTimes(1);\n    });\n    \n    it('should pass through the error if the function fails but threshold not reached', async () => {\n      const error = new Error('Test error');\n      const fn = jest.fn().mockRejectedValue(error);\n      \n      const protectedFn = circuitBreaker(fn, { failureThreshold: 2 });\n      \n      await expect(protectedFn()).rejects.toThrow(error);\n      expect(fn).toHaveBeenCalledTimes(1);\n    });\n    \n    it('should open the circuit after threshold failures', async () => {\n      const error = new Error('Test error');\n      const fn = jest.fn().mockRejectedValue(error);\n      \n      const protectedFn = circuitBreaker(fn, { failureThreshold: 2 });\n      \n      // First failure\n      await expect(protectedFn()).rejects.toThrow(error);\n      \n      // Second failure - should open the circuit\n      await expect(protectedFn()).rejects.toThrow(error);\n      \n      // Third call - should fail with circuit open error\n      await expect(protectedFn()).rejects.toThrow('Circuit is open');\n      \n      expect(fn).toHaveBeenCalledTimes(2);\n    });\n    \n    it('should reset failures after a successful call', async () => {\n      const error = new Error('Test error');\n      const fn = jest.fn()\n        .mockRejectedValueOnce(error)\n        .mockResolvedValueOnce('success')\n        .mockRejectedValueOnce(error);\n      \n      const protectedFn = circuitBreaker(fn, { failureThreshold: 2 });\n      \n      // First failure\n      await expect(protectedFn()).rejects.toThrow(error);\n      \n      // Success - should reset failures\n      await expect(protectedFn()).resolves.toBe('success');\n      \n      // Another failure - should not open the circuit yet\n      await expect(protectedFn()).rejects.toThrow(error);\n      \n      expect(fn).toHaveBeenCalledTimes(3);\n    });\n    \n    it('should respect isFailure function', async () => {\n      const retryError = new Error('Retry error');\n      const noRetryError = new Error('No retry error');\n      \n      const fn = jest.fn()\n        .mockRejectedValueOnce(retryError)\n        .mockRejectedValueOnce(retryError)\n        .mockRejectedValueOnce(noRetryError);\n      \n      const isFailure = jest.fn(error => error === retryError);\n      \n      const protectedFn = circuitBreaker(fn, { \n        failureThreshold: 2,\n        isFailure\n      });\n      \n      // First failure (counts)\n      await expect(protectedFn()).rejects.toThrow(retryError);\n      \n      // Second failure (counts) - should open the circuit\n      await expect(protectedFn()).rejects.toThrow(retryError);\n      \n      // Third call - should fail with circuit open error\n      await expect(protectedFn()).rejects.toThrow('Circuit is open');\n      \n      expect(fn).toHaveBeenCalledTimes(2);\n      expect(isFailure).toHaveBeenCalledTimes(2);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA,YAAY;EAAEC,gBAAgB;EAAEC;AAAe,CAAC,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AAEnGC,QAAQ,CAAC,eAAe,EAAE,MAAM;EAC9BA,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7BC,EAAE,CAAC,0DAA0D,EAAE,YAAY;MACzE,MAAMC,GAAG,GAAG,CAAC,CAAC;MACd,MAAMC,GAAG,GAAG,CAAC,CAAC;MACd,MAAMC,IAAI,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;MAEtB,MAAMC,OAAO,GAAGX,YAAY,CAAC,OAAOM,GAAG,EAAEC,GAAG,KAAK;QAC/C,OAAO,SAAS;MAClB,CAAC,CAAC;MAEF,MAAMI,OAAO,CAACL,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;MAE7BI,MAAM,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACC,gBAAgB,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFT,EAAE,CAAC,yDAAyD,EAAE,YAAY;MACxE,MAAMC,GAAG,GAAG,CAAC,CAAC;MACd,MAAMC,GAAG,GAAG,CAAC,CAAC;MACd,MAAMC,IAAI,GAAGC,IAAI,CAACC,EAAE,CAAC,CAAC;MACtB,MAAMK,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MAErC,MAAML,OAAO,GAAGX,YAAY,CAAC,OAAOM,GAAG,EAAEC,GAAG,KAAK;QAC/C,MAAMQ,KAAK;MACb,CAAC,CAAC;MAEF,MAAMJ,OAAO,CAACL,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;MAE7BI,MAAM,CAACJ,IAAI,CAAC,CAACS,oBAAoB,CAACF,KAAK,CAAC;IAC1C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCc,UAAU,CAAC,MAAM;MACfT,IAAI,CAACU,aAAa,CAAC,CAAC;IACtB,CAAC,CAAC;IAEFC,SAAS,CAAC,MAAM;MACdX,IAAI,CAACY,aAAa,CAAC,CAAC;IACtB,CAAC,CAAC;IAEFhB,EAAE,CAAC,gEAAgE,EAAE,YAAY;MAC/E,MAAMK,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACY,iBAAiB,CAAC,SAAS,CAAC;MAEjD,MAAMC,OAAO,GAAGtB,gBAAgB,CAACS,EAAE,CAAC;MAEpC,MAAME,MAAM,CAACW,OAAO,CAAC,CAACC,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;MAC9Cb,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFrB,EAAE,CAAC,uCAAuC,EAAE,YAAY;MACtD,MAAMU,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MACrC,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CACjBiB,qBAAqB,CAACZ,KAAK,CAAC,CAC5Ba,qBAAqB,CAAC,SAAS,CAAC;MAEnC,MAAML,OAAO,GAAGtB,gBAAgB,CAACS,EAAE,EAAE;QAAEmB,YAAY,EAAE;MAAI,CAAC,CAAC;;MAE3D;MACApB,IAAI,CAACqB,mBAAmB,CAAC,GAAG,CAAC;MAE7B,MAAMlB,MAAM,CAACW,OAAO,CAAC,CAACC,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;MAC9Cb,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFrB,EAAE,CAAC,kDAAkD,EAAE,YAAY;MACjE,MAAMU,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MACrC,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACqB,iBAAiB,CAAChB,KAAK,CAAC;MAE7C,MAAMQ,OAAO,GAAGtB,gBAAgB,CAACS,EAAE,EAAE;QAAEsB,UAAU,EAAE,CAAC;QAAEH,YAAY,EAAE;MAAI,CAAC,CAAC;;MAE1E;MACApB,IAAI,CAACqB,mBAAmB,CAAC,GAAG,CAAC;MAE7B,MAAMlB,MAAM,CAACW,OAAO,CAAC,CAACU,OAAO,CAACC,OAAO,CAACnB,KAAK,CAAC;MAC5CH,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC;IAEFrB,EAAE,CAAC,qCAAqC,EAAE,YAAY;MACpD,MAAM8B,UAAU,GAAG,IAAInB,KAAK,CAAC,aAAa,CAAC;MAC3C,MAAMoB,YAAY,GAAG,IAAIpB,KAAK,CAAC,gBAAgB,CAAC;MAChD,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CACjBiB,qBAAqB,CAACQ,UAAU,CAAC,CACjCR,qBAAqB,CAACS,YAAY,CAAC;MAEtC,MAAMC,WAAW,GAAG5B,IAAI,CAACC,EAAE,CAACK,KAAK,IAAIA,KAAK,KAAKoB,UAAU,CAAC;MAE1D,MAAMZ,OAAO,GAAGtB,gBAAgB,CAACS,EAAE,EAAE;QACnCsB,UAAU,EAAE,CAAC;QACbH,YAAY,EAAE,GAAG;QACjBQ;MACF,CAAC,CAAC;;MAEF;MACA5B,IAAI,CAACqB,mBAAmB,CAAC,GAAG,CAAC;MAE7B,MAAMlB,MAAM,CAACW,OAAO,CAAC,CAACU,OAAO,CAACC,OAAO,CAACE,YAAY,CAAC;MACnDxB,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;MACnCd,MAAM,CAACyB,WAAW,CAAC,CAACX,qBAAqB,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtB,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BC,EAAE,CAAC,yDAAyD,EAAE,YAAY;MACxE,MAAMK,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACY,iBAAiB,CAAC,SAAS,CAAC;MAEjD,MAAMgB,WAAW,GAAGpC,cAAc,CAACQ,EAAE,CAAC;MAEtC,MAAME,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACd,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;MACpDb,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFrB,EAAE,CAAC,+EAA+E,EAAE,YAAY;MAC9F,MAAMU,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MACrC,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACqB,iBAAiB,CAAChB,KAAK,CAAC;MAE7C,MAAMuB,WAAW,GAAGpC,cAAc,CAACQ,EAAE,EAAE;QAAE6B,gBAAgB,EAAE;MAAE,CAAC,CAAC;MAE/D,MAAM3B,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACnB,KAAK,CAAC;MAClDH,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFrB,EAAE,CAAC,kDAAkD,EAAE,YAAY;MACjE,MAAMU,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MACrC,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CAACqB,iBAAiB,CAAChB,KAAK,CAAC;MAE7C,MAAMuB,WAAW,GAAGpC,cAAc,CAACQ,EAAE,EAAE;QAAE6B,gBAAgB,EAAE;MAAE,CAAC,CAAC;;MAE/D;MACA,MAAM3B,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACnB,KAAK,CAAC;;MAElD;MACA,MAAMH,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACnB,KAAK,CAAC;;MAElD;MACA,MAAMH,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAE9DtB,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFrB,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D,MAAMU,KAAK,GAAG,IAAIC,KAAK,CAAC,YAAY,CAAC;MACrC,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CACjBiB,qBAAqB,CAACZ,KAAK,CAAC,CAC5Ba,qBAAqB,CAAC,SAAS,CAAC,CAChCD,qBAAqB,CAACZ,KAAK,CAAC;MAE/B,MAAMuB,WAAW,GAAGpC,cAAc,CAACQ,EAAE,EAAE;QAAE6B,gBAAgB,EAAE;MAAE,CAAC,CAAC;;MAE/D;MACA,MAAM3B,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACnB,KAAK,CAAC;;MAElD;MACA,MAAMH,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACd,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;;MAEpD;MACA,MAAMb,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACnB,KAAK,CAAC;MAElDH,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEFrB,EAAE,CAAC,mCAAmC,EAAE,YAAY;MAClD,MAAM8B,UAAU,GAAG,IAAInB,KAAK,CAAC,aAAa,CAAC;MAC3C,MAAMoB,YAAY,GAAG,IAAIpB,KAAK,CAAC,gBAAgB,CAAC;MAEhD,MAAMN,EAAE,GAAGD,IAAI,CAACC,EAAE,CAAC,CAAC,CACjBiB,qBAAqB,CAACQ,UAAU,CAAC,CACjCR,qBAAqB,CAACQ,UAAU,CAAC,CACjCR,qBAAqB,CAACS,YAAY,CAAC;MAEtC,MAAMI,SAAS,GAAG/B,IAAI,CAACC,EAAE,CAACK,KAAK,IAAIA,KAAK,KAAKoB,UAAU,CAAC;MAExD,MAAMG,WAAW,GAAGpC,cAAc,CAACQ,EAAE,EAAE;QACrC6B,gBAAgB,EAAE,CAAC;QACnBC;MACF,CAAC,CAAC;;MAEF;MACA,MAAM5B,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACC,UAAU,CAAC;;MAEvD;MACA,MAAMvB,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAACC,UAAU,CAAC;;MAEvD;MACA,MAAMvB,MAAM,CAAC0B,WAAW,CAAC,CAAC,CAAC,CAACL,OAAO,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAE9DtB,MAAM,CAACF,EAAE,CAAC,CAACgB,qBAAqB,CAAC,CAAC,CAAC;MACnCd,MAAM,CAAC4B,SAAS,CAAC,CAACd,qBAAqB,CAAC,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}