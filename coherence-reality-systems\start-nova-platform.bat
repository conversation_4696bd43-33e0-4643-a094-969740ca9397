@echo off
title Nova Agent Platform Console Integration
color 0A

echo ================================================
echo        NOVA AGENT PLATFORM CONSOLE
echo        NovaFuse Coherence Operating System
echo ================================================
echo.

echo [1/3] Building Nova Agent with WebSocket...
cd /d "%~dp0"

REM Build Nova Agent
"C:\Program Files\Go\bin\go.exe" mod tidy
"C:\Program Files\Go\bin\go.exe" build -o nova-agent-api.exe nova-agent.go

if not exist nova-agent-api.exe (
    echo ❌ Nova Agent build failed!
    pause
    exit /b 1
)

echo ✅ Nova Agent built successfully!
echo.

echo [2/3] Installing Dashboard Dependencies...
cd nova-agent-dashboard
if not exist node_modules (
    echo Installing npm packages...
    npm install
)

echo [3/3] Starting Platform Components...
echo.

echo 🚀 Starting Nova Agent API Server (Port 8080)...
start "Nova Agent API" cmd /k "cd /d %~dp0 && nova-agent-api.exe"

timeout /t 3 /nobreak >nul

echo 🎛️ Starting Dashboard (Port 3001)...
start "Nova Dashboard" cmd /k "cd /d %~dp0\nova-agent-dashboard && npm run dev"

echo.
echo ================================================
echo           PLATFORM CONSOLE READY!
echo ================================================
echo.
echo 📡 Nova Agent API:  http://localhost:8080
echo 🔌 WebSocket:       ws://localhost:8080/ws  
echo 🎛️ Dashboard:       http://localhost:3001
echo.
echo ✅ Platform Console Integration Complete!
echo.
echo Press any key to open dashboard in browser...
pause >nul

start http://localhost:3001

echo.
echo Platform is running. Close this window to stop all services.
pause
