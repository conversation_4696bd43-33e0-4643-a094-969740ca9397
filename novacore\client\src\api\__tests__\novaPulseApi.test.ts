/**
 * Tests for NovaPulse API Client
 */

import { NovaPulseAPI } from '../novaPulseApi';
import axiosInstance from '../axiosConfig';
import authService from '../authService';

// Mock axios instance
jest.mock('../axiosConfig', () => ({
  __esModule: true,
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    defaults: {
      baseURL: '/api/v1'
    }
  }
}));

// Mock auth service
jest.mock('../authService', () => ({
  __esModule: true,
  default: {
    getOrganizationId: jest.fn(),
    getToken: jest.fn()
  }
}));

describe('NovaPulseAPI', () => {
  let api: NovaPulseAPI;
  
  beforeEach(() => {
    jest.clearAllMocks();
    api = new NovaPulseAPI();
  });
  
  describe('constructor', () => {
    it('should use the default base URL', () => {
      expect(axiosInstance.defaults.baseURL).toBe('/api/v1');
    });
    
    it('should override the base URL if provided', () => {
      const customApi = new NovaPulseAPI('/custom/api');
      expect(axiosInstance.defaults.baseURL).toBe('/custom/api');
    });
  });
  
  describe('getOrganizationId', () => {
    it('should return organization ID from auth service', () => {
      (authService.getOrganizationId as jest.Mock).mockReturnValue('org123');
      expect(api.getOrganizationId()).toBe('org123');
    });
    
    it('should throw error if organization ID is not available', () => {
      (authService.getOrganizationId as jest.Mock).mockReturnValue(null);
      expect(() => api.getOrganizationId()).toThrow('Organization ID not available');
    });
  });
  
  describe('getRegulations', () => {
    it('should fetch regulations', async () => {
      const mockResponse = {
        data: {
          data: [{ _id: 'reg1', name: 'GDPR' }],
          pagination: { total: 1, page: 1, limit: 10 }
        }
      };
      
      (axiosInstance.get as jest.Mock).mockResolvedValue(mockResponse);
      
      const result = await api.getRegulations();
      
      expect(axiosInstance.get).toHaveBeenCalledWith('/regulations', { params: undefined });
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should pass query parameters', async () => {
      const mockResponse = {
        data: {
          data: [{ _id: 'reg1', name: 'GDPR' }],
          pagination: { total: 1, page: 1, limit: 10 }
        }
      };
      
      (axiosInstance.get as jest.Mock).mockResolvedValue(mockResponse);
      
      const params = { page: 1, limit: 10, category: 'privacy' };
      await api.getRegulations(params);
      
      expect(axiosInstance.get).toHaveBeenCalledWith('/regulations', { params });
    });
  });
  
  describe('getComplianceStatusData', () => {
    it('should fetch compliance status data with provided organization ID', async () => {
      const mockProfilesResponse = {
        data: {
          data: [{
            _id: 'profile1',
            applicableFrameworks: [
              { frameworkName: 'GDPR', complianceStatus: { score: 85 } },
              { frameworkName: 'HIPAA', complianceStatus: { score: 70 } }
            ]
          }]
        }
      };
      
      (axiosInstance.get as jest.Mock).mockResolvedValue(mockProfilesResponse);
      
      const result = await api.getComplianceStatusData('org123');
      
      expect(axiosInstance.get).toHaveBeenCalledWith('/organizations/org123/compliance-profiles', { params: undefined });
      expect(result).toEqual([
        { framework: 'GDPR', compliant: 85, gap: 15 },
        { framework: 'HIPAA', compliant: 70, gap: 30 }
      ]);
    });
    
    it('should fetch compliance status data with organization ID from auth service', async () => {
      const mockProfilesResponse = {
        data: {
          data: [{
            _id: 'profile1',
            applicableFrameworks: [
              { frameworkName: 'GDPR', complianceStatus: { score: 85 } }
            ]
          }]
        }
      };
      
      (axiosInstance.get as jest.Mock).mockResolvedValue(mockProfilesResponse);
      (authService.getOrganizationId as jest.Mock).mockReturnValue('org456');
      
      await api.getComplianceStatusData();
      
      expect(axiosInstance.get).toHaveBeenCalledWith('/organizations/org456/compliance-profiles', { params: undefined });
    });
    
    it('should return empty array when no profiles exist', async () => {
      const mockProfilesResponse = {
        data: {
          data: []
        }
      };
      
      (axiosInstance.get as jest.Mock).mockResolvedValue(mockProfilesResponse);
      
      const result = await api.getComplianceStatusData('org123');
      
      expect(result).toEqual([]);
    });
  });
  
  describe('getRegulatoryChangeData', () => {
    it('should fetch and process regulatory change data', async () => {
      const mockPendingResponse = { data: { data: [
        { category: 'privacy', impactAssessment: { level: 'high' } }
      ]}};
      
      const mockOverdueResponse = { data: { data: [
        { category: 'security', impactAssessment: { level: 'medium' } }
      ]}};
      
      const mockUpcomingResponse = { data: { data: [
        { category: 'privacy', impactAssessment: { level: 'low' } }
      ]}};
      
      (axiosInstance.get as jest.Mock)
        .mockResolvedValueOnce(mockPendingResponse)
        .mockResolvedValueOnce(mockOverdueResponse)
        .mockResolvedValueOnce(mockUpcomingResponse);
      
      const result = await api.getRegulatoryChangeData('org123');
      
      expect(axiosInstance.get).toHaveBeenCalledWith('/organizations/org123/regulatory-changes/pending');
      expect(axiosInstance.get).toHaveBeenCalledWith('/organizations/org123/regulatory-changes/overdue');
      expect(axiosInstance.get).toHaveBeenCalledWith('/organizations/org123/regulatory-changes/upcoming', { params: { days: 30 } });
      
      expect(result).toEqual({
        byImpact: { high: 1, medium: 1, low: 1 },
        byStatus: { pending: 1, overdue: 1, upcoming: 1 },
        byCategory: [
          { category: 'privacy', count: 2 },
          { category: 'security', count: 1 }
        ]
      });
    });
    
    it('should use organization ID from auth service if not provided', async () => {
      const mockResponse = { data: { data: [] }};
      
      (axiosInstance.get as jest.Mock).mockResolvedValue(mockResponse);
      (authService.getOrganizationId as jest.Mock).mockReturnValue('org789');
      
      await api.getRegulatoryChangeData();
      
      expect(axiosInstance.get).toHaveBeenCalledWith('/organizations/org789/regulatory-changes/pending');
    });
  });
});
