# NovaCaia Enterprise Production Dockerfile
# AI Governance Engine - Production Ready Container

FROM node:18-alpine AS node-base

# Install Python and build dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    python3-dev \
    build-base \
    curl \
    bash

# Create app directory
WORKDIR /app

# Copy CASTL™ components first (for better caching)
COPY coherence-reality-systems/nhetx-castl-alpha/ ./castl/

# Set up Node.js environment for CASTL™
WORKDIR /app/castl
RUN npm init -y && \
    npm install --production

# Create package.json for proper module resolution
RUN echo '{\
  "name": "nhetx-castl-alpha",\
  "version": "1.0.0",\
  "description": "NHET-X CASTL Alpha Components",\
  "main": "nhetx-castl-unified.js",\
  "type": "commonjs",\
  "engines": {"node": ">=18.0.0"},\
  "exports": {\
    "./ners": "./ners-castl-enhanced.js",\
    "./nepi": "./nepi-castl-enhanced.js",\
    "./nefc": "./nefc-castl-enhanced.js",\
    "./unified": "./nhetx-castl-unified.js"\
  }\
}' > package.json

# Create index.js for unified access
RUN echo 'module.exports = {\
  NERS: require("./ners-castl-enhanced.js"),\
  NEPI: require("./nepi-castl-enhanced.js"),\
  NEFC: require("./nefc-castl-enhanced.js"),\
  Unified: require("./nhetx-castl-unified.js")\
};' > index.js

# Copy NovaCaia application
WORKDIR /app
COPY src/novacaia/ ./novacaia/

# Install Python dependencies using Alpine packages and virtual environment
WORKDIR /app/novacaia
RUN apk add --no-cache py3-aiohttp py3-pip && \
    python3 -m venv /opt/venv && \
    . /opt/venv/bin/activate && \
    pip install --no-cache-dir aiohttp uvloop

# Set up virtual environment in PATH
ENV PATH="/opt/venv/bin:$PATH"

# Create production configuration
RUN echo '{\
  "environment": "production",\
  "platform_allocation": 18.0,\
  "enterprise_retention": 82.0,\
  "consciousness_threshold": 0.91,\
  "accuracy_target": 0.9783,\
  "processing_timeout": 30,\
  "max_concurrent": 1000000,\
  "security": {\
    "encryption": "AES-256",\
    "audit_logging": true,\
    "rate_limiting": true\
  }\
}' > production-config.json

# Create health check script
RUN echo '#!/usr/bin/env python3\
import asyncio\
import sys\
import json\
from novacaia_enterprise import NovaCaia\
\
async def health_check():\
    try:\
        novacaia = NovaCaia()\
        activation = await novacaia.activate()\
        if activation["success"]:\
            result = await novacaia.process_ai_input({\
                "text": "Health check test",\
                "context": "system_validation"\
            })\
            if result["success"] and result["consciousness_score"]["score"] >= 0.91:\
                print(json.dumps({\
                    "status": "operational",\
                    "consciousness_score": result["consciousness_score"]["score"],\
                    "processing_time_ms": result["processing_time_ms"],\
                    "boundary_enforced": result["boundary_enforced"]\
                }))\
                return 0\
        print(json.dumps({"status": "failed", "error": "Health check failed"}))\
        return 1\
    except Exception as e:\
        print(json.dumps({"status": "error", "error": str(e)}))\
        return 1\
\
if __name__ == "__main__":\
    exit_code = asyncio.run(health_check())\
    sys.exit(exit_code)\
' > health_check.py && chmod +x health_check.py

# Create nova-validate script
RUN echo '#!/usr/bin/env python3\
import asyncio\
import sys\
import json\
import argparse\
from novacaia_enterprise import NovaCaia\
\
async def validate_component(component):\
    novacaia = NovaCaia()\
    await novacaia.activate()\
    \
    if component == "ners":\
        result = novacaia.ners.validateConsciousness({"test": "ners_validation"})\
        return {"component": "NERS", "valid": result["valid"], "score": result.get("sentience_score", 0)}\
    elif component == "nepi":\
        result = novacaia.nepi.evolveTruth({"text": "Truth validation test"})\
        return {"component": "NEPI", "valid": True, "score": result.get("truth_coherence", 0)}\
    elif component == "nefc":\
        result = novacaia.nefc.calculate_financial_coherence(100, {"mandatory": 10, "performance": 8})\
        return {"component": "NEFC", "valid": True, "score": result.get("coherence_score", 0)}\
    elif component == "all":\
        ners = await validate_component("ners")\
        nepi = await validate_component("nepi")\
        nefc = await validate_component("nefc")\
        return {"all_components": [ners, nepi, nefc], "overall_valid": True}\
    else:\
        return {"error": f"Unknown component: {component}"}\
\
def main():\
    parser = argparse.ArgumentParser()\
    parser.add_argument("--component", default="all", help="Component to validate")\
    args = parser.parse_args()\
    \
    result = asyncio.run(validate_component(args.component))\
    print(json.dumps(result, indent=2))\
    return 0 if result.get("valid") or result.get("overall_valid") else 1\
\
if __name__ == "__main__":\
    sys.exit(main())\
' > nova-validate && chmod +x nova-validate

# Create simple HTTP server for testing
RUN echo '#!/usr/bin/env python3\
import asyncio\
import json\
from aiohttp import web, web_request\
from novacaia_enterprise import NovaCaia\
\
class NovaServer:\
    def __init__(self):\
        self.novacaia = None\
        \
    async def init_novacaia(self):\
        self.novacaia = NovaCaia()\
        await self.novacaia.activate()\
        \
    async def health(self, request):\
        if not self.novacaia:\
            await self.init_novacaia()\
        \
        result = await self.novacaia.process_ai_input({\
            "text": "Health check",\
            "context": "api_health"\
        })\
        \
        return web.json_response({\
            "status": "operational" if result["success"] else "failed",\
            "consciousness_score": result.get("consciousness_score", {}).get("score", 0),\
            "processing_time_ms": result.get("processing_time_ms", 0),\
            "boundary_enforced": result.get("boundary_enforced", False)\
        })\
        \
    async def validate(self, request):\
        if not self.novacaia:\
            await self.init_novacaia()\
            \
        data = await request.json()\
        result = await self.novacaia.process_ai_input(data)\
        \
        return web.json_response({\
            "truth": result.get("truth_processing", {}).get("truth_coherence", 0),\
            "consciousness": result.get("consciousness_score", {}).get("score", 0),\
            "boundary_enforced": result.get("boundary_enforced", False),\
            "action": "processed" if result["success"] else "quarantined"\
        })\
        \
    async def economics(self, request):\
        if not self.novacaia:\
            await self.init_novacaia()\
            \
        return web.json_response({\
            "platform_allocation": 18.0,\
            "enterprise_retention": 82.0,\
            "entropy_tax": 0.0,\
            "optimization_active": True\
        })\
\
async def create_app():\
    server = NovaServer()\
    app = web.Application()\
    app.router.add_get("/health", server.health)\
    app.router.add_post("/validate", server.validate)\
    app.router.add_get("/economics", server.economics)\
    return app\
\
if __name__ == "__main__":\
    app = asyncio.run(create_app())\
    web.run_app(app, host="0.0.0.0", port=7777)\
' > nova-server.py && chmod +x nova-server.py

# Set environment variables
ENV PYTHONPATH=/app
ENV CASTL_PATH=/app/castl
ENV NODE_ENV=production
ENV PLATFORM_ALLOCATION=18.0
ENV ENTERPRISE_RETENTION=82.0

# Create non-root user for security
RUN addgroup -g 1001 -S novacaia && \
    adduser -S novacaia -u 1001 -G novacaia

# Create log directory
RUN mkdir -p /var/log/nova && \
    chown -R novacaia:novacaia /var/log/nova /app

# Switch to non-root user
USER novacaia

# Expose port
EXPOSE 7777

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD python3 health_check.py || exit 1

# Default command
CMD ["python3", "nova-server.py"]
