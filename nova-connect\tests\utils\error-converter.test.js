/**
 * NovaFuse Universal API Connector - Error Converter Tests
 * 
 * This module tests the error converter utilities for the UAC.
 */

const { 
  convertAxiosError, 
  convertJsonSchemaError, 
  convertError 
} = require('../../src/utils/error-converter');

const {
  UAConnectorError,
  TimeoutError,
  NetworkError,
  InvalidCredentialsError,
  AuthenticationError,
  ResourceNotFoundError,
  RateLimitExceededError,
  BadRequestError,
  ServerError,
  ValidationError
} = require('../../src/errors');

describe('Error Converter', () => {
  describe('convertAxiosError', () => {
    it('should return the error if it is already a UAConnectorError', () => {
      const error = new UAConnectorError('Test error');
      
      const result = convertAxiosError(error);
      
      expect(result).toBe(error);
    });
    
    it('should convert timeout error', () => {
      const error = {
        code: 'ECONNABORTED',
        message: 'timeout of 1000ms exceeded',
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(TimeoutError);
      expect(result.message).toBe('Request timed out');
      expect(result.cause).toBe(error);
      expect(result.context.request.url).toBe('https://api.example.com');
    });
    
    it('should convert network error', () => {
      const error = {
        code: 'ENOTFOUND',
        message: 'getaddrinfo ENOTFOUND api.example.com',
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(NetworkError);
      expect(result.message).toBe('Network error: ENOTFOUND');
      expect(result.cause).toBe(error);
    });
    
    it('should convert 401 Unauthorized error', () => {
      const error = {
        response: {
          status: 401,
          data: { message: 'Unauthorized' }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(InvalidCredentialsError);
      expect(result.statusCode).toBe(401);
      expect(result.response).toEqual({ message: 'Unauthorized' });
    });
    
    it('should convert 403 Forbidden error', () => {
      const error = {
        response: {
          status: 403,
          data: { message: 'Forbidden' }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(AuthenticationError);
      expect(result.code).toBe('AUTH_FORBIDDEN');
      expect(result.statusCode).toBe(403);
    });
    
    it('should convert 404 Not Found error', () => {
      const error = {
        response: {
          status: 404,
          data: { message: 'Not Found' }
        },
        config: {
          url: 'https://api.example.com/users/123',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(ResourceNotFoundError);
      expect(result.statusCode).toBe(404);
      expect(result.resourceId).toBe('https://api.example.com/users/123');
    });
    
    it('should convert 429 Too Many Requests error', () => {
      const error = {
        response: {
          status: 429,
          data: { message: 'Too Many Requests' },
          headers: {
            'retry-after': '60'
          }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(RateLimitExceededError);
      expect(result.statusCode).toBe(429);
      expect(result.retryAfter).toBe(60);
    });
    
    it('should convert 400 Bad Request error', () => {
      const error = {
        response: {
          status: 400,
          data: { message: 'Bad Request' }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(BadRequestError);
      expect(result.statusCode).toBe(400);
    });
    
    it('should convert 500 Server Error', () => {
      const error = {
        response: {
          status: 500,
          data: { message: 'Internal Server Error' }
        },
        config: {
          url: 'https://api.example.com',
          method: 'get'
        }
      };
      
      const result = convertAxiosError(error);
      
      expect(result).toBeInstanceOf(ServerError);
      expect(result.statusCode).toBe(500);
    });
  });
  
  describe('convertJsonSchemaError', () => {
    it('should return the error if it is already a UAConnectorError', () => {
      const error = new UAConnectorError('Test error');
      
      const result = convertJsonSchemaError(error, 'TestSchema');
      
      expect(result).toBe(error);
    });
    
    it('should convert JSON Schema validation error', () => {
      const error = {
        errors: [
          {
            keyword: 'required',
            dataPath: '.user',
            schemaPath: '#/properties/user/required',
            message: 'should have required property \'name\''
          },
          {
            keyword: 'format',
            dataPath: '.user.email',
            schemaPath: '#/properties/user/properties/email/format',
            message: 'should match format "email"'
          }
        ]
      };
      
      const result = convertJsonSchemaError(error, 'UserSchema');
      
      expect(result).toBeInstanceOf(ValidationError);
      expect(result.code).toBe('VALIDATION_SCHEMA_ERROR');
      expect(result.validationErrors.length).toBe(2);
      expect(result.validationErrors[0].field).toBe('.user');
      expect(result.validationErrors[0].code).toBe('required');
    });
  });
  
  describe('convertError', () => {
    it('should return the error if it is already a UAConnectorError', () => {
      const error = new UAConnectorError('Test error');
      
      const result = convertError(error);
      
      expect(result).toBe(error);
    });
    
    it('should convert Axios error', () => {
      const error = {
        isAxiosError: true,
        response: {
          status: 404,
          data: { message: 'Not Found' }
        },
        config: {
          url: 'https://api.example.com/users/123',
          method: 'get'
        }
      };
      
      const result = convertError(error);
      
      expect(result).toBeInstanceOf(ResourceNotFoundError);
    });
    
    it('should convert JSON Schema validation error', () => {
      const error = {
        errors: [
          {
            keyword: 'required',
            dataPath: '.user',
            message: 'should have required property \'name\''
          }
        ]
      };
      
      const result = convertError(error, { schemaName: 'UserSchema' });
      
      expect(result).toBeInstanceOf(ValidationError);
    });
    
    it('should convert generic error to UAConnectorError', () => {
      const error = new Error('Generic error');
      
      const result = convertError(error);
      
      expect(result).toBeInstanceOf(UAConnectorError);
      expect(result.message).toBe('Generic error');
      expect(result.cause).toBe(error);
    });
  });
});
