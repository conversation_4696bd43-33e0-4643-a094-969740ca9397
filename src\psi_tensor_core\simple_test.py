#!/usr/bin/env python3
"""
Simple Test Script for Ψ Tensor Core Concept

This script demonstrates the core concepts of the Ψ Tensor Core using simple
NumPy operations instead of PyTorch.
"""

import numpy as np
import json
import time

# Constants
PI_103 = 3141.59  # π103 scaling factor

def sigmoid(x):
    """Simple sigmoid function"""
    return 1 / (1 + np.exp(-x))

def create_sample_data():
    """Create sample data for CSDE, CSFE, and CSME engines."""
    # Sample CSDE data
    csde_data = {
        "governance": 0.75,  # High governance score
        "data": 0.85,        # High data quality score
        "action": "Implement enhanced access controls",
        "confidence": 0.9    # High confidence
    }
    
    # Sample CSFE data
    csfe_data = {
        "risk": 0.65,        # Moderate risk score
        "finance": 0.70,     # Moderate financial impact score
        "action": "Allocate additional budget to security",
        "confidence": 0.8    # High confidence
    }
    
    # Sample CSME data
    csme_data = {
        "bio": 0.40,         # Low biological risk score
        "med_compliance": 0.90,  # High medical compliance score
        "action": "Update patient data protection protocols",
        "confidence": 0.85   # High confidence
    }
    
    return csde_data, csfe_data, csme_data

def create_tensor(data, tensor_type):
    """Create a tensor from data"""
    if tensor_type == "CSDE":
        return np.array([
            data["governance"],
            data["data"],
            hash(data["action"]) % 1000 / 1000,  # Simple hash-based embedding
            data["confidence"]
        ])
    elif tensor_type == "CSFE":
        return np.array([
            data["risk"],
            data["finance"],
            hash(data["action"]) % 1000 / 1000,  # Simple hash-based embedding
            data["confidence"]
        ])
    elif tensor_type == "CSME":
        return np.array([
            data["bio"],
            data["med_compliance"],
            hash(data["action"]) % 1000 / 1000,  # Simple hash-based embedding
            data["confidence"]
        ])
    else:
        raise ValueError(f"Unknown tensor type: {tensor_type}")

def apply_dynamic_weighting(csde_tensor, csfe_tensor, csme_tensor):
    """Apply dynamic weighting using 18/82 principle"""
    # Extract components
    G = csde_tensor[0]
    D = csde_tensor[1]
    R = csfe_tensor[0]
    phi = csfe_tensor[1]
    B = csme_tensor[0]
    gamma = csme_tensor[1]
    
    # Apply 18/82 principle
    w_CSDE_raw = 0.18 * G + 0.82 * D
    w_CSFE_raw = 0.18 * R + 0.82 * phi
    w_CSME_raw = 0.18 * B + 0.82 * gamma
    
    # Apply sigmoid function for normalization
    w_CSDE = sigmoid(w_CSDE_raw)
    w_CSFE = sigmoid(w_CSFE_raw)
    w_CSME = sigmoid(w_CSME_raw)
    
    # Normalize weights to sum to 1
    total = w_CSDE + w_CSFE + w_CSME
    w_CSDE /= total
    w_CSFE /= total
    w_CSME /= total
    
    return w_CSDE, w_CSFE, w_CSME

def tensor_product(tensor_a, tensor_b):
    """Implement tensor product using NumPy's kron"""
    return np.kron(tensor_a, tensor_b)

def direct_sum(tensor_a, tensor_b):
    """Implement direct sum using NumPy's concatenate"""
    return np.concatenate([tensor_a.flatten(), tensor_b.flatten()])

def fuse_engines(csde_tensor, csfe_tensor, csme_tensor):
    """Fuse engines using the equation: Ψ_fused = (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)"""
    # Apply tensor product: (Ψ_CSDE ⊗ Ψ_CSFE)
    csde_csfe_product = tensor_product(csde_tensor, csfe_tensor)
    
    # Apply scaling: (Ψ_CSME * π103)
    csme_scaled = csme_tensor * PI_103
    
    # Apply direct sum: (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)
    fused_tensor = direct_sum(csde_csfe_product, csme_scaled)
    
    return fused_tensor

def quantum_consensus(actions, confidences):
    """Simulate quantum consensus"""
    # Create simple embeddings
    embeddings = [hash(action) % 1000 / 1000 for action in actions]
    
    # Weight by confidence
    weighted_embeddings = [emb * conf for emb, conf in zip(embeddings, confidences)]
    
    # Find dominant action
    dominant_idx = np.argmax(weighted_embeddings)
    consensus_confidence = weighted_embeddings[dominant_idx] / sum(weighted_embeddings)
    
    return actions[dominant_idx], consensus_confidence

def main():
    """Main function"""
    print("=== Simple Ψ Tensor Core Demonstration ===")
    
    try:
        # Create sample data
        print("\nCreating sample data...")
        csde_data, csfe_data, csme_data = create_sample_data()
        
        print("\nSample Data:")
        print(f"CSDE Data: {json.dumps(csde_data, indent=2)}")
        print(f"CSFE Data: {json.dumps(csfe_data, indent=2)}")
        print(f"CSME Data: {json.dumps(csme_data, indent=2)}")
        
        # Create tensors
        print("\n1. Creating tensors for each engine...")
        csde_tensor = create_tensor(csde_data, "CSDE")
        csfe_tensor = create_tensor(csfe_data, "CSFE")
        csme_tensor = create_tensor(csme_data, "CSME")
        
        print(f"CSDE Tensor: {csde_tensor}")
        print(f"CSFE Tensor: {csfe_tensor}")
        print(f"CSME Tensor: {csme_tensor}")
        
        # Apply dynamic weighting
        print("\n2. Applying dynamic weighting...")
        print("Using 18/82 principle:")
        print(f"CSDE: 0.18 * {csde_data['governance']:.2f} + 0.82 * {csde_data['data']:.2f}")
        print(f"CSFE: 0.18 * {csfe_data['risk']:.2f} + 0.82 * {csfe_data['finance']:.2f}")
        print(f"CSME: 0.18 * {csme_data['bio']:.2f} + 0.82 * {csme_data['med_compliance']:.2f}")
        
        w_CSDE, w_CSFE, w_CSME = apply_dynamic_weighting(csde_tensor, csfe_tensor, csme_tensor)
        
        print(f"CSDE Weight: {w_CSDE:.4f}")
        print(f"CSFE Weight: {w_CSFE:.4f}")
        print(f"CSME Weight: {w_CSME:.4f}")
        print(f"Dominant Engine: {'CSDE' if w_CSDE > max(w_CSFE, w_CSME) else 'CSFE' if w_CSFE > w_CSME else 'CSME'}")
        
        # Fuse engines
        print("\n3. Fusing engines...")
        print(f"Applying fusion equation: Ψ_fused = (Ψ_CSDE ⊗ Ψ_CSFE) ⊕ (Ψ_CSME * π103)")
        print(f"Where π103 = {PI_103}")
        
        start_time = time.time()
        
        # Step 1: Tensor product
        csde_csfe_product = tensor_product(csde_tensor, csfe_tensor)
        print(f"Tensor Product (Ψ_CSDE ⊗ Ψ_CSFE) Shape: {csde_csfe_product.shape}")
        
        # Step 2: Scaling
        csme_scaled = csme_tensor * PI_103
        print(f"Scaled CSME (Ψ_CSME * π103): {csme_scaled}")
        
        # Step 3: Direct sum
        fused_tensor = direct_sum(csde_csfe_product, csme_scaled)
        
        fusion_time = time.time() - start_time
        
        print(f"Fusion Time: {fusion_time:.4f} seconds")
        print(f"Fused Tensor Shape: {fused_tensor.shape}")
        print(f"Fused Tensor (first 10 elements): {fused_tensor[:10]}")
        
        # Reach consensus
        print("\n4. Reaching consensus on actions...")
        print(f"Actions proposed:")
        print(f"  CSDE: '{csde_data['action']}' (Confidence: {csde_data['confidence']:.2f})")
        print(f"  CSFE: '{csfe_data['action']}' (Confidence: {csfe_data['confidence']:.2f})")
        print(f"  CSME: '{csme_data['action']}' (Confidence: {csme_data['confidence']:.2f})")
        
        actions = [csde_data["action"], csfe_data["action"], csme_data["action"]]
        confidences = [csde_data["confidence"], csfe_data["confidence"], csme_data["confidence"]]
        
        consensus_action, consensus_confidence = quantum_consensus(actions, confidences)
        
        print(f"Consensus Action: {consensus_action}")
        print(f"Consensus Confidence: {consensus_confidence:.4f}")
        print(f"Execute Action: {consensus_confidence > 0.82}")
        
        print("\n=== Demonstration Complete ===")
    
    except Exception as e:
        print(f"\nError during demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
