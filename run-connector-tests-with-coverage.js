/**
 * <PERSON><PERSON><PERSON> to run connector tests with coverage
 */

const { execSync } = require('child_process');

try {
  console.log('Running connector tests with coverage...');
  execSync('npx jest tests/unit/connectors --coverage --testPathIgnorePatterns=tests/mocks', { stdio: 'inherit' });
  console.log('✅ Tests completed');
  process.exit(0);
} catch (error) {
  console.error('❌ Tests failed');
  process.exit(1);
}
