# NovaConnect UAC Dockerfile
# This Dockerfile builds the NovaConnect Universal API Connector

# Use Node.js LTS as the base image
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy the rest of the application
COPY . .

# Create production image using Google's distroless base image
FROM gcr.io/distroless/nodejs:18

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3001

# Copy from builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app ./

# Create necessary directories
COPY --from=builder /app/logs ./logs

# Expose port
EXPOSE 3001

# Health check
# Note: distroless doesn't have wget, using NODE_OPTIONS for health check
ENV NODE_OPTIONS="--require ./health-check.js"

# Start the application
CMD ["index.js"]
