/**
 * UCTO Dashboard Module
 * 
 * This module provides a unified dashboard for the Universal Compliance Tracking Optimizer.
 */

const DashboardManager = require('./core/dashboard-manager');
const DashboardAPI = require('./api/dashboard-api');
const DashboardIntegration = require('./core/dashboard-integration');
const dashboardUtils = require('./utils/dashboard-utils');
const dashboardSchema = require('./schema/dashboard-schema');

module.exports = {
  DashboardManager,
  DashboardAPI,
  DashboardIntegration,
  dashboardUtils,
  dashboardSchema
};
