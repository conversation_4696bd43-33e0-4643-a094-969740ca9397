import { useState } from 'react'
import { motion } from 'framer-motion'
import { SearchIcon } from '@heroicons/react/outline'

interface ProductScannerProps {
  onProductFound: (product: any) => void
}

export function ProductScanner({ onProductFound }: ProductScannerProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [scanning, setScanning] = useState(false)
  const [error, setError] = useState('')
  const [products, setProducts] = useState<any[]>([])

  const handleScan = async () => {
    setScanning(true)
    setError('')
    
    try {
      const response = await fetch('/api/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: searchTerm })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch product')
      }

      const product = await response.json()
      onProductFound(product)
      setProducts(prev => [...prev, product])
    } catch (err) {
      setError(err.message)
    } finally {
      setScanning(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="relative">
        <input
          type="text"
          placeholder="Enter product URL or keyword..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 bg-gray-50"
        />
        <button
          onClick={handleScan}
          className="absolute right-2 top-1/2 -translate-y-1/2 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
          disabled={scanning}
        >
          {scanning ? (
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 1, repeat: Infinity }}
              className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
            />
          ) : (
            <SearchIcon className="w-5 h-5" />
          )}
        </button>
      </div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4 bg-red-50 rounded-lg border border-red-200"
        >
          <p className="text-sm text-red-600">{error}</p>
        </motion.div>
      )}

      {products.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          {products.map((product, index) => (
            <div
              key={index}
              className="p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
            >
              <div className="flex items-center">
                <img
                  src={product.image}
                  alt={product.title}
                  className="w-16 h-16 rounded-lg mr-4"
                />
                <div>
                  <h3 className="font-semibold">{product.title}</h3>
                  <p className="text-gray-400">Price: ${product.price}</p>
                  <div className="mt-2 space-x-2">
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                      PSI: {product.psi.toFixed(1)}%
                    </span>
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                      PHI: {product.phi.toFixed(1)}%
                    </span>
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                      KAPPA: {product.kappa.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </motion.div>
      )}
    </div>
  )
}
