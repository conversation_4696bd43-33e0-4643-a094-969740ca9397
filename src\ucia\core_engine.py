"""
Core Compliance Engine for the Universal Compliance Intelligence Architecture (UCIA).

This module implements the foundational large language model fine-tuned for regulatory
and compliance contexts, capable of understanding and interpreting regulatory text
across multiple frameworks.
"""

import os
import logging
import json
from typing import Dict, List, Optional, Any, Tuple

import numpy as np
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, AutoModelForSequenceClassification

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CoreComplianceEngine:
    """
    Core Compliance Engine for the Universal Compliance Intelligence Architecture.

    This class implements the foundational language model that powers the UCIA,
    with specialized capabilities for understanding regulatory text and providing
    compliance guidance.
    """

    def __init__(self, model_name: str = "novafuse/regulator-base", use_gpu: bool = True,
                 concept_model_name: str = None, framework_model_name: str = None):
        """
        Initialize the Core Compliance Engine.

        Args:
            model_name: Name or path of the pre-trained model to use
            use_gpu: Whether to use GPU acceleration if available
            concept_model_name: Name or path of the concept detection model
            framework_model_name: Name or path of the framework detection model
        """
        self.model_name = model_name
        self.use_gpu = use_gpu
        self.device = "cuda" if use_gpu and self._is_gpu_available() else "cpu"

        logger.info(f"Initializing CoreComplianceEngine with model: {model_name} on {self.device}")

        # Load main model and tokenizer
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name).to(self.device)
            logger.info("Main model and tokenizer loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load main model or tokenizer: {e}")
            raise

        # Load concept detection model if provided
        self.concept_model = None
        self.concept_tokenizer = None
        if concept_model_name:
            try:
                self.concept_tokenizer = AutoTokenizer.from_pretrained(concept_model_name)
                self.concept_model = AutoModelForSequenceClassification.from_pretrained(concept_model_name).to(self.device)
                logger.info(f"Concept detection model loaded successfully: {concept_model_name}")
            except Exception as e:
                logger.warning(f"Failed to load concept detection model: {e}")

        # Load framework detection model if provided
        self.framework_model = None
        self.framework_tokenizer = None
        if framework_model_name:
            try:
                self.framework_tokenizer = AutoTokenizer.from_pretrained(framework_model_name)
                self.framework_model = AutoModelForSequenceClassification.from_pretrained(framework_model_name).to(self.device)
                logger.info(f"Framework detection model loaded successfully: {framework_model_name}")
            except Exception as e:
                logger.warning(f"Failed to load framework detection model: {e}")

        # Load concept and framework labels
        self.concept_labels = self._load_concept_labels()
        self.framework_labels = self._load_framework_labels()

        # Initialize the compliance context manager
        self.context_manager = ComplianceContextManager()

        # Initialize the query interpreter
        self.query_interpreter = QueryInterpreter(
            tokenizer=self.tokenizer,
            model=self.model,
            concept_model=self.concept_model,
            concept_tokenizer=self.concept_tokenizer,
            framework_model=self.framework_model,
            framework_tokenizer=self.framework_tokenizer,
            concept_labels=self.concept_labels,
            framework_labels=self.framework_labels
        )

        # Initialize the response generator
        self.response_generator = ResponseGenerator(self.tokenizer, self.model)

    def _is_gpu_available(self) -> bool:
        """Check if GPU is available for use."""
        try:
            return torch.cuda.is_available()
        except ImportError:
            return False

    def _load_concept_labels(self) -> List[str]:
        """Load concept labels for the concept detection model."""
        try:
            labels_path = os.path.join(os.path.dirname(__file__), 'data', 'concept_labels.json')
            with open(labels_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load concept labels: {e}")
            # Return default concept labels
            return [
                'data_breach', 'data_subject_rights', 'consent', 'data_processing',
                'security', 'governance', 'risk_assessment', 'documentation',
                'incident_response', 'vendor_management', 'training', 'audit'
            ]

    def _load_framework_labels(self) -> List[str]:
        """Load framework labels for the framework detection model."""
        try:
            labels_path = os.path.join(os.path.dirname(__file__), 'data', 'framework_labels.json')
            with open(labels_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.warning(f"Failed to load framework labels: {e}")
            # Return default framework labels
            return ['GDPR', 'HIPAA', 'SOC2', 'NIST', 'ISO27001', 'CCPA', 'PCI-DSS']

    def process_query(self, query: str, user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a compliance query and generate a response.

        Args:
            query: The user's compliance query
            user_context: Context information about the user and their organization

        Returns:
            A dictionary containing the response and metadata
        """
        logger.info(f"Processing query: {query}")

        # Update context with the new query
        context = self.context_manager.update_context(query, user_context)

        # Interpret the query
        interpreted_query = self.query_interpreter.interpret(query, context)

        # Generate response
        response = self.response_generator.generate(interpreted_query, context)

        # Log the interaction for future learning
        self._log_interaction(query, response, context)

        return response

    def _log_interaction(self, query: str, response: Dict[str, Any], context: Dict[str, Any]) -> None:
        """
        Log the interaction for future learning.

        Args:
            query: The user's query
            response: The system's response
            context: The context of the interaction
        """
        # TODO: Implement secure, privacy-preserving logging for adaptive learning
        pass


class ComplianceContextManager:
    """
    Manages the context of compliance conversations.

    This class maintains the state of ongoing compliance conversations,
    including user context, conversation history, and active compliance frameworks.
    """

    def __init__(self):
        """Initialize the Compliance Context Manager."""
        self.current_context = {}

    def update_context(self, query: str, user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Update the conversation context with a new query and user context.

        Args:
            query: The user's query
            user_context: Additional context about the user and their organization

        Returns:
            The updated context
        """
        # If user_context is provided, update the current context
        if user_context:
            self.current_context.update(user_context)

        # Add the query to the conversation history
        if 'conversation_history' not in self.current_context:
            self.current_context['conversation_history'] = []

        self.current_context['conversation_history'].append({
            'role': 'user',
            'content': query
        })

        # TODO: Extract entities and intents from the query to enrich the context

        return self.current_context


class QueryInterpreter:
    """
    Interprets user queries and maps them to relevant regulatory concepts.

    This class is responsible for understanding the user's question and
    mapping it to relevant regulatory concepts across frameworks.
    """

    def __init__(self, tokenizer, model, concept_model=None, concept_tokenizer=None,
                 framework_model=None, framework_tokenizer=None, concept_labels=None, framework_labels=None):
        """
        Initialize the Query Interpreter.

        Args:
            tokenizer: The tokenizer for the language model
            model: The language model
            concept_model: Optional model for concept detection
            concept_tokenizer: Optional tokenizer for concept detection
            framework_model: Optional model for framework detection
            framework_tokenizer: Optional tokenizer for framework detection
            concept_labels: List of concept labels
            framework_labels: List of framework labels
        """
        self.tokenizer = tokenizer
        self.model = model
        self.concept_model = concept_model
        self.concept_tokenizer = concept_tokenizer
        self.framework_model = framework_model
        self.framework_tokenizer = framework_tokenizer
        self.concept_labels = concept_labels or []
        self.framework_labels = framework_labels or []

    def interpret(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Interpret a user query and map it to relevant regulatory concepts.

        Args:
            query: The user's query
            context: The conversation context

        Returns:
            A dictionary containing the interpreted query and relevant concepts
        """
        # Use the language model to interpret the query
        interpreted_query = self._interpret_query(query, context)

        # Detect frameworks using the framework detection model if available
        detected_frameworks = self._detect_frameworks(query)

        # Detect concepts using the concept detection model if available
        detected_concepts = self._detect_concepts(query)

        # Determine the query type
        query_type = self._determine_query_type(query)

        # Calculate confidence score
        confidence = 0.8  # Placeholder confidence score

        return {
            'original_query': query,
            'interpreted_query': interpreted_query,
            'detected_frameworks': detected_frameworks,
            'detected_concepts': detected_concepts,
            'query_type': query_type,
            'confidence': confidence
        }

    def _interpret_query(self, query: str, context: Dict[str, Any]) -> str:
        """
        Use the language model to interpret the query.

        Args:
            query: The user's query
            context: The conversation context

        Returns:
            The interpreted query
        """
        # For now, return the original query
        # In a full implementation, this would use the language model to rephrase or clarify the query
        return query

    def _detect_frameworks(self, query: str) -> List[str]:
        """
        Detect which regulatory frameworks are relevant to the query.

        Args:
            query: The user's query

        Returns:
            A list of relevant framework identifiers
        """
        # Use the framework detection model if available
        if self.framework_model and self.framework_tokenizer and self.framework_labels:
            try:
                inputs = self.framework_tokenizer(query, return_tensors="pt", truncation=True, padding=True)
                with torch.no_grad():
                    outputs = self.framework_model(**inputs)

                # Get predictions
                logits = outputs.logits
                probabilities = torch.sigmoid(logits).squeeze().cpu().numpy()

                # Select frameworks with probability above threshold
                threshold = 0.5
                detected_frameworks = [self.framework_labels[i] for i, prob in enumerate(probabilities) if prob > threshold]

                # If no frameworks are detected, use the most likely one
                if not detected_frameworks and len(probabilities) > 0:
                    most_likely_idx = probabilities.argmax()
                    detected_frameworks = [self.framework_labels[most_likely_idx]]

                return detected_frameworks
            except Exception as e:
                logger.warning(f"Framework detection model failed: {e}")
                # Fall back to keyword matching

        # Fall back to keyword matching
        frameworks = []
        query_lower = query.lower()
        if 'gdpr' in query_lower or 'data protection' in query_lower or 'eu' in query_lower:
            frameworks.append('GDPR')
        if 'hipaa' in query_lower or 'health' in query_lower or 'phi' in query_lower or 'medical' in query_lower:
            frameworks.append('HIPAA')
        if 'soc' in query_lower or 'service organization' in query_lower or 'aicpa' in query_lower:
            frameworks.append('SOC2')
        if 'nist' in query_lower or 'national institute' in query_lower:
            frameworks.append('NIST')
        if 'iso' in query_lower or '27001' in query_lower:
            frameworks.append('ISO27001')
        if 'ccpa' in query_lower or 'california' in query_lower or 'consumer privacy' in query_lower:
            frameworks.append('CCPA')
        if 'pci' in query_lower or 'payment card' in query_lower or 'credit card' in query_lower:
            frameworks.append('PCI-DSS')

        # If no specific framework is detected, assume a general compliance query
        if not frameworks:
            frameworks.append('GENERAL')

        return frameworks

    def _detect_concepts(self, query: str) -> List[str]:
        """
        Detect which compliance concepts are relevant to the query.

        Args:
            query: The user's query

        Returns:
            A list of relevant concept identifiers
        """
        # Use the concept detection model if available
        if self.concept_model and self.concept_tokenizer and self.concept_labels:
            try:
                inputs = self.concept_tokenizer(query, return_tensors="pt", truncation=True, padding=True)
                with torch.no_grad():
                    outputs = self.concept_model(**inputs)

                # Get predictions
                logits = outputs.logits
                probabilities = torch.sigmoid(logits).squeeze().cpu().numpy()

                # Select concepts with probability above threshold
                threshold = 0.5
                detected_concepts = [self.concept_labels[i] for i, prob in enumerate(probabilities) if prob > threshold]

                # If no concepts are detected, use the most likely one
                if not detected_concepts and len(probabilities) > 0:
                    most_likely_idx = probabilities.argmax()
                    detected_concepts = [self.concept_labels[most_likely_idx]]

                return detected_concepts
            except Exception as e:
                logger.warning(f"Concept detection model failed: {e}")
                # Fall back to keyword matching

        # Fall back to keyword matching
        concepts = []
        query_lower = query.lower()

        # Data subject rights
        if ('right' in query_lower or 'access' in query_lower or 'erasure' in query_lower or
            'rectification' in query_lower or 'portability' in query_lower or 'subject' in query_lower):
            concepts.append('data_subject_rights')

        # Consent
        if 'consent' in query_lower or 'permission' in query_lower or 'opt' in query_lower:
            concepts.append('consent')

        # Data breach
        if ('breach' in query_lower or 'incident' in query_lower or 'violation' in query_lower or
            'compromise' in query_lower or 'leak' in query_lower):
            concepts.append('data_breach')

        # Security
        if ('security' in query_lower or 'protect' in query_lower or 'safeguard' in query_lower or
            'encrypt' in query_lower or 'access control' in query_lower):
            concepts.append('security')

        # Data processing
        if ('process' in query_lower or 'collect' in query_lower or 'store' in query_lower or
            'transfer' in query_lower or 'share' in query_lower):
            concepts.append('data_processing')

        # Governance
        if ('governance' in query_lower or 'policy' in query_lower or 'procedure' in query_lower or
            'management' in query_lower or 'oversight' in query_lower):
            concepts.append('governance')

        # Risk assessment
        if ('risk' in query_lower or 'assessment' in query_lower or 'impact' in query_lower or
            'analysis' in query_lower or 'evaluation' in query_lower):
            concepts.append('risk_assessment')

        # Documentation
        if ('document' in query_lower or 'record' in query_lower or 'evidence' in query_lower or
            'log' in query_lower or 'report' in query_lower):
            concepts.append('documentation')

        return concepts

    def _determine_query_type(self, query: str) -> str:
        """
        Determine the type of query (e.g., question, request for guidance, etc.).

        Args:
            query: The user's query

        Returns:
            The query type
        """
        query_lower = query.lower()

        # Question
        if (query_lower.startswith('what') or query_lower.startswith('how') or
            query_lower.startswith('why') or query_lower.startswith('when') or
            query_lower.startswith('where') or query_lower.startswith('who') or
            query_lower.startswith('which') or '?' in query):
            return 'QUESTION'

        # Guidance request
        if ('should' in query_lower or 'can' in query_lower or 'could' in query_lower or
            'would' in query_lower or 'need to' in query_lower or 'have to' in query_lower or
            'must' in query_lower or 'advise' in query_lower or 'recommend' in query_lower):
            return 'GUIDANCE_REQUEST'

        # Help request
        if ('help' in query_lower or 'assist' in query_lower or 'support' in query_lower or
            'guide' in query_lower or 'explain' in query_lower):
            return 'HELP_REQUEST'

        # Compliance check
        if ('compliant' in query_lower or 'compliance' in query_lower or 'conform' in query_lower or
            'meet' in query_lower or 'satisfy' in query_lower or 'fulfill' in query_lower):
            return 'COMPLIANCE_CHECK'

        return 'GENERAL_QUERY'


class ResponseGenerator:
    """
    Generates responses to compliance queries.

    This class is responsible for producing accurate, contextually appropriate
    compliance guidance based on the interpreted query and conversation context.
    """

    def __init__(self, tokenizer, model):
        """
        Initialize the Response Generator.

        Args:
            tokenizer: The tokenizer for the language model
            model: The language model
        """
        self.tokenizer = tokenizer
        self.model = model

    def generate(self, interpreted_query: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a response to the interpreted query.

        Args:
            interpreted_query: The interpreted user query
            context: The conversation context

        Returns:
            A dictionary containing the response and metadata
        """
        # TODO: Implement response generation using the language model

        # For now, return a placeholder response
        response_text = f"I understand you're asking about {', '.join(interpreted_query['detected_frameworks'])}. "
        response_text += "This is a placeholder response. In the full implementation, this would be generated by the language model."

        return {
            'response_text': response_text,
            'confidence': 0.8,  # Placeholder confidence score
            'citations': self._generate_citations(interpreted_query),
            'frameworks': interpreted_query['detected_frameworks'],
            'concepts': interpreted_query['detected_concepts'],
            'follow_up_questions': self._generate_follow_up_questions(interpreted_query)
        }

    def _generate_citations(self, interpreted_query: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Generate citations for the response.

        Args:
            interpreted_query: The interpreted user query

        Returns:
            A list of citation dictionaries
        """
        # TODO: Implement citation generation
        # For now, return placeholder citations
        citations = []
        for framework in interpreted_query['detected_frameworks']:
            if framework == 'GDPR':
                citations.append({
                    'framework': 'GDPR',
                    'reference': 'Article 5',
                    'text': 'Principles relating to processing of personal data',
                    'url': 'https://gdpr-info.eu/art-5-gdpr/'
                })
            elif framework == 'HIPAA':
                citations.append({
                    'framework': 'HIPAA',
                    'reference': '45 CFR § 164.306',
                    'text': 'Security standards: General rules',
                    'url': 'https://www.law.cornell.edu/cfr/text/45/164.306'
                })
            elif framework == 'SOC2':
                citations.append({
                    'framework': 'SOC2',
                    'reference': 'CC1.1',
                    'text': 'The entity demonstrates a commitment to integrity and ethical values',
                    'url': 'https://www.aicpa.org/interestareas/frc/assuranceadvisoryservices/sorhome.html'
                })

        return citations

    def _generate_follow_up_questions(self, interpreted_query: Dict[str, Any]) -> List[str]:
        """
        Generate follow-up questions for the response.

        Args:
            interpreted_query: The interpreted user query

        Returns:
            A list of follow-up questions
        """
        # TODO: Implement follow-up question generation
        # For now, return placeholder follow-up questions
        follow_ups = [
            "Would you like more specific guidance on this topic?",
            "Are you interested in how this applies to your specific industry?",
            "Do you need help implementing these requirements?"
        ]

        return follow_ups


if __name__ == "__main__":
    # Simple test of the Core Compliance Engine
    engine = CoreComplianceEngine(model_name="gpt2")  # Use a small model for testing
    response = engine.process_query("What are the GDPR requirements for data breach notification?")
    print(f"Query: What are the GDPR requirements for data breach notification?")
    print(f"Response: {response['response_text']}")
    print(f"Citations: {response['citations']}")
    print(f"Follow-up questions: {response['follow_up_questions']}")
