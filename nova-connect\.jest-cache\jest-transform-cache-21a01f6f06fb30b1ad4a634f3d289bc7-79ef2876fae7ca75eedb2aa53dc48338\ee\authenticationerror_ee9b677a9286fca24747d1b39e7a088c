385d2989e46314ea79ea8c962dfce93b
/**
 * NovaFuse Universal API Connector - Authentication Error
 * 
 * This module defines authentication-related errors for the UAC.
 */

const UAConnectorError = require('./base-error');

/**
 * Error class for authentication failures
 * @class AuthenticationError
 * @extends UAConnectorError
 */
class AuthenticationError extends UAConnectorError {
  /**
   * Create a new AuthenticationError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   * @param {string} options.code - Error code
   * @param {string} options.severity - Error severity
   * @param {Object} options.context - Additional context for the error
   * @param {Error} options.cause - The error that caused this error
   */
  constructor(message, options = {}) {
    super(message, {
      code: options.code || 'AUTH_ERROR',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. Please check your credentials and try again.';
  }
}

/**
 * Error class for missing credentials
 * @class MissingCredentialsError
 * @extends AuthenticationError
 */
class MissingCredentialsError extends AuthenticationError {
  /**
   * Create a new MissingCredentialsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Required credentials are missing', options = {}) {
    super(message, {
      code: options.code || 'AUTH_MISSING_CREDENTIALS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. Required credentials are missing.';
  }
}

/**
 * Error class for invalid credentials
 * @class InvalidCredentialsError
 * @extends AuthenticationError
 */
class InvalidCredentialsError extends AuthenticationError {
  /**
   * Create a new InvalidCredentialsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The provided credentials are invalid', options = {}) {
    super(message, {
      code: options.code || 'AUTH_INVALID_CREDENTIALS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. The provided credentials are invalid.';
  }
}

/**
 * Error class for expired credentials
 * @class ExpiredCredentialsError
 * @extends AuthenticationError
 */
class ExpiredCredentialsError extends AuthenticationError {
  /**
   * Create a new ExpiredCredentialsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'The credentials have expired', options = {}) {
    super(message, {
      code: options.code || 'AUTH_EXPIRED_CREDENTIALS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. Your credentials have expired. Please refresh your credentials and try again.';
  }
}

/**
 * Error class for insufficient permissions
 * @class InsufficientPermissionsError
 * @extends AuthenticationError
 */
class InsufficientPermissionsError extends AuthenticationError {
  /**
   * Create a new InsufficientPermissionsError
   * 
   * @param {string} message - Error message
   * @param {Object} options - Error options
   */
  constructor(message = 'Insufficient permissions to perform this operation', options = {}) {
    super(message, {
      code: options.code || 'AUTH_INSUFFICIENT_PERMISSIONS',
      severity: options.severity || 'error',
      context: options.context || {},
      cause: options.cause
    });
  }

  /**
   * Get a user-friendly error message
   * 
   * @returns {string} - User-friendly error message
   */
  getUserMessage() {
    return 'Authentication failed. You do not have sufficient permissions to perform this operation.';
  }
}
module.exports = {
  AuthenticationError,
  MissingCredentialsError,
  InvalidCredentialsError,
  ExpiredCredentialsError,
  InsufficientPermissionsError
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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