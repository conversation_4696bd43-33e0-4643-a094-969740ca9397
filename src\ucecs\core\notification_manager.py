"""
Notification Manager for the Universal Compliance Evidence Collection System.

This module provides functionality for sending notifications about system events.
"""

import os
import json
import logging
import datetime
from enum import Enum
from typing import Dict, List, Any, Optional, Set, Union

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class NotificationType(Enum):
    """Types of notifications."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class NotificationManager:
    """
    Manager for sending notifications about system events.
    
    This class is responsible for sending notifications about important events
    in the system, such as evidence validation failures, new evidence, etc.
    """
    
    def __init__(self, notifications_dir: Optional[str] = None):
        """
        Initialize the Notification Manager.
        
        Args:
            notifications_dir: Path to a directory for storing notification logs
        """
        logger.info("Initializing Notification Manager")
        
        # Set the notifications directory
        self.notifications_dir = notifications_dir or os.path.join(os.getcwd(), 'notifications')
        
        # Create the notifications directory if it doesn't exist
        os.makedirs(self.notifications_dir, exist_ok=True)
        
        # Set the notification log file path
        self.notification_log_path = os.path.join(self.notifications_dir, 'notification_log.json')
        
        # Initialize the notification log
        self.notification_log = self._load_notification_log()
        
        logger.info("Notification Manager initialized")
    
    def _load_notification_log(self) -> List[Dict[str, Any]]:
        """
        Load the notification log from disk.
        
        Returns:
            List of notification entries
        """
        if os.path.exists(self.notification_log_path):
            try:
                with open(self.notification_log_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Failed to load notification log: {e}")
        
        return []
    
    def _save_notification_log(self) -> None:
        """Save the notification log to disk."""
        try:
            with open(self.notification_log_path, 'w', encoding='utf-8') as f:
                json.dump(self.notification_log, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save notification log: {e}")
    
    def send_notification(self, 
                        message: str, 
                        notification_type: NotificationType = NotificationType.INFO,
                        subject: Optional[str] = None,
                        details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Send a notification.
        
        Args:
            message: The notification message
            notification_type: The type of notification
            subject: The notification subject
            details: Additional details about the notification
            
        Returns:
            The notification entry
        """
        # Create the notification entry
        timestamp = datetime.datetime.now(datetime.timezone.utc).isoformat()
        notification = {
            'timestamp': timestamp,
            'type': notification_type.value,
            'subject': subject or 'UCECS Notification',
            'message': message,
            'details': details or {}
        }
        
        # Log the notification
        self._log_notification(notification)
        
        # Display the notification in the console
        self._display_notification(notification)
        
        return notification
    
    def _log_notification(self, notification: Dict[str, Any]) -> None:
        """
        Log a notification to the notification log.
        
        Args:
            notification: The notification entry
        """
        # Add the notification to the log
        self.notification_log.append(notification)
        
        # Limit the log size to 1000 entries
        if len(self.notification_log) > 1000:
            self.notification_log = self.notification_log[-1000:]
        
        # Save the log
        self._save_notification_log()
    
    def _display_notification(self, notification: Dict[str, Any]) -> None:
        """
        Display a notification in the console.
        
        Args:
            notification: The notification entry
        """
        notification_type = notification['type']
        subject = notification['subject']
        message = notification['message']
        
        if notification_type == NotificationType.INFO.value:
            logger.info(f"{subject}: {message}")
        elif notification_type == NotificationType.WARNING.value:
            logger.warning(f"{subject}: {message}")
        elif notification_type == NotificationType.ERROR.value:
            logger.error(f"{subject}: {message}")
    
    def get_notifications(self, 
                        limit: int = 100, 
                        notification_type: Optional[NotificationType] = None) -> List[Dict[str, Any]]:
        """
        Get recent notifications.
        
        Args:
            limit: Maximum number of notifications to return
            notification_type: Filter by notification type
            
        Returns:
            List of notification entries
        """
        # Filter by type if specified
        if notification_type:
            filtered_notifications = [
                n for n in self.notification_log if n['type'] == notification_type.value
            ]
        else:
            filtered_notifications = self.notification_log
        
        # Return the most recent notifications
        return filtered_notifications[-limit:]
    
    def clear_notifications(self) -> None:
        """Clear all notifications from the log."""
        self.notification_log = []
        self._save_notification_log()
