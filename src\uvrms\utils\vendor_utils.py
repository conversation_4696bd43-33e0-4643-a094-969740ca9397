"""
Vendor utilities for the Universal Vendor Risk Management System.

This module provides utility functions for working with vendor information.
"""

import json
import logging
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_vendor_data(file_path: str) -> Dict[str, Any]:
    """
    Load vendor data from a JSON file.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        The vendor data
        
    Raises:
        FileNotFoundError: If the file does not exist
        json.JSONDecodeError: If the file is not valid JSON
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            vendor_data = json.load(f)
        
        logger.info(f"Loaded vendor data from {file_path}")
        return vendor_data
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except json.JSONDecodeError:
        logger.error(f"Invalid JSON in file: {file_path}")
        raise

def save_vendor_data(vendor_data: Dict[str, Any], file_path: str) -> None:
    """
    Save vendor data to a JSON file.
    
    Args:
        vendor_data: The vendor data
        file_path: Path to the output JSON file
        
    Raises:
        IOError: If the file cannot be written
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(vendor_data, f, indent=2)
        
        logger.info(f"Saved vendor data to {file_path}")
    except IOError:
        logger.error(f"Failed to save vendor data to {file_path}")
        raise

def validate_vendor_data(vendor_data: Dict[str, Any]) -> List[str]:
    """
    Validate vendor data.
    
    Args:
        vendor_data: The vendor data to validate
        
    Returns:
        List of validation errors, empty if valid
    """
    errors = []
    
    # Check required fields
    required_fields = ['name', 'contact_info']
    for field in required_fields:
        if field not in vendor_data:
            errors.append(f"Missing required field: {field}")
    
    # If any required fields are missing, return early
    if errors:
        return errors
    
    # Validate contact_info
    if not isinstance(vendor_data['contact_info'], dict):
        errors.append("Contact info must be a dictionary")
    else:
        # Check required contact_info fields
        contact_info = vendor_data['contact_info']
        required_contact_fields = ['email']
        for field in required_contact_fields:
            if field not in contact_info:
                errors.append(f"Missing required contact info field: {field}")
    
    return errors

def calculate_vendor_risk_score(vendor_data: Dict[str, Any], assessments: List[Dict[str, Any]], risks: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate a risk score for a vendor.
    
    Args:
        vendor_data: The vendor data
        assessments: List of assessments for the vendor
        risks: List of risks for the vendor
        
    Returns:
        The risk score information
    """
    logger.info(f"Calculating risk score for vendor: {vendor_data.get('id')}")
    
    # Initialize risk score components
    assessment_score = 0
    risk_score = 0
    
    # Calculate assessment score
    if assessments:
        total_questions = 0
        total_positive_answers = 0
        
        for assessment in assessments:
            if assessment.get('status') == 'completed':
                for section in assessment.get('sections', []):
                    for question in section.get('questions', []):
                        total_questions += 1
                        
                        if question.get('answer') == 'yes':
                            total_positive_answers += 1
        
        if total_questions > 0:
            assessment_score = (total_positive_answers / total_questions) * 100
    
    # Calculate risk score
    if risks:
        risk_levels = {
            'low': 1,
            'medium': 2,
            'high': 3
        }
        
        total_risk_level = 0
        
        for risk in risks:
            risk_level = risk.get('risk_level', 'medium')
            total_risk_level += risk_levels.get(risk_level, 2)
        
        # Normalize risk score to 0-100 scale (higher is worse)
        # Assuming a maximum of 10 high risks (30 points)
        risk_score = min(100, (total_risk_level / 30) * 100)
    
    # Calculate overall score (higher is better)
    # Assessment score is positive (higher is better)
    # Risk score is negative (higher is worse)
    overall_score = max(0, min(100, assessment_score - risk_score * 0.5))
    
    # Determine risk level
    if overall_score >= 80:
        risk_level = 'low'
    elif overall_score >= 50:
        risk_level = 'medium'
    else:
        risk_level = 'high'
    
    # Create the risk score object
    risk_score_obj = {
        'vendor_id': vendor_data.get('id'),
        'assessment_score': assessment_score,
        'risk_score': risk_score,
        'overall_score': overall_score,
        'risk_level': risk_level,
        'timestamp': _get_current_timestamp()
    }
    
    logger.info(f"Risk score calculated for vendor: {vendor_data.get('id')}")
    
    return risk_score_obj

def categorize_vendors(vendors: List[Dict[str, Any]], risk_scores: Dict[str, Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Categorize vendors by risk level.
    
    Args:
        vendors: List of vendors
        risk_scores: Dictionary of risk scores by vendor ID
        
    Returns:
        Dictionary of vendors categorized by risk level
    """
    logger.info("Categorizing vendors by risk level")
    
    # Initialize categories
    categories = {
        'high': [],
        'medium': [],
        'low': []
    }
    
    # Categorize vendors
    for vendor in vendors:
        vendor_id = vendor.get('id')
        
        if vendor_id in risk_scores:
            risk_level = risk_scores[vendor_id].get('risk_level', 'medium')
            categories[risk_level].append(vendor)
        else:
            # If no risk score, default to medium
            categories['medium'].append(vendor)
    
    logger.info(f"Categorized {len(vendors)} vendors by risk level")
    
    return categories

def filter_vendors_by_category(vendors: List[Dict[str, Any]], category: str) -> List[Dict[str, Any]]:
    """
    Filter vendors by category.
    
    Args:
        vendors: List of vendors
        category: The category to filter by
        
    Returns:
        List of vendors in the specified category
    """
    logger.info(f"Filtering vendors by category: {category}")
    
    return [v for v in vendors if category in v.get('categories', [])]

def filter_vendors_by_service(vendors: List[Dict[str, Any]], service: str) -> List[Dict[str, Any]]:
    """
    Filter vendors by service.
    
    Args:
        vendors: List of vendors
        service: The service to filter by
        
    Returns:
        List of vendors providing the specified service
    """
    logger.info(f"Filtering vendors by service: {service}")
    
    return [v for v in vendors if service in v.get('services', [])]

def filter_vendors_by_status(vendors: List[Dict[str, Any]], status: str) -> List[Dict[str, Any]]:
    """
    Filter vendors by status.
    
    Args:
        vendors: List of vendors
        status: The status to filter by
        
    Returns:
        List of vendors with the specified status
    """
    logger.info(f"Filtering vendors by status: {status}")
    
    return [v for v in vendors if v.get('status') == status]

def _get_current_timestamp() -> str:
    """
    Get the current timestamp.
    
    Returns:
        The current timestamp as a string
    """
    import datetime
    return datetime.datetime.now().isoformat()
