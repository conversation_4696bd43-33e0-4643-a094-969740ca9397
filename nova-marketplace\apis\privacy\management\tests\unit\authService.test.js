/**
 * Authentication Service Tests
 * 
 * This file contains unit tests for the authentication service.
 */

const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const authService = require('../../services/authService');

// Mock bcrypt and jsonwebtoken
jest.mock('bcryptjs');
jest.mock('jsonwebtoken');

describe('Authentication Service', () => {
  // Reset mocks before each test
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('authenticate', () => {
    it('should authenticate a user with valid credentials', async () => {
      // Mock bcrypt.compare to return true
      bcrypt.compare.mockResolvedValue(true);
      
      // Mock jwt.sign to return a token
      jwt.sign.mockReturnValue('mock-token');
      
      // Call the authenticate function
      const result = await authService.authenticate('admin', 'password');
      
      // Check that bcrypt.compare was called with the correct arguments
      expect(bcrypt.compare).toHaveBeenCalledWith('password', expect.any(String));
      
      // Check that jwt.sign was called with the correct arguments
      expect(jwt.sign).toHaveBeenCalledWith(
        expect.objectContaining({
          sub: expect.any(String),
          username: 'admin',
          role: expect.any(String)
        }),
        expect.any(String),
        expect.objectContaining({
          expiresIn: expect.any(String)
        })
      );
      
      // Check the result
      expect(result).toEqual({
        token: 'mock-token',
        expiresIn: expect.any(String),
        user: expect.objectContaining({
          id: expect.any(String),
          username: 'admin',
          role: expect.any(String)
        })
      });
    });
    
    it('should throw an error for a non-existent user', async () => {
      // Call the authenticate function with a non-existent user
      await expect(authService.authenticate('non-existent', 'password'))
        .rejects
        .toThrow('Invalid credentials');
    });
    
    it('should throw an error for invalid credentials', async () => {
      // Mock bcrypt.compare to return false
      bcrypt.compare.mockResolvedValue(false);
      
      // Call the authenticate function with invalid credentials
      await expect(authService.authenticate('admin', 'wrong-password'))
        .rejects
        .toThrow('Invalid credentials');
    });
  });
  
  describe('verifyToken', () => {
    it('should verify a valid token', async () => {
      // Mock jwt.verify to return a decoded token
      jwt.verify.mockReturnValue({
        sub: 'user-123',
        username: 'admin',
        role: 'admin'
      });
      
      // Call the verifyToken function
      const result = await authService.verifyToken('valid-token');
      
      // Check that jwt.verify was called with the correct arguments
      expect(jwt.verify).toHaveBeenCalledWith('valid-token', expect.any(String));
      
      // Check the result
      expect(result).toEqual({
        sub: 'user-123',
        username: 'admin',
        role: 'admin'
      });
    });
    
    it('should throw an error for an invalid token', async () => {
      // Mock jwt.verify to throw an error
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });
      
      // Call the verifyToken function with an invalid token
      await expect(authService.verifyToken('invalid-token'))
        .rejects
        .toThrow('Invalid token');
    });
  });
  
  describe('hashPassword', () => {
    it('should hash a password', async () => {
      // Mock bcrypt.genSalt to return a salt
      bcrypt.genSalt.mockResolvedValue('mock-salt');
      
      // Mock bcrypt.hash to return a hashed password
      bcrypt.hash.mockResolvedValue('hashed-password');
      
      // Call the hashPassword function
      const result = await authService.hashPassword('password');
      
      // Check that bcrypt.genSalt was called
      expect(bcrypt.genSalt).toHaveBeenCalledWith(10);
      
      // Check that bcrypt.hash was called with the correct arguments
      expect(bcrypt.hash).toHaveBeenCalledWith('password', 'mock-salt');
      
      // Check the result
      expect(result).toBe('hashed-password');
    });
  });
});
