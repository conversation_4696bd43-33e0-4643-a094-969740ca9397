{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 2, "numPassedTests": 15, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 15, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1753032899482, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 5, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1753032902755, "runtime": 2202, "slow": false, "start": 1753032900553}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\src\\novaascend\\simulation.test.js", "testResults": [{"ancestorTitles": [], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "Policy adaptation in NovaCortex", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "Policy adaptation in NovaCortex"}, {"ancestorTitles": [], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "System coherence under load", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "System coherence under load"}, {"ancestorTitles": [], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "Performance scaling in NovaLift", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "Performance scaling in NovaLift"}, {"ancestorTitles": [], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "Error handling robustness", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "Error handling robustness"}, {"ancestorTitles": [], "duration": 1, "failureDetails": [], "failureMessages": [], "fullName": "E2E workflow", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "E2E workflow"}], "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 10, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1753032905945, "runtime": 5376, "slow": true, "start": 1753032900569}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\src\\novaascend\\api.test.js", "testResults": [{"ancestorTitles": ["NovaAscend API"], "duration": 257, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should align NovaCortex via /decree", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should align NovaCortex via /decree"}, {"ancestorTitles": ["NovaAscend API"], "duration": 8, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should get vision from NovaCortex via /vision", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should get vision from NovaCortex via /vision"}, {"ancestorTitles": ["NovaAscend API"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should update policy via /firewall", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should update policy via /firewall"}, {"ancestorTitles": ["NovaAscend API"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should check system coherence via /coherence/check", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should check system coherence via /coherence/check"}, {"ancestorTitles": ["NovaAscend API"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should scale NovaLift via /lift/scale", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should scale NovaLift via /lift/scale"}, {"ancestorTitles": ["NovaAscend API"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should get NovaLift status via /lift/status", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should get NovaLift status via /lift/status"}, {"ancestorTitles": ["NovaAscend API"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should get NovaCaia status via /caia/status", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should get NovaCaia status via /caia/status"}, {"ancestorTitles": ["NovaAscend API"], "duration": 6, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should evaluate performance vs. compliance via /performance/evaluate", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should evaluate performance vs. compliance via /performance/evaluate"}, {"ancestorTitles": ["NovaAscend API"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should normalize telemetry data via /telemetry/normalize", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should normalize telemetry data via /telemetry/normalize"}, {"ancestorTitles": ["NovaAscend API"], "duration": 5, "failureDetails": [], "failureMessages": [], "fullName": "NovaAscend API should return 400 for invalid /decree request", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "should return 400 for invalid /decree request"}], "failureMessage": null}], "wasInterrupted": false}