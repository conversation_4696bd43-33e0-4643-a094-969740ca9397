/**
 * Monitoring Controller
 *
 * This controller handles API requests related to monitoring connectors.
 */

const MonitoringService = require('../services/MonitoringService');
const { ValidationError, NotFoundError } = require('../utils/errors');

class MonitoringController {
  constructor() {
    this.monitoringService = new MonitoringService();
  }

  /**
   * Get health status of all connectors
   */
  async getHealthStatus(req, res, next) {
    try {
      const healthStatus = await this.monitoringService.getHealthStatus();
      res.json(healthStatus);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get health status of a specific connector
   */
  async getConnectorHealth(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Connector ID is required');
      }

      const health = await this.monitoringService.getConnectorHealth(id);
      res.json(health);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Run health check for a connector
   */
  async runHealthCheck(req, res, next) {
    try {
      const { id } = req.params;
      const { credentialId } = req.body;

      if (!id) {
        throw new ValidationError('Connector ID is required');
      }

      if (!credentialId) {
        throw new ValidationError('Credential ID is required');
      }

      const result = await this.monitoringService.runHealthCheck(id, credentialId);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Get all alerts
   */
  async getAllAlerts(req, res, next) {
    try {
      const alerts = await this.monitoringService.getAllAlerts();
      res.json(alerts);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get alert by ID
   */
  async getAlertById(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Alert ID is required');
      }

      const alert = await this.monitoringService.getAlertById(id);
      res.json(alert);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Acknowledge alert
   */
  async acknowledgeAlert(req, res, next) {
    try {
      const { id } = req.params;
      const { user, comment } = req.body;

      if (!id) {
        throw new ValidationError('Alert ID is required');
      }

      if (!user) {
        throw new ValidationError('User is required');
      }

      const result = await this.monitoringService.acknowledgeAlert(id, user, comment);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Resolve alert
   */
  async resolveAlert(req, res, next) {
    try {
      const { id } = req.params;
      const { user, comment } = req.body;

      if (!id) {
        throw new ValidationError('Alert ID is required');
      }

      if (!user) {
        throw new ValidationError('User is required');
      }

      const result = await this.monitoringService.resolveAlert(id, user, comment);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Add comment to alert
   */
  async addAlertComment(req, res, next) {
    try {
      const { id } = req.params;
      const { user, comment } = req.body;

      if (!id) {
        throw new ValidationError('Alert ID is required');
      }

      if (!user) {
        throw new ValidationError('User is required');
      }

      if (!comment) {
        throw new ValidationError('Comment is required');
      }

      const result = await this.monitoringService.addAlertComment(id, user, comment);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Get alert configuration
   */
  async getAlertConfig(req, res, next) {
    try {
      const config = await this.monitoringService.getAlertConfig();
      res.json(config);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update alert configuration
   */
  async updateAlertConfig(req, res, next) {
    try {
      const config = req.body;

      if (!config) {
        throw new ValidationError('Configuration data is required');
      }

      const result = await this.monitoringService.updateAlertConfig(config);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get anomalies for a connector
   */
  async getConnectorAnomalies(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Connector ID is required');
      }

      const anomalies = await this.monitoringService.getConnectorAnomalies(id);
      res.json(anomalies);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all anomalies
   */
  async getAllAnomalies(req, res, next) {
    try {
      const anomalies = await this.monitoringService.getAllAnomalies();
      res.json(anomalies);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Detect anomalies for a connector
   */
  async detectAnomalies(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Connector ID is required');
      }

      const result = await this.monitoringService.detectAnomalies(id);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }
}

module.exports = new MonitoringController();
