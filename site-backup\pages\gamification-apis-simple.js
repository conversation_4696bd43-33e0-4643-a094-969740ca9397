import React from 'react';
import Sidebar from '../components/Sidebar';

export default function GamificationAPIsSimple() {
  const sidebarItems = [
    { type: 'category', label: 'Gamification APIs', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Achievement API', href: '#achievement-api' },
      { label: 'Points API', href: '#points-api' },
      { label: 'Integration Example', href: '#integration' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'API Documentation', href: '/api-docs' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Gamification APIs" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select 
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div id="overview" className="bg-secondary p-8 rounded-lg mb-8">
          <h2 className="text-3xl font-bold mb-4">Transform Compliance into Engagement</h2>
          <p className="text-xl mb-6">
            NovaFuse Gamification APIs turn compliance activities into engaging experiences that drive user adoption.
          </p>
        </div>
        
        <div className="mb-12">
          <h3 className="text-2xl font-bold mb-6">Core Gamification APIs</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
            {/* Achievement API */}
            <div id="achievement-api" className="bg-secondary p-6 rounded-lg">
              <h4 className="text-xl font-semibold mb-2">Achievement API</h4>
              <p className="text-gray-300 mb-4">Create, award, and track achievements for compliance activities.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Create a new achievement
POST /api/gamification/achievements
{
  "name": "Security Champion",
  "description": "Complete all security training",
  "points": 500
}`}
                </pre>
              </div>
            </div>
            
            {/* Points API */}
            <div id="points-api" className="bg-secondary p-6 rounded-lg">
              <h4 className="text-xl font-semibold mb-2">Points API</h4>
              <p className="text-gray-300 mb-4">Award points for compliance activities and track user progress.</p>
              
              <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono mb-4 text-sm">
                <pre className="text-green-400">
{`// Award points to a user
POST /api/gamification/points
{
  "user_id": "user-123",
  "points": 100,
  "reason": "Completed security review"
}`}
                </pre>
              </div>
            </div>
          </div>
          
          <h3 id="integration" className="text-2xl font-bold mb-6">Integration Example</h3>
          
          <div className="bg-secondary p-6 rounded-lg mb-6">
            <h4 className="text-xl font-semibold mb-2">Zapier Integration</h4>
            <p className="text-gray-300 mb-4">Automatically award points when users complete compliance tasks in other systems.</p>
            
            <div className="bg-[#0f172a] p-4 rounded-lg overflow-x-auto font-mono text-sm">
              <pre className="text-green-400">
{`// Zapier Trigger: When a user completes security training
// Zapier Action: Award points via NovaFuse API
POST /api/gamification/points
{
  "user_id": "{{user_id}}",
  "points": 100,
  "reason": "Completed security training"
}`}
              </pre>
            </div>
          </div>
          
          <div className="text-center mt-8">
            <a href="/partner-ecosystem" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              Back to Partner Ecosystem
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
