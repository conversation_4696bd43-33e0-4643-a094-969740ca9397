#!/bin/bash
# Script to set up monitoring for NovaConnect UAC

# Set variables
PROJECT_ID=${1:-"novafuse-test"}
CLUSTER_NAME=${2:-"novafuse-test-cluster"}
ZONE=${3:-"us-central1-a"}
NAMESPACE=${4:-"novafuse-monitoring"}

# Get credentials for the cluster
echo "Getting credentials for the cluster..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone $ZONE --project $PROJECT_ID

# Create namespace
echo "Creating namespace $NAMESPACE..."
kubectl create namespace $NAMESPACE || true

# Deploy Prometheus
echo "Deploying Prometheus..."
kubectl apply -f - <<EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: $NAMESPACE
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\\d+)?;(\\d+)
            replacement: \$1:\$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: $NAMESPACE
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
        - name: prometheus
          image: prom/prometheus:v2.30.3
          args:
            - "--config.file=/etc/prometheus/prometheus.yml"
            - "--storage.tsdb.path=/prometheus"
            - "--web.console.libraries=/usr/share/prometheus/console_libraries"
            - "--web.console.templates=/usr/share/prometheus/consoles"
          ports:
            - containerPort: 9090
          volumeMounts:
            - name: prometheus-config
              mountPath: /etc/prometheus
            - name: prometheus-storage
              mountPath: /prometheus
      volumes:
        - name: prometheus-config
          configMap:
            name: prometheus-config
        - name: prometheus-storage
          emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: $NAMESPACE
  labels:
    app: prometheus
spec:
  ports:
    - port: 9090
      targetPort: 9090
  selector:
    app: prometheus
EOF

# Deploy Grafana
echo "Deploying Grafana..."
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: $NAMESPACE
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
        - name: grafana
          image: grafana/grafana:8.2.2
          ports:
            - containerPort: 3000
          env:
            - name: GF_SECURITY_ADMIN_PASSWORD
              value: admin
            - name: GF_USERS_ALLOW_SIGN_UP
              value: "false"
          volumeMounts:
            - name: grafana-storage
              mountPath: /var/lib/grafana
      volumes:
        - name: grafana-storage
          emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: $NAMESPACE
  labels:
    app: grafana
spec:
  ports:
    - port: 3000
      targetPort: 3000
  selector:
    app: grafana
EOF

# Wait for Prometheus and Grafana to be ready
echo "Waiting for Prometheus and Grafana to be ready..."
kubectl rollout status deployment/prometheus -n $NAMESPACE
kubectl rollout status deployment/grafana -n $NAMESPACE

# Set up port forwarding for Grafana
echo "Setting up port forwarding for Grafana..."
kubectl port-forward service/grafana -n $NAMESPACE 3000:3000 &
GRAFANA_PID=$!

# Wait for Grafana to be accessible
echo "Waiting for Grafana to be accessible..."
until curl -s http://localhost:3000 > /dev/null; do
  echo "Waiting for Grafana..."
  sleep 5
done

# Add Prometheus as a data source
echo "Adding Prometheus as a data source..."
curl -s -X POST -H "Content-Type: application/json" -d '{
  "name": "Prometheus",
  "type": "prometheus",
  "url": "http://prometheus:9090",
  "access": "proxy",
  "basicAuth": false
}' *********************************/api/datasources

# Import the dashboard
echo "Importing the dashboard..."
curl -s -X POST -H "Content-Type: application/json" -d @monitoring/dashboards/marketplace-dashboard.json *********************************/api/dashboards/db

# Kill the port forwarding
echo "Killing port forwarding..."
kill $GRAFANA_PID

echo "Monitoring setup complete!"
