/**
 * OfflineCollaborationLobby Component
 * 
 * A component for creating and joining offline collaboration rooms.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useOfflineCollaboration } from '../collaboration/OfflineCollaborationContext';
import { useOffline } from '../offline/OfflineContext';
import { useTheme } from '../theme/ThemeContext';
import { useI18n } from '../i18n/I18nContext';
import { Animated } from './Animated';
import { FormattedDate } from './FormattedDate';

/**
 * OfflineCollaborationLobby component
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onJoinRoom - Callback when joining a room
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} OfflineCollaborationLobby component
 */
const OfflineCollaborationLobby = ({
  onJoinRoom,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { translate } = useI18n();
  const { isOnline, offlineMode } = useOffline();
  const {
    isConnected,
    isConnecting,
    connectionError,
    user,
    rooms,
    isLoading,
    pendingChanges,
    isSyncing,
    lastSyncTime,
    isOffline,
    connect,
    createRoom,
    joinRoom,
    syncPendingChanges
  } = useOfflineCollaboration();
  
  // State
  const [userName, setUserName] = useState('');
  const [roomName, setRoomName] = useState('');
  const [error, setError] = useState(null);
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [showOfflineIndicator, setShowOfflineIndicator] = useState(false);
  
  // Show offline indicator when offline
  useEffect(() => {
    setShowOfflineIndicator(!isOnline || offlineMode);
  }, [isOnline, offlineMode]);
  
  // Connect on mount
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      connect({ userName: userName || 'Guest' }).catch(err => {
        console.error('Error connecting:', err);
        setError(err.message || 'Failed to connect');
      });
    }
  }, [isConnected, isConnecting, connect, userName]);
  
  // Handle connect
  const handleConnect = async (event) => {
    event.preventDefault();
    
    if (!userName.trim()) {
      setError('Please enter a name');
      return;
    }
    
    try {
      await connect({ userName });
    } catch (err) {
      console.error('Error connecting:', err);
      setError(err.message || 'Failed to connect');
    }
  };
  
  // Handle create room
  const handleCreateRoom = async (event) => {
    event.preventDefault();
    
    if (!roomName.trim()) {
      setError('Please enter a room name');
      return;
    }
    
    try {
      const room = await createRoom({ name: roomName });
      setRoomName('');
      setShowCreateRoom(false);
      
      if (onJoinRoom) {
        onJoinRoom(room.id);
      }
    } catch (err) {
      console.error('Error creating room:', err);
      setError(err.message || 'Failed to create room');
    }
  };
  
  // Handle join room
  const handleJoinRoom = async (roomId) => {
    try {
      await joinRoom(roomId);
      
      if (onJoinRoom) {
        onJoinRoom(roomId);
      }
    } catch (err) {
      console.error('Error joining room:', err);
      setError(err.message || 'Failed to join room');
    }
  };
  
  // Handle sync
  const handleSync = async () => {
    try {
      await syncPendingChanges();
    } catch (err) {
      console.error('Error syncing changes:', err);
      setError(err.message || 'Failed to sync changes');
    }
  };
  
  // Format timestamp
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div
        className={`flex items-center justify-center p-8 ${className}`}
        style={style}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  // Render connect form
  if (!isConnected) {
    return (
      <div
        className={`p-8 ${className}`}
        style={style}
        data-testid="offline-collaboration-lobby-connect"
      >
        <div className="max-w-md mx-auto bg-surface rounded-lg shadow-lg overflow-hidden">
          <div className="bg-background p-4 border-b border-divider">
            <h2 className="text-xl font-bold text-textPrimary">
              {translate('collaboration.joinCollaboration', 'Join Collaboration')}
            </h2>
          </div>
          
          <div className="p-6">
            {connectionError && (
              <div className="bg-error bg-opacity-10 text-error p-3 rounded-md mb-4">
                {connectionError}
              </div>
            )}
            
            <form onSubmit={handleConnect}>
              <div className="mb-4">
                <label htmlFor="user-name" className="block text-sm font-medium text-textSecondary mb-1">
                  {translate('collaboration.yourName', 'Your Name')}
                </label>
                <input
                  type="text"
                  id="user-name"
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder={translate('collaboration.enterYourName', 'Enter your name')}
                  required
                />
              </div>
              
              <div className="flex justify-end">
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                  disabled={isConnecting}
                >
                  {isConnecting
                    ? translate('collaboration.connecting', 'Connecting...')
                    : translate('collaboration.connect', 'Connect')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }
  
  // Render room list
  return (
    <div
      className={`p-8 ${className}`}
      style={style}
      data-testid="offline-collaboration-lobby-rooms"
    >
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-textPrimary">
              {translate('collaboration.collaborationRooms', 'Collaboration Rooms')}
            </h2>
            <p className="text-textSecondary">
              {translate('collaboration.joinOrCreateRoom', 'Join an existing room or create a new one')}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Offline indicator */}
            {showOfflineIndicator && (
              <div className="flex items-center text-warning">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <span>{translate('collaboration.offline', 'Offline')}</span>
              </div>
            )}
            
            {/* Pending changes indicator */}
            {pendingChanges.length > 0 && (
              <button
                type="button"
                className="flex items-center text-primary"
                onClick={handleSync}
                disabled={isSyncing || isOffline}
              >
                {isSyncing ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="animate-spin h-5 w-5 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10" strokeOpacity="0.25" />
                    <path d="M12 2a10 10 0 0110 10" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                )}
                <span>
                  {isSyncing
                    ? translate('collaboration.syncing', 'Syncing...')
                    : translate('collaboration.pendingChanges', 'Sync {{count}} changes', { count: pendingChanges.length })}
                </span>
              </button>
            )}
            
            {/* Create room button */}
            <button
              type="button"
              className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
              onClick={() => setShowCreateRoom(true)}
            >
              {translate('collaboration.createRoom', 'Create Room')}
            </button>
          </div>
        </div>
        
        {/* Error message */}
        {error && (
          <div className="bg-error bg-opacity-10 text-error p-4 rounded-md mb-6">
            {error}
          </div>
        )}
        
        {/* Create room form */}
        {showCreateRoom && (
          <Animated
            animation="fadeIn"
            className="bg-surface rounded-lg shadow-lg overflow-hidden mb-6"
          >
            <div className="bg-background p-4 border-b border-divider">
              <h3 className="text-lg font-medium text-textPrimary">
                {translate('collaboration.createNewRoom', 'Create New Room')}
              </h3>
            </div>
            
            <div className="p-6">
              <form onSubmit={handleCreateRoom}>
                <div className="mb-4">
                  <label htmlFor="room-name" className="block text-sm font-medium text-textSecondary mb-1">
                    {translate('collaboration.roomName', 'Room Name')}
                  </label>
                  <input
                    type="text"
                    id="room-name"
                    className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
                    value={roomName}
                    onChange={(e) => setRoomName(e.target.value)}
                    placeholder={translate('collaboration.enterRoomName', 'Enter room name')}
                    required
                  />
                </div>
                
                <div className="flex justify-end space-x-2">
                  <button
                    type="button"
                    className="px-4 py-2 text-textSecondary hover:text-textPrimary transition-colors duration-200"
                    onClick={() => setShowCreateRoom(false)}
                  >
                    {translate('common.cancel', 'Cancel')}
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                  >
                    {translate('collaboration.createRoom', 'Create Room')}
                  </button>
                </div>
              </form>
            </div>
          </Animated>
        )}
        
        {/* Room list */}
        {rooms.length === 0 ? (
          <div className="bg-surface rounded-lg shadow-lg p-8 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-textSecondary mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <h3 className="text-lg font-medium text-textPrimary mb-2">
              {translate('collaboration.noRoomsAvailable', 'No Rooms Available')}
            </h3>
            <p className="text-textSecondary mb-4">
              {translate('collaboration.createRoomToStart', 'Create a room to start collaborating')}
            </p>
            <button
              type="button"
              className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
              onClick={() => setShowCreateRoom(true)}
            >
              {translate('collaboration.createRoom', 'Create Room')}
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {rooms.map(room => (
              <div
                key={room.id}
                className="bg-surface rounded-lg shadow-lg overflow-hidden"
              >
                <div className="p-6">
                  <h3 className="text-lg font-medium text-textPrimary mb-2">
                    {room.name}
                  </h3>
                  <div className="text-sm text-textSecondary mb-4">
                    <div>
                      {translate('collaboration.created', 'Created')}: <FormattedDate value={room.createdAt} />
                    </div>
                    <div>
                      {translate('collaboration.participants', 'Participants')}: {room.users.length}
                    </div>
                    {room.isOffline && (
                      <div className="text-warning mt-1">
                        {translate('collaboration.createdOffline', 'Created offline')}
                      </div>
                    )}
                  </div>
                  <button
                    type="button"
                    className="w-full px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
                    onClick={() => handleJoinRoom(room.id)}
                  >
                    {translate('collaboration.joinRoom', 'Join Room')}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

OfflineCollaborationLobby.propTypes = {
  onJoinRoom: PropTypes.func.isRequired,
  className: PropTypes.string,
  style: PropTypes.object
};

export default OfflineCollaborationLobby;
