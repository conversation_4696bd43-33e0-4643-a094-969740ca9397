/**
 * FeedbackDialog Component
 * 
 * A dialog for collecting user feedback on the NovaVision Hub.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useAuth } from '../auth';

/**
 * FeedbackDialog component
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when the dialog is closed
 * @param {string} [props.component='novavision-hub'] - The component to provide feedback on
 * @param {string} [props.type='general'] - The type of feedback
 * @param {Object} [props.metadata={}] - Additional metadata to include with the feedback
 * @param {Function} [props.onSubmit] - Function to call when feedback is submitted
 * @returns {React.ReactElement} FeedbackDialog component
 */
const FeedbackDialog = ({
  open,
  onClose,
  component = 'novavision-hub',
  type = 'general',
  metadata = {},
  onSubmit
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const { user } = useAuth();
  
  // State
  const [feedback, setFeedback] = useState({
    type,
    component,
    rating: 3,
    title: '',
    description: '',
    metadata,
    tags: []
  });
  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  
  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setFeedback({
        type,
        component,
        rating: 3,
        title: '',
        description: '',
        metadata,
        tags: []
      });
      setSubmitError(null);
      setSubmitSuccess(false);
    }
  }, [open, type, component, metadata]);
  
  // Handle input change
  const handleChange = (field, value) => {
    setFeedback(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle tag toggle
  const handleTagToggle = (tag) => {
    setFeedback(prev => {
      const tags = [...prev.tags];
      
      if (tags.includes(tag)) {
        return {
          ...prev,
          tags: tags.filter(t => t !== tag)
        };
      } else {
        return {
          ...prev,
          tags: [...tags, tag]
        };
      }
    });
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      setSubmitError(null);
      
      // Validate form
      if (!feedback.title.trim()) {
        throw new Error('Please provide a title');
      }
      
      if (!feedback.description.trim()) {
        throw new Error('Please provide a description');
      }
      
      // Add user info to metadata
      const enhancedMetadata = {
        ...feedback.metadata,
        userAgent: navigator.userAgent,
        screenSize: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        timestamp: new Date().toISOString()
      };
      
      // Prepare feedback data
      const feedbackData = {
        ...feedback,
        metadata: enhancedMetadata
      };
      
      // Submit feedback
      if (onSubmit) {
        await onSubmit(feedbackData);
      } else {
        // Default implementation using fetch
        const response = await fetch('/api/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(feedbackData)
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Error submitting feedback');
        }
      }
      
      // Show success message
      setSubmitSuccess(true);
      
      // Close dialog after a delay
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setSubmitError(error.message || 'Error submitting feedback');
    } finally {
      setSubmitting(false);
    }
  };
  
  // Feedback types
  const feedbackTypes = [
    { value: 'general', label: translate('feedback.types.general', 'General Feedback') },
    { value: 'feature', label: translate('feedback.types.feature', 'Feature Request') },
    { value: 'bug', label: translate('feedback.types.bug', 'Bug Report') },
    { value: 'suggestion', label: translate('feedback.types.suggestion', 'Suggestion') },
    { value: 'usability', label: translate('feedback.types.usability', 'Usability Issue') },
    { value: 'performance', label: translate('feedback.types.performance', 'Performance Issue') },
    { value: 'other', label: translate('feedback.types.other', 'Other') }
  ];
  
  // Common tags
  const commonTags = [
    'ui',
    'ux',
    'performance',
    'accessibility',
    'security',
    'documentation',
    'api',
    'mobile',
    'desktop'
  ];
  
  // If dialog is not open, don't render anything
  if (!open) {
    return null;
  }
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div 
        className="bg-background rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        role="dialog"
        aria-labelledby="feedback-dialog-title"
        aria-describedby="feedback-dialog-description"
      >
        {/* Header */}
        <div className="p-4 border-b border-divider flex justify-between items-center">
          <h2 
            id="feedback-dialog-title"
            className="text-xl font-semibold text-textPrimary"
          >
            {translate('feedback.title', 'Provide Feedback')}
          </h2>
          
          <button
            className="text-textSecondary hover:text-textPrimary"
            onClick={onClose}
            aria-label={translate('common.close', 'Close')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <p 
            id="feedback-dialog-description"
            className="text-textSecondary mb-6"
          >
            {translate('feedback.description', 'Your feedback helps us improve the NovaVision Hub. Please share your thoughts, suggestions, or report any issues you\'ve encountered.')}
          </p>
          
          {submitSuccess ? (
            <div className="bg-success bg-opacity-10 text-success p-4 rounded-md mb-4">
              <p className="font-semibold">
                {translate('feedback.submitSuccess', 'Thank you for your feedback!')}
              </p>
              <p>
                {translate('feedback.submitSuccessDescription', 'Your feedback has been submitted successfully and will help us improve the NovaVision Hub.')}
              </p>
            </div>
          ) : (
            <form className="space-y-6">
              {/* Feedback Type */}
              <div>
                <label 
                  htmlFor="feedback-type"
                  className="block text-textPrimary font-medium mb-2"
                >
                  {translate('feedback.typeLabel', 'Feedback Type')}
                </label>
                
                <select
                  id="feedback-type"
                  className="w-full p-2 border border-divider rounded-md bg-surface text-textPrimary"
                  value={feedback.type}
                  onChange={(e) => handleChange('type', e.target.value)}
                >
                  {feedbackTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Rating */}
              <div>
                <label 
                  className="block text-textPrimary font-medium mb-2"
                >
                  {translate('feedback.ratingLabel', 'Rating')}
                </label>
                
                <div className="flex items-center space-x-2">
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <button
                      key={rating}
                      type="button"
                      className={`p-2 rounded-full ${feedback.rating >= rating ? 'text-yellow-500' : 'text-textSecondary'}`}
                      onClick={() => handleChange('rating', rating)}
                      aria-label={`${rating} ${translate('feedback.stars', 'stars')}`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                      </svg>
                    </button>
                  ))}
                  
                  <span className="ml-2 text-textPrimary">
                    {feedback.rating}/5
                  </span>
                </div>
              </div>
              
              {/* Title */}
              <div>
                <label 
                  htmlFor="feedback-title"
                  className="block text-textPrimary font-medium mb-2"
                >
                  {translate('feedback.titleLabel', 'Title')}
                </label>
                
                <input
                  id="feedback-title"
                  type="text"
                  className="w-full p-2 border border-divider rounded-md bg-surface text-textPrimary"
                  value={feedback.title}
                  onChange={(e) => handleChange('title', e.target.value)}
                  placeholder={translate('feedback.titlePlaceholder', 'Brief summary of your feedback')}
                  maxLength={200}
                />
              </div>
              
              {/* Description */}
              <div>
                <label 
                  htmlFor="feedback-description"
                  className="block text-textPrimary font-medium mb-2"
                >
                  {translate('feedback.descriptionLabel', 'Description')}
                </label>
                
                <textarea
                  id="feedback-description"
                  className="w-full p-2 border border-divider rounded-md bg-surface text-textPrimary min-h-[150px]"
                  value={feedback.description}
                  onChange={(e) => handleChange('description', e.target.value)}
                  placeholder={translate('feedback.descriptionPlaceholder', 'Please provide details about your feedback...')}
                  maxLength={5000}
                />
              </div>
              
              {/* Tags */}
              <div>
                <label 
                  className="block text-textPrimary font-medium mb-2"
                >
                  {translate('feedback.tagsLabel', 'Tags (Optional)')}
                </label>
                
                <div className="flex flex-wrap gap-2">
                  {commonTags.map((tag) => (
                    <button
                      key={tag}
                      type="button"
                      className={`px-3 py-1 rounded-full text-sm ${
                        feedback.tags.includes(tag)
                          ? 'bg-primary text-white'
                          : 'bg-surface text-textSecondary border border-divider'
                      }`}
                      onClick={() => handleTagToggle(tag)}
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* Error message */}
              {submitError && (
                <div className="bg-error bg-opacity-10 text-error p-4 rounded-md">
                  {submitError}
                </div>
              )}
            </form>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-divider flex justify-end space-x-2">
          <button
            className="px-4 py-2 border border-divider rounded-md text-textPrimary bg-surface hover:bg-opacity-80"
            onClick={onClose}
            disabled={submitting}
          >
            {translate('common.cancel', 'Cancel')}
          </button>
          
          {!submitSuccess && (
            <button
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-opacity-90 disabled:opacity-50"
              onClick={handleSubmit}
              disabled={submitting}
            >
              {submitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {translate('common.submitting', 'Submitting...')}
                </span>
              ) : (
                translate('common.submit', 'Submit')
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

FeedbackDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  component: PropTypes.string,
  type: PropTypes.oneOf(['general', 'feature', 'bug', 'suggestion', 'usability', 'performance', 'other']),
  metadata: PropTypes.object,
  onSubmit: PropTypes.func
};

export default FeedbackDialog;
