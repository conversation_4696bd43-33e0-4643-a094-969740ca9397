{"version": 3, "names": ["request", "require", "mongoose", "MongoMemoryServer", "app", "EncryptionService", "jwt", "crypto", "mongoServer", "server", "encryptionService", "validToken", "expiredToken", "invalidSignatureToken", "testUser", "email", "password", "name", "beforeAll", "create", "mongo<PERSON>ri", "get<PERSON><PERSON>", "connect", "useNewUrlParser", "useUnifiedTopology", "listen", "jwtSecret", "process", "env", "JWT_SECRET", "sign", "id", "expiresIn", "afterAll", "close", "disconnect", "stop", "describe", "test", "weakPasswordResponse", "post", "send", "expect", "status", "toBe", "body", "toHaveProperty", "error", "toMatch", "validResponse", "failedLoginPromises", "i", "push", "responses", "Promise", "all", "rateLimitedResponse", "find", "res", "toBeDefined", "response", "get", "set", "keyInfo", "<PERSON><PERSON>ey", "key", "length", "toBeGreaterThanOrEqual", "testData", "secret", "encryptedPackage", "encrypt", "keyId", "encryptedString", "data", "not", "toContain", "decryptedData", "decrypt", "toEqual", "tamperedPackage", "authTag", "<PERSON><PERSON><PERSON>", "from", "toString", "rejects", "toThrow", "initialKeyInfo", "newKeyInfo", "rotateKeys", "newEncryptedPackage", "newDecryptedData", "authToken", "token", "csrfResponse", "csrfToken", "type", "description", "invalidResponse", "headers", "maliciousInput", "connectorResponse", "adminToken", "userToken", "resourceId", "role", "adminResponse", "userResponse", "resourceResponse", "delete", "anotherResourceId"], "sources": ["uac-security.test.js"], "sourcesContent": ["/**\n * NovaConnect UAC Security Tests\n * \n * This test suite validates the security features of the Universal API Connector.\n */\n\nconst request = require('supertest');\nconst mongoose = require('mongoose');\nconst { MongoMemoryServer } = require('mongodb-memory-server');\nconst app = require('../../app');\nconst { EncryptionService } = require('../../src/security/encryption-service');\nconst jwt = require('jsonwebtoken');\nconst crypto = require('crypto');\n\nlet mongoServer;\nlet server;\nlet encryptionService;\nlet validToken;\nlet expiredToken;\nlet invalidSignatureToken;\n\n// Test data\nconst testUser = {\n  email: '<EMAIL>',\n  password: 'SecurePassword123!',\n  name: 'Security Test User'\n};\n\n// Setup and teardown\nbeforeAll(async () => {\n  // Start MongoDB memory server\n  mongoServer = await MongoMemoryServer.create();\n  const mongoUri = mongoServer.getUri();\n  \n  // Connect to in-memory database\n  await mongoose.connect(mongoUri, {\n    useNewUrlParser: true,\n    useUnifiedTopology: true\n  });\n  \n  // Initialize encryption service\n  encryptionService = new EncryptionService();\n  \n  // Start server\n  server = app.listen(0);\n  \n  // Create test tokens\n  const jwtSecret = process.env.JWT_SECRET || 'test-secret';\n  \n  // Valid token\n  validToken = jwt.sign(\n    { id: 'user-123', email: testUser.email },\n    jwtSecret,\n    { expiresIn: '1h' }\n  );\n  \n  // Expired token\n  expiredToken = jwt.sign(\n    { id: 'user-123', email: testUser.email },\n    jwtSecret,\n    { expiresIn: '0s' }\n  );\n  \n  // Invalid signature token\n  invalidSignatureToken = jwt.sign(\n    { id: 'user-123', email: testUser.email },\n    'wrong-secret',\n    { expiresIn: '1h' }\n  );\n});\n\nafterAll(async () => {\n  // Stop server and close database connection\n  server.close();\n  await mongoose.disconnect();\n  await mongoServer.stop();\n});\n\ndescribe('NovaConnect UAC Security Tests', () => {\n  describe('Authentication Security', () => {\n    test('Should register a new user with password validation', async () => {\n      // Test with weak password\n      const weakPasswordResponse = await request(app)\n        .post('/api/auth/register')\n        .send({\n          email: '<EMAIL>',\n          password: 'weak',\n          name: 'Weak Password User'\n        });\n      \n      expect(weakPasswordResponse.status).toBe(400);\n      expect(weakPasswordResponse.body).toHaveProperty('error');\n      expect(weakPasswordResponse.body.error).toMatch(/password/i);\n      \n      // Test with valid password\n      const validResponse = await request(app)\n        .post('/api/auth/register')\n        .send(testUser);\n      \n      expect(validResponse.status).toBe(201);\n      expect(validResponse.body).toHaveProperty('token');\n    });\n    \n    test('Should enforce rate limiting on login attempts', async () => {\n      // Make multiple failed login attempts\n      const failedLoginPromises = [];\n      for (let i = 0; i < 10; i++) {\n        failedLoginPromises.push(\n          request(app)\n            .post('/api/auth/login')\n            .send({\n              email: testUser.email,\n              password: 'wrong-password'\n            })\n        );\n      }\n      \n      const responses = await Promise.all(failedLoginPromises);\n      \n      // At least one of the later responses should be rate limited\n      const rateLimitedResponse = responses.find(res => res.status === 429);\n      expect(rateLimitedResponse).toBeDefined();\n      expect(rateLimitedResponse.body).toHaveProperty('error');\n      expect(rateLimitedResponse.body.error).toMatch(/too many/i);\n    });\n    \n    test('Should reject expired tokens', async () => {\n      const response = await request(app)\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${expiredToken}`);\n      \n      expect(response.status).toBe(401);\n      expect(response.body).toHaveProperty('error');\n      expect(response.body.error).toMatch(/expired/i);\n    });\n    \n    test('Should reject tokens with invalid signatures', async () => {\n      const response = await request(app)\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${invalidSignatureToken}`);\n      \n      expect(response.status).toBe(401);\n      expect(response.body).toHaveProperty('error');\n      expect(response.body.error).toMatch(/invalid/i);\n    });\n  });\n  \n  describe('Encryption Security', () => {\n    test('Should generate secure encryption keys', async () => {\n      const keyInfo = await encryptionService.generateKey();\n      \n      expect(keyInfo).toHaveProperty('keyId');\n      expect(keyInfo).toHaveProperty('key');\n      expect(keyInfo.key.length).toBeGreaterThanOrEqual(32); // At least 256 bits\n    });\n    \n    test('Should encrypt and decrypt data securely', async () => {\n      const testData = { secret: 'sensitive-information' };\n      const keyInfo = await encryptionService.generateKey();\n      \n      // Encrypt data\n      const encryptedPackage = await encryptionService.encrypt(testData, keyInfo.keyId);\n      \n      expect(encryptedPackage).toHaveProperty('keyId');\n      expect(encryptedPackage).toHaveProperty('iv');\n      expect(encryptedPackage).toHaveProperty('authTag');\n      expect(encryptedPackage).toHaveProperty('data');\n      \n      // Encrypted data should not contain original data\n      const encryptedString = encryptedPackage.data;\n      expect(encryptedString).not.toContain('sensitive-information');\n      \n      // Decrypt data\n      const decryptedData = await encryptionService.decrypt(encryptedPackage);\n      \n      expect(decryptedData).toEqual(testData);\n    });\n    \n    test('Should detect tampering with encrypted data', async () => {\n      const testData = { secret: 'sensitive-information' };\n      const keyInfo = await encryptionService.generateKey();\n      \n      // Encrypt data\n      const encryptedPackage = await encryptionService.encrypt(testData, keyInfo.keyId);\n      \n      // Tamper with auth tag\n      const tamperedPackage = {\n        ...encryptedPackage,\n        authTag: Buffer.from('tampered-auth-tag').toString('base64')\n      };\n      \n      // Attempt to decrypt tampered data\n      await expect(encryptionService.decrypt(tamperedPackage)).rejects.toThrow();\n    });\n    \n    test('Should support key rotation', async () => {\n      const testData = { secret: 'sensitive-information' };\n      \n      // Generate initial key and encrypt data\n      const initialKeyInfo = await encryptionService.generateKey();\n      const encryptedPackage = await encryptionService.encrypt(testData, initialKeyInfo.keyId);\n      \n      // Rotate keys\n      const newKeyInfo = await encryptionService.rotateKeys();\n      \n      // Should still be able to decrypt with old key\n      const decryptedData = await encryptionService.decrypt(encryptedPackage);\n      expect(decryptedData).toEqual(testData);\n      \n      // Should be able to encrypt with new key\n      const newEncryptedPackage = await encryptionService.encrypt(testData, newKeyInfo.keyId);\n      const newDecryptedData = await encryptionService.decrypt(newEncryptedPackage);\n      expect(newDecryptedData).toEqual(testData);\n    });\n  });\n  \n  describe('API Security', () => {\n    let authToken;\n    \n    beforeAll(async () => {\n      // Login to get valid token\n      const response = await request(app)\n        .post('/api/auth/login')\n        .send({\n          email: testUser.email,\n          password: testUser.password\n        });\n      \n      authToken = response.body.token;\n    });\n    \n    test('Should enforce CSRF protection', async () => {\n      // Get CSRF token\n      const csrfResponse = await request(app)\n        .get('/api/csrf-token')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(csrfResponse.status).toBe(200);\n      expect(csrfResponse.body).toHaveProperty('csrfToken');\n      \n      const csrfToken = csrfResponse.body.csrfToken;\n      \n      // Request with CSRF token should succeed\n      const validResponse = await request(app)\n        .post('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`)\n        .set('X-CSRF-Token', csrfToken)\n        .send({\n          name: 'Test Connector',\n          type: 'REST',\n          description: 'Test connector'\n        });\n      \n      expect(validResponse.status).toBe(201);\n      \n      // Request without CSRF token should fail\n      const invalidResponse = await request(app)\n        .post('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`)\n        .send({\n          name: 'Another Connector',\n          type: 'REST',\n          description: 'Another test connector'\n        });\n      \n      expect(invalidResponse.status).toBe(403);\n      expect(invalidResponse.body).toHaveProperty('error');\n      expect(invalidResponse.body.error).toMatch(/csrf/i);\n    });\n    \n    test('Should enforce content security policy', async () => {\n      const response = await request(app)\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.headers).toHaveProperty('content-security-policy');\n      expect(response.headers['content-security-policy']).toContain('default-src');\n    });\n    \n    test('Should set secure HTTP headers', async () => {\n      const response = await request(app)\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      expect(response.headers).toHaveProperty('x-content-type-options');\n      expect(response.headers).toHaveProperty('x-frame-options');\n      expect(response.headers).toHaveProperty('x-xss-protection');\n      expect(response.headers).toHaveProperty('strict-transport-security');\n    });\n    \n    test('Should sanitize input to prevent injection attacks', async () => {\n      const maliciousInput = {\n        name: 'Test<script>alert(\"XSS\")</script>',\n        type: 'REST',\n        description: 'Test connector with script injection'\n      };\n      \n      // Get CSRF token\n      const csrfResponse = await request(app)\n        .get('/api/csrf-token')\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      const csrfToken = csrfResponse.body.csrfToken;\n      \n      const response = await request(app)\n        .post('/api/connectors')\n        .set('Authorization', `Bearer ${authToken}`)\n        .set('X-CSRF-Token', csrfToken)\n        .send(maliciousInput);\n      \n      expect(response.status).toBe(201);\n      \n      // Get the created connector\n      const connectorResponse = await request(app)\n        .get(`/api/connectors/${response.body.id}`)\n        .set('Authorization', `Bearer ${authToken}`);\n      \n      // Script tags should be sanitized\n      expect(connectorResponse.body.name).not.toContain('<script>');\n    });\n  });\n  \n  describe('Authorization Security', () => {\n    let adminToken;\n    let userToken;\n    let resourceId;\n    \n    beforeAll(async () => {\n      // Create admin user\n      await request(app)\n        .post('/api/auth/register')\n        .send({\n          email: '<EMAIL>',\n          password: 'AdminPassword123!',\n          name: 'Admin User',\n          role: 'admin'\n        });\n      \n      // Create regular user\n      await request(app)\n        .post('/api/auth/register')\n        .send({\n          email: '<EMAIL>',\n          password: 'UserPassword123!',\n          name: 'Regular User',\n          role: 'user'\n        });\n      \n      // Login as admin\n      const adminResponse = await request(app)\n        .post('/api/auth/login')\n        .send({\n          email: '<EMAIL>',\n          password: 'AdminPassword123!'\n        });\n      \n      adminToken = adminResponse.body.token;\n      \n      // Login as user\n      const userResponse = await request(app)\n        .post('/api/auth/login')\n        .send({\n          email: '<EMAIL>',\n          password: 'UserPassword123!'\n        });\n      \n      userToken = userResponse.body.token;\n      \n      // Create a resource as admin\n      const resourceResponse = await request(app)\n        .post('/api/connectors')\n        .set('Authorization', `Bearer ${adminToken}`)\n        .send({\n          name: 'Admin Connector',\n          type: 'REST',\n          description: 'Connector created by admin'\n        });\n      \n      resourceId = resourceResponse.body.id;\n    });\n    \n    test('Should enforce role-based access control', async () => {\n      // Admin should be able to access admin-only endpoint\n      const adminResponse = await request(app)\n        .get('/api/admin/users')\n        .set('Authorization', `Bearer ${adminToken}`);\n      \n      expect(adminResponse.status).toBe(200);\n      \n      // Regular user should not be able to access admin-only endpoint\n      const userResponse = await request(app)\n        .get('/api/admin/users')\n        .set('Authorization', `Bearer ${userToken}`);\n      \n      expect(userResponse.status).toBe(403);\n      expect(userResponse.body).toHaveProperty('error');\n      expect(userResponse.body.error).toMatch(/permission/i);\n    });\n    \n    test('Should enforce resource-based access control', async () => {\n      // Admin should be able to delete any resource\n      const adminResponse = await request(app)\n        .delete(`/api/connectors/${resourceId}`)\n        .set('Authorization', `Bearer ${adminToken}`);\n      \n      expect(adminResponse.status).toBe(200);\n      \n      // Create another resource\n      const resourceResponse = await request(app)\n        .post('/api/connectors')\n        .set('Authorization', `Bearer ${adminToken}`)\n        .send({\n          name: 'Another Connector',\n          type: 'REST',\n          description: 'Another connector'\n        });\n      \n      const anotherResourceId = resourceResponse.body.id;\n      \n      // Regular user should not be able to delete resources they don't own\n      const userResponse = await request(app)\n        .delete(`/api/connectors/${anotherResourceId}`)\n        .set('Authorization', `Bearer ${userToken}`);\n      \n      expect(userResponse.status).toBe(403);\n      expect(userResponse.body).toHaveProperty('error');\n      expect(userResponse.body.error).toMatch(/permission/i);\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AACpC,MAAMC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AACpC,MAAM;EAAEE;AAAkB,CAAC,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AAC9D,MAAMG,GAAG,GAAGH,OAAO,CAAC,WAAW,CAAC;AAChC,MAAM;EAAEI;AAAkB,CAAC,GAAGJ,OAAO,CAAC,uCAAuC,CAAC;AAC9E,MAAMK,GAAG,GAAGL,OAAO,CAAC,cAAc,CAAC;AACnC,MAAMM,MAAM,GAAGN,OAAO,CAAC,QAAQ,CAAC;AAEhC,IAAIO,WAAW;AACf,IAAIC,MAAM;AACV,IAAIC,iBAAiB;AACrB,IAAIC,UAAU;AACd,IAAIC,YAAY;AAChB,IAAIC,qBAAqB;;AAEzB;AACA,MAAMC,QAAQ,GAAG;EACfC,KAAK,EAAE,2BAA2B;EAClCC,QAAQ,EAAE,oBAAoB;EAC9BC,IAAI,EAAE;AACR,CAAC;;AAED;AACAC,SAAS,CAAC,YAAY;EACpB;EACAV,WAAW,GAAG,MAAML,iBAAiB,CAACgB,MAAM,CAAC,CAAC;EAC9C,MAAMC,QAAQ,GAAGZ,WAAW,CAACa,MAAM,CAAC,CAAC;;EAErC;EACA,MAAMnB,QAAQ,CAACoB,OAAO,CAACF,QAAQ,EAAE;IAC/BG,eAAe,EAAE,IAAI;IACrBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACAd,iBAAiB,GAAG,IAAIL,iBAAiB,CAAC,CAAC;;EAE3C;EACAI,MAAM,GAAGL,GAAG,CAACqB,MAAM,CAAC,CAAC,CAAC;;EAEtB;EACA,MAAMC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,aAAa;;EAEzD;EACAlB,UAAU,GAAGL,GAAG,CAACwB,IAAI,CACnB;IAAEC,EAAE,EAAE,UAAU;IAAEhB,KAAK,EAAED,QAAQ,CAACC;EAAM,CAAC,EACzCW,SAAS,EACT;IAAEM,SAAS,EAAE;EAAK,CACpB,CAAC;;EAED;EACApB,YAAY,GAAGN,GAAG,CAACwB,IAAI,CACrB;IAAEC,EAAE,EAAE,UAAU;IAAEhB,KAAK,EAAED,QAAQ,CAACC;EAAM,CAAC,EACzCW,SAAS,EACT;IAAEM,SAAS,EAAE;EAAK,CACpB,CAAC;;EAED;EACAnB,qBAAqB,GAAGP,GAAG,CAACwB,IAAI,CAC9B;IAAEC,EAAE,EAAE,UAAU;IAAEhB,KAAK,EAAED,QAAQ,CAACC;EAAM,CAAC,EACzC,cAAc,EACd;IAAEiB,SAAS,EAAE;EAAK,CACpB,CAAC;AACH,CAAC,CAAC;AAEFC,QAAQ,CAAC,YAAY;EACnB;EACAxB,MAAM,CAACyB,KAAK,CAAC,CAAC;EACd,MAAMhC,QAAQ,CAACiC,UAAU,CAAC,CAAC;EAC3B,MAAM3B,WAAW,CAAC4B,IAAI,CAAC,CAAC;AAC1B,CAAC,CAAC;AAEFC,QAAQ,CAAC,gCAAgC,EAAE,MAAM;EAC/CA,QAAQ,CAAC,yBAAyB,EAAE,MAAM;IACxCC,IAAI,CAAC,qDAAqD,EAAE,YAAY;MACtE;MACA,MAAMC,oBAAoB,GAAG,MAAMvC,OAAO,CAACI,GAAG,CAAC,CAC5CoC,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,IAAI,CAAC;QACJ1B,KAAK,EAAE,kBAAkB;QACzBC,QAAQ,EAAE,MAAM;QAChBC,IAAI,EAAE;MACR,CAAC,CAAC;MAEJyB,MAAM,CAACH,oBAAoB,CAACI,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MAC7CF,MAAM,CAACH,oBAAoB,CAACM,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MACzDJ,MAAM,CAACH,oBAAoB,CAACM,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,WAAW,CAAC;;MAE5D;MACA,MAAMC,aAAa,GAAG,MAAMjD,OAAO,CAACI,GAAG,CAAC,CACrCoC,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,IAAI,CAAC3B,QAAQ,CAAC;MAEjB4B,MAAM,CAACO,aAAa,CAACN,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACtCF,MAAM,CAACO,aAAa,CAACJ,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;IACpD,CAAC,CAAC;IAEFR,IAAI,CAAC,gDAAgD,EAAE,YAAY;MACjE;MACA,MAAMY,mBAAmB,GAAG,EAAE;MAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3BD,mBAAmB,CAACE,IAAI,CACtBpD,OAAO,CAACI,GAAG,CAAC,CACToC,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;UACJ1B,KAAK,EAAED,QAAQ,CAACC,KAAK;UACrBC,QAAQ,EAAE;QACZ,CAAC,CACL,CAAC;MACH;MAEA,MAAMqC,SAAS,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,mBAAmB,CAAC;;MAExD;MACA,MAAMM,mBAAmB,GAAGH,SAAS,CAACI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACf,MAAM,KAAK,GAAG,CAAC;MACrED,MAAM,CAACc,mBAAmB,CAAC,CAACG,WAAW,CAAC,CAAC;MACzCjB,MAAM,CAACc,mBAAmB,CAACX,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MACxDJ,MAAM,CAACc,mBAAmB,CAACX,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,WAAW,CAAC;IAC7D,CAAC,CAAC;IAEFV,IAAI,CAAC,8BAA8B,EAAE,YAAY;MAC/C,MAAMsB,QAAQ,GAAG,MAAM5D,OAAO,CAACI,GAAG,CAAC,CAChCyD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAUlD,YAAY,EAAE,CAAC;MAEjD8B,MAAM,CAACkB,QAAQ,CAACjB,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACkB,QAAQ,CAACf,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MAC7CJ,MAAM,CAACkB,QAAQ,CAACf,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,CAAC,CAAC;IAEFV,IAAI,CAAC,8CAA8C,EAAE,YAAY;MAC/D,MAAMsB,QAAQ,GAAG,MAAM5D,OAAO,CAACI,GAAG,CAAC,CAChCyD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAUjD,qBAAqB,EAAE,CAAC;MAE1D6B,MAAM,CAACkB,QAAQ,CAACjB,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACjCF,MAAM,CAACkB,QAAQ,CAACf,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MAC7CJ,MAAM,CAACkB,QAAQ,CAACf,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFX,QAAQ,CAAC,qBAAqB,EAAE,MAAM;IACpCC,IAAI,CAAC,wCAAwC,EAAE,YAAY;MACzD,MAAMyB,OAAO,GAAG,MAAMrD,iBAAiB,CAACsD,WAAW,CAAC,CAAC;MAErDtB,MAAM,CAACqB,OAAO,CAAC,CAACjB,cAAc,CAAC,OAAO,CAAC;MACvCJ,MAAM,CAACqB,OAAO,CAAC,CAACjB,cAAc,CAAC,KAAK,CAAC;MACrCJ,MAAM,CAACqB,OAAO,CAACE,GAAG,CAACC,MAAM,CAAC,CAACC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;IAEF7B,IAAI,CAAC,0CAA0C,EAAE,YAAY;MAC3D,MAAM8B,QAAQ,GAAG;QAAEC,MAAM,EAAE;MAAwB,CAAC;MACpD,MAAMN,OAAO,GAAG,MAAMrD,iBAAiB,CAACsD,WAAW,CAAC,CAAC;;MAErD;MACA,MAAMM,gBAAgB,GAAG,MAAM5D,iBAAiB,CAAC6D,OAAO,CAACH,QAAQ,EAAEL,OAAO,CAACS,KAAK,CAAC;MAEjF9B,MAAM,CAAC4B,gBAAgB,CAAC,CAACxB,cAAc,CAAC,OAAO,CAAC;MAChDJ,MAAM,CAAC4B,gBAAgB,CAAC,CAACxB,cAAc,CAAC,IAAI,CAAC;MAC7CJ,MAAM,CAAC4B,gBAAgB,CAAC,CAACxB,cAAc,CAAC,SAAS,CAAC;MAClDJ,MAAM,CAAC4B,gBAAgB,CAAC,CAACxB,cAAc,CAAC,MAAM,CAAC;;MAE/C;MACA,MAAM2B,eAAe,GAAGH,gBAAgB,CAACI,IAAI;MAC7ChC,MAAM,CAAC+B,eAAe,CAAC,CAACE,GAAG,CAACC,SAAS,CAAC,uBAAuB,CAAC;;MAE9D;MACA,MAAMC,aAAa,GAAG,MAAMnE,iBAAiB,CAACoE,OAAO,CAACR,gBAAgB,CAAC;MAEvE5B,MAAM,CAACmC,aAAa,CAAC,CAACE,OAAO,CAACX,QAAQ,CAAC;IACzC,CAAC,CAAC;IAEF9B,IAAI,CAAC,6CAA6C,EAAE,YAAY;MAC9D,MAAM8B,QAAQ,GAAG;QAAEC,MAAM,EAAE;MAAwB,CAAC;MACpD,MAAMN,OAAO,GAAG,MAAMrD,iBAAiB,CAACsD,WAAW,CAAC,CAAC;;MAErD;MACA,MAAMM,gBAAgB,GAAG,MAAM5D,iBAAiB,CAAC6D,OAAO,CAACH,QAAQ,EAAEL,OAAO,CAACS,KAAK,CAAC;;MAEjF;MACA,MAAMQ,eAAe,GAAG;QACtB,GAAGV,gBAAgB;QACnBW,OAAO,EAAEC,MAAM,CAACC,IAAI,CAAC,mBAAmB,CAAC,CAACC,QAAQ,CAAC,QAAQ;MAC7D,CAAC;;MAED;MACA,MAAM1C,MAAM,CAAChC,iBAAiB,CAACoE,OAAO,CAACE,eAAe,CAAC,CAAC,CAACK,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5E,CAAC,CAAC;IAEFhD,IAAI,CAAC,6BAA6B,EAAE,YAAY;MAC9C,MAAM8B,QAAQ,GAAG;QAAEC,MAAM,EAAE;MAAwB,CAAC;;MAEpD;MACA,MAAMkB,cAAc,GAAG,MAAM7E,iBAAiB,CAACsD,WAAW,CAAC,CAAC;MAC5D,MAAMM,gBAAgB,GAAG,MAAM5D,iBAAiB,CAAC6D,OAAO,CAACH,QAAQ,EAAEmB,cAAc,CAACf,KAAK,CAAC;;MAExF;MACA,MAAMgB,UAAU,GAAG,MAAM9E,iBAAiB,CAAC+E,UAAU,CAAC,CAAC;;MAEvD;MACA,MAAMZ,aAAa,GAAG,MAAMnE,iBAAiB,CAACoE,OAAO,CAACR,gBAAgB,CAAC;MACvE5B,MAAM,CAACmC,aAAa,CAAC,CAACE,OAAO,CAACX,QAAQ,CAAC;;MAEvC;MACA,MAAMsB,mBAAmB,GAAG,MAAMhF,iBAAiB,CAAC6D,OAAO,CAACH,QAAQ,EAAEoB,UAAU,CAAChB,KAAK,CAAC;MACvF,MAAMmB,gBAAgB,GAAG,MAAMjF,iBAAiB,CAACoE,OAAO,CAACY,mBAAmB,CAAC;MAC7EhD,MAAM,CAACiD,gBAAgB,CAAC,CAACZ,OAAO,CAACX,QAAQ,CAAC;IAC5C,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF/B,QAAQ,CAAC,cAAc,EAAE,MAAM;IAC7B,IAAIuD,SAAS;IAEb1E,SAAS,CAAC,YAAY;MACpB;MACA,MAAM0C,QAAQ,GAAG,MAAM5D,OAAO,CAACI,GAAG,CAAC,CAChCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;QACJ1B,KAAK,EAAED,QAAQ,CAACC,KAAK;QACrBC,QAAQ,EAAEF,QAAQ,CAACE;MACrB,CAAC,CAAC;MAEJ4E,SAAS,GAAGhC,QAAQ,CAACf,IAAI,CAACgD,KAAK;IACjC,CAAC,CAAC;IAEFvD,IAAI,CAAC,gCAAgC,EAAE,YAAY;MACjD;MACA,MAAMwD,YAAY,GAAG,MAAM9F,OAAO,CAACI,GAAG,CAAC,CACpCyD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC;MAE9ClD,MAAM,CAACoD,YAAY,CAACnD,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACrCF,MAAM,CAACoD,YAAY,CAACjD,IAAI,CAAC,CAACC,cAAc,CAAC,WAAW,CAAC;MAErD,MAAMiD,SAAS,GAAGD,YAAY,CAACjD,IAAI,CAACkD,SAAS;;MAE7C;MACA,MAAM9C,aAAa,GAAG,MAAMjD,OAAO,CAACI,GAAG,CAAC,CACrCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBsB,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC,CAC3C9B,GAAG,CAAC,cAAc,EAAEiC,SAAS,CAAC,CAC9BtD,IAAI,CAAC;QACJxB,IAAI,EAAE,gBAAgB;QACtB+E,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE;MACf,CAAC,CAAC;MAEJvD,MAAM,CAACO,aAAa,CAACN,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;MAEtC;MACA,MAAMsD,eAAe,GAAG,MAAMlG,OAAO,CAACI,GAAG,CAAC,CACvCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBsB,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC,CAC3CnD,IAAI,CAAC;QACJxB,IAAI,EAAE,mBAAmB;QACzB+E,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE;MACf,CAAC,CAAC;MAEJvD,MAAM,CAACwD,eAAe,CAACvD,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACxCF,MAAM,CAACwD,eAAe,CAACrD,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MACpDJ,MAAM,CAACwD,eAAe,CAACrD,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,OAAO,CAAC;IACrD,CAAC,CAAC;IAEFV,IAAI,CAAC,wCAAwC,EAAE,YAAY;MACzD,MAAMsB,QAAQ,GAAG,MAAM5D,OAAO,CAACI,GAAG,CAAC,CAChCyD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC;MAE9ClD,MAAM,CAACkB,QAAQ,CAACuC,OAAO,CAAC,CAACrD,cAAc,CAAC,yBAAyB,CAAC;MAClEJ,MAAM,CAACkB,QAAQ,CAACuC,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAACvB,SAAS,CAAC,aAAa,CAAC;IAC9E,CAAC,CAAC;IAEFtC,IAAI,CAAC,gCAAgC,EAAE,YAAY;MACjD,MAAMsB,QAAQ,GAAG,MAAM5D,OAAO,CAACI,GAAG,CAAC,CAChCyD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC;MAE9ClD,MAAM,CAACkB,QAAQ,CAACuC,OAAO,CAAC,CAACrD,cAAc,CAAC,wBAAwB,CAAC;MACjEJ,MAAM,CAACkB,QAAQ,CAACuC,OAAO,CAAC,CAACrD,cAAc,CAAC,iBAAiB,CAAC;MAC1DJ,MAAM,CAACkB,QAAQ,CAACuC,OAAO,CAAC,CAACrD,cAAc,CAAC,kBAAkB,CAAC;MAC3DJ,MAAM,CAACkB,QAAQ,CAACuC,OAAO,CAAC,CAACrD,cAAc,CAAC,2BAA2B,CAAC;IACtE,CAAC,CAAC;IAEFR,IAAI,CAAC,oDAAoD,EAAE,YAAY;MACrE,MAAM8D,cAAc,GAAG;QACrBnF,IAAI,EAAE,mCAAmC;QACzC+E,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE;MACf,CAAC;;MAED;MACA,MAAMH,YAAY,GAAG,MAAM9F,OAAO,CAACI,GAAG,CAAC,CACpCyD,GAAG,CAAC,iBAAiB,CAAC,CACtBC,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC;MAE9C,MAAMG,SAAS,GAAGD,YAAY,CAACjD,IAAI,CAACkD,SAAS;MAE7C,MAAMnC,QAAQ,GAAG,MAAM5D,OAAO,CAACI,GAAG,CAAC,CAChCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBsB,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC,CAC3C9B,GAAG,CAAC,cAAc,EAAEiC,SAAS,CAAC,CAC9BtD,IAAI,CAAC2D,cAAc,CAAC;MAEvB1D,MAAM,CAACkB,QAAQ,CAACjB,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;MAEjC;MACA,MAAMyD,iBAAiB,GAAG,MAAMrG,OAAO,CAACI,GAAG,CAAC,CACzCyD,GAAG,CAAC,mBAAmBD,QAAQ,CAACf,IAAI,CAACd,EAAE,EAAE,CAAC,CAC1C+B,GAAG,CAAC,eAAe,EAAE,UAAU8B,SAAS,EAAE,CAAC;;MAE9C;MACAlD,MAAM,CAAC2D,iBAAiB,CAACxD,IAAI,CAAC5B,IAAI,CAAC,CAAC0D,GAAG,CAACC,SAAS,CAAC,UAAU,CAAC;IAC/D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFvC,QAAQ,CAAC,wBAAwB,EAAE,MAAM;IACvC,IAAIiE,UAAU;IACd,IAAIC,SAAS;IACb,IAAIC,UAAU;IAEdtF,SAAS,CAAC,YAAY;MACpB;MACA,MAAMlB,OAAO,CAACI,GAAG,CAAC,CACfoC,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,IAAI,CAAC;QACJ1B,KAAK,EAAE,mBAAmB;QAC1BC,QAAQ,EAAE,mBAAmB;QAC7BC,IAAI,EAAE,YAAY;QAClBwF,IAAI,EAAE;MACR,CAAC,CAAC;;MAEJ;MACA,MAAMzG,OAAO,CAACI,GAAG,CAAC,CACfoC,IAAI,CAAC,oBAAoB,CAAC,CAC1BC,IAAI,CAAC;QACJ1B,KAAK,EAAE,kBAAkB;QACzBC,QAAQ,EAAE,kBAAkB;QAC5BC,IAAI,EAAE,cAAc;QACpBwF,IAAI,EAAE;MACR,CAAC,CAAC;;MAEJ;MACA,MAAMC,aAAa,GAAG,MAAM1G,OAAO,CAACI,GAAG,CAAC,CACrCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;QACJ1B,KAAK,EAAE,mBAAmB;QAC1BC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEJsF,UAAU,GAAGI,aAAa,CAAC7D,IAAI,CAACgD,KAAK;;MAErC;MACA,MAAMc,YAAY,GAAG,MAAM3G,OAAO,CAACI,GAAG,CAAC,CACpCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBC,IAAI,CAAC;QACJ1B,KAAK,EAAE,kBAAkB;QACzBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEJuF,SAAS,GAAGI,YAAY,CAAC9D,IAAI,CAACgD,KAAK;;MAEnC;MACA,MAAMe,gBAAgB,GAAG,MAAM5G,OAAO,CAACI,GAAG,CAAC,CACxCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBsB,GAAG,CAAC,eAAe,EAAE,UAAUwC,UAAU,EAAE,CAAC,CAC5C7D,IAAI,CAAC;QACJxB,IAAI,EAAE,iBAAiB;QACvB+E,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE;MACf,CAAC,CAAC;MAEJO,UAAU,GAAGI,gBAAgB,CAAC/D,IAAI,CAACd,EAAE;IACvC,CAAC,CAAC;IAEFO,IAAI,CAAC,0CAA0C,EAAE,YAAY;MAC3D;MACA,MAAMoE,aAAa,GAAG,MAAM1G,OAAO,CAACI,GAAG,CAAC,CACrCyD,GAAG,CAAC,kBAAkB,CAAC,CACvBC,GAAG,CAAC,eAAe,EAAE,UAAUwC,UAAU,EAAE,CAAC;MAE/C5D,MAAM,CAACgE,aAAa,CAAC/D,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;MAEtC;MACA,MAAM+D,YAAY,GAAG,MAAM3G,OAAO,CAACI,GAAG,CAAC,CACpCyD,GAAG,CAAC,kBAAkB,CAAC,CACvBC,GAAG,CAAC,eAAe,EAAE,UAAUyC,SAAS,EAAE,CAAC;MAE9C7D,MAAM,CAACiE,YAAY,CAAChE,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACrCF,MAAM,CAACiE,YAAY,CAAC9D,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MACjDJ,MAAM,CAACiE,YAAY,CAAC9D,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,aAAa,CAAC;IACxD,CAAC,CAAC;IAEFV,IAAI,CAAC,8CAA8C,EAAE,YAAY;MAC/D;MACA,MAAMoE,aAAa,GAAG,MAAM1G,OAAO,CAACI,GAAG,CAAC,CACrCyG,MAAM,CAAC,mBAAmBL,UAAU,EAAE,CAAC,CACvC1C,GAAG,CAAC,eAAe,EAAE,UAAUwC,UAAU,EAAE,CAAC;MAE/C5D,MAAM,CAACgE,aAAa,CAAC/D,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;;MAEtC;MACA,MAAMgE,gBAAgB,GAAG,MAAM5G,OAAO,CAACI,GAAG,CAAC,CACxCoC,IAAI,CAAC,iBAAiB,CAAC,CACvBsB,GAAG,CAAC,eAAe,EAAE,UAAUwC,UAAU,EAAE,CAAC,CAC5C7D,IAAI,CAAC;QACJxB,IAAI,EAAE,mBAAmB;QACzB+E,IAAI,EAAE,MAAM;QACZC,WAAW,EAAE;MACf,CAAC,CAAC;MAEJ,MAAMa,iBAAiB,GAAGF,gBAAgB,CAAC/D,IAAI,CAACd,EAAE;;MAElD;MACA,MAAM4E,YAAY,GAAG,MAAM3G,OAAO,CAACI,GAAG,CAAC,CACpCyG,MAAM,CAAC,mBAAmBC,iBAAiB,EAAE,CAAC,CAC9ChD,GAAG,CAAC,eAAe,EAAE,UAAUyC,SAAS,EAAE,CAAC;MAE9C7D,MAAM,CAACiE,YAAY,CAAChE,MAAM,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACrCF,MAAM,CAACiE,YAAY,CAAC9D,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;MACjDJ,MAAM,CAACiE,YAAY,CAAC9D,IAAI,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,aAAa,CAAC;IACxD,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}