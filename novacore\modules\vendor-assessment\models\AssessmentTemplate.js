/**
 * NovaCore Assessment Template Model
 * 
 * This model defines the schema for assessment templates in the SaaS vendor assessment module.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define question schema (same as in Assessment model)
const questionSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  text: { 
    type: String, 
    required: true, 
    trim: true 
  },
  category: { 
    type: String, 
    required: true, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: ['multiple_choice', 'yes_no', 'text', 'file_upload', 'rating'], 
    default: 'yes_no' 
  },
  options: [{ 
    value: { 
      type: String, 
      required: true, 
      trim: true 
    },
    text: { 
      type: String, 
      required: true, 
      trim: true 
    },
    score: { 
      type: Number 
    }
  }],
  required: { 
    type: Boolean, 
    default: true 
  },
  weight: { 
    type: Number, 
    default: 1 
  },
  guidance: { 
    type: String, 
    trim: true 
  },
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  dependsOn: { 
    questionId: { 
      type: String, 
      trim: true 
    },
    value: { 
      type: String, 
      trim: true 
    }
  }
}, { _id: false });

// Define section schema (same as in Assessment model)
const sectionSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  order: { 
    type: Number, 
    default: 0 
  },
  questions: [questionSchema],
  weight: { 
    type: Number, 
    default: 1 
  }
}, { _id: false });

// Define assessment template schema
const assessmentTemplateSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  type: { 
    type: String, 
    enum: ['vendor', 'internal', 'compliance', 'custom'], 
    default: 'vendor' 
  },
  status: { 
    type: String, 
    enum: ['draft', 'active', 'archived'], 
    default: 'draft' 
  },
  sections: [sectionSchema],
  passingScore: { 
    type: Number, 
    default: 70 
  },
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  tags: [{ 
    type: String, 
    trim: true 
  }],
  isDefault: { 
    type: Boolean, 
    default: false 
  },
  version: { 
    type: String, 
    default: '1.0' 
  },
  previousVersion: { 
    type: Schema.Types.ObjectId, 
    ref: 'AssessmentTemplate' 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
assessmentTemplateSchema.index({ organizationId: 1 });
assessmentTemplateSchema.index({ name: 1, organizationId: 1 }, { unique: true });
assessmentTemplateSchema.index({ type: 1 });
assessmentTemplateSchema.index({ status: 1 });
assessmentTemplateSchema.index({ frameworks: 1 });
assessmentTemplateSchema.index({ tags: 1 });
assessmentTemplateSchema.index({ isDefault: 1 });
assessmentTemplateSchema.index({ version: 1 });
assessmentTemplateSchema.index({ createdAt: 1 });

// Add methods
assessmentTemplateSchema.methods.isActive = function() {
  return this.status === 'active';
};

assessmentTemplateSchema.methods.getTotalQuestions = function() {
  return this.sections.reduce((sum, section) => {
    return sum + section.questions.length;
  }, 0);
};

assessmentTemplateSchema.methods.getFrameworkQuestions = function(framework) {
  const questions = [];
  
  for (const section of this.sections) {
    for (const question of section.questions) {
      if (question.frameworks && question.frameworks.includes(framework)) {
        questions.push(question);
      }
    }
  }
  
  return questions;
};

// Add statics
assessmentTemplateSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

assessmentTemplateSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'active' 
  });
};

assessmentTemplateSchema.statics.findDefault = function(organizationId, type = 'vendor') {
  return this.findOne({ 
    organizationId, 
    type, 
    isDefault: true, 
    status: 'active' 
  });
};

assessmentTemplateSchema.statics.findByFramework = function(organizationId, framework) {
  return this.find({ 
    organizationId, 
    frameworks: framework, 
    status: 'active' 
  });
};

// Create model
const AssessmentTemplate = mongoose.model('AssessmentTemplate', assessmentTemplateSchema);

module.exports = AssessmentTemplate;
