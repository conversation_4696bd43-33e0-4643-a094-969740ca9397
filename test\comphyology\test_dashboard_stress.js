/**
 * Comphyology Dashboard Stress Test
 *
 * This script tests the performance of the Comphyology real-time dashboard
 * under various load conditions.
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const { novaVision } = require('../../src/novavision');
const { novaConnect } = require('../../src/novaconnect');
const ComphyologyNovaConnectDashboard = require('../../src/comphyology/nova_connect_dashboard');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../comphyology_test_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

// Mock NovaConnect for testing
class NovaConnectMock {
  constructor() {
    this.subscribers = new Map();
    this.publishInterval = null;
    this.publishRate = 10; // messages per second
  }

  async subscribe(topic, callback) {
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, []);
    }

    this.subscribers.get(topic).push(callback);
    console.log(`Subscribed to topic: ${topic}`);

    return Promise.resolve();
  }

  async unsubscribe(topic, callback) {
    if (!this.subscribers.has(topic)) {
      return Promise.resolve();
    }

    if (callback) {
      const callbacks = this.subscribers.get(topic);
      const index = callbacks.indexOf(callback);

      if (index !== -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.subscribers.delete(topic);
    }

    console.log(`Unsubscribed from topic: ${topic}`);

    return Promise.resolve();
  }

  async publish(topic, message) {
    if (!this.subscribers.has(topic)) {
      return Promise.resolve();
    }

    const callbacks = this.subscribers.get(topic);

    for (const callback of callbacks) {
      try {
        callback(message, topic);
      } catch (error) {
        console.error(`Error in subscriber callback for topic ${topic}:`, error);
      }
    }

    return Promise.resolve();
  }

  startPublishing(rate = 10) {
    this.publishRate = rate;

    if (this.publishInterval) {
      clearInterval(this.publishInterval);
    }

    const interval = 1000 / rate;

    this.publishInterval = setInterval(() => {
      this._publishRandomData();
    }, interval);

    console.log(`Started publishing at ${rate} messages per second`);
  }

  stopPublishing() {
    if (this.publishInterval) {
      clearInterval(this.publishInterval);
      this.publishInterval = null;
    }

    console.log('Stopped publishing');
  }

  _publishRandomData() {
    // Publish random threat data
    this.publish('novaShield.threatDetected', this._generateRandomThreat());

    // Publish random compliance data
    this.publish('novaTrack.complianceChanged', this._generateRandomRegulation());

    // Publish random decision data
    this.publish('novaCore.decisionMade', this._generateRandomDecision());
  }

  _generateRandomThreat() {
    const threatTypes = ['Malware', 'Phishing', 'DDoS', 'Intrusion', 'Data Exfiltration'];

    return {
      id: `threat-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: threatTypes[Math.floor(Math.random() * threatTypes.length)],
      entropy: Math.random(),
      phase: Math.random() * Math.PI * 2,
      certainty: Math.random(),
      direction: Math.random() * Math.PI * 2,
      magnitude: Math.random(),
      timestamp: new Date()
    };
  }

  _generateRandomRegulation() {
    const regulationTypes = ['GDPR', 'HIPAA', 'PCI-DSS', 'SOX', 'NIST CSF'];

    return {
      id: `regulation-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      name: regulationTypes[Math.floor(Math.random() * regulationTypes.length)],
      complexity: Math.random(),
      adaptability: Math.random(),
      resonance: Math.random(),
      environmentalPressure: Math.random(),
      timestamp: new Date()
    };
  }

  _generateRandomDecision() {
    const decisionTypes = ['Block', 'Allow', 'Quarantine', 'Alert', 'Escalate'];

    return {
      id: `decision-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type: decisionTypes[Math.floor(Math.random() * decisionTypes.length)],
      fairness: Math.random(),
      transparency: Math.random(),
      ethicalTensor: Math.random(),
      accountability: Math.random(),
      timestamp: new Date()
    };
  }
}

/**
 * Run dashboard stress test
 *
 * @param {Object} options - Test options
 * @param {number} options.duration - Test duration in seconds
 * @param {number[]} options.rates - Array of message rates to test (messages per second)
 * @returns {Object} - Test results
 */
async function runDashboardStressTest(options = {}) {
  const testOptions = {
    duration: options.duration || 10,
    rates: options.rates || [1, 5, 10, 20, 50, 100]
  };

  console.log('=== Comphyology Dashboard Stress Test ===');
  console.log(`Test duration: ${testOptions.duration} seconds per rate`);
  console.log(`Message rates: ${testOptions.rates.join(', ')} messages per second`);

  // Create NovaConnect mock
  const novaConnectMock = new NovaConnectMock();

  // Create dashboard
  const dashboard = new ComphyologyNovaConnectDashboard({
    novaVision,
    novaConnect: novaConnectMock,
    enableLogging: false,
    updateInterval: 100
  });

  // Wait for initial schemas to be generated
  console.log('Generating initial schemas...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Connect to NovaConnect
  console.log('Connecting to NovaConnect...');
  await dashboard.connect();

  // Run tests for each rate
  const results = {};

  for (const rate of testOptions.rates) {
    console.log(`\nTesting with rate: ${rate} messages per second`);

    // Start publishing at the current rate
    novaConnectMock.startPublishing(rate);

    // Reset performance metrics
    dashboard.performanceMetrics = {
      frameRates: [],
      dataPoints: 0,
      updateTimes: [],
      renderTimes: []
    };

    // Run for the specified duration
    console.log(`Running for ${testOptions.duration} seconds...`);
    await new Promise(resolve => setTimeout(resolve, testOptions.duration * 1000));

    // Get performance metrics
    const metrics = dashboard.getPerformanceMetrics();

    // Store results
    results[rate] = {
      messageRate: rate,
      averageFrameRate: metrics.averageFrameRate,
      averageUpdateTime: metrics.averageUpdateTime,
      dataPoints: metrics.dataPoints,
      updateCount: metrics.updateCount
    };

    console.log(`Results for rate ${rate}:`);
    console.log(`  Average Frame Rate: ${metrics.averageFrameRate.toFixed(2)} fps`);
    console.log(`  Average Update Time: ${metrics.averageUpdateTime.toFixed(2)} ms`);
    console.log(`  Data Points: ${metrics.dataPoints}`);
    console.log(`  Update Count: ${metrics.updateCount}`);

    // Stop publishing
    novaConnectMock.stopPublishing();

    // Wait for a moment before the next test
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Disconnect from NovaConnect
  console.log('\nDisconnecting from NovaConnect...');
  await dashboard.disconnect();

  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `dashboard_stress_test_results_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));

  console.log(`\nResults saved to ${resultFile}`);

  // Generate HTML report
  const htmlReport = generateHtmlReport(results);
  const htmlFile = path.join(RESULTS_DIR, `dashboard_stress_test_report_${new Date().toISOString().replace(/:/g, '-')}.html`);
  fs.writeFileSync(htmlFile, htmlReport);

  console.log(`HTML report saved to ${htmlFile}`);

  // Print summary
  console.log('\n=== Dashboard Stress Test Summary ===');
  console.log('Message Rate | Frame Rate | Update Time | Efficiency');
  console.log('--------------------------------------------------');

  for (const rate of testOptions.rates) {
    const result = results[rate];
    const efficiency = (result.averageFrameRate / rate * 100).toFixed(2);

    console.log(`${result.messageRate.toString().padStart(12)} | ${result.averageFrameRate.toFixed(2).padStart(10)} | ${result.averageUpdateTime.toFixed(2).padStart(11)} | ${efficiency.padStart(10)}%`);
  }

  return results;
}

/**
 * Generate HTML report
 *
 * @param {Object} results - Test results
 * @returns {string} - HTML report
 */
function generateHtmlReport(results) {
  const rates = Object.keys(results).map(Number).sort((a, b) => a - b);
  const frameRates = rates.map(rate => results[rate].averageFrameRate);
  const updateTimes = rates.map(rate => results[rate].averageUpdateTime);
  const efficiencies = rates.map(rate => results[rate].averageFrameRate / rate * 100);

  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comphyology Dashboard Stress Test Report</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }

    h1, h2 {
      color: #333;
    }

    .chart-container {
      height: 400px;
      margin-bottom: 30px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }

    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #f2f2f2;
    }

    tr:hover {
      background-color: #f9f9f9;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Comphyology Dashboard Stress Test Report</h1>
    <p>Generated on ${new Date().toLocaleString()}</p>

    <h2>Frame Rate vs. Message Rate</h2>
    <div class="chart-container">
      <canvas id="frameRateChart"></canvas>
    </div>

    <h2>Update Time vs. Message Rate</h2>
    <div class="chart-container">
      <canvas id="updateTimeChart"></canvas>
    </div>

    <h2>Efficiency vs. Message Rate</h2>
    <div class="chart-container">
      <canvas id="efficiencyChart"></canvas>
    </div>

    <h2>Detailed Results</h2>
    <table>
      <thead>
        <tr>
          <th>Message Rate (msg/s)</th>
          <th>Frame Rate (fps)</th>
          <th>Update Time (ms)</th>
          <th>Efficiency (%)</th>
          <th>Data Points</th>
          <th>Update Count</th>
        </tr>
      </thead>
      <tbody>
        ${rates.map(rate => `
          <tr>
            <td>${rate}</td>
            <td>${results[rate].averageFrameRate.toFixed(2)}</td>
            <td>${results[rate].averageUpdateTime.toFixed(2)}</td>
            <td>${(results[rate].averageFrameRate / rate * 100).toFixed(2)}</td>
            <td>${results[rate].dataPoints}</td>
            <td>${results[rate].updateCount}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  </div>

  <script>
    // Frame Rate Chart
    const frameRateCtx = document.getElementById('frameRateChart').getContext('2d');
    new Chart(frameRateCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(rates)},
        datasets: [{
          label: 'Frame Rate (fps)',
          data: ${JSON.stringify(frameRates)},
          borderColor: '#4CAF50',
          backgroundColor: 'rgba(76, 175, 80, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Message Rate (msg/s)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Frame Rate (fps)'
            },
            beginAtZero: true
          }
        }
      }
    });

    // Update Time Chart
    const updateTimeCtx = document.getElementById('updateTimeChart').getContext('2d');
    new Chart(updateTimeCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(rates)},
        datasets: [{
          label: 'Update Time (ms)',
          data: ${JSON.stringify(updateTimes)},
          borderColor: '#2196F3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Message Rate (msg/s)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Update Time (ms)'
            },
            beginAtZero: true
          }
        }
      }
    });

    // Efficiency Chart
    const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
    new Chart(efficiencyCtx, {
      type: 'line',
      data: {
        labels: ${JSON.stringify(rates)},
        datasets: [{
          label: 'Efficiency (%)',
          data: ${JSON.stringify(efficiencies)},
          borderColor: '#FF9800',
          backgroundColor: 'rgba(255, 152, 0, 0.1)',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            title: {
              display: true,
              text: 'Message Rate (msg/s)'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Efficiency (%)'
            },
            beginAtZero: true
          }
        }
      }
    });
  </script>
</body>
</html>
  `;
}

// Run the test
if (require.main === module) {
  runDashboardStressTest({
    duration: 2,
    rates: [1, 5, 10, 20]
  }).catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runDashboardStressTest
};
