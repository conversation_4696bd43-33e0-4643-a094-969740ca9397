/**
 * Validation Middleware
 * 
 * This middleware provides request validation for API endpoints:
 * - Schema-based validation for request body, query, and params
 * - Consistent error handling
 * - Support for Joi validation schemas
 */

const Joi = require('joi');
const { ValidationError } = require('../utils/enhancedErrors');

/**
 * Validate request against schema
 * 
 * @param {Object} schema - Validation schema
 * @returns {Function} - Express middleware
 */
const validate = (schema) => {
  return (req, res, next) => {
    const validationErrors = {};
    
    // Validate request body
    if (schema.body) {
      const { error, value } = schema.body.validate(req.body, {
        abortEarly: false,
        stripUnknown: true
      });
      
      if (error) {
        validationErrors.body = error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        }));
      } else {
        req.body = value;
      }
    }
    
    // Validate request query
    if (schema.query) {
      const { error, value } = schema.query.validate(req.query, {
        abortEarly: false,
        stripUnknown: true
      });
      
      if (error) {
        validationErrors.query = error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        }));
      } else {
        req.query = value;
      }
    }
    
    // Validate request params
    if (schema.params) {
      const { error, value } = schema.params.validate(req.params, {
        abortEarly: false,
        stripUnknown: true
      });
      
      if (error) {
        validationErrors.params = error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        }));
      } else {
        req.params = value;
      }
    }
    
    // If there are validation errors, return error response
    if (Object.keys(validationErrors).length > 0) {
      const validationError = new ValidationError('Validation failed', validationErrors);
      return next(validationError);
    }
    
    next();
  };
};

/**
 * Validate array of items against schema
 * 
 * @param {Object} schema - Validation schema
 * @param {string} arrayPath - Path to array in request body
 * @returns {Function} - Express middleware
 */
const validateArray = (schema, arrayPath) => {
  return (req, res, next) => {
    // Get array from request body
    const array = arrayPath.split('.').reduce((obj, path) => {
      return obj && obj[path] ? obj[path] : null;
    }, req.body);
    
    // If array is not found, return error
    if (!array || !Array.isArray(array)) {
      const validationError = new ValidationError(`Validation failed: ${arrayPath} must be an array`, {
        body: [{
          path: arrayPath,
          message: `${arrayPath} must be an array`,
          type: 'array.base'
        }]
      });
      return next(validationError);
    }
    
    // Validate each item in array
    const validationErrors = [];
    
    array.forEach((item, index) => {
      const { error } = schema.validate(item, {
        abortEarly: false,
        stripUnknown: true
      });
      
      if (error) {
        error.details.forEach(detail => {
          validationErrors.push({
            path: `${arrayPath}[${index}].${detail.path.join('.')}`,
            message: detail.message,
            type: detail.type
          });
        });
      }
    });
    
    // If there are validation errors, return error response
    if (validationErrors.length > 0) {
      const validationError = new ValidationError('Validation failed', {
        body: validationErrors
      });
      return next(validationError);
    }
    
    next();
  };
};

/**
 * Validate conditional schema based on request property
 * 
 * @param {Function} schemaSelector - Function that returns schema based on request
 * @returns {Function} - Express middleware
 */
const validateConditional = (schemaSelector) => {
  return (req, res, next) => {
    // Get schema based on request
    const schema = schemaSelector(req);
    
    // If schema is not found, skip validation
    if (!schema) {
      return next();
    }
    
    // Validate request against schema
    const validationMiddleware = validate(schema);
    validationMiddleware(req, res, next);
  };
};

/**
 * Validate request ID parameter
 * 
 * @param {string} paramName - Name of ID parameter
 * @param {Object} options - Validation options
 * @returns {Function} - Express middleware
 */
const validateId = (paramName, options = {}) => {
  const idSchema = Joi.object({
    [paramName]: options.type === 'uuid'
      ? Joi.string().uuid().required()
      : Joi.string().pattern(/^[a-f0-9]{24}$/).required()
  });
  
  return (req, res, next) => {
    const { error } = idSchema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      const validationError = new ValidationError(`Invalid ${paramName}`, {
        params: error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        }))
      });
      return next(validationError);
    }
    
    next();
  };
};

/**
 * Validate pagination parameters
 * 
 * @param {Object} options - Pagination options
 * @returns {Function} - Express middleware
 */
const validatePagination = (options = {}) => {
  const paginationSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(options.maxLimit || 100).default(options.defaultLimit || 10),
    sortBy: options.sortFields
      ? Joi.string().valid(...options.sortFields).default(options.defaultSortField || options.sortFields[0])
      : Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc')
  });
  
  return (req, res, next) => {
    const { error, value } = paginationSchema.validate(req.query, {
      abortEarly: false,
      stripUnknown: false
    });
    
    if (error) {
      const validationError = new ValidationError('Invalid pagination parameters', {
        query: error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        }))
      });
      return next(validationError);
    }
    
    // Add pagination parameters to request
    req.pagination = {
      page: value.page,
      limit: value.limit,
      skip: (value.page - 1) * value.limit,
      sortBy: value.sortBy,
      sortOrder: value.sortOrder
    };
    
    next();
  };
};

/**
 * Validate date range parameters
 * 
 * @param {Object} options - Date range options
 * @returns {Function} - Express middleware
 */
const validateDateRange = (options = {}) => {
  const dateRangeSchema = Joi.object({
    startDate: options.required
      ? Joi.date().iso().required()
      : Joi.date().iso().optional(),
    endDate: options.required
      ? Joi.date().iso().min(Joi.ref('startDate')).required()
      : Joi.date().iso().min(Joi.ref('startDate')).optional()
  });
  
  return (req, res, next) => {
    const { error, value } = dateRangeSchema.validate(req.query, {
      abortEarly: false,
      stripUnknown: false
    });
    
    if (error) {
      const validationError = new ValidationError('Invalid date range parameters', {
        query: error.details.map(detail => ({
          path: detail.path.join('.'),
          message: detail.message,
          type: detail.type
        }))
      });
      return next(validationError);
    }
    
    // Add date range parameters to request
    if (value.startDate || value.endDate) {
      req.dateRange = {
        startDate: value.startDate,
        endDate: value.endDate
      };
    }
    
    next();
  };
};

module.exports = {
  validate,
  validateArray,
  validateConditional,
  validateId,
  validatePagination,
  validateDateRange
};
