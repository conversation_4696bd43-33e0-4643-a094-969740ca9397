import { motion } from 'framer-motion'

interface ChartProps {
  data: number[]
  type: 'line' | 'bar'
}

export function Chart({ data, type = 'line' }: ChartProps) {
  const maxValue = Math.max(...data)
  const minValue = Math.min(...data)
  const range = maxValue - minValue

  return (
    <div className="bg-white/5 backdrop-blur-lg rounded-lg p-4">
      <div className="h-64 relative">
        {data.map((value, index) => {
          const height = ((value - minValue) / range) * 100
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: `${height}%` }}
              className="absolute bottom-0 left-0 w-full"
              style={{
                backgroundColor: `hsl(${(index / data.length) * 360}, 70%, 50%)`,
                borderRadius: type === 'line' ? '0' : '4px'
              }}
            />
          )
        })}
      </div>
    </div>
  )
}

export function ChartBar({ data }: ChartProps) {
  return <Chart data={data} type="bar" />
}

export function ChartLine({ data }: ChartProps) {
  return <Chart data={data} type="line" />
}
