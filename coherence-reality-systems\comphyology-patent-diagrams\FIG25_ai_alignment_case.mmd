```mermaid
graph TD
    %% Title and Description
    classDef titleStyle fill:#f5f5f5,stroke:#333,stroke-width:1px,font-weight:bold,text-align:center;
    classDef component fill:#fff,stroke:#333,stroke-width:2px,color:#333;
    classDef process fill:#e6f3ff,stroke:#0066cc,stroke-width:2px;
    classDef system fill:#e6ffe6,stroke:#009933,stroke-width:2px;
    classDef interface fill:#fff2cc,stroke:#e69138,stroke-width:2px;
    
    %% Title
    title[FIG. X: AI Alignment Case Study - NovaFuse Alignment Framework]
    class title titleStyle;
    
    %% Main Components
    subgraph "NovaFuse Alignment Stack"
        A[Ethical Framework Layer] -->|Feeds| B[Alignment Constraints]
        B -->|Guides| C[Reward Shaping]
        C -->|Influences| D[Policy Optimization]
        
        E[Human Values Interface] -->|Provides| F[Value Proxies]
        F -->|Trains| G[Value Model]
        G -->|Updates| H[Alignment Parameters]
        H -->|Adjusts| B
    end
    
    subgraph "AI System"
        I[AI Model] <-->|Interacts| J[Environment]
        I -->|Receives| K[Alignment Signals]
        K <-->|From| C
        I -->|Outputs| L[Actions]
        L -->|Impacts| J
    end
    
    %% Feedback Loops
    J -->|State| M[Impact Assessment]
    M -->|Feedback| N[Alignment Monitor]
    N -->|Adjusts| H
    
    %% Component Details
    A -->|Includes| A1[Ethical Principles]
    A -->|Implements| A2[Safety Constraints]
    
    E -->|Collects| E1[Human Preferences]
    E -->|Processes| E2[Value Elicitation]
    
    I -->|Uses| I1[Reinforcement Learning]
    I -->|Maintains| I2[Uncertainty Estimates]
    
    M -->|Measures| M1[Value Alignment]
    M -->|Tracks| M2[Behavior Drift]
    
    %% Styling
    class A,B,C,D,E,F,G,H process;
    class I,J,K,L system;
    class A1,A2,E1,E2,I1,I2,M1,M2 component;
    class M,N interface;
    
    %% Legend
    legend[Legend: Alignment (Blue) | AI System (Green) | Monitoring (Gold)]
    class legend titleStyle;
```
