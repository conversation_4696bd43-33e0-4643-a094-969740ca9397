{"version": 3, "names": ["SecurityCenterClient", "require", "TransformationEngine", "SCCConnector", "constructor", "options", "enableMetrics", "maxConcurrentRequests", "pageSize", "transformationEngine", "enableCaching", "registerTransformer", "_parseSCCResourceName", "_mapSCCSeverity", "_extractSCCCategory", "metrics", "findingsRetrieved", "findingsNormalized", "apiCalls", "totalApiLatency", "averageApiLatency", "totalNormalizationTime", "averageNormalizationTime", "initialize", "credentials", "client", "projectId", "project_id", "success", "error", "console", "message", "getFindings", "params", "Error", "startTime", "Date", "now", "organizationId", "folderId", "filter", "pageToken", "orderBy", "parent", "request", "findings", "metadata", "listFindings", "endTime", "latency", "length", "nextPageToken", "totalSize", "normalizeFindings", "rules", "source", "target", "transform", "normalizedFindings", "map", "finding", "duration", "getFindingsNormalized", "getAllFindings", "allFindings", "token", "concat", "getAllFindingsNormalized", "getMetrics", "transformationMetrics", "name", "parts", "split", "severity", "severityMap", "category", "toLowerCase", "replace", "module", "exports"], "sources": ["scc-connector.js"], "sourcesContent": ["/**\n * Google Security Command Center (SCC) Connector\n * \n * Connects to Google SCC API and normalizes security findings\n * for use with NovaConnect's remediation engine.\n */\n\nconst { SecurityCenterClient } = require('@google-cloud/security-center');\nconst TransformationEngine = require('../../engines/transformation-engine');\n\nclass SCCConnector {\n  constructor(options = {}) {\n    this.options = {\n      enableMetrics: true,\n      maxConcurrentRequests: 10,\n      pageSize: 1000,\n      ...options\n    };\n    \n    this.transformationEngine = new TransformationEngine({\n      enableMetrics: this.options.enableMetrics,\n      enableCaching: true\n    });\n    \n    // Register SCC-specific transformers\n    this.transformationEngine.registerTransformer('parseSCCResourceName', this._parseSCCResourceName);\n    this.transformationEngine.registerTransformer('mapSCCSeverity', this._mapSCCSeverity);\n    this.transformationEngine.registerTransformer('extractSCCCategory', this._extractSCCCategory);\n    \n    // Initialize metrics\n    this.metrics = {\n      findingsRetrieved: 0,\n      findingsNormalized: 0,\n      apiCalls: 0,\n      totalApiLatency: 0,\n      averageApiLatency: 0,\n      totalNormalizationTime: 0,\n      averageNormalizationTime: 0\n    };\n  }\n  \n  /**\n   * Initialize the SCC client with credentials\n   * @param {Object} credentials - GCP credentials\n   */\n  async initialize(credentials) {\n    try {\n      this.client = new SecurityCenterClient({\n        credentials: credentials,\n        projectId: credentials.project_id\n      });\n      \n      return { success: true };\n    } catch (error) {\n      console.error('Error initializing SCC client:', error);\n      return { \n        success: false, \n        error: error.message \n      };\n    }\n  }\n  \n  /**\n   * Get findings from Security Command Center\n   * @param {Object} params - Query parameters\n   * @returns {Object} - Findings and metadata\n   */\n  async getFindings(params = {}) {\n    if (!this.client) {\n      throw new Error('SCC client not initialized. Call initialize() first.');\n    }\n    \n    const startTime = this.options.enableMetrics ? Date.now() : 0;\n    \n    try {\n      const {\n        organizationId,\n        projectId,\n        folderId,\n        filter = '',\n        pageSize = this.options.pageSize,\n        pageToken,\n        orderBy\n      } = params;\n      \n      let parent;\n      if (organizationId) {\n        parent = `organizations/${organizationId}`;\n      } else if (projectId) {\n        parent = `projects/${projectId}`;\n      } else if (folderId) {\n        parent = `folders/${folderId}`;\n      } else {\n        throw new Error('One of organizationId, projectId, or folderId must be provided');\n      }\n      \n      // Build the request\n      const request = {\n        parent,\n        filter,\n        pageSize,\n        pageToken,\n        orderBy\n      };\n      \n      // Make the API call\n      const [findings, metadata] = await this.client.listFindings(request);\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = Date.now();\n        const latency = endTime - startTime;\n        \n        this.metrics.apiCalls++;\n        this.metrics.findingsRetrieved += findings.length;\n        this.metrics.totalApiLatency += latency;\n        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;\n      }\n      \n      return {\n        findings,\n        nextPageToken: metadata.nextPageToken,\n        totalSize: metadata.totalSize\n      };\n    } catch (error) {\n      console.error('Error retrieving findings from SCC:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Normalize SCC findings to NovaConnect format\n   * @param {Array} findings - SCC findings\n   * @returns {Array} - Normalized findings\n   */\n  normalizeFindings(findings) {\n    const startTime = this.options.enableMetrics ? Date.now() : 0;\n    \n    try {\n      // Define transformation rules for SCC findings\n      const rules = [\n        { source: 'name', target: 'id', transform: 'parseSCCResourceName' },\n        { source: 'category', target: 'category', transform: 'extractSCCCategory' },\n        { source: 'severity', target: 'severity', transform: 'mapSCCSeverity' },\n        { source: 'resourceName', target: 'resourceName' },\n        { source: 'state', target: 'state', transform: 'lowercase' },\n        { source: 'eventTime', target: 'eventTime' },\n        { source: 'createTime', target: 'createdAt', transform: 'isoToUnix' },\n        { source: 'sourceProperties.finding_type', target: 'type' },\n        { source: 'sourceProperties.finding_description', target: 'description' },\n        { source: 'sourceProperties.finding_id', target: 'externalId' },\n        { source: 'securityMarks.marks', target: 'tags' }\n      ];\n      \n      // Transform each finding\n      const normalizedFindings = findings.map(finding => \n        this.transformationEngine.transform(finding, rules)\n      );\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = Date.now();\n        const duration = endTime - startTime;\n        \n        this.metrics.findingsNormalized += findings.length;\n        this.metrics.totalNormalizationTime += duration;\n        this.metrics.averageNormalizationTime = \n          this.metrics.totalNormalizationTime / this.metrics.findingsNormalized;\n      }\n      \n      return normalizedFindings;\n    } catch (error) {\n      console.error('Error normalizing SCC findings:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get and normalize findings in a single call\n   * @param {Object} params - Query parameters\n   * @returns {Object} - Normalized findings and metadata\n   */\n  async getFindingsNormalized(params = {}) {\n    const { findings, nextPageToken, totalSize } = await this.getFindings(params);\n    const normalizedFindings = this.normalizeFindings(findings);\n    \n    return {\n      findings: normalizedFindings,\n      nextPageToken,\n      totalSize\n    };\n  }\n  \n  /**\n   * Get all findings with pagination handling\n   * @param {Object} params - Query parameters\n   * @returns {Array} - All findings\n   */\n  async getAllFindings(params = {}) {\n    let allFindings = [];\n    let nextPageToken = null;\n    \n    do {\n      const { findings, nextPageToken: token } = await this.getFindings({\n        ...params,\n        pageToken: nextPageToken\n      });\n      \n      allFindings = allFindings.concat(findings);\n      nextPageToken = token;\n    } while (nextPageToken);\n    \n    return allFindings;\n  }\n  \n  /**\n   * Get all normalized findings with pagination handling\n   * @param {Object} params - Query parameters\n   * @returns {Array} - All normalized findings\n   */\n  async getAllFindingsNormalized(params = {}) {\n    const findings = await this.getAllFindings(params);\n    return this.normalizeFindings(findings);\n  }\n  \n  /**\n   * Get metrics for the connector\n   * @returns {Object} - Metrics\n   */\n  getMetrics() {\n    return {\n      ...this.metrics,\n      transformationMetrics: this.transformationEngine.getMetrics()\n    };\n  }\n  \n  /**\n   * Parse SCC resource name to extract ID\n   * @private\n   */\n  _parseSCCResourceName(name) {\n    if (!name) return '';\n    \n    // Extract the finding ID from the name\n    // Format: organizations/{organization_id}/sources/{source_id}/findings/{finding_id}\n    const parts = name.split('/');\n    return parts[parts.length - 1];\n  }\n  \n  /**\n   * Map SCC severity to normalized severity\n   * @private\n   */\n  _mapSCCSeverity(severity) {\n    const severityMap = {\n      'CRITICAL': 'critical',\n      'HIGH': 'high',\n      'MEDIUM': 'medium',\n      'LOW': 'low'\n    };\n    \n    return severityMap[severity] || 'unknown';\n  }\n  \n  /**\n   * Extract category from SCC finding\n   * @private\n   */\n  _extractSCCCategory(category) {\n    if (!category) return 'unknown';\n    \n    // Convert to lowercase and remove spaces\n    return category.toLowerCase().replace(/\\s+/g, '_');\n  }\n}\n\nmodule.exports = SCCConnector;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAqB,CAAC,GAAGC,OAAO,CAAC,+BAA+B,CAAC;AACzE,MAAMC,oBAAoB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AAE3E,MAAME,YAAY,CAAC;EACjBC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACA,OAAO,GAAG;MACbC,aAAa,EAAE,IAAI;MACnBC,qBAAqB,EAAE,EAAE;MACzBC,QAAQ,EAAE,IAAI;MACd,GAAGH;IACL,CAAC;IAED,IAAI,CAACI,oBAAoB,GAAG,IAAIP,oBAAoB,CAAC;MACnDI,aAAa,EAAE,IAAI,CAACD,OAAO,CAACC,aAAa;MACzCI,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACD,oBAAoB,CAACE,mBAAmB,CAAC,sBAAsB,EAAE,IAAI,CAACC,qBAAqB,CAAC;IACjG,IAAI,CAACH,oBAAoB,CAACE,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAACE,eAAe,CAAC;IACrF,IAAI,CAACJ,oBAAoB,CAACE,mBAAmB,CAAC,oBAAoB,EAAE,IAAI,CAACG,mBAAmB,CAAC;;IAE7F;IACA,IAAI,CAACC,OAAO,GAAG;MACbC,iBAAiB,EAAE,CAAC;MACpBC,kBAAkB,EAAE,CAAC;MACrBC,QAAQ,EAAE,CAAC;MACXC,eAAe,EAAE,CAAC;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,sBAAsB,EAAE,CAAC;MACzBC,wBAAwB,EAAE;IAC5B,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACE,MAAMC,UAAUA,CAACC,WAAW,EAAE;IAC5B,IAAI;MACF,IAAI,CAACC,MAAM,GAAG,IAAIzB,oBAAoB,CAAC;QACrCwB,WAAW,EAAEA,WAAW;QACxBE,SAAS,EAAEF,WAAW,CAACG;MACzB,CAAC,CAAC;MAEF,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO;QACLD,OAAO,EAAE,KAAK;QACdC,KAAK,EAAEA,KAAK,CAACE;MACf,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,WAAWA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC7B,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE;MAChB,MAAM,IAAIS,KAAK,CAAC,sDAAsD,CAAC;IACzE;IAEA,MAAMC,SAAS,GAAG,IAAI,CAAC9B,OAAO,CAACC,aAAa,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE7D,IAAI;MACF,MAAM;QACJC,cAAc;QACdZ,SAAS;QACTa,QAAQ;QACRC,MAAM,GAAG,EAAE;QACXhC,QAAQ,GAAG,IAAI,CAACH,OAAO,CAACG,QAAQ;QAChCiC,SAAS;QACTC;MACF,CAAC,GAAGT,MAAM;MAEV,IAAIU,MAAM;MACV,IAAIL,cAAc,EAAE;QAClBK,MAAM,GAAG,iBAAiBL,cAAc,EAAE;MAC5C,CAAC,MAAM,IAAIZ,SAAS,EAAE;QACpBiB,MAAM,GAAG,YAAYjB,SAAS,EAAE;MAClC,CAAC,MAAM,IAAIa,QAAQ,EAAE;QACnBI,MAAM,GAAG,WAAWJ,QAAQ,EAAE;MAChC,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAAC,gEAAgE,CAAC;MACnF;;MAEA;MACA,MAAMU,OAAO,GAAG;QACdD,MAAM;QACNH,MAAM;QACNhC,QAAQ;QACRiC,SAAS;QACTC;MACF,CAAC;;MAED;MACA,MAAM,CAACG,QAAQ,EAAEC,QAAQ,CAAC,GAAG,MAAM,IAAI,CAACrB,MAAM,CAACsB,YAAY,CAACH,OAAO,CAAC;;MAEpE;MACA,IAAI,IAAI,CAACvC,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM0C,OAAO,GAAGZ,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B,MAAMY,OAAO,GAAGD,OAAO,GAAGb,SAAS;QAEnC,IAAI,CAACpB,OAAO,CAACG,QAAQ,EAAE;QACvB,IAAI,CAACH,OAAO,CAACC,iBAAiB,IAAI6B,QAAQ,CAACK,MAAM;QACjD,IAAI,CAACnC,OAAO,CAACI,eAAe,IAAI8B,OAAO;QACvC,IAAI,CAAClC,OAAO,CAACK,iBAAiB,GAAG,IAAI,CAACL,OAAO,CAACI,eAAe,GAAG,IAAI,CAACJ,OAAO,CAACG,QAAQ;MACvF;MAEA,OAAO;QACL2B,QAAQ;QACRM,aAAa,EAAEL,QAAQ,CAACK,aAAa;QACrCC,SAAS,EAAEN,QAAQ,CAACM;MACtB,CAAC;IACH,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEwB,iBAAiBA,CAACR,QAAQ,EAAE;IAC1B,MAAMV,SAAS,GAAG,IAAI,CAAC9B,OAAO,CAACC,aAAa,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;IAE7D,IAAI;MACF;MACA,MAAMiB,KAAK,GAAG,CACZ;QAAEC,MAAM,EAAE,MAAM;QAAEC,MAAM,EAAE,IAAI;QAAEC,SAAS,EAAE;MAAuB,CAAC,EACnE;QAAEF,MAAM,EAAE,UAAU;QAAEC,MAAM,EAAE,UAAU;QAAEC,SAAS,EAAE;MAAqB,CAAC,EAC3E;QAAEF,MAAM,EAAE,UAAU;QAAEC,MAAM,EAAE,UAAU;QAAEC,SAAS,EAAE;MAAiB,CAAC,EACvE;QAAEF,MAAM,EAAE,cAAc;QAAEC,MAAM,EAAE;MAAe,CAAC,EAClD;QAAED,MAAM,EAAE,OAAO;QAAEC,MAAM,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAY,CAAC,EAC5D;QAAEF,MAAM,EAAE,WAAW;QAAEC,MAAM,EAAE;MAAY,CAAC,EAC5C;QAAED,MAAM,EAAE,YAAY;QAAEC,MAAM,EAAE,WAAW;QAAEC,SAAS,EAAE;MAAY,CAAC,EACrE;QAAEF,MAAM,EAAE,+BAA+B;QAAEC,MAAM,EAAE;MAAO,CAAC,EAC3D;QAAED,MAAM,EAAE,sCAAsC;QAAEC,MAAM,EAAE;MAAc,CAAC,EACzE;QAAED,MAAM,EAAE,6BAA6B;QAAEC,MAAM,EAAE;MAAa,CAAC,EAC/D;QAAED,MAAM,EAAE,qBAAqB;QAAEC,MAAM,EAAE;MAAO,CAAC,CAClD;;MAED;MACA,MAAME,kBAAkB,GAAGb,QAAQ,CAACc,GAAG,CAACC,OAAO,IAC7C,IAAI,CAACnD,oBAAoB,CAACgD,SAAS,CAACG,OAAO,EAAEN,KAAK,CACpD,CAAC;;MAED;MACA,IAAI,IAAI,CAACjD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM0C,OAAO,GAAGZ,IAAI,CAACC,GAAG,CAAC,CAAC;QAC1B,MAAMwB,QAAQ,GAAGb,OAAO,GAAGb,SAAS;QAEpC,IAAI,CAACpB,OAAO,CAACE,kBAAkB,IAAI4B,QAAQ,CAACK,MAAM;QAClD,IAAI,CAACnC,OAAO,CAACM,sBAAsB,IAAIwC,QAAQ;QAC/C,IAAI,CAAC9C,OAAO,CAACO,wBAAwB,GACnC,IAAI,CAACP,OAAO,CAACM,sBAAsB,GAAG,IAAI,CAACN,OAAO,CAACE,kBAAkB;MACzE;MAEA,OAAOyC,kBAAkB;IAC3B,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMiC,qBAAqBA,CAAC7B,MAAM,GAAG,CAAC,CAAC,EAAE;IACvC,MAAM;MAAEY,QAAQ;MAAEM,aAAa;MAAEC;IAAU,CAAC,GAAG,MAAM,IAAI,CAACpB,WAAW,CAACC,MAAM,CAAC;IAC7E,MAAMyB,kBAAkB,GAAG,IAAI,CAACL,iBAAiB,CAACR,QAAQ,CAAC;IAE3D,OAAO;MACLA,QAAQ,EAAEa,kBAAkB;MAC5BP,aAAa;MACbC;IACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMW,cAAcA,CAAC9B,MAAM,GAAG,CAAC,CAAC,EAAE;IAChC,IAAI+B,WAAW,GAAG,EAAE;IACpB,IAAIb,aAAa,GAAG,IAAI;IAExB,GAAG;MACD,MAAM;QAAEN,QAAQ;QAAEM,aAAa,EAAEc;MAAM,CAAC,GAAG,MAAM,IAAI,CAACjC,WAAW,CAAC;QAChE,GAAGC,MAAM;QACTQ,SAAS,EAAEU;MACb,CAAC,CAAC;MAEFa,WAAW,GAAGA,WAAW,CAACE,MAAM,CAACrB,QAAQ,CAAC;MAC1CM,aAAa,GAAGc,KAAK;IACvB,CAAC,QAAQd,aAAa;IAEtB,OAAOa,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMG,wBAAwBA,CAAClC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1C,MAAMY,QAAQ,GAAG,MAAM,IAAI,CAACkB,cAAc,CAAC9B,MAAM,CAAC;IAClD,OAAO,IAAI,CAACoB,iBAAiB,CAACR,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;AACA;EACEuB,UAAUA,CAAA,EAAG;IACX,OAAO;MACL,GAAG,IAAI,CAACrD,OAAO;MACfsD,qBAAqB,EAAE,IAAI,CAAC5D,oBAAoB,CAAC2D,UAAU,CAAC;IAC9D,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACExD,qBAAqBA,CAAC0D,IAAI,EAAE;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;;IAEpB;IACA;IACA,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7B,OAAOD,KAAK,CAACA,KAAK,CAACrB,MAAM,GAAG,CAAC,CAAC;EAChC;;EAEA;AACF;AACA;AACA;EACErC,eAAeA,CAAC4D,QAAQ,EAAE;IACxB,MAAMC,WAAW,GAAG;MAClB,UAAU,EAAE,UAAU;MACtB,MAAM,EAAE,MAAM;MACd,QAAQ,EAAE,QAAQ;MAClB,KAAK,EAAE;IACT,CAAC;IAED,OAAOA,WAAW,CAACD,QAAQ,CAAC,IAAI,SAAS;EAC3C;;EAEA;AACF;AACA;AACA;EACE3D,mBAAmBA,CAAC6D,QAAQ,EAAE;IAC5B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;;IAE/B;IACA,OAAOA,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACpD;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG5E,YAAY", "ignoreList": []}