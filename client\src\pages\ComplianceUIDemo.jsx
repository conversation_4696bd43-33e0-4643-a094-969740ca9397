import React, { useState } from 'react';
import ComplianceAutoForm from '../components/ComplianceAutoForm';
import { Tab, Tabs, TabList, TabPanel } from 'react-tabs';
import 'react-tabs/style/react-tabs.css';

/**
 * Compliance UI Demo Page
 * 
 * A demo page to showcase the compliance UI capabilities.
 */
const ComplianceUIDemo = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [formResult, setFormResult] = useState(null);
  
  // Sample user roles
  const userRoles = [
    { id: 'admin', name: 'Admin', role: 'Admin' },
    { id: 'privacy_officer', name: '<PERSON>', role: 'PrivacyOfficer' },
    { id: 'clinician', name: 'Dr. <PERSON>', role: 'Clinician' },
    { id: 'auditor', name: 'Alex Auditor', role: 'Auditor' },
    { id: 'user', name: 'Regular User', role: 'User' }
  ];
  
  const [selectedUser, setSelectedUser] = useState(userRoles[0]);
  
  // Sample schemas with compliance modes
  const schemas = {
    hipaa_patient_data: {
      entity: 'PatientData',
      entityPlural: 'PatientData',
      apiEndpoint: '/api/v1/patient-data',
      compliance_mode: 'HIPAA',
      fields: [
        {
          name: 'patientName',
          label: 'Patient Name',
          type: 'text',
          required: true,
          tags: ['phi'],
          description: 'Full name of the patient'
        },
        {
          name: 'patientDOB',
          label: 'Date of Birth',
          type: 'date',
          required: true,
          tags: ['phi'],
          description: 'Patient date of birth'
        },
        {
          name: 'patientSSN',
          label: 'Social Security Number',
          type: 'text',
          required: true,
          tags: ['phi', 'phi_export'],
          description: 'Patient SSN (last 4 digits)'
        },
        {
          name: 'medicalCondition',
          label: 'Medical Condition',
          type: 'textarea',
          required: true,
          tags: ['phi'],
          description: 'Primary medical condition'
        },
        {
          name: 'treatmentPlan',
          label: 'Treatment Plan',
          type: 'textarea',
          required: true,
          tags: ['phi'],
          description: 'Prescribed treatment plan'
        },
        {
          name: 'exportData',
          label: 'Export Patient Data',
          type: 'checkbox',
          required: false,
          tags: ['phi_export'],
          description: 'Export patient data for external use'
        }
      ],
      submitLabel: 'Save Patient Data'
    },
    soc2_evidence: {
      entity: 'Evidence',
      entityPlural: 'Evidence',
      apiEndpoint: '/api/v1/evidence',
      compliance_mode: 'SOC2',
      fields: [
        {
          name: 'evidenceName',
          label: 'Evidence Name',
          type: 'text',
          required: true,
          description: 'Name of the evidence'
        },
        {
          name: 'evidenceType',
          label: 'Evidence Type',
          type: 'select',
          options: [
            { label: 'Screenshot', value: 'screenshot' },
            { label: 'Document', value: 'document' },
            { label: 'Configuration', value: 'configuration' },
            { label: 'Log', value: 'log' },
            { label: 'Report', value: 'report' }
          ],
          required: true,
          description: 'Type of evidence'
        },
        {
          name: 'controlId',
          label: 'Control ID',
          type: 'text',
          required: true,
          description: 'ID of the control this evidence supports'
        },
        {
          name: 'description',
          label: 'Description',
          type: 'textarea',
          required: true,
          description: 'Description of the evidence'
        },
        {
          name: 'collectionDate',
          label: 'Collection Date',
          type: 'date',
          required: true,
          description: 'Date the evidence was collected'
        },
        {
          name: 'modifyEvidence',
          label: 'Modify Existing Evidence',
          type: 'checkbox',
          required: false,
          tags: ['evidence_modify'],
          description: 'Check to modify existing evidence'
        },
        {
          name: 'deleteEvidence',
          label: 'Delete Evidence',
          type: 'checkbox',
          required: false,
          tags: ['evidence_modify'],
          description: 'Check to delete this evidence'
        }
      ],
      submitLabel: 'Submit Evidence'
    },
    gdpr_consent: {
      entity: 'Consent',
      entityPlural: 'Consents',
      apiEndpoint: '/api/v1/consents',
      compliance_mode: 'GDPR',
      fields: [
        {
          name: 'fullName',
          label: 'Full Name',
          type: 'text',
          required: true,
          tags: ['pii'],
          description: 'Your full name'
        },
        {
          name: 'email',
          label: 'Email Address',
          type: 'email',
          required: true,
          tags: ['pii'],
          description: 'Your email address'
        },
        {
          name: 'marketingConsent',
          label: 'Marketing Communications',
          type: 'checkbox',
          required: false,
          description: 'I consent to receiving marketing communications'
        },
        {
          name: 'dataProcessingConsent',
          label: 'Data Processing',
          type: 'checkbox',
          required: true,
          description: 'I consent to the processing of my personal data as described in the privacy policy'
        },
        {
          name: 'thirdPartySharing',
          label: 'Third Party Sharing',
          type: 'checkbox',
          required: false,
          description: 'I consent to sharing my data with third parties'
        },
        {
          name: 'retentionPeriod',
          label: 'Data Retention Period',
          type: 'select',
          options: [
            { label: '1 Year', value: '1_year' },
            { label: '3 Years', value: '3_years' },
            { label: '5 Years', value: '5_years' },
            { label: 'Until Consent Withdrawn', value: 'until_withdrawn' }
          ],
          required: true,
          description: 'How long we will retain your data'
        },
        {
          name: 'exportConsent',
          label: 'Export Consent Data',
          type: 'checkbox',
          required: false,
          tags: ['pii_export'],
          description: 'Export consent data'
        }
      ],
      submitLabel: 'Submit Consent'
    }
  };
  
  // Handle form submission
  const handleFormSubmit = (data) => {
    setFormResult({
      data,
      timestamp: new Date().toISOString(),
      user: selectedUser,
      compliance: activeTab === 0 ? 'HIPAA' : activeTab === 1 ? 'SOC2' : 'GDPR'
    });
    
    console.log('Form submitted:', data);
  };
  
  // Handle user change
  const handleUserChange = (e) => {
    const userId = e.target.value;
    const user = userRoles.find(u => u.id === userId);
    setSelectedUser(user);
    setFormResult(null);
  };
  
  return (
    <div className="container mt-4">
      <h1 className="mb-4">NovaFuse Compliance UI Demo</h1>
      <p className="lead">
        This demo showcases how the Universal UI automatically adapts to different compliance frameworks.
        The UI dynamically changes based on the user's role and the compliance requirements.
      </p>
      
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="card">
            <div className="card-header bg-primary text-white">
              <h3 className="card-title mb-0">User Selection</h3>
            </div>
            <div className="card-body">
              <div className="form-group">
                <label htmlFor="userRole">Select User Role:</label>
                <select
                  id="userRole"
                  className="form-control"
                  value={selectedUser.id}
                  onChange={handleUserChange}
                >
                  {userRoles.map(user => (
                    <option key={user.id} value={user.id}>
                      {user.name} ({user.role})
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="alert alert-info">
                <strong>Current User:</strong> {selectedUser.name}<br />
                <strong>Role:</strong> {selectedUser.role}<br />
                <small>The UI will adapt based on this user's role and permissions.</small>
              </div>
            </div>
          </div>
        </div>
        
        <div className="col-md-6">
          <div className="card">
            <div className="card-header bg-success text-white">
              <h3 className="card-title mb-0">How It Works</h3>
            </div>
            <div className="card-body">
              <ol>
                <li>Select a user role from the dropdown</li>
                <li>Choose a compliance framework tab below</li>
                <li>Notice how the form adapts based on the user's role and compliance requirements</li>
                <li>Submit the form to see the compliance audit trail</li>
              </ol>
              <p>
                <strong>Key Features:</strong>
              </p>
              <ul>
                <li>Role-based field visibility</li>
                <li>Compliance-specific validations</li>
                <li>Automatic audit logging</li>
                <li>Blockchain verification</li>
                <li>Framework-specific UI components</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <Tabs selectedIndex={activeTab} onSelect={index => setActiveTab(index)}>
        <TabList>
          <Tab>HIPAA Compliance</Tab>
          <Tab>SOC 2 Compliance</Tab>
          <Tab>GDPR Compliance</Tab>
        </TabList>
        
        <TabPanel>
          <div className="row mt-4">
            <div className="col-md-7">
              <div className="card">
                <div className="card-header bg-info text-white">
                  <h3 className="card-title mb-0">HIPAA-Compliant Patient Data Form</h3>
                </div>
                <div className="card-body">
                  <ComplianceAutoForm
                    schema={schemas.hipaa_patient_data}
                    onSubmit={handleFormSubmit}
                    user={selectedUser}
                  />
                </div>
              </div>
            </div>
            
            <div className="col-md-5">
              {formResult && activeTab === 0 && (
                <div className="card">
                  <div className="card-header bg-dark text-white">
                    <h3 className="card-title mb-0">HIPAA Compliance Audit</h3>
                  </div>
                  <div className="card-body">
                    <div className="alert alert-warning">
                      <i className="fas fa-shield-alt mr-2"></i>
                      <strong>HIPAA Compliance Notice:</strong> This action has been logged in the compliance audit trail.
                    </div>
                    
                    <h5>Audit Information:</h5>
                    <ul className="list-group mb-3">
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        User
                        <span>{formResult.user.name} ({formResult.user.role})</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Action
                        <span>PHI Access</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Timestamp
                        <span>{new Date(formResult.timestamp).toLocaleString()}</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Compliance Framework
                        <span className="badge badge-primary">HIPAA</span>
                      </li>
                    </ul>
                    
                    <h5>Submitted Data:</h5>
                    <pre className="bg-light p-3 rounded">
                      {JSON.stringify(formResult.data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabPanel>
        
        <TabPanel>
          <div className="row mt-4">
            <div className="col-md-7">
              <div className="card">
                <div className="card-header bg-primary text-white">
                  <h3 className="card-title mb-0">SOC 2-Compliant Evidence Form</h3>
                </div>
                <div className="card-body">
                  <ComplianceAutoForm
                    schema={schemas.soc2_evidence}
                    onSubmit={handleFormSubmit}
                    user={selectedUser}
                  />
                </div>
              </div>
            </div>
            
            <div className="col-md-5">
              {formResult && activeTab === 1 && (
                <div className="card">
                  <div className="card-header bg-dark text-white">
                    <h3 className="card-title mb-0">SOC 2 Compliance Audit</h3>
                  </div>
                  <div className="card-body">
                    <div className="alert alert-info">
                      <i className="fas fa-link mr-2"></i>
                      <strong>Blockchain Verification:</strong> This evidence has been cryptographically verified and recorded on the blockchain.
                    </div>
                    
                    <h5>Audit Information:</h5>
                    <ul className="list-group mb-3">
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        User
                        <span>{formResult.user.name} ({formResult.user.role})</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Action
                        <span>Evidence Submission</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Timestamp
                        <span>{new Date(formResult.timestamp).toLocaleString()}</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Compliance Framework
                        <span className="badge badge-primary">SOC 2</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Merkle Root
                        <span className="text-monospace">0x8f4e2a1d...</span>
                      </li>
                    </ul>
                    
                    <h5>Submitted Evidence:</h5>
                    <pre className="bg-light p-3 rounded">
                      {JSON.stringify(formResult.data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabPanel>
        
        <TabPanel>
          <div className="row mt-4">
            <div className="col-md-7">
              <div className="card">
                <div className="card-header bg-success text-white">
                  <h3 className="card-title mb-0">GDPR-Compliant Consent Form</h3>
                </div>
                <div className="card-body">
                  <ComplianceAutoForm
                    schema={schemas.gdpr_consent}
                    onSubmit={handleFormSubmit}
                    user={selectedUser}
                  />
                </div>
              </div>
            </div>
            
            <div className="col-md-5">
              {formResult && activeTab === 2 && (
                <div className="card">
                  <div className="card-header bg-dark text-white">
                    <h3 className="card-title mb-0">GDPR Compliance Audit</h3>
                  </div>
                  <div className="card-body">
                    <div className="alert alert-success">
                      <i className="fas fa-check-circle mr-2"></i>
                      <strong>GDPR Consent Recorded:</strong> Consent has been recorded with a unique identifier and timestamp.
                    </div>
                    
                    <h5>Consent Information:</h5>
                    <ul className="list-group mb-3">
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Data Subject
                        <span>{formResult.data.fullName}</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Consent ID
                        <span className="text-monospace">GDPR-{Math.random().toString(36).substring(2, 10)}</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Timestamp
                        <span>{new Date(formResult.timestamp).toLocaleString()}</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Retention Period
                        <span>{formResult.data.retentionPeriod}</span>
                      </li>
                      <li className="list-group-item d-flex justify-content-between align-items-center">
                        Compliance Framework
                        <span className="badge badge-primary">GDPR</span>
                      </li>
                    </ul>
                    
                    <h5>Consent Details:</h5>
                    <pre className="bg-light p-3 rounded">
                      {JSON.stringify(formResult.data, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          </div>
        </TabPanel>
      </Tabs>
      
      <div className="mt-5 p-3 bg-light rounded">
        <h3>NovaFuse Cyber-Safety UI</h3>
        <p>
          This demo showcases NovaFuse's Universal UI with Cyber-Safety compliance enforcement.
          The system automatically adapts the UI based on:
        </p>
        <ul>
          <li><strong>Compliance Framework:</strong> HIPAA, SOC 2, GDPR, etc.</li>
          <li><strong>User Role:</strong> Different roles see different fields and actions</li>
          <li><strong>Data Sensitivity:</strong> Special handling for PHI, PII, and other sensitive data</li>
        </ul>
        <p>
          Key benefits:
        </p>
        <ul>
          <li><strong>Compliance by Design:</strong> UI enforces compliance rules automatically</li>
          <li><strong>Reduced Risk:</strong> Prevents unauthorized access and actions</li>
          <li><strong>Audit Trail:</strong> Comprehensive logging for compliance evidence</li>
          <li><strong>Blockchain Verification:</strong> Immutable record of compliance actions</li>
          <li><strong>No-Code Compliance:</strong> Create compliant UIs without coding</li>
        </ul>
      </div>
    </div>
  );
};

export default ComplianceUIDemo;
