/**
 * Validation Middleware
 * 
 * This module provides middleware for validating API requests.
 */

/**
 * Validate profile data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateProfile(req, res, next) {
  const { fullName, dateOfBirth, bloodType, emergencyContacts } = req.body;
  
  // Check required fields
  if (!fullName) {
    return res.status(400).json({
      status: 'error',
      error: 'Full name is required'
    });
  }
  
  if (!dateOfBirth) {
    return res.status(400).json({
      status: 'error',
      error: 'Date of birth is required'
    });
  }
  
  if (!bloodType) {
    return res.status(400).json({
      status: 'error',
      error: 'Blood type is required'
    });
  }
  
  if (!emergencyContacts || !Array.isArray(emergencyContacts) || emergencyContacts.length === 0) {
    return res.status(400).json({
      status: 'error',
      error: 'At least one emergency contact is required'
    });
  }
  
  // Validate blood type
  const validBloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown'];
  if (!validBloodTypes.includes(bloodType)) {
    return res.status(400).json({
      status: 'error',
      error: 'Invalid blood type'
    });
  }
  
  // Validate date of birth
  const dobDate = new Date(dateOfBirth);
  if (isNaN(dobDate.getTime())) {
    return res.status(400).json({
      status: 'error',
      error: 'Invalid date of birth'
    });
  }
  
  // Validate emergency contacts
  for (const contact of emergencyContacts) {
    if (!contact.name || !contact.relationship || !contact.phone) {
      return res.status(400).json({
        status: 'error',
        error: 'Emergency contacts must have name, relationship, and phone'
      });
    }
  }
  
  next();
}

/**
 * Validate access request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateAccessRequest(req, res, next) {
  const { formFactorId, accessCode, context } = req.body;
  
  // Check required fields
  if (!formFactorId) {
    return res.status(400).json({
      status: 'error',
      error: 'Form factor ID is required'
    });
  }
  
  if (!accessCode) {
    return res.status(400).json({
      status: 'error',
      error: 'Access code is required'
    });
  }
  
  // Validate context if provided
  if (context) {
    if (context.emergencyType) {
      const validEmergencyTypes = [
        'MEDICAL', 'TRAUMA', 'CARDIAC', 'RESPIRATORY', 
        'NEUROLOGICAL', 'ALLERGIC', 'VEHICULAR', 'OTHER'
      ];
      
      if (!validEmergencyTypes.includes(context.emergencyType)) {
        return res.status(400).json({
          status: 'error',
          error: 'Invalid emergency type'
        });
      }
    }
    
    if (context.emergencySeverity) {
      const validSeverities = ['LOW', 'MODERATE', 'HIGH', 'CRITICAL'];
      
      if (!validSeverities.includes(context.emergencySeverity)) {
        return res.status(400).json({
          status: 'error',
          error: 'Invalid emergency severity'
        });
      }
    }
    
    if (context.responderType) {
      const validResponderTypes = [
        'PARAMEDIC', 'EMT', 'DOCTOR', 'NURSE', 
        'FIREFIGHTER', 'POLICE', 'OTHER'
      ];
      
      if (!validResponderTypes.includes(context.responderType)) {
        return res.status(400).json({
          status: 'error',
          error: 'Invalid responder type'
        });
      }
    }
  }
  
  next();
}

/**
 * Validate override request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateOverrideRequest(req, res, next) {
  const { profileId, reason, emergencyType, severityLevel } = req.body;
  
  // Check required fields
  if (!profileId) {
    return res.status(400).json({
      status: 'error',
      error: 'Profile ID is required'
    });
  }
  
  if (!reason) {
    return res.status(400).json({
      status: 'error',
      error: 'Override reason is required'
    });
  }
  
  if (!emergencyType) {
    return res.status(400).json({
      status: 'error',
      error: 'Emergency type is required'
    });
  }
  
  // Validate emergency type
  const validEmergencyTypes = [
    'MEDICAL', 'TRAUMA', 'CARDIAC', 'RESPIRATORY', 
    'NEUROLOGICAL', 'ALLERGIC', 'VEHICULAR', 'OTHER'
  ];
  
  if (!validEmergencyTypes.includes(emergencyType)) {
    return res.status(400).json({
      status: 'error',
      error: 'Invalid emergency type'
    });
  }
  
  // Validate severity level if provided
  if (severityLevel) {
    const validSeverities = ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'];
    
    if (!validSeverities.includes(severityLevel)) {
      return res.status(400).json({
        status: 'error',
        error: 'Invalid severity level'
      });
    }
  }
  
  next();
}

/**
 * Validate form factor request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateFormFactorRequest(req, res, next) {
  const { profileId, accessLevel } = req.body;
  
  // Check required fields
  if (!profileId) {
    return res.status(400).json({
      status: 'error',
      error: 'Profile ID is required'
    });
  }
  
  // Validate access level if provided
  if (accessLevel) {
    const validAccessLevels = ['basic', 'standard', 'full'];
    
    if (!validAccessLevels.includes(accessLevel)) {
      return res.status(400).json({
        status: 'error',
        error: 'Invalid access level'
      });
    }
  }
  
  next();
}

/**
 * Validate service authentication request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateServiceAuth(req, res, next) {
  const { apiKey, apiSecret } = req.body;
  
  // Check required fields
  if (!apiKey) {
    return res.status(400).json({
      status: 'error',
      error: 'API key is required'
    });
  }
  
  if (!apiSecret) {
    return res.status(400).json({
      status: 'error',
      error: 'API secret is required'
    });
  }
  
  next();
}

/**
 * Validate user authentication request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validateUserAuth(req, res, next) {
  const { email, password } = req.body;
  
  // Check required fields
  if (!email) {
    return res.status(400).json({
      status: 'error',
      error: 'Email is required'
    });
  }
  
  if (!password) {
    return res.status(400).json({
      status: 'error',
      error: 'Password is required'
    });
  }
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({
      status: 'error',
      error: 'Invalid email format'
    });
  }
  
  next();
}

module.exports = {
  validateProfile,
  validateAccessRequest,
  validateOverrideRequest,
  validateFormFactorRequest,
  validateServiceAuth,
  validateUserAuth
};
