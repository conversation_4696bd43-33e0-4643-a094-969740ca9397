import React from 'react';
import Image from 'next/image';

const UACInfographicStatic = () => {
  return (
    <div className="uac-infographic-container bg-blue-900 rounded-lg overflow-hidden shadow-xl border border-blue-700 mb-8 p-6 md:p-10">
      {/* Header */}
      <div className="uac-header text-center mb-10">
        <h2 className="text-2xl md:text-3xl font-bold mb-3">The Dual Meaning of UAC</h2>
        <p className="text-lg opacity-90 max-w-3xl mx-auto">
          One acronym with two powerful interpretations that work together to create a complete compliance solution
        </p>
      </div>

      {/* Brain Infographic Image */}
      <div className="mb-10 flex justify-center">
        <div className="relative w-full max-w-2xl">
          <img
            src="/images/uac/left_right_brain_infographic.png"
            alt="UAC: Universal API Connector and Unified AI Compliance"
            className="w-full h-auto rounded-lg shadow-lg"
            style={{ maxHeight: '300px', objectFit: 'contain' }}
          />
        </div>
      </div>

      {/* Two Sections */}
      <div className="uac-segment grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
        {/* Left Section - Universal API Connector */}
        <div className="uac-left">
          <div className="bg-blue-800 p-6 rounded-lg border border-blue-600 h-full">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-blue-700 flex items-center justify-center mr-4">
                <span className="text-2xl">🔌</span>
              </div>
              <h3 className="text-xl font-bold">Universal API Connector</h3>
            </div>
            <p className="mb-6">
              The connectivity layer that seamlessly integrates disparate systems, translating between different APIs and ensuring data flows securely across your enterprise.
            </p>
            <div className="space-y-4">
              <div className="bg-blue-900 p-4 rounded-lg">
                <h4 className="font-semibold mb-1 text-blue-300">Connect</h4>
                <p className="text-sm">Establish secure connections to any API using a universal adapter framework</p>
              </div>
              <div className="bg-blue-900 p-4 rounded-lg">
                <h4 className="font-semibold mb-1 text-blue-300">Transform</h4>
                <p className="text-sm">Normalize data through the Semantic Translation Layer</p>
              </div>
              <div className="bg-blue-900 p-4 rounded-lg">
                <h4 className="font-semibold mb-1 text-blue-300">Secure</h4>
                <p className="text-sm">Implement Zero Trust security with comprehensive audit trails</p>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Unified AI Compliance */}
        <div className="uac-right">
          <div className="bg-purple-900 p-6 rounded-lg border border-purple-600 h-full">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 rounded-full bg-purple-800 flex items-center justify-center mr-4">
                <span className="text-2xl">🧠</span>
              </div>
              <h3 className="text-xl font-bold">Unified AI Compliance</h3>
            </div>
            <p className="mb-6">
              The intelligence layer that continuously monitors, interprets, predicts, and adapts to evolving compliance requirements across multiple frameworks in real-time.
            </p>
            <div className="space-y-4">
              <div className="bg-purple-950 p-4 rounded-lg">
                <h4 className="font-semibold mb-1 text-purple-300">Monitor</h4>
                <p className="text-sm">Continuously analyze data flows for compliance risks</p>
              </div>
              <div className="bg-purple-950 p-4 rounded-lg">
                <h4 className="font-semibold mb-1 text-purple-300">Predict</h4>
                <p className="text-sm">Identify potential compliance issues before they occur</p>
              </div>
              <div className="bg-purple-950 p-4 rounded-lg">
                <h4 className="font-semibold mb-1 text-purple-300">Adapt</h4>
                <p className="text-sm">Continuously learn and update to new regulations</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Arrow Section */}
      <div className="arrow-box flex justify-center items-center my-8">
        <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-700 to-purple-700 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </div>

      {/* Benefits */}
      <div className="uac-benefits bg-gradient-to-r from-blue-800 to-purple-800 p-6 rounded-lg border border-blue-600 mb-8">
        <h3 className="text-xl font-bold mb-4 text-center">The Power of Integration</h3>
        <p className="text-center mb-6">When Universal API Connector meets Unified AI Compliance, organizations gain:</p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg text-center">
            <div className="text-3xl mb-2">🛡️</div>
            <h4 className="font-semibold mb-1">Proactive Protection</h4>
            <p className="text-sm">Prevent compliance violations before they occur</p>
          </div>
          <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg text-center">
            <div className="text-3xl mb-2">⚡</div>
            <h4 className="font-semibold mb-1">Accelerated Innovation</h4>
            <p className="text-sm">Deploy new systems with built-in compliance</p>
          </div>
          <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg text-center">
            <div className="text-3xl mb-2">📊</div>
            <h4 className="font-semibold mb-1">Unified Governance</h4>
            <p className="text-sm">One system for all compliance needs</p>
          </div>
          <div className="bg-blue-900 bg-opacity-50 p-4 rounded-lg text-center">
            <div className="text-3xl mb-2">💰</div>
            <h4 className="font-semibold mb-1">Cost Reduction</h4>
            <p className="text-sm">Lower integration and compliance costs</p>
          </div>
        </div>
      </div>

      {/* Result Box */}
      <div className="result-box bg-gradient-to-r from-blue-700 to-purple-700 p-6 rounded-lg border border-blue-500 text-center">
        <h3 className="text-xl font-bold mb-3">The Compliance Brain</h3>
        <p className="text-lg mb-4">
          Together, these two aspects of UAC create a complete "Compliance Brain" for your organization
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-800 bg-opacity-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-1">Real-Time Risk Interception</h4>
            <p className="text-sm">Stop compliance violations before they happen</p>
          </div>
          <div className="bg-blue-800 bg-opacity-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-1">Explainable Compliance</h4>
            <p className="text-sm">Understand why decisions are made</p>
          </div>
          <div className="bg-blue-800 bg-opacity-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-1">Self-Updating Framework</h4>
            <p className="text-sm">Always current with regulations</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="uac-footer mt-8 text-center">
        <p className="italic text-blue-300">
          "The UAC is not just a product. It's a platform. A movement. A paradigm shift in how organizations approach connectivity and compliance."
        </p>
      </div>
    </div>
  );
};

export default UACInfographicStatic;
