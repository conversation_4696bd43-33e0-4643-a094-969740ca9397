/**
 * Security Middleware
 * 
 * This middleware implements security headers and protections for the NovaFuse API.
 */

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const xss = require('xss-clean');
const hpp = require('hpp');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

/**
 * Configure security middleware
 * @param {Object} app - Express app
 */
function configureSecurity(app) {
  // Set security headers with Helmet
  app.use(helmet());
  
  // Set specific security headers
  app.use((req, res, next) => {
    // Content Security Policy
    res.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'self'; object-src 'none'; img-src 'self' data:; media-src 'self'; frame-src 'none'; font-src 'self'; connect-src 'self'"
    );
    
    // Permissions Policy (formerly Feature-Policy)
    res.setHeader(
      'Permissions-Policy',
      'camera=(), microphone=(), geolocation=(), interest-cohort=()'
    );
    
    // Request ID for tracing
    const requestId = uuidv4();
    req.id = requestId;
    res.setHeader('X-Request-ID', requestId);
    
    next();
  });
  
  // Configure CORS
  const corsOptions = {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-API-Key'],
    exposedHeaders: ['X-Request-ID'],
    credentials: true,
    maxAge: 86400 // 24 hours
  };
  
  app.use(cors(corsOptions));
  
  // Rate limiting
  const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    message: 'Too many requests from this IP, please try again after 15 minutes',
    handler: (req, res, next, options) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
      res.status(options.statusCode).json({
        error: options.message,
        retryAfter: Math.ceil(options.windowMs / 1000 / 60) // in minutes
      });
    }
  });
  
  // Apply rate limiting to all routes
  app.use(apiLimiter);
  
  // Prevent XSS attacks
  app.use(xss());
  
  // Prevent HTTP Parameter Pollution
  app.use(hpp());
  
  // Log all requests
  app.use((req, res, next) => {
    const start = Date.now();
    
    // Log request
    logger.info({
      type: 'request',
      id: req.id,
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    // Log response
    res.on('finish', () => {
      const duration = Date.now() - start;
      
      logger.info({
        type: 'response',
        id: req.id,
        method: req.method,
        path: req.path,
        statusCode: res.statusCode,
        duration: `${duration}ms`
      });
    });
    
    next();
  });
}

module.exports = configureSecurity;
