import React, { useRef, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import TensorVisualization3D from './TensorVisualization3D';
import ResonanceSpectrogram from './ResonanceSpectrogram';
import PhaseSpaceVisualization from './PhaseSpaceVisualization';
import HarmonicPatternExplorer from './HarmonicPatternExplorer';
import TriDomainTensorVisualization from './TriDomainTensorVisualization';
import CyberSafetyHarmonyIndex from './CyberSafetyHarmonyIndex';
import RiskControlFusionMap from './RiskControlFusionMap';
import CyberSafetyResonanceSpectrogram from './CyberSafetyResonanceSpectrogram';
import UnifiedComplianceSecurityVisualizer from './UnifiedComplianceSecurityVisualizer';

/**
 * VisualizationRenderer component
 *
 * Renders the appropriate visualization based on the type
 */
function VisualizationRenderer({
  visualizationType,
  tensor,
  dimensions,
  options,
  width = '100%',
  height = '100%'
}) {
  // Render the appropriate visualization based on the type
  const renderVisualization = () => {
    switch (visualizationType) {
      case '3d_tensor_visualization':
        return (
          <TensorVisualization3D
            tensor={tensor}
            dimensions={dimensions}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'resonance_spectrogram':
        return (
          <ResonanceSpectrogram
            tensor={tensor}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'phase_space_visualization':
        return (
          <PhaseSpaceVisualization
            tensor={tensor}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'harmonic_pattern_explorer':
        return (
          <HarmonicPatternExplorer
            tensor={tensor}
            options={options}
            width={width}
            height={height}
          />
        );

      // New Cyber-Safety Fusion Visualizations
      case 'tri_domain_tensor_visualization':
        return (
          <TriDomainTensorVisualization
            domainData={tensor.domainData || {
              grc: { values: tensor.values || [], health: tensor.health || 0.8, entropyContainment: tensor.entropyContainment || 0.03 },
              it: { values: tensor.values || [], health: tensor.health || 0.8, entropyContainment: tensor.entropyContainment || 0.03 },
              cybersecurity: { values: tensor.values || [], health: tensor.health || 0.8, entropyContainment: tensor.entropyContainment || 0.03 },
              connections: []
            }}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'cyber_safety_harmony_index':
        return (
          <CyberSafetyHarmonyIndex
            domainData={tensor.domainData || {
              grc: { score: 0.7, metrics: { governance: 0.6, risk: 0.7, compliance: 0.8 } },
              it: { score: 0.8, metrics: { infrastructure: 0.8, applications: 0.7, data: 0.9 } },
              cybersecurity: { score: 0.6, metrics: { prevention: 0.5, detection: 0.6, response: 0.7 } }
            }}
            harmonyHistory={tensor.harmonyHistory || []}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'risk_control_fusion_map':
        return (
          <RiskControlFusionMap
            riskData={tensor.riskData || {
              grc: { governance: 0.7, risk: 0.5, compliance: 0.8 },
              it: { infrastructure: 0.6, applications: 0.4, data: 0.7 },
              cybersecurity: { prevention: 0.8, detection: 0.5, response: 0.3 }
            }}
            controlData={tensor.controlData || {
              grc: { governance: 0.8, risk: 0.6, compliance: 0.9 },
              it: { infrastructure: 0.7, applications: 0.5, data: 0.6 },
              cybersecurity: { prevention: 0.7, detection: 0.4, response: 0.2 }
            }}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'cyber_safety_resonance_spectrogram':
        return (
          <CyberSafetyResonanceSpectrogram
            domainData={tensor.domainData || {
              grc: { values: tensor.values || [], frequency: 0.3, amplitude: 0.7, phase: 0 },
              it: { values: tensor.values || [], frequency: 0.6, amplitude: 0.8, phase: Math.PI / 3 },
              cybersecurity: { values: tensor.values || [], frequency: 0.9, amplitude: 0.6, phase: Math.PI / 2 },
              crossDomainFlows: []
            }}
            predictionData={tensor.predictionData || {
              timeHorizon: 10,
              dissonanceProbability: 0.2,
              criticalPoints: []
            }}
            options={options}
            width={width}
            height={height}
          />
        );

      case 'unified_compliance_security_visualizer':
        return (
          <UnifiedComplianceSecurityVisualizer
            complianceData={tensor.complianceData || {
              requirements: [
                { id: 'req1', name: 'Data Protection', domain: 'grc', completeness: 0.8 },
                { id: 'req2', name: 'Access Control', domain: 'grc', completeness: 0.6 },
                { id: 'req3', name: 'Incident Response', domain: 'grc', completeness: 0.4 }
              ],
              controls: [
                { id: 'ctrl1', name: 'Encryption', domain: 'it', completeness: 0.9 },
                { id: 'ctrl2', name: 'Authentication', domain: 'it', completeness: 0.7 },
                { id: 'ctrl3', name: 'Monitoring', domain: 'cybersecurity', completeness: 0.5 },
                { id: 'ctrl4', name: 'Firewalls', domain: 'cybersecurity', completeness: 0.8 }
              ],
              implementations: [
                { id: 'impl1', name: 'AES-256', domain: 'it', completeness: 0.9 },
                { id: 'impl2', name: 'MFA', domain: 'it', completeness: 0.6 },
                { id: 'impl3', name: 'SIEM', domain: 'cybersecurity', completeness: 0.4 },
                { id: 'impl4', name: 'Next-Gen FW', domain: 'cybersecurity', completeness: 0.7 }
              ],
              links: [
                { source: 'req1', target: 'ctrl1', strength: 0.8, efficiency: 0.9 },
                { source: 'req1', target: 'ctrl3', strength: 0.4, efficiency: 0.5 },
                { source: 'req2', target: 'ctrl2', strength: 0.7, efficiency: 0.8 },
                { source: 'req2', target: 'ctrl4', strength: 0.5, efficiency: 0.6 },
                { source: 'req3', target: 'ctrl3', strength: 0.6, efficiency: 0.7 },
                { source: 'ctrl1', target: 'impl1', strength: 0.9, efficiency: 0.9 },
                { source: 'ctrl2', target: 'impl2', strength: 0.6, efficiency: 0.7 },
                { source: 'ctrl3', target: 'impl3', strength: 0.4, efficiency: 0.5 },
                { source: 'ctrl4', target: 'impl4', strength: 0.7, efficiency: 0.8 }
              ]
            }}
            impactAnalysis={tensor.impactAnalysis || {
              proposedChanges: [
                { id: 'change1', target: 'ctrl2', impact: 0.7, description: 'Upgrade to biometric authentication' },
                { id: 'change2', target: 'impl3', impact: 0.5, description: 'Implement AI-based threat detection' }
              ]
            }}
            options={options}
            width={width}
            height={height}
          />
        );

      default:
        // Default visualization
        return (
          <Box
            sx={{
              width,
              height,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'background.paper',
              borderRadius: 1,
              p: 2,
              textAlign: 'center'
            }}
          >
            <Typography variant="body1" color="text.secondary">
              Unknown visualization type: {visualizationType}
            </Typography>
          </Box>
        );
    }
  };

  return renderVisualization();
}

export default VisualizationRenderer;
