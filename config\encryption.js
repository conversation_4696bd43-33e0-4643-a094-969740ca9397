/**
 * NovaFuse API Superstore Encryption Configuration
 * 
 * This file contains configuration settings for encryption.
 * In a production environment, these keys should be stored securely
 * in environment variables or a secure key management system.
 */

const crypto = require('crypto');

// Load environment variables
const env = process.env.NODE_ENV || 'development';

// Default configuration
const defaultConfig = {
  // Master key for encrypting other keys
  masterKey: process.env.ENCRYPTION_MASTER_KEY || crypto.randomBytes(32).toString('hex'),
  
  // Key for encrypting request data
  requestKey: process.env.ENCRYPTION_REQUEST_KEY || crypto.randomBytes(32).toString('hex'),
  
  // Key for encrypting response data
  responseKey: process.env.ENCRYPTION_RESPONSE_KEY || crypto.randomBytes(32).toString('hex'),
  
  // Key for encrypting API keys
  apiKeyEncryptionKey: process.env.API_KEY_ENCRYPTION_KEY || crypto.randomBytes(32).toString('hex'),
  
  // Key for encrypting JWT tokens
  jwtSecret: process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex'),
  
  // Fields to encrypt in responses
  sensitiveResponseFields: [
    'user.password',
    'user.passwordHash',
    'user.passwordSalt',
    'user.securityQuestion',
    'user.securityAnswer',
    'user.recoveryCode',
    'payment.cardNumber',
    'payment.cvv',
    'payment.accountNumber',
    'payment.routingNumber',
    'apiKey',
    'apiSecret',
    'credentials.password',
    'credentials.secret',
    'credentials.token',
    'credentials.apiKey',
    'credentials.apiSecret',
    'credentials.accessToken',
    'credentials.refreshToken',
    'credentials.privateKey'
  ],
  
  // Fields to decrypt in requests
  encryptedRequestFields: [
    'user.password',
    'payment.cardNumber',
    'payment.cvv',
    'payment.accountNumber',
    'payment.routingNumber',
    'apiKey',
    'apiSecret',
    'credentials.password',
    'credentials.secret',
    'credentials.token',
    'credentials.apiKey',
    'credentials.apiSecret'
  ],
  
  // Fields to mask in logs
  sensitiveLogFields: [
    'user.password',
    'user.passwordHash',
    'user.passwordSalt',
    'user.securityQuestion',
    'user.securityAnswer',
    'user.recoveryCode',
    'payment.cardNumber',
    'payment.cvv',
    'payment.accountNumber',
    'payment.routingNumber',
    'apiKey',
    'apiSecret',
    'credentials.password',
    'credentials.secret',
    'credentials.token',
    'credentials.apiKey',
    'credentials.apiSecret',
    'credentials.accessToken',
    'credentials.refreshToken',
    'credentials.privateKey'
  ]
};

// Environment-specific configurations
const configurations = {
  development: {
    ...defaultConfig,
    // Development-specific overrides
  },
  test: {
    ...defaultConfig,
    // Test-specific overrides
    masterKey: 'test-master-key-for-consistent-test-results',
    requestKey: 'test-request-key-for-consistent-test-results',
    responseKey: 'test-response-key-for-consistent-test-results',
    apiKeyEncryptionKey: 'test-api-key-encryption-key-for-consistent-test-results',
    jwtSecret: 'test-jwt-secret-for-consistent-test-results'
  },
  production: {
    ...defaultConfig,
    // Production-specific overrides
    // In production, all keys should come from environment variables
    masterKey: process.env.ENCRYPTION_MASTER_KEY,
    requestKey: process.env.ENCRYPTION_REQUEST_KEY,
    responseKey: process.env.ENCRYPTION_RESPONSE_KEY,
    apiKeyEncryptionKey: process.env.API_KEY_ENCRYPTION_KEY,
    jwtSecret: process.env.JWT_SECRET
  }
};

// Validate production configuration
if (env === 'production') {
  const requiredKeys = [
    'masterKey',
    'requestKey',
    'responseKey',
    'apiKeyEncryptionKey',
    'jwtSecret'
  ];
  
  for (const key of requiredKeys) {
    if (!configurations.production[key]) {
      console.error(`ERROR: Missing required encryption key: ${key}`);
      console.error(`Please set the environment variable: ${key.toUpperCase()}`);
      process.exit(1);
    }
  }
}

// Export the configuration for the current environment
module.exports = configurations[env];
