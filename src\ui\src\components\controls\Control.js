import React from 'react';
import {
  Box,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  Slider,
  Switch,
  TextField,
  Typography,
  LinearProgress,
} from '@mui/material';
import { useControl } from '../../contexts/ControlContext';

/**
 * Control component
 * 
 * Renders a control based on its type
 */
function Control({ controlId, control }) {
  const { getControlValue, setControlValue } = useControl();
  const value = getControlValue(controlId);

  if (!control) {
    return null;
  }

  const handleChange = (newValue) => {
    setControlValue(controlId, newValue);
  };

  // Render different control types
  switch (control.type) {
    case 'select':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <InputLabel id={`${controlId}-label`}>{control.label}</InputLabel>
          <Select
            labelId={`${controlId}-label`}
            id={controlId}
            value={value || ''}
            label={control.label}
            onChange={(e) => handleChange(e.target.value)}
            disabled={control.readOnly}
          >
            {control.options.map((option) => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      );

    case 'checkbox':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={Boolean(value)}
                onChange={(e) => handleChange(e.target.checked)}
                disabled={control.readOnly}
              />
            }
            label={control.label}
          />
        </FormControl>
      );

    case 'slider':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Typography gutterBottom>{control.label}</Typography>
          <Slider
            value={value || 0}
            onChange={(e, newValue) => handleChange(newValue)}
            min={control.min || 0}
            max={control.max || 100}
            step={control.step || 1}
            valueLabelDisplay="auto"
            disabled={control.readOnly}
          />
        </FormControl>
      );

    case 'number':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <TextField
            id={controlId}
            label={control.label}
            type="number"
            value={value || 0}
            onChange={(e) => handleChange(Number(e.target.value))}
            InputProps={{
              readOnly: control.readOnly,
            }}
            inputProps={{
              min: control.min,
              max: control.max,
              step: control.step || 1,
            }}
          />
        </FormControl>
      );

    case 'text':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <TextField
            id={controlId}
            label={control.label}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            InputProps={{
              readOnly: control.readOnly,
            }}
          />
        </FormControl>
      );

    case 'textarea':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <TextField
            id={controlId}
            label={control.label}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            multiline
            rows={4}
            InputProps={{
              readOnly: control.readOnly,
            }}
          />
        </FormControl>
      );

    case 'json':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <TextField
            id={controlId}
            label={control.label}
            value={typeof value === 'string' ? value : JSON.stringify(value, null, 2)}
            onChange={(e) => {
              try {
                const jsonValue = JSON.parse(e.target.value);
                handleChange(jsonValue);
              } catch (err) {
                // If it's not valid JSON, just store the string
                handleChange(e.target.value);
              }
            }}
            multiline
            rows={6}
            InputProps={{
              readOnly: control.readOnly,
            }}
          />
        </FormControl>
      );

    case 'progress':
      return (
        <FormControl fullWidth sx={{ mb: 2 }}>
          <Typography gutterBottom>
            {control.label}: {Math.round((value || 0) * 100)}%
          </Typography>
          <LinearProgress
            variant="determinate"
            value={Math.round((value || 0) * 100)}
            sx={{
              height: 10,
              borderRadius: 5,
            }}
          />
        </FormControl>
      );

    default:
      return (
        <Box sx={{ mb: 2 }}>
          <Typography color="error">
            Unknown control type: {control.type}
          </Typography>
        </Box>
      );
  }
}

export default Control;
