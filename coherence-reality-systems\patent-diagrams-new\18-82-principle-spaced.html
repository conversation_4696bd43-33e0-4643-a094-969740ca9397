<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>18/82 Principle Implementation Diagram</title>
    <style>
        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.4;
        }

        /* Main Diagram Container */
        .diagram-container {
            position: relative;
            width: 950px;
            min-height: 900px; /* Increased minimum height */
            margin: 0 auto 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
            overflow: visible; /* Allow content to be fully visible */
        }

        /* Container Box Styles */
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            overflow: visible; /* Allow content to be fully visible */
            min-height: 100px; /* Minimum height */
            min-width: 200px; /* Minimum width */
        }

        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            width: 90%; /* Ensures text doesn't overflow container */
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            z-index: 10; /* Ensure labels are in front of connecting lines */
            background-color: white; /* Add background to hide lines behind text */
            padding: 0 5px; /* Add padding around text */
        }

        /* Component Box Styles */
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
            align-items: center;
            text-align: center;
            padding: 5px; /* Reduced padding */
            overflow: visible; /* Allow content to be fully visible */
            border-top-left-radius: 0; /* Flat corner for number */
            min-height: 50px; /* Minimum height */
            min-width: 100px; /* Minimum width */
        }

        /* Component Number Styles - Integrated into corner */
        .component-number-inside {
            position: absolute;
            top: 0;
            left: 0;
            width: 18px;
            height: 18px;
            background-color: #000000; /* Black background for component numbers */
            color: white;
            border-radius: 0 0 6px 0; /* Rounded on bottom-right corner only */
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 10px; /* Smaller font */
            font-weight: bold;
            z-index: 2;
            margin: 0; /* Remove any margin */
            padding: 0; /* Remove any padding */
        }

        .component-label {
            font-weight: bold;
            margin-top: 10px; /* Reduced margin */
            margin-bottom: 2px; /* Reduced margin */
            font-size: 12px; /* Smaller font */
            text-align: center;
            width: 100%;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
        }

        .component-content {
            font-size: 10px; /* Smaller font */
            text-align: center;
            width: 100%;
            padding: 0;
            white-space: normal; /* Allows text to wrap */
            overflow: visible; /* Allow content to be fully visible */
            line-height: 1.2; /* Tighter line spacing */
        }

        /* Arrow Styles */
        .arrow {
            position: absolute;
            background-color: #555555; /* Grey color for patent compliance */
            width: 2px;
            z-index: 0;
        }

        /* Legend Styles */
        .legend {
            position: absolute;
            right: 10px;
            bottom: 30px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            z-index: 10;
            width: 200px;
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 8px;
            border: 1px solid #333;
            flex-shrink: 0; /* Prevents the color box from shrinking */
        }

        /* Inventor Label */
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 30px;
            font-size: 12px;
            font-style: italic;
            color: #333;
            z-index: 10;
        }

        /* Title Styles */
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 20px;
            color: #333;
        }
    </style>
</head>
<body>

    <h1>FIG. 7: 18/82 Principle Implementation Diagram</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">18/82 PRINCIPLE: RESOURCE OPTIMIZATION IMPLEMENTATION</div>
        </div>

        <!-- Principle Overview -->
        <div class="component-box" style="left: 50px; top: 80px; width: 650px; height: 60px;">
            <div class="component-number">901</div>
            <div class="component-label">18/82 Principle Definition</div>
            <div style="font-size: 12px; text-align: center;">
                18% of inputs consistently account for 82% of outputs across all domains
            </div>
        </div>

        <!-- Pie Chart -->
        <div class="pie-chart" style="left: 275px; top: 170px;"></div>
        <div class="pie-label" style="left: 340px; top: 230px; color: white;">18%</div>
        <div class="pie-label" style="left: 400px; top: 270px; color: #333;">82%</div>

        <!-- Critical Inputs -->
        <div class="component-box" style="left: 50px; top: 270px; width: 150px; height: 100px;">
            <div class="component-number">902</div>
            <div class="component-label">Critical Inputs</div>
            <div style="font-size: 12px; text-align: center;">
                18% of Resources<br>
                • Key Indicators<br>
                • Critical Patterns<br>
                • Essential Data Points
            </div>
        </div>

        <!-- Resource Allocation -->
        <div class="component-box" style="left: 550px; top: 270px; width: 150px; height: 100px;">
            <div class="component-number">903</div>
            <div class="component-label">Resource Allocation</div>
            <div style="font-size: 12px; text-align: center;">
                Prioritized Processing<br>
                • Computing Power<br>
                • Memory Allocation<br>
                • Processing Time
            </div>
        </div>

        <!-- Domain Applications -->
        <div class="container-box" style="width: 650px; height: 180px; left: 50px; top: 400px;">
            <div class="container-label" style="font-size: 16px;">CROSS-DOMAIN APPLICATIONS</div>
        </div>

        <div class="component-box" style="left: 80px; top: 440px; width: 150px; height: 60px;">
            <div class="component-number">904</div>
            <div class="component-label">Cybersecurity</div>
            <div style="font-size: 12px; text-align: center;">
                18% of threat indicators<br>
                predict 82% of attacks
            </div>
        </div>

        <div class="component-box" style="left: 300px; top: 440px; width: 150px; height: 60px;">
            <div class="component-number">905</div>
            <div class="component-label">Compliance</div>
            <div style="font-size: 12px; text-align: center;">
                18% of controls satisfy<br>
                82% of requirements
            </div>
        </div>

        <div class="component-box" style="left: 520px; top: 440px; width: 150px; height: 60px;">
            <div class="component-number">906</div>
            <div class="component-label">Risk Management</div>
            <div style="font-size: 12px; text-align: center;">
                18% of risk factors<br>
                account for 82% of impact
            </div>
        </div>

        <div class="component-box" style="left: 80px; top: 510px; width: 150px; height: 60px;">
            <div class="component-number">907</div>
            <div class="component-label">Data Processing</div>
            <div style="font-size: 12px; text-align: center;">
                18% of data fields<br>
                provide 82% of insights
            </div>
        </div>

        <div class="component-box" style="left: 300px; top: 510px; width: 150px; height: 60px;">
            <div class="component-number">908</div>
            <div class="component-label">User Interface</div>
            <div style="font-size: 12px; text-align: center;">
                18% of UI elements<br>
                handle 82% of interactions
            </div>
        </div>

        <div class="component-box" style="left: 520px; top: 510px; width: 150px; height: 60px;">
            <div class="component-number">909</div>
            <div class="component-label">System Architecture</div>
            <div style="font-size: 12px; text-align: center;">
                18% of components<br>
                deliver 82% of functionality
            </div>
        </div>

        <!-- Arrows -->
        <div class="arrow-line" style="left: 200px; top: 320px; width: 75px; height: 2px;"></div>
        <div class="arrow-head" style="left: 275px; top: 316px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <div class="arrow-line" style="left: 475px; top: 320px; width: 75px; height: 2px;"></div>
        <div class="arrow-head" style="left: 550px; top: 316px; border-width: 4px 0 4px 8px; border-color: transparent transparent transparent #333;"></div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #555555;"></div>
                <div>Critical 18% (High Impact)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e0e0e0;"></div>
                <div>Remaining 82% (Low Impact)</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>

</body>
</html>
