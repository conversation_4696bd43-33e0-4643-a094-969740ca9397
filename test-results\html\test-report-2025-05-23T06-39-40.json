{"numFailedTestSuites": 0, "numFailedTests": 0, "numPassedTestSuites": 2, "numPassedTests": 13, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 0, "numTodoTests": 0, "numTotalTestSuites": 2, "numTotalTests": 13, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1747982374692, "success": false, "testResults": [{"leaks": false, "numFailingTests": 0, "numPassingTests": 6, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982379022, "runtime": 2524, "slow": false, "start": 1747982376498}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\unit\\novacore\\tensor-runtime.test.js", "testResults": [{"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Creation"], "duration": 16, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Creation should create a tensor with the correct dimensions", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "should create a tensor with the correct dimensions"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Creation"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Creation should handle empty data", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should handle empty data"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Processing"], "duration": 2, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Processing should process a tensor correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should process a tensor correctly"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Processing"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Processing should handle complex data in tensors", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should handle complex data in tensors"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Tensor Transformation"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Tensor Transformation should transform a tensor correctly", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "should transform a tensor correctly"}, {"ancestorTitles": ["NovaCore Tensor-Based Runtime", "Performance"], "duration": 3, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore Tensor-Based Runtime Performance should process tensors efficiently", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "should process tensors efficiently"}], "failureMessage": null}, {"leaks": false, "numFailingTests": 0, "numPassingTests": 7, "numPendingTests": 0, "numTodoTests": 0, "openHandles": [], "perfStats": {"end": 1747982380816, "runtime": 4102, "slow": false, "start": 1747982376714}, "skipped": false, "snapshot": {"added": 0, "fileDeleted": false, "matched": 0, "unchecked": 0, "uncheckedKeys": [], "unmatched": 0, "updated": 0}, "testFilePath": "D:\\novafuse-api-superstore\\tests\\integration\\novacore\\api-endpoints.test.js", "testResults": [{"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 56, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints GET /api/v1/evidence should return a list of evidence items", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence should return a list of evidence items"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints POST /api/v1/evidence should create a new evidence item", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "POST /api/v1/evidence should create a new evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 7, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints GET /api/v1/evidence/:id should return a specific evidence item", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "GET /api/v1/evidence/:id should return a specific evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints PUT /api/v1/evidence/:id should update an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "PUT /api/v1/evidence/:id should update an evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Evidence Endpoints"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Evidence Endpoints DELETE /api/v1/evidence/:id should delete an evidence item", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "DELETE /api/v1/evidence/:id should delete an evidence item"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Blockchain Endpoints"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Blockchain Endpoints POST /api/v1/blockchain/verify should verify data on the blockchain", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "POST /api/v1/blockchain/verify should verify data on the blockchain"}, {"ancestorTitles": ["NovaCore API Integration Tests", "Connector Endpoints"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "NovaCore API Integration Tests Connector Endpoints GET /api/v1/connectors should return a list of connectors", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "GET /api/v1/connectors should return a list of connectors"}], "failureMessage": null}], "wasInterrupted": false}