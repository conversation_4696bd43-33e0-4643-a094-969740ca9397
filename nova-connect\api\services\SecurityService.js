/**
 * Security Service
 *
 * This service provides enhanced security features including IP restrictions,
 * encryption management, and custom security policies.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');

class SecurityService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.securityDir = path.join(this.dataDir, 'security');
    this.ipRestrictionsFile = path.join(this.securityDir, 'ip_restrictions.json');
    this.encryptionKeysFile = path.join(this.securityDir, 'encryption_keys.json');
    this.securityPoliciesFile = path.join(this.securityDir, 'security_policies.json');
    this.securityAuditFile = path.join(this.securityDir, 'security_audit.json');
    this.auditService = new AuditService(dataDir);

    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.securityDir, { recursive: true });

      // Initialize files if they don't exist
      await this.initializeFile(this.ipRestrictionsFile, []);
      await this.initializeFile(this.encryptionKeysFile, []);
      await this.initializeFile(this.securityPoliciesFile, []);
      await this.initializeFile(this.securityAuditFile, []);
    } catch (error) {
      console.error('Error creating security directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Log security audit event
   */
  async logSecurityAudit(data) {
    try {
      // Validate required fields
      if (!data.action) {
        throw new ValidationError('Security audit action is required');
      }

      if (!data.userId) {
        throw new ValidationError('User ID is required');
      }

      // Create audit entry
      const auditEntry = {
        id: uuidv4(),
        action: data.action,
        userId: data.userId,
        resourceType: data.resourceType || 'security',
        resourceId: data.resourceId || null,
        details: data.details || {},
        ipAddress: data.ipAddress || null,
        userAgent: data.userAgent || null,
        timestamp: new Date().toISOString(),
        success: data.success !== undefined ? data.success : true,
        failureReason: data.failureReason || null
      };

      // Save audit entry
      const auditEntries = await this.loadData(this.securityAuditFile);
      auditEntries.push(auditEntry);
      await this.saveData(this.securityAuditFile, auditEntries);

      // Also log to general audit log
      await this.auditService.logEvent({
        userId: data.userId,
        action: `SECURITY_${data.action}`,
        resourceType: data.resourceType || 'security',
        resourceId: data.resourceId || null,
        details: data.details || {}
      });

      return auditEntry;
    } catch (error) {
      console.error('Error logging security audit:', error);
      throw error;
    }
  }

  /**
   * Get security audit logs
   */
  async getSecurityAuditLogs(filters = {}) {
    try {
      const auditEntries = await this.loadData(this.securityAuditFile);

      // Apply filters
      let filteredEntries = auditEntries;

      if (filters.userId) {
        filteredEntries = filteredEntries.filter(entry => entry.userId === filters.userId);
      }

      if (filters.action) {
        filteredEntries = filteredEntries.filter(entry => entry.action === filters.action);
      }

      if (filters.resourceType) {
        filteredEntries = filteredEntries.filter(entry => entry.resourceType === filters.resourceType);
      }

      if (filters.resourceId) {
        filteredEntries = filteredEntries.filter(entry => entry.resourceId === filters.resourceId);
      }

      if (filters.startDate) {
        const startDate = new Date(filters.startDate);
        filteredEntries = filteredEntries.filter(entry => new Date(entry.timestamp) >= startDate);
      }

      if (filters.endDate) {
        const endDate = new Date(filters.endDate);
        filteredEntries = filteredEntries.filter(entry => new Date(entry.timestamp) <= endDate);
      }

      if (filters.success !== undefined) {
        filteredEntries = filteredEntries.filter(entry => entry.success === filters.success);
      }

      // Sort by timestamp (newest first)
      filteredEntries.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Apply pagination
      if (filters.limit && filters.offset !== undefined) {
        const limit = parseInt(filters.limit, 10);
        const offset = parseInt(filters.offset, 10);
        filteredEntries = filteredEntries.slice(offset, offset + limit);
      }

      return filteredEntries;
    } catch (error) {
      console.error('Error getting security audit logs:', error);
      throw error;
    }
  }

  /**
   * Get all IP restrictions
   */
  async getAllIpRestrictions(filters = {}) {
    try {
      const restrictions = await this.loadData(this.ipRestrictionsFile);

      // Apply filters
      let filteredRestrictions = restrictions;

      if (filters.resourceType) {
        filteredRestrictions = filteredRestrictions.filter(r => r.resourceType === filters.resourceType);
      }

      if (filters.resourceId) {
        filteredRestrictions = filteredRestrictions.filter(r => r.resourceId === filters.resourceId);
      }

      if (filters.enabled !== undefined) {
        filteredRestrictions = filteredRestrictions.filter(r => r.enabled === filters.enabled);
      }

      return filteredRestrictions;
    } catch (error) {
      console.error('Error getting IP restrictions:', error);
      throw error;
    }
  }

  /**
   * Get IP restriction by ID
   */
  async getIpRestrictionById(id) {
    try {
      const restrictions = await this.loadData(this.ipRestrictionsFile);
      const restriction = restrictions.find(r => r.id === id);

      if (!restriction) {
        throw new NotFoundError(`IP restriction with ID ${id} not found`);
      }

      return restriction;
    } catch (error) {
      console.error('Error getting IP restriction:', error);
      throw error;
    }
  }

  /**
   * Create IP restriction
   */
  async createIpRestriction(data, userId) {
    try {
      // Validate required fields
      if (!data.name) {
        throw new ValidationError('IP restriction name is required');
      }

      if (!data.resourceType) {
        throw new ValidationError('Resource type is required');
      }

      if (!data.resourceId) {
        throw new ValidationError('Resource ID is required');
      }

      if (!data.allowedIps || !Array.isArray(data.allowedIps) || data.allowedIps.length === 0) {
        throw new ValidationError('At least one allowed IP is required');
      }

      // Validate IP addresses
      for (const ip of data.allowedIps) {
        if (!this.isValidIpOrCidr(ip)) {
          throw new ValidationError(`Invalid IP address or CIDR notation: ${ip}`);
        }
      }

      // Create IP restriction
      const restriction = {
        id: uuidv4(),
        name: data.name,
        description: data.description || '',
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        allowedIps: data.allowedIps,
        enabled: data.enabled !== undefined ? data.enabled : true,
        createdBy: userId,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      // Save IP restriction
      const restrictions = await this.loadData(this.ipRestrictionsFile);
      restrictions.push(restriction);
      await this.saveData(this.ipRestrictionsFile, restrictions);

      // Log security audit
      await this.logSecurityAudit({
        action: 'CREATE_IP_RESTRICTION',
        userId,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        details: {
          restrictionId: restriction.id,
          name: restriction.name,
          allowedIps: restriction.allowedIps
        }
      });

      return restriction;
    } catch (error) {
      console.error('Error creating IP restriction:', error);
      throw error;
    }
  }

  /**
   * Update IP restriction
   */
  async updateIpRestriction(id, data, userId) {
    try {
      const restrictions = await this.loadData(this.ipRestrictionsFile);
      const index = restrictions.findIndex(r => r.id === id);

      if (index === -1) {
        throw new NotFoundError(`IP restriction with ID ${id} not found`);
      }

      const restriction = restrictions[index];

      // Validate IP addresses if provided
      if (data.allowedIps) {
        if (!Array.isArray(data.allowedIps) || data.allowedIps.length === 0) {
          throw new ValidationError('At least one allowed IP is required');
        }

        for (const ip of data.allowedIps) {
          if (!this.isValidIpOrCidr(ip)) {
            throw new ValidationError(`Invalid IP address or CIDR notation: ${ip}`);
          }
        }
      }

      // Update IP restriction
      const updatedRestriction = {
        ...restriction,
        name: data.name || restriction.name,
        description: data.description !== undefined ? data.description : restriction.description,
        allowedIps: data.allowedIps || restriction.allowedIps,
        enabled: data.enabled !== undefined ? data.enabled : restriction.enabled,
        updated: new Date().toISOString()
      };

      restrictions[index] = updatedRestriction;
      await this.saveData(this.ipRestrictionsFile, restrictions);

      // Log security audit
      await this.logSecurityAudit({
        action: 'UPDATE_IP_RESTRICTION',
        userId,
        resourceType: updatedRestriction.resourceType,
        resourceId: updatedRestriction.resourceId,
        details: {
          restrictionId: updatedRestriction.id,
          name: updatedRestriction.name,
          allowedIps: updatedRestriction.allowedIps,
          enabled: updatedRestriction.enabled
        }
      });

      return updatedRestriction;
    } catch (error) {
      console.error('Error updating IP restriction:', error);
      throw error;
    }
  }

  /**
   * Delete IP restriction
   */
  async deleteIpRestriction(id, userId) {
    try {
      const restrictions = await this.loadData(this.ipRestrictionsFile);
      const index = restrictions.findIndex(r => r.id === id);

      if (index === -1) {
        throw new NotFoundError(`IP restriction with ID ${id} not found`);
      }

      const restriction = restrictions[index];

      // Remove IP restriction
      restrictions.splice(index, 1);
      await this.saveData(this.ipRestrictionsFile, restrictions);

      // Log security audit
      await this.logSecurityAudit({
        action: 'DELETE_IP_RESTRICTION',
        userId,
        resourceType: restriction.resourceType,
        resourceId: restriction.resourceId,
        details: {
          restrictionId: restriction.id,
          name: restriction.name
        }
      });

      return { success: true, message: `IP restriction ${id} deleted` };
    } catch (error) {
      console.error('Error deleting IP restriction:', error);
      throw error;
    }
  }

  /**
   * Check if IP is allowed for resource
   */
  async isIpAllowedForResource(ip, resourceType, resourceId) {
    try {
      const restrictions = await this.loadData(this.ipRestrictionsFile);

      // Find restrictions for this resource
      const resourceRestrictions = restrictions.filter(r =>
        r.resourceType === resourceType &&
        r.resourceId === resourceId &&
        r.enabled
      );

      // If no restrictions, allow access
      if (resourceRestrictions.length === 0) {
        return true;
      }

      // Check if IP is allowed by any restriction
      for (const restriction of resourceRestrictions) {
        for (const allowedIp of restriction.allowedIps) {
          if (this.isIpInRange(ip, allowedIp)) {
            return true;
          }
        }
      }

      // IP not allowed by any restriction
      return false;
    } catch (error) {
      console.error('Error checking IP restriction:', error);
      // In case of error, default to denying access
      return false;
    }
  }

  /**
   * Check if IP is valid
   */
  isValidIpOrCidr(ip) {
    // IPv4 address pattern
    const ipv4Pattern = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})(\/\d{1,2})?$/;

    // IPv6 address patterns (simplified)
    const ipv6Pattern = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}(\/\d{1,3})?$/;
    const ipv6AbbrPattern = /^([0-9a-fA-F]{1,4}:){0,6}(:[0-9a-fA-F]{1,4}){0,6}(\/\d{1,3})?$/;

    // Check if it's an IPv4 address
    if (ipv4Pattern.test(ip)) {
      const parts = ip.split('/');
      const address = parts[0];
      const prefix = parts[1] ? parseInt(parts[1], 10) : null;

      // Validate each octet
      const octets = address.split('.');
      for (const octet of octets) {
        const num = parseInt(octet, 10);
        if (num < 0 || num > 255) {
          return false;
        }
      }

      // Validate prefix if present
      if (prefix !== null && (prefix < 0 || prefix > 32)) {
        return false;
      }

      return true;
    }

    // Check if it's an IPv6 address (simplified validation)
    if (ipv6Pattern.test(ip) || ipv6AbbrPattern.test(ip)) {
      const parts = ip.split('/');
      const prefix = parts[1] ? parseInt(parts[1], 10) : null;

      // Validate prefix if present
      if (prefix !== null && (prefix < 0 || prefix > 128)) {
        return false;
      }

      return true;
    }

    return false;
  }

  /**
   * Check if IP is in range
   */
  isIpInRange(ip, range) {
    // If exact match
    if (ip === range) {
      return true;
    }

    // If range is CIDR notation
    if (range.includes('/')) {
      // For simplicity, we'll just check IPv4 CIDR ranges
      // In a real implementation, this would handle IPv6 as well
      const [rangeIp, prefixStr] = range.split('/');
      const prefix = parseInt(prefixStr, 10);

      // Convert IP addresses to numeric form
      const ipNum = this.ipToNumber(ip);
      const rangeIpNum = this.ipToNumber(rangeIp);

      // Calculate mask
      const mask = ~(2 ** (32 - prefix) - 1);

      // Check if IP is in range
      return (ipNum & mask) === (rangeIpNum & mask);
    }

    return false;
  }

  /**
   * Convert IP to number
   */
  ipToNumber(ip) {
    const octets = ip.split('.');
    return ((parseInt(octets[0], 10) << 24) |
            (parseInt(octets[1], 10) << 16) |
            (parseInt(octets[2], 10) << 8) |
            parseInt(octets[3], 10)) >>> 0;
  }

  /**
   * Get all encryption keys
   */
  async getAllEncryptionKeys(filters = {}) {
    try {
      const keys = await this.loadData(this.encryptionKeysFile);

      // Apply filters
      let filteredKeys = keys;

      if (filters.purpose) {
        filteredKeys = filteredKeys.filter(k => k.purpose === filters.purpose);
      }

      if (filters.algorithm) {
        filteredKeys = filteredKeys.filter(k => k.algorithm === filters.algorithm);
      }

      if (filters.active !== undefined) {
        filteredKeys = filteredKeys.filter(k => k.active === filters.active);
      }

      // For security, don't return the actual key material in list operations
      return filteredKeys.map(key => ({
        ...key,
        keyMaterial: undefined,
        privateKey: undefined
      }));
    } catch (error) {
      console.error('Error getting encryption keys:', error);
      throw error;
    }
  }

  /**
   * Get encryption key by ID
   */
  async getEncryptionKeyById(id, includeKeyMaterial = false) {
    try {
      const keys = await this.loadData(this.encryptionKeysFile);
      const key = keys.find(k => k.id === id);

      if (!key) {
        throw new NotFoundError(`Encryption key with ID ${id} not found`);
      }

      // For security, don't return the actual key material unless specifically requested
      if (!includeKeyMaterial) {
        return {
          ...key,
          keyMaterial: undefined,
          privateKey: undefined
        };
      }

      return key;
    } catch (error) {
      console.error('Error getting encryption key:', error);
      throw error;
    }
  }

  /**
   * Create encryption key
   */
  async createEncryptionKey(data, userId) {
    try {
      // Validate required fields
      if (!data.name) {
        throw new ValidationError('Encryption key name is required');
      }

      if (!data.purpose) {
        throw new ValidationError('Encryption key purpose is required');
      }

      if (!data.algorithm) {
        throw new ValidationError('Encryption algorithm is required');
      }

      // Generate key material if not provided
      let keyMaterial = data.keyMaterial;
      let publicKey = data.publicKey;
      let privateKey = data.privateKey;

      if (!keyMaterial && !privateKey) {
        // Generate key based on algorithm
        const keyPair = await this.generateKeyPair(data.algorithm);

        if (keyPair.type === 'symmetric') {
          keyMaterial = keyPair.key;
        } else {
          publicKey = keyPair.publicKey;
          privateKey = keyPair.privateKey;
        }
      }

      // Create encryption key
      const key = {
        id: uuidv4(),
        name: data.name,
        description: data.description || '',
        purpose: data.purpose,
        algorithm: data.algorithm,
        keyMaterial,
        publicKey,
        privateKey,
        active: data.active !== undefined ? data.active : true,
        rotationPeriod: data.rotationPeriod || null,
        lastRotated: null,
        createdBy: userId,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      // Save encryption key
      const keys = await this.loadData(this.encryptionKeysFile);
      keys.push(key);
      await this.saveData(this.encryptionKeysFile, keys);

      // Log security audit
      await this.logSecurityAudit({
        action: 'CREATE_ENCRYPTION_KEY',
        userId,
        resourceType: 'encryption_key',
        resourceId: key.id,
        details: {
          name: key.name,
          purpose: key.purpose,
          algorithm: key.algorithm
        }
      });

      // For security, don't return the actual key material in the response
      return {
        ...key,
        keyMaterial: undefined,
        privateKey: undefined
      };
    } catch (error) {
      console.error('Error creating encryption key:', error);
      throw error;
    }
  }

  /**
   * Update encryption key
   */
  async updateEncryptionKey(id, data, userId) {
    try {
      const keys = await this.loadData(this.encryptionKeysFile);
      const index = keys.findIndex(k => k.id === id);

      if (index === -1) {
        throw new NotFoundError(`Encryption key with ID ${id} not found`);
      }

      const key = keys[index];

      // Update encryption key
      const updatedKey = {
        ...key,
        name: data.name || key.name,
        description: data.description !== undefined ? data.description : key.description,
        active: data.active !== undefined ? data.active : key.active,
        rotationPeriod: data.rotationPeriod !== undefined ? data.rotationPeriod : key.rotationPeriod,
        updated: new Date().toISOString()
      };

      keys[index] = updatedKey;
      await this.saveData(this.encryptionKeysFile, keys);

      // Log security audit
      await this.logSecurityAudit({
        action: 'UPDATE_ENCRYPTION_KEY',
        userId,
        resourceType: 'encryption_key',
        resourceId: updatedKey.id,
        details: {
          name: updatedKey.name,
          active: updatedKey.active
        }
      });

      // For security, don't return the actual key material in the response
      return {
        ...updatedKey,
        keyMaterial: undefined,
        privateKey: undefined
      };
    } catch (error) {
      console.error('Error updating encryption key:', error);
      throw error;
    }
  }

  /**
   * Rotate encryption key
   */
  async rotateEncryptionKey(id, userId) {
    try {
      const keys = await this.loadData(this.encryptionKeysFile);
      const index = keys.findIndex(k => k.id === id);

      if (index === -1) {
        throw new NotFoundError(`Encryption key with ID ${id} not found`);
      }

      const key = keys[index];

      // Generate new key material
      const keyPair = await this.generateKeyPair(key.algorithm);

      // Create new key with rotated material
      const rotatedKey = {
        ...key,
        keyMaterial: keyPair.type === 'symmetric' ? keyPair.key : key.keyMaterial,
        publicKey: keyPair.type === 'asymmetric' ? keyPair.publicKey : key.publicKey,
        privateKey: keyPair.type === 'asymmetric' ? keyPair.privateKey : key.privateKey,
        lastRotated: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      keys[index] = rotatedKey;
      await this.saveData(this.encryptionKeysFile, keys);

      // Log security audit
      await this.logSecurityAudit({
        action: 'ROTATE_ENCRYPTION_KEY',
        userId,
        resourceType: 'encryption_key',
        resourceId: rotatedKey.id,
        details: {
          name: rotatedKey.name,
          algorithm: rotatedKey.algorithm
        }
      });

      // For security, don't return the actual key material in the response
      return {
        ...rotatedKey,
        keyMaterial: undefined,
        privateKey: undefined
      };
    } catch (error) {
      console.error('Error rotating encryption key:', error);
      throw error;
    }
  }

  /**
   * Delete encryption key
   */
  async deleteEncryptionKey(id, userId) {
    try {
      const keys = await this.loadData(this.encryptionKeysFile);
      const index = keys.findIndex(k => k.id === id);

      if (index === -1) {
        throw new NotFoundError(`Encryption key with ID ${id} not found`);
      }

      const key = keys[index];

      // Remove encryption key
      keys.splice(index, 1);
      await this.saveData(this.encryptionKeysFile, keys);

      // Log security audit
      await this.logSecurityAudit({
        action: 'DELETE_ENCRYPTION_KEY',
        userId,
        resourceType: 'encryption_key',
        resourceId: id,
        details: {
          name: key.name,
          purpose: key.purpose
        }
      });

      return { success: true, message: `Encryption key ${id} deleted` };
    } catch (error) {
      console.error('Error deleting encryption key:', error);
      throw error;
    }
  }

  /**
   * Generate key pair
   */
  async generateKeyPair(algorithm) {
    try {
      switch (algorithm) {
        case 'AES-256-GCM': {
          // Generate a random 256-bit key (32 bytes)
          const key = crypto.randomBytes(32).toString('base64');
          return { type: 'symmetric', key };
        }

        case 'RSA-2048': {
          // Generate RSA key pair
          const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: 2048,
            publicKeyEncoding: {
              type: 'spki',
              format: 'pem'
            },
            privateKeyEncoding: {
              type: 'pkcs8',
              format: 'pem'
            }
          });

          return { type: 'asymmetric', publicKey, privateKey };
        }

        case 'RSA-4096': {
          // Generate RSA key pair
          const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
            modulusLength: 4096,
            publicKeyEncoding: {
              type: 'spki',
              format: 'pem'
            },
            privateKeyEncoding: {
              type: 'pkcs8',
              format: 'pem'
            }
          });

          return { type: 'asymmetric', publicKey, privateKey };
        }

        case 'EC-P256': {
          // Generate EC key pair
          const { publicKey, privateKey } = crypto.generateKeyPairSync('ec', {
            namedCurve: 'prime256v1',
            publicKeyEncoding: {
              type: 'spki',
              format: 'pem'
            },
            privateKeyEncoding: {
              type: 'pkcs8',
              format: 'pem'
            }
          });

          return { type: 'asymmetric', publicKey, privateKey };
        }

        default:
          throw new ValidationError(`Unsupported encryption algorithm: ${algorithm}`);
      }
    } catch (error) {
      console.error('Error generating key pair:', error);
      throw error;
    }
  }

  /**
   * Encrypt data
   */
  async encryptData(data, keyId) {
    try {
      // Get encryption key
      const key = await this.getEncryptionKeyById(keyId, true);

      if (!key.active) {
        throw new ValidationError(`Encryption key ${keyId} is not active`);
      }

      // Encrypt data based on algorithm
      switch (key.algorithm) {
        case 'AES-256-GCM': {
          // Generate a random initialization vector
          const iv = crypto.randomBytes(16);

          // Create cipher using key and iv
          const cipher = crypto.createCipheriv(
            'aes-256-gcm',
            Buffer.from(key.keyMaterial, 'base64'),
            iv
          );

          // Encrypt the data
          let encrypted = cipher.update(data, 'utf8', 'base64');
          encrypted += cipher.final('base64');

          // Get the auth tag
          const authTag = cipher.getAuthTag().toString('base64');

          // Return the encrypted data with iv and auth tag
          return {
            algorithm: key.algorithm,
            keyId: key.id,
            iv: iv.toString('base64'),
            authTag,
            encryptedData: encrypted
          };
        }

        case 'RSA-2048':
        case 'RSA-4096': {
          // Encrypt with public key
          const encrypted = crypto.publicEncrypt(
            {
              key: key.publicKey,
              padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
            },
            Buffer.from(data, 'utf8')
          );

          return {
            algorithm: key.algorithm,
            keyId: key.id,
            encryptedData: encrypted.toString('base64')
          };
        }

        default:
          throw new ValidationError(`Encryption not implemented for algorithm: ${key.algorithm}`);
      }
    } catch (error) {
      console.error('Error encrypting data:', error);
      throw error;
    }
  }

  /**
   * Decrypt data
   */
  async decryptData(encryptedPackage) {
    try {
      // Validate required fields
      if (!encryptedPackage.keyId) {
        throw new ValidationError('Key ID is required');
      }

      if (!encryptedPackage.algorithm) {
        throw new ValidationError('Algorithm is required');
      }

      if (!encryptedPackage.encryptedData) {
        throw new ValidationError('Encrypted data is required');
      }

      // Get encryption key
      const key = await this.getEncryptionKeyById(encryptedPackage.keyId, true);

      if (!key.active) {
        throw new ValidationError(`Encryption key ${encryptedPackage.keyId} is not active`);
      }

      // Decrypt data based on algorithm
      switch (encryptedPackage.algorithm) {
        case 'AES-256-GCM': {
          // Validate required fields for AES-GCM
          if (!encryptedPackage.iv) {
            throw new ValidationError('Initialization vector (iv) is required for AES-GCM');
          }

          if (!encryptedPackage.authTag) {
            throw new ValidationError('Authentication tag is required for AES-GCM');
          }

          // Create decipher
          const decipher = crypto.createDecipheriv(
            'aes-256-gcm',
            Buffer.from(key.keyMaterial, 'base64'),
            Buffer.from(encryptedPackage.iv, 'base64')
          );

          // Set auth tag
          decipher.setAuthTag(Buffer.from(encryptedPackage.authTag, 'base64'));

          // Decrypt the data
          let decrypted = decipher.update(encryptedPackage.encryptedData, 'base64', 'utf8');
          decrypted += decipher.final('utf8');

          return decrypted;
        }

        case 'RSA-2048':
        case 'RSA-4096': {
          // Decrypt with private key
          const decrypted = crypto.privateDecrypt(
            {
              key: key.privateKey,
              padding: crypto.constants.RSA_PKCS1_OAEP_PADDING
            },
            Buffer.from(encryptedPackage.encryptedData, 'base64')
          );

          return decrypted.toString('utf8');
        }

        default:
          throw new ValidationError(`Decryption not implemented for algorithm: ${encryptedPackage.algorithm}`);
      }
    } catch (error) {
      console.error('Error decrypting data:', error);
      throw error;
    }
  }

  /**
   * Get all security policies
   */
  async getAllSecurityPolicies(filters = {}) {
    try {
      const policies = await this.loadData(this.securityPoliciesFile);

      // Apply filters
      let filteredPolicies = policies;

      if (filters.type) {
        filteredPolicies = filteredPolicies.filter(p => p.type === filters.type);
      }

      if (filters.enabled !== undefined) {
        filteredPolicies = filteredPolicies.filter(p => p.enabled === filters.enabled);
      }

      return filteredPolicies;
    } catch (error) {
      console.error('Error getting security policies:', error);
      throw error;
    }
  }

  /**
   * Get security policy by ID
   */
  async getSecurityPolicyById(id) {
    try {
      const policies = await this.loadData(this.securityPoliciesFile);
      const policy = policies.find(p => p.id === id);

      if (!policy) {
        throw new NotFoundError(`Security policy with ID ${id} not found`);
      }

      return policy;
    } catch (error) {
      console.error('Error getting security policy:', error);
      throw error;
    }
  }

  /**
   * Create security policy
   */
  async createSecurityPolicy(data, userId) {
    try {
      // Validate required fields
      if (!data.name) {
        throw new ValidationError('Security policy name is required');
      }

      if (!data.type) {
        throw new ValidationError('Security policy type is required');
      }

      if (!data.rules || !Array.isArray(data.rules) || data.rules.length === 0) {
        throw new ValidationError('At least one rule is required');
      }

      // Validate rules
      for (const rule of data.rules) {
        if (!rule.name) {
          throw new ValidationError('Rule name is required');
        }

        if (!rule.condition) {
          throw new ValidationError(`Condition is required for rule: ${rule.name}`);
        }

        if (!rule.action) {
          throw new ValidationError(`Action is required for rule: ${rule.name}`);
        }
      }

      // Create security policy
      const policy = {
        id: uuidv4(),
        name: data.name,
        description: data.description || '',
        type: data.type,
        rules: data.rules,
        enabled: data.enabled !== undefined ? data.enabled : true,
        createdBy: userId,
        created: new Date().toISOString(),
        updated: new Date().toISOString()
      };

      // Save security policy
      const policies = await this.loadData(this.securityPoliciesFile);
      policies.push(policy);
      await this.saveData(this.securityPoliciesFile, policies);

      // Log security audit
      await this.logSecurityAudit({
        action: 'CREATE_SECURITY_POLICY',
        userId,
        resourceType: 'security_policy',
        resourceId: policy.id,
        details: {
          name: policy.name,
          type: policy.type,
          rulesCount: policy.rules.length
        }
      });

      return policy;
    } catch (error) {
      console.error('Error creating security policy:', error);
      throw error;
    }
  }

  /**
   * Update security policy
   */
  async updateSecurityPolicy(id, data, userId) {
    try {
      const policies = await this.loadData(this.securityPoliciesFile);
      const index = policies.findIndex(p => p.id === id);

      if (index === -1) {
        throw new NotFoundError(`Security policy with ID ${id} not found`);
      }

      const policy = policies[index];

      // Validate rules if provided
      if (data.rules) {
        if (!Array.isArray(data.rules) || data.rules.length === 0) {
          throw new ValidationError('At least one rule is required');
        }

        for (const rule of data.rules) {
          if (!rule.name) {
            throw new ValidationError('Rule name is required');
          }

          if (!rule.condition) {
            throw new ValidationError(`Condition is required for rule: ${rule.name}`);
          }

          if (!rule.action) {
            throw new ValidationError(`Action is required for rule: ${rule.name}`);
          }
        }
      }

      // Update security policy
      const updatedPolicy = {
        ...policy,
        name: data.name || policy.name,
        description: data.description !== undefined ? data.description : policy.description,
        rules: data.rules || policy.rules,
        enabled: data.enabled !== undefined ? data.enabled : policy.enabled,
        updated: new Date().toISOString()
      };

      policies[index] = updatedPolicy;
      await this.saveData(this.securityPoliciesFile, policies);

      // Log security audit
      await this.logSecurityAudit({
        action: 'UPDATE_SECURITY_POLICY',
        userId,
        resourceType: 'security_policy',
        resourceId: updatedPolicy.id,
        details: {
          name: updatedPolicy.name,
          enabled: updatedPolicy.enabled,
          rulesCount: updatedPolicy.rules.length
        }
      });

      return updatedPolicy;
    } catch (error) {
      console.error('Error updating security policy:', error);
      throw error;
    }
  }

  /**
   * Delete security policy
   */
  async deleteSecurityPolicy(id, userId) {
    try {
      const policies = await this.loadData(this.securityPoliciesFile);
      const index = policies.findIndex(p => p.id === id);

      if (index === -1) {
        throw new NotFoundError(`Security policy with ID ${id} not found`);
      }

      const policy = policies[index];

      // Remove security policy
      policies.splice(index, 1);
      await this.saveData(this.securityPoliciesFile, policies);

      // Log security audit
      await this.logSecurityAudit({
        action: 'DELETE_SECURITY_POLICY',
        userId,
        resourceType: 'security_policy',
        resourceId: id,
        details: {
          name: policy.name,
          type: policy.type
        }
      });

      return { success: true, message: `Security policy ${id} deleted` };
    } catch (error) {
      console.error('Error deleting security policy:', error);
      throw error;
    }
  }

  /**
   * Evaluate security policy
   */
  async evaluateSecurityPolicy(policyId, context) {
    try {
      const policy = await this.getSecurityPolicyById(policyId);

      if (!policy.enabled) {
        // Policy is disabled, return default allow
        return { allowed: true, reason: 'Policy is disabled' };
      }

      // Evaluate each rule in order
      for (const rule of policy.rules) {
        const conditionMet = this.evaluateCondition(rule.condition, context);

        if (conditionMet) {
          // Rule condition is met, apply action
          const action = rule.action.toLowerCase();

          // Log policy evaluation
          await this.logSecurityAudit({
            action: 'EVALUATE_SECURITY_POLICY',
            userId: context.userId || 'system',
            resourceType: policy.type,
            resourceId: policyId,
            details: {
              policyName: policy.name,
              ruleName: rule.name,
              action: action,
              context: JSON.stringify(context)
            },
            success: action !== 'deny'
          });

          if (action === 'allow') {
            return { allowed: true, reason: rule.name };
          } else if (action === 'deny') {
            return { allowed: false, reason: rule.name };
          } else if (action.startsWith('require_')) {
            // Handle special requirements
            const requirement = action.substring('require_'.length);

            if (requirement === 'mfa' && !context.mfaVerified) {
              return { allowed: false, reason: 'MFA required', requireMfa: true };
            } else if (requirement === 'approval' && !context.approved) {
              return { allowed: false, reason: 'Approval required', requireApproval: true };
            }
          }
        }
      }

      // No rules matched, use default action (usually deny)
      return { allowed: false, reason: 'No matching rules' };
    } catch (error) {
      console.error('Error evaluating security policy:', error);
      // Default to deny on error
      return { allowed: false, reason: 'Error evaluating policy' };
    }
  }

  /**
   * Evaluate condition
   */
  evaluateCondition(condition, context) {
    try {
      // Simple condition evaluation for common patterns
      if (typeof condition === 'string') {
        // Parse condition string (e.g., "ip == '***********'" or "role == 'admin'")
        const parts = condition.split(/\s+/);

        if (parts.length === 3) {
          const [left, operator, right] = parts;
          const leftValue = this.getValueFromContext(left, context);
          const rightValue = this.parseValue(right);

          return this.compareValues(leftValue, operator, rightValue);
        }
      } else if (typeof condition === 'object') {
        // Object condition with AND/OR logic
        if (condition.and && Array.isArray(condition.and)) {
          return condition.and.every(subCondition =>
            this.evaluateCondition(subCondition, context)
          );
        } else if (condition.or && Array.isArray(condition.or)) {
          return condition.or.some(subCondition =>
            this.evaluateCondition(subCondition, context)
          );
        } else if (condition.not) {
          return !this.evaluateCondition(condition.not, context);
        } else if (condition.field && condition.operator && condition.value !== undefined) {
          const leftValue = this.getValueFromContext(condition.field, context);
          return this.compareValues(leftValue, condition.operator, condition.value);
        }
      }

      // Default to false for invalid conditions
      return false;
    } catch (error) {
      console.error('Error evaluating condition:', error);
      return false;
    }
  }

  /**
   * Get value from context
   */
  getValueFromContext(field, context) {
    // Handle nested fields (e.g., "user.role")
    const parts = field.split('.');
    let value = context;

    for (const part of parts) {
      if (value === undefined || value === null) {
        return undefined;
      }

      value = value[part];
    }

    return value;
  }

  /**
   * Parse value from string
   */
  parseValue(valueStr) {
    if (valueStr === 'true') {
      return true;
    } else if (valueStr === 'false') {
      return false;
    } else if (valueStr === 'null') {
      return null;
    } else if (valueStr === 'undefined') {
      return undefined;
    } else if (valueStr.startsWith('\'') && valueStr.endsWith('\'')) {
      // String value
      return valueStr.substring(1, valueStr.length - 1);
    } else if (!isNaN(Number(valueStr))) {
      // Numeric value
      return Number(valueStr);
    }

    // Default to string
    return valueStr;
  }

  /**
   * Compare values
   */
  compareValues(left, operator, right) {
    switch (operator) {
      case '==':
        return left == right; // eslint-disable-line eqeqeq
      case '!=':
        return left != right; // eslint-disable-line eqeqeq
      case '===':
        return left === right;
      case '!==':
        return left !== right;
      case '<':
        return left < right;
      case '<=':
        return left <= right;
      case '>':
        return left > right;
      case '>=':
        return left >= right;
      case 'in':
        return Array.isArray(right) && right.includes(left);
      case 'contains':
        return typeof left === 'string' && typeof right === 'string' && left.includes(right);
      case 'startsWith':
        return typeof left === 'string' && typeof right === 'string' && left.startsWith(right);
      case 'endsWith':
        return typeof left === 'string' && typeof right === 'string' && left.endsWith(right);
      case 'matches':
        return typeof left === 'string' && new RegExp(right).test(left);
      default:
        return false;
    }
  }
}

module.exports = SecurityService;
