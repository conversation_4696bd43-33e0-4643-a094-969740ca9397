/**
 * SecureTemporaryCache.js
 * 
 * This module provides secure temporary caching for emergency medical data.
 * It ensures that data is only cached for the duration of an emergency and
 * is securely deleted afterward.
 */

const crypto = require('crypto');

/**
 * SecureTemporaryCache class for secure temporary caching
 */
class SecureTemporaryCache {
  constructor(options = {}) {
    this.enabled = options.enabled !== false;
    this.defaultTTL = options.defaultTTL || 300000; // 5 minutes
    this.maxTTL = options.maxTTL || 3600000; // 1 hour
    this.encryptionEnabled = options.encryptionEnabled !== false;
    this.encryptionKey = options.encryptionKey || this._generateEncryptionKey();
    this.cache = new Map();
    this.accessLog = [];
    this.maxLogSize = options.maxLogSize || 1000;
    
    // Set up automatic cleanup
    this.cleanupInterval = setInterval(() => {
      this._cleanupExpiredEntries();
    }, 60000); // Clean up every minute
  }

  /**
   * Store data in the cache
   * @param {String} key - The cache key
   * @param {Object} data - The data to cache
   * @param {Object} options - Cache options
   * @returns {Boolean} - Whether the data was cached successfully
   */
  store(key, data, options = {}) {
    if (!this.enabled) {
      return false;
    }
    
    if (!key || !data) {
      return false;
    }
    
    // Determine TTL
    const ttl = options.ttl || this.defaultTTL;
    
    // Ensure TTL is not greater than maxTTL
    const effectiveTTL = Math.min(ttl, this.maxTTL);
    
    // Calculate expiration time
    const expiresAt = Date.now() + effectiveTTL;
    
    // Encrypt data if enabled
    const storedData = this.encryptionEnabled ? this._encrypt(data) : data;
    
    // Create cache entry
    const entry = {
      key,
      data: storedData,
      encrypted: this.encryptionEnabled,
      createdAt: Date.now(),
      expiresAt,
      context: options.context || {},
      accessCount: 0,
      lastAccessed: null
    };
    
    // Store in cache
    this.cache.set(key, entry);
    
    // Log the storage
    this._logAccess(key, 'STORE', options.context);
    
    return true;
  }

  /**
   * Retrieve data from the cache
   * @param {String} key - The cache key
   * @param {Object} context - The access context
   * @returns {Object|null} - The cached data or null if not found
   */
  retrieve(key, context = {}) {
    if (!this.enabled) {
      return null;
    }
    
    if (!key) {
      return null;
    }
    
    // Get cache entry
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // Check if expired
    if (entry.expiresAt < Date.now()) {
      // Remove expired entry
      this.cache.delete(key);
      
      // Log the expiration
      this._logAccess(key, 'EXPIRED', context);
      
      return null;
    }
    
    // Update access information
    entry.accessCount += 1;
    entry.lastAccessed = Date.now();
    
    // Log the access
    this._logAccess(key, 'RETRIEVE', context);
    
    // Decrypt data if encrypted
    const data = entry.encrypted ? this._decrypt(entry.data) : entry.data;
    
    return data;
  }

  /**
   * Remove data from the cache
   * @param {String} key - The cache key
   * @param {Object} context - The removal context
   * @returns {Boolean} - Whether the data was removed successfully
   */
  remove(key, context = {}) {
    if (!key) {
      return false;
    }
    
    // Check if entry exists
    if (!this.cache.has(key)) {
      return false;
    }
    
    // Remove from cache
    this.cache.delete(key);
    
    // Log the removal
    this._logAccess(key, 'REMOVE', context);
    
    return true;
  }

  /**
   * Clear all data from the cache
   * @param {Object} context - The clear context
   * @returns {Boolean} - Whether the cache was cleared successfully
   */
  clear(context = {}) {
    // Log the clear
    this._logAccess('ALL', 'CLEAR', context);
    
    // Clear the cache
    this.cache.clear();
    
    return true;
  }

  /**
   * Get cache statistics
   * @returns {Object} - The cache statistics
   */
  getStats() {
    const now = Date.now();
    let activeEntries = 0;
    let expiredEntries = 0;
    let totalSize = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt >= now) {
        activeEntries += 1;
        
        // Estimate size in bytes
        const entrySize = this._estimateSize(entry);
        totalSize += entrySize;
      } else {
        expiredEntries += 1;
      }
    }
    
    return {
      enabled: this.enabled,
      encryptionEnabled: this.encryptionEnabled,
      activeEntries,
      expiredEntries,
      totalEntries: this.cache.size,
      totalSize,
      accessLogSize: this.accessLog.length
    };
  }

  /**
   * Get access logs
   * @param {Object} options - Options for filtering logs
   * @returns {Array} - The access logs
   */
  getAccessLogs(options = {}) {
    let filteredLogs = [...this.accessLog];
    
    // Apply filters
    if (options.key) {
      filteredLogs = filteredLogs.filter(log => log.key === options.key);
    }
    
    if (options.action) {
      filteredLogs = filteredLogs.filter(log => log.action === options.action);
    }
    
    if (options.startTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= options.startTime);
    }
    
    if (options.endTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= options.endTime);
    }
    
    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp - a.timestamp);
    
    // Apply limit
    if (options.limit && options.limit > 0) {
      filteredLogs = filteredLogs.slice(0, options.limit);
    }
    
    return filteredLogs;
  }

  /**
   * Destroy the cache
   */
  destroy() {
    // Clear cleanup interval
    clearInterval(this.cleanupInterval);
    
    // Clear the cache
    this.cache.clear();
    
    // Clear the access log
    this.accessLog = [];
    
    // Clear the encryption key
    this.encryptionKey = null;
  }

  /**
   * Generate encryption key
   * @returns {Buffer} - The encryption key
   * @private
   */
  _generateEncryptionKey() {
    return crypto.randomBytes(32); // 256-bit key
  }

  /**
   * Encrypt data
   * @param {Object} data - The data to encrypt
   * @returns {Object} - The encrypted data
   * @private
   */
  _encrypt(data) {
    try {
      // Convert data to string
      const dataString = JSON.stringify(data);
      
      // Generate initialization vector
      const iv = crypto.randomBytes(16);
      
      // Create cipher
      const cipher = crypto.createCipheriv('aes-256-gcm', this.encryptionKey, iv);
      
      // Encrypt data
      let encrypted = cipher.update(dataString, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Get authentication tag
      const authTag = cipher.getAuthTag();
      
      return {
        encrypted,
        iv: iv.toString('hex'),
        authTag: authTag.toString('hex')
      };
    } catch (error) {
      console.error('Encryption error:', error);
      return data; // Return original data on error
    }
  }

  /**
   * Decrypt data
   * @param {Object} encryptedData - The encrypted data
   * @returns {Object} - The decrypted data
   * @private
   */
  _decrypt(encryptedData) {
    try {
      // Check if data is encrypted
      if (!encryptedData.encrypted || !encryptedData.iv || !encryptedData.authTag) {
        return encryptedData;
      }
      
      // Convert IV and auth tag to buffers
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const authTag = Buffer.from(encryptedData.authTag, 'hex');
      
      // Create decipher
      const decipher = crypto.createDecipheriv('aes-256-gcm', this.encryptionKey, iv);
      decipher.setAuthTag(authTag);
      
      // Decrypt data
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      // Parse JSON
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Decryption error:', error);
      return null; // Return null on error
    }
  }

  /**
   * Clean up expired entries
   * @private
   */
  _cleanupExpiredEntries() {
    const now = Date.now();
    const expiredKeys = [];
    
    // Find expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt < now) {
        expiredKeys.push(key);
      }
    }
    
    // Remove expired entries
    for (const key of expiredKeys) {
      this.cache.delete(key);
      
      // Log the expiration
      this._logAccess(key, 'EXPIRED', { automatic: true });
    }
  }

  /**
   * Log cache access
   * @param {String} key - The cache key
   * @param {String} action - The action performed
   * @param {Object} context - The access context
   * @private
   */
  _logAccess(key, action, context = {}) {
    // Create log entry
    const logEntry = {
      key,
      action,
      timestamp: Date.now(),
      context
    };
    
    // Add to log
    this.accessLog.push(logEntry);
    
    // Limit log size
    if (this.accessLog.length > this.maxLogSize) {
      this.accessLog = this.accessLog.slice(-this.maxLogSize);
    }
  }

  /**
   * Estimate size of a cache entry
   * @param {Object} entry - The cache entry
   * @returns {Number} - The estimated size in bytes
   * @private
   */
  _estimateSize(entry) {
    // Estimate key size
    const keySize = entry.key.length * 2; // Approximate size in bytes
    
    // Estimate data size
    let dataSize = 0;
    
    if (entry.encrypted) {
      // For encrypted data, use the length of the encrypted string
      dataSize = entry.data.encrypted.length / 2; // Convert hex to bytes
      dataSize += entry.data.iv.length / 2;
      dataSize += entry.data.authTag.length / 2;
    } else {
      // For unencrypted data, use JSON.stringify
      dataSize = JSON.stringify(entry.data).length * 2;
    }
    
    // Estimate context size
    const contextSize = JSON.stringify(entry.context).length * 2;
    
    // Add fixed overhead for timestamps, etc.
    const overhead = 100;
    
    return keySize + dataSize + contextSize + overhead;
  }
}

module.exports = SecureTemporaryCache;
