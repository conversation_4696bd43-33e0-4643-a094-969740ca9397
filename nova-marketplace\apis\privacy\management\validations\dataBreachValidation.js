/**
 * Data Breach Validation Schemas
 * 
 * This file defines the validation schemas for data breach endpoints.
 */

const Joi = require('joi');
const { objectIdPattern } = require('./validationPatterns');

// Placeholder validation schema
const getDataBreaches = {
  query: Joi.object({
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100),
    sort: Joi.string().trim()
  })
};

module.exports = {
  getDataBreaches
};
