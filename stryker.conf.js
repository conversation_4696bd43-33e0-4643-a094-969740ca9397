/**
 * @type {import('@stryker-mutator/api/core').StrykerOptions}
 */
module.exports = {
  packageManager: 'npm',
  reporters: ['html', 'clear-text', 'progress'],
  testRunner: 'jest',
  coverageAnalysis: 'perTest',
  jest: {
    projectType: 'custom',
    configFile: 'jest.config.js',
    enableFindRelatedTests: true
  },
  mutate: [
    'components/**/*.js',
    '!components/**/*.test.js',
    // Focus on critical compliance logic first
    'utils/compliance/**/*.js',
    'utils/validation/**/*.js',
    'utils/security/**/*.js',
  ],
  // Focus on critical components first
  mutator: {
    excludedMutations: ['StringLiteral', 'ArrayDeclaration']
  },
  timeoutMS: 60000,
  timeoutFactor: 1.5,
  maxConcurrentTestRunners: 4,
  dashboard: {
    project: 'github.com/Dartan1983/nova-marketplace',
    version: 'main',
    module: 'novafuse-api-superstore'
  },
  thresholds: {
    high: 80,
    low: 60,
    break: 50
  }
};
