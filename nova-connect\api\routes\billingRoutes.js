/**
 * Billing Routes
 * 
 * This file contains routes for billing-related operations.
 */

const express = require('express');
const router = express.Router();
const BillingController = require('../controllers/BillingController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// GCP Marketplace webhook (no authentication required)
router.post('/webhook', BillingController.handleMarketplaceWebhook);

// All other routes require authentication
router.use(authenticate);

// Entitlement management (admin only)
router.post('/entitlements', hasPermission('admin:billing'), BillingController.handleEntitlementCreation);
router.put('/entitlements', hasPermission('admin:billing'), BillingController.handleEntitlementUpdate);
router.delete('/entitlements', hasPermission('admin:billing'), BillingController.handleEntitlementDeletion);
router.post('/entitlements/activate', hasPermission('admin:billing'), BillingController.handleEntitlementActivation);
router.post('/entitlements/suspend', hasPermission('admin:billing'), BillingController.handleEntitlementSuspension);

// Customer entitlements and usage (admin or customer)
router.get('/entitlements/:customerId', hasPermission('admin:billing', 'customer:view'), BillingController.getCustomerEntitlements);
router.get('/usage/:customerId', hasPermission('admin:billing', 'customer:view'), BillingController.getCustomerUsage);

// Usage reporting (admin or service)
router.post('/usage', hasPermission('admin:billing', 'service:billing'), BillingController.reportUsage);
router.post('/usage/tenant', hasPermission('admin:billing', 'service:billing'), BillingController.reportTenantUsage);

module.exports = router;
