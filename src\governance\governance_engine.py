"""
C-AIaaS Governance Engine

Implements the physics-based decision-making system for the C-AIaaS platform.
Uses Supabase as the backend data store and implements the ∂Ψ=0 coherence protocol.
"""

import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from supabase import Client, create_client
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DecisionType(str, Enum):
    """Possible decision outcomes for task evaluation"""
    AUTO_APPROVED = "Auto-Approved"
    REQUIRES_APPROVAL = "Requires Approval"
    ESCALATE = "Escalate"

@dataclass
class Task:
    """Represents a task to be evaluated by the governance engine"""
    id: str
    name: str
    type: str
    budget: float
    deadline_hours: int
    vendor_id: str
    role_id: str
    vendor_score: Optional[float] = None
    signature: Optional[str] = None

@dataclass
class Decision:
    """Represents a decision made by the governance engine"""
    task_id: str
    entropy_value: float
    threshold: float
    q_factor: float
    decision: DecisionType
    entropy_signature: str
    is_recurring: bool = False
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

class EntropyValidator:
    """
    Implements the ∂Ψ=0 coherence protocol for task evaluation.
    
    The validator calculates the entropy of a task based on:
    - Time constraints
    - Energy (vendor Q-Score)
    - Effort (budget)
    
    Tasks are evaluated against role-specific thresholds and task-type modifiers.
    """
    
    def __init__(self, supabase: Optional[Client] = None):
        """Initialize the validator with an optional Supabase client"""
        self.supabase = supabase or self._init_supabase()
        self.entropy_cache: Dict[str, float] = {}
        
    @staticmethod
    def _init_supabase() -> Client:
        """Initialize Supabase client from environment variables"""
        load_dotenv()
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        if not url or not key:
            raise ValueError("Missing SUPABASE_URL or SUPABASE_KEY in environment")
        return create_client(url, key)
    
    async def get_role_threshold(self, role_id: str) -> Dict[str, Any]:
        """Retrieve role-specific thresholds and modifiers from Supabase"""
        try:
            result = self.supabase.table('roles').select('*').eq('id', role_id).single().execute()
            if not result.data:
                raise ValueError(f"Role {role_id} not found")
            return result.data
        except Exception as e:
            logger.error(f"Error fetching role {role_id}: {e}")
            # Return default values if role not found
            return {
                'spend_limit': 5000,
                'q_score_threshold': 7.0,
                'entropy_modifiers': {'default': 1.0}
            }
    
    async def get_vendor_q_score(self, vendor_id: str) -> float:
        """Retrieve vendor's current Q-Score from Supabase"""
        try:
            result = self.supabase.table('vendors') \
                .select('current_q_score') \
                .eq('id', vendor_id) \
                .single() \
                .execute()
            return float(result.data.get('current_q_score', 5.0))
        except Exception as e:
            logger.warning(f"Error fetching Q-Score for vendor {vendor_id}: {e}")
            return 5.0  # Default to neutral score
    
    def calculate_entropy(self, task: Task, q_score: float) -> float:
        """
        Calculate the entropy of a task using the Comphyology formula:
        
        ∂Ψ = (Effort / (Time * (Energy + 1))) * 100 * Q-Factor
        
        Where:
        - Effort = Budget
        - Time = Deadline in hours
        - Energy = Vendor Q-Score (5-10 scale)
        - Q-Factor = Task-type specific modifier
        """
        # Cache key for memoization
        cache_key = f"{task.id}:{q_score}:{task.type}"
        if cache_key in self.entropy_cache:
            return self.entropy_cache[cache_key]
        
        # Apply task-type specific modifier (default to 1.0 if not found)
        modifier = 1.0  # Default if no specific modifier found
        
        # Calculate base entropy
        time = max(task.deadline_hours, 1)  # Prevent division by zero
        energy = max(min(q_score, 10.0), 0.1)  # Clamp between 0.1 and 10.0
        
        # Apply Comphyology entropy formula
        entropy = (task.budget / (time * (energy + 1))) * 100 * modifier
        
        # Cache the result
        self.entropy_cache[cache_key] = entropy
        return entropy
    
    async def evaluate_task(self, task_data: Dict[str, Any]) -> Decision:
        """
        Evaluate a task using the ∂Ψ=0 coherence protocol.
        
        Args:
            task_data: Dictionary containing task details
            
        Returns:
            Decision object with evaluation results
        """
        # Validate task data
        task = Task(**task_data)
        
        # Get vendor Q-Score (async)
        q_score = await self.get_vendor_q_score(task.vendor_id)
        
        # Get role-specific thresholds (async)
        role = await self.get_role_threshold(task.role_id)
        
        # Calculate entropy
        entropy = self.calculate_entropy(task, q_score)
        
        # Get task-type specific threshold modifier
        task_type = task.type.lower()
        modifier = role.get('entropy_modifiers', {}).get(task_type, 1.0)
        threshold = 25.0 * modifier  # Base threshold adjusted by task type
        
        # Determine decision
        if entropy > threshold * 1.5:
            decision_type = DecisionType.ESCALATE
        elif task.budget <= role.get('spend_limit', 5000) and q_score >= role.get('q_score_threshold', 7.0):
            decision_type = DecisionType.AUTO_APPROVED
        else:
            decision_type = DecisionType.REQUIRES_APPROVAL
        
        # Generate entropy signature
        entropy_sig = f"{task.type}:{entropy:.2f}:{q_score:.1f}"
        
        # Check for recurring patterns (simplified)
        is_recurring = entropy_sig in self.entropy_cache
        
        # Create and return decision
        return Decision(
            task_id=task.id,
            entropy_value=entropy,
            threshold=threshold,
            q_factor=modifier,
            decision=decision_type,
            entropy_signature=entropy_sig,
            is_recurring=is_recurring
        )
    
    async def log_decision(self, decision: Decision) -> Dict[str, Any]:
        """Log a decision to the Supabase database"""
        try:
            decision_data = asdict(decision)
            decision_data['timestamp'] = decision.timestamp.isoformat()
            
            result = self.supabase.table('decisions') \
                .insert(decision_data) \
                .execute()
            
            logger.info(f"Logged decision for task {decision.task_id}")
            return result.data[0] if result.data else {}
            
        except Exception as e:
            logger.error(f"Error logging decision: {e}")
            raise

# Example usage
async def example_usage():
    """Example of how to use the EntropyValidator"""
    validator = EntropyValidator()
    
    # Example task data
    task_data = {
        "id": "task_123",
        "name": "API Integration",
        "type": "critical_bugfix",
        "budget": 4500.0,
        "deadline_hours": 48,
        "vendor_id": "vendor_123",
        "role_id": "role_456",
        "signature": "NOVA-1234567890ABCDEF"
    }
    
    # Evaluate task
    decision = await validator.evaluate_task(task_data)
    print(f"Decision: {decision.decision}")
    print(f"Entropy: {decision.entropy_value:.2f} (Threshold: {decision.threshold:.2f})")
    
    # Log decision
    await validator.log_decision(decision)

if __name__ == "__main__":
    import asyncio
    asyncio.run(example_usage())
