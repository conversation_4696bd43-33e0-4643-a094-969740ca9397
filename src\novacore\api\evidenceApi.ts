/**
 * evidenceApi.ts
 * 
 * API endpoints for evidence management in the NovaCore system.
 */

import express from 'express';
import { EvidenceService } from '../services/EvidenceService';
import { BlockchainService } from '../services/BlockchainService';
import { 
  Evidence, 
  EvidenceType, 
  EvidenceStatus,
  EvidenceMetadata 
} from '../models/Evidence';

// Create router
const router = express.Router();

// Create services
const blockchainService = new BlockchainService();
const evidenceService = new EvidenceService(blockchainService);

/**
 * GET /api/evidence
 * Get all evidence with optional filtering
 */
router.get('/', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, ...query } = req.query;
    
    const result = await evidenceService.searchEvidence(
      query,
      Number(page),
      Number(pageSize),
    );
    
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

/**
 * GET /api/evidence/:id
 * Get evidence by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const evidence = await evidenceService.getEvidence(req.params.id);
    res.json(evidence);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * POST /api/evidence
 * Create new evidence
 */
router.post('/', async (req, res) => {
  try {
    const {
      name,
      type,
      category,
      content,
      metadata,
      description,
      framework,
      control,
    } = req.body;
    
    // Validate required fields
    if (!name || !type || !category || !content) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system', organization: 'default' };
    
    // Create evidence
    const evidence = await evidenceService.createEvidence(
      name,
      type as EvidenceType,
      category,
      content,
      user.id,
      user.organization,
      metadata as EvidenceMetadata,
      description,
      framework,
      control,
    );
    
    res.status(201).json(evidence);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * PUT /api/evidence/:id
 * Update evidence
 */
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Update evidence
    const evidence = await evidenceService.updateEvidence(
      id,
      updates,
      user.id,
    );
    
    res.json(evidence);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * DELETE /api/evidence/:id
 * Delete evidence
 */
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Delete evidence
    await evidenceService.deleteEvidence(id);
    
    res.status(204).end();
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * POST /api/evidence/:id/versions
 * Create new version of evidence
 */
router.post('/:id/versions', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, metadata, comments } = req.body;
    
    // Validate required fields
    if (!content) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Create new version
    const evidence = await evidenceService.createVersion(
      id,
      content,
      user.id,
      metadata,
      comments,
    );
    
    res.status(201).json(evidence);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * GET /api/evidence/:id/versions
 * Get all versions of evidence
 */
router.get('/:id/versions', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get versions
    const versions = await evidenceService.listVersions(id);
    
    res.json(versions);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * GET /api/evidence/:id/versions/:versionId
 * Get specific version of evidence
 */
router.get('/:id/versions/:versionId', async (req, res) => {
  try {
    const { id, versionId } = req.params;
    
    // Get version
    const version = await evidenceService.getVersion(id, versionId);
    
    res.json(version);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

/**
 * PUT /api/evidence/:id/status
 * Update evidence status
 */
router.put('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // Validate required fields
    if (!status) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    // Get user from request (in a real app, this would come from authentication)
    const user = req.user || { id: 'system' };
    
    // Update status
    const evidence = await evidenceService.updateStatus(
      id,
      status as EvidenceStatus,
      user.id,
    );
    
    res.json(evidence);
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/evidence/:id/verify
 * Verify evidence on blockchain
 */
router.post('/:id/verify', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Verify evidence
    const isVerified = await evidenceService.verifyEvidence(id);
    
    res.json({ verified: isVerified });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/evidence/:id/versions/:versionId/verify
 * Verify specific version of evidence on blockchain
 */
router.post('/:id/versions/:versionId/verify', async (req, res) => {
  try {
    const { id, versionId } = req.params;
    
    // Verify version
    const isVerified = await evidenceService.verifyVersion(id, versionId);
    
    res.json({ verified: isVerified });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * POST /api/evidence/:id/requirements/:requirementId
 * Link evidence to requirement
 */
router.post('/:id/requirements/:requirementId', async (req, res) => {
  try {
    const { id, requirementId } = req.params;
    
    // Link evidence to requirement
    await evidenceService.linkToRequirement(id, requirementId);
    
    res.status(204).end();
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * DELETE /api/evidence/:id/requirements/:requirementId
 * Unlink evidence from requirement
 */
router.delete('/:id/requirements/:requirementId', async (req, res) => {
  try {
    const { id, requirementId } = req.params;
    
    // Unlink evidence from requirement
    await evidenceService.unlinkFromRequirement(id, requirementId);
    
    res.status(204).end();
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});

/**
 * GET /api/evidence/:id/requirements
 * Get requirements linked to evidence
 */
router.get('/:id/requirements', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Get linked requirements
    const requirementIds = await evidenceService.getLinkedRequirements(id);
    
    res.json(requirementIds);
  } catch (error) {
    res.status(404).json({ error: error.message });
  }
});

export default router;
