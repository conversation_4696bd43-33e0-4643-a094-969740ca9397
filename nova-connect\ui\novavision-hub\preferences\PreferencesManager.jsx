/**
 * PreferencesManager Component
 * 
 * A component for managing user preferences.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { usePreferences } from './PreferencesContext';
import { TabPanel } from '../components';

/**
 * PreferencesManager component
 * 
 * @param {Object} props - Component props
 * @param {Function} [props.onClose] - Function to call when the preferences manager is closed
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} PreferencesManager component
 */
const PreferencesManager = ({
  onClose,
  className = '',
  style = {}
}) => {
  const { 
    preferences, 
    updateUISettings, 
    updateDataSettings, 
    updateNotificationSettings,
    resetPreferences
  } = usePreferences();
  
  const [activeTab, setActiveTab] = useState('ui');
  const [uiSettings, setUISettings] = useState(preferences.ui);
  const [dataSettings, setDataSettings] = useState(preferences.data);
  const [notificationSettings, setNotificationSettings] = useState(preferences.notifications);
  
  // Handle save
  const handleSave = () => {
    // Update UI settings
    updateUISettings(uiSettings);
    
    // Update data settings
    updateDataSettings(dataSettings);
    
    // Update notification settings
    updateNotificationSettings(notificationSettings);
    
    // Close preferences manager
    if (onClose) {
      onClose();
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    if (onClose) {
      onClose();
    }
  };
  
  // Handle reset
  const handleReset = () => {
    // Show confirmation dialog
    if (window.confirm('Are you sure you want to reset all preferences to defaults?')) {
      resetPreferences();
      
      // Close preferences manager
      if (onClose) {
        onClose();
      }
    }
  };
  
  // Update UI settings
  const handleUISettingsChange = (key, value) => {
    setUISettings(prevSettings => {
      if (key.includes('.')) {
        // Handle nested properties
        const [parent, child] = key.split('.');
        return {
          ...prevSettings,
          [parent]: {
            ...prevSettings[parent],
            [child]: value
          }
        };
      }
      
      return {
        ...prevSettings,
        [key]: value
      };
    });
  };
  
  // Update data settings
  const handleDataSettingsChange = (key, value) => {
    setDataSettings(prevSettings => {
      if (key.includes('.')) {
        // Handle nested properties
        const [parent, child] = key.split('.');
        return {
          ...prevSettings,
          [parent]: {
            ...prevSettings[parent],
            [child]: value
          }
        };
      }
      
      return {
        ...prevSettings,
        [key]: value
      };
    });
  };
  
  // Update notification settings
  const handleNotificationSettingsChange = (key, value) => {
    setNotificationSettings(prevSettings => {
      if (key.includes('.')) {
        // Handle nested properties
        const [parent, child, grandchild] = key.split('.');
        
        if (grandchild) {
          return {
            ...prevSettings,
            [parent]: {
              ...prevSettings[parent],
              [child]: {
                ...prevSettings[parent][child],
                [grandchild]: value
              }
            }
          };
        }
        
        return {
          ...prevSettings,
          [parent]: {
            ...prevSettings[parent],
            [child]: value
          }
        };
      }
      
      return {
        ...prevSettings,
        [key]: value
      };
    });
  };
  
  // Define tabs
  const tabs = [
    {
      id: 'ui',
      label: 'UI Settings',
      content: (
        <div className="space-y-6">
          {/* Theme settings */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Theme Settings</h3>
            <div className="space-y-4">
              {/* Theme name */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Theme</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={uiSettings.theme.name}
                  onChange={(e) => handleUISettingsChange('theme.name', e.target.value)}
                >
                  <option value="Default">Default</option>
                  <option value="Nova">Nova</option>
                  <option value="High Contrast">High Contrast</option>
                </select>
              </div>
              
              {/* Color mode */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Color Mode</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={uiSettings.theme.colorMode}
                  onChange={(e) => handleUISettingsChange('theme.colorMode', e.target.value)}
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                </select>
              </div>
              
              {/* Enable system preference detection */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableSystemPreference"
                  className="mr-2"
                  checked={uiSettings.theme.enableSystemPreference}
                  onChange={(e) => handleUISettingsChange('theme.enableSystemPreference', e.target.checked)}
                />
                <label htmlFor="enableSystemPreference" className="text-sm text-textPrimary">
                  Use system color scheme preference
                </label>
              </div>
            </div>
          </div>
          
          {/* Layout settings */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Layout Settings</h3>
            <div className="space-y-4">
              {/* Sidebar collapsed state */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="sidebarCollapsed"
                  className="mr-2"
                  checked={uiSettings.layout.sidebarCollapsed}
                  onChange={(e) => handleUISettingsChange('layout.sidebarCollapsed', e.target.checked)}
                />
                <label htmlFor="sidebarCollapsed" className="text-sm text-textPrimary">
                  Collapse sidebar by default
                </label>
              </div>
              
              {/* Content width */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Content Width</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={uiSettings.layout.contentWidth}
                  onChange={(e) => handleUISettingsChange('layout.contentWidth', e.target.value)}
                >
                  <option value="fluid">Fluid</option>
                  <option value="fixed">Fixed</option>
                </select>
              </div>
              
              {/* Show breadcrumbs */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showBreadcrumbs"
                  className="mr-2"
                  checked={uiSettings.layout.showBreadcrumbs}
                  onChange={(e) => handleUISettingsChange('layout.showBreadcrumbs', e.target.checked)}
                />
                <label htmlFor="showBreadcrumbs" className="text-sm text-textPrimary">
                  Show breadcrumbs
                </label>
              </div>
              
              {/* Show footer */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="showFooter"
                  className="mr-2"
                  checked={uiSettings.layout.showFooter}
                  onChange={(e) => handleUISettingsChange('layout.showFooter', e.target.checked)}
                />
                <label htmlFor="showFooter" className="text-sm text-textPrimary">
                  Show footer
                </label>
              </div>
            </div>
          </div>
          
          {/* Accessibility settings */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Accessibility Settings</h3>
            <div className="space-y-4">
              {/* Enable high contrast mode */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="highContrast"
                  className="mr-2"
                  checked={uiSettings.accessibility.highContrast}
                  onChange={(e) => handleUISettingsChange('accessibility.highContrast', e.target.checked)}
                />
                <label htmlFor="highContrast" className="text-sm text-textPrimary">
                  Enable high contrast mode
                </label>
              </div>
              
              {/* Enable reduced motion */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="reducedMotion"
                  className="mr-2"
                  checked={uiSettings.accessibility.reducedMotion}
                  onChange={(e) => handleUISettingsChange('accessibility.reducedMotion', e.target.checked)}
                />
                <label htmlFor="reducedMotion" className="text-sm text-textPrimary">
                  Enable reduced motion
                </label>
              </div>
              
              {/* Font size */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Font Size</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={uiSettings.accessibility.fontSize}
                  onChange={(e) => handleUISettingsChange('accessibility.fontSize', e.target.value)}
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'data',
      label: 'Data Settings',
      content: (
        <div className="space-y-6">
          {/* Data refresh settings */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Data Refresh Settings</h3>
            <div className="space-y-4">
              {/* Default refresh interval */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Default Refresh Interval</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={dataSettings.refreshInterval}
                  onChange={(e) => handleDataSettingsChange('refreshInterval', parseInt(e.target.value))}
                >
                  <option value="60">1 minute</option>
                  <option value="300">5 minutes</option>
                  <option value="600">10 minutes</option>
                  <option value="1800">30 minutes</option>
                  <option value="3600">1 hour</option>
                  <option value="0">Never</option>
                </select>
              </div>
            </div>
          </div>
          
          {/* Data display settings */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Data Display Settings</h3>
            <div className="space-y-4">
              {/* Default date range */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Default Date Range</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={dataSettings.defaultDateRange}
                  onChange={(e) => handleDataSettingsChange('defaultDateRange', e.target.value)}
                >
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="last7Days">Last 7 Days</option>
                  <option value="last30Days">Last 30 Days</option>
                  <option value="thisMonth">This Month</option>
                  <option value="lastMonth">Last Month</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
              
              {/* Default data view */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Default Data View</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={dataSettings.defaultView}
                  onChange={(e) => handleDataSettingsChange('defaultView', e.target.value)}
                >
                  <option value="chart">Chart</option>
                  <option value="table">Table</option>
                  <option value="card">Card</option>
                </select>
              </div>
              
              {/* Default sort order */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Default Sort Order</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={dataSettings.defaultSortOrder}
                  onChange={(e) => handleDataSettingsChange('defaultSortOrder', e.target.value)}
                >
                  <option value="asc">Ascending</option>
                  <option value="desc">Descending</option>
                </select>
              </div>
              
              {/* Default page size */}
              <div>
                <label className="block text-sm font-medium text-textSecondary mb-1">Default Page Size</label>
                <select
                  className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
                  value={dataSettings.defaultPageSize}
                  onChange={(e) => handleDataSettingsChange('defaultPageSize', parseInt(e.target.value))}
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'notifications',
      label: 'Notifications',
      content: (
        <div className="space-y-6">
          {/* Notification channels */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Notification Channels</h3>
            <div className="space-y-4">
              {/* Enable desktop notifications */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableDesktopNotifications"
                  className="mr-2"
                  checked={notificationSettings.enableDesktopNotifications}
                  onChange={(e) => handleNotificationSettingsChange('enableDesktopNotifications', e.target.checked)}
                />
                <label htmlFor="enableDesktopNotifications" className="text-sm text-textPrimary">
                  Enable desktop notifications
                </label>
              </div>
              
              {/* Enable email notifications */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableEmailNotifications"
                  className="mr-2"
                  checked={notificationSettings.enableEmailNotifications}
                  onChange={(e) => handleNotificationSettingsChange('enableEmailNotifications', e.target.checked)}
                />
                <label htmlFor="enableEmailNotifications" className="text-sm text-textPrimary">
                  Enable email notifications
                </label>
              </div>
              
              {/* Enable in-app notifications */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableInAppNotifications"
                  className="mr-2"
                  checked={notificationSettings.enableInAppNotifications}
                  onChange={(e) => handleNotificationSettingsChange('enableInAppNotifications', e.target.checked)}
                />
                <label htmlFor="enableInAppNotifications" className="text-sm text-textPrimary">
                  Enable in-app notifications
                </label>
              </div>
            </div>
          </div>
          
          {/* Notification settings by type */}
          <div>
            <h3 className="text-lg font-medium mb-4 text-textPrimary">Notification Settings by Type</h3>
            
            {/* Critical alerts */}
            <div className="mb-4">
              <h4 className="text-md font-medium mb-2 text-textPrimary">Critical Alerts</h4>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="criticalDesktop"
                    className="mr-2"
                    checked={notificationSettings.byType.critical.desktop}
                    onChange={(e) => handleNotificationSettingsChange('byType.critical.desktop', e.target.checked)}
                  />
                  <label htmlFor="criticalDesktop" className="text-sm text-textPrimary">
                    Desktop
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="criticalEmail"
                    className="mr-2"
                    checked={notificationSettings.byType.critical.email}
                    onChange={(e) => handleNotificationSettingsChange('byType.critical.email', e.target.checked)}
                  />
                  <label htmlFor="criticalEmail" className="text-sm text-textPrimary">
                    Email
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="criticalInApp"
                    className="mr-2"
                    checked={notificationSettings.byType.critical.inApp}
                    onChange={(e) => handleNotificationSettingsChange('byType.critical.inApp', e.target.checked)}
                  />
                  <label htmlFor="criticalInApp" className="text-sm text-textPrimary">
                    In-App
                  </label>
                </div>
              </div>
            </div>
            
            {/* High alerts */}
            <div className="mb-4">
              <h4 className="text-md font-medium mb-2 text-textPrimary">High Alerts</h4>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="highDesktop"
                    className="mr-2"
                    checked={notificationSettings.byType.high.desktop}
                    onChange={(e) => handleNotificationSettingsChange('byType.high.desktop', e.target.checked)}
                  />
                  <label htmlFor="highDesktop" className="text-sm text-textPrimary">
                    Desktop
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="highEmail"
                    className="mr-2"
                    checked={notificationSettings.byType.high.email}
                    onChange={(e) => handleNotificationSettingsChange('byType.high.email', e.target.checked)}
                  />
                  <label htmlFor="highEmail" className="text-sm text-textPrimary">
                    Email
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="highInApp"
                    className="mr-2"
                    checked={notificationSettings.byType.high.inApp}
                    onChange={(e) => handleNotificationSettingsChange('byType.high.inApp', e.target.checked)}
                  />
                  <label htmlFor="highInApp" className="text-sm text-textPrimary">
                    In-App
                  </label>
                </div>
              </div>
            </div>
            
            {/* Medium alerts */}
            <div className="mb-4">
              <h4 className="text-md font-medium mb-2 text-textPrimary">Medium Alerts</h4>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="mediumDesktop"
                    className="mr-2"
                    checked={notificationSettings.byType.medium.desktop}
                    onChange={(e) => handleNotificationSettingsChange('byType.medium.desktop', e.target.checked)}
                  />
                  <label htmlFor="mediumDesktop" className="text-sm text-textPrimary">
                    Desktop
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="mediumEmail"
                    className="mr-2"
                    checked={notificationSettings.byType.medium.email}
                    onChange={(e) => handleNotificationSettingsChange('byType.medium.email', e.target.checked)}
                  />
                  <label htmlFor="mediumEmail" className="text-sm text-textPrimary">
                    Email
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="mediumInApp"
                    className="mr-2"
                    checked={notificationSettings.byType.medium.inApp}
                    onChange={(e) => handleNotificationSettingsChange('byType.medium.inApp', e.target.checked)}
                  />
                  <label htmlFor="mediumInApp" className="text-sm text-textPrimary">
                    In-App
                  </label>
                </div>
              </div>
            </div>
            
            {/* Low alerts */}
            <div>
              <h4 className="text-md font-medium mb-2 text-textPrimary">Low Alerts</h4>
              <div className="flex flex-wrap gap-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="lowDesktop"
                    className="mr-2"
                    checked={notificationSettings.byType.low.desktop}
                    onChange={(e) => handleNotificationSettingsChange('byType.low.desktop', e.target.checked)}
                  />
                  <label htmlFor="lowDesktop" className="text-sm text-textPrimary">
                    Desktop
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="lowEmail"
                    className="mr-2"
                    checked={notificationSettings.byType.low.email}
                    onChange={(e) => handleNotificationSettingsChange('byType.low.email', e.target.checked)}
                  />
                  <label htmlFor="lowEmail" className="text-sm text-textPrimary">
                    Email
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="lowInApp"
                    className="mr-2"
                    checked={notificationSettings.byType.low.inApp}
                    onChange={(e) => handleNotificationSettingsChange('byType.low.inApp', e.target.checked)}
                  />
                  <label htmlFor="lowInApp" className="text-sm text-textPrimary">
                    In-App
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];
  
  return (
    <div
      className={`bg-background border border-divider rounded-lg shadow-lg ${className}`}
      style={style}
      data-testid="preferences-manager"
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-divider">
        <h2 className="text-xl font-semibold text-textPrimary">User Preferences</h2>
      </div>
      
      {/* Content */}
      <div className="p-6">
        <TabPanel
          tabs={tabs}
          defaultTab="ui"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </div>
      
      {/* Footer */}
      <div className="px-6 py-4 border-t border-divider flex justify-between">
        <button
          className="px-4 py-2 bg-error text-errorContrast rounded-md hover:bg-errorDark transition-colors duration-200"
          onClick={handleReset}
          data-testid="reset-button"
        >
          Reset to Defaults
        </button>
        
        <div className="flex space-x-2">
          <button
            className="px-4 py-2 bg-surface text-textPrimary border border-divider rounded-md hover:bg-actionHover transition-colors duration-200"
            onClick={handleCancel}
            data-testid="cancel-button"
          >
            Cancel
          </button>
          
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={handleSave}
            data-testid="save-button"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

PreferencesManager.propTypes = {
  onClose: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default PreferencesManager;
