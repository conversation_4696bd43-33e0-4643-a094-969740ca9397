/**
 * Help Styles
 * 
 * Styles for help components.
 */

/* Contextual Help */
.contextual-help {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.contextual-help__icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-left: 4px;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: color 0.2s ease;
}

.contextual-help__icon:hover {
  color: var(--color-primary);
}

.contextual-help__tooltip {
  max-width: 300px;
}

.contextual-help__content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contextual-help__title {
  font-weight: 600;
  font-size: 14px;
  color: var(--color-text-primary);
}

.contextual-help__description {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.contextual-help__footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
}

.contextual-help__learn-more {
  font-size: 12px;
  color: var(--color-primary);
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.contextual-help__learn-more:hover {
  color: var(--color-primary-dark);
}

/* Help Button */
.help-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.help-button--icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  color: var(--color-text-secondary);
}

.help-button--icon:hover {
  background-color: var(--color-hover);
  color: var(--color-primary);
}

.help-button--text {
  padding: 6px 12px;
  border-radius: 4px;
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  gap: 8px;
}

.help-button--text:hover {
  background-color: var(--color-hover);
}

.help-button--fab {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-primary);
  border: none;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.help-button--fab:hover {
  background-color: var(--color-primary-dark);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.help-button--small {
  font-size: 12px;
}

.help-button--small.help-button--icon {
  width: 24px;
  height: 24px;
}

.help-button--small.help-button--fab {
  width: 40px;
  height: 40px;
}

.help-button--large {
  font-size: 16px;
}

.help-button--large.help-button--icon {
  width: 40px;
  height: 40px;
}

.help-button--large.help-button--fab {
  width: 56px;
  height: 56px;
}

.help-button__label {
  font-size: 14px;
}

.help-button__badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-error);
  border: 2px solid var(--color-background);
  transform: translate(25%, -25%);
}

/* Help Panel */
.help-panel {
  position: fixed;
  top: 0;
  width: 400px;
  max-width: 90vw;
  height: 100vh;
  background-color: var(--color-background);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  z-index: 1050;
  display: flex;
  flex-direction: column;
  transition: transform 0.3s ease;
}

.help-panel--right {
  right: 0;
  border-left: 1px solid var(--color-border);
}

.help-panel--left {
  left: 0;
  border-right: 1px solid var(--color-border);
}

.help-panel__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-surface);
}

.help-panel__title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.help-panel__actions {
  display: flex;
  gap: 8px;
}

.help-panel__action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.help-panel__action:hover {
  background-color: var(--color-hover);
  color: var(--color-text-primary);
}

.help-panel__content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.help-panel__search-form {
  display: flex;
  align-items: center;
  width: 100%;
}

.help-panel__search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 14px;
}

.help-panel__search-submit,
.help-panel__search-cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
}

.help-panel__search-results {
  list-style: none;
  padding: 0;
  margin: 16px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.help-panel__search-result {
  padding: 12px;
  border-radius: 4px;
  background-color: var(--color-surface);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.help-panel__search-result:hover {
  background-color: var(--color-hover);
}

.help-panel__search-result-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--color-text-primary);
}

.help-panel__search-result-description {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.help-panel__search-empty {
  padding: 24px;
  text-align: center;
  color: var(--color-text-secondary);
}

.help-panel__toc {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border);
}

.help-panel__toc-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--color-text-primary);
}

.help-panel__toc-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.help-panel__toc-item {
  margin-bottom: 4px;
}

.help-panel__toc-item--level-1 {
  margin-left: 0;
}

.help-panel__toc-item--level-2 {
  margin-left: 12px;
}

.help-panel__toc-item--level-3 {
  margin-left: 24px;
}

.help-panel__toc-item--level-4,
.help-panel__toc-item--level-5,
.help-panel__toc-item--level-6 {
  margin-left: 36px;
}

.help-panel__toc-link {
  color: var(--color-text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.help-panel__toc-link:hover {
  color: var(--color-primary);
}

.help-panel__main {
  margin-bottom: 16px;
}

.help-panel__markdown {
  font-size: 14px;
  line-height: 1.6;
  color: var(--color-text-primary);
}

.help-panel__markdown h1 {
  font-size: 24px;
  margin-top: 0;
}

.help-panel__markdown h2 {
  font-size: 20px;
}

.help-panel__markdown h3 {
  font-size: 18px;
}

.help-panel__markdown h4,
.help-panel__markdown h5,
.help-panel__markdown h6 {
  font-size: 16px;
}

.help-panel__markdown code {
  background-color: var(--color-surface);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: var(--font-family-code);
  font-size: 12px;
}

.help-panel__markdown pre {
  background-color: var(--color-surface);
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
}

.help-panel__markdown pre code {
  background-color: transparent;
  padding: 0;
}

.help-panel__related {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.help-panel__related-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--color-text-primary);
}

.help-panel__related-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.help-panel__related-item {
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--color-text-secondary);
}

.help-panel__related-item:hover {
  background-color: var(--color-hover);
  color: var(--color-text-primary);
}

.help-panel__empty {
  padding: 24px;
  text-align: center;
  color: var(--color-text-secondary);
}

/* Help Tour */
.help-tour {
  /* Inherits styles from OnboardingTour */
}
