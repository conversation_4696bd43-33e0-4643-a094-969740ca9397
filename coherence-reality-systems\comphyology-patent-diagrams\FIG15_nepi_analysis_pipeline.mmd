# NEPI Analysis Pipeline
# File: nepi_analysis_pipeline.mmd
# Description: Illustrates the Non-Empirical Pattern Identification process flow
# Created: 2025-07-06

%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph NEPI_Pipeline["NEPI Analysis Pipeline"]
        A[Raw Input Data] --> B["Data Pre-Coherence\n(Normalization/Filtering)"]
        B --> C[Pattern Detection Layer]
        C --> D["Non-Empirical Pattern\nIdentification (NEPI) Engine"]
        D --> E[Comphyological\nPattern Database]
        E --> F["Coherence Anomaly\nDetection"]
        F -->|Anomalies/Opportunities| G["Feedback Loop\nfor ∂Ψ=0 Optimization"]
        G --> H["Coherent Output /\nAction Recommendation"]
    end

    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:rectangle
    classDef decision fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:diamond
    classDef database fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:cylinder
    classDef output fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:box3d
    
    class A process
    class B,C,D,F,G process
    class E database
    class H output
