/**
 * CHAEONIX PROFIT ANALYTICS ENGINE
 * Comprehensive profit tracking across timeframes and market categories
 * Day-to-day, week-by-week, month-by-month, YTD analytics
 * Breakdown by Stocks, Crypto, and Forex markets
 */

// MARKET CATEGORIES
const MARKET_CATEGORIES = {
  STOCKS: {
    name: '<PERSON><PERSON>',
    color: '#3B82F6', // Blue
    symbols: ['AAPL', 'TSLA', 'NVDA', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NFLX']
  },
  CRYPTO: {
    name: 'Crypto',
    color: '#F59E0B', // Orange
    symbols: ['BTCUSD', 'ETHUSD', 'ADAUSD', 'SOLUSD', 'DOTUSD', 'LINKUSD', 'AVAXUSD']
  },
  FOREX: {
    name: 'Forex',
    color: '#10B981', // Green
    symbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCHF', 'USDCAD', 'NZDUSD']
  }
};

class ProfitTracker {
  constructor() {
    this.trades = [];
    this.daily_summaries = new Map();
    this.weekly_summaries = new Map();
    this.monthly_summaries = new Map();
    this.yearly_summaries = new Map();
    this.category_performance = new Map();
    this.last_update = new Date();
    
    // RESET: Start with empty data (no sample data)
    console.log('💰 CHAEONIX Profit Tracker initialized with EMPTY data - ready for real trades only');
    // this.initializeSampleData(); // DISABLED - no more sample data
  }

  // INITIALIZE SAMPLE DATA
  initializeSampleData() {
    const now = new Date();
    const daysBack = 30;
    
    for (let i = daysBack; i >= 0; i--) {
      const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
      const tradesPerDay = 8 + Math.floor(Math.random() * 12); // 8-20 trades per day
      
      for (let j = 0; j < tradesPerDay; j++) {
        const categories = Object.keys(MARKET_CATEGORIES);
        const category = categories[Math.floor(Math.random() * categories.length)];
        const symbols = MARKET_CATEGORIES[category].symbols;
        const symbol = symbols[Math.floor(Math.random() * symbols.length)];
        
        // Generate realistic profit/loss with 82% win rate
        const isWin = Math.random() < 0.82;
        let profit;
        
        if (isWin) {
          profit = 15 + Math.random() * 85; // $15-100 profit
        } else {
          profit = -(2 + Math.random() * 8); // $2-10 loss (divine protection)
        }
        
        const trade = {
          id: `TRADE_${Date.now()}_${j}`,
          timestamp: new Date(date.getTime() + (j * 60 * 60 * 1000)), // Spread throughout day
          symbol: symbol,
          category: category,
          action: Math.random() > 0.5 ? 'BUY' : 'SELL',
          quantity: 0.01 + Math.random() * 0.05, // 0.01-0.06 lots
          profit: profit,
          commission: -0.50,
          net_profit: profit - 0.50
        };
        
        this.trades.push(trade);
      }
    }
    
    // Calculate all summaries
    this.calculateAllSummaries();
  }

  // ADD NEW TRADE
  addTrade(trade) {
    this.trades.push({
      ...trade,
      id: trade.id || `TRADE_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: trade.timestamp || new Date(),
      category: this.categorizeSymbol(trade.symbol),
      net_profit: trade.profit - (trade.commission || 0)
    });
    
    this.calculateAllSummaries();
  }

  // CATEGORIZE SYMBOL
  categorizeSymbol(symbol) {
    for (const [category, data] of Object.entries(MARKET_CATEGORIES)) {
      if (data.symbols.includes(symbol)) {
        return category;
      }
    }
    
    // Auto-categorize based on symbol pattern
    if (symbol.includes('USD') && symbol.length === 6) return 'FOREX';
    if (symbol.includes('USD') && symbol.length <= 7) return 'CRYPTO';
    return 'STOCKS';
  }

  // CALCULATE ALL SUMMARIES
  calculateAllSummaries() {
    this.calculateDailySummaries();
    this.calculateWeeklySummaries();
    this.calculateMonthlySummaries();
    this.calculateYearlySummaries();
    this.calculateCategoryPerformance();
  }

  // CALCULATE DAILY SUMMARIES
  calculateDailySummaries() {
    this.daily_summaries.clear();
    
    const dailyGroups = this.groupTradesByPeriod('day');
    
    for (const [dateKey, trades] of dailyGroups.entries()) {
      const summary = this.calculatePeriodSummary(trades);
      summary.date = dateKey;
      summary.period_type = 'daily';
      this.daily_summaries.set(dateKey, summary);
    }
  }

  // CALCULATE WEEKLY SUMMARIES
  calculateWeeklySummaries() {
    this.weekly_summaries.clear();
    
    const weeklyGroups = this.groupTradesByPeriod('week');
    
    for (const [weekKey, trades] of weeklyGroups.entries()) {
      const summary = this.calculatePeriodSummary(trades);
      summary.week = weekKey;
      summary.period_type = 'weekly';
      this.weekly_summaries.set(weekKey, summary);
    }
  }

  // CALCULATE MONTHLY SUMMARIES
  calculateMonthlySummaries() {
    this.monthly_summaries.clear();
    
    const monthlyGroups = this.groupTradesByPeriod('month');
    
    for (const [monthKey, trades] of monthlyGroups.entries()) {
      const summary = this.calculatePeriodSummary(trades);
      summary.month = monthKey;
      summary.period_type = 'monthly';
      this.monthly_summaries.set(monthKey, summary);
    }
  }

  // CALCULATE YEARLY SUMMARIES
  calculateYearlySummaries() {
    this.yearly_summaries.clear();
    
    const yearlyGroups = this.groupTradesByPeriod('year');
    
    for (const [yearKey, trades] of yearlyGroups.entries()) {
      const summary = this.calculatePeriodSummary(trades);
      summary.year = yearKey;
      summary.period_type = 'yearly';
      this.yearly_summaries.set(yearKey, summary);
    }
  }

  // GROUP TRADES BY PERIOD
  groupTradesByPeriod(period) {
    const groups = new Map();
    
    for (const trade of this.trades) {
      const date = new Date(trade.timestamp);
      let key;
      
      switch (period) {
        case 'day':
          key = date.toISOString().split('T')[0]; // YYYY-MM-DD
          break;
        case 'week':
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay()); // Start of week (Sunday)
          key = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'year':
          key = date.getFullYear().toString();
          break;
      }
      
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key).push(trade);
    }
    
    return groups;
  }

  // CALCULATE PERIOD SUMMARY
  calculatePeriodSummary(trades) {
    const total_trades = trades.length;
    const winning_trades = trades.filter(t => t.net_profit > 0);
    const losing_trades = trades.filter(t => t.net_profit < 0);
    
    const total_profit = trades.reduce((sum, t) => sum + t.net_profit, 0);
    const gross_profit = winning_trades.reduce((sum, t) => sum + t.net_profit, 0);
    const gross_loss = losing_trades.reduce((sum, t) => sum + t.net_profit, 0);
    
    const win_rate = total_trades > 0 ? (winning_trades.length / total_trades) * 100 : 0;
    const avg_win = winning_trades.length > 0 ? gross_profit / winning_trades.length : 0;
    const avg_loss = losing_trades.length > 0 ? gross_loss / losing_trades.length : 0;
    const profit_factor = gross_loss !== 0 ? Math.abs(gross_profit / gross_loss) : gross_profit > 0 ? 999 : 0;
    
    // Category breakdown
    const category_breakdown = {};
    for (const category of Object.keys(MARKET_CATEGORIES)) {
      const categoryTrades = trades.filter(t => t.category === category);
      category_breakdown[category] = {
        trades: categoryTrades.length,
        profit: categoryTrades.reduce((sum, t) => sum + t.net_profit, 0),
        win_rate: categoryTrades.length > 0 ? 
          (categoryTrades.filter(t => t.net_profit > 0).length / categoryTrades.length) * 100 : 0
      };
    }
    
    return {
      total_trades,
      winning_trades: winning_trades.length,
      losing_trades: losing_trades.length,
      total_profit,
      gross_profit,
      gross_loss,
      win_rate,
      avg_win,
      avg_loss,
      profit_factor,
      category_breakdown,
      best_trade: trades.reduce((best, trade) => 
        trade.net_profit > (best?.net_profit || -Infinity) ? trade : best, null),
      worst_trade: trades.reduce((worst, trade) => 
        trade.net_profit < (worst?.net_profit || Infinity) ? trade : worst, null)
    };
  }

  // CALCULATE CATEGORY PERFORMANCE
  calculateCategoryPerformance() {
    this.category_performance.clear();
    
    for (const category of Object.keys(MARKET_CATEGORIES)) {
      const categoryTrades = this.trades.filter(t => t.category === category);
      const summary = this.calculatePeriodSummary(categoryTrades);
      summary.category = category;
      summary.color = MARKET_CATEGORIES[category].color;
      this.category_performance.set(category, summary);
    }
  }

  // GET ACCUMULATIVE PROFITS
  getAccumulativeProfits() {
    const daily = Array.from(this.daily_summaries.values())
      .sort((a, b) => new Date(a.date) - new Date(b.date));
    
    const weekly = Array.from(this.weekly_summaries.values())
      .sort((a, b) => new Date(a.week) - new Date(b.week));
    
    const monthly = Array.from(this.monthly_summaries.values())
      .sort((a, b) => a.month.localeCompare(b.month));
    
    const yearly = Array.from(this.yearly_summaries.values())
      .sort((a, b) => a.year.localeCompare(b.year));
    
    // Calculate cumulative totals
    let cumulative = 0;
    daily.forEach(day => {
      cumulative += day.total_profit;
      day.cumulative_profit = cumulative;
    });
    
    cumulative = 0;
    weekly.forEach(week => {
      cumulative += week.total_profit;
      week.cumulative_profit = cumulative;
    });
    
    cumulative = 0;
    monthly.forEach(month => {
      cumulative += month.total_profit;
      month.cumulative_profit = cumulative;
    });
    
    cumulative = 0;
    yearly.forEach(year => {
      cumulative += year.total_profit;
      year.cumulative_profit = cumulative;
    });
    
    return { daily, weekly, monthly, yearly };
  }

  // GET CATEGORY BREAKDOWN
  getCategoryBreakdown() {
    return Array.from(this.category_performance.values());
  }

  // GET CURRENT PERIOD STATS
  getCurrentPeriodStats() {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const thisWeek = new Date(now);
    thisWeek.setDate(now.getDate() - now.getDay());
    const thisMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    const thisYear = now.getFullYear().toString();
    
    return {
      today: this.daily_summaries.get(today) || this.getEmptySummary(),
      this_week: this.weekly_summaries.get(thisWeek.toISOString().split('T')[0]) || this.getEmptySummary(),
      this_month: this.monthly_summaries.get(thisMonth) || this.getEmptySummary(),
      this_year: this.yearly_summaries.get(thisYear) || this.getEmptySummary(),
      ytd_total: Array.from(this.yearly_summaries.values())
        .reduce((sum, year) => sum + year.total_profit, 0)
    };
  }

  // GET EMPTY SUMMARY
  getEmptySummary() {
    return {
      total_trades: 0,
      total_profit: 0,
      win_rate: 0,
      category_breakdown: {
        STOCKS: { trades: 0, profit: 0, win_rate: 0 },
        CRYPTO: { trades: 0, profit: 0, win_rate: 0 },
        FOREX: { trades: 0, profit: 0, win_rate: 0 }
      }
    };
  }
}

// Export singleton instance
const profitTracker = new ProfitTracker();

export default function handler(req, res) {
  if (req.method === 'GET') {
    const { timeframe, category } = req.query;
    
    if (timeframe === 'accumulative') {
      const accumulative = profitTracker.getAccumulativeProfits();
      res.status(200).json({
        success: true,
        data_type: 'accumulative_profits',
        accumulative_profits: accumulative,
        timestamp: new Date().toISOString()
      });
      
    } else if (category === 'breakdown') {
      const categories = profitTracker.getCategoryBreakdown();
      res.status(200).json({
        success: true,
        data_type: 'category_breakdown',
        category_performance: categories,
        timestamp: new Date().toISOString()
      });
      
    } else {
      const current_stats = profitTracker.getCurrentPeriodStats();
      const accumulative = profitTracker.getAccumulativeProfits();
      const categories = profitTracker.getCategoryBreakdown();
      
      res.status(200).json({
        success: true,
        profit_analytics: 'CHAEONIX Comprehensive Profit Tracking',
        current_period_stats: current_stats,
        accumulative_profits: accumulative,
        category_performance: categories,
        total_trades: profitTracker.trades.length,
        timestamp: new Date().toISOString()
      });
    }

  } else if (req.method === 'POST') {
    const { action, trade_data } = req.body;

    if (action === 'ADD_TRADE') {
      profitTracker.addTrade(trade_data);
      res.status(200).json({
        success: true,
        message: 'Trade added to profit tracking',
        total_trades: profitTracker.trades.length
      });

    } else if (action === 'RESET_FOR_LIVE') {
      // Clear sample data and start fresh for live trading
      profitTracker.trades = [];
      profitTracker.daily_summaries.clear();
      profitTracker.weekly_summaries.clear();
      profitTracker.monthly_summaries.clear();
      profitTracker.yearly_summaries.clear();
      profitTracker.category_performance.clear();

      console.log('🔄 Profit tracker reset for live trading');

      res.status(200).json({
        success: true,
        message: 'Profit tracker reset for live trading',
        total_trades: 0
      });

    } else if (action === 'RESET_METRICS') {
      // Reset all metrics to $0
      profitTracker.trades = [];
      profitTracker.daily_summaries.clear();
      profitTracker.weekly_summaries.clear();
      profitTracker.monthly_summaries.clear();
      profitTracker.yearly_summaries.clear();
      profitTracker.category_performance.clear();
      profitTracker.last_update = new Date();

      console.log('💰 All CHAEONIX metrics reset to $0');

      res.status(200).json({
        success: true,
        message: 'All CHAEONIX metrics reset to $0',
        total_trades: 0,
        total_profit: 0,
        reset_timestamp: new Date().toISOString()
      });

    } else {
      res.status(400).json({ error: 'Invalid action' });
    }
    
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
