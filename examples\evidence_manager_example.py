"""
Example of using the enhanced Evidence Manager.

This example demonstrates how to use the enhanced Evidence Manager with
versioning, encryption, access control, audit logging, relationships, and workflows.
"""

import os
import sys
import json
import uuid
import logging
import datetime

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.evidence_manager import (
    EvidenceManager,
    EvidenceRelationshipType,
    EvidenceWorkflowState
)
from src.ucecs.core.storage_manager import EncryptionType, AccessLevel

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the Evidence Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_evidence_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create directories for the Evidence Manager
    evidence_dir = os.path.join(temp_dir, 'evidence_data')
    relationships_dir = os.path.join(temp_dir, 'evidence_relationships')
    workflow_dir = os.path.join(temp_dir, 'evidence_workflows')
    audit_log_dir = os.path.join(temp_dir, 'audit_logs')
    versions_dir = os.path.join(temp_dir, 'evidence_versions')
    access_control_dir = os.path.join(temp_dir, 'access_control')
    encryption_keys_dir = os.path.join(temp_dir, 'encryption_keys')
    
    # Create an Evidence Manager
    manager = EvidenceManager(
        evidence_dir=evidence_dir,
        relationships_dir=relationships_dir,
        workflow_dir=workflow_dir,
        audit_log_dir=audit_log_dir,
        versions_dir=versions_dir,
        access_control_dir=access_control_dir,
        encryption_keys_dir=encryption_keys_dir,
        enable_versioning=True,
        enable_encryption=True,
        enable_access_control=True,
        enable_audit_logging=True,
        enable_workflows=True,
        enable_relationships=True,
        current_user_id="admin"
    )
    
    try:
        # Create a sample evidence item
        evidence_id = str(uuid.uuid4())
        evidence = {
            'id': evidence_id,
            'type': 'document',
            'source': 'user_upload',
            'data': {
                'title': 'Sample Document',
                'content': 'This is a sample document for testing the Evidence Manager.',
                'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            },
            'metadata': {
                'tags': ['sample', 'document', 'test']
            }
        }
        
        # Register the evidence
        logger.info("Registering evidence...")
        manager.register_evidence(evidence)
        
        # Create a workflow for the evidence
        logger.info("Creating workflow...")
        workflow = manager.create_evidence_workflow(
            evidence_id=evidence_id,
            initial_state=EvidenceWorkflowState.DRAFT,
            user_id="admin",
            comment="Initial draft"
        )
        logger.info(f"Workflow created: {json.dumps(workflow, indent=2)}")
        
        # Validate the evidence
        logger.info("Validating evidence...")
        validated_evidence = manager.validate_evidence(
            evidence=evidence,
            validator_id="file_exists",
            user_id="admin"
        )
        logger.info(f"Evidence validated: {json.dumps(validated_evidence.get('validation_results', {}), indent=2)}")
        
        # Store the evidence
        logger.info("Storing evidence...")
        stored_evidence = manager.store_evidence(
            evidence=evidence,
            storage_id="file_system",
            user_id="admin",
            encrypt=True,
            encryption_type=EncryptionType.AES_256,
            create_version=True,
            version_comment="Initial version"
        )
        logger.info(f"Evidence stored at: {stored_evidence.get('storage_location')}")
        
        # Update the workflow state
        logger.info("Updating workflow state...")
        workflow = manager.update_evidence_workflow_state(
            evidence_id=evidence_id,
            new_state=EvidenceWorkflowState.PENDING_REVIEW,
            user_id="admin",
            comment="Ready for review"
        )
        logger.info(f"Workflow updated: {json.dumps(workflow, indent=2)}")
        
        # Create another evidence item
        related_evidence_id = str(uuid.uuid4())
        related_evidence = {
            'id': related_evidence_id,
            'type': 'document',
            'source': 'user_upload',
            'data': {
                'title': 'Related Document',
                'content': 'This is a related document for testing relationships.',
                'created_at': datetime.datetime.now(datetime.timezone.utc).isoformat()
            },
            'metadata': {
                'tags': ['related', 'document', 'test']
            }
        }
        
        # Register the related evidence
        logger.info("Registering related evidence...")
        manager.register_evidence(related_evidence)
        
        # Create a workflow for the related evidence
        logger.info("Creating workflow for related evidence...")
        related_workflow = manager.create_evidence_workflow(
            evidence_id=related_evidence_id,
            initial_state=EvidenceWorkflowState.DRAFT,
            user_id="admin",
            comment="Initial draft"
        )
        
        # Store the related evidence
        logger.info("Storing related evidence...")
        manager.store_evidence(
            evidence=related_evidence,
            storage_id="file_system",
            user_id="admin",
            encrypt=True,
            create_version=True,
            version_comment="Initial version"
        )
        
        # Add a relationship between the evidence items
        logger.info("Adding relationship...")
        relationship = manager.add_evidence_relationship(
            evidence_id=evidence_id,
            related_evidence_id=related_evidence_id,
            relationship_type=EvidenceRelationshipType.PARENT,
            user_id="admin",
            description="Parent-child relationship"
        )
        logger.info(f"Relationship added: {json.dumps(relationship, indent=2)}")
        
        # Get the relationships
        logger.info("Getting relationships...")
        relationships = manager.get_evidence_relationships(evidence_id)
        logger.info(f"Relationships: {json.dumps(relationships, indent=2)}")
        
        # Get related evidence
        logger.info("Getting related evidence...")
        related = manager.get_related_evidence(evidence_id)
        logger.info(f"Related evidence: {json.dumps(related, indent=2)}")
        
        # Create a new user
        user_id = "user1"
        
        # Grant access to the user
        logger.info(f"Granting access to user {user_id}...")
        manager.storage_manager.grant_access(
            evidence_id=evidence_id,
            user_id=user_id,
            access_level=AccessLevel.READ,
            granter_id="admin"
        )
        
        # Get access control information
        logger.info("Getting access control information...")
        access_control = manager.storage_manager.get_access_control(
            evidence_id=evidence_id,
            user_id="admin"
        )
        logger.info(f"Access control: {json.dumps(access_control, indent=2)}")
        
        # Retrieve the evidence as the user
        logger.info(f"Retrieving evidence as user {user_id}...")
        user_retrieved_evidence = manager.retrieve_evidence(
            evidence_id=evidence_id,
            storage_id="file_system",
            user_id=user_id,
            decrypt=True
        )
        logger.info(f"Evidence retrieved by user: {json.dumps(user_retrieved_evidence.get('data', {}), indent=2)}")
        
        # Update the evidence
        logger.info("Updating evidence...")
        updated_evidence = evidence.copy()
        updated_evidence['data']['content'] = 'This is an updated sample document.'
        updated_evidence['data']['updated_at'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
        
        # Store the updated evidence
        logger.info("Storing updated evidence...")
        manager.store_evidence(
            evidence=updated_evidence,
            storage_id="file_system",
            user_id="admin",
            encrypt=True,
            create_version=True,
            version_comment="Updated content"
        )
        
        # Get the evidence versions
        logger.info("Getting evidence versions...")
        versions = manager.storage_manager.get_evidence_versions(
            evidence_id=evidence_id,
            user_id="admin"
        )
        logger.info(f"Evidence versions: {json.dumps(versions, indent=2)}")
        
        # Approve the evidence
        logger.info("Approving evidence...")
        workflow = manager.update_evidence_workflow_state(
            evidence_id=evidence_id,
            new_state=EvidenceWorkflowState.APPROVED,
            user_id="admin",
            comment="Evidence approved"
        )
        logger.info(f"Workflow updated: {json.dumps(workflow, indent=2)}")
        
        # Get the audit logs
        logger.info("Getting audit logs...")
        audit_logs = manager.storage_manager.get_audit_logs(
            evidence_id=evidence_id,
            user_id="admin"
        )
        logger.info(f"Audit logs: {json.dumps(audit_logs, indent=2)}")
        
        # Delete the evidence
        logger.info("Deleting evidence...")
        manager.delete_evidence(
            evidence_id=evidence_id,
            storage_id="file_system",
            user_id="admin",
            delete_versions=True,
            delete_audit_logs=False,
            permanent=False
        )
        logger.info("Evidence deleted")
        
        # Check if the relationship was removed
        logger.info("Checking if relationship was removed...")
        try:
            relationships = manager.get_evidence_relationships(related_evidence_id)
            logger.info(f"Relationships after deletion: {json.dumps(relationships, indent=2)}")
        except ValueError as e:
            logger.info(f"Error: {e}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
