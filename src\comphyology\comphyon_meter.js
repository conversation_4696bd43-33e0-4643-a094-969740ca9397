/**
 * Comphyon Meter
 * 
 * This module implements the Comphyon Meter, which measures emergent intelligence (Cph)
 * using the formula:
 * 
 * Cph = ((dE_CSDE × dE_CSFE) × log(E_CSME))/166000
 * 
 * Where:
 * - E_CSDE = A1×D (Decision Energy)
 * - E_CSFE = A2×P (Financial Energy)
 * - E_CSME = T×I (Medical Energy)
 * 
 * The Comphyon (Cph) is a unit of measure for emergent intelligence
 * (1 Cph = 3,142 predictions/sec) that serves as an early warning system
 * and control mechanism for monitoring AI intelligence.
 */

const EventEmitter = require('events');

/**
 * Comphyon Meter class
 */
class ComphyonMeter extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      // Calibration factors
      calibrationFactor: 1.0,
      decisionFactor: 0.3, // A1
      financialFactor: 0.6, // A2
      medicalFactor: 0.9, // Combined T×I factor
      
      // Domain weights
      domainWeights: {
        cyber: 0.3,
        financial: 0.6,
        medical: 0.9
      },
      
      // Thresholds
      warningThreshold: 2.0,
      criticalThreshold: 3.0,
      maxSafeComphyon: 3.142,
      
      // History
      keepHistory: true,
      historyLength: 100,
      
      // Logging
      logMeasurements: false,
      
      ...options
    };
    
    // Initialize metrics
    this.metrics = {
      measurements: 0,
      totalComphyon: 0,
      averageComphyon: 0,
      maxComphyon: 0,
      minComphyon: Number.MAX_SAFE_INTEGER,
      warnings: 0,
      criticalAlerts: 0,
      domainMeasurements: {
        cyber: 0,
        financial: 0,
        medical: 0
      }
    };
    
    // Initialize history
    this.history = [];
    
    // Initialize domain energies
    this.domainEnergies = {
      cyber: { current: 0, previous: 0 },
      financial: { current: 0, previous: 0 },
      medical: { current: 0, previous: 0 }
    };
    
    if (this.options.logMeasurements) {
      console.log('Comphyon Meter initialized with options:', this.options);
    }
  }
  
  /**
   * Measure Comphyon value
   * @param {Object|number} state - State to measure
   * @param {Object} context - Measurement context
   * @returns {number} - Comphyon value
   */
  measure(state, context = {}) {
    // Update metrics
    this.metrics.measurements++;
    
    // Get domain from context or default to 'cyber'
    const domain = context.domain || 'cyber';
    this.metrics.domainMeasurements[domain]++;
    
    // Calculate domain energies
    this._calculateDomainEnergies(state, domain);
    
    // Calculate Comphyon value
    const comphyonValue = this._calculateComphyon();
    
    // Update metrics
    this.metrics.totalComphyon += comphyonValue;
    this.metrics.averageComphyon = this.metrics.totalComphyon / this.metrics.measurements;
    this.metrics.maxComphyon = Math.max(this.metrics.maxComphyon, comphyonValue);
    this.metrics.minComphyon = Math.min(this.metrics.minComphyon, comphyonValue);
    
    // Check thresholds
    if (comphyonValue > this.options.criticalThreshold) {
      this.metrics.criticalAlerts++;
      this.emit('critical-alert', { comphyonValue, state, context });
      
      if (this.options.logMeasurements) {
        console.error(`CRITICAL: Comphyon value ${comphyonValue} exceeds critical threshold ${this.options.criticalThreshold}`);
      }
    } else if (comphyonValue > this.options.warningThreshold) {
      this.metrics.warnings++;
      this.emit('warning', { comphyonValue, state, context });
      
      if (this.options.logMeasurements) {
        console.warn(`WARNING: Comphyon value ${comphyonValue} exceeds warning threshold ${this.options.warningThreshold}`);
      }
    }
    
    // Add to history if enabled
    if (this.options.keepHistory) {
      this.history.push({
        timestamp: Date.now(),
        comphyonValue,
        domain,
        context
      });
      
      // Trim history if needed
      if (this.history.length > this.options.historyLength) {
        this.history.shift();
      }
    }
    
    // Emit measurement event
    this.emit('measurement', {
      comphyonValue,
      state,
      domain,
      context,
      timestamp: Date.now()
    });
    
    // Log measurement if enabled
    if (this.options.logMeasurements) {
      console.log(`Comphyon measurement: ${comphyonValue} (domain: ${domain})`);
    }
    
    return comphyonValue;
  }
  
  /**
   * Calculate domain energies
   * @param {Object|number} state - State to calculate energies for
   * @param {string} domain - Domain
   * @private
   */
  _calculateDomainEnergies(state, domain) {
    // Store previous energy
    this.domainEnergies[domain].previous = this.domainEnergies[domain].current;
    
    // Calculate current energy based on state
    let energy = 0;
    
    if (typeof state === 'number') {
      energy = this._calculateEnergyFromNumber(state, domain);
    } else if (typeof state === 'object' && state !== null) {
      energy = this._calculateEnergyFromObject(state, domain);
    }
    
    // Store current energy
    this.domainEnergies[domain].current = energy;
  }
  
  /**
   * Calculate energy from a numeric state
   * @param {number} value - Numeric state
   * @param {string} domain - Domain
   * @returns {number} - Energy
   * @private
   */
  _calculateEnergyFromNumber(value, domain) {
    // Apply domain-specific factor
    const factor = this.options.domainWeights[domain] || 1.0;
    
    // Calculate energy based on domain
    switch (domain) {
      case 'cyber':
        // E_CSDE = A1×D
        return this.options.decisionFactor * value * factor;
        
      case 'financial':
        // E_CSFE = A2×P
        return this.options.financialFactor * value * factor;
        
      case 'medical':
        // E_CSME = T×I
        return this.options.medicalFactor * value * factor;
        
      default:
        return value * factor;
    }
  }
  
  /**
   * Calculate energy from an object state
   * @param {Object} obj - Object state
   * @param {string} domain - Domain
   * @returns {number} - Energy
   * @private
   */
  _calculateEnergyFromObject(obj, domain) {
    // Extract numeric values
    const values = this._extractNumericValues(obj);
    
    // Calculate average value
    const avgValue = values.length > 0 ? 
      values.reduce((sum, val) => sum + val, 0) / values.length : 0;
    
    // Calculate energy using the average value
    return this._calculateEnergyFromNumber(avgValue, domain);
  }
  
  /**
   * Extract numeric values from an object
   * @param {Object} obj - Object to extract values from
   * @returns {Array<number>} - Array of numeric values
   * @private
   */
  _extractNumericValues(obj) {
    const values = [];
    
    const extract = (o) => {
      if (typeof o === 'number') {
        values.push(o);
      } else if (typeof o === 'object' && o !== null) {
        for (const key in o) {
          extract(o[key]);
        }
      }
    };
    
    extract(obj);
    return values;
  }
  
  /**
   * Calculate Comphyon value
   * @returns {number} - Comphyon value
   * @private
   */
  _calculateComphyon() {
    // Calculate energy differentials
    const dE_CSDE = Math.abs(this.domainEnergies.cyber.current - this.domainEnergies.cyber.previous);
    const dE_CSFE = Math.abs(this.domainEnergies.financial.current - this.domainEnergies.financial.previous);
    
    // Get medical energy
    const E_CSME = Math.max(this.domainEnergies.medical.current, 0.001); // Avoid log(0)
    
    // Calculate Comphyon using the formula:
    // Cph = ((dE_CSDE × dE_CSFE) × log(E_CSME))/166000
    const comphyonRaw = ((dE_CSDE * dE_CSFE) * Math.log(E_CSME)) / 166000;
    
    // Apply calibration factor
    const comphyonCalibrated = comphyonRaw * this.options.calibrationFactor;
    
    // Ensure Comphyon value is within safe bounds
    return Math.min(comphyonCalibrated, this.options.maxSafeComphyon);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get history
   * @returns {Array} - Measurement history
   */
  getHistory() {
    return [...this.history];
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      measurements: 0,
      totalComphyon: 0,
      averageComphyon: 0,
      maxComphyon: 0,
      minComphyon: Number.MAX_SAFE_INTEGER,
      warnings: 0,
      criticalAlerts: 0,
      domainMeasurements: {
        cyber: 0,
        financial: 0,
        medical: 0
      }
    };
    
    this.history = [];
    
    this.domainEnergies = {
      cyber: { current: 0, previous: 0 },
      financial: { current: 0, previous: 0 },
      medical: { current: 0, previous: 0 }
    };
  }
}

module.exports = ComphyonMeter;
