# NovaConnect + CSDE Integration: Production Deployment Guide

This guide provides comprehensive instructions for deploying the NovaConnect + CSDE integration in a production environment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Overview](#architecture-overview)
3. [Deployment Options](#deployment-options)
   - [Docker Compose](#docker-compose)
   - [Kubernetes](#kubernetes)
4. [Configuration](#configuration)
5. [Monitoring and Observability](#monitoring-and-observability)
6. [Security Considerations](#security-considerations)
7. [Scaling](#scaling)
8. [Backup and Recovery](#backup-and-recovery)
9. [Troubleshooting](#troubleshooting)
10. [Maintenance](#maintenance)

## Prerequisites

Before deploying the NovaConnect + CSDE integration, ensure you have:

- Access to the NovaConnect and CSDE codebases
- Docker and Docker Compose (for Docker deployment)
- Kubernetes cluster (for Kubernetes deployment)
- Access to container registries
- SSL certificates for secure communication
- Monitoring infrastructure (Prometheus, Grafana)

## Architecture Overview

The NovaConnect + CSDE integration consists of the following components:

- **NovaConnect API**: The main API service that handles requests from clients
- **CSDE API**: The Cyber-Safety Domain Engine API that implements the UUFT equation
- **Databases**: MongoDB for NovaConnect, PostgreSQL for CSDE
- **Redis**: For caching and message queuing
- **Prometheus**: For metrics collection
- **Grafana**: For metrics visualization

```
┌─────────────┐     ┌───────────┐     ┌──────────────┐
│ NovaConnect │────▶│ CSDE API  │────▶│ CSDE Engine  │
└─────────────┘     └───────────┘     └──────────────┘
       │                  │                  │
       ▼                  ▼                  ▼
┌─────────────┐     ┌───────────┐     ┌──────────────┐
│  MongoDB    │     │   Redis   │     │  PostgreSQL  │
└─────────────┘     └───────────┘     └──────────────┘
       │                  │                  │
       └──────────────────┼──────────────────┘
                          ▼
                   ┌──────────────┐
                   │  Prometheus  │
                   └──────────────┘
                          │
                          ▼
                   ┌──────────────┐
                   │   Grafana    │
                   └──────────────┘
```

## Deployment Options

### Docker Compose

Docker Compose is recommended for development and small-scale production deployments.

#### Build and Deploy

1. **Build the Docker images**:

   ```bash
   # Build NovaConnect image
   docker build -t nova-connect:latest -f Dockerfile .
   
   # Build CSDE image
   docker build -t csde-api:latest -f ../src/csde/Dockerfile ../src/csde
   ```

2. **Deploy with Docker Compose**:

   ```bash
   docker-compose up -d
   ```

3. **Verify deployment**:

   ```bash
   docker-compose ps
   ```

### Kubernetes

Kubernetes is recommended for large-scale production deployments.

#### Build and Deploy

1. **Build and push Docker images**:

   ```bash
   # Build and tag NovaConnect image
   docker build -t your-registry/nova-connect:latest -f Dockerfile .
   docker push your-registry/nova-connect:latest
   
   # Build and tag CSDE image
   docker build -t your-registry/csde-api:latest -f ../src/csde/Dockerfile ../src/csde
   docker push your-registry/csde-api:latest
   ```

2. **Update Kubernetes deployment files**:

   Update the image references in `kubernetes/csde-integration-deployment.yaml` to point to your registry.

3. **Deploy to Kubernetes**:

   ```bash
   kubectl apply -f kubernetes/csde-integration-deployment.yaml
   ```

4. **Verify deployment**:

   ```bash
   kubectl get pods -n nova-connect
   ```

## Configuration

### Environment Variables

The following environment variables can be configured:

#### NovaConnect API

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Node.js environment | `production` |
| `PORT` | Port to listen on | `3001` |
| `CSDE_API_URL` | URL of the CSDE API | `http://csde-api:3010` |
| `LOG_LEVEL` | Logging level | `info` |
| `ENABLE_CACHING` | Enable caching | `true` |
| `ENABLE_METRICS` | Enable metrics | `true` |
| `CACHE_SIZE` | Maximum cache size | `1000` |
| `DOMAIN` | Domain-specific optimization | `security` |

#### CSDE API

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Node.js environment | `production` |
| `PORT` | Port to listen on | `3010` |
| `LOG_LEVEL` | Logging level | `info` |

### Configuration Files

#### NovaConnect API

The NovaConnect API is configured using the following files:

- `config/default.json`: Default configuration
- `config/production.json`: Production-specific configuration
- `config/connectors/csde-connector.json`: CSDE connector configuration

#### CSDE API

The CSDE API is configured using the following files:

- `config/default.json`: Default configuration
- `config/production.json`: Production-specific configuration

## Monitoring and Observability

### Metrics

The following metrics are available in Prometheus:

- `csde_api_requests_total`: Total number of CSDE API requests
- `csde_api_errors_total`: Total number of CSDE API errors
- `csde_api_request_duration_seconds`: CSDE API request duration
- `csde_api_cache_hits_total`: Total number of CSDE API cache hits
- `csde_api_cache_misses_total`: Total number of CSDE API cache misses
- `csde_api_performance_factor`: CSDE performance factor

### Grafana Dashboard

A Grafana dashboard is available for monitoring the CSDE integration. The dashboard includes the following panels:

- CSDE API Request Rate
- CSDE API Response Time
- Total CSDE API Requests
- Total CSDE API Errors
- CSDE API Cache Hit Rate
- CSDE Performance Factor

### Logging

Logs are written to the following locations:

- NovaConnect API: `/app/logs/nova-connect.log`
- CSDE API: `/app/logs/csde-api.log`

## Security Considerations

### Network Security

- Use HTTPS for all external communication
- Use network policies to restrict communication between services
- Use firewalls to restrict access to services

### Authentication and Authorization

- Use API keys for authentication between services
- Use JWT for user authentication
- Implement role-based access control

### Secrets Management

- Use Kubernetes Secrets or Docker Secrets for sensitive information
- Rotate secrets regularly
- Use a secrets management solution like HashiCorp Vault

## Scaling

### Horizontal Scaling

The NovaConnect + CSDE integration can be horizontally scaled by increasing the number of replicas:

```bash
# Scale NovaConnect API
kubectl scale deployment nova-connect-api -n nova-connect --replicas=3

# Scale CSDE API
kubectl scale deployment csde-api -n nova-connect --replicas=3
```

### Vertical Scaling

The NovaConnect + CSDE integration can be vertically scaled by increasing the resources allocated to each pod:

```yaml
resources:
  requests:
    cpu: "200m"
    memory: "512Mi"
  limits:
    cpu: "1000m"
    memory: "1Gi"
```

## Backup and Recovery

### Database Backup

- MongoDB: Use `mongodump` to create backups
- PostgreSQL: Use `pg_dump` to create backups

### Configuration Backup

- Back up configuration files regularly
- Store backups in a secure location

### Recovery

- Restore databases from backups
- Restore configuration files from backups
- Redeploy services

## Troubleshooting

### Common Issues

#### CSDE API Connection Issues

If you're having trouble connecting to the CSDE API:

1. Check that the CSDE API is running: `kubectl get pods -n nova-connect`
2. Verify the CSDE API URL: `echo $CSDE_API_URL`
3. Check the CSDE API logs: `kubectl logs -n nova-connect deployment/csde-api`

#### Performance Issues

If you're experiencing performance issues:

1. Enable caching: `ENABLE_CACHING=true`
2. Increase cache size: `CACHE_SIZE=1000`
3. Use batch processing for large datasets: `BATCH_SIZE=10`
4. Monitor performance metrics in Grafana

#### Error Handling

Common errors and how to handle them:

1. **CSDE API Not Available**: Implement a fallback to standard transformation
2. **Invalid Input Data**: Validate data before sending to CSDE API
3. **Timeout**: Implement retry logic with exponential backoff

### Debugging

- Check logs: `kubectl logs -n nova-connect deployment/nova-connect-api`
- Check metrics: `kubectl port-forward -n nova-connect service/prometheus 9090:9090`
- Check health: `curl http://localhost:3001/health`

## Maintenance

### Updates

- Update NovaConnect API: `kubectl set image deployment/nova-connect-api nova-connect-api=your-registry/nova-connect:new-version`
- Update CSDE API: `kubectl set image deployment/csde-api csde-api=your-registry/csde-api:new-version`

### Monitoring

- Monitor metrics in Grafana
- Set up alerts for critical metrics
- Review logs regularly

### Cleanup

- Remove old Docker images: `docker image prune -a`
- Remove old Kubernetes resources: `kubectl delete -f kubernetes/csde-integration-deployment.yaml`
