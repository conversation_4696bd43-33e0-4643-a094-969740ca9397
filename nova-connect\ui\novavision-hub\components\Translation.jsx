/**
 * Translation Component
 * 
 * A component for translating text.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n/I18nContext';

/**
 * Translation component
 * 
 * @param {Object} props - Component props
 * @param {string} props.id - Translation key
 * @param {Object} [props.params] - Translation parameters
 * @param {string} [props.defaultValue] - Default value if translation is not found
 * @param {string} [props.component='span'] - Component to render
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} Translation component
 */
const Translation = ({
  id,
  params,
  defaultValue,
  component: Component = 'span',
  className = '',
  style = {},
  ...rest
}) => {
  const { translate } = useI18n();
  
  // Get translation
  const translation = translate(id, params) || defaultValue || id;
  
  return (
    <Component
      className={className}
      style={style}
      {...rest}
    >
      {translation}
    </Component>
  );
};

Translation.propTypes = {
  id: PropTypes.string.isRequired,
  params: PropTypes.object,
  defaultValue: PropTypes.string,
  component: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object
};

export default Translation;
