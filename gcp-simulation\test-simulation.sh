#!/bin/bash

# Test GCP Simulation Environment
# This script tests the GCP simulation environment for NovaFuse

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing GCP Simulation Environment${NC}"

# Check if the containers are running
if ! docker-compose ps | grep -q "Up"; then
  echo -e "${RED}Containers are not running. Please start the simulation environment first.${NC}"
  exit 1
fi

# Test NovaFuse API
echo -e "${YELLOW}Testing NovaFuse API...${NC}"
API_HEALTH=$(curl -s http://localhost:3001/health)
if [[ $API_HEALTH == *"ok"* ]]; then
  echo -e "${GREEN}NovaFuse API is running.${NC}"
else
  echo -e "${RED}NovaFuse API is not responding correctly.${NC}"
  exit 1
fi

# Test NovaConnect UAC
echo -e "${YELLOW}Testing NovaConnect UAC...${NC}"
UAC_HEALTH=$(curl -s http://localhost:3002/health)
if [[ $UAC_HEALTH == *"ok"* ]]; then
  echo -e "${GREEN}NovaConnect UAC is running.${NC}"
else
  echo -e "${RED}NovaConnect UAC is not responding correctly.${NC}"
  exit 1
fi

# Test Security Command Center Simulator
echo -e "${YELLOW}Testing Security Command Center Simulator...${NC}"
SCC_HEALTH=$(curl -s http://localhost:8081/health)
if [[ $SCC_HEALTH == *"ok"* ]]; then
  echo -e "${GREEN}Security Command Center Simulator is running.${NC}"
else
  echo -e "${RED}Security Command Center Simulator is not responding correctly.${NC}"
  exit 1
fi

# Test Cloud IAM Simulator
echo -e "${YELLOW}Testing Cloud IAM Simulator...${NC}"
IAM_HEALTH=$(curl -s http://localhost:8082/health)
if [[ $IAM_HEALTH == *"ok"* ]]; then
  echo -e "${GREEN}Cloud IAM Simulator is running.${NC}"
else
  echo -e "${RED}Cloud IAM Simulator is not responding correctly.${NC}"
  exit 1
fi

# Test BigQuery Simulator
echo -e "${YELLOW}Testing BigQuery Simulator...${NC}"
BQ_HEALTH=$(curl -s http://localhost:8083/health)
if [[ $BQ_HEALTH == *"ok"* ]]; then
  echo -e "${GREEN}BigQuery Simulator is running.${NC}"
else
  echo -e "${RED}BigQuery Simulator is not responding correctly.${NC}"
  exit 1
fi

# Test NovaFuse UI
echo -e "${YELLOW}Testing NovaFuse UI...${NC}"
UI_HEALTH=$(curl -s http://localhost:3003/health)
if [[ $UI_HEALTH == *"ok"* ]]; then
  echo -e "${GREEN}NovaFuse UI is running.${NC}"
else
  echo -e "${RED}NovaFuse UI is not responding correctly.${NC}"
  exit 1
fi

# Test API Gateway
echo -e "${YELLOW}Testing API Gateway...${NC}"
GATEWAY_RESPONSE=$(curl -s http://localhost:3000)
if [[ $GATEWAY_RESPONSE == *"NovaFuse"* ]]; then
  echo -e "${GREEN}API Gateway is running.${NC}"
else
  echo -e "${RED}API Gateway is not responding correctly.${NC}"
  exit 1
fi

# Test Feature Flag System
echo -e "${YELLOW}Testing Feature Flag System...${NC}"

# Test with NovaPrime tier (should have access to all features)
echo -e "${YELLOW}Testing with NovaPrime tier...${NC}"
PRIVACY_RESPONSE=$(curl -s -H "X-Product-Tier: novaPrime" http://localhost:3001/privacy/management/processing-activities)
if [[ $PRIVACY_RESPONSE == *"data"* ]]; then
  echo -e "${GREEN}NovaPrime has access to Privacy Management API.${NC}"
else
  echo -e "${RED}NovaPrime does not have access to Privacy Management API.${NC}"
  exit 1
fi

# Test with NovaCore tier (should have access to privacy but not ESG)
echo -e "${YELLOW}Testing with NovaCore tier...${NC}"
PRIVACY_RESPONSE=$(curl -s -H "X-Product-Tier: novaCore" http://localhost:3001/privacy/management/processing-activities)
ESG_RESPONSE=$(curl -s -H "X-Product-Tier: novaCore" http://localhost:3001/esg/metrics)
if [[ $PRIVACY_RESPONSE == *"data"* ]] && [[ $ESG_RESPONSE == *"Feature not available"* ]]; then
  echo -e "${GREEN}NovaCore has correct feature access.${NC}"
else
  echo -e "${RED}NovaCore does not have correct feature access.${NC}"
  exit 1
fi

# Test Google Cloud Integration
echo -e "${YELLOW}Testing Google Cloud Integration...${NC}"
SCC_INTEGRATION=$(curl -s http://localhost:3001/integrations/gcp/scc/findings)
if [[ $SCC_INTEGRATION == *"data"* ]]; then
  echo -e "${GREEN}Google Cloud Integration is working.${NC}"
else
  echo -e "${RED}Google Cloud Integration is not working.${NC}"
  exit 1
fi

# Test NovaConnect UAC with Google Cloud connectors
echo -e "${YELLOW}Testing NovaConnect UAC with Google Cloud connectors...${NC}"
GCP_CONNECTORS=$(curl -s http://localhost:3002/integrations/gcp/connectors)
if [[ $GCP_CONNECTORS == *"Google Security Command Center"* ]]; then
  echo -e "${GREEN}NovaConnect UAC has Google Cloud connectors.${NC}"
else
  echo -e "${RED}NovaConnect UAC does not have Google Cloud connectors.${NC}"
  exit 1
fi

echo -e "${GREEN}All tests passed. GCP Simulation Environment is working correctly.${NC}"

# Open the NovaFuse UI in the browser
echo -e "${YELLOW}Opening NovaFuse UI in the browser...${NC}"
if command -v xdg-open > /dev/null; then
  xdg-open http://localhost:3003
elif command -v open > /dev/null; then
  open http://localhost:3003
elif command -v start > /dev/null; then
  start http://localhost:3003
else
  echo -e "${YELLOW}Please open http://localhost:3003 in your browser.${NC}"
fi
