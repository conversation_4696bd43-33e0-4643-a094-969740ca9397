/**
 * GraphQL Query Builder Component
 * 
 * This component provides a UI for building GraphQL queries.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Divider, 
  FormControl, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  TextField, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RefreshIcon from '@mui/icons-material/Refresh';
import CodeIcon from '@mui/icons-material/Code';
import SchemaIcon from '@mui/icons-material/AccountTree';
import { graphqlApi } from '../../services/api';

// Monaco Editor for code editing
import Editor from '@monaco-editor/react';

const GraphQLQueryBuilder = ({ endpoint, headers, auth, onExecute }) => {
  const [query, setQuery] = useState('');
  const [variables, setVariables] = useState('{}');
  const [schema, setSchema] = useState(null);
  const [schemaLoading, setSchemaLoading] = useState(false);
  const [schemaError, setSchemaError] = useState(null);
  const [operationType, setOperationType] = useState('query');
  const [validationResult, setValidationResult] = useState(null);
  
  // Fetch schema on component mount or when endpoint changes
  useEffect(() => {
    if (endpoint) {
      fetchSchema();
    }
  }, [endpoint]);
  
  // Fetch GraphQL schema
  const fetchSchema = async () => {
    setSchemaLoading(true);
    setSchemaError(null);
    
    try {
      const response = await graphqlApi.fetchSchema(endpoint, headers, auth);
      setSchema(response.data);
      
      // Generate a sample query based on the schema
      if (response.data) {
        generateSampleQuery(response.data);
      }
    } catch (error) {
      console.error('Error fetching GraphQL schema:', error);
      setSchemaError(error.message || 'Failed to fetch schema');
    } finally {
      setSchemaLoading(false);
    }
  };
  
  // Generate a sample query
  const generateSampleQuery = async (schemaData) => {
    try {
      const response = await graphqlApi.generateSampleQuery(schemaData, operationType);
      setQuery(response.data.query);
    } catch (error) {
      console.error('Error generating sample query:', error);
    }
  };
  
  // Validate the query
  const validateQuery = async () => {
    if (!query || !schema) return;
    
    try {
      const response = await graphqlApi.validateQuery(query, schema);
      setValidationResult(response.data);
      
      return response.data.valid;
    } catch (error) {
      console.error('Error validating query:', error);
      setValidationResult({
        valid: false,
        errors: [error.message || 'Validation failed']
      });
      
      return false;
    }
  };
  
  // Handle query execution
  const handleExecute = async () => {
    // Validate query first
    const isValid = await validateQuery();
    
    if (!isValid) {
      return;
    }
    
    // Parse variables
    let parsedVariables = {};
    try {
      parsedVariables = variables ? JSON.parse(variables) : {};
    } catch (error) {
      setValidationResult({
        valid: false,
        errors: ['Invalid JSON in variables']
      });
      return;
    }
    
    // Execute query
    if (onExecute) {
      onExecute(query, parsedVariables);
    }
  };
  
  // Handle operation type change
  const handleOperationTypeChange = (event) => {
    setOperationType(event.target.value);
    
    if (schema) {
      generateSampleQuery(schema);
    }
  };
  
  return (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              GraphQL Query Builder
            </Typography>
            
            <Box>
              <FormControl size="small" sx={{ minWidth: 120, mr: 2 }}>
                <InputLabel id="operation-type-label">Operation</InputLabel>
                <Select
                  labelId="operation-type-label"
                  value={operationType}
                  label="Operation"
                  onChange={handleOperationTypeChange}
                >
                  <MenuItem value="query">Query</MenuItem>
                  <MenuItem value="mutation">Mutation</MenuItem>
                </Select>
              </FormControl>
              
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchSchema}
                disabled={schemaLoading || !endpoint}
                sx={{ mr: 1 }}
              >
                {schemaLoading ? 'Loading...' : 'Refresh Schema'}
              </Button>
              
              <Button
                variant="contained"
                startIcon={<PlayArrowIcon />}
                onClick={handleExecute}
                disabled={!query || !endpoint}
              >
                Execute
              </Button>
            </Box>
          </Box>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <Paper variant="outlined" sx={{ height: 400 }}>
            <Editor
              height="400px"
              language="graphql"
              value={query}
              onChange={setQuery}
              options={{
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                wordWrap: 'on'
              }}
            />
          </Paper>
          
          {validationResult && (
            <Box sx={{ mt: 2 }}>
              {validationResult.valid ? (
                <Typography variant="body2" color="success.main">
                  Query is valid
                </Typography>
              ) : (
                <Box>
                  <Typography variant="body2" color="error.main">
                    Query validation failed:
                  </Typography>
                  <ul style={{ margin: '4px 0', paddingLeft: 20 }}>
                    {validationResult.errors.map((error, index) => (
                      <li key={index}>
                        <Typography variant="body2" color="error.main">
                          {error}
                        </Typography>
                      </li>
                    ))}
                  </ul>
                </Box>
              )}
            </Box>
          )}
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper variant="outlined" sx={{ height: 400, display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ p: 1, borderBottom: 1, borderColor: 'divider' }}>
              <Typography variant="subtitle2">
                Variables (JSON)
              </Typography>
            </Box>
            <Box sx={{ flexGrow: 1 }}>
              <Editor
                height="100%"
                language="json"
                value={variables}
                onChange={setVariables}
                options={{
                  minimap: { enabled: false },
                  scrollBeyondLastLine: false,
                  fontSize: 14,
                  wordWrap: 'on'
                }}
              />
            </Box>
          </Paper>
        </Grid>
        
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SchemaIcon sx={{ mr: 1 }} />
                <Typography variant="h6">
                  Schema Explorer
                </Typography>
              </Box>
              
              {schemaLoading ? (
                <Typography variant="body2">Loading schema...</Typography>
              ) : schemaError ? (
                <Typography variant="body2" color="error.main">
                  Error loading schema: {schemaError}
                </Typography>
              ) : !schema ? (
                <Typography variant="body2">
                  No schema available. Please connect to a GraphQL endpoint.
                </Typography>
              ) : (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Available Types
                  </Typography>
                  
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {schema.types
                      .filter(type => !type.name.startsWith('__'))
                      .map(type => (
                        <Chip 
                          key={type.name} 
                          label={`${type.name} (${type.kind})`} 
                          size="small" 
                          variant="outlined"
                          onClick={() => {
                            // In a real implementation, this would show type details
                            console.log('Type details:', type);
                          }}
                        />
                      ))}
                  </Box>
                  
                  <Divider sx={{ my: 2 }} />
                  
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Query Type: {schema.queryType?.name}
                      </Typography>
                      
                      {schema.types
                        .find(type => type.name === schema.queryType?.name)
                        ?.fields?.map(field => (
                          <Box key={field.name} sx={{ mb: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {field.name}
                              {field.args && field.args.length > 0 && (
                                <span style={{ color: 'gray' }}>
                                  ({field.args.map(arg => `${arg.name}: ${arg.type.name || 'Object'}`).join(', ')})
                                </span>
                              )}
                              : {field.type.name || 'Object'}
                            </Typography>
                            {field.description && (
                              <Typography variant="caption" color="textSecondary">
                                {field.description}
                              </Typography>
                            )}
                          </Box>
                        ))}
                    </Grid>
                    
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Mutation Type: {schema.mutationType?.name || 'None'}
                      </Typography>
                      
                      {schema.mutationType?.name && schema.types
                        .find(type => type.name === schema.mutationType.name)
                        ?.fields?.map(field => (
                          <Box key={field.name} sx={{ mb: 1 }}>
                            <Typography variant="body2" fontWeight="medium">
                              {field.name}
                              {field.args && field.args.length > 0 && (
                                <span style={{ color: 'gray' }}>
                                  ({field.args.map(arg => `${arg.name}: ${arg.type.name || 'Object'}`).join(', ')})
                                </span>
                              )}
                              : {field.type.name || 'Object'}
                            </Typography>
                            {field.description && (
                              <Typography variant="caption" color="textSecondary">
                                {field.description}
                              </Typography>
                            )}
                          </Box>
                        ))}
                    </Grid>
                  </Grid>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GraphQLQueryBuilder;
