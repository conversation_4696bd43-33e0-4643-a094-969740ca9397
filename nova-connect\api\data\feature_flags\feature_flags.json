[{"id": "core.basic_connectors", "name": "Basic Connectors", "description": "Connect to basic API endpoints", "category": "core", "enabled": true, "tiers": ["free", "standard", "professional", "enterprise"], "limits": {"free": {"connections": 3}, "standard": {"connections": 10}, "professional": {"connections": 50}, "enterprise": {"connections": -1}}}, {"id": "core.manual_execution", "name": "Manual Execution", "description": "Manually execute API operations", "category": "core", "enabled": true, "tiers": ["free", "standard", "professional", "enterprise"], "limits": {"free": {"operations_per_day": 50}, "standard": {"operations_per_day": 500}, "professional": {"operations_per_day": 5000}, "enterprise": {"operations_per_day": -1}}}, {"id": "core.basic_monitoring", "name": "Basic Monitoring", "description": "Basic API monitoring capabilities", "category": "core", "enabled": true, "tiers": ["free", "standard", "professional", "enterprise"]}, {"id": "workflow.basic", "name": "Basic Workflows", "description": "Create simple sequential workflows", "category": "workflow", "enabled": true, "tiers": ["free", "standard", "professional", "enterprise"], "limits": {"free": {"workflows": 1, "actions_per_workflow": 5}, "standard": {"workflows": 5, "actions_per_workflow": 10}, "professional": {"workflows": 20, "actions_per_workflow": 50}, "enterprise": {"workflows": -1, "actions_per_workflow": -1}}}, {"id": "workflow.advanced", "name": "Advanced Workflows", "description": "Create complex workflows with conditions and branching", "category": "workflow", "enabled": true, "tiers": ["professional", "enterprise"]}, {"id": "workflow.scheduled", "name": "Scheduled Workflows", "description": "Schedule workflows to run automatically", "category": "workflow", "enabled": true, "tiers": ["standard", "professional", "enterprise"], "limits": {"standard": {"scheduled_workflows": 2}, "professional": {"scheduled_workflows": 10}, "enterprise": {"scheduled_workflows": -1}}}, {"id": "workflow.event_triggered", "name": "Event-Triggered Workflows", "description": "Trigger workflows based on events", "category": "workflow", "enabled": true, "tiers": ["professional", "enterprise"]}, {"id": "export_import.basic", "name": "Basic Export/Import", "description": "Basic configuration export and import", "category": "export_import", "enabled": true, "tiers": ["standard", "professional", "enterprise"]}, {"id": "export_import.advanced", "name": "Advanced Export/Import", "description": "Advanced configuration export and import with selective options", "category": "export_import", "enabled": true, "tiers": ["professional", "enterprise"]}, {"id": "security.basic", "name": "Basic Security", "description": "Basic security features", "category": "security", "enabled": true, "tiers": ["free", "standard", "professional", "enterprise"]}, {"id": "security.advanced", "name": "Advanced Security", "description": "Advanced security features including IP restrictions", "category": "security", "enabled": true, "tiers": ["professional", "enterprise"]}, {"id": "security.enterprise", "name": "Enterprise Security", "description": "Enterprise-grade security features", "category": "security", "enabled": true, "tiers": ["enterprise"]}, {"id": "monitoring.advanced", "name": "Advanced Monitoring", "description": "Advanced API monitoring capabilities", "category": "monitoring", "enabled": true, "tiers": ["standard", "professional", "enterprise"]}, {"id": "monitoring.alerting", "name": "Alerting", "description": "Set up alerts for API monitoring", "category": "monitoring", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"alerts": 10}, "enterprise": {"alerts": -1}}}, {"id": "analytics.basic", "name": "Basic Analytics", "description": "Basic analytics and reporting", "category": "analytics", "enabled": true, "tiers": ["standard", "professional", "enterprise"]}, {"id": "analytics.advanced", "name": "Advanced Analytics", "description": "Advanced analytics and reporting", "category": "analytics", "enabled": true, "tiers": ["professional", "enterprise"]}, {"id": "analytics.custom_reports", "name": "Custom Reports", "description": "Create and schedule custom reports", "category": "analytics", "enabled": true, "tiers": ["enterprise"]}, {"id": "ai.connector_generation", "name": "AI-Assisted Connector Creation", "description": "Generate connector configurations from API documentation", "category": "ai", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"generations_per_day": 5}, "enterprise": {"generations_per_day": 20}}}, {"id": "ai.natural_language", "name": "Natural Language API Queries", "description": "Create connectors using natural language descriptions", "category": "ai", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"queries_per_day": 10}, "enterprise": {"queries_per_day": 50}}}, {"id": "ai.error_resolution", "name": "Intelligent Error Resolution", "description": "AI-powered suggestions for fixing API errors", "category": "ai", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"suggestions_per_day": 20}, "enterprise": {"suggestions_per_day": 100}}}, {"id": "ai.workflow_optimization", "name": "Predictive Workflow Optimization", "description": "AI-powered suggestions for optimizing workflows", "category": "ai", "enabled": true, "tiers": ["enterprise"], "limits": {"enterprise": {"optimizations_per_day": 10}}}, {"id": "governance.approvals", "name": "Approval Workflows", "description": "Create and manage multi-step approval workflows", "category": "governance", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"active_workflows": 5}, "enterprise": {"active_workflows": -1}}}, {"id": "governance.compliance", "name": "Compliance Templates", "description": "Pre-built compliance templates for various regulations", "category": "governance", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"custom_templates": 2}, "enterprise": {"custom_templates": -1}}}, {"id": "governance.data_lineage", "name": "Data Lineage Tracking", "description": "Track how data moves through different systems", "category": "governance", "enabled": true, "tiers": ["enterprise"], "limits": {"enterprise": {"tracking_depth": 3}}}, {"id": "security.ip_restrictions", "name": "IP Restrictions", "description": "Restrict API access to specific IP addresses", "category": "security", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"restrictions_per_resource": 5}, "enterprise": {"restrictions_per_resource": -1}}}, {"id": "security.encryption", "name": "Advanced Encryption", "description": "Manage encryption keys and encrypt sensitive data", "category": "security", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"encryption_keys": 10}, "enterprise": {"encryption_keys": -1}}}, {"id": "security.policies", "name": "Custom Security Policies", "description": "Create and enforce custom security policies", "category": "security", "enabled": true, "tiers": ["enterprise"], "limits": {"enterprise": {"policies": -1}}}, {"id": "monitoring.anomaly_detection", "name": "Anomaly Detection", "description": "Automatically detect unusual patterns in API usage", "category": "monitoring", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"detection_frequency": "daily"}, "enterprise": {"detection_frequency": "hourly"}}}, {"id": "analytics.custom_reports", "name": "Custom Reports", "description": "Create and schedule custom reports", "category": "analytics", "enabled": true, "tiers": ["enterprise"], "limits": {"enterprise": {"reports_per_month": 50}}}, {"id": "analytics.scheduled_reports", "name": "Scheduled Reports", "description": "Schedule reports to run automatically", "category": "analytics", "enabled": true, "tiers": ["enterprise"], "limits": {"enterprise": {"scheduled_reports": 10}}}, {"id": "analytics.export_formats", "name": "Advanced Export Formats", "description": "Export reports in various formats (PDF, CSV, Excel)", "category": "analytics", "enabled": true, "tiers": ["professional", "enterprise"], "limits": {"professional": {"formats": ["CSV"]}, "enterprise": {"formats": ["CSV", "PDF", "Excel"]}}}]