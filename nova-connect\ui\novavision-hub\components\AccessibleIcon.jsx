/**
 * AccessibleIcon Component
 * 
 * A wrapper component that makes icons accessible by adding appropriate ARIA attributes.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { generateAccessibilityId } from '../utils/accessibility';

/**
 * AccessibleIcon component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.icon - Icon to render
 * @param {string} props.label - Accessible label for the icon
 * @param {boolean} [props.focusable=false] - Whether the icon should be focusable
 * @param {string} [props.role='img'] - ARIA role for the icon
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @param {Object} [props.labelProps] - Additional props for the label element
 * @returns {React.ReactElement} AccessibleIcon component
 */
const AccessibleIcon = ({
  icon,
  label,
  focusable = false,
  role = 'img',
  className = '',
  style = {},
  labelProps = {},
  ...rest
}) => {
  // Generate unique ID for the label
  const labelId = generateAccessibilityId('icon-label');
  
  return (
    <span
      className={`inline-flex ${className}`}
      style={style}
      role={role}
      aria-labelledby={labelId}
      aria-hidden={!focusable}
      tabIndex={focusable ? 0 : -1}
      data-testid="accessible-icon"
      {...rest}
    >
      {icon}
      <span
        id={labelId}
        className="sr-only"
        {...labelProps}
      >
        {label}
      </span>
    </span>
  );
};

AccessibleIcon.propTypes = {
  icon: PropTypes.node.isRequired,
  label: PropTypes.string.isRequired,
  focusable: PropTypes.bool,
  role: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  labelProps: PropTypes.object
};

export default AccessibleIcon;
