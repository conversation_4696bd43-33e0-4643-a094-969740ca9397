/**
 * NovaConnect Performance Tests - Remediation Workflow
 * 
 * These tests validate the performance of remediation workflows,
 * ensuring they complete within the 8-second requirement.
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics if available
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  remediationUrl: '/api/remediation'
};

// Test data for HIPAA violation remediation
const hipaaViolationScenario = {
  id: 'hipaa-violation-1',
  type: 'compliance',
  framework: 'HIPAA',
  control: '164.312(a)(1)',
  severity: 'high',
  resource: {
    id: 'storage-bucket-123',
    type: 'storage',
    name: 'patient-data-bucket',
    provider: 'gcp'
  },
  finding: {
    id: 'finding-456',
    title: 'Unencrypted PHI data in storage',
    description: 'Protected Health Information (PHI) is stored without encryption',
    detectedAt: new Date().toISOString()
  },
  remediationSteps: [
    {
      id: 'step-1',
      action: 'enable-encryption',
      parameters: {
        encryptionType: 'AES-256',
        keyRotationPeriod: '90d'
      }
    },
    {
      id: 'step-2',
      action: 'update-access-controls',
      parameters: {
        accessLevel: 'restricted',
        allowedRoles: ['healthcare-admin', 'compliance-officer']
      }
    },
    {
      id: 'step-3',
      action: 'generate-evidence',
      parameters: {
        evidenceType: 'encryption-audit',
        destination: 'compliance-evidence-store'
      }
    },
    {
      id: 'step-4',
      action: 'update-compliance-score',
      parameters: {
        framework: 'HIPAA',
        control: '164.312(a)(1)'
      }
    }
  ]
};

// Test data for conflicting compliance requirements
const conflictingRequirementsScenario = {
  id: 'conflicting-reqs-1',
  type: 'compliance',
  frameworks: ['PCI-DSS', 'GDPR'],
  severity: 'medium',
  resource: {
    id: 'database-456',
    type: 'database',
    name: 'customer-payment-db',
    provider: 'gcp'
  },
  finding: {
    id: 'finding-789',
    title: 'Conflicting data retention policies',
    description: 'PCI-DSS requires data retention while GDPR requires data deletion',
    detectedAt: new Date().toISOString()
  },
  conflictingRequirements: [
    {
      framework: 'PCI-DSS',
      control: '3.1',
      requirement: 'Retain cardholder data for business, legal, and regulatory purposes'
    },
    {
      framework: 'GDPR',
      control: 'Article 17',
      requirement: 'Right to erasure (right to be forgotten)'
    }
  ]
};

// Test data for failed remediation
const failedRemediationScenario = {
  id: 'failed-remediation-1',
  type: 'compliance',
  framework: 'SOC2',
  control: 'CC7.1',
  severity: 'high',
  resource: {
    id: 'vm-789',
    type: 'compute',
    name: 'auth-server-prod',
    provider: 'gcp'
  },
  finding: {
    id: 'finding-101',
    title: 'Missing security patches',
    description: 'Critical security patches are missing on production authentication server',
    detectedAt: new Date().toISOString()
  },
  remediationSteps: [
    {
      id: 'step-1',
      action: 'apply-security-patches',
      parameters: {
        patchIds: ['CVE-2023-1234', 'CVE-2023-5678'],
        requireRestart: true
      }
    }
  ],
  expectedFailure: {
    reason: 'insufficient-permissions',
    details: 'Service account lacks permission to update VM instances'
  }
};

describe('Remediation Workflow Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);
  
  // Test multi-step remediation sequence
  it('should complete multi-step remediation within 8 seconds', async () => {
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.remediationUrl}/execute`, hipaaViolationScenario);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('success', true);
    expect(result.data).toHaveProperty('remediationId');
    expect(result.data).toHaveProperty('steps');
    expect(result.data.steps).toHaveLength(hipaaViolationScenario.remediationSteps.length);
    expect(result.data.steps.every(step => step.status === 'completed')).toBe(true);
    
    // Remediation should complete in less than 8 seconds
    expect(duration).toBeLessThan(8000);
    
    console.log(`Multi-step remediation completed in ${duration.toFixed(2)} ms`);
    console.log(`Average time per step: ${(duration / hipaaViolationScenario.remediationSteps.length).toFixed(2)} ms`);
  });
  
  // Test conflicting compliance requirements resolution
  it('should resolve conflicting compliance requirements within acceptable time', async () => {
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.remediationUrl}/resolve-conflict`, conflictingRequirementsScenario);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('success', true);
    expect(result.data).toHaveProperty('resolution');
    expect(result.data).toHaveProperty('appliedFramework');
    expect(result.data).toHaveProperty('justification');
    
    // Conflict resolution should be reasonably fast
    expect(duration).toBeLessThan(5000);
    
    console.log(`Conflicting requirements resolution completed in ${duration.toFixed(2)} ms`);
  });
  
  // Test failed remediation handling
  it('should handle failed remediation properly and efficiently', async () => {
    const { result, duration } = await measureExecutionTime(async () => {
      return await axios.post(`${config.baseUrl}${config.remediationUrl}/execute`, failedRemediationScenario);
    });
    
    expect(result.status).toBe(200);
    expect(result.data).toHaveProperty('success', false);
    expect(result.data).toHaveProperty('error');
    expect(result.data).toHaveProperty('escalation');
    expect(result.data.error).toHaveProperty('reason', failedRemediationScenario.expectedFailure.reason);
    
    // Failed remediation handling should be efficient
    expect(duration).toBeLessThan(3000);
    
    console.log(`Failed remediation handling completed in ${duration.toFixed(2)} ms`);
  });
  
  // Test remediation throughput
  it('should achieve acceptable throughput for sequential remediations', async () => {
    const totalRemediations = 10;
    const remediationFn = () => axios.post(`${config.baseUrl}${config.remediationUrl}/execute`, hipaaViolationScenario);
    
    const { result, duration } = await measureExecutionTime(async () => {
      const results = [];
      for (let i = 0; i < totalRemediations; i++) {
        results.push(await remediationFn());
      }
      return results;
    });
    
    expect(result).toHaveLength(totalRemediations);
    result.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('success', true);
    });
    
    // Calculate throughput (remediations per minute)
    const throughput = (totalRemediations / duration) * 60000;
    
    console.log(`${totalRemediations} sequential remediations completed in ${duration.toFixed(2)} ms`);
    console.log(`Throughput: ${throughput.toFixed(2)} remediations/minute`);
    
    // Throughput should be reasonable for enterprise use
    expect(throughput).toBeGreaterThan(30); // At least 30 remediations per minute
  });
});
