/**
 * GraphQL Routes
 */

const express = require('express');
const router = express.Router();
const GraphQLController = require('../controllers/GraphQLController');

// Execute a GraphQL query
router.post('/execute', (req, res, next) => {
  GraphQLController.executeQuery(req, res, next);
});

// Fetch GraphQL schema
router.post('/schema', (req, res, next) => {
  GraphQLController.fetchSchema(req, res, next);
});

// Validate a GraphQL query
router.post('/validate', (req, res, next) => {
  GraphQLController.validateQuery(req, res, next);
});

// Generate a sample GraphQL query
router.post('/sample', (req, res, next) => {
  GraphQLController.generateSampleQuery(req, res, next);
});

module.exports = router;
