import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Box, CircularProgress } from '@mui/material';

/**
 * PhaseSpaceVisualization component
 * 
 * Renders a 3D phase space visualization of tensor data using Three.js
 * Phase space visualizations are useful for analyzing system dynamics and stability
 */
function PhaseSpaceVisualization({
  tensor,
  options = {
    renderMode: 'medium',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    colorScheme: 'default',
    trailLength: 100,
    dimensions: 3 // 2D or 3D phase space
  },
  width = '100%',
  height = '100%'
}) {
  const containerRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const animationFrameRef = useRef(null);
  const phasePointsRef = useRef([]);
  const trailRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x121212);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(
        75,
        containerRef.current.clientWidth / containerRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.z = 5;
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
      containerRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Create controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.25;
      controls.enableZoom = true;
      controlsRef.current = controls;

      // Add ambient light
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambientLight);

      // Add directional light
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(1, 1, 1);
      scene.add(directionalLight);

      // Add axes helper if enabled
      if (options.showAxes) {
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
      }

      // Add grid helper if enabled
      if (options.showGrid) {
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);
      }

      // Add phase space boundaries
      const boundaryGeometry = new THREE.BoxGeometry(10, 10, 10);
      const boundaryEdges = new THREE.EdgesGeometry(boundaryGeometry);
      const boundaryMaterial = new THREE.LineBasicMaterial({ color: 0x444444 });
      const boundary = new THREE.LineSegments(boundaryEdges, boundaryMaterial);
      scene.add(boundary);

      // Animation loop
      const animate = () => {
        animationFrameRef.current = requestAnimationFrame(animate);
        
        // Update controls
        if (controlsRef.current) {
          controlsRef.current.update();
        }
        
        // Render scene
        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      };

      // Start animation loop
      animate();

      // Handle window resize
      const handleResize = () => {
        if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
        
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;
        
        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
        
        rendererRef.current.setSize(width, height);
      };

      window.addEventListener('resize', handleResize);

      // Visualization is ready
      setIsLoading(false);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        if (rendererRef.current && containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        
        if (sceneRef.current) {
          // Dispose of all geometries and materials
          sceneRef.current.traverse((object) => {
            if (object.geometry) {
              object.geometry.dispose();
            }
            
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((material) => material.dispose());
              } else {
                object.material.dispose();
              }
            }
          });
        }
        
        rendererRef.current = null;
        sceneRef.current = null;
        cameraRef.current = null;
        controlsRef.current = null;
      };
    } catch (err) {
      console.error('Error initializing Three.js:', err);
      setError(err.message || 'Error initializing visualization');
      setIsLoading(false);
    }
  }, [options.showAxes, options.showGrid]);

  // Update visualization when tensor data changes
  useEffect(() => {
    if (!sceneRef.current || !tensor || isLoading) return;

    try {
      // Remove existing phase space visualization
      const existingPhaseSpace = sceneRef.current.getObjectByName('phase-space');
      if (existingPhaseSpace) {
        sceneRef.current.remove(existingPhaseSpace);
        
        // Dispose of geometry and material
        if (existingPhaseSpace.geometry) {
          existingPhaseSpace.geometry.dispose();
        }
        
        if (existingPhaseSpace.material) {
          if (Array.isArray(existingPhaseSpace.material)) {
            existingPhaseSpace.material.forEach((material) => material.dispose());
          } else {
            existingPhaseSpace.material.dispose();
          }
        }
      }

      // Create phase space group
      const phaseSpaceGroup = new THREE.Group();
      phaseSpaceGroup.name = 'phase-space';

      // Get tensor values
      const values = tensor.values || [];
      
      // Create phase space points
      const is3D = options.dimensions === 3;
      const pointCount = is3D ? Math.floor(values.length / 3) : Math.floor(values.length / 2);
      
      // Clear existing phase points
      phasePointsRef.current = [];
      
      // Create points for phase space
      for (let i = 0; i < pointCount; i++) {
        let point;
        
        if (is3D) {
          // 3D phase space (x, y, z)
          const x = (values[i * 3] || 0) * 10 - 5;
          const y = (values[i * 3 + 1] || 0) * 10 - 5;
          const z = (values[i * 3 + 2] || 0) * 10 - 5;
          point = new THREE.Vector3(x, y, z);
        } else {
          // 2D phase space (x, y) with z=0
          const x = (values[i * 2] || 0) * 10 - 5;
          const y = (values[i * 2 + 1] || 0) * 10 - 5;
          point = new THREE.Vector3(x, y, 0);
        }
        
        phasePointsRef.current.push(point);
      }
      
      // Create trail geometry
      const trailGeometry = new THREE.BufferGeometry().setFromPoints(phasePointsRef.current);
      
      // Create trail material based on color scheme
      const colorMap = getColorMap(options.colorScheme);
      const colors = [];
      
      for (let i = 0; i < phasePointsRef.current.length; i++) {
        // Color based on position in trail (newer points are brighter)
        const t = i / phasePointsRef.current.length;
        const color = colorMap(t);
        colors.push(color.r / 255, color.g / 255, color.b / 255);
      }
      
      trailGeometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
      
      const trailMaterial = new THREE.LineBasicMaterial({
        vertexColors: true,
        linewidth: 2
      });
      
      const trail = new THREE.Line(trailGeometry, trailMaterial);
      phaseSpaceGroup.add(trail);
      trailRef.current = trail;
      
      // Add points
      const pointGeometry = new THREE.SphereGeometry(0.1, 16, 16);
      
      for (let i = 0; i < phasePointsRef.current.length; i++) {
        // Only add points for every 5th position to reduce clutter
        if (i % 5 === 0) {
          const point = phasePointsRef.current[i];
          
          // Color based on position in trail (newer points are brighter)
          const t = i / phasePointsRef.current.length;
          const color = colorMap(t);
          const material = new THREE.MeshBasicMaterial({
            color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255)
          });
          
          const sphere = new THREE.Mesh(pointGeometry, material);
          sphere.position.copy(point);
          phaseSpaceGroup.add(sphere);
        }
      }
      
      // Add current point (highlighted)
      if (phasePointsRef.current.length > 0) {
        const currentPoint = phasePointsRef.current[phasePointsRef.current.length - 1];
        const currentPointGeometry = new THREE.SphereGeometry(0.2, 16, 16);
        const currentPointMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff });
        const currentPointMesh = new THREE.Mesh(currentPointGeometry, currentPointMaterial);
        currentPointMesh.position.copy(currentPoint);
        phaseSpaceGroup.add(currentPointMesh);
      }
      
      // Add phase space labels
      const createLabel = (text, position) => {
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 128;
        canvas.height = 64;
        
        context.fillStyle = '#ffffff';
        context.font = '24px Arial';
        context.fillText(text, 10, 40);
        
        const texture = new THREE.CanvasTexture(canvas);
        const material = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(material);
        sprite.position.copy(position);
        sprite.scale.set(1, 0.5, 1);
        
        return sprite;
      };
      
      // Add axis labels
      phaseSpaceGroup.add(createLabel('X', new THREE.Vector3(5.5, 0, 0)));
      phaseSpaceGroup.add(createLabel('Y', new THREE.Vector3(0, 5.5, 0)));
      
      if (is3D) {
        phaseSpaceGroup.add(createLabel('Z', new THREE.Vector3(0, 0, 5.5)));
      }

      // Add phase space to scene
      sceneRef.current.add(phaseSpaceGroup);

      // Adjust camera position
      if (cameraRef.current) {
        if (is3D) {
          cameraRef.current.position.set(7, 7, 7);
        } else {
          cameraRef.current.position.set(0, 0, 10);
        }
        
        cameraRef.current.lookAt(0, 0, 0);
        
        if (controlsRef.current) {
          controlsRef.current.update();
        }
      }
    } catch (err) {
      console.error('Error updating phase space visualization:', err);
      setError(err.message || 'Error updating visualization');
    }
  }, [tensor, options.dimensions, options.colorScheme, isLoading]);

  // Update rotation speed
  useEffect(() => {
    if (!sceneRef.current || isLoading) return;

    const phaseSpaceGroup = sceneRef.current.getObjectByName('phase-space');
    if (!phaseSpaceGroup) return;

    // Clear existing rotation animation
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Animation loop with rotation
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);
      
      // Rotate phase space based on rotation speed
      if (phaseSpaceGroup && options.rotationSpeed > 0) {
        phaseSpaceGroup.rotation.y += 0.01 * options.rotationSpeed;
      }
      
      // Update controls
      if (controlsRef.current) {
        controlsRef.current.update();
      }
      
      // Render scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    // Start animation loop
    animate();

    // Clean up
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [options.rotationSpeed, isLoading]);

  // Helper function to get color map based on color scheme
  const getColorMap = (colorScheme) => {
    switch (colorScheme) {
      case 'rainbow':
        return (value) => {
          const h = (1 - value) * 240; // Hue (0 to 240)
          const s = 1; // Saturation
          const l = 0.5; // Lightness
          
          return hslToRgb(h, s, l);
        };
        
      case 'heatmap':
        return (value) => {
          const r = Math.floor(value * 255);
          const g = Math.floor((1 - Math.abs(value - 0.5) * 2) * 255);
          const b = Math.floor((1 - value) * 255);
          
          return { r, g, b };
        };
        
      case 'grayscale':
        return (value) => {
          const intensity = Math.floor(value * 255);
          return { r: intensity, g: intensity, b: intensity };
        };
        
      case 'default':
      default:
        return (value) => {
          if (value < 0.33) {
            return { r: 0, g: Math.floor(value * 3 * 255), b: 255 };
          } else if (value < 0.66) {
            return { r: 0, g: 255, b: Math.floor((1 - (value - 0.33) * 3) * 255) };
          } else {
            return { r: Math.floor((value - 0.66) * 3 * 255), g: 255, b: 0 };
          }
        };
    }
  };

  // Helper function to convert HSL to RGB
  const hslToRgb = (h, s, l) => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      
      r = hue2rgb(p, q, (h / 360) + 1/3);
      g = hue2rgb(p, q, h / 360);
      b = hue2rgb(p, q, (h / 360) - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 1,
        bgcolor: 'background.paper'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1
          }}
        >
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1,
            color: 'error.main',
            p: 2,
            textAlign: 'center'
          }}
        >
          {error}
        </Box>
      )}
    </Box>
  );
}

export default PhaseSpaceVisualization;
