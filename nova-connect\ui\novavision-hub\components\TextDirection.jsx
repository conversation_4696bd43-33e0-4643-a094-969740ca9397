/**
 * TextDirection Component
 * 
 * A component for setting text direction based on the current locale.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n/I18nContext';

/**
 * TextDirection component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.component='div'] - Component to render
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} TextDirection component
 */
const TextDirection = ({
  children,
  component: Component = 'div',
  className = '',
  style = {},
  ...rest
}) => {
  const { getTextDirection } = useI18n();
  
  // Get text direction
  const dir = getTextDirection();
  
  return (
    <Component
      dir={dir}
      className={className}
      style={style}
      {...rest}
    >
      {children}
    </Component>
  );
};

TextDirection.propTypes = {
  children: PropTypes.node.isRequired,
  component: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object
};

export default TextDirection;
