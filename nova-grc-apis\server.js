/**
 * NovaGRC APIs
 * 
 * Main server entry point for the NovaGRC APIs.
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const winston = require('winston');
const mongoose = require('mongoose');

// Import routes (these will be implemented as APIs are migrated)
// const privacyManagementRoutes = require('./privacy-management/routes');
// const regulatoryComplianceRoutes = require('./regulatory-compliance/routes');
// const securityAssessmentRoutes = require('./security-assessment/routes');
// const controlTestingRoutes = require('./control-testing/routes');
// const esgRoutes = require('./esg/routes');
// const complianceAutomationRoutes = require('./compliance-automation/routes');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3002;

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'nova-grc-apis' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(morgan('dev'));

// MongoDB connection
const connectToMongoDB = async () => {
  if (process.env.NODE_ENV === 'test') {
    logger.info('Skipping MongoDB connection in test environment');
    return;
  }

  try {
    const mongoOptions = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4
    };

    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/nova-grc-apis';

    await mongoose.connect(mongoURI, mongoOptions);
    logger.info('Connected to MongoDB successfully');

    mongoose.connection.on('error', (err) => {
      logger.error('MongoDB connection error:', { error: err.message });
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB disconnected. Attempting to reconnect...');
    });

    mongoose.connection.on('reconnected', () => {
      logger.info('Reconnected to MongoDB');
    });

    process.on('SIGINT', async () => {
      try {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed through app termination');
        process.exit(0);
      } catch (err) {
        logger.error('Error closing MongoDB connection:', { error: err.message });
        process.exit(1);
      }
    });

  } catch (err) {
    logger.error('Failed to connect to MongoDB:', { error: err.message });
  }
};

// Initialize MongoDB connection
connectToMongoDB();

// Routes
app.get('/', (req, res) => {
  res.json({
    name: 'NovaGRC APIs',
    version: '1.0.0',
    description: 'A comprehensive suite of Governance, Risk, and Compliance APIs'
  });
});

// API Routes (these will be uncommented as APIs are migrated)
// app.use('/api/privacy/management', privacyManagementRoutes);
// app.use('/api/compliance', regulatoryComplianceRoutes);
// app.use('/api/security/assessment', securityAssessmentRoutes);
// app.use('/api/control/testing', controlTestingRoutes);
// app.use('/api/esg', esgRoutes);
// app.use('/api/compliance/automation', complianceAutomationRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: require('./package.json').version,
    mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

// Error handler
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { error: err.message, stack: err.stack });
  res.status(500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'production' ? 'An unexpected error occurred' : err.message
  });
});

// Start the server
app.listen(PORT, () => {
  logger.info(`NovaGRC APIs server running on port ${PORT}`);
  console.log(`NovaGRC APIs server running on http://localhost:${PORT}`);
});

module.exports = app;
