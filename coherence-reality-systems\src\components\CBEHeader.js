import React, { useState } from 'react';

const CBEHeader = ({ 
  currentUrl, 
  onNavigate, 
  onHome, 
  cbeMode, 
  setCbeMode, 
  consciousnessData 
}) => {
  const [urlInput, setUrlInput] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (urlInput.trim()) {
      onNavigate(urlInput.trim());
      setUrlInput('');
    }
  };

  const getCoherenceColor = (score) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getCoherenceIndicatorColor = (score) => {
    if (score >= 90) return 'bg-green-400';
    if (score >= 70) return 'bg-yellow-400';
    return 'bg-red-400';
  };

  const getConsciousnessStateEmoji = (state) => {
    switch (state) {
      case 'TRANSCENDENT': return '🌟';
      case 'DIVINE': return '✨';
      case 'CONSCIOUS': return '🧬';
      case 'AWAKENING': return '💫';
      default: return '🌌';
    }
  };

  return (
    <header className="border-b border-purple-500/20 bg-black/20 backdrop-blur-xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo & Title */}
          <div className="flex items-center space-x-4">
            <button 
              onClick={onHome}
              className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg flex items-center justify-center hover:scale-105 transition-transform"
            >
              <span className="text-white font-bold text-xl animate-pulse">🌌</span>
            </button>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                CBE Browser
              </h1>
              <p className="text-xs text-purple-300">
                Comphyological Browsing Engine v1.0
              </p>
            </div>
          </div>
          
          {/* Address Bar */}
          <div className="flex-1 max-w-2xl mx-8">
            <form onSubmit={handleSubmit} className="relative">
              <div className="flex items-center bg-gray-900/50 rounded-full px-4 py-3 border border-gray-600 hover:border-purple-400 transition-all">
                <div 
                  className={`w-3 h-3 rounded-full mr-3 animate-pulse ${getCoherenceIndicatorColor(consciousnessData.overall_consciousness)}`} 
                  title={`Consciousness: ${consciousnessData.overall_consciousness}%`} 
                />
                <input
                  type="text"
                  value={urlInput}
                  onChange={(e) => setUrlInput(e.target.value)}
                  placeholder="Enter consciousness intention or URL..."
                  className="flex-1 bg-transparent text-white placeholder-gray-400 outline-none font-mono text-sm"
                />
                <button
                  type="submit"
                  className="ml-2 px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded-full text-xs font-medium transition-colors"
                >
                  Navigate
                </button>
              </div>
            </form>
          </div>
          
          {/* Status Indicators */}
          <div className="flex items-center space-x-6">
            {/* Consciousness Level */}
            <div className="flex items-center space-x-2">
              <span className="text-blue-400">{getConsciousnessStateEmoji(consciousnessData.consciousness_state)}</span>
              <span className="text-sm text-gray-300">
                Ψᶜʰ: <span className={`font-mono ${getCoherenceColor(consciousnessData.overall_consciousness)}`}>
                  {consciousnessData.overall_consciousness}%
                </span>
              </span>
            </div>
            
            {/* Ψ-Snap Status */}
            <div className="flex items-center space-x-2">
              <span className="text-yellow-400">⚡</span>
              <span className="text-sm text-gray-300">
                Ψ-Snap: <span className={consciousnessData.psi_snap_active ? 'text-green-400' : 'text-red-400'}>
                  {consciousnessData.psi_snap_active ? 'ACTIVE' : 'INACTIVE'}
                </span>
              </span>
            </div>
            
            {/* Engine Status */}
            <div className="flex items-center space-x-2">
              <span className="text-purple-400">🔧</span>
              <span className="text-sm text-gray-300">
                Engines: <span className="text-green-400">9/9</span>
              </span>
            </div>
            
            {/* CBE Mode Toggle */}
            <button 
              onClick={() => setCbeMode(!cbeMode)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                cbeMode 
                  ? 'bg-purple-600 text-white shadow-lg hover:bg-purple-700' 
                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              <span className="text-purple-300">🌌</span>
              <span className="ml-2">{cbeMode ? 'CBE MODE' : 'STANDARD'}</span>
            </button>
          </div>
        </div>
        
        {/* Engine Status Bar */}
        {cbeMode && (
          <div className="pb-3">
            <div className="flex items-center justify-between text-xs">
              <div className="flex space-x-4">
                <span className="text-green-400">🧬 NERS: ACTIVE</span>
                <span className="text-green-400">💫 NEEE: ACTIVE</span>
                <span className="text-green-400">🤖 NEPI: ACTIVE</span>
                <span className="text-green-400">🧪 CHEMISTRY: ACTIVE</span>
                <span className="text-green-400">🔱 TRINITY: ACTIVE</span>
              </div>
              <div className="flex space-x-4">
                <span className="text-purple-300">Analysis: {consciousnessData.analysis_time}ms</span>
                <span className="text-purple-300">Threshold: 2847</span>
                <span className={consciousnessData.threat_level === 'LOW' ? 'text-green-400' : 'text-yellow-400'}>
                  🛡️ {consciousnessData.threat_level}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default CBEHeader;
