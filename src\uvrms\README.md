# Universal Vendor Risk Management System (UVRMS)

The Universal Vendor Risk Management System (UVRMS) is a comprehensive framework for assessing, monitoring, and managing vendor risks, including fourth-party risk management and advanced risk assessment methodologies.

## Overview

The UVRMS provides a flexible and extensible framework for managing vendor information, conducting assessments, identifying and mitigating risks, and continuously monitoring vendors. It helps organizations maintain a comprehensive view of their vendor ecosystem and associated risks, including fourth-party risks (vendors of your vendors).

## Key Features

- **Vendor Management**: Maintain detailed information about vendors, including services, categories, and contact information
- **Assessment Management**: Create and manage vendor assessments using customizable templates
- **Risk Management**: Identify, assess, and mitigate vendor risks
- **Continuous Monitoring**: Set up continuous monitoring of vendor risks with customizable alert thresholds
- **Advanced Risk Scoring**: Calculate vendor risk scores using multiple methodologies
- **Fourth-Party Risk Management**: Identify and manage risks from vendors of your vendors
- **Vendor Relationship Mapping**: Map relationships between vendors and fourth parties
- **Supply Chain Risk Visualization**: Visualize vendor supply chain risks
- **Inherited Risk Calculation**: Calculate risk inherited through vendor relationships

## Architecture

The UVRMS consists of several core components:

- **Vendor Manager**: Manages vendor information and profiles
- **Assessment Manager**: Manages vendor assessments and templates
- **Risk Manager**: Manages vendor risks and controls
- **Monitoring Manager**: Manages continuous monitoring of vendor risks
- **Relationship Manager**: Manages vendor relationships and fourth-party risks

## Advanced Risk Assessment Methodologies

UVRMS supports multiple risk assessment methodologies:

- **Inherent Risk Scoring**: Score based on inherent risk factors (data sensitivity, service criticality, etc.)
- **Residual Risk Scoring**: Score based on residual risk after controls are applied
- **Impact-Likelihood Scoring**: Score based on impact and likelihood of identified risks
- **Compliance-Based Scoring**: Score based on compliance with specific requirements
- **Industry-Specific Scoring**: Score based on industry-specific factors and benchmarks
- **Composite Risk Scoring**: Combine multiple scoring methodologies with weighted factors

## Usage

Here's a simple example of how to use the UVRMS:

```python
from uvrms import VendorManager, AssessmentManager, RiskManager, MonitoringManager

# Initialize the managers
vendor_manager = VendorManager()
assessment_manager = AssessmentManager()
risk_manager = RiskManager()
monitoring_manager = MonitoringManager()

# Create a vendor
vendor = vendor_manager.create_vendor({
    'name': 'Acme Corporation',
    'description': 'Provider of cloud services',
    'contact_info': {
        'email': '<EMAIL>',
        'phone': '************',
        'address': '123 Main St, Anytown, USA'
    },
    'services': ['cloud_storage', 'data_processing', 'analytics'],
    'categories': ['technology', 'data_services']
})

# Create an assessment for the vendor
assessment = assessment_manager.create_assessment(
    vendor['id'],
    'security_assessment',
    {
        'name': 'Security Assessment for Acme Corporation',
        'description': 'Assessment of Acme Corporation security controls'
    }
)

# Create a risk for the vendor
risk = risk_manager.create_risk({
    'vendor_id': vendor['id'],
    'category_id': 'security',
    'name': 'Data breach risk',
    'description': 'Risk of data breach due to inadequate security controls',
    'impact': 'high',
    'likelihood': 'medium',
    'status': 'identified'
})

# Create a monitoring configuration for the vendor
monitoring_config = monitoring_manager.create_monitoring_config({
    'vendor_id': vendor['id'],
    'name': 'Security monitoring for Acme Corporation',
    'description': 'Continuous monitoring of security indicators',
    'handler_id': 'security_monitoring',
    'parameters': {
        'check_ssl': True,
        'check_vulnerabilities': True
    },
    'frequency': 'daily',
    'alert_threshold': 'medium'
})
```

## Extending the Framework

### Adding a Custom Risk Category

```python
from uvrms import RiskManager

# Initialize the Risk Manager
risk_manager = RiskManager()

# Define a custom risk category
custom_category = {
    'id': 'custom_category',
    'name': 'Custom Risk Category',
    'description': 'A custom risk category',
    'impact_levels': {
        'low': 'Low impact description',
        'medium': 'Medium impact description',
        'high': 'High impact description'
    },
    'likelihood_levels': {
        'low': 'Low likelihood description',
        'medium': 'Medium likelihood description',
        'high': 'High likelihood description'
    }
}

# Register the custom risk category
risk_manager.register_risk_category(custom_category)
```

### Adding a Custom Assessment Template

```python
from uvrms import AssessmentManager

# Initialize the Assessment Manager
assessment_manager = AssessmentManager()

# Define a custom assessment template
custom_template = {
    'id': 'custom_assessment',
    'name': 'Custom Assessment',
    'description': 'A custom assessment template',
    'categories': ['custom'],
    'sections': [
        {
            'id': 'custom_section',
            'name': 'Custom Section',
            'description': 'A custom section',
            'questions': [
                {
                    'id': 'custom_question_1',
                    'text': 'Custom question 1?',
                    'type': 'yes_no',
                    'required': True
                },
                {
                    'id': 'custom_question_2',
                    'text': 'Custom question 2?',
                    'type': 'multiple_choice',
                    'options': ['Option 1', 'Option 2', 'Option 3'],
                    'required': True
                }
            ]
        }
    ]
}

# Register the custom assessment template
assessment_manager.register_assessment_template(custom_template)
```

### Adding a Custom Monitoring Handler

```python
from uvrms import MonitoringManager

# Initialize the Monitoring Manager
monitoring_manager = MonitoringManager()

# Define a custom monitoring handler
def custom_monitoring_handler(config):
    # Custom implementation to monitor vendor risks
    # ...
    return {
        'type': 'custom',
        'severity': 'medium',
        'findings': [
            {
                'id': 'CUSTOM-001',
                'description': 'Custom finding',
                'details': 'Details of the custom finding'
            }
        ]
    }

# Register the custom monitoring handler
monitoring_manager.register_monitoring_handler('custom_monitoring', custom_monitoring_handler)
```

### Adding a Custom Alert Handler

```python
from uvrms import MonitoringManager

# Initialize the Monitoring Manager
monitoring_manager = MonitoringManager()

# Define a custom alert handler
def custom_alert_handler(alert):
    # Custom implementation to handle alerts
    # ...
    print(f"Custom alert handler: {alert.get('id')}")

# Register the custom alert handler
monitoring_manager.register_alert_handler('custom', custom_alert_handler)
```

## Advanced Usage Examples

### Fourth-Party Risk Management

```python
from uvrms import VendorManager, RelationshipManager

# Initialize the managers
vendor_manager = VendorManager()
relationship_manager = RelationshipManager()

# Create vendors
primary_vendor = vendor_manager.create_vendor({
    'name': 'Primary Corp',
    'description': 'Our primary organization',
    'contact_info': {
        'email': '<EMAIL>'
    }
})

direct_vendor = vendor_manager.create_vendor({
    'name': 'Direct Vendor',
    'description': 'Direct vendor providing services',
    'contact_info': {
        'email': '<EMAIL>'
    }
})

fourth_party = vendor_manager.create_vendor({
    'name': 'Fourth Party',
    'description': 'Vendor of our direct vendor',
    'contact_info': {
        'email': '<EMAIL>'
    }
})

# Create relationships
direct_rel = relationship_manager.create_relationship({
    'from_vendor_id': primary_vendor['id'],
    'to_vendor_id': direct_vendor['id'],
    'type_id': 'service_provider',
    'description': 'Cloud services provider',
    'data_shared': ['PII', 'confidential'],
    'criticality': 'high'
})

fourth_rel = relationship_manager.create_relationship({
    'from_vendor_id': direct_vendor['id'],
    'to_vendor_id': fourth_party['id'],
    'type_id': 'technology_partner',
    'description': 'Database provider',
    'data_shared': ['confidential'],
    'criticality': 'medium'
})

# Get fourth-party vendors
fourth_parties = relationship_manager.get_fourth_parties(primary_vendor['id'])

# Generate vendor dependency graph
dependency_graph = relationship_manager.get_vendor_dependency_graph(
    primary_vendor['id'], max_depth=3)
```

### Advanced Risk Scoring

```python
from uvrms import (
    calculate_inherent_risk_score,
    calculate_residual_risk_score,
    calculate_impact_likelihood_score,
    calculate_composite_risk_score,
    get_default_inherent_risk_factors,
    get_default_composite_weights
)

# Get default risk factors and weights
inherent_risk_factors = get_default_inherent_risk_factors()
composite_weights = get_default_composite_weights()

# Calculate inherent risk score
inherent_score = calculate_inherent_risk_score(vendor, inherent_risk_factors)

# Calculate residual risk score (after controls)
residual_score = calculate_residual_risk_score(inherent_score, vendor_controls)

# Calculate impact-likelihood score
impact_likelihood_score = calculate_impact_likelihood_score(vendor_risks)

# Calculate composite risk score
scores = [inherent_score, residual_score, impact_likelihood_score]
composite_score = calculate_composite_risk_score(scores, composite_weights)

print(f"Vendor risk level: {composite_score['risk_level']}")
print(f"Overall risk score: {composite_score['overall_score']}")
```

### Inherited Risk Calculation

```python
from uvrms import RelationshipManager

# Initialize the relationship manager
relationship_manager = RelationshipManager()

# Calculate inherited risk from vendor relationships
inherited_risk = relationship_manager.calculate_inherited_risk(
    primary_vendor['id'], risk_scores)

print(f"Inherited risk level: {inherited_risk['risk_level']}")
print(f"Direct inherited risk: {inherited_risk['direct_inherited_risk']}")
print(f"Fourth-party inherited risk: {inherited_risk['fourth_party_inherited_risk']}")
```

## Demo Scripts

The UVRMS includes two demo scripts:

- **uvrms_demo.py**: Demonstrates basic UVRMS functionality
- **uvrms_advanced_demo.py**: Demonstrates advanced risk assessment and fourth-party risk management

To run the demo scripts:

```bash
# Run the basic demo
python src/uvrms_demo.py

# Run the advanced demo
python src/uvrms_advanced_demo.py
```

## Integration with Other NovaFuse Components

UVRMS can be integrated with other NovaFuse components:

- **UCWO**: Trigger vendor assessment workflows
- **UCTF**: Include vendor controls in compliance testing
- **UCVF**: Visualize vendor risk across the organization
- **UCECS**: Collect evidence from vendors for assessments

## License

Copyright © 2023-2025 NovaFuse. All rights reserved.
