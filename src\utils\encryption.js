/**
 * NovaFuse Universal API Connector - Encryption Utilities
 * 
 * This module provides utilities for encrypting and decrypting sensitive data.
 */

const crypto = require('crypto');
const { createLogger } = require('./logger');

const logger = createLogger('encryption');

// Encryption key (should be in environment variables in production)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'novafuse-uac-encryption-key-32chars';

// Initialization vector length
const IV_LENGTH = 16;

/**
 * Encrypt a value
 * 
 * @param {string} value - The value to encrypt
 * @returns {string} - The encrypted value
 */
function encrypt(value) {
  if (!value) {
    return value;
  }
  
  try {
    // Create initialization vector
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Create cipher
    const cipher = crypto.createCipheriv(
      'aes-256-cbc',
      Buffer.from(ENCRYPTION_KEY.padEnd(32).slice(0, 32)),
      iv
    );
    
    // Encrypt value
    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Return initialization vector and encrypted value
    return `${iv.toString('hex')}:${encrypted}`;
  } catch (error) {
    logger.error('Encryption error:', { error });
    throw new Error('Failed to encrypt value');
  }
}

/**
 * Decrypt a value
 * 
 * @param {string} value - The value to decrypt
 * @returns {string} - The decrypted value
 */
function decrypt(value) {
  if (!value || !value.includes(':')) {
    return value;
  }
  
  try {
    // Split initialization vector and encrypted value
    const [ivHex, encryptedHex] = value.split(':');
    
    // Create decipher
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipheriv(
      'aes-256-cbc',
      Buffer.from(ENCRYPTION_KEY.padEnd(32).slice(0, 32)),
      iv
    );
    
    // Decrypt value
    let decrypted = decipher.update(encryptedHex, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    logger.error('Decryption error:', { error });
    throw new Error('Failed to decrypt value');
  }
}

/**
 * Hash a value
 * 
 * @param {string} value - The value to hash
 * @returns {string} - The hashed value
 */
function hash(value) {
  if (!value) {
    return value;
  }
  
  try {
    return crypto.createHash('sha256').update(value).digest('hex');
  } catch (error) {
    logger.error('Hashing error:', { error });
    throw new Error('Failed to hash value');
  }
}

/**
 * Generate a secure random string
 * 
 * @param {number} length - The length of the string
 * @returns {string} - The random string
 */
function generateRandomString(length = 32) {
  try {
    return crypto.randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length);
  } catch (error) {
    logger.error('Random string generation error:', { error });
    throw new Error('Failed to generate random string');
  }
}

module.exports = {
  encrypt,
  decrypt,
  hash,
  generateRandomString
};
