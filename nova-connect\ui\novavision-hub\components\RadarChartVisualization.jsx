/**
 * RadarChartVisualization Component
 * 
 * A component for visualizing multi-dimensional data using a radar chart.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme/ThemeContext';
import { usePerformance } from '../performance/usePerformance';

/**
 * RadarChartVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Chart data
 * @param {Array} props.data.labels - Axis labels
 * @param {Array} props.data.datasets - Datasets
 * @param {Object} [props.options] - Chart options
 * @param {boolean} [props.options.showLegend=true] - Whether to show the legend
 * @param {boolean} [props.options.showLabels=true] - Whether to show axis labels
 * @param {boolean} [props.options.showGrid=true] - Whether to show the grid
 * @param {number} [props.options.gridLevels=5] - Number of grid levels
 * @param {boolean} [props.options.fillArea=true] - Whether to fill the area
 * @param {number} [props.options.fillOpacity=0.2] - Fill opacity
 * @param {boolean} [props.options.showPoints=true] - Whether to show data points
 * @param {boolean} [props.options.showTooltips=true] - Whether to show tooltips
 * @param {boolean} [props.options.animate=true] - Whether to animate the chart
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} RadarChartVisualization component
 */
const RadarChartVisualization = ({
  data,
  options = {},
  className = '',
  style = {}
}) => {
  const { measureOperation } = usePerformance('RadarChartVisualization');
  const { theme } = useTheme();
  
  // Refs
  const canvasRef = useRef(null);
  const tooltipRef = useRef(null);
  
  // State
  const [hoveredPoint, setHoveredPoint] = useState(null);
  
  // Default options
  const {
    showLegend = true,
    showLabels = true,
    showGrid = true,
    gridLevels = 5,
    fillArea = true,
    fillOpacity = 0.2,
    showPoints = true,
    showTooltips = true,
    animate = true
  } = options;
  
  // Draw chart
  useEffect(() => {
    if (!canvasRef.current || !data || !data.labels || !data.datasets) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);
    
    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);
    
    // Calculate chart dimensions
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const radius = Math.min(centerX, centerY) * 0.8;
    
    // Draw chart
    measureOperation('drawChart', () => {
      // Draw grid
      if (showGrid) {
        drawGrid(ctx, centerX, centerY, radius, data.labels.length, gridLevels);
      }
      
      // Draw axis labels
      if (showLabels) {
        drawAxisLabels(ctx, centerX, centerY, radius, data.labels);
      }
      
      // Draw datasets
      drawDatasets(ctx, centerX, centerY, radius, data);
      
      // Draw legend
      if (showLegend) {
        drawLegend(ctx, rect.width, data.datasets);
      }
    });
  }, [data, options, theme, measureOperation]);
  
  // Handle mouse move
  const handleMouseMove = (e) => {
    if (!showTooltips || !canvasRef.current || !data || !data.labels || !data.datasets) return;
    
    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Calculate chart dimensions
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const radius = Math.min(centerX, centerY) * 0.8;
    
    // Check if mouse is over a data point
    let closestPoint = null;
    let closestDistance = Infinity;
    
    data.datasets.forEach((dataset, datasetIndex) => {
      dataset.data.forEach((value, valueIndex) => {
        // Calculate point position
        const angle = (Math.PI * 2 * valueIndex) / data.labels.length - Math.PI / 2;
        const normalizedValue = value / getMaxValue(data);
        const pointX = centerX + radius * normalizedValue * Math.cos(angle);
        const pointY = centerY + radius * normalizedValue * Math.sin(angle);
        
        // Calculate distance to mouse
        const distance = Math.sqrt((x - pointX) ** 2 + (y - pointY) ** 2);
        
        // Check if this point is closer than the current closest
        if (distance < closestDistance && distance < 20) {
          closestDistance = distance;
          closestPoint = {
            datasetIndex,
            valueIndex,
            value,
            x: pointX,
            y: pointY
          };
        }
      });
    });
    
    setHoveredPoint(closestPoint);
  };
  
  // Handle mouse leave
  const handleMouseLeave = () => {
    setHoveredPoint(null);
  };
  
  // Draw grid
  const drawGrid = (ctx, centerX, centerY, radius, axisCount, levels) => {
    ctx.save();
    
    // Draw circular grid lines
    ctx.strokeStyle = theme.colors.divider;
    ctx.setLineDash([2, 2]);
    
    for (let i = 1; i <= levels; i++) {
      const levelRadius = (radius * i) / levels;
      
      ctx.beginPath();
      ctx.arc(centerX, centerY, levelRadius, 0, Math.PI * 2);
      ctx.stroke();
    }
    
    // Draw axis lines
    ctx.setLineDash([]);
    
    for (let i = 0; i < axisCount; i++) {
      const angle = (Math.PI * 2 * i) / axisCount - Math.PI / 2;
      
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(
        centerX + radius * Math.cos(angle),
        centerY + radius * Math.sin(angle)
      );
      ctx.stroke();
    }
    
    ctx.restore();
  };
  
  // Draw axis labels
  const drawAxisLabels = (ctx, centerX, centerY, radius, labels) => {
    ctx.save();
    
    ctx.font = `${theme.typography.body2.fontSize}px ${theme.typography.fontFamily}`;
    ctx.fillStyle = theme.colors.textSecondary;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    labels.forEach((label, i) => {
      const angle = (Math.PI * 2 * i) / labels.length - Math.PI / 2;
      const x = centerX + (radius + 20) * Math.cos(angle);
      const y = centerY + (radius + 20) * Math.sin(angle);
      
      // Adjust text alignment based on position
      if (angle === -Math.PI / 2) {
        ctx.textAlign = 'center';
        ctx.textBaseline = 'bottom';
      } else if (angle === Math.PI / 2) {
        ctx.textAlign = 'center';
        ctx.textBaseline = 'top';
      } else if (angle === 0) {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
      } else if (angle === Math.PI) {
        ctx.textAlign = 'right';
        ctx.textBaseline = 'middle';
      } else if (angle > -Math.PI / 2 && angle < Math.PI / 2) {
        ctx.textAlign = 'left';
        ctx.textBaseline = 'middle';
      } else {
        ctx.textAlign = 'right';
        ctx.textBaseline = 'middle';
      }
      
      ctx.fillText(label, x, y);
    });
    
    ctx.restore();
  };
  
  // Draw datasets
  const drawDatasets = (ctx, centerX, centerY, radius, data) => {
    const maxValue = getMaxValue(data);
    
    data.datasets.forEach((dataset, datasetIndex) => {
      ctx.save();
      
      // Set styles
      ctx.strokeStyle = dataset.borderColor || getDefaultColor(datasetIndex);
      ctx.lineWidth = dataset.borderWidth || 2;
      
      // Draw dataset path
      ctx.beginPath();
      
      dataset.data.forEach((value, valueIndex) => {
        const angle = (Math.PI * 2 * valueIndex) / data.labels.length - Math.PI / 2;
        const normalizedValue = value / maxValue;
        const x = centerX + radius * normalizedValue * Math.cos(angle);
        const y = centerY + radius * normalizedValue * Math.sin(angle);
        
        if (valueIndex === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });
      
      // Close the path
      const firstAngle = -Math.PI / 2;
      const firstValue = dataset.data[0] / maxValue;
      const firstX = centerX + radius * firstValue * Math.cos(firstAngle);
      const firstY = centerY + radius * firstValue * Math.sin(firstAngle);
      ctx.lineTo(firstX, firstY);
      
      // Fill area
      if (fillArea) {
        ctx.fillStyle = dataset.backgroundColor || getDefaultColor(datasetIndex, fillOpacity);
        ctx.fill();
      }
      
      // Stroke path
      ctx.stroke();
      
      // Draw points
      if (showPoints) {
        dataset.data.forEach((value, valueIndex) => {
          const angle = (Math.PI * 2 * valueIndex) / data.labels.length - Math.PI / 2;
          const normalizedValue = value / maxValue;
          const x = centerX + radius * normalizedValue * Math.cos(angle);
          const y = centerY + radius * normalizedValue * Math.sin(angle);
          
          ctx.beginPath();
          ctx.arc(x, y, 4, 0, Math.PI * 2);
          ctx.fillStyle = dataset.pointBackgroundColor || dataset.borderColor || getDefaultColor(datasetIndex);
          ctx.fill();
          ctx.strokeStyle = theme.colors.background;
          ctx.lineWidth = 1;
          ctx.stroke();
        });
      }
      
      ctx.restore();
    });
  };
  
  // Draw legend
  const drawLegend = (ctx, width, datasets) => {
    ctx.save();
    
    const legendX = 20;
    let legendY = 20;
    const itemHeight = 20;
    const itemWidth = 30;
    const textMargin = 10;
    
    ctx.font = `${theme.typography.body2.fontSize}px ${theme.typography.fontFamily}`;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    
    datasets.forEach((dataset, i) => {
      // Draw color box
      ctx.fillStyle = dataset.backgroundColor || getDefaultColor(i, fillOpacity);
      ctx.strokeStyle = dataset.borderColor || getDefaultColor(i);
      ctx.lineWidth = 2;
      
      ctx.fillRect(legendX, legendY, itemWidth, itemHeight);
      ctx.strokeRect(legendX, legendY, itemWidth, itemHeight);
      
      // Draw label
      ctx.fillStyle = theme.colors.textPrimary;
      ctx.fillText(dataset.label, legendX + itemWidth + textMargin, legendY + itemHeight / 2);
      
      legendY += itemHeight + 10;
    });
    
    ctx.restore();
  };
  
  // Get max value
  const getMaxValue = (data) => {
    let max = 0;
    
    data.datasets.forEach(dataset => {
      dataset.data.forEach(value => {
        max = Math.max(max, value);
      });
    });
    
    return max;
  };
  
  // Get default color
  const getDefaultColor = (index, opacity = 1) => {
    const colors = [
      `rgba(54, 162, 235, ${opacity})`,   // Blue
      `rgba(255, 99, 132, ${opacity})`,   // Red
      `rgba(75, 192, 192, ${opacity})`,   // Green
      `rgba(255, 159, 64, ${opacity})`,   // Orange
      `rgba(153, 102, 255, ${opacity})`,  // Purple
      `rgba(255, 205, 86, ${opacity})`,   // Yellow
      `rgba(201, 203, 207, ${opacity})`   // Grey
    ];
    
    return colors[index % colors.length];
  };
  
  return (
    <div
      className={`relative ${className}`}
      style={style}
      data-testid="radar-chart-visualization"
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      />
      
      {/* Tooltip */}
      {hoveredPoint && showTooltips && (
        <div
          ref={tooltipRef}
          className="absolute z-10 bg-surface border border-divider rounded-md shadow-lg p-2 text-sm pointer-events-none"
          style={{
            left: `${hoveredPoint.x}px`,
            top: `${hoveredPoint.y - 40}px`,
            transform: 'translateX(-50%)'
          }}
        >
          <div className="font-medium text-textPrimary">
            {data.datasets[hoveredPoint.datasetIndex].label}
          </div>
          <div className="text-textSecondary">
            {data.labels[hoveredPoint.valueIndex]}: {hoveredPoint.value}
          </div>
        </div>
      )}
    </div>
  );
};

RadarChartVisualization.propTypes = {
  data: PropTypes.shape({
    labels: PropTypes.arrayOf(PropTypes.string).isRequired,
    datasets: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string.isRequired,
        data: PropTypes.arrayOf(PropTypes.number).isRequired,
        backgroundColor: PropTypes.string,
        borderColor: PropTypes.string,
        borderWidth: PropTypes.number,
        pointBackgroundColor: PropTypes.string
      })
    ).isRequired
  }).isRequired,
  options: PropTypes.shape({
    showLegend: PropTypes.bool,
    showLabels: PropTypes.bool,
    showGrid: PropTypes.bool,
    gridLevels: PropTypes.number,
    fillArea: PropTypes.bool,
    fillOpacity: PropTypes.number,
    showPoints: PropTypes.bool,
    showTooltips: PropTypes.bool,
    animate: PropTypes.bool
  }),
  className: PropTypes.string,
  style: PropTypes.object
};

export default RadarChartVisualization;
