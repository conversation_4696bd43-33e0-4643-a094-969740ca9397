.task-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.task-form h3 {
  margin: 0 0 1.25rem 0;
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.25rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  font-size: 0.9rem;
  color: #4a5568;
}

.form-control {
  padding: 0.6rem 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: #fff;
}

.form-control:focus {
  outline: none;
  border-color: #8a2be2;
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.2);
}

.form-control::placeholder {
  color: #a0aec0;
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.form-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}

.submit-button {
  background-color: #8a2be2;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.submit-button:hover:not(:disabled) {
  background-color: #7b1fa2;
  transform: translateY(-1px);
}

.submit-button:active:not(:disabled) {
  transform: translateY(0);
}

.submit-button:disabled {
  background-color: #b39ddb;
  cursor: not-allowed;
  opacity: 0.8;
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.submit-button:disabled::after {
  content: '';
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s ease-in-out infinite;
  margin-left: 0.5rem;
}

/* Custom select styling */
select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1em;
  padding-right: 2.5rem;
}

/* Checkbox and radio button styling */
input[type="checkbox"],
input[type="radio"] {
  margin-right: 0.5rem;
}

/* Focus states for accessibility */
*:focus-visible {
  outline: 2px solid #8a2be2;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Error state */
.form-group.error .form-control {
  border-color: #e53e3e;
}

.error-message {
  color: #e53e3e;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .submit-button {
    width: 100%;
  }
}
