import React from 'react';
import { motion } from 'framer-motion';

// Triadic Card Element
export const TriadicCard = ({ children, title, icon, psi, phi, kappa }) => (
  <motion.div 
    className="card"
    whileHover={{ scale: 1.02 }}
    transition={{ type: "spring", stiffness: 300, damping: 20 }}
  >
    <div className="card-header">
      {icon && <div className="card-icon" style={{ background: `linear-gradient(45deg, ${psi}, ${phi})` }}>{icon}</div>}
      <h3>{title}</h3>
    </div>
    <div className="card-content">
      {children}
    </div>
    <div className="card-footer">
      <div className="triadic-metrics">
        <div className="metric" style={{ color: psi }}>
          Ψ: {psi}
        </div>
        <div className="metric" style={{ color: phi }}>
          φ: {phi}
        </div>
        <div className="metric" style={{ color: kappa }}>
          κ: {kappa}
        </div>
      </div>
    </div>
  </motion.div>
);

// Consciousness Meter
export const ConsciousnessMeter = ({ level, label }) => (
  <motion.div 
    className="consciousness-meter"
    initial={{ width: '0%' }}
    animate={{ width: `${level}%` }}
    transition={{ duration: 1 }}
  >
    <div className="meter-label">{label}</div>
    <div className="meter-value" style={{ 
      background: `linear-gradient(90deg, 
        ${level < 33 ? 'var(--consciousness-low)' : 
        level < 66 ? 'var(--consciousness-medium)' : 
        'var(--consciousness-high)'} 0%,
        rgba(255, 255, 255, 0.1) 100%)
      `}}>
      {level}%
    </div>
  </motion.div>
);

// Quantum Toggle
export const QuantumToggle = ({ active, onToggle }) => (
  <motion.div 
    className="quantum-toggle"
    whileHover={{ scale: 1.1 }}
    onClick={onToggle}
  >
    <div className={`toggle-button ${active ? 'active' : 'inactive'}`}>
      <div className="toggle-state" style={{ 
        background: active ? 'var(--quantum-active)' : 'var(--quantum-inactive)'
      }}>
        {active ? '🟢' : '🔴'}
      </div>
    </div>
  </motion.div>
);

// Triadic Chart
export const TriadicChart = ({ data }) => (
  <motion.div 
    className="triadic-chart"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.5 }}
  >
    <div className="chart-container">
      <div className="psi-axis" style={{ background: 'var(--psi-color)' }}>
        <span>Ψ</span>
        <div className="axis-value" style={{ height: `${data.psi}%` }}></div>
      </div>
      <div className="phi-axis" style={{ background: 'var(--phi-color)' }}>
        <span>φ</span>
        <div className="axis-value" style={{ height: `${data.phi}%` }}></div>
      </div>
      <div className="kappa-axis" style={{ background: 'var(--kappa-color)' }}>
        <span>κ</span>
        <div className="axis-value" style={{ height: `${data.kappa}%` }}></div>
      </div>
    </div>
  </motion.div>
);

// Ethical Impact Map
export const EthicalImpactMap = ({ data }) => (
  <motion.div 
    className="ethical-map"
    initial={{ scale: 0.95 }}
    animate={{ scale: 1 }}
    transition={{ duration: 0.5 }}
  >
    <div className="map-container">
      <div className="impact-grid">
        {data.map((impact, index) => (
          <div 
            key={index} 
            className="impact-cell"
            style={{
              background: `linear-gradient(45deg, 
                ${impact.positive ? 'var(--consciousness-high)' : 'var(--consciousness-low)'} 0%,
                rgba(255, 255, 255, 0.1) 100%)`
            }}
          >
            <div className="impact-value">
              {impact.value}
            </div>
          </div>
        ))}
      </div>
    </div>
  </motion.div>
);
