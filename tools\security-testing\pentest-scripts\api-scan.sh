#!/bin/bash
# API Security Scanning Script for NovaFuse
# This script performs comprehensive API security testing

# Configuration
TARGET_URL=${1:-"http://novafuse-api:3000"}
API_SPEC=${2:-"/pentest/api-specs/openapi.json"}
OUTPUT_DIR=${3:-"/pentest/reports"}
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Create output directory
mkdir -p $OUTPUT_DIR

echo "==== NovaFuse API Security Scan ===="
echo "Target URL: $TARGET_URL"
echo "API Specification: $API_SPEC"
echo "Output Directory: $OUTPUT_DIR"
echo "Timestamp: $TIMESTAMP"
echo "====================================="

# Function to run a command and log its output
run_command() {
  local cmd="$1"
  local description="$2"
  local output_file="$3"
  
  echo "Running: $description"
  echo "Command: $cmd"
  
  eval "$cmd" | tee "$output_file"
  
  if [ ${PIPESTATUS[0]} -eq 0 ]; then
    echo "✅ $description completed successfully"
  else
    echo "❌ $description failed"
  fi
  
  echo "Output saved to: $output_file"
  echo "-----------------------------------"
}

# 1. Run OWASP ZAP API Scan
run_command "zap-api-scan.py -t $TARGET_URL -f openapi -r $OUTPUT_DIR/zap-api-scan-$TIMESTAMP.html" \
  "OWASP ZAP API Scan" \
  "$OUTPUT_DIR/zap-api-scan-$TIMESTAMP.log"

# 2. Run SQLMap on API endpoints
run_command "sqlmap --url=$TARGET_URL/api/v1/users?id=1 --batch --level=5 --risk=3" \
  "SQLMap Scan" \
  "$OUTPUT_DIR/sqlmap-scan-$TIMESTAMP.log"

# 3. Run Nikto Web Scanner
run_command "nikto -h $TARGET_URL -output $OUTPUT_DIR/nikto-scan-$TIMESTAMP.html" \
  "Nikto Web Scan" \
  "$OUTPUT_DIR/nikto-scan-$TIMESTAMP.log"

# 4. Run Wfuzz for API fuzzing
run_command "wfuzz -c -z file,/usr/share/wordlists/dirb/common.txt --hc 404 $TARGET_URL/api/FUZZ" \
  "Wfuzz API Path Fuzzing" \
  "$OUTPUT_DIR/wfuzz-paths-$TIMESTAMP.log"

# 5. Run JWT token testing if applicable
if [ -f "/pentest/scripts/jwt-test.py" ]; then
  run_command "python3 /pentest/scripts/jwt-test.py --url $TARGET_URL" \
    "JWT Token Security Testing" \
    "$OUTPUT_DIR/jwt-test-$TIMESTAMP.log"
fi

# 6. Run API contract testing with Dredd
if [ -f "$API_SPEC" ]; then
  run_command "dredd $API_SPEC $TARGET_URL --output=$OUTPUT_DIR/dredd-$TIMESTAMP.html" \
    "API Contract Testing" \
    "$OUTPUT_DIR/dredd-$TIMESTAMP.log"
fi

# Generate summary report
echo "Generating summary report..."
cat > "$OUTPUT_DIR/summary-$TIMESTAMP.html" << EOF
<!DOCTYPE html>
<html>
<head>
  <title>NovaFuse API Security Scan Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1 { color: #0A84FF; }
    .section { margin-bottom: 20px; }
    .result { margin-left: 20px; }
    .timestamp { color: #666; font-size: 0.8em; }
    .success { color: green; }
    .failure { color: red; }
  </style>
</head>
<body>
  <h1>NovaFuse API Security Scan Report</h1>
  <div class="timestamp">Generated on: $(date)</div>
  
  <div class="section">
    <h2>Scan Configuration</h2>
    <div class="result">Target URL: $TARGET_URL</div>
    <div class="result">API Specification: $API_SPEC</div>
    <div class="result">Timestamp: $TIMESTAMP</div>
  </div>
  
  <div class="section">
    <h2>Scan Results</h2>
    <div class="result"><a href="zap-api-scan-$TIMESTAMP.html">OWASP ZAP API Scan Results</a></div>
    <div class="result"><a href="sqlmap-scan-$TIMESTAMP.log">SQLMap Scan Results</a></div>
    <div class="result"><a href="nikto-scan-$TIMESTAMP.html">Nikto Web Scan Results</a></div>
    <div class="result"><a href="wfuzz-paths-$TIMESTAMP.log">Wfuzz API Path Fuzzing Results</a></div>
    <div class="result"><a href="jwt-test-$TIMESTAMP.log">JWT Token Security Testing Results</a></div>
    <div class="result"><a href="dredd-$TIMESTAMP.html">API Contract Testing Results</a></div>
  </div>
</body>
</html>
EOF

echo "✅ Security scan completed successfully"
echo "Summary report saved to: $OUTPUT_DIR/summary-$TIMESTAMP.html"
