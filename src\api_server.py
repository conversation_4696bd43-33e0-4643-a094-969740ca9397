"""
API Server for the Universal Compliance Intelligence Architecture (UCIA).

This module implements a simple API server that exposes the UCIA functionality
through a RESTful API.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from ucia import UCIA

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize the UCIA
# Use a small model for testing; in production, use the full model
ucia = UCIA(model_name="gpt2")

# Create the FastAPI app
app = FastAPI(
    title="Universal Compliance Intelligence Architecture API",
    description="API for the Universal Compliance Intelligence Architecture (UCIA)",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Define API models
class QueryRequest(BaseModel):
    query: str = Field(..., description="The compliance query")
    user_context: Optional[Dict[str, Any]] = Field(None, description="Context information about the user and their organization")

class QueryResponse(BaseModel):
    response_text: str = Field(..., description="The response text")
    confidence: float = Field(..., description="The confidence score for the response")
    citations: List[Dict[str, Any]] = Field(..., description="Citations supporting the response")
    frameworks: List[str] = Field(..., description="Frameworks relevant to the query")
    concepts: List[str] = Field(..., description="Concepts relevant to the query")
    common_themes: Optional[List[str]] = Field(None, description="Common themes across frameworks")
    differences: Optional[List[Dict[str, Any]]] = Field(None, description="Differences across frameworks")
    follow_up_questions: Optional[List[str]] = Field(None, description="Suggested follow-up questions")

class ModuleMetadata(BaseModel):
    id: str = Field(..., description="The module identifier")
    name: str = Field(..., description="The module name")
    version: str = Field(..., description="The module version")
    description: str = Field(..., description="The module description")
    effective_date: str = Field(..., description="The effective date of the regulation")
    jurisdictions: List[str] = Field(..., description="The jurisdictions where the regulation applies")
    categories: List[str] = Field(..., description="The categories of compliance covered by the regulation")

class RequirementRequest(BaseModel):
    concept: str = Field(..., description="The concept identifier")
    framework_id: Optional[str] = Field(None, description="Optional framework to filter by")

class MappingRequest(BaseModel):
    source_framework: str = Field(..., description="The source framework identifier")
    target_framework: str = Field(..., description="The target framework identifier")

class ConceptRequest(BaseModel):
    concept_path: List[str] = Field(..., description="The path to the concept")

# Define API endpoints
@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """
    Process a compliance query and generate a response.
    """
    try:
        response = ucia.process_query(request.query, request.user_context)
        return response
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/modules/active", response_model=List[ModuleMetadata])
async def get_active_modules():
    """
    Get metadata for all active modules.
    """
    try:
        return ucia.get_active_modules()
    except Exception as e:
        logger.error(f"Error getting active modules: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/modules/all", response_model=List[ModuleMetadata])
async def get_all_modules():
    """
    Get metadata for all registered modules.
    """
    try:
        return ucia.get_all_modules()
    except Exception as e:
        logger.error(f"Error getting all modules: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/modules/activate/{module_id}")
async def activate_module(module_id: str):
    """
    Activate a compliance framework module.
    """
    try:
        result = ucia.activate_module(module_id)
        if result:
            return {"status": "success", "message": f"Module {module_id} activated"}
        else:
            raise HTTPException(status_code=404, detail=f"Module {module_id} not found or already active")
    except Exception as e:
        logger.error(f"Error activating module: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/modules/deactivate/{module_id}")
async def deactivate_module(module_id: str):
    """
    Deactivate a compliance framework module.
    """
    try:
        result = ucia.deactivate_module(module_id)
        if result:
            return {"status": "success", "message": f"Module {module_id} deactivated"}
        else:
            raise HTTPException(status_code=404, detail=f"Module {module_id} not found or not active")
    except Exception as e:
        logger.error(f"Error deactivating module: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/requirements", response_model=Dict[str, List[Dict[str, Any]]])
async def get_related_requirements(request: RequirementRequest):
    """
    Get requirements related to a concept across frameworks.
    """
    try:
        return ucia.get_related_requirements(request.concept, request.framework_id)
    except Exception as e:
        logger.error(f"Error getting related requirements: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/mappings", response_model=Dict[str, List[str]])
async def get_framework_mappings(request: MappingRequest):
    """
    Get mappings between two frameworks.
    """
    try:
        return ucia.get_framework_mappings(request.source_framework, request.target_framework)
    except Exception as e:
        logger.error(f"Error getting framework mappings: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/concepts", response_model=Dict[str, Any])
async def get_concept_hierarchy():
    """
    Get the complete concept hierarchy from the ontology.
    """
    try:
        return ucia.get_concept_hierarchy()
    except Exception as e:
        logger.error(f"Error getting concept hierarchy: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/concepts/children", response_model=List[List[str]])
async def get_child_concepts(request: ConceptRequest):
    """
    Get the child concepts of a concept.
    """
    try:
        return ucia.get_child_concepts(request.concept_path)
    except Exception as e:
        logger.error(f"Error getting child concepts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Error handling
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."}
    )

if __name__ == "__main__":
    import uvicorn
    
    # Get port from environment variable or use default
    port = int(os.environ.get("PORT", 8000))
    
    # Run the server
    uvicorn.run("api_server:app", host="0.0.0.0", port=port, reload=True)
