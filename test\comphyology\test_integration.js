/**
 * Comphyology Integration Test
 * 
 * This script tests the integration between Comphyology visualizations and
 * NovaFuse components (NovaShield, NovaTrack, and NovaCore).
 */

const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');
const { novaVision } = require('../../src/novavision');
const { 
  ComphyologyRealTimeDashboard,
  NovaShieldConnector,
  NovaTrackConnector,
  NovaCoreConnector
} = require('../../src/comphyology/exports');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../comphyology_test_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

// Mock NovaFuse components
const { 
  NovaShieldMock, 
  NovaTrackMock, 
  NovaCoreMock 
} = require('./mocks/nova_components');

/**
 * Test data flow from NovaShield to Quantum Phase Space visualization
 */
async function testNovaShieldIntegration() {
  console.log('\n=== Testing NovaShield Integration ===');
  
  // Create mock NovaShield
  const novaShield = new NovaShieldMock();
  
  // Create connector
  const connector = new NovaShieldConnector({
    novaShield,
    enableLogging: true,
    pollingInterval: 1000
  });
  
  // Set up event listener
  let dataReceived = false;
  let transformedData = null;
  
  connector.on('data', (data) => {
    dataReceived = true;
    console.log('Received data from NovaShield:', data.type);
  });
  
  // Connect to NovaShield
  await connector.connect();
  
  // Start polling
  await connector.startPolling();
  
  // Generate some test data
  console.log('Generating test threats...');
  for (let i = 0; i < 5; i++) {
    novaShield.generateRandomThreat();
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Wait for data to be processed
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Stop polling
  await connector.stopPolling();
  
  // Disconnect
  await connector.disconnect();
  
  // Check results
  console.log('Data received:', dataReceived);
  console.log('Latest data:', connector.getLatestData());
  
  return {
    success: dataReceived,
    dataPoints: connector.getAllData().length
  };
}

/**
 * Test data flow from NovaTrack to Morphological Resonance visualization
 */
async function testNovaTrackIntegration() {
  console.log('\n=== Testing NovaTrack Integration ===');
  
  // Create mock NovaTrack
  const novaTrack = new NovaTrackMock();
  
  // Create connector
  const connector = new NovaTrackConnector({
    novaTrack,
    enableLogging: true,
    pollingInterval: 1000
  });
  
  // Set up event listener
  let dataReceived = false;
  
  connector.on('data', (data) => {
    dataReceived = true;
    console.log('Received data from NovaTrack:', data.name);
  });
  
  // Connect to NovaTrack
  await connector.connect();
  
  // Start polling
  await connector.startPolling();
  
  // Generate some test data
  console.log('Generating test regulations...');
  for (let i = 0; i < 5; i++) {
    novaTrack.generateRandomRegulation();
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Wait for data to be processed
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Stop polling
  await connector.stopPolling();
  
  // Disconnect
  await connector.disconnect();
  
  // Check results
  console.log('Data received:', dataReceived);
  console.log('Latest data:', connector.getLatestData());
  
  return {
    success: dataReceived,
    dataPoints: connector.getAllData().length
  };
}

/**
 * Test data flow from NovaCore to Ethical Tensor visualization
 */
async function testNovaCoreIntegration() {
  console.log('\n=== Testing NovaCore Integration ===');
  
  // Create mock NovaCore
  const novaCore = new NovaCoreMock();
  
  // Create connector
  const connector = new NovaCoreConnector({
    novaCore,
    enableLogging: true,
    pollingInterval: 1000
  });
  
  // Set up event listener
  let dataReceived = false;
  
  connector.on('data', (data) => {
    dataReceived = true;
    console.log('Received data from NovaCore:', data.type);
  });
  
  // Connect to NovaCore
  await connector.connect();
  
  // Start polling
  await connector.startPolling();
  
  // Generate some test data
  console.log('Generating test decisions...');
  for (let i = 0; i < 5; i++) {
    novaCore.generateRandomDecision();
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // Wait for data to be processed
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Stop polling
  await connector.stopPolling();
  
  // Disconnect
  await connector.disconnect();
  
  // Check results
  console.log('Data received:', dataReceived);
  console.log('Latest data:', connector.getLatestData());
  
  return {
    success: dataReceived,
    dataPoints: connector.getAllData().length
  };
}

/**
 * Test full integration with real-time dashboard
 */
async function testFullIntegration() {
  console.log('\n=== Testing Full Integration ===');
  
  // Create mock components
  const novaShield = new NovaShieldMock();
  const novaTrack = new NovaTrackMock();
  const novaCore = new NovaCoreMock();
  
  // Create dashboard
  const dashboard = new ComphyologyRealTimeDashboard({
    novaVision,
    novaShield,
    novaTrack,
    novaCore,
    enableLogging: true,
    updateInterval: 500
  });
  
  // Wait for initial schemas to be generated
  console.log('Generating initial schemas...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Start real-time updates
  console.log('Starting real-time updates...');
  await dashboard.start();
  
  // Generate random data
  console.log('Generating random data...');
  const startTime = performance.now();
  const dataGenerationInterval = setInterval(() => {
    novaShield.generateRandomThreat();
    novaTrack.generateRandomRegulation();
    novaCore.generateRandomDecision();
  }, 100);
  
  // Run for 5 seconds
  console.log('Running for 5 seconds...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Stop data generation
  clearInterval(dataGenerationInterval);
  
  // Stop real-time updates
  console.log('Stopping real-time updates...');
  await dashboard.stop();
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  // Get dashboard schema
  const dashboardSchema = dashboard.getDashboardSchema();
  
  // Save dashboard schema to file
  const schemaFile = path.join(RESULTS_DIR, 'integration_test_dashboard_schema.json');
  fs.writeFileSync(schemaFile, JSON.stringify(dashboardSchema, null, 2));
  
  console.log(`Dashboard schema saved to: ${schemaFile}`);
  console.log(`Test duration: ${duration.toFixed(2)}ms`);
  
  return {
    success: true,
    duration,
    schemaFile
  };
}

/**
 * Run all integration tests
 */
async function runIntegrationTests() {
  console.log('=== Comphyology Integration Tests ===');
  
  const results = {
    novaShield: await testNovaShieldIntegration(),
    novaTrack: await testNovaTrackIntegration(),
    novaCore: await testNovaCoreIntegration(),
    fullIntegration: await testFullIntegration()
  };
  
  // Save results to file
  const resultFile = path.join(RESULTS_DIR, `integration_test_results_${new Date().toISOString().replace(/:/g, '-')}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(results, null, 2));
  
  console.log(`\nResults saved to ${resultFile}`);
  
  // Print summary
  console.log('\n=== Integration Test Summary ===');
  console.log(`NovaShield Integration: ${results.novaShield.success ? 'PASS' : 'FAIL'} (${results.novaShield.dataPoints} data points)`);
  console.log(`NovaTrack Integration: ${results.novaTrack.success ? 'PASS' : 'FAIL'} (${results.novaTrack.dataPoints} data points)`);
  console.log(`NovaCore Integration: ${results.novaCore.success ? 'PASS' : 'FAIL'} (${results.novaCore.dataPoints} data points)`);
  console.log(`Full Integration: ${results.fullIntegration.success ? 'PASS' : 'FAIL'} (${results.fullIntegration.duration.toFixed(2)}ms)`);
  
  return results;
}

// Run the tests
if (require.main === module) {
  runIntegrationTests().catch(error => {
    console.error('Tests failed:', error);
    process.exit(1);
  });
}

module.exports = {
  testNovaShieldIntegration,
  testNovaTrackIntegration,
  testNovaCoreIntegration,
  testFullIntegration,
  runIntegrationTests
};
