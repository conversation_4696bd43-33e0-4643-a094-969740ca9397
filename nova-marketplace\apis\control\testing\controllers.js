/**
 * Control Testing API - Controllers
 * 
 * This file defines the controllers for the Control Testing API.
 */

const { Control, TestPlan, TestResult } = require('./models');
const logger = require('../../../utils/logger');

/**
 * Control Controllers
 */
const controlController = {
  /**
   * Get all controls
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllControls: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', ...filters } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      const controls = await Control.find(filters)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit);
      
      const total = await Control.countDocuments(filters);
      
      return res.status(200).json({
        success: true,
        data: controls,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting controls: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting controls'
      });
    }
  },
  
  /**
   * Get control by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getControlById: async (req, res) => {
    try {
      const control = await Control.findById(req.params.id);
      
      if (!control) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Control not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: control
      });
    } catch (error) {
      logger.error(`Error getting control: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the control'
      });
    }
  },
  
  /**
   * Create a new control
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createControl: async (req, res) => {
    try {
      const control = new Control(req.body);
      await control.save();
      
      return res.status(201).json({
        success: true,
        data: control,
        message: 'Control created successfully'
      });
    } catch (error) {
      logger.error(`Error creating control: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the control'
      });
    }
  },
  
  /**
   * Update a control
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateControl: async (req, res) => {
    try {
      const control = await Control.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!control) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Control not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: control,
        message: 'Control updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating control: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the control'
      });
    }
  },
  
  /**
   * Delete a control
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteControl: async (req, res) => {
    try {
      const control = await Control.findByIdAndDelete(req.params.id);
      
      if (!control) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Control not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Control deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting control: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the control'
      });
    }
  }
};

/**
 * Test Plan Controllers
 */
const testPlanController = {
  /**
   * Get all test plans
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllTestPlans: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', ...filters } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      const testPlans = await TestPlan.find(filters)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('controls.control');
      
      const total = await TestPlan.countDocuments(filters);
      
      return res.status(200).json({
        success: true,
        data: testPlans,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting test plans: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting test plans'
      });
    }
  },
  
  /**
   * Get test plan by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getTestPlanById: async (req, res) => {
    try {
      const testPlan = await TestPlan.findById(req.params.id).populate('controls.control');
      
      if (!testPlan) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test plan not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: testPlan
      });
    } catch (error) {
      logger.error(`Error getting test plan: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the test plan'
      });
    }
  },
  
  /**
   * Create a new test plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createTestPlan: async (req, res) => {
    try {
      const testPlan = new TestPlan(req.body);
      await testPlan.save();
      
      return res.status(201).json({
        success: true,
        data: testPlan,
        message: 'Test plan created successfully'
      });
    } catch (error) {
      logger.error(`Error creating test plan: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the test plan'
      });
    }
  },
  
  /**
   * Update a test plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateTestPlan: async (req, res) => {
    try {
      const testPlan = await TestPlan.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!testPlan) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test plan not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: testPlan,
        message: 'Test plan updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating test plan: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the test plan'
      });
    }
  },
  
  /**
   * Delete a test plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteTestPlan: async (req, res) => {
    try {
      const testPlan = await TestPlan.findByIdAndDelete(req.params.id);
      
      if (!testPlan) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test plan not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Test plan deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting test plan: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the test plan'
      });
    }
  },
  
  /**
   * Add a control to a test plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  addControlToTestPlan: async (req, res) => {
    try {
      const testPlan = await TestPlan.findById(req.params.id);
      
      if (!testPlan) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test plan not found'
        });
      }
      
      // Check if control exists
      const control = await Control.findById(req.body.control);
      
      if (!control) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Control not found'
        });
      }
      
      // Check if control is already in the test plan
      const controlExists = testPlan.controls.find(c => c.control.toString() === req.body.control);
      
      if (controlExists) {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'Control already exists in the test plan'
        });
      }
      
      testPlan.controls.push(req.body);
      testPlan.updatedAt = Date.now();
      
      await testPlan.save();
      
      return res.status(200).json({
        success: true,
        data: testPlan,
        message: 'Control added to test plan successfully'
      });
    } catch (error) {
      logger.error(`Error adding control to test plan: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while adding the control to the test plan'
      });
    }
  },
  
  /**
   * Remove a control from a test plan
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  removeControlFromTestPlan: async (req, res) => {
    try {
      const testPlan = await TestPlan.findById(req.params.id);
      
      if (!testPlan) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test plan not found'
        });
      }
      
      // Check if control exists in the test plan
      const controlIndex = testPlan.controls.findIndex(c => c.control.toString() === req.params.controlId);
      
      if (controlIndex === -1) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Control not found in the test plan'
        });
      }
      
      testPlan.controls.splice(controlIndex, 1);
      testPlan.updatedAt = Date.now();
      
      await testPlan.save();
      
      return res.status(200).json({
        success: true,
        data: testPlan,
        message: 'Control removed from test plan successfully'
      });
    } catch (error) {
      logger.error(`Error removing control from test plan: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while removing the control from the test plan'
      });
    }
  }
};

/**
 * Test Result Controllers
 */
const testResultController = {
  /**
   * Get all test results
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllTestResults: async (req, res) => {
    try {
      const { page = 1, limit = 10, sort = '-createdAt', ...filters } = req.query;
      
      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        sort
      };
      
      const testResults = await TestResult.find(filters)
        .sort(sort)
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .populate('testPlan')
        .populate('control');
      
      const total = await TestResult.countDocuments(filters);
      
      return res.status(200).json({
        success: true,
        data: testResults,
        pagination: {
          total,
          page: options.page,
          limit: options.limit,
          pages: Math.ceil(total / options.limit)
        }
      });
    } catch (error) {
      logger.error(`Error getting test results: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting test results'
      });
    }
  },
  
  /**
   * Get test result by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getTestResultById: async (req, res) => {
    try {
      const testResult = await TestResult.findById(req.params.id)
        .populate('testPlan')
        .populate('control');
      
      if (!testResult) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test result not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: testResult
      });
    } catch (error) {
      logger.error(`Error getting test result: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while getting the test result'
      });
    }
  },
  
  /**
   * Create a new test result
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createTestResult: async (req, res) => {
    try {
      // Check if test plan exists
      const testPlan = await TestPlan.findById(req.body.testPlan);
      
      if (!testPlan) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test plan not found'
        });
      }
      
      // Check if control exists
      const control = await Control.findById(req.body.control);
      
      if (!control) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Control not found'
        });
      }
      
      // Check if control is in the test plan
      const controlInTestPlan = testPlan.controls.find(c => c.control.toString() === req.body.control);
      
      if (!controlInTestPlan) {
        return res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: 'Control is not part of the test plan'
        });
      }
      
      const testResult = new TestResult(req.body);
      await testResult.save();
      
      return res.status(201).json({
        success: true,
        data: testResult,
        message: 'Test result created successfully'
      });
    } catch (error) {
      logger.error(`Error creating test result: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while creating the test result'
      });
    }
  },
  
  /**
   * Update a test result
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateTestResult: async (req, res) => {
    try {
      const testResult = await TestResult.findByIdAndUpdate(
        req.params.id,
        { ...req.body, updatedAt: Date.now() },
        { new: true, runValidators: true }
      );
      
      if (!testResult) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test result not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        data: testResult,
        message: 'Test result updated successfully'
      });
    } catch (error) {
      logger.error(`Error updating test result: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while updating the test result'
      });
    }
  },
  
  /**
   * Delete a test result
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteTestResult: async (req, res) => {
    try {
      const testResult = await TestResult.findByIdAndDelete(req.params.id);
      
      if (!testResult) {
        return res.status(404).json({
          success: false,
          error: 'Not Found',
          message: 'Test result not found'
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'Test result deleted successfully'
      });
    } catch (error) {
      logger.error(`Error deleting test result: ${error.message}`, { service: 'control-testing-api', error });
      return res.status(500).json({
        success: false,
        error: 'Server Error',
        message: 'An error occurred while deleting the test result'
      });
    }
  }
};

module.exports = {
  controlController,
  testPlanController,
  testResultController
};
