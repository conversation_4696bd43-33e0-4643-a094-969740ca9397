const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/metrics/routes');
const models = require('../../../../apis/esg/metrics/models');

// Mock the models
jest.mock('../../../../apis/esg/metrics/models', () => ({
  esgMetrics: [
    {
      id: 'esg-m-12345678',
      name: 'Carbon Emissions',
      description: 'Total carbon emissions in metric tons of CO2 equivalent',
      category: 'environmental',
      subcategory: 'emissions',
      unit: 'tCO2e',
      dataType: 'numeric',
      framework: 'GRI',
      targetValue: '1000',
      targetDate: '2025-12-31',
      owner: 'Sustainability Team',
      status: 'active',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'esg-m-87654321',
      name: 'Gender Diversity',
      description: 'Percentage of women in leadership positions',
      category: 'social',
      subcategory: 'diversity',
      unit: '%',
      dataType: 'percentage',
      framework: 'SASB',
      targetValue: '50',
      targetDate: '2024-12-31',
      owner: 'HR Team',
      status: 'active',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }
  ],
  esgDataPoints: [
    {
      id: 'esg-d-12345678',
      metricId: 'esg-m-12345678',
      value: '1200',
      date: '2023-01-01',
      period: 'quarterly',
      source: 'Internal Report',
      notes: 'Initial measurement',
      verificationStatus: 'verified',
      verifiedBy: 'John Doe',
      verifiedAt: '2023-01-15T00:00:00Z',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    }
  ],
  esgInitiatives: [],
  esgCategories: [
    { id: 'env', name: 'Environmental', description: 'Environmental metrics' },
    { id: 'soc', name: 'Social', description: 'Social metrics' },
    { id: 'gov', name: 'Governance', description: 'Governance metrics' }
  ],
  esgFrameworks: [
    { id: 'gri', name: 'GRI', description: 'Global Reporting Initiative' },
    { id: 'sasb', name: 'SASB', description: 'Sustainability Accounting Standards Board' }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/metrics', router);

describe('ESG Metrics API Integration Tests', () => {
  describe('GET /governance/esg/metrics', () => {
    it('should return all metrics with default pagination', async () => {
      const response = await request(app).get('/governance/esg/metrics');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter metrics by category', async () => {
      const response = await request(app).get('/governance/esg/metrics?category=environmental');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].category).toBe('environmental');
    });
  });

  describe('GET /governance/esg/metrics/:id', () => {
    it('should return a specific metric by ID', async () => {
      const response = await request(app).get('/governance/esg/metrics/esg-m-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-m-12345678');
      expect(response.body.data.name).toBe('Carbon Emissions');
    });

    it('should return 404 if metric not found', async () => {
      const response = await request(app).get('/governance/esg/metrics/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /governance/esg/metrics', () => {
    it('should create a new metric', async () => {
      const newMetric = {
        name: 'Water Usage',
        description: 'Total water consumption in cubic meters',
        category: 'environmental',
        subcategory: 'water',
        unit: 'm³',
        dataType: 'numeric',
        framework: 'GRI',
        targetValue: '5000',
        targetDate: '2025-12-31',
        owner: 'Sustainability Team',
        status: 'active'
      };

      const response = await request(app)
        .post('/governance/esg/metrics')
        .send(newMetric);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'ESG metric created successfully');
      expect(response.body.data.name).toBe('Water Usage');
      expect(response.body.data.category).toBe('environmental');
    });

    it('should return 400 for invalid input', async () => {
      const invalidMetric = {
        // Missing required fields
        description: 'Invalid metric'
      };

      const response = await request(app)
        .post('/governance/esg/metrics')
        .send(invalidMetric);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  // Add more tests for other API endpoints
});
