/**
 * FormattedNumber Component
 * 
 * A component for formatting numbers according to the current locale.
 */

import React from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n/I18nContext';

/**
 * FormattedNumber component
 * 
 * @param {Object} props - Component props
 * @param {number} props.value - Number value
 * @param {Object} [props.options] - Number format options
 * @param {string} [props.component='span'] - Component to render
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} FormattedNumber component
 */
const FormattedNumber = ({
  value,
  options,
  component: Component = 'span',
  className = '',
  style = {},
  ...rest
}) => {
  const { formatNumber } = useI18n();
  
  // Format number
  const formattedNumber = formatNumber(value, options);
  
  return (
    <Component
      className={className}
      style={style}
      {...rest}
    >
      {formattedNumber}
    </Component>
  );
};

FormattedNumber.propTypes = {
  value: PropTypes.number.isRequired,
  options: PropTypes.object,
  component: PropTypes.elementType,
  className: PropTypes.string,
  style: PropTypes.object
};

export default FormattedNumber;
