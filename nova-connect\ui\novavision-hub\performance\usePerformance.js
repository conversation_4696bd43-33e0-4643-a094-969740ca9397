/**
 * usePerformance Hook
 * 
 * This hook provides performance measurement for React components.
 */

import { useRef, useEffect } from 'react';
import performanceMonitor from './PerformanceMonitor';

/**
 * usePerformance hook
 * 
 * @param {string} componentName - Component name
 * @param {Object} [options] - Options
 * @param {boolean} [options.trackRenders=true] - Whether to track renders
 * @param {boolean} [options.trackEffects=false] - Whether to track effects
 * @param {boolean} [options.trackMounts=true] - Whether to track mounts
 * @param {boolean} [options.trackUnmounts=true] - Whether to track unmounts
 * @returns {Object} Performance utilities
 */
const usePerformance = (
  componentName,
  {
    trackRenders = true,
    trackEffects = false,
    trackMounts = true,
    trackUnmounts = true
  } = {}
) => {
  // Refs for timing
  const renderStartTime = useRef(0);
  const mountStartTime = useRef(0);
  const effectStartTime = useRef(0);
  const isMounted = useRef(false);
  
  // Start render timing
  if (trackRenders) {
    renderStartTime.current = performance.now();
  }
  
  // Track mount
  useEffect(() => {
    if (trackMounts && !isMounted.current) {
      const mountTime = performance.now() - mountStartTime.current;
      performanceMonitor.recordOperationMetric(`${componentName}:mount`, mountTime);
      isMounted.current = true;
    }
    
    // Track unmount
    return () => {
      if (trackUnmounts && isMounted.current) {
        performanceMonitor.recordOperationMetric(`${componentName}:unmount`, 0, {
          timestamp: Date.now()
        });
        isMounted.current = false;
      }
    };
  }, [componentName, trackMounts, trackUnmounts]);
  
  // Track effect
  useEffect(() => {
    if (trackEffects) {
      const effectTime = performance.now() - effectStartTime.current;
      performanceMonitor.recordOperationMetric(`${componentName}:effect`, effectTime);
    }
  });
  
  // Set mount start time
  if (trackMounts && !isMounted.current) {
    mountStartTime.current = performance.now();
  }
  
  // Set effect start time
  if (trackEffects) {
    effectStartTime.current = performance.now();
  }
  
  // Record render time on next tick
  useEffect(() => {
    if (trackRenders) {
      const renderTime = performance.now() - renderStartTime.current;
      performanceMonitor.recordRenderMetric(componentName, renderTime);
    }
  });
  
  /**
   * Measure operation
   * 
   * @param {string} operationName - Operation name
   * @param {Function} operation - Operation function
   * @param {Object} [metadata] - Additional metadata
   * @returns {*} Operation result
   */
  const measureOperation = (operationName, operation, metadata = {}) => {
    return performanceMonitor.measureOperation(
      `${componentName}:${operationName}`,
      operation,
      metadata
    );
  };
  
  /**
   * Create measured callback
   * 
   * @param {string} operationName - Operation name
   * @param {Function} callback - Callback function
   * @returns {Function} Measured callback
   */
  const createMeasuredCallback = (operationName, callback) => {
    return (...args) => {
      return measureOperation(operationName, () => callback(...args), {
        args: args.length > 0 ? `${args.length} arguments` : 'no arguments'
      });
    };
  };
  
  return {
    measureOperation,
    createMeasuredCallback
  };
};

export default usePerformance;
