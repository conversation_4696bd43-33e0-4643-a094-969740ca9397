# Start-Kethernet.ps1
# KetherNet Server Startup Script with Auto-Restart and Logging

# Configuration
$ServerScript = "kethernet-server.js"
$LogDirectory = "logs"
$MaxLogSizeMB = 10
$MaxLogFiles = 5
$RestartDelay = 5 # seconds
$NodeArgs = @("--trace-warnings", "--max-old-space-size=4096") # Node.js arguments

# Create logs directory if it doesn't exist
if (-not (Test-Path -Path $LogDirectory)) {
    New-Item -ItemType Directory -Path $LogDirectory | Out-Null
    Write-Host "Created logs directory: $LogDirectory"
}

# Function to get current timestamp
function Get-Timestamp {
    return Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
}

# Function to rotate log files
function Rotate-LogFile {
    param (
        [string]$LogPath
    )
    
    if (Test-Path $LogPath) {
        $fileInfo = Get-Item $LogPath
        if ($fileInfo.Length -gt ($MaxLogSizeMB * 1MB)) {
            # Rotate logs
            Write-Host "Rotating log file: $LogPath"
            
            # Get existing log files
            $logFiles = Get-ChildItem -Path "$LogDirectory\server-*.log" | 
                       Sort-Object LastWriteTime -Descending
            
            # Remove oldest logs if we have too many
            if ($logFiles.Count -ge $MaxLogFiles) {
                $logFiles | Select-Object -Skip ($MaxLogFiles - 1) | Remove-Item -Force
            }
            
            # Rename current log with timestamp
            $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
            $newName = "server-$timestamp.log"
            Rename-Item -Path $LogPath -NewName $newName -Force
        }
    }
}

# Main server process loop
$restartCount = 0
$startTime = Get-Date

Write-Host "========================================"
Write-Host "🚀 Starting KetherNet Server"
Write-Host "   Script: $ServerScript"
Write-Host "   Log Directory: $LogDirectory"
Write-Host "   Max Log Size: ${MaxLogSizeMB}MB"
Write-Host "   Max Log Files: $MaxLogFiles"
Write-Host "   Node.js Args: $($NodeArgs -join ' ')"
Write-Host "   Start Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Host "========================================"

while ($true) {
    $timestamp = Get-Timestamp
    $logFile = "$LogDirectory\server.log"
    
    # Rotate log if needed
    Rotate-LogFile -LogPath $logFile
    
    # Start the server process
    $processInfo = New-Object System.Diagnostics.ProcessStartInfo
    $processInfo.FileName = "node"
    $processInfo.Arguments = ($NodeArgs + @($ServerScript)) -join ' '
    $processInfo.RedirectStandardOutput = $true
    $processInfo.RedirectStandardError = $true
    $processInfo.UseShellExecute = $false
    $processInfo.CreateNoWindow = $true
    $processInfo.WorkingDirectory = $PSScriptRoot
    
    $process = New-Object System.Diagnostics.Process
    $process.StartInfo = $processInfo
    
    # Create event handlers for output
    $outputHandler = {
        if (![String]::IsNullOrWhiteSpace($EventArgs.Data)) {
            $timestamp = Get-Timestamp
            $line = "[$timestamp] $($EventArgs.Data)"
            Add-Content -Path $logFile -Value $line -Force
            Write-Host $line
        }
    }
    
    $errorHandler = {
        if (![String]::IsNullOrWhiteSpace($EventArgs.Data)) {
            $timestamp = Get-Timestamp
            $line = "[$timestamp] [ERROR] $($EventArgs.Data)"
            Add-Content -Path $logFile -Value $line -Force
            Write-Host $line -ForegroundColor Red
        }
    }
    
    $stdoutEvent = Register-ObjectEvent -InputObject $process -EventName OutputDataReceived -Action $outputHandler
    $stderrEvent = Register-ObjectEvent -InputObject $process -EventName ErrorDataReceived -Action $errorHandler
    
    try {
        # Start the process
        $process.Start() | Out-Null
        $process.BeginOutputReadLine()
        $process.BeginErrorReadLine()
        
        $processId = $process.Id
        $restartCount++
        $uptime = (Get-Date) - $startTime
        
        Write-Host "[$timestamp] ✅ Server started (PID: $processId, Restarts: $restartCount, Uptime: $($uptime.Days)d $($uptime.Hours)h $($uptime.Minutes)m)"
        
        # Wait for the process to exit
        $process.WaitForExit()
        
        # Log the exit
        $exitCode = $process.ExitCode
        $timestamp = Get-Timestamp
        $uptime = (Get-Date) - $startTime
        
        Write-Host "[$timestamp] ⚠️  Server process exited with code $exitCode" -ForegroundColor Yellow
        Write-Host "[$timestamp] ⏳ Waiting $RestartDelay seconds before restart..."
        
        # Clean up event handlers
        Unregister-Event -SourceIdentifier $stdoutEvent.Name -ErrorAction SilentlyContinue
        Unregister-Event -SourceIdentifier $stderrEvent.Name -ErrorAction SilentlyContinue
        
        # Wait before restarting
        Start-Sleep -Seconds $RestartDelay
        
    } catch {
        $errorMsg = $_.Exception.Message
        $timestamp = Get-Timestamp
        
        Write-Host "[$timestamp] ❌ Error: $errorMsg" -ForegroundColor Red
        Write-Host "[$timestamp] ⏳ Waiting $RestartDelay seconds before retry..."
        
        # Clean up event handlers on error
        Unregister-Event -SourceIdentifier $stdoutEvent.Name -ErrorAction SilentlyContinue
        Unregister-Event -SourceIdentifier $stderrEvent.Name -ErrorAction SilentlyContinue
        
        Start-Sleep -Seconds $RestartDelay
    } finally {
        # Ensure process is terminated
        if ($process -and !$process.HasExited) {
            $process.Kill($true)
        }
        $process.Dispose()
    }
}
