/**
 * Test Security Features
 * 
 * This script tests the security features implemented in the NovaConnect API.
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const API_URL = 'http://localhost:3000';
const TEST_USER = {
  username: 'testuser',
  password: 'password123'
};
const TEST_ADMIN = {
  username: 'admin',
  password: 'admin123'
};

// Test results
const results = {
  rateLimiting: {
    passed: 0,
    failed: 0,
    tests: []
  },
  bruteForceProtection: {
    passed: 0,
    failed: 0,
    tests: []
  },
  ipRestrictions: {
    passed: 0,
    failed: 0,
    tests: []
  },
  authAuditLogging: {
    passed: 0,
    failed: 0,
    tests: []
  }
};

// Helper functions
async function login(username, password) {
  try {
    const response = await axios.post(`${API_URL}/api/auth/login`, {
      username,
      password
    });
    return response.data;
  } catch (error) {
    console.error('Login error:', error.response ? error.response.data : error.message);
    return null;
  }
}

async function testRateLimiting() {
  console.log('\n--- Testing Rate Limiting ---');
  
  // Test 1: Get rate limit configuration (admin only)
  try {
    // Login as admin
    const adminAuth = await login(TEST_ADMIN.username, TEST_ADMIN.password);
    if (!adminAuth) {
      recordResult('rateLimiting', 'Admin login for rate limit test', false);
      return;
    }
    
    // Get rate limit configuration
    const response = await axios.get(`${API_URL}/api/rate-limits`, {
      headers: {
        Authorization: `Bearer ${adminAuth.token}`
      }
    });
    
    if (response.status === 200) {
      console.log('✅ Admin can access rate limit configuration');
      recordResult('rateLimiting', 'Admin access to rate limit configuration', true);
    } else {
      console.log('❌ Admin cannot access rate limit configuration');
      recordResult('rateLimiting', 'Admin access to rate limit configuration', false);
    }
  } catch (error) {
    console.log('❌ Error testing admin access to rate limit configuration:', error.message);
    recordResult('rateLimiting', 'Admin access to rate limit configuration', false);
  }
  
  // Test 2: Regular user cannot access rate limit configuration
  try {
    // Login as regular user
    const userAuth = await login(TEST_USER.username, TEST_USER.password);
    if (!userAuth) {
      recordResult('rateLimiting', 'User login for rate limit test', false);
      return;
    }
    
    try {
      // Try to get rate limit configuration
      await axios.get(`${API_URL}/api/rate-limits`, {
        headers: {
          Authorization: `Bearer ${userAuth.token}`
        }
      });
      
      console.log('❌ Regular user can access rate limit configuration (should be forbidden)');
      recordResult('rateLimiting', 'Regular user access to rate limit configuration', false);
    } catch (error) {
      if (error.response && error.response.status === 403) {
        console.log('✅ Regular user correctly denied access to rate limit configuration');
        recordResult('rateLimiting', 'Regular user access to rate limit configuration', true);
      } else {
        console.log('❌ Unexpected error when testing regular user access:', error.message);
        recordResult('rateLimiting', 'Regular user access to rate limit configuration', false);
      }
    }
  } catch (error) {
    console.log('❌ Error testing regular user access to rate limit configuration:', error.message);
    recordResult('rateLimiting', 'Regular user access to rate limit configuration', false);
  }
}

async function testBruteForceProtection() {
  console.log('\n--- Testing Brute Force Protection ---');
  
  // Test 1: Failed login attempts
  const testUsername = `test-${Date.now()}`;
  const wrongPassword = 'wrongpassword';
  
  try {
    // Make multiple failed login attempts
    for (let i = 0; i < 3; i++) {
      try {
        await login(testUsername, wrongPassword);
        console.log(`Attempt ${i + 1}: Login attempt with wrong password`);
      } catch (error) {
        console.log(`Attempt ${i + 1}: Login failed as expected`);
      }
    }
    
    // Try one more time to see if we get blocked
    try {
      await login(testUsername, wrongPassword);
      console.log('❌ Account not blocked after multiple failed attempts');
      recordResult('bruteForceProtection', 'Account blocking after multiple failed attempts', false);
    } catch (error) {
      if (error.response && error.response.status === 429) {
        console.log('✅ Account correctly blocked after multiple failed attempts');
        recordResult('bruteForceProtection', 'Account blocking after multiple failed attempts', true);
      } else {
        console.log('❌ Unexpected error when testing brute force protection:', error.message);
        recordResult('bruteForceProtection', 'Account blocking after multiple failed attempts', false);
      }
    }
  } catch (error) {
    console.log('❌ Error testing brute force protection:', error.message);
    recordResult('bruteForceProtection', 'Account blocking after multiple failed attempts', false);
  }
}

async function testIpRestrictions() {
  console.log('\n--- Testing IP Restrictions ---');
  
  // Test 1: Get IP restrictions configuration (admin only)
  try {
    // Login as admin
    const adminAuth = await login(TEST_ADMIN.username, TEST_ADMIN.password);
    if (!adminAuth) {
      recordResult('ipRestrictions', 'Admin login for IP restrictions test', false);
      return;
    }
    
    // Get IP restrictions configuration
    const response = await axios.get(`${API_URL}/api/ip-restrictions`, {
      headers: {
        Authorization: `Bearer ${adminAuth.token}`
      }
    });
    
    if (response.status === 200) {
      console.log('✅ Admin can access IP restrictions configuration');
      recordResult('ipRestrictions', 'Admin access to IP restrictions configuration', true);
    } else {
      console.log('❌ Admin cannot access IP restrictions configuration');
      recordResult('ipRestrictions', 'Admin access to IP restrictions configuration', false);
    }
  } catch (error) {
    console.log('❌ Error testing admin access to IP restrictions configuration:', error.message);
    recordResult('ipRestrictions', 'Admin access to IP restrictions configuration', false);
  }
}

async function testAuthAuditLogging() {
  console.log('\n--- Testing Authentication Audit Logging ---');
  
  // Test 1: Get authentication audit logs (admin only)
  try {
    // Login as admin
    const adminAuth = await login(TEST_ADMIN.username, TEST_ADMIN.password);
    if (!adminAuth) {
      recordResult('authAuditLogging', 'Admin login for auth audit test', false);
      return;
    }
    
    // Get authentication audit logs
    const response = await axios.get(`${API_URL}/api/auth/audit`, {
      headers: {
        Authorization: `Bearer ${adminAuth.token}`
      }
    });
    
    if (response.status === 200) {
      console.log('✅ Admin can access authentication audit logs');
      recordResult('authAuditLogging', 'Admin access to authentication audit logs', true);
    } else {
      console.log('❌ Admin cannot access authentication audit logs');
      recordResult('authAuditLogging', 'Admin access to authentication audit logs', false);
    }
  } catch (error) {
    console.log('❌ Error testing admin access to authentication audit logs:', error.message);
    recordResult('authAuditLogging', 'Admin access to authentication audit logs', false);
  }
  
  // Test 2: Regular user can access their own login history
  try {
    // Login as regular user
    const userAuth = await login(TEST_USER.username, TEST_USER.password);
    if (!userAuth) {
      recordResult('authAuditLogging', 'User login for auth audit test', false);
      return;
    }
    
    // Get user's login history
    const response = await axios.get(`${API_URL}/api/auth/audit/user/${userAuth.user.id}/login-history`, {
      headers: {
        Authorization: `Bearer ${userAuth.token}`
      }
    });
    
    if (response.status === 200) {
      console.log('✅ User can access their own login history');
      recordResult('authAuditLogging', 'User access to own login history', true);
    } else {
      console.log('❌ User cannot access their own login history');
      recordResult('authAuditLogging', 'User access to own login history', false);
    }
  } catch (error) {
    console.log('❌ Error testing user access to own login history:', error.message);
    recordResult('authAuditLogging', 'User access to own login history', false);
  }
}

function recordResult(feature, testName, passed) {
  results[feature].tests.push({
    name: testName,
    passed
  });
  
  if (passed) {
    results[feature].passed++;
  } else {
    results[feature].failed++;
  }
}

async function printSummary() {
  console.log('\n--- Test Summary ---');
  
  for (const [feature, result] of Object.entries(results)) {
    const total = result.passed + result.failed;
    const passPercentage = total > 0 ? Math.round((result.passed / total) * 100) : 0;
    
    console.log(`\n${feature}:`);
    console.log(`  Passed: ${result.passed}/${total} (${passPercentage}%)`);
    
    for (const test of result.tests) {
      console.log(`  ${test.passed ? '✅' : '❌'} ${test.name}`);
    }
  }
}

// Main function
async function main() {
  console.log('Starting security features tests...');
  
  try {
    await testRateLimiting();
    await testBruteForceProtection();
    await testIpRestrictions();
    await testAuthAuditLogging();
    
    await printSummary();
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the tests
main();
