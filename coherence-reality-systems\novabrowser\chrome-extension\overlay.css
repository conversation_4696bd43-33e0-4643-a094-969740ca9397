/* NovaBrowser Chrome Extension Overlay Styles */

#novabrowser-overlay {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 300px !important;
    background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
    color: white !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    z-index: 999999 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
}

.nova-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 15px 20px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    cursor: move !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 12px 12px 0 0 !important;
}

.nova-logo {
    font-size: 18px !important;
    margin-right: 8px !important;
}

.nova-title {
    font-weight: bold !important;
    font-size: 16px !important;
    background: linear-gradient(45deg, #00ff96, #667eea) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.nova-close {
    background: none !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 18px !important;
    cursor: pointer !important;
    padding: 0 !important;
    width: 24px !important;
    height: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 50% !important;
    transition: background 0.2s !important;
}

.nova-close:hover {
    background: rgba(255, 255, 255, 0.1) !important;
}

.nova-metrics {
    padding: 15px 20px !important;
}

.nova-metric {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 10px !important;
}

.nova-label {
    font-size: 13px !important;
    opacity: 0.8 !important;
}

.nova-value {
    font-weight: bold !important;
    font-size: 14px !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
}

.nova-value.high {
    background: rgba(0, 255, 150, 0.2) !important;
    color: #00ff96 !important;
}

.nova-value.medium {
    background: rgba(255, 167, 38, 0.2) !important;
    color: #ffa726 !important;
}

.nova-value.low {
    background: rgba(255, 71, 87, 0.2) !important;
    color: #ff4757 !important;
}

.nova-status {
    text-align: center !important;
    padding: 10px 20px !important;
    font-weight: bold !important;
    font-size: 12px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(0, 0, 0, 0.1) !important;
}

.nova-violations {
    padding: 15px 20px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
    background: rgba(255, 71, 87, 0.05) !important;
}

.nova-violations-header {
    font-weight: bold !important;
    margin-bottom: 8px !important;
    color: #ff4757 !important;
    font-size: 12px !important;
}

.nova-violation {
    font-size: 11px !important;
    margin-bottom: 4px !important;
    opacity: 0.9 !important;
    padding-left: 8px !important;
}

.nova-autofix {
    background: linear-gradient(45deg, #00ff96, #00d4aa) !important;
    color: #000 !important;
    border: none !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    font-weight: bold !important;
    font-size: 11px !important;
    margin-top: 10px !important;
    width: 100% !important;
    transition: transform 0.2s !important;
}

.nova-autofix:hover {
    transform: translateY(-1px) !important;
}

/* Animation for overlay appearance */
@keyframes novaSlideIn {
    0% {
        transform: translateX(100%) !important;
        opacity: 0 !important;
    }
    100% {
        transform: translateX(0) !important;
        opacity: 1 !important;
    }
}

#novabrowser-overlay {
    animation: novaSlideIn 0.3s ease-out !important;
}

/* Ensure overlay stays on top of everything */
#novabrowser-overlay * {
    box-sizing: border-box !important;
    line-height: 1.4 !important;
}

/* Override any site styles that might interfere */
#novabrowser-overlay,
#novabrowser-overlay * {
    all: unset !important;
}

/* Re-apply our styles after reset */
#novabrowser-overlay {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 300px !important;
    background: linear-gradient(135deg, #1a1a2e, #16213e) !important;
    color: white !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
    z-index: 999999 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    display: block !important;
}
