# Zapier Integration

NovaConnect UAC integrates with Zapier to enable no-code automation with 5,000+ apps. This document explains how the integration works and how to use it.

## Overview

The Zapier integration provides:

- **OAuth Authentication**: Secure authentication with Zapier
- **Triggers**: Events that can trigger Zaps
- **Actions**: Operations that can be performed by Zaps
- **Pre-built Zaps**: Ready-to-use Zap templates for common use cases

## Key Components

### Zapier Service

The `ZapierService` handles all Zapier-related functionality:

- **App Definition**: Defines the Zapier app
- **Triggers**: Manages triggers for Zapier
- **Actions**: Manages actions for Zapier
- **OAuth Authentication**: Handles OAuth authentication with Zapier

### Zapier Controller

The `ZapierController` provides API endpoints for Zapier integration:

- **App Definition**: Endpoint for Zapier app definition
- **Triggers**: Endpoints for Zapier triggers
- **Actions**: Endpoints for Zapier actions
- **OAuth Authentication**: Endpoints for OAuth authentication

## Authentication

The Zapier integration uses OAuth 2.0 for authentication:

1. **Authorization**: User authorizes NovaConnect UAC to access their Zapier account
2. **Token Exchange**: NovaConnect UAC exchanges authorization code for access token
3. **API Access**: NovaConnect UAC uses access token to access Zapier API

### OAuth Endpoints

- **Authorization**: `GET /api/zapier/oauth/authorize`
- **Token Exchange**: `POST /api/zapier/oauth/token`
- **Token Refresh**: `POST /api/zapier/oauth/refresh`

## Triggers

Triggers are events that can start a Zap. NovaConnect UAC provides the following triggers:

### New Connector

Triggers when a new connector is created.

```json
{
  "key": "new_connector",
  "noun": "Connector",
  "display": {
    "label": "New Connector",
    "description": "Triggers when a new connector is created."
  },
  "operation": {
    "type": "polling",
    "perform": {
      "url": "https://api.nova-connect.io/api/zapier/triggers/new-connector"
    }
  }
}
```

### New Workflow

Triggers when a new workflow is created.

```json
{
  "key": "new_workflow",
  "noun": "Workflow",
  "display": {
    "label": "New Workflow",
    "description": "Triggers when a new workflow is created."
  },
  "operation": {
    "type": "polling",
    "perform": {
      "url": "https://api.nova-connect.io/api/zapier/triggers/new-workflow"
    }
  }
}
```

### Compliance Event

Triggers when a new compliance event occurs.

```json
{
  "key": "compliance_event",
  "noun": "Compliance Event",
  "display": {
    "label": "New Compliance Event",
    "description": "Triggers when a new compliance event occurs."
  },
  "operation": {
    "type": "polling",
    "perform": {
      "url": "https://api.nova-connect.io/api/zapier/triggers/compliance-event"
    }
  }
}
```

## Actions

Actions are operations that can be performed by a Zap. NovaConnect UAC provides the following actions:

### Create Connector

Creates a new connector.

```json
{
  "key": "create_connector",
  "noun": "Connector",
  "display": {
    "label": "Create Connector",
    "description": "Creates a new connector."
  },
  "operation": {
    "type": "perform",
    "perform": {
      "url": "https://api.nova-connect.io/api/zapier/actions/create-connector",
      "method": "POST"
    },
    "inputFields": [
      {
        "key": "name",
        "label": "Name",
        "type": "string",
        "required": true
      },
      {
        "key": "type",
        "label": "Type",
        "type": "string",
        "required": true,
        "choices": {
          "api": "API",
          "database": "Database",
          "file": "File"
        }
      },
      {
        "key": "config",
        "label": "Configuration",
        "type": "text",
        "required": true
      }
    ]
  }
}
```

### Execute Workflow

Executes a workflow.

```json
{
  "key": "execute_workflow",
  "noun": "Workflow",
  "display": {
    "label": "Execute Workflow",
    "description": "Executes a workflow."
  },
  "operation": {
    "type": "perform",
    "perform": {
      "url": "https://api.nova-connect.io/api/zapier/actions/execute-workflow",
      "method": "POST"
    },
    "inputFields": [
      {
        "key": "workflowId",
        "label": "Workflow ID",
        "type": "string",
        "required": true
      },
      {
        "key": "inputs",
        "label": "Inputs",
        "type": "text",
        "required": false
      }
    ]
  }
}
```

### Create Compliance Evidence

Creates a new compliance evidence record.

```json
{
  "key": "create_compliance_evidence",
  "noun": "Compliance Evidence",
  "display": {
    "label": "Create Compliance Evidence",
    "description": "Creates a new compliance evidence record."
  },
  "operation": {
    "type": "perform",
    "perform": {
      "url": "https://api.nova-connect.io/api/zapier/actions/create-compliance-evidence",
      "method": "POST"
    },
    "inputFields": [
      {
        "key": "controlId",
        "label": "Control ID",
        "type": "string",
        "required": true
      },
      {
        "key": "evidenceType",
        "label": "Evidence Type",
        "type": "string",
        "required": true,
        "choices": {
          "document": "Document",
          "screenshot": "Screenshot",
          "log": "Log",
          "test_result": "Test Result",
          "attestation": "Attestation"
        }
      },
      {
        "key": "description",
        "label": "Description",
        "type": "text",
        "required": true
      },
      {
        "key": "data",
        "label": "Data",
        "type": "text",
        "required": false
      }
    ]
  }
}
```

## Pre-built Zaps

NovaConnect UAC provides pre-built Zap templates for common use cases:

### Compliance Notification

Sends a notification when a compliance event occurs.

1. **Trigger**: New Compliance Event
2. **Action**: Send Email/Slack Message/Teams Message

### Connector Creation

Creates a connector when a new record is added to a spreadsheet.

1. **Trigger**: New Row in Google Sheets/Excel/Airtable
2. **Action**: Create Connector

### Workflow Execution

Executes a workflow when a form is submitted.

1. **Trigger**: New Form Submission in Google Forms/Typeform/JotForm
2. **Action**: Execute Workflow

### Evidence Collection

Creates compliance evidence when a document is uploaded.

1. **Trigger**: New File in Google Drive/Dropbox/OneDrive
2. **Action**: Create Compliance Evidence

## API Endpoints

### App Definition

- `GET /api/zapier/app-definition`: Get Zapier app definition

### Triggers and Actions

- `GET /api/zapier/triggers`: Get all triggers
- `GET /api/zapier/actions`: Get all actions

### OAuth

- `GET /api/zapier/oauth/authorize`: Authorize Zapier
- `POST /api/zapier/oauth/token`: Get OAuth token

### App Hooks

- `GET /api/zapier/before-app`: Before app hook
- `GET /api/zapier/after-app`: After app hook

### Trigger Endpoints

- `GET /api/zapier/triggers/new-connector`: New connector trigger
- `GET /api/zapier/triggers/new-workflow`: New workflow trigger
- `GET /api/zapier/triggers/compliance-event`: Compliance event trigger

### Action Endpoints

- `POST /api/zapier/actions/create-connector`: Create connector action
- `POST /api/zapier/actions/execute-workflow`: Execute workflow action
- `POST /api/zapier/actions/create-compliance-evidence`: Create compliance evidence action

### Admin Endpoints

- `POST /api/zapier/apps`: Register Zapier app
- `GET /api/zapier/apps`: Get all Zapier apps
- `GET /api/zapier/apps/:id`: Get Zapier app by ID
- `PUT /api/zapier/apps/:id`: Update Zapier app
- `DELETE /api/zapier/apps/:id`: Delete Zapier app
- `POST /api/zapier/triggers/register`: Register Zapier trigger
- `POST /api/zapier/actions/register`: Register Zapier action

## Configuration

The following environment variables are used for Zapier integration:

- `API_BASE_URL`: Base URL for the API (default: `https://api.nova-connect.io`)
- `JWT_SECRET`: Secret for JWT tokens
- `ZAPIER_JWT_EXPIRES_IN`: Expiration time for JWT tokens (default: `30d`)
- `ZAPIER_CLIENT_ID`: Client ID for Zapier OAuth (default: `nova-connect-zapier`)
- `ZAPIER_CLIENT_SECRET`: Client secret for Zapier OAuth
- `ZAPIER_REDIRECT_URI`: Redirect URI for Zapier OAuth (default: `https://zapier.com/dashboard/auth/oauth/return/App-ID/`)

## Testing

To test the Zapier integration locally:

1. Set up a local development environment with the required environment variables
2. Use the API endpoints to simulate Zapier triggers and actions
3. Use the Zapier CLI to test the integration

Example:

```bash
# Get app definition
curl http://localhost:3000/api/zapier/app-definition

# Get triggers
curl http://localhost:3000/api/zapier/triggers

# Get actions
curl http://localhost:3000/api/zapier/actions

# Simulate new connector trigger
curl http://localhost:3000/api/zapier/triggers/new-connector

# Simulate create connector action
curl -X POST http://localhost:3000/api/zapier/actions/create-connector \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Connector", "type": "api", "config": "{\"baseUrl\": \"https://api.example.com\"}"}'
```

## Conclusion

The Zapier integration provides a powerful way to automate workflows between NovaConnect UAC and 5,000+ apps on Zapier. It enables no-code automation for compliance, security, and governance use cases.
