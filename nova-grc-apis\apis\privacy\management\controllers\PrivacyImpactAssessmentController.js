/**
 * Privacy Impact Assessment Controller
 * 
 * Handles operations related to privacy impact assessments (PIAs).
 */

const PrivacyImpactAssessment = require('../models/PrivacyImpactAssessment');
const ProcessingActivity = require('../models/ProcessingActivity');
const { validationResult } = require('express-validator');
const mongoose = require('mongoose');

/**
 * Get all privacy impact assessments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllPrivacyImpactAssessments = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build query based on filters
    const query = {};
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.processingActivity) {
      query.processingActivity = req.query.processingActivity;
    }
    
    // Count total documents for pagination
    const total = await PrivacyImpactAssessment.countDocuments(query);
    
    // Get privacy impact assessments with pagination
    const pias = await PrivacyImpactAssessment.find(query)
      .populate('processingActivity', 'name')
      .populate('initiatedBy', 'name')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      count: pias.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: pias
    });
  } catch (error) {
    console.error('Error in getAllPrivacyImpactAssessments:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Get a single privacy impact assessment by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPrivacyImpactAssessment = async (req, res) => {
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id)
      .populate('processingActivity')
      .populate('initiatedBy', 'name')
      .populate('assessmentTeam.user', 'name')
      .populate('risks.owner', 'name')
      .populate('mitigationPlan.owner', 'name')
      .populate('approvals.user', 'name');
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in getPrivacyImpactAssessment:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Create a new privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createPrivacyImpactAssessment = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    // Check if processing activity exists
    const processingActivity = await ProcessingActivity.findById(req.body.processingActivity);
    
    if (!processingActivity) {
      return res.status(404).json({
        success: false,
        error: 'Processing activity not found'
      });
    }
    
    // Generate a unique reference number if not provided
    if (!req.body.reference) {
      const count = await PrivacyImpactAssessment.countDocuments();
      req.body.reference = `PIA-${new Date().getFullYear()}-${(count + 1).toString().padStart(4, '0')}`;
    }
    
    // Add initial history entry
    if (!req.body.history) {
      req.body.history = [];
    }
    
    req.body.history.push({
      action: 'Created',
      date: new Date(),
      user: req.user ? req.user._id : null,
      details: 'Privacy impact assessment created'
    });
    
    // Create new privacy impact assessment
    const pia = await PrivacyImpactAssessment.create(req.body);
    
    // Update processing activity with DPIA reference
    await ProcessingActivity.findByIdAndUpdate(
      req.body.processingActivity,
      {
        'dpia.required': true,
        'dpia.completed': false,
        'dpia.reference': pia.reference,
        'dpia.date': new Date()
      }
    );
    
    res.status(201).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in createPrivacyImpactAssessment:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updatePrivacyImpactAssessment = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    let pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Add history entry if status is changing
    if (req.body.status && req.body.status !== pia.status) {
      if (!req.body.history) {
        req.body.history = [...pia.history];
      }
      
      req.body.history.push({
        action: `Status Changed to ${req.body.status}`,
        date: new Date(),
        user: req.user ? req.user._id : null,
        details: `Status changed from ${pia.status} to ${req.body.status}`
      });
      
      // If status is changing to 'Completed', update the completion date
      if (req.body.status === 'Completed' && pia.status !== 'Completed') {
        req.body.completionDate = new Date();
        
        // Update processing activity with DPIA completed status
        await ProcessingActivity.findByIdAndUpdate(
          pia.processingActivity,
          {
            'dpia.completed': true
          }
        );
      }
    }
    
    // Update privacy impact assessment
    pia = await PrivacyImpactAssessment.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in updatePrivacyImpactAssessment:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Delete a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deletePrivacyImpactAssessment = async (req, res) => {
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    await pia.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error in deletePrivacyImpactAssessment:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Add a risk to a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addRisk = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Add risk
    pia.risks.push(req.body);
    
    // Add history entry
    pia.addHistoryEntry(
      'Risk Added',
      req.user ? req.user._id : null,
      `Risk added: ${req.body.description}`
    );
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in addRisk:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update a risk in a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateRisk = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Find the risk
    const riskIndex = pia.risks.findIndex(risk => risk._id.toString() === req.params.riskId);
    
    if (riskIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Risk not found'
      });
    }
    
    // Update risk
    pia.risks[riskIndex] = { ...pia.risks[riskIndex].toObject(), ...req.body };
    
    // Add history entry
    pia.addHistoryEntry(
      'Risk Updated',
      req.user ? req.user._id : null,
      `Risk updated: ${pia.risks[riskIndex].description}`
    );
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in updateRisk:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment or risk not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Delete a risk from a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteRisk = async (req, res) => {
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Find the risk
    const risk = pia.risks.id(req.params.riskId);
    
    if (!risk) {
      return res.status(404).json({
        success: false,
        error: 'Risk not found'
      });
    }
    
    // Add history entry
    pia.addHistoryEntry(
      'Risk Deleted',
      req.user ? req.user._id : null,
      `Risk deleted: ${risk.description}`
    );
    
    // Remove the risk
    risk.remove();
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in deleteRisk:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment or risk not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Add a mitigation action to a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addMitigation = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Add mitigation action
    pia.mitigationPlan.push(req.body);
    
    // Add history entry
    pia.addHistoryEntry(
      'Mitigation Added',
      req.user ? req.user._id : null,
      `Mitigation action added: ${req.body.action}`
    );
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in addMitigation:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update a mitigation action in a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateMitigation = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Find the mitigation action
    const mitigationIndex = pia.mitigationPlan.findIndex(
      mitigation => mitigation._id.toString() === req.params.mitigationId
    );
    
    if (mitigationIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Mitigation action not found'
      });
    }
    
    // Update mitigation action
    pia.mitigationPlan[mitigationIndex] = {
      ...pia.mitigationPlan[mitigationIndex].toObject(),
      ...req.body
    };
    
    // If status is changing to 'Completed', update the completion date
    if (
      req.body.status === 'Completed' &&
      pia.mitigationPlan[mitigationIndex].status !== 'Completed'
    ) {
      pia.mitigationPlan[mitigationIndex].completionDate = new Date();
    }
    
    // Add history entry
    pia.addHistoryEntry(
      'Mitigation Updated',
      req.user ? req.user._id : null,
      `Mitigation action updated: ${pia.mitigationPlan[mitigationIndex].action}`
    );
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in updateMitigation:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment or mitigation action not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Delete a mitigation action from a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteMitigation = async (req, res) => {
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Find the mitigation action
    const mitigation = pia.mitigationPlan.id(req.params.mitigationId);
    
    if (!mitigation) {
      return res.status(404).json({
        success: false,
        error: 'Mitigation action not found'
      });
    }
    
    // Add history entry
    pia.addHistoryEntry(
      'Mitigation Deleted',
      req.user ? req.user._id : null,
      `Mitigation action deleted: ${mitigation.action}`
    );
    
    // Remove the mitigation action
    mitigation.remove();
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in deleteMitigation:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment or mitigation action not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update the conclusion of a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateConclusion = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Update conclusion
    pia.conclusion = req.body;
    
    // Add history entry
    pia.addHistoryEntry(
      'Conclusion Updated',
      req.user ? req.user._id : null,
      `Conclusion updated with recommendation: ${req.body.recommendation}`
    );
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in updateConclusion:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Add an approval to a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addApproval = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    // Add approval
    pia.approvals.push({
      ...req.body,
      date: new Date()
    });
    
    // Add history entry
    pia.addHistoryEntry(
      `Approval ${req.body.decision}`,
      req.user ? req.user._id : null,
      `Approval added by ${req.body.role} with decision: ${req.body.decision}`
    );
    
    // If all approvals are complete and all are approved, update status
    const allApproved = pia.approvals.every(approval => approval.decision === 'Approved');
    if (allApproved && pia.approvals.length >= 2) { // Assuming at least 2 approvals are required
      pia.status = 'Approved';
    }
    
    await pia.save();
    
    res.status(200).json({
      success: true,
      data: pia
    });
  } catch (error) {
    console.error('Error in addApproval:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Calculate the overall risk level of a privacy impact assessment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.calculateRiskLevel = async (req, res) => {
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    const riskLevel = pia.calculateOverallRiskLevel();
    
    res.status(200).json({
      success: true,
      data: {
        riskLevel,
        risks: pia.risks
      }
    });
  } catch (error) {
    console.error('Error in calculateRiskLevel:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Check if supervisory authority consultation is required
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkConsultationRequired = async (req, res) => {
  try {
    const pia = await PrivacyImpactAssessment.findById(req.params.id);
    
    if (!pia) {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    const isRequired = pia.isSupervisoryConsultationRequired();
    
    res.status(200).json({
      success: true,
      data: {
        isRequired,
        highRisks: pia.risks.filter(risk => 
          (risk.riskLevel === 'High' || risk.riskLevel === 'Critical') && 
          (!risk.residualRisk || risk.residualRisk.riskLevel === 'High' || risk.residualRisk.riskLevel === 'Critical')
        )
      }
    });
  } catch (error) {
    console.error('Error in checkConsultationRequired:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Privacy impact assessment not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
