/**
 * NovaConnect - Chronicle Live Integration Test
 * 
 * This test connects to the actual Google Chronicle API
 * to validate threat-to-compliance mapping capabilities.
 * 
 * NOTE: This test requires valid GCP credentials with Chronicle access.
 * Set the GOOGLE_APPLICATION_CREDENTIALS environment variable to point
 * to a service account key file with appropriate permissions.
 */

const { ChronicleConnector } = require('../../../src/connectors/gcp/chronicle-connector');
const { RemediationEngine } = require('../../../src/engines/remediation-engine');
const { performance } = require('perf_hooks');
const fs = require('fs');

// Skip tests if credentials are not available
const hasCredentials = process.env.GOOGLE_APPLICATION_CREDENTIALS && 
  fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS);

// Test configuration
const config = {
  startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
  endTime: new Date().toISOString(),
  maxAlerts: 100
};

describe('Chronicle Live Integration', () => {
  let chronicleConnector;
  let remediationEngine;
  
  beforeAll(async () => {
    // Initialize the connectors and engines
    chronicleConnector = new ChronicleConnector();
    remediationEngine = new RemediationEngine();
    
    // Register a test remediation action
    remediationEngine.registerAction('block-ip-address', async ({ parameters }) => {
      console.log('Mock blocking IP address:', parameters);
      return { success: true };
    });
    
    // Initialize the Chronicle connector with credentials
    if (hasCredentials) {
      try {
        const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
        await chronicleConnector.initialize(credentials);
      } catch (error) {
        console.error('Error initializing Chronicle connector:', error);
      }
    }
  });
  
  // Skip tests if credentials are not available
  const conditionalTest = hasCredentials ? it : it.skip;
  
  conditionalTest('should retrieve alerts from Chronicle', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    const startTime = performance.now();
    
    // Get alerts from Chronicle
    const { alerts, nextPageToken } = await chronicleConnector.getAlerts({
      startTime: config.startTime,
      endTime: config.endTime,
      pageSize: 10
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Retrieved ${alerts.length} alerts in ${duration.toFixed(2)}ms`);
    console.log(`Average time per alert: ${(duration / Math.max(1, alerts.length)).toFixed(2)}ms`);
    
    // Verify alerts
    expect(Array.isArray(alerts)).toBe(true);
    if (alerts.length > 0) {
      const firstAlert = alerts[0];
      expect(firstAlert).toHaveProperty('id');
      expect(firstAlert).toHaveProperty('type');
      expect(firstAlert).toHaveProperty('createdTime');
    }
  }, 30000);
  
  conditionalTest('should normalize alerts efficiently', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get alerts from Chronicle
    const { alerts } = await chronicleConnector.getAlerts({
      startTime: config.startTime,
      endTime: config.endTime,
      pageSize: 10
    });
    
    if (alerts.length === 0) {
      console.log('No alerts to normalize');
      return;
    }
    
    const startTime = performance.now();
    
    // Normalize alerts
    const normalizedAlerts = chronicleConnector.normalizeAlerts(alerts);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Normalized ${alerts.length} alerts in ${duration.toFixed(2)}ms`);
    console.log(`Average time per alert: ${(duration / alerts.length).toFixed(2)}ms`);
    
    // Verify normalized alerts
    expect(Array.isArray(normalizedAlerts)).toBe(true);
    expect(normalizedAlerts.length).toBe(alerts.length);
    
    if (normalizedAlerts.length > 0) {
      const firstNormalized = normalizedAlerts[0];
      expect(firstNormalized).toHaveProperty('id');
      expect(firstNormalized).toHaveProperty('title');
      expect(firstNormalized).toHaveProperty('severity');
      expect(firstNormalized).toHaveProperty('createdAt');
      expect(typeof firstNormalized.createdAt).toBe('number');
    }
    
    // Verify normalization speed
    expect(duration / alerts.length).toBeLessThan(1); // Less than 1ms per alert
  }, 30000);
  
  conditionalTest('should map MITRE ATT&CK techniques to NIST controls', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get alerts from Chronicle
    const { alerts } = await chronicleConnector.getAlerts({
      startTime: config.startTime,
      endTime: config.endTime,
      pageSize: 10
    });
    
    if (alerts.length === 0) {
      console.log('No alerts to map');
      return;
    }
    
    // Find an alert with ATT&CK techniques
    const alertWithTechniques = alerts.find(alert => 
      alert.attackTechniques && alert.attackTechniques.length > 0
    );
    
    if (!alertWithTechniques) {
      console.log('No alerts with ATT&CK techniques found');
      
      // Create a mock alert with techniques for testing
      const mockTechniques = ['T1059', 'T1190', 'T1133'];
      
      const startTime = performance.now();
      
      // Map techniques to NIST controls
      const nistControls = chronicleConnector._mapAttackToNist(mockTechniques);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Log performance metrics
      console.log(`Mapped ${mockTechniques.length} techniques to ${nistControls.length} NIST controls in ${duration.toFixed(2)}ms`);
      
      // Verify mapping
      expect(Array.isArray(nistControls)).toBe(true);
      expect(nistControls.length).toBeGreaterThan(0);
      
      // Verify specific mappings
      expect(nistControls).toContain('AC-4'); // T1190 maps to AC-4
      expect(nistControls).toContain('CM-7'); // T1059 maps to CM-7
      
      return;
    }
    
    const startTime = performance.now();
    
    // Map techniques to NIST controls
    const nistControls = chronicleConnector._mapAttackToNist(alertWithTechniques.attackTechniques);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Mapped ${alertWithTechniques.attackTechniques.length} techniques to ${nistControls.length} NIST controls in ${duration.toFixed(2)}ms`);
    
    // Verify mapping
    expect(Array.isArray(nistControls)).toBe(true);
    expect(nistControls.length).toBeGreaterThan(0);
  }, 30000);
  
  conditionalTest('should search for events in Chronicle', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    const startTime = performance.now();
    
    // Search for events in Chronicle
    const { events, nextPageToken } = await chronicleConnector.searchEvents({
      query: 'principal.ip = "********" OR target.ip = "********"',
      startTime: config.startTime,
      endTime: config.endTime,
      pageSize: 10
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Searched for events in ${duration.toFixed(2)}ms`);
    console.log(`Found ${events ? events.length : 0} events`);
    
    // Verify events (if any)
    if (events && events.length > 0) {
      const firstEvent = events[0];
      expect(firstEvent).toHaveProperty('id');
      expect(firstEvent).toHaveProperty('metadata');
    }
  }, 30000);
  
  conditionalTest('should create and execute threat remediation workflow', async () => {
    // Skip if no credentials
    if (!hasCredentials) {
      return;
    }
    
    // Get alerts from Chronicle
    const { alerts } = await chronicleConnector.getAlerts({
      startTime: config.startTime,
      endTime: config.endTime,
      pageSize: 1
    });
    
    if (alerts.length === 0) {
      console.log('No alerts to remediate');
      
      // Create a mock alert for testing
      const mockAlert = {
        id: 'mock-alert-1',
        type: 'BRUTE_FORCE',
        severity: 'HIGH',
        createdTime: new Date().toISOString(),
        attackTechniques: ['T1110'],
        indicators: [
          { type: 'IP_ADDRESS', value: '*************' }
        ]
      };
      
      const normalizedAlert = {
        id: 'mock-alert-1',
        type: 'brute_force',
        severity: 'high',
        createdAt: Date.now(),
        attackTechniques: ['T1110'],
        nistControls: ['AC-7', 'IA-5'],
        indicators: [
          { type: 'IP_ADDRESS', value: '*************' }
        ]
      };
      
      // Create a remediation scenario
      const remediationScenario = {
        id: `threat-remediation-${Date.now()}`,
        type: 'security',
        severity: normalizedAlert.severity,
        resource: {
          id: normalizedAlert.indicators[0].value,
          type: 'ip_address',
          provider: 'gcp'
        },
        finding: normalizedAlert,
        remediationSteps: [
          {
            id: 'step-1',
            action: 'block-ip-address',
            parameters: {
              ipAddress: normalizedAlert.indicators[0].value,
              duration: '24h',
              reason: 'Brute force attack detected'
            }
          }
        ]
      };
      
      const startTime = performance.now();
      
      // Execute the remediation
      const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Log performance metrics
      console.log(`Executed threat remediation in ${duration.toFixed(2)}ms`);
      
      // Verify remediation result
      expect(remediationResult).toHaveProperty('id');
      expect(remediationResult).toHaveProperty('status');
      expect(remediationResult).toHaveProperty('steps');
      expect(remediationResult.steps.length).toBe(1);
      expect(remediationResult.steps[0].success).toBe(true);
      
      // Verify remediation speed
      expect(duration).toBeLessThan(8000); // Less than 8 seconds
      
      return;
    }
    
    // Normalize the alert
    const normalizedAlerts = chronicleConnector.normalizeAlerts(alerts);
    const alert = normalizedAlerts[0];
    
    // Check if the alert has indicators
    if (!alert.indicators || alert.indicators.length === 0) {
      console.log('Alert has no indicators to remediate');
      return;
    }
    
    // Find an IP address indicator
    const ipIndicator = alert.indicators.find(ind => ind.type === 'IP_ADDRESS');
    
    if (!ipIndicator) {
      console.log('No IP address indicator found');
      return;
    }
    
    // Create a remediation scenario
    const remediationScenario = {
      id: `threat-remediation-${Date.now()}`,
      type: 'security',
      severity: alert.severity,
      resource: {
        id: ipIndicator.value,
        type: 'ip_address',
        provider: 'gcp'
      },
      finding: alert,
      remediationSteps: [
        {
          id: 'step-1',
          action: 'block-ip-address',
          parameters: {
            ipAddress: ipIndicator.value,
            duration: '24h',
            reason: `${alert.type} alert detected`
          }
        }
      ]
    };
    
    const startTime = performance.now();
    
    // Execute the remediation
    const remediationResult = await remediationEngine.executeRemediation(remediationScenario);
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Log performance metrics
    console.log(`Executed threat remediation in ${duration.toFixed(2)}ms`);
    
    // Verify remediation result
    expect(remediationResult).toHaveProperty('id');
    expect(remediationResult).toHaveProperty('status');
    expect(remediationResult).toHaveProperty('steps');
    expect(remediationResult.steps.length).toBe(1);
    expect(remediationResult.steps[0].success).toBe(true);
    
    // Verify remediation speed
    expect(duration).toBeLessThan(8000); // Less than 8 seconds
  }, 30000);
});
