@echo off
REM NovaActuary™ Manual Docker Build Script
REM Run this script to build the NovaActuary™ Docker container

echo.
echo 🚀 NOVAACTUARY™ DOCKER BUILD SCRIPT
echo The ∂Ψ=0 Underwriting Revolution
echo ===================================
echo.

REM Display current directory
echo Current directory: %CD%
echo.

REM Check if we're in the right directory
if not exist "Dockerfile" (
    echo ❌ Dockerfile not found in current directory
    echo Please navigate to: src\novaactuary
    echo Then run this script again
    pause
    exit /b 1
)

if not exist "docker-compose.yml" (
    echo ❌ docker-compose.yml not found in current directory
    echo Please navigate to: src\novaactuary
    echo Then run this script again
    pause
    exit /b 1
)

echo ✅ Found Dockerfile and docker-compose.yml
echo.

REM Check Docker Desktop
echo [STEP 1] Checking Docker Desktop...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Desktop not found or not running
    echo.
    echo Please:
    echo 1. Install Docker Desktop if not installed
    echo 2. Start Docker Desktop
    echo 3. Wait for it to fully initialize
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ Docker Desktop is available
docker --version
echo.

REM Check Docker Compose
echo [STEP 2] Checking Docker Compose...
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose not available
    pause
    exit /b 1
)

echo ✅ Docker Compose is available
docker-compose --version
echo.

REM Clean up existing containers
echo [STEP 3] Cleaning up existing containers...
docker-compose down --remove-orphans >nul 2>&1
echo ✅ Cleanup completed
echo.

REM Build the container
echo [STEP 4] Building NovaActuary™ container...
echo This will take 5-10 minutes on first build...
echo Building with full output for debugging...
echo.

docker-compose build --no-cache --progress=plain novaactuary

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ NOVAACTUARY™ CONTAINER BUILD SUCCESS!
    echo ========================================
    echo.
    
    REM Show built images
    echo 📦 Built Images:
    docker images | findstr novaactuary
    echo.
    
    echo 🚀 Next Steps:
    echo.
    echo 1. Start the container:
    echo    docker-compose up -d novaactuary
    echo.
    echo 2. Check container status:
    echo    docker-compose ps
    echo.
    echo 3. Run health check:
    echo    docker-compose exec novaactuary node novaactuary/health-check.js
    echo.
    echo 4. Run quick test:
    echo    docker-compose exec novaactuary node novaactuary/quick-test.js
    echo.
    echo 5. View logs:
    echo    docker-compose logs novaactuary
    echo.
    echo 6. Stop container:
    echo    docker-compose down
    echo.
    
) else (
    echo.
    echo ========================================
    echo ❌ NOVAACTUARY™ CONTAINER BUILD FAILED!
    echo ========================================
    echo.
    echo Common solutions:
    echo.
    echo 1. Ensure Docker Desktop is running
    echo 2. Check available disk space (need 2GB+)
    echo 3. Check internet connection
    echo 4. Try running as Administrator
    echo 5. Restart Docker Desktop and try again
    echo.
    echo If problems persist, check the error messages above.
    echo.
)

echo Press any key to continue...
pause >nul
