<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Patent Diagram Template</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            box-shadow: none; /* No shadow for patent diagrams */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
        /* Different patterns for different element types instead of colors */
        .pattern-1 {
            background-color: white;
            border: 2px solid black;
        }
        .pattern-2 {
            background-color: white;
            border: 2px solid black;
            border-style: dashed;
        }
        .pattern-3 {
            background-color: white;
            border: 2px solid black;
            border-style: double;
        }
        .pattern-4 {
            background-color: white;
            border: 2px solid black;
            border-width: 3px;
        }
        .pattern-5 {
            background-color: white;
            border: 2px solid black;
            border-style: dotted;
        }
    </style>
</head>
<body>
    <h1>DIAGRAM TITLE</h1>
    
    <div class="diagram-container">
        <!-- Elements will be placed here -->
        
        <!-- Example element with proper spacing and numbering -->
        <div class="element pattern-1" style="top: 50px; left: 350px; width: 300px; font-weight: bold; font-size: 20px;">
            Element Title
            <div class="element-number">1</div>
        </div>
        
        <!-- Example element with formula -->
        <div class="element pattern-2" style="top: 150px; left: 350px; width: 300px; font-size: 16px;">
            <span class="bold-formula">U=T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]</span>
            <div class="element-number">2</div>
        </div>
        
        <!-- Example connection -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px;"></div>
        <div class="arrow" style="top: 140px; left: 495px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
    </div>
</body>
</html>
