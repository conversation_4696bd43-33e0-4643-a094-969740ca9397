"""
ComphyonΨᶜ Governor - Control system for managing emergent intelligence.

The ComphyonΨᶜ Governor is the control layer of the ComphyonΨᶜ Framework,
providing mechanisms to manage and regulate emergent intelligence based on
ComphyonΨᶜ measurements.
"""

from .controller import Comphyon<PERSON><PERSON>roller
from .safety import SafetyProtocols
from .feedback import FeedbackSystem

__version__ = '0.1.0'

class ComphyonGovernor:
    """
    Main class for the ComphyonΨᶜ Governor.
    
    Provides methods to control and regulate emergent intelligence based on
    ComphyonΨᶜ metrics.
    """
    
    def __init__(self, meter=None, thresholds=None):
        """
        Initialize the ComphyonΨᶜ Governor.
        
        Args:
            meter: Optional ComphyonMeter instance
            thresholds: Optional dictionary of thresholds for different metrics
        """
        self.controller = ComphyonController()
        self.safety = SafetyProtocols()
        self.feedback = FeedbackSystem()
        
        self.meter = meter
        self.thresholds = thresholds or {
            'velocity': 100.0,
            'acceleration': 2.5
        }
        
        self.control_history = []
    
    def set_threshold(self, metric, value):
        """
        Set a threshold for a specific metric.
        
        Args:
            metric: Metric name (e.g., 'velocity', 'acceleration')
            value: Threshold value
        """
        self.thresholds[metric] = value
    
    def regulate(self, metrics):
        """
        Regulate the system based on ComphyonΨᶜ metrics.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
            
        Returns:
            dict: Control actions to be applied
        """
        # Determine if any thresholds are exceeded
        threshold_exceeded = False
        exceeded_metrics = []
        
        if metrics['velocity'] > self.thresholds.get('velocity', float('inf')):
            threshold_exceeded = True
            exceeded_metrics.append('velocity')
        
        if metrics['acceleration'] > self.thresholds.get('acceleration', float('inf')):
            threshold_exceeded = True
            exceeded_metrics.append('acceleration')
        
        # Generate control actions if needed
        if threshold_exceeded:
            control_actions = self.controller.generate_control_actions(
                metrics, exceeded_metrics, self.thresholds)
            
            # Apply safety protocols
            self.safety.apply_safety_protocols(control_actions, metrics)
            
            # Record the control action
            self.log_control_event(metrics, control_actions)
            
            return control_actions
        
        return {'type': 'none', 'actions': []}
    
    def log_control_event(self, metrics, control_actions):
        """
        Log a control event.
        
        Args:
            metrics: Metrics that triggered the control action
            control_actions: Control actions applied
        """
        event = {
            'timestamp': metrics['timestamp'],
            'metrics': metrics,
            'control_actions': control_actions
        }
        
        self.control_history.append(event)
        
        # Limit history size
        if len(self.control_history) > 1000:
            self.control_history.pop(0)
    
    def get_control_history(self):
        """
        Get the control history.
        
        Returns:
            list: Control history
        """
        return self.control_history
