{"name": "novafuse-ui", "version": "1.0.0", "description": "Frontend UI for NovaConnect", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"react": "^17.0.2", "react-dom": "^17.0.2", "react-router-dom": "^6.2.2", "react-scripts": "5.0.0", "axios": "^0.26.1", "@mui/material": "^5.5.1", "@mui/icons-material": "^5.5.1", "@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.4", "@testing-library/user-event": "^13.5.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}