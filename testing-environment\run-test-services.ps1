# Start all test services

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Green
npm install express cors body-parser axios crypto

# Start Mock API Service
Start-Process -FilePath "node" -ArgumentList "test-mock-api.js" -NoNewWindow
Write-Host "Mock API Service started on port 3005" -ForegroundColor Green

# Start Connector Registry Service
Start-Process -FilePath "node" -ArgumentList "test-connector-registry.js" -NoNewWindow
Write-Host "Connector Registry Service started on port 3006" -ForegroundColor Green

# Start Authentication Service
Start-Process -FilePath "node" -ArgumentList "test-auth-service.js" -NoNewWindow
Write-Host "Authentication Service started on port 3007" -ForegroundColor Green

# Start Connector Executor Service
Start-Process -FilePath "node" -ArgumentList "test-connector-executor.js" -NoNewWindow
Write-Host "Connector Executor Service started on port 3008" -ForegroundColor Green

# Start Usage Metering Service
Start-Process -FilePath "node" -ArgumentList "test-usage-metering.js" -NoNewWindow
Write-Host "Usage Metering Service started on port 3009" -ForegroundColor Green

Write-Host "All services started successfully!" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop all services" -ForegroundColor Yellow

# Keep the script running
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
}
finally {
    # This block will execute when Ctrl+C is pressed
    Write-Host "Stopping all services..." -ForegroundColor Yellow
    Get-Process -Name "node" | Where-Object { $_.CommandLine -like "*test-*" } | Stop-Process
    Write-Host "All services stopped" -ForegroundColor Green
}
