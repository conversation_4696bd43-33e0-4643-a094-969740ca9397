/**
 * Test script for tenant-specific audit logging
 * 
 * This script simulates API requests with different tenant IDs to verify
 * that the audit logging system correctly captures tenant information.
 */

const AuditService = require('../api/services/AuditService');
const path = require('path');
const fs = require('fs').promises;

// Create a temporary directory for testing
const testDir = path.join(__dirname, '../test-data');

// Initialize the audit service with the test directory
const auditService = new AuditService(testDir);

// Mock tenant IDs for testing
const tenants = ['tenant1', 'tenant2', 'tenant3'];

// Simulate API requests for different tenants
async function simulateRequests() {
  console.log('Simulating API requests for multiple tenants...');
  
  // Create test directory
  try {
    await fs.mkdir(testDir, { recursive: true });
  } catch (error) {
    console.error('Error creating test directory:', error);
  }
  
  // Simulate requests for each tenant
  for (const tenantId of tenants) {
    console.log(`\nSimulating requests for tenant: ${tenantId}`);
    
    // Simulate GET request
    await simulateRequest(tenantId, 'GET', 'users', null);
    
    // Simulate POST request
    await simulateRequest(tenantId, 'POST', 'connectors', { name: 'Test Connector', type: 'api' });
    
    // Simulate PUT request
    await simulateRequest(tenantId, 'PUT', 'workflows', { id: '123', status: 'active' });
    
    // Simulate DELETE request
    await simulateRequest(tenantId, 'DELETE', 'tokens', '456');
  }
  
  // Simulate a request without tenant ID
  console.log('\nSimulating request without tenant ID');
  await simulateRequest(null, 'GET', 'health', null);
  
  // Check the audit logs
  await checkAuditLogs();
  
  // Clean up
  await cleanup();
}

// Simulate a single API request
async function simulateRequest(tenantId, method, resourceType, data) {
  const userId = `user-${Math.floor(Math.random() * 1000)}`;
  const resourceId = data && data.id ? data.id : (method === 'DELETE' ? data : null);
  
  console.log(`  ${method} /${resourceType}${resourceId ? `/${resourceId}` : ''}`);
  
  const auditData = {
    userId,
    action: method,
    resourceType,
    resourceId,
    details: {
      path: `/${resourceType}${resourceId ? `/${resourceId}` : ''}`,
      query: {},
      body: method !== 'GET' ? data : null
    },
    ip: '127.0.0.1',
    userAgent: 'NovaConnect-Test/1.0',
    status: 'success',
    teamId: `team-${Math.floor(Math.random() * 100)}`,
    environmentId: 'test',
    tenantId
  };
  
  if (tenantId) {
    await auditService.logTenantEvent(tenantId, auditData);
  } else {
    await auditService.logEvent(auditData);
  }
}

// Check the audit logs
async function checkAuditLogs() {
  console.log('\nChecking audit logs...');
  
  // Get all audit logs
  const allLogs = await auditService.getAuditLogs();
  console.log(`Total audit logs: ${allLogs.total}`);
  
  // Check logs for each tenant
  for (const tenantId of tenants) {
    const tenantLogs = allLogs.logs.filter(log => log.tenantId === tenantId);
    console.log(`Logs for tenant ${tenantId}: ${tenantLogs.length}`);
    
    // Print sample log
    if (tenantLogs.length > 0) {
      console.log('  Sample log:');
      console.log(`    ID: ${tenantLogs[0].id}`);
      console.log(`    Action: ${tenantLogs[0].action}`);
      console.log(`    Resource: ${tenantLogs[0].resourceType}`);
      console.log(`    Tenant ID: ${tenantLogs[0].tenantId}`);
    }
  }
  
  // Check logs without tenant ID
  const noTenantLogs = allLogs.logs.filter(log => !log.tenantId);
  console.log(`Logs without tenant ID: ${noTenantLogs.length}`);
}

// Clean up test data
async function cleanup() {
  console.log('\nCleaning up test data...');
  
  try {
    // Remove audit log file
    await fs.unlink(path.join(testDir, 'audit', 'audit_log.json'));
    
    // Remove audit directory
    await fs.rmdir(path.join(testDir, 'audit'));
    
    // Remove test directory
    await fs.rmdir(testDir);
    
    console.log('Test data cleaned up successfully.');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

// Run the simulation
simulateRequests().catch(error => {
  console.error('Error running simulation:', error);
});
