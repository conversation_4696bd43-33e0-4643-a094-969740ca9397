/**
 * Dashboard
 * 
 * This module exports all components of the Dashboard system.
 * The Dashboard provides real-time visualization of the Comphyology framework.
 */

const Dashboard = require('./dashboard');
const NovaVisionIntegration = require('./nova-vision-integration');

/**
 * Create a basic Dashboard system
 * @param {Object} options - Configuration options
 * @returns {Object} - Dashboard system components
 */
function createDashboardSystem(options = {}) {
  // Create components
  const dashboard = new Dashboard(options);
  
  return {
    dashboard
  };
}

/**
 * Create an enhanced Dashboard system with integrated components
 * @param {Object} options - Configuration options
 * @param {Object} meter - Meter system
 * @param {Object} bridge - Bridge system
 * @param {Object} governor - Governor system
 * @returns {Object} - Enhanced Dashboard system
 */
function createEnhancedDashboardSystem(options = {}, meter = null, bridge = null, governor = null) {
  // Create basic system
  const dashboardSystem = createDashboardSystem(options);
  
  // Set up meter integration if provided
  if (meter) {
    _integrateMeter(dashboardSystem, meter, options);
  }
  
  // Set up bridge integration if provided
  if (bridge) {
    _integrateBridge(dashboardSystem, bridge, options);
  }
  
  // Set up governor integration if provided
  if (governor) {
    _integrateGovernor(dashboardSystem, governor, options);
  }
  
  // Add enhanced methods
  const enhancedSystem = {
    ...dashboardSystem,
    
    /**
     * Start all components
     * @returns {Promise<boolean>} - Success status
     */
    async start() {
      return dashboardSystem.dashboard.start();
    },
    
    /**
     * Stop all components
     * @returns {boolean} - Success status
     */
    stop() {
      return dashboardSystem.dashboard.stop();
    },
    
    /**
     * Render universal entropy dashboard
     * @param {Object} target - Target element
     * @returns {boolean} - Success status
     */
    renderUniversalEntropyDashboard(target) {
      return dashboardSystem.dashboard.render('universal-entropy', target);
    },
    
    /**
     * Render cross-domain risk dashboard
     * @param {Object} target - Target element
     * @returns {boolean} - Success status
     */
    renderCrossDomainRiskDashboard(target) {
      return dashboardSystem.dashboard.render('cross-domain-risk', target);
    },
    
    /**
     * Get metrics
     * @returns {Object} - Metrics
     */
    getMetrics() {
      return dashboardSystem.dashboard.getMetrics();
    }
  };
  
  return enhancedSystem;
}

/**
 * Integrate Meter with Dashboard
 * @param {Object} dashboardSystem - Dashboard system
 * @param {Object} meter - Meter system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateMeter(dashboardSystem, meter, options = {}) {
  if (!meter) return;
  
  try {
    // Listen for entropy updates from Meter
    if (meter.universalEntropyMeasurement && typeof meter.universalEntropyMeasurement.on === 'function') {
      meter.universalEntropyMeasurement.on('entropy-update', (data) => {
        // Update dashboard with entropy data
        dashboardSystem.dashboard.update({
          universalEntropy: data.universalEntropy,
          domainEntropy: data.domainEntropy,
          comphyonValue: meter.universalEntropyMeasurement.calculateComphyon(),
          timestamp: data.timestamp
        });
      });
    }
    
    // Listen for alerts from Meter
    if (meter.alertingSystem && typeof meter.alertingSystem.on === 'function') {
      meter.alertingSystem.on('alert-processed', (alert) => {
        // Update dashboard with active alerts
        dashboardSystem.dashboard.update({
          activeAlerts: meter.alertingSystem.getActiveAlerts()
        });
      });
    }
    
    // Listen for dashboard update interval
    dashboardSystem.dashboard.on('update-interval', () => {
      if (meter.universalEntropyMeasurement) {
        // Get current entropy data
        const universalEntropy = meter.universalEntropyMeasurement.getUniversalEntropy();
        const cyberEntropy = meter.universalEntropyMeasurement.getDomainEntropy('cyber');
        const financialEntropy = meter.universalEntropyMeasurement.getDomainEntropy('financial');
        const biologicalEntropy = meter.universalEntropyMeasurement.getDomainEntropy('biological');
        const comphyonValue = meter.universalEntropyMeasurement.calculateComphyon();
        
        // Update dashboard with entropy data
        dashboardSystem.dashboard.update({
          universalEntropy,
          domainEntropy: {
            cyber: cyberEntropy,
            financial: financialEntropy,
            biological: biologicalEntropy
          },
          comphyonValue,
          timestamp: Date.now()
        });
      }
      
      if (meter.alertingSystem) {
        // Update dashboard with active alerts
        dashboardSystem.dashboard.update({
          activeAlerts: meter.alertingSystem.getActiveAlerts()
        });
      }
    });
  } catch (error) {
    if (options.enableLogging) {
      console.error(`Dashboard: Error integrating with Meter: ${error.message}`);
    }
  }
}

/**
 * Integrate Bridge with Dashboard
 * @param {Object} dashboardSystem - Dashboard system
 * @param {Object} bridge - Bridge system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateBridge(dashboardSystem, bridge, options = {}) {
  if (!bridge) return;
  
  try {
    // Listen for risk updates from Bridge
    if (bridge.unifiedRiskScoring && typeof bridge.unifiedRiskScoring.on === 'function') {
      bridge.unifiedRiskScoring.on('risk-update', (data) => {
        // Update dashboard with risk data
        dashboardSystem.dashboard.update({
          unifiedRiskScore: data.unifiedRiskScore,
          domainRiskScores: data.domainRiskScores,
          riskStatus: data.riskStatus,
          timestamp: data.timestamp
        });
      });
    }
    
    // Listen for dashboard update interval
    dashboardSystem.dashboard.on('update-interval', () => {
      if (bridge.unifiedRiskScoring) {
        // Get current risk data
        const unifiedRiskScore = bridge.unifiedRiskScoring.getUnifiedRiskScore();
        const domainRiskScores = bridge.unifiedRiskScoring.getDomainRiskScores();
        const riskStatus = bridge.unifiedRiskScoring.getRiskStatus();
        
        // Update dashboard with risk data
        dashboardSystem.dashboard.update({
          unifiedRiskScore,
          domainRiskScores,
          riskStatus,
          timestamp: Date.now()
        });
      }
    });
  } catch (error) {
    if (options.enableLogging) {
      console.error(`Dashboard: Error integrating with Bridge: ${error.message}`);
    }
  }
}

/**
 * Integrate Governor with Dashboard
 * @param {Object} dashboardSystem - Dashboard system
 * @param {Object} governor - Governor system
 * @param {Object} options - Configuration options
 * @private
 */
function _integrateGovernor(dashboardSystem, governor, options = {}) {
  if (!governor) return;
  
  try {
    // Listen for policy violations from Governor
    if (governor.policyEnforcement && typeof governor.policyEnforcement.on === 'function') {
      governor.policyEnforcement.on('policy-violation', (data) => {
        // Update dashboard with policy violation data
        // In a real implementation, this would update a policy violations section of the dashboard
        if (options.enableLogging) {
          console.log(`Dashboard: Policy violation detected: ${data.policyId} (${data.policyName})`);
        }
      });
    }
    
    // Listen for actions from Governor
    if (governor.controlActionExecution && typeof governor.controlActionExecution.on === 'function') {
      governor.controlActionExecution.on('action-completed', (data) => {
        // Update dashboard with action data
        // In a real implementation, this would update an actions section of the dashboard
        if (options.enableLogging) {
          console.log(`Dashboard: Action completed: ${data.actionId} (${data.name})`);
        }
      });
    }
  } catch (error) {
    if (options.enableLogging) {
      console.error(`Dashboard: Error integrating with Governor: ${error.message}`);
    }
  }
}

module.exports = {
  Dashboard,
  NovaVisionIntegration,
  createDashboardSystem,
  createEnhancedDashboardSystem
};
