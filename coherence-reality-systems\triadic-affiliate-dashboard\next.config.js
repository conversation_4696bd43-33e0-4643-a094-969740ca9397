/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: ['localhost'],
  },
  devIndicators: {
    buildActivity: false,
  },
  serverRuntimeConfig: {
    port: 3006
  },
  server: {
    port: 3006
  },
  webpack: (config) => {
    config.resolve.fallback = { fs: false, path: false };
    config.module.rules.push({
      test: /\.js$/, 
      use: ['babel-loader'],
      exclude: /node_modules/,
    });
    return config;
  },
  env: {
    WS_URL: 'ws://localhost:8080'
  },
  experimental: {
    esmExternals: false
  },
};

module.exports = nextConfig;
