import React from 'react';
import { motion } from 'framer-motion';
import { staggerContainer, fadeInUp } from '../utils/animations';

/**
 * Animated list component with staggered item animations
 * @param {Object} props - Component props
 * @param {React.ReactNode[]} props.items - List items
 * @param {string} props.className - Additional CSS classes for the list container
 * @param {string} props.itemClassName - Additional CSS classes for list items
 * @param {number} props.staggerDuration - Stagger duration in milliseconds
 * @param {number} props.initialDelay - Initial delay in milliseconds
 * @param {Function} props.renderItem - Custom render function for list items
 */
const AnimatedList = ({ 
  items, 
  className = '', 
  itemClassName = '',
  staggerDuration = 100, 
  initialDelay = 0,
  renderItem
}) => {
  // Container animation
  const containerAnimation = staggerContainer(staggerDuration, initialDelay);
  
  // Item animation
  const itemAnimation = fadeInUp(500);

  return (
    <motion.ul
      className={className}
      variants={containerAnimation}
      initial="initial"
      animate="animate"
    >
      {items.map((item, index) => (
        <motion.li
          key={index}
          className={itemClassName}
          variants={itemAnimation}
        >
          {renderItem ? renderItem(item, index) : item}
        </motion.li>
      ))}
    </motion.ul>
  );
};

export default AnimatedList;
