/**
 * Integration tests for the APIs, iPaaS & Developer Tools Connector
 */

const nock = require('nock');
const ApisIpaasDeveloperToolsConnector = require('../../../../connector/implementations/apis-ipaas-developer-tools');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('ApisIpaasDeveloperToolsConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();
    
    // Create connector instance
    connector = new ApisIpaasDeveloperToolsConnector({
      baseUrl
    }, {
      apiKey: 'test-api-key',
      apiKeyHeader: 'X-API-Key'
    });
  });
  
  describe('API Management', () => {
    it('should list APIs', async () => {
      // Mock APIs endpoint
      const mockApis = {
        data: [
          {
            id: 'api-1',
            name: 'Customer API',
            type: 'rest',
            status: 'active',
            baseUrl: 'https://api.example.com/customers'
          },
          {
            id: 'api-2',
            name: 'Product API',
            type: 'rest',
            status: 'active',
            baseUrl: 'https://api.example.com/products'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/apis')
        .query({ status: 'active', type: 'rest' })
        .reply(200, mockApis);
      
      // Initialize connector
      await connector.initialize();
      
      // List APIs
      const result = await connector.listApis({ status: 'active', type: 'rest' });
      
      // Verify result
      expect(result).toEqual(mockApis);
    });
    
    it('should get a specific API', async () => {
      // Mock API endpoint
      const mockApi = {
        id: 'api-123',
        name: 'Customer API',
        description: 'API for managing customer data',
        version: '1.0.0',
        type: 'rest',
        status: 'active',
        baseUrl: 'https://api.example.com/customers',
        endpoints: [
          {
            id: 'endpoint-1',
            path: '/customers',
            method: 'GET',
            description: 'List customers'
          }
        ]
      };
      
      nock(baseUrl)
        .get('/apis/api-123')
        .reply(200, mockApi);
      
      // Initialize connector
      await connector.initialize();
      
      // Get API
      const result = await connector.getApi('api-123');
      
      // Verify result
      expect(result).toEqual(mockApi);
    });
    
    it('should create a new API', async () => {
      // API data
      const apiData = {
        name: 'New API',
        description: 'New API description',
        type: 'rest',
        baseUrl: 'https://api.example.com/new'
      };
      
      // Mock response
      const mockResponse = {
        id: 'api-new',
        ...apiData,
        status: 'draft',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      
      nock(baseUrl)
        .post('/apis', apiData)
        .reply(201, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Create API
      const result = await connector.createApi(apiData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should update an existing API', async () => {
      // API update data
      const apiId = 'api-123';
      const updateData = {
        name: 'Updated API',
        description: 'Updated description',
        status: 'active'
      };
      
      // Mock response
      const mockResponse = {
        id: apiId,
        name: 'Updated API',
        description: 'Updated description',
        status: 'active',
        type: 'rest',
        baseUrl: 'https://api.example.com/customers',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      
      nock(baseUrl)
        .put(`/apis/${apiId}`, updateData)
        .reply(200, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Update API
      const result = await connector.updateApi(apiId, updateData);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
    
    it('should delete an API', async () => {
      // API ID
      const apiId = 'api-123';
      
      nock(baseUrl)
        .delete(`/apis/${apiId}`)
        .reply(204);
      
      // Initialize connector
      await connector.initialize();
      
      // Delete API
      await connector.deleteApi(apiId);
      
      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  
  describe('Integration Management', () => {
    it('should list integrations', async () => {
      // Mock integrations endpoint
      const mockIntegrations = {
        data: [
          {
            id: 'integration-1',
            name: 'Customer Data Sync',
            status: 'active',
            sourceSystem: 'CRM System',
            targetSystem: 'ERP System'
          },
          {
            id: 'integration-2',
            name: 'Order Processing',
            status: 'active',
            sourceSystem: 'E-commerce Platform',
            targetSystem: 'Fulfillment System'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/integrations')
        .query({ status: 'active' })
        .reply(200, mockIntegrations);
      
      // Initialize connector
      await connector.initialize();
      
      // List integrations
      const result = await connector.listIntegrations({ status: 'active' });
      
      // Verify result
      expect(result).toEqual(mockIntegrations);
    });
    
    it('should get a specific integration', async () => {
      // Mock integration endpoint
      const mockIntegration = {
        id: 'integration-123',
        name: 'Customer Data Sync',
        description: 'Synchronize customer data between CRM and ERP',
        status: 'active',
        type: 'scheduled',
        sourceSystem: {
          id: 'system-1',
          name: 'CRM System'
        },
        targetSystem: {
          id: 'system-2',
          name: 'ERP System'
        }
      };
      
      nock(baseUrl)
        .get('/integrations/integration-123')
        .reply(200, mockIntegration);
      
      // Initialize connector
      await connector.initialize();
      
      // Get integration
      const result = await connector.getIntegration('integration-123');
      
      // Verify result
      expect(result).toEqual(mockIntegration);
    });
    
    it('should execute an integration', async () => {
      // Integration ID
      const integrationId = 'integration-123';
      
      // Execution options
      const options = {
        parameters: {
          startDate: '2023-06-01',
          endDate: '2023-06-02'
        },
        async: true
      };
      
      // Mock response
      const mockResponse = {
        executionId: 'exec-123',
        status: 'queued',
        startTime: '2023-06-02T10:15:00Z',
        estimatedCompletionTime: '2023-06-02T10:20:00Z',
        statusUrl: `https://api.test.com/integrations/${integrationId}/executions/exec-123`
      };
      
      nock(baseUrl)
        .post(`/integrations/${integrationId}/execute`, options)
        .reply(202, mockResponse);
      
      // Initialize connector
      await connector.initialize();
      
      // Execute integration
      const result = await connector.executeIntegration(integrationId, options);
      
      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  
  describe('Developer Tools', () => {
    it('should list developer tools', async () => {
      // Mock developer tools endpoint
      const mockTools = {
        data: [
          {
            id: 'tool-1',
            name: 'API Tester',
            category: 'testing',
            version: '2.1.0'
          },
          {
            id: 'tool-2',
            name: 'Schema Validator',
            category: 'testing',
            version: '1.5.0'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      
      nock(baseUrl)
        .get('/developer-tools')
        .query({ category: 'testing' })
        .reply(200, mockTools);
      
      // Initialize connector
      await connector.initialize();
      
      // List developer tools
      const result = await connector.listDeveloperTools({ category: 'testing' });
      
      // Verify result
      expect(result).toEqual(mockTools);
    });
    
    it('should get a specific developer tool', async () => {
      // Mock developer tool endpoint
      const mockTool = {
        id: 'tool-123',
        name: 'API Tester',
        description: 'Tool for testing APIs',
        category: 'testing',
        version: '2.1.0',
        url: 'https://tools.example.com/api-tester'
      };
      
      nock(baseUrl)
        .get('/developer-tools/tool-123')
        .reply(200, mockTool);
      
      // Initialize connector
      await connector.initialize();
      
      // Get developer tool
      const result = await connector.getDeveloperTool('tool-123');
      
      // Verify result
      expect(result).toEqual(mockTool);
    });
  });
  
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Mock authentication error
      nock(baseUrl)
        .get('/apis')
        .reply(401, {
          error: 'unauthorized',
          error_description: 'Invalid API key'
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to list APIs
      await expect(connector.listApis()).rejects.toThrow('Error listing APIs');
    });
    
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl)
        .get('/apis/non-existent')
        .reply(404, {
          error: 'not_found',
          error_description: 'API not found'
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to get non-existent API
      await expect(connector.getApi('non-existent')).rejects.toThrow('Error getting API');
    });
    
    it('should handle validation errors', async () => {
      // API data with missing required fields
      const invalidData = {
        name: 'Invalid API'
        // Missing required fields: type, baseUrl
      };
      
      // Mock validation error
      nock(baseUrl)
        .post('/apis', invalidData)
        .reply(400, {
          error: 'validation_error',
          error_description: 'Validation failed',
          errors: [
            { field: 'type', message: 'Type is required' },
            { field: 'baseUrl', message: 'Base URL is required' }
          ]
        });
      
      // Initialize connector
      await connector.initialize();
      
      // Try to create invalid API
      await expect(connector.createApi(invalidData)).rejects.toThrow('type is required');
    });
  });
});
