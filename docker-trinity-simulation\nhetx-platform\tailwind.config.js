/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // NHET-X Consciousness Color Palette
        consciousness: {
          primary: 'rgb(0, 255, 255)', // <PERSON>an
          secondary: 'rgb(255, 0, 255)', // Magenta
          tertiary: 'rgb(255, 255, 0)', // Yellow
          bg: 'rgb(0, 0, 0)', // Black
          surface: 'rgb(20, 20, 40)', // Dark Blue
        },
        // Trinity Colors
        psi: 'rgb(0, 255, 255)', // Spatial - <PERSON>an
        phi: 'rgb(255, 0, 255)', // Temporal - Magenta
        theta: 'rgb(255, 255, 0)', // Recursive - Yellow
      },
      backgroundImage: {
        'consciousness-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'reality-gradient': 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4)',
        'trinity-gradient': 'linear-gradient(45deg, rgb(0, 255, 255), rgb(255, 0, 255), rgb(255, 255, 0))',
      },
      animation: {
        'consciousness-pulse': 'consciousness-pulse 3s ease-in-out infinite',
        'reality-shift': 'reality-shift 10s ease-in-out infinite',
        'float': 'float 15s ease-in-out infinite',
        'quantum-entanglement': 'quantum-entanglement 8s linear infinite',
        'fractal-superposition': 'fractal-superposition 6s ease-in-out infinite',
      },
      keyframes: {
        'consciousness-pulse': {
          '0%, 100%': { 
            opacity: '0.3',
            transform: 'scale(1)',
          },
          '50%': { 
            opacity: '1',
            transform: 'scale(1.05)',
          },
        },
        'reality-shift': {
          '0%': { filter: 'hue-rotate(0deg)' },
          '25%': { filter: 'hue-rotate(90deg)' },
          '50%': { filter: 'hue-rotate(180deg)' },
          '75%': { filter: 'hue-rotate(270deg)' },
          '100%': { filter: 'hue-rotate(360deg)' },
        },
        'float': {
          '0%, 100%': {
            transform: 'translateY(0px) translateX(0px)',
            opacity: '0.3',
          },
          '25%': {
            transform: 'translateY(-20px) translateX(10px)',
            opacity: '0.7',
          },
          '50%': {
            transform: 'translateY(-40px) translateX(-5px)',
            opacity: '1',
          },
          '75%': {
            transform: 'translateY(-20px) translateX(-15px)',
            opacity: '0.7',
          },
        },
        'quantum-entanglement': {
          '0%': {
            transform: 'rotate(0deg) scale(1)',
            borderColor: 'rgb(0, 255, 255)',
          },
          '33%': {
            transform: 'rotate(120deg) scale(1.1)',
            borderColor: 'rgb(255, 0, 255)',
          },
          '66%': {
            transform: 'rotate(240deg) scale(0.9)',
            borderColor: 'rgb(255, 255, 0)',
          },
          '100%': {
            transform: 'rotate(360deg) scale(1)',
            borderColor: 'rgb(0, 255, 255)',
          },
        },
        'fractal-superposition': {
          '0%, 100%': {
            background: 'linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(255, 0, 255, 0.1), rgba(255, 255, 0, 0.1))',
          },
          '50%': {
            background: 'linear-gradient(225deg, rgba(255, 255, 0, 0.2), rgba(0, 255, 255, 0.2), rgba(255, 0, 255, 0.2))',
          },
        },
      },
      fontFamily: {
        'consciousness': ['Courier New', 'monospace'],
      },
      backdropBlur: {
        'consciousness': '15px',
      },
      boxShadow: {
        'consciousness': '0 0 30px rgba(0, 255, 255, 0.3)',
        'reality': '0 0 50px rgba(255, 0, 255, 0.4)',
        'trinity': '0 0 40px rgba(255, 255, 0, 0.3)',
      },
    },
  },
  plugins: [
    // Custom plugin for consciousness-specific utilities
    function({ addUtilities }) {
      const newUtilities = {
        '.text-consciousness': {
          background: 'linear-gradient(45deg, rgb(0, 255, 255), rgb(255, 0, 255), rgb(255, 255, 0))',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
        },
        '.glass-morphism': {
          background: 'rgba(255, 255, 255, 0.05)',
          'backdrop-filter': 'blur(15px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.consciousness-card': {
          background: 'rgba(255, 255, 255, 0.05)',
          'backdrop-filter': 'blur(15px)',
          border: '1px solid rgba(0, 255, 255, 0.3)',
          'border-radius': '12px',
          padding: '24px',
          'box-shadow': '0 8px 32px rgba(0, 255, 255, 0.1)',
          transition: 'all 0.3s ease',
        },
        '.consciousness-card:hover': {
          'box-shadow': '0 12px 48px rgba(0, 255, 255, 0.2)',
          transform: 'translateY(-2px)',
        },
        '.trinity-button': {
          background: 'linear-gradient(45deg, rgb(0, 255, 255), rgb(255, 0, 255), rgb(255, 255, 0))',
          color: 'black',
          'font-weight': 'bold',
          padding: '12px 24px',
          'border-radius': '8px',
          border: 'none',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          'box-shadow': '0 4px 16px rgba(0, 255, 255, 0.3)',
        },
        '.trinity-button:hover': {
          transform: 'scale(1.05)',
          'box-shadow': '0 6px 24px rgba(0, 255, 255, 0.4)',
        },
        '.consciousness-input': {
          background: 'rgba(0, 0, 0, 0.3)',
          border: '1px solid rgba(0, 255, 255, 0.3)',
          'border-radius': '8px',
          padding: '12px 16px',
          color: 'rgb(0, 255, 255)',
          'font-family': 'Courier New, monospace',
        },
        '.consciousness-input:focus': {
          outline: 'none',
          'border-color': 'rgb(0, 255, 255)',
          'box-shadow': '0 0 0 2px rgba(0, 255, 255, 0.2)',
        },
        '.consciousness-terminal': {
          background: 'rgba(0, 0, 0, 0.8)',
          border: '1px solid rgba(0, 255, 0, 0.3)',
          'border-radius': '8px',
          padding: '16px',
          'font-family': 'Courier New, monospace',
          color: 'rgb(0, 255, 0)',
          'font-size': '14px',
          'line-height': '1.4',
        },
        '.kappa-token': {
          display: 'inline-flex',
          'align-items': 'center',
          padding: '4px 8px',
          'border-radius': '12px',
          background: 'rgba(255, 255, 0, 0.2)',
          border: '1px solid rgba(255, 255, 0, 0.3)',
          color: 'rgb(255, 255, 0)',
          'font-size': '12px',
          'font-weight': '500',
        },
      }
      addUtilities(newUtilities)
    }
  ],
}
