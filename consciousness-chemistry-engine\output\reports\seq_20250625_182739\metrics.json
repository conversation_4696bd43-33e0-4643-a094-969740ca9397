{"psi_score": 0.66, "fibonacci_analysis": {"residue_distances": [{"start": 15, "end": 19, "score": 0.8508265571154183, "ratio": 1.646402602633365, "golden_ratio_diff": 0.017532767593706613}, {"start": 2, "end": 4, "score": 0.8371158003928756, "ratio": 1.6495173475720506, "golden_ratio_diff": 0.019457785832101074}, {"start": 11, "end": 13, "score": 0.579249427941985, "ratio": 1.7355634490369087, "golden_ratio_diff": 0.07263720113680552}, {"start": 5, "end": 9, "score": 0.5673490209682573, "ratio": 1.4946453931117887, "golden_ratio_diff": 0.07625834592846667}, {"start": 14, "end": 17, "score": 0.5387613974184805, "ratio": 1.479512606283304, "golden_ratio_diff": 0.08561092253297696}], "torsion_angles": [{"start": 0, "end": 4, "score": 0.7327667388482594, "ratio": 1.5590257837449004, "golden_ratio_diff": 0.03646907630820825}, {"start": 1, "end": 3, "score": 0.7130768679081729, "ratio": 1.6831393652915634, "golden_ratio_diff": 0.0402373355531112}], "overall_score": 0.6884494015133499}, "fibonacci_alignment": {"closest_fibonacci": 21, "difference": 1, "ratio": 0.9523809523809523, "alignment_score": 0.9545454545454545}, "trinity_validation": {"ners": 0.6685348204540049, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6373139281816019, "passed": false}, "trinity_report": {"scores": {"ners": 0.6685348204540049, "nepi": 0.5389999999999999, "nefc": 0.694, "overall": 0.6373139281816019, "passed": false}, "thresholds": {"ners": 0.7, "nepi": 0.5, "nefc": 0.6}, "weights": {"ners": 0.4, "nepi": 0.3, "nefc": 0.3}, "validation": {"ners": {"score": 0.6685348204540049, "threshold": 0.7, "passed": false, "description": "Neural-Emotional Resonance Score: Measures structural harmony and consciousness resonance."}, "nepi": {"score": 0.5389999999999999, "threshold": 0.5, "passed": true, "description": "Neural-Emotional Potential Index: Evaluates functional potential and adaptability."}, "nefc": {"score": 0.694, "threshold": 0.6, "passed": true, "description": "Neural-Emotional Field Coherence: Assesses field coherence and quantum effects."}, "overall": {"score": 0.6373139281816019, "passed": false, "description": "Overall validation status based on all metrics."}}}}