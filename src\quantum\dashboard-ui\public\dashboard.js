/**
 * Dashboard JavaScript
 *
 * This file contains the client-side JavaScript for the Finite Universe Principle
 * monitoring dashboard. It handles real-time updates, chart rendering, and UI interactions.
 */

// Initialize Socket.IO connection
const socket = io();

// Charts
let boundaryChart;
let domainChart;
let anomaliesChart;
let forecastsChart;

// Data storage
let metricsHistory = [];
let alertsHistory = [];
let anomaliesHistory = [];
let forecastsHistory = [];
let mlSettings = {
  anomalyDetector: {
    anomalyThreshold: 3.0,
    learningRate: 0.1,
    historyLength: 100
  },
  predictiveAnalytics: {
    forecastHorizon: 10,
    confidenceLevel: 0.95,
    seasonalityPeriod: 24
  }
};

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', () => {
  // Initialize charts
  initializeCharts();

  // Set up Socket.IO event listeners
  setupSocketListeners();

  // Fetch initial data
  fetchInitialData();

  // Set up tooltips
  setupTooltips();

  // Set up ML settings form event listeners
  setupMLSettingsListeners();
});

/**
 * Initialize charts
 */
function initializeCharts() {
  // Boundary metrics chart
  const boundaryCtx = document.getElementById('boundary-chart').getContext('2d');
  boundaryChart = new Chart(boundaryCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Boundary Violations',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Boundary Corrections',
          data: [],
          borderColor: '#198754',
          backgroundColor: 'rgba(25, 135, 84, 0.1)',
          tension: 0.4,
          fill: true
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Domain metrics chart
  const domainCtx = document.getElementById('domain-chart').getContext('2d');
  domainChart = new Chart(domainCtx, {
    type: 'bar',
    data: {
      labels: ['Cyber', 'Financial', 'Medical'],
      datasets: [
        {
          label: 'Boundary Violations',
          data: [0, 0, 0],
          backgroundColor: [
            'rgba(220, 53, 69, 0.7)',
            'rgba(25, 135, 84, 0.7)',
            'rgba(13, 202, 240, 0.7)'
          ],
          borderColor: [
            'rgb(220, 53, 69)',
            'rgb(25, 135, 84)',
            'rgb(13, 202, 240)'
          ],
          borderWidth: 1
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // Anomalies chart
  const anomaliesCtx = document.getElementById('anomalies-chart').getContext('2d');
  anomaliesChart = new Chart(anomaliesCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Cyber Anomalies',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Financial Anomalies',
          data: [],
          borderColor: '#198754',
          backgroundColor: 'rgba(25, 135, 84, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Medical Anomalies',
          data: [],
          borderColor: '#0dcaf0',
          backgroundColor: 'rgba(13, 202, 240, 0.1)',
          tension: 0.4,
          fill: true
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Anomaly Count'
          }
        }
      }
    }
  });

  // Forecasts chart
  const forecastsCtx = document.getElementById('forecasts-chart').getContext('2d');
  forecastsChart = new Chart(forecastsCtx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [
        {
          label: 'Boundary Violations (Actual)',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          borderDash: [],
          tension: 0.4,
          fill: false
        },
        {
          label: 'Boundary Violations (Forecast)',
          data: [],
          borderColor: '#dc3545',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          borderDash: [5, 5],
          tension: 0.4,
          fill: false
        },
        {
          label: 'Validation Failures (Actual)',
          data: [],
          borderColor: '#0d6efd',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          borderDash: [],
          tension: 0.4,
          fill: false
        },
        {
          label: 'Validation Failures (Forecast)',
          data: [],
          borderColor: '#0d6efd',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          borderDash: [5, 5],
          tension: 0.4,
          fill: false
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          mode: 'index',
          intersect: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Count'
          }
        }
      }
    }
  });
}

/**
 * Set up Socket.IO event listeners
 */
function setupSocketListeners() {
  // Listen for metrics updates
  socket.on('metrics', (metrics) => {
    updateDashboard(metrics);
    updateCharts(metrics);
  });

  // Listen for alerts
  socket.on('alerts', (alerts) => {
    updateAlerts(alerts);
  });

  // Listen for anomalies
  socket.on('anomalies', (anomalies) => {
    updateAnomalies(anomalies);
  });

  // Listen for forecasts
  socket.on('forecasts', (forecasts) => {
    updateForecasts(forecasts);
  });

  // Listen for ML settings updates
  socket.on('ml-settings', (settings) => {
    updateMLSettings(settings);
  });

  // Connection status
  socket.on('connect', () => {
    console.log('Connected to server');
  });

  socket.on('disconnect', () => {
    console.log('Disconnected from server');
  });
}

/**
 * Fetch initial data from the API
 */
function fetchInitialData() {
  // Fetch metrics
  fetch('/api/metrics')
    .then(response => response.json())
    .then(metrics => {
      updateDashboard(metrics);
      updateCharts(metrics);
    })
    .catch(error => console.error('Error fetching metrics:', error));

  // Fetch metrics history
  fetch('/api/metrics/history')
    .then(response => response.json())
    .then(history => {
      metricsHistory = history;
      updateHistoryCharts();
    })
    .catch(error => console.error('Error fetching metrics history:', error));

  // Fetch alerts
  fetch('/api/alerts')
    .then(response => response.json())
    .then(alerts => {
      alertsHistory = alerts;
      updateAlerts(alerts);
    })
    .catch(error => console.error('Error fetching alerts:', error));
}

/**
 * Update the dashboard with new metrics
 * @param {Object} metrics - Current metrics
 */
function updateDashboard(metrics) {
  // Update summary metrics
  document.getElementById('boundary-violations').textContent = metrics.boundaryViolations || 0;
  document.getElementById('boundary-corrections').textContent = metrics.boundaryCorrections || 0;
  document.getElementById('validation-failures').textContent = metrics.validationFailures || 0;
  document.getElementById('active-alerts').textContent = alertsHistory.length || 0;

  // Update domain-specific metrics
  updateCyberDomainMetrics(metrics.domainMetrics?.cyber || {});
  updateFinancialDomainMetrics(metrics.domainMetrics?.financial || {});
  updateMedicalDomainMetrics(metrics.domainMetrics?.medical || {});
}

/**
 * Update cyber domain metrics
 * @param {Object} metrics - Cyber domain metrics
 */
function updateCyberDomainMetrics(metrics) {
  const securityScore = metrics.averageSecurityScore || 0;
  const threatLevel = metrics.averageThreatLevel || 0;
  const violations = metrics.boundaryViolations || 0;

  // Update security score
  document.getElementById('security-score-value').textContent = securityScore.toFixed(1);
  document.getElementById('security-score-progress').style.width = `${(securityScore / 10) * 100}%`;

  // Update threat level
  document.getElementById('threat-level-value').textContent = threatLevel.toFixed(1);
  document.getElementById('threat-level-progress').style.width = `${(threatLevel / 10) * 100}%`;

  // Update violations
  document.getElementById('cyber-violations').textContent = violations;
}

/**
 * Update financial domain metrics
 * @param {Object} metrics - Financial domain metrics
 */
function updateFinancialDomainMetrics(metrics) {
  const interestRate = metrics.averageInterestRate || 0;
  const transactionVolume = metrics.totalTransactionVolume || 0;
  const violations = metrics.boundaryViolations || 0;

  // Update interest rate
  document.getElementById('interest-rate-value').textContent = (interestRate * 100).toFixed(2);
  document.getElementById('interest-rate-progress').style.width = `${(interestRate) * 100}%`;

  // Update transaction volume
  document.getElementById('transaction-volume').textContent = formatCurrency(transactionVolume);

  // Update violations
  document.getElementById('financial-violations').textContent = violations;
}

/**
 * Update medical domain metrics
 * @param {Object} metrics - Medical domain metrics
 */
function updateMedicalDomainMetrics(metrics) {
  const heartRate = metrics.averageHeartRate || 0;
  const criticalAlerts = metrics.criticalAlerts || 0;
  const violations = metrics.boundaryViolations || 0;

  // Update heart rate
  document.getElementById('heart-rate-value').textContent = heartRate.toFixed(0);
  document.getElementById('heart-rate-progress').style.width = `${(heartRate / 200) * 100}%`;

  // Update critical alerts
  document.getElementById('critical-alerts').textContent = criticalAlerts;
  if (criticalAlerts > 0) {
    document.getElementById('critical-alerts').classList.add('pulse');
  } else {
    document.getElementById('critical-alerts').classList.remove('pulse');
  }

  // Update violations
  document.getElementById('medical-violations').textContent = violations;
}

/**
 * Update charts with new metrics
 * @param {Object} metrics - Current metrics
 */
function updateCharts(metrics) {
  // Add current time as label
  const now = new Date();
  const timeLabel = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;

  // Update boundary chart
  boundaryChart.data.labels.push(timeLabel);
  boundaryChart.data.datasets[0].data.push(metrics.boundaryViolations || 0);
  boundaryChart.data.datasets[1].data.push(metrics.boundaryCorrections || 0);

  // Limit the number of data points
  if (boundaryChart.data.labels.length > 20) {
    boundaryChart.data.labels.shift();
    boundaryChart.data.datasets[0].data.shift();
    boundaryChart.data.datasets[1].data.shift();
  }

  boundaryChart.update();

  // Update domain chart
  domainChart.data.datasets[0].data = [
    metrics.domainMetrics?.cyber?.boundaryViolations || 0,
    metrics.domainMetrics?.financial?.boundaryViolations || 0,
    metrics.domainMetrics?.medical?.boundaryViolations || 0
  ];

  domainChart.update();
}

/**
 * Update alerts table
 * @param {Array} alerts - Alerts
 */
function updateAlerts(alerts) {
  const tableBody = document.getElementById('alerts-table-body');

  // Clear existing alerts
  tableBody.innerHTML = '';

  // Add alerts to table
  alerts.forEach(alert => {
    const row = document.createElement('tr');

    // Add alert level class
    if (alert.level === 'critical') {
      row.classList.add('critical-alert');
    } else if (alert.level === 'warning') {
      row.classList.add('new-alert');
    }

    // Format timestamp
    const timestamp = new Date(alert.timestamp);
    const timeString = timestamp.toLocaleTimeString();

    // Create table cells
    row.innerHTML = `
      <td>${timeString}</td>
      <td>${alert.type}</td>
      <td><span class="badge bg-${getBadgeColor(alert.level)}">${alert.level}</span></td>
      <td>${alert.message}</td>
    `;

    // Add row to table
    tableBody.appendChild(row);
  });
}

/**
 * Update history charts
 */
function updateHistoryCharts() {
  // Implementation for history charts
}

/**
 * Set up tooltips
 */
function setupTooltips() {
  // Implementation for tooltips
}

/**
 * Update anomalies visualization
 * @param {Array} anomalies - Anomalies data
 */
function updateAnomalies(anomalies) {
  // Add anomalies to history
  anomaliesHistory = anomalies;

  // Update anomalies chart
  const now = new Date();
  const timeLabel = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;

  // Count anomalies by domain
  const cyberAnomalies = anomalies.filter(a => a.domain === 'cyber').length;
  const financialAnomalies = anomalies.filter(a => a.domain === 'financial').length;
  const medicalAnomalies = anomalies.filter(a => a.domain === 'medical').length;

  // Add data to chart
  anomaliesChart.data.labels.push(timeLabel);
  anomaliesChart.data.datasets[0].data.push(cyberAnomalies);
  anomaliesChart.data.datasets[1].data.push(financialAnomalies);
  anomaliesChart.data.datasets[2].data.push(medicalAnomalies);

  // Limit the number of data points
  if (anomaliesChart.data.labels.length > 20) {
    anomaliesChart.data.labels.shift();
    anomaliesChart.data.datasets[0].data.shift();
    anomaliesChart.data.datasets[1].data.shift();
    anomaliesChart.data.datasets[2].data.shift();
  }

  anomaliesChart.update();

  // Update anomalies table
  const tableBody = document.getElementById('anomalies-table-body');
  tableBody.innerHTML = '';

  // Add recent anomalies to table (most recent first)
  const recentAnomalies = [...anomalies].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 5);

  recentAnomalies.forEach(anomaly => {
    const row = document.createElement('tr');

    // Format timestamp
    const timestamp = new Date(anomaly.timestamp);
    const timeString = timestamp.toLocaleTimeString();

    // Create table cells
    row.innerHTML = `
      <td>${timeString}</td>
      <td>${anomaly.domain}</td>
      <td>${anomaly.field}</td>
      <td>${anomaly.value.toFixed(2)}</td>
      <td>${anomaly.zScore.toFixed(2)}</td>
    `;

    // Add row to table
    tableBody.appendChild(row);
  });
}

/**
 * Update forecasts visualization
 * @param {Object} forecasts - Forecasts data
 */
function updateForecasts(forecasts) {
  // Add forecasts to history
  forecastsHistory = forecasts;

  // Update forecasts chart
  // Generate labels for time steps
  const labels = [];
  for (let i = 0; i < forecasts.boundaryViolations.length; i++) {
    labels.push(`T+${i+1}`);
  }

  // Clear existing data
  forecastsChart.data.labels = labels;

  // Add actual data (last few points from history)
  const actualBoundaryViolations = metricsHistory.slice(-5).map(entry => entry.metrics.boundaryViolations || 0);
  const actualValidationFailures = metricsHistory.slice(-5).map(entry => entry.metrics.validationFailures || 0);

  // Add forecast data
  forecastsChart.data.datasets[0].data = actualBoundaryViolations;
  forecastsChart.data.datasets[1].data = forecasts.boundaryViolations;
  forecastsChart.data.datasets[2].data = actualValidationFailures;
  forecastsChart.data.datasets[3].data = forecasts.validationFailures;

  forecastsChart.update();

  // Update prediction accuracy
  document.getElementById('boundary-accuracy-progress').style.width = `${forecasts.accuracy.boundaryViolations * 100}%`;
  document.getElementById('validation-accuracy-progress').style.width = `${forecasts.accuracy.validationFailures * 100}%`;
  document.getElementById('cyber-accuracy-progress').style.width = `${forecasts.accuracy.domainViolations.cyber * 100}%`;
  document.getElementById('financial-accuracy-progress').style.width = `${forecasts.accuracy.domainViolations.financial * 100}%`;
  document.getElementById('medical-accuracy-progress').style.width = `${forecasts.accuracy.domainViolations.medical * 100}%`;
}

/**
 * Update ML settings
 * @param {Object} settings - ML settings
 */
function updateMLSettings(settings) {
  // Update local settings
  mlSettings = settings;

  // Update form values
  document.getElementById('anomalyThreshold').value = settings.anomalyDetector.anomalyThreshold;
  document.getElementById('anomalyThresholdValue').textContent = settings.anomalyDetector.anomalyThreshold;

  document.getElementById('learningRate').value = settings.anomalyDetector.learningRate;
  document.getElementById('learningRateValue').textContent = settings.anomalyDetector.learningRate;

  document.getElementById('historyLength').value = settings.anomalyDetector.historyLength;
  document.getElementById('historyLengthValue').textContent = settings.anomalyDetector.historyLength;

  document.getElementById('forecastHorizon').value = settings.predictiveAnalytics.forecastHorizon;
  document.getElementById('forecastHorizonValue').textContent = settings.predictiveAnalytics.forecastHorizon;

  document.getElementById('confidenceLevel').value = settings.predictiveAnalytics.confidenceLevel;
  document.getElementById('confidenceLevelValue').textContent = settings.predictiveAnalytics.confidenceLevel;

  document.getElementById('seasonalityPeriod').value = settings.predictiveAnalytics.seasonalityPeriod;
  document.getElementById('seasonalityPeriodValue').textContent = settings.predictiveAnalytics.seasonalityPeriod;
}

/**
 * Set up ML settings form event listeners
 */
function setupMLSettingsListeners() {
  // Anomaly detection settings form
  const anomalyForm = document.getElementById('anomaly-settings-form');
  if (anomalyForm) {
    // Update display values when sliders change
    document.getElementById('anomalyThreshold').addEventListener('input', (e) => {
      document.getElementById('anomalyThresholdValue').textContent = e.target.value;
    });

    document.getElementById('learningRate').addEventListener('input', (e) => {
      document.getElementById('learningRateValue').textContent = e.target.value;
    });

    document.getElementById('historyLength').addEventListener('input', (e) => {
      document.getElementById('historyLengthValue').textContent = e.target.value;
    });

    // Handle form submission
    anomalyForm.addEventListener('submit', (e) => {
      e.preventDefault();

      // Get form values
      const anomalyThreshold = parseFloat(document.getElementById('anomalyThreshold').value);
      const learningRate = parseFloat(document.getElementById('learningRate').value);
      const historyLength = parseInt(document.getElementById('historyLength').value);

      // Update local settings
      mlSettings.anomalyDetector = {
        ...mlSettings.anomalyDetector,
        anomalyThreshold,
        learningRate,
        historyLength
      };

      // Send settings to server
      socket.emit('update-ml-settings', mlSettings);

      // Show success message
      alert('Anomaly detection settings updated successfully');
    });
  }

  // Predictive analytics settings form
  const predictiveForm = document.getElementById('predictive-settings-form');
  if (predictiveForm) {
    // Update display values when sliders change
    document.getElementById('forecastHorizon').addEventListener('input', (e) => {
      document.getElementById('forecastHorizonValue').textContent = e.target.value;
    });

    document.getElementById('confidenceLevel').addEventListener('input', (e) => {
      document.getElementById('confidenceLevelValue').textContent = e.target.value;
    });

    document.getElementById('seasonalityPeriod').addEventListener('input', (e) => {
      document.getElementById('seasonalityPeriodValue').textContent = e.target.value;
    });

    // Handle form submission
    predictiveForm.addEventListener('submit', (e) => {
      e.preventDefault();

      // Get form values
      const forecastHorizon = parseInt(document.getElementById('forecastHorizon').value);
      const confidenceLevel = parseFloat(document.getElementById('confidenceLevel').value);
      const seasonalityPeriod = parseInt(document.getElementById('seasonalityPeriod').value);

      // Update local settings
      mlSettings.predictiveAnalytics = {
        ...mlSettings.predictiveAnalytics,
        forecastHorizon,
        confidenceLevel,
        seasonalityPeriod
      };

      // Send settings to server
      socket.emit('update-ml-settings', mlSettings);

      // Show success message
      alert('Predictive analytics settings updated successfully');
    });
  }
}

/**
 * Format a number as currency
 * @param {number} value - Value to format
 * @returns {string} - Formatted currency string
 */
function formatCurrency(value) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value);
}

/**
 * Get badge color based on alert level
 * @param {string} level - Alert level
 * @returns {string} - Bootstrap color class
 */
function getBadgeColor(level) {
  switch (level) {
    case 'critical':
      return 'danger';
    case 'warning':
      return 'warning';
    case 'info':
      return 'info';
    default:
      return 'secondary';
  }
}
