FROM node:18-alpine

WORKDIR /app

# Install necessary build tools
RUN apk add --no-cache python3 make g++

# Create necessary directories
RUN mkdir -p src/monitoring src/utils

# Copy monitoring components
COPY src/monitoring ./src/monitoring/
COPY src/utils/logger.js ./src/utils/

# Copy test script
COPY test-monitoring.js ./

# Install dependencies
RUN npm init -y && \
    npm install uuid winston

# Run the monitoring test script
CMD ["node", "test-monitoring.js"]
