/**
 * GraphQL Connector Configuration Component
 * 
 * This component allows users to configure GraphQL connector settings.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Divider, 
  FormControl, 
  FormControlLabel, 
  Grid, 
  InputLabel, 
  MenuItem, 
  Paper, 
  Select, 
  Switch, 
  TextField, 
  Typography 
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import TestIcon from '@mui/icons-material/PlayArrow';
import { graphqlApi } from '../../services/api';

const GraphQLConnectorConfig = ({ connector, onSave, onTest }) => {
  const [config, setConfig] = useState({
    name: connector?.name || '',
    description: connector?.description || '',
    baseUrl: connector?.baseUrl || '',
    authentication: connector?.authentication || {
      type: 'none',
      fields: {}
    },
    headers: connector?.headers || {},
    introspectionEnabled: connector?.introspectionEnabled !== false,
    subscriptionsEnabled: connector?.subscriptionsEnabled || false,
    batchingEnabled: connector?.batchingEnabled || false,
    cacheConfig: connector?.cacheConfig || {
      enabled: false,
      ttl: 300 // 5 minutes
    }
  });
  
  const [newHeaderKey, setNewHeaderKey] = useState('');
  const [newHeaderValue, setNewHeaderValue] = useState('');
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);
  
  const handleConfigChange = (field, value) => {
    setConfig({
      ...config,
      [field]: value
    });
  };
  
  const handleAuthTypeChange = (type) => {
    setConfig({
      ...config,
      authentication: {
        type,
        fields: {}
      }
    });
  };
  
  const handleAuthFieldChange = (field, value) => {
    setConfig({
      ...config,
      authentication: {
        ...config.authentication,
        fields: {
          ...config.authentication.fields,
          [field]: value
        }
      }
    });
  };
  
  const handleAddHeader = () => {
    if (!newHeaderKey.trim()) return;
    
    setConfig({
      ...config,
      headers: {
        ...config.headers,
        [newHeaderKey]: newHeaderValue
      }
    });
    
    setNewHeaderKey('');
    setNewHeaderValue('');
  };
  
  const handleRemoveHeader = (key) => {
    const newHeaders = { ...config.headers };
    delete newHeaders[key];
    
    setConfig({
      ...config,
      headers: newHeaders
    });
  };
  
  const handleSave = () => {
    if (onSave) {
      onSave(config);
    }
  };
  
  const handleTest = async () => {
    setTesting(true);
    setTestResult(null);
    
    try {
      // Prepare auth object for the test
      let auth = null;
      if (config.authentication.type !== 'none') {
        auth = {
          type: config.authentication.type,
          ...config.authentication.fields
        };
      }
      
      // Simple introspection query to test the connection
      const query = `
        query {
          __schema {
            queryType {
              name
            }
          }
        }
      `;
      
      const response = await graphqlApi.executeQuery(
        config.baseUrl,
        query,
        {},
        config.headers,
        auth
      );
      
      setTestResult({
        success: response.data?.data?.__schema != null,
        status: response.status,
        message: response.data?.data?.__schema 
          ? 'Successfully connected to GraphQL endpoint' 
          : 'Connected to server but schema introspection failed',
        data: response.data
      });
    } catch (error) {
      setTestResult({
        success: false,
        status: error.response?.status || 0,
        message: error.message || 'Connection failed',
        error: error
      });
    } finally {
      setTesting(false);
    }
    
    if (onTest) {
      onTest(testResult);
    }
  };
  
  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        GraphQL Connector Configuration
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Basic Information
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Connector Name"
                    value={config.name}
                    onChange={(e) => handleConfigChange('name', e.target.value)}
                    required
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Description"
                    value={config.description}
                    onChange={(e) => handleConfigChange('description', e.target.value)}
                    multiline
                    rows={2}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="GraphQL Endpoint URL"
                    value={config.baseUrl}
                    onChange={(e) => handleConfigChange('baseUrl', e.target.value)}
                    required
                    placeholder="https://api.example.com/graphql"
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Authentication
              </Typography>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel id="auth-type-label">Authentication Type</InputLabel>
                <Select
                  labelId="auth-type-label"
                  value={config.authentication.type}
                  label="Authentication Type"
                  onChange={(e) => handleAuthTypeChange(e.target.value)}
                >
                  <MenuItem value="none">None</MenuItem>
                  <MenuItem value="bearer">Bearer Token</MenuItem>
                  <MenuItem value="api_key">API Key</MenuItem>
                  <MenuItem value="basic">Basic Auth</MenuItem>
                </Select>
              </FormControl>
              
              {config.authentication.type === 'bearer' && (
                <TextField
                  fullWidth
                  label="Bearer Token"
                  value={config.authentication.fields.token || ''}
                  onChange={(e) => handleAuthFieldChange('token', e.target.value)}
                  placeholder="Enter your bearer token"
                />
              )}
              
              {config.authentication.type === 'api_key' && (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="API Key"
                      value={config.authentication.fields.apiKey || ''}
                      onChange={(e) => handleAuthFieldChange('apiKey', e.target.value)}
                      placeholder="Enter your API key"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Header Name"
                      value={config.authentication.fields.headerName || 'X-API-Key'}
                      onChange={(e) => handleAuthFieldChange('headerName', e.target.value)}
                      placeholder="X-API-Key"
                    />
                  </Grid>
                </Grid>
              )}
              
              {config.authentication.type === 'basic' && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Username"
                      value={config.authentication.fields.username || ''}
                      onChange={(e) => handleAuthFieldChange('username', e.target.value)}
                      placeholder="Enter username"
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Password"
                      type="password"
                      value={config.authentication.fields.password || ''}
                      onChange={(e) => handleAuthFieldChange('password', e.target.value)}
                      placeholder="Enter password"
                    />
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined" sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Headers
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={12} sm={5}>
                  <TextField
                    fullWidth
                    label="Header Name"
                    value={newHeaderKey}
                    onChange={(e) => setNewHeaderKey(e.target.value)}
                    placeholder="Content-Type"
                  />
                </Grid>
                
                <Grid item xs={12} sm={5}>
                  <TextField
                    fullWidth
                    label="Header Value"
                    value={newHeaderValue}
                    onChange={(e) => setNewHeaderValue(e.target.value)}
                    placeholder="application/json"
                  />
                </Grid>
                
                <Grid item xs={12} sm={2}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleAddHeader}
                    disabled={!newHeaderKey.trim()}
                    sx={{ height: '100%' }}
                  >
                    Add
                  </Button>
                </Grid>
              </Grid>
              
              <Divider sx={{ my: 2 }} />
              
              {Object.keys(config.headers).length > 0 ? (
                <Grid container spacing={2}>
                  {Object.entries(config.headers).map(([key, value]) => (
                    <Grid item xs={12} key={key}>
                      <Paper variant="outlined" sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box>
                          <Typography variant="subtitle2">
                            {key}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {value}
                          </Typography>
                        </Box>
                        
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          onClick={() => handleRemoveHeader(key)}
                        >
                          Remove
                        </Button>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body2" color="textSecondary" align="center">
                  No headers defined
                </Typography>
              )}
            </CardContent>
          </Card>
          
          <Card variant="outlined">
            <CardContent>
              <Typography variant="subtitle1" gutterBottom>
                Advanced Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.introspectionEnabled}
                        onChange={(e) => handleConfigChange('introspectionEnabled', e.target.checked)}
                      />
                    }
                    label="Enable Schema Introspection"
                  />
                  <Typography variant="caption" color="textSecondary" display="block">
                    Allows fetching schema information from the GraphQL server
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.subscriptionsEnabled}
                        onChange={(e) => handleConfigChange('subscriptionsEnabled', e.target.checked)}
                      />
                    }
                    label="Enable Subscriptions"
                  />
                  <Typography variant="caption" color="textSecondary" display="block">
                    Enables real-time data with GraphQL subscriptions
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.batchingEnabled}
                        onChange={(e) => handleConfigChange('batchingEnabled', e.target.checked)}
                      />
                    }
                    label="Enable Query Batching"
                  />
                  <Typography variant="caption" color="textSecondary" display="block">
                    Allows sending multiple queries in a single request
                  </Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={config.cacheConfig.enabled}
                        onChange={(e) => handleConfigChange('cacheConfig', {
                          ...config.cacheConfig,
                          enabled: e.target.checked
                        })}
                      />
                    }
                    label="Enable Response Caching"
                  />
                  
                  {config.cacheConfig.enabled && (
                    <Box sx={{ mt: 1, ml: 4 }}>
                      <TextField
                        label="Cache TTL (seconds)"
                        type="number"
                        value={config.cacheConfig.ttl}
                        onChange={(e) => handleConfigChange('cacheConfig', {
                          ...config.cacheConfig,
                          ttl: parseInt(e.target.value) || 300
                        })}
                        InputProps={{ inputProps: { min: 0 } }}
                        size="small"
                      />
                    </Box>
                  )}
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12}>
          {testResult && (
            <Card 
              variant="outlined" 
              sx={{ 
                mb: 3, 
                bgcolor: testResult.success ? 'success.light' : 'error.light' 
              }}
            >
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {testResult.success ? 'Connection Successful' : 'Connection Failed'}
                </Typography>
                
                <Typography variant="body2">
                  {testResult.message}
                </Typography>
                
                {testResult.status && (
                  <Typography variant="body2" color="textSecondary">
                    Status: {testResult.status}
                  </Typography>
                )}
              </CardContent>
            </Card>
          )}
          
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<TestIcon />}
              onClick={handleTest}
              disabled={!config.baseUrl || testing}
            >
              {testing ? 'Testing...' : 'Test Connection'}
            </Button>
            
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              disabled={!config.name || !config.baseUrl}
            >
              Save Configuration
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default GraphQLConnectorConfig;
