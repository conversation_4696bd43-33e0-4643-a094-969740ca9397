/**
 * Simple test for connector registry
 */

const connectorRegistry = require('./src/connectors/services/connector-registry');

async function test() {
  try {
    console.log('Creating connector...');
    const connector = await connectorRegistry.createConnector({
      name: 'Test Connector',
      description: 'A test connector',
      version: '1.0.0'
    });
    
    console.log('Connector created:', connector);
    
    console.log('Getting all connectors...');
    const connectors = await connectorRegistry.getAllConnectors();
    console.log('All connectors:', connectors);
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

test();
