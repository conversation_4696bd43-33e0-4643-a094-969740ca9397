# NovaCortex Docker Testing Guide

## Overview

This document provides guidance for running NovaCortex tests in Docker containers. The testing setup includes:

- **NovaCortex Server**: A Node.js test server that simulates NovaCortex functionality
- **Python Test Runner**: Integration tests using pytest and aiohttp
- **Supporting Services**: MongoDB, Redis, Prometheus, and Grafana for monitoring
- **Test Coverage**: Coherence testing, CASTL decision making, π-Rhythm synchronization

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Test Runner   │───→│   NovaCortex    │───→│    MongoDB      │
│   (Python)      │    │   (Node.js)     │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                      ┌─────────────────┐    ┌─────────────────┐
                      │     Redis       │    │   Prometheus    │
                      │                 │    │   + <PERSON><PERSON>     │
                      └─────────────────┘    └─────────────────┘
```

## Quick Start

### 1. Run Tests with Docker Compose

```bash
# Build and run all tests
docker-compose -f docker-compose.novacortex-test.yml up --build test-runner

# Or run individual services
docker-compose -f docker-compose.novacortex-test.yml up -d --build
docker-compose -f docker-compose.novacortex-test.yml run --rm test-runner
```

### 2. Run Tests with PowerShell Script

```powershell
# Run the comprehensive test script
./scripts/run-novacortex-tests.ps1
```

### 3. Run Tests with npm

```bash
# Run NovaCortex tests directly
npm run test:novacortex:docker
```

## Test Coverage

### Integration Tests (`tests/novacortex/test_integration.py`)

1. **Coherence Initialization** (`test_coherence_initialization`)
   - Tests that NovaCortex maintains coherence (∂Ψ = 0)
   - Validates coherence level is between 0.95 and 1.0
   - Endpoint: `GET /api/novacortex/coherence`

2. **CASTL Decision Making** (`test_castl_decision_making`)
   - Tests ethical decision-making framework
   - Validates proper reasoning and principle application
   - Endpoint: `POST /api/novacortex/castl/decide`

3. **π-Rhythm Synchronization** (`test_pi_rhythm_synchronization`)
   - Tests π-Rhythm measurement and synchronization
   - Validates deviation is within acceptable limits (< 0.1)
   - Endpoint: `GET /api/novacortex/pi-rhythm/measure`

4. **Metrics Endpoint** (`test_metrics_endpoint`)
   - Tests system metrics collection
   - Validates all required metrics are present
   - Endpoint: `GET /api/novacortex/metrics`

5. **End-to-End Workflow** (`test_end_to_end_workflow`)
   - Tests complete NovaCortex workflow
   - Combines all functionality in realistic scenario
   - Validates system health remains optimal

## API Endpoints

### Health Check
```http
GET /health
```
Returns system health status and component information.

### Coherence Testing
```http
GET /api/novacortex/coherence
```
Returns current coherence level and status.

### CASTL Decision Making
```http
POST /api/novacortex/castl/decide
Content-Type: application/json

{
  "scenario": {
    "type": "trolley",
    "options": ["do_nothing", "pull_lever"],
    "consequences": {
      "do_nothing": ["5_fatalities"],
      "pull_lever": ["1_fatality"]
    }
  }
}
```

### π-Rhythm Measurement
```http
GET /api/novacortex/pi-rhythm/measure?duration=5.0
```

### System Metrics
```http
GET /api/novacortex/metrics
```

### Prometheus Metrics
```http
GET /metrics
```

## Service Configuration

### NovaCortex Service
- **Port**: 3010
- **Environment**: 
  - `NODE_ENV=test`
  - `MONGODB_URI=mongodb://mongodb:27017/novacortex-test`
  - `REDIS_URI=redis://redis:6379/0`

### Test Runner Service
- **Base Image**: `python:3.10-slim`
- **Test Framework**: pytest with asyncio support
- **Dependencies**: aiohttp, pytest-asyncio, allure-pytest

### Supporting Services
- **MongoDB**: Port 27017 (with health checks)
- **Redis**: Port 6379 (with health checks)
- **Prometheus**: Port 9090 (optional monitoring)
- **Grafana**: Port 3000 (optional visualization)

## Test Results

### Successful Test Run Output
```
=================================================== test session starts ====================================================
platform linux -- Python 3.10.18, pytest-8.4.1, pluggy-1.6.0
collected 5 items

tests/novacortex/test_integration.py::test_coherence_initialization PASSED     [ 20%]
tests/novacortex/test_integration.py::test_castl_decision_making PASSED        [ 40%]
tests/novacortex/test_integration.py::test_pi_rhythm_synchronization PASSED    [ 60%]
tests/novacortex/test_integration.py::test_metrics_endpoint PASSED             [ 80%]
tests/novacortex/test_integration.py::test_end_to_end_workflow PASSED          [100%]

==================================================== 5 passed in 0.82s =====================================================
```

## Monitoring and Debugging

### View Container Status
```bash
docker-compose -f docker-compose.novacortex-test.yml ps
```

### View Logs
```bash
# All services
docker-compose -f docker-compose.novacortex-test.yml logs

# Specific service
docker-compose -f docker-compose.novacortex-test.yml logs novacortex
docker-compose -f docker-compose.novacortex-test.yml logs test-runner
```

### Health Check
```bash
# Internal (from container)
docker-compose -f docker-compose.novacortex-test.yml exec novacortex curl http://localhost:3010/health

# External (if port is accessible)
curl http://localhost:3010/health
```

### Access Monitoring Tools
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)
- **NovaCortex Health**: http://localhost:3010/health
- **NovaCortex Metrics**: http://localhost:3010/metrics

## Cleanup

### Stop Services
```bash
docker-compose -f docker-compose.novacortex-test.yml down
```

### Remove Volumes (Complete Reset)
```bash
docker-compose -f docker-compose.novacortex-test.yml down -v
```

## Troubleshooting

### Common Issues

1. **Connection Refused Errors**
   - Ensure all services are healthy: `docker-compose -f docker-compose.novacortex-test.yml ps`
   - Check service logs for startup errors
   - Verify network connectivity between containers

2. **Import Errors in Tests**
   - Ensure `PYTHONPATH` is set correctly in test-runner container
   - Verify all test dependencies are installed

3. **Health Check Failures**
   - Check if NovaCortex server is responding: `docker-compose logs novacortex`
   - Verify curl is available in the container for health checks
   - Check if port 3010 is properly exposed and accessible

4. **Build Issues**
   - Clean Docker cache: `docker system prune`
   - Rebuild without cache: `docker-compose build --no-cache`
   - Check .dockerignore settings to ensure required files are included

### Performance Tips

1. **Optimize Docker Context**
   - Use .dockerignore to exclude large directories
   - Only copy necessary files to reduce build time

2. **Parallel Testing**
   - Use `pytest-xdist` for parallel test execution
   - Configure appropriate worker count based on system resources

3. **Caching**
   - Leverage Docker layer caching
   - Use multi-stage builds for optimization

## Integration with CI/CD

### GitHub Actions Example
```yaml
name: NovaCortex Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run NovaCortex Tests
        run: |
          docker-compose -f docker-compose.novacortex-test.yml up --build --abort-on-container-exit test-runner
```

### Jenkins Pipeline Example
```groovy
pipeline {
    agent any
    stages {
        stage('NovaCortex Tests') {
            steps {
                sh 'docker-compose -f docker-compose.novacortex-test.yml up --build --abort-on-container-exit test-runner'
            }
        }
    }
    post {
        always {
            sh 'docker-compose -f docker-compose.novacortex-test.yml down -v'
        }
    }
}
```

## Contributing

When adding new tests:

1. Add test functions to `tests/novacortex/test_integration.py`
2. Update API endpoints in `src/novacortex/server.js` if needed
3. Update this documentation with new test descriptions
4. Ensure all tests use Docker network hostnames (not localhost)
5. Add appropriate assertions and error handling

## Security Considerations

- Use environment variables for sensitive configuration
- Don't expose production credentials in test containers
- Regularly update base images and dependencies
- Use specific version tags instead of 'latest' in production
