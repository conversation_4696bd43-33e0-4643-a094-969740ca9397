/**
 * Security Context
 * 
 * This module provides security context and provider for the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { usePerformance } from '../performance/usePerformance';
import { useAuth } from '../auth/AuthContext';

// Create security context
const SecurityContext = createContext();

/**
 * Security provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} [props.initialSettings] - Initial security settings
 * @param {Function} [props.onSettingsChanged] - Callback when settings change
 * @param {Object} [props.securityService] - Security service
 * @returns {React.ReactElement} Security provider component
 */
export const SecurityProvider = ({
  children,
  initialSettings,
  onSettingsChanged,
  securityService
}) => {
  const { measureOperation } = usePerformance('SecurityProvider');
  const { user, isAuthenticated } = useAuth();
  
  // State
  const [settings, setSettings] = useState({
    twoFactorAuthEnabled: false,
    passwordPolicyEnabled: true,
    sessionTimeout: 30, // minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15, // minutes
    requirePasswordChange: 90, // days
    ...initialSettings
  });
  
  const [twoFactorAuthStatus, setTwoFactorAuthStatus] = useState({
    isEnrolled: false,
    isVerified: false,
    verificationMethod: null,
    availableMethods: ['app', 'sms', 'email'],
    preferredMethod: null,
    isLoading: false,
    error: null
  });
  
  const [securityLog, setSecurityLog] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Load security settings
  useEffect(() => {
    if (!isAuthenticated || !securityService) {
      return;
    }
    
    const loadSecuritySettings = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Load settings
        const userSettings = await measureOperation('loadSecuritySettings', () => 
          securityService.getUserSecuritySettings(user.id)
        );
        
        if (userSettings) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ...userSettings
          }));
        }
        
        // Load 2FA status
        const twoFactorStatus = await measureOperation('loadTwoFactorStatus', () => 
          securityService.getTwoFactorStatus(user.id)
        );
        
        if (twoFactorStatus) {
          setTwoFactorAuthStatus(prevStatus => ({
            ...prevStatus,
            ...twoFactorStatus
          }));
        }
        
        // Load security log
        const log = await measureOperation('loadSecurityLog', () => 
          securityService.getSecurityLog(user.id)
        );
        
        if (log) {
          setSecurityLog(log);
        }
      } catch (err) {
        console.error('Error loading security settings:', err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadSecuritySettings();
  }, [isAuthenticated, user, securityService, measureOperation]);
  
  // Update security settings
  const updateSecuritySettings = useCallback(async (newSettings) => {
    if (!isAuthenticated || !securityService) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Update settings
      await measureOperation('updateSecuritySettings', () => 
        securityService.updateUserSecuritySettings(user.id, newSettings)
      );
      
      // Update state
      setSettings(prevSettings => ({
        ...prevSettings,
        ...newSettings
      }));
      
      // Notify settings changed
      if (onSettingsChanged) {
        onSettingsChanged({
          ...settings,
          ...newSettings
        });
      }
    } catch (err) {
      console.error('Error updating security settings:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, securityService, settings, onSettingsChanged, measureOperation]);
  
  // Enable two-factor authentication
  const enableTwoFactorAuth = useCallback(async (method) => {
    if (!isAuthenticated || !securityService) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Enable 2FA
      const enrollmentData = await measureOperation('enableTwoFactorAuth', () => 
        securityService.enableTwoFactorAuth(user.id, method)
      );
      
      // Update state
      setTwoFactorAuthStatus(prevStatus => ({
        ...prevStatus,
        isEnrolled: true,
        verificationMethod: method,
        preferredMethod: method,
        ...enrollmentData
      }));
      
      // Update settings
      setSettings(prevSettings => ({
        ...prevSettings,
        twoFactorAuthEnabled: true
      }));
      
      // Return enrollment data
      return enrollmentData;
    } catch (err) {
      console.error('Error enabling two-factor authentication:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, securityService, measureOperation]);
  
  // Disable two-factor authentication
  const disableTwoFactorAuth = useCallback(async () => {
    if (!isAuthenticated || !securityService) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Disable 2FA
      await measureOperation('disableTwoFactorAuth', () => 
        securityService.disableTwoFactorAuth(user.id)
      );
      
      // Update state
      setTwoFactorAuthStatus(prevStatus => ({
        ...prevStatus,
        isEnrolled: false,
        isVerified: false,
        verificationMethod: null
      }));
      
      // Update settings
      setSettings(prevSettings => ({
        ...prevSettings,
        twoFactorAuthEnabled: false
      }));
    } catch (err) {
      console.error('Error disabling two-factor authentication:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, securityService, measureOperation]);
  
  // Verify two-factor authentication
  const verifyTwoFactorAuth = useCallback(async (code, method) => {
    if (!isAuthenticated || !securityService) {
      return false;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Verify 2FA
      const isValid = await measureOperation('verifyTwoFactorAuth', () => 
        securityService.verifyTwoFactorAuth(user.id, code, method)
      );
      
      // Update state
      if (isValid) {
        setTwoFactorAuthStatus(prevStatus => ({
          ...prevStatus,
          isVerified: true
        }));
      }
      
      return isValid;
    } catch (err) {
      console.error('Error verifying two-factor authentication:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, securityService, measureOperation]);
  
  // Send two-factor authentication code
  const sendTwoFactorAuthCode = useCallback(async (method) => {
    if (!isAuthenticated || !securityService) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Send 2FA code
      await measureOperation('sendTwoFactorAuthCode', () => 
        securityService.sendTwoFactorAuthCode(user.id, method)
      );
      
      // Update state
      setTwoFactorAuthStatus(prevStatus => ({
        ...prevStatus,
        verificationMethod: method
      }));
    } catch (err) {
      console.error('Error sending two-factor authentication code:', err);
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, securityService, measureOperation]);
  
  // Change password
  const changePassword = useCallback(async (currentPassword, newPassword) => {
    if (!isAuthenticated || !securityService) {
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Change password
      await measureOperation('changePassword', () => 
        securityService.changePassword(user.id, currentPassword, newPassword)
      );
      
      // Add to security log
      const logEntry = {
        type: 'password_change',
        timestamp: new Date().toISOString(),
        userId: user.id,
        details: {
          success: true
        }
      };
      
      setSecurityLog(prevLog => [logEntry, ...prevLog]);
    } catch (err) {
      console.error('Error changing password:', err);
      setError(err);
      
      // Add to security log
      const logEntry = {
        type: 'password_change',
        timestamp: new Date().toISOString(),
        userId: user.id,
        details: {
          success: false,
          error: err.message
        }
      };
      
      setSecurityLog(prevLog => [logEntry, ...prevLog]);
      
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, securityService, measureOperation]);
  
  // Check password strength
  const checkPasswordStrength = useCallback((password) => {
    if (!securityService) {
      return {
        score: 0,
        feedback: {
          warning: 'Password strength check not available',
          suggestions: ['Use a strong password with a mix of characters, numbers, and symbols']
        }
      };
    }
    
    return securityService.checkPasswordStrength(password);
  }, [securityService]);
  
  // Add security log entry
  const addSecurityLogEntry = useCallback((type, details) => {
    if (!isAuthenticated) {
      return;
    }
    
    const logEntry = {
      type,
      timestamp: new Date().toISOString(),
      userId: user?.id,
      details
    };
    
    setSecurityLog(prevLog => [logEntry, ...prevLog]);
    
    if (securityService) {
      securityService.addSecurityLogEntry(user.id, logEntry)
        .catch(err => console.error('Error adding security log entry:', err));
    }
  }, [isAuthenticated, user, securityService]);
  
  // Create context value
  const contextValue = {
    settings,
    twoFactorAuthStatus,
    securityLog,
    isLoading,
    error,
    updateSecuritySettings,
    enableTwoFactorAuth,
    disableTwoFactorAuth,
    verifyTwoFactorAuth,
    sendTwoFactorAuthCode,
    changePassword,
    checkPasswordStrength,
    addSecurityLogEntry
  };
  
  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};

SecurityProvider.propTypes = {
  children: PropTypes.node.isRequired,
  initialSettings: PropTypes.object,
  onSettingsChanged: PropTypes.func,
  securityService: PropTypes.object
};

/**
 * Use security hook
 * 
 * @returns {Object} Security context
 */
export const useSecurity = () => {
  const context = useContext(SecurityContext);
  
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  
  return context;
};

export default SecurityContext;
