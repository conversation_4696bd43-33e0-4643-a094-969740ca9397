/**
 * NovaCore Tensor Runtime Example
 * 
 * This example demonstrates how to use the NovaCore tensor-based runtime
 * to create, transform, and process tensors.
 */

const { 
  TensorRuntime, 
  Tensor, 
  tensorUtils 
} = require('../src/novacore');

// Create a new TensorRuntime instance
const runtime = new TensorRuntime({
  enableLogging: true,
  cacheResults: true,
  maxConcurrentOperations: 5
});

// Listen for tensor events
runtime.on('tensorCreated', ({ tensorId }) => {
  console.log(`Event: Tensor created with ID ${tensorId}`);
});

runtime.on('tensorTransformed', ({ originalTensorId, newTensorId, transformationType }) => {
  console.log(`Event: Tensor ${originalTensorId} transformed to ${newTensorId} using ${transformationType}`);
});

runtime.on('tensorProcessed', ({ originalTensorId, newTensorId }) => {
  console.log(`Event: Tensor ${originalTensorId} processed to ${newTensorId}`);
});

// Example data for a compliance evidence tensor
const evidenceData = {
  controlId: 'C-123',
  framework: 'NIST-CSF',
  status: 'COLLECTED',
  timestamp: new Date().toISOString(),
  source: 'GCP',
  value: true,
  details: 'Encryption enabled for all storage buckets'
};

// Define tensor dimensions
const dimensions = [
  { name: 'time', size: 1 },
  { name: 'compliance', size: 5 },
  { name: 'risk', size: 3 }
];

async function runExample() {
  try {
    console.log('Creating a tensor...');
    const tensor = await runtime.create(evidenceData, dimensions, {
      metadata: {
        source: 'example',
        type: 'compliance-evidence'
      }
    });
    
    console.log('Tensor created:', tensor.id);
    console.log('Tensor shape:', tensor.shape);
    console.log('Tensor dimensions:', tensor.dimensions);
    
    // Transform the tensor using projection
    console.log('\nTransforming tensor using projection...');
    const projectedTensor = await runtime.transform(tensor, {
      type: 'projection',
      parameters: {
        dimension: 'compliance',
        value: 2
      }
    });
    
    console.log('Projected tensor shape:', projectedTensor.shape);
    console.log('Projection transformation:', projectedTensor.transformations[0]);
    
    // Transform the tensor using filtering
    console.log('\nTransforming tensor using filtering...');
    const filteredTensor = await runtime.transform(tensor, {
      type: 'filtering',
      parameters: {
        condition: {
          field: 'status',
          operator: 'equals',
          value: 'COLLECTED'
        }
      }
    });
    
    console.log('Filtered tensor ID:', filteredTensor.id);
    console.log('Filtering transformation:', filteredTensor.transformations[0]);
    
    // Process the tensor
    console.log('\nProcessing tensor...');
    const processedTensor = await runtime.process(tensor, {
      processingType: 'compliance-analysis',
      parameters: {
        framework: 'NIST-CSF',
        controlFamily: 'ID'
      }
    });
    
    console.log('Processed tensor ID:', processedTensor.id);
    console.log('Processing metadata:', processedTensor.processingMetadata);
    
    // Convert tensor to array
    console.log('\nConverting tensor to array...');
    const tensorArray = tensorUtils.tensorFromArray([
      { name: 'Item 1', value: 42 },
      { name: 'Item 2', value: 84 },
      { name: 'Item 3', value: 126 }
    ]);
    
    console.log('Tensor from array:', tensorArray.id);
    console.log('Tensor from array dimensions:', tensorArray.dimensions);
    
    // Merge tensors
    console.log('\nMerging tensors...');
    const mergedTensor = tensorUtils.mergeTensors([tensor, processedTensor], {
      method: 'stack',
      dimension: 'versions'
    });
    
    console.log('Merged tensor ID:', mergedTensor.id);
    console.log('Merged tensor dimensions:', mergedTensor.dimensions);
    
    // Calculate tensor similarity
    console.log('\nCalculating tensor similarity...');
    const similarity = tensorUtils.calculateTensorSimilarity(tensor, processedTensor, {
      method: 'dimensions'
    });
    
    console.log('Tensor similarity score:', similarity);
    
    // Convert tensor to JSON and back
    console.log('\nConverting tensor to JSON and back...');
    const jsonString = tensorUtils.tensorToJSONString(tensor);
    console.log('JSON string length:', jsonString.length);
    
    const reconstructedTensor = tensorUtils.tensorFromJSON(JSON.parse(jsonString));
    console.log('Reconstructed tensor ID:', reconstructedTensor.id);
    
    const reconstructedSimilarity = tensorUtils.calculateTensorSimilarity(tensor, reconstructedTensor, {
      method: 'hash'
    });
    
    console.log('Reconstructed tensor similarity:', reconstructedSimilarity);
    
    console.log('\nExample completed successfully!');
  } catch (error) {
    console.error('Error in example:', error);
  }
}

// Run the example
runExample();
