{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "jest", "require", "AuthAuditService", "AuditService", "path", "describe", "authAuditService", "testDataDir", "join", "__dirname", "beforeEach", "clearAllMocks", "mockImplementation", "logEvent", "fn", "mockResolvedValue", "id", "getAuditLogs", "logs", "total", "page", "limit", "it", "expect", "toHaveBeenCalledWith", "resourceType", "toBe", "loginData", "userId", "username", "ip", "userAgent", "success", "method", "logLoginAttempt", "auditService", "action", "resourceId", "details", "reason", "status", "teamId", "environmentId", "tenantId", "provider", "email", "logoutData", "logLogout", "registrationData", "logRegistration", "twoFactorData", "logTwoFactorAuth", "filters", "startDate", "endDate", "getAuthAuditLogs"], "sources": ["AuthAuditService.test.js"], "sourcesContent": ["/**\n * Authentication Audit Service Tests\n */\n\nconst AuthAuditService = require('../../../api/services/AuthAuditService');\nconst AuditService = require('../../../api/services/AuditService');\nconst path = require('path');\n\n// Mock AuditService\njest.mock('../../../api/services/AuditService');\n\ndescribe('AuthAuditService', () => {\n  let authAuditService;\n  const testDataDir = path.join(__dirname, 'test-data');\n  \n  beforeEach(() => {\n    // Reset mocks\n    jest.clearAllMocks();\n    \n    // Mock AuditService implementation\n    AuditService.mockImplementation(() => ({\n      logEvent: jest.fn().mockResolvedValue({ id: 'test-log-id' }),\n      getAuditLogs: jest.fn().mockResolvedValue({\n        logs: [],\n        total: 0,\n        page: 1,\n        limit: 10\n      })\n    }));\n    \n    // Create a new instance for each test\n    authAuditService = new AuthAuditService(testDataDir);\n  });\n  \n  describe('constructor', () => {\n    it('should initialize with the correct data directory', () => {\n      expect(AuditService).toHaveBeenCalledWith(testDataDir);\n      expect(authAuditService.resourceType).toBe('auth');\n    });\n  });\n  \n  describe('logLoginAttempt', () => {\n    it('should log successful login attempt', async () => {\n      const loginData = {\n        userId: 'user-123',\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        success: true,\n        method: 'password'\n      };\n      \n      await authAuditService.logLoginAttempt(loginData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: 'user-123',\n        action: 'LOGIN',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {\n          method: 'password',\n          success: true,\n          reason: null\n        },\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'success',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n    \n    it('should log failed login attempt', async () => {\n      const loginData = {\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        success: false,\n        reason: 'Invalid password',\n        method: 'password'\n      };\n      \n      await authAuditService.logLoginAttempt(loginData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: null,\n        action: 'LOGIN',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {\n          method: 'password',\n          success: false,\n          reason: 'Invalid password'\n        },\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'failure',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n    \n    it('should include additional details', async () => {\n      const loginData = {\n        userId: 'user-123',\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        success: true,\n        method: 'oauth2',\n        details: {\n          provider: 'google',\n          email: '<EMAIL>'\n        }\n      };\n      \n      await authAuditService.logLoginAttempt(loginData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: 'user-123',\n        action: 'LOGIN',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {\n          method: 'oauth2',\n          success: true,\n          reason: null,\n          provider: 'google',\n          email: '<EMAIL>'\n        },\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'success',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n  });\n  \n  describe('logLogout', () => {\n    it('should log logout event', async () => {\n      const logoutData = {\n        userId: 'user-123',\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0'\n      };\n      \n      await authAuditService.logLogout(logoutData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: 'user-123',\n        action: 'LOGOUT',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {},\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'success',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n  });\n  \n  describe('logRegistration', () => {\n    it('should log successful registration', async () => {\n      const registrationData = {\n        userId: 'user-123',\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        success: true,\n        details: {\n          email: '<EMAIL>'\n        }\n      };\n      \n      await authAuditService.logRegistration(registrationData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: 'user-123',\n        action: 'REGISTER',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {\n          success: true,\n          reason: null,\n          email: '<EMAIL>'\n        },\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'success',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n  });\n  \n  describe('logTwoFactorAuth', () => {\n    it('should log two-factor authentication setup', async () => {\n      const twoFactorData = {\n        userId: 'user-123',\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        action: 'setup',\n        success: true\n      };\n      \n      await authAuditService.logTwoFactorAuth(twoFactorData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: 'user-123',\n        action: '2FA_SETUP',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {\n          success: true,\n          reason: null\n        },\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'success',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n    \n    it('should log failed two-factor authentication verification', async () => {\n      const twoFactorData = {\n        userId: 'user-123',\n        username: 'testuser',\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        action: 'verify',\n        success: false,\n        reason: 'Invalid token'\n      };\n      \n      await authAuditService.logTwoFactorAuth(twoFactorData);\n      \n      expect(authAuditService.auditService.logEvent).toHaveBeenCalledWith({\n        userId: 'user-123',\n        action: '2FA_VERIFY',\n        resourceType: 'auth',\n        resourceId: 'testuser',\n        details: {\n          success: false,\n          reason: 'Invalid token'\n        },\n        ip: '***********',\n        userAgent: 'Mozilla/5.0',\n        status: 'failure',\n        teamId: null,\n        environmentId: null,\n        tenantId: null\n      });\n    });\n  });\n  \n  describe('getAuthAuditLogs', () => {\n    it('should get authentication audit logs with filters', async () => {\n      const filters = {\n        userId: 'user-123',\n        startDate: '2023-01-01',\n        endDate: '2023-01-31',\n        action: 'LOGIN',\n        status: 'success',\n        page: 1,\n        limit: 10\n      };\n      \n      await authAuditService.getAuthAuditLogs(filters);\n      \n      expect(authAuditService.auditService.getAuditLogs).toHaveBeenCalledWith({\n        userId: 'user-123',\n        startDate: '2023-01-01',\n        endDate: '2023-01-31',\n        action: 'LOGIN',\n        status: 'success',\n        page: 1,\n        limit: 10,\n        resourceType: 'auth'\n      });\n    });\n  });\n});\n"], "mappings": "AAQA;AACAA,WAAA,GAAKC,IAAI,CAAC,oCAAoC,CAAC;AAAC,SAAAD,YAAA;EAAA;IAAAE;EAAA,IAAAC,OAAA;EAAAH,WAAA,GAAAA,CAAA,KAAAE,IAAA;EAAA,OAAAA,IAAA;AAAA;AAThD;AACA;AACA;;AAEA,MAAME,gBAAgB,GAAGD,OAAO,CAAC,wCAAwC,CAAC;AAC1E,MAAME,YAAY,GAAGF,OAAO,CAAC,oCAAoC,CAAC;AAClE,MAAMG,IAAI,GAAGH,OAAO,CAAC,MAAM,CAAC;AAK5BI,QAAQ,CAAC,kBAAkB,EAAE,MAAM;EACjC,IAAIC,gBAAgB;EACpB,MAAMC,WAAW,GAAGH,IAAI,CAACI,IAAI,CAACC,SAAS,EAAE,WAAW,CAAC;EAErDC,UAAU,CAAC,MAAM;IACf;IACAV,IAAI,CAACW,aAAa,CAAC,CAAC;;IAEpB;IACAR,YAAY,CAACS,kBAAkB,CAAC,OAAO;MACrCC,QAAQ,EAAEb,IAAI,CAACc,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;QAAEC,EAAE,EAAE;MAAc,CAAC,CAAC;MAC5DC,YAAY,EAAEjB,IAAI,CAACc,EAAE,CAAC,CAAC,CAACC,iBAAiB,CAAC;QACxCG,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,CAAC;;IAEH;IACAf,gBAAgB,GAAG,IAAIJ,gBAAgB,CAACK,WAAW,CAAC;EACtD,CAAC,CAAC;EAEFF,QAAQ,CAAC,aAAa,EAAE,MAAM;IAC5BiB,EAAE,CAAC,mDAAmD,EAAE,MAAM;MAC5DC,MAAM,CAACpB,YAAY,CAAC,CAACqB,oBAAoB,CAACjB,WAAW,CAAC;MACtDgB,MAAM,CAACjB,gBAAgB,CAACmB,YAAY,CAAC,CAACC,IAAI,CAAC,MAAM,CAAC;IACpD,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFrB,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCiB,EAAE,CAAC,qCAAqC,EAAE,YAAY;MACpD,MAAMK,SAAS,GAAG;QAChBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE;MACV,CAAC;MAED,MAAM3B,gBAAgB,CAAC4B,eAAe,CAACP,SAAS,CAAC;MAEjDJ,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,UAAU;QAClBQ,MAAM,EAAE,OAAO;QACfX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE;UACPL,MAAM,EAAE,UAAU;UAClBD,OAAO,EAAE,IAAI;UACbO,MAAM,EAAE;QACV,CAAC;QACDT,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFrB,EAAE,CAAC,iCAAiC,EAAE,YAAY;MAChD,MAAMK,SAAS,GAAG;QAChBE,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBC,OAAO,EAAE,KAAK;QACdO,MAAM,EAAE,kBAAkB;QAC1BN,MAAM,EAAE;MACV,CAAC;MAED,MAAM3B,gBAAgB,CAAC4B,eAAe,CAACP,SAAS,CAAC;MAEjDJ,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,IAAI;QACZQ,MAAM,EAAE,OAAO;QACfX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE;UACPL,MAAM,EAAE,UAAU;UAClBD,OAAO,EAAE,KAAK;UACdO,MAAM,EAAE;QACV,CAAC;QACDT,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFrB,EAAE,CAAC,mCAAmC,EAAE,YAAY;MAClD,MAAMK,SAAS,GAAG;QAChBC,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,QAAQ;QAChBK,OAAO,EAAE;UACPM,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT;MACF,CAAC;MAED,MAAMvC,gBAAgB,CAAC4B,eAAe,CAACP,SAAS,CAAC;MAEjDJ,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,UAAU;QAClBQ,MAAM,EAAE,OAAO;QACfX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE;UACPL,MAAM,EAAE,QAAQ;UAChBD,OAAO,EAAE,IAAI;UACbO,MAAM,EAAE,IAAI;UACZK,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAC;QACDf,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,WAAW,EAAE,MAAM;IAC1BiB,EAAE,CAAC,yBAAyB,EAAE,YAAY;MACxC,MAAMwB,UAAU,GAAG;QACjBlB,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE;MACb,CAAC;MAED,MAAMzB,gBAAgB,CAACyC,SAAS,CAACD,UAAU,CAAC;MAE5CvB,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,UAAU;QAClBQ,MAAM,EAAE,QAAQ;QAChBX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE,CAAC,CAAC;QACXR,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,iBAAiB,EAAE,MAAM;IAChCiB,EAAE,CAAC,oCAAoC,EAAE,YAAY;MACnD,MAAM0B,gBAAgB,GAAG;QACvBpB,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBC,OAAO,EAAE,IAAI;QACbM,OAAO,EAAE;UACPO,KAAK,EAAE;QACT;MACF,CAAC;MAED,MAAMvC,gBAAgB,CAAC2C,eAAe,CAACD,gBAAgB,CAAC;MAExDzB,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,UAAU;QAClBQ,MAAM,EAAE,UAAU;QAClBX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE;UACPN,OAAO,EAAE,IAAI;UACbO,MAAM,EAAE,IAAI;UACZM,KAAK,EAAE;QACT,CAAC;QACDf,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCiB,EAAE,CAAC,4CAA4C,EAAE,YAAY;MAC3D,MAAM4B,aAAa,GAAG;QACpBtB,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBK,MAAM,EAAE,OAAO;QACfJ,OAAO,EAAE;MACX,CAAC;MAED,MAAM1B,gBAAgB,CAAC6C,gBAAgB,CAACD,aAAa,CAAC;MAEtD3B,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,UAAU;QAClBQ,MAAM,EAAE,WAAW;QACnBX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE;UACPN,OAAO,EAAE,IAAI;UACbO,MAAM,EAAE;QACV,CAAC;QACDT,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFrB,EAAE,CAAC,0DAA0D,EAAE,YAAY;MACzE,MAAM4B,aAAa,GAAG;QACpBtB,MAAM,EAAE,UAAU;QAClBC,QAAQ,EAAE,UAAU;QACpBC,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBK,MAAM,EAAE,QAAQ;QAChBJ,OAAO,EAAE,KAAK;QACdO,MAAM,EAAE;MACV,CAAC;MAED,MAAMjC,gBAAgB,CAAC6C,gBAAgB,CAACD,aAAa,CAAC;MAEtD3B,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAACtB,QAAQ,CAAC,CAACW,oBAAoB,CAAC;QAClEI,MAAM,EAAE,UAAU;QAClBQ,MAAM,EAAE,YAAY;QACpBX,YAAY,EAAE,MAAM;QACpBY,UAAU,EAAE,UAAU;QACtBC,OAAO,EAAE;UACPN,OAAO,EAAE,KAAK;UACdO,MAAM,EAAE;QACV,CAAC;QACDT,EAAE,EAAE,aAAa;QACjBC,SAAS,EAAE,aAAa;QACxBS,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,IAAI;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFtC,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCiB,EAAE,CAAC,mDAAmD,EAAE,YAAY;MAClE,MAAM8B,OAAO,GAAG;QACdxB,MAAM,EAAE,UAAU;QAClByB,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBlB,MAAM,EAAE,OAAO;QACfI,MAAM,EAAE,SAAS;QACjBpB,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE;MACT,CAAC;MAED,MAAMf,gBAAgB,CAACiD,gBAAgB,CAACH,OAAO,CAAC;MAEhD7B,MAAM,CAACjB,gBAAgB,CAAC6B,YAAY,CAAClB,YAAY,CAAC,CAACO,oBAAoB,CAAC;QACtEI,MAAM,EAAE,UAAU;QAClByB,SAAS,EAAE,YAAY;QACvBC,OAAO,EAAE,YAAY;QACrBlB,MAAM,EAAE,OAAO;QACfI,MAAM,EAAE,SAAS;QACjBpB,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,EAAE;QACTI,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}