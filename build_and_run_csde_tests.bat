@echo off
REM Build and run CSDE tests in Docker

REM Build the Docker image
echo Building CSDE Docker image...
docker build -t novafuse/csde-testing -f csde/Dockerfile .

REM Run the container without GPU support
echo Running CSDE tests with CPU only...
docker run -v %cd%/results:/app/results novafuse/csde-testing --output-dir results

REM Display results
echo Test results:
type results\summary_results.txt
