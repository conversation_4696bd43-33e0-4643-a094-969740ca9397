/**
 * Parameter Handling Tests for NovaConnect Universal API Connector
 * 
 * These tests verify that the connector can handle various parameter types.
 */

const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { 
  startAllServices, 
  stopAllServices,
  registryUrl,
  authUrl,
  executorUrl,
  mockApiUrl
} = require('../setup');

// Test data
const connector = require('../connectors/parameter-handling-connector.json');

// Test credential
const credential = {
  name: 'Parameter Handling Test Credential',
  authType: 'API_KEY',
  credentials: {
    apiKey: 'valid-api-key',
    headerName: 'X-API-Key'
  }
};

// Store connector and credential IDs
let connectorId;
let credentialId;

describe('Parameter Handling Tests', () => {
  // Start services before all tests
  beforeAll(async () => {
    await startAllServices();
    
    // Clear request history
    await axios.post(`${mockApiUrl}/clear-history`);
    
    // Register connector
    const connResponse = await axios.post(`${registryUrl}/connectors`, connector);
    connectorId = connResponse.data.id;
    
    // Create credential
    credential.connectorId = connectorId;
    const credResponse = await axios.post(`${authUrl}/credentials`, credential);
    credentialId = credResponse.data.id;
  }, 60000);
  
  // Stop services after all tests
  afterAll(async () => {
    // Clean up test data
    try {
      await axios.delete(`${registryUrl}/connectors/${connectorId}`);
    } catch (error) {
      console.error(`Error deleting connector ${connectorId}:`, error.message);
    }
    
    try {
      await axios.delete(`${authUrl}/credentials/${credentialId}`);
    } catch (error) {
      console.error(`Error deleting credential ${credentialId}:`, error.message);
    }
    
    stopAllServices();
  });
  
  // Test query parameters
  describe('Query Parameters', () => {
    it('should handle query parameters', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/queryParameters`, {
        credentialId,
        parameters: {
          query: {
            param1: 'value1',
            param2: 'value2'
          }
        },
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('param1', 'value1');
      expect(response.data).toHaveProperty('param2', 'value2');
      
      // Verify the request was made with the correct query parameters
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const queryRequest = historyResponse.data.find(req => 
        req.path === '/parameters/query' && 
        req.query.param1 === 'value1' && 
        req.query.param2 === 'value2'
      );
      
      expect(queryRequest).toBeDefined();
    });
    
    it('should handle required query parameters', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/queryParameters`, {
          credentialId,
          parameters: {
            query: {
              // param1 is required but missing
              param2: 'value2'
            }
          },
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('param1 is required');
      }
    });
    
    it('should handle default values for optional query parameters', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/queryParameters`, {
        credentialId,
        parameters: {
          query: {
            param1: 'value1'
            // param2 is optional and will use default value
          }
        },
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('param1', 'value1');
      expect(response.data).toHaveProperty('param2', 'default');
    });
  });
  
  // Test path parameters
  describe('Path Parameters', () => {
    it('should handle path parameters', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/pathParameters`, {
        credentialId,
        parameters: {
          path: {
            id: '12345'
          }
        },
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('id', '12345');
      
      // Verify the request was made with the correct path parameter
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const pathRequest = historyResponse.data.find(req => 
        req.path === '/parameters/path/12345'
      );
      
      expect(pathRequest).toBeDefined();
    });
    
    it('should handle required path parameters', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/pathParameters`, {
          credentialId,
          parameters: {
            path: {
              // id is required but missing
            }
          },
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('path parameter');
      }
    });
  });
  
  // Test body parameters
  describe('Body Parameters', () => {
    it('should handle body parameters', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/bodyParameters`, {
        credentialId,
        parameters: {
          body: {
            param1: 'value1',
            param2: 'value2'
          }
        },
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('param1', 'value1');
      expect(response.data).toHaveProperty('param2', 'value2');
      
      // Verify the request was made with the correct body parameters
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const bodyRequest = historyResponse.data.find(req => 
        req.path === '/parameters/body' && 
        req.method === 'POST' &&
        req.body.param1 === 'value1' && 
        req.body.param2 === 'value2'
      );
      
      expect(bodyRequest).toBeDefined();
    });
    
    it('should handle required body parameters', async () => {
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/bodyParameters`, {
          credentialId,
          parameters: {
            body: {
              // param1 is required but missing
              param2: 'value2'
            }
          },
          userId: 'test-user'
        });
        
        fail('Expected request to fail');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toContain('param1 is required');
      }
    });
    
    it('should handle default values for optional body parameters', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/bodyParameters`, {
        credentialId,
        parameters: {
          body: {
            param1: 'value1'
            // param2 is optional and will use default value
          }
        },
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      expect(response.data).toHaveProperty('param1', 'value1');
      expect(response.data).toHaveProperty('param2', 'default');
    });
  });
  
  // Test parameter validation
  describe('Parameter Validation', () => {
    it('should validate parameter types', async () => {
      // The connector defines param1 as a string
      // Let's try to send a number
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/queryParameters`, {
          credentialId,
          parameters: {
            query: {
              param1: 12345, // Number instead of string
              param2: 'value2'
            }
          },
          userId: 'test-user'
        });
        
        // This should still work because the number will be converted to a string
        // in the URL query parameters
      } catch (error) {
        fail('Request should not fail');
      }
      
      // Verify the request was made with the param1 converted to a string
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const queryRequest = historyResponse.data.find(req => 
        req.path === '/parameters/query' && 
        req.query.param1 === '12345'
      );
      
      expect(queryRequest).toBeDefined();
    });
    
    it('should handle special characters in parameters', async () => {
      const specialValue = 'value with spaces & special chars: !@#$%^&*()';
      
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/queryParameters`, {
        credentialId,
        parameters: {
          query: {
            param1: specialValue,
            param2: 'value2'
          }
        },
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Verify the request was made with the special characters properly encoded
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const queryRequest = historyResponse.data.find(req => 
        req.path === '/parameters/query' && 
        req.query.param1 === specialValue
      );
      
      expect(queryRequest).toBeDefined();
    });
  });
  
  // Test parameter combinations
  describe('Parameter Combinations', () => {
    it('should handle combinations of parameter types', async () => {
      // Create a custom endpoint that uses all parameter types
      const customConnector = {
        ...connector,
        endpoints: [
          ...connector.endpoints,
          {
            id: 'combinedParameters',
            name: 'Combined Parameters',
            path: '/parameters/path/{{id}}',
            method: 'POST',
            parameters: {
              query: {
                queryParam: {
                  type: 'string',
                  required: true
                }
              },
              path: {
                id: {
                  type: 'string',
                  required: true
                }
              },
              body: {
                bodyParam: {
                  type: 'string',
                  required: true
                }
              }
            },
            response: {
              successCode: 200
            }
          }
        ]
      };
      
      // Update the connector
      await axios.put(`${registryUrl}/connectors/${connectorId}`, customConnector);
      
      // Set up the mock API to handle this request
      await axios.post(`${mockApiUrl}/parameters/path/12345?queryParam=queryValue`, {
        bodyParam: 'bodyValue'
      });
      
      // Execute the endpoint
      try {
        await axios.post(`${executorUrl}/execute/${connectorId}/combinedParameters`, {
          credentialId,
          parameters: {
            query: {
              queryParam: 'queryValue'
            },
            path: {
              id: '12345'
            },
            body: {
              bodyParam: 'bodyValue'
            }
          },
          userId: 'test-user'
        });
        
        // This might fail because our mock API isn't set up to handle this specific combination,
        // but we can still check that the request was made correctly
      } catch (error) {
        // Ignore errors for this test
      }
      
      // Verify the request was made with all parameter types
      const historyResponse = await axios.get(`${mockApiUrl}/history`);
      const combinedRequest = historyResponse.data.find(req => 
        req.path === '/parameters/path/12345' && 
        req.query.queryParam === 'queryValue' && 
        req.method === 'POST' &&
        req.body.bodyParam === 'bodyValue'
      );
      
      expect(combinedRequest).toBeDefined();
    });
  });
});
