/**
 * NovaFuse Universal API Connector - Rate Limiting Middleware
 * 
 * This module provides middleware for rate limiting API requests.
 */

const { createLogger } = require('../utils/logger');

const logger = createLogger('rate-limiter');

// Simple in-memory store for rate limiting
// In production, this should be replaced with Redis or another distributed store
const requestStore = new Map();

/**
 * Rate limiting middleware
 * 
 * @param {Object} options - Rate limiting options
 * @param {number} options.windowMs - Time window in milliseconds
 * @param {number} options.max - Maximum number of requests per window
 * @param {string} options.keyGenerator - Function to generate key from request
 * @returns {Function} - The rate limiting middleware
 */
function rateLimiter(options = {}) {
  const windowMs = options.windowMs || 60 * 1000; // Default: 1 minute
  const max = options.max || 100; // Default: 100 requests per minute
  const keyGenerator = options.keyGenerator || ((req) => req.ip);
  
  // Clean up old entries every windowMs
  setInterval(() => {
    const now = Date.now();
    
    for (const [key, data] of requestStore.entries()) {
      if (now - data.startTime > windowMs) {
        requestStore.delete(key);
      }
    }
  }, windowMs);
  
  return (req, res, next) => {
    try {
      const key = keyGenerator(req);
      const now = Date.now();
      
      // Get or create request data
      let requestData = requestStore.get(key);
      
      if (!requestData || now - requestData.startTime > windowMs) {
        // First request or window expired, create new entry
        requestData = {
          count: 1,
          startTime: now
        };
        
        requestStore.set(key, requestData);
      } else {
        // Increment request count
        requestData.count++;
        
        // Check if limit exceeded
        if (requestData.count > max) {
          logger.warn(`Rate limit exceeded for ${key}`);
          
          // Set rate limit headers
          res.setHeader('X-RateLimit-Limit', max);
          res.setHeader('X-RateLimit-Remaining', 0);
          res.setHeader('X-RateLimit-Reset', Math.ceil((requestData.startTime + windowMs) / 1000));
          res.setHeader('Retry-After', Math.ceil((requestData.startTime + windowMs - now) / 1000));
          
          return res.status(429).json({
            error: {
              message: 'Too many requests, please try again later',
              code: 'RATE_LIMIT_EXCEEDED'
            }
          });
        }
      }
      
      // Set rate limit headers
      res.setHeader('X-RateLimit-Limit', max);
      res.setHeader('X-RateLimit-Remaining', Math.max(0, max - requestData.count));
      res.setHeader('X-RateLimit-Reset', Math.ceil((requestData.startTime + windowMs) / 1000));
      
      next();
    } catch (error) {
      logger.error('Rate limiting error:', { error });
      next(error);
    }
  };
}

module.exports = {
  rateLimiter
};
