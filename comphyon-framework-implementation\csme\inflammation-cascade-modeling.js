/**
 * Inflammation Cascade Modeling
 * 
 * This module implements the Inflammation Cascade Modeling component of the CSME.
 * It models inflammatory pathways and their systemic effects.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * InflammationCascadeModeling class
 */
class InflammationCascadeModeling extends EventEmitter {
  /**
   * Create a new InflammationCascadeModeling instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 12000, // ms
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      thresholds: {
        inflammation: {
          minimal: 0.2,
          mild: 0.4,
          moderate: 0.6,
          severe: 0.8,
          critical: 0.95
        }
      },
      ...options
    };
    
    // Initialize state
    this.state = {
      inflammationLevel: 0.3, // Normalized inflammation level (0-1)
      inflammationHistory: [],
      inflammationStatus: 'mild', // minimal, mild, moderate, severe, critical
      triggers: new Map(), // id -> trigger object
      mediators: new Map(), // id -> mediator object
      systemicEffects: {
        immuneActivation: 0.3,
        tissueRepair: 0.4,
        oxidativeStress: 0.3,
        metabolicDisruption: 0.2
      },
      affectedSystems: new Map(), // system -> impact level
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      totalUpdates: 0,
      triggersAdded: 0,
      triggersRemoved: 0,
      mediatorsAdded: 0,
      mediatorsRemoved: 0,
      statusChanges: 0
    };
    
    // Initialize affected systems
    this._initializeAffectedSystems();
    
    if (this.options.enableLogging) {
      console.log('InflammationCascadeModeling initialized');
    }
  }
  
  /**
   * Start the inflammation cascade modeling
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('InflammationCascadeModeling is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    this._startUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('InflammationCascadeModeling started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the inflammation cascade modeling
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('InflammationCascadeModeling is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    this._stopUpdateInterval();
    
    if (this.options.enableLogging) {
      console.log('InflammationCascadeModeling stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Set inflammation level
   * @param {number} level - Inflammation level (0-1)
   * @param {Object} metadata - Additional metadata
   * @returns {number} - Updated inflammation level
   */
  setInflammationLevel(level, metadata = {}) {
    const startTime = performance.now();
    
    if (typeof level !== 'number' || level < 0 || level > 1) {
      throw new Error('Inflammation level must be a number between 0 and 1');
    }
    
    // Update state
    this.state.inflammationLevel = level;
    this.state.lastUpdateTime = Date.now();
    
    // Update inflammation status
    this._updateInflammationStatus();
    
    // Update systemic effects
    this._updateSystemicEffects();
    
    // Update affected systems
    this._updateAffectedSystems();
    
    // Add to history
    this.state.inflammationHistory.push({
      inflammationLevel: this.state.inflammationLevel,
      inflammationStatus: this.state.inflammationStatus,
      systemicEffects: { ...this.state.systemicEffects },
      timestamp: Date.now(),
      metadata
    });
    
    // Limit history size
    if (this.state.inflammationHistory.length > this.options.historySize) {
      this.state.inflammationHistory.shift();
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    
    // Emit update event
    this.emit('inflammation-update', {
      inflammationLevel: this.state.inflammationLevel,
      inflammationStatus: this.state.inflammationStatus,
      systemicEffects: { ...this.state.systemicEffects },
      timestamp: Date.now()
    });
    
    return this.state.inflammationLevel;
  }
  
  /**
   * Add inflammation trigger
   * @param {Object} trigger - Trigger object
   * @returns {Object} - Added trigger
   */
  addTrigger(trigger) {
    const startTime = performance.now();
    
    if (!trigger || typeof trigger !== 'object') {
      throw new Error('Trigger must be an object');
    }
    
    if (!trigger.id) {
      trigger.id = `trigger-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    trigger = {
      type: 'generic', // pathogen, injury, autoimmune, etc.
      strength: 0.5, // 0-1 (strength of trigger)
      duration: 48, // hours
      status: 'active', // active, inactive
      startedAt: Date.now(),
      ...trigger
    };
    
    // Add to state
    this.state.triggers.set(trigger.id, trigger);
    
    // Update inflammation level
    this._updateInflammationLevel();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.triggersAdded++;
    
    // Emit event
    this.emit('trigger-added', trigger);
    
    if (this.options.enableLogging) {
      console.log(`InflammationCascadeModeling: Added ${trigger.type} trigger ${trigger.id}`);
    }
    
    return trigger;
  }
  
  /**
   * Remove inflammation trigger
   * @param {string} triggerId - Trigger ID
   * @returns {boolean} - Success status
   */
  removeTrigger(triggerId) {
    const startTime = performance.now();
    
    if (!triggerId || !this.state.triggers.has(triggerId)) {
      return false;
    }
    
    // Get trigger
    const trigger = this.state.triggers.get(triggerId);
    
    // Remove from state
    this.state.triggers.delete(triggerId);
    
    // Update inflammation level
    this._updateInflammationLevel();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.triggersRemoved++;
    
    // Emit event
    this.emit('trigger-removed', trigger);
    
    if (this.options.enableLogging) {
      console.log(`InflammationCascadeModeling: Removed trigger ${triggerId}`);
    }
    
    return true;
  }
  
  /**
   * Add inflammation mediator
   * @param {Object} mediator - Mediator object
   * @returns {Object} - Added mediator
   */
  addMediator(mediator) {
    const startTime = performance.now();
    
    if (!mediator || typeof mediator !== 'object') {
      throw new Error('Mediator must be an object');
    }
    
    if (!mediator.id) {
      mediator.id = `mediator-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    // Set default values
    mediator = {
      type: 'generic', // cytokine, chemokine, enzyme, etc.
      effect: 'pro-inflammatory', // pro-inflammatory or anti-inflammatory
      strength: 0.5, // 0-1 (strength of effect)
      halfLife: 24, // hours
      status: 'active', // active, inactive
      startedAt: Date.now(),
      ...mediator
    };
    
    // Add to state
    this.state.mediators.set(mediator.id, mediator);
    
    // Update inflammation level
    this._updateInflammationLevel();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.mediatorsAdded++;
    
    // Emit event
    this.emit('mediator-added', mediator);
    
    if (this.options.enableLogging) {
      console.log(`InflammationCascadeModeling: Added ${mediator.type} mediator ${mediator.id}`);
    }
    
    return mediator;
  }
  
  /**
   * Remove inflammation mediator
   * @param {string} mediatorId - Mediator ID
   * @returns {boolean} - Success status
   */
  removeMediator(mediatorId) {
    const startTime = performance.now();
    
    if (!mediatorId || !this.state.mediators.has(mediatorId)) {
      return false;
    }
    
    // Get mediator
    const mediator = this.state.mediators.get(mediatorId);
    
    // Remove from state
    this.state.mediators.delete(mediatorId);
    
    // Update inflammation level
    this._updateInflammationLevel();
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.totalUpdates++;
    this.metrics.mediatorsRemoved++;
    
    // Emit event
    this.emit('mediator-removed', mediator);
    
    if (this.options.enableLogging) {
      console.log(`InflammationCascadeModeling: Removed mediator ${mediatorId}`);
    }
    
    return true;
  }
  
  /**
   * Get inflammation level
   * @returns {number} - Current inflammation level
   */
  getInflammationLevel() {
    return this.state.inflammationLevel;
  }
  
  /**
   * Get inflammation status
   * @returns {string} - Current inflammation status
   */
  getInflammationStatus() {
    return this.state.inflammationStatus;
  }
  
  /**
   * Get systemic effects
   * @returns {Object} - Current systemic effects
   */
  getSystemicEffects() {
    return { ...this.state.systemicEffects };
  }
  
  /**
   * Get affected systems
   * @returns {Object} - Affected systems and their impact levels
   */
  getAffectedSystems() {
    return Object.fromEntries(this.state.affectedSystems);
  }
  
  /**
   * Get current state
   * @returns {Object} - Current state
   */
  getState() {
    return {
      inflammationLevel: this.state.inflammationLevel,
      inflammationStatus: this.state.inflammationStatus,
      systemicEffects: { ...this.state.systemicEffects },
      affectedSystems: Object.fromEntries(this.state.affectedSystems),
      triggerCount: this.state.triggers.size,
      mediatorCount: this.state.mediators.size,
      inflammationHistory: [...this.state.inflammationHistory],
      isRunning: this.state.isRunning,
      lastUpdateTime: this.state.lastUpdateTime
    };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get triggers
   * @param {string} type - Optional type filter
   * @returns {Array} - Triggers
   */
  getTriggers(type) {
    const triggers = Array.from(this.state.triggers.values());
    
    if (type) {
      return triggers.filter(t => t.type === type);
    }
    
    return triggers;
  }
  
  /**
   * Get mediators
   * @param {string} type - Optional type filter
   * @returns {Array} - Mediators
   */
  getMediators(type) {
    const mediators = Array.from(this.state.mediators.values());
    
    if (type) {
      return mediators.filter(m => m.type === type);
    }
    
    return mediators;
  }
  
  /**
   * Initialize affected systems
   * @private
   */
  _initializeAffectedSystems() {
    // Initialize with common systems
    this.state.affectedSystems.set('cardiovascular', 0.2);
    this.state.affectedSystems.set('respiratory', 0.2);
    this.state.affectedSystems.set('immune', 0.3);
    this.state.affectedSystems.set('nervous', 0.1);
    this.state.affectedSystems.set('digestive', 0.2);
    this.state.affectedSystems.set('endocrine', 0.2);
    this.state.affectedSystems.set('musculoskeletal', 0.2);
  }
  
  /**
   * Update inflammation status
   * @private
   */
  _updateInflammationStatus() {
    const { inflammationLevel } = this.state;
    const { thresholds } = this.options;
    
    let newStatus = 'mild';
    
    if (inflammationLevel >= thresholds.inflammation.critical) {
      newStatus = 'critical';
    } else if (inflammationLevel >= thresholds.inflammation.severe) {
      newStatus = 'severe';
    } else if (inflammationLevel >= thresholds.inflammation.moderate) {
      newStatus = 'moderate';
    } else if (inflammationLevel <= thresholds.inflammation.minimal) {
      newStatus = 'minimal';
    }
    
    // If status changed, emit event
    if (newStatus !== this.state.inflammationStatus) {
      this.state.inflammationStatus = newStatus;
      this.metrics.statusChanges++;
      
      // Emit status change event
      this.emit('status-change', {
        inflammationStatus: this.state.inflammationStatus,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`InflammationCascadeModeling: Inflammation status changed to ${this.state.inflammationStatus}`);
      }
    }
  }
  
  /**
   * Update systemic effects
   * @private
   */
  _updateSystemicEffects() {
    const { inflammationLevel } = this.state;
    
    // Update immune activation (directly proportional to inflammation)
    this.state.systemicEffects.immuneActivation = inflammationLevel;
    
    // Update tissue repair (bell curve - optimal at moderate inflammation)
    this.state.systemicEffects.tissueRepair = 1 - Math.abs(inflammationLevel - 0.5) * 2;
    
    // Update oxidative stress (directly proportional to inflammation)
    this.state.systemicEffects.oxidativeStress = inflammationLevel;
    
    // Update metabolic disruption (directly proportional to inflammation)
    this.state.systemicEffects.metabolicDisruption = inflammationLevel;
  }
  
  /**
   * Update affected systems
   * @private
   */
  _updateAffectedSystems() {
    const { inflammationLevel, systemicEffects } = this.state;
    
    // Update each system based on inflammation level and systemic effects
    for (const [system, impact] of this.state.affectedSystems.entries()) {
      let newImpact = impact;
      
      // Apply system-specific logic
      switch (system) {
        case 'cardiovascular':
          // Cardiovascular impact increases with inflammation and oxidative stress
          newImpact = (inflammationLevel * 0.7 + systemicEffects.oxidativeStress * 0.3);
          break;
          
        case 'respiratory':
          // Respiratory impact increases with inflammation
          newImpact = inflammationLevel;
          break;
          
        case 'immune':
          // Immune impact directly tied to immune activation
          newImpact = systemicEffects.immuneActivation;
          break;
          
        case 'nervous':
          // Nervous system impact increases with inflammation and metabolic disruption
          newImpact = (inflammationLevel * 0.5 + systemicEffects.metabolicDisruption * 0.5);
          break;
          
        case 'digestive':
          // Digestive impact increases with inflammation
          newImpact = inflammationLevel;
          break;
          
        case 'endocrine':
          // Endocrine impact increases with inflammation and metabolic disruption
          newImpact = (inflammationLevel * 0.4 + systemicEffects.metabolicDisruption * 0.6);
          break;
          
        case 'musculoskeletal':
          // Musculoskeletal impact increases with inflammation and decreases with tissue repair
          newImpact = (inflammationLevel * 0.8 - systemicEffects.tissueRepair * 0.2);
          break;
          
        default:
          // Default impact directly tied to inflammation level
          newImpact = inflammationLevel;
      }
      
      // Ensure impact is between 0 and 1
      newImpact = Math.max(0, Math.min(1, newImpact));
      
      // Update impact
      this.state.affectedSystems.set(system, newImpact);
    }
  }
  
  /**
   * Update inflammation level based on triggers and mediators
   * @private
   */
  _updateInflammationLevel() {
    // Start with baseline inflammation (0.2)
    let inflammationLevel = 0.2;
    
    // Apply trigger effects
    for (const trigger of this.state.triggers.values()) {
      if (trigger.status === 'active') {
        // Triggers increase inflammation level
        inflammationLevel += trigger.strength * 0.4;
      }
    }
    
    // Apply mediator effects
    for (const mediator of this.state.mediators.values()) {
      if (mediator.status === 'active') {
        if (mediator.effect === 'pro-inflammatory') {
          // Pro-inflammatory mediators increase inflammation level
          inflammationLevel += mediator.strength * 0.3;
        } else if (mediator.effect === 'anti-inflammatory') {
          // Anti-inflammatory mediators decrease inflammation level
          inflammationLevel -= mediator.strength * 0.3;
        }
      }
    }
    
    // Ensure inflammation level is between 0 and 1
    inflammationLevel = Math.max(0, Math.min(1, inflammationLevel));
    
    // Update inflammation level
    this.setInflammationLevel(inflammationLevel, { source: 'cascade_update' });
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      if (this.state.isRunning) {
        // In a real implementation, this would fetch real-time data
        // For now, just simulate some changes
        this._simulateChanges();
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop update interval
   * @private
   */
  _stopUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
  }
  
  /**
   * Simulate changes
   * @private
   */
  _simulateChanges() {
    // Simulate random changes to triggers and mediators
    const rand = Math.random();
    
    if (rand < 0.1) {
      // Add a new trigger
      this.addTrigger({
        type: this._randomTriggerType(),
        strength: Math.random() * 0.5,
        duration: Math.floor(Math.random() * 48) + 1
      });
    } else if (rand < 0.2 && this.state.triggers.size > 0) {
      // Remove a random trigger
      const triggers = Array.from(this.state.triggers.keys());
      const randomTriggerId = triggers[Math.floor(Math.random() * triggers.length)];
      this.removeTrigger(randomTriggerId);
    } else if (rand < 0.3) {
      // Add a new mediator
      this.addMediator({
        type: this._randomMediatorType(),
        effect: Math.random() < 0.7 ? 'pro-inflammatory' : 'anti-inflammatory',
        strength: Math.random() * 0.5,
        halfLife: Math.floor(Math.random() * 24) + 1
      });
    } else if (rand < 0.4 && this.state.mediators.size > 0) {
      // Remove a random mediator
      const mediators = Array.from(this.state.mediators.keys());
      const randomMediatorId = mediators[Math.floor(Math.random() * mediators.length)];
      this.removeMediator(randomMediatorId);
    }
  }
  
  /**
   * Generate random trigger type
   * @returns {string} - Random trigger type
   * @private
   */
  _randomTriggerType() {
    const types = [
      'pathogen',
      'injury',
      'autoimmune',
      'allergen',
      'toxin'
    ];
    
    return types[Math.floor(Math.random() * types.length)];
  }
  
  /**
   * Generate random mediator type
   * @returns {string} - Random mediator type
   * @private
   */
  _randomMediatorType() {
    const types = [
      'cytokine',
      'chemokine',
      'enzyme',
      'reactive_oxygen_species',
      'lipid_mediator'
    ];
    
    return types[Math.floor(Math.random() * types.length)];
  }
}

module.exports = InflammationCascadeModeling;
