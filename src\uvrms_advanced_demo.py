"""
Advanced demo script for the Universal Vendor Risk Management System (UVRMS).

This script demonstrates the enhanced risk assessment methodologies and
fourth-party risk management capabilities of the UVRMS.
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UVRMS
from uvrms import (
    VendorManager,
    AssessmentManager,
    RiskManager,
    MonitoringManager,
    RelationshipManager,
    calculate_inherent_risk_score,
    calculate_residual_risk_score,
    calculate_impact_likelihood_score,
    calculate_compliance_based_score,
    calculate_industry_specific_score,
    calculate_composite_risk_score,
    get_default_inherent_risk_factors,
    get_default_composite_weights,
    get_default_industry_benchmarks,
    get_default_compliance_requirements
)

def main():
    """Run the advanced UVRMS demo."""
    logger.info("Starting advanced UVRMS demo")
    
    # Create output directory
    output_dir = os.path.join(os.getcwd(), 'advanced_demo_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize managers
    vendor_manager = VendorManager()
    assessment_manager = AssessmentManager()
    risk_manager = RiskManager()
    monitoring_manager = MonitoringManager()
    relationship_manager = RelationshipManager()
    
    # Step 1: Create vendors
    logger.info("Step 1: Creating vendors")
    
    # Create primary vendor
    primary_vendor = vendor_manager.create_vendor({
        'name': 'Primary Corp',
        'description': 'Our primary organization',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'technology',
        'size': 'enterprise',
        'criticality': 'critical',
        'data_types': ['PII', 'PCI', 'confidential'],
        'access_level': 'admin',
        'regulations': ['GDPR', 'CCPA', 'PCI-DSS'],
        'relationship_duration': 0,  # This is us
        'security_maturity': 'high',
        'has_security_certification': True
    })
    
    # Create direct vendors
    direct_vendor1 = vendor_manager.create_vendor({
        'name': 'Cloud Provider Inc',
        'description': 'Cloud infrastructure provider',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'technology',
        'size': 'enterprise',
        'criticality': 'high',
        'data_types': ['PII', 'confidential'],
        'access_level': 'admin',
        'regulations': ['GDPR', 'CCPA', 'SOC2'],
        'relationship_duration': 3,
        'security_maturity': 'high',
        'has_security_certification': True,
        'has_soc2': True,
        'uptime_percentage': 99.95
    })
    
    direct_vendor2 = vendor_manager.create_vendor({
        'name': 'Payment Processor LLC',
        'description': 'Payment processing service',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'financial',
        'size': 'mid-market',
        'criticality': 'critical',
        'data_types': ['PCI', 'confidential'],
        'access_level': 'write',
        'regulations': ['PCI-DSS', 'SOC2'],
        'relationship_duration': 2,
        'financial_stability': 'high',
        'has_pci_dss': True,
        'years_in_business': 8
    })
    
    direct_vendor3 = vendor_manager.create_vendor({
        'name': 'Analytics Partner',
        'description': 'Data analytics service',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'technology',
        'size': 'small',
        'criticality': 'medium',
        'data_types': ['PII', 'internal'],
        'access_level': 'read',
        'regulations': ['GDPR', 'CCPA'],
        'relationship_duration': 1,
        'security_maturity': 'medium',
        'has_security_certification': False,
        'uptime_percentage': 99.5
    })
    
    # Create fourth-party vendors
    fourth_party1 = vendor_manager.create_vendor({
        'name': 'Data Center Operator',
        'description': 'Physical data center operator',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'technology',
        'size': 'enterprise',
        'criticality': 'high',
        'data_types': ['confidential'],
        'access_level': 'admin',
        'regulations': ['SOC2'],
        'relationship_duration': 5,
        'security_maturity': 'high',
        'has_security_certification': True,
        'uptime_percentage': 99.99
    })
    
    fourth_party2 = vendor_manager.create_vendor({
        'name': 'Security Service Provider',
        'description': 'Security monitoring service',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'technology',
        'size': 'mid-market',
        'criticality': 'high',
        'data_types': ['confidential'],
        'access_level': 'read',
        'regulations': ['SOC2'],
        'relationship_duration': 4,
        'security_maturity': 'high',
        'has_security_certification': True
    })
    
    fourth_party3 = vendor_manager.create_vendor({
        'name': 'Fraud Detection Startup',
        'description': 'AI-based fraud detection',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'financial',
        'size': 'startup',
        'criticality': 'medium',
        'data_types': ['PCI', 'confidential'],
        'access_level': 'read',
        'regulations': ['PCI-DSS'],
        'relationship_duration': 1,
        'financial_stability': 'medium',
        'has_pci_dss': True,
        'years_in_business': 2
    })
    
    fourth_party4 = vendor_manager.create_vendor({
        'name': 'Database Provider',
        'description': 'Database as a service',
        'contact_info': {
            'email': '<EMAIL>',
            'phone': '************'
        },
        'industry': 'technology',
        'size': 'mid-market',
        'criticality': 'high',
        'data_types': ['PII', 'confidential'],
        'access_level': 'admin',
        'regulations': ['GDPR', 'CCPA', 'SOC2'],
        'relationship_duration': 3,
        'security_maturity': 'medium',
        'has_security_certification': True,
        'uptime_percentage': 99.9
    })
    
    # Step 2: Create vendor relationships
    logger.info("Step 2: Creating vendor relationships")
    
    # Create direct vendor relationships
    direct_rel1 = relationship_manager.create_relationship({
        'from_vendor_id': primary_vendor['id'],
        'to_vendor_id': direct_vendor1['id'],
        'type_id': 'service_provider',
        'description': 'Cloud infrastructure services',
        'data_shared': ['PII', 'confidential'],
        'services_provided': ['compute', 'storage', 'networking'],
        'criticality': 'critical'
    })
    
    direct_rel2 = relationship_manager.create_relationship({
        'from_vendor_id': primary_vendor['id'],
        'to_vendor_id': direct_vendor2['id'],
        'type_id': 'data_processor',
        'description': 'Payment processing services',
        'data_shared': ['PCI'],
        'services_provided': ['payment_processing', 'fraud_detection'],
        'criticality': 'critical'
    })
    
    direct_rel3 = relationship_manager.create_relationship({
        'from_vendor_id': primary_vendor['id'],
        'to_vendor_id': direct_vendor3['id'],
        'type_id': 'technology_partner',
        'description': 'Data analytics services',
        'data_shared': ['PII'],
        'services_provided': ['analytics', 'reporting'],
        'criticality': 'medium'
    })
    
    # Create fourth-party relationships
    fourth_rel1 = relationship_manager.create_relationship({
        'from_vendor_id': direct_vendor1['id'],
        'to_vendor_id': fourth_party1['id'],
        'type_id': 'service_provider',
        'description': 'Data center services',
        'data_shared': ['confidential'],
        'services_provided': ['physical_hosting', 'power', 'cooling'],
        'criticality': 'high'
    })
    
    fourth_rel2 = relationship_manager.create_relationship({
        'from_vendor_id': direct_vendor1['id'],
        'to_vendor_id': fourth_party2['id'],
        'type_id': 'technology_partner',
        'description': 'Security monitoring services',
        'data_shared': ['confidential'],
        'services_provided': ['security_monitoring', 'incident_response'],
        'criticality': 'high'
    })
    
    fourth_rel3 = relationship_manager.create_relationship({
        'from_vendor_id': direct_vendor2['id'],
        'to_vendor_id': fourth_party3['id'],
        'type_id': 'technology_partner',
        'description': 'Fraud detection services',
        'data_shared': ['PCI'],
        'services_provided': ['fraud_detection'],
        'criticality': 'medium'
    })
    
    fourth_rel4 = relationship_manager.create_relationship({
        'from_vendor_id': direct_vendor1['id'],
        'to_vendor_id': fourth_party4['id'],
        'type_id': 'service_provider',
        'description': 'Database services',
        'data_shared': ['PII', 'confidential'],
        'services_provided': ['database'],
        'criticality': 'high'
    })
    
    # Step 3: Create assessments
    logger.info("Step 3: Creating and completing assessments")
    
    # Create and complete security assessment for direct vendor 1
    assessment1 = assessment_manager.create_assessment(
        direct_vendor1['id'],
        'security_assessment',
        {'name': 'Annual Security Assessment - Cloud Provider'}
    )
    
    # Answer questions for the assessment
    for section in assessment1['sections']:
        for question in section['questions']:
            # Simulate good security practices for this vendor
            if question['id'] in ['security_policy_1', 'security_policy_2', 'security_policy_3',
                                'access_control_1', 'access_control_2', 'data_protection_1',
                                'data_protection_2', 'data_protection_3']:
                answer = 'yes'
            elif question['id'] == 'access_control_3':
                answer = 'Monthly'
            else:
                answer = 'yes'  # Default to yes for other questions
            
            assessment_manager.answer_question(
                assessment1['id'],
                section['id'],
                question['id'],
                answer,
                'Verified during assessment'
            )
    
    # Mark the assessment as completed
    assessment_manager.update_assessment(assessment1['id'], {'status': 'completed'})
    
    # Create and complete security assessment for direct vendor 2
    assessment2 = assessment_manager.create_assessment(
        direct_vendor2['id'],
        'security_assessment',
        {'name': 'Annual Security Assessment - Payment Processor'}
    )
    
    # Answer questions for the assessment
    for section in assessment2['sections']:
        for question in section['questions']:
            # Simulate good security practices but with some gaps
            if question['id'] in ['security_policy_1', 'security_policy_3',
                                'access_control_1', 'access_control_2',
                                'data_protection_1', 'data_protection_2']:
                answer = 'yes'
            elif question['id'] == 'security_policy_2':
                answer = 'no'  # Gap: policy not regularly reviewed
            elif question['id'] == 'access_control_3':
                answer = 'Quarterly'
            elif question['id'] == 'data_protection_3':
                answer = 'no'  # Gap: no data classification policy
            else:
                answer = 'yes'  # Default to yes for other questions
            
            assessment_manager.answer_question(
                assessment2['id'],
                section['id'],
                question['id'],
                answer,
                'Verified during assessment'
            )
    
    # Mark the assessment as completed
    assessment_manager.update_assessment(assessment2['id'], {'status': 'completed'})
    
    # Create and complete security assessment for direct vendor 3
    assessment3 = assessment_manager.create_assessment(
        direct_vendor3['id'],
        'security_assessment',
        {'name': 'Annual Security Assessment - Analytics Partner'}
    )
    
    # Answer questions for the assessment
    for section in assessment3['sections']:
        for question in section['questions']:
            # Simulate mediocre security practices
            if question['id'] in ['security_policy_1', 'access_control_2',
                                'data_protection_2']:
                answer = 'yes'
            elif question['id'] in ['security_policy_2', 'security_policy_3',
                                  'access_control_1', 'data_protection_1',
                                  'data_protection_3']:
                answer = 'no'  # Several gaps
            elif question['id'] == 'access_control_3':
                answer = 'Annually'
            else:
                answer = 'no'  # Default to no for other questions
            
            assessment_manager.answer_question(
                assessment3['id'],
                section['id'],
                question['id'],
                answer,
                'Verified during assessment'
            )
    
    # Mark the assessment as completed
    assessment_manager.update_assessment(assessment3['id'], {'status': 'completed'})
    
    # Step 4: Create risks
    logger.info("Step 4: Creating risks")
    
    # Create risks for direct vendor 1
    risk1_1 = risk_manager.create_risk({
        'vendor_id': direct_vendor1['id'],
        'category_id': 'security',
        'name': 'Cloud infrastructure access control risk',
        'description': 'Risk of unauthorized access to cloud infrastructure',
        'impact': 'high',
        'likelihood': 'low',
        'status': 'mitigated'
    })
    
    # Add control to the risk
    risk_manager.add_control(risk1_1['id'], {
        'name': 'Multi-factor authentication',
        'description': 'MFA implemented for all cloud infrastructure access',
        'type': 'preventive',
        'status': 'implemented',
        'effectiveness': 'high'
    })
    
    # Create risks for direct vendor 2
    risk2_1 = risk_manager.create_risk({
        'vendor_id': direct_vendor2['id'],
        'category_id': 'compliance',
        'name': 'PCI DSS compliance risk',
        'description': 'Risk of non-compliance with PCI DSS requirements',
        'impact': 'high',
        'likelihood': 'medium',
        'status': 'mitigated'
    })
    
    # Add control to the risk
    risk_manager.add_control(risk2_1['id'], {
        'name': 'Annual PCI audit',
        'description': 'Annual third-party PCI DSS audit',
        'type': 'detective',
        'status': 'implemented',
        'effectiveness': 'medium'
    })
    
    risk2_2 = risk_manager.create_risk({
        'vendor_id': direct_vendor2['id'],
        'category_id': 'security',
        'name': 'Data breach risk',
        'description': 'Risk of payment data breach',
        'impact': 'high',
        'likelihood': 'medium',
        'status': 'mitigated'
    })
    
    # Add control to the risk
    risk_manager.add_control(risk2_2['id'], {
        'name': 'End-to-end encryption',
        'description': 'End-to-end encryption for all payment data',
        'type': 'preventive',
        'status': 'implemented',
        'effectiveness': 'high'
    })
    
    # Create risks for direct vendor 3
    risk3_1 = risk_manager.create_risk({
        'vendor_id': direct_vendor3['id'],
        'category_id': 'security',
        'name': 'Inadequate access controls',
        'description': 'Risk due to inadequate access controls',
        'impact': 'medium',
        'likelihood': 'high',
        'status': 'identified'
    })
    
    risk3_2 = risk_manager.create_risk({
        'vendor_id': direct_vendor3['id'],
        'category_id': 'privacy',
        'name': 'Data handling risk',
        'description': 'Risk of improper handling of personal data',
        'impact': 'medium',
        'likelihood': 'high',
        'status': 'identified'
    })
    
    risk3_3 = risk_manager.create_risk({
        'vendor_id': direct_vendor3['id'],
        'category_id': 'operational',
        'name': 'Service availability risk',
        'description': 'Risk of service unavailability',
        'impact': 'low',
        'likelihood': 'medium',
        'status': 'accepted'
    })
    
    # Create risks for fourth-party vendors
    risk4_1 = risk_manager.create_risk({
        'vendor_id': fourth_party3['id'],
        'category_id': 'financial',
        'name': 'Financial stability risk',
        'description': 'Risk due to startup financial instability',
        'impact': 'medium',
        'likelihood': 'high',
        'status': 'identified'
    })
    
    risk4_2 = risk_manager.create_risk({
        'vendor_id': fourth_party4['id'],
        'category_id': 'security',
        'name': 'Database security risk',
        'description': 'Risk of database security vulnerabilities',
        'impact': 'high',
        'likelihood': 'medium',
        'status': 'mitigated'
    })
    
    # Add control to the risk
    risk_manager.add_control(risk4_2['id'], {
        'name': 'Database encryption',
        'description': 'Encryption of sensitive data in the database',
        'type': 'preventive',
        'status': 'implemented',
        'effectiveness': 'medium'
    })
    
    # Step 5: Set up monitoring
    logger.info("Step 5: Setting up monitoring")
    
    # Set up security monitoring for direct vendor 1
    monitoring1 = monitoring_manager.create_monitoring_config({
        'vendor_id': direct_vendor1['id'],
        'name': 'Cloud Provider Security Monitoring',
        'description': 'Continuous monitoring of cloud provider security',
        'handler_id': 'security_monitoring',
        'parameters': {
            'check_ssl': True,
            'check_vulnerabilities': True
        },
        'frequency': 'daily',
        'alert_threshold': 'medium'
    })
    
    # Set up compliance monitoring for direct vendor 2
    monitoring2 = monitoring_manager.create_monitoring_config({
        'vendor_id': direct_vendor2['id'],
        'name': 'Payment Processor Compliance Monitoring',
        'description': 'Continuous monitoring of payment processor compliance',
        'handler_id': 'compliance_monitoring',
        'parameters': {
            'check_certifications': True,
            'check_regulatory_changes': True
        },
        'frequency': 'weekly',
        'alert_threshold': 'low'
    })
    
    # Set up operational monitoring for direct vendor 3
    monitoring3 = monitoring_manager.create_monitoring_config({
        'vendor_id': direct_vendor3['id'],
        'name': 'Analytics Partner Operational Monitoring',
        'description': 'Continuous monitoring of analytics partner operations',
        'handler_id': 'operational_monitoring',
        'parameters': {
            'check_availability': True,
            'check_performance': True
        },
        'frequency': 'daily',
        'alert_threshold': 'medium'
    })
    
    # Step 6: Calculate risk scores using advanced methodologies
    logger.info("Step 6: Calculating risk scores using advanced methodologies")
    
    # Get all vendors
    all_vendors = vendor_manager.get_all_vendors()
    
    # Get default risk factors and weights
    inherent_risk_factors = get_default_inherent_risk_factors()
    composite_weights = get_default_composite_weights()
    industry_benchmarks = get_default_industry_benchmarks()
    compliance_requirements = get_default_compliance_requirements()
    
    # Calculate risk scores for all vendors
    risk_scores = {}
    
    for vendor in all_vendors:
        vendor_id = vendor['id']
        logger.info(f"Calculating risk scores for vendor: {vendor['name']}")
        
        # Get vendor assessments
        assessments = assessment_manager.get_vendor_assessments(vendor_id)
        
        # Get vendor risks
        risks = risk_manager.get_vendor_risks(vendor_id)
        
        # Calculate inherent risk score
        inherent_score = calculate_inherent_risk_score(vendor, inherent_risk_factors)
        
        # Calculate residual risk score
        controls = []
        for risk in risks:
            controls.extend(risk.get('controls', []))
        
        residual_score = calculate_residual_risk_score(inherent_score, controls)
        
        # Calculate impact-likelihood score
        impact_likelihood_score = calculate_impact_likelihood_score(risks)
        
        # Calculate compliance-based score
        compliance_score = calculate_compliance_based_score(
            vendor, assessments, compliance_requirements)
        
        # Calculate industry-specific score
        industry_score = calculate_industry_specific_score(
            vendor, industry_benchmarks)
        
        # Calculate composite risk score
        scores = [
            inherent_score,
            residual_score,
            impact_likelihood_score,
            compliance_score,
            industry_score
        ]
        
        composite_score = calculate_composite_risk_score(scores, composite_weights)
        
        # Store all scores
        risk_scores[vendor_id] = {
            'vendor_name': vendor['name'],
            'inherent_score': inherent_score,
            'residual_score': residual_score,
            'impact_likelihood_score': impact_likelihood_score,
            'compliance_score': compliance_score,
            'industry_score': industry_score,
            'composite_score': composite_score,
            'overall_score': composite_score['overall_score'],
            'risk_level': composite_score['risk_level']
        }
        
        # Save the risk scores to a file
        risk_score_path = os.path.join(output_dir, f"{vendor_id}_risk_scores.json")
        with open(risk_score_path, 'w', encoding='utf-8') as f:
            json.dump(risk_scores[vendor_id], f, indent=2)
        
        logger.info(f"Saved risk scores for {vendor['name']} to {risk_score_path}")
    
    # Step 7: Calculate inherited risk from vendor relationships
    logger.info("Step 7: Calculating inherited risk from vendor relationships")
    
    # Calculate inherited risk for the primary vendor
    inherited_risk = relationship_manager.calculate_inherited_risk(
        primary_vendor['id'], {v_id: scores for v_id, scores in risk_scores.items()})
    
    # Save the inherited risk to a file
    inherited_risk_path = os.path.join(output_dir, f"{primary_vendor['id']}_inherited_risk.json")
    with open(inherited_risk_path, 'w', encoding='utf-8') as f:
        json.dump(inherited_risk, f, indent=2)
    
    logger.info(f"Saved inherited risk for {primary_vendor['name']} to {inherited_risk_path}")
    
    # Step 8: Generate vendor dependency graph
    logger.info("Step 8: Generating vendor dependency graph")
    
    # Generate the dependency graph
    dependency_graph = relationship_manager.get_vendor_dependency_graph(
        primary_vendor['id'], max_depth=3)
    
    # Add vendor names to the graph for better readability
    for node in dependency_graph['nodes']:
        vendor_id = node['id']
        for vendor in all_vendors:
            if vendor['id'] == vendor_id:
                node['name'] = vendor['name']
                node['risk_level'] = risk_scores[vendor_id]['risk_level']
                break
    
    # Save the dependency graph to a file
    graph_path = os.path.join(output_dir, f"{primary_vendor['id']}_dependency_graph.json")
    with open(graph_path, 'w', encoding='utf-8') as f:
        json.dump(dependency_graph, f, indent=2)
    
    logger.info(f"Saved vendor dependency graph to {graph_path}")
    
    # Step 9: Get fourth-party vendors
    logger.info("Step 9: Getting fourth-party vendors")
    
    # Get fourth-party vendors with high risk
    fourth_parties = relationship_manager.get_fourth_parties(
        primary_vendor['id'], risk_threshold='high')
    
    # Add vendor names and risk scores for better readability
    for fourth_party in fourth_parties:
        vendor_id = fourth_party['id']
        for vendor in all_vendors:
            if vendor['id'] == vendor_id:
                fourth_party['name'] = vendor['name']
                if vendor_id in risk_scores:
                    fourth_party['risk_score'] = risk_scores[vendor_id]['overall_score']
                    fourth_party['risk_level'] = risk_scores[vendor_id]['risk_level']
                break
    
    # Save the fourth-party vendors to a file
    fourth_party_path = os.path.join(output_dir, f"{primary_vendor['id']}_fourth_parties.json")
    with open(fourth_party_path, 'w', encoding='utf-8') as f:
        json.dump(fourth_parties, f, indent=2)
    
    logger.info(f"Saved fourth-party vendors to {fourth_party_path}")
    
    # Step 10: Generate summary report
    logger.info("Step 10: Generating summary report")
    
    # Create summary report
    summary = {
        'organization': primary_vendor['name'],
        'total_vendors': len(all_vendors) - 1,  # Exclude primary vendor
        'direct_vendors': len(relationship_manager.get_vendor_relationships(
            primary_vendor['id'], direction='from')),
        'fourth_party_vendors': len(fourth_parties),
        'high_risk_vendors': sum(1 for v_id, score in risk_scores.items()
                               if score['risk_level'] == 'high' and v_id != primary_vendor['id']),
        'medium_risk_vendors': sum(1 for v_id, score in risk_scores.items()
                                 if score['risk_level'] == 'medium' and v_id != primary_vendor['id']),
        'low_risk_vendors': sum(1 for v_id, score in risk_scores.items()
                              if score['risk_level'] == 'low' and v_id != primary_vendor['id']),
        'inherited_risk_score': inherited_risk['overall_score'],
        'inherited_risk_level': inherited_risk['risk_level'],
        'direct_vendor_risks': [
            {
                'vendor_name': risk_scores[v['id']]['vendor_name'],
                'risk_level': risk_scores[v['id']]['risk_level'],
                'risk_score': risk_scores[v['id']]['overall_score']
            }
            for v in [direct_vendor1, direct_vendor2, direct_vendor3]
        ],
        'high_risk_fourth_parties': [
            {
                'vendor_name': fp['name'],
                'risk_level': fp.get('risk_level', 'unknown'),
                'path': [
                    vendor['name'] for vendor in all_vendors
                    if vendor['id'] in fp['path']
                ]
            }
            for fp in fourth_parties if fp.get('risk_level') == 'high'
        ]
    }
    
    # Save the summary report to a file
    summary_path = os.path.join(output_dir, 'summary_report.json')
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)
    
    logger.info(f"Saved summary report to {summary_path}")
    
    logger.info("Advanced UVRMS demo completed successfully")
    logger.info(f"All output files are in: {output_dir}")

if __name__ == '__main__':
    main()
