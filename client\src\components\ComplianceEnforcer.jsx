import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';

/**
 * ComplianceEnforcer Component
 * 
 * This component enforces compliance rules on schemas based on the framework
 * and user permissions.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.schema - The original schema
 * @param {Object} props.user - Current user information
 * @param {Function} props.onSchemaChange - Callback when schema is modified
 */
const ComplianceEnforcer = ({ schema, user, onSchemaChange }) => {
  const [enforcedSchema, setEnforcedSchema] = useState(schema);
  
  useEffect(() => {
    if (!schema) return;
    
    // Create a deep copy of the schema
    const modifiedSchema = JSON.parse(JSON.stringify(schema));
    
    // Apply compliance rules based on framework
    if (schema.compliance_mode) {
      switch (schema.compliance_mode) {
        case 'HIPAA':
          // HIPAA compliance rules
          if (user?.role !== 'PrivacyOfficer' && user?.role !== 'Admin') {
            // Filter out PHI export fields
            modifiedSchema.fields = modifiedSchema.fields.filter(f => 
              f.type !== 'phi_export' && 
              !f.tags?.includes('phi')
            );
            
            // Add warnings to PHI fields
            modifiedSchema.fields.forEach(field => {
              if (field.tags?.includes('phi_view')) {
                field.description = `${field.description || ''} (PHI data - handle according to HIPAA regulations)`;
                field.className = `${field.className || ''} hipaa-field`;
              }
            });
          }
          break;
          
        case 'SOC2':
          // SOC2 compliance rules
          if (user?.role !== 'Auditor' && user?.role !== 'Admin') {
            // Filter out evidence modification fields for non-auditors
            modifiedSchema.fields = modifiedSchema.fields.filter(f => 
              !f.tags?.includes('evidence_modify')
            );
            
            // Make evidence fields read-only
            modifiedSchema.fields.forEach(field => {
              if (field.tags?.includes('evidence_view')) {
                field.readOnly = true;
                field.description = `${field.description || ''} (Evidence data - read only)`;
              }
            });
          }
          
          // Add blockchain verification for evidence fields
          modifiedSchema.fields.forEach(field => {
            if (field.tags?.includes('evidence')) {
              field.verification = 'blockchain';
            }
          });
          break;
          
        case 'GDPR':
          // GDPR compliance rules
          if (user?.role !== 'DataProtectionOfficer' && user?.role !== 'Admin') {
            // Filter out PII export fields
            modifiedSchema.fields = modifiedSchema.fields.filter(f => 
              !f.tags?.includes('pii_export')
            );
            
            // Add consent requirements to PII fields
            modifiedSchema.fields.forEach(field => {
              if (field.tags?.includes('pii')) {
                field.description = `${field.description || ''} (Personal data - subject to GDPR)`;
                field.consent_required = true;
              }
            });
          }
          
          // Add data retention information
          if (!modifiedSchema.footer) {
            modifiedSchema.footer = '';
          }
          modifiedSchema.footer += 'Data is retained according to our GDPR-compliant data retention policy.';
          break;
          
        case 'PCI-DSS':
          // PCI-DSS compliance rules
          // Mask credit card fields
          modifiedSchema.fields.forEach(field => {
            if (field.tags?.includes('pci') || field.type === 'credit_card') {
              field.masking = true;
              field.description = `${field.description || ''} (Card data - subject to PCI-DSS)`;
            }
          });
          break;
          
        default:
          // No specific compliance mode
          break;
      }
    }
    
    // Apply role-based restrictions
    if (user?.role) {
      // Get role permissions from compliance rules
      const rolePermissions = getRolePermissions(user.role, schema.compliance_mode);
      
      if (rolePermissions) {
        // Filter fields based on permissions
        if (rolePermissions.forbidden_fields) {
          modifiedSchema.fields = modifiedSchema.fields.filter(field => 
            !rolePermissions.forbidden_fields.includes(field.name)
          );
        }
        
        // Add required fields
        if (rolePermissions.required_fields) {
          rolePermissions.required_fields.forEach(requiredField => {
            if (!modifiedSchema.fields.some(f => f.name === requiredField.name)) {
              modifiedSchema.fields.push(requiredField);
            }
          });
        }
        
        // Modify submit behavior if needed
        if (rolePermissions.submit_behavior) {
          modifiedSchema.submitBehavior = rolePermissions.submit_behavior;
        }
      }
    }
    
    // Apply universal compliance components
    if (schema.compliance_mode) {
      // Add compliance footer if not present
      if (!modifiedSchema.complianceFooter) {
        modifiedSchema.complianceFooter = `This form complies with ${schema.compliance_mode} requirements.`;
      }
      
      // Add audit trail
      modifiedSchema.auditTrail = true;
    }
    
    // Update the enforced schema
    setEnforcedSchema(modifiedSchema);
    
    // Notify parent component
    if (onSchemaChange) {
      onSchemaChange(modifiedSchema);
    }
  }, [schema, user, onSchemaChange]);
  
  // Helper function to get role permissions
  const getRolePermissions = (role, complianceMode) => {
    // This would typically come from a configuration file or API
    // For now, we'll hardcode some examples
    const rolePermissionsMap = {
      'HIPAA': {
        'PrivacyOfficer': {
          forbidden_fields: [],
          required_fields: [
            {
              name: 'hipaa_certification',
              label: 'HIPAA Certification',
              type: 'checkbox',
              required: true,
              description: 'I certify that this action complies with HIPAA regulations'
            }
          ],
          submit_behavior: 'log_and_verify'
        },
        'Clinician': {
          forbidden_fields: ['patient_ssn', 'full_medical_history'],
          required_fields: [
            {
              name: 'access_reason',
              label: 'Reason for Access',
              type: 'text',
              required: true,
              description: 'Provide clinical justification for accessing this information'
            }
          ],
          submit_behavior: 'log_and_notify'
        }
      },
      'SOC2': {
        'Auditor': {
          forbidden_fields: [],
          required_fields: [
            {
              name: 'audit_purpose',
              label: 'Audit Purpose',
              type: 'text',
              required: true,
              description: 'Specify the purpose of this audit action'
            }
          ],
          submit_behavior: 'blockchain_verify'
        }
      }
    };
    
    return rolePermissionsMap[complianceMode]?.[role];
  };
  
  // This component doesn't render anything itself
  return null;
};

ComplianceEnforcer.propTypes = {
  schema: PropTypes.object.isRequired,
  user: PropTypes.object,
  onSchemaChange: PropTypes.func.isRequired
};

export default ComplianceEnforcer;
