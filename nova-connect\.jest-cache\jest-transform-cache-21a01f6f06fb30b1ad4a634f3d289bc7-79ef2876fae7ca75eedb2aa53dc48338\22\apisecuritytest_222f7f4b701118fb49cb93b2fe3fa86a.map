{"version": 3, "names": ["expect", "require", "supertest", "mongoose", "MongoMemoryServer", "jwt", "app", "describe", "mongoServer", "request", "validApi<PERSON>ey", "validJwtToken", "before", "create", "mongo<PERSON>ri", "get<PERSON><PERSON>", "connect", "useNewUrlParser", "useUnifiedTopology", "jwtSecret", "process", "env", "JWT_SECRET", "sign", "userId", "role", "expiresIn", "after", "disconnect", "stop", "it", "response", "get", "status", "to", "equal", "set", "expiredToken", "adminToken", "userToken", "requests", "i", "push", "responses", "Promise", "all", "rateLimitedResponses", "filter", "r", "length", "be", "greaterThan", "invalidConnector", "type", "post", "send", "body", "have", "property", "xssConnector", "name", "description", "not", "include", "sqlInjection<PERSON>uery", "headers"], "sources": ["api.security.test.js"], "sourcesContent": ["/**\n * Security tests for the NovaConnect API\n */\n\nconst { expect } = require('chai');\nconst supertest = require('supertest');\nconst mongoose = require('mongoose');\nconst { MongoMemoryServer } = require('mongodb-memory-server');\nconst jwt = require('jsonwebtoken');\n\n// Import the app\nconst app = require('../../server');\n\ndescribe('NovaConnect API Security Tests', () => {\n  let mongoServer;\n  let request;\n  let validApiKey;\n  let validJwtToken;\n\n  before(async () => {\n    // Create an in-memory MongoDB server\n    mongoServer = await MongoMemoryServer.create();\n    const mongoUri = mongoServer.getUri();\n\n    // Connect to the in-memory database\n    await mongoose.connect(mongoUri, {\n      useNewUrlParser: true,\n      useUnifiedTopology: true\n    });\n\n    // Create a supertest request object\n    request = supertest(app);\n\n    // Create a valid API key for testing\n    validApiKey = 'test-api-key-12345';\n\n    // Create a valid JWT token for testing\n    const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';\n    validJwtToken = jwt.sign(\n      { \n        userId: 'test-user-id',\n        role: 'admin'\n      },\n      jwtSecret,\n      { expiresIn: '1h' }\n    );\n  });\n\n  after(async () => {\n    // Disconnect from the database\n    await mongoose.disconnect();\n    // Stop the in-memory MongoDB server\n    await mongoServer.stop();\n  });\n\n  describe('Authentication', () => {\n    it('should reject requests without authentication', async () => {\n      const response = await request.get('/api/connectors');\n      expect(response.status).to.equal(401);\n    });\n\n    it('should accept requests with valid API key', async () => {\n      const response = await request\n        .get('/api/connectors')\n        .set('X-API-Key', validApiKey);\n      \n      expect(response.status).to.equal(200);\n    });\n\n    it('should reject requests with invalid API key', async () => {\n      const response = await request\n        .get('/api/connectors')\n        .set('X-API-Key', 'invalid-api-key');\n      \n      expect(response.status).to.equal(401);\n    });\n\n    it('should accept requests with valid JWT token', async () => {\n      const response = await request\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${validJwtToken}`);\n      \n      expect(response.status).to.equal(200);\n    });\n\n    it('should reject requests with invalid JWT token', async () => {\n      const response = await request\n        .get('/api/connectors')\n        .set('Authorization', 'Bearer invalid-token');\n      \n      expect(response.status).to.equal(401);\n    });\n\n    it('should reject requests with expired JWT token', async () => {\n      // Create an expired JWT token\n      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';\n      const expiredToken = jwt.sign(\n        { \n          userId: 'test-user-id',\n          role: 'admin'\n        },\n        jwtSecret,\n        { expiresIn: '-1h' } // Expired 1 hour ago\n      );\n\n      const response = await request\n        .get('/api/connectors')\n        .set('Authorization', `Bearer ${expiredToken}`);\n      \n      expect(response.status).to.equal(401);\n    });\n  });\n\n  describe('Authorization', () => {\n    it('should allow admin users to access admin endpoints', async () => {\n      // Create an admin JWT token\n      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';\n      const adminToken = jwt.sign(\n        { \n          userId: 'admin-user-id',\n          role: 'admin'\n        },\n        jwtSecret,\n        { expiresIn: '1h' }\n      );\n\n      const response = await request\n        .get('/api/admin/users')\n        .set('Authorization', `Bearer ${adminToken}`);\n      \n      expect(response.status).to.equal(200);\n    });\n\n    it('should deny regular users access to admin endpoints', async () => {\n      // Create a regular user JWT token\n      const jwtSecret = process.env.JWT_SECRET || 'test-jwt-secret';\n      const userToken = jwt.sign(\n        { \n          userId: 'regular-user-id',\n          role: 'user'\n        },\n        jwtSecret,\n        { expiresIn: '1h' }\n      );\n\n      const response = await request\n        .get('/api/admin/users')\n        .set('Authorization', `Bearer ${userToken}`);\n      \n      expect(response.status).to.equal(403);\n    });\n  });\n\n  describe('Rate Limiting', () => {\n    it('should rate limit excessive requests', async () => {\n      // Make multiple requests in quick succession\n      const requests = [];\n      for (let i = 0; i < 110; i++) {\n        requests.push(\n          request\n            .get('/api/connectors')\n            .set('X-API-Key', validApiKey)\n        );\n      }\n\n      // Wait for all requests to complete\n      const responses = await Promise.all(requests);\n      \n      // At least one request should be rate limited\n      const rateLimitedResponses = responses.filter(r => r.status === 429);\n      expect(rateLimitedResponses.length).to.be.greaterThan(0);\n    });\n  });\n\n  describe('Input Validation', () => {\n    it('should reject invalid input', async () => {\n      const invalidConnector = {\n        // Missing required fields\n        type: 'http'\n      };\n\n      const response = await request\n        .post('/api/connectors')\n        .set('X-API-Key', validApiKey)\n        .send(invalidConnector);\n      \n      expect(response.status).to.equal(400);\n      expect(response.body).to.have.property('error');\n    });\n\n    it('should sanitize input to prevent XSS', async () => {\n      const xssConnector = {\n        name: '<script>alert(\"XSS\")</script>',\n        type: 'http',\n        description: 'Test connector with XSS payload'\n      };\n\n      const response = await request\n        .post('/api/connectors')\n        .set('X-API-Key', validApiKey)\n        .send(xssConnector);\n      \n      expect(response.status).to.equal(201);\n      expect(response.body.name).to.not.include('<script>');\n    });\n\n    it('should prevent SQL injection', async () => {\n      const sqlInjectionQuery = \"'; DROP TABLE users; --\";\n      \n      const response = await request\n        .get(`/api/connectors?query=${sqlInjectionQuery}`)\n        .set('X-API-Key', validApiKey);\n      \n      expect(response.status).to.equal(200);\n      // The query should be sanitized and not cause any errors\n    });\n  });\n\n  describe('Security Headers', () => {\n    it('should include security headers in responses', async () => {\n      const response = await request\n        .get('/health');\n      \n      expect(response.headers).to.have.property('x-content-type-options');\n      expect(response.headers['x-content-type-options']).to.equal('nosniff');\n      \n      expect(response.headers).to.have.property('x-frame-options');\n      expect(response.headers['x-frame-options']).to.equal('DENY');\n      \n      expect(response.headers).to.have.property('content-security-policy');\n      \n      expect(response.headers).to.have.property('strict-transport-security');\n    });\n  });\n});\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAO,CAAC,GAAGC,OAAO,CAAC,MAAM,CAAC;AAClC,MAAMC,SAAS,GAAGD,OAAO,CAAC,WAAW,CAAC;AACtC,MAAME,QAAQ,GAAGF,OAAO,CAAC,UAAU,CAAC;AACpC,MAAM;EAAEG;AAAkB,CAAC,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAC9D,MAAMI,GAAG,GAAGJ,OAAO,CAAC,cAAc,CAAC;;AAEnC;AACA,MAAMK,GAAG,GAAGL,OAAO,CAAC,cAAc,CAAC;AAEnCM,QAAQ,CAAC,gCAAgC,EAAE,MAAM;EAC/C,IAAIC,WAAW;EACf,IAAIC,OAAO;EACX,IAAIC,WAAW;EACf,IAAIC,aAAa;EAEjBC,MAAM,CAAC,YAAY;IACjB;IACAJ,WAAW,GAAG,MAAMJ,iBAAiB,CAACS,MAAM,CAAC,CAAC;IAC9C,MAAMC,QAAQ,GAAGN,WAAW,CAACO,MAAM,CAAC,CAAC;;IAErC;IACA,MAAMZ,QAAQ,CAACa,OAAO,CAACF,QAAQ,EAAE;MAC/BG,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;;IAEF;IACAT,OAAO,GAAGP,SAAS,CAACI,GAAG,CAAC;;IAExB;IACAI,WAAW,GAAG,oBAAoB;;IAElC;IACA,MAAMS,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,iBAAiB;IAC7DX,aAAa,GAAGN,GAAG,CAACkB,IAAI,CACtB;MACEC,MAAM,EAAE,cAAc;MACtBC,IAAI,EAAE;IACR,CAAC,EACDN,SAAS,EACT;MAAEO,SAAS,EAAE;IAAK,CACpB,CAAC;EACH,CAAC,CAAC;EAEFC,KAAK,CAAC,YAAY;IAChB;IACA,MAAMxB,QAAQ,CAACyB,UAAU,CAAC,CAAC;IAC3B;IACA,MAAMpB,WAAW,CAACqB,IAAI,CAAC,CAAC;EAC1B,CAAC,CAAC;EAEFtB,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IAC/BuB,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAACuB,GAAG,CAAC,iBAAiB,CAAC;MACrDhC,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;IAEFL,EAAE,CAAC,2CAA2C,EAAE,YAAY;MAC1D,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,iBAAiB,CAAC,CACtBI,GAAG,CAAC,WAAW,EAAE1B,WAAW,CAAC;MAEhCV,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;IAEFL,EAAE,CAAC,6CAA6C,EAAE,YAAY;MAC5D,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,iBAAiB,CAAC,CACtBI,GAAG,CAAC,WAAW,EAAE,iBAAiB,CAAC;MAEtCpC,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;IAEFL,EAAE,CAAC,6CAA6C,EAAE,YAAY;MAC5D,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,iBAAiB,CAAC,CACtBI,GAAG,CAAC,eAAe,EAAE,UAAUzB,aAAa,EAAE,CAAC;MAElDX,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;IAEFL,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,iBAAiB,CAAC,CACtBI,GAAG,CAAC,eAAe,EAAE,sBAAsB,CAAC;MAE/CpC,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;IAEFL,EAAE,CAAC,+CAA+C,EAAE,YAAY;MAC9D;MACA,MAAMX,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,iBAAiB;MAC7D,MAAMe,YAAY,GAAGhC,GAAG,CAACkB,IAAI,CAC3B;QACEC,MAAM,EAAE,cAAc;QACtBC,IAAI,EAAE;MACR,CAAC,EACDN,SAAS,EACT;QAAEO,SAAS,EAAE;MAAM,CAAC,CAAC;MACvB,CAAC;MAED,MAAMK,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,iBAAiB,CAAC,CACtBI,GAAG,CAAC,eAAe,EAAE,UAAUC,YAAY,EAAE,CAAC;MAEjDrC,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5B,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BuB,EAAE,CAAC,oDAAoD,EAAE,YAAY;MACnE;MACA,MAAMX,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,iBAAiB;MAC7D,MAAMgB,UAAU,GAAGjC,GAAG,CAACkB,IAAI,CACzB;QACEC,MAAM,EAAE,eAAe;QACvBC,IAAI,EAAE;MACR,CAAC,EACDN,SAAS,EACT;QAAEO,SAAS,EAAE;MAAK,CACpB,CAAC;MAED,MAAMK,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,kBAAkB,CAAC,CACvBI,GAAG,CAAC,eAAe,EAAE,UAAUE,UAAU,EAAE,CAAC;MAE/CtC,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;IAEFL,EAAE,CAAC,qDAAqD,EAAE,YAAY;MACpE;MACA,MAAMX,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,iBAAiB;MAC7D,MAAMiB,SAAS,GAAGlC,GAAG,CAACkB,IAAI,CACxB;QACEC,MAAM,EAAE,iBAAiB;QACzBC,IAAI,EAAE;MACR,CAAC,EACDN,SAAS,EACT;QAAEO,SAAS,EAAE;MAAK,CACpB,CAAC;MAED,MAAMK,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,kBAAkB,CAAC,CACvBI,GAAG,CAAC,eAAe,EAAE,UAAUG,SAAS,EAAE,CAAC;MAE9CvC,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5B,QAAQ,CAAC,eAAe,EAAE,MAAM;IAC9BuB,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrD;MACA,MAAMU,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;QAC5BD,QAAQ,CAACE,IAAI,CACXjC,OAAO,CACJuB,GAAG,CAAC,iBAAiB,CAAC,CACtBI,GAAG,CAAC,WAAW,EAAE1B,WAAW,CACjC,CAAC;MACH;;MAEA;MACA,MAAMiC,SAAS,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;;MAE7C;MACA,MAAMM,oBAAoB,GAAGH,SAAS,CAACI,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,MAAM,KAAK,GAAG,CAAC;MACpEjC,MAAM,CAAC8C,oBAAoB,CAACG,MAAM,CAAC,CAACf,EAAE,CAACgB,EAAE,CAACC,WAAW,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5C,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCuB,EAAE,CAAC,6BAA6B,EAAE,YAAY;MAC5C,MAAMsB,gBAAgB,GAAG;QACvB;QACAC,IAAI,EAAE;MACR,CAAC;MAED,MAAMtB,QAAQ,GAAG,MAAMtB,OAAO,CAC3B6C,IAAI,CAAC,iBAAiB,CAAC,CACvBlB,GAAG,CAAC,WAAW,EAAE1B,WAAW,CAAC,CAC7B6C,IAAI,CAACH,gBAAgB,CAAC;MAEzBpD,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAAC+B,QAAQ,CAACyB,IAAI,CAAC,CAACtB,EAAE,CAACuB,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC;IACjD,CAAC,CAAC;IAEF5B,EAAE,CAAC,sCAAsC,EAAE,YAAY;MACrD,MAAM6B,YAAY,GAAG;QACnBC,IAAI,EAAE,+BAA+B;QACrCP,IAAI,EAAE,MAAM;QACZQ,WAAW,EAAE;MACf,CAAC;MAED,MAAM9B,QAAQ,GAAG,MAAMtB,OAAO,CAC3B6C,IAAI,CAAC,iBAAiB,CAAC,CACvBlB,GAAG,CAAC,WAAW,EAAE1B,WAAW,CAAC,CAC7B6C,IAAI,CAACI,YAAY,CAAC;MAErB3D,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;MACrCnC,MAAM,CAAC+B,QAAQ,CAACyB,IAAI,CAACI,IAAI,CAAC,CAAC1B,EAAE,CAAC4B,GAAG,CAACC,OAAO,CAAC,UAAU,CAAC;IACvD,CAAC,CAAC;IAEFjC,EAAE,CAAC,8BAA8B,EAAE,YAAY;MAC7C,MAAMkC,iBAAiB,GAAG,yBAAyB;MAEnD,MAAMjC,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,yBAAyBgC,iBAAiB,EAAE,CAAC,CACjD5B,GAAG,CAAC,WAAW,EAAE1B,WAAW,CAAC;MAEhCV,MAAM,CAAC+B,QAAQ,CAACE,MAAM,CAAC,CAACC,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;MACrC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF5B,QAAQ,CAAC,kBAAkB,EAAE,MAAM;IACjCuB,EAAE,CAAC,8CAA8C,EAAE,YAAY;MAC7D,MAAMC,QAAQ,GAAG,MAAMtB,OAAO,CAC3BuB,GAAG,CAAC,SAAS,CAAC;MAEjBhC,MAAM,CAAC+B,QAAQ,CAACkC,OAAO,CAAC,CAAC/B,EAAE,CAACuB,IAAI,CAACC,QAAQ,CAAC,wBAAwB,CAAC;MACnE1D,MAAM,CAAC+B,QAAQ,CAACkC,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC/B,EAAE,CAACC,KAAK,CAAC,SAAS,CAAC;MAEtEnC,MAAM,CAAC+B,QAAQ,CAACkC,OAAO,CAAC,CAAC/B,EAAE,CAACuB,IAAI,CAACC,QAAQ,CAAC,iBAAiB,CAAC;MAC5D1D,MAAM,CAAC+B,QAAQ,CAACkC,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC/B,EAAE,CAACC,KAAK,CAAC,MAAM,CAAC;MAE5DnC,MAAM,CAAC+B,QAAQ,CAACkC,OAAO,CAAC,CAAC/B,EAAE,CAACuB,IAAI,CAACC,QAAQ,CAAC,yBAAyB,CAAC;MAEpE1D,MAAM,CAAC+B,QAAQ,CAACkC,OAAO,CAAC,CAAC/B,EAAE,CAACuB,IAAI,CAACC,QAAQ,CAAC,2BAA2B,CAAC;IACxE,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}