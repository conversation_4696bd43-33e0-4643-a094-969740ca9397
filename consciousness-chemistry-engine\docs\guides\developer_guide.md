# ConsciousNovaFold Developer Guide

## Table of Contents
- [Project Structure](#project-structure)
- [Code Organization](#code-organization)
- [Development Setup](#development-setup)
- [Testing](#testing)
- [Contribution Guidelines](#contribution-guidelines)
- [Code Style](#code-style)
- [Release Process](#release-process)

## Project Structure

```
consciousness-chemistry-engine/
├── src/                    # Source code
│   ├── ConsciousNovaFold.py  # Main module
│   └── metrics/             # Consciousness metrics
│       ├── __init__.py
│       ├── fibonacci_bias.py
│       ├── psi_score.py
│       └── trinity_validator.py
├── tests/                  # Test suite
│   ├── __init__.py
│   ├── test_conscious_novafold.py
│   └── test_integration.py
├── examples/               # Example scripts
├── docs/                   # Documentation
└── requirements.txt        # Dependencies
```

## Code Organization

### Core Components

1. **ConsciousNovaFold** (`src/ConsciousNovaFold.py`)
   - Main class that orchestrates the folding pipeline
   - Handles sequence processing, structure prediction, and metric calculation
   - Manages report generation and output

2. **Metrics** (`src/metrics/`)
   - `fibonacci_bias.py`: Analyzes protein domains using Fibonacci sequence
   - `psi_score.py`: Calculates consciousness potential scores
   - `trinity_validator.py`: Validates structures using NERS, NEPI, and NEFC

## Development Setup

### Prerequisites
- Python 3.8+
- Git
- pip

### Setup Instructions

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/consciousness-chemistry-engine.git
   cd consciousness-chemistry-engine
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Dependencies**
   ```bash
   pip install -r requirements-dev.txt
   pip install -e .
   ```

4. **Run Tests**
   ```bash
   pytest -v
   ```

## Testing

### Running Tests
```bash
# Run all tests
pytest

# Run a specific test file
pytest tests/test_conscious_novafold.py

# Run with coverage
pytest --cov=src tests/
```

### Writing Tests
- Place new test files in the `tests/` directory
- Follow naming convention: `test_*.py`
- Use descriptive test function names: `test_<function>_<scenario>_<expected>`

Example test:
```python
def test_fold_sequence_valid_input_returns_structure():
    # Setup
    folder = ConsciousNovaFold()
    
    # Execute
    result = folder.fold("ACDEFGHIKLMNPQRSTVWY")
    
    # Assert
    assert 'structure' in result
    assert 'metrics' in result
```

## Contribution Guidelines

### Branching Strategy
- `main`: Production-ready code
- `develop`: Integration branch for features
- `feature/*`: New features
- `bugfix/*`: Bug fixes
- `docs/*`: Documentation updates

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Commit changes with descriptive messages
4. Push to your fork
5. Open a pull request to `develop`

### Commit Message Format
```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style/formatting
- `refactor`: Code changes that neither fix bugs nor add features
- `test`: Adding tests
- `chore`: Maintenance tasks

## Code Style

### Python Style Guide
Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) with these additions:
- Maximum line length: 88 characters (Black default)
- Use type hints for all function signatures
- Document all public functions with NumPy-style docstrings

### Linting and Formatting
```bash
# Auto-format code
black .


# Check for style issues
flake8

# Check type hints
mypy src/
```

## Release Process

### Versioning
Follow [Semantic Versioning](https://semver.org/): `MAJOR.MINOR.PATCH`

### Creating a Release
1. Update `__version__` in `src/__init__.py`
2. Update `CHANGELOG.md`
3. Create a release tag
   ```bash
   git tag -a v1.0.0 -m "Initial release"
   git push origin v1.0.0
   ```
4. Create a GitHub release with release notes

## Documentation

### Building Documentation
```bash
cd docs
make html  # Requires Sphinx
```

### Documentation Structure
- `guides/`: User and developer documentation
- `api/`: API reference (auto-generated)
- `examples/`: Example usage
- `scientific/`: Scientific background and methodology

## Getting Help
- Read the [FAQ](faq.md)
- Open an issue on GitHub
- Join our community chat

## License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
