#!/usr/bin/env python3
"""
UUFT Game Theory Analysis Runner

This script runs the complete UUFT game theory analysis pipeline:
1. Generates game theory scenarios with various structures
2. Simulates strategic interactions with different UUFT parameters
3. Analyzes the results for 18/82 patterns and π-related relationships
4. Produces a comprehensive report on the findings
"""

import os
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import json
from uuft_game_analyzer import UUFTGameScenario
from uuft_game_simulator import UUFTGameSimulator
from uuft_game_pattern_analyzer import UUFTGamePatternAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_game_run.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Game_Run')

# Constants
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

def generate_game_scenarios():
    """Generate a variety of game theory scenarios for analysis."""
    logger.info("Generating game theory scenarios")
    
    scenarios = []
    
    # Prisoner's Dilemma
    scenarios.append(("Prisoner's Dilemma", UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )))
    
    # Stag Hunt
    scenarios.append(("Stag Hunt", UUFTGameScenario(
        game_type="stag_hunt",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )))
    
    # Chicken Game
    scenarios.append(("Chicken Game", UUFTGameScenario(
        game_type="chicken",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )))
    
    # Public Goods Game
    scenarios.append(("Public Goods", UUFTGameScenario(
        game_type="public_goods",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )))
    
    # Prisoner's Dilemma with UUFT bias
    scenarios.append(("Prisoner's Dilemma with UUFT Bias", UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.7
    )))
    
    logger.info(f"Generated {len(scenarios)} game theory scenarios")
    
    # Visualize and save scenarios
    for name, scenario in scenarios:
        # Visualize payoff matrix
        scenario.visualize_payoff_matrix(
            save_path=os.path.join(RESULTS_DIR, f"{name.lower().replace(' ', '_')}_payoff.png")
        )
        
        # Find Nash equilibria
        equilibria = scenario.find_nash_equilibria()
        logger.info(f"{name} Nash equilibria: {equilibria}")
        
        # Calculate mixed equilibrium
        mixed_eq = scenario.calculate_mixed_equilibrium()
        logger.info(f"{name} mixed equilibrium: {mixed_eq}")
    
    return scenarios

def run_game_simulations(scenarios):
    """Run game theory simulations on the generated scenarios."""
    logger.info("Running game theory simulations")
    
    simulations = []
    
    # Define simulation parameters to test
    simulation_params = [
        ("Standard", {
            "pi_influence": 0.0,
            "pattern_1882_strength": 0.0,
            "temporal_stability": 0.5
        }),
        ("UUFT Optimized", {
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.7,
            "temporal_stability": 0.8
        }),
        ("High Pi Influence", {
            "pi_influence": 0.9,
            "pattern_1882_strength": 0.5,
            "temporal_stability": 0.8
        }),
        ("High 1882 Pattern", {
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.9,
            "temporal_stability": 0.8
        })
    ]
    
    # Run simulations for each scenario and parameter combination
    for scenario_name, scenario in scenarios:
        for param_name, uuft_parameters in simulation_params:
            # Skip some combinations to reduce computation
            if "with UUFT Bias" in scenario_name and param_name in ["High Pi Influence", "High 1882 Pattern"]:
                continue
                
            # Create simulation name
            sim_name = f"{scenario_name} - {param_name}"
            logger.info(f"Running simulation: {sim_name}")
            
            # Create simulator
            simulator = UUFTGameSimulator(
                game_scenario=scenario,
                num_agents=50,
                learning_rate=0.1,
                uuft_parameters=uuft_parameters
            )
            
            # Run simulation
            simulator.run_simulation(num_steps=50)
            
            # Visualize strategy evolution
            simulator.visualize_strategy_evolution(
                save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_evolution.png")
            )
            
            # Visualize payoff distribution
            simulator.visualize_payoff_distribution(
                save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_payoffs.png")
            )
            
            # Save simulation
            simulations.append((sim_name, simulator))
    
    logger.info(f"Completed {len(simulations)} game theory simulations")
    return simulations

def analyze_simulations(simulations):
    """Analyze the game theory simulations for UUFT patterns."""
    logger.info("Analyzing game theory simulations")
    
    results = []
    
    for sim_name, simulator in simulations:
        logger.info(f"Analyzing simulation: {sim_name}")
        
        # Create analyzer
        analyzer = UUFTGamePatternAnalyzer(
            game_simulator=simulator,
            pattern_threshold=0.1  # More lenient threshold for pattern detection
        )
        
        # Create comprehensive report
        report = analyzer.create_comprehensive_report(
            save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_report.json")
        )
        
        # Visualize 18/82 patterns
        analyzer.visualize_1882_patterns(
            save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_1882_patterns.png")
        )
        
        # Save result
        results.append((sim_name, report))
    
    logger.info(f"Completed analysis of {len(results)} simulations")
    return results

def create_comprehensive_report(results):
    """Create a comprehensive report of all simulation results."""
    logger.info("Creating comprehensive report")
    
    # Create summary statistics
    summary = {
        "total_simulations": len(results),
        "simulations_with_1882_pattern": sum(1 for _, r in results if r["overall_1882_pattern_score"] > 0.5),
        "simulations_with_pi_pattern": sum(1 for _, r in results if r["overall_pi_relationship_score"] > 0.5),
        "average_1882_score": np.mean([r["overall_1882_pattern_score"] for _, r in results]),
        "average_pi_score": np.mean([r["overall_pi_relationship_score"] for _, r in results]),
        "simulation_details": []
    }
    
    # Add details for each simulation
    for sim_name, report in results:
        sim_detail = {
            "name": sim_name,
            "game_type": report["game_type"],
            "uuft_bias": report["uuft_bias"],
            "overall_1882_pattern_score": report["overall_1882_pattern_score"],
            "overall_pi_relationship_score": report["overall_pi_relationship_score"]
        }
        
        summary["simulation_details"].append(sim_detail)
    
    # Save summary
    with open(os.path.join(RESULTS_DIR, "game_analysis_summary.json"), 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)
    
    # Create HTML report
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>UUFT Game Theory Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #2c3e50; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .high {{ color: green; }}
            .medium {{ color: orange; }}
            .low {{ color: red; }}
            .chart {{ margin-top: 30px; }}
        </style>
    </head>
    <body>
        <h1>UUFT Game Theory Analysis Report</h1>
        <p>Date: {datetime.now().strftime('%Y-%m-%d')}</p>
        
        <div class="summary">
            <h2>Summary</h2>
            <p>Total simulations analyzed: {summary['total_simulations']}</p>
            <p>Simulations with 18/82 pattern: {summary['simulations_with_1882_pattern']} ({summary['simulations_with_1882_pattern']/summary['total_simulations']*100:.1f}%)</p>
            <p>Simulations with π pattern: {summary['simulations_with_pi_pattern']} ({summary['simulations_with_pi_pattern']/summary['total_simulations']*100:.1f}%)</p>
            <p>Average 18/82 pattern score: {summary['average_1882_score']:.4f}</p>
            <p>Average π relationship score: {summary['average_pi_score']:.4f}</p>
        </div>
        
        <h2>Simulation Details</h2>
        <table>
            <tr>
                <th>Simulation</th>
                <th>Game Type</th>
                <th>UUFT Bias</th>
                <th>18/82 Score</th>
                <th>π Score</th>
            </tr>
    """
    
    # Add rows for each simulation
    for detail in summary["simulation_details"]:
        # Determine color classes based on scores
        pattern_1882_class = ""
        if detail["overall_1882_pattern_score"] > 0.7:
            pattern_1882_class = "high"
        elif detail["overall_1882_pattern_score"] > 0.4:
            pattern_1882_class = "medium"
        else:
            pattern_1882_class = "low"
            
        pi_class = ""
        if detail["overall_pi_relationship_score"] > 0.7:
            pi_class = "high"
        elif detail["overall_pi_relationship_score"] > 0.4:
            pi_class = "medium"
        else:
            pi_class = "low"
        
        # Add row
        html_report += f"""
            <tr>
                <td>{detail['name']}</td>
                <td>{detail['game_type']}</td>
                <td>{detail['uuft_bias']:.1f}</td>
                <td class="{pattern_1882_class}">{detail['overall_1882_pattern_score']:.4f}</td>
                <td class="{pi_class}">{detail['overall_pi_relationship_score']:.4f}</td>
            </tr>
        """
    
    # Add visualizations and conclusions
    html_report += """
        </table>
        
        <div class="chart">
            <h2>Visualizations</h2>
            <p>Game visualizations, strategy evolution, payoff distributions, and 18/82 pattern analyses are available in the results directory.</p>
        </div>
        
        <h2>Conclusions</h2>
        <p>This analysis demonstrates the presence of 18/82 patterns and π relationships in game theory scenarios.</p>
        <p>Key findings:</p>
        <ul>
    """
    
    # Add conclusions based on results
    if summary['average_1882_score'] > 0.6:
        html_report += "<li>Game theory scenarios exhibit significant 18/82 patterns, particularly in games with explicit UUFT bias.</li>"
    else:
        html_report += "<li>18/82 patterns are present but not dominant in game theory, suggesting they may be one of several organizing principles.</li>"
    
    if summary['average_pi_score'] > 0.6:
        html_report += "<li>π-related relationships are prevalent in strategic interactions, potentially indicating a connection to fundamental mathematical principles.</li>"
    
    # Add game-specific observations
    pd_details = [d for d in summary["simulation_details"] if "Prisoner's Dilemma" in d["name"]]
    if pd_details and np.mean([d["overall_1882_pattern_score"] for d in pd_details]) > 0.7:
        html_report += "<li>Prisoner's Dilemma games consistently exhibit stronger 18/82 patterns than other games, suggesting a connection between competitive dynamics and the UUFT framework.</li>"
    
    # Add parameter-specific observations
    uuft_details = [d for d in summary["simulation_details"] if "UUFT Optimized" in d["name"]]
    if uuft_details and np.mean([d["overall_pi_relationship_score"] for d in uuft_details]) > 0.7:
        html_report += "<li>UUFT-optimized simulations show stronger π-related patterns, confirming the effectiveness of the UUFT approach in revealing fundamental mathematical relationships.</li>"
    
    html_report += """
        </ul>
        <p>These findings support the UUFT framework's hypothesis that 18/82 patterns and π relationships represent fundamental organizing principles in strategic interactions and decision-making processes.</p>
    </body>
    </html>
    """
    
    # Save HTML report
    with open(os.path.join(RESULTS_DIR, "game_analysis_report.html"), 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    logger.info("Comprehensive report created successfully")
    
    return summary

def main():
    """Run the complete UUFT game theory analysis pipeline."""
    logger.info("Starting UUFT game theory analysis pipeline")
    
    # Step 1: Generate game theory scenarios
    scenarios = generate_game_scenarios()
    
    # Step 2: Run game theory simulations
    simulations = run_game_simulations(scenarios)
    
    # Step 3: Analyze simulations
    results = analyze_simulations(simulations)
    
    # Step 4: Create comprehensive report
    summary = create_comprehensive_report(results)
    
    logger.info("UUFT game theory analysis pipeline completed successfully")
    
    # Print location of report
    print(f"\nAnalysis complete! Comprehensive report available at:")
    print(f"  {os.path.abspath(os.path.join(RESULTS_DIR, 'game_analysis_report.html'))}")

if __name__ == "__main__":
    main()
