"""
Example of using the integrated Query Manager with the Evidence Manager.

This example demonstrates how to use the Query Manager integrated with
the Evidence Manager to search for evidence and requirements.
"""

import os
import sys
import json
import logging
import datetime
import uuid

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.evidence_manager import EvidenceManager
from src.ucecs.core.query_manager import QueryField, QueryOperator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the integrated Query Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_integrated_query_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create directories for the Evidence Manager
    evidence_dir = os.path.join(temp_dir, 'evidence_data')
    
    # Create an Evidence Manager
    manager = EvidenceManager(
        evidence_dir=evidence_dir,
        current_user_id="admin"
    )
    
    # Create sample requirements
    requirements = {
        'REQ-001': {
            'id': 'REQ-001',
            'name': 'Password Policy',
            'description': 'The organization must have a password policy that requires complex passwords.',
            'framework': 'NIST SP 800-53',
            'control': 'IA-5'
        },
        'REQ-002': {
            'id': 'REQ-002',
            'name': 'Access Control',
            'description': 'The organization must implement access controls to restrict system access to authorized users.',
            'framework': 'NIST SP 800-53',
            'control': 'AC-3'
        },
        'REQ-003': {
            'id': 'REQ-003',
            'name': 'Audit Logging',
            'description': 'The organization must implement audit logging to record security-relevant events.',
            'framework': 'NIST SP 800-53',
            'control': 'AU-2'
        },
        'REQ-004': {
            'id': 'REQ-004',
            'name': 'Security Awareness Training',
            'description': 'The organization must provide security awareness training to all personnel.',
            'framework': 'NIST SP 800-53',
            'control': 'AT-2'
        },
        'REQ-005': {
            'id': 'REQ-005',
            'name': 'Incident Response',
            'description': 'The organization must establish an incident response capability.',
            'framework': 'NIST SP 800-53',
            'control': 'IR-1'
        }
    }
    
    try:
        # Create sample evidence items
        for i in range(50):
            # Generate a unique ID
            evidence_id = f"evidence_{i+1}"
            
            # Determine the type and category
            if i < 10:
                evidence_type = 'document'
                category = 'policy'
                requirement_id = 'REQ-001'
                is_valid = i != 5  # Make one invalid
            elif i < 20:
                evidence_type = 'configuration'
                category = 'configuration'
                requirement_id = 'REQ-002'
                is_valid = i != 15  # Make one invalid
            elif i < 30:
                evidence_type = 'log'
                category = 'log'
                requirement_id = 'REQ-003'
                is_valid = i != 25  # Make one invalid
            elif i < 40:
                evidence_type = 'document'
                category = 'procedure'
                requirement_id = 'REQ-004'
                is_valid = i != 35  # Make one invalid
            else:
                evidence_type = 'document'
                category = 'policy'
                requirement_id = 'REQ-005'
                is_valid = i != 45  # Make one invalid
            
            # Create the evidence
            created_at = (datetime.datetime.now(datetime.timezone.utc) - 
                         datetime.timedelta(days=i)).isoformat()
            
            evidence = {
                'id': evidence_id,
                'type': evidence_type,
                'source': 'example',
                'status': 'stored',
                'created_at': created_at,
                'updated_at': created_at,
                'data': {
                    'title': f'Sample {evidence_type.title()} {i+1}',
                    'content': f'This is a sample {evidence_type} for testing the Query Manager.'
                },
                'metadata': {
                    'tags': ['example', evidence_type, category]
                },
                'validation_results': {
                    'is_valid': is_valid,
                    'details': {
                        'reason': 'Example validation result'
                    }
                }
            }
            
            # Register the evidence
            manager.register_evidence(evidence)
            
            # Add the evidence to the category
            manager.add_evidence_to_category(evidence_id, category)
            
            # Add the evidence to the requirement
            manager.add_evidence_to_requirement(evidence_id, requirement_id)
            
            # Add tags
            for tag in ['example', evidence_type, category]:
                manager.add_evidence_tag(evidence_id, tag)
        
        # Example 1: Simple query by type
        logger.info("Example 1: Simple query by type")
        type_query = manager.build_query(QueryField.TYPE, 'document')
        type_results = manager.search_evidence(
            query=type_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {type_results['pagination']['total_results']} documents")
        logger.info(f"First page results: {json.dumps([r['id'] for r in type_results['results']], indent=2)}")
        
        # Example 2: Query by category
        logger.info("\nExample 2: Query by category")
        category_query = manager.build_query(QueryField.CATEGORY, 'policy')
        category_results = manager.search_evidence(
            query=category_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {category_results['pagination']['total_results']} policy evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in category_results['results']], indent=2)}")
        
        # Example 3: Query by validation status
        logger.info("\nExample 3: Query by validation status")
        validation_query = manager.build_query(QueryField.VALIDATION_STATUS, True)
        validation_results = manager.search_evidence(
            query=validation_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {validation_results['pagination']['total_results']} valid evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in validation_results['results']], indent=2)}")
        
        # Example 4: Query by date range
        logger.info("\nExample 4: Query by date range")
        date_query = manager.build_query(
            QueryField.CREATED_AT,
            {
                'start': (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=20)).isoformat(),
                'end': (datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=10)).isoformat()
            }
        )
        date_results = manager.search_evidence(
            query=date_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {date_results['pagination']['total_results']} evidence items in date range")
        logger.info(f"First page results: {json.dumps([r['id'] for r in date_results['results']], indent=2)}")
        
        # Example 5: Compound query (AND)
        logger.info("\nExample 5: Compound query (AND)")
        and_query = manager.build_compound_query(
            QueryOperator.AND,
            [
                manager.build_query(QueryField.TYPE, 'document'),
                manager.build_query(QueryField.VALIDATION_STATUS, True)
            ]
        )
        and_results = manager.search_evidence(
            query=and_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {and_results['pagination']['total_results']} valid documents")
        logger.info(f"First page results: {json.dumps([r['id'] for r in and_results['results']], indent=2)}")
        
        # Example 6: Compound query (OR)
        logger.info("\nExample 6: Compound query (OR)")
        or_query = manager.build_compound_query(
            QueryOperator.OR,
            [
                manager.build_query(QueryField.CATEGORY, 'policy'),
                manager.build_query(QueryField.CATEGORY, 'procedure')
            ]
        )
        or_results = manager.search_evidence(
            query=or_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {or_results['pagination']['total_results']} policy or procedure evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in or_results['results']], indent=2)}")
        
        # Example 7: Compound query (NOT)
        logger.info("\nExample 7: Compound query (NOT)")
        not_query = manager.build_compound_query(
            QueryOperator.NOT,
            [
                manager.build_query(QueryField.VALIDATION_STATUS, True)
            ]
        )
        not_results = manager.search_evidence(
            query=not_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {not_results['pagination']['total_results']} invalid evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in not_results['results']], indent=2)}")
        
        # Example 8: Complex compound query
        logger.info("\nExample 8: Complex compound query")
        complex_query = manager.build_compound_query(
            QueryOperator.AND,
            [
                manager.build_query(QueryField.TYPE, 'document'),
                manager.build_compound_query(
                    QueryOperator.OR,
                    [
                        manager.build_query(QueryField.CATEGORY, 'policy'),
                        manager.build_query(QueryField.CATEGORY, 'procedure')
                    ]
                ),
                manager.build_query(QueryField.VALIDATION_STATUS, True)
            ]
        )
        complex_results = manager.search_evidence(
            query=complex_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {complex_results['pagination']['total_results']} valid policy or procedure documents")
        logger.info(f"First page results: {json.dumps([r['id'] for r in complex_results['results']], indent=2)}")
        
        # Example 9: Search requirements by framework
        logger.info("\nExample 9: Search requirements by framework")
        framework_query = {
            'field': 'framework',
            'value': 'NIST SP 800-53'
        }
        framework_results = manager.search_requirements(
            requirements=requirements,
            query=framework_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {framework_results['pagination']['total_results']} NIST SP 800-53 requirements")
        logger.info(f"First page results: {json.dumps([r['id'] for r in framework_results['results']], indent=2)}")
        
        # Example 10: Search requirements by text
        logger.info("\nExample 10: Search requirements by text")
        text_query = {
            'field': 'content',
            'value': 'password'
        }
        text_results = manager.search_requirements(
            requirements=requirements,
            query=text_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {text_results['pagination']['total_results']} requirements containing 'password'")
        logger.info(f"First page results: {json.dumps([r['id'] for r in text_results['results']], indent=2)}")
        
        # Example 11: Search requirements by evidence count
        logger.info("\nExample 11: Search requirements by evidence count")
        count_query = {
            'field': 'evidence_count',
            'value': {
                'min': 5
            }
        }
        count_results = manager.search_requirements(
            requirements=requirements,
            query=count_query,
            page=1,
            page_size=10
        )
        logger.info(f"Found {count_results['pagination']['total_results']} requirements with at least 5 evidence items")
        logger.info(f"First page results: {json.dumps([r['id'] for r in count_results['results']], indent=2)}")
        
        # Example 12: Use the legacy search method for backward compatibility
        logger.info("\nExample 12: Use the legacy search method")
        legacy_results = manager.search_evidence_legacy(
            category='policy',
            tags=['document']
        )
        logger.info(f"Found {len(legacy_results)} policy documents using legacy search")
        logger.info(f"Legacy search results: {json.dumps([r['id'] for r in legacy_results], indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
