/**
 * Unit tests for the Connector Registry
 */

const path = require('path');
const fs = require('fs').promises;
const connectorRegistry = require('../../registry/connector-registry');

describe('Connector Registry', () => {
  beforeAll(async () => {
    // Initialize the registry
    await connectorRegistry.initialize();
  });

  test('should load connector templates from the templates directory', () => {
    // Check if connectors were loaded
    const connectors = connectorRegistry.getAllConnectors();
    expect(connectors.length).toBeGreaterThan(0);
  });

  test('should get a connector by ID', () => {
    // Get a connector by ID
    const connectorId = 'google-cloud-security-1.0.0';
    const connector = connectorRegistry.getConnector(connectorId);
    
    expect(connector).not.toBeNull();
    expect(connector.metadata.name).toBe('Google Cloud Security');
    expect(connector.metadata.version).toBe('1.0.0');
  });

  test('should search connectors by query', () => {
    // Search for connectors
    const results = connectorRegistry.searchConnectors('google');
    
    expect(results.length).toBeGreaterThan(0);
    expect(results[0].metadata.name).toBe('Google Cloud Security');
  });

  test('should get connectors by category', () => {
    // Get connectors by category
    const results = connectorRegistry.getConnectorsByCategory('Cloud Security');
    
    expect(results.length).toBeGreaterThan(0);
    expect(results[0].metadata.category).toBe('Cloud Security');
  });

  test('should register a new connector', async () => {
    // Create a new connector template
    const template = {
      metadata: {
        name: 'Test Connector',
        version: '1.0.0',
        category: 'Other',
        description: 'Test connector for unit tests'
      },
      authentication: {
        type: 'API_KEY',
        fields: {
          apiKey: {
            type: 'string',
            description: 'API Key',
            required: true,
            sensitive: true
          }
        },
        testConnection: {
          endpoint: '/test',
          method: 'GET'
        }
      },
      configuration: {
        baseUrl: 'https://api.example.com'
      },
      endpoints: [
        {
          id: 'test-endpoint',
          name: 'Test Endpoint',
          path: '/test',
          method: 'GET'
        }
      ]
    };
    
    // Register the connector
    await connectorRegistry.registerConnector(template);
    
    // Get the connector
    const connectorId = 'test-connector-1.0.0';
    const connector = connectorRegistry.getConnector(connectorId);
    
    expect(connector).not.toBeNull();
    expect(connector.metadata.name).toBe('Test Connector');
    
    // Clean up
    await connectorRegistry.deleteConnector(connectorId);
  });

  test('should update an existing connector', async () => {
    // Create a new connector template
    const template = {
      metadata: {
        name: 'Update Test',
        version: '1.0.0',
        category: 'Other',
        description: 'Test connector for update tests'
      },
      authentication: {
        type: 'API_KEY',
        fields: {
          apiKey: {
            type: 'string',
            description: 'API Key',
            required: true,
            sensitive: true
          }
        },
        testConnection: {
          endpoint: '/test',
          method: 'GET'
        }
      },
      configuration: {
        baseUrl: 'https://api.example.com'
      },
      endpoints: [
        {
          id: 'test-endpoint',
          name: 'Test Endpoint',
          path: '/test',
          method: 'GET'
        }
      ]
    };
    
    // Register the connector
    await connectorRegistry.registerConnector(template);
    
    // Update the connector
    const connectorId = 'update-test-1.0.0';
    const updatedTemplate = {
      ...template,
      metadata: {
        ...template.metadata,
        description: 'Updated description'
      }
    };
    
    await connectorRegistry.updateConnector(connectorId, updatedTemplate);
    
    // Get the updated connector
    const connector = connectorRegistry.getConnector(connectorId);
    
    expect(connector).not.toBeNull();
    expect(connector.metadata.description).toBe('Updated description');
    
    // Clean up
    await connectorRegistry.deleteConnector(connectorId);
  });
});
