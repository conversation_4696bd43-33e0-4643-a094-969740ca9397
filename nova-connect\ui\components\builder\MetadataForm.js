import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

const categories = [
  'Cloud Security',
  'Identity & Access Management',
  'Workflow & Ticketing',
  'Compliance Frameworks',
  'Security Information',
  'Document & Signature',
  'ERP & Finance',
  'Threat Intelligence',
  'Other'
];

export default function MetadataForm({ connector, updateConnector }) {
  const [newTag, setNewTag] = useState('');
  const { control, handleSubmit, formState: { errors } } = useForm({
    defaultValues: connector.metadata
  });

  const onSubmit = (data) => {
    updateConnector('metadata', data);
  };

  const handleAddTag = () => {
    if (newTag.trim() && !connector.metadata.tags.includes(newTag.trim())) {
      const updatedTags = [...connector.metadata.tags, newTag.trim()];
      updateConnector('metadata', { tags: updatedTags });
      setNewTag('');
    }
  };

  const handleDeleteTag = (tagToDelete) => {
    const updatedTags = connector.metadata.tags.filter(tag => tag !== tagToDelete);
    updateConnector('metadata', { tags: updatedTags });
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  return (
    <Box component="form" onChange={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Controller
            name="name"
            control={control}
            rules={{ required: 'Name is required' }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Connector Name"
                fullWidth
                required
                error={!!errors.name}
                helperText={errors.name?.message}
                placeholder="e.g., AWS Security Hub"
              />
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            name="version"
            control={control}
            rules={{ 
              required: 'Version is required',
              pattern: {
                value: /^\d+\.\d+\.\d+$/,
                message: 'Version must be in format x.y.z'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Version"
                fullWidth
                required
                error={!!errors.version}
                helperText={errors.version?.message || 'Semantic version (e.g., 1.0.0)'}
                placeholder="1.0.0"
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Controller
            name="category"
            control={control}
            rules={{ required: 'Category is required' }}
            render={({ field }) => (
              <FormControl fullWidth required error={!!errors.category}>
                <InputLabel id="category-label">Category</InputLabel>
                <Select
                  {...field}
                  labelId="category-label"
                  label="Category"
                >
                  {categories.map((category) => (
                    <MenuItem key={category} value={category}>
                      {category}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Controller
            name="description"
            control={control}
            rules={{ 
              required: 'Description is required',
              minLength: {
                value: 20,
                message: 'Description should be at least 20 characters'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Description"
                fullWidth
                required
                multiline
                rows={4}
                error={!!errors.description}
                helperText={errors.description?.message || 'Detailed description of the connector'}
                placeholder="Describe what this connector does and what systems it integrates with"
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Controller
            name="author"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Author"
                fullWidth
                helperText="Organization or individual who created this connector"
                placeholder="e.g., NovaGRC"
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Tags
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <TextField
              label="Add Tag"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="e.g., security, cloud, aws"
              sx={{ mr: 1, flexGrow: 1 }}
            />
            <IconButton 
              onClick={handleAddTag}
              color="primary"
              disabled={!newTag.trim()}
            >
              <AddIcon />
            </IconButton>
          </Box>
          <Paper 
            variant="outlined" 
            sx={{ 
              p: 2, 
              minHeight: '100px',
              backgroundColor: 'background.default'
            }}
          >
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {connector.metadata.tags.map((tag) => (
                <Chip
                  key={tag}
                  label={tag}
                  onDelete={() => handleDeleteTag(tag)}
                  color="primary"
                  variant="outlined"
                />
              ))}
              {connector.metadata.tags.length === 0 && (
                <Typography variant="body2" color="text.secondary">
                  No tags added yet. Tags help users find your connector.
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
