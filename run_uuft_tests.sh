#!/bin/bash
# Run UUFT/CSDE tests locally

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Running UUFT/CSDE Tests ===${NC}"

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Error: Python not found. Please install Python 3.8 or higher.${NC}"
    exit 1
fi

# Install required packages
echo -e "${GREEN}Installing required packages...${NC}"
pip install pyyaml matplotlib numpy

# Run the tests
echo -e "${GREEN}Running tests...${NC}"
python3 config_loader.py

echo -e "${GREEN}Tests completed!${NC}"
echo "Results are available in the results directory."
