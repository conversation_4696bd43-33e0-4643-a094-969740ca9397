/**
 * NovaCore NovaFlow Module
 *
 * This module provides workflow orchestration functionality.
 * Nova<PERSON>low is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const routes = require('./routes');
const { WorkflowController, VerificationController } = require('./controllers');
const { WorkflowService, WorkflowTemplateService } = require('./services');
const { Workflow, WorkflowTemplate, WorkflowExecution } = require('./models');
const { WorkflowExecutionEngine, TaskExecutionEngine, verification } = require('./engines');

module.exports = {
  routes,
  controllers: {
    WorkflowController,
    VerificationController
  },
  services: {
    WorkflowService,
    WorkflowTemplateService
  },
  models: {
    Workflow,
    WorkflowTemplate,
    WorkflowExecution
  },
  engines: {
    WorkflowExecutionEngine,
    TaskExecutionEngine,
    verification
  }
};
