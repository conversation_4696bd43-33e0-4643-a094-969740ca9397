<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>3. Three-Body Problem Reframing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>3. Three-Body Problem Reframing</h1>

    <div class="diagram-container">
        <!-- Three-Body Problem Reframing -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Three-Body Problem Reframing
            <div class="element-number">1</div>
        </div>

        <!-- Classical Physics Lens -->
        <div class="element" style="top: 150px; left: 150px; width: 300px; font-weight: bold; font-size: 16px;">
            Classical Physics Lens
            <div class="element-number">2</div>
        </div>

        <!-- Comphyological Lens -->
        <div class="element" style="top: 150px; left: 550px; width: 300px; font-weight: bold; font-size: 16px;">
            Comphyological Lens
            <div class="element-number">3</div>
        </div>

        <!-- Classical Boundaries -->
        <div class="element" style="top: 250px; left: 150px; width: 300px; font-size: 14px;">
            System Boundaries:<br>Potentially infinite, open
            <div class="element-number">4</div>
        </div>

        <!-- Comphyological Boundaries -->
        <div class="element" style="top: 250px; left: 550px; width: 300px; font-size: 14px;">
            System Boundaries:<br>Finite, closed, nested
            <div class="element-number">5</div>
        </div>

        <!-- Classical Predictability -->
        <div class="element" style="top: 350px; left: 150px; width: 300px; font-size: 14px;">
            Predictability:<br>Chaotic, sensitive to initial conditions
            <div class="element-number">6</div>
        </div>

        <!-- Comphyological Predictability -->
        <div class="element" style="top: 350px; left: 550px; width: 300px; font-size: 14px;">
            Predictability:<br>Stable under nested constraints
            <div class="element-number">7</div>
        </div>

        <!-- Classical Math -->
        <div class="element" style="top: 450px; left: 150px; width: 300px; font-size: 14px;">
            Mathematical Approach:<br>Differential equations with diverging solutions
            <div class="element-number">8</div>
        </div>

        <!-- Comphyological Math -->
        <div class="element" style="top: 450px; left: 550px; width: 300px; font-size: 14px;">
            Mathematical Approach:<br>Tensor fields with boundary conditions
            <div class="element-number">9</div>
        </div>

        <!-- Solution Equation -->
        <div class="element" style="top: 600px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Three-Body Solution = <span class="bold-formula">∮(T⊗G)·dS</span>
            <div class="element-number">10</div>
        </div>

        <!-- Implementation -->
        <div class="element" style="top: 700px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Technical Implementation: Three-Body Problem Reframing
            <div class="element-number">11</div>
        </div>

        <!-- Connections -->
        <!-- Title to Classical -->
        <div class="connection" style="top: 100px; left: 300px; width: 2px; height: 50px;"></div>

        <!-- Title to Comphyological -->
        <div class="connection" style="top: 100px; left: 700px; width: 2px; height: 50px;"></div>

        <!-- Classical to Boundaries -->
        <div class="connection" style="top: 200px; left: 300px; width: 2px; height: 50px;"></div>

        <!-- Comphyological to Boundaries -->
        <div class="connection" style="top: 200px; left: 700px; width: 2px; height: 50px;"></div>

        <!-- Classical Boundaries to Predictability -->
        <div class="connection" style="top: 300px; left: 300px; width: 2px; height: 50px;"></div>

        <!-- Comphyological Boundaries to Predictability -->
        <div class="connection" style="top: 300px; left: 700px; width: 2px; height: 50px;"></div>

        <!-- Classical Predictability to Math -->
        <div class="connection" style="top: 400px; left: 300px; width: 2px; height: 50px;"></div>

        <!-- Comphyological Predictability to Math -->
        <div class="connection" style="top: 400px; left: 700px; width: 2px; height: 50px;"></div>

        <!-- Classical to Solution -->
        <div class="connection" style="top: 500px; left: 300px; width: 2px; height: 100px;"></div>

        <!-- Comphyological to Solution -->
        <div class="connection" style="top: 500px; left: 700px; width: 2px; height: 100px;"></div>

        <!-- Solution to Implementation -->
        <!-- Line removed as requested -->
    </div>
</body>
</html>
