/**
 * Advanced Resonance Detection
 * 
 * This module provides advanced algorithms for detecting resonance states,
 * quantum silence, and harmonic patterns in the Comphyological Tensor Core.
 * 
 * It implements:
 * 1. Harmonic Resonance Detection
 * 2. Quantum Silence Detection with adaptive thresholds
 * 3. Cross-Domain Phase Alignment
 * 4. Resonance Forecasting
 * 5. Resonant Slope (Ψᴿ = dΨₑ/dt) calculation
 */

const { performance } = require('perf_hooks');
const { FFT } = require('./fft');

/**
 * Advanced Resonance Detector class
 */
class AdvancedResonanceDetector {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableLogging: options.enableLogging || false,
      targetFrequency: options.targetFrequency || 396, // Hz - the "OM Tone"
      precisionFFT: options.precisionFFT || 0.001, // attohertz precision
      quantumVacuumNoise: options.quantumVacuumNoise !== false,
      crossDomainPhaseAlignment: options.crossDomainPhaseAlignment !== false,
      silenceThreshold: options.silenceThreshold || 0.001,
      adaptiveThreshold: options.adaptiveThreshold !== false,
      adaptiveRate: options.adaptiveRate || 0.01,
      harmonicDetection: options.harmonicDetection !== false,
      harmonicThreshold: options.harmonicThreshold || 0.05,
      forecastingWindow: options.forecastingWindow || 10,
      resonantSlopeWindow: options.resonantSlopeWindow || 5,
      sampleRate: options.sampleRate || 44100,
      fftSize: options.fftSize || 4096,
      ...options
    };
    
    // Initialize FFT
    this.fft = new FFT(this.options.fftSize, this.options.sampleRate);
    
    // Initialize state
    this.state = {
      frequency: this.options.targetFrequency,
      deviation: 0,
      isQuantumSilence: false,
      phaseAlignment: 1.0,
      quantumVacuumNoise: 0.0,
      harmonics: [],
      resonantSlope: 0.0,
      forecast: [],
      adaptiveThreshold: this.options.silenceThreshold,
      lastUpdate: performance.now(),
      history: []
    };
    
    if (this.options.enableLogging) {
      console.log('Advanced Resonance Detector initialized with options:', {
        targetFrequency: this.options.targetFrequency,
        precisionFFT: this.options.precisionFFT,
        silenceThreshold: this.options.silenceThreshold,
        adaptiveThreshold: this.options.adaptiveThreshold,
        harmonicDetection: this.options.harmonicDetection
      });
    }
  }
  
  /**
   * Analyze resonance state from tensor core result
   * @param {Object} result - Tensor core result
   * @returns {Object} Resonance state
   */
  analyzeResonance(result) {
    const startTime = performance.now();
    
    // Extract data from result
    const { comphyon, energies, weights } = result;
    
    // Calculate frequency deviation
    const frequency = this.calculateFrequency(comphyon, energies, weights);
    const deviation = Math.abs(frequency - this.options.targetFrequency) / this.options.targetFrequency * 100;
    
    // Calculate phase alignment
    const phaseAlignment = this.calculatePhaseAlignment(energies, weights);
    
    // Calculate quantum vacuum noise
    const quantumVacuumNoise = this.calculateQuantumVacuumNoise(comphyon, energies);
    
    // Detect quantum silence
    const isQuantumSilence = this.detectQuantumSilence(deviation, phaseAlignment, quantumVacuumNoise);
    
    // Detect harmonics
    const harmonics = this.detectHarmonics(frequency, comphyon, energies);
    
    // Calculate resonant slope
    const resonantSlope = this.calculateResonantSlope(comphyon);
    
    // Generate forecast
    const forecast = this.generateForecast(comphyon, frequency, deviation);
    
    // Update adaptive threshold if enabled
    if (this.options.adaptiveThreshold) {
      this.updateAdaptiveThreshold(deviation, isQuantumSilence);
    }
    
    // Update state
    const now = performance.now();
    const state = {
      frequency,
      deviation,
      isQuantumSilence,
      phaseAlignment,
      quantumVacuumNoise,
      harmonics,
      resonantSlope,
      forecast,
      adaptiveThreshold: this.options.adaptiveThreshold ? this.state.adaptiveThreshold : this.options.silenceThreshold,
      timestamp: now,
      processingTime: now - startTime
    };
    
    // Update history
    this.updateHistory(state);
    
    // Update state
    this.state = {
      ...this.state,
      ...state
    };
    
    if (this.options.enableLogging) {
      console.log('Resonance analysis:', {
        frequency: frequency.toFixed(6),
        deviation: deviation.toFixed(6),
        isQuantumSilence,
        phaseAlignment: phaseAlignment.toFixed(6),
        quantumVacuumNoise: quantumVacuumNoise.toFixed(6),
        resonantSlope: resonantSlope.toFixed(6),
        processingTime: (now - startTime).toFixed(6)
      });
    }
    
    return state;
  }
  
  /**
   * Calculate frequency from comphyon value and energies
   * @param {number} comphyon - Comphyon value
   * @param {Object} energies - Domain energies
   * @param {Object} weights - Domain weights
   * @returns {number} Frequency
   */
  calculateFrequency(comphyon, energies, weights) {
    // Base frequency is the target frequency
    const baseFrequency = this.options.targetFrequency;
    
    // Calculate frequency deviation based on comphyon value
    // Perfect resonance (comphyon = 0) should result in exactly the target frequency
    const comphyonFactor = Math.abs(comphyon) * 10;
    
    // Calculate weighted energy factor
    const weightedEnergies = 
      (energies.csde * weights.csde) + 
      (energies.csfe * weights.csfe) + 
      (energies.csme * weights.csme);
    
    // Calculate frequency deviation
    const frequencyDeviation = comphyonFactor * (1 - Math.exp(-weightedEnergies));
    
    // Calculate frequency
    // If comphyon is positive, frequency is higher than target
    // If comphyon is negative, frequency is lower than target
    const frequency = comphyon >= 0 
      ? baseFrequency + frequencyDeviation
      : baseFrequency - frequencyDeviation;
    
    return frequency;
  }
  
  /**
   * Calculate phase alignment from energies and weights
   * @param {Object} energies - Domain energies
   * @param {Object} weights - Domain weights
   * @returns {number} Phase alignment (0-1)
   */
  calculatePhaseAlignment(energies, weights) {
    // Calculate energy ratios
    const totalEnergy = energies.csde + energies.csfe + energies.csme;
    const csdeRatio = energies.csde / totalEnergy;
    const csfeRatio = energies.csfe / totalEnergy;
    const csmeRatio = energies.csme / totalEnergy;
    
    // Calculate weight ratios
    const csdeWeightRatio = weights.csde;
    const csfeWeightRatio = weights.csfe;
    const csmeWeightRatio = weights.csme;
    
    // Calculate phase alignment as the similarity between energy and weight distributions
    const alignment = 1 - (
      Math.abs(csdeRatio - csdeWeightRatio) +
      Math.abs(csfeRatio - csfeWeightRatio) +
      Math.abs(csmeRatio - csmeWeightRatio)
    ) / 2;
    
    return Math.max(0, Math.min(1, alignment));
  }
  
  /**
   * Calculate quantum vacuum noise from comphyon value and energies
   * @param {number} comphyon - Comphyon value
   * @param {Object} energies - Domain energies
   * @returns {number} Quantum vacuum noise (0-1)
   */
  calculateQuantumVacuumNoise(comphyon, energies) {
    if (!this.options.quantumVacuumNoise) {
      return 0;
    }
    
    // Calculate total energy
    const totalEnergy = energies.csde + energies.csfe + energies.csme;
    
    // Calculate energy variance
    const meanEnergy = totalEnergy / 3;
    const variance = (
      Math.pow(energies.csde - meanEnergy, 2) +
      Math.pow(energies.csfe - meanEnergy, 2) +
      Math.pow(energies.csme - meanEnergy, 2)
    ) / 3;
    
    // Calculate quantum vacuum noise
    // Higher comphyon values and higher energy variance result in higher noise
    const noise = Math.abs(comphyon) * Math.sqrt(variance) * 5;
    
    return Math.max(0, Math.min(1, noise));
  }
  
  /**
   * Detect quantum silence
   * @param {number} deviation - Frequency deviation
   * @param {number} phaseAlignment - Phase alignment
   * @param {number} quantumVacuumNoise - Quantum vacuum noise
   * @returns {boolean} Whether quantum silence is detected
   */
  detectQuantumSilence(deviation, phaseAlignment, quantumVacuumNoise) {
    // Get threshold (adaptive or fixed)
    const threshold = this.options.adaptiveThreshold 
      ? this.state.adaptiveThreshold 
      : this.options.silenceThreshold;
    
    // Calculate silence score
    const deviationScore = Math.exp(-deviation / 10);
    const alignmentScore = Math.pow(phaseAlignment, 2);
    const noiseScore = Math.exp(-quantumVacuumNoise * 10);
    
    const silenceScore = (deviationScore + alignmentScore + noiseScore) / 3;
    
    // Detect quantum silence
    return silenceScore > (1 - threshold);
  }
  
  /**
   * Update adaptive threshold
   * @param {number} deviation - Frequency deviation
   * @param {boolean} isQuantumSilence - Whether quantum silence is detected
   */
  updateAdaptiveThreshold(deviation, isQuantumSilence) {
    if (!this.options.adaptiveThreshold) {
      return;
    }
    
    // Calculate target threshold
    const targetThreshold = isQuantumSilence
      ? Math.max(this.options.silenceThreshold, this.state.adaptiveThreshold * 0.99)
      : Math.min(0.1, this.state.adaptiveThreshold * 1.01);
    
    // Update threshold with adaptive rate
    this.state.adaptiveThreshold += (targetThreshold - this.state.adaptiveThreshold) * this.options.adaptiveRate;
  }
  
  /**
   * Detect harmonics
   * @param {number} frequency - Frequency
   * @param {number} comphyon - Comphyon value
   * @param {Object} energies - Domain energies
   * @returns {Array} Harmonics
   */
  detectHarmonics(frequency, comphyon, energies) {
    if (!this.options.harmonicDetection) {
      return [];
    }
    
    // Generate synthetic signal based on comphyon and energies
    const signal = this.generateSignal(comphyon, energies);
    
    // Perform FFT
    const fftResult = this.fft.forward(signal);
    
    // Find peaks in FFT result
    const peaks = this.findPeaks(fftResult);
    
    // Convert peaks to harmonics
    const harmonics = peaks.map(peak => ({
      frequency: peak.frequency,
      amplitude: peak.amplitude,
      phase: peak.phase,
      ratio: peak.frequency / this.options.targetFrequency
    }));
    
    // Filter harmonics by threshold
    return harmonics.filter(h => h.amplitude > this.options.harmonicThreshold);
  }
  
  /**
   * Generate synthetic signal based on comphyon and energies
   * @param {number} comphyon - Comphyon value
   * @param {Object} energies - Domain energies
   * @returns {Array} Signal
   */
  generateSignal(comphyon, energies) {
    const signal = new Array(this.options.fftSize);
    
    // Calculate signal parameters
    const amplitude = Math.min(1, Math.max(0.1, 1 - Math.abs(comphyon)));
    const frequency = this.calculateFrequency(comphyon, energies, { csde: 1/3, csfe: 1/3, csme: 1/3 });
    const phase = Math.atan2(energies.csfe, energies.csde);
    
    // Generate signal
    for (let i = 0; i < this.options.fftSize; i++) {
      const t = i / this.options.sampleRate;
      
      // Base signal at target frequency
      const baseSignal = amplitude * Math.sin(2 * Math.PI * frequency * t + phase);
      
      // Add harmonics based on energies
      const harmonic1 = energies.csde * 0.5 * Math.sin(2 * Math.PI * frequency * 2 * t);
      const harmonic2 = energies.csfe * 0.3 * Math.sin(2 * Math.PI * frequency * 3 * t);
      const harmonic3 = energies.csme * 0.2 * Math.sin(2 * Math.PI * frequency * 5 * t);
      
      // Add noise based on comphyon
      const noise = Math.abs(comphyon) * (Math.random() * 2 - 1) * 0.1;
      
      signal[i] = baseSignal + harmonic1 + harmonic2 + harmonic3 + noise;
    }
    
    return signal;
  }
  
  /**
   * Find peaks in FFT result
   * @param {Array} fftResult - FFT result
   * @returns {Array} Peaks
   */
  findPeaks(fftResult) {
    const peaks = [];
    const threshold = 0.1;
    
    // Find peaks
    for (let i = 2; i < fftResult.length / 2 - 2; i++) {
      if (
        fftResult[i] > threshold &&
        fftResult[i] > fftResult[i - 1] &&
        fftResult[i] > fftResult[i - 2] &&
        fftResult[i] > fftResult[i + 1] &&
        fftResult[i] > fftResult[i + 2]
      ) {
        // Calculate frequency
        const frequency = i * this.options.sampleRate / this.options.fftSize;
        
        // Calculate amplitude
        const amplitude = fftResult[i];
        
        // Calculate phase
        const phase = Math.atan2(fftResult[i + this.options.fftSize / 2], fftResult[i]);
        
        peaks.push({
          frequency,
          amplitude,
          phase
        });
      }
    }
    
    // Sort peaks by amplitude
    return peaks.sort((a, b) => b.amplitude - a.amplitude);
  }
  
  /**
   * Calculate resonant slope (Ψᴿ = dΨₑ/dt)
   * @param {number} comphyon - Comphyon value
   * @returns {number} Resonant slope
   */
  calculateResonantSlope(comphyon) {
    // Get history window
    const historyWindow = this.state.history.slice(-this.options.resonantSlopeWindow);
    
    // If not enough history, return 0
    if (historyWindow.length < 2) {
      return 0;
    }
    
    // Calculate slope using linear regression
    const x = historyWindow.map((_, i) => i);
    const y = historyWindow.map(h => h.comphyon || 0);
    
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((a, b, i) => a + b * y[i], 0);
    const sumX2 = x.reduce((a, b) => a + b * b, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    
    return slope;
  }
  
  /**
   * Generate forecast
   * @param {number} comphyon - Comphyon value
   * @param {number} frequency - Frequency
   * @param {number} deviation - Deviation
   * @returns {Array} Forecast
   */
  generateForecast(comphyon, frequency, deviation) {
    // Get history window
    const historyWindow = this.state.history.slice(-this.options.forecastingWindow);
    
    // If not enough history, return empty forecast
    if (historyWindow.length < 3) {
      return [];
    }
    
    // Calculate resonant slope
    const resonantSlope = this.calculateResonantSlope(comphyon);
    
    // Generate forecast
    const forecast = [];
    
    for (let i = 1; i <= this.options.forecastingWindow; i++) {
      // Forecast comphyon
      const forecastComphyon = comphyon + resonantSlope * i;
      
      // Forecast frequency
      const forecastFrequency = this.options.targetFrequency + (frequency - this.options.targetFrequency) * Math.exp(-i * 0.1);
      
      // Forecast deviation
      const forecastDeviation = deviation * Math.exp(-i * 0.1);
      
      // Forecast quantum silence
      const forecastQuantumSilence = forecastDeviation < this.state.adaptiveThreshold * 100;
      
      forecast.push({
        comphyon: forecastComphyon,
        frequency: forecastFrequency,
        deviation: forecastDeviation,
        isQuantumSilence: forecastQuantumSilence,
        timestamp: this.state.timestamp + i * 1000
      });
    }
    
    return forecast;
  }
  
  /**
   * Update history
   * @param {Object} state - Resonance state
   */
  updateHistory(state) {
    // Add comphyon value to state for history
    state.comphyon = state.comphyon || 0;
    
    // Add state to history
    this.state.history.push(state);
    
    // Trim history if needed
    if (this.state.history.length > this.options.maxHistoryLength) {
      this.state.history.shift();
    }
  }
  
  /**
   * Get resonance state
   * @returns {Object} Resonance state
   */
  getResonanceState() {
    return {
      frequency: this.state.frequency,
      deviation: this.state.deviation,
      isQuantumSilence: this.state.isQuantumSilence,
      phaseAlignment: this.state.phaseAlignment,
      quantumVacuumNoise: this.state.quantumVacuumNoise,
      harmonics: this.state.harmonics,
      resonantSlope: this.state.resonantSlope,
      forecast: this.state.forecast,
      adaptiveThreshold: this.state.adaptiveThreshold,
      timestamp: this.state.timestamp
    };
  }
  
  /**
   * Get resonance history
   * @returns {Array} Resonance history
   */
  getResonanceHistory() {
    return this.state.history;
  }
}

/**
 * Create an advanced resonance detector
 * @param {Object} options - Configuration options
 * @returns {AdvancedResonanceDetector} Advanced resonance detector
 */
function createAdvancedResonanceDetector(options = {}) {
  return new AdvancedResonanceDetector(options);
}

module.exports = {
  AdvancedResonanceDetector,
  createAdvancedResonanceDetector
};
