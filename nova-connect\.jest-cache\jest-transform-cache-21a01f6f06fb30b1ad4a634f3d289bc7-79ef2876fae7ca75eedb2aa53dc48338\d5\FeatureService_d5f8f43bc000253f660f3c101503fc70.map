{"version": 3, "names": ["fs", "require", "promises", "path", "logger", "FeatureService", "constructor", "dataDir", "join", "__dirname", "featuresFile", "customerFeaturesFile", "tierFeatures", "core", "secure", "enterprise", "ai_boost", "ensureDataDir", "mkdir", "recursive", "access", "error", "code", "writeFile", "JSON", "stringify", "message", "loadFeatures", "data", "readFile", "parse", "loadCustomerFeatures", "saveCustomerFeatures", "customerFeatures", "enableFeaturesForTier", "customerId", "tier", "info", "features", "length", "warn", "status", "updatedAt", "Date", "toISOString", "featureCount", "disableFeatures", "disabledAt", "suspendFeatures", "suspendedAt", "isFeatureEnabled", "featureName", "debug", "isEnabled", "includes", "getCustomerFeatures", "module", "exports"], "sources": ["FeatureService.js"], "sourcesContent": ["/**\n * NovaConnect UAC Feature Service\n * \n * This service handles feature toggling based on customer entitlements.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst logger = require('../../config/logger');\n\nclass FeatureService {\n  constructor() {\n    this.dataDir = path.join(__dirname, '../../data/features');\n    this.featuresFile = path.join(this.dataDir, 'features.json');\n    this.customerFeaturesFile = path.join(this.dataDir, 'customer_features.json');\n    \n    // Define tier features\n    this.tierFeatures = {\n      core: [\n        'basic_api_access',\n        'standard_connectors',\n        'basic_normalization',\n        'basic_monitoring'\n      ],\n      secure: [\n        'basic_api_access',\n        'standard_connectors',\n        'advanced_connectors',\n        'basic_normalization',\n        'advanced_normalization',\n        'basic_monitoring',\n        'advanced_monitoring',\n        'basic_security'\n      ],\n      enterprise: [\n        'basic_api_access',\n        'standard_connectors',\n        'advanced_connectors',\n        'enterprise_connectors',\n        'basic_normalization',\n        'advanced_normalization',\n        'enterprise_normalization',\n        'basic_monitoring',\n        'advanced_monitoring',\n        'enterprise_monitoring',\n        'basic_security',\n        'advanced_security'\n      ],\n      ai_boost: [\n        'basic_api_access',\n        'standard_connectors',\n        'advanced_connectors',\n        'enterprise_connectors',\n        'ai_connectors',\n        'basic_normalization',\n        'advanced_normalization',\n        'enterprise_normalization',\n        'ai_normalization',\n        'basic_monitoring',\n        'advanced_monitoring',\n        'enterprise_monitoring',\n        'ai_monitoring',\n        'basic_security',\n        'advanced_security',\n        'ai_security'\n      ]\n    };\n    \n    this.ensureDataDir();\n  }\n  \n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.dataDir, { recursive: true });\n      \n      // Initialize features file if it doesn't exist\n      try {\n        await fs.access(this.featuresFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.featuresFile, JSON.stringify(this.tierFeatures));\n        } else {\n          throw error;\n        }\n      }\n      \n      // Initialize customer features file if it doesn't exist\n      try {\n        await fs.access(this.customerFeaturesFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.customerFeaturesFile, JSON.stringify({}));\n        } else {\n          throw error;\n        }\n      }\n    } catch (error) {\n      logger.error('Error ensuring feature data directory', { error: error.message });\n      throw error;\n    }\n  }\n  \n  /**\n   * Load features from file\n   */\n  async loadFeatures() {\n    try {\n      const data = await fs.readFile(this.featuresFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      logger.error('Error loading features', { error: error.message });\n      return this.tierFeatures;\n    }\n  }\n  \n  /**\n   * Load customer features from file\n   */\n  async loadCustomerFeatures() {\n    try {\n      const data = await fs.readFile(this.customerFeaturesFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      logger.error('Error loading customer features', { error: error.message });\n      return {};\n    }\n  }\n  \n  /**\n   * Save customer features to file\n   */\n  async saveCustomerFeatures(customerFeatures) {\n    try {\n      await fs.writeFile(this.customerFeaturesFile, JSON.stringify(customerFeatures, null, 2));\n    } catch (error) {\n      logger.error('Error saving customer features', { error: error.message });\n      throw error;\n    }\n  }\n  \n  /**\n   * Enable features for a customer based on tier\n   */\n  async enableFeaturesForTier(customerId, tier) {\n    try {\n      logger.info('Enabling features for tier', { customerId, tier });\n      \n      // Load features\n      const features = await this.loadFeatures();\n      \n      // Get features for tier\n      const tierFeatures = features[tier] || this.tierFeatures[tier] || [];\n      \n      if (tierFeatures.length === 0) {\n        logger.warn('No features found for tier', { tier });\n      }\n      \n      // Load customer features\n      const customerFeatures = await this.loadCustomerFeatures();\n      \n      // Update customer features\n      customerFeatures[customerId] = {\n        tier,\n        features: tierFeatures,\n        status: 'ACTIVE',\n        updatedAt: new Date().toISOString()\n      };\n      \n      // Save customer features\n      await this.saveCustomerFeatures(customerFeatures);\n      \n      logger.info('Features enabled for tier', { customerId, tier, featureCount: tierFeatures.length });\n      \n      return tierFeatures;\n    } catch (error) {\n      logger.error('Error enabling features for tier', { error: error.message, customerId, tier });\n      throw error;\n    }\n  }\n  \n  /**\n   * Disable features for a customer\n   */\n  async disableFeatures(customerId) {\n    try {\n      logger.info('Disabling features', { customerId });\n      \n      // Load customer features\n      const customerFeatures = await this.loadCustomerFeatures();\n      \n      // Check if customer has features\n      if (!customerFeatures[customerId]) {\n        logger.warn('No features found for customer', { customerId });\n        return;\n      }\n      \n      // Update customer features\n      customerFeatures[customerId] = {\n        ...customerFeatures[customerId],\n        status: 'DISABLED',\n        updatedAt: new Date().toISOString(),\n        disabledAt: new Date().toISOString()\n      };\n      \n      // Save customer features\n      await this.saveCustomerFeatures(customerFeatures);\n      \n      logger.info('Features disabled', { customerId });\n    } catch (error) {\n      logger.error('Error disabling features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n  \n  /**\n   * Suspend features for a customer\n   */\n  async suspendFeatures(customerId) {\n    try {\n      logger.info('Suspending features', { customerId });\n      \n      // Load customer features\n      const customerFeatures = await this.loadCustomerFeatures();\n      \n      // Check if customer has features\n      if (!customerFeatures[customerId]) {\n        logger.warn('No features found for customer', { customerId });\n        return;\n      }\n      \n      // Update customer features\n      customerFeatures[customerId] = {\n        ...customerFeatures[customerId],\n        status: 'SUSPENDED',\n        updatedAt: new Date().toISOString(),\n        suspendedAt: new Date().toISOString()\n      };\n      \n      // Save customer features\n      await this.saveCustomerFeatures(customerFeatures);\n      \n      logger.info('Features suspended', { customerId });\n    } catch (error) {\n      logger.error('Error suspending features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n  \n  /**\n   * Check if a feature is enabled for a customer\n   */\n  async isFeatureEnabled(customerId, featureName) {\n    try {\n      // Load customer features\n      const customerFeatures = await this.loadCustomerFeatures();\n      \n      // Check if customer has features\n      if (!customerFeatures[customerId]) {\n        logger.debug('No features found for customer', { customerId });\n        return false;\n      }\n      \n      // Check if customer features are active\n      if (customerFeatures[customerId].status !== 'ACTIVE') {\n        logger.debug('Customer features are not active', { customerId, status: customerFeatures[customerId].status });\n        return false;\n      }\n      \n      // Check if feature is enabled\n      const isEnabled = customerFeatures[customerId].features.includes(featureName);\n      \n      return isEnabled;\n    } catch (error) {\n      logger.error('Error checking if feature is enabled', { error: error.message, customerId, featureName });\n      return false;\n    }\n  }\n  \n  /**\n   * Get customer features\n   */\n  async getCustomerFeatures(customerId) {\n    try {\n      // Load customer features\n      const customerFeatures = await this.loadCustomerFeatures();\n      \n      // Return customer features or empty object\n      return customerFeatures[customerId] || { status: 'NOT_FOUND' };\n    } catch (error) {\n      logger.error('Error getting customer features', { error: error.message, customerId });\n      throw error;\n    }\n  }\n}\n\nmodule.exports = FeatureService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMG,MAAM,GAAGH,OAAO,CAAC,qBAAqB,CAAC;AAE7C,MAAMI,cAAc,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGJ,IAAI,CAACK,IAAI,CAACC,SAAS,EAAE,qBAAqB,CAAC;IAC1D,IAAI,CAACC,YAAY,GAAGP,IAAI,CAACK,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,eAAe,CAAC;IAC5D,IAAI,CAACI,oBAAoB,GAAGR,IAAI,CAACK,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,wBAAwB,CAAC;;IAE7E;IACA,IAAI,CAACK,YAAY,GAAG;MAClBC,IAAI,EAAE,CACJ,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,kBAAkB,CACnB;MACDC,MAAM,EAAE,CACN,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,qBAAqB,EACrB,wBAAwB,EACxB,kBAAkB,EAClB,qBAAqB,EACrB,gBAAgB,CACjB;MACDC,UAAU,EAAE,CACV,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,uBAAuB,EACvB,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,kBAAkB,EAClB,qBAAqB,EACrB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,CACpB;MACDC,QAAQ,EAAE,CACR,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,uBAAuB,EACvB,eAAe,EACf,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,kBAAkB,EAClB,kBAAkB,EAClB,qBAAqB,EACrB,uBAAuB,EACvB,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,aAAa;IAEjB,CAAC;IAED,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACE,MAAMA,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMjB,EAAE,CAACkB,KAAK,CAAC,IAAI,CAACX,OAAO,EAAE;QAAEY,SAAS,EAAE;MAAK,CAAC,CAAC;;MAEjD;MACA,IAAI;QACF,MAAMnB,EAAE,CAACoB,MAAM,CAAC,IAAI,CAACV,YAAY,CAAC;MACpC,CAAC,CAAC,OAAOW,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAMtB,EAAE,CAACuB,SAAS,CAAC,IAAI,CAACb,YAAY,EAAEc,IAAI,CAACC,SAAS,CAAC,IAAI,CAACb,YAAY,CAAC,CAAC;QAC1E,CAAC,MAAM;UACL,MAAMS,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMrB,EAAE,CAACoB,MAAM,CAAC,IAAI,CAACT,oBAAoB,CAAC;MAC5C,CAAC,CAAC,OAAOU,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAMtB,EAAE,CAACuB,SAAS,CAAC,IAAI,CAACZ,oBAAoB,EAAEa,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,CAAC,MAAM;UACL,MAAMJ,KAAK;QACb;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,uCAAuC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC,CAAC;MAC/E,MAAML,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMM,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAM5B,EAAE,CAAC6B,QAAQ,CAAC,IAAI,CAACnB,YAAY,EAAE,MAAM,CAAC;MACzD,OAAOc,IAAI,CAACM,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,wBAAwB,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC,CAAC;MAChE,OAAO,IAAI,CAACd,YAAY;IAC1B;EACF;;EAEA;AACF;AACA;EACE,MAAMmB,oBAAoBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAMH,IAAI,GAAG,MAAM5B,EAAE,CAAC6B,QAAQ,CAAC,IAAI,CAAClB,oBAAoB,EAAE,MAAM,CAAC;MACjE,OAAOa,IAAI,CAACM,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,iCAAiC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC,CAAC;MACzE,OAAO,CAAC,CAAC;IACX;EACF;;EAEA;AACF;AACA;EACE,MAAMM,oBAAoBA,CAACC,gBAAgB,EAAE;IAC3C,IAAI;MACF,MAAMjC,EAAE,CAACuB,SAAS,CAAC,IAAI,CAACZ,oBAAoB,EAAEa,IAAI,CAACC,SAAS,CAACQ,gBAAgB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC1F,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,gCAAgC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK;MAAQ,CAAC,CAAC;MACxE,MAAML,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMa,qBAAqBA,CAACC,UAAU,EAAEC,IAAI,EAAE;IAC5C,IAAI;MACFhC,MAAM,CAACiC,IAAI,CAAC,4BAA4B,EAAE;QAAEF,UAAU;QAAEC;MAAK,CAAC,CAAC;;MAE/D;MACA,MAAME,QAAQ,GAAG,MAAM,IAAI,CAACX,YAAY,CAAC,CAAC;;MAE1C;MACA,MAAMf,YAAY,GAAG0B,QAAQ,CAACF,IAAI,CAAC,IAAI,IAAI,CAACxB,YAAY,CAACwB,IAAI,CAAC,IAAI,EAAE;MAEpE,IAAIxB,YAAY,CAAC2B,MAAM,KAAK,CAAC,EAAE;QAC7BnC,MAAM,CAACoC,IAAI,CAAC,4BAA4B,EAAE;UAAEJ;QAAK,CAAC,CAAC;MACrD;;MAEA;MACA,MAAMH,gBAAgB,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;MAE1D;MACAE,gBAAgB,CAACE,UAAU,CAAC,GAAG;QAC7BC,IAAI;QACJE,QAAQ,EAAE1B,YAAY;QACtB6B,MAAM,EAAE,QAAQ;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACZ,oBAAoB,CAACC,gBAAgB,CAAC;MAEjD7B,MAAM,CAACiC,IAAI,CAAC,2BAA2B,EAAE;QAAEF,UAAU;QAAEC,IAAI;QAAES,YAAY,EAAEjC,YAAY,CAAC2B;MAAO,CAAC,CAAC;MAEjG,OAAO3B,YAAY;IACrB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,kCAAkC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK,OAAO;QAAES,UAAU;QAAEC;MAAK,CAAC,CAAC;MAC5F,MAAMf,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMyB,eAAeA,CAACX,UAAU,EAAE;IAChC,IAAI;MACF/B,MAAM,CAACiC,IAAI,CAAC,oBAAoB,EAAE;QAAEF;MAAW,CAAC,CAAC;;MAEjD;MACA,MAAMF,gBAAgB,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;MAE1D;MACA,IAAI,CAACE,gBAAgB,CAACE,UAAU,CAAC,EAAE;QACjC/B,MAAM,CAACoC,IAAI,CAAC,gCAAgC,EAAE;UAAEL;QAAW,CAAC,CAAC;QAC7D;MACF;;MAEA;MACAF,gBAAgB,CAACE,UAAU,CAAC,GAAG;QAC7B,GAAGF,gBAAgB,CAACE,UAAU,CAAC;QAC/BM,MAAM,EAAE,UAAU;QAClBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCG,UAAU,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACrC,CAAC;;MAED;MACA,MAAM,IAAI,CAACZ,oBAAoB,CAACC,gBAAgB,CAAC;MAEjD7B,MAAM,CAACiC,IAAI,CAAC,mBAAmB,EAAE;QAAEF;MAAW,CAAC,CAAC;IAClD,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,0BAA0B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK,OAAO;QAAES;MAAW,CAAC,CAAC;MAC9E,MAAMd,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM2B,eAAeA,CAACb,UAAU,EAAE;IAChC,IAAI;MACF/B,MAAM,CAACiC,IAAI,CAAC,qBAAqB,EAAE;QAAEF;MAAW,CAAC,CAAC;;MAElD;MACA,MAAMF,gBAAgB,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;MAE1D;MACA,IAAI,CAACE,gBAAgB,CAACE,UAAU,CAAC,EAAE;QACjC/B,MAAM,CAACoC,IAAI,CAAC,gCAAgC,EAAE;UAAEL;QAAW,CAAC,CAAC;QAC7D;MACF;;MAEA;MACAF,gBAAgB,CAACE,UAAU,CAAC,GAAG;QAC7B,GAAGF,gBAAgB,CAACE,UAAU,CAAC;QAC/BM,MAAM,EAAE,WAAW;QACnBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCK,WAAW,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACtC,CAAC;;MAED;MACA,MAAM,IAAI,CAACZ,oBAAoB,CAACC,gBAAgB,CAAC;MAEjD7B,MAAM,CAACiC,IAAI,CAAC,oBAAoB,EAAE;QAAEF;MAAW,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,2BAA2B,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK,OAAO;QAAES;MAAW,CAAC,CAAC;MAC/E,MAAMd,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM6B,gBAAgBA,CAACf,UAAU,EAAEgB,WAAW,EAAE;IAC9C,IAAI;MACF;MACA,MAAMlB,gBAAgB,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;MAE1D;MACA,IAAI,CAACE,gBAAgB,CAACE,UAAU,CAAC,EAAE;QACjC/B,MAAM,CAACgD,KAAK,CAAC,gCAAgC,EAAE;UAAEjB;QAAW,CAAC,CAAC;QAC9D,OAAO,KAAK;MACd;;MAEA;MACA,IAAIF,gBAAgB,CAACE,UAAU,CAAC,CAACM,MAAM,KAAK,QAAQ,EAAE;QACpDrC,MAAM,CAACgD,KAAK,CAAC,kCAAkC,EAAE;UAAEjB,UAAU;UAAEM,MAAM,EAAER,gBAAgB,CAACE,UAAU,CAAC,CAACM;QAAO,CAAC,CAAC;QAC7G,OAAO,KAAK;MACd;;MAEA;MACA,MAAMY,SAAS,GAAGpB,gBAAgB,CAACE,UAAU,CAAC,CAACG,QAAQ,CAACgB,QAAQ,CAACH,WAAW,CAAC;MAE7E,OAAOE,SAAS;IAClB,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,sCAAsC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK,OAAO;QAAES,UAAU;QAAEgB;MAAY,CAAC,CAAC;MACvG,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,MAAMI,mBAAmBA,CAACpB,UAAU,EAAE;IACpC,IAAI;MACF;MACA,MAAMF,gBAAgB,GAAG,MAAM,IAAI,CAACF,oBAAoB,CAAC,CAAC;;MAE1D;MACA,OAAOE,gBAAgB,CAACE,UAAU,CAAC,IAAI;QAAEM,MAAM,EAAE;MAAY,CAAC;IAChE,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdjB,MAAM,CAACiB,KAAK,CAAC,iCAAiC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACK,OAAO;QAAES;MAAW,CAAC,CAAC;MACrF,MAAMd,KAAK;IACb;EACF;AACF;AAEAmC,MAAM,CAACC,OAAO,GAAGpD,cAAc", "ignoreList": []}