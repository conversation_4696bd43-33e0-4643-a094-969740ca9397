generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String    @id @default(cuid())
  name      String?
  email     String?   @unique
  image     String?
  affiliates Affiliate[]
  products  Product[]
  networks  AffiliateNetwork[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model AffiliateNetwork {
  id        String    @id @default(cuid())
  userId    String
  name      String
  logo      String
  apiKey    String
  secretKey String
  status    String
  earnings  Float
  clicks    Int
  conversions Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])
  stats     NetworkStats @relation(fields: [statsId], references: [id])
  statsId   String
}

model NetworkStats {
  id          String   @id @default(cuid())
  networkId   String   @unique
  totalRevenue Float
  totalConversions Int
  clicks      Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Product {
  id        String    @id @default(cuid())
  userId    String
  name      String
  url       String
  price     Float
  image     String
  category  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])
  metrics   ProductMetrics @relation(fields: [metricsId], references: [id])
  metricsId String
}

model ProductMetrics {
  id        String   @id @default(cuid())
  productId String   @unique
  psi       Float
  phi       Float
  kappa     Float
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Conversion {
  id          String   @id @default(cuid())
  userId      String
  productId   String
  networkId   String
  revenue     Float
  status      String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id])
  product     Product  @relation(fields: [productId], references: [id])
  network     AffiliateNetwork @relation(fields: [networkId], references: [id])
  metrics     ConversionMetrics @relation(fields: [metricsId], references: [id])
  metricsId   String
}

model ConversionMetrics {
  id        String   @id @default(cuid())
  conversionId String @unique
  psi          Float
  phi          Float
  kappa        Float
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
}

model Affiliate {
  id          String    @id @default(cuid())
  userId      String
  user        User      @relation(fields: [userId], references: [id])
  name        String
  description String?
  status      String    @default("ACTIVE")
  products    Product[]
  metrics     Metric[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}


model Metric {
  id          String    @id @default(cuid())
  affiliateId String?
  productId   String?
  affiliate   Affiliate? @relation(fields: [affiliateId], references: [id])
  product     Product?  @relation(fields: [productId], references: [id])
  psi         Float     @default(0)
  phi         Float     @default(0)
  kappa       Float     @default(0)
  timestamp   DateTime  @default(now())
  type        String    // 'AFFILIATE' | 'PRODUCT'
  source      String    // 'CONVERSION' | 'ENGAGEMENT' | 'REVENUE'
  value       Float
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
}
