# NovaFuse Universal Platform Security Testing Script

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Text,
        [string]$ForegroundColor = "White"
    )
    
    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Text
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create directories for reports
Write-ColorOutput "Creating directories for security reports..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "./security-reports" | Out-Null
New-Item -ItemType Directory -Force -Path "./performance-reports" | Out-Null
New-Item -ItemType Directory -Force -Path "./test-results" | Out-Null

# Start the Docker environment
Write-ColorOutput "Starting Docker security test environment..." -ForegroundColor Yellow
docker-compose -f docker-compose.security-test.yml up -d novafuse-api mongo redis

# Wait for services to be ready
Write-ColorOutput "Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Run SAST with Semgrep
Write-ColorOutput "Running Static Application Security Testing (SAST) with Semgrep..." -ForegroundColor Yellow
docker-compose -f docker-compose.security-test.yml up semgrep

# Run container scanning with Trivy
Write-ColorOutput "Running container scanning with Trivy..." -ForegroundColor Yellow
docker-compose -f docker-compose.security-test.yml up trivy

# Run DAST with OWASP ZAP
Write-ColorOutput "Running Dynamic Application Security Testing (DAST) with OWASP ZAP..." -ForegroundColor Yellow
docker-compose -f docker-compose.security-test.yml up zap

# Run dependency scanning
Write-ColorOutput "Running dependency scanning..." -ForegroundColor Yellow
npm audit --json > ./security-reports/npm-audit-results.json

# Run security tests
Write-ColorOutput "Running security unit tests..." -ForegroundColor Yellow
docker-compose -f docker-compose.security-test.yml up test-runner

# Generate security report
Write-ColorOutput "Generating security report..." -ForegroundColor Yellow
node tools/generate-security-report.js

# Stop the Docker environment
Write-ColorOutput "Stopping Docker security test environment..." -ForegroundColor Yellow
docker-compose -f docker-compose.security-test.yml down

# Display summary
Write-ColorOutput "Security testing completed!" -ForegroundColor Green
Write-ColorOutput "Reports are available in:" -ForegroundColor Green
Write-ColorOutput "- SAST: ./security-reports/semgrep-results.json" -ForegroundColor Green
Write-ColorOutput "- Container Scanning: ./security-reports/trivy-results.json" -ForegroundColor Green
Write-ColorOutput "- DAST: ./security-reports/zap-report.html" -ForegroundColor Green
Write-ColorOutput "- Dependency Scanning: ./security-reports/npm-audit-results.json" -ForegroundColor Green
Write-ColorOutput "- Security Tests: ./test-results/security-test-results.json" -ForegroundColor Green
Write-ColorOutput "- Security Report: ./security-reports/security-report.html" -ForegroundColor Green
