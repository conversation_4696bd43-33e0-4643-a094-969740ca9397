/**
 * Consent Model
 * 
 * Represents a consent record for data processing activities.
 * This model tracks when and how consent was obtained, for what purposes,
 * and maintains a history of consent changes.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const consentSchema = new Schema({
  dataSubject: {
    identifier: {
      type: String,
      required: true
    },
    identifierType: {
      type: String,
      enum: ['Email', 'UserID', 'CustomerID', 'CookieID', 'Other'],
      required: true
    },
    name: String,
    email: String,
    phone: String
  },
  processingPurposes: [{
    purpose: {
      type: String,
      required: true
    },
    description: String,
    status: {
      type: String,
      enum: ['Granted', 'Denied', 'Withdrawn', 'Expired'],
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  consentSource: {
    type: {
      type: String,
      enum: ['Web Form', 'Mobile App', 'Paper Form', 'Phone', 'Email', 'API', 'Other'],
      required: true
    },
    details: String,
    ipAddress: String,
    userAgent: String,
    formVersion: String
  },
  consentText: {
    version: {
      type: String,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    language: {
      type: String,
      default: 'en'
    }
  },
  proofOfConsent: {
    type: String,
    enum: ['Form Submission', 'Checkbox', 'Signature', 'Double Opt-in', 'Recorded Call', 'Other'],
    required: true
  },
  proofDetails: {
    doubleOptInDate: Date,
    confirmationCode: String,
    signatureImage: String,
    recordingReference: String,
    other: String
  },
  expiryDate: Date,
  active: {
    type: Boolean,
    default: true
  },
  history: [{
    action: {
      type: String,
      enum: ['Created', 'Updated', 'Withdrawn', 'Expired', 'Renewed'],
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    details: String,
    performedBy: {
      type: String,
      enum: ['Data Subject', 'System', 'Administrator'],
      required: true
    },
    ipAddress: String,
    userAgent: String
  }],
  relatedProcessingActivities: [{
    type: Schema.Types.ObjectId,
    ref: 'ProcessingActivity'
  }],
  metadata: {
    region: String,
    country: String,
    regulatoryFramework: {
      type: String,
      enum: ['GDPR', 'CCPA', 'LGPD', 'PIPEDA', 'POPIA', 'Other']
    },
    tags: [String],
    notes: String
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add compound index for data subject and processing purpose
consentSchema.index({
  'dataSubject.identifier': 1,
  'dataSubject.identifierType': 1,
  'processingPurposes.purpose': 1
});

// Add text index for search functionality
consentSchema.index({
  'dataSubject.identifier': 'text',
  'dataSubject.name': 'text',
  'dataSubject.email': 'text',
  'processingPurposes.purpose': 'text'
});

// Method to check if consent is valid for a specific purpose
consentSchema.methods.isValidForPurpose = function(purpose) {
  if (!this.active) return false;
  
  // Check if consent has expired
  if (this.expiryDate && new Date() > this.expiryDate) return false;
  
  // Find the purpose in the processing purposes array
  const purposeEntry = this.processingPurposes.find(p => p.purpose === purpose);
  if (!purposeEntry) return false;
  
  return purposeEntry.status === 'Granted';
};

// Method to withdraw consent for a specific purpose
consentSchema.methods.withdrawConsent = function(purpose, details, performedBy, ipAddress, userAgent) {
  // Find the purpose in the processing purposes array
  const purposeEntry = this.processingPurposes.find(p => p.purpose === purpose);
  if (!purposeEntry) return false;
  
  // Update the purpose status
  purposeEntry.status = 'Withdrawn';
  purposeEntry.timestamp = new Date();
  
  // Add to history
  this.history.push({
    action: 'Withdrawn',
    timestamp: new Date(),
    details: details || `Consent withdrawn for purpose: ${purpose}`,
    performedBy: performedBy || 'Data Subject',
    ipAddress,
    userAgent
  });
  
  // Check if all purposes are withdrawn or denied
  const allWithdrawnOrDenied = this.processingPurposes.every(p => 
    p.status === 'Withdrawn' || p.status === 'Denied' || p.status === 'Expired'
  );
  
  // If all purposes are withdrawn or denied, set active to false
  if (allWithdrawnOrDenied) {
    this.active = false;
  }
  
  return true;
};

const Consent = mongoose.model('Consent', consentSchema);

module.exports = Consent;
