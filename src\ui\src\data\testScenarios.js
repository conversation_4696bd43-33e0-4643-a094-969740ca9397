/**
 * Test Scenarios
 * 
 * This file contains test scenarios for the Cyber-Safety visualizations.
 */

/**
 * Test types
 */
export const TEST_TYPES = {
  USABILITY: 'usability',
  PERFORMANCE: 'performance',
  ACCESSIBILITY: 'accessibility',
  COMPREHENSION: 'comprehension'
};

/**
 * Difficulty levels
 */
export const DIFFICULTY_LEVELS = {
  EASY: 'easy',
  MEDIUM: 'medium',
  HARD: 'hard'
};

/**
 * Test scenario for Tri-Domain Tensor visualization
 */
export const triDomainTensorScenario = {
  visualizationType: 'triDomainTensor',
  name: 'Tri-Domain Tensor Visualization Test',
  description: 'This test evaluates the usability and effectiveness of the Tri-Domain Tensor visualization.',
  tasks: [
    {
      id: 'tdt_task_1',
      name: 'Explore the visualization',
      description: 'Take a moment to explore the Tri-Domain Tensor visualization. Rotate and adjust the view to get familiar with it.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 60, // seconds
      successCriteria: 'User has rotated the visualization and adjusted the view.'
    },
    {
      id: 'tdt_task_2',
      name: 'Identify domain health',
      description: 'Identify which domain (GRC, IT, or Cybersecurity) has the highest health score.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 30,
      successCriteria: 'User correctly identifies the domain with the highest health score.'
    },
    {
      id: 'tdt_task_3',
      name: 'Analyze domain connections',
      description: 'Identify the strongest connection between domains and explain what it represents.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies the strongest connection and provides a reasonable explanation.'
    },
    {
      id: 'tdt_task_4',
      name: 'Identify entropy containment',
      description: 'Identify which domain has the lowest entropy containment and explain what that means.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies the domain with the lowest entropy containment and explains its significance.'
    },
    {
      id: 'tdt_task_5',
      name: 'Suggest improvements',
      description: 'Based on the visualization, suggest one area where the organization should focus improvement efforts.',
      difficulty: DIFFICULTY_LEVELS.HARD,
      expectedTime: 90,
      successCriteria: 'User provides a reasonable suggestion based on the visualization data.'
    }
  ]
};

/**
 * Test scenario for Harmony Index visualization
 */
export const harmonyIndexScenario = {
  visualizationType: 'harmonyIndex',
  name: 'Harmony Index Visualization Test',
  description: 'This test evaluates the usability and effectiveness of the Harmony Index visualization.',
  tasks: [
    {
      id: 'hi_task_1',
      name: 'Explore the visualization',
      description: 'Take a moment to explore the Harmony Index visualization. Toggle different views to get familiar with it.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 60,
      successCriteria: 'User has toggled different views and explored the visualization.'
    },
    {
      id: 'hi_task_2',
      name: 'Identify harmony trend',
      description: 'Analyze the harmony trend over time and identify if it is improving, declining, or stable.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 45,
      successCriteria: 'User correctly identifies the harmony trend.'
    },
    {
      id: 'hi_task_3',
      name: 'Compare domain scores',
      description: 'Compare the harmony scores across domains and identify which domain has the lowest score.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 45,
      successCriteria: 'User correctly identifies the domain with the lowest harmony score.'
    },
    {
      id: 'hi_task_4',
      name: 'Analyze cross-domain harmony',
      description: 'Identify which pair of domains has the lowest cross-domain harmony and explain what that means.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies the domain pair with the lowest harmony and explains its significance.'
    },
    {
      id: 'hi_task_5',
      name: 'Identify resonance factors',
      description: 'Analyze the resonance factors and identify which factor contributes most to the current harmony index.',
      difficulty: DIFFICULTY_LEVELS.HARD,
      expectedTime: 75,
      successCriteria: 'User correctly identifies the most significant resonance factor.'
    }
  ]
};

/**
 * Test scenario for Risk-Control Fusion visualization
 */
export const riskControlFusionScenario = {
  visualizationType: 'riskControlFusion',
  name: 'Risk-Control Fusion Visualization Test',
  description: 'This test evaluates the usability and effectiveness of the Risk-Control Fusion visualization.',
  tasks: [
    {
      id: 'rcf_task_1',
      name: 'Explore the visualization',
      description: 'Take a moment to explore the Risk-Control Fusion visualization. Toggle different views to get familiar with it.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 60,
      successCriteria: 'User has toggled different views and explored the visualization.'
    },
    {
      id: 'rcf_task_2',
      name: 'Identify highest risk area',
      description: 'Identify which domain and category has the highest risk score.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 45,
      successCriteria: 'User correctly identifies the domain and category with the highest risk score.'
    },
    {
      id: 'rcf_task_3',
      name: 'Identify strongest control',
      description: 'Identify which domain and category has the strongest control implementation.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 45,
      successCriteria: 'User correctly identifies the domain and category with the strongest control.'
    },
    {
      id: 'rcf_task_4',
      name: 'Analyze control gaps',
      description: 'Identify the largest gap between risk and control, and explain what that means for the organization.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies the largest gap and explains its significance.'
    },
    {
      id: 'rcf_task_5',
      name: 'Recommend control improvements',
      description: 'Based on the gap analysis, recommend where the organization should focus control improvement efforts.',
      difficulty: DIFFICULTY_LEVELS.HARD,
      expectedTime: 75,
      successCriteria: 'User provides a reasonable recommendation based on the gap analysis.'
    }
  ]
};

/**
 * Test scenario for Resonance Spectrogram visualization
 */
export const resonanceSpectrogramScenario = {
  visualizationType: 'resonanceSpectrogram',
  name: 'Resonance Spectrogram Visualization Test',
  description: 'This test evaluates the usability and effectiveness of the Resonance Spectrogram visualization.',
  tasks: [
    {
      id: 'rs_task_1',
      name: 'Explore the visualization',
      description: 'Take a moment to explore the Resonance Spectrogram visualization. Adjust the time range to get familiar with it.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 60,
      successCriteria: 'User has adjusted the time range and explored the visualization.'
    },
    {
      id: 'rs_task_2',
      name: 'Identify domain frequencies',
      description: 'Identify which domain has the highest frequency and explain what that means.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 45,
      successCriteria: 'User correctly identifies the domain with the highest frequency and explains its significance.'
    },
    {
      id: 'rs_task_3',
      name: 'Analyze cross-domain flows',
      description: 'Identify which cross-domain flow is strongest and explain what that represents.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies the strongest cross-domain flow and explains its significance.'
    },
    {
      id: 'rs_task_4',
      name: 'Identify critical points',
      description: 'Locate critical points in the spectrogram and explain what they represent.',
      difficulty: DIFFICULTY_LEVELS.HARD,
      expectedTime: 75,
      successCriteria: 'User correctly identifies critical points and explains their significance.'
    },
    {
      id: 'rs_task_5',
      name: 'Predict future dissonance',
      description: 'Based on the spectrogram, predict where future dissonance might occur and explain your reasoning.',
      difficulty: DIFFICULTY_LEVELS.HARD,
      expectedTime: 90,
      successCriteria: 'User provides a reasonable prediction based on the spectrogram data.'
    }
  ]
};

/**
 * Test scenario for Unified Compliance-Security visualization
 */
export const unifiedComplianceSecurityScenario = {
  visualizationType: 'unifiedComplianceSecurity',
  name: 'Unified Compliance-Security Visualization Test',
  description: 'This test evaluates the usability and effectiveness of the Unified Compliance-Security visualization.',
  tasks: [
    {
      id: 'ucs_task_1',
      name: 'Explore the visualization',
      description: 'Take a moment to explore the Unified Compliance-Security visualization. Toggle different views to get familiar with it.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 60,
      successCriteria: 'User has toggled different views and explored the visualization.'
    },
    {
      id: 'ucs_task_2',
      name: 'Identify most important requirement',
      description: 'Identify which compliance requirement has the highest importance rating.',
      difficulty: DIFFICULTY_LEVELS.EASY,
      expectedTime: 45,
      successCriteria: 'User correctly identifies the most important compliance requirement.'
    },
    {
      id: 'ucs_task_3',
      name: 'Analyze control effectiveness',
      description: 'Identify which control has the highest effectiveness rating and which requirement it supports.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies the most effective control and the requirement it supports.'
    },
    {
      id: 'ucs_task_4',
      name: 'Identify implementation gaps',
      description: 'Identify which implementations have gaps and explain what those gaps mean for compliance.',
      difficulty: DIFFICULTY_LEVELS.MEDIUM,
      expectedTime: 60,
      successCriteria: 'User correctly identifies implementation gaps and explains their significance.'
    },
    {
      id: 'ucs_task_5',
      name: 'Recommend compliance improvements',
      description: 'Based on the impact analysis, recommend where the organization should focus compliance improvement efforts.',
      difficulty: DIFFICULTY_LEVELS.HARD,
      expectedTime: 75,
      successCriteria: 'User provides a reasonable recommendation based on the impact analysis.'
    }
  ]
};

/**
 * All test scenarios
 */
export const allTestScenarios = {
  triDomainTensor: triDomainTensorScenario,
  harmonyIndex: harmonyIndexScenario,
  riskControlFusion: riskControlFusionScenario,
  resonanceSpectrogram: resonanceSpectrogramScenario,
  unifiedComplianceSecurity: unifiedComplianceSecurityScenario
};

/**
 * Get test scenario for visualization type
 * @param {string} visualizationType - Visualization type
 * @param {string} testType - Test type
 * @returns {Object} - Test scenario
 */
export const getTestScenario = (visualizationType, testType = TEST_TYPES.USABILITY) => {
  const scenario = allTestScenarios[visualizationType];
  
  if (!scenario) {
    throw new Error(`No test scenario found for visualization type: ${visualizationType}`);
  }
  
  // Clone scenario to avoid modifying the original
  const clonedScenario = JSON.parse(JSON.stringify(scenario));
  
  // Add test type
  clonedScenario.testType = testType;
  
  return clonedScenario;
};
