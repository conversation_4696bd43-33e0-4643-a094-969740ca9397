/**
 * NovaConnect UAC Compliance Utilities
 * 
 * This module provides utilities for compliance automation, including
 * SOC2 evidence collection and reporting.
 */

const { SecurityCenterClient } = require('@google-cloud/security-center');
const { BigQuery } = require('@google-cloud/bigquery');
const cron = require('node-cron');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../config/logger');

// Initialize clients
let securityCenter;
let bigquery;

try {
  securityCenter = new SecurityCenterClient();
  bigquery = new BigQuery({
    projectId: process.env.GCP_PROJECT_ID
  });
} catch (error) {
  logger.error('Error initializing Google Cloud clients:', error);
}

/**
 * Collect SOC2 evidence from Google Cloud Security Center
 */
async function collectSOC2Evidence() {
  try {
    logger.info('Starting SOC2 evidence collection');
    
    if (!securityCenter || !bigquery) {
      logger.error('Google Cloud clients not initialized');
      return;
    }
    
    const formattedParent = securityCenter.projectPath(process.env.GCP_PROJECT_ID);
    
    // List findings related to SOC2 requirements
    const [findings] = await securityCenter.listFindings({
      parent: formattedParent,
      filter: 'category="soc2_requirement" OR category="compliance"'
    });
    
    logger.info(`Found ${findings.length} SOC2-related findings`);
    
    // Transform findings for BigQuery
    const rows = findings.map(finding => ({
      finding_id: finding.name,
      category: finding.category,
      resource_name: finding.resourceName,
      severity: finding.severity,
      event_time: finding.eventTime,
      source_properties: JSON.stringify(finding.sourceProperties),
      security_marks: JSON.stringify(finding.securityMarks),
      collected_at: new Date().toISOString()
    }));
    
    // Insert into BigQuery
    if (rows.length > 0) {
      const dataset = bigquery.dataset('compliance');
      const table = dataset.table('soc2_evidence');
      
      await table.insert(rows);
      logger.info(`Inserted ${rows.length} findings into BigQuery`);
    }
    
    // Also save locally for backup
    await saveLocalEvidence(rows);
    
    return {
      status: 'success',
      count: rows.length,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Error collecting SOC2 evidence:', error);
    throw error;
  }
}

/**
 * Save evidence locally as backup
 */
async function saveLocalEvidence(findings) {
  try {
    const dataDir = path.join(__dirname, '../data/compliance');
    await fs.mkdir(dataDir, { recursive: true });
    
    const filename = path.join(dataDir, `soc2_evidence_${new Date().toISOString().replace(/:/g, '-')}.json`);
    await fs.writeFile(filename, JSON.stringify(findings, null, 2));
    
    logger.info(`Saved evidence backup to ${filename}`);
  } catch (error) {
    logger.error('Error saving local evidence:', error);
  }
}

/**
 * Generate SOC2 compliance report
 */
async function generateSOC2Report() {
  try {
    logger.info('Generating SOC2 compliance report');
    
    if (!bigquery) {
      logger.error('BigQuery client not initialized');
      return;
    }
    
    // Query for compliance status
    const query = `
      SELECT
        category,
        severity,
        COUNT(*) as finding_count,
        COUNTIF(severity = 'HIGH') as high_severity_count,
        COUNTIF(severity = 'MEDIUM') as medium_severity_count,
        COUNTIF(severity = 'LOW') as low_severity_count
      FROM
        \`${process.env.GCP_PROJECT_ID}.compliance.soc2_evidence\`
      WHERE
        collected_at > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
      GROUP BY
        category, severity
      ORDER BY
        category, severity
    `;
    
    const [rows] = await bigquery.query(query);
    
    // Calculate compliance score
    const totalFindings = rows.reduce((sum, row) => sum + row.finding_count, 0);
    const weightedScore = rows.reduce((score, row) => {
      const severityWeight = row.severity === 'HIGH' ? 5 : (row.severity === 'MEDIUM' ? 3 : 1);
      return score - (row.finding_count * severityWeight);
    }, 100);
    
    const complianceScore = Math.max(0, Math.min(100, weightedScore));
    
    const report = {
      timestamp: new Date().toISOString(),
      compliance_score: complianceScore,
      total_findings: totalFindings,
      findings_by_category: rows,
      status: complianceScore >= 80 ? 'COMPLIANT' : 'NON_COMPLIANT'
    };
    
    // Save report
    const dataDir = path.join(__dirname, '../data/compliance/reports');
    await fs.mkdir(dataDir, { recursive: true });
    
    const filename = path.join(dataDir, `soc2_report_${new Date().toISOString().replace(/:/g, '-')}.json`);
    await fs.writeFile(filename, JSON.stringify(report, null, 2));
    
    logger.info(`Generated SOC2 report with compliance score: ${complianceScore}`);
    
    return report;
  } catch (error) {
    logger.error('Error generating SOC2 report:', error);
    throw error;
  }
}

/**
 * Schedule compliance tasks
 */
function scheduleComplianceTasks() {
  // Collect evidence daily at 3 AM
  cron.schedule('0 3 * * *', async () => {
    try {
      await collectSOC2Evidence();
    } catch (error) {
      logger.error('Scheduled SOC2 evidence collection failed:', error);
    }
  });
  
  // Generate report weekly on Sunday at 4 AM
  cron.schedule('0 4 * * 0', async () => {
    try {
      await generateSOC2Report();
    } catch (error) {
      logger.error('Scheduled SOC2 report generation failed:', error);
    }
  });
  
  logger.info('Scheduled compliance tasks');
}

// Export functions
module.exports = {
  collectSOC2Evidence,
  generateSOC2Report,
  scheduleComplianceTasks
};
