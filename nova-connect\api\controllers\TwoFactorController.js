/**
 * Two-Factor Authentication Controller
 *
 * This controller handles API requests related to two-factor authentication.
 */

const TwoFactorService = require('../services/TwoFactorService');
const AuthService = require('../services/AuthService');
const AuthAuditService = require('../services/AuthAuditService');
const { ValidationError, AuthenticationError } = require('../utils/errors');

class TwoFactorController {
  constructor() {
    this.twoFactorService = new TwoFactorService();
    this.authService = new AuthService();
    this.authAuditService = new AuthAuditService();
  }

  /**
   * Generate a new 2FA secret for a user
   */
  async generateSecret(req, res, next) {
    try {
      // User should be authenticated
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const userId = req.user.id;
      const username = req.user.username;

      // Check if 2FA is already enabled
      const isEnabled = await this.twoFactorService.isEnabled(userId);

      if (isEnabled) {
        throw new ValidationError('Two-factor authentication is already enabled for this user');
      }

      // Generate secret
      const result = await this.twoFactorService.generateSecret(userId, username);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify a token and enable 2FA for a user
   */
  async verifyAndEnable(req, res, next) {
    try {
      // User should be authenticated
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const userId = req.user.id;
      const username = req.user.username;
      const { token } = req.body;

      if (!token) {
        throw new ValidationError('Token is required');
      }

      try {
        // Verify token and enable 2FA
        await this.twoFactorService.verifyAndEnable(userId, token);

        // Log successful 2FA setup
        await this.authAuditService.logTwoFactorAuth({
          userId,
          username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          action: 'setup',
          success: true
        });

        res.json({
          success: true,
          message: 'Two-factor authentication enabled successfully'
        });
      } catch (error) {
        // Log failed 2FA setup
        await this.authAuditService.logTwoFactorAuth({
          userId,
          username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          action: 'setup',
          success: false,
          reason: error.message
        });

        throw error;
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify a token for login
   */
  async verifyToken(req, res, next) {
    try {
      const { userId, token } = req.body;

      if (!userId) {
        throw new ValidationError('User ID is required');
      }

      if (!token) {
        throw new ValidationError('Token is required');
      }

      try {
        // Verify token
        await this.twoFactorService.verifyToken(userId, token);

        // Get user
        const user = await this.authService.getUserById(userId);

        // Generate tokens
        const accessToken = this.authService.generateToken(user);
        const refreshToken = this.authService.generateRefreshToken(user);

        // Save tokens
        await this.authService.saveToken(accessToken, userId, 'access');
        await this.authService.saveToken(refreshToken, userId, 'refresh');

        // Log successful 2FA verification
        await this.authAuditService.logTwoFactorAuth({
          userId,
          username: user.username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          action: 'verify',
          success: true
        });

        res.json({
          success: true,
          token: accessToken,
          refreshToken,
          user,
          expiresIn: this.authService.jwtExpiresIn
        });
      } catch (error) {
        // Log failed 2FA verification
        await this.authAuditService.logTwoFactorAuth({
          userId,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          action: 'verify',
          success: false,
          reason: error.message
        });

        throw error;
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Disable 2FA for a user
   */
  async disable(req, res, next) {
    try {
      // User should be authenticated
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const userId = req.user.id;
      const username = req.user.username;

      try {
        // Disable 2FA
        await this.twoFactorService.disable(userId);

        // Log successful 2FA disable
        await this.authAuditService.logTwoFactorAuth({
          userId,
          username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          action: 'disable',
          success: true
        });

        res.json({
          success: true,
          message: 'Two-factor authentication disabled successfully'
        });
      } catch (error) {
        // Log failed 2FA disable
        await this.authAuditService.logTwoFactorAuth({
          userId,
          username,
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          action: 'disable',
          success: false,
          reason: error.message
        });

        throw error;
      }
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if 2FA is enabled for a user
   */
  async isEnabled(req, res, next) {
    try {
      // User should be authenticated
      if (!req.user) {
        throw new AuthenticationError('Authentication required');
      }

      const userId = req.user.id;

      // Check if 2FA is enabled
      const isEnabled = await this.twoFactorService.isEnabled(userId);

      res.json({
        success: true,
        enabled: isEnabled
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new TwoFactorController();
