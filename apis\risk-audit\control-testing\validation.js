const Joi = require('joi');

// Validation schemas
const schemas = {
  createControl: Joi.object({
    title: Joi.string().required().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().required().valid('preventive', 'detective', 'corrective', 'directive'),
    category: Joi.string().required().valid('administrative', 'technical', 'physical'),
    status: Joi.string().required().valid('draft', 'implemented', 'under-review', 'approved', 'deprecated'),
    owner: Joi.string().required().max(100),
    framework: Joi.string().optional().max(100),
    riskLevel: Joi.string().optional().valid('critical', 'high', 'medium', 'low'),
    testFrequency: Joi.string().required().valid('daily', 'weekly', 'monthly', 'quarterly', 'semi-annually', 'annually', 'as-needed'),
    testProcedure: Joi.string().optional().max(1000)
  }),
  
  updateControl: Joi.object({
    title: Joi.string().optional().min(3).max(100),
    description: Joi.string().optional().max(500),
    type: Joi.string().optional().valid('preventive', 'detective', 'corrective', 'directive'),
    category: Joi.string().optional().valid('administrative', 'technical', 'physical'),
    status: Joi.string().optional().valid('draft', 'implemented', 'under-review', 'approved', 'deprecated'),
    owner: Joi.string().optional().max(100),
    framework: Joi.string().optional().max(100),
    riskLevel: Joi.string().optional().valid('critical', 'high', 'medium', 'low'),
    testFrequency: Joi.string().optional().valid('daily', 'weekly', 'monthly', 'quarterly', 'semi-annually', 'annually', 'as-needed'),
    testProcedure: Joi.string().optional().max(1000)
  }).min(1), // At least one field must be provided
  
  createTestResult: Joi.object({
    testDate: Joi.string().required().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    tester: Joi.string().required().max(100),
    result: Joi.string().required().valid('pass', 'fail', 'inconclusive', 'not-applicable'),
    evidence: Joi.string().optional().max(500),
    notes: Joi.string().optional().max(500),
    remediation: Joi.string().optional().max(500),
    remediationDueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    remediationStatus: Joi.string().optional().valid('not-required', 'pending', 'in-progress', 'completed', 'verified')
  }),
  
  updateTestResult: Joi.object({
    testDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    tester: Joi.string().optional().max(100),
    result: Joi.string().optional().valid('pass', 'fail', 'inconclusive', 'not-applicable'),
    evidence: Joi.string().optional().max(500),
    notes: Joi.string().optional().max(500),
    remediation: Joi.string().optional().max(500),
    remediationDueDate: Joi.string().optional().pattern(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD format
    remediationStatus: Joi.string().optional().valid('not-required', 'pending', 'in-progress', 'completed', 'verified')
  }).min(1) // At least one field must be provided
};

/**
 * Middleware to validate request data against a schema
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Function} Express middleware function
 */
const validateRequest = (schemaName) => {
  return (req, res, next) => {
    const schema = schemas[schemaName];
    
    if (!schema) {
      return res.status(500).json({
        error: 'Internal Server Error',
        message: `Validation schema '${schemaName}' not found`
      });
    }
    
    const { error, value } = schema.validate(req.body, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true // Remove unknown fields
    });
    
    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      return res.status(400).json({
        error: 'Bad Request',
        message: errorMessage
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
};

module.exports = {
  validateRequest
};
