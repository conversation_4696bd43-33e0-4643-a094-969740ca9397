version: '3.8'

services:
  # MongoDB for data storage
  mongodb:
    image: mongo:latest
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    healthcheck:
      test: ["C<PERSON>", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Redis for caching and pub/sub
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # NovaConnect API
  nova-connect:
    build:
      context: ./
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=test
      - PORT=3001
      - MONGODB_URI=mongodb://mongodb:27017/nova-connect-test
      - REDIS_URI=redis://redis:6379
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:3001/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # GCP Service Simulators
  gcp-simulators:
    build:
      context: ./gcp-simulation
      dockerfile: Dockerfile
    ports:
      - "8081:8081" # SCC
      - "8082:8082" # IAM
      - "8083:8083" # BigQuery
      - "8084:8084" # Cloud Storage
      - "8085:8085" # Cloud Functions
      - "8086:8086" # Cloud Monitoring
    environment:
      - NODE_ENV=test
    healthcheck:
      test: ["CMD", "wget", "--spider", "http://localhost:8081/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Test Runner
  test-runner:
    build:
      context: ./
      dockerfile: Dockerfile.test
    volumes:
      - ./:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - NOVA_CONNECT_URL=http://nova-connect:3001
      - GCP_SIMULATORS_URL=http://gcp-simulators
      - SCC_PORT=8081
      - IAM_PORT=8082
      - BIGQUERY_PORT=8083
      - CLOUD_STORAGE_PORT=8084
      - CLOUD_FUNCTIONS_PORT=8085
      - CLOUD_MONITORING_PORT=8086
    depends_on:
      nova-connect:
        condition: service_healthy
      gcp-simulators:
        condition: service_healthy
    command: ["node", "run-novaconnect-tests.js"]

volumes:
  mongodb_data:
