# Universal Compliance Training Optimizer (UCTO Training)

The Universal Compliance Training Optimizer (UCTO Training) is a framework for creating, managing, and optimizing compliance training programs.

## Overview

The UCTO Training provides a flexible and extensible framework for managing compliance training programs, tracking learner progress, and optimizing training effectiveness. It helps organizations ensure that their employees receive appropriate compliance training and can demonstrate compliance with regulatory requirements.

## Key Features

- **Training Program Management**: Create and manage compliance training programs
- **Learner Management**: Track learners and their progress through training programs
- **Content Management**: Organize and deliver training content
- **Assessment Management**: Create and manage assessments to evaluate learner understanding
- **Optimization**: Optimize training programs based on learner performance and feedback

## Architecture

The UCTO Training consists of several core components:

- **Training Manager**: Manages compliance training programs
- **Learner Manager**: Manages learners and their progress
- **Content Manager**: Manages training content
- **Assessment Manager**: Manages assessments

## Usage

Here's a simple example of how to use the UCTO Training:

```python
from ucto_training import TrainingManager, LearnerManager

# Initialize the managers
training_manager = TrainingManager()
learner_manager = LearnerManager()

# Create a training program
program = training_manager.create_program({
    'name': 'GDPR Compliance Training',
    'description': 'Training program for GDPR compliance',
    'framework': 'GDPR',
    'target_audience': ['all_employees'],
    'status': 'active'
})

# Add a module to the program
training_manager.add_module_to_program(program['id'], {
    'name': 'Introduction to GDPR',
    'description': 'Overview of GDPR principles and requirements',
    'content_type': 'text',
    'content': 'The General Data Protection Regulation (GDPR) is a regulation in EU law...',
    'duration': 30,
    'order': 1
})

# Create a learner
learner = learner_manager.create_learner({
    'name': 'John Doe',
    'email': '<EMAIL>',
    'role': 'employee',
    'department': 'IT'
})

# Record learner progress
progress = learner_manager.record_progress({
    'learner_id': learner['id'],
    'program_id': program['id'],
    'module_id': program['modules'][0]['id'],
    'status': 'completed',
    'score': 90,
    'completed_at': '2023-10-15T14:30:00Z'
})

# Get learner progress
learner_progress = learner_manager.get_learner_progress(learner['id'])

# Get program progress
program_progress = learner_manager.get_program_progress(program['id'])
```

## Extending the Framework

### Adding a Custom Training Program Type

```python
from ucto_training import TrainingManager

# Initialize the Training Manager
training_manager = TrainingManager()

# Create a custom training program
custom_program = training_manager.create_program({
    'name': 'Custom Compliance Training',
    'description': 'Custom training program for compliance',
    'framework': 'custom',
    'target_audience': ['specific_roles'],
    'status': 'active',
    'custom_field': 'Custom value'  # Add custom fields as needed
})
```

### Adding a Custom Learner Type

```python
from ucto_training import LearnerManager

# Initialize the Learner Manager
learner_manager = LearnerManager()

# Create a custom learner
custom_learner = learner_manager.create_learner({
    'name': 'Jane Smith',
    'email': '<EMAIL>',
    'role': 'custom_role',
    'department': 'custom_department',
    'custom_field': 'Custom value'  # Add custom fields as needed
})
```

### Adding a Custom Progress Tracking Method

```python
from ucto_training import LearnerManager

# Initialize the Learner Manager
learner_manager = LearnerManager()

# Record custom progress
custom_progress = learner_manager.record_progress({
    'learner_id': 'learner_id',
    'program_id': 'program_id',
    'module_id': 'module_id',
    'status': 'custom_status',
    'score': 95,
    'completed_at': '2023-10-15T14:30:00Z',
    'custom_field': 'Custom value'  # Add custom fields as needed
})
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
