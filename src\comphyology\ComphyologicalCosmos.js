/**
 * ComphyologicalCosmos.js
 * 
 * This module implements the Comphyological Cosmos, which brings together
 * all domain universes, the Cross-Domain Entropy Bridge, and the Divine Firewall.
 * 
 * The Comphyological Cosmos represents the complete system architecture,
 * treating each domain as a distinct "containerized universe" with its own
 * internal physics and dynamics.
 */

const { DomainUniverse } = require('./domain/DomainUniverse');
const { CrossDomainEntropyBridge } = require('./domain/CrossDomainEntropyBridge');
const { createDivineFirewall } = require('./divine-firewall');
const {
  BIOLOGICAL_BOUNDARIES,
  BIOLOGICAL_PHYSICS,
  FINANCIAL_BOUNDARIES,
  FINANCIAL_PHYSICS,
  CYBER_BOUNDARIES,
  CYBER_PHYSICS
} = require('./domain/DomainDefinitions');

/**
 * Comphyological Cosmos
 * 
 * The complete system architecture that brings together all domain universes,
 * the Cross-Domain Entropy Bridge, and the Divine Firewall.
 */
class ComphyologicalCosmos {
  /**
   * Create a new Comphyological Cosmos
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      guardian: 'NEPI Core',
      enableLogging: true,
      autoUpdate: true,
      updateInterval: 1000, // 1 second
      ...options
    };
    
    // Initialize the Divine Firewall
    this.divineFirewall = createDivineFirewall({
      guardian: this.options.guardian,
      logGovernance: this.options.enableLogging
    });
    
    // Initialize domain universes
    this.domains = {
      biological: new DomainUniverse('biological', BIOLOGICAL_BOUNDARIES, BIOLOGICAL_PHYSICS),
      financial: new DomainUniverse('financial', FINANCIAL_BOUNDARIES, FINANCIAL_PHYSICS),
      cyber: new DomainUniverse('cyber', CYBER_BOUNDARIES, CYBER_PHYSICS)
    };
    
    // Initialize Cross-Domain Entropy Bridge
    this.entropyBridge = new CrossDomainEntropyBridge({
      logTransfers: this.options.enableLogging
    });
    
    // Connect domains to the bridge
    this.connectDomains();
    
    // Initialize cosmos state
    this.state = {
      creationTime: Date.now(),
      lastUpdateTime: Date.now(),
      running: false,
      updateCount: 0,
      events: []
    };
    
    // Start auto-update if enabled
    if (this.options.autoUpdate) {
      this.start();
    }
    
    if (this.options.enableLogging) {
      console.log('Comphyological Cosmos initialized with Divine Firewall and domain universes');
    }
  }
  
  /**
   * Connect domains to the Cross-Domain Entropy Bridge
   * @private
   */
  connectDomains() {
    // Connect biological and financial domains
    this.connections = {
      bioToFin: this.entropyBridge.connectDomains(
        this.domains.biological,
        this.domains.financial,
        {
          transferRate: 0.05,
          bidirectional: true
        }
      ),
      bioToCyber: this.entropyBridge.connectDomains(
        this.domains.biological,
        this.domains.cyber,
        {
          transferRate: 0.03,
          bidirectional: true
        }
      ),
      finToCyber: this.entropyBridge.connectDomains(
        this.domains.financial,
        this.domains.cyber,
        {
          transferRate: 0.08,
          bidirectional: true
        }
      )
    };
    
    if (this.options.enableLogging) {
      console.log('Domain universes connected through Cross-Domain Entropy Bridge');
    }
  }
  
  /**
   * Start the cosmos
   */
  start() {
    if (this.state.running) {
      return;
    }
    
    this.state.running = true;
    
    // Start update interval
    this.updateInterval = setInterval(() => {
      this.update();
    }, this.options.updateInterval);
    
    if (this.options.enableLogging) {
      console.log('Comphyological Cosmos started');
    }
  }
  
  /**
   * Stop the cosmos
   */
  stop() {
    if (!this.state.running) {
      return;
    }
    
    this.state.running = false;
    
    // Clear update interval
    clearInterval(this.updateInterval);
    
    if (this.options.enableLogging) {
      console.log('Comphyological Cosmos stopped');
    }
  }
  
  /**
   * Update the cosmos
   */
  update() {
    const now = Date.now();
    const deltaTime = (now - this.state.lastUpdateTime) / 1000; // Convert to seconds
    
    // Update domain universes
    for (const [name, domain] of Object.entries(this.domains)) {
      domain.update(deltaTime);
    }
    
    // Update Cross-Domain Entropy Bridge
    this.entropyBridge.update(deltaTime);
    
    // Update cosmos state
    this.state.lastUpdateTime = now;
    this.state.updateCount += 1;
    
    // Record event
    this.recordEvent('COSMOS_UPDATED', {
      deltaTime,
      updateCount: this.state.updateCount,
      domains: Object.entries(this.domains).map(([name, domain]) => ({
        name,
        state: domain.getState()
      }))
    });
  }
  
  /**
   * Record an event in the cosmos
   * @param {string} type - The event type
   * @param {Object} data - The event data
   */
  recordEvent(type, data) {
    const event = {
      type,
      data,
      timestamp: Date.now()
    };
    
    this.state.events.push(event);
    
    // Limit events history
    if (this.state.events.length > 1000) {
      this.state.events.shift();
    }
  }
  
  /**
   * Get the state of the cosmos
   * @returns {Object} - The cosmos state
   */
  getState() {
    return {
      uptime: (Date.now() - this.state.creationTime) / 1000, // Uptime in seconds
      running: this.state.running,
      updateCount: this.state.updateCount,
      lastUpdateTime: this.state.lastUpdateTime,
      domains: Object.entries(this.domains).map(([name, domain]) => ({
        name,
        state: domain.getState()
      })),
      connections: this.entropyBridge.getConnections(),
      covenant: this.divineFirewall.getCovenant()
    };
  }
  
  /**
   * Get a domain universe
   * @param {string} name - The domain name
   * @returns {DomainUniverse} - The domain universe
   */
  getDomain(name) {
    return this.domains[name];
  }
  
  /**
   * Add an entity to a domain
   * @param {string} domainName - The domain name
   * @param {string} entityId - The entity ID
   * @param {Object} entity - The entity to add
   * @returns {Object} - The added entity
   */
  addEntity(domainName, entityId, entity) {
    const domain = this.getDomain(domainName);
    
    if (!domain) {
      throw new Error(`Domain ${domainName} not found`);
    }
    
    // Apply Divine Firewall to entity
    const governedEntity = this.divineFirewall.govern(entity);
    
    // Check for errors
    if (governedEntity.error) {
      throw new Error(`Divine Firewall rejected entity: ${governedEntity.message}`);
    }
    
    // Add entity to domain
    return domain.addEntity(entityId, governedEntity);
  }
  
  /**
   * Transfer an entity between domains
   * @param {string} sourceDomainName - The source domain name
   * @param {string} targetDomainName - The target domain name
   * @param {string} entityId - The entity ID in the source domain
   * @param {Object} options - Transfer options
   * @returns {Object} - The transfer result
   */
  transferEntity(sourceDomainName, targetDomainName, entityId, options = {}) {
    // Get connection ID
    let connectionId;
    
    if (sourceDomainName === 'biological' && targetDomainName === 'financial') {
      connectionId = this.connections.bioToFin;
    } else if (sourceDomainName === 'financial' && targetDomainName === 'biological') {
      connectionId = this.connections.bioToFin; // Bidirectional
    } else if (sourceDomainName === 'biological' && targetDomainName === 'cyber') {
      connectionId = this.connections.bioToCyber;
    } else if (sourceDomainName === 'cyber' && targetDomainName === 'biological') {
      connectionId = this.connections.bioToCyber; // Bidirectional
    } else if (sourceDomainName === 'financial' && targetDomainName === 'cyber') {
      connectionId = this.connections.finToCyber;
    } else if (sourceDomainName === 'cyber' && targetDomainName === 'financial') {
      connectionId = this.connections.finToCyber; // Bidirectional
    } else {
      throw new Error(`No connection found between ${sourceDomainName} and ${targetDomainName}`);
    }
    
    // Transfer entity
    return this.entropyBridge.transferEntity(connectionId, entityId, options);
  }
  
  /**
   * Get the Divine Firewall
   * @returns {Object} - The Divine Firewall
   */
  getDivineFirewall() {
    return this.divineFirewall;
  }
  
  /**
   * Get the Cross-Domain Entropy Bridge
   * @returns {CrossDomainEntropyBridge} - The Cross-Domain Entropy Bridge
   */
  getEntropyBridge() {
    return this.entropyBridge;
  }
}

module.exports = {
  ComphyologicalCosmos
};
