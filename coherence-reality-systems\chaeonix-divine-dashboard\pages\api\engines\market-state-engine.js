/**
 * CHAEONIX MARKET STATE RECOGNITION ENGINE
 * Adaptive detection of accumulation, distribution, and expansion phases
 * Integrates NEBE cortisol levels and NEFC liquidity analysis
 */

// MARKET PHASE DEFINITIONS
const MARKET_PHASES = {
  ACCUMULATION: {
    name: 'Quiet Accumulation',
    description: 'Smart money building positions',
    volatility_threshold: 0.5,
    cortisol_max: 60,
    volume_pattern: 'declining',
    price_action: 'sideways_compression'
  },
  DISTRIBUTION: {
    name: 'Distribution Trap',
    description: 'False breakouts and institutional selling',
    volatility_threshold: 0.8,
    cortisol_range: [70, 85],
    volume_pattern: 'climactic',
    price_action: 'failed_breakouts'
  },
  EXPANSION: {
    name: 'Trend Expansion',
    description: 'Sustained directional movement',
    volatility_threshold: 1.2,
    cortisol_range: [40, 70],
    volume_pattern: 'confirming',
    price_action: 'trending'
  },
  EXHAUSTION: {
    name: 'Trend Exhaustion',
    description: 'Overextended moves ready for reversal',
    volatility_threshold: 2.0,
    cortisol_max: 95,
    volume_pattern: 'parabolic',
    price_action: 'climactic'
  }
};

class MarketStateEngine {
  constructor() {
    this.current_phase = 'ACCUMULATION';
    this.phase_confidence = 0.75;
    this.phase_duration = 0;
    this.last_phase_change = new Date();
    this.market_data = {};
    this.nebe_cortisol = 65;
    this.nefc_liquidity_gaps = false;
    this.volatility_index = 0.45;
    this.volume_profile = {};
    this.price_action_signals = {};
  }

  // DETECT CURRENT MARKET PHASE
  detect_phase(market_data = {}) {
    this.market_data = market_data;
    this.update_indicators();
    
    const phase_scores = {};
    
    // Calculate scores for each phase
    Object.keys(MARKET_PHASES).forEach(phase => {
      phase_scores[phase] = this.calculate_phase_score(phase);
    });
    
    // Find highest scoring phase
    const best_phase = Object.keys(phase_scores).reduce((a, b) => 
      phase_scores[a] > phase_scores[b] ? a : b
    );
    
    const confidence = phase_scores[best_phase];
    
    // Only change phase if confidence is high enough
    if (confidence > 0.7 && best_phase !== this.current_phase) {
      this.change_phase(best_phase, confidence);
    } else if (best_phase === this.current_phase) {
      this.phase_confidence = Math.max(this.phase_confidence, confidence);
      this.phase_duration += 1;
    }
    
    return {
      phase: this.current_phase,
      confidence: this.phase_confidence,
      duration: this.phase_duration,
      phase_scores: phase_scores,
      indicators: this.get_phase_indicators()
    };
  }

  // CALCULATE PHASE SCORE
  calculate_phase_score(phase) {
    const phase_def = MARKET_PHASES[phase];
    let score = 0;
    let max_score = 0;
    
    // Volatility scoring
    max_score += 25;
    if (phase === 'ACCUMULATION' && this.volatility_index < phase_def.volatility_threshold) {
      score += 25;
    } else if (phase === 'DISTRIBUTION' && this.volatility_index >= 0.6 && this.volatility_index <= phase_def.volatility_threshold) {
      score += 25;
    } else if (phase === 'EXPANSION' && this.volatility_index >= 0.8 && this.volatility_index <= phase_def.volatility_threshold) {
      score += 25;
    } else if (phase === 'EXHAUSTION' && this.volatility_index >= phase_def.volatility_threshold) {
      score += 25;
    }
    
    // NEBE Cortisol scoring
    max_score += 25;
    if (phase === 'ACCUMULATION' && this.nebe_cortisol < phase_def.cortisol_max) {
      score += 25;
    } else if (phase === 'DISTRIBUTION' && this.nebe_cortisol >= phase_def.cortisol_range[0] && this.nebe_cortisol <= phase_def.cortisol_range[1]) {
      score += 25;
    } else if (phase === 'EXPANSION' && this.nebe_cortisol >= phase_def.cortisol_range[0] && this.nebe_cortisol <= phase_def.cortisol_range[1]) {
      score += 25;
    } else if (phase === 'EXHAUSTION' && this.nebe_cortisol >= phase_def.cortisol_max) {
      score += 25;
    }
    
    // NEFC Liquidity gaps scoring
    max_score += 20;
    if (phase === 'DISTRIBUTION' && this.nefc_liquidity_gaps) {
      score += 20;
    } else if (phase !== 'DISTRIBUTION' && !this.nefc_liquidity_gaps) {
      score += 15;
    }
    
    // Volume pattern scoring
    max_score += 15;
    const volume_match = this.check_volume_pattern(phase_def.volume_pattern);
    if (volume_match) score += 15;
    
    // Price action scoring
    max_score += 15;
    const price_match = this.check_price_action(phase_def.price_action);
    if (price_match) score += 15;
    
    return score / max_score;
  }

  // UPDATE INDICATORS
  update_indicators() {
    // Simulate real market data updates
    this.volatility_index = 0.3 + Math.random() * 1.5; // 0.3 to 1.8
    this.nebe_cortisol = 40 + Math.random() * 55; // 40 to 95
    this.nefc_liquidity_gaps = Math.random() > 0.7; // 30% chance of gaps
    
    // Update volume profile
    this.volume_profile = {
      current_volume: Math.random() * 1000000,
      avg_volume: 750000,
      volume_ratio: 0.8 + Math.random() * 0.6 // 0.8 to 1.4
    };
    
    // Update price action signals
    this.price_action_signals = {
      trend_strength: Math.random(),
      breakout_quality: Math.random(),
      support_resistance: Math.random(),
      momentum_divergence: Math.random() > 0.8
    };
  }

  // CHECK VOLUME PATTERN
  check_volume_pattern(expected_pattern) {
    const volume_ratio = this.volume_profile.volume_ratio;
    
    switch (expected_pattern) {
      case 'declining':
        return volume_ratio < 0.9;
      case 'climactic':
        return volume_ratio > 1.3;
      case 'confirming':
        return volume_ratio >= 1.0 && volume_ratio <= 1.3;
      case 'parabolic':
        return volume_ratio > 1.5;
      default:
        return false;
    }
  }

  // CHECK PRICE ACTION
  check_price_action(expected_action) {
    const signals = this.price_action_signals;
    
    switch (expected_action) {
      case 'sideways_compression':
        return signals.trend_strength < 0.4 && !signals.momentum_divergence;
      case 'failed_breakouts':
        return signals.breakout_quality < 0.3 && signals.momentum_divergence;
      case 'trending':
        return signals.trend_strength > 0.6 && signals.breakout_quality > 0.5;
      case 'climactic':
        return signals.trend_strength > 0.8 && signals.momentum_divergence;
      default:
        return false;
    }
  }

  // CHANGE PHASE
  change_phase(new_phase, confidence) {
    console.log(`🔄 Market Phase Change: ${this.current_phase} → ${new_phase} (${(confidence * 100).toFixed(1)}% confidence)`);
    
    this.current_phase = new_phase;
    this.phase_confidence = confidence;
    this.phase_duration = 0;
    this.last_phase_change = new Date();
  }

  // GET PHASE INDICATORS
  get_phase_indicators() {
    return {
      volatility_index: this.volatility_index,
      nebe_cortisol: this.nebe_cortisol,
      nefc_liquidity_gaps: this.nefc_liquidity_gaps,
      volume_profile: this.volume_profile,
      price_action_signals: this.price_action_signals
    };
  }

  // GET TRADING RECOMMENDATIONS
  get_trading_recommendations() {
    const phase = MARKET_PHASES[this.current_phase];
    const recommendations = [];
    
    switch (this.current_phase) {
      case 'ACCUMULATION':
        recommendations.push({
          action: 'ACCUMULATE',
          priority: 'HIGH',
          strategy: 'Scale into positions on weakness',
          risk_level: 'LOW',
          position_size: '0.5x - 1.0x',
          timeframe: 'Long-term holds'
        });
        break;
        
      case 'DISTRIBUTION':
        recommendations.push({
          action: 'AVOID_BREAKOUTS',
          priority: 'CRITICAL',
          strategy: 'Wait for confirmation, avoid FOMO',
          risk_level: 'HIGH',
          position_size: '0.25x - 0.5x',
          timeframe: 'Quick scalps only'
        });
        break;
        
      case 'EXPANSION':
        recommendations.push({
          action: 'TREND_FOLLOW',
          priority: 'HIGH',
          strategy: 'Ride the momentum with stops',
          risk_level: 'MEDIUM',
          position_size: '1.0x - 1.5x',
          timeframe: 'Swing trades'
        });
        break;
        
      case 'EXHAUSTION':
        recommendations.push({
          action: 'PREPARE_REVERSAL',
          priority: 'MEDIUM',
          strategy: 'Look for reversal signals',
          risk_level: 'HIGH',
          position_size: '0.5x - 0.75x',
          timeframe: 'Counter-trend scalps'
        });
        break;
    }
    
    return recommendations;
  }

  // GET CURRENT STATUS
  getCurrentStatus() {
    return {
      current_phase: this.current_phase,
      phase_confidence: this.phase_confidence,
      phase_duration: this.phase_duration,
      phase_description: MARKET_PHASES[this.current_phase].description,
      last_change: this.last_phase_change,
      indicators: this.get_phase_indicators(),
      recommendations: this.get_trading_recommendations()
    };
  }
}

// Export singleton instance
const marketStateEngine = new MarketStateEngine();

export default function handler(req, res) {
  if (req.method === 'GET') {
    const phase_analysis = marketStateEngine.detect_phase();
    const status = marketStateEngine.getCurrentStatus();
    
    res.status(200).json({
      success: true,
      market_state_engine: 'CHAEONIX Adaptive Market Recognition',
      phase_analysis: phase_analysis,
      current_status: status,
      timestamp: new Date().toISOString()
    });
    
  } else if (req.method === 'POST') {
    const { action, market_data } = req.body;
    
    if (action === 'UPDATE_MARKET_DATA') {
      const analysis = marketStateEngine.detect_phase(market_data);
      res.status(200).json({
        success: true,
        message: 'Market data updated',
        phase_analysis: analysis
      });
      
    } else {
      res.status(400).json({ error: 'Invalid action' });
    }
    
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
