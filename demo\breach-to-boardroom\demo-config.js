/**
 * NovaConnect "Breach to Boardroom" Demo Configuration
 * 
 * This script configures the NovaConnect system for the "Breach to Boardroom" demo,
 * setting up the necessary connectors, remediation actions, and test data.
 */

const fs = require('fs');
const path = require('path');
const { BigQuery } = require('@google-cloud/bigquery');
const { SecurityCenterClient } = require('@google-cloud/security-center');
const axios = require('axios');

// Configuration
const config = {
  projectId: process.env.GCP_PROJECT_ID || 'demo-project',
  datasetId: process.env.DATASET_ID || 'patient_records',
  location: process.env.LOCATION || 'US',
  organizationId: process.env.ORGANIZATION_ID || '123456789012',
  novaConnectApiUrl: process.env.NOVA_CONNECT_API_URL || 'http://localhost:3000',
  novaConnectApiKey: process.env.NOVA_CONNECT_API_KEY || 'demo-api-key',
  lookerDashboardId: process.env.LOOKER_DASHBOARD_ID || 'hipaa-compliance-dashboard',
  credentialsPath: process.env.GOOGLE_APPLICATION_CREDENTIALS
};

// Create API client
const apiClient = axios.create({
  baseURL: config.novaConnectApiUrl,
  headers: {
    'Authorization': `Bearer ${config.novaConnectApiKey}`,
    'Content-Type': 'application/json'
  }
});

/**
 * Setup BigQuery dataset with PHI data
 */
async function setupBigQueryDataset() {
  console.log('Setting up BigQuery dataset...');
  
  try {
    // Initialize BigQuery client
    const bigquery = new BigQuery({
      projectId: config.projectId
    });
    
    // Check if dataset exists, create if not
    try {
      await bigquery.dataset(config.datasetId).get();
      console.log(`Dataset ${config.datasetId} already exists`);
    } catch (error) {
      if (error.code === 404) {
        // Create dataset
        const [dataset] = await bigquery.createDataset(config.datasetId, {
          location: config.location
        });
        console.log(`Dataset ${dataset.id} created`);
      } else {
        throw error;
      }
    }
    
    // Get dataset reference
    const dataset = bigquery.dataset(config.datasetId);
    
    // Set public access (for demo purposes - this is the misconfiguration)
    const [metadata] = await dataset.getMetadata();
    metadata.access = [
      ...metadata.access || [],
      { specialGroup: 'allAuthenticatedUsers', role: 'READER' }
    ];
    await dataset.setMetadata(metadata);
    console.log(`Dataset ${config.datasetId} set to public access`);
    
    // Create patient records table if it doesn't exist
    const tableId = 'patient_data';
    try {
      await dataset.table(tableId).get();
      console.log(`Table ${tableId} already exists`);
    } catch (error) {
      if (error.code === 404) {
        // Define schema
        const schema = [
          { name: 'patient_id', type: 'STRING', mode: 'REQUIRED' },
          { name: 'name', type: 'STRING', mode: 'REQUIRED' },
          { name: 'dob', type: 'DATE' },
          { name: 'ssn', type: 'STRING' },
          { name: 'address', type: 'STRING' },
          { name: 'phone', type: 'STRING' },
          { name: 'email', type: 'STRING' },
          { name: 'diagnosis', type: 'STRING' },
          { name: 'treatment', type: 'STRING' },
          { name: 'medication', type: 'STRING' },
          { name: 'insurance_id', type: 'STRING' },
          { name: 'created_at', type: 'TIMESTAMP' },
          { name: 'updated_at', type: 'TIMESTAMP' }
        ];
        
        // Create table
        const [table] = await dataset.createTable(tableId, { schema });
        console.log(`Table ${table.id} created`);
        
        // Insert sample data
        const rows = generateSamplePatientData(10);
        await table.insert(rows);
        console.log(`Inserted ${rows.length} sample patient records`);
      } else {
        throw error;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error setting up BigQuery dataset:', error);
    return false;
  }
}

/**
 * Configure NovaConnect for the demo
 */
async function configureNovaConnect() {
  console.log('Configuring NovaConnect...');
  
  try {
    // Check if NovaConnect API is available
    await apiClient.get('/health');
    
    // Register SCC connector
    await apiClient.post('/api/connectors/register', {
      type: 'scc',
      name: 'Google Security Command Center',
      config: {
        projectId: config.projectId,
        organizationId: config.organizationId
      }
    });
    console.log('Registered SCC connector');
    
    // Register BigQuery connector
    await apiClient.post('/api/connectors/register', {
      type: 'bigquery',
      name: 'Google BigQuery',
      config: {
        projectId: config.projectId
      }
    });
    console.log('Registered BigQuery connector');
    
    // Register Looker connector
    await apiClient.post('/api/connectors/register', {
      type: 'looker',
      name: 'Google Looker',
      config: {
        dashboardId: config.lookerDashboardId
      }
    });
    console.log('Registered Looker connector');
    
    // Configure remediation actions
    await apiClient.post('/api/remediate/actions/register', {
      action: 'encrypt-dataset',
      connector: 'bigquery',
      description: 'Encrypt a BigQuery dataset',
      parameters: [
        { name: 'projectId', type: 'string', required: true },
        { name: 'datasetId', type: 'string', required: true },
        { name: 'encryptionType', type: 'string', required: false, default: 'AES-256' },
        { name: 'keyRotationPeriod', type: 'string', required: false, default: '90d' }
      ]
    });
    console.log('Registered encrypt-dataset action');
    
    await apiClient.post('/api/remediate/actions/register', {
      action: 'update-access-controls',
      connector: 'bigquery',
      description: 'Update access controls for a BigQuery dataset',
      parameters: [
        { name: 'projectId', type: 'string', required: true },
        { name: 'datasetId', type: 'string', required: true },
        { name: 'accessLevel', type: 'string', required: true },
        { name: 'allowedRoles', type: 'array', required: false }
      ]
    });
    console.log('Registered update-access-controls action');
    
    await apiClient.post('/api/remediate/actions/register', {
      action: 'update-compliance-dashboard',
      connector: 'looker',
      description: 'Update compliance dashboard',
      parameters: [
        { name: 'dashboardId', type: 'string', required: true },
        { name: 'findingId', type: 'string', required: true },
        { name: 'remediationId', type: 'string', required: true }
      ]
    });
    console.log('Registered update-compliance-dashboard action');
    
    // Configure remediation workflow
    await apiClient.post('/api/remediate/workflows/register', {
      name: 'hipaa-phi-exposure',
      description: 'Remediate exposed PHI data',
      trigger: {
        source: 'scc',
        conditions: [
          { field: 'category', operator: 'equals', value: 'DATA_LEAK' },
          { field: 'severity', operator: 'equals', value: 'HIGH' },
          { field: 'resourceType', operator: 'equals', value: 'bigquery.dataset' }
        ]
      },
      steps: [
        {
          id: 'step-1',
          action: 'encrypt-dataset',
          parameters: {
            projectId: '{{resource.projectId}}',
            datasetId: '{{resource.name}}',
            encryptionType: 'AES-256',
            keyRotationPeriod: '90d'
          }
        },
        {
          id: 'step-2',
          action: 'update-access-controls',
          parameters: {
            projectId: '{{resource.projectId}}',
            datasetId: '{{resource.name}}',
            accessLevel: 'restricted',
            allowedRoles: ['healthcare-admin', 'compliance-officer']
          }
        },
        {
          id: 'step-3',
          action: 'update-compliance-dashboard',
          parameters: {
            dashboardId: config.lookerDashboardId,
            findingId: '{{finding.id}}',
            remediationId: '{{remediation.id}}'
          }
        }
      ]
    });
    console.log('Registered hipaa-phi-exposure workflow');
    
    return true;
  } catch (error) {
    console.error('Error configuring NovaConnect:', error);
    return false;
  }
}

/**
 * Create a test SCC finding
 */
async function createTestFinding() {
  console.log('Creating test SCC finding...');
  
  try {
    // Check if credentials are available
    if (!config.credentialsPath) {
      console.warn('GOOGLE_APPLICATION_CREDENTIALS not set. Skipping SCC finding creation.');
      return false;
    }
    
    // Initialize SCC client
    const sccClient = new SecurityCenterClient({
      keyFilename: config.credentialsPath
    });
    
    // Create finding
    const [finding] = await sccClient.createFinding({
      parent: `organizations/${config.organizationId}/sources/12345678`,
      findingId: `phi-exposure-${Date.now()}`,
      finding: {
        state: 'ACTIVE',
        category: 'DATA_LEAK',
        severity: 'HIGH',
        resourceName: `//bigquery.googleapis.com/projects/${config.projectId}/datasets/${config.datasetId}`,
        eventTime: { seconds: Math.floor(Date.now() / 1000) },
        sourceProperties: {
          finding_type: 'Sensitive Data Exposure',
          finding_description: 'PHI data exposed in BigQuery dataset',
          data_type: 'PHI',
          compliance_frameworks: 'HIPAA,GDPR'
        }
      }
    });
    
    console.log(`Created SCC finding: ${finding.name}`);
    return true;
  } catch (error) {
    console.error('Error creating SCC finding:', error);
    return false;
  }
}

/**
 * Generate sample patient data
 * @param {number} count - Number of records to generate
 * @returns {Array} - Array of patient records
 */
function generateSamplePatientData(count) {
  const patients = [];
  
  for (let i = 0; i < count; i++) {
    patients.push({
      patient_id: `P${100000 + i}`,
      name: `Patient ${i}`,
      dob: `1970-01-${(i % 28) + 1}`,
      ssn: `123-45-${6789 + i}`,
      address: `${1000 + i} Main St, Anytown, US 12345`,
      phone: `(555) 123-${4567 + i}`,
      email: `patient${i}@example.com`,
      diagnosis: ['Hypertension', 'Diabetes', 'Asthma'][i % 3],
      treatment: ['Medication', 'Surgery', 'Therapy'][i % 3],
      medication: ['Lisinopril', 'Metformin', 'Albuterol'][i % 3],
      insurance_id: `INS${200000 + i}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });
  }
  
  return patients;
}

/**
 * Main function
 */
async function main() {
  console.log('Starting "Breach to Boardroom" demo setup...');
  
  // Setup BigQuery dataset
  const bigquerySetup = await setupBigQueryDataset();
  if (!bigquerySetup) {
    console.error('Failed to setup BigQuery dataset. Exiting.');
    process.exit(1);
  }
  
  // Configure NovaConnect
  const novaConnectSetup = await configureNovaConnect();
  if (!novaConnectSetup) {
    console.error('Failed to configure NovaConnect. Exiting.');
    process.exit(1);
  }
  
  // Create test finding (optional)
  if (process.argv.includes('--create-finding')) {
    await createTestFinding();
  }
  
  console.log('\nDemo setup completed successfully!');
  console.log('\nTo run the demo:');
  console.log('1. Verify the BigQuery dataset is publicly accessible');
  console.log('2. Verify NovaConnect is properly configured');
  console.log('3. Create a test finding or wait for SCC to detect the misconfiguration');
  console.log('4. Watch the remediation workflow execute');
  console.log('5. Check the Looker dashboard for the updated compliance score');
}

// Run the main function
if (require.main === module) {
  main().catch(error => {
    console.error('Demo setup failed:', error);
    process.exit(1);
  });
}

module.exports = {
  setupBigQueryDataset,
  configureNovaConnect,
  createTestFinding
};
