# NovaSentient Stack Demo - Docker Compose Configuration
# The World's First Conscious AI Defense Grid™ Demo Environment

version: '3.8'

services:
  # Main Demo Web Interface
  novasentient-demo:
    build:
      context: .
      dockerfile: demo/Dockerfile.demo
    ports:
      - "3142:3142"  # π × 1000 port (sacred number)
    environment:
      - NODE_ENV=demo
      - DEMO_MODE=interactive
      - SECURITY_LEVEL=maximum
    volumes:
      - ./demo:/app/demo
      - ./logs:/app/logs
    depends_on:
      - kethernet-node
      - novamemx-engine
      - nucp-simulator
    networks:
      - novasentient-network

  # KetherNet Distributed Node
  kethernet-node:
    build:
      context: ./demo/kethernet
      dockerfile: Dockerfile
    ports:
      - "7777:7777"  # Divine frequency port
    environment:
      - NODE_TYPE=demo_validator
      - CONSENSUS_MODE=crown
      - MORAL_THRESHOLD=0.9
    volumes:
      - ./demo/kethernet/data:/data
    networks:
      - novasentient-network

  # NovaMemX Memory Engine
  novamemx-engine:
    build:
      context: ./demo/novamemx
      dockerfile: Dockerfile
    ports:
      - "2847:2847"  # Consciousness threshold port
    environment:
      - MEMORY_MODE=quantum_coherence
      - GEOMETRY=icosahedral
      - COHERENCE_THRESHOLD=0.01
    volumes:
      - ./demo/novamemx/memory:/memory
    networks:
      - novasentient-network

  # NUCP Hardware Simulator
  nucp-simulator:
    build:
      context: ./demo/nucp
      dockerfile: Dockerfile
    ports:
      - "1618:1618"  # Golden ratio port
    environment:
      - CHIP_MODE=simulation
      - CLOCK_FREQUENCY=1.618e9
      - POWER_CONSUMPTION=7.77
    volumes:
      - ./demo/nucp/simulation:/simulation
    networks:
      - novasentient-network

  # NovaAlign AI Safety Monitor
  novaalign-monitor:
    build:
      context: ./demo/novaalign
      dockerfile: Dockerfile
    ports:
      - "9999:9999"  # Maximum alignment port
    environment:
      - ALIGNMENT_MODE=real_time
      - CONSCIOUSNESS_FILTER=enabled
      - MORAL_ENFORCEMENT=strict
    networks:
      - novasentient-network

  # NovaDNA Identity Validator
  novadna-validator:
    build:
      context: ./demo/novadna
      dockerfile: Dockerfile
    ports:
      - "3333:3333"  # Trinity validation port
    environment:
      - BIOMETRIC_MODE=multi_modal
      - CONSCIOUSNESS_DETECTION=enabled
      - QUANTUM_ENCRYPTION=active
    networks:
      - novasentient-network

  # NovaShield Threat Detector
  novashield-detector:
    build:
      context: ./demo/novashield
      dockerfile: Dockerfile
    ports:
      - "8888:8888"  # Infinite protection port
    environment:
      - DETECTION_MODE=predictive
      - RESPONSE_TIME=0.07ms
      - THREAT_IMMUNITY=active
    networks:
      - novasentient-network

  # NovaConnect API Gateway
  novaconnect-gateway:
    build:
      context: ./demo/novaconnect
      dockerfile: Dockerfile
    ports:
      - "4444:4444"  # Quadruple security port
    environment:
      - GATEWAY_MODE=divine_firewall
      - FIPS_COMPLIANCE=enabled
      - ZERO_TRUST=enforced
    networks:
      - novasentient-network

  # Attack Simulator (Red Team)
  attack-simulator:
    build:
      context: ./demo/attack-sim
      dockerfile: Dockerfile
    ports:
      - "6666:6666"  # Attack simulation port
    environment:
      - ATTACK_MODE=comprehensive
      - INTENSITY=maximum
      - TARGET=novasentient-stack
    networks:
      - novasentient-network

  # Metrics Dashboard
  metrics-dashboard:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=NovaSentient2025!
    volumes:
      - ./demo/grafana:/var/lib/grafana
      - ./demo/grafana/dashboards:/etc/grafana/provisioning/dashboards
    networks:
      - novasentient-network

networks:
  novasentient-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16  # Sacred network range

volumes:
  kethernet-data:
  novamemx-memory:
  nucp-simulation:
  grafana-storage:
