2869a07ac7b3f9f8ac5f0ff12b68b998
/**
 * NovaFuse Universal API Connector Validator
 * 
 * This module provides validation utilities for connector templates.
 */

/**
 * Connector Validator
 * 
 * Validates connector templates against schema requirements.
 */
class ConnectorValidator {
  /**
   * Validate a connector template
   * @param {Object} connector - Connector template to validate
   * @returns {Object} - Validation result with isValid and errors properties
   */
  static validateConnector(connector) {
    const errors = [];
    const result = {
      isValid: true,
      errors
    };
    if (!connector) {
      result.isValid = false;
      errors.push('Connector template is required');
      return result;
    }

    // Validate metadata
    this.validateMetadata(connector, errors);

    // Validate authentication
    this.validateAuthentication(connector, errors);

    // Validate configuration
    this.validateConfiguration(connector, errors);

    // Validate endpoints
    this.validateEndpoints(connector, errors);

    // Update isValid based on errors
    result.isValid = errors.length === 0;
    return result;
  }

  /**
   * Validate connector metadata
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateMetadata(connector, errors) {
    if (!connector.metadata) {
      errors.push('Connector metadata is required');
      return;
    }
    const {
      metadata
    } = connector;

    // Check required fields
    if (!metadata.name) {
      errors.push('Connector name is required');
    } else if (typeof metadata.name !== 'string') {
      errors.push('Connector name must be a string');
    }
    if (!metadata.version) {
      errors.push('Connector version is required');
    } else if (typeof metadata.version !== 'string') {
      errors.push('Connector version must be a string');
    } else if (!this.isValidVersion(metadata.version)) {
      errors.push('Connector version must be in semver format (e.g., 1.0.0)');
    }
    if (!metadata.category) {
      errors.push('Connector category is required');
    } else if (typeof metadata.category !== 'string') {
      errors.push('Connector category must be a string');
    }
    if (!metadata.description) {
      errors.push('Connector description is required');
    } else if (typeof metadata.description !== 'string') {
      errors.push('Connector description must be a string');
    }

    // Check optional fields
    if (metadata.author && typeof metadata.author !== 'string') {
      errors.push('Connector author must be a string');
    }
    if (metadata.tags) {
      if (!Array.isArray(metadata.tags)) {
        errors.push('Connector tags must be an array');
      } else {
        for (const tag of metadata.tags) {
          if (typeof tag !== 'string') {
            errors.push('Connector tags must be strings');
            break;
          }
        }
      }
    }
    if (metadata.icon && typeof metadata.icon !== 'string') {
      errors.push('Connector icon must be a string URL');
    }
    if (metadata.documentationUrl && typeof metadata.documentationUrl !== 'string') {
      errors.push('Connector documentation URL must be a string');
    }
  }

  /**
   * Validate connector authentication
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateAuthentication(connector, errors) {
    if (!connector.authentication) {
      errors.push('Connector authentication is required');
      return;
    }
    const {
      authentication
    } = connector;

    // Check authentication type
    if (!authentication.type) {
      errors.push('Authentication type is required');
    } else if (typeof authentication.type !== 'string') {
      errors.push('Authentication type must be a string');
    } else if (!['API_KEY', 'BASIC', 'OAUTH2', 'CUSTOM', 'NONE'].includes(authentication.type)) {
      errors.push('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');
    }

    // Check authentication fields
    if (!authentication.fields) {
      errors.push('Authentication fields are required');
      return;
    }
    if (typeof authentication.fields !== 'object') {
      errors.push('Authentication fields must be an object');
      return;
    }

    // Validate each field
    for (const [key, field] of Object.entries(authentication.fields)) {
      if (typeof field !== 'object') {
        errors.push(`Authentication field '${key}' must be an object`);
        continue;
      }
      if (!field.type) {
        errors.push(`Authentication field '${key}' must have a type`);
      } else if (typeof field.type !== 'string') {
        errors.push(`Authentication field '${key}' type must be a string`);
      } else if (!['string', 'number', 'boolean', 'object', 'array'].includes(field.type)) {
        errors.push(`Authentication field '${key}' type must be one of: string, number, boolean, object, array`);
      }
      if (field.required !== undefined && typeof field.required !== 'boolean') {
        errors.push(`Authentication field '${key}' required must be a boolean`);
      }
      if (field.sensitive !== undefined && typeof field.sensitive !== 'boolean') {
        errors.push(`Authentication field '${key}' sensitive must be a boolean`);
      }
      if (field.description && typeof field.description !== 'string') {
        errors.push(`Authentication field '${key}' description must be a string`);
      }
    }

    // Validate OAuth2 config if applicable
    if (authentication.type === 'OAUTH2' && authentication.oauth2Config) {
      this.validateOAuth2Config(authentication.oauth2Config, errors);
    }
  }

  /**
   * Validate OAuth2 configuration
   * @param {Object} oauth2Config - OAuth2 configuration
   * @param {Array} errors - Array to add errors to
   */
  static validateOAuth2Config(oauth2Config, errors) {
    if (typeof oauth2Config !== 'object') {
      errors.push('OAuth2 configuration must be an object');
      return;
    }

    // Check required fields
    if (!oauth2Config.tokenUrl) {
      errors.push('OAuth2 token URL is required');
    } else if (typeof oauth2Config.tokenUrl !== 'string') {
      errors.push('OAuth2 token URL must be a string');
    }

    // Check optional fields
    if (oauth2Config.authorizationUrl && typeof oauth2Config.authorizationUrl !== 'string') {
      errors.push('OAuth2 authorization URL must be a string');
    }
    if (oauth2Config.scopes) {
      if (!Array.isArray(oauth2Config.scopes)) {
        errors.push('OAuth2 scopes must be an array');
      } else {
        for (const scope of oauth2Config.scopes) {
          if (typeof scope !== 'string') {
            errors.push('OAuth2 scopes must be strings');
            break;
          }
        }
      }
    }
    if (oauth2Config.grantType && typeof oauth2Config.grantType !== 'string') {
      errors.push('OAuth2 grant type must be a string');
    }
  }

  /**
   * Validate connector configuration
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateConfiguration(connector, errors) {
    if (!connector.configuration) {
      errors.push('Connector configuration is required');
      return;
    }
    const {
      configuration
    } = connector;

    // Check required fields
    if (!configuration.baseUrl) {
      errors.push('Configuration base URL is required');
    } else if (typeof configuration.baseUrl !== 'string') {
      errors.push('Configuration base URL must be a string');
    } else if (!this.isValidUrl(configuration.baseUrl)) {
      errors.push('Configuration base URL must be a valid URL');
    }

    // Check optional fields
    if (configuration.headers && typeof configuration.headers !== 'object') {
      errors.push('Configuration headers must be an object');
    }
    if (configuration.timeout !== undefined) {
      if (typeof configuration.timeout !== 'number') {
        errors.push('Configuration timeout must be a number');
      } else if (configuration.timeout <= 0) {
        errors.push('Configuration timeout must be greater than 0');
      }
    }
    if (configuration.rateLimit) {
      if (typeof configuration.rateLimit !== 'object') {
        errors.push('Configuration rate limit must be an object');
      } else {
        if (configuration.rateLimit.requests !== undefined) {
          if (typeof configuration.rateLimit.requests !== 'number') {
            errors.push('Configuration rate limit requests must be a number');
          } else if (configuration.rateLimit.requests <= 0) {
            errors.push('Configuration rate limit requests must be greater than 0');
          }
        }
        if (configuration.rateLimit.period !== undefined && typeof configuration.rateLimit.period !== 'string') {
          errors.push('Configuration rate limit period must be a string');
        }
      }
    }
    if (configuration.retryPolicy) {
      if (typeof configuration.retryPolicy !== 'object') {
        errors.push('Configuration retry policy must be an object');
      } else {
        if (configuration.retryPolicy.maxRetries !== undefined) {
          if (typeof configuration.retryPolicy.maxRetries !== 'number') {
            errors.push('Configuration retry policy max retries must be a number');
          } else if (configuration.retryPolicy.maxRetries < 0) {
            errors.push('Configuration retry policy max retries must be greater than or equal to 0');
          }
        }
        if (configuration.retryPolicy.backoffStrategy !== undefined && typeof configuration.retryPolicy.backoffStrategy !== 'string') {
          errors.push('Configuration retry policy backoff strategy must be a string');
        }
      }
    }
  }

  /**
   * Validate connector endpoints
   * @param {Object} connector - Connector template
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpoints(connector, errors) {
    if (!connector.endpoints) {
      errors.push('Connector endpoints are required');
      return;
    }
    if (!Array.isArray(connector.endpoints)) {
      errors.push('Connector endpoints must be an array');
      return;
    }
    if (connector.endpoints.length === 0) {
      errors.push('Connector must have at least one endpoint');
      return;
    }

    // Validate each endpoint
    for (let i = 0; i < connector.endpoints.length; i++) {
      const endpoint = connector.endpoints[i];
      if (typeof endpoint !== 'object') {
        errors.push(`Endpoint at index ${i} must be an object`);
        continue;
      }

      // Check required fields
      if (!endpoint.id) {
        errors.push(`Endpoint at index ${i} must have an ID`);
      } else if (typeof endpoint.id !== 'string') {
        errors.push(`Endpoint at index ${i} ID must be a string`);
      }
      if (!endpoint.name) {
        errors.push(`Endpoint at index ${i} must have a name`);
      } else if (typeof endpoint.name !== 'string') {
        errors.push(`Endpoint at index ${i} name must be a string`);
      }
      if (!endpoint.path) {
        errors.push(`Endpoint at index ${i} must have a path`);
      } else if (typeof endpoint.path !== 'string') {
        errors.push(`Endpoint at index ${i} path must be a string`);
      }
      if (!endpoint.method) {
        errors.push(`Endpoint at index ${i} must have a method`);
      } else if (typeof endpoint.method !== 'string') {
        errors.push(`Endpoint at index ${i} method must be a string`);
      } else if (!['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].includes(endpoint.method)) {
        errors.push(`Endpoint at index ${i} method must be one of: GET, POST, PUT, PATCH, DELETE`);
      }

      // Validate parameters if present
      if (endpoint.parameters) {
        this.validateEndpointParameters(endpoint.parameters, i, errors);
      }

      // Validate response if present
      if (endpoint.response) {
        this.validateEndpointResponse(endpoint.response, i, errors);
      }

      // Validate pagination if present
      if (endpoint.pagination) {
        this.validateEndpointPagination(endpoint.pagination, i, errors);
      }
    }

    // Check for duplicate endpoint IDs
    const endpointIds = connector.endpoints.map(e => e.id);
    const duplicateIds = endpointIds.filter((id, index) => endpointIds.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      errors.push(`Duplicate endpoint IDs found: ${duplicateIds.join(', ')}`);
    }
  }

  /**
   * Validate endpoint parameters
   * @param {Object} parameters - Endpoint parameters
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpointParameters(parameters, endpointIndex, errors) {
    if (typeof parameters !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} parameters must be an object`);
      return;
    }

    // Validate path parameters
    if (parameters.path) {
      if (typeof parameters.path !== 'object') {
        errors.push(`Endpoint at index ${endpointIndex} path parameters must be an object`);
      } else {
        for (const [key, param] of Object.entries(parameters.path)) {
          this.validateParameter(param, key, 'path', endpointIndex, errors);
        }
      }
    }

    // Validate query parameters
    if (parameters.query) {
      if (typeof parameters.query !== 'object') {
        errors.push(`Endpoint at index ${endpointIndex} query parameters must be an object`);
      } else {
        for (const [key, param] of Object.entries(parameters.query)) {
          this.validateParameter(param, key, 'query', endpointIndex, errors);
        }
      }
    }

    // Validate body parameters
    if (parameters.body) {
      if (typeof parameters.body !== 'object') {
        errors.push(`Endpoint at index ${endpointIndex} body parameters must be an object`);
      } else {
        if (parameters.body.required !== undefined && typeof parameters.body.required !== 'boolean') {
          errors.push(`Endpoint at index ${endpointIndex} body required must be a boolean`);
        }
        if (parameters.body.properties && typeof parameters.body.properties !== 'object') {
          errors.push(`Endpoint at index ${endpointIndex} body properties must be an object`);
        }
      }
    }
  }

  /**
   * Validate a parameter
   * @param {Object} param - Parameter to validate
   * @param {string} key - Parameter key
   * @param {string} type - Parameter type (path, query, body)
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateParameter(param, key, type, endpointIndex, errors) {
    if (typeof param !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' must be an object`);
      return;
    }
    if (param.type && typeof param.type !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' type must be a string`);
    }
    if (param.required !== undefined && typeof param.required !== 'boolean') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' required must be a boolean`);
    }
    if (param.description && typeof param.description !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} ${type} parameter '${key}' description must be a string`);
    }
  }

  /**
   * Validate endpoint response
   * @param {Object} response - Endpoint response
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpointResponse(response, endpointIndex, errors) {
    if (typeof response !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} response must be an object`);
      return;
    }
    if (response.successCode !== undefined) {
      if (typeof response.successCode !== 'number') {
        errors.push(`Endpoint at index ${endpointIndex} response success code must be a number`);
      } else if (response.successCode < 200 || response.successCode >= 300) {
        errors.push(`Endpoint at index ${endpointIndex} response success code must be in the 2xx range`);
      }
    }
    if (response.dataPath && typeof response.dataPath !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} response data path must be a string`);
    }
    if (response.schema && typeof response.schema !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} response schema must be an object`);
    }
  }

  /**
   * Validate endpoint pagination
   * @param {Object} pagination - Endpoint pagination
   * @param {number} endpointIndex - Index of the endpoint
   * @param {Array} errors - Array to add errors to
   */
  static validateEndpointPagination(pagination, endpointIndex, errors) {
    if (typeof pagination !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} pagination must be an object`);
      return;
    }
    if (!pagination.type) {
      errors.push(`Endpoint at index ${endpointIndex} pagination type is required`);
    } else if (typeof pagination.type !== 'string') {
      errors.push(`Endpoint at index ${endpointIndex} pagination type must be a string`);
    } else if (!['offset', 'token', 'page', 'cursor'].includes(pagination.type)) {
      errors.push(`Endpoint at index ${endpointIndex} pagination type must be one of: offset, token, page, cursor`);
    }
    if (!pagination.parameters) {
      errors.push(`Endpoint at index ${endpointIndex} pagination parameters are required`);
    } else if (typeof pagination.parameters !== 'object') {
      errors.push(`Endpoint at index ${endpointIndex} pagination parameters must be an object`);
    } else {
      // Validate based on pagination type
      switch (pagination.type) {
        case 'offset':
          if (!pagination.parameters.limit) {
            errors.push(`Endpoint at index ${endpointIndex} pagination offset requires a limit parameter`);
          }
          if (!pagination.parameters.offset) {
            errors.push(`Endpoint at index ${endpointIndex} pagination offset requires an offset parameter`);
          }
          break;
        case 'token':
          if (!pagination.parameters.nextPageToken) {
            errors.push(`Endpoint at index ${endpointIndex} pagination token requires a nextPageToken parameter`);
          }
          if (!pagination.parameters.pageToken) {
            errors.push(`Endpoint at index ${endpointIndex} pagination token requires a pageToken parameter`);
          }
          break;
        case 'page':
          if (!pagination.parameters.page) {
            errors.push(`Endpoint at index ${endpointIndex} pagination page requires a page parameter`);
          }
          if (!pagination.parameters.pageSize) {
            errors.push(`Endpoint at index ${endpointIndex} pagination page requires a pageSize parameter`);
          }
          break;
        case 'cursor':
          if (!pagination.parameters.cursor) {
            errors.push(`Endpoint at index ${endpointIndex} pagination cursor requires a cursor parameter`);
          }
          break;
      }
    }
  }

  /**
   * Check if a string is a valid URL
   * @param {string} url - URL to check
   * @returns {boolean} - Whether the URL is valid
   */
  static isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a string is a valid semver version
   * @param {string} version - Version to check
   * @returns {boolean} - Whether the version is valid
   */
  static isValidVersion(version) {
    const semverRegex = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
    return semverRegex.test(version);
  }
}
module.exports = ConnectorValidator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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