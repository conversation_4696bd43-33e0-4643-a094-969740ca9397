/**
 * CSME Basic Example
 * 
 * This example demonstrates the basic usage of the CSME (Cyber-Safety Medical Engine).
 */

// Import CSME components
const { 
  CSMEEngine,
  BioEntropicTensor,
  EnvironmentalContextProcessor,
  PsiRevertProtocolEngine,
  NeuroEthicalFilterBank,
  DecayRateCal<PERSON>tor,
  CSMEController,
  createCSMESystem
} = require('../index');

// Sample data
const sampleGenomicData = {
  geneticRiskFactors: {
    alzheimers: 0.2,
    heartDisease: 0.3,
    diabetes: 0.1
  },
  geneticProtectiveFactors: {
    longevity: 0.4,
    immuneResponse: 0.5
  },
  telomereLength: 0.8,
  mitochondrialDNA: {
    mutations: 0.1,
    copyNumber: 0.9
  }
};

const sampleProteomicData = {
  proteinExpression: {
    p53: 0.7,
    mTOR: 0.6,
    FOXO3: 0.8
  },
  inflammatoryMarkers: {
    crp: 0.3,
    il6: 0.2,
    tnfAlpha: 0.1
  },
  oxidativeStressMarkers: {
    glutathione: 0.7,
    superoxideDismutase: 0.6,
    catalase: 0.8
  }
};

const sampleClinicalData = {
  symptoms: {
    fatigue: 0.3,
    pain: 0.2,
    cognitiveFog: 0.1
  },
  vitalSigns: {
    bloodPressure: 0.8,
    heartRate: 0.7,
    oxygenSaturation: 0.9
  },
  labResults: {
    bloodGlucose: 0.8,
    cholesterol: 0.7,
    vitaminD: 0.6
  },
  diagnosis: {
    primary: 'Mild Cognitive Impairment',
    stage: 'Early',
    severity: 'Mild'
  }
};

const sampleEnvironmentalData = {
  temperature: 22,
  humidity: 45,
  oxygenLevel: 21,
  atmosphericPressure: 1013,
  lightLevel: 500,
  noiseLevel: 40,
  toxinLevel: 0.1,
  radiationLevel: 0.05
};

/**
 * Example 1: Basic CSME Engine Usage
 */
function basicEngineExample() {
  console.log('\n=== Basic CSME Engine Example ===');
  
  // Initialize CSME Engine
  const csmeEngine = new CSMEEngine();
  
  // Calculate CSME value
  const result = csmeEngine.calculate(
    sampleGenomicData,
    sampleProteomicData,
    sampleClinicalData
  );
  
  // Display result
  console.log('CSME Result:');
  console.log(`CSME Value: ${result.csmeValue}`);
  console.log(`Performance Factor: ${result.performanceFactor}x`);
  console.log(`Ethical Score: ${csmeEngine.getEthicalScore()}`);
  console.log(`Calculated At: ${result.calculatedAt}`);
}

/**
 * Example 2: Using Individual Components
 */
function individualComponentsExample() {
  console.log('\n=== Individual Components Example ===');
  
  // Initialize components
  const bioEntropicTensor = new BioEntropicTensor();
  const environmentalContextProcessor = new EnvironmentalContextProcessor();
  const psiRevertProtocolEngine = new PsiRevertProtocolEngine();
  const neuroEthicalFilterBank = new NeuroEthicalFilterBank();
  const decayRateCalculator = new DecayRateCalculator();
  
  // Process biological data
  const tensorResult = bioEntropicTensor.processBiologicalData({
    genomicData: sampleGenomicData,
    proteomicData: sampleProteomicData,
    clinicalData: sampleClinicalData,
    environmentalData: sampleEnvironmentalData
  });
  
  // Process environmental context
  const environmentalContext = environmentalContextProcessor.processEnvironmentalContext(
    sampleEnvironmentalData
  );
  
  // Calculate decay rate
  const decayRateResult = decayRateCalculator.calculateDecayRate(
    { 
      age: 45,
      geneticProfile: sampleGenomicData,
      lifestyleFactors: {
        diet: 0.7,
        exercise: 0.6,
        sleep: 0.8,
        smoking: 0.1,
        alcohol: 0.2
      }
    },
    sampleEnvironmentalData
  );
  
  // Display results
  console.log('BioEntropicTensor Result:');
  console.log(`Coherence: ${tensorResult.coherence}`);
  console.log(`Entropy Gradient: ${tensorResult.entropyGradient}`);
  console.log(`Tensor: [${tensorResult.tensor.join(', ')}]`);
  
  console.log('\nEnvironmentalContextProcessor Result:');
  console.log(`Overall Impact: ${environmentalContext.overallImpact}`);
  console.log(`Edenic Distance: ${environmentalContext.edenicDistance}`);
  console.log(`Decay Rate Adjustment: ${environmentalContext.decayRateAdjustment}`);
  
  console.log('\nDecayRateCalculator Result:');
  console.log(`Decay Rate: ${decayRateResult.decayRate}`);
  console.log('Adjustments:', decayRateResult.adjustments);
}

/**
 * Example 3: Complete CSME System
 */
function completeSystemExample() {
  console.log('\n=== Complete CSME System Example ===');
  
  // Create CSME system
  const csmeSystem = createCSMESystem();
  
  // Process biological data
  const result = csmeSystem.csmeController.processBiologicalData(
    {
      genomicData: sampleGenomicData,
      proteomicData: sampleProteomicData,
      clinicalData: sampleClinicalData
    },
    sampleEnvironmentalData
  );
  
  // Recommend protocols
  const recommendations = csmeSystem.csmeController.recommendProtocols();
  
  // Project coherence trajectory
  const projection = csmeSystem.csmeController.projectCoherenceTrajectory(10);
  
  // Display results
  console.log('Processing Result:');
  console.log(`Coherence: ${result.coherence}`);
  console.log(`Coherence Change: ${result.coherenceChange}`);
  console.log(`Decay Rate: ${result.decayRate}`);
  
  console.log('\nRecommended Protocols:');
  console.log(`Number of Protocols: ${recommendations.protocols.length}`);
  if (recommendations.protocols.length > 0) {
    console.log(`Top Protocol: ${recommendations.protocols[0].name}`);
  }
  
  console.log('\nCoherence Projection:');
  console.log(`Initial Coherence: ${projection.baselineProjection[0]}`);
  console.log(`Final Coherence (10 steps): ${projection.baselineProjection[10]}`);
}

/**
 * Run all examples
 */
function runAllExamples() {
  basicEngineExample();
  individualComponentsExample();
  completeSystemExample();
}

// Run examples
runAllExamples();
