/**
 * CSDE Performance Monitoring Controller
 * 
 * This module provides API endpoints for the CSDE performance monitoring service.
 */

/**
 * Create CSDE performance monitoring controller
 * @param {Object} options - Controller options
 * @param {Object} options.performanceMonitor - Performance monitoring service
 * @param {Object} options.logger - Logger instance
 * @returns {Object} Controller methods
 */
function createPerformanceMonitoringController(options = {}) {
  const performanceMonitor = options.performanceMonitor;
  const logger = options.logger || console;
  
  if (!performanceMonitor) {
    throw new Error('Performance monitor is required');
  }
  
  /**
   * Get performance metrics
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   */
  function getMetrics(req, res) {
    try {
      const metrics = performanceMonitor.getMetrics();
      
      res.json({
        success: true,
        metrics
      });
    } catch (error) {
      logger.error('Error getting performance metrics', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get performance metrics',
        message: error.message
      });
    }
  }
  
  /**
   * Get visualization data
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   */
  function getVisualizationData(req, res) {
    try {
      const visualizationData = performanceMonitor.getVisualizationData();
      
      res.json({
        success: true,
        data: visualizationData
      });
    } catch (error) {
      logger.error('Error getting visualization data', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get visualization data',
        message: error.message
      });
    }
  }
  
  /**
   * Reset metrics
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   */
  function resetMetrics(req, res) {
    try {
      performanceMonitor.resetMetrics();
      
      res.json({
        success: true,
        message: 'Metrics reset successfully'
      });
    } catch (error) {
      logger.error('Error resetting metrics', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to reset metrics',
        message: error.message
      });
    }
  }
  
  /**
   * Get alerts
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   */
  function getAlerts(req, res) {
    try {
      const metrics = performanceMonitor.getMetrics();
      const alerts = metrics.alerts || [];
      
      res.json({
        success: true,
        alerts
      });
    } catch (error) {
      logger.error('Error getting alerts', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get alerts',
        message: error.message
      });
    }
  }
  
  /**
   * Get health status
   * @param {Object} req - Express request
   * @param {Object} res - Express response
   */
  function getHealth(req, res) {
    try {
      const metrics = performanceMonitor.getMetrics();
      
      // Calculate error rate
      const errorRate = metrics.engine.totalOperations > 0 
        ? metrics.engine.failedOperations / metrics.engine.totalOperations 
        : 0;
      
      // Determine health status
      let status = 'healthy';
      
      if (errorRate > 0.05 || metrics.engine.averageLatency > 100) {
        status = 'degraded';
      }
      
      if (errorRate > 0.1 || metrics.engine.averageLatency > 200) {
        status = 'unhealthy';
      }
      
      res.json({
        success: true,
        health: {
          status,
          metrics: {
            totalOperations: metrics.engine.totalOperations,
            successfulOperations: metrics.engine.successfulOperations,
            failedOperations: metrics.engine.failedOperations,
            errorRate,
            averageLatency: metrics.engine.averageLatency,
            performanceFactor: metrics.engine.performanceFactor,
            cacheHitRate: metrics.cache.hitRate
          },
          system: {
            cpuUsage: metrics.system.cpu.length > 0 
              ? metrics.system.cpu[metrics.system.cpu.length - 1].value 
              : 0,
            memoryUsage: metrics.system.memory.length > 0 
              ? metrics.system.memory[metrics.system.memory.length - 1].value 
              : 0,
            eventLoopLag: metrics.system.eventLoop.length > 0 
              ? metrics.system.eventLoop[metrics.system.eventLoop.length - 1].value 
              : 0
          },
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      logger.error('Error getting health status', error);
      
      res.status(500).json({
        success: false,
        error: 'Failed to get health status',
        message: error.message
      });
    }
  }
  
  return {
    getMetrics,
    getVisualizationData,
    resetMetrics,
    getAlerts,
    getHealth
  };
}

module.exports = createPerformanceMonitoringController;
