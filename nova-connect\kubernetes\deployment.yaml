apiVersion: apps/v1
kind: Deployment
metadata:
  name: novafuse-uac
  namespace: novafuse
  labels:
    app: novafuse-uac
    component: api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: novafuse-uac
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: novafuse-uac
        component: api
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "3001"
    spec:
      serviceAccountName: novafuse-sa
      containers:
      - name: novafuse-uac
        image: gcr.io/PROJECT_ID/novafuse-uac:VERSION
        imagePullPolicy: Always
        ports:
        - containerPort: 3001
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: novafuse-secrets
              key: mongodb-uri
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: novafuse-secrets
              key: jwt-secret
        - name: GOOGLE_CLOUD_PROJECT
          valueFrom:
            configMapKeyRef:
              name: novafuse-config
              key: google-cloud-project
        - name: GOOGLE_CLOUD_MONITORING_ENABLED
          value: "true"
        - name: TRACING_ENABLED
          value: "true"
        - name: TRACING_EXPORTER
          value: "gcp"
        - name: METRICS_ENABLED
          value: "true"
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: http
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
        - name: tmp-volume
          mountPath: /tmp
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: google-cloud-key
        secret:
          secretName: novafuse-gcp-key
      - name: tmp-volume
        emptyDir: {}
      - name: logs-volume
        emptyDir: {}
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
