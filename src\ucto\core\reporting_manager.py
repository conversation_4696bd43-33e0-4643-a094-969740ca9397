"""
Reporting Manager for the Universal Compliance Tracking Optimizer.

This module provides functionality for generating compliance reports.
"""

import os
import json
import logging
import uuid
from typing import Dict, List, Any, Optional, Callable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ReportingManager:
    """
    Manager for compliance reporting.
    
    This class is responsible for generating various types of compliance reports.
    """
    
    def __init__(self, reports_dir: Optional[str] = None):
        """
        Initialize the Reporting Manager.
        
        Args:
            reports_dir: Path to a directory for storing reports
        """
        logger.info("Initializing Reporting Manager")
        
        # Set the reports directory
        self.reports_dir = reports_dir or os.path.join(os.getcwd(), 'reports_data')
        
        # Create the reports directory if it doesn't exist
        os.makedirs(self.reports_dir, exist_ok=True)
        
        # Dictionary to store reports in memory
        self.reports: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store report generators
        self.report_generators: Dict[str, Callable] = {}
        
        # Load reports from disk
        self._load_reports_from_disk()
        
        # Register default report generators
        self._register_default_report_generators()
        
        logger.info(f"Reporting Manager initialized with {len(self.reports)} reports and {len(self.report_generators)} generators")
    
    def _register_default_report_generators(self) -> None:
        """Register default report generators."""
        # Summary report generator
        self.register_report_generator('summary', self._generate_summary_report)
        
        # Status report generator
        self.register_report_generator('status', self._generate_status_report)
        
        # Framework compliance report generator
        self.register_report_generator('framework_compliance', self._generate_framework_compliance_report)
        
        # Activity report generator
        self.register_report_generator('activity', self._generate_activity_report)
        
        # Timeline report generator
        self.register_report_generator('timeline', self._generate_timeline_report)
    
    def register_report_generator(self, report_type: str, generator_func: Callable) -> None:
        """
        Register a report generator.
        
        Args:
            report_type: The type of report
            generator_func: The report generator function
        """
        self.report_generators[report_type] = generator_func
        logger.info(f"Registered report generator: {report_type}")
    
    def generate_report(self, 
                       report_type: str, 
                       requirements: List[Dict[str, Any]], 
                       activities: List[Dict[str, Any]], 
                       parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate a report.
        
        Args:
            report_type: The type of report to generate
            requirements: List of requirements for the report
            activities: List of activities for the report
            parameters: Optional parameters for the report
            
        Returns:
            The generated report
            
        Raises:
            ValueError: If the report type does not exist
        """
        logger.info(f"Generating report: {report_type}")
        
        # Check if the report type exists
        if report_type not in self.report_generators:
            raise ValueError(f"Report type not found: {report_type}")
        
        # Get the report generator
        generator_func = self.report_generators[report_type]
        
        # Generate the report
        report = generator_func(requirements, activities, parameters or {})
        
        # Store the report in memory
        self.reports[report['id']] = report
        
        # Store the report on disk
        self._save_report_to_disk(report)
        
        logger.info(f"Report generated: {report['id']}")
        
        return report
    
    def get_report(self, report_id: str) -> Dict[str, Any]:
        """
        Get a report.
        
        Args:
            report_id: The ID of the report
            
        Returns:
            The report
            
        Raises:
            ValueError: If the report does not exist
        """
        logger.info(f"Getting report: {report_id}")
        
        # Check if the report is in memory
        if report_id in self.reports:
            return self.reports[report_id]
        
        # Try to load the report from disk
        report_path = os.path.join(self.reports_dir, f"{report_id}.json")
        
        if os.path.exists(report_path):
            try:
                with open(report_path, 'r', encoding='utf-8') as f:
                    report = json.load(f)
                
                # Store the report in memory
                self.reports[report_id] = report
                
                return report
            
            except Exception as e:
                logger.error(f"Failed to load report from disk: {e}")
        
        raise ValueError(f"Report not found: {report_id}")
    
    def delete_report(self, report_id: str) -> None:
        """
        Delete a report.
        
        Args:
            report_id: The ID of the report
            
        Raises:
            ValueError: If the report does not exist
        """
        logger.info(f"Deleting report: {report_id}")
        
        # Check if the report exists
        if report_id not in self.reports:
            raise ValueError(f"Report not found: {report_id}")
        
        # Remove the report from memory
        del self.reports[report_id]
        
        # Remove the report from disk
        report_path = os.path.join(self.reports_dir, f"{report_id}.json")
        
        if os.path.exists(report_path):
            try:
                os.remove(report_path)
                logger.info(f"Deleted report from disk: {report_id}")
            
            except Exception as e:
                logger.error(f"Failed to delete report from disk: {e}")
    
    def get_all_reports(self) -> List[Dict[str, Any]]:
        """
        Get all reports.
        
        Returns:
            List of reports
        """
        logger.info("Getting all reports")
        
        return list(self.reports.values())
    
    def get_reports_by_type(self, report_type: str) -> List[Dict[str, Any]]:
        """
        Get all reports of a specific type.
        
        Args:
            report_type: The type of reports to get
            
        Returns:
            List of reports of the specified type
        """
        logger.info(f"Getting reports of type: {report_type}")
        
        return [r for r in self.reports.values() if r.get('type') == report_type]
    
    def _load_reports_from_disk(self) -> None:
        """Load reports from disk."""
        try:
            # Get all JSON files in the reports directory
            report_files = [f for f in os.listdir(self.reports_dir) if f.endswith('.json')]
            
            for report_file in report_files:
                try:
                    # Load the report from disk
                    file_path = os.path.join(self.reports_dir, report_file)
                    
                    with open(file_path, 'r', encoding='utf-8') as f:
                        report = json.load(f)
                    
                    # Store the report in memory
                    report_id = report.get('id')
                    
                    if report_id:
                        self.reports[report_id] = report
                        logger.info(f"Loaded report from disk: {report_id}")
                
                except Exception as e:
                    logger.error(f"Failed to load report from {report_file}: {e}")
            
            logger.info(f"Loaded {len(self.reports)} reports from disk")
        
        except Exception as e:
            logger.error(f"Failed to load reports from disk: {e}")
    
    def _save_report_to_disk(self, report: Dict[str, Any]) -> None:
        """
        Save a report to disk.
        
        Args:
            report: The report to save
        """
        try:
            # Get the report ID
            report_id = report.get('id')
            
            if not report_id:
                raise ValueError("Report ID is missing")
            
            # Create the file path
            file_path = os.path.join(self.reports_dir, f"{report_id}.json")
            
            # Save the report to a JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Saved report to disk: {report_id}")
        
        except Exception as e:
            logger.error(f"Failed to save report to disk: {e}")
    
    # Default report generators
    
    def _generate_summary_report(self, 
                               requirements: List[Dict[str, Any]], 
                               activities: List[Dict[str, Any]], 
                               parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary report.
        
        Args:
            requirements: List of requirements for the report
            activities: List of activities for the report
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating summary report")
        
        # Generate a unique report ID
        report_id = str(uuid.uuid4())
        
        # Count requirements by status
        requirement_status_counts = {}
        for requirement in requirements:
            status = requirement.get('status', 'pending')
            requirement_status_counts[status] = requirement_status_counts.get(status, 0) + 1
        
        # Count activities by status
        activity_status_counts = {}
        for activity in activities:
            status = activity.get('status', 'pending')
            activity_status_counts[status] = activity_status_counts.get(status, 0) + 1
        
        # Count requirements by framework
        framework_counts = {}
        for requirement in requirements:
            framework = requirement.get('framework', 'unknown')
            framework_counts[framework] = framework_counts.get(framework, 0) + 1
        
        # Calculate completion percentage
        total_requirements = len(requirements)
        completed_requirements = requirement_status_counts.get('completed', 0)
        requirement_completion_percentage = (completed_requirements / total_requirements * 100) if total_requirements > 0 else 0
        
        total_activities = len(activities)
        completed_activities = activity_status_counts.get('completed', 0)
        activity_completion_percentage = (completed_activities / total_activities * 100) if total_activities > 0 else 0
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'summary',
            'title': parameters.get('title', 'Compliance Summary Report'),
            'generated_at': self._get_current_timestamp(),
            'total_requirements': total_requirements,
            'total_activities': total_activities,
            'requirement_status_counts': requirement_status_counts,
            'activity_status_counts': activity_status_counts,
            'framework_counts': framework_counts,
            'requirement_completion_percentage': requirement_completion_percentage,
            'activity_completion_percentage': activity_completion_percentage
        }
        
        logger.info("Summary report generated")
        
        return report
    
    def _generate_status_report(self, 
                              requirements: List[Dict[str, Any]], 
                              activities: List[Dict[str, Any]], 
                              parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a status report.
        
        Args:
            requirements: List of requirements for the report
            activities: List of activities for the report
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating status report")
        
        # Generate a unique report ID
        report_id = str(uuid.uuid4())
        
        # Filter requirements by status if specified
        status_filter = parameters.get('status')
        if status_filter:
            filtered_requirements = [r for r in requirements if r.get('status') == status_filter]
        else:
            filtered_requirements = requirements
        
        # Filter activities by status if specified
        if status_filter:
            filtered_activities = [a for a in activities if a.get('status') == status_filter]
        else:
            filtered_activities = activities
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'status',
            'title': parameters.get('title', f"Compliance Status Report - {status_filter or 'All'}"),
            'generated_at': self._get_current_timestamp(),
            'status_filter': status_filter,
            'total_requirements': len(requirements),
            'filtered_requirements': len(filtered_requirements),
            'total_activities': len(activities),
            'filtered_activities': len(filtered_activities),
            'requirements': filtered_requirements,
            'activities': filtered_activities
        }
        
        logger.info("Status report generated")
        
        return report
    
    def _generate_framework_compliance_report(self, 
                                            requirements: List[Dict[str, Any]], 
                                            activities: List[Dict[str, Any]], 
                                            parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a framework compliance report.
        
        Args:
            requirements: List of requirements for the report
            activities: List of activities for the report
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating framework compliance report")
        
        # Generate a unique report ID
        report_id = str(uuid.uuid4())
        
        # Get the framework
        framework = parameters.get('framework')
        if not framework:
            raise ValueError("Framework is required for framework compliance report")
        
        # Filter requirements by framework
        framework_requirements = [r for r in requirements if r.get('framework') == framework]
        
        # Get activities for the framework requirements
        framework_requirement_ids = [r.get('id') for r in framework_requirements]
        framework_activities = [a for a in activities if a.get('requirement_id') in framework_requirement_ids]
        
        # Count requirements by status
        requirement_status_counts = {}
        for requirement in framework_requirements:
            status = requirement.get('status', 'pending')
            requirement_status_counts[status] = requirement_status_counts.get(status, 0) + 1
        
        # Count activities by status
        activity_status_counts = {}
        for activity in framework_activities:
            status = activity.get('status', 'pending')
            activity_status_counts[status] = activity_status_counts.get(status, 0) + 1
        
        # Calculate completion percentage
        total_requirements = len(framework_requirements)
        completed_requirements = requirement_status_counts.get('completed', 0)
        requirement_completion_percentage = (completed_requirements / total_requirements * 100) if total_requirements > 0 else 0
        
        total_activities = len(framework_activities)
        completed_activities = activity_status_counts.get('completed', 0)
        activity_completion_percentage = (completed_activities / total_activities * 100) if total_activities > 0 else 0
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'framework_compliance',
            'title': parameters.get('title', f"{framework} Compliance Report"),
            'generated_at': self._get_current_timestamp(),
            'framework': framework,
            'total_requirements': total_requirements,
            'total_activities': total_activities,
            'requirement_status_counts': requirement_status_counts,
            'activity_status_counts': activity_status_counts,
            'requirement_completion_percentage': requirement_completion_percentage,
            'activity_completion_percentage': activity_completion_percentage,
            'requirements': framework_requirements,
            'activities': framework_activities
        }
        
        logger.info("Framework compliance report generated")
        
        return report
    
    def _generate_activity_report(self, 
                                requirements: List[Dict[str, Any]], 
                                activities: List[Dict[str, Any]], 
                                parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an activity report.
        
        Args:
            requirements: List of requirements for the report
            activities: List of activities for the report
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating activity report")
        
        # Generate a unique report ID
        report_id = str(uuid.uuid4())
        
        # Filter activities by assigned_to if specified
        assigned_to = parameters.get('assigned_to')
        if assigned_to:
            filtered_activities = [a for a in activities if a.get('assigned_to') == assigned_to]
        else:
            filtered_activities = activities
        
        # Count activities by type
        activity_type_counts = {}
        for activity in filtered_activities:
            activity_type = activity.get('type', 'task')
            activity_type_counts[activity_type] = activity_type_counts.get(activity_type, 0) + 1
        
        # Count activities by status
        activity_status_counts = {}
        for activity in filtered_activities:
            status = activity.get('status', 'pending')
            activity_status_counts[status] = activity_status_counts.get(status, 0) + 1
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'activity',
            'title': parameters.get('title', f"Activity Report - {assigned_to or 'All'}"),
            'generated_at': self._get_current_timestamp(),
            'assigned_to': assigned_to,
            'total_activities': len(filtered_activities),
            'activity_type_counts': activity_type_counts,
            'activity_status_counts': activity_status_counts,
            'activities': filtered_activities
        }
        
        logger.info("Activity report generated")
        
        return report
    
    def _generate_timeline_report(self, 
                                requirements: List[Dict[str, Any]], 
                                activities: List[Dict[str, Any]], 
                                parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a timeline report.
        
        Args:
            requirements: List of requirements for the report
            activities: List of activities for the report
            parameters: Parameters for the report
            
        Returns:
            The generated report
        """
        logger.info("Generating timeline report")
        
        # Generate a unique report ID
        report_id = str(uuid.uuid4())
        
        # Filter activities with dates
        activities_with_dates = [a for a in activities if a.get('start_date') and a.get('end_date')]
        
        # Sort activities by start date
        activities_with_dates.sort(key=lambda a: a.get('start_date', ''))
        
        # Group activities by month
        activities_by_month = {}
        for activity in activities_with_dates:
            start_date = activity.get('start_date', '')
            if start_date:
                # Extract month from date (assuming ISO format)
                month = start_date[:7]  # YYYY-MM
                if month not in activities_by_month:
                    activities_by_month[month] = []
                activities_by_month[month].append(activity)
        
        # Create the report
        report = {
            'id': report_id,
            'type': 'timeline',
            'title': parameters.get('title', 'Compliance Timeline Report'),
            'generated_at': self._get_current_timestamp(),
            'total_activities': len(activities),
            'activities_with_dates': len(activities_with_dates),
            'activities_by_month': activities_by_month
        }
        
        logger.info("Timeline report generated")
        
        return report
    
    def _get_current_timestamp(self) -> str:
        """
        Get the current timestamp.
        
        Returns:
            The current timestamp as a string
        """
        import datetime
        return datetime.datetime.now().isoformat()
