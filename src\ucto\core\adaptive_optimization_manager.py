"""
Adaptive Optimization Manager for the Universal Compliance Tracking Optimizer.

This module provides functionality for adaptively optimizing compliance workflows
based on risk, resource availability, and regulatory changes.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdaptiveOptimizationManager:
    """
    Manager for adaptive compliance workflow optimization.
    
    This class is responsible for adaptively optimizing compliance workflows
    based on risk, resource availability, and regulatory changes.
    """
    
    def __init__(self, data_dir: Optional[str] = None):
        """
        Initialize the Adaptive Optimization Manager.
        
        Args:
            data_dir: Path to a directory for storing optimization data
        """
        logger.info("Initializing Adaptive Optimization Manager")
        
        # Set the data directory
        self.data_dir = data_dir or os.path.join(os.getcwd(), 'optimization_data')
        
        # Create the data directory if it doesn't exist
        os.makedirs(self.data_dir, exist_ok=True)
        
        # Dictionary to store optimization strategies
        self.optimization_strategies: Dict[str, Callable] = {}
        
        # Dictionary to store adaptation rules
        self.adaptation_rules: Dict[str, Dict[str, Any]] = {}
        
        # Dictionary to store optimization history
        self.optimization_history: List[Dict[str, Any]] = []
        
        # Register default optimization strategies
        self._register_default_optimization_strategies()
        
        # Register default adaptation rules
        self._register_default_adaptation_rules()
        
        # Load optimization history from disk
        self._load_optimization_history()
        
        logger.info(f"Adaptive Optimization Manager initialized with {len(self.optimization_strategies)} strategies and {len(self.adaptation_rules)} adaptation rules")
    
    def _register_default_optimization_strategies(self) -> None:
        """Register default optimization strategies."""
        # Risk-based prioritization
        self.register_optimization_strategy('risk_based_prioritization', self._optimize_risk_based_prioritization)
        
        # Resource-aware scheduling
        self.register_optimization_strategy('resource_aware_scheduling', self._optimize_resource_aware_scheduling)
        
        # Regulatory change adaptation
        self.register_optimization_strategy('regulatory_change_adaptation', self._optimize_regulatory_change_adaptation)
        
        # Compliance consolidation
        self.register_optimization_strategy('compliance_consolidation', self._optimize_compliance_consolidation)
        
        # Deadline-driven optimization
        self.register_optimization_strategy('deadline_driven_optimization', self._optimize_deadline_driven)
    
    def _register_default_adaptation_rules(self) -> None:
        """Register default adaptation rules."""
        # High-risk rule
        self.register_adaptation_rule('high_risk', {
            'condition': lambda req, act, params: req.get('priority') == 'high' and req.get('status') != 'completed',
            'actions': [
                {
                    'strategy': 'risk_based_prioritization',
                    'parameters': {'risk_threshold': 'high'}
                },
                {
                    'strategy': 'deadline_driven_optimization',
                    'parameters': {'urgency_factor': 2.0}
                }
            ],
            'description': 'Apply risk-based prioritization and deadline-driven optimization for high-risk requirements'
        })
        
        # Resource constraint rule
        self.register_adaptation_rule('resource_constraint', {
            'condition': lambda req, act, params: params.get('resource_utilization', 0) > 0.8,
            'actions': [
                {
                    'strategy': 'resource_aware_scheduling',
                    'parameters': {'max_utilization': 0.8}
                },
                {
                    'strategy': 'compliance_consolidation',
                    'parameters': {'consolidation_threshold': 0.7}
                }
            ],
            'description': 'Apply resource-aware scheduling and compliance consolidation when resources are constrained'
        })
        
        # Regulatory change rule
        self.register_adaptation_rule('regulatory_change', {
            'condition': lambda req, act, params: params.get('regulatory_changes', False),
            'actions': [
                {
                    'strategy': 'regulatory_change_adaptation',
                    'parameters': {'change_impact_threshold': 'medium'}
                }
            ],
            'description': 'Apply regulatory change adaptation when regulatory changes are detected'
        })
        
        # Approaching deadline rule
        self.register_adaptation_rule('approaching_deadline', {
            'condition': lambda req, act, params: 
                req.get('due_date') and 
                datetime.fromisoformat(req.get('due_date')) - datetime.now() < timedelta(days=params.get('deadline_threshold', 30)),
            'actions': [
                {
                    'strategy': 'deadline_driven_optimization',
                    'parameters': {'urgency_factor': 1.5}
                }
            ],
            'description': 'Apply deadline-driven optimization for requirements with approaching deadlines'
        })
    
    def register_optimization_strategy(self, strategy_id: str, strategy_func: Callable) -> None:
        """
        Register an optimization strategy.
        
        Args:
            strategy_id: The ID of the strategy
            strategy_func: The strategy function
        """
        self.optimization_strategies[strategy_id] = strategy_func
        logger.info(f"Registered optimization strategy: {strategy_id}")
    
    def register_adaptation_rule(self, rule_id: str, rule_data: Dict[str, Any]) -> None:
        """
        Register an adaptation rule.
        
        Args:
            rule_id: The ID of the rule
            rule_data: The rule data
        """
        self.adaptation_rules[rule_id] = rule_data
        logger.info(f"Registered adaptation rule: {rule_id}")
    
    def optimize(self, 
                strategy_id: str, 
                requirements: List[Dict[str, Any]], 
                activities: List[Dict[str, Any]], 
                parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply an optimization strategy.
        
        Args:
            strategy_id: The ID of the strategy to apply
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Optional parameters for the optimization
            
        Returns:
            The optimization result
            
        Raises:
            ValueError: If the strategy does not exist
        """
        logger.info(f"Applying optimization strategy: {strategy_id}")
        
        # Check if the strategy exists
        if strategy_id not in self.optimization_strategies:
            raise ValueError(f"Optimization strategy not found: {strategy_id}")
        
        # Get the strategy function
        strategy_func = self.optimization_strategies[strategy_id]
        
        # Apply the strategy
        result = strategy_func(requirements, activities, parameters or {})
        
        # Record the optimization in history
        self._record_optimization(strategy_id, result)
        
        logger.info(f"Optimization strategy applied: {strategy_id}")
        
        return result
    
    def adapt_and_optimize(self, 
                          requirements: List[Dict[str, Any]], 
                          activities: List[Dict[str, Any]], 
                          parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Adaptively optimize compliance workflows.
        
        This method evaluates adaptation rules and applies appropriate optimization strategies
        based on the current state of requirements, activities, and parameters.
        
        Args:
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Optional parameters for the optimization
            
        Returns:
            The optimization result
        """
        logger.info("Adaptively optimizing compliance workflows")
        
        # Initialize parameters if not provided
        params = parameters or {}
        
        # Initialize the result
        result = {
            'rules_applied': [],
            'strategies_applied': [],
            'optimizations': {}
        }
        
        # Evaluate adaptation rules
        for rule_id, rule_data in self.adaptation_rules.items():
            # Get the condition function
            condition_func = rule_data.get('condition')
            
            # Check if the rule applies to any requirement
            rule_applies = False
            for req in requirements:
                if condition_func(req, activities, params):
                    rule_applies = True
                    break
            
            # If the rule applies, apply the associated optimization strategies
            if rule_applies:
                logger.info(f"Adaptation rule applies: {rule_id}")
                
                # Add the rule to the list of applied rules
                result['rules_applied'].append(rule_id)
                
                # Apply the associated optimization strategies
                for action in rule_data.get('actions', []):
                    strategy_id = action.get('strategy')
                    strategy_params = action.get('parameters', {})
                    
                    # Merge the strategy parameters with the provided parameters
                    merged_params = params.copy()
                    merged_params.update(strategy_params)
                    
                    try:
                        # Apply the strategy
                        strategy_result = self.optimize(strategy_id, requirements, activities, merged_params)
                        
                        # Add the strategy to the list of applied strategies
                        if strategy_id not in result['strategies_applied']:
                            result['strategies_applied'].append(strategy_id)
                        
                        # Add the strategy result to the optimizations
                        result['optimizations'][strategy_id] = strategy_result
                    
                    except Exception as e:
                        logger.error(f"Failed to apply optimization strategy {strategy_id}: {e}")
        
        logger.info(f"Applied {len(result['rules_applied'])} adaptation rules and {len(result['strategies_applied'])} optimization strategies")
        
        return result
    
    def get_optimization_strategies(self) -> List[str]:
        """
        Get all optimization strategies.
        
        Returns:
            List of optimization strategy IDs
        """
        return list(self.optimization_strategies.keys())
    
    def get_adaptation_rules(self) -> List[str]:
        """
        Get all adaptation rules.
        
        Returns:
            List of adaptation rule IDs
        """
        return list(self.adaptation_rules.keys())
    
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """
        Get the optimization history.
        
        Returns:
            List of optimization history entries
        """
        return self.optimization_history
    
    def _record_optimization(self, strategy_id: str, result: Dict[str, Any]) -> None:
        """
        Record an optimization in the history.
        
        Args:
            strategy_id: The ID of the strategy
            result: The optimization result
        """
        # Create the history entry
        entry = {
            'timestamp': datetime.now().isoformat(),
            'strategy_id': strategy_id,
            'result': result
        }
        
        # Add the entry to the history
        self.optimization_history.append(entry)
        
        # Save the history to disk
        self._save_optimization_history()
        
        logger.info(f"Recorded optimization in history: {strategy_id}")
    
    def _load_optimization_history(self) -> None:
        """Load optimization history from disk."""
        try:
            # Create the history file path
            history_file = os.path.join(self.data_dir, 'optimization_history.json')
            
            # Check if the file exists
            if os.path.exists(history_file):
                # Load the history from the file
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.optimization_history = json.load(f)
                
                logger.info(f"Loaded {len(self.optimization_history)} optimization history entries from disk")
            else:
                logger.info("No optimization history file found")
        
        except Exception as e:
            logger.error(f"Failed to load optimization history from disk: {e}")
    
    def _save_optimization_history(self) -> None:
        """Save optimization history to disk."""
        try:
            # Create the history file path
            history_file = os.path.join(self.data_dir, 'optimization_history.json')
            
            # Save the history to the file
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.optimization_history, f, indent=2)
            
            logger.info(f"Saved {len(self.optimization_history)} optimization history entries to disk")
        
        except Exception as e:
            logger.error(f"Failed to save optimization history to disk: {e}")
    
    # Default optimization strategies
    
    def _optimize_risk_based_prioritization(self, 
                                          requirements: List[Dict[str, Any]], 
                                          activities: List[Dict[str, Any]], 
                                          parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize based on risk prioritization.
        
        Args:
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Parameters for the optimization
            
        Returns:
            The optimization result
        """
        logger.info("Optimizing based on risk prioritization")
        
        # Get the risk threshold from parameters
        risk_threshold = parameters.get('risk_threshold', 'medium')
        
        # Map risk levels to numeric values
        risk_values = {
            'low': 1,
            'medium': 2,
            'high': 3
        }
        
        # Calculate risk threshold value
        threshold_value = risk_values.get(risk_threshold, 2)
        
        # Identify high-risk requirements
        high_risk_requirements = []
        for req in requirements:
            # Get the priority (risk level)
            priority = req.get('priority', 'medium')
            
            # Convert to numeric value
            risk_value = risk_values.get(priority, 2)
            
            # Check if the risk is above the threshold
            if risk_value >= threshold_value:
                high_risk_requirements.append(req)
        
        # Identify activities for high-risk requirements
        high_risk_activities = []
        for act in activities:
            req_id = act.get('requirement_id')
            if req_id:
                # Find the requirement
                req = next((r for r in requirements if r.get('id') == req_id), None)
                if req and req in high_risk_requirements:
                    high_risk_activities.append(act)
        
        # Create the optimization result
        result = {
            'strategy': 'risk_based_prioritization',
            'risk_threshold': risk_threshold,
            'total_requirements': len(requirements),
            'high_risk_requirements': len(high_risk_requirements),
            'high_risk_activities': len(high_risk_activities),
            'recommendations': []
        }
        
        # Generate recommendations
        if high_risk_requirements:
            result['recommendations'].append({
                'type': 'prioritize_high_risk',
                'count': len(high_risk_requirements),
                'message': f"Prioritize {len(high_risk_requirements)} high-risk requirements"
            })
        
        logger.info("Risk-based prioritization optimization completed")
        
        return result
    
    def _optimize_resource_aware_scheduling(self, 
                                          requirements: List[Dict[str, Any]], 
                                          activities: List[Dict[str, Any]], 
                                          parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize scheduling based on resource availability.
        
        Args:
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Parameters for the optimization
            
        Returns:
            The optimization result
        """
        logger.info("Optimizing scheduling based on resource availability")
        
        # Get the maximum utilization from parameters
        max_utilization = parameters.get('max_utilization', 0.8)
        
        # Get the list of assigned people
        assigned_people = set()
        for activity in activities:
            assigned_to = activity.get('assigned_to')
            if assigned_to:
                assigned_people.add(assigned_to)
        
        # Count activities per person
        activities_per_person = {}
        for person in assigned_people:
            activities_per_person[person] = len([a for a in activities if a.get('assigned_to') == person])
        
        # Calculate utilization per person (assuming each activity takes 1 unit of time)
        utilization_per_person = {}
        for person, count in activities_per_person.items():
            utilization_per_person[person] = min(count / 10, 1.0)  # Normalize to 0-1
        
        # Identify overloaded people
        overloaded_people = [p for p, util in utilization_per_person.items() if util > max_utilization]
        
        # Create the optimization result
        result = {
            'strategy': 'resource_aware_scheduling',
            'max_utilization': max_utilization,
            'assigned_people': list(assigned_people),
            'activities_per_person': activities_per_person,
            'utilization_per_person': utilization_per_person,
            'overloaded_people': overloaded_people,
            'recommendations': []
        }
        
        # Generate recommendations
        for person in overloaded_people:
            result['recommendations'].append({
                'type': 'reschedule',
                'person': person,
                'current_utilization': utilization_per_person[person],
                'target_utilization': max_utilization,
                'activities_to_reschedule': int((utilization_per_person[person] - max_utilization) * 10)
            })
        
        logger.info("Resource-aware scheduling optimization completed")
        
        return result
    
    def _optimize_regulatory_change_adaptation(self, 
                                             requirements: List[Dict[str, Any]], 
                                             activities: List[Dict[str, Any]], 
                                             parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize adaptation to regulatory changes.
        
        Args:
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Parameters for the optimization
            
        Returns:
            The optimization result
        """
        logger.info("Optimizing adaptation to regulatory changes")
        
        # Get the change impact threshold from parameters
        change_impact_threshold = parameters.get('change_impact_threshold', 'medium')
        
        # Map impact levels to numeric values
        impact_values = {
            'low': 1,
            'medium': 2,
            'high': 3
        }
        
        # Calculate impact threshold value
        threshold_value = impact_values.get(change_impact_threshold, 2)
        
        # Get regulatory changes from parameters
        regulatory_changes = parameters.get('regulatory_changes', [])
        
        # If no changes provided, use a placeholder
        if not regulatory_changes:
            regulatory_changes = [
                {
                    'framework': 'GDPR',
                    'description': 'Updated data breach notification requirements',
                    'impact': 'medium'
                }
            ]
        
        # Identify affected requirements
        affected_requirements = []
        for req in requirements:
            # Get the framework
            framework = req.get('framework', '')
            
            # Check if the requirement is affected by any regulatory change
            for change in regulatory_changes:
                if change.get('framework') == framework:
                    # Get the impact
                    impact = change.get('impact', 'medium')
                    
                    # Convert to numeric value
                    impact_value = impact_values.get(impact, 2)
                    
                    # Check if the impact is above the threshold
                    if impact_value >= threshold_value:
                        affected_requirements.append(req)
                        break
        
        # Create the optimization result
        result = {
            'strategy': 'regulatory_change_adaptation',
            'change_impact_threshold': change_impact_threshold,
            'regulatory_changes': regulatory_changes,
            'affected_requirements': len(affected_requirements),
            'recommendations': []
        }
        
        # Generate recommendations
        if affected_requirements:
            result['recommendations'].append({
                'type': 'update_requirements',
                'count': len(affected_requirements),
                'message': f"Update {len(affected_requirements)} requirements affected by regulatory changes"
            })
        
        logger.info("Regulatory change adaptation optimization completed")
        
        return result
    
    def _optimize_compliance_consolidation(self, 
                                         requirements: List[Dict[str, Any]], 
                                         activities: List[Dict[str, Any]], 
                                         parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize compliance consolidation.
        
        Args:
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Parameters for the optimization
            
        Returns:
            The optimization result
        """
        logger.info("Optimizing compliance consolidation")
        
        # Get the consolidation threshold from parameters
        consolidation_threshold = parameters.get('consolidation_threshold', 0.7)
        
        # Group requirements by framework
        requirements_by_framework = {}
        for req in requirements:
            framework = req.get('framework', 'unknown')
            if framework not in requirements_by_framework:
                requirements_by_framework[framework] = []
            requirements_by_framework[framework].append(req)
        
        # Identify consolidation opportunities
        consolidation_opportunities = []
        
        # Check for similar requirements across frameworks
        for framework1, reqs1 in requirements_by_framework.items():
            for framework2, reqs2 in requirements_by_framework.items():
                if framework1 < framework2:  # Avoid duplicate comparisons
                    for req1 in reqs1:
                        for req2 in reqs2:
                            # Simple similarity check (in a real implementation, this would be more sophisticated)
                            name1 = req1.get('name', '').lower()
                            name2 = req2.get('name', '').lower()
                            desc1 = req1.get('description', '').lower()
                            desc2 = req2.get('description', '').lower()
                            
                            # Calculate similarity
                            name_similarity = self._calculate_similarity(name1, name2)
                            desc_similarity = self._calculate_similarity(desc1, desc2)
                            
                            # Average similarity
                            avg_similarity = (name_similarity + desc_similarity) / 2
                            
                            # Check if similarity is above threshold
                            if avg_similarity >= consolidation_threshold:
                                consolidation_opportunities.append({
                                    'requirement1': req1,
                                    'requirement2': req2,
                                    'framework1': framework1,
                                    'framework2': framework2,
                                    'similarity': avg_similarity
                                })
        
        # Create the optimization result
        result = {
            'strategy': 'compliance_consolidation',
            'consolidation_threshold': consolidation_threshold,
            'frameworks': list(requirements_by_framework.keys()),
            'requirements_by_framework': {k: len(v) for k, v in requirements_by_framework.items()},
            'consolidation_opportunities': len(consolidation_opportunities),
            'recommendations': []
        }
        
        # Generate recommendations
        if consolidation_opportunities:
            result['recommendations'].append({
                'type': 'consolidate_requirements',
                'count': len(consolidation_opportunities),
                'message': f"Consolidate {len(consolidation_opportunities)} similar requirements across frameworks"
            })
        
        logger.info("Compliance consolidation optimization completed")
        
        return result
    
    def _optimize_deadline_driven(self, 
                                requirements: List[Dict[str, Any]], 
                                activities: List[Dict[str, Any]], 
                                parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Optimize based on deadlines.
        
        Args:
            requirements: List of requirements to optimize
            activities: List of activities to optimize
            parameters: Parameters for the optimization
            
        Returns:
            The optimization result
        """
        logger.info("Optimizing based on deadlines")
        
        # Get the urgency factor from parameters
        urgency_factor = parameters.get('urgency_factor', 1.0)
        
        # Get the deadline threshold from parameters (in days)
        deadline_threshold = parameters.get('deadline_threshold', 30)
        
        # Identify requirements with approaching deadlines
        approaching_deadlines = []
        for req in requirements:
            # Get the due date
            due_date_str = req.get('due_date')
            if due_date_str:
                try:
                    # Parse the due date
                    due_date = datetime.fromisoformat(due_date_str)
                    
                    # Calculate days until due
                    days_until_due = (due_date - datetime.now()).days
                    
                    # Check if the deadline is approaching
                    if 0 < days_until_due < deadline_threshold:
                        approaching_deadlines.append({
                            'requirement': req,
                            'days_until_due': days_until_due,
                            'urgency': (deadline_threshold - days_until_due) / deadline_threshold * urgency_factor
                        })
                except ValueError:
                    logger.warning(f"Invalid due date format: {due_date_str}")
        
        # Sort by urgency (descending)
        approaching_deadlines.sort(key=lambda x: x['urgency'], reverse=True)
        
        # Create the optimization result
        result = {
            'strategy': 'deadline_driven_optimization',
            'urgency_factor': urgency_factor,
            'deadline_threshold': deadline_threshold,
            'approaching_deadlines': len(approaching_deadlines),
            'recommendations': []
        }
        
        # Generate recommendations
        for deadline in approaching_deadlines:
            req = deadline['requirement']
            result['recommendations'].append({
                'type': 'prioritize_deadline',
                'requirement_id': req.get('id'),
                'requirement_name': req.get('name'),
                'days_until_due': deadline['days_until_due'],
                'urgency': deadline['urgency'],
                'message': f"Prioritize '{req.get('name')}' due in {deadline['days_until_due']} days"
            })
        
        logger.info("Deadline-driven optimization completed")
        
        return result
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two texts.
        
        Args:
            text1: First text
            text2: Second text
            
        Returns:
            Similarity score (0-1)
        """
        # Simple similarity calculation based on common words
        if not text1 or not text2:
            return 0.0
        
        # Split into words
        words1 = set(text1.split())
        words2 = set(text2.split())
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
