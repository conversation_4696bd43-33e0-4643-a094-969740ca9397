/**
 * NovaFuse Trust Automation Demo
 *
 * This script demonstrates how NovaFuse automates trust - turning what used to take
 * teams of people into an automated, continuous process.
 */

const chalk = require('chalk');

// Simple utility to create a delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Print with typing effect
async function typePrint(text, speed = 10) {
  for (let i = 0; i < text.length; i++) {
    process.stdout.write(text[i]);
    await delay(speed);
  }
  console.log('');
}

// Main demo function
async function runDemo() {
  console.clear();

  // Introduction
  await typePrint(chalk.blue.bold('=== NovaFuse: The Trust Automation Platform ==='), 20);
  await delay(500);
  await typePrint(chalk.white.bold('Trust, Automated.'), 30);
  await delay(1000);
  await typePrint(chalk.gray('"In a world where'));
  await delay(300);
  await typePrint(chalk.gray('1 breach can bankrupt,'));
  await delay(300);
  await typePrint(chalk.gray('1 audit can paralyze,'));
  await delay(300);
  await typePrint(chalk.gray('1 mistake can destroy,'));
  await delay(300);
  await typePrint(chalk.gray('We built NovaFuse to be the exception:'));
  await delay(300);
  await typePrint(chalk.gray('The platform that never sleeps,'));
  await delay(300);
  await typePrint(chalk.gray('never guesses,'));
  await delay(300);
  await typePrint(chalk.gray('never forgets.'));
  await delay(300);
  await typePrint(chalk.gray('Trust isn\'t earned here—it\'s engineered."'));
  await delay(1000);
  await typePrint('This demo shows how NovaFuse turns what used to take teams of people into an automated process.');
  await delay(1000);

  // Traditional vs. NovaFuse comparison
  console.log('\n' + chalk.yellow.bold('TRADITIONAL APPROACH vs. NOVAFUSE'));
  await delay(500);

  // Create a table for comparison
  const tasks = [
    { name: 'Framework Interpretation', traditional: '2-3 weeks', novafuse: '0.07 seconds' },
    { name: 'Control Mapping', traditional: '1-2 months', novafuse: '0.12 seconds' },
    { name: 'Evidence Collection', traditional: '3-6 months', novafuse: '5-10 minutes' },
    { name: 'Gap Analysis', traditional: '2-4 weeks', novafuse: '0.35 seconds' },
    { name: 'Audit Preparation', traditional: '1-2 months', novafuse: '1.2 seconds' },
    { name: 'Regulatory Updates', traditional: '1-3 months', novafuse: 'Continuous' }
  ];

  console.log(chalk.white('┌─────────────────────────┬─────────────────┬─────────────────┐'));
  console.log(chalk.white('│ Task                    │ Traditional     │ NovaFuse        │'));
  console.log(chalk.white('├─────────────────────────┼─────────────────┼─────────────────┤'));

  for (const task of tasks) {
    const paddedName = task.name.padEnd(23);
    const paddedTraditional = task.traditional.padEnd(15);
    const paddedNovaFuse = task.novafuse.padEnd(15);

    await delay(300);
    console.log(chalk.white(`│ ${paddedName} │ `) +
                chalk.red(`${paddedTraditional}`) +
                chalk.white(' │ ') +
                chalk.green(`${paddedNovaFuse}`) +
                chalk.white(' │'));
  }

  console.log(chalk.white('└─────────────────────────┴─────────────────┴─────────────────┘'));
  await delay(1500);

  // Demonstration of Trust Automation
  console.log('\n' + chalk.yellow.bold('TRUST AUTOMATION IN ACTION'));
  await delay(500);

  // Step 1: New Regulatory Requirement
  console.log('\n' + chalk.white.bold('Step 1: New Regulatory Requirement Detected'));
  await delay(500);
  await typePrint(chalk.gray('NovaFuse monitoring detects new GDPR amendment (Article 28.7) regarding processor obligations'));
  await delay(300);
  await typePrint(chalk.gray('Traditional approach: Schedule legal team review → 2-3 weeks'));
  await delay(300);

  // Show NovaFuse processing
  process.stdout.write(chalk.white('NovaFuse processing: '));
  for (let i = 0; i < 20; i++) {
    process.stdout.write(chalk.green('█'));
    await delay(50);
  }
  console.log(chalk.green(' COMPLETE (0.13 seconds)'));
  await delay(1000);

  // Step 2: Framework Mapping
  console.log('\n' + chalk.white.bold('Step 2: Cross-Framework Mapping'));
  await delay(500);
  await typePrint(chalk.gray('New requirement needs to be mapped to existing controls across frameworks'));
  await delay(300);
  await typePrint(chalk.gray('Traditional approach: Compliance team analysis → 1-2 weeks'));
  await delay(300);

  // Show NovaFuse processing
  process.stdout.write(chalk.white('NovaFuse processing: '));
  for (let i = 0; i < 20; i++) {
    process.stdout.write(chalk.green('█'));
    await delay(50);
  }
  console.log(chalk.green(' COMPLETE (0.21 seconds)'));

  // Show mapping results
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  Cross-Framework Mapping Results                    │
│  ────────────────────────────────                   │
│                                                     │
│  GDPR Article 28.7 maps to:                         │
│    ✓ HIPAA 164.308(b)(1) - Business Associate       │
│      Contracts                                      │
│                                                     │
│    ✓ SOC 2 CC7.1 - Third-party risk management      │
│                                                     │
│    ✓ ISO 27001 A.15.1 - Supplier relationships      │
│                                                     │
│    ✓ PCI DSS 12.8 - Third-party service providers   │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(1500);

  // Step 3: Control Implementation
  console.log('\n' + chalk.white.bold('Step 3: Control Implementation'));
  await delay(500);
  await typePrint(chalk.gray('New controls need to be implemented to address the requirement'));
  await delay(300);
  await typePrint(chalk.gray('Traditional approach: Development team implementation → 2-4 weeks'));
  await delay(300);

  // Show NovaFuse processing
  process.stdout.write(chalk.white('NovaFuse processing: '));
  for (let i = 0; i < 20; i++) {
    process.stdout.write(chalk.green('█'));
    await delay(50);
  }
  console.log(chalk.green(' COMPLETE (1.45 seconds)'));

  // Show implementation results
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  Control Implementation                             │
│  ─────────────────────                              │
│                                                     │
│  ✓ API endpoint created: /api/vendor-assessment     │
│                                                     │
│  ✓ Database schema updated                          │
│                                                     │
│  ✓ Automated tests created (15 tests)               │
│                                                     │
│  ✓ Documentation generated                          │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(1500);

  // Step 4: Evidence Collection
  console.log('\n' + chalk.white.bold('Step 4: Evidence Collection'));
  await delay(500);
  await typePrint(chalk.gray('Evidence needs to be collected to demonstrate compliance'));
  await delay(300);
  await typePrint(chalk.gray('Traditional approach: Manual evidence gathering → 1-3 months'));
  await delay(300);

  // Show NovaFuse processing
  process.stdout.write(chalk.white('NovaFuse processing: '));
  for (let i = 0; i < 20; i++) {
    process.stdout.write(chalk.green('█'));
    await delay(50);
  }
  console.log(chalk.green(' COMPLETE (3.27 minutes)'));

  // Show evidence results
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  Automated Evidence Collection                      │
│  ───────────────────────────                        │
│                                                     │
│  ✓ Vendor contracts scanned: 127                    │
│                                                     │
│  ✓ Data processing agreements analyzed: 42          │
│                                                     │
│  ✓ Vendor assessment questionnaires: 38             │
│                                                     │
│  ✓ Evidence items collected: 215                    │
│                                                     │
│  ✓ Compliance score: 97.3%                          │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(1500);

  // Step 5: Audit Readiness
  console.log('\n' + chalk.white.bold('Step 5: Audit Readiness'));
  await delay(500);
  await typePrint(chalk.gray('Preparing for audit to demonstrate compliance'));
  await delay(300);
  await typePrint(chalk.gray('Traditional approach: Audit preparation → 1-2 months'));
  await delay(300);

  // Show NovaFuse processing
  process.stdout.write(chalk.white('NovaFuse processing: '));
  for (let i = 0; i < 20; i++) {
    process.stdout.write(chalk.green('█'));
    await delay(50);
  }
  console.log(chalk.green(' COMPLETE (0.87 seconds)'));

  // Show audit readiness results
  await delay(500);
  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  Audit Readiness Report                             │
│  ────────────────────                               │
│                                                     │
│  ✓ Compliance evidence package generated            │
│                                                     │
│  ✓ Cross-framework mapping documentation            │
│                                                     │
│  ✓ Control implementation verification              │
│                                                     │
│  ✓ Audit-ready reports generated for:               │
│    - GDPR                                           │
│    - HIPAA                                          │
│    - SOC 2                                          │
│    - ISO 27001                                      │
│    - PCI DSS                                        │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(1500);

  // Trust Automation Summary
  console.log('\n' + chalk.yellow.bold('TRUST AUTOMATION SUMMARY'));
  await delay(500);

  console.log(chalk.white(`
┌─────────────────────────────────────────────────────┐
│                                                     │
│  NovaFuse Trust Automation                          │
│  ─────────────────────────                          │
│                                                     │
│  Traditional Approach:                              │
│    Total time: 3-6 months                           │
│    Resources: Multiple teams                        │
│    Cost: $150,000 - $300,000                        │
│                                                     │
│  NovaFuse Approach:                                 │
│    Total time: 3.5 minutes                          │
│    Resources: Automated                             │
│    Cost: Fraction of traditional approach           │
│                                                     │
│  Time Savings: 99.8%                                │
│  Cost Savings: 99.5%                                │
│  Trust Level: Continuous & Verifiable               │
│                                                     │
└─────────────────────────────────────────────────────┘
`));
  await delay(2000);

  // Conclusion
  console.log('\n' + chalk.blue.bold('=== Trust Automation Demo Complete ==='));
  await delay(500);
  console.log(chalk.white(`
NovaFuse: Trust, Automated.

From 300-Day Audits → 3-Click Compliance

This demonstration shows how NovaFuse transforms the traditional, manual compliance
process into an automated, continuous system that:

1. Detects regulatory changes automatically
2. Maps requirements across frameworks instantly
3. Implements controls systematically
4. Collects evidence continuously
5. Maintains audit readiness at all times

The result is not just faster compliance, but a fundamentally different approach
to trust - one that's automated, verifiable, and continuous.
`));
}

// Check if chalk is installed, if not, suggest installing it
try {
  require.resolve('chalk');
  // Run the demo
  runDemo().catch(console.error);
} catch (e) {
  console.error('This demo requires the "chalk" package. Please install it with:');
  console.error('npm install chalk');
}
