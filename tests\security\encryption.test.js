/**
 * NovaConnect Security Tests - Encryption
 * 
 * These tests validate the encryption capabilities of NovaConnect,
 * ensuring sensitive data is properly protected.
 */

const axios = require('axios');
const crypto = require('crypto');
const { performance } = require('perf_hooks');

// Helper function to measure execution time
const measureExecutionTime = async (fn) => {
  const start = performance.now();
  const result = await fn();
  const end = performance.now();
  const duration = end - start;
  
  // Record response time in global metrics
  if (global.recordResponseTime) {
    global.recordResponseTime(duration);
  }
  
  return { result, duration };
};

// Test configuration
const config = {
  baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
  encryptionUrl: '/api/security/encrypt',
  decryptionUrl: '/api/security/decrypt',
  keyManagementUrl: '/api/security/keys'
};

describe('Encryption Security Tests', () => {
  // Set a longer timeout for security tests
  jest.setTimeout(30000);
  
  // Test data
  let testKeyId;
  let encryptedData;
  
  // Test key management
  describe('Key Management', () => {
    it('should generate a new encryption key', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.keyManagementUrl}/generate`, {
          keyType: 'AES-256-GCM',
          description: 'Test encryption key'
        });
      });
      
      expect(result.status).toBe(201);
      expect(result.data).toHaveProperty('id');
      expect(result.data).toHaveProperty('type', 'AES-256-GCM');
      expect(result.data).toHaveProperty('createdAt');
      expect(duration).toBeLessThan(500); // Should complete in less than 500ms
      
      // Save key ID for later tests
      testKeyId = result.data.id;
      
      console.log(`Key generation completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should retrieve key metadata', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.get(`${config.baseUrl}${config.keyManagementUrl}/${testKeyId}`);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('id', testKeyId);
      expect(result.data).toHaveProperty('type', 'AES-256-GCM');
      expect(result.data).toHaveProperty('createdAt');
      expect(result.data).not.toHaveProperty('key'); // Should not return the actual key
      expect(duration).toBeLessThan(200); // Should complete in less than 200ms
      
      console.log(`Key metadata retrieval completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test data encryption
  describe('Data Encryption', () => {
    it('should encrypt sensitive data', async () => {
      const sensitiveData = {
        apiKey: 'test-api-key-12345',
        password: 'test-password-secure',
        credentials: {
          username: 'test-user',
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token'
        }
      };
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.encryptionUrl}`, {
          data: sensitiveData,
          keyId: testKeyId
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('encryptedData');
      expect(result.data).toHaveProperty('iv');
      expect(result.data).toHaveProperty('tag');
      expect(result.data).toHaveProperty('keyId', testKeyId);
      expect(duration).toBeLessThan(200); // Should complete in less than 200ms
      
      // Save encrypted data for decryption test
      encryptedData = result.data;
      
      console.log(`Data encryption completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should decrypt encrypted data', async () => {
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.decryptionUrl}`, encryptedData);
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('apiKey', 'test-api-key-12345');
      expect(result.data).toHaveProperty('password', 'test-password-secure');
      expect(result.data).toHaveProperty('credentials');
      expect(result.data.credentials).toHaveProperty('username', 'test-user');
      expect(result.data.credentials).toHaveProperty('accessToken', 'test-access-token');
      expect(result.data.credentials).toHaveProperty('refreshToken', 'test-refresh-token');
      expect(duration).toBeLessThan(200); // Should complete in less than 200ms
      
      console.log(`Data decryption completed in ${duration.toFixed(2)} ms`);
    });
  });
  
  // Test encryption performance
  describe('Encryption Performance', () => {
    it('should encrypt large data efficiently', async () => {
      // Generate a large data object (1MB)
      const largeData = {
        data: crypto.randomBytes(1024 * 1024).toString('base64')
      };
      
      const { result, duration } = await measureExecutionTime(async () => {
        return await axios.post(`${config.baseUrl}${config.encryptionUrl}`, {
          data: largeData,
          keyId: testKeyId
        });
      });
      
      expect(result.status).toBe(200);
      expect(result.data).toHaveProperty('encryptedData');
      expect(duration).toBeLessThan(1000); // Should encrypt 1MB in less than 1 second
      
      console.log(`Large data encryption (1MB) completed in ${duration.toFixed(2)} ms`);
    });
    
    it('should handle concurrent encryption requests', async () => {
      const concurrentRequests = 10;
      const sensitiveData = {
        apiKey: 'test-api-key-12345',
        password: 'test-password-secure'
      };
      
      const encryptionFn = () => axios.post(`${config.baseUrl}${config.encryptionUrl}`, {
        data: sensitiveData,
        keyId: testKeyId
      });
      
      const { result, duration } = await measureExecutionTime(async () => {
        const promises = Array(concurrentRequests).fill(0).map(() => encryptionFn());
        return await Promise.all(promises);
      });
      
      expect(result).toHaveLength(concurrentRequests);
      result.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.data).toHaveProperty('encryptedData');
      });
      
      // Average time per encryption should be less than 50ms
      const averageTime = duration / concurrentRequests;
      expect(averageTime).toBeLessThan(50);
      
      console.log(`${concurrentRequests} concurrent encryptions completed in ${duration.toFixed(2)} ms`);
      console.log(`Average time per encryption: ${averageTime.toFixed(2)} ms`);
    });
  });
  
  // Test encryption security
  describe('Encryption Security', () => {
    it('should reject decryption with invalid tag', async () => {
      // Create a copy of the encrypted data with a modified tag
      const invalidData = {
        ...encryptedData,
        tag: Buffer.from(crypto.randomBytes(16)).toString('base64')
      };
      
      try {
        await axios.post(`${config.baseUrl}${config.decryptionUrl}`, invalidData);
        // If we get here, the test failed
        fail('Decryption should have failed with invalid tag');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toMatch(/integrity/i);
      }
    });
    
    it('should reject decryption with invalid key ID', async () => {
      // Create a copy of the encrypted data with a non-existent key ID
      const invalidData = {
        ...encryptedData,
        keyId: 'non-existent-key-id'
      };
      
      try {
        await axios.post(`${config.baseUrl}${config.decryptionUrl}`, invalidData);
        // If we get here, the test failed
        fail('Decryption should have failed with invalid key ID');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data).toHaveProperty('error');
        expect(error.response.data.error).toMatch(/key/i);
      }
    });
  });
});
