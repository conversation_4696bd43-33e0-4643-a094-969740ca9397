"""
Demo script for the Universal Compliance Training Optimizer (UCTO Training).

This script demonstrates how to use the UCTO Training to create and manage
compliance training programs and track learner progress.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTO Training
from ucto_training import TrainingManager, LearnerManager

def main():
    """Run the UCTO Training demo."""
    logger.info("Starting UCTO Training demo")
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'ucto_training_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize the managers
    training_manager = TrainingManager()
    learner_manager = LearnerManager()
    
    # Create a training program
    logger.info("Creating a training program")
    
    gdpr_program = training_manager.create_program({
        'name': 'GDPR Compliance Training',
        'description': 'Training program for GDPR compliance',
        'framework': 'GDPR',
        'target_audience': ['all_employees'],
        'status': 'active'
    })
    
    logger.info(f"Training program created: {gdpr_program.get('id')}")
    
    # Save the program to a file
    program_path = os.path.join(output_dir, f"{gdpr_program.get('id')}.json")
    
    with open(program_path, 'w', encoding='utf-8') as f:
        json.dump(gdpr_program, f, indent=2)
    
    logger.info(f"Saved program to {program_path}")
    
    # Add modules to the program
    logger.info("Adding modules to the program")
    
    module1 = {
        'name': 'Introduction to GDPR',
        'description': 'Overview of GDPR principles and requirements',
        'content_type': 'text',
        'content': 'The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy...',
        'duration': 30,
        'order': 1
    }
    
    module2 = {
        'name': 'Data Subject Rights',
        'description': 'Understanding data subject rights under GDPR',
        'content_type': 'text',
        'content': 'Under GDPR, data subjects have various rights including the right to access, right to rectification...',
        'duration': 45,
        'order': 2
    }
    
    module3 = {
        'name': 'Data Breach Handling',
        'description': 'Procedures for handling data breaches',
        'content_type': 'text',
        'content': 'In the event of a data breach, organizations must follow specific procedures...',
        'duration': 60,
        'order': 3
    }
    
    training_manager.add_module_to_program(gdpr_program['id'], module1)
    training_manager.add_module_to_program(gdpr_program['id'], module2)
    training_manager.add_module_to_program(gdpr_program['id'], module3)
    
    logger.info("Modules added to the program")
    
    # Get the updated program
    updated_program = training_manager.get_program(gdpr_program['id'])
    
    # Save the updated program to a file
    updated_program_path = os.path.join(output_dir, f"{updated_program.get('id')}_updated.json")
    
    with open(updated_program_path, 'w', encoding='utf-8') as f:
        json.dump(updated_program, f, indent=2)
    
    logger.info(f"Saved updated program to {updated_program_path}")
    
    # Create learners
    logger.info("Creating learners")
    
    learner1 = learner_manager.create_learner({
        'name': 'John Doe',
        'email': '<EMAIL>',
        'role': 'employee',
        'department': 'IT'
    })
    
    learner2 = learner_manager.create_learner({
        'name': 'Jane Smith',
        'email': '<EMAIL>',
        'role': 'manager',
        'department': 'HR'
    })
    
    logger.info(f"Learners created: {learner1.get('id')}, {learner2.get('id')}")
    
    # Save the learners to files
    learner1_path = os.path.join(output_dir, f"{learner1.get('id')}.json")
    learner2_path = os.path.join(output_dir, f"{learner2.get('id')}.json")
    
    with open(learner1_path, 'w', encoding='utf-8') as f:
        json.dump(learner1, f, indent=2)
    
    with open(learner2_path, 'w', encoding='utf-8') as f:
        json.dump(learner2, f, indent=2)
    
    logger.info(f"Saved learners to {learner1_path} and {learner2_path}")
    
    # Record learner progress
    logger.info("Recording learner progress")
    
    # Get the first module ID
    module1_id = updated_program['modules'][0]['id']
    
    # Record progress for learner 1
    progress1 = learner_manager.record_progress({
        'learner_id': learner1['id'],
        'program_id': updated_program['id'],
        'module_id': module1_id,
        'status': 'completed',
        'score': 90,
        'completed_at': '2023-10-15T14:30:00Z'
    })
    
    # Record progress for learner 2
    progress2 = learner_manager.record_progress({
        'learner_id': learner2['id'],
        'program_id': updated_program['id'],
        'module_id': module1_id,
        'status': 'in_progress'
    })
    
    logger.info(f"Progress recorded: {progress1.get('id')}, {progress2.get('id')}")
    
    # Save the progress to files
    progress1_path = os.path.join(output_dir, f"{progress1.get('id')}.json")
    progress2_path = os.path.join(output_dir, f"{progress2.get('id')}.json")
    
    with open(progress1_path, 'w', encoding='utf-8') as f:
        json.dump(progress1, f, indent=2)
    
    with open(progress2_path, 'w', encoding='utf-8') as f:
        json.dump(progress2, f, indent=2)
    
    logger.info(f"Saved progress to {progress1_path} and {progress2_path}")
    
    # Get learner progress
    logger.info("Getting learner progress")
    
    learner1_progress = learner_manager.get_learner_progress(learner1['id'])
    
    # Save the learner progress to a file
    learner1_progress_path = os.path.join(output_dir, f"{learner1.get('id')}_progress.json")
    
    with open(learner1_progress_path, 'w', encoding='utf-8') as f:
        json.dump(learner1_progress, f, indent=2)
    
    logger.info(f"Saved learner progress to {learner1_progress_path}")
    
    # Get program progress
    logger.info("Getting program progress")
    
    program_progress = learner_manager.get_program_progress(updated_program['id'])
    
    # Save the program progress to a file
    program_progress_path = os.path.join(output_dir, f"{updated_program.get('id')}_progress.json")
    
    with open(program_progress_path, 'w', encoding='utf-8') as f:
        json.dump(program_progress, f, indent=2)
    
    logger.info(f"Saved program progress to {program_progress_path}")
    
    logger.info("UCTO Training demo completed successfully")

if __name__ == "__main__":
    main()
