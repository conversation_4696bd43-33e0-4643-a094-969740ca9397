import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import FloatingNovaConcierge from '../../components/FloatingNovaConcierge';

// Mock setTimeout and clearTimeout
jest.useFakeTimers();

// Mock scrollIntoView which is not available in JSDOM
window.HTMLElement.prototype.scrollIntoView = jest.fn();

describe('FloatingNovaConcierge', () => {
  beforeEach(() => {
    // Clear mocks before each test
    jest.clearAllMocks();
  });

  it('renders the closed state initially', () => {
    render(<FloatingNovaConcierge />);

    // The chat window should be closed initially
    expect(screen.queryByText('NovaConcierge')).not.toBeInTheDocument();

    // The floating button should be visible
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('opens the chat window when the button is clicked', () => {
    render(<FloatingNovaConcierge />);

    // Find and click the floating button
    const button = screen.getByRole('button');
    fireEvent.click(button);

    // The chat window should now be open
    expect(screen.getByText('NovaConcierge')).toBeInTheDocument();
    expect(screen.getByText('Your API integration assistant')).toBeInTheDocument();

    // The initial message should be displayed
    expect(screen.getByText(/Hello! I'm NovaConcierge/)).toBeInTheDocument();

    // Check if scrollToBottom was called
    expect(window.HTMLElement.prototype.scrollIntoView).toHaveBeenCalled();
  });

  it('closes the chat window when the close button is clicked', () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Find and click the close button
    const closeButton = screen.getByLabelText('Close NovaConcierge');
    fireEvent.click(closeButton);

    // The chat window should now be closed
    expect(screen.queryByText('NovaConcierge')).not.toBeInTheDocument();
  });

  it('allows sending a message', async () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Type a message in the input field
    const input = screen.getByPlaceholderText('Ask about API integration...');
    fireEvent.change(input, { target: { value: 'test message' } });

    // Submit the form (find it by its class)
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // The user message should be added to the chat
    expect(screen.getByText('test message')).toBeInTheDocument();

    // The input should be cleared
    expect(input.value).toBe('');

    // Fast-forward timers to simulate the AI response
    jest.advanceTimersByTime(2000);

    // The AI should respond
    await waitFor(() => {
      expect(screen.getAllByText(/I'd be happy to show you what I can do/)).toHaveLength(1);
    });

    // Check if suggestions are rendered
    expect(screen.getByText('Compare NovaFuse to competitors')).toBeInTheDocument();
    expect(screen.getByText('Explore success stories')).toBeInTheDocument();
    expect(screen.getByText('Calculate your potential ROI')).toBeInTheDocument();
    expect(screen.getByText('Learn about Partner Empowerment')).toBeInTheDocument();
    expect(screen.getByText('Find the right API for your needs')).toBeInTheDocument();

    // Click on a suggestion
    fireEvent.click(screen.getByText('Compare NovaFuse to competitors'));

    // Check if the suggestion was added as a user message
    expect(screen.getAllByText('Compare NovaFuse to competitors')).toHaveLength(2);

    // Check if the AI responded to the suggestion
    expect(screen.getByText(/I'll help you compare novafuse to competitors/i)).toBeInTheDocument();
  });

  it('handles empty input submission', () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Submit the form with empty input
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // No new messages should be added
    expect(screen.getAllByText(/Hello! I'm NovaConcierge/)).toHaveLength(1);
  });

  it('disables input while processing', async () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Type a message in the input field
    const input = screen.getByPlaceholderText('Ask about API integration...');
    fireEvent.change(input, { target: { value: 'test message' } });

    // Submit the form
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // Input should be disabled while processing
    expect(input).toBeDisabled();

    // Fast-forward timers to simulate the AI response
    jest.advanceTimersByTime(2000);

    // Input should be enabled again after processing
    await waitFor(() => {
      expect(input).not.toBeDisabled();
    });
  });

  it('responds with comparison data when asked about competitors', async () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Type a message about competitors
    const input = screen.getByPlaceholderText('Ask about API integration...');
    fireEvent.change(input, { target: { value: 'How does NovaFuse compare to competitors?' } });

    // Submit the form
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // Fast-forward timers to simulate the AI response
    jest.advanceTimersByTime(2000);

    // Check if comparison data is rendered
    await waitFor(() => {
      expect(screen.getByText('NovaFuse vs. Traditional GRC Solutions')).toBeInTheDocument();
      expect(screen.getByText('3,142x faster data normalization (0.07ms vs 220ms per finding)')).toBeInTheDocument();
      expect(screen.getByText('Multi-cloud support vs. single-cloud coverage')).toBeInTheDocument();
    });
  });

  it('responds with partner information when asked about partners', async () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Type a message about partners
    const input = screen.getByPlaceholderText('Ask about API integration...');
    fireEvent.change(input, { target: { value: 'Tell me about partner empowerment' } });

    // Submit the form
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // Fast-forward timers to simulate the AI response
    jest.advanceTimersByTime(2000);

    // Check if partner information is rendered
    await waitFor(() => {
      expect(screen.getByText('Partner Empowerment Program')).toBeInTheDocument();
      expect(screen.getByText('Up to 90% revenue sharing')).toBeInTheDocument();
      expect(screen.getByText('Co-marketing opportunities')).toBeInTheDocument();
    });
  });

  it('responds with ROI information when asked about savings', async () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Type a message about ROI
    const input = screen.getByPlaceholderText('Ask about API integration...');
    fireEvent.change(input, { target: { value: 'How much can I save with NovaFuse?' } });

    // Submit the form
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // Fast-forward timers to simulate the AI response
    jest.advanceTimersByTime(2000);

    // Check if ROI information is rendered
    await waitFor(() => {
      expect(screen.getByText('ROI Metrics')).toBeInTheDocument();
      expect(screen.getByText('75% reduction in compliance management effort')).toBeInTheDocument();
      expect(screen.getByText('$1.5M annual savings in compliance management costs')).toBeInTheDocument();
    });
  });

  it('responds with a generic message for other queries', async () => {
    render(<FloatingNovaConcierge />);

    // Open the chat window
    const openButton = screen.getByRole('button');
    fireEvent.click(openButton);

    // Type a generic message
    const input = screen.getByPlaceholderText('Ask about API integration...');
    fireEvent.change(input, { target: { value: 'Hello there' } });

    // Submit the form
    const form = document.querySelector('form');
    fireEvent.submit(form);

    // Fast-forward timers to simulate the AI response
    jest.advanceTimersByTime(2000);

    // Check if generic response is rendered
    await waitFor(() => {
      expect(screen.getByText(/I'd be happy to help with that/)).toBeInTheDocument();
    });
  });
});
