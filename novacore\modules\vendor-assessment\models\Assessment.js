/**
 * NovaCore Assessment Model
 * 
 * This model defines the schema for vendor assessments in the SaaS vendor assessment module.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define question schema
const questionSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  text: { 
    type: String, 
    required: true, 
    trim: true 
  },
  category: { 
    type: String, 
    required: true, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: ['multiple_choice', 'yes_no', 'text', 'file_upload', 'rating'], 
    default: 'yes_no' 
  },
  options: [{ 
    value: { 
      type: String, 
      required: true, 
      trim: true 
    },
    text: { 
      type: String, 
      required: true, 
      trim: true 
    },
    score: { 
      type: Number 
    }
  }],
  required: { 
    type: Boolean, 
    default: true 
  },
  weight: { 
    type: Number, 
    default: 1 
  },
  guidance: { 
    type: String, 
    trim: true 
  },
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  dependsOn: { 
    questionId: { 
      type: String, 
      trim: true 
    },
    value: { 
      type: String, 
      trim: true 
    }
  }
}, { _id: false });

// Define answer schema
const answerSchema = new Schema({
  questionId: { 
    type: String, 
    required: true, 
    trim: true 
  },
  value: { 
    type: Schema.Types.Mixed, 
    required: true 
  },
  notes: { 
    type: String, 
    trim: true 
  },
  attachments: [{ 
    type: Schema.Types.ObjectId, 
    ref: 'Document' 
  }],
  score: { 
    type: Number 
  },
  flags: [{ 
    type: String, 
    trim: true 
  }],
  reviewStatus: { 
    type: String, 
    enum: ['pending', 'approved', 'rejected', 'needs_more_info'], 
    default: 'pending' 
  },
  reviewNotes: { 
    type: String, 
    trim: true 
  },
  reviewedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  reviewedAt: { 
    type: Date 
  }
}, { _id: false });

// Define section schema
const sectionSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  order: { 
    type: Number, 
    default: 0 
  },
  questions: [questionSchema],
  weight: { 
    type: Number, 
    default: 1 
  },
  score: { 
    type: Number 
  },
  maxScore: { 
    type: Number 
  },
  completionStatus: { 
    type: String, 
    enum: ['not_started', 'in_progress', 'completed'], 
    default: 'not_started' 
  }
}, { _id: false });

// Define finding schema
const findingSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    trim: true 
  },
  title: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    required: true, 
    trim: true 
  },
  severity: { 
    type: String, 
    enum: ['critical', 'high', 'medium', 'low', 'informational'], 
    default: 'medium' 
  },
  category: { 
    type: String, 
    required: true, 
    trim: true 
  },
  relatedQuestions: [{ 
    type: String, 
    trim: true 
  }],
  remediation: { 
    type: String, 
    trim: true 
  },
  status: { 
    type: String, 
    enum: ['open', 'in_progress', 'remediated', 'accepted', 'false_positive'], 
    default: 'open' 
  },
  dueDate: { 
    type: Date 
  },
  assignedTo: { 
    type: String, 
    trim: true 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedAt: { 
    type: Date 
  }
}, { _id: false });

// Define assessment schema
const assessmentSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  organizationId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Organization', 
    required: true 
  },
  vendorId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Vendor', 
    required: true 
  },
  templateId: { 
    type: Schema.Types.ObjectId, 
    ref: 'AssessmentTemplate' 
  },
  status: { 
    type: String, 
    enum: ['draft', 'in_progress', 'pending_review', 'completed', 'archived'], 
    default: 'draft' 
  },
  sections: [sectionSchema],
  answers: [answerSchema],
  findings: [findingSchema],
  score: { 
    type: Number 
  },
  maxScore: { 
    type: Number 
  },
  passingScore: { 
    type: Number 
  },
  result: { 
    type: String, 
    enum: ['pass', 'fail', 'conditional_pass', 'not_applicable'], 
    default: 'not_applicable' 
  },
  startDate: { 
    type: Date 
  },
  dueDate: { 
    type: Date 
  },
  completedDate: { 
    type: Date 
  },
  reviewedDate: { 
    type: Date 
  },
  reviewedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  assignedTo: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  frameworks: [{ 
    type: String, 
    trim: true 
  }],
  cyberSafetyScore: { 
    type: Number, 
    min: 0, 
    max: 100 
  },
  cyberSafetyCertified: { 
    type: Boolean, 
    default: false 
  },
  createdBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User' 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
assessmentSchema.index({ organizationId: 1 });
assessmentSchema.index({ vendorId: 1 });
assessmentSchema.index({ templateId: 1 });
assessmentSchema.index({ status: 1 });
assessmentSchema.index({ score: 1 });
assessmentSchema.index({ result: 1 });
assessmentSchema.index({ startDate: 1 });
assessmentSchema.index({ dueDate: 1 });
assessmentSchema.index({ completedDate: 1 });
assessmentSchema.index({ assignedTo: 1 });
assessmentSchema.index({ tags: 1 });
assessmentSchema.index({ frameworks: 1 });
assessmentSchema.index({ cyberSafetyCertified: 1 });
assessmentSchema.index({ createdAt: 1 });

// Add methods
assessmentSchema.methods.isComplete = function() {
  return this.status === 'completed';
};

assessmentSchema.methods.isPassed = function() {
  return this.result === 'pass' || this.result === 'conditional_pass';
};

assessmentSchema.methods.getCompletionPercentage = function() {
  if (!this.sections || this.sections.length === 0) {
    return 0;
  }
  
  const totalQuestions = this.sections.reduce((sum, section) => {
    return sum + section.questions.length;
  }, 0);
  
  if (totalQuestions === 0) {
    return 0;
  }
  
  const answeredQuestions = this.answers.length;
  
  return Math.round((answeredQuestions / totalQuestions) * 100);
};

assessmentSchema.methods.getAnswer = function(questionId) {
  return this.answers.find(answer => answer.questionId === questionId);
};

assessmentSchema.methods.calculateScore = function() {
  if (!this.sections || this.sections.length === 0 || !this.answers || this.answers.length === 0) {
    return 0;
  }
  
  let totalScore = 0;
  let totalMaxScore = 0;
  
  // Calculate score for each section
  for (const section of this.sections) {
    let sectionScore = 0;
    let sectionMaxScore = 0;
    
    for (const question of section.questions) {
      const answer = this.getAnswer(question.id);
      
      if (answer && answer.score !== undefined) {
        sectionScore += answer.score * question.weight;
      }
      
      // Calculate max possible score for this question
      let questionMaxScore = 0;
      
      if (question.type === 'yes_no') {
        questionMaxScore = 1;
      } else if (question.type === 'multiple_choice' && question.options) {
        questionMaxScore = Math.max(...question.options.map(option => option.score || 0));
      } else if (question.type === 'rating') {
        questionMaxScore = 5; // Assuming 5-point rating scale
      } else {
        questionMaxScore = 1; // Default for text and file_upload
      }
      
      sectionMaxScore += questionMaxScore * question.weight;
    }
    
    // Update section score
    section.score = sectionScore;
    section.maxScore = sectionMaxScore;
    
    // Add to total scores, weighted by section weight
    totalScore += sectionScore * section.weight;
    totalMaxScore += sectionMaxScore * section.weight;
  }
  
  // Update assessment score
  this.score = totalScore;
  this.maxScore = totalMaxScore;
  
  // Calculate percentage score
  return totalMaxScore > 0 ? Math.round((totalScore / totalMaxScore) * 100) : 0;
};

// Add statics
assessmentSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

assessmentSchema.statics.findByVendor = function(vendorId) {
  return this.find({ vendorId });
};

assessmentSchema.statics.findActive = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: { $in: ['draft', 'in_progress', 'pending_review'] } 
  });
};

assessmentSchema.statics.findCompleted = function(organizationId) {
  return this.find({ 
    organizationId, 
    status: 'completed' 
  });
};

assessmentSchema.statics.findCyberSafetyCertified = function(organizationId) {
  return this.find({ 
    organizationId, 
    cyberSafetyCertified: true 
  });
};

// Create model
const Assessment = mongoose.model('Assessment', assessmentSchema);

module.exports = Assessment;
