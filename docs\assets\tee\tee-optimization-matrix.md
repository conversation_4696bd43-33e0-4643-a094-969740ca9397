```mermaid
graph TD
    %% Matrix Grid
    subgraph Optimization_Matrix["TEE Optimization Matrix"]
        direction TB
        
        %% Quadrant Labels
        Q1["Q1: High η, Low F<br>Optimal Zone"]
        Q2["Q2: High η, High F<br>Friction Zone"]
        Q3["Q3: Low η, High F<br>Danger Zone"]
        Q4["Q4: Low η, Low F<br>Inefficiency Zone"]
        
        %% Axis Labels
        Y["Efficiency (η)"]
        X["Friction (F)"]
        
        %% Grid Lines
        classDef axis stroke:#666,stroke-width:2px
        classDef q1 fill:#E8F5E9,stroke:#2E7D32,stroke-width:2px
        classDef q2 fill:#FFF8E1,stroke:#FFA000,stroke-width:2px
        classDef q3 fill:#FFEBEE,stroke:#B71C1C,stroke-width:2px
        classDef q4 fill:#E3F2FD,stroke:#1565C0,stroke-width:2px
        
        class Q1 q1
        class Q2 q2
        class Q3 q3
        class Q4 q4
        class Y,X axis
    end
    
    %% Legend
    subgraph Legend["Matrix Legend"]
        direction TB
        
        L1["🟢 Q1: Maximize (High ROI)"]
        L2["🟡 Q2: Reduce Friction"]
        L3["🔴 Q3: Avoid/Redesign"]
        L4["🔵 Q4: Improve Efficiency"]
        
        class L1 q1
        class L2 q2
        class L3 q3
        class L4 q4
    end
```

## TEE Optimization Matrix

### Quadrant Analysis

#### Q1: Optimal Zone (High η, Low F)
- **Characteristics**: Maximum ROI, sustainable energy use
- **Actions**:
  - Protect these processes
  - Document best practices
  - Consider automation
- **Example**: Deep work sessions with minimal distractions

#### Q2: Friction Zone (High η, High F)
- **Characteristics**: Good output but energy-draining
- **Actions**:
  - Identify and eliminate friction points
  - Streamline workflows
  - Automate repetitive tasks
- **Example**: Important but complex team coordination

#### Q3: Danger Zone (Low η, High F)
- **Characteristics**: Energy drain, low returns
- **Actions**:
  - Eliminate or completely redesign
  - Question necessity
  - Consider delegation
- **Example**: Unnecessary meetings with unclear outcomes

#### Q4: Inefficiency Zone (Low η, Low F)
- **Characteristics**: Easy but low-value activities
- **Actions**:
  - Question value alignment
  - Batch process if necessary
  - Set strict time limits
- **Example**: Mindless administrative tasks

### How to Use This Matrix
1. **Audit Tasks**: List your regular activities
2. **Plot Each Task**: Based on η (efficiency) and F (friction)
3. **Optimize**:
   - Move Q2 → Q1 by reducing friction
   - Move Q4 → Q1 by increasing value
   - Eliminate or redesign Q3 items

### Color Coding
- 🟢 **Green (Q1)**: Ideal state, maximize
- 🟡 **Yellow (Q2)**: Fix friction points
- 🔴 **Red (Q3)**: Eliminate/redesign
- 🔵 **Blue (Q4)**: Improve or automate
