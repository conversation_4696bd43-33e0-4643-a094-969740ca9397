# NovaAlign Studio Interaction Layers
# File: nova_align_studio.mmd
# Description: Illustrates the interaction layers within NovaAlign Studio
# Created: 2025-07-06

%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph "NovaAlign Studio Interaction Layers"
        A["User (Comphyological Architect)"] --> B("Design & Configuration Interface")
        B --> C{"NovaAlign Core Engine"}
        C -- System Blueprints --> D["Comphyological Pattern Library"]
        C -- Component Selection --> E["Nova Modules & CSEs Repository"]
        D & E --> F{"System Coherence Validation"}
        F -- Validated Design --> G["Deployment & Orchestration Manager"]
        G --> H["Operational NovaFuse Platform"]
        H -- Performance Feedback --> B
    end

    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef user fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:box3d
    classDef process fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:rectangle
    classDef decision fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:diamond
    classDef storage fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:cylinder
    classDef system fill:#fff,stroke:#000,stroke-width:2px,color:#000,shape:box3d,style=rounded
    
    class A user
    class B,G process
    class C,F decision
    class D,E storage
    class H system
