{"bridge": {"_events": {}, "_eventsCount": 0, "options": {"enforceFirstLaw": true, "enforceSecondLaw": true, "enforceThirdLaw": true, "comphyonMeterEnabled": true, "comphyonGovernorEnabled": true, "comphyonDirectorEnabled": true, "autoHarmonization": true, "resonanceThreshold": 0.9, "comphyonThreshold": 3.142, "crossDomainEnabled": true, "domains": ["cyber", "financial", "medical"], "logBridge": true}, "trinity": {"_events": {}, "_eventsCount": 1, "options": {"enforceFirstLaw": true, "enforceSecondLaw": true, "enforceThirdLaw": true, "strictMode": false, "resonanceLock": true, "logValidation": false, "energyMinimization": true, "selfSimilarityPreservation": true, "logOptimization": false, "domains": ["cyber", "financial", "medical"], "preserveResonance": true, "logTranslations": false, "logGovernance": true}, "firstLaw": {"options": {"strictMode": false, "logValidation": false, "resonanceLock": true}}, "secondLaw": {"_events": {}, "_eventsCount": 1, "options": {"resonanceSet": [0.03, 0.06, 0.09, 0.12, 0.13, 0.3, 0.6, 0.9, 3, 6, 9, 12, 13], "energyMinimization": true, "selfSimilarityPreservation": true, "energyFactor": 0.6, "selfSimilarityFactor": 0.3, "logOptimization": false}, "metrics": {"transitions": 28, "energySaved": 4.290855659550014, "totalEnergy": 7.831236007823298, "averageEnergySaved": 0.15324484498392907, "selfSimilarityScore": 0.877020683017888, "resonanceImprovements": 19, "resonanceDegradations": 0, "optimalTransitions": 13}}, "thirdLaw": {"_events": {}, "_eventsCount": 1, "options": {"domains": ["cyber", "financial", "medical"], "preserveResonance": true, "translationFidelity": 0.9, "logTranslations": false, "strictMode": false}, "resonanceValidator": {"options": {"strictMode": false, "logValidation": true, "resonanceLock": true}}, "metrics": {"translations": 12, "resonancePreserved": 12, "resonanceViolations": 0, "crossDomainFidelity": 1, "domainPairs": {"cyber->financial": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "cyber->medical": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "financial->cyber": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "financial->medical": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "medical->cyber": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "medical->financial": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}}}, "resonanceMaps": {"cyber": {"3": {"value": 3, "representation": "low_priority", "risk": "low"}, "6": {"value": 6, "representation": "medium_priority", "risk": "medium"}, "9": {"value": 9, "representation": "high_priority", "risk": "high"}, "12": {"value": 12, "representation": "critical_priority", "risk": "very_high"}, "13": {"value": 13, "representation": "emergency_priority", "risk": "extreme"}, "0.03": {"value": 0.03, "representation": "minimal_access", "risk": "very_low"}, "0.06": {"value": 0.06, "representation": "standard_access", "risk": "low"}, "0.09": {"value": 0.09, "representation": "elevated_access", "risk": "low_medium"}, "0.12": {"value": 0.12, "representation": "privileged_access", "risk": "medium"}, "0.13": {"value": 0.13, "representation": "admin_access", "risk": "medium_high"}, "0.3": {"value": 0.3, "representation": "low_risk", "risk": "low"}, "0.6": {"value": 0.6, "representation": "medium_risk", "risk": "medium"}, "0.9": {"value": 0.9, "representation": "high_risk", "risk": "high"}}, "financial": {"3": {"value": 3, "representation": "low_value", "risk": "low"}, "6": {"value": 6, "representation": "medium_value", "risk": "medium"}, "9": {"value": 9, "representation": "high_value", "risk": "high"}, "12": {"value": 12, "representation": "premium_value", "risk": "very_high"}, "13": {"value": 13, "representation": "exceptional_value", "risk": "extreme"}, "0.03": {"value": 0.03, "representation": "micro_transaction", "risk": "very_low"}, "0.06": {"value": 0.06, "representation": "small_transaction", "risk": "low"}, "0.09": {"value": 0.09, "representation": "medium_transaction", "risk": "low_medium"}, "0.12": {"value": 0.12, "representation": "large_transaction", "risk": "medium"}, "0.13": {"value": 0.13, "representation": "major_transaction", "risk": "medium_high"}, "0.3": {"value": 0.3, "representation": "low_volatility", "risk": "low"}, "0.6": {"value": 0.6, "representation": "medium_volatility", "risk": "medium"}, "0.9": {"value": 0.9, "representation": "high_volatility", "risk": "high"}}, "medical": {"3": {"value": 3, "representation": "low_urgency", "risk": "low"}, "6": {"value": 6, "representation": "medium_urgency", "risk": "medium"}, "9": {"value": 9, "representation": "high_urgency", "risk": "high"}, "12": {"value": 12, "representation": "critical_urgency", "risk": "very_high"}, "13": {"value": 13, "representation": "life_threatening", "risk": "extreme"}, "0.03": {"value": 0.03, "representation": "routine_care", "risk": "very_low"}, "0.06": {"value": 0.06, "representation": "standard_care", "risk": "low"}, "0.09": {"value": 0.09, "representation": "enhanced_care", "risk": "low_medium"}, "0.12": {"value": 0.12, "representation": "specialized_care", "risk": "medium"}, "0.13": {"value": 0.13, "representation": "advanced_care", "risk": "medium_high"}, "0.3": {"value": 0.3, "representation": "low_severity", "risk": "low"}, "0.6": {"value": 0.6, "representation": "medium_severity", "risk": "medium"}, "0.9": {"value": 0.9, "representation": "high_severity", "risk": "high"}}}}, "metrics": {"operations": 23, "firstLawEnforcements": 26, "firstLawViolations": 0, "secondLawOptimizations": 23, "thirdLawTranslations": 12, "fullTrinityApplications": 12}}, "comphyonMeter": {"_events": {}, "_eventsCount": 0, "options": {"calibrationFactor": 1, "decisionFactor": 0.3, "financialFactor": 0.6, "medicalFactor": 0.9, "domainWeights": {"cyber": 0.3, "financial": 0.6, "medical": 0.9}, "warningThreshold": 2, "criticalThreshold": 3, "maxSafeComphyon": 3.142, "keepHistory": true, "historyLength": 100, "logMeasurements": true}, "metrics": {"measurements": 17, "totalComphyon": 0, "averageComphyon": 0, "maxComphyon": 0, "minComphyon": 0, "warnings": 0, "criticalAlerts": 0, "domainMeasurements": {"cyber": 13, "financial": 2, "medical": 2}}, "history": [{"timestamp": 1747472368963, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368960, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368965, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368965, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368967, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368967, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368969, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368968, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368970, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368970, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368972, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368972, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368974, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368974, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368976, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368976, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368979, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368978, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368981, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368980, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368984, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368983, "operation": "generic", "domain": "cyber", "targetDomain": "financial"}}, {"timestamp": 1747472368988, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368986, "operation": "generic", "domain": "cyber", "targetDomain": "medical"}}, {"timestamp": 1747472368990, "comphyonValue": 0, "domain": "financial", "context": {"timestamp": 1747472368989, "operation": "generic", "domain": "financial", "targetDomain": "cyber"}}, {"timestamp": 1747472368992, "comphyonValue": 0, "domain": "financial", "context": {"timestamp": 1747472368991, "operation": "generic", "domain": "financial", "targetDomain": "medical"}}, {"timestamp": 1747472368994, "comphyonValue": 0, "domain": "medical", "context": {"timestamp": 1747472368994, "operation": "generic", "domain": "medical", "targetDomain": "cyber"}}, {"timestamp": 1747472368996, "comphyonValue": 0, "domain": "medical", "context": {"timestamp": 1747472368996, "operation": "generic", "domain": "medical", "targetDomain": "financial"}}, {"timestamp": 1747472368999, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368998, "operation": "generic", "domain": "cyber"}}], "domainEnergies": {"cyber": {"current": 0.051449999999999996, "previous": 0.054}, "financial": {"current": 0.216, "previous": 0.216}, "medical": {"current": 0.48600000000000004, "previous": 0.48600000000000004}}}, "comphyonGovernor": {"_events": {}, "_eventsCount": 0, "options": {"safeThreshold": 1, "warningThreshold": 2, "criticalThreshold": 3, "maxSafeComphyon": 3.142, "defaultMode": "standard", "warningMode": "constrained", "criticalMode": "minimal", "standardFactor": 1, "constrainedFactor": 0.6, "minimalFactor": 0.3, "tensorWeightA": 0.3, "tensorWeightB": 0.6, "tensorWeightC": 0.9, "piMultiplier": 3.142, "autoAdjust": true, "adjustmentRate": 0.1, "logGovernance": true}, "metrics": {"governanceOperations": 17, "modeChanges": 0, "standardOperations": 17, "constrainedOperations": 0, "minimalOperations": 0, "adjustments": 0, "totalComphyonGoverned": 0, "averageComphyonGoverned": 0}, "currentMode": "standard"}, "comphyonDirector": {}, "metrics": {"bridgeOperations": 17, "trinityEnforcements": 17, "comphyonMeasurements": 17, "governanceDecisions": 17, "crossDomainTranslations": 6, "harmonizationEvents": 0, "resonanceViolations": 0, "comphyonViolations": 0}}, "trinity": {"_events": {}, "_eventsCount": 0, "options": {"enforceFirstLaw": true, "enforceSecondLaw": true, "enforceThirdLaw": true, "strictMode": false, "resonanceLock": true, "logValidation": false, "energyMinimization": true, "selfSimilarityPreservation": true, "logOptimization": false, "domains": ["cyber", "financial", "medical"], "preserveResonance": true, "logTranslations": false, "logGovernance": true}, "firstLaw": {"options": {"strictMode": false, "logValidation": false, "resonanceLock": true}}, "secondLaw": {"_events": {}, "_eventsCount": 1, "options": {"resonanceSet": [0.03, 0.06, 0.09, 0.12, 0.13, 0.3, 0.6, 0.9, 3, 6, 9, 12, 13], "energyMinimization": true, "selfSimilarityPreservation": true, "energyFactor": 0.6, "selfSimilarityFactor": 0.3, "logOptimization": false}, "metrics": {"transitions": 0, "energySaved": 0, "totalEnergy": 0, "averageEnergySaved": 0, "selfSimilarityScore": 0, "resonanceImprovements": 0, "resonanceDegradations": 0, "optimalTransitions": 0}}, "thirdLaw": {"_events": {}, "_eventsCount": 1, "options": {"domains": ["cyber", "financial", "medical"], "preserveResonance": true, "translationFidelity": 0.9, "logTranslations": false, "strictMode": false}, "resonanceValidator": {"options": {"strictMode": false, "logValidation": true, "resonanceLock": true}}, "metrics": {"translations": 0, "resonancePreserved": 0, "resonanceViolations": 0, "crossDomainFidelity": 0, "domainPairs": {"cyber->financial": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "cyber->medical": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "financial->cyber": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "financial->medical": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "medical->cyber": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "medical->financial": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}}}, "resonanceMaps": {"cyber": {"3": {"value": 3, "representation": "low_priority", "risk": "low"}, "6": {"value": 6, "representation": "medium_priority", "risk": "medium"}, "9": {"value": 9, "representation": "high_priority", "risk": "high"}, "12": {"value": 12, "representation": "critical_priority", "risk": "very_high"}, "13": {"value": 13, "representation": "emergency_priority", "risk": "extreme"}, "0.03": {"value": 0.03, "representation": "minimal_access", "risk": "very_low"}, "0.06": {"value": 0.06, "representation": "standard_access", "risk": "low"}, "0.09": {"value": 0.09, "representation": "elevated_access", "risk": "low_medium"}, "0.12": {"value": 0.12, "representation": "privileged_access", "risk": "medium"}, "0.13": {"value": 0.13, "representation": "admin_access", "risk": "medium_high"}, "0.3": {"value": 0.3, "representation": "low_risk", "risk": "low"}, "0.6": {"value": 0.6, "representation": "medium_risk", "risk": "medium"}, "0.9": {"value": 0.9, "representation": "high_risk", "risk": "high"}}, "financial": {"3": {"value": 3, "representation": "low_value", "risk": "low"}, "6": {"value": 6, "representation": "medium_value", "risk": "medium"}, "9": {"value": 9, "representation": "high_value", "risk": "high"}, "12": {"value": 12, "representation": "premium_value", "risk": "very_high"}, "13": {"value": 13, "representation": "exceptional_value", "risk": "extreme"}, "0.03": {"value": 0.03, "representation": "micro_transaction", "risk": "very_low"}, "0.06": {"value": 0.06, "representation": "small_transaction", "risk": "low"}, "0.09": {"value": 0.09, "representation": "medium_transaction", "risk": "low_medium"}, "0.12": {"value": 0.12, "representation": "large_transaction", "risk": "medium"}, "0.13": {"value": 0.13, "representation": "major_transaction", "risk": "medium_high"}, "0.3": {"value": 0.3, "representation": "low_volatility", "risk": "low"}, "0.6": {"value": 0.6, "representation": "medium_volatility", "risk": "medium"}, "0.9": {"value": 0.9, "representation": "high_volatility", "risk": "high"}}, "medical": {"3": {"value": 3, "representation": "low_urgency", "risk": "low"}, "6": {"value": 6, "representation": "medium_urgency", "risk": "medium"}, "9": {"value": 9, "representation": "high_urgency", "risk": "high"}, "12": {"value": 12, "representation": "critical_urgency", "risk": "very_high"}, "13": {"value": 13, "representation": "life_threatening", "risk": "extreme"}, "0.03": {"value": 0.03, "representation": "routine_care", "risk": "very_low"}, "0.06": {"value": 0.06, "representation": "standard_care", "risk": "low"}, "0.09": {"value": 0.09, "representation": "enhanced_care", "risk": "low_medium"}, "0.12": {"value": 0.12, "representation": "specialized_care", "risk": "medium"}, "0.13": {"value": 0.13, "representation": "advanced_care", "risk": "medium_high"}, "0.3": {"value": 0.3, "representation": "low_severity", "risk": "low"}, "0.6": {"value": 0.6, "representation": "medium_severity", "risk": "medium"}, "0.9": {"value": 0.9, "representation": "high_severity", "risk": "high"}}}}, "metrics": {"operations": 0, "firstLawEnforcements": 0, "firstLawViolations": 0, "secondLawOptimizations": 0, "thirdLawTranslations": 0, "fullTrinityApplications": 0}}, "comphyonMeter": {"_events": {}, "_eventsCount": 0, "options": {"calibrationFactor": 1, "decisionFactor": 0.3, "financialFactor": 0.6, "medicalFactor": 0.9, "domainWeights": {"cyber": 0.3, "financial": 0.6, "medical": 0.9}, "warningThreshold": 2, "criticalThreshold": 3, "maxSafeComphyon": 3.142, "keepHistory": true, "historyLength": 100, "logMeasurements": true}, "metrics": {"measurements": 17, "totalComphyon": 0, "averageComphyon": 0, "maxComphyon": 0, "minComphyon": 0, "warnings": 0, "criticalAlerts": 0, "domainMeasurements": {"cyber": 13, "financial": 2, "medical": 2}}, "history": [{"timestamp": 1747472368963, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368960, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368965, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368965, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368967, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368967, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368969, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368968, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368970, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368970, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368972, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368972, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368974, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368974, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368976, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368976, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368979, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368978, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368981, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368980, "operation": "generic", "domain": "cyber"}}, {"timestamp": 1747472368984, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368983, "operation": "generic", "domain": "cyber", "targetDomain": "financial"}}, {"timestamp": 1747472368988, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368986, "operation": "generic", "domain": "cyber", "targetDomain": "medical"}}, {"timestamp": 1747472368990, "comphyonValue": 0, "domain": "financial", "context": {"timestamp": 1747472368989, "operation": "generic", "domain": "financial", "targetDomain": "cyber"}}, {"timestamp": 1747472368992, "comphyonValue": 0, "domain": "financial", "context": {"timestamp": 1747472368991, "operation": "generic", "domain": "financial", "targetDomain": "medical"}}, {"timestamp": 1747472368994, "comphyonValue": 0, "domain": "medical", "context": {"timestamp": 1747472368994, "operation": "generic", "domain": "medical", "targetDomain": "cyber"}}, {"timestamp": 1747472368996, "comphyonValue": 0, "domain": "medical", "context": {"timestamp": 1747472368996, "operation": "generic", "domain": "medical", "targetDomain": "financial"}}, {"timestamp": 1747472368999, "comphyonValue": 0, "domain": "cyber", "context": {"timestamp": 1747472368998, "operation": "generic", "domain": "cyber"}}], "domainEnergies": {"cyber": {"current": 0.051449999999999996, "previous": 0.054}, "financial": {"current": 0.216, "previous": 0.216}, "medical": {"current": 0.48600000000000004, "previous": 0.48600000000000004}}}, "comphyonGovernor": {"_events": {}, "_eventsCount": 0, "options": {"safeThreshold": 1, "warningThreshold": 2, "criticalThreshold": 3, "maxSafeComphyon": 3.142, "defaultMode": "standard", "warningMode": "constrained", "criticalMode": "minimal", "standardFactor": 1, "constrainedFactor": 0.6, "minimalFactor": 0.3, "tensorWeightA": 0.3, "tensorWeightB": 0.6, "tensorWeightC": 0.9, "piMultiplier": 3.142, "autoAdjust": true, "adjustmentRate": 0.1, "logGovernance": true}, "metrics": {"governanceOperations": 17, "modeChanges": 0, "standardOperations": 17, "constrainedOperations": 0, "minimalOperations": 0, "adjustments": 0, "totalComphyonGoverned": 0, "averageComphyonGoverned": 0}, "currentMode": "standard"}, "testValues": [0.1, 0.3, 0.5, 0.7, 0.9, 1.1, 1.3, 1.5, 1.7, 1.9], "results": [{"value": 0.1, "result": {"originalState": 0.1, "processedState": 2.8956671999999997, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368960, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 0.3, "result": {"originalState": 0.3, "processedState": 2.997468, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368965, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 0.5, "result": {"originalState": 0.5, "processedState": 3.1671359999999997, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368967, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 0.7, "result": {"originalState": 0.7, "processedState": 3.1671359999999997, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368968, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 0.9, "result": {"originalState": 0.9, "processedState": 3.336804, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368970, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 1.1, "result": {"originalState": 1.1, "processedState": 3.336804, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368972, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 1.3, "result": {"originalState": 1.3, "processedState": 3.336804, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368974, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 1.5, "result": {"originalState": 1.5, "processedState": 3.336804, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368976, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 1.7, "result": {"originalState": 1.7, "processedState": 3.336804, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368978, "operation": "generic", "domain": "cyber"}}, "valid": true}, {"value": 1.9, "result": {"originalState": 1.9, "processedState": 4.52448, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368980, "operation": "generic", "domain": "cyber"}}, "valid": true}], "crossDomainResults": [{"sourceDomain": "cyber", "targetDomain": "financial", "result": {"originalState": 0.6, "processedState": 3, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368983, "operation": "generic", "domain": "cyber", "targetDomain": "financial"}}, "valid": true}, {"sourceDomain": "cyber", "targetDomain": "medical", "result": {"originalState": 0.6, "processedState": 3, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368986, "operation": "generic", "domain": "cyber", "targetDomain": "medical"}}, "valid": true}, {"sourceDomain": "financial", "targetDomain": "cyber", "result": {"originalState": 0.6, "processedState": 3, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368989, "operation": "generic", "domain": "financial", "targetDomain": "cyber"}}, "valid": true}, {"sourceDomain": "financial", "targetDomain": "medical", "result": {"originalState": 0.6, "processedState": 3, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368991, "operation": "generic", "domain": "financial", "targetDomain": "medical"}}, "valid": true}, {"sourceDomain": "medical", "targetDomain": "cyber", "result": {"originalState": 0.6, "processedState": 3, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368994, "operation": "generic", "domain": "medical", "targetDomain": "cyber"}}, "valid": true}, {"sourceDomain": "medical", "targetDomain": "financial", "result": {"originalState": 0.6, "processedState": 3, "comphyonValue": 0, "componentsApplied": {"trinity": true, "comphyonMeter": true, "comphyonGovernor": true, "comphyonDirector": true}, "context": {"timestamp": 1747472368996, "operation": "generic", "domain": "medical", "targetDomain": "financial"}}, "valid": true}], "metrics": {"bridge": {"bridge": {"bridgeOperations": 17, "trinityEnforcements": 17, "comphyonMeasurements": 17, "governanceDecisions": 17, "crossDomainTranslations": 6, "harmonizationEvents": 0, "resonanceViolations": 0, "comphyonViolations": 0}, "trinity": {"trinity": {"operations": 23, "firstLawEnforcements": 26, "firstLawViolations": 0, "secondLawOptimizations": 23, "thirdLawTranslations": 12, "fullTrinityApplications": 12}, "firstLaw": {}, "secondLaw": {"transitions": 28, "energySaved": 4.290855659550014, "totalEnergy": 7.831236007823298, "averageEnergySaved": 0.15324484498392907, "selfSimilarityScore": 0.877020683017888, "resonanceImprovements": 19, "resonanceDegradations": 0, "optimalTransitions": 13}, "thirdLaw": {"translations": 12, "resonancePreserved": 12, "resonanceViolations": 0, "crossDomainFidelity": 1, "domainPairs": {"cyber->financial": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "cyber->medical": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "financial->cyber": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "financial->medical": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "medical->cyber": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}, "medical->financial": {"translations": 2, "resonancePreserved": 2, "fidelity": 1}}}}, "comphyonMeter": {"measurements": 17, "totalComphyon": 0, "averageComphyon": 0, "maxComphyon": 0, "minComphyon": 0, "warnings": 0, "criticalAlerts": 0, "domainMeasurements": {"cyber": 13, "financial": 2, "medical": 2}}, "comphyonGovernor": {"governanceOperations": 17, "modeChanges": 0, "standardOperations": 17, "constrainedOperations": 0, "minimalOperations": 0, "adjustments": 0, "totalComphyonGoverned": 0, "averageComphyonGoverned": 0}, "comphyonDirector": {"directions": 0}}, "trinity": {"trinity": {"operations": 0, "firstLawEnforcements": 0, "firstLawViolations": 0, "secondLawOptimizations": 0, "thirdLawTranslations": 0, "fullTrinityApplications": 0}, "firstLaw": {}, "secondLaw": {"transitions": 0, "energySaved": 0, "totalEnergy": 0, "averageEnergySaved": 0, "selfSimilarityScore": 0, "resonanceImprovements": 0, "resonanceDegradations": 0, "optimalTransitions": 0}, "thirdLaw": {"translations": 0, "resonancePreserved": 0, "resonanceViolations": 0, "crossDomainFidelity": 0, "domainPairs": {"cyber->financial": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "cyber->medical": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "financial->cyber": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "financial->medical": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "medical->cyber": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}, "medical->financial": {"translations": 0, "resonancePreserved": 0, "fidelity": 0}}}}, "comphyonMeter": {"measurements": 17, "totalComphyon": 0, "averageComphyon": 0, "maxComphyon": 0, "minComphyon": 0, "warnings": 0, "criticalAlerts": 0, "domainMeasurements": {"cyber": 13, "financial": 2, "medical": 2}}, "comphyonGovernor": {"governanceOperations": 17, "modeChanges": 0, "standardOperations": 17, "constrainedOperations": 0, "minimalOperations": 0, "adjustments": 0, "totalComphyonGoverned": 0, "averageComphyonGoverned": 0}}}