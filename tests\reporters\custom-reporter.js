class CustomReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options || {};
    this.outputFile = this._options.outputFile || './test-results/test-report.md';
    this.includePerformanceMetrics = this._options.includePerformanceMetrics || false;
    this.results = {
      startTime: new Date(),
      endTime: null,
      numTotalTests: 0,
      numPassedTests: 0,
      numFailedTests: 0,
      numPendingTests: 0,
      testSuites: [],
      performance: {
        averageResponseTime: null,
        memoryUsage: null
      }
    };
  }

  onRunStart(results, options) {
    this.results.startTime = new Date();
    console.log(`Starting test run at ${this.results.startTime.toISOString()}`);
  }

  onTestResult(test, testResult, aggregatedResult) {
    const testSuite = {
      name: testResult.testFilePath.split('/').pop(),
      numPassingTests: testResult.numPassingTests,
      numFailingTests: testResult.numFailingTests,
      numPendingTests: testResult.numPendingTests,
      testResults: testResult.testResults.map(result => ({
        title: result.title,
        status: result.status,
        duration: result.duration,
        failureMessages: result.failureMessages
      }))
    };

    this.results.testSuites.push(testSuite);
    this.results.numTotalTests += testResult.numPassingTests + testResult.numFailingTests + testResult.numPendingTests;
    this.results.numPassedTests += testResult.numPassingTests;
    this.results.numFailedTests += testResult.numFailingTests;
    this.results.numPendingTests += testResult.numPendingTests;
  }

  onRunComplete(contexts, results) {
    this.results.endTime = new Date();
    const duration = (this.results.endTime - this.results.startTime) / 1000;
    
    // Get performance metrics if available
    if (this.includePerformanceMetrics && global.testMetrics) {
      this.results.performance.averageResponseTime = global.testMetrics.performance.averageResponseTime;
      this.results.performance.memoryUsage = {
        start: global.testMetrics.memoryUsage[0],
        end: global.testMetrics.memoryUsage[global.testMetrics.memoryUsage.length - 1]
      };
    }
    
    // Generate report
    const fs = require('fs');
    const path = require('path');
    
    // Ensure directory exists
    const dir = path.dirname(this.outputFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Generate markdown report
    const report = this._generateMarkdownReport(duration);
    
    // Write report to file
    fs.writeFileSync(this.outputFile, report);
    
    console.log(`Test report written to ${this.outputFile}`);
  }
  
  _generateMarkdownReport(duration) {
    const passRate = this.results.numTotalTests > 0 
      ? ((this.results.numPassedTests / this.results.numTotalTests) * 100).toFixed(2) 
      : 0;
    
    let report = `# NovaConnect Test Report\n\n`;
    report += `## Summary\n\n`;
    report += `- **Start Time**: ${this.results.startTime.toISOString()}\n`;
    report += `- **End Time**: ${this.results.endTime.toISOString()}\n`;
    report += `- **Duration**: ${duration.toFixed(2)} seconds\n`;
    report += `- **Total Tests**: ${this.results.numTotalTests}\n`;
    report += `- **Passed Tests**: ${this.results.numPassedTests}\n`;
    report += `- **Failed Tests**: ${this.results.numFailedTests}\n`;
    report += `- **Pending Tests**: ${this.results.numPendingTests}\n`;
    report += `- **Pass Rate**: ${passRate}%\n\n`;
    
    // Add performance metrics if available
    if (this.includePerformanceMetrics && this.results.performance.averageResponseTime) {
      report += `## Performance Metrics\n\n`;
      report += `- **Average Response Time**: ${this.results.performance.averageResponseTime.toFixed(2)} ms\n`;
      
      if (this.results.performance.memoryUsage) {
        const startHeapUsed = this.results.performance.memoryUsage.start.usage.heapUsed / 1024 / 1024;
        const endHeapUsed = this.results.performance.memoryUsage.end.usage.heapUsed / 1024 / 1024;
        const memoryDiff = endHeapUsed - startHeapUsed;
        
        report += `- **Memory Usage**:\n`;
        report += `  - Start: ${startHeapUsed.toFixed(2)} MB\n`;
        report += `  - End: ${endHeapUsed.toFixed(2)} MB\n`;
        report += `  - Difference: ${memoryDiff.toFixed(2)} MB\n\n`;
      }
    }
    
    // Add test suite details
    report += `## Test Suites\n\n`;
    
    this.results.testSuites.forEach(suite => {
      const suitePassRate = (suite.numPassingTests + suite.numFailingTests) > 0 
        ? ((suite.numPassingTests / (suite.numPassingTests + suite.numFailingTests)) * 100).toFixed(2) 
        : 0;
      
      report += `### ${suite.name}\n\n`;
      report += `- **Pass Rate**: ${suitePassRate}%\n`;
      report += `- **Passed**: ${suite.numPassingTests}\n`;
      report += `- **Failed**: ${suite.numFailingTests}\n`;
      report += `- **Pending**: ${suite.numPendingTests}\n\n`;
      
      // Add test details
      report += `| Test | Status | Duration (ms) |\n`;
      report += `|------|--------|---------------|\n`;
      
      suite.testResults.forEach(test => {
        const status = test.status === 'passed' ? '✅ PASS' : test.status === 'failed' ? '❌ FAIL' : '⏸️ SKIP';
        report += `| ${test.title} | ${status} | ${test.duration} |\n`;
      });
      
      report += `\n`;
      
      // Add failure messages
      const failedTests = suite.testResults.filter(test => test.status === 'failed');
      if (failedTests.length > 0) {
        report += `#### Failures\n\n`;
        
        failedTests.forEach(test => {
          report += `##### ${test.title}\n\n`;
          test.failureMessages.forEach(message => {
            report += "```\n" + message + "\n```\n\n";
          });
        });
      }
    });
    
    return report;
  }
}

module.exports = CustomReporter;
