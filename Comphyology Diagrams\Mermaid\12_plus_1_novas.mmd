# 12+1 Universal Novas Architecture
# File: 12_plus_1_novas.mmd
# Description: Illustrates the 12+1 Universal Novas modular architecture
# Created: 2025-07-06

%%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#fff', 'primaryTextColor': '#000', 'primaryBorderColor': '#000', 'lineColor': '#000', 'secondaryColor': '#fff', 'tertiaryColor': '#fff'}}}%%

graph TD
    subgraph "CYBER-SAFETY FRAMEWORK: 12+1 UNIVERSAL NOVAS"
        %% Central Core
        Core["NovaCore\n(Ψᶜ Central Processor)\n\nThe 13th Nova"]
        
        %% Governance Layer
        subgraph Governance["Governance Layer"]
            NovaGovern["NovaGovern\n(Governance & Compliance)"]
            NovaAudit["NovaAudit\n(Continuous Monitoring)"]
            NovaPolicy["NovaPolicy\n(Policy Management)"]
        end
        
        %% Security Layer
        subgraph Security["Security Layer"]
            NovaShield["NovaShield\n(Threat Protection)"]
            NovaCrypt["NovaCrypt\n(Encryption Services)"]
            NovaAuth["NovaAuth\n(Authentication)"]
        end
        
        %% Intelligence Layer
        subgraph Intelligence["Intelligence Layer"]
            NovaAI["NovaAI\n(Artificial Intelligence)"]
            NovaNEPI["NovaNEPI\n(Pattern Recognition)"]
            NovaInsight["NovaInsight\n(Analytics)"]
        end
        
        %% Integration Layer
        subgraph Integration["Integration Layer"]
            NovaLink["NovaLink\n(API Gateway)"]
            NovaSync["NovaSync\n(Data Synchronization)"]
            NovaFlow["NovaFlow\n(Workflow Automation)"]
        end
        
        %% Connections from Core to Layers
        Core --> Governance
        Core --> Security
        Core --> Intelligence
        Core --> Integration
        
        %% Layer Connections
        Governance --> Security
        Security --> Intelligence
        Intelligence --> Integration
        Integration --> Governance
    end
    
    %% Styling for USPTO compliance (black and white)
    classDef default fill:#fff,stroke:#000,stroke-width:1px,color:#000
    classDef core fill:#fff,stroke:#000,stroke-width:3px,color:#000,shape:doublecircle
    classDef layer fill:#fff,stroke:#000,stroke-width:2px,stroke-dasharray: 5 5,color:#000
    classDef nova fill:#fff,stroke:#000,stroke-width:1px,color:#000,shape:box3d
    
    class Core core
    class Governance,Security,Intelligence,Integration layer
    class NovaGovern,NovaAudit,NovaPolicy,NovaShield,NovaCrypt,NovaAuth,NovaAI,NovaNEPI,NovaInsight,NovaLink,NovaSync,NovaFlow nova
