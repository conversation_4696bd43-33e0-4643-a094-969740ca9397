
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0A84FF;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-item {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      flex: 1;
      margin-right: 10px;
      text-align: center;
    }
    .summary-item:last-child {
      margin-right: 0;
    }
    .summary-item.passed {
      background-color: #d4edda;
      color: #155724;
    }
    .summary-item.failed {
      background-color: #f8d7da;
      color: #721c24;
    }
    .summary-item.pending {
      background-color: #fff3cd;
      color: #856404;
    }
    .summary-item.total {
      background-color: #e2e3e5;
      color: #383d41;
    }
    .summary-number {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .test-suite {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .test-suite-header {
      padding: 10px 15px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .test-suite-title {
      margin: 0;
      font-size: 18px;
    }
    .test-suite-status {
      font-weight: bold;
    }
    .test-suite-status.passed {
      color: #28a745;
    }
    .test-suite-status.failed {
      color: #dc3545;
    }
    .test-suite-body {
      padding: 15px;
    }
    .test-case {
      padding: 8px 15px;
      border-bottom: 1px solid #eee;
    }
    .test-case:last-child {
      border-bottom: none;
    }
    .test-case.passed {
      border-left: 4px solid #28a745;
    }
    .test-case.failed {
      border-left: 4px solid #dc3545;
      background-color: #f8f9fa;
    }
    .test-case.pending {
      border-left: 4px solid #ffc107;
      color: #6c757d;
    }
    .test-case-title {
      margin: 0;
      font-size: 16px;
    }
    .test-case-error {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8d7da;
      border-radius: 3px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .test-duration {
      color: #6c757d;
      font-size: 14px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <header>
    <h1>NovaFuse Test Report</h1>
    <p>Generated on 7/12/2025, 4:44:05 AM</p>
  </header>
  
  <div class="summary">
    <div class="summary-item passed">
      <h3>Passed</h3>
      <div class="summary-number">19</div>
      <div>53%</div>
    </div>
    <div class="summary-item failed">
      <h3>Failed</h3>
      <div class="summary-number">17</div>
      <div>47%</div>
    </div>
    <div class="summary-item pending">
      <h3>Pending</h3>
      <div class="summary-number">0</div>
      <div>0%</div>
    </div>
    <div class="summary-item total">
      <h3>Total</h3>
      <div class="summary-number">36</div>
      <div>100%</div>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  
  
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">certifications-accreditation.integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">complianceRoutes.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">business-intelligence-workflow.integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">enterprise-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">should authenticate and authorize based on AD groups</h4>
        <div class="test-duration">Duration: 271ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle role-based access control correctly</h4>
        <div class="test-duration">Duration: 17ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should send compliance events to SIEM</h4>
        <div class="test-duration">Duration: 23ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should receive and process security events from SIEM</h4>
        <div class="test-duration">Duration: 23ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle bidirectional SIEM integration efficiently</h4>
        <div class="test-duration">Duration: 16ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should create change requests for manual approval</h4>
        <div class="test-duration">Duration: 16ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should track change request approval workflow</h4>
        <div class="test-duration">Duration: 18ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should execute approved changes and update status</h4>
        <div class="test-duration">Duration: 17ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">chronicle-live-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">chronicle-live-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">api.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">api.comprehensive.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">apis-ipaas-developer-tools.integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">rbac-api.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">bigquery-live-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">contracts-policy-lifecycle.integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">bigquery-live-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">gcp-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">should retrieve findings from SCC</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should normalize SCC findings to NovaConnect format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should retrieve IAM roles</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should retrieve IAM policy</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should execute a BigQuery query</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should store evidence in Cloud Storage</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should orchestrate a compliance workflow across multiple GCP services</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">AxiosError: Network Error
    at XMLHttpRequest.handleError (D:\novafuse-api-superstore\node_modules\axios\lib\adapters\xhr.js:110:14)
    at XMLHttpRequest.invokeTheCallbackFunction (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\generated\EventHandlerNonNull.js:14:28)
    at XMLHttpRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\create-event-accessor.js:35:32)
    at innerInvokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:350:25)
    at invokeEventListeners (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:286:3)
    at XMLHttpRequestImpl._dispatch (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\events\EventTarget-impl.js:233:9)
    at fireAnEvent (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\events.js:18:36)
    at requestErrorSteps (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:131:3)
    at Object.dispatchError (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:60:3)
    at Request.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:529:35)
    at ClientRequest.<anonymous> (D:\novafuse-api-superstore\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:121:14)
    at ClientRequest.emit (node:events:517:28)
    at Socket.socketErrorListener (node:_http_client:501:9)
    at Socket.emit (node:events:517:28)
    at emitErrorNT (node:internal/streams/destroy:151:8)
    at emitErrorCloseNT (node:internal/streams/destroy:116:3)
    at processTicksAndRejections (node:internal/process/task_queues:82:21)
    at Axios.request (D:\novafuse-api-superstore\node_modules\axios\lib\core\Axios.js:45:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">securityRoutes.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">testExecution.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">controlApi.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">scc-live-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">scc-live-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">securityFeatures.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">ui-navigation.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">evidence-verification.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/evidence should return a list of evidence items</h4>
        <div class="test-duration">Duration: 50ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">POST /api/v1/evidence should create a new evidence item</h4>
        <div class="test-duration">Duration: 296ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/evidence/:id should return a specific evidence item</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">POST /api/v1/verification should verify an evidence item</h4>
        <div class="test-duration">Duration: 13ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/verification should return a list of verifications</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/verification/:id should return a specific verification</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/verification/:id/proof should return the proof for a verification</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">api-endpoints.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/evidence should return a list of evidence items</h4>
        <div class="test-duration">Duration: 8ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">POST /api/v1/evidence should create a new evidence item</h4>
        <div class="test-duration">Duration: 14ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/evidence/:id should return a specific evidence item</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">PUT /api/v1/evidence/:id should update an evidence item</h4>
        <div class="test-duration">Duration: 8ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">DELETE /api/v1/evidence/:id should delete an evidence item</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">POST /api/v1/blockchain/verify should verify data on the blockchain</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">GET /api/v1/connectors should return a list of connectors</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">connector-api.integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">api.integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">tenant-audit-isolation.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should log events for multiple tenants</h4>
        <div class="test-duration">Duration: 447ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should not expose logs across tenants</h4>
        <div class="test-duration">Duration: 7ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: [32m"test-tenant-[7m1[27m"[39m
Received: [31m"test-tenant-[7m3[27m"[39m
    at toBe (D:\novafuse-api-superstore\nova-connect\tests\integration\tenant-audit-isolation.test.js:96:30)
    at Array.forEach (<anonymous>)
    at Object.forEach (D:\novafuse-api-superstore\nova-connect\tests\integration\tenant-audit-isolation.test.js:95:23)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle requests without tenant ID</h4>
        <div class="test-duration">Duration: 15ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle tenant-specific middleware</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">TypeError: this.logTenantEvent is not a function
    at Object.logTenantEvent (D:\novafuse-api-superstore\nova-connect\api\services\AuditService.js:434:16)
    at Object.end (D:\novafuse-api-superstore\nova-connect\tests\integration\tenant-audit-isolation.test.js:161:9)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">api.simple.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return a 200 status code</h4>
        <div class="test-duration">Duration: 49ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new requirement</h4>
        <div class="test-duration">Duration: 49ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a 400 error for invalid requirement data</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">auth.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  
  <footer>
    <p>NovaFuse Universal Platform &copy; 2025</p>
  </footer>
</body>
</html>
  