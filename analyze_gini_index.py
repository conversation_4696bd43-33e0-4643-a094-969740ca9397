#!/usr/bin/env python3
"""
Analyze World Bank Gini index data for 18/82 patterns.
This script analyzes Gini index data from the World Bank to identify
18/82 patterns and other UUFT patterns across countries and time periods.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('gini_index_analysis.log')
    ]
)
logger = logging.getLogger('Gini_Index_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PATTERN_1882_THRESHOLD = 0.20  # 20% threshold for considering a match (relaxed from 5%)
GINI_1882 = 0.82  # Gini coefficient corresponding to 18/82 split
RESULTS_DIR = "gini_results_relaxed"
os.makedirs(RESULTS_DIR, exist_ok=True)

def load_gini_data():
    """Load the World Bank Gini index data."""
    logger.info("Loading World Bank Gini index data...")

    # Load the data
    file_path = "API_SI.POV.GINI_DS2_en_csv_v2_85135.csv"

    # Skip the first 4 rows which contain metadata
    df = pd.read_csv(file_path, skiprows=4)

    # Rename columns for clarity
    df = df.rename(columns={"Country Name": "country", "Country Code": "country_code"})

    # Drop unnecessary columns
    df = df.drop(columns=["Indicator Name", "Indicator Code"])

    # Melt the dataframe to convert years from columns to rows
    df_melted = pd.melt(
        df,
        id_vars=["country", "country_code"],
        var_name="year",
        value_name="gini"
    )

    # Convert year to integer and filter out rows with missing Gini values
    df_melted["year"] = pd.to_numeric(df_melted["year"], errors="coerce")
    df_melted = df_melted.dropna(subset=["year", "gini"])
    df_melted["year"] = df_melted["year"].astype(int)

    # Sort by country and year
    df_melted = df_melted.sort_values(["country", "year"])

    logger.info(f"Loaded Gini index data for {df_melted['country'].nunique()} countries and {df_melted['year'].nunique()} years")

    return df_melted

def analyze_gini_1882_patterns(df):
    """Analyze Gini index data for 18/82 patterns."""
    logger.info("Analyzing Gini index data for 18/82 patterns...")

    # Calculate proximity to 0.82 (18/82 split)
    # World Bank Gini index is already in percentage (0-100), so we need to divide by 100 to get 0-1 scale
    df["proximity_to_1882"] = abs(df["gini"] / 100 - GINI_1882) / GINI_1882 * 100
    df["is_1882_pattern"] = df["proximity_to_1882"] <= PATTERN_1882_THRESHOLD * 100

    # Count countries with 18/82 patterns
    countries_with_1882 = df[df["is_1882_pattern"]]["country"].unique()
    logger.info(f"Found {len(countries_with_1882)} countries with Gini coefficients close to 0.82 (18/82 split)")

    # Calculate percentage of countries with 18/82 patterns
    total_countries = df["country"].nunique()
    percentage_countries_with_1882 = len(countries_with_1882) / total_countries * 100
    logger.info(f"Percentage of countries with 18/82 patterns: {percentage_countries_with_1882:.2f}%")

    # Calculate percentage of observations with 18/82 patterns
    total_observations = len(df)
    observations_with_1882 = df[df["is_1882_pattern"]].shape[0]
    percentage_observations_with_1882 = observations_with_1882 / total_observations * 100
    logger.info(f"Percentage of observations with 18/82 patterns: {percentage_observations_with_1882:.2f}%")

    # Find countries with the most consistent 18/82 patterns
    country_pattern_counts = df[df["is_1882_pattern"]].groupby("country").size().reset_index(name="count")
    country_total_counts = df.groupby("country").size().reset_index(name="total")
    country_pattern_percentage = pd.merge(country_pattern_counts, country_total_counts, on="country")
    country_pattern_percentage["percentage"] = country_pattern_percentage["count"] / country_pattern_percentage["total"] * 100
    country_pattern_percentage = country_pattern_percentage.sort_values("percentage", ascending=False)

    logger.info("Top 10 countries with the most consistent 18/82 patterns:")
    for i, row in country_pattern_percentage.head(10).iterrows():
        logger.info(f"  {row['country']}: {row['percentage']:.2f}% of observations ({row['count']} out of {row['total']})")

    return {
        "countries_with_1882": list(countries_with_1882),
        "percentage_countries_with_1882": percentage_countries_with_1882,
        "observations_with_1882": observations_with_1882,
        "percentage_observations_with_1882": percentage_observations_with_1882,
        "top_countries": country_pattern_percentage.head(10).to_dict("records")
    }

def analyze_gini_trends(df):
    """Analyze trends in Gini index data over time."""
    logger.info("Analyzing trends in Gini index data over time...")

    # Calculate global average Gini by year
    global_avg_by_year = df.groupby("year")["gini"].mean().reset_index()

    # Calculate percentage of countries with 18/82 patterns by year
    pattern_by_year = df.groupby("year")["is_1882_pattern"].mean().reset_index()
    pattern_by_year["percentage"] = pattern_by_year["is_1882_pattern"] * 100

    # Find years with the highest percentage of 18/82 patterns
    top_years = pattern_by_year.sort_values("percentage", ascending=False).head(10)

    logger.info("Top 10 years with the highest percentage of 18/82 patterns:")
    for i, row in top_years.iterrows():
        logger.info(f"  {int(row['year'])}: {row['percentage']:.2f}%")

    # Calculate average Gini coefficient by region/continent
    # First, create a mapping of country codes to regions
    regions = {
        "EAS": "East Asia & Pacific",
        "ECS": "Europe & Central Asia",
        "LCN": "Latin America & Caribbean",
        "MEA": "Middle East & North Africa",
        "NAC": "North America",
        "SAS": "South Asia",
        "SSF": "Sub-Saharan Africa"
    }

    # Assign regions based on country codes (simplified approach)
    def assign_region(country_code):
        if country_code.startswith(("CN", "JP", "KR", "ID", "MY", "TH", "VN", "PH")):
            return "East Asia & Pacific"
        elif country_code.startswith(("GB", "DE", "FR", "IT", "ES", "RU", "TR")):
            return "Europe & Central Asia"
        elif country_code.startswith(("BR", "MX", "AR", "CO", "CL", "PE")):
            return "Latin America & Caribbean"
        elif country_code.startswith(("SA", "AE", "EG", "IR", "IQ", "IL")):
            return "Middle East & North Africa"
        elif country_code.startswith(("US", "CA")):
            return "North America"
        elif country_code.startswith(("IN", "PK", "BD", "LK")):
            return "South Asia"
        elif country_code.startswith(("ZA", "NG", "KE", "ET", "GH")):
            return "Sub-Saharan Africa"
        else:
            return "Other"

    df["region"] = df["country_code"].apply(assign_region)

    # Calculate average Gini by region
    region_avg = df.groupby("region")["gini"].mean().reset_index()
    region_avg = region_avg.sort_values("gini", ascending=False)

    logger.info("Average Gini coefficient by region:")
    for i, row in region_avg.iterrows():
        logger.info(f"  {row['region']}: {row['gini']:.2f}")

    # Calculate percentage of 18/82 patterns by region
    region_pattern = df.groupby("region")["is_1882_pattern"].mean().reset_index()
    region_pattern["percentage"] = region_pattern["is_1882_pattern"] * 100
    region_pattern = region_pattern.sort_values("percentage", ascending=False)

    logger.info("Percentage of 18/82 patterns by region:")
    for i, row in region_pattern.iterrows():
        logger.info(f"  {row['region']}: {row['percentage']:.2f}%")

    return {
        "global_avg_by_year": global_avg_by_year.to_dict("records"),
        "pattern_by_year": pattern_by_year.to_dict("records"),
        "top_years": top_years.to_dict("records"),
        "region_avg": region_avg.to_dict("records"),
        "region_pattern": region_pattern.to_dict("records")
    }

def visualize_gini_data(df, results):
    """Create visualizations of Gini index data."""
    logger.info("Creating Gini index visualizations...")

    # Create a figure for global average Gini over time
    plt.figure(figsize=(12, 8))

    global_avg_by_year = pd.DataFrame(results["trends"]["global_avg_by_year"])
    plt.plot(global_avg_by_year["year"], global_avg_by_year["gini"], marker='o', linestyle='-', color='blue')
    plt.axhline(y=82, color='r', linestyle='--', alpha=0.5, label='18/82 Split (Gini = 82)')

    plt.xlabel('Year')
    plt.ylabel('Average Gini Coefficient')
    plt.title('Global Average Gini Coefficient Over Time')
    plt.grid(True, alpha=0.3)
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'global_gini_trend.png'), dpi=300)
    plt.close()

    # Create a figure for percentage of countries with 18/82 patterns over time
    plt.figure(figsize=(12, 8))

    pattern_by_year = pd.DataFrame(results["trends"]["pattern_by_year"])
    plt.plot(pattern_by_year["year"], pattern_by_year["percentage"], marker='o', linestyle='-', color='green')

    plt.xlabel('Year')
    plt.ylabel('Percentage of Countries (%)')
    plt.title('Percentage of Countries with 18/82 Patterns Over Time')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'pattern_trend.png'), dpi=300)
    plt.close()

    # Create a figure for average Gini by region
    plt.figure(figsize=(12, 8))

    region_avg = pd.DataFrame(results["trends"]["region_avg"])
    plt.bar(region_avg["region"], region_avg["gini"], color='skyblue')
    plt.axhline(y=82, color='r', linestyle='--', alpha=0.5, label='18/82 Split (Gini = 82)')

    plt.xlabel('Region')
    plt.ylabel('Average Gini Coefficient')
    plt.title('Average Gini Coefficient by Region')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'region_gini.png'), dpi=300)
    plt.close()

    # Create a figure for percentage of 18/82 patterns by region
    plt.figure(figsize=(12, 8))

    region_pattern = pd.DataFrame(results["trends"]["region_pattern"])
    plt.bar(region_pattern["region"], region_pattern["percentage"], color='lightgreen')

    plt.xlabel('Region')
    plt.ylabel('Percentage of Observations (%)')
    plt.title('Percentage of Observations with 18/82 Patterns by Region')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'region_pattern.png'), dpi=300)
    plt.close()

    # Create a figure for top countries with 18/82 patterns only if there are any
    if len(results["patterns"]["top_countries"]) > 0:
        plt.figure(figsize=(12, 8))

        top_countries = pd.DataFrame(results["patterns"]["top_countries"])
        plt.bar(top_countries["country"], top_countries["percentage"], color='orange')

        plt.xlabel('Country')
        plt.ylabel('Percentage of Observations (%)')
        plt.title('Top Countries with 18/82 Patterns')
        plt.xticks(rotation=45, ha='right')
        plt.grid(axis='y', alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, 'top_countries.png'), dpi=300)
        plt.close()
    else:
        logger.info("No countries with 18/82 patterns found, skipping top countries visualization.")

    logger.info("Visualizations saved to the 'gini_results' directory.")

def main():
    """Main function to analyze World Bank Gini index data."""
    logger.info("Starting World Bank Gini index analysis...")

    # Load the data
    df = load_gini_data()

    # Analyze Gini index data for 18/82 patterns
    patterns_results = analyze_gini_1882_patterns(df)

    # Analyze trends in Gini index data over time
    trends_results = analyze_gini_trends(df)

    # Combine results
    results = {
        "patterns": patterns_results,
        "trends": trends_results
    }

    # Create visualizations
    visualize_gini_data(df, results)

    # Print summary
    logger.info("\n=== World Bank Gini Index Analysis Summary ===")
    logger.info(f"Total countries analyzed: {df['country'].nunique()}")
    logger.info(f"Total years analyzed: {df['year'].nunique()}")
    logger.info(f"Total observations: {len(df)}")
    logger.info(f"Countries with 18/82 patterns: {len(patterns_results['countries_with_1882'])} ({patterns_results['percentage_countries_with_1882']:.2f}%)")
    logger.info(f"Observations with 18/82 patterns: {patterns_results['observations_with_1882']} ({patterns_results['percentage_observations_with_1882']:.2f}%)")

    return results

if __name__ == "__main__":
    main()
