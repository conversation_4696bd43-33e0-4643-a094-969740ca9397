"""
ComphyonΨᶜ Meter - Quantifying Emergent Intelligence

The ComphyonΨᶜ Meter is the instrumentation layer of the ComphyonΨᶜ Framework,
providing tools to measure and monitor emergent intelligence in computational systems.
"""

from .calculator import ComphyonCalculator
from .visualization import ComphyonVisualizer

__version__ = '0.1.0'

class ComphyonMeter:
    """
    Main class for the ComphyonΨᶜ Meter.
    
    Provides methods to calculate ComphyonΨᶜ metrics from system data.
    """
    
    def __init__(self):
        """Initialize the ComphyonΨᶜ Meter."""
        self.calculator = ComphyonCalculator()
        self.visualizer = ComphyonVisualizer()
        self.history = []
    
    def calculate(self, csde_tensor, csfe_tensor, csme_tensor, timestamp=None):
        """
        Calculate ComphyonΨᶜ metrics from system data.
        
        Args:
            csde_tensor: Tensor data from CSDE engine
            csfe_tensor: Tensor data from CSFE engine
            csme_tensor: Tensor data from CSME engine
            timestamp: Optional timestamp for the measurement
            
        Returns:
            Dictionary containing ComphyonΨᶜ metrics
        """
        metrics = self.calculator.calculate_metrics(
            csde_tensor, csfe_tensor, csme_tensor, timestamp
        )
        
        # Store in history
        self.history.append(metrics)
        
        return metrics
