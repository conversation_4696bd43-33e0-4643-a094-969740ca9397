<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>5. Trinity Equation Visualization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 700px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
        .triangle-line {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
    </style>
</head>
<body>
    <h1>5. Trinity Equation Visualization</h1>

    <div class="diagram-container">
        <!-- Trinity Equation -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Trinity Equation Visualization
            <div class="element-number">1</div>
        </div>

        <!-- Equation -->
        <div class="element" style="top: 150px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            <span class="bold-formula">U=T[∑(n=1 to 5) Sn⋅(En+In)⋅Φn]</span>
            <div class="element-number">2</div>
        </div>

        <!-- Truth -->
        <div class="element" style="top: 250px; left: 100px; width: 200px; font-weight: bold; font-size: 16px;">
            Truth
            <div class="element-number">3</div>
        </div>

        <!-- Trust -->
        <div class="element" style="top: 250px; left: 700px; width: 200px; font-weight: bold; font-size: 16px;">
            Trust
            <div class="element-number">4</div>
        </div>

        <!-- Transparency -->
        <div class="element" style="top: 450px; left: 400px; width: 200px; font-weight: bold; font-size: 16px;">
            Transparency
            <div class="element-number">5</div>
        </div>

        <!-- Truth Description -->
        <div class="element" style="top: 350px; left: 100px; width: 200px; font-size: 14px;">
            Factual accuracy<br>Data integrity<br>Source reliability
            <div class="element-number">6</div>
        </div>

        <!-- Trust Description -->
        <div class="element" style="top: 350px; left: 700px; width: 200px; font-size: 14px;">
            System reliability<br>Predictable behavior<br>Consistent outcomes
            <div class="element-number">7</div>
        </div>

        <!-- Transparency Description -->
        <div class="element" style="top: 550px; left: 400px; width: 200px; font-size: 14px;">
            Visibility into processes<br>Explainable decisions<br>Audit capability
            <div class="element-number">8</div>
        </div>

        <!-- Tensorial Governance -->
        <div class="element" style="top: 650px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Tensorial Governance
            <div class="element-number">9</div>
        </div>

        <!-- Connections - direct lines to boxes -->
        <!-- Equation to Truth -->
        <!-- Line removed as requested -->

        <!-- Equation to Trust -->
        <!-- Line removed as requested -->

        <!-- Equation to Transparency -->
        <div class="connection" style="top: 200px; left: 500px; width: 2px; height: 250px;"></div>

        <!-- Truth to Description -->
        <div class="connection" style="top: 300px; left: 200px; width: 2px; height: 50px;"></div>

        <!-- Trust to Description -->
        <div class="connection" style="top: 300px; left: 800px; width: 2px; height: 50px;"></div>

        <!-- Transparency to Description -->
        <div class="connection" style="top: 500px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Triangle connecting the three principles -->
        <div class="triangle-line" style="top: 275px; left: 200px; width: 600px; height: 2px;"></div>
        <div class="triangle-line" style="top: 275px; left: 200px; width: 2px; height: 175px;"></div>
        <div class="triangle-line" style="top: 275px; left: 800px; width: 2px; height: 175px;"></div>
        <div class="triangle-line" style="top: 450px; left: 200px; width: 200px; height: 2px;"></div>
        <div class="triangle-line" style="top: 450px; left: 600px; width: 200px; height: 2px;"></div>

        <!-- All principles to Tensorial Governance -->
        <div class="connection" style="top: 600px; left: 500px; width: 2px; height: 50px;"></div>
    </div>
</body>
</html>
