/**
 * Boundary Enforcer
 *
 * This module implements the Boundary Enforcer, which is responsible for detecting
 * and rejecting operations that violate finite boundaries. It serves as a key
 * component of the Divine Firewall, protecting NEPI from mathematical incompatibility.
 * 
 * The Boundary Enforcer provides:
 * 1. Detection of infinite values, NaN, and values exceeding bounds
 * 2. Detection of circular references and excessive complexity
 * 3. Auto-correction of boundary violations when appropriate
 * 4. Detailed reporting of boundary violations and corrections
 */

const EventEmitter = require('events');
const FiniteUniverse = require('./finite-universe');

/**
 * BoundaryEnforcer class
 * 
 * Enforces boundaries on operations and values to protect against
 * mathematical incompatibility.
 */
class BoundaryEnforcer extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      strictMode: true, // Throw errors on boundary violations
      autoCorrect: true, // Attempt to auto-correct boundary violations
      preventRecursion: true, // Prevent unbounded recursion
      preventInfiniteLoops: true, // Prevent infinite loops
      maxLoopIterations: 10000, // Maximum number of loop iterations
      maxFunctionCalls: 1000, // Maximum number of function calls
      maxStackDepth: 100, // Maximum stack depth
      ...options
    };

    // Initialize FiniteUniverse
    this.finiteUniverse = new FiniteUniverse({
      enableLogging: this.options.enableLogging,
      strictMode: this.options.strictMode,
      autoCorrect: this.options.autoCorrect
    });

    // Forward FiniteUniverse events
    this.finiteUniverse.on('boundary-violation', (data) => {
      this.emit('boundary-violation', data);
    });

    this.finiteUniverse.on('boundary-correction', (data) => {
      this.emit('boundary-correction', data);
    });

    this.finiteUniverse.on('enforcement-error', (data) => {
      this.emit('enforcement-error', data);
    });

    // Initialize call counters
    this.callCounters = new Map();
    this.loopCounters = new Map();
    this.stackDepth = 0;

    if (this.options.enableLogging) {
      console.log('BoundaryEnforcer initialized with options:', this.options);
    }
  }

  /**
   * Enforce boundaries on a value
   * @param {any} value - Value to enforce boundaries on
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {any} - Value with enforced boundaries
   */
  enforceValue(value, domain = 'universal') {
    return this.finiteUniverse.enforce(value, domain);
  }

  /**
   * Enforce boundaries on a function call
   * @param {Function} fn - Function to call
   * @param {Array} args - Arguments to pass to the function
   * @param {Object} context - Context to call the function with
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {any} - Result of the function call with enforced boundaries
   */
  enforceFunction(fn, args = [], context = null, domain = 'universal') {
    // Check if function is valid
    if (typeof fn !== 'function') {
      throw new Error('Invalid function');
    }

    // Get function name for tracking
    const fnName = fn.name || 'anonymous';

    // Track function calls
    if (this.options.preventRecursion) {
      this.incrementCallCounter(fnName);
    }

    try {
      // Call function
      const result = fn.apply(context, args);

      // Enforce boundaries on result
      return this.enforceValue(result, domain);
    } finally {
      // Decrement call counter
      if (this.options.preventRecursion) {
        this.decrementCallCounter(fnName);
      }
    }
  }

  /**
   * Enforce boundaries on an async function call
   * @param {Function} fn - Async function to call
   * @param {Array} args - Arguments to pass to the function
   * @param {Object} context - Context to call the function with
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {Promise} - Promise that resolves to the result of the function call with enforced boundaries
   */
  async enforceAsyncFunction(fn, args = [], context = null, domain = 'universal') {
    // Check if function is valid
    if (typeof fn !== 'function') {
      throw new Error('Invalid function');
    }

    // Get function name for tracking
    const fnName = fn.name || 'anonymous';

    // Track function calls
    if (this.options.preventRecursion) {
      this.incrementCallCounter(fnName);
    }

    try {
      // Call function
      const result = await fn.apply(context, args);

      // Enforce boundaries on result
      return this.enforceValue(result, domain);
    } finally {
      // Decrement call counter
      if (this.options.preventRecursion) {
        this.decrementCallCounter(fnName);
      }
    }
  }

  /**
   * Enforce boundaries on a loop
   * @param {Function} condition - Loop condition function
   * @param {Function} body - Loop body function
   * @param {Function} update - Loop update function
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {any} - Result of the loop with enforced boundaries
   */
  enforceLoop(condition, body, update = () => {}, domain = 'universal') {
    // Generate a unique ID for this loop
    const loopId = `loop-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Initialize loop counter
    this.loopCounters.set(loopId, 0);

    let result;

    try {
      // Execute loop
      while (this.enforceValue(condition(), domain)) {
        // Check loop counter
        if (this.options.preventInfiniteLoops) {
          const count = this.loopCounters.get(loopId) + 1;
          this.loopCounters.set(loopId, count);

          if (count > this.options.maxLoopIterations) {
            const error = new Error(`Maximum loop iterations (${this.options.maxLoopIterations}) exceeded`);
            this.emit('infinite-loop-detected', { loopId, iterations: count });

            if (this.options.strictMode) {
              throw error;
            }

            break;
          }
        }

        // Execute loop body
        result = this.enforceValue(body(), domain);

        // Execute loop update
        update();
      }

      return result;
    } finally {
      // Clean up loop counter
      this.loopCounters.delete(loopId);
    }
  }

  /**
   * Increment call counter for a function
   * @param {string} fnName - Function name
   */
  incrementCallCounter(fnName) {
    const count = (this.callCounters.get(fnName) || 0) + 1;
    this.callCounters.set(fnName, count);
    this.stackDepth++;

    // Check for excessive recursion
    if (count > this.options.maxFunctionCalls) {
      const error = new Error(`Maximum function calls (${this.options.maxFunctionCalls}) exceeded for function ${fnName}`);
      this.emit('excessive-recursion-detected', { function: fnName, calls: count });

      if (this.options.strictMode) {
        throw error;
      }
    }

    // Check for excessive stack depth
    if (this.stackDepth > this.options.maxStackDepth) {
      const error = new Error(`Maximum stack depth (${this.options.maxStackDepth}) exceeded`);
      this.emit('excessive-stack-depth-detected', { depth: this.stackDepth });

      if (this.options.strictMode) {
        throw error;
      }
    }
  }

  /**
   * Decrement call counter for a function
   * @param {string} fnName - Function name
   */
  decrementCallCounter(fnName) {
    const count = (this.callCounters.get(fnName) || 1) - 1;
    this.callCounters.set(fnName, count);
    this.stackDepth--;
  }

  /**
   * Get violation statistics
   * @returns {Object} - Violation statistics
   */
  getViolationStats() {
    return this.finiteUniverse.getViolationStats();
  }

  /**
   * Get correction statistics
   * @returns {Object} - Correction statistics
   */
  getCorrectionStats() {
    return this.finiteUniverse.getCorrectionStats();
  }

  /**
   * Reset all statistics
   */
  resetStats() {
    this.finiteUniverse.resetStats();
    this.callCounters.clear();
    this.loopCounters.clear();
    this.stackDepth = 0;
    this.emit('stats-reset');
  }

  /**
   * Create a safe proxy for an object
   * @param {Object} obj - Object to create a safe proxy for
   * @param {string} domain - Domain to enforce boundaries for
   * @returns {Proxy} - Safe proxy for the object
   */
  createSafeProxy(obj, domain = 'universal') {
    const self = this;

    return new Proxy(obj, {
      get(target, prop) {
        const value = target[prop];

        // If the property is a function, return a safe function
        if (typeof value === 'function') {
          return function(...args) {
            return self.enforceFunction(value, args, target, domain);
          };
        }

        // Otherwise, enforce boundaries on the value
        return self.enforceValue(value, domain);
      },

      set(target, prop, value) {
        // Enforce boundaries on the value before setting
        target[prop] = self.enforceValue(value, domain);
        return true;
      }
    });
  }
}

module.exports = BoundaryEnforcer;
