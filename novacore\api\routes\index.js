/**
 * NovaCore Routes Index
 *
 * This file exports all routes for the NovaCore API.
 */

const evidenceRoutes = require('./evidenceRoutes');
const blockchainRoutes = require('./blockchainRoutes');
const connectorRoutes = require('./connectorRoutes');
const safetyRoutes = require('./safetyRoutes');
const novaAssistAIRoutes = require('./novaAssistAI');
const fileUploadRoutes = require('./fileUpload');
const securityRoutes = require('./security');
const complianceRoutes = require('./compliance');
const monitoringRoutes = require('./monitoringRoutes');
const analyticsRoutes = require('./analyticsRoutes');
const userTestingRoutes = require('./userTestingRoutes');
const testResultsAnalysisRoutes = require('./testResultsAnalysisRoutes');
const participantRecruitmentRoutes = require('./participantRecruitmentRoutes');

module.exports = {
  evidenceRoutes,
  blockchainRoutes,
  connectorRoutes,
  safetyRoutes,
  novaAssistAIRoutes,
  fileUploadRoutes,
  securityRoutes,
  complianceRoutes,
  monitoringRoutes,
  analyticsRoutes,
  userTestingRoutes,
  testResultsAnalysisRoutes,
  participantRecruitmentRoutes
};
