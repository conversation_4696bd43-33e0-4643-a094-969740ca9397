import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

const Sidebar = ({ items, title = 'Navigation' }) => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(true);

  const isActive = (path) => {
    return router.pathname === path ? 'text-blue-400 font-semibold' : 'text-gray-300 hover:text-white';
  };

  return (
    <div className="bg-secondary rounded-lg shadow-lg overflow-hidden">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-gray-700 flex justify-between items-center">
        <h3 className="font-bold text-lg">{title}</h3>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="text-gray-400 hover:text-white focus:outline-none"
          aria-label={isOpen ? 'Collapse sidebar' : 'Expand sidebar'}
        >
          {isOpen ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>

      {/* Sidebar Content */}
      {isOpen && (
        <div className="p-4">
          <ul className="space-y-2">
            {items.map((item, index) => (
              <React.Fragment key={index}>
                {item.type === 'link' || !item.type ? (
                  <li>
                    <Link href={item.href} className={`${isActive(item.href)} block py-2 px-3 rounded hover:bg-gray-700`}>
                      {item.label}
                    </Link>
                  </li>
                ) : item.type === 'category' ? (
                  <li className="pt-4 first:pt-0">
                    <div className="text-xs uppercase tracking-wider text-gray-500 font-semibold mb-2 px-3">
                      {item.label}
                    </div>
                    {item.items && (
                      <ul className="space-y-1">
                        {item.items.map((subItem, subIndex) => (
                          <li key={subIndex}>
                            <Link href={subItem.href} className={`${isActive(subItem.href)} block py-2 px-3 rounded hover:bg-gray-700`}>
                              {subItem.label}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </li>
                ) : null}
              </React.Fragment>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
