/**
 * Security Middleware
 * 
 * This middleware provides security features for NovaConnect UAC.
 */

const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cors = require('cors');
const xss = require('xss-clean');
const hpp = require('hpp');
const mongoSanitize = require('express-mongo-sanitize');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const logger = require('../utils/logger');

/**
 * Configure security middleware
 * @param {Object} app - Express app
 */
const configureSecurityMiddleware = (app) => {
  // Set security HTTP headers
  app.use(helmet());
  
  // Enable CORS
  app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-Request-ID'],
    exposedHeaders: ['X-Request-ID', 'X-RateLimit-Limit', 'X-RateLimit-Remaining', 'X-RateLimit-Reset'],
    credentials: true,
    maxAge: 86400 // 24 hours
  }));
  
  // Rate limiting
  const limiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || 60000, 10), // 1 minute
    max: parseInt(process.env.RATE_LIMIT_MAX || 100, 10), // 100 requests per minute
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      // Use API key or IP address as rate limit key
      return req.headers['x-api-key'] || req.ip;
    },
    handler: (req, res) => {
      logger.warn('Rate limit exceeded', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        apiKey: req.headers['x-api-key'] ? '***' : undefined
      });
      
      res.status(429).json({
        error: {
          type: 'rate_limit_error',
          message: 'Rate limit exceeded',
          status: 429
        }
      });
    }
  });
  
  // Apply rate limiting to all API routes
  app.use('/api', limiter);
  
  // Data sanitization against NoSQL query injection
  app.use(mongoSanitize());
  
  // Data sanitization against XSS
  app.use(xss());
  
  // Prevent parameter pollution
  app.use(hpp({
    whitelist: [
      'page',
      'limit',
      'sort',
      'fields',
      'filter',
      'type',
      'status'
    ]
  }));
  
  // Request ID middleware
  app.use(requestIdMiddleware);
  
  // Security headers middleware
  app.use(securityHeadersMiddleware);
  
  // Content security policy middleware
  app.use(contentSecurityPolicyMiddleware);
  
  // IP filtering middleware (if enabled)
  if (process.env.IP_FILTERING_ENABLED === 'true') {
    app.use(ipFilteringMiddleware);
  }
  
  // API key validation middleware
  app.use('/api', apiKeyValidationMiddleware);
  
  logger.info('Security middleware configured');
};

/**
 * Request ID middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requestIdMiddleware = (req, res, next) => {
  // Use existing request ID or generate a new one
  const requestId = req.headers['x-request-id'] || uuidv4();
  
  // Set request ID on request and response
  req.requestId = requestId;
  res.setHeader('X-Request-ID', requestId);
  
  next();
};

/**
 * Security headers middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const securityHeadersMiddleware = (req, res, next) => {
  // Set security headers
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Download-Options', 'noopen');
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Set HSTS header in production
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  next();
};

/**
 * Content security policy middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const contentSecurityPolicyMiddleware = (req, res, next) => {
  // Set content security policy
  res.setHeader('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self'",
    "style-src 'self'",
    "img-src 'self' data:",
    "font-src 'self'",
    "connect-src 'self'",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'"
  ].join('; '));
  
  next();
};

/**
 * IP filtering middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const ipFilteringMiddleware = (req, res, next) => {
  // Get allowed IPs from environment variable
  const allowedIPs = (process.env.ALLOWED_IPS || '').split(',').map(ip => ip.trim()).filter(Boolean);
  
  // If no allowed IPs are configured, allow all IPs
  if (allowedIPs.length === 0) {
    return next();
  }
  
  // Check if client IP is allowed
  const clientIP = req.ip;
  
  if (!allowedIPs.includes(clientIP)) {
    logger.warn('IP access denied', {
      ip: clientIP,
      path: req.path,
      method: req.method
    });
    
    return res.status(403).json({
      error: {
        type: 'ip_access_denied',
        message: 'Access denied from this IP address',
        status: 403
      }
    });
  }
  
  next();
};

/**
 * API key validation middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const apiKeyValidationMiddleware = (req, res, next) => {
  // Skip API key validation for health check and public endpoints
  if (req.path === '/health' || req.path.startsWith('/public')) {
    return next();
  }
  
  // Get API key from header
  const apiKey = req.headers['x-api-key'];
  
  // Check if API key is provided
  if (!apiKey) {
    logger.warn('API key missing', {
      ip: req.ip,
      path: req.path,
      method: req.method
    });
    
    return res.status(401).json({
      error: {
        type: 'authentication_error',
        message: 'API key is required',
        status: 401
      }
    });
  }
  
  // TODO: Validate API key against database or secret manager
  // For now, use a simple environment variable for testing
  const validApiKey = process.env.API_KEY;
  
  if (apiKey !== validApiKey) {
    logger.warn('Invalid API key', {
      ip: req.ip,
      path: req.path,
      method: req.method
    });
    
    return res.status(401).json({
      error: {
        type: 'authentication_error',
        message: 'Invalid API key',
        status: 401
      }
    });
  }
  
  // Add API key to request
  req.apiKey = apiKey;
  
  next();
};

/**
 * Generate a secure random token
 * @param {number} length - Token length
 * @returns {string} - Secure random token
 */
const generateSecureToken = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Hash a password
 * @param {string} password - Password to hash
 * @returns {string} - Hashed password
 */
const hashPassword = (password) => {
  const salt = crypto.randomBytes(16).toString('hex');
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return `${salt}:${hash}`;
};

/**
 * Verify a password
 * @param {string} password - Password to verify
 * @param {string} hashedPassword - Hashed password
 * @returns {boolean} - Whether the password is valid
 */
const verifyPassword = (password, hashedPassword) => {
  const [salt, hash] = hashedPassword.split(':');
  const verifyHash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  return hash === verifyHash;
};

/**
 * Encrypt data
 * @param {string} data - Data to encrypt
 * @param {string} key - Encryption key
 * @returns {string} - Encrypted data
 */
const encryptData = (data, key) => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'hex'), iv);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`;
};

/**
 * Decrypt data
 * @param {string} encryptedData - Encrypted data
 * @param {string} key - Encryption key
 * @returns {string} - Decrypted data
 */
const decryptData = (encryptedData, key) => {
  const [ivHex, encrypted] = encryptedData.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'hex'), iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

module.exports = {
  configureSecurityMiddleware,
  requestIdMiddleware,
  securityHeadersMiddleware,
  contentSecurityPolicyMiddleware,
  ipFilteringMiddleware,
  apiKeyValidationMiddleware,
  generateSecureToken,
  hashPassword,
  verifyPassword,
  encryptData,
  decryptData
};
