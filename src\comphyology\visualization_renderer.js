/**
 * Comphyology Visualization Renderer
 * 
 * This module provides functions to render Comphyology visualizations as HTML.
 * It uses D3.js for rendering charts and diagrams.
 */

/**
 * Generate HTML for a Comphyology visualization
 * 
 * @param {Object} visualizationData - Visualization data from ComphyologyVisualization
 * @returns {string} - HTML string
 */
function generateVisualizationHTML(visualizationData) {
  const { type } = visualizationData;
  
  switch (type) {
    case 'morphological_resonance_field':
      return generateMorphologicalResonanceFieldHTML(visualizationData);
    case 'quantum_phase_space_map':
      return generateQuantumPhaseSpaceMapHTML(visualizationData);
    case 'ethical_tensor_projection':
      return generateEthicalTensorProjectionHTML(visualizationData);
    case 'trinity_integration_diagram':
      return generateTrinityIntegrationDiagramHTML(visualizationData);
    default:
      return `<div class="error">Unknown visualization type: ${type}</div>`;
  }
}

/**
 * Generate HTML for a Morphological Resonance Field visualization
 * 
 * @param {Object} visualizationData - Visualization data
 * @returns {string} - HTML string
 */
function generateMorphologicalResonanceFieldHTML(visualizationData) {
  const { title, xLabel, yLabel, zLabel, colorScale } = visualizationData;
  
  return `
    <div class="comphyology-visualization morphological-resonance-field">
      <h2>${title}</h2>
      <div class="visualization-container">
        <div id="morphological-resonance-field-chart" class="chart-container"></div>
      </div>
      <div class="visualization-controls">
        <div class="control-group">
          <label for="environmental-pressure-slider">Environmental Pressure:</label>
          <input type="range" id="environmental-pressure-slider" min="0" max="4" step="1" value="2">
          <span id="environmental-pressure-value">0.5</span>
        </div>
      </div>
      <div class="visualization-legend">
        <div class="axis-label x-label">${xLabel}</div>
        <div class="axis-label y-label">${yLabel}</div>
        <div class="axis-label z-label">${zLabel}</div>
        <div class="color-scale ${colorScale}">
          <div class="scale-gradient"></div>
          <div class="scale-labels">
            <span class="min-label">Low</span>
            <span class="max-label">High</span>
          </div>
        </div>
      </div>
      <div class="visualization-description">
        <p>
          The Morphological Resonance Field visualization shows how structural complexity interacts with 
          environmental factors to produce resonance patterns. Higher resonance (brighter colors) indicates 
          stronger structural integrity in the face of environmental pressure.
        </p>
        <p>
          Use the slider to adjust the environmental pressure and observe how different structures 
          respond to changing conditions.
        </p>
      </div>
      <script>
        // This script would be replaced with actual D3.js code in a real implementation
        console.log('Rendering Morphological Resonance Field visualization');
      </script>
    </div>
  `;
}

/**
 * Generate HTML for a Quantum Phase Space Map visualization
 * 
 * @param {Object} visualizationData - Visualization data
 * @returns {string} - HTML string
 */
function generateQuantumPhaseSpaceMapHTML(visualizationData) {
  const { title, xLabel, yLabel, zLabel, colorScale } = visualizationData;
  
  return `
    <div class="comphyology-visualization quantum-phase-space-map">
      <h2>${title}</h2>
      <div class="visualization-container">
        <div id="quantum-phase-space-map-chart" class="chart-container"></div>
      </div>
      <div class="visualization-controls">
        <div class="control-group">
          <label for="visualization-mode-selector">Visualization Mode:</label>
          <select id="visualization-mode-selector">
            <option value="certainty">Certainty</option>
            <option value="patterns">Pattern Strength</option>
            <option value="phase-space">Phase Space</option>
            <option value="vector-field">Vector Field</option>
          </select>
        </div>
      </div>
      <div class="visualization-legend">
        <div class="axis-label x-label">${xLabel}</div>
        <div class="axis-label y-label">${yLabel}</div>
        <div class="axis-label z-label">${zLabel}</div>
        <div class="color-scale ${colorScale}">
          <div class="scale-gradient"></div>
          <div class="scale-labels">
            <span class="min-label">Low</span>
            <span class="max-label">High</span>
          </div>
        </div>
      </div>
      <div class="visualization-description">
        <p>
          The Quantum Phase Space Map visualization shows the relationship between entropy and phase, 
          revealing patterns and certainty in the quantum-inspired state space. Brighter colors indicate 
          higher certainty, while arrows show the direction of increasing pattern strength.
        </p>
        <p>
          Use the dropdown to switch between different visualization modes to explore different aspects 
          of the quantum phase space.
        </p>
      </div>
      <script>
        // This script would be replaced with actual D3.js code in a real implementation
        console.log('Rendering Quantum Phase Space Map visualization');
      </script>
    </div>
  `;
}

/**
 * Generate HTML for an Ethical Tensor Projection visualization
 * 
 * @param {Object} visualizationData - Visualization data
 * @returns {string} - HTML string
 */
function generateEthicalTensorProjectionHTML(visualizationData) {
  const { title, xLabel, yLabel, zLabel, colorScale } = visualizationData;
  
  return `
    <div class="comphyology-visualization ethical-tensor-projection">
      <h2>${title}</h2>
      <div class="visualization-container">
        <div id="ethical-tensor-projection-chart" class="chart-container"></div>
      </div>
      <div class="visualization-controls">
        <div class="control-group">
          <label for="accountability-slider">Accountability:</label>
          <input type="range" id="accountability-slider" min="0" max="4" step="1" value="2">
          <span id="accountability-value">0.5</span>
        </div>
        <div class="control-group">
          <label for="decision-threshold-slider">Decision Threshold:</label>
          <input type="range" id="decision-threshold-slider" min="0" max="3" step="1" value="1">
          <span id="decision-threshold-value">0.5</span>
        </div>
      </div>
      <div class="visualization-legend">
        <div class="axis-label x-label">${xLabel}</div>
        <div class="axis-label y-label">${yLabel}</div>
        <div class="axis-label z-label">${zLabel}</div>
        <div class="color-scale ${colorScale}">
          <div class="scale-gradient"></div>
          <div class="scale-labels">
            <span class="min-label">Low</span>
            <span class="max-label">High</span>
          </div>
        </div>
      </div>
      <div class="visualization-description">
        <p>
          The Ethical Tensor Projection visualization shows how fairness and transparency interact to 
          produce ethical tensor values. Brighter colors indicate higher ethical tensor values, while 
          contour lines show decision boundaries at different thresholds.
        </p>
        <p>
          Use the accountability slider to adjust the accountability dimension and observe how it affects 
          the ethical tensor field. Use the decision threshold slider to highlight different decision 
          boundaries.
        </p>
      </div>
      <script>
        // This script would be replaced with actual D3.js code in a real implementation
        console.log('Rendering Ethical Tensor Projection visualization');
      </script>
    </div>
  `;
}

/**
 * Generate HTML for a Trinity Integration Diagram visualization
 * 
 * @param {Object} visualizationData - Visualization data
 * @returns {string} - HTML string
 */
function generateTrinityIntegrationDiagramHTML(visualizationData) {
  const { title, nodes, edges } = visualizationData;
  
  // Generate node HTML
  const nodeHTML = nodes.map(node => `
    <div class="node node-${node.id} node-group-${node.group}" style="--node-size: ${node.size}px;">
      <div class="node-label">${node.label}</div>
    </div>
  `).join('');
  
  // Generate edge HTML
  const edgeHTML = edges.map(edge => `
    <div class="edge edge-${edge.source}-${edge.target}" 
         data-source="${edge.source}" 
         data-target="${edge.target}"
         style="--edge-value: ${edge.value};">
      <div class="edge-label">${edge.label}</div>
    </div>
  `).join('');
  
  return `
    <div class="comphyology-visualization trinity-integration-diagram">
      <h2>${title}</h2>
      <div class="visualization-container">
        <div id="trinity-integration-diagram-chart" class="chart-container">
          <div class="nodes">
            ${nodeHTML}
          </div>
          <div class="edges">
            ${edgeHTML}
          </div>
        </div>
      </div>
      <div class="visualization-controls">
        <div class="control-group">
          <label for="highlight-group-selector">Highlight Group:</label>
          <select id="highlight-group-selector">
            <option value="all">All</option>
            <option value="foundation">Foundation</option>
            <option value="framework">Framework</option>
            <option value="implementation">Implementation</option>
            <option value="component">Component</option>
            <option value="concept">Concept</option>
          </select>
        </div>
      </div>
      <div class="visualization-legend">
        <div class="legend-item legend-foundation">
          <div class="legend-color"></div>
          <div class="legend-label">Foundation</div>
        </div>
        <div class="legend-item legend-framework">
          <div class="legend-color"></div>
          <div class="legend-label">Framework</div>
        </div>
        <div class="legend-item legend-implementation">
          <div class="legend-color"></div>
          <div class="legend-label">Implementation</div>
        </div>
        <div class="legend-item legend-component">
          <div class="legend-color"></div>
          <div class="legend-label">Component</div>
        </div>
        <div class="legend-item legend-concept">
          <div class="legend-color"></div>
          <div class="legend-label">Concept</div>
        </div>
      </div>
      <div class="visualization-description">
        <p>
          The Trinity Integration Diagram visualization shows how Comphyology enhances the Trinity CSDE 
          architecture. Nodes represent concepts and components, while edges represent relationships 
          between them.
        </p>
        <p>
          Use the dropdown to highlight different groups of nodes and explore the relationships 
          between them.
        </p>
      </div>
      <script>
        // This script would be replaced with actual D3.js code in a real implementation
        console.log('Rendering Trinity Integration Diagram visualization');
      </script>
    </div>
  `;
}

/**
 * Generate CSS for Comphyology visualizations
 * 
 * @returns {string} - CSS string
 */
function generateVisualizationCSS() {
  return `
    .comphyology-visualization {
      font-family: 'Arial', sans-serif;
      margin: 20px 0;
      padding: 20px;
      border: 1px solid #ddd;
      border-radius: 8px;
      background-color: #f9f9f9;
    }
    
    .comphyology-visualization h2 {
      margin-top: 0;
      color: #333;
      font-size: 1.5em;
    }
    
    .visualization-container {
      position: relative;
      width: 100%;
      height: 500px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .chart-container {
      width: 100%;
      height: 100%;
    }
    
    .visualization-controls {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    
    .control-group {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .visualization-legend {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      align-items: center;
    }
    
    .axis-label {
      font-weight: bold;
      color: #555;
    }
    
    .color-scale {
      display: flex;
      flex-direction: column;
      width: 150px;
    }
    
    .scale-gradient {
      height: 20px;
      border-radius: 4px;
      background: linear-gradient(to right, #000, #fff);
    }
    
    .color-scale.viridis .scale-gradient {
      background: linear-gradient(to right, #440154, #414487, #2a788e, #22a884, #7ad151, #fde725);
    }
    
    .color-scale.plasma .scale-gradient {
      background: linear-gradient(to right, #0d0887, #5302a3, #8b0aa5, #b83289, #db5c68, #f48849, #febd2a, #f0f921);
    }
    
    .color-scale.cividis .scale-gradient {
      background: linear-gradient(to right, #00204c, #213d6b, #555b6c, #7b7b78, #a59c74, #d6c064, #fde737);
    }
    
    .scale-labels {
      display: flex;
      justify-content: space-between;
      margin-top: 2px;
      font-size: 0.8em;
      color: #666;
    }
    
    .visualization-description {
      margin-top: 20px;
      color: #555;
      font-size: 0.9em;
      line-height: 1.4;
    }
    
    /* Trinity Integration Diagram specific styles */
    .nodes {
      position: relative;
    }
    
    .node {
      position: absolute;
      width: var(--node-size);
      height: var(--node-size);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      font-size: 0.8em;
      font-weight: bold;
      color: #fff;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .node-group-foundation { background-color: #3498db; }
    .node-group-framework { background-color: #9b59b6; }
    .node-group-implementation { background-color: #2ecc71; }
    .node-group-component { background-color: #e74c3c; }
    .node-group-concept { background-color: #f39c12; }
    
    .edges {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
    
    .edge {
      position: absolute;
      height: 2px;
      background-color: #aaa;
      transform-origin: 0 0;
    }
    
    .edge-label {
      position: absolute;
      font-size: 0.7em;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 2px 4px;
      border-radius: 2px;
      white-space: nowrap;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .legend-color {
      width: 15px;
      height: 15px;
      border-radius: 50%;
    }
    
    .legend-foundation .legend-color { background-color: #3498db; }
    .legend-framework .legend-color { background-color: #9b59b6; }
    .legend-implementation .legend-color { background-color: #2ecc71; }
    .legend-component .legend-color { background-color: #e74c3c; }
    .legend-concept .legend-color { background-color: #f39c12; }
  `;
}

module.exports = {
  generateVisualizationHTML,
  generateVisualizationCSS
};
