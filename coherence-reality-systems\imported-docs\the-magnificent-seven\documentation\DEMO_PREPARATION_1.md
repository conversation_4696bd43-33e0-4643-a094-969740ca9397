# "Breach to Boardroom" Demo Preparation Guide

This guide provides detailed instructions for preparing and executing the "Breach to Boardroom" demo for Google leadership.

## 1. Pre-Demo Setup (1-2 Days Before)

### Environment Setup

1. **Provision GCP Resources**:
   ```bash
   # Set environment variables
   export GCP_PROJECT_ID=your-project-id
   export GCP_ORGANIZATION_ID=your-organization-id
   export GCP_CREDENTIALS_PATH=/path/to/credentials.json
   
   # Deploy test environment
   node scripts/deploy-test-env.js
   ```

2. **Verify BigQuery Dataset**:
   - Log into GCP Console
   - Navigate to BigQuery
   - Verify that the `patient_records` dataset exists
   - Check that it contains PHI data
   - Confirm it has public access (for demo purposes)

3. **Verify Security Command Center**:
   - Navigate to Security Command Center
   - Ensure API access is enabled
   - Test a manual finding creation

4. **Verify NovaConnect Deployment**:
   - Start the NovaConnect API server:
     ```bash
     cd deployment
     ./start-api.sh
     ```
   - Verify the API is running: http://localhost:3000/health
   - Test the connectors:
     ```bash
     curl -H "Authorization: Bearer test-api-key" http://localhost:3000/api/connectors
     ```

5. **Prepare Looker Dashboard**:
   - Set up the HIPAA compliance dashboard
   - Configure the dashboard to show:
     - Compliance score (99.9%)
     - Incident timeline
     - Cost savings calculator
     - Remediation metrics

### Demo Environment Testing

1. **Run End-to-End Test**:
   ```bash
   # Run the demo setup
   npm run demo:setup
   
   # Create a test finding
   npm run demo:setup:with-finding
   ```

2. **Verify Remediation**:
   - Check that the finding was detected
   - Verify the remediation workflow executed
   - Confirm the dataset was encrypted
   - Confirm access controls were updated
   - Verify the dashboard was updated

3. **Measure Performance**:
   - Time the remediation process (should be <8 seconds)
   - Verify data normalization speed (should be <1ms per finding)
   - Check dashboard update time

4. **Record Backup Video**:
   - Record a full demo run as backup
   - Save the video in the `demo/breach-to-boardroom/backup` directory
   - Test playing the video during the presentation

## 2. Day of Demo Preparation (2-3 Hours Before)

### Final Checks

1. **Reset Demo Environment**:
   ```bash
   # Reset the demo environment
   node demo/breach-to-boardroom/reset-demo.js
   
   # Verify reset was successful
   npm run demo:setup
   ```

2. **Verify Connectivity**:
   - Check internet connection
   - Verify GCP console access
   - Test NovaConnect API access
   - Confirm Looker dashboard access

3. **Prepare Presentation Environment**:
   - Set up dual monitors
   - Arrange windows for easy navigation:
     - GCP Console (BigQuery)
     - Security Command Center
     - NovaConnect API logs
     - Looker Dashboard
   - Test screen sharing
   - Check audio/video

4. **Rehearse Demo Flow**:
   - Practice the full demo flow (0:00-1:00)
   - Time each section:
     - The Breach (0:00-0:30)
     - Auto-Containment (0:30-0:38)
     - Boardroom Ready (0:38-1:00)
   - Practice key talking points
   - Rehearse transitions between screens

### Prepare Handouts

1. **Print Key Materials**:
   - Performance comparison with AWS/Azure
   - ROI calculator
   - Technical architecture diagram
   - Integration roadmap

2. **Prepare Digital Assets**:
   - PDF versions of all handouts
   - Link to technical white paper
   - QR code for follow-up resources

## 3. Demo Execution (1 Hour)

### Pre-Demo (15 Minutes)

1. **Set Up Room**:
   - Connect laptop to projector/screen
   - Test audio/visual
   - Distribute handouts
   - Start recording (if permitted)

2. **Final Environment Check**:
   - Verify NovaConnect API is running
   - Check BigQuery dataset is accessible
   - Confirm SCC access
   - Load Looker dashboard

### Demo Flow (1 Minute)

Follow the script in `demo-script.md`:

1. **The Breach (0:00-0:30)**:
   - Show BigQuery dataset with public access
   - Highlight PHI data
   - Explain compliance implications

2. **Auto-Containment (0:30-0:38)**:
   - Show SCC finding
   - Show NovaConnect receiving and normalizing
   - Show remediation workflow execution
   - Highlight the 8-second timeframe

3. **Boardroom Ready (0:38-1:00)**:
   - Show Looker dashboard
   - Highlight compliance score
   - Show incident timeline
   - Emphasize cost savings

### Q&A and Discussion (30-45 Minutes)

Be prepared to answer questions about:

1. **Technical Implementation**:
   - How NovaConnect integrates with GCP services
   - How the normalization engine achieves sub-millisecond performance
   - How the remediation engine works

2. **Business Value**:
   - ROI calculation methodology
   - Comparison with AWS/Azure solutions
   - Value for regulated industries

3. **Strategic Fit with Google**:
   - How NovaConnect fills Google's compliance gap
   - Integration roadmap with other Google services
   - Go-to-market strategy

## 4. Post-Demo Follow-Up (Same Day)

1. **Send Follow-Up Materials**:
   - Thank you email
   - Digital copies of handouts
   - Technical white paper
   - ROI calculator
   - Case studies

2. **Document Feedback**:
   - Record questions asked
   - Note areas of interest
   - Document any concerns raised

3. **Schedule Next Steps**:
   - Technical deep dive
   - Proof of concept
   - Business discussion

## Troubleshooting

### SCC Finding Not Generated

If the SCC finding is not generated automatically:

1. Manually create a finding:
   ```bash
   node demo/breach-to-boardroom/create-finding.js
   ```

2. If that fails, use the pre-recorded demo:
   ```bash
   # Play backup video
   open demo/breach-to-boardroom/backup/full-demo.mp4
   ```

### Remediation Workflow Fails

If the remediation workflow fails:

1. Check the API logs:
   ```bash
   tail -f logs/api.log
   ```

2. Try restarting the API:
   ```bash
   cd deployment
   ./restart-api.sh
   ```

3. If all else fails, use the static dashboard backup:
   ```bash
   open demo/breach-to-boardroom/backup/dashboard.html
   ```

### Network Issues

If you encounter network issues:

1. Switch to local demo mode:
   ```bash
   node demo/breach-to-boardroom/local-demo.js
   ```

2. Use the pre-recorded video as backup

## Demo Success Criteria

The demo is considered successful if:

1. The breach is detected and remediated in under 8 seconds
2. The dashboard shows the compliance score maintained at 99.9%
3. The audience understands the key differentiators:
   - 3,142x faster data normalization
   - 4-6x faster remediation
   - 13.8x higher throughput
4. The strategic value proposition is clear:
   - "Google's compliance central nervous system"
   - "18-24 month lead over AWS/Azure"
   - "Why build when we're 3 years ahead?"
