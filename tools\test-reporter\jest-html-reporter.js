/**
 * NovaFuse Jest HTML Reporter
 * 
 * This Jest reporter generates HTML reports from test results.
 */

const path = require('path');
const { generateHtmlReport, generateJsonReport } = require('./simple-test-reporter');

class JestHtmlReporter {
  constructor(globalConfig, options) {
    this._globalConfig = globalConfig;
    this._options = options || {};
  }
  
  onRunComplete(contexts, results) {
    // Generate timestamp for the report filename
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
    
    // Determine the output directory
    const outputDir = this._options.outputDir || 'test-results';
    
    // Generate the HTML report
    const htmlOutputPath = path.join(outputDir, `test-report-${timestamp}.html`);
    generateHtmlReport(results, htmlOutputPath);
    
    // Generate the JSON report if enabled
    if (this._options.jsonReport) {
      const jsonOutputPath = path.join(outputDir, `test-report-${timestamp}.json`);
      generateJsonReport(results, jsonOutputPath);
    }
  }
}

module.exports = JestHtmlReporter;
