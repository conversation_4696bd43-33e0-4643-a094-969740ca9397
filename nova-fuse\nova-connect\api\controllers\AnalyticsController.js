/**
 * Analytics Controller
 *
 * This controller handles API requests related to analytics.
 */

const AnalyticsService = require('../services/AnalyticsService');
const { ValidationError } = require('../utils/errors');

class AnalyticsController {
  constructor() {
    this.analyticsService = new AnalyticsService();
  }

  /**
   * Get usage analytics
   */
  async getUsageAnalytics(req, res, next) {
    try {
      const filters = req.query;

      const analytics = await this.analyticsService.getUsageAnalytics(filters);

      res.json(analytics);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get performance analytics
   */
  async getPerformanceAnalytics(req, res, next) {
    try {
      const filters = req.query;

      const analytics = await this.analyticsService.getPerformanceAnalytics(filters);

      res.json(analytics);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get error analytics
   */
  async getErrorAnalytics(req, res, next) {
    try {
      const filters = req.query;

      const analytics = await this.analyticsService.getErrorAnalytics(filters);

      res.json(analytics);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get dashboard analytics
   */
  async getDashboardAnalytics(req, res, next) {
    try {
      // Get date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30); // Last 30 days

      const filters = {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      };

      // Get analytics data
      const [usage, performance, errors] = await Promise.all([
        this.analyticsService.getUsageAnalytics(filters),
        this.analyticsService.getPerformanceAnalytics(filters),
        this.analyticsService.getErrorAnalytics(filters)
      ]);

      // Combine data for dashboard
      const dashboard = {
        summary: {
          totalRequests: usage.summary.totalRequests,
          averageResponseTime: performance.summary.averageResponseTime,
          p95ResponseTime: performance.summary.p95ResponseTime,
          successRate: usage.summary.successRate,
          errorRate: usage.summary.errorRate,
          uniqueEndpoints: usage.summary.uniqueEndpoints,
          uniqueUsers: usage.summary.uniqueUsers,
          totalErrors: errors.summary.totalErrors
        },
        charts: {
          usageByDate: usage.usageByDate,
          responseTimeByDate: performance.avgResponseTimeByDate,
          errorsByDate: errors.errorsByDate,
          usageByHour: usage.usageByHour,
          responseTimeByHour: performance.avgResponseTimeByHour,
          errorsByHour: errors.errorsByHour
        },
        topEndpoints: usage.topEndpoints,
        slowestEndpoints: performance.slowestEndpoints,
        topErrorEndpoints: errors.topErrorEndpoints,
        topErrorCodes: errors.topErrorCodes,
        recentRequests: usage.recentRequests.slice(0, 10),
        recentErrors: errors.recentErrors.slice(0, 10)
      };

      res.json(dashboard);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all report templates
   */
  async getReportTemplates(req, res, next) {
    try {
      const templates = await this.analyticsService.getReportTemplates();
      res.json(templates);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get report template by ID
   */
  async getReportTemplateById(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Report template ID is required');
      }

      const template = await this.analyticsService.getReportTemplateById(id);
      res.json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create report template
   */
  async createReportTemplate(req, res, next) {
    try {
      const data = req.body;

      if (!data) {
        throw new ValidationError('Report template data is required');
      }

      const template = await this.analyticsService.createReportTemplate(data);
      res.status(201).json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update report template
   */
  async updateReportTemplate(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;

      if (!id) {
        throw new ValidationError('Report template ID is required');
      }

      if (!data) {
        throw new ValidationError('Report template data is required');
      }

      const template = await this.analyticsService.updateReportTemplate(id, data);
      res.json(template);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete report template
   */
  async deleteReportTemplate(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Report template ID is required');
      }

      const result = await this.analyticsService.deleteReportTemplate(id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all reports
   */
  async getReports(req, res, next) {
    try {
      const filters = req.query;
      const reports = await this.analyticsService.getReports(filters);
      res.json(reports);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get report by ID
   */
  async getReportById(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Report ID is required');
      }

      const report = await this.analyticsService.getReportById(id);
      res.json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Generate report
   */
  async generateReport(req, res, next) {
    try {
      const { templateId } = req.params;
      const customFilters = req.body || {};

      if (!templateId) {
        throw new ValidationError('Report template ID is required');
      }

      const report = await this.analyticsService.generateReport(templateId, customFilters);
      res.status(201).json(report);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete report
   */
  async deleteReport(req, res, next) {
    try {
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('Report ID is required');
      }

      const result = await this.analyticsService.deleteReport(id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Schedule report
   */
  async scheduleReport(req, res, next) {
    try {
      const { templateId } = req.params;
      const schedule = req.body;

      if (!templateId) {
        throw new ValidationError('Report template ID is required');
      }

      if (!schedule) {
        throw new ValidationError('Schedule data is required');
      }

      const result = await this.analyticsService.scheduleReport(templateId, schedule);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AnalyticsController();
