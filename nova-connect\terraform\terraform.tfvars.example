# NovaConnect UAC Terraform Variables
# Copy this file to terraform.tfvars and update the values

# GCP project ID
project_id = "novafuse-marketplace"

# GCP project number (numeric ID)
project_number = "123456789012"

# GCP organization ID
organization_id = "987654321098"

# GCP region
region = "us-central1"

# Authorized IP ranges
authorized_ip_ranges = [
  "***********/24",  # Example corporate network
  "************/24"  # Example VPN network
]
