/**
 * Enhanced Analytics Routes
 * 
 * This file defines enhanced routes for analytics functionality including:
 * - User analytics
 * - Connector analytics
 * - Compliance analytics
 * - Predictive analytics
 * - Real-time analytics
 * - Custom analytics
 */

const express = require('express');
const router = express.Router();
const EnhancedAnalyticsController = require('../controllers/EnhancedAnalyticsController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

/**
 * @route GET /api/enhanced-analytics/user
 * @description Get user analytics
 * @access Authenticated
 */
router.get('/user', (req, res, next) => {
  EnhancedAnalyticsController.getUserAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/connector
 * @description Get connector analytics
 * @access Authenticated
 */
router.get('/connector', (req, res, next) => {
  EnhancedAnalyticsController.getConnectorAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/compliance
 * @description Get compliance analytics
 * @access Authenticated
 */
router.get('/compliance', (req, res, next) => {
  EnhancedAnalyticsController.getComplianceAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/predictive
 * @description Get predictive analytics
 * @access Authenticated
 */
router.get('/predictive', (req, res, next) => {
  EnhancedAnalyticsController.getPredictiveAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/real-time
 * @description Get real-time analytics
 * @access Authenticated
 */
router.get('/real-time', (req, res, next) => {
  EnhancedAnalyticsController.getRealTimeAnalytics(req, res, next);
});

/**
 * @route POST /api/enhanced-analytics/custom
 * @description Get custom analytics
 * @access Authenticated
 */
router.post('/custom', (req, res, next) => {
  EnhancedAnalyticsController.getCustomAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/dashboard
 * @description Get analytics dashboard
 * @access Authenticated
 */
router.get('/dashboard', (req, res, next) => {
  EnhancedAnalyticsController.getAnalyticsDashboard(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/export
 * @description Export analytics data
 * @access Authenticated
 */
router.get('/export', (req, res, next) => {
  EnhancedAnalyticsController.exportAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/user/:userId
 * @description Get analytics for a specific user
 * @access Admin
 */
router.get('/user/:userId', hasPermission('system:analytics'), (req, res, next) => {
  req.query.userId = req.params.userId;
  EnhancedAnalyticsController.getUserAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/team/:teamId
 * @description Get analytics for a specific team
 * @access Team Admin
 */
router.get('/team/:teamId', hasPermission('team:analytics'), (req, res, next) => {
  req.query.teamId = req.params.teamId;
  EnhancedAnalyticsController.getUserAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/connector/:connectorId
 * @description Get analytics for a specific connector
 * @access Authenticated
 */
router.get('/connector/:connectorId', (req, res, next) => {
  req.query.connectorId = req.params.connectorId;
  EnhancedAnalyticsController.getConnectorAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/connector-type/:connectorType
 * @description Get analytics for a specific connector type
 * @access Authenticated
 */
router.get('/connector-type/:connectorType', (req, res, next) => {
  req.query.connectorType = req.params.connectorType;
  EnhancedAnalyticsController.getConnectorAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/compliance/:framework
 * @description Get compliance analytics for a specific framework
 * @access Authenticated
 */
router.get('/compliance/:framework', (req, res, next) => {
  req.query.framework = req.params.framework;
  EnhancedAnalyticsController.getComplianceAnalytics(req, res, next);
});

/**
 * @route GET /api/enhanced-analytics/compliance/:framework/control/:controlId
 * @description Get compliance analytics for a specific control
 * @access Authenticated
 */
router.get('/compliance/:framework/control/:controlId', (req, res, next) => {
  req.query.framework = req.params.framework;
  req.query.controlId = req.params.controlId;
  EnhancedAnalyticsController.getComplianceAnalytics(req, res, next);
});

module.exports = router;
