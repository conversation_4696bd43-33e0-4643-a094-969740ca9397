/**
 * Base Connector
 * Abstract base class for all data source connectors
 */

/**
 * Abstract base connector class
 * @abstract
 */
class BaseConnector {
  /**
   * Create a new base connector
   * @param {string} type - Connector type identifier
   */
  constructor(type) {
    if (this.constructor === BaseConnector) {
      throw new Error('BaseConnector is an abstract class and cannot be instantiated directly');
    }
    
    this.type = type;
    this.isAuthenticated = false;
  }

  /**
   * Authenticate with the data source
   * @abstract
   * @param {Object} credentials - Authentication credentials
   * @returns {Promise<void>}
   */
  async authenticate(credentials) {
    throw new Error('authenticate() method must be implemented by subclass');
  }

  /**
   * Scan the data source for personal data
   * @abstract
   * @param {Object} options - Scan options
   * @returns {Promise<Array>} Scan results
   */
  async scanData(options) {
    throw new Error('scanData() method must be implemented by subclass');
  }

  /**
   * Check if the connector is authenticated
   * @protected
   * @throws {Error} If not authenticated
   */
  _checkAuthentication() {
    if (!this.isAuthenticated) {
      throw new Error(`Not authenticated with ${this.type}. Call authenticate() first.`);
    }
  }

  /**
   * Get connector type
   * @returns {string} Connector type
   */
  getType() {
    return this.type;
  }

  /**
   * Get authentication status
   * @returns {boolean} True if authenticated
   */
  getAuthenticationStatus() {
    return this.isAuthenticated;
  }
}

module.exports = BaseConnector;
