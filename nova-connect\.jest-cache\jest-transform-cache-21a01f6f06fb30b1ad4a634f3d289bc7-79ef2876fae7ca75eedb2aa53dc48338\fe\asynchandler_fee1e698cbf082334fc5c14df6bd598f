a9272451d832302c8af0f3114f1dac24
/**
 * NovaFuse Universal API Connector - Async Handler
 * 
 * This module provides utilities for handling asynchronous operations.
 */

/**
 * Wrap an async route handler to catch errors and pass them to the error middleware
 * 
 * @param {Function} fn - The async route handler function
 * @returns {Function} - The wrapped route handler
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Retry an async function with exponential backoff
 * 
 * @param {Function} fn - The async function to retry
 * @param {Object} options - Retry options
 * @param {number} options.maxRetries - Maximum number of retries
 * @param {number} options.initialDelay - Initial delay in milliseconds
 * @param {number} options.maxDelay - Maximum delay in milliseconds
 * @param {Function} options.shouldRetry - Function to determine if retry should be attempted
 * @returns {Promise<any>} - The result of the function
 */
async function retryWithBackoff(fn, options = {}) {
  const maxRetries = options.maxRetries || 3;
  const initialDelay = options.initialDelay || 1000;
  const maxDelay = options.maxDelay || 30000;
  const shouldRetry = options.shouldRetry || (() => true);
  let retries = 0;
  let delay = initialDelay;
  while (true) {
    try {
      return await fn();
    } catch (error) {
      // If we've reached the maximum retries or shouldn't retry, throw the error
      if (retries >= maxRetries || !shouldRetry(error)) {
        throw error;
      }

      // Increment retry count
      retries++;

      // Calculate delay with exponential backoff and jitter
      delay = Math.min(delay * 2, maxDelay);
      const jitter = delay * 0.2 * Math.random();
      const actualDelay = delay + jitter;

      // Wait for the delay
      await new Promise(resolve => setTimeout(resolve, actualDelay));
    }
  }
}

/**
 * Create a circuit breaker for an async function
 * 
 * @param {Function} fn - The async function to protect
 * @param {Object} options - Circuit breaker options
 * @param {number} options.failureThreshold - Number of failures before opening the circuit
 * @param {number} options.resetTimeout - Time in milliseconds before attempting to close the circuit
 * @param {Function} options.isFailure - Function to determine if a result is a failure
 * @returns {Function} - The protected function
 */
function circuitBreaker(fn, options = {}) {
  const failureThreshold = options.failureThreshold || 5;
  const resetTimeout = options.resetTimeout || 30000;
  const isFailure = options.isFailure || (error => true);
  let failures = 0;
  let circuitOpen = false;
  let nextAttempt = Date.now();
  return async function circuitBreakerWrapper(...args) {
    // If the circuit is open, check if we should try again
    if (circuitOpen) {
      if (Date.now() < nextAttempt) {
        throw new Error('Circuit is open');
      }

      // Circuit is half-open, allow one request
      circuitOpen = false;
    }
    try {
      const result = await fn(...args);

      // Reset failures on success
      failures = 0;
      return result;
    } catch (error) {
      // Check if this error should count as a failure
      if (isFailure(error)) {
        failures++;

        // If we've reached the threshold, open the circuit
        if (failures >= failureThreshold) {
          circuitOpen = true;
          nextAttempt = Date.now() + resetTimeout;
        }
      }
      throw error;
    }
  };
}
module.exports = {
  asyncHandler,
  retryWithBackoff,
  circuitBreaker
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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