/**
 * Connector.ts
 * 
 * Model for data source connectors in the NovaCore system.
 * These connectors enable integration with various data sources through NovaConnect.
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * Connector type enum
 */
export enum ConnectorType {
  AWS = 'aws',
  AZURE = 'azure',
  GCP = 'gcp',
  GITHUB = 'github',
  JIRA = 'jira',
  SLACK = 'slack',
  REST_API = 'rest_api',
  WEBHOOK = 'webhook',
  DATABASE = 'database',
  FILE_SYSTEM = 'file_system',
  CUSTOM = 'custom',
}

/**
 * Connector status enum
 */
export enum ConnectorStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error',
  PENDING = 'pending',
  CONFIGURING = 'configuring',
}

/**
 * Authentication type enum
 */
export enum AuthenticationType {
  API_KEY = 'api_key',
  OAUTH2 = 'oauth2',
  JWT = 'jwt',
  BASIC = 'basic',
  CERTIFICATE = 'certificate',
  AWS_IAM = 'aws_iam',
  AZURE_AD = 'azure_ad',
  GCP_SERVICE_ACCOUNT = 'gcp_service_account',
  NONE = 'none',
}

/**
 * Authentication configuration interface
 */
export interface AuthenticationConfig {
  type: AuthenticationType;
  credentials: Record<string, any>;
  expiresAt?: Date;
  refreshToken?: string;
}

/**
 * Collection schedule interface
 */
export interface CollectionSchedule {
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'custom';
  interval?: number; // e.g., every 2 hours
  dayOfWeek?: number; // 0-6, where 0 is Sunday
  dayOfMonth?: number; // 1-31
  time?: string; // HH:MM format
  timezone?: string; // e.g., 'America/New_York'
  lastRun?: Date;
  nextRun?: Date;
  enabled: boolean;
}

/**
 * Data mapping interface
 */
export interface DataMapping {
  sourceField: string;
  targetField: string;
  transformation?: string; // e.g., 'uppercase', 'lowercase', 'date_format'
  defaultValue?: any;
}

/**
 * Connector configuration interface
 */
export interface ConnectorConfig {
  baseUrl?: string;
  apiVersion?: string;
  endpoints?: Record<string, string>;
  headers?: Record<string, string>;
  parameters?: Record<string, any>;
  timeout?: number;
  retryConfig?: {
    maxRetries: number;
    retryDelay: number;
  };
  pagination?: {
    enabled: boolean;
    pageSize?: number;
    pageParam?: string;
    limitParam?: string;
  };
  filters?: Record<string, any>;
  dataMapping?: DataMapping[];
  transformationScript?: string;
  validationRules?: Record<string, any>;
  [key: string]: any;
}

/**
 * Connector health check result interface
 */
export interface HealthCheckResult {
  status: 'healthy' | 'unhealthy';
  timestamp: Date;
  responseTime?: number;
  errorMessage?: string;
  details?: Record<string, any>;
}

/**
 * Connector interface
 */
export interface Connector {
  id: string;
  name: string;
  description?: string;
  type: ConnectorType;
  status: ConnectorStatus;
  authentication: AuthenticationConfig;
  config: ConnectorConfig;
  schedule?: CollectionSchedule;
  organization: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  lastHealthCheck?: HealthCheckResult;
  metadata?: Record<string, any>;
  tags?: string[];
}

/**
 * Create a new connector
 */
export function createConnector(
  name: string,
  type: ConnectorType,
  authentication: AuthenticationConfig,
  config: ConnectorConfig,
  createdBy: string,
  organization: string,
  description?: string,
  schedule?: CollectionSchedule,
  metadata?: Record<string, any>,
  tags?: string[],
): Connector {
  const now = new Date();
  
  return {
    id: uuidv4(),
    name,
    description,
    type,
    status: ConnectorStatus.CONFIGURING,
    authentication,
    config,
    schedule,
    organization,
    createdAt: now,
    updatedAt: now,
    createdBy,
    updatedBy: createdBy,
    metadata,
    tags,
  };
}

/**
 * Update connector status
 */
export function updateConnectorStatus(
  connector: Connector,
  status: ConnectorStatus,
  updatedBy: string,
  statusDetails?: Record<string, any>,
): Connector {
  const now = new Date();
  
  return {
    ...connector,
    status,
    updatedAt: now,
    updatedBy,
    metadata: {
      ...connector.metadata,
      statusDetails,
      statusUpdatedAt: now,
    },
  };
}

/**
 * Update connector health check
 */
export function updateConnectorHealthCheck(
  connector: Connector,
  healthCheck: HealthCheckResult,
): Connector {
  return {
    ...connector,
    lastHealthCheck: healthCheck,
    status: healthCheck.status === 'healthy' ? ConnectorStatus.ACTIVE : ConnectorStatus.ERROR,
    metadata: {
      ...connector.metadata,
      lastHealthCheckAt: healthCheck.timestamp,
    },
  };
}

/**
 * Update connector schedule
 */
export function updateConnectorSchedule(
  connector: Connector,
  schedule: CollectionSchedule,
  updatedBy: string,
): Connector {
  const now = new Date();
  
  return {
    ...connector,
    schedule,
    updatedAt: now,
    updatedBy,
  };
}

/**
 * Calculate next run time based on schedule
 */
export function calculateNextRunTime(schedule: CollectionSchedule): Date {
  // This is a placeholder. In a real implementation, we would calculate
  // the next run time based on the schedule configuration.
  const now = new Date();
  
  switch (schedule.frequency) {
    case 'hourly':
      return new Date(now.getTime() + (schedule.interval || 1) * 60 * 60 * 1000);
    case 'daily':
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      return tomorrow;
    case 'weekly':
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);
      nextWeek.setHours(0, 0, 0, 0);
      return nextWeek;
    case 'monthly':
      const nextMonth = new Date(now);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      nextMonth.setDate(1);
      nextMonth.setHours(0, 0, 0, 0);
      return nextMonth;
    default:
      return new Date(now.getTime() + 24 * 60 * 60 * 1000); // Default to daily
  }
}
