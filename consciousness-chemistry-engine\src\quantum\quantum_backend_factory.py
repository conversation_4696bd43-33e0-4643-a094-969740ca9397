"""
Factory for creating and managing quantum backends.

This module provides a factory class for creating and managing different
quantum computing backends in a unified way.
"""

import importlib
import pkgutil
import inspect
import logging
from typing import Dict, Type, List, Optional, Any, TypeVar

# Import all backends to register them
from .qiskit_backend import Qiskit<PERSON>ack<PERSON>
from .cirq_backend import CirqBackend
from .pennylane_backend import PennyLaneBackend
from .braket_backend import BraketBackend

# Type variable for QuantumBackend subclasses
T = TypeVar('T', bound='QuantumBackend')

class QuantumBackendFactory:
    """Factory class for creating and managing quantum backends."""
    
    # Registry of available backends
    _backends: Dict[str, Type['QuantumBackend']] = {}
    
    @classmethod
    def register_backend(cls, backend_class: Type[T]) -> None:
        """Register a new quantum backend class.
        
        Args:
            backend_class: The backend class to register
        """
        if not hasattr(backend_class, 'get_backend_name'):
            raise ValueError("Backend class must implement 'get_backend_name' class method")
            
        backend_name = backend_class.get_backend_name()
        cls._backends[backend_name] = backend_class
    
    @classmethod
    def get_available_backends(cls) -> List[str]:
        """Get a list of available backend names.
        
        Returns:
            List of backend names that are available (dependencies installed)
        """
        available = []
        for name, backend_class in cls._backends.items():
            if backend_class.is_available():
                available.append(name)
        return available
    
    @classmethod
    def create_backend(
        cls, 
        backend_name: str, 
        config: Optional[Dict[str, Any]] = None
    ) -> 'QuantumBackend':
        """Create a new quantum backend instance.
        
        Args:
            backend_name: Name of the backend to create
            config: Configuration dictionary for the backend
            
        Returns:
            An instance of the requested quantum backend
            
        Raises:
            ValueError: If the backend is not found or not available
        """
        if config is None:
            config = {}
            
        backend_class = cls._backends.get(backend_name.lower())
        
        if not backend_class:
            raise ValueError(f"Unknown quantum backend: {backend_name}")
            
        if not backend_class.is_available():
            raise ValueError(
                f"Backend '{backend_name}' is not available. "
                "Make sure all required dependencies are installed."
            )
            
        return backend_class(config)
    
    @classmethod
    def get_backend_info(cls, backend_name: str) -> Dict[str, Any]:
        """Get information about a specific backend.
        
        Args:
            backend_name: Name of the backend
            
        Returns:
            Dictionary with backend information
        """
        backend_class = cls._backends.get(backend_name.lower())
        if not backend_class:
            raise ValueError(f"Unknown quantum backend: {backend_name}")
            
        info = {
            'name': backend_name,
            'available': backend_class.is_available(),
            'description': backend_class.__doc__.strip() if backend_class.__doc__ else ''
        }
        
        if info['available']:
            # Create a temporary instance to get dynamic info
            try:
                backend = backend_class({})
                if hasattr(backend, 'get_backend_info'):
                    info.update(backend.get_backend_info())
            except Exception as e:
                logging.warning(f"Error getting backend info for {backend_name}: {str(e)}")
        
        return info

# Register all built-in backends
QuantumBackendFactory.register_backend(QiskitBackend)
QuantumBackendFactory.register_backend(CirqBackend)
QuantumBackendFactory.register_backend(PennyLaneBackend)
QuantumBackendFactory.register_backend(BraketBackend)

# For backward compatibility
get_available_backends = QuantumBackendFactory.get_available_backends
create_quantum_backend = QuantumBackendFactory.create_backend
