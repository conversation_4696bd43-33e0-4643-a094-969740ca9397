import React from 'react';
import Layout from '../components/Layout';

function Error({ statusCode }) {
  return (
    <Layout title={`Error ${statusCode || 'Unknown'}`}>
      <div className="flex flex-col items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          <h1 className="text-4xl font-bold text-red-500">
            {statusCode ? `Error ${statusCode}` : 'An error occurred'}
          </h1>
          <p className="mt-2 text-lg text-gray-300">
            {statusCode
              ? `A server-side error occurred (${statusCode})`
              : 'An error occurred on client'}
          </p>
          <div className="mt-6">
            <a
              href="/"
              className="bg-gradient-to-r from-blue-700 to-purple-700 text-white px-6 py-3 rounded-lg font-bold hover:from-blue-800 hover:to-purple-800 inline-block shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200 border border-blue-400"
            >
              Return to Home
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
}

Error.getInitialProps = ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default Error;
