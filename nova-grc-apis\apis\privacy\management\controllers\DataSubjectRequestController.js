/**
 * Data Subject Request Controller
 * 
 * Handles operations related to data subject requests (DSRs).
 */

const DataSubjectRequest = require('../models/DataSubjectRequest');
const { validationResult } = require('express-validator');

/**
 * Get all data subject requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllDataSubjectRequests = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build query based on filters
    const query = {};
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.requestType) {
      query.requestType = req.query.requestType;
    }
    
    if (req.query.overdue === 'true') {
      const today = new Date();
      query.dueDate = { $lt: today };
      query.status = { $nin: ['Completed', 'Denied', 'Withdrawn'] };
    }
    
    // Count total documents for pagination
    const total = await DataSubjectRequest.countDocuments(query);
    
    // Get data subject requests with pagination
    const dataSubjectRequests = await DataSubjectRequest.find(query)
      .skip(skip)
      .limit(limit)
      .sort({ requestDate: -1 });
    
    res.status(200).json({
      success: true,
      count: dataSubjectRequests.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: dataSubjectRequests
    });
  } catch (error) {
    console.error('Error in getAllDataSubjectRequests:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Get a single data subject request by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getDataSubjectRequest = async (req, res) => {
  try {
    const dataSubjectRequest = await DataSubjectRequest.findById(req.params.id)
      .populate('processingActivities');
    
    if (!dataSubjectRequest) {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: dataSubjectRequest
    });
  } catch (error) {
    console.error('Error in getDataSubjectRequest:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Create a new data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createDataSubjectRequest = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    // Generate a unique reference number if not provided
    if (!req.body.reference) {
      const count = await DataSubjectRequest.countDocuments();
      req.body.reference = `DSR-${new Date().getFullYear()}-${(count + 1).toString().padStart(4, '0')}`;
    }
    
    // Create new data subject request
    const dataSubjectRequest = await DataSubjectRequest.create(req.body);
    
    // Add initial timeline entry
    dataSubjectRequest.timeline.push({
      date: new Date(),
      action: 'Request Created',
      user: req.user ? req.user._id : null,
      notes: 'Data subject request created'
    });
    
    await dataSubjectRequest.save();
    
    res.status(201).json({
      success: true,
      data: dataSubjectRequest
    });
  } catch (error) {
    console.error('Error in createDataSubjectRequest:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update a data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateDataSubjectRequest = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    let dataSubjectRequest = await DataSubjectRequest.findById(req.params.id);
    
    if (!dataSubjectRequest) {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    // Add timeline entry if status is changing
    if (req.body.status && req.body.status !== dataSubjectRequest.status) {
      req.body.timeline = [
        ...dataSubjectRequest.timeline,
        {
          date: new Date(),
          action: `Status Changed to ${req.body.status}`,
          user: req.user ? req.user._id : null,
          notes: req.body.statusNotes || `Status changed from ${dataSubjectRequest.status} to ${req.body.status}`
        }
      ];
    }
    
    // Update data subject request
    dataSubjectRequest = await DataSubjectRequest.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );
    
    res.status(200).json({
      success: true,
      data: dataSubjectRequest
    });
  } catch (error) {
    console.error('Error in updateDataSubjectRequest:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Delete a data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteDataSubjectRequest = async (req, res) => {
  try {
    const dataSubjectRequest = await DataSubjectRequest.findById(req.params.id);
    
    if (!dataSubjectRequest) {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    await dataSubjectRequest.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error in deleteDataSubjectRequest:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Add a timeline entry to a data subject request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.addTimelineEntry = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const dataSubjectRequest = await DataSubjectRequest.findById(req.params.id);
    
    if (!dataSubjectRequest) {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    // Add timeline entry
    dataSubjectRequest.timeline.push({
      date: new Date(),
      action: req.body.action,
      user: req.user ? req.user._id : null,
      notes: req.body.notes
    });
    
    await dataSubjectRequest.save();
    
    res.status(200).json({
      success: true,
      data: dataSubjectRequest
    });
  } catch (error) {
    console.error('Error in addTimelineEntry:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Check if a data subject request is overdue
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.checkOverdue = async (req, res) => {
  try {
    const dataSubjectRequest = await DataSubjectRequest.findById(req.params.id);
    
    if (!dataSubjectRequest) {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    const isOverdue = dataSubjectRequest.isOverdue();
    const daysRemaining = dataSubjectRequest.daysRemaining();
    
    res.status(200).json({
      success: true,
      data: {
        isOverdue,
        daysRemaining,
        dueDate: dataSubjectRequest.extensionApplied.applied ? 
          dataSubjectRequest.extensionApplied.newDueDate : 
          dataSubjectRequest.dueDate
      }
    });
  } catch (error) {
    console.error('Error in checkOverdue:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Data subject request not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
