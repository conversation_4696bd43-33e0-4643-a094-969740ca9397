/**
 * NovaVision - Form Builder Service
 * 
 * This service builds dynamic UI forms based on API schema specifications.
 */

import { Logger } from '../utils/Logger';
import { NovaVisionOptions } from '../NovaVision';

// Create logger
const logger = new Logger('form-builder');

/**
 * Form Builder class
 */
export class FormBuilder {
  private options: NovaVisionOptions;
  
  constructor(options: NovaVisionOptions = {}) {
    this.options = {
      theme: options.theme || 'default',
      responsive: options.responsive !== false,
      accessibilityLevel: options.accessibilityLevel || 'AA',
      ...options
    };
    
    logger.debug('Form Builder initialized', {
      theme: this.options.theme,
      responsive: this.options.responsive
    });
  }
  
  /**
   * Build form schema from API response
   * 
   * @param apiResponse - API response containing form schema
   * @returns Processed form schema ready for rendering
   */
  public buildFormSchema(apiResponse: any): any {
    logger.debug('Building form schema from API response');
    
    // Extract schema from API response
    const schema = apiResponse.ui_schema || apiResponse.schema || apiResponse;
    
    if (!schema) {
      throw new Error('API response does not contain a UI schema');
    }
    
    // Process schema
    const processedSchema = this._processFormSchema(schema);
    
    return processedSchema;
  }
  
  /**
   * Process form schema
   * 
   * @param schema - Form schema
   * @returns Processed form schema
   * @private
   */
  private _processFormSchema(schema: any): any {
    logger.debug('Processing form schema', { formId: schema.id });
    
    // Create processed schema
    const processedSchema = {
      ...schema,
      metadata: {
        ...(schema.metadata || {}),
        processedAt: new Date().toISOString(),
        renderer: 'NovaVision',
        rendererVersion: '1.0',
        theme: this.options.theme
      }
    };
    
    // Process sections
    if (processedSchema.sections && Array.isArray(processedSchema.sections)) {
      processedSchema.sections = processedSchema.sections.map((section: any) => 
        this._processFormSection(section)
      );
    }
    
    // Process fields if they exist at the root level
    if (processedSchema.fields && Array.isArray(processedSchema.fields)) {
      processedSchema.fields = processedSchema.fields.map((field: any) => 
        this._processFormField(field)
      );
    }
    
    // Process actions
    if (processedSchema.actions && Array.isArray(processedSchema.actions)) {
      processedSchema.actions = processedSchema.actions.map((action: any) => 
        this._processFormAction(action)
      );
    }
    
    // Add default actions if none exist
    if (!processedSchema.actions || processedSchema.actions.length === 0) {
      processedSchema.actions = [
        {
          id: 'submit',
          type: 'submit',
          label: 'Submit',
          primary: true
        },
        {
          id: 'cancel',
          type: 'button',
          label: 'Cancel',
          primary: false
        }
      ];
    }
    
    return processedSchema;
  }
  
  /**
   * Process form section
   * 
   * @param section - Form section
   * @returns Processed form section
   * @private
   */
  private _processFormSection(section: any): any {
    logger.debug('Processing form section', { sectionId: section.id });
    
    // Create processed section
    const processedSection = {
      ...section
    };
    
    // Process fields
    if (processedSection.fields && Array.isArray(processedSection.fields)) {
      processedSection.fields = processedSection.fields.map((field: any) => 
        this._processFormField(field)
      );
    }
    
    return processedSection;
  }
  
  /**
   * Process form field
   * 
   * @param field - Form field
   * @returns Processed form field
   * @private
   */
  private _processFormField(field: any): any {
    logger.debug('Processing form field', { 
      fieldId: field.id,
      fieldType: field.type
    });
    
    // Create processed field
    const processedField = {
      ...field
    };
    
    // Apply theme-specific styling
    processedField.styling = this._applyThemeStyling(field.type);
    
    // Apply accessibility enhancements
    processedField.accessibility = this._applyAccessibilityEnhancements(field);
    
    // Process nested fields for group type
    if (field.type === 'group' && field.fields && Array.isArray(field.fields)) {
      processedField.fields = field.fields.map((nestedField: any) => 
        this._processFormField(nestedField)
      );
    }
    
    // Process options for select, radio, checkbox types
    if (['select', 'radio', 'checkbox'].includes(field.type) && field.options) {
      processedField.options = field.options.map((option: any) => ({
        ...option,
        styling: this._applyThemeStyling('option')
      }));
    }
    
    return processedField;
  }
  
  /**
   * Process form action
   * 
   * @param action - Form action
   * @returns Processed form action
   * @private
   */
  private _processFormAction(action: any): any {
    logger.debug('Processing form action', { 
      actionId: action.id,
      actionType: action.type
    });
    
    // Create processed action
    const processedAction = {
      ...action
    };
    
    // Apply theme-specific styling
    processedAction.styling = this._applyThemeStyling(action.type === 'submit' ? 'submit-button' : 'button');
    
    // Apply accessibility enhancements
    processedAction.accessibility = this._applyAccessibilityEnhancements(action);
    
    return processedAction;
  }
  
  /**
   * Apply theme-specific styling
   * 
   * @param elementType - Element type
   * @returns Theme-specific styling
   * @private
   */
  private _applyThemeStyling(elementType: string): any {
    // Theme-specific styling would be more comprehensive in a real implementation
    const themeStyles: Record<string, Record<string, any>> = {
      default: {
        'text': { className: 'nv-input nv-input-text' },
        'password': { className: 'nv-input nv-input-password' },
        'email': { className: 'nv-input nv-input-email' },
        'number': { className: 'nv-input nv-input-number' },
        'select': { className: 'nv-select' },
        'checkbox': { className: 'nv-checkbox' },
        'radio': { className: 'nv-radio' },
        'textarea': { className: 'nv-textarea' },
        'date': { className: 'nv-input nv-input-date' },
        'file': { className: 'nv-input nv-input-file' },
        'group': { className: 'nv-field-group' },
        'option': { className: 'nv-option' },
        'submit-button': { className: 'nv-btn nv-btn-primary' },
        'button': { className: 'nv-btn nv-btn-secondary' }
      },
      dark: {
        'text': { className: 'nv-input nv-input-text nv-dark' },
        'password': { className: 'nv-input nv-input-password nv-dark' },
        'email': { className: 'nv-input nv-input-email nv-dark' },
        'number': { className: 'nv-input nv-input-number nv-dark' },
        'select': { className: 'nv-select nv-dark' },
        'checkbox': { className: 'nv-checkbox nv-dark' },
        'radio': { className: 'nv-radio nv-dark' },
        'textarea': { className: 'nv-textarea nv-dark' },
        'date': { className: 'nv-input nv-input-date nv-dark' },
        'file': { className: 'nv-input nv-input-file nv-dark' },
        'group': { className: 'nv-field-group nv-dark' },
        'option': { className: 'nv-option nv-dark' },
        'submit-button': { className: 'nv-btn nv-btn-primary nv-dark' },
        'button': { className: 'nv-btn nv-btn-secondary nv-dark' }
      }
    };
    
    return themeStyles[this.options.theme as string]?.[elementType] || themeStyles.default[elementType] || {};
  }
  
  /**
   * Apply accessibility enhancements
   * 
   * @param element - Form element
   * @returns Accessibility enhancements
   * @private
   */
  private _applyAccessibilityEnhancements(element: any): any {
    const accessibility: any = {
      ariaAttributes: {}
    };
    
    // Add appropriate ARIA attributes based on element type
    if (element.type === 'text' || element.type === 'password' || element.type === 'email') {
      accessibility.ariaAttributes['aria-required'] = element.required ? 'true' : 'false';
      
      if (element.description) {
        accessibility.ariaAttributes['aria-describedby'] = `${element.id}-description`;
      }
    }
    
    if (element.type === 'group') {
      accessibility.ariaAttributes['role'] = 'group';
      accessibility.ariaAttributes['aria-labelledby'] = `${element.id}-label`;
    }
    
    // Add more accessibility enhancements based on the accessibility level
    if (this.options.accessibilityLevel === 'AAA') {
      // Add AAA level enhancements
      accessibility.enhancedKeyboardNavigation = true;
      accessibility.highContrastMode = true;
    }
    
    return accessibility;
  }
}
