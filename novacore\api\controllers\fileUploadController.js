/**
 * File Upload Controller
 * 
 * This controller handles file uploads for NovaAssistAI.
 * It processes uploaded files, stores them securely, and returns URLs.
 */

const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const logger = require('../utils/logger');

// Configure storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // Create user-specific directory
    const userDir = path.join(uploadDir, req.user.id.toString());
    if (!fs.existsSync(userDir)) {
      fs.mkdirSync(userDir, { recursive: true });
    }
    
    cb(null, userDir);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueId = uuidv4();
    const fileExtension = path.extname(file.originalname);
    const safeFilename = file.originalname
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .substring(0, 50);
    
    cb(null, `${uniqueId}-${safeFilename}${fileExtension}`);
  }
});

// Configure upload limits
const limits = {
  fileSize: 10 * 1024 * 1024, // 10MB
  files: 5
};

// Configure file filter
const fileFilter = (req, file, cb) => {
  // Allow common file types
  const allowedMimeTypes = [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    
    // Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
    'text/plain',
    
    // Archives
    'application/zip',
    'application/x-rar-compressed',
    
    // JSON
    'application/json'
  ];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} is not allowed`), false);
  }
};

// Create multer upload instance
const upload = multer({ 
  storage, 
  limits,
  fileFilter
});

/**
 * Upload a file
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.uploadFile = (req, res) => {
  // Use multer upload middleware
  upload.single('file')(req, res, (err) => {
    if (err) {
      logger.error('File upload error:', err);
      
      if (err instanceof multer.MulterError) {
        // Multer error (file size, file count, etc.)
        return res.status(400).json({ 
          error: `Upload error: ${err.message}` 
        });
      }
      
      // Other errors
      return res.status(500).json({ 
        error: err.message || 'File upload failed' 
      });
    }
    
    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    // Generate file URL
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const relativePath = path.relative(
      path.join(__dirname, '..'),
      req.file.path
    ).replace(/\\/g, '/');
    
    const fileUrl = `${baseUrl}/${relativePath}`;
    
    // Return file information
    return res.status(200).json({
      url: fileUrl,
      name: req.file.originalname,
      size: req.file.size,
      mimetype: req.file.mimetype
    });
  });
};

/**
 * Upload multiple files
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.uploadFiles = (req, res) => {
  // Use multer upload middleware
  upload.array('files', limits.files)(req, res, (err) => {
    if (err) {
      logger.error('Files upload error:', err);
      
      if (err instanceof multer.MulterError) {
        // Multer error (file size, file count, etc.)
        return res.status(400).json({ 
          error: `Upload error: ${err.message}` 
        });
      }
      
      // Other errors
      return res.status(500).json({ 
        error: err.message || 'File upload failed' 
      });
    }
    
    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }
    
    // Generate file URLs
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const fileInfos = req.files.map(file => {
      const relativePath = path.relative(
        path.join(__dirname, '..'),
        file.path
      ).replace(/\\/g, '/');
      
      const fileUrl = `${baseUrl}/${relativePath}`;
      
      return {
        url: fileUrl,
        name: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      };
    });
    
    // Return file information
    return res.status(200).json({
      files: fileInfos
    });
  });
};

module.exports = exports;
