/**
 * Data Breach Model
 * 
 * Represents a data breach incident involving personal data,
 * including details about the breach, affected data, risk assessment,
 * notifications, and remediation actions.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const dataBreachSchema = new Schema({
  reference: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['Draft', 'Under Investigation', 'Contained', 'Remediated', 'Closed', 'Reopened'],
    default: 'Draft'
  },
  discoveryDate: {
    type: Date,
    required: true
  },
  discoveryMethod: {
    type: String,
    enum: ['Internal Monitoring', 'Employee Report', 'Customer Report', 'Third Party Notification', 'Security Tool Alert', 'Audit', 'Other'],
    required: true
  },
  discoveredBy: {
    name: String,
    role: String,
    contact: String,
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  breachDetails: {
    startDate: Date,
    endDate: Date,
    ongoing: {
      type: Boolean,
      default: false
    },
    description: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['Unauthorized Access', 'Unauthorized Disclosure', 'Data Loss', 'Data Alteration', 'Data Destruction', 'Ransomware', 'Other'],
      required: true
    },
    cause: {
      type: String,
      enum: ['Human Error', 'System Error', 'Process Failure', 'Malicious Attack', 'Third Party Breach', 'Unknown', 'Other'],
      required: true
    },
    location: String,
    systemsAffected: [String],
    containmentDate: Date,
    containmentActions: [String]
  },
  affectedData: {
    dataCategories: [{
      type: String,
      enum: [
        'Personal', 
        'Special', 
        'Criminal', 
        'Children', 
        'Financial', 
        'Location', 
        'Biometric', 
        'Health', 
        'Genetic',
        'Other'
      ]
    }],
    dataSubjectCategories: [{
      type: String,
      enum: [
        'Customers', 
        'Employees', 
        'Vendors', 
        'Partners', 
        'Website Visitors', 
        'App Users', 
        'Children', 
        'Patients', 
        'Other'
      ]
    }],
    approximateRecords: Number,
    dataDescription: String,
    processingActivities: [{
      type: Schema.Types.ObjectId,
      ref: 'ProcessingActivity'
    }]
  },
  riskAssessment: {
    performedBy: {
      name: String,
      role: String,
      userId: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      }
    },
    performedDate: Date,
    confidentialityImpact: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Unknown'],
      default: 'Unknown'
    },
    integrityImpact: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Unknown'],
      default: 'Unknown'
    },
    availabilityImpact: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Unknown'],
      default: 'Unknown'
    },
    overallRisk: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Critical', 'Unknown'],
      default: 'Unknown'
    },
    likelyConsequences: [String],
    potentialHarm: String
  },
  notifications: {
    supervisoryAuthority: {
      required: {
        type: Boolean,
        default: false
      },
      authority: String,
      notificationDate: Date,
      notificationMethod: String,
      referenceNumber: String,
      notifiedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      responseDate: Date,
      responseDetails: String,
      documents: [{
        name: String,
        url: String,
        uploadDate: Date
      }]
    },
    dataSubjects: {
      required: {
        type: Boolean,
        default: false
      },
      notificationDate: Date,
      notificationMethod: String,
      messageTemplate: String,
      numberOfNotified: Number,
      notifiedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      documents: [{
        name: String,
        url: String,
        uploadDate: Date
      }]
    },
    lawEnforcement: {
      notified: {
        type: Boolean,
        default: false
      },
      agency: String,
      notificationDate: Date,
      referenceNumber: String,
      contactPerson: String,
      details: String
    },
    otherNotifications: [{
      entity: String,
      reason: String,
      notificationDate: Date,
      notifiedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      details: String
    }]
  },
  investigation: {
    team: [{
      name: String,
      role: String,
      userId: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      }
    }],
    findings: String,
    rootCause: String,
    evidenceCollected: [String],
    forensicAnalysis: {
      performed: {
        type: Boolean,
        default: false
      },
      performedBy: String,
      date: Date,
      report: String
    },
    timeline: [{
      date: Date,
      event: String,
      details: String
    }]
  },
  remediation: {
    actions: [{
      description: String,
      type: {
        type: String,
        enum: ['Technical', 'Organizational', 'Legal', 'Communication', 'Training', 'Other']
      },
      status: {
        type: String,
        enum: ['Planned', 'In Progress', 'Completed', 'Deferred', 'Cancelled'],
        default: 'Planned'
      },
      assignedTo: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      dueDate: Date,
      completionDate: Date,
      notes: String
    }],
    lessonsLearned: [String],
    preventiveMeasures: [String]
  },
  documents: [{
    name: String,
    description: String,
    category: {
      type: String,
      enum: ['Investigation', 'Notification', 'Remediation', 'Communication', 'Legal', 'Other']
    },
    url: String,
    uploadDate: {
      type: Date,
      default: Date.now
    },
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  communications: [{
    date: Date,
    type: {
      type: String,
      enum: ['Internal', 'External', 'Regulatory', 'Media', 'Other']
    },
    sender: String,
    recipient: String,
    subject: String,
    content: String,
    attachments: [{
      name: String,
      url: String
    }]
  }],
  costs: {
    investigation: Number,
    notification: Number,
    remediation: Number,
    legal: Number,
    regulatory: Number,
    other: Number,
    currency: {
      type: String,
      default: 'USD'
    },
    notes: String
  },
  history: [{
    date: {
      type: Date,
      default: Date.now
    },
    action: String,
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    details: String
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Add text index for search functionality
dataBreachSchema.index({
  reference: 'text',
  'breachDetails.description': 'text',
  'affectedData.dataDescription': 'text'
});

// Method to check if supervisory authority notification is required
dataBreachSchema.methods.isAuthorityNotificationRequired = function() {
  // Check if high-risk data categories are affected
  const highRiskCategories = ['Special', 'Criminal', 'Children', 'Health', 'Genetic'];
  const hasHighRiskData = this.affectedData.dataCategories.some(category => 
    highRiskCategories.includes(category)
  );
  
  // Check if risk is high or critical
  const highRisk = this.riskAssessment.overallRisk === 'High' || this.riskAssessment.overallRisk === 'Critical';
  
  // Check if large number of records are affected (e.g., more than 500)
  const largeScale = this.affectedData.approximateRecords > 500;
  
  return hasHighRiskData || highRisk || largeScale;
};

// Method to check if data subject notification is required
dataBreachSchema.methods.isDataSubjectNotificationRequired = function() {
  // Data subject notification is typically required when there is a high risk to rights and freedoms
  return this.riskAssessment.overallRisk === 'High' || this.riskAssessment.overallRisk === 'Critical';
};

// Method to calculate notification deadline (72 hours from discovery for GDPR)
dataBreachSchema.methods.calculateNotificationDeadline = function() {
  if (!this.discoveryDate) return null;
  
  const deadline = new Date(this.discoveryDate);
  deadline.setHours(deadline.getHours() + 72);
  
  return deadline;
};

// Method to calculate total cost
dataBreachSchema.methods.calculateTotalCost = function() {
  if (!this.costs) return 0;
  
  return (
    (this.costs.investigation || 0) +
    (this.costs.notification || 0) +
    (this.costs.remediation || 0) +
    (this.costs.legal || 0) +
    (this.costs.regulatory || 0) +
    (this.costs.other || 0)
  );
};

// Method to add a history entry
dataBreachSchema.methods.addHistoryEntry = function(action, user, details) {
  this.history.push({
    date: new Date(),
    action,
    user,
    details
  });
};

const DataBreach = mongoose.model('DataBreach', dataBreachSchema);

module.exports = DataBreach;
