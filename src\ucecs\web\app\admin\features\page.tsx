'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useFeatureFlags } from '@/contexts/FeatureFlagContext';
import { FeatureFlag } from '@/utils/features/featureFlags';
import { FiSave, FiRefreshCw, FiInfo } from 'react-icons/fi';

// Group feature flags by category
const featureFlagGroups = {
  'Core Features': [
    FeatureFlag.ADVANCED_DASHBOARD,
    FeatureFlag.EVIDENCE_VERSIONING,
    FeatureFlag.REQUIREMENT_MAPPING,
    FeatureFlag.ADVANCED_SEARCH,
    FeatureFlag.REPORT_SCHEDULING,
  ],
  'Integration Features': [
    FeatureFlag.VENDOR_RISK_INTEGRATION,
    FeatureFlag.REGULATORY_CHANGE_ALERTS,
    FeatureFlag.WORKFLOW_AUTOMATION,
    FeatureFlag.AI_INSIGHTS,
  ],
  'Advanced Features': [
    FeatureFlag.EVIDENCE_AUTO_COLLECTION,
    FeatureFlag.EVIDENCE_AUTO_VALIDATION,
    FeatureFlag.REAL_TIME_COMPLIANCE,
    FeatureFlag.PREDICTIVE_COMPLIANCE,
    FeatureFlag.BLOCKCHAIN_VERIFICATION,
  ],
  'UI/UX Features': [
    FeatureFlag.DARK_MODE,
    FeatureFlag.CUSTOMIZABLE_DASHBOARD,
    FeatureFlag.MOBILE_OPTIMIZED,
    FeatureFlag.ACCESSIBILITY_FEATURES,
  ],
  'Experimental Features': [
    FeatureFlag.EXPERIMENTAL_AI_ASSISTANT,
    FeatureFlag.EXPERIMENTAL_GRAPH_VIEW,
  ],
};

// Feature flag descriptions
const featureFlagDescriptions: Record<FeatureFlag, string> = {
  [FeatureFlag.ADVANCED_DASHBOARD]: 'Enhanced dashboard with advanced analytics and visualizations',
  [FeatureFlag.EVIDENCE_VERSIONING]: 'Track and manage multiple versions of evidence',
  [FeatureFlag.REQUIREMENT_MAPPING]: 'Map requirements to frameworks and controls',
  [FeatureFlag.ADVANCED_SEARCH]: 'Advanced search capabilities with filters and saved searches',
  [FeatureFlag.REPORT_SCHEDULING]: 'Schedule automated report generation',
  
  [FeatureFlag.VENDOR_RISK_INTEGRATION]: 'Integration with BridgeCore for vendor risk management',
  [FeatureFlag.REGULATORY_CHANGE_ALERTS]: 'Integration with NovaPulse for regulatory change alerts',
  [FeatureFlag.WORKFLOW_AUTOMATION]: 'Integration with NovaFlow for workflow automation',
  [FeatureFlag.AI_INSIGHTS]: 'Integration with NovaEdge for AI-powered insights',
  
  [FeatureFlag.EVIDENCE_AUTO_COLLECTION]: 'Automated evidence collection from integrated systems',
  [FeatureFlag.EVIDENCE_AUTO_VALIDATION]: 'Automated validation of evidence against requirements',
  [FeatureFlag.REAL_TIME_COMPLIANCE]: 'Real-time compliance monitoring and alerts',
  [FeatureFlag.PREDICTIVE_COMPLIANCE]: 'Predictive compliance analysis using AI',
  [FeatureFlag.BLOCKCHAIN_VERIFICATION]: 'Blockchain-based evidence verification',
  
  [FeatureFlag.DARK_MODE]: 'Dark mode UI theme',
  [FeatureFlag.CUSTOMIZABLE_DASHBOARD]: 'User-customizable dashboard layout',
  [FeatureFlag.MOBILE_OPTIMIZED]: 'Mobile-optimized UI for tablets and smartphones',
  [FeatureFlag.ACCESSIBILITY_FEATURES]: 'Enhanced accessibility features',
  
  [FeatureFlag.EXPERIMENTAL_AI_ASSISTANT]: 'AI assistant for compliance tasks',
  [FeatureFlag.EXPERIMENTAL_GRAPH_VIEW]: 'Graph visualization of compliance relationships',
};

export default function FeatureFlagsPage() {
  const { getAllFeatures, toggleFeature } = useFeatureFlags();
  const [features, setFeatures] = useState<Record<FeatureFlag, boolean>>({} as Record<FeatureFlag, boolean>);
  const [loading, setLoading] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Load feature flags
  useEffect(() => {
    setFeatures(getAllFeatures());
    setLoading(false);
  }, [getAllFeatures]);

  // Handle toggle
  const handleToggle = (feature: FeatureFlag) => {
    toggleFeature(feature);
    setFeatures(getAllFeatures());
    setSaveSuccess(true);
    
    // Reset success message after 3 seconds
    setTimeout(() => {
      setSaveSuccess(false);
    }, 3000);
  };

  // Format feature flag name for display
  const formatFeatureName = (feature: string) => {
    return feature
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <MainLayout>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Feature Flags</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage system features and capabilities</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-2">
          {saveSuccess && (
            <div className="bg-green-100 text-green-800 px-4 py-2 rounded-md flex items-center">
              <FiInfo className="mr-2" />
              Changes saved successfully
            </div>
          )}
          <button
            className="btn btn-outline flex items-center"
            onClick={() => {
              setFeatures(getAllFeatures());
            }}
          >
            <FiRefreshCw className="mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {loading ? (
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Loading feature flags...</p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(featureFlagGroups).map(([groupName, groupFeatures]) => (
            <div key={groupName} className="card">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">{groupName}</h2>
              <div className="space-y-4">
                {groupFeatures.map((feature) => (
                  <div key={feature} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">{formatFeatureName(feature)}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{featureFlagDescriptions[feature]}</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={features[feature as FeatureFlag]}
                        onChange={() => handleToggle(feature as FeatureFlag)}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </MainLayout>
  );
}
