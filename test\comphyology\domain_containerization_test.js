/**
 * Domain Containerization Test
 * 
 * This script tests the domain containerization and finite possibility spaces
 * in the Comphyological Cosmos, verifying that each domain is treated as a
 * distinct "containerized universe" with its own physics and boundaries.
 */

const { ComphyologicalCosmos } = require('../../src/comphyology/ComphyologicalCosmos');
const { v4: uuidv4 } = require('uuid');

/**
 * Test domain containerization
 */
function testDomainContainerization() {
  console.log('=== Testing Domain Containerization ===');
  
  // Create a ComphyologicalCosmos instance
  const cosmos = new ComphyologicalCosmos({
    enableLogging: true,
    autoUpdate: false // Disable auto-update for testing
  });
  
  console.log('\n1. Testing Domain Boundaries');
  testDomainBoundaries(cosmos);
  
  console.log('\n2. Testing Domain Physics');
  testDomainPhysics(cosmos);
  
  console.log('\n3. Testing Cross-Domain Transfer');
  testCrossDomainTransfer(cosmos);
  
  console.log('\n4. Testing Divine Firewall Integration');
  testDivineFirewallIntegration(cosmos);
  
  console.log('\n=== Domain Containerization Test Complete ===');
}

/**
 * Test domain boundaries
 * @param {ComphyologicalCosmos} cosmos - The cosmos instance
 */
function testDomainBoundaries(cosmos) {
  console.log('Testing that each domain enforces its own boundaries');
  
  // Test case 1: Valid entity in biological domain
  const validBioEntity = {
    cellularEnergy: 75,
    cellularIntegrity: 0.9,
    geneticStability: 0.85,
    physiologicalFunction: 0.8
  };
  
  try {
    const addedBioEntity = cosmos.addEntity('biological', 'bio-1', validBioEntity);
    console.log('Valid biological entity added:', addedBioEntity);
    console.assert(addedBioEntity.cellularEnergy === 75, 'Cellular energy should be 75');
  } catch (error) {
    console.error('Error adding valid biological entity:', error.message);
  }
  
  // Test case 2: Valid entity in financial domain
  const validFinEntity = {
    valueExchange: 1000000,
    valueStability: 0.7,
    riskExposure: 0.3,
    resourceAllocation: 0.8
  };
  
  try {
    const addedFinEntity = cosmos.addEntity('financial', 'fin-1', validFinEntity);
    console.log('Valid financial entity added:', addedFinEntity);
    console.assert(addedFinEntity.valueExchange === 1000000, 'Value exchange should be 1000000');
  } catch (error) {
    console.error('Error adding valid financial entity:', error.message);
  }
  
  // Test case 3: Invalid entity in cyber domain (exceeds boundaries)
  const invalidCyberEntity = {
    informationIntegrity: 1.5, // Exceeds maximum of 1
    securityStrength: 0.8,
    complianceLevel: 0.9
  };
  
  try {
    const addedCyberEntity = cosmos.addEntity('cyber', 'cyber-1', invalidCyberEntity);
    console.log('Invalid cyber entity result:', addedCyberEntity);
  } catch (error) {
    console.log('Expected error for invalid cyber entity:', error.message);
  }
  
  console.log('Domain boundaries test complete');
}

/**
 * Test domain physics
 * @param {ComphyologicalCosmos} cosmos - The cosmos instance
 */
function testDomainPhysics(cosmos) {
  console.log('Testing that each domain follows its own physics');
  
  // Get initial domain states
  const initialBioState = cosmos.getDomain('biological').getState();
  const initialFinState = cosmos.getDomain('financial').getState();
  const initialCyberState = cosmos.getDomain('cyber').getState();
  
  console.log('Initial domain states:');
  console.log('Biological:', initialBioState);
  console.log('Financial:', initialFinState);
  console.log('Cyber:', initialCyberState);
  
  // Update cosmos to apply physics
  console.log('\nUpdating cosmos to apply physics...');
  cosmos.update();
  
  // Get updated domain states
  const updatedBioState = cosmos.getDomain('biological').getState();
  const updatedFinState = cosmos.getDomain('financial').getState();
  const updatedCyberState = cosmos.getDomain('cyber').getState();
  
  console.log('Updated domain states:');
  console.log('Biological:', updatedBioState);
  console.log('Financial:', updatedFinState);
  console.log('Cyber:', updatedCyberState);
  
  // Verify that entropy increased according to domain physics
  console.log('\nVerifying entropy changes:');
  console.log('Biological entropy change:', updatedBioState.entropy - initialBioState.entropy);
  console.log('Financial entropy change:', updatedFinState.entropy - initialFinState.entropy);
  console.log('Cyber entropy change:', updatedCyberState.entropy - initialCyberState.entropy);
  
  // Update multiple times to see cumulative effects
  console.log('\nUpdating cosmos multiple times...');
  for (let i = 0; i < 10; i++) {
    cosmos.update();
  }
  
  // Get final domain states
  const finalBioState = cosmos.getDomain('biological').getState();
  const finalFinState = cosmos.getDomain('financial').getState();
  const finalCyberState = cosmos.getDomain('cyber').getState();
  
  console.log('Final domain states after multiple updates:');
  console.log('Biological:', finalBioState);
  console.log('Financial:', finalFinState);
  console.log('Cyber:', finalCyberState);
  
  console.log('Domain physics test complete');
}

/**
 * Test cross-domain transfer
 * @param {ComphyologicalCosmos} cosmos - The cosmos instance
 */
function testCrossDomainTransfer(cosmos) {
  console.log('Testing cross-domain entity transfer');
  
  // Create entities in each domain
  const bioEntityId = 'bio-transfer-' + uuidv4().substring(0, 8);
  const finEntityId = 'fin-transfer-' + uuidv4().substring(0, 8);
  const cyberEntityId = 'cyber-transfer-' + uuidv4().substring(0, 8);
  
  const bioEntity = {
    cellularEnergy: 80,
    cellularIntegrity: 0.9,
    geneticStability: 0.85,
    physiologicalFunction: 0.8,
    systemCoherence: 0.75
  };
  
  const finEntity = {
    valueExchange: 500000,
    valueStability: 0.8,
    riskExposure: 0.2,
    resourceAllocation: 0.9,
    systemLiquidity: 0.85
  };
  
  const cyberEntity = {
    informationIntegrity: 0.95,
    securityStrength: 0.9,
    complianceLevel: 0.85,
    systemResilience: 0.8
  };
  
  // Add entities to domains
  cosmos.addEntity('biological', bioEntityId, bioEntity);
  cosmos.addEntity('financial', finEntityId, finEntity);
  cosmos.addEntity('cyber', cyberEntityId, cyberEntity);
  
  // Test transfer from biological to financial
  console.log('\nTransferring entity from biological to financial domain...');
  try {
    const bioToFinTransfer = cosmos.transferEntity('biological', 'financial', bioEntityId);
    console.log('Transfer result:', bioToFinTransfer);
    
    // Verify that entity exists in target domain
    const transferredEntity = cosmos.getDomain('financial').getEntity(bioToFinTransfer.transfer.targetEntityId);
    console.log('Transferred entity in financial domain:', transferredEntity);
  } catch (error) {
    console.error('Error transferring from biological to financial:', error.message);
  }
  
  // Test transfer from financial to cyber
  console.log('\nTransferring entity from financial to cyber domain...');
  try {
    const finToCyberTransfer = cosmos.transferEntity('financial', 'cyber', finEntityId);
    console.log('Transfer result:', finToCyberTransfer);
    
    // Verify that entity exists in target domain
    const transferredEntity = cosmos.getDomain('cyber').getEntity(finToCyberTransfer.transfer.targetEntityId);
    console.log('Transferred entity in cyber domain:', transferredEntity);
  } catch (error) {
    console.error('Error transferring from financial to cyber:', error.message);
  }
  
  // Test transfer from cyber to biological
  console.log('\nTransferring entity from cyber to biological domain...');
  try {
    const cyberToBioTransfer = cosmos.transferEntity('cyber', 'biological', cyberEntityId);
    console.log('Transfer result:', cyberToBioTransfer);
    
    // Verify that entity exists in target domain
    const transferredEntity = cosmos.getDomain('biological').getEntity(cyberToBioTransfer.transfer.targetEntityId);
    console.log('Transferred entity in biological domain:', transferredEntity);
  } catch (error) {
    console.error('Error transferring from cyber to biological:', error.message);
  }
  
  console.log('Cross-domain transfer test complete');
}

/**
 * Test Divine Firewall integration
 * @param {ComphyologicalCosmos} cosmos - The cosmos instance
 */
function testDivineFirewallIntegration(cosmos) {
  console.log('Testing Divine Firewall integration with domain containerization');
  
  // Test case 1: Entity with infinite value
  const infiniteEntity = {
    cellularEnergy: Infinity,
    cellularIntegrity: 0.9,
    geneticStability: 0.85
  };
  
  console.log('\nTesting entity with infinite value...');
  try {
    const result = cosmos.addEntity('biological', 'infinite-entity', infiniteEntity);
    console.log('Result:', result);
  } catch (error) {
    console.log('Expected error for infinite entity:', error.message);
  }
  
  // Test case 2: Entity with NaN value
  const nanEntity = {
    valueExchange: NaN,
    valueStability: 0.8,
    riskExposure: 0.2
  };
  
  console.log('\nTesting entity with NaN value...');
  try {
    const result = cosmos.addEntity('financial', 'nan-entity', nanEntity);
    console.log('Result:', result);
  } catch (error) {
    console.log('Expected error for NaN entity:', error.message);
  }
  
  // Test case 3: Entity with excessive value
  const excessiveEntity = {
    informationIntegrity: 0.9,
    securityStrength: 0.8,
    systemComplexity: 1000 // Exceeds maximum of 1
  };
  
  console.log('\nTesting entity with excessive value...');
  try {
    const result = cosmos.addEntity('cyber', 'excessive-entity', excessiveEntity);
    console.log('Result:', result);
  } catch (error) {
    console.log('Expected error for excessive entity:', error.message);
  }
  
  console.log('Divine Firewall integration test complete');
}

/**
 * Main function
 */
function main() {
  console.log('=== Domain Containerization Test ===');
  
  // Run domain containerization test
  testDomainContainerization();
}

// Run main function
main();
