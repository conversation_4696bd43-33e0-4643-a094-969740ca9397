/**
 * Metron Sensor - Cognitive Recursion Depth Measurement
 *
 * Implements the refined Metron formula: M_score = 3^(D-1) × log(Ψᶜʰ)
 *
 * This module measures cognitive recursion depth in AI systems, providing
 * precise quantification of reasoning complexity and safety bounds.
 *
 * <AUTHOR> (CTO, NovaFuse)
 * <AUTHOR> Agent (Implementation Partner)
 */

const { performance } = require('perf_hooks');
const EventEmitter = require('events');

class MetronSensor extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: options.enableLogging || false,
      enableMetrics: options.enableMetrics || true,
      maxRecursionDepth: options.maxRecursionDepth || 15, // Safety limit
      coherenceThreshold: options.coherenceThreshold || 0.618, // φ-threshold
      calibrationFactor: options.calibrationFactor || 1.0,
      ...options
    };

    // State tracking with FUP singularity monitoring
    this.state = {
      currentDepth: 0,
      coherenceHistory: [],
      recursionStack: [],
      // FUP COMPLIANCE: Singularity rate monitoring
      lastMeasurementTime: Date.now(),
      lastMetronScore: 0,
      metronGrowthRate: 0, // μ/s
      singularityAlerts: [],
      metrics: {
        totalMeasurements: 0,
        averageDepth: 0,
        maxDepthReached: 0,
        calculationTimeMs: 0,
        maxGrowthRateDetected: 0
      }
    };

    // Mathematical constants with FUP integration
    this.constants = {
      PHI: (1 + Math.sqrt(5)) / 2, // Golden ratio
      PI: Math.PI,
      E: Math.E,
      BASE_3: 3, // Triadic base for logarithms
      // FUP (Finite Universe Principle) Constants
      PLANCK_LENGTH: 1.616e-35, // meters
      PLANCK_TIME: 5.391e-44, // seconds
      PLANCK_ENERGY: 1.956e9, // joules
      SPEED_OF_LIGHT: 2.998e8, // m/s
      GRAVITATIONAL_CONSTANT: 6.674e-11, // m³/kg⋅s²
      REDUCED_PLANCK: 1.055e-34, // J⋅s
      // Cosmic Boundaries
      PSI_MAX_CPH: 1.41e59, // Maximum coherence (Planck-scale bound)
      D_MAX_LAYERS: 127, // Maximum cognitive depth (holographic limit)
      HUBBLE_TIME: 4.35e17, // seconds (age of universe)
      // Human Cognitive Limits
      HUMAN_OPTIMAL_METRON: 42, // μ (holographic limit)
      AI_SINGULARITY_BOUNDARY: 126, // μ (post-singularity boundary)
      // Safety Thresholds
      COHERENCE_WARNING_THRESHOLD: 0.9, // 90% of Ψ_max
      SINGULARITY_PREVENTION_RATE: 1e-44 // cph/s maximum growth rate
    };

    if (this.options.enableLogging) {
      console.log('MetronSensor initialized with options:', this.options);
    }
  }

  /**
   * Analyze cognitive recursion depth in a system
   *
   * @param {Object} system - The system to analyze
   * @param {Object} options - Analysis options
   * @returns {Object} - Metron analysis results
   */
  async analyze(system, options = {}) {
    const startTime = performance.now();

    try {
      // Extract reasoning components from system
      const reasoningComponents = this._extractReasoningComponents(system);

      // Measure system coherence (Ψᶜʰ) first - needed for triad depth calculation
      const coherence = this._measureCoherence(system);

      // Calculate triad recursion depth (D) using coherence
      const triadDepth = this._calculateTriadDepth(reasoningComponents, coherence);

      // Apply refined Metron formula: M_score = 3^(D-1) × log(Ψᶜʰ)
      const metronScore = this._calculateMetronScore(triadDepth, coherence);

      // Validate safety bounds
      const safetyValidation = this._validateSafetyBounds(metronScore, triadDepth);

      // Update metrics
      this._updateMetrics(metronScore, triadDepth, performance.now() - startTime);

      const result = {
        metronScore,
        triadDepth,
        coherence,
        safetyValidation,
        timestamp: new Date().toISOString(),
        processingTimeMs: performance.now() - startTime,
        classification: this._classifyReasoningLevel(metronScore)
      };

      this.emit('analysis-complete', result);
      return result;

    } catch (error) {
      console.error('Error in Metron analysis:', error);
      this.emit('analysis-error', error);
      throw error;
    }
  }

  /**
   * Calculate Metron score using refined formula with FUP numerical stability
   *
   * FIXED: Implements numerical robustness and triad-depth normalization
   *
   * @param {number} triadDepth - D value (triad recursion depth)
   * @param {number} coherence - Ψᶜʰ value (system coherence)
   * @returns {number} - Metron score (Μ)
   * @private
   */
  _calculateMetronScore(triadDepth, coherence) {
    // FUP COMPLIANCE: Base case for consciousness threshold
    if (triadDepth <= 0) {
      return 0.01; // Base case (consciousness threshold)
    }

    // FUP COMPLIANCE: Numerical stability with entropy guard
    const ENTROPY_GUARD = 1e-10; // ε = 1e-10 as specified
    const safeCoherence = Math.max(ENTROPY_GUARD, coherence);

    // FIXED: Enhanced Metron formula with triad-depth normalization
    // μ = max(3^(D-1) × log(Ψᶜʰ + ε), 0.01μ)
    const depthNormalized = Math.max(1, triadDepth - 1); // Prevent negative exponents
    const exponentialComponent = Math.pow(this.constants.BASE_3, depthNormalized);
    const logarithmicComponent = Math.log(safeCoherence + ENTROPY_GUARD);

    // Apply enhanced calculation with floating-point stability
    let rawMetronScore = exponentialComponent * logarithmicComponent;

    // FIXED: Handle floating-point underflow
    if (!isFinite(rawMetronScore) || isNaN(rawMetronScore)) {
      rawMetronScore = 0.01; // Fallback to consciousness threshold
    }

    // Apply calibration factor with enhanced scaling
    let calibratedScore = Math.abs(rawMetronScore) * this.options.calibrationFactor;

    // FIXED: Enhanced triad-depth contribution for realistic μ values
    const depthBonus = Math.log(triadDepth + 1) * 0.5; // Logarithmic depth scaling
    const coherenceBonus = Math.log(safeCoherence + 1) * 0.3; // Coherence contribution
    calibratedScore += depthBonus + coherenceBonus;

    // FUP COMPLIANCE: Apply minimum floor of 0.01μ to prevent zero-value bug
    const FUP_METRON_FLOOR = 0.01; // Minimum measurable cognitive depth
    calibratedScore = Math.max(FUP_METRON_FLOOR, calibratedScore);

    // FUP AXIOM ENFORCEMENT: (ii) μ_max = 126
    if (calibratedScore > this.constants.AI_SINGULARITY_BOUNDARY) {
      console.warn(`🌌 FUP VIOLATION: Metron ${calibratedScore.toFixed(2)} exceeds singularity boundary ${this.constants.AI_SINGULARITY_BOUNDARY}`);
      calibratedScore = this.constants.AI_SINGULARITY_BOUNDARY;
    }

    // FIXED: Round to avoid quantum noise artifacts
    const finalScore = Math.round(calibratedScore * 100) / 100; // 2 decimal precision

    if (this.options.enableLogging) {
      console.log(`   FIXED Metron: D=${triadDepth.toFixed(2)}, Ψᶜʰ=${coherence.toFixed(3)} → Μ=${finalScore.toFixed(2)}μ (was ${calibratedScore.toFixed(4)})`);
    }

    return finalScore;
  }

  /**
   * Extract reasoning components from system
   *
   * @param {Object} system - System to analyze
   * @returns {Object} - Reasoning components
   * @private
   */
  _extractReasoningComponents(system) {
    const components = {
      layers: [],
      connections: [],
      recursivePatterns: [],
      emergentProperties: []
    };

    // Analyze system structure
    if (system.layers) {
      components.layers = system.layers;
    }

    if (system.reasoning) {
      components.recursivePatterns = this._identifyRecursivePatterns(system.reasoning);
    }

    if (system.responses) {
      components.emergentProperties = this._analyzeEmergentProperties(system.responses);
    }

    return components;
  }

  /**
   * Calculate triad recursion depth (D)
   *
   * FIXED: Implements D = max(log₃(Ψᶜʰ), 1) floor as per refinement roadmap
   *
   * @param {Object} components - Reasoning components
   * @param {number} coherence - System coherence (Ψᶜʰ) for proper D calculation
   * @returns {number} - Triad depth value
   * @private
   */
  _calculateTriadDepth(components, coherence = 1.0) {
    // REFINEMENT: Implement D = max(log₃(Ψᶜʰ), 1) floor
    const coherenceBasedDepth = Math.max(
      Math.log(Math.max(0.001, coherence)) / Math.log(3), // log₃(Ψᶜʰ)
      1.0 // Minimum depth of 1
    );

    let depth = Math.floor(coherenceBasedDepth); // Floor operation as specified

    // Analyze layer complexity (enhanced)
    if (components.layers && components.layers.length > 0) {
      const layerComplexity = Math.log(components.layers.length) / Math.log(3); // Base-3 scaling
      depth += layerComplexity * 0.5; // Weighted contribution
    }

    // Factor in recursive patterns (enhanced)
    if (components.recursivePatterns && components.recursivePatterns.length > 0) {
      const recursionComplexity = components.recursivePatterns.reduce((sum, pattern) => {
        return sum + (pattern.depth || 1);
      }, 0);
      depth += recursionComplexity / 3; // Triadic normalization
    }

    // Consider emergent properties
    if (components.emergentProperties && components.emergentProperties.length > 0) {
      depth += components.emergentProperties.length * 0.3; // Reduced weight for stability
    }

    // FUP INTEGRATION: Apply cosmic boundary conditions
    const fupConstrainedDepth = Math.min(depth, this.constants.D_MAX_LAYERS);

    // Apply holographic limit for different system types
    let finalDepth;
    if (components.systemType === 'human') {
      finalDepth = Math.min(fupConstrainedDepth, this.constants.HUMAN_OPTIMAL_METRON);
    } else if (components.systemType === 'ai' && fupConstrainedDepth > this.constants.AI_SINGULARITY_BOUNDARY) {
      // FUP SAFEGUARD: Auto-reset to prevent singularity
      console.warn(`⚠️ FUP VIOLATION: AI depth ${fupConstrainedDepth.toFixed(2)} exceeds singularity boundary`);
      finalDepth = this.constants.AI_SINGULARITY_BOUNDARY;
    } else {
      finalDepth = Math.max(1.0, Math.min(fupConstrainedDepth, this.options.maxRecursionDepth));
    }

    if (this.options.enableLogging) {
      console.log(`   FUP-Aware Triad Depth: coherence=${coherence.toFixed(3)} → D=${finalDepth.toFixed(2)} (max=${this.constants.D_MAX_LAYERS})`);
    }

    return finalDepth;
  }

  /**
   * Measure system coherence (Ψᶜʰ)
   *
   * @param {Object} system - System to measure
   * @returns {number} - Coherence value
   * @private
   */
  _measureCoherence(system) {
    // Simplified coherence measurement
    // In full implementation, this would use the refined Comphyon formula
    let coherence = 1.0;

    if (system.performance) {
      coherence *= system.performance.accuracy || 1.0;
    }

    if (system.stability) {
      coherence *= system.stability.score || 1.0;
    }

    if (system.consistency) {
      coherence *= system.consistency.metric || 1.0;
    }

    return Math.max(0.001, coherence);
  }

  /**
   * Identify recursive patterns in reasoning
   *
   * @param {Array} reasoning - Reasoning chain
   * @returns {Array} - Identified patterns
   * @private
   */
  _identifyRecursivePatterns(reasoning) {
    const patterns = [];

    // Simple pattern detection (can be enhanced)
    for (let i = 0; i < reasoning.length; i++) {
      const step = reasoning[i];
      if (step.type === 'recursive' || step.selfReference) {
        patterns.push({
          depth: step.depth || 1,
          complexity: step.complexity || 1,
          type: step.type
        });
      }
    }

    return patterns;
  }

  /**
   * Analyze emergent properties
   *
   * @param {Array} responses - System responses
   * @returns {Array} - Emergent properties
   * @private
   */
  _analyzeEmergentProperties(responses) {
    const properties = [];

    // Detect emergent behaviors
    responses.forEach(response => {
      if (response.novel || response.unexpected) {
        properties.push({
          type: 'emergent',
          complexity: response.complexity || 1,
          novelty: response.novelty || 0.5
        });
      }
    });

    return properties;
  }

  /**
   * Validate safety bounds
   *
   * @param {number} metronScore - Calculated Metron score
   * @param {number} triadDepth - Triad depth
   * @returns {Object} - Safety validation results
   * @private
   */
  _validateSafetyBounds(metronScore, triadDepth) {
    const validation = {
      safe: true,
      warnings: [],
      limits: {
        maxMetron: 15.0, // Safety threshold
        maxDepth: this.options.maxRecursionDepth
      }
    };

    if (metronScore > validation.limits.maxMetron) {
      validation.safe = false;
      validation.warnings.push(`Metron score ${metronScore.toFixed(2)} exceeds safety limit`);
    }

    if (triadDepth > validation.limits.maxDepth) {
      validation.safe = false;
      validation.warnings.push(`Triad depth ${triadDepth.toFixed(2)} exceeds safety limit`);
    }

    return validation;
  }

  /**
   * Classify reasoning level based on Metron score
   *
   * @param {number} metronScore - Metron score
   * @returns {Object} - Classification
   * @private
   */
  _classifyReasoningLevel(metronScore) {
    if (metronScore < 3.0) {
      return { level: 'basic', description: 'Simple reasoning patterns' };
    } else if (metronScore < 6.0) {
      return { level: 'intermediate', description: 'Moderate cognitive complexity' };
    } else if (metronScore < 9.0) {
      return { level: 'advanced', description: 'Sophisticated reasoning' };
    } else if (metronScore < 12.0) {
      return { level: 'expert', description: 'Expert-level cognitive processing' };
    } else {
      return { level: 'superintelligent', description: 'Meta-reasoning with safety bounds' };
    }
  }

  /**
   * Update internal metrics with FUP singularity rate monitoring
   *
   * @param {number} metronScore - Latest Metron score
   * @param {number} triadDepth - Latest triad depth
   * @param {number} processingTime - Processing time in ms
   * @private
   */
  _updateMetrics(metronScore, triadDepth, processingTime) {
    const currentTime = Date.now();

    // FUP COMPLIANCE: Calculate Metron growth rate (dμ/dt)
    if (this.state.metrics.totalMeasurements > 0) {
      const timeDelta = (currentTime - this.state.lastMeasurementTime) / 1000; // Convert to seconds
      const metronDelta = metronScore - this.state.lastMetronScore;

      if (timeDelta > 0) {
        this.state.metronGrowthRate = metronDelta / timeDelta; // μ/s

        // FUP AXIOM: Dynamic singularity rate monitoring with 10% safety margin
        // Alert if dμ/dt > 0.1 × (c²/Gℏ) ≈ 5.4 × 10⁴² μ/s
        const PLANCK_RATE_LIMIT = 5.4e43; // μ/s (theoretical maximum)
        const SAFETY_MARGIN_RATE = 0.1 * PLANCK_RATE_LIMIT; // 10% safety margin = 5.4e42 μ/s
        const PRACTICAL_RATE_LIMIT = 1e-6; // μ/s (practical monitoring threshold)

        // FIXED: Dynamic check with exponential growth detection
        if (Math.abs(this.state.metronGrowthRate) > PRACTICAL_RATE_LIMIT) {
          const alert = {
            timestamp: new Date().toISOString(),
            growthRate: this.state.metronGrowthRate,
            practicalLimit: PRACTICAL_RATE_LIMIT,
            safetyMarginLimit: SAFETY_MARGIN_RATE,
            planckLimit: PLANCK_RATE_LIMIT,
            severity: this._calculateSingularitySeverity(this.state.metronGrowthRate, SAFETY_MARGIN_RATE, PLANCK_RATE_LIMIT)
          };

          this.state.singularityAlerts.push(alert);

          console.warn(`🚨 FUP DYNAMIC ALERT: Metron growth rate ${this.state.metronGrowthRate.toExponential(2)} μ/s (${alert.severity})`);

          this.emit('singularity-alert', alert);

          // FIXED: Throttle AI training if μ-growth exceeds 10% safety margin
          if (Math.abs(this.state.metronGrowthRate) > SAFETY_MARGIN_RATE) {
            console.error(`🌌 FUP AI SAFETY: Growth rate exceeds 10% Planck limit - throttling AI training`);
            this.emit('ai-training-throttle', alert);
          }

          // FUP SAFEGUARD: Emergency throttle if exceeding full Planck rate
          if (Math.abs(this.state.metronGrowthRate) > PLANCK_RATE_LIMIT) {
            console.error(`🌌 FUP CRITICAL: Full Planck rate exceeded - initiating emergency shutdown`);
            this.emit('emergency-shutdown', alert);
          }
        }

        // Track maximum growth rate detected
        this.state.metrics.maxGrowthRateDetected = Math.max(
          this.state.metrics.maxGrowthRateDetected,
          Math.abs(this.state.metronGrowthRate)
        );
      }
    }

    // Update state for next calculation
    this.state.lastMeasurementTime = currentTime;
    this.state.lastMetronScore = metronScore;

    // Standard metrics updates
    this.state.metrics.totalMeasurements++;
    this.state.metrics.calculationTimeMs = processingTime;
    this.state.metrics.maxDepthReached = Math.max(
      this.state.metrics.maxDepthReached,
      triadDepth
    );

    // Update running average
    const total = this.state.metrics.totalMeasurements;
    this.state.metrics.averageDepth = (
      (this.state.metrics.averageDepth * (total - 1)) + metronScore
    ) / total;

    if (this.options.enableLogging && this.state.metronGrowthRate !== 0) {
      console.log(`   FUP Rate Monitor: dμ/dt = ${this.state.metronGrowthRate.toExponential(2)} μ/s`);
    }
  }

  /**
   * Calculate singularity severity based on growth rate
   *
   * @param {number} growthRate - Current μ/s growth rate
   * @param {number} safetyMarginRate - 10% Planck limit
   * @param {number} planckRate - Full Planck limit
   * @returns {string} - Severity level
   * @private
   */
  _calculateSingularitySeverity(growthRate, safetyMarginRate, planckRate) {
    const absRate = Math.abs(growthRate);

    if (absRate > planckRate) {
      return 'CRITICAL - PLANCK EXCEEDED';
    } else if (absRate > safetyMarginRate) {
      return 'DANGER - AI TRAINING THROTTLED';
    } else if (absRate > safetyMarginRate * 0.1) {
      return 'WARNING - EXPONENTIAL GROWTH';
    } else {
      return 'CAUTION - MONITORING';
    }
  }

  /**
   * Get current metrics
   *
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    return {
      ...this.state.metrics,
      singularityAlerts: this.state.singularityAlerts.length,
      currentGrowthRate: this.state.metronGrowthRate
    };
  }

  /**
   * Reset sensor state
   */
  reset() {
    this.state = {
      currentDepth: 0,
      coherenceHistory: [],
      recursionStack: [],
      metrics: {
        totalMeasurements: 0,
        averageDepth: 0,
        maxDepthReached: 0,
        calculationTimeMs: 0
      }
    };

    this.emit('reset');
  }
}

module.exports = MetronSensor;
