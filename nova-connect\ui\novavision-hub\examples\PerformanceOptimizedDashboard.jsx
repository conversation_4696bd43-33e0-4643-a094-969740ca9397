/**
 * Performance Optimized Dashboard Example
 * 
 * This example demonstrates how to use performance optimizations.
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  VirtualList,
  LazyLoad,
  PerformanceMonitorPanel,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider } from '../theme';
import { PreferencesProvider } from '../preferences';
import { OfflineProvider } from '../offline';
import { usePerformance, memoize, createSelector } from '../performance';

/**
 * Performance Optimized Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Performance Optimized Dashboard Content component
 */
const PerformanceOptimizedDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const { measureOperation, createMeasuredCallback } = usePerformance('PerformanceOptimizedDashboard');
  
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [showPerformancePanel, setShowPerformancePanel] = useState(false);
  const [itemCount, setItemCount] = useState(1000);
  const [filterText, setFilterText] = useState('');
  
  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        
        if (enableLogging) {
          console.log('Fetching dashboard data...');
        }
        
        // Simulate API call to fetch dashboard data
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Generate large dataset
        const generateItems = measureOperation('generateItems', () => {
          const items = [];
          
          for (let i = 0; i < itemCount; i++) {
            items.push({
              id: `item-${i}`,
              name: `Item ${i}`,
              category: ['Compliance', 'Security', 'Identity', 'Risk'][i % 4],
              status: ['Active', 'Inactive', 'Pending', 'Completed'][i % 4],
              score: Math.floor(Math.random() * 100),
              timestamp: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
              details: {
                owner: `User ${i % 10}`,
                priority: ['Low', 'Medium', 'High', 'Critical'][i % 4],
                tags: [`Tag ${i % 5}`, `Tag ${(i + 1) % 5}`, `Tag ${(i + 2) % 5}`].filter((_, index) => index < (i % 3) + 1)
              }
            });
          }
          
          return items;
        });
        
        setDashboardData({
          items: generateItems
        });
        
        setLoading(false);
        
        if (enableLogging) {
          console.log('Dashboard data fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching dashboard data', error);
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [itemCount, enableLogging, measureOperation]);
  
  // Memoized filter function
  const filterItems = useMemo(() => {
    return memoize((items, filterText) => {
      if (!filterText) return items;
      
      const lowerFilterText = filterText.toLowerCase();
      
      return items.filter(item => 
        item.name.toLowerCase().includes(lowerFilterText) ||
        item.category.toLowerCase().includes(lowerFilterText) ||
        item.status.toLowerCase().includes(lowerFilterText)
      );
    }, { maxSize: 100 });
  }, []);
  
  // Create selector for filtered items
  const getFilteredItems = useMemo(() => {
    return createSelector(
      [(state) => state.items, (state) => state.filterText],
      (items, filterText) => {
        if (!items) return [];
        return filterItems(items, filterText);
      }
    );
  }, [filterItems]);
  
  // Get filtered items
  const filteredItems = useMemo(() => {
    return measureOperation('getFilteredItems', () => {
      if (!dashboardData) return [];
      return getFilteredItems({ items: dashboardData.items, filterText });
    });
  }, [dashboardData, filterText, getFilteredItems, measureOperation]);
  
  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setLoading(true);
    
    try {
      if (enableLogging) {
        console.log('Refreshing dashboard data...');
      }
      
      // Simulate API call to refresh dashboard data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update dashboard data with new values
      setDashboardData(prevData => {
        if (!prevData) return null;
        
        // Update items with new scores
        const updatedItems = prevData.items.map(item => ({
          ...item,
          score: Math.floor(Math.random() * 100),
          timestamp: new Date().toISOString()
        }));
        
        return {
          ...prevData,
          items: updatedItems
        };
      });
      
      setLoading(false);
      
      if (enableLogging) {
        console.log('Dashboard data refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing dashboard data', error);
      setLoading(false);
    }
  }, [enableLogging]);
  
  // Toggle performance panel
  const togglePerformancePanel = useCallback(() => {
    setShowPerformancePanel(!showPerformancePanel);
  }, [showPerformancePanel]);
  
  // Handle filter change
  const handleFilterChange = useCallback((e) => {
    setFilterText(e.target.value);
  }, []);
  
  // Handle item count change
  const handleItemCountChange = useCallback((e) => {
    setItemCount(Number(e.target.value));
  }, []);
  
  // Render item
  const renderItem = useCallback((item) => {
    return (
      <div className="p-4 border-b border-divider hover:bg-surface transition-colors duration-200">
        <div className="flex justify-between">
          <div>
            <h3 className="text-md font-medium text-textPrimary">{item.name}</h3>
            <div className="text-sm text-textSecondary mt-1">
              {item.category} | {item.status} | Score: {item.score}
            </div>
          </div>
          <div className="text-xs text-textSecondary">
            {new Date(item.timestamp).toLocaleString()}
          </div>
        </div>
      </div>
    );
  }, []);
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Performance Optimized Dashboard
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
          
          <button
            className="px-4 py-2 bg-surface text-textPrimary border border-divider rounded-md hover:bg-actionHover transition-colors duration-200"
            onClick={togglePerformancePanel}
          >
            {showPerformancePanel ? 'Hide Performance' : 'Show Performance'}
          </button>
          
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>
      
      {/* Controls */}
      <div className="flex flex-wrap gap-4 items-center">
        <div>
          <label htmlFor="filter" className="block text-sm font-medium text-textSecondary mb-1">Filter</label>
          <input
            id="filter"
            type="text"
            className="px-3 py-2 border border-divider rounded-md bg-background text-textPrimary w-64"
            placeholder="Filter by name, category, or status"
            value={filterText}
            onChange={handleFilterChange}
          />
        </div>
        
        <div>
          <label htmlFor="item-count" className="block text-sm font-medium text-textSecondary mb-1">Item Count</label>
          <select
            id="item-count"
            className="px-3 py-2 border border-divider rounded-md bg-background text-textPrimary"
            value={itemCount}
            onChange={handleItemCountChange}
          >
            <option value="100">100 items</option>
            <option value="1000">1,000 items</option>
            <option value="10000">10,000 items</option>
            <option value="100000">100,000 items</option>
          </select>
        </div>
        
        <div className="ml-auto">
          <div className="text-sm text-textSecondary">
            Showing {filteredItems.length} of {dashboardData?.items?.length || 0} items
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <div className="space-y-6">
          {/* Virtual list */}
          <DashboardCard
            title="Virtualized List"
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className="h-[500px]">
              {dashboardData ? (
                <VirtualList
                  items={filteredItems}
                  renderItem={renderItem}
                  itemHeight={80}
                  overscan={5}
                  getItemId={(item) => item.id}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-textSecondary">No data available</p>
                </div>
              )}
            </div>
          </DashboardCard>
          
          {/* Lazy loaded content */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((index) => (
              <LazyLoad
                key={index}
                placeholder={
                  <div className="h-64 bg-surface border border-divider rounded-md flex items-center justify-center">
                    <p className="text-textSecondary">Loading content...</p>
                  </div>
                }
                threshold={0.1}
                once={true}
              >
                <DashboardCard
                  title={`Lazy Loaded Card ${index}`}
                  collapsible={true}
                >
                  <div className="h-48 p-4">
                    <p className="text-textPrimary">
                      This content was lazily loaded when it became visible in the viewport.
                    </p>
                    <p className="text-textSecondary mt-2">
                      Lazy loading helps improve initial page load performance by deferring the loading of off-screen content.
                    </p>
                  </div>
                </DashboardCard>
              </LazyLoad>
            ))}
          </div>
        </div>
      </main>
      
      {/* Performance panel */}
      {showPerformancePanel && (
        <div className="mt-6">
          <PerformanceMonitorPanel
            startMonitoringOnMount={true}
          />
        </div>
      )}
    </div>
  );
};

PerformanceOptimizedDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Performance Optimized Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Performance Optimized Dashboard component
 */
const PerformanceOptimizedDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <PreferencesProvider>
        <OfflineProvider>
          <PerformanceOptimizedDashboardContent
            novaConnect={novaConnect}
            novaShield={novaShield}
            novaTrack={novaTrack}
            enableLogging={enableLogging}
          />
        </OfflineProvider>
      </PreferencesProvider>
    </ThemeProvider>
  );
};

PerformanceOptimizedDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default PerformanceOptimizedDashboard;
