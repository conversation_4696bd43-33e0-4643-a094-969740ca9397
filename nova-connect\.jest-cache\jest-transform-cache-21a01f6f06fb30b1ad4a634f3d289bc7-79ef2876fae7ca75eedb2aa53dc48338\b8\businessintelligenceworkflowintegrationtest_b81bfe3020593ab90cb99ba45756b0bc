71832b067aa2c5359745202deb327ad4
// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Integration tests for the Business Intelligence & Workflow Connector
 */

const nock = require('nock');
const BusinessIntelligenceWorkflowConnector = require('../../../../connector/implementations/business-intelligence-workflow');
describe('BusinessIntelligenceWorkflowConnector Integration', () => {
  let connector;
  const baseUrl = 'https://api.test.com';
  beforeAll(() => {
    // Disable real HTTP requests
    nock.disableNetConnect();
  });
  afterAll(() => {
    // Enable real HTTP requests
    nock.enableNetConnect();
  });
  beforeEach(() => {
    // Reset nock
    nock.cleanAll();

    // Create connector instance
    connector = new BusinessIntelligenceWorkflowConnector({
      baseUrl
    }, {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    });

    // Mock authentication
    nock(baseUrl).post('/oauth2/token').reply(200, {
      access_token: 'test-access-token',
      expires_in: 3600
    });
  });
  describe('Dashboard Management', () => {
    it('should list dashboards', async () => {
      // Mock dashboards endpoint
      const mockDashboards = {
        data: [{
          id: 'dashboard-1',
          name: 'Financial Overview',
          folder: 'Finance',
          owner: '<EMAIL>'
        }, {
          id: 'dashboard-2',
          name: 'Sales Performance',
          folder: 'Finance',
          owner: '<EMAIL>'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/dashboards').query({
        folder: 'Finance'
      }).reply(200, mockDashboards);

      // Initialize connector
      await connector.initialize();

      // List dashboards
      const result = await connector.listDashboards({
        folder: 'Finance'
      });

      // Verify result
      expect(result).toEqual(mockDashboards);
    });
    it('should get a specific dashboard', async () => {
      // Mock dashboard endpoint
      const mockDashboard = {
        id: 'dashboard-123',
        name: 'Financial Overview',
        description: 'Financial KPIs and metrics',
        folder: 'Finance',
        owner: '<EMAIL>',
        widgets: [{
          id: 'widget-1',
          type: 'chart',
          title: 'Revenue by Quarter'
        }]
      };
      nock(baseUrl).get('/dashboards/dashboard-123').reply(200, mockDashboard);

      // Initialize connector
      await connector.initialize();

      // Get dashboard
      const result = await connector.getDashboard('dashboard-123');

      // Verify result
      expect(result).toEqual(mockDashboard);
    });
    it('should create a new dashboard', async () => {
      // Dashboard data
      const dashboardData = {
        name: 'New Dashboard',
        description: 'New dashboard description',
        folder: 'Finance'
      };

      // Mock response
      const mockResponse = {
        id: 'dashboard-new',
        ...dashboardData,
        owner: '<EMAIL>',
        createdAt: '2023-06-15T10:30:00Z',
        updatedAt: '2023-06-15T10:30:00Z'
      };
      nock(baseUrl).post('/dashboards', dashboardData).reply(201, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Create dashboard
      const result = await connector.createDashboard(dashboardData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should update an existing dashboard', async () => {
      // Dashboard update data
      const dashboardId = 'dashboard-123';
      const updateData = {
        name: 'Updated Dashboard',
        description: 'Updated description'
      };

      // Mock response
      const mockResponse = {
        id: dashboardId,
        name: 'Updated Dashboard',
        description: 'Updated description',
        folder: 'Finance',
        owner: '<EMAIL>',
        updatedAt: '2023-06-15T11:45:00Z'
      };
      nock(baseUrl).put(`/dashboards/${dashboardId}`, updateData).reply(200, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Update dashboard
      const result = await connector.updateDashboard(dashboardId, updateData);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
    it('should delete a dashboard', async () => {
      // Dashboard ID
      const dashboardId = 'dashboard-123';
      nock(baseUrl).delete(`/dashboards/${dashboardId}`).reply(204);

      // Initialize connector
      await connector.initialize();

      // Delete dashboard
      await connector.deleteDashboard(dashboardId);

      // If no error is thrown, the test passes
      expect(true).toBe(true);
    });
  });
  describe('Report Management', () => {
    it('should list reports', async () => {
      // Mock reports endpoint
      const mockReports = {
        data: [{
          id: 'report-1',
          name: 'Quarterly Financial Report',
          folder: 'Finance',
          owner: '<EMAIL>'
        }, {
          id: 'report-2',
          name: 'Sales by Region',
          folder: 'Finance',
          owner: '<EMAIL>'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/reports').query({
        folder: 'Finance'
      }).reply(200, mockReports);

      // Initialize connector
      await connector.initialize();

      // List reports
      const result = await connector.listReports({
        folder: 'Finance'
      });

      // Verify result
      expect(result).toEqual(mockReports);
    });
    it('should get a specific report', async () => {
      // Mock report endpoint
      const mockReport = {
        id: 'report-123',
        name: 'Quarterly Financial Report',
        description: 'Detailed financial analysis by quarter',
        folder: 'Finance',
        owner: '<EMAIL>',
        query: 'SELECT * FROM financial_data WHERE quarter = :quarter AND year = :year',
        parameters: [{
          name: 'quarter',
          type: 'string',
          defaultValue: 'Q1'
        }, {
          name: 'year',
          type: 'integer',
          defaultValue: '2023'
        }]
      };
      nock(baseUrl).get('/reports/report-123').reply(200, mockReport);

      // Initialize connector
      await connector.initialize();

      // Get report
      const result = await connector.getReport('report-123');

      // Verify result
      expect(result).toEqual(mockReport);
    });
    it('should execute a report', async () => {
      // Report ID
      const reportId = 'report-123';

      // Execution options
      const options = {
        parameters: {
          quarter: 'Q1',
          year: 2023
        },
        format: 'json'
      };

      // Mock response
      const mockResponse = {
        executionId: 'exec-123',
        status: 'success',
        data: [{
          region: 'North America',
          revenue: 1250000,
          expenses: 750000,
          profit: 500000
        }, {
          region: 'Europe',
          revenue: 980000,
          expenses: 620000,
          profit: 360000
        }],
        executedAt: '2023-06-01T10:15:30Z'
      };
      nock(baseUrl).post(`/reports/${reportId}/execute`, options).reply(200, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Execute report
      const result = await connector.executeReport(reportId, options);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  describe('Workflow Management', () => {
    it('should list workflows', async () => {
      // Mock workflows endpoint
      const mockWorkflows = {
        data: [{
          id: 'workflow-1',
          name: 'Invoice Approval',
          status: 'active',
          category: 'Finance'
        }, {
          id: 'workflow-2',
          name: 'Expense Report Processing',
          status: 'active',
          category: 'Finance'
        }],
        pagination: {
          page: 1,
          limit: 20,
          totalItems: 2,
          totalPages: 1
        }
      };
      nock(baseUrl).get('/workflows').query({
        status: 'active',
        category: 'Finance'
      }).reply(200, mockWorkflows);

      // Initialize connector
      await connector.initialize();

      // List workflows
      const result = await connector.listWorkflows({
        status: 'active',
        category: 'Finance'
      });

      // Verify result
      expect(result).toEqual(mockWorkflows);
    });
    it('should get a specific workflow', async () => {
      // Mock workflow endpoint
      const mockWorkflow = {
        id: 'workflow-123',
        name: 'Invoice Approval',
        description: 'Workflow for approving invoices',
        status: 'active',
        category: 'Finance',
        trigger: {
          type: 'event',
          config: {
            event: 'invoice.created'
          }
        },
        steps: [{
          id: 'step-1',
          name: 'Validate Invoice',
          type: 'script',
          config: {
            script: 'validateInvoice'
          },
          nextSteps: [{
            stepId: 'step-2',
            condition: 'invoice.valid === true'
          }]
        }, {
          id: 'step-2',
          name: 'Approve Invoice',
          type: 'approval',
          config: {
            approvers: ['finance-manager']
          }
        }]
      };
      nock(baseUrl).get('/workflows/workflow-123').reply(200, mockWorkflow);

      // Initialize connector
      await connector.initialize();

      // Get workflow
      const result = await connector.getWorkflow('workflow-123');

      // Verify result
      expect(result).toEqual(mockWorkflow);
    });
    it('should execute a workflow', async () => {
      // Workflow ID
      const workflowId = 'workflow-123';

      // Execution options
      const options = {
        input: {
          invoiceId: 'INV-12345',
          amount: 1500,
          vendor: 'Acme Corp'
        },
        async: true
      };

      // Mock response
      const mockResponse = {
        executionId: 'exec-123',
        status: 'queued',
        startTime: '2023-06-01T10:15:30Z',
        statusUrl: `https://api.test.com/workflows/${workflowId}/executions/exec-123`
      };
      nock(baseUrl).post(`/workflows/${workflowId}/execute`, options).reply(202, mockResponse);

      // Initialize connector
      await connector.initialize();

      // Execute workflow
      const result = await connector.executeWorkflow(workflowId, options);

      // Verify result
      expect(result).toEqual(mockResponse);
    });
  });
  describe('Error Handling', () => {
    it('should handle authentication errors', async () => {
      // Clean previous nock mocks
      nock.cleanAll();

      // Mock authentication error
      nock(baseUrl).post('/oauth2/token').reply(401, {
        error: 'invalid_client',
        error_description: 'Invalid client credentials'
      });

      // Try to initialize connector
      await expect(connector.initialize()).rejects.toThrow('Authentication failed');
    });
    it('should handle not found errors', async () => {
      // Mock not found error
      nock(baseUrl).get('/dashboards/non-existent').reply(404, {
        error: 'not_found',
        error_description: 'Dashboard not found'
      });

      // Initialize connector
      await connector.initialize();

      // Try to get non-existent dashboard
      await expect(connector.getDashboard('non-existent')).rejects.toThrow('Error getting dashboard');
    });
    it('should handle validation errors', async () => {
      // Dashboard data with missing required fields
      const invalidData = {
        description: 'Invalid Dashboard'
        // Missing required field: name
      };

      // Mock validation error
      nock(baseUrl).post('/dashboards', invalidData).reply(400, {
        error: 'validation_error',
        error_description: 'Validation failed',
        errors: [{
          field: 'name',
          message: 'Name is required'
        }]
      });

      // Initialize connector
      await connector.initialize();

      // Try to create invalid dashboard
      await expect(connector.createDashboard(invalidData)).rejects.toThrow('name is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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