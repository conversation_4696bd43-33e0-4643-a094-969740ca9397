/**
 * CSDE Performance Monitoring Service
 * 
 * This module provides comprehensive performance monitoring for the CSDE engine,
 * including metrics collection, analysis, and visualization data generation.
 */

const { performance } = require('perf_hooks');
const os = require('os');
const EventEmitter = require('events');

/**
 * CSDE Performance Monitoring Service
 */
class CSEDPerformanceMonitoringService extends EventEmitter {
  /**
   * Create a new CSDE Performance Monitoring Service
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      sampleInterval: options.sampleInterval || 60, // 1 minute
      historySize: options.historySize || 60, // 1 hour of minute samples
      enableSystemMetrics: options.enableSystemMetrics !== false,
      enableDetailedMetrics: options.enableDetailedMetrics !== false,
      enableAlerts: options.enableAlerts !== false,
      alertThresholds: {
        cpuUsage: options.alertThresholds?.cpuUsage || 0.8, // 80%
        memoryUsage: options.alertThresholds?.memoryUsage || 0.8, // 80%
        errorRate: options.alertThresholds?.errorRate || 0.05, // 5%
        latency: options.alertThresholds?.latency || 100, // 100ms
        performanceFactor: options.alertThresholds?.performanceFactor || 1000, // 1000x
        ...options.alertThresholds
      },
      logger: options.logger || console,
      ...options
    };
    
    // Initialize metrics
    this.metrics = {
      // System metrics
      system: {
        cpu: [],
        memory: [],
        eventLoop: []
      },
      
      // CSDE engine metrics
      engine: {
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        totalLatency: 0,
        averageLatency: 0,
        operationsPerSecond: 0,
        performanceFactor: 3142, // Default to theoretical maximum
        lastCalculatedAt: Date.now()
      },
      
      // Cache metrics
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        size: 0,
        maxSize: 0
      },
      
      // Component metrics
      components: {
        tensor: {
          operations: 0,
          latency: 0,
          averageLatency: 0
        },
        fusion: {
          operations: 0,
          latency: 0,
          averageLatency: 0
        },
        circularTrust: {
          operations: 0,
          latency: 0,
          averageLatency: 0
        }
      },
      
      // History
      history: {
        operations: [],
        latency: [],
        performanceFactor: [],
        cacheHitRate: [],
        errorRate: [],
        cpuUsage: [],
        memoryUsage: []
      },
      
      // Alerts
      alerts: []
    };
    
    this.startTime = Date.now();
    this.sampleInterval = null;
    
    // Start sampling if enabled
    if (this.options.sampleInterval > 0) {
      this.startSampling();
    }
    
    this.options.logger.info('CSDE Performance Monitoring Service initialized', {
      sampleInterval: this.options.sampleInterval,
      historySize: this.options.historySize,
      enableSystemMetrics: this.options.enableSystemMetrics,
      enableDetailedMetrics: this.options.enableDetailedMetrics,
      enableAlerts: this.options.enableAlerts
    });
  }
  
  /**
   * Start sampling metrics
   */
  startSampling() {
    if (this.sampleInterval) {
      return;
    }
    
    this.sampleInterval = setInterval(() => {
      this.sampleMetrics();
    }, this.options.sampleInterval * 1000);
    
    this.options.logger.debug('Started sampling metrics');
  }
  
  /**
   * Stop sampling metrics
   */
  stopSampling() {
    if (this.sampleInterval) {
      clearInterval(this.sampleInterval);
      this.sampleInterval = null;
      this.options.logger.debug('Stopped sampling metrics');
    }
  }
  
  /**
   * Sample metrics
   */
  sampleMetrics() {
    // Sample system metrics if enabled
    if (this.options.enableSystemMetrics) {
      this._sampleSystemMetrics();
    }
    
    // Sample CSDE metrics
    this._sampleCSEDMetrics();
    
    // Check for alerts if enabled
    if (this.options.enableAlerts) {
      this._checkAlerts();
    }
    
    // Emit metrics updated event
    this.emit('metrics-updated', this.getMetrics());
  }
  
  /**
   * Sample system metrics
   * @private
   */
  _sampleSystemMetrics() {
    // Sample CPU usage
    const cpuUsage = this._getCpuUsage();
    this.metrics.system.cpu.push({
      timestamp: Date.now(),
      value: cpuUsage
    });
    
    // Trim history
    if (this.metrics.system.cpu.length > this.options.historySize) {
      this.metrics.system.cpu.shift();
    }
    
    // Update history
    this.metrics.history.cpuUsage.push({
      timestamp: Date.now(),
      value: cpuUsage
    });
    
    // Trim history
    if (this.metrics.history.cpuUsage.length > this.options.historySize) {
      this.metrics.history.cpuUsage.shift();
    }
    
    // Sample memory usage
    const memoryUsage = this._getMemoryUsage();
    this.metrics.system.memory.push({
      timestamp: Date.now(),
      value: memoryUsage
    });
    
    // Trim history
    if (this.metrics.system.memory.length > this.options.historySize) {
      this.metrics.system.memory.shift();
    }
    
    // Update history
    this.metrics.history.memoryUsage.push({
      timestamp: Date.now(),
      value: memoryUsage
    });
    
    // Trim history
    if (this.metrics.history.memoryUsage.length > this.options.historySize) {
      this.metrics.history.memoryUsage.shift();
    }
    
    // Sample event loop lag
    const eventLoopLag = this._getEventLoopLag();
    this.metrics.system.eventLoop.push({
      timestamp: Date.now(),
      value: eventLoopLag
    });
    
    // Trim history
    if (this.metrics.system.eventLoop.length > this.options.historySize) {
      this.metrics.system.eventLoop.shift();
    }
  }
  
  /**
   * Sample CSDE metrics
   * @private
   */
  _sampleCSEDMetrics() {
    const now = Date.now();
    
    // Calculate operations per second
    const timeDiff = (now - this.metrics.engine.lastCalculatedAt) / 1000; // Convert to seconds
    
    if (timeDiff >= 1) {
      this.metrics.engine.operationsPerSecond = this.metrics.engine.totalOperations / timeDiff;
      this.metrics.engine.lastCalculatedAt = now;
    }
    
    // Calculate cache hit rate
    const totalCacheAccesses = this.metrics.cache.hits + this.metrics.cache.misses;
    this.metrics.cache.hitRate = totalCacheAccesses > 0 ? this.metrics.cache.hits / totalCacheAccesses : 0;
    
    // Calculate error rate
    const errorRate = this.metrics.engine.totalOperations > 0 
      ? this.metrics.engine.failedOperations / this.metrics.engine.totalOperations 
      : 0;
    
    // Update history
    this.metrics.history.operations.push({
      timestamp: now,
      value: this.metrics.engine.operationsPerSecond
    });
    
    this.metrics.history.latency.push({
      timestamp: now,
      value: this.metrics.engine.averageLatency
    });
    
    this.metrics.history.performanceFactor.push({
      timestamp: now,
      value: this.metrics.engine.performanceFactor
    });
    
    this.metrics.history.cacheHitRate.push({
      timestamp: now,
      value: this.metrics.cache.hitRate
    });
    
    this.metrics.history.errorRate.push({
      timestamp: now,
      value: errorRate
    });
    
    // Trim history
    if (this.metrics.history.operations.length > this.options.historySize) {
      this.metrics.history.operations.shift();
    }
    
    if (this.metrics.history.latency.length > this.options.historySize) {
      this.metrics.history.latency.shift();
    }
    
    if (this.metrics.history.performanceFactor.length > this.options.historySize) {
      this.metrics.history.performanceFactor.shift();
    }
    
    if (this.metrics.history.cacheHitRate.length > this.options.historySize) {
      this.metrics.history.cacheHitRate.shift();
    }
    
    if (this.metrics.history.errorRate.length > this.options.historySize) {
      this.metrics.history.errorRate.shift();
    }
  }
  
  /**
   * Check for alerts
   * @private
   */
  _checkAlerts() {
    const now = Date.now();
    const alerts = [];
    
    // Check CPU usage
    const cpuUsage = this.metrics.system.cpu.length > 0 
      ? this.metrics.system.cpu[this.metrics.system.cpu.length - 1].value 
      : 0;
    
    if (cpuUsage > this.options.alertThresholds.cpuUsage) {
      alerts.push({
        type: 'cpu',
        level: 'warning',
        message: `CPU usage is high: ${(cpuUsage * 100).toFixed(2)}%`,
        timestamp: now
      });
    }
    
    // Check memory usage
    const memoryUsage = this.metrics.system.memory.length > 0 
      ? this.metrics.system.memory[this.metrics.system.memory.length - 1].value 
      : 0;
    
    if (memoryUsage > this.options.alertThresholds.memoryUsage) {
      alerts.push({
        type: 'memory',
        level: 'warning',
        message: `Memory usage is high: ${(memoryUsage * 100).toFixed(2)}%`,
        timestamp: now
      });
    }
    
    // Check error rate
    const errorRate = this.metrics.engine.totalOperations > 0 
      ? this.metrics.engine.failedOperations / this.metrics.engine.totalOperations 
      : 0;
    
    if (errorRate > this.options.alertThresholds.errorRate) {
      alerts.push({
        type: 'error',
        level: 'warning',
        message: `Error rate is high: ${(errorRate * 100).toFixed(2)}%`,
        timestamp: now
      });
    }
    
    // Check latency
    if (this.metrics.engine.averageLatency > this.options.alertThresholds.latency) {
      alerts.push({
        type: 'latency',
        level: 'warning',
        message: `Average latency is high: ${this.metrics.engine.averageLatency.toFixed(2)}ms`,
        timestamp: now
      });
    }
    
    // Check performance factor
    if (this.metrics.engine.performanceFactor < this.options.alertThresholds.performanceFactor) {
      alerts.push({
        type: 'performance',
        level: 'warning',
        message: `Performance factor is low: ${this.metrics.engine.performanceFactor.toFixed(2)}x`,
        timestamp: now
      });
    }
    
    // Add alerts to metrics
    if (alerts.length > 0) {
      this.metrics.alerts = [...this.metrics.alerts, ...alerts];
      
      // Trim alerts
      if (this.metrics.alerts.length > 100) {
        this.metrics.alerts = this.metrics.alerts.slice(-100);
      }
      
      // Emit alerts
      alerts.forEach(alert => {
        this.emit('alert', alert);
      });
    }
  }
  
  /**
   * Get CPU usage
   * @returns {number} CPU usage (0-1)
   * @private
   */
  _getCpuUsage() {
    try {
      const cpus = os.cpus();
      let totalIdle = 0;
      let totalTick = 0;
      
      cpus.forEach(cpu => {
        for (const type in cpu.times) {
          totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
      });
      
      return 1 - (totalIdle / totalTick);
    } catch (error) {
      this.options.logger.error('Error getting CPU usage', error);
      return 0;
    }
  }
  
  /**
   * Get memory usage
   * @returns {number} Memory usage (0-1)
   * @private
   */
  _getMemoryUsage() {
    try {
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      return (totalMemory - freeMemory) / totalMemory;
    } catch (error) {
      this.options.logger.error('Error getting memory usage', error);
      return 0;
    }
  }
  
  /**
   * Get event loop lag
   * @returns {number} Event loop lag in milliseconds
   * @private
   */
  _getEventLoopLag() {
    return new Promise(resolve => {
      const start = performance.now();
      setImmediate(() => {
        const lag = performance.now() - start;
        resolve(lag);
      });
    });
  }
  
  /**
   * Record operation metrics
   * @param {Object} data - Operation data
   */
  recordOperation(data) {
    const { success, latency, cacheHit, component } = data;
    
    // Update engine metrics
    this.metrics.engine.totalOperations++;
    
    if (success) {
      this.metrics.engine.successfulOperations++;
    } else {
      this.metrics.engine.failedOperations++;
    }
    
    if (latency) {
      this.metrics.engine.totalLatency += latency;
      this.metrics.engine.averageLatency = this.metrics.engine.totalLatency / this.metrics.engine.totalOperations;
      
      // Update performance factor (baseline latency / average latency)
      // Baseline latency is 220ms (traditional compliance processing)
      const baselineLatency = 220;
      this.metrics.engine.performanceFactor = baselineLatency / this.metrics.engine.averageLatency;
    }
    
    // Update cache metrics
    if (cacheHit !== undefined) {
      if (cacheHit) {
        this.metrics.cache.hits++;
      } else {
        this.metrics.cache.misses++;
      }
    }
    
    // Update component metrics if provided
    if (component) {
      if (this.metrics.components[component]) {
        this.metrics.components[component].operations++;
        
        if (latency) {
          this.metrics.components[component].latency += latency;
          this.metrics.components[component].averageLatency = 
            this.metrics.components[component].latency / this.metrics.components[component].operations;
        }
      }
    }
  }
  
  /**
   * Update cache metrics
   * @param {Object} data - Cache data
   */
  updateCacheMetrics(data) {
    const { size, maxSize } = data;
    
    if (size !== undefined) {
      this.metrics.cache.size = size;
    }
    
    if (maxSize !== undefined) {
      this.metrics.cache.maxSize = maxSize;
    }
  }
  
  /**
   * Get current metrics
   * @returns {Object} Current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get visualization data for dashboard
   * @returns {Object} Visualization data
   */
  getVisualizationData() {
    // Generate time labels
    const timeLabels = this.metrics.history.operations.map(item => {
      const date = new Date(item.timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    });
    
    // Generate operations data
    const operationsData = this.metrics.history.operations.map(item => item.value);
    
    // Generate latency data
    const latencyData = this.metrics.history.latency.map(item => item.value);
    
    // Generate performance factor data
    const performanceFactorData = this.metrics.history.performanceFactor.map(item => item.value);
    
    // Generate cache hit rate data
    const cacheHitRateData = this.metrics.history.cacheHitRate.map(item => item.value);
    
    // Generate error rate data
    const errorRateData = this.metrics.history.errorRate.map(item => item.value);
    
    // Generate CPU usage data
    const cpuUsageData = this.metrics.history.cpuUsage.map(item => item.value);
    
    // Generate memory usage data
    const memoryUsageData = this.metrics.history.memoryUsage.map(item => item.value);
    
    return {
      timeLabels,
      operations: {
        labels: timeLabels,
        data: operationsData
      },
      latency: {
        labels: timeLabels,
        data: latencyData
      },
      performanceFactor: {
        labels: timeLabels,
        data: performanceFactorData
      },
      cacheHitRate: {
        labels: timeLabels,
        data: cacheHitRateData
      },
      errorRate: {
        labels: timeLabels,
        data: errorRateData
      },
      cpuUsage: {
        labels: timeLabels,
        data: cpuUsageData
      },
      memoryUsage: {
        labels: timeLabels,
        data: memoryUsageData
      },
      components: {
        labels: Object.keys(this.metrics.components),
        operations: Object.values(this.metrics.components).map(component => component.operations),
        latency: Object.values(this.metrics.components).map(component => component.averageLatency)
      }
    };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      // System metrics
      system: {
        cpu: [],
        memory: [],
        eventLoop: []
      },
      
      // CSDE engine metrics
      engine: {
        totalOperations: 0,
        successfulOperations: 0,
        failedOperations: 0,
        totalLatency: 0,
        averageLatency: 0,
        operationsPerSecond: 0,
        performanceFactor: 3142, // Default to theoretical maximum
        lastCalculatedAt: Date.now()
      },
      
      // Cache metrics
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        size: 0,
        maxSize: 0
      },
      
      // Component metrics
      components: {
        tensor: {
          operations: 0,
          latency: 0,
          averageLatency: 0
        },
        fusion: {
          operations: 0,
          latency: 0,
          averageLatency: 0
        },
        circularTrust: {
          operations: 0,
          latency: 0,
          averageLatency: 0
        }
      },
      
      // History
      history: {
        operations: [],
        latency: [],
        performanceFactor: [],
        cacheHitRate: [],
        errorRate: [],
        cpuUsage: [],
        memoryUsage: []
      },
      
      // Alerts
      alerts: []
    };
    
    this.startTime = Date.now();
    
    this.options.logger.info('Metrics reset');
  }
}

module.exports = CSEDPerformanceMonitoringService;
