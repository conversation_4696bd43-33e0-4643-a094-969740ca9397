import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  ContainerBox,
  ContainerLabel,
  CurvedArrow,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText
} from '../../components/DiagramComponents';

const NovaDNA = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>NOVADNA - UNIVERSAL IDENTITY VERIFICATION SYSTEM</ContainerLabel>
      </ContainerBox>
      
      {/* Core Architecture */}
      <ContainerBox width="700px" height="350px" left="50px" top="70px">
        <ContainerLabel>IDENTITY VERIFICATION ARCHITECTURE</ContainerLabel>
      </ContainerBox>
      
      {/* Central Identity Core */}
      <ContainerBox width="200px" height="200px" left="300px" top="120px" style={{ borderRadius: '50%' }}>
        <ContainerLabel style={{ top: '90px' }}>IDENTITY CORE</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="340px" top="160px" width="120px" height="40px">
        <ComponentNumber>701</ComponentNumber>
        <ComponentLabel>Zero-Persistence</ComponentLabel>
        Identity Engine
      </ComponentBox>
      
      <ComponentBox left="340px" top="210px" width="120px" height="40px">
        <ComponentNumber>702</ComponentNumber>
        <ComponentLabel>Blockchain</ComponentLabel>
        Anchor
      </ComponentBox>
      
      {/* Left Side - Identity Sources */}
      <ContainerBox width="150px" height="280px" left="100px" top="120px">
        <ContainerLabel>IDENTITY SOURCES</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="115px" top="160px" width="120px" height="40px">
        <ComponentNumber>703</ComponentNumber>
        <ComponentLabel>NFC</ComponentLabel>
        Wristbands
      </ComponentBox>
      
      <ComponentBox left="115px" top="210px" width="120px" height="40px">
        <ComponentNumber>704</ComponentNumber>
        <ComponentLabel>QR</ComponentLabel>
        Stickers
      </ComponentBox>
      
      <ComponentBox left="115px" top="260px" width="120px" height="40px">
        <ComponentNumber>705</ComponentNumber>
        <ComponentLabel>Biometric</ComponentLabel>
        Identifiers
      </ComponentBox>
      
      <ComponentBox left="115px" top="310px" width="120px" height="40px">
        <ComponentNumber>706</ComponentNumber>
        <ComponentLabel>Mobile</ComponentLabel>
        Devices
      </ComponentBox>
      
      {/* Right Side - Access Systems */}
      <ContainerBox width="150px" height="280px" left="550px" top="120px">
        <ContainerLabel>ACCESS SYSTEMS</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="565px" top="160px" width="120px" height="40px">
        <ComponentNumber>707</ComponentNumber>
        <ComponentLabel>Emergency</ComponentLabel>
        Health Profiles
      </ComponentBox>
      
      <ComponentBox left="565px" top="210px" width="120px" height="40px">
        <ComponentNumber>708</ComponentNumber>
        <ComponentLabel>Break-Glass</ComponentLabel>
        Access
      </ComponentBox>
      
      <ComponentBox left="565px" top="260px" width="120px" height="40px">
        <ComponentNumber>709</ComponentNumber>
        <ComponentLabel>Self-Destructing</ComponentLabel>
        Sessions
      </ComponentBox>
      
      <ComponentBox left="565px" top="310px" width="120px" height="40px">
        <ComponentNumber>710</ComponentNumber>
        <ComponentLabel>Dynamic UI</ComponentLabel>
        Consent
      </ComponentBox>
      
      {/* Connecting Arrows - Left Side */}
      <Arrow left="235px" top="180px" width="65px" />
      <Arrow left="235px" top="230px" width="65px" />
      <Arrow left="235px" top="280px" width="65px" />
      <Arrow left="235px" top="330px" width="65px" />
      
      {/* Connecting Arrows - Right Side */}
      <Arrow left="500px" top="180px" width="65px" />
      <Arrow left="500px" top="230px" width="65px" />
      <Arrow left="500px" top="280px" width="65px" />
      <Arrow left="500px" top="330px" width="65px" />
      
      {/* Bottom Layer - Core Features */}
      <ContainerBox width="650px" height="70px" left="75px" top="410px">
        <ContainerLabel>CORE FEATURES</ContainerLabel>
      </ContainerBox>
      
      <ComponentBox left="100px" top="430px" width="120px" height="40px">
        <ComponentNumber>711</ComponentNumber>
        <ComponentLabel>Multi-Biometric</ComponentLabel>
        Verification
      </ComponentBox>
      
      <ComponentBox left="250px" top="430px" width="120px" height="40px">
        <ComponentNumber>712</ComponentNumber>
        <ComponentLabel>Blockchain-Anchored</ComponentLabel>
        PHI Disclosure
      </ComponentBox>
      
      <ComponentBox left="400px" top="430px" width="120px" height="40px">
        <ComponentNumber>713</ComponentNumber>
        <ComponentLabel>Eye-Blink</ComponentLabel>
        Revocation
      </ComponentBox>
      
      <ComponentBox left="550px" top="430px" width="120px" height="40px">
        <ComponentNumber>714</ComponentNumber>
        <ComponentLabel>Secure</ComponentLabel>
        Data Pipes
      </ComponentBox>
      
      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Identity Core</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Identity Sources</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Access Systems</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Core Features</LegendText>
        </LegendItem>
      </DiagramLegend>
    </DiagramFrame>
  );
};

export default NovaDNA;
