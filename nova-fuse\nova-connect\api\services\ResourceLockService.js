/**
 * Resource Lock Service
 * 
 * This service handles resource locking for governance controls.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const TeamService = require('./TeamService');
const AuditService = require('./AuditService');

class ResourceLockService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.locksFile = path.join(this.dataDir, 'resource_locks.json');
    this.lockOverridesFile = path.join(this.dataDir, 'resource_lock_overrides.json');
    this.teamService = new TeamService(dataDir);
    this.auditService = new AuditService(dataDir);
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.locksFile, []);
      await this.initializeFile(this.lockOverridesFile, []);
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get all resource locks
   */
  async getAllResourceLocks(filters = {}) {
    const locks = await this.loadData(this.locksFile);
    
    // Apply filters
    let filteredLocks = locks;
    
    if (filters.resourceType) {
      filteredLocks = filteredLocks.filter(lock => lock.resourceType === filters.resourceType);
    }
    
    if (filters.resourceId) {
      filteredLocks = filteredLocks.filter(lock => lock.resourceId === filters.resourceId);
    }
    
    if (filters.teamId) {
      filteredLocks = filteredLocks.filter(lock => lock.teamId === filters.teamId);
    }
    
    if (filters.environmentId) {
      filteredLocks = filteredLocks.filter(lock => lock.environmentId === filters.environmentId);
    }
    
    if (filters.lockType) {
      filteredLocks = filteredLocks.filter(lock => lock.lockType === filters.lockType);
    }
    
    if (filters.createdBy) {
      filteredLocks = filteredLocks.filter(lock => lock.createdBy === filters.createdBy);
    }
    
    // Sort by created date (newest first)
    filteredLocks.sort((a, b) => new Date(b.created) - new Date(a.created));
    
    return filteredLocks;
  }

  /**
   * Get resource locks for a team
   */
  async getResourceLocksForTeam(teamId, filters = {}) {
    return this.getAllResourceLocks({ ...filters, teamId });
  }

  /**
   * Get resource lock by ID
   */
  async getResourceLockById(id) {
    const locks = await this.loadData(this.locksFile);
    const lock = locks.find(l => l.id === id);
    
    if (!lock) {
      throw new NotFoundError(`Resource lock with ID ${id} not found`);
    }
    
    return lock;
  }

  /**
   * Check if a resource is locked
   */
  async isResourceLocked(resourceType, resourceId, environmentId = null) {
    const locks = await this.getAllResourceLocks({
      resourceType,
      resourceId,
      environmentId
    });
    
    return locks.length > 0;
  }

  /**
   * Create a new resource lock
   */
  async createResourceLock(data, userId) {
    if (!data.resourceType) {
      throw new ValidationError('Resource type is required');
    }
    
    if (!data.resourceId) {
      throw new ValidationError('Resource ID is required');
    }
    
    if (!data.lockType) {
      throw new ValidationError('Lock type is required');
    }
    
    // Check if resource is already locked
    const existingLocks = await this.getAllResourceLocks({
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      environmentId: data.environmentId
    });
    
    if (existingLocks.length > 0) {
      throw new ValidationError('Resource is already locked');
    }
    
    // If team ID is provided, check if user is a member with admin or owner role
    if (data.teamId) {
      try {
        await this.teamService.checkTeamPermission(data.teamId, userId, ['owner', 'admin']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to lock resources for this team');
      }
    }
    
    const locks = await this.loadData(this.locksFile);
    
    // Create new lock
    const newLock = {
      id: uuidv4(),
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      lockType: data.lockType,
      reason: data.reason || '',
      expiresAt: data.expiresAt || null,
      teamId: data.teamId || null,
      environmentId: data.environmentId || null,
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    locks.push(newLock);
    await this.saveData(this.locksFile, locks);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'LOCK',
      resourceType: data.resourceType,
      resourceId: data.resourceId,
      details: {
        lockType: newLock.lockType,
        reason: newLock.reason
      },
      teamId: newLock.teamId,
      environmentId: newLock.environmentId
    });
    
    return newLock;
  }

  /**
   * Update a resource lock
   */
  async updateResourceLock(id, data, userId) {
    const locks = await this.loadData(this.locksFile);
    const index = locks.findIndex(l => l.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Resource lock with ID ${id} not found`);
    }
    
    const lock = locks[index];
    
    // Check if user has permission to update
    if (lock.createdBy !== userId) {
      // If not the creator, check if user is team owner or admin
      if (lock.teamId) {
        try {
          await this.teamService.checkTeamPermission(lock.teamId, userId, ['owner', 'admin']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to update this resource lock');
        }
      } else {
        throw new AuthorizationError('You do not have permission to update this resource lock');
      }
    }
    
    // Update lock
    const updatedLock = {
      ...lock,
      ...data,
      id, // Don't allow changing the ID
      resourceType: lock.resourceType, // Don't allow changing the resource type
      resourceId: lock.resourceId, // Don't allow changing the resource ID
      createdBy: lock.createdBy, // Don't allow changing the creator
      updated: new Date().toISOString()
    };
    
    locks[index] = updatedLock;
    await this.saveData(this.locksFile, locks);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'resource_lock',
      resourceId: id,
      details: {
        resourceType: lock.resourceType,
        resourceId: lock.resourceId,
        lockType: updatedLock.lockType
      },
      teamId: lock.teamId,
      environmentId: lock.environmentId
    });
    
    return updatedLock;
  }

  /**
   * Delete a resource lock (unlock)
   */
  async deleteResourceLock(id, userId, reason = '') {
    const locks = await this.loadData(this.locksFile);
    const index = locks.findIndex(l => l.id === id);
    
    if (index === -1) {
      throw new NotFoundError(`Resource lock with ID ${id} not found`);
    }
    
    const lock = locks[index];
    
    // Check if user has permission to delete
    if (lock.createdBy !== userId) {
      // If not the creator, check if user is team owner or admin
      if (lock.teamId) {
        try {
          await this.teamService.checkTeamPermission(lock.teamId, userId, ['owner', 'admin']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to unlock this resource');
        }
      } else {
        throw new AuthorizationError('You do not have permission to unlock this resource');
      }
    }
    
    // Remove the lock
    locks.splice(index, 1);
    await this.saveData(this.locksFile, locks);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UNLOCK',
      resourceType: lock.resourceType,
      resourceId: lock.resourceId,
      details: {
        lockType: lock.lockType,
        reason
      },
      teamId: lock.teamId,
      environmentId: lock.environmentId
    });
    
    return { 
      success: true, 
      message: `Resource ${lock.resourceType}/${lock.resourceId} unlocked`,
      lock
    };
  }

  /**
   * Check if a user can modify a resource
   */
  async canModifyResource(resourceType, resourceId, userId, environmentId = null) {
    // Check if resource is locked
    const locks = await this.getAllResourceLocks({
      resourceType,
      resourceId,
      environmentId
    });
    
    if (locks.length === 0) {
      // Resource is not locked, can modify
      return { canModify: true };
    }
    
    // Check each lock
    for (const lock of locks) {
      // Check if user is the lock creator
      if (lock.createdBy === userId) {
        continue; // User can modify their own locks
      }
      
      // Check if user has an override for this lock
      const hasOverride = await this.hasLockOverride(lock.id, userId);
      if (hasOverride) {
        continue; // User has an override for this lock
      }
      
      // Check if user is team owner or admin
      if (lock.teamId) {
        try {
          await this.teamService.checkTeamPermission(lock.teamId, userId, ['owner', 'admin']);
          continue; // User is team owner or admin
        } catch (error) {
          // User is not team owner or admin
        }
      }
      
      // User cannot modify this locked resource
      return { 
        canModify: false, 
        lock,
        reason: `Resource is locked by ${lock.createdBy} with type '${lock.lockType}'`
      };
    }
    
    // User can modify all locks
    return { canModify: true };
  }

  /**
   * Create a lock override
   */
  async createLockOverride(lockId, userId, overrideUserId, reason) {
    // Get lock
    const lock = await this.getResourceLockById(lockId);
    
    // Check if user has permission to create override
    if (lock.teamId) {
      try {
        await this.teamService.checkTeamPermission(lock.teamId, userId, ['owner']);
      } catch (error) {
        throw new AuthorizationError('You do not have permission to create lock overrides for this team');
      }
    } else {
      throw new AuthorizationError('You do not have permission to create lock overrides');
    }
    
    if (!reason) {
      throw new ValidationError('Reason for override is required');
    }
    
    const overrides = await this.loadData(this.lockOverridesFile);
    
    // Check if override already exists
    const existingOverride = overrides.find(o => 
      o.lockId === lockId && o.userId === overrideUserId
    );
    
    if (existingOverride) {
      throw new ValidationError('Override already exists for this user and lock');
    }
    
    // Create new override
    const newOverride = {
      id: uuidv4(),
      lockId,
      userId: overrideUserId,
      reason,
      createdBy: userId,
      created: new Date().toISOString()
    };
    
    overrides.push(newOverride);
    await this.saveData(this.lockOverridesFile, overrides);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'OVERRIDE',
      resourceType: 'resource_lock',
      resourceId: lockId,
      details: {
        overrideUserId,
        reason
      },
      teamId: lock.teamId,
      environmentId: lock.environmentId
    });
    
    return newOverride;
  }

  /**
   * Delete a lock override
   */
  async deleteLockOverride(overrideId, userId) {
    const overrides = await this.loadData(this.lockOverridesFile);
    const index = overrides.findIndex(o => o.id === overrideId);
    
    if (index === -1) {
      throw new NotFoundError(`Lock override with ID ${overrideId} not found`);
    }
    
    const override = overrides[index];
    
    // Get lock
    const lock = await this.getResourceLockById(override.lockId);
    
    // Check if user has permission to delete override
    if (override.createdBy !== userId) {
      if (lock.teamId) {
        try {
          await this.teamService.checkTeamPermission(lock.teamId, userId, ['owner']);
        } catch (error) {
          throw new AuthorizationError('You do not have permission to delete this lock override');
        }
      } else {
        throw new AuthorizationError('You do not have permission to delete this lock override');
      }
    }
    
    // Remove the override
    overrides.splice(index, 1);
    await this.saveData(this.lockOverridesFile, overrides);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE_OVERRIDE',
      resourceType: 'resource_lock',
      resourceId: override.lockId,
      details: {
        overrideUserId: override.userId
      },
      teamId: lock.teamId,
      environmentId: lock.environmentId
    });
    
    return { success: true, message: `Lock override ${overrideId} deleted` };
  }

  /**
   * Get lock overrides for a lock
   */
  async getLockOverrides(lockId) {
    const overrides = await this.loadData(this.lockOverridesFile);
    return overrides.filter(o => o.lockId === lockId);
  }

  /**
   * Check if a user has an override for a lock
   */
  async hasLockOverride(lockId, userId) {
    const overrides = await this.loadData(this.lockOverridesFile);
    return overrides.some(o => o.lockId === lockId && o.userId === userId);
  }

  /**
   * Clean up expired locks
   */
  async cleanupExpiredLocks() {
    const locks = await this.loadData(this.locksFile);
    const now = new Date();
    
    // Filter out expired locks
    const expiredLocks = locks.filter(lock => 
      lock.expiresAt && new Date(lock.expiresAt) <= now
    );
    
    if (expiredLocks.length === 0) {
      return { success: true, message: 'No expired locks found' };
    }
    
    // Remove expired locks
    const updatedLocks = locks.filter(lock => 
      !lock.expiresAt || new Date(lock.expiresAt) > now
    );
    
    await this.saveData(this.locksFile, updatedLocks);
    
    // Log audit events for each expired lock
    for (const lock of expiredLocks) {
      await this.auditService.logEvent({
        userId: 'system',
        action: 'UNLOCK',
        resourceType: lock.resourceType,
        resourceId: lock.resourceId,
        details: {
          lockType: lock.lockType,
          reason: 'Lock expired'
        },
        teamId: lock.teamId,
        environmentId: lock.environmentId
      });
    }
    
    return { 
      success: true, 
      message: `${expiredLocks.length} expired locks removed`,
      expiredLocks
    };
  }
}

module.exports = ResourceLockService;
