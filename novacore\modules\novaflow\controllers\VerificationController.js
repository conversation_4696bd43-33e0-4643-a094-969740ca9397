/**
 * NovaCore Verification Controller
 * 
 * This controller handles API requests related to verification checkpoints.
 * It implements the "Compliance Verification Checkpoints" patent concept.
 */

const { engines } = require('../index');
const { WorkflowService } = require('../services');
const logger = require('../../../config/logger');

const { 
  VerificationCheckpointEngine, 
  VerificationRuleEngine, 
  EvidenceVerificationService 
} = engines.verification;

class VerificationController {
  /**
   * Create verification checkpoint
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createCheckpoint(req, res, next) {
    try {
      const { workflowId, stageId } = req.params;
      
      // Get workflow
      const workflow = await WorkflowService.getWorkflowById(workflowId);
      
      // Find stage
      const stage = workflow.stages.find(s => s.id === stageId);
      
      if (!stage) {
        return res.status(404).json({
          success: false,
          message: `Stage with ID ${stageId} not found in workflow ${workflowId}`
        });
      }
      
      // Create checkpoint
      const checkpoint = VerificationCheckpointEngine.createCheckpoint(workflow, stage, req.body);
      
      res.status(201).json({
        success: true,
        data: checkpoint
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Process verification checkpoint
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async processCheckpoint(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { executionId, checkpointId } = req.params;
      
      // Get execution
      const execution = await WorkflowService.getExecutionById(executionId);
      
      // Get checkpoint
      const checkpoint = VerificationCheckpointEngine.getCheckpoint(checkpointId, execution);
      
      if (!checkpoint) {
        return res.status(404).json({
          success: false,
          message: `Checkpoint with ID ${checkpointId} not found in execution ${executionId}`
        });
      }
      
      // Process checkpoint
      const result = await VerificationCheckpointEngine.processCheckpoint(
        checkpoint, 
        execution, 
        { 
          userId,
          ...req.body
        }
      );
      
      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get checkpoints for stage
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getCheckpointsForStage(req, res, next) {
    try {
      const { executionId, stageId } = req.params;
      
      // Get execution
      const execution = await WorkflowService.getExecutionById(executionId);
      
      // Get checkpoints for stage
      const checkpoints = VerificationCheckpointEngine.getCheckpointsForStage(stageId, execution);
      
      res.status(200).json({
        success: true,
        data: checkpoints
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Create verification rule
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createRule(req, res, next) {
    try {
      const { type } = req.body;
      
      if (!type) {
        return res.status(400).json({
          success: false,
          message: 'Rule type is required'
        });
      }
      
      // Create rule
      const rule = VerificationCheckpointEngine.createRule(type, req.body);
      
      res.status(201).json({
        success: true,
        data: rule
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Verify evidence
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async verifyEvidence(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const { evidenceId } = req.params;
      
      // Verify evidence
      const verificationRecord = await EvidenceVerificationService.verifyEvidence(
        evidenceId, 
        req.body, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: verificationRecord
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get verification record
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVerificationRecord(req, res, next) {
    try {
      const { verificationId } = req.params;
      
      // Get verification record
      const verificationRecord = await EvidenceVerificationService.getVerificationById(verificationId);
      
      res.status(200).json({
        success: true,
        data: verificationRecord
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get verifications for evidence
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getVerificationsForEvidence(req, res, next) {
    try {
      const { evidenceId } = req.params;
      
      // Get verifications for evidence
      const verificationRecords = await EvidenceVerificationService.getVerificationsForEvidence(evidenceId);
      
      res.status(200).json({
        success: true,
        data: verificationRecords
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new VerificationController();
