<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FIG E2: 3-6-9-12-16 Alignment Architecture</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .fig-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .description {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .patent-info {
            background: #f8f9fa;
            padding: 20px;
            border-left: 5px solid #007bff;
            margin: 20px;
        }
        
        .patent-info h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        
        .diagram-container {
            padding: 40px;
            text-align: center;
            background: #fafafa;
        }
        
        .mermaid {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 100%;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .inventor-info {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="fig-number">FIG E2</div>
            <div class="title">3-6-9-12-16 Alignment Architecture (Mermaid)</div>
            <div class="description">
                Mathematical alignment progression from core triad to complete system implementation with consciousness-aware processing.
            </div>
        </div>
        
        <div class="patent-info">
            <h3>📋 Patent Information</h3>
            <p><strong>Claims:</strong> 29, 31 (Alignment Theory)</p>
            <p><strong>Reference Numbers:</strong> 300-340</p>
            <p><strong>Innovation Focus:</strong> AI Alignment and Consciousness Progression</p>
            <p><strong>Source File:</strong> alignment_architecture.mmd</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph TD
    %% Alignment Architecture
    A3[3: Core Triad] --> A6[6: Connection Matrix]
    A6 --> A9[9: Intelligence Grid]
    A9 --> A12[12: Universal Framework]
    A12 --> A16[16: Complete System]
    
    %% USPTO Compliance
    classDef uspto fill:#fff,stroke:#000,stroke-width:2px
    class A3,A6,A9,A12,A16 uspto
    
    %% Reference Numbers (300 series)
    A3:::reference300
    A6:::reference310
    A9:::reference320
    A12:::reference330
    A16:::reference340
    
    %% Mathematical Annotations
    Math1[3: Core Principles]:::math
    Math2[6: Connection Pairs]:::math
    Math3[9: Intelligence Matrix]:::math
    Math4[12: Framework Elements]:::math
    Math5[16: System Components]:::math
    
    %% Legend
    Legend[3-6-9-12-16 Alignment Architecture]:::legend
    
    %% Styling
    classDef reference300,reference310,reference320,reference330,reference340 fill:none,stroke:none,font-size:8pt
    classDef math fill:#f9f9f9,stroke:#ddd,stroke-dasharray: 5 5,font-size:8pt
    classDef legend fill:#f0f0f0,stroke:#ccc,stroke-width:1px
            </div>
        </div>
        
        <div class="footer">
            <div class="inventor-info">
                <strong>Inventor:</strong> David Nigel Irvin | <strong>Company:</strong> NovaFuse Technologies<br>
                Patent Title: Comphyology Universal Unified Field Theory Implementation System
            </div>
        </div>
    </div>
    
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
