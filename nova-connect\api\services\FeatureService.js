/**
 * NovaConnect UAC Feature Service
 * 
 * This service handles feature toggling based on customer entitlements.
 */

const fs = require('fs').promises;
const path = require('path');
const logger = require('../../config/logger');

class FeatureService {
  constructor() {
    this.dataDir = path.join(__dirname, '../../data/features');
    this.featuresFile = path.join(this.dataDir, 'features.json');
    this.customerFeaturesFile = path.join(this.dataDir, 'customer_features.json');
    
    // Define tier features
    this.tierFeatures = {
      core: [
        'basic_api_access',
        'standard_connectors',
        'basic_normalization',
        'basic_monitoring'
      ],
      secure: [
        'basic_api_access',
        'standard_connectors',
        'advanced_connectors',
        'basic_normalization',
        'advanced_normalization',
        'basic_monitoring',
        'advanced_monitoring',
        'basic_security'
      ],
      enterprise: [
        'basic_api_access',
        'standard_connectors',
        'advanced_connectors',
        'enterprise_connectors',
        'basic_normalization',
        'advanced_normalization',
        'enterprise_normalization',
        'basic_monitoring',
        'advanced_monitoring',
        'enterprise_monitoring',
        'basic_security',
        'advanced_security'
      ],
      ai_boost: [
        'basic_api_access',
        'standard_connectors',
        'advanced_connectors',
        'enterprise_connectors',
        'ai_connectors',
        'basic_normalization',
        'advanced_normalization',
        'enterprise_normalization',
        'ai_normalization',
        'basic_monitoring',
        'advanced_monitoring',
        'enterprise_monitoring',
        'ai_monitoring',
        'basic_security',
        'advanced_security',
        'ai_security'
      ]
    };
    
    this.ensureDataDir();
  }
  
  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
      
      // Initialize features file if it doesn't exist
      try {
        await fs.access(this.featuresFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.featuresFile, JSON.stringify(this.tierFeatures));
        } else {
          throw error;
        }
      }
      
      // Initialize customer features file if it doesn't exist
      try {
        await fs.access(this.customerFeaturesFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.customerFeaturesFile, JSON.stringify({}));
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error ensuring feature data directory', { error: error.message });
      throw error;
    }
  }
  
  /**
   * Load features from file
   */
  async loadFeatures() {
    try {
      const data = await fs.readFile(this.featuresFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading features', { error: error.message });
      return this.tierFeatures;
    }
  }
  
  /**
   * Load customer features from file
   */
  async loadCustomerFeatures() {
    try {
      const data = await fs.readFile(this.customerFeaturesFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error('Error loading customer features', { error: error.message });
      return {};
    }
  }
  
  /**
   * Save customer features to file
   */
  async saveCustomerFeatures(customerFeatures) {
    try {
      await fs.writeFile(this.customerFeaturesFile, JSON.stringify(customerFeatures, null, 2));
    } catch (error) {
      logger.error('Error saving customer features', { error: error.message });
      throw error;
    }
  }
  
  /**
   * Enable features for a customer based on tier
   */
  async enableFeaturesForTier(customerId, tier) {
    try {
      logger.info('Enabling features for tier', { customerId, tier });
      
      // Load features
      const features = await this.loadFeatures();
      
      // Get features for tier
      const tierFeatures = features[tier] || this.tierFeatures[tier] || [];
      
      if (tierFeatures.length === 0) {
        logger.warn('No features found for tier', { tier });
      }
      
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();
      
      // Update customer features
      customerFeatures[customerId] = {
        tier,
        features: tierFeatures,
        status: 'ACTIVE',
        updatedAt: new Date().toISOString()
      };
      
      // Save customer features
      await this.saveCustomerFeatures(customerFeatures);
      
      logger.info('Features enabled for tier', { customerId, tier, featureCount: tierFeatures.length });
      
      return tierFeatures;
    } catch (error) {
      logger.error('Error enabling features for tier', { error: error.message, customerId, tier });
      throw error;
    }
  }
  
  /**
   * Disable features for a customer
   */
  async disableFeatures(customerId) {
    try {
      logger.info('Disabling features', { customerId });
      
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();
      
      // Check if customer has features
      if (!customerFeatures[customerId]) {
        logger.warn('No features found for customer', { customerId });
        return;
      }
      
      // Update customer features
      customerFeatures[customerId] = {
        ...customerFeatures[customerId],
        status: 'DISABLED',
        updatedAt: new Date().toISOString(),
        disabledAt: new Date().toISOString()
      };
      
      // Save customer features
      await this.saveCustomerFeatures(customerFeatures);
      
      logger.info('Features disabled', { customerId });
    } catch (error) {
      logger.error('Error disabling features', { error: error.message, customerId });
      throw error;
    }
  }
  
  /**
   * Suspend features for a customer
   */
  async suspendFeatures(customerId) {
    try {
      logger.info('Suspending features', { customerId });
      
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();
      
      // Check if customer has features
      if (!customerFeatures[customerId]) {
        logger.warn('No features found for customer', { customerId });
        return;
      }
      
      // Update customer features
      customerFeatures[customerId] = {
        ...customerFeatures[customerId],
        status: 'SUSPENDED',
        updatedAt: new Date().toISOString(),
        suspendedAt: new Date().toISOString()
      };
      
      // Save customer features
      await this.saveCustomerFeatures(customerFeatures);
      
      logger.info('Features suspended', { customerId });
    } catch (error) {
      logger.error('Error suspending features', { error: error.message, customerId });
      throw error;
    }
  }
  
  /**
   * Check if a feature is enabled for a customer
   */
  async isFeatureEnabled(customerId, featureName) {
    try {
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();
      
      // Check if customer has features
      if (!customerFeatures[customerId]) {
        logger.debug('No features found for customer', { customerId });
        return false;
      }
      
      // Check if customer features are active
      if (customerFeatures[customerId].status !== 'ACTIVE') {
        logger.debug('Customer features are not active', { customerId, status: customerFeatures[customerId].status });
        return false;
      }
      
      // Check if feature is enabled
      const isEnabled = customerFeatures[customerId].features.includes(featureName);
      
      return isEnabled;
    } catch (error) {
      logger.error('Error checking if feature is enabled', { error: error.message, customerId, featureName });
      return false;
    }
  }
  
  /**
   * Get customer features
   */
  async getCustomerFeatures(customerId) {
    try {
      // Load customer features
      const customerFeatures = await this.loadCustomerFeatures();
      
      // Return customer features or empty object
      return customerFeatures[customerId] || { status: 'NOT_FOUND' };
    } catch (error) {
      logger.error('Error getting customer features', { error: error.message, customerId });
      throw error;
    }
  }
}

module.exports = FeatureService;
