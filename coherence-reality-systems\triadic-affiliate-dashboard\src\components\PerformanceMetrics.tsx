import { motion } from 'framer-motion'
import { Chart, ChartBar, ChartLine } from '@/components/ui/Chart'

interface PerformanceMetricsProps {
  stats: {
    conversionRate: number
    monthlySales: number
    totalRevenue: number
  }
  products: any[]
}

export function PerformanceMetrics({ stats, products }: PerformanceMetricsProps) {
  const metrics = [
    {
      label: 'Conversion Rate',
      value: `${(stats.conversionRate * 100).toFixed(1)}%`,
      icon: '📈',
      color: 'text-green-500'
    },
    {
      label: 'Monthly Sales',
      value: stats.monthlySales.toLocaleString(),
      icon: '📦',
      color: 'text-blue-500'
    },
    {
      label: 'Total Revenue',
      value: `$${stats.totalRevenue.toLocaleString()}`,
      icon: '💰',
      color: 'text-yellow-500'
    }
  ]

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {metrics.map((metric) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 bg-white/5 backdrop-blur-lg rounded-lg border border-white/10"
          >
            <div className="flex items-center">
              <span className={`text-2xl mr-2 ${metric.color}`}>{metric.icon}</span>
              <div>
                <p className="text-sm text-gray-400">{metric.label}</p>
                <p className="text-xl font-bold">{metric.value}</p>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {products.length > 0 && (
        <div className="bg-white/5 backdrop-blur-lg rounded-lg border border-white/10 p-4">
          <h3 className="text-lg font-semibold mb-4">Recent Products</h3>
          <div className="space-y-2">
            {products.slice(0, 5).map((product, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-gray-300">{product.name || 'Product'}</span>
                <span className="text-sm text-green-400">${(product.price || 0).toFixed(2)}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
