/**
 * Simple test script for the error handler middleware
 */

try {
  console.log('Loading error handler middleware...');
  const { errorHandler, notFoundHandler } = require('./src/middleware/error-handler');
  const { 
    ValidationError,
    AuthenticationError,
    ResourceNotFoundError,
    ServerError
  } = require('./src/errors');

  console.log('Testing error handler middleware...');

  // Mock response object
  const mockResponse = () => {
    const res = {};
    res.status = (code) => {
      console.log(`Response status set to: ${code}`);
      res.statusCode = code;
      return res;
    };
    res.json = (data) => {
      console.log(`Response data: ${JSON.stringify(data, null, 2)}`);
      res.data = data;
      return res;
    };
    return res;
  };

  // Mock request object
  const mockRequest = () => ({
    originalUrl: '/test',
    method: 'GET'
  });

  // Mock next function
  const mockNext = (error) => {
    console.log(`Next called with error: ${error ? error.message : 'none'}`);
  };

  // Test ValidationError
  console.log('\n--- Testing ValidationError handling ---');
  const validationError = new ValidationError('Validation failed');
  const req1 = mockRequest();
  const res1 = mockResponse();
  
  errorHandler(validationError, req1, res1, mockNext);
  console.log(`Status code: ${res1.statusCode}`);
  console.log(`Error code: ${res1.data.error.code}`);

  // Test AuthenticationError
  console.log('\n--- Testing AuthenticationError handling ---');
  const authError = new AuthenticationError('Authentication failed');
  const req2 = mockRequest();
  const res2 = mockResponse();
  
  errorHandler(authError, req2, res2, mockNext);
  console.log(`Status code: ${res2.statusCode}`);
  console.log(`Error code: ${res2.data.error.code}`);

  // Test ResourceNotFoundError
  console.log('\n--- Testing ResourceNotFoundError handling ---');
  const notFoundError = new ResourceNotFoundError('User', '123');
  const req3 = mockRequest();
  const res3 = mockResponse();
  
  errorHandler(notFoundError, req3, res3, mockNext);
  console.log(`Status code: ${res3.statusCode}`);
  console.log(`Error code: ${res3.data.error.code}`);

  // Test ServerError
  console.log('\n--- Testing ServerError handling ---');
  const serverError = new ServerError('Server error');
  const req4 = mockRequest();
  const res4 = mockResponse();
  
  errorHandler(serverError, req4, res4, mockNext);
  console.log(`Status code: ${res4.statusCode}`);
  console.log(`Error code: ${res4.data.error.code}`);

  // Test notFoundHandler
  console.log('\n--- Testing notFoundHandler ---');
  const req5 = mockRequest();
  const res5 = mockResponse();
  
  notFoundHandler(req5, res5, (error) => {
    console.log(`notFoundHandler called next with error: ${error.name}`);
    console.log(`Error message: ${error.message}`);
    console.log(`Resource type: ${error.resourceType}`);
    console.log(`Resource ID: ${error.resourceId}`);
  });

  console.log('\nAll middleware tests passed!');
} catch (error) {
  console.error('Test failed:', error);
}
