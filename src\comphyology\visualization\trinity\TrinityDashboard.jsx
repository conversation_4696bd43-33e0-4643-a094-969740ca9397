import React, { useState, useEffect } from 'react';
import TrinityVisualization from './TrinityVisualization';
import './TrinityDashboard.css';

/**
 * Trinity Dashboard Component
 * 
 * This component provides a dashboard interface for the Trinity Visualization,
 * including controls and real-time data displays.
 */
const TrinityDashboard = ({ universalRippleStack }) => {
  const [visualizationData, setVisualizationData] = useState({
    rippleEffect: {
      directImpact: 0.5,
      adjacentResonance: 0.5,
      fieldSaturation: 0.5
    },
    mathematicalConstants: {
      pi: 3.14159,
      phi: 0.618033988749895,
      e: 2.71828
    },
    implementationPatterns: {
      quantumStateVectors: 0.5,
      resonancePatterns: 0.5,
      fieldMatrices: 0.5
    },
    trinityBalance: 0.5,
    systemHealth: 0.8,
    activeConnections: 0,
    predictionConfidence: 0.6
  });
  
  const [selectedWheel, setSelectedWheel] = useState('outer');
  const [isAutoRotate, setIsAutoRotate] = useState(true);
  const [showDetails, setShowDetails] = useState(false);
  
  // Simulate real-time data updates
  useEffect(() => {
    const updateInterval = setInterval(() => {
      setVisualizationData(prevData => ({
        ...prevData,
        rippleEffect: {
          directImpact: Math.min(1, Math.max(0.3, prevData.rippleEffect.directImpact + (Math.random() - 0.5) * 0.1)),
          adjacentResonance: Math.min(1, Math.max(0.3, prevData.rippleEffect.adjacentResonance + (Math.random() - 0.5) * 0.1)),
          fieldSaturation: Math.min(1, Math.max(0.3, prevData.rippleEffect.fieldSaturation + (Math.random() - 0.5) * 0.1))
        },
        implementationPatterns: {
          quantumStateVectors: Math.min(1, Math.max(0.3, prevData.implementationPatterns.quantumStateVectors + (Math.random() - 0.5) * 0.1)),
          resonancePatterns: Math.min(1, Math.max(0.3, prevData.implementationPatterns.resonancePatterns + (Math.random() - 0.5) * 0.1)),
          fieldMatrices: Math.min(1, Math.max(0.3, prevData.implementationPatterns.fieldMatrices + (Math.random() - 0.5) * 0.1))
        },
        trinityBalance: Math.min(1, Math.max(0.3, prevData.trinityBalance + (Math.random() - 0.5) * 0.05)),
        systemHealth: Math.min(1, Math.max(0.5, prevData.systemHealth + (Math.random() - 0.5) * 0.03)),
        activeConnections: Math.floor(Math.max(0, prevData.activeConnections + (Math.random() > 0.7 ? 1 : -1))),
        predictionConfidence: Math.min(1, Math.max(0.3, prevData.predictionConfidence + (Math.random() - 0.5) * 0.08))
      }));
    }, 1000);
    
    return () => clearInterval(updateInterval);
  }, []);
  
  // If we have a real UniversalRippleStack instance, use its data
  useEffect(() => {
    if (!universalRippleStack) return;
    
    const updateFromRippleStack = () => {
      try {
        const metrics = universalRippleStack.getMetrics();
        
        // Map metrics to visualization data
        setVisualizationData(prevData => ({
          ...prevData,
          rippleEffect: {
            directImpact: metrics.rippleEffect.layer1 ? 0.8 : 0.2,
            adjacentResonance: metrics.rippleEffect.layer2 ? 0.8 : 0.2,
            fieldSaturation: metrics.rippleEffect.layer3 ? 0.8 : 0.2
          },
          activeConnections: metrics.quantum.resonance.connectionCount || 0,
          systemHealth: metrics.components.novaConnect && metrics.components.novaThink ? 0.9 : 0.5,
          predictionConfidence: metrics.quantum.engine.stateCount > 0 ? 0.7 : 0.3
        }));
      } catch (error) {
        console.error('Error getting metrics from UniversalRippleStack:', error);
      }
    };
    
    // Update immediately
    updateFromRippleStack();
    
    // Set up interval for updates
    const updateInterval = setInterval(updateFromRippleStack, 2000);
    
    return () => clearInterval(updateInterval);
  }, [universalRippleStack]);
  
  // Format number with fixed precision
  const formatNumber = (num, precision = 2) => {
    return num.toFixed(precision);
  };
  
  // Convert value to percentage
  const toPercentage = (value) => {
    return `${Math.round(value * 100)}%`;
  };
  
  return (
    <div className="trinity-dashboard">
      <div className="trinity-dashboard-header">
        <h1>NovaFuse Trinity Visualization</h1>
        <p>3-in-1 Nested Trinities - Wheels Within Wheels</p>
      </div>
      
      <div className="trinity-dashboard-content">
        <div className="trinity-visualization-wrapper">
          <TrinityVisualization data={visualizationData} />
        </div>
        
        <div className="trinity-dashboard-controls">
          <div className="control-section">
            <h3>Wheel Selection</h3>
            <div className="wheel-selector">
              <button 
                className={selectedWheel === 'outer' ? 'active' : ''} 
                onClick={() => setSelectedWheel('outer')}
              >
                Outer Wheel
              </button>
              <button 
                className={selectedWheel === 'middle' ? 'active' : ''} 
                onClick={() => setSelectedWheel('middle')}
              >
                Middle Wheel
              </button>
              <button 
                className={selectedWheel === 'inner' ? 'active' : ''} 
                onClick={() => setSelectedWheel('inner')}
              >
                Inner Wheel
              </button>
            </div>
          </div>
          
          <div className="control-section">
            <h3>Visualization Controls</h3>
            <div className="control-options">
              <label className="control-toggle">
                <input 
                  type="checkbox" 
                  checked={isAutoRotate} 
                  onChange={() => setIsAutoRotate(!isAutoRotate)} 
                />
                Auto Rotate
              </label>
              <label className="control-toggle">
                <input 
                  type="checkbox" 
                  checked={showDetails} 
                  onChange={() => setShowDetails(!showDetails)} 
                />
                Show Details
              </label>
            </div>
          </div>
          
          <div className="control-section">
            <h3>System Metrics</h3>
            <div className="metrics-grid">
              <div className="metric-item">
                <div className="metric-label">Trinity Balance</div>
                <div className="metric-value">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ width: toPercentage(visualizationData.trinityBalance) }}
                    ></div>
                  </div>
                  <div className="metric-number">{toPercentage(visualizationData.trinityBalance)}</div>
                </div>
              </div>
              
              <div className="metric-item">
                <div className="metric-label">System Health</div>
                <div className="metric-value">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ 
                        width: toPercentage(visualizationData.systemHealth),
                        backgroundColor: visualizationData.systemHealth > 0.7 ? '#4CAF50' : '#FF9800'
                      }}
                    ></div>
                  </div>
                  <div className="metric-number">{toPercentage(visualizationData.systemHealth)}</div>
                </div>
              </div>
              
              <div className="metric-item">
                <div className="metric-label">Active Connections</div>
                <div className="metric-value">
                  <div className="metric-number">{visualizationData.activeConnections}</div>
                </div>
              </div>
              
              <div className="metric-item">
                <div className="metric-label">Prediction Confidence</div>
                <div className="metric-value">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{ 
                        width: toPercentage(visualizationData.predictionConfidence),
                        backgroundColor: visualizationData.predictionConfidence > 0.618 ? '#4CAF50' : '#FF9800'
                      }}
                    ></div>
                  </div>
                  <div className="metric-number">{toPercentage(visualizationData.predictionConfidence)}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {showDetails && (
        <div className="trinity-dashboard-details">
          <div className="details-section">
            <h3>Ripple Effect Layers (Outer Wheel)</h3>
            <div className="details-grid">
              <div className="detail-item">
                <div className="detail-label">Direct Impact</div>
                <div className="detail-value">{toPercentage(visualizationData.rippleEffect.directImpact)}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">Adjacent Resonance</div>
                <div className="detail-value">{toPercentage(visualizationData.rippleEffect.adjacentResonance)}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">Field Saturation</div>
                <div className="detail-value">{toPercentage(visualizationData.rippleEffect.fieldSaturation)}</div>
              </div>
            </div>
          </div>
          
          <div className="details-section">
            <h3>Mathematical Constants (Middle Wheel)</h3>
            <div className="details-grid">
              <div className="detail-item">
                <div className="detail-label">π (Pi) - Governance</div>
                <div className="detail-value">{formatNumber(visualizationData.mathematicalConstants.pi, 5)}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">φ (Phi) - Detection</div>
                <div className="detail-value">{formatNumber(visualizationData.mathematicalConstants.phi, 5)}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">e - Response</div>
                <div className="detail-value">{formatNumber(visualizationData.mathematicalConstants.e, 5)}</div>
              </div>
            </div>
          </div>
          
          <div className="details-section">
            <h3>Implementation Patterns (Inner Wheel)</h3>
            <div className="details-grid">
              <div className="detail-item">
                <div className="detail-label">Quantum State Vectors</div>
                <div className="detail-value">{toPercentage(visualizationData.implementationPatterns.quantumStateVectors)}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">Resonance Patterns</div>
                <div className="detail-value">{toPercentage(visualizationData.implementationPatterns.resonancePatterns)}</div>
              </div>
              <div className="detail-item">
                <div className="detail-label">Field Matrices</div>
                <div className="detail-value">{toPercentage(visualizationData.implementationPatterns.fieldMatrices)}</div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="trinity-dashboard-footer">
        <p>NovaFuse Universal Ripple Stack - Powered by Comphyology (Ψᶜ)</p>
      </div>
    </div>
  );
};

export default TrinityDashboard;
