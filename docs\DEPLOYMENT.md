# NovaConnect UAC Deployment Guide

This guide provides instructions for deploying NovaConnect UAC to Google Cloud Platform (GCP) and Google Cloud Marketplace.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development](#local-development)
3. [Docker Deployment](#docker-deployment)
4. [Kubernetes Deployment](#kubernetes-deployment)
5. [Google Cloud Marketplace Deployment](#google-cloud-marketplace-deployment)
6. [Continuous Integration and Deployment](#continuous-integration-and-deployment)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying NovaConnect UAC, ensure you have the following:

- [Node.js](https://nodejs.org/) 16 or higher
- [Docker](https://www.docker.com/)
- [kubectl](https://kubernetes.io/docs/tasks/tools/install-kubectl/)
- [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
- A Google Cloud Platform account with billing enabled
- A Google Cloud project with the following APIs enabled:
  - Container Registry API
  - Kubernetes Engine API
  - Cloud Build API
  - Cloud Monitoring API
  - Cloud Logging API
  - Cloud Trace API
  - Cloud Profiler API
  - Cloud Debugger API
  - Secret Manager API

## Local Development

To set up NovaConnect UAC for local development:

1. Clone the repository:

```bash
git clone https://github.com/novafuse/novafuse-uac.git
cd novafuse-uac
```

2. Install dependencies:

```bash
npm install
```

3. Create a `.env` file with the following environment variables:

```
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/novafuse
REDIS_URI=redis://localhost:6379
API_KEY=your-api-key
JWT_SECRET=your-jwt-secret
CLUSTER_ENABLED=false
LOG_LEVEL=debug
MONITORING_ENABLED=false
TRACING_ENABLED=false
```

4. Start MongoDB and Redis:

```bash
docker-compose up -d mongodb redis
```

5. Start the application:

```bash
npm run dev
```

The application will be available at http://localhost:3001.

## Docker Deployment

To deploy NovaConnect UAC using Docker:

1. Build the Docker image:

```bash
docker build -t novafuse/novafuse-uac:latest .
```

2. Run the Docker container:

```bash
docker run -p 3001:3001 \
  -e MONGODB_URI=mongodb://mongodb:27017/novafuse \
  -e REDIS_URI=redis://redis:6379 \
  -e API_KEY=your-api-key \
  -e JWT_SECRET=your-jwt-secret \
  -e CLUSTER_ENABLED=true \
  -e LOG_LEVEL=info \
  -e MONITORING_ENABLED=true \
  -e TRACING_ENABLED=true \
  novafuse/novafuse-uac:latest
```

## Kubernetes Deployment

To deploy NovaConnect UAC to Kubernetes:

1. Create a namespace:

```bash
kubectl create namespace novafuse
```

2. Create a secret for sensitive information:

```bash
kubectl create secret generic novafuse-uac-secrets \
  --namespace=novafuse \
  --from-literal=mongodb-uri=mongodb://mongodb:27017/novafuse \
  --from-literal=redis-uri=redis://redis:6379 \
  --from-literal=api-key=your-api-key \
  --from-literal=jwt-secret=your-jwt-secret \
  --from-literal=gcp-project-id=your-gcp-project-id
```

3. Create a secret for Google Cloud credentials:

```bash
kubectl create secret generic novafuse-uac-gcp-key \
  --namespace=novafuse \
  --from-file=key.json=/path/to/your/service-account-key.json
```

4. Deploy NovaConnect UAC:

```bash
kubectl apply -f k8s/production/deployment.yaml
```

5. Verify the deployment:

```bash
kubectl get pods --namespace=novafuse
```

## Google Cloud Marketplace Deployment

To deploy NovaConnect UAC from Google Cloud Marketplace:

1. Go to the [Google Cloud Marketplace](https://console.cloud.google.com/marketplace).
2. Search for "NovaConnect UAC".
3. Click on the NovaConnect UAC listing.
4. Click "Configure".
5. Fill in the required fields:
   - Namespace: The Kubernetes namespace to deploy to
   - MongoDB URI: The URI for connecting to MongoDB
   - Redis URI: The URI for connecting to Redis
   - API Key: The API key for accessing the NovaConnect UAC API
   - JWT Secret: The secret for signing JWT tokens
6. Click "Deploy".

## Continuous Integration and Deployment

NovaConnect UAC uses GitHub Actions for continuous integration and deployment. The workflow is defined in `.github/workflows/ci-cd.yml`.

The workflow includes the following jobs:

1. **Test**: Runs linting and tests
2. **Build**: Builds and pushes the Docker image to Google Container Registry
3. **Deploy to Staging**: Deploys the application to the staging environment
4. **Deploy to Production**: Deploys the application to the production environment
5. **Marketplace Submission**: Submits the application to Google Cloud Marketplace

To set up continuous integration and deployment:

1. Create a Google Cloud service account with the following roles:
   - Container Registry Service Agent
   - Kubernetes Engine Admin
   - Storage Admin
   - Cloud Build Service Account
   - Secret Manager Admin
   - Marketplace Admin

2. Create a JSON key for the service account.

3. Add the following secrets to your GitHub repository:
   - `GCP_PROJECT_ID`: Your Google Cloud project ID
   - `GCP_SA_KEY`: The JSON key for the service account

4. Push to the `develop` branch to deploy to staging or the `main` branch to deploy to production.

## Monitoring and Logging

NovaConnect UAC integrates with Google Cloud Operations for monitoring and logging:

- **Metrics**: NovaConnect UAC exposes metrics at the `/metrics` endpoint in Prometheus format. These metrics are collected by Google Cloud Monitoring.
- **Logs**: NovaConnect UAC logs are sent to Google Cloud Logging.
- **Traces**: NovaConnect UAC traces are sent to Google Cloud Trace.
- **Profiling**: NovaConnect UAC profiles are sent to Google Cloud Profiler.
- **Debugging**: NovaConnect UAC debugging information is sent to Google Cloud Debugger.

To view monitoring and logging information:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/).
2. Navigate to **Monitoring** > **Dashboards**.
3. Select the "NovaConnect UAC" dashboard.

## Troubleshooting

### Common Issues

#### Application fails to start

Check the logs for errors:

```bash
kubectl logs deployment/novafuse-uac --namespace=novafuse
```

#### Cannot connect to MongoDB or Redis

Check if MongoDB and Redis are running:

```bash
kubectl get pods --namespace=novafuse
```

Check if the MongoDB and Redis URIs are correct:

```bash
kubectl get secret novafuse-uac-secrets --namespace=novafuse -o jsonpath='{.data.mongodb-uri}' | base64 --decode
kubectl get secret novafuse-uac-secrets --namespace=novafuse -o jsonpath='{.data.redis-uri}' | base64 --decode
```

#### Health check fails

Check if the application is running:

```bash
kubectl get pods --namespace=novafuse
```

Check the logs for errors:

```bash
kubectl logs deployment/novafuse-uac --namespace=novafuse
```

#### Cannot access the API

Check if the service is running:

```bash
kubectl get service novafuse-uac --namespace=novafuse
```

Check if the ingress is configured correctly:

```bash
kubectl get ingress novafuse-uac --namespace=novafuse
```

### Getting Help

If you need assistance, please contact [<EMAIL>](mailto:<EMAIL>) or visit [https://novafuse.io/support](https://novafuse.io/support).
