# NovaFuse Universal UI Connector (UUIC) Architecture Diagrams

This directory contains architecture diagrams for the NovaFuse Universal UI Connector (UUIC). These diagrams illustrate the flow between the 4 layers of the UUIC architecture.

## Overview

The UUIC is built on a 4-tier architecture:

1. **Dynamic Compliance-Aware UI Rendering** (React) - UI morphs based on user's physical location and role
2. **Real-Time Regulation Switching** (Node.js) - Zero-reboot updates - switches compliance regimes without refresh
3. **AI-Powered Interface Optimization** (TensorFlow) - UI learns from user behavior and adapts
4. **Cross-Platform Consistency Enforcement** (WASM) - Ensures UI consistency across platforms and regulatory environments

## Diagrams

- [Architecture Overview](./architecture-overview.md) - High-level overview of the UUIC architecture
- [Layer 1: Dynamic Compliance-Aware UI Rendering](./layer1-dynamic-rendering.md) - Details of the React layer
- [Layer 2: Real-Time Regulation Switching](./layer2-regulation-switching.md) - Details of the Node.js layer
- [Layer 3: AI-Powered Interface Optimization](./layer3-ai-optimization.md) - Details of the TensorFlow layer
- [Layer 4: Cross-Platform Consistency Enforcement](./layer4-consistency-enforcement.md) - Details of the WASM layer

## Viewing the Diagrams

These diagrams are written in Mermaid, a markdown-based diagramming language. To view them:

1. Open the markdown files in a Mermaid-compatible viewer (GitHub, VS Code with Mermaid extension, etc.)
2. Alternatively, copy the Mermaid code and paste it into the [Mermaid Live Editor](https://mermaid.live/)

## Key Flows

### Regulation Switching Flow

1. User changes jurisdiction (e.g., travels to a new country)
2. UI requests jurisdiction change from Regulation Orchestrator
3. Regulation Orchestrator unloads current UI modules
4. Regulation Orchestrator fetches new regulatory schema
5. Regulation Orchestrator broadcasts UI update
6. UI re-renders with new regulations

### AI Optimization Flow

1. User interacts with UI
2. UI sends behavior data to UI Optimizer
3. UI Optimizer analyzes behavior data
4. UI Optimizer generates optimization parameters
5. UI applies optimizations

### Consistency Enforcement Flow

1. UI requests validation from Consistency Enforcer
2. Consistency Enforcer validates components against regulatory profile
3. Consistency Enforcer returns validation results
4. If issues are found, UI requests fixes
5. Consistency Enforcer applies fixes
6. UI applies fixed components
