"""
Factory for creating and managing quantum backends.

This module provides a factory class for creating and managing
instances of different quantum computing backends.
"""

from typing import Dict, Type, Any, Optional, List
import importlib
import logging
from pathlib import Path

from . import QuantumBackend

logger = logging.getLogger(__name__)

# Built-in backends
BUILTIN_BACKENDS = {
    'qiskit': 'src.quantum.qiskit_backend.QiskitBackend',
    'cirq': 'src.quantum.cirq_backend.CirqBackend',
}

class QuantumBackendFactory:
    """Factory for creating and managing quantum backends."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the backend factory.
        
        Args:
            config: Configuration dictionary with the following optional keys:
                - default_backend: Name of the default backend to use
                - backends: Dictionary of backend configurations
        """
        self.config = config or {}
        self._backends: Dict[str, QuantumBackend] = {}
        self._discover_backends()
    
    def _discover_backends(self) -> None:
        """Discover and register available backends."""
        self._available_backends = {}
        
        # Register built-in backends
        for name, class_path in BUILTIN_BACKENDS.items():
            try:
                module_name, class_name = class_path.rsplit('.', 1)
                module = importlib.import_module(module_name)
                backend_class = getattr(module, class_name)
                
                if backend_class.is_available():
                    self._available_backends[name] = backend_class
                    logger.info(f"Registered backend: {name} ({class_path})")
                else:
                    logger.warning(f"Backend {name} is not available (dependencies missing)")
            except Exception as e:
                logger.warning(f"Failed to register backend {name}: {str(e)}")
    
    def get_available_backends(self) -> List[str]:
        """Get a list of available backend names.
        
        Returns:
            List of available backend names
        """
        return list(self._available_backends.keys())
    
    def get_backend(
        self, 
        name: Optional[str] = None, 
        config: Optional[Dict[str, Any]] = None
    ) -> QuantumBackend:
        """Get a quantum backend instance.
        
        Args:
            name: Name of the backend to get. If None, use the default backend.
            config: Configuration to pass to the backend
            
        Returns:
            An instance of the requested quantum backend
            
        Raises:
            ValueError: If the requested backend is not available
        """
        # Use default backend if none specified
        if name is None:
            name = self.config.get('default_backend', 'cirq')
        
        # Check if backend is available
        if name not in self._available_backends:
            available = ', '.join(self.get_available_backends())
            raise ValueError(
                f"Backend '{name}' is not available. "
                f"Available backends: {available}"
            )
        
        # Use cached instance if available and config hasn't changed
        if name in self._backends:
            if config is None or self._backends[name].config == config:
                return self._backends[name]
        
        # Create new instance
        backend_class = self._available_backends[name]
        backend_config = self.config.get('backends', {}).get(name, {})
        
        # Merge with provided config
        if config:
            backend_config = {**backend_config, **config}
        
        # Create and cache the backend
        self._backends[name] = backend_class(backend_config)
        return self._backends[name]
    
    def register_backend(
        self, 
        name: str, 
        backend_class: Type[QuantumBackend], 
        force: bool = False
    ) -> None:
        """Register a new backend class.
        
        Args:
            name: Name to register the backend under
            backend_class: Backend class to register
            force: Whether to overwrite an existing backend with the same name
            
        Raises:
            ValueError: If a backend with the same name already exists and force is False
        """
        if not issubclass(backend_class, QuantumBackend):
            raise ValueError("Backend class must be a subclass of QuantumBackend")
            
        if name in self._available_backends and not force:
            raise ValueError(f"Backend '{name}' is already registered")
        
        self._available_backends[name] = backend_class
        logger.info(f"Registered custom backend: {name} ({backend_class.__name__})")

# Global instance for convenience
_default_factory = QuantumBackendFactory()

def get_backend_factory() -> QuantumBackendFactory:
    """Get the default backend factory instance."""
    return _default_factory

def get_backend(
    name: Optional[str] = None, 
    config: Optional[Dict[str, Any]] = None
) -> QuantumBackend:
    """Get a quantum backend using the default factory."""
    return _default_factory.get_backend(name, config)

def register_backend(
    name: str, 
    backend_class: Type[QuantumBackend], 
    force: bool = False
) -> None:
    """Register a new backend class with the default factory."""
    _default_factory.register_backend(name, backend_class, force)
