FROM node:18-alpine as builder

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy all application code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM node:18-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install production dependencies only
RUN npm ci --only=production

# Copy built application from builder stage
COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/public /app/public
COPY --from=builder /app/server.js /app/server.js
COPY --from=builder /app/src /app/src
COPY --from=builder /app/config /app/config

# Create directory for metrics
RUN mkdir -p /app/metrics

# Set environment to production
ENV NODE_ENV=production
ENV PORT=3000
ENV ENABLE_METRICS=true
ENV QUANTUM_CONFIG=default
ENV TRINITY_CSDE_ENABLED=true

# Expose port
EXPOSE 3000

# Command to run the application
CMD ["node", "server.js"]
