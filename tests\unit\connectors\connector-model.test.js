/**
 * Unit tests for the Connector Model
 */

const { Connector, ConnectorStatus, ConnectorType } = require('../../../src/connectors/models/connector');

describe('Connector Model', () => {
  describe('constructor', () => {
    it('should create a connector with default values', () => {
      const connector = new Connector();
      
      expect(connector.id).toBeDefined();
      expect(connector.name).toBe('');
      expect(connector.description).toBe('');
      expect(connector.version).toBe('1.0.0');
      expect(connector.type).toBe(ConnectorType.SOURCE);
      expect(connector.status).toBe(ConnectorStatus.DRAFT);
      expect(connector.createdAt).toBeDefined();
      expect(connector.updatedAt).toBeDefined();
      expect(connector.configSchema).toEqual({ type: 'object', properties: {} });
    });
    
    it('should create a connector with provided values', () => {
      const data = {
        id: 'test-id',
        name: 'Test Connector',
        description: 'A test connector',
        version: '2.0.0',
        type: ConnectorType.DESTINATION,
        status: ConnectorStatus.PUBLISHED,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-02T00:00:00.000Z',
        configSchema: {
          type: 'object',
          properties: {
            apiKey: { type: 'string' }
          }
        }
      };
      
      const connector = new Connector(data);
      
      expect(connector.id).toBe('test-id');
      expect(connector.name).toBe('Test Connector');
      expect(connector.description).toBe('A test connector');
      expect(connector.version).toBe('2.0.0');
      expect(connector.type).toBe(ConnectorType.DESTINATION);
      expect(connector.status).toBe(ConnectorStatus.PUBLISHED);
      expect(connector.createdAt).toBe('2023-01-01T00:00:00.000Z');
      expect(connector.updatedAt).toBe('2023-01-02T00:00:00.000Z');
      expect(connector.configSchema).toEqual({
        type: 'object',
        properties: {
          apiKey: { type: 'string' }
        }
      });
    });
  });
  
  describe('validate', () => {
    it('should return valid=true when all required fields are present', () => {
      const connector = new Connector({
        name: 'Test Connector',
        description: 'A test connector'
      });
      
      const validation = connector.validate();
      
      expect(validation.valid).toBe(true);
      expect(validation.errors).toEqual([]);
    });
    
    it('should return valid=false when name is missing', () => {
      const connector = new Connector({
        description: 'A test connector'
      });
      
      const validation = connector.validate();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Name is required');
    });
    
    it('should return valid=false when description is missing', () => {
      const connector = new Connector({
        name: 'Test Connector'
      });
      
      const validation = connector.validate();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Description is required');
    });
    
    it('should return valid=false when both name and description are missing', () => {
      const connector = new Connector();
      
      const validation = connector.validate();
      
      expect(validation.valid).toBe(false);
      expect(validation.errors).toContain('Name is required');
      expect(validation.errors).toContain('Description is required');
    });
  });
  
  describe('toJSON', () => {
    it('should return a JSON representation of the connector', () => {
      const data = {
        id: 'test-id',
        name: 'Test Connector',
        description: 'A test connector',
        version: '1.0.0',
        type: ConnectorType.SOURCE,
        status: ConnectorStatus.DRAFT,
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
        configSchema: { type: 'object', properties: {} }
      };
      
      const connector = new Connector(data);
      const json = connector.toJSON();
      
      expect(json).toEqual(data);
    });
  });
  
  describe('ConnectorStatus enum', () => {
    it('should have the correct values', () => {
      expect(ConnectorStatus.DRAFT).toBe('draft');
      expect(ConnectorStatus.TESTING).toBe('testing');
      expect(ConnectorStatus.PUBLISHED).toBe('published');
      expect(ConnectorStatus.DEPRECATED).toBe('deprecated');
      expect(ConnectorStatus.RETIRED).toBe('retired');
    });
  });
  
  describe('ConnectorType enum', () => {
    it('should have the correct values', () => {
      expect(ConnectorType.SOURCE).toBe('source');
      expect(ConnectorType.DESTINATION).toBe('destination');
      expect(ConnectorType.TRANSFORMATION).toBe('transformation');
    });
  });
});
