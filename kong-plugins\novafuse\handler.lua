local BasePlugin = require "kong.plugins.base_plugin"
local cjson = require "cjson"

local NovaFuseUsageTracker = BasePlugin:extend()

NovaFuseUsageTracker.PRIORITY = 900
NovaFuseUsageTracker.VERSION = "1.0.0"

function NovaFuseUsageTracker:new()
  NovaFuseUsageTracker.super.new(self, "novafuse-usage-tracker")
end

function NovaFuseUsageTracker:access(conf)
  NovaFuseUsageTracker.super.access(self)
  
  -- Get request details
  local request_method = kong.request.get_method()
  local request_path = kong.request.get_path()
  local service_id = kong.router.get_service() and kong.router.get_service().id or "unknown"
  local service_name = kong.router.get_service() and kong.router.get_service().name or "unknown"
  local consumer_id = kong.client.get_consumer() and kong.client.get_consumer().id or "anonymous"
  local api_key = kong.request.get_header("apikey") or "none"
  
  -- Create usage record
  local usage_record = {
    timestamp = os.time(),
    service_id = service_id,
    service_name = service_name,
    consumer_id = consumer_id,
    api_key = api_key,
    method = request_method,
    path = request_path,
    ip = kong.client.get_forwarded_ip() or kong.client.get_ip()
  }
  
  -- Log usage record
  kong.log.notice("NOVAFUSE_USAGE_TRACKER: " .. cjson.encode(usage_record))
  
  -- Store usage record in Kong context for later use in header_filter phase
  kong.ctx.plugin.usage_record = usage_record
end

function NovaFuseUsageTracker:header_filter(conf)
  NovaFuseUsageTracker.super.header_filter(self)
  
  -- Add tracking headers to response
  kong.response.set_header("X-NovaFuse-Tracked", "true")
  kong.response.set_header("X-NovaFuse-Consumer-ID", kong.ctx.plugin.usage_record.consumer_id)
end

return NovaFuseUsageTracker
