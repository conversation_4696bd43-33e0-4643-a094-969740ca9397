#!/usr/bin/env python3
"""
Analyze wealth distribution data for 18/82 patterns.
This script analyzes wealth distribution data from various sources to identify
18/82 patterns and other UUFT patterns.
"""

import os
import sys
import logging
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('wealth_distribution_analysis.log')
    ]
)
logger = logging.getLogger('Wealth_Distribution_Analysis')

# Constants
PATTERN_1882_RATIO = 18 / 82
PATTERN_1882_THRESHOLD = 0.05  # 5% threshold for considering a match
PI = np.pi
RESULTS_DIR = "wealth_results"
os.makedirs(RESULTS_DIR, exist_ok=True)

# Sample wealth distribution data based on UBS Global Wealth Report insights
# These are approximate values based on publicly available information
WEALTH_DISTRIBUTION = {
    "Global": {
        "Top_1_percent": 45.8,  # Percentage of global wealth held by top 1%
        "Top_10_percent": 81.7,  # Percentage of global wealth held by top 10%
        "Bottom_50_percent": 1.1,  # Percentage of global wealth held by bottom 50%
        "Middle_40_percent": 17.2  # Percentage of global wealth held by middle 40%
    },
    "US": {
        "Top_1_percent": 35.3,  # Percentage of US wealth held by top 1%
        "Top_10_percent": 76.0,  # Percentage of US wealth held by top 10%
        "Bottom_50_percent": 2.6,  # Percentage of US wealth held by bottom 50%
        "Middle_40_percent": 21.4  # Percentage of US wealth held by middle 40%
    },
    "China": {
        "Top_1_percent": 30.6,  # Percentage of China's wealth held by top 1%
        "Top_10_percent": 67.8,  # Percentage of China's wealth held by top 10%
        "Bottom_50_percent": 6.0,  # Percentage of China's wealth held by bottom 50%
        "Middle_40_percent": 26.2  # Percentage of China's wealth held by middle 40%
    },
    "Europe": {
        "Top_1_percent": 25.2,  # Percentage of Europe's wealth held by top 1%
        "Top_10_percent": 65.7,  # Percentage of Europe's wealth held by top 10%
        "Bottom_50_percent": 4.5,  # Percentage of Europe's wealth held by bottom 50%
        "Middle_40_percent": 29.8  # Percentage of Europe's wealth held by middle 40%
    }
}

# Gini coefficients (measure of inequality, higher = more unequal)
GINI_COEFFICIENTS = {
    "Global": 0.82,
    "US": 0.85,
    "China": 0.70,
    "Europe": 0.67,
    "India": 0.83,
    "Brazil": 0.89,
    "Russia": 0.88,
    "South Africa": 0.86,
    "Japan": 0.63,
    "Australia": 0.65,
    "Canada": 0.73,
    "Mexico": 0.83
}

# Wealth per adult in USD (approximate values)
WEALTH_PER_ADULT = {
    "Global": 87_000,
    "US": 546_000,
    "China": 76_000,
    "Europe": 166_000,
    "India": 16_000,
    "Brazil": 42_000,
    "Russia": 27_000,
    "South Africa": 25_000,
    "Japan": 238_000,
    "Australia": 394_000,
    "Canada": 409_000,
    "Mexico": 46_000
}

# Wealth growth rates (annual average over past 15 years)
WEALTH_GROWTH_RATES = {
    "Global": 5.2,
    "US": 6.3,
    "China": 12.8,
    "Europe": 2.9,
    "India": 9.2,
    "Brazil": 4.1,
    "Russia": 3.5,
    "South Africa": 2.8,
    "Japan": 2.1,
    "Australia": 5.3,
    "Canada": 5.1,
    "Mexico": 3.7
}

def analyze_1882_patterns(data, name):
    """Analyze a dataset for 18/82 patterns."""
    logger.info(f"Analyzing {name} for 18/82 patterns...")
    
    # Convert data to numpy array if it's a dictionary
    if isinstance(data, dict):
        values = np.array(list(data.values()))
    else:
        values = np.array(data)
    
    # Check if we have enough data
    if len(values) < 2:
        logger.warning(f"Not enough data points in {name} to test for 18/82 patterns")
        return None
    
    # Sort the data
    sorted_data = np.sort(values)
    total_sum = np.sum(sorted_data)
    
    # Find the best 18/82 split
    best_split_idx = None
    best_proximity = float('inf')
    
    for i in range(1, len(sorted_data)):
        lower_sum = np.sum(sorted_data[:i])
        upper_sum = np.sum(sorted_data[i:])
        
        if total_sum == 0:
            continue
            
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
        
        # Calculate proximity to 18/82 ratio
        proximity_to_1882 = abs((lower_ratio / upper_ratio) - (18 / 82))
        
        if proximity_to_1882 < best_proximity:
            best_proximity = proximity_to_1882
            best_split_idx = i
    
    if best_split_idx is None:
        logger.warning(f"Could not find a valid 18/82 split for {name}")
        return None
        
    # Calculate the actual ratios
    lower_sum = np.sum(sorted_data[:best_split_idx])
    upper_sum = np.sum(sorted_data[best_split_idx:])
    
    if total_sum == 0:
        lower_ratio = 0
        upper_ratio = 0
    else:
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
    
    # Calculate proximity to 18/82
    proximity_percent = abs((lower_ratio / upper_ratio) - (18 / 82)) / (18 / 82) * 100
    is_1882_pattern = proximity_percent <= PATTERN_1882_THRESHOLD * 100
    
    result = {
        "dataset": name,
        "total_data_points": len(values),
        "split_index": best_split_idx,
        "lower_sum": float(lower_sum),
        "upper_sum": float(upper_sum),
        "lower_ratio": float(lower_ratio),
        "upper_ratio": float(upper_ratio),
        "proximity_to_1882_percent": float(proximity_percent),
        "is_1882_pattern": is_1882_pattern
    }
    
    logger.info(f"18/82 pattern analysis for {name}:")
    logger.info(f"  Lower ratio: {lower_ratio:.4f}, Upper ratio: {upper_ratio:.4f}")
    logger.info(f"  Proximity to 18/82: {proximity_percent:.2f}%")
    logger.info(f"  18/82 pattern present: {is_1882_pattern}")
    
    return result

def analyze_pareto_principle(data, name):
    """Analyze a dataset for the Pareto principle (80/20 rule)."""
    logger.info(f"Analyzing {name} for Pareto principle (80/20)...")
    
    # Convert data to numpy array if it's a dictionary
    if isinstance(data, dict):
        values = np.array(list(data.values()))
    else:
        values = np.array(data)
    
    # Check if we have enough data
    if len(values) < 2:
        logger.warning(f"Not enough data points in {name} to test for Pareto principle")
        return None
    
    # Sort the data
    sorted_data = np.sort(values)
    total_sum = np.sum(sorted_data)
    
    # Find the best 20/80 split
    best_split_idx = None
    best_proximity = float('inf')
    
    for i in range(1, len(sorted_data)):
        lower_sum = np.sum(sorted_data[:i])
        upper_sum = np.sum(sorted_data[i:])
        
        if total_sum == 0:
            continue
            
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
        
        # Calculate proximity to 20/80 ratio
        proximity_to_2080 = abs((lower_ratio / upper_ratio) - (20 / 80))
        
        if proximity_to_2080 < best_proximity:
            best_proximity = proximity_to_2080
            best_split_idx = i
    
    if best_split_idx is None:
        logger.warning(f"Could not find a valid 20/80 split for {name}")
        return None
        
    # Calculate the actual ratios
    lower_sum = np.sum(sorted_data[:best_split_idx])
    upper_sum = np.sum(sorted_data[best_split_idx:])
    
    if total_sum == 0:
        lower_ratio = 0
        upper_ratio = 0
    else:
        lower_ratio = lower_sum / total_sum
        upper_ratio = upper_sum / total_sum
    
    # Calculate proximity to 20/80
    proximity_percent = abs((lower_ratio / upper_ratio) - (20 / 80)) / (20 / 80) * 100
    is_pareto = proximity_percent <= PATTERN_1882_THRESHOLD * 100
    
    result = {
        "dataset": name,
        "total_data_points": len(values),
        "split_index": best_split_idx,
        "lower_sum": float(lower_sum),
        "upper_sum": float(upper_sum),
        "lower_ratio": float(lower_ratio),
        "upper_ratio": float(upper_ratio),
        "proximity_to_2080_percent": float(proximity_percent),
        "is_pareto_principle": is_pareto
    }
    
    logger.info(f"Pareto principle analysis for {name}:")
    logger.info(f"  Lower ratio: {lower_ratio:.4f}, Upper ratio: {upper_ratio:.4f}")
    logger.info(f"  Proximity to 20/80: {proximity_percent:.2f}%")
    logger.info(f"  Pareto principle present: {is_pareto}")
    
    return result

def analyze_gini_coefficients():
    """Analyze Gini coefficients for patterns."""
    logger.info("Analyzing Gini coefficients for patterns...")
    
    # Calculate the average Gini coefficient
    avg_gini = np.mean(list(GINI_COEFFICIENTS.values()))
    logger.info(f"Average Gini coefficient: {avg_gini:.4f}")
    
    # Check if the average Gini is close to the golden ratio (1.618)
    golden_ratio = (1 + np.sqrt(5)) / 2  # Approximately 1.618
    proximity_to_golden = abs(avg_gini - (1 / golden_ratio)) / (1 / golden_ratio) * 100
    logger.info(f"Proximity to 1/golden ratio: {proximity_to_golden:.2f}%")
    
    # Check if the average Gini is close to 0.82 (which would be 18/82 split)
    proximity_to_1882 = abs(avg_gini - 0.82) / 0.82 * 100
    logger.info(f"Proximity to 0.82 (18/82 split): {proximity_to_1882:.2f}%")
    
    # Analyze individual countries
    results = []
    for country, gini in GINI_COEFFICIENTS.items():
        proximity_to_1882 = abs(gini - 0.82) / 0.82 * 100
        is_1882 = proximity_to_1882 <= PATTERN_1882_THRESHOLD * 100
        
        result = {
            "country": country,
            "gini": gini,
            "proximity_to_1882_percent": proximity_to_1882,
            "is_1882_pattern": is_1882
        }
        results.append(result)
        
        logger.info(f"Gini coefficient for {country}: {gini:.4f}")
        logger.info(f"  Proximity to 0.82 (18/82 split): {proximity_to_1882:.2f}%")
        logger.info(f"  18/82 pattern present: {is_1882}")
    
    return results

def visualize_wealth_distribution():
    """Create visualizations of wealth distribution data."""
    logger.info("Creating wealth distribution visualizations...")
    
    # Create a figure for wealth distribution
    plt.figure(figsize=(12, 8))
    
    # Plot wealth distribution for different regions
    regions = list(WEALTH_DISTRIBUTION.keys())
    top_1_percent = [WEALTH_DISTRIBUTION[region]["Top_1_percent"] for region in regions]
    top_10_percent = [WEALTH_DISTRIBUTION[region]["Top_10_percent"] for region in regions]
    middle_40_percent = [WEALTH_DISTRIBUTION[region]["Middle_40_percent"] for region in regions]
    bottom_50_percent = [WEALTH_DISTRIBUTION[region]["Bottom_50_percent"] for region in regions]
    
    x = np.arange(len(regions))
    width = 0.2
    
    plt.bar(x - 1.5*width, top_1_percent, width, label='Top 1%')
    plt.bar(x - 0.5*width, top_10_percent, width, label='Top 10%')
    plt.bar(x + 0.5*width, middle_40_percent, width, label='Middle 40%')
    plt.bar(x + 1.5*width, bottom_50_percent, width, label='Bottom 50%')
    
    plt.xlabel('Region')
    plt.ylabel('Percentage of Wealth')
    plt.title('Wealth Distribution by Region')
    plt.xticks(x, regions)
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add a horizontal line at 18% and 82%
    plt.axhline(y=18, color='r', linestyle='--', alpha=0.5, label='18% Line')
    plt.axhline(y=82, color='g', linestyle='--', alpha=0.5, label='82% Line')
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'wealth_distribution.png'), dpi=300)
    plt.close()
    
    # Create a figure for Gini coefficients
    plt.figure(figsize=(12, 8))
    
    countries = list(GINI_COEFFICIENTS.keys())
    gini_values = list(GINI_COEFFICIENTS.values())
    
    # Sort by Gini coefficient
    sorted_indices = np.argsort(gini_values)
    sorted_countries = [countries[i] for i in sorted_indices]
    sorted_gini = [gini_values[i] for i in sorted_indices]
    
    plt.bar(sorted_countries, sorted_gini, color='skyblue')
    plt.xlabel('Country')
    plt.ylabel('Gini Coefficient')
    plt.title('Gini Coefficients by Country')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add a horizontal line at 0.82 (18/82 split)
    plt.axhline(y=0.82, color='r', linestyle='--', alpha=0.5, label='18/82 Split (0.82)')
    
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'gini_coefficients.png'), dpi=300)
    plt.close()
    
    logger.info("Visualizations saved to the 'wealth_results' directory.")

def main():
    """Main function to analyze wealth distribution data."""
    logger.info("Starting wealth distribution analysis...")
    
    # Analyze wealth distribution data for 18/82 patterns
    results_1882 = []
    
    # Analyze global wealth distribution
    for region, data in WEALTH_DISTRIBUTION.items():
        result = analyze_1882_patterns(data, f"{region}_wealth_distribution")
        if result:
            results_1882.append(result)
    
    # Analyze Gini coefficients
    gini_results = analyze_gini_coefficients()
    
    # Analyze wealth per adult
    result = analyze_1882_patterns(WEALTH_PER_ADULT, "wealth_per_adult")
    if result:
        results_1882.append(result)
    
    # Analyze wealth growth rates
    result = analyze_1882_patterns(WEALTH_GROWTH_RATES, "wealth_growth_rates")
    if result:
        results_1882.append(result)
    
    # Analyze for Pareto principle (80/20 rule)
    results_pareto = []
    
    # Analyze global wealth distribution for Pareto principle
    for region, data in WEALTH_DISTRIBUTION.items():
        result = analyze_pareto_principle(data, f"{region}_wealth_distribution")
        if result:
            results_pareto.append(result)
    
    # Create visualizations
    visualize_wealth_distribution()
    
    # Print summary
    logger.info("\n=== Wealth Distribution Analysis Summary ===")
    logger.info(f"Total datasets analyzed for 18/82 patterns: {len(results_1882)}")
    logger.info(f"Datasets with 18/82 patterns: {sum(1 for r in results_1882 if r['is_1882_pattern'])}")
    logger.info(f"Total datasets analyzed for Pareto principle: {len(results_pareto)}")
    logger.info(f"Datasets with Pareto principle: {sum(1 for r in results_pareto if r['is_pareto_principle'])}")
    logger.info(f"Countries with Gini coefficients close to 0.82 (18/82 split): {sum(1 for r in gini_results if r['is_1882_pattern'])}")
    
    return {
        "results_1882": results_1882,
        "results_pareto": results_pareto,
        "gini_results": gini_results
    }

if __name__ == "__main__":
    main()
