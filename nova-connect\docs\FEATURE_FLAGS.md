# NovaConnect UAC Feature Flag System

This document describes the feature flag system for NovaConnect UAC, which provides dynamic enabling/disabling of features based on product tier, user role, environment, and other criteria.

## Overview

The NovaConnect UAC Feature Flag System consists of the following components:

1. **Feature Flag Service**: Core service for managing feature flags
2. **Feature Flag Middleware**: Express middleware for feature flag checking
3. **Feature Routes**: Example routes demonstrating feature flag functionality
4. **Subscription Management**: Tools for managing user subscriptions and entitlements

## Product Tiers

NovaConnect UAC offers the following product tiers:

1. **Free**: Basic functionality for small projects
2. **Standard**: Standard functionality for growing teams
3. **Professional**: Advanced functionality for professional teams
4. **Enterprise**: Enterprise-grade functionality for large organizations

## Feature Categories

Features are organized into the following categories:

1. **Core**: Basic functionality available in all tiers
2. **Workflow**: Workflow automation features
3. **Export/Import**: Configuration export and import features
4. **Security**: Security features
5. **Monitoring**: Monitoring and alerting features
6. **Analytics**: Analytics and reporting features
7. **AI**: AI-powered features
8. **Governance**: Governance and compliance features

## Feature Flag Service

The Feature Flag Service provides the following functionality:

### Feature Flag Management

- **Get All Feature Flags**: Get all available feature flags
- **Get Feature Flag by ID**: Get a specific feature flag by ID
- **Update Feature Flag**: Update a feature flag

### Subscription Management

- **Get All Subscription Tiers**: Get all available subscription tiers
- **Get Subscription Tier by ID**: Get a specific subscription tier by ID
- **Get User Entitlement**: Get a user's entitlement
- **Update User Entitlement**: Update a user's entitlement

### Feature Access Control

- **Check Feature Access**: Check if a user has access to a feature
- **Get Feature Limit**: Get a user's limit for a feature
- **Track Feature Usage**: Track a user's usage of a feature
- **Check Feature Limit**: Check if a user has reached a feature limit

### Usage Reporting

- **Get Feature Usage**: Get a user's feature usage
- **Get User Subscription Details**: Get a user's subscription details
- **Get User Available Features**: Get a user's available features

## Feature Flag Middleware

The Feature Flag Middleware provides the following functionality:

### Middleware Components

- **requireFeature**: Middleware for checking feature access
- **checkFeatureLimit**: Middleware for checking feature limits

### API Endpoints

- **GET /subscription**: Get the current user's subscription details
- **GET /features**: Get the current user's available features
- **GET /features/usage**: Get the current user's feature usage
- **PUT /subscription/:userId**: Update a user's subscription

## Example Feature Routes

The Feature Routes demonstrate how to use the Feature Flag System:

- **GET /features/basic**: Basic feature endpoint (available in all tiers)
- **GET /features/standard**: Standard feature endpoint (available in Standard tier and above)
- **GET /features/professional**: Professional feature endpoint (available in Professional tier and above)
- **GET /features/enterprise**: Enterprise feature endpoint (available in Enterprise tier only)
- **GET /features/limited**: Limited feature endpoint (checks usage limits)
- **POST /features/ai/generate**: AI feature endpoint (with usage tracking)

## Feature Flag Configuration

Feature flags are configured in the `feature_flags.json` file with the following structure:

```json
{
  "id": "feature.id",
  "name": "Feature Name",
  "description": "Feature description",
  "category": "feature_category",
  "enabled": true,
  "tiers": ["free", "standard", "professional", "enterprise"],
  "limits": {
    "free": { "limit_key": 10 },
    "standard": { "limit_key": 100 },
    "professional": { "limit_key": 1000 },
    "enterprise": { "limit_key": -1 }
  }
}
```

## Subscription Tier Configuration

Subscription tiers are configured in the `subscription_tiers.json` file with the following structure:

```json
{
  "id": "tier_id",
  "name": "Tier Name",
  "description": "Tier description",
  "price": 0,
  "features": [
    "feature.id1",
    "feature.id2"
  ],
  "limits": {
    "limit_key1": 10,
    "limit_key2": 100
  }
}
```

## User Entitlement Configuration

User entitlements are configured in the `user_entitlements.json` file with the following structure:

```json
{
  "userId": "user_id",
  "tierId": "tier_id",
  "customFeatures": [
    "feature.id1",
    "feature.id2"
  ],
  "customLimits": {
    "feature.id1": {
      "limit_key": 20
    }
  },
  "created": "2023-01-01T00:00:00Z",
  "updated": "2023-01-01T00:00:00Z"
}
```

## Feature Usage Tracking

Feature usage is tracked in the `feature_usage.json` file with the following structure:

```json
{
  "userId": "user_id",
  "featureId": "feature.id",
  "date": "2023-01-01",
  "quantity": 10,
  "created": "2023-01-01T00:00:00Z",
  "updated": "2023-01-01T00:00:00Z"
}
```

## Google Cloud Marketplace Integration

For Google Cloud Marketplace deployment, the Feature Flag System is configured to support the following tiers:

1. **NovaConnect Core**: Basic functionality (equivalent to Free tier)
2. **NovaConnect Secure**: Standard functionality (equivalent to Standard tier)
3. **NovaConnect Enterprise**: Advanced functionality (equivalent to Professional tier)
4. **NovaConnect AI Boost**: Enterprise-grade functionality (equivalent to Enterprise tier)

## Usage Examples

### Checking Feature Access in Routes

```javascript
const { requireFeature } = require('../middleware/featureFlagMiddleware');

// Route that requires a specific feature
router.get('/my-feature', requireFeature('feature.id'), (req, res) => {
  res.json({ message: 'Feature is available' });
});
```

### Checking Feature Limits in Routes

```javascript
const { checkFeatureLimit } = require('../middleware/featureFlagMiddleware');

// Route that checks a feature limit
router.post('/my-limited-feature', 
  requireFeature('feature.id'),
  checkFeatureLimit('feature.id', 'operations_per_day'),
  (req, res) => {
    res.json({ message: 'Feature limit not reached' });
  }
);
```

### Getting User Subscription Details

```javascript
const featureFlagService = new FeatureFlagService();

// Get user subscription details
const subscriptionDetails = await featureFlagService.getUserSubscriptionDetails('user_id');
```

### Tracking Feature Usage

```javascript
const featureFlagService = new FeatureFlagService();

// Track feature usage
await featureFlagService.trackFeatureUsage('user_id', 'feature.id', 1);
```

## Best Practices

### Feature Flag Naming

- Use a consistent naming convention for feature flags
- Use a hierarchical structure (e.g., `category.feature`)
- Use descriptive names that clearly indicate the feature's purpose

### Feature Flag Design

- Keep feature flags simple and focused
- Avoid dependencies between feature flags
- Document feature flags thoroughly

### Feature Flag Lifecycle

- Regularly review and clean up feature flags
- Remove feature flags that are no longer needed
- Update feature flags as product tiers evolve

## Conclusion

The NovaConnect UAC Feature Flag System provides a flexible and powerful way to manage feature access based on product tier, user role, environment, and other criteria. By using this system, you can easily create and manage different product tiers for Google Cloud Marketplace deployment.
