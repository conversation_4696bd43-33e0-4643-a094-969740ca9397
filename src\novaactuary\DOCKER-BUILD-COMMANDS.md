# NovaActuary™ Docker Build Commands
**Step-by-Step Container Creation**

---

## **🚀 Quick Build Commands**

### **Option 1: Using Docker Compose (Recommended)**
```bash
# Navigate to NovaActuary directory
cd src/novaactuary

# Build the container
docker-compose build novaactuary

# Start the container
docker-compose up -d novaactuary

# Check container status
docker-compose ps

# View logs
docker-compose logs novaactuary
```

### **Option 2: Using Docker Build Directly**
```bash
# Navigate to project root
cd C:\Users\<USER>\novafuse-api-superstore

# Build the container
docker build -t novaactuary:latest -f src/novaactuary/Dockerfile .

# Run the container
docker run -d --name novaactuary-core -p 3000:3000 novaactuary:latest

# Check container status
docker ps

# View logs
docker logs novaactuary-core
```

### **Option 3: Using Batch Script**
```bash
# Navigate to NovaActuary directory
cd src/novaactuary

# Run the build script
build-container.bat
```

---

## **🧪 Testing Commands**

### **Health Check**
```bash
# Test container health
docker-compose exec novaactuary node novaactuary/health-check.js

# Or if using direct Docker
docker exec novaactuary-core node novaactuary/health-check.js
```

### **Quick Test**
```bash
# Run quick validation test
docker-compose exec novaactuary node novaactuary/quick-test.js

# Or if using direct Docker
docker exec novaactuary-core node novaactuary/quick-test.js
```

### **Executive Demo**
```bash
# Run executive demonstration
docker-compose exec novaactuary node novaactuary/demo-novaactuary.js

# Or if using direct Docker
docker exec novaactuary-core node novaactuary/demo-novaactuary.js
```

---

## **🔧 Troubleshooting**

### **If Docker Desktop is not running:**
1. Start Docker Desktop from Windows Start Menu
2. Wait for Docker to fully initialize (whale icon in system tray)
3. Try the build commands again

### **If build fails with permission errors:**
```bash
# Run PowerShell as Administrator
# Then try the build commands
```

### **If build fails with network errors:**
```bash
# Check internet connection
# Try building with --no-cache flag
docker-compose build --no-cache novaactuary
```

### **If container won't start:**
```bash
# Check Docker logs
docker-compose logs novaactuary

# Check system resources
docker system df

# Clean up if needed
docker system prune -f
```

---

## **📊 Expected Build Output**

### **Successful Build:**
```
Building novaactuary
Step 1/15 : FROM node:18-alpine
 ---> abc123def456
Step 2/15 : WORKDIR /app
 ---> Running in xyz789abc123
...
Successfully built abc123def456
Successfully tagged novaactuary_novaactuary:latest
```

### **Successful Container Start:**
```
Creating novaactuary-core ... done
```

### **Successful Health Check:**
```
🏥 NOVAACTUARY™ HEALTH CHECK RESULTS
==================================================
📊 Overall Status: HEALTHY
⏱️  Health Check Duration: 287.45ms

📋 Component Checks:
   ✅ coreModule: PASS - NovaActuary™ v1.0.0-REVOLUTIONARY loaded successfully
   ✅ componentIntegration: PASS - All components integrated
   ✅ mathematicalFramework: PASS - Mathematical framework validated
   ✅ performance: PASS - Performance within acceptable limits

🎉 NovaActuary™ is ready for production deployment!
```

---

## **🎯 Container Specifications**

### **Image Details:**
- **Base Image**: node:18-alpine
- **Size**: ~500MB (optimized)
- **Ports**: 3000 (API), 8080 (Dashboard)
- **Memory**: 1-2GB recommended
- **CPU**: 1-2 cores recommended

### **Included Components:**
- ✅ NovaActuary™ Core Platform
- ✅ NovaConnect Universal API
- ✅ CSM-PRS AI Test Suite
- ✅ Comphyology Mathematical Framework
- ✅ Trinity Financial Oracle (simulated)
- ✅ Health Monitoring
- ✅ Performance Benchmarking

### **Production Features:**
- ✅ Non-root user security
- ✅ Health check endpoints
- ✅ Resource limits
- ✅ Logging and monitoring
- ✅ Graceful shutdown

---

## **🚀 Quick Start Guide**

### **1. Ensure Docker Desktop is Running**
- Check system tray for Docker whale icon
- Should show "Docker Desktop is running"

### **2. Open Terminal/PowerShell**
- Navigate to: `C:\Users\<USER>\novafuse-api-superstore\src\novaactuary`

### **3. Build Container**
```bash
docker-compose build novaactuary
```

### **4. Start Container**
```bash
docker-compose up -d novaactuary
```

### **5. Test Container**
```bash
docker-compose exec novaactuary node novaactuary/health-check.js
```

### **6. Run Demo**
```bash
docker-compose exec novaactuary node novaactuary/quick-test.js
```

**If all steps complete successfully, your NovaActuary™ container is ready for production deployment! 🎉**

---

**RECOMMENDATION: Start with Option 1 (Docker Compose) for the most reliable build process.**
