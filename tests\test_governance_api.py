"""
Tests for the C-AIaaS Governance API.

These tests verify the functionality of the quantum risk assessment
and threshold management endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from fastapi import status
import json
from datetime import datetime

from src.api.governance_api import app, RiskAssessmentRequest, ThresholdRequest
from src.governance.novashield import RiskLevel, ComplianceLevel

# Test client
client = TestClient(app)

def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == status.HTTP_200_OK
    assert "status" in response.json()
    assert response.json()["status"] == "healthy"

def test_risk_assessment_valid():
    """Test risk assessment with valid input"""
    test_data = {
        "task_type": "security_update",
        "vendor_qscore": 7.5,
        "compliance_level": "enterprise",
        "metadata": {
            "priority": "high",
            "environment": "production"
        }
    }
    
    response = client.post(
        "/api/v1/risk/assess",
        json=test_data,
        params={"request_id": "test-123"}
    )
    
    assert response.status_code == status.HTTP_200_OK
    result = response.json()
    
    # Validate response structure
    assert "risk_level" in result
    assert "quantum_risk_score" in result
    assert "confidence_interval" in result
    assert "recommended_action" in result
    assert "request_id" in result
    assert result["request_id"] == "test-123"
    
    # Validate risk score is within expected range
    assert 0.0 <= result["quantum_risk_score"] <= 1.0
    
    # Validate confidence interval
    assert len(result["confidence_interval"]) == 2
    lower, upper = result["confidence_interval"]
    assert 0.0 <= lower <= upper <= 1.0

def test_risk_assessment_invalid_qscore():
    """Test risk assessment with invalid Q-Score"""
    test_data = {
        "task_type": "security_update",
        "vendor_qscore": 15.0,  # Invalid: Q-Score > 10.0
        "compliance_level": "enterprise"
    }
    
    response = client.post("/api/v1/risk/assess", json=test_data)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

def test_get_threshold_valid():
    """Test getting a threshold with valid input"""
    risk_level = "high"
    compliance_level = "enterprise"
    
    response = client.get(f"/api/v1/thresholds/{risk_level}", 
                         params={"compliance_level": compliance_level})
    
    assert response.status_code == status.HTTP_200_OK
    result = response.json()
    
    # Validate response structure
    assert "threshold" in result
    assert "risk_level" in result
    assert "compliance_level" in result
    assert "last_updated" in result
    
    # Validate values
    assert result["risk_level"] == risk_level
    assert result["compliance_level"] == compliance_level
    assert isinstance(result["threshold"], float)
    assert result["threshold"] > 0

def test_get_threshold_invalid_risk_level():
    """Test getting a threshold with invalid risk level"""
    response = client.get("/api/v1/thresholds/invalid_risk")
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

def test_get_threshold_nonexistent():
    """Test getting a threshold for a non-existent risk level"""
    # This assumes 'nonexistent' is not a valid RiskLevel
    response = client.get("/api/v1/thresholds/nonexistent")
    assert response.status_code == status.HTTP_404_NOT_FOUND

def test_risk_assessment_model():
    """Test the RiskAssessmentRequest model"""
    # Test valid data
    valid_data = {
        "task_type": "security_update",
        "vendor_qscore": 8.0,
        "compliance_level": "enterprise",
        "metadata": {"key": "value"}
    }
    request = RiskAssessmentRequest(**valid_data)
    assert request.task_type == "security_update"
    assert request.vendor_qscore == 8.0
    assert request.compliance_level == ComplianceLevel.ENTERPRISE
    assert request.metadata == {"key": "value"}
    
    # Test default values
    minimal_data = {
        "task_type": "test",
        "vendor_qscore": 5.0
    }
    request = RiskAssessmentRequest(**minimal_data)
    assert request.compliance_level == ComplianceLevel.STANDARD
    assert request.metadata == {}

if __name__ == "__main__":
    pytest.main(["-v", "test_governance_api.py"])
