/**
 * Security Controller
 * 
 * This controller handles API requests related to security features.
 */

const SecurityService = require('../services/SecurityService');
const { ValidationError } = require('../utils/errors');

class SecurityController {
  constructor() {
    this.securityService = new SecurityService();
  }

  /**
   * Get security audit logs
   */
  async getSecurityAuditLogs(req, res, next) {
    try {
      const filters = req.query;
      const logs = await this.securityService.getSecurityAuditLogs(filters);
      res.json(logs);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all IP restrictions
   */
  async getAllIpRestrictions(req, res, next) {
    try {
      const filters = req.query;
      const restrictions = await this.securityService.getAllIpRestrictions(filters);
      res.json(restrictions);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get IP restriction by ID
   */
  async getIpRestrictionById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('IP restriction ID is required');
      }
      
      const restriction = await this.securityService.getIpRestrictionById(id);
      res.json(restriction);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create IP restriction
   */
  async createIpRestriction(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('IP restriction data is required');
      }
      
      const restriction = await this.securityService.createIpRestriction(data, req.user.id);
      res.status(201).json(restriction);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update IP restriction
   */
  async updateIpRestriction(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('IP restriction ID is required');
      }
      
      if (!data) {
        throw new ValidationError('IP restriction data is required');
      }
      
      const restriction = await this.securityService.updateIpRestriction(id, data, req.user.id);
      res.json(restriction);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete IP restriction
   */
  async deleteIpRestriction(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('IP restriction ID is required');
      }
      
      const result = await this.securityService.deleteIpRestriction(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check if IP is allowed for resource
   */
  async checkIpAccess(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      const { ip } = req.query;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      if (!ip) {
        throw new ValidationError('IP address is required');
      }
      
      const allowed = await this.securityService.isIpAllowedForResource(ip, resourceType, resourceId);
      res.json({ allowed });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all encryption keys
   */
  async getAllEncryptionKeys(req, res, next) {
    try {
      const filters = req.query;
      const keys = await this.securityService.getAllEncryptionKeys(filters);
      res.json(keys);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get encryption key by ID
   */
  async getEncryptionKeyById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Encryption key ID is required');
      }
      
      const key = await this.securityService.getEncryptionKeyById(id);
      res.json(key);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create encryption key
   */
  async createEncryptionKey(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Encryption key data is required');
      }
      
      const key = await this.securityService.createEncryptionKey(data, req.user.id);
      res.status(201).json(key);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update encryption key
   */
  async updateEncryptionKey(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Encryption key ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Encryption key data is required');
      }
      
      const key = await this.securityService.updateEncryptionKey(id, data, req.user.id);
      res.json(key);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Rotate encryption key
   */
  async rotateEncryptionKey(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Encryption key ID is required');
      }
      
      const key = await this.securityService.rotateEncryptionKey(id, req.user.id);
      res.json(key);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete encryption key
   */
  async deleteEncryptionKey(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Encryption key ID is required');
      }
      
      const result = await this.securityService.deleteEncryptionKey(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Encrypt data
   */
  async encryptData(req, res, next) {
    try {
      const { keyId, data } = req.body;
      
      if (!keyId) {
        throw new ValidationError('Encryption key ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Data to encrypt is required');
      }
      
      const encryptedData = await this.securityService.encryptData(data, keyId);
      res.json(encryptedData);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Decrypt data
   */
  async decryptData(req, res, next) {
    try {
      const encryptedPackage = req.body;
      
      if (!encryptedPackage) {
        throw new ValidationError('Encrypted data package is required');
      }
      
      const decryptedData = await this.securityService.decryptData(encryptedPackage);
      res.json({ decryptedData });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all security policies
   */
  async getAllSecurityPolicies(req, res, next) {
    try {
      const filters = req.query;
      const policies = await this.securityService.getAllSecurityPolicies(filters);
      res.json(policies);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get security policy by ID
   */
  async getSecurityPolicyById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Security policy ID is required');
      }
      
      const policy = await this.securityService.getSecurityPolicyById(id);
      res.json(policy);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create security policy
   */
  async createSecurityPolicy(req, res, next) {
    try {
      const data = req.body;
      
      if (!data) {
        throw new ValidationError('Security policy data is required');
      }
      
      const policy = await this.securityService.createSecurityPolicy(data, req.user.id);
      res.status(201).json(policy);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update security policy
   */
  async updateSecurityPolicy(req, res, next) {
    try {
      const { id } = req.params;
      const data = req.body;
      
      if (!id) {
        throw new ValidationError('Security policy ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Security policy data is required');
      }
      
      const policy = await this.securityService.updateSecurityPolicy(id, data, req.user.id);
      res.json(policy);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete security policy
   */
  async deleteSecurityPolicy(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Security policy ID is required');
      }
      
      const result = await this.securityService.deleteSecurityPolicy(id, req.user.id);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Evaluate security policy
   */
  async evaluateSecurityPolicy(req, res, next) {
    try {
      const { id } = req.params;
      const context = req.body;
      
      if (!id) {
        throw new ValidationError('Security policy ID is required');
      }
      
      if (!context) {
        throw new ValidationError('Evaluation context is required');
      }
      
      const result = await this.securityService.evaluateSecurityPolicy(id, context);
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new SecurityController();
