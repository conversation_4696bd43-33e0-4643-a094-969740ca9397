# Simple test script for NovaFuse API Superstore

Write-Host "Testing NovaFuse API Superstore API..."
Write-Host ""

# Test Governance API directly
Write-Host "Testing Governance API directly..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3001/governance/board/meetings" -ErrorAction Stop
    Write-Host "? Governance API is working!" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) board meetings"
} catch {
    Write-Host "? Governance API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test Security API directly
Write-Host "Testing Security API directly..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3002/security/vulnerabilities" -ErrorAction Stop
    Write-Host "? Security API is working!" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) vulnerabilities"
} catch {
    Write-Host "? Security API test failed: $_" -ForegroundColor Red
}
Write-Host ""

# Test APIs API directly
Write-Host "Testing APIs API directly..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:3003/apis/catalog" -ErrorAction Stop
    Write-Host "? APIs API is working!" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) APIs in the catalog"
} catch {
    Write-Host "? APIs API test failed: $_" -ForegroundColor Red
}
Write-Host ""

Write-Host "Tests completed!"
