/**
 * Authentication Middleware
 *
 * This middleware handles authentication and authorization.
 */

const AuthService = require('../services/AuthService');
const ApiKeyService = require('../services/ApiKeyService');
const RateLimitService = require('../services/RateLimitService');
const TeamService = require('../services/TeamService');
const RolePermissionService = require('../services/RolePermissionService');
const { AuthenticationError, AuthorizationError } = require('../utils/errors');

const authService = new AuthService();
const apiKeyService = new ApiKeyService();
const rateLimitService = new RateLimitService();
const teamService = new TeamService();
const rolePermissionService = new RolePermissionService();

/**
 * Authenticate user using JWT token
 */
const authenticate = async (req, res, next) => {
  try {
    // Check for API key
    const apiKey = req.headers['x-api-key'];

    if (apiKey) {
      // Authenticate using API key
      const apiKeyData = await apiKeyService.verifyApiKey(apiKey);

      // Check rate limit for API key
      const rateLimitResult = await rateLimitService.checkRateLimit(apiKey, 'apiKey');

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': rateLimitResult.limit,
        'X-RateLimit-Remaining': rateLimitResult.remaining,
        'X-RateLimit-Reset': rateLimitResult.reset
      });

      // Get user associated with API key
      const user = await authService.getUserById(apiKeyData.userId);

      // Attach user and API key to request
      req.user = user;
      req.apiKey = apiKeyData;

      return next();
    }

    // Check for JWT token
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No authentication provided, check rate limit for anonymous user
      const clientIp = req.ip || req.connection.remoteAddress;
      const rateLimitResult = await rateLimitService.checkRateLimit(clientIp, 'anonymous');

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': rateLimitResult.limit,
        'X-RateLimit-Remaining': rateLimitResult.remaining,
        'X-RateLimit-Reset': rateLimitResult.reset
      });

      throw new AuthenticationError('Authentication required');
    }

    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = await authService.verifyToken(token);

    // Get user
    const user = await authService.getUserById(decoded.sub);

    // Check rate limit for authenticated user
    const rateLimitResult = await rateLimitService.checkRateLimit(user.id, 'authenticated');

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': rateLimitResult.limit,
      'X-RateLimit-Remaining': rateLimitResult.remaining,
      'X-RateLimit-Reset': rateLimitResult.reset
    });

    // Attach user to request
    req.user = user;

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    // Check for API key
    const apiKey = req.headers['x-api-key'];

    if (apiKey) {
      try {
        // Authenticate using API key
        const apiKeyData = await apiKeyService.verifyApiKey(apiKey);

        // Check rate limit for API key
        const rateLimitResult = await rateLimitService.checkRateLimit(apiKey, 'apiKey');

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': rateLimitResult.limit,
          'X-RateLimit-Remaining': rateLimitResult.remaining,
          'X-RateLimit-Reset': rateLimitResult.reset
        });

        // Get user associated with API key
        const user = await authService.getUserById(apiKeyData.userId);

        // Attach user and API key to request
        req.user = user;
        req.apiKey = apiKeyData;

        return next();
      } catch (error) {
        // Ignore API key errors
      }
    }

    // Check for JWT token
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.split(' ')[1];

      try {
        // Verify token
        const decoded = await authService.verifyToken(token);

        // Get user
        const user = await authService.getUserById(decoded.sub);

        // Check rate limit for authenticated user
        const rateLimitResult = await rateLimitService.checkRateLimit(user.id, 'authenticated');

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': rateLimitResult.limit,
          'X-RateLimit-Remaining': rateLimitResult.remaining,
          'X-RateLimit-Reset': rateLimitResult.reset
        });

        // Attach user to request
        req.user = user;

        return next();
      } catch (error) {
        // Ignore token errors
      }
    }

    // No authentication provided or authentication failed, check rate limit for anonymous user
    const clientIp = req.ip || req.connection.remoteAddress;
    const rateLimitResult = await rateLimitService.checkRateLimit(clientIp, 'anonymous');

    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': rateLimitResult.limit,
      'X-RateLimit-Remaining': rateLimitResult.remaining,
      'X-RateLimit-Reset': rateLimitResult.reset
    });

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Check if user has required role
 */
const hasRole = (role) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    if (!authService.hasRole(req.user, role)) {
      return next(new AuthenticationError(`Role ${role} required`));
    }

    next();
  };
};

/**
 * Check if user has required permission
 */
const hasPermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AuthenticationError('Authentication required'));
    }

    // Check API key permissions if request was made with API key
    if (req.apiKey) {
      if (!apiKeyService.hasPermission(req.apiKey, permission)) {
        return next(new AuthenticationError(`Permission ${permission} required`));
      }

      return next();
    }

    // Check user permissions
    if (!authService.hasPermission(req.user, permission)) {
      return next(new AuthenticationError(`Permission ${permission} required`));
    }

    next();
  };
};

/**
 * Check if user is a member of a team
 */
const isTeamMember = (teamIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const teamId = req.params[teamIdParam];

      if (!teamId) {
        return next(new AuthorizationError(`Team ID parameter '${teamIdParam}' not found`));
      }

      // Check if user is a member of the team
      const isMember = await teamService.isUserTeamMember(teamId, req.user.id);

      if (!isMember) {
        return next(new AuthorizationError('Team membership required'));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Check if user has a specific role in a team
 */
const hasTeamRole = (role, teamIdParam = 'id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('Authentication required'));
      }

      const teamId = req.params[teamIdParam];

      if (!teamId) {
        return next(new AuthorizationError(`Team ID parameter '${teamIdParam}' not found`));
      }

      // Get user's role in the team
      const userRole = await teamService.getUserRoleInTeam(teamId, req.user.id);

      if (!userRole) {
        return next(new AuthorizationError('Team membership required'));
      }

      // Check if user has the required role
      if (typeof role === 'string') {
        if (userRole !== role) {
          return next(new AuthorizationError(`Team role '${role}' required`));
        }
      } else if (Array.isArray(role)) {
        if (!role.includes(userRole)) {
          return next(new AuthorizationError(`One of team roles [${role.join(', ')}] required`));
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Check if user is an admin
 */
const isAdmin = (req, res, next) => {
  return hasRole('admin')(req, res, next);
};

module.exports = {
  authenticate,
  optionalAuth,
  hasRole,
  hasPermission,
  isTeamMember,
  hasTeamRole,
  isAdmin
};
