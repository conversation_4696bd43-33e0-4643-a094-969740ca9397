/**
 * Unit tests for ProgressiveDisclosureSystem
 */

const { expect } = require('chai');
const ProgressiveDisclosureSystem = require('../../access/ProgressiveDisclosureSystem');

describe('ProgressiveDisclosureSystem', () => {
  let disclosureSystem;
  let mockProfile;
  
  beforeEach(() => {
    disclosureSystem = new ProgressiveDisclosureSystem();
    
    // Create a mock profile with various medical information
    mockProfile = {
      profileId: 'test-profile-123',
      schemaVersion: '1.0.0',
      fullName: '<PERSON>',
      dateOfBirth: '1980-01-01',
      bloodType: 'A+',
      emergencyContacts: [
        {
          name: '<PERSON>',
          relationship: 'Spouse',
          phone: '************'
        }
      ],
      allergies: [
        {
          name: 'Peanuts',
          severity: 'Severe'
        }
      ],
      medications: [
        {
          name: 'Aspirin',
          dosage: '100mg',
          frequency: 'Daily'
        }
      ],
      medicalConditions: [
        {
          name: 'Hypertension',
          diagnosisDate: '2010-05-15'
        }
      ],
      insuranceInfo: {
        provider: 'Health Insurance Co',
        policyNumber: '12345678',
        groupNumber: 'G123',
        phone: '************'
      },
      dnr: false,
      organDonor: true,
      primaryCareProvider: {
        name: 'Dr. Smith',
        phone: '************',
        address: '123 Medical Center Dr'
      },
      notes: 'Additional medical notes'
    };
  });
  
  describe('determineDisclosureLevel', () => {
    it('should determine MINIMAL access level for low context scores', () => {
      const context = {
        emergencyType: 'OTHER',
        emergencySeverity: 'LOW'
      };
      
      const authentication = {
        serviceId: 'test-service',
        role: 'OTHER'
      };
      
      const result = disclosureSystem.determineDisclosureLevel(mockProfile, context, authentication);
      
      expect(result).to.be.an('object');
      expect(result.accessLevel).to.equal('MINIMAL');
      expect(result.profile).to.be.an('object');
      expect(result.profile.profileId).to.equal(mockProfile.profileId);
      expect(result.profile.fullName).to.equal(mockProfile.fullName);
      expect(result.profile.dateOfBirth).to.equal(mockProfile.dateOfBirth);
      expect(result.profile.bloodType).to.equal(mockProfile.bloodType);
      
      // Should not include sensitive information
      expect(result.profile.allergies).to.be.undefined;
      expect(result.profile.medications).to.be.undefined;
      expect(result.profile.medicalConditions).to.be.undefined;
      expect(result.profile.insuranceInfo).to.be.undefined;
    });
    
    it('should determine STANDARD access level for medical emergency', () => {
      const context = {
        emergencyType: 'MEDICAL',
        emergencySeverity: 'HIGH'
      };
      
      const authentication = {
        serviceId: 'test-service',
        role: 'PARAMEDIC',
        serviceVerified: true
      };
      
      const result = disclosureSystem.determineDisclosureLevel(mockProfile, context, authentication);
      
      expect(result).to.be.an('object');
      expect(result.accessLevel).to.equal('STANDARD');
      expect(result.profile).to.be.an('object');
      expect(result.profile.profileId).to.equal(mockProfile.profileId);
      expect(result.profile.fullName).to.equal(mockProfile.fullName);
      expect(result.profile.dateOfBirth).to.equal(mockProfile.dateOfBirth);
      expect(result.profile.bloodType).to.equal(mockProfile.bloodType);
      
      // Should include medical information
      expect(result.profile.allergies).to.deep.equal(mockProfile.allergies);
      expect(result.profile.medications).to.deep.equal(mockProfile.medications);
      expect(result.profile.medicalConditions).to.deep.equal(mockProfile.medicalConditions);
      expect(result.profile.dnr).to.equal(mockProfile.dnr);
      expect(result.profile.organDonor).to.equal(mockProfile.organDonor);
      
      // Should not include sensitive information
      expect(result.profile.insuranceInfo).to.be.undefined;
      expect(result.profile.notes).to.be.undefined;
    });
    
    it('should determine FULL access level for critical emergency with doctor', () => {
      const context = {
        emergencyType: 'CARDIAC',
        emergencySeverity: 'CRITICAL',
        locationType: 'HOSPITAL'
      };
      
      const authentication = {
        serviceId: 'test-service',
        role: 'DOCTOR',
        serviceVerified: true,
        userVerified: true,
        mfaCompleted: true
      };
      
      const result = disclosureSystem.determineDisclosureLevel(mockProfile, context, authentication);
      
      expect(result).to.be.an('object');
      expect(result.accessLevel).to.equal('FULL');
      expect(result.profile).to.be.an('object');
      expect(result.profile.profileId).to.equal(mockProfile.profileId);
      expect(result.profile.fullName).to.equal(mockProfile.fullName);
      
      // Should include all information
      expect(result.profile.allergies).to.deep.equal(mockProfile.allergies);
      expect(result.profile.medications).to.deep.equal(mockProfile.medications);
      expect(result.profile.medicalConditions).to.deep.equal(mockProfile.medicalConditions);
      expect(result.profile.insuranceInfo).to.deep.equal(mockProfile.insuranceInfo);
      expect(result.profile.primaryCareProvider).to.deep.equal(mockProfile.primaryCareProvider);
      expect(result.profile.notes).to.equal(mockProfile.notes);
    });
    
    it('should highlight allergies for allergic emergency', () => {
      const context = {
        emergencyType: 'ALLERGIC',
        emergencySeverity: 'HIGH'
      };
      
      const authentication = {
        serviceId: 'test-service',
        role: 'EMT',
        serviceVerified: true
      };
      
      const result = disclosureSystem.determineDisclosureLevel(mockProfile, context, authentication);
      
      expect(result).to.be.an('object');
      expect(result.profile.allergies).to.deep.equal(mockProfile.allergies);
      expect(result.profile._highlighted).to.include('allergies');
    });
    
    it('should throw error for invalid profile', () => {
      const context = {
        emergencyType: 'MEDICAL',
        emergencySeverity: 'HIGH'
      };
      
      const authentication = {
        serviceId: 'test-service',
        role: 'PARAMEDIC'
      };
      
      expect(() => disclosureSystem.determineDisclosureLevel(null, context, authentication))
        .to.throw('Invalid profile');
      
      expect(() => disclosureSystem.determineDisclosureLevel({}, context, authentication))
        .to.throw('Invalid profile');
    });
    
    it('should throw error for invalid context', () => {
      const authentication = {
        serviceId: 'test-service',
        role: 'PARAMEDIC'
      };
      
      expect(() => disclosureSystem.determineDisclosureLevel(mockProfile, null, authentication))
        .to.throw('Invalid context');
      
      expect(() => disclosureSystem.determineDisclosureLevel(mockProfile, {}, authentication))
        .to.throw('Invalid context: emergencyType is required');
    });
    
    it('should throw error for invalid authentication', () => {
      const context = {
        emergencyType: 'MEDICAL',
        emergencySeverity: 'HIGH'
      };
      
      expect(() => disclosureSystem.determineDisclosureLevel(mockProfile, context, null))
        .to.throw('Invalid authentication');
      
      expect(() => disclosureSystem.determineDisclosureLevel(mockProfile, context, {}))
        .to.throw('Invalid authentication: serviceId is required');
    });
  });
  
  describe('elevateDisclosureLevel', () => {
    it('should elevate disclosure level for emergency', () => {
      const profileId = 'test-profile-123';
      const context = {
        emergencyType: 'TRAUMA',
        emergencySeverity: 'HIGH'
      };
      
      const authentication = {
        serviceId: 'test-service',
        userId: 'test-user',
        role: 'DOCTOR'
      };
      
      const reason = 'Patient unconscious, immediate access needed';
      
      const result = disclosureSystem.elevateDisclosureLevel(profileId, context, authentication, reason);
      
      expect(result).to.be.an('object');
      expect(result.elevationId).to.be.a('string');
      expect(result.status).to.equal('APPROVED');
      expect(result.timestamp).to.be.a('string');
    });
    
    it('should throw error for missing profile ID', () => {
      const context = {
        emergencyType: 'TRAUMA',
        emergencySeverity: 'HIGH'
      };
      
      const authentication = {
        serviceId: 'test-service',
        userId: 'test-user',
        role: 'DOCTOR'
      };
      
      const reason = 'Patient unconscious, immediate access needed';
      
      expect(() => disclosureSystem.elevateDisclosureLevel(null, context, authentication, reason))
        .to.throw('Profile ID is required');
    });
    
    it('should throw error for invalid context', () => {
      const profileId = 'test-profile-123';
      
      const authentication = {
        serviceId: 'test-service',
        userId: 'test-user',
        role: 'DOCTOR'
      };
      
      const reason = 'Patient unconscious, immediate access needed';
      
      expect(() => disclosureSystem.elevateDisclosureLevel(profileId, null, authentication, reason))
        .to.throw('Invalid context');
      
      expect(() => disclosureSystem.elevateDisclosureLevel(profileId, {}, authentication, reason))
        .to.throw('Invalid context: emergencyType is required');
    });
    
    it('should throw error for invalid authentication', () => {
      const profileId = 'test-profile-123';
      const context = {
        emergencyType: 'TRAUMA',
        emergencySeverity: 'HIGH'
      };
      
      const reason = 'Patient unconscious, immediate access needed';
      
      expect(() => disclosureSystem.elevateDisclosureLevel(profileId, context, null, reason))
        .to.throw('Invalid authentication');
      
      expect(() => disclosureSystem.elevateDisclosureLevel(profileId, context, {}, reason))
        .to.throw('Invalid authentication: serviceId is required');
    });
    
    it('should throw error for missing reason', () => {
      const profileId = 'test-profile-123';
      const context = {
        emergencyType: 'TRAUMA',
        emergencySeverity: 'HIGH'
      };
      
      const authentication = {
        serviceId: 'test-service',
        userId: 'test-user',
        role: 'DOCTOR'
      };
      
      expect(() => disclosureSystem.elevateDisclosureLevel(profileId, context, authentication, null))
        .to.throw('Reason for elevation is required');
    });
  });
  
  describe('getAccessLogs', () => {
    it('should return access logs for a profile', () => {
      const profileId = 'test-profile-123';
      
      // Create some access logs
      disclosureSystem.determineDisclosureLevel(
        mockProfile,
        { emergencyType: 'MEDICAL', emergencySeverity: 'HIGH' },
        { serviceId: 'service-1', role: 'PARAMEDIC' }
      );
      
      disclosureSystem.determineDisclosureLevel(
        mockProfile,
        { emergencyType: 'CARDIAC', emergencySeverity: 'CRITICAL' },
        { serviceId: 'service-2', role: 'DOCTOR' }
      );
      
      const logs = disclosureSystem.getAccessLogs(profileId);
      
      expect(logs).to.be.an('array');
      expect(logs.length).to.equal(2);
      expect(logs[0].profileId).to.equal(profileId);
      expect(logs[0].accessId).to.be.a('string');
      expect(logs[0].timestamp).to.be.a('string');
      expect(logs[0].serviceId).to.be.a('string');
      expect(logs[0].accessLevel).to.be.a('string');
    });
    
    it('should filter logs by options', () => {
      const profileId = 'test-profile-123';
      
      // Create some access logs
      disclosureSystem.determineDisclosureLevel(
        mockProfile,
        { emergencyType: 'MEDICAL', emergencySeverity: 'HIGH' },
        { serviceId: 'service-1', role: 'PARAMEDIC' }
      );
      
      disclosureSystem.determineDisclosureLevel(
        mockProfile,
        { emergencyType: 'CARDIAC', emergencySeverity: 'CRITICAL' },
        { serviceId: 'service-2', role: 'DOCTOR' }
      );
      
      const logs = disclosureSystem.getAccessLogs(profileId, {
        serviceId: 'service-1'
      });
      
      expect(logs).to.be.an('array');
      expect(logs.length).to.equal(1);
      expect(logs[0].serviceId).to.equal('service-1');
    });
    
    it('should throw error for missing profile ID', () => {
      expect(() => disclosureSystem.getAccessLogs(null))
        .to.throw('Profile ID is required');
    });
  });
  
  describe('getDisclosureRules', () => {
    it('should return disclosure rules', () => {
      const rules = disclosureSystem.getDisclosureRules();
      
      expect(rules).to.be.an('array');
      expect(rules.length).to.be.at.least(3);
      
      const minimalRule = rules.find(rule => rule.level === 'MINIMAL');
      expect(minimalRule).to.be.an('object');
      expect(minimalRule.fields).to.be.an('array');
      
      const standardRule = rules.find(rule => rule.level === 'STANDARD');
      expect(standardRule).to.be.an('object');
      expect(standardRule.fields).to.be.an('array');
      
      const fullRule = rules.find(rule => rule.level === 'FULL');
      expect(fullRule).to.be.an('object');
      expect(fullRule.fields).to.be.an('array');
    });
  });
  
  describe('updateDisclosureRules', () => {
    it('should update disclosure rules', () => {
      const newRules = [
        {
          level: 'CUSTOM',
          description: 'Custom disclosure level',
          fields: ['profileId', 'fullName', 'bloodType']
        },
        {
          level: 'FULL',
          description: 'Full disclosure level',
          fields: ['profileId', 'fullName', 'dateOfBirth', 'bloodType', 'allergies', 'medications']
        }
      ];
      
      const result = disclosureSystem.updateDisclosureRules(newRules);
      expect(result).to.be.true;
      
      const updatedRules = disclosureSystem.getDisclosureRules();
      expect(updatedRules).to.deep.equal(newRules);
    });
    
    it('should throw error for invalid rules', () => {
      expect(() => disclosureSystem.updateDisclosureRules(null))
        .to.throw('Invalid disclosure rules');
      
      expect(() => disclosureSystem.updateDisclosureRules([]))
        .to.throw('Invalid disclosure rules');
      
      expect(() => disclosureSystem.updateDisclosureRules([{ level: 'INVALID' }]))
        .to.throw('Invalid rule format');
    });
  });
});
