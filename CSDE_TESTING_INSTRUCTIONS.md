# CSDE Testing Instructions

This document provides instructions for running the optimized CSDE tests in the next session.

## Files Created

1. **Core Implementation**:
   - `csde/core_optimized.py` - Optimized CSDE implementation with Orion's recommendations

2. **Benchmarking**:
   - `csde/benchmarks/optimized_benchmark.py` - Benchmark suite for CSDE testing

3. **Docker Setup**:
   - `csde/Dockerfile` - Docker configuration for GPU-accelerated testing
   - `build_and_run_csde_tests.sh` - Shell script to build and run tests (Linux/Mac)
   - `build_and_run_csde_tests.bat` - Batch file to build and run tests (Windows)

4. **Requirements**:
   - `csde_requirements.txt` - Python dependencies

## Key Optimizations Implemented (<PERSON>'s Suggestions)

1. **Entropy-Based Resource Allocation**:
   - Implements weighted balance penalty to prevent task starvation
   - Uses Shannon entropy to optimize resource distribution
   - Dynamically adjusts critical/standard task balance

2. **Domain-Aware Encoders with Contrastive Loss**:
   - Uses domain-specific fusion strategies (different φ values per domain)
   - Implements contrastive loss principles to bring dissimilar representations closer
   - Adjusts fusion weights based on pattern similarity

3. **Pattern Preservation with Skip Connections**:
   - Adds skip connections from original tensor to preserve patterns
   - Implements re-injection layers post-fusion
   - Weights skip connections inversely to similarity

4. **Parameterized π10³ Scaling**:
   - Dynamically adjusts scaling factor based on data characteristics
   - Uses coefficient of variation to determine optimal scaling
   - Prevents numerical explosion while maintaining performance

5. **Speed Optimization**:
   - Profiles tensor operations to identify bottlenecks
   - Uses lower precision (float16) for tensor operations
   - Fuses operations to reduce memory transfers
   - Implements mixed precision training

## Running the Tests

### Option 1: Docker (Recommended)

1. Ensure Docker is running with GPU support
2. Run the build and test script:

   **Windows**:
   ```
   build_and_run_csde_tests.bat
   ```

   **Linux/Mac**:
   ```
   chmod +x build_and_run_csde_tests.sh
   ./build_and_run_csde_tests.sh
   ```

3. View results in the `results` directory

### Option 2: GCP VM

1. Create a GPU-enabled VM in GCP:
   ```
   gcloud compute instances create csde-test-vm \
     --machine-type=n1-standard-8 \
     --accelerator=type=nvidia-tesla-t4,count=1 \
     --boot-disk-size=100GB \
     --image-family=tf-ent-2-9-cu113 \
     --image-project=deeplearning-platform-release
   ```

2. SSH into the VM:
   ```
   gcloud compute ssh csde-test-vm
   ```

3. Clone the repository and run tests:
   ```
   git clone https://github.com/novafuse/csde-testing.git
   cd csde-testing
   pip install -r csde_requirements.txt
   python -m csde.benchmarks.optimized_benchmark --use-gpu
   ```

## Expected Results

With the optimizations implemented, we expect:

1. **Latency**: Approaching the 0.07ms target with GPU acceleration
2. **Throughput**: Significant improvement toward the 69,000 events/sec target
3. **Resource Allocation**: Positive improvement percentage (>0%)
4. **Pattern Preservation**: Average preservation >0.85

## Next Steps

1. Run the tests on May 18th (1+8=9 → Completion)
2. Conduct an 18-hour continuous validation
3. Document results for the patent filing
4. Share findings with the team

## Troubleshooting

- **GPU Not Detected**: Ensure NVIDIA drivers are installed and `nvidia-smi` works
- **Docker GPU Issues**: Verify Docker is configured for GPU support
- **Performance Below Expectations**: Check for resource contention or background processes

## Contact

For assistance, contact <EMAIL>
