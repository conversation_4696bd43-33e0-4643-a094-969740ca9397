/**
 * Data Transformer Tests
 *
 * This file contains unit tests for the data transformer service.
 */

const dataTransformer = require('../../services/dataTransformer');
const rewire = require('rewire');
const dataTransformerRewire = rewire('../../services/dataTransformer');

describe('Data Transformer', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('transformRequest', () => {
    it('should transform request data using the default transformer', async () => {
      const data = { test: 'data' };
      const integrationType = 'unknown';
      const action = 'unknown';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual(data);
    });

    it('should transform request data for CRM integration', async () => {
      const data = { test: 'data' };
      const integrationType = 'crm';
      const action = 'unknown';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual(data);
    });

    it('should transform request data for marketing integration', async () => {
      const data = { test: 'data' };
      const integrationType = 'marketing';
      const action = 'unknown';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual(data);
    });

    it('should transform request data for data-export action', async () => {
      const data = {
        identifiers: { email: '<EMAIL>' },
        dataCategories: ['Contact Information'],
        dateRange: { start: '2023-01-01', end: '2023-12-31' }
      };
      const integrationType = 'unknown';
      const action = 'data-export';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual({
        identifiers: { email: '<EMAIL>' },
        dataCategories: ['Contact Information'],
        dateRange: { start: '2023-01-01', end: '2023-12-31' }
      });
    });

    it('should transform request data for data-deletion action', async () => {
      const data = {
        identifiers: { email: '<EMAIL>' },
        dataCategories: ['Contact Information'],
        deletionType: 'hard'
      };
      const integrationType = 'unknown';
      const action = 'data-deletion';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual({
        identifiers: { email: '<EMAIL>' },
        dataCategories: ['Contact Information'],
        deletionType: 'hard'
      });
    });

    it('should transform request data for CRM data-export action', async () => {
      const data = {
        identifiers: { email: '<EMAIL>' },
        dataCategories: ['Contact Information', 'Account Information'],
        dateRange: { start: '2023-01-01', end: '2023-12-31' }
      };
      const integrationType = 'crm';
      const action = 'data-export';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual({
        email: '<EMAIL>',
        fields: ['name', 'email', 'phone', 'address', 'account_name', 'account_type', 'industry'],
        dateRange: { start: '2023-01-01', end: '2023-12-31' }
      });
    });

    it('should transform request data for marketing data-export action', async () => {
      const data = {
        identifiers: { email: '<EMAIL>' },
        dataCategories: ['Contact Information', 'Marketing Preferences'],
        dateRange: { start: '2023-01-01', end: '2023-12-31' }
      };
      const integrationType = 'marketing';
      const action = 'data-export';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual({
        subscriberId: '<EMAIL>',
        dataTypes: ['profile', 'contact_details', 'preferences', 'subscriptions'],
        timeFrame: {
          from: '2023-01-01',
          to: '2023-12-31'
        }
      });
    });

    it('should handle missing data in data-export action', async () => {
      const data = {};
      const integrationType = 'unknown';
      const action = 'data-export';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual({
        identifiers: {},
        dataCategories: [],
        dateRange: { start: null, end: null }
      });
    });

    it('should handle missing data in data-deletion action', async () => {
      const data = {};
      const integrationType = 'unknown';
      const action = 'data-deletion';

      const result = await dataTransformer.transformRequest(data, integrationType, action);

      expect(result).toEqual({
        identifiers: {},
        dataCategories: [],
        deletionType: 'soft'
      });
    });
  });

  describe('transformResponse', () => {
    it('should transform response data using the default transformer', async () => {
      const response = { data: { test: 'data' } };
      const integrationType = 'unknown';
      const action = 'unknown';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toEqual({ test: 'data' });
    });

    it('should transform response data for CRM integration', async () => {
      const response = { data: { test: 'data' } };
      const integrationType = 'crm';
      const action = 'unknown';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toEqual({ test: 'data' });
    });

    it('should transform response data for marketing integration', async () => {
      const response = { data: { test: 'data' } };
      const integrationType = 'marketing';
      const action = 'unknown';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toEqual({ test: 'data' });
    });

    it('should transform response data for data-export action', async () => {
      const response = { data: { id: 'export-123', test: 'data' } };
      const integrationType = 'unknown';
      const action = 'data-export';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('exportId', 'export-123');
      expect(result).toHaveProperty('exportData', { id: 'export-123', test: 'data' });
      expect(result).toHaveProperty('exportTimestamp');
      expect(result.exportTimestamp).toBeInstanceOf(Date);
    });

    it('should transform response data for data-deletion action', async () => {
      const response = { data: { id: 'deletion-123', status: 'pending' } };
      const integrationType = 'unknown';
      const action = 'data-deletion';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('deletionId', 'deletion-123');
      expect(result).toHaveProperty('status', 'pending');
      expect(result).toHaveProperty('deletionTimestamp');
      expect(result.deletionTimestamp).toBeInstanceOf(Date);
    });

    it('should transform response data for CRM data-export action', async () => {
      const response = {
        data: {
          contact: { name: 'Test User' },
          account: { name: 'Test Company' },
          activities: [{ type: 'email', date: '2023-01-01' }]
        }
      };
      const integrationType = 'crm';
      const action = 'data-export';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('exportId');
      expect(result.exportId).toMatch(/^crm-export-\d+$/);
      expect(result).toHaveProperty('contactData', { name: 'Test User' });
      expect(result).toHaveProperty('accountData', { name: 'Test Company' });
      expect(result).toHaveProperty('activityData', [{ type: 'email', date: '2023-01-01' }]);
      expect(result).toHaveProperty('exportTimestamp');
      expect(result.exportTimestamp).toBeInstanceOf(Date);
    });

    it('should transform response data for marketing data-export action', async () => {
      const response = {
        data: {
          subscriber: { email: '<EMAIL>' },
          campaigns: [{ id: 'campaign-1', name: 'Test Campaign' }],
          interactions: [{ type: 'open', date: '2023-01-01' }]
        }
      };
      const integrationType = 'marketing';
      const action = 'data-export';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('exportId');
      expect(result.exportId).toMatch(/^marketing-export-\d+$/);
      expect(result).toHaveProperty('subscriberData', { email: '<EMAIL>' });
      expect(result).toHaveProperty('campaignData', [{ id: 'campaign-1', name: 'Test Campaign' }]);
      expect(result).toHaveProperty('interactionData', [{ type: 'open', date: '2023-01-01' }]);
      expect(result).toHaveProperty('exportTimestamp');
      expect(result.exportTimestamp).toBeInstanceOf(Date);
    });

    it('should generate an export ID if not provided in data-export action', async () => {
      const response = { data: { test: 'data' } };
      const integrationType = 'unknown';
      const action = 'data-export';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('exportId');
      expect(result.exportId).toMatch(/^export-\d+$/);
    });

    it('should generate a deletion ID if not provided in data-deletion action', async () => {
      const response = { data: { status: 'completed' } };
      const integrationType = 'unknown';
      const action = 'data-deletion';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('deletionId');
      expect(result.deletionId).toMatch(/^deletion-\d+$/);
    });

    it('should use a default status if not provided in data-deletion action', async () => {
      const response = { data: { id: 'deletion-123' } };
      const integrationType = 'unknown';
      const action = 'data-deletion';

      const result = await dataTransformer.transformResponse(response, integrationType, action);

      expect(result).toHaveProperty('status', 'completed');
    });
  });

  describe('getRequestTransformer', () => {
    const getRequestTransformer = dataTransformerRewire.__get__('getRequestTransformer');
    const defaultRequestTransformer = dataTransformerRewire.__get__('defaultRequestTransformer');
    const requestTransformers = dataTransformerRewire.__get__('requestTransformers');

    it('should return the specific transformer for integration type and action', () => {
      const integrationType = 'crm';
      const action = 'data-export';

      const transformer = getRequestTransformer(integrationType, action);

      expect(transformer).toBe(requestTransformers['crm_data-export']);
    });

    it('should return the integration type transformer if no specific transformer exists', () => {
      const integrationType = 'crm';
      const action = 'unknown';

      const transformer = getRequestTransformer(integrationType, action);

      expect(transformer).toBe(requestTransformers['crm']);
    });

    it('should return the action transformer if no integration type or specific transformer exists', () => {
      const integrationType = 'unknown';
      const action = 'data-export';

      const transformer = getRequestTransformer(integrationType, action);

      expect(transformer).toBe(requestTransformers['data-export']);
    });

    it('should return the default transformer if no other transformer exists', () => {
      const integrationType = 'unknown';
      const action = 'unknown';

      const transformer = getRequestTransformer(integrationType, action);

      expect(transformer).toBe(defaultRequestTransformer);
    });
  });

  describe('getResponseTransformer', () => {
    const getResponseTransformer = dataTransformerRewire.__get__('getResponseTransformer');
    const defaultResponseTransformer = dataTransformerRewire.__get__('defaultResponseTransformer');
    const responseTransformers = dataTransformerRewire.__get__('responseTransformers');

    it('should return the specific transformer for integration type and action', () => {
      const integrationType = 'crm';
      const action = 'data-export';

      const transformer = getResponseTransformer(integrationType, action);

      expect(transformer).toBe(responseTransformers['crm_data-export']);
    });

    it('should return the integration type transformer if no specific transformer exists', () => {
      const integrationType = 'crm';
      const action = 'unknown';

      const transformer = getResponseTransformer(integrationType, action);

      expect(transformer).toBe(responseTransformers['crm']);
    });

    it('should return the action transformer if no integration type or specific transformer exists', () => {
      const integrationType = 'unknown';
      const action = 'data-export';

      const transformer = getResponseTransformer(integrationType, action);

      expect(transformer).toBe(responseTransformers['data-export']);
    });

    it('should return the default transformer if no other transformer exists', () => {
      const integrationType = 'unknown';
      const action = 'unknown';

      const transformer = getResponseTransformer(integrationType, action);

      expect(transformer).toBe(defaultResponseTransformer);
    });
  });

  describe('defaultRequestTransformer', () => {
    const defaultRequestTransformer = dataTransformerRewire.__get__('defaultRequestTransformer');

    it('should return the original data', async () => {
      const data = { test: 'data' };

      const result = await defaultRequestTransformer(data);

      expect(result).toBe(data);
    });
  });

  describe('defaultResponseTransformer', () => {
    const defaultResponseTransformer = dataTransformerRewire.__get__('defaultResponseTransformer');

    it('should return the response data', async () => {
      const response = { data: { test: 'data' } };

      const result = await defaultResponseTransformer(response);

      expect(result).toEqual({ test: 'data' });
    });
  });

  describe('mapDataCategoriesToCrmFields', () => {
    const mapDataCategoriesToCrmFields = dataTransformerRewire.__get__('mapDataCategoriesToCrmFields');

    it('should map data categories to CRM fields', () => {
      const dataCategories = ['Contact Information', 'Account Information'];

      const result = mapDataCategoriesToCrmFields(dataCategories);

      expect(result).toEqual([
        'name', 'email', 'phone', 'address',
        'account_name', 'account_type', 'industry'
      ]);
    });

    it('should handle empty data categories', () => {
      const dataCategories = [];

      const result = mapDataCategoriesToCrmFields(dataCategories);

      expect(result).toEqual([]);
    });

    it('should handle unknown data categories', () => {
      const dataCategories = ['Unknown Category'];

      const result = mapDataCategoriesToCrmFields(dataCategories);

      expect(result).toEqual([]);
    });
  });

  describe('mapDataCategoriesToMarketingFields', () => {
    const mapDataCategoriesToMarketingFields = dataTransformerRewire.__get__('mapDataCategoriesToMarketingFields');

    it('should map data categories to marketing fields', () => {
      const dataCategories = ['Contact Information', 'Marketing Preferences'];

      const result = mapDataCategoriesToMarketingFields(dataCategories);

      expect(result).toEqual([
        'profile', 'contact_details',
        'preferences', 'subscriptions'
      ]);
    });

    it('should handle empty data categories', () => {
      const dataCategories = [];

      const result = mapDataCategoriesToMarketingFields(dataCategories);

      expect(result).toEqual([]);
    });

    it('should handle unknown data categories', () => {
      const dataCategories = ['Unknown Category'];

      const result = mapDataCategoriesToMarketingFields(dataCategories);

      expect(result).toEqual([]);
    });
  });
});
