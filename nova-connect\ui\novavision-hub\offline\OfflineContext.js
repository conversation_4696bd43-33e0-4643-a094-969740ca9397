/**
 * Offline Context
 * 
 * This module provides a context for offline functionality in the NovaVision Hub.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import offlineManager from './OfflineManager';

// Create offline context
const OfflineContext = createContext();

/**
 * Offline Provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @returns {React.ReactElement} Offline Provider component
 */
export const OfflineProvider = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [pendingRequests, setPendingRequests] = useState([]);
  const [offlineMode, setOfflineMode] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  
  // Update state when online/offline status changes
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      
      // If offline mode is enabled, don't automatically disable it
      if (!offlineMode) {
        syncPendingRequests();
      }
    };
    
    const handleOffline = () => {
      setIsOnline(false);
    };
    
    const handleSyncComplete = (data) => {
      setPendingRequests(offlineManager.pendingRequests);
      setLastSyncTime(new Date());
    };
    
    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    offlineManager.addEventListener('online', handleOnline);
    offlineManager.addEventListener('offline', handleOffline);
    offlineManager.addEventListener('syncComplete', handleSyncComplete);
    
    // Initial state
    setIsOnline(navigator.onLine);
    setPendingRequests(offlineManager.pendingRequests);
    
    // Cleanup
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      offlineManager.removeEventListener('online', handleOnline);
      offlineManager.removeEventListener('offline', handleOffline);
      offlineManager.removeEventListener('syncComplete', handleSyncComplete);
    };
  }, [offlineMode]);
  
  /**
   * Toggle offline mode
   */
  const toggleOfflineMode = () => {
    setOfflineMode(!offlineMode);
    
    // If switching to online mode, sync pending requests
    if (offlineMode && isOnline) {
      syncPendingRequests();
    }
  };
  
  /**
   * Sync pending requests
   */
  const syncPendingRequests = async () => {
    if (!isOnline) return;
    
    await offlineManager.syncPendingRequests();
    setPendingRequests(offlineManager.pendingRequests);
    setLastSyncTime(new Date());
  };
  
  /**
   * Save data for offline use
   * 
   * @param {string} key - Data key
   * @param {*} data - Data to save
   * @returns {Promise<boolean>} Success
   */
  const saveOfflineData = async (key, data) => {
    const success = await offlineManager.saveOfflineData(key, data);
    return success;
  };
  
  /**
   * Get data for offline use
   * 
   * @param {string} key - Data key
   * @returns {Promise<*>} Data
   */
  const getOfflineData = async (key) => {
    return await offlineManager.getOfflineData(key);
  };
  
  /**
   * Clear offline data
   * 
   * @param {string} [key] - Data key (if not provided, clears all data)
   * @returns {Promise<boolean>} Success
   */
  const clearOfflineData = async (key) => {
    const success = await offlineManager.clearOfflineData(key);
    return success;
  };
  
  /**
   * Add request to pending requests
   * 
   * @param {Object} request - Request to add
   * @returns {Promise<number>} Request ID
   */
  const addPendingRequest = async (request) => {
    const id = await offlineManager.addRequest(request);
    setPendingRequests(offlineManager.pendingRequests);
    return id;
  };
  
  /**
   * Remove request from pending requests
   * 
   * @param {number} id - Request ID
   * @returns {Promise<boolean>} Success
   */
  const removePendingRequest = async (id) => {
    const success = await offlineManager.removeRequest(id);
    setPendingRequests(offlineManager.pendingRequests);
    return success;
  };
  
  /**
   * Fetch with offline support
   * 
   * @param {string|Request} resource - Resource to fetch
   * @param {Object} [options] - Fetch options
   * @returns {Promise<Response>} Response
   */
  const offlineFetch = async (resource, options = {}) => {
    // If online and not in offline mode, use normal fetch
    if (isOnline && !offlineMode) {
      try {
        const response = await fetch(resource, options);
        
        // Cache successful GET requests
        if (response.ok && (!options.method || options.method === 'GET')) {
          const clonedResponse = response.clone();
          const responseData = await clonedResponse.json();
          
          // Generate cache key
          const url = typeof resource === 'string' ? resource : resource.url;
          const cacheKey = `cache:${url}`;
          
          // Save to offline cache
          await saveOfflineData(cacheKey, responseData);
        }
        
        return response;
      } catch (error) {
        console.error('Fetch failed:', error);
        
        // Try to get from offline cache
        if (!options.method || options.method === 'GET') {
          const url = typeof resource === 'string' ? resource : resource.url;
          const cacheKey = `cache:${url}`;
          const cachedData = await getOfflineData(cacheKey);
          
          if (cachedData) {
            return new Response(JSON.stringify(cachedData), {
              headers: { 'Content-Type': 'application/json' },
              status: 200,
              statusText: 'OK (from cache)'
            });
          }
        }
        
        // If not a GET request or no cached data, queue for later
        if (options.method && options.method !== 'GET') {
          const url = typeof resource === 'string' ? resource : resource.url;
          
          await addPendingRequest({
            url,
            method: options.method,
            headers: options.headers,
            body: options.body
          });
          
          return new Response(JSON.stringify({ success: true, offline: true }), {
            headers: { 'Content-Type': 'application/json' },
            status: 202,
            statusText: 'Accepted (queued for sync)'
          });
        }
        
        // If all else fails, return error response
        return new Response(JSON.stringify({ error: 'You are offline and no cached data is available' }), {
          headers: { 'Content-Type': 'application/json' },
          status: 503,
          statusText: 'Service Unavailable'
        });
      }
    }
    
    // If offline or in offline mode
    if (!options.method || options.method === 'GET') {
      // Try to get from offline cache
      const url = typeof resource === 'string' ? resource : resource.url;
      const cacheKey = `cache:${url}`;
      const cachedData = await getOfflineData(cacheKey);
      
      if (cachedData) {
        return new Response(JSON.stringify(cachedData), {
          headers: { 'Content-Type': 'application/json' },
          status: 200,
          statusText: 'OK (from cache)'
        });
      }
    } else {
      // If not a GET request, queue for later
      const url = typeof resource === 'string' ? resource : resource.url;
      
      await addPendingRequest({
        url,
        method: options.method,
        headers: options.headers,
        body: options.body
      });
      
      return new Response(JSON.stringify({ success: true, offline: true }), {
        headers: { 'Content-Type': 'application/json' },
        status: 202,
        statusText: 'Accepted (queued for sync)'
      });
    }
    
    // If all else fails, return error response
    return new Response(JSON.stringify({ error: 'You are offline and no cached data is available' }), {
      headers: { 'Content-Type': 'application/json' },
      status: 503,
      statusText: 'Service Unavailable'
    });
  };
  
  // Create context value
  const contextValue = {
    isOnline,
    offlineMode,
    pendingRequests,
    lastSyncTime,
    toggleOfflineMode,
    syncPendingRequests,
    saveOfflineData,
    getOfflineData,
    clearOfflineData,
    addPendingRequest,
    removePendingRequest,
    offlineFetch
  };
  
  return (
    <OfflineContext.Provider value={contextValue}>
      {children}
    </OfflineContext.Provider>
  );
};

OfflineProvider.propTypes = {
  children: PropTypes.node.isRequired
};

/**
 * Use offline hook
 * 
 * @returns {Object} Offline context value
 */
export const useOffline = () => {
  const context = useContext(OfflineContext);
  
  if (!context) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  
  return context;
};

export default OfflineContext;
