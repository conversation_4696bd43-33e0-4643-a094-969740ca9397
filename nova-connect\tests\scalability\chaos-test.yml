config:
  target: "{{ $processEnvironment.TARGET_URL }}"
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 100
      name: "Warm up"
    
    # Ramp-up phase
    - duration: 120
      arrivalRate: 100
      rampTo: 10000
      name: "Ramp up to 10K RPS"
    
    # Sustained load phase
    - duration: 300
      arrivalRate: 10000
      name: "Sustained 10K RPS"
    
    # Chaos phase 1 - Pod kills
    - duration: 120
      arrivalRate: 10000
      name: "Chaos - Pod kills"
    
    # Ramp-up to peak
    - duration: 120
      arrivalRate: 10000
      rampTo: 100000
      name: "Ramp up to 100K RPS"
    
    # Peak load phase
    - duration: 300
      arrivalRate: 100000
      name: "Peak 100K RPS"
    
    # Chaos phase 2 - Network issues
    - duration: 120
      arrivalRate: 100000
      name: "Chaos - Network issues"
    
    # Cool down phase
    - duration: 60
      arrivalRate: 10000
      rampTo: 100
      name: "Cool down"
  
  environments:
    gcp:
      target: "https://{{ $processEnvironment.GCP_SERVICE_URL }}"
      phases:
        - duration: 60
          arrivalRate: 100
          name: "Warm up"
        - duration: 120
          arrivalRate: 100
          rampTo: 10000
          name: "Ramp up to 10K RPS"
        - duration: 300
          arrivalRate: 10000
          name: "Sustained 10K RPS"
        - duration: 120
          arrivalRate: 10000
          name: "Chaos - Pod kills"
        - duration: 120
          arrivalRate: 10000
          rampTo: 100000
          name: "Ramp up to 100K RPS"
        - duration: 300
          arrivalRate: 100000
          name: "Peak 100K RPS"
        - duration: 120
          arrivalRate: 100000
          name: "Chaos - Network issues"
        - duration: 60
          arrivalRate: 10000
          rampTo: 100
          name: "Cool down"
  
  plugins:
    metrics-by-endpoint: {}
    expect: {}
    cloud-metrics:
      provider: "gcp"
      projectId: "{{ $processEnvironment.GCP_PROJECT_ID }}"
    chaos-monkey:
      enabled: true
      phases:
        - name: "Chaos - Pod kills"
          actions:
            - type: "pod-kill"
              selector: "app=novafuse-uac"
              namespace: "{{ $processEnvironment.NAMESPACE }}"
              count: 3
              interval: 30
        - name: "Chaos - Network issues"
          actions:
            - type: "network-delay"
              selector: "app=novafuse-uac"
              namespace: "{{ $processEnvironment.NAMESPACE }}"
              delay: 1000
              jitter: 500
              duration: 60
            - type: "network-loss"
              selector: "app=novafuse-uac"
              namespace: "{{ $processEnvironment.NAMESPACE }}"
              loss: 20
              duration: 30
  
  http:
    timeout: 60
    pool: 1000
  
  processor: "./chaos-test-functions.js"
  
  defaults:
    headers:
      x-api-key: "{{ $processEnvironment.API_KEY }}"
      Content-Type: "application/json"
      Accept: "application/json"

scenarios:
  - name: "GRC API Checks"
    weight: 40
    flow:
      - function: "generateGrcCheckPayload"
      - post:
          url: "/api/v1/grc-check"
          json: "{{ grcCheck }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
  
  - name: "Connector Operations"
    weight: 30
    flow:
      - function: "generateConnectorPayload"
      - post:
          url: "/api/v1/connectors"
          json: "{{ connector }}"
          capture:
            - json: "$.id"
              as: "connectorId"
          expect:
            - statusCode: 201
      - get:
          url: "/api/v1/connectors/{{ connectorId }}"
          expect:
            - statusCode: 200
      - function: "updateConnectorPayload"
      - put:
          url: "/api/v1/connectors/{{ connectorId }}"
          json: "{{ updatedConnector }}"
          expect:
            - statusCode: 200
      - delete:
          url: "/api/v1/connectors/{{ connectorId }}"
          expect:
            - statusCode: 204
  
  - name: "Data Normalization"
    weight: 20
    flow:
      - function: "generateNormalizationPayload"
      - post:
          url: "/api/v1/normalize"
          json: "{{ normalization }}"
          expect:
            - statusCode: 200
            - contentType: "application/json"
  
  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200
            - contentType: "application/json"
