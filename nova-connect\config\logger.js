/**
 * Logger Configuration
 * 
 * This module provides a simple logger for the application.
 */

const logger = {
  info: (message, meta = {}) => {
    console.log(`[INFO] ${message}`, meta);
  },
  
  error: (message, meta = {}) => {
    console.error(`[ERROR] ${message}`, meta);
  },
  
  warn: (message, meta = {}) => {
    console.warn(`[WARN] ${message}`, meta);
  },
  
  debug: (message, meta = {}) => {
    console.debug(`[DEBUG] ${message}`, meta);
  }
};

module.exports = logger;
