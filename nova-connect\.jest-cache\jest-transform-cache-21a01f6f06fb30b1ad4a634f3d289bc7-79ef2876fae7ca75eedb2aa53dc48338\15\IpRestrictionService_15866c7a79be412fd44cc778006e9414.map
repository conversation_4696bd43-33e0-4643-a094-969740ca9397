{"version": 3, "names": ["fs", "require", "promises", "path", "ipRangeCheck", "ValidationError", "AuthorizationError", "IpRestrictionService", "constructor", "dataDir", "join", "__dirname", "ipRestrictionsFile", "defaultConfig", "enabled", "mode", "allowlist", "blocklist", "rules", "ensureDataDir", "mkdir", "recursive", "error", "console", "loadRestrictions", "data", "readFile", "JSON", "parse", "code", "saveRestrictions", "restrictions", "writeFile", "stringify", "isAllowed", "ip", "rule", "matchesRule", "action", "isInList", "ipRange", "list", "item", "includes", "addToAllowlist", "ipOrRange", "isValidIpOrRange", "push", "filter", "addToBlocklist", "removeFromAllowlist", "removeFromBlocklist", "addRule", "name", "isValidIp", "existingRuleIndex", "findIndex", "r", "removeRule", "ruleName", "ruleIndex", "splice", "updateConfig", "config", "undefined", "resetToDefault", "cidr", "split", "isValidCidr", "startIp", "endIp", "ipv4Regex", "test", "octets", "octet", "num", "parseInt", "isNaN", "module", "exports"], "sources": ["IpRestrictionService.js"], "sourcesContent": ["/**\n * IP Restriction Service\n * \n * This service handles IP-based access restrictions.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst ipRangeCheck = require('ip-range-check');\nconst { ValidationError, AuthorizationError } = require('../utils/errors');\n\nclass IpRestrictionService {\n  constructor(dataDir = path.join(__dirname, '../data')) {\n    this.dataDir = dataDir;\n    this.ipRestrictionsFile = path.join(this.dataDir, 'ip_restrictions.json');\n    this.defaultConfig = {\n      enabled: false,\n      mode: 'allowlist', // 'allowlist' or 'blocklist'\n      allowlist: [],\n      blocklist: [],\n      rules: []\n    };\n    this.ensureDataDir();\n  }\n\n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.dataDir, { recursive: true });\n    } catch (error) {\n      console.error('Error creating data directory:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Load IP restrictions from file\n   */\n  async loadRestrictions() {\n    try {\n      const data = await fs.readFile(this.ipRestrictionsFile, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      if (error.code === 'ENOENT') {\n        // File doesn't exist, return default config\n        return this.defaultConfig;\n      }\n      console.error('Error loading IP restrictions:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Save IP restrictions to file\n   */\n  async saveRestrictions(restrictions) {\n    try {\n      await fs.writeFile(this.ipRestrictionsFile, JSON.stringify(restrictions, null, 2));\n    } catch (error) {\n      console.error('Error saving IP restrictions:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Check if an IP address is allowed\n   */\n  async isAllowed(ip) {\n    const restrictions = await this.loadRestrictions();\n    \n    // If IP restrictions are disabled, allow all IPs\n    if (!restrictions.enabled) {\n      return true;\n    }\n    \n    // Check if IP matches any rule\n    for (const rule of restrictions.rules) {\n      if (this.matchesRule(ip, rule)) {\n        return rule.action === 'allow';\n      }\n    }\n    \n    // If no rule matches, check the mode\n    if (restrictions.mode === 'allowlist') {\n      // In allowlist mode, IP must be in the allowlist\n      return this.isInList(ip, restrictions.allowlist);\n    } else {\n      // In blocklist mode, IP must not be in the blocklist\n      return !this.isInList(ip, restrictions.blocklist);\n    }\n  }\n\n  /**\n   * Check if an IP address matches a rule\n   */\n  matchesRule(ip, rule) {\n    // Check if IP matches the rule's IP range\n    if (rule.ipRange) {\n      return ipRangeCheck(ip, rule.ipRange);\n    }\n    \n    // Check if IP matches the rule's IP address\n    if (rule.ip) {\n      return ip === rule.ip;\n    }\n    \n    return false;\n  }\n\n  /**\n   * Check if an IP address is in a list\n   */\n  isInList(ip, list) {\n    for (const item of list) {\n      // Check if item is an IP range\n      if (item.includes('/') || item.includes('-')) {\n        if (ipRangeCheck(ip, item)) {\n          return true;\n        }\n      } else {\n        // Check if item is an exact IP match\n        if (ip === item) {\n          return true;\n        }\n      }\n    }\n    \n    return false;\n  }\n\n  /**\n   * Add an IP address or range to the allowlist\n   */\n  async addToAllowlist(ipOrRange) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Validate IP or range\n    if (!this.isValidIpOrRange(ipOrRange)) {\n      throw new ValidationError('Invalid IP address or range');\n    }\n    \n    // Check if IP or range is already in the allowlist\n    if (restrictions.allowlist.includes(ipOrRange)) {\n      return restrictions;\n    }\n    \n    // Add IP or range to the allowlist\n    restrictions.allowlist.push(ipOrRange);\n    \n    // Remove from blocklist if present\n    const blocklist = restrictions.blocklist.filter(item => item !== ipOrRange);\n    restrictions.blocklist = blocklist;\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Add an IP address or range to the blocklist\n   */\n  async addToBlocklist(ipOrRange) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Validate IP or range\n    if (!this.isValidIpOrRange(ipOrRange)) {\n      throw new ValidationError('Invalid IP address or range');\n    }\n    \n    // Check if IP or range is already in the blocklist\n    if (restrictions.blocklist.includes(ipOrRange)) {\n      return restrictions;\n    }\n    \n    // Add IP or range to the blocklist\n    restrictions.blocklist.push(ipOrRange);\n    \n    // Remove from allowlist if present\n    const allowlist = restrictions.allowlist.filter(item => item !== ipOrRange);\n    restrictions.allowlist = allowlist;\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Remove an IP address or range from the allowlist\n   */\n  async removeFromAllowlist(ipOrRange) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Check if IP or range is in the allowlist\n    if (!restrictions.allowlist.includes(ipOrRange)) {\n      return restrictions;\n    }\n    \n    // Remove IP or range from the allowlist\n    const allowlist = restrictions.allowlist.filter(item => item !== ipOrRange);\n    restrictions.allowlist = allowlist;\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Remove an IP address or range from the blocklist\n   */\n  async removeFromBlocklist(ipOrRange) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Check if IP or range is in the blocklist\n    if (!restrictions.blocklist.includes(ipOrRange)) {\n      return restrictions;\n    }\n    \n    // Remove IP or range from the blocklist\n    const blocklist = restrictions.blocklist.filter(item => item !== ipOrRange);\n    restrictions.blocklist = blocklist;\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Add a rule\n   */\n  async addRule(rule) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Validate rule\n    if (!rule.name) {\n      throw new ValidationError('Rule name is required');\n    }\n    \n    if (!rule.action || !['allow', 'block'].includes(rule.action)) {\n      throw new ValidationError('Rule action must be \"allow\" or \"block\"');\n    }\n    \n    if (!rule.ipRange && !rule.ip) {\n      throw new ValidationError('Rule must have an IP address or range');\n    }\n    \n    if (rule.ipRange && !this.isValidIpOrRange(rule.ipRange)) {\n      throw new ValidationError('Invalid IP range');\n    }\n    \n    if (rule.ip && !this.isValidIp(rule.ip)) {\n      throw new ValidationError('Invalid IP address');\n    }\n    \n    // Check if rule with the same name already exists\n    const existingRuleIndex = restrictions.rules.findIndex(r => r.name === rule.name);\n    \n    if (existingRuleIndex !== -1) {\n      // Update existing rule\n      restrictions.rules[existingRuleIndex] = rule;\n    } else {\n      // Add new rule\n      restrictions.rules.push(rule);\n    }\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Remove a rule\n   */\n  async removeRule(ruleName) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Check if rule exists\n    const ruleIndex = restrictions.rules.findIndex(r => r.name === ruleName);\n    \n    if (ruleIndex === -1) {\n      return restrictions;\n    }\n    \n    // Remove rule\n    restrictions.rules.splice(ruleIndex, 1);\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Update IP restrictions configuration\n   */\n  async updateConfig(config) {\n    const restrictions = await this.loadRestrictions();\n    \n    // Update config\n    if (config.enabled !== undefined) {\n      restrictions.enabled = !!config.enabled;\n    }\n    \n    if (config.mode !== undefined) {\n      if (!['allowlist', 'blocklist'].includes(config.mode)) {\n        throw new ValidationError('Mode must be \"allowlist\" or \"blocklist\"');\n      }\n      \n      restrictions.mode = config.mode;\n    }\n    \n    // Save restrictions\n    await this.saveRestrictions(restrictions);\n    \n    return restrictions;\n  }\n\n  /**\n   * Reset IP restrictions to default\n   */\n  async resetToDefault() {\n    await this.saveRestrictions(this.defaultConfig);\n    return this.defaultConfig;\n  }\n\n  /**\n   * Validate an IP address or range\n   */\n  isValidIpOrRange(ipOrRange) {\n    // Check if it's an IP range with CIDR notation (e.g., ***********/24)\n    if (ipOrRange.includes('/')) {\n      const [ip, cidr] = ipOrRange.split('/');\n      return this.isValidIp(ip) && this.isValidCidr(cidr);\n    }\n    \n    // Check if it's an IP range with hyphen notation (e.g., ***********-************)\n    if (ipOrRange.includes('-')) {\n      const [startIp, endIp] = ipOrRange.split('-');\n      return this.isValidIp(startIp) && this.isValidIp(endIp);\n    }\n    \n    // Check if it's a single IP address\n    return this.isValidIp(ipOrRange);\n  }\n\n  /**\n   * Validate an IP address\n   */\n  isValidIp(ip) {\n    // Simple IPv4 validation\n    const ipv4Regex = /^(\\d{1,3}\\.){3}\\d{1,3}$/;\n    \n    if (!ipv4Regex.test(ip)) {\n      return false;\n    }\n    \n    // Check if each octet is between 0 and 255\n    const octets = ip.split('.');\n    \n    for (const octet of octets) {\n      const num = parseInt(octet, 10);\n      if (num < 0 || num > 255) {\n        return false;\n      }\n    }\n    \n    return true;\n  }\n\n  /**\n   * Validate a CIDR value\n   */\n  isValidCidr(cidr) {\n    const num = parseInt(cidr, 10);\n    return !isNaN(num) && num >= 0 && num <= 32;\n  }\n}\n\nmodule.exports = IpRestrictionService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMG,YAAY,GAAGH,OAAO,CAAC,gBAAgB,CAAC;AAC9C,MAAM;EAAEI,eAAe;EAAEC;AAAmB,CAAC,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAE1E,MAAMM,oBAAoB,CAAC;EACzBC,WAAWA,CAACC,OAAO,GAAGN,IAAI,CAACO,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAE;IACrD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,kBAAkB,GAAGT,IAAI,CAACO,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,sBAAsB,CAAC;IACzE,IAAI,CAACI,aAAa,GAAG;MACnBC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,WAAW;MAAE;MACnBC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACE,MAAMA,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMnB,EAAE,CAACoB,KAAK,CAAC,IAAI,CAACX,OAAO,EAAE;QAAEY,SAAS,EAAE;MAAK,CAAC,CAAC;IACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAME,gBAAgBA,CAAA,EAAG;IACvB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAMzB,EAAE,CAAC0B,QAAQ,CAAC,IAAI,CAACd,kBAAkB,EAAE,MAAM,CAAC;MAC/D,OAAOe,IAAI,CAACC,KAAK,CAACH,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOH,KAAK,EAAE;MACd,IAAIA,KAAK,CAACO,IAAI,KAAK,QAAQ,EAAE;QAC3B;QACA,OAAO,IAAI,CAAChB,aAAa;MAC3B;MACAU,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,gBAAgBA,CAACC,YAAY,EAAE;IACnC,IAAI;MACF,MAAM/B,EAAE,CAACgC,SAAS,CAAC,IAAI,CAACpB,kBAAkB,EAAEe,IAAI,CAACM,SAAS,CAACF,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpF,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMY,SAASA,CAACC,EAAE,EAAE;IAClB,MAAMJ,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAI,CAACO,YAAY,CAACjB,OAAO,EAAE;MACzB,OAAO,IAAI;IACb;;IAEA;IACA,KAAK,MAAMsB,IAAI,IAAIL,YAAY,CAACb,KAAK,EAAE;MACrC,IAAI,IAAI,CAACmB,WAAW,CAACF,EAAE,EAAEC,IAAI,CAAC,EAAE;QAC9B,OAAOA,IAAI,CAACE,MAAM,KAAK,OAAO;MAChC;IACF;;IAEA;IACA,IAAIP,YAAY,CAAChB,IAAI,KAAK,WAAW,EAAE;MACrC;MACA,OAAO,IAAI,CAACwB,QAAQ,CAACJ,EAAE,EAAEJ,YAAY,CAACf,SAAS,CAAC;IAClD,CAAC,MAAM;MACL;MACA,OAAO,CAAC,IAAI,CAACuB,QAAQ,CAACJ,EAAE,EAAEJ,YAAY,CAACd,SAAS,CAAC;IACnD;EACF;;EAEA;AACF;AACA;EACEoB,WAAWA,CAACF,EAAE,EAAEC,IAAI,EAAE;IACpB;IACA,IAAIA,IAAI,CAACI,OAAO,EAAE;MAChB,OAAOpC,YAAY,CAAC+B,EAAE,EAAEC,IAAI,CAACI,OAAO,CAAC;IACvC;;IAEA;IACA,IAAIJ,IAAI,CAACD,EAAE,EAAE;MACX,OAAOA,EAAE,KAAKC,IAAI,CAACD,EAAE;IACvB;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEI,QAAQA,CAACJ,EAAE,EAAEM,IAAI,EAAE;IACjB,KAAK,MAAMC,IAAI,IAAID,IAAI,EAAE;MACvB;MACA,IAAIC,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,IAAID,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC5C,IAAIvC,YAAY,CAAC+B,EAAE,EAAEO,IAAI,CAAC,EAAE;UAC1B,OAAO,IAAI;QACb;MACF,CAAC,MAAM;QACL;QACA,IAAIP,EAAE,KAAKO,IAAI,EAAE;UACf,OAAO,IAAI;QACb;MACF;IACF;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACE,MAAME,cAAcA,CAACC,SAAS,EAAE;IAC9B,MAAMd,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAI,CAAC,IAAI,CAACsB,gBAAgB,CAACD,SAAS,CAAC,EAAE;MACrC,MAAM,IAAIxC,eAAe,CAAC,6BAA6B,CAAC;IAC1D;;IAEA;IACA,IAAI0B,YAAY,CAACf,SAAS,CAAC2B,QAAQ,CAACE,SAAS,CAAC,EAAE;MAC9C,OAAOd,YAAY;IACrB;;IAEA;IACAA,YAAY,CAACf,SAAS,CAAC+B,IAAI,CAACF,SAAS,CAAC;;IAEtC;IACA,MAAM5B,SAAS,GAAGc,YAAY,CAACd,SAAS,CAAC+B,MAAM,CAACN,IAAI,IAAIA,IAAI,KAAKG,SAAS,CAAC;IAC3Ed,YAAY,CAACd,SAAS,GAAGA,SAAS;;IAElC;IACA,MAAM,IAAI,CAACa,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAMkB,cAAcA,CAACJ,SAAS,EAAE;IAC9B,MAAMd,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAI,CAAC,IAAI,CAACsB,gBAAgB,CAACD,SAAS,CAAC,EAAE;MACrC,MAAM,IAAIxC,eAAe,CAAC,6BAA6B,CAAC;IAC1D;;IAEA;IACA,IAAI0B,YAAY,CAACd,SAAS,CAAC0B,QAAQ,CAACE,SAAS,CAAC,EAAE;MAC9C,OAAOd,YAAY;IACrB;;IAEA;IACAA,YAAY,CAACd,SAAS,CAAC8B,IAAI,CAACF,SAAS,CAAC;;IAEtC;IACA,MAAM7B,SAAS,GAAGe,YAAY,CAACf,SAAS,CAACgC,MAAM,CAACN,IAAI,IAAIA,IAAI,KAAKG,SAAS,CAAC;IAC3Ed,YAAY,CAACf,SAAS,GAAGA,SAAS;;IAElC;IACA,MAAM,IAAI,CAACc,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAMmB,mBAAmBA,CAACL,SAAS,EAAE;IACnC,MAAMd,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAI,CAACO,YAAY,CAACf,SAAS,CAAC2B,QAAQ,CAACE,SAAS,CAAC,EAAE;MAC/C,OAAOd,YAAY;IACrB;;IAEA;IACA,MAAMf,SAAS,GAAGe,YAAY,CAACf,SAAS,CAACgC,MAAM,CAACN,IAAI,IAAIA,IAAI,KAAKG,SAAS,CAAC;IAC3Ed,YAAY,CAACf,SAAS,GAAGA,SAAS;;IAElC;IACA,MAAM,IAAI,CAACc,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAMoB,mBAAmBA,CAACN,SAAS,EAAE;IACnC,MAAMd,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAI,CAACO,YAAY,CAACd,SAAS,CAAC0B,QAAQ,CAACE,SAAS,CAAC,EAAE;MAC/C,OAAOd,YAAY;IACrB;;IAEA;IACA,MAAMd,SAAS,GAAGc,YAAY,CAACd,SAAS,CAAC+B,MAAM,CAACN,IAAI,IAAIA,IAAI,KAAKG,SAAS,CAAC;IAC3Ed,YAAY,CAACd,SAAS,GAAGA,SAAS;;IAElC;IACA,MAAM,IAAI,CAACa,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAMqB,OAAOA,CAAChB,IAAI,EAAE;IAClB,MAAML,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAI,CAACY,IAAI,CAACiB,IAAI,EAAE;MACd,MAAM,IAAIhD,eAAe,CAAC,uBAAuB,CAAC;IACpD;IAEA,IAAI,CAAC+B,IAAI,CAACE,MAAM,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAACK,QAAQ,CAACP,IAAI,CAACE,MAAM,CAAC,EAAE;MAC7D,MAAM,IAAIjC,eAAe,CAAC,wCAAwC,CAAC;IACrE;IAEA,IAAI,CAAC+B,IAAI,CAACI,OAAO,IAAI,CAACJ,IAAI,CAACD,EAAE,EAAE;MAC7B,MAAM,IAAI9B,eAAe,CAAC,uCAAuC,CAAC;IACpE;IAEA,IAAI+B,IAAI,CAACI,OAAO,IAAI,CAAC,IAAI,CAACM,gBAAgB,CAACV,IAAI,CAACI,OAAO,CAAC,EAAE;MACxD,MAAM,IAAInC,eAAe,CAAC,kBAAkB,CAAC;IAC/C;IAEA,IAAI+B,IAAI,CAACD,EAAE,IAAI,CAAC,IAAI,CAACmB,SAAS,CAAClB,IAAI,CAACD,EAAE,CAAC,EAAE;MACvC,MAAM,IAAI9B,eAAe,CAAC,oBAAoB,CAAC;IACjD;;IAEA;IACA,MAAMkD,iBAAiB,GAAGxB,YAAY,CAACb,KAAK,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,KAAKjB,IAAI,CAACiB,IAAI,CAAC;IAEjF,IAAIE,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACAxB,YAAY,CAACb,KAAK,CAACqC,iBAAiB,CAAC,GAAGnB,IAAI;IAC9C,CAAC,MAAM;MACL;MACAL,YAAY,CAACb,KAAK,CAAC6B,IAAI,CAACX,IAAI,CAAC;IAC/B;;IAEA;IACA,MAAM,IAAI,CAACN,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAM2B,UAAUA,CAACC,QAAQ,EAAE;IACzB,MAAM5B,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,MAAMoC,SAAS,GAAG7B,YAAY,CAACb,KAAK,CAACsC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACJ,IAAI,KAAKM,QAAQ,CAAC;IAExE,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;MACpB,OAAO7B,YAAY;IACrB;;IAEA;IACAA,YAAY,CAACb,KAAK,CAAC2C,MAAM,CAACD,SAAS,EAAE,CAAC,CAAC;;IAEvC;IACA,MAAM,IAAI,CAAC9B,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAM+B,YAAYA,CAACC,MAAM,EAAE;IACzB,MAAMhC,YAAY,GAAG,MAAM,IAAI,CAACP,gBAAgB,CAAC,CAAC;;IAElD;IACA,IAAIuC,MAAM,CAACjD,OAAO,KAAKkD,SAAS,EAAE;MAChCjC,YAAY,CAACjB,OAAO,GAAG,CAAC,CAACiD,MAAM,CAACjD,OAAO;IACzC;IAEA,IAAIiD,MAAM,CAAChD,IAAI,KAAKiD,SAAS,EAAE;MAC7B,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAACrB,QAAQ,CAACoB,MAAM,CAAChD,IAAI,CAAC,EAAE;QACrD,MAAM,IAAIV,eAAe,CAAC,yCAAyC,CAAC;MACtE;MAEA0B,YAAY,CAAChB,IAAI,GAAGgD,MAAM,CAAChD,IAAI;IACjC;;IAEA;IACA,MAAM,IAAI,CAACe,gBAAgB,CAACC,YAAY,CAAC;IAEzC,OAAOA,YAAY;EACrB;;EAEA;AACF;AACA;EACE,MAAMkC,cAAcA,CAAA,EAAG;IACrB,MAAM,IAAI,CAACnC,gBAAgB,CAAC,IAAI,CAACjB,aAAa,CAAC;IAC/C,OAAO,IAAI,CAACA,aAAa;EAC3B;;EAEA;AACF;AACA;EACEiC,gBAAgBA,CAACD,SAAS,EAAE;IAC1B;IACA,IAAIA,SAAS,CAACF,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC3B,MAAM,CAACR,EAAE,EAAE+B,IAAI,CAAC,GAAGrB,SAAS,CAACsB,KAAK,CAAC,GAAG,CAAC;MACvC,OAAO,IAAI,CAACb,SAAS,CAACnB,EAAE,CAAC,IAAI,IAAI,CAACiC,WAAW,CAACF,IAAI,CAAC;IACrD;;IAEA;IACA,IAAIrB,SAAS,CAACF,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC3B,MAAM,CAAC0B,OAAO,EAAEC,KAAK,CAAC,GAAGzB,SAAS,CAACsB,KAAK,CAAC,GAAG,CAAC;MAC7C,OAAO,IAAI,CAACb,SAAS,CAACe,OAAO,CAAC,IAAI,IAAI,CAACf,SAAS,CAACgB,KAAK,CAAC;IACzD;;IAEA;IACA,OAAO,IAAI,CAAChB,SAAS,CAACT,SAAS,CAAC;EAClC;;EAEA;AACF;AACA;EACES,SAASA,CAACnB,EAAE,EAAE;IACZ;IACA,MAAMoC,SAAS,GAAG,yBAAyB;IAE3C,IAAI,CAACA,SAAS,CAACC,IAAI,CAACrC,EAAE,CAAC,EAAE;MACvB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMsC,MAAM,GAAGtC,EAAE,CAACgC,KAAK,CAAC,GAAG,CAAC;IAE5B,KAAK,MAAMO,KAAK,IAAID,MAAM,EAAE;MAC1B,MAAME,GAAG,GAAGC,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC;MAC/B,IAAIC,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,GAAG,EAAE;QACxB,OAAO,KAAK;MACd;IACF;IAEA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACEP,WAAWA,CAACF,IAAI,EAAE;IAChB,MAAMS,GAAG,GAAGC,QAAQ,CAACV,IAAI,EAAE,EAAE,CAAC;IAC9B,OAAO,CAACW,KAAK,CAACF,GAAG,CAAC,IAAIA,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,EAAE;EAC7C;AACF;AAEAG,MAAM,CAACC,OAAO,GAAGxE,oBAAoB", "ignoreList": []}