{"version": 3, "names": ["mongoose", "require", "logger", "DEFAULT_POOL_SIZE", "DEFAULT_CONNECT_TIMEOUT_MS", "DEFAULT_SOCKET_TIMEOUT_MS", "DEFAULT_HEARTBEAT_FREQUENCY_MS", "DatabaseManager", "constructor", "config", "uri", "process", "env", "MONGODB_URI", "dbN<PERSON>", "MONGODB_DB_NAME", "poolSize", "parseInt", "MONGODB_POOL_SIZE", "connectTimeoutMS", "MONGODB_CONNECT_TIMEOUT_MS", "socketTimeoutMS", "MONGODB_SOCKET_TIMEOUT_MS", "heartbeatFrequencyMS", "MONGODB_HEARTBEAT_FREQUENCY_MS", "retryWrites", "MONGODB_RETRY_WRITES", "retryReads", "MONGODB_RETRY_READS", "connection", "isConnected", "connectionPromise", "connect", "Promise", "resolve", "reject", "info", "set", "maxPoolSize", "on", "err", "error", "message", "warn", "disconnect", "getConnection", "createIndexes", "databaseManager", "module", "exports"], "sources": ["database.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Database Configuration\n * \n * This module provides MongoDB connection management for the NovaConnect UAC.\n */\n\nconst mongoose = require('mongoose');\nconst logger = require('./logger');\n\n// Default connection options\nconst DEFAULT_POOL_SIZE = 10;\nconst DEFAULT_CONNECT_TIMEOUT_MS = 30000;\nconst DEFAULT_SOCKET_TIMEOUT_MS = 45000;\nconst DEFAULT_HEARTBEAT_FREQUENCY_MS = 10000;\n\n/**\n * Database connection manager\n */\nclass DatabaseManager {\n  constructor(config = {}) {\n    this.config = {\n      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/novafuse-uac',\n      dbName: process.env.MONGODB_DB_NAME || 'novafuse-uac',\n      poolSize: parseInt(process.env.MONGODB_POOL_SIZE) || DEFAULT_POOL_SIZE,\n      connectTimeoutMS: parseInt(process.env.MONGODB_CONNECT_TIMEOUT_MS) || DEFAULT_CONNECT_TIMEOUT_MS,\n      socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT_MS) || DEFAULT_SOCKET_TIMEOUT_MS,\n      heartbeatFrequencyMS: parseInt(process.env.MONGODB_HEARTBEAT_FREQUENCY_MS) || DEFAULT_HEARTBEAT_FREQUENCY_MS,\n      retryWrites: process.env.MONGODB_RETRY_WRITES !== 'false',\n      retryReads: process.env.MONGODB_RETRY_READS !== 'false',\n      ...config\n    };\n    \n    this.connection = null;\n    this.isConnected = false;\n    this.connectionPromise = null;\n  }\n\n  /**\n   * Connect to MongoDB\n   * @returns {Promise<mongoose.Connection>} MongoDB connection\n   */\n  async connect() {\n    // If already connecting, return the existing promise\n    if (this.connectionPromise) {\n      return this.connectionPromise;\n    }\n    \n    // If already connected, return the existing connection\n    if (this.isConnected && this.connection) {\n      return this.connection;\n    }\n    \n    // Create connection promise\n    this.connectionPromise = new Promise(async (resolve, reject) => {\n      try {\n        logger.info('Connecting to MongoDB...', { uri: this.config.uri, dbName: this.config.dbName });\n        \n        // Configure mongoose\n        mongoose.set('strictQuery', true);\n        \n        // Connect to MongoDB\n        await mongoose.connect(this.config.uri, {\n          dbName: this.config.dbName,\n          maxPoolSize: this.config.poolSize,\n          connectTimeoutMS: this.config.connectTimeoutMS,\n          socketTimeoutMS: this.config.socketTimeoutMS,\n          heartbeatFrequencyMS: this.config.heartbeatFrequencyMS,\n          retryWrites: this.config.retryWrites,\n          retryReads: this.config.retryReads\n        });\n        \n        this.connection = mongoose.connection;\n        this.isConnected = true;\n        \n        // Set up connection event handlers\n        this.connection.on('error', (err) => {\n          logger.error('MongoDB connection error:', { error: err.message });\n          this.isConnected = false;\n        });\n        \n        this.connection.on('disconnected', () => {\n          logger.warn('MongoDB disconnected');\n          this.isConnected = false;\n        });\n        \n        this.connection.on('reconnected', () => {\n          logger.info('MongoDB reconnected');\n          this.isConnected = true;\n        });\n        \n        logger.info('Connected to MongoDB successfully');\n        resolve(this.connection);\n      } catch (error) {\n        logger.error('Failed to connect to MongoDB:', { error: error.message });\n        this.isConnected = false;\n        this.connectionPromise = null;\n        reject(error);\n      }\n    });\n    \n    return this.connectionPromise;\n  }\n\n  /**\n   * Disconnect from MongoDB\n   * @returns {Promise<void>}\n   */\n  async disconnect() {\n    if (!this.isConnected || !this.connection) {\n      logger.warn('Not connected to MongoDB, nothing to disconnect');\n      return;\n    }\n    \n    try {\n      logger.info('Disconnecting from MongoDB...');\n      await mongoose.disconnect();\n      this.isConnected = false;\n      this.connection = null;\n      this.connectionPromise = null;\n      logger.info('Disconnected from MongoDB successfully');\n    } catch (error) {\n      logger.error('Failed to disconnect from MongoDB:', { error: error.message });\n      throw error;\n    }\n  }\n\n  /**\n   * Get MongoDB connection\n   * @returns {Promise<mongoose.Connection>} MongoDB connection\n   */\n  async getConnection() {\n    if (!this.isConnected || !this.connection) {\n      return this.connect();\n    }\n    \n    return this.connection;\n  }\n\n  /**\n   * Check if connected to MongoDB\n   * @returns {boolean} Connection status\n   */\n  isConnected() {\n    return this.isConnected;\n  }\n\n  /**\n   * Create indexes for collections\n   * @returns {Promise<void>}\n   */\n  async createIndexes() {\n    if (!this.isConnected || !this.connection) {\n      await this.connect();\n    }\n    \n    logger.info('Creating indexes...');\n    \n    // In a real implementation, this would create indexes for all collections\n    // For now, we'll just log a message\n    \n    logger.info('Indexes created successfully');\n  }\n}\n\n// Create singleton instance\nconst databaseManager = new DatabaseManager();\n\nmodule.exports = databaseManager;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AACpC,MAAMC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAElC;AACA,MAAME,iBAAiB,GAAG,EAAE;AAC5B,MAAMC,0BAA0B,GAAG,KAAK;AACxC,MAAMC,yBAAyB,GAAG,KAAK;AACvC,MAAMC,8BAA8B,GAAG,KAAK;;AAE5C;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EACpBC,WAAWA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAACA,MAAM,GAAG;MACZC,GAAG,EAAEC,OAAO,CAACC,GAAG,CAACC,WAAW,IAAI,wCAAwC;MACxEC,MAAM,EAAEH,OAAO,CAACC,GAAG,CAACG,eAAe,IAAI,cAAc;MACrDC,QAAQ,EAAEC,QAAQ,CAACN,OAAO,CAACC,GAAG,CAACM,iBAAiB,CAAC,IAAIf,iBAAiB;MACtEgB,gBAAgB,EAAEF,QAAQ,CAACN,OAAO,CAACC,GAAG,CAACQ,0BAA0B,CAAC,IAAIhB,0BAA0B;MAChGiB,eAAe,EAAEJ,QAAQ,CAACN,OAAO,CAACC,GAAG,CAACU,yBAAyB,CAAC,IAAIjB,yBAAyB;MAC7FkB,oBAAoB,EAAEN,QAAQ,CAACN,OAAO,CAACC,GAAG,CAACY,8BAA8B,CAAC,IAAIlB,8BAA8B;MAC5GmB,WAAW,EAAEd,OAAO,CAACC,GAAG,CAACc,oBAAoB,KAAK,OAAO;MACzDC,UAAU,EAAEhB,OAAO,CAACC,GAAG,CAACgB,mBAAmB,KAAK,OAAO;MACvD,GAAGnB;IACL,CAAC;IAED,IAAI,CAACoB,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;EAC/B;;EAEA;AACF;AACA;AACA;EACE,MAAMC,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,IAAI,CAACD,iBAAiB,EAAE;MAC1B,OAAO,IAAI,CAACA,iBAAiB;IAC/B;;IAEA;IACA,IAAI,IAAI,CAACD,WAAW,IAAI,IAAI,CAACD,UAAU,EAAE;MACvC,OAAO,IAAI,CAACA,UAAU;IACxB;;IAEA;IACA,IAAI,CAACE,iBAAiB,GAAG,IAAIE,OAAO,CAAC,OAAOC,OAAO,EAAEC,MAAM,KAAK;MAC9D,IAAI;QACFjC,MAAM,CAACkC,IAAI,CAAC,0BAA0B,EAAE;UAAE1B,GAAG,EAAE,IAAI,CAACD,MAAM,CAACC,GAAG;UAAEI,MAAM,EAAE,IAAI,CAACL,MAAM,CAACK;QAAO,CAAC,CAAC;;QAE7F;QACAd,QAAQ,CAACqC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;;QAEjC;QACA,MAAMrC,QAAQ,CAACgC,OAAO,CAAC,IAAI,CAACvB,MAAM,CAACC,GAAG,EAAE;UACtCI,MAAM,EAAE,IAAI,CAACL,MAAM,CAACK,MAAM;UAC1BwB,WAAW,EAAE,IAAI,CAAC7B,MAAM,CAACO,QAAQ;UACjCG,gBAAgB,EAAE,IAAI,CAACV,MAAM,CAACU,gBAAgB;UAC9CE,eAAe,EAAE,IAAI,CAACZ,MAAM,CAACY,eAAe;UAC5CE,oBAAoB,EAAE,IAAI,CAACd,MAAM,CAACc,oBAAoB;UACtDE,WAAW,EAAE,IAAI,CAAChB,MAAM,CAACgB,WAAW;UACpCE,UAAU,EAAE,IAAI,CAAClB,MAAM,CAACkB;QAC1B,CAAC,CAAC;QAEF,IAAI,CAACE,UAAU,GAAG7B,QAAQ,CAAC6B,UAAU;QACrC,IAAI,CAACC,WAAW,GAAG,IAAI;;QAEvB;QACA,IAAI,CAACD,UAAU,CAACU,EAAE,CAAC,OAAO,EAAGC,GAAG,IAAK;UACnCtC,MAAM,CAACuC,KAAK,CAAC,2BAA2B,EAAE;YAAEA,KAAK,EAAED,GAAG,CAACE;UAAQ,CAAC,CAAC;UACjE,IAAI,CAACZ,WAAW,GAAG,KAAK;QAC1B,CAAC,CAAC;QAEF,IAAI,CAACD,UAAU,CAACU,EAAE,CAAC,cAAc,EAAE,MAAM;UACvCrC,MAAM,CAACyC,IAAI,CAAC,sBAAsB,CAAC;UACnC,IAAI,CAACb,WAAW,GAAG,KAAK;QAC1B,CAAC,CAAC;QAEF,IAAI,CAACD,UAAU,CAACU,EAAE,CAAC,aAAa,EAAE,MAAM;UACtCrC,MAAM,CAACkC,IAAI,CAAC,qBAAqB,CAAC;UAClC,IAAI,CAACN,WAAW,GAAG,IAAI;QACzB,CAAC,CAAC;QAEF5B,MAAM,CAACkC,IAAI,CAAC,mCAAmC,CAAC;QAChDF,OAAO,CAAC,IAAI,CAACL,UAAU,CAAC;MAC1B,CAAC,CAAC,OAAOY,KAAK,EAAE;QACdvC,MAAM,CAACuC,KAAK,CAAC,+BAA+B,EAAE;UAAEA,KAAK,EAAEA,KAAK,CAACC;QAAQ,CAAC,CAAC;QACvE,IAAI,CAACZ,WAAW,GAAG,KAAK;QACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI;QAC7BI,MAAM,CAACM,KAAK,CAAC;MACf;IACF,CAAC,CAAC;IAEF,OAAO,IAAI,CAACV,iBAAiB;EAC/B;;EAEA;AACF;AACA;AACA;EACE,MAAMa,UAAUA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACd,WAAW,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MACzC3B,MAAM,CAACyC,IAAI,CAAC,iDAAiD,CAAC;MAC9D;IACF;IAEA,IAAI;MACFzC,MAAM,CAACkC,IAAI,CAAC,+BAA+B,CAAC;MAC5C,MAAMpC,QAAQ,CAAC4C,UAAU,CAAC,CAAC;MAC3B,IAAI,CAACd,WAAW,GAAG,KAAK;MACxB,IAAI,CAACD,UAAU,GAAG,IAAI;MACtB,IAAI,CAACE,iBAAiB,GAAG,IAAI;MAC7B7B,MAAM,CAACkC,IAAI,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdvC,MAAM,CAACuC,KAAK,CAAC,oCAAoC,EAAE;QAAEA,KAAK,EAAEA,KAAK,CAACC;MAAQ,CAAC,CAAC;MAC5E,MAAMD,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMI,aAAaA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACf,WAAW,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MACzC,OAAO,IAAI,CAACG,OAAO,CAAC,CAAC;IACvB;IAEA,OAAO,IAAI,CAACH,UAAU;EACxB;;EAEA;AACF;AACA;AACA;EACEC,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACA,WAAW;EACzB;;EAEA;AACF;AACA;AACA;EACE,MAAMgB,aAAaA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAChB,WAAW,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MACzC,MAAM,IAAI,CAACG,OAAO,CAAC,CAAC;IACtB;IAEA9B,MAAM,CAACkC,IAAI,CAAC,qBAAqB,CAAC;;IAElC;IACA;;IAEAlC,MAAM,CAACkC,IAAI,CAAC,8BAA8B,CAAC;EAC7C;AACF;;AAEA;AACA,MAAMW,eAAe,GAAG,IAAIxC,eAAe,CAAC,CAAC;AAE7CyC,MAAM,CAACC,OAAO,GAAGF,eAAe", "ignoreList": []}