# NovaDNA Integration with NovaVision

This module provides integration between NovaDNA and NovaVision, allowing you to leverage the existing NovaVision UI framework to render NovaDNA UI components.

## Overview

The integration consists of:

1. **NovaVisionIntegration.js** - The main integration class that maps NovaDNA API endpoints to NovaVision UI components
2. **Example Components** - React components that demonstrate how to use the integration
   - `EmergencyOverrideComponent.js` - For emergency override access
   - `ProgressiveDisclosureComponent.js` - For progressive disclosure of medical information
   - `example-usage.js` - A Next.js page example

## How It Works

The integration works by:

1. Using the existing `NovaVisionComponents` class to get UI schemas
2. Enhancing these schemas with NovaDNA API endpoints
3. Adding context-specific UI elements for emergency scenarios
4. Providing React components that use these schemas with the NovaVision renderer

## Usage

### Basic Integration

```javascript
import NovaVisionComponents from '../NovaVisionComponents';
import NovaVisionIntegration from './NovaVisionIntegration';
import NovaVisionRenderer from '@nova-ui/ui-components/NovaVisionRenderer';

// Initialize NovaVisionComponents
const novaVisionComponents = new NovaVisionComponents({
  baseUrl: '/novadna',
  theme: 'emergency'
});

// Initialize NovaVisionIntegration
const novaVisionIntegration = new NovaVisionIntegration({
  apiBaseUrl: '/api',
  novaVisionComponents
});

// Get UI schema for emergency access
const schema = novaVisionIntegration.getEmergencyAccessUI();

// Render the UI using NovaVision renderer
<NovaVisionRenderer
  schema={schema}
  onAction={handleAction}
  onSubmit={handleSubmit}
  onError={handleError}
/>
```

### Emergency Access

To implement emergency access:

```javascript
// Get emergency access UI schema
const schema = novaVisionIntegration.getEmergencyAccessUI();

// Handle form submission
function handleSubmit(formId, formData) {
  // Make API call to NovaDNA API
  fetch('/api/access/emergency', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(formData)
  })
    .then(response => response.json())
    .then(data => {
      if (data.status === 'success') {
        // Handle successful response
        displayProfile(data.data.profile);
      } else {
        // Handle error
        handleError(data.error);
      }
    });
}
```

### Emergency Override

To implement emergency override:

```javascript
// Get emergency override UI schema
const schema = novaVisionIntegration.getEmergencyOverrideUI();

// Handle form submission
function handleSubmit(formId, formData) {
  // Make API call to NovaDNA API
  fetch('/api/access/override', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token')}`
    },
    body: JSON.stringify(formData)
  })
    .then(response => response.json())
    .then(data => {
      if (data.status === 'success') {
        // Handle successful response
        displayProfile(data.data.profile);
      } else {
        // Handle error
        handleError(data.error);
      }
    });
}
```

### Progressive Disclosure

To implement progressive disclosure:

```javascript
// Get profile view UI schema with appropriate access level
const schema = novaVisionIntegration.getProfileViewUI('standard');

// Fetch profile data with context
fetch('/api/access/emergency', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  },
  body: JSON.stringify({
    formFactorId: 'form-factor-123',
    accessCode: 'ABC123',
    context: {
      emergencyType: 'CARDIAC',
      emergencySeverity: 'HIGH',
      responderType: 'PARAMEDIC',
      locationType: 'AMBULANCE'
    }
  })
})
  .then(response => response.json())
  .then(data => {
    if (data.status === 'success') {
      // Render the profile with NovaVision
      <NovaVisionRenderer
        schema={schema}
        data={data.data.profile}
        onAction={handleAction}
        onError={handleError}
      />
    }
  });
```

## Available UI Schemas

The `NovaVisionIntegration` class provides the following UI schemas:

1. `getEmergencyAccessUI()` - For emergency access interface
2. `getEmergencyOverrideUI()` - For emergency override interface
3. `getProfileViewUI(accessLevel)` - For profile view with progressive disclosure
4. `getOverrideReviewUI()` - For reviewing emergency overrides
5. `getSecurityDashboardUI()` - For security monitoring dashboard
6. `getBreakGlassProtocolUI()` - For break-glass protocol management

## Example Components

The example components demonstrate how to use the integration in a React application:

1. `EmergencyOverrideComponent.js` - A complete component for emergency override
2. `ProgressiveDisclosureComponent.js` - A complete component for progressive disclosure
3. `example-usage.js` - A Next.js page example for emergency access

These components can be used as-is or as a reference for your own implementation.

## Integration with NovaFuse

This integration leverages the existing NovaFuse ecosystem:

1. Uses NovaVision (NUUI) for UI rendering
2. Connects to NovaDNA API endpoints
3. Implements progressive disclosure based on context
4. Supports emergency override with break-glass protocol

By using this integration, you can quickly implement NovaDNA UI components without having to build them from scratch.
