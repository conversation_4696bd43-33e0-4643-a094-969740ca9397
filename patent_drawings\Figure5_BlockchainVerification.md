```
+------------------------+
| User Submits Form      |
| with Compliance Data   |
+----------+-------------+
           |
           v
+------------------------+
| Evidence Package       |
| Creation               |
|                        |
| - Form Data            |
| - User Information     |
| - Timestamp            |
| - Compliance Framework |
| - Metadata             |
+----------+-------------+
           |
           v
+------------------------+
| Cryptographic Hash     |
| Generation             |
|                        |
| SHA-256(evidence)      |
+----------+-------------+
           |
           v
+------------------------+
| Digital Signature      |
| Creation               |
|                        |
| Sign(hash, privateKey) |
+----------+-------------+
           |
           v
+------------------------+
| Blockchain Transaction |
| Submission             |
|                        |
| - Hash                 |
| - Signature            |
| - Metadata             |
+----------+-------------+
           |
           v
+------------------------+
| Transaction            |
| Confirmation           |
|                        |
| - Transaction ID       |
| - Block Number         |
| - Timestamp            |
+----------+-------------+
           |
           v
+------------------------+
| Verification Record    |
| Storage                |
|                        |
| - Evidence ID          |
| - Transaction ID       |
| - Block Information    |
+----------+-------------+
           |
           v
+------------------------+
| UI Verification        |
| Display                |
|                        |
| - Verification Badge   |
| - Verification Details |
| - Audit Information    |
+------------------------+

Figure 5: Blockchain Verification Flow
```
