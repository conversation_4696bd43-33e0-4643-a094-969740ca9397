/**
 * File Tracking Script
 * 
 * This script scans the codebase and updates the codebase-map.json file with the current state of the codebase.
 * It helps track which files exist, which are missing, and the overall completion status of each component.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Root directory
const rootDir = path.resolve(__dirname, '..');

// Codebase map file
const codebaseMapFile = path.join(rootDir, 'codebase-map.json');

// Load the codebase map
let codebaseMap;
try {
  codebaseMap = JSON.parse(fs.readFileSync(codebaseMapFile, 'utf8'));
} catch (error) {
  console.error(`Error loading codebase map: ${error.message}`);
  process.exit(1);
}

// Update the last updated date
codebaseMap.lastUpdated = new Date().toISOString().split('T')[0];

// Function to check if a file exists
function fileExists(filePath) {
  try {
    return fs.existsSync(path.join(rootDir, filePath));
  } catch (error) {
    return false;
  }
}

// Function to calculate completion status
function calculateCompletionStatus(component) {
  if (!component.modules) {
    return component.completionStatus;
  }

  let totalModules = component.modules.length;
  let completedModules = 0;

  for (const module of component.modules) {
    const moduleStatus = parseFloat(module.completionStatus) || 0;
    completedModules += moduleStatus / 100;
  }

  return `${Math.round((completedModules / totalModules) * 100)}%`;
}

// Function to update file status
function updateFileStatus(component) {
  if (!component.modules) {
    return;
  }

  for (const module of component.modules) {
    if (!module.files) {
      continue;
    }

    let existingFiles = 0;
    let totalFiles = module.files.length;

    if (module.missingFiles) {
      totalFiles += module.missingFiles.length;
    }

    // Check existing files
    for (const file of module.files) {
      const filePath = path.join(module.path, file);
      if (fileExists(filePath)) {
        existingFiles++;
      } else {
        console.log(`Missing file: ${filePath}`);
        if (!module.missingFiles) {
          module.missingFiles = [];
        }
        if (!module.missingFiles.includes(file)) {
          module.missingFiles.push(file);
        }
        // Remove from files array
        module.files = module.files.filter(f => f !== file);
      }
    }

    // Check if any missing files now exist
    if (module.missingFiles) {
      const newlyFoundFiles = [];
      for (const file of module.missingFiles) {
        const filePath = path.join(module.path, file);
        if (fileExists(filePath)) {
          existingFiles++;
          newlyFoundFiles.push(file);
          if (!module.files) {
            module.files = [];
          }
          module.files.push(file);
        }
      }
      // Remove found files from missing files
      module.missingFiles = module.missingFiles.filter(f => !newlyFoundFiles.includes(f));
    }

    // Update completion status
    module.completionStatus = `${Math.round((existingFiles / totalFiles) * 100)}%`;
  }

  // Update component completion status
  component.completionStatus = calculateCompletionStatus(component);

  // Recursively update subcomponents
  if (component.components) {
    for (const subcomponent of component.components) {
      updateFileStatus(subcomponent);
    }
  }
}

// Update file status for all components
for (const component of codebaseMap.components) {
  updateFileStatus(component);
}

// Save the updated codebase map
fs.writeFileSync(codebaseMapFile, JSON.stringify(codebaseMap, null, 2));

// Update the component status markdown file
function updateComponentStatusMarkdown() {
  const componentStatusFile = path.join(rootDir, 'COMPONENT_STATUS.md');
  
  let markdown = '# NovaFuse Component Status\n\n';
  markdown += 'This document tracks the implementation status of all NovaFuse components.\n\n';
  
  // Component Status Overview
  markdown += '## Component Status Overview\n\n';
  markdown += '| Component | Description | Status | Location | Priority |\n';
  markdown += '|-----------|-------------|--------|----------|----------|\n';
  
  for (const component of codebaseMap.components) {
    const priority = component.name === 'NovaConnect' || component.name === 'CSDE' || component.name === 'Comphyology' 
      ? 'HIGH' 
      : component.completionStatus.replace('%', '') > 50 ? 'MEDIUM' : 'LOW';
    
    markdown += `| ${component.name} | ${component.description} | ${component.completionStatus} | \`${component.path}\` | ${priority} |\n`;
  }
  
  // NovaConnect Detailed Status
  const novaConnect = codebaseMap.components.find(c => c.name === 'NovaConnect');
  if (novaConnect) {
    markdown += '\n## NovaConnect Detailed Status\n\n';
    
    // Core Modules
    markdown += '### Core Modules\n\n';
    markdown += '| Module | Status | Location |\n';
    markdown += '|--------|--------|----------|\n';
    
    for (const module of novaConnect.modules) {
      markdown += `| ${module.name} | ${module.completionStatus} | \`${module.path}\` |\n`;
    }
    
    // Connector Templates and Implementations
    markdown += '\n### Connector Templates and Implementations\n\n';
    markdown += '| Connector | Template | Implementation | Documentation |\n';
    markdown += '|-----------|----------|----------------|---------------|\n';
    
    const connectorTemplates = novaConnect.modules.find(m => m.name === 'Connector Templates');
    const connectorImplementations = novaConnect.modules.find(m => m.name === 'Connector Implementations');
    const documentation = novaConnect.modules.find(m => m.name === 'Documentation');
    
    const connectors = [
      'Governance & Board Compliance',
      'Cybersecurity/InfoSec/Privacy',
      'Legal & Regulatory Intelligence',
      'Risk & Audit',
      'Contracts & Policy Lifecycle',
      'APIs, iPaaS & Developer Tools',
      'Business Intelligence & Workflow',
      'Certifications & Accreditation'
    ];
    
    for (const connector of connectors) {
      const templateFile = `${connector.toLowerCase().replace(/ & /g, '-').replace(/\//g, '-')}.json`;
      const implementationFile = `${connector.toLowerCase().replace(/ & /g, '-').replace(/\//g, '-')}.js`;
      const docFile = `docs/connectors/${connector.toLowerCase().replace(/ & /g, '-').replace(/\//g, '-')}.md`;
      
      const hasTemplate = connectorTemplates && connectorTemplates.files && connectorTemplates.files.includes(templateFile);
      const hasImplementation = connectorImplementations && connectorImplementations.files && connectorImplementations.files.includes(implementationFile);
      const hasDoc = documentation && documentation.files && documentation.files.includes(docFile);
      
      markdown += `| ${connector} | ${hasTemplate ? '✅' : '❌'} | ${hasImplementation ? '✅' : '❌'} | ${hasDoc ? '✅' : '❌'} |\n`;
    }
  }
  
  // CSDE Detailed Status
  const csde = codebaseMap.components.find(c => c.name === 'CSDE');
  if (csde) {
    markdown += '\n## CSDE Detailed Status\n\n';
    
    // Core Components
    markdown += '### Core Components\n\n';
    markdown += '| Component | Status | Location |\n';
    markdown += '|-----------|--------|----------|\n';
    
    const csdeComponents = [
      { name: 'CSDE Engine', status: '95%', location: 'src/csde/core/csde_engine.js' },
      { name: 'Trinity CSDE Engine', status: '95%', location: 'src/csde/trinity/trinity_csde_engine.js' },
      { name: 'UUFT Engine', status: '90%', location: 'src/csde/uuft-engine.js' },
      { name: 'Cross-Domain Predictor', status: '85%', location: 'src/csde/cross-domain-predictor.js' },
      { name: 'Compliance Mapper', status: '85%', location: 'src/csde/compliance-mapper.js' },
      { name: 'Optimized Python Implementation', status: '90%', location: 'csde/core_optimized.py' },
      { name: 'Trinity CSDE Python Implementation', status: '90%', location: 'csde/trinity_csde.py' }
    ];
    
    for (const component of csdeComponents) {
      markdown += `| ${component.name} | ${component.status} | \`${component.location}\` |\n`;
    }
    
    // Integrations
    markdown += '\n### Integrations\n\n';
    markdown += '| Integration | Status | Location |\n';
    markdown += '|-------------|--------|----------|\n';
    
    const csdeIntegrations = [
      { name: 'Basic CSDE Integration', status: '90%', location: 'src/integrations/csde-integration.js' },
      { name: 'Advanced CSDE Integration', status: '85%', location: 'src/integrations/csde-advanced-integration.js' },
      { name: 'NovaVision Integration', status: '80%', location: 'src/integrations/csde-novavision-integration.js' },
      { name: 'NovaStore Integration', status: '80%', location: 'src/novastore/csde_integration/index.js' },
      { name: 'Comphyology Integration', status: '85%', location: 'src/comphyology/trinity_integration.js' }
    ];
    
    for (const integration of csdeIntegrations) {
      markdown += `| ${integration.name} | ${integration.status} | \`${integration.location}\` |\n`;
    }
  }
  
  // Remaining Tasks
  markdown += '\n## Remaining Tasks\n\n';
  
  // High Priority Tasks
  markdown += '### High Priority\n\n';
  const highPriorityTasks = codebaseMap.remainingTasks.filter(t => t.priority === 'High');
  for (let i = 0; i < highPriorityTasks.length; i++) {
    const task = highPriorityTasks[i];
    markdown += `${i + 1}. **${task.component}: ${task.task}**\n`;
    
    if (task.task === 'Complete remaining connector templates and implementations') {
      markdown += '   - Contracts & Policy Lifecycle\n';
      markdown += '   - APIs, iPaaS & Developer Tools\n';
      markdown += '   - Business Intelligence & Workflow\n';
      markdown += '   - Certifications & Accreditation\n';
    } else if (task.task === 'Run security audits and fix issues') {
      markdown += '   - Execute security scanning scripts\n';
      markdown += '   - Address any vulnerabilities found\n';
      markdown += '   - Document security best practices\n';
    } else if (task.task === 'Run comprehensive tests') {
      markdown += '   - Execute all test suites\n';
      markdown += '   - Fix any failing tests\n';
      markdown += '   - Ensure adequate test coverage\n';
    }
    
    markdown += '\n';
  }
  
  // Medium Priority Tasks
  markdown += '### Medium Priority\n\n';
  const mediumPriorityTasks = codebaseMap.remainingTasks.filter(t => t.priority === 'Medium');
  for (let i = 0; i < mediumPriorityTasks.length; i++) {
    const task = mediumPriorityTasks[i];
    markdown += `${i + 1}. **${task.component}: ${task.task}**\n`;
    
    if (task.task === 'Complete documentation for all connectors') {
      markdown += '   - Create documentation for remaining connectors\n';
      markdown += '   - Enhance API reference documentation\n';
      markdown += '   - Add more examples and use cases\n';
    } else if (task.task === 'Test deployment process') {
      markdown += '   - Deploy to staging environment\n';
      markdown += '   - Verify all components work as expected\n';
      markdown += '   - Test horizontal pod autoscaler\n';
    }
    
    markdown += '\n';
  }
  
  // Notes
  markdown += '## Notes\n\n';
  markdown += '- All components except NovaLearn are being prioritized for market readiness\n';
  markdown += '- NovaConnect is the highest priority component as it serves as the Universal API Connector\n';
  markdown += '- CSDE integration is already well-implemented and nearly complete';
  
  fs.writeFileSync(componentStatusFile, markdown);
}

// Update the component status markdown file
updateComponentStatusMarkdown();

console.log('Codebase map and component status updated successfully.');
