import React from 'react';
import '../styles/globals.css';
import Head from 'next/head';
import Navigation from '../components/Navigation';
import FloatingNovaConcierge from '../components/FloatingNovaConcierge';
import Link from 'next/link';
import { AuthProvider } from '../contexts/AuthContext';
import axios from 'axios';
import { AnimatePresence } from 'framer-motion';
import PageTransition from '../components/PageTransition';
import { useRouter } from 'next/router';

// Configure axios defaults
axios.defaults.withCredentials = true;

function MyApp({ Component, pageProps }) {
  const router = useRouter();
  return (
    <AuthProvider>
      <Head>
        <title>{pageProps.title || 'NovaFuse API Superstore - The Future of Business Security'}</title>
        <meta name="description" content={pageProps.description || 'NovaFuse API Superstore: The world\'s first Universal API Connector with built-in compliance intelligence. Revolutionizing GRC with Cyber-Safety Framework, NovaConnect UAC, and Partner Empowerment.'} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="keywords" content={pageProps.keywords || 'NovaFuse, API Superstore, GRC, Governance, Risk, Compliance, Universal API Connector, UAC, Cyber-Safety, Partner Empowerment, NovaConnect'} />
        <meta name="author" content="NovaFuse" />
        <meta name="robots" content="index, follow" />

        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={pageProps.canonical || 'https://novafuse.io'} />
        <meta property="og:title" content={pageProps.title || 'NovaFuse API Superstore - The Future of Business Security'} />
        <meta property="og:description" content={pageProps.description || 'NovaFuse API Superstore: The world\'s first Universal API Connector with built-in compliance intelligence. Revolutionizing GRC with Cyber-Safety Framework, NovaConnect UAC, and Partner Empowerment.'} />
        <meta property="og:image" content={pageProps.ogImage || '/images/novafuse-og-image.png'} />

        {/* Twitter */}
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content={pageProps.canonical || 'https://novafuse.io'} />
        <meta property="twitter:title" content={pageProps.title || 'NovaFuse API Superstore - The Future of Business Security'} />
        <meta property="twitter:description" content={pageProps.description || 'NovaFuse API Superstore: The world\'s first Universal API Connector with built-in compliance intelligence. Revolutionizing GRC with Cyber-Safety Framework, NovaConnect UAC, and Partner Empowerment.'} />
        <meta property="twitter:image" content={pageProps.ogImage || '/images/novafuse-og-image.png'} />

        {/* Canonical URL */}
        <link rel="canonical" href={pageProps.canonical || 'https://novafuse.io'} />

        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />

        {/* Structured Data / JSON-LD */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'NovaFuse',
              url: 'https://novafuse.io',
              logo: 'https://novafuse.io/images/novafuse-logo.png',
              description: 'NovaFuse API Superstore: The world\'s first Universal API Connector with built-in compliance intelligence.',
              sameAs: [
                'https://twitter.com/novafuse',
                'https://www.linkedin.com/company/novafuse',
                'https://github.com/novafuse'
              ],
              contactPoint: {
                '@type': 'ContactPoint',
                telephone: '******-NOVAFUSE',
                contactType: 'customer service',
                availableLanguage: ['English']
              },
              address: {
                '@type': 'PostalAddress',
                addressCountry: 'US'
              }
            })
          }}
        />
      </Head>

      <div className="min-h-screen flex flex-col bg-primary text-primary">
        <Navigation />

        <main className="flex-grow container mx-auto px-4 py-8">
          <AnimatePresence mode="wait">
            <PageTransition key={router.pathname}>
              <Component {...pageProps} />
            </PageTransition>
          </AnimatePresence>
        </main>

        <footer className="bg-secondary text-white py-12 mt-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">NovaFuse API Superstore</h3>
                <p className="text-gray-400">
                  Connect your GRC workflows with premium partners and services.
                </p>
              </div>
              <div>
                <h4 className="text-md font-semibold mb-4">For Partners</h4>
                <ul className="space-y-2">
                  <li><Link href="/become-partner" className="text-gray-400 hover:text-white">Become a Partner</Link></li>
                  <li><Link href="/partner-portal" className="text-gray-400 hover:text-white">Partner Portal</Link></li>
                  <li><Link href="/api-docs" className="text-gray-400 hover:text-white">API Documentation</Link></li>
                  <li><Link href="/support" className="text-gray-400 hover:text-white">Support</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="text-md font-semibold mb-4">For Customers</h4>
                <ul className="space-y-2">
                  <li><Link href="/integration-guides" className="text-gray-400 hover:text-white">Integration Guides</Link></li>
                  <li><Link href="/api-reference" className="text-gray-400 hover:text-white">API Reference</Link></li>
                  <li><Link href="/support-center" className="text-gray-400 hover:text-white">Support Center</Link></li>
                  <li><Link href="/contact" className="text-gray-400 hover:text-white">Contact Us</Link></li>
                </ul>
              </div>
              <div>
                <h4 className="text-md font-semibold mb-4">Legal</h4>
                <ul className="space-y-2">
                  <li><Link href="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link></li>
                  <li><Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
                  <li><Link href="/cookie-policy" className="text-gray-400 hover:text-white">Cookie Policy</Link></li>
                  <li><Link href="/gdpr" className="text-gray-400 hover:text-white">GDPR Compliance</Link></li>
                </ul>
              </div>
            </div>
            <div className="mt-8 pt-8 border-t border-gray-700 text-center text-gray-400">
              <p>&copy; {new Date().getFullYear()} NovaFuse. All rights reserved.</p>
            </div>
          </div>
        </footer>
      </div>

      <FloatingNovaConcierge />
    </AuthProvider>
  );
}

export default MyApp;
