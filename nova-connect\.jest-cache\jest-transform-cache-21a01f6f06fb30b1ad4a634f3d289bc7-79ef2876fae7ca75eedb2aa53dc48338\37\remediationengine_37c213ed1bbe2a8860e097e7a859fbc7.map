{"version": 3, "names": ["performance", "require", "EventEmitter", "RemediationEngine", "constructor", "options", "enableMetrics", "maxConcurrentRemediations", "defaultTimeout", "retryCount", "retry<PERSON><PERSON><PERSON>", "actions", "Map", "connectors", "activeRemediations", "metrics", "totalRemediations", "successfulRemediations", "failedRemediations", "totalSteps", "successfulSteps", "failedSteps", "totalRemediationTime", "averageRemediationTime", "totalStepTime", "averageStepTime", "registerAction", "actionName", "handler", "metadata", "Error", "set", "description", "parameters", "resourceTypes", "providers", "registerConnector", "connectorName", "connector", "executeRemediation", "scenario", "startTime", "now", "remediationId", "id", "Date", "Math", "random", "toString", "substr", "remediationContext", "toISOString", "endTime", "status", "steps", "result", "error", "emit", "remediationSteps", "length", "step", "step<PERSON><PERSON><PERSON>", "_executeStep", "push", "success", "critical", "message", "map", "action", "stack", "duration", "delete", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attempts", "get", "attempt", "resource", "finding", "stepId", "framework", "control", "previousSteps", "err", "Promise", "resolve", "setTimeout", "resolveConflict", "resolutionId", "frameworks", "conflictingRequirements", "primaryFramework", "resolution", "strategy", "appliedFramework", "justification", "join", "details", "conflicts", "req", "requirement", "applied", "conflictResolutions", "total", "successful", "failed", "totalTime", "averageTime", "handleFailedRemediation", "handlingId", "escalation", "type", "priority", "severity", "assignedTo", "escalationTarget", "failureHandling", "getMetrics", "getActions", "name", "entries", "getActiveRemediations", "Array", "from", "values", "module", "exports"], "sources": ["remediation-engine.js"], "sourcesContent": ["/**\n * NovaConnect Remediation Engine\n * \n * High-performance remediation engine capable of executing complex\n * multi-step remediation workflows for security and compliance findings.\n */\n\nconst { performance } = require('perf_hooks');\nconst EventEmitter = require('events');\n\nclass RemediationEngine extends EventEmitter {\n  constructor(options = {}) {\n    super();\n    \n    this.options = {\n      enableMetrics: true,\n      maxConcurrentRemediations: 10,\n      defaultTimeout: 30000, // 30 seconds\n      retryCount: 3,\n      retryDelay: 1000, // 1 second\n      ...options\n    };\n    \n    // Registry of remediation actions\n    this.actions = new Map();\n    \n    // Registry of connectors\n    this.connectors = new Map();\n    \n    // Active remediations\n    this.activeRemediations = new Map();\n    \n    // Initialize metrics\n    this.metrics = {\n      totalRemediations: 0,\n      successfulRemediations: 0,\n      failedRemediations: 0,\n      totalSteps: 0,\n      successfulSteps: 0,\n      failedSteps: 0,\n      totalRemediationTime: 0,\n      averageRemediationTime: 0,\n      totalStepTime: 0,\n      averageStepTime: 0\n    };\n  }\n  \n  /**\n   * Register a remediation action\n   * @param {string} actionName - Name of the action\n   * @param {Function} handler - Action handler function\n   * @param {Object} metadata - Action metadata\n   */\n  registerAction(actionName, handler, metadata = {}) {\n    if (typeof handler !== 'function') {\n      throw new Error('Action handler must be a function');\n    }\n    \n    this.actions.set(actionName, {\n      handler,\n      metadata: {\n        description: '',\n        parameters: [],\n        resourceTypes: [],\n        providers: [],\n        ...metadata\n      }\n    });\n  }\n  \n  /**\n   * Register a connector\n   * @param {string} connectorName - Name of the connector\n   * @param {Object} connector - Connector instance\n   */\n  registerConnector(connectorName, connector) {\n    this.connectors.set(connectorName, connector);\n  }\n  \n  /**\n   * Execute a remediation workflow\n   * @param {Object} scenario - Remediation scenario\n   * @returns {Object} - Remediation result\n   */\n  async executeRemediation(scenario) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    \n    // Generate a unique remediation ID if not provided\n    const remediationId = scenario.id || `rem-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    \n    // Create remediation context\n    const remediationContext = {\n      id: remediationId,\n      scenario,\n      startTime: new Date().toISOString(),\n      endTime: null,\n      status: 'in_progress',\n      steps: [],\n      result: null,\n      error: null\n    };\n    \n    // Store in active remediations\n    this.activeRemediations.set(remediationId, remediationContext);\n    \n    // Emit start event\n    this.emit('remediation:start', { remediationId, scenario });\n    \n    try {\n      // Execute each step in sequence\n      if (scenario.remediationSteps && scenario.remediationSteps.length > 0) {\n        for (const step of scenario.remediationSteps) {\n          const stepResult = await this._executeStep(step, remediationContext);\n          remediationContext.steps.push(stepResult);\n          \n          // Emit step completion event\n          this.emit('remediation:step', { \n            remediationId, \n            step: stepResult \n          });\n          \n          // If step failed and is critical, stop remediation\n          if (!stepResult.success && (step.critical !== false)) {\n            remediationContext.status = 'failed';\n            remediationContext.error = {\n              message: `Critical step ${step.id} failed: ${stepResult.error?.message || 'Unknown error'}`,\n              step: step.id\n            };\n            break;\n          }\n        }\n      }\n      \n      // If we got here without setting status to failed, it's successful\n      if (remediationContext.status === 'in_progress') {\n        remediationContext.status = 'completed';\n      }\n      \n      // Set result based on status\n      remediationContext.result = {\n        success: remediationContext.status === 'completed',\n        status: remediationContext.status,\n        steps: remediationContext.steps.map(step => ({\n          id: step.id,\n          action: step.action,\n          status: step.status,\n          success: step.success\n        }))\n      };\n    } catch (error) {\n      // Handle unexpected errors\n      remediationContext.status = 'failed';\n      remediationContext.error = {\n        message: error.message,\n        stack: error.stack\n      };\n      remediationContext.result = {\n        success: false,\n        status: 'failed',\n        error: error.message\n      };\n      \n      // Emit error event\n      this.emit('remediation:error', { \n        remediationId, \n        error \n      });\n    }\n    \n    // Set end time\n    remediationContext.endTime = new Date().toISOString();\n    \n    // Update metrics\n    if (this.options.enableMetrics) {\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      this.metrics.totalRemediations++;\n      if (remediationContext.status === 'completed') {\n        this.metrics.successfulRemediations++;\n      } else {\n        this.metrics.failedRemediations++;\n      }\n      \n      this.metrics.totalRemediationTime += duration;\n      this.metrics.averageRemediationTime = \n        this.metrics.totalRemediationTime / this.metrics.totalRemediations;\n    }\n    \n    // Emit completion event\n    this.emit('remediation:complete', { \n      remediationId, \n      result: remediationContext.result \n    });\n    \n    // Remove from active remediations\n    this.activeRemediations.delete(remediationId);\n    \n    return {\n      id: remediationId,\n      status: remediationContext.status,\n      startTime: remediationContext.startTime,\n      endTime: remediationContext.endTime,\n      steps: remediationContext.steps,\n      result: remediationContext.result,\n      error: remediationContext.error\n    };\n  }\n  \n  /**\n   * Execute a remediation step\n   * @param {Object} step - Step configuration\n   * @param {Object} context - Remediation context\n   * @returns {Object} - Step result\n   * @private\n   */\n  async _executeStep(step, context) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    \n    // Create step context\n    const stepContext = {\n      id: step.id,\n      action: step.action,\n      parameters: step.parameters || {},\n      startTime: new Date().toISOString(),\n      endTime: null,\n      status: 'in_progress',\n      success: false,\n      result: null,\n      error: null,\n      attempts: 0\n    };\n    \n    // Emit step start event\n    this.emit('remediation:step:start', { \n      remediationId: context.id, \n      step: stepContext \n    });\n    \n    try {\n      // Get the action handler\n      const action = this.actions.get(step.action);\n      \n      if (!action) {\n        throw new Error(`Action ${step.action} not registered`);\n      }\n      \n      // Execute the action with retry logic\n      let result;\n      let error;\n      let success = false;\n      \n      for (let attempt = 1; attempt <= this.options.retryCount + 1; attempt++) {\n        stepContext.attempts = attempt;\n        \n        try {\n          // Execute the action\n          result = await action.handler({\n            parameters: step.parameters || {},\n            resource: context.scenario.resource,\n            finding: context.scenario.finding,\n            context: {\n              remediationId: context.id,\n              stepId: step.id,\n              framework: context.scenario.framework,\n              control: context.scenario.control,\n              previousSteps: context.steps\n            }\n          });\n          \n          success = true;\n          break;\n        } catch (err) {\n          error = err;\n          \n          // Emit retry event\n          if (attempt <= this.options.retryCount) {\n            this.emit('remediation:step:retry', { \n              remediationId: context.id, \n              step: stepContext,\n              attempt,\n              error\n            });\n            \n            // Wait before retrying\n            await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));\n          }\n        }\n      }\n      \n      // Update step context based on result\n      if (success) {\n        stepContext.status = 'completed';\n        stepContext.success = true;\n        stepContext.result = result;\n      } else {\n        stepContext.status = 'failed';\n        stepContext.success = false;\n        stepContext.error = {\n          message: error.message,\n          stack: error.stack\n        };\n      }\n    } catch (error) {\n      // Handle unexpected errors\n      stepContext.status = 'failed';\n      stepContext.success = false;\n      stepContext.error = {\n        message: error.message,\n        stack: error.stack\n      };\n      \n      // Emit error event\n      this.emit('remediation:step:error', { \n        remediationId: context.id, \n        step: stepContext,\n        error\n      });\n    }\n    \n    // Set end time\n    stepContext.endTime = new Date().toISOString();\n    \n    // Update metrics\n    if (this.options.enableMetrics) {\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      this.metrics.totalSteps++;\n      if (stepContext.success) {\n        this.metrics.successfulSteps++;\n      } else {\n        this.metrics.failedSteps++;\n      }\n      \n      this.metrics.totalStepTime += duration;\n      this.metrics.averageStepTime = \n        this.metrics.totalStepTime / this.metrics.totalSteps;\n    }\n    \n    // Emit step completion event\n    this.emit('remediation:step:complete', { \n      remediationId: context.id, \n      step: stepContext \n    });\n    \n    return stepContext;\n  }\n  \n  /**\n   * Resolve conflicting compliance requirements\n   * @param {Object} scenario - Conflict scenario\n   * @returns {Object} - Resolution result\n   */\n  async resolveConflict(scenario) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    \n    try {\n      // Generate a unique resolution ID\n      const resolutionId = `res-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      \n      // Emit start event\n      this.emit('conflict:start', { resolutionId, scenario });\n      \n      // Implement conflict resolution logic\n      // This is a simplified implementation - in a real system, this would be more complex\n      const frameworks = scenario.frameworks;\n      const conflictingRequirements = scenario.conflictingRequirements;\n      \n      // Default strategy: apply the most stringent requirement\n      // In a real implementation, this would use more sophisticated logic\n      const primaryFramework = frameworks[0];\n      \n      // Create resolution result\n      const resolution = {\n        id: resolutionId,\n        success: true,\n        strategy: 'most_stringent',\n        appliedFramework: primaryFramework,\n        justification: `Applied the most stringent requirements from ${frameworks.join(' and ')}`,\n        details: {\n          conflicts: conflictingRequirements.map(req => ({\n            framework: req.framework,\n            control: req.control,\n            requirement: req.requirement,\n            applied: req.framework === primaryFramework\n          }))\n        },\n        startTime: new Date(startTime).toISOString(),\n        endTime: new Date().toISOString()\n      };\n      \n      // Emit completion event\n      this.emit('conflict:complete', { resolutionId, resolution });\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        \n        if (!this.metrics.conflictResolutions) {\n          this.metrics.conflictResolutions = {\n            total: 0,\n            successful: 0,\n            failed: 0,\n            totalTime: 0,\n            averageTime: 0\n          };\n        }\n        \n        this.metrics.conflictResolutions.total++;\n        this.metrics.conflictResolutions.successful++;\n        this.metrics.conflictResolutions.totalTime += duration;\n        this.metrics.conflictResolutions.averageTime = \n          this.metrics.conflictResolutions.totalTime / this.metrics.conflictResolutions.total;\n      }\n      \n      return resolution;\n    } catch (error) {\n      // Handle unexpected errors\n      const endTime = performance.now();\n      \n      // Emit error event\n      this.emit('conflict:error', { scenario, error });\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const duration = endTime - startTime;\n        \n        if (!this.metrics.conflictResolutions) {\n          this.metrics.conflictResolutions = {\n            total: 0,\n            successful: 0,\n            failed: 0,\n            totalTime: 0,\n            averageTime: 0\n          };\n        }\n        \n        this.metrics.conflictResolutions.total++;\n        this.metrics.conflictResolutions.failed++;\n        this.metrics.conflictResolutions.totalTime += duration;\n        this.metrics.conflictResolutions.averageTime = \n          this.metrics.conflictResolutions.totalTime / this.metrics.conflictResolutions.total;\n      }\n      \n      return {\n        success: false,\n        error: {\n          message: error.message,\n          stack: error.stack\n        },\n        startTime: new Date(startTime).toISOString(),\n        endTime: new Date().toISOString()\n      };\n    }\n  }\n  \n  /**\n   * Handle a failed remediation\n   * @param {Object} scenario - Failed remediation scenario\n   * @returns {Object} - Handling result\n   */\n  async handleFailedRemediation(scenario) {\n    const startTime = this.options.enableMetrics ? performance.now() : 0;\n    \n    try {\n      // Generate a unique handling ID\n      const handlingId = `handle-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n      \n      // Emit start event\n      this.emit('handling:start', { handlingId, scenario });\n      \n      // Create escalation\n      const escalation = {\n        id: `escalation-${Date.now()}`,\n        type: 'manual_intervention',\n        priority: scenario.severity || 'medium',\n        assignedTo: scenario.escalationTarget || 'security-team',\n        status: 'pending',\n        details: {\n          remediationId: scenario.remediationId,\n          framework: scenario.framework,\n          control: scenario.control,\n          resource: scenario.resource,\n          finding: scenario.finding,\n          error: scenario.error\n        }\n      };\n      \n      // In a real implementation, this would create a ticket in a ticketing system\n      // or send a notification to the appropriate team\n      \n      // Create handling result\n      const result = {\n        id: handlingId,\n        success: true,\n        escalation,\n        startTime: new Date(startTime).toISOString(),\n        endTime: new Date().toISOString()\n      };\n      \n      // Emit completion event\n      this.emit('handling:complete', { handlingId, result });\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const endTime = performance.now();\n        const duration = endTime - startTime;\n        \n        if (!this.metrics.failureHandling) {\n          this.metrics.failureHandling = {\n            total: 0,\n            successful: 0,\n            failed: 0,\n            totalTime: 0,\n            averageTime: 0\n          };\n        }\n        \n        this.metrics.failureHandling.total++;\n        this.metrics.failureHandling.successful++;\n        this.metrics.failureHandling.totalTime += duration;\n        this.metrics.failureHandling.averageTime = \n          this.metrics.failureHandling.totalTime / this.metrics.failureHandling.total;\n      }\n      \n      return result;\n    } catch (error) {\n      // Handle unexpected errors\n      const endTime = performance.now();\n      \n      // Emit error event\n      this.emit('handling:error', { scenario, error });\n      \n      // Update metrics\n      if (this.options.enableMetrics) {\n        const duration = endTime - startTime;\n        \n        if (!this.metrics.failureHandling) {\n          this.metrics.failureHandling = {\n            total: 0,\n            successful: 0,\n            failed: 0,\n            totalTime: 0,\n            averageTime: 0\n          };\n        }\n        \n        this.metrics.failureHandling.total++;\n        this.metrics.failureHandling.failed++;\n        this.metrics.failureHandling.totalTime += duration;\n        this.metrics.failureHandling.averageTime = \n          this.metrics.failureHandling.totalTime / this.metrics.failureHandling.total;\n      }\n      \n      return {\n        success: false,\n        error: {\n          message: error.message,\n          stack: error.stack\n        },\n        startTime: new Date(startTime).toISOString(),\n        endTime: new Date().toISOString()\n      };\n    }\n  }\n  \n  /**\n   * Get metrics for the remediation engine\n   * @returns {Object} - Metrics\n   */\n  getMetrics() {\n    return this.metrics;\n  }\n  \n  /**\n   * Get all registered actions\n   * @returns {Array} - Actions with metadata\n   */\n  getActions() {\n    const actions = [];\n    \n    for (const [name, action] of this.actions.entries()) {\n      actions.push({\n        name,\n        ...action.metadata\n      });\n    }\n    \n    return actions;\n  }\n  \n  /**\n   * Get all active remediations\n   * @returns {Array} - Active remediations\n   */\n  getActiveRemediations() {\n    return Array.from(this.activeRemediations.values());\n  }\n}\n\nmodule.exports = RemediationEngine;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAY,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;AAC7C,MAAMC,YAAY,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAEtC,MAAME,iBAAiB,SAASD,YAAY,CAAC;EAC3CE,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,KAAK,CAAC,CAAC;IAEP,IAAI,CAACA,OAAO,GAAG;MACbC,aAAa,EAAE,IAAI;MACnBC,yBAAyB,EAAE,EAAE;MAC7BC,cAAc,EAAE,KAAK;MAAE;MACvBC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,IAAI;MAAE;MAClB,GAAGL;IACL,CAAC;;IAED;IACA,IAAI,CAACM,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;;IAExB;IACA,IAAI,CAACC,UAAU,GAAG,IAAID,GAAG,CAAC,CAAC;;IAE3B;IACA,IAAI,CAACE,kBAAkB,GAAG,IAAIF,GAAG,CAAC,CAAC;;IAEnC;IACA,IAAI,CAACG,OAAO,GAAG;MACbC,iBAAiB,EAAE,CAAC;MACpBC,sBAAsB,EAAE,CAAC;MACzBC,kBAAkB,EAAE,CAAC;MACrBC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,CAAC;MAClBC,WAAW,EAAE,CAAC;MACdC,oBAAoB,EAAE,CAAC;MACvBC,sBAAsB,EAAE,CAAC;MACzBC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE;IACnB,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,cAAcA,CAACC,UAAU,EAAEC,OAAO,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;IACjD,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;MACjC,MAAM,IAAIE,KAAK,CAAC,mCAAmC,CAAC;IACtD;IAEA,IAAI,CAACnB,OAAO,CAACoB,GAAG,CAACJ,UAAU,EAAE;MAC3BC,OAAO;MACPC,QAAQ,EAAE;QACRG,WAAW,EAAE,EAAE;QACfC,UAAU,EAAE,EAAE;QACdC,aAAa,EAAE,EAAE;QACjBC,SAAS,EAAE,EAAE;QACb,GAAGN;MACL;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEO,iBAAiBA,CAACC,aAAa,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAACzB,UAAU,CAACkB,GAAG,CAACM,aAAa,EAAEC,SAAS,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMC,kBAAkBA,CAACC,QAAQ,EAAE;IACjC,MAAMC,SAAS,GAAG,IAAI,CAACpC,OAAO,CAACC,aAAa,GAAGN,WAAW,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;;IAEpE;IACA,MAAMC,aAAa,GAAGH,QAAQ,CAACI,EAAE,IAAI,OAAOC,IAAI,CAACH,GAAG,CAAC,CAAC,IAAII,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;IAEnG;IACA,MAAMC,kBAAkB,GAAG;MACzBN,EAAE,EAAED,aAAa;MACjBH,QAAQ;MACRC,SAAS,EAAE,IAAII,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;MACnCC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,aAAa;MACrBC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE;IACT,CAAC;;IAED;IACA,IAAI,CAAC1C,kBAAkB,CAACiB,GAAG,CAACY,aAAa,EAAEO,kBAAkB,CAAC;;IAE9D;IACA,IAAI,CAACO,IAAI,CAAC,mBAAmB,EAAE;MAAEd,aAAa;MAAEH;IAAS,CAAC,CAAC;IAE3D,IAAI;MACF;MACA,IAAIA,QAAQ,CAACkB,gBAAgB,IAAIlB,QAAQ,CAACkB,gBAAgB,CAACC,MAAM,GAAG,CAAC,EAAE;QACrE,KAAK,MAAMC,IAAI,IAAIpB,QAAQ,CAACkB,gBAAgB,EAAE;UAC5C,MAAMG,UAAU,GAAG,MAAM,IAAI,CAACC,YAAY,CAACF,IAAI,EAAEV,kBAAkB,CAAC;UACpEA,kBAAkB,CAACI,KAAK,CAACS,IAAI,CAACF,UAAU,CAAC;;UAEzC;UACA,IAAI,CAACJ,IAAI,CAAC,kBAAkB,EAAE;YAC5Bd,aAAa;YACbiB,IAAI,EAAEC;UACR,CAAC,CAAC;;UAEF;UACA,IAAI,CAACA,UAAU,CAACG,OAAO,IAAKJ,IAAI,CAACK,QAAQ,KAAK,KAAM,EAAE;YACpDf,kBAAkB,CAACG,MAAM,GAAG,QAAQ;YACpCH,kBAAkB,CAACM,KAAK,GAAG;cACzBU,OAAO,EAAE,iBAAiBN,IAAI,CAAChB,EAAE,YAAYiB,UAAU,CAACL,KAAK,EAAEU,OAAO,IAAI,eAAe,EAAE;cAC3FN,IAAI,EAAEA,IAAI,CAAChB;YACb,CAAC;YACD;UACF;QACF;MACF;;MAEA;MACA,IAAIM,kBAAkB,CAACG,MAAM,KAAK,aAAa,EAAE;QAC/CH,kBAAkB,CAACG,MAAM,GAAG,WAAW;MACzC;;MAEA;MACAH,kBAAkB,CAACK,MAAM,GAAG;QAC1BS,OAAO,EAAEd,kBAAkB,CAACG,MAAM,KAAK,WAAW;QAClDA,MAAM,EAAEH,kBAAkB,CAACG,MAAM;QACjCC,KAAK,EAAEJ,kBAAkB,CAACI,KAAK,CAACa,GAAG,CAACP,IAAI,KAAK;UAC3ChB,EAAE,EAAEgB,IAAI,CAAChB,EAAE;UACXwB,MAAM,EAAER,IAAI,CAACQ,MAAM;UACnBf,MAAM,EAAEO,IAAI,CAACP,MAAM;UACnBW,OAAO,EAAEJ,IAAI,CAACI;QAChB,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,CAAC,OAAOR,KAAK,EAAE;MACd;MACAN,kBAAkB,CAACG,MAAM,GAAG,QAAQ;MACpCH,kBAAkB,CAACM,KAAK,GAAG;QACzBU,OAAO,EAAEV,KAAK,CAACU,OAAO;QACtBG,KAAK,EAAEb,KAAK,CAACa;MACf,CAAC;MACDnB,kBAAkB,CAACK,MAAM,GAAG;QAC1BS,OAAO,EAAE,KAAK;QACdX,MAAM,EAAE,QAAQ;QAChBG,KAAK,EAAEA,KAAK,CAACU;MACf,CAAC;;MAED;MACA,IAAI,CAACT,IAAI,CAAC,mBAAmB,EAAE;QAC7Bd,aAAa;QACba;MACF,CAAC,CAAC;IACJ;;IAEA;IACAN,kBAAkB,CAACE,OAAO,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;;IAErD;IACA,IAAI,IAAI,CAAC9C,OAAO,CAACC,aAAa,EAAE;MAC9B,MAAM8C,OAAO,GAAGpD,WAAW,CAAC0C,GAAG,CAAC,CAAC;MACjC,MAAM4B,QAAQ,GAAGlB,OAAO,GAAGX,SAAS;MAEpC,IAAI,CAAC1B,OAAO,CAACC,iBAAiB,EAAE;MAChC,IAAIkC,kBAAkB,CAACG,MAAM,KAAK,WAAW,EAAE;QAC7C,IAAI,CAACtC,OAAO,CAACE,sBAAsB,EAAE;MACvC,CAAC,MAAM;QACL,IAAI,CAACF,OAAO,CAACG,kBAAkB,EAAE;MACnC;MAEA,IAAI,CAACH,OAAO,CAACO,oBAAoB,IAAIgD,QAAQ;MAC7C,IAAI,CAACvD,OAAO,CAACQ,sBAAsB,GACjC,IAAI,CAACR,OAAO,CAACO,oBAAoB,GAAG,IAAI,CAACP,OAAO,CAACC,iBAAiB;IACtE;;IAEA;IACA,IAAI,CAACyC,IAAI,CAAC,sBAAsB,EAAE;MAChCd,aAAa;MACbY,MAAM,EAAEL,kBAAkB,CAACK;IAC7B,CAAC,CAAC;;IAEF;IACA,IAAI,CAACzC,kBAAkB,CAACyD,MAAM,CAAC5B,aAAa,CAAC;IAE7C,OAAO;MACLC,EAAE,EAAED,aAAa;MACjBU,MAAM,EAAEH,kBAAkB,CAACG,MAAM;MACjCZ,SAAS,EAAES,kBAAkB,CAACT,SAAS;MACvCW,OAAO,EAAEF,kBAAkB,CAACE,OAAO;MACnCE,KAAK,EAAEJ,kBAAkB,CAACI,KAAK;MAC/BC,MAAM,EAAEL,kBAAkB,CAACK,MAAM;MACjCC,KAAK,EAAEN,kBAAkB,CAACM;IAC5B,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMM,YAAYA,CAACF,IAAI,EAAEY,OAAO,EAAE;IAChC,MAAM/B,SAAS,GAAG,IAAI,CAACpC,OAAO,CAACC,aAAa,GAAGN,WAAW,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;;IAEpE;IACA,MAAM+B,WAAW,GAAG;MAClB7B,EAAE,EAAEgB,IAAI,CAAChB,EAAE;MACXwB,MAAM,EAAER,IAAI,CAACQ,MAAM;MACnBnC,UAAU,EAAE2B,IAAI,CAAC3B,UAAU,IAAI,CAAC,CAAC;MACjCQ,SAAS,EAAE,IAAII,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;MACnCC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,aAAa;MACrBW,OAAO,EAAE,KAAK;MACdT,MAAM,EAAE,IAAI;MACZC,KAAK,EAAE,IAAI;MACXkB,QAAQ,EAAE;IACZ,CAAC;;IAED;IACA,IAAI,CAACjB,IAAI,CAAC,wBAAwB,EAAE;MAClCd,aAAa,EAAE6B,OAAO,CAAC5B,EAAE;MACzBgB,IAAI,EAAEa;IACR,CAAC,CAAC;IAEF,IAAI;MACF;MACA,MAAML,MAAM,GAAG,IAAI,CAACzD,OAAO,CAACgE,GAAG,CAACf,IAAI,CAACQ,MAAM,CAAC;MAE5C,IAAI,CAACA,MAAM,EAAE;QACX,MAAM,IAAItC,KAAK,CAAC,UAAU8B,IAAI,CAACQ,MAAM,iBAAiB,CAAC;MACzD;;MAEA;MACA,IAAIb,MAAM;MACV,IAAIC,KAAK;MACT,IAAIQ,OAAO,GAAG,KAAK;MAEnB,KAAK,IAAIY,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAI,IAAI,CAACvE,OAAO,CAACI,UAAU,GAAG,CAAC,EAAEmE,OAAO,EAAE,EAAE;QACvEH,WAAW,CAACC,QAAQ,GAAGE,OAAO;QAE9B,IAAI;UACF;UACArB,MAAM,GAAG,MAAMa,MAAM,CAACxC,OAAO,CAAC;YAC5BK,UAAU,EAAE2B,IAAI,CAAC3B,UAAU,IAAI,CAAC,CAAC;YACjC4C,QAAQ,EAAEL,OAAO,CAAChC,QAAQ,CAACqC,QAAQ;YACnCC,OAAO,EAAEN,OAAO,CAAChC,QAAQ,CAACsC,OAAO;YACjCN,OAAO,EAAE;cACP7B,aAAa,EAAE6B,OAAO,CAAC5B,EAAE;cACzBmC,MAAM,EAAEnB,IAAI,CAAChB,EAAE;cACfoC,SAAS,EAAER,OAAO,CAAChC,QAAQ,CAACwC,SAAS;cACrCC,OAAO,EAAET,OAAO,CAAChC,QAAQ,CAACyC,OAAO;cACjCC,aAAa,EAAEV,OAAO,CAAClB;YACzB;UACF,CAAC,CAAC;UAEFU,OAAO,GAAG,IAAI;UACd;QACF,CAAC,CAAC,OAAOmB,GAAG,EAAE;UACZ3B,KAAK,GAAG2B,GAAG;;UAEX;UACA,IAAIP,OAAO,IAAI,IAAI,CAACvE,OAAO,CAACI,UAAU,EAAE;YACtC,IAAI,CAACgD,IAAI,CAAC,wBAAwB,EAAE;cAClCd,aAAa,EAAE6B,OAAO,CAAC5B,EAAE;cACzBgB,IAAI,EAAEa,WAAW;cACjBG,OAAO;cACPpB;YACF,CAAC,CAAC;;YAEF;YACA,MAAM,IAAI4B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAChF,OAAO,CAACK,UAAU,CAAC,CAAC;UAC5E;QACF;MACF;;MAEA;MACA,IAAIsD,OAAO,EAAE;QACXS,WAAW,CAACpB,MAAM,GAAG,WAAW;QAChCoB,WAAW,CAACT,OAAO,GAAG,IAAI;QAC1BS,WAAW,CAAClB,MAAM,GAAGA,MAAM;MAC7B,CAAC,MAAM;QACLkB,WAAW,CAACpB,MAAM,GAAG,QAAQ;QAC7BoB,WAAW,CAACT,OAAO,GAAG,KAAK;QAC3BS,WAAW,CAACjB,KAAK,GAAG;UAClBU,OAAO,EAAEV,KAAK,CAACU,OAAO;UACtBG,KAAK,EAAEb,KAAK,CAACa;QACf,CAAC;MACH;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd;MACAiB,WAAW,CAACpB,MAAM,GAAG,QAAQ;MAC7BoB,WAAW,CAACT,OAAO,GAAG,KAAK;MAC3BS,WAAW,CAACjB,KAAK,GAAG;QAClBU,OAAO,EAAEV,KAAK,CAACU,OAAO;QACtBG,KAAK,EAAEb,KAAK,CAACa;MACf,CAAC;;MAED;MACA,IAAI,CAACZ,IAAI,CAAC,wBAAwB,EAAE;QAClCd,aAAa,EAAE6B,OAAO,CAAC5B,EAAE;QACzBgB,IAAI,EAAEa,WAAW;QACjBjB;MACF,CAAC,CAAC;IACJ;;IAEA;IACAiB,WAAW,CAACrB,OAAO,GAAG,IAAIP,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;;IAE9C;IACA,IAAI,IAAI,CAAC9C,OAAO,CAACC,aAAa,EAAE;MAC9B,MAAM8C,OAAO,GAAGpD,WAAW,CAAC0C,GAAG,CAAC,CAAC;MACjC,MAAM4B,QAAQ,GAAGlB,OAAO,GAAGX,SAAS;MAEpC,IAAI,CAAC1B,OAAO,CAACI,UAAU,EAAE;MACzB,IAAIsD,WAAW,CAACT,OAAO,EAAE;QACvB,IAAI,CAACjD,OAAO,CAACK,eAAe,EAAE;MAChC,CAAC,MAAM;QACL,IAAI,CAACL,OAAO,CAACM,WAAW,EAAE;MAC5B;MAEA,IAAI,CAACN,OAAO,CAACS,aAAa,IAAI8C,QAAQ;MACtC,IAAI,CAACvD,OAAO,CAACU,eAAe,GAC1B,IAAI,CAACV,OAAO,CAACS,aAAa,GAAG,IAAI,CAACT,OAAO,CAACI,UAAU;IACxD;;IAEA;IACA,IAAI,CAACsC,IAAI,CAAC,2BAA2B,EAAE;MACrCd,aAAa,EAAE6B,OAAO,CAAC5B,EAAE;MACzBgB,IAAI,EAAEa;IACR,CAAC,CAAC;IAEF,OAAOA,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMc,eAAeA,CAAC/C,QAAQ,EAAE;IAC9B,MAAMC,SAAS,GAAG,IAAI,CAACpC,OAAO,CAACC,aAAa,GAAGN,WAAW,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;IAEpE,IAAI;MACF;MACA,MAAM8C,YAAY,GAAG,OAAO3C,IAAI,CAACH,GAAG,CAAC,CAAC,IAAII,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;MAEnF;MACA,IAAI,CAACQ,IAAI,CAAC,gBAAgB,EAAE;QAAE+B,YAAY;QAAEhD;MAAS,CAAC,CAAC;;MAEvD;MACA;MACA,MAAMiD,UAAU,GAAGjD,QAAQ,CAACiD,UAAU;MACtC,MAAMC,uBAAuB,GAAGlD,QAAQ,CAACkD,uBAAuB;;MAEhE;MACA;MACA,MAAMC,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;;MAEtC;MACA,MAAMG,UAAU,GAAG;QACjBhD,EAAE,EAAE4C,YAAY;QAChBxB,OAAO,EAAE,IAAI;QACb6B,QAAQ,EAAE,gBAAgB;QAC1BC,gBAAgB,EAAEH,gBAAgB;QAClCI,aAAa,EAAE,gDAAgDN,UAAU,CAACO,IAAI,CAAC,OAAO,CAAC,EAAE;QACzFC,OAAO,EAAE;UACPC,SAAS,EAAER,uBAAuB,CAACvB,GAAG,CAACgC,GAAG,KAAK;YAC7CnB,SAAS,EAAEmB,GAAG,CAACnB,SAAS;YACxBC,OAAO,EAAEkB,GAAG,CAAClB,OAAO;YACpBmB,WAAW,EAAED,GAAG,CAACC,WAAW;YAC5BC,OAAO,EAAEF,GAAG,CAACnB,SAAS,KAAKW;UAC7B,CAAC,CAAC;QACJ,CAAC;QACDlD,SAAS,EAAE,IAAII,IAAI,CAACJ,SAAS,CAAC,CAACU,WAAW,CAAC,CAAC;QAC5CC,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;MAClC,CAAC;;MAED;MACA,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAE;QAAE+B,YAAY;QAAEI;MAAW,CAAC,CAAC;;MAE5D;MACA,IAAI,IAAI,CAACvF,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM8C,OAAO,GAAGpD,WAAW,CAAC0C,GAAG,CAAC,CAAC;QACjC,MAAM4B,QAAQ,GAAGlB,OAAO,GAAGX,SAAS;QAEpC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAACuF,mBAAmB,EAAE;UACrC,IAAI,CAACvF,OAAO,CAACuF,mBAAmB,GAAG;YACjCC,KAAK,EAAE,CAAC;YACRC,UAAU,EAAE,CAAC;YACbC,MAAM,EAAE,CAAC;YACTC,SAAS,EAAE,CAAC;YACZC,WAAW,EAAE;UACf,CAAC;QACH;QAEA,IAAI,CAAC5F,OAAO,CAACuF,mBAAmB,CAACC,KAAK,EAAE;QACxC,IAAI,CAACxF,OAAO,CAACuF,mBAAmB,CAACE,UAAU,EAAE;QAC7C,IAAI,CAACzF,OAAO,CAACuF,mBAAmB,CAACI,SAAS,IAAIpC,QAAQ;QACtD,IAAI,CAACvD,OAAO,CAACuF,mBAAmB,CAACK,WAAW,GAC1C,IAAI,CAAC5F,OAAO,CAACuF,mBAAmB,CAACI,SAAS,GAAG,IAAI,CAAC3F,OAAO,CAACuF,mBAAmB,CAACC,KAAK;MACvF;MAEA,OAAOX,UAAU;IACnB,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACd;MACA,MAAMJ,OAAO,GAAGpD,WAAW,CAAC0C,GAAG,CAAC,CAAC;;MAEjC;MACA,IAAI,CAACe,IAAI,CAAC,gBAAgB,EAAE;QAAEjB,QAAQ;QAAEgB;MAAM,CAAC,CAAC;;MAEhD;MACA,IAAI,IAAI,CAACnD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAMgE,QAAQ,GAAGlB,OAAO,GAAGX,SAAS;QAEpC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAACuF,mBAAmB,EAAE;UACrC,IAAI,CAACvF,OAAO,CAACuF,mBAAmB,GAAG;YACjCC,KAAK,EAAE,CAAC;YACRC,UAAU,EAAE,CAAC;YACbC,MAAM,EAAE,CAAC;YACTC,SAAS,EAAE,CAAC;YACZC,WAAW,EAAE;UACf,CAAC;QACH;QAEA,IAAI,CAAC5F,OAAO,CAACuF,mBAAmB,CAACC,KAAK,EAAE;QACxC,IAAI,CAACxF,OAAO,CAACuF,mBAAmB,CAACG,MAAM,EAAE;QACzC,IAAI,CAAC1F,OAAO,CAACuF,mBAAmB,CAACI,SAAS,IAAIpC,QAAQ;QACtD,IAAI,CAACvD,OAAO,CAACuF,mBAAmB,CAACK,WAAW,GAC1C,IAAI,CAAC5F,OAAO,CAACuF,mBAAmB,CAACI,SAAS,GAAG,IAAI,CAAC3F,OAAO,CAACuF,mBAAmB,CAACC,KAAK;MACvF;MAEA,OAAO;QACLvC,OAAO,EAAE,KAAK;QACdR,KAAK,EAAE;UACLU,OAAO,EAAEV,KAAK,CAACU,OAAO;UACtBG,KAAK,EAAEb,KAAK,CAACa;QACf,CAAC;QACD5B,SAAS,EAAE,IAAII,IAAI,CAACJ,SAAS,CAAC,CAACU,WAAW,CAAC,CAAC;QAC5CC,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;MAClC,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMyD,uBAAuBA,CAACpE,QAAQ,EAAE;IACtC,MAAMC,SAAS,GAAG,IAAI,CAACpC,OAAO,CAACC,aAAa,GAAGN,WAAW,CAAC0C,GAAG,CAAC,CAAC,GAAG,CAAC;IAEpE,IAAI;MACF;MACA,MAAMmE,UAAU,GAAG,UAAUhE,IAAI,CAACH,GAAG,CAAC,CAAC,IAAII,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;MAEpF;MACA,IAAI,CAACQ,IAAI,CAAC,gBAAgB,EAAE;QAAEoD,UAAU;QAAErE;MAAS,CAAC,CAAC;;MAErD;MACA,MAAMsE,UAAU,GAAG;QACjBlE,EAAE,EAAE,cAAcC,IAAI,CAACH,GAAG,CAAC,CAAC,EAAE;QAC9BqE,IAAI,EAAE,qBAAqB;QAC3BC,QAAQ,EAAExE,QAAQ,CAACyE,QAAQ,IAAI,QAAQ;QACvCC,UAAU,EAAE1E,QAAQ,CAAC2E,gBAAgB,IAAI,eAAe;QACxD9D,MAAM,EAAE,SAAS;QACjB4C,OAAO,EAAE;UACPtD,aAAa,EAAEH,QAAQ,CAACG,aAAa;UACrCqC,SAAS,EAAExC,QAAQ,CAACwC,SAAS;UAC7BC,OAAO,EAAEzC,QAAQ,CAACyC,OAAO;UACzBJ,QAAQ,EAAErC,QAAQ,CAACqC,QAAQ;UAC3BC,OAAO,EAAEtC,QAAQ,CAACsC,OAAO;UACzBtB,KAAK,EAAEhB,QAAQ,CAACgB;QAClB;MACF,CAAC;;MAED;MACA;;MAEA;MACA,MAAMD,MAAM,GAAG;QACbX,EAAE,EAAEiE,UAAU;QACd7C,OAAO,EAAE,IAAI;QACb8C,UAAU;QACVrE,SAAS,EAAE,IAAII,IAAI,CAACJ,SAAS,CAAC,CAACU,WAAW,CAAC,CAAC;QAC5CC,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;MAClC,CAAC;;MAED;MACA,IAAI,CAACM,IAAI,CAAC,mBAAmB,EAAE;QAAEoD,UAAU;QAAEtD;MAAO,CAAC,CAAC;;MAEtD;MACA,IAAI,IAAI,CAAClD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAM8C,OAAO,GAAGpD,WAAW,CAAC0C,GAAG,CAAC,CAAC;QACjC,MAAM4B,QAAQ,GAAGlB,OAAO,GAAGX,SAAS;QAEpC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAACqG,eAAe,EAAE;UACjC,IAAI,CAACrG,OAAO,CAACqG,eAAe,GAAG;YAC7Bb,KAAK,EAAE,CAAC;YACRC,UAAU,EAAE,CAAC;YACbC,MAAM,EAAE,CAAC;YACTC,SAAS,EAAE,CAAC;YACZC,WAAW,EAAE;UACf,CAAC;QACH;QAEA,IAAI,CAAC5F,OAAO,CAACqG,eAAe,CAACb,KAAK,EAAE;QACpC,IAAI,CAACxF,OAAO,CAACqG,eAAe,CAACZ,UAAU,EAAE;QACzC,IAAI,CAACzF,OAAO,CAACqG,eAAe,CAACV,SAAS,IAAIpC,QAAQ;QAClD,IAAI,CAACvD,OAAO,CAACqG,eAAe,CAACT,WAAW,GACtC,IAAI,CAAC5F,OAAO,CAACqG,eAAe,CAACV,SAAS,GAAG,IAAI,CAAC3F,OAAO,CAACqG,eAAe,CAACb,KAAK;MAC/E;MAEA,OAAOhD,MAAM;IACf,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd;MACA,MAAMJ,OAAO,GAAGpD,WAAW,CAAC0C,GAAG,CAAC,CAAC;;MAEjC;MACA,IAAI,CAACe,IAAI,CAAC,gBAAgB,EAAE;QAAEjB,QAAQ;QAAEgB;MAAM,CAAC,CAAC;;MAEhD;MACA,IAAI,IAAI,CAACnD,OAAO,CAACC,aAAa,EAAE;QAC9B,MAAMgE,QAAQ,GAAGlB,OAAO,GAAGX,SAAS;QAEpC,IAAI,CAAC,IAAI,CAAC1B,OAAO,CAACqG,eAAe,EAAE;UACjC,IAAI,CAACrG,OAAO,CAACqG,eAAe,GAAG;YAC7Bb,KAAK,EAAE,CAAC;YACRC,UAAU,EAAE,CAAC;YACbC,MAAM,EAAE,CAAC;YACTC,SAAS,EAAE,CAAC;YACZC,WAAW,EAAE;UACf,CAAC;QACH;QAEA,IAAI,CAAC5F,OAAO,CAACqG,eAAe,CAACb,KAAK,EAAE;QACpC,IAAI,CAACxF,OAAO,CAACqG,eAAe,CAACX,MAAM,EAAE;QACrC,IAAI,CAAC1F,OAAO,CAACqG,eAAe,CAACV,SAAS,IAAIpC,QAAQ;QAClD,IAAI,CAACvD,OAAO,CAACqG,eAAe,CAACT,WAAW,GACtC,IAAI,CAAC5F,OAAO,CAACqG,eAAe,CAACV,SAAS,GAAG,IAAI,CAAC3F,OAAO,CAACqG,eAAe,CAACb,KAAK;MAC/E;MAEA,OAAO;QACLvC,OAAO,EAAE,KAAK;QACdR,KAAK,EAAE;UACLU,OAAO,EAAEV,KAAK,CAACU,OAAO;UACtBG,KAAK,EAAEb,KAAK,CAACa;QACf,CAAC;QACD5B,SAAS,EAAE,IAAII,IAAI,CAACJ,SAAS,CAAC,CAACU,WAAW,CAAC,CAAC;QAC5CC,OAAO,EAAE,IAAIP,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC;MAClC,CAAC;IACH;EACF;;EAEA;AACF;AACA;AACA;EACEkE,UAAUA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtG,OAAO;EACrB;;EAEA;AACF;AACA;AACA;EACEuG,UAAUA,CAAA,EAAG;IACX,MAAM3G,OAAO,GAAG,EAAE;IAElB,KAAK,MAAM,CAAC4G,IAAI,EAAEnD,MAAM,CAAC,IAAI,IAAI,CAACzD,OAAO,CAAC6G,OAAO,CAAC,CAAC,EAAE;MACnD7G,OAAO,CAACoD,IAAI,CAAC;QACXwD,IAAI;QACJ,GAAGnD,MAAM,CAACvC;MACZ,CAAC,CAAC;IACJ;IAEA,OAAOlB,OAAO;EAChB;;EAEA;AACF;AACA;AACA;EACE8G,qBAAqBA,CAAA,EAAG;IACtB,OAAOC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7G,kBAAkB,CAAC8G,MAAM,CAAC,CAAC,CAAC;EACrD;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG3H,iBAAiB", "ignoreList": []}