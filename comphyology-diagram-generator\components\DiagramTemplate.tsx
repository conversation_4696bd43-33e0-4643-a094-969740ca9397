import React from 'react';
import styled from 'styled-components';

// Types
interface Point {
  x: number;
  y: number;
}

interface Connection {
  start: Point;
  end: Point;
  type?: 'line' | 'arrow';
  dashed?: boolean;
}

interface DiagramElement {
  id: string;
  top: number;
  left: number;
  width: number;
  text: string;
  number?: string;
  bold?: boolean;
  fontSize?: string;
  backgroundColor?: string;
}

interface DiagramTemplateProps {
  elements: DiagramElement[];
  connections: Connection[];
  width?: string;
  height?: string;
}

// Styled components
const DiagramContainer = styled.div<{ width?: string; height?: string }>`
  position: relative;
  padding: 40px;
  width: ${props => props.width || '800px'};
  height: ${props => props.height || 'auto'};
  min-height: 600px;
  background-color: white;
  border: 1px solid #ccc;
  box-sizing: border-box;
`;

const BubbleContainer = styled.div<{ 
  top: number; 
  left: number; 
  width: number; 
  backgroundColor?: string;
}>`
  position: absolute;
  top: ${props => props.top}px;
  left: ${props => props.left}px;
  width: ${props => props.width}px;
  padding: 15px;
  background-color: ${props => props.backgroundColor || '#f0f0f0'};
  border-radius: 8px;
  border: 2px solid #333;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const ComponentNumber = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  background-color: black;
  color: white;
  padding: 2px 6px;
  border-radius: 4px 0 4px 0;
  font-weight: bold;
  font-size: 12px;
`;

const BubbleText = styled.div<{ 
  fontSize?: string; 
  bold?: boolean; 
  hasNumber?: boolean;
}>`
  font-family: Arial, sans-serif;
  font-size: ${props => props.fontSize || '14px'};
  font-weight: ${props => props.bold ? 'bold' : 'normal'};
  text-align: center;
  margin-top: ${props => props.hasNumber ? '10px' : '0'};
`;

const ConnectingLine = styled.svg`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
`;

// Arrow marker definition for SVG
const ArrowMarker = () => (
  <defs>
    <marker
      id="arrowhead"
      markerWidth="10"
      markerHeight="7"
      refX="9"
      refY="3.5"
      orient="auto"
    >
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
);

// Main component
const DiagramTemplate: React.FC<DiagramTemplateProps> = ({ 
  elements, 
  connections, 
  width, 
  height 
}) => {
  return (
    <DiagramContainer width={width} height={height}>
      {/* Render connecting lines first (behind bubbles) */}
      <ConnectingLine>
        <ArrowMarker />
        {connections.map((connection, index) => (
          <line
            key={`line-${index}`}
            x1={connection.start.x}
            y1={connection.start.y}
            x2={connection.end.x}
            y2={connection.end.y}
            stroke="#333"
            strokeWidth="2"
            strokeDasharray={connection.dashed ? "5,5" : "none"}
            markerEnd={connection.type === 'arrow' ? "url(#arrowhead)" : "none"}
          />
        ))}
      </ConnectingLine>
      
      {/* Render bubbles on top of lines */}
      {elements.map((element) => (
        <BubbleContainer
          key={element.id}
          top={element.top}
          left={element.left}
          width={element.width}
          backgroundColor={element.backgroundColor}
        >
          {element.number && (
            <ComponentNumber>{element.number}</ComponentNumber>
          )}
          <BubbleText 
            fontSize={element.fontSize} 
            bold={element.bold}
            hasNumber={!!element.number}
          >
            {element.text}
          </BubbleText>
        </BubbleContainer>
      ))}
    </DiagramContainer>
  );
};

export default DiagramTemplate;
