/**
 * Logger Middleware Tests
 *
 * This file contains unit tests for the logger middleware.
 */

const { httpLogger, requestLogger, responseLogger, errorLogger } = require('../../../middleware/logger');
const logger = require('../../../config/logger');

// Mock morgan
jest.mock('morgan', () => {
  const mockMorgan = jest.fn(() => jest.fn());
  mockMorgan.token = jest.fn();
  return mockMorgan;
});

// Mock the logger
jest.mock('../../../config/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  stream: { write: jest.fn() }
}));

describe('Logger Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup request, response and next function
    req = {
      method: 'GET',
      url: '/test',
      ip: '127.0.0.1',
      user: { username: 'testuser' },
      body: {},
      startTime: Date.now()
    };

    res = {
      statusCode: 200,
      end: jest.fn()
    };

    next = jest.fn();
  });

  describe('requestLogger', () => {
    it('should log GET requests without body', () => {
      // Call the middleware
      requestLogger(req, res, next);

      // Verify the logger was called
      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        type: 'request',
        method: 'GET',
        url: '/test',
        ip: '127.0.0.1',
        user: 'testuser'
      }));

      // Verify next was called
      expect(next).toHaveBeenCalled();
    });

    it('should log POST requests with masked body', () => {
      // Setup POST request with sensitive data
      req.method = 'POST';
      req.body = {
        username: 'testuser',
        password: 'secret123',
        token: 'abc123',
        apiKey: 'xyz456',
        secret: 'verysecret',
        credentials: { user: 'admin', pass: 'admin123' },
        otherData: 'not sensitive'
      };

      // Call the middleware
      requestLogger(req, res, next);

      // Verify the logger was called with masked data
      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        type: 'request',
        method: 'POST',
        body: expect.objectContaining({
          username: 'testuser',
          password: '********',
          token: '********',
          apiKey: '********',
          secret: '********',
          credentials: '********',
          otherData: 'not sensitive'
        })
      }));
    });

    it('should handle requests without user', () => {
      // Setup request without user
      req.user = null;

      // Call the middleware
      requestLogger(req, res, next);

      // Verify the logger was called with anonymous user
      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        user: 'anonymous'
      }));
    });

    it('should handle requests with null body', () => {
      // Setup request with null body
      req.method = 'POST';
      req.body = null;

      // Call the middleware
      requestLogger(req, res, next);

      // Verify the logger was called
      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        body: null
      }));
    });
  });

  describe('responseLogger', () => {
    it('should log responses', () => {
      // Call the middleware
      responseLogger(req, res, next);

      // Verify the end method was overridden
      expect(res.end).not.toBe(jest.fn());

      // Call the overridden end method
      res.end();

      // Verify the logger was called
      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        type: 'response',
        method: 'GET',
        url: '/test',
        status: 200,
        responseTime: expect.any(Number),
        user: 'testuser'
      }));
    });

    it('should handle responses without user', () => {
      // Setup request without user
      req.user = null;

      // Call the middleware
      responseLogger(req, res, next);

      // Call the overridden end method
      res.end();

      // Verify the logger was called with anonymous user
      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        user: 'anonymous'
      }));
    });

    it('should pass arguments to original end method', () => {
      // Setup original end method
      const originalEnd = jest.fn();
      res.end = originalEnd;

      // Call the middleware
      responseLogger(req, res, next);

      // Call the overridden end method with arguments
      const chunk = Buffer.from('test');
      const encoding = 'utf8';
      res.end(chunk, encoding);

      // Verify the original end method was called with the same arguments
      expect(originalEnd).toHaveBeenCalledWith(chunk, encoding);
    });
  });

  describe('errorLogger', () => {
    it('should log errors', () => {
      // Setup error
      const error = new Error('Test error');
      error.status = 400;

      // Call the middleware
      errorLogger(error, req, res, next);

      // Verify the logger was called
      expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
        type: 'error',
        method: 'GET',
        url: '/test',
        status: 400,
        message: 'Test error',
        stack: error.stack,
        user: 'testuser'
      }));

      // Verify next was called with the error
      expect(next).toHaveBeenCalledWith(error);
    });

    it('should handle errors without status', () => {
      // Setup error without status
      const error = new Error('Test error');

      // Call the middleware
      errorLogger(error, req, res, next);

      // Verify the logger was called with default status 500
      expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
        status: 500
      }));
    });

    it('should handle errors with requests without user', () => {
      // Setup request without user
      req.user = null;

      // Setup error
      const error = new Error('Test error');

      // Call the middleware
      errorLogger(error, req, res, next);

      // Verify the logger was called with anonymous user
      expect(logger.error).toHaveBeenCalledWith(expect.objectContaining({
        user: 'anonymous'
      }));
    });
  });

  describe('httpLogger', () => {
    it('should be a function', () => {
      expect(typeof httpLogger).toBe('function');
    });

    it('should use morgan with the correct format', () => {
      // This is a basic test to ensure httpLogger is created with morgan
      // We can't easily test the morgan functionality directly
      expect(httpLogger).toBeDefined();
    });

    it('should use morgan for HTTP logging', () => {
      // Since we've mocked morgan, we can just verify it was called
      const morgan = require('morgan');
      expect(morgan).toBeDefined();
      // Morgan is called during module initialization, so we can't easily test it here
    });

    it('should be configured with the correct format', () => {
      // We can't easily test the morgan token functions directly
      // But we can verify that httpLogger is defined
      expect(httpLogger).toBeDefined();
    });
  });

  describe('maskSensitiveData', () => {
    // We need to get access to the maskSensitiveData function
    // Since it's not exported directly, we can test it through the exported functions

    it('should mask password in data', () => {
      const data = { username: 'test', password: 'secret' };
      req.method = 'POST';
      req.body = data;

      requestLogger(req, res, next);

      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        body: expect.objectContaining({
          username: 'test',
          password: '********'
        })
      }));
    });

    it('should mask sensitive fields in data', () => {
      const data = {
        username: 'test',
        token: 'abc123',
        apiKey: 'key123',
        secret: 'topsecret',
        credentials: { user: 'admin', pass: 'pass123' }
      };
      req.method = 'POST';
      req.body = data;

      requestLogger(req, res, next);

      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        body: expect.objectContaining({
          username: 'test',
          token: '********',
          apiKey: '********',
          secret: '********',
          credentials: '********'
        })
      }));
    });

    it('should return null for null data', () => {
      req.method = 'POST';
      req.body = null;

      requestLogger(req, res, next);

      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        body: null
      }));
    });

    it('should handle data without sensitive fields', () => {
      const data = { username: 'test', email: '<EMAIL>' };
      req.method = 'POST';
      req.body = data;

      requestLogger(req, res, next);

      expect(logger.info).toHaveBeenCalledWith(expect.objectContaining({
        body: expect.objectContaining({
          username: 'test',
          email: '<EMAIL>'
        })
      }));
    });
  });
});
