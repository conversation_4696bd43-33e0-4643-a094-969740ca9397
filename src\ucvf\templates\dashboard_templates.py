"""
Dashboard templates for the Universal Compliance Visualization Framework.

This module provides dashboard templates for different stakeholder roles.
"""

import logging
from typing import Dict, List, Any, Optional

from ..core.template_manager import VisualizationTemplate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BoardDashboardTemplate(VisualizationTemplate):
    """Dashboard template for the Board of Directors role."""
    
    def __init__(self):
        """Initialize the Board Dashboard Template."""
        super().__init__(
            name="Board Dashboard",
            description="High-level dashboard for Board of Directors showing compliance posture and risk"
        )
    
    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided data.
        
        Args:
            data: The data to visualize
            context: Additional context for the visualization
            
        Returns:
            The visualization data
        """
        logger.info("Applying Board Dashboard Template")
        
        # Create the visualization structure
        visualization = {
            'template': self.name,
            'description': self.description,
            'sections': []
        }
        
        # Add the overall compliance section
        if 'overall_compliance' in data:
            visualization['sections'].append({
                'type': 'score_card',
                'title': 'Overall Compliance',
                'value': data['overall_compliance'],
                'format': 'percentage',
                'color': self._get_score_color(data['overall_compliance'])
            })
        
        # Add the overall risk section
        if 'overall_risk' in data:
            visualization['sections'].append({
                'type': 'risk_indicator',
                'title': 'Overall Risk',
                'value': data['overall_risk'],
                'color': self._get_risk_color(data['overall_risk'])
            })
        
        # Add the frameworks section
        if 'frameworks' in data:
            framework_section = {
                'type': 'bar_chart',
                'title': 'Compliance by Framework',
                'data': []
            }
            
            for framework, framework_data in data['frameworks'].items():
                framework_section['data'].append({
                    'label': framework_data.get('name', framework),
                    'value': framework_data.get('compliance_score', 0),
                    'color': self._get_score_color(framework_data.get('compliance_score', 0))
                })
            
            visualization['sections'].append(framework_section)
        
        # Add the risk by category section
        if 'risk_by_category' in data:
            risk_section = {
                'type': 'heat_map',
                'title': 'Risk by Category',
                'data': []
            }
            
            for category, risk_level in data['risk_by_category'].items():
                risk_section['data'].append({
                    'label': category,
                    'value': risk_level,
                    'color': self._get_risk_color(risk_level)
                })
            
            visualization['sections'].append(risk_section)
        
        # Add the compliance trends section
        if 'compliance_trends' in data:
            trends_section = {
                'type': 'line_chart',
                'title': 'Compliance Trends',
                'data': data['compliance_trends']
            }
            
            visualization['sections'].append(trends_section)
        
        # Add the financial impact section
        if 'financial_impact' in data:
            financial_section = {
                'type': 'financial_summary',
                'title': 'Financial Impact',
                'data': data['financial_impact']
            }
            
            visualization['sections'].append(financial_section)
        
        return visualization
    
    def _get_score_color(self, score: float) -> str:
        """
        Get the color for a compliance score.
        
        Args:
            score: The compliance score
            
        Returns:
            The color code
        """
        if score >= 90:
            return 'green'
        elif score >= 70:
            return 'yellow'
        else:
            return 'red'
    
    def _get_risk_color(self, risk_level: str) -> str:
        """
        Get the color for a risk level.
        
        Args:
            risk_level: The risk level
            
        Returns:
            The color code
        """
        if risk_level.lower() == 'low':
            return 'green'
        elif risk_level.lower() == 'medium':
            return 'yellow'
        elif risk_level.lower() == 'high':
            return 'red'
        else:
            return 'gray'


class CISODashboardTemplate(VisualizationTemplate):
    """Dashboard template for the CISO role."""
    
    def __init__(self):
        """Initialize the CISO Dashboard Template."""
        super().__init__(
            name="CISO Dashboard",
            description="Detailed dashboard for CISOs showing security controls and vulnerabilities"
        )
    
    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided data.
        
        Args:
            data: The data to visualize
            context: Additional context for the visualization
            
        Returns:
            The visualization data
        """
        logger.info("Applying CISO Dashboard Template")
        
        # Create the visualization structure
        visualization = {
            'template': self.name,
            'description': self.description,
            'sections': []
        }
        
        # Add the overall compliance section
        if 'overall_compliance' in data:
            visualization['sections'].append({
                'type': 'score_card',
                'title': 'Overall Compliance',
                'value': data['overall_compliance'],
                'format': 'percentage',
                'color': self._get_score_color(data['overall_compliance'])
            })
        
        # Add the security controls summary section
        if 'security_controls_summary' in data:
            controls_summary = data['security_controls_summary']
            
            controls_section = {
                'type': 'donut_chart',
                'title': 'Security Controls Status',
                'data': [
                    {
                        'label': 'Implemented',
                        'value': controls_summary.get('implemented_count', 0),
                        'color': 'green'
                    },
                    {
                        'label': 'Partially Implemented',
                        'value': controls_summary.get('partially_implemented_count', 0),
                        'color': 'yellow'
                    },
                    {
                        'label': 'Not Implemented',
                        'value': controls_summary.get('not_implemented_count', 0),
                        'color': 'red'
                    }
                ]
            }
            
            visualization['sections'].append(controls_section)
            
            # Add the controls by category section
            if 'by_category' in controls_summary:
                categories_section = {
                    'type': 'stacked_bar_chart',
                    'title': 'Controls by Category',
                    'data': []
                }
                
                for category, category_data in controls_summary['by_category'].items():
                    categories_section['data'].append({
                        'label': category,
                        'values': [
                            {
                                'label': 'Implemented',
                                'value': category_data.get('implemented_count', 0),
                                'color': 'green'
                            },
                            {
                                'label': 'Partially Implemented',
                                'value': category_data.get('partially_implemented_count', 0),
                                'color': 'yellow'
                            },
                            {
                                'label': 'Not Implemented',
                                'value': category_data.get('not_implemented_count', 0),
                                'color': 'red'
                            }
                        ]
                    })
                
                visualization['sections'].append(categories_section)
        
        # Add the vulnerabilities section
        if 'vulnerabilities' in data:
            vulnerabilities_section = {
                'type': 'vulnerability_summary',
                'title': 'Vulnerabilities',
                'data': data['vulnerabilities']
            }
            
            visualization['sections'].append(vulnerabilities_section)
        
        # Add the remediation section
        if 'remediation' in data:
            remediation_section = {
                'type': 'remediation_plan',
                'title': 'Remediation Plan',
                'data': data['remediation']
            }
            
            visualization['sections'].append(remediation_section)
        
        # Add the frameworks section with detailed control information
        if 'frameworks' in data:
            for framework, framework_data in data['frameworks'].items():
                if 'controls' in framework_data:
                    framework_section = {
                        'type': 'control_list',
                        'title': f"{framework_data.get('name', framework)} Controls",
                        'data': framework_data['controls']
                    }
                    
                    visualization['sections'].append(framework_section)
        
        return visualization
    
    def _get_score_color(self, score: float) -> str:
        """
        Get the color for a compliance score.
        
        Args:
            score: The compliance score
            
        Returns:
            The color code
        """
        if score >= 90:
            return 'green'
        elif score >= 70:
            return 'yellow'
        else:
            return 'red'


class ComplianceManagerDashboardTemplate(VisualizationTemplate):
    """Dashboard template for the Compliance Manager role."""
    
    def __init__(self):
        """Initialize the Compliance Manager Dashboard Template."""
        super().__init__(
            name="Compliance Manager Dashboard",
            description="Detailed dashboard for Compliance Managers showing requirements and evidence"
        )
    
    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided data.
        
        Args:
            data: The data to visualize
            context: Additional context for the visualization
            
        Returns:
            The visualization data
        """
        logger.info("Applying Compliance Manager Dashboard Template")
        
        # Create the visualization structure
        visualization = {
            'template': self.name,
            'description': self.description,
            'sections': []
        }
        
        # Add the overall compliance section
        if 'overall_compliance' in data:
            visualization['sections'].append({
                'type': 'score_card',
                'title': 'Overall Compliance',
                'value': data['overall_compliance'],
                'format': 'percentage',
                'color': self._get_score_color(data['overall_compliance'])
            })
        
        # Add the requirements summary section
        if 'requirements_summary' in data:
            requirements_summary = data['requirements_summary']
            
            requirements_section = {
                'type': 'donut_chart',
                'title': 'Requirements Status',
                'data': [
                    {
                        'label': 'Met',
                        'value': requirements_summary.get('met_count', 0),
                        'color': 'green'
                    },
                    {
                        'label': 'Partially Met',
                        'value': requirements_summary.get('partially_met_count', 0),
                        'color': 'yellow'
                    },
                    {
                        'label': 'Not Met',
                        'value': requirements_summary.get('not_met_count', 0),
                        'color': 'red'
                    }
                ]
            }
            
            visualization['sections'].append(requirements_section)
            
            # Add the requirements by framework section
            if 'by_framework' in requirements_summary:
                frameworks_section = {
                    'type': 'stacked_bar_chart',
                    'title': 'Requirements by Framework',
                    'data': []
                }
                
                for framework, framework_data in requirements_summary['by_framework'].items():
                    frameworks_section['data'].append({
                        'label': framework,
                        'values': [
                            {
                                'label': 'Met',
                                'value': framework_data.get('met_count', 0),
                                'color': 'green'
                            },
                            {
                                'label': 'Partially Met',
                                'value': framework_data.get('partially_met_count', 0),
                                'color': 'yellow'
                            },
                            {
                                'label': 'Not Met',
                                'value': framework_data.get('not_met_count', 0),
                                'color': 'red'
                            }
                        ]
                    })
                
                visualization['sections'].append(frameworks_section)
        
        # Add the evidence summary section
        if 'evidence_summary' in data:
            evidence_summary = data['evidence_summary']
            
            evidence_section = {
                'type': 'donut_chart',
                'title': 'Evidence Status',
                'data': [
                    {
                        'label': 'Valid',
                        'value': evidence_summary.get('valid_count', 0),
                        'color': 'green'
                    },
                    {
                        'label': 'Invalid',
                        'value': evidence_summary.get('invalid_count', 0),
                        'color': 'red'
                    },
                    {
                        'label': 'Pending',
                        'value': evidence_summary.get('pending_count', 0),
                        'color': 'yellow'
                    }
                ]
            }
            
            visualization['sections'].append(evidence_section)
            
            # Add the evidence by type section
            if 'by_type' in evidence_summary:
                types_section = {
                    'type': 'stacked_bar_chart',
                    'title': 'Evidence by Type',
                    'data': []
                }
                
                for evidence_type, type_data in evidence_summary['by_type'].items():
                    types_section['data'].append({
                        'label': evidence_type,
                        'values': [
                            {
                                'label': 'Valid',
                                'value': type_data.get('valid_count', 0),
                                'color': 'green'
                            },
                            {
                                'label': 'Invalid',
                                'value': type_data.get('invalid_count', 0),
                                'color': 'red'
                            },
                            {
                                'label': 'Pending',
                                'value': type_data.get('pending_count', 0),
                                'color': 'yellow'
                            }
                        ]
                    })
                
                visualization['sections'].append(types_section)
        
        # Add the gaps summary section
        if 'gaps_summary' in data:
            gaps_summary = data['gaps_summary']
            
            gaps_section = {
                'type': 'donut_chart',
                'title': 'Compliance Gaps by Risk',
                'data': [
                    {
                        'label': 'High Risk',
                        'value': gaps_summary.get('high_risk_count', 0),
                        'color': 'red'
                    },
                    {
                        'label': 'Medium Risk',
                        'value': gaps_summary.get('medium_risk_count', 0),
                        'color': 'yellow'
                    },
                    {
                        'label': 'Low Risk',
                        'value': gaps_summary.get('low_risk_count', 0),
                        'color': 'green'
                    }
                ]
            }
            
            visualization['sections'].append(gaps_section)
            
            # Add the gaps by framework section
            if 'by_framework' in gaps_summary:
                frameworks_section = {
                    'type': 'stacked_bar_chart',
                    'title': 'Gaps by Framework',
                    'data': []
                }
                
                for framework, framework_data in gaps_summary['by_framework'].items():
                    frameworks_section['data'].append({
                        'label': framework,
                        'values': [
                            {
                                'label': 'High Risk',
                                'value': framework_data.get('high_risk_count', 0),
                                'color': 'red'
                            },
                            {
                                'label': 'Medium Risk',
                                'value': framework_data.get('medium_risk_count', 0),
                                'color': 'yellow'
                            },
                            {
                                'label': 'Low Risk',
                                'value': framework_data.get('low_risk_count', 0),
                                'color': 'green'
                            }
                        ]
                    })
                
                visualization['sections'].append(frameworks_section)
        
        return visualization
    
    def _get_score_color(self, score: float) -> str:
        """
        Get the color for a compliance score.
        
        Args:
            score: The compliance score
            
        Returns:
            The color code
        """
        if score >= 90:
            return 'green'
        elif score >= 70:
            return 'yellow'
        else:
            return 'red'


class ITManagerDashboardTemplate(VisualizationTemplate):
    """Dashboard template for the IT Manager role."""
    
    def __init__(self):
        """Initialize the IT Manager Dashboard Template."""
        super().__init__(
            name="IT Manager Dashboard",
            description="Dashboard for IT Managers showing systems, configurations, and patches"
        )
    
    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided data.
        
        Args:
            data: The data to visualize
            context: Additional context for the visualization
            
        Returns:
            The visualization data
        """
        logger.info("Applying IT Manager Dashboard Template")
        
        # Create the visualization structure
        visualization = {
            'template': self.name,
            'description': self.description,
            'sections': []
        }
        
        # Add the overall compliance section
        if 'overall_compliance' in data:
            visualization['sections'].append({
                'type': 'score_card',
                'title': 'Overall Compliance',
                'value': data['overall_compliance'],
                'format': 'percentage',
                'color': self._get_score_color(data['overall_compliance'])
            })
        
        # Add the security controls summary section
        if 'security_controls_summary' in data:
            controls_summary = data['security_controls_summary']
            
            controls_section = {
                'type': 'donut_chart',
                'title': 'Security Controls Status',
                'data': [
                    {
                        'label': 'Implemented',
                        'value': controls_summary.get('implemented_count', 0),
                        'color': 'green'
                    },
                    {
                        'label': 'Partially Implemented',
                        'value': controls_summary.get('partially_implemented_count', 0),
                        'color': 'yellow'
                    },
                    {
                        'label': 'Not Implemented',
                        'value': controls_summary.get('not_implemented_count', 0),
                        'color': 'red'
                    }
                ]
            }
            
            visualization['sections'].append(controls_section)
        
        # Add systems-specific sections
        # These would be populated with actual data in a real implementation
        
        # Systems inventory section
        systems_section = {
            'type': 'systems_inventory',
            'title': 'Systems Inventory',
            'data': {
                'total_systems': 100,
                'compliant_systems': 75,
                'non_compliant_systems': 25
            }
        }
        
        visualization['sections'].append(systems_section)
        
        # Configuration status section
        config_section = {
            'type': 'configuration_status',
            'title': 'Configuration Status',
            'data': {
                'total_configurations': 500,
                'compliant_configurations': 400,
                'non_compliant_configurations': 100
            }
        }
        
        visualization['sections'].append(config_section)
        
        # Patch status section
        patch_section = {
            'type': 'patch_status',
            'title': 'Patch Status',
            'data': {
                'total_patches': 200,
                'applied_patches': 180,
                'pending_patches': 20
            }
        }
        
        visualization['sections'].append(patch_section)
        
        return visualization
    
    def _get_score_color(self, score: float) -> str:
        """
        Get the color for a compliance score.
        
        Args:
            score: The compliance score
            
        Returns:
            The color code
        """
        if score >= 90:
            return 'green'
        elif score >= 70:
            return 'yellow'
        else:
            return 'red'


class AuditorDashboardTemplate(VisualizationTemplate):
    """Dashboard template for the Auditor role."""
    
    def __init__(self):
        """Initialize the Auditor Dashboard Template."""
        super().__init__(
            name="Auditor Dashboard",
            description="Dashboard for Auditors showing evidence, controls, and testing"
        )
    
    def apply(self, data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Apply the template to the provided data.
        
        Args:
            data: The data to visualize
            context: Additional context for the visualization
            
        Returns:
            The visualization data
        """
        logger.info("Applying Auditor Dashboard Template")
        
        # Create the visualization structure
        visualization = {
            'template': self.name,
            'description': self.description,
            'sections': []
        }
        
        # Add the overall compliance section
        if 'overall_compliance' in data:
            visualization['sections'].append({
                'type': 'score_card',
                'title': 'Overall Compliance',
                'value': data['overall_compliance'],
                'format': 'percentage',
                'color': self._get_score_color(data['overall_compliance'])
            })
        
        # Add the requirements summary section
        if 'requirements_summary' in data:
            requirements_summary = data['requirements_summary']
            
            requirements_section = {
                'type': 'donut_chart',
                'title': 'Requirements Status',
                'data': [
                    {
                        'label': 'Met',
                        'value': requirements_summary.get('met_count', 0),
                        'color': 'green'
                    },
                    {
                        'label': 'Partially Met',
                        'value': requirements_summary.get('partially_met_count', 0),
                        'color': 'yellow'
                    },
                    {
                        'label': 'Not Met',
                        'value': requirements_summary.get('not_met_count', 0),
                        'color': 'red'
                    }
                ]
            }
            
            visualization['sections'].append(requirements_section)
        
        # Add the evidence summary section
        if 'evidence_summary' in data:
            evidence_summary = data['evidence_summary']
            
            evidence_section = {
                'type': 'donut_chart',
                'title': 'Evidence Status',
                'data': [
                    {
                        'label': 'Valid',
                        'value': evidence_summary.get('valid_count', 0),
                        'color': 'green'
                    },
                    {
                        'label': 'Invalid',
                        'value': evidence_summary.get('invalid_count', 0),
                        'color': 'red'
                    },
                    {
                        'label': 'Pending',
                        'value': evidence_summary.get('pending_count', 0),
                        'color': 'yellow'
                    }
                ]
            }
            
            visualization['sections'].append(evidence_section)
        
        # Add the testing section
        if 'testing' in data:
            testing_section = {
                'type': 'testing_summary',
                'title': 'Testing Summary',
                'data': data['testing']
            }
            
            visualization['sections'].append(testing_section)
        
        # Add the frameworks section with detailed requirement information
        if 'frameworks' in data:
            for framework, framework_data in data['frameworks'].items():
                if 'requirements' in framework_data:
                    framework_section = {
                        'type': 'requirement_list',
                        'title': f"{framework_data.get('name', framework)} Requirements",
                        'data': framework_data['requirements']
                    }
                    
                    visualization['sections'].append(framework_section)
        
        return visualization
    
    def _get_score_color(self, score: float) -> str:
        """
        Get the color for a compliance score.
        
        Args:
            score: The compliance score
            
        Returns:
            The color code
        """
        if score >= 90:
            return 'green'
        elif score >= 70:
            return 'yellow'
        else:
            return 'red'
