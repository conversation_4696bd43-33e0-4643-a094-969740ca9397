import React from 'react';
import Sidebar from '../components/Sidebar';
import Link from 'next/link';

export default function PartnerOnboarding() {
  const sidebarItems = [
    { type: 'category', label: 'Partner Onboarding', items: [
      { label: 'Overview', href: '#overview' },
      { label: 'Benefits', href: '#benefits' },
      { label: 'Process', href: '#process' },
      { label: 'Application', href: '#application' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'API Documentation', href: '/api-docs' },
      { label: 'Partner Ecosystem', href: '/partner-ecosystem' },
      { label: 'Partner Portal', href: '#' }
    ]}
  ];

  return (
    <div className="flex flex-col md:flex-row gap-8">
      {/* Sidebar - Hidden on mobile, shown on desktop */}
      <div className="hidden md:block md:w-1/4 lg:w-1/5">
        <div className="sticky top-4">
          <Sidebar items={sidebarItems} title="Partner Onboarding" />
        </div>
      </div>

      {/* Mobile Sidebar Toggle - Shown on mobile only */}
      <div className="md:hidden mb-4">
        <select
          className="w-full bg-secondary text-white border border-gray-700 rounded p-2"
          onChange={(e) => {
            if (e.target.value) window.location.href = e.target.value;
          }}
          defaultValue=""
        >
          <option value="" disabled>Navigate to...</option>
          {sidebarItems.map((item, index) => (
            item.type === 'category' ? (
              <optgroup key={index} label={item.label}>
                {item.items.map((subItem, subIndex) => (
                  <option key={`${index}-${subIndex}`} value={subItem.href}>
                    {subItem.label}
                  </option>
                ))}
              </optgroup>
            ) : (
              <option key={index} value={item.href}>
                {item.label}
              </option>
            )
          ))}
        </select>
      </div>

      {/* Main Content */}
      <div className="w-full md:w-3/4 lg:w-4/5">
        {/* Hero Section */}
        <div id="overview" className="accent-bg text-white rounded-lg p-8 mb-8">
          <h2 className="text-3xl font-bold mb-4">Become a NovaFuse Partner</h2>
          <p className="text-xl mb-6">
            Join the NovaFuse API Superstore and connect your services with our enterprise GRC platform.
            Gain access to new customers and generate revenue through our marketplace.
          </p>
        </div>

        {/* Partner Benefits */}
        <div id="benefits" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Partner Benefits</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-secondary p-6 rounded-lg">
              <div className="text-4xl text-blue-400 mb-4">30%</div>
              <h3 className="text-xl font-semibold mb-2">Revenue Share</h3>
              <p className="text-gray-300">Earn up to 30% of revenue generated through your integration with our platform.</p>
            </div>
            <div className="bg-secondary p-6 rounded-lg">
              <div className="text-4xl text-blue-400 mb-4">1M+</div>
              <h3 className="text-xl font-semibold mb-2">Customer Reach</h3>
              <p className="text-gray-300">Access to NovaFuse's enterprise customer base in regulated industries.</p>
            </div>
            <div className="bg-secondary p-6 rounded-lg">
              <div className="text-4xl text-blue-400 mb-4">24/7</div>
              <h3 className="text-xl font-semibold mb-2">Technical Support</h3>
              <p className="text-gray-300">Dedicated technical support for your integration and customers.</p>
            </div>
          </div>
        </div>

        {/* Onboarding Process */}
        <div id="process" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Onboarding Process</h2>
          <div className="bg-secondary p-6 rounded-lg">
            <div className="flex flex-col md:flex-row">
              <div className="md:w-1/4 mb-4 md:mb-0 md:pr-4">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">1</div>
                  <h3 className="text-xl font-semibold">Apply</h3>
                </div>
                <p className="text-gray-300">Complete the partner application form with your company details.</p>
              </div>
              <div className="md:w-1/4 mb-4 md:mb-0 md:px-4">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">2</div>
                  <h3 className="text-xl font-semibold">Integrate</h3>
                </div>
                <p className="text-gray-300">Implement the required endpoints based on our connector templates.</p>
              </div>
              <div className="md:w-1/4 mb-4 md:mb-0 md:px-4">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">3</div>
                  <h3 className="text-xl font-semibold">Validate</h3>
                </div>
                <p className="text-gray-300">Test your integration and submit it for validation by our team.</p>
              </div>
              <div className="md:w-1/4 md:pl-4">
                <div className="flex items-center mb-2">
                  <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-2">4</div>
                  <h3 className="text-xl font-semibold">Launch</h3>
                </div>
                <p className="text-gray-300">Go live in the NovaFuse API Superstore and start generating revenue.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Application Form */}
        <div id="application" className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Partner Application</h2>
          <div className="bg-secondary p-6 rounded-lg">
            <form>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="company_name">Company Name</label>
                  <input type="text" id="company_name" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="Your company name" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="website">Website</label>
                  <input type="url" id="website" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="https://example.com" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="contact_name">Contact Name</label>
                  <input type="text" id="contact_name" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="Your name" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="email">Email</label>
                  <input type="email" id="email" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="<EMAIL>" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="phone">Phone</label>
                  <input type="tel" id="phone" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" placeholder="(*************" />
                </div>
                <div>
                  <label className="block text-gray-300 mb-2" htmlFor="category">Integration Category</label>
                  <select id="category" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white">
                    <option value="">Select a category</option>
                    <option value="governance">Governance & Board Compliance</option>
                    <option value="security">Security</option>
                    <option value="apis">APIs & Developer Tools</option>
                    <option value="risk">Risk & Audit</option>
                    <option value="contracts">Contracts & Policy Lifecycle</option>
                    <option value="certifications">Certifications & Accreditation</option>
                  </select>
                </div>
              </div>
              <div className="mb-6">
                <label className="block text-gray-300 mb-2" htmlFor="description">Integration Description</label>
                <textarea id="description" className="w-full p-2 rounded bg-gray-800 border border-gray-700 text-white" rows="4" placeholder="Describe your integration and how it will benefit NovaFuse customers"></textarea>
              </div>
              <div className="flex justify-end">
                <button type="submit" className="accent-bg text-white px-6 py-3 rounded font-bold hover:bg-blue-700">
                  Submit Application
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
