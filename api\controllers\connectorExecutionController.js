const { executeConnector, scheduleConnector, cancelScheduledConnector } = require('../services/connectorExecutionEngine');
const { Installation } = require('../models');

/**
 * Execute a connector
 */
const executeConnectorHandler = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const parameters = req.body.parameters || {};
    
    // Check if connector is installed for this user
    const installation = await Installation.findOne({ connector: id, user: userId });
    if (!installation) {
      return res.status(404).json({ error: true, message: 'Connector not installed for this user' });
    }
    
    // Execute connector
    const result = await executeConnector(id, userId, parameters);
    
    res.status(200).json(result);
  } catch (error) {
    next(error);
  }
};

/**
 * Schedule a connector for execution
 */
const scheduleConnectorHandler = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { parameters, scheduledTime } = req.body;
    
    // Check if connector is installed for this user
    const installation = await Installation.findOne({ connector: id, user: userId });
    if (!installation) {
      return res.status(404).json({ error: true, message: 'Connector not installed for this user' });
    }
    
    // Schedule connector
    const job = await scheduleConnector(id, userId, parameters, new Date(scheduledTime));
    
    res.status(200).json(job);
  } catch (error) {
    next(error);
  }
};

/**
 * Cancel a scheduled connector execution
 */
const cancelScheduledConnectorHandler = async (req, res, next) => {
  try {
    const { jobId } = req.params;
    
    // Cancel scheduled connector
    const result = await cancelScheduledConnector(jobId);
    
    if (result) {
      res.status(200).json({ message: 'Scheduled execution cancelled successfully' });
    } else {
      res.status(404).json({ error: true, message: 'Scheduled execution not found or already completed' });
    }
  } catch (error) {
    next(error);
  }
};

/**
 * Get execution history for a connector
 */
const getExecutionHistory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    
    // Check if connector is installed for this user
    const installation = await Installation.findOne({ connector: id, user: userId });
    if (!installation) {
      return res.status(404).json({ error: true, message: 'Connector not installed for this user' });
    }
    
    // In a real app, this would fetch execution history from a database
    // For now, we'll return mock data
    const history = [
      {
        id: 'exec-1',
        connectorId: id,
        userId,
        status: 'success',
        startTime: new Date(Date.now() - 86400000), // 1 day ago
        endTime: new Date(Date.now() - 86395000),
        duration: 5000, // 5 seconds
        findings: 2,
        criticalFindings: 0
      },
      {
        id: 'exec-2',
        connectorId: id,
        userId,
        status: 'warning',
        startTime: new Date(Date.now() - 172800000), // 2 days ago
        endTime: new Date(Date.now() - 172790000),
        duration: 10000, // 10 seconds
        findings: 5,
        criticalFindings: 1
      }
    ];
    
    res.status(200).json({
      history,
      pagination: {
        total: history.length,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(history.length / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  executeConnectorHandler,
  scheduleConnectorHandler,
  cancelScheduledConnectorHandler,
  getExecutionHistory
};
