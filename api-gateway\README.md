# NovaFuse API Superstore - API Gateway

This is the API Gateway for the NovaFuse API Superstore. It routes requests to the appropriate services and provides a unified API for clients.

## Features

- **Unified API**: Single entry point for all NovaFuse APIs
- **Service Discovery**: Automatically routes requests to the appropriate service
- **Authentication**: Centralized authentication and authorization
- **Rate Limiting**: Prevents abuse with configurable rate limits
- **Logging**: Comprehensive logging for all requests
- **Error Handling**: Consistent error handling across all services
- **API Documentation**: Centralized API documentation

## Services

The API Gateway routes requests to the following services:

- **NovaConnect**: Universal API Connector
- **Privacy Management**: Privacy management and compliance
- **Regulatory Compliance**: Regulatory compliance management
- **Security Assessment**: Security assessment and management
- **Control Testing**: Control testing and management
- **ESG**: Environmental, Social, and Governance management

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```

### Configuration

The API Gateway can be configured using environment variables:

- `PORT`: The port to listen on (default: 3000)
- `NOVACONNECT_URL`: The URL of the NovaConnect service (default: http://localhost:3001)
- `PRIVACY_MANAGEMENT_URL`: The URL of the Privacy Management service (default: http://localhost:3002)
- `REGULATORY_COMPLIANCE_URL`: The URL of the Regulatory Compliance service (default: http://localhost:3003)
- `SECURITY_ASSESSMENT_URL`: The URL of the Security Assessment service (default: http://localhost:3004)
- `CONTROL_TESTING_URL`: The URL of the Control Testing service (default: http://localhost:3005)
- `ESG_URL`: The URL of the ESG service (default: http://localhost:3006)

### Running the API Gateway

```
npm start
```

For development:

```
npm run dev
```

## API Routes

- `/api/novaconnect`: NovaConnect API
- `/api/privacy/management`: Privacy Management API
- `/api/compliance`: Regulatory Compliance API
- `/api/security/assessment`: Security Assessment API
- `/api/control/testing`: Control Testing API
- `/api/esg`: ESG API
- `/api-docs`: API documentation
- `/health`: Health check endpoint

## License

This project is licensed under the MIT License - see the LICENSE file for details.
