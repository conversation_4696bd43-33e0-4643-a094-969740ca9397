/**
 * MetricsCard Component
 * 
 * A reusable metrics card component for displaying key metrics with trends and visualizations.
 */

import React from 'react';
import PropTypes from 'prop-types';

/**
 * MetricsCard component
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Card title
 * @param {Array} props.metrics - Metrics to display
 * @param {string} props.metrics[].label - Metric label
 * @param {number|string} props.metrics[].value - Metric value
 * @param {number} [props.metrics[].trend] - Metric trend (percentage change)
 * @param {string} [props.metrics[].trendDirection] - Trend direction (up, down, neutral)
 * @param {string} [props.metrics[].icon] - Metric icon
 * @param {string} [props.metrics[].color] - Metric color
 * @param {string} [props.metrics[].suffix] - Metric suffix (e.g., %, $)
 * @param {string} [props.metrics[].description] - Metric description
 * @param {number} [props.columns=2] - Number of columns to display metrics in
 * @param {boolean} [props.loading=false] - Whether the card is in loading state
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} MetricsCard component
 */
const MetricsCard = ({
  title,
  metrics,
  columns = 2,
  loading = false,
  className = '',
  style = {}
}) => {
  // Determine grid columns class
  const gridColumnsClass = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-3',
    4: 'grid-cols-2 sm:grid-cols-4'
  }[columns] || 'grid-cols-1 sm:grid-cols-2';
  
  // Render trend indicator
  const renderTrendIndicator = (trend, trendDirection) => {
    if (trend === undefined && trendDirection === undefined) return null;
    
    // Determine direction if not provided
    const direction = trendDirection || (trend > 0 ? 'up' : trend < 0 ? 'down' : 'neutral');
    
    // Determine color based on direction
    const colorClass = {
      up: 'text-green-500',
      down: 'text-red-500',
      neutral: 'text-gray-500'
    }[direction] || 'text-gray-500';
    
    // Determine icon based on direction
    const icon = {
      up: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
        </svg>
      ),
      down: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
        </svg>
      ),
      neutral: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      )
    }[direction] || null;
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        {icon}
        <span className="ml-1">{trend !== undefined ? `${Math.abs(trend)}%` : ''}</span>
      </div>
    );
  };
  
  // Render metric icon
  const renderMetricIcon = (icon, color) => {
    if (!icon) return null;
    
    // Determine color class
    const colorClass = color || 'text-blue-500';
    
    return (
      <div className={`flex items-center justify-center w-10 h-10 rounded-full bg-opacity-10 ${colorClass.replace('text-', 'bg-')}`}>
        <span className={colorClass}>{icon}</span>
      </div>
    );
  };
  
  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden ${className}`}
      style={style}
      data-testid="metrics-card"
    >
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : (
        <div className={`grid ${gridColumnsClass} gap-4 p-4`}>
          {metrics.map((metric, index) => (
            <div
              key={index}
              className="flex flex-col p-4 rounded-lg border border-gray-100 hover:shadow-md transition-shadow duration-200"
              data-testid={`metric-${index}`}
            >
              <div className="flex justify-between items-start mb-2">
                <div className="text-sm font-medium text-gray-500">{metric.label}</div>
                {renderTrendIndicator(metric.trend, metric.trendDirection)}
              </div>
              
              <div className="flex items-center">
                {renderMetricIcon(metric.icon, metric.color)}
                <div className={`${metric.icon ? 'ml-3' : ''}`}>
                  <div className="flex items-baseline">
                    <div className="text-2xl font-bold text-gray-900">
                      {metric.value}
                      {metric.suffix && <span className="text-lg ml-1">{metric.suffix}</span>}
                    </div>
                  </div>
                  {metric.description && (
                    <div className="text-xs text-gray-500 mt-1">{metric.description}</div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

MetricsCard.propTypes = {
  title: PropTypes.string.isRequired,
  metrics: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
      trend: PropTypes.number,
      trendDirection: PropTypes.oneOf(['up', 'down', 'neutral']),
      icon: PropTypes.node,
      color: PropTypes.string,
      suffix: PropTypes.string,
      description: PropTypes.string
    })
  ).isRequired,
  columns: PropTypes.oneOf([1, 2, 3, 4]),
  loading: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default MetricsCard;
