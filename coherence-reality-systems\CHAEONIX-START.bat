@echo off
title CHAEONIX - 15-Engine Comphyological Money Making Machine
color 0A

echo.
echo ========================================================
echo    CHAEONIX LIVE TRADING SYSTEM
echo    15-Engine Comphyological Money Making Machine
echo    System Status: EXCELLENT (86%% Health)
echo    Profit Potential: $4,097/hr
echo ========================================================
echo.
echo Starting CHAEONIX Dashboard...
echo.

REM Change to the correct directory
cd /d "%~dp0chaeonix-divine-dashboard"

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm dependencies are installed
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
)

REM Start the development server
echo Starting CHAEONIX Dashboard on http://localhost:3141
echo.
echo ========================================================
echo    CHAEONIX DASHBOARD CONTROLS:
echo    - Dashboard: http://localhost:3141
echo    - System Test: Click "Run System Test"
echo    - Live Bot: Click "Deploy Live Bot"
echo    - 6PM Reset: Use Profit Analytics reset
echo ========================================================
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the server and automatically open browser
start "" "http://localhost:3141"
npm run dev

pause
