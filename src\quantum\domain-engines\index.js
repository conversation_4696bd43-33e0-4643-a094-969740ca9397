/**
 * Domain Engines
 *
 * This module exports all domain-specific engine adapters for the Finite Universe
 * Principle defense system. These adapters connect the boundary enforcement
 * mechanisms with domain-specific engines (CSDE, CSFE, CSME) to ensure that
 * operations in each domain remain within finite boundaries.
 */

const { CSEDAdapter, createCSEDAdapter } = require('./csde-adapter');
const { CSFEAdapter, createCSFEAdapter } = require('./csfe-adapter');
const { CSMEAdapter, createCSMEAdapter } = require('./csme-adapter');

/**
 * Create all domain adapters
 * @param {Object} options - Configuration options
 * @returns {Object} - Object containing all domain adapters
 */
function createAllDomainAdapters(options = {}) {
  return {
    cyber: createCSEDAdapter(options.csdeOptions),
    financial: createCSFEAdapter(options.csfeOptions),
    medical: createCSMEAdapter(options.csmeOptions)
  };
}

module.exports = {
  // Domain adapters
  CSEDAdapter,
  CSFEAdapter,
  CSMEAdapter,
  
  // Factory functions
  createCSED<PERSON>dapter,
  createCSF<PERSON>dapter,
  createCS<PERSON><PERSON>dapter,
  createAllDomainAdapters
};
