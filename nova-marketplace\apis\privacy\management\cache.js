/**
 * Cache Utility
 *
 * This module provides a caching mechanism for the Privacy Management API.
 * It uses node-cache for in-memory caching with configurable TTL.
 */

const NodeCache = require('node-cache');
const { logger } = require('./utils/logger');

// Default cache options
const DEFAULT_TTL = 600; // 10 minutes in seconds
const DEFAULT_CHECK_PERIOD = 120; // 2 minutes in seconds

// Create cache instance with default options
const cacheOptions = {
  stdTTL: DEFAULT_TTL,
  checkperiod: DEFAULT_CHECK_PERIOD,
  useClones: false
};

// Initialize cache
const cacheInstance = new NodeCache(cacheOptions);
logger.info(`Cache initialized with TTL: ${DEFAULT_TTL} seconds`);

/**
 * Set a value in the cache
 * @param {string} key - Cache key
 * @param {any} value - Value to cache
 * @param {number} ttl - Time to live in seconds (optional)
 * @returns {boolean} - Success status
 */
function set(key, value, ttl) {
  try {
    cacheInstance.set(key, value, ttl);
    logger.debug(`Cache set: ${key}`);
    return true;
  } catch (error) {
    logger.error(`Cache set error: ${error}`);
    return false;
  }
}

/**
 * Get a value from the cache
 * @param {string} key - Cache key
 * @returns {any|null} - Cached value or null if not found
 */
function get(key) {
  try {
    const value = cacheInstance.get(key);

    if (value === undefined) {
      logger.debug(`Cache miss: ${key}`);
      return null;
    }

    logger.debug(`Cache hit: ${key}`);
    return value;
  } catch (error) {
    logger.error(`Cache get error: ${error}`);
    return null;
  }
}

/**
 * Delete a value from the cache
 * @param {string} key - Cache key
 * @returns {boolean} - Success status
 */
function delete_(key) {
  try {
    const result = cacheInstance.del(key);
    logger.debug(`Cache delete: ${key}`);
    return result > 0;
  } catch (error) {
    logger.error(`Cache delete error: ${error}`);
    return false;
  }
}

/**
 * Clear all values from the cache
 */
function clear() {
  try {
    cacheInstance.flushAll();
    logger.info('Cache cleared');
  } catch (error) {
    logger.error(`Cache clear error: ${error}`);
  }
}

/**
 * Get all keys in the cache
 * @returns {string[]} - Array of cache keys
 */
function getKeys() {
  try {
    return cacheInstance.keys();
  } catch (error) {
    logger.error(`Cache getKeys error: ${error}`);
    return [];
  }
}

/**
 * Get cache statistics
 * @returns {Object} - Cache statistics
 */
function getStats() {
  try {
    return cacheInstance.getStats();
  } catch (error) {
    logger.error(`Cache getStats error: ${error}`);
    return {};
  }
}

/**
 * Close the cache
 */
function close() {
  try {
    cacheInstance.close();
    logger.info('Cache closed');
  } catch (error) {
    logger.error(`Cache close error: ${error}`);
  }
}

// Export the cache module
module.exports = {
  cacheInstance,
  set,
  get,
  delete: delete_,
  clear,
  getKeys,
  getStats,
  close
};
