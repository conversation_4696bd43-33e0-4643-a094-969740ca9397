/**
 * Comphyon Exports
 * 
 * This module exports all Comphyon components for use in the NovaFuse platform.
 */

const ComphyonMeter = require('./comphyon_meter');

// Note: React components are imported differently in actual usage
// These imports are for documentation purposes
// const ComphyonGauge = require('./components/ComphyonGauge');
// const ComphyonDashboard = require('./components/ComphyonDashboard');

module.exports = {
  // Core components
  ComphyonMeter,
  
  // React components are typically imported directly from their source files
  // rather than through this exports file, but they're listed here for documentation
  // ComphyonGauge,
  // ComphyonDashboard
};
