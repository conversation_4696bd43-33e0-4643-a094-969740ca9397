4150c19d3415acbad0a5e0a0b933b2d8
/**
 * NovaFuse Universal API Connector Validator Tests
 * 
 * This module contains tests for the connector validator.
 */

const {
  ConnectorValidator
} = require('../../src/validation');
const fs = require('fs');
const path = require('path');
describe('ConnectorValidator', () => {
  let googleCloudConnector;
  let microsoftDefenderConnector;
  let awsSecurityHubConnector;
  beforeAll(() => {
    // Load test connectors
    googleCloudConnector = JSON.parse(fs.readFileSync(path.join(__dirname, '../../templates/google-cloud-security.json'), 'utf8'));
    microsoftDefenderConnector = JSON.parse(fs.readFileSync(path.join(__dirname, '../../templates/microsoft-defender-cloud.json'), 'utf8'));
    awsSecurityHubConnector = JSON.parse(fs.readFileSync(path.join(__dirname, '../../templates/aws-security-hub.json'), 'utf8'));
  });
  describe('validateConnector', () => {
    test('should validate a valid connector', () => {
      const result = ConnectorValidator.validateConnector(googleCloudConnector);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    test('should validate another valid connector', () => {
      const result = ConnectorValidator.validateConnector(microsoftDefenderConnector);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    test('should validate a third valid connector', () => {
      const result = ConnectorValidator.validateConnector(awsSecurityHubConnector);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
    test('should return errors for an invalid connector', () => {
      const invalidConnector = {
        // Missing metadata
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector metadata is required');
    });
    test('should return errors for a connector with invalid metadata', () => {
      const invalidConnector = {
        metadata: {
          // Missing name
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector name is required');
    });
    test('should return errors for a connector with invalid authentication', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'INVALID_TYPE',
          // Invalid type
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');
    });
    test('should return errors for a connector with missing configuration', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        }
        // Missing configuration
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector configuration is required');
    });
    test('should return errors for a connector with invalid configuration', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          // Missing baseUrl
          headers: {
            'Content-Type': 'application/json'
          }
        }
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Configuration base URL is required');
    });
    test('should return errors for a connector with missing endpoints', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          baseUrl: 'https://api.example.com'
        }
        // Missing endpoints
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Connector endpoints are required');
    });
    test('should return errors for a connector with invalid endpoints', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          baseUrl: 'https://api.example.com'
        },
        endpoints: [{
          // Missing id
          name: 'Test Endpoint',
          path: '/test',
          method: 'GET'
        }]
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Endpoint at index 0 must have an ID');
    });
    test('should return errors for a connector with duplicate endpoint IDs', () => {
      const invalidConnector = {
        metadata: {
          name: 'Test Connector',
          version: '1.0.0',
          category: 'Test',
          description: 'Test connector'
        },
        authentication: {
          type: 'API_KEY',
          fields: {
            apiKey: {
              type: 'string',
              required: true
            }
          }
        },
        configuration: {
          baseUrl: 'https://api.example.com'
        },
        endpoints: [{
          id: 'test',
          name: 'Test Endpoint 1',
          path: '/test1',
          method: 'GET'
        }, {
          id: 'test',
          // Duplicate ID
          name: 'Test Endpoint 2',
          path: '/test2',
          method: 'GET'
        }]
      };
      const result = ConnectorValidator.validateConnector(invalidConnector);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Duplicate endpoint IDs found: test');
    });
  });
  describe('validateMetadata', () => {
    test('should validate valid metadata', () => {
      const errors = [];
      ConnectorValidator.validateMetadata(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    test('should return errors for invalid metadata', () => {
      const errors = [];
      const invalidConnector = {
        metadata: {
          name: 123,
          // Should be a string
          version: '1.0',
          // Invalid semver
          category: 'Test',
          description: 'Test connector'
        }
      };
      ConnectorValidator.validateMetadata(invalidConnector, errors);
      expect(errors).toContain('Connector name must be a string');
      expect(errors).toContain('Connector version must be in semver format (e.g., 1.0.0)');
    });
  });
  describe('validateAuthentication', () => {
    test('should validate valid authentication', () => {
      const errors = [];
      ConnectorValidator.validateAuthentication(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    test('should return errors for invalid authentication', () => {
      const errors = [];
      const invalidConnector = {
        authentication: {
          type: 'INVALID_TYPE',
          fields: {
            apiKey: {
              type: 'invalid_type',
              required: 'yes' // Should be a boolean
            }
          }
        }
      };
      ConnectorValidator.validateAuthentication(invalidConnector, errors);
      expect(errors).toContain('Authentication type must be one of: API_KEY, BASIC, OAUTH2, CUSTOM, NONE');
      expect(errors).toContain('Authentication field \'apiKey\' type must be one of: string, number, boolean, object, array');
      expect(errors).toContain('Authentication field \'apiKey\' required must be a boolean');
    });
  });
  describe('validateConfiguration', () => {
    test('should validate valid configuration', () => {
      const errors = [];
      ConnectorValidator.validateConfiguration(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    test('should return errors for invalid configuration', () => {
      const errors = [];
      const invalidConnector = {
        configuration: {
          baseUrl: 'invalid-url',
          timeout: -1,
          // Should be positive
          rateLimit: 'invalid' // Should be an object
        }
      };
      ConnectorValidator.validateConfiguration(invalidConnector, errors);
      expect(errors).toContain('Configuration base URL must be a valid URL');
      expect(errors).toContain('Configuration timeout must be greater than 0');
      expect(errors).toContain('Configuration rate limit must be an object');
    });
  });
  describe('validateEndpoints', () => {
    test('should validate valid endpoints', () => {
      const errors = [];
      ConnectorValidator.validateEndpoints(googleCloudConnector, errors);
      expect(errors).toHaveLength(0);
    });
    test('should return errors for invalid endpoints', () => {
      const errors = [];
      const invalidConnector = {
        endpoints: [{
          // Missing id
          name: 'Test Endpoint',
          path: '/test',
          method: 'INVALID_METHOD' // Invalid method
        }]
      };
      ConnectorValidator.validateEndpoints(invalidConnector, errors);
      expect(errors).toContain('Endpoint at index 0 must have an ID');
      expect(errors).toContain('Endpoint at index 0 method must be one of: GET, POST, PUT, PATCH, DELETE');
    });
  });
  describe('utility methods', () => {
    test('isValidUrl should validate URLs correctly', () => {
      expect(ConnectorValidator.isValidUrl('https://example.com')).toBe(true);
      expect(ConnectorValidator.isValidUrl('http://example.com/path')).toBe(true);
      expect(ConnectorValidator.isValidUrl('invalid-url')).toBe(false);
      expect(ConnectorValidator.isValidUrl('example.com')).toBe(false);
    });
    test('isValidVersion should validate semver versions correctly', () => {
      expect(ConnectorValidator.isValidVersion('1.0.0')).toBe(true);
      expect(ConnectorValidator.isValidVersion('1.0.0-alpha')).toBe(true);
      expect(ConnectorValidator.isValidVersion('1.0.0-alpha.1')).toBe(true);
      expect(ConnectorValidator.isValidVersion('1.0')).toBe(false);
      expect(ConnectorValidator.isValidVersion('v1.0.0')).toBe(false);
      expect(ConnectorValidator.isValidVersion('1')).toBe(false);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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