# NovaFuse Testing Playbook

This playbook provides guidelines and best practices for testing the NovaFuse API Superstore. It is designed to ensure consistent, high-quality testing across the codebase.

## Table of Contents

1. [Testing Pyramid](#testing-pyramid)
2. [Unit Testing Guidelines](#unit-testing-guidelines)
3. [API Testing Guidelines](#api-testing-guidelines)
4. [End-to-End Testing Guidelines](#end-to-end-testing-guidelines)
5. [Security Testing Guidelines](#security-testing-guidelines)
6. [Mutation Testing](#mutation-testing)
7. [Custom Commands](#custom-commands)
8. [Test Data Management](#test-data-management)
9. [CI/CD Integration](#cicd-integration)
10. [Troubleshooting](#troubleshooting)

## Testing Pyramid

We follow the testing pyramid approach:

- **Unit Tests**: Fast, focused tests for individual functions and components
- **API Tests**: Tests for API endpoints and integration points
- **End-to-End Tests**: Tests that simulate user journeys through the application

The ratio should be approximately:
- 70% Unit Tests
- 20% API Tests
- 10% End-to-End Tests

## Unit Testing Guidelines

### Component Testing

1. **Test Rendering**: Verify components render without errors
2. **Test Props**: Verify components handle different props correctly
3. **Test User Interactions**: Verify components respond to user interactions
4. **Test Edge Cases**: Verify components handle edge cases (empty data, errors, etc.)

Example:

```javascript
describe('Button Component', () => {
  it('renders correctly', () => {
    render(<Button label="Click me" />);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Button label="Click me" onClick={handleClick} />);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(<Button label="Click me" disabled />);
    expect(screen.getByText('Click me')).toBeDisabled();
  });
});
```

### Utility Function Testing

1. **Test Input/Output**: Verify functions return expected outputs for given inputs
2. **Test Edge Cases**: Verify functions handle edge cases (null, undefined, empty arrays, etc.)
3. **Test Error Handling**: Verify functions throw or handle errors appropriately

Example:

```javascript
describe('formatDate utility', () => {
  it('formats date correctly', () => {
    const date = new Date('2023-01-01');
    expect(formatDate(date)).toBe('01/01/2023');
  });

  it('handles invalid dates', () => {
    expect(formatDate(null)).toBe('N/A');
    expect(formatDate(undefined)).toBe('N/A');
    expect(formatDate('not a date')).toBe('Invalid Date');
  });
});
```

## API Testing Guidelines

1. **Test Happy Paths**: Verify endpoints return expected responses for valid requests
2. **Test Error Handling**: Verify endpoints return appropriate error responses for invalid requests
3. **Test Authentication**: Verify endpoints enforce authentication requirements
4. **Test Authorization**: Verify endpoints enforce authorization requirements
5. **Test Validation**: Verify endpoints validate request data

Example:

```javascript
describe('GET /api/connect/connectors', () => {
  it('returns a list of connectors', async () => {
    const response = await request(app)
      .get('/api/connect/connectors')
      .set('Authorization', `Bearer ${authToken}`)
      .expect('Content-Type', /json/)
      .expect(200);
    
    expect(Array.isArray(response.body)).toBe(true);
  });

  it('returns 401 when not authenticated', async () => {
    await request(app)
      .get('/api/connect/connectors')
      .expect(401);
  });
});
```

### Security Testing

1. **Test Input Sanitization**: Verify endpoints sanitize inputs to prevent injection attacks
2. **Test Authentication**: Verify endpoints reject invalid tokens
3. **Test Rate Limiting**: Verify endpoints enforce rate limits
4. **Test Data Protection**: Verify sensitive data is not exposed

Example:

```javascript
describe('API Security', () => {
  it('sanitizes inputs to prevent SQL injection', async () => {
    const maliciousPayload = {
      name: "Test Connector'; DROP TABLE connectors; --",
      description: "Malicious connector with SQL injection attempt",
      version: "1.0.0",
      category: "Security"
    };

    const response = await request(app)
      .post('/api/connect/connectors')
      .send(maliciousPayload)
      .set('Authorization', `Bearer ${authToken}`)
      .expect(201);
    
    // Verify the name was stored as-is (sanitized by the API)
    expect(response.body.name).toEqual(maliciousPayload.name);
  });
});
```

## End-to-End Testing Guidelines

1. **Test Critical User Journeys**: Focus on the most important user flows
2. **Use Data Attributes**: Use `data-testid` attributes for selecting elements
3. **Minimize Flakiness**: Use stable selectors and appropriate waits
4. **Use Custom Commands**: Create custom commands for common operations

Example:

```javascript
describe('Authentication Flow', () => {
  it('allows user login', () => {
    cy.visit('/signin');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    cy.get('form').submit();
    cy.url().should('include', '/dashboard');
    cy.contains('Welcome back').should('be.visible');
  });
});
```

### Custom Commands

Create custom commands for common operations to improve readability and maintainability:

```javascript
// cypress/support/commands.js
Cypress.Commands.add('login', (email, password) => {
  cy.visit('/signin');
  cy.get('input[name="email"]').type(email);
  cy.get('input[name="password"]').type(password);
  cy.get('form').submit();
  cy.url().should('include', '/dashboard');
});

// Usage
cy.login('<EMAIL>', 'Password123!');
```

## Security Testing Guidelines

1. **OWASP Top 10**: Test for the OWASP Top 10 vulnerabilities
2. **Input Validation**: Test for XSS, SQL injection, command injection, etc.
3. **Authentication**: Test for authentication bypasses, weak passwords, etc.
4. **Authorization**: Test for authorization bypasses, privilege escalation, etc.
5. **Data Protection**: Test for sensitive data exposure, insecure storage, etc.

Example:

```javascript
describe('XSS Prevention', () => {
  it('sanitizes user input to prevent XSS', () => {
    const xssPayload = '<script>alert("XSS")</script>';
    
    cy.visit('/profile');
    cy.get('input[name="displayName"]').type(xssPayload);
    cy.get('form').submit();
    
    // Verify the script tag is escaped or sanitized
    cy.get('[data-testid="profile-name"]').should('not.contain', '<script>');
  });
});
```

## Mutation Testing

Mutation testing helps identify weaknesses in your test suite by making small changes to your code and checking if your tests catch these changes.

1. **Focus on Critical Code**: Start with the most critical parts of your codebase
2. **Analyze Results**: Look for surviving mutants and add tests to kill them
3. **Set Thresholds**: Aim for a mutation score of at least 80%

Example:

```javascript
// stryker.conf.js
module.exports = {
  mutate: [
    'utils/compliance/**/*.js',
    'utils/validation/**/*.js',
    'utils/security/**/*.js',
  ],
  thresholds: {
    high: 80,
    low: 60,
    break: 50
  }
};
```

## Test Data Management

1. **Use Fixtures**: Store test data in fixture files
2. **Isolate Tests**: Each test should be independent of others
3. **Clean Up**: Clean up test data after tests run
4. **Use Factories**: Create factory functions for generating test data

Example:

```javascript
// cypress/fixtures/users.json
{
  "testUser": {
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "password": "Password123!"
  }
}

// Usage
cy.fixture('users').then((users) => {
  cy.login(users.testUser.email, users.testUser.password);
});
```

## CI/CD Integration

1. **Run Tests on Every PR**: Run tests automatically on every pull request
2. **Parallel Execution**: Run tests in parallel to reduce feedback time
3. **Coverage Gates**: Block PRs if coverage drops below thresholds
4. **Test Reports**: Generate and publish test reports

Example:

```yaml
# .github/workflows/test.yml
jobs:
  test:
    strategy:
      matrix:
        test-group: [unit, api, e2e-auth, e2e-partner, e2e-compliance]
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: npm run test:${{ matrix.test-group }}
```

## Troubleshooting

### Flaky Tests

1. **Identify Flaky Tests**: Use test reports to identify tests that fail intermittently
2. **Add Retries**: Configure tests to retry on failure
3. **Add Waits**: Use appropriate waits for asynchronous operations
4. **Improve Selectors**: Use more stable selectors

Example:

```javascript
// cypress.config.js
module.exports = {
  e2e: {
    retries: {
      runMode: 2,
      openMode: 0
    }
  }
};
```

### Slow Tests

1. **Identify Slow Tests**: Use test reports to identify slow tests
2. **Optimize Selectors**: Use more efficient selectors
3. **Reduce Waits**: Use more precise waits
4. **Parallelize Tests**: Run tests in parallel

Example:

```javascript
// Instead of this
cy.wait(5000);

// Use this
cy.get('[data-testid="loaded-indicator"]', { timeout: 5000 }).should('be.visible');
```
