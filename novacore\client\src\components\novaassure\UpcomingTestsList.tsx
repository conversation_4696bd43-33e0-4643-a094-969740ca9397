/**
 * Upcoming Tests List Component
 * 
 * This component displays a list of upcoming scheduled tests.
 */

import React from 'react';
import Link from 'next/link';
import { Calendar, Clock, ArrowRight } from 'lucide-react';
import { Button } from '../ui/button';

interface UpcomingTest {
  _id: string;
  testId: string;
  testName: string;
  controlId: string;
  controlName: string;
  frameworkId: string;
  frameworkName: string;
  nextRun: string;
  owner: string;
}

interface UpcomingTestsListProps {
  tests: UpcomingTest[];
}

export const UpcomingTestsList: React.FC<UpcomingTestsListProps> = ({ tests }) => {
  // Format date to readable format
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };
  
  // Calculate days remaining
  const getDaysRemaining = (dateString: string): number => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const targetDate = new Date(dateString);
    targetDate.setHours(0, 0, 0, 0);
    
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };
  
  // Get status color based on days remaining
  const getStatusColor = (daysRemaining: number): string => {
    if (daysRemaining < 0) return 'text-red-600';
    if (daysRemaining <= 2) return 'text-yellow-600';
    return 'text-green-600';
  };
  
  // Get status text based on days remaining
  const getStatusText = (daysRemaining: number): string => {
    if (daysRemaining < 0) return 'Overdue';
    if (daysRemaining === 0) return 'Today';
    if (daysRemaining === 1) return 'Tomorrow';
    return `In ${daysRemaining} days`;
  };
  
  // If no tests, show empty state
  if (!tests || tests.length === 0) {
    return (
      <div className="h-48 flex items-center justify-center text-gray-500">
        No upcoming tests scheduled
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      {tests.map((test) => {
        const daysRemaining = getDaysRemaining(test.nextRun);
        const statusColor = getStatusColor(daysRemaining);
        const statusText = getStatusText(daysRemaining);
        
        return (
          <div key={test._id} className="p-3 border rounded-md bg-gray-50">
            <div className="flex justify-between items-start">
              <div>
                <div className="font-medium">{test.testName}</div>
                <div className="text-sm text-gray-500">{test.frameworkName} - {test.controlName}</div>
              </div>
              <div className={`text-sm font-medium ${statusColor}`}>
                {statusText}
              </div>
            </div>
            
            <div className="mt-2 flex justify-between items-center">
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(test.nextRun)}
              </div>
              
              <Link href={`/novaassure/tests/${test.testId}`}>
                <Button variant="outline" size="sm" className="text-xs">
                  Run Now
                </Button>
              </Link>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default UpcomingTestsList;
