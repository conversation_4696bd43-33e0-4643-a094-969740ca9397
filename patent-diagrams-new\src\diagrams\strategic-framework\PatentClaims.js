import React from 'react';
import {
  DiagramFrame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  ContainerBox,
  ContainerLabel,
  DiagramLegend,
  LegendItem,
  LegendColor,
  LegendText,
  InventorLabel
} from '../../components/DiagramComponents';

const PatentClaims = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel fontSize="18px">SYSTEMS AND METHODS FOR AI-DRIVEN COMPLIANCE ENFORCEMENT USING TENSOR-NIST FUSION</ContainerLabel>
      </ContainerBox>

      {/* Patent Title and Information */}
      <ComponentBox left="100px" top="70px" width="600px" height="60px">
        <ComponentNumber>901</ComponentNumber>
        <ComponentLabel fontSize="16px" color="#555555">Cyber-Safety Patent Protection</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontStyle: 'italic' }}>
            Patent Pending - David Nigel Irvin
          </div>
        </div>
      </ComponentBox>

      {/* Key Claims Section */}
      <ContainerBox width="700px" height="400px" left="50px" top="150px">
        <ContainerLabel fontSize="16px">KEY PATENT CLAIMS</ContainerLabel>
      </ContainerBox>

      {/* Tensor Operator Fusion */}
      <ComponentBox left="80px" top="190px" width="300px" height="100px">
        <ComponentNumber>902</ComponentNumber>
        <ComponentLabel fontSize="14px">Tensor Operator Fusion</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontStyle: 'italic' }}>
            A method wherein a tensor operator (⊕) automates NIST 800-53 control mapping at 3,142× industry baseline speed
          </div>
        </div>
      </ComponentBox>

      {/* NovaFlowX Engine */}
      <ComponentBox left="420px" top="190px" width="300px" height="100px">
        <ComponentNumber>903</ComponentNumber>
        <ComponentLabel fontSize="14px">NovaFlowX Engine</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontStyle: 'italic' }}>
            An apparatus executing φ-optimized compliance compilation using Golden Ratio-weighted AI training vectors
          </div>
        </div>
      </ComponentBox>

      {/* Partner Empowerment Billing Logic */}
      <ComponentBox left="80px" top="310px" width="300px" height="100px">
        <ComponentNumber>904</ComponentNumber>
        <ComponentLabel fontSize="14px">Partner Empowerment Billing Logic</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontStyle: 'italic' }}>
            A partner incentive model leveraging a (0.82 × 2)^n dynamic embedded in cloud-native billing infrastructure
          </div>
        </div>
      </ComponentBox>

      {/* Cyber-Safety Dominance Equation */}
      <ComponentBox left="420px" top="310px" width="300px" height="100px">
        <ComponentNumber>905</ComponentNumber>
        <ComponentLabel fontSize="14px">Cyber-Safety Dominance Equation</ComponentLabel>
        <div style={{ fontSize: '12px', textAlign: 'center', marginTop: '5px' }}>
          <div style={{ fontStyle: 'italic' }}>
            Use of CSDE = (N ⊗ G ⊕ C) × π10³ as a self-remediating compliance trigger for CVE-based threat mitigation
          </div>
        </div>
      </ComponentBox>

      {/* Patentable Workarounds */}
      <ComponentBox left="80px" top="430px" width="640px" height="100px">
        <ComponentNumber>906</ComponentNumber>
        <ComponentLabel fontSize="14px">Patentable Workarounds for Abstract Concepts</ComponentLabel>
        <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%', padding: '10px', fontSize: '10px' }}>
          <div style={{ width: '30%', textAlign: 'center' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Raw CSDE Equation</div>
            <div style={{ fontStyle: 'italic', fontSize: '9px' }}>
              Method using CSDE in operations
            </div>
          </div>
          <div style={{ width: '30%', textAlign: 'center' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>18/82 Split Model</div>
            <div style={{ fontStyle: 'italic', fontSize: '9px' }}>
              Cloud billing with partner logic
            </div>
          </div>
          <div style={{ width: '30%', textAlign: 'center' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>3-6-9-12-13 Framework</div>
            <div style={{ fontStyle: 'italic', fontSize: '9px' }}>
              AI grid with 13-node risk lenses
            </div>
          </div>
        </div>
      </ComponentBox>

      {/* Legend */}
      <DiagramLegend>
        <LegendItem>
          <LegendColor color="#fff" />
          <LegendText>Patent Claims</LegendText>
        </LegendItem>
        <LegendItem>
          <LegendColor color="#555555" />
          <LegendText>Protected IP</LegendText>
        </LegendItem>
      </DiagramLegend>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default PatentClaims;
