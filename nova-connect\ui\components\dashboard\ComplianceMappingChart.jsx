import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON>lt<PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Treemap
} from 'recharts';

/**
 * Compliance Mapping Chart Component
 * 
 * Displays charts for compliance mapping metrics.
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Compliance mapping data
 * @param {Object} props.metrics - Compliance mapping metrics
 */
const ComplianceMappingChart = ({ data, metrics }) => {
  const theme = useTheme();
  
  // If no data is available, show a message
  if (!data || !data.frameworks || data.frameworks.length === 0) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%' 
      }}>
        <Typography variant="body2" color="text.secondary">
          No compliance mapping data available
        </Typography>
      </Box>
    );
  }
  
  // Prepare data for framework coverage chart
  const frameworkCoverageData = data.frameworks.map(framework => ({
    name: framework.name,
    implemented: framework.implementedControls,
    partiallyImplemented: framework.partiallyImplementedControls,
    notImplemented: framework.notImplementedControls,
    unmapped: framework.unmappedControls,
    total: framework.totalControls
  }));
  
  // Prepare data for mapping distribution pie chart
  const mappingDistributionData = [
    { name: 'Predefined', value: data.mappingDistribution.predefined },
    { name: 'Semantic', value: data.mappingDistribution.semantic },
    { name: 'Machine Learning', value: data.mappingDistribution.machineLearning }
  ];
  
  // Prepare data for gap severity treemap
  const gapSeverityData = [];
  
  // Process gap severity data
  data.frameworks.forEach(framework => {
    if (framework.gapSeverity) {
      gapSeverityData.push({
        name: `${framework.name} - High`,
        size: framework.gapSeverity.high.length,
        category: 'High'
      });
      
      gapSeverityData.push({
        name: `${framework.name} - Medium`,
        size: framework.gapSeverity.medium.length,
        category: 'Medium'
      });
      
      gapSeverityData.push({
        name: `${framework.name} - Low`,
        size: framework.gapSeverity.low.length,
        category: 'Low'
      });
    }
  });
  
  // Calculate success rate
  const successRate = metrics.totalRequests > 0 
    ? metrics.successfulRequests / metrics.totalRequests 
    : 1;
  
  // Format success rate as percentage
  const successRateFormatted = `${(successRate * 100).toFixed(1)}%`;
  
  // Colors for pie chart
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.info.main
  ];
  
  // Colors for treemap
  const SEVERITY_COLORS = {
    'High': theme.palette.error.main,
    'Medium': theme.palette.warning.main,
    'Low': theme.palette.success.main
  };
  
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Summary Metrics */}
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-around', 
        mb: 1 
      }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Success Rate
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {successRateFormatted}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Avg. Latency
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {metrics.averageLatency.toFixed(2)}ms
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Total Mappings
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {data.totalMappings}
          </Typography>
        </Box>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="caption" color="text.secondary">
            Avg. Confidence
          </Typography>
          <Typography variant="body2" fontWeight="bold">
            {(data.averageConfidence * 100).toFixed(1)}%
          </Typography>
        </Box>
      </Box>
      
      {/* Charts */}
      <Box sx={{ display: 'flex', flexGrow: 1 }}>
        {/* Framework Coverage Chart */}
        <Box sx={{ width: '60%', height: '100%' }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={frameworkCoverageData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              layout="vertical"
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                type="number"
                tick={{ fontSize: 10 }}
              />
              <YAxis 
                type="category"
                dataKey="name" 
                tick={{ fontSize: 10 }}
              />
              <Tooltip 
                formatter={(value, name) => {
                  if (name === 'implemented') return [value, 'Implemented Controls'];
                  if (name === 'partiallyImplemented') return [value, 'Partially Implemented'];
                  if (name === 'notImplemented') return [value, 'Not Implemented'];
                  if (name === 'unmapped') return [value, 'Unmapped Controls'];
                  return [value, name];
                }}
              />
              <Legend />
              <Bar 
                dataKey="implemented" 
                name="Implemented" 
                stackId="a" 
                fill={theme.palette.success.main} 
              />
              <Bar 
                dataKey="partiallyImplemented" 
                name="Partially" 
                stackId="a" 
                fill={theme.palette.warning.main} 
              />
              <Bar 
                dataKey="notImplemented" 
                name="Not Implemented" 
                stackId="a" 
                fill={theme.palette.error.main} 
              />
              <Bar 
                dataKey="unmapped" 
                name="Unmapped" 
                stackId="a" 
                fill={theme.palette.grey[500]} 
              />
            </BarChart>
          </ResponsiveContainer>
        </Box>
        
        {/* Mapping Distribution Pie Chart */}
        <Box sx={{ width: '40%', height: '100%' }}>
          <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
            Mapping Source Distribution
          </Typography>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={mappingDistributionData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {mappingDistributionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value, name) => [value, name]}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </Box>
      </Box>
      
      {/* Gap Severity Treemap */}
      <Box sx={{ height: '40%', mt: 2 }}>
        <Typography variant="caption" color="text.secondary" align="center" sx={{ display: 'block' }}>
          Gap Severity Distribution
        </Typography>
        <ResponsiveContainer width="100%" height="90%">
          <Treemap
            data={gapSeverityData}
            dataKey="size"
            aspectRatio={4/3}
            stroke="#fff"
            fill="#8884d8"
          >
            <Tooltip 
              formatter={(value, name, props) => [
                value, 
                `${props.payload.name}: ${value} gaps`
              ]}
            />
            {gapSeverityData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={SEVERITY_COLORS[entry.category]} 
              />
            ))}
          </Treemap>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default ComplianceMappingChart;
