/**
 * Theme CSS Variables
 * 
 * This file defines CSS variables for theming the NovaVision Hub.
 */

:root {
  /* Colors */
  --color-primary: #1976d2;
  --color-primaryLight: #4791db;
  --color-primaryDark: #115293;
  --color-primaryContrast: #ffffff;
  
  --color-secondary: #dc004e;
  --color-secondaryLight: #e33371;
  --color-secondaryDark: #9a0036;
  --color-secondaryContrast: #ffffff;
  
  --color-success: #4caf50;
  --color-successLight: #80e27e;
  --color-successDark: #087f23;
  --color-successContrast: #ffffff;
  
  --color-warning: #ff9800;
  --color-warningLight: #ffc947;
  --color-warningDark: #c66900;
  --color-warningContrast: #000000;
  
  --color-error: #f44336;
  --color-errorLight: #e57373;
  --color-errorDark: #d32f2f;
  --color-errorContrast: #ffffff;
  
  --color-info: #2196f3;
  --color-infoLight: #64b5f6;
  --color-infoDark: #0d47a1;
  --color-infoContrast: #ffffff;
  
  --color-background: #ffffff;
  --color-surface: #f5f5f5;
  --color-divider: #e0e0e0;
  
  --color-textPrimary: #212121;
  --color-textSecondary: #757575;
  --color-textDisabled: #9e9e9e;
  --color-textHint: #9e9e9e;
  
  --color-actionActive: rgba(0, 0, 0, 0.54);
  --color-actionHover: rgba(0, 0, 0, 0.04);
  --color-actionSelected: rgba(0, 0, 0, 0.08);
  --color-actionDisabled: rgba(0, 0, 0, 0.26);
  --color-actionDisabledBackground: rgba(0, 0, 0, 0.12);
  --color-actionFocus: rgba(0, 0, 0, 0.12);
  
  /* Typography */
  --font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  --font-family-code: "Roboto Mono", monospace;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-md: 1rem;       /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  --font-size-7xl: 4.5rem;    /* 72px */
  --font-size-8xl: 6rem;      /* 96px */
  --font-size-9xl: 8rem;      /* 128px */
  
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Spacing */
  --spacing-unit: 8px;
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 2.5rem;    /* 40px */
  --spacing-3xl: 3rem;      /* 48px */
  --spacing-4xl: 4rem;      /* 64px */
  --spacing-5xl: 5rem;      /* 80px */
  
  /* Breakpoints */
  --breakpoint-xs: 0px;
  --breakpoint-sm: 600px;
  --breakpoint-md: 960px;
  --breakpoint-lg: 1280px;
  --breakpoint-xl: 1920px;
  
  /* Shadows */
  --shadow-none: none;
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Border Radius */
  --radius-none: 0;
  --radius-xs: 0.125rem;   /* 2px */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;
  
  /* Z-Index */
  --z-index-hide: -1;
  --z-index-auto: auto;
  --z-index-base: 0;
  --z-index-docked: 10;
  --z-index-dropdown: 1000;
  --z-index-sticky: 1100;
  --z-index-banner: 1200;
  --z-index-overlay: 1300;
  --z-index-modal: 1400;
  --z-index-popover: 1500;
  --z-index-skipLink: 1600;
  --z-index-toast: 1700;
  --z-index-tooltip: 1800;
  
  /* Transitions */
  --transition-easing-easeInOut: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-easing-easeOut: cubic-bezier(0.0, 0, 0.2, 1);
  --transition-easing-easeIn: cubic-bezier(0.4, 0, 1, 1);
  --transition-easing-sharp: cubic-bezier(0.4, 0, 0.6, 1);
  
  --transition-duration-shortest: 150ms;
  --transition-duration-shorter: 200ms;
  --transition-duration-short: 250ms;
  --transition-duration-standard: 300ms;
  --transition-duration-complex: 375ms;
  --transition-duration-enteringScreen: 225ms;
  --transition-duration-leavingScreen: 195ms;
}

/* Dark Mode */
.dark-mode {
  --color-background: #121212;
  --color-surface: #1e1e1e;
  --color-divider: #424242;
  
  --color-textPrimary: #ffffff;
  --color-textSecondary: #b0b0b0;
  --color-textDisabled: #6c6c6c;
  --color-textHint: #6c6c6c;
  
  --color-actionActive: rgba(255, 255, 255, 0.7);
  --color-actionHover: rgba(255, 255, 255, 0.08);
  --color-actionSelected: rgba(255, 255, 255, 0.16);
  --color-actionDisabled: rgba(255, 255, 255, 0.3);
  --color-actionDisabledBackground: rgba(255, 255, 255, 0.12);
  --color-actionFocus: rgba(255, 255, 255, 0.12);
}

/* Utility Classes */
.bg-primary { background-color: var(--color-primary); }
.bg-primaryLight { background-color: var(--color-primaryLight); }
.bg-primaryDark { background-color: var(--color-primaryDark); }
.bg-secondary { background-color: var(--color-secondary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-info { background-color: var(--color-info); }
.bg-background { background-color: var(--color-background); }
.bg-surface { background-color: var(--color-surface); }

.text-primary { color: var(--color-primary); }
.text-primaryLight { color: var(--color-primaryLight); }
.text-primaryDark { color: var(--color-primaryDark); }
.text-primaryContrast { color: var(--color-primaryContrast); }
.text-secondary { color: var(--color-secondary); }
.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }
.text-textPrimary { color: var(--color-textPrimary); }
.text-textSecondary { color: var(--color-textSecondary); }
.text-textDisabled { color: var(--color-textDisabled); }
.text-textHint { color: var(--color-textHint); }

.border-primary { border-color: var(--color-primary); }
.border-secondary { border-color: var(--color-secondary); }
.border-success { border-color: var(--color-success); }
.border-warning { border-color: var(--color-warning); }
.border-error { border-color: var(--color-error); }
.border-info { border-color: var(--color-info); }
.border-divider { border-color: var(--color-divider); }

.font-light { font-weight: var(--font-weight-light); }
.font-regular { font-weight: var(--font-weight-regular); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }
.text-6xl { font-size: var(--font-size-6xl); }
.text-7xl { font-size: var(--font-size-7xl); }
.text-8xl { font-size: var(--font-size-8xl); }
.text-9xl { font-size: var(--font-size-9xl); }

.leading-none { line-height: var(--line-height-none); }
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

.rounded-none { border-radius: var(--radius-none); }
.rounded-xs { border-radius: var(--radius-xs); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-none { box-shadow: var(--shadow-none); }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }

.transition-standard {
  transition-property: all;
  transition-timing-function: var(--transition-easing-easeInOut);
  transition-duration: var(--transition-duration-standard);
}

.transition-fast {
  transition-property: all;
  transition-timing-function: var(--transition-easing-easeInOut);
  transition-duration: var(--transition-duration-shorter);
}

.transition-slow {
  transition-property: all;
  transition-timing-function: var(--transition-easing-easeInOut);
  transition-duration: var(--transition-duration-complex);
}
