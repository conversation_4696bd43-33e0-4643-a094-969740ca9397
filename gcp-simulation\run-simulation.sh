#!/bin/bash

# Run GCP Simulation Environment
# This script sets up and runs the GCP simulation environment for NovaFuse testing

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting GCP Simulation Environment${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
  exit 1
fi

# Create necessary directories
mkdir -p output
mkdir -p gcp-simulators/iam
mkdir -p gcp-simulators/bigquery
mkdir -p novafuse-ui

# Create placeholder files for services that aren't fully implemented yet
if [ ! -f "gcp-simulators/iam/Dockerfile" ]; then
  echo -e "${YELLOW}Creating placeholder for Cloud IAM simulator...${NC}"
  mkdir -p gcp-simulators/iam
  cat > gcp-simulators/iam/Dockerfile << EOF
FROM node:18-alpine
WORKDIR /app
RUN npm init -y && npm install express cors helmet morgan winston dotenv
COPY server.js .
EXPOSE 8082
CMD ["node", "server.js"]
EOF

  cat > gcp-simulators/iam/server.js << EOF
const express = require('express');
const app = express();
app.use(express.json());
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.post('/token', (req, res) => res.json({ access_token: 'simulation-token', expires_in: 3600 }));
app.get('/v1/projects/:projectId/roles', (req, res) => res.json({ roles: [{ name: 'roles/viewer' }, { name: 'roles/editor' }] }));
app.post('/v1/projects/:projectId:getIamPolicy', (req, res) => res.json({ bindings: [{ role: 'roles/viewer', members: ['user:<EMAIL>'] }] }));
app.listen(8082, () => console.log('Cloud IAM Simulator running on port 8082'));
EOF
fi

if [ ! -f "gcp-simulators/bigquery/Dockerfile" ]; then
  echo -e "${YELLOW}Creating placeholder for BigQuery simulator...${NC}"
  mkdir -p gcp-simulators/bigquery
  cat > gcp-simulators/bigquery/Dockerfile << EOF
FROM node:18-alpine
WORKDIR /app
RUN npm init -y && npm install express cors helmet morgan winston dotenv
COPY server.js .
EXPOSE 8083
CMD ["node", "server.js"]
EOF

  cat > gcp-simulators/bigquery/server.js << EOF
const express = require('express');
const app = express();
app.use(express.json());
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.post('/token', (req, res) => res.json({ access_token: 'simulation-token', expires_in: 3600 }));
app.post('/v2/projects/:projectId/queries', (req, res) => res.json({ jobComplete: true, rows: [{ f: [{ v: 'result1' }] }] }));
app.get('/v2/projects/:projectId/datasets/:datasetId', (req, res) => res.json({ id: req.params.datasetId, friendlyName: 'Test Dataset' }));
app.listen(8083, () => console.log('BigQuery Simulator running on port 8083'));
EOF
fi

if [ ! -f "novafuse-ui/Dockerfile" ]; then
  echo -e "${YELLOW}Creating placeholder for NovaFuse UI...${NC}"
  mkdir -p novafuse-ui
  cat > novafuse-ui/Dockerfile << EOF
FROM node:18-alpine
WORKDIR /app
RUN npm init -y && npm install express cors helmet
COPY server.js .
EXPOSE 3000
CMD ["node", "server.js"]
EOF

  cat > novafuse-ui/server.js << EOF
const express = require('express');
const app = express();
app.use(express.json());
app.use(express.static('public'));
app.get('/health', (req, res) => res.json({ status: 'ok' }));
app.get('/', (req, res) => res.send('<h1>NovaFuse UI Simulation</h1><p>This is a placeholder for the NovaFuse UI.</p>'));
app.listen(3000, () => console.log('NovaFuse UI running on port 3000'));
mkdir -p public
EOF
fi

# Build and start the Docker containers
echo -e "${YELLOW}Building and starting Docker containers...${NC}"
docker-compose up -d --build

# Wait for the containers to start
echo -e "${YELLOW}Waiting for containers to start...${NC}"
sleep 10

# Check if the containers are running
if ! docker-compose ps | grep -q "Up"; then
  echo -e "${RED}Containers failed to start. Please check the logs.${NC}"
  docker-compose logs
  exit 1
fi

echo -e "${GREEN}GCP Simulation Environment started successfully.${NC}"
echo -e "${GREEN}NovaFuse API: http://localhost:3001${NC}"
echo -e "${GREEN}NovaConnect UAC: http://localhost:3002${NC}"
echo -e "${GREEN}NovaFuse UI: http://localhost:3003${NC}"
echo -e "${GREEN}API Gateway: http://localhost:3000${NC}"

# Display logs
echo -e "${YELLOW}Displaying container logs (press Ctrl+C to exit)...${NC}"
docker-compose logs -f
