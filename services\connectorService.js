/**
 * Connector service for managing connectors
 */

/**
 * Get all available connectors
 * @returns {Promise<Array>} - List of connectors
 */
const getConnectors = async () => {
  try {
    const response = await fetch('/api/connectors');

    if (!response.ok) {
      throw new Error(`Failed to fetch connectors: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching connectors:', error);
    throw error;
  }
};

/**
 * Get a connector by ID
 * @param {string} id - Connector ID
 * @returns {Promise<Object>} - Connector details
 */
const getConnectorById = async (id) => {
  try {
    const response = await fetch(`/api/connectors/${id}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch connector: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching connector ${id}:`, error);
    throw error;
  }
};

/**
 * Install a connector
 * @param {string} id - Connector ID
 * @param {Object} config - Connector configuration
 * @returns {Promise<Object>} - Installation result
 */
const installConnector = async (id, config) => {
  try {
    const response = await fetch(`/api/connectors/${id}/install`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to install connector: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error installing connector ${id}:`, error);
    throw error;
  }
};

/**
 * Uninstall a connector
 * @param {string} id - Connector ID
 * @returns {Promise<Object>} - Uninstallation result
 */
const uninstallConnector = async (id) => {
  try {
    const response = await fetch(`/api/connectors/${id}/uninstall`, {
      method: 'POST'
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to uninstall connector: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error uninstalling connector ${id}:`, error);
    throw error;
  }
};

/**
 * Update a connector configuration
 * @param {string} id - Connector ID
 * @param {Object} config - New connector configuration
 * @returns {Promise<Object>} - Update result
 */
const updateConnectorConfig = async (id, config) => {
  try {
    const response = await fetch(`/api/connectors/${id}/config`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to update connector config: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error updating connector ${id} config:`, error);
    throw error;
  }
};

/**
 * Test a connector connection
 * @param {string} id - Connector ID
 * @param {Object} config - Connector configuration to test
 * @returns {Promise<Object>} - Test result
 */
const testConnectorConnection = async (id, config) => {
  try {
    const response = await fetch(`/api/connectors/${id}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to test connector: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error testing connector ${id}:`, error);
    throw error;
  }
};

/**
 * Execute a connector with parameters
 * @param {string} id - Connector ID
 * @param {Object} params - Parameters for execution
 * @returns {Promise<Object>} - Execution result
 */
const executeConnector = async (id, params) => {
  try {
    const response = await fetch(`/api/connectors/${id}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to execute connector: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error executing connector ${id}:`, error);
    throw error;
  }
};

/**
 * Submit a new connector for approval
 * @param {Object} connector - Connector details
 * @returns {Promise<Object>} - Submission result
 */
const submitConnector = async (connector) => {
  try {
    const response = await fetch('/api/connectors/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(connector)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to submit connector: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error submitting connector:', error);
    throw error;
  }
};

module.exports = {
  getConnectors,
  getConnectorById,
  installConnector,
  uninstallConnector,
  updateConnectorConfig,
  testConnectorConnection,
  executeConnector,
  submitConnector
};
