const express = require('express');
const { validateRequest } = require('./validation');
const controllers = require('./controllers');

const router = express.Router();

/**
 * @swagger
 * /compliance/automation/frameworks:
 *   get:
 *     summary: Get a list of compliance frameworks
 *     description: Returns a paginated list of compliance frameworks with optional filtering
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by framework category
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceFramework'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks', controllers.getFrameworks);

/**
 * @swagger
 * /compliance/automation/frameworks/{id}:
 *   get:
 *     summary: Get a specific compliance framework
 *     description: Returns a specific compliance framework by ID
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceFramework'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks/:id', controllers.getFrameworkById);

/**
 * @swagger
 * /compliance/automation/frameworks:
 *   post:
 *     summary: Create a new compliance framework
 *     description: Creates a new compliance framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               version:
 *                 type: string
 *               category:
 *                 type: string
 *               authority:
 *                 type: string
 *               website:
 *                 type: string
 *               applicability:
 *                 type: array
 *                 items:
 *                   type: string
 *             required:
 *               - name
 *               - description
 *               - version
 *               - category
 *     responses:
 *       201:
 *         description: Framework created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceFramework'
 *                 message:
 *                   type: string
 *                   example: Compliance framework created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/frameworks', validateRequest('createFramework'), controllers.createFramework);

/**
 * @swagger
 * /compliance/automation/frameworks/{id}:
 *   put:
 *     summary: Update a compliance framework
 *     description: Updates an existing compliance framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance framework ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               version:
 *                 type: string
 *               category:
 *                 type: string
 *               authority:
 *                 type: string
 *               website:
 *                 type: string
 *               applicability:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Framework updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceFramework'
 *                 message:
 *                   type: string
 *                   example: Compliance framework updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/frameworks/:id', validateRequest('updateFramework'), controllers.updateFramework);

/**
 * @swagger
 * /compliance/automation/frameworks/{id}:
 *   delete:
 *     summary: Delete a compliance framework
 *     description: Deletes an existing compliance framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance framework ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Framework deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Compliance framework deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/frameworks/:id', controllers.deleteFramework);

/**
 * @swagger
 * /compliance/automation/frameworks/{frameworkId}/requirements:
 *   get:
 *     summary: Get requirements for a framework
 *     description: Returns a paginated list of requirements for a specific compliance framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: path
 *         description: Compliance framework ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: category
 *         in: query
 *         description: Filter by requirement category
 *         schema:
 *           type: string
 *       - name: priority
 *         in: query
 *         description: Filter by requirement priority
 *         schema:
 *           type: string
 *           enum: [critical, high, medium, low]
 *       - name: status
 *         in: query
 *         description: Filter by requirement status
 *         schema:
 *           type: string
 *           enum: [applicable, not-applicable, under-review]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceRequirement'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks/:frameworkId/requirements', controllers.getFrameworkRequirements);

/**
 * @swagger
 * /compliance/automation/requirements/{id}:
 *   get:
 *     summary: Get a specific compliance requirement
 *     description: Returns a specific compliance requirement by ID
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceRequirement'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/requirements/:id', controllers.getRequirementById);

/**
 * @swagger
 * /compliance/automation/frameworks/{frameworkId}/requirements:
 *   post:
 *     summary: Create a new compliance requirement
 *     description: Creates a new compliance requirement for a specific framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: path
 *         description: Compliance framework ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [critical, high, medium, low]
 *               status:
 *                 type: string
 *                 enum: [applicable, not-applicable, under-review]
 *             required:
 *               - code
 *               - title
 *               - description
 *               - priority
 *               - status
 *     responses:
 *       201:
 *         description: Requirement created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceRequirement'
 *                 message:
 *                   type: string
 *                   example: Compliance requirement created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/frameworks/:frameworkId/requirements', validateRequest('createRequirement'), controllers.createRequirement);

/**
 * @swagger
 * /compliance/automation/requirements/{id}:
 *   put:
 *     summary: Update a compliance requirement
 *     description: Updates an existing compliance requirement
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [critical, high, medium, low]
 *               status:
 *                 type: string
 *                 enum: [applicable, not-applicable, under-review]
 *     responses:
 *       200:
 *         description: Requirement updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceRequirement'
 *                 message:
 *                   type: string
 *                   example: Compliance requirement updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/requirements/:id', validateRequest('updateRequirement'), controllers.updateRequirement);

/**
 * @swagger
 * /compliance/automation/requirements/{id}:
 *   delete:
 *     summary: Delete a compliance requirement
 *     description: Deletes an existing compliance requirement
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Requirement deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Compliance requirement deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/requirements/:id', controllers.deleteRequirement);

/**
 * @swagger
 * /compliance/automation/framework-categories:
 *   get:
 *     summary: Get framework categories
 *     description: Returns a list of compliance framework categories
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/framework-categories', controllers.getFrameworkCategories);

/**
 * @swagger
 * /compliance/automation/controls:
 *   get:
 *     summary: Get a list of compliance controls
 *     description: Returns a paginated list of compliance controls with optional filtering
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by control type
 *         schema:
 *           type: string
 *           enum: [preventive, detective, corrective, administrative, technical, physical]
 *       - name: status
 *         in: query
 *         description: Filter by control status
 *         schema:
 *           type: string
 *           enum: [implemented, partially-implemented, not-implemented, planned]
 *       - name: owner
 *         in: query
 *         description: Filter by control owner (partial match)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceControl'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/controls', controllers.getControls);

/**
 * @swagger
 * /compliance/automation/controls/{id}:
 *   get:
 *     summary: Get a specific compliance control
 *     description: Returns a specific compliance control by ID
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance control ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceControl'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/controls/:id', controllers.getControlById);

/**
 * @swagger
 * /compliance/automation/controls:
 *   post:
 *     summary: Create a new compliance control
 *     description: Creates a new compliance control
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [preventive, detective, corrective, administrative, technical, physical]
 *               status:
 *                 type: string
 *                 enum: [implemented, partially-implemented, not-implemented, planned]
 *               owner:
 *                 type: string
 *               implementationDetails:
 *                 type: string
 *               testProcedure:
 *                 type: string
 *               lastTestedDate:
 *                 type: string
 *                 format: date
 *               nextTestDate:
 *                 type: string
 *                 format: date
 *               relatedRequirements:
 *                 type: array
 *                 items:
 *                   type: string
 *             required:
 *               - name
 *               - description
 *               - type
 *               - status
 *               - owner
 *     responses:
 *       201:
 *         description: Control created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceControl'
 *                 message:
 *                   type: string
 *                   example: Compliance control created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/controls', validateRequest('createControl'), controllers.createControl);

/**
 * @swagger
 * /compliance/automation/controls/{id}:
 *   put:
 *     summary: Update a compliance control
 *     description: Updates an existing compliance control
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance control ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [preventive, detective, corrective, administrative, technical, physical]
 *               status:
 *                 type: string
 *                 enum: [implemented, partially-implemented, not-implemented, planned]
 *               owner:
 *                 type: string
 *               implementationDetails:
 *                 type: string
 *               testProcedure:
 *                 type: string
 *               lastTestedDate:
 *                 type: string
 *                 format: date
 *               nextTestDate:
 *                 type: string
 *                 format: date
 *               relatedRequirements:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Control updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceControl'
 *                 message:
 *                   type: string
 *                   example: Compliance control updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/controls/:id', validateRequest('updateControl'), controllers.updateControl);

/**
 * @swagger
 * /compliance/automation/controls/{id}:
 *   delete:
 *     summary: Delete a compliance control
 *     description: Deletes an existing compliance control
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance control ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Control deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Compliance control deleted successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/controls/:id', controllers.deleteControl);

/**
 * @swagger
 * /compliance/automation/requirements/{requirementId}/controls:
 *   get:
 *     summary: Get controls for a requirement
 *     description: Returns a paginated list of controls linked to a specific requirement
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: requirementId
 *         in: path
 *         description: Compliance requirement ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by control type
 *         schema:
 *           type: string
 *           enum: [preventive, detective, corrective, administrative, technical, physical]
 *       - name: status
 *         in: query
 *         description: Filter by control status
 *         schema:
 *           type: string
 *           enum: [implemented, partially-implemented, not-implemented, planned]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceControl'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/requirements/:requirementId/controls', controllers.getRequirementControls);

/**
 * @swagger
 * /compliance/automation/requirements/{requirementId}/controls:
 *   post:
 *     summary: Add a control to a requirement
 *     description: Links an existing control to a requirement
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: requirementId
 *         in: path
 *         description: Compliance requirement ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               controlId:
 *                 type: string
 *                 description: ID of the control to link to the requirement
 *             required:
 *               - controlId
 *     responses:
 *       200:
 *         description: Control linked to requirement successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceControl'
 *                 message:
 *                   type: string
 *                   example: Control successfully linked to requirement
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/requirements/:requirementId/controls', controllers.addControlToRequirement);

/**
 * @swagger
 * /compliance/automation/control-types:
 *   get:
 *     summary: Get control types
 *     description: Returns a list of compliance control types
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/control-types', controllers.getControlTypes);

/**
 * @swagger
 * /compliance/automation/assessments:
 *   get:
 *     summary: Get a list of compliance assessments
 *     description: Returns a paginated list of compliance assessments with optional filtering
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: frameworkId
 *         in: query
 *         description: Filter by framework ID
 *         schema:
 *           type: string
 *       - name: assessor
 *         in: query
 *         description: Filter by assessor (partial match)
 *         schema:
 *           type: string
 *       - name: status
 *         in: query
 *         description: Filter by assessment status
 *         schema:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceAssessment'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/assessments', controllers.getAssessments);

/**
 * @swagger
 * /compliance/automation/assessments/{id}:
 *   get:
 *     summary: Get a specific compliance assessment
 *     description: Returns a specific compliance assessment by ID
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAssessment'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/assessments/:id', controllers.getAssessmentById);

/**
 * @swagger
 * /compliance/automation/assessments:
 *   post:
 *     summary: Create a new compliance assessment
 *     description: Creates a new compliance assessment
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               frameworkId:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               assessor:
 *                 type: string
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, completed, cancelled]
 *               scope:
 *                 type: string
 *               findings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     requirementId:
 *                       type: string
 *                     status:
 *                       type: string
 *                       enum: [compliant, non-compliant, partially-compliant, not-applicable]
 *                     notes:
 *                       type: string
 *                     remediationPlan:
 *                       type: string
 *                     remediationDueDate:
 *                       type: string
 *                       format: date
 *             required:
 *               - frameworkId
 *               - name
 *               - assessor
 *               - startDate
 *               - status
 *     responses:
 *       201:
 *         description: Assessment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAssessment'
 *                 message:
 *                   type: string
 *                   example: Compliance assessment created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/assessments', validateRequest('createAssessment'), controllers.createAssessment);

/**
 * @swagger
 * /compliance/automation/assessments/{id}:
 *   put:
 *     summary: Update a compliance assessment
 *     description: Updates an existing compliance assessment
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               assessor:
 *                 type: string
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [planned, in-progress, completed, cancelled]
 *               scope:
 *                 type: string
 *               findings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     requirementId:
 *                       type: string
 *                     status:
 *                       type: string
 *                       enum: [compliant, non-compliant, partially-compliant, not-applicable]
 *                     notes:
 *                       type: string
 *                     remediationPlan:
 *                       type: string
 *                     remediationDueDate:
 *                       type: string
 *                       format: date
 *     responses:
 *       200:
 *         description: Assessment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAssessment'
 *                 message:
 *                   type: string
 *                   example: Compliance assessment updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/assessments/:id', validateRequest('updateAssessment'), controllers.updateAssessment);

/**
 * @swagger
 * /compliance/automation/assessments/{id}:
 *   delete:
 *     summary: Delete a compliance assessment
 *     description: Deletes an existing compliance assessment
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Assessment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Compliance assessment deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/assessments/:id', controllers.deleteAssessment);

/**
 * @swagger
 * /compliance/automation/assessments/{id}/start:
 *   post:
 *     summary: Start a compliance assessment
 *     description: Changes the status of an assessment to 'in-progress' and sets the start date
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               assessor:
 *                 type: string
 *                 description: Person or team conducting the assessment
 *               assessorType:
 *                 type: string
 *                 enum: [internal, external, third-party]
 *                 description: Type of assessor
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: Date when the assessment starts (defaults to current date)
 *     responses:
 *       200:
 *         description: Assessment started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAssessment'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - assessment cannot be started
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/assessments/:id/start', validateRequest('startAssessment'), controllers.startAssessment);

/**
 * @swagger
 * /compliance/automation/assessments/{id}/complete:
 *   post:
 *     summary: Complete a compliance assessment
 *     description: Changes the status of an assessment to 'completed', sets the end date, and records findings
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: Date when the assessment ended (defaults to current date)
 *               overallScore:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 100
 *                 description: Overall compliance score as a percentage
 *               findings:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     requirementId:
 *                       type: string
 *                     description:
 *                       type: string
 *                     severity:
 *                       type: string
 *                       enum: [critical, high, medium, low]
 *                     status:
 *                       type: string
 *                       enum: [open, in-remediation, closed]
 *               recommendations:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     description:
 *                       type: string
 *                     priority:
 *                       type: string
 *                       enum: [critical, high, medium, low]
 *                     status:
 *                       type: string
 *                       enum: [planned, in-progress, completed, rejected]
 *     responses:
 *       200:
 *         description: Assessment completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAssessment'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - assessment cannot be completed
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/assessments/:id/complete', validateRequest('completeAssessment'), controllers.completeAssessment);

/**
 * @swagger
 * /compliance/automation/assessments/{id}/results:
 *   get:
 *     summary: Get assessment results
 *     description: Returns detailed results for a completed compliance assessment
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Assessment ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     assessmentId:
 *                       type: string
 *                     assessmentName:
 *                       type: string
 *                     status:
 *                       type: string
 *                     startDate:
 *                       type: string
 *                       format: date
 *                     endDate:
 *                       type: string
 *                       format: date
 *                     assessor:
 *                       type: string
 *                     assessorType:
 *                       type: string
 *                     overallScore:
 *                       type: number
 *                     framework:
 *                       type: object
 *                       nullable: true
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         version:
 *                           type: string
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalFindings:
 *                           type: integer
 *                         findingsBySeverity:
 *                           type: object
 *                           properties:
 *                             critical:
 *                               type: integer
 *                             high:
 *                               type: integer
 *                             medium:
 *                               type: integer
 *                             low:
 *                               type: integer
 *                         findingsByStatus:
 *                           type: object
 *                           properties:
 *                             open:
 *                               type: integer
 *                             in-remediation:
 *                               type: integer
 *                             closed:
 *                               type: integer
 *                         totalRecommendations:
 *                           type: integer
 *                     findings:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           requirementId:
 *                             type: string
 *                           description:
 *                             type: string
 *                           severity:
 *                             type: string
 *                           status:
 *                             type: string
 *                     recommendations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           description:
 *                             type: string
 *                           priority:
 *                             type: string
 *                           status:
 *                             type: string
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - assessment is not completed
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/assessments/:id/results', controllers.getAssessmentResults);

/**
 * @swagger
 * /compliance/automation/rules:
 *   get:
 *     summary: Get a list of automation rules
 *     description: Returns a paginated list of compliance automation rules with optional filtering
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: requirementId
 *         in: query
 *         description: Filter by requirement ID
 *         schema:
 *           type: string
 *       - name: triggerType
 *         in: query
 *         description: Filter by trigger type
 *         schema:
 *           type: string
 *           enum: [scheduled, event-based, manual]
 *       - name: status
 *         in: query
 *         description: Filter by rule status
 *         schema:
 *           type: string
 *           enum: [active, inactive, draft]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceAutomationRule'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/rules', controllers.getAutomationRules);

/**
 * @swagger
 * /compliance/automation/rules/{id}:
 *   get:
 *     summary: Get a specific automation rule
 *     description: Returns details of a specific compliance automation rule
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Automation rule ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAutomationRule'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/rules/:id', controllers.getAutomationRuleById);

/**
 * @swagger
 * /compliance/automation/rules:
 *   post:
 *     summary: Create a new automation rule
 *     description: Creates a new compliance automation rule
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - requirementId
 *               - triggerType
 *               - actions
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the rule
 *               description:
 *                 type: string
 *                 description: Description of the rule
 *               requirementId:
 *                 type: string
 *                 description: ID of the associated compliance requirement
 *               triggerType:
 *                 type: string
 *                 enum: [scheduled, event-based, manual]
 *                 description: Type of trigger for the rule
 *               triggerCondition:
 *                 type: string
 *                 description: Condition that triggers the rule
 *               actions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - type
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [collect-evidence, notify, update-status, create-task]
 *                     parameters:
 *                       type: object
 *                 description: Actions to perform when the rule is triggered
 *               schedule:
 *                 type: string
 *                 description: Schedule for the rule (required for scheduled rules)
 *               frequency:
 *                 type: string
 *                 enum: [daily, weekly, monthly, quarterly, annually, custom]
 *                 description: Frequency of rule execution
 *               status:
 *                 type: string
 *                 enum: [active, inactive, draft]
 *                 default: active
 *                 description: Status of the rule
 *     responses:
 *       201:
 *         description: Rule created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAutomationRule'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/rules', validateRequest('createAutomationRule'), controllers.createAutomationRule);

/**
 * @swagger
 * /compliance/automation/rules/{id}:
 *   put:
 *     summary: Update an automation rule
 *     description: Updates an existing compliance automation rule
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Automation rule ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the rule
 *               description:
 *                 type: string
 *                 description: Description of the rule
 *               requirementId:
 *                 type: string
 *                 description: ID of the associated compliance requirement
 *               triggerType:
 *                 type: string
 *                 enum: [scheduled, event-based, manual]
 *                 description: Type of trigger for the rule
 *               triggerCondition:
 *                 type: string
 *                 description: Condition that triggers the rule
 *               actions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - type
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [collect-evidence, notify, update-status, create-task]
 *                     parameters:
 *                       type: object
 *                 description: Actions to perform when the rule is triggered
 *               schedule:
 *                 type: string
 *                 description: Schedule for the rule
 *               frequency:
 *                 type: string
 *                 enum: [daily, weekly, monthly, quarterly, annually, custom]
 *                 description: Frequency of rule execution
 *               status:
 *                 type: string
 *                 enum: [active, inactive, draft]
 *                 description: Status of the rule
 *     responses:
 *       200:
 *         description: Rule updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAutomationRule'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/rules/:id', validateRequest('updateAutomationRule'), controllers.updateAutomationRule);

/**
 * @swagger
 * /compliance/automation/rules/{id}:
 *   delete:
 *     summary: Delete an automation rule
 *     description: Deletes an existing compliance automation rule
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Automation rule ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Rule deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/rules/:id', controllers.deleteAutomationRule);

/**
 * @swagger
 * /compliance/automation/rules/{id}/execute:
 *   post:
 *     summary: Execute an automation rule
 *     description: Manually executes a compliance automation rule
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Automation rule ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Rule executed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     rule:
 *                       $ref: '#/components/schemas/ComplianceAutomationRule'
 *                     executionResults:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           status:
 *                             type: string
 *                             enum: [success, warning, error]
 *                           message:
 *                             type: string
 *                           details:
 *                             type: object
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - rule is not active
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/rules/:id/execute', controllers.executeAutomationRule);

/**
 * @swagger
 * /compliance/automation/rules/{id}/enable:
 *   post:
 *     summary: Enable an automation rule
 *     description: Changes the status of an automation rule to 'active'
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Automation rule ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Rule enabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAutomationRule'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - rule is already active
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/rules/:id/enable', controllers.enableAutomationRule);

/**
 * @swagger
 * /compliance/automation/rules/{id}/disable:
 *   post:
 *     summary: Disable an automation rule
 *     description: Changes the status of an automation rule to 'inactive'
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Automation rule ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Rule disabled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceAutomationRule'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - rule is already inactive
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/rules/:id/disable', controllers.disableAutomationRule);

/**
 * @swagger
 * /compliance/automation/requirements/{requirementId}/rules:
 *   get:
 *     summary: Get automation rules for a requirement
 *     description: Returns a paginated list of automation rules for a specific compliance requirement
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: requirementId
 *         in: path
 *         description: Compliance requirement ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: status
 *         in: query
 *         description: Filter by rule status
 *         schema:
 *           type: string
 *           enum: [active, inactive, draft]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceAutomationRule'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/requirements/:requirementId/rules', controllers.getRequirementAutomationRules);

/**
 * @swagger
 * /compliance/automation/dashboard:
 *   get:
 *     summary: Get compliance dashboard data
 *     description: Returns comprehensive compliance statistics and metrics for the dashboard
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     overallComplianceScore:
 *                       type: number
 *                       description: Overall compliance score as a percentage
 *                     frameworkStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         compliant:
 *                           type: integer
 *                         partiallyCompliant:
 *                           type: integer
 *                         nonCompliant:
 *                           type: integer
 *                         notAssessed:
 *                           type: integer
 *                     requirementStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         applicable:
 *                           type: integer
 *                         notApplicable:
 *                           type: integer
 *                         underReview:
 *                           type: integer
 *                         byPriority:
 *                           type: object
 *                           properties:
 *                             critical:
 *                               type: integer
 *                             high:
 *                               type: integer
 *                             medium:
 *                               type: integer
 *                             low:
 *                               type: integer
 *                     controlStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         implemented:
 *                           type: integer
 *                         partiallyImplemented:
 *                           type: integer
 *                         notImplemented:
 *                           type: integer
 *                         planned:
 *                           type: integer
 *                         byType:
 *                           type: object
 *                           properties:
 *                             preventive:
 *                               type: integer
 *                             detective:
 *                               type: integer
 *                             corrective:
 *                               type: integer
 *                             administrative:
 *                               type: integer
 *                             technical:
 *                               type: integer
 *                             physical:
 *                               type: integer
 *                         automationStatus:
 *                           type: object
 *                           properties:
 *                             manual:
 *                               type: integer
 *                             semiAutomated:
 *                               type: integer
 *                             fullyAutomated:
 *                               type: integer
 *                     assessmentStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         planned:
 *                           type: integer
 *                         inProgress:
 *                           type: integer
 *                         completed:
 *                           type: integer
 *                         cancelled:
 *                           type: integer
 *                         averageScore:
 *                           type: number
 *                     automationStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         active:
 *                           type: integer
 *                         inactive:
 *                           type: integer
 *                         draft:
 *                           type: integer
 *                         byTriggerType:
 *                           type: object
 *                           properties:
 *                             scheduled:
 *                               type: integer
 *                             eventBased:
 *                               type: integer
 *                             manual:
 *                               type: integer
 *                     recentAssessments:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           status:
 *                             type: string
 *                           frameworkId:
 *                             type: string
 *                           startDate:
 *                             type: string
 *                             format: date
 *                           endDate:
 *                             type: string
 *                             format: date
 *                             nullable: true
 *                           overallScore:
 *                             type: number
 *                             nullable: true
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *                     upcomingRules:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           triggerType:
 *                             type: string
 *                           nextExecutionDate:
 *                             type: string
 *                             format: date-time
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/dashboard', controllers.getComplianceDashboard);

/**
 * @swagger
 * /compliance/automation/reports/compliance-status:
 *   get:
 *     summary: Get compliance status report
 *     description: Returns a detailed report on compliance status across frameworks or for a specific framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: query
 *         description: Optional framework ID to filter the report to a specific framework
 *         schema:
 *           type: string
 *       - name: detailed
 *         in: query
 *         description: Whether to include detailed information about requirements, controls, and findings
 *         schema:
 *           type: string
 *           enum: [true, false]
 *           default: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     reportName:
 *                       type: string
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *                     overallComplianceScore:
 *                       type: number
 *                     frameworks:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           version:
 *                             type: string
 *                           complianceScore:
 *                             type: number
 *                           lastAssessmentDate:
 *                             type: string
 *                             format: date
 *                             nullable: true
 *                           requirementStats:
 *                             type: object
 *                             properties:
 *                               total:
 *                                 type: integer
 *                               applicable:
 *                                 type: integer
 *                               notApplicable:
 *                                 type: integer
 *                               underReview:
 *                                 type: integer
 *                               byPriority:
 *                                 type: object
 *                                 properties:
 *                                   critical:
 *                                     type: integer
 *                                   high:
 *                                     type: integer
 *                                   medium:
 *                                     type: integer
 *                                   low:
 *                                     type: integer
 *                           controlStats:
 *                             type: object
 *                             properties:
 *                               total:
 *                                 type: integer
 *                               implemented:
 *                                 type: integer
 *                               partiallyImplemented:
 *                                 type: integer
 *                               notImplemented:
 *                                 type: integer
 *                               planned:
 *                                 type: integer
 *                               automationStatus:
 *                                 type: object
 *                                 properties:
 *                                   manual:
 *                                     type: integer
 *                                   semiAutomated:
 *                                     type: integer
 *                                   fullyAutomated:
 *                                     type: integer
 *                           latestAssessment:
 *                             type: object
 *                             nullable: true
 *                             properties:
 *                               id:
 *                                 type: string
 *                               name:
 *                                 type: string
 *                               status:
 *                                 type: string
 *                               startDate:
 *                                 type: string
 *                                 format: date
 *                               endDate:
 *                                 type: string
 *                                 format: date
 *                                 nullable: true
 *                               overallScore:
 *                                 type: number
 *                                 nullable: true
 *                               assessor:
 *                                 type: string
 *                               findingsCount:
 *                                 type: integer
 *                           requirements:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                 name:
 *                                   type: string
 *                                 description:
 *                                   type: string
 *                                 status:
 *                                   type: string
 *                                 priority:
 *                                   type: string
 *                                 controls:
 *                                   type: array
 *                                   items:
 *                                     type: object
 *                                     properties:
 *                                       id:
 *                                         type: string
 *                                       name:
 *                                         type: string
 *                                       status:
 *                                         type: string
 *                                       type:
 *                                         type: string
 *                                       automationStatus:
 *                                         type: string
 *                           findings:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                 requirementId:
 *                                   type: string
 *                                 description:
 *                                   type: string
 *                                 severity:
 *                                   type: string
 *                                 status:
 *                                   type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports/compliance-status', controllers.getComplianceStatusReport);

/**
 * @swagger
 * /compliance/automation/reports/control-effectiveness:
 *   get:
 *     summary: Get control effectiveness report
 *     description: Returns a report on the effectiveness of controls with various filtering options
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: query
 *         description: Optional framework ID to filter controls by framework
 *         schema:
 *           type: string
 *       - name: controlType
 *         in: query
 *         description: Optional control type to filter by
 *         schema:
 *           type: string
 *           enum: [preventive, detective, corrective, administrative, technical, physical]
 *       - name: effectiveness
 *         in: query
 *         description: Optional effectiveness rating to filter by
 *         schema:
 *           type: string
 *           enum: [effective, partially-effective, ineffective, not-assessed]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     reportName:
 *                       type: string
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *                     filters:
 *                       type: object
 *                       properties:
 *                         frameworkId:
 *                           type: string
 *                         controlType:
 *                           type: string
 *                         effectiveness:
 *                           type: string
 *                     overallEffectivenessRate:
 *                       type: number
 *                     effectivenessStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         effective:
 *                           type: integer
 *                         partiallyEffective:
 *                           type: integer
 *                         ineffective:
 *                           type: integer
 *                         notAssessed:
 *                           type: integer
 *                     effectivenessByType:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           total:
 *                             type: integer
 *                           effective:
 *                             type: integer
 *                           partiallyEffective:
 *                             type: integer
 *                           ineffective:
 *                             type: integer
 *                           notAssessed:
 *                             type: integer
 *                           effectivenessRate:
 *                             type: number
 *                     effectivenessByAutomation:
 *                       type: object
 *                       properties:
 *                         manual:
 *                           type: object
 *                           properties:
 *                             total:
 *                               type: integer
 *                             effective:
 *                               type: integer
 *                             partiallyEffective:
 *                               type: integer
 *                             ineffective:
 *                               type: integer
 *                             notAssessed:
 *                               type: integer
 *                             effectivenessRate:
 *                               type: number
 *                         semiAutomated:
 *                           type: object
 *                           properties:
 *                             total:
 *                               type: integer
 *                             effective:
 *                               type: integer
 *                             partiallyEffective:
 *                               type: integer
 *                             ineffective:
 *                               type: integer
 *                             notAssessed:
 *                               type: integer
 *                             effectivenessRate:
 *                               type: number
 *                         fullyAutomated:
 *                           type: object
 *                           properties:
 *                             total:
 *                               type: integer
 *                             effective:
 *                               type: integer
 *                             partiallyEffective:
 *                               type: integer
 *                             ineffective:
 *                               type: integer
 *                             notAssessed:
 *                               type: integer
 *                             effectivenessRate:
 *                               type: number
 *                     topEffectiveControls:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           type:
 *                             type: string
 *                           automationStatus:
 *                             type: string
 *                           lastTestedDate:
 *                             type: string
 *                             format: date
 *                     topIneffectiveControls:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           type:
 *                             type: string
 *                           automationStatus:
 *                             type: string
 *                           lastTestedDate:
 *                             type: string
 *                             format: date
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports/control-effectiveness', controllers.getControlEffectivenessReport);

/**
 * @swagger
 * /compliance/automation/reports/automation-coverage:
 *   get:
 *     summary: Get automation coverage report
 *     description: Returns a report on automation coverage across frameworks or for a specific framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: query
 *         description: Optional framework ID to filter the report to a specific framework
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     reportName:
 *                       type: string
 *                     generatedAt:
 *                       type: string
 *                       format: date-time
 *                     overallAutomationCoverage:
 *                       type: number
 *                     overallAutomationStats:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         manual:
 *                           type: integer
 *                         semiAutomated:
 *                           type: integer
 *                         fullyAutomated:
 *                           type: integer
 *                     frameworks:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           name:
 *                             type: string
 *                           version:
 *                             type: string
 *                           automationCoverage:
 *                             type: number
 *                           automationStats:
 *                             type: object
 *                             properties:
 *                               total:
 *                                 type: integer
 *                               manual:
 *                                 type: integer
 *                               semiAutomated:
 *                                 type: integer
 *                               fullyAutomated:
 *                                 type: integer
 *                           automationByType:
 *                             type: object
 *                             additionalProperties:
 *                               type: object
 *                               properties:
 *                                 total:
 *                                   type: integer
 *                                 manual:
 *                                   type: integer
 *                                 semiAutomated:
 *                                   type: integer
 *                                 fullyAutomated:
 *                                   type: integer
 *                                 automationCoverage:
 *                                   type: number
 *                           ruleStats:
 *                             type: object
 *                             properties:
 *                               total:
 *                                 type: integer
 *                               active:
 *                                 type: integer
 *                               inactive:
 *                                 type: integer
 *                               draft:
 *                                 type: integer
 *                               byTriggerType:
 *                                 type: object
 *                                 properties:
 *                                   scheduled:
 *                                     type: integer
 *                                   eventBased:
 *                                     type: integer
 *                                   manual:
 *                                     type: integer
 *                           requirementCoverage:
 *                             type: number
 *                           requirementsWithAutomation:
 *                             type: integer
 *                           totalRequirements:
 *                             type: integer
 *                           topAutomatedControls:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                 name:
 *                                   type: string
 *                                 type:
 *                                   type: string
 *                                 automationStatus:
 *                                   type: string
 *                                 lastTestedDate:
 *                                   type: string
 *                                   format: date
 *                           automationCandidates:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                 name:
 *                                   type: string
 *                                 type:
 *                                   type: string
 *                                 status:
 *                                   type: string
 *                                 lastTestedDate:
 *                                   type: string
 *                                   format: date
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/reports/automation-coverage', controllers.getAutomationCoverageReport);

/**
 * @swagger
 * /compliance/automation/frameworks/{frameworkId}/assessments:
 *   get:
 *     summary: Get assessments for a framework
 *     description: Returns a paginated list of assessments for a specific compliance framework
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: frameworkId
 *         in: path
 *         description: Compliance framework ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: status
 *         in: query
 *         description: Filter by assessment status
 *         schema:
 *           type: string
 *           enum: [planned, in-progress, completed, cancelled]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceAssessment'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/frameworks/:frameworkId/assessments', controllers.getFrameworkAssessments);

/**
 * @swagger
 * /compliance/automation/controls/{controlId}/evidence:
 *   get:
 *     summary: Get evidence for a control
 *     description: Returns a paginated list of evidence for a specific compliance control
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: controlId
 *         in: path
 *         description: Compliance control ID
 *         required: true
 *         schema:
 *           type: string
 *       - $ref: '#/components/parameters/page'
 *       - $ref: '#/components/parameters/limit'
 *       - $ref: '#/components/parameters/sortBy'
 *       - $ref: '#/components/parameters/sortOrder'
 *       - name: type
 *         in: query
 *         description: Filter by evidence type
 *         schema:
 *           type: string
 *           enum: [document, screenshot, log, report, certification, attestation, other]
 *       - name: status
 *         in: query
 *         description: Filter by evidence status
 *         schema:
 *           type: string
 *           enum: [valid, expired, pending-review, rejected]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ComplianceEvidence'
 *                 pagination:
 *                   $ref: '#/components/schemas/Pagination'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/controls/:controlId/evidence', controllers.getControlEvidence);

/**
 * @swagger
 * /compliance/automation/evidence/{id}:
 *   get:
 *     summary: Get a specific compliance evidence
 *     description: Returns a specific compliance evidence by ID
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance evidence ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceEvidence'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/evidence/:id', controllers.getEvidenceById);

/**
 * @swagger
 * /compliance/automation/controls/{controlId}/evidence:
 *   post:
 *     summary: Create new evidence for a control
 *     description: Creates new evidence for a specific compliance control
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: controlId
 *         in: path
 *         description: Compliance control ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [document, screenshot, log, report, certification, attestation, other]
 *               location:
 *                 type: string
 *               collectedBy:
 *                 type: string
 *               collectedAt:
 *                 type: string
 *                 format: date
 *               expiresAt:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [valid, expired, pending-review, rejected]
 *             required:
 *               - name
 *               - type
 *               - location
 *               - collectedBy
 *               - collectedAt
 *               - status
 *     responses:
 *       201:
 *         description: Evidence created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceEvidence'
 *                 message:
 *                   type: string
 *                   example: Compliance evidence created successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/controls/:controlId/evidence', validateRequest('createEvidence'), controllers.createEvidence);

/**
 * @swagger
 * /compliance/automation/evidence/{id}:
 *   put:
 *     summary: Update a compliance evidence
 *     description: Updates an existing compliance evidence
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance evidence ID
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [document, screenshot, log, report, certification, attestation, other]
 *               location:
 *                 type: string
 *               collectedBy:
 *                 type: string
 *               collectedAt:
 *                 type: string
 *                 format: date
 *               expiresAt:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *                 enum: [valid, expired, pending-review, rejected]
 *     responses:
 *       200:
 *         description: Evidence updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/ComplianceEvidence'
 *                 message:
 *                   type: string
 *                   example: Compliance evidence updated successfully
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/evidence/:id', validateRequest('updateEvidence'), controllers.updateEvidence);

/**
 * @swagger
 * /compliance/automation/evidence/{id}:
 *   delete:
 *     summary: Delete a compliance evidence
 *     description: Deletes an existing compliance evidence
 *     tags: [Compliance Automation]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Compliance evidence ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Evidence deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Compliance evidence deleted successfully
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/evidence/:id', controllers.deleteEvidence);

module.exports = router;
