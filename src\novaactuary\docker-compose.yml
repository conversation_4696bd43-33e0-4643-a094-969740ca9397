version: '3.8'

services:
  # NovaActuary™ Core Platform
  novaactuary:
    build:
      context: ../..
      dockerfile: src/novaactuary/Dockerfile
    container_name: novaactuary-core
    restart: unless-stopped
    ports:
      - "3000:3000"  # API port
      - "8080:8080"  # Dashboard port
    volumes:
      - novaactuary_data:/app/data
    environment:
      - NODE_ENV=production
      - NOVAACTUARY_VERSION=1.0.0-REVOLUTIONARY
      - PSI_ZERO_THRESHOLD=0.1
      - PI_COHERENCE_ENABLED=true
      - CSM_PRS_VALIDATION=true
      - TRINITY_ORACLE_ENABLED=true
      - LOG_LEVEL=info
    networks:
      - novaactuary-network
    healthcheck:
      test: ["CMD", "node", "novaactuary/health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          cpus: '1'
          memory: 1G

  # NovaActuary™ API Gateway
  api-gateway:
    image: nginx:alpine
    container_name: novaactuary-api-gateway
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - novaactuary
    networks:
      - novaactuary-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NovaActuary™ Dashboard
  dashboard:
    image: node:18-alpine
    container_name: novaactuary-dashboard
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./dashboard:/app
    command: sh -c "npm install && npm start"
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - NOVAACTUARY_API_URL=http://novaactuary:3000
    depends_on:
      - novaactuary
    networks:
      - novaactuary-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # NovaActuary™ Test Runner
  test-runner:
    build:
      context: ../..
      dockerfile: src/novaactuary/Dockerfile
    container_name: novaactuary-test-runner
    restart: "no"
    volumes:
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - TEST_MODE=comprehensive
      - REPORT_FORMAT=html
    command: sh -c "node novaactuary/test-novaactuary.js > /app/test-results/test-output.log"
    depends_on:
      - novaactuary
    networks:
      - novaactuary-network

  # NovaActuary™ Demo Runner
  demo-runner:
    build:
      context: ../..
      dockerfile: src/novaactuary/Dockerfile
    container_name: novaactuary-demo-runner
    restart: "no"
    volumes:
      - ./demo-results:/app/demo-results
    environment:
      - NODE_ENV=demo
      - DEMO_MODE=executive
      - REPORT_FORMAT=html
    command: sh -c "node novaactuary/demo-novaactuary.js > /app/demo-results/demo-output.log"
    depends_on:
      - novaactuary
    networks:
      - novaactuary-network

  # NovaActuary™ Performance Benchmarking
  benchmark:
    build:
      context: ../..
      dockerfile: src/novaactuary/Dockerfile
    container_name: novaactuary-benchmark
    restart: "no"
    volumes:
      - ./benchmark-results:/app/benchmark-results
    environment:
      - NODE_ENV=benchmark
      - BENCHMARK_ITERATIONS=100
      - BENCHMARK_CONCURRENCY=10
      - REPORT_FORMAT=html
    command: sh -c "node novaactuary/benchmark.js > /app/benchmark-results/benchmark-output.log"
    depends_on:
      - novaactuary
    networks:
      - novaactuary-network
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 4G

  # NovaActuary™ Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: novaactuary-prometheus
    restart: unless-stopped
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - novaactuary-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  grafana:
    image: grafana/grafana:latest
    container_name: novaactuary-grafana
    restart: unless-stopped
    volumes:
      - ./grafana/provisioning:/etc/grafana/provisioning
      - grafana_data:/var/lib/grafana
    ports:
      - "3002:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=novaactuary
      - GF_USERS_ALLOW_SIGN_UP=false
    depends_on:
      - prometheus
    networks:
      - novaactuary-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  novaactuary_data:
  prometheus_data:
  grafana_data:

networks:
  novaactuary-network:
    driver: bridge
