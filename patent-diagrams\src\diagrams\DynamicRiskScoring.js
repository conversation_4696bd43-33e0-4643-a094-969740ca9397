import React from 'react';
import {
  <PERSON><PERSON>ram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  Arrow,
  VerticalArrow,
  ContainerBox,
  ContainerLabel
} from '../components/DiagramComponents';

const DynamicRiskScoring = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="400px" left="25px" top="20px">
        <ContainerLabel>DYNAMIC RISK SCORING WITH COMPLIANCE ENFORCEMENT</ContainerLabel>
      </ContainerBox>
      
      {/* Top row components */}
      <ComponentBox left="80px" top="100px" width="130px">
        <ComponentNumber>701</ComponentNumber>
        <ComponentLabel>Transaction</ComponentLabel>
        Input
      </ComponentBox>
      
      <Arrow left="210px" top="130px" width="100px" />
      
      <ComponentBox left="320px" top="100px" width="130px">
        <ComponentNumber>702</ComponentNumber>
        <ComponentLabel>Risk Scoring</ComponentLabel>
        Engine
      </ComponentBox>
      
      <Arrow left="450px" top="130px" width="100px" />
      
      <ComponentBox left="560px" top="100px" width="130px">
        <ComponentNumber>703</ComponentNumber>
        <ComponentLabel>Compliance</ComponentLabel>
        Check
      </ComponentBox>
      
      <Arrow left="690px" top="130px" width="50px" />
      
      <ComponentBox left="650px" top="100px" width="130px" style={{ left: '650px' }}>
        <ComponentNumber>704</ComponentNumber>
        <ComponentLabel>Enforcement</ComponentLabel>
        Action
      </ComponentBox>
      
      {/* Bottom row components */}
      <ComponentBox left="80px" top="250px" width="130px">
        <ComponentNumber>705</ComponentNumber>
        <ComponentLabel>Machine Learning</ComponentLabel>
        Models
      </ComponentBox>
      
      <ComponentBox left="320px" top="250px" width="130px">
        <ComponentNumber>706</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Rules
      </ComponentBox>
      
      <ComponentBox left="560px" top="250px" width="130px">
        <ComponentNumber>707</ComponentNumber>
        <ComponentLabel>Action</ComponentLabel>
        Orchestration
      </ComponentBox>
      
      {/* Connecting arrows */}
      <VerticalArrow left="145px" top="160px" height="90px" />
      <VerticalArrow left="385px" top="160px" height="90px" />
      <VerticalArrow left="625px" top="160px" height="90px" />
      
      {/* Additional components */}
      <ComponentBox left="80px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>708</ComponentNumber>
        <ComponentLabel>Continuous</ComponentLabel>
        Learning
      </ComponentBox>
      
      <ComponentBox left="240px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>709</ComponentNumber>
        <ComponentLabel>Adaptive</ComponentLabel>
        Thresholds
      </ComponentBox>
      
      <ComponentBox left="400px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>710</ComponentNumber>
        <ComponentLabel>Regulatory</ComponentLabel>
        Alignment
      </ComponentBox>
      
      <ComponentBox left="560px" top="350px" width="130px" height="50px" style={{ opacity: 0.7 }}>
        <ComponentNumber>711</ComponentNumber>
        <ComponentLabel>Audit Trail</ComponentLabel>
        Generator
      </ComponentBox>
    </DiagramFrame>
  );
};

export default DynamicRiskScoring;
