/**
 * EnvironmentalContextProcessor
 * 
 * This module implements the EnvironmentalContextProcessor class, which analyzes
 * environmental factors and their impact on biological coherence.
 * 
 * The EnvironmentalContextProcessor is responsible for:
 * 1. Analyzing environmental factors
 * 2. Calculating environmental impact on coherence
 * 3. Adjusting decay rates based on environmental conditions
 * 4. Calculating edenic distance (distance from optimal conditions)
 * 5. Supporting atmospheric pressure and oxygen level analysis
 */

const { performance } = require('perf_hooks');

/**
 * EnvironmentalContextProcessor class
 */
class EnvironmentalContextProcessor {
  /**
   * Create a new EnvironmentalContextProcessor instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableAtmosphericAnalysis: true,
      enableOxygenLevelAnalysis: true,
      enableToxinAnalysis: true,
      enableRadiationAnalysis: true,
      enableElectromagneticAnalysis: true,
      enableNutrientAnalysis: true,
      enableCaching: true,
      enableMetrics: true,
      optimalTemperature: 22.0, // Celsius
      optimalHumidity: 45.0, // Percent
      optimalOxygenLevel: 21.0, // Percent
      optimalAtmosphericPressure: 1013.25, // hPa (sea level)
      optimalLightLevel: 500, // Lux
      optimalNoiseLevel: 40, // dB
      ...options
    };
    
    // Initialize cache
    this.cache = new Map();
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalProcessed: 0
    };
    
    // Initialize optimal environmental conditions
    this.optimalConditions = {
      temperature: this.options.optimalTemperature,
      humidity: this.options.optimalHumidity,
      oxygenLevel: this.options.optimalOxygenLevel,
      atmosphericPressure: this.options.optimalAtmosphericPressure,
      lightLevel: this.options.optimalLightLevel,
      noiseLevel: this.options.optimalNoiseLevel,
      toxinLevel: 0,
      radiationLevel: 0,
      electromagneticLevel: 0,
      nutrientLevel: 100
    };
    
    console.log('EnvironmentalContextProcessor initialized');
  }
  
  /**
   * Process environmental context data
   * @param {Object} environmentalData - Environmental data
   * @returns {Object} - Processed environmental context
   */
  processEnvironmentalContext(environmentalData) {
    const startTime = performance.now();
    this.metrics.totalProcessed++;
    
    // Generate cache key
    const cacheKey = this._generateCacheKey(environmentalData);
    
    // Check cache if enabled
    if (this.options.enableCaching && this.cache.has(cacheKey)) {
      this.metrics.cacheHits++;
      const cachedResult = this.cache.get(cacheKey);
      
      // Update processing time
      this.metrics.processingTimeMs += performance.now() - startTime;
      
      return cachedResult;
    }
    
    this.metrics.cacheMisses++;
    
    // Extract environmental factors
    const {
      temperature = 22.0,
      humidity = 45.0,
      oxygenLevel = 21.0,
      atmosphericPressure = 1013.25,
      lightLevel = 500,
      noiseLevel = 40,
      toxinLevel = 0,
      radiationLevel = 0,
      electromagneticLevel = 0,
      nutrientLevel = 100,
      ...otherFactors
    } = environmentalData;
    
    // Calculate environmental impact on coherence
    const temperatureImpact = this._calculateTemperatureImpact(temperature);
    const humidityImpact = this._calculateHumidityImpact(humidity);
    const oxygenImpact = this._calculateOxygenImpact(oxygenLevel);
    const pressureImpact = this._calculatePressureImpact(atmosphericPressure);
    const lightImpact = this._calculateLightImpact(lightLevel);
    const noiseImpact = this._calculateNoiseImpact(noiseLevel);
    const toxinImpact = this._calculateToxinImpact(toxinLevel);
    const radiationImpact = this._calculateRadiationImpact(radiationLevel);
    const electromagneticImpact = this._calculateElectromagneticImpact(electromagneticLevel);
    const nutrientImpact = this._calculateNutrientImpact(nutrientLevel);
    
    // Calculate overall environmental impact
    const overallImpact = this._calculateOverallImpact({
      temperatureImpact,
      humidityImpact,
      oxygenImpact,
      pressureImpact,
      lightImpact,
      noiseImpact,
      toxinImpact,
      radiationImpact,
      electromagneticImpact,
      nutrientImpact
    });
    
    // Calculate edenic distance
    const edenicDistance = this.calculateEdenicDistance(environmentalData);
    
    // Calculate decay rate adjustment
    const decayRateAdjustment = this._calculateDecayRateAdjustment(overallImpact, edenicDistance);
    
    // Prepare result
    const result = {
      overallImpact,
      edenicDistance,
      decayRateAdjustment,
      impacts: {
        temperatureImpact,
        humidityImpact,
        oxygenImpact,
        pressureImpact,
        lightImpact,
        noiseImpact,
        toxinImpact,
        radiationImpact,
        electromagneticImpact,
        nutrientImpact
      },
      processedAt: new Date().toISOString()
    };
    
    // Cache result if caching is enabled
    if (this.options.enableCaching) {
      this.cache.set(cacheKey, result);
      
      // Limit cache size
      if (this.cache.size > 1000) {
        const oldestKey = this.cache.keys().next().value;
        this.cache.delete(oldestKey);
      }
    }
    
    // Update processing time
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    return result;
  }
  
  /**
   * Calculate edenic distance (distance from optimal environmental conditions)
   * @param {Object} environmentalData - Environmental data
   * @returns {number} - Edenic distance (0-1, where 0 is optimal)
   */
  calculateEdenicDistance(environmentalData) {
    // Extract environmental factors
    const {
      temperature = 22.0,
      humidity = 45.0,
      oxygenLevel = 21.0,
      atmosphericPressure = 1013.25,
      lightLevel = 500,
      noiseLevel = 40,
      toxinLevel = 0,
      radiationLevel = 0,
      electromagneticLevel = 0,
      nutrientLevel = 100
    } = environmentalData;
    
    // Calculate normalized distances from optimal conditions
    const temperatureDistance = Math.abs(temperature - this.optimalConditions.temperature) / 50;
    const humidityDistance = Math.abs(humidity - this.optimalConditions.humidity) / 100;
    const oxygenDistance = Math.abs(oxygenLevel - this.optimalConditions.oxygenLevel) / 21;
    const pressureDistance = Math.abs(atmosphericPressure - this.optimalConditions.atmosphericPressure) / 1000;
    const lightDistance = Math.abs(lightLevel - this.optimalConditions.lightLevel) / 1000;
    const noiseDistance = Math.abs(noiseLevel - this.optimalConditions.noiseLevel) / 100;
    const toxinDistance = toxinLevel / 100;
    const radiationDistance = radiationLevel / 100;
    const electromagneticDistance = electromagneticLevel / 100;
    const nutrientDistance = Math.abs(nutrientLevel - this.optimalConditions.nutrientLevel) / 100;
    
    // Apply 18/82 principle: 18% weight to physical factors, 82% to biological impact factors
    const physicalFactorsDistance = (
      temperatureDistance + 
      humidityDistance + 
      pressureDistance + 
      lightDistance + 
      noiseDistance
    ) / 5;
    
    const biologicalImpactFactorsDistance = (
      oxygenDistance + 
      toxinDistance + 
      radiationDistance + 
      electromagneticDistance + 
      nutrientDistance
    ) / 5;
    
    const edenicDistance = (0.18 * physicalFactorsDistance) + (0.82 * biologicalImpactFactorsDistance);
    
    // Normalize to 0-1 range
    return Math.min(1, Math.max(0, edenicDistance));
  }
  
  /**
   * Calculate temperature impact on coherence
   * @param {number} temperature - Temperature in Celsius
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculateTemperatureImpact(temperature) {
    const optimalTemp = this.optimalConditions.temperature;
    const deviation = temperature - optimalTemp;
    
    // Temperature has more negative impact when too high than too low
    if (deviation > 0) {
      return -Math.min(1, deviation / 15);
    } else {
      return -Math.min(1, Math.abs(deviation) / 20);
    }
  }
  
  /**
   * Calculate humidity impact on coherence
   * @param {number} humidity - Humidity percentage
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculateHumidityImpact(humidity) {
    const optimalHumidity = this.optimalConditions.humidity;
    const deviation = humidity - optimalHumidity;
    
    // Extreme humidity (high or low) has negative impact
    return -Math.min(1, Math.abs(deviation) / 50);
  }
  
  /**
   * Calculate oxygen impact on coherence
   * @param {number} oxygenLevel - Oxygen level percentage
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculateOxygenImpact(oxygenLevel) {
    const optimalOxygen = this.optimalConditions.oxygenLevel;
    
    // Low oxygen has severe negative impact
    if (oxygenLevel < optimalOxygen) {
      return -Math.min(1, (optimalOxygen - oxygenLevel) / 10);
    } 
    // High oxygen has mild positive impact up to a point, then negative
    else if (oxygenLevel <= 30) {
      return Math.min(0.3, (oxygenLevel - optimalOxygen) / 30);
    } else {
      return -Math.min(1, (oxygenLevel - 30) / 10);
    }
  }
  
  /**
   * Calculate atmospheric pressure impact on coherence
   * @param {number} pressure - Atmospheric pressure in hPa
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculatePressureImpact(pressure) {
    const optimalPressure = this.optimalConditions.atmosphericPressure;
    const deviation = pressure - optimalPressure;
    
    // Pressure deviations have moderate impact
    return -Math.min(1, Math.abs(deviation) / 300);
  }
  
  /**
   * Calculate light impact on coherence
   * @param {number} lightLevel - Light level in lux
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculateLightImpact(lightLevel) {
    const optimalLight = this.optimalConditions.lightLevel;
    
    // Too little light has negative impact
    if (lightLevel < optimalLight) {
      return -Math.min(1, (optimalLight - lightLevel) / 500);
    } 
    // Too much light has negative impact
    else if (lightLevel > 1000) {
      return -Math.min(1, (lightLevel - 1000) / 9000);
    }
    // Optimal range has slight positive impact
    else {
      return 0.1;
    }
  }
  
  /**
   * Calculate noise impact on coherence
   * @param {number} noiseLevel - Noise level in dB
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculateNoiseImpact(noiseLevel) {
    const optimalNoise = this.optimalConditions.noiseLevel;
    
    // Noise below optimal has no impact
    if (noiseLevel <= optimalNoise) {
      return 0;
    } 
    // Noise above optimal has increasingly negative impact
    else {
      return -Math.min(1, Math.pow((noiseLevel - optimalNoise) / 40, 1.5));
    }
  }
  
  /**
   * Calculate toxin impact on coherence
   * @param {number} toxinLevel - Toxin level (0-100)
   * @returns {number} - Impact value (-1 to 0, where 0 is neutral)
   * @private
   */
  _calculateToxinImpact(toxinLevel) {
    // Toxins only have negative impact, exponentially increasing with level
    return -Math.min(1, Math.pow(toxinLevel / 50, 2));
  }
  
  /**
   * Calculate radiation impact on coherence
   * @param {number} radiationLevel - Radiation level (0-100)
   * @returns {number} - Impact value (-1 to 0, where 0 is neutral)
   * @private
   */
  _calculateRadiationImpact(radiationLevel) {
    // Radiation only has negative impact, exponentially increasing with level
    return -Math.min(1, Math.pow(radiationLevel / 40, 2));
  }
  
  /**
   * Calculate electromagnetic impact on coherence
   * @param {number} emLevel - Electromagnetic level (0-100)
   * @returns {number} - Impact value (-1 to 0, where 0 is neutral)
   * @private
   */
  _calculateElectromagneticImpact(emLevel) {
    // EM fields have negative impact above certain thresholds
    if (emLevel < 20) {
      return 0; // Minimal impact below threshold
    } else {
      return -Math.min(1, (emLevel - 20) / 80);
    }
  }
  
  /**
   * Calculate nutrient impact on coherence
   * @param {number} nutrientLevel - Nutrient level (0-100)
   * @returns {number} - Impact value (-1 to 1, where 0 is neutral)
   * @private
   */
  _calculateNutrientImpact(nutrientLevel) {
    const optimalNutrient = this.optimalConditions.nutrientLevel;
    
    // Low nutrients have severe negative impact
    if (nutrientLevel < optimalNutrient) {
      return -Math.min(1, Math.pow((optimalNutrient - nutrientLevel) / 50, 1.5));
    } 
    // Excess nutrients have mild negative impact
    else if (nutrientLevel > optimalNutrient) {
      return -Math.min(0.5, (nutrientLevel - optimalNutrient) / 100);
    }
    // Optimal has no impact
    else {
      return 0;
    }
  }
  
  /**
   * Calculate overall environmental impact
   * @param {Object} impacts - Individual impact values
   * @returns {number} - Overall impact (-1 to 1)
   * @private
   */
  _calculateOverallImpact(impacts) {
    // Apply 18/82 principle: 18% weight to physical factors, 82% to biological impact factors
    const physicalImpact = (
      impacts.temperatureImpact + 
      impacts.humidityImpact + 
      impacts.pressureImpact + 
      impacts.lightImpact + 
      impacts.noiseImpact
    ) / 5;
    
    const biologicalImpact = (
      impacts.oxygenImpact + 
      impacts.toxinImpact + 
      impacts.radiationImpact + 
      impacts.electromagneticImpact + 
      impacts.nutrientImpact
    ) / 5;
    
    return (0.18 * physicalImpact) + (0.82 * biologicalImpact);
  }
  
  /**
   * Calculate decay rate adjustment based on environmental impact
   * @param {number} overallImpact - Overall environmental impact
   * @param {number} edenicDistance - Distance from optimal conditions
   * @returns {number} - Decay rate adjustment factor
   * @private
   */
  _calculateDecayRateAdjustment(overallImpact, edenicDistance) {
    // Negative impact increases decay rate, positive impact decreases it
    if (overallImpact < 0) {
      // Negative impact: increase decay rate (up to 3x at maximum negative impact)
      return 1 + Math.abs(overallImpact) * 2;
    } else {
      // Positive impact: decrease decay rate (up to 0.5x at maximum positive impact)
      return Math.max(0.5, 1 - overallImpact);
    }
  }
  
  /**
   * Generate cache key for environmental data
   * @param {Object} environmentalData - Environmental data
   * @returns {string} - Cache key
   * @private
   */
  _generateCacheKey(environmentalData) {
    try {
      // Round values to reduce cache variations
      const keyData = {
        t: Math.round(environmentalData.temperature * 10) / 10,
        h: Math.round(environmentalData.humidity),
        o: Math.round(environmentalData.oxygenLevel * 10) / 10,
        p: Math.round(environmentalData.atmosphericPressure),
        l: Math.round(environmentalData.lightLevel / 10) * 10,
        n: Math.round(environmentalData.noiseLevel),
        tx: Math.round(environmentalData.toxinLevel),
        r: Math.round(environmentalData.radiationLevel),
        e: Math.round(environmentalData.electromagneticLevel),
        nt: Math.round(environmentalData.nutrientLevel)
      };
      
      return JSON.stringify(keyData);
    } catch (error) {
      console.error('Error generating cache key:', error);
      return Date.now().toString(); // Fallback to timestamp
    }
  }
}

module.exports = EnvironmentalContextProcessor;
