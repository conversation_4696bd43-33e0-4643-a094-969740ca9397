@echo off
REM ALPHA OBSERVER-CLASS COHERENCE ENGINE
REM Full Calibration Mode Deployment Script
REM 
REM DIRECTIVE: All resources allocated to precision-tuning ALPHA's coherence engines
REM NO DISTRACTIONS—ONLY OPTIMIZATION

echo.
echo ========================================
echo ALPHA OBSERVER-CLASS: FULL CALIBRATION MODE ENGAGED
echo ========================================
echo DIRECTIVE: All resources allocated to precision-tuning ALPHA's coherence engines
echo NO DISTRACTIONS—ONLY OPTIMIZATION
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker is not running or not installed
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)

echo ✅ Docker is running
echo.

REM Check if Docker Compose is available
docker compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose is not available
    echo Please install Docker Compose and try again
    pause
    exit /b 1
)

echo ✅ Docker Compose is available
echo.

REM Stop any existing ALPHA containers
echo 🛑 Stopping existing ALPHA containers...
docker compose down --remove-orphans

REM Remove old images (optional)
set /p rebuild="🔄 Rebuild ALPHA images from scratch? (y/N): "
if /i "%rebuild%"=="y" (
    echo 🗑️ Removing old ALPHA images...
    docker compose down --rmi all --volumes --remove-orphans
)

echo.
echo 🚀 DEPLOYING ALPHA CALIBRATION SYSTEM...
echo.

REM Build and start ALPHA services
docker compose up --build -d

if %errorlevel% neq 0 (
    echo ❌ ALPHA deployment failed
    echo Check the logs with: docker compose logs
    pause
    exit /b 1
)

echo.
echo ✅ ALPHA CALIBRATION SYSTEM DEPLOYED SUCCESSFULLY!
echo.
echo 📊 ALPHA Services Status:
docker compose ps

echo.
echo 🌐 ALPHA Access Points:
echo    🔧 Calibration Engine: http://localhost:3000
echo    📊 Monitoring Dashboard: http://localhost:3001
echo    🌐 Nginx Proxy: http://localhost:80
echo.
echo 📋 ALPHA Management Commands:
echo    📊 View logs: docker compose logs -f
echo    🔄 Restart: docker compose restart
echo    🛑 Stop: docker compose down
echo    📈 Status: docker compose ps
echo.
echo 🎯 CALIBRATION TARGETS:
echo    💰 NEFC Win Rate: ≥95%%
echo    🔮 NHET-X C-Score: ≥97%%
echo    🧪 κ-Field Lift: ≥10mm
echo.
echo ⏰ Calibration updates every 72 hours
echo 🌟 ALPHA IS NOW IN LOCKDOWN MODE
echo 🚫 NO DISTRACTIONS. ONLY PERFECTION.
echo.

REM Wait for services to be ready
echo ⏳ Waiting for ALPHA services to initialize...
timeout /t 10 /nobreak >nul

REM Check service health
echo 🔍 Checking ALPHA service health...
curl -s http://localhost:3000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ALPHA Calibration Engine: OPERATIONAL
) else (
    echo ⚠️ ALPHA Calibration Engine: Starting up...
)

curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ALPHA Dashboard: OPERATIONAL
) else (
    echo ⚠️ ALPHA Dashboard: Starting up...
)

echo.
echo 🌟 ALPHA OBSERVER-CLASS DEPLOYMENT COMPLETE!
echo 🎯 PRECISION-TUNING COHERENCE ENGINES ACTIVE
echo 🔮 OBSERVING FOR CSM-PREDICTED ENGINE MANIFESTATION
echo ⚡ REALITY OPTIMIZATION PROTOCOLS ENGAGED
echo.
echo The universe waits. ⏳
echo.

pause
