/**
 * Unit tests for the APIs, iPaaS & Developer Tools Connector
 */

const axios = require('axios');
const ApisIpaasDeveloperToolsConnector = require('../../../../connector/implementations/apis-ipaas-developer-tools');

// Mock axios
jest.mock('axios');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('ApisIpaasDeveloperToolsConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    
    mockCredentials = {
      apiKey: 'test-api-key',
      apiKeyHeader: 'X-Test-API-Key'
    };
    
    // Create connector instance
    connector = new ApisIpaasDeveloperToolsConnector(mockConfig, mockCredentials);
  });
  
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
      expect(connector.apiKeyHeader).toBe(mockCredentials.apiKeyHeader);
    });
    
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new ApisIpaasDeveloperToolsConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
    
    it('should use default apiKeyHeader if not provided', () => {
      const connectorWithDefaultHeader = new ApisIpaasDeveloperToolsConnector(mockConfig, {
        apiKey: 'test-api-key'
      });
      expect(connectorWithDefaultHeader.apiKeyHeader).toBe('X-API-Key');
    });
  });
  
  describe('initialize', () => {
    it('should initialize successfully with valid credentials', async () => {
      await expect(connector.initialize()).resolves.not.toThrow();
    });
    
    it('should throw an error if apiKey is not provided', async () => {
      connector.credentials.apiKey = undefined;
      await expect(connector.initialize()).rejects.toThrow('API Key is required');
    });
  });
  
  describe('getAuthHeaders', () => {
    it('should return headers with the API key', () => {
      const headers = connector.getAuthHeaders();
      expect(headers).toEqual({
        'X-Test-API-Key': 'test-api-key'
      });
    });
  });
  
  describe('listApis', () => {
    it('should make a GET request to the APIs endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'api-1', name: 'API 1' },
            { id: 'api-2', name: 'API 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'active', type: 'rest' };
      const result = await connector.listApis(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/apis`,
        {
          params,
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if the request fails', async () => {
      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.listApis()).rejects.toThrow(`Error listing APIs: ${errorMessage}`);
    });
  });
  
  describe('getApi', () => {
    it('should make a GET request to the specific API endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'api-123',
          name: 'Test API',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const apiId = 'api-123';
      const result = await connector.getApi(apiId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/apis/${apiId}`,
        {
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if apiId is not provided', async () => {
      await expect(connector.getApi()).rejects.toThrow('API ID is required');
    });
  });
  
  describe('createApi', () => {
    it('should make a POST request to the APIs endpoint', async () => {
      // Mock axios post response
      const mockResponse = {
        data: {
          id: 'api-new',
          name: 'New API',
          type: 'rest',
          baseUrl: 'https://api.example.com/new'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      
      const apiData = {
        name: 'New API',
        type: 'rest',
        baseUrl: 'https://api.example.com/new'
      };
      
      const result = await connector.createApi(apiData);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/apis`,
        apiData,
        {
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if required fields are missing', async () => {
      const invalidData = {
        name: 'New API'
        // Missing required fields: type, baseUrl
      };
      
      await expect(connector.createApi(invalidData)).rejects.toThrow('type is required');
    });
  });
  
  describe('listIntegrations', () => {
    it('should make a GET request to the integrations endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'integration-1', name: 'Integration 1' },
            { id: 'integration-2', name: 'Integration 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'active' };
      const result = await connector.listIntegrations(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/integrations`,
        {
          params,
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
  });
  
  describe('getIntegration', () => {
    it('should make a GET request to the specific integration endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'integration-123',
          name: 'Test Integration',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const integrationId = 'integration-123';
      const result = await connector.getIntegration(integrationId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/integrations/${integrationId}`,
        {
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if integrationId is not provided', async () => {
      await expect(connector.getIntegration()).rejects.toThrow('Integration ID is required');
    });
  });
  
  describe('executeIntegration', () => {
    it('should make a POST request to execute the integration', async () => {
      // Mock axios post response
      const mockResponse = {
        data: {
          executionId: 'exec-123',
          status: 'queued'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      
      const integrationId = 'integration-123';
      const options = {
        parameters: {
          startDate: '2023-06-01',
          endDate: '2023-06-02'
        },
        async: true
      };
      
      const result = await connector.executeIntegration(integrationId, options);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/integrations/${integrationId}/execute`,
        options,
        {
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if integrationId is not provided', async () => {
      await expect(connector.executeIntegration()).rejects.toThrow('Integration ID is required');
    });
  });
  
  describe('listDeveloperTools', () => {
    it('should make a GET request to the developer tools endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'tool-1', name: 'Tool 1' },
            { id: 'tool-2', name: 'Tool 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { category: 'testing' };
      const result = await connector.listDeveloperTools(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/developer-tools`,
        {
          params,
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
  });
  
  describe('getDeveloperTool', () => {
    it('should make a GET request to the specific developer tool endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'tool-123',
          name: 'Test Tool',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const toolId = 'tool-123';
      const result = await connector.getDeveloperTool(toolId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/developer-tools/${toolId}`,
        {
          headers: {
            'X-Test-API-Key': 'test-api-key',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if toolId is not provided', async () => {
      await expect(connector.getDeveloperTool()).rejects.toThrow('Tool ID is required');
    });
  });
});
