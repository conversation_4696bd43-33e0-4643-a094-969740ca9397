/**
 * Enhanced Report Controller
 * 
 * This controller provides advanced reporting functionality including:
 * - Compliance reporting
 * - Performance reporting
 * - Security reporting
 * - Custom reporting with advanced filtering
 * - Multiple export formats (PDF, CSV, Excel, JSON)
 * - Report scheduling and distribution
 */

const ReportService = require('../services/ReportService');
const EnhancedReportService = require('../services/EnhancedReportService');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const logger = require('../../config/logger');

class EnhancedReportController {
  constructor() {
    this.reportService = new ReportService();
    this.enhancedReportService = new EnhancedReportService();
  }

  /**
   * Generate a compliance report
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async generateComplianceReport(req, res, next) {
    try {
      const {
        framework,
        startDate,
        endDate,
        includeControls = true,
        includeFindings = true,
        includeRemediation = true,
        format = 'pdf'
      } = req.body;

      // Validate required parameters
      if (!framework) {
        throw new ValidationError('Framework is required');
      }

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Generate the report
      const report = await this.enhancedReportService.generateComplianceReport({
        framework,
        startDate,
        endDate,
        includeControls,
        includeFindings,
        includeRemediation,
        format,
        userId: req.user.id
      });

      res.status(201).json(report);
    } catch (error) {
      logger.error('Error generating compliance report', { error: error.message });
      next(error);
    }
  }

  /**
   * Generate a performance report
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async generatePerformanceReport(req, res, next) {
    try {
      const {
        metrics = ['api_calls', 'response_time', 'error_rate'],
        startDate,
        endDate,
        interval = 'day',
        connectorIds = [],
        format = 'pdf'
      } = req.body;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Validate interval
      const validIntervals = ['hour', 'day', 'week', 'month'];
      if (!validIntervals.includes(interval)) {
        throw new ValidationError(`Invalid interval. Must be one of: ${validIntervals.join(', ')}`);
      }

      // Generate the report
      const report = await this.enhancedReportService.generatePerformanceReport({
        metrics,
        startDate,
        endDate,
        interval,
        connectorIds,
        format,
        userId: req.user.id
      });

      res.status(201).json(report);
    } catch (error) {
      logger.error('Error generating performance report', { error: error.message });
      next(error);
    }
  }

  /**
   * Generate a security report
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async generateSecurityReport(req, res, next) {
    try {
      const {
        securityDomains = ['authentication', 'authorization', 'data_protection', 'network_security'],
        startDate,
        endDate,
        includeIncidents = true,
        includeVulnerabilities = true,
        includeMitigations = true,
        format = 'pdf'
      } = req.body;

      // Validate date parameters
      if (startDate && isNaN(Date.parse(startDate))) {
        throw new ValidationError('Invalid startDate format');
      }
      if (endDate && isNaN(Date.parse(endDate))) {
        throw new ValidationError('Invalid endDate format');
      }

      // Generate the report
      const report = await this.enhancedReportService.generateSecurityReport({
        securityDomains,
        startDate,
        endDate,
        includeIncidents,
        includeVulnerabilities,
        includeMitigations,
        format,
        userId: req.user.id
      });

      res.status(201).json(report);
    } catch (error) {
      logger.error('Error generating security report', { error: error.message });
      next(error);
    }
  }

  /**
   * Generate a custom report
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async generateCustomReport(req, res, next) {
    try {
      const {
        name,
        description,
        dataSources = [],
        filters = {},
        groupBy = [],
        metrics = [],
        visualizations = [],
        format = 'pdf'
      } = req.body;

      // Validate required parameters
      if (!name) {
        throw new ValidationError('Report name is required');
      }

      if (!dataSources || dataSources.length === 0) {
        throw new ValidationError('At least one data source is required');
      }

      // Generate the report
      const report = await this.enhancedReportService.generateCustomReport({
        name,
        description,
        dataSources,
        filters,
        groupBy,
        metrics,
        visualizations,
        format,
        userId: req.user.id
      });

      res.status(201).json(report);
    } catch (error) {
      logger.error('Error generating custom report', { error: error.message });
      next(error);
    }
  }

  /**
   * Schedule a report
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async scheduleReport(req, res, next) {
    try {
      const { reportId } = req.params;
      const {
        schedule,
        recipients = [],
        startDate,
        endDate,
        timezone = 'UTC',
        format = 'pdf'
      } = req.body;

      // Validate required parameters
      if (!reportId) {
        throw new ValidationError('Report ID is required');
      }

      if (!schedule) {
        throw new ValidationError('Schedule is required');
      }

      // Validate schedule format
      const validSchedules = ['daily', 'weekly', 'monthly', 'quarterly', 'custom'];
      if (!validSchedules.includes(schedule.type)) {
        throw new ValidationError(`Invalid schedule type. Must be one of: ${validSchedules.join(', ')}`);
      }

      // Schedule the report
      const scheduledReport = await this.enhancedReportService.scheduleReport({
        reportId,
        schedule,
        recipients,
        startDate,
        endDate,
        timezone,
        format,
        userId: req.user.id
      });

      res.status(201).json(scheduledReport);
    } catch (error) {
      logger.error('Error scheduling report', { error: error.message });
      next(error);
    }
  }

  /**
   * Get report status
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getReportStatus(req, res, next) {
    try {
      const { reportId } = req.params;

      // Validate required parameters
      if (!reportId) {
        throw new ValidationError('Report ID is required');
      }

      // Get report status
      const status = await this.enhancedReportService.getReportStatus(reportId);

      res.json(status);
    } catch (error) {
      logger.error('Error getting report status', { error: error.message });
      next(error);
    }
  }

  /**
   * Download a report
   * 
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async downloadReport(req, res, next) {
    try {
      const { reportId } = req.params;
      const { format } = req.query;

      // Validate required parameters
      if (!reportId) {
        throw new ValidationError('Report ID is required');
      }

      // Get report
      const report = await this.enhancedReportService.getReportById(reportId);

      // Check if report is ready
      if (report.status !== 'completed') {
        throw new ValidationError(`Report is not ready for download. Current status: ${report.status}`);
      }

      // Download report
      const { filePath, fileName, mimeType } = await this.enhancedReportService.downloadReport(reportId, format);

      // Set headers for file download
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', mimeType);

      // Stream the file
      res.sendFile(filePath);
    } catch (error) {
      logger.error('Error downloading report', { error: error.message });
      next(error);
    }
  }
}

module.exports = new EnhancedReportController();
