/**
 * Schema Validator
 *
 * This module provides high-performance schema validation for data mapping.
 */

const Ajv = require('ajv');

/**
 * Schema Validator
 */
class SchemaValidator {
  /**
   * Create a new Schema Validator
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      enableCaching: options.enableCaching !== false,
      cacheSize: options.cacheSize || 100,
      logger: options.logger || console,
      ...options
    };

    // Initialize Ajv
    this.ajv = new Ajv({
      allErrors: true,
      coerceTypes: true,
      removeAdditional: false,
      useDefaults: true,
      ...options.ajvOptions
    });

    // Add formats
    // Note: addFormats is not available, but we don't need it for basic validation

    // Initialize cache
    this.cache = new Map();
    this.cacheTimestamps = new Map();

    // Initialize compiled schemas
    this.compiledSchemas = new Map();

    this.options.logger.debug('Schema Validator initialized', {
      cacheSize: this.options.cacheSize
    });
  }

  /**
   * Validate data against schema
   * @param {Object} data - Data to validate
   * @param {Object} schema - JSON Schema
   * @returns {Object} - Validation result
   */
  validate(data, schema) {
    // Generate schema key
    const schemaKey = this._generateSchemaKey(schema);

    // Get or compile validator
    let validate;

    if (this.compiledSchemas.has(schemaKey)) {
      validate = this.compiledSchemas.get(schemaKey);
    } else {
      validate = this.ajv.compile(schema);
      this.compiledSchemas.set(schemaKey, validate);
    }

    // Generate cache key if caching is enabled
    const cacheKey = this.options.enableCaching ? this._generateCacheKey(data, schemaKey) : null;

    // Check cache if enabled
    if (this.options.enableCaching && this.cache.has(cacheKey)) {
      // Check if cache entry is expired
      const timestamp = this.cacheTimestamps.get(cacheKey);
      const now = Date.now();

      if (timestamp && now - timestamp < 60 * 60 * 1000) { // 1 hour TTL
        return this.cache.get(cacheKey);
      } else {
        // Remove expired cache entry
        this.cache.delete(cacheKey);
        this.cacheTimestamps.delete(cacheKey);
      }
    }

    // Validate data
    const valid = validate(data);

    // Create result
    const result = {
      valid,
      errors: valid ? null : validate.errors
    };

    // Cache result if caching is enabled
    if (this.options.enableCaching && cacheKey) {
      // Limit cache size
      if (this.cache.size >= this.options.cacheSize) {
        // Find oldest entry
        let oldestKey = null;
        let oldestTime = Date.now();

        for (const [key, timestamp] of this.cacheTimestamps.entries()) {
          if (timestamp < oldestTime) {
            oldestTime = timestamp;
            oldestKey = key;
          }
        }

        // Remove oldest entry
        if (oldestKey) {
          this.cache.delete(oldestKey);
          this.cacheTimestamps.delete(oldestKey);
        }
      }

      // Add new entry
      this.cache.set(cacheKey, result);
      this.cacheTimestamps.set(cacheKey, Date.now());
    }

    return result;
  }

  /**
   * Generate schema key
   * @param {Object} schema - JSON Schema
   * @returns {string} - Schema key
   * @private
   */
  _generateSchemaKey(schema) {
    return JSON.stringify(schema);
  }

  /**
   * Generate cache key
   * @param {Object} data - Data to validate
   * @param {string} schemaKey - Schema key
   * @returns {string} - Cache key
   * @private
   */
  _generateCacheKey(data, schemaKey) {
    // Use a faster hashing algorithm for large objects
    const dataString = JSON.stringify(data);

    // Simple hash function
    let hash = 0;
    for (let i = 0; i < dataString.length; i++) {
      hash = ((hash << 5) - hash) + dataString.charCodeAt(i);
      hash |= 0; // Convert to 32bit integer
    }

    return `validate-${schemaKey}-${hash}`;
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear();
    this.cacheTimestamps.clear();
  }

  /**
   * Clear compiled schemas
   */
  clearCompiledSchemas() {
    this.compiledSchemas.clear();
  }
}

module.exports = SchemaValidator;
