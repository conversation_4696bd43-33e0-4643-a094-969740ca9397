/**
 * Cybersecurity/InfoSec/Privacy Connector Implementation
 * 
 * This module implements the connector for cybersecurity, information security, and privacy systems.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('cybersecurity-infosec-privacy-connector');

/**
 * Cybersecurity/InfoSec/Privacy Connector
 */
class CybersecurityInfoSecPrivacyConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.apiKeyHeader = this.credentials.apiKeyHeader || 'X-API-Key';
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing Cybersecurity/InfoSec/Privacy connector');
    
    if (!this.credentials.apiKey) {
      throw new Error('API Key is required');
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Object} - Authentication headers
   */
  getAuthHeaders() {
    const headers = {};
    headers[this.apiKeyHeader] = this.credentials.apiKey;
    return headers;
  }

  /**
   * List vulnerabilities
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of vulnerabilities
   */
  async listVulnerabilities(params = {}) {
    logger.info('Listing vulnerabilities', { params });
    
    try {
      const response = await axios.get(`${this.baseUrl}/vulnerabilities`, {
        params,
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing vulnerabilities', { error: error.message });
      throw new Error(`Error listing vulnerabilities: ${error.message}`);
    }
  }

  /**
   * Get a vulnerability
   * 
   * @param {string} vulnerabilityId - Vulnerability ID
   * @returns {Promise<Object>} - Vulnerability details
   */
  async getVulnerability(vulnerabilityId) {
    logger.info('Getting vulnerability', { vulnerabilityId });
    
    if (!vulnerabilityId) {
      throw new Error('Vulnerability ID is required');
    }
    
    try {
      const response = await axios.get(`${this.baseUrl}/vulnerabilities/${vulnerabilityId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting vulnerability', { vulnerabilityId, error: error.message });
      throw new Error(`Error getting vulnerability: ${error.message}`);
    }
  }

  /**
   * Update vulnerability status
   * 
   * @param {string} vulnerabilityId - Vulnerability ID
   * @param {string} status - New status
   * @param {string} comment - Comment
   * @returns {Promise<Object>} - Updated vulnerability
   */
  async updateVulnerabilityStatus(vulnerabilityId, status, comment) {
    logger.info('Updating vulnerability status', { vulnerabilityId, status });
    
    if (!vulnerabilityId) {
      throw new Error('Vulnerability ID is required');
    }
    
    if (!status) {
      throw new Error('Status is required');
    }
    
    const validStatuses = ['open', 'closed', 'in_progress', 'deferred'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status: ${status}. Valid statuses are: ${validStatuses.join(', ')}`);
    }
    
    try {
      const response = await axios.patch(`${this.baseUrl}/vulnerabilities/${vulnerabilityId}/status`, {
        status,
        comment
      }, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating vulnerability status', { vulnerabilityId, status, error: error.message });
      throw new Error(`Error updating vulnerability status: ${error.message}`);
    }
  }

  /**
   * Create a vulnerability
   * 
   * @param {Object} vulnerabilityData - Vulnerability data
   * @returns {Promise<Object>} - Created vulnerability
   */
  async createVulnerability(vulnerabilityData) {
    logger.info('Creating vulnerability');
    
    if (!vulnerabilityData) {
      throw new Error('Vulnerability data is required');
    }
    
    if (!vulnerabilityData.title) {
      throw new Error('Vulnerability title is required');
    }
    
    if (!vulnerabilityData.description) {
      throw new Error('Vulnerability description is required');
    }
    
    if (!vulnerabilityData.severity) {
      throw new Error('Vulnerability severity is required');
    }
    
    const validSeverities = ['critical', 'high', 'medium', 'low', 'info'];
    if (!validSeverities.includes(vulnerabilityData.severity)) {
      throw new Error(`Invalid severity: ${vulnerabilityData.severity}. Valid severities are: ${validSeverities.join(', ')}`);
    }
    
    try {
      const response = await axios.post(`${this.baseUrl}/vulnerabilities`, vulnerabilityData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating vulnerability', { error: error.message });
      throw new Error(`Error creating vulnerability: ${error.message}`);
    }
  }

  /**
   * Update a vulnerability
   * 
   * @param {string} vulnerabilityId - Vulnerability ID
   * @param {Object} vulnerabilityData - Vulnerability data
   * @returns {Promise<Object>} - Updated vulnerability
   */
  async updateVulnerability(vulnerabilityId, vulnerabilityData) {
    logger.info('Updating vulnerability', { vulnerabilityId });
    
    if (!vulnerabilityId) {
      throw new Error('Vulnerability ID is required');
    }
    
    if (!vulnerabilityData) {
      throw new Error('Vulnerability data is required');
    }
    
    try {
      const response = await axios.put(`${this.baseUrl}/vulnerabilities/${vulnerabilityId}`, vulnerabilityData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating vulnerability', { vulnerabilityId, error: error.message });
      throw new Error(`Error updating vulnerability: ${error.message}`);
    }
  }

  /**
   * Delete a vulnerability
   * 
   * @param {string} vulnerabilityId - Vulnerability ID
   * @returns {Promise<void>}
   */
  async deleteVulnerability(vulnerabilityId) {
    logger.info('Deleting vulnerability', { vulnerabilityId });
    
    if (!vulnerabilityId) {
      throw new Error('Vulnerability ID is required');
    }
    
    try {
      await axios.delete(`${this.baseUrl}/vulnerabilities/${vulnerabilityId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Accept': 'application/json'
        }
      });
      
      logger.info('Vulnerability deleted successfully', { vulnerabilityId });
    } catch (error) {
      logger.error('Error deleting vulnerability', { vulnerabilityId, error: error.message });
      throw new Error(`Error deleting vulnerability: ${error.message}`);
    }
  }
}

module.exports = CybersecurityInfoSecPrivacyConnector;
