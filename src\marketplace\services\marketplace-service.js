/**
 * NovaFuse Universal API Connector - Marketplace Service
 *
 * This module provides services for the NovaMarketplace API.
 */

const { v4: uuidv4 } = require('uuid');
const { createLogger } = require('../../utils/logger');
const cache = require('../../utils/cache');

const logger = createLogger('marketplace-service');

// In-memory data store (would be replaced with a database in production)
const categories = [
  {
    id: 'governance',
    name: 'Governance',
    description: 'APIs for governance, risk, and compliance management',
    icon: 'governance-icon.svg',
    count: 15
  },
  {
    id: 'risk-audit',
    name: 'Risk & Audit',
    description: 'APIs for risk assessment and audit management',
    icon: 'risk-icon.svg',
    count: 12
  },
  {
    id: 'compliance',
    name: 'Compliance',
    description: 'APIs for regulatory compliance and reporting',
    icon: 'compliance-icon.svg',
    count: 18
  },
  {
    id: 'security',
    name: 'Security',
    description: 'APIs for cybersecurity and threat management',
    icon: 'security-icon.svg',
    count: 20
  },
  {
    id: 'privacy',
    name: 'Privacy',
    description: 'APIs for data privacy and protection',
    icon: 'privacy-icon.svg',
    count: 10
  },
  {
    id: 'industry-specific',
    name: 'Industry-Specific',
    description: 'APIs tailored for specific industries',
    icon: 'industry-icon.svg',
    count: 25
  }
];

const providers = [
  {
    id: 'novafuse',
    name: 'NovaFuse',
    description: 'Official NovaFuse APIs',
    website: 'https://novafuse.com',
    logo: 'novafuse-logo.svg',
    verified: true
  },
  {
    id: 'compliance-cloud',
    name: 'Compliance Cloud',
    description: 'Specialized compliance APIs',
    website: 'https://compliancecloud.example.com',
    logo: 'compliance-cloud-logo.svg',
    verified: true
  },
  {
    id: 'security-hub',
    name: 'Security Hub',
    description: 'Cybersecurity API provider',
    website: 'https://securityhub.example.com',
    logo: 'security-hub-logo.svg',
    verified: true
  },
  {
    id: 'data-shield',
    name: 'Data Shield',
    description: 'Privacy and data protection APIs',
    website: 'https://datashield.example.com',
    logo: 'data-shield-logo.svg',
    verified: false
  }
];

// Generate sample APIs
const generateApis = () => {
  const apis = [];
  const statuses = ['active', 'beta', 'deprecated'];
  const pricingModels = ['free', 'freemium', 'paid', 'subscription'];
  
  // Generate APIs for each category and provider
  categories.forEach(category => {
    providers.forEach(provider => {
      // Generate 1-3 APIs per category per provider
      const count = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < count; i++) {
        const id = uuidv4();
        const status = statuses[Math.floor(Math.random() * statuses.length)];
        const pricingModel = pricingModels[Math.floor(Math.random() * pricingModels.length)];
        const rating = (Math.random() * 4 + 1).toFixed(1); // 1.0 to 5.0
        
        apis.push({
          id,
          name: `${provider.name} ${category.name} API ${i + 1}`,
          description: `${provider.name}'s API for ${category.name.toLowerCase()} management and automation.`,
          version: '1.0.0',
          category: category.id,
          provider: provider.id,
          status,
          pricingModel,
          price: pricingModel === 'free' ? 0 : Math.floor(Math.random() * 100) + 10,
          rating: parseFloat(rating),
          reviewCount: Math.floor(Math.random() * 100),
          endpoints: [
            {
              path: `/api/${category.id}/resource1`,
              method: 'GET',
              description: 'Get resource information'
            },
            {
              path: `/api/${category.id}/resource1`,
              method: 'POST',
              description: 'Create a new resource'
            },
            {
              path: `/api/${category.id}/resource1/{id}`,
              method: 'PUT',
              description: 'Update a resource'
            }
          ],
          documentation: `https://docs.example.com/api/${id}`,
          createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
    });
  });
  
  return apis;
};

// Generate sample subscriptions
const generateSubscriptions = () => {
  return [];
};

// Initialize data
const apis = generateApis();
const subscriptions = generateSubscriptions();

/**
 * Get marketplace overview
 * @returns {Promise<Object>} Marketplace overview
 */
async function getMarketplaceOverview() {
  // Check cache first
  const cachedOverview = cache.get('marketplace:overview');
  if (cachedOverview) {
    return cachedOverview;
  }
  
  // Generate overview
  const overview = {
    message: 'NovaMarketplace API',
    status: 'Active',
    stats: {
      totalApis: apis.length,
      totalCategories: categories.length,
      totalProviders: providers.length
    },
    categories: categories.map(category => ({
      id: category.id,
      name: category.name,
      count: category.count
    })),
    featuredApis: await getFeaturedApis(),
    trendingApis: await getTrendingApis()
  };
  
  // Cache the overview
  cache.set('marketplace:overview', overview, 3600); // Cache for 1 hour
  
  return overview;
}

/**
 * Get all categories
 * @returns {Promise<Array>} List of categories
 */
async function getCategories() {
  return categories;
}

/**
 * Get a category by ID
 * @param {string} id - Category ID
 * @returns {Promise<Object>} Category
 */
async function getCategory(id) {
  const category = categories.find(c => c.id === id);
  
  if (!category) {
    logger.warn('Category not found', { id });
    throw new Error(`Category not found with ID: ${id}`);
  }
  
  // Get APIs in this category
  const categoryApis = apis.filter(api => api.category === id);
  
  return {
    ...category,
    apis: categoryApis.map(api => ({
      id: api.id,
      name: api.name,
      provider: api.provider,
      status: api.status,
      rating: api.rating
    }))
  };
}

/**
 * Get all APIs
 * @param {Object} filters - Filters
 * @param {Object} pagination - Pagination options
 * @returns {Promise<Object>} APIs with pagination
 */
async function getApis(filters = {}, pagination = { page: 1, limit: 20 }) {
  let filteredApis = [...apis];
  
  // Apply filters
  if (filters.category) {
    filteredApis = filteredApis.filter(api => api.category === filters.category);
  }
  
  if (filters.status) {
    filteredApis = filteredApis.filter(api => api.status === filters.status);
  }
  
  if (filters.provider) {
    filteredApis = filteredApis.filter(api => api.provider === filters.provider);
  }
  
  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filteredApis = filteredApis.filter(api => 
      api.name.toLowerCase().includes(searchLower) || 
      api.description.toLowerCase().includes(searchLower)
    );
  }
  
  // Apply pagination
  const totalCount = filteredApis.length;
  const totalPages = Math.ceil(totalCount / pagination.limit);
  const startIndex = (pagination.page - 1) * pagination.limit;
  const endIndex = startIndex + pagination.limit;
  
  const paginatedApis = filteredApis.slice(startIndex, endIndex);
  
  return {
    apis: paginatedApis,
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      totalCount,
      totalPages
    }
  };
}

/**
 * Get an API by ID
 * @param {string} id - API ID
 * @returns {Promise<Object>} API
 */
async function getApi(id) {
  const api = apis.find(a => a.id === id);
  
  if (!api) {
    logger.warn('API not found', { id });
    throw new Error(`API not found with ID: ${id}`);
  }
  
  // Get provider details
  const provider = providers.find(p => p.id === api.provider);
  
  // Get category details
  const category = categories.find(c => c.id === api.category);
  
  return {
    ...api,
    provider,
    category
  };
}

/**
 * Subscribe to an API
 * @param {string} apiId - API ID
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Subscription
 */
async function subscribeToApi(apiId, userId) {
  const api = apis.find(a => a.id === apiId);
  
  if (!api) {
    logger.warn('API not found', { apiId });
    throw new Error(`API not found with ID: ${apiId}`);
  }
  
  // Check if already subscribed
  const existingSubscription = subscriptions.find(s => s.apiId === apiId && s.userId === userId);
  
  if (existingSubscription) {
    return existingSubscription;
  }
  
  // Create new subscription
  const subscription = {
    id: uuidv4(),
    apiId,
    userId,
    status: 'active',
    createdAt: new Date().toISOString(),
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
  };
  
  subscriptions.push(subscription);
  
  return subscription;
}

/**
 * Get user's API subscriptions
 * @param {string} userId - User ID
 * @returns {Promise<Array>} List of subscriptions
 */
async function getUserSubscriptions(userId) {
  const userSubscriptions = subscriptions.filter(s => s.userId === userId);
  
  // Enrich with API details
  return Promise.all(userSubscriptions.map(async subscription => {
    const api = apis.find(a => a.id === subscription.apiId);
    return {
      ...subscription,
      api: {
        id: api.id,
        name: api.name,
        provider: api.provider,
        category: api.category
      }
    };
  }));
}

/**
 * Cancel an API subscription
 * @param {string} subscriptionId - Subscription ID
 * @param {string} userId - User ID
 * @returns {Promise<void>}
 */
async function cancelSubscription(subscriptionId, userId) {
  const index = subscriptions.findIndex(s => s.id === subscriptionId && s.userId === userId);
  
  if (index === -1) {
    logger.warn('Subscription not found', { subscriptionId, userId });
    throw new Error(`Subscription not found with ID: ${subscriptionId}`);
  }
  
  // Remove subscription
  subscriptions.splice(index, 1);
}

/**
 * Get all providers
 * @returns {Promise<Array>} List of providers
 */
async function getProviders() {
  return providers;
}

/**
 * Get a provider by ID
 * @param {string} id - Provider ID
 * @returns {Promise<Object>} Provider
 */
async function getProvider(id) {
  const provider = providers.find(p => p.id === id);
  
  if (!provider) {
    logger.warn('Provider not found', { id });
    throw new Error(`Provider not found with ID: ${id}`);
  }
  
  // Get APIs from this provider
  const providerApis = apis.filter(api => api.provider === id);
  
  return {
    ...provider,
    apis: providerApis.map(api => ({
      id: api.id,
      name: api.name,
      category: api.category,
      status: api.status,
      rating: api.rating
    }))
  };
}

/**
 * Get featured APIs
 * @returns {Promise<Array>} List of featured APIs
 */
async function getFeaturedApis() {
  // For demo purposes, just return the top 5 highest-rated APIs
  return apis
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 5)
    .map(api => ({
      id: api.id,
      name: api.name,
      description: api.description,
      provider: providers.find(p => p.id === api.provider).name,
      category: categories.find(c => c.id === api.category).name,
      rating: api.rating
    }));
}

/**
 * Get trending APIs
 * @returns {Promise<Array>} List of trending APIs
 */
async function getTrendingApis() {
  // For demo purposes, just return the 5 most recently updated APIs
  return apis
    .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
    .slice(0, 5)
    .map(api => ({
      id: api.id,
      name: api.name,
      description: api.description,
      provider: providers.find(p => p.id === api.provider).name,
      category: categories.find(c => c.id === api.category).name,
      rating: api.rating
    }));
}

module.exports = {
  getMarketplaceOverview,
  getCategories,
  getCategory,
  getApis,
  getApi,
  subscribeToApi,
  getUserSubscriptions,
  cancelSubscription,
  getProviders,
  getProvider,
  getFeaturedApis,
  getTrendingApis
};
