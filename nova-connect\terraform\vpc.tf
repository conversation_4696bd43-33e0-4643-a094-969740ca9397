/**
 * NovaConnect UAC VPC Service Controls
 * 
 * This Terraform configuration sets up VPC Service Controls for NovaConnect UAC
 * to isolate the marketplace deployment.
 */

# Access policy
resource "google_access_context_manager_access_policy" "nova_policy" {
  parent = "organizations/${var.organization_id}"
  title  = "NovaConnect UAC Access Policy"
}

# Service perimeter
resource "google_access_context_manager_service_perimeter" "nova_perimeter" {
  parent = "accessPolicies/${google_access_context_manager_access_policy.nova_policy.name}"
  name   = "accessPolicies/${google_access_context_manager_access_policy.nova_policy.name}/servicePerimeters/nova-marketplace"
  title  = "nova-marketplace"
  
  status {
    restricted_services = [
      "storage.googleapis.com",
      "bigquery.googleapis.com",
      "secretmanager.googleapis.com",
      "cloudfunctions.googleapis.com",
      "cloudkms.googleapis.com",
      "monitoring.googleapis.com"
    ]
    
    vpc_accessible_services {
      enable_restriction = true
      allowed_services   = [
        "container.googleapis.com",
        "compute.googleapis.com",
        "novafuse.googleapis.com"
      ]
    }
    
    ingress_policies {
      ingress_from {
        sources {
          access_level = google_access_context_manager_access_level.nova_access_level.name
        }
        identity_type = "ANY_IDENTITY"
      }
      ingress_to {
        resources = ["*"]
        operations {
          service_name = "container.googleapis.com"
          method_selectors {
            method = "*"
          }
        }
        operations {
          service_name = "compute.googleapis.com"
          method_selectors {
            method = "*"
          }
        }
      }
    }
    
    egress_policies {
      egress_from {
        identity_type = "ANY_IDENTITY"
      }
      egress_to {
        resources = ["*"]
        operations {
          service_name = "container.googleapis.com"
          method_selectors {
            method = "*"
          }
        }
        operations {
          service_name = "compute.googleapis.com"
          method_selectors {
            method = "*"
          }
        }
      }
    }
  }
  
  # Specify the projects to be protected
  spec {
    resources = [
      "projects/${var.project_number}"
    ]
  }
}

# Access level for authorized IPs
resource "google_access_context_manager_access_level" "nova_access_level" {
  parent = "accessPolicies/${google_access_context_manager_access_policy.nova_policy.name}"
  name   = "accessPolicies/${google_access_context_manager_access_policy.nova_policy.name}/accessLevels/nova_authorized_ips"
  title  = "nova_authorized_ips"
  
  basic {
    conditions {
      ip_subnetworks = var.authorized_ip_ranges
      
      device_policy {
        require_screen_lock = true
        allowed_encryption_statuses = ["ENCRYPTED"]
      }
      
      required_access_levels = []
    }
    
    combining_function = "AND"
  }
}

# VPC network
resource "google_compute_network" "nova_network" {
  name                    = "nova-network"
  auto_create_subnetworks = false
  project                 = var.project_id
}

# Subnet for GKE
resource "google_compute_subnetwork" "nova_subnet" {
  name          = "nova-subnet"
  ip_cidr_range = "10.0.0.0/16"
  region        = var.region
  network       = google_compute_network.nova_network.id
  project       = var.project_id
  
  secondary_ip_range {
    range_name    = "pods"
    ip_cidr_range = "********/16"
  }
  
  secondary_ip_range {
    range_name    = "services"
    ip_cidr_range = "********/16"
  }
}

# Firewall rules
resource "google_compute_firewall" "nova_firewall_internal" {
  name    = "nova-firewall-internal"
  network = google_compute_network.nova_network.name
  project = var.project_id
  
  allow {
    protocol = "tcp"
  }
  
  allow {
    protocol = "udp"
  }
  
  allow {
    protocol = "icmp"
  }
  
  source_ranges = ["10.0.0.0/16", "********/16", "********/16"]
}

resource "google_compute_firewall" "nova_firewall_external" {
  name    = "nova-firewall-external"
  network = google_compute_network.nova_network.name
  project = var.project_id
  
  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }
  
  source_ranges = var.authorized_ip_ranges
}

# Variables
variable "project_id" {
  description = "The GCP project ID"
  type        = string
}

variable "project_number" {
  description = "The GCP project number"
  type        = string
}

variable "organization_id" {
  description = "The GCP organization ID"
  type        = string
}

variable "region" {
  description = "The GCP region"
  type        = string
  default     = "us-central1"
}

variable "authorized_ip_ranges" {
  description = "List of authorized IP ranges"
  type        = list(string)
  default     = ["0.0.0.0/0"]  # Replace with actual authorized IPs in production
}

# Outputs
output "network_name" {
  value = google_compute_network.nova_network.name
}

output "subnet_name" {
  value = google_compute_subnetwork.nova_subnet.name
}

output "perimeter_name" {
  value = google_access_context_manager_service_perimeter.nova_perimeter.name
}
