/**
 * Resonant Comphyology Core Test
 * 
 * This script tests the Resonant Comphyology Core implementation, verifying:
 * 1. Resonance validation for Comphyology values
 * 2. Harmonization of non-resonant values
 * 3. Resonance lock mode and strict mode
 */

const fs = require('fs');
const path = require('path');
const { ComphyologyCore } = require('../../src/comphyology');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../resonance_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test Resonant Comphyology Core
 */
function testResonantComphyologyCore() {
  console.log('=== Testing Resonant Comphyology Core ===');
  
  // Create Comphyology Core with resonance lock enabled
  const comphyologyCore = new ComphyologyCore({
    enableLogging: true,
    enableCaching: false,
    resonanceLock: true,
    strictMode: false
  });
  
  // Test with different system states
  console.log('\nTesting with different system states:');
  
  // Test case 1: Simple system state
  const systemState1 = {
    structure: {
      complexity: 0.7,
      adaptability: 0.8,
      resilience: 0.6
    },
    environment: {
      volatility: 0.4,
      uncertainty: 0.3,
      complexity: 0.5,
      ambiguity: 0.2
    },
    quantum: {
      entropy: {
        value: 0.3,
        gradient: 0.1,
        threshold: 0.5
      },
      phase: {
        value: 0.6,
        coherence: 0.7,
        stability: 0.8
      }
    },
    ethics: {
      fairness: 0.9,
      transparency: 0.8,
      accountability: 0.7
    }
  };
  
  console.log('Test case 1: Simple system state');
  const result1 = comphyologyCore.calculate(systemState1);
  console.log(`Comphyology value: ${result1.comphyologyValue}`);
  console.log(`Resonance: ${JSON.stringify(result1.resonance)}`);
  
  // Test case 2: Complex system state
  const systemState2 = {
    structure: {
      complexity: 0.4,
      adaptability: 0.5,
      resilience: 0.9
    },
    environment: {
      volatility: 0.7,
      uncertainty: 0.6,
      complexity: 0.8,
      ambiguity: 0.5
    },
    quantum: {
      entropy: {
        value: 0.7,
        gradient: 0.2,
        threshold: 0.4
      },
      phase: {
        value: 0.3,
        coherence: 0.4,
        stability: 0.5
      },
      superposition: {
        states: 3,
        amplitude: [0.5, 0.3, 0.2],
        phase: [0, Math.PI/3, 2*Math.PI/3]
      }
    },
    ethics: {
      fairness: 0.6,
      transparency: 0.7,
      accountability: 0.8
    },
    decision: {
      options: ['option1', 'option2', 'option3'],
      utilities: [0.7, 0.6, 0.8],
      risks: [0.3, 0.2, 0.4],
      selectedOption: 'option1'
    }
  };
  
  console.log('\nTest case 2: Complex system state');
  const result2 = comphyologyCore.calculate(systemState2);
  console.log(`Comphyology value: ${result2.comphyologyValue}`);
  console.log(`Resonance: ${JSON.stringify(result2.resonance)}`);
  
  // Test with resonance lock disabled
  console.log('\nTesting with resonance lock disabled:');
  comphyologyCore.setResonanceLock(false);
  
  const result3 = comphyologyCore.calculate(systemState1);
  console.log(`Comphyology value: ${result3.comphyologyValue}`);
  console.log(`Resonance: ${JSON.stringify(result3.resonance)}`);
  
  // Test with resonance lock enabled and strict mode
  console.log('\nTesting with resonance lock enabled and strict mode:');
  comphyologyCore.setResonanceLock(true);
  comphyologyCore.setStrictMode(true);
  
  try {
    const result4 = comphyologyCore.calculate(systemState2);
    console.log(`Comphyology value: ${result4.comphyologyValue}`);
    console.log(`Resonance: ${JSON.stringify(result4.resonance)}`);
  } catch (error) {
    console.log(`Error in strict mode: ${error.message}`);
  }
  
  // Reset to normal mode
  comphyologyCore.setStrictMode(false);
  
  // Get resonance metrics
  console.log('\nResonance metrics:');
  const metrics = comphyologyCore.getResonanceMetrics();
  console.log(metrics);
  
  return {
    comphyologyCore,
    systemState1,
    systemState2,
    result1,
    result2,
    result3,
    metrics
  };
}

/**
 * Test Comphyology values with different patterns
 */
function testComphyologyPatterns() {
  console.log('\n=== Testing Comphyology Patterns ===');
  
  // Create Comphyology Core with resonance lock enabled
  const comphyologyCore = new ComphyologyCore({
    enableLogging: true,
    enableCaching: false,
    resonanceLock: true,
    strictMode: false
  });
  
  // Create a base system state
  const baseSystemState = {
    structure: {
      complexity: 0.6,
      adaptability: 0.6,
      resilience: 0.6
    },
    environment: {
      volatility: 0.3,
      uncertainty: 0.3,
      complexity: 0.3,
      ambiguity: 0.3
    },
    quantum: {
      entropy: {
        value: 0.3,
        gradient: 0.1,
        threshold: 0.3
      },
      phase: {
        value: 0.6,
        coherence: 0.6,
        stability: 0.6
      }
    },
    ethics: {
      fairness: 0.9,
      transparency: 0.9,
      accountability: 0.9
    }
  };
  
  // Test with variations of the 3-6-9-12-13 pattern
  console.log('\nTesting with variations of the 3-6-9-12-13 pattern:');
  
  const results = [];
  
  // Test with values aligned with the 3-6-9 pattern
  for (let i = 0; i < 10; i++) {
    // Create a variation of the system state
    const systemState = JSON.parse(JSON.stringify(baseSystemState));
    
    // Modify values to create variations
    systemState.structure.complexity = 0.3 + (i * 0.03);
    systemState.environment.volatility = 0.6 + (i * 0.03);
    systemState.quantum.entropy.value = 0.9 - (i * 0.03);
    
    // Calculate Comphyology value
    const result = comphyologyCore.calculate(systemState);
    
    console.log(`Variation ${i}: Comphyology value = ${result.comphyologyValue}, Resonant = ${result.resonance.isResonant}`);
    
    results.push({
      variation: i,
      comphyologyValue: result.comphyologyValue,
      isResonant: result.resonance.isResonant,
      wasHarmonized: result.resonance.wasHarmonized,
      resonanceDrift: result.resonance.resonanceDrift
    });
  }
  
  // Analyze results
  console.log('\nAnalysis of results:');
  
  const resonantCount = results.filter(r => r.isResonant).length;
  const harmonizedCount = results.filter(r => r.wasHarmonized).length;
  const averageDrift = results.reduce((sum, r) => sum + r.resonanceDrift, 0) / results.length;
  
  console.log(`Resonant values: ${resonantCount}/${results.length} (${(resonantCount/results.length*100).toFixed(2)}%)`);
  console.log(`Harmonized values: ${harmonizedCount}/${results.length} (${(harmonizedCount/results.length*100).toFixed(2)}%)`);
  console.log(`Average drift: ${averageDrift.toFixed(6)}`);
  
  return {
    comphyologyCore,
    baseSystemState,
    results,
    resonantCount,
    harmonizedCount,
    averageDrift
  };
}

/**
 * Main test function
 */
function main() {
  console.log('=== Resonant Comphyology Core Test ===');
  
  // Run tests
  const coreResults = testResonantComphyologyCore();
  const patternResults = testComphyologyPatterns();
  
  // Save results to file
  const results = {
    coreResults,
    patternResults
  };
  
  fs.writeFileSync(
    path.join(RESULTS_DIR, 'resonant_comphyology_core_test_results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log(`\nTest results saved to ${path.join(RESULTS_DIR, 'resonant_comphyology_core_test_results.json')}`);
}

// Run main test function
main();
