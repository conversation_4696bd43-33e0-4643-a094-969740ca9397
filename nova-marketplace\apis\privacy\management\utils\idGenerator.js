/**
 * ID Generator
 *
 * This file contains functions for generating IDs for various entities.
 */

const crypto = require('crypto');

/**
 * Generate a random ID
 * @param {number} length - Length of the ID
 * @returns {string} Random ID
 */
const generateRandomId = (length = 16) => {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
};

/**
 * Generate a data subject request ID
 * @returns {string} Data subject request ID
 */
const generateDsrId = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `DSR-${timestamp}-${random}`.toUpperCase();
};

/**
 * Generate a consent record ID
 * @returns {string} Consent record ID
 */
const generateConsentId = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `CONSENT-${timestamp}-${random}`.toUpperCase();
};

/**
 * Generate a data breach ID
 * @returns {string} Data breach ID
 */
const generateDataBreachId = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `BREACH-${timestamp}-${random}`.toUpperCase();
};

/**
 * Generate a privacy notice ID
 * @returns {string} Privacy notice ID
 */
const generatePrivacyNoticeId = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `NOTICE-${timestamp}-${random}`.toUpperCase();
};

/**
 * Generate a notification ID
 * @returns {string} Notification ID
 */
const generateNotificationId = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `NOTIF-${timestamp}-${random}`.toUpperCase();
};

/**
 * Generate a processing activity ID
 * @returns {string} Processing activity ID
 */
const generateProcessingActivityId = () => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `PROC-${timestamp}-${random}`.toUpperCase();
};

/**
 * Generate an integration ID
 * @param {string} name - Integration name
 * @returns {string} Integration ID
 */
const generateIntegrationId = (name) => {
  // Normalize the name: lowercase, replace special chars with hyphens, remove consecutive hyphens
  const normalizedName = name.toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')     // Replace multiple consecutive hyphens with a single one
    .replace(/^-|-$/g, ''); // Remove leading and trailing hyphens

  const timestamp = Date.now().toString(36);
  return `${normalizedName}-${timestamp}`;
};

/**
 * Generate a UUID
 * @returns {string} UUID
 */
const generateUuid = () => {
  return crypto.randomUUID();
};

module.exports = {
  generateRandomId,
  generateDsrId,
  generateConsentId,
  generateDataBreachId,
  generatePrivacyNoticeId,
  generateNotificationId,
  generateProcessingActivityId,
  generateIntegrationId,
  generateUuid
};
