/**
 * Comphyology Visualization Worker
 *
 * This worker handles the generation of visualization data off the main thread,
 * preventing UI blocking during complex calculations.
 */

// Support both ESM and CommonJS
let ComphyologyCore, ComphyologyVisualization;

try {
  // Try CommonJS first
  const comphyology = require('../index');
  ComphyologyCore = comphyology.ComphyologyCore;
  ComphyologyVisualization = require('../visualization');
} catch (e) {
  // Fall back to ESM
  import('../index.js').then(module => {
    ComphyologyCore = module.ComphyologyCore;
  });
  import('../visualization.js').then(module => {
    ComphyologyVisualization = module.default;
  });
}

// Initialize visualization generator
let visualizer = null;

/**
 * Initialize the worker
 *
 * @param {Object} options - Initialization options
 */
function initialize(options) {
  // Initialize ComphyologyCore and ComphyologyVisualization
  const comphyologyCore = new ComphyologyCore(options);
  visualizer = new ComphyologyVisualization({
    ...options,
    comphyologyCore
  });

  return {
    status: 'initialized',
    options
  };
}

/**
 * Generate Morphological Resonance Field data
 *
 * @param {Object} options - Visualization options
 * @returns {Object} - Visualization data
 */
function generateMorphologicalResonanceField(options) {
  if (!visualizer) {
    throw new Error('Worker not initialized');
  }

  return visualizer.generateMorphologicalResonanceField(options);
}

/**
 * Generate Quantum Phase Space Map data
 *
 * @param {Object} options - Visualization options
 * @returns {Object} - Visualization data
 */
function generateQuantumPhaseSpaceMap(options) {
  if (!visualizer) {
    throw new Error('Worker not initialized');
  }

  return visualizer.generateQuantumPhaseSpaceMap(options);
}

/**
 * Generate Ethical Tensor Projection data
 *
 * @param {Object} options - Visualization options
 * @returns {Object} - Visualization data
 */
function generateEthicalTensorProjection(options) {
  if (!visualizer) {
    throw new Error('Worker not initialized');
  }

  return visualizer.generateEthicalTensorProjection(options);
}

/**
 * Generate Trinity Integration Diagram data
 *
 * @param {Object} options - Visualization options
 * @returns {Object} - Visualization data
 */
function generateTrinityIntegrationDiagram(options) {
  if (!visualizer) {
    throw new Error('Worker not initialized');
  }

  return visualizer.generateTrinityIntegrationDiagram(options);
}

/**
 * Handle messages from the main thread
 */
self.onmessage = function(e) {
  try {
    const { type, id, payload } = e.data;

    let result;

    switch (type) {
      case 'initialize':
        result = initialize(payload);
        break;
      case 'morphological':
        result = generateMorphologicalResonanceField(payload);
        break;
      case 'quantum':
        result = generateQuantumPhaseSpaceMap(payload);
        break;
      case 'ethical':
        result = generateEthicalTensorProjection(payload);
        break;
      case 'trinity':
        result = generateTrinityIntegrationDiagram(payload);
        break;
      default:
        throw new Error(`Unknown message type: ${type}`);
    }

    // Send result back to main thread
    self.postMessage({
      type: 'result',
      id,
      payload: result
    });
  } catch (error) {
    // Send error back to main thread
    self.postMessage({
      type: 'error',
      id: e.data.id,
      payload: {
        message: error.message,
        stack: error.stack
      }
    });
  }
};

// Notify that the worker is ready
self.postMessage({
  type: 'ready'
});
