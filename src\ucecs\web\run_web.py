"""
<PERSON><PERSON><PERSON> to run the UCECS web application.

This script installs the necessary dependencies and starts the web application.
"""

import os
import sys
import subprocess
import logging
import argparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the web application."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the UCECS web application")
    parser.add_argument("--port", type=int, default=3000, help="Port to bind to")
    parser.add_argument("--dev", action="store_true", help="Run in development mode")
    args = parser.parse_args()
    
    # Get the path to the web directory
    web_dir = os.path.dirname(os.path.abspath(__file__))
    
    try:
        # Check if Node.js is installed
        try:
            subprocess.run(["node", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            logger.info("Node.js is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Node.js is not installed. Please install Node.js and npm.")
            sys.exit(1)
        
        # Check if npm is installed
        try:
            subprocess.run(["npm", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            logger.info("npm is installed")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("npm is not installed. Please install npm.")
            sys.exit(1)
        
        # Install dependencies
        logger.info("Installing dependencies...")
        subprocess.run(["npm", "install"], cwd=web_dir, check=True)
        
        # Run the application
        if args.dev:
            logger.info(f"Starting development server on port {args.port}...")
            subprocess.run(["npm", "run", "dev", "--", "--port", str(args.port)], cwd=web_dir)
        else:
            logger.info("Building the application...")
            subprocess.run(["npm", "run", "build"], cwd=web_dir, check=True)
            
            logger.info(f"Starting production server on port {args.port}...")
            subprocess.run(["npm", "run", "start", "--", "--port", str(args.port)], cwd=web_dir)
    
    except subprocess.CalledProcessError as e:
        logger.error(f"Error: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
        sys.exit(0)

if __name__ == "__main__":
    main()
