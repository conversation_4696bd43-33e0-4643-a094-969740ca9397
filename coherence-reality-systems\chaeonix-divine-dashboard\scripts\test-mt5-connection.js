#!/usr/bin/env node
/**
 * CHAEONIX MT5 CONNECTION TEST
 * Tests the connection between CHAEONIX dashboard and real MT5 demo account
 * Account: *********** (<PERSON>) | MetaQuotes-Demo
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🧪 CHAEONIX MT5 CONNECTION TEST');
console.log('='.repeat(50));
console.log('🎯 Testing connection to real MT5 demo account');
console.log('🏢 Server: MetaQuotes-Demo');
console.log('🆔 Account: *********** (<PERSON>)');
console.log('💰 Expected Balance: $100,000 USD');
console.log('='.repeat(50));

async function testMT5Connection() {
  return new Promise((resolve, reject) => {
    console.log('\n🔌 Launching Python MT5 bridge...');
    
    // Path to the MT5 connector
    const mt5ConnectorPath = path.join(__dirname, '..', '..', 'chaeonix-mt5-connector', 'test_connection.py');
    
    console.log(`📂 MT5 Connector Path: ${mt5ConnectorPath}`);
    
    // Spawn Python process to test MT5 connection
    const pythonProcess = spawn('python', [mt5ConnectorPath], {
      cwd: path.join(__dirname, '..', '..', 'chaeonix-mt5-connector'),
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let errorOutput = '';
    
    pythonProcess.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log(text.trim());
    });
    
    pythonProcess.stderr.on('data', (data) => {
      const text = data.toString();
      errorOutput += text;
      console.error('❌ Error:', text.trim());
    });
    
    pythonProcess.on('close', (code) => {
      console.log(`\n📊 Process exited with code: ${code}`);
      
      if (code === 0 && output.includes('CONNECTION TEST SUCCESSFUL')) {
        console.log('\n✅ MT5 CONNECTION TEST PASSED!');
        console.log('🎯 Real MT5 demo account is accessible');
        console.log('🔗 CHAEONIX can connect to live trading');
        
        // Parse and display account info
        try {
          const balanceMatch = output.match(/Balance: \$([0-9,]+\.[0-9]+)/);
          const equityMatch = output.match(/Equity: \$([0-9,]+\.[0-9]+)/);
          const profitMatch = output.match(/Profit: \$([0-9,.-]+)/);
          const leverageMatch = output.match(/Leverage: 1:([0-9]+)/);
          
          if (balanceMatch) {
            console.log(`💰 Account Balance: $${balanceMatch[1]}`);
          }
          if (equityMatch) {
            console.log(`💎 Account Equity: $${equityMatch[1]}`);
          }
          if (profitMatch) {
            console.log(`📈 Current Profit: $${profitMatch[1]}`);
          }
          if (leverageMatch) {
            console.log(`⚡ Leverage: 1:${leverageMatch[1]}`);
          }
        } catch (parseError) {
          console.log('⚠️ Could not parse detailed account info');
        }
        
        resolve(true);
      } else {
        console.log('\n❌ MT5 CONNECTION TEST FAILED');
        console.log('🔧 CHAEONIX will use simulation mode');
        console.log('\n🛠️ TROUBLESHOOTING:');
        console.log('   1. Ensure MetaTrader 5 terminal is installed');
        console.log('   2. Verify MT5 terminal is running');
        console.log('   3. Check account credentials are correct');
        console.log('   4. Enable "Allow automated trading" in MT5');
        console.log('   5. Install Python package: pip install MetaTrader5');
        
        if (errorOutput) {
          console.log('\n🔍 Error Details:');
          console.log(errorOutput);
        }
        
        resolve(false);
      }
    });
    
    pythonProcess.on('error', (error) => {
      console.log('\n❌ Failed to start Python process');
      console.log(`Error: ${error.message}`);
      console.log('\n🔧 Make sure Python is installed and in PATH');
      resolve(false);
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      pythonProcess.kill();
      console.log('\n⏰ Connection test timed out (30 seconds)');
      console.log('🔧 CHAEONIX will use simulation mode');
      resolve(false);
    }, 30000);
  });
}

async function testDashboardAPI() {
  console.log('\n🌐 Testing CHAEONIX Dashboard MT5 API...');
  
  try {
    const response = await fetch('http://localhost:3141/api/mt5/status');
    const data = await response.json();
    
    console.log('✅ Dashboard API Response:');
    console.log(`   🔌 Connection: ${data.connection.status}`);
    console.log(`   🏢 Server: ${data.connection.server}`);
    console.log(`   🆔 Login: ${data.connection.login}`);
    console.log(`   💰 Balance: $${data.account.balance.toLocaleString()}`);
    console.log(`   💎 Equity: $${data.account.equity.toLocaleString()}`);
    console.log(`   📈 Profit: $${data.account.profit.toFixed(2)}`);
    console.log(`   🔧 Mode: ${data.connection.mode || 'UNKNOWN'}`);
    
    return true;
  } catch (error) {
    console.log('❌ Dashboard API test failed:');
    console.log(`   Error: ${error.message}`);
    console.log('   Make sure CHAEONIX dashboard is running on port 3141');
    return false;
  }
}

async function main() {
  console.log('\n🚀 Starting comprehensive MT5 connection test...\n');
  
  // Test 1: Direct Python MT5 connection
  console.log('📋 TEST 1: Direct MT5 Python Bridge');
  const mt5Success = await testMT5Connection();
  
  // Test 2: Dashboard API integration
  console.log('\n📋 TEST 2: CHAEONIX Dashboard API');
  const apiSuccess = await testDashboardAPI();
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(50));
  console.log(`🔌 MT5 Python Bridge: ${mt5Success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🌐 Dashboard API: ${apiSuccess ? '✅ PASS' : '❌ FAIL'}`);
  
  if (mt5Success && apiSuccess) {
    console.log('\n🎉 ALL TESTS PASSED!');
    console.log('🚀 CHAEONIX is ready for live MT5 demo trading');
    console.log('🎯 You can now activate the live trading bot');
  } else if (apiSuccess) {
    console.log('\n⚠️ PARTIAL SUCCESS');
    console.log('🔧 Dashboard API works, but MT5 connection failed');
    console.log('💡 CHAEONIX will use simulation mode');
  } else {
    console.log('\n❌ TESTS FAILED');
    console.log('🔧 Check the troubleshooting steps above');
  }
  
  console.log('\n🙏 Test completed');
}

// Run the test
main().catch(console.error);
