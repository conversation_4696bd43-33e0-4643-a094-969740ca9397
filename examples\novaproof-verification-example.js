/**
 * NovaProof Verification Example
 * 
 * This example demonstrates how to use NovaProof to create evidence,
 * verify it on the blockchain, and generate cryptographic proofs.
 */

const { 
  Evidence, 
  EvidenceStatus,
  verifyEvidence,
  generateProof,
  verifyProof,
  batchVerifyEvidence,
  VerificationStatus,
  BlockchainType,
  evidenceUtils
} = require('../src/novaproof');

// Example data for compliance evidence
const evidenceData = {
  controlId: 'C-123',
  framework: 'NIST-CSF',
  source: 'GCP',
  timestamp: new Date().toISOString(),
  data: {
    value: true,
    details: 'Encryption enabled for all storage buckets',
    score: 100
  },
  status: EvidenceStatus.COLLECTED
};

async function runExample() {
  try {
    console.log('Creating evidence...');
    const evidence = new Evidence(evidenceData);
    
    console.log('Evidence created:', evidence.id);
    console.log('Evidence hash:', evidence.hash());
    console.log('Evidence status:', evidence.status);
    
    // Verify the evidence on the blockchain
    console.log('\nVerifying evidence on the blockchain...');
    const verificationResult = await verifyEvidence(evidence, {
      blockchainType: BlockchainType.ETHEREUM
    });
    
    console.log('Verification result:', verificationResult);
    console.log('Evidence status after verification:', evidence.status);
    console.log('Latest verification:', evidence.getLatestVerification());
    
    // Generate a proof for the verification
    console.log('\nGenerating proof for the verification...');
    const transaction = {
      evidenceId: evidence.id,
      contentHash: evidence.hash(),
      transactionId: verificationResult.blockchainReference.transactionId
    };
    
    const proof = generateProof(evidence, transaction);
    console.log('Proof generated:', proof);
    
    // Verify the proof
    console.log('\nVerifying the proof...');
    const isValid = verifyProof(
      proof.contentHash,
      proof.merkleRoot,
      proof.merkleProof
    );
    
    console.log('Proof is valid:', isValid);
    
    // Create multiple evidence items for batch verification
    console.log('\nCreating multiple evidence items for batch verification...');
    const evidenceItems = [];
    
    for (let i = 0; i < 5; i++) {
      const item = new Evidence({
        ...evidenceData,
        controlId: `C-${i + 100}`,
        data: {
          ...evidenceData.data,
          details: `Evidence item ${i + 1}`
        }
      });
      
      evidenceItems.push(item);
    }
    
    console.log(`Created ${evidenceItems.length} evidence items`);
    
    // Batch verify the evidence items
    console.log('\nBatch verifying evidence items...');
    const batchResults = await batchVerifyEvidence(evidenceItems, {
      blockchainType: BlockchainType.ETHEREUM
    });
    
    console.log(`Batch verification completed for ${batchResults.length} items`);
    console.log('First batch result:', batchResults[0]);
    
    // Check the status of all evidence items
    console.log('\nStatus of all evidence items after batch verification:');
    evidenceItems.forEach((item, index) => {
      console.log(`Item ${index + 1}: ${item.status}`);
    });
    
    // Create evidence from a finding
    console.log('\nCreating evidence from a finding...');
    const finding = {
      controlId: 'C-456',
      framework: 'SOC2',
      source: 'AWS',
      value: true,
      details: 'Access controls properly implemented',
      score: 95
    };
    
    const evidenceFromFinding = evidenceUtils.evidenceFromFinding(finding);
    console.log('Evidence created from finding:', evidenceFromFinding.id);
    console.log('Evidence data:', evidenceFromFinding.data);
    
    // Create evidence from a scan result
    console.log('\nCreating evidence from a scan result...');
    const scanResult = {
      id: 'scan-123',
      name: 'Weekly Security Scan',
      type: 'SECURITY',
      framework: 'ISO27001',
      source: 'SCANNER',
      timestamp: new Date().toISOString(),
      findings: [
        {
          controlId: 'ISO-A.5.1.1',
          value: true,
          details: 'Information security policies documented',
          score: 100
        },
        {
          controlId: 'ISO-A.6.1.1',
          value: true,
          details: 'Security roles defined and allocated',
          score: 90
        },
        {
          controlId: 'ISO-A.7.1.1',
          value: false,
          details: 'Background verification checks not performed',
          score: 40
        }
      ]
    };
    
    const evidenceFromScan = evidenceUtils.evidenceFromScanResult(scanResult);
    console.log(`Created ${evidenceFromScan.length} evidence items from scan result`);
    
    // Filter and group evidence
    console.log('\nFiltering and grouping evidence...');
    
    // Add the scan evidence to our collection
    const allEvidence = [...evidenceItems, ...evidenceFromScan];
    
    // Filter by status
    const collectedEvidence = evidenceUtils.filterEvidenceByStatus(
      allEvidence,
      EvidenceStatus.COLLECTED
    );
    
    console.log(`Found ${collectedEvidence.length} evidence items with COLLECTED status`);
    
    // Group by framework
    const groupedByFramework = evidenceUtils.groupEvidenceByFramework(allEvidence);
    console.log('Evidence grouped by framework:');
    Object.keys(groupedByFramework).forEach(framework => {
      console.log(`- ${framework}: ${groupedByFramework[framework].length} items`);
    });
    
    // Calculate verification coverage
    console.log('\nCalculating verification coverage...');
    const coverage = evidenceUtils.calculateVerificationCoverage(allEvidence);
    console.log('Verification coverage:', coverage);
    
    console.log('\nExample completed successfully!');
  } catch (error) {
    console.error('Error in example:', error);
  }
}

// Run the example
runExample();
