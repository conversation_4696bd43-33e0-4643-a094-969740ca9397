"""
Universal Compliance Testing Framework (UCTF).

A framework for defining, executing, and reporting on compliance tests.
"""

from .core.test_engine import TestEngine
from .core.test_manager import TestManager
from .core.result_manager import ResultManager
from .core.report_manager import ReportManager

__version__ = '0.1.0'
__all__ = [
    'TestEngine',
    'TestManager',
    'ResultManager',
    'ReportManager'
]
