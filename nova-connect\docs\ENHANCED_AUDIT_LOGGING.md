# Enhanced Audit Logging

NovaConnect provides comprehensive audit logging capabilities to track user actions and system events. This document describes the enhanced audit logging features and how to use them.

## Overview

The enhanced audit logging system provides the following features:

- **Advanced Filtering**: Filter audit logs by various criteria
- **Export Capabilities**: Export audit logs to CSV format
- **Analytics and Reporting**: Generate statistics and reports from audit logs
- **Tenant-Specific Audit Logs**: Isolate audit logs by tenant
- **BigQuery Integration**: Store and query audit logs in Google BigQuery

## API Endpoints

### Get Audit Logs

Retrieve audit logs with advanced filtering options.

```
GET /api/enhanced-audit/logs
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | ISO Date | Filter logs after this date |
| endDate | ISO Date | Filter logs before this date |
| userId | String | Filter logs by user ID |
| action | String | Filter logs by action (e.g., GET, POST, PUT, DELETE) |
| resourceType | String | Filter logs by resource type |
| resourceId | String | Filter logs by resource ID |
| status | String | Filter logs by status (success, failure) |
| teamId | String | Filter logs by team ID |
| environmentId | String | Filter logs by environment ID |
| tenantId | String | Filter logs by tenant ID |
| search | String | Search term to filter logs |
| page | Number | Page number for pagination (default: 1) |
| limit | Number | Number of logs per page (default: 100) |
| sortBy | String | Field to sort by (default: timestamp) |
| sortOrder | String | Sort order (asc, desc) (default: desc) |

#### Response

```json
{
  "logs": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "timestamp": "2023-05-01T12:34:56.789Z",
      "userId": "user-123",
      "action": "GET",
      "resourceType": "connector",
      "resourceId": "conn-456",
      "details": { "path": "/api/connectors/conn-456" },
      "ip": "***********",
      "userAgent": "Mozilla/5.0...",
      "status": "success",
      "teamId": "team-789",
      "environmentId": "env-012",
      "tenantId": "tenant-345"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 100,
    "totalItems": 1,
    "totalPages": 1
  },
  "filters": {
    "startDate": "2023-05-01T00:00:00.000Z",
    "endDate": "2023-05-02T00:00:00.000Z"
  }
}
```

### Export Audit Logs

Export audit logs to CSV format.

```
GET /api/enhanced-audit/logs/export
```

This endpoint accepts the same query parameters as the `/api/enhanced-audit/logs` endpoint.

The response is a CSV file with the following columns:
- id
- timestamp
- userId
- action
- resourceType
- resourceId
- status
- ip
- userAgent
- teamId
- environmentId
- tenantId

### Get Audit Log Statistics

Generate statistics from audit logs.

```
GET /api/enhanced-audit/logs/stats
```

This endpoint accepts the same filtering parameters as the `/api/enhanced-audit/logs` endpoint.

#### Response

```json
{
  "totalEvents": 1000,
  "byAction": {
    "GET": 500,
    "POST": 300,
    "PUT": 150,
    "DELETE": 50
  },
  "byResourceType": {
    "connector": 400,
    "user": 200,
    "team": 150,
    "credential": 250
  },
  "byStatus": {
    "success": 950,
    "failure": 50
  },
  "byUser": {
    "user-123": 300,
    "user-456": 700
  },
  "byDay": {
    "2023-05-01": 500,
    "2023-05-02": 500
  },
  "byHour": {
    "00": 50,
    "01": 30,
    "02": 20,
    "03": 10,
    "04": 5,
    "05": 5,
    "06": 10,
    "07": 20,
    "08": 50,
    "09": 100,
    "10": 150,
    "11": 200,
    "12": 100,
    "13": 50,
    "14": 50,
    "15": 50,
    "16": 30,
    "17": 20,
    "18": 20,
    "19": 10,
    "20": 10,
    "21": 5,
    "22": 5,
    "23": 0
  }
}
```

### Get Tenant-Specific Audit Logs

Retrieve audit logs for a specific tenant.

```
GET /api/enhanced-audit/tenant/:tenantId/logs
```

This endpoint accepts the same query parameters as the `/api/enhanced-audit/logs` endpoint.

### Export Tenant-Specific Audit Logs

Export audit logs for a specific tenant to CSV format.

```
GET /api/enhanced-audit/tenant/:tenantId/logs/export
```

This endpoint accepts the same query parameters as the `/api/enhanced-audit/logs` endpoint.

### Get Tenant-Specific Audit Log Statistics

Generate statistics from audit logs for a specific tenant.

```
GET /api/enhanced-audit/tenant/:tenantId/logs/stats
```

This endpoint accepts the same filtering parameters as the `/api/enhanced-audit/logs` endpoint.

## User-Specific Audit Logs

Retrieve audit logs for the current user.

```
GET /api/enhanced-audit/user/me/logs
```

This endpoint accepts the same query parameters as the `/api/enhanced-audit/logs` endpoint.

## Team-Specific Audit Logs

Retrieve audit logs for a specific team.

```
GET /api/enhanced-audit/team/:teamId/logs
```

This endpoint accepts the same query parameters as the `/api/enhanced-audit/logs` endpoint.

## Resource-Specific Audit Logs

Retrieve audit logs for a specific resource.

```
GET /api/enhanced-audit/resource/:resourceType/:resourceId/logs
```

This endpoint accepts the same query parameters as the `/api/enhanced-audit/logs` endpoint.

## BigQuery Integration

The enhanced audit logging system can be configured to store audit logs in Google BigQuery for advanced querying and analysis.

### Configuration

To enable BigQuery integration, set the following environment variables:

```
BIGQUERY_ENABLED=true
GCP_PROJECT_ID=your-project-id
BIGQUERY_DATASET_ID=novafuse_audit
BIGQUERY_TABLE_ID=events
```

### Schema

The BigQuery table schema matches the audit log structure:

```json
[
  { "name": "id", "type": "STRING" },
  { "name": "timestamp", "type": "TIMESTAMP" },
  { "name": "userId", "type": "STRING" },
  { "name": "action", "type": "STRING" },
  { "name": "resourceType", "type": "STRING" },
  { "name": "resourceId", "type": "STRING" },
  { "name": "details", "type": "JSON" },
  { "name": "ip", "type": "STRING" },
  { "name": "userAgent", "type": "STRING" },
  { "name": "status", "type": "STRING" },
  { "name": "teamId", "type": "STRING" },
  { "name": "environmentId", "type": "STRING" },
  { "name": "tenantId", "type": "STRING" }
]
```

## Programmatic Usage

### Logging Events

To log an audit event programmatically:

```javascript
const EnhancedAuditService = require('../services/EnhancedAuditService');

// Log an audit event
await EnhancedAuditService.logEvent({
  userId: 'user-123',
  action: 'CREATE',
  resourceType: 'connector',
  resourceId: 'conn-456',
  details: { /* additional details */ },
  ip: '***********',
  userAgent: 'Mozilla/5.0...',
  status: 'success',
  teamId: 'team-789',
  environmentId: 'env-012',
  tenantId: 'tenant-345'
});
```

### Tenant-Specific Logging

To log a tenant-specific audit event:

```javascript
const EnhancedAuditService = require('../services/EnhancedAuditService');

// Log a tenant-specific event
await EnhancedAuditService.logTenantEvent('tenant-345', {
  userId: 'user-123',
  action: 'CREATE',
  resourceType: 'connector',
  resourceId: 'conn-456',
  details: { /* additional details */ },
  ip: '***********',
  userAgent: 'Mozilla/5.0...',
  status: 'success',
  teamId: 'team-789',
  environmentId: 'env-012'
});
```

## Security Considerations

- Audit logs contain sensitive information and should be protected accordingly
- Access to audit logs should be restricted to authorized users
- Audit logs should be retained for a period consistent with compliance requirements
- Audit logs should be backed up regularly
- Audit log integrity should be maintained to prevent tampering
