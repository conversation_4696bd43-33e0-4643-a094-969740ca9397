"""
Demo script for the Control Mapping Manager of the Universal Compliance Tracking Optimizer (UCTO).

This script demonstrates how to use the Control Mapping Manager to map controls across
different compliance frameworks.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCTO
from ucto import ControlMappingManager

def main():
    """Run the Control Mapping Manager demo."""
    logger.info("Starting Control Mapping Manager demo")
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(os.path.dirname(__file__), 'control_mapping_output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Initialize the Control Mapping Manager
    mapping_dir = os.path.join(output_dir, 'mapping_data')
    mapping_manager = ControlMappingManager(mapping_dir)
    
    # Step 1: Create compliance frameworks
    logger.info("Step 1: Creating compliance frameworks")
    
    # Create GDPR framework
    gdpr_framework = mapping_manager.create_framework({
        'name': 'GDPR',
        'description': 'General Data Protection Regulation',
        'version': '2016/679',
        'category': 'privacy'
    })
    
    # Create SOC 2 framework
    soc2_framework = mapping_manager.create_framework({
        'name': 'SOC 2',
        'description': 'Service Organization Control 2',
        'version': '2017',
        'category': 'security'
    })
    
    # Create HIPAA framework
    hipaa_framework = mapping_manager.create_framework({
        'name': 'HIPAA',
        'description': 'Health Insurance Portability and Accountability Act',
        'version': '1996',
        'category': 'healthcare'
    })
    
    # Step 2: Create controls for each framework
    logger.info("Step 2: Creating controls for each framework")
    
    # Create GDPR controls
    gdpr_controls = []
    gdpr_controls.append(mapping_manager.create_control({
        'framework_id': gdpr_framework['id'],
        'name': 'Data Subject Rights',
        'description': 'Implement processes for handling data subject rights requests',
        'identifier': 'GDPR-DSR',
        'category': 'data_subject_rights'
    }))
    
    gdpr_controls.append(mapping_manager.create_control({
        'framework_id': gdpr_framework['id'],
        'name': 'Data Protection Impact Assessment',
        'description': 'Conduct data protection impact assessments for high-risk processing',
        'identifier': 'GDPR-DPIA',
        'category': 'risk_assessment'
    }))
    
    gdpr_controls.append(mapping_manager.create_control({
        'framework_id': gdpr_framework['id'],
        'name': 'Data Breach Notification',
        'description': 'Implement processes for notifying authorities of data breaches',
        'identifier': 'GDPR-DBN',
        'category': 'incident_response'
    }))
    
    # Create SOC 2 controls
    soc2_controls = []
    soc2_controls.append(mapping_manager.create_control({
        'framework_id': soc2_framework['id'],
        'name': 'Access Control',
        'description': 'Implement access controls to restrict access to information assets',
        'identifier': 'SOC2-AC',
        'category': 'access_control'
    }))
    
    soc2_controls.append(mapping_manager.create_control({
        'framework_id': soc2_framework['id'],
        'name': 'Risk Management',
        'description': 'Implement risk management processes to identify and mitigate risks',
        'identifier': 'SOC2-RM',
        'category': 'risk_assessment'
    }))
    
    soc2_controls.append(mapping_manager.create_control({
        'framework_id': soc2_framework['id'],
        'name': 'Incident Response',
        'description': 'Implement incident response processes to detect and respond to security incidents',
        'identifier': 'SOC2-IR',
        'category': 'incident_response'
    }))
    
    # Create HIPAA controls
    hipaa_controls = []
    hipaa_controls.append(mapping_manager.create_control({
        'framework_id': hipaa_framework['id'],
        'name': 'Access Control',
        'description': 'Implement technical policies and procedures for electronic PHI access',
        'identifier': 'HIPAA-AC',
        'category': 'access_control'
    }))
    
    hipaa_controls.append(mapping_manager.create_control({
        'framework_id': hipaa_framework['id'],
        'name': 'Risk Analysis',
        'description': 'Conduct risk analysis to identify risks to electronic PHI',
        'identifier': 'HIPAA-RA',
        'category': 'risk_assessment'
    }))
    
    hipaa_controls.append(mapping_manager.create_control({
        'framework_id': hipaa_framework['id'],
        'name': 'Breach Notification',
        'description': 'Implement procedures to notify affected individuals of breaches',
        'identifier': 'HIPAA-BN',
        'category': 'incident_response'
    }))
    
    # Step 3: Create mappings between controls
    logger.info("Step 3: Creating mappings between controls")
    
    # Map GDPR Data Protection Impact Assessment to SOC 2 Risk Management
    mapping_manager.create_mapping({
        'source_control_id': gdpr_controls[1]['id'],
        'target_control_id': soc2_controls[1]['id'],
        'strength': 'partial',
        'notes': 'GDPR DPIA is more specific than SOC 2 Risk Management'
    })
    
    # Map GDPR Data Breach Notification to SOC 2 Incident Response
    mapping_manager.create_mapping({
        'source_control_id': gdpr_controls[2]['id'],
        'target_control_id': soc2_controls[2]['id'],
        'strength': 'partial',
        'notes': 'GDPR has specific notification requirements'
    })
    
    # Map SOC 2 Access Control to HIPAA Access Control
    mapping_manager.create_mapping({
        'source_control_id': soc2_controls[0]['id'],
        'target_control_id': hipaa_controls[0]['id'],
        'strength': 'full',
        'notes': 'Both require similar access controls'
    })
    
    # Map SOC 2 Risk Management to HIPAA Risk Analysis
    mapping_manager.create_mapping({
        'source_control_id': soc2_controls[1]['id'],
        'target_control_id': hipaa_controls[1]['id'],
        'strength': 'partial',
        'notes': 'HIPAA is more specific to healthcare data'
    })
    
    # Map SOC 2 Incident Response to HIPAA Breach Notification
    mapping_manager.create_mapping({
        'source_control_id': soc2_controls[2]['id'],
        'target_control_id': hipaa_controls[2]['id'],
        'strength': 'partial',
        'notes': 'HIPAA has specific notification requirements'
    })
    
    # Map GDPR Data Breach Notification to HIPAA Breach Notification
    mapping_manager.create_mapping({
        'source_control_id': gdpr_controls[2]['id'],
        'target_control_id': hipaa_controls[2]['id'],
        'strength': 'partial',
        'notes': 'Both require breach notification but with different requirements'
    })
    
    # Step 4: Get mapped controls
    logger.info("Step 4: Getting mapped controls")
    
    # Get controls mapped to GDPR Data Breach Notification
    gdpr_dbn_mapped_controls = mapping_manager.get_mapped_controls(gdpr_controls[2]['id'])
    
    # Save mapped controls to file
    with open(os.path.join(output_dir, 'gdpr_dbn_mapped_controls.json'), 'w', encoding='utf-8') as f:
        json.dump(gdpr_dbn_mapped_controls, f, indent=2)
    
    logger.info(f"Saved mapped controls for GDPR Data Breach Notification to {os.path.join(output_dir, 'gdpr_dbn_mapped_controls.json')}")
    
    # Step 5: Calculate framework coverage
    logger.info("Step 5: Calculating framework coverage")
    
    # Calculate coverage between GDPR and SOC 2
    gdpr_soc2_coverage = mapping_manager.calculate_framework_coverage(gdpr_framework['id'], soc2_framework['id'])
    
    # Save coverage to file
    with open(os.path.join(output_dir, 'gdpr_soc2_coverage.json'), 'w', encoding='utf-8') as f:
        json.dump(gdpr_soc2_coverage, f, indent=2)
    
    logger.info(f"Saved coverage between GDPR and SOC 2 to {os.path.join(output_dir, 'gdpr_soc2_coverage.json')}")
    
    # Calculate coverage between SOC 2 and HIPAA
    soc2_hipaa_coverage = mapping_manager.calculate_framework_coverage(soc2_framework['id'], hipaa_framework['id'])
    
    # Save coverage to file
    with open(os.path.join(output_dir, 'soc2_hipaa_coverage.json'), 'w', encoding='utf-8') as f:
        json.dump(soc2_hipaa_coverage, f, indent=2)
    
    logger.info(f"Saved coverage between SOC 2 and HIPAA to {os.path.join(output_dir, 'soc2_hipaa_coverage.json')}")
    
    # Step 6: Generate a summary report
    logger.info("Step 6: Generating a summary report")
    
    # Create a summary report
    summary_report = {
        'frameworks': {
            'gdpr': {
                'id': gdpr_framework['id'],
                'name': gdpr_framework['name'],
                'controls': len(gdpr_controls)
            },
            'soc2': {
                'id': soc2_framework['id'],
                'name': soc2_framework['name'],
                'controls': len(soc2_controls)
            },
            'hipaa': {
                'id': hipaa_framework['id'],
                'name': hipaa_framework['name'],
                'controls': len(hipaa_controls)
            }
        },
        'mappings': {
            'total': len(mapping_manager.mappings),
            'gdpr_to_soc2': len(mapping_manager.get_framework_mappings(gdpr_framework['id'], soc2_framework['id'])),
            'soc2_to_hipaa': len(mapping_manager.get_framework_mappings(soc2_framework['id'], hipaa_framework['id'])),
            'gdpr_to_hipaa': len(mapping_manager.get_framework_mappings(gdpr_framework['id'], hipaa_framework['id']))
        },
        'coverage': {
            'gdpr_to_soc2': gdpr_soc2_coverage,
            'soc2_to_hipaa': soc2_hipaa_coverage
        }
    }
    
    # Save summary report to file
    with open(os.path.join(output_dir, 'summary_report.json'), 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2)
    
    logger.info(f"Saved summary report to {os.path.join(output_dir, 'summary_report.json')}")
    
    logger.info("Control Mapping Manager demo completed successfully")
    logger.info(f"All output files are in: {output_dir}")

if __name__ == '__main__':
    main()
