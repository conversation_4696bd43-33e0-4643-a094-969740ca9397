import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Box, CircularProgress, Typography } from '@mui/material';

/**
 * CyberSafetyResonanceSpectrogram component
 * 
 * Enhanced version of the ResonanceSpectrogram specifically designed to show
 * Cyber-Safety fusion between GRC, IT, and Cybersecurity domains.
 * 
 * Features:
 * - 3D resonance patterns showing domain interactions
 * - Frequency analysis of cross-domain data flows
 * - Predictive modeling of potential dissonance issues
 * - Real-time monitoring of system harmony
 */
function CyberSafetyResonanceSpectrogram({
  domainData = {
    grc: { values: [], frequency: 0.3, amplitude: 0.7, phase: 0 },
    it: { values: [], frequency: 0.6, amplitude: 0.8, phase: Math.PI / 3 },
    cybersecurity: { values: [], frequency: 0.9, amplitude: 0.6, phase: Math.PI / 2 },
    crossDomainFlows: [] // Array of { source: 'domain1', target: 'domain2', strength: 0.5, frequency: 0.4 }
  },
  predictionData = {
    timeHorizon: 10, // Number of future time steps to predict
    dissonanceProbability: 0.2, // Probability of future dissonance (0-1)
    criticalPoints: [] // Array of { timeStep: 5, severity: 0.7, description: 'Potential dissonance between GRC and IT' }
  },
  options = {
    renderMode: 'medium',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    colorScheme: 'default',
    showPredictions: true,
    showCrossDomainFlows: true,
    showLabels: true,
    highlightDissonance: true
  },
  width = '100%',
  height = '100%'
}) {
  // Refs for Three.js objects
  const containerRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const rendererRef = useRef(null);
  const controlsRef = useRef(null);
  const animationFrameRef = useRef(null);
  const spectrogramGroupRef = useRef(null);
  const flowsGroupRef = useRef(null);
  const predictionsGroupRef = useRef(null);
  const labelsGroupRef = useRef(null);

  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [time, setTime] = useState(0);

  // Domain colors
  const domainColors = {
    grc: new THREE.Color(0x3366cc),      // Blue
    it: new THREE.Color(0x33cc33),       // Green
    cybersecurity: new THREE.Color(0xcc3333)  // Red
  };

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x111122);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(75, containerRef.current.clientWidth / containerRef.current.clientHeight, 0.1, 1000);
      camera.position.set(5, 5, 5);
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({ antialias: options.renderMode !== 'low' });
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
      renderer.setPixelRatio(window.devicePixelRatio);
      containerRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Create controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;
      controlsRef.current = controls;

      // Create groups for organization
      const spectrogramGroup = new THREE.Group();
      spectrogramGroup.name = 'spectrogram';
      scene.add(spectrogramGroup);
      spectrogramGroupRef.current = spectrogramGroup;

      const flowsGroup = new THREE.Group();
      flowsGroup.name = 'flows';
      scene.add(flowsGroup);
      flowsGroupRef.current = flowsGroup;

      const predictionsGroup = new THREE.Group();
      predictionsGroup.name = 'predictions';
      scene.add(predictionsGroup);
      predictionsGroupRef.current = predictionsGroup;

      const labelsGroup = new THREE.Group();
      labelsGroup.name = 'labels';
      scene.add(labelsGroup);
      labelsGroupRef.current = labelsGroup;

      // Add axes and grid if enabled
      if (options.showAxes) {
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
      }

      if (options.showGrid) {
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);
      }

      // Add lights
      const ambientLight = new THREE.AmbientLight(0x404040);
      scene.add(ambientLight);
      
      const directionalLight1 = new THREE.DirectionalLight(0xffffff, 0.5);
      directionalLight1.position.set(1, 1, 1);
      scene.add(directionalLight1);
      
      const directionalLight2 = new THREE.DirectionalLight(0xffffff, 0.3);
      directionalLight2.position.set(-1, -1, -1);
      scene.add(directionalLight2);

      // Set loading to false
      setIsLoading(false);

      // Handle window resize
      const handleResize = () => {
        if (containerRef.current && cameraRef.current && rendererRef.current) {
          const width = containerRef.current.clientWidth;
          const height = containerRef.current.clientHeight;
          
          cameraRef.current.aspect = width / height;
          cameraRef.current.updateProjectionMatrix();
          
          rendererRef.current.setSize(width, height);
        }
      };

      window.addEventListener('resize', handleResize);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (rendererRef.current && containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
      };
    } catch (err) {
      console.error('Error initializing 3D visualization:', err);
      setError(err.message || 'Error initializing visualization');
      setIsLoading(false);
    }
  }, [options.renderMode, options.showAxes, options.showGrid]);

  // Update visualization when domain data changes
  useEffect(() => {
    if (isLoading || !sceneRef.current || !spectrogramGroupRef.current) return;

    try {
      // Clear previous visualization
      while (spectrogramGroupRef.current.children.length > 0) {
        spectrogramGroupRef.current.remove(spectrogramGroupRef.current.children[0]);
      }
      
      while (flowsGroupRef.current.children.length > 0) {
        flowsGroupRef.current.remove(flowsGroupRef.current.children[0]);
      }
      
      while (predictionsGroupRef.current.children.length > 0) {
        predictionsGroupRef.current.remove(predictionsGroupRef.current.children[0]);
      }
      
      while (labelsGroupRef.current.children.length > 0) {
        labelsGroupRef.current.remove(labelsGroupRef.current.children[0]);
      }

      // Domain positions in a circular arrangement
      const domainPositions = {
        grc: new THREE.Vector3(-3, 0, 0),
        it: new THREE.Vector3(0, 0, 3),
        cybersecurity: new THREE.Vector3(3, 0, 0)
      };

      // Create domain spectrograms
      Object.entries(domainData).forEach(([domain, data]) => {
        if (domain === 'crossDomainFlows') return; // Skip flows array
        
        if (!domainPositions[domain]) return; // Skip unknown domains
        
        // Create domain wave
        const waveGeometry = new THREE.BufferGeometry();
        const wavePoints = [];
        const segments = 100;
        const radius = 1;
        const frequency = data.frequency || 0.5;
        const amplitude = data.amplitude || 0.5;
        const phase = data.phase || 0;
        
        for (let i = 0; i <= segments; i++) {
          const theta = (i / segments) * Math.PI * 2;
          const y = amplitude * Math.sin(frequency * 10 * theta + phase);
          const x = radius * Math.cos(theta);
          const z = radius * Math.sin(theta);
          
          wavePoints.push(new THREE.Vector3(x, y, z));
        }
        
        waveGeometry.setFromPoints(wavePoints);
        
        const waveMaterial = new THREE.LineBasicMaterial({
          color: domainColors[domain] || 0xffffff,
          linewidth: 2
        });
        
        const wave = new THREE.Line(waveGeometry, waveMaterial);
        wave.position.copy(domainPositions[domain]);
        wave.userData = { domain, frequency, amplitude, phase };
        
        spectrogramGroupRef.current.add(wave);
        
        // Create domain surface
        const surfaceGeometry = new THREE.ParametricBufferGeometry((u, v, target) => {
          const theta = u * Math.PI * 2;
          const phi = v * Math.PI;
          
          const r = radius + amplitude * 0.5 * Math.sin(frequency * 10 * theta + phase);
          
          const x = r * Math.sin(phi) * Math.cos(theta);
          const y = r * Math.cos(phi);
          const z = r * Math.sin(phi) * Math.sin(theta);
          
          target.set(x, y, z);
        }, 32, 16);
        
        const surfaceMaterial = new THREE.MeshPhongMaterial({
          color: domainColors[domain] || 0xffffff,
          transparent: true,
          opacity: 0.2,
          side: THREE.DoubleSide,
          flatShading: options.renderMode === 'low'
        });
        
        const surface = new THREE.Mesh(surfaceGeometry, surfaceMaterial);
        surface.position.copy(domainPositions[domain]);
        surface.userData = { domain, frequency, amplitude, phase };
        
        spectrogramGroupRef.current.add(surface);
        
        // Add domain label if enabled
        if (options.showLabels) {
          const domainLabel = document.createElement('div');
          domainLabel.className = 'domain-label';
          domainLabel.textContent = domain.toUpperCase();
          domainLabel.style.color = '#ffffff';
          
          const label = new CSS2DObject(domainLabel);
          label.position.copy(domainPositions[domain]);
          label.position.y += 1.5;
          
          labelsGroupRef.current.add(label);
        }
      });

      // Create cross-domain flows
      if (options.showCrossDomainFlows && domainData.crossDomainFlows && domainData.crossDomainFlows.length > 0) {
        domainData.crossDomainFlows.forEach(flow => {
          if (!domainPositions[flow.source] || !domainPositions[flow.target]) return;
          
          const sourcePos = domainPositions[flow.source];
          const targetPos = domainPositions[flow.target];
          
          // Create flow curve
          const curvePoints = [];
          const segments = 50;
          
          for (let i = 0; i <= segments; i++) {
            const t = i / segments;
            
            // Interpolate between source and target
            const x = sourcePos.x + (targetPos.x - sourcePos.x) * t;
            const z = sourcePos.z + (targetPos.z - sourcePos.z) * t;
            
            // Add sine wave based on flow frequency
            const frequency = flow.frequency || 1;
            const amplitude = flow.strength || 0.5;
            const y = amplitude * Math.sin(frequency * 10 * t * Math.PI * 2);
            
            curvePoints.push(new THREE.Vector3(x, y, z));
          }
          
          const curveGeometry = new THREE.BufferGeometry().setFromPoints(curvePoints);
          
          // Blend colors of source and target domains
          const sourceColor = domainColors[flow.source] || new THREE.Color(0xffffff);
          const targetColor = domainColors[flow.target] || new THREE.Color(0xffffff);
          const blendedColor = new THREE.Color().lerpColors(sourceColor, targetColor, 0.5);
          
          const curveMaterial = new THREE.LineBasicMaterial({
            color: blendedColor,
            linewidth: 2,
            transparent: true,
            opacity: flow.strength
          });
          
          const curve = new THREE.Line(curveGeometry, curveMaterial);
          curve.userData = { 
            source: flow.source, 
            target: flow.target, 
            strength: flow.strength,
            frequency: flow.frequency
          };
          
          flowsGroupRef.current.add(curve);
        });
      }

      // Create predictions if enabled
      if (options.showPredictions && predictionData && predictionData.criticalPoints) {
        // Create prediction path
        const pathPoints = [];
        const segments = 50;
        const timeHorizon = predictionData.timeHorizon || 10;
        
        for (let i = 0; i <= segments; i++) {
          const t = i / segments;
          const x = t * 5 - 2.5; // -2.5 to 2.5
          
          // Calculate y based on dissonance probability
          const dissonanceProbability = predictionData.dissonanceProbability || 0.2;
          const y = dissonanceProbability * Math.sin(t * Math.PI * 4);
          
          // Calculate z based on time
          const z = t * 5 - 2.5; // -2.5 to 2.5
          
          pathPoints.push(new THREE.Vector3(x, y, z));
        }
        
        const pathGeometry = new THREE.BufferGeometry().setFromPoints(pathPoints);
        const pathMaterial = new THREE.LineBasicMaterial({
          color: 0xffff00,
          linewidth: 2,
          transparent: true,
          opacity: 0.7
        });
        
        const path = new THREE.Line(pathGeometry, pathMaterial);
        predictionsGroupRef.current.add(path);
        
        // Add critical points
        predictionData.criticalPoints.forEach(point => {
          const criticalGeometry = new THREE.SphereGeometry(0.1, 16, 16);
          const criticalMaterial = new THREE.MeshPhongMaterial({
            color: 0xff0000,
            emissive: 0xff0000,
            emissiveIntensity: 0.5
          });
          
          const criticalPoint = new THREE.Mesh(criticalGeometry, criticalMaterial);
          
          // Position based on time step and severity
          const t = point.timeStep / timeHorizon;
          const x = t * 5 - 2.5;
          const y = point.severity;
          const z = t * 5 - 2.5;
          
          criticalPoint.position.set(x, y, z);
          criticalPoint.userData = { 
            timeStep: point.timeStep,
            severity: point.severity,
            description: point.description
          };
          
          predictionsGroupRef.current.add(criticalPoint);
          
          // Add label if enabled
          if (options.showLabels) {
            const pointLabel = document.createElement('div');
            pointLabel.className = 'critical-point-label';
            pointLabel.textContent = `Critical: T+${point.timeStep}`;
            pointLabel.style.color = '#ff0000';
            
            const label = new CSS2DObject(pointLabel);
            label.position.copy(criticalPoint.position);
            label.position.y += 0.2;
            
            labelsGroupRef.current.add(label);
          }
        });
      }
    } catch (err) {
      console.error('Error updating cyber-safety resonance spectrogram:', err);
      setError(err.message || 'Error updating visualization');
    }
  }, [domainData, predictionData, options, isLoading]);

  // Animation loop
  useEffect(() => {
    if (isLoading) return;

    // Animation loop with rotation and wave animation
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);
      
      // Update time
      setTime(prevTime => prevTime + 0.01);
      
      // Animate domain waves
      if (spectrogramGroupRef.current) {
        spectrogramGroupRef.current.children.forEach(child => {
          if (child instanceof THREE.Line && child.userData.domain) {
            const domain = child.userData.domain;
            const frequency = child.userData.frequency || 0.5;
            const amplitude = child.userData.amplitude || 0.5;
            const phase = child.userData.phase || 0;
            
            // Update wave points
            const geometry = child.geometry;
            const positions = geometry.attributes.position.array;
            const segments = positions.length / 3 - 1;
            const radius = 1;
            
            for (let i = 0; i <= segments; i++) {
              const theta = (i / segments) * Math.PI * 2;
              const y = amplitude * Math.sin(frequency * 10 * theta + phase + time);
              const x = radius * Math.cos(theta);
              const z = radius * Math.sin(theta);
              
              positions[i * 3] = x;
              positions[i * 3 + 1] = y;
              positions[i * 3 + 2] = z;
            }
            
            geometry.attributes.position.needsUpdate = true;
          }
        });
      }
      
      // Animate cross-domain flows
      if (flowsGroupRef.current) {
        flowsGroupRef.current.children.forEach(child => {
          if (child instanceof THREE.Line) {
            const geometry = child.geometry;
            const positions = geometry.attributes.position.array;
            const segments = positions.length / 3 - 1;
            
            const sourcePos = new THREE.Vector3(
              positions[0],
              positions[1],
              positions[2]
            );
            
            const targetPos = new THREE.Vector3(
              positions[segments * 3],
              positions[segments * 3 + 1],
              positions[segments * 3 + 2]
            );
            
            const frequency = child.userData.frequency || 1;
            const strength = child.userData.strength || 0.5;
            
            for (let i = 0; i <= segments; i++) {
              const t = i / segments;
              
              // Interpolate between source and target
              const x = sourcePos.x + (targetPos.x - sourcePos.x) * t;
              const z = sourcePos.z + (targetPos.z - sourcePos.z) * t;
              
              // Add sine wave based on flow frequency
              const y = strength * Math.sin(frequency * 10 * t * Math.PI * 2 + time);
              
              positions[i * 3] = x;
              positions[i * 3 + 1] = y;
              positions[i * 3 + 2] = z;
            }
            
            geometry.attributes.position.needsUpdate = true;
          }
        });
      }
      
      // Rotate spectrogram based on rotation speed
      if (spectrogramGroupRef.current && options.rotationSpeed > 0) {
        spectrogramGroupRef.current.rotation.y += 0.005 * options.rotationSpeed;
      }
      
      // Update controls
      if (controlsRef.current) {
        controlsRef.current.update();
      }
      
      // Render scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    // Start animation loop
    animate();

    // Clean up
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [options.rotationSpeed, isLoading, time]);

  // CSS2D Renderer for labels
  class CSS2DObject extends THREE.Object3D {
    constructor(element) {
      super();
      this.element = element;
      this.element.style.position = 'absolute';
      this.element.style.fontSize = '14px';
      this.element.style.fontWeight = 'bold';
      this.element.style.textAlign = 'center';
      this.element.style.pointerEvents = 'none';
      
      const vector = new THREE.Vector3();
      const setPosition = (x, y, z) => {
        vector.set(x, y, z);
        vector.project(cameraRef.current);
        
        const widthHalf = containerRef.current.clientWidth / 2;
        const heightHalf = containerRef.current.clientHeight / 2;
        
        this.element.style.left = (vector.x * widthHalf + widthHalf) + 'px';
        this.element.style.top = (- vector.y * heightHalf + heightHalf) + 'px';
      };
      
      this.setPosition = setPosition;
    }
  }

  // Update label positions in animation loop
  useEffect(() => {
    if (isLoading || !labelsGroupRef.current || !options.showLabels) return;
    
    const updateLabels = () => {
      if (labelsGroupRef.current && labelsGroupRef.current.children) {
        labelsGroupRef.current.children.forEach(label => {
          if (label instanceof CSS2DObject) {
            label.setPosition(label.position.x, label.position.y, label.position.z);
          }
        });
      }
      
      requestAnimationFrame(updateLabels);
    };
    
    updateLabels();
  }, [isLoading, options.showLabels]);

  return (
    <Box
      ref={containerRef}
      sx={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)'
          }}
        >
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            color: 'error.main',
            p: 2
          }}
        >
          <Typography variant="body1" color="error">
            Error: {error}
          </Typography>
        </Box>
      )}
    </Box>
  );
}

export default CyberSafetyResonanceSpectrogram;
