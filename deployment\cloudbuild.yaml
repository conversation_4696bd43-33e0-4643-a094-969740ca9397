# NovaConnect CI/CD Pipeline for Google Cloud Build
# This pipeline builds, tests, and deploys NovaConnect to GKE

timeout: 3600s # 1 hour
options:
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100
  env:
    - 'DOCKER_BUILDKIT=1'

substitutions:
  _GKE_CLUSTER: 'novaconnect-cluster'
  _GKE_ZONE: 'us-central1-a'
  _IMAGE_NAME: 'novaconnect-api'
  _NAMESPACE: 'novaconnect'
  _SONAR_PROJECT_KEY: 'novafuse-novaconnect'

steps:
  # Install dependencies
  - name: 'node:18'
    id: 'install-dependencies'
    entrypoint: 'npm'
    args: ['ci']
    dir: 'nova-connect'

  # Run linting
  - name: 'node:18'
    id: 'lint'
    entrypoint: 'npm'
    args: ['run', 'lint']
    dir: 'nova-connect'
    waitFor: ['install-dependencies']

  # Run unit tests
  - name: 'node:18'
    id: 'unit-tests'
    entrypoint: 'npm'
    args: ['run', 'test:unit']
    dir: 'nova-connect'
    waitFor: ['install-dependencies']

  # Run integration tests
  - name: 'node:18'
    id: 'integration-tests'
    entrypoint: 'npm'
    args: ['run', 'test:integration']
    dir: 'nova-connect'
    waitFor: ['install-dependencies']

  # Run performance tests
  - name: 'node:18'
    id: 'performance-tests'
    entrypoint: 'npm'
    args: ['run', 'test:performance']
    dir: 'nova-connect'
    waitFor: ['install-dependencies']

  # Generate test coverage report
  - name: 'node:18'
    id: 'test-coverage'
    entrypoint: 'npm'
    args: ['run', 'test:coverage']
    dir: 'nova-connect'
    waitFor: ['unit-tests', 'integration-tests']

  # Run security scan
  - name: 'gcr.io/cloud-builders/npm'
    id: 'security-scan'
    args: ['run', 'security:scan']
    dir: 'nova-connect'
    waitFor: ['install-dependencies']

  # SonarQube analysis
  - name: 'gcr.io/$PROJECT_ID/sonar-scanner:latest'
    id: 'sonarqube-analysis'
    args:
      - '-Dsonar.projectKey=${_SONAR_PROJECT_KEY}'
      - '-Dsonar.sources=nova-connect/src'
      - '-Dsonar.tests=nova-connect/tests'
      - '-Dsonar.javascript.lcov.reportPaths=nova-connect/coverage/lcov.info'
      - '-Dsonar.testExecutionReportPaths=nova-connect/test-results/sonar-report.xml'
    waitFor: ['test-coverage']

  # Build Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-image'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:latest',
      '-f', 'nova-connect/Dockerfile',
      '--build-arg', 'BUILDKIT_INLINE_CACHE=1',
      '--cache-from', 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:latest',
      '.'
    ]
    waitFor: ['lint', 'unit-tests', 'integration-tests', 'security-scan']

  # Run container structure tests
  - name: 'gcr.io/gcp-runtimes/container-structure-test'
    id: 'container-structure-test'
    args: [
      'test',
      '--image', 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$COMMIT_SHA',
      '--config', 'nova-connect/container-structure-test.yaml'
    ]
    waitFor: ['build-image']

  # Vulnerability scanning
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'vulnerability-scan'
    args: [
      'container', 'images', 'describe',
      'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$COMMIT_SHA',
      '--format=json'
    ]
    waitFor: ['build-image']

  # Push Docker image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-image'
    args: ['push', 'gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$COMMIT_SHA']
    waitFor: ['container-structure-test', 'vulnerability-scan']

  # Update deployment manifest
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'update-deployment-manifest'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        sed -i "s|gcr.io/novafuse/novaconnect-api:latest|gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$COMMIT_SHA|g" deployment/kubernetes/novaconnect-deployment.yaml
    waitFor: ['push-image']

  # Deploy to GKE
  - name: 'gcr.io/cloud-builders/kubectl'
    id: 'deploy-to-gke'
    args: [
      'apply',
      '-f', 'deployment/kubernetes/novaconnect-deployment.yaml'
    ]
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=${_GKE_ZONE}'
      - 'CLOUDSDK_CONTAINER_CLUSTER=${_GKE_CLUSTER}'
    waitFor: ['update-deployment-manifest']

  # Wait for deployment to complete
  - name: 'gcr.io/cloud-builders/kubectl'
    id: 'wait-for-deployment'
    args: [
      'rollout', 'status', 'deployment/novaconnect-api',
      '-n', '${_NAMESPACE}',
      '--timeout=5m'
    ]
    env:
      - 'CLOUDSDK_COMPUTE_ZONE=${_GKE_ZONE}'
      - 'CLOUDSDK_CONTAINER_CLUSTER=${_GKE_CLUSTER}'
    waitFor: ['deploy-to-gke']

  # Run post-deployment tests
  - name: 'gcr.io/cloud-builders/curl'
    id: 'post-deployment-test'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Wait for ingress to be ready
        sleep 60
        # Test the API
        curl -s -o /dev/null -w "%{http_code}" https://api.novaconnect.novafuse.com/health | grep 200
    waitFor: ['wait-for-deployment']

  # Tag successful release
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'tag-release'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Only tag on main branch
        if [ "$BRANCH_NAME" = "main" ]; then
          # Get the latest version tag
          LATEST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
          # Increment the patch version
          MAJOR=$(echo $LATEST_TAG | cut -d. -f1 | sed 's/v//')
          MINOR=$(echo $LATEST_TAG | cut -d. -f2)
          PATCH=$(echo $LATEST_TAG | cut -d. -f3)
          NEW_PATCH=$((PATCH + 1))
          NEW_TAG="v$MAJOR.$MINOR.$NEW_PATCH"
          # Tag the image with the new version
          gcloud container images add-tag \
            gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$COMMIT_SHA \
            gcr.io/$PROJECT_ID/${_IMAGE_NAME}:$NEW_TAG \
            --quiet
          echo "Tagged release: $NEW_TAG"
        else
          echo "Skipping release tagging for non-main branch"
        fi
    waitFor: ['post-deployment-test']

artifacts:
  objects:
    location: 'gs://${PROJECT_ID}-artifacts/novaconnect/$COMMIT_SHA/'
    paths:
      - 'nova-connect/coverage/**'
      - 'nova-connect/test-results/**'
      - 'deployment/kubernetes/novaconnect-deployment.yaml'

# Notifications
logsBucket: 'gs://${PROJECT_ID}-cloudbuild-logs'
options:
  logging: GCS_ONLY
