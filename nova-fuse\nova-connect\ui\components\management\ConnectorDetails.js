/**
 * Connector Details Component
 * 
 * This component displays detailed information about a connector.
 */

import React, { useState } from 'react';
import { 
  Box, 
  Button, 
  Card, 
  CardContent, 
  Chip, 
  Divider, 
  Grid, 
  IconButton, 
  Paper, 
  Tab, 
  Tabs, 
  Typography 
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import HistoryIcon from '@mui/icons-material/History';
import CodeIcon from '@mui/icons-material/Code';
import SettingsIcon from '@mui/icons-material/Settings';
import DescriptionIcon from '@mui/icons-material/Description';
import { useRouter } from 'next/router';
import EndpointList from './EndpointList';
import VersionHistory from './VersionHistory';
import ConnectorConfiguration from './ConnectorConfiguration';

const ConnectorDetails = ({ connector, onDelete, onDuplicate }) => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  const handleEdit = () => {
    router.push(`/connectors/edit/${connector.id}`);
  };
  
  const handleTest = () => {
    router.push(`/testing?connector=${connector.id}`);
  };
  
  const handleDelete = () => {
    if (onDelete) {
      onDelete(connector.id);
    }
  };
  
  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate(connector.id);
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'draft':
        return 'warning';
      case 'deprecated':
        return 'error';
      default:
        return 'default';
    }
  };
  
  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            {connector.name}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Chip 
              label={connector.category} 
              size="small" 
              sx={{ mr: 1 }} 
            />
            
            <Chip 
              label={`v${connector.version}`} 
              size="small" 
              color="primary" 
              variant="outlined" 
              sx={{ mr: 1 }} 
            />
            
            <Chip 
              label={connector.status} 
              size="small" 
              color={getStatusColor(connector.status)} 
            />
          </Box>
          
          <Typography variant="body1" color="textSecondary">
            {connector.description}
          </Typography>
        </Box>
        
        <Box>
          <Button
            variant="contained"
            startIcon={<PlayArrowIcon />}
            onClick={handleTest}
            sx={{ mr: 1 }}
          >
            Test
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={handleEdit}
            sx={{ mr: 1 }}
          >
            Edit
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<ContentCopyIcon />}
            onClick={handleDuplicate}
            sx={{ mr: 1 }}
          >
            Duplicate
          </Button>
          
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDelete}
          >
            Delete
          </Button>
        </Box>
      </Box>
      
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} aria-label="connector tabs">
          <Tab label="Overview" value="overview" icon={<DescriptionIcon />} iconPosition="start" />
          <Tab label="Endpoints" value="endpoints" icon={<CodeIcon />} iconPosition="start" />
          <Tab label="Configuration" value="configuration" icon={<SettingsIcon />} iconPosition="start" />
          <Tab label="Version History" value="history" icon={<HistoryIcon />} iconPosition="start" />
        </Tabs>
      </Box>
      
      {activeTab === 'overview' && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Details
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Name
                    </Typography>
                    <Typography variant="body1">
                      {connector.name}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Category
                    </Typography>
                    <Typography variant="body1">
                      {connector.category}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Version
                    </Typography>
                    <Typography variant="body1">
                      {connector.version}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Status
                    </Typography>
                    <Typography variant="body1">
                      <Chip 
                        label={connector.status} 
                        size="small" 
                        color={getStatusColor(connector.status)} 
                      />
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Created
                    </Typography>
                    <Typography variant="body1">
                      {new Date(connector.created).toLocaleString()}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Last Updated
                    </Typography>
                    <Typography variant="body1">
                      {new Date(connector.updated).toLocaleString()}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Description
                    </Typography>
                    <Typography variant="body1">
                      {connector.description}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Base Configuration
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Base URL
                    </Typography>
                    <Typography variant="body1">
                      {connector.baseUrl}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Authentication Type
                    </Typography>
                    <Typography variant="body1">
                      {connector.authentication?.type || 'None'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Timeout
                    </Typography>
                    <Typography variant="body1">
                      {connector.configuration?.timeout || 30000}ms
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Retry Count
                    </Typography>
                    <Typography variant="body1">
                      {connector.configuration?.retryPolicy?.maxRetries || 3}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Retry Strategy
                    </Typography>
                    <Typography variant="body1">
                      {connector.configuration?.retryPolicy?.backoffStrategy || 'exponential'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Statistics
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Endpoints
                    </Typography>
                    <Typography variant="h4">
                      {connector.endpoints?.length || 0}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Versions
                    </Typography>
                    <Typography variant="h4">
                      {connector.versions?.length || 1}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Test Success Rate
                    </Typography>
                    <Typography variant="h4">
                      {connector.testSuccessRate || '95%'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Usage
                    </Typography>
                    <Typography variant="h4">
                      {connector.usage || '0'}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
            
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Tags
                </Typography>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {connector.tags?.map(tag => (
                    <Chip key={tag} label={tag} size="small" />
                  )) || (
                    <Typography variant="body2" color="textSecondary">
                      No tags
                    </Typography>
                  )}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}
      
      {activeTab === 'endpoints' && (
        <EndpointList endpoints={connector.endpoints || []} />
      )}
      
      {activeTab === 'configuration' && (
        <ConnectorConfiguration connector={connector} />
      )}
      
      {activeTab === 'history' && (
        <VersionHistory versions={connector.versions || []} />
      )}
    </Box>
  );
};

export default ConnectorDetails;
