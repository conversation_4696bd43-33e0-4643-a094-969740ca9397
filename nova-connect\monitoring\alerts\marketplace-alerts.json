{"displayName": "NovaConnect UAC Marketplace Alerts", "documentation": {"content": "# NovaConnect UAC Marketplace Alerts\n\nThis document describes the alerts configured for NovaConnect UAC in Google Cloud Marketplace deployments.\n\n## Alert Categories\n\n1. **Availability Alerts**: Alerts related to service availability\n2. **Performance Alerts**: Alerts related to service performance\n3. **Resource Alerts**: Alerts related to resource utilization\n4. **Error Alerts**: Alerts related to errors and exceptions\n5. **Security Alerts**: Alerts related to security events\n\n## Alert Severity Levels\n\n- **Critical**: Immediate action required\n- **Warning**: Action required soon\n- **Information**: No immediate action required\n\n## Alert Notification Channels\n\n- **Email**: Notifications sent to the configured email addresses\n- **Slack**: Notifications sent to the configured Slack channels\n- **PagerDuty**: Notifications sent to the configured PagerDuty service\n- **Webhook**: Notifications sent to the configured webhook endpoints\n\n## Alert Escalation\n\n1. **First Level**: Notification sent to the primary on-call engineer\n2. **Second Level**: Notification sent to the secondary on-call engineer\n3. **Third Level**: Notification sent to the engineering manager\n\n## Alert Suppression\n\nAlerts can be suppressed during maintenance windows by creating a suppression rule in Google Cloud Monitoring.", "mimeType": "text/markdown"}, "alertStrategy": {"autoClose": "604800s", "notificationChannels": ["projects/${PROJECT_ID}/notificationChannels/${EMAIL_CHANNEL_ID}", "projects/${PROJECT_ID}/notificationChannels/${SLACK_CHANNEL_ID}"], "notificationRateLimit": {"period": "300s"}}, "conditions": [{"displayName": "High Error Rate", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/api_errors_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}], "comparison": "COMPARISON_GT", "thresholdValue": 0.05, "duration": "60s", "trigger": {"count": 1}}}, {"displayName": "High Response Time", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/http_request_duration_seconds\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_PERCENTILE_95", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_GT", "thresholdValue": 1.0, "duration": "300s", "trigger": {"count": 1}}}, {"displayName": "High CPU Usage", "conditionThreshold": {"filter": "metric.type=\"kubernetes.io/container/cpu/utilization\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_GT", "thresholdValue": 0.8, "duration": "300s", "trigger": {"count": 1}}}, {"displayName": "High Memory Usage", "conditionThreshold": {"filter": "metric.type=\"kubernetes.io/container/memory/used_bytes\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_GT", "thresholdValue": 0.8, "duration": "300s", "trigger": {"count": 1}}}, {"displayName": "Service Unavailable", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/health_check\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_LT", "thresholdValue": 1.0, "duration": "60s", "trigger": {"count": 1}}}, {"displayName": "Database Connection Failure", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/database_connection_status\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_LT", "thresholdValue": 1.0, "duration": "60s", "trigger": {"count": 1}}}, {"displayName": "Redis Connection Failure", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/redis_connection_status\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_LT", "thresholdValue": 1.0, "duration": "60s", "trigger": {"count": 1}}}, {"displayName": "High Rate Limiting", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/rate_limit_exceeded_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}], "comparison": "COMPARISON_GT", "thresholdValue": 10.0, "duration": "300s", "trigger": {"count": 1}}}, {"displayName": "Low Cache <PERSON>", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/cache_hit_ratio\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "300s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}], "comparison": "COMPARISON_LT", "thresholdValue": 0.5, "duration": "900s", "trigger": {"count": 1}}}, {"displayName": "High Authentication Failures", "conditionThreshold": {"filter": "metric.type=\"custom.googleapis.com/novafuse/authentication_failures_total\" resource.type=\"k8s_container\" resource.label.\"container_name\"=\"novafuse-uac\"", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}], "comparison": "COMPARISON_GT", "thresholdValue": 5.0, "duration": "300s", "trigger": {"count": 1}}}], "combiner": "OR", "enabled": true, "notificationChannels": ["projects/${PROJECT_ID}/notificationChannels/${EMAIL_CHANNEL_ID}", "projects/${PROJECT_ID}/notificationChannels/${SLACK_CHANNEL_ID}"], "severity": "WARNING"}