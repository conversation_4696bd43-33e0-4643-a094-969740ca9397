{"id": "workflow-019de50e-524e-4561-ab26-73c6282e3def", "name": "Automated GDPR Compliance Workflow", "description": "Automatically create GDPR compliance requirements and collect evidence", "version": "1.0.0", "enabled": true, "trigger": {"type": "event", "parameters": {"eventType": "compliance_framework_added", "conditions": {"framework": "GDPR"}}}, "actions": [{"id": "create_data_subject_rights_requirement", "type": "create_requirement", "name": "Create Data Subject Rights Requirement", "description": "Create a requirement for implementing data subject rights", "parameters": {"name": "Data Subject Rights", "description": "Implement processes for handling data subject rights requests", "framework": "GDPR", "category": "privacy", "priority": "high", "status": "pending", "due_date": "2025-08-11T04:35:00.880985", "assigned_to": "privacy_officer", "tags": ["gdpr", "data_subject_rights", "privacy"]}}, {"id": "create_dpia_requirement", "type": "create_requirement", "name": "Create DPIA Requirement", "description": "Create a requirement for conducting data protection impact assessments", "parameters": {"name": "Data Protection Impact Assessment", "description": "Conduct data protection impact assessments for high-risk processing", "framework": "GDPR", "category": "risk_assessment", "priority": "medium", "status": "pending", "due_date": "2025-08-26T04:35:00.880985", "assigned_to": "privacy_officer", "tags": ["gdpr", "dpia", "risk_assessment"]}}, {"id": "create_breach_notification_requirement", "type": "create_requirement", "name": "Create Breach Notification Requirement", "description": "Create a requirement for implementing data breach notification processes", "parameters": {"name": "Data Breach Notification", "description": "Implement processes for notifying authorities of data breaches", "framework": "GDPR", "category": "incident_response", "priority": "high", "status": "pending", "due_date": "2025-07-27T04:35:00.880985", "assigned_to": "security_officer", "tags": ["gdpr", "breach_notification", "incident_response"]}}, {"id": "collect_privacy_policy_evidence", "type": "collect_evidence", "name": "Collect Privacy Policy Evidence", "description": "Collect the privacy policy as evidence", "parameters": {"name": "Privacy Policy", "description": "Organization's privacy policy document", "requirement_id": "${create_data_subject_rights_requirement.result.requirement.id}", "type": "document", "source": "website", "collection_method": "automated", "url": "https://example.com/privacy-policy"}}], "variables": {"organization": "Example Organization", "privacy_officer": "<PERSON>", "security_officer": "<PERSON>"}, "settings": {"timeout": 3600, "maxRetries": 3, "retryDelay": 60, "logLevel": "info"}, "tags": ["gdpr", "automation", "compliance"], "created_by": "admin", "created_at": "2025-07-12T04:35:00.880985", "updated_by": "admin", "updated_at": "2025-07-12T04:35:00.880985"}