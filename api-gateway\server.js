/**
 * NovaFuse API Superstore - API Gateway
 *
 * This is the main entry point for the NovaFuse API Superstore.
 * It routes requests to the appropriate services.
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { createProxyMiddleware } = require('http-proxy-middleware');
const rateLimit = require('express-rate-limit');
const path = require('path');
const fs = require('fs');
const jwt = require('jsonwebtoken');

// Import configuration
const config = require('./config');

// Import middleware
const { authenticate, authenticateApiKey, optionalAuth } = require('./middleware/auth');
const { errorHandler, notFound } = require('./middleware/errorHandler');
const { logger, httpLogger } = require('./middleware/logger');

// Create Express app
const app = express();

// Create logs directory if it doesn't exist
if (!fs.existsSync(config.logging.directory)) {
  fs.mkdirSync(config.logging.directory, { recursive: true });
}

// Middleware
app.use(helmet());
app.use(cors(config.cors));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(httpLogger);

// Rate limiting
const limiter = rateLimit(config.rateLimit);
app.use(limiter);

// Service routes and proxies
const services = config.services;

// Create proxies for each service
Object.entries(services).forEach(([name, service]) => {
  // Create proxy middleware
  const proxy = createProxyMiddleware({
    target: service.url,
    changeOrigin: true,
    pathRewrite: {
      [`^${service.path}`]: '/'
    },
    logLevel: 'debug',
    onError: (err, req, res) => {
      logger.error(`Proxy error for ${name}: ${err.message}`);
      res.status(500).json({
        success: false,
        error: 'Service Unavailable',
        message: 'The requested service is currently unavailable. Please try again later.'
      });
    }
  });

  // Add authentication middleware based on service
  if (name === 'novaconnect' || name === 'privacyManagement') {
    // These services require authentication
    app.use(service.path, authenticate, proxy);
  } else if (name === 'regulatoryCompliance' || name === 'securityAssessment') {
    // These services can use either JWT or API key
    app.use(service.path, (req, res, next) => {
      // Check for API key first
      if (req.headers[config.auth.apiKeyHeader.toLowerCase()]) {
        return authenticateApiKey(req, res, next);
      }
      // Fall back to JWT authentication
      return authenticate(req, res, next);
    }, proxy);
  } else {
    // Other services use optional authentication
    app.use(service.path, optionalAuth, proxy);
  }
});

// Create public directory if it doesn't exist
if (!fs.existsSync(path.join(__dirname, 'public'))) {
  fs.mkdirSync(path.join(__dirname, 'public'), { recursive: true });
}

// Create api-docs directory if it doesn't exist
if (!fs.existsSync(path.join(__dirname, 'public/api-docs'))) {
  fs.mkdirSync(path.join(__dirname, 'public/api-docs'), { recursive: true });
}

// API documentation route
app.use('/api-docs', express.static(path.join(__dirname, 'public/api-docs')));

// Authentication routes
app.post('/auth/login', (req, res) => {
  // TODO: Implement actual authentication
  const { username, password } = req.body;

  if (username === 'admin' && password === 'password') {
    const token = jwt.sign(
      { id: '1', username: 'admin', role: 'admin' },
      config.auth.jwtSecret,
      { expiresIn: config.auth.jwtExpiresIn }
    );

    return res.status(200).json({
      success: true,
      token,
      user: {
        id: '1',
        username: 'admin',
        role: 'admin'
      }
    });
  }

  return res.status(401).json({
    success: false,
    error: 'Unauthorized',
    message: 'Invalid username or password'
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'api-gateway',
    timestamp: new Date().toISOString(),
    services: Object.entries(services).map(([name, service]) => ({
      name,
      url: service.url,
      path: service.path
    }))
  });
});

// Root route
app.get('/', (req, res) => {
  res.status(200).json({
    name: 'NovaFuse API Superstore',
    version: '1.0.0',
    description: 'API Gateway for NovaFuse API Superstore',
    documentation: '/api-docs',
    services: Object.keys(services).map(name => ({
      name,
      path: services[name].path
    }))
  });
});

// 404 handler
app.use(notFound);

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = config.server.port;
app.listen(PORT, () => {
  logger.info(`API Gateway listening on port ${PORT} in ${config.server.env} mode`);
});

module.exports = app;
