import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Paper,
  Divider,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { Editor } from '@monaco-editor/react';

const httpMethods = [
  'GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'
];

export default function EndpointsForm({ connector, updateConnector }) {
  const [endpoints, setEndpoints] = useState(connector.endpoints || []);
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);
  const [activeTab, setActiveTab] = useState(0);

  const handleAddEndpoint = () => {
    const newEndpoint = {
      id: `endpoint_${uuidv4().substring(0, 8)}`,
      name: 'New Endpoint',
      description: 'Description of the endpoint',
      path: '',
      method: 'GET',
      parameters: {
        query: {},
        path: {},
        body: {}
      },
      response: {
        successCode: 200
      }
    };
    
    const updatedEndpoints = [...endpoints, newEndpoint];
    setEndpoints(updatedEndpoints);
    updateConnector('endpoints', updatedEndpoints);
    setSelectedEndpoint(newEndpoint.id);
  };

  const handleDeleteEndpoint = (endpointId) => {
    const updatedEndpoints = endpoints.filter(endpoint => endpoint.id !== endpointId);
    setEndpoints(updatedEndpoints);
    updateConnector('endpoints', updatedEndpoints);
    
    if (selectedEndpoint === endpointId) {
      setSelectedEndpoint(updatedEndpoints.length > 0 ? updatedEndpoints[0].id : null);
    }
  };

  const handleUpdateEndpoint = (endpointId, field, value) => {
    const updatedEndpoints = endpoints.map(endpoint => {
      if (endpoint.id === endpointId) {
        return {
          ...endpoint,
          [field]: value
        };
      }
      return endpoint;
    });
    
    setEndpoints(updatedEndpoints);
    updateConnector('endpoints', updatedEndpoints);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              API Endpoints
            </Typography>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddEndpoint}
            >
              Add Endpoint
            </Button>
          </Box>
          
          {endpoints.length === 0 ? (
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 4, 
                textAlign: 'center',
                backgroundColor: 'background.default'
              }}
            >
              <Typography variant="body1" color="text.secondary" gutterBottom>
                No endpoints defined yet
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Define the API endpoints that this connector will use
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddEndpoint}
              >
                Add Your First Endpoint
              </Button>
            </Paper>
          ) : (
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Paper sx={{ backgroundColor: 'background.paper' }}>
                  <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
                    <Typography variant="subtitle1">Endpoints</Typography>
                  </Box>
                  <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
                    {endpoints.map((endpoint) => (
                      <Box
                        key={endpoint.id}
                        sx={{
                          p: 2,
                          cursor: 'pointer',
                          borderBottom: 1,
                          borderColor: 'divider',
                          backgroundColor: selectedEndpoint === endpoint.id ? 'rgba(37, 99, 235, 0.1)' : 'transparent',
                          '&:hover': {
                            backgroundColor: selectedEndpoint === endpoint.id ? 'rgba(37, 99, 235, 0.1)' : 'rgba(0, 0, 0, 0.04)'
                          }
                        }}
                        onClick={() => setSelectedEndpoint(endpoint.id)}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="subtitle2">{endpoint.name}</Typography>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteEndpoint(endpoint.id);
                            }}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <Typography
                            variant="caption"
                            sx={{
                              backgroundColor: (() => {
                                switch (endpoint.method) {
                                  case 'GET': return 'rgba(0, 128, 0, 0.1)';
                                  case 'POST': return 'rgba(255, 165, 0, 0.1)';
                                  case 'PUT': return 'rgba(0, 0, 255, 0.1)';
                                  case 'DELETE': return 'rgba(255, 0, 0, 0.1)';
                                  default: return 'rgba(128, 128, 128, 0.1)';
                                }
                              })(),
                              color: (() => {
                                switch (endpoint.method) {
                                  case 'GET': return 'green';
                                  case 'POST': return 'orange';
                                  case 'PUT': return 'blue';
                                  case 'DELETE': return 'red';
                                  default: return 'gray';
                                }
                              })(),
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              fontWeight: 'bold',
                              mr: 1
                            }}
                          >
                            {endpoint.method}
                          </Typography>
                          <Typography variant="caption" sx={{ color: 'text.secondary', fontFamily: 'monospace' }}>
                            {endpoint.path || '/'}
                          </Typography>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={9}>
                {selectedEndpoint ? (
                  <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
                    {endpoints.filter(endpoint => endpoint.id === selectedEndpoint).map((endpoint) => (
                      <Box key={endpoint.id}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={8}>
                            <TextField
                              label="Endpoint Name"
                              value={endpoint.name}
                              onChange={(e) => handleUpdateEndpoint(endpoint.id, 'name', e.target.value)}
                              fullWidth
                              margin="normal"
                            />
                          </Grid>
                          <Grid item xs={12} sm={4}>
                            <FormControl fullWidth margin="normal">
                              <InputLabel id={`method-label-${endpoint.id}`}>HTTP Method</InputLabel>
                              <Select
                                labelId={`method-label-${endpoint.id}`}
                                value={endpoint.method}
                                onChange={(e) => handleUpdateEndpoint(endpoint.id, 'method', e.target.value)}
                                label="HTTP Method"
                              >
                                {httpMethods.map((method) => (
                                  <MenuItem key={method} value={method}>
                                    {method}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              label="Path"
                              value={endpoint.path}
                              onChange={(e) => handleUpdateEndpoint(endpoint.id, 'path', e.target.value)}
                              fullWidth
                              margin="normal"
                              placeholder="/users/{userId}"
                              helperText="Use {paramName} for path parameters"
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <TextField
                              label="Description"
                              value={endpoint.description}
                              onChange={(e) => handleUpdateEndpoint(endpoint.id, 'description', e.target.value)}
                              fullWidth
                              multiline
                              rows={2}
                              margin="normal"
                            />
                          </Grid>
                        </Grid>
                        
                        <Box sx={{ mt: 3 }}>
                          <Tabs value={activeTab} onChange={handleTabChange} aria-label="endpoint configuration tabs">
                            <Tab label="Parameters" />
                            <Tab label="Response" />
                            <Tab label="Test" />
                          </Tabs>
                          
                          <Box sx={{ mt: 2 }}>
                            {activeTab === 0 && (
                              <Box>
                                <Typography variant="subtitle1" gutterBottom>
                                  Parameters
                                </Typography>
                                
                                <Accordion>
                                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                    <Typography>Query Parameters</Typography>
                                  </AccordionSummary>
                                  <AccordionDetails>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                      Define query parameters for this endpoint.
                                    </Typography>
                                    <Box sx={{ height: '200px' }}>
                                      <Editor
                                        height="100%"
                                        defaultLanguage="json"
                                        defaultValue={JSON.stringify(endpoint.parameters.query || {}, null, 2)}
                                        theme="vs-dark"
                                        options={{
                                          minimap: { enabled: false },
                                          scrollBeyondLastLine: false
                                        }}
                                        onChange={(value) => {
                                          try {
                                            const parsedValue = JSON.parse(value);
                                            const updatedParameters = {
                                              ...endpoint.parameters,
                                              query: parsedValue
                                            };
                                            handleUpdateEndpoint(endpoint.id, 'parameters', updatedParameters);
                                          } catch (e) {
                                            // Handle JSON parse error
                                          }
                                        }}
                                      />
                                    </Box>
                                  </AccordionDetails>
                                </Accordion>
                                
                                <Accordion>
                                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                    <Typography>Path Parameters</Typography>
                                  </AccordionSummary>
                                  <AccordionDetails>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                      Define path parameters for this endpoint.
                                    </Typography>
                                    <Box sx={{ height: '200px' }}>
                                      <Editor
                                        height="100%"
                                        defaultLanguage="json"
                                        defaultValue={JSON.stringify(endpoint.parameters.path || {}, null, 2)}
                                        theme="vs-dark"
                                        options={{
                                          minimap: { enabled: false },
                                          scrollBeyondLastLine: false
                                        }}
                                        onChange={(value) => {
                                          try {
                                            const parsedValue = JSON.parse(value);
                                            const updatedParameters = {
                                              ...endpoint.parameters,
                                              path: parsedValue
                                            };
                                            handleUpdateEndpoint(endpoint.id, 'parameters', updatedParameters);
                                          } catch (e) {
                                            // Handle JSON parse error
                                          }
                                        }}
                                      />
                                    </Box>
                                  </AccordionDetails>
                                </Accordion>
                                
                                <Accordion>
                                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                    <Typography>Request Body</Typography>
                                  </AccordionSummary>
                                  <AccordionDetails>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                                      Define the request body schema for this endpoint.
                                    </Typography>
                                    <Box sx={{ height: '300px' }}>
                                      <Editor
                                        height="100%"
                                        defaultLanguage="json"
                                        defaultValue={JSON.stringify(endpoint.parameters.body || {}, null, 2)}
                                        theme="vs-dark"
                                        options={{
                                          minimap: { enabled: false },
                                          scrollBeyondLastLine: false
                                        }}
                                        onChange={(value) => {
                                          try {
                                            const parsedValue = JSON.parse(value);
                                            const updatedParameters = {
                                              ...endpoint.parameters,
                                              body: parsedValue
                                            };
                                            handleUpdateEndpoint(endpoint.id, 'parameters', updatedParameters);
                                          } catch (e) {
                                            // Handle JSON parse error
                                          }
                                        }}
                                      />
                                    </Box>
                                  </AccordionDetails>
                                </Accordion>
                              </Box>
                            )}
                            
                            {activeTab === 1 && (
                              <Box>
                                <Typography variant="subtitle1" gutterBottom>
                                  Response Configuration
                                </Typography>
                                <Grid container spacing={2}>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      label="Success Status Code"
                                      value={endpoint.response.successCode}
                                      onChange={(e) => {
                                        const updatedResponse = {
                                          ...endpoint.response,
                                          successCode: parseInt(e.target.value) || 200
                                        };
                                        handleUpdateEndpoint(endpoint.id, 'response', updatedResponse);
                                      }}
                                      type="number"
                                      fullWidth
                                      margin="normal"
                                    />
                                  </Grid>
                                  <Grid item xs={12} sm={6}>
                                    <TextField
                                      label="Data Path"
                                      value={endpoint.response.dataPath || ''}
                                      onChange={(e) => {
                                        const updatedResponse = {
                                          ...endpoint.response,
                                          dataPath: e.target.value
                                        };
                                        handleUpdateEndpoint(endpoint.id, 'response', updatedResponse);
                                      }}
                                      fullWidth
                                      margin="normal"
                                      placeholder="$.data"
                                      helperText="JSONPath to the data in the response"
                                    />
                                  </Grid>
                                  <Grid item xs={12}>
                                    <Typography variant="subtitle2" gutterBottom>
                                      Response Schema
                                    </Typography>
                                    <Box sx={{ height: '300px' }}>
                                      <Editor
                                        height="100%"
                                        defaultLanguage="json"
                                        defaultValue={JSON.stringify(endpoint.response.schema || {}, null, 2)}
                                        theme="vs-dark"
                                        options={{
                                          minimap: { enabled: false },
                                          scrollBeyondLastLine: false
                                        }}
                                        onChange={(value) => {
                                          try {
                                            const parsedValue = JSON.parse(value);
                                            const updatedResponse = {
                                              ...endpoint.response,
                                              schema: parsedValue
                                            };
                                            handleUpdateEndpoint(endpoint.id, 'response', updatedResponse);
                                          } catch (e) {
                                            // Handle JSON parse error
                                          }
                                        }}
                                      />
                                    </Box>
                                  </Grid>
                                </Grid>
                              </Box>
                            )}
                            
                            {activeTab === 2 && (
                              <Box>
                                <Typography variant="subtitle1" gutterBottom>
                                  Test Endpoint
                                </Typography>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                                  Test this endpoint with sample parameters.
                                </Typography>
                                
                                <Paper variant="outlined" sx={{ p: 2, mb: 3, backgroundColor: 'background.default' }}>
                                  <Typography variant="subtitle2" gutterBottom>
                                    Request Preview
                                  </Typography>
                                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    <Typography
                                      variant="body2"
                                      sx={{
                                        backgroundColor: (() => {
                                          switch (endpoint.method) {
                                            case 'GET': return 'rgba(0, 128, 0, 0.1)';
                                            case 'POST': return 'rgba(255, 165, 0, 0.1)';
                                            case 'PUT': return 'rgba(0, 0, 255, 0.1)';
                                            case 'DELETE': return 'rgba(255, 0, 0, 0.1)';
                                            default: return 'rgba(128, 128, 128, 0.1)';
                                          }
                                        })(),
                                        color: (() => {
                                          switch (endpoint.method) {
                                            case 'GET': return 'green';
                                            case 'POST': return 'orange';
                                            case 'PUT': return 'blue';
                                            case 'DELETE': return 'red';
                                            default: return 'gray';
                                          }
                                        })(),
                                        px: 1,
                                        py: 0.5,
                                        borderRadius: 1,
                                        fontWeight: 'bold',
                                        mr: 1
                                      }}
                                    >
                                      {endpoint.method}
                                    </Typography>
                                    <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                      {connector.configuration.baseUrl}{endpoint.path}
                                    </Typography>
                                  </Box>
                                  
                                  <Divider sx={{ my: 2 }} />
                                  
                                  <Typography variant="subtitle2" gutterBottom>
                                    Test Parameters
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    This feature will be available in the next update.
                                  </Typography>
                                </Paper>
                                
                                <Button
                                  variant="contained"
                                  startIcon={<CodeIcon />}
                                  disabled
                                >
                                  Test Endpoint
                                </Button>
                              </Box>
                            )}
                          </Box>
                        </Box>
                      </Box>
                    ))}
                  </Paper>
                ) : (
                  <Paper 
                    variant="outlined" 
                    sx={{ 
                      p: 4, 
                      textAlign: 'center',
                      backgroundColor: 'background.default',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <Typography variant="body1" color="text.secondary" gutterBottom>
                      No endpoint selected
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Select an endpoint from the list or create a new one
                    </Typography>
                    <Button
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={handleAddEndpoint}
                    >
                      Add Endpoint
                    </Button>
                  </Paper>
                )}
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
