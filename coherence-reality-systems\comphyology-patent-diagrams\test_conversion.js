const { execSync } = require('child_process');
const path = require('path');

const inputFile = 'FIG1_uuft_core_architecture.mmd';
const outputFile = 'TEST_OUTPUT.svg';

console.log('Testing Mermaid CLI with single file...');
console.log(`Input: ${inputFile}`);
console.log(`Output: ${outputFile}`);

try {
    console.log('\nRunning conversion command...');
    const cmd = `npx @mermaid-js/mermaid-cli -i "${inputFile}" -o "${outputFile}"`;
    console.log(`Command: ${cmd}\n`);
    
    const output = execSync(cmd, { stdio: 'pipe' });
    console.log('Conversion output:');
    console.log(output.toString());
    
    console.log('\nChecking if output file was created...');
    const fs = require('fs');
    if (fs.existsSync(outputFile)) {
        console.log(`✅ Success! Output file created: ${outputFile}`);
    } else {
        console.log('❌ Output file was not created');
    }
} catch (error) {
    console.error('\n❌ Error during conversion:');
    console.error('Error message:', error.message);
    console.error('\nError output:');
    console.error(error.stderr ? error.stderr.toString() : 'No error output');
}
