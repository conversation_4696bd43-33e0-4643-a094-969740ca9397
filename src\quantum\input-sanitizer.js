/**
 * Quantum Input Sanitizer
 *
 * This module provides functions for sanitizing inputs to quantum operations,
 * ensuring that all inputs are valid and safe for use in quantum calculations.
 *
 * This module implements bounded calculus principles from Comphyology,
 * ensuring all values remain within finite boundaries.
 */

const { MAX_SAFE_BOUNDS, saturate, asymptotic } = require('./constants');

// Default Ψ value to use when input is invalid
const DEFAULT_PSI_VALUE = 0.5;

// Entropy firewall status
let entropyFirewallActive = false;

/**
 * Harden a numeric input to ensure it's safe for quantum operations
 * @param {any} input - The input to harden
 * @param {string} domain - Domain to use for bounds (default: 'universal')
 * @returns {number} - The hardened input
 */
function hardenInput(input, domain = 'universal') {
  // Check if input is a valid number
  if (typeof input !== 'number' || !Number.isFinite(input)) {
    triggerEntropyFirewall();
    return DEFAULT_PSI_VALUE;
  }

  // Handle boundary values using domain-specific saturation
  if (Math.abs(input) > MAX_SAFE_BOUNDS[domain.toUpperCase()]?.MAX_VALUE ||
      (input !== 0 && Math.abs(input) < MAX_SAFE_BOUNDS[domain.toUpperCase()]?.MIN_VALUE)) {
    return saturate.forDomain(domain, input);
  }

  // Handle IEEE-754 special values (replacing Infinity handling)
  if (Number.isNaN(input) ||
      !Number.isFinite(input) ||
      input === Number.POSITIVE_INFINITY ||
      input === Number.NEGATIVE_INFINITY) {
    triggerEntropyFirewall();
    return DEFAULT_PSI_VALUE;
  }

  // Apply asymptotic thresholding for values near boundaries
  if (Math.abs(input) > MAX_SAFE_BOUNDS[domain.toUpperCase()]?.MAX_VALUE * 0.9) {
    return asymptotic.forDomain(domain, input);
  }

  return input;
}

/**
 * Normalize extreme values to safe ranges using asymptotic thresholding
 * @param {number} value - The extreme value to normalize
 * @param {string} domain - Domain to use for bounds (default: 'universal')
 * @returns {number} - The normalized value
 */
function normalizeExtremeValue(value, domain = 'universal') {
  // Get domain-specific bounds
  const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()] || MAX_SAFE_BOUNDS.UNIVERSAL;

  // Apply asymptotic thresholding to approach but never reach boundaries
  return asymptotic.threshold(value, bounds.MAX_VALUE);
}

/**
 * Trigger the entropy firewall to protect against quantum attacks
 */
function triggerEntropyFirewall() {
  entropyFirewallActive = true;

  // Log the firewall activation
  console.log('[QUANTUM SHIELD] Entropy firewall activated');

  // In a real implementation, this would implement additional
  // protection mechanisms against quantum attacks

  // Reset firewall after a delay
  setTimeout(() => {
    entropyFirewallActive = false;
    console.log('[QUANTUM SHIELD] Entropy firewall deactivated');
  }, 5000);
}

/**
 * Check if the entropy firewall is active
 * @returns {boolean} - Whether the entropy firewall is active
 */
function isEntropyFirewallActive() {
  return entropyFirewallActive;
}

/**
 * Sanitize a tensor for quantum operations
 * @param {Object} tensor - The tensor to sanitize
 * @param {string} domain - Domain to use for bounds (default: 'universal')
 * @returns {Object} - The sanitized tensor
 */
function sanitizeTensor(tensor, domain = 'universal') {
  // Check if tensor is valid
  if (!tensor || typeof tensor !== 'object') {
    return createDefaultTensor(domain);
  }

  // Check if tensor has dimensions and values
  if (!Array.isArray(tensor.dimensions) || !Array.isArray(tensor.values)) {
    return createDefaultTensor(domain);
  }

  // Get domain-specific bounds
  const bounds = MAX_SAFE_BOUNDS[domain.toUpperCase()] || MAX_SAFE_BOUNDS.UNIVERSAL;

  // Enforce maximum tensor dimensions
  const sanitizedDimensions = tensor.dimensions.map(dim =>
    Math.min(dim, bounds.MAX_DIMENSION)
  );

  // Enforce maximum tensor size
  if (sanitizedDimensions.reduce((a, b) => a * b, 1) > bounds.MAX_TENSOR_SIZE) {
    // Rescale dimensions to fit within bounds
    const scale = Math.pow(bounds.MAX_TENSOR_SIZE / sanitizedDimensions.reduce((a, b) => a * b, 1), 1/sanitizedDimensions.length);
    for (let i = 0; i < sanitizedDimensions.length; i++) {
      sanitizedDimensions[i] = Math.floor(sanitizedDimensions[i] * scale);
    }
  }

  // Sanitize tensor values using domain-specific bounds
  const sanitizedValues = tensor.values.map(value => hardenInput(value, domain));

  // Create sanitized tensor
  return {
    dimensions: sanitizedDimensions,
    values: sanitizedValues,
    domain: domain,
    integrity: tensor.integrity ? { ...tensor.integrity } : createIntegrityHash(sanitizedDimensions, sanitizedValues)
  };
}

/**
 * Create a default tensor
 * @param {string} domain - Domain to use for bounds (default: 'universal')
 * @returns {Object} - A default tensor
 */
function createDefaultTensor(domain = 'universal') {
  const dimensions = [1];
  const values = [DEFAULT_PSI_VALUE];

  return {
    dimensions,
    values,
    domain: domain,
    integrity: createIntegrityHash(dimensions, values)
  };
}

/**
 * Create an integrity hash for a tensor
 * @param {Array} dimensions - The tensor dimensions
 * @param {Array} values - The tensor values
 * @returns {Object} - The integrity hash
 */
function createIntegrityHash(dimensions, values) {
  // Simple hash function for demonstration
  let hash = 0;
  const str = JSON.stringify(dimensions) + JSON.stringify(values);

  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) - hash) + str.charCodeAt(i);
    hash |= 0; // Convert to 32-bit integer
  }

  return {
    hash: hash.toString(16),
    timestamp: Date.now()
  };
}

/**
 * Sanitize cross-domain input
 * @param {Object} input - The cross-domain input to sanitize
 * @returns {Object} - The sanitized cross-domain input
 */
function sanitizeCrossDomainInput(input) {
  // Check if input is valid
  if (!input || typeof input !== 'object') {
    return createDefaultCrossDomainInput();
  }

  // Check if input has source and target domains
  if (!input.source || !input.target) {
    return createDefaultCrossDomainInput();
  }

  // Sanitize payload
  const sanitizedPayload = input.payload ? sanitizePayload(input.payload) : {};

  // Create sanitized cross-domain input
  return {
    source: input.source,
    target: input.target,
    payload: sanitizedPayload,
    timestamp: Date.now(),
    integrity: createIntegrityHash([input.source, input.target], Object.values(sanitizedPayload))
  };
}

/**
 * Create a default cross-domain input
 * @returns {Object} - A default cross-domain input
 */
function createDefaultCrossDomainInput() {
  return {
    source: 'universal',
    target: 'universal',
    payload: {},
    timestamp: Date.now(),
    integrity: createIntegrityHash(['universal', 'universal'], [])
  };
}

/**
 * Sanitize payload
 * @param {Object} payload - The payload to sanitize
 * @returns {Object} - The sanitized payload
 */
function sanitizePayload(payload) {
  // Check if payload is valid
  if (!payload || typeof payload !== 'object') {
    return {};
  }

  // Create sanitized payload
  const sanitizedPayload = {};

  // Sanitize each property
  for (const [key, value] of Object.entries(payload)) {
    if (typeof value === 'number') {
      sanitizedPayload[key] = hardenInput(value);
    } else if (typeof value === 'string') {
      sanitizedPayload[key] = value.substring(0, 1000); // Limit string length
    } else if (typeof value === 'boolean') {
      sanitizedPayload[key] = value;
    } else if (Array.isArray(value)) {
      sanitizedPayload[key] = value.slice(0, 100).map(item =>
        typeof item === 'number' ? hardenInput(item) : item
      );
    } else if (typeof value === 'object' && value !== null) {
      sanitizedPayload[key] = sanitizePayload(value); // Recursively sanitize nested objects
    }
  }

  return sanitizedPayload;
}

module.exports = {
  hardenInput,
  sanitizeTensor,
  sanitizeCrossDomainInput,
  isEntropyFirewallActive,
  DEFAULT_PSI_VALUE
};
