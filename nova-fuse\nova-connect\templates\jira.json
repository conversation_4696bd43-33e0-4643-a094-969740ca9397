{"metadata": {"name": "<PERSON><PERSON>", "version": "1.0.0", "category": "Workflow & Ticketing", "description": "Connect to Jira for issue tracking and project management", "author": "NovaGRC", "tags": ["jira", "atlassian", "issues", "tickets", "workflow"], "created": "2025-01-01T00:00:00Z", "updated": "2025-01-01T00:00:00Z", "icon": "https://wac-cdn.atlassian.com/assets/img/favicons/atlassian/favicon-32x32.png"}, "authentication": {"type": "BASIC", "fields": {"email": {"type": "string", "description": "Jira account email", "required": true}, "apiToken": {"type": "string", "description": "Jira API token", "required": true, "sensitive": true}, "domain": {"type": "string", "description": "Jira domain (e.g., company.atlassian.net)", "required": true}}, "testConnection": {"endpoint": "/rest/api/3/myself", "method": "GET", "expectedResponse": {"status": 200}}}, "configuration": {"baseUrl": "https://{{domain}}", "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "timeout": 30000, "retryPolicy": {"maxRetries": 3, "backoffStrategy": "exponential"}}, "endpoints": [{"id": "searchIssues", "name": "Search Issues", "description": "Search for issues in Jira", "path": "/rest/api/3/search", "method": "POST", "parameters": {"body": {"jql": "{{jql}}", "startAt": 0, "maxResults": 50, "fields": ["summary", "status", "assignee", "reporter", "priority", "issuetype", "created", "updated", "duedate", "labels", "description"]}}, "pagination": {"type": "offset", "parameters": {"startAt": "startAt", "maxResults": "maxResults", "total": "total"}}, "response": {"successCode": 200, "dataPath": "$.issues"}}, {"id": "getIssue", "name": "Get Issue", "description": "Get a specific issue from <PERSON><PERSON>", "path": "/rest/api/3/issue/{{issueKey}}", "method": "GET", "parameters": {"path": {"issueKey": "string"}, "query": {"fields": "summary,status,assignee,reporter,priority,issuetype,created,updated,duedate,labels,description"}}, "response": {"successCode": 200}}, {"id": "createIssue", "name": "Create Issue", "description": "Create a new issue in Jira", "path": "/rest/api/3/issue", "method": "POST", "parameters": {"body": {"fields": {"project": {"key": "{{project<PERSON><PERSON>}}"}, "summary": "{{summary}}", "description": {"type": "doc", "version": 1, "content": [{"type": "paragraph", "content": [{"type": "text", "text": "{{description}}"}]}]}, "issuetype": {"name": "{{issueType}}"}, "priority": {"name": "{{priority}}"}, "labels": "{{labels}}"}}}, "response": {"successCode": 201}}, {"id": "updateIssue", "name": "Update Issue", "description": "Update an existing issue in Jira", "path": "/rest/api/3/issue/{{issueKey}}", "method": "PUT", "parameters": {"path": {"issueKey": "string"}, "body": {"fields": "{{fields}}"}}, "response": {"successCode": 204}}, {"id": "getTransitions", "name": "Get Transitions", "description": "Get available transitions for an issue", "path": "/rest/api/3/issue/{{issueKey}}/transitions", "method": "GET", "parameters": {"path": {"issueKey": "string"}}, "response": {"successCode": 200, "dataPath": "$.transitions"}}, {"id": "transitionIssue", "name": "Transition Issue", "description": "Transition an issue to a new status", "path": "/rest/api/3/issue/{{issueKey}}/transitions", "method": "POST", "parameters": {"path": {"issueKey": "string"}, "body": {"transition": {"id": "{{transitionId}}"}}}, "response": {"successCode": 204}}, {"id": "getProjects", "name": "Get Projects", "description": "Get all projects in Jira", "path": "/rest/api/3/project", "method": "GET", "response": {"successCode": 200}}], "mappings": [{"sourceEndpoint": "searchIssues", "targetSystem": "NovaGRC", "targetEntity": "ComplianceTasks", "transformations": [{"source": "$[*].key", "target": "taskId", "transform": "identity"}, {"source": "$[*].fields.summary", "target": "title", "transform": "identity"}, {"source": "$[*].fields.description.content[0].content[0].text", "target": "description", "transform": "identity"}, {"source": "$[*].fields.status.name", "target": "status", "transform": "mapJiraStatusToCompliance"}, {"source": "$[*].fields.assignee.displayName", "target": "assignee", "transform": "identity"}, {"source": "$[*].fields.priority.name", "target": "priority", "transform": "mapJiraPriorityToRisk"}, {"source": "$[*].fields.created", "target": "createdAt", "transform": "formatDate"}, {"source": "$[*].fields.duedate", "target": "dueDate", "transform": "formatDate"}, {"source": "$[*].fields.labels", "target": "tags", "transform": "identity"}]}], "events": {"webhooks": [{"path": "/webhook/jira", "method": "POST", "description": "Webhook for Jira issue events", "handler": "handleJiraWebhook"}], "polling": [{"endpoint": "searchIssues", "interval": "5m", "condition": "hasUpdatedIssues"}]}}