.quantum-debug-panel {
  background: #1a202c;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1.5rem;
  color: #e2e8f0;
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 0.9rem;
  border: 1px solid #2d3748;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid #2d3748;
}

.debug-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #81e6d9;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4ade80;
  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.status-text {
  font-size: 0.8rem;
  text-transform: capitalize;
  color: #a0aec0;
}

.debug-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.metric-group {
  background: #2d3748;
  border-radius: 6px;
  padding: 0.75rem;
  border-left: 3px solid #4299e1;
}

.metric-group h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.8rem;
  color: #90cdf4;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.metric:last-child {
  margin-bottom: 0;
}

.metric-label {
  color: #a0aec0;
}

.metric-value {
  font-weight: 500;
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
}

.metric-subtext {
  font-size: 0.75rem;
  color: #718096;
  margin-left: 0.25rem;
}

.debug-log {
  background: #1a202c;
  border-radius: 6px;
  border: 1px solid #2d3748;
  overflow: hidden;
}

.debug-log h4 {
  margin: 0;
  padding: 0.75rem 1rem;
  background: #2d3748;
  font-size: 0.8rem;
  color: #90cdf4;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #2d3748;
}

.log-entries {
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.8rem;
}

.log-entry {
  display: flex;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #2d3748;
  gap: 1rem;
  align-items: center;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #718096;
  font-family: 'Fira Code', monospace;
  font-size: 0.75rem;
  min-width: 80px;
}

.log-mode {
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.15rem 0.4rem;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.log-mode-quantum {
  background: rgba(138, 43, 226, 0.2);
  color: #b794f4;
  border: 1px solid rgba(138, 43, 226, 0.3);
}

.log-mode-classical {
  background: rgba(113, 128, 150, 0.2);
  color: #a0aec0;
  border: 1px solid rgba(113, 128, 150, 0.3);
}

.log-message {
  flex: 1;
  color: #e2e8f0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.log-warning {
  color: #f6ad55;
  margin-left: 0.5rem;
  font-weight: 500;
}

/* Scrollbar styling */
.log-entries::-webkit-scrollbar {
  width: 6px;
}

.log-entries::-webkit-scrollbar-track {
  background: #1a202c;
}

.log-entries::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.log-entries::-webkit-scrollbar-thumb:hover {
  background: #718096;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .debug-metrics {
    grid-template-columns: 1fr;
  }
  
  .log-entry {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .log-time {
    order: 1;
  }
  
  .log-mode {
    order: 2;
  }
  
  .log-message {
    order: 3;
    width: 100%;
  }
}

/* Animation for status indicators */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Status-specific colors */
.status-measuring .status-dot {
  background: #fbbf24;
  box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.3);
  animation: blink 1.5s infinite;
}

.status-error .status-dot {
  background: #f87171;
  box-shadow: 0 0 0 2px rgba(248, 113, 113, 0.3);
  animation: blink 0.5s infinite;
}

.status-collapsed .status-dot {
  background: #60a5fa;
  box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
}

.status-idle .status-dot {
  background: #9ca3af;
  box-shadow: 0 0 0 2px rgba(156, 163, 175, 0.3);
  animation: none;
}
