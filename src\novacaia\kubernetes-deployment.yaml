apiVersion: v1
kind: Namespace
metadata:
  name: novacaia-enterprise
  labels:
    name: novacaia-enterprise
    environment: production
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: novacaia-deployment
  namespace: novacaia-enterprise
  labels:
    app: novacaia
    version: v1.0.0-enterprise
spec:
  replicas: 3
  selector:
    matchLabels:
      app: novacaia
  template:
    metadata:
      labels:
        app: novacaia
        version: v1.0.0-enterprise
    spec:
      containers:
      - name: novacaia
        image: registry.cadence.ai/novacaia:v1.0.0-enterprise
        ports:
        - containerPort: 7777
          name: http
        env:
        - name: PLATFORM_ALLOCATION
          value: "18.0"
        - name: ENTERPRISE_RETENTION
          value: "82.0"
        - name: PRODUCTION_MODE
          value: "True"
        - name: CONSCIOUSNESS_THRESHOLD
          value: "0.91"
        - name: ACCURACY_TARGET
          value: "0.9783"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 7777
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 7777
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
---
apiVersion: v1
kind: Service
metadata:
  name: novacaia-service
  namespace: novacaia-enterprise
  labels:
    app: novacaia
spec:
  selector:
    app: novacaia
  ports:
  - name: http
    port: 80
    targetPort: 7777
    protocol: TCP
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novacaia-ingress
  namespace: novacaia-enterprise
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.novacaia.com
    secretName: novacaia-tls
  rules:
  - host: api.novacaia.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: novacaia-service
            port:
              number: 80
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: novacaia-hpa
  namespace: novacaia-enterprise
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: novacaia-deployment
  minReplicas: 3
  maxReplicas: 100
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: novacaia-config
  namespace: novacaia-enterprise
data:
  production-config.json: |
    {
      "environment": "production",
      "platform_allocation": 18.0,
      "enterprise_retention": 82.0,
      "consciousness_threshold": 0.91,
      "accuracy_target": 0.9783,
      "processing_timeout": 30,
      "max_concurrent": 1000000,
      "security": {
        "encryption": "AES-256",
        "audit_logging": true,
        "rate_limiting": true
      },
      "monitoring": {
        "consciousness_metrics": true,
        "performance_metrics": true,
        "business_metrics": true,
        "real_time_alerts": true
      }
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: novacaia-secrets
  namespace: novacaia-enterprise
type: Opaque
data:
  # Base64 encoded secrets (replace with actual values)
  api-key: bm92YWNhaWEtYXBpLWtleQ==  # novacaia-api-key
  encryption-key: ZW5jcnlwdGlvbi1rZXktMjU2LWJpdA==  # encryption-key-256-bit
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: novacaia-pdb
  namespace: novacaia-enterprise
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: novacaia
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: novacaia-serviceaccount
  namespace: novacaia-enterprise
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: novacaia-role
  namespace: novacaia-enterprise
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: novacaia-rolebinding
  namespace: novacaia-enterprise
subjects:
- kind: ServiceAccount
  name: novacaia-serviceaccount
  namespace: novacaia-enterprise
roleRef:
  kind: Role
  name: novacaia-role
  apiGroup: rbac.authorization.k8s.io
