/**
 * Advanced Visualizations Dashboard Example
 * 
 * This example demonstrates how to use the advanced visualization components.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  DashboardCard,
  TabPanel,
  GraphVisualization,
  HeatmapVisualization,
  TreemapVisualization,
  SankeyVisualization,
  ResponsiveLayout,
  SkipLink,
  ThemeSelector
} from '../components';
import { ThemeProvider, useTheme } from '../theme';

/**
 * Advanced Visualizations Dashboard Content component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Advanced Visualizations Dashboard Content component
 */
const AdvancedVisualizationsDashboardContent = ({
  novaConnect,
  novaShield,
  novaTrack,
  enableLogging = false
}) => {
  const { theme } = useTheme();
  const [loading, setLoading] = useState(true);
  const [visualizationData, setVisualizationData] = useState(null);
  const [activeTab, setActiveTab] = useState('graph');
  
  // Fetch visualization data
  useEffect(() => {
    const fetchVisualizationData = async () => {
      try {
        if (enableLogging) {
          console.log('Fetching visualization data...');
        }
        
        // Simulate API call to fetch visualization data
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Sample visualization data
        const data = {
          graph: {
            nodes: [
              { id: 'n1', label: 'AWS S3', category: 'storage', riskScore: 25 },
              { id: 'n2', label: 'Azure AD', category: 'identity', riskScore: 15 },
              { id: 'n3', label: 'Salesforce', category: 'crm', riskScore: 40 },
              { id: 'n4', label: 'Google Workspace', category: 'productivity', riskScore: 20 },
              { id: 'n5', label: 'Customer Data', category: 'data', riskScore: 75 },
              { id: 'n6', label: 'Employee Data', category: 'data', riskScore: 60 },
              { id: 'n7', label: 'Payment Processing', category: 'finance', riskScore: 80 }
            ],
            edges: [
              { source: 'n1', target: 'n5', weight: 3 },
              { source: 'n1', target: 'n6', weight: 2 },
              { source: 'n2', target: 'n4', weight: 1 },
              { source: 'n2', target: 'n6', weight: 3 },
              { source: 'n3', target: 'n5', weight: 4 },
              { source: 'n4', target: 'n6', weight: 2 },
              { source: 'n5', target: 'n7', weight: 5 },
              { source: 'n6', target: 'n7', weight: 3 }
            ]
          },
          heatmap: {
            x: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            y: ['AWS S3', 'Azure AD', 'Salesforce', 'Google Workspace', 'Payment Processing'],
            values: [
              [25, 30, 15, 20, 35, 40],
              [15, 20, 25, 30, 20, 15],
              [40, 35, 30, 25, 20, 15],
              [20, 25, 30, 35, 30, 25],
              [80, 75, 70, 65, 60, 55]
            ]
          },
          treemap: {
            name: 'Risk Map',
            children: [
              {
                name: 'Infrastructure',
                children: [
                  { name: 'AWS S3', value: 25 },
                  { name: 'Azure AD', value: 15 },
                  { name: 'Google Workspace', value: 20 }
                ]
              },
              {
                name: 'Applications',
                children: [
                  { name: 'Salesforce', value: 40 },
                  { name: 'Office 365', value: 30 },
                  { name: 'Slack', value: 15 }
                ]
              },
              {
                name: 'Data',
                children: [
                  { name: 'Customer Data', value: 75 },
                  { name: 'Employee Data', value: 60 },
                  { name: 'Financial Data', value: 85 }
                ]
              }
            ]
          },
          sankey: {
            nodes: [
              { id: 'a', name: 'AWS S3' },
              { id: 'b', name: 'Azure AD' },
              { id: 'c', name: 'Salesforce' },
              { id: 'd', name: 'Customer Data' },
              { id: 'e', name: 'Employee Data' },
              { id: 'f', name: 'Payment Processing' }
            ],
            links: [
              { source: 'a', target: 'd', value: 30 },
              { source: 'a', target: 'e', value: 20 },
              { source: 'b', target: 'e', value: 30 },
              { source: 'c', target: 'd', value: 40 },
              { source: 'd', target: 'f', value: 50 },
              { source: 'e', target: 'f', value: 30 }
            ]
          }
        };
        
        setVisualizationData(data);
        setLoading(false);
        
        if (enableLogging) {
          console.log('Visualization data fetched successfully');
        }
      } catch (error) {
        console.error('Error fetching visualization data', error);
        setLoading(false);
      }
    };
    
    fetchVisualizationData();
  }, [enableLogging]);
  
  // Refresh visualization data
  const handleRefresh = async () => {
    setLoading(true);
    
    try {
      if (enableLogging) {
        console.log('Refreshing visualization data...');
      }
      
      // Simulate API call to refresh visualization data
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Update visualization data with new values
      setVisualizationData(prevData => {
        if (!prevData) return null;
        
        // Update heatmap values
        const newHeatmapValues = prevData.heatmap.values.map(row => 
          row.map(value => Math.max(10, Math.min(90, value + Math.floor(Math.random() * 20) - 10)))
        );
        
        return {
          ...prevData,
          heatmap: {
            ...prevData.heatmap,
            values: newHeatmapValues
          }
        };
      });
      
      setLoading(false);
      
      if (enableLogging) {
        console.log('Visualization data refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing visualization data', error);
      setLoading(false);
    }
  };
  
  // Define tabs for the dashboard
  const dashboardTabs = [
    {
      id: 'graph',
      label: 'Network Graph',
      content: (
        <DashboardCard
          title="Risk Relationship Network"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <div className="h-[500px]">
            <GraphVisualization
              data={visualizationData?.graph || { nodes: [], edges: [] }}
              options={{
                layout: 'force',
                nodeSize: 'value',
                nodeColor: 'category',
                edgeWidth: 'value',
                interactive: true,
                zoomable: true,
                draggable: true,
                highlightNeighbors: true,
                showLegend: true
              }}
              onNodeClick={(node) => {
                if (enableLogging) {
                  console.log('Node clicked:', node);
                }
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'heatmap',
      label: 'Heatmap',
      content: (
        <DashboardCard
          title="Risk Heatmap by Month"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <div className="h-[500px]">
            <HeatmapVisualization
              data={visualizationData?.heatmap}
              options={{
                title: 'Risk Scores by System and Month',
                xAxisLabel: 'Month',
                yAxisLabel: 'System',
                colorScale: [
                  theme.colors.success,
                  theme.colors.successLight,
                  theme.colors.background,
                  theme.colors.warningLight,
                  theme.colors.warning,
                  theme.colors.error
                ],
                showLegend: true,
                showTooltip: true,
                showGrid: true,
                showLabels: true,
                formatValue: (value) => value.toFixed(0)
              }}
              onCellClick={(cell) => {
                if (enableLogging) {
                  console.log('Cell clicked:', cell);
                }
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'treemap',
      label: 'Treemap',
      content: (
        <DashboardCard
          title="Risk Treemap"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <div className="h-[500px]">
            <TreemapVisualization
              data={visualizationData?.treemap}
              options={{
                title: 'Risk Distribution by Category',
                colorScale: [
                  theme.colors.primary,
                  theme.colors.secondary,
                  theme.colors.success,
                  theme.colors.warning,
                  theme.colors.error,
                  theme.colors.info
                ],
                showTooltip: true,
                showLabels: true,
                formatValue: (value) => value.toFixed(0)
              }}
              onCellClick={(cell) => {
                if (enableLogging) {
                  console.log('Cell clicked:', cell);
                }
              }}
            />
          </div>
        </DashboardCard>
      )
    },
    {
      id: 'sankey',
      label: 'Sankey Diagram',
      content: (
        <DashboardCard
          title="Data Flow Diagram"
          collapsible={true}
          onRefresh={handleRefresh}
          loading={loading}
        >
          <div className="h-[500px]">
            <SankeyVisualization
              data={visualizationData?.sankey}
              options={{
                title: 'Data Flow Between Systems',
                colorScale: [
                  theme.colors.primary,
                  theme.colors.secondary,
                  theme.colors.success,
                  theme.colors.warning,
                  theme.colors.error,
                  theme.colors.info
                ],
                showTooltip: true,
                showLabels: true,
                showLinkLabels: true,
                formatValue: (value) => value.toFixed(0),
                nodeWidth: 20,
                nodePadding: 10
              }}
              onNodeClick={(node) => {
                if (enableLogging) {
                  console.log('Node clicked:', node);
                }
              }}
              onLinkClick={(link) => {
                if (enableLogging) {
                  console.log('Link clicked:', link);
                }
              }}
            />
          </div>
        </DashboardCard>
      )
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Skip link for keyboard navigation */}
      <SkipLink targetId="main-content" />
      
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-textPrimary" id="dashboard-title">
          Advanced Visualizations
        </h1>
        
        <div className="flex items-center space-x-2">
          <ThemeSelector variant="dropdown" />
          
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={handleRefresh}
            disabled={loading}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>
      
      {/* Main content */}
      <main id="main-content" tabIndex="-1">
        <TabPanel
          tabs={dashboardTabs}
          defaultTab="graph"
          variant="pills"
          onTabChange={setActiveTab}
        />
      </main>
    </div>
  );
};

AdvancedVisualizationsDashboardContent.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  enableLogging: PropTypes.bool
};

/**
 * Advanced Visualizations Dashboard component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.novaConnect - NovaConnect instance
 * @param {Object} props.novaShield - NovaShield instance
 * @param {Object} props.novaTrack - NovaTrack instance
 * @param {Object} [props.initialTheme] - Initial theme
 * @param {boolean} [props.enableSystemPreference=true] - Whether to enable system preference detection
 * @param {boolean} [props.enableLogging=false] - Whether to enable logging
 * @returns {React.ReactElement} Advanced Visualizations Dashboard component
 */
const AdvancedVisualizationsDashboard = ({
  novaConnect,
  novaShield,
  novaTrack,
  initialTheme,
  enableSystemPreference = true,
  enableLogging = false
}) => {
  return (
    <ThemeProvider theme={initialTheme} enableSystemPreference={enableSystemPreference}>
      <AdvancedVisualizationsDashboardContent
        novaConnect={novaConnect}
        novaShield={novaShield}
        novaTrack={novaTrack}
        enableLogging={enableLogging}
      />
    </ThemeProvider>
  );
};

AdvancedVisualizationsDashboard.propTypes = {
  novaConnect: PropTypes.object,
  novaShield: PropTypes.object,
  novaTrack: PropTypes.object,
  initialTheme: PropTypes.object,
  enableSystemPreference: PropTypes.bool,
  enableLogging: PropTypes.bool
};

export default AdvancedVisualizationsDashboard;
