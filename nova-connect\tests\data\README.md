# NovaFuse Universal API Connector - Data Layer Tests

This directory contains tests for the data layer of the NovaFuse Universal API Connector (UAC).

## Structure

- `db-connection.test.js` - Tests for the database connection manager
- `repositories/` - Tests for data access repositories
  - `connector-repository.test.js` - Tests for the connector template repository
  - `credential-repository.test.js` - Tests for the API credential repository
  - `api-usage-repository.test.js` - Tests for the API usage tracking repository
  - `partner-repository.test.js` - Tests for the partner information repository

## Running Tests

To run all tests:

```bash
npm test
```

To run only data layer tests:

```bash
npx jest tests/data
```

## Test Coverage

The tests aim to achieve at least 80% code coverage for the data layer. This includes:

- Database connection management
- Model validation
- Repository methods
- Error handling

## Mocking

The tests use Je<PERSON>'s mocking capabilities to mock:

- MongoDB connection
- Mongoose models
- Encryption/decryption functions

This allows the tests to run without requiring a real MongoDB instance.

## Test Environment

The tests run in a Node.js environment with the following configuration:

- `NODE_ENV` set to `test`
- `MONGODB_URI` set to `mongodb://localhost:27017/nova-connect-test`
- `CREDENTIALS_ENCRYPTION_KEY` set to a test key

## Adding New Tests

When adding new functionality to the data layer, please add corresponding tests. Follow these guidelines:

1. Create a new test file if testing a new component
2. Use descriptive test names that explain what is being tested
3. Mock external dependencies
4. Test both success and failure cases
5. Test edge cases and error handling
