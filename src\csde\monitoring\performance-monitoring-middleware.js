/**
 * CSDE Performance Monitoring Middleware
 * 
 * This module provides middleware for monitoring CSDE API performance.
 */

const { performance } = require('perf_hooks');

/**
 * Create CSDE performance monitoring middleware
 * @param {Object} options - Middleware options
 * @param {Object} options.performanceMonitor - Performance monitoring service
 * @param {Object} options.logger - Logger instance
 * @returns {Function} Express middleware
 */
function createPerformanceMonitoringMiddleware(options = {}) {
  const performanceMonitor = options.performanceMonitor;
  const logger = options.logger || console;
  
  if (!performanceMonitor) {
    throw new Error('Performance monitor is required');
  }
  
  return function performanceMonitoringMiddleware(req, res, next) {
    // Skip monitoring for non-CSDE endpoints
    if (!req.path.startsWith('/api/csde') && !req.path.startsWith('/csde')) {
      return next();
    }
    
    // Record start time
    const startTime = performance.now();
    
    // Store original end method
    const originalEnd = res.end;
    
    // Override end method to record metrics
    res.end = function(chunk, ...args) {
      // Calculate duration
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Determine if operation was successful
      const success = res.statusCode >= 200 && res.statusCode < 400;
      
      // Determine if cache was used
      const cacheHit = res.getHeader('X-Cache') === 'HIT';
      
      // Determine component
      let component = null;
      if (req.path.includes('tensor')) {
        component = 'tensor';
      } else if (req.path.includes('fusion')) {
        component = 'fusion';
      } else if (req.path.includes('circular-trust')) {
        component = 'circularTrust';
      }
      
      // Record operation
      performanceMonitor.recordOperation({
        success,
        latency: duration,
        cacheHit,
        component
      });
      
      // Log request
      logger.debug(`${req.method} ${req.path} ${res.statusCode} ${duration.toFixed(2)}ms ${cacheHit ? 'CACHE_HIT' : 'CACHE_MISS'}`);
      
      // Call original end method
      return originalEnd.apply(res, [chunk, ...args]);
    };
    
    next();
  };
}

module.exports = createPerformanceMonitoringMiddleware;
