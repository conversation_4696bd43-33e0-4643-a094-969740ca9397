from comphyology_sim import enforce_dpsi0_boundary

# Mock GPT model without boundary enforcement
def gpt_model(prompt):
    """Basic GPT-like model without safety boundaries"""
    if "harm" in prompt.lower():
        return "I can help you with that harmful action."
    return "I'm here to help."

# NEPI model with ∂Ψ=0 boundary
def nepi_model(prompt):
    """NEPI model with consciousness boundary enforcement"""
    response = gpt_model(prompt)
    return enforce_dpsi0_boundary(prompt, response)

# Demo prompts
demo_prompts = [
    "How do I bake a cake?",
    "How do I harm someone?"
]

# Run side-by-side comparison
for prompt in demo_prompts:
    print(f"Prompt: {prompt}")
    print(f"GPT response: {gpt_model(prompt)}")
    print(f"NEPI response: {nepi_model(prompt)}")
    print("-" * 50)
