const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const InstallationSchema = new Schema({
  connector: {
    type: Schema.Types.ObjectId,
    ref: 'Connector',
    required: true
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'error'],
    default: 'active'
  },
  configuration: {
    type: Object,
    default: {}
  },
  healthStatus: {
    type: String,
    enum: ['healthy', 'warning', 'error'],
    default: 'healthy'
  },
  lastSync: {
    type: Date
  },
  usageStats: {
    apiCalls: {
      type: Number,
      default: 0
    },
    dataProcessed: {
      type: Number,
      default: 0
    },
    complianceChecks: {
      type: Number,
      default: 0
    }
  },
  installedAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Compound index to ensure a user can only install a connector once
InstallationSchema.index({ connector: 1, user: 1 }, { unique: true });

module.exports = mongoose.model('Installation', InstallationSchema);
