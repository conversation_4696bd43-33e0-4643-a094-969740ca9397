/**
 * NovaFuse Universal API Connector Model
 * 
 * This model defines the schema for API connectors in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define endpoint schema
const endpointSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  path: { 
    type: String, 
    required: true, 
    trim: true 
  },
  method: { 
    type: String, 
    required: true, 
    enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'], 
    default: 'GET' 
  },
  parameters: [{
    name: { type: String, required: true },
    description: { type: String },
    type: { 
      type: String, 
      enum: ['string', 'number', 'boolean', 'object', 'array'], 
      default: 'string' 
    },
    required: { type: Boolean, default: false },
    location: { 
      type: String, 
      enum: ['path', 'query', 'header', 'body'], 
      default: 'query' 
    },
    default: { type: Schema.Types.Mixed },
    enum: [{ type: Schema.Types.Mixed }]
  }],
  requestBody: {
    type: { 
      type: String, 
      enum: ['json', 'form', 'multipart', 'text', 'binary', 'none'], 
      default: 'json' 
    },
    schema: { type: Object }
  },
  responses: [{
    statusCode: { type: Number, required: true },
    description: { type: String },
    schema: { type: Object }
  }],
  authentication: {
    required: { type: Boolean, default: true },
    scopes: [{ type: String }]
  },
  rateLimiting: {
    enabled: { type: Boolean, default: false },
    limit: { type: Number, default: 100 },
    window: { type: Number, default: 60 } // in seconds
  },
  caching: {
    enabled: { type: Boolean, default: false },
    ttl: { type: Number, default: 300 } // in seconds
  },
  metadata: { 
    type: Object 
  }
}, {
  _id: false
});

// Define authentication schema
const authenticationSchema = new Schema({
  type: { 
    type: String, 
    required: true, 
    enum: ['API_KEY', 'BASIC', 'OAUTH2', 'JWT', 'AWS_SIG_V4', 'CUSTOM'], 
    default: 'API_KEY' 
  },
  fields: { 
    type: Object, 
    default: {} 
  },
  oauth2Config: {
    authorizationUrl: { type: String },
    tokenUrl: { type: String },
    refreshUrl: { type: String },
    scopes: [{ type: String }],
    grantType: { 
      type: String, 
      enum: ['client_credentials', 'authorization_code', 'password', 'implicit'] 
    }
  },
  location: { 
    type: String, 
    enum: ['header', 'query', 'body'], 
    default: 'header' 
  },
  headerName: { 
    type: String, 
    default: 'Authorization' 
  },
  queryParamName: { 
    type: String 
  },
  bodyParamName: { 
    type: String 
  },
  prefix: { 
    type: String 
  }
}, {
  _id: false
});

// Define error handling schema
const errorHandlingSchema = new Schema({
  retryCount: { 
    type: Number, 
    default: 3 
  },
  retryDelay: { 
    type: Number, 
    default: 1000 
  }, // in milliseconds
  timeout: { 
    type: Number, 
    default: 30000 
  }, // in milliseconds
  errorMapping: [{ 
    statusCode: { type: Number },
    errorCode: { type: String },
    message: { type: String },
    retryable: { type: Boolean, default: false }
  }]
}, {
  _id: false
});

// Define rate limiting schema
const rateLimitingSchema = new Schema({
  requestsPerSecond: { 
    type: Number, 
    default: 10 
  },
  burst: { 
    type: Number, 
    default: 20 
  },
  strategy: { 
    type: String, 
    enum: ['fixed', 'sliding', 'token_bucket'], 
    default: 'token_bucket' 
  }
}, {
  _id: false
});

// Define logging schema
const loggingSchema = new Schema({
  level: { 
    type: String, 
    enum: ['error', 'warn', 'info', 'debug', 'trace'], 
    default: 'info' 
  },
  format: { 
    type: String, 
    enum: ['json', 'text'], 
    default: 'json' 
  },
  maskSensitiveData: { 
    type: Boolean, 
    default: true 
  },
  sensitiveFields: [{ 
    type: String 
  }]
}, {
  _id: false
});

// Define connector schema
const connectorSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  version: { 
    type: String, 
    required: true, 
    trim: true 
  },
  category: { 
    type: String, 
    required: true, 
    trim: true 
  },
  tags: [{ 
    type: String, 
    trim: true 
  }],
  baseUrl: { 
    type: String, 
    required: true, 
    trim: true 
  },
  authentication: { 
    type: authenticationSchema, 
    default: () => ({}) 
  },
  endpoints: [endpointSchema],
  errorHandling: { 
    type: errorHandlingSchema, 
    default: () => ({}) 
  },
  rateLimiting: { 
    type: rateLimitingSchema, 
    default: () => ({}) 
  },
  logging: { 
    type: loggingSchema, 
    default: () => ({}) 
  },
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'deprecated'], 
    default: 'active' 
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add method to get endpoint by name
connectorSchema.methods.getEndpointByName = function(name) {
  return this.endpoints.find(endpoint => endpoint.name === name);
};

// Add method to get endpoint by path and method
connectorSchema.methods.getEndpointByPathAndMethod = function(path, method) {
  return this.endpoints.find(endpoint => 
    endpoint.path === path && endpoint.method === method
  );
};

// Add method to check if connector is active
connectorSchema.methods.isActive = function() {
  return this.status === 'active';
};

// Add method to check if connector is deprecated
connectorSchema.methods.isDeprecated = function() {
  return this.status === 'deprecated';
};

// Add indexes
connectorSchema.index({ name: 1, version: 1 }, { unique: true });
connectorSchema.index({ category: 1 });
connectorSchema.index({ tags: 1 });
connectorSchema.index({ status: 1 });
connectorSchema.index({ createdBy: 1 });

// Create model
const Connector = mongoose.model('Connector', connectorSchema);

module.exports = Connector;
