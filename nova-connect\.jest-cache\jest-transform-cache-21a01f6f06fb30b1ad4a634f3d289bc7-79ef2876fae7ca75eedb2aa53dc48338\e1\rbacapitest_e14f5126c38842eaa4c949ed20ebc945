781ac0c7268b3ebd3cacdae5a9b580ea
/**
 * RBAC API Integration Tests
 * 
 * These tests verify that the RBAC API endpoints work correctly.
 */

const request = require('supertest');
const mongoose = require('mongoose');
const {
  MongoMemoryServer
} = require('mongodb-memory-server');
const app = require('../../app');
const User = require('../../api/models/User');
const Role = require('../../api/models/Role');
const Permission = require('../../api/models/Permission');
const UserRole = require('../../api/models/UserRole');
const {
  hashPassword
} = require('../../api/utils/auth');
let mongoServer;
let adminToken;
let userToken;
let adminUser;
let regularUser;
let testRole;
let testPermission;

/**
 * Setup test environment before all tests
 */
beforeAll(async () => {
  // Set up in-memory MongoDB
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });

  // Create test users
  adminUser = new User({
    username: 'admin',
    email: '<EMAIL>',
    password: await hashPassword('password123'),
    firstName: 'Admin',
    lastName: 'User',
    isAdmin: true
  });
  regularUser = new User({
    username: 'user',
    email: '<EMAIL>',
    password: await hashPassword('password123'),
    firstName: 'Regular',
    lastName: 'User',
    isAdmin: false
  });
  await adminUser.save();
  await regularUser.save();

  // Create test permission
  testPermission = new Permission({
    name: 'Test Permission',
    description: 'Test permission for integration tests',
    resource: 'test',
    action: 'read',
    isSystem: false
  });
  await testPermission.save();

  // Create test role
  testRole = new Role({
    name: 'Test Role',
    description: 'Test role for integration tests',
    permissions: [testPermission._id],
    isSystem: false,
    isDefault: false
  });
  await testRole.save();

  // Assign role to regular user
  const userRole = new UserRole({
    userId: regularUser._id,
    roleId: testRole._id
  });
  await userRole.save();

  // Get authentication tokens
  const adminLoginResponse = await request(app).post('/api/auth/login').send({
    email: '<EMAIL>',
    password: 'password123'
  });
  const userLoginResponse = await request(app).post('/api/auth/login').send({
    email: '<EMAIL>',
    password: 'password123'
  });
  adminToken = adminLoginResponse.body.token;
  userToken = userLoginResponse.body.token;
});

/**
 * Clean up after all tests
 */
afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

/**
 * Role API Tests
 */
describe('Role API', () => {
  let createdRoleId;
  test('GET /api/rbac/roles should return all roles', async () => {
    const response = await request(app).get('/api/rbac/roles').set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body.find(role => role.name === 'Test Role')).toBeTruthy();
  });
  test('GET /api/rbac/roles/:id should return a specific role', async () => {
    const response = await request(app).get(`/api/rbac/roles/${testRole._id}`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Test Role');
    expect(response.body.description).toBe('Test role for integration tests');
    expect(Array.isArray(response.body.permissions)).toBe(true);
  });
  test('POST /api/rbac/roles should create a new role', async () => {
    const newRole = {
      name: 'New Test Role',
      description: 'New test role created via API',
      permissions: [testPermission._id.toString()]
    };
    const response = await request(app).post('/api/rbac/roles').set('Authorization', `Bearer ${adminToken}`).send(newRole);
    expect(response.status).toBe(201);
    expect(response.body.name).toBe('New Test Role');
    expect(response.body.description).toBe('New test role created via API');
    expect(Array.isArray(response.body.permissions)).toBe(true);
    createdRoleId = response.body._id;
  });
  test('POST /api/rbac/roles should handle string permission format', async () => {
    const newRole = {
      name: 'String Permission Role',
      description: 'Role with string permission format',
      permissions: ['test:read']
    };
    const response = await request(app).post('/api/rbac/roles').set('Authorization', `Bearer ${adminToken}`).send(newRole);
    expect(response.status).toBe(201);
    expect(response.body.name).toBe('String Permission Role');
    expect(Array.isArray(response.body.permissions)).toBe(true);
  });
  test('PUT /api/rbac/roles/:id should update a role', async () => {
    const updatedRole = {
      name: 'Updated Test Role',
      description: 'Updated test role via API'
    };
    const response = await request(app).put(`/api/rbac/roles/${createdRoleId}`).set('Authorization', `Bearer ${adminToken}`).send(updatedRole);
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Updated Test Role');
    expect(response.body.description).toBe('Updated test role via API');
  });
  test('DELETE /api/rbac/roles/:id should delete a role', async () => {
    const response = await request(app).delete(`/api/rbac/roles/${createdRoleId}`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);

    // Verify role is deleted
    const getResponse = await request(app).get(`/api/rbac/roles/${createdRoleId}`).set('Authorization', `Bearer ${adminToken}`);
    expect(getResponse.status).toBe(404);
  });
});

/**
 * Permission API Tests
 */
describe('Permission API', () => {
  let createdPermissionId;
  test('GET /api/rbac/permissions should return all permissions', async () => {
    const response = await request(app).get('/api/rbac/permissions').set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body.find(perm => perm.name === 'Test Permission')).toBeTruthy();
  });
  test('GET /api/rbac/permissions/:id should return a specific permission', async () => {
    const response = await request(app).get(`/api/rbac/permissions/${testPermission._id}`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Test Permission');
    expect(response.body.resource).toBe('test');
    expect(response.body.action).toBe('read');
  });
  test('POST /api/rbac/permissions should create a new permission', async () => {
    const newPermission = {
      name: 'New Test Permission',
      description: 'New test permission created via API',
      resource: 'test',
      action: 'write'
    };
    const response = await request(app).post('/api/rbac/permissions').set('Authorization', `Bearer ${adminToken}`).send(newPermission);
    expect(response.status).toBe(201);
    expect(response.body.name).toBe('New Test Permission');
    expect(response.body.resource).toBe('test');
    expect(response.body.action).toBe('write');
    createdPermissionId = response.body._id;
  });
  test('PUT /api/rbac/permissions/:id should update a permission', async () => {
    const updatedPermission = {
      name: 'Updated Test Permission',
      description: 'Updated test permission via API'
    };
    const response = await request(app).put(`/api/rbac/permissions/${createdPermissionId}`).set('Authorization', `Bearer ${adminToken}`).send(updatedPermission);
    expect(response.status).toBe(200);
    expect(response.body.name).toBe('Updated Test Permission');
    expect(response.body.description).toBe('Updated test permission via API');
  });
  test('DELETE /api/rbac/permissions/:id should delete a permission', async () => {
    const response = await request(app).delete(`/api/rbac/permissions/${createdPermissionId}`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);

    // Verify permission is deleted
    const getResponse = await request(app).get(`/api/rbac/permissions/${createdPermissionId}`).set('Authorization', `Bearer ${adminToken}`);
    expect(getResponse.status).toBe(404);
  });
});

/**
 * User Role API Tests
 */
describe('User Role API', () => {
  test('GET /api/rbac/users/:userId/roles should return user roles', async () => {
    const response = await request(app).get(`/api/rbac/users/${regularUser._id}/roles`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBe(1);
    expect(response.body[0].name).toBe('Test Role');
  });
  test('POST /api/rbac/users/:userId/roles should assign role to user', async () => {
    // Create a new role to assign
    const newRole = new Role({
      name: 'Assign Test Role',
      description: 'Role to test assignment',
      permissions: [testPermission._id],
      isSystem: false,
      isDefault: false
    });
    await newRole.save();
    const response = await request(app).post(`/api/rbac/users/${regularUser._id}/roles`).set('Authorization', `Bearer ${adminToken}`).send({
      roleId: newRole._id.toString()
    });
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);

    // Verify role was assigned
    const getRolesResponse = await request(app).get(`/api/rbac/users/${regularUser._id}/roles`).set('Authorization', `Bearer ${adminToken}`);
    expect(getRolesResponse.body.length).toBe(2);
    expect(getRolesResponse.body.find(role => role.name === 'Assign Test Role')).toBeTruthy();
  });
  test('DELETE /api/rbac/users/:userId/roles/:roleId should remove role from user', async () => {
    const role = await Role.findOne({
      name: 'Assign Test Role'
    });
    const response = await request(app).delete(`/api/rbac/users/${regularUser._id}/roles/${role._id}`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(response.body.success).toBe(true);

    // Verify role was removed
    const getRolesResponse = await request(app).get(`/api/rbac/users/${regularUser._id}/roles`).set('Authorization', `Bearer ${adminToken}`);
    expect(getRolesResponse.body.length).toBe(1);
    expect(getRolesResponse.body.find(r => r.name === 'Assign Test Role')).toBeFalsy();
  });
  test('GET /api/rbac/users/:userId/permissions should return user permissions', async () => {
    const response = await request(app).get(`/api/rbac/users/${regularUser._id}/permissions`).set('Authorization', `Bearer ${adminToken}`);
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body)).toBe(true);
    expect(response.body.length).toBeGreaterThan(0);
    expect(response.body.find(perm => perm.resource === 'test' && perm.action === 'read')).toBeTruthy();
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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