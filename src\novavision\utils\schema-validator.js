/**
 * NovaVision - Schema Validator
 * 
 * This utility provides schema validation capabilities.
 */

const { createLogger } = require('../../utils/logger');

const logger = createLogger('schema-validator');

/**
 * Validate form schema
 * 
 * @param {Object} schema - Form schema to validate
 * @returns {Object} - Validation result
 */
function validateFormSchema(schema) {
  logger.debug('Validating form schema', { formId: schema.id });
  
  const errors = [];
  
  // Validate required properties
  if (!schema.id) {
    errors.push('Form schema must have an id');
  }
  
  if (!schema.type || schema.type !== 'form') {
    errors.push('Form schema must have type "form"');
  }
  
  if (!schema.title) {
    errors.push('Form schema must have a title');
  }
  
  // Validate sections
  if (schema.sections) {
    if (!Array.isArray(schema.sections)) {
      errors.push('Form sections must be an array');
    } else {
      schema.sections.forEach((section, index) => {
        const sectionErrors = validateFormSection(section);
        
        if (sectionErrors.length > 0) {
          errors.push(`Section at index ${index} has errors: ${sectionErrors.join(', ')}`);
        }
      });
    }
  }
  
  // Validate actions
  if (schema.actions) {
    if (!Array.isArray(schema.actions)) {
      errors.push('Form actions must be an array');
    } else {
      schema.actions.forEach((action, index) => {
        const actionErrors = validateFormAction(action);
        
        if (actionErrors.length > 0) {
          errors.push(`Action at index ${index} has errors: ${actionErrors.join(', ')}`);
        }
      });
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate form section
 * 
 * @param {Object} section - Form section to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateFormSection(section) {
  const errors = [];
  
  // Validate required properties
  if (!section.id) {
    errors.push('Section must have an id');
  }
  
  // Validate fields
  if (section.fields) {
    if (!Array.isArray(section.fields)) {
      errors.push('Section fields must be an array');
    } else {
      section.fields.forEach((field, index) => {
        const fieldErrors = validateFormField(field);
        
        if (fieldErrors.length > 0) {
          errors.push(`Field at index ${index} has errors: ${fieldErrors.join(', ')}`);
        }
      });
    }
  }
  
  return errors;
}

/**
 * Validate form field
 * 
 * @param {Object} field - Form field to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateFormField(field) {
  const errors = [];
  
  // Validate required properties
  if (!field.id) {
    errors.push('Field must have an id');
  }
  
  if (!field.type) {
    errors.push('Field must have a type');
  }
  
  // Validate type-specific properties
  switch (field.type) {
    case 'text':
    case 'password':
    case 'email':
    case 'url':
    case 'tel':
      // Validate min/max length
      if (field.minLength !== undefined && field.maxLength !== undefined && field.minLength > field.maxLength) {
        errors.push('Field minLength cannot be greater than maxLength');
      }
      break;
      
    case 'number':
    case 'range':
      // Validate min/max
      if (field.min !== undefined && field.max !== undefined && field.min > field.max) {
        errors.push('Field min cannot be greater than max');
      }
      break;
      
    case 'select':
    case 'radio':
    case 'checkbox':
      // Validate options
      if (!field.options || !Array.isArray(field.options) || field.options.length === 0) {
        errors.push('Field of type select/radio/checkbox must have options array');
      }
      break;
      
    case 'group':
      // Validate nested fields
      if (!field.fields || !Array.isArray(field.fields)) {
        errors.push('Field of type group must have fields array');
      } else {
        field.fields.forEach((nestedField, index) => {
          const nestedFieldErrors = validateFormField(nestedField);
          
          if (nestedFieldErrors.length > 0) {
            errors.push(`Nested field at index ${index} has errors: ${nestedFieldErrors.join(', ')}`);
          }
        });
      }
      break;
  }
  
  return errors;
}

/**
 * Validate form action
 * 
 * @param {Object} action - Form action to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateFormAction(action) {
  const errors = [];
  
  // Validate required properties
  if (!action.id) {
    errors.push('Action must have an id');
  }
  
  if (!action.type) {
    errors.push('Action must have a type');
  }
  
  if (!action.label) {
    errors.push('Action must have a label');
  }
  
  // Validate type-specific properties
  switch (action.type) {
    case 'submit':
      // No additional validation needed
      break;
      
    case 'button':
      // No additional validation needed
      break;
      
    case 'link':
      if (!action.url) {
        errors.push('Action of type link must have a url');
      }
      break;
      
    default:
      errors.push(`Unknown action type: ${action.type}`);
  }
  
  return errors;
}

/**
 * Validate dashboard schema
 * 
 * @param {Object} schema - Dashboard schema to validate
 * @returns {Object} - Validation result
 */
function validateDashboardSchema(schema) {
  logger.debug('Validating dashboard schema', { dashboardId: schema.id });
  
  const errors = [];
  
  // Validate required properties
  if (!schema.id) {
    errors.push('Dashboard schema must have an id');
  }
  
  if (!schema.type || schema.type !== 'dashboard') {
    errors.push('Dashboard schema must have type "dashboard"');
  }
  
  if (!schema.title) {
    errors.push('Dashboard schema must have a title');
  }
  
  // Validate sections
  if (schema.sections) {
    if (!Array.isArray(schema.sections)) {
      errors.push('Dashboard sections must be an array');
    } else {
      schema.sections.forEach((section, index) => {
        const sectionErrors = validateDashboardSection(section);
        
        if (sectionErrors.length > 0) {
          errors.push(`Section at index ${index} has errors: ${sectionErrors.join(', ')}`);
        }
      });
    }
  }
  
  // Validate filters
  if (schema.filters) {
    if (!Array.isArray(schema.filters)) {
      errors.push('Dashboard filters must be an array');
    } else {
      schema.filters.forEach((filter, index) => {
        const filterErrors = validateDashboardFilter(filter);
        
        if (filterErrors.length > 0) {
          errors.push(`Filter at index ${index} has errors: ${filterErrors.join(', ')}`);
        }
      });
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate dashboard section
 * 
 * @param {Object} section - Dashboard section to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateDashboardSection(section) {
  const errors = [];
  
  // Validate required properties
  if (!section.id) {
    errors.push('Section must have an id');
  }
  
  // Validate widgets
  if (section.widgets) {
    if (!Array.isArray(section.widgets)) {
      errors.push('Section widgets must be an array');
    } else {
      section.widgets.forEach((widget, index) => {
        const widgetErrors = validateDashboardWidget(widget);
        
        if (widgetErrors.length > 0) {
          errors.push(`Widget at index ${index} has errors: ${widgetErrors.join(', ')}`);
        }
      });
    }
  }
  
  return errors;
}

/**
 * Validate dashboard widget
 * 
 * @param {Object} widget - Dashboard widget to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateDashboardWidget(widget) {
  const errors = [];
  
  // Validate required properties
  if (!widget.id) {
    errors.push('Widget must have an id');
  }
  
  if (!widget.type) {
    errors.push('Widget must have a type');
  }
  
  // Validate type-specific properties
  switch (widget.type) {
    case 'chart':
      if (!widget.chartConfig) {
        errors.push('Widget of type chart must have chartConfig');
      } else if (!widget.chartConfig.type) {
        errors.push('Chart widget must have a chart type');
      }
      break;
      
    case 'table':
      if (!widget.tableConfig) {
        errors.push('Widget of type table must have tableConfig');
      }
      break;
      
    case 'metric':
      if (!widget.metricConfig) {
        errors.push('Widget of type metric must have metricConfig');
      }
      break;
      
    case 'list':
      if (!widget.listConfig) {
        errors.push('Widget of type list must have listConfig');
      }
      break;
      
    case 'map':
      if (!widget.mapConfig) {
        errors.push('Widget of type map must have mapConfig');
      }
      break;
      
    default:
      errors.push(`Unknown widget type: ${widget.type}`);
  }
  
  return errors;
}

/**
 * Validate dashboard filter
 * 
 * @param {Object} filter - Dashboard filter to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateDashboardFilter(filter) {
  const errors = [];
  
  // Validate required properties
  if (!filter.id) {
    errors.push('Filter must have an id');
  }
  
  if (!filter.type) {
    errors.push('Filter must have a type');
  }
  
  if (!filter.label) {
    errors.push('Filter must have a label');
  }
  
  // Validate type-specific properties
  switch (filter.type) {
    case 'select':
      if (!filter.options || !Array.isArray(filter.options) || filter.options.length === 0) {
        errors.push('Filter of type select must have options array');
      }
      break;
      
    case 'date':
    case 'daterange':
      // No additional validation needed
      break;
      
    case 'range':
      if (filter.min !== undefined && filter.max !== undefined && filter.min > filter.max) {
        errors.push('Filter min cannot be greater than max');
      }
      break;
      
    default:
      errors.push(`Unknown filter type: ${filter.type}`);
  }
  
  return errors;
}

/**
 * Validate report schema
 * 
 * @param {Object} schema - Report schema to validate
 * @returns {Object} - Validation result
 */
function validateReportSchema(schema) {
  logger.debug('Validating report schema', { reportId: schema.id });
  
  const errors = [];
  
  // Validate required properties
  if (!schema.id) {
    errors.push('Report schema must have an id');
  }
  
  if (!schema.type || schema.type !== 'report') {
    errors.push('Report schema must have type "report"');
  }
  
  if (!schema.title) {
    errors.push('Report schema must have a title');
  }
  
  // Validate sections
  if (schema.sections) {
    if (!Array.isArray(schema.sections)) {
      errors.push('Report sections must be an array');
    } else {
      schema.sections.forEach((section, index) => {
        const sectionErrors = validateReportSection(section);
        
        if (sectionErrors.length > 0) {
          errors.push(`Section at index ${index} has errors: ${sectionErrors.join(', ')}`);
        }
      });
    }
  }
  
  // Validate parameters
  if (schema.parameters) {
    if (!Array.isArray(schema.parameters)) {
      errors.push('Report parameters must be an array');
    } else {
      schema.parameters.forEach((parameter, index) => {
        const parameterErrors = validateReportParameter(parameter);
        
        if (parameterErrors.length > 0) {
          errors.push(`Parameter at index ${index} has errors: ${parameterErrors.join(', ')}`);
        }
      });
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Validate report section
 * 
 * @param {Object} section - Report section to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateReportSection(section) {
  const errors = [];
  
  // Validate required properties
  if (!section.id) {
    errors.push('Section must have an id');
  }
  
  // Validate elements
  if (section.elements) {
    if (!Array.isArray(section.elements)) {
      errors.push('Section elements must be an array');
    } else {
      section.elements.forEach((element, index) => {
        const elementErrors = validateReportElement(element);
        
        if (elementErrors.length > 0) {
          errors.push(`Element at index ${index} has errors: ${elementErrors.join(', ')}`);
        }
      });
    }
  }
  
  return errors;
}

/**
 * Validate report element
 * 
 * @param {Object} element - Report element to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateReportElement(element) {
  const errors = [];
  
  // Validate required properties
  if (!element.id) {
    errors.push('Element must have an id');
  }
  
  if (!element.type) {
    errors.push('Element must have a type');
  }
  
  // Validate type-specific properties
  switch (element.type) {
    case 'text':
      if (!element.textConfig) {
        errors.push('Element of type text must have textConfig');
      } else if (!element.textConfig.content) {
        errors.push('Text element must have content');
      }
      break;
      
    case 'chart':
      if (!element.chartConfig) {
        errors.push('Element of type chart must have chartConfig');
      } else if (!element.chartConfig.type) {
        errors.push('Chart element must have a chart type');
      }
      break;
      
    case 'table':
      if (!element.tableConfig) {
        errors.push('Element of type table must have tableConfig');
      }
      break;
      
    case 'image':
      if (!element.imageConfig) {
        errors.push('Element of type image must have imageConfig');
      } else if (!element.imageConfig.src) {
        errors.push('Image element must have src');
      }
      break;
      
    case 'page-break':
      // No additional validation needed
      break;
      
    default:
      errors.push(`Unknown element type: ${element.type}`);
  }
  
  return errors;
}

/**
 * Validate report parameter
 * 
 * @param {Object} parameter - Report parameter to validate
 * @returns {Array} - Validation errors
 * @private
 */
function validateReportParameter(parameter) {
  const errors = [];
  
  // Validate required properties
  if (!parameter.id) {
    errors.push('Parameter must have an id');
  }
  
  if (!parameter.type) {
    errors.push('Parameter must have a type');
  }
  
  if (!parameter.label) {
    errors.push('Parameter must have a label');
  }
  
  // Validate type-specific properties
  switch (parameter.type) {
    case 'text':
      // No additional validation needed
      break;
      
    case 'select':
      if (!parameter.options || !Array.isArray(parameter.options) || parameter.options.length === 0) {
        errors.push('Parameter of type select must have options array');
      }
      break;
      
    case 'date':
      // No additional validation needed
      break;
      
    default:
      errors.push(`Unknown parameter type: ${parameter.type}`);
  }
  
  return errors;
}

module.exports = {
  validateFormSchema,
  validateDashboardSchema,
  validateReportSchema
};
