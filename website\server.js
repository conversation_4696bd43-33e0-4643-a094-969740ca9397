const express = require('express');
const path = require('path');
const fs = require('fs');
const app = express();
const port = 3000;

// Serve static files from the current directory
app.use(express.static(__dirname));

// Serve index.html for the root route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Handle component includes
app.get('/components/:component', (req, res) => {
  const componentPath = path.join(__dirname, 'components', req.params.component);
  if (fs.existsSync(componentPath)) {
    res.sendFile(componentPath);
  } else {
    res.status(404).send('Component not found');
  }
});

// Handle 404s
app.use((req, res) => {
  res.status(404).sendFile(path.join(__dirname, '404.html'));
});

// Start the server
app.listen(port, () => {
  console.log(`NovaFuse API Superstore website running at http://localhost:${port}`);
});

