/**
 * Tensor Health Dashboard
 * 
 * This module provides a real-time dashboard for monitoring tensor health,
 * entropy, drift, and healing metrics.
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

/**
 * Tensor Health Dashboard class
 */
class TensorHealthDashboard extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Dashboard options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      updateInterval: 5000, // Update interval in milliseconds
      maxHistoryPoints: 100, // Maximum number of history points to keep
      dashboardDir: path.join(__dirname, '../dashboard_output'),
      autoRefresh: true, // Whether to auto-refresh the dashboard
      ...options
    };
    
    this.tensors = new Map();
    this.metrics = new Map();
    this.forecasts = new Map();
    this.healingHistory = new Map();
    this.driftHistory = new Map();
    this.thresholdHistory = new Map();
    
    // Create dashboard directory if it doesn't exist
    if (!fs.existsSync(this.options.dashboardDir)) {
      fs.mkdirSync(this.options.dashboardDir, { recursive: true });
    }
    
    // Start auto-refresh if enabled
    if (this.options.autoRefresh) {
      this.startAutoRefresh();
    }
  }
  
  /**
   * Register a tensor for monitoring
   * @param {string} id - Tensor ID
   * @param {Object} tensor - Tensor to monitor
   * @param {Object} selfHealer - Self-healing tensor manager
   * @param {Object} driftAnalyzer - Time drift analyzer
   * @returns {Object} - Registered tensor
   */
  registerTensor(id, tensor, selfHealer, driftAnalyzer) {
    // Store tensor
    this.tensors.set(id, {
      tensor,
      selfHealer,
      driftAnalyzer,
      registeredAt: Date.now()
    });
    
    // Initialize metrics
    this.metrics.set(id, {
      health: [{ timestamp: Date.now(), value: tensor.health }],
      entropy: [{ timestamp: Date.now(), value: tensor.entropyContainment }],
      drift: [],
      healing: []
    });
    
    // Register event listeners
    this._registerEventListeners(id, selfHealer, driftAnalyzer);
    
    // Generate initial dashboard
    this.generateDashboard(id);
    
    this.emit('tensor-registered', { id });
    
    return tensor;
  }
  
  /**
   * Register event listeners
   * @param {string} id - Tensor ID
   * @param {Object} selfHealer - Self-healing tensor manager
   * @param {Object} driftAnalyzer - Time drift analyzer
   * @private
   */
  _registerEventListeners(id, selfHealer, driftAnalyzer) {
    // Listen for healing events
    selfHealer.on('tensor-healed', (data) => {
      if (data.id === id) {
        // Update metrics
        const metrics = this.metrics.get(id);
        metrics.health.push({ timestamp: Date.now(), value: selfHealer.getTensor(id).health });
        metrics.entropy.push({ timestamp: Date.now(), value: selfHealer.getTensor(id).entropyContainment });
        metrics.healing.push({
          timestamp: Date.now(),
          healthImprovement: data.healthImprovement,
          entropyReduction: data.entropyReduction,
          efficiencyScore: data.efficiencyScore
        });
        
        // Trim history if needed
        this._trimHistory(metrics);
        
        // Update healing history
        this.healingHistory.set(id, selfHealer.getHealingHistory(id));
        
        // Update threshold history if available
        if (selfHealer.getThresholdHistory) {
          this.thresholdHistory.set(id, selfHealer.getThresholdHistory(id));
        }
        
        // Generate forecast if available
        if (selfHealer.forecastEntropy) {
          this.forecasts.set(id, selfHealer.forecastEntropy(id));
        }
        
        // Update dashboard
        this.generateDashboard(id);
        
        this.emit('metrics-updated', { id, type: 'healing' });
      }
    });
    
    // Listen for drift events
    driftAnalyzer.on('time-drift-detected', (data) => {
      if (data.id === id) {
        // Update metrics
        const metrics = this.metrics.get(id);
        metrics.drift.push({
          timestamp: Date.now(),
          driftFactor: data.driftFactor,
          driftCompensated: data.driftCompensated
        });
        
        // Trim history if needed
        this._trimHistory(metrics);
        
        // Update drift history
        this.driftHistory.set(id, driftAnalyzer.getDriftHistory(id));
        
        // Update dashboard
        this.generateDashboard(id);
        
        this.emit('metrics-updated', { id, type: 'drift' });
      }
    });
    
    // Listen for threshold adjustment events
    if (selfHealer.on && typeof selfHealer.on === 'function') {
      selfHealer.on('threshold-adjusted', (data) => {
        if (data.id === id) {
          // Update threshold history
          if (selfHealer.getThresholdHistory) {
            this.thresholdHistory.set(id, selfHealer.getThresholdHistory(id));
          }
          
          // Update dashboard
          this.generateDashboard(id);
          
          this.emit('metrics-updated', { id, type: 'threshold' });
        }
      });
    }
  }
  
  /**
   * Trim history to maximum length
   * @param {Object} metrics - Metrics object
   * @private
   */
  _trimHistory(metrics) {
    const maxPoints = this.options.maxHistoryPoints;
    
    if (metrics.health.length > maxPoints) {
      metrics.health = metrics.health.slice(-maxPoints);
    }
    
    if (metrics.entropy.length > maxPoints) {
      metrics.entropy = metrics.entropy.slice(-maxPoints);
    }
    
    if (metrics.drift.length > maxPoints) {
      metrics.drift = metrics.drift.slice(-maxPoints);
    }
    
    if (metrics.healing.length > maxPoints) {
      metrics.healing = metrics.healing.slice(-maxPoints);
    }
  }
  
  /**
   * Start auto-refresh
   */
  startAutoRefresh() {
    this.refreshInterval = setInterval(() => {
      // Update all dashboards
      for (const id of this.tensors.keys()) {
        this.generateDashboard(id);
      }
    }, this.options.updateInterval);
  }
  
  /**
   * Stop auto-refresh
   */
  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }
  
  /**
   * Generate dashboard for a tensor
   * @param {string} id - Tensor ID
   * @returns {string} - Dashboard HTML
   */
  generateDashboard(id) {
    const tensorData = this.tensors.get(id);
    const metrics = this.metrics.get(id);
    const forecast = this.forecasts.get(id);
    
    if (!tensorData || !metrics) {
      throw new Error(`Tensor not found: ${id}`);
    }
    
    // Generate HTML
    const html = this._generateDashboardHTML(id, tensorData, metrics, forecast);
    
    // Write to file
    const dashboardPath = path.join(this.options.dashboardDir, `tensor-dashboard-${id}.html`);
    fs.writeFileSync(dashboardPath, html);
    
    return dashboardPath;
  }
  
  /**
   * Generate dashboard HTML
   * @param {string} id - Tensor ID
   * @param {Object} tensorData - Tensor data
   * @param {Object} metrics - Metrics data
   * @param {Object} forecast - Entropy forecast
   * @returns {string} - Dashboard HTML
   * @private
   */
  _generateDashboardHTML(id, tensorData, metrics, forecast) {
    // Current tensor state
    const tensor = tensorData.tensor;
    
    // Format timestamps
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString();
    };
    
    // Generate health chart data
    const healthData = metrics.health.map(point => ({
      x: formatTime(point.timestamp),
      y: point.value.toFixed(3)
    }));
    
    // Generate entropy chart data
    const entropyData = metrics.entropy.map(point => ({
      x: formatTime(point.timestamp),
      y: point.value.toFixed(3)
    }));
    
    // Generate drift chart data
    const driftData = metrics.drift.map(point => ({
      x: formatTime(point.timestamp),
      y: point.driftFactor.toFixed(3),
      compensated: point.driftCompensated
    }));
    
    // Generate healing efficiency chart data
    const healingData = metrics.healing.map(point => ({
      x: formatTime(point.timestamp),
      y: point.efficiencyScore ? point.efficiencyScore.combined.toFixed(3) : '0.000'
    }));
    
    // Generate forecast data if available
    let forecastHTML = '';
    if (forecast) {
      forecastHTML = `
        <div class="card">
          <div class="card-header">
            <h5>Entropy Forecast</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <p><strong>Current Entropy:</strong> ${forecast.currentEntropy.toFixed(3)}</p>
                <p><strong>Forecasted Entropy:</strong> ${forecast.forecastedEntropy.toFixed(3)}</p>
                <p><strong>Trend:</strong> ${forecast.trend}</p>
              </div>
              <div class="col-md-6">
                <p><strong>Confidence:</strong> ${(forecast.confidence * 100).toFixed(1)}%</p>
                <p><strong>Forecast Window:</strong> ${forecast.forecastWindow} cycles</p>
                <p><strong>Seasonality Detected:</strong> ${forecast.seasonalityDetected ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>
        </div>
      `;
    }
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Tensor Health Dashboard - ${id}</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background-color: #f8f9fa;
    }
    .dashboard-header {
      margin-bottom: 20px;
    }
    .card {
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
    }
    .metric-label {
      font-size: 14px;
      color: #6c757d;
    }
    .chart-container {
      position: relative;
      height: 250px;
    }
    .status-indicator {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 5px;
    }
    .status-good {
      background-color: #28a745;
    }
    .status-warning {
      background-color: #ffc107;
    }
    .status-critical {
      background-color: #dc3545;
    }
    .auto-refresh {
      font-size: 12px;
      color: #6c757d;
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="dashboard-header">
      <div class="row">
        <div class="col-md-6">
          <h1>Tensor Health Dashboard</h1>
          <p>Tensor ID: ${id}</p>
          <p class="auto-refresh">Auto-refreshes every ${this.options.updateInterval / 1000} seconds</p>
        </div>
        <div class="col-md-6 text-end">
          <p>Last Updated: ${new Date().toLocaleString()}</p>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Health</div>
            <div class="metric-value">
              <span class="status-indicator ${tensor.health > 0.8 ? 'status-good' : tensor.health > 0.6 ? 'status-warning' : 'status-critical'}"></span>
              ${tensor.health.toFixed(3)}
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Entropy Containment</div>
            <div class="metric-value">
              <span class="status-indicator ${tensor.entropyContainment < 0.02 ? 'status-good' : tensor.entropyContainment < 0.04 ? 'status-warning' : 'status-critical'}"></span>
              ${tensor.entropyContainment.toFixed(3)}
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Healing Cycles</div>
            <div class="metric-value">${tensor.healingCycles || 0}</div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card">
          <div class="card-body text-center">
            <div class="metric-label">Domain</div>
            <div class="metric-value">${tensor.domain}</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Health History</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="healthChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Entropy History</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="entropyChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Drift History</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="driftChart"></canvas>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h5>Healing Efficiency</h5>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="healingChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    ${forecastHTML}
    
  </div>
  
  <script>
    // Health Chart
    const healthCtx = document.getElementById('healthChart').getContext('2d');
    new Chart(healthCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Health',
          data: ${JSON.stringify(healthData)},
          borderColor: 'rgba(40, 167, 69, 1)',
          backgroundColor: 'rgba(40, 167, 69, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });
    
    // Entropy Chart
    const entropyCtx = document.getElementById('entropyChart').getContext('2d');
    new Chart(entropyCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Entropy Containment',
          data: ${JSON.stringify(entropyData)},
          borderColor: 'rgba(220, 53, 69, 1)',
          backgroundColor: 'rgba(220, 53, 69, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 0.05
          }
        }
      }
    });
    
    // Drift Chart
    const driftCtx = document.getElementById('driftChart').getContext('2d');
    new Chart(driftCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Drift Factor',
          data: ${JSON.stringify(driftData)},
          borderColor: 'rgba(255, 193, 7, 1)',
          backgroundColor: 'rgba(255, 193, 7, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true
          }
        }
      }
    });
    
    // Healing Chart
    const healingCtx = document.getElementById('healingChart').getContext('2d');
    new Chart(healingCtx, {
      type: 'line',
      data: {
        datasets: [{
          label: 'Healing Efficiency',
          data: ${JSON.stringify(healingData)},
          borderColor: 'rgba(13, 110, 253, 1)',
          backgroundColor: 'rgba(13, 110, 253, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 1
          }
        }
      }
    });
    
    // Auto-refresh
    setTimeout(() => {
      location.reload();
    }, ${this.options.updateInterval});
  </script>
</body>
</html>
    `;
  }
}

module.exports = TensorHealthDashboard;
