{"version": 3, "names": ["UAConnectorError", "require", "ConnectionError", "constructor", "message", "options", "code", "severity", "context", "cause", "getUserMessage", "TimeoutError", "NetworkError", "ServiceUnavailableError", "DnsResolutionError", "module", "exports"], "sources": ["connection-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - Connection Error\n * \n * This module defines connection-related errors for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\n\n/**\n * Error class for connection failures\n * @class ConnectionError\n * @extends UAConnectorError\n */\nclass ConnectionError extends UAConnectorError {\n  /**\n   * Create a new ConnectionError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   */\n  constructor(message, options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTION_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Failed to connect to the service. Please check your network connection and try again.';\n  }\n}\n\n/**\n * Error class for timeout errors\n * @class TimeoutError\n * @extends ConnectionError\n */\nclass TimeoutError extends ConnectionError {\n  /**\n   * Create a new TimeoutError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'The request timed out', options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTION_TIMEOUT',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'The request timed out. Please try again later or contact support if the issue persists.';\n  }\n}\n\n/**\n * Error class for network errors\n * @class NetworkError\n * @extends ConnectionError\n */\nclass NetworkError extends ConnectionError {\n  /**\n   * Create a new NetworkError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'A network error occurred', options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTION_NETWORK_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'A network error occurred. Please check your internet connection and try again.';\n  }\n}\n\n/**\n * Error class for service unavailable errors\n * @class ServiceUnavailableError\n * @extends ConnectionError\n */\nclass ServiceUnavailableError extends ConnectionError {\n  /**\n   * Create a new ServiceUnavailableError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'The service is currently unavailable', options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTION_SERVICE_UNAVAILABLE',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'The service is currently unavailable. Please try again later.';\n  }\n}\n\n/**\n * Error class for DNS resolution errors\n * @class DnsResolutionError\n * @extends ConnectionError\n */\nclass DnsResolutionError extends ConnectionError {\n  /**\n   * Create a new DnsResolutionError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'Failed to resolve the hostname', options = {}) {\n    super(message, {\n      code: options.code || 'CONNECTION_DNS_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'Failed to connect to the service. The hostname could not be resolved.';\n  }\n}\n\nmodule.exports = {\n  ConnectionError,\n  TimeoutError,\n  NetworkError,\n  ServiceUnavailableError,\n  DnsResolutionError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASF,gBAAgB,CAAC;EAC7C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,kBAAkB;MACxCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,uFAAuF;EAChG;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,SAAST,eAAe,CAAC;EACzC;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,uBAAuB,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3D,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,oBAAoB;MAC1CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,yFAAyF;EAClG;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAME,YAAY,SAASV,eAAe,CAAC;EACzC;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,0BAA0B,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9D,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,0BAA0B;MAChDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,gFAAgF;EACzF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,uBAAuB,SAASX,eAAe,CAAC;EACpD;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,sCAAsC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1E,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,gCAAgC;MACtDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,+DAA+D;EACxE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMI,kBAAkB,SAASZ,eAAe,CAAC;EAC/C;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,gCAAgC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpE,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,sBAAsB;MAC5CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,uEAAuE;EAChF;AACF;AAEAK,MAAM,CAACC,OAAO,GAAG;EACfd,eAAe;EACfS,YAAY;EACZC,YAAY;EACZC,uBAAuB;EACvBC;AACF,CAAC", "ignoreList": []}