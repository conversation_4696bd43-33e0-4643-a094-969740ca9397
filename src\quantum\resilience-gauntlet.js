/**
 * Resilience Gauntlet
 *
 * This module implements the Resilience Gauntlet, a comprehensive testing framework
 * that runs Mathematical Incompatibility Red Team Scenarios against components to
 * verify their resilience against mathematical incompatibility.
 * 
 * The Resilience Gauntlet:
 * 1. Runs all red team scenarios against a target component
 * 2. Generates detailed reports of test results
 * 3. Provides an overall resilience score
 * 4. Is extensible for future test categories
 */

const EventEmitter = require('events');
const {
  MathematicalIncompatibilityScenario,
  InfiniteValueScenario,
  UnboundedRecursionScenario,
  BoundaryViolationScenario,
  CircularReferenceScenario,
  ExcessiveComplexityScenario,
  InfiniteLoopScenario
} = require('./mathematical-incompatibility-scenarios');

/**
 * ResilienceGauntlet class
 * 
 * A comprehensive testing framework for verifying resilience against
 * mathematical incompatibility.
 */
class ResilienceGauntlet extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      parallelExecution: false, // Whether to run scenarios in parallel
      timeoutMs: 10000, // Timeout for scenario execution
      categoriesEnabled: {
        'Infinite Values': true,
        'Unbounded Recursion': true,
        'Boundary Violations': true,
        'Circular References': true,
        'Excessive Complexity': true,
        'Infinite Loops': true
      },
      ...options
    };

    // Initialize scenarios
    this.scenarios = [];
    this.results = [];

    if (this.options.enableLogging) {
      console.log('ResilienceGauntlet initialized with options:', this.options);
    }
  }

  /**
   * Add a scenario to the gauntlet
   * @param {MathematicalIncompatibilityScenario} scenario - Scenario to add
   */
  addScenario(scenario) {
    if (!(scenario instanceof MathematicalIncompatibilityScenario)) {
      throw new Error('Invalid scenario');
    }

    this.scenarios.push(scenario);

    if (this.options.enableLogging) {
      console.log(`Added scenario: ${scenario.name} (${scenario.category})`);
    }
  }

  /**
   * Add multiple scenarios to the gauntlet
   * @param {Array<MathematicalIncompatibilityScenario>} scenarios - Scenarios to add
   */
  addScenarios(scenarios) {
    for (const scenario of scenarios) {
      this.addScenario(scenario);
    }
  }

  /**
   * Run all scenarios against a target
   * @param {Object} target - Target to run scenarios against
   * @returns {Object} - Gauntlet results
   */
  async run(target) {
    if (!target) {
      throw new Error('Invalid target');
    }

    // Reset results
    this.results = [];

    // Filter scenarios by enabled categories
    const enabledScenarios = this.scenarios.filter(scenario => 
      this.options.categoriesEnabled[scenario.category]
    );

    if (this.options.enableLogging) {
      console.log(`Running ${enabledScenarios.length} scenarios against target`);
    }

    // Run scenarios
    if (this.options.parallelExecution) {
      // Run scenarios in parallel
      const promises = enabledScenarios.map(scenario => this.runScenario(scenario, target));
      this.results = await Promise.all(promises);
    } else {
      // Run scenarios sequentially
      for (const scenario of enabledScenarios) {
        const result = await this.runScenario(scenario, target);
        this.results.push(result);
      }
    }

    // Generate report
    const report = this.generateReport();

    this.emit('gauntlet-completed', report);

    return report;
  }

  /**
   * Run a single scenario against a target
   * @param {MathematicalIncompatibilityScenario} scenario - Scenario to run
   * @param {Object} target - Target to run scenario against
   * @returns {Object} - Scenario result
   */
  async runScenario(scenario, target) {
    if (this.options.enableLogging) {
      console.log(`Running scenario: ${scenario.name} (${scenario.category})`);
    }

    this.emit('scenario-started', { name: scenario.name, category: scenario.category });

    try {
      // Run scenario with timeout
      const result = await Promise.race([
        scenario.run(target),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error(`Scenario timed out after ${this.options.timeoutMs}ms`)), this.options.timeoutMs)
        )
      ]);

      this.emit('scenario-completed', result);

      return result;
    } catch (error) {
      const result = {
        name: scenario.name,
        category: scenario.category,
        severity: scenario.severity,
        success: false,
        message: error.message,
        error: error.stack
      };

      this.emit('scenario-error', result);

      return result;
    }
  }

  /**
   * Generate a report of gauntlet results
   * @returns {Object} - Gauntlet report
   */
  generateReport() {
    // Calculate statistics
    const totalScenarios = this.results.length;
    const successfulScenarios = this.results.filter(result => result.success).length;
    const failedScenarios = totalScenarios - successfulScenarios;

    // Calculate success rate by category
    const categoryStats = {};
    for (const result of this.results) {
      if (!categoryStats[result.category]) {
        categoryStats[result.category] = {
          total: 0,
          successful: 0,
          failed: 0
        };
      }

      categoryStats[result.category].total++;
      if (result.success) {
        categoryStats[result.category].successful++;
      } else {
        categoryStats[result.category].failed++;
      }
    }

    // Calculate success rate by severity
    const severityStats = {};
    for (const result of this.results) {
      if (!severityStats[result.severity]) {
        severityStats[result.severity] = {
          total: 0,
          successful: 0,
          failed: 0
        };
      }

      severityStats[result.severity].total++;
      if (result.success) {
        severityStats[result.severity].successful++;
      } else {
        severityStats[result.severity].failed++;
      }
    }

    // Calculate overall resilience score (0-100)
    // Weight by severity: critical = 4, high = 3, medium = 2, low = 1
    const severityWeights = {
      critical: 4,
      high: 3,
      medium: 2,
      low: 1
    };

    let totalWeight = 0;
    let weightedSuccessful = 0;

    for (const result of this.results) {
      const weight = severityWeights[result.severity] || 1;
      totalWeight += weight;
      if (result.success) {
        weightedSuccessful += weight;
      }
    }

    const resilienceScore = totalWeight > 0 
      ? Math.round((weightedSuccessful / totalWeight) * 100) 
      : 0;

    // Generate report
    return {
      timestamp: new Date().toISOString(),
      summary: {
        totalScenarios,
        successfulScenarios,
        failedScenarios,
        successRate: totalScenarios > 0 ? (successfulScenarios / totalScenarios) : 0,
        resilienceScore
      },
      categoryStats,
      severityStats,
      results: this.results
    };
  }

  /**
   * Create default scenarios for testing
   * @returns {Array<MathematicalIncompatibilityScenario>} - Default scenarios
   */
  static createDefaultScenarios() {
    const scenarios = [];

    // Infinite Value scenarios
    scenarios.push(
      new InfiniteValueScenario(
        'Positive Infinity',
        'Tests handling of positive infinity',
        () => Number.POSITIVE_INFINITY,
        'high'
      ),
      new InfiniteValueScenario(
        'Negative Infinity',
        'Tests handling of negative infinity',
        () => Number.NEGATIVE_INFINITY,
        'high'
      ),
      new InfiniteValueScenario(
        'NaN',
        'Tests handling of NaN',
        () => NaN,
        'high'
      ),
      new InfiniteValueScenario(
        'Very Large Number',
        'Tests handling of very large numbers',
        () => Number.MAX_VALUE * 2,
        'medium'
      )
    );

    // Unbounded Recursion scenarios
    scenarios.push(
      new UnboundedRecursionScenario(
        'Simple Recursion',
        'Tests handling of simple recursive function',
        function factorial(n) {
          if (n <= 1) return 1;
          return n * factorial(n - 1);
        },
        [1000], // This would normally cause a stack overflow
        'critical'
      ),
      new UnboundedRecursionScenario(
        'Mutual Recursion',
        'Tests handling of mutually recursive functions',
        function isEven(n) {
          if (n === 0) return true;
          return isOdd(n - 1);
          
          function isOdd(n) {
            if (n === 0) return false;
            return isEven(n - 1);
          }
        },
        [10000], // This would normally cause a stack overflow
        'critical'
      )
    );

    // Boundary Violation scenarios
    scenarios.push(
      new BoundaryViolationScenario(
        'Cyber Domain Overflow',
        'Tests handling of values exceeding cyber domain boundaries',
        1e10, // Very large value
        'cyber',
        'medium'
      ),
      new BoundaryViolationScenario(
        'Financial Domain Overflow',
        'Tests handling of values exceeding financial domain boundaries',
        1e15, // Very large value
        'financial',
        'high'
      ),
      new BoundaryViolationScenario(
        'Medical Domain Overflow',
        'Tests handling of values exceeding medical domain boundaries',
        1e8, // Very large value
        'medical',
        'high'
      )
    );

    // Circular Reference scenarios
    scenarios.push(
      new CircularReferenceScenario(
        'Simple Circular Reference',
        'Tests handling of simple circular reference',
        () => {
          const obj = {};
          obj.self = obj;
          return obj;
        },
        'medium'
      ),
      new CircularReferenceScenario(
        'Complex Circular Reference',
        'Tests handling of complex circular reference',
        () => {
          const obj1 = { name: 'obj1' };
          const obj2 = { name: 'obj2' };
          const obj3 = { name: 'obj3' };
          obj1.next = obj2;
          obj2.next = obj3;
          obj3.next = obj1;
          return obj1;
        },
        'medium'
      )
    );

    // Excessive Complexity scenarios
    scenarios.push(
      new ExcessiveComplexityScenario(
        'Deep Object Nesting',
        'Tests handling of deeply nested objects',
        () => {
          let obj = {};
          let current = obj;
          for (let i = 0; i < 1000; i++) {
            current.next = {};
            current = current.next;
          }
          return obj;
        },
        'medium'
      ),
      new ExcessiveComplexityScenario(
        'Large Array',
        'Tests handling of very large arrays',
        () => Array(1000000).fill(1),
        'medium'
      ),
      new ExcessiveComplexityScenario(
        'Large String',
        'Tests handling of very large strings',
        () => 'a'.repeat(1000000),
        'medium'
      )
    );

    // Infinite Loop scenarios
    scenarios.push(
      new InfiniteLoopScenario(
        'Simple Infinite Loop',
        'Tests handling of simple infinite loop',
        () => true, // Always true condition
        () => 1, // Simple body
        () => {}, // No update
        'critical'
      ),
      new InfiniteLoopScenario(
        'Deceptive Infinite Loop',
        'Tests handling of loop that appears to terminate but does not',
        () => true, // Always true condition
        () => 1, // Simple body
        () => {}, // No update
        'critical'
      )
    );

    return scenarios;
  }
}

module.exports = ResilienceGauntlet;
