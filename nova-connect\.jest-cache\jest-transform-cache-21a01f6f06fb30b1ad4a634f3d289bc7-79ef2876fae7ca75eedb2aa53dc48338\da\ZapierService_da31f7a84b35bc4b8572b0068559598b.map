{"version": 3, "names": ["fs", "require", "promises", "path", "v4", "uuidv4", "jwt", "crypto", "logger", "ZapierService", "constructor", "dataDir", "join", "__dirname", "zapierDir", "zapierAppsFile", "zapierAuthsFile", "zapierTriggersFile", "zapierActionsFile", "jwtSecret", "process", "env", "JWT_SECRET", "jwtExpiresIn", "ZAPIER_JWT_EXPIRES_IN", "clientId", "ZAPIER_CLIENT_ID", "clientSecret", "ZAPIER_CLIENT_SECRET", "randomBytes", "toString", "redirectUri", "ZAPIER_REDIRECT_URI", "ensureDataDir", "mkdir", "recursive", "access", "error", "code", "writeFile", "JSON", "stringify", "loadData", "filePath", "data", "readFile", "parse", "saveData", "getAppDefinition", "title", "description", "version", "platformVersion", "authentication", "type", "oauth2Config", "authorizeUrl", "API_BASE_URL", "tokenUrl", "refreshUrl", "autoRefresh", "scope", "connectionLabel", "beforeApp", "url", "afterApp", "getTriggers", "triggers", "length", "defaultTriggers", "key", "noun", "display", "label", "operation", "perform", "sample", "id", "name", "createdAt", "status", "severity", "resource", "details", "timestamp", "getActions", "actions", "defaultActions", "method", "body", "config", "inputFields", "required", "helpText", "choices", "api", "database", "file", "workflowId", "inputs", "result", "success", "startedAt", "completedAt", "controlId", "evidenceType", "document", "screenshot", "log", "test_result", "attestation", "createAuthorizationUrl", "state", "authUrl", "URL", "searchParams", "append", "generateAccessToken", "userId", "Math", "floor", "random", "accessToken", "sign", "sub", "client_id", "expiresIn", "refreshToken", "auths", "push", "Date", "toISOString", "expiresAt", "now", "access_token", "refresh_token", "token_type", "expires_in", "refreshAccessToken", "authIndex", "findIndex", "auth", "Error", "newRefreshToken", "updatedAt", "verifyAccessToken", "decoded", "verify", "find", "registerApp", "appData", "apps", "app", "getAppById", "appId", "getAllApps", "updateApp", "appIndex", "deleteApp", "splice", "registerTrigger", "triggerData", "trigger", "registerAction", "actionData", "action", "module", "exports"], "sources": ["ZapierService.js"], "sourcesContent": ["/**\n * Zapier Service\n * \n * This service handles Zapier integration for NovaConnect UAC.\n */\n\nconst fs = require('fs').promises;\nconst path = require('path');\nconst { v4: uuidv4 } = require('uuid');\nconst jwt = require('jsonwebtoken');\nconst crypto = require('crypto');\nconst logger = require('../../config/logger');\n\nclass ZapierService {\n  constructor(dataDir = path.join(__dirname, '../../data')) {\n    this.dataDir = dataDir;\n    this.zapierDir = path.join(this.dataDir, 'zapier');\n    this.zapierAppsFile = path.join(this.zapierDir, 'zapier_apps.json');\n    this.zapierAuthsFile = path.join(this.zapierDir, 'zapier_auths.json');\n    this.zapierTriggersFile = path.join(this.zapierDir, 'zapier_triggers.json');\n    this.zapierActionsFile = path.join(this.zapierDir, 'zapier_actions.json');\n    \n    // JWT settings\n    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';\n    this.jwtExpiresIn = process.env.ZAPIER_JWT_EXPIRES_IN || '30d';\n    \n    // OAuth settings\n    this.clientId = process.env.ZAPIER_CLIENT_ID || 'nova-connect-zapier';\n    this.clientSecret = process.env.ZAPIER_CLIENT_SECRET || crypto.randomBytes(32).toString('hex');\n    this.redirectUri = process.env.ZAPIER_REDIRECT_URI || 'https://zapier.com/dashboard/auth/oauth/return/App-ID/';\n    \n    // Initialize data directory\n    this.ensureDataDir();\n  }\n  \n  /**\n   * Ensure the data directory exists\n   */\n  async ensureDataDir() {\n    try {\n      await fs.mkdir(this.zapierDir, { recursive: true });\n      \n      // Initialize Zapier apps file if it doesn't exist\n      try {\n        await fs.access(this.zapierAppsFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.zapierAppsFile, JSON.stringify([]));\n        } else {\n          throw error;\n        }\n      }\n      \n      // Initialize Zapier auths file if it doesn't exist\n      try {\n        await fs.access(this.zapierAuthsFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.zapierAuthsFile, JSON.stringify([]));\n        } else {\n          throw error;\n        }\n      }\n      \n      // Initialize Zapier triggers file if it doesn't exist\n      try {\n        await fs.access(this.zapierTriggersFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.zapierTriggersFile, JSON.stringify([]));\n        } else {\n          throw error;\n        }\n      }\n      \n      // Initialize Zapier actions file if it doesn't exist\n      try {\n        await fs.access(this.zapierActionsFile);\n      } catch (error) {\n        if (error.code === 'ENOENT') {\n          await fs.writeFile(this.zapierActionsFile, JSON.stringify([]));\n        } else {\n          throw error;\n        }\n      }\n    } catch (error) {\n      logger.error('Error creating Zapier directory:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Load data from file\n   */\n  async loadData(filePath) {\n    try {\n      const data = await fs.readFile(filePath, 'utf8');\n      return JSON.parse(data);\n    } catch (error) {\n      logger.error(`Error loading data from ${filePath}:`, error);\n      return [];\n    }\n  }\n  \n  /**\n   * Save data to file\n   */\n  async saveData(filePath, data) {\n    try {\n      await fs.writeFile(filePath, JSON.stringify(data, null, 2));\n    } catch (error) {\n      logger.error(`Error saving data to ${filePath}:`, error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get Zapier app definition\n   */\n  getAppDefinition() {\n    return {\n      title: 'NovaConnect UAC',\n      description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.',\n      version: '1.0.0',\n      platformVersion: '1.0.0',\n      authentication: {\n        type: 'oauth2',\n        oauth2Config: {\n          authorizeUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/authorize`,\n          tokenUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/token`,\n          refreshUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/refresh`,\n          autoRefresh: true,\n          scope: 'read write'\n        },\n        connectionLabel: '{{bundle.authData.username}}'\n      },\n      beforeApp: {\n        url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/before-app`\n      },\n      afterApp: {\n        url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/after-app`\n      }\n    };\n  }\n  \n  /**\n   * Get Zapier triggers\n   */\n  async getTriggers() {\n    const triggers = await this.loadData(this.zapierTriggersFile);\n    \n    // Add default triggers if none exist\n    if (triggers.length === 0) {\n      const defaultTriggers = [\n        {\n          key: 'new_connector',\n          noun: 'Connector',\n          display: {\n            label: 'New Connector',\n            description: 'Triggers when a new connector is created.'\n          },\n          operation: {\n            type: 'polling',\n            perform: {\n              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/new-connector`\n            },\n            sample: {\n              id: 'conn-123',\n              name: 'Sample Connector',\n              type: 'api',\n              createdAt: '2023-01-01T00:00:00Z'\n            }\n          }\n        },\n        {\n          key: 'new_workflow',\n          noun: 'Workflow',\n          display: {\n            label: 'New Workflow',\n            description: 'Triggers when a new workflow is created.'\n          },\n          operation: {\n            type: 'polling',\n            perform: {\n              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/new-workflow`\n            },\n            sample: {\n              id: 'wf-123',\n              name: 'Sample Workflow',\n              status: 'active',\n              createdAt: '2023-01-01T00:00:00Z'\n            }\n          }\n        },\n        {\n          key: 'compliance_event',\n          noun: 'Compliance Event',\n          display: {\n            label: 'New Compliance Event',\n            description: 'Triggers when a new compliance event occurs.'\n          },\n          operation: {\n            type: 'polling',\n            perform: {\n              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/compliance-event`\n            },\n            sample: {\n              id: 'evt-123',\n              type: 'compliance.violation',\n              severity: 'high',\n              resource: 'storage-bucket-123',\n              details: 'Public access detected',\n              timestamp: '2023-01-01T00:00:00Z'\n            }\n          }\n        }\n      ];\n      \n      await this.saveData(this.zapierTriggersFile, defaultTriggers);\n      return defaultTriggers;\n    }\n    \n    return triggers;\n  }\n  \n  /**\n   * Get Zapier actions\n   */\n  async getActions() {\n    const actions = await this.loadData(this.zapierActionsFile);\n    \n    // Add default actions if none exist\n    if (actions.length === 0) {\n      const defaultActions = [\n        {\n          key: 'create_connector',\n          noun: 'Connector',\n          display: {\n            label: 'Create Connector',\n            description: 'Creates a new connector.'\n          },\n          operation: {\n            type: 'perform',\n            perform: {\n              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/create-connector`,\n              method: 'POST',\n              body: {\n                name: '{{bundle.inputData.name}}',\n                type: '{{bundle.inputData.type}}',\n                config: '{{bundle.inputData.config}}'\n              }\n            },\n            inputFields: [\n              {\n                key: 'name',\n                label: 'Name',\n                type: 'string',\n                required: true,\n                helpText: 'The name of the connector.'\n              },\n              {\n                key: 'type',\n                label: 'Type',\n                type: 'string',\n                required: true,\n                choices: {\n                  api: 'API',\n                  database: 'Database',\n                  file: 'File'\n                },\n                helpText: 'The type of the connector.'\n              },\n              {\n                key: 'config',\n                label: 'Configuration',\n                type: 'text',\n                required: true,\n                helpText: 'The configuration of the connector in JSON format.'\n              }\n            ],\n            sample: {\n              id: 'conn-123',\n              name: 'Sample Connector',\n              type: 'api',\n              createdAt: '2023-01-01T00:00:00Z'\n            }\n          }\n        },\n        {\n          key: 'execute_workflow',\n          noun: 'Workflow',\n          display: {\n            label: 'Execute Workflow',\n            description: 'Executes a workflow.'\n          },\n          operation: {\n            type: 'perform',\n            perform: {\n              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/execute-workflow`,\n              method: 'POST',\n              body: {\n                workflowId: '{{bundle.inputData.workflowId}}',\n                inputs: '{{bundle.inputData.inputs}}'\n              }\n            },\n            inputFields: [\n              {\n                key: 'workflowId',\n                label: 'Workflow ID',\n                type: 'string',\n                required: true,\n                helpText: 'The ID of the workflow to execute.'\n              },\n              {\n                key: 'inputs',\n                label: 'Inputs',\n                type: 'text',\n                required: false,\n                helpText: 'The inputs for the workflow in JSON format.'\n              }\n            ],\n            sample: {\n              id: 'exec-123',\n              workflowId: 'wf-123',\n              status: 'completed',\n              result: {\n                success: true,\n                data: {}\n              },\n              startedAt: '2023-01-01T00:00:00Z',\n              completedAt: '2023-01-01T00:00:01Z'\n            }\n          }\n        },\n        {\n          key: 'create_compliance_evidence',\n          noun: 'Compliance Evidence',\n          display: {\n            label: 'Create Compliance Evidence',\n            description: 'Creates a new compliance evidence record.'\n          },\n          operation: {\n            type: 'perform',\n            perform: {\n              url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/create-compliance-evidence`,\n              method: 'POST',\n              body: {\n                controlId: '{{bundle.inputData.controlId}}',\n                evidenceType: '{{bundle.inputData.evidenceType}}',\n                description: '{{bundle.inputData.description}}',\n                data: '{{bundle.inputData.data}}'\n              }\n            },\n            inputFields: [\n              {\n                key: 'controlId',\n                label: 'Control ID',\n                type: 'string',\n                required: true,\n                helpText: 'The ID of the compliance control.'\n              },\n              {\n                key: 'evidenceType',\n                label: 'Evidence Type',\n                type: 'string',\n                required: true,\n                choices: {\n                  document: 'Document',\n                  screenshot: 'Screenshot',\n                  log: 'Log',\n                  test_result: 'Test Result',\n                  attestation: 'Attestation'\n                },\n                helpText: 'The type of evidence.'\n              },\n              {\n                key: 'description',\n                label: 'Description',\n                type: 'text',\n                required: true,\n                helpText: 'A description of the evidence.'\n              },\n              {\n                key: 'data',\n                label: 'Data',\n                type: 'text',\n                required: false,\n                helpText: 'Additional data for the evidence in JSON format.'\n              }\n            ],\n            sample: {\n              id: 'evid-123',\n              controlId: 'ctrl-123',\n              evidenceType: 'document',\n              description: 'Sample evidence',\n              createdAt: '2023-01-01T00:00:00Z'\n            }\n          }\n        }\n      ];\n      \n      await this.saveData(this.zapierActionsFile, defaultActions);\n      return defaultActions;\n    }\n    \n    return actions;\n  }\n  \n  /**\n   * Create OAuth authorization URL\n   */\n  createAuthorizationUrl(state, redirectUri) {\n    const authUrl = new URL(`${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/authorize`);\n    \n    authUrl.searchParams.append('client_id', this.clientId);\n    authUrl.searchParams.append('redirect_uri', redirectUri || this.redirectUri);\n    authUrl.searchParams.append('response_type', 'code');\n    authUrl.searchParams.append('state', state);\n    \n    return authUrl.toString();\n  }\n  \n  /**\n   * Generate OAuth access token\n   */\n  async generateAccessToken(code, redirectUri) {\n    try {\n      // In a real implementation, this would validate the code\n      // For now, we'll generate a token directly\n      \n      // Generate a random user ID for demo purposes\n      const userId = `user-${Math.floor(Math.random() * 1000)}`;\n      \n      // Generate access token\n      const accessToken = jwt.sign(\n        {\n          sub: userId,\n          client_id: this.clientId,\n          scope: 'read write'\n        },\n        this.jwtSecret,\n        { expiresIn: this.jwtExpiresIn }\n      );\n      \n      // Generate refresh token\n      const refreshToken = crypto.randomBytes(32).toString('hex');\n      \n      // Save auth data\n      const auths = await this.loadData(this.zapierAuthsFile);\n      \n      auths.push({\n        userId,\n        clientId: this.clientId,\n        accessToken,\n        refreshToken,\n        scope: 'read write',\n        createdAt: new Date().toISOString(),\n        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days\n      });\n      \n      await this.saveData(this.zapierAuthsFile, auths);\n      \n      return {\n        access_token: accessToken,\n        refresh_token: refreshToken,\n        token_type: 'Bearer',\n        expires_in: 30 * 24 * 60 * 60, // 30 days in seconds\n        scope: 'read write'\n      };\n    } catch (error) {\n      logger.error('Error generating access token:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Refresh OAuth access token\n   */\n  async refreshAccessToken(refreshToken) {\n    try {\n      // Load auths\n      const auths = await this.loadData(this.zapierAuthsFile);\n      \n      // Find auth by refresh token\n      const authIndex = auths.findIndex(auth => auth.refreshToken === refreshToken);\n      \n      if (authIndex === -1) {\n        throw new Error('Invalid refresh token');\n      }\n      \n      const auth = auths[authIndex];\n      \n      // Generate new access token\n      const accessToken = jwt.sign(\n        {\n          sub: auth.userId,\n          client_id: auth.clientId,\n          scope: auth.scope\n        },\n        this.jwtSecret,\n        { expiresIn: this.jwtExpiresIn }\n      );\n      \n      // Generate new refresh token\n      const newRefreshToken = crypto.randomBytes(32).toString('hex');\n      \n      // Update auth data\n      auths[authIndex] = {\n        ...auth,\n        accessToken,\n        refreshToken: newRefreshToken,\n        updatedAt: new Date().toISOString(),\n        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days\n      };\n      \n      await this.saveData(this.zapierAuthsFile, auths);\n      \n      return {\n        access_token: accessToken,\n        refresh_token: newRefreshToken,\n        token_type: 'Bearer',\n        expires_in: 30 * 24 * 60 * 60, // 30 days in seconds\n        scope: auth.scope\n      };\n    } catch (error) {\n      logger.error('Error refreshing access token:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Verify OAuth access token\n   */\n  async verifyAccessToken(accessToken) {\n    try {\n      // Verify JWT\n      const decoded = jwt.verify(accessToken, this.jwtSecret);\n      \n      // Load auths\n      const auths = await this.loadData(this.zapierAuthsFile);\n      \n      // Find auth by access token\n      const auth = auths.find(auth => auth.accessToken === accessToken);\n      \n      if (!auth) {\n        throw new Error('Invalid access token');\n      }\n      \n      return {\n        userId: decoded.sub,\n        clientId: decoded.client_id,\n        scope: decoded.scope\n      };\n    } catch (error) {\n      logger.error('Error verifying access token:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Register Zapier app\n   */\n  async registerApp(appData) {\n    try {\n      // Load apps\n      const apps = await this.loadData(this.zapierAppsFile);\n      \n      // Create app\n      const app = {\n        id: uuidv4(),\n        ...appData,\n        createdAt: new Date().toISOString()\n      };\n      \n      // Add app\n      apps.push(app);\n      \n      // Save apps\n      await this.saveData(this.zapierAppsFile, apps);\n      \n      return app;\n    } catch (error) {\n      logger.error('Error registering Zapier app:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get Zapier app by ID\n   */\n  async getAppById(appId) {\n    try {\n      // Load apps\n      const apps = await this.loadData(this.zapierAppsFile);\n      \n      // Find app by ID\n      const app = apps.find(app => app.id === appId);\n      \n      if (!app) {\n        throw new Error(`App with ID ${appId} not found`);\n      }\n      \n      return app;\n    } catch (error) {\n      logger.error('Error getting Zapier app by ID:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Get all Zapier apps\n   */\n  async getAllApps() {\n    try {\n      // Load apps\n      return await this.loadData(this.zapierAppsFile);\n    } catch (error) {\n      logger.error('Error getting all Zapier apps:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Update Zapier app\n   */\n  async updateApp(appId, appData) {\n    try {\n      // Load apps\n      const apps = await this.loadData(this.zapierAppsFile);\n      \n      // Find app index\n      const appIndex = apps.findIndex(app => app.id === appId);\n      \n      if (appIndex === -1) {\n        throw new Error(`App with ID ${appId} not found`);\n      }\n      \n      // Update app\n      apps[appIndex] = {\n        ...apps[appIndex],\n        ...appData,\n        updatedAt: new Date().toISOString()\n      };\n      \n      // Save apps\n      await this.saveData(this.zapierAppsFile, apps);\n      \n      return apps[appIndex];\n    } catch (error) {\n      logger.error('Error updating Zapier app:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Delete Zapier app\n   */\n  async deleteApp(appId) {\n    try {\n      // Load apps\n      const apps = await this.loadData(this.zapierAppsFile);\n      \n      // Find app index\n      const appIndex = apps.findIndex(app => app.id === appId);\n      \n      if (appIndex === -1) {\n        throw new Error(`App with ID ${appId} not found`);\n      }\n      \n      // Remove app\n      apps.splice(appIndex, 1);\n      \n      // Save apps\n      await this.saveData(this.zapierAppsFile, apps);\n      \n      return true;\n    } catch (error) {\n      logger.error('Error deleting Zapier app:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Register Zapier trigger\n   */\n  async registerTrigger(triggerData) {\n    try {\n      // Load triggers\n      const triggers = await this.loadData(this.zapierTriggersFile);\n      \n      // Create trigger\n      const trigger = {\n        id: uuidv4(),\n        ...triggerData,\n        createdAt: new Date().toISOString()\n      };\n      \n      // Add trigger\n      triggers.push(trigger);\n      \n      // Save triggers\n      await this.saveData(this.zapierTriggersFile, triggers);\n      \n      return trigger;\n    } catch (error) {\n      logger.error('Error registering Zapier trigger:', error);\n      throw error;\n    }\n  }\n  \n  /**\n   * Register Zapier action\n   */\n  async registerAction(actionData) {\n    try {\n      // Load actions\n      const actions = await this.loadData(this.zapierActionsFile);\n      \n      // Create action\n      const action = {\n        id: uuidv4(),\n        ...actionData,\n        createdAt: new Date().toISOString()\n      };\n      \n      // Add action\n      actions.push(action);\n      \n      // Save actions\n      await this.saveData(this.zapierActionsFile, actions);\n      \n      return action;\n    } catch (error) {\n      logger.error('Error registering Zapier action:', error);\n      throw error;\n    }\n  }\n}\n\nmodule.exports = ZapierService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,EAAE,GAAGC,OAAO,CAAC,IAAI,CAAC,CAACC,QAAQ;AACjC,MAAMC,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAM;EAAEG,EAAE,EAAEC;AAAO,CAAC,GAAGJ,OAAO,CAAC,MAAM,CAAC;AACtC,MAAMK,GAAG,GAAGL,OAAO,CAAC,cAAc,CAAC;AACnC,MAAMM,MAAM,GAAGN,OAAO,CAAC,QAAQ,CAAC;AAChC,MAAMO,MAAM,GAAGP,OAAO,CAAC,qBAAqB,CAAC;AAE7C,MAAMQ,aAAa,CAAC;EAClBC,WAAWA,CAACC,OAAO,GAAGR,IAAI,CAACS,IAAI,CAACC,SAAS,EAAE,YAAY,CAAC,EAAE;IACxD,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,SAAS,GAAGX,IAAI,CAACS,IAAI,CAAC,IAAI,CAACD,OAAO,EAAE,QAAQ,CAAC;IAClD,IAAI,CAACI,cAAc,GAAGZ,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE,kBAAkB,CAAC;IACnE,IAAI,CAACE,eAAe,GAAGb,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE,mBAAmB,CAAC;IACrE,IAAI,CAACG,kBAAkB,GAAGd,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE,sBAAsB,CAAC;IAC3E,IAAI,CAACI,iBAAiB,GAAGf,IAAI,CAACS,IAAI,CAAC,IAAI,CAACE,SAAS,EAAE,qBAAqB,CAAC;;IAEzE;IACA,IAAI,CAACK,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,IAAI,iBAAiB;IAC5D,IAAI,CAACC,YAAY,GAAGH,OAAO,CAACC,GAAG,CAACG,qBAAqB,IAAI,KAAK;;IAE9D;IACA,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAACC,GAAG,CAACK,gBAAgB,IAAI,qBAAqB;IACrE,IAAI,CAACC,YAAY,GAAGP,OAAO,CAACC,GAAG,CAACO,oBAAoB,IAAIrB,MAAM,CAACsB,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;IAC9F,IAAI,CAACC,WAAW,GAAGX,OAAO,CAACC,GAAG,CAACW,mBAAmB,IAAI,wDAAwD;;IAE9G;IACA,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACE,MAAMA,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMjC,EAAE,CAACkC,KAAK,CAAC,IAAI,CAACpB,SAAS,EAAE;QAAEqB,SAAS,EAAE;MAAK,CAAC,CAAC;;MAEnD;MACA,IAAI;QACF,MAAMnC,EAAE,CAACoC,MAAM,CAAC,IAAI,CAACrB,cAAc,CAAC;MACtC,CAAC,CAAC,OAAOsB,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAMtC,EAAE,CAACuC,SAAS,CAAC,IAAI,CAACxB,cAAc,EAAEyB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC7D,CAAC,MAAM;UACL,MAAMJ,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMrC,EAAE,CAACoC,MAAM,CAAC,IAAI,CAACpB,eAAe,CAAC;MACvC,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAMtC,EAAE,CAACuC,SAAS,CAAC,IAAI,CAACvB,eAAe,EAAEwB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC9D,CAAC,MAAM;UACL,MAAMJ,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMrC,EAAE,CAACoC,MAAM,CAAC,IAAI,CAACnB,kBAAkB,CAAC;MAC1C,CAAC,CAAC,OAAOoB,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAMtC,EAAE,CAACuC,SAAS,CAAC,IAAI,CAACtB,kBAAkB,EAAEuB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;QACjE,CAAC,MAAM;UACL,MAAMJ,KAAK;QACb;MACF;;MAEA;MACA,IAAI;QACF,MAAMrC,EAAE,CAACoC,MAAM,CAAC,IAAI,CAAClB,iBAAiB,CAAC;MACzC,CAAC,CAAC,OAAOmB,KAAK,EAAE;QACd,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC3B,MAAMtC,EAAE,CAACuC,SAAS,CAAC,IAAI,CAACrB,iBAAiB,EAAEsB,IAAI,CAACC,SAAS,CAAC,EAAE,CAAC,CAAC;QAChE,CAAC,MAAM;UACL,MAAMJ,KAAK;QACb;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMK,QAAQA,CAACC,QAAQ,EAAE;IACvB,IAAI;MACF,MAAMC,IAAI,GAAG,MAAM5C,EAAE,CAAC6C,QAAQ,CAACF,QAAQ,EAAE,MAAM,CAAC;MAChD,OAAOH,IAAI,CAACM,KAAK,CAACF,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,2BAA2BM,QAAQ,GAAG,EAAEN,KAAK,CAAC;MAC3D,OAAO,EAAE;IACX;EACF;;EAEA;AACF;AACA;EACE,MAAMU,QAAQA,CAACJ,QAAQ,EAAEC,IAAI,EAAE;IAC7B,IAAI;MACF,MAAM5C,EAAE,CAACuC,SAAS,CAACI,QAAQ,EAAEH,IAAI,CAACC,SAAS,CAACG,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,OAAOP,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,wBAAwBM,QAAQ,GAAG,EAAEN,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEW,gBAAgBA,CAAA,EAAG;IACjB,OAAO;MACLC,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE,qDAAqD;MAClEC,OAAO,EAAE,OAAO;MAChBC,eAAe,EAAE,OAAO;MACxBC,cAAc,EAAE;QACdC,IAAI,EAAE,QAAQ;QACdC,YAAY,EAAE;UACZC,YAAY,EAAE,GAAGpC,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,6BAA6B;UACvGC,QAAQ,EAAE,GAAGtC,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,yBAAyB;UAC/FE,UAAU,EAAE,GAAGvC,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,2BAA2B;UACnGG,WAAW,EAAE,IAAI;UACjBC,KAAK,EAAE;QACT,CAAC;QACDC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B;MACnE,CAAC;MACDQ,QAAQ,EAAE;QACRD,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B;MACnE;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACE,MAAMS,WAAWA,CAAA,EAAG;IAClB,MAAMC,QAAQ,GAAG,MAAM,IAAI,CAACzB,QAAQ,CAAC,IAAI,CAACzB,kBAAkB,CAAC;;IAE7D;IACA,IAAIkD,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,MAAMC,eAAe,GAAG,CACtB;QACEC,GAAG,EAAE,eAAe;QACpBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;UACPC,KAAK,EAAE,eAAe;UACtBvB,WAAW,EAAE;QACf,CAAC;QACDwB,SAAS,EAAE;UACTpB,IAAI,EAAE,SAAS;UACfqB,OAAO,EAAE;YACPX,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B;UACnE,CAAC;UACDmB,MAAM,EAAE;YACNC,EAAE,EAAE,UAAU;YACdC,IAAI,EAAE,kBAAkB;YACxBxB,IAAI,EAAE,KAAK;YACXyB,SAAS,EAAE;UACb;QACF;MACF,CAAC,EACD;QACET,GAAG,EAAE,cAAc;QACnBC,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAE;UACPC,KAAK,EAAE,cAAc;UACrBvB,WAAW,EAAE;QACf,CAAC;QACDwB,SAAS,EAAE;UACTpB,IAAI,EAAE,SAAS;UACfqB,OAAO,EAAE;YACPX,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B;UACnE,CAAC;UACDmB,MAAM,EAAE;YACNC,EAAE,EAAE,QAAQ;YACZC,IAAI,EAAE,iBAAiB;YACvBE,MAAM,EAAE,QAAQ;YAChBD,SAAS,EAAE;UACb;QACF;MACF,CAAC,EACD;QACET,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,kBAAkB;QACxBC,OAAO,EAAE;UACPC,KAAK,EAAE,sBAAsB;UAC7BvB,WAAW,EAAE;QACf,CAAC;QACDwB,SAAS,EAAE;UACTpB,IAAI,EAAE,SAAS;UACfqB,OAAO,EAAE;YACPX,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B;UACnE,CAAC;UACDmB,MAAM,EAAE;YACNC,EAAE,EAAE,SAAS;YACbvB,IAAI,EAAE,sBAAsB;YAC5B2B,QAAQ,EAAE,MAAM;YAChBC,QAAQ,EAAE,oBAAoB;YAC9BC,OAAO,EAAE,wBAAwB;YACjCC,SAAS,EAAE;UACb;QACF;MACF,CAAC,CACF;MAED,MAAM,IAAI,CAACrC,QAAQ,CAAC,IAAI,CAAC9B,kBAAkB,EAAEoD,eAAe,CAAC;MAC7D,OAAOA,eAAe;IACxB;IAEA,OAAOF,QAAQ;EACjB;;EAEA;AACF;AACA;EACE,MAAMkB,UAAUA,CAAA,EAAG;IACjB,MAAMC,OAAO,GAAG,MAAM,IAAI,CAAC5C,QAAQ,CAAC,IAAI,CAACxB,iBAAiB,CAAC;;IAE3D;IACA,IAAIoE,OAAO,CAAClB,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMmB,cAAc,GAAG,CACrB;QACEjB,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;UACPC,KAAK,EAAE,kBAAkB;UACzBvB,WAAW,EAAE;QACf,CAAC;QACDwB,SAAS,EAAE;UACTpB,IAAI,EAAE,SAAS;UACfqB,OAAO,EAAE;YACPX,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,sCAAsC;YACvG+B,MAAM,EAAE,MAAM;YACdC,IAAI,EAAE;cACJX,IAAI,EAAE,2BAA2B;cACjCxB,IAAI,EAAE,2BAA2B;cACjCoC,MAAM,EAAE;YACV;UACF,CAAC;UACDC,WAAW,EAAE,CACX;YACErB,GAAG,EAAE,MAAM;YACXG,KAAK,EAAE,MAAM;YACbnB,IAAI,EAAE,QAAQ;YACdsC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE;UACZ,CAAC,EACD;YACEvB,GAAG,EAAE,MAAM;YACXG,KAAK,EAAE,MAAM;YACbnB,IAAI,EAAE,QAAQ;YACdsC,QAAQ,EAAE,IAAI;YACdE,OAAO,EAAE;cACPC,GAAG,EAAE,KAAK;cACVC,QAAQ,EAAE,UAAU;cACpBC,IAAI,EAAE;YACR,CAAC;YACDJ,QAAQ,EAAE;UACZ,CAAC,EACD;YACEvB,GAAG,EAAE,QAAQ;YACbG,KAAK,EAAE,eAAe;YACtBnB,IAAI,EAAE,MAAM;YACZsC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE;UACZ,CAAC,CACF;UACDjB,MAAM,EAAE;YACNC,EAAE,EAAE,UAAU;YACdC,IAAI,EAAE,kBAAkB;YACxBxB,IAAI,EAAE,KAAK;YACXyB,SAAS,EAAE;UACb;QACF;MACF,CAAC,EACD;QACET,GAAG,EAAE,kBAAkB;QACvBC,IAAI,EAAE,UAAU;QAChBC,OAAO,EAAE;UACPC,KAAK,EAAE,kBAAkB;UACzBvB,WAAW,EAAE;QACf,CAAC;QACDwB,SAAS,EAAE;UACTpB,IAAI,EAAE,SAAS;UACfqB,OAAO,EAAE;YACPX,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,sCAAsC;YACvG+B,MAAM,EAAE,MAAM;YACdC,IAAI,EAAE;cACJS,UAAU,EAAE,iCAAiC;cAC7CC,MAAM,EAAE;YACV;UACF,CAAC;UACDR,WAAW,EAAE,CACX;YACErB,GAAG,EAAE,YAAY;YACjBG,KAAK,EAAE,aAAa;YACpBnB,IAAI,EAAE,QAAQ;YACdsC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE;UACZ,CAAC,EACD;YACEvB,GAAG,EAAE,QAAQ;YACbG,KAAK,EAAE,QAAQ;YACfnB,IAAI,EAAE,MAAM;YACZsC,QAAQ,EAAE,KAAK;YACfC,QAAQ,EAAE;UACZ,CAAC,CACF;UACDjB,MAAM,EAAE;YACNC,EAAE,EAAE,UAAU;YACdqB,UAAU,EAAE,QAAQ;YACpBlB,MAAM,EAAE,WAAW;YACnBoB,MAAM,EAAE;cACNC,OAAO,EAAE,IAAI;cACbzD,IAAI,EAAE,CAAC;YACT,CAAC;YACD0D,SAAS,EAAE,sBAAsB;YACjCC,WAAW,EAAE;UACf;QACF;MACF,CAAC,EACD;QACEjC,GAAG,EAAE,4BAA4B;QACjCC,IAAI,EAAE,qBAAqB;QAC3BC,OAAO,EAAE;UACPC,KAAK,EAAE,4BAA4B;UACnCvB,WAAW,EAAE;QACf,CAAC;QACDwB,SAAS,EAAE;UACTpB,IAAI,EAAE,SAAS;UACfqB,OAAO,EAAE;YACPX,GAAG,EAAE,GAAG5C,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,gDAAgD;YACjH+B,MAAM,EAAE,MAAM;YACdC,IAAI,EAAE;cACJe,SAAS,EAAE,gCAAgC;cAC3CC,YAAY,EAAE,mCAAmC;cACjDvD,WAAW,EAAE,kCAAkC;cAC/CN,IAAI,EAAE;YACR;UACF,CAAC;UACD+C,WAAW,EAAE,CACX;YACErB,GAAG,EAAE,WAAW;YAChBG,KAAK,EAAE,YAAY;YACnBnB,IAAI,EAAE,QAAQ;YACdsC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE;UACZ,CAAC,EACD;YACEvB,GAAG,EAAE,cAAc;YACnBG,KAAK,EAAE,eAAe;YACtBnB,IAAI,EAAE,QAAQ;YACdsC,QAAQ,EAAE,IAAI;YACdE,OAAO,EAAE;cACPY,QAAQ,EAAE,UAAU;cACpBC,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE,KAAK;cACVC,WAAW,EAAE,aAAa;cAC1BC,WAAW,EAAE;YACf,CAAC;YACDjB,QAAQ,EAAE;UACZ,CAAC,EACD;YACEvB,GAAG,EAAE,aAAa;YAClBG,KAAK,EAAE,aAAa;YACpBnB,IAAI,EAAE,MAAM;YACZsC,QAAQ,EAAE,IAAI;YACdC,QAAQ,EAAE;UACZ,CAAC,EACD;YACEvB,GAAG,EAAE,MAAM;YACXG,KAAK,EAAE,MAAM;YACbnB,IAAI,EAAE,MAAM;YACZsC,QAAQ,EAAE,KAAK;YACfC,QAAQ,EAAE;UACZ,CAAC,CACF;UACDjB,MAAM,EAAE;YACNC,EAAE,EAAE,UAAU;YACd2B,SAAS,EAAE,UAAU;YACrBC,YAAY,EAAE,UAAU;YACxBvD,WAAW,EAAE,iBAAiB;YAC9B6B,SAAS,EAAE;UACb;QACF;MACF,CAAC,CACF;MAED,MAAM,IAAI,CAAChC,QAAQ,CAAC,IAAI,CAAC7B,iBAAiB,EAAEqE,cAAc,CAAC;MAC3D,OAAOA,cAAc;IACvB;IAEA,OAAOD,OAAO;EAChB;;EAEA;AACF;AACA;EACEyB,sBAAsBA,CAACC,KAAK,EAAEjF,WAAW,EAAE;IACzC,MAAMkF,OAAO,GAAG,IAAIC,GAAG,CAAC,GAAG9F,OAAO,CAACC,GAAG,CAACoC,YAAY,IAAI,6BAA6B,6BAA6B,CAAC;IAElHwD,OAAO,CAACE,YAAY,CAACC,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC3F,QAAQ,CAAC;IACvDwF,OAAO,CAACE,YAAY,CAACC,MAAM,CAAC,cAAc,EAAErF,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC;IAC5EkF,OAAO,CAACE,YAAY,CAACC,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC;IACpDH,OAAO,CAACE,YAAY,CAACC,MAAM,CAAC,OAAO,EAAEJ,KAAK,CAAC;IAE3C,OAAOC,OAAO,CAACnF,QAAQ,CAAC,CAAC;EAC3B;;EAEA;AACF;AACA;EACE,MAAMuF,mBAAmBA,CAAC/E,IAAI,EAAEP,WAAW,EAAE;IAC3C,IAAI;MACF;MACA;;MAEA;MACA,MAAMuF,MAAM,GAAG,QAAQC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE;;MAEzD;MACA,MAAMC,WAAW,GAAGpH,GAAG,CAACqH,IAAI,CAC1B;QACEC,GAAG,EAAEN,MAAM;QACXO,SAAS,EAAE,IAAI,CAACpG,QAAQ;QACxBoC,KAAK,EAAE;MACT,CAAC,EACD,IAAI,CAAC1C,SAAS,EACd;QAAE2G,SAAS,EAAE,IAAI,CAACvG;MAAa,CACjC,CAAC;;MAED;MACA,MAAMwG,YAAY,GAAGxH,MAAM,CAACsB,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;;MAE3D;MACA,MAAMkG,KAAK,GAAG,MAAM,IAAI,CAACtF,QAAQ,CAAC,IAAI,CAAC1B,eAAe,CAAC;MAEvDgH,KAAK,CAACC,IAAI,CAAC;QACTX,MAAM;QACN7F,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBiG,WAAW;QACXK,YAAY;QACZlE,KAAK,EAAE,YAAY;QACnBkB,SAAS,EAAE,IAAImD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACF,WAAW,CAAC,CAAC,CAAC;MAC3E,CAAC,CAAC;MAEF,MAAM,IAAI,CAACpF,QAAQ,CAAC,IAAI,CAAC/B,eAAe,EAAEgH,KAAK,CAAC;MAEhD,OAAO;QACLM,YAAY,EAAEZ,WAAW;QACzBa,aAAa,EAAER,YAAY;QAC3BS,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;QAAE;QAC/B5E,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMqG,kBAAkBA,CAACX,YAAY,EAAE;IACrC,IAAI;MACF;MACA,MAAMC,KAAK,GAAG,MAAM,IAAI,CAACtF,QAAQ,CAAC,IAAI,CAAC1B,eAAe,CAAC;;MAEvD;MACA,MAAM2H,SAAS,GAAGX,KAAK,CAACY,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACd,YAAY,KAAKA,YAAY,CAAC;MAE7E,IAAIY,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB,MAAM,IAAIG,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEA,MAAMD,IAAI,GAAGb,KAAK,CAACW,SAAS,CAAC;;MAE7B;MACA,MAAMjB,WAAW,GAAGpH,GAAG,CAACqH,IAAI,CAC1B;QACEC,GAAG,EAAEiB,IAAI,CAACvB,MAAM;QAChBO,SAAS,EAAEgB,IAAI,CAACpH,QAAQ;QACxBoC,KAAK,EAAEgF,IAAI,CAAChF;MACd,CAAC,EACD,IAAI,CAAC1C,SAAS,EACd;QAAE2G,SAAS,EAAE,IAAI,CAACvG;MAAa,CACjC,CAAC;;MAED;MACA,MAAMwH,eAAe,GAAGxI,MAAM,CAACsB,WAAW,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC;;MAE9D;MACAkG,KAAK,CAACW,SAAS,CAAC,GAAG;QACjB,GAAGE,IAAI;QACPnB,WAAW;QACXK,YAAY,EAAEgB,eAAe;QAC7BC,SAAS,EAAE,IAAId,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIF,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAACF,WAAW,CAAC,CAAC,CAAC;MAC3E,CAAC;MAED,MAAM,IAAI,CAACpF,QAAQ,CAAC,IAAI,CAAC/B,eAAe,EAAEgH,KAAK,CAAC;MAEhD,OAAO;QACLM,YAAY,EAAEZ,WAAW;QACzBa,aAAa,EAAEQ,eAAe;QAC9BP,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;QAAE;QAC/B5E,KAAK,EAAEgF,IAAI,CAAChF;MACd,CAAC;IACH,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4G,iBAAiBA,CAACvB,WAAW,EAAE;IACnC,IAAI;MACF;MACA,MAAMwB,OAAO,GAAG5I,GAAG,CAAC6I,MAAM,CAACzB,WAAW,EAAE,IAAI,CAACvG,SAAS,CAAC;;MAEvD;MACA,MAAM6G,KAAK,GAAG,MAAM,IAAI,CAACtF,QAAQ,CAAC,IAAI,CAAC1B,eAAe,CAAC;;MAEvD;MACA,MAAM6H,IAAI,GAAGb,KAAK,CAACoB,IAAI,CAACP,IAAI,IAAIA,IAAI,CAACnB,WAAW,KAAKA,WAAW,CAAC;MAEjE,IAAI,CAACmB,IAAI,EAAE;QACT,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MACzC;MAEA,OAAO;QACLxB,MAAM,EAAE4B,OAAO,CAACtB,GAAG;QACnBnG,QAAQ,EAAEyH,OAAO,CAACrB,SAAS;QAC3BhE,KAAK,EAAEqF,OAAO,CAACrF;MACjB,CAAC;IACH,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgH,WAAWA,CAACC,OAAO,EAAE;IACzB,IAAI;MACF;MACA,MAAMC,IAAI,GAAG,MAAM,IAAI,CAAC7G,QAAQ,CAAC,IAAI,CAAC3B,cAAc,CAAC;;MAErD;MACA,MAAMyI,GAAG,GAAG;QACV3E,EAAE,EAAExE,MAAM,CAAC,CAAC;QACZ,GAAGiJ,OAAO;QACVvE,SAAS,EAAE,IAAImD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACAoB,IAAI,CAACtB,IAAI,CAACuB,GAAG,CAAC;;MAEd;MACA,MAAM,IAAI,CAACzG,QAAQ,CAAC,IAAI,CAAChC,cAAc,EAAEwI,IAAI,CAAC;MAE9C,OAAOC,GAAG;IACZ,CAAC,CAAC,OAAOnH,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoH,UAAUA,CAACC,KAAK,EAAE;IACtB,IAAI;MACF;MACA,MAAMH,IAAI,GAAG,MAAM,IAAI,CAAC7G,QAAQ,CAAC,IAAI,CAAC3B,cAAc,CAAC;;MAErD;MACA,MAAMyI,GAAG,GAAGD,IAAI,CAACH,IAAI,CAACI,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAK6E,KAAK,CAAC;MAE9C,IAAI,CAACF,GAAG,EAAE;QACR,MAAM,IAAIV,KAAK,CAAC,eAAeY,KAAK,YAAY,CAAC;MACnD;MAEA,OAAOF,GAAG;IACZ,CAAC,CAAC,OAAOnH,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMsH,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF;MACA,OAAO,MAAM,IAAI,CAACjH,QAAQ,CAAC,IAAI,CAAC3B,cAAc,CAAC;IACjD,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMuH,SAASA,CAACF,KAAK,EAAEJ,OAAO,EAAE;IAC9B,IAAI;MACF;MACA,MAAMC,IAAI,GAAG,MAAM,IAAI,CAAC7G,QAAQ,CAAC,IAAI,CAAC3B,cAAc,CAAC;;MAErD;MACA,MAAM8I,QAAQ,GAAGN,IAAI,CAACX,SAAS,CAACY,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAK6E,KAAK,CAAC;MAExD,IAAIG,QAAQ,KAAK,CAAC,CAAC,EAAE;QACnB,MAAM,IAAIf,KAAK,CAAC,eAAeY,KAAK,YAAY,CAAC;MACnD;;MAEA;MACAH,IAAI,CAACM,QAAQ,CAAC,GAAG;QACf,GAAGN,IAAI,CAACM,QAAQ,CAAC;QACjB,GAAGP,OAAO;QACVN,SAAS,EAAE,IAAId,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAM,IAAI,CAACpF,QAAQ,CAAC,IAAI,CAAChC,cAAc,EAAEwI,IAAI,CAAC;MAE9C,OAAOA,IAAI,CAACM,QAAQ,CAAC;IACvB,CAAC,CAAC,OAAOxH,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMyH,SAASA,CAACJ,KAAK,EAAE;IACrB,IAAI;MACF;MACA,MAAMH,IAAI,GAAG,MAAM,IAAI,CAAC7G,QAAQ,CAAC,IAAI,CAAC3B,cAAc,CAAC;;MAErD;MACA,MAAM8I,QAAQ,GAAGN,IAAI,CAACX,SAAS,CAACY,GAAG,IAAIA,GAAG,CAAC3E,EAAE,KAAK6E,KAAK,CAAC;MAExD,IAAIG,QAAQ,KAAK,CAAC,CAAC,EAAE;QACnB,MAAM,IAAIf,KAAK,CAAC,eAAeY,KAAK,YAAY,CAAC;MACnD;;MAEA;MACAH,IAAI,CAACQ,MAAM,CAACF,QAAQ,EAAE,CAAC,CAAC;;MAExB;MACA,MAAM,IAAI,CAAC9G,QAAQ,CAAC,IAAI,CAAChC,cAAc,EAAEwI,IAAI,CAAC;MAE9C,OAAO,IAAI;IACb,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM2H,eAAeA,CAACC,WAAW,EAAE;IACjC,IAAI;MACF;MACA,MAAM9F,QAAQ,GAAG,MAAM,IAAI,CAACzB,QAAQ,CAAC,IAAI,CAACzB,kBAAkB,CAAC;;MAE7D;MACA,MAAMiJ,OAAO,GAAG;QACdrF,EAAE,EAAExE,MAAM,CAAC,CAAC;QACZ,GAAG4J,WAAW;QACdlF,SAAS,EAAE,IAAImD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACAhE,QAAQ,CAAC8D,IAAI,CAACiC,OAAO,CAAC;;MAEtB;MACA,MAAM,IAAI,CAACnH,QAAQ,CAAC,IAAI,CAAC9B,kBAAkB,EAAEkD,QAAQ,CAAC;MAEtD,OAAO+F,OAAO;IAChB,CAAC,CAAC,OAAO7H,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8H,cAAcA,CAACC,UAAU,EAAE;IAC/B,IAAI;MACF;MACA,MAAM9E,OAAO,GAAG,MAAM,IAAI,CAAC5C,QAAQ,CAAC,IAAI,CAACxB,iBAAiB,CAAC;;MAE3D;MACA,MAAMmJ,MAAM,GAAG;QACbxF,EAAE,EAAExE,MAAM,CAAC,CAAC;QACZ,GAAG+J,UAAU;QACbrF,SAAS,EAAE,IAAImD,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;;MAED;MACA7C,OAAO,CAAC2C,IAAI,CAACoC,MAAM,CAAC;;MAEpB;MACA,MAAM,IAAI,CAACtH,QAAQ,CAAC,IAAI,CAAC7B,iBAAiB,EAAEoE,OAAO,CAAC;MAEpD,OAAO+E,MAAM;IACf,CAAC,CAAC,OAAOhI,KAAK,EAAE;MACd7B,MAAM,CAAC6B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACvD,MAAMA,KAAK;IACb;EACF;AACF;AAEAiI,MAAM,CAACC,OAAO,GAAG9J,aAAa", "ignoreList": []}