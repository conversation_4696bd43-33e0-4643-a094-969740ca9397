/**
 * Meter Tests
 * 
 * This module provides tests for the Comphyon Meter component,
 * which is responsible for measuring emergent intelligence.
 */

const { NEPITestSuite, assertions, nepiAssertions, PI_10_CUBED, GOLDEN_RATIO } = require('./nepi-test-framework');
const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * Create a Meter Test Suite
 * @returns {NEPITestSuite} The test suite
 */
function createMeterTestSuite() {
  // Create test suite
  const suite = new NEPITestSuite('Meter Tests', {
    testingLayer: 'Operational',
    domains: ['universal', 'cyber', 'financial', 'biological']
  });

  // Mock classes for testing
  const mockComphyonMeter = createMockComphyonMeter();
  const mockDomainEnergyCalculator = createMockDomainEnergyCalculator();
  const mockEmergentIntelligenceDetector = createMockEmergentIntelligenceDetector();

  // Test: Comphyon Calculation
  suite.nepiTest('should correctly calculate Comphyons', async () => {
    // Calculate domain energies
    const csdeEnergy = mockDomainEnergyCalculator.calculateCSDE(0.8, 0.7);
    const csfeEnergy = mockDomainEnergyCalculator.calculateCSFE(0.6, 0.9);
    const csmeEnergy = mockDomainEnergyCalculator.calculateCSME(0.7, 0.8);
    
    // Calculate energy gradients
    const csdeGradient = mockDomainEnergyCalculator.calculateGradient(csdeEnergy, 0.05);
    const csfeGradient = mockDomainEnergyCalculator.calculateGradient(csfeEnergy, 0.03);
    const csmeGradient = mockDomainEnergyCalculator.calculateGradient(csmeEnergy, 0.04);
    
    // Calculate Comphyons
    const comphyons = mockComphyonMeter.calculateComphyons(
      csdeGradient,
      csfeGradient,
      csmeEnergy
    );
    
    // Calculate expected value manually
    const expectedComphyons = ((csdeGradient * csfeGradient) * Math.log(csmeEnergy)) / 166000;
    
    // Assert
    assertions.approximately(comphyons, expectedComphyons, 0.0001, 'Comphyon calculation incorrect');
  }, {
    testingType: 'Meter Validation',
    coherenceImpact: 'positive',
    domains: ['universal']
  });

  // Test: Domain Energy Calculation
  suite.nepiTest('should correctly calculate domain energies', async () => {
    // Calculate domain energies
    const csdeEnergy = mockDomainEnergyCalculator.calculateCSDE(0.8, 0.7);
    const csfeEnergy = mockDomainEnergyCalculator.calculateCSFE(0.6, 0.9);
    const csmeEnergy = mockDomainEnergyCalculator.calculateCSME(0.7, 0.8);
    
    // Calculate expected values manually
    const expectedCsdeEnergy = 0.8 * 0.7;
    const expectedCsfeEnergy = 0.6 * 0.9;
    const expectedCsmeEnergy = 0.7 * 0.8;
    
    // Assert
    assertions.approximately(csdeEnergy, expectedCsdeEnergy, 0.0001, 'CSDE energy calculation incorrect');
    assertions.approximately(csfeEnergy, expectedCsfeEnergy, 0.0001, 'CSFE energy calculation incorrect');
    assertions.approximately(csmeEnergy, expectedCsmeEnergy, 0.0001, 'CSME energy calculation incorrect');
  }, {
    testingType: 'Meter Validation',
    coherenceImpact: 'positive',
    domains: ['cyber', 'financial', 'biological']
  });

  // Test: Energy Gradient Calculation
  suite.nepiTest('should correctly calculate energy gradients', async () => {
    // Calculate energy gradients
    const csdeGradient = mockDomainEnergyCalculator.calculateGradient(0.5, 0.05);
    const csfeGradient = mockDomainEnergyCalculator.calculateGradient(0.6, 0.03);
    const csmeGradient = mockDomainEnergyCalculator.calculateGradient(0.7, 0.04);
    
    // Calculate expected values manually
    const expectedCsdeGradient = 0.05 / 0.5;
    const expectedCsfeGradient = 0.03 / 0.6;
    const expectedCsmeGradient = 0.04 / 0.7;
    
    // Assert
    assertions.approximately(csdeGradient, expectedCsdeGradient, 0.0001, 'CSDE gradient calculation incorrect');
    assertions.approximately(csfeGradient, expectedCsfeGradient, 0.0001, 'CSFE gradient calculation incorrect');
    assertions.approximately(csmeGradient, expectedCsmeGradient, 0.0001, 'CSME gradient calculation incorrect');
  }, {
    testingType: 'Meter Validation',
    coherenceImpact: 'positive',
    domains: ['cyber', 'financial', 'biological']
  });

  // Test: Emergent Intelligence Detection
  suite.nepiTest('should detect emergent intelligence', async () => {
    // Set up test data
    const testData = {
      comphyons: 3.5,
      coherence: 0.85,
      entropyContainment: 0.01,
      crossDomainSynchronization: 0.9
    };
    
    // Detect emergent intelligence
    const result = mockEmergentIntelligenceDetector.detect(testData);
    
    // Assert
    assertions.equal(result.detected, true, 'Failed to detect emergent intelligence');
    assertions.ok(result.confidence > 0.8, 'Low confidence in emergent intelligence detection');
  }, {
    testingType: 'Meter Validation',
    coherenceImpact: 'positive',
    domains: ['universal']
  });

  // Test: Comphyon Threshold Validation
  suite.nepiTest('should validate Comphyon thresholds', async () => {
    // Set up test thresholds
    const thresholds = {
      warning: 2.5,
      critical: 5.0,
      emergency: 10.0
    };
    
    // Validate thresholds
    const validationResult = mockComphyonMeter.validateThresholds(thresholds);
    
    // Assert
    assertions.equal(validationResult.isValid, true, 'Threshold validation failed');
    assertions.equal(validationResult.warnings.length, 0, 'Unexpected warnings in threshold validation');
  }, {
    testingType: 'Meter Validation',
    coherenceImpact: 'positive',
    domains: ['universal']
  });

  // Test: Real-time Monitoring
  suite.nepiTest('should monitor Comphyons in real-time', async () => {
    // Start monitoring
    mockComphyonMeter.startMonitoring();
    
    // Generate some test data
    for (let i = 0; i < 5; i++) {
      mockComphyonMeter.addMeasurement({
        comphyons: 1.5 + (Math.random() * 0.5),
        timestamp: Date.now() + (i * 1000)
      });
    }
    
    // Get monitoring results
    const monitoringResults = mockComphyonMeter.getMonitoringResults();
    
    // Stop monitoring
    mockComphyonMeter.stopMonitoring();
    
    // Assert
    assertions.equal(monitoringResults.measurements.length, 5, 'Incorrect number of measurements');
    assertions.ok(monitoringResults.averageComphyons > 0, 'Invalid average Comphyons');
    assertions.ok(monitoringResults.maxComphyons > 0, 'Invalid max Comphyons');
  }, {
    testingType: 'Meter Validation',
    coherenceImpact: 'positive',
    domains: ['universal']
  });

  return suite;
}

/**
 * Create a mock Comphyon Meter for testing
 * @returns {Object} Mock Comphyon Meter
 */
function createMockComphyonMeter() {
  return {
    _monitoring: false,
    _measurements: [],
    
    calculateComphyons(csdeGradient, csfeGradient, csmeEnergy) {
      // Comphyon formula: ((dE_CSDE × dE_CSFE) × log(E_CSME)) / 166000
      return ((csdeGradient * csfeGradient) * Math.log(csmeEnergy)) / 166000;
    },
    
    validateThresholds(thresholds) {
      // Validate thresholds
      const warnings = [];
      
      if (thresholds.warning >= thresholds.critical) {
        warnings.push('Warning threshold should be less than critical threshold');
      }
      
      if (thresholds.critical >= thresholds.emergency) {
        warnings.push('Critical threshold should be less than emergency threshold');
      }
      
      return {
        isValid: warnings.length === 0,
        warnings
      };
    },
    
    startMonitoring() {
      this._monitoring = true;
      this._measurements = [];
    },
    
    stopMonitoring() {
      this._monitoring = false;
    },
    
    addMeasurement(measurement) {
      if (this._monitoring) {
        this._measurements.push(measurement);
      }
    },
    
    getMonitoringResults() {
      // Calculate statistics
      const comphyons = this._measurements.map(m => m.comphyons);
      const averageComphyons = comphyons.reduce((sum, c) => sum + c, 0) / comphyons.length;
      const maxComphyons = Math.max(...comphyons);
      
      return {
        measurements: [...this._measurements],
        averageComphyons,
        maxComphyons
      };
    }
  };
}

/**
 * Create a mock Domain Energy Calculator for testing
 * @returns {Object} Mock Domain Energy Calculator
 */
function createMockDomainEnergyCalculator() {
  return {
    calculateCSDE(A1, D) {
      // CSDE energy formula: E_CSDE = A1 × D
      return A1 * D;
    },
    
    calculateCSFE(A2, P) {
      // CSFE energy formula: E_CSFE = A2 × P
      return A2 * P;
    },
    
    calculateCSME(T, I) {
      // CSME energy formula: E_CSME = T × I
      return T * I;
    },
    
    calculateGradient(energy, deltaEnergy) {
      // Gradient formula: dE = ΔE / E
      return deltaEnergy / energy;
    }
  };
}

/**
 * Create a mock Emergent Intelligence Detector for testing
 * @returns {Object} Mock Emergent Intelligence Detector
 */
function createMockEmergentIntelligenceDetector() {
  return {
    detect(data) {
      // Simple detection algorithm
      let score = 0;
      
      // Score based on Comphyons
      if (data.comphyons > 3.0) {
        score += 0.4;
      } else if (data.comphyons > 1.5) {
        score += 0.2;
      }
      
      // Score based on coherence
      if (data.coherence > 0.8) {
        score += 0.3;
      } else if (data.coherence > 0.6) {
        score += 0.15;
      }
      
      // Score based on entropy containment
      if (data.entropyContainment < 0.015) {
        score += 0.2;
      } else if (data.entropyContainment < 0.03) {
        score += 0.1;
      }
      
      // Score based on cross-domain synchronization
      if (data.crossDomainSynchronization > 0.85) {
        score += 0.1;
      } else if (data.crossDomainSynchronization > 0.7) {
        score += 0.05;
      }
      
      return {
        detected: score > 0.5,
        confidence: score,
        threshold: 0.5
      };
    }
  };
}

module.exports = { createMeterTestSuite };
