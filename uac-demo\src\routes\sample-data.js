/**
 * Sample Data for UAC Demo
 * 
 * This module provides sample data for the UAC demo.
 */

// HIPAA Sample Data
const hipaaSamples = {
  // Compliant sample - No PHI, encrypted, with authorization
  compliant: {
    patientId: "ENCRYPTED-12345",
    recordType: "Summary",
    dataStatus: "encrypted",
    authorization: true,
    metadata: {
      recordCount: 5,
      lastUpdated: "2023-05-15"
    },
    summaryData: {
      totalVisits: 3,
      averageDuration: 25
    }
  },
  
  // Non-compliant sample - Contains PHI, not encrypted, no authorization
  nonCompliant: {
    patientId: "12345",
    name: "<PERSON>",
    dob: "1980-01-01",
    medicalRecordNumber: "MRN-67890",
    diagnosis: "Hypertension",
    medications: [
      "Lisinopril 10mg",
      "Aspirin 81mg"
    ],
    ssn: "***********"
  },
  
  // Partially compliant - Contains some PHI but has authorization
  partiallyCompliant: {
    patientId: "PT-98765",
    recordType: "Medical",
    consent: {
      status: "approved",
      date: "2023-01-15"
    },
    diagnosis: "Seasonal allergies",
    treatment: "OTC antihistamines",
    notes: "Patient reports improvement with current regimen"
  }
};

// GDPR Sample Data
const gdprSamples = {
  // Compliant sample - Anonymized, with consent, within retention period
  compliant: {
    userId: "user-anonymized-123",
    anonymized: true,
    location: "anonymized-region-EU",
    preferences: {
      marketing: true,
      analytics: false
    },
    consent: true,
    lastConsent: new Date().toISOString(),
    lastVisit: new Date().toISOString()
  },
  
  // Non-compliant sample - Contains PII, no consent, beyond retention period
  nonCompliant: {
    userId: "user123",
    name: "Jane Smith",
    email: "<EMAIL>",
    address: "123 Main St, London, UK",
    phoneNumber: "+44 1234 567890",
    location: "London, UK",
    lastVisit: "2020-01-15" // More than 12 months ago
  },
  
  // Partially compliant - Contains some PII but has consent
  partiallyCompliant: {
    userId: "user456",
    preferences: {
      marketing: true,
      analytics: true
    },
    location: "anonymized-region-EU",
    lastConsent: new Date().toISOString(),
    lastVisit: new Date().toISOString(),
    email: "<EMAIL>"
  }
};

// PCI DSS Sample Data
const pciSamples = {
  // Compliant sample - No card data, or properly masked
  compliant: {
    customerId: "cust123",
    amount: 99.99,
    currency: "USD",
    paymentMethod: "tokenized",
    cardNumber: "****-****-****-1234",
    encrypted: true,
    billingAddress: {
      country: "USA"
    }
  },
  
  // Non-compliant sample - Contains full card data, CVV
  nonCompliant: {
    customerId: "cust123",
    amount: 99.99,
    currency: "USD",
    cardNumber: "4111 1111 1111 1111",
    expirationDate: "12/25",
    cvv: "123",
    billingAddress: {
      street: "456 Oak Ave",
      city: "New York",
      state: "NY",
      zip: "10001",
      country: "USA"
    }
  },
  
  // Partially compliant - Masked card number but still has CVV
  partiallyCompliant: {
    customerId: "cust123",
    amount: 99.99,
    currency: "USD",
    cardNumber: "****-****-****-1111",
    expirationDate: "12/25",
    cvv: "123",
    encrypted: true,
    billingAddress: {
      country: "USA"
    }
  }
};

// Multi-API Sample Data
const multiApiSamples = {
  // All compliant
  allCompliant: {
    weather: {
      location: "anonymized-region-EU",
      temperature: 72,
      conditions: "Sunny",
      anonymized: true
    },
    github: {
      repositories: 120,
      stars: 1500,
      followers: 300,
      anonymized: true
    },
    health: {
      patientId: "ENCRYPTED-12345",
      dataStatus: "encrypted",
      authorization: true,
      appointments: 5,
      prescriptions: 2
    }
  },
  
  // All non-compliant
  allNonCompliant: {
    weather: {
      location: "New York, NY",
      temperature: 72,
      conditions: "Sunny",
      userAddress: "123 Main St, New York, NY"
    },
    github: {
      repositories: 120,
      stars: 1500,
      followers: 300,
      email: "<EMAIL>",
      name: "John Developer"
    },
    health: {
      patientId: "12345",
      name: "John Doe",
      diagnosis: "Hypertension",
      appointments: 5,
      prescriptions: 2,
      lastVisit: "2023-01-15"
    }
  },
  
  // Mixed compliance
  mixedCompliance: {
    weather: {
      location: "anonymized-region-EU",
      temperature: 72,
      conditions: "Sunny",
      anonymized: true
    },
    github: {
      repositories: 120,
      stars: 1500,
      followers: 300,
      email: "<EMAIL>"
    },
    health: {
      patientId: "ENCRYPTED-12345",
      dataStatus: "encrypted",
      authorization: true,
      diagnosis: "Hypertension",
      appointments: 5,
      prescriptions: 2
    }
  }
};

module.exports = {
  hipaaSamples,
  gdprSamples,
  pciSamples,
  multiApiSamples
};
