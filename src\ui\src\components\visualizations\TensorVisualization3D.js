import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { Box, CircularProgress } from '@mui/material';

/**
 * TensorVisualization3D component
 * 
 * Renders a 3D visualization of a tensor using Three.js
 */
function TensorVisualization3D({
  tensor,
  dimensions = [5, 5, 5],
  options = {
    renderMode: 'medium',
    showAxes: true,
    showGrid: true,
    rotationSpeed: 1,
    colorScheme: 'default'
  },
  width = '100%',
  height = '100%'
}) {
  const containerRef = useRef(null);
  const rendererRef = useRef(null);
  const sceneRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const animationFrameRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize Three.js scene
  useEffect(() => {
    if (!containerRef.current) return;

    try {
      // Create scene
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x121212);
      sceneRef.current = scene;

      // Create camera
      const camera = new THREE.PerspectiveCamera(
        75,
        containerRef.current.clientWidth / containerRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.z = 5;
      cameraRef.current = camera;

      // Create renderer
      const renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(containerRef.current.clientWidth, containerRef.current.clientHeight);
      containerRef.current.appendChild(renderer.domElement);
      rendererRef.current = renderer;

      // Create controls
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.25;
      controls.enableZoom = true;
      controlsRef.current = controls;

      // Add ambient light
      const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(ambientLight);

      // Add directional light
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(1, 1, 1);
      scene.add(directionalLight);

      // Add axes helper if enabled
      if (options.showAxes) {
        const axesHelper = new THREE.AxesHelper(5);
        scene.add(axesHelper);
      }

      // Add grid helper if enabled
      if (options.showGrid) {
        const gridHelper = new THREE.GridHelper(10, 10);
        scene.add(gridHelper);
      }

      // Animation loop
      const animate = () => {
        animationFrameRef.current = requestAnimationFrame(animate);
        
        // Update controls
        if (controlsRef.current) {
          controlsRef.current.update();
        }
        
        // Render scene
        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      };

      // Start animation loop
      animate();

      // Handle window resize
      const handleResize = () => {
        if (!containerRef.current || !cameraRef.current || !rendererRef.current) return;
        
        const width = containerRef.current.clientWidth;
        const height = containerRef.current.clientHeight;
        
        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
        
        rendererRef.current.setSize(width, height);
      };

      window.addEventListener('resize', handleResize);

      // Visualization is ready
      setIsLoading(false);

      // Clean up
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
        }
        
        if (rendererRef.current && containerRef.current) {
          containerRef.current.removeChild(rendererRef.current.domElement);
        }
        
        if (sceneRef.current) {
          // Dispose of all geometries and materials
          sceneRef.current.traverse((object) => {
            if (object.geometry) {
              object.geometry.dispose();
            }
            
            if (object.material) {
              if (Array.isArray(object.material)) {
                object.material.forEach((material) => material.dispose());
              } else {
                object.material.dispose();
              }
            }
          });
        }
        
        rendererRef.current = null;
        sceneRef.current = null;
        cameraRef.current = null;
        controlsRef.current = null;
      };
    } catch (err) {
      console.error('Error initializing Three.js:', err);
      setError(err.message || 'Error initializing visualization');
      setIsLoading(false);
    }
  }, [options.showAxes, options.showGrid]);

  // Update visualization when tensor data changes
  useEffect(() => {
    if (!sceneRef.current || !tensor || isLoading) return;

    try {
      // Remove existing tensor visualization
      const existingTensor = sceneRef.current.getObjectByName('tensor');
      if (existingTensor) {
        sceneRef.current.remove(existingTensor);
        
        // Dispose of geometry and material
        if (existingTensor.geometry) {
          existingTensor.geometry.dispose();
        }
        
        if (existingTensor.material) {
          if (Array.isArray(existingTensor.material)) {
            existingTensor.material.forEach((material) => material.dispose());
          } else {
            existingTensor.material.dispose();
          }
        }
      }

      // Create tensor visualization
      const tensorGroup = new THREE.Group();
      tensorGroup.name = 'tensor';

      // Get tensor values
      const values = tensor.values || [];
      
      // Determine dimensions
      let [width, height, depth] = dimensions;
      
      // If tensor is 1D, create a line
      if (height === 1 && depth === 1) {
        const points = [];
        const colors = [];
        const colorMap = getColorMap(options.colorScheme);
        
        for (let i = 0; i < values.length; i++) {
          const x = (i / (values.length - 1)) * width - width / 2;
          const y = values[i] * 2 - 1; // Scale to [-1, 1]
          const z = 0;
          
          points.push(new THREE.Vector3(x, y, z));
          
          // Add color based on value
          const color = colorMap(values[i]);
          colors.push(color.r / 255, color.g / 255, color.b / 255);
        }
        
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        
        const material = new THREE.LineBasicMaterial({
          vertexColors: true,
          linewidth: 2
        });
        
        const line = new THREE.Line(geometry, material);
        tensorGroup.add(line);
        
        // Add points
        const pointGeometry = new THREE.SphereGeometry(0.1, 16, 16);
        
        for (let i = 0; i < values.length; i++) {
          const x = (i / (values.length - 1)) * width - width / 2;
          const y = values[i] * 2 - 1; // Scale to [-1, 1]
          const z = 0;
          
          const color = colorMap(values[i]);
          const material = new THREE.MeshBasicMaterial({
            color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255)
          });
          
          const point = new THREE.Mesh(pointGeometry, material);
          point.position.set(x, y, z);
          tensorGroup.add(point);
        }
      }
      // If tensor is 2D, create a surface
      else if (depth === 1) {
        const geometry = new THREE.PlaneGeometry(width, height, width - 1, height - 1);
        const vertices = geometry.attributes.position.array;
        const colorMap = getColorMap(options.colorScheme);
        const colors = [];
        
        // Update vertices based on tensor values
        for (let i = 0; i < height; i++) {
          for (let j = 0; j < width; j++) {
            const index = i * width + j;
            const vertexIndex = index * 3;
            
            // Set z position based on tensor value
            if (index < values.length) {
              vertices[vertexIndex + 2] = values[index] * 2 - 1; // Scale to [-1, 1]
              
              // Add color based on value
              const color = colorMap(values[index]);
              colors.push(color.r / 255, color.g / 255, color.b / 255);
            } else {
              vertices[vertexIndex + 2] = 0;
              colors.push(0.5, 0.5, 0.5); // Gray for missing values
            }
          }
        }
        
        geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));
        
        const material = new THREE.MeshPhongMaterial({
          vertexColors: true,
          side: THREE.DoubleSide,
          flatShading: options.renderMode === 'low'
        });
        
        const surface = new THREE.Mesh(geometry, material);
        surface.rotation.x = -Math.PI / 2;
        surface.position.set(-width / 2, 0, -height / 2);
        tensorGroup.add(surface);
      }
      // If tensor is 3D, create a volume
      else {
        const colorMap = getColorMap(options.colorScheme);
        const cubeSize = 0.8;
        const geometry = new THREE.BoxGeometry(cubeSize, cubeSize, cubeSize);
        
        for (let i = 0; i < depth; i++) {
          for (let j = 0; j < height; j++) {
            for (let k = 0; k < width; k++) {
              const index = i * (width * height) + j * width + k;
              
              if (index < values.length) {
                const value = values[index];
                const color = colorMap(value);
                const material = new THREE.MeshPhongMaterial({
                  color: new THREE.Color(color.r / 255, color.g / 255, color.b / 255),
                  transparent: true,
                  opacity: value * 0.8 + 0.2 // Scale opacity based on value
                });
                
                const cube = new THREE.Mesh(geometry, material);
                cube.position.set(
                  k - width / 2 + cubeSize / 2,
                  i - depth / 2 + cubeSize / 2,
                  j - height / 2 + cubeSize / 2
                );
                
                tensorGroup.add(cube);
              }
            }
          }
        }
      }

      // Add tensor to scene
      sceneRef.current.add(tensorGroup);

      // Adjust camera position based on tensor dimensions
      if (cameraRef.current) {
        const maxDimension = Math.max(width, height, depth);
        cameraRef.current.position.z = maxDimension * 2;
        
        if (controlsRef.current) {
          controlsRef.current.update();
        }
      }
    } catch (err) {
      console.error('Error updating tensor visualization:', err);
      setError(err.message || 'Error updating visualization');
    }
  }, [tensor, dimensions, options.colorScheme, options.renderMode, isLoading]);

  // Update rotation speed
  useEffect(() => {
    if (!sceneRef.current || isLoading) return;

    const tensorGroup = sceneRef.current.getObjectByName('tensor');
    if (!tensorGroup) return;

    // Clear existing rotation animation
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Animation loop with rotation
    const animate = () => {
      animationFrameRef.current = requestAnimationFrame(animate);
      
      // Rotate tensor based on rotation speed
      if (tensorGroup && options.rotationSpeed > 0) {
        tensorGroup.rotation.y += 0.01 * options.rotationSpeed;
      }
      
      // Update controls
      if (controlsRef.current) {
        controlsRef.current.update();
      }
      
      // Render scene
      if (rendererRef.current && sceneRef.current && cameraRef.current) {
        rendererRef.current.render(sceneRef.current, cameraRef.current);
      }
    };

    // Start animation loop
    animate();

    // Clean up
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [options.rotationSpeed, isLoading]);

  // Helper function to get color map based on color scheme
  const getColorMap = (colorScheme) => {
    switch (colorScheme) {
      case 'rainbow':
        return (value) => {
          const h = (1 - value) * 240; // Hue (0 to 240)
          const s = 1; // Saturation
          const l = 0.5; // Lightness
          
          return hslToRgb(h, s, l);
        };
        
      case 'heatmap':
        return (value) => {
          const r = Math.floor(value * 255);
          const g = Math.floor((1 - Math.abs(value - 0.5) * 2) * 255);
          const b = Math.floor((1 - value) * 255);
          
          return { r, g, b };
        };
        
      case 'grayscale':
        return (value) => {
          const intensity = Math.floor(value * 255);
          return { r: intensity, g: intensity, b: intensity };
        };
        
      case 'default':
      default:
        return (value) => {
          if (value < 0.33) {
            return { r: 0, g: Math.floor(value * 3 * 255), b: 255 };
          } else if (value < 0.66) {
            return { r: 0, g: 255, b: Math.floor((1 - (value - 0.33) * 3) * 255) };
          } else {
            return { r: Math.floor((value - 0.66) * 3 * 255), g: 255, b: 0 };
          }
        };
    }
  };

  // Helper function to convert HSL to RGB
  const hslToRgb = (h, s, l) => {
    let r, g, b;

    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1/6) return p + (q - p) * 6 * t;
        if (t < 1/2) return q;
        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
        return p;
      };

      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      
      r = hue2rgb(p, q, (h / 360) + 1/3);
      g = hue2rgb(p, q, h / 360);
      b = hue2rgb(p, q, (h / 360) - 1/3);
    }

    return {
      r: Math.round(r * 255),
      g: Math.round(g * 255),
      b: Math.round(b * 255)
    };
  };

  return (
    <Box
      ref={containerRef}
      sx={{
        width,
        height,
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 1,
        bgcolor: 'background.paper'
      }}
    >
      {isLoading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1
          }}
        >
          <CircularProgress />
        </Box>
      )}
      
      {error && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1,
            color: 'error.main',
            p: 2,
            textAlign: 'center'
          }}
        >
          {error}
        </Box>
      )}
    </Box>
  );
}

export default TensorVisualization3D;
