# Quantum Monitoring Dashboard Runner
# This script runs the Quantum Monitoring Dashboard example

# Function to display colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$ForegroundColor = "White"
    )

    $originalColor = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $Message
    $host.UI.RawUI.ForegroundColor = $originalColor
}

# Create necessary directories
Write-ColorOutput "Creating necessary directories..." "Yellow"
$monitoringDir = ".\monitoring_results"
if (-not (Test-Path $monitoringDir)) {
    New-Item -ItemType Directory -Path $monitoringDir | Out-Null
}

# Display banner
Write-ColorOutput "`n====================================================" "Cyan"
Write-ColorOutput "       QUANTUM MONITORING DASHBOARD" "Cyan"
Write-ColorOutput "====================================================" "Cyan"
Write-ColorOutput "Real-time monitoring of quantum resilience metrics," "Cyan"
Write-ColorOutput "including PI-10-cubed drift, entanglement health," "Cyan"
Write-ColorOutput "and PSI-x containment with predictive forecasting." "Cyan"
Write-ColorOutput "====================================================`n" "Cyan"

# Run the quantum monitoring example
Write-ColorOutput "Running Quantum Monitoring Dashboard..." "Green"
try {
    node examples/quantum-monitoring-example.js
    $result = $LASTEXITCODE

    if ($result -eq 0) {
        Write-ColorOutput "`nQuantum Monitoring completed successfully!" "Green"
    } else {
        Write-ColorOutput "`nQuantum Monitoring failed. Check the logs for details." "Red"
    }
} catch {
    Write-ColorOutput "Error running Quantum Monitoring: $_" "Red"
    $result = 1
}

# Find the dashboard HTML
Write-ColorOutput "`nLocating dashboard HTML..." "Yellow"
$dashboard = Get-ChildItem -Path $monitoringDir -Filter "quantum-monitoring-dashboard.html" -ErrorAction SilentlyContinue

if ($dashboard) {
    Write-ColorOutput "Dashboard found: $($dashboard.FullName)" "Green"

    # Open the dashboard in the default browser
    Write-ColorOutput "`nOpening dashboard in browser..." "Yellow"
    Start-Process $dashboard.FullName
} else {
    Write-ColorOutput "No dashboard found in $monitoringDir" "Red"
}

# Display system status
Write-ColorOutput "`n[QUANTUM SHIELD]" "Cyan"
Write-ColorOutput "  ├─ Core: ✅ 100% operational" "Green"
Write-ColorOutput "  ├─ Operations: ⚠️ Needs patching" "Yellow"
Write-ColorOutput "  └─ Adversarial: 🚨 Critical fixes required" "Red"

# Display summary
Write-ColorOutput "`n====================================================" "Cyan"
Write-ColorOutput "                    SUMMARY" "Cyan"
Write-ColorOutput "====================================================" "Cyan"
if ($result -eq 0) {
    Write-ColorOutput "Quantum Monitoring: COMPLETED" "Green"
} else {
    Write-ColorOutput "Quantum Monitoring: FAILED" "Red"
}
Write-ColorOutput "====================================================`n" "Cyan"

# Exit with appropriate code
exit $result
