<!DOCTYPE html>
<html>
<head>
    <title>Mermaid Test</title>
    <!-- Using a different CDN -->
    <script src="https://unpkg.com/mermaid@8.14.0/dist/mermaid.min.js"></script>
    <style>
        body { font-family: Arial; padding: 20px; }
        #diagram { border: 1px solid #ccc; padding: 20px; margin: 20px 0; }
        button { padding: 10px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Simple Mermaid Test</h1>
    <button onclick="render()">Render Diagram</button>
    <button onclick="downloadSVG()">Download SVG</button>
    
    <div id="diagram">
        <!-- Diagram will appear here -->
    </div>

    <script>
        // Simple test diagram
        const diagram = `
graph TD
    A[Start] --> B{Is it working?}
    B -->|Yes| C[Great!]
    B -->|No| D[Let's fix it]
    D --> E[Check console for errors]`;

        // Initialize Mermaid
        mermaid.initialize({ startOnLoad: false });
        
        // Initial render
        render();
        
        function render() {
            document.getElementById('diagram').innerHTML = diagram;
            mermaid.init(undefined, document.getElementById('diagram'));
        }
        
        function downloadSVG() {
            const svg = document.querySelector('#diagram svg');
            if (!svg) {
                alert('No SVG found. Try rendering first.');
                return;
            }
            
            const serializer = new XMLSerializer();
            let source = serializer.serializeToString(svg);
            
            // Add namespaces if not present
            if(!source.match(/^<svg[^>]+xmlns="http\:\/\/www\.w3\.org\/2000\/svg"/)){
                source = source.replace(/^<svg/, '<svg xmlns="http://www.w3.org/2000/svg"');
            }
            
            // Add XML declaration
            source = '<?xml version="1.0" standalone="no"?>\r\n' + source;
            
            // Create download link
            const url = "data:image/svg+xml;charset=utf-8," + encodeURIComponent(source);
            const link = document.createElement('a');
            link.download = 'test_diagram.svg';
            link.href = url;
            link.click();
        }
        
        // Log any errors
        window.addEventListener('error', function(e) {
            console.error('Error:', e.message, 'in', e.filename, 'line', e.lineno);
        });
    </script>
</body>
</html>
