/**
 * NovaConnect Security Scan
 * 
 * This script runs security scans on the NovaConnect codebase,
 * including SAST (Static Application Security Testing) and
 * dependency vulnerability scanning.
 * 
 * Usage:
 *   npm run security:scan
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  outputDir: path.join(__dirname, '../../security-reports'),
  eslintConfig: path.join(__dirname, '../../.eslintrc.js'),
  nodeModulesDir: path.join(__dirname, '../../node_modules'),
  srcDir: path.join(__dirname, '../../src')
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * Run ESLint security scan
 */
function runEslintScan() {
  console.log('Running ESLint security scan...');
  
  try {
    const outputFile = path.join(config.outputDir, 'eslint-security.json');
    
    // Run ESLint with security plugins
    const command = `npx eslint --config ${config.eslintConfig} --plugin security --rule 'security/detect-unsafe-regex: error' --rule 'security/detect-buffer-noassert: error' --rule 'security/detect-child-process: error' --rule 'security/detect-disable-mustache-escape: error' --rule 'security/detect-eval-with-expression: error' --rule 'security/detect-no-csrf-before-method-override: error' --rule 'security/detect-non-literal-fs-filename: error' --rule 'security/detect-non-literal-regexp: error' --rule 'security/detect-non-literal-require: error' --rule 'security/detect-object-injection: error' --rule 'security/detect-possible-timing-attacks: error' --rule 'security/detect-pseudoRandomBytes: error' --rule 'security/detect-unsafe-regex: error' --format json ${config.srcDir} > ${outputFile}`;
    
    execSync(command, { stdio: 'inherit' });
    
    console.log(`ESLint security scan completed. Results saved to ${outputFile}`);
    return true;
  } catch (error) {
    console.error('ESLint security scan failed:', error.message);
    return false;
  }
}

/**
 * Run npm audit for dependency vulnerabilities
 */
function runNpmAudit() {
  console.log('Running npm audit...');
  
  try {
    const outputFile = path.join(config.outputDir, 'npm-audit.json');
    
    // Run npm audit
    const command = `npm audit --json > ${outputFile}`;
    
    execSync(command, { stdio: 'inherit' });
    
    console.log(`npm audit completed. Results saved to ${outputFile}`);
    return true;
  } catch (error) {
    // npm audit returns non-zero exit code if vulnerabilities are found
    // We still want to capture the output
    console.warn('npm audit found vulnerabilities. Check the report for details.');
    return true;
  }
}

/**
 * Run Snyk security scan
 */
function runSnykScan() {
  console.log('Running Snyk security scan...');
  
  try {
    const outputFile = path.join(config.outputDir, 'snyk-report.json');
    
    // Check if Snyk is installed
    try {
      execSync('npx snyk --version', { stdio: 'ignore' });
    } catch (error) {
      console.log('Snyk not found. Installing...');
      execSync('npm install -g snyk', { stdio: 'inherit' });
    }
    
    // Run Snyk test
    const command = `npx snyk test --json > ${outputFile}`;
    
    execSync(command, { stdio: 'inherit' });
    
    console.log(`Snyk security scan completed. Results saved to ${outputFile}`);
    return true;
  } catch (error) {
    // Snyk returns non-zero exit code if vulnerabilities are found
    // We still want to capture the output
    console.warn('Snyk found vulnerabilities. Check the report for details.');
    return true;
  }
}

/**
 * Run SonarQube scan
 */
function runSonarQubeScan() {
  console.log('Running SonarQube scan...');
  
  try {
    // Check if SonarQube scanner is installed
    try {
      execSync('npx sonar-scanner --version', { stdio: 'ignore' });
    } catch (error) {
      console.log('SonarQube scanner not found. Installing...');
      execSync('npm install -g sonar-scanner', { stdio: 'inherit' });
    }
    
    // Run SonarQube scan
    const command = `npx sonar-scanner -Dsonar.projectKey=novaconnect -Dsonar.sources=${config.srcDir} -Dsonar.sourceEncoding=UTF-8`;
    
    execSync(command, { stdio: 'inherit' });
    
    console.log('SonarQube scan completed.');
    return true;
  } catch (error) {
    console.error('SonarQube scan failed:', error.message);
    return false;
  }
}

/**
 * Generate security report summary
 */
function generateSummary() {
  console.log('Generating security report summary...');
  
  const summaryFile = path.join(config.outputDir, 'security-summary.md');
  
  let summary = `# NovaConnect Security Scan Summary\n\n`;
  summary += `Generated on: ${new Date().toISOString()}\n\n`;
  
  // Add ESLint results
  const eslintFile = path.join(config.outputDir, 'eslint-security.json');
  if (fs.existsSync(eslintFile)) {
    try {
      const eslintResults = JSON.parse(fs.readFileSync(eslintFile, 'utf8'));
      const issueCount = eslintResults.reduce((count, file) => count + file.messages.length, 0);
      
      summary += `## ESLint Security Scan\n\n`;
      summary += `- Total issues found: ${issueCount}\n`;
      
      if (issueCount > 0) {
        summary += `- Issues by severity:\n`;
        
        const severityCounts = eslintResults.reduce((counts, file) => {
          file.messages.forEach(msg => {
            counts[msg.severity] = (counts[msg.severity] || 0) + 1;
          });
          return counts;
        }, {});
        
        for (const [severity, count] of Object.entries(severityCounts)) {
          const severityName = severity === '2' ? 'Error' : 'Warning';
          summary += `  - ${severityName}: ${count}\n`;
        }
      }
      
      summary += `\n`;
    } catch (error) {
      summary += `## ESLint Security Scan\n\n`;
      summary += `- Error parsing results: ${error.message}\n\n`;
    }
  }
  
  // Add npm audit results
  const npmAuditFile = path.join(config.outputDir, 'npm-audit.json');
  if (fs.existsSync(npmAuditFile)) {
    try {
      const auditResults = JSON.parse(fs.readFileSync(npmAuditFile, 'utf8'));
      
      summary += `## npm Audit\n\n`;
      
      if (auditResults.vulnerabilities) {
        const vulnCount = Object.values(auditResults.vulnerabilities).length;
        summary += `- Total vulnerabilities found: ${vulnCount}\n`;
        
        if (vulnCount > 0) {
          summary += `- Vulnerabilities by severity:\n`;
          
          const severityCounts = {};
          for (const vuln of Object.values(auditResults.vulnerabilities)) {
            severityCounts[vuln.severity] = (severityCounts[vuln.severity] || 0) + 1;
          }
          
          for (const [severity, count] of Object.entries(severityCounts)) {
            summary += `  - ${severity}: ${count}\n`;
          }
        }
      } else {
        summary += `- No vulnerabilities found\n`;
      }
      
      summary += `\n`;
    } catch (error) {
      summary += `## npm Audit\n\n`;
      summary += `- Error parsing results: ${error.message}\n\n`;
    }
  }
  
  // Add Snyk results
  const snykFile = path.join(config.outputDir, 'snyk-report.json');
  if (fs.existsSync(snykFile)) {
    try {
      const snykResults = JSON.parse(fs.readFileSync(snykFile, 'utf8'));
      
      summary += `## Snyk Security Scan\n\n`;
      
      if (snykResults.vulnerabilities) {
        const vulnCount = snykResults.vulnerabilities.length;
        summary += `- Total vulnerabilities found: ${vulnCount}\n`;
        
        if (vulnCount > 0) {
          summary += `- Vulnerabilities by severity:\n`;
          
          const severityCounts = {};
          for (const vuln of snykResults.vulnerabilities) {
            severityCounts[vuln.severity] = (severityCounts[vuln.severity] || 0) + 1;
          }
          
          for (const [severity, count] of Object.entries(severityCounts)) {
            summary += `  - ${severity}: ${count}\n`;
          }
        }
      } else {
        summary += `- No vulnerabilities found\n`;
      }
      
      summary += `\n`;
    } catch (error) {
      summary += `## Snyk Security Scan\n\n`;
      summary += `- Error parsing results: ${error.message}\n\n`;
    }
  }
  
  // Write summary to file
  fs.writeFileSync(summaryFile, summary);
  
  console.log(`Security report summary generated: ${summaryFile}`);
}

/**
 * Main function
 */
async function main() {
  console.log('Starting NovaConnect security scan...');
  
  const results = {
    eslint: runEslintScan(),
    npmAudit: runNpmAudit(),
    snyk: runSnykScan(),
    sonarQube: runSonarQubeScan()
  };
  
  generateSummary();
  
  console.log('\nSecurity scan completed.');
  console.log(`Results saved to ${config.outputDir}`);
  
  // Return non-zero exit code if any scan failed
  if (Object.values(results).some(result => !result)) {
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Security scan failed:', error);
  process.exit(1);
});
