/**
 * API Test Runner
 * 
 * This script runs all API tests for the NovaFuse API Superstore.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testDir: path.join(__dirname),
  resultsDir: path.join(__dirname, '../../test-results/api'),
  jestConfig: path.join(__dirname, '../../jest.config.js'),
  testPattern: '**/*.test.js'
};

// Create results directory if it doesn't exist
if (!fs.existsSync(config.resultsDir)) {
  fs.mkdirSync(config.resultsDir, { recursive: true });
}

// Get all test files
const testFiles = fs.readdirSync(config.testDir)
  .filter(file => file.endsWith('.test.js'))
  .map(file => path.join(config.testDir, file));

console.log(`Found ${testFiles.length} API test files`);

// Run tests
try {
  console.log('Running API tests...');
  
  const command = `npx jest --config=${config.jestConfig} --testMatch=${config.testDir}/${config.testPattern} --json --outputFile=${path.join(config.resultsDir, 'results.json')}`;
  
  execSync(command, { stdio: 'inherit' });
  
  console.log('API tests completed successfully');
} catch (error) {
  console.error('Error running API tests:', error.message);
  process.exit(1);
}
