/**
 * CustomizableDashboard Component
 * 
 * A customizable dashboard component that allows users to add, remove, and rearrange widgets.
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { usePreferences } from '../preferences/PreferencesContext';
import { DashboardPreferences } from '../preferences';
import { DashboardCard, MetricsCard, ChartCard, DataTable, GraphVisualization, HeatmapVisualization, TreemapVisualization, SankeyVisualization } from './index';

/**
 * CustomizableDashboard component
 * 
 * @param {Object} props - Component props
 * @param {string} props.id - Dashboard ID
 * @param {Object} props.data - Dashboard data
 * @param {Function} [props.onRefresh] - Function to call when the dashboard is refreshed
 * @param {boolean} [props.loading=false] - Whether the dashboard is loading
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} CustomizableDashboard component
 */
const CustomizableDashboard = ({
  id,
  data,
  onRefresh,
  loading = false,
  className = '',
  style = {}
}) => {
  const { getDashboardPreferences } = usePreferences();
  const [showPreferences, setShowPreferences] = useState(false);
  const [dashboardData, setDashboardData] = useState(data);
  
  // Get dashboard preferences
  const dashboardPreferences = getDashboardPreferences(id);
  
  // Update dashboard data when props change
  useEffect(() => {
    setDashboardData(data);
  }, [data]);
  
  // Handle refresh
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };
  
  // Toggle preferences
  const togglePreferences = () => {
    setShowPreferences(!showPreferences);
  };
  
  // Render widget based on type
  const renderWidget = (widget) => {
    switch (widget.type) {
      case 'metrics':
        return (
          <MetricsCard
            title={widget.title}
            metrics={dashboardData?.metrics || []}
            columns={widget.settings?.columns || 3}
            loading={loading}
          />
        );
      
      case 'chart':
        return (
          <ChartCard
            title={widget.title}
            chartType={widget.settings?.chartType || 'bar'}
            data={dashboardData?.charts?.[widget.id] || { labels: [], datasets: [] }}
            loading={loading}
            collapsible={true}
            onRefresh={handleRefresh}
          />
        );
      
      case 'alerts':
        return (
          <DashboardCard
            title={widget.title}
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <DataTable
              columns={[
                { field: 'severity', header: 'Severity' },
                { field: 'type', header: 'Type' },
                { field: 'message', header: 'Message' },
                { field: 'timestamp', header: 'Timestamp', render: (value) => new Date(value).toLocaleString() },
                { field: 'status', header: 'Status' }
              ]}
              data={dashboardData?.alerts || []}
              loading={loading}
              emptyMessage="No alerts found"
            />
          </DashboardCard>
        );
      
      case 'graph':
        return (
          <DashboardCard
            title={widget.title}
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className="h-[300px]">
              <GraphVisualization
                data={dashboardData?.graph || { nodes: [], edges: [] }}
                options={{
                  layout: widget.settings?.layout || 'force',
                  nodeSize: widget.settings?.nodeSize || 'value',
                  nodeColor: widget.settings?.nodeColor || 'category',
                  edgeWidth: 'value',
                  interactive: true,
                  zoomable: true,
                  draggable: true,
                  highlightNeighbors: true,
                  showLegend: widget.settings?.showLegend || false
                }}
              />
            </div>
          </DashboardCard>
        );
      
      case 'heatmap':
        return (
          <DashboardCard
            title={widget.title}
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className="h-[300px]">
              <HeatmapVisualization
                data={dashboardData?.heatmap || { x: [], y: [], values: [] }}
                options={{
                  showLegend: widget.settings?.showLegend || true,
                  showLabels: widget.settings?.showLabels || true
                }}
              />
            </div>
          </DashboardCard>
        );
      
      case 'treemap':
        return (
          <DashboardCard
            title={widget.title}
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className="h-[300px]">
              <TreemapVisualization
                data={dashboardData?.treemap || { name: '', children: [] }}
                options={{
                  showLabels: widget.settings?.showLabels || true
                }}
              />
            </div>
          </DashboardCard>
        );
      
      case 'sankey':
        return (
          <DashboardCard
            title={widget.title}
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className="h-[300px]">
              <SankeyVisualization
                data={dashboardData?.sankey || { nodes: [], links: [] }}
                options={{
                  showLabels: widget.settings?.showLabels || true,
                  showLinkLabels: widget.settings?.showLinkLabels || false
                }}
              />
            </div>
          </DashboardCard>
        );
      
      default:
        return (
          <DashboardCard
            title={widget.title}
            collapsible={true}
            onRefresh={handleRefresh}
            loading={loading}
          >
            <div className="p-4 text-center text-textSecondary">
              Unknown widget type: {widget.type}
            </div>
          </DashboardCard>
        );
    }
  };
  
  // Get layout settings
  const layoutSettings = dashboardPreferences.layout || {
    columns: 3,
    rowHeight: 200,
    gap: 16,
    compactType: 'vertical'
  };
  
  // Get widgets
  const widgets = dashboardPreferences.widgets || [];
  
  // Calculate grid template columns
  const gridTemplateColumns = `repeat(${layoutSettings.columns}, 1fr)`;
  
  return (
    <div
      className={`relative ${className}`}
      style={style}
      data-testid="customizable-dashboard"
    >
      {/* Dashboard header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-textPrimary">
            {dashboardPreferences.settings?.title || 'Dashboard'}
          </h2>
          {dashboardPreferences.settings?.description && (
            <p className="text-textSecondary mt-1">
              {dashboardPreferences.settings.description}
            </p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            className="px-4 py-2 bg-surface text-textPrimary border border-divider rounded-md hover:bg-actionHover transition-colors duration-200"
            onClick={togglePreferences}
            data-testid="customize-button"
          >
            Customize
          </button>
          
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={handleRefresh}
            disabled={loading}
            data-testid="refresh-button"
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>
      
      {/* Dashboard content */}
      {widgets.length === 0 ? (
        <div className="bg-surface border border-divider rounded-lg p-8 text-center">
          <p className="text-textSecondary mb-4">
            This dashboard has no widgets yet. Click the "Customize" button to add widgets.
          </p>
          <button
            className="px-4 py-2 bg-primary text-primaryContrast rounded-md hover:bg-primaryDark transition-colors duration-200"
            onClick={togglePreferences}
          >
            Customize Dashboard
          </button>
        </div>
      ) : (
        <div
          className="grid gap-4"
          style={{
            gridTemplateColumns,
            gridAutoRows: `${layoutSettings.rowHeight}px`,
            gap: `${layoutSettings.gap}px`
          }}
        >
          {widgets.map((widget) => (
            <div
              key={widget.id}
              className="h-full"
              style={{
                gridColumn: `span ${Math.min(widget.position?.w || 1, layoutSettings.columns)}`,
                gridRow: `span ${widget.position?.h || 1}`
              }}
            >
              {renderWidget(widget)}
            </div>
          ))}
        </div>
      )}
      
      {/* Dashboard preferences */}
      {showPreferences && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="w-full max-w-4xl max-h-[90vh] overflow-auto">
            <DashboardPreferences
              dashboardId={id}
              onClose={togglePreferences}
            />
          </div>
        </div>
      )}
    </div>
  );
};

CustomizableDashboard.propTypes = {
  id: PropTypes.string.isRequired,
  data: PropTypes.object,
  onRefresh: PropTypes.func,
  loading: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default CustomizableDashboard;
