/**
 * Control Controller
 * 
 * This controller handles API requests for control management.
 */

const controlService = require('../services/controlService');
const logger = require('../utils/logger');
const multer = require('multer');
const path = require('path');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../../uploads'));
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({ storage });

/**
 * Get all controls
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getAllControls(req, res, next) {
  try {
    const { framework, category, search, page = 1, limit = 10 } = req.query;
    
    const filters = {};
    
    if (framework) {
      filters.framework = framework;
    }
    
    if (category) {
      filters.category = category;
    }
    
    if (search) {
      filters.search = search;
    }
    
    const result = await controlService.getAllControls(
      filters,
      parseInt(page, 10),
      parseInt(limit, 10)
    );
    
    res.json(result);
  } catch (error) {
    logger.error('Error getting controls', error);
    next(error);
  }
}

/**
 * Get control by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getControlById(req, res, next) {
  try {
    const { id } = req.params;
    
    const control = await controlService.getControlById(id);
    
    res.json(control);
  } catch (error) {
    logger.error(`Error getting control ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Create control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function createControl(req, res, next) {
  try {
    const controlData = req.body;
    const userId = req.user.id;
    
    const control = await controlService.createControl(controlData, userId);
    
    res.status(201).json(control);
  } catch (error) {
    logger.error('Error creating control', error);
    next(error);
  }
}

/**
 * Update control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function updateControl(req, res, next) {
  try {
    const { id } = req.params;
    const controlData = req.body;
    const userId = req.user.id;
    
    const control = await controlService.updateControl(id, controlData, userId);
    
    res.json(control);
  } catch (error) {
    logger.error(`Error updating control ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Delete control
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function deleteControl(req, res, next) {
  try {
    const { id } = req.params;
    
    const control = await controlService.deleteControl(id);
    
    res.json({
      success: true,
      message: `Control ${id} deleted successfully`,
      control
    });
  } catch (error) {
    logger.error(`Error deleting control ${req.params.id}`, error);
    next(error);
  }
}

/**
 * Import controls
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function importControls(req, res, next) {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }
    
    const userId = req.user.id;
    
    const result = await controlService.importControls(req.file, userId);
    
    res.json(result);
  } catch (error) {
    logger.error('Error importing controls', error);
    next(error);
  }
}

/**
 * Export controls
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function exportControls(req, res, next) {
  try {
    const { framework, format = 'json' } = req.query;
    
    const filters = {};
    
    if (framework) {
      filters.framework = framework;
    }
    
    const result = await controlService.exportControls(filters, format);
    
    res.setHeader('Content-Type', result.contentType);
    res.setHeader('Content-Disposition', `attachment; filename=${result.filename}`);
    
    if (format === 'excel') {
      // Excel is binary
      res.end(result.content, 'binary');
    } else {
      // CSV, JSON are strings
      res.send(result.content);
    }
  } catch (error) {
    logger.error('Error exporting controls', error);
    next(error);
  }
}

/**
 * Get control mapping
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
async function getControlMapping(req, res, next) {
  try {
    const { sourceFramework, targetFramework } = req.query;
    
    if (!sourceFramework || !targetFramework) {
      return res.status(400).json({
        success: false,
        error: 'Source and target frameworks are required'
      });
    }
    
    const mapping = await controlService.getControlMapping(sourceFramework, targetFramework);
    
    res.json(mapping);
  } catch (error) {
    logger.error('Error getting control mapping', error);
    next(error);
  }
}

// Middleware for file upload
const uploadMiddleware = upload.single('file');

module.exports = {
  getAllControls,
  getControlById,
  createControl,
  updateControl,
  deleteControl,
  importControls: [uploadMiddleware, importControls],
  exportControls,
  getControlMapping
};
