/**
 * NovaFuse Universal API Connector - Sanitization Middleware
 * 
 * This module provides middleware for sanitizing request data.
 */

const { createLogger } = require('../utils/logger');

const logger = createLogger('sanitizer');

/**
 * Sanitize a string to prevent XSS attacks
 * 
 * @param {string} str - The string to sanitize
 * @returns {string} - The sanitized string
 */
function sanitizeString(str) {
  if (typeof str !== 'string') {
    return str;
  }
  
  return str
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Recursively sanitize an object
 * 
 * @param {Object} obj - The object to sanitize
 * @returns {Object} - The sanitized object
 */
function sanitizeObject(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }
  
  const sanitized = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

/**
 * Sanitization middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function sanitize(req, res, next) {
  try {
    // Sanitize request body
    if (req.body) {
      req.body = sanitizeObject(req.body);
    }
    
    // Sanitize request query
    if (req.query) {
      req.query = sanitizeObject(req.query);
    }
    
    // Sanitize request params
    if (req.params) {
      req.params = sanitizeObject(req.params);
    }
    
    next();
  } catch (error) {
    logger.error('Sanitization error:', { error });
    next(error);
  }
}

module.exports = {
  sanitize,
  sanitizeString,
  sanitizeObject
};
