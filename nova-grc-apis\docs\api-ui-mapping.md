# NovaFuse API-UI Mapping

This document maps the NovaGRC APIs to the NovaFuse UI components and product tiers.

## Product Tiers

NovaFuse offers the following product tiers:

1. **NovaPrime**: Premium, full-featured offering with all capabilities
2. **NovaCore**: Mid-tier offering with essential functionality
3. **NovaShield**: Security-focused offering
4. **NovaLearn**: Training and education-focused offering

## API-UI Mapping

### Privacy Management API

| API Endpoint | UI Component | NovaPrime | NovaCore | NovaShield | NovaLearn |
|--------------|-------------|-----------|----------|------------|-----------|
| `/privacy/management/processing-activities` | Data Processing Registry | ✅ | ✅ | ❌ | ❌ |
| `/privacy/management/subject-requests` | DSR Management | ✅ | ✅ | ❌ | ❌ |
| `/privacy/management/consent-records` | Consent Management | ✅ | ✅ | ❌ | ❌ |
| `/privacy/management/privacy-notices` | Privacy Notice Management | ✅ | ✅ | ❌ | ❌ |
| `/privacy/management/data-breaches` | Data Breach Management | ✅ | ✅ | ✅ | ❌ |
| `/privacy/management/impact-assessment` | PIA/DPIA Tool | ✅ | ❌ | ❌ | ❌ |
| `/privacy/management/reports` | Privacy Reports | ✅ | ✅ | ❌ | ❌ |
| `/privacy/management/notifications` | Privacy Notifications | ✅ | ✅ | ❌ | ❌ |
| `/privacy/management/integrations` | Privacy Integrations | ✅ | ❌ | ❌ | ❌ |

### Regulatory Compliance API

| API Endpoint | UI Component | NovaPrime | NovaCore | NovaShield | NovaLearn |
|--------------|-------------|-----------|----------|------------|-----------|
| `/compliance/regulatory/frameworks` | Compliance Frameworks | ✅ | ✅ | ✅ | ✅ |
| `/compliance/regulatory/requirements` | Compliance Requirements | ✅ | ✅ | ✅ | ✅ |
| `/compliance/regulatory/jurisdictions` | Jurisdictions | ✅ | ✅ | ❌ | ✅ |
| `/compliance/regulatory/changes` | Regulatory Changes | ✅ | ✅ | ❌ | ✅ |
| `/compliance/regulatory/reports` | Compliance Reports | ✅ | ✅ | ✅ | ✅ |
| `/compliance/regulatory/gap-analysis` | Gap Analysis | ✅ | ❌ | ❌ | ❌ |
| `/compliance/regulatory/remediation` | Remediation Tracking | ✅ | ❌ | ❌ | ❌ |

### Security Assessment API

| API Endpoint | UI Component | NovaPrime | NovaCore | NovaShield | NovaLearn |
|--------------|-------------|-----------|----------|------------|-----------|
| `/security/assessment/vulnerabilities` | Vulnerability Management | ✅ | ❌ | ✅ | ❌ |
| `/security/assessment/controls` | Security Controls | ✅ | ❌ | ✅ | ❌ |
| `/security/assessment/threats` | Threat Intelligence | ✅ | ❌ | ✅ | ❌ |
| `/security/assessment/risk-assessments` | Risk Assessments | ✅ | ✅ | ✅ | ❌ |
| `/security/assessment/incidents` | Security Incidents | ✅ | ❌ | ✅ | ❌ |
| `/security/assessment/scans` | Security Scans | ✅ | ❌ | ✅ | ❌ |
| `/security/assessment/reports` | Security Reports | ✅ | ❌ | ✅ | ❌ |

### Control Testing API

| API Endpoint | UI Component | NovaPrime | NovaCore | NovaShield | NovaLearn |
|--------------|-------------|-----------|----------|------------|-----------|
| `/control/testing/controls` | Control Inventory | ✅ | ✅ | ✅ | ❌ |
| `/control/testing/test-plans` | Test Plans | ✅ | ✅ | ✅ | ❌ |
| `/control/testing/test-cases` | Test Cases | ✅ | ✅ | ✅ | ❌ |
| `/control/testing/test-executions` | Test Execution | ✅ | ❌ | ✅ | ❌ |
| `/control/testing/evidence` | Evidence Collection | ✅ | ❌ | ✅ | ❌ |
| `/control/testing/remediation` | Remediation Tracking | ✅ | ❌ | ✅ | ❌ |
| `/control/testing/reports` | Control Reports | ✅ | ✅ | ✅ | ❌ |

### ESG API

| API Endpoint | UI Component | NovaPrime | NovaCore | NovaShield | NovaLearn |
|--------------|-------------|-----------|----------|------------|-----------|
| `/esg/metrics` | ESG Metrics | ✅ | ✅ | ❌ | ❌ |
| `/esg/frameworks` | ESG Frameworks | ✅ | ✅ | ❌ | ❌ |
| `/esg/disclosures` | ESG Disclosures | ✅ | ❌ | ❌ | ❌ |
| `/esg/reports` | ESG Reports | ✅ | ✅ | ❌ | ❌ |
| `/esg/targets` | ESG Targets | ✅ | ❌ | ❌ | ❌ |
| `/esg/stakeholders` | Stakeholder Management | ✅ | ❌ | ❌ | ❌ |
| `/esg/initiatives` | Sustainability Initiatives | ✅ | ❌ | ❌ | ❌ |

### Compliance Automation API

| API Endpoint | UI Component | NovaPrime | NovaCore | NovaShield | NovaLearn |
|--------------|-------------|-----------|----------|------------|-----------|
| `/compliance/automation/workflows` | Compliance Workflows | ✅ | ❌ | ❌ | ❌ |
| `/compliance/automation/tasks` | Compliance Tasks | ✅ | ✅ | ✅ | ❌ |
| `/compliance/automation/schedules` | Compliance Schedules | ✅ | ❌ | ✅ | ❌ |
| `/compliance/automation/integrations` | Compliance Integrations | ✅ | ❌ | ✅ | ❌ |
| `/compliance/automation/reports` | Automation Reports | ✅ | ✅ | ✅ | ❌ |
| `/compliance/automation/notifications` | Automation Notifications | ✅ | ✅ | ✅ | ❌ |
| `/compliance/automation/document-generation` | Document Generation | ✅ | ❌ | ❌ | ❌ |

## NovaAssist AI Integration

### AI Capability Tiers

| AI Capability | NovaPrime | NovaCore | NovaShield | NovaLearn |
|---------------|-----------|----------|------------|-----------|
| Basic Q&A | ✅ | ✅ | ✅ | ✅ |
| Regulatory Guidance | ✅ | ✅ | ✅ | ✅ |
| Data Analysis | ✅ | ✅ | ✅ | ✅ |
| Predictive Analytics | ✅ | ❌ | ❌ | ❌ |
| Custom Model Training | ✅ | ❌ | ❌ | ❌ |
| Advanced NLP | ✅ | ❌ | ❌ | ❌ |
| Automated Reporting | ✅ | ❌ | ❌ | ❌ |
| Security Analysis | ✅ | ❌ | ✅ | ❌ |
| Training Generation | ✅ | ❌ | ❌ | ✅ |
| Interactive Learning | ✅ | ❌ | ❌ | ✅ |

### AI-API Integration Points

| API | AI Integration | NovaPrime | NovaCore | NovaShield | NovaLearn |
|-----|---------------|-----------|----------|------------|-----------|
| Privacy Management | Privacy compliance guidance | ✅ | ✅ | ❌ | ❌ |
| Regulatory Compliance | Regulatory interpretation | ✅ | ✅ | ✅ | ✅ |
| Security Assessment | Threat analysis | ✅ | ❌ | ✅ | ❌ |
| Control Testing | Control recommendations | ✅ | ❌ | ✅ | ❌ |
| ESG | ESG reporting assistance | ✅ | ✅ | ❌ | ❌ |
| Compliance Automation | Workflow optimization | ✅ | ❌ | ❌ | ❌ |

## Feature Flag Implementation

Feature flags will be implemented at both the API and UI levels:

1. **API Feature Flags**:
   - Implemented in middleware
   - Check user's product tier before allowing access to endpoints
   - Return appropriate error messages for unauthorized access

2. **UI Feature Flags**:
   - Implemented using the `nova-ui-feature-flags.js` module
   - Control visibility of UI components based on product tier
   - Provide upgrade prompts for unavailable features

### Feature Flag Configuration Example

```javascript
// Example feature flag configuration
const featureFlags = {
  // Privacy Management API
  'privacy-management-processing-activities': {
    novaPrime: true,
    novaCore: true,
    novaShield: false,
    novaLearn: false
  },
  'privacy-management-impact-assessment': {
    novaPrime: true,
    novaCore: false,
    novaShield: false,
    novaLearn: false
  },
  
  // AI Capabilities
  'ai-predictive-analytics': {
    novaPrime: true,
    novaCore: false,
    novaShield: false,
    novaLearn: false
  },
  'ai-security-analysis': {
    novaPrime: true,
    novaCore: false,
    novaShield: true,
    novaLearn: false
  }
};
```

## Implementation Notes

1. **API Access Control**:
   - All API endpoints should check the user's product tier before processing requests
   - Unauthorized access should return a 403 Forbidden response with information about upgrading

2. **UI Component Visibility**:
   - UI components should check feature flags before rendering
   - Unavailable features should show upgrade prompts

3. **AI Integration**:
   - The AI should check feature flags before providing advanced capabilities
   - When users request unavailable capabilities, the AI should explain the limitations and benefits of upgrading
