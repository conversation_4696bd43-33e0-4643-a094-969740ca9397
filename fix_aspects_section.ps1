# Script to remove "3 Critical Aspects" section from all terms except "Consciousness"

$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force

# Read the content
$content = [System.IO.File]::ReadAllText($dictionaryPath)

# This pattern matches the 3 Critical Aspects section (with or without emoji, and with any amount of whitespace)
$pattern = '(?s)(?<!Consciousness.*?)(\s*[^\n]*Consciousness in Comphyology has 3 Critical Aspects:[\s\S]*?)(?=And all of this can be measured using tools like:|\*\*[^\*]|$|\n\n)'

# Also catch any remaining instances that might have slightly different formatting
$pattern2 = '(?s)(?<!Consciousness.*?)(\s*[^\n]*3 Critical Aspects:[\s\S]*?)(?=\n\n|\*\*[^\*]|$)'

# Remove the patterns from all terms except Consciousness
$modifiedContent = [System.Text.RegularExpressions.Regex]::Replace($content, $pattern, '')
$modifiedContent = [System.Text.RegularExpressions.Regex]::Replace($modifiedContent, $pattern2, '')

# Write the modified content back to the file
[System.IO.File]::WriteAllText($dictionaryPath, $modifiedContent.Trim())

Write-Host "Created backup at: $backupPath"
Write-Host "Removed '3 Critical Aspects' sections from all terms except 'Consciousness'"
Write-Host "Please review the changes in the dictionary file."
