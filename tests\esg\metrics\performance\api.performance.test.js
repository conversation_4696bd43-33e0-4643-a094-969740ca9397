const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/esg/metrics/routes');
const models = require('../../../../apis/esg/metrics/models');

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/governance/esg/metrics', router);

// Generate a large dataset for performance testing
const generateLargeDataset = (count) => {
  const metrics = [];
  for (let i = 0; i < count; i++) {
    metrics.push({
      id: `esg-m-${i.toString().padStart(8, '0')}`,
      name: `Test Metric ${i}`,
      description: `Description for test metric ${i}`,
      category: i % 3 === 0 ? 'environmental' : i % 3 === 1 ? 'social' : 'governance',
      subcategory: `subcategory-${i % 5}`,
      unit: i % 2 === 0 ? 'count' : 'percentage',
      dataType: i % 4 === 0 ? 'numeric' : i % 4 === 1 ? 'percentage' : i % 4 === 2 ? 'boolean' : 'text',
      framework: i % 2 === 0 ? 'GRI' : 'SASB',
      targetValue: `${i * 10}`,
      targetDate: '2025-12-31',
      owner: 'Test Owner',
      status: i % 3 === 0 ? 'active' : i % 3 === 1 ? 'inactive' : 'archived',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  return metrics;
};

// Mock the models with a large dataset
jest.mock('../../../../apis/esg/metrics/models', () => {
  const largeDataset = generateLargeDataset(1000);
  return {
    esgMetrics: largeDataset,
    esgDataPoints: [],
    esgInitiatives: [],
    esgCategories: [
      { id: 'env', name: 'Environmental', description: 'Environmental metrics' },
      { id: 'soc', name: 'Social', description: 'Social metrics' },
      { id: 'gov', name: 'Governance', description: 'Governance metrics' }
    ],
    esgFrameworks: [
      { id: 'gri', name: 'GRI', description: 'Global Reporting Initiative' },
      { id: 'sasb', name: 'SASB', description: 'Sustainability Accounting Standards Board' }
    ]
  };
});

describe('ESG Metrics API Performance Tests', () => {
  // Set a longer timeout for performance tests
  jest.setTimeout(30000);

  describe('GET /governance/esg/metrics', () => {
    it('should handle pagination efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/metrics?page=1&limit=100');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(100);
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
    });

    it('should handle filtering efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/metrics?category=environmental&status=active');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      
      // Response time should be under 200ms
      expect(responseTime).toBeLessThan(200);
    });
  });

  describe('GET /governance/esg/metrics/:id', () => {
    it('should retrieve a specific metric efficiently', async () => {
      const startTime = Date.now();
      
      const response = await request(app).get('/governance/esg/metrics/esg-m-00000500');
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('esg-m-00000500');
      
      // Response time should be under 100ms
      expect(responseTime).toBeLessThan(100);
    });
  });

  describe('Concurrent requests', () => {
    it('should handle multiple concurrent requests efficiently', async () => {
      const startTime = Date.now();
      
      // Make 10 concurrent requests
      const requests = [];
      for (let i = 0; i < 10; i++) {
        requests.push(request(app).get(`/governance/esg/metrics?page=${i+1}&limit=10`));
      }
      
      const responses = await Promise.all(requests);
      
      const endTime = Date.now();
      const totalResponseTime = endTime - startTime;
      
      // All responses should be successful
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
      });
      
      // Total response time for 10 concurrent requests should be under 1000ms
      expect(totalResponseTime).toBeLessThan(1000);
    });
  });
});
