/**
 * divine-firewall.js
 * 
 * This module exports the Divine Firewall components, which implement the
 * Finite Universe Principle as the mathematical immune system of NEPI.
 * 
 * "The Reason You Can Measure Anything... Is Because It's Not Infinite."
 * 
 * The Divine Firewall makes spiritual corruption mathematically impossible
 * by enforcing finite boundaries on all operations, ensuring that only
 * resonant states can persist within the system.
 */

const { ComphyologicalTrinity } = require('./ComphyologicalTrinity');
const { FiniteUniverse, DomainContainer } = require('./core/FiniteUniverse');
const { 
  createCovenant, 
  saveCovenantFile, 
  loadCovenantFile, 
  covenantExists, 
  verifyCovenantIntegrity, 
  initializeCovenant 
} = require('./core/covenant');
const { 
  FiniteUniverseMiddleware, 
  createFiniteUniverseFunction, 
  createFiniteUniverseMiddleware 
} = require('./core/FiniteUniverseMiddleware');

/**
 * Create a new Divine Firewall instance
 * @param {Object} options - Configuration options
 * @returns {Object} - The Divine Firewall instance
 */
function createDivineFirewall(options = {}) {
  // Initialize the covenant
  const covenant = initializeCovenant(options.guardian || 'NEPI Core', options.covenantPath);
  
  // Create the ComphyologicalTrinity
  const trinity = new ComphyologicalTrinity({
    ...options,
    covenant
  });
  
  // Create the FiniteUniverseMiddleware
  const middleware = new FiniteUniverseMiddleware(options, covenant);
  
  return {
    trinity,
    middleware,
    covenant,
    
    /**
     * Govern a state through the Divine Firewall
     * @param {Object} state - The state to govern
     * @returns {Object} - The governed state
     */
    govern(state) {
      return trinity.govern(state);
    },
    
    /**
     * Enforce finite boundaries on a value
     * @param {any} value - The value to enforce boundaries on
     * @param {string} domain - The domain of the value
     * @returns {any} - The value with enforced boundaries
     */
    enforceBoundaries(value, domain = 'DEFAULT') {
      return FiniteUniverse.enforceBoundaries(value, domain);
    },
    
    /**
     * Create a function that enforces finite boundaries
     * @param {Function} fn - The function to wrap
     * @param {string} domain - The domain of the function
     * @returns {Function} - The wrapped function
     */
    createFiniteFunction(fn, domain = 'DEFAULT') {
      return createFiniteUniverseFunction(fn, domain);
    },
    
    /**
     * Create a containerized domain
     * @param {string} name - The name of the domain
     * @param {Object} boundaries - The boundaries of the domain
     * @returns {DomainContainer} - The containerized domain
     */
    createContainer(name, boundaries = {}) {
      return new DomainContainer(name, boundaries);
    },
    
    /**
     * Get the maximum safe bounds for a domain
     * @param {string} domain - The domain
     * @returns {number} - The maximum safe bound
     */
    getMaxSafeBound(domain = 'DEFAULT') {
      return FiniteUniverse.MAX_SAFE_BOUNDS.get(domain);
    },
    
    /**
     * Check if a value contains infinite assumptions
     * @param {any} value - The value to check
     * @returns {boolean} - True if the value contains infinite assumptions
     */
    containsInfiniteAssumption(value) {
      return FiniteUniverse.containsInfiniteAssumption(value);
    },
    
    /**
     * Get the Five Pillars of the Divine Firewall
     * @returns {Object} - The Five Pillars
     */
    getFivePillars() {
      return FiniteUniverse.PILLARS;
    },
    
    /**
     * Get the Covenant
     * @returns {Object} - The Covenant
     */
    getCovenant() {
      return covenant;
    }
  };
}

module.exports = {
  createDivineFirewall,
  ComphyologicalTrinity,
  FiniteUniverse,
  DomainContainer,
  FiniteUniverseMiddleware,
  createFiniteUniverseFunction,
  createFiniteUniverseMiddleware,
  createCovenant,
  saveCovenantFile,
  loadCovenantFile,
  covenantExists,
  verifyCovenantIntegrity,
  initializeCovenant
};
