/**
 * Comphyon Core Example
 * 
 * This example demonstrates the usage of the Comphyon Core system.
 */

const { createComphyonSystem } = require('../comphyon-core');

// Mock NovaVision for demonstration
const mockNovaVision = {
  render: (schema, target) => {
    console.log(`[NovaVision] Rendering schema: ${schema.id}`);
    console.log(`[NovaVision] Schema type: ${schema.type}`);
    console.log(`[NovaVision] Schema title: ${schema.title}`);
    console.log(`[NovaVision] Target: ${target}`);
    return true;
  }
};

// Create Comphyon System
const comphyonSystem = createComphyonSystem({
  enableLogging: true,
  enableMetrics: true,
  updateInterval: 5000,
  novaVision: mockNovaVision
});

// Initialize and start the system
async function runExample() {
  try {
    console.log('Initializing Comphyon System...');
    await comphyonSystem.initialize();
    console.log('Comphyon System initialized successfully');
    
    console.log('\nStarting Comphyon System...');
    await comphyonSystem.start();
    console.log('Comphyon System started successfully');
    
    // Process data in different domains
    console.log('\nProcessing data in Cyber domain...');
    const cyberResult = comphyonSystem.processData('cyber', 'policy_entropy', 0.7, {
      source: 'example',
      description: 'High policy entropy'
    });
    console.log('Cyber data processed:', cyberResult);
    
    console.log('\nProcessing data in Financial domain...');
    const financialResult = comphyonSystem.processData('financial', 'transaction_entropy', 0.8, {
      source: 'example',
      description: 'High transaction entropy'
    });
    console.log('Financial data processed:', financialResult);
    
    console.log('\nProcessing data in Biological domain...');
    const biologicalResult = comphyonSystem.processData('biological', 'inflammation_level', 0.6, {
      source: 'example',
      description: 'Moderate inflammation level'
    });
    console.log('Biological data processed:', biologicalResult);
    
    // Process events in different domains
    console.log('\nProcessing event in Cyber domain...');
    const cyberEventResult = comphyonSystem.processEvent('cyber', 'policy_violation', {
      policyId: 'policy-123',
      severity: 'high',
      description: 'Critical policy violation detected'
    }, {
      source: 'example'
    });
    console.log('Cyber event processed:', cyberEventResult);
    
    // Render dashboards
    console.log('\nRendering Universal Entropy Dashboard...');
    comphyonSystem.renderDashboard('universal-entropy', 'universal-entropy-container');
    
    console.log('\nRendering Cross-Domain Risk Dashboard...');
    comphyonSystem.renderDashboard('cross-domain-risk', 'cross-domain-risk-container');
    
    // Calculate Comphyon value
    console.log('\nCalculating Comphyon value...');
    const comphyonValue = comphyonSystem.calculateComphyon();
    console.log(`Comphyon value: ${comphyonValue.toFixed(4)} Cph`);
    
    // Get system state
    console.log('\nGetting system state...');
    const systemState = comphyonSystem.getSystemState();
    console.log('System state:', JSON.stringify(systemState, null, 2));
    
    // Get metrics
    console.log('\nGetting metrics...');
    const metrics = comphyonSystem.getMetrics();
    console.log('System metrics:');
    console.log(`- Processing Time: ${metrics.processingTimeMs.toFixed(2)} ms`);
    console.log(`- Updates Processed: ${metrics.updatesProcessed}`);
    console.log(`- System Uptime: ${metrics.systemUptime} ms`);
    
    // Simulate real-time updates
    console.log('\nSimulating real-time updates...');
    let updateCount = 0;
    const maxUpdates = 3;
    
    const updateInterval = setInterval(() => {
      updateCount++;
      console.log(`\nUpdate ${updateCount}:`);
      
      // Generate random entropy values
      const policyEntropy = 0.5 + Math.random() * 0.3;
      const transactionEntropy = 0.4 + Math.random() * 0.3;
      const inflammationLevel = 0.3 + Math.random() * 0.3;
      
      console.log(`- Policy Entropy: ${policyEntropy.toFixed(4)}`);
      console.log(`- Transaction Entropy: ${transactionEntropy.toFixed(4)}`);
      console.log(`- Inflammation Level: ${inflammationLevel.toFixed(4)}`);
      
      // Process data
      comphyonSystem.processData('cyber', 'policy_entropy', policyEntropy, {
        source: 'simulation'
      });
      
      comphyonSystem.processData('financial', 'transaction_entropy', transactionEntropy, {
        source: 'simulation'
      });
      
      comphyonSystem.processData('biological', 'inflammation_level', inflammationLevel, {
        source: 'simulation'
      });
      
      // Calculate Comphyon value
      const comphyonValue = comphyonSystem.calculateComphyon();
      console.log(`- Comphyon value: ${comphyonValue.toFixed(4)} Cph`);
      
      // Render dashboards
      comphyonSystem.renderDashboard('universal-entropy', 'universal-entropy-container');
      
      // Stop after max updates
      if (updateCount >= maxUpdates) {
        clearInterval(updateInterval);
        
        // Stop the system
        console.log('\nStopping Comphyon System...');
        comphyonSystem.stop();
        console.log('Comphyon System stopped successfully');
        
        // Get final metrics
        const finalMetrics = comphyonSystem.getMetrics();
        console.log('\nFinal metrics:');
        console.log(`- Processing Time: ${finalMetrics.processingTimeMs.toFixed(2)} ms`);
        console.log(`- Updates Processed: ${finalMetrics.updatesProcessed}`);
        console.log(`- System Uptime: ${finalMetrics.systemUptime} ms`);
        
        console.log('\nComphyon Core example completed successfully!');
      }
    }, 2000);
  } catch (error) {
    console.error('Error running Comphyon Core example:', error);
  }
}

// Run the example
runExample();
