"""
Simple test script for NovaFoldClient folding archetypes.
"""
import os
import sys

# Add the src directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

try:
    from ConsciousNovaFold import NovaFoldClient
    print("Successfully imported NovaFoldClient")
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Current sys.path: {sys.path}")
    raise

def main():
    print("Testing NovaFoldClient...")
    
    try:
        # Initialize the client
        client = NovaFoldClient(enable_benchmark=False)
        print("Successfully created NovaFoldClient instance")
        
        # List available archetypes
        archetypes = client.list_archetypes()
        print("\nAvailable archetypes:")
        for archetype in archetypes:
            info = client.get_archetype_info(archetype)
            print(f"- {archetype}: {info.get('description', 'No description')}")
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
