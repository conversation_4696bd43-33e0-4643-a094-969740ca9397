describe('Compliance Audit Flow', () => {
  beforeEach(() => {
    // Login as an admin user before each test
    cy.fixture('users').then((users) => {
      cy.visit('/signin');
      cy.get('input[name="email"]').type(users.adminUser.email);
      cy.get('input[name="password"]').type(users.adminUser.password);
      cy.get('form').submit();
      cy.url().should('include', '/dashboard');
    });
  });

  it('completes a full compliance audit cycle', () => {
    // Step 1: Navigate to compliance dashboard
    cy.contains('Compliance').click();
    cy.url().should('include', '/compliance');
    cy.contains('Compliance Dashboard').should('be.visible');

    // Step 2: Initiate a new audit
    cy.contains('New Audit').click();
    cy.get('[data-testid="audit-form"]').should('be.visible');
    
    // Step 3: Configure audit parameters
    cy.get('select[name="framework"]').select('PCI DSS');
    cy.get('select[name="version"]').select('4.0');
    cy.get('input[name="scope"]').type('Payment Processing Systems');
    cy.get('input[name="startDate"]').type('2023-06-01');
    cy.get('input[name="endDate"]').type('2023-06-30');
    
    // Step 4: Select controls to audit
    cy.contains('Select Controls').click();
    cy.get('[data-testid="control-selection"]').should('be.visible');
    cy.get('[data-testid="select-all-controls"]').click();
    cy.contains('Next').click();
    
    // Step 5: Select evidence sources
    cy.contains('Evidence Sources').should('be.visible');
    cy.get('[data-testid="aws-source"]').click();
    cy.get('[data-testid="azure-source"]').click();
    cy.get('[data-testid="gcp-source"]').click();
    cy.contains('Next').click();
    
    // Step 6: Review and start audit
    cy.contains('Review Audit Configuration').should('be.visible');
    cy.contains('Start Audit').click();
    
    // Step 7: Verify audit initiated
    cy.contains('Audit Initiated').should('be.visible');
    cy.contains('Collecting Evidence...').should('be.visible');
    
    // Step 8: Wait for evidence collection (mock for testing)
    cy.contains('View Progress').click();
    cy.url().should('include', '/audit/');
    
    // Step 9: Verify evidence collection progress
    cy.get('[data-testid="evidence-progress"]').should('be.visible');
    cy.get('[data-testid="progress-bar"]').should('have.attr', 'aria-valuenow').and('not.equal', '0');
    
    // Step 10: Complete evidence collection (mock for testing)
    cy.window().then((win) => {
      // Mock the completion of evidence collection
      win.mockCompleteEvidenceCollection();
    });
    
    // Step 11: Verify evidence collection complete
    cy.contains('Evidence Collection Complete', { timeout: 10000 }).should('be.visible');
    cy.contains('Generate Report').click();
    
    // Step 12: Configure report options
    cy.get('[data-testid="report-options"]').should('be.visible');
    cy.get('select[name="reportFormat"]').select('PDF');
    cy.get('input[name="includeEvidence"]').check();
    cy.get('input[name="includeFindings"]').check();
    cy.get('input[name="includeRecommendations"]').check();
    cy.contains('Generate').click();
    
    // Step 13: Verify report generation
    cy.contains('Report Generated Successfully', { timeout: 10000 }).should('be.visible');
    cy.get('[data-testid="download-report"]').should('be.visible');
    
    // Step 14: View findings
    cy.contains('View Findings').click();
    cy.url().should('include', '/findings');
    
    // Step 15: Verify findings display
    cy.get('[data-testid="findings-table"]').should('be.visible');
    cy.get('[data-testid="findings-count"]').should('not.have.text', '0');
    
    // Step 16: Test remediation workflow
    cy.contains('Remediate').first().click();
    cy.get('[data-testid="remediation-form"]').should('be.visible');
    cy.get('textarea[name="remediationPlan"]').type('Update AWS security groups to restrict access');
    cy.get('input[name="assignee"]').type('Security Team');
    cy.get('input[name="dueDate"]').type('2023-07-15');
    cy.contains('Save Plan').click();
    
    // Step 17: Verify remediation plan saved
    cy.contains('Remediation Plan Saved').should('be.visible');
    
    // Step 18: Complete the audit
    cy.contains('Back to Audit').click();
    cy.url().should('include', '/audit/');
    cy.contains('Complete Audit').click();
    cy.get('[data-testid="audit-complete-confirmation"]').should('be.visible');
    cy.contains('Yes, Complete Audit').click();
    
    // Step 19: Verify audit completion
    cy.contains('Audit Completed Successfully').should('be.visible');
    cy.url().should('include', '/compliance');
    
    // Step 20: Verify audit appears in history
    cy.contains('Audit History').click();
    cy.get('[data-testid="audit-history-table"]').should('be.visible');
    cy.contains('PCI DSS 4.0').should('be.visible');
    cy.contains('Completed').should('be.visible');
  });

  it('handles evidence collection failures gracefully', () => {
    // Navigate to compliance dashboard
    cy.contains('Compliance').click();
    cy.contains('New Audit').click();
    
    // Configure a minimal audit
    cy.get('select[name="framework"]').select('PCI DSS');
    cy.get('input[name="scope"]').type('Test Failure Handling');
    cy.get('[data-testid="select-all-controls"]').click();
    cy.contains('Next').click();
    
    // Select evidence source that will fail
    cy.get('[data-testid="failing-source"]').click();
    cy.contains('Next').click();
    cy.contains('Start Audit').click();
    
    // Verify failure handling
    cy.contains('Evidence Collection Issues Detected', { timeout: 10000 }).should('be.visible');
    cy.contains('Retry Failed Sources').should('be.visible');
    cy.contains('Continue Without Failed Evidence').should('be.visible');
    
    // Test retry functionality
    cy.contains('Retry Failed Sources').click();
    cy.contains('Retrying Evidence Collection...').should('be.visible');
    
    // Mock successful retry
    cy.window().then((win) => {
      win.mockSuccessfulRetry();
    });
    
    // Verify recovery
    cy.contains('Evidence Collection Complete', { timeout: 10000 }).should('be.visible');
    cy.contains('Generate Report').should('be.visible');
  });

  it('supports manual evidence upload', () => {
    // Navigate to compliance dashboard
    cy.contains('Compliance').click();
    cy.contains('New Audit').click();
    
    // Configure a minimal audit
    cy.get('select[name="framework"]').select('HIPAA');
    cy.get('input[name="scope"]').type('Manual Evidence Test');
    cy.get('[data-testid="select-all-controls"]').click();
    cy.contains('Next').click();
    cy.contains('Next').click();
    cy.contains('Start Audit').click();
    
    // Navigate to manual evidence upload
    cy.contains('Upload Manual Evidence').click();
    cy.get('[data-testid="manual-evidence-form"]').should('be.visible');
    
    // Select a control
    cy.get('select[name="control"]').select('HIPAA-1');
    
    // Upload evidence file
    cy.get('input[type="file"]').selectFile({
      contents: Cypress.Buffer.from('Test evidence content'),
      fileName: 'evidence.pdf',
      mimeType: 'application/pdf'
    });
    
    // Add description and submit
    cy.get('textarea[name="description"]').type('Manual evidence for testing');
    cy.contains('Upload Evidence').click();
    
    // Verify upload success
    cy.contains('Evidence Uploaded Successfully').should('be.visible');
    
    // Verify evidence appears in list
    cy.get('[data-testid="evidence-list"]').should('contain', 'evidence.pdf');
  });
});
