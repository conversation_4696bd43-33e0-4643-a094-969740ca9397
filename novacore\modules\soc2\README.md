# NovaCore SOC 2 Module

The SOC 2 Module provides comprehensive SOC 2 compliance automation as part of the NovaFuse Cyber-Safety Platform.

## Features

- **SOC 2 Control Management**: Manage SOC 2 controls and their implementation status
- **Evidence Collection**: Automatically collect evidence from various sources (AWS, GitHub, etc.)
- **Blockchain Verification**: Verify evidence integrity using blockchain technology
- **Assessment Management**: Create and manage SOC 2 readiness assessments and gap analyses
- **Compliance Reporting**: Generate SOC 2 compliance reports and dashboards

## Architecture

The SOC 2 Module is built with a modular architecture:

- **Controllers**: Handle API requests and responses
- **Services**: Implement business logic for SOC 2 compliance
- **Models**: Define data structures for SOC 2 controls, evidence, and assessments
- **Collectors**: Gather evidence from various sources (AWS, GitHub, etc.)

## API Endpoints

### SOC 2 Controls

- `GET /api/soc2/controls` - Get all SOC 2 controls
- `GET /api/soc2/controls/:id` - Get SOC 2 control by ID
- `GET /api/soc2/organizations/:organizationId/controls/:controlId` - Get SOC 2 control implementation
- `PUT /api/soc2/organizations/:organizationId/controls/:controlId` - Update SOC 2 control implementation
- `GET /api/soc2/organizations/:organizationId/summary` - Get SOC 2 implementation summary

### SOC 2 Evidence

- `POST /api/soc2/evidence` - Create SOC 2 evidence
- `GET /api/soc2/organizations/:organizationId/evidence` - Get all SOC 2 evidence
- `GET /api/soc2/evidence/:id` - Get SOC 2 evidence by ID
- `POST /api/soc2/organizations/:organizationId/controls/:controlId/collect/:source` - Collect SOC 2 evidence
- `POST /api/soc2/evidence/:id/verify` - Verify SOC 2 evidence

### SOC 2 Assessments

- `POST /api/soc2/assessments` - Create SOC 2 assessment
- `GET /api/soc2/organizations/:organizationId/assessments` - Get all SOC 2 assessments
- `GET /api/soc2/assessments/:id` - Get SOC 2 assessment by ID

## Integration with NovaCore

The SOC 2 Module integrates with the NovaCore API to provide a comprehensive SOC 2 compliance solution:

- Uses the Evidence API for evidence management
- Uses the Blockchain API for evidence verification
- Uses the Connector API for integration with external systems
- Uses the Cyber-Safety middleware for automatic evidence collection

## Usage

```javascript
// Example: Create a SOC 2 assessment
const assessment = await fetch('/api/soc2/assessments', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    name: 'SOC 2 Readiness Assessment',
    description: 'Initial readiness assessment for SOC 2 compliance',
    organizationId: 'YOUR_ORGANIZATION_ID',
    type: 'readiness',
    trustServiceCriteria: ['security', 'availability']
  })
});

// Example: Collect evidence from AWS
const evidence = await fetch(`/api/soc2/organizations/YOUR_ORGANIZATION_ID/controls/soc2-cc6-1/collect/aws`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    // Collection options
  })
});
```
