/**
 * Parameter Input Component
 * 
 * This component renders the appropriate input field for a parameter based on its type.
 */

import React from 'react';
import { 
  Box, 
  Checkbox, 
  FormControl, 
  FormControlLabel, 
  InputLabel, 
  MenuItem, 
  Select, 
  TextField, 
  Typography 
} from '@mui/material';

const ParameterInput = ({ parameter, value, onChange }) => {
  const { name, type, description, required, options } = parameter;
  
  const handleChange = (event) => {
    const newValue = type === 'boolean' 
      ? event.target.checked 
      : event.target.value;
    
    onChange(newValue);
  };
  
  const renderInput = () => {
    switch (type) {
      case 'string':
        return (
          <TextField
            fullWidth
            label={name}
            value={value || ''}
            onChange={handleChange}
            required={required}
            helperText={description}
            margin="normal"
          />
        );
        
      case 'number':
        return (
          <TextField
            fullWidth
            label={name}
            value={value || ''}
            onChange={handleChange}
            required={required}
            helperText={description}
            margin="normal"
            type="number"
          />
        );
        
      case 'boolean':
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={!!value}
                onChange={handleChange}
                name={name}
              />
            }
            label={
              <Box>
                <Typography variant="body1">{name}</Typography>
                {description && (
                  <Typography variant="caption" color="textSecondary">
                    {description}
                  </Typography>
                )}
              </Box>
            }
          />
        );
        
      case 'enum':
        return (
          <FormControl fullWidth margin="normal">
            <InputLabel id={`${name}-label`}>{name}</InputLabel>
            <Select
              labelId={`${name}-label`}
              value={value || ''}
              onChange={handleChange}
              label={name}
              required={required}
            >
              {options && options.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label || option.value}
                </MenuItem>
              ))}
            </Select>
            {description && (
              <Typography variant="caption" color="textSecondary">
                {description}
              </Typography>
            )}
          </FormControl>
        );
        
      case 'object':
      case 'array':
        return (
          <TextField
            fullWidth
            label={name}
            value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value || ''}
            onChange={(e) => {
              try {
                const parsedValue = JSON.parse(e.target.value);
                onChange(parsedValue);
              } catch (error) {
                // If it's not valid JSON, just store the string
                onChange(e.target.value);
              }
            }}
            required={required}
            helperText={description}
            margin="normal"
            multiline
            rows={4}
          />
        );
        
      default:
        return (
          <TextField
            fullWidth
            label={name}
            value={value || ''}
            onChange={handleChange}
            required={required}
            helperText={description}
            margin="normal"
          />
        );
    }
  };
  
  return (
    <Box sx={{ mb: 2 }}>
      {renderInput()}
    </Box>
  );
};

export default ParameterInput;
