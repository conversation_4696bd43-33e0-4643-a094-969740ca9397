/**
 * Data Transformation Test for NovaConnect Universal API Connector
 *
 * This test focuses on the connector's ability to transform complex data.
 */

const axios = require('axios');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test connector definition with complex transformations
const connector = {
  metadata: {
    name: 'Findings API Connector',
    version: '1.0.0',
    description: 'Connector for security findings API'
  },
  authentication: {
    type: 'API_KEY',
    fields: {
      apiKey: {
        type: 'string',
        description: 'API Key',
        required: true
      },
      headerName: {
        type: 'string',
        description: 'Header name for the API key',
        default: 'X-API-Key'
      }
    }
  },
  configuration: {
    baseUrl: 'http://localhost:3005',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 5000
  },
  endpoints: [
    {
      id: 'getFindings',
      name: 'Get Security Findings',
      path: '/api-key/findings',
      method: 'GET'
    }
  ],
  mappings: [
    {
      sourceEndpoint: 'getFindings',
      targetEntity: 'SecurityFindings',
      transformations: [
        // Extract all finding IDs into an array
        {
          source: '$.findings[*].id',
          target: 'findingIds',
          transform: 'extractArray'
        },
        // Extract all finding titles into an array
        {
          source: '$.findings[*].title',
          target: 'titles',
          transform: 'extractArray'
        },
        // Count the number of findings
        {
          source: '$.findings',
          target: 'count',
          transform: 'count'
        },
        // Map severity levels to risk ratings
        {
          source: '$.findings[*].severity',
          target: 'riskLevels',
          transform: 'mapSeverityToRisk'
        },
        // Extract all resource IDs from all findings
        {
          source: '$.findings[*].resources[*].id',
          target: 'resourceIds',
          transform: 'extractNestedArray'
        },
        // Format the creation dates
        {
          source: '$.findings[*].created_at',
          target: 'creationDates',
          transform: 'formatDates'
        },
        // Get compliance statuses
        {
          source: '$.findings[*].compliance.status',
          target: 'complianceStatuses',
          transform: 'extractArray'
        }
      ]
    }
  ]
};

// Test credentials
const credentials = {
  apiKey: 'valid-api-key',
  headerName: 'X-API-Key'
};

/**
 * Apply a transformation to data
 *
 * @param {string} transformType - The type of transformation to apply
 * @param {*} data - The data to transform
 * @param {string} sourcePath - The source path in the data
 * @returns {*} - The transformed data
 */
function applyTransformation(transformType, data, sourcePath) {
  // Extract the path parts from the source path
  const pathParts = sourcePath.replace('$.', '').split('.');

  // Extract the base data using the path
  let baseData = data;
  for (const part of pathParts) {
    if (part.endsWith('[*]')) {
      // Handle array wildcard
      const arrayName = part.replace('[*]', '');
      baseData = baseData[arrayName];
      break;
    } else if (part.includes('[*]')) {
      // Handle nested array wildcard
      const [arrayName, nestedPart] = part.split('[*].');
      baseData = baseData[arrayName];
      break;
    } else {
      baseData = baseData[part];
    }
  }

  // Apply the transformation
  switch (transformType) {
    case 'extractArray':
      // Extract values from an array of objects
      if (Array.isArray(baseData)) {
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart.endsWith('[*]')) {
          // Direct array extraction
          return baseData;
        } else {
          // Extract a property from each object in the array
          const property = lastPart;
          return baseData.map(item => item[property]);
        }
      }
      return [];

    case 'count':
      // Count the number of items in an array
      return Array.isArray(baseData) ? baseData.length : 0;

    case 'mapSeverityToRisk':
      // Map severity levels to risk ratings
      if (Array.isArray(baseData)) {
        const lastPart = pathParts[pathParts.length - 1];
        const property = lastPart;
        const severities = baseData.map(item => item[property]);

        return severities.map(severity => {
          switch (severity) {
            case 'CRITICAL':
              return 'VERY_HIGH';
            case 'HIGH':
              return 'HIGH';
            case 'MEDIUM':
              return 'MEDIUM';
            case 'LOW':
              return 'LOW';
            default:
              return 'UNKNOWN';
          }
        });
      }
      return [];

    case 'extractNestedArray':
      // Extract values from a nested array
      if (Array.isArray(baseData)) {
        const result = [];

        // Handle the specific case of findings[*].resources[*].id
        if (pathParts.join('.') === 'findings[*].resources[*].id') {
          for (const finding of baseData) {
            if (finding.resources && Array.isArray(finding.resources)) {
              for (const resource of finding.resources) {
                result.push(resource.id);
              }
            }
          }
        }

        return result;
      }
      return [];

    case 'formatDates':
      // Format dates to YYYY-MM-DD
      if (Array.isArray(baseData)) {
        const lastPart = pathParts[pathParts.length - 1];
        const property = lastPart;
        const dates = baseData.map(item => item[property]);

        return dates.map(date => {
          if (date) {
            return date.split('T')[0];
          }
          return '';
        });
      }
      return [];

    default:
      return baseData;
  }
}

/**
 * Execute a connector endpoint with data transformation
 *
 * @param {Object} connector - The connector definition
 * @param {string} endpointId - The endpoint ID to execute
 * @param {Object} credentials - The credentials to use
 * @param {Object} parameters - The parameters to pass to the endpoint
 * @returns {Promise<Object>} - The transformed response data
 */
async function executeConnectorEndpoint(connector, endpointId, credentials, parameters = {}) {
  // Find the endpoint
  const endpoint = connector.endpoints.find(e => e.id === endpointId);
  if (!endpoint) {
    throw new Error(`Endpoint ${endpointId} not found`);
  }

  // Find the mapping
  const mapping = connector.mappings.find(m => m.sourceEndpoint === endpointId);

  // Build the request URL
  const url = `${connector.configuration.baseUrl}${endpoint.path}`;

  // Build the request headers
  const headers = {
    ...connector.configuration.headers
  };

  // Add authentication headers
  if (connector.authentication.type === 'API_KEY') {
    headers[credentials.headerName] = credentials.apiKey;
  }

  // Make the request
  console.log(`${colors.yellow}Making request to ${endpoint.method} ${url}${colors.reset}`);

  try {
    let response;

    switch (endpoint.method) {
      case 'GET':
        response = await axios.get(url, { headers, params: parameters.query });
        break;
      case 'POST':
        response = await axios.post(url, parameters.body, { headers });
        break;
      case 'PUT':
        response = await axios.put(url, parameters.body, { headers });
        break;
      case 'DELETE':
        response = await axios.delete(url, { headers });
        break;
      default:
        throw new Error(`Unsupported method: ${endpoint.method}`);
    }

    console.log(`${colors.green}✓ Request successful! Status: ${response.status}${colors.reset}`);

    // Transform the response if a mapping exists
    if (mapping) {
      console.log(`${colors.yellow}Transforming response using mapping...${colors.reset}`);

      const transformedData = {};

      for (const transformation of mapping.transformations) {
        const { source, target, transform } = transformation;

        // Apply the transformation
        transformedData[target] = applyTransformation(transform, response.data, source);
      }

      console.log(`${colors.green}✓ Response transformed successfully!${colors.reset}`);
      return transformedData;
    }

    return response.data;
  } catch (error) {
    console.log(`${colors.red}✗ Request failed! Error: ${error.message}${colors.reset}`);
    if (error.response) {
      console.log(`${colors.red}Status: ${error.response.status}${colors.reset}`);
      console.log(`${colors.red}Response: ${JSON.stringify(error.response.data, null, 2)}${colors.reset}`);
    }
    throw error;
  }
}

/**
 * Run the data transformation test
 */
async function runTransformationTest() {
  console.log(`${colors.bright}${colors.magenta}=== Data Transformation Test ===${colors.reset}\n`);

  try {
    // Execute the connector endpoint
    const result = await executeConnectorEndpoint(connector, 'getFindings', credentials);

    // Display the transformed result
    console.log(`${colors.cyan}Transformed result: ${JSON.stringify(result, null, 2)}${colors.reset}`);

    // Verify the transformations
    let allTestsPassed = true;

    // Check finding IDs
    if (Array.isArray(result.findingIds) && result.findingIds.includes('finding-1') && result.findingIds.includes('finding-2')) {
      console.log(`${colors.green}✓ Finding IDs transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Finding IDs transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Check titles
    if (Array.isArray(result.titles) &&
        result.titles.includes('Security Group allows unrestricted access') &&
        result.titles.includes('S3 bucket allows public access')) {
      console.log(`${colors.green}✓ Titles transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Titles transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Check count
    if (result.count === 2) {
      console.log(`${colors.green}✓ Count transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Count transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Check risk levels
    if (Array.isArray(result.riskLevels) &&
        result.riskLevels.includes('HIGH') &&
        result.riskLevels.includes('VERY_HIGH')) {
      console.log(`${colors.green}✓ Risk levels transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Risk levels transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Check resource IDs
    if (Array.isArray(result.resourceIds) &&
        result.resourceIds.includes('sg-12345') &&
        result.resourceIds.includes('bucket-12345')) {
      console.log(`${colors.green}✓ Resource IDs transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Resource IDs transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Check creation dates
    if (Array.isArray(result.creationDates) &&
        result.creationDates.includes('2023-01-01') &&
        result.creationDates.includes('2023-01-03')) {
      console.log(`${colors.green}✓ Creation dates transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Creation dates transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Check compliance statuses
    if (Array.isArray(result.complianceStatuses)) {
      console.log(`${colors.green}✓ Compliance statuses transformation passed${colors.reset}`);
    } else {
      console.log(`${colors.red}✗ Compliance statuses transformation failed${colors.reset}`);
      allTestsPassed = false;
    }

    // Overall result
    if (allTestsPassed) {
      console.log(`\n${colors.bright}${colors.green}✓ All data transformations passed!${colors.reset}`);
      console.log(`${colors.green}The Universal API Connector successfully transformed complex data.${colors.reset}`);
    } else {
      console.log(`\n${colors.bright}${colors.red}✗ Some data transformations failed!${colors.reset}`);
      console.log(`${colors.red}The Universal API Connector did not correctly transform all data.${colors.reset}`);
    }
  } catch (error) {
    console.log(`${colors.bright}${colors.red}Test failed with error: ${error.message}${colors.reset}`);
  }
}

// Run the test
runTransformationTest();
