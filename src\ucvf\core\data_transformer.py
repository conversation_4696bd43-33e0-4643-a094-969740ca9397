"""
Data Transformer for the Universal Compliance Visualization Framework.

This module provides functionality for transforming compliance data based on
stakeholder roles and visualization templates.
"""

import logging
from typing import Dict, List, Any, Optional

from .template_manager import VisualizationTemplate

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataTransformer:
    """
    Transformer for compliance data.
    
    This class is responsible for transforming raw compliance data into a format
    suitable for visualization based on stakeholder roles and templates.
    """
    
    def __init__(self):
        """Initialize the Data Transformer."""
        logger.info("Initializing Data Transformer")
    
    def transform_data(self, 
                      data: Dict[str, Any], 
                      role_config: Dict[str, Any],
                      template: VisualizationTemplate) -> Dict[str, Any]:
        """
        Transform compliance data based on role configuration and template.
        
        Args:
            data: The raw compliance data
            role_config: The configuration for the stakeholder role
            template: The visualization template
            
        Returns:
            The transformed data suitable for visualization
        """
        logger.info(f"Transforming data for role '{role_config['name']}' and template '{template.name}'")
        
        # Apply role-based filtering
        filtered_data = self._filter_data_by_role(data, role_config)
        
        # Apply role-based aggregation
        aggregated_data = self._aggregate_data_by_role(filtered_data, role_config)
        
        # Apply template-specific transformations
        transformed_data = self._apply_template_transformations(aggregated_data, template)
        
        logger.info("Data transformation completed")
        
        return transformed_data
    
    def _filter_data_by_role(self, data: Dict[str, Any], role_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter data based on role configuration.
        
        Args:
            data: The raw compliance data
            role_config: The configuration for the stakeholder role
            
        Returns:
            The filtered data
        """
        # Get the data access level from the role configuration
        access_level = role_config.get('data_access_level', 'summary')
        
        # Filter the data based on the access level
        if access_level == 'summary':
            return self._filter_for_summary_level(data)
        elif access_level == 'detailed':
            return self._filter_for_detailed_level(data)
        else:
            # Default to summary level
            return self._filter_for_summary_level(data)
    
    def _filter_for_summary_level(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter data for summary-level access.
        
        Args:
            data: The raw compliance data
            
        Returns:
            The filtered data
        """
        filtered_data = {}
        
        # Include only high-level summary information
        if 'compliance_summary' in data:
            filtered_data['compliance_summary'] = data['compliance_summary']
        
        if 'risk_summary' in data:
            filtered_data['risk_summary'] = data['risk_summary']
        
        if 'frameworks' in data:
            # Include only framework names and overall compliance scores
            filtered_data['frameworks'] = {}
            for framework, framework_data in data['frameworks'].items():
                filtered_data['frameworks'][framework] = {
                    'name': framework_data.get('name', framework),
                    'compliance_score': framework_data.get('compliance_score', 0),
                    'risk_level': framework_data.get('risk_level', 'unknown')
                }
        
        return filtered_data
    
    def _filter_for_detailed_level(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Filter data for detailed-level access.
        
        Args:
            data: The raw compliance data
            
        Returns:
            The filtered data
        """
        # For detailed level, include most of the data
        # but still exclude sensitive information
        filtered_data = data.copy()
        
        # Remove any sensitive fields
        sensitive_fields = ['raw_evidence', 'personal_data', 'credentials']
        for field in sensitive_fields:
            if field in filtered_data:
                del filtered_data[field]
        
        return filtered_data
    
    def _aggregate_data_by_role(self, data: Dict[str, Any], role_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aggregate data based on role configuration.
        
        Args:
            data: The filtered compliance data
            role_config: The configuration for the stakeholder role
            
        Returns:
            The aggregated data
        """
        # Get the visualization preferences from the role configuration
        preferences = role_config.get('visualization_preferences', {})
        
        # Get the complexity level
        complexity = preferences.get('complexity', 'medium')
        
        # Get the focus areas
        focus_areas = preferences.get('focus_areas', [])
        
        # Aggregate the data based on complexity and focus areas
        if complexity == 'low':
            return self._aggregate_for_low_complexity(data, focus_areas)
        elif complexity == 'medium':
            return self._aggregate_for_medium_complexity(data, focus_areas)
        elif complexity == 'high':
            return self._aggregate_for_high_complexity(data, focus_areas)
        else:
            # Default to medium complexity
            return self._aggregate_for_medium_complexity(data, focus_areas)
    
    def _aggregate_for_low_complexity(self, data: Dict[str, Any], focus_areas: List[str]) -> Dict[str, Any]:
        """
        Aggregate data for low complexity visualization.
        
        Args:
            data: The filtered compliance data
            focus_areas: The areas to focus on
            
        Returns:
            The aggregated data
        """
        aggregated_data = {}
        
        # Include only the most important summary information
        if 'compliance_summary' in data:
            aggregated_data['overall_compliance'] = data['compliance_summary'].get('overall_score', 0)
        
        if 'risk_summary' in data:
            aggregated_data['overall_risk'] = data['risk_summary'].get('overall_level', 'unknown')
        
        # Include high-level information for focus areas
        for area in focus_areas:
            if area == 'risk' and 'risk_summary' in data:
                aggregated_data['risk_by_category'] = data['risk_summary'].get('by_category', {})
            
            elif area == 'trends' and 'compliance_trends' in data:
                aggregated_data['compliance_trends'] = data['compliance_trends']
            
            elif area == 'financial_impact' and 'financial_impact' in data:
                aggregated_data['financial_impact'] = data['financial_impact']
        
        return aggregated_data
    
    def _aggregate_for_medium_complexity(self, data: Dict[str, Any], focus_areas: List[str]) -> Dict[str, Any]:
        """
        Aggregate data for medium complexity visualization.
        
        Args:
            data: The filtered compliance data
            focus_areas: The areas to focus on
            
        Returns:
            The aggregated data
        """
        # Start with the low complexity aggregation
        aggregated_data = self._aggregate_for_low_complexity(data, focus_areas)
        
        # Add more detailed information
        if 'frameworks' in data:
            aggregated_data['frameworks'] = {}
            for framework, framework_data in data['frameworks'].items():
                aggregated_data['frameworks'][framework] = {
                    'name': framework_data.get('name', framework),
                    'compliance_score': framework_data.get('compliance_score', 0),
                    'risk_level': framework_data.get('risk_level', 'unknown'),
                    'control_summary': framework_data.get('control_summary', {})
                }
        
        # Add more detailed information for focus areas
        for area in focus_areas:
            if area == 'requirements' and 'requirements' in data:
                aggregated_data['requirements_summary'] = self._summarize_requirements(data['requirements'])
            
            elif area == 'evidence' and 'evidence' in data:
                aggregated_data['evidence_summary'] = self._summarize_evidence(data['evidence'])
            
            elif area == 'gaps' and 'gaps' in data:
                aggregated_data['gaps_summary'] = self._summarize_gaps(data['gaps'])
            
            elif area == 'security_controls' and 'security_controls' in data:
                aggregated_data['security_controls_summary'] = self._summarize_security_controls(data['security_controls'])
        
        return aggregated_data
    
    def _aggregate_for_high_complexity(self, data: Dict[str, Any], focus_areas: List[str]) -> Dict[str, Any]:
        """
        Aggregate data for high complexity visualization.
        
        Args:
            data: The filtered compliance data
            focus_areas: The areas to focus on
            
        Returns:
            The aggregated data
        """
        # Start with the medium complexity aggregation
        aggregated_data = self._aggregate_for_medium_complexity(data, focus_areas)
        
        # Add even more detailed information
        if 'frameworks' in data:
            for framework, framework_data in data['frameworks'].items():
                if framework in aggregated_data['frameworks']:
                    # Add detailed control information
                    if 'controls' in framework_data:
                        aggregated_data['frameworks'][framework]['controls'] = framework_data['controls']
                    
                    # Add detailed requirement information
                    if 'requirements' in framework_data:
                        aggregated_data['frameworks'][framework]['requirements'] = framework_data['requirements']
        
        # Add detailed information for focus areas
        for area in focus_areas:
            if area == 'vulnerabilities' and 'vulnerabilities' in data:
                aggregated_data['vulnerabilities'] = data['vulnerabilities']
            
            elif area == 'remediation' and 'remediation' in data:
                aggregated_data['remediation'] = data['remediation']
            
            elif area == 'testing' and 'testing' in data:
                aggregated_data['testing'] = data['testing']
        
        return aggregated_data
    
    def _summarize_requirements(self, requirements: Dict[str, Any]) -> Dict[str, Any]:
        """
        Summarize requirements data.
        
        Args:
            requirements: The requirements data
            
        Returns:
            The summarized data
        """
        summary = {
            'total_count': 0,
            'met_count': 0,
            'partially_met_count': 0,
            'not_met_count': 0,
            'by_framework': {}
        }
        
        # Count requirements by status
        for req_id, req_data in requirements.items():
            summary['total_count'] += 1
            
            status = req_data.get('status', 'unknown')
            if status == 'met':
                summary['met_count'] += 1
            elif status == 'partially_met':
                summary['partially_met_count'] += 1
            elif status == 'not_met':
                summary['not_met_count'] += 1
            
            # Group by framework
            framework = req_data.get('framework', 'unknown')
            if framework not in summary['by_framework']:
                summary['by_framework'][framework] = {
                    'total_count': 0,
                    'met_count': 0,
                    'partially_met_count': 0,
                    'not_met_count': 0
                }
            
            summary['by_framework'][framework]['total_count'] += 1
            if status == 'met':
                summary['by_framework'][framework]['met_count'] += 1
            elif status == 'partially_met':
                summary['by_framework'][framework]['partially_met_count'] += 1
            elif status == 'not_met':
                summary['by_framework'][framework]['not_met_count'] += 1
        
        return summary
    
    def _summarize_evidence(self, evidence: Dict[str, Any]) -> Dict[str, Any]:
        """
        Summarize evidence data.
        
        Args:
            evidence: The evidence data
            
        Returns:
            The summarized data
        """
        summary = {
            'total_count': 0,
            'valid_count': 0,
            'invalid_count': 0,
            'pending_count': 0,
            'by_type': {}
        }
        
        # Count evidence by status
        for evidence_id, evidence_data in evidence.items():
            summary['total_count'] += 1
            
            status = evidence_data.get('status', 'unknown')
            if status == 'valid':
                summary['valid_count'] += 1
            elif status == 'invalid':
                summary['invalid_count'] += 1
            elif status == 'pending':
                summary['pending_count'] += 1
            
            # Group by type
            evidence_type = evidence_data.get('type', 'unknown')
            if evidence_type not in summary['by_type']:
                summary['by_type'][evidence_type] = {
                    'total_count': 0,
                    'valid_count': 0,
                    'invalid_count': 0,
                    'pending_count': 0
                }
            
            summary['by_type'][evidence_type]['total_count'] += 1
            if status == 'valid':
                summary['by_type'][evidence_type]['valid_count'] += 1
            elif status == 'invalid':
                summary['by_type'][evidence_type]['invalid_count'] += 1
            elif status == 'pending':
                summary['by_type'][evidence_type]['pending_count'] += 1
        
        return summary
    
    def _summarize_gaps(self, gaps: Dict[str, Any]) -> Dict[str, Any]:
        """
        Summarize gaps data.
        
        Args:
            gaps: The gaps data
            
        Returns:
            The summarized data
        """
        summary = {
            'total_count': 0,
            'high_risk_count': 0,
            'medium_risk_count': 0,
            'low_risk_count': 0,
            'by_framework': {}
        }
        
        # Count gaps by risk level
        for gap_id, gap_data in gaps.items():
            summary['total_count'] += 1
            
            risk_level = gap_data.get('risk_level', 'unknown')
            if risk_level == 'high':
                summary['high_risk_count'] += 1
            elif risk_level == 'medium':
                summary['medium_risk_count'] += 1
            elif risk_level == 'low':
                summary['low_risk_count'] += 1
            
            # Group by framework
            framework = gap_data.get('framework', 'unknown')
            if framework not in summary['by_framework']:
                summary['by_framework'][framework] = {
                    'total_count': 0,
                    'high_risk_count': 0,
                    'medium_risk_count': 0,
                    'low_risk_count': 0
                }
            
            summary['by_framework'][framework]['total_count'] += 1
            if risk_level == 'high':
                summary['by_framework'][framework]['high_risk_count'] += 1
            elif risk_level == 'medium':
                summary['by_framework'][framework]['medium_risk_count'] += 1
            elif risk_level == 'low':
                summary['by_framework'][framework]['low_risk_count'] += 1
        
        return summary
    
    def _summarize_security_controls(self, controls: Dict[str, Any]) -> Dict[str, Any]:
        """
        Summarize security controls data.
        
        Args:
            controls: The security controls data
            
        Returns:
            The summarized data
        """
        summary = {
            'total_count': 0,
            'implemented_count': 0,
            'partially_implemented_count': 0,
            'not_implemented_count': 0,
            'by_category': {}
        }
        
        # Count controls by status
        for control_id, control_data in controls.items():
            summary['total_count'] += 1
            
            status = control_data.get('status', 'unknown')
            if status == 'implemented':
                summary['implemented_count'] += 1
            elif status == 'partially_implemented':
                summary['partially_implemented_count'] += 1
            elif status == 'not_implemented':
                summary['not_implemented_count'] += 1
            
            # Group by category
            category = control_data.get('category', 'unknown')
            if category not in summary['by_category']:
                summary['by_category'][category] = {
                    'total_count': 0,
                    'implemented_count': 0,
                    'partially_implemented_count': 0,
                    'not_implemented_count': 0
                }
            
            summary['by_category'][category]['total_count'] += 1
            if status == 'implemented':
                summary['by_category'][category]['implemented_count'] += 1
            elif status == 'partially_implemented':
                summary['by_category'][category]['partially_implemented_count'] += 1
            elif status == 'not_implemented':
                summary['by_category'][category]['not_implemented_count'] += 1
        
        return summary
    
    def _apply_template_transformations(self, 
                                       data: Dict[str, Any],
                                       template: VisualizationTemplate) -> Dict[str, Any]:
        """
        Apply template-specific transformations to the data.
        
        Args:
            data: The aggregated compliance data
            template: The visualization template
            
        Returns:
            The transformed data
        """
        # For now, just return the aggregated data
        # In a real implementation, this would apply template-specific transformations
        return data
