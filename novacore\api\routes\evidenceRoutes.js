/**
 * NovaCore Evidence Routes
 * 
 * This file defines the routes for evidence management.
 */

const express = require('express');
const router = express.Router();
const { EvidenceController } = require('../controllers');
const { authenticate, authorize } = require('../middleware/authMiddleware');

// Create a new evidence record
router.post(
  '/',
  authenticate,
  authorize('create:evidence'),
  EvidenceController.createEvidence
);

// Get all evidence records
router.get(
  '/',
  authenticate,
  authorize('read:evidence'),
  EvidenceController.getAllEvidence
);

// Get evidence by ID
router.get(
  '/:id',
  authenticate,
  authorize('read:evidence'),
  EvidenceController.getEvidenceById
);

// Update evidence by ID
router.put(
  '/:id',
  authenticate,
  authorize('update:evidence'),
  EvidenceController.updateEvidence
);

// Delete evidence by ID
router.delete(
  '/:id',
  authenticate,
  authorize('delete:evidence'),
  EvidenceController.deleteEvidence
);

// Update evidence verification status
router.patch(
  '/:id/verification',
  authenticate,
  authorize('verify:evidence'),
  EvidenceController.updateVerificationStatus
);

// Find evidence by framework and control
router.get(
  '/framework/:framework/control/:control',
  authenticate,
  authorize('read:evidence'),
  EvidenceController.findByFrameworkAndControl
);

// Find evidence by tags
router.get(
  '/tags',
  authenticate,
  authorize('read:evidence'),
  EvidenceController.findByTags
);

module.exports = router;
