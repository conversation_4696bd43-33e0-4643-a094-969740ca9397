const request = require('supertest');
const app = require('./mockApp');

describe('Privacy Management API - Consent Management', () => {
  describe('GET /privacy/management/consent/forms', () => {
    it('should generate a consent form', async () => {
      const response = await request(app)
        .get('/privacy/management/consent/forms')
        .query({ consentType: 'marketing', language: 'en' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.consentType).toBe('marketing');
      expect(response.body.data.consentTitle).toBeDefined();
      expect(response.body.data.consentDescription).toBeDefined();
      expect(response.body.data.consentOptions).toBeDefined();
    });

    it('should return 400 if consent type is missing', async () => {
      const response = await request(app)
        .get('/privacy/management/consent/forms');

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('GET /privacy/management/consent/:id/validity', () => {
    it('should verify consent validity', async () => {
      // First create a consent record
      const newConsent = {
        dataSubjectId: 'ds-0001',
        dataSubjectName: 'John Doe',
        dataSubjectEmail: '<EMAIL>',
        consentType: 'marketing',
        consentDescription: 'Marketing communications consent',
        consentGiven: true,
        consentVersion: '1.0',
        consentMethod: 'online-form',
        privacyNoticeVersion: '1.0'
      };

      const createResponse = await request(app)
        .post('/privacy/management/consent-records')
        .send(newConsent);

      expect(createResponse.status).toBe(201);

      const consentId = createResponse.body.data.id;

      const response = await request(app)
        .get(`/privacy/management/consent/${consentId}/validity`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.valid).toBeDefined();
      expect(response.body.data.record).toBeDefined();
    });

    it('should return invalid for non-existent consent', async () => {
      const response = await request(app)
        .get('/privacy/management/consent/non-existent/validity');

      expect(response.status).toBe(200);
      expect(response.body.data.valid).toBe(false);
      expect(response.body.data.reason).toBe('Consent record not found');
    });
  });

  describe('POST /privacy/management/consent/:id/withdraw', () => {
    it('should withdraw consent', async () => {
      // First create a consent record
      const newConsent = {
        dataSubjectId: 'ds-0002',
        dataSubjectName: 'Jane Smith',
        dataSubjectEmail: '<EMAIL>',
        consentType: 'marketing',
        consentDescription: 'Marketing communications consent',
        consentGiven: true,
        consentVersion: '1.0',
        consentMethod: 'online-form',
        privacyNoticeVersion: '1.0'
      };

      const createResponse = await request(app)
        .post('/privacy/management/consent-records')
        .send(newConsent);

      expect(createResponse.status).toBe(201);

      const consentId = createResponse.body.data.id;

      const response = await request(app)
        .post(`/privacy/management/consent/${consentId}/withdraw`)
        .send({ withdrawalMethod: 'online-form' });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.status).toBe('withdrawn');
      expect(response.body.data.withdrawalDate).toBeDefined();
      expect(response.body.data.withdrawalMethod).toBe('online-form');
      expect(response.body.message).toBe('Consent withdrawn successfully');
    });

    it('should return 400 if withdrawal method is missing', async () => {
      const response = await request(app)
        .post('/privacy/management/consent/con-0001/withdraw')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });

    it('should return 404 for non-existent consent', async () => {
      const response = await request(app)
        .post('/privacy/management/consent/non-existent/withdraw')
        .send({ withdrawalMethod: 'online-form' });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Not Found');
    });
  });

  describe('GET /privacy/management/consent/data-subjects/:dataSubjectId', () => {
    it('should get consent records by data subject', async () => {
      const response = await request(app)
        .get('/privacy/management/consent/data-subjects/ds-0001');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('GET /privacy/management/consent/emails/:email', () => {
    it('should get consent records by email', async () => {
      const response = await request(app)
        .get('/privacy/management/consent/emails/<EMAIL>');

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('POST /privacy/management/consent/proof/verify', () => {
    it('should verify consent proof', async () => {
      const consentProof = 'IP: ***********, User-Agent: Mozilla/5.0, Timestamp: 2023-07-15T10:30:00Z';

      const response = await request(app)
        .post('/privacy/management/consent/proof/verify')
        .send({ consentProof });

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.verified).toBeDefined();
    });

    it('should return 400 if consent proof is missing', async () => {
      const response = await request(app)
        .post('/privacy/management/consent/proof/verify')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });

  describe('POST /privacy/management/consent/proof/generate', () => {
    it('should generate consent proof', async () => {
      const proofData = {
        ip: '***********',
        userAgent: 'Mozilla/5.0',
        timestamp: '2023-07-15T10:30:00Z'
      };

      const response = await request(app)
        .post('/privacy/management/consent/proof/generate')
        .send(proofData);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.consentProof).toBeDefined();
      expect(response.body.data.consentProof).toContain(proofData.ip);
      expect(response.body.data.consentProof).toContain(proofData.userAgent);
    });

    it('should return 400 if required data is missing', async () => {
      const response = await request(app)
        .post('/privacy/management/consent/proof/generate')
        .send({ timestamp: '2023-07-15T10:30:00Z' });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Bad Request');
    });
  });
});
