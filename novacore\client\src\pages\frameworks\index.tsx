/**
 * Frameworks Page
 *
 * This page displays a list of compliance frameworks and allows users to manage them.
 * It leverages the existing NovaPrime components and connects them to the NovaPulse API.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { NovaPulseAPI, Framework } from '@/api/novaPulseApi';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import { ComplianceNavigation } from '@/components/compliance/ComplianceNavigation';
import { FrameworkExplorer } from '@/components/FrameworkExplorer';
import { UnifiedFrameworkVisualizer } from '@/components/UnifiedFrameworkVisualizer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, Download, LogOut } from 'lucide-react';

export default function FrameworksPage() {
  return (
    <ProtectedRoute>
      <FrameworksContent />
    </ProtectedRoute>
  );
}

function FrameworksContent() {
  const router = useRouter();
  const { logout, user } = useAuth();
  const [frameworks, setFrameworks] = useState<Framework[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState('list');
  const [organizationId, setOrganizationId] = useState('');

  const api = new NovaPulseAPI();

  useEffect(() => {
    // Get organization ID from auth service
    try {
      const orgId = api.getOrganizationId();
      setOrganizationId(orgId);
    } catch (error) {
      console.error('Error getting organization ID:', error);
    }

    loadFrameworks();
  }, []);

  const handleLogout = () => {
    logout();
    router.push('/login');
  };

  const loadFrameworks = async () => {
    setLoading(true);
    try {
      const response = await api.getFrameworks();
      setFrameworks(response.data);

      // Select the first framework by default if available
      if (response.data.length > 0 && selectedFrameworks.length === 0) {
        setSelectedFrameworks([response.data[0]._id]);
      }
    } catch (error) {
      console.error('Error loading frameworks:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleFrameworkSelect = (id: string) => {
    if (selectedFrameworks.includes(id)) {
      setSelectedFrameworks(selectedFrameworks.filter(fId => fId !== id));
    } else {
      setSelectedFrameworks([...selectedFrameworks, id]);
    }
  };

  const handleCreateFramework = () => {
    router.push('/frameworks/create');
  };

  const filteredFrameworks = frameworks.filter(framework =>
    framework.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    framework.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Compliance Frameworks</h1>
        <Button variant="outline" size="sm" onClick={handleLogout} className="flex items-center">
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </Button>
      </div>

      <ComplianceNavigation />

      <div className="mb-6 flex flex-col md:flex-row justify-between gap-4">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
          <input
            type="text"
            placeholder="Search frameworks..."
            className="pl-9 pr-4 py-2 border rounded-md w-full md:w-64"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>

        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm" onClick={handleCreateFramework}>
            <Plus className="h-4 w-4 mr-2" />
            New Framework
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="list">List View</TabsTrigger>
          <TabsTrigger value="explorer">Framework Explorer</TabsTrigger>
          <TabsTrigger value="visualizer">Visualizer</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-4">
          {loading ? (
            <div className="text-center py-8">Loading frameworks...</div>
          ) : filteredFrameworks.length === 0 ? (
            <div className="text-center py-8">
              No frameworks found. {searchQuery && 'Try a different search term.'}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredFrameworks.map(framework => (
                <Card key={framework._id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{framework.name}</CardTitle>
                      <div className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                        {framework.type}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">{framework.shortName}</div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">{framework.description}</p>
                    <div className="flex flex-wrap gap-1 mb-3">
                      {framework.industry && (
                        <span className="text-xs px-2 py-1 rounded-full border">
                          {framework.industry}
                        </span>
                      )}
                      <span className="text-xs px-2 py-1 rounded-full border">
                        {framework.jurisdiction.country || 'Global'}
                      </span>
                      <span className="text-xs px-2 py-1 rounded-full border">
                        {framework.controls.length} Controls
                      </span>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <div>Version: {framework.currentVersion}</div>
                      <div>{framework.isCustom ? 'Custom' : 'Standard'}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="explorer">
          <FrameworkExplorer organizationId={organizationId} />
        </TabsContent>

        <TabsContent value="visualizer">
          <Card>
            <CardHeader>
              <CardTitle>Framework Relationships</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <div className="text-sm font-medium mb-2">Select Frameworks to Visualize:</div>
                <div className="flex flex-wrap gap-2">
                  {frameworks.map(framework => (
                    <Button
                      key={framework._id}
                      variant={selectedFrameworks.includes(framework._id) ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleFrameworkSelect(framework._id)}
                    >
                      {framework.shortName || framework.name}
                    </Button>
                  ))}
                </div>
              </div>

              {selectedFrameworks.length > 0 ? (
                <UnifiedFrameworkVisualizer
                  frameworkIds={selectedFrameworks}
                  showControls={true}
                  showMappings={true}
                  height="600px"
                />
              ) : (
                <div className="text-center py-8">
                  Please select at least one framework to visualize.
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
