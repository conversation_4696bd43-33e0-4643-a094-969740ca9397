/**
 * Comphyological Analytics Dashboard Styles
 */

/* Base styles */
:root {
    --primary-color: #3f51b5;
    --primary-dark: #303f9f;
    --primary-light: #c5cae9;
    --accent-color: #ff4081;
    --text-color: #212121;
    --text-secondary: #757575;
    --divider-color: #bdbdbd;
    --background-color: #f5f5f5;
    --card-color: #ffffff;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    
    --csde-color: #0088ff;
    --csfe-color: #22cc44;
    --csme-color: #8844ff;
    --fused-color: #ff7700;
    
    --shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    
    --border-radius: 4px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* Dashboard layout */
.dashboard {
    max-width: 1600px;
    margin: 0 auto;
    padding: var(--spacing-md);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
}

.dashboard-header h1 {
    font-size: 24px;
    font-weight: 500;
}

.dashboard-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.control-button {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-dark);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.control-button:hover {
    background-color: var(--accent-color);
}

.control-select {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-dark);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
}

@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

.dashboard-section {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.dashboard-section h2 {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--divider-color);
    font-size: 18px;
    font-weight: 500;
    color: var(--primary-color);
}

/* Performance Metrics Section */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.metric-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
}

.metric-card h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    font-family: monospace;
}

.metric-chart-container {
    flex-grow: 1;
    min-height: 150px;
    position: relative;
}

/* Pattern Recognition Section */
.patterns-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.pattern-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: var(--spacing-md);
}

.pattern-card h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
}

.pattern-strength {
    margin-bottom: var(--spacing-md);
}

.strength-bar {
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 4px;
    margin-bottom: var(--spacing-xs);
}

.strength-value {
    font-size: 14px;
    font-weight: 500;
    text-align: right;
}

.pattern-details {
    font-size: 14px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
}

.detail-label {
    color: var(--text-secondary);
}

.detail-value {
    font-weight: 500;
}

/* Predictive Analytics Section */
.predictions-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.prediction-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: var(--spacing-md);
}

.prediction-card h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
}

.prediction-chart-container {
    height: 200px;
    position: relative;
}

.prediction-value {
    padding: var(--spacing-md);
}

.probability-meter {
    height: 20px;
    background-color: var(--divider-color);
    border-radius: 10px;
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.probability-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 10px;
}

.probability-value {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    text-align: center;
    line-height: 20px;
    color: white;
    font-weight: 500;
    font-size: 14px;
}

.prediction-detail {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
    font-size: 14px;
}

.optimization-suggestions {
    font-size: 14px;
}

.suggestion-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
    align-items: center;
}

.suggestion-label {
    flex: 2;
    color: var(--text-secondary);
}

.suggestion-value {
    flex: 1;
    font-weight: 500;
    text-align: right;
}

.suggestion-adjustment {
    flex: 1;
    text-align: right;
    font-weight: 500;
}

.suggestion-adjustment.positive {
    color: var(--success-color);
}

.suggestion-adjustment.negative {
    color: var(--danger-color);
}

.estimated-improvement {
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--divider-color);
    display: flex;
    justify-content: space-between;
    font-weight: 500;
}

.improvement-value {
    color: var(--success-color);
}

/* System Health Section */
.health-metrics-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.health-metric {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: var(--spacing-md);
    text-align: center;
}

.health-metric h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
}

.health-gauge {
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
}

.gauge {
    width: 100%;
    height: auto;
}

.gauge-background {
    fill: none;
    stroke: var(--divider-color);
    stroke-width: 10;
}

.gauge-value {
    fill: none;
    stroke: var(--primary-color);
    stroke-width: 10;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
}

.gauge-label {
    font-size: 14px;
    font-weight: 700;
    fill: var(--text-color);
}

/* Optimization Tools Section */
.tools-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.tool-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-1);
    padding: var(--spacing-md);
}

.tool-card h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
}

.tool-controls {
    margin-bottom: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.tool-button {
    padding: var(--spacing-sm) var(--spacing-md);
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.tool-button:hover {
    background-color: var(--primary-dark);
}

.tool-button:disabled {
    background-color: var(--divider-color);
    cursor: not-allowed;
}

.tool-options {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 14px;
}

.tool-options label {
    flex: 1;
    color: var(--text-secondary);
}

.tool-options input[type="range"] {
    flex: 2;
}

.tool-options span {
    flex: 0.5;
    text-align: right;
    font-weight: 500;
}

.tool-result-container {
    height: 200px;
    position: relative;
}

.tuning-results {
    margin-top: var(--spacing-md);
}

.tuning-progress {
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    height: 8px;
    background-color: var(--divider-color);
    border-radius: 4px;
    margin-bottom: var(--spacing-xs);
}

.progress-fill {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-label {
    font-size: 14px;
    text-align: right;
}

.tuning-improvement {
    margin-bottom: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .metrics-grid,
    .patterns-container,
    .predictions-container,
    .health-metrics-container,
    .tools-container {
        grid-template-columns: 1fr;
    }
    
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dashboard-controls {
        margin-top: var(--spacing-md);
        flex-wrap: wrap;
    }
}
