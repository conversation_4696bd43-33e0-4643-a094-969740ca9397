"""
Authentication module for the UCECS API.

This module provides authentication and authorization functionality for the UCECS API.
"""

import os
import datetime
import logging
from typing import Dict, Any, Optional, List, Tuple

from flask_jwt_extended import J<PERSON>TManager, create_access_token, create_refresh_token, get_jwt_identity

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Default users for development (in production, this would be stored in a database)
DEFAULT_USERS = {
    "admin": {
        "password": "admin123",
        "roles": ["admin"],
        "permissions": ["read", "write", "delete", "validate", "store", "retrieve", "report", "search"]
    },
    "user": {
        "password": "user123",
        "roles": ["user"],
        "permissions": ["read", "search"]
    },
    "auditor": {
        "password": "auditor123",
        "roles": ["auditor"],
        "permissions": ["read", "search", "report"]
    },
    "collector": {
        "password": "collector123",
        "roles": ["collector"],
        "permissions": ["read", "write", "validate", "store"]
    }
}

# Role-based permissions
ROLE_PERMISSIONS = {
    "admin": ["read", "write", "delete", "validate", "store", "retrieve", "report", "search"],
    "user": ["read", "search"],
    "auditor": ["read", "search", "report"],
    "collector": ["read", "write", "validate", "store"]
}

# JWT configuration
JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "super-secret-key-change-in-production")
JWT_ACCESS_TOKEN_EXPIRES = datetime.timedelta(hours=1)
JWT_REFRESH_TOKEN_EXPIRES = datetime.timedelta(days=30)

# Initialize JWT manager
jwt = JWTManager()

def configure_jwt(app):
    """
    Configure JWT for the Flask application.
    
    Args:
        app: The Flask application
    """
    app.config["JWT_SECRET_KEY"] = JWT_SECRET_KEY
    app.config["JWT_ACCESS_TOKEN_EXPIRES"] = JWT_ACCESS_TOKEN_EXPIRES
    app.config["JWT_REFRESH_TOKEN_EXPIRES"] = JWT_REFRESH_TOKEN_EXPIRES
    
    jwt.init_app(app)
    
    # Add claims to the JWT
    @jwt.additional_claims_loader
    def add_claims_to_access_token(identity):
        user = get_user(identity)
        if user:
            return {
                "roles": user.get("roles", []),
                "permissions": user.get("permissions", [])
            }
        return {}

def authenticate_user(username: str, password: str) -> Optional[Dict[str, Any]]:
    """
    Authenticate a user.
    
    Args:
        username: The username
        password: The password
        
    Returns:
        The user if authentication is successful, None otherwise
    """
    user = get_user(username)
    
    if not user:
        logger.warning(f"User not found: {username}")
        return None
    
    if user.get("password") != password:
        logger.warning(f"Invalid password for user: {username}")
        return None
    
    logger.info(f"User authenticated: {username}")
    
    return user

def get_user(username: str) -> Optional[Dict[str, Any]]:
    """
    Get a user by username.
    
    Args:
        username: The username
        
    Returns:
        The user if found, None otherwise
    """
    return DEFAULT_USERS.get(username)

def create_tokens(identity: str) -> Tuple[str, str]:
    """
    Create access and refresh tokens for a user.
    
    Args:
        identity: The user identity
        
    Returns:
        A tuple containing the access token and refresh token
    """
    access_token = create_access_token(identity=identity)
    refresh_token = create_refresh_token(identity=identity)
    
    logger.info(f"Tokens created for user: {identity}")
    
    return access_token, refresh_token

def get_current_user_identity() -> str:
    """
    Get the identity of the current user.
    
    Returns:
        The user identity
    """
    return get_jwt_identity()

def has_permission(required_permission: str) -> bool:
    """
    Check if the current user has a specific permission.
    
    Args:
        required_permission: The required permission
        
    Returns:
        True if the user has the permission, False otherwise
    """
    identity = get_current_user_identity()
    user = get_user(identity)
    
    if not user:
        return False
    
    permissions = user.get("permissions", [])
    
    return required_permission in permissions

def has_role(required_role: str) -> bool:
    """
    Check if the current user has a specific role.
    
    Args:
        required_role: The required role
        
    Returns:
        True if the user has the role, False otherwise
    """
    identity = get_current_user_identity()
    user = get_user(identity)
    
    if not user:
        return False
    
    roles = user.get("roles", [])
    
    return required_role in roles
