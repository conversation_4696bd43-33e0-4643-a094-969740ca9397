/**
 * Metrics Module
 * 
 * This module provides a unified interface for creating and managing metrics.
 * It supports multiple backends (Prometheus, CloudWatch, custom) through a common API.
 */

const logger = require('../config/logger');
const { getConfig } = require('../config');

// Get metrics configuration
const config = getConfig().monitoring.metrics;

// Available metric types
const METRIC_TYPES = {
  COUNTER: 'counter',
  GAUGE: 'gauge',
  HISTOGRAM: 'histogram',
  SUMMARY: 'summary'
};

// Metric backends
let prometheusClient;
let cloudWatchClient;
let customMetricsStore = {};

// Initialize metrics backends
const initializeBackends = () => {
  // Initialize Prometheus if enabled
  if (config.prometheus.enabled) {
    try {
      const prometheus = require('prom-client');
      prometheusClient = {
        registry: new prometheus.Registry(),
        Counter: prometheus.Counter,
        Gauge: prometheus.Gauge,
        Histogram: prometheus.Histogram,
        Summary: prometheus.Summary
      };
      
      // Add default metrics
      if (config.prometheus.defaultMetrics) {
        prometheus.collectDefaultMetrics({ register: prometheusClient.registry });
      }
      
      logger.info('Prometheus metrics initialized');
    } catch (error) {
      logger.error('Failed to initialize Prometheus metrics:', error);
    }
  }
  
  // Initialize CloudWatch if enabled
  if (config.cloudWatch.enabled) {
    try {
      const AWS = require('aws-sdk');
      cloudWatchClient = new AWS.CloudWatch({
        region: config.cloudWatch.region || 'us-east-1',
        apiVersion: '2010-08-01'
      });
      
      logger.info('CloudWatch metrics initialized');
    } catch (error) {
      logger.error('Failed to initialize CloudWatch metrics:', error);
    }
  }
};

// Initialize backends
initializeBackends();

/**
 * Create a new metric
 * @param {string} name - Metric name
 * @param {string} type - Metric type (counter, gauge, histogram, summary)
 * @param {string} description - Metric description
 * @param {Object} options - Additional options for the metric
 * @returns {Object} - Metric object
 */
const createMetric = (name, type, description, options = {}) => {
  // Validate metric type
  if (!Object.values(METRIC_TYPES).includes(type)) {
    throw new Error(`Invalid metric type: ${type}`);
  }
  
  // Create metric object
  const metric = {
    name,
    type,
    description,
    options,
    backends: {}
  };
  
  // Create Prometheus metric if enabled
  if (prometheusClient) {
    try {
      switch (type) {
        case METRIC_TYPES.COUNTER:
          metric.backends.prometheus = new prometheusClient.Counter({
            name,
            help: description,
            labelNames: options.labels || [],
            registers: [prometheusClient.registry]
          });
          break;
        case METRIC_TYPES.GAUGE:
          metric.backends.prometheus = new prometheusClient.Gauge({
            name,
            help: description,
            labelNames: options.labels || [],
            registers: [prometheusClient.registry]
          });
          break;
        case METRIC_TYPES.HISTOGRAM:
          metric.backends.prometheus = new prometheusClient.Histogram({
            name,
            help: description,
            labelNames: options.labels || [],
            buckets: options.buckets || [0.1, 0.5, 1, 2, 5, 10, 20, 50, 100, 500, 1000],
            registers: [prometheusClient.registry]
          });
          break;
        case METRIC_TYPES.SUMMARY:
          metric.backends.prometheus = new prometheusClient.Summary({
            name,
            help: description,
            labelNames: options.labels || [],
            percentiles: options.percentiles || [0.01, 0.05, 0.5, 0.9, 0.95, 0.99],
            registers: [prometheusClient.registry]
          });
          break;
      }
    } catch (error) {
      logger.error(`Failed to create Prometheus metric ${name}:`, error);
    }
  }
  
  // Create custom metric store
  customMetricsStore[name] = {
    type,
    description,
    values: {},
    lastUpdated: Date.now()
  };
  
  // Add methods based on metric type
  switch (type) {
    case METRIC_TYPES.COUNTER:
      metric.inc = (labels = {}, value = 1) => {
        try {
          // Update Prometheus
          if (metric.backends.prometheus) {
            metric.backends.prometheus.inc(labels, value);
          }
          
          // Update CloudWatch
          if (cloudWatchClient) {
            sendToCloudWatch(name, value, 'Sum', labels);
          }
          
          // Update custom store
          const key = JSON.stringify(labels);
          if (!customMetricsStore[name].values[key]) {
            customMetricsStore[name].values[key] = 0;
          }
          customMetricsStore[name].values[key] += value;
          customMetricsStore[name].lastUpdated = Date.now();
        } catch (error) {
          logger.error(`Failed to increment metric ${name}:`, error);
        }
      };
      break;
    
    case METRIC_TYPES.GAUGE:
      metric.set = (labels = {}, value) => {
        try {
          // Update Prometheus
          if (metric.backends.prometheus) {
            metric.backends.prometheus.set(labels, value);
          }
          
          // Update CloudWatch
          if (cloudWatchClient) {
            sendToCloudWatch(name, value, 'Average', labels);
          }
          
          // Update custom store
          const key = JSON.stringify(labels);
          customMetricsStore[name].values[key] = value;
          customMetricsStore[name].lastUpdated = Date.now();
        } catch (error) {
          logger.error(`Failed to set metric ${name}:`, error);
        }
      };
      
      metric.inc = (labels = {}, value = 1) => {
        try {
          // Update Prometheus
          if (metric.backends.prometheus) {
            metric.backends.prometheus.inc(labels, value);
          }
          
          // Update CloudWatch
          if (cloudWatchClient) {
            sendToCloudWatch(name, value, 'Sum', labels);
          }
          
          // Update custom store
          const key = JSON.stringify(labels);
          if (!customMetricsStore[name].values[key]) {
            customMetricsStore[name].values[key] = 0;
          }
          customMetricsStore[name].values[key] += value;
          customMetricsStore[name].lastUpdated = Date.now();
        } catch (error) {
          logger.error(`Failed to increment gauge ${name}:`, error);
        }
      };
      
      metric.dec = (labels = {}, value = 1) => {
        try {
          // Update Prometheus
          if (metric.backends.prometheus) {
            metric.backends.prometheus.dec(labels, value);
          }
          
          // Update CloudWatch
          if (cloudWatchClient) {
            sendToCloudWatch(name, -value, 'Sum', labels);
          }
          
          // Update custom store
          const key = JSON.stringify(labels);
          if (!customMetricsStore[name].values[key]) {
            customMetricsStore[name].values[key] = 0;
          }
          customMetricsStore[name].values[key] -= value;
          customMetricsStore[name].lastUpdated = Date.now();
        } catch (error) {
          logger.error(`Failed to decrement gauge ${name}:`, error);
        }
      };
      break;
    
    case METRIC_TYPES.HISTOGRAM:
    case METRIC_TYPES.SUMMARY:
      metric.observe = (labels = {}, value) => {
        try {
          // Update Prometheus
          if (metric.backends.prometheus) {
            metric.backends.prometheus.observe(labels, value);
          }
          
          // Update CloudWatch
          if (cloudWatchClient) {
            sendToCloudWatch(name, value, 'Average', labels);
          }
          
          // Update custom store
          const key = JSON.stringify(labels);
          if (!customMetricsStore[name].values[key]) {
            customMetricsStore[name].values[key] = [];
          }
          customMetricsStore[name].values[key].push(value);
          
          // Limit array size to prevent memory issues
          if (customMetricsStore[name].values[key].length > 1000) {
            customMetricsStore[name].values[key] = customMetricsStore[name].values[key].slice(-1000);
          }
          
          customMetricsStore[name].lastUpdated = Date.now();
        } catch (error) {
          logger.error(`Failed to observe metric ${name}:`, error);
        }
      };
      break;
  }
  
  return metric;
};

/**
 * Send metric to CloudWatch
 * @param {string} name - Metric name
 * @param {number} value - Metric value
 * @param {string} unit - Metric unit
 * @param {Object} labels - Metric labels
 */
const sendToCloudWatch = (name, value, unit, labels = {}) => {
  if (!cloudWatchClient) return;
  
  try {
    // Convert labels to dimensions
    const dimensions = Object.entries(labels).map(([key, value]) => ({
      Name: key,
      Value: String(value)
    }));
    
    // Add namespace dimension if configured
    if (config.cloudWatch.namespace) {
      dimensions.push({
        Name: 'Namespace',
        Value: config.cloudWatch.namespace
      });
    }
    
    // Send metric to CloudWatch
    cloudWatchClient.putMetricData({
      Namespace: config.cloudWatch.namespace || 'NovaFuse',
      MetricData: [
        {
          MetricName: name,
          Dimensions: dimensions,
          Value: value,
          Unit: unit,
          Timestamp: new Date()
        }
      ]
    }).send();
  } catch (error) {
    logger.error(`Failed to send metric ${name} to CloudWatch:`, error);
  }
};

/**
 * Get all metrics
 * @returns {Object} - All metrics
 */
const getAllMetrics = () => {
  return customMetricsStore;
};

/**
 * Get Prometheus registry
 * @returns {Object} - Prometheus registry
 */
const getPrometheusRegistry = () => {
  return prometheusClient ? prometheusClient.registry : null;
};

module.exports = {
  createMetric,
  getAllMetrics,
  getPrometheusRegistry,
  METRIC_TYPES
};
