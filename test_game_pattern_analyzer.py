#!/usr/bin/env python3
"""
Test script for UUFT Game Theory Pattern Analyzer
"""

import os
import logging
import numpy as np
import matplotlib.pyplot as plt
from uuft_game_analyzer import UUFTGameScenario
from uuft_game_simulator import UUFTGameSimulator
from uuft_game_pattern_analyzer import UUFTGamePatternAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_game_pattern.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('Test_Game_Pattern')

# Constants
RESULTS_DIR = "uuft_results/game"
os.makedirs(RESULTS_DIR, exist_ok=True)

def test_game_pattern_analyzer():
    """Test the game theory pattern analyzer."""
    logger.info("Testing game theory pattern analyzer")
    
    # Create a Prisoner's Dilemma game
    game = UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.0
    )
    
    # Create simulator
    simulator = UUFTGameSimulator(
        game_scenario=game,
        num_agents=50,
        learning_rate=0.1,
        uuft_parameters={
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.7,
            "temporal_stability": 0.8
        }
    )
    
    # Run simulation
    logger.info("Running game simulation")
    simulator.run_simulation(num_steps=50)
    
    # Create pattern analyzer
    analyzer = UUFTGamePatternAnalyzer(
        game_simulator=simulator,
        pattern_threshold=0.1
    )
    
    # Run analyses
    logger.info("Running pattern analyses")
    
    # Analyze strategy distribution
    strategy_result = analyzer.analyze_strategy_distribution()
    logger.info(f"Strategy distribution analysis: {strategy_result}")
    
    # Analyze temporal stability
    temporal_result = analyzer.analyze_temporal_stability()
    logger.info(f"Temporal stability analysis: {temporal_result}")
    
    # Analyze payoff distribution
    payoff_result = analyzer.analyze_payoff_distribution()
    logger.info(f"Payoff distribution analysis: {payoff_result}")
    
    # Analyze equilibrium convergence
    equilibrium_result = analyzer.analyze_equilibrium_convergence()
    logger.info(f"Equilibrium convergence analysis: {equilibrium_result}")
    
    # Create comprehensive report
    report = analyzer.create_comprehensive_report(
        save_path=os.path.join(RESULTS_DIR, "test_game_analysis_report.json")
    )
    
    # Visualize 18/82 patterns
    analyzer.visualize_1882_patterns(
        save_path=os.path.join(RESULTS_DIR, "test_game_1882_patterns.png")
    )
    
    # Test with UUFT bias
    logger.info("Testing pattern analyzer with UUFT bias")
    game_with_bias = UUFTGameScenario(
        game_type="prisoners_dilemma",
        num_players=2,
        num_strategies=2,
        uuft_bias=0.7
    )
    
    # Create simulator with bias
    simulator_with_bias = UUFTGameSimulator(
        game_scenario=game_with_bias,
        num_agents=50,
        learning_rate=0.1,
        uuft_parameters={
            "pi_influence": 0.8,
            "pattern_1882_strength": 0.9,
            "temporal_stability": 0.8
        }
    )
    
    # Run simulation with bias
    logger.info("Running game simulation with UUFT bias")
    simulator_with_bias.run_simulation(num_steps=50)
    
    # Create pattern analyzer with bias
    analyzer_with_bias = UUFTGamePatternAnalyzer(
        game_simulator=simulator_with_bias,
        pattern_threshold=0.1
    )
    
    # Create comprehensive report with bias
    report_with_bias = analyzer_with_bias.create_comprehensive_report(
        save_path=os.path.join(RESULTS_DIR, "test_game_analysis_report_with_bias.json")
    )
    
    # Visualize 18/82 patterns with bias
    analyzer_with_bias.visualize_1882_patterns(
        save_path=os.path.join(RESULTS_DIR, "test_game_1882_patterns_with_bias.png")
    )
    
    logger.info("Game theory pattern analyzer test completed successfully")

if __name__ == "__main__":
    test_game_pattern_analyzer()
