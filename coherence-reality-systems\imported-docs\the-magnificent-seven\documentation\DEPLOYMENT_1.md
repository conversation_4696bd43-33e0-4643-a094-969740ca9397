# NovaConnect UAC Deployment Guide

This document provides comprehensive instructions for deploying NovaConnect UAC to Google Cloud Platform (GCP) and Google Cloud Marketplace.

## Overview

NovaConnect UAC can be deployed in several ways:

1. **Local Development**: For development and testing
2. **Docker Compose**: For local testing with dependencies
3. **Kubernetes (GKE)**: For staging and production environments
4. **Google Cloud Marketplace**: For customer deployments

## Prerequisites

Before deploying NovaConnect UAC, ensure you have the following:

- Google Cloud Platform account
- Google Kubernetes Engine (GKE) cluster
- Google Container Registry (GCR) access
- Google Cloud Storage bucket for artifacts
- Google Cloud IAM service account with appropriate permissions
- Docker and Docker Compose installed locally
- kubectl configured to access your GKE cluster
- Helm 3 installed

## Local Development Deployment

For local development:

```bash
# Clone the repository
git clone https://github.com/novafuse/novafuse-uac.git
cd novafuse-uac

# Install dependencies
npm install

# Start the application
npm run dev
```

## Docker Compose Deployment

For local testing with dependencies:

```bash
# Build and start the containers
docker-compose up -d

# Check the logs
docker-compose logs -f

# Stop the containers
docker-compose down
```

## Kubernetes (GKE) Deployment

### Manual Deployment

For manual deployment to GKE:

```bash
# Set environment variables
export PROJECT_ID=your-project-id
export GKE_CLUSTER=your-cluster-name
export GKE_ZONE=your-cluster-zone
export IMAGE_TAG=your-image-tag

# Configure kubectl
gcloud container clusters get-credentials $GKE_CLUSTER --zone $GKE_ZONE --project $PROJECT_ID

# Build and push the Docker image
docker build -t gcr.io/$PROJECT_ID/novafuse-uac:$IMAGE_TAG .
docker push gcr.io/$PROJECT_ID/novafuse-uac:$IMAGE_TAG

# Update image tag in deployment files
sed -i "s|IMAGE_TAG|$IMAGE_TAG|g" k8s/staging/*.yaml

# Apply Kubernetes manifests
kubectl apply -f k8s/staging/namespace.yaml
kubectl apply -f k8s/staging/configmap.yaml
kubectl apply -f k8s/staging/secret.yaml
kubectl apply -f k8s/staging/deployment.yaml
kubectl apply -f k8s/staging/service.yaml
kubectl apply -f k8s/staging/ingress.yaml

# Check deployment status
kubectl get pods -n novafuse-staging
```

### Automated Deployment with CI/CD

NovaConnect UAC uses GitHub Actions for CI/CD:

1. Push to the `develop` branch triggers deployment to staging
2. Push to the `main` branch triggers deployment to production
3. Manual workflow dispatch can deploy to staging or production

The CI/CD pipeline performs the following steps:

1. Run tests (unit, integration, security)
2. Build and push Docker image
3. Deploy to GKE
4. Run post-deployment tests
5. Notify on success or failure

## Google Cloud Marketplace Deployment

### Preparing for Marketplace

To prepare NovaConnect UAC for Google Cloud Marketplace:

1. Create a Google Cloud Marketplace Partner account
2. Set up a Google Cloud Marketplace listing
3. Prepare the Marketplace package

### Building the Marketplace Package

```bash
# Install mpdev tool
curl -LO https://github.com/GoogleCloudPlatform/marketplace-tools/releases/download/v0.3.5/mpdev_linux_amd64
chmod +x mpdev_linux_amd64
sudo mv mpdev_linux_amd64 /usr/local/bin/mpdev

# Set environment variables
export PROJECT_ID=your-project-id
export IMAGE_TAG=your-image-tag

# Update image tag in marketplace configuration
sed -i "s|IMAGE_TAG|$IMAGE_TAG|g" marketplace/schema.yaml

# Package for marketplace
cd marketplace
mpdev pkg package .

# Validate marketplace package
mpdev pkg verify
```

### Submitting to Marketplace

```bash
# Upload to Google Cloud Storage
gsutil cp *.tar.gz gs://your-marketplace-bucket/

# Submit to Partner Portal
cd ../scripts
./submit-to-partner-portal.sh your-api-key
```

## Environment Configuration

### Configuration Files

NovaConnect UAC uses the following configuration files:

- `.env`: Environment variables for local development
- `config/default.json`: Default configuration
- `config/production.json`: Production configuration
- `k8s/staging/configmap.yaml`: Staging environment configuration
- `k8s/production/configmap.yaml`: Production environment configuration

### Environment Variables

Key environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| NODE_ENV | Environment (development, staging, production) | development |
| PORT | HTTP port | 3001 |
| MONGODB_URI | MongoDB connection URI | mongodb://localhost:27017/novafuse |
| REDIS_URI | Redis connection URI | redis://localhost:6379 |
| LOG_LEVEL | Logging level | info |
| METRICS_ENABLED | Enable metrics | true |
| TRACING_ENABLED | Enable tracing | true |
| CLUSTER_ENABLED | Enable cluster mode | false |
| CACHE_ENABLED | Enable caching | true |
| FEATURE_FLAG_SOURCE | Feature flag source | file |
| DEFAULT_TIER | Default product tier | core |

## Scaling

NovaConnect UAC can scale in several ways:

1. **Vertical Scaling**: Increase CPU and memory resources
2. **Horizontal Scaling**: Increase the number of replicas
3. **Cluster Mode**: Enable cluster mode to utilize all CPU cores
4. **Caching**: Enable caching to reduce database load
5. **Database Scaling**: Scale MongoDB and Redis

### Horizontal Pod Autoscaling (HPA)

NovaConnect UAC uses Horizontal Pod Autoscaling in Kubernetes:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: novafuse-uac
  namespace: novafuse-production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: novafuse-uac
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Monitoring and Logging

NovaConnect UAC integrates with Google Cloud Operations for monitoring and logging:

1. **Cloud Monitoring**: Metrics and dashboards
2. **Cloud Logging**: Centralized logging
3. **Cloud Trace**: Distributed tracing
4. **Cloud Profiler**: Performance profiling
5. **Error Reporting**: Error tracking and alerting

### Prometheus Metrics

NovaConnect UAC exposes Prometheus metrics at `/metrics`:

- HTTP request count, duration, and errors
- Node.js metrics (memory, CPU, event loop)
- MongoDB and Redis metrics
- Custom business metrics

### Logging

NovaConnect UAC uses structured logging with Winston and Google Cloud Logging:

```javascript
logger.info('API request received', {
  method: req.method,
  path: req.path,
  userId: req.user?.id,
  requestId: req.requestId
});
```

## Security

NovaConnect UAC implements several security measures:

1. **HTTPS**: All traffic is encrypted
2. **Authentication**: JWT-based authentication
3. **Authorization**: Role-based access control
4. **Rate Limiting**: Prevents abuse
5. **Input Validation**: Prevents injection attacks
6. **Secret Management**: Secure storage of secrets
7. **Network Policies**: Restricts network traffic
8. **Security Headers**: Prevents common web vulnerabilities

### Secret Management

NovaConnect UAC uses Google Cloud Secret Manager for secrets:

```javascript
const { SecretManagerServiceClient } = require('@google-cloud/secret-manager');
const secretManager = new SecretManagerServiceClient();

async function getSecret(secretName) {
  const name = `projects/${process.env.GCP_PROJECT_ID}/secrets/${secretName}/versions/latest`;
  const [version] = await secretManager.accessSecretVersion({ name });
  return version.payload.data.toString('utf8');
}
```

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if MongoDB and Redis are running
2. **Authentication Failed**: Check if JWT secret is correct
3. **Resource Limits**: Check if CPU or memory limits are reached
4. **Rate Limiting**: Check if rate limits are too restrictive
5. **Network Issues**: Check if network policies are blocking traffic

### Debugging

```bash
# Check pod status
kubectl get pods -n novafuse-production

# Check pod logs
kubectl logs -f deployment/novafuse-uac -n novafuse-production

# Check pod events
kubectl describe pod -l app=novafuse-uac -n novafuse-production

# Check service
kubectl get service novafuse-uac-service -n novafuse-production

# Check ingress
kubectl get ingress novafuse-uac-ingress -n novafuse-production
```

## Conclusion

This deployment guide provides comprehensive instructions for deploying NovaConnect UAC to Google Cloud Platform and Google Cloud Marketplace. By following these instructions, you can ensure a smooth and successful deployment.
