{"csdeValue": 3142.59, "performanceFactor": 3142, "nistComponent": {"processedValue": 10, "complianceFrameworks": [{"name": "NIST CSF", "version": "1.1", "categories": ["Identify", "Protect", "Detect", "Respond", "Recover"], "controlsMapped": 108, "coveragePercentage": 92.3}, {"name": "NIST 800-53", "version": "Rev 5", "categories": ["Access Control", "Audit and Accountability", "Security Assessment", "Configuration Management", "Contingency Planning"], "controlsMapped": 325, "coveragePercentage": 87.6}], "importantControls": ["AC-3", "AC-6", "CM-6", "IA-2", "SC-7"], "controlEffectiveness": 0.95}, "gcpComponent": {"processedValue": 10, "services": [{"name": "Compute Engine", "resourceCount": 42, "securityScore": 0.89}, {"name": "Cloud Storage", "resourceCount": 18, "securityScore": 0.92}, {"name": "Cloud IAM", "resourceCount": 76, "securityScore": 0.85}], "securityRecommendations": [{"service": "Compute Engine", "recommendation": "Enable OS Login for all instances", "severity": "MEDIUM", "impact": 0.15}, {"service": "Cloud Storage", "recommendation": "Enable bucket versioning", "severity": "LOW", "impact": 0.08}], "infrastructureEfficiency": 0.93}, "cyberSafetyComponent": {"processedValue": 31.42, "threatIntelligence": {"threatScore": 65, "activeThreatCount": 12, "criticalThreats": 3}, "vulnerabilityAssessment": {"totalVulnerabilities": 87, "criticalVulnerabilities": 5, "highVulnerabilities": 18, "mediumVulnerabilities": 42, "lowVulnerabilities": 22}, "aiInsights": [{"category": "Anomaly Detection", "confidence": 0.92, "description": "Unusual login pattern detected for service accounts"}, {"category": "Threat Prediction", "confidence": 0.87, "description": "Increased risk of data exfiltration in the next 7 days"}], "securityPosture": 0.78}, "tensorProduct": 100, "fusionResult": 100.31, "remediationActions": [{"id": "action-1", "title": "Update Web Application", "description": "Update the web application to the latest version to fix the XSS vulnerability", "severity": "HIGH", "targetField": "sourceProperties.vulnerabilityDetails.fixAvailable", "enhancedValue": true, "estimatedEffort": "MEDIUM", "estimatedImpact": "HIGH", "automationPossible": true, "automationScript": "gcloud compute ssh instance-name --command='sudo apt-get update && sudo apt-get upgrade -y'"}, {"id": "action-2", "title": "Implement Input Validation", "description": "Implement proper input validation to prevent XSS attacks", "severity": "MEDIUM", "targetField": "nextSteps", "enhancedValue": ["Update web application to latest version", "Apply security patch", "Implement input validation", "Add Content Security Policy (CSP) headers"], "estimatedEffort": "HIGH", "estimatedImpact": "HIGH", "automationPossible": false, "codeExample": "// Example input validation\nfunction validateInput(input) {\n  return input.replace(/[<>\"'&]/g, (c) => {\n    return {'<': '&lt;', '>': '&gt;', '\"': '&quot;', \"'\": '&#39;', '&': '&amp;'}[c];\n  });\n}"}, {"id": "action-3", "title": "Enable Firewall Rules", "description": "Configure firewall rules to restrict access to the vulnerable service", "severity": "MEDIUM", "targetField": "nextSteps", "enhancedValue": ["Update web application to latest version", "Apply security patch", "Implement input validation", "Add Content Security Policy (CSP) headers", "Enable firewall rules to restrict access"], "estimatedEffort": "LOW", "estimatedImpact": "MEDIUM", "automationPossible": true, "automationScript": "gcloud compute firewall-rules create restrict-access --direction=INGRESS --priority=1000 --network=default --action=DENY --rules=tcp:80,tcp:443 --source-ranges=0.0.0.0/0 --target-tags=web-server"}], "complianceImpact": {"frameworks": [{"name": "PCI DSS", "version": "3.2.1", "requirements": [{"id": "6.5.1", "description": "Address common coding vulnerabilities in software-development processes", "status": "NON_COMPLIANT", "remediation": "Implement secure coding practices and input validation"}]}, {"name": "HIPAA", "version": "2013", "requirements": [{"id": "164.308(a)(1)(ii)(B)", "description": "Risk analysis", "status": "COMPLIANT", "remediation": null}]}], "overallStatus": "PARTIALLY_COMPLIANT", "criticalGaps": 1, "highGaps": 2, "mediumGaps": 5, "lowGaps": 3}, "riskAssessment": {"inherentRisk": "HIGH", "residualRisk": "MEDIUM", "businessImpact": "MEDIUM", "likelihoodOfExploit": "MEDIUM", "dataExposureRisk": "HIGH"}, "calculatedAt": "2025-05-08T15:30:45.123Z"}