#!/usr/bin/env python3
"""
Command-line interface for visualizing quantum benchmark results.
"""

import argparse
import sys
from pathlib import Path

# Add parent directory to path to import from src
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.visualization.benchmark_visualizer import visualize_benchmark_results

def main():
    """Main entry point for the visualization script."""
    parser = argparse.ArgumentParser(
        description='Visualize quantum benchmark results',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument(
        'results_dir',
        help='Directory containing benchmark results (should contain benchmark_results.json)'
    )
    
    parser.add_argument(
        '-o', '--output-dir',
        default='benchmark_visualizations',
        help='Directory to save visualizations'
    )
    
    parser.add_argument(
        '--format',
        choices=['html', 'png', 'both'],
        default='both',
        help='Output format for visualizations'
    )
    
    args = parser.parse_args()
    
    print(f"Generating visualizations from: {args.results_dir}")
    print(f"Saving output to: {args.output_dir}")
    print(f"Output format: {args.format}")
    
    try:
        visualize_benchmark_results(args.results_dir, args.output_dir)
        print("\nVisualization complete!")
        print(f"Report generated: {Path(args.output_dir).resolve()}/benchmark_report.html")
    except Exception as e:
        print(f"\nError generating visualizations: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
