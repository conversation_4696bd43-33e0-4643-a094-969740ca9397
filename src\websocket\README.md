# WebSocket-Based Real-Time Data Flow

This module provides a WebSocket-based real-time data flow system for connecting the Self-Healing Tensor, 3D Tensor Visualization, Analytics Dashboard, and other components.

## Overview

The WebSocket-based real-time data flow system enables seamless communication between components in real-time. It provides:

- WebSocket server for handling connections and message routing
- WebSocket client for connecting to the server
- Message handlers for processing component-specific messages
- Channel-based publish/subscribe mechanism
- Automatic reconnection and heartbeat

## Architecture

The real-time data flow system consists of the following components:

### Core Components

- **WebSocketServer**: The main server that handles WebSocket connections and message routing.
- **WebSocketClient**: A client for connecting to the WebSocket server.
- **MessageHandlers**: Handlers for processing component-specific messages.

### Message Flow

1. Components connect to the WebSocket server using the WebSocketClient.
2. Components subscribe to channels they are interested in.
3. When a component wants to send data to another component, it publishes a message to a channel.
4. The WebSocket server routes the message to all subscribers of that channel.
5. The receiving components process the message using their message handlers.

## Usage

### Basic Usage

```javascript
const { createRealTimeCommunication } = require('./websocket');
const { createUnifiedIntegration } = require('./integration');
const SelfHealingTensor = require('./quantum/self-healing-tensor');

// Create components
const selfHealingTensor = new SelfHealingTensor();
const visualizationSystem = createVisualizationSystem();
const analyticsDashboard = createAnalyticsDashboard();

// Create unified integration
const unifiedIntegration = createUnifiedIntegration({
  tensor: selfHealingTensor,
  visualization: visualizationSystem,
  analytics: analyticsDashboard
});

// Create real-time communication system
const realTimeCommunication = createRealTimeCommunication({
  tensorAdapter: unifiedIntegration.adapters.tensor,
  visualizationAdapter: unifiedIntegration.adapters.visualization,
  analyticsAdapter: unifiedIntegration.adapters.analytics
});

// Create clients for each component
const tensorClient = realTimeCommunication.createClient('tensor');
const visualizationClient = realTimeCommunication.createClient('visualization');
const analyticsClient = realTimeCommunication.createClient('analytics');

// Connect clients
await tensorClient.connect();
await visualizationClient.connect();
await analyticsClient.connect();

// Register a tensor
const registerResponse = await tensorClient.send({
  component: 'tensor',
  type: 'register-tensor',
  id: 'example-tensor',
  tensor: {
    values: [0.5, 0.6, 0.7, 0.8, 0.9]
  },
  domain: 'universal'
});

// Create a visualization
const createVisualizationResponse = await visualizationClient.send({
  component: 'visualization',
  type: 'create-visualization',
  visualizationType: '3d_tensor_visualization',
  data: {
    tensor: registerResponse.result.tensor,
    dimensions: [5, 1, 1]
  }
});

// Subscribe to real-time updates
await tensorClient.subscribe('tensor-updates');
await visualizationClient.subscribe('visualization-updates');
await analyticsClient.subscribe('analytics-updates');

// Set up event handlers
tensorClient.on('message:tensor-updates', (data) => {
  console.log('Received tensor update:', data);
});

// Publish updates
tensorClient.publish('tensor-updates', {
  type: 'tensor-updated',
  id: 'example-tensor',
  tensor: {
    values: [0.6, 0.7, 0.8, 0.9, 1.0],
    health: 0.9,
    entropyContainment: 0.02
  }
});
```

### Advanced Usage

For more advanced usage, see the example in `examples/real-time-data-flow-example.js`.

## API Reference

### WebSocketServer

The main server that handles WebSocket connections and message routing.

#### Methods

- `start()`: Start the WebSocket server.
- `stop()`: Stop the WebSocket server.
- `publish(channel, data, excludeClientId)`: Publish a message to a channel.
- `getMetrics()`: Get metrics about the server.
- `getClientCount()`: Get the number of connected clients.
- `getChannelCount()`: Get the number of active channels.
- `getClientsInChannel(channel)`: Get the clients subscribed to a channel.
- `getChannelsForClient(clientId)`: Get the channels a client is subscribed to.

### WebSocketClient

A client for connecting to the WebSocket server.

#### Methods

- `connect()`: Connect to the WebSocket server.
- `disconnect(code, reason)`: Disconnect from the WebSocket server.
- `send(message, timeout)`: Send a message to the server.
- `subscribe(channel, callback)`: Subscribe to a channel.
- `unsubscribe(channel, callback)`: Unsubscribe from a channel.
- `publish(channel, data)`: Publish a message to a channel.
- `getMetrics()`: Get metrics about the client.
- `getSubscriptions()`: Get the channels the client is subscribed to.
- `isConnected()`: Check if the client is connected.

### MessageHandlers

Handlers for processing component-specific messages.

#### TensorMessageHandler

Handles tensor-related messages.

##### Supported Messages

- `register-tensor`: Register a tensor.
- `get-tensor`: Get a tensor.
- `update-tensor`: Update a tensor.
- `heal-tensor`: Heal a tensor.
- `damage-tensor`: Damage a tensor.
- `get-healing-history`: Get healing history for a tensor.

#### VisualizationMessageHandler

Handles visualization-related messages.

##### Supported Messages

- `get-visualization-types`: Get available visualization types.
- `create-visualization`: Create a visualization.
- `update-visualization`: Update a visualization.
- `delete-visualization`: Delete a visualization.

#### AnalyticsMessageHandler

Handles analytics-related messages.

##### Supported Messages

- `get-metrics`: Get available metrics.
- `get-dashboards`: Get available dashboards.
- `get-dashboard`: Get a dashboard by ID.
- `execute-query`: Execute a query.

## Events

The real-time data flow system uses events to communicate between components. Here are the main events:

### WebSocketServer Events

- `client-connected`: Emitted when a client connects.
- `client-disconnected`: Emitted when a client disconnects.
- `client-error`: Emitted when a client error occurs.
- `client-subscribed`: Emitted when a client subscribes to a channel.
- `client-unsubscribed`: Emitted when a client unsubscribes from a channel.
- `message-published`: Emitted when a message is published to a channel.
- `message`: Emitted when a message is received.

### WebSocketClient Events

- `connected`: Emitted when the client connects.
- `disconnected`: Emitted when the client disconnects.
- `error`: Emitted when an error occurs.
- `welcome`: Emitted when the client receives a welcome message.
- `pong`: Emitted when the client receives a pong message.
- `subscribed`: Emitted when the client subscribes to a channel.
- `unsubscribed`: Emitted when the client unsubscribes from a channel.
- `published`: Emitted when the client publishes a message.
- `channel-message`: Emitted when the client receives a message from a channel.
- `server-error`: Emitted when the client receives an error from the server.
- `message:${channel}`: Emitted when the client receives a message from a specific channel.

## Examples

See the `examples` directory for usage examples:

- `real-time-data-flow-example.js`: A complete example that demonstrates how to use the WebSocket-based real-time data flow to connect the Self-Healing Tensor, 3D Tensor Visualization, and Analytics Dashboard.
