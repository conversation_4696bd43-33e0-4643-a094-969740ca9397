openapi: 3.0.0
info:
  title: Privacy Management API - Regulatory Compliance
  description: API for managing regulatory compliance in the Privacy Management system
  version: 1.0.0
  contact:
    name: NovaFuse API Support
    email: <EMAIL>
    url: https://novafuse.io/support
  license:
    name: Proprietary
    url: https://novafuse.io/license

servers:
  - url: /api/privacy/management
    description: Privacy Management API

tags:
  - name: Regulatory Frameworks
    description: Operations related to regulatory frameworks
  - name: Compliance Requirements
    description: Operations related to compliance requirements
  - name: Compliance Status
    description: Operations related to compliance status tracking
  - name: Compliance Reports
    description: Operations related to compliance reporting
  - name: Regulatory Updates
    description: Operations related to regulatory updates

paths:
  /compliance/frameworks:
    get:
      summary: Get all regulatory frameworks
      description: Returns a list of all regulatory frameworks
      operationId: getAllFrameworks
      tags:
        - Regulatory Frameworks
      parameters:
        - name: status
          in: query
          description: Filter by framework status
          schema:
            type: string
            enum: [active, inactive, draft, superseded, archived]
        - name: region
          in: query
          description: Filter by region
          schema:
            type: string
        - name: category
          in: query
          description: Filter by category
          schema:
            type: string
            enum: [privacy, security, governance, industry, general]
        - name: search
          in: query
          description: Search term for framework name, code, or description
          schema:
            type: string
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: sort
          in: query
          description: Sort field(s), prefix with - for descending order
          schema:
            type: string
            example: name,-effectiveDate
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/RegulatoryFramework'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/frameworks/{id}:
    get:
      summary: Get regulatory framework by ID
      description: Returns a regulatory framework by ID
      operationId: getFrameworkById
      tags:
        - Regulatory Frameworks
      parameters:
        - name: id
          in: path
          description: Framework ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/RegulatoryFramework'
        '404':
          description: Framework not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/frameworks/code/{code}:
    get:
      summary: Get regulatory framework by code
      description: Returns a regulatory framework by code
      operationId: getFrameworkByCode
      tags:
        - Regulatory Frameworks
      parameters:
        - name: code
          in: path
          description: Framework code
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/RegulatoryFramework'
        '404':
          description: Framework not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/frameworks/{frameworkId}/requirements:
    get:
      summary: Get compliance requirements for a framework
      description: Returns a list of compliance requirements for a framework
      operationId: getRequirementsByFramework
      tags:
        - Compliance Requirements
      parameters:
        - name: frameworkId
          in: path
          description: Framework ID
          required: true
          schema:
            type: string
        - name: status
          in: query
          description: Filter by requirement status
          schema:
            type: string
            enum: [active, inactive, draft, archived]
        - name: category
          in: query
          description: Filter by category
          schema:
            type: string
            enum: [notice, choice, access, security, retention, transfer, accountability, governance, rights, breach, consent, other]
        - name: search
          in: query
          description: Search term for requirement title, description, or guidance
          schema:
            type: string
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
        - name: sort
          in: query
          description: Sort field(s), prefix with - for descending order
          schema:
            type: string
            example: section,number
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ComplianceRequirement'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/requirements/{id}:
    get:
      summary: Get compliance requirement by ID
      description: Returns a compliance requirement by ID
      operationId: getRequirementById
      tags:
        - Compliance Requirements
      parameters:
        - name: id
          in: path
          description: Requirement ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/ComplianceRequirement'
        '404':
          description: Requirement not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/status/{entityType}/{entityId}/{frameworkId}:
    get:
      summary: Get compliance status for an entity
      description: Returns the compliance status for an entity against a framework
      operationId: getComplianceStatus
      tags:
        - Compliance Status
      parameters:
        - name: entityType
          in: path
          description: Entity type
          required: true
          schema:
            type: string
            enum: [organization, system, process, vendor, dataset]
        - name: entityId
          in: path
          description: Entity ID
          required: true
          schema:
            type: string
        - name: frameworkId
          in: path
          description: Framework ID
          required: true
          schema:
            type: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/ComplianceStatus'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/status/{statusId}/requirements/{requirementId}:
    patch:
      summary: Update compliance status for a requirement
      description: Updates the compliance status for a specific requirement
      operationId: updateRequirementStatus
      tags:
        - Compliance Status
      parameters:
        - name: statusId
          in: path
          description: Compliance status ID
          required: true
          schema:
            type: string
        - name: requirementId
          in: path
          description: Requirement ID
          required: true
          schema:
            type: string
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
              properties:
                status:
                  type: string
                  enum: [compliant, non-compliant, in-progress, not-started, not-applicable]
                  description: Compliance status
                notes:
                  type: string
                  description: Notes about the compliance status
                evidence:
                  type: array
                  description: Evidence supporting the compliance status
                  items:
                    $ref: '#/components/schemas/Evidence'
                assignedTo:
                  type: string
                  description: User ID of the person assigned to this requirement
                dueDate:
                  type: string
                  format: date-time
                  description: Due date for compliance
                completedDate:
                  type: string
                  format: date-time
                  description: Date when compliance was achieved
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/ComplianceStatus'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/reports/{entityType}/{entityId}/{frameworkId}:
    get:
      summary: Generate compliance report
      description: Generates a compliance report for an entity against a framework
      operationId: generateComplianceReport
      tags:
        - Compliance Reports
      parameters:
        - name: entityType
          in: path
          description: Entity type
          required: true
          schema:
            type: string
            enum: [organization, system, process, vendor, dataset]
        - name: entityId
          in: path
          description: Entity ID
          required: true
          schema:
            type: string
        - name: frameworkId
          in: path
          description: Framework ID
          required: true
          schema:
            type: string
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/ComplianceReport'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/mapping/{sourceFrameworkId}/{targetFrameworkId}:
    get:
      summary: Map requirements between frameworks
      description: Maps requirements between two regulatory frameworks
      operationId: mapRequirementsBetweenFrameworks
      tags:
        - Compliance Requirements
      parameters:
        - name: sourceFrameworkId
          in: path
          description: Source framework ID
          required: true
          schema:
            type: string
        - name: targetFrameworkId
          in: path
          description: Target framework ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/RequirementMapping'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /compliance/updates:
    get:
      summary: Get regulatory updates
      description: Returns a list of regulatory updates
      operationId: getRegulatoryUpdates
      tags:
        - Regulatory Updates
      parameters:
        - name: region
          in: query
          description: Filter by region
          schema:
            type: string
        - name: framework
          in: query
          description: Filter by framework ID
          schema:
            type: string
        - name: since
          in: query
          description: Filter by date (ISO format)
          schema:
            type: string
            format: date-time
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/RegulatoryUpdate'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    RegulatoryFramework:
      type: object
      properties:
        _id:
          type: string
          description: Framework ID
        name:
          type: string
          description: Framework name
        code:
          type: string
          description: Framework code
        version:
          type: string
          description: Framework version
        description:
          type: string
          description: Framework description
        category:
          type: string
          enum: [privacy, security, governance, industry, general]
          description: Framework category
        regions:
          type: array
          description: Regions where the framework applies
          items:
            type: string
        countries:
          type: array
          description: Countries where the framework applies
          items:
            type: string
        effectiveDate:
          type: string
          format: date-time
          description: Date when the framework became effective
        lastUpdated:
          type: string
          format: date-time
          description: Date when the framework was last updated
        authority:
          type: string
          description: Authority responsible for the framework
        authorityUrl:
          type: string
          description: URL of the authority
        documentUrl:
          type: string
          description: URL of the framework document
        status:
          type: string
          enum: [active, inactive, draft, superseded, archived]
          description: Framework status
        supersededBy:
          type: string
          description: ID of the framework that supersedes this one
        relatedFrameworks:
          type: array
          description: Related frameworks
          items:
            type: object
            properties:
              framework:
                type: string
                description: Related framework ID
              relationship:
                type: string
                enum: [supersedes, implements, complements, conflicts]
                description: Relationship type
              notes:
                type: string
                description: Notes about the relationship
        createdAt:
          type: string
          format: date-time
          description: Creation date
        updatedAt:
          type: string
          format: date-time
          description: Last update date

    ComplianceRequirement:
      type: object
      properties:
        _id:
          type: string
          description: Requirement ID
        framework:
          type: string
          description: Framework ID
        section:
          type: string
          description: Section identifier
        number:
          type: string
          description: Requirement number
        title:
          type: string
          description: Requirement title
        description:
          type: string
          description: Requirement description
        guidance:
          type: string
          description: Implementation guidance
        category:
          type: string
          enum: [notice, choice, access, security, retention, transfer, accountability, governance, rights, breach, consent, other]
          description: Requirement category
        subCategory:
          type: string
          description: Requirement sub-category
        applicability:
          type: string
          enum: [all, conditional, optional]
          description: Applicability of the requirement
        applicabilityCondition:
          type: string
          description: Condition for applicability
        risk:
          type: string
          enum: [low, medium, high, critical]
          description: Risk level
        implementationEffort:
          type: string
          enum: [low, medium, high]
          description: Implementation effort
        keywords:
          type: array
          description: Keywords related to the requirement
          items:
            type: string
        relatedRequirements:
          type: array
          description: Related requirements
          items:
            type: object
            properties:
              framework:
                type: string
                description: Related framework ID
              requirement:
                type: string
                description: Related requirement ID
              requirementCode:
                type: string
                description: Related requirement code
              requirementTitle:
                type: string
                description: Related requirement title
              mappingStrength:
                type: string
                enum: [strong, medium, weak]
                description: Strength of the mapping
              notes:
                type: string
                description: Notes about the relationship
        implementationGuidance:
          type: string
          description: Detailed implementation guidance
        evidenceRequired:
          type: array
          description: Evidence required for compliance
          items:
            type: string
        status:
          type: string
          enum: [active, inactive, draft, archived]
          description: Requirement status
        createdAt:
          type: string
          format: date-time
          description: Creation date
        updatedAt:
          type: string
          format: date-time
          description: Last update date

    Evidence:
      type: object
      properties:
        _id:
          type: string
          description: Evidence ID
        type:
          type: string
          enum: [document, screenshot, link, text, other]
          description: Evidence type
        title:
          type: string
          description: Evidence title
        description:
          type: string
          description: Evidence description
        url:
          type: string
          description: URL to evidence
        fileId:
          type: string
          description: File ID
        fileName:
          type: string
          description: File name
        fileType:
          type: string
          description: File type
        fileSize:
          type: number
          description: File size in bytes
        content:
          type: string
          description: Text content
        addedBy:
          type: string
          description: User ID who added the evidence
        addedAt:
          type: string
          format: date-time
          description: Date when the evidence was added

    RequirementStatus:
      type: object
      properties:
        _id:
          type: string
          description: Requirement status ID
        requirement:
          type: string
          description: Requirement ID
        requirementCode:
          type: string
          description: Requirement code
        requirementTitle:
          type: string
          description: Requirement title
        status:
          type: string
          enum: [compliant, non-compliant, in-progress, not-started, not-applicable]
          description: Compliance status
        evidence:
          type: array
          description: Evidence supporting the compliance status
          items:
            $ref: '#/components/schemas/Evidence'
        notes:
          type: string
          description: Notes about the compliance status
        assignedTo:
          type: string
          description: User ID of the person assigned to this requirement
        dueDate:
          type: string
          format: date-time
          description: Due date for compliance
        completedDate:
          type: string
          format: date-time
          description: Date when compliance was achieved
        lastUpdatedBy:
          type: string
          description: User ID who last updated the status
        lastUpdatedAt:
          type: string
          format: date-time
          description: Date when the status was last updated
        history:
          type: array
          description: History of status changes
          items:
            type: object
            properties:
              status:
                type: string
                enum: [compliant, non-compliant, in-progress, not-started, not-applicable]
                description: Previous status
              notes:
                type: string
                description: Notes about the status change
              updatedBy:
                type: string
                description: User ID who updated the status
              updatedAt:
                type: string
                format: date-time
                description: Date when the status was updated

    ComplianceStatus:
      type: object
      properties:
        _id:
          type: string
          description: Compliance status ID
        entityType:
          type: string
          enum: [organization, system, process, vendor, dataset]
          description: Entity type
        entityId:
          type: string
          description: Entity ID
        framework:
          type: string
          description: Framework ID
        frameworkName:
          type: string
          description: Framework name
        frameworkCode:
          type: string
          description: Framework code
        frameworkVersion:
          type: string
          description: Framework version
        status:
          type: string
          enum: [compliant, non-compliant, in-progress, not-started, not-applicable]
          description: Overall compliance status
        progress:
          type: number
          minimum: 0
          maximum: 100
          description: Compliance progress percentage
        requirementStatuses:
          type: array
          description: Status of individual requirements
          items:
            $ref: '#/components/schemas/RequirementStatus'
        lastAssessment:
          type: string
          format: date-time
          description: Date of the last assessment
        nextAssessment:
          type: string
          format: date-time
          description: Date of the next scheduled assessment
        assessmentFrequency:
          type: string
          enum: [monthly, quarterly, semi-annual, annual, bi-annual, custom]
          description: Assessment frequency
        assessmentFrequencyDays:
          type: number
          description: Custom assessment frequency in days
        notes:
          type: string
          description: Notes about the compliance status
        createdAt:
          type: string
          format: date-time
          description: Creation date
        updatedAt:
          type: string
          format: date-time
          description: Last update date

    ComplianceReport:
      type: object
      properties:
        entityType:
          type: string
          enum: [organization, system, process, vendor, dataset]
          description: Entity type
        entityId:
          type: string
          description: Entity ID
        framework:
          type: object
          properties:
            id:
              type: string
              description: Framework ID
            name:
              type: string
              description: Framework name
            code:
              type: string
              description: Framework code
            version:
              type: string
              description: Framework version
            description:
              type: string
              description: Framework description
        status:
          type: string
          enum: [compliant, non-compliant, in-progress, not-started, not-applicable]
          description: Overall compliance status
        progress:
          type: number
          minimum: 0
          maximum: 100
          description: Compliance progress percentage
        lastAssessment:
          type: string
          format: date-time
          description: Date of the last assessment
        nextAssessment:
          type: string
          format: date-time
          description: Date of the next scheduled assessment
        sectionCompliance:
          type: object
          additionalProperties:
            type: object
            properties:
              total:
                type: number
                description: Total requirements in the section
              compliant:
                type: number
                description: Compliant requirements in the section
              progress:
                type: number
                minimum: 0
                maximum: 100
                description: Section compliance progress percentage
        requirementsBySection:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/RequirementStatus'
        summary:
          type: object
          properties:
            total:
              type: number
              description: Total requirements
            compliant:
              type: number
              description: Compliant requirements
            nonCompliant:
              type: number
              description: Non-compliant requirements
            inProgress:
              type: number
              description: Requirements in progress
            notStarted:
              type: number
              description: Requirements not started
            notApplicable:
              type: number
              description: Requirements not applicable
        generatedAt:
          type: string
          format: date-time
          description: Report generation date

    RequirementMapping:
      type: object
      properties:
        sourceFramework:
          type: object
          properties:
            id:
              type: string
              description: Source framework ID
            name:
              type: string
              description: Source framework name
            code:
              type: string
              description: Source framework code
            version:
              type: string
              description: Source framework version
        targetFramework:
          type: object
          properties:
            id:
              type: string
              description: Target framework ID
            name:
              type: string
              description: Target framework name
            code:
              type: string
              description: Target framework code
            version:
              type: string
              description: Target framework version
        mapping:
          type: object
          additionalProperties:
            type: array
            items:
              type: object
              properties:
                id:
                  type: string
                  description: Target requirement ID
                code:
                  type: string
                  description: Target requirement code
                title:
                  type: string
                  description: Target requirement title
                mappingStrength:
                  type: string
                  enum: [strong, medium, weak]
                  description: Strength of the mapping
                matchedKeywords:
                  type: array
                  description: Keywords that matched
                  items:
                    type: string
        generatedAt:
          type: string
          format: date-time
          description: Mapping generation date

    RegulatoryUpdate:
      type: object
      properties:
        id:
          type: string
          description: Update ID
        title:
          type: string
          description: Update title
        description:
          type: string
          description: Update description
        framework:
          type: string
          description: Framework code
        region:
          type: string
          description: Region code
        publishedDate:
          type: string
          format: date-time
          description: Date when the update was published
        effectiveDate:
          type: string
          format: date-time
          description: Date when the update becomes effective
        url:
          type: string
          description: URL to the update details
        severity:
          type: string
          enum: [low, medium, high, critical]
          description: Update severity
        impactedRequirements:
          type: array
          description: Requirements impacted by the update
          items:
            type: object
            properties:
              code:
                type: string
                description: Requirement code
              description:
                type: string
                description: Requirement description

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
        limit:
          type: integer
          description: Number of items per page
        total:
          type: integer
          description: Total number of items
        pages:
          type: integer
          description: Total number of pages

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          description: Error type
        message:
          type: string
          description: Error message

    ValidationError:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          example: Validation Error
        details:
          type: object
          properties:
            params:
              type: array
              items:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                  path:
                    type: array
                    description: Path to the invalid field
                    items:
                      type: string
            query:
              type: array
              items:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                  path:
                    type: array
                    description: Path to the invalid field
                    items:
                      type: string
            body:
              type: array
              items:
                type: object
                properties:
                  message:
                    type: string
                    description: Error message
                  path:
                    type: array
                    description: Path to the invalid field
                    items:
                      type: string
