/**
 * Main Application Module
 */
class App {
  constructor() {
    this.currentPage = 'dashboard';
    this.navLinks = document.querySelectorAll('.nav-link');
    this.pageTitle = document.getElementById('page-title');
    
    this.init();
  }
  
  /**
   * Initialize the application
   */
  init() {
    // Add event listeners to navigation links
    this.navLinks.forEach(link => {
      link.addEventListener('click', this.handleNavigation.bind(this));
    });
    
    // Handle hash changes
    window.addEventListener('hashchange', this.handleHashChange.bind(this));
    
    // Load the initial page based on the hash
    this.handleHashChange();
  }
  
  /**
   * Handle navigation link clicks
   * @param {Event} event - Click event
   */
  handleNavigation(event) {
    event.preventDefault();
    
    const href = event.currentTarget.getAttribute('href');
    
    if (href.startsWith('#')) {
      window.location.hash = href;
    }
  }
  
  /**
   * Handle hash changes
   */
  handleHashChange() {
    const hash = window.location.hash.substring(1) || 'dashboard';
    
    // Update the active navigation link
    this.navLinks.forEach(link => {
      const href = link.getAttribute('href');
      
      if (href === `#${hash}`) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
    
    // Update the page title
    this.updatePageTitle(hash);
    
    // Load the page content
    this.loadPage(hash);
  }
  
  /**
   * Update the page title
   * @param {string} page - Page name
   */
  updatePageTitle(page) {
    const titles = {
      'dashboard': 'Dashboard',
      'processing-activities': 'Data Processing Activities',
      'subject-requests': 'Data Subject Requests',
      'consent-management': 'Consent Management',
      'privacy-notices': 'Privacy Notices',
      'data-breaches': 'Data Breaches',
      'integrations': 'Integrations',
      'reports': 'Reports',
      'notifications': 'Notifications'
    };
    
    this.pageTitle.textContent = titles[page] || 'Dashboard';
  }
  
  /**
   * Load the page content
   * @param {string} page - Page name
   */
  loadPage(page) {
    // For now, we only have the dashboard page implemented
    // In a real application, we would load different page content here
    
    // Hide all page containers
    const containers = document.querySelectorAll('[id$="-container"]');
    containers.forEach(container => {
      if (container.id === 'login-container') {
        return; // Skip the login container
      }
      
      if (container.id === `${page}-container`) {
        container.classList.remove('d-none');
      } else {
        container.classList.add('d-none');
      }
    });
    
    // If the dashboard page is loaded, refresh the dashboard data
    if (page === 'dashboard' && typeof dashboard !== 'undefined' && api.isAuthenticated()) {
      dashboard.loadDashboard();
    }
  }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.app = new App();
});
