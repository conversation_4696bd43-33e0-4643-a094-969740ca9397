<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NovaFuse Logo Concepts</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h1, h2, h3 {
            color: #1a73e8;
        }
        h1 {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2.5em;
            border-bottom: 1px solid #dadce0;
            padding-bottom: 10px;
        }
        h2 {
            margin-top: 40px;
            font-size: 1.8em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 5px;
        }
        h3 {
            font-size: 1.3em;
            margin-top: 30px;
        }
        .logo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }
        .logo-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        .logo-display {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            border-radius: 4px;
            overflow: hidden;
        }
        .logo-description {
            flex: 1;
        }
        .logo-title {
            font-weight: bold;
            font-size: 1.2em;
            margin-bottom: 10px;
            color: #1a73e8;
        }
        .color-palette {
            display: flex;
            margin: 20px 0;
        }
        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8em;
            font-weight: bold;
        }
        .typography-sample {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .font-example {
            margin: 15px 0;
        }
        .font-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .font-sample {
            font-size: 1.8em;
            margin-bottom: 10px;
        }
        .logo-svg {
            max-width: 100%;
            max-height: 100%;
        }
        .logo-concept-1 .logo-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%);
        }
        .logo-concept-2 .logo-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%);
        }
        .logo-concept-3 .logo-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%);
        }
        .logo-concept-4 .logo-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e8f0fe 100%);
        }
        .next-steps {
            background: #e8f0fe;
            padding: 20px;
            border-radius: 8px;
            margin-top: 40px;
        }
        .next-steps h2 {
            border-bottom: none;
            margin-top: 0;
        }
        .next-steps ul {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <h1>NovaFuse Logo Concepts</h1>
    
    <p>The following logo concepts have been designed to represent NovaFuse as a universal integration platform with compliance as the first compelling use case. Each concept captures different aspects of the brand's identity: innovation, integration, security, and transformation.</p>
    
    <div class="logo-grid">
        <div class="logo-card logo-concept-1">
            <div class="logo-display">
                <svg class="logo-svg" width="250" height="150" viewBox="0 0 250 150" xmlns="http://www.w3.org/2000/svg">
                    <!-- The Integration Nexus Concept -->
                    <defs>
                        <linearGradient id="nexus-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#1A365D" />
                            <stop offset="100%" stop-color="#00A3A1" />
                        </linearGradient>
                    </defs>
                    
                    <!-- Central Node -->
                    <circle cx="125" cy="75" r="15" fill="url(#nexus-gradient)" />
                    
                    <!-- Connecting Lines and Nodes -->
                    <line x1="125" y1="75" x2="65" y2="40" stroke="url(#nexus-gradient)" stroke-width="3" />
                    <circle cx="65" cy="40" r="8" fill="url(#nexus-gradient)" />
                    
                    <line x1="125" y1="75" x2="185" y2="40" stroke="url(#nexus-gradient)" stroke-width="3" />
                    <circle cx="185" cy="40" r="8" fill="url(#nexus-gradient)" />
                    
                    <line x1="125" y1="75" x2="185" y2="110" stroke="url(#nexus-gradient)" stroke-width="3" />
                    <circle cx="185" cy="110" r="8" fill="url(#nexus-gradient)" />
                    
                    <line x1="125" y1="75" x2="65" y2="110" stroke="url(#nexus-gradient)" stroke-width="3" />
                    <circle cx="65" cy="110" r="8" fill="url(#nexus-gradient)" />
                    
                    <line x1="125" y1="75" x2="125" y2="25" stroke="url(#nexus-gradient)" stroke-width="3" />
                    <circle cx="125" cy="25" r="8" fill="url(#nexus-gradient)" />
                    
                    <line x1="125" y1="75" x2="125" y2="125" stroke="url(#nexus-gradient)" stroke-width="3" />
                    <circle cx="125" cy="125" r="8" fill="url(#nexus-gradient)" />
                    
                    <!-- Logo Text -->
                    <text x="125" y="160" font-family="Montserrat, sans-serif" font-size="18" font-weight="700" text-anchor="middle" fill="#1A365D">NOVAFUSE</text>
                </svg>
            </div>
            <div class="logo-description">
                <div class="logo-title">Concept 1: The Integration Nexus</div>
                <p>This concept visualizes NovaFuse as the central hub that connects various systems and data sources. The central node represents the Universal API Connector (UAC), with connecting nodes symbolizing different systems and applications.</p>
                <p>The blue-to-teal gradient represents trust, technology, and innovation, while the geometric design conveys precision and reliability.</p>
            </div>
        </div>
        
        <div class="logo-card logo-concept-2">
            <div class="logo-display">
                <svg class="logo-svg" width="250" height="150" viewBox="0 0 250 150" xmlns="http://www.w3.org/2000/svg">
                    <!-- The Data Flow Concept -->
                    <defs>
                        <linearGradient id="flow-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#0A2463" />
                            <stop offset="100%" stop-color="#3E92CC" />
                        </linearGradient>
                    </defs>
                    
                    <!-- Stylized N formed by flowing data streams -->
                    <path d="M70,40 C90,50 90,70 70,80 L70,120 C110,100 130,80 130,60 L130,40 C110,60 90,80 90,100 L90,120 C130,100 150,80 150,60 L150,40 C170,60 190,80 190,100 L190,120" 
                          fill="none" stroke="url(#flow-gradient)" stroke-width="8" stroke-linecap="round" stroke-linejoin="round" />
                    
                    <!-- Data points along the flow -->
                    <circle cx="70" cy="40" r="4" fill="#FF5A5F" />
                    <circle cx="90" cy="100" r="4" fill="#FF5A5F" />
                    <circle cx="130" cy="40" r="4" fill="#FF5A5F" />
                    <circle cx="150" cy="40" r="4" fill="#FF5A5F" />
                    <circle cx="190" cy="120" r="4" fill="#FF5A5F" />
                    
                    <!-- Logo Text -->
                    <text x="125" y="160" font-family="Poppins, sans-serif" font-size="18" font-weight="600" text-anchor="middle" fill="#0A2463">NOVA<tspan font-weight="300">FUSE</tspan></text>
                </svg>
            </div>
            <div class="logo-description">
                <div class="logo-title">Concept 2: The Data Flow</div>
                <p>This concept uses flowing lines to create a stylized "N" that represents data streams being normalized and connected through the UAC. The smooth curves convey the seamless integration capabilities of NovaFuse.</p>
                <p>The blue gradient represents trust and reliability, while the coral data points highlight key transformation moments in the data flow.</p>
            </div>
        </div>
        
        <div class="logo-card logo-concept-3">
            <div class="logo-display">
                <svg class="logo-svg" width="250" height="150" viewBox="0 0 250 150" xmlns="http://www.w3.org/2000/svg">
                    <!-- The Fusion Symbol Concept -->
                    <defs>
                        <linearGradient id="fusion-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#4A148C" />
                            <stop offset="100%" stop-color="#00897B" />
                        </linearGradient>
                    </defs>
                    
                    <!-- Different shapes fusing at the center -->
                    <rect x="60" y="45" width="30" height="30" rx="2" fill="#4A148C" transform="rotate(-15 60 45)" opacity="0.8" />
                    <circle cx="75" cy="95" r="18" fill="#00897B" opacity="0.8" />
                    <polygon points="175,45 195,65 175,85 155,65" fill="#FFC107" opacity="0.8" />
                    <rect x="160" y="85" width="30" height="30" rx="15" fill="#F44336" opacity="0.8" />
                    
                    <!-- Central fusion point -->
                    <circle cx="125" cy="75" r="25" fill="url(#fusion-gradient)" />
                    
                    <!-- Connection lines -->
                    <line x1="85" y1="55" x2="105" y2="65" stroke="#4A148C" stroke-width="2" opacity="0.6" />
                    <line x1="85" y1="95" x2="105" y2="85" stroke="#00897B" stroke-width="2" opacity="0.6" />
                    <line x1="165" y1="55" x2="145" y2="65" stroke="#FFC107" stroke-width="2" opacity="0.6" />
                    <line x1="165" y1="95" x2="145" y2="85" stroke="#F44336" stroke-width="2" opacity="0.6" />
                    
                    <!-- Logo Text -->
                    <text x="125" y="160" font-family="Roboto, sans-serif" font-size="18" text-anchor="middle">
                        <tspan font-weight="300" fill="#4A148C">NOVA</tspan><tspan font-weight="700" fill="#00897B">FUSE</tspan>
                    </text>
                </svg>
            </div>
            <div class="logo-description">
                <div class="logo-title">Concept 3: The Fusion Symbol</div>
                <p>This concept shows different shapes representing various systems and data types coming together and fusing at the center through the UAC. The diverse shapes symbolize the different domains NovaFuse integrates.</p>
                <p>The purple-to-teal gradient in the central fusion point represents the transformation power of NovaFuse, while the colored shapes represent different domains (security, compliance, IT, business).</p>
            </div>
        </div>
        
        <div class="logo-card logo-concept-4">
            <div class="logo-display">
                <svg class="logo-svg" width="250" height="150" viewBox="0 0 250 150" xmlns="http://www.w3.org/2000/svg">
                    <!-- The Nova Burst Concept -->
                    <defs>
                        <linearGradient id="nova-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#1A365D" />
                            <stop offset="100%" stop-color="#7B68EE" />
                        </linearGradient>
                        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                            <feGaussianBlur stdDeviation="2" result="blur" />
                            <feComposite in="SourceGraphic" in2="blur" operator="over" />
                        </filter>
                    </defs>
                    
                    <!-- Nova burst rays -->
                    <g filter="url(#glow)">
                        <!-- Long rays -->
                        <path d="M125,75 L125,30" stroke="url(#nova-gradient)" stroke-width="4" stroke-linecap="round" />
                        <path d="M125,75 L125,120" stroke="url(#nova-gradient)" stroke-width="4" stroke-linecap="round" />
                        <path d="M125,75 L80,75" stroke="url(#nova-gradient)" stroke-width="4" stroke-linecap="round" />
                        <path d="M125,75 L170,75" stroke="url(#nova-gradient)" stroke-width="4" stroke-linecap="round" />
                        
                        <!-- Medium rays -->
                        <path d="M125,75 L95,45" stroke="url(#nova-gradient)" stroke-width="3" stroke-linecap="round" />
                        <path d="M125,75 L155,45" stroke="url(#nova-gradient)" stroke-width="3" stroke-linecap="round" />
                        <path d="M125,75 L95,105" stroke="url(#nova-gradient)" stroke-width="3" stroke-linecap="round" />
                        <path d="M125,75 L155,105" stroke="url(#nova-gradient)" stroke-width="3" stroke-linecap="round" />
                        
                        <!-- Short rays -->
                        <path d="M125,75 L110,60" stroke="url(#nova-gradient)" stroke-width="2" stroke-linecap="round" />
                        <path d="M125,75 L140,60" stroke="url(#nova-gradient)" stroke-width="2" stroke-linecap="round" />
                        <path d="M125,75 L110,90" stroke="url(#nova-gradient)" stroke-width="2" stroke-linecap="round" />
                        <path d="M125,75 L140,90" stroke="url(#nova-gradient)" stroke-width="2" stroke-linecap="round" />
                    </g>
                    
                    <!-- Central core -->
                    <circle cx="125" cy="75" r="15" fill="url(#nova-gradient)" />
                    
                    <!-- Connection nodes -->
                    <circle cx="125" cy="30" r="5" fill="#7B68EE" />
                    <circle cx="125" cy="120" r="5" fill="#7B68EE" />
                    <circle cx="80" cy="75" r="5" fill="#7B68EE" />
                    <circle cx="170" cy="75" r="5" fill="#7B68EE" />
                    <circle cx="95" cy="45" r="4" fill="#7B68EE" />
                    <circle cx="155" cy="45" r="4" fill="#7B68EE" />
                    <circle cx="95" cy="105" r="4" fill="#7B68EE" />
                    <circle cx="155" cy="105" r="4" fill="#7B68EE" />
                    
                    <!-- Logo Text -->
                    <text x="125" y="160" font-family="Montserrat, sans-serif" font-size="18" text-anchor="middle">
                        <tspan font-weight="700" fill="#1A365D">NOVA</tspan><tspan font-weight="300" fill="#7B68EE">FUSE</tspan>
                    </text>
                </svg>
            </div>
            <div class="logo-description">
                <div class="logo-title">Concept 4: The Nova Burst</div>
                <p>This concept uses a starburst or nova explosion symbol to represent innovation and transformation, with connecting nodes to symbolize integration capabilities. The radial design conveys energy and expansion.</p>
                <p>The blue-to-purple gradient represents innovation and forward-thinking, while the glowing effect adds a dynamic, high-tech feel that stands out.</p>
            </div>
        </div>
    </div>
    
    <h2>Color Palettes</h2>
    
    <h3>Tech-Forward Palette</h3>
    <div class="color-palette">
        <div class="color-swatch" style="background-color: #1A365D;">#1A365D</div>
        <div class="color-swatch" style="background-color: #00A3A1;">#00A3A1</div>
        <div class="color-swatch" style="background-color: #7B68EE;">#7B68EE</div>
        <div class="color-swatch" style="background-color: #F8F9FA; color: #333;">#F8F9FA</div>
    </div>
    
    <h3>Trust & Innovation Palette</h3>
    <div class="color-palette">
        <div class="color-swatch" style="background-color: #0A2463;">#0A2463</div>
        <div class="color-swatch" style="background-color: #3E92CC;">#3E92CC</div>
        <div class="color-swatch" style="background-color: #FF5A5F;">#FF5A5F</div>
        <div class="color-swatch" style="background-color: #F8F8FF; color: #333;">#F8F8FF</div>
    </div>
    
    <h3>Modern Enterprise Palette</h3>
    <div class="color-palette">
        <div class="color-swatch" style="background-color: #4A148C;">#4A148C</div>
        <div class="color-swatch" style="background-color: #00897B;">#00897B</div>
        <div class="color-swatch" style="background-color: #FFC107;">#FFC107</div>
        <div class="color-swatch" style="background-color: #ECEFF1; color: #333;">#ECEFF1</div>
    </div>
    
    <h2>Typography Options</h2>
    
    <div class="typography-sample">
        <div class="font-example">
            <div class="font-name">Montserrat</div>
            <div class="font-sample" style="font-family: Montserrat, sans-serif;">
                <span style="font-weight: 700;">NOVA</span><span style="font-weight: 300;">FUSE</span>
            </div>
            <p>Clean, modern sans-serif with excellent readability and a geometric character.</p>
        </div>
        
        <div class="font-example">
            <div class="font-name">Poppins</div>
            <div class="font-sample" style="font-family: Poppins, sans-serif;">
                <span style="font-weight: 600;">NOVA</span><span style="font-weight: 300;">FUSE</span>
            </div>
            <p>Geometric sans-serif with a friendly, modern feel and balanced proportions.</p>
        </div>
        
        <div class="font-example">
            <div class="font-name">Roboto</div>
            <div class="font-sample" style="font-family: Roboto, sans-serif;">
                <span style="font-weight: 700;">NOVA</span><span style="font-weight: 300;">FUSE</span>
            </div>
            <p>Versatile sans-serif with a natural reading rhythm and professional appearance.</p>
        </div>
    </div>
    
    <div class="next-steps">
        <h2>Next Steps</h2>
        <p>These initial concepts provide a starting point for NovaFuse's visual identity. To refine these concepts:</p>
        <ul>
            <li>Select your preferred concept direction(s)</li>
            <li>Indicate your preferred color palette</li>
            <li>Choose a typography style that best represents NovaFuse</li>
            <li>Provide any additional feedback on specific elements you like or dislike</li>
        </ul>
        <p>Based on your feedback, we can refine these concepts and develop a final logo that perfectly captures NovaFuse's identity as a universal integration platform.</p>
    </div>
</body>
</html>
