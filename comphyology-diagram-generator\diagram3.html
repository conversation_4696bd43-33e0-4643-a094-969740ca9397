<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>3. Three-Body Problem Reframing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 900px;
            height: 700px;
            position: relative;
            border: 1px solid #eee;
            margin: 0 auto;
            background-color: white;
        }
        .element {
            position: absolute;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 2;
        }
        .element-number {
            position: absolute;
            top: 5px;
            left: 5px;
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
        }
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border-style: solid;
            z-index: 1;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>3. Three-Body Problem Reframing</h1>
    
    <div class="diagram-container">
        <!-- Three-Body Problem Reframing -->
        <div class="element" style="top: 50px; left: 300px; width: 300px; background-color: #fff0f6; font-weight: bold; font-size: 20px;">
            Three-Body Problem Reframing
            <div class="element-number">1</div>
        </div>
        
        <!-- Classical Physics Lens -->
        <div class="element" style="top: 120px; left: 150px; width: 250px; background-color: #e6f7ff; font-weight: bold; font-size: 16px;">
            Classical Physics Lens
            <div class="element-number">2</div>
        </div>
        
        <!-- Comphyological Lens -->
        <div class="element" style="top: 120px; left: 500px; width: 250px; background-color: #f6ffed; font-weight: bold; font-size: 16px;">
            Comphyological Lens
            <div class="element-number">3</div>
        </div>
        
        <!-- Classical Boundaries -->
        <div class="element" style="top: 180px; left: 150px; width: 250px; background-color: #e6f7ff;">
            System Boundaries:<br>Potentially infinite, open
            <div class="element-number">4</div>
        </div>
        
        <!-- Comphyological Boundaries -->
        <div class="element" style="top: 180px; left: 500px; width: 250px; background-color: #f6ffed;">
            System Boundaries:<br>Finite, closed, nested
            <div class="element-number">5</div>
        </div>
        
        <!-- Classical Predictability -->
        <div class="element" style="top: 240px; left: 150px; width: 250px; background-color: #e6f7ff;">
            Predictability:<br>Chaotic, sensitive to initial conditions
            <div class="element-number">6</div>
        </div>
        
        <!-- Comphyological Predictability -->
        <div class="element" style="top: 240px; left: 500px; width: 250px; background-color: #f6ffed;">
            Predictability:<br>Stable under nested constraints
            <div class="element-number">7</div>
        </div>
        
        <!-- Classical Math -->
        <div class="element" style="top: 300px; left: 150px; width: 250px; background-color: #e6f7ff;">
            Mathematical Approach:<br>Differential equations with diverging solutions
            <div class="element-number">8</div>
        </div>
        
        <!-- Comphyological Math -->
        <div class="element" style="top: 300px; left: 500px; width: 250px; background-color: #f6ffed;">
            Mathematical Approach:<br>Tensor fields with boundary conditions
            <div class="element-number">9</div>
        </div>
        
        <!-- Solution Equation -->
        <div class="element" style="top: 550px; left: 300px; width: 300px; background-color: #fffbe6; font-weight: bold; font-size: 16px;">
            Three-Body Solution = ∮(T⊗G)·dS
            <div class="element-number">10</div>
        </div>
        
        <!-- Implementation -->
        <div class="element" style="top: 620px; left: 300px; width: 300px; background-color: #f9f0ff; font-weight: bold; font-size: 16px;">
            Technical Implementation: Three-Body Problem Reframing
            <div class="element-number">11</div>
        </div>
        
        <!-- Connections -->
        <!-- Title to Classical -->
        <div class="connection" style="top: 100px; left: 375px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 120px; left: 265px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Title to Comphyological -->
        <div class="connection" style="top: 100px; left: 525px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 120px; left: 615px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Classical to Boundaries -->
        <div class="connection" style="top: 150px; left: 275px; width: 2px; height: 30px; background-color: black;"></div>
        
        <!-- Comphyological to Boundaries -->
        <div class="connection" style="top: 150px; left: 625px; width: 2px; height: 30px; background-color: black;"></div>
        
        <!-- Classical Boundaries to Predictability -->
        <div class="connection" style="top: 210px; left: 275px; width: 2px; height: 30px; background-color: black;"></div>
        
        <!-- Comphyological Boundaries to Predictability -->
        <div class="connection" style="top: 210px; left: 625px; width: 2px; height: 30px; background-color: black;"></div>
        
        <!-- Classical Predictability to Math -->
        <div class="connection" style="top: 270px; left: 275px; width: 2px; height: 30px; background-color: black;"></div>
        
        <!-- Comphyological Predictability to Math -->
        <div class="connection" style="top: 270px; left: 625px; width: 2px; height: 30px; background-color: black;"></div>
        
        <!-- Classical to Solution -->
        <div class="connection" style="top: 350px; left: 275px; width: 100px; height: 2px; background-color: black; transform: rotate(-45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 550px; left: 365px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Comphyological to Solution -->
        <div class="connection" style="top: 350px; left: 625px; width: 100px; height: 2px; background-color: black; transform: rotate(45deg); transform-origin: left center;"></div>
        <div class="arrow" style="top: 550px; left: 515px; border-width: 5px 0 5px 10px; border-color: transparent transparent transparent black;"></div>
        
        <!-- Solution to Implementation -->
        <div class="connection" style="top: 580px; left: 450px; width: 2px; height: 40px; background-color: black;"></div>
        <div class="arrow" style="top: 610px; left: 445px; border-width: 10px 5px 0 5px; border-color: black transparent transparent transparent;"></div>
    </div>
</body>
</html>
