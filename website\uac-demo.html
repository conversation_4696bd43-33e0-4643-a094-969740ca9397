<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal API Connector (UAC) Demo - NovaFuse</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="css/styles.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- Include Header and Navigation -->
    <div id="header-container"></div>

    <main class="container">
        <!-- Breadcrumbs -->
        <ul class="breadcrumbs">
            <li><a href="index.html">Home</a></li>
            <li><a href="uac-demo.html">UAC Demo</a></li>
        </ul>

        <!-- Hero Section -->
        <div class="hero">
            <h2>Universal API Connector (UAC) Demo</h2>
            <p>
                Experience the power of NovaFuse's patent-pending Universal API Connector technology.
                Connect, normalize, and control data across your entire ecosystem in real-time.
            </p>
        </div>

        <!-- Demo Overview -->
        <div class="section-header">
            <h2 class="text-2xl font-bold mb-4">The UAC Difference</h2>
            <p class="text-lg mb-0">See how our Universal API Connector transforms your compliance operations</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 my-8">
            <div class="card">
                <h3 class="card-title">What is the UAC?</h3>
                <p class="card-content">
                    The Universal API Connector (UAC) is the central nervous system for your digital operations. 
                    It connects, normalizes, and controls data across your entire ecosystem in real-time, 
                    enabling unprecedented compliance automation and cross-domain intelligence.
                </p>
                <ul class="mt-4 space-y-2 text-secondary-text">
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-accent-color mt-1 mr-2"></i>
                        <span>100ms Data Normalization across systems</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-accent-color mt-1 mr-2"></i>
                        <span>Bidirectional control for real-time compliance</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-accent-color mt-1 mr-2"></i>
                        <span>AI-powered mapping and transformation</span>
                    </li>
                    <li class="flex items-start">
                        <i class="fas fa-check-circle text-accent-color mt-1 mr-2"></i>
                        <span>Cross-domain intelligence across 59+ domains</span>
                    </li>
                </ul>
            </div>

            <div class="card">
                <h3 class="card-title">The Two-Part Demo Strategy</h3>
                <p class="card-content">
                    NovaFuse uses a powerful two-part demo strategy to showcase the UAC's capabilities:
                </p>
                <div class="mt-4 space-y-4">
                    <div class="p-4 bg-secondary-bg rounded-lg border-l-4 border-accent-color">
                        <h4 class="font-bold mb-2">UAC Demo (Day 0)</h4>
                        <p class="text-secondary-text">
                            Shows universal integration and baseline compliance logic as proof of concept.
                            This is what you'll experience in this interactive demo.
                        </p>
                    </div>
                    <div class="p-4 bg-secondary-bg rounded-lg border-l-4 border-accent-color">
                        <h4 class="font-bold mb-2">48-Hour Demo (Days 1-2)</h4>
                        <p class="text-secondary-text">
                            Customizes the solution for your specific systems, frameworks, and workflows.
                            Request this personalized demo after exploring the UAC Demo.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Demo Section -->
        <div class="my-12">
            <h2 class="text-2xl font-bold mb-6">Interactive UAC Demo</h2>
            
            <div class="bg-secondary-bg p-6 rounded-lg">
                <div class="flex flex-col md:flex-row gap-8">
                    <div class="w-full md:w-1/3">
                        <h3 class="text-xl font-bold mb-4">Demo Scenarios</h3>
                        <div class="space-y-4">
                            <button class="demo-scenario-btn w-full text-left p-3 rounded-lg bg-primary-bg hover:bg-accent-color hover:bg-opacity-20 transition-colors active">
                                <i class="fas fa-shield-alt mr-2"></i> HIPAA Compliance
                            </button>
                            <button class="demo-scenario-btn w-full text-left p-3 rounded-lg bg-primary-bg hover:bg-accent-color hover:bg-opacity-20 transition-colors">
                                <i class="fas fa-user-shield mr-2"></i> GDPR Compliance
                            </button>
                            <button class="demo-scenario-btn w-full text-left p-3 rounded-lg bg-primary-bg hover:bg-accent-color hover:bg-opacity-20 transition-colors">
                                <i class="fas fa-credit-card mr-2"></i> PCI DSS Compliance
                            </button>
                            <button class="demo-scenario-btn w-full text-left p-3 rounded-lg bg-primary-bg hover:bg-accent-color hover:bg-opacity-20 transition-colors">
                                <i class="fas fa-exchange-alt mr-2"></i> Multi-API Demo
                            </button>
                        </div>
                        
                        <div class="mt-8">
                            <h3 class="text-xl font-bold mb-4">Access the Full Demo</h3>
                            <p class="text-secondary-text mb-4">
                                The full interactive UAC demo is available for partners and qualified prospects.
                            </p>
                            <div class="space-y-4">
                                <a href="http://localhost:3030" target="_blank" rel="noopener noreferrer" class="btn btn-primary w-full flex justify-center items-center">
                                    <i class="fas fa-external-link-alt mr-2"></i> Launch Full Demo
                                </a>
                                <a href="partner-onboarding.html" class="btn btn-outline w-full flex justify-center items-center">
                                    <i class="fas fa-user-plus mr-2"></i> Request Partner Access
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="w-full md:w-2/3">
                        <div class="demo-content p-4 bg-primary-bg rounded-lg h-full">
                            <!-- HIPAA Compliance Demo Content (Default) -->
                            <div class="demo-scenario active" id="hipaa-demo">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-xl font-bold">HIPAA Compliance Check</h3>
                                    <span class="px-3 py-1 bg-accent-color bg-opacity-20 text-accent-color rounded-full text-sm">Healthcare</span>
                                </div>
                                
                                <p class="mb-4">
                                    The UAC automatically checks for Protected Health Information (PHI) and ensures HIPAA compliance
                                    by verifying encryption, authorization, and proper data handling.
                                </p>
                                
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div class="p-3 bg-secondary-bg rounded-lg">
                                        <h4 class="font-bold mb-2 text-sm uppercase">Input Data</h4>
                                        <pre class="text-xs overflow-auto p-2 bg-primary-bg rounded max-h-60">
{
  "patientId": "12345",
  "name": "John Doe",
  "dob": "1980-01-01",
  "medicalRecordNumber": "MRN-67890",
  "diagnosis": "Hypertension",
  "medications": [
    "Lisinopril 10mg",
    "Aspirin 81mg"
  ],
  "ssn": "***********"
}
                                        </pre>
                                    </div>
                                    
                                    <div class="p-3 bg-secondary-bg rounded-lg">
                                        <h4 class="font-bold mb-2 text-sm uppercase">Compliance Result</h4>
                                        <div class="p-3 bg-error-color bg-opacity-20 border border-error-color rounded-lg mb-3">
                                            <div class="flex items-center">
                                                <i class="fas fa-exclamation-triangle text-error-color mr-2"></i>
                                                <span class="font-bold text-error-color">Non-Compliant</span>
                                            </div>
                                        </div>
                                        <ul class="space-y-2 text-sm">
                                            <li class="flex items-start">
                                                <i class="fas fa-times-circle text-error-color mt-1 mr-2"></i>
                                                <span>Data contains PHI identifiers (name, DOB, SSN)</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-times-circle text-error-color mt-1 mr-2"></i>
                                                <span>No encryption indicators found</span>
                                            </li>
                                            <li class="flex items-start">
                                                <i class="fas fa-times-circle text-error-color mt-1 mr-2"></i>
                                                <span>No patient authorization found</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="p-4 bg-secondary-bg rounded-lg">
                                    <h4 class="font-bold mb-2">UAC Remediation</h4>
                                    <p class="text-secondary-text mb-3">
                                        The UAC can automatically remediate these issues by:
                                    </p>
                                    <ul class="space-y-2 text-sm">
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-success-color mt-1 mr-2"></i>
                                            <span>Encrypting sensitive data fields</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-success-color mt-1 mr-2"></i>
                                            <span>Verifying authorization before data transmission</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-success-color mt-1 mr-2"></i>
                                            <span>Masking or anonymizing PHI when appropriate</span>
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-success-color mt-1 mr-2"></i>
                                            <span>Logging all access and modifications for audit purposes</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- Other demo scenarios would be added here and shown/hidden with JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="global-vision p-8 rounded-lg my-12 text-center">
            <h2 class="text-2xl font-bold mb-4">Ready to Experience the Full Power of the UAC?</h2>
            <p class="mb-6 max-w-3xl mx-auto">
                Request a personalized 48-hour demo where we'll customize the UAC for your specific systems, frameworks, and workflows.
                See firsthand how NovaFuse can transform your compliance operations.
            </p>
            <div class="flex flex-wrap justify-center gap-4">
                <a href="partner-onboarding.html" class="btn btn-primary">Request 48-Hour Demo</a>
                <a href="uac-features.html" class="btn btn-outline border-white text-white hover:bg-blue-900 hover:bg-opacity-20">Explore UAC Features</a>
            </div>
        </div>
    </main>

    <!-- Include Footer -->
    <div id="footer-container"></div>

    <script src="js/main.js"></script>
    <script>
        // Load header and footer components
        document.addEventListener('DOMContentLoaded', function() {
            // Load header
            fetch('components/header.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('header-container').innerHTML = data;
                    // Set active nav item
                    document.getElementById('nav-solutions').classList.add('active');
                });

            // Load footer
            fetch('components/footer.html')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('footer-container').innerHTML = data;
                });
                
            // Demo scenario buttons
            const scenarioButtons = document.querySelectorAll('.demo-scenario-btn');
            scenarioButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    scenarioButtons.forEach(btn => btn.classList.remove('active'));
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // In a full implementation, this would show the corresponding demo content
                    // For now, we're just showing the HIPAA demo
                });
            });
        });
    </script>
</body>
</html>
