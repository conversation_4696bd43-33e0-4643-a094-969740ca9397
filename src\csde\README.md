# CSDE (Cyber-Safety Dominance Equation) Engine

The CSDE Engine is the core component of the NovaFuse Cyber-Safety platform, implementing the Cyber-Safety Dominance Equation:

**CSDE = (N ⊗ G ⊕ C) × π10³**

Where:
- N = NIST Multiplier (10) - representing 90% reduction in compliance gaps
- G = GCP Multiplier (10) - representing 90% reduction in processing latency
- C = Cyber-Safety Multiplier (31.42) - representing 97% faster threat response
- ⊗ = Tensor product operator - enabling multi-dimensional integration
- ⊕ = Fusion operator - creating non-linear synergy between components
- π10³ = Circular trust topology factor - derived from the Wilson loop circumference

## Features

- **Tensor Operations**: Multi-dimensional integration of compliance frameworks
- **Fusion Operator**: Non-linear synergy between components
- **Circular Trust Topology**: Zero-trust architecture forming a closed loop
- **NovaFlowX Engine**: φ-optimized compliance compilation
- **Remediation Actions**: Automated generation of remediation actions
- **GCP Integration**: Seamless integration with Google Cloud Platform
- **Performance Metrics**: Real-time performance monitoring
- **Dashboard UI**: Visual representation of CSDE calculations

## Installation

```bash
# Clone the repository
git clone https://github.com/novafuse/csde-engine.git
cd csde-engine

# Install dependencies
npm install
```

## Usage

### Running the CSDE Engine

```bash
# Run the CSDE API server
npm start

# Run the CSDE UI server
npm run start:ui

# Run the GCP integration test
npm run gcp-test

# Run the test script
npm test
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Stop Docker Compose
docker-compose down
```

## API Endpoints

### CSDE API (Port 3010)

- `GET /`: API information
- `POST /calculate`: Calculate CSDE value
- `GET /metrics`: Get CSDE engine performance metrics
- `POST /clear-cache`: Clear CSDE engine cache

### CSDE UI (Port 3011)

- `GET /`: CSDE Dashboard
- `POST /api/calculate`: Calculate CSDE value
- `GET /api/metrics`: Get CSDE engine performance metrics

## Components

### Core Components

- **CSDEEngine**: Main engine that calculates the CSDE value
- **TensorOperator**: Implements the tensor product operator (⊗)
- **FusionOperator**: Implements the fusion operator (⊕)
- **CircularTrustTopology**: Implements the circular trust topology factor (π10³)
- **NovaFlowXEngine**: Generates remediation actions

### UI Components

- **CSDE Dashboard**: Visual representation of CSDE calculations
- **Tensor Visualization**: Visual representation of tensor operations
- **Remediation Actions**: Display of remediation actions

## Performance

The CSDE Engine achieves a 3,142× performance improvement over traditional compliance and security approaches, enabling:

- Real-time compliance verification across multiple regulatory frameworks
- Automated remediation of security and compliance issues
- Predictive analytics for compliance and security risks

## GCP Integration

The CSDE Engine integrates with Google Cloud Platform (GCP) to provide:

- Security Command Center integration for security findings
- IAM integration for identity and access management
- BigQuery integration for data analysis

### Configuring GCP Permissions

To configure GCP permissions for the CSDE Engine, run the following script:

```bash
# Configure GCP permissions
./scripts/configure-gcp-permissions.ps1 -ProjectId <your-project-id>
```

### Running GCP Integration Tests

```bash
# Run GCP integration test with simulation environment
npm run gcp-test

# Run GCP integration test with live GCP environment
node gcp_integration_test.js --live --project=<your-project-id>
```

## NovaFlowX Remediation

The CSDE Engine includes NovaFlowX remediation capabilities to automatically remediate compliance and security issues:

```bash
# Remediate a specific control
node scripts/novaflowx-remediate.js --control=AC-2 --automation=high

# Remediate all high-priority issues
node scripts/novaflowx-remediate.js --all --priority=high

# Dry run to see what would be remediated
node scripts/novaflowx-remediate.js --dry-run --control=CM-7
```

## Performance Testing

The CSDE Engine includes performance testing capabilities to validate the 3,142× performance improvement:

```bash
# Run performance test
node scripts/performance-test.js
```

## ML Testing

The CSDE Engine includes ML testing capabilities to validate the ML components:

```bash
# Run ML test
node ml/ml_test.js

# Visualize ML test results
node ml/visualize.js
```

## License

Proprietary - NovaFuse
