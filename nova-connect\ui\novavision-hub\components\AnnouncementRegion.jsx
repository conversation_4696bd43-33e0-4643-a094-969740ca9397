/**
 * AnnouncementRegion Component
 * 
 * A component that announces changes to screen readers using ARIA live regions.
 */

import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';

/**
 * AnnouncementRegion component
 * 
 * @param {Object} props - Component props
 * @param {string} props.message - Message to announce
 * @param {string} [props.politeness='polite'] - ARIA live politeness setting (polite, assertive, off)
 * @param {boolean} [props.clearAfter=5000] - Time in milliseconds to clear the message after (0 to disable)
 * @param {boolean} [props.atomic=true] - Whether the live region should be considered as a whole
 * @param {string} [props.relevant='additions text'] - What changes are relevant (additions, removals, text, all)
 * @param {string} [props.className] - Additional CSS class names
 * @returns {React.ReactElement} AnnouncementRegion component
 */
const AnnouncementRegion = ({
  message,
  politeness = 'polite',
  clearAfter = 5000,
  atomic = true,
  relevant = 'additions text',
  className = ''
}) => {
  const [announcement, setAnnouncement] = useState('');
  
  // Update announcement when message changes
  useEffect(() => {
    if (message) {
      // Set the announcement
      setAnnouncement(message);
      
      // Clear the announcement after the specified time
      if (clearAfter > 0) {
        const timeoutId = setTimeout(() => {
          setAnnouncement('');
        }, clearAfter);
        
        return () => {
          clearTimeout(timeoutId);
        };
      }
    }
  }, [message, clearAfter]);
  
  return (
    <div
      className={`sr-only ${className}`}
      aria-live={politeness}
      aria-atomic={atomic.toString()}
      aria-relevant={relevant}
      data-testid="announcement-region"
    >
      {announcement}
    </div>
  );
};

AnnouncementRegion.propTypes = {
  message: PropTypes.string,
  politeness: PropTypes.oneOf(['polite', 'assertive', 'off']),
  clearAfter: PropTypes.number,
  atomic: PropTypes.bool,
  relevant: PropTypes.string,
  className: PropTypes.string
};

export default AnnouncementRegion;
