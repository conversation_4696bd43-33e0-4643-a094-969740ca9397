/**
 * Zapier Controller Tests
 */

const ZapierController = require('../../../api/controllers/ZapierController');
const ZapierService = require('../../../api/services/ZapierService');

// Mock the ZapierService
jest.mock('../../../api/services/ZapierService');

describe('ZapierController', () => {
  let req, res, next;
  
  beforeEach(() => {
    // Mock request, response, and next
    req = {
      params: {},
      query: {},
      body: {}
    };
    
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
      redirect: jest.fn(),
      end: jest.fn()
    };
    
    next = jest.fn();
    
    // Mock ZapierService methods
    ZapierService.mockImplementation(() => ({
      getAppDefinition: jest.fn().mockReturnValue({
        title: 'NovaConnect UAC',
        description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.'
      }),
      getTriggers: jest.fn().mockResolvedValue([
        {
          key: 'new_connector',
          noun: 'Connector',
          display: {
            label: 'New Connector',
            description: 'Triggers when a new connector is created.'
          }
        }
      ]),
      getActions: jest.fn().mockResolvedValue([
        {
          key: 'create_connector',
          noun: 'Connector',
          display: {
            label: 'Create Connector',
            description: 'Creates a new connector.'
          }
        }
      ]),
      clientId: 'nova-connect-zapier',
      clientSecret: 'test-secret',
      redirectUri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      generateAccessToken: jest.fn().mockResolvedValue({
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        token_type: 'Bearer',
        expires_in: 2592000,
        scope: 'read write'
      }),
      refreshAccessToken: jest.fn().mockResolvedValue({
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        token_type: 'Bearer',
        expires_in: 2592000,
        scope: 'read write'
      }),
      registerApp: jest.fn().mockResolvedValue({
        id: 'app-123',
        name: 'Test App',
        description: 'Test Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z'
      }),
      getAllApps: jest.fn().mockResolvedValue([
        {
          id: 'app-123',
          name: 'Test App',
          description: 'Test Description',
          webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
          createdAt: '2023-01-01T00:00:00Z'
        }
      ]),
      getAppById: jest.fn().mockResolvedValue({
        id: 'app-123',
        name: 'Test App',
        description: 'Test Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z'
      }),
      updateApp: jest.fn().mockResolvedValue({
        id: 'app-123',
        name: 'Updated App',
        description: 'Updated Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      }),
      deleteApp: jest.fn().mockResolvedValue(true),
      registerTrigger: jest.fn().mockResolvedValue({
        id: 'trigger-123',
        key: 'test_trigger',
        noun: 'Test',
        display: {
          label: 'Test Trigger',
          description: 'Test trigger description'
        },
        createdAt: '2023-01-01T00:00:00Z'
      }),
      registerAction: jest.fn().mockResolvedValue({
        id: 'action-123',
        key: 'test_action',
        noun: 'Test',
        display: {
          label: 'Test Action',
          description: 'Test action description'
        },
        createdAt: '2023-01-01T00:00:00Z'
      })
    }));
  });
  
  afterEach(() => {
    jest.clearAllMocks();
  });
  
  test('getAppDefinition should return app definition', async () => {
    await ZapierController.getAppDefinition(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      title: 'NovaConnect UAC',
      description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.'
    });
  });
  
  test('getTriggers should return triggers', async () => {
    await ZapierController.getTriggers(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([
      {
        key: 'new_connector',
        noun: 'Connector',
        display: {
          label: 'New Connector',
          description: 'Triggers when a new connector is created.'
        }
      }
    ]);
  });
  
  test('getActions should return actions', async () => {
    await ZapierController.getActions(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([
      {
        key: 'create_connector',
        noun: 'Connector',
        display: {
          label: 'Create Connector',
          description: 'Creates a new connector.'
        }
      }
    ]);
  });
  
  test('authorizeOAuth should redirect with code', async () => {
    req.query = {
      client_id: 'nova-connect-zapier',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      state: 'test-state',
      response_type: 'code'
    };
    
    await ZapierController.authorizeOAuth(req, res, next);
    
    expect(res.redirect).toHaveBeenCalled();
    const redirectUrl = res.redirect.mock.calls[0][0];
    expect(redirectUrl).toContain('https://zapier.com/dashboard/auth/oauth/return/App-ID/');
    expect(redirectUrl).toContain('code=');
    expect(redirectUrl).toContain('state=test-state');
  });
  
  test('authorizeOAuth should validate client ID', async () => {
    req.query = {
      client_id: 'invalid-client',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      state: 'test-state',
      response_type: 'code'
    };
    
    await ZapierController.authorizeOAuth(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'invalid_client',
      error_description: 'Invalid client ID'
    });
  });
  
  test('authorizeOAuth should validate response type', async () => {
    req.query = {
      client_id: 'nova-connect-zapier',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      state: 'test-state',
      response_type: 'token'
    };
    
    await ZapierController.authorizeOAuth(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'unsupported_response_type',
      error_description: 'Only code response type is supported'
    });
  });
  
  test('getOAuthToken should generate access token', async () => {
    req.body = {
      grant_type: 'authorization_code',
      code: 'test-code',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      client_id: 'nova-connect-zapier',
      client_secret: 'test-secret'
    };
    
    await ZapierController.getOAuthToken(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      access_token: 'test-access-token',
      refresh_token: 'test-refresh-token',
      token_type: 'Bearer',
      expires_in: 2592000,
      scope: 'read write'
    });
  });
  
  test('getOAuthToken should refresh access token', async () => {
    req.body = {
      grant_type: 'refresh_token',
      refresh_token: 'test-refresh-token',
      client_id: 'nova-connect-zapier',
      client_secret: 'test-secret'
    };
    
    await ZapierController.getOAuthToken(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token',
      token_type: 'Bearer',
      expires_in: 2592000,
      scope: 'read write'
    });
  });
  
  test('getOAuthToken should validate client credentials', async () => {
    req.body = {
      grant_type: 'authorization_code',
      code: 'test-code',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      client_id: 'invalid-client',
      client_secret: 'invalid-secret'
    };
    
    await ZapierController.getOAuthToken(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      error: 'invalid_client',
      error_description: 'Invalid client credentials'
    });
  });
  
  test('getOAuthToken should validate grant type', async () => {
    req.body = {
      grant_type: 'invalid-grant',
      client_id: 'nova-connect-zapier',
      client_secret: 'test-secret'
    };
    
    await ZapierController.getOAuthToken(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'unsupported_grant_type',
      error_description: 'Unsupported grant type'
    });
  });
  
  test('beforeApp should execute successfully', async () => {
    await ZapierController.beforeApp(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      status: 'success',
      message: 'Before app hook executed successfully'
    });
  });
  
  test('afterApp should execute successfully', async () => {
    await ZapierController.afterApp(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      status: 'success',
      message: 'After app hook executed successfully'
    });
  });
  
  test('newConnectorTrigger should return connectors', async () => {
    await ZapierController.newConnectorTrigger(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({
        id: expect.any(String),
        name: expect.any(String),
        type: expect.any(String),
        createdAt: expect.any(String)
      })
    ]));
  });
  
  test('newWorkflowTrigger should return workflows', async () => {
    await ZapierController.newWorkflowTrigger(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({
        id: expect.any(String),
        name: expect.any(String),
        status: expect.any(String),
        createdAt: expect.any(String)
      })
    ]));
  });
  
  test('complianceEventTrigger should return events', async () => {
    await ZapierController.complianceEventTrigger(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({
        id: expect.any(String),
        type: expect.any(String),
        severity: expect.any(String),
        resource: expect.any(String),
        details: expect.any(String),
        timestamp: expect.any(String)
      })
    ]));
  });
  
  test('createConnectorAction should create connector', async () => {
    req.body = {
      name: 'Test Connector',
      type: 'api',
      config: '{"baseUrl": "https://api.example.com"}'
    };
    
    await ZapierController.createConnectorAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      name: 'Test Connector',
      type: 'api',
      config: { baseUrl: 'https://api.example.com' },
      createdAt: expect.any(String)
    }));
  });
  
  test('createConnectorAction should validate required fields', async () => {
    req.body = {
      name: 'Test Connector'
      // Missing type and config
    };
    
    await ZapierController.createConnectorAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Name, type, and config are required'
    });
  });
  
  test('executeWorkflowAction should execute workflow', async () => {
    req.body = {
      workflowId: 'wf-123',
      inputs: '{"param1": "value1"}'
    };
    
    await ZapierController.executeWorkflowAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      workflowId: 'wf-123',
      status: 'completed',
      result: expect.objectContaining({
        success: true,
        data: { param1: 'value1' }
      }),
      startedAt: expect.any(String),
      completedAt: expect.any(String)
    }));
  });
  
  test('executeWorkflowAction should validate required fields', async () => {
    req.body = {
      // Missing workflowId
      inputs: '{"param1": "value1"}'
    };
    
    await ZapierController.executeWorkflowAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Workflow ID is required'
    });
  });
  
  test('createComplianceEvidenceAction should create evidence', async () => {
    req.body = {
      controlId: 'ctrl-123',
      evidenceType: 'document',
      description: 'Test evidence',
      data: '{"source": "zapier"}'
    };
    
    await ZapierController.createComplianceEvidenceAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      controlId: 'ctrl-123',
      evidenceType: 'document',
      description: 'Test evidence',
      data: { source: 'zapier' },
      createdAt: expect.any(String)
    }));
  });
  
  test('createComplianceEvidenceAction should validate required fields', async () => {
    req.body = {
      controlId: 'ctrl-123',
      // Missing evidenceType and description
      data: '{"source": "zapier"}'
    };
    
    await ZapierController.createComplianceEvidenceAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Control ID, evidence type, and description are required'
    });
  });
  
  test('registerApp should register app', async () => {
    req.body = {
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/'
    };
    
    await ZapierController.registerApp(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'app-123',
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  
  test('registerApp should validate required fields', async () => {
    req.body = {
      name: 'Test App'
      // Missing webhookUrl
    };
    
    await ZapierController.registerApp(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Name and webhook URL are required'
    });
  });
  
  test('getAllApps should return apps', async () => {
    await ZapierController.getAllApps(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([
      {
        id: 'app-123',
        name: 'Test App',
        description: 'Test Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z'
      }
    ]);
  });
  
  test('getAppById should return app', async () => {
    req.params = {
      id: 'app-123'
    };
    
    await ZapierController.getAppById(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      id: 'app-123',
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  
  test('updateApp should update app', async () => {
    req.params = {
      id: 'app-123'
    };
    
    req.body = {
      name: 'Updated App',
      description: 'Updated Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/'
    };
    
    await ZapierController.updateApp(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      id: 'app-123',
      name: 'Updated App',
      description: 'Updated Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-02T00:00:00Z'
    });
  });
  
  test('deleteApp should delete app', async () => {
    req.params = {
      id: 'app-123'
    };
    
    await ZapierController.deleteApp(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(204);
    expect(res.end).toHaveBeenCalled();
  });
  
  test('registerTrigger should register trigger', async () => {
    req.body = {
      key: 'test_trigger',
      noun: 'Test',
      display: {
        label: 'Test Trigger',
        description: 'Test trigger description'
      },
      operation: {
        type: 'polling',
        perform: {
          url: 'https://api.example.com/triggers/test'
        }
      }
    };
    
    await ZapierController.registerTrigger(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'trigger-123',
      key: 'test_trigger',
      noun: 'Test',
      display: {
        label: 'Test Trigger',
        description: 'Test trigger description'
      },
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  
  test('registerTrigger should validate required fields', async () => {
    req.body = {
      key: 'test_trigger',
      noun: 'Test'
      // Missing display and operation
    };
    
    await ZapierController.registerTrigger(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Key, noun, display, and operation are required'
    });
  });
  
  test('registerAction should register action', async () => {
    req.body = {
      key: 'test_action',
      noun: 'Test',
      display: {
        label: 'Test Action',
        description: 'Test action description'
      },
      operation: {
        type: 'perform',
        perform: {
          url: 'https://api.example.com/actions/test',
          method: 'POST'
        }
      }
    };
    
    await ZapierController.registerAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'action-123',
      key: 'test_action',
      noun: 'Test',
      display: {
        label: 'Test Action',
        description: 'Test action description'
      },
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  
  test('registerAction should validate required fields', async () => {
    req.body = {
      key: 'test_action',
      noun: 'Test'
      // Missing display and operation
    };
    
    await ZapierController.registerAction(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Key, noun, display, and operation are required'
    });
  });
});
