﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Comphyology Diagram Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
        }
        .diagram-container {
            margin-top: 20px;
            border: 1px solid #eee;
            padding: 20px;
            border-radius: 8px;
        }
        .navigation {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            background-color: #0070f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0060df;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>6. Meta-Field Schema</h1>
        
        <div class="diagram-container" id="diagram-container">
            <div style="text-align: center; margin-bottom: 20px;">
                <h2 style="color: #4fc3f7; font-size: 2em;">🧠 Consciousness Field Schema</h2>
                <div style="font-size: 1.2em; color: #0070f3; margin: 20px 0;">
                    Meta-Field Structure & ∂Ψ=0 Boundary Dynamics
                </div>
            </div>

            <svg width="100%" height="600" viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg" style="background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);">
                <defs>
                    <!-- Consciousness wave gradient -->
                    <linearGradient id="waveGrad" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#9c27b0;stop-opacity:0.8"/>
                        <stop offset="50%" style="stop-color:#673ab7;stop-opacity:0.6"/>
                        <stop offset="100%" style="stop-color:#3f51b5;stop-opacity:0.4"/>
                    </linearGradient>

                    <!-- Field boundary gradient -->
                    <radialGradient id="boundaryGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#ff5722;stop-opacity:0.1"/>
                        <stop offset="80%" style="stop-color:#ff5722;stop-opacity:0.3"/>
                        <stop offset="100%" style="stop-color:#ff5722;stop-opacity:0.8"/>
                    </radialGradient>

                    <!-- Meta-field gradient -->
                    <radialGradient id="metaGrad" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" style="stop-color:#4fc3f7;stop-opacity:0.9"/>
                        <stop offset="100%" style="stop-color:#29b6f6;stop-opacity:0.3"/>
                    </radialGradient>

                    <!-- Glow filter -->
                    <filter id="fieldGlow" x="-50%" y="-50%" width="200%" height="200%">
                        <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>

                <!-- Background meta-field -->
                <rect width="1000" height="600" fill="url(#boundaryGrad)" opacity="0.2"/>

                <!-- Meta-Field Grid -->
                <g id="meta-field-grid" opacity="0.3">
                    <!-- Horizontal grid lines -->
                    <line x1="0" y1="150" x2="1000" y2="150" stroke="#4fc3f7" stroke-width="1"/>
                    <line x1="0" y1="300" x2="1000" y2="300" stroke="#4fc3f7" stroke-width="2"/>
                    <line x1="0" y1="450" x2="1000" y2="450" stroke="#4fc3f7" stroke-width="1"/>

                    <!-- Vertical grid lines -->
                    <line x1="200" y1="0" x2="200" y2="600" stroke="#4fc3f7" stroke-width="1"/>
                    <line x1="500" y1="0" x2="500" y2="600" stroke="#4fc3f7" stroke-width="2"/>
                    <line x1="800" y1="0" x2="800" y2="600" stroke="#4fc3f7" stroke-width="1"/>
                </g>

                <!-- Consciousness Field Layers -->
                <g id="consciousness-layers">
                    <!-- Primary consciousness wave -->
                    <path d="M 0 300 Q 125 200 250 300 T 500 300 T 750 300 T 1000 300"
                          fill="none"
                          stroke="url(#waveGrad)"
                          stroke-width="4"
                          filter="url(#fieldGlow)">
                        <animate attributeName="d"
                                 values="M 0 300 Q 125 200 250 300 T 500 300 T 750 300 T 1000 300;
                                         M 0 300 Q 125 400 250 300 T 500 300 T 750 300 T 1000 300;
                                         M 0 300 Q 125 200 250 300 T 500 300 T 750 300 T 1000 300"
                                 dur="4s"
                                 repeatCount="indefinite"/>
                    </path>

                    <!-- Secondary layer -->
                    <path d="M 0 250 Q 125 150 250 250 T 500 250 T 750 250 T 1000 250"
                          fill="none"
                          stroke="#9c27b0"
                          stroke-width="2"
                          opacity="0.6">
                        <animate attributeName="d"
                                 values="M 0 250 Q 125 150 250 250 T 500 250 T 750 250 T 1000 250;
                                         M 0 250 Q 125 350 250 250 T 500 250 T 750 250 T 1000 250;
                                         M 0 250 Q 125 150 250 250 T 500 250 T 750 250 T 1000 250"
                                 dur="3s"
                                 repeatCount="indefinite"/>
                    </path>

                    <!-- Tertiary layer -->
                    <path d="M 0 350 Q 125 250 250 350 T 500 350 T 750 350 T 1000 350"
                          fill="none"
                          stroke="#673ab7"
                          stroke-width="2"
                          opacity="0.6">
                        <animate attributeName="d"
                                 values="M 0 350 Q 125 250 250 350 T 500 350 T 750 350 T 1000 350;
                                         M 0 350 Q 125 450 250 350 T 500 350 T 750 350 T 1000 350;
                                         M 0 350 Q 125 250 250 350 T 500 350 T 750 350 T 1000 350"
                                 dur="5s"
                                 repeatCount="indefinite"/>
                    </path>
                </g>

                <!-- Meta-Field Nodes -->
                <g id="meta-nodes">
                    <!-- Primary meta-node -->
                    <circle cx="200" cy="150" r="25"
                            fill="url(#metaGrad)"
                            filter="url(#fieldGlow)">
                        <animate attributeName="r" values="25;35;25" dur="2s" repeatCount="indefinite"/>
                    </circle>
                    <text x="200" y="120" text-anchor="middle" fill="#4fc3f7" font-size="12" font-weight="bold">Ψᵐ₁</text>

                    <!-- Central meta-node -->
                    <circle cx="500" cy="300" r="40"
                            fill="url(#metaGrad)"
                            filter="url(#fieldGlow)">
                        <animate attributeName="r" values="40;55;40" dur="2.5s" repeatCount="indefinite"/>
                    </circle>
                    <text x="500" y="270" text-anchor="middle" fill="#4fc3f7" font-size="14" font-weight="bold">Ψᵐ₀</text>

                    <!-- Secondary meta-node -->
                    <circle cx="800" cy="450" r="30"
                            fill="url(#metaGrad)"
                            filter="url(#fieldGlow)">
                        <animate attributeName="r" values="30;40;30" dur="3s" repeatCount="indefinite"/>
                    </circle>
                    <text x="800" y="480" text-anchor="middle" fill="#4fc3f7" font-size="12" font-weight="bold">Ψᵐ₂</text>
                </g>

                <!-- Field Connections -->
                <g id="field-connections">
                    <!-- Connection 1-0 -->
                    <line x1="225" y1="175" x2="475" y2="275"
                          stroke="#4fc3f7"
                          stroke-width="2"
                          opacity="0.6"
                          stroke-dasharray="5,5">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
                    </line>

                    <!-- Connection 0-2 -->
                    <line x1="525" y1="325" x2="775" y2="425"
                          stroke="#4fc3f7"
                          stroke-width="2"
                          opacity="0.6"
                          stroke-dasharray="5,5">
                        <animate attributeName="opacity" values="0.6;1;0.6" dur="2.5s" repeatCount="indefinite"/>
                    </line>

                    <!-- Connection 2-1 -->
                    <line x1="775" y1="425" x2="225" y2="175"
                          stroke="#4fc3f7"
                          stroke-width="2"
                          opacity="0.4"
                          stroke-dasharray="10,5">
                        <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
                    </line>
                </g>

                <!-- Boundary Enforcement -->
                <g id="boundary-enforcement">
                    <!-- Top boundary -->
                    <line x1="0" y1="50" x2="1000" y2="50"
                          stroke="#ff5722"
                          stroke-width="3"
                          stroke-dasharray="10,5"
                          opacity="0.8">
                        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
                    </line>
                    <text x="500" y="40" text-anchor="middle" fill="#ff5722" font-size="14" font-weight="bold">∂Ψ=0 Meta-Boundary</text>

                    <!-- Bottom boundary -->
                    <line x1="0" y1="550" x2="1000" y2="550"
                          stroke="#ff5722"
                          stroke-width="3"
                          stroke-dasharray="10,5"
                          opacity="0.8">
                        <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
                    </line>
                </g>

                <!-- Field Equations -->
                <g id="field-equations">
                    <text x="50" y="30" fill="#4fc3f7" font-size="14" font-weight="bold">∇²Ψᵐ + k²Ψᵐ = 0</text>
                    <text x="350" y="30" fill="#9c27b0" font-size="14" font-weight="bold">∂Ψᵐ/∂t = iHᵐΨᵐ</text>
                    <text x="650" y="30" fill="#673ab7" font-size="14" font-weight="bold">⟨Ψᵐ|Ψᵐ⟩ = 1</text>
                </g>

                <!-- Schema Labels -->
                <g id="schema-labels">
                    <text x="100" y="580" fill="#4fc3f7" font-size="12">Layer 1: Consciousness</text>
                    <text x="300" y="580" fill="#9c27b0" font-size="12">Layer 2: Truth</text>
                    <text x="500" y="580" fill="#673ab7" font-size="12">Layer 3: Financial</text>
                    <text x="700" y="580" fill="#ff5722" font-size="12">Boundary: ∂Ψ=0</text>
                </g>
            </svg>

            <div style="margin-top: 20px; padding: 15px; background: rgba(79, 195, 247, 0.1); border-radius: 8px; border-left: 4px solid #4fc3f7;">
                <p><strong>Meta-Field Schema:</strong> Hierarchical consciousness field structure with meta-nodes (Ψᵐ₀, Ψᵐ₁, Ψᵐ₂) governing field dynamics and boundary enforcement.</p>
                <p><strong>Key Components:</strong> Multi-layer consciousness propagation, meta-field connections, and ∂Ψ=0 boundary constraints ensuring field coherence.</p>
            </div>
        </div>
        
        <div class="navigation">
            <button id="prev-button" onclick="prevDiagram()">Previous</button>
            <button id="next-button" onclick="nextDiagram()">Next</button>
        </div>
    </div>

    <script>
        function prevDiagram() {
            window.location.href = 'trinity-equation.html';
        }
        
        function nextDiagram() {
            window.location.href = 'pattern-translation.html';
        }
        
        // Disable buttons if needed
        document.addEventListener('DOMContentLoaded', function() {
            if ('trinity-equation.html' === '#') {
                document.getElementById('prev-button').disabled = true;
            }
            if ('pattern-translation.html' === '#') {
                document.getElementById('next-button').disabled = true;
            }
        });
    </script>
</body>
</html>
