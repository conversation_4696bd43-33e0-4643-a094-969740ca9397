/**
 * File Cleanup Utility
 * 
 * This utility provides functions for cleaning up temporary files.
 */

const fs = require('fs');
const path = require('path');
const largeFileConfig = require('../config/largeFileConfig');
const logger = require('./logger');

/**
 * Clean up temporary files older than the specified age
 * @param {string} directory - Directory to clean
 * @param {number} maxAge - Maximum age in milliseconds
 * @returns {Promise<number>} Number of files cleaned
 */
async function cleanupTempFiles(directory = largeFileConfig.tempDir, maxAge = largeFileConfig.cleanup.maxAge) {
  return new Promise((resolve, reject) => {
    try {
      // Check if directory exists
      if (!fs.existsSync(directory)) {
        logger.warn(`Temp directory does not exist: ${directory}`);
        return resolve(0);
      }
      
      // Get current time
      const now = Date.now();
      
      // Read directory
      fs.readdir(directory, (err, files) => {
        if (err) {
          logger.error(`Error reading temp directory: ${err.message}`);
          return reject(err);
        }
        
        let cleanedCount = 0;
        
        // Process each file
        files.forEach(file => {
          const filePath = path.join(directory, file);
          
          // Get file stats
          fs.stat(filePath, (statErr, stats) => {
            if (statErr) {
              logger.warn(`Error getting stats for file ${filePath}: ${statErr.message}`);
              return;
            }
            
            // Check if file is older than maxAge
            const fileAge = now - stats.mtime.getTime();
            
            if (fileAge > maxAge) {
              // Delete the file
              fs.unlink(filePath, (unlinkErr) => {
                if (unlinkErr) {
                  logger.warn(`Error deleting file ${filePath}: ${unlinkErr.message}`);
                } else {
                  cleanedCount++;
                  logger.info(`Cleaned up temporary file: ${file} (age: ${Math.round(fileAge / 1000 / 60)} minutes)`);
                }
              });
            }
          });
        });
        
        resolve(cleanedCount);
      });
    } catch (error) {
      logger.error(`Error in cleanupTempFiles: ${error.message}`);
      reject(error);
    }
  });
}

/**
 * Schedule periodic cleanup of temporary files
 * @param {number} interval - Cleanup interval in milliseconds
 * @returns {NodeJS.Timeout} Interval ID
 */
function scheduleCleanup(interval = 3600000) { // Default: 1 hour
  return setInterval(async () => {
    try {
      const cleanedCount = await cleanupTempFiles();
      if (cleanedCount > 0) {
        logger.info(`Scheduled cleanup removed ${cleanedCount} temporary files`);
      }
    } catch (error) {
      logger.error(`Error in scheduled cleanup: ${error.message}`);
    }
  }, interval);
}

module.exports = {
  cleanupTempFiles,
  scheduleCleanup
};
