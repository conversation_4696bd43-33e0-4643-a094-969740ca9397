import React, { useState } from 'react';
import Link from 'next/link';

// Mock NovaConcierge component for demonstration
const NovaConcierge = () => {
  const [messages, setMessages] = useState([
    { role: 'assistant', content: 'Hello! I\'m <PERSON><PERSON><PERSON><PERSON><PERSON>, your API integration assistant. How can I help you today?' }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!inputValue.trim() || isProcessing) return;
    
    // Add user message
    const userMessage = { role: 'user', content: inputValue };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsProcessing(true);
    
    // Simulate AI processing
    setTimeout(() => {
      let response;
      const lowerInput = inputValue.toLowerCase();
      
      if (lowerInput.includes('test') || lowerInput.includes('abilities') || lowerInput.includes('what can you do')) {
        response = { 
          role: 'assistant', 
          content: "I'd be happy to show you what I can do! Here are some ways I can help you:",
          suggestions: [
            { title: "Compare NovaFuse to competitors", description: "See how NovaFuse stacks up against other solutions" },
            { title: "Explore success stories", description: "Learn how other companies have benefited from NovaFuse" },
            { title: "Calculate your potential ROI", description: "See how much you could save with NovaFuse" },
            { title: "Learn about Partner Empowerment", description: "Discover our unique approach to partnerships" },
            { title: "Find the right API for your needs", description: "Get personalized API recommendations" }
          ]
        };
      } else if (lowerInput.includes('compare') || lowerInput.includes('competitor')) {
        response = { 
          role: 'assistant', 
          content: "Here's how NovaFuse compares to traditional GRC solutions:",
          comparison: {
            title: "NovaFuse vs. Traditional GRC Solutions",
            advantages: [
              "3,142x faster data normalization (0.07ms vs 220ms per finding)",
              "Multi-cloud support vs. single-cloud coverage",
              "Automated remediation across all platforms",
              "Unified compliance view across all cloud providers",
              "Partner Empowerment model vs. traditional licensing"
            ]
          }
        };
      } else if (lowerInput.includes('partner') || lowerInput.includes('empowerment')) {
        response = { 
          role: 'assistant', 
          content: "Partner Empowerment is our unique approach to partnerships. Unlike traditional vendor relationships, we believe in building with partners rather than selling to them.",
          partnerInfo: {
            title: "Partner Empowerment Program",
            description: "Our Partner Empowerment program is designed to create mutual success through revenue sharing, co-marketing, and technical enablement.",
            benefits: [
              "Up to 90% revenue sharing",
              "Co-marketing opportunities",
              "Technical enablement and support",
              "Joint product development"
            ]
          }
        };
      } else if (lowerInput.includes('roi') || lowerInput.includes('return') || lowerInput.includes('save')) {
        response = { 
          role: 'assistant', 
          content: "I can help you calculate your potential ROI with NovaFuse. Based on industry averages, companies typically see:",
          roi: {
            metrics: [
              "75% reduction in compliance management effort",
              "90% faster audit response time",
              "85% automation of common remediation tasks",
              "$1.5M annual savings in compliance management costs"
            ]
          }
        };
      } else {
        response = { 
          role: 'assistant', 
          content: "I'd be happy to help with that. Could you provide more details about your specific needs or use case? This will help me give you the most relevant information about our APIs and integration options." 
        };
      }
      
      setMessages(prev => [...prev, response]);
      setIsProcessing(false);
    }, 1500);
  };

  const renderMessageContent = (message) => {
    if (message.role === 'user') {
      return <div>{message.content}</div>;
    }
    
    return (
      <div>
        <div>{message.content}</div>
        
        {message.suggestions && (
          <div className="mt-4 space-y-2">
            {message.suggestions.map((suggestion, index) => (
              <div 
                key={index} 
                className="bg-gray-800 p-3 rounded cursor-pointer hover:bg-gray-700"
                onClick={() => {
                  setMessages(prev => [...prev, 
                    { role: 'user', content: suggestion.title },
                    { role: 'assistant', content: `I'll help you ${suggestion.title.toLowerCase()}.` }
                  ]);
                }}
              >
                <div className="font-medium">{suggestion.title}</div>
                <div className="text-sm text-gray-400">{suggestion.description}</div>
              </div>
            ))}
          </div>
        )}
        
        {message.comparison && (
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <div className="font-semibold mb-2">{message.comparison.title}</div>
            <ul className="space-y-1">
              {message.comparison.advantages.map((advantage, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{advantage}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {message.partnerInfo && (
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <div className="font-semibold mb-2">{message.partnerInfo.title}</div>
            <p className="mb-2 text-gray-300">{message.partnerInfo.description}</p>
            <div className="font-medium mt-3 mb-1">Key Benefits:</div>
            <ul className="space-y-1">
              {message.partnerInfo.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {message.roi && (
          <div className="mt-4 bg-gray-800 p-4 rounded">
            <div className="font-semibold mb-2">ROI Metrics</div>
            <ul className="space-y-1">
              {message.roi.metrics.map((metric, index) => (
                <li key={index} className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  <span>{metric}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-secondary rounded-lg shadow-lg overflow-hidden flex flex-col h-[600px]">
      <div className="bg-gray-800 p-4">
        <h2 className="text-xl font-bold">NovaConcierge</h2>
        <p className="text-sm text-gray-400">Your AI-powered API integration assistant</p>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div 
            key={index} 
            className={`p-3 rounded-lg max-w-[80%] ${
              message.role === 'user' 
                ? 'bg-blue-600 ml-auto' 
                : 'bg-gray-700'
            }`}
          >
            {renderMessageContent(message)}
          </div>
        ))}
        
        {isProcessing && (
          <div className="bg-gray-700 p-3 rounded-lg max-w-[80%] flex items-center space-x-2">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-150"></div>
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse delay-300"></div>
          </div>
        )}
      </div>
      
      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-700 flex">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Ask about API integration..."
          className="flex-1 bg-gray-700 text-white rounded-l-lg px-4 py-2 focus:outline-none"
          disabled={isProcessing}
        />
        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700 focus:outline-none disabled:opacity-50"
          disabled={isProcessing || !inputValue.trim()}
        >
          Send
        </button>
      </form>
    </div>
  );
};

export default function NovaConciergeAI() {
  return (
    <div>
      {/* Hero Section */}
      <div className="bg-secondary rounded-lg p-8 mb-8">
        <h1 className="text-3xl font-bold mb-4">Meet NovaConcierge AI</h1>
        <p className="text-xl mb-6">
          Your intelligent assistant for navigating the NovaFuse API Superstore.
          NovaConcierge helps you find the right APIs, understand integration options,
          and maximize your ROI with NovaFuse.
        </p>
      </div>

      {/* NovaConcierge Demo */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Try NovaConcierge</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <NovaConcierge />
          
          <div>
            <div className="bg-secondary rounded-lg p-6 mb-6">
              <h3 className="text-xl font-semibold mb-4">What Can NovaConcierge Do?</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Find the right APIs for your specific use case</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Compare NovaFuse to competitors with detailed metrics</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Calculate your potential ROI from using NovaFuse</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Explain the Partner Empowerment model and benefits</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Share success stories and use cases from similar companies</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Guide you through the integration process step-by-step</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-secondary rounded-lg p-6">
              <h3 className="text-xl font-semibold mb-4">Try These Prompts</h3>
              <div className="space-y-2">
                <div className="bg-gray-800 p-3 rounded cursor-pointer hover:bg-gray-700" onClick={() => document.querySelector('input').value = "What can you do?"}>
                  "What can you do?"
                </div>
                <div className="bg-gray-800 p-3 rounded cursor-pointer hover:bg-gray-700" onClick={() => document.querySelector('input').value = "Compare NovaFuse to competitors"}>
                  "Compare NovaFuse to competitors"
                </div>
                <div className="bg-gray-800 p-3 rounded cursor-pointer hover:bg-gray-700" onClick={() => document.querySelector('input').value = "Tell me about Partner Empowerment"}>
                  "Tell me about Partner Empowerment"
                </div>
                <div className="bg-gray-800 p-3 rounded cursor-pointer hover:bg-gray-700" onClick={() => document.querySelector('input').value = "What's the ROI of using NovaFuse?"}>
                  "What's the ROI of using NovaFuse?"
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Features */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Key Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">01</div>
            <h3 className="text-xl font-semibold mb-3">API Navigation</h3>
            <p className="text-gray-300">
              NovaConcierge helps you navigate our extensive API catalog to find the perfect solutions for your specific use case.
            </p>
          </div>
          
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">02</div>
            <h3 className="text-xl font-semibold mb-3">Marketplace Guidance</h3>
            <p className="text-gray-300">
              Get personalized recommendations for partners, integrations, and solutions available in the NovaFuse ecosystem.
            </p>
          </div>
          
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">03</div>
            <h3 className="text-xl font-semibold mb-3">Risk Profiling</h3>
            <p className="text-gray-300">
              NovaConcierge analyzes your requirements to identify potential compliance risks and recommend appropriate controls.
            </p>
          </div>
          
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">04</div>
            <h3 className="text-xl font-semibold mb-3">Use-Case Storytelling</h3>
            <p className="text-gray-300">
              Learn how similar organizations have solved compliance challenges using NovaFuse's API Superstore.
            </p>
          </div>
          
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">05</div>
            <h3 className="text-xl font-semibold mb-3">Competitor Comparison</h3>
            <p className="text-gray-300">
              Get detailed, fact-based comparisons between NovaFuse and alternative solutions with specific metrics.
            </p>
          </div>
          
          <div className="bg-secondary border border-gray-700 rounded-lg p-6">
            <div className="text-3xl text-blue-500 mb-4">06</div>
            <h3 className="text-xl font-semibold mb-3">ROI Calculator</h3>
            <p className="text-gray-300">
              Calculate your potential return on investment from implementing NovaFuse solutions in your organization.
            </p>
          </div>
        </div>
      </div>

      {/* Technical Specifications */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Technical Specifications</h2>
        <div className="bg-secondary border border-gray-700 rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-semibold mb-3">AI Capabilities</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Advanced natural language processing</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Context-aware conversation management</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Personalized recommendations engine</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Explainable AI (XAI) for transparent reasoning</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Multi-turn conversation handling</span>
                </li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-3">Integration</h3>
              <ul className="space-y-2 text-gray-300">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Seamless integration with NovaFuse platform</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Real-time access to API catalog and documentation</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Integration with partner ecosystem data</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Secure handling of customer information</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">→</span>
                  <span>Available as widget or full-page experience</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-secondary border border-gray-700 rounded-lg p-6">
        <div className="md:flex justify-between items-center">
          <div className="mb-6 md:mb-0">
            <h2 className="text-2xl font-bold mb-2">Ready to Experience NovaConcierge?</h2>
            <p className="text-gray-300">
              Integrate NovaConcierge into your workflow or explore our API Superstore with AI assistance.
            </p>
          </div>
          <div className="flex flex-wrap gap-4">
            <Link href="/partner-login" className="accent-bg text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 inline-block">
              Partner Login
            </Link>
            <Link href="/api-docs" className="border border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-bold hover:bg-blue-900 hover:bg-opacity-20 inline-block">
              Explore API Docs
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
