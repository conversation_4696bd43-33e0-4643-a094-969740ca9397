/**
 * MT5 CONNECTION STATUS COMPONENT
 * Real-time MetaTrader 5 account monitoring for CHAEONIX
 * <PERSON> Account: *********** | MetaQuotes-Demo
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

export default function MT5ConnectionStatus({ onConnectionChange }) {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [accountInfo, setAccountInfo] = useState(null);
  const [positions, setPositions] = useState([]);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);

  // Simulate MT5 connection status (replace with real API calls)
  useEffect(() => {
    // Initial update
    updateMT5Status();

    const interval = setInterval(() => {
      updateMT5Status();
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const updateMT5Status = async () => {
    try {
      // Real API call to MT5 status endpoint
      const response = await fetch('/api/mt5/status');
      const data = await response.json();

      console.log('MT5 API Response:', data); // Debug log

      const accountInfo = {
        ...data.account,
        login: data.connection.login,
        name: data.connection.name,
        server: data.connection.server,
        phi_metrics: data.phi_metrics
      };

      console.log('Processed Account Info:', accountInfo); // Debug log

      setAccountInfo(accountInfo);
      setPositions(data.positions || []);
      setConnectionStatus('connected');
      setLastUpdate(new Date());

      // Notify parent component
      if (onConnectionChange) {
        onConnectionChange('connected', accountInfo);
      }

    } catch (error) {
      setConnectionStatus('error');
      console.error('MT5 connection error:', error);
    }
  };

  const handleConnect = async () => {
    setIsConnecting(true);
    setConnectionStatus('connecting');

    try {
      // Call MT5 connection API
      const response = await fetch('/api/mt5/status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'connect' })
      });

      if (response.ok) {
        await updateMT5Status();
      } else {
        setConnectionStatus('error');
      }
    } catch (error) {
      console.error('Connection failed:', error);
      setConnectionStatus('error');
    } finally {
      setIsConnecting(false);
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
      case 'connecting':
        return <div className="w-5 h-5 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin" />;
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-400" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'border-green-500 bg-green-500/10';
      case 'connecting':
        return 'border-yellow-500 bg-yellow-500/10';
      case 'error':
        return 'border-red-500 bg-red-500/10';
      default:
        return 'border-gray-500 bg-gray-500/10';
    }
  };

  const formatCurrency = (amount) => {
    // Handle undefined, null, or NaN values
    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount)) {
      return '$0.00';
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(numericAmount);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-4 rounded-lg border backdrop-blur-sm ${getStatusColor()}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h3 className="text-lg font-semibold text-white">
              MT5 Connection
            </h3>
            <p className="text-sm text-gray-400">
              MetaQuotes-Demo | Account: ***********
            </p>
          </div>
        </div>

        {connectionStatus !== 'connected' && (
          <motion.button
            onClick={handleConnect}
            disabled={isConnecting}
            className={`px-4 py-2 rounded-lg font-medium transition-all ${
              isConnecting
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
            whileHover={!isConnecting ? { scale: 1.05 } : {}}
            whileTap={!isConnecting ? { scale: 0.95 } : {}}
          >
            {isConnecting ? 'Connecting...' : 'Connect'}
          </motion.button>
        )}
      </div>

      {/* Connection Status */}
      <div className="mb-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">Status:</span>
          <span className={`font-medium ${
            connectionStatus === 'connected' ? 'text-green-400' :
            connectionStatus === 'connecting' ? 'text-yellow-400' :
            connectionStatus === 'error' ? 'text-red-400' : 'text-gray-400'
          }`}>
            {connectionStatus.toUpperCase()}
          </span>
        </div>
        {lastUpdate && (
          <div className="flex items-center justify-between text-sm mt-1">
            <span className="text-gray-400">Last Update:</span>
            <span className="text-gray-300">
              {lastUpdate.toLocaleTimeString()}
            </span>
          </div>
        )}
      </div>

      {/* Account Information */}
      {accountInfo && connectionStatus === 'connected' && (
        <div className="space-y-4">
          {/* Account Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <CurrencyDollarIcon className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-400">Balance</span>
              </div>
              <div className="text-lg font-bold text-white">
                {formatCurrency(accountInfo.balance)}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <ChartBarIcon className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-gray-400">Equity</span>
              </div>
              <div className="text-lg font-bold text-white">
                {formatCurrency(accountInfo.equity)}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <BoltIcon className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-400">Profit</span>
              </div>
              <div className={`text-lg font-bold ${
                accountInfo.profit >= 0 ? 'text-green-400' : 'text-red-400'
              }`}>
                {accountInfo.profit >= 0 ? '+' : ''}{formatCurrency(accountInfo.profit)}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <ShieldCheckIcon className="w-4 h-4 text-purple-400" />
                <span className="text-sm text-gray-400">Leverage</span>
              </div>
              <div className="text-lg font-bold text-white">
                1:{accountInfo.leverage}
              </div>
            </div>
          </div>

          {/* φ-Divine Metrics */}
          <div className="border-t border-gray-600 pt-4">
            <h4 className="text-sm font-medium text-purple-400 mb-2">🔮 φ-Divine Metrics</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">φ-Balance:</span>
                <span className="text-purple-300">
                  {formatCurrency(accountInfo.phi_metrics.phi_balance)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">φ-Equity Ratio:</span>
                <span className="text-purple-300">
                  {accountInfo.phi_metrics.phi_equity_ratio.toFixed(3)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Golden Health:</span>
                <span className="text-purple-300">
                  {accountInfo.phi_metrics.golden_ratio_health.toFixed(3)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Divine Margin:</span>
                <span className="text-purple-300">
                  {accountInfo.phi_metrics.divine_margin_level.toFixed(1)}
                </span>
              </div>
            </div>
          </div>

          {/* Account Info */}
          <div className="border-t border-gray-600 pt-4">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">Server:</span>
                <span className="text-gray-300">{accountInfo.server}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Type:</span>
                <span className="text-gray-300">{accountInfo.account_type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Company:</span>
                <span className="text-gray-300">{accountInfo.company}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Currency:</span>
                <span className="text-gray-300">{accountInfo.currency}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error State */}
      {connectionStatus === 'error' && (
        <div className="text-center py-4">
          <XCircleIcon className="w-12 h-12 text-red-400 mx-auto mb-2" />
          <p className="text-red-400 font-medium">Connection Failed</p>
          <p className="text-sm text-gray-400 mt-1">
            Check MT5 terminal and credentials
          </p>
        </div>
      )}

      {/* Disconnected State */}
      {connectionStatus === 'disconnected' && (
        <div className="text-center py-4">
          <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-400 font-medium">Not Connected</p>
          <p className="text-sm text-gray-500 mt-1">
            Click Connect to establish MT5 connection
          </p>
        </div>
      )}
    </motion.div>
  );
}
