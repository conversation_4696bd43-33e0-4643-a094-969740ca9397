"""
Test script for the Universal Compliance Intelligence Architecture (UCIA).

This script tests the basic functionality of the UCIA.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import the UCIA
from ucia import UCIA

def test_ucia():
    """Test the basic functionality of the UCIA."""
    logger.info("Initializing UCIA...")
    ucia = UCIA(model_name="gpt2")  # Use a small model for testing
    
    logger.info("Getting active modules...")
    active_modules = ucia.get_active_modules()
    logger.info(f"Active modules: {', '.join([module['id'] for module in active_modules])}")
    
    # Test queries for different frameworks
    test_queries = [
        "What are the GDPR requirements for data breach notification?",
        "How does HIPAA define a security incident?",
        "What are the SOC2 requirements for incident response?",
        "How do I ensure compliance with data subject rights under GDPR?",
        "What are the key differences between GDPR and HIPAA breach notification requirements?"
    ]
    
    for query in test_queries:
        logger.info(f"\nProcessing query: {query}")
        response = ucia.process_query(query)
        
        logger.info(f"Response: {response['response_text']}")
        logger.info(f"Frameworks: {', '.join(response['frameworks'])}")
        logger.info(f"Concepts: {', '.join(response['concepts'])}")
        
        if response['citations']:
            logger.info("Citations:")
            for citation in response['citations']:
                logger.info(f"  - {citation['framework']}: {citation['reference']} - {citation['text']}")
    
    # Test cross-framework functionality
    logger.info("\nTesting cross-framework functionality...")
    
    # Get related requirements for data breach across frameworks
    logger.info("Getting related requirements for data breach...")
    data_breach_reqs = ucia.get_related_requirements('data_breach')
    for framework, reqs in data_breach_reqs.items():
        logger.info(f"  {framework.upper()}:")
        for req in reqs:
            logger.info(f"    - {req['reference']}: {req['title']}")
    
    # Get mappings between frameworks
    logger.info("\nGetting mappings between GDPR and HIPAA...")
    gdpr_to_hipaa = ucia.get_framework_mappings('gdpr', 'hipaa')
    for gdpr_req, hipaa_reqs in gdpr_to_hipaa.items():
        logger.info(f"  {gdpr_req} -> {', '.join(hipaa_reqs)}")
    
    logger.info("\nUCIA test completed successfully!")

if __name__ == "__main__":
    test_ucia()
