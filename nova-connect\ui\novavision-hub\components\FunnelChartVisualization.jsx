/**
 * FunnelChartVisualization Component
 * 
 * A component for visualizing sequential data as a funnel chart.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme/ThemeContext';
import { usePerformance } from '../performance/usePerformance';
import { Animated } from './Animated';

/**
 * FunnelChartVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Array} props.data - Chart data
 * @param {Object} [props.options] - Chart options
 * @param {boolean} [props.options.showLabels=true] - Whether to show labels
 * @param {boolean} [props.options.showValues=true] - Whether to show values
 * @param {boolean} [props.options.showPercentages=true] - Whether to show percentages
 * @param {boolean} [props.options.animate=true] - Whether to animate the chart
 * @param {string} [props.options.valueFormat='value'] - Value format ('value', 'percentage', or 'both')
 * @param {string} [props.options.direction='vertical'] - Chart direction ('vertical' or 'horizontal')
 * @param {boolean} [props.options.gradientColors=false] - Whether to use gradient colors
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} FunnelChartVisualization component
 */
const FunnelChartVisualization = ({
  data,
  options = {},
  className = '',
  style = {}
}) => {
  const { measureOperation } = usePerformance('FunnelChartVisualization');
  const { theme } = useTheme();
  
  // Refs
  const containerRef = useRef(null);
  
  // State
  const [hoveredSegment, setHoveredSegment] = useState(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  // Default options
  const {
    showLabels = true,
    showValues = true,
    showPercentages = true,
    animate = true,
    valueFormat = 'value',
    direction = 'vertical',
    gradientColors = false
  } = options;
  
  // Update dimensions on resize
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };
    
    updateDimensions();
    
    const resizeObserver = new ResizeObserver(updateDimensions);
    resizeObserver.observe(containerRef.current);
    
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);
  
  // Process data
  const processedData = measureOperation('processData', () => {
    if (!data || data.length === 0) return [];
    
    const maxValue = Math.max(...data.map(item => item.value));
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return data.map((item, index) => ({
      ...item,
      percentage: (item.value / maxValue) * 100,
      percentageOfTotal: (item.value / total) * 100,
      color: item.color || getDefaultColor(index)
    }));
  });
  
  // Calculate segment dimensions
  const segmentDimensions = measureOperation('calculateSegmentDimensions', () => {
    if (!processedData || processedData.length === 0 || dimensions.width === 0 || dimensions.height === 0) {
      return [];
    }
    
    const isVertical = direction === 'vertical';
    const containerWidth = dimensions.width;
    const containerHeight = dimensions.height;
    const segmentCount = processedData.length;
    
    // Calculate segment dimensions
    return processedData.map((segment, index) => {
      const percentage = segment.percentage;
      
      if (isVertical) {
        // Vertical funnel
        const segmentHeight = containerHeight / segmentCount;
        const maxWidth = containerWidth * 0.8;
        const minWidth = containerWidth * 0.3;
        const widthDiff = maxWidth - minWidth;
        const segmentWidth = maxWidth - (widthDiff * index) / (segmentCount - 1 || 1);
        
        return {
          x: (containerWidth - segmentWidth) / 2,
          y: index * segmentHeight,
          width: segmentWidth,
          height: segmentHeight,
          labelX: containerWidth / 2,
          labelY: index * segmentHeight + segmentHeight / 2
        };
      } else {
        // Horizontal funnel
        const segmentWidth = containerWidth / segmentCount;
        const maxHeight = containerHeight * 0.8;
        const minHeight = containerHeight * 0.3;
        const heightDiff = maxHeight - minHeight;
        const segmentHeight = maxHeight - (heightDiff * index) / (segmentCount - 1 || 1);
        
        return {
          x: index * segmentWidth,
          y: (containerHeight - segmentHeight) / 2,
          width: segmentWidth,
          height: segmentHeight,
          labelX: index * segmentWidth + segmentWidth / 2,
          labelY: containerHeight / 2
        };
      }
    });
  });
  
  // Handle mouse enter
  const handleMouseEnter = (index) => {
    setHoveredSegment(index);
  };
  
  // Handle mouse leave
  const handleMouseLeave = () => {
    setHoveredSegment(null);
  };
  
  // Format value
  const formatValue = (value, percentageOfTotal) => {
    switch (valueFormat) {
      case 'percentage':
        return `${percentageOfTotal.toFixed(1)}%`;
      case 'both':
        return `${value.toLocaleString()} (${percentageOfTotal.toFixed(1)}%)`;
      case 'value':
      default:
        return value.toLocaleString();
    }
  };
  
  // Get default color
  const getDefaultColor = (index) => {
    const colors = [
      theme.colors.primary,
      theme.colors.secondary,
      theme.colors.tertiary,
      theme.colors.success,
      theme.colors.info,
      theme.colors.warning,
      theme.colors.error
    ];
    
    return colors[index % colors.length];
  };
  
  // Create gradient
  const createGradient = (color, index, total) => {
    if (!gradientColors) return color;
    
    const opacity = 1 - (index / (total - 1)) * 0.6;
    
    // Convert hex to rgba
    if (color.startsWith('#')) {
      const r = parseInt(color.slice(1, 3), 16);
      const g = parseInt(color.slice(3, 5), 16);
      const b = parseInt(color.slice(5, 7), 16);
      
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }
    
    // Handle rgba
    if (color.startsWith('rgba')) {
      return color.replace(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/, `rgba($1, $2, $3, ${opacity})`);
    }
    
    // Handle rgb
    if (color.startsWith('rgb')) {
      return color.replace(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/, `rgba($1, $2, $3, ${opacity})`);
    }
    
    return color;
  };
  
  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={{ ...style, minHeight: '200px' }}
      data-testid="funnel-chart-visualization"
    >
      {processedData.length > 0 && segmentDimensions.length > 0 && (
        <svg
          width="100%"
          height="100%"
          viewBox={`0 0 ${dimensions.width} ${dimensions.height}`}
          preserveAspectRatio="none"
        >
          {/* Segments */}
          {processedData.map((segment, index) => {
            const dim = segmentDimensions[index];
            const isHovered = hoveredSegment === index;
            const segmentColor = createGradient(segment.color, index, processedData.length);
            
            return (
              <Animated
                key={index}
                animation={{
                  from: { opacity: 0, transform: 'scale(0.9)' },
                  to: { opacity: 1, transform: 'scale(1)' },
                  duration: 500,
                  delay: index * 100
                }}
                as="g"
                onMouseEnter={() => handleMouseEnter(index)}
                onMouseLeave={handleMouseLeave}
                style={{ cursor: 'pointer' }}
              >
                {/* Segment */}
                <rect
                  x={dim.x}
                  y={dim.y}
                  width={dim.width}
                  height={dim.height}
                  fill={segmentColor}
                  stroke={theme.colors.background}
                  strokeWidth={isHovered ? 2 : 1}
                  opacity={isHovered ? 1 : 0.9}
                  rx={4}
                  ry={4}
                />
                
                {/* Label */}
                {showLabels && (
                  <text
                    x={dim.labelX}
                    y={dim.labelY - 10}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fill={theme.colors.textPrimary}
                    fontSize={theme.typography.body2.fontSize}
                    fontWeight={isHovered ? 'bold' : 'normal'}
                  >
                    {segment.label}
                  </text>
                )}
                
                {/* Value */}
                {showValues && (
                  <text
                    x={dim.labelX}
                    y={dim.labelY + 10}
                    textAnchor="middle"
                    dominantBaseline="middle"
                    fill={theme.colors.textSecondary}
                    fontSize={theme.typography.body2.fontSize}
                  >
                    {formatValue(segment.value, segment.percentageOfTotal)}
                  </text>
                )}
              </Animated>
            );
          })}
        </svg>
      )}
      
      {(!processedData || processedData.length === 0) && (
        <div className="absolute inset-0 flex items-center justify-center text-textSecondary">
          No data available
        </div>
      )}
    </div>
  );
};

FunnelChartVisualization.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.number.isRequired,
      color: PropTypes.string
    })
  ).isRequired,
  options: PropTypes.shape({
    showLabels: PropTypes.bool,
    showValues: PropTypes.bool,
    showPercentages: PropTypes.bool,
    animate: PropTypes.bool,
    valueFormat: PropTypes.oneOf(['value', 'percentage', 'both']),
    direction: PropTypes.oneOf(['vertical', 'horizontal']),
    gradientColors: PropTypes.bool
  }),
  className: PropTypes.string,
  style: PropTypes.object
};

export default FunnelChartVisualization;
