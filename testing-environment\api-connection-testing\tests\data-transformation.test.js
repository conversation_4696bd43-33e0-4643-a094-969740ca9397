/**
 * Data Transformation Tests for NovaConnect Universal API Connector
 * 
 * These tests verify that the connector can transform data from external APIs.
 */

const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { 
  startAllServices, 
  stopAllServices,
  registryUrl,
  authUrl,
  executorUrl,
  mockApiUrl
} = require('../setup');

// Test data
const connector = require('../connectors/data-transformation-connector.json');

// Test credential
const credential = {
  name: 'Data Transformation Test Credential',
  authType: 'API_KEY',
  credentials: {
    apiKey: 'valid-api-key',
    headerName: 'X-API-Key'
  }
};

// Store connector and credential IDs
let connectorId;
let credentialId;

// Custom transformations
const customTransformations = {
  mapComplianceStatus: `
    function transform(value) {
      if (!value) return 'UNKNOWN';
      
      switch (value.toUpperCase()) {
        case 'PASSED':
          return 'COMPLIANT';
        case 'FAILED':
          return 'NON_COMPLIANT';
        case 'WARNING':
          return 'PARTIALLY_COMPLIANT';
        default:
          return 'UNKNOWN';
      }
    }
  `,
  mapSeverityToRisk: `
    function transform(value) {
      if (!value) return 'UNKNOWN';
      
      switch (value.toUpperCase()) {
        case 'CRITICAL':
          return 'VERY_HIGH';
        case 'HIGH':
          return 'HIGH';
        case 'MEDIUM':
          return 'MEDIUM';
        case 'LOW':
          return 'LOW';
        case 'INFORMATIONAL':
          return 'VERY_LOW';
        default:
          return 'UNKNOWN';
      }
    }
  `,
  formatDate: `
    function transform(value, params) {
      if (!value) return '';
      
      const date = new Date(value);
      const format = params?.format || 'YYYY-MM-DD';
      
      if (format === 'YYYY-MM-DD') {
        return date.toISOString().split('T')[0];
      }
      
      return date.toISOString();
    }
  `
};

describe('Data Transformation Tests', () => {
  // Start services before all tests
  beforeAll(async () => {
    await startAllServices();
    
    // Clear request history
    await axios.post(`${mockApiUrl}/clear-history`);
    
    // Register custom transformations
    for (const [name, code] of Object.entries(customTransformations)) {
      try {
        await axios.post(`${registryUrl}/transformations`, {
          name,
          code,
          description: `Custom transformation for ${name}`
        });
      } catch (error) {
        console.error(`Error registering transformation ${name}:`, error.message);
      }
    }
    
    // Register connector
    const connResponse = await axios.post(`${registryUrl}/connectors`, connector);
    connectorId = connResponse.data.id;
    
    // Create credential
    credential.connectorId = connectorId;
    const credResponse = await axios.post(`${authUrl}/credentials`, credential);
    credentialId = credResponse.data.id;
  }, 60000);
  
  // Stop services after all tests
  afterAll(async () => {
    // Clean up test data
    try {
      await axios.delete(`${registryUrl}/connectors/${connectorId}`);
    } catch (error) {
      console.error(`Error deleting connector ${connectorId}:`, error.message);
    }
    
    try {
      await axios.delete(`${authUrl}/credentials/${credentialId}`);
    } catch (error) {
      console.error(`Error deleting credential ${credentialId}:`, error.message);
    }
    
    // Delete custom transformations
    for (const name of Object.keys(customTransformations)) {
      try {
        await axios.delete(`${registryUrl}/transformations/${name}`);
      } catch (error) {
        console.error(`Error deleting transformation ${name}:`, error.message);
      }
    }
    
    stopAllServices();
  });
  
  // Test basic data transformation
  describe('Basic Data Transformation', () => {
    it('should transform data from the API response', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Check that the data was transformed correctly
      expect(response.data).toHaveProperty('findingIds');
      expect(response.data.findingIds).toContain('finding-1');
      expect(response.data.findingIds).toContain('finding-2');
      
      expect(response.data).toHaveProperty('titles');
      expect(response.data.titles).toContain('Security Group allows unrestricted access');
      expect(response.data.titles).toContain('S3 bucket allows public access');
      
      expect(response.data).toHaveProperty('severities');
      expect(response.data.severities).toContain('HIGH');
      expect(response.data.severities).toContain('CRITICAL');
      
      expect(response.data).toHaveProperty('statuses');
      expect(response.data.statuses).toContain('ACTIVE');
    });
    
    it('should apply custom transformations', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Check that custom transformations were applied
      expect(response.data).toHaveProperty('complianceStatuses');
      expect(response.data.complianceStatuses).toContain('NON_COMPLIANT');
      
      expect(response.data).toHaveProperty('riskLevels');
      expect(response.data.riskLevels).toContain('HIGH');
      expect(response.data.riskLevels).toContain('VERY_HIGH');
      
      expect(response.data).toHaveProperty('createdDates');
      expect(response.data.createdDates[0]).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    });
  });
  
  // Test array handling
  describe('Array Handling', () => {
    it('should handle arrays in the response', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Check that arrays were handled correctly
      expect(Array.isArray(response.data.findingIds)).toBe(true);
      expect(response.data.findingIds.length).toBe(2);
      
      expect(Array.isArray(response.data.titles)).toBe(true);
      expect(response.data.titles.length).toBe(2);
      
      expect(Array.isArray(response.data.severities)).toBe(true);
      expect(response.data.severities.length).toBe(2);
    });
  });
  
  // Test nested object handling
  describe('Nested Object Handling', () => {
    it('should handle nested objects in the response', async () => {
      // Get the raw response from the mock API
      const mockResponse = await axios.get(`${mockApiUrl}/transform/findings`);
      
      // Execute the connector
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Check that nested objects were handled correctly
      expect(response.data).toHaveProperty('complianceStatuses');
      expect(response.data.complianceStatuses.length).toBe(2);
      
      // Verify that the compliance status was extracted from the nested object
      const originalCompliance = mockResponse.data.Findings.map(f => f.Compliance.Status);
      expect(originalCompliance).toContain('FAILED');
      
      // Verify that the transformation was applied
      expect(response.data.complianceStatuses).toContain('NON_COMPLIANT');
    });
  });
  
  // Test transformation with parameters
  describe('Transformation with Parameters', () => {
    it('should apply transformations with parameters', async () => {
      const response = await axios.post(`${executorUrl}/execute/${connectorId}/getFindings`, {
        credentialId,
        parameters: {},
        userId: 'test-user'
      });
      
      expect(response.status).toBe(200);
      
      // Check that the date format transformation was applied with parameters
      expect(response.data).toHaveProperty('createdDates');
      expect(response.data.createdDates[0]).toMatch(/^\d{4}-\d{2}-\d{2}$/);
      
      // The original date was in ISO format
      const mockResponse = await axios.get(`${mockApiUrl}/transform/findings`);
      const originalDate = mockResponse.data.Findings[0].CreatedAt;
      expect(originalDate).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/);
      
      // The transformed date should be in YYYY-MM-DD format
      const transformedDate = response.data.createdDates[0];
      expect(transformedDate).toBe(originalDate.split('T')[0]);
    });
  });
});
