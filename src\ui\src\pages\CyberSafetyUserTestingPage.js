import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Tabs, 
  Tab, 
  Grid, 
  Card, 
  CardContent, 
  CardActions, 
  Divider, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  Chip, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  TextField, 
  CircularProgress, 
  Alert
} from '@mui/material';
import { 
  Add as AddIcon, 
  PlayArrow as PlayIcon, 
  Assessment as AssessmentIcon, 
  Person as PersonIcon, 
  Assignment as AssignmentIcon, 
  CheckCircle as CheckCircleIcon, 
  Error as ErrorIcon, 
  Schedule as ScheduleIcon, 
  Delete as DeleteIcon, 
  Edit as EditIcon, 
  Download as DownloadIcon
} from '@mui/icons-material';
import axios from 'axios';
import UserTestingWizard from '../components/testing/UserTestingWizard';
import userTestingService, { TEST_TYPES } from '../services/userTestingService';

// API base URL
const API_BASE_URL = '/api';

/**
 * Cyber-Safety User Testing Page
 * 
 * This page allows administrators to manage user testing sessions for Cyber-Safety visualizations.
 */
function CyberSafetyUserTestingPage() {
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);
  
  // State for test sessions
  const [testSessions, setTestSessions] = useState([]);
  
  // State for test results
  const [testResults, setTestResults] = useState([]);
  
  // State for loading
  const [loading, setLoading] = useState(false);
  
  // State for error
  const [error, setError] = useState(null);
  
  // State for new test dialog
  const [newTestDialog, setNewTestDialog] = useState({
    open: false,
    visualizationType: 'triDomainTensor',
    testType: TEST_TYPES.USABILITY,
    description: ''
  });
  
  // State for active test session
  const [activeTestSession, setActiveTestSession] = useState(null);
  
  // Load test sessions and results on component mount
  useEffect(() => {
    loadTestSessions();
    loadTestResults();
  }, []);
  
  // Load test sessions
  const loadTestSessions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`${API_BASE_URL}/user-testing/sessions`);
      setTestSessions(response.data);
    } catch (error) {
      console.error('Error loading test sessions:', error);
      setError('Failed to load test sessions. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Load test results
  const loadTestResults = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`${API_BASE_URL}/user-testing/results`);
      setTestResults(response.data);
    } catch (error) {
      console.error('Error loading test results:', error);
      setError('Failed to load test results. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle new test dialog open
  const handleNewTestDialogOpen = () => {
    setNewTestDialog({ ...newTestDialog, open: true });
  };
  
  // Handle new test dialog close
  const handleNewTestDialogClose = () => {
    setNewTestDialog({ ...newTestDialog, open: false });
  };
  
  // Handle new test dialog change
  const handleNewTestDialogChange = (field, value) => {
    setNewTestDialog({ ...newTestDialog, [field]: value });
  };
  
  // Handle new test creation
  const handleCreateTest = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { visualizationType, testType, description } = newTestDialog;
      
      const response = await axios.post(`${API_BASE_URL}/user-testing/sessions`, {
        visualizationType,
        testType,
        description
      });
      
      // Add new test to list
      setTestSessions([...testSessions, response.data]);
      
      // Close dialog
      handleNewTestDialogClose();
    } catch (error) {
      console.error('Error creating test session:', error);
      setError('Failed to create test session. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle test session start
  const handleStartTest = (session) => {
    setActiveTestSession(session);
  };
  
  // Handle test session completion
  const handleTestComplete = () => {
    setActiveTestSession(null);
    loadTestResults();
  };
  
  // Handle test session deletion
  const handleDeleteSession = async (sessionId) => {
    try {
      setLoading(true);
      setError(null);
      
      await axios.delete(`${API_BASE_URL}/user-testing/sessions/${sessionId}`);
      
      // Remove session from list
      setTestSessions(testSessions.filter(session => session.id !== sessionId));
    } catch (error) {
      console.error('Error deleting test session:', error);
      setError('Failed to delete test session. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle test result deletion
  const handleDeleteResult = async (resultId) => {
    try {
      setLoading(true);
      setError(null);
      
      await axios.delete(`${API_BASE_URL}/user-testing/results/${resultId}`);
      
      // Remove result from list
      setTestResults(testResults.filter(result => result.id !== resultId));
    } catch (error) {
      console.error('Error deleting test result:', error);
      setError('Failed to delete test result. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle test result export
  const handleExportResult = async (resultId) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`${API_BASE_URL}/user-testing/results/${resultId}/export`, {
        responseType: 'blob'
      });
      
      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `test-result-${resultId}.json`);
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting test result:', error);
      setError('Failed to export test result. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Render test sessions tab
  const renderTestSessionsTab = () => {
    return (
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h5">Test Sessions</Typography>
          
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleNewTestDialogOpen}
          >
            Create Test
          </Button>
        </Box>
        
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {!loading && testSessions.length === 0 && (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              No test sessions available. Create a new test session to get started.
            </Typography>
          </Paper>
        )}
        
        <Grid container spacing={2}>
          {testSessions.map(session => (
            <Grid item xs={12} sm={6} md={4} key={session.id}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {session.visualizationType} Test
                  </Typography>
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Type: {session.testType}
                  </Typography>
                  
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    Created: {new Date(session.createdAt).toLocaleString()}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    {session.description}
                  </Typography>
                  
                  <Box sx={{ mt: 1 }}>
                    <Chip
                      label={`${session.completedCount || 0} completed`}
                      size="small"
                      color="primary"
                      sx={{ mr: 1 }}
                    />
                    
                    <Chip
                      label={session.active ? 'Active' : 'Inactive'}
                      size="small"
                      color={session.active ? 'success' : 'default'}
                    />
                  </Box>
                </CardContent>
                
                <Divider />
                
                <CardActions>
                  <Button
                    size="small"
                    startIcon={<PlayIcon />}
                    onClick={() => handleStartTest(session)}
                  >
                    Start Test
                  </Button>
                  
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                  >
                    Edit
                  </Button>
                  
                  <Button
                    size="small"
                    startIcon={<DeleteIcon />}
                    color="error"
                    onClick={() => handleDeleteSession(session.id)}
                  >
                    Delete
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };
  
  // Render test results tab
  const renderTestResultsTab = () => {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Test Results
        </Typography>
        
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        )}
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {!loading && testResults.length === 0 && (
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body1">
              No test results available. Complete a test session to see results here.
            </Typography>
          </Paper>
        )}
        
        <List>
          {testResults.map(result => (
            <Paper key={result.id} sx={{ mb: 2 }}>
              <ListItem>
                <ListItemIcon>
                  <AssessmentIcon />
                </ListItemIcon>
                
                <ListItemText
                  primary={`${result.visualizationType} - ${result.testType}`}
                  secondary={
                    <>
                      <Typography component="span" variant="body2" color="textPrimary">
                        Participant: {result.participantInfo?.name || 'Anonymous'}
                      </Typography>
                      <br />
                      <Typography component="span" variant="body2" color="textSecondary">
                        Completed: {new Date(result.endTime).toLocaleString()}
                      </Typography>
                      <br />
                      <Typography component="span" variant="body2" color="textSecondary">
                        Success Rate: {Math.round(result.successRate * 100)}%
                      </Typography>
                    </>
                  }
                />
                
                <Box sx={{ display: 'flex' }}>
                  <Button
                    size="small"
                    startIcon={<AssessmentIcon />}
                  >
                    View
                  </Button>
                  
                  <Button
                    size="small"
                    startIcon={<DownloadIcon />}
                    onClick={() => handleExportResult(result.id)}
                  >
                    Export
                  </Button>
                  
                  <Button
                    size="small"
                    startIcon={<DeleteIcon />}
                    color="error"
                    onClick={() => handleDeleteResult(result.id)}
                  >
                    Delete
                  </Button>
                </Box>
              </ListItem>
            </Paper>
          ))}
        </List>
      </Box>
    );
  };
  
  // If active test session, render test wizard
  if (activeTestSession) {
    return (
      <UserTestingWizard
        visualizationType={activeTestSession.visualizationType}
        testType={activeTestSession.testType}
        onComplete={handleTestComplete}
      />
    );
  }
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        Cyber-Safety User Testing
      </Typography>
      
      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Test Sessions" icon={<AssignmentIcon />} iconPosition="start" />
        <Tab label="Test Results" icon={<AssessmentIcon />} iconPosition="start" />
      </Tabs>
      
      {activeTab === 0 && renderTestSessionsTab()}
      {activeTab === 1 && renderTestResultsTab()}
      
      {/* New Test Dialog */}
      <Dialog open={newTestDialog.open} onClose={handleNewTestDialogClose}>
        <DialogTitle>Create New Test Session</DialogTitle>
        
        <DialogContent>
          <FormControl fullWidth margin="normal">
            <InputLabel>Visualization Type</InputLabel>
            <Select
              value={newTestDialog.visualizationType}
              onChange={(e) => handleNewTestDialogChange('visualizationType', e.target.value)}
              label="Visualization Type"
            >
              <MenuItem value="triDomainTensor">Tri-Domain Tensor</MenuItem>
              <MenuItem value="harmonyIndex">Harmony Index</MenuItem>
              <MenuItem value="riskControlFusion">Risk-Control Fusion</MenuItem>
              <MenuItem value="resonanceSpectrogram">Resonance Spectrogram</MenuItem>
              <MenuItem value="unifiedComplianceSecurity">Unified Compliance-Security</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl fullWidth margin="normal">
            <InputLabel>Test Type</InputLabel>
            <Select
              value={newTestDialog.testType}
              onChange={(e) => handleNewTestDialogChange('testType', e.target.value)}
              label="Test Type"
            >
              <MenuItem value={TEST_TYPES.USABILITY}>Usability</MenuItem>
              <MenuItem value={TEST_TYPES.COMPREHENSION}>Comprehension</MenuItem>
              <MenuItem value={TEST_TYPES.ACCESSIBILITY}>Accessibility</MenuItem>
              <MenuItem value={TEST_TYPES.PERFORMANCE}>Performance</MenuItem>
              <MenuItem value={TEST_TYPES.MOBILE}>Mobile</MenuItem>
            </Select>
          </FormControl>
          
          <TextField
            label="Description"
            multiline
            rows={4}
            value={newTestDialog.description}
            onChange={(e) => handleNewTestDialogChange('description', e.target.value)}
            fullWidth
            margin="normal"
          />
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleNewTestDialogClose}>Cancel</Button>
          <Button onClick={handleCreateTest} color="primary" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default CyberSafetyUserTestingPage;
