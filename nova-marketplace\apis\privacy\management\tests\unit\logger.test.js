/**
 * Logger Tests
 *
 * This file contains unit tests for the logger utility.
 */

// <PERSON><PERSON> winston before importing the logger module
jest.mock('winston', () => {
  // Create a mock logger instance
  const mockLogger = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    add: jest.fn() // Add the missing 'add' method
  };

  return {
    format: {
      timestamp: jest.fn().mockReturnValue('timestamp-format'),
      json: jest.fn().mockReturnValue('json-format'),
      combine: jest.fn().mockReturnValue('combined-format'),
      colorize: jest.fn().mockReturnValue('colorize-format'),
      simple: jest.fn().mockReturnValue('simple-format')
    },
    createLogger: jest.fn().mockReturnValue(mockLogger),
    transports: {
      Console: jest.fn(),
      File: jest.fn()
    }
  };
});

describe('Logger', () => {
  // Save original environment
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    // Reset environment variables
    process.env = { ...originalEnv };
    // Clear the module cache to ensure the logger is re-initialized for each test
    jest.resetModules();
  });

  afterAll(() => {
    // Restore environment variables
    process.env = originalEnv;
  });

  it('should create a logger with the correct configuration in development', () => {
    // Set NODE_ENV to development
    process.env.NODE_ENV = 'development';

    // Import the logger module
    const { logger } = require('../../utils/logger');

    // Verify winston.createLogger was called with the correct configuration
    expect(require('winston').createLogger).toHaveBeenCalledWith(expect.objectContaining({
      level: 'info',
      format: expect.anything(),
      defaultMeta: { service: 'privacy-management-api' }
    }));

    // Verify Console transport was added
    expect(require('winston').transports.Console).toHaveBeenCalledWith(expect.objectContaining({
      format: expect.anything()
    }));

    // Verify File transports were not added
    expect(require('winston').transports.File).not.toHaveBeenCalled();
  });

  it('should create a logger with the correct configuration in production', () => {
    // Set NODE_ENV to production
    process.env.NODE_ENV = 'production';

    // Import the logger module
    const { logger } = require('../../utils/logger');

    // Verify winston.createLogger was called with the correct configuration
    expect(require('winston').createLogger).toHaveBeenCalledWith(expect.objectContaining({
      level: 'info',
      format: expect.anything(),
      defaultMeta: { service: 'privacy-management-api' }
    }));

    // Verify Console transport was added
    expect(require('winston').transports.Console).toHaveBeenCalledWith(expect.objectContaining({
      format: expect.anything()
    }));

    // Verify File transports were added
    expect(require('winston').transports.File).toHaveBeenCalledTimes(2);
    expect(require('winston').transports.File).toHaveBeenCalledWith(expect.objectContaining({
      filename: 'logs/error.log',
      level: 'error'
    }));
    expect(require('winston').transports.File).toHaveBeenCalledWith(expect.objectContaining({
      filename: 'logs/combined.log'
    }));
  });

  it('should use the LOG_LEVEL environment variable if available', () => {
    // Set LOG_LEVEL environment variable
    process.env.LOG_LEVEL = 'debug';
    process.env.NODE_ENV = 'development';

    // Import the logger module
    const { logger } = require('../../utils/logger');

    // Verify winston.createLogger was called with the correct configuration
    expect(require('winston').createLogger).toHaveBeenCalledWith(expect.objectContaining({
      level: 'debug',
      format: expect.anything(),
      defaultMeta: { service: 'privacy-management-api' }
    }));
  });

  it('should expose a logger object with the expected methods', () => {
    // Import the logger module
    const { logger } = require('../../utils/logger');

    // Verify the logger has the expected methods
    expect(logger).toBeDefined();
    expect(typeof logger.info).toBe('function');
    expect(typeof logger.error).toBe('function');
    expect(typeof logger.warn).toBe('function');
    expect(typeof logger.debug).toBe('function');
  });
});
