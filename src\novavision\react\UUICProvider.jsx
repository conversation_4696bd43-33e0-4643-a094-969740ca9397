import React, { createContext, useContext } from 'react';

const UUICContext = createContext();

/**
 * UUICProvider - Context provider for UI data injection
 * 
 * This component provides the UI configuration to all child components
 * through React Context.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Object} props.moduleConfig - UI module configuration
 * @returns {React.ReactElement} Provider component
 */
export const UUICProvider = ({ children, moduleConfig }) => {
  return (
    <UUICContext.Provider value={moduleConfig}>
      {children}
    </UUICContext.Provider>
  );
};

/**
 * useUUICContext - Hook to access the UUIC context
 * 
 * @returns {Object} UUIC context value
 */
export const useUUICContext = () => {
  const context = useContext(UUICContext);
  if (context === undefined) {
    throw new Error('useUUICContext must be used within a UUICProvider');
  }
  return context;
};

export default UUICProvider;
