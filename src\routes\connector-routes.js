/**
 * NovaFuse Universal API Connector - Connector Routes
 *
 * This module provides routes for managing connectors.
 */

const express = require('express');
const router = express.Router();
const connectorRegistryService = require('../connectors/services/connector-registry');
const connectorConfigService = require('../connectors/services/connector-config');
const connectorRuntimeService = require('../connectors/services/connector-runtime');
const { auth, validator, sanitizer, rateLimiter } = require('../middleware');

// Async handler to catch errors
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Validation schemas
const connectorSchema = {
  required: ['name', 'description', 'version', 'type'],
  properties: {
    name: { type: 'string' },
    description: { type: 'string' },
    version: { type: 'string' },
    type: { type: 'string', enum: ['source', 'destination', 'transformation'] },
    status: { type: 'string', enum: ['draft', 'testing', 'published', 'deprecated', 'retired'] }
  }
};

const configSchema = {
  required: ['name'],
  properties: {
    name: { type: 'string' },
    description: { type: 'string' },
    values: { type: 'object' }
  }
};

const executeSchema = {
  required: ['configId'],
  properties: {
    configId: { type: 'string' },
    timeout: { type: 'number' },
    parameters: { type: 'object' }
  }
};

// Apply rate limiting to all routes
router.use(rateLimiter.rateLimiter({
  windowMs: 60 * 1000, // 1 minute
  max: 100 // 100 requests per minute
}));

// Apply sanitization to all routes
router.use(sanitizer.sanitize);

/**
 * @route GET /connectors
 * @description Get all connectors
 * @access Public
 */
router.get('/', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  const filters = {
    status: req.query.status,
    type: req.query.type
  };

  const connectors = await connectorRegistryService.getAllConnectors(filters);

  res.json({
    count: connectors.length,
    connectors
  });
}));

/**
 * @route GET /connectors/:id
 * @description Get a connector by ID
 * @access Public
 */
router.get('/:id', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  const connector = await connectorRegistryService.getConnector(req.params.id);

  res.json(connector);
}));

/**
 * @route POST /connectors
 * @description Create a new connector
 * @access Public
 */
router.post('/', auth.apiKeyAuth, auth.roleAuth(['admin']), validator.validate(connectorSchema), asyncHandler(async (req, res) => {

  const connector = await connectorRegistryService.createConnector(req.body);

  res.status(201).json(connector);
}));

/**
 * @route PUT /connectors/:id
 * @description Update a connector
 * @access Public
 */
router.put('/:id', auth.apiKeyAuth, auth.roleAuth(['admin']), validator.validate(connectorSchema), asyncHandler(async (req, res) => {
  const connector = await connectorRegistryService.updateConnector(req.params.id, req.body);

  res.json(connector);
}));

/**
 * @route DELETE /connectors/:id
 * @description Delete a connector
 * @access Public
 */
router.delete('/:id', auth.apiKeyAuth, auth.roleAuth(['admin']), asyncHandler(async (req, res) => {
  await connectorRegistryService.deleteConnector(req.params.id);

  res.status(204).end();
}));

/**
 * @route GET /connectors/:id/configs
 * @description Get configurations for a connector
 * @access Public
 */
router.get('/:id/configs', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  const configs = await connectorConfigService.getConfigurationsForConnector(req.params.id);

  res.json({
    count: configs.length,
    configs
  });
}));

/**
 * @route POST /connectors/:id/configs
 * @description Create a configuration for a connector
 * @access Public
 */
router.post('/:id/configs', auth.apiKeyAuth, auth.roleAuth(['admin']), validator.validate(configSchema), asyncHandler(async (req, res) => {

  const config = await connectorConfigService.createConfiguration({
    ...req.body,
    connectorId: req.params.id
  });

  res.status(201).json(config);
}));

/**
 * @route POST /connectors/:id/execute
 * @description Execute a connector
 * @access Public
 */
router.post('/:id/execute', auth.apiKeyAuth, validator.validate(executeSchema), asyncHandler(async (req, res) => {

  const options = {
    timeout: req.body.timeout || 30000,
    parameters: req.body.parameters || {}
  };

  const result = await connectorRuntimeService.executeConnector(req.params.id, req.body.configId, options);

  res.json(result);
}));

/**
 * @route GET /executions
 * @description Get all executions
 * @access Public
 */
router.get('/executions', auth.apiKeyAuth, auth.roleAuth(['admin']), asyncHandler(async (req, res) => {
  const executions = connectorRuntimeService.getAllExecutions();

  res.json({
    count: executions.length,
    executions
  });
}));

/**
 * @route GET /executions/:id
 * @description Get execution status
 * @access Public
 */
router.get('/executions/:id', auth.apiKeyAuth, asyncHandler(async (req, res) => {
  const status = connectorRuntimeService.getExecutionStatus(req.params.id);

  res.json(status);
}));

module.exports = router;
