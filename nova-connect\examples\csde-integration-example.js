/**
 * CSDE Integration Example
 * 
 * This example demonstrates how to use the CSDE integration with NovaConnect
 * to transform security findings from Google Cloud Security Command Center.
 */

const CSEDIntegration = require('../src/integrations/csde-integration');
const fs = require('fs');
const path = require('path');

// Create CSDE integration
const csdeIntegration = new CSEDIntegration({
  csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
  enableCaching: true,
  enableMetrics: true
});

// Sample security finding from Google Cloud Security Command Center
const securityFinding = {
  id: 'finding-123456',
  source: 'gcp-security-command-center',
  severity: 'HIGH',
  category: 'VULNERABILITY',
  createTime: '2025-05-08T12:34:56.789Z',
  updateTime: '2025-05-08T13:45:12.345Z',
  resourceName: 'projects/my-project/zones/us-central1-a/instances/my-instance',
  resourceType: 'google.compute.Instance',
  state: 'ACTIVE',
  externalUri: 'https://console.cloud.google.com/security/command-center/findings?project=my-project',
  sourceProperties: {
    scanConfigId: 'scan-config-12345',
    scanRunId: 'scan-run-67890',
    vulnerabilityType: 'XSS',
    vulnerabilityDetails: {
      cvssScore: 8.5,
      cvssVector: 'CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:H/A:H',
      cve: 'CVE-2025-12345',
      description: 'Cross-site scripting vulnerability in web application',
      references: [
        'https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2025-12345',
        'https://nvd.nist.gov/vuln/detail/CVE-2025-12345'
      ],
      fixAvailable: true,
      exploitAvailable: true
    }
  },
  securityMarks: {
    marks: {
      criticality: 'p0',
      compliance: 'pci-dss,hipaa,soc2',
      dataClassification: 'confidential'
    }
  },
  nextSteps: [
    'Update web application to latest version',
    'Apply security patch',
    'Implement input validation'
  ],
  complianceState: 'NON_COMPLIANT'
};

// Transformation rules
const transformationRules = [
  {
    source: 'id',
    target: 'finding_id',
    transform: 'uppercase'
  },
  {
    source: 'source',
    target: 'source_system',
    transform: 'lowercase'
  },
  {
    source: 'severity',
    target: 'risk_level',
    transform: ['uppercase', 'trim']
  },
  {
    source: 'category',
    target: 'finding_type',
    transform: 'lowercase'
  },
  {
    source: 'createTime',
    target: 'created_at'
  },
  {
    source: 'updateTime',
    target: 'updated_at'
  },
  {
    source: 'resourceName',
    target: 'asset.full_path'
  },
  {
    source: 'resourceType',
    target: 'asset.type',
    transform: 'split',
    transformParams: '.'
  },
  {
    source: 'state',
    target: 'status',
    transform: 'lowercase'
  },
  {
    source: 'externalUri',
    target: 'links.console'
  },
  {
    source: 'sourceProperties.vulnerabilityType',
    target: 'details.vulnerability_type',
    transform: 'lowercase'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssScore',
    target: 'details.cvss_score',
    transform: 'toNumber'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cvssVector',
    target: 'details.cvss_vector'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.cve',
    target: 'details.cve_id'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.description',
    target: 'details.description'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.references',
    target: 'details.references'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.fixAvailable',
    target: 'details.fix_available'
  },
  {
    source: 'sourceProperties.vulnerabilityDetails.exploitAvailable',
    target: 'details.exploit_available'
  },
  {
    source: 'securityMarks.marks.criticality',
    target: 'metadata.criticality'
  },
  {
    source: 'securityMarks.marks.compliance',
    target: 'metadata.compliance_frameworks',
    transform: 'split',
    transformParams: ','
  },
  {
    source: 'securityMarks.marks.dataClassification',
    target: 'metadata.data_classification'
  },
  {
    source: 'nextSteps',
    target: 'remediation.steps'
  },
  {
    source: 'complianceState',
    target: 'compliance_status',
    transform: 'lowercase'
  }
];

// Mock CSDE API response for demonstration purposes
const mockCsdeResponse = {
  data: {
    result: {
      csdeValue: 3142.59,
      performanceFactor: 3142,
      nistComponent: {
        processedValue: 10
      },
      gcpComponent: {
        processedValue: 10
      },
      cyberSafetyComponent: {
        processedValue: 31.42
      },
      tensorProduct: 100,
      fusionResult: 100.31,
      remediationActions: [
        {
          id: 'action-1',
          title: 'Update Web Application',
          description: 'Update the web application to the latest version to fix the XSS vulnerability',
          severity: 'HIGH',
          targetField: 'sourceProperties.vulnerabilityDetails.fixAvailable',
          enhancedValue: true
        },
        {
          id: 'action-2',
          title: 'Implement Input Validation',
          description: 'Implement proper input validation to prevent XSS attacks',
          severity: 'MEDIUM',
          targetField: 'nextSteps',
          enhancedValue: [
            'Update web application to latest version',
            'Apply security patch',
            'Implement input validation',
            'Add Content Security Policy (CSP) headers'
          ]
        }
      ],
      calculatedAt: new Date().toISOString()
    }
  }
};

// Mock axios for demonstration purposes
const axios = require('axios');
axios.post = jest.fn().mockResolvedValue(mockCsdeResponse);

/**
 * Transform a security finding using CSDE
 * @param {Object} finding - Security finding to transform
 * @returns {Promise<Object>} - Transformed finding
 */
async function transformFinding(finding) {
  console.log('Transforming security finding using CSDE...');
  
  try {
    // Transform finding using CSDE
    const result = await csdeIntegration.transform(finding, transformationRules);
    
    // Log metrics
    const metrics = csdeIntegration.getMetrics();
    console.log('Metrics:', metrics);
    
    return result;
  } catch (error) {
    console.error('Error transforming finding:', error);
    throw error;
  }
}

/**
 * Process a batch of security findings
 * @param {Array} findings - Security findings to process
 * @returns {Promise<Array>} - Transformed findings
 */
async function processBatch(findings) {
  console.log(`Processing batch of ${findings.length} findings...`);
  
  const startTime = performance.now();
  
  // Transform each finding
  const promises = findings.map(finding => transformFinding(finding));
  
  // Wait for all transformations to complete
  const results = await Promise.all(promises);
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log(`Batch processing complete in ${duration.toFixed(2)} ms`);
  console.log(`Average time per finding: ${(duration / findings.length).toFixed(2)} ms`);
  
  return results;
}

/**
 * Save results to file
 * @param {Object} result - Result to save
 * @param {string} filename - Filename to save to
 */
function saveResult(result, filename) {
  const outputDir = path.join(__dirname, 'output');
  
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Save result to file
  const filePath = path.join(outputDir, filename);
  fs.writeFileSync(filePath, JSON.stringify(result, null, 2));
  
  console.log(`Result saved to ${filePath}`);
}

/**
 * Main function
 */
async function main() {
  try {
    // Transform a single finding
    console.log('Transforming a single finding...');
    const result = await transformFinding(securityFinding);
    
    // Save result to file
    saveResult(result, 'transformed-finding.json');
    
    // Process a batch of findings
    console.log('\nProcessing a batch of findings...');
    const batch = Array(10).fill().map((_, i) => ({
      ...securityFinding,
      id: `finding-${100000 + i}`,
      severity: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'][i % 4]
    }));
    
    const batchResults = await processBatch(batch);
    
    // Save batch results to file
    saveResult(batchResults, 'batch-results.json');
    
    // Log final metrics
    console.log('\nFinal Metrics:');
    console.log(csdeIntegration.getMetrics());
    
    console.log('\nExample complete!');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the example
main().catch(console.error);
