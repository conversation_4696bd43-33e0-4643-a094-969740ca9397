/**
 * Domain Integration
 *
 * This module provides integration between the Boundary Enforcer and
 * domain-specific engines (CSME, CSFE, CSDE). It enables cross-domain
 * validation and ensures that each domain operates within its own
 * bounded container.
 * 
 * Key features:
 * 1. Domain-specific adapters for CSME, CSFE, and CSDE
 * 2. Cross-domain validation
 * 3. Domain boundary detection
 * 4. Integration with the Boundary Enforcer
 */

const EventEmitter = require('events');
const { EnhancedBoundaryEnforcer } = require('./enhanced-boundary-enforcer');

/**
 * DomainIntegration class
 * 
 * Provides integration between the Boundary Enforcer and domain-specific engines.
 */
class DomainIntegration extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      enableCrossDomainValidation: true,
      enableDomainBoundaryDetection: true,
      ...options
    };

    // Create enhanced boundary enforcer
    this.boundaryEnforcer = options.boundaryEnforcer || 
      new EnhancedBoundaryEnforcer({
        enableLogging: this.options.enableLogging,
        enableRealTimeMonitoring: true
      });

    // Initialize domain adapters
    this.domainAdapters = {
      cyber: null,
      financial: null,
      medical: null
    };

    // Initialize cross-domain validation
    this.crossDomainValidation = {
      enabled: this.options.enableCrossDomainValidation,
      validationRules: this._createCrossDomainValidationRules()
    };

    // Initialize domain boundary detection
    this.domainBoundaryDetection = {
      enabled: this.options.enableDomainBoundaryDetection,
      detectionRules: this._createDomainBoundaryDetectionRules()
    };

    // Forward boundary enforcer events
    this.boundaryEnforcer.on('boundary-violation', (data) => {
      this.emit('boundary-violation', data);
    });

    this.boundaryEnforcer.on('boundary-correction', (data) => {
      this.emit('boundary-correction', data);
    });

    this.boundaryEnforcer.on('violation-threshold-exceeded', (data) => {
      this.emit('violation-threshold-exceeded', data);
    });

    if (this.options.enableLogging) {
      console.log('DomainIntegration initialized with options:', this.options);
    }
  }

  /**
   * Register a CSDE adapter
   * @param {Object} csdeAdapter - CSDE adapter
   */
  registerCSDEAdapter(csdeAdapter) {
    this.domainAdapters.cyber = csdeAdapter;
    
    if (this.options.enableLogging) {
      console.log('CSDE adapter registered');
    }
    
    this.emit('adapter-registered', { domain: 'cyber', adapter: csdeAdapter });
  }

  /**
   * Register a CSFE adapter
   * @param {Object} csfeAdapter - CSFE adapter
   */
  registerCSFEAdapter(csfeAdapter) {
    this.domainAdapters.financial = csfeAdapter;
    
    if (this.options.enableLogging) {
      console.log('CSFE adapter registered');
    }
    
    this.emit('adapter-registered', { domain: 'financial', adapter: csfeAdapter });
  }

  /**
   * Register a CSME adapter
   * @param {Object} csmeAdapter - CSME adapter
   */
  registerCSMEAdapter(csmeAdapter) {
    this.domainAdapters.medical = csmeAdapter;
    
    if (this.options.enableLogging) {
      console.log('CSME adapter registered');
    }
    
    this.emit('adapter-registered', { domain: 'medical', adapter: csmeAdapter });
  }

  /**
   * Process data through the appropriate domain adapter
   * @param {any} data - Data to process
   * @param {string} domain - Domain to process data for
   * @returns {any} - Processed data
   */
  async processData(data, domain) {
    // Detect domain if not provided
    if (!domain && this.domainBoundaryDetection.enabled) {
      domain = this._detectDomain(data);
    }
    
    // Default to universal domain if not detected
    domain = domain || 'universal';
    
    // Enforce boundaries on input data
    const enforcedData = this.boundaryEnforcer.enforceValue(data, domain);
    
    // Get domain adapter
    const adapter = this.domainAdapters[domain];
    
    if (!adapter) {
      // If no adapter is available, just return the enforced data
      return enforcedData;
    }
    
    try {
      // Process data through domain adapter
      const result = await this.boundaryEnforcer.enforceFunction(
        adapter.processData,
        [enforcedData],
        adapter,
        domain
      );
      
      // Perform cross-domain validation if enabled
      if (this.crossDomainValidation.enabled) {
        this._validateCrossDomain(result, domain);
      }
      
      return result;
    } catch (error) {
      this.emit('processing-error', { domain, error: error.message });
      
      // Return enforced data on error
      return enforcedData;
    }
  }

  /**
   * Detect the domain of data
   * @param {any} data - Data to detect domain for
   * @returns {string|null} - Detected domain or null if not detected
   * @private
   */
  _detectDomain(data) {
    if (!data || typeof data !== 'object') {
      return null;
    }
    
    const rules = this.domainBoundaryDetection.detectionRules;
    
    // Check each domain's detection rules
    for (const domain in rules) {
      if (rules[domain].detect(data)) {
        return domain;
      }
    }
    
    return null;
  }

  /**
   * Validate data across domains
   * @param {any} data - Data to validate
   * @param {string} sourceDomain - Source domain
   * @private
   */
  _validateCrossDomain(data, sourceDomain) {
    if (!data || typeof data !== 'object') {
      return;
    }
    
    const rules = this.crossDomainValidation.validationRules;
    
    // Check each domain's validation rules
    for (const targetDomain in rules) {
      if (targetDomain !== sourceDomain) {
        const validationResult = rules[targetDomain].validate(data, sourceDomain);
        
        if (!validationResult.valid) {
          this.emit('cross-domain-validation-failed', {
            sourceDomain,
            targetDomain,
            reason: validationResult.reason
          });
        }
      }
    }
  }

  /**
   * Create cross-domain validation rules
   * @returns {Object} - Cross-domain validation rules
   * @private
   */
  _createCrossDomainValidationRules() {
    return {
      cyber: {
        validate: (data, sourceDomain) => {
          // Validate data from other domains against cyber domain rules
          return { valid: true };
        }
      },
      financial: {
        validate: (data, sourceDomain) => {
          // Validate data from other domains against financial domain rules
          return { valid: true };
        }
      },
      medical: {
        validate: (data, sourceDomain) => {
          // Validate data from other domains against medical domain rules
          return { valid: true };
        }
      }
    };
  }

  /**
   * Create domain boundary detection rules
   * @returns {Object} - Domain boundary detection rules
   * @private
   */
  _createDomainBoundaryDetectionRules() {
    return {
      cyber: {
        detect: (data) => {
          // Detect if data belongs to cyber domain
          return data.securityScore !== undefined || 
                 data.threatLevel !== undefined ||
                 data.encryptionStrength !== undefined;
        }
      },
      financial: {
        detect: (data) => {
          // Detect if data belongs to financial domain
          return data.balance !== undefined || 
                 data.interestRate !== undefined ||
                 data.transactionVolume !== undefined;
        }
      },
      medical: {
        detect: (data) => {
          // Detect if data belongs to medical domain
          return data.heartRate !== undefined || 
                 data.bloodPressure !== undefined ||
                 data.temperature !== undefined;
        }
      }
    };
  }

  /**
   * Get the boundary enforcer
   * @returns {EnhancedBoundaryEnforcer} - Boundary enforcer
   */
  getBoundaryEnforcer() {
    return this.boundaryEnforcer;
  }

  /**
   * Get monitoring statistics
   * @returns {Object} - Monitoring statistics
   */
  getMonitoringStats() {
    return this.boundaryEnforcer.getMonitoringStats();
  }

  /**
   * Clean up resources
   */
  dispose() {
    this.boundaryEnforcer.dispose();
  }
}

/**
 * Create a domain adapter for CSDE
 * @param {Object} csde - CSDE instance
 * @returns {Object} - CSDE adapter
 */
function createCSDEAdapter(csde) {
  return {
    processData: async (data) => {
      // Process data through CSDE
      // This is a placeholder for actual CSDE integration
      return data;
    }
  };
}

/**
 * Create a domain adapter for CSFE
 * @param {Object} csfe - CSFE instance
 * @returns {Object} - CSFE adapter
 */
function createCSFEAdapter(csfe) {
  return {
    processData: async (data) => {
      // Process data through CSFE
      // This is a placeholder for actual CSFE integration
      return data;
    }
  };
}

/**
 * Create a domain adapter for CSME
 * @param {Object} csme - CSME instance
 * @returns {Object} - CSME adapter
 */
function createCSMEAdapter(csme) {
  return {
    processData: async (data) => {
      // Process data through CSME
      // This is a placeholder for actual CSME integration
      return data;
    }
  };
}

/**
 * Create a domain integration instance with recommended settings
 * @param {Object} options - Configuration options
 * @returns {DomainIntegration} - Configured domain integration
 */
function createDomainIntegration(options = {}) {
  return new DomainIntegration({
    enableLogging: true,
    enableCrossDomainValidation: true,
    enableDomainBoundaryDetection: true,
    ...options
  });
}

module.exports = {
  DomainIntegration,
  createDomainIntegration,
  createCSDEAdapter,
  createCSFEAdapter,
  createCSMEAdapter
};
