<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partner Empowerment Flywheel Diagram</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagram-container {
            position: relative;
            width: 800px;
            height: 650px;
            margin: 0 auto;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        .container-box {
            position: absolute;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 30px 10px 10px;
            box-sizing: border-box;
        }
        .container-label {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }
        .component-box {
            position: absolute;
            background-color: white;
            border: 2px solid #333;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 1.2;
            z-index: 1;
        }
        .dashed-box {
            border: 1px dashed #333;
        }
        .component-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 20px;
            height: 20px;
            background-color: #555555; /* Changed from blue to grey for patent compliance */
            color: white;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        .component-label {
            font-weight: bold;
            margin-bottom: 4px;
        }
        .legend {
            position: absolute;
            right: 10px;
            bottom: 10px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            z-index: 10;
            width: 200px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border: 1px solid #333;
        }
        .inventor-label {
            position: absolute;
            left: 10px;
            bottom: 10px;
            font-size: 12px;
            font-style: italic;
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>FIG. 3: Partner Empowerment Flywheel</h1>
    
    <div class="diagram-container">
        <!-- Main Container -->
        <div class="container-box" style="width: 750px; height: 600px; left: 25px; top: 20px;">
            <div class="container-label" style="font-size: 18px;">PARTNER EMPOWERMENT FLYWHEEL</div>
        </div>

        <!-- Center Formula -->
        <div class="component-box" style="left: 325px; top: 270px; width: 150px; height: 60px;">
            <div class="component-number">301</div>
            <div class="component-label" style="font-size: 16px;">Growth Formula</div>
            <span style="font-size: 14px; font-weight: bold; color: #555555;">(0.82 × 2)^n</span>
        </div>

        <!-- Flywheel Circle -->
        <svg width="800" height="600" style="position: absolute; top: 0; left: 0; z-index: 0;">
            <circle
                cx="400"
                cy="300"
                r="180"
                fill="none"
                stroke="#333"
                stroke-width="2"
            />

            <!-- Arrows around the circle -->
            <path
                d="M 580,300 A 180,180 0 0,1 490,460"
                fill="none"
                stroke="#333"
                stroke-width="2"
                marker-end="url(#arrowhead)"
            />

            <path
                d="M 490,460 A 180,180 0 0,1 310,460"
                fill="none"
                stroke="#333"
                stroke-width="2"
                marker-end="url(#arrowhead)"
            />

            <path
                d="M 310,460 A 180,180 0 0,1 220,300"
                fill="none"
                stroke="#333"
                stroke-width="2"
                marker-end="url(#arrowhead)"
            />

            <path
                d="M 220,300 A 180,180 0 0,1 310,140"
                fill="none"
                stroke="#333"
                stroke-width="2"
                marker-end="url(#arrowhead)"
            />

            <path
                d="M 310,140 A 180,180 0 0,1 490,140"
                fill="none"
                stroke="#333"
                stroke-width="2"
                marker-end="url(#arrowhead)"
            />

            <path
                d="M 490,140 A 180,180 0 0,1 580,300"
                fill="none"
                stroke="#333"
                stroke-width="2"
                marker-end="url(#arrowhead)"
            />

            <!-- Arrow definitions -->
            <defs>
                <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="0"
                    refY="3.5"
                    orient="auto"
                >
                    <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
                </marker>
            </defs>
        </svg>

        <!-- Flywheel Components -->
        <div class="component-box" style="left: 550px; top: 270px; width: 120px; height: 60px;">
            <div class="component-number">302</div>
            <div class="component-label" style="font-size: 14px;">Partner Revenue</div>
            <span style="font-size: 12px; font-weight: bold; color: #555555;">82% Share</span>
        </div>

        <div class="component-box" style="left: 470px; top: 430px; width: 120px; height: 60px;">
            <div class="component-number">303</div>
            <div class="component-label" style="font-size: 14px;">Partner Growth</div>
            <span style="font-size: 12px;">Ecosystem Expansion</span>
        </div>

        <div class="component-box" style="left: 210px; top: 430px; width: 120px; height: 60px;">
            <div class="component-number">304</div>
            <div class="component-label" style="font-size: 14px;">Customer Value</div>
            <span style="font-size: 12px; font-weight: bold; color: #555555;">82% Cost Reduction</span>
        </div>

        <div class="component-box" style="left: 130px; top: 270px; width: 120px; height: 60px;">
            <div class="component-number">305</div>
            <div class="component-label" style="font-size: 14px;">Market Adoption</div>
            <span style="font-size: 12px;">Accelerated Uptake</span>
        </div>

        <div class="component-box" style="left: 210px; top: 110px; width: 120px; height: 60px;">
            <div class="component-number">306</div>
            <div class="component-label" style="font-size: 14px;">Platform Growth</div>
            <span style="font-size: 12px;">Compounding Value</span>
        </div>

        <div class="component-box" style="left: 470px; top: 110px; width: 120px; height: 60px;">
            <div class="component-number">307</div>
            <div class="component-label" style="font-size: 14px;">Innovation</div>
            <span style="font-size: 12px;">Ecosystem Creativity</span>
        </div>

        <!-- Traditional Model Comparison -->
        <div class="container-box" style="width: 700px; height: 100px; left: 50px; top: 500px;">
            <div class="container-label" style="font-size: 16px;">TRADITIONAL VS. PARTNER EMPOWERMENT MODEL</div>
        </div>

        <div class="component-box dashed-box" style="left: 100px; top: 530px; width: 250px; height: 50px;">
            <div class="component-number">308</div>
            <div class="component-label" style="font-size: 14px;">Traditional Model</div>
            <span style="font-size: 12px;">Zero-Sum: Partners compete for limited value</span>
        </div>

        <div class="component-box" style="left: 450px; top: 530px; width: 250px; height: 50px;">
            <div class="component-number">309</div>
            <div class="component-label" style="font-size: 14px;">Partner Empowerment Model</div>
            <span style="font-size: 12px; font-weight: bold; color: #555555;">Exponential: (0.82 × 2)^n value creation</span>
        </div>

        <!-- Legend -->
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff;"></div>
                <div>Flywheel Components</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff; border: 1px dashed #333;"></div>
                <div>Traditional Model (Comparison)</div>
            </div>
        </div>

        <div class="inventor-label">Inventor: David Nigel Irvin</div>
    </div>
</body>
</html>
