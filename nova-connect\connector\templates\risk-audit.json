{"id": "risk-audit", "name": "Risk & Audit Connector", "description": "Connector for risk management and audit systems", "version": "1.0.0", "category": "risk", "icon": "risk-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/risk", "supportEmail": "<EMAIL>", "authentication": {"type": "api_key", "fields": {"apiKey": {"type": "string", "label": "API Key", "required": true, "sensitive": true, "description": "API Key for authentication"}, "apiKeyHeader": {"type": "string", "label": "API Key Header", "required": true, "sensitive": false, "default": "X-API-Key", "description": "HTTP header name for the API Key"}}}, "endpoints": [{"id": "listRisks", "name": "List Risks", "description": "List all risks", "method": "GET", "url": "https://api.example.com/risks", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "category": {"type": "string", "label": "Category", "required": false, "description": "Filter by risk category"}, "severity": {"type": "string", "label": "Severity", "required": false, "enum": ["critical", "high", "medium", "low"], "description": "Filter by risk severity"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["open", "mitigated", "accepted", "transferred", "closed"], "description": "Filter by risk status"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "category": {"type": "string", "description": "Filter by risk category"}, "severity": {"type": "string", "description": "Filter by risk severity", "enum": ["critical", "high", "medium", "low"]}, "status": {"type": "string", "description": "Filter by risk status", "enum": ["open", "mitigated", "accepted", "transferred", "closed"]}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Risk ID"}, "title": {"type": "string", "description": "Risk title"}, "description": {"type": "string", "description": "Risk description"}, "category": {"type": "string", "description": "Risk category"}, "severity": {"type": "string", "description": "Risk severity", "enum": ["critical", "high", "medium", "low"]}, "status": {"type": "string", "description": "Risk status", "enum": ["open", "mitigated", "accepted", "transferred", "closed"]}, "likelihood": {"type": "number", "description": "Risk likelihood score (1-5)"}, "impact": {"type": "number", "description": "Risk impact score (1-5)"}, "riskScore": {"type": "number", "description": "Overall risk score"}, "createdAt": {"type": "string", "format": "date-time", "description": "Risk creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Risk last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getRisk", "name": "Get Risk", "description": "Get a specific risk", "method": "GET", "url": "https://api.example.com/risks/{riskId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"riskId": {"type": "string", "label": "Risk ID", "required": true, "description": "ID of the risk to retrieve"}}, "inputSchema": {"type": "object", "properties": {"riskId": {"type": "string", "description": "ID of the risk to retrieve"}}, "required": ["riskId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Risk ID"}, "title": {"type": "string", "description": "Risk title"}, "description": {"type": "string", "description": "Risk description"}, "category": {"type": "string", "description": "Risk category"}, "severity": {"type": "string", "description": "Risk severity", "enum": ["critical", "high", "medium", "low"]}, "status": {"type": "string", "description": "Risk status", "enum": ["open", "mitigated", "accepted", "transferred", "closed"]}, "likelihood": {"type": "number", "description": "Risk likelihood score (1-5)"}, "impact": {"type": "number", "description": "Risk impact score (1-5)"}, "riskScore": {"type": "number", "description": "Overall risk score"}, "owner": {"type": "string", "description": "Risk owner"}, "mitigationPlan": {"type": "string", "description": "Risk mitigation plan"}, "mitigationStatus": {"type": "string", "description": "Mitigation status", "enum": ["not_started", "in_progress", "completed", "not_applicable"]}, "relatedControls": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Control ID"}, "name": {"type": "string", "description": "Control name"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Risk creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Risk last update date"}}}}, {"id": "createRisk", "name": "Create Risk", "description": "Create a new risk", "method": "POST", "url": "https://api.example.com/risks", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "bodyParameters": {"title": {"type": "string", "label": "Title", "required": true, "description": "Risk title"}, "description": {"type": "string", "label": "Description", "required": true, "description": "Risk description"}, "category": {"type": "string", "label": "Category", "required": true, "description": "Risk category"}, "severity": {"type": "string", "label": "Severity", "required": true, "enum": ["critical", "high", "medium", "low"], "description": "Risk severity"}, "likelihood": {"type": "number", "label": "Likelihood", "required": true, "description": "Risk likelihood score (1-5)"}, "impact": {"type": "number", "label": "Impact", "required": true, "description": "Risk impact score (1-5)"}, "owner": {"type": "string", "label": "Owner", "required": false, "description": "Risk owner"}, "mitigationPlan": {"type": "string", "label": "Mitigation Plan", "required": false, "description": "Risk mitigation plan"}}, "inputSchema": {"type": "object", "properties": {"title": {"type": "string", "description": "Risk title"}, "description": {"type": "string", "description": "Risk description"}, "category": {"type": "string", "description": "Risk category"}, "severity": {"type": "string", "description": "Risk severity", "enum": ["critical", "high", "medium", "low"]}, "likelihood": {"type": "number", "description": "Risk likelihood score (1-5)"}, "impact": {"type": "number", "description": "Risk impact score (1-5)"}, "owner": {"type": "string", "description": "Risk owner"}, "mitigationPlan": {"type": "string", "description": "Risk mitigation plan"}}, "required": ["title", "description", "category", "severity", "likelihood", "impact"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Risk ID"}, "title": {"type": "string", "description": "Risk title"}, "description": {"type": "string", "description": "Risk description"}, "category": {"type": "string", "description": "Risk category"}, "severity": {"type": "string", "description": "Risk severity", "enum": ["critical", "high", "medium", "low"]}, "status": {"type": "string", "description": "Risk status", "enum": ["open", "mitigated", "accepted", "transferred", "closed"]}, "likelihood": {"type": "number", "description": "Risk likelihood score (1-5)"}, "impact": {"type": "number", "description": "Risk impact score (1-5)"}, "riskScore": {"type": "number", "description": "Overall risk score"}, "owner": {"type": "string", "description": "Risk owner"}, "mitigationPlan": {"type": "string", "description": "Risk mitigation plan"}, "createdAt": {"type": "string", "format": "date-time", "description": "Risk creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Risk last update date"}}}}, {"id": "updateRiskStatus", "name": "Update Risk Status", "description": "Update the status of a risk", "method": "PATCH", "url": "https://api.example.com/risks/{riskId}/status", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"riskId": {"type": "string", "label": "Risk ID", "required": true, "description": "ID of the risk to update"}}, "bodyParameters": {"status": {"type": "string", "label": "Status", "required": true, "enum": ["open", "mitigated", "accepted", "transferred", "closed"], "description": "New risk status"}, "comment": {"type": "string", "label": "Comment", "required": false, "description": "Comment about the status update"}}, "inputSchema": {"type": "object", "properties": {"riskId": {"type": "string", "description": "ID of the risk to update"}, "status": {"type": "string", "description": "New risk status", "enum": ["open", "mitigated", "accepted", "transferred", "closed"]}, "comment": {"type": "string", "description": "Comment about the status update"}}, "required": ["riskId", "status"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Risk ID"}, "status": {"type": "string", "description": "Updated risk status", "enum": ["open", "mitigated", "accepted", "transferred", "closed"]}, "updatedAt": {"type": "string", "format": "date-time", "description": "Risk last update date"}}}}]}