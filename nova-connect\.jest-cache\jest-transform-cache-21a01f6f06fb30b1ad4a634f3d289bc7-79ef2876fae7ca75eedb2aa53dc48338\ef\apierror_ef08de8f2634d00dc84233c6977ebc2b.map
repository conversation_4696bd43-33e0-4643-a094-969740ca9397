{"version": 3, "names": ["UAConnectorError", "require", "ApiError", "constructor", "message", "options", "code", "severity", "context", "cause", "statusCode", "response", "getUserMessage", "toJSON", "includeStack", "json", "RateLimitExceededError", "retryAfter", "ResourceNotFoundError", "resourceType", "resourceId", "toLowerCase", "BadRequestError", "ServerError", "module", "exports"], "sources": ["api-error.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector - API Error\n * \n * This module defines API-related errors for the UAC.\n */\n\nconst UAConnectorError = require('./base-error');\n\n/**\n * Error class for API failures\n * @class ApiError\n * @extends UAConnectorError\n */\nclass ApiError extends UAConnectorError {\n  /**\n   * Create a new ApiError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {string} options.code - Error code\n   * @param {string} options.severity - Error severity\n   * @param {Object} options.context - Additional context for the error\n   * @param {Error} options.cause - The error that caused this error\n   * @param {number} options.statusCode - HTTP status code\n   * @param {Object} options.response - API response data\n   */\n  constructor(message, options = {}) {\n    super(message, {\n      code: options.code || 'API_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause\n    });\n    \n    this.statusCode = options.statusCode;\n    this.response = options.response;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred while communicating with the external service. Please try again later.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    \n    if (this.statusCode) {\n      json.statusCode = this.statusCode;\n    }\n    \n    if (this.response) {\n      json.response = this.response;\n    }\n    \n    return json;\n  }\n}\n\n/**\n * Error class for rate limit exceeded errors\n * @class RateLimitExceededError\n * @extends ApiError\n */\nclass RateLimitExceededError extends ApiError {\n  /**\n   * Create a new RateLimitExceededError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   * @param {number} options.retryAfter - Seconds to wait before retrying\n   */\n  constructor(message = 'Rate limit exceeded', options = {}) {\n    super(message, {\n      code: options.code || 'API_RATE_LIMIT_EXCEEDED',\n      severity: options.severity || 'warning',\n      context: options.context || {},\n      cause: options.cause,\n      statusCode: options.statusCode || 429,\n      response: options.response\n    });\n    \n    this.retryAfter = options.retryAfter;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    if (this.retryAfter) {\n      return `Rate limit exceeded. Please try again after ${this.retryAfter} seconds.`;\n    }\n    return 'Rate limit exceeded. Please try again later.';\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    \n    if (this.retryAfter) {\n      json.retryAfter = this.retryAfter;\n    }\n    \n    return json;\n  }\n}\n\n/**\n * Error class for resource not found errors\n * @class ResourceNotFoundError\n * @extends ApiError\n */\nclass ResourceNotFoundError extends ApiError {\n  /**\n   * Create a new ResourceNotFoundError\n   * \n   * @param {string} resourceType - The type of resource\n   * @param {string} resourceId - The ID of the resource\n   * @param {Object} options - Error options\n   */\n  constructor(resourceType, resourceId, options = {}) {\n    const message = `${resourceType} not found with ID: ${resourceId}`;\n    \n    super(message, {\n      code: options.code || 'API_RESOURCE_NOT_FOUND',\n      severity: options.severity || 'error',\n      context: { ...options.context, resourceType, resourceId },\n      cause: options.cause,\n      statusCode: options.statusCode || 404,\n      response: options.response\n    });\n    \n    this.resourceType = resourceType;\n    this.resourceId = resourceId;\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return `The requested ${this.resourceType.toLowerCase()} could not be found.`;\n  }\n\n  /**\n   * Convert the error to a JSON object\n   * \n   * @param {boolean} includeStack - Whether to include the stack trace\n   * @returns {Object} - JSON representation of the error\n   */\n  toJSON(includeStack = false) {\n    const json = super.toJSON(includeStack);\n    json.resourceType = this.resourceType;\n    json.resourceId = this.resourceId;\n    return json;\n  }\n}\n\n/**\n * Error class for bad request errors\n * @class BadRequestError\n * @extends ApiError\n */\nclass BadRequestError extends ApiError {\n  /**\n   * Create a new BadRequestError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'Bad request', options = {}) {\n    super(message, {\n      code: options.code || 'API_BAD_REQUEST',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause,\n      statusCode: options.statusCode || 400,\n      response: options.response\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'The request was invalid. Please check your input and try again.';\n  }\n}\n\n/**\n * Error class for server errors\n * @class ServerError\n * @extends ApiError\n */\nclass ServerError extends ApiError {\n  /**\n   * Create a new ServerError\n   * \n   * @param {string} message - Error message\n   * @param {Object} options - Error options\n   */\n  constructor(message = 'Server error', options = {}) {\n    super(message, {\n      code: options.code || 'API_SERVER_ERROR',\n      severity: options.severity || 'error',\n      context: options.context || {},\n      cause: options.cause,\n      statusCode: options.statusCode || 500,\n      response: options.response\n    });\n  }\n\n  /**\n   * Get a user-friendly error message\n   * \n   * @returns {string} - User-friendly error message\n   */\n  getUserMessage() {\n    return 'An error occurred on the server. Please try again later or contact support if the issue persists.';\n  }\n}\n\nmodule.exports = {\n  ApiError,\n  RateLimitExceededError,\n  ResourceNotFoundError,\n  BadRequestError,\n  ServerError\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,gBAAgB,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,SAASF,gBAAgB,CAAC;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACC,OAAO,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,WAAW;MACjCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI;IACjB,CAAC,CAAC;IAEF,IAAI,CAACC,UAAU,GAAGL,OAAO,CAACK,UAAU;IACpC,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACM,QAAQ;EAClC;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,0FAA0F;EACnG;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEC,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IAEvC,IAAI,IAAI,CAACJ,UAAU,EAAE;MACnBK,IAAI,CAACL,UAAU,GAAG,IAAI,CAACA,UAAU;IACnC;IAEA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjBI,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC/B;IAEA,OAAOI,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,SAASd,QAAQ,CAAC;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,qBAAqB,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACzD,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,yBAAyB;MAC/CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,SAAS;MACvCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,UAAU,EAAEL,OAAO,CAACK,UAAU,IAAI,GAAG;MACrCC,QAAQ,EAAEN,OAAO,CAACM;IACpB,CAAC,CAAC;IAEF,IAAI,CAACM,UAAU,GAAGZ,OAAO,CAACY,UAAU;EACtC;;EAEA;AACF;AACA;AACA;AACA;EACEL,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACK,UAAU,EAAE;MACnB,OAAO,+CAA+C,IAAI,CAACA,UAAU,WAAW;IAClF;IACA,OAAO,8CAA8C;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACEJ,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IAEvC,IAAI,IAAI,CAACG,UAAU,EAAE;MACnBF,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU;IACnC;IAEA,OAAOF,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,qBAAqB,SAAShB,QAAQ,CAAC;EAC3C;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACgB,YAAY,EAAEC,UAAU,EAAEf,OAAO,GAAG,CAAC,CAAC,EAAE;IAClD,MAAMD,OAAO,GAAG,GAAGe,YAAY,uBAAuBC,UAAU,EAAE;IAElE,KAAK,CAAChB,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,wBAAwB;MAC9CC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAE;QAAE,GAAGH,OAAO,CAACG,OAAO;QAAEW,YAAY;QAAEC;MAAW,CAAC;MACzDX,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,UAAU,EAAEL,OAAO,CAACK,UAAU,IAAI,GAAG;MACrCC,QAAQ,EAAEN,OAAO,CAACM;IACpB,CAAC,CAAC;IAEF,IAAI,CAACQ,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;;EAEA;AACF;AACA;AACA;AACA;EACER,cAAcA,CAAA,EAAG;IACf,OAAO,iBAAiB,IAAI,CAACO,YAAY,CAACE,WAAW,CAAC,CAAC,sBAAsB;EAC/E;;EAEA;AACF;AACA;AACA;AACA;AACA;EACER,MAAMA,CAACC,YAAY,GAAG,KAAK,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,YAAY,CAAC;IACvCC,IAAI,CAACI,YAAY,GAAG,IAAI,CAACA,YAAY;IACrCJ,IAAI,CAACK,UAAU,GAAG,IAAI,CAACA,UAAU;IACjC,OAAOL,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMO,eAAe,SAASpB,QAAQ,CAAC;EACrC;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,aAAa,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACjD,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,iBAAiB;MACvCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,UAAU,EAAEL,OAAO,CAACK,UAAU,IAAI,GAAG;MACrCC,QAAQ,EAAEN,OAAO,CAACM;IACpB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,iEAAiE;EAC1E;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMW,WAAW,SAASrB,QAAQ,CAAC;EACjC;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,GAAG,cAAc,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClD,KAAK,CAACD,OAAO,EAAE;MACbE,IAAI,EAAED,OAAO,CAACC,IAAI,IAAI,kBAAkB;MACxCC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI,OAAO;MACrCC,OAAO,EAAEH,OAAO,CAACG,OAAO,IAAI,CAAC,CAAC;MAC9BC,KAAK,EAAEJ,OAAO,CAACI,KAAK;MACpBC,UAAU,EAAEL,OAAO,CAACK,UAAU,IAAI,GAAG;MACrCC,QAAQ,EAAEN,OAAO,CAACM;IACpB,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAAA,EAAG;IACf,OAAO,mGAAmG;EAC5G;AACF;AAEAY,MAAM,CAACC,OAAO,GAAG;EACfvB,QAAQ;EACRc,sBAAsB;EACtBE,qBAAqB;EACrBI,eAAe;EACfC;AACF,CAAC", "ignoreList": []}