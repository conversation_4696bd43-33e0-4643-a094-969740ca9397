/**
 * Button Component
 * 
 * A reusable button component with different variants and sizes.
 */

import React from 'react';

type ButtonVariant = 'default' | 'primary' | 'outline' | 'link' | 'destructive';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'default',
  size = 'md',
  className = '',
  children,
  ...props
}) => {
  // Define variant styles
  const getVariantClasses = (): string => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
      case 'outline':
        return 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500';
      case 'link':
        return 'bg-transparent text-blue-600 hover:underline focus:ring-blue-500 p-0';
      case 'destructive':
        return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';
      case 'default':
      default:
        return 'bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-500';
    }
  };

  // Define size styles
  const getSizeClasses = (): string => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2.5 py-1.5 rounded';
      case 'lg':
        return 'text-base px-6 py-3 rounded-md';
      case 'md':
      default:
        return 'text-sm px-4 py-2 rounded-md';
    }
  };

  const variantClasses = getVariantClasses();
  const sizeClasses = getSizeClasses();
  
  // Don't apply padding and rounded corners to link variant
  const baseClasses = variant === 'link' 
    ? 'inline-flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors'
    : 'inline-flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';

  return (
    <button
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
