<!DOCTYPE html>
<html>
<head>
    <title>Mermaid to SVG Converter</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { display: flex; }
        #input { width: 50%; padding: 10px; }
        #output { width: 50%; padding: 10px; }
        textarea { width: 100%; height: 300px; }
        button { margin: 10px 0; padding: 10px; }
    </style>
</head>
<body>
    <h1>Mermaid to SVG Converter</h1>
    <div class="container">
        <div id="input">
            <h2>Input Mermaid Code</h2>
            <textarea id="mermaidCode" spellcheck="false">graph TD
    A[Start] --> B{Is it?}
    B -->|Yes| C[OK]
    C --> D[Rethink]
    D --> B
    B -->|No| E[End]</textarea>
            <div>
                <button onclick="renderMermaid()">Render</button>
                <button onclick="downloadSVG()">Download SVG</button>
            </div>
        </div>
        <div id="output">
            <h2>Output SVG</h2>
            <div id="mermaidDiagram"></div>
        </div>
    </div>

    <script>
        mermaid.initialize({ startOnLoad: false });
        let currentSvg = '';

        async function renderMermaid() {
            const code = document.getElementById('mermaidCode').value;
            const container = document.getElementById('mermaidDiagram');
            
            try {
                const { svg } = await mermaid.render('mermaid-svg', code);
                container.innerHTML = svg;
                currentSvg = svg;
            } catch (error) {
                container.innerHTML = `<div style="color: red">Error: ${error.message}</div>`;
                console.error(error);
            }
        }

        function downloadSVG() {
            if (!currentSvg) {
                alert('Please render a diagram first');
                return;
            }
            
            const blob = new Blob([currentSvg], { type: 'image/svg+xml' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'diagram.svg';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Load the first Mermaid file by default
        async function loadFirstMermaidFile() {
            try {
                const response = await fetch('FIG1_uuft_core_architecture.mmd');
                if (response.ok) {
                    const content = await response.text();
                    document.getElementById('mermaidCode').value = content;
                    renderMermaid();
                }
            } catch (error) {
                console.error('Error loading Mermaid file:', error);
            }
        }

        // Load first file when page loads
        window.onload = loadFirstMermaidFile;
    </script>
</body>
</html>
