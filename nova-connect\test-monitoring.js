/**
 * Simple test script for the monitoring components
 */

try {
  console.log('Loading monitoring components...');
  
  // Load metrics service
  const metricsService = require('./src/monitoring/metrics-service');
  console.log('Metrics service loaded successfully');
  
  // Load health service
  const healthService = require('./src/monitoring/health-service');
  console.log('Health service loaded successfully');
  
  // Load tracing service
  const tracingService = require('./src/monitoring/tracing-service');
  console.log('Tracing service loaded successfully');
  
  // Load dashboard service
  const dashboardService = require('./src/monitoring/dashboard-service');
  console.log('Dashboard service loaded successfully');
  
  // Test metrics service
  console.log('\n--- Testing Metrics Service ---');
  
  // Register some metrics
  const requestCounter = metricsService.registerCounter(
    'test_requests_total',
    'Total number of test requests',
    ['method', 'path']
  );
  
  const responseTime = metricsService.registerHistogram(
    'test_response_time_seconds',
    'Test response time in seconds',
    ['method', 'path']
  );
  
  const activeUsers = metricsService.registerGauge(
    'test_active_users',
    'Number of active test users'
  );
  
  // Record some metrics
  requestCounter.inc(1, { method: 'GET', path: '/test' });
  requestCounter.inc(1, { method: 'POST', path: '/test' });
  requestCounter.inc(1, { method: 'GET', path: '/test' });
  
  responseTime.observe(0.1, { method: 'GET', path: '/test' });
  responseTime.observe(0.2, { method: 'POST', path: '/test' });
  responseTime.observe(0.15, { method: 'GET', path: '/test' });
  
  activeUsers.set(5);
  
  // Get metrics in Prometheus format
  const prometheusMetrics = metricsService.getMetricsAsPrometheusFormat();
  console.log('Prometheus metrics:');
  console.log(prometheusMetrics);
  
  // Test health service
  console.log('\n--- Testing Health Service ---');
  
  // Run health checks
  healthService.runChecks().then(health => {
    console.log('Health check results:');
    console.log(JSON.stringify(health, null, 2));
    
    // Test tracing service
    console.log('\n--- Testing Tracing Service ---');
    
    // Start a trace
    const trace = tracingService.startTrace('test-trace', {
      attributes: {
        'test.attribute': 'test-value'
      }
    });
    
    // Add an event
    trace.addEvent('test-event', {
      'event.attribute': 'event-value'
    });
    
    // End the trace
    tracingService.endTrace(trace);
    
    console.log('Trace created:');
    console.log(JSON.stringify(trace.toJSON(), null, 2));
    
    // Test dashboard service
    console.log('\n--- Testing Dashboard Service ---');
    
    // Start the dashboard service
    dashboardService.start(1000);
    
    // Wait for the dashboard to update
    setTimeout(() => {
      // Get dashboard data
      const dashboard = dashboardService.getDashboardData();
      console.log('Dashboard data:');
      console.log(JSON.stringify(dashboard, null, 2));
      
      // Stop the dashboard service
      dashboardService.stop();
      
      console.log('\nAll monitoring components tested successfully!');
    }, 1500);
  });
} catch (error) {
  console.error('Test failed:', error);
}
