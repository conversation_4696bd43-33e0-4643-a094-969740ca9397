import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Button, 
  Container, 
  Grid, 
  Paper, 
  Slider, 
  Typography 
} from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';

// Import Comphyon components
import ComphyonGauge from '../components/ComphyonGauge';
import ComphyonDashboard from '../components/ComphyonDashboard';

// Import ComphyonMeter
const { ComphyonMeter } = require('../exports');

/**
 * Mock CSDE Engine for demo purposes
 */
class MockCSDEEngine {
  constructor() {
    this.performanceFactor = 3142;
    this.predictionRate = 314.2;
    this.multiplier = 1.0;
  }
  
  getPredictionRate() {
    // Simulate some variation in the prediction rate
    const variation = Math.sin(Date.now() / 1000) * 50;
    return (this.predictionRate + variation) * this.multiplier;
  }
  
  setMultiplier(value) {
    this.multiplier = value;
  }
}

/**
 * Mock CSFE Engine for demo purposes
 */
class MockCSFEEngine {
  constructor() {
    this.performanceFactor = 3142;
    this.predictionRate = 251.36;
    this.multiplier = 1.0;
  }
  
  getPredictionRate() {
    // Simulate some variation in the prediction rate
    const variation = Math.cos(Date.now() / 1000) * 40;
    return (this.predictionRate + variation) * this.multiplier;
  }
  
  setMultiplier(value) {
    this.multiplier = value;
  }
}

/**
 * Mock CSME Engine for demo purposes
 */
class MockCSMEEngine {
  constructor() {
    this.ethicalScore = 0.82;
  }
  
  getEthicalScore() {
    // Simulate some variation in the ethical score
    const variation = (Math.sin(Date.now() / 5000) * 0.1);
    return this.ethicalScore + variation;
  }
  
  setEthicalScore(value) {
    this.ethicalScore = value;
  }
}

/**
 * ComphyonDemo Component
 * 
 * Demonstrates the use of Comphyon components in a React application.
 */
const ComphyonDemo = () => {
  // State for the ComphyonMeter
  const [comphyonMeter, setComphyonMeter] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  
  // State for engine controls
  const [csdeMultiplier, setCsdeMultiplier] = useState(1.0);
  const [csfeMultiplier, setCsfeMultiplier] = useState(1.0);
  const [csmeScore, setCsmeScore] = useState(0.82);
  
  // References to engines
  const csdeEngineRef = React.useRef(new MockCSDEEngine());
  const csfeEngineRef = React.useRef(new MockCSFEEngine());
  const csmeEngineRef = React.useRef(new MockCSMEEngine());
  
  // Initialize ComphyonMeter
  useEffect(() => {
    const csdeEngine = csdeEngineRef.current;
    const csfeEngine = csfeEngineRef.current;
    const csmeEngine = csmeEngineRef.current;
    
    const meter = new ComphyonMeter({
      csdeEngine,
      csfeEngine,
      csmeEngine,
      enableLogging: false,
      updateInterval: 1000
    });
    
    setComphyonMeter(meter);
    
    // Clean up on unmount
    return () => {
      if (meter && meter.state.isRunning) {
        meter.stop();
      }
    };
  }, []);
  
  // Handle start/stop
  const handleStartStop = () => {
    if (!comphyonMeter) return;
    
    if (isRunning) {
      comphyonMeter.stop();
      setIsRunning(false);
    } else {
      comphyonMeter.start();
      setIsRunning(true);
    }
  };
  
  // Handle CSDE multiplier change
  const handleCsdeMultiplierChange = (event, value) => {
    setCsdeMultiplier(value);
    csdeEngineRef.current.setMultiplier(value);
  };
  
  // Handle CSFE multiplier change
  const handleCsfeMultiplierChange = (event, value) => {
    setCsfeMultiplier(value);
    csfeEngineRef.current.setMultiplier(value);
  };
  
  // Handle CSME score change
  const handleCsmeScoreChange = (event, value) => {
    setCsmeScore(value);
    csmeEngineRef.current.setEthicalScore(value);
  };
  
  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" gutterBottom>
        Comphyon Measurement System
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Real-time measurement of emergent intelligence in the NovaFuse platform
      </Typography>
      
      <Box sx={{ mb: 4 }}>
        <Button
          variant="contained"
          color={isRunning ? "error" : "success"}
          startIcon={isRunning ? <StopIcon /> : <PlayArrowIcon />}
          onClick={handleStartStop}
          disabled={!comphyonMeter}
          sx={{ mr: 2 }}
        >
          {isRunning ? "Stop" : "Start"}
        </Button>
        
        <Typography variant="body2" color="text.secondary" component="span">
          {isRunning ? "Comphyon meter is running" : "Comphyon meter is stopped"}
        </Typography>
      </Box>
      
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              CSDE Engine
            </Typography>
            <Typography variant="body2" gutterBottom>
              Prediction Rate Multiplier: {csdeMultiplier.toFixed(2)}x
            </Typography>
            <Slider
              value={csdeMultiplier}
              onChange={handleCsdeMultiplierChange}
              min={0.1}
              max={3.0}
              step={0.1}
              marks={[
                { value: 0.1, label: '0.1x' },
                { value: 1.0, label: '1.0x' },
                { value: 2.0, label: '2.0x' },
                { value: 3.0, label: '3.0x' }
              ]}
              valueLabelDisplay="auto"
            />
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              CSFE Engine
            </Typography>
            <Typography variant="body2" gutterBottom>
              Prediction Rate Multiplier: {csfeMultiplier.toFixed(2)}x
            </Typography>
            <Slider
              value={csfeMultiplier}
              onChange={handleCsfeMultiplierChange}
              min={0.1}
              max={3.0}
              step={0.1}
              marks={[
                { value: 0.1, label: '0.1x' },
                { value: 1.0, label: '1.0x' },
                { value: 2.0, label: '2.0x' },
                { value: 3.0, label: '3.0x' }
              ]}
              valueLabelDisplay="auto"
            />
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              CSME Engine
            </Typography>
            <Typography variant="body2" gutterBottom>
              Ethical Score: {csmeScore.toFixed(2)}
            </Typography>
            <Slider
              value={csmeScore}
              onChange={handleCsmeScoreChange}
              min={0.1}
              max={1.0}
              step={0.01}
              marks={[
                { value: 0.1, label: '0.1' },
                { value: 0.5, label: '0.5' },
                { value: 0.82, label: '0.82' },
                { value: 1.0, label: '1.0' }
              ]}
              valueLabelDisplay="auto"
            />
          </Paper>
        </Grid>
      </Grid>
      
      {comphyonMeter && (
        <ComphyonDashboard 
          comphyonMeter={comphyonMeter} 
          autoRefresh={true} 
          refreshInterval={1000} 
        />
      )}
    </Container>
  );
};

export default ComphyonDemo;
