const fs = require('fs');
const path = require('path');

async function setupClickBank() {
  try {
    // Check if .env file exists
    const envPath = path.join(__dirname, '../.env');
    const envExamplePath = path.join(__dirname, '../.env.example');
    
    // If .env doesn't exist, create it from example
    if (!fs.existsSync(envPath)) {
      if (!fs.existsSync(envExamplePath)) {
        throw new Error('No .env.example file found!');
      }
      
      console.log('Creating .env file from example...');
      fs.copyFileSync(envExamplePath, envPath);
    }

    // Read current .env
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envLines = envContent.split('\n');

    // Get ClickBank credentials
    console.log('\n🔑 Please enter your ClickBank credentials:');
    
    const username = await askForInput('ClickBank Username: ');
    const password = await askForInput('ClickBank Password: ', true);
    
    // Update .env file
    const updatedLines = envLines.map(line => {
      if (line.startsWith('CLICKBANK_USERNAME=')) {
        return `CLICKBANK_USERNAME=${username}`;
      }
      if (line.startsWith('CLICKBANK_PASSWORD=')) {
        return `CLICKBANK_PASSWORD=${password}`;
      }
      return line;
    });

    // Write updated .env
    fs.writeFileSync(envPath, updatedLines.join('\n'));
    
    console.log('\n✅ ClickBank credentials have been successfully set up!');
    console.log('\nYou can now run the product discovery script:');
    console.log('node scripts/discover-products.js');

  } catch (error) {
    console.error('❌ Error setting up ClickBank:', error.message);
  }
}

// Helper function to ask for input
async function askForInput(prompt, isPassword = false) {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(prompt, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

// Run setup
setupClickBank();
