/**
 * Integrations Module
 * 
 * This module provides functionality for integrating with external systems.
 */

const salesforceIntegration = require('./salesforce');
const microsoftIntegration = require('./microsoft');
const googleIntegration = require('./google');
const awsIntegration = require('./aws');
const slackIntegration = require('./slack');

/**
 * Get all available integrations
 * @returns {Array} - Array of available integrations
 */
const getAvailableIntegrations = () => {
  return [
    {
      id: 'salesforce',
      name: 'Salesforce',
      description: 'Integration with Salesforce CRM',
      status: 'active',
      version: '1.0.0',
      capabilities: ['data-export', 'data-deletion', 'data-update']
    },
    {
      id: 'microsoft',
      name: 'Microsoft 365',
      description: 'Integration with Microsoft 365 services',
      status: 'active',
      version: '1.0.0',
      capabilities: ['data-export', 'data-deletion']
    },
    {
      id: 'google',
      name: 'Google Workspace',
      description: 'Integration with Google Workspace services',
      status: 'active',
      version: '1.0.0',
      capabilities: ['data-export', 'data-deletion', 'data-update']
    },
    {
      id: 'aws',
      name: 'Amazon Web Services',
      description: 'Integration with AWS services',
      status: 'active',
      version: '1.0.0',
      capabilities: ['data-export', 'data-deletion']
    },
    {
      id: 'slack',
      name: 'Slack',
      description: 'Integration with Slack',
      status: 'active',
      version: '1.0.0',
      capabilities: ['data-export', 'data-deletion', 'notifications']
    }
  ];
};

/**
 * Get an integration by ID
 * @param {string} id - Integration ID
 * @returns {Object|null} - Integration or null if not found
 */
const getIntegrationById = (id) => {
  const integrations = getAvailableIntegrations();
  return integrations.find(integration => integration.id === id) || null;
};

/**
 * Execute an integration action
 * @param {string} integrationId - Integration ID
 * @param {string} action - Action to execute
 * @param {Object} data - Data for the action
 * @returns {Promise<Object>} - Result of the action
 */
const executeIntegrationAction = async (integrationId, action, data) => {
  switch (integrationId) {
    case 'salesforce':
      return salesforceIntegration.executeAction(action, data);
    case 'microsoft':
      return microsoftIntegration.executeAction(action, data);
    case 'google':
      return googleIntegration.executeAction(action, data);
    case 'aws':
      return awsIntegration.executeAction(action, data);
    case 'slack':
      return slackIntegration.executeAction(action, data);
    default:
      throw new Error(`Integration '${integrationId}' not found`);
  }
};

module.exports = {
  getAvailableIntegrations,
  getIntegrationById,
  executeIntegrationAction
};
