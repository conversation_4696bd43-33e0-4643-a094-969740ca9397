/**
 * useAnimation Hook
 * 
 * This hook provides animation functionality for React components.
 */

import { useRef, useEffect, useState, useCallback } from 'react';
import { createAnimation, createAnimationFromPreset } from './AnimationUtils';

/**
 * useAnimation hook
 * 
 * @param {Object|string} animationConfig - Animation configuration or preset name
 * @param {Object} [options] - Animation options
 * @param {boolean} [options.autoPlay=true] - Whether to play the animation automatically
 * @param {Function} [options.onStart] - Callback when animation starts
 * @param {Function} [options.onFinish] - Callback when animation finishes
 * @param {Function} [options.onCancel] - Callback when animation is cancelled
 * @returns {Object} Animation controls and state
 */
const useAnimation = (animationConfig, {
  autoPlay = true,
  onStart,
  onFinish,
  onCancel
} = {}) => {
  // Refs
  const elementRef = useRef(null);
  const animationRef = useRef(null);
  
  // State
  const [state, setState] = useState({
    status: 'idle', // idle, running, paused, finished
    progress: 0,
    currentTime: 0,
    playState: 'idle'
  });
  
  // Get animation configuration
  const getAnimation = useCallback(() => {
    if (typeof animationConfig === 'string') {
      return createAnimationFromPreset(animationConfig);
    } else {
      return createAnimation(animationConfig);
    }
  }, [animationConfig]);
  
  // Play animation
  const play = useCallback(() => {
    if (!elementRef.current) return;
    
    // Get animation
    const animation = getAnimation();
    
    // Cancel existing animation
    if (animationRef.current) {
      animationRef.current.cancel();
    }
    
    // Create new animation
    try {
      const webAnimation = elementRef.current.animate(
        animation.keyframes,
        animation.options
      );
      
      // Store animation
      animationRef.current = webAnimation;
      
      // Update state
      setState(prev => ({
        ...prev,
        status: 'running',
        playState: 'running'
      }));
      
      // Add event listeners
      webAnimation.onfinish = () => {
        setState(prev => ({
          ...prev,
          status: 'finished',
          progress: 1,
          currentTime: animation.options.duration,
          playState: 'finished'
        }));
        
        if (onFinish) {
          onFinish();
        }
      };
      
      webAnimation.oncancel = () => {
        setState(prev => ({
          ...prev,
          status: 'idle',
          progress: 0,
          currentTime: 0,
          playState: 'idle'
        }));
        
        if (onCancel) {
          onCancel();
        }
      };
      
      // Call onStart callback
      if (onStart) {
        onStart();
      }
      
      // Track progress
      const updateProgress = () => {
        if (webAnimation.playState !== 'finished' && webAnimation.playState !== 'idle') {
          const currentTime = webAnimation.currentTime || 0;
          const progress = Math.min(1, currentTime / animation.options.duration);
          
          setState(prev => ({
            ...prev,
            progress,
            currentTime,
            playState: webAnimation.playState
          }));
          
          requestAnimationFrame(updateProgress);
        }
      };
      
      requestAnimationFrame(updateProgress);
    } catch (error) {
      console.error('Error playing animation:', error);
    }
  }, [getAnimation, onStart, onFinish, onCancel]);
  
  // Pause animation
  const pause = useCallback(() => {
    if (animationRef.current && animationRef.current.playState === 'running') {
      animationRef.current.pause();
      
      setState(prev => ({
        ...prev,
        status: 'paused',
        playState: 'paused'
      }));
    }
  }, []);
  
  // Resume animation
  const resume = useCallback(() => {
    if (animationRef.current && animationRef.current.playState === 'paused') {
      animationRef.current.play();
      
      setState(prev => ({
        ...prev,
        status: 'running',
        playState: 'running'
      }));
    }
  }, []);
  
  // Cancel animation
  const cancel = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.cancel();
      
      setState({
        status: 'idle',
        progress: 0,
        currentTime: 0,
        playState: 'idle'
      });
    }
  }, []);
  
  // Finish animation
  const finish = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.finish();
      
      const animation = getAnimation();
      
      setState({
        status: 'finished',
        progress: 1,
        currentTime: animation.options.duration,
        playState: 'finished'
      });
    }
  }, [getAnimation]);
  
  // Reverse animation
  const reverse = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.reverse();
      
      setState(prev => ({
        ...prev,
        playState: animationRef.current.playState
      }));
    }
  }, []);
  
  // Set playback rate
  const setPlaybackRate = useCallback((rate) => {
    if (animationRef.current) {
      animationRef.current.playbackRate = rate;
    }
  }, []);
  
  // Auto-play animation
  useEffect(() => {
    if (autoPlay && elementRef.current) {
      play();
    }
    
    return () => {
      if (animationRef.current) {
        animationRef.current.cancel();
      }
    };
  }, [autoPlay, play]);
  
  return {
    ref: elementRef,
    play,
    pause,
    resume,
    cancel,
    finish,
    reverse,
    setPlaybackRate,
    ...state
  };
};

export default useAnimation;
