/**
 * Regulatory Routes
 * 
 * This module defines the routes for the regulatory intelligence API.
 */

const express = require('express');
const router = express.Router();
const regulatoryController = require('../controllers/regulatoryController');
const { cacheMiddleware } = require('../cache');

// Get all regulations
router.get('/regulations', cacheMiddleware(3600), regulatoryController.getRegulations);

// Get a specific regulation
router.get('/regulations/:id', cacheMiddleware(3600), regulatoryController.getRegulation);

// Get all requirements
router.get('/requirements', cacheMiddleware(3600), regulatoryController.getRequirements);

// Get a specific requirement
router.get('/requirements/:id', cacheMiddleware(3600), regulatoryController.getRequirement);

// Get regulatory changes
router.get('/changes', cacheMiddleware(3600), regulatoryController.getChanges);

// Get a specific change
router.get('/changes/:id', cacheMiddleware(3600), regulatoryController.getChange);

// Check compliance for a data processing activity
router.get('/compliance/activities/:activityId', regulatoryController.checkCompliance);

// Get compliance status for all data processing activities
router.get('/compliance/status', regulatoryController.getComplianceStatus);

// Get regulatory intelligence for a data processing activity
router.get('/intelligence/activities/:activityId', regulatoryController.getRegulatoryIntelligence);

module.exports = router;
