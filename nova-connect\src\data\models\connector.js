/**
 * NovaFuse Universal API Connector - Connector Model
 * 
 * This module defines the MongoDB schema for connector templates.
 */

const mongoose = require('mongoose');
const { Schema } = mongoose;

// Define sub-schemas
const MetadataSchema = new Schema({
  name: { type: String, required: true },
  version: { type: String, required: true },
  category: { type: String, required: true },
  description: { type: String, required: true },
  author: { type: String },
  tags: [String],
  created: { type: Date, default: Date.now },
  updated: { type: Date, default: Date.now },
  icon: String,
  documentationUrl: String
}, { _id: false });

const AuthFieldSchema = new Schema({
  type: { type: String, required: true, enum: ['string', 'number', 'boolean', 'object', 'array'] },
  description: String,
  required: { type: Boolean, default: false },
  sensitive: { type: Boolean, default: false },
  default: Schema.Types.Mixed
}, { _id: false });

const AuthenticationSchema = new Schema({
  type: { type: String, required: true, enum: ['API_KEY', 'BASIC', 'OAUTH2', 'CUSTOM', 'NONE'] },
  fields: { type: Map, of: AuthFieldSchema },
  testConnection: {
    endpoint: String,
    method: { type: String, enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] },
    expectedResponse: {
      status: Number
    }
  },
  oauth2Config: {
    authorizationUrl: String,
    tokenUrl: String,
    scopes: [String],
    grantType: String
  }
}, { _id: false });

const ConfigurationSchema = new Schema({
  baseUrl: { type: String, required: true },
  headers: { type: Map, of: String },
  rateLimit: {
    requests: Number,
    period: String
  },
  timeout: Number,
  retryPolicy: {
    maxRetries: Number,
    backoffStrategy: String
  }
}, { _id: false });

const ParameterSchema = new Schema({
  type: String,
  description: String,
  required: { type: Boolean, default: false },
  default: Schema.Types.Mixed
}, { _id: false });

const EndpointParametersSchema = new Schema({
  path: { type: Map, of: ParameterSchema },
  query: { type: Map, of: ParameterSchema },
  body: {
    required: { type: Boolean, default: false },
    properties: { type: Map, of: Schema.Types.Mixed }
  }
}, { _id: false });

const PaginationSchema = new Schema({
  type: { type: String, enum: ['offset', 'token', 'page', 'cursor'], required: true },
  parameters: { type: Map, of: String }
}, { _id: false });

const ResponseSchema = new Schema({
  successCode: Number,
  dataPath: String,
  schema: Schema.Types.Mixed
}, { _id: false });

const EndpointSchema = new Schema({
  id: { type: String, required: true },
  name: { type: String, required: true },
  description: String,
  path: { type: String, required: true },
  method: { type: String, required: true, enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'] },
  parameters: EndpointParametersSchema,
  pagination: PaginationSchema,
  response: ResponseSchema
}, { _id: false });

const TransformationSchema = new Schema({
  source: { type: String, required: true },
  target: { type: String, required: true },
  transform: { type: String, required: true },
  parameters: Schema.Types.Mixed
}, { _id: false });

const MappingSchema = new Schema({
  sourceEndpoint: { type: String, required: true },
  targetSystem: { type: String, required: true },
  targetEntity: { type: String, required: true },
  transformations: [TransformationSchema]
}, { _id: false });

const PollingEventSchema = new Schema({
  endpoint: { type: String, required: true },
  interval: { type: String, required: true },
  condition: String
}, { _id: false });

const EventsSchema = new Schema({
  polling: [PollingEventSchema]
}, { _id: false });

// Define main connector schema
const ConnectorSchema = new Schema({
  id: { 
    type: String, 
    required: true, 
    unique: true,
    index: true
  },
  metadata: { 
    type: MetadataSchema, 
    required: true 
  },
  authentication: { 
    type: AuthenticationSchema, 
    required: true 
  },
  configuration: { 
    type: ConfigurationSchema, 
    required: true 
  },
  endpoints: { 
    type: [EndpointSchema], 
    required: true,
    validate: [array => array.length > 0, 'Connector must have at least one endpoint']
  },
  mappings: [MappingSchema],
  events: EventsSchema,
  isActive: { 
    type: Boolean, 
    default: true 
  },
  isPublic: { 
    type: Boolean, 
    default: false 
  },
  ownerId: { 
    type: String,
    index: true
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
}, {
  timestamps: true,
  versionKey: true
});

// Create compound indexes
ConnectorSchema.index({ 'metadata.name': 1, 'metadata.version': 1 }, { unique: true });
ConnectorSchema.index({ 'metadata.category': 1 });
ConnectorSchema.index({ 'metadata.tags': 1 });
ConnectorSchema.index({ isActive: 1, isPublic: 1 });

// Pre-save hook to update the ID
ConnectorSchema.pre('save', function(next) {
  if (!this.id) {
    // Generate ID from name and version
    const name = this.metadata.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const version = this.metadata.version.replace(/\./g, '-');
    this.id = `${name}-${version}`;
  }
  next();
});

// Create the model
const Connector = mongoose.model('Connector', ConnectorSchema);

module.exports = Connector;
