# Business Intelligence & Workflow Connector

The Business Intelligence & Workflow Connector enables integration with business intelligence platforms and workflow automation systems, allowing you to manage dashboards, reports, and workflows through a standardized API.

## Overview

The Business Intelligence & Workflow Connector provides a unified interface for interacting with various business intelligence and workflow automation systems. It allows you to:

- Retrieve and manage dashboards
- Access and execute reports
- Create and manage workflows
- Execute workflows and monitor their status
- Integrate data visualization with workflow automation

## Configuration

### Authentication

The connector supports OAuth 2.0 authentication with the following parameters:

| Parameter | Description | Required |
|-----------|-------------|----------|
| `clientId` | OAuth 2.0 Client ID | Yes |
| `clientSecret` | OAuth 2.0 Client Secret | Yes |
| `redirectUri` | OAuth 2.0 Redirect URI | Yes |

### Base Configuration

| Parameter | Description | Default | Required |
|-----------|-------------|---------|----------|
| `baseUrl` | Base URL of the API | https://api.example.com | Yes |
| `timeout` | Request timeout in milliseconds | 30000 | No |
| `retryAttempts` | Number of retry attempts for failed requests | 3 | No |
| `retryDelay` | Delay between retry attempts in milliseconds | 1000 | No |

## Endpoints

### Dashboard Management

#### List Dashboards

Retrieves a list of dashboards.

**Endpoint:** `GET /dashboards`

**Query Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `page` | integer | Page number | No (default: 1) |
| `limit` | integer | Number of items per page | No (default: 20) |
| `folder` | string | Filter by folder | No |
| `owner` | string | Filter by owner | No |

**Example Request:**

```javascript
const dashboards = await connector.listDashboards({
  folder: 'Finance',
  limit: 50
});
```

**Example Response:**

```json
{
  "data": [
    {
      "id": "dashboard-123",
      "name": "Financial Overview",
      "description": "Financial KPIs and metrics",
      "folder": "Finance",
      "owner": "<EMAIL>",
      "url": "https://bi.example.com/dashboards/financial-overview",
      "createdAt": "2023-01-15T10:30:00Z",
      "updatedAt": "2023-02-20T14:15:00Z"
    },
    {
      "id": "dashboard-124",
      "name": "Sales Performance",
      "description": "Sales metrics and trends",
      "folder": "Finance",
      "owner": "<EMAIL>",
      "url": "https://bi.example.com/dashboards/sales-performance",
      "createdAt": "2023-01-20T09:45:00Z",
      "updatedAt": "2023-02-25T11:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "totalItems": 45,
    "totalPages": 1
  }
}
```

#### Get Dashboard

Retrieves a specific dashboard by ID.

**Endpoint:** `GET /dashboards/{dashboardId}`

**Path Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `dashboardId` | string | ID of the dashboard to retrieve | Yes |

**Example Request:**

```javascript
const dashboard = await connector.getDashboard('dashboard-123');
```

**Example Response:**

```json
{
  "id": "dashboard-123",
  "name": "Financial Overview",
  "description": "Financial KPIs and metrics",
  "folder": "Finance",
  "owner": "<EMAIL>",
  "url": "https://bi.example.com/dashboards/financial-overview",
  "layout": {
    "type": "grid",
    "columns": 12,
    "rows": 8
  },
  "widgets": [
    {
      "id": "widget-1",
      "type": "chart",
      "title": "Revenue by Quarter",
      "dataSource": "financial_data",
      "config": {
        "chartType": "bar",
        "xAxis": "quarter",
        "yAxis": "revenue"
      }
    },
    {
      "id": "widget-2",
      "type": "kpi",
      "title": "Total Revenue",
      "dataSource": "financial_data",
      "config": {
        "metric": "total_revenue",
        "format": "currency"
      }
    }
  ],
  "filters": [
    {
      "id": "filter-1",
      "field": "year",
      "operator": "equals",
      "value": "2023"
    }
  ],
  "permissions": [
    {
      "userId": "<EMAIL>",
      "role": "owner"
    },
    {
      "userId": "finance-team",
      "role": "viewer"
    }
  ],
  "createdAt": "2023-01-15T10:30:00Z",
  "updatedAt": "2023-02-20T14:15:00Z"
}
```

### Report Management

#### List Reports

Retrieves a list of reports.

**Endpoint:** `GET /reports`

**Query Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `page` | integer | Page number | No (default: 1) |
| `limit` | integer | Number of items per page | No (default: 20) |
| `folder` | string | Filter by folder | No |
| `owner` | string | Filter by owner | No |

**Example Request:**

```javascript
const reports = await connector.listReports({
  folder: 'Finance',
  limit: 50
});
```

**Example Response:**

```json
{
  "data": [
    {
      "id": "report-123",
      "name": "Quarterly Financial Report",
      "description": "Detailed financial analysis by quarter",
      "folder": "Finance",
      "owner": "<EMAIL>",
      "url": "https://bi.example.com/reports/quarterly-financial",
      "schedule": {
        "enabled": true,
        "frequency": "monthly"
      },
      "createdAt": "2023-01-15T10:30:00Z",
      "updatedAt": "2023-02-20T14:15:00Z"
    },
    {
      "id": "report-124",
      "name": "Sales by Region",
      "description": "Sales breakdown by geographic region",
      "folder": "Finance",
      "owner": "<EMAIL>",
      "url": "https://bi.example.com/reports/sales-by-region",
      "schedule": {
        "enabled": false,
        "frequency": null
      },
      "createdAt": "2023-01-20T09:45:00Z",
      "updatedAt": "2023-02-25T11:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "totalItems": 45,
    "totalPages": 1
  }
}
```

#### Execute Report

Executes a report with optional parameters.

**Endpoint:** `POST /reports/{reportId}/execute`

**Path Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `reportId` | string | ID of the report to execute | Yes |

**Request Body:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `parameters` | object | Report parameters | No |
| `format` | string | Output format (json, csv, excel, pdf) | No (default: json) |

**Example Request:**

```javascript
const result = await connector.executeReport('report-123', {
  parameters: {
    startDate: '2023-01-01',
    endDate: '2023-03-31',
    region: 'North America'
  },
  format: 'json'
});
```

**Example Response:**

```json
{
  "executionId": "exec-123",
  "status": "success",
  "data": [
    {
      "region": "North America",
      "quarter": "Q1",
      "revenue": 1250000,
      "expenses": 750000,
      "profit": 500000
    },
    {
      "region": "North America",
      "quarter": "Q2",
      "revenue": 1350000,
      "expenses": 800000,
      "profit": 550000
    }
  ],
  "executedAt": "2023-06-01T10:15:30Z"
}
```

### Workflow Management

#### List Workflows

Retrieves a list of workflows.

**Endpoint:** `GET /workflows`

**Query Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `page` | integer | Page number | No (default: 1) |
| `limit` | integer | Number of items per page | No (default: 20) |
| `status` | string | Filter by workflow status (active, inactive, draft) | No |
| `category` | string | Filter by workflow category | No |

**Example Request:**

```javascript
const workflows = await connector.listWorkflows({
  status: 'active',
  category: 'Finance',
  limit: 50
});
```

**Example Response:**

```json
{
  "data": [
    {
      "id": "workflow-123",
      "name": "Invoice Approval",
      "description": "Workflow for approving invoices",
      "status": "active",
      "category": "Finance",
      "trigger": {
        "type": "event",
        "config": {
          "event": "invoice.created"
        }
      },
      "createdAt": "2023-01-15T10:30:00Z",
      "updatedAt": "2023-02-20T14:15:00Z"
    },
    {
      "id": "workflow-124",
      "name": "Expense Report Processing",
      "description": "Workflow for processing expense reports",
      "status": "active",
      "category": "Finance",
      "trigger": {
        "type": "schedule",
        "config": {
          "cron": "0 9 * * 1-5"
        }
      },
      "createdAt": "2023-01-20T09:45:00Z",
      "updatedAt": "2023-02-25T11:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "totalItems": 45,
    "totalPages": 1
  }
}
```

#### Execute Workflow

Executes a workflow with optional input data.

**Endpoint:** `POST /workflows/{workflowId}/execute`

**Path Parameters:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `workflowId` | string | ID of the workflow to execute | Yes |

**Request Body:**

| Parameter | Type | Description | Required |
|-----------|------|-------------|----------|
| `input` | object | Workflow input data | No |
| `async` | boolean | Execute asynchronously | No (default: true) |

**Example Request:**

```javascript
const result = await connector.executeWorkflow('workflow-123', {
  input: {
    invoiceId: 'INV-12345',
    amount: 1500,
    vendor: 'Acme Corp'
  },
  async: true
});
```

**Example Response:**

```json
{
  "executionId": "exec-123",
  "status": "queued",
  "startTime": "2023-06-01T10:15:30Z",
  "statusUrl": "https://api.example.com/workflows/workflow-123/executions/exec-123"
}
```

## Error Handling

The connector handles errors according to the following table:

| HTTP Status Code | Error Code | Description |
|------------------|------------|-------------|
| 400 | INVALID_REQUEST | The request was invalid or malformed |
| 401 | UNAUTHORIZED | Authentication failed |
| 403 | FORBIDDEN | The authenticated user does not have permission |
| 404 | NOT_FOUND | The requested resource was not found |
| 409 | CONFLICT | The request conflicts with the current state |
| 429 | RATE_LIMITED | Too many requests, rate limit exceeded |
| 500 | SERVER_ERROR | An error occurred on the server |

## Examples

### Basic Usage

```javascript
// Initialize the connector
const connector = new BusinessIntelligenceWorkflowConnector({
  baseUrl: 'https://api.bi-platform.com'
}, {
  clientId: 'your-client-id',
  clientSecret: 'your-client-secret',
  redirectUri: 'https://your-app.com/callback'
});

// Initialize the connector
await connector.initialize();

// List dashboards in the Finance folder
const dashboards = await connector.listDashboards({
  folder: 'Finance',
  limit: 50
});

// Get a specific dashboard
const dashboard = await connector.getDashboard('dashboard-123');

// List reports in the Finance folder
const reports = await connector.listReports({
  folder: 'Finance',
  limit: 50
});

// Execute a report with parameters
const reportResult = await connector.executeReport('report-123', {
  parameters: {
    startDate: '2023-01-01',
    endDate: '2023-03-31',
    region: 'North America'
  },
  format: 'json'
});

// List active workflows in the Finance category
const workflows = await connector.listWorkflows({
  status: 'active',
  category: 'Finance',
  limit: 50
});

// Execute a workflow with input data
const workflowResult = await connector.executeWorkflow('workflow-123', {
  input: {
    invoiceId: 'INV-12345',
    amount: 1500,
    vendor: 'Acme Corp'
  },
  async: true
});
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Ensure that the client ID and client secret are correct
   - Check that the redirect URI matches the one configured in the authentication provider
   - Verify that the authentication token has not expired

2. **Rate Limiting**
   - Implement exponential backoff for retry attempts
   - Consider caching frequently accessed resources
   - Monitor API usage to stay within limits

3. **Connection Timeouts**
   - Increase the timeout value in the connector configuration
   - Check network connectivity to the API endpoint
   - Verify that the API service is operational

## Support

For additional support with the Business Intelligence & Workflow Connector, please contact [<EMAIL>](mailto:<EMAIL>) or visit our [support portal](https://support.novafuse.io).
