/**
 * NetworkGraphVisualization Component
 * 
 * A component for visualizing network relationships with force-directed layout.
 */

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTheme } from '../theme/ThemeContext';
import { usePerformance } from '../performance/usePerformance';

/**
 * NetworkGraphVisualization component
 * 
 * @param {Object} props - Component props
 * @param {Object} props.data - Graph data
 * @param {Array} props.data.nodes - Graph nodes
 * @param {Array} props.data.links - Graph links
 * @param {Object} [props.options] - Graph options
 * @param {boolean} [props.options.directed=false] - Whether the graph is directed
 * @param {boolean} [props.options.weighted=false] - Whether the graph is weighted
 * @param {string} [props.options.layout='force'] - Layout algorithm
 * @param {boolean} [props.options.draggable=true] - Whether nodes are draggable
 * @param {boolean} [props.options.zoomable=true] - Whether the graph is zoomable
 * @param {boolean} [props.options.pannable=true] - Whether the graph is pannable
 * @param {boolean} [props.options.showLabels=true] - Whether to show node labels
 * @param {boolean} [props.options.showTooltips=true] - Whether to show tooltips
 * @param {boolean} [props.options.highlightNeighbors=true] - Whether to highlight neighbors on hover
 * @param {Function} [props.options.nodeColor] - Function to determine node color
 * @param {Function} [props.options.nodeSize] - Function to determine node size
 * @param {Function} [props.options.linkWidth] - Function to determine link width
 * @param {Function} [props.options.linkColor] - Function to determine link color
 * @param {Function} [props.options.tooltipContent] - Function to generate tooltip content
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} NetworkGraphVisualization component
 */
const NetworkGraphVisualization = ({
  data,
  options = {},
  className = '',
  style = {}
}) => {
  const { measureOperation } = usePerformance('NetworkGraphVisualization');
  const { theme } = useTheme();
  
  // Refs
  const containerRef = useRef(null);
  const canvasRef = useRef(null);
  const simulationRef = useRef(null);
  const nodesRef = useRef([]);
  const linksRef = useRef([]);
  const transformRef = useRef({ x: 0, y: 0, scale: 1 });
  const dragRef = useRef({ dragging: false, node: null, offset: { x: 0, y: 0 } });
  const panRef = useRef({ panning: false, start: { x: 0, y: 0 } });
  
  // State
  const [hoveredNode, setHoveredNode] = useState(null);
  const [hoveredLink, setHoveredLink] = useState(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
  // Default options
  const {
    directed = false,
    weighted = false,
    layout = 'force',
    draggable = true,
    zoomable = true,
    pannable = true,
    showLabels = true,
    showTooltips = true,
    highlightNeighbors = true,
    nodeColor = (node) => node.color || theme.colors.primary,
    nodeSize = (node) => node.size || 10,
    linkWidth = (link) => link.width || 1,
    linkColor = (link) => link.color || theme.colors.divider,
    tooltipContent
  } = options;
  
  // Initialize graph
  useEffect(() => {
    if (!containerRef.current || !canvasRef.current || !data || !data.nodes || !data.links) return;
    
    const initializeGraph = () => {
      // Get container dimensions
      const { width, height } = containerRef.current.getBoundingClientRect();
      setDimensions({ width, height });
      
      // Set canvas size
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');
      const dpr = window.devicePixelRatio || 1;
      canvas.width = width * dpr;
      canvas.height = height * dpr;
      ctx.scale(dpr, dpr);
      
      // Process nodes and links
      nodesRef.current = data.nodes.map(node => ({
        ...node,
        x: node.x || Math.random() * width,
        y: node.y || Math.random() * height,
        size: nodeSize(node),
        color: nodeColor(node)
      }));
      
      linksRef.current = data.links.map(link => {
        const source = typeof link.source === 'object' ? link.source : nodesRef.current.find(n => n.id === link.source);
        const target = typeof link.target === 'object' ? link.target : nodesRef.current.find(n => n.id === link.target);
        
        return {
          ...link,
          source,
          target,
          width: linkWidth(link),
          color: linkColor(link)
        };
      });
      
      // Initialize force simulation
      if (layout === 'force' && typeof window.d3 !== 'undefined') {
        const { forceSimulation, forceLink, forceManyBody, forceCenter } = window.d3;
        
        simulationRef.current = forceSimulation(nodesRef.current)
          .force('link', forceLink(linksRef.current).id(d => d.id).distance(50))
          .force('charge', forceManyBody().strength(-100))
          .force('center', forceCenter(width / 2, height / 2))
          .on('tick', renderGraph);
      } else {
        // Simple layout for when d3 is not available
        layoutGraph();
        renderGraph();
      }
    };
    
    initializeGraph();
    
    // Cleanup
    return () => {
      if (simulationRef.current) {
        simulationRef.current.stop();
      }
    };
  }, [data, layout, nodeColor, nodeSize, linkWidth, linkColor]);
  
  // Update on resize
  useEffect(() => {
    if (!containerRef.current) return;
    
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
        
        // Update canvas size
        if (canvasRef.current) {
          const canvas = canvasRef.current;
          const ctx = canvas.getContext('2d');
          const dpr = window.devicePixelRatio || 1;
          canvas.width = width * dpr;
          canvas.height = height * dpr;
          ctx.scale(dpr, dpr);
          
          // Re-render
          renderGraph();
        }
      }
    };
    
    const resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(containerRef.current);
    
    return () => {
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);
  
  // Simple layout algorithm
  const layoutGraph = () => {
    const { width, height } = dimensions;
    const nodeCount = nodesRef.current.length;
    
    if (layout === 'circular') {
      // Circular layout
      const radius = Math.min(width, height) * 0.4;
      const centerX = width / 2;
      const centerY = height / 2;
      
      nodesRef.current.forEach((node, i) => {
        const angle = (i / nodeCount) * 2 * Math.PI;
        node.x = centerX + radius * Math.cos(angle);
        node.y = centerY + radius * Math.sin(angle);
      });
    } else if (layout === 'grid') {
      // Grid layout
      const cols = Math.ceil(Math.sqrt(nodeCount));
      const rows = Math.ceil(nodeCount / cols);
      const cellWidth = width / cols;
      const cellHeight = height / rows;
      
      nodesRef.current.forEach((node, i) => {
        const col = i % cols;
        const row = Math.floor(i / cols);
        node.x = col * cellWidth + cellWidth / 2;
        node.y = row * cellHeight + cellHeight / 2;
      });
    }
  };
  
  // Render graph
  const renderGraph = () => {
    if (!canvasRef.current) return;
    
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    const { width, height } = dimensions;
    
    // Clear canvas
    ctx.clearRect(0, 0, width, height);
    
    // Apply transform
    ctx.save();
    ctx.translate(transformRef.current.x, transformRef.current.y);
    ctx.scale(transformRef.current.scale, transformRef.current.scale);
    
    // Draw links
    linksRef.current.forEach(link => {
      if (!link.source || !link.target) return;
      
      const isHovered = hoveredLink === link || 
        (hoveredNode && highlightNeighbors && 
          (link.source.id === hoveredNode.id || link.target.id === hoveredNode.id));
      
      ctx.beginPath();
      ctx.moveTo(link.source.x, link.source.y);
      ctx.lineTo(link.target.x, link.target.y);
      ctx.strokeStyle = isHovered ? theme.colors.primary : link.color;
      ctx.lineWidth = isHovered ? link.width * 2 : link.width;
      ctx.stroke();
      
      // Draw arrow for directed graphs
      if (directed) {
        const dx = link.target.x - link.source.x;
        const dy = link.target.y - link.source.y;
        const angle = Math.atan2(dy, dx);
        const targetRadius = nodeSize(link.target);
        
        const arrowLength = 10;
        const arrowWidth = 6;
        
        const arrowX = link.target.x - targetRadius * Math.cos(angle);
        const arrowY = link.target.y - targetRadius * Math.sin(angle);
        
        ctx.beginPath();
        ctx.moveTo(arrowX, arrowY);
        ctx.lineTo(
          arrowX - arrowLength * Math.cos(angle) + arrowWidth * Math.sin(angle),
          arrowY - arrowLength * Math.sin(angle) - arrowWidth * Math.cos(angle)
        );
        ctx.lineTo(
          arrowX - arrowLength * Math.cos(angle) - arrowWidth * Math.sin(angle),
          arrowY - arrowLength * Math.sin(angle) + arrowWidth * Math.cos(angle)
        );
        ctx.closePath();
        ctx.fillStyle = isHovered ? theme.colors.primary : link.color;
        ctx.fill();
      }
      
      // Draw weight for weighted graphs
      if (weighted && link.weight) {
        const midX = (link.source.x + link.target.x) / 2;
        const midY = (link.source.y + link.target.y) / 2;
        
        ctx.font = '10px sans-serif';
        ctx.fillStyle = theme.colors.textSecondary;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(link.weight.toString(), midX, midY);
      }
    });
    
    // Draw nodes
    nodesRef.current.forEach(node => {
      const isHovered = hoveredNode === node;
      const isNeighbor = hoveredNode && highlightNeighbors && 
        linksRef.current.some(link => 
          (link.source.id === hoveredNode.id && link.target.id === node.id) ||
          (link.target.id === hoveredNode.id && link.source.id === node.id)
        );
      
      // Draw node
      ctx.beginPath();
      ctx.arc(node.x, node.y, node.size, 0, 2 * Math.PI);
      ctx.fillStyle = isHovered || isNeighbor ? theme.colors.secondary : node.color;
      ctx.fill();
      ctx.strokeStyle = theme.colors.background;
      ctx.lineWidth = 1;
      ctx.stroke();
      
      // Draw label
      if (showLabels || isHovered || isNeighbor) {
        ctx.font = `${isHovered ? 'bold ' : ''}12px sans-serif`;
        ctx.fillStyle = theme.colors.textPrimary;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(node.label || node.id, node.x, node.y + node.size + 10);
      }
    });
    
    ctx.restore();
  };
  
  // Handle mouse move
  const handleMouseMove = (e) => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = (e.clientX - rect.left) / transformRef.current.scale - transformRef.current.x / transformRef.current.scale;
    const y = (e.clientY - rect.top) / transformRef.current.scale - transformRef.current.y / transformRef.current.scale;
    
    // Handle node dragging
    if (dragRef.current.dragging && dragRef.current.node) {
      dragRef.current.node.x = x - dragRef.current.offset.x;
      dragRef.current.node.y = y - dragRef.current.offset.y;
      
      if (simulationRef.current) {
        simulationRef.current.alpha(0.3).restart();
      } else {
        renderGraph();
      }
      
      return;
    }
    
    // Handle panning
    if (panRef.current.panning) {
      transformRef.current.x += e.clientX - panRef.current.start.x;
      transformRef.current.y += e.clientY - panRef.current.start.y;
      panRef.current.start = { x: e.clientX, y: e.clientY };
      renderGraph();
      return;
    }
    
    // Check for hover
    let hoveredNodeFound = null;
    let hoveredLinkFound = null;
    
    // Check nodes (in reverse order to handle overlapping)
    for (let i = nodesRef.current.length - 1; i >= 0; i--) {
      const node = nodesRef.current[i];
      const dx = x - node.x;
      const dy = y - node.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance <= node.size) {
        hoveredNodeFound = node;
        break;
      }
    }
    
    // Check links if no node is hovered
    if (!hoveredNodeFound) {
      for (const link of linksRef.current) {
        if (!link.source || !link.target) continue;
        
        const x1 = link.source.x;
        const y1 = link.source.y;
        const x2 = link.target.x;
        const y2 = link.target.y;
        
        // Calculate distance from point to line segment
        const A = x - x1;
        const B = y - y1;
        const C = x2 - x1;
        const D = y2 - y1;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;
        
        if (lenSq !== 0) {
          param = dot / lenSq;
        }
        
        let xx, yy;
        
        if (param < 0) {
          xx = x1;
          yy = y1;
        } else if (param > 1) {
          xx = x2;
          yy = y2;
        } else {
          xx = x1 + param * C;
          yy = y1 + param * D;
        }
        
        const dx = x - xx;
        const dy = y - yy;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance <= 5) {
          hoveredLinkFound = link;
          break;
        }
      }
    }
    
    // Update hover state
    if (hoveredNodeFound !== hoveredNode) {
      setHoveredNode(hoveredNodeFound);
      renderGraph();
    }
    
    if (hoveredLinkFound !== hoveredLink) {
      setHoveredLink(hoveredLinkFound);
      renderGraph();
    }
  };
  
  // Handle mouse down
  const handleMouseDown = (e) => {
    if (!canvasRef.current) return;
    
    const rect = canvasRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / transformRef.current.scale - transformRef.current.x / transformRef.current.scale;
    const y = (e.clientY - rect.top) / transformRef.current.scale - transformRef.current.y / transformRef.current.scale;
    
    // Check if clicking on a node
    for (let i = nodesRef.current.length - 1; i >= 0; i--) {
      const node = nodesRef.current[i];
      const dx = x - node.x;
      const dy = y - node.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance <= node.size) {
        if (draggable) {
          dragRef.current = {
            dragging: true,
            node,
            offset: { x: dx, y: dy }
          };
          
          if (simulationRef.current) {
            simulationRef.current.alphaTarget(0.3).restart();
          }
        }
        return;
      }
    }
    
    // Start panning if not on a node
    if (pannable) {
      panRef.current = {
        panning: true,
        start: { x: e.clientX, y: e.clientY }
      };
    }
  };
  
  // Handle mouse up
  const handleMouseUp = () => {
    if (dragRef.current.dragging) {
      dragRef.current.dragging = false;
      
      if (simulationRef.current) {
        simulationRef.current.alphaTarget(0);
      }
    }
    
    if (panRef.current.panning) {
      panRef.current.panning = false;
    }
  };
  
  // Handle wheel for zooming
  const handleWheel = (e) => {
    if (!zoomable) return;
    
    e.preventDefault();
    
    const delta = e.deltaY < 0 ? 1.1 : 0.9;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Zoom around mouse position
    transformRef.current.x = x - delta * (x - transformRef.current.x);
    transformRef.current.y = y - delta * (y - transformRef.current.y);
    transformRef.current.scale *= delta;
    
    renderGraph();
  };
  
  // Get tooltip content
  const getTooltipContent = (node) => {
    if (tooltipContent && node) {
      return tooltipContent(node);
    }
    
    return `
      <div>
        <div><strong>${node.label || node.id}</strong></div>
        ${node.description ? `<div>${node.description}</div>` : ''}
      </div>
    `;
  };
  
  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={{ ...style, minHeight: '200px' }}
      data-testid="network-graph-visualization"
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full cursor-grab"
        onMouseMove={handleMouseMove}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      />
      
      {/* Tooltip */}
      {showTooltips && hoveredNode && (
        <div
          className="absolute z-10 bg-surface border border-divider rounded-md shadow-lg p-2 text-sm pointer-events-none"
          style={{
            left: `${hoveredNode.x * transformRef.current.scale + transformRef.current.x}px`,
            top: `${hoveredNode.y * transformRef.current.scale + transformRef.current.y - 40}px`,
            transform: 'translateX(-50%)'
          }}
          dangerouslySetInnerHTML={{ __html: getTooltipContent(hoveredNode) }}
        />
      )}
    </div>
  );
};

NetworkGraphVisualization.propTypes = {
  data: PropTypes.shape({
    nodes: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string.isRequired,
        label: PropTypes.string,
        size: PropTypes.number,
        color: PropTypes.string,
        x: PropTypes.number,
        y: PropTypes.number
      })
    ).isRequired,
    links: PropTypes.arrayOf(
      PropTypes.shape({
        source: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,
        target: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,
        weight: PropTypes.number,
        width: PropTypes.number,
        color: PropTypes.string
      })
    ).isRequired
  }).isRequired,
  options: PropTypes.shape({
    directed: PropTypes.bool,
    weighted: PropTypes.bool,
    layout: PropTypes.oneOf(['force', 'circular', 'grid']),
    draggable: PropTypes.bool,
    zoomable: PropTypes.bool,
    pannable: PropTypes.bool,
    showLabels: PropTypes.bool,
    showTooltips: PropTypes.bool,
    highlightNeighbors: PropTypes.bool,
    nodeColor: PropTypes.func,
    nodeSize: PropTypes.func,
    linkWidth: PropTypes.func,
    linkColor: PropTypes.func,
    tooltipContent: PropTypes.func
  }),
  className: PropTypes.string,
  style: PropTypes.object
};

export default NetworkGraphVisualization;
