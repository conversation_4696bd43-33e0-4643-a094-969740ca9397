/**
 * Database Error Handler
 * 
 * This utility provides error handling functions for database operations.
 */

const errorHandlingService = require('../services/ErrorHandlingService');
const logger = require('./logger');

/**
 * Handle database errors
 * @param {Error} error - Error object
 * @param {Object} context - Error context
 * @returns {Object} - Error response
 */
const handleDatabaseError = (error, context = {}) => {
  // Create database-specific error
  const dbError = createDatabaseError(error, context);
  
  // Handle the error using the error handling service
  return errorHandlingService.handleError(dbError, context);
};

/**
 * Create a database-specific error
 * @param {Error} error - Original error
 * @param {Object} context - Error context
 * @returns {Error} - Database error
 */
const createDatabaseError = (error, context = {}) => {
  const { model, operation, query } = context;
  
  // Create a new error with database-specific information
  const dbError = new Error(
    `Database error: ${model ? model + ' - ' : ''}${operation ? operation + ' - ' : ''}${error.message}`
  );
  
  // Copy properties from the original error
  Object.assign(dbError, error);
  
  // Set database-specific properties
  dbError.name = 'DatabaseError';
  dbError.model = model;
  dbError.operation = operation;
  dbError.query = query;
  dbError.originalError = error;
  
  return dbError;
};

/**
 * Map database-specific error codes to standard error types
 * @param {string} errorCode - Database-specific error code
 * @returns {string} - Standard error type
 */
const mapErrorCodeToType = (errorCode) => {
  // MongoDB error code mappings
  const mongoMappings = {
    1: 'connection_error',
    2: 'query_error',
    3: 'duplicate_key',
    4: 'graphics_error',
    5: 'cursor_not_found',
    6: 'operation_failed',
    7: 'host_unreachable',
    8: 'host_not_found',
    9: 'network_timeout',
    11000: 'duplicate_key',
    11001: 'duplicate_key',
    12000: 'connection_error',
    13000: 'authentication_error',
    14000: 'server_selection_error',
    16000: 'write_concern_error',
    17000: 'replication_error',
    18000: 'shard_error',
    50000: 'execution_timeout',
    51000: 'command_error',
    56000: 'transaction_error'
  };
  
  return mongoMappings[errorCode] || 'unknown_error';
};

/**
 * Create a retry policy for database operations
 * @param {Object} options - Retry options
 * @returns {Object} - Retry policy
 */
const createRetryPolicy = (options = {}) => {
  // Default retry policy for database operations
  const defaultPolicy = {
    maxRetries: 3,
    initialDelay: 100,
    maxDelay: 5000,
    backoffFactor: 2,
    jitter: true,
    retryableErrors: ['connection_error', 'host_unreachable', 'host_not_found', 'network_timeout', 'server_selection_error', 'replication_error', 'shard_error', 'execution_timeout']
  };
  
  // Merge default policy with provided options
  return {
    ...defaultPolicy,
    ...options
  };
};

/**
 * Create a circuit breaker for database operations
 * @param {string} model - Model name
 * @param {Object} options - Circuit breaker options
 */
const createCircuitBreaker = (model, options = {}) => {
  // Default circuit breaker options for database operations
  const defaultOptions = {
    failureThreshold: 5,
    resetTimeout: 10000
  };
  
  // Merge default options with provided options
  const mergedOptions = {
    ...defaultOptions,
    ...options
  };
  
  // Register circuit breaker
  errorHandlingService.registerCircuitBreaker(`db:${model}`, mergedOptions);
};

/**
 * Create a wrapped database function with error handling
 * @param {Function} fn - Database function to wrap
 * @param {Object} options - Options
 * @returns {Function} - Wrapped function
 */
const withDatabaseErrorHandling = (fn, options = {}) => {
  const { model, operation, retry = true, circuitBreaker = true, timeout = true } = options;
  
  // Create context
  const context = {
    model,
    operation
  };
  
  // Create wrapped function
  let wrappedFn = async (...args) => {
    try {
      return await fn(...args);
    } catch (error) {
      throw createDatabaseError(error, context);
    }
  };
  
  // Add timeout if enabled
  if (timeout) {
    const timeoutMs = options.timeoutMs || 5000;
    wrappedFn = errorHandlingService.withTimeout(wrappedFn, { timeoutMs });
  }
  
  // Add retry if enabled
  if (retry) {
    const retryPolicy = createRetryPolicy(options.retry);
    wrappedFn = errorHandlingService.withRetry(wrappedFn, retryPolicy);
  }
  
  // Add circuit breaker if enabled
  if (circuitBreaker) {
    createCircuitBreaker(model, options.circuitBreaker);
    wrappedFn = errorHandlingService.withCircuitBreaker(wrappedFn, { resource: `db:${model}` });
  }
  
  return wrappedFn;
};

/**
 * Create a Mongoose plugin for error handling
 * @param {Object} options - Options
 * @returns {Function} - Mongoose plugin
 */
const mongooseErrorHandlingPlugin = (options = {}) => {
  return (schema) => {
    // Get model name
    const modelName = options.modelName || schema.options.collection || 'unknown';
    
    // Add pre-save hook
    schema.pre('save', function(next) {
      this.$operation = 'save';
      next();
    });
    
    // Add pre-remove hook
    schema.pre('remove', function(next) {
      this.$operation = 'remove';
      next();
    });
    
    // Add pre-find hook
    schema.pre('find', function() {
      this.$operation = 'find';
    });
    
    // Add pre-findOne hook
    schema.pre('findOne', function() {
      this.$operation = 'findOne';
    });
    
    // Add pre-update hook
    schema.pre('update', function() {
      this.$operation = 'update';
    });
    
    // Add pre-updateOne hook
    schema.pre('updateOne', function() {
      this.$operation = 'updateOne';
    });
    
    // Add post-save hook
    schema.post('save', function(error, doc, next) {
      if (error) {
        next(createDatabaseError(error, {
          model: modelName,
          operation: 'save',
          query: doc
        }));
      } else {
        next();
      }
    });
    
    // Add post-remove hook
    schema.post('remove', function(error, doc, next) {
      if (error) {
        next(createDatabaseError(error, {
          model: modelName,
          operation: 'remove',
          query: doc
        }));
      } else {
        next();
      }
    });
    
    // Add post-find hook
    schema.post('find', function(error, result, next) {
      if (error) {
        next(createDatabaseError(error, {
          model: modelName,
          operation: 'find',
          query: this.getQuery()
        }));
      } else {
        next();
      }
    });
    
    // Add post-findOne hook
    schema.post('findOne', function(error, result, next) {
      if (error) {
        next(createDatabaseError(error, {
          model: modelName,
          operation: 'findOne',
          query: this.getQuery()
        }));
      } else {
        next();
      }
    });
    
    // Add post-update hook
    schema.post('update', function(error, result, next) {
      if (error) {
        next(createDatabaseError(error, {
          model: modelName,
          operation: 'update',
          query: this.getQuery()
        }));
      } else {
        next();
      }
    });
    
    // Add post-updateOne hook
    schema.post('updateOne', function(error, result, next) {
      if (error) {
        next(createDatabaseError(error, {
          model: modelName,
          operation: 'updateOne',
          query: this.getQuery()
        }));
      } else {
        next();
      }
    });
  };
};

module.exports = {
  handleDatabaseError,
  createDatabaseError,
  mapErrorCodeToType,
  createRetryPolicy,
  createCircuitBreaker,
  withDatabaseErrorHandling,
  mongooseErrorHandlingPlugin
};
