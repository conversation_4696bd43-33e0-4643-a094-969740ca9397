d8e7b53588f81c58106f80788ce63170
/**
 * NovaFuse Universal API Connector Rate Limiter
 * 
 * This module provides rate limiting functionality for the UAC.
 */

/**
 * Rate Limiting Utility
 * 
 * Provides methods for rate limiting API requests.
 */
class RateLimiter {
  constructor(options = {}) {
    this.options = {
      windowMs: 60 * 1000,
      // 1 minute
      maxRequests: 100,
      // 100 requests per minute
      keyGenerator: req => req.ip || 'default',
      ...options
    };
    this.requests = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), this.options.windowMs);
  }

  /**
   * Check if a request is allowed
   * @param {Object} req - Request object
   * @returns {boolean} - Whether the request is allowed
   */
  isAllowed(req) {
    const key = this.options.keyGenerator(req);
    const now = Date.now();

    // Get or create request record
    let record = this.requests.get(key);
    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.options.windowMs
      };
      this.requests.set(key, record);
    }

    // Reset count if window has passed
    if (now > record.resetTime) {
      record.count = 0;
      record.resetTime = now + this.options.windowMs;
    }

    // Check if request is allowed
    if (record.count >= this.options.maxRequests) {
      return false;
    }

    // Increment count
    record.count++;
    return true;
  }

  /**
   * Get remaining requests for a key
   * @param {Object} req - Request object
   * @returns {number} - Remaining requests
   */
  getRemainingRequests(req) {
    const key = this.options.keyGenerator(req);
    const now = Date.now();

    // Get request record
    const record = this.requests.get(key);
    if (!record) {
      return this.options.maxRequests;
    }

    // Reset count if window has passed
    if (now > record.resetTime) {
      return this.options.maxRequests;
    }
    return Math.max(0, this.options.maxRequests - record.count);
  }

  /**
   * Get reset time for a key
   * @param {Object} req - Request object
   * @returns {number} - Reset time in milliseconds
   */
  getResetTime(req) {
    const key = this.options.keyGenerator(req);

    // Get request record
    const record = this.requests.get(key);
    if (!record) {
      return Date.now() + this.options.windowMs;
    }
    return record.resetTime;
  }

  /**
   * Clean up expired records
   */
  cleanup() {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }

  /**
   * Stop the rate limiter and clear the cleanup interval
   */
  stop() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}
module.exports = RateLimiter;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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