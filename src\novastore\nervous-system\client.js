/**
 * NovaStore Nervous System Client
 * 
 * This module provides a client for interacting with the NovaStore Nervous System.
 * It supports all three integration tiers:
 * 1. Physics Tier: Direct gRPC integration with sub-millisecond latency
 * 2. Transition Tier: Enhanced NovaConnect + CSDE integration
 * 3. Legacy Tier: Traditional NovaConnect REST API integration
 */

const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const { performance } = require('perf_hooks');

// Constants
const LATENCY_THRESHOLD = 0.07; // 0.07ms maximum latency

class NovaStoreNervousSystemClient {
  /**
   * Create a new NovaStore Nervous System Client
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    this.options = {
      clientId: options.clientId || uuidv4(),
      clientName: options.clientName || 'NovaConnect Client',
      tier: options.tier || 'physics', // 'physics', 'transition', or 'legacy'
      grpcHost: options.grpcHost || 'localhost',
      grpcPort: options.grpcPort || 50051,
      restApiUrl: options.restApiUrl || 'http://localhost:3000/api',
      enableMetrics: options.enableMetrics !== false,
      ...options
    };
    
    this.logger = options.logger || console;
    
    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageLatency: 0,
      totalLatency: 0
    };
    
    // Initialize client based on tier
    this._initializeClient();
    
    this.logger.info(`NovaStore Nervous System Client initialized (Tier: ${this.options.tier})`);
  }
  
  /**
   * Initialize client based on tier
   * @private
   */
  _initializeClient() {
    switch (this.options.tier) {
      case 'physics':
        this._initializeGrpcClient();
        break;
      case 'transition':
        this._initializeTransitionClient();
        break;
      case 'legacy':
        this._initializeRestClient();
        break;
      default:
        throw new Error(`Invalid tier: ${this.options.tier}`);
    }
  }
  
  /**
   * Initialize gRPC client for Physics tier
   * @private
   */
  _initializeGrpcClient() {
    this.logger.info('Initializing gRPC client for Physics tier');
    
    // Load proto definition
    const packageDefinition = protoLoader.loadSync(
      path.resolve(__dirname, './protos/csde.proto'),
      {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true
      }
    );
    
    const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);
    const csdeService = protoDescriptor.novafuse.csde;
    
    // Create gRPC client
    this.grpcClient = new csdeService.CSEDService(
      `${this.options.grpcHost}:${this.options.grpcPort}`,
      grpc.credentials.createInsecure()
    );
    
    this.logger.info(`gRPC client initialized (${this.options.grpcHost}:${this.options.grpcPort})`);
  }
  
  /**
   * Initialize client for Transition tier
   * @private
   */
  _initializeTransitionClient() {
    this.logger.info('Initializing client for Transition tier');
    
    // Initialize both gRPC and REST clients
    this._initializeGrpcClient();
    this._initializeRestClient();
    
    this.logger.info('Transition tier client initialized');
  }
  
  /**
   * Initialize REST client for Legacy tier
   * @private
   */
  _initializeRestClient() {
    this.logger.info('Initializing REST client for Legacy tier');
    
    // Create axios client
    this.restClient = axios.create({
      baseURL: this.options.restApiUrl,
      headers: {
        'Content-Type': 'application/json',
        'X-Client-ID': this.options.clientId,
        'X-Client-Name': this.options.clientName
      }
    });
    
    this.logger.info(`REST client initialized (${this.options.restApiUrl})`);
  }
  
  /**
   * Calculate CSDE value
   * @param {Object} complianceData - Compliance data
   * @param {Object} gcpData - GCP integration data
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @param {Object} options - Calculation options
   * @returns {Promise<Object>} - CSDE calculation result
   */
  async calculateCSDE(complianceData, gcpData, cyberSafetyData, options = {}) {
    const startTime = performance.now();
    
    try {
      let result;
      
      switch (this.options.tier) {
        case 'physics':
          result = await this._calculateCSEDPhysics(complianceData, gcpData, cyberSafetyData, options);
          break;
        case 'transition':
          result = await this._calculateCSEDTransition(complianceData, gcpData, cyberSafetyData, options);
          break;
        case 'legacy':
          result = await this._calculateCSEDLegacy(complianceData, gcpData, cyberSafetyData, options);
          break;
      }
      
      // Update metrics
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (this.options.enableMetrics) {
        this.metrics.totalRequests++;
        this.metrics.successfulRequests++;
        this.metrics.totalLatency += duration;
        this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.successfulRequests;
      }
      
      // Check latency threshold for Physics tier
      if (this.options.tier === 'physics' && duration > LATENCY_THRESHOLD) {
        this.logger.warn(`Latency threshold exceeded: ${duration.toFixed(3)}ms > ${LATENCY_THRESHOLD}ms`);
      }
      
      return result;
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.totalRequests++;
        this.metrics.failedRequests++;
      }
      
      this.logger.error('Error calculating CSDE:', error);
      throw error;
    }
  }
  
  /**
   * Calculate CSDE value using Physics tier (gRPC)
   * @param {Object} complianceData - Compliance data
   * @param {Object} gcpData - GCP integration data
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @param {Object} options - Calculation options
   * @returns {Promise<Object>} - CSDE calculation result
   * @private
   */
  async _calculateCSEDPhysics(complianceData, gcpData, cyberSafetyData, options) {
    return new Promise((resolve, reject) => {
      this.grpcClient.calculateCSDE({
        client_id: this.options.clientId,
        client_name: this.options.clientName,
        compliance_data: JSON.stringify(complianceData),
        gcp_data: JSON.stringify(gcpData),
        cyber_safety_data: JSON.stringify(cyberSafetyData),
        options: {
          enable_wilson_loop: options.enableWilsonLoop !== false,
          enable_remediation: options.enableRemediation !== false,
          enable_caching: options.enableCaching !== false,
          domain: options.domain || 'security',
          optimization_level: options.optimizationLevel || 3
        }
      }, (error, response) => {
        if (error) {
          reject(error);
          return;
        }
        
        resolve({
          csdeValue: response.csde_value,
          performanceFactor: response.performance_factor,
          remediationActions: JSON.parse(response.remediation_actions || '[]'),
          wilsonLoopId: response.wilson_loop_id,
          processingTime: response.processing_time
        });
      });
    });
  }
  
  /**
   * Calculate CSDE value using Transition tier
   * @param {Object} complianceData - Compliance data
   * @param {Object} gcpData - GCP integration data
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @param {Object} options - Calculation options
   * @returns {Promise<Object>} - CSDE calculation result
   * @private
   */
  async _calculateCSEDTransition(complianceData, gcpData, cyberSafetyData, options) {
    // For critical operations, use Physics tier
    if (options.priority === 'high') {
      return this._calculateCSEDPhysics(complianceData, gcpData, cyberSafetyData, options);
    }
    
    // For non-critical operations, use Legacy tier
    return this._calculateCSEDLegacy(complianceData, gcpData, cyberSafetyData, options);
  }
  
  /**
   * Calculate CSDE value using Legacy tier (REST)
   * @param {Object} complianceData - Compliance data
   * @param {Object} gcpData - GCP integration data
   * @param {Object} cyberSafetyData - Cyber-Safety data
   * @param {Object} options - Calculation options
   * @returns {Promise<Object>} - CSDE calculation result
   * @private
   */
  async _calculateCSEDLegacy(complianceData, gcpData, cyberSafetyData, options) {
    const response = await this.restClient.post('/csde/calculate', {
      complianceData,
      gcpData,
      cyberSafetyData,
      options: {
        enableWilsonLoop: options.enableWilsonLoop !== false,
        enableRemediation: options.enableRemediation !== false,
        enableCaching: options.enableCaching !== false,
        domain: options.domain || 'security',
        optimizationLevel: options.optimizationLevel || 3
      }
    });
    
    return response.data;
  }
  
  /**
   * Process a security event
   * @param {Object} eventData - Event data
   * @param {string} eventType - Event type
   * @param {string} severity - Event severity
   * @returns {Promise<Object>} - Event processing result
   */
  async processEvent(eventData, eventType, severity = 'MEDIUM') {
    const startTime = performance.now();
    
    try {
      let result;
      
      switch (this.options.tier) {
        case 'physics':
          result = await this._processEventPhysics(eventData, eventType, severity);
          break;
        case 'transition':
          result = await this._processEventTransition(eventData, eventType, severity);
          break;
        case 'legacy':
          result = await this._processEventLegacy(eventData, eventType, severity);
          break;
      }
      
      // Update metrics
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (this.options.enableMetrics) {
        this.metrics.totalRequests++;
        this.metrics.successfulRequests++;
        this.metrics.totalLatency += duration;
        this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.successfulRequests;
      }
      
      return result;
    } catch (error) {
      // Update metrics
      if (this.options.enableMetrics) {
        this.metrics.totalRequests++;
        this.metrics.failedRequests++;
      }
      
      this.logger.error('Error processing event:', error);
      throw error;
    }
  }
  
  /**
   * Process a security event using Physics tier (gRPC)
   * @param {Object} eventData - Event data
   * @param {string} eventType - Event type
   * @param {string} severity - Event severity
   * @returns {Promise<Object>} - Event processing result
   * @private
   */
  async _processEventPhysics(eventData, eventType, severity) {
    // Implementation for processing events using Physics tier
    // This would be similar to _calculateCSEDPhysics
    return { success: true };
  }
  
  /**
   * Process a security event using Transition tier
   * @param {Object} eventData - Event data
   * @param {string} eventType - Event type
   * @param {string} severity - Event severity
   * @returns {Promise<Object>} - Event processing result
   * @private
   */
  async _processEventTransition(eventData, eventType, severity) {
    // Implementation for processing events using Transition tier
    return { success: true };
  }
  
  /**
   * Process a security event using Legacy tier (REST)
   * @param {Object} eventData - Event data
   * @param {string} eventType - Event type
   * @param {string} severity - Event severity
   * @returns {Promise<Object>} - Event processing result
   * @private
   */
  async _processEventLegacy(eventData, eventType, severity) {
    // Implementation for processing events using Legacy tier
    return { success: true };
  }
  
  /**
   * Get metrics
   * @returns {Object} - Client metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Close the client
   * @returns {Promise<void>}
   */
  async close() {
    this.logger.info('Closing NovaStore Nervous System Client');
    
    // Close gRPC client if it exists
    if (this.grpcClient && this.options.tier !== 'legacy') {
      this.grpcClient.close();
    }
    
    this.logger.info('NovaStore Nervous System Client closed');
  }
}

module.exports = NovaStoreNervousSystemClient;
