/**
 * NovaFuse Partner SDK - Revenue Calculator
 * 
 * This module calculates partner revenue based on usage.
 * 
 * @version 1.0.0
 * @license NovaFuse Proprietary
 */

const fs = require('fs');
const path = require('path');

/**
 * Revenue Calculator
 * 
 * Calculates partner revenue based on usage.
 */
class RevenueCalculator {
  /**
   * Create a new Revenue Calculator instance
   * @param {Object} config - Configuration
   * @param {String} config.partnerId - Partner ID
   * @param {Number} config.revenueShare - Revenue share (0.0 - 1.0)
   * @param {String} config.environment - Environment (production, sandbox)
   */
  constructor(config) {
    this.partnerId = config.partnerId;
    this.revenueShare = config.revenueShare || 0.82; // Default to 82%
    this.environment = config.environment || 'sandbox';
    this.revenueData = [];
    
    // In a real implementation, this would connect to a database
    // For now, we'll use a local file for demonstration
    this.revenueFile = path.join(__dirname, 'data', `${this.partnerId}_revenue.json`);
    
    // Create data directory if it doesn't exist
    const dataDir = path.join(__dirname, 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Load existing revenue data if available
    this._loadRevenueData();
    
    // Define pricing tiers
    this.pricingTiers = {
      analyze: {
        base: 10.00, // Base price per analysis
        small: 10.00, // Small scope
        medium: 25.00, // Medium scope
        large: 50.00, // Large scope
        enterprise: 100.00 // Enterprise scope
      },
      remediate: {
        base: 25.00, // Base price per remediation
        small: 25.00, // Small scope
        medium: 50.00, // Medium scope
        large: 100.00, // Large scope
        enterprise: 200.00 // Enterprise scope
      },
      report: {
        base: 5.00 // Base price per report
      }
    };
    
    console.log(`Revenue Calculator initialized for partner: ${this.partnerId}`);
    console.log(`Revenue share: ${this.revenueShare * 100}%`);
  }
  
  /**
   * Calculate revenue for an operation
   * @param {Object} params - Parameters
   * @param {String} params.operation - Operation (analyze, remediate, report)
   * @param {String} params.scope - Operation scope (small, medium, large, enterprise)
   * @param {Object} params.result - Operation result
   * @returns {Object} - Revenue calculation
   */
  async calculateRevenue(params) {
    console.log(`Calculating revenue for operation: ${params.operation}, scope: ${params.scope}`);
    
    // Get pricing tier
    const pricingTier = this.pricingTiers[params.operation] || { base: 10.00 };
    
    // Calculate base price
    let basePrice = pricingTier.base;
    
    // Add scope price if available
    if (pricingTier[params.scope]) {
      basePrice = pricingTier[params.scope];
    }
    
    // Apply complexity multiplier based on result
    let complexityMultiplier = 1.0;
    
    if (params.result) {
      // For analysis, use the number of remediation actions
      if (params.operation === 'analyze' && params.result.remediationActions) {
        const actionCount = params.result.remediationActions.length;
        if (actionCount > 20) {
          complexityMultiplier = 1.5;
        } else if (actionCount > 10) {
          complexityMultiplier = 1.25;
        }
      }
      
      // For remediation, use the number of remediated actions
      if (params.operation === 'remediate' && params.result.remediation) {
        const remediatedCount = params.result.remediation.actionsRemediated;
        if (remediatedCount > 10) {
          complexityMultiplier = 1.5;
        } else if (remediatedCount > 5) {
          complexityMultiplier = 1.25;
        }
      }
    }
    
    // Calculate total price
    const totalPrice = basePrice * complexityMultiplier;
    
    // Calculate partner revenue
    const partnerRevenue = totalPrice * this.revenueShare;
    
    // Calculate NovaFuse revenue
    const novaFuseRevenue = totalPrice - partnerRevenue;
    
    // Generate revenue ID
    const revenueId = `REV-${Date.now()}-${Math.floor(Math.random() * 1000000)}`;
    
    // Create revenue record
    const revenueRecord = {
      id: revenueId,
      partnerId: this.partnerId,
      operation: params.operation,
      scope: params.scope,
      basePrice,
      complexityMultiplier,
      totalPrice,
      partnerRevenue,
      novaFuseRevenue,
      revenueShare: this.revenueShare,
      timestamp: new Date().toISOString(),
      environment: this.environment
    };
    
    // Add to revenue data
    this.revenueData.push(revenueRecord);
    
    // Save revenue data
    this._saveRevenueData();
    
    return {
      id: revenueId,
      basePrice,
      complexityMultiplier,
      totalPrice,
      partnerRevenue,
      novaFuseRevenue,
      revenueShare: this.revenueShare
    };
  }
  
  /**
   * Get revenue information
   * @param {Object} options - Options
   * @param {String} options.startDate - Start date
   * @param {String} options.endDate - End date
   * @param {String} options.clientId - Filter by client ID
   * @param {String} options.operation - Filter by operation
   * @returns {Object} - Revenue information
   */
  async getRevenueInfo(options = {}) {
    console.log(`Getting revenue information for partner: ${this.partnerId}`);
    
    // Filter revenue data
    let filteredRevenue = [...this.revenueData];
    
    if (options.startDate) {
      const startDate = new Date(options.startDate).getTime();
      filteredRevenue = filteredRevenue.filter(revenue => new Date(revenue.timestamp).getTime() >= startDate);
    }
    
    if (options.endDate) {
      const endDate = new Date(options.endDate).getTime();
      filteredRevenue = filteredRevenue.filter(revenue => new Date(revenue.timestamp).getTime() <= endDate);
    }
    
    if (options.operation) {
      filteredRevenue = filteredRevenue.filter(revenue => revenue.operation === options.operation);
    }
    
    // Calculate revenue statistics
    const totalRevenue = filteredRevenue.reduce((sum, revenue) => sum + revenue.partnerRevenue, 0);
    const totalNovaFuseRevenue = filteredRevenue.reduce((sum, revenue) => sum + revenue.novaFuseRevenue, 0);
    const totalTransactions = filteredRevenue.length;
    
    // Group by operation
    const operationStats = {};
    filteredRevenue.forEach(revenue => {
      if (!operationStats[revenue.operation]) {
        operationStats[revenue.operation] = {
          count: 0,
          revenue: 0
        };
      }
      
      operationStats[revenue.operation].count++;
      operationStats[revenue.operation].revenue += revenue.partnerRevenue;
    });
    
    // Group by day
    const dailyStats = {};
    filteredRevenue.forEach(revenue => {
      const day = revenue.timestamp.split('T')[0];
      
      if (!dailyStats[day]) {
        dailyStats[day] = {
          count: 0,
          revenue: 0
        };
      }
      
      dailyStats[day].count++;
      dailyStats[day].revenue += revenue.partnerRevenue;
    });
    
    return {
      totalRevenue,
      totalNovaFuseRevenue,
      totalTransactions,
      averageTransactionValue: totalTransactions > 0 ? totalRevenue / totalTransactions : 0,
      byOperation: operationStats,
      byDay: dailyStats,
      details: filteredRevenue
    };
  }
  
  /**
   * Load revenue data from file
   * @private
   */
  _loadRevenueData() {
    try {
      if (fs.existsSync(this.revenueFile)) {
        const data = fs.readFileSync(this.revenueFile, 'utf8');
        this.revenueData = JSON.parse(data);
        console.log(`Loaded ${this.revenueData.length} revenue records for partner: ${this.partnerId}`);
      } else {
        this.revenueData = [];
        console.log(`No existing revenue data for partner: ${this.partnerId}`);
      }
    } catch (error) {
      console.error(`Error loading revenue data: ${error.message}`);
      this.revenueData = [];
    }
  }
  
  /**
   * Save revenue data to file
   * @private
   */
  _saveRevenueData() {
    try {
      fs.writeFileSync(this.revenueFile, JSON.stringify(this.revenueData, null, 2));
      console.log(`Saved ${this.revenueData.length} revenue records for partner: ${this.partnerId}`);
    } catch (error) {
      console.error(`Error saving revenue data: ${error.message}`);
    }
  }
}

module.exports = RevenueCalculator;
