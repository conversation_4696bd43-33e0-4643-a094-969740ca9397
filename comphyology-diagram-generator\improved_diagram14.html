<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>14. Dashboard and Visualization Examples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>14. Dashboard and Visualization Examples</h1>

    <div class="diagram-container">
        <!-- Dashboard Visualization -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Dashboard and Visualization Examples
            <div class="element-number">1</div>
        </div>

        <!-- Real-time Dashboards -->
        <div class="element" style="top: 150px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Real-time Dashboards
            <div class="element-number">2</div>
        </div>

        <div class="element" style="top: 250px; left: 150px; width: 250px; font-size: 14px;">
            Metric Gauges<br>Circular or linear gauges showing current values
            <div class="element-number">3</div>
        </div>

        <div class="element" style="top: 250px; left: 600px; width: 250px; font-size: 14px;">
            Trend Charts<br>Line, bar, or area charts showing changes over time
            <div class="element-number">4</div>
        </div>

        <div class="element" style="top: 350px; left: 150px; width: 250px; font-size: 14px;">
            Heatmaps<br>Color-coded representations of metric values
            <div class="element-number">5</div>
        </div>

        <div class="element" style="top: 350px; left: 600px; width: 250px; font-size: 14px;">
            Status Indicators<br>Red/yellow/green indicators showing status
            <div class="element-number">6</div>
        </div>

        <!-- Trinity Visualization -->
        <div class="element" style="top: 450px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Trinity Visualization
            <div class="element-number">7</div>
        </div>

        <div class="element" style="top: 550px; left: 150px; width: 250px; font-size: 14px;">
            Trinity Triangle<br>Three-sided representation with G, D, R vertices
            <div class="element-number">8</div>
        </div>

        <div class="element" style="top: 550px; left: 600px; width: 250px; font-size: 14px;">
            Force Diagram<br>Dynamic visualization showing push and pull
            <div class="element-number">9</div>
        </div>

        <div class="element" style="top: 650px; left: 150px; width: 250px; font-size: 14px;">
            Vector Field<br>Directional representation of curl and divergence
            <div class="element-number">10</div>
        </div>

        <div class="element" style="top: 650px; left: 600px; width: 250px; font-size: 14px;">
            Phase Space Plot<br>Representation of system state in G-D-R coordinates
            <div class="element-number">11</div>
        </div>

        <!-- Implementation -->
        <div class="element" style="top: 750px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Technical Implementation: NovaView and NovaVision
            <div class="element-number">12</div>
        </div>

        <!-- Connections -->
        <!-- Connect Dashboard Visualization to Real-time Dashboards -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Connect Real-time Dashboards to components -->
        <div class="connection" style="top: 200px; left: 275px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 275px; width: 25px; height: 2px;"></div>

        <div class="connection" style="top: 200px; left: 725px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 700px; width: 25px; height: 2px;"></div>

        <div class="connection" style="top: 300px; left: 275px; width: 2px; height: 50px;"></div>

        <div class="connection" style="top: 300px; left: 725px; width: 2px; height: 50px;"></div>

        <!-- Connect to Trinity Visualization -->
        <!-- Line removed as requested -->

        <!-- Connect Trinity Visualization to components -->
        <!-- Lines removed as requested -->

        <div class="connection" style="top: 600px; left: 275px; width: 2px; height: 50px;"></div>

        <div class="connection" style="top: 600px; left: 725px; width: 2px; height: 50px;"></div>

        <!-- Connect to Implementation -->
        <!-- Line removed as requested -->
    </div>
</body>
</html>
