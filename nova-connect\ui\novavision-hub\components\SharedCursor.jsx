/**
 * SharedCursor Component
 * 
 * A component for displaying shared cursors in collaborative sessions.
 */

import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useCollaboration } from '../collaboration/CollaborationContext';
import { useAuth } from '../auth/AuthContext';
import { Animated } from './Animated';

/**
 * SharedCursor component
 * 
 * @param {Object} props - Component props
 * @param {string} props.containerId - ID of the container element
 * @param {string} [props.roomId] - Room ID (if not using the current room from context)
 * @param {boolean} [props.showNames=true] - Whether to show user names
 * @param {boolean} [props.showInactive=false] - Whether to show inactive cursors
 * @param {number} [props.inactiveTimeout=5000] - Timeout in milliseconds before a cursor is considered inactive
 * @param {number} [props.updateInterval=100] - Interval in milliseconds for cursor position updates
 * @param {string} [props.className] - Additional CSS class names
 * @returns {React.ReactElement} SharedCursor component
 */
const SharedCursor = ({
  containerId,
  roomId: externalRoomId,
  showNames = true,
  showInactive = false,
  inactiveTimeout = 5000,
  updateInterval = 100,
  className = ''
}) => {
  const { user } = useAuth();
  const {
    isConnected,
    currentRoom,
    cursors,
    updateCursorPosition
  } = useCollaboration();
  
  // State
  const [containerRect, setContainerRect] = useState(null);
  const [localCursor, setLocalCursor] = useState({ x: 0, y: 0 });
  const [isMouseInContainer, setIsMouseInContainer] = useState(false);
  
  // Refs
  const containerRef = useRef(null);
  const updateTimerRef = useRef(null);
  const lastUpdateRef = useRef(null);
  
  // Get room ID
  const roomId = externalRoomId || (currentRoom ? currentRoom.id : null);
  
  // Update container rect
  const updateContainerRect = () => {
    if (containerRef.current) {
      setContainerRect(containerRef.current.getBoundingClientRect());
    }
  };
  
  // Initialize container ref and rect
  useEffect(() => {
    containerRef.current = document.getElementById(containerId);
    
    if (containerRef.current) {
      updateContainerRect();
      
      // Add resize listener
      const resizeObserver = new ResizeObserver(updateContainerRect);
      resizeObserver.observe(containerRef.current);
      
      // Add mouse event listeners
      const handleMouseMove = (e) => {
        if (!containerRect) return;
        
        const x = (e.clientX - containerRect.left) / containerRect.width;
        const y = (e.clientY - containerRect.top) / containerRect.height;
        
        setLocalCursor({ x, y });
      };
      
      const handleMouseEnter = () => {
        setIsMouseInContainer(true);
      };
      
      const handleMouseLeave = () => {
        setIsMouseInContainer(false);
      };
      
      containerRef.current.addEventListener('mousemove', handleMouseMove);
      containerRef.current.addEventListener('mouseenter', handleMouseEnter);
      containerRef.current.addEventListener('mouseleave', handleMouseLeave);
      
      // Cleanup
      return () => {
        resizeObserver.disconnect();
        
        if (containerRef.current) {
          containerRef.current.removeEventListener('mousemove', handleMouseMove);
          containerRef.current.removeEventListener('mouseenter', handleMouseEnter);
          containerRef.current.removeEventListener('mouseleave', handleMouseLeave);
        }
      };
    }
  }, [containerId, containerRect]);
  
  // Update cursor position
  useEffect(() => {
    if (!isConnected || !roomId || !user || !isMouseInContainer) {
      return;
    }
    
    const updateCursor = () => {
      const now = Date.now();
      
      // Throttle updates
      if (!lastUpdateRef.current || now - lastUpdateRef.current >= updateInterval) {
        updateCursorPosition(roomId, localCursor);
        lastUpdateRef.current = now;
      }
      
      updateTimerRef.current = requestAnimationFrame(updateCursor);
    };
    
    updateTimerRef.current = requestAnimationFrame(updateCursor);
    
    return () => {
      if (updateTimerRef.current) {
        cancelAnimationFrame(updateTimerRef.current);
      }
    };
  }, [isConnected, roomId, user, localCursor, isMouseInContainer, updateCursorPosition, updateInterval]);
  
  // Filter and render cursors
  const renderCursors = () => {
    if (!roomId || !containerRect || !cursors[roomId]) {
      return null;
    }
    
    const now = Date.now();
    
    return Object.entries(cursors[roomId])
      .filter(([userId, cursor]) => {
        // Don't show current user's cursor
        if (userId === user?.id) {
          return false;
        }
        
        // Check if cursor is active
        if (!showInactive) {
          const timestamp = new Date(cursor.timestamp).getTime();
          return now - timestamp < inactiveTimeout;
        }
        
        return true;
      })
      .map(([userId, cursor]) => {
        const timestamp = new Date(cursor.timestamp).getTime();
        const isActive = now - timestamp < inactiveTimeout;
        
        // Calculate position
        const x = cursor.x * containerRect.width;
        const y = cursor.y * containerRect.height;
        
        return (
          <Animated
            key={userId}
            animation="fadeIn"
            className={`absolute pointer-events-none ${className}`}
            style={{
              left: `${x}px`,
              top: `${y}px`,
              opacity: isActive ? 1 : 0.3,
              transform: 'translate(-50%, -50%)',
              zIndex: 1000
            }}
          >
            <div className="relative">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="filter drop-shadow-md"
              >
                <path
                  d="M5.64124 1.43129C5.28997 1.23443 4.86358 1.22178 4.5 1.39887C4.13642 1.57595 3.9 1.91631 3.9 2.29129V21.7087C3.9 22.0837 4.13642 22.424 4.5 22.6011C4.86358 22.7782 5.28997 22.7655 5.64124 22.5687L22.6412 13.3687C22.9699 13.1838 23.1667 12.8369 23.1667 12.5C23.1667 12.1631 22.9699 11.8162 22.6412 11.6313L5.64124 2.43129C5.64123 2.43129 5.64125 2.4313 5.64124 2.43129L5.64124 1.43129ZM5.64124 1.43129L5.64124 2.43129C5.64125 2.4313 5.64123 2.43129 5.64124 2.43129L5.64124 1.43129Z"
                  fill="#3B82F6"
                  stroke="#3B82F6"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              
              {showNames && (
                <div
                  className="absolute left-6 top-0 bg-primary text-primaryContrast text-xs py-1 px-2 rounded whitespace-nowrap"
                  style={{ transform: 'translateY(-50%)' }}
                >
                  {cursors[roomId][userId].userDisplayName || 'Unknown User'}
                </div>
              )}
            </div>
          </Animated>
        );
      });
  };
  
  return (
    <div className="shared-cursors">
      {renderCursors()}
    </div>
  );
};

SharedCursor.propTypes = {
  containerId: PropTypes.string.isRequired,
  roomId: PropTypes.string,
  showNames: PropTypes.bool,
  showInactive: PropTypes.bool,
  inactiveTimeout: PropTypes.number,
  updateInterval: PropTypes.number,
  className: PropTypes.string
};

export default SharedCursor;
