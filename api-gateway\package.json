{"name": "novafuse-api-gateway", "version": "1.0.0", "description": "API Gateway for NovaFuse API Superstore", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["api", "gateway", "novafuse", "superstore"], "author": "NovaFuse", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "helmet": "^6.1.5", "http-proxy-middleware": "^2.0.6", "morgan": "^1.10.0", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}}