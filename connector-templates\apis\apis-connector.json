{"extends": "base-connector", "name": "novafuse-apis-connector", "version": "1.0.0", "description": "APIs, iPaaS & Developer Tools connector template for NovaFuse API Superstore", "category": "apis", "base_url": "http://localhost:8000/apis", "endpoints": [{"name": "api_catalog", "path": "/catalog", "method": "GET", "description": "Get a catalog of available APIs", "parameters": [{"name": "category", "in": "query", "required": false, "description": "Filter by API category"}, {"name": "status", "in": "query", "required": false, "description": "Filter by status (active, deprecated, beta)"}]}, {"name": "api_details", "path": "/catalog/{id}", "method": "GET", "description": "Get details of a specific API", "parameters": [{"name": "id", "in": "path", "required": true, "description": "API ID"}]}, {"name": "api_metrics", "path": "/metrics", "method": "GET", "description": "Get API usage metrics", "parameters": [{"name": "api_id", "in": "query", "required": false, "description": "Filter by API ID"}, {"name": "period", "in": "query", "required": false, "description": "Time period (day, week, month, year)"}]}, {"name": "integration_flows", "path": "/integration/flows", "method": "GET", "description": "Get a list of integration flows", "parameters": [{"name": "status", "in": "query", "required": false, "description": "Filter by status (active, inactive, draft)"}, {"name": "category", "in": "query", "required": false, "description": "Filter by flow category"}]}, {"name": "integration_flow_details", "path": "/integration/flows/{id}", "method": "GET", "description": "Get details of a specific integration flow", "parameters": [{"name": "id", "in": "path", "required": true, "description": "Flow ID"}]}, {"name": "api_keys", "path": "/keys", "method": "GET", "description": "Get a list of API keys", "parameters": [{"name": "status", "in": "query", "required": false, "description": "Filter by status (active, revoked, expired)"}]}, {"name": "developer_resources", "path": "/resources", "method": "GET", "description": "Get a list of developer resources", "parameters": [{"name": "type", "in": "query", "required": false, "description": "Filter by resource type (documentation, sample, tutorial)"}, {"name": "api_id", "in": "query", "required": false, "description": "Filter by API ID"}]}, {"name": "api_test", "path": "/test", "method": "POST", "description": "Test an API endpoint", "parameters": []}]}