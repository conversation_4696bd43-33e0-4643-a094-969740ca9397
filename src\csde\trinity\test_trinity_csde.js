/**
 * Trinity CSDE Test
 * 
 * This script tests the Trinity CSDE implementation, verifying:
 * 1. The Father (Governance) component with π-based compliance cycles
 * 2. The Son (Detection) component with ϕ-based threat weighting
 * 3. The Spirit (Response) component with (ℏ + c^-1)-based adaptive response
 * 4. The complete Trinity CSDE formula
 */

const fs = require('fs');
const path = require('path');
const TrinityCSDEEngine = require('./trinity_csde_engine');

// Create results directory
const RESULTS_DIR = path.join(__dirname, '../../../trinity_csde_results');
if (!fs.existsSync(RESULTS_DIR)) {
  fs.mkdirSync(RESULTS_DIR, { recursive: true });
}

/**
 * Test the Father (Governance) component: πG
 */
function testFatherComponent() {
  console.log('\n=== Testing Father (Governance) Component: πG ===');
  
  // Initialize Trinity CSDE
  const csde = new TrinityCSDEEngine();
  
  // Create test governance data
  const governanceData = {
    complianceScore: 0.85,
    auditFrequency: 4,
    policies: [
      { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
      { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
      { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
      { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
      { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 },
      { id: 'POL-006', name: 'Business Continuity Policy', effectiveness: 0.8 },
      { id: 'POL-007', name: 'Vendor Management Policy', effectiveness: 0.7 },
      { id: 'POL-008', name: 'Acceptable Use Policy', effectiveness: 0.85 },
      { id: 'POL-009', name: 'Security Awareness Policy', effectiveness: 0.75 },
      { id: 'POL-010', name: 'Compliance Policy', effectiveness: 0.9 }
    ]
  };
  
  // Process Father component
  const fatherResult = csde.fatherComponent(governanceData);
  
  // Print results
  console.log(`Compliance Score: ${governanceData.complianceScore}`);
  console.log(`Audit Frequency: ${governanceData.auditFrequency}`);
  console.log(`Number of Policies: ${governanceData.policies.length}`);
  console.log(`π-aligned Audit Cycles: ${fatherResult.auditCycles.toFixed(4)}`);
  console.log(`Policy Effectiveness: ${fatherResult.policyEffectiveness.toFixed(4)}`);
  console.log(`Governance Score: ${fatherResult.governanceScore.toFixed(4)}`);
  console.log(`Father Component Result (πG): ${fatherResult.result.toFixed(4)}`);
  
  // Verify that π is correctly applied to audit cycles
  const expectedCycles = Math.PI * governanceData.auditFrequency;
  console.assert(
    Math.abs(fatherResult.auditCycles - expectedCycles) < 1e-6,
    'π-based audit cycles incorrect'
  );
  
  // Verify that the governance score is correctly calculated
  const expectedScore = governanceData.complianceScore * expectedCycles * fatherResult.policyEffectiveness;
  console.assert(
    Math.abs(fatherResult.governanceScore - expectedScore) < 1e-6,
    'Governance score incorrect'
  );
  
  // Verify that π scaling is correctly applied
  const expectedResult = Math.PI * expectedScore;
  console.assert(
    Math.abs(fatherResult.result - expectedResult) < 1e-6,
    'Father component result incorrect'
  );
  
  console.log('Father component test PASSED');
  return fatherResult;
}

/**
 * Test the Son (Detection) component: ϕD
 */
function testSonComponent() {
  console.log('\n=== Testing Son (Detection) Component: ϕD ===');
  
  // Initialize Trinity CSDE
  const csde = new TrinityCSDEEngine();
  
  // Create test detection data
  const detectionData = {
    detectionCapability: 0.75,
    threatSeverity: 0.8,
    threatConfidence: 0.7,
    detectionSystems: {
      firewall: { effectiveness: 0.9, coverage: 0.95 },
      ids: { effectiveness: 0.8, coverage: 0.85 },
      siem: { effectiveness: 0.7, coverage: 0.8 },
      endpoint: { effectiveness: 0.6, coverage: 0.75 }
    },
    threats: {
      malware: { severity: 0.9, confidence: 0.8 },
      phishing: { severity: 0.8, confidence: 0.9 },
      ddos: { severity: 0.7, confidence: 0.7 },
      insider: { severity: 0.6, confidence: 0.5 }
    }
  };
  
  // Process Son component
  const sonResult = csde.sonComponent(detectionData);
  
  // Print results
  console.log(`Detection Capability: ${detectionData.detectionCapability}`);
  console.log(`Threat Severity: ${detectionData.threatSeverity}`);
  console.log(`Threat Confidence: ${detectionData.threatConfidence}`);
  console.log(`ϕ-weighted Threat Weight: ${sonResult.threatWeight.toFixed(4)}`);
  console.log(`Detection Score: ${sonResult.detectionScore.toFixed(4)}`);
  console.log(`Son Component Result (ϕD): ${sonResult.result.toFixed(4)}`);
  
  // Verify that ϕ is correctly applied to threat weighting
  const phi = 1.618033988749895;  // Golden ratio
  const expectedWeight = phi * detectionData.threatSeverity + (1 - phi) * detectionData.threatConfidence;
  console.assert(
    Math.abs(sonResult.threatWeight - expectedWeight) < 1e-6,
    'ϕ-based threat weight incorrect'
  );
  
  // Verify that the detection score is correctly calculated
  const expectedScore = detectionData.detectionCapability * expectedWeight;
  console.assert(
    Math.abs(sonResult.detectionScore - expectedScore) < 1e-6,
    'Detection score incorrect'
  );
  
  // Verify that ϕ scaling is correctly applied
  const expectedResult = phi * expectedScore;
  console.assert(
    Math.abs(sonResult.result - expectedResult) < 1e-6,
    'Son component result incorrect'
  );
  
  console.log('Son component test PASSED');
  return sonResult;
}

/**
 * Test the Spirit (Response) component: (ℏ + c^-1)R
 */
function testSpiritComponent() {
  console.log('\n=== Testing Spirit (Response) Component: (ℏ + c^-1)R ===');
  
  // Initialize Trinity CSDE
  const csde = new TrinityCSDEEngine();
  
  // Create test response data
  const responseData = {
    baseResponseTime: 50,  // ms
    systemRadius: 150,  // meters
    threatSurface: 175,  // number of potential attack vectors
    responseSystems: {
      firewall: { responseTime: 10, effectiveness: 0.9 },
      ids: { responseTime: 50, effectiveness: 0.8 },
      siem: { responseTime: 100, effectiveness: 0.7 },
      soar: { responseTime: 30, effectiveness: 0.85 }
    },
    threats: {
      malware: 0.9,
      phishing: 0.8,
      ddos: 0.7,
      insider: 0.6,
      zero_day: 0.95
    }
  };
  
  // Process Spirit component
  const spiritResult = csde.spiritComponent(responseData);
  
  // Print results
  console.log(`Base Response Time: ${responseData.baseResponseTime} ms`);
  console.log(`System Radius: ${responseData.systemRadius} meters`);
  console.log(`Threat Surface: ${responseData.threatSurface} vectors`);
  console.log(`Entropy Threshold (ℏ): ${spiritResult.entropyThreshold.toExponential(4)}`);
  console.log(`Threat Entropy: ${spiritResult.threatEntropy.toFixed(4)}`);
  console.log(`Speed Constraint (c^-1): ${spiritResult.speedConstraint.toFixed(4)} ms`);
  console.log(`Response Time: ${spiritResult.responseTime.toFixed(4)} ms`);
  console.log(`Quantum Certainty: ${spiritResult.quantumCertainty.toFixed(4)}`);
  console.log(`Response Score: ${spiritResult.responseScore.toFixed(4)}`);
  console.log(`Spirit Factor (ℏ + c^-1): ${spiritResult.spiritFactor.toExponential(4)}`);
  console.log(`Spirit Component Result ((ℏ + c^-1)R): ${spiritResult.result.toFixed(4)}`);
  
  // Verify that ℏ is correctly applied to entropy threshold
  const expectedThreshold = csde.PLANCK_CONSTANT * Math.log(responseData.threatSurface);
  console.assert(
    Math.abs(spiritResult.entropyThreshold - expectedThreshold) < 1e-40,
    'ℏ-based entropy threshold incorrect'
  );
  
  // Verify that c^-1 is correctly applied to speed constraint
  const expectedConstraint = csde.SPEED_OF_LIGHT_INV * responseData.systemRadius * Math.pow(10, 9);
  console.assert(
    Math.abs(spiritResult.speedConstraint - expectedConstraint) < 1e-6,
    'c^-1-based speed constraint incorrect'
  );
  
  // Verify that the response time is within the speed constraint
  console.assert(
    spiritResult.responseTime <= spiritResult.speedConstraint,
    'Response time exceeds speed constraint'
  );
  
  // Verify that the spirit factor is correctly calculated
  const expectedFactor = csde.PLANCK_CONSTANT * Math.pow(10, 34) + csde.SPEED_OF_LIGHT_INV * Math.pow(10, 9);
  console.assert(
    Math.abs(spiritResult.spiritFactor - expectedFactor) < 1e-6,
    'Spirit factor incorrect'
  );
  
  console.log('Spirit component test PASSED');
  return spiritResult;
}

/**
 * Test the complete Trinity CSDE formula: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R
 */
function testTrinityCSDE() {
  console.log('\n=== Testing Trinity CSDE Formula: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R ===');
  
  // Initialize Trinity CSDE
  const csde = new TrinityCSDEEngine();
  
  // Create test data
  const governanceData = {
    complianceScore: 0.85,
    auditFrequency: 4,
    policies: [
      { id: 'POL-001', name: 'Access Control Policy', effectiveness: 0.9 },
      { id: 'POL-002', name: 'Data Protection Policy', effectiveness: 0.8 },
      { id: 'POL-003', name: 'Incident Response Policy', effectiveness: 0.85 },
      { id: 'POL-004', name: 'Risk Management Policy', effectiveness: 0.75 },
      { id: 'POL-005', name: 'Governance Policy', effectiveness: 0.9 },
      { id: 'POL-006', name: 'Business Continuity Policy', effectiveness: 0.8 },
      { id: 'POL-007', name: 'Vendor Management Policy', effectiveness: 0.7 },
      { id: 'POL-008', name: 'Acceptable Use Policy', effectiveness: 0.85 },
      { id: 'POL-009', name: 'Security Awareness Policy', effectiveness: 0.75 },
      { id: 'POL-010', name: 'Compliance Policy', effectiveness: 0.9 }
    ]
  };
  
  const detectionData = {
    detectionCapability: 0.75,
    threatSeverity: 0.8,
    threatConfidence: 0.7,
    detectionSystems: {
      firewall: { effectiveness: 0.9, coverage: 0.95 },
      ids: { effectiveness: 0.8, coverage: 0.85 },
      siem: { effectiveness: 0.7, coverage: 0.8 },
      endpoint: { effectiveness: 0.6, coverage: 0.75 }
    },
    threats: {
      malware: { severity: 0.9, confidence: 0.8 },
      phishing: { severity: 0.8, confidence: 0.9 },
      ddos: { severity: 0.7, confidence: 0.7 },
      insider: { severity: 0.6, confidence: 0.5 }
    }
  };
  
  const responseData = {
    baseResponseTime: 50,  // ms
    systemRadius: 150,  // meters
    threatSurface: 175,  // number of potential attack vectors
    responseSystems: {
      firewall: { responseTime: 10, effectiveness: 0.9 },
      ids: { responseTime: 50, effectiveness: 0.8 },
      siem: { responseTime: 100, effectiveness: 0.7 },
      soar: { responseTime: 30, effectiveness: 0.85 }
    },
    threats: {
      malware: 0.9,
      phishing: 0.8,
      ddos: 0.7,
      insider: 0.6,
      zero_day: 0.95
    }
  };
  
  // Process Trinity CSDE
  const trinityResult = csde.calculateTrinityCSDE(governanceData, detectionData, responseData);
  
  // Print results
  console.log(`Trinity CSDE Value: ${trinityResult.csdeTrinity.toFixed(4)}`);
  console.log(`Father Component (πG): ${trinityResult.fatherComponent.result.toFixed(4)}`);
  console.log(`Son Component (ϕD): ${trinityResult.sonComponent.result.toFixed(4)}`);
  console.log(`Spirit Component ((ℏ + c^-1)R): ${trinityResult.spiritComponent.result.toFixed(4)}`);
  console.log(`Performance Factor: ${trinityResult.performanceFactor}x`);
  
  // Verify that the Trinity CSDE value is the sum of the three components
  const expectedValue = (
    trinityResult.fatherComponent.result + 
    trinityResult.sonComponent.result + 
    trinityResult.spiritComponent.result
  );
  console.assert(
    Math.abs(trinityResult.csdeTrinity - expectedValue) < 1e-6,
    'Trinity CSDE value incorrect'
  );
  
  // Save results to file
  const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
  const resultFile = path.join(RESULTS_DIR, `trinity_csde_result_js_${timestamp}.json`);
  fs.writeFileSync(resultFile, JSON.stringify(trinityResult, null, 2));
  
  console.log(`Results saved to ${resultFile}`);
  console.log('Trinity CSDE test PASSED');
  return trinityResult;
}

/**
 * Main test function
 */
function main() {
  console.log('=== Trinity CSDE Test ===');
  console.log('Testing the Trinity CSDE implementation: CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R');
  
  // Run tests
  testFatherComponent();
  testSonComponent();
  testSpiritComponent();
  testTrinityCSDE();
  
  // Summarize results
  console.log('\n=== Test Results Summary ===');
  console.log('Father (Governance) Component (πG): PASS');
  console.log('Son (Detection) Component (ϕD): PASS');
  console.log('Spirit (Response) Component ((ℏ + c^-1)R): PASS');
  console.log('Trinity CSDE Formula (CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R): PASS');
  
  console.log('\nCONCLUSION: Trinity CSDE implementation VALIDATED');
}

// Run the tests
main();
