/**
 * NovaFuse Universal API Connector - Error Handling Middleware
 * 
 * This module provides middleware for handling errors.
 */

const { createLogger } = require('../utils/logger');
const { AppError } = require('../errors');

const logger = createLogger('error-middleware');

/**
 * Error handling middleware
 * 
 * @param {Error} err - The error object
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 */
function errorHandler(err, req, res, next) {
  // Log error
  logger.error(`Error handling request: ${err.message}`, {
    error: err,
    url: req.url,
    method: req.method,
    ip: req.ip,
    user: req.user ? req.user.id : 'anonymous'
  });
  
  // Check if error is an AppError
  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      error: {
        message: err.message,
        code: err.code,
        details: err.details
      }
    });
  }
  
  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      error: {
        message: 'Invalid token',
        code: 'INVALID_TOKEN'
      }
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      error: {
        message: 'Token expired',
        code: 'TOKEN_EXPIRED'
      }
    });
  }
  
  // Handle validation errors
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      error: {
        message: 'Validation error',
        code: 'VALIDATION_ERROR',
        details: err.details || err.message
      }
    });
  }
  
  // Handle other errors
  return res.status(500).json({
    error: {
      message: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
      code: 'INTERNAL_ERROR'
    }
  });
}

/**
 * Not found middleware
 * 
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
function notFound(req, res) {
  logger.warn(`Route not found: ${req.method} ${req.url}`, {
    ip: req.ip,
    user: req.user ? req.user.id : 'anonymous'
  });
  
  res.status(404).json({
    error: {
      message: `Route not found: ${req.method} ${req.url}`,
      code: 'NOT_FOUND'
    }
  });
}

module.exports = {
  errorHandler,
  notFound
};
