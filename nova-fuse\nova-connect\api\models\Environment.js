/**
 * NovaFuse Universal API Connector Environment Model
 * 
 * This model defines the schema for environments in the NovaConnect UAC.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define environment schema
const environmentSchema = new Schema({
  name: { 
    type: String, 
    required: true, 
    trim: true 
  },
  description: { 
    type: String, 
    trim: true 
  },
  type: { 
    type: String, 
    enum: ['development', 'testing', 'staging', 'production', 'custom'], 
    default: 'development' 
  },
  color: { 
    type: String, 
    default: '#3498db' 
  },
  icon: { 
    type: String 
  },
  variables: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    value: { 
      type: String, 
      required: true 
    },
    isSecret: { 
      type: Boolean, 
      default: false 
    },
    description: { 
      type: String, 
      trim: true 
    }
  }],
  baseUrls: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    url: { 
      type: String, 
      required: true, 
      trim: true 
    },
    description: { 
      type: String, 
      trim: true 
    }
  }],
  headers: [{
    name: { 
      type: String, 
      required: true, 
      trim: true 
    },
    value: { 
      type: String, 
      required: true 
    },
    isSecret: { 
      type: Boolean, 
      default: false 
    },
    description: { 
      type: String, 
      trim: true 
    }
  }],
  status: { 
    type: String, 
    enum: ['active', 'inactive', 'archived'], 
    default: 'active' 
  },
  createdBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  updatedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  teamId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Team' 
  },
  metadata: { 
    type: Object 
  }
}, {
  timestamps: true
});

// Add method to get variable by name
environmentSchema.methods.getVariableByName = function(name) {
  return this.variables.find(variable => variable.name === name);
};

// Add method to get variable value by name
environmentSchema.methods.getVariableValue = function(name) {
  const variable = this.getVariableByName(name);
  return variable ? variable.value : null;
};

// Add method to get base URL by name
environmentSchema.methods.getBaseUrlByName = function(name) {
  return this.baseUrls.find(baseUrl => baseUrl.name === name);
};

// Add method to get base URL value by name
environmentSchema.methods.getBaseUrlValue = function(name) {
  const baseUrl = this.getBaseUrlByName(name);
  return baseUrl ? baseUrl.url : null;
};

// Add method to get header by name
environmentSchema.methods.getHeaderByName = function(name) {
  return this.headers.find(header => header.name === name);
};

// Add method to get header value by name
environmentSchema.methods.getHeaderValue = function(name) {
  const header = this.getHeaderByName(name);
  return header ? header.value : null;
};

// Add method to check if environment is active
environmentSchema.methods.isActive = function() {
  return this.status === 'active';
};

// Add method to check if environment is archived
environmentSchema.methods.isArchived = function() {
  return this.status === 'archived';
};

// Add method to archive environment
environmentSchema.methods.archive = function(userId) {
  this.status = 'archived';
  this.updatedBy = userId;
  return this.save();
};

// Add method to activate environment
environmentSchema.methods.activate = function(userId) {
  this.status = 'active';
  this.updatedBy = userId;
  return this.save();
};

// Add indexes
environmentSchema.index({ name: 1, teamId: 1 }, { unique: true });
environmentSchema.index({ type: 1 });
environmentSchema.index({ status: 1 });
environmentSchema.index({ createdBy: 1 });
environmentSchema.index({ teamId: 1 });

// Create model
const Environment = mongoose.model('Environment', environmentSchema);

module.exports = Environment;
