/**
 * ScreenReaderText Component
 * 
 * A component for providing text that is only visible to screen readers.
 */

import React from 'react';
import PropTypes from 'prop-types';

/**
 * ScreenReaderText component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.id] - Element ID
 * @param {string} [props.component='span'] - Component to render
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} ScreenReaderText component
 */
const ScreenReaderText = ({
  children,
  id,
  component: Component = 'span',
  style = {},
  ...rest
}) => {
  return (
    <Component
      id={id}
      className="sr-only"
      style={{
        position: 'absolute',
        width: '1px',
        height: '1px',
        padding: '0',
        margin: '-1px',
        overflow: 'hidden',
        clip: 'rect(0, 0, 0, 0)',
        whiteSpace: 'nowrap',
        borderWidth: '0',
        ...style
      }}
      {...rest}
    >
      {children}
    </Component>
  );
};

ScreenReaderText.propTypes = {
  children: PropTypes.node.isRequired,
  id: PropTypes.string,
  component: PropTypes.elementType,
  style: PropTypes.object
};

export default ScreenReaderText;
