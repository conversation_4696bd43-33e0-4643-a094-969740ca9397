import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  FormControl, 
  FormLabel, 
  RadioGroup, 
  Radio, 
  FormControlLabel, 
  Checkbox, 
  Select, 
  MenuItem, 
  InputLabel, 
  Divider, 
  Alert, 
  Snackbar, 
  CircularProgress, 
  Grid 
} from '@mui/material';
import { Send as SendIcon, Save as SaveIcon } from '@mui/icons-material';
import axios from 'axios';

// API base URL
const API_BASE_URL = '/api';

/**
 * Participant Recruitment Page
 * 
 * This page allows users to sign up for user testing sessions.
 */
function ParticipantRecruitmentPage() {
  // State for form data
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    age: '',
    gender: '',
    role: '',
    company: '',
    industry: '',
    experience: 'beginner',
    deviceTypes: [],
    visualizationInterest: [],
    availability: [],
    preferredContactMethod: 'email',
    additionalInfo: '',
    consentGiven: false
  });
  
  // State for form validation
  const [formErrors, setFormErrors] = useState({});
  
  // State for form submission
  const [submitting, setSubmitting] = useState(false);
  
  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // State for form completion
  const [formCompleted, setFormCompleted] = useState(false);
  
  // Handle form field change
  const handleFieldChange = (field, value) => {
    setFormData(prevData => ({
      ...prevData,
      [field]: value
    }));
    
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        [field]: null
      }));
    }
  };
  
  // Handle checkbox change
  const handleCheckboxChange = (field, value) => {
    setFormData(prevData => {
      const currentValues = [...(prevData[field] || [])];
      
      if (currentValues.includes(value)) {
        // Remove value if already selected
        return {
          ...prevData,
          [field]: currentValues.filter(item => item !== value)
        };
      } else {
        // Add value if not selected
        return {
          ...prevData,
          [field]: [...currentValues, value]
        };
      }
    });
    
    // Clear error for this field
    if (formErrors[field]) {
      setFormErrors(prevErrors => ({
        ...prevErrors,
        [field]: null
      }));
    }
  };
  
  // Validate form
  const validateForm = () => {
    const errors = {};
    
    // Required fields
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      errors.email = 'Invalid email address';
    }
    
    if (!formData.consentGiven) {
      errors.consentGiven = 'You must give consent to participate';
    }
    
    if (formData.availability.length === 0) {
      errors.availability = 'Please select at least one availability option';
    }
    
    if (formData.visualizationInterest.length === 0) {
      errors.visualizationInterest = 'Please select at least one visualization of interest';
    }
    
    setFormErrors(errors);
    
    return Object.keys(errors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (event) => {
    event.preventDefault();
    
    // Validate form
    if (!validateForm()) {
      setSnackbar({
        open: true,
        message: 'Please correct the errors in the form',
        severity: 'error'
      });
      return;
    }
    
    try {
      setSubmitting(true);
      
      // Submit form data to API
      const response = await axios.post(`${API_BASE_URL}/user-testing/participants`, formData);
      
      // Show success message
      setSnackbar({
        open: true,
        message: 'Thank you for signing up! We will contact you soon.',
        severity: 'success'
      });
      
      // Set form as completed
      setFormCompleted(true);
    } catch (error) {
      console.error('Error submitting form:', error);
      
      // Show error message
      setSnackbar({
        open: true,
        message: 'Failed to submit form. Please try again later.',
        severity: 'error'
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };
  
  // Reset form
  const handleReset = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      age: '',
      gender: '',
      role: '',
      company: '',
      industry: '',
      experience: 'beginner',
      deviceTypes: [],
      visualizationInterest: [],
      availability: [],
      preferredContactMethod: 'email',
      additionalInfo: '',
      consentGiven: false
    });
    
    setFormErrors({});
    setFormCompleted(false);
  };
  
  // Render thank you message
  const renderThankYou = () => {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h4" gutterBottom>
          Thank You!
        </Typography>
        
        <Typography paragraph>
          Thank you for signing up to participate in our user testing sessions for the Cyber-Safety visualizations.
        </Typography>
        
        <Typography paragraph>
          We will contact you soon with more information about the testing sessions.
        </Typography>
        
        <Button
          variant="contained"
          color="primary"
          onClick={handleReset}
          sx={{ mt: 2 }}
        >
          Sign Up Another Participant
        </Button>
      </Paper>
    );
  };
  
  // Render recruitment form
  const renderForm = () => {
    return (
      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom>
          Cyber-Safety Visualization User Testing
        </Typography>
        
        <Typography paragraph>
          We are looking for participants to help us test and improve our Cyber-Safety visualizations.
          Please fill out the form below to sign up for user testing sessions.
        </Typography>
        
        <Divider sx={{ my: 3 }} />
        
        <Box component="form" onSubmit={handleSubmit}>
          <Typography variant="h6" gutterBottom>
            Personal Information
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Name"
                value={formData.name}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                fullWidth
                margin="normal"
                required
                error={!!formErrors.name}
                helperText={formErrors.name}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFieldChange('email', e.target.value)}
                fullWidth
                margin="normal"
                required
                error={!!formErrors.email}
                helperText={formErrors.email}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleFieldChange('phone', e.target.value)}
                fullWidth
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                label="Age"
                type="number"
                value={formData.age}
                onChange={(e) => handleFieldChange('age', e.target.value)}
                fullWidth
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Gender</InputLabel>
                <Select
                  value={formData.gender}
                  onChange={(e) => handleFieldChange('gender', e.target.value)}
                  label="Gender"
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                  <MenuItem value="non-binary">Non-binary</MenuItem>
                  <MenuItem value="prefer-not-to-say">Prefer not to say</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="h6" gutterBottom>
            Professional Information
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Role/Position"
                value={formData.role}
                onChange={(e) => handleFieldChange('role', e.target.value)}
                fullWidth
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                label="Company/Organization"
                value={formData.company}
                onChange={(e) => handleFieldChange('company', e.target.value)}
                fullWidth
                margin="normal"
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Industry</InputLabel>
                <Select
                  value={formData.industry}
                  onChange={(e) => handleFieldChange('industry', e.target.value)}
                  label="Industry"
                >
                  <MenuItem value="technology">Technology</MenuItem>
                  <MenuItem value="finance">Finance</MenuItem>
                  <MenuItem value="healthcare">Healthcare</MenuItem>
                  <MenuItem value="education">Education</MenuItem>
                  <MenuItem value="government">Government</MenuItem>
                  <MenuItem value="manufacturing">Manufacturing</MenuItem>
                  <MenuItem value="retail">Retail</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl component="fieldset" margin="normal">
                <FormLabel component="legend">Experience with data visualizations</FormLabel>
                <RadioGroup
                  value={formData.experience}
                  onChange={(e) => handleFieldChange('experience', e.target.value)}
                >
                  <FormControlLabel value="beginner" control={<Radio />} label="Beginner" />
                  <FormControlLabel value="intermediate" control={<Radio />} label="Intermediate" />
                  <FormControlLabel value="advanced" control={<Radio />} label="Advanced" />
                </RadioGroup>
              </FormControl>
            </Grid>
          </Grid>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="h6" gutterBottom>
            Testing Preferences
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl component="fieldset" margin="normal" error={!!formErrors.deviceTypes}>
                <FormLabel component="legend">Device types you use regularly</FormLabel>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.deviceTypes.includes('desktop')}
                        onChange={() => handleCheckboxChange('deviceTypes', 'desktop')}
                      />
                    }
                    label="Desktop"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.deviceTypes.includes('laptop')}
                        onChange={() => handleCheckboxChange('deviceTypes', 'laptop')}
                      />
                    }
                    label="Laptop"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.deviceTypes.includes('tablet')}
                        onChange={() => handleCheckboxChange('deviceTypes', 'tablet')}
                      />
                    }
                    label="Tablet"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.deviceTypes.includes('smartphone')}
                        onChange={() => handleCheckboxChange('deviceTypes', 'smartphone')}
                      />
                    }
                    label="Smartphone"
                  />
                </Box>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl component="fieldset" margin="normal" error={!!formErrors.visualizationInterest}>
                <FormLabel component="legend">Visualizations you are interested in testing</FormLabel>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.visualizationInterest.includes('triDomainTensor')}
                        onChange={() => handleCheckboxChange('visualizationInterest', 'triDomainTensor')}
                      />
                    }
                    label="Tri-Domain Tensor"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.visualizationInterest.includes('harmonyIndex')}
                        onChange={() => handleCheckboxChange('visualizationInterest', 'harmonyIndex')}
                      />
                    }
                    label="Harmony Index"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.visualizationInterest.includes('riskControlFusion')}
                        onChange={() => handleCheckboxChange('visualizationInterest', 'riskControlFusion')}
                      />
                    }
                    label="Risk-Control Fusion"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.visualizationInterest.includes('resonanceSpectrogram')}
                        onChange={() => handleCheckboxChange('visualizationInterest', 'resonanceSpectrogram')}
                      />
                    }
                    label="Resonance Spectrogram"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.visualizationInterest.includes('unifiedComplianceSecurity')}
                        onChange={() => handleCheckboxChange('visualizationInterest', 'unifiedComplianceSecurity')}
                      />
                    }
                    label="Unified Compliance-Security"
                  />
                </Box>
                {formErrors.visualizationInterest && (
                  <Typography variant="caption" color="error">
                    {formErrors.visualizationInterest}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl component="fieldset" margin="normal" error={!!formErrors.availability}>
                <FormLabel component="legend">Availability for testing</FormLabel>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.availability.includes('weekday-morning')}
                        onChange={() => handleCheckboxChange('availability', 'weekday-morning')}
                      />
                    }
                    label="Weekday Mornings"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.availability.includes('weekday-afternoon')}
                        onChange={() => handleCheckboxChange('availability', 'weekday-afternoon')}
                      />
                    }
                    label="Weekday Afternoons"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.availability.includes('weekday-evening')}
                        onChange={() => handleCheckboxChange('availability', 'weekday-evening')}
                      />
                    }
                    label="Weekday Evenings"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.availability.includes('weekend')}
                        onChange={() => handleCheckboxChange('availability', 'weekend')}
                      />
                    }
                    label="Weekends"
                  />
                </Box>
                {formErrors.availability && (
                  <Typography variant="caption" color="error">
                    {formErrors.availability}
                  </Typography>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Preferred Contact Method</InputLabel>
                <Select
                  value={formData.preferredContactMethod}
                  onChange={(e) => handleFieldChange('preferredContactMethod', e.target.value)}
                  label="Preferred Contact Method"
                >
                  <MenuItem value="email">Email</MenuItem>
                  <MenuItem value="phone">Phone</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                label="Additional Information"
                multiline
                rows={4}
                value={formData.additionalInfo}
                onChange={(e) => handleFieldChange('additionalInfo', e.target.value)}
                fullWidth
                margin="normal"
                placeholder="Please share any additional information that might be relevant for the testing sessions."
              />
            </Grid>
          </Grid>
          
          <Divider sx={{ my: 3 }} />
          
          <FormControlLabel
            control={
              <Checkbox
                checked={formData.consentGiven}
                onChange={(e) => handleFieldChange('consentGiven', e.target.checked)}
                required
              />
            }
            label="I consent to participate in user testing sessions and allow my feedback to be used for research and product improvement."
          />
          {formErrors.consentGiven && (
            <Typography variant="caption" color="error" display="block">
              {formErrors.consentGiven}
            </Typography>
          )}
          
          <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
            <Button
              type="button"
              variant="outlined"
              onClick={handleReset}
              startIcon={<SaveIcon />}
            >
              Reset Form
            </Button>
            
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={submitting}
              endIcon={submitting ? <CircularProgress size={20} /> : <SendIcon />}
            >
              {submitting ? 'Submitting...' : 'Submit'}
            </Button>
          </Box>
        </Box>
      </Paper>
    );
  };
  
  return (
    <Box sx={{ p: 2, maxWidth: 1200, mx: 'auto' }}>
      {formCompleted ? renderThankYou() : renderForm()}
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default ParticipantRecruitmentPage;
