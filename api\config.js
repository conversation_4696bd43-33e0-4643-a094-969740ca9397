/**
 * API Configuration
 * 
 * This file contains configuration settings for the NovaFuse API.
 */

const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from .env file
dotenv.config({ path: path.join(__dirname, '../.env') });

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    apiPrefix: process.env.API_PREFIX || '/api/v1'
  },
  
  // Database configuration
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/novafuse',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useCreateIndex: true,
      useFindAndModify: false
    }
  },
  
  // JWT configuration
  jwt: {
    secretKey: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d'
  },
  
  // Blockchain configuration
  blockchain: {
    provider: process.env.BLOCKCHAIN_PROVIDER || 'http://localhost:8545',
    contractAddress: process.env.BLOCKCHAIN_CONTRACT_ADDRESS || '',
    privateKey: process.env.BLOCKCHAIN_PRIVATE_KEY || ''
  },
  
  // NovaConnect configuration
  novaConnect: {
    baseUrl: process.env.NOVA_CONNECT_URL || 'http://localhost:3001',
    apiKey: process.env.NOVA_CONNECT_API_KEY || ''
  },
  
  // NovaPulse configuration
  novaPulse: {
    baseUrl: process.env.NOVA_PULSE_URL || 'http://localhost:3002',
    apiKey: process.env.NOVA_PULSE_API_KEY || ''
  },
  
  // NovaFlow configuration
  novaFlow: {
    baseUrl: process.env.NOVA_FLOW_URL || 'http://localhost:3003',
    apiKey: process.env.NOVA_FLOW_API_KEY || ''
  },
  
  // NovaAssistAI configuration
  novaAssistAI: {
    baseUrl: process.env.NOVA_ASSIST_AI_URL || 'http://localhost:3004',
    apiKey: process.env.NOVA_ASSIST_AI_API_KEY || ''
  },
  
  // NovaSphere configuration
  novaSphere: {
    baseUrl: process.env.NOVA_SPHERE_URL || 'http://localhost:3005',
    apiKey: process.env.NOVA_SPHERE_API_KEY || ''
  },
  
  // File upload configuration
  fileUpload: {
    maxSize: process.env.FILE_UPLOAD_MAX_SIZE || 10 * 1024 * 1024, // 10MB
    allowedTypes: process.env.FILE_UPLOAD_ALLOWED_TYPES || 'image/jpeg,image/png,application/pdf,text/plain,application/json,application/xml',
    uploadDir: process.env.FILE_UPLOAD_DIR || path.join(__dirname, '../uploads')
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    dir: process.env.LOG_DIR || path.join(__dirname, '../logs')
  },
  
  // Email configuration
  email: {
    host: process.env.EMAIL_HOST || '',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER || '',
      pass: process.env.EMAIL_PASS || ''
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  }
};

module.exports = config;
