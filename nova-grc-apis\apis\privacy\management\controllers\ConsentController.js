/**
 * Consent Controller
 * 
 * Handles operations related to consent management.
 */

const Consent = require('../models/Consent');
const { validationResult } = require('express-validator');

/**
 * Get all consent records
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllConsents = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    // Build query based on filters
    const query = {};
    
    if (req.query.active) {
      query.active = req.query.active === 'true';
    }
    
    if (req.query.purpose) {
      query['processingPurposes.purpose'] = req.query.purpose;
    }
    
    // Count total documents for pagination
    const total = await Consent.countDocuments(query);
    
    // Get consent records with pagination
    const consents = await Consent.find(query)
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      count: consents.length,
      pagination: {
        total,
        page,
        pages: Math.ceil(total / limit)
      },
      data: consents
    });
  } catch (error) {
    console.error('Error in getAllConsents:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Get a single consent record by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConsent = async (req, res) => {
  try {
    const consent = await Consent.findById(req.params.id)
      .populate('relatedProcessingActivities');
    
    if (!consent) {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    res.status(200).json({
      success: true,
      data: consent
    });
  } catch (error) {
    console.error('Error in getConsent:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Create a new consent record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createConsent = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    // Add initial history entry
    if (!req.body.history) {
      req.body.history = [];
    }
    
    req.body.history.push({
      action: 'Created',
      timestamp: new Date(),
      details: 'Consent record created',
      performedBy: req.user ? 'Administrator' : 'System',
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });
    
    // Create new consent record
    const consent = await Consent.create(req.body);
    
    res.status(201).json({
      success: true,
      data: consent
    });
  } catch (error) {
    console.error('Error in createConsent:', error);
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Update a consent record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateConsent = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    let consent = await Consent.findById(req.params.id);
    
    if (!consent) {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    // Add history entry
    if (!req.body.history) {
      req.body.history = [...consent.history];
    }
    
    req.body.history.push({
      action: 'Updated',
      timestamp: new Date(),
      details: 'Consent record updated',
      performedBy: req.user ? 'Administrator' : 'System',
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });
    
    // Update consent record
    consent = await Consent.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    );
    
    res.status(200).json({
      success: true,
      data: consent
    });
  } catch (error) {
    console.error('Error in updateConsent:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    // Handle validation errors
    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      
      return res.status(400).json({
        success: false,
        errors: messages
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Delete a consent record
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteConsent = async (req, res) => {
  try {
    const consent = await Consent.findById(req.params.id);
    
    if (!consent) {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    await consent.remove();
    
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    console.error('Error in deleteConsent:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Withdraw consent for a specific purpose
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.withdrawConsent = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const consent = await Consent.findById(req.params.id);
    
    if (!consent) {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    // Withdraw consent for the specified purpose
    const success = consent.withdrawConsent(
      req.body.purpose,
      req.body.details,
      req.body.performedBy || 'Data Subject',
      req.ip,
      req.headers['user-agent']
    );
    
    if (!success) {
      return res.status(400).json({
        success: false,
        error: `Purpose '${req.body.purpose}' not found in consent record`
      });
    }
    
    await consent.save();
    
    res.status(200).json({
      success: true,
      data: consent
    });
  } catch (error) {
    console.error('Error in withdrawConsent:', error);
    
    // Handle invalid ObjectId
    if (error.kind === 'ObjectId') {
      return res.status(404).json({
        success: false,
        error: 'Consent record not found'
      });
    }
    
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Validate consent for a specific purpose
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.validateConsent = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    // Find the most recent consent record for the data subject and purpose
    const consent = await Consent.findOne({
      'dataSubject.identifier': req.query.identifier,
      'dataSubject.identifierType': req.query.identifierType,
      'processingPurposes.purpose': req.query.purpose,
      active: true
    }).sort({ createdAt: -1 });
    
    if (!consent) {
      return res.status(200).json({
        success: true,
        data: {
          valid: false,
          reason: 'No consent record found'
        }
      });
    }
    
    // Check if consent is valid for the purpose
    const isValid = consent.isValidForPurpose(req.query.purpose);
    
    // Get the purpose entry
    const purposeEntry = consent.processingPurposes.find(p => p.purpose === req.query.purpose);
    
    res.status(200).json({
      success: true,
      data: {
        valid: isValid,
        reason: isValid ? 'Consent is valid' : `Consent status is ${purposeEntry ? purposeEntry.status : 'not found'}`,
        consentId: consent._id,
        timestamp: purposeEntry ? purposeEntry.timestamp : null,
        expiryDate: consent.expiryDate
      }
    });
  } catch (error) {
    console.error('Error in validateConsent:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};

/**
 * Get all consent records for a data subject
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getConsentsByDataSubject = async (req, res) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    });
  }
  
  try {
    const consents = await Consent.find({
      'dataSubject.identifier': req.query.identifier,
      'dataSubject.identifierType': req.query.identifierType
    }).sort({ createdAt: -1 });
    
    res.status(200).json({
      success: true,
      count: consents.length,
      data: consents
    });
  } catch (error) {
    console.error('Error in getConsentsByDataSubject:', error);
    res.status(500).json({
      success: false,
      error: 'Server Error'
    });
  }
};
