/**
 * Integration tests for the NovaConnect API
 */

const { expect } = require('chai');
const supertest = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// Import the app
const app = require('../../server');

describe('NovaConnect API Integration Tests', () => {
  let mongoServer;
  let request;

  before(async () => {
    // Create an in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();

    // Connect to the in-memory database
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Create a supertest request object
    request = supertest(app);
  });

  after(async () => {
    // Disconnect from the database
    await mongoose.disconnect();
    // Stop the in-memory MongoDB server
    await mongoServer.stop();
  });

  describe('Health Check', () => {
    it('should return a 200 status code', async () => {
      const response = await request.get('/health');
      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('status');
      expect(response.body.status).to.equal('ok');
    });
  });

  describe('API Routes', () => {
    it('should return the API information', async () => {
      const response = await request.get('/');
      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('name');
      expect(response.body.name).to.equal('NovaConnect');
      expect(response.body).to.have.property('version');
      expect(response.body).to.have.property('description');
      expect(response.body).to.have.property('links');
      expect(response.body.links).to.be.an('array');
    });
  });

  describe('Connectors API', () => {
    // Test data
    const testConnector = {
      name: 'Test Connector',
      type: 'http',
      description: 'Test connector for integration tests',
      config: {
        baseUrl: 'https://api.example.com',
        authentication: {
          type: 'api_key',
          header: 'X-API-Key'
        }
      },
      schema: {
        input: {
          type: 'object',
          properties: {
            query: {
              type: 'string'
            }
          }
        },
        output: {
          type: 'object',
          properties: {
            results: {
              type: 'array'
            }
          }
        }
      }
    };

    let connectorId;

    it('should create a new connector', async () => {
      const response = await request
        .post('/api/connectors')
        .send(testConnector);

      expect(response.status).to.equal(201);
      expect(response.body).to.have.property('id');
      expect(response.body.name).to.equal(testConnector.name);
      expect(response.body.type).to.equal(testConnector.type);
      expect(response.body.description).to.equal(testConnector.description);

      // Save the connector ID for later tests
      connectorId = response.body.id;
    });

    it('should get all connectors', async () => {
      const response = await request.get('/api/connectors');

      expect(response.status).to.equal(200);
      expect(response.body).to.have.property('data');
      expect(response.body.data).to.be.an('array');
      expect(response.body.data.length).to.be.at.least(1);
      expect(response.body.data[0].name).to.equal(testConnector.name);
    });

    it('should get a connector by ID', async () => {
      const response = await request.get(`/api/connectors/${connectorId}`);

      expect(response.status).to.equal(200);
      expect(response.body.id).to.equal(connectorId);
      expect(response.body.name).to.equal(testConnector.name);
      expect(response.body.type).to.equal(testConnector.type);
      expect(response.body.description).to.equal(testConnector.description);
    });

    it('should update a connector', async () => {
      const updatedConnector = {
        name: 'Updated Test Connector',
        description: 'Updated test connector for integration tests'
      };

      const response = await request
        .put(`/api/connectors/${connectorId}`)
        .send(updatedConnector);

      expect(response.status).to.equal(200);
      expect(response.body.id).to.equal(connectorId);
      expect(response.body.name).to.equal(updatedConnector.name);
      expect(response.body.description).to.equal(updatedConnector.description);
      expect(response.body.type).to.equal(testConnector.type); // Type should not change
    });

    it('should delete a connector', async () => {
      const response = await request.delete(`/api/connectors/${connectorId}`);

      expect(response.status).to.equal(204);

      // Verify the connector was deleted
      const getResponse = await request.get(`/api/connectors/${connectorId}`);
      expect(getResponse.status).to.equal(404);
    });
  });
});
