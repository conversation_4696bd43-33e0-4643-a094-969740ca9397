/**
 * ProductContext
 * 
 * A React context for managing the current product.
 */

import { createContext, useState, useEffect } from 'react';

// Define the available products
export const PRODUCTS = {
  NOVA_PRIME: 'novaPrime',
  NOVA_CORE: 'novaCore',
  NOVA_SHIELD: 'novaShield',
  NOVA_LEARN: 'novaLearn',
  NOVA_ASSIST_AI: 'novaAssistAI',
  NOVA_MARKETPLACE: 'novaMarketplace'
};

// Create the context
export const ProductContext = createContext({
  product: null,
  setProduct: () => {},
  isProductActive: () => false
});

/**
 * ProductProvider component
 * @param {object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {string} [props.initialProduct] - Initial product
 * @returns {React.ReactNode} - Provider component
 */
export function ProductProvider({ children, initialProduct }) {
  // Get the initial product from props, localStorage, URL parameters, or default
  const getInitialProduct = () => {
    // Use the initialProduct prop if provided
    if (initialProduct && Object.values(PRODUCTS).includes(initialProduct)) {
      return initialProduct;
    }
    
    // Check URL parameters
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const productParam = urlParams.get('product');
      
      if (productParam && Object.values(PRODUCTS).includes(productParam)) {
        return productParam;
      }
      
      // Check localStorage
      const storedProduct = localStorage.getItem('novaProduct');
      
      if (storedProduct && Object.values(PRODUCTS).includes(storedProduct)) {
        return storedProduct;
      }
    }
    
    // Default to NovaPrime
    return PRODUCTS.NOVA_PRIME;
  };
  
  const [product, setProduct] = useState(null);
  
  // Initialize the product on mount
  useEffect(() => {
    const initialProductValue = getInitialProduct();
    setProduct(initialProductValue);
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('novaProduct', initialProductValue);
    }
  }, []);
  
  // Update localStorage when product changes
  useEffect(() => {
    if (product && typeof window !== 'undefined') {
      localStorage.setItem('novaProduct', product);
    }
  }, [product]);
  
  /**
   * Check if a product is active
   * @param {string} productToCheck - The product to check
   * @returns {boolean} - Whether the product is active
   */
  const isProductActive = (productToCheck) => {
    return product === productToCheck;
  };
  
  return (
    <ProductContext.Provider value={{ product, setProduct, isProductActive }}>
      {children}
    </ProductContext.Provider>
  );
}

export default ProductContext;
