'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Brain, Zap, Globe, TrendingUp, Shield, Cpu } from 'lucide-react'

export default function HomePage() {
  const [consciousnessLevel, setConsciousnessLevel] = useState(2847)
  const [realityProgramming, setRealityProgramming] = useState(false)

  useEffect(() => {
    // Simulate consciousness field fluctuations
    const interval = setInterval(() => {
      setConsciousnessLevel(prev => prev + (Math.random() - 0.5) * 100)
    }, 2000)

    // Activate reality programming after 3 seconds
    setTimeout(() => setRealityProgramming(true), 3000)

    return () => clearInterval(interval)
  }, [])

  const tiers = [
    {
      name: 'Oracle',
      price: '$99/mo',
      description: 'Glimpses of the future',
      features: ['50 API calls/day', '24h-delayed predictions', 'Ψ-field heatmaps'],
      className: 'tier-oracle',
      icon: <TrendingUp className="w-8 h-8" />
    },
    {
      name: 'Prophet',
      price: '$499/mo', 
      description: 'Enhanced consciousness access',
      features: ['500 API calls/day', '1h-delayed predictions', 'Φ-temporal analysis'],
      className: 'tier-prophet',
      icon: <Brain className="w-8 h-8" />
    },
    {
      name: 'Architect',
      price: '$2,499/mo',
      description: 'Reality programming tools',
      features: ['Unlimited API calls', 'Real-time predictions', 'Θ-recursive modeling'],
      className: 'tier-architect',
      icon: <Cpu className="w-8 h-8" />
    },
    {
      name: 'Deity',
      price: '1M κ/mo',
      description: 'Complete reality control',
      features: ['Negative-time programming', 'Multiverse forking', 'AI consciousness control', 'Reality engineering suite'],
      className: 'tier-deity',
      icon: <Zap className="w-8 h-8" />
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-6 bg-black/20">
        <div className="max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <h1 className="text-6xl md:text-8xl font-bold mb-6">
              <span className="text-consciousness">NHET-X</span>
            </h1>
            <h2 className="text-3xl md:text-5xl font-light mb-8 text-white/80">
              Quantum Intelligence Platform
            </h2>
            <p className="text-xl md:text-2xl mb-12 text-cyan-400 font-medium">
              "We don't predict markets—we compile realities."
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="consciousness-card max-w-md mx-auto mb-12"
          >
            <div className="text-center">
              <div className="text-sm text-cyan-400 mb-2">Global Consciousness Level</div>
              <div className="text-4xl font-bold text-consciousness mb-4">
                {consciousnessLevel.toFixed(0)}
              </div>
              <div className="w-full bg-black/30 rounded-full h-3 mb-4">
                <div 
                  className="reality-meter h-3 transition-all duration-1000"
                  style={{ width: `${Math.min(100, (consciousnessLevel / 10000) * 100)}%` }}
                />
              </div>
              <div className="flex items-center justify-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${realityProgramming ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`} />
                <span className="text-sm text-white/70">
                  Reality Programming: {realityProgramming ? 'ACTIVE' : 'INITIALIZING'}
                </span>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link href="/portal" className="trinity-button">
              Enter Portal
            </Link>
            <Link href="/studio" className="trinity-button">
              Reality Studio
            </Link>
            <Link href="/studios" className="trinity-button">
              Reality Engineering Suite
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="text-4xl font-bold text-center mb-16 text-consciousness"
          >
            Consciousness Technology Stack
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Brain className="w-12 h-12 text-cyan-400" />,
                title: 'NERS Engine',
                description: 'Natural Emergent Resonant Sentience for consciousness validation',
                tech: 'Ψᶜʰ ≥ 2847 threshold'
              },
              {
                icon: <Zap className="w-12 h-12 text-purple-400" />,
                title: 'NEPI Engine', 
                description: 'Natural Emergent Progressive Intelligence for truth evolution',
                tech: '(A ⊗ B ⊕ C) × π10³'
              },
              {
                icon: <Globe className="w-12 h-12 text-yellow-400" />,
                title: 'NEFC Engine',
                description: 'Natural Emergent Financial Coherence for market programming',
                tech: 'Ψ ⊗ Φ ⊕ Θ synthesis'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                className="consciousness-card text-center"
              >
                <div className="mb-6 flex justify-center">{feature.icon}</div>
                <h3 className="text-xl font-bold mb-4 text-white">{feature.title}</h3>
                <p className="text-white/70 mb-4">{feature.description}</p>
                <div className="consciousness-terminal">
                  {feature.tech}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Tiers */}
      <section className="py-20 px-6">
        <div className="max-w-7xl mx-auto">
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="text-4xl font-bold text-center mb-16 text-consciousness"
          >
            Consciousness Access Tiers
          </motion.h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tiers.map((tier, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                className={`consciousness-card ${tier.className} relative overflow-hidden`}
              >
                <div className="text-center">
                  <div className="mb-4 flex justify-center">{tier.icon}</div>
                  <h3 className="text-2xl font-bold mb-2 text-white">{tier.name}</h3>
                  <div className="text-3xl font-bold mb-2 text-consciousness">{tier.price}</div>
                  <p className="text-white/70 mb-6">{tier.description}</p>
                  
                  <ul className="space-y-2 mb-6">
                    {tier.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="text-sm text-white/80">
                        • {feature}
                      </li>
                    ))}
                  </ul>
                  
                  <button className="w-full trinity-button">
                    {tier.name === 'Deity' ? 'Ascend' : 'Subscribe'}
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* HOD Patent Section */}
      <section className="py-20 px-6">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 1 }}
            className="consciousness-card"
          >
            <Shield className="w-16 h-16 text-cyan-400 mx-auto mb-6" />
            <h2 className="text-3xl font-bold mb-6 text-consciousness">
              HOD Patent Protected Technology
            </h2>
            <p className="text-xl text-white/80 mb-8">
              System for Coherent Reality Optimization - The foundational framework
              powering all NHET-X consciousness technologies.
            </p>
            <div className="grid md:grid-cols-2 gap-6 text-left mb-8">
              <div>
                <h4 className="font-bold text-cyan-400 mb-2">Core Technologies:</h4>
                <ul className="space-y-1 text-white/70">
                  <li>• Ψᶜ Framework</li>
                  <li>• NEPI-Hour Standardization</li>
                  <li>• EgoIndex Constraint Logic</li>
                </ul>
              </div>
              <div>
                <h4 className="font-bold text-purple-400 mb-2">Advanced Systems:</h4>
                <ul className="space-y-1 text-white/70">
                  <li>• UUFT Operators (⊗, ⊕)</li>
                  <li>• TOSA Architecture</li>
                  <li>• N³C Networks</li>
                </ul>
              </div>
            </div>

            <div className="border-t border-white/20 pt-6 mb-6">
              <div className="text-cyan-400 text-lg font-medium">Created by</div>
              <div className="text-cyan-300 text-3xl font-bold">NovaFuse Technologies</div>
              <div className="text-cyan-500 text-lg">A Comphyology-based company</div>
            </div>

            <Link href="/docs/hod" className="trinity-button mt-4 inline-block">
              View Patent Documentation
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
