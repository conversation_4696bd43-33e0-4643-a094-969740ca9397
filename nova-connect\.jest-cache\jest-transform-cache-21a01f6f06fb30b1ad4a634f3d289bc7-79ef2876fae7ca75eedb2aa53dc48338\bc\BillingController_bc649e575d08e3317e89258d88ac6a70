8ab75c92b7e5c0a20c9ea26f85926a5d
/**
 * Billing Controller
 * 
 * This controller handles billing-related operations, including
 * GCP Marketplace entitlements and usage reporting.
 */

const BillingService = require('../services/BillingService');
const logger = require('../../config/logger');

// Initialize services
const billingService = new BillingService();

/**
 * Handle GCP Marketplace entitlement creation
 */
const handleEntitlementCreation = async (req, res, next) => {
  try {
    const {
      customerId,
      entitlement
    } = req.body;
    if (!customerId || !entitlement) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID and entitlement are required'
      });
    }
    await billingService.enableFeatures(customerId, entitlement);
    res.status(200).json({
      message: 'Entitlement created successfully',
      customerId,
      entitlement: {
        ...entitlement,
        status: 'ACTIVE'
      }
    });
  } catch (error) {
    logger.error('Error handling entitlement creation', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement update
 */
const handleEntitlementUpdate = async (req, res, next) => {
  try {
    const {
      customerId,
      entitlement
    } = req.body;
    if (!customerId || !entitlement) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID and entitlement are required'
      });
    }
    await billingService.updateFeatures(customerId, entitlement);
    res.status(200).json({
      message: 'Entitlement updated successfully',
      customerId,
      entitlement
    });
  } catch (error) {
    logger.error('Error handling entitlement update', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement deletion
 */
const handleEntitlementDeletion = async (req, res, next) => {
  try {
    const {
      customerId,
      entitlement
    } = req.body;
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    await billingService.disableFeatures(customerId, entitlement);
    res.status(200).json({
      message: 'Entitlement deleted successfully',
      customerId
    });
  } catch (error) {
    logger.error('Error handling entitlement deletion', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement activation
 */
const handleEntitlementActivation = async (req, res, next) => {
  try {
    const {
      customerId,
      entitlement
    } = req.body;
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    await billingService.activateFeatures(customerId, entitlement);
    res.status(200).json({
      message: 'Entitlement activated successfully',
      customerId
    });
  } catch (error) {
    logger.error('Error handling entitlement activation', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Handle GCP Marketplace entitlement suspension
 */
const handleEntitlementSuspension = async (req, res, next) => {
  try {
    const {
      customerId,
      entitlement
    } = req.body;
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    await billingService.suspendFeatures(customerId, entitlement);
    res.status(200).json({
      message: 'Entitlement suspended successfully',
      customerId
    });
  } catch (error) {
    logger.error('Error handling entitlement suspension', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Get customer entitlements
 */
const getCustomerEntitlements = async (req, res, next) => {
  try {
    const {
      customerId
    } = req.params;
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    const entitlement = await billingService.getCustomerEntitlements(customerId);
    res.status(200).json(entitlement);
  } catch (error) {
    logger.error('Error getting customer entitlements', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Get customer usage
 */
const getCustomerUsage = async (req, res, next) => {
  try {
    const {
      customerId
    } = req.params;
    const {
      startDate,
      endDate
    } = req.query;
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required'
      });
    }
    const usage = await billingService.getCustomerUsage(customerId, startDate, endDate);
    res.status(200).json(usage);
  } catch (error) {
    logger.error('Error getting customer usage', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Report usage
 */
const reportUsage = async (req, res, next) => {
  try {
    const {
      customerId,
      metricName,
      quantity,
      timestamp,
      tenantId
    } = req.body;
    if (!customerId || !metricName || quantity === undefined) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID, metric name, and quantity are required'
      });
    }
    await billingService.reportUsage(customerId, metricName, quantity, timestamp, tenantId);
    res.status(200).json({
      message: 'Usage reported successfully',
      customerId,
      metricName,
      quantity
    });
  } catch (error) {
    logger.error('Error reporting usage', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Report tenant usage
 */
const reportTenantUsage = async (req, res, next) => {
  try {
    const {
      tenantId,
      metricName,
      quantity,
      timestamp
    } = req.body;
    if (!tenantId || !metricName || quantity === undefined) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Tenant ID, metric name, and quantity are required'
      });
    }
    await billingService.reportTenantUsage(tenantId, metricName, quantity, timestamp);
    res.status(200).json({
      message: 'Tenant usage reported successfully',
      tenantId,
      metricName,
      quantity
    });
  } catch (error) {
    logger.error('Error reporting tenant usage', {
      error: error.message
    });
    next(error);
  }
};

/**
 * Handle GCP Marketplace webhook
 */
const handleMarketplaceWebhook = async (req, res, next) => {
  try {
    const {
      event,
      resource
    } = req.body;
    if (!event || !resource) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Event and resource are required'
      });
    }
    logger.info('Received GCP Marketplace webhook', {
      event,
      resource
    });

    // Extract customer ID from resource
    const customerId = resource.customerId || resource.customer_id;
    if (!customerId) {
      return res.status(400).json({
        error: 'Bad Request',
        message: 'Customer ID is required in resource'
      });
    }

    // Handle different event types
    switch (event) {
      case 'ENTITLEMENT_CREATION':
        await billingService.enableFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_UPDATE':
        await billingService.updateFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_DELETION':
        await billingService.disableFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_ACTIVATION':
        await billingService.activateFeatures(customerId, resource);
        break;
      case 'ENTITLEMENT_SUSPENSION':
        await billingService.suspendFeatures(customerId, resource);
        break;
      default:
        logger.warn('Unknown event type', {
          event
        });
        return res.status(400).json({
          error: 'Bad Request',
          message: `Unknown event type: ${event}`
        });
    }
    res.status(200).json({
      message: 'Webhook processed successfully',
      event,
      customerId
    });
  } catch (error) {
    logger.error('Error handling marketplace webhook', {
      error: error.message
    });
    next(error);
  }
};
module.exports = {
  handleEntitlementCreation,
  handleEntitlementUpdate,
  handleEntitlementDeletion,
  handleEntitlementActivation,
  handleEntitlementSuspension,
  getCustomerEntitlements,
  getCustomerUsage,
  reportUsage,
  reportTenantUsage,
  handleMarketplaceWebhook
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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