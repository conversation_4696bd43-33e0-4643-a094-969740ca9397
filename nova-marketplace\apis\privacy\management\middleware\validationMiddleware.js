/**
 * Validation Middleware
 * 
 * This middleware validates request data against <PERSON><PERSON> schemas.
 */

const logger = require('../config/logger');

/**
 * Validate request data against schema
 * @param {Object} schema - Joi schema object
 * @returns {Function} - Express middleware
 */
const validateRequest = (schema) => {
  return (req, res, next) => {
    if (!schema) {
      return next();
    }

    const validationErrors = {};

    // Validate request params
    if (schema.params) {
      const { error } = schema.params.validate(req.params, { abortEarly: false });
      if (error) {
        validationErrors.params = error.details.map(detail => ({
          message: detail.message,
          path: detail.path
        }));
      }
    }

    // Validate request query
    if (schema.query) {
      const { error } = schema.query.validate(req.query, { abortEarly: false });
      if (error) {
        validationErrors.query = error.details.map(detail => ({
          message: detail.message,
          path: detail.path
        }));
      }
    }

    // Validate request body
    if (schema.body) {
      const { error } = schema.body.validate(req.body, { abortEarly: false });
      if (error) {
        validationErrors.body = error.details.map(detail => ({
          message: detail.message,
          path: detail.path
        }));
      }
    }

    // If there are validation errors, return them
    if (Object.keys(validationErrors).length > 0) {
      logger.warn('Request validation failed', { validationErrors });
      
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: validationErrors
      });
    }

    next();
  };
};

module.exports = {
  validateRequest
};
