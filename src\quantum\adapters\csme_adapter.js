/**
 * CSME Adapter
 * 
 * This module provides an adapter for connecting the CSME (Cyber-Safety Medical Engine)
 * with the Comphyological Tensor Core.
 */

const { createComphyologicalTensorCore } = require('../tensor');

/**
 * CSMEAdapter class
 * 
 * Provides an adapter for connecting the CSME with the Comphyological Tensor Core.
 */
class CSMEAdapter {
  /**
   * Constructor
   * @param {Object} csmeEngine - CSME engine instance
   * @param {Object} options - Configuration options
   */
  constructor(csmeEngine, options = {}) {
    this.options = {
      enableLogging: true,
      strictMode: false,
      useGPU: false,
      useDynamicWeighting: true,
      ...options
    };

    this.csmeEngine = csmeEngine;
    this.tensorCore = createComphyologicalTensorCore(this.options);
    this.lastResult = null;
    this.metrics = {
      processCount: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastProcessingTime: 0,
      startTime: Date.now()
    };

    if (this.options.enableLogging) {
      console.log('CSMEAdapter initialized with options:', {
        strictMode: this.options.strictMode,
        useGPU: this.options.useGPU,
        useDynamicWeighting: this.options.useDynamicWeighting
      });
    }
  }

  /**
   * Process data through the CSME and Comphyological Tensor Core
   * @param {Object} csdeData - CSDE data
   * @param {Object} csfeData - CSFE data
   * @param {Object} csmeData - CSME data
   * @returns {Object} - Processing result
   */
  processData(csdeData, csfeData, csmeData) {
    const startTime = Date.now();

    try {
      // Process data through CSME engine if available
      let processedCsmeData = csmeData;
      if (this.csmeEngine && typeof this.csmeEngine.processData === 'function') {
        processedCsmeData = this.csmeEngine.processData(csmeData);
      }

      // Transform CSME data
      const transformedCsmeData = this._transformCsmeData(processedCsmeData);

      // Process data through tensor core
      this.lastResult = this.tensorCore.processData(
        csdeData,
        csfeData,
        transformedCsmeData
      );

      // Update metrics
      this._updateMetrics(startTime);

      return this.lastResult;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error('Error processing data through CSMEAdapter:', error);
      }

      if (this.options.strictMode) {
        throw error;
      }

      return {
        error: error.message,
        timestamp: Date.now(),
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Transform CSME data to format expected by tensor core
   * @param {Object} csmeData - CSME data
   * @returns {Object} - Transformed CSME data
   * @private
   */
  _transformCsmeData(csmeData) {
    return {
      trustFactor: csmeData.bioScore || csmeData.trustFactor || csmeData.bio || 0.5,
      integrityFactor: csmeData.medComplianceScore || csmeData.integrityFactor || csmeData.medCompliance || 0.5,
      action: csmeData.recommendedAction || csmeData.action || 'allow',
      confidence: csmeData.confidenceScore || csmeData.confidence || 0.5
    };
  }

  /**
   * Update metrics
   * @param {number} startTime - Start time
   * @private
   */
  _updateMetrics(startTime) {
    const processingTime = Date.now() - startTime;

    // Update metrics
    this.metrics.processCount++;
    this.metrics.lastProcessingTime = processingTime;
    this.metrics.totalProcessingTime += processingTime;
    this.metrics.averageProcessingTime = this.metrics.totalProcessingTime / this.metrics.processCount;
  }

  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      tensorCoreMetrics: this.tensorCore.getMetrics()
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      processCount: 0,
      averageProcessingTime: 0,
      totalProcessingTime: 0,
      lastProcessingTime: 0,
      startTime: Date.now()
    };

    this.tensorCore.resetMetrics();
  }

  /**
   * Get the last result
   * @returns {Object} - Last result
   */
  getLastResult() {
    return this.lastResult;
  }

  /**
   * Get the tensor core
   * @returns {Object} - Tensor core
   */
  getTensorCore() {
    return this.tensorCore;
  }
}

/**
 * Create a CSME adapter
 * @param {Object} csmeEngine - CSME engine instance
 * @param {Object} options - Configuration options
 * @returns {CSMEAdapter} - CSME adapter instance
 */
function createCSMEAdapter(csmeEngine, options = {}) {
  return new CSMEAdapter(csmeEngine, options);
}

module.exports = {
  CSMEAdapter,
  createCSMEAdapter
};
