/**
 * NovaCore Workflow Execution Model
 *
 * This model defines the schema for workflow executions in the NovaFlow module.
 * NovaFlow is the Universal Compliance Workflow Orchestrator (UCWO) component
 * of the NovaFuse Cyber-Safety Platform.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Define task execution schema
const taskExecutionSchema = new Schema({
  taskId: {
    type: String,
    required: true,
    trim: true
  },
  stageId: {
    type: String,
    required: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: [
      'pending',
      'in_progress',
      'completed',
      'failed',
      'skipped',
      'blocked'
    ],
    default: 'pending'
  },
  assignedTo: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedRole: {
    type: String,
    trim: true
  },
  startedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  dueAt: {
    type: Date
  },
  result: {
    type: Schema.Types.Mixed
  },
  output: {
    type: Schema.Types.Mixed
  },
  error: {
    message: {
      type: String,
      trim: true
    },
    code: {
      type: String,
      trim: true
    },
    stack: {
      type: String,
      trim: true
    },
    details: {
      type: Schema.Types.Mixed
    }
  },
  retries: {
    type: Number,
    default: 0
  },
  nextRetryAt: {
    type: Date
  },
  notes: {
    type: String,
    trim: true
  },
  executedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  automationDetails: {
    serviceType: {
      type: String,
      trim: true
    },
    actionName: {
      type: String,
      trim: true
    },
    parameters: {
      type: Map,
      of: Schema.Types.Mixed
    },
    executionId: {
      type: String,
      trim: true
    }
  },
  integrationDetails: {
    connectorId: {
      type: String,
      trim: true
    },
    endpoint: {
      type: String,
      trim: true
    },
    requestPayload: {
      type: Schema.Types.Mixed
    },
    responsePayload: {
      type: Schema.Types.Mixed
    },
    statusCode: {
      type: Number
    }
  },
  evidenceDetails: {
    evidenceId: {
      type: Schema.Types.ObjectId,
      ref: 'Evidence'
    },
    controlId: {
      type: String,
      trim: true
    },
    verificationStatus: {
      type: String,
      enum: ['pending', 'verified', 'failed'],
      default: 'pending'
    },
    verificationId: {
      type: Schema.Types.ObjectId
    }
  },
  decisionDetails: {
    condition: {
      type: String,
      trim: true
    },
    evaluationResult: {
      type: Boolean
    },
    nextTaskId: {
      type: String,
      trim: true
    }
  },
  approvalDetails: {
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: {
      type: Date
    },
    comments: {
      type: String,
      trim: true
    },
    evidence: [{
      type: Schema.Types.ObjectId,
      ref: 'Evidence'
    }]
  }
}, { _id: false });

// Define verification rule schema
const verificationRuleSchema = new Schema({
  id: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    trim: true
  },
  config: {
    type: Schema.Types.Mixed
  },
  weight: {
    type: Number,
    default: 1
  }
}, { _id: false });

// Define verification checkpoint schema
const verificationCheckpointSchema = new Schema({
  id: {
    type: String,
    required: true,
    trim: true
  },
  workflowId: {
    type: Schema.Types.ObjectId,
    ref: 'Workflow',
    required: true
  },
  stageId: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: [
      'pending',
      'in_progress',
      'passed',
      'failed',
      'error'
    ],
    default: 'pending'
  },
  rules: [verificationRuleSchema],
  requiredVerifications: {
    type: Number,
    default: 1
  },
  verificationLevel: {
    type: String,
    enum: [
      'basic',
      'standard',
      'enhanced',
      'strict'
    ],
    default: 'standard'
  },
  blockchainVerification: {
    type: Boolean,
    default: false
  },
  startedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  createdAt: {
    type: Date
  },
  verificationRecord: {
    type: Schema.Types.Mixed
  },
  error: {
    message: {
      type: String,
      trim: true
    },
    code: {
      type: String,
      trim: true
    },
    details: {
      type: Schema.Types.Mixed
    }
  }
}, { _id: false });

// Define stage execution schema
const stageExecutionSchema = new Schema({
  stageId: {
    type: String,
    required: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  status: {
    type: String,
    enum: [
      'pending',
      'in_progress',
      'completed',
      'failed',
      'skipped'
    ],
    default: 'pending'
  },
  startedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  dueAt: {
    type: Date
  },
  completionPercentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  notes: {
    type: String,
    trim: true
  }
}, { _id: false });

// Define workflow execution schema
const workflowExecutionSchema = new Schema({
  workflowId: {
    type: Schema.Types.ObjectId,
    ref: 'Workflow',
    required: true
  },
  organizationId: {
    type: Schema.Types.ObjectId,
    ref: 'Organization',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: [
      'pending',
      'in_progress',
      'completed',
      'failed',
      'paused',
      'cancelled'
    ],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  startedAt: {
    type: Date
  },
  completedAt: {
    type: Date
  },
  dueAt: {
    type: Date
  },
  completionPercentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  stages: [stageExecutionSchema],
  tasks: [taskExecutionSchema],
  checkpoints: [verificationCheckpointSchema],
  currentStageId: {
    type: String,
    trim: true
  },
  currentTaskIds: [{
    type: String,
    trim: true
  }],
  triggerType: {
    type: String,
    enum: [
      'manual',
      'scheduled',
      'event',
      'api',
      'condition'
    ],
    default: 'manual'
  },
  triggerDetails: {
    triggeredBy: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    eventType: {
      type: String,
      trim: true
    },
    eventSource: {
      type: String,
      trim: true
    },
    eventData: {
      type: Schema.Types.Mixed
    },
    scheduleId: {
      type: String,
      trim: true
    }
  },
  data: {
    type: Map,
    of: Schema.Types.Mixed
  },
  variables: {
    type: Map,
    of: Schema.Types.Mixed
  },
  result: {
    type: Schema.Types.Mixed
  },
  error: {
    message: {
      type: String,
      trim: true
    },
    code: {
      type: String,
      trim: true
    },
    details: {
      type: Schema.Types.Mixed
    }
  },
  relatedEntities: [{
    entityType: {
      type: String,
      required: true,
      trim: true
    },
    entityId: {
      type: Schema.Types.ObjectId,
      required: true
    }
  }],
  tags: [{
    type: String,
    trim: true
  }],
  notes: {
    type: String,
    trim: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  versionKey: true
});

// Add indexes
workflowExecutionSchema.index({ workflowId: 1 });
workflowExecutionSchema.index({ organizationId: 1 });
workflowExecutionSchema.index({ status: 1 });
workflowExecutionSchema.index({ priority: 1 });
workflowExecutionSchema.index({ startedAt: 1 });
workflowExecutionSchema.index({ completedAt: 1 });
workflowExecutionSchema.index({ dueAt: 1 });
workflowExecutionSchema.index({ 'stages.status': 1 });
workflowExecutionSchema.index({ 'tasks.status': 1 });
workflowExecutionSchema.index({ 'tasks.assignedTo': 1 });
workflowExecutionSchema.index({ 'checkpoints.status': 1 });
workflowExecutionSchema.index({ 'checkpoints.stageId': 1 });
workflowExecutionSchema.index({ 'relatedEntities.entityType': 1, 'relatedEntities.entityId': 1 });
workflowExecutionSchema.index({ tags: 1 });
workflowExecutionSchema.index({ createdAt: 1 });

// Add methods
workflowExecutionSchema.methods.isCompleted = function() {
  return this.status === 'completed';
};

workflowExecutionSchema.methods.isFailed = function() {
  return this.status === 'failed';
};

workflowExecutionSchema.methods.isInProgress = function() {
  return this.status === 'in_progress';
};

workflowExecutionSchema.methods.isPaused = function() {
  return this.status === 'paused';
};

workflowExecutionSchema.methods.getStage = function(stageId) {
  return this.stages.find(stage => stage.stageId === stageId);
};

workflowExecutionSchema.methods.getTask = function(taskId) {
  return this.tasks.find(task => task.taskId === taskId);
};

workflowExecutionSchema.methods.getPendingTasks = function() {
  return this.tasks.filter(task => task.status === 'pending');
};

workflowExecutionSchema.methods.getInProgressTasks = function() {
  return this.tasks.filter(task => task.status === 'in_progress');
};

workflowExecutionSchema.methods.getCompletedTasks = function() {
  return this.tasks.filter(task => task.status === 'completed');
};

workflowExecutionSchema.methods.getFailedTasks = function() {
  return this.tasks.filter(task => task.status === 'failed');
};

workflowExecutionSchema.methods.getCheckpoint = function(checkpointId) {
  if (!this.checkpoints || this.checkpoints.length === 0) {
    return null;
  }

  return this.checkpoints.find(checkpoint => checkpoint.id === checkpointId);
};

workflowExecutionSchema.methods.getCheckpointsForStage = function(stageId) {
  if (!this.checkpoints || this.checkpoints.length === 0) {
    return [];
  }

  return this.checkpoints.filter(checkpoint => checkpoint.stageId === stageId);
};

workflowExecutionSchema.methods.getPendingCheckpoints = function() {
  if (!this.checkpoints || this.checkpoints.length === 0) {
    return [];
  }

  return this.checkpoints.filter(checkpoint => checkpoint.status === 'pending');
};

workflowExecutionSchema.methods.getPassedCheckpoints = function() {
  if (!this.checkpoints || this.checkpoints.length === 0) {
    return [];
  }

  return this.checkpoints.filter(checkpoint => checkpoint.status === 'passed');
};

workflowExecutionSchema.methods.getFailedCheckpoints = function() {
  if (!this.checkpoints || this.checkpoints.length === 0) {
    return [];
  }

  return this.checkpoints.filter(checkpoint => checkpoint.status === 'failed');
};

workflowExecutionSchema.methods.calculateCompletionPercentage = function() {
  if (!this.tasks || this.tasks.length === 0) {
    return 0;
  }

  const totalTasks = this.tasks.length;
  const completedTasks = this.tasks.filter(task =>
    task.status === 'completed' || task.status === 'skipped'
  ).length;

  return Math.round((completedTasks / totalTasks) * 100);
};

// Add statics
workflowExecutionSchema.statics.findByOrganization = function(organizationId) {
  return this.find({ organizationId });
};

workflowExecutionSchema.statics.findActive = function(organizationId) {
  return this.find({
    organizationId,
    status: { $in: ['pending', 'in_progress', 'paused'] }
  });
};

workflowExecutionSchema.statics.findByWorkflow = function(workflowId) {
  return this.find({ workflowId });
};

workflowExecutionSchema.statics.findByRelatedEntity = function(entityType, entityId) {
  return this.find({
    'relatedEntities.entityType': entityType,
    'relatedEntities.entityId': entityId
  });
};

workflowExecutionSchema.statics.findDue = function(organizationId, date = new Date()) {
  return this.find({
    organizationId,
    status: { $in: ['pending', 'in_progress', 'paused'] },
    dueAt: { $lte: date }
  });
};

workflowExecutionSchema.statics.findByAssignee = function(userId) {
  return this.find({
    'tasks.assignedTo': userId,
    'tasks.status': { $in: ['pending', 'in_progress'] }
  });
};

// Create model
const WorkflowExecution = mongoose.model('WorkflowExecution', workflowExecutionSchema);

module.exports = WorkflowExecution;
