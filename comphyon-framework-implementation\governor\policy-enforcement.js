/**
 * Policy Enforcement
 * 
 * This module implements the Policy Enforcement component of the Governor.
 * It enforces policies across domains and ensures compliance.
 */

const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * PolicyEnforcement class
 */
class PolicyEnforcement extends EventEmitter {
  /**
   * Create a new PolicyEnforcement instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      historySize: 100, // Number of historical data points to keep
      ...options
    };
    
    // Initialize state
    this.state = {
      policies: new Map(), // id -> policy
      policyViolations: [],
      enforcementActions: [],
      isRunning: false,
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      policiesRegistered: 0,
      policiesEvaluated: 0,
      policyViolations: 0,
      enforcementActions: 0,
      violationsByDomain: {
        universal: 0,
        cyber: 0,
        financial: 0,
        biological: 0
      }
    };
    
    if (this.options.enableLogging) {
      console.log('PolicyEnforcement initialized');
    }
  }
  
  /**
   * Start the policy enforcement
   * @returns {boolean} - Success status
   */
  start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('PolicyEnforcement is already running');
      }
      return false;
    }
    
    this.state.isRunning = true;
    
    if (this.options.enableLogging) {
      console.log('PolicyEnforcement started');
    }
    
    this.emit('start');
    
    return true;
  }
  
  /**
   * Stop the policy enforcement
   * @returns {boolean} - Success status
   */
  stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('PolicyEnforcement is not running');
      }
      return false;
    }
    
    this.state.isRunning = false;
    
    if (this.options.enableLogging) {
      console.log('PolicyEnforcement stopped');
    }
    
    this.emit('stop');
    
    return true;
  }
  
  /**
   * Register policy
   * @param {Object} policy - Policy definition
   * @returns {Object} - Registered policy
   */
  registerPolicy(policy) {
    const startTime = performance.now();
    
    if (!policy || typeof policy !== 'object') {
      throw new Error('Policy must be an object');
    }
    
    if (!policy.id) {
      policy.id = `policy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    
    if (!policy.name) {
      throw new Error('Policy must have a name');
    }
    
    if (!policy.rules || !Array.isArray(policy.rules) || policy.rules.length === 0) {
      throw new Error('Policy must have at least one rule');
    }
    
    // Validate rules
    for (const rule of policy.rules) {
      if (!rule.id) {
        rule.id = `rule-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      }
      
      if (!rule.condition || typeof rule.condition !== 'function') {
        throw new Error('Each rule must have a condition function');
      }
      
      if (!rule.action || typeof rule.action !== 'function') {
        throw new Error('Each rule must have an action function');
      }
    }
    
    // Set default values
    policy = {
      domains: ['universal'], // universal, cyber, financial, biological
      description: `Policy: ${policy.name}`,
      priority: 'medium', // low, medium, high, critical
      enabled: true,
      registeredAt: Date.now(),
      ...policy
    };
    
    // Add to policies
    this.state.policies.set(policy.id, policy);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.policiesRegistered++;
    
    // Emit event
    this.emit('policy-registered', {
      policyId: policy.id,
      name: policy.name,
      domains: policy.domains,
      priority: policy.priority,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`PolicyEnforcement: Registered policy ${policy.id} (${policy.name})`);
    }
    
    return policy;
  }
  
  /**
   * Unregister policy
   * @param {string} policyId - Policy ID
   * @returns {boolean} - Success status
   */
  unregisterPolicy(policyId) {
    if (!policyId || !this.state.policies.has(policyId)) {
      return false;
    }
    
    // Get policy
    const policy = this.state.policies.get(policyId);
    
    // Remove from policies
    this.state.policies.delete(policyId);
    
    // Emit event
    this.emit('policy-unregistered', {
      policyId: policy.id,
      name: policy.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`PolicyEnforcement: Unregistered policy ${policy.id} (${policy.name})`);
    }
    
    return true;
  }
  
  /**
   * Enable policy
   * @param {string} policyId - Policy ID
   * @returns {boolean} - Success status
   */
  enablePolicy(policyId) {
    if (!policyId || !this.state.policies.has(policyId)) {
      return false;
    }
    
    // Get policy
    const policy = this.state.policies.get(policyId);
    
    // Enable policy
    policy.enabled = true;
    
    // Emit event
    this.emit('policy-enabled', {
      policyId: policy.id,
      name: policy.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`PolicyEnforcement: Enabled policy ${policy.id} (${policy.name})`);
    }
    
    return true;
  }
  
  /**
   * Disable policy
   * @param {string} policyId - Policy ID
   * @returns {boolean} - Success status
   */
  disablePolicy(policyId) {
    if (!policyId || !this.state.policies.has(policyId)) {
      return false;
    }
    
    // Get policy
    const policy = this.state.policies.get(policyId);
    
    // Disable policy
    policy.enabled = false;
    
    // Emit event
    this.emit('policy-disabled', {
      policyId: policy.id,
      name: policy.name,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`PolicyEnforcement: Disabled policy ${policy.id} (${policy.name})`);
    }
    
    return true;
  }
  
  /**
   * Evaluate data against policies
   * @param {string} domain - Domain (cyber, financial, biological, universal)
   * @param {Object} data - Data to evaluate
   * @param {Object} context - Evaluation context
   * @returns {Array} - Policy violations
   */
  evaluateData(domain, data, context = {}) {
    const startTime = performance.now();
    
    if (!this.state.isRunning) {
      throw new Error('PolicyEnforcement is not running');
    }
    
    if (!domain) {
      throw new Error('Domain is required');
    }
    
    if (!data) {
      throw new Error('Data is required');
    }
    
    // Find matching policies
    const matchingPolicies = this._findMatchingPolicies(domain);
    
    // Evaluate policies
    const violations = [];
    
    for (const policy of matchingPolicies) {
      const policyViolations = this._evaluatePolicy(policy, domain, data, context);
      violations.push(...policyViolations);
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.policiesEvaluated += matchingPolicies.length;
    this.metrics.policyViolations += violations.length;
    
    if (violations.length > 0) {
      this.metrics.violationsByDomain[domain] = (this.metrics.violationsByDomain[domain] || 0) + violations.length;
    }
    
    if (this.options.enableLogging && violations.length > 0) {
      console.log(`PolicyEnforcement: Found ${violations.length} policy violations in ${domain} domain`);
    }
    
    return violations;
  }
  
  /**
   * Enforce policy
   * @param {string} policyId - Policy ID
   * @param {string} domain - Domain (cyber, financial, biological, universal)
   * @param {Object} data - Data to enforce policy on
   * @param {Object} context - Enforcement context
   * @returns {Array} - Enforcement actions
   */
  enforcePolicy(policyId, domain, data, context = {}) {
    const startTime = performance.now();
    
    if (!this.state.isRunning) {
      throw new Error('PolicyEnforcement is not running');
    }
    
    if (!policyId || !this.state.policies.has(policyId)) {
      throw new Error(`Policy ${policyId} not found`);
    }
    
    if (!domain) {
      throw new Error('Domain is required');
    }
    
    if (!data) {
      throw new Error('Data is required');
    }
    
    // Get policy
    const policy = this.state.policies.get(policyId);
    
    // Check if policy is enabled
    if (!policy.enabled) {
      return [];
    }
    
    // Check if policy applies to domain
    if (!policy.domains.includes(domain) && !policy.domains.includes('universal')) {
      return [];
    }
    
    // Evaluate policy
    const violations = this._evaluatePolicy(policy, domain, data, context);
    
    // Enforce policy for violations
    const actions = [];
    
    for (const violation of violations) {
      const action = this._enforceViolation(violation, domain, data, context);
      
      if (action) {
        actions.push(action);
      }
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.enforcementActions += actions.length;
    
    if (this.options.enableLogging && actions.length > 0) {
      console.log(`PolicyEnforcement: Enforced ${actions.length} actions for policy ${policy.id} (${policy.name})`);
    }
    
    return actions;
  }
  
  /**
   * Get policies
   * @param {string} domain - Domain filter (all domains if not specified)
   * @returns {Array} - Policies
   */
  getPolicies(domain) {
    const policies = Array.from(this.state.policies.values());
    
    if (domain) {
      return policies.filter(p => p.domains.includes(domain) || p.domains.includes('universal'));
    }
    
    return policies;
  }
  
  /**
   * Get policy violations
   * @param {number} limit - Maximum number of violations to return
   * @returns {Array} - Policy violations
   */
  getPolicyViolations(limit = 10) {
    return this.state.policyViolations.slice(0, limit);
  }
  
  /**
   * Get enforcement actions
   * @param {number} limit - Maximum number of actions to return
   * @returns {Array} - Enforcement actions
   */
  getEnforcementActions(limit = 10) {
    return this.state.enforcementActions.slice(0, limit);
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Find matching policies for domain
   * @param {string} domain - Domain
   * @returns {Array} - Matching policies
   * @private
   */
  _findMatchingPolicies(domain) {
    const matchingPolicies = [];
    
    // Check each policy
    for (const policy of this.state.policies.values()) {
      // Skip disabled policies
      if (!policy.enabled) {
        continue;
      }
      
      // Check domain
      if (!policy.domains.includes(domain) && !policy.domains.includes('universal')) {
        continue;
      }
      
      // Policy matches
      matchingPolicies.push(policy);
    }
    
    return matchingPolicies;
  }
  
  /**
   * Evaluate policy
   * @param {Object} policy - Policy
   * @param {string} domain - Domain
   * @param {Object} data - Data
   * @param {Object} context - Context
   * @returns {Array} - Policy violations
   * @private
   */
  _evaluatePolicy(policy, domain, data, context) {
    const violations = [];
    
    // Check each rule
    for (const rule of policy.rules) {
      try {
        // Check condition
        const conditionResult = rule.condition(data, { domain, context });
        
        if (conditionResult) {
          // Rule violated
          const violation = {
            id: `violation-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            policyId: policy.id,
            policyName: policy.name,
            ruleId: rule.id,
            domain,
            data,
            context,
            timestamp: Date.now()
          };
          
          // Add to violations
          violations.push(violation);
          
          // Add to policy violations
          this.state.policyViolations.unshift(violation);
          
          // Limit policy violations
          if (this.state.policyViolations.length > this.options.historySize) {
            this.state.policyViolations.pop();
          }
          
          // Emit event
          this.emit('policy-violation', {
            violationId: violation.id,
            policyId: policy.id,
            policyName: policy.name,
            ruleId: rule.id,
            domain,
            timestamp: Date.now()
          });
          
          if (this.options.enableLogging) {
            console.log(`PolicyEnforcement: Policy violation for ${policy.id} (${policy.name}), rule ${rule.id}`);
          }
        }
      } catch (error) {
        if (this.options.enableLogging) {
          console.error(`PolicyEnforcement: Error evaluating rule ${rule.id} for policy ${policy.id}:`, error.message);
        }
      }
    }
    
    return violations;
  }
  
  /**
   * Enforce violation
   * @param {Object} violation - Policy violation
   * @param {string} domain - Domain
   * @param {Object} data - Data
   * @param {Object} context - Context
   * @returns {Object} - Enforcement action
   * @private
   */
  _enforceViolation(violation, domain, data, context) {
    // Get policy
    const policy = this.state.policies.get(violation.policyId);
    
    if (!policy) {
      return null;
    }
    
    // Find rule
    const rule = policy.rules.find(r => r.id === violation.ruleId);
    
    if (!rule) {
      return null;
    }
    
    try {
      // Execute action
      const actionResult = rule.action(data, { domain, context, violation });
      
      // Create enforcement action
      const action = {
        id: `action-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        violationId: violation.id,
        policyId: policy.id,
        policyName: policy.name,
        ruleId: rule.id,
        domain,
        result: actionResult,
        timestamp: Date.now()
      };
      
      // Add to enforcement actions
      this.state.enforcementActions.unshift(action);
      
      // Limit enforcement actions
      if (this.state.enforcementActions.length > this.options.historySize) {
        this.state.enforcementActions.pop();
      }
      
      // Emit event
      this.emit('policy-enforced', {
        actionId: action.id,
        violationId: violation.id,
        policyId: policy.id,
        policyName: policy.name,
        ruleId: rule.id,
        domain,
        timestamp: Date.now()
      });
      
      if (this.options.enableLogging) {
        console.log(`PolicyEnforcement: Enforced policy ${policy.id} (${policy.name}) for violation ${violation.id}`);
      }
      
      return action;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`PolicyEnforcement: Error enforcing rule ${rule.id} for policy ${policy.id}:`, error.message);
      }
      
      return null;
    }
  }
}

module.exports = PolicyEnforcement;
