/**
 * HelpPanel Component
 * 
 * A panel for displaying detailed help content.
 */

import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { useI18n } from '../i18n';
import { useAccessibility } from '../accessibility';
import { useHelp } from '../help/HelpContext';
import ReactMarkdown from 'react-markdown';

/**
 * HelpPanel component
 * 
 * @param {Object} props - Component props
 * @param {string} [props.position='right'] - Panel position
 * @param {boolean} [props.showSearch=true] - Whether to show search
 * @param {boolean} [props.showToc=true] - Whether to show table of contents
 * @param {boolean} [props.showRelated=true] - Whether to show related help
 * @param {Function} [props.onClose] - Function to call when panel is closed
 * @param {string} [props.className=''] - Additional CSS class names
 * @param {Object} [props.style={}] - Additional inline styles
 * @returns {React.ReactElement} HelpPanel component
 */
const HelpPanel = ({
  position = 'right',
  showSearch = true,
  showToc = true,
  showRelated = true,
  onClose,
  className = '',
  style = {}
}) => {
  // Hooks
  const { translate } = useI18n();
  const { settings } = useAccessibility();
  const {
    isPanelOpen,
    activeHelpId,
    helpContent,
    closeHelpPanel,
    searchHelp,
    searchQuery,
    searchResults
  } = useHelp();
  
  // State
  const [currentHelpId, setCurrentHelpId] = useState(activeHelpId);
  const [currentContent, setCurrentContent] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState('');
  
  // Refs
  const searchInputRef = useRef(null);
  const panelRef = useRef(null);
  
  // Update current help ID when active help ID changes
  useEffect(() => {
    if (activeHelpId) {
      setCurrentHelpId(activeHelpId);
    }
  }, [activeHelpId]);
  
  // Update current content when current help ID changes
  useEffect(() => {
    if (currentHelpId && helpContent[currentHelpId]) {
      setCurrentContent(helpContent[currentHelpId]);
    } else {
      setCurrentContent(null);
    }
  }, [currentHelpId, helpContent]);
  
  // Update local search query when search query changes
  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);
  
  // Focus search input when searching
  useEffect(() => {
    if (isSearching && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isSearching]);
  
  // Handle escape key to close panel
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };
    
    if (isPanelOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isPanelOpen]);
  
  // If panel is not open, don't render anything
  if (!isPanelOpen) {
    return null;
  }
  
  // Handle close
  const handleClose = () => {
    if (onClose) {
      onClose();
    } else {
      closeHelpPanel();
    }
  };
  
  // Handle search input change
  const handleSearchInputChange = (e) => {
    setLocalSearchQuery(e.target.value);
  };
  
  // Handle search submit
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    searchHelp(localSearchQuery);
  };
  
  // Handle search result click
  const handleSearchResultClick = (helpId) => {
    setCurrentHelpId(helpId);
    setIsSearching(false);
  };
  
  // Handle search toggle
  const handleSearchToggle = () => {
    setIsSearching(!isSearching);
  };
  
  // Get panel classes
  const panelClasses = [
    'help-panel',
    `help-panel--${position}`,
    isSearching ? 'help-panel--searching' : '',
    className
  ].filter(Boolean).join(' ');
  
  // Render search results
  const renderSearchResults = () => {
    if (!searchResults || searchResults.length === 0) {
      return (
        <div className="help-panel__search-empty">
          {translate('help.noResults', 'No results found')}
        </div>
      );
    }
    
    return (
      <ul className="help-panel__search-results">
        {searchResults.map((result) => (
          <li
            key={result.helpId}
            className="help-panel__search-result"
            onClick={() => handleSearchResultClick(result.helpId)}
          >
            <div className="help-panel__search-result-title">
              {result.title}
            </div>
            <div className="help-panel__search-result-description">
              {result.description}
            </div>
          </li>
        ))}
      </ul>
    );
  };
  
  // Render table of contents
  const renderTableOfContents = () => {
    if (!showToc || !currentContent || !currentContent.content) {
      return null;
    }
    
    // Extract headings from content
    const headings = [];
    const content = currentContent.content;
    const lines = content.split('\n');
    
    lines.forEach((line) => {
      const match = line.match(/^(#{1,6})\s+(.+)$/);
      
      if (match) {
        const level = match[1].length;
        const text = match[2];
        
        headings.push({
          level,
          text,
          id: text.toLowerCase().replace(/[^\w]+/g, '-')
        });
      }
    });
    
    if (headings.length === 0) {
      return null;
    }
    
    return (
      <div className="help-panel__toc">
        <h3 className="help-panel__toc-title">
          {translate('help.tableOfContents', 'Table of Contents')}
        </h3>
        
        <ul className="help-panel__toc-list">
          {headings.map((heading, index) => (
            <li
              key={index}
              className={`help-panel__toc-item help-panel__toc-item--level-${heading.level}`}
            >
              <a
                href={`#${heading.id}`}
                className="help-panel__toc-link"
              >
                {heading.text}
              </a>
            </li>
          ))}
        </ul>
      </div>
    );
  };
  
  // Render related help
  const renderRelatedHelp = () => {
    if (!showRelated || !currentContent || !currentContent.related) {
      return null;
    }
    
    return (
      <div className="help-panel__related">
        <h3 className="help-panel__related-title">
          {translate('help.relatedTopics', 'Related Topics')}
        </h3>
        
        <ul className="help-panel__related-list">
          {currentContent.related.map((related) => (
            <li
              key={related.helpId}
              className="help-panel__related-item"
              onClick={() => setCurrentHelpId(related.helpId)}
            >
              {related.title}
            </li>
          ))}
        </ul>
      </div>
    );
  };
  
  return (
    <div
      className={panelClasses}
      ref={panelRef}
      style={style}
      role="dialog"
      aria-labelledby="help-panel-title"
      aria-modal="true"
    >
      {/* Header */}
      <div className="help-panel__header">
        {isSearching ? (
          <form
            className="help-panel__search-form"
            onSubmit={handleSearchSubmit}
          >
            <input
              type="text"
              className="help-panel__search-input"
              placeholder={translate('help.searchPlaceholder', 'Search help...')}
              value={localSearchQuery}
              onChange={handleSearchInputChange}
              ref={searchInputRef}
            />
            
            <button
              type="submit"
              className="help-panel__search-submit"
              aria-label={translate('help.search', 'Search')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </button>
            
            <button
              type="button"
              className="help-panel__search-cancel"
              onClick={handleSearchToggle}
              aria-label={translate('help.cancelSearch', 'Cancel Search')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </form>
        ) : (
          <>
            <h2
              id="help-panel-title"
              className="help-panel__title"
            >
              {currentContent ? currentContent.title : translate('help.helpCenter', 'Help Center')}
            </h2>
            
            <div className="help-panel__actions">
              {showSearch && (
                <button
                  className="help-panel__action help-panel__action--search"
                  onClick={handleSearchToggle}
                  aria-label={translate('help.search', 'Search')}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                </button>
              )}
              
              <button
                className="help-panel__action help-panel__action--close"
                onClick={handleClose}
                aria-label={translate('help.close', 'Close')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          </>
        )}
      </div>
      
      {/* Content */}
      <div className="help-panel__content">
        {isSearching ? (
          renderSearchResults()
        ) : currentContent ? (
          <>
            {renderTableOfContents()}
            
            <div className="help-panel__main">
              {currentContent.content ? (
                <ReactMarkdown className="help-panel__markdown">
                  {currentContent.content}
                </ReactMarkdown>
              ) : (
                <p className="help-panel__description">
                  {currentContent.description}
                </p>
              )}
            </div>
            
            {renderRelatedHelp()}
          </>
        ) : (
          <div className="help-panel__empty">
            <p>
              {translate('help.selectTopic', 'Select a topic from the list or search for help.')}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

HelpPanel.propTypes = {
  position: PropTypes.oneOf(['left', 'right']),
  showSearch: PropTypes.bool,
  showToc: PropTypes.bool,
  showRelated: PropTypes.bool,
  onClose: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object
};

export default HelpPanel;
