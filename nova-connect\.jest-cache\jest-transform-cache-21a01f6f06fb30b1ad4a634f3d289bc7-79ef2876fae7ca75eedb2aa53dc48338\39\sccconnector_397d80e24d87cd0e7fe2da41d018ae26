e1236cacaca266b7dee7c7cb2c118101
/**
 * Google Security Command Center (SCC) Connector
 * 
 * Connects to Google SCC API and normalizes security findings
 * for use with NovaConnect's remediation engine.
 */

const {
  SecurityCenterClient
} = require('@google-cloud/security-center');
const TransformationEngine = require('../../engines/transformation-engine');
class SCCConnector {
  constructor(options = {}) {
    this.options = {
      enableMetrics: true,
      maxConcurrentRequests: 10,
      pageSize: 1000,
      ...options
    };
    this.transformationEngine = new TransformationEngine({
      enableMetrics: this.options.enableMetrics,
      enableCaching: true
    });

    // Register SCC-specific transformers
    this.transformationEngine.registerTransformer('parseSCCResourceName', this._parseSCCResourceName);
    this.transformationEngine.registerTransformer('mapSCCSeverity', this._mapSCCSeverity);
    this.transformationEngine.registerTransformer('extractSCCCategory', this._extractSCCCategory);

    // Initialize metrics
    this.metrics = {
      findingsRetrieved: 0,
      findingsNormalized: 0,
      apiCalls: 0,
      totalApiLatency: 0,
      averageApiLatency: 0,
      totalNormalizationTime: 0,
      averageNormalizationTime: 0
    };
  }

  /**
   * Initialize the SCC client with credentials
   * @param {Object} credentials - GCP credentials
   */
  async initialize(credentials) {
    try {
      this.client = new SecurityCenterClient({
        credentials: credentials,
        projectId: credentials.project_id
      });
      return {
        success: true
      };
    } catch (error) {
      console.error('Error initializing SCC client:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get findings from Security Command Center
   * @param {Object} params - Query parameters
   * @returns {Object} - Findings and metadata
   */
  async getFindings(params = {}) {
    if (!this.client) {
      throw new Error('SCC client not initialized. Call initialize() first.');
    }
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    try {
      const {
        organizationId,
        projectId,
        folderId,
        filter = '',
        pageSize = this.options.pageSize,
        pageToken,
        orderBy
      } = params;
      let parent;
      if (organizationId) {
        parent = `organizations/${organizationId}`;
      } else if (projectId) {
        parent = `projects/${projectId}`;
      } else if (folderId) {
        parent = `folders/${folderId}`;
      } else {
        throw new Error('One of organizationId, projectId, or folderId must be provided');
      }

      // Build the request
      const request = {
        parent,
        filter,
        pageSize,
        pageToken,
        orderBy
      };

      // Make the API call
      const [findings, metadata] = await this.client.listFindings(request);

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const latency = endTime - startTime;
        this.metrics.apiCalls++;
        this.metrics.findingsRetrieved += findings.length;
        this.metrics.totalApiLatency += latency;
        this.metrics.averageApiLatency = this.metrics.totalApiLatency / this.metrics.apiCalls;
      }
      return {
        findings,
        nextPageToken: metadata.nextPageToken,
        totalSize: metadata.totalSize
      };
    } catch (error) {
      console.error('Error retrieving findings from SCC:', error);
      throw error;
    }
  }

  /**
   * Normalize SCC findings to NovaConnect format
   * @param {Array} findings - SCC findings
   * @returns {Array} - Normalized findings
   */
  normalizeFindings(findings) {
    const startTime = this.options.enableMetrics ? Date.now() : 0;
    try {
      // Define transformation rules for SCC findings
      const rules = [{
        source: 'name',
        target: 'id',
        transform: 'parseSCCResourceName'
      }, {
        source: 'category',
        target: 'category',
        transform: 'extractSCCCategory'
      }, {
        source: 'severity',
        target: 'severity',
        transform: 'mapSCCSeverity'
      }, {
        source: 'resourceName',
        target: 'resourceName'
      }, {
        source: 'state',
        target: 'state',
        transform: 'lowercase'
      }, {
        source: 'eventTime',
        target: 'eventTime'
      }, {
        source: 'createTime',
        target: 'createdAt',
        transform: 'isoToUnix'
      }, {
        source: 'sourceProperties.finding_type',
        target: 'type'
      }, {
        source: 'sourceProperties.finding_description',
        target: 'description'
      }, {
        source: 'sourceProperties.finding_id',
        target: 'externalId'
      }, {
        source: 'securityMarks.marks',
        target: 'tags'
      }];

      // Transform each finding
      const normalizedFindings = findings.map(finding => this.transformationEngine.transform(finding, rules));

      // Update metrics
      if (this.options.enableMetrics) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.metrics.findingsNormalized += findings.length;
        this.metrics.totalNormalizationTime += duration;
        this.metrics.averageNormalizationTime = this.metrics.totalNormalizationTime / this.metrics.findingsNormalized;
      }
      return normalizedFindings;
    } catch (error) {
      console.error('Error normalizing SCC findings:', error);
      throw error;
    }
  }

  /**
   * Get and normalize findings in a single call
   * @param {Object} params - Query parameters
   * @returns {Object} - Normalized findings and metadata
   */
  async getFindingsNormalized(params = {}) {
    const {
      findings,
      nextPageToken,
      totalSize
    } = await this.getFindings(params);
    const normalizedFindings = this.normalizeFindings(findings);
    return {
      findings: normalizedFindings,
      nextPageToken,
      totalSize
    };
  }

  /**
   * Get all findings with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All findings
   */
  async getAllFindings(params = {}) {
    let allFindings = [];
    let nextPageToken = null;
    do {
      const {
        findings,
        nextPageToken: token
      } = await this.getFindings({
        ...params,
        pageToken: nextPageToken
      });
      allFindings = allFindings.concat(findings);
      nextPageToken = token;
    } while (nextPageToken);
    return allFindings;
  }

  /**
   * Get all normalized findings with pagination handling
   * @param {Object} params - Query parameters
   * @returns {Array} - All normalized findings
   */
  async getAllFindingsNormalized(params = {}) {
    const findings = await this.getAllFindings(params);
    return this.normalizeFindings(findings);
  }

  /**
   * Get metrics for the connector
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      transformationMetrics: this.transformationEngine.getMetrics()
    };
  }

  /**
   * Parse SCC resource name to extract ID
   * @private
   */
  _parseSCCResourceName(name) {
    if (!name) return '';

    // Extract the finding ID from the name
    // Format: organizations/{organization_id}/sources/{source_id}/findings/{finding_id}
    const parts = name.split('/');
    return parts[parts.length - 1];
  }

  /**
   * Map SCC severity to normalized severity
   * @private
   */
  _mapSCCSeverity(severity) {
    const severityMap = {
      'CRITICAL': 'critical',
      'HIGH': 'high',
      'MEDIUM': 'medium',
      'LOW': 'low'
    };
    return severityMap[severity] || 'unknown';
  }

  /**
   * Extract category from SCC finding
   * @private
   */
  _extractSCCCategory(category) {
    if (!category) return 'unknown';

    // Convert to lowercase and remove spaces
    return category.toLowerCase().replace(/\s+/g, '_');
  }
}
module.exports = SCCConnector;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTZWN1cml0eUNlbnRlckNsaWVudCIsInJlcXVpcmUiLCJUcmFuc2Zvcm1hdGlvbkVuZ2luZSIsIlNDQ0Nvbm5lY3RvciIsImNvbnN0cnVjdG9yIiwib3B0aW9ucyIsImVuYWJsZU1ldHJpY3MiLCJtYXhDb25jdXJyZW50UmVxdWVzdHMiLCJwYWdlU2l6ZSIsInRyYW5zZm9ybWF0aW9uRW5naW5lIiwiZW5hYmxlQ2FjaGluZyIsInJlZ2lzdGVyVHJhbnNmb3JtZXIiLCJfcGFyc2VTQ0NSZXNvdXJjZU5hbWUiLCJfbWFwU0NDU2V2ZXJpdHkiLCJfZXh0cmFjdFNDQ0NhdGVnb3J5IiwibWV0cmljcyIsImZpbmRpbmdzUmV0cmlldmVkIiwiZmluZGluZ3NOb3JtYWxpemVkIiwiYXBpQ2FsbHMiLCJ0b3RhbEFwaUxhdGVuY3kiLCJhdmVyYWdlQXBpTGF0ZW5jeSIsInRvdGFsTm9ybWFsaXphdGlvblRpbWUiLCJhdmVyYWdlTm9ybWFsaXphdGlvblRpbWUiLCJpbml0aWFsaXplIiwiY3JlZGVudGlhbHMiLCJjbGllbnQiLCJwcm9qZWN0SWQiLCJwcm9qZWN0X2lkIiwic3VjY2VzcyIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiLCJnZXRGaW5kaW5ncyIsInBhcmFtcyIsIkVycm9yIiwic3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsIm9yZ2FuaXphdGlvbklkIiwiZm9sZGVySWQiLCJmaWx0ZXIiLCJwYWdlVG9rZW4iLCJvcmRlckJ5IiwicGFyZW50IiwicmVxdWVzdCIsImZpbmRpbmdzIiwibWV0YWRhdGEiLCJsaXN0RmluZGluZ3MiLCJlbmRUaW1lIiwibGF0ZW5jeSIsImxlbmd0aCIsIm5leHRQYWdlVG9rZW4iLCJ0b3RhbFNpemUiLCJub3JtYWxpemVGaW5kaW5ncyIsInJ1bGVzIiwic291cmNlIiwidGFyZ2V0IiwidHJhbnNmb3JtIiwibm9ybWFsaXplZEZpbmRpbmdzIiwibWFwIiwiZmluZGluZyIsImR1cmF0aW9uIiwiZ2V0RmluZGluZ3NOb3JtYWxpemVkIiwiZ2V0QWxsRmluZGluZ3MiLCJhbGxGaW5kaW5ncyIsInRva2VuIiwiY29uY2F0IiwiZ2V0QWxsRmluZGluZ3NOb3JtYWxpemVkIiwiZ2V0TWV0cmljcyIsInRyYW5zZm9ybWF0aW9uTWV0cmljcyIsIm5hbWUiLCJwYXJ0cyIsInNwbGl0Iiwic2V2ZXJpdHkiLCJzZXZlcml0eU1hcCIsImNhdGVnb3J5IiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbInNjYy1jb25uZWN0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBHb29nbGUgU2VjdXJpdHkgQ29tbWFuZCBDZW50ZXIgKFNDQykgQ29ubmVjdG9yXG4gKiBcbiAqIENvbm5lY3RzIHRvIEdvb2dsZSBTQ0MgQVBJIGFuZCBub3JtYWxpemVzIHNlY3VyaXR5IGZpbmRpbmdzXG4gKiBmb3IgdXNlIHdpdGggTm92YUNvbm5lY3QncyByZW1lZGlhdGlvbiBlbmdpbmUuXG4gKi9cblxuY29uc3QgeyBTZWN1cml0eUNlbnRlckNsaWVudCB9ID0gcmVxdWlyZSgnQGdvb2dsZS1jbG91ZC9zZWN1cml0eS1jZW50ZXInKTtcbmNvbnN0IFRyYW5zZm9ybWF0aW9uRW5naW5lID0gcmVxdWlyZSgnLi4vLi4vZW5naW5lcy90cmFuc2Zvcm1hdGlvbi1lbmdpbmUnKTtcblxuY2xhc3MgU0NDQ29ubmVjdG9yIHtcbiAgY29uc3RydWN0b3Iob3B0aW9ucyA9IHt9KSB7XG4gICAgdGhpcy5vcHRpb25zID0ge1xuICAgICAgZW5hYmxlTWV0cmljczogdHJ1ZSxcbiAgICAgIG1heENvbmN1cnJlbnRSZXF1ZXN0czogMTAsXG4gICAgICBwYWdlU2l6ZTogMTAwMCxcbiAgICAgIC4uLm9wdGlvbnNcbiAgICB9O1xuICAgIFxuICAgIHRoaXMudHJhbnNmb3JtYXRpb25FbmdpbmUgPSBuZXcgVHJhbnNmb3JtYXRpb25FbmdpbmUoe1xuICAgICAgZW5hYmxlTWV0cmljczogdGhpcy5vcHRpb25zLmVuYWJsZU1ldHJpY3MsXG4gICAgICBlbmFibGVDYWNoaW5nOiB0cnVlXG4gICAgfSk7XG4gICAgXG4gICAgLy8gUmVnaXN0ZXIgU0NDLXNwZWNpZmljIHRyYW5zZm9ybWVyc1xuICAgIHRoaXMudHJhbnNmb3JtYXRpb25FbmdpbmUucmVnaXN0ZXJUcmFuc2Zvcm1lcigncGFyc2VTQ0NSZXNvdXJjZU5hbWUnLCB0aGlzLl9wYXJzZVNDQ1Jlc291cmNlTmFtZSk7XG4gICAgdGhpcy50cmFuc2Zvcm1hdGlvbkVuZ2luZS5yZWdpc3RlclRyYW5zZm9ybWVyKCdtYXBTQ0NTZXZlcml0eScsIHRoaXMuX21hcFNDQ1NldmVyaXR5KTtcbiAgICB0aGlzLnRyYW5zZm9ybWF0aW9uRW5naW5lLnJlZ2lzdGVyVHJhbnNmb3JtZXIoJ2V4dHJhY3RTQ0NDYXRlZ29yeScsIHRoaXMuX2V4dHJhY3RTQ0NDYXRlZ29yeSk7XG4gICAgXG4gICAgLy8gSW5pdGlhbGl6ZSBtZXRyaWNzXG4gICAgdGhpcy5tZXRyaWNzID0ge1xuICAgICAgZmluZGluZ3NSZXRyaWV2ZWQ6IDAsXG4gICAgICBmaW5kaW5nc05vcm1hbGl6ZWQ6IDAsXG4gICAgICBhcGlDYWxsczogMCxcbiAgICAgIHRvdGFsQXBpTGF0ZW5jeTogMCxcbiAgICAgIGF2ZXJhZ2VBcGlMYXRlbmN5OiAwLFxuICAgICAgdG90YWxOb3JtYWxpemF0aW9uVGltZTogMCxcbiAgICAgIGF2ZXJhZ2VOb3JtYWxpemF0aW9uVGltZTogMFxuICAgIH07XG4gIH1cbiAgXG4gIC8qKlxuICAgKiBJbml0aWFsaXplIHRoZSBTQ0MgY2xpZW50IHdpdGggY3JlZGVudGlhbHNcbiAgICogQHBhcmFtIHtPYmplY3R9IGNyZWRlbnRpYWxzIC0gR0NQIGNyZWRlbnRpYWxzXG4gICAqL1xuICBhc3luYyBpbml0aWFsaXplKGNyZWRlbnRpYWxzKSB7XG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuY2xpZW50ID0gbmV3IFNlY3VyaXR5Q2VudGVyQ2xpZW50KHtcbiAgICAgICAgY3JlZGVudGlhbHM6IGNyZWRlbnRpYWxzLFxuICAgICAgICBwcm9qZWN0SWQ6IGNyZWRlbnRpYWxzLnByb2plY3RfaWRcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBTQ0MgY2xpZW50OicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7IFxuICAgICAgICBzdWNjZXNzOiBmYWxzZSwgXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlIFxuICAgICAgfTtcbiAgICB9XG4gIH1cbiAgXG4gIC8qKlxuICAgKiBHZXQgZmluZGluZ3MgZnJvbSBTZWN1cml0eSBDb21tYW5kIENlbnRlclxuICAgKiBAcGFyYW0ge09iamVjdH0gcGFyYW1zIC0gUXVlcnkgcGFyYW1ldGVyc1xuICAgKiBAcmV0dXJucyB7T2JqZWN0fSAtIEZpbmRpbmdzIGFuZCBtZXRhZGF0YVxuICAgKi9cbiAgYXN5bmMgZ2V0RmluZGluZ3MocGFyYW1zID0ge30pIHtcbiAgICBpZiAoIXRoaXMuY2xpZW50KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1NDQyBjbGllbnQgbm90IGluaXRpYWxpemVkLiBDYWxsIGluaXRpYWxpemUoKSBmaXJzdC4nKTtcbiAgICB9XG4gICAgXG4gICAgY29uc3Qgc3RhcnRUaW1lID0gdGhpcy5vcHRpb25zLmVuYWJsZU1ldHJpY3MgPyBEYXRlLm5vdygpIDogMDtcbiAgICBcbiAgICB0cnkge1xuICAgICAgY29uc3Qge1xuICAgICAgICBvcmdhbml6YXRpb25JZCxcbiAgICAgICAgcHJvamVjdElkLFxuICAgICAgICBmb2xkZXJJZCxcbiAgICAgICAgZmlsdGVyID0gJycsXG4gICAgICAgIHBhZ2VTaXplID0gdGhpcy5vcHRpb25zLnBhZ2VTaXplLFxuICAgICAgICBwYWdlVG9rZW4sXG4gICAgICAgIG9yZGVyQnlcbiAgICAgIH0gPSBwYXJhbXM7XG4gICAgICBcbiAgICAgIGxldCBwYXJlbnQ7XG4gICAgICBpZiAob3JnYW5pemF0aW9uSWQpIHtcbiAgICAgICAgcGFyZW50ID0gYG9yZ2FuaXphdGlvbnMvJHtvcmdhbml6YXRpb25JZH1gO1xuICAgICAgfSBlbHNlIGlmIChwcm9qZWN0SWQpIHtcbiAgICAgICAgcGFyZW50ID0gYHByb2plY3RzLyR7cHJvamVjdElkfWA7XG4gICAgICB9IGVsc2UgaWYgKGZvbGRlcklkKSB7XG4gICAgICAgIHBhcmVudCA9IGBmb2xkZXJzLyR7Zm9sZGVySWR9YDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignT25lIG9mIG9yZ2FuaXphdGlvbklkLCBwcm9qZWN0SWQsIG9yIGZvbGRlcklkIG11c3QgYmUgcHJvdmlkZWQnKTtcbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gQnVpbGQgdGhlIHJlcXVlc3RcbiAgICAgIGNvbnN0IHJlcXVlc3QgPSB7XG4gICAgICAgIHBhcmVudCxcbiAgICAgICAgZmlsdGVyLFxuICAgICAgICBwYWdlU2l6ZSxcbiAgICAgICAgcGFnZVRva2VuLFxuICAgICAgICBvcmRlckJ5XG4gICAgICB9O1xuICAgICAgXG4gICAgICAvLyBNYWtlIHRoZSBBUEkgY2FsbFxuICAgICAgY29uc3QgW2ZpbmRpbmdzLCBtZXRhZGF0YV0gPSBhd2FpdCB0aGlzLmNsaWVudC5saXN0RmluZGluZ3MocmVxdWVzdCk7XG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSBtZXRyaWNzXG4gICAgICBpZiAodGhpcy5vcHRpb25zLmVuYWJsZU1ldHJpY3MpIHtcbiAgICAgICAgY29uc3QgZW5kVGltZSA9IERhdGUubm93KCk7XG4gICAgICAgIGNvbnN0IGxhdGVuY3kgPSBlbmRUaW1lIC0gc3RhcnRUaW1lO1xuICAgICAgICBcbiAgICAgICAgdGhpcy5tZXRyaWNzLmFwaUNhbGxzKys7XG4gICAgICAgIHRoaXMubWV0cmljcy5maW5kaW5nc1JldHJpZXZlZCArPSBmaW5kaW5ncy5sZW5ndGg7XG4gICAgICAgIHRoaXMubWV0cmljcy50b3RhbEFwaUxhdGVuY3kgKz0gbGF0ZW5jeTtcbiAgICAgICAgdGhpcy5tZXRyaWNzLmF2ZXJhZ2VBcGlMYXRlbmN5ID0gdGhpcy5tZXRyaWNzLnRvdGFsQXBpTGF0ZW5jeSAvIHRoaXMubWV0cmljcy5hcGlDYWxscztcbiAgICAgIH1cbiAgICAgIFxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgZmluZGluZ3MsXG4gICAgICAgIG5leHRQYWdlVG9rZW46IG1ldGFkYXRhLm5leHRQYWdlVG9rZW4sXG4gICAgICAgIHRvdGFsU2l6ZTogbWV0YWRhdGEudG90YWxTaXplXG4gICAgICB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciByZXRyaWV2aW5nIGZpbmRpbmdzIGZyb20gU0NDOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuICBcbiAgLyoqXG4gICAqIE5vcm1hbGl6ZSBTQ0MgZmluZGluZ3MgdG8gTm92YUNvbm5lY3QgZm9ybWF0XG4gICAqIEBwYXJhbSB7QXJyYXl9IGZpbmRpbmdzIC0gU0NDIGZpbmRpbmdzXG4gICAqIEByZXR1cm5zIHtBcnJheX0gLSBOb3JtYWxpemVkIGZpbmRpbmdzXG4gICAqL1xuICBub3JtYWxpemVGaW5kaW5ncyhmaW5kaW5ncykge1xuICAgIGNvbnN0IHN0YXJ0VGltZSA9IHRoaXMub3B0aW9ucy5lbmFibGVNZXRyaWNzID8gRGF0ZS5ub3coKSA6IDA7XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIC8vIERlZmluZSB0cmFuc2Zvcm1hdGlvbiBydWxlcyBmb3IgU0NDIGZpbmRpbmdzXG4gICAgICBjb25zdCBydWxlcyA9IFtcbiAgICAgICAgeyBzb3VyY2U6ICduYW1lJywgdGFyZ2V0OiAnaWQnLCB0cmFuc2Zvcm06ICdwYXJzZVNDQ1Jlc291cmNlTmFtZScgfSxcbiAgICAgICAgeyBzb3VyY2U6ICdjYXRlZ29yeScsIHRhcmdldDogJ2NhdGVnb3J5JywgdHJhbnNmb3JtOiAnZXh0cmFjdFNDQ0NhdGVnb3J5JyB9LFxuICAgICAgICB7IHNvdXJjZTogJ3NldmVyaXR5JywgdGFyZ2V0OiAnc2V2ZXJpdHknLCB0cmFuc2Zvcm06ICdtYXBTQ0NTZXZlcml0eScgfSxcbiAgICAgICAgeyBzb3VyY2U6ICdyZXNvdXJjZU5hbWUnLCB0YXJnZXQ6ICdyZXNvdXJjZU5hbWUnIH0sXG4gICAgICAgIHsgc291cmNlOiAnc3RhdGUnLCB0YXJnZXQ6ICdzdGF0ZScsIHRyYW5zZm9ybTogJ2xvd2VyY2FzZScgfSxcbiAgICAgICAgeyBzb3VyY2U6ICdldmVudFRpbWUnLCB0YXJnZXQ6ICdldmVudFRpbWUnIH0sXG4gICAgICAgIHsgc291cmNlOiAnY3JlYXRlVGltZScsIHRhcmdldDogJ2NyZWF0ZWRBdCcsIHRyYW5zZm9ybTogJ2lzb1RvVW5peCcgfSxcbiAgICAgICAgeyBzb3VyY2U6ICdzb3VyY2VQcm9wZXJ0aWVzLmZpbmRpbmdfdHlwZScsIHRhcmdldDogJ3R5cGUnIH0sXG4gICAgICAgIHsgc291cmNlOiAnc291cmNlUHJvcGVydGllcy5maW5kaW5nX2Rlc2NyaXB0aW9uJywgdGFyZ2V0OiAnZGVzY3JpcHRpb24nIH0sXG4gICAgICAgIHsgc291cmNlOiAnc291cmNlUHJvcGVydGllcy5maW5kaW5nX2lkJywgdGFyZ2V0OiAnZXh0ZXJuYWxJZCcgfSxcbiAgICAgICAgeyBzb3VyY2U6ICdzZWN1cml0eU1hcmtzLm1hcmtzJywgdGFyZ2V0OiAndGFncycgfVxuICAgICAgXTtcbiAgICAgIFxuICAgICAgLy8gVHJhbnNmb3JtIGVhY2ggZmluZGluZ1xuICAgICAgY29uc3Qgbm9ybWFsaXplZEZpbmRpbmdzID0gZmluZGluZ3MubWFwKGZpbmRpbmcgPT4gXG4gICAgICAgIHRoaXMudHJhbnNmb3JtYXRpb25FbmdpbmUudHJhbnNmb3JtKGZpbmRpbmcsIHJ1bGVzKVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgLy8gVXBkYXRlIG1ldHJpY3NcbiAgICAgIGlmICh0aGlzLm9wdGlvbnMuZW5hYmxlTWV0cmljcykge1xuICAgICAgICBjb25zdCBlbmRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgICAgY29uc3QgZHVyYXRpb24gPSBlbmRUaW1lIC0gc3RhcnRUaW1lO1xuICAgICAgICBcbiAgICAgICAgdGhpcy5tZXRyaWNzLmZpbmRpbmdzTm9ybWFsaXplZCArPSBmaW5kaW5ncy5sZW5ndGg7XG4gICAgICAgIHRoaXMubWV0cmljcy50b3RhbE5vcm1hbGl6YXRpb25UaW1lICs9IGR1cmF0aW9uO1xuICAgICAgICB0aGlzLm1ldHJpY3MuYXZlcmFnZU5vcm1hbGl6YXRpb25UaW1lID0gXG4gICAgICAgICAgdGhpcy5tZXRyaWNzLnRvdGFsTm9ybWFsaXphdGlvblRpbWUgLyB0aGlzLm1ldHJpY3MuZmluZGluZ3NOb3JtYWxpemVkO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gbm9ybWFsaXplZEZpbmRpbmdzO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBub3JtYWxpemluZyBTQ0MgZmluZGluZ3M6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG4gIFxuICAvKipcbiAgICogR2V0IGFuZCBub3JtYWxpemUgZmluZGluZ3MgaW4gYSBzaW5nbGUgY2FsbFxuICAgKiBAcGFyYW0ge09iamVjdH0gcGFyYW1zIC0gUXVlcnkgcGFyYW1ldGVyc1xuICAgKiBAcmV0dXJucyB7T2JqZWN0fSAtIE5vcm1hbGl6ZWQgZmluZGluZ3MgYW5kIG1ldGFkYXRhXG4gICAqL1xuICBhc3luYyBnZXRGaW5kaW5nc05vcm1hbGl6ZWQocGFyYW1zID0ge30pIHtcbiAgICBjb25zdCB7IGZpbmRpbmdzLCBuZXh0UGFnZVRva2VuLCB0b3RhbFNpemUgfSA9IGF3YWl0IHRoaXMuZ2V0RmluZGluZ3MocGFyYW1zKTtcbiAgICBjb25zdCBub3JtYWxpemVkRmluZGluZ3MgPSB0aGlzLm5vcm1hbGl6ZUZpbmRpbmdzKGZpbmRpbmdzKTtcbiAgICBcbiAgICByZXR1cm4ge1xuICAgICAgZmluZGluZ3M6IG5vcm1hbGl6ZWRGaW5kaW5ncyxcbiAgICAgIG5leHRQYWdlVG9rZW4sXG4gICAgICB0b3RhbFNpemVcbiAgICB9O1xuICB9XG4gIFxuICAvKipcbiAgICogR2V0IGFsbCBmaW5kaW5ncyB3aXRoIHBhZ2luYXRpb24gaGFuZGxpbmdcbiAgICogQHBhcmFtIHtPYmplY3R9IHBhcmFtcyAtIFF1ZXJ5IHBhcmFtZXRlcnNcbiAgICogQHJldHVybnMge0FycmF5fSAtIEFsbCBmaW5kaW5nc1xuICAgKi9cbiAgYXN5bmMgZ2V0QWxsRmluZGluZ3MocGFyYW1zID0ge30pIHtcbiAgICBsZXQgYWxsRmluZGluZ3MgPSBbXTtcbiAgICBsZXQgbmV4dFBhZ2VUb2tlbiA9IG51bGw7XG4gICAgXG4gICAgZG8ge1xuICAgICAgY29uc3QgeyBmaW5kaW5ncywgbmV4dFBhZ2VUb2tlbjogdG9rZW4gfSA9IGF3YWl0IHRoaXMuZ2V0RmluZGluZ3Moe1xuICAgICAgICAuLi5wYXJhbXMsXG4gICAgICAgIHBhZ2VUb2tlbjogbmV4dFBhZ2VUb2tlblxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIGFsbEZpbmRpbmdzID0gYWxsRmluZGluZ3MuY29uY2F0KGZpbmRpbmdzKTtcbiAgICAgIG5leHRQYWdlVG9rZW4gPSB0b2tlbjtcbiAgICB9IHdoaWxlIChuZXh0UGFnZVRva2VuKTtcbiAgICBcbiAgICByZXR1cm4gYWxsRmluZGluZ3M7XG4gIH1cbiAgXG4gIC8qKlxuICAgKiBHZXQgYWxsIG5vcm1hbGl6ZWQgZmluZGluZ3Mgd2l0aCBwYWdpbmF0aW9uIGhhbmRsaW5nXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBwYXJhbXMgLSBRdWVyeSBwYXJhbWV0ZXJzXG4gICAqIEByZXR1cm5zIHtBcnJheX0gLSBBbGwgbm9ybWFsaXplZCBmaW5kaW5nc1xuICAgKi9cbiAgYXN5bmMgZ2V0QWxsRmluZGluZ3NOb3JtYWxpemVkKHBhcmFtcyA9IHt9KSB7XG4gICAgY29uc3QgZmluZGluZ3MgPSBhd2FpdCB0aGlzLmdldEFsbEZpbmRpbmdzKHBhcmFtcyk7XG4gICAgcmV0dXJuIHRoaXMubm9ybWFsaXplRmluZGluZ3MoZmluZGluZ3MpO1xuICB9XG4gIFxuICAvKipcbiAgICogR2V0IG1ldHJpY3MgZm9yIHRoZSBjb25uZWN0b3JcbiAgICogQHJldHVybnMge09iamVjdH0gLSBNZXRyaWNzXG4gICAqL1xuICBnZXRNZXRyaWNzKCkge1xuICAgIHJldHVybiB7XG4gICAgICAuLi50aGlzLm1ldHJpY3MsXG4gICAgICB0cmFuc2Zvcm1hdGlvbk1ldHJpY3M6IHRoaXMudHJhbnNmb3JtYXRpb25FbmdpbmUuZ2V0TWV0cmljcygpXG4gICAgfTtcbiAgfVxuICBcbiAgLyoqXG4gICAqIFBhcnNlIFNDQyByZXNvdXJjZSBuYW1lIHRvIGV4dHJhY3QgSURcbiAgICogQHByaXZhdGVcbiAgICovXG4gIF9wYXJzZVNDQ1Jlc291cmNlTmFtZShuYW1lKSB7XG4gICAgaWYgKCFuYW1lKSByZXR1cm4gJyc7XG4gICAgXG4gICAgLy8gRXh0cmFjdCB0aGUgZmluZGluZyBJRCBmcm9tIHRoZSBuYW1lXG4gICAgLy8gRm9ybWF0OiBvcmdhbml6YXRpb25zL3tvcmdhbml6YXRpb25faWR9L3NvdXJjZXMve3NvdXJjZV9pZH0vZmluZGluZ3Mve2ZpbmRpbmdfaWR9XG4gICAgY29uc3QgcGFydHMgPSBuYW1lLnNwbGl0KCcvJyk7XG4gICAgcmV0dXJuIHBhcnRzW3BhcnRzLmxlbmd0aCAtIDFdO1xuICB9XG4gIFxuICAvKipcbiAgICogTWFwIFNDQyBzZXZlcml0eSB0byBub3JtYWxpemVkIHNldmVyaXR5XG4gICAqIEBwcml2YXRlXG4gICAqL1xuICBfbWFwU0NDU2V2ZXJpdHkoc2V2ZXJpdHkpIHtcbiAgICBjb25zdCBzZXZlcml0eU1hcCA9IHtcbiAgICAgICdDUklUSUNBTCc6ICdjcml0aWNhbCcsXG4gICAgICAnSElHSCc6ICdoaWdoJyxcbiAgICAgICdNRURJVU0nOiAnbWVkaXVtJyxcbiAgICAgICdMT1cnOiAnbG93J1xuICAgIH07XG4gICAgXG4gICAgcmV0dXJuIHNldmVyaXR5TWFwW3NldmVyaXR5XSB8fCAndW5rbm93bic7XG4gIH1cbiAgXG4gIC8qKlxuICAgKiBFeHRyYWN0IGNhdGVnb3J5IGZyb20gU0NDIGZpbmRpbmdcbiAgICogQHByaXZhdGVcbiAgICovXG4gIF9leHRyYWN0U0NDQ2F0ZWdvcnkoY2F0ZWdvcnkpIHtcbiAgICBpZiAoIWNhdGVnb3J5KSByZXR1cm4gJ3Vua25vd24nO1xuICAgIFxuICAgIC8vIENvbnZlcnQgdG8gbG93ZXJjYXNlIGFuZCByZW1vdmUgc3BhY2VzXG4gICAgcmV0dXJuIGNhdGVnb3J5LnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvXFxzKy9nLCAnXycpO1xuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gU0NDQ29ubmVjdG9yO1xuIl0sIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTTtFQUFFQTtBQUFxQixDQUFDLEdBQUdDLE9BQU8sQ0FBQywrQkFBK0IsQ0FBQztBQUN6RSxNQUFNQyxvQkFBb0IsR0FBR0QsT0FBTyxDQUFDLHFDQUFxQyxDQUFDO0FBRTNFLE1BQU1FLFlBQVksQ0FBQztFQUNqQkMsV0FBV0EsQ0FBQ0MsT0FBTyxHQUFHLENBQUMsQ0FBQyxFQUFFO0lBQ3hCLElBQUksQ0FBQ0EsT0FBTyxHQUFHO01BQ2JDLGFBQWEsRUFBRSxJQUFJO01BQ25CQyxxQkFBcUIsRUFBRSxFQUFFO01BQ3pCQyxRQUFRLEVBQUUsSUFBSTtNQUNkLEdBQUdIO0lBQ0wsQ0FBQztJQUVELElBQUksQ0FBQ0ksb0JBQW9CLEdBQUcsSUFBSVAsb0JBQW9CLENBQUM7TUFDbkRJLGFBQWEsRUFBRSxJQUFJLENBQUNELE9BQU8sQ0FBQ0MsYUFBYTtNQUN6Q0ksYUFBYSxFQUFFO0lBQ2pCLENBQUMsQ0FBQzs7SUFFRjtJQUNBLElBQUksQ0FBQ0Qsb0JBQW9CLENBQUNFLG1CQUFtQixDQUFDLHNCQUFzQixFQUFFLElBQUksQ0FBQ0MscUJBQXFCLENBQUM7SUFDakcsSUFBSSxDQUFDSCxvQkFBb0IsQ0FBQ0UsbUJBQW1CLENBQUMsZ0JBQWdCLEVBQUUsSUFBSSxDQUFDRSxlQUFlLENBQUM7SUFDckYsSUFBSSxDQUFDSixvQkFBb0IsQ0FBQ0UsbUJBQW1CLENBQUMsb0JBQW9CLEVBQUUsSUFBSSxDQUFDRyxtQkFBbUIsQ0FBQzs7SUFFN0Y7SUFDQSxJQUFJLENBQUNDLE9BQU8sR0FBRztNQUNiQyxpQkFBaUIsRUFBRSxDQUFDO01BQ3BCQyxrQkFBa0IsRUFBRSxDQUFDO01BQ3JCQyxRQUFRLEVBQUUsQ0FBQztNQUNYQyxlQUFlLEVBQUUsQ0FBQztNQUNsQkMsaUJBQWlCLEVBQUUsQ0FBQztNQUNwQkMsc0JBQXNCLEVBQUUsQ0FBQztNQUN6QkMsd0JBQXdCLEVBQUU7SUFDNUIsQ0FBQztFQUNIOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0VBQ0UsTUFBTUMsVUFBVUEsQ0FBQ0MsV0FBVyxFQUFFO0lBQzVCLElBQUk7TUFDRixJQUFJLENBQUNDLE1BQU0sR0FBRyxJQUFJekIsb0JBQW9CLENBQUM7UUFDckN3QixXQUFXLEVBQUVBLFdBQVc7UUFDeEJFLFNBQVMsRUFBRUYsV0FBVyxDQUFDRztNQUN6QixDQUFDLENBQUM7TUFFRixPQUFPO1FBQUVDLE9BQU8sRUFBRTtNQUFLLENBQUM7SUFDMUIsQ0FBQyxDQUFDLE9BQU9DLEtBQUssRUFBRTtNQUNkQyxPQUFPLENBQUNELEtBQUssQ0FBQyxnQ0FBZ0MsRUFBRUEsS0FBSyxDQUFDO01BQ3RELE9BQU87UUFDTEQsT0FBTyxFQUFFLEtBQUs7UUFDZEMsS0FBSyxFQUFFQSxLQUFLLENBQUNFO01BQ2YsQ0FBQztJQUNIO0VBQ0Y7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtFQUNFLE1BQU1DLFdBQVdBLENBQUNDLE1BQU0sR0FBRyxDQUFDLENBQUMsRUFBRTtJQUM3QixJQUFJLENBQUMsSUFBSSxDQUFDUixNQUFNLEVBQUU7TUFDaEIsTUFBTSxJQUFJUyxLQUFLLENBQUMsc0RBQXNELENBQUM7SUFDekU7SUFFQSxNQUFNQyxTQUFTLEdBQUcsSUFBSSxDQUFDOUIsT0FBTyxDQUFDQyxhQUFhLEdBQUc4QixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsQ0FBQztJQUU3RCxJQUFJO01BQ0YsTUFBTTtRQUNKQyxjQUFjO1FBQ2RaLFNBQVM7UUFDVGEsUUFBUTtRQUNSQyxNQUFNLEdBQUcsRUFBRTtRQUNYaEMsUUFBUSxHQUFHLElBQUksQ0FBQ0gsT0FBTyxDQUFDRyxRQUFRO1FBQ2hDaUMsU0FBUztRQUNUQztNQUNGLENBQUMsR0FBR1QsTUFBTTtNQUVWLElBQUlVLE1BQU07TUFDVixJQUFJTCxjQUFjLEVBQUU7UUFDbEJLLE1BQU0sR0FBRyxpQkFBaUJMLGNBQWMsRUFBRTtNQUM1QyxDQUFDLE1BQU0sSUFBSVosU0FBUyxFQUFFO1FBQ3BCaUIsTUFBTSxHQUFHLFlBQVlqQixTQUFTLEVBQUU7TUFDbEMsQ0FBQyxNQUFNLElBQUlhLFFBQVEsRUFBRTtRQUNuQkksTUFBTSxHQUFHLFdBQVdKLFFBQVEsRUFBRTtNQUNoQyxDQUFDLE1BQU07UUFDTCxNQUFNLElBQUlMLEtBQUssQ0FBQyxnRUFBZ0UsQ0FBQztNQUNuRjs7TUFFQTtNQUNBLE1BQU1VLE9BQU8sR0FBRztRQUNkRCxNQUFNO1FBQ05ILE1BQU07UUFDTmhDLFFBQVE7UUFDUmlDLFNBQVM7UUFDVEM7TUFDRixDQUFDOztNQUVEO01BQ0EsTUFBTSxDQUFDRyxRQUFRLEVBQUVDLFFBQVEsQ0FBQyxHQUFHLE1BQU0sSUFBSSxDQUFDckIsTUFBTSxDQUFDc0IsWUFBWSxDQUFDSCxPQUFPLENBQUM7O01BRXBFO01BQ0EsSUFBSSxJQUFJLENBQUN2QyxPQUFPLENBQUNDLGFBQWEsRUFBRTtRQUM5QixNQUFNMEMsT0FBTyxHQUFHWixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO1FBQzFCLE1BQU1ZLE9BQU8sR0FBR0QsT0FBTyxHQUFHYixTQUFTO1FBRW5DLElBQUksQ0FBQ3BCLE9BQU8sQ0FBQ0csUUFBUSxFQUFFO1FBQ3ZCLElBQUksQ0FBQ0gsT0FBTyxDQUFDQyxpQkFBaUIsSUFBSTZCLFFBQVEsQ0FBQ0ssTUFBTTtRQUNqRCxJQUFJLENBQUNuQyxPQUFPLENBQUNJLGVBQWUsSUFBSThCLE9BQU87UUFDdkMsSUFBSSxDQUFDbEMsT0FBTyxDQUFDSyxpQkFBaUIsR0FBRyxJQUFJLENBQUNMLE9BQU8sQ0FBQ0ksZUFBZSxHQUFHLElBQUksQ0FBQ0osT0FBTyxDQUFDRyxRQUFRO01BQ3ZGO01BRUEsT0FBTztRQUNMMkIsUUFBUTtRQUNSTSxhQUFhLEVBQUVMLFFBQVEsQ0FBQ0ssYUFBYTtRQUNyQ0MsU0FBUyxFQUFFTixRQUFRLENBQUNNO01BQ3RCLENBQUM7SUFDSCxDQUFDLENBQUMsT0FBT3ZCLEtBQUssRUFBRTtNQUNkQyxPQUFPLENBQUNELEtBQUssQ0FBQyxxQ0FBcUMsRUFBRUEsS0FBSyxDQUFDO01BQzNELE1BQU1BLEtBQUs7SUFDYjtFQUNGOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7RUFDRXdCLGlCQUFpQkEsQ0FBQ1IsUUFBUSxFQUFFO0lBQzFCLE1BQU1WLFNBQVMsR0FBRyxJQUFJLENBQUM5QixPQUFPLENBQUNDLGFBQWEsR0FBRzhCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsR0FBRyxDQUFDO0lBRTdELElBQUk7TUFDRjtNQUNBLE1BQU1pQixLQUFLLEdBQUcsQ0FDWjtRQUFFQyxNQUFNLEVBQUUsTUFBTTtRQUFFQyxNQUFNLEVBQUUsSUFBSTtRQUFFQyxTQUFTLEVBQUU7TUFBdUIsQ0FBQyxFQUNuRTtRQUFFRixNQUFNLEVBQUUsVUFBVTtRQUFFQyxNQUFNLEVBQUUsVUFBVTtRQUFFQyxTQUFTLEVBQUU7TUFBcUIsQ0FBQyxFQUMzRTtRQUFFRixNQUFNLEVBQUUsVUFBVTtRQUFFQyxNQUFNLEVBQUUsVUFBVTtRQUFFQyxTQUFTLEVBQUU7TUFBaUIsQ0FBQyxFQUN2RTtRQUFFRixNQUFNLEVBQUUsY0FBYztRQUFFQyxNQUFNLEVBQUU7TUFBZSxDQUFDLEVBQ2xEO1FBQUVELE1BQU0sRUFBRSxPQUFPO1FBQUVDLE1BQU0sRUFBRSxPQUFPO1FBQUVDLFNBQVMsRUFBRTtNQUFZLENBQUMsRUFDNUQ7UUFBRUYsTUFBTSxFQUFFLFdBQVc7UUFBRUMsTUFBTSxFQUFFO01BQVksQ0FBQyxFQUM1QztRQUFFRCxNQUFNLEVBQUUsWUFBWTtRQUFFQyxNQUFNLEVBQUUsV0FBVztRQUFFQyxTQUFTLEVBQUU7TUFBWSxDQUFDLEVBQ3JFO1FBQUVGLE1BQU0sRUFBRSwrQkFBK0I7UUFBRUMsTUFBTSxFQUFFO01BQU8sQ0FBQyxFQUMzRDtRQUFFRCxNQUFNLEVBQUUsc0NBQXNDO1FBQUVDLE1BQU0sRUFBRTtNQUFjLENBQUMsRUFDekU7UUFBRUQsTUFBTSxFQUFFLDZCQUE2QjtRQUFFQyxNQUFNLEVBQUU7TUFBYSxDQUFDLEVBQy9EO1FBQUVELE1BQU0sRUFBRSxxQkFBcUI7UUFBRUMsTUFBTSxFQUFFO01BQU8sQ0FBQyxDQUNsRDs7TUFFRDtNQUNBLE1BQU1FLGtCQUFrQixHQUFHYixRQUFRLENBQUNjLEdBQUcsQ0FBQ0MsT0FBTyxJQUM3QyxJQUFJLENBQUNuRCxvQkFBb0IsQ0FBQ2dELFNBQVMsQ0FBQ0csT0FBTyxFQUFFTixLQUFLLENBQ3BELENBQUM7O01BRUQ7TUFDQSxJQUFJLElBQUksQ0FBQ2pELE9BQU8sQ0FBQ0MsYUFBYSxFQUFFO1FBQzlCLE1BQU0wQyxPQUFPLEdBQUdaLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFDMUIsTUFBTXdCLFFBQVEsR0FBR2IsT0FBTyxHQUFHYixTQUFTO1FBRXBDLElBQUksQ0FBQ3BCLE9BQU8sQ0FBQ0Usa0JBQWtCLElBQUk0QixRQUFRLENBQUNLLE1BQU07UUFDbEQsSUFBSSxDQUFDbkMsT0FBTyxDQUFDTSxzQkFBc0IsSUFBSXdDLFFBQVE7UUFDL0MsSUFBSSxDQUFDOUMsT0FBTyxDQUFDTyx3QkFBd0IsR0FDbkMsSUFBSSxDQUFDUCxPQUFPLENBQUNNLHNCQUFzQixHQUFHLElBQUksQ0FBQ04sT0FBTyxDQUFDRSxrQkFBa0I7TUFDekU7TUFFQSxPQUFPeUMsa0JBQWtCO0lBQzNCLENBQUMsQ0FBQyxPQUFPN0IsS0FBSyxFQUFFO01BQ2RDLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLGlDQUFpQyxFQUFFQSxLQUFLLENBQUM7TUFDdkQsTUFBTUEsS0FBSztJQUNiO0VBQ0Y7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtFQUNFLE1BQU1pQyxxQkFBcUJBLENBQUM3QixNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUU7SUFDdkMsTUFBTTtNQUFFWSxRQUFRO01BQUVNLGFBQWE7TUFBRUM7SUFBVSxDQUFDLEdBQUcsTUFBTSxJQUFJLENBQUNwQixXQUFXLENBQUNDLE1BQU0sQ0FBQztJQUM3RSxNQUFNeUIsa0JBQWtCLEdBQUcsSUFBSSxDQUFDTCxpQkFBaUIsQ0FBQ1IsUUFBUSxDQUFDO0lBRTNELE9BQU87TUFDTEEsUUFBUSxFQUFFYSxrQkFBa0I7TUFDNUJQLGFBQWE7TUFDYkM7SUFDRixDQUFDO0VBQ0g7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7QUFDQTtFQUNFLE1BQU1XLGNBQWNBLENBQUM5QixNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUU7SUFDaEMsSUFBSStCLFdBQVcsR0FBRyxFQUFFO0lBQ3BCLElBQUliLGFBQWEsR0FBRyxJQUFJO0lBRXhCLEdBQUc7TUFDRCxNQUFNO1FBQUVOLFFBQVE7UUFBRU0sYUFBYSxFQUFFYztNQUFNLENBQUMsR0FBRyxNQUFNLElBQUksQ0FBQ2pDLFdBQVcsQ0FBQztRQUNoRSxHQUFHQyxNQUFNO1FBQ1RRLFNBQVMsRUFBRVU7TUFDYixDQUFDLENBQUM7TUFFRmEsV0FBVyxHQUFHQSxXQUFXLENBQUNFLE1BQU0sQ0FBQ3JCLFFBQVEsQ0FBQztNQUMxQ00sYUFBYSxHQUFHYyxLQUFLO0lBQ3ZCLENBQUMsUUFBUWQsYUFBYTtJQUV0QixPQUFPYSxXQUFXO0VBQ3BCOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7RUFDRSxNQUFNRyx3QkFBd0JBLENBQUNsQyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUU7SUFDMUMsTUFBTVksUUFBUSxHQUFHLE1BQU0sSUFBSSxDQUFDa0IsY0FBYyxDQUFDOUIsTUFBTSxDQUFDO0lBQ2xELE9BQU8sSUFBSSxDQUFDb0IsaUJBQWlCLENBQUNSLFFBQVEsQ0FBQztFQUN6Qzs7RUFFQTtBQUNGO0FBQ0E7QUFDQTtFQUNFdUIsVUFBVUEsQ0FBQSxFQUFHO0lBQ1gsT0FBTztNQUNMLEdBQUcsSUFBSSxDQUFDckQsT0FBTztNQUNmc0QscUJBQXFCLEVBQUUsSUFBSSxDQUFDNUQsb0JBQW9CLENBQUMyRCxVQUFVLENBQUM7SUFDOUQsQ0FBQztFQUNIOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0VBQ0V4RCxxQkFBcUJBLENBQUMwRCxJQUFJLEVBQUU7SUFDMUIsSUFBSSxDQUFDQSxJQUFJLEVBQUUsT0FBTyxFQUFFOztJQUVwQjtJQUNBO0lBQ0EsTUFBTUMsS0FBSyxHQUFHRCxJQUFJLENBQUNFLEtBQUssQ0FBQyxHQUFHLENBQUM7SUFDN0IsT0FBT0QsS0FBSyxDQUFDQSxLQUFLLENBQUNyQixNQUFNLEdBQUcsQ0FBQyxDQUFDO0VBQ2hDOztFQUVBO0FBQ0Y7QUFDQTtBQUNBO0VBQ0VyQyxlQUFlQSxDQUFDNEQsUUFBUSxFQUFFO0lBQ3hCLE1BQU1DLFdBQVcsR0FBRztNQUNsQixVQUFVLEVBQUUsVUFBVTtNQUN0QixNQUFNLEVBQUUsTUFBTTtNQUNkLFFBQVEsRUFBRSxRQUFRO01BQ2xCLEtBQUssRUFBRTtJQUNULENBQUM7SUFFRCxPQUFPQSxXQUFXLENBQUNELFFBQVEsQ0FBQyxJQUFJLFNBQVM7RUFDM0M7O0VBRUE7QUFDRjtBQUNBO0FBQ0E7RUFDRTNELG1CQUFtQkEsQ0FBQzZELFFBQVEsRUFBRTtJQUM1QixJQUFJLENBQUNBLFFBQVEsRUFBRSxPQUFPLFNBQVM7O0lBRS9CO0lBQ0EsT0FBT0EsUUFBUSxDQUFDQyxXQUFXLENBQUMsQ0FBQyxDQUFDQyxPQUFPLENBQUMsTUFBTSxFQUFFLEdBQUcsQ0FBQztFQUNwRDtBQUNGO0FBRUFDLE1BQU0sQ0FBQ0MsT0FBTyxHQUFHNUUsWUFBWSIsImlnbm9yZUxpc3QiOltdfQ==