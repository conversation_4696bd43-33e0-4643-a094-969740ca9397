/**
 * Unified Integration Module
 * 
 * This module provides a central integration point for connecting the Self-Healing Tensor,
 * 3D Tensor Visualization, Analytics Dashboard, and Real-Time Data Flow components.
 * 
 * It builds upon the existing ComphyonIntegrationLayer but adds specific functionality
 * for visualization and analytics integration.
 */

const EventEmitter = require('events');
const { performance } = require('perf_hooks');

/**
 * UnifiedIntegrationModule class
 */
class UnifiedIntegrationModule extends EventEmitter {
  /**
   * Create a new UnifiedIntegrationModule instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      updateInterval: 1000, // ms
      ...options
    };
    
    // Initialize state
    this.state = {
      isInitialized: false,
      componentRegistry: new Map(), // name -> component
      connectionRegistry: new Map(), // connectionId -> connection
      dataFlowRegistry: new Map(), // flowId -> flow
      lastUpdateTime: Date.now()
    };
    
    // Initialize metrics
    this.metrics = {
      processingTimeMs: 0,
      dataFlowsProcessed: 0,
      componentsRegistered: 0,
      connectionsEstablished: 0,
      visualizationUpdates: 0,
      analyticsUpdates: 0,
      tensorOperations: 0
    };
    
    // Initialize update interval
    if (this.options.updateInterval > 0) {
      this._startUpdateInterval();
    }
    
    // Log initialization
    if (this.options.enableLogging) {
      console.log('UnifiedIntegrationModule initialized');
    }
  }
  
  /**
   * Register a component with the integration module
   * @param {string} name - Component name
   * @param {Object} component - Component instance
   * @param {Object} metadata - Component metadata
   * @returns {boolean} - Success status
   */
  registerComponent(name, component, metadata = {}) {
    const startTime = performance.now();
    
    // Validate parameters
    if (!name || typeof name !== 'string') {
      throw new Error('Component name must be a non-empty string');
    }
    
    if (!component) {
      throw new Error('Component instance is required');
    }
    
    // Check if component already exists
    if (this.state.componentRegistry.has(name)) {
      throw new Error(`Component already registered: ${name}`);
    }
    
    // Determine component type if not provided
    if (!metadata.type) {
      if (name.includes('tensor')) {
        metadata.type = 'tensor';
      } else if (name.includes('visualization') || name.includes('visual')) {
        metadata.type = 'visualization';
      } else if (name.includes('analytics') || name.includes('dashboard')) {
        metadata.type = 'analytics';
      } else if (name.includes('data') || name.includes('flow')) {
        metadata.type = 'dataflow';
      } else {
        metadata.type = 'unknown';
      }
    }
    
    // Register component
    this.state.componentRegistry.set(name, {
      instance: component,
      metadata: {
        type: metadata.type,
        version: metadata.version || '1.0.0',
        interfaces: metadata.interfaces || [],
        dependencies: metadata.dependencies || [],
        ...metadata
      },
      connections: [],
      dataFlows: [],
      registeredAt: Date.now()
    });
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.componentsRegistered++;
    
    // Emit event
    this.emit('component-registered', {
      name,
      type: metadata.type,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`UnifiedIntegrationModule: Registered component ${name} (${metadata.type})`);
    }
    
    return true;
  }
  
  /**
   * Get a registered component
   * @param {string} name - Component name
   * @returns {Object|null} - Component instance or null if not found
   */
  getComponent(name) {
    const component = this.state.componentRegistry.get(name);
    return component ? component.instance : null;
  }
  
  /**
   * Get component metadata
   * @param {string} name - Component name
   * @returns {Object|null} - Component metadata or null if not found
   */
  getComponentMetadata(name) {
    const component = this.state.componentRegistry.get(name);
    return component ? component.metadata : null;
  }
  
  /**
   * Get all registered components
   * @param {string} [type] - Filter by component type
   * @returns {Array} - Array of component names
   */
  getComponents(type) {
    const components = [];
    
    for (const [name, component] of this.state.componentRegistry.entries()) {
      if (!type || component.metadata.type === type) {
        components.push(name);
      }
    }
    
    return components;
  }
  
  /**
   * Connect two components
   * @param {string} sourceComponent - Source component name
   * @param {string} targetComponent - Target component name
   * @param {Object} options - Connection options
   * @returns {string} - Connection ID
   */
  createConnection(sourceComponent, targetComponent, options = {}) {
    const startTime = performance.now();
    
    // Validate parameters
    if (!sourceComponent || !targetComponent) {
      throw new Error('Source and target components are required');
    }
    
    // Check if components exist
    if (!this.state.componentRegistry.has(sourceComponent)) {
      throw new Error(`Source component not found: ${sourceComponent}`);
    }
    
    if (!this.state.componentRegistry.has(targetComponent)) {
      throw new Error(`Target component not found: ${targetComponent}`);
    }
    
    // Create connection ID
    const connectionId = `conn-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    // Create connection
    const connection = {
      id: connectionId,
      sourceComponent,
      targetComponent,
      type: options.type || 'bidirectional', // unidirectional, bidirectional
      protocol: options.protocol || 'event', // event, method, stream
      metadata: options.metadata || {},
      status: 'active',
      createdAt: Date.now()
    };
    
    // Store connection
    this.state.connectionRegistry.set(connectionId, connection);
    
    // Update component connections
    const sourceComponentData = this.state.componentRegistry.get(sourceComponent);
    sourceComponentData.connections.push(connectionId);
    
    const targetComponentData = this.state.componentRegistry.get(targetComponent);
    targetComponentData.connections.push(connectionId);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.connectionsEstablished++;
    
    // Emit event
    this.emit('connection-created', {
      id: connectionId,
      sourceComponent,
      targetComponent,
      type: connection.type,
      protocol: connection.protocol,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`UnifiedIntegrationModule: Created connection from ${sourceComponent} to ${targetComponent}`);
    }
    
    return connectionId;
  }
  
  /**
   * Create a data flow between components
   * @param {string} sourceComponent - Source component name
   * @param {string} targetComponent - Target component name
   * @param {Object} options - Data flow options
   * @returns {string} - Data flow ID
   */
  createDataFlow(sourceComponent, targetComponent, options = {}) {
    const startTime = performance.now();
    
    // Validate parameters
    if (!sourceComponent || !targetComponent) {
      throw new Error('Source and target components are required');
    }
    
    // Check if components exist
    if (!this.state.componentRegistry.has(sourceComponent)) {
      throw new Error(`Source component not found: ${sourceComponent}`);
    }
    
    if (!this.state.componentRegistry.has(targetComponent)) {
      throw new Error(`Target component not found: ${targetComponent}`);
    }
    
    // Create data flow ID
    const flowId = `flow-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    // Create data flow
    const dataFlow = {
      id: flowId,
      sourceComponent,
      targetComponent,
      dataType: options.dataType || 'tensor', // tensor, visualization, analytics
      direction: options.direction || 'forward', // forward, backward, bidirectional
      transformations: options.transformations || [],
      filters: options.filters || [],
      priority: options.priority || 'medium', // low, medium, high, critical
      metadata: options.metadata || {},
      status: 'active',
      createdAt: Date.now()
    };
    
    // Store data flow
    this.state.dataFlowRegistry.set(flowId, dataFlow);
    
    // Update component data flows
    const sourceComponentData = this.state.componentRegistry.get(sourceComponent);
    sourceComponentData.dataFlows.push(flowId);
    
    const targetComponentData = this.state.componentRegistry.get(targetComponent);
    targetComponentData.dataFlows.push(flowId);
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.dataFlowsProcessed++;
    
    // Emit event
    this.emit('data-flow-created', {
      id: flowId,
      sourceComponent,
      targetComponent,
      dataType: dataFlow.dataType,
      direction: dataFlow.direction,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`UnifiedIntegrationModule: Created data flow from ${sourceComponent} to ${targetComponent}`);
    }
    
    return flowId;
  }
  
  /**
   * Process data through a data flow
   * @param {string} flowId - Data flow ID
   * @param {*} data - Data to process
   * @returns {*} - Processed data
   */
  processDataFlow(flowId, data) {
    const startTime = performance.now();
    
    // Validate parameters
    if (!flowId) {
      throw new Error('Data flow ID is required');
    }
    
    // Check if data flow exists
    if (!this.state.dataFlowRegistry.has(flowId)) {
      throw new Error(`Data flow not found: ${flowId}`);
    }
    
    // Get data flow
    const dataFlow = this.state.dataFlowRegistry.get(flowId);
    
    // Check if data flow is active
    if (dataFlow.status !== 'active') {
      throw new Error(`Data flow is not active: ${flowId}`);
    }
    
    // Apply filters
    let processedData = data;
    let shouldProcess = true;
    
    for (const filter of dataFlow.filters) {
      if (!filter(processedData)) {
        shouldProcess = false;
        break;
      }
    }
    
    if (!shouldProcess) {
      if (this.options.enableLogging) {
        console.log(`UnifiedIntegrationModule: Data filtered out for flow ${flowId}`);
      }
      return null;
    }
    
    // Apply transformations
    for (const transformation of dataFlow.transformations) {
      processedData = transformation(processedData);
    }
    
    // Get source and target components
    const sourceComponent = this.state.componentRegistry.get(dataFlow.sourceComponent);
    const targetComponent = this.state.componentRegistry.get(dataFlow.targetComponent);
    
    // Update metrics based on data type
    if (dataFlow.dataType === 'visualization') {
      this.metrics.visualizationUpdates++;
    } else if (dataFlow.dataType === 'analytics') {
      this.metrics.analyticsUpdates++;
    } else if (dataFlow.dataType === 'tensor') {
      this.metrics.tensorOperations++;
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
    this.metrics.dataFlowsProcessed++;
    
    // Emit event
    this.emit('data-flow-processed', {
      id: flowId,
      sourceComponent: dataFlow.sourceComponent,
      targetComponent: dataFlow.targetComponent,
      dataType: dataFlow.dataType,
      timestamp: Date.now()
    });
    
    return processedData;
  }
  
  /**
   * Start update interval
   * @private
   */
  _startUpdateInterval() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
    }
    
    this._updateInterval = setInterval(() => {
      this._processUpdates();
    }, this.options.updateInterval);
  }
  
  /**
   * Process updates
   * @private
   */
  _processUpdates() {
    const now = Date.now();
    this.state.lastUpdateTime = now;
    
    // Emit update event
    this.emit('update', {
      timestamp: now,
      metrics: this.getMetrics()
    });
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Stop the integration module
   */
  stop() {
    if (this._updateInterval) {
      clearInterval(this._updateInterval);
      this._updateInterval = null;
    }
    
    if (this.options.enableLogging) {
      console.log('UnifiedIntegrationModule stopped');
    }
  }
}

module.exports = UnifiedIntegrationModule;
