import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Create context
const NovaConnectContext = createContext();

// Initial state
const initialState = {
  isQuantumAvailable: true,
  metrics: {
    quantumSuccessRate: 1.0,  // Start assuming 100% success
    fallbackFrequency: 0,
    readinessScore: 1.0,
    totalRequests: 0,
    quantumRequests: 0,
    classicalRequests: 0,
    lastUpdated: null
  },
  endpointStatus: {
    quantumGateway: 'operational',
    classicalEndpoint: 'operational',
    lastChecked: null
  },
  settings: {
    autoFallback: true,
    logLevel: 'info',
    quantumThreshold: 0.7  // Minimum success rate to consider quantum available
  }
};

// Provider component
export const NovaConnectProvider = ({ children }) => {
  const [state, setState] = useState(initialState);
  const [endpoints, setEndpoints] = useState({
    quantum: '/api/v1/hybrid-assess',
    classical: '/api/v1/classical-assess',
    status: '/api/v1/status'
  });

  // Check endpoint health
  const checkEndpointHealth = useCallback(async (endpoint) => {
    try {
      const response = await fetch(`${endpoint}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });
      
      if (!response.ok) throw new Error('Endpoint not healthy');
      const data = await response.json();
      return data.status === 'healthy';
    } catch (error) {
      console.error(`Health check failed for ${endpoint}:`, error);
      return false;
    }
  }, []);

  // Update metrics when quantum/classical requests are made
  const updateMetrics = useCallback((type, success = true) => {
    setState(prev => {
      const totalRequests = prev.metrics.totalRequests + 1;
      const quantumRequests = type === 'quantum' 
        ? prev.metrics.quantumRequests + 1 
        : prev.metrics.quantumRequests;
      
      const classicalRequests = type === 'classical' 
        ? prev.metrics.classicalRequests + 1 
        : prev.metrics.classicalRequests;
      
      // Calculate success rate for quantum operations
      const quantumSuccessRate = quantumRequests > 0 
        ? (prev.metrics.quantumSuccessRate * (quantumRequests - 1) + (success ? 1 : 0)) / quantumRequests
        : 1.0;

      // Calculate fallback frequency (classical / total)
      const fallbackFrequency = totalRequests > 0 
        ? classicalRequests / totalRequests 
        : 0;

      // Calculate overall readiness score (weighted average)
      const readinessScore = (quantumSuccessRate * 0.7) + 
                           ((1 - fallbackFrequency) * 0.3);

      // Determine if quantum should be considered available
      const isQuantumAvailable = quantumSuccessRate >= prev.settings.quantumThreshold;

      return {
        ...prev,
        isQuantumAvailable,
        metrics: {
          ...prev.metrics,
          totalRequests,
          quantumRequests,
          classicalRequests,
          quantumSuccessRate,
          fallbackFrequency,
          readinessScore,
          lastUpdated: new Date().toISOString()
        },
        endpointStatus: {
          ...prev.endpointStatus,
          quantumGateway: isQuantumAvailable ? 'operational' : 'degraded',
          lastChecked: new Date().toISOString()
        }
      };
    });
  }, []);

  // Monitor endpoints periodically
  useEffect(() => {
    const monitorEndpoints = async () => {
      const [quantumHealthy, classicalHealthy] = await Promise.all([
        checkEndpointHealth(endpoints.quantum),
        checkEndpointHealth(endpoints.classical)
      ]);

      setState(prev => ({
        ...prev,
        endpointStatus: {
          quantumGateway: quantumHealthy ? 'operational' : 'degraded',
          classicalEndpoint: classicalHealthy ? 'operational' : 'degraded',
          lastChecked: new Date().toISOString()
        }
      }));
    };

    // Initial check
    monitorEndpoints();
    
    // Check every 5 minutes
    const interval = setInterval(monitorEndpoints, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [endpoints, checkEndpointHealth]);

  // Update settings
  const updateSettings = useCallback((newSettings) => {
    setState(prev => ({
      ...prev,
      settings: {
        ...prev.settings,
        ...newSettings
      }
    }));
  }, []);

  // Update endpoints
  const updateEndpoints = useCallback((newEndpoints) => {
    setEndpoints(prev => ({
      ...prev,
      ...newEndpoints
    }));
  }, []);

  // Get current status
  const getStatus = useCallback(() => {
    return {
      ...state,
      currentTime: new Date().toISOString()
    };
  }, [state]);

  // Context value
  const value = {
    ...state,
    endpoints,
    updateMetrics,
    updateSettings,
    updateEndpoints,
    getStatus,
    isReady: state.metrics.readinessScore >= 0.8
  };

  return (
    <NovaConnectContext.Provider value={value}>
      {children}
    </NovaConnectContext.Provider>
  );
};

// Custom hook for using the context
export const useNovaConnect = () => {
  const context = useContext(NovaConnectContext);
  if (!context) {
    throw new Error('useNovaConnect must be used within a NovaConnectProvider');
  }
  return context;
};

export default NovaConnectContext;
