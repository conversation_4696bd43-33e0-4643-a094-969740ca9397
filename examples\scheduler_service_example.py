"""
Example of using the Scheduler Service.

This example demonstrates how to use the Scheduler Service to execute
scheduled tasks in the background.
"""

import os
import sys
import json
import logging
import datetime
import time

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.schedule_manager import ScheduleManager, ScheduleInterval, TaskType
from src.ucecs.services.scheduler_service import SchedulerService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def execute_collect_evidence_task(task):
    """
    Execute a collect evidence task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing collect evidence task: {task['name']}")
    
    # Simulate collecting evidence
    time.sleep(1)
    
    return {
        'evidence_id': f"evidence_{int(time.time())}",
        'collector_id': task['parameters'].get('collector_id'),
        'status': 'collected'
    }

def execute_validate_evidence_task(task):
    """
    Execute a validate evidence task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing validate evidence task: {task['name']}")
    
    # Simulate validating evidence
    time.sleep(1)
    
    return {
        'evidence_id': task['parameters'].get('evidence_id'),
        'validator_id': task['parameters'].get('validator_id'),
        'is_valid': True
    }

def execute_generate_report_task(task):
    """
    Execute a generate report task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing generate report task: {task['name']}")
    
    # Simulate generating a report
    time.sleep(1)
    
    return {
        'report_id': f"report_{int(time.time())}",
        'format': task['parameters'].get('format', 'json'),
        'status': 'generated'
    }

def execute_custom_task(task):
    """
    Execute a custom task.
    
    Args:
        task: The task to execute
        
    Returns:
        The result of the task execution
    """
    logger.info(f"Executing custom task: {task['name']}")
    
    # Simulate executing a custom task
    time.sleep(1)
    
    return {
        'custom_result': f"result_{int(time.time())}",
        'status': 'completed'
    }

def main():
    """Run the Scheduler Service example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_scheduler_service_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create a schedules directory
    schedules_dir = os.path.join(temp_dir, 'schedules')
    
    # Create a Schedule Manager
    schedule_manager = ScheduleManager(schedules_dir=schedules_dir)
    
    # Create task executors
    task_executors = {
        TaskType.COLLECT_EVIDENCE: execute_collect_evidence_task,
        TaskType.VALIDATE_EVIDENCE: execute_validate_evidence_task,
        TaskType.GENERATE_REPORT: execute_generate_report_task,
        TaskType.CUSTOM: execute_custom_task
    }
    
    # Create a Scheduler Service
    scheduler_service = SchedulerService(
        schedule_manager=schedule_manager,
        task_executors=task_executors,
        check_interval=5  # Check every 5 seconds for this example
    )
    
    try:
        # Create scheduled tasks that are due to run
        logger.info("Creating scheduled tasks...")
        
        # Create a task that is due immediately
        collect_task_id = schedule_manager.create_scheduled_task(
            name="Collect System Configuration",
            task_type=TaskType.COLLECT_EVIDENCE,
            interval=ScheduleInterval.DAILY,
            parameters={
                'collector_id': 'system_config',
                'parameters': {
                    'include_network': True,
                    'include_users': True
                }
            }
        )
        
        # Make the task due immediately
        collect_task = schedule_manager.get_scheduled_task(collect_task_id)
        collect_task['next_run'] = datetime.datetime.now(datetime.timezone.utc).isoformat()
        schedule_manager._save_scheduled_task(collect_task_id)
        
        # Create a task that will be due in 10 seconds
        validate_task_id = schedule_manager.create_scheduled_task(
            name="Validate System Configuration",
            task_type=TaskType.VALIDATE_EVIDENCE,
            interval=ScheduleInterval.WEEKLY,
            parameters={
                'evidence_id': 'evidence_123',
                'validator_id': 'system_config_validator'
            }
        )
        
        # Make the task due in 10 seconds
        validate_task = schedule_manager.get_scheduled_task(validate_task_id)
        next_run = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=10)
        validate_task['next_run'] = next_run.isoformat()
        schedule_manager._save_scheduled_task(validate_task_id)
        
        # Create a task that will be due in 15 seconds
        report_task_id = schedule_manager.create_scheduled_task(
            name="Generate Compliance Report",
            task_type=TaskType.GENERATE_REPORT,
            interval=ScheduleInterval.MONTHLY,
            parameters={
                'format': 'html',
                'include_evidence': True,
                'include_validation': True
            }
        )
        
        # Make the task due in 15 seconds
        report_task = schedule_manager.get_scheduled_task(report_task_id)
        next_run = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(seconds=15)
        report_task['next_run'] = next_run.isoformat()
        schedule_manager._save_scheduled_task(report_task_id)
        
        # Start the scheduler service
        logger.info("Starting the scheduler service...")
        scheduler_service.start()
        
        # Wait for tasks to be executed
        logger.info("Waiting for tasks to be executed...")
        time.sleep(20)
        
        # Stop the scheduler service
        logger.info("Stopping the scheduler service...")
        scheduler_service.stop()
        
        # Get task history
        logger.info("Getting task history...")
        collect_history = schedule_manager.get_task_history(collect_task_id)
        validate_history = schedule_manager.get_task_history(validate_task_id)
        report_history = schedule_manager.get_task_history(report_task_id)
        
        logger.info(f"Collect task history: {json.dumps(collect_history, indent=2)}")
        logger.info(f"Validate task history: {json.dumps(validate_history, indent=2)}")
        logger.info(f"Report task history: {json.dumps(report_history, indent=2)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
