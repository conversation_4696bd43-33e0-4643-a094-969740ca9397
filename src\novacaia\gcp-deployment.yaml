# NovaCaia Enterprise GCP Deployment Configuration
# Strategic proof-of-concept for Fortune 500 validation

apiVersion: v1
kind: Namespace
metadata:
  name: novacaia-gcp
  labels:
    environment: production
    cloud-provider: gcp
    compliance: enterprise-grade
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: novacaia-gcp-deployment
  namespace: novacaia-gcp
  labels:
    app: novacaia
    version: v1.0.0-enterprise
    cloud: gcp
    purpose: strategic-validation
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 0
  selector:
    matchLabels:
      app: novacaia
  template:
    metadata:
      labels:
        app: novacaia
        version: v1.0.0-enterprise
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "7777"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: novacaia-gcp-sa
      containers:
      - name: novacaia
        image: gcr.io/PROJECT_ID/novacaia:v1.0.0-enterprise
        ports:
        - containerPort: 7777
          name: http
        env:
        - name: CLOUD_PROVIDER
          value: "GCP"
        - name: PLATFORM_ALLOCATION
          value: "18.0"
        - name: ENTERPRISE_RETENTION
          value: "82.0"
        - name: PRODUCTION_MODE
          value: "True"
        - name: CONSCIOUSNESS_THRESHOLD
          value: "0.91"
        - name: ACCURACY_TARGET
          value: "0.9783"
        - name: GCP_PROJECT_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
        livenessProbe:
          httpGet:
            path: /health
            port: 7777
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 7777
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      volumes:
      - name: google-cloud-key
        secret:
          secretName: google-cloud-key
---
apiVersion: v1
kind: Service
metadata:
  name: novacaia-gcp-service
  namespace: novacaia-gcp
  labels:
    app: novacaia
  annotations:
    cloud.google.com/load-balancer-type: "External"
spec:
  type: LoadBalancer
  selector:
    app: novacaia
  ports:
  - name: http
    port: 80
    targetPort: 7777
    protocol: TCP
  - name: https
    port: 443
    targetPort: 7777
    protocol: TCP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: novacaia-gcp-hpa
  namespace: novacaia-gcp
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: novacaia-gcp-deployment
  minReplicas: 3
  maxReplicas: 1000
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: consciousness_score
      target:
        type: AverageValue
        averageValue: "0.91"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 10
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: novacaia-gcp-sa
  namespace: novacaia-gcp
  annotations:
    iam.gke.io/gcp-service-account: novacaia-gcp@PROJECT_ID.iam.gserviceaccount.com
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: novacaia-ssl-cert
  namespace: novacaia-gcp
spec:
  domains:
  - api.novacaia.com
  - gcp.novacaia.com
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: novacaia-gcp-ingress
  namespace: novacaia-gcp
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "novacaia-ip"
    networking.gke.io/managed-certificates: "novacaia-ssl-cert"
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.allow-http: "false"
spec:
  rules:
  - host: gcp.novacaia.com
    http:
      paths:
      - path: /*
        pathType: ImplementationSpecific
        backend:
          service:
            name: novacaia-gcp-service
            port:
              number: 80
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: novacaia-gcp-config
  namespace: novacaia-gcp
data:
  gcp-config.json: |
    {
      "cloud_provider": "GCP",
      "environment": "production",
      "strategic_validation": true,
      "compliance_frameworks": [
        "GDPR",
        "EU_AI_Act",
        "FedRAMP",
        "SOC2_Type_II",
        "ISO_27001"
      ],
      "performance_targets": {
        "latency_p99_ms": 10,
        "throughput_rps": 100000,
        "consciousness_threshold": 0.91,
        "accuracy_target": 0.9783,
        "false_positive_rate": 0.0
      },
      "economic_model": {
        "platform_allocation": 18.0,
        "enterprise_retention": 82.0,
        "cost_per_million_inferences": 18.0,
        "industry_standard_cost": 100.0,
        "cost_advantage": "82% reduction"
      },
      "gcp_integrations": {
        "monitoring": "Cloud Monitoring",
        "pubsub": "Pub/Sub for 1M+ queries/sec",
        "secrets": "Secret Manager for CASTL™ keys",
        "security": "Cloud Armor for adversarial protection",
        "storage": "Cloud Storage for audit logs",
        "ai": "Vertex AI integration ready"
      }
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: novacaia-gcp-secrets
  namespace: novacaia-gcp
type: Opaque
data:
  # Base64 encoded secrets
  castl-validation-key: Y2FzdGwtdmFsaWRhdGlvbi1rZXk=
  consciousness-threshold: MC45MQ==
  enterprise-api-key: ZW50ZXJwcmlzZS1hcGkta2V5
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: novacaia-gcp-pdb
  namespace: novacaia-gcp
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: novacaia
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: novacaia-gcp-netpol
  namespace: novacaia-gcp
spec:
  podSelector:
    matchLabels:
      app: novacaia
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 7777
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
