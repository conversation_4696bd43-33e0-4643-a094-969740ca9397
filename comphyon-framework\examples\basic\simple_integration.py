"""
Simple Integration Example for the ComphyonΨᶜ Framework.

This example demonstrates the basic integration of the ComphyonΨᶜ Meter and
ComphyonΨᶜ Governor components.
"""

import sys
import os
import time
import numpy as np
import matplotlib.pyplot as plt

# Add the parent directory to the path so we can import the packages
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../comphyon-meter')))
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../comphyon-governor')))

try:
    from src.comphyon_meter import ComphyonMeter
    from src.comphyon_governor import ComphyonGovernor
except ImportError:
    print("Error: Could not import ComphyonMeter or ComphyonGovernor.")
    print("Please make sure you have cloned the repositories and installed the packages.")
    print("See the README.md for instructions.")
    sys.exit(1)

def generate_tensor_data(t, base_values, oscillation_factor=0.05, trend_factor=0.02):
    """
    Generate tensor data with oscillation and trend.
    
    Args:
        t: Time value
        base_values: Base values for the tensor
        oscillation_factor: Factor for oscillation amplitude
        trend_factor: Factor for trend amplitude
        
    Returns:
        list: Generated tensor data
    """
    oscillation = oscillation_factor * np.sin(t / 5)
    trend = trend_factor * np.sin(t / 20)
    
    tensor = [
        base_values[0] + trend + np.random.normal(0, 0.03),
        base_values[1] + oscillation + np.random.normal(0, 0.03),
        base_values[2] + np.random.normal(0, 0.03),
        base_values[3] + np.random.normal(0, 0.03)
    ]
    
    # Clip values to valid range
    return [max(0.1, min(0.99, x)) for x in tensor]

def main():
    """
    Run the simple integration example.
    """
    print("ComphyonΨᶜ Framework - Simple Integration Example")
    print("================================================")
    
    # Initialize the ComphyonΨᶜ Meter
    meter = ComphyonMeter()
    print("ComphyonΨᶜ Meter initialized.")
    
    # Initialize the ComphyonΨᶜ Governor with custom thresholds
    governor = ComphyonGovernor(
        thresholds={
            'acceleration': 1.5,  # Trigger control at 1.5 Cph
            'velocity': 60.0      # Trigger control at 60.0 Cph-Flux
        }
    )
    print("ComphyonΨᶜ Governor initialized.")
    
    print(f"Thresholds: Acceleration = {governor.thresholds['acceleration']} Cph, "
          f"Velocity = {governor.thresholds['velocity']} Cph-Flux")
    
    # Base values for tensor data
    csde_base = [0.75, 0.85, 0.65, 0.90]
    csfe_base = [0.65, 0.70, 0.80, 0.80]
    csme_base = [0.70, 0.90, 0.60, 0.85]
    
    # Monitoring and control loop
    print("\nStarting monitoring and control loop...")
    print("(Press Ctrl+C to exit)")
    print("\n{:<10} {:<15} {:<15} {:<20} {:<20}".format(
        "Time (s)", "Acceleration", "Velocity", "Control Action", "Reason"))
    print("-" * 80)
    
    start_time = time.time()
    metrics_history = []
    control_history = []
    
    try:
        for i in range(30):  # Run for 30 iterations
            # Calculate elapsed time
            elapsed = time.time() - start_time
            
            # Generate tensor data with increasing trend over time
            trend_factor = 0.02 * (1 + i / 10)  # Increase trend factor over time
            
            csde_tensor = generate_tensor_data(elapsed, csde_base, trend_factor=trend_factor)
            csfe_tensor = generate_tensor_data(elapsed, csfe_base, trend_factor=trend_factor)
            csme_tensor = generate_tensor_data(elapsed, csme_base)
            
            # Calculate metrics
            metrics = meter.calculate(csde_tensor, csfe_tensor, csme_tensor)
            metrics_history.append(metrics)
            
            # Apply control actions if needed
            control_actions = governor.regulate(metrics)
            
            # If control actions were applied, adjust the trend factor
            if control_actions['type'] != 'none':
                control_history.append(control_actions)
                # Simulate the effect of control actions by reducing the trend factor
                trend_factor *= 0.5
            
            # Display status
            print("{:<10.1f} {:<15.4f} {:<15.4f} {:<20} {:<20}".format(
                elapsed,
                metrics['acceleration'],
                metrics['velocity'],
                control_actions['type'],
                control_actions.get('reason', 'N/A')[:20]
            ))
            
            # Sleep for a bit
            time.sleep(0.5)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")
    
    # Display control history
    if control_history:
        print("\nControl History:")
        print("-" * 80)
        
        for i, event in enumerate(control_history):
            print(f"Event {i+1}:")
            print(f"  Type: {event['type']}")
            print(f"  Reason: {event.get('reason', 'N/A')}")
            print(f"  Severity: {event.get('severity', 'N/A')}")
            print(f"  Actions: {len(event['actions'])} actions applied")
            
            # Display the first few actions
            for j, action in enumerate(event['actions'][:3]):
                print(f"    - {action['level']} level: {action['type']} on {action['target']}")
            
            if len(event['actions']) > 3:
                print(f"    - ... and {len(event['actions']) - 3} more actions")
            
            print()
    else:
        print("\nNo control actions were applied during this run.")
    
    # Plot the metrics
    print("\nPlotting metrics...")
    fig = meter.visualizer.plot_metrics_time_series(metrics_history)
    plt.show()
    
    print("\nExample completed successfully.")

if __name__ == "__main__":
    main()
