cmake_minimum_required(VERSION 3.18)
project(csde_engine LANGUAGES CXX CUDA)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set CUDA standard
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# Find CUDA
find_package(CUDA REQUIRED)

# Include directories
include_directories(${CUDA_INCLUDE_DIRS})

# Add CUDA flags
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -arch=sm_70")

# Add source files
set(SOURCES
    csde_engine.cpp
    csde_test.cpp
    cuda/csde_kernels.cu
)

# Add header files
set(HEADERS
    csde_engine.h
    cuda/csde_kernels.cuh
)

# Create executable
add_executable(csde_test ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(csde_test ${CUDA_LIBRARIES})

# Set output directory
set_target_properties(csde_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# Install target
install(TARGETS csde_test DESTINATION bin)
