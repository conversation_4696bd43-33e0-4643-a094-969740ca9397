/**
 * CSDE Performance Test Script
 * 
 * This script tests the performance of the CSDE engine to validate the 3,142x performance improvement.
 */

const { CSDEEngine } = require('../index');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Configuration
const config = {
  iterations: 1000,
  warmupIterations: 100,
  outputDir: path.join(__dirname, '..', 'performance'),
  baselineLatency: 220, // ms - baseline latency for traditional compliance processing
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Sample data for testing
const complianceData = {
  complianceScore: 0.75,
  controls: [
    {
      id: 'AC-2',
      name: 'Account Management',
      description: 'The organization needs to implement account management procedures',
      severity: 'high',
      status: 'non-compliant',
      framework: 'NIST 800-53'
    },
    {
      id: 'CM-7',
      name: 'Least Functionality',
      description: 'The organization needs to configure systems to provide only essential capabilities',
      severity: 'medium',
      status: 'partial',
      framework: 'NIST 800-53'
    },
    {
      id: 'SC-7',
      name: 'Boundary Protection',
      description: 'The organization needs to implement boundary protection mechanisms',
      severity: 'high',
      status: 'compliant',
      framework: 'NIST 800-53'
    }
  ]
};

const gcpData = {
  integrationScore: 0.85,
  services: [
    {
      id: 'GCP-IAM-1',
      name: 'IAM Role Configuration',
      description: 'IAM roles need to be configured with least privilege',
      severity: 'high',
      status: 'non-optimal',
      service: 'Cloud IAM'
    },
    {
      id: 'GCP-VPC-1',
      name: 'VPC Network Security',
      description: 'VPC network security needs to be enhanced',
      severity: 'medium',
      status: 'partial',
      service: 'VPC Network'
    },
    {
      id: 'GCP-KMS-1',
      name: 'Key Management',
      description: 'Cloud KMS keys need to be properly managed',
      severity: 'high',
      status: 'optimal',
      service: 'Cloud KMS'
    }
  ]
};

const cyberSafetyData = {
  safetyScore: 0.65,
  controls: [
    {
      id: 'CS-P3-1',
      name: 'Self-Destructing Compliance Servers',
      description: 'Implement self-destructing compliance servers with hardware-enforced geo-fencing',
      severity: 'high',
      status: 'not-implemented',
      pillar: 'Pillar 3'
    },
    {
      id: 'CS-P9-1',
      name: 'Post-Quantum Immutable Compliance Journal',
      description: 'Implement post-quantum immutable compliance journal',
      severity: 'medium',
      status: 'partial',
      pillar: 'Pillar 9'
    },
    {
      id: 'CS-P12-1',
      name: 'C-Suite Directive to Code Compiler',
      description: 'Implement C-Suite Directive to Code Compiler',
      severity: 'medium',
      status: 'implemented',
      pillar: 'Pillar 12'
    }
  ]
};

// Initialize CSDE Engine
const csdeEngine = new CSDEEngine();

// System information
const systemInfo = {
  platform: os.platform(),
  release: os.release(),
  arch: os.arch(),
  cpus: os.cpus().length,
  totalMemory: os.totalmem(),
  freeMemory: os.freemem()
};

// Performance metrics
const metrics = {
  totalIterations: config.iterations,
  warmupIterations: config.warmupIterations,
  latencies: [],
  minLatency: Number.MAX_VALUE,
  maxLatency: 0,
  avgLatency: 0,
  medianLatency: 0,
  p95Latency: 0,
  p99Latency: 0,
  totalTime: 0,
  iterationsPerSecond: 0,
  performanceFactor: 0,
  systemInfo
};

// Run performance test
async function runPerformanceTest() {
  console.log('Starting CSDE Performance Test...');
  console.log(`Iterations: ${config.iterations}`);
  console.log(`Warmup Iterations: ${config.warmupIterations}`);
  
  // Warmup
  console.log('\nPerforming warmup...');
  for (let i = 0; i < config.warmupIterations; i++) {
    csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);
    if ((i + 1) % 10 === 0) {
      process.stdout.write('.');
    }
  }
  console.log('\nWarmup completed.');
  
  // Clear cache
  csdeEngine.clearCache();
  
  // Main test
  console.log('\nRunning performance test...');
  const startTime = performance.now();
  
  for (let i = 0; i < config.iterations; i++) {
    const iterationStart = performance.now();
    csdeEngine.calculate(complianceData, gcpData, cyberSafetyData);
    const iterationEnd = performance.now();
    const latency = iterationEnd - iterationStart;
    
    metrics.latencies.push(latency);
    metrics.minLatency = Math.min(metrics.minLatency, latency);
    metrics.maxLatency = Math.max(metrics.maxLatency, latency);
    
    if ((i + 1) % 100 === 0) {
      process.stdout.write('.');
    }
  }
  
  const endTime = performance.now();
  metrics.totalTime = endTime - startTime;
  
  console.log('\nPerformance test completed.');
  
  // Calculate metrics
  metrics.avgLatency = metrics.latencies.reduce((sum, latency) => sum + latency, 0) / config.iterations;
  
  // Sort latencies for percentile calculations
  const sortedLatencies = [...metrics.latencies].sort((a, b) => a - b);
  metrics.medianLatency = sortedLatencies[Math.floor(config.iterations / 2)];
  metrics.p95Latency = sortedLatencies[Math.floor(config.iterations * 0.95)];
  metrics.p99Latency = sortedLatencies[Math.floor(config.iterations * 0.99)];
  
  metrics.iterationsPerSecond = config.iterations / (metrics.totalTime / 1000);
  metrics.performanceFactor = config.baselineLatency / metrics.avgLatency;
  
  // Save metrics to file
  const metricsPath = path.join(config.outputDir, 'performance_metrics.json');
  fs.writeFileSync(metricsPath, JSON.stringify(metrics, null, 2));
  
  // Generate report
  generateReport(metrics);
  
  console.log(`\nPerformance metrics saved to ${metricsPath}`);
}

// Generate performance report
function generateReport(metrics) {
  console.log('\nCSDE Performance Test Results:');
  console.log('-----------------------------');
  console.log(`Total Iterations: ${metrics.totalIterations}`);
  console.log(`Total Time: ${metrics.totalTime.toFixed(2)} ms`);
  console.log(`Iterations Per Second: ${metrics.iterationsPerSecond.toFixed(2)}`);
  console.log('\nLatency Metrics:');
  console.log(`Min Latency: ${metrics.minLatency.toFixed(2)} ms`);
  console.log(`Max Latency: ${metrics.maxLatency.toFixed(2)} ms`);
  console.log(`Average Latency: ${metrics.avgLatency.toFixed(2)} ms`);
  console.log(`Median Latency: ${metrics.medianLatency.toFixed(2)} ms`);
  console.log(`95th Percentile Latency: ${metrics.p95Latency.toFixed(2)} ms`);
  console.log(`99th Percentile Latency: ${metrics.p99Latency.toFixed(2)} ms`);
  
  console.log('\nPerformance Factor:');
  console.log(`Baseline Latency: ${config.baselineLatency.toFixed(2)} ms`);
  console.log(`CSDE Latency: ${metrics.avgLatency.toFixed(2)} ms`);
  console.log(`Performance Improvement: ${metrics.performanceFactor.toFixed(2)}x`);
  
  // Check if we achieved the 3,142x performance improvement
  const targetFactor = 3142;
  const achievedFactor = metrics.performanceFactor;
  
  if (achievedFactor >= targetFactor) {
    console.log(`\n✅ VALIDATED: ${achievedFactor.toFixed(2)}x performance improvement exceeds the target of ${targetFactor}x`);
  } else {
    console.log(`\n❌ NOT VALIDATED: ${achievedFactor.toFixed(2)}x performance improvement does not meet the target of ${targetFactor}x`);
    console.log(`   Performance gap: ${(targetFactor - achievedFactor).toFixed(2)}x`);
  }
  
  // Generate HTML report
  const htmlReport = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>CSDE Performance Test Results</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #f8f9fa;
      padding: 20px;
    }
    .card {
      margin-bottom: 20px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .card-header {
      background-color: #0a84ff;
      color: white;
      font-weight: bold;
      border-radius: 10px 10px 0 0 !important;
    }
    .metric-value {
      font-size: 2.5rem;
      font-weight: bold;
      color: #0a84ff;
    }
    .metric-label {
      font-size: 1rem;
      color: #6c757d;
    }
    .chart-container {
      height: 400px;
      margin-bottom: 20px;
    }
    .validation-badge {
      font-size: 1.5rem;
      padding: 10px 20px;
      border-radius: 10px;
      display: inline-block;
      margin-bottom: 20px;
    }
    .validation-badge.success {
      background-color: #28a745;
      color: white;
    }
    .validation-badge.failure {
      background-color: #dc3545;
      color: white;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="row mb-4">
      <div class="col-12">
        <h1 class="text-center">CSDE Performance Test Results</h1>
        <p class="text-center text-muted">Validation of the 3,142x performance improvement</p>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12 text-center">
        <div class="validation-badge ${metrics.performanceFactor >= targetFactor ? 'success' : 'failure'}">
          ${metrics.performanceFactor >= targetFactor ? '✅ VALIDATED' : '❌ NOT VALIDATED'}: 
          ${metrics.performanceFactor.toFixed(2)}x performance improvement
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">Performance Factor</div>
          <div class="card-body text-center">
            <div class="metric-value">${metrics.performanceFactor.toFixed(2)}x</div>
            <div class="metric-label">Improvement over baseline</div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">Average Latency</div>
          <div class="card-body text-center">
            <div class="metric-value">${metrics.avgLatency.toFixed(2)} ms</div>
            <div class="metric-label">Per calculation</div>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">Throughput</div>
          <div class="card-body text-center">
            <div class="metric-value">${metrics.iterationsPerSecond.toFixed(2)}</div>
            <div class="metric-label">Calculations per second</div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Latency Distribution</div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="latencyChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">Latency Percentiles</div>
          <div class="card-body">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Percentile</th>
                  <th>Latency (ms)</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Minimum</td>
                  <td>${metrics.minLatency.toFixed(2)}</td>
                </tr>
                <tr>
                  <td>Median (50th)</td>
                  <td>${metrics.medianLatency.toFixed(2)}</td>
                </tr>
                <tr>
                  <td>95th</td>
                  <td>${metrics.p95Latency.toFixed(2)}</td>
                </tr>
                <tr>
                  <td>99th</td>
                  <td>${metrics.p99Latency.toFixed(2)}</td>
                </tr>
                <tr>
                  <td>Maximum</td>
                  <td>${metrics.maxLatency.toFixed(2)}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">System Information</div>
          <div class="card-body">
            <table class="table table-striped">
              <tbody>
                <tr>
                  <td>Platform</td>
                  <td>${metrics.systemInfo.platform}</td>
                </tr>
                <tr>
                  <td>OS Version</td>
                  <td>${metrics.systemInfo.release}</td>
                </tr>
                <tr>
                  <td>Architecture</td>
                  <td>${metrics.systemInfo.arch}</td>
                </tr>
                <tr>
                  <td>CPU Cores</td>
                  <td>${metrics.systemInfo.cpus}</td>
                </tr>
                <tr>
                  <td>Total Memory</td>
                  <td>${(metrics.systemInfo.totalMemory / (1024 * 1024 * 1024)).toFixed(2)} GB</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header">Test Configuration</div>
          <div class="card-body">
            <table class="table table-striped">
              <tbody>
                <tr>
                  <td>Total Iterations</td>
                  <td>${metrics.totalIterations}</td>
                </tr>
                <tr>
                  <td>Warmup Iterations</td>
                  <td>${metrics.warmupIterations}</td>
                </tr>
                <tr>
                  <td>Total Test Time</td>
                  <td>${metrics.totalTime.toFixed(2)} ms</td>
                </tr>
                <tr>
                  <td>Baseline Latency</td>
                  <td>${config.baselineLatency.toFixed(2)} ms</td>
                </tr>
                <tr>
                  <td>Target Performance Factor</td>
                  <td>${targetFactor}x</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Create latency histogram
    const latencyCtx = document.getElementById('latencyChart').getContext('2d');
    
    // Create histogram bins
    const latencies = ${JSON.stringify(metrics.latencies)};
    const min = ${metrics.minLatency};
    const max = ${metrics.maxLatency};
    const binCount = 20;
    const binSize = (max - min) / binCount;
    
    const bins = Array(binCount).fill(0);
    latencies.forEach(latency => {
      const binIndex = Math.min(Math.floor((latency - min) / binSize), binCount - 1);
      bins[binIndex]++;
    });
    
    const binLabels = Array(binCount).fill(0).map((_, i) => {
      const start = min + i * binSize;
      const end = min + (i + 1) * binSize;
      return \`\${start.toFixed(2)} - \${end.toFixed(2)}\`;
    });
    
    new Chart(latencyCtx, {
      type: 'bar',
      data: {
        labels: binLabels,
        datasets: [{
          label: 'Number of Calculations',
          data: bins,
          backgroundColor: 'rgba(10, 132, 255, 0.6)',
          borderColor: 'rgba(10, 132, 255, 1)',
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Number of Calculations'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Latency (ms)'
            }
          }
        },
        plugins: {
          title: {
            display: true,
            text: 'Distribution of Calculation Latencies'
          }
        }
      }
    });
  </script>
</body>
</html>
`;

  // Save HTML report
  const htmlReportPath = path.join(config.outputDir, 'performance_report.html');
  fs.writeFileSync(htmlReportPath, htmlReport);
  console.log(`\nHTML report saved to ${htmlReportPath}`);
}

// Run the performance test
runPerformanceTest();
