/**
 * 🪐 NovaNexxus™: The Living Architecture of Cyber-Safety
 * 
 * This example demonstrates our implementation of the NovaNexxus architecture,
 * which integrates all Nova components with the Cyber-Safety Domain Engine (CSDE)
 * to create a unified Cyber-Safety platform.
 * 
 * NovaNexxus enforces NIST compliance by design and action across all components.
 */

// Import required modules
const { 
  createAndInitializeFullNovaNexxus,
  CSEDOperationType,
  CSEDDomainType
} = require('../src/novanexxus/factory');

const {
  Tensor,
  Event
} = require('../src/novacore');

const {
  Evidence,
  BlockchainType
} = require('../src/novaproof');

// Configure logging
const logger = {
  info: (...args) => console.log('\x1b[36m%s\x1b[0m', '[INFO]', ...args),
  warn: (...args) => console.log('\x1b[33m%s\x1b[0m', '[WARN]', ...args),
  error: (...args) => console.log('\x1b[31m%s\x1b[0m', '[ERROR]', ...args),
  success: (...args) => console.log('\x1b[32m%s\x1b[0m', '[SUCCESS]', ...args)
};

/**
 * Example 1: Process a tensor with NovaCore and enhance it with CSDE
 */
async function demoTensorProcessing(novanexxus) {
  logger.info('\n--- Example 1: Tensor Processing with CSDE Enhancement ---');
  
  try {
    // Create a tensor
    const tensor = new Tensor(
      [3, 3],
      [1, 2, 3, 4, 5, 6, 7, 8, 9],
      { name: 'Example Tensor' }
    );
    
    logger.info('Created tensor:', tensor);
    
    // Process the tensor with NovaCore
    const processEvent = new Event('tensor.process', {
      tensor,
      operation: 'normalize',
      options: { axis: 0 }
    });
    
    const processResult = await novanexxus.eventProcessor.processEvent(processEvent);
    const processedTensor = processResult.data.result;
    
    logger.info('Processed tensor:', processedTensor);
    
    // Enhance the tensor with CSDE
    const enhanceEvent = new Event('tensor.enhance', {
      tensor: processedTensor,
      domain: CSEDDomainType.COMPLIANCE,
      options: { priority: 1 }
    });
    
    const enhanceResult = await novanexxus.eventProcessor.processEvent(enhanceEvent);
    const enhancedTensor = enhanceResult.data.result;
    
    logger.info('Enhanced tensor:', enhancedTensor);
    logger.info('CSDE metadata:', enhancedTensor.metadata.csde);
    
    logger.success('Tensor processing with CSDE enhancement completed successfully');
    return enhancedTensor;
  } catch (error) {
    logger.error('Error in tensor processing demo:', error);
    throw error;
  }
}

/**
 * Example 2: Verify evidence with NovaProof and enhance it with CSDE
 */
async function demoEvidenceVerification(novanexxus) {
  logger.info('\n--- Example 2: Evidence Verification with CSDE Enhancement ---');
  
  try {
    // Create evidence
    const evidence = new Evidence({
      controlId: 'AC-2',
      framework: 'NIST-800-53',
      source: 'GCP',
      timestamp: new Date().toISOString(),
      data: {
        value: true,
        details: 'Account management controls implemented',
        score: 95
      }
    });
    
    logger.info('Created evidence:', evidence);
    
    // Verify the evidence with NovaProof
    const verifyEvent = new Event('evidence.verify', {
      evidence,
      options: {
        blockchainType: BlockchainType.ETHEREUM,
        storeOnChain: false
      }
    });
    
    const verifyResult = await novanexxus.eventProcessor.processEvent(verifyEvent);
    const verifiedEvidence = verifyResult.data.result;
    
    logger.info('Verified evidence:', verifiedEvidence);
    
    // Enhance the evidence with CSDE
    const enhanceEvent = new Event('evidence.enhance', {
      evidence: verifiedEvidence,
      domain: CSEDDomainType.COMPLIANCE,
      options: { priority: 1 }
    });
    
    const enhanceResult = await novanexxus.eventProcessor.processEvent(enhanceEvent);
    const enhancedEvidence = enhanceResult.data.result;
    
    logger.info('Enhanced evidence:', enhancedEvidence);
    logger.info('CSDE metadata:', enhancedEvidence.metadata.csde);
    
    logger.success('Evidence verification with CSDE enhancement completed successfully');
    return enhancedEvidence;
  } catch (error) {
    logger.error('Error in evidence verification demo:', error);
    throw error;
  }
}

/**
 * Example 3: NIST compliance check across all components
 */
async function demoNISTComplianceCheck(novanexxus) {
  logger.info('\n--- Example 3: NIST Compliance Check ---');
  
  try {
    // Create NIST compliance check event
    const complianceEvent = new Event('system.compliance.check', {
      framework: 'NIST-CSF',
      timestamp: new Date().toISOString()
    });
    
    // Process the event
    const complianceResult = await novanexxus.eventProcessor.processEvent(complianceEvent);
    
    logger.info('NIST compliance check result:', complianceResult.data.result);
    
    logger.success('NIST compliance check completed successfully');
    return complianceResult.data.result;
  } catch (error) {
    logger.error('Error in NIST compliance check demo:', error);
    throw error;
  }
}

/**
 * Example 4: Get system metrics
 */
async function demoSystemMetrics(novanexxus) {
  logger.info('\n--- Example 4: System Metrics ---');
  
  try {
    // Get system metrics
    const metrics = novanexxus.getMetrics();
    
    logger.info('System metrics:', JSON.stringify(metrics, null, 2));
    
    logger.success('System metrics retrieved successfully');
    return metrics;
  } catch (error) {
    logger.error('Error in system metrics demo:', error);
    throw error;
  }
}

/**
 * Main function
 */
async function main() {
  logger.info('Starting 🪐 NovaNexxus™ Implementation Example...');
  
  let novanexxus;
  
  try {
    // Create and initialize NovaNexxus
    logger.info('Creating and initializing NovaNexxus...');
    novanexxus = await createAndInitializeFullNovaNexxus({
      enableLogging: true,
      enableMetrics: true,
      enableCaching: true
    });
    
    logger.success('NovaNexxus initialized successfully');
    
    // Run demos
    await demoTensorProcessing(novanexxus);
    await demoEvidenceVerification(novanexxus);
    await demoNISTComplianceCheck(novanexxus);
    await demoSystemMetrics(novanexxus);
    
    // Shutdown NovaNexxus
    logger.info('Shutting down NovaNexxus...');
    await novanexxus.shutdown();
    
    logger.success('NovaNexxus shut down successfully');
    logger.success('🪐 NovaNexxus™ Implementation Example completed successfully!');
  } catch (error) {
    logger.error('Error in NovaNexxus example:', error);
    
    // Attempt to shutdown NovaNexxus if it was created
    if (novanexxus) {
      try {
        await novanexxus.shutdown();
      } catch (shutdownError) {
        logger.error('Error shutting down NovaNexxus:', shutdownError);
      }
    }
  }
}

// Run the example
main();
