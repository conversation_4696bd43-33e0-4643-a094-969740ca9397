/**
 * event-processor.js
 * 
 * This file implements the event processing system for NovaCore.
 * The event processor handles events from various sources, processes them,
 * and routes them to appropriate handlers.
 */

const EventEmitter = require('events');
const { performance } = require('perf_hooks');
const crypto = require('crypto');

/**
 * Event priority enum
 * @enum {number}
 */
const EventPriority = {
  CRITICAL: 0,
  HIGH: 1,
  NORMAL: 2,
  LOW: 3,
  BACKGROUND: 4
};

/**
 * Event status enum
 * @enum {string}
 */
const EventStatus = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  DROPPED: 'DROPPED'
};

/**
 * Event class representing a single event in the system
 */
class Event {
  /**
   * Create a new event
   * @param {string} type - The event type
   * @param {Object} data - The event data
   * @param {Object} options - Event options
   */
  constructor(type, data = {}, options = {}) {
    this.id = options.id || crypto.randomUUID();
    this.type = type;
    this.data = data;
    this.timestamp = options.timestamp || new Date().toISOString();
    this.source = options.source || 'unknown';
    this.priority = options.priority !== undefined ? options.priority : EventPriority.NORMAL;
    this.status = EventStatus.PENDING;
    this.processingTime = null;
    this.error = null;
    this.metadata = options.metadata || {};
    this.correlationId = options.correlationId || null;
    this.causationId = options.causationId || null;
  }

  /**
   * Mark the event as processing
   * @returns {Event} - The event instance
   */
  markAsProcessing() {
    this.status = EventStatus.PROCESSING;
    return this;
  }

  /**
   * Mark the event as completed
   * @param {number} processingTime - The time taken to process the event in milliseconds
   * @returns {Event} - The event instance
   */
  markAsCompleted(processingTime) {
    this.status = EventStatus.COMPLETED;
    this.processingTime = processingTime;
    return this;
  }

  /**
   * Mark the event as failed
   * @param {Error} error - The error that occurred during processing
   * @param {number} processingTime - The time taken to process the event in milliseconds
   * @returns {Event} - The event instance
   */
  markAsFailed(error, processingTime) {
    this.status = EventStatus.FAILED;
    this.error = error.message;
    this.processingTime = processingTime;
    return this;
  }

  /**
   * Mark the event as dropped
   * @param {string} reason - The reason the event was dropped
   * @returns {Event} - The event instance
   */
  markAsDropped(reason) {
    this.status = EventStatus.DROPPED;
    this.error = reason;
    return this;
  }

  /**
   * Clone the event with a new ID
   * @returns {Event} - A new event with the same data but a new ID
   */
  clone() {
    return new Event(this.type, { ...this.data }, {
      source: this.source,
      priority: this.priority,
      metadata: { ...this.metadata },
      correlationId: this.correlationId,
      causationId: this.id // The new event is caused by this event
    });
  }

  /**
   * Create a response event
   * @param {string} type - The response event type
   * @param {Object} data - The response event data
   * @returns {Event} - A new response event
   */
  createResponse(type, data = {}) {
    return new Event(type, data, {
      source: 'response',
      priority: this.priority,
      metadata: {
        isResponse: true,
        originalEventType: this.type,
        ...this.metadata
      },
      correlationId: this.correlationId || this.id,
      causationId: this.id
    });
  }

  /**
   * Convert the event to a JSON-serializable object
   * @returns {Object} - A JSON-serializable representation of the event
   */
  toJSON() {
    return {
      id: this.id,
      type: this.type,
      data: this.data,
      timestamp: this.timestamp,
      source: this.source,
      priority: this.priority,
      status: this.status,
      processingTime: this.processingTime,
      error: this.error,
      metadata: this.metadata,
      correlationId: this.correlationId,
      causationId: this.causationId
    };
  }

  /**
   * Create an event from a JSON object
   * @param {Object} json - The JSON representation of an event
   * @returns {Event} - A new event created from the JSON
   */
  static fromJSON(json) {
    const event = new Event(json.type, json.data, {
      id: json.id,
      timestamp: json.timestamp,
      source: json.source,
      priority: json.priority,
      metadata: json.metadata,
      correlationId: json.correlationId,
      causationId: json.causationId
    });

    event.status = json.status;
    event.processingTime = json.processingTime;
    event.error = json.error;

    return event;
  }
}

/**
 * EventProcessor class for processing events
 * @extends EventEmitter
 */
class EventProcessor extends EventEmitter {
  /**
   * Create a new event processor
   * @param {Object} options - Event processor options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      maxConcurrentEvents: options.maxConcurrentEvents || 10,
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      defaultErrorHandler: options.defaultErrorHandler || this._defaultErrorHandler.bind(this),
      priorityProcessing: options.priorityProcessing !== undefined ? options.priorityProcessing : true,
      dropEventsWhenFull: options.dropEventsWhenFull !== undefined ? options.dropEventsWhenFull : false,
      maxQueueSize: options.maxQueueSize || 1000,
      ...options
    };
    
    this.handlers = new Map();
    this.eventQueue = [];
    this.activeEvents = 0;
    this.processedEvents = 0;
    this.failedEvents = 0;
    this.droppedEvents = 0;
    this.isProcessing = false;
    
    this.log('EventProcessor initialized with options:', this.options);
  }
  
  /**
   * Log a message if logging is enabled
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.enableLogging) {
      console.log(`[EventProcessor ${new Date().toISOString()}]`, ...args);
    }
  }
  
  /**
   * Default error handler for event processing
   * @param {Error} error - The error that occurred
   * @param {Event} event - The event that had the error
   * @private
   */
  _defaultErrorHandler(error, event) {
    this.log(`Error processing event ${event.id} of type ${event.type}:`, error);
    this.emit('error', { error, event: event.toJSON() });
  }
  
  /**
   * Register an event handler
   * @param {string} eventType - The event type to handle
   * @param {Function} handler - The handler function
   * @param {Object} options - Handler options
   * @returns {EventProcessor} - The event processor instance
   */
  registerHandler(eventType, handler, options = {}) {
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    
    this.handlers.get(eventType).push({
      handler,
      options: {
        priority: options.priority !== undefined ? options.priority : EventPriority.NORMAL,
        ...options
      }
    });
    
    this.log(`Registered handler for event type: ${eventType}`);
    return this;
  }
  
  /**
   * Unregister an event handler
   * @param {string} eventType - The event type
   * @param {Function} handler - The handler function to unregister
   * @returns {boolean} - Whether the handler was successfully unregistered
   */
  unregisterHandler(eventType, handler) {
    if (!this.handlers.has(eventType)) {
      return false;
    }
    
    const handlers = this.handlers.get(eventType);
    const index = handlers.findIndex(h => h.handler === handler);
    
    if (index === -1) {
      return false;
    }
    
    handlers.splice(index, 1);
    
    if (handlers.length === 0) {
      this.handlers.delete(eventType);
    }
    
    this.log(`Unregistered handler for event type: ${eventType}`);
    return true;
  }
  
  /**
   * Process an event
   * @param {Event|Object} event - The event to process or event data
   * @returns {Promise<Event>} - A promise that resolves to the processed event
   */
  async processEvent(event) {
    // Convert plain object to Event if necessary
    const eventObj = event instanceof Event 
      ? event 
      : new Event(event.type, event.data, event);
    
    // Check if the queue is full
    if (this.options.dropEventsWhenFull && 
        this.eventQueue.length >= this.options.maxQueueSize) {
      eventObj.markAsDropped('Queue full');
      this.droppedEvents++;
      this.emit('eventDropped', { event: eventObj.toJSON() });
      return eventObj;
    }
    
    // Add the event to the queue
    this.eventQueue.push(eventObj);
    this.emit('eventQueued', { event: eventObj.toJSON() });
    
    // Start processing if not already processing
    if (!this.isProcessing) {
      this.isProcessing = true;
      this._processQueue();
    }
    
    // Return a promise that resolves when the event is processed
    return new Promise((resolve) => {
      const onComplete = (completedEvent) => {
        if (completedEvent.id === eventObj.id) {
          this.removeListener('eventCompleted', onComplete);
          this.removeListener('eventFailed', onComplete);
          this.removeListener('eventDropped', onComplete);
          resolve(completedEvent);
        }
      };
      
      this.on('eventCompleted', onComplete);
      this.on('eventFailed', onComplete);
      this.on('eventDropped', onComplete);
    });
  }
  
  /**
   * Process the event queue
   * @private
   */
  _processQueue() {
    if (this.eventQueue.length === 0) {
      this.isProcessing = false;
      return;
    }
    
    if (this.activeEvents >= this.options.maxConcurrentEvents) {
      return;
    }
    
    // Sort the queue by priority if enabled
    if (this.options.priorityProcessing) {
      this.eventQueue.sort((a, b) => a.priority - b.priority);
    }
    
    const event = this.eventQueue.shift();
    this.activeEvents++;
    
    this._processEvent(event)
      .then(() => {
        this.activeEvents--;
        this._processQueue();
      })
      .catch((error) => {
        this.log('Unexpected error in _processEvent:', error);
        this.activeEvents--;
        this._processQueue();
      });
  }
  
  /**
   * Process a single event
   * @param {Event} event - The event to process
   * @returns {Promise<void>} - A promise that resolves when the event is processed
   * @private
   */
  async _processEvent(event) {
    const startTime = performance.now();
    event.markAsProcessing();
    
    this.emit('eventProcessing', { event: event.toJSON() });
    
    try {
      // Get handlers for this event type
      const handlers = this.handlers.get(event.type) || [];
      
      // If no handlers, mark as completed
      if (handlers.length === 0) {
        const processingTime = performance.now() - startTime;
        event.markAsCompleted(processingTime);
        this.processedEvents++;
        this.emit('eventCompleted', event);
        return;
      }
      
      // Execute all handlers
      const promises = handlers.map(({ handler }) => {
        return Promise.resolve().then(() => handler(event));
      });
      
      await Promise.all(promises);
      
      const processingTime = performance.now() - startTime;
      event.markAsCompleted(processingTime);
      this.processedEvents++;
      this.emit('eventCompleted', event);
    } catch (error) {
      const processingTime = performance.now() - startTime;
      event.markAsFailed(error, processingTime);
      this.failedEvents++;
      
      if (this.options.defaultErrorHandler) {
        this.options.defaultErrorHandler(error, event);
      }
      
      this.emit('eventFailed', { event: event.toJSON(), error });
    }
  }
  
  /**
   * Get the processor state
   * @returns {Object} - The current state of the event processor
   */
  getState() {
    return {
      queueLength: this.eventQueue.length,
      activeEvents: this.activeEvents,
      processedEvents: this.processedEvents,
      failedEvents: this.failedEvents,
      droppedEvents: this.droppedEvents,
      isProcessing: this.isProcessing,
      registeredEventTypes: Array.from(this.handlers.keys())
    };
  }
  
  /**
   * Clear the event queue
   * @returns {EventProcessor} - The event processor instance
   */
  clearQueue() {
    const droppedCount = this.eventQueue.length;
    
    this.eventQueue.forEach(event => {
      event.markAsDropped('Queue cleared');
      this.emit('eventDropped', { event: event.toJSON() });
    });
    
    this.eventQueue = [];
    this.droppedEvents += droppedCount;
    
    this.log(`Cleared event queue, dropped ${droppedCount} events`);
    return this;
  }
}

module.exports = {
  EventProcessor,
  Event,
  EventPriority,
  EventStatus
};
