/**
 * Test Role Inheritance
 *
 * This script tests the role inheritance functionality.
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Create a mock cache service
const mockCacheService = `
const NodeCache = require('node-cache');

class CacheService {
  constructor() {
    this.cache = new NodeCache({
      stdTTL: 300,
      checkperiod: 60
    });
  }

  async get(key) {
    return this.cache.get(key) || null;
  }

  async set(key, value, ttl = 300) {
    return this.cache.set(key, value, ttl);
  }

  async delete(key) {
    return this.cache.del(key) > 0;
  }

  async keys(pattern) {
    const allKeys = this.cache.keys();
    const regex = new RegExp(pattern.replace('*', '.*'));
    return allKeys.filter(key => regex.test(key));
  }

  async flush(prefix) {
    const keys = this.cache.keys();
    const keysToDelete = keys.filter(key => key.startsWith(prefix));
    this.cache.del(keysToDelete);
    return true;
  }
}

module.exports = new CacheService();
`;

// Mock the cache service
const cacheServicePath = path.join(__dirname, '../api/services/CacheService.js');
const originalCacheService = fs.readFileSync(cacheServicePath, 'utf8');

// Write the mock cache service
fs.writeFileSync(cacheServicePath, mockCacheService, 'utf8');

// Now import the modules
const Role = require('../api/models/Role');
const Permission = require('../api/models/Permission');
const UserRole = require('../api/models/UserRole');
const User = require('../api/models/User');
const logger = require('../config/logger');
const RBACService = require('../api/services/RBACService');

// Silence the logger during tests
logger.level = 'silent';

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/novaconnect-test';

// Initialize RBAC service
const rbacService = new RBACService();

/**
 * Connect to the database
 */
async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    return false;
  }
}

/**
 * Disconnect from the database
 */
async function disconnectFromDatabase() {
  try {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error disconnecting from MongoDB:', error);
  }
}

/**
 * Run tests
 */
async function runTests() {
  try {
    console.log('Running role inheritance tests...');

    // Test 1: Create test permissions
    console.log('\nTest 1: Creating test permissions...');

    // Create or get view permission
    let viewPermission = await Permission.findOne({ resource: 'test-resource', action: 'view' });
    if (!viewPermission) {
      viewPermission = await rbacService.createPermission({
        name: 'View Test Resource',
        description: 'Permission to view test resources',
        resource: 'test-resource',
        action: 'view'
      });
    }

    // Create or get edit permission
    let editPermission = await Permission.findOne({ resource: 'test-resource', action: 'edit' });
    if (!editPermission) {
      editPermission = await rbacService.createPermission({
        name: 'Edit Test Resource',
        description: 'Permission to edit test resources',
        resource: 'test-resource',
        action: 'edit'
      });
    }

    // Create or get delete permission
    let deletePermission = await Permission.findOne({ resource: 'test-resource', action: 'delete' });
    if (!deletePermission) {
      deletePermission = await rbacService.createPermission({
        name: 'Delete Test Resource',
        description: 'Permission to delete test resources',
        resource: 'test-resource',
        action: 'delete'
      });
    }

    console.log('Test permissions created successfully');

    // Test 2: Create roles with inheritance
    console.log('\nTest 2: Creating roles with inheritance...');

    // Create or get viewer role
    let viewerRole = await Role.findOne({ name: 'Test Viewer' });
    if (!viewerRole) {
      viewerRole = await rbacService.createRole({
        name: 'Test Viewer',
        description: 'Role with view permission',
        permissions: [viewPermission._id]
      });
    }

    // Create or get editor role that inherits from viewer
    let editorRole = await Role.findOne({ name: 'Test Editor' });
    if (!editorRole) {
      editorRole = await rbacService.createRole({
        name: 'Test Editor',
        description: 'Role with edit permission, inherits view from viewer',
        permissions: [editPermission._id],
        inheritsFrom: [viewerRole._id]
      });
    } else {
      // Update to ensure it inherits from viewer
      editorRole = await rbacService.updateRole(editorRole._id, {
        inheritsFrom: [viewerRole._id]
      });
    }

    // Create or get admin role that inherits from editor
    let adminRole = await Role.findOne({ name: 'Test Admin' });
    if (!adminRole) {
      adminRole = await rbacService.createRole({
        name: 'Test Admin',
        description: 'Role with delete permission, inherits view and edit from editor',
        permissions: [deletePermission._id],
        inheritsFrom: [editorRole._id]
      });
    } else {
      // Update to ensure it inherits from editor
      adminRole = await rbacService.updateRole(adminRole._id, {
        inheritsFrom: [editorRole._id]
      });
    }

    console.log('Roles with inheritance created successfully');

    // Test 3: Create test users
    console.log('\nTest 3: Creating test users...');

    // Create or get viewer user
    let viewerUser = await User.findOne({ email: '<EMAIL>' });
    if (!viewerUser) {
      viewerUser = new User({
        username: 'viewer-test',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Viewer',
        lastName: 'Test',
        status: 'active'
      });
      await viewerUser.save();
    }

    // Create or get editor user
    let editorUser = await User.findOne({ email: '<EMAIL>' });
    if (!editorUser) {
      editorUser = new User({
        username: 'editor-test',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Editor',
        lastName: 'Test',
        status: 'active'
      });
      await editorUser.save();
    }

    // Create or get admin user
    let adminUser = await User.findOne({ email: '<EMAIL>' });
    if (!adminUser) {
      adminUser = new User({
        username: 'admin-test',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Admin',
        lastName: 'Test',
        status: 'active'
      });
      await adminUser.save();
    }

    console.log('Test users created successfully');

    // Test 4: Assign roles to users
    console.log('\nTest 4: Assigning roles to users...');

    // Remove existing roles
    await UserRole.deleteMany({
      user: { $in: [viewerUser._id, editorUser._id, adminUser._id] }
    });

    // Assign viewer role to viewer user
    await rbacService.assignRoleToUser(viewerUser._id, viewerRole._id);

    // Assign editor role to editor user
    await rbacService.assignRoleToUser(editorUser._id, editorRole._id);

    // Assign admin role to admin user
    await rbacService.assignRoleToUser(adminUser._id, adminRole._id);

    console.log('Roles assigned to users successfully');

    // Test 5: Check permissions with inheritance
    console.log('\nTest 5: Checking permissions with inheritance...');

    // Check viewer user permissions
    const viewerViewPermission = await rbacService.hasPermission(viewerUser._id, 'test-resource:view');
    const viewerEditPermission = await rbacService.hasPermission(viewerUser._id, 'test-resource:edit');
    const viewerDeletePermission = await rbacService.hasPermission(viewerUser._id, 'test-resource:delete');

    console.log('Viewer user permissions:');
    console.log(`- View: ${viewerViewPermission}`);
    console.log(`- Edit: ${viewerEditPermission}`);
    console.log(`- Delete: ${viewerDeletePermission}`);

    // Check editor user permissions
    const editorViewPermission = await rbacService.hasPermission(editorUser._id, 'test-resource:view');
    const editorEditPermission = await rbacService.hasPermission(editorUser._id, 'test-resource:edit');
    const editorDeletePermission = await rbacService.hasPermission(editorUser._id, 'test-resource:delete');

    console.log('Editor user permissions:');
    console.log(`- View: ${editorViewPermission}`);
    console.log(`- Edit: ${editorEditPermission}`);
    console.log(`- Delete: ${editorDeletePermission}`);

    // Check admin user permissions
    const adminViewPermission = await rbacService.hasPermission(adminUser._id, 'test-resource:view');
    const adminEditPermission = await rbacService.hasPermission(adminUser._id, 'test-resource:edit');
    const adminDeletePermission = await rbacService.hasPermission(adminUser._id, 'test-resource:delete');

    console.log('Admin user permissions:');
    console.log(`- View: ${adminViewPermission}`);
    console.log(`- Edit: ${adminEditPermission}`);
    console.log(`- Delete: ${adminDeletePermission}`);

    // Test 6: Get role permissions
    console.log('\nTest 6: Getting role permissions...');

    const viewerRolePermissions = await rbacService.getRolePermissions(viewerRole._id);
    const editorRolePermissions = await rbacService.getRolePermissions(editorRole._id);
    const adminRolePermissions = await rbacService.getRolePermissions(adminRole._id);

    console.log('Viewer role permissions:', viewerRolePermissions);
    console.log('Editor role permissions:', editorRolePermissions);
    console.log('Admin role permissions:', adminRolePermissions);

    console.log('\nRole inheritance tests completed successfully');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Main function
async function main() {
  const connected = await connectToDatabase();

  try {
    if (connected) {
      await runTests();
    }
  } finally {
    // Restore the original cache service
    fs.writeFileSync(cacheServicePath, originalCacheService, 'utf8');
    console.log('Restored original cache service');

    await disconnectFromDatabase();
  }
}

// Run the main function
main();
