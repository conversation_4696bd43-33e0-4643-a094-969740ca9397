/**
 * Enhanced CSDE and CSFE Example
 * 
 * This example demonstrates the enhanced CSDE and CSFE components working together
 * to provide a unified cyber-safety system across domains.
 */

// Import enhanced CSDE components
const {
  EntropicGRCControlSystem,
  HumanSystemCoherenceInterface,
  CrossDomainEntropyBridge,
  createEnhancedCSDESystem
} = require('../src/csde');

// Import enhanced CSFE components
const {
  FinancialEntropyInterpreter,
  CSFEMeter,
  CSFEGovernor,
  PsiRevertFinancial,
  createEnhancedCSFESystem
} = require('../src/csfe');

// Import CSME components
const {
  CSMEEngine,
  BioEntropicTensor,
  EnvironmentalContextProcessor,
  PsiRevertProtocolEngine,
  createCSMESystem
} = require('../src/csme');

/**
 * Run the enhanced CSDE example
 */
function runEnhancedCSDEExample() {
  console.log('\n=== Enhanced CSDE Example ===');
  
  try {
    // Create enhanced CSDE system
    console.log('Creating enhanced CSDE system...');
    const csdeSystem = createEnhancedCSDESystem({
      entropicGRCControlSystemOptions: {
        enableLogging: true
      },
      humanSystemCoherenceInterfaceOptions: {
        enableLogging: true
      },
      crossDomainEntropyBridgeOptions: {
        enableLogging: true
      }
    });
    
    // Start components
    csdeSystem.entropicGRCControlSystem.start();
    csdeSystem.humanSystemCoherenceInterface.start();
    csdeSystem.crossDomainEntropyBridge.start();
    
    // Process policy data
    console.log('\nProcessing policy data...');
    const policyData = {
      policyCount: 100,
      policyChanges: [
        { id: 'change-1', policyId: 'policy-1', changeType: 'update' },
        { id: 'change-2', policyId: 'policy-2', changeType: 'add' }
      ],
      policyConflicts: [
        { id: 'conflict-1', policies: ['policy-1', 'policy-3'] }
      ],
      policyGaps: [
        { id: 'gap-1', description: 'Missing policy for new regulation' }
      ],
      policyImplementation: {
        implemented: 80,
        pending: 15,
        failed: 5
      }
    };
    
    const policyEntropy = csdeSystem.entropicGRCControlSystem.calculatePolicyEntropy(policyData);
    console.log(`Policy Entropy (Ψ_gov): ${policyEntropy.toFixed(4)}`);
    
    // Process human data
    console.log('\nProcessing human data...');
    const humanData = {
      cognitiveLoad: 0.3,
      stressLevel: 0.2,
      fatigue: 0.3,
      attention: 0.8,
      expertise: 0.7
    };
    
    const humanCoherenceIndex = csdeSystem.humanSystemCoherenceInterface.updateHumanCoherenceIndex(humanData);
    console.log(`Human Coherence Index (Ψₕ): ${humanCoherenceIndex.toFixed(4)}`);
    
    // Queue alert
    console.log('\nQueuing alert...');
    csdeSystem.humanSystemCoherenceInterface.queueAlert({
      message: 'Policy compliance threshold breached',
      priority: 'high',
      source: 'EntropicGRCControlSystem'
    });
    
    // Update cross-domain entropy bridge
    console.log('\nUpdating cross-domain entropy bridge...');
    csdeSystem.crossDomainEntropyBridge.updateCyberEntropy(0.7);
    
    // Get unified risk score
    const unifiedRiskScore = csdeSystem.crossDomainEntropyBridge.getUnifiedRiskScore();
    console.log(`Unified Risk Score: ${unifiedRiskScore.toFixed(4)}`);
    
    // Stop components
    csdeSystem.entropicGRCControlSystem.stop();
    csdeSystem.humanSystemCoherenceInterface.stop();
    csdeSystem.crossDomainEntropyBridge.stop();
    
    console.log('\nEnhanced CSDE example completed successfully.');
  } catch (error) {
    console.error('Error in enhanced CSDE example:', error);
  }
}

/**
 * Run the enhanced CSFE example
 */
function runEnhancedCSFEExample() {
  console.log('\n=== Enhanced CSFE Example ===');
  
  try {
    // Create enhanced CSFE system
    console.log('Creating enhanced CSFE system...');
    const csfeSystem = createEnhancedCSFESystem({
      financialEntropyInterpreterOptions: {
        enableLogging: true
      },
      csfeMeterOptions: {
        enableLogging: true
      },
      csfeGovernorOptions: {
        enableLogging: true
      },
      psiRevertFinancialOptions: {
        enableLogging: true
      }
    });
    
    // Start components
    csfeSystem.csfeController.start();
    
    // Process financial data
    console.log('\nProcessing financial data...');
    const financialData = {
      transactionData: {
        transactions: [
          { id: 'tx-1', volume: 500, frequency: 0.8, anomalyScore: 0.1 },
          { id: 'tx-2', volume: 1000, frequency: 0.5, anomalyScore: 0.3 }
        ],
        liquidityGaps: [0.1, 0.2, 0.15, 0.1],
        velocity: [0.5, 0.52, 0.48, 0.51]
      },
      securityData: {
        vulnerabilities: [
          { id: 'vuln-1', severity: 0.7 },
          { id: 'vuln-2', severity: 0.3 }
        ],
        patchStatus: [{ age: 15 }],
        threatIntel: [{ relevance: 0.6 }],
        keyLength: 2048,
        algoAge: 3,
        quantumThreatLevel: 0.3
      },
      marketData: {
        volatility: [0.1, 0.15, 0.12],
        correlations: [0.7, 0.65, 0.8],
        liquidity: [0.9, 0.85, 0.95]
      },
      networkData: {
        resilience: 0.8
      }
    };
    
    const result = csfeSystem.csfeController.processFinancialData(financialData);
    console.log(`Financial Entropy (Ψₜᶠ): ${result.financialEntropy.toFixed(4)}`);
    console.log(`Entropy Gradient: ${result.entropyGradient.toFixed(4)}`);
    
    // Get available protocols
    console.log('\nAvailable protocols:');
    const protocols = csfeSystem.csfeController.getAvailableProtocols();
    Object.keys(protocols).forEach(protocolId => {
      console.log(`- ${protocolId}: ${protocols[protocolId].name}`);
    });
    
    // Execute protocol
    console.log('\nExecuting protocol...');
    const executionResult = csfeSystem.csfeController.executeProtocol('ΨRF-3', {
      threatIndicators: ['indicator-1'],
      partnerInstitutions: ['partner-1', 'partner-2'],
      sharingAgreements: ['agreement-1']
    });
    
    console.log(`Protocol execution: ${executionResult.success ? 'Success' : 'Failed'}`);
    
    // Stop components
    csfeSystem.csfeController.stop();
    
    console.log('\nEnhanced CSFE example completed successfully.');
  } catch (error) {
    console.error('Error in enhanced CSFE example:', error);
  }
}

/**
 * Run the cross-domain integration example
 */
function runCrossDomainIntegrationExample() {
  console.log('\n=== Cross-Domain Integration Example ===');
  
  try {
    // Create systems
    console.log('Creating systems...');
    const csdeSystem = createEnhancedCSDESystem();
    const csfeSystem = createEnhancedCSFESystem();
    const csmeSystem = createCSMESystem();
    
    // Start cross-domain entropy bridge
    csdeSystem.crossDomainEntropyBridge.start();
    
    // Process data from each domain
    console.log('\nProcessing data from each domain...');
    
    // Process cyber data
    const cyberEntropy = 0.7;
    csdeSystem.crossDomainEntropyBridge.updateCyberEntropy(cyberEntropy);
    console.log(`Cyber Entropy (Ψₜᵈ): ${cyberEntropy.toFixed(4)}`);
    
    // Process financial data
    const financialData = {
      transactionData: { transactions: [] },
      securityData: { vulnerabilities: [] },
      marketData: { volatility: [] },
      networkData: { resilience: 0.8 }
    };
    
    const financialResult = csfeSystem.financialEntropyInterpreter.processFinancialData(financialData);
    const financialEntropy = financialResult.coherence;
    csdeSystem.crossDomainEntropyBridge.updateFinancialEntropy(financialEntropy);
    console.log(`Financial Entropy (Ψₜᶠ): ${financialEntropy.toFixed(4)}`);
    
    // Process biological data
    const biologicalData = {
      genomicData: {},
      proteomicData: {},
      clinicalData: {},
      environmentalData: {}
    };
    
    const biologicalResult = csmeSystem.bioEntropicTensor.processBiologicalData(biologicalData);
    const biologicalEntropy = biologicalResult.coherence;
    csdeSystem.crossDomainEntropyBridge.updateBiologicalEntropy(biologicalEntropy);
    console.log(`Biological Entropy (Ψₜ): ${biologicalEntropy.toFixed(4)}`);
    
    // Get unified risk score
    const unifiedRiskScore = csdeSystem.crossDomainEntropyBridge.getUnifiedRiskScore();
    console.log(`Unified Risk Score: ${unifiedRiskScore.toFixed(4)}`);
    
    // Stop components
    csdeSystem.crossDomainEntropyBridge.stop();
    
    console.log('\nCross-domain integration example completed successfully.');
  } catch (error) {
    console.error('Error in cross-domain integration example:', error);
  }
}

/**
 * Run all examples
 */
function runAllExamples() {
  console.log('=== Running Enhanced CSDE and CSFE Examples ===');
  
  runEnhancedCSDEExample();
  runEnhancedCSFEExample();
  runCrossDomainIntegrationExample();
  
  console.log('\nAll examples completed.');
}

// Run examples
runAllExamples();
