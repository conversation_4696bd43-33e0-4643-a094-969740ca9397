const express = require("express");
const cors = require("cors");
const morgan = require("morgan");

const app = express();
const port = 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(morgan("dev"));

// Create router with prefix
const router = express.Router();
app.use('/governance', router);

// Direct health check route (no prefix)
app.get("/health", (req, res) => {
  res.json({ status: "healthy", timestamp: new Date().toISOString() });
});

// Sample data
const boardMeetings = [
  {
    id: "bm-001",
    title: "Q1 Board Meeting",
    date: "2025-01-15",
    status: "scheduled",
    agenda: [
      "Financial review",
      "Strategic initiatives",
      "Compliance updates"
    ],
    participants: [
      { id: "p-001", name: "<PERSON>", role: "Chairman" },
      { id: "p-002", name: "<PERSON>", role: "CEO" },
      { id: "p-003", name: "<PERSON>", role: "CFO" }
    ]
  },
  {
    id: "bm-002",
    title: "Q2 Board Meeting",
    date: "2025-04-15",
    status: "scheduled",
    agenda: [
      "Q1 performance review",
      "Risk assessment",
      "Audit committee report"
    ],
    participants: [
      { id: "p-001", name: "<PERSON> <PERSON>", role: "Chairman" },
      { id: "p-002", name: "Jane Doe", role: "CEO" },
      { id: "p-003", name: "<PERSON> <PERSON>", role: "CFO" }
    ]
  },
  {
    id: "bm-003",
    title: "Emergency Board Meeting",
    date: "2024-12-10",
    status: "completed",
    agenda: [
      "Security incident response",
      "Crisis management",
      "Communication strategy"
    ],
    participants: [
      { id: "p-001", name: "John Smith", role: "Chairman" },
      { id: "p-002", name: "Jane Doe", role: "CEO" },
      { id: "p-004", name: "Alice Williams", role: "CISO" }
    ]
  }
];

const policies = [
  {
    id: "pol-001",
    title: "Data Privacy Policy",
    category: "privacy",
    status: "active",
    version: "2.1",
    lastUpdated: "2024-09-15",
    approvedBy: "John Smith",
    content: "This policy outlines how the organization handles personal data..."
  },
  {
    id: "pol-002",
    title: "Code of Conduct",
    category: "ethics",
    status: "active",
    version: "1.5",
    lastUpdated: "2024-08-20",
    approvedBy: "Jane Doe",
    content: "This code of conduct establishes the ethical standards..."
  },
  {
    id: "pol-003",
    title: "Remote Work Policy",
    category: "hr",
    status: "draft",
    version: "0.9",
    lastUpdated: "2024-10-05",
    approvedBy: null,
    content: "This policy defines the guidelines for remote work..."
  }
];

const complianceReports = [
  {
    id: "cr-001",
    title: "GDPR Compliance Report",
    framework: "gdpr",
    period: "Q3 2024",
    status: "completed",
    completionDate: "2024-10-10",
    findings: [
      { id: "f-001", severity: "medium", description: "Incomplete data processing records" },
      { id: "f-002", severity: "low", description: "Privacy notice needs updating" }
    ]
  },
  {
    id: "cr-002",
    title: "SOC 2 Readiness Assessment",
    framework: "soc2",
    period: "Q4 2024",
    status: "in-progress",
    completionDate: null,
    findings: []
  },
  {
    id: "cr-003",
    title: "HIPAA Compliance Audit",
    framework: "hipaa",
    period: "Q2 2024",
    status: "completed",
    completionDate: "2024-07-15",
    findings: [
      { id: "f-003", severity: "high", description: "Insufficient access controls" },
      { id: "f-004", severity: "medium", description: "Incomplete risk assessment" },
      { id: "f-005", severity: "low", description: "Training documentation gaps" }
    ]
  }
];

const boardResolutions = [
  {
    id: "res-001",
    title: "Approval of Q3 Financial Statements",
    meetingId: "bm-001",
    status: "approved",
    approvalDate: "2024-10-15",
    votes: { for: 7, against: 0, abstain: 1 }
  },
  {
    id: "res-002",
    title: "Appointment of New Board Member",
    meetingId: "bm-001",
    status: "approved",
    approvalDate: "2024-10-15",
    votes: { for: 6, against: 1, abstain: 1 }
  },
  {
    id: "res-003",
    title: "Cybersecurity Investment Proposal",
    meetingId: "bm-003",
    status: "pending",
    approvalDate: null,
    votes: null
  }
];

// Routes

// Board meetings
router.get("/board/meetings", (req, res) => {
  let result = [...boardMeetings];

  // Apply filters
  if (req.query.status) {
    result = result.filter(m => m.status === req.query.status);
  }

  if (req.query.start_date) {
    result = result.filter(m => m.date >= req.query.start_date);
  }

  if (req.query.end_date) {
    result = result.filter(m => m.date <= req.query.end_date);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/board/meetings/:id", (req, res) => {
  const meeting = boardMeetings.find(m => m.id === req.params.id);

  if (!meeting) {
    return res.status(404).json({ error: "Meeting not found" });
  }

  res.json({ data: meeting });
});

// Policies
router.get("/policies", (req, res) => {
  let result = [...policies];

  // Apply filters
  if (req.query.category) {
    result = result.filter(p => p.category === req.query.category);
  }

  if (req.query.status) {
    result = result.filter(p => p.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/policies/:id", (req, res) => {
  const policy = policies.find(p => p.id === req.params.id);

  if (!policy) {
    return res.status(404).json({ error: "Policy not found" });
  }

  res.json({ data: policy });
});

// Compliance reports
router.get("/compliance/reports", (req, res) => {
  let result = [...complianceReports];

  // Apply filters
  if (req.query.framework) {
    result = result.filter(r => r.framework === req.query.framework);
  }

  if (req.query.period) {
    result = result.filter(r => r.period === req.query.period);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/compliance/reports/:id", (req, res) => {
  const report = complianceReports.find(r => r.id === req.params.id);

  if (!report) {
    return res.status(404).json({ error: "Report not found" });
  }

  res.json({ data: report });
});

// Board resolutions
router.get("/board/resolutions", (req, res) => {
  let result = [...boardResolutions];

  // Apply filters
  if (req.query.meeting_id) {
    result = result.filter(r => r.meetingId === req.query.meeting_id);
  }

  if (req.query.status) {
    result = result.filter(r => r.status === req.query.status);
  }

  res.json({
    data: result,
    total: result.length,
    page: 1,
    limit: result.length
  });
});

router.get("/board/resolutions/:id", (req, res) => {
  const resolution = boardResolutions.find(r => r.id === req.params.id);

  if (!resolution) {
    return res.status(404).json({ error: "Resolution not found" });
  }

  res.json({ data: resolution });
});

// Start server
app.listen(port, () => {
  console.log(`Governance mock API running on port ${port}`);
});
