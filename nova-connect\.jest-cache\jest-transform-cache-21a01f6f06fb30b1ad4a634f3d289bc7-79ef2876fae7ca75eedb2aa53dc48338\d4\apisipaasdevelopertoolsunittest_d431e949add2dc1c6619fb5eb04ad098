8a0686840600fed584ab1832b6884c09
// Mock axios
_getJestObj().mock('axios');

// Mock logger
_getJestObj().mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Unit tests for the APIs, iPaaS & Developer Tools Connector
 */

const axios = require('axios');
const ApisIpaasDeveloperToolsConnector = require('../../../../connector/implementations/apis-ipaas-developer-tools');
describe('ApisIpaasDeveloperToolsConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    mockCredentials = {
      apiKey: 'test-api-key',
      apiKeyHeader: 'X-Test-API-Key'
    };

    // Create connector instance
    connector = new ApisIpaasDeveloperToolsConnector(mockConfig, mockCredentials);
  });
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
      expect(connector.apiKeyHeader).toBe(mockCredentials.apiKeyHeader);
    });
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new ApisIpaasDeveloperToolsConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
    it('should use default apiKeyHeader if not provided', () => {
      const connectorWithDefaultHeader = new ApisIpaasDeveloperToolsConnector(mockConfig, {
        apiKey: 'test-api-key'
      });
      expect(connectorWithDefaultHeader.apiKeyHeader).toBe('X-API-Key');
    });
  });
  describe('initialize', () => {
    it('should initialize successfully with valid credentials', async () => {
      await expect(connector.initialize()).resolves.not.toThrow();
    });
    it('should throw an error if apiKey is not provided', async () => {
      connector.credentials.apiKey = undefined;
      await expect(connector.initialize()).rejects.toThrow('API Key is required');
    });
  });
  describe('getAuthHeaders', () => {
    it('should return headers with the API key', () => {
      const headers = connector.getAuthHeaders();
      expect(headers).toEqual({
        'X-Test-API-Key': 'test-api-key'
      });
    });
  });
  describe('listApis', () => {
    it('should make a GET request to the APIs endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'api-1',
            name: 'API 1'
          }, {
            id: 'api-2',
            name: 'API 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'active',
        type: 'rest'
      };
      const result = await connector.listApis(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/apis`, {
        params,
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if the request fails', async () => {
      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      await expect(connector.listApis()).rejects.toThrow(`Error listing APIs: ${errorMessage}`);
    });
  });
  describe('getApi', () => {
    it('should make a GET request to the specific API endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'api-123',
          name: 'Test API',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const apiId = 'api-123';
      const result = await connector.getApi(apiId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/apis/${apiId}`, {
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if apiId is not provided', async () => {
      await expect(connector.getApi()).rejects.toThrow('API ID is required');
    });
  });
  describe('createApi', () => {
    it('should make a POST request to the APIs endpoint', async () => {
      // Mock axios post response
      const mockResponse = {
        data: {
          id: 'api-new',
          name: 'New API',
          type: 'rest',
          baseUrl: 'https://api.example.com/new'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      const apiData = {
        name: 'New API',
        type: 'rest',
        baseUrl: 'https://api.example.com/new'
      };
      const result = await connector.createApi(apiData);
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/apis`, apiData, {
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if required fields are missing', async () => {
      const invalidData = {
        name: 'New API'
        // Missing required fields: type, baseUrl
      };
      await expect(connector.createApi(invalidData)).rejects.toThrow('type is required');
    });
  });
  describe('listIntegrations', () => {
    it('should make a GET request to the integrations endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'integration-1',
            name: 'Integration 1'
          }, {
            id: 'integration-2',
            name: 'Integration 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        status: 'active'
      };
      const result = await connector.listIntegrations(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/integrations`, {
        params,
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
  });
  describe('getIntegration', () => {
    it('should make a GET request to the specific integration endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'integration-123',
          name: 'Test Integration',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const integrationId = 'integration-123';
      const result = await connector.getIntegration(integrationId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/integrations/${integrationId}`, {
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if integrationId is not provided', async () => {
      await expect(connector.getIntegration()).rejects.toThrow('Integration ID is required');
    });
  });
  describe('executeIntegration', () => {
    it('should make a POST request to execute the integration', async () => {
      // Mock axios post response
      const mockResponse = {
        data: {
          executionId: 'exec-123',
          status: 'queued'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      const integrationId = 'integration-123';
      const options = {
        parameters: {
          startDate: '2023-06-01',
          endDate: '2023-06-02'
        },
        async: true
      };
      const result = await connector.executeIntegration(integrationId, options);
      expect(axios.post).toHaveBeenCalledWith(`${mockConfig.baseUrl}/integrations/${integrationId}/execute`, options, {
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if integrationId is not provided', async () => {
      await expect(connector.executeIntegration()).rejects.toThrow('Integration ID is required');
    });
  });
  describe('listDeveloperTools', () => {
    it('should make a GET request to the developer tools endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [{
            id: 'tool-1',
            name: 'Tool 1'
          }, {
            id: 'tool-2',
            name: 'Tool 2'
          }],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const params = {
        category: 'testing'
      };
      const result = await connector.listDeveloperTools(params);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/developer-tools`, {
        params,
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
  });
  describe('getDeveloperTool', () => {
    it('should make a GET request to the specific developer tool endpoint', async () => {
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'tool-123',
          name: 'Test Tool',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      const toolId = 'tool-123';
      const result = await connector.getDeveloperTool(toolId);
      expect(axios.get).toHaveBeenCalledWith(`${mockConfig.baseUrl}/developer-tools/${toolId}`, {
        headers: {
          'X-Test-API-Key': 'test-api-key',
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      expect(result).toEqual(mockResponse.data);
    });
    it('should throw an error if toolId is not provided', async () => {
      await expect(connector.getDeveloperTool()).rejects.toThrow('Tool ID is required');
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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