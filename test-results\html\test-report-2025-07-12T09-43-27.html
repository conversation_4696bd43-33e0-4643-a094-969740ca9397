
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NovaFuse Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #0A84FF;
      color: white;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      margin-top: 0;
    }
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    .summary-item {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      flex: 1;
      margin-right: 10px;
      text-align: center;
    }
    .summary-item:last-child {
      margin-right: 0;
    }
    .summary-item.passed {
      background-color: #d4edda;
      color: #155724;
    }
    .summary-item.failed {
      background-color: #f8d7da;
      color: #721c24;
    }
    .summary-item.pending {
      background-color: #fff3cd;
      color: #856404;
    }
    .summary-item.total {
      background-color: #e2e3e5;
      color: #383d41;
    }
    .summary-number {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .test-suite {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .test-suite-header {
      padding: 10px 15px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .test-suite-title {
      margin: 0;
      font-size: 18px;
    }
    .test-suite-status {
      font-weight: bold;
    }
    .test-suite-status.passed {
      color: #28a745;
    }
    .test-suite-status.failed {
      color: #dc3545;
    }
    .test-suite-body {
      padding: 15px;
    }
    .test-case {
      padding: 8px 15px;
      border-bottom: 1px solid #eee;
    }
    .test-case:last-child {
      border-bottom: none;
    }
    .test-case.passed {
      border-left: 4px solid #28a745;
    }
    .test-case.failed {
      border-left: 4px solid #dc3545;
      background-color: #f8f9fa;
    }
    .test-case.pending {
      border-left: 4px solid #ffc107;
      color: #6c757d;
    }
    .test-case-title {
      margin: 0;
      font-size: 16px;
    }
    .test-case-error {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8d7da;
      border-radius: 3px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    .test-duration {
      color: #6c757d;
      font-size: 14px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #6c757d;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <header>
    <h1>NovaFuse Test Report</h1>
    <p>Generated on 7/12/2025, 4:43:27 AM</p>
  </header>
  
  <div class="summary">
    <div class="summary-item passed">
      <h3>Passed</h3>
      <div class="summary-number">689</div>
      <div>90%</div>
    </div>
    <div class="summary-item failed">
      <h3>Failed</h3>
      <div class="summary-number">70</div>
      <div>9%</div>
    </div>
    <div class="summary-item pending">
      <h3>Pending</h3>
      <div class="summary-number">3</div>
      <div>0%</div>
    </div>
    <div class="summary-item total">
      <h3>Total</h3>
      <div class="summary-number">762</div>
      <div>100%</div>
    </div>
  </div>
  
  <h2>Test Suites</h2>
  
  
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">integrationController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all integrations</h4>
        <div class="test-duration">Duration: 11ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return an integration by ID</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new integration</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ConflictError when integration already exists</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update an integration</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should not allow changing the ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete an integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should configure an integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should execute a data-export action</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should execute a data-deletion action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should execute a data-update action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should execute a notifications action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should execute a default action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError when action is not supported</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError when integration is not active</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check integration health and return healthy status</h4>
        <div class="test-duration">Duration: 51ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check integration health and return unhealthy status</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return integration logs with pagination</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default pagination values if not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return integration metrics</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default period if not provided</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">dataBreachService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all data breaches with pagination</h4>
        <div class="test-duration">Duration: 10ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default options if not provided</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 32ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a data breach by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the data breach is not found</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new data breach</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update a data breach</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update status-related dates when status changes to contained</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update status-related dates when status changes to remediated</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update status-related dates when status changes to resolved</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update notification-related dates when supervisory authority notification status changes</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update notification-related dates when data subjects notification status changes</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the data breach is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete a data breach</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the data breach is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should send notifications for a data breach</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle data subject notifications</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw ValidationError if notification data is incomplete</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the data breach is not found</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a report for a data breach</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the data breach is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should assess notification requirements for a data breach</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the data breach is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">consentController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all consent records with pagination</h4>
        <div class="test-duration">Duration: 21ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default pagination values if not provided</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle filtering by status</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle filtering by consentType</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle filtering by dataSubjectId</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle filtering by email</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle filtering by search term</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle custom sorting</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle descending sort order</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default sort order when sortOrder is not specified</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default sort when sortBy is not specified</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a consent record by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 404 if the consent record is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new consent record</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update a consent record</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 404 if the consent record is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should withdraw consent</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 404 if the consent record is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return validation error if consent is already withdrawn</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify a valid consent record</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify a valid consent record with no expiry date</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify an invalid consent record</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 404 if the consent record is not found</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get consent records by data subject ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get consent records by email</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a consent form</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return validation error if consent type is not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify a consent proof</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return validation error if consent proof is not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a consent proof</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return validation error if consent ID is not provided</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return validation error if proof type is not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 404 if the consent record is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">privacyNoticeController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">dataBreachController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">ZapierController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">getAppDefinition should return app definition</h4>
        <div class="test-duration">Duration: 6ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"description": "Connect NovaConnect UAC with 5,000+ apps on Zapier.", "title": "NovaConnect UAC"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:137:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getTriggers should return triggers</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m[{"display": {"description": "Triggers when a new connector is created.", "label": "New Connector"}, "key": "new_connector", "noun": "Connector"}][39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:147:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getActions should return actions</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m[{"display": {"description": "Creates a new connector.", "label": "Create Connector"}, "key": "create_connector", "noun": "Connector"}][39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:163:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">authorizeOAuth should redirect with code</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalled[2m()[22m

Expected number of calls: >= [32m1[39m
Received number of calls:    [31m0[39m
    at Object.toHaveBeenCalled (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:185:26)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">authorizeOAuth should validate client ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">authorizeOAuth should validate response type</h4>
        <div class="test-duration">Duration: 125ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

[32m- Expected[39m
[31m+ Received[39m

[2m  Object {[22m
[32m-   "error": "unsupported_response_type",[39m
[32m-   "error_description": "Only code response type is supported",[39m
[31m+   "error": "invalid_client",[39m
[31m+   "error_description": "Invalid client ID",[39m
[2m  }[22m,

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:220:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getOAuthToken should generate access token</h4>
        <div class="test-duration">Duration: 4ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m200[39m
Received: [31m401[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:237:24)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getOAuthToken should refresh access token</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m200[39m
Received: [31m401[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:257:24)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">getOAuthToken should validate client credentials</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getOAuthToken should validate grant type</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m400[39m
Received: [31m401[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:294:24)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">beforeApp should execute successfully</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">afterApp should execute successfully</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">newConnectorTrigger should return connectors</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">newWorkflowTrigger should return workflows</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">complianceEventTrigger should return events</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">createConnectorAction should create connector</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">createConnectorAction should validate required fields</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">executeWorkflowAction should execute workflow</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">executeWorkflowAction should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">createComplianceEvidenceAction should create evidence</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">createComplianceEvidenceAction should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">registerApp should register app</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"createdAt": "2023-01-01T00:00:00Z", "description": "Test Description", "id": "app-123", "name": "Test App", "webhookUrl": "https://hooks.zapier.com/hooks/catch/123/456/"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:483:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">registerApp should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getAllApps should return apps</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m[{"createdAt": "2023-01-01T00:00:00Z", "description": "Test Description", "id": "app-123", "name": "Test App", "webhookUrl": "https://hooks.zapier.com/hooks/catch/123/456/"}][39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:511:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getAppById should return app</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"createdAt": "2023-01-01T00:00:00Z", "description": "Test Description", "id": "app-123", "name": "Test App", "webhookUrl": "https://hooks.zapier.com/hooks/catch/123/456/"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:530:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">updateApp should update app</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"createdAt": "2023-01-01T00:00:00Z", "description": "Updated Description", "id": "app-123", "name": "Updated App", "updatedAt": "2023-01-02T00:00:00Z", "webhookUrl": "https://hooks.zapier.com/hooks/catch/123/456/"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:553:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">deleteApp should delete app</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">registerTrigger should register trigger</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"createdAt": "2023-01-01T00:00:00Z", "display": {"description": "Test trigger description", "label": "Test Trigger"}, "id": "trigger-123", "key": "test_trigger", "noun": "Test"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:593:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">registerTrigger should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">registerAction should register action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"createdAt": "2023-01-01T00:00:00Z", "display": {"description": "Test action description", "label": "Test Action"}, "id": "action-123", "key": "test_action", "noun": "Test"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\ZapierController.test.js:641:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">registerAction should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">subjectRequestController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">subjectRequestService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all requests with pagination</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default options if not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 26ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a request by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the request is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new request</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update a request</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the request is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should process a request</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the request is not found</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a data export for a request</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the request is not found</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw ValidationError if request type is not access</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should identify affected systems for a request</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if the request is not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">privacyNoticeService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should get all privacy notices with default pagination</h4>
        <div class="test-duration">Duration: 8ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 28ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get a privacy notice by ID</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if privacy notice is not found</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get the latest privacy notice by type and audience</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get the latest privacy notice without language filter</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if privacy notice is not found</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new privacy notice with auto-incremented version</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new privacy notice with provided version and status</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle validation errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update a privacy notice</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update status and set effectiveDate for published notices</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw ValidationError when updating a published notice</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete a privacy notice</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw ValidationError when deleting a published notice</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">BreakGlassProtocol.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">notificationController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should have the correct functions</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all notifications with pagination</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should apply filters from query parameters</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a notification by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new notification</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should mark a notification as read</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should send a notification</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError when notification is not in pending status</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate notifications based on criteria</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError when notification type is not provided</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should send all pending notifications</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get notifications by recipient</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get notifications by related entity</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">EmergencyProfile.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">impact-assessment.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new impact assessment</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when creating an assessment</h4>
        <div class="test-duration">Duration: 24ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve an impact assessment by ID</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return null if assessment not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when retrieving an assessment</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update an existing impact assessment</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when updating an assessment</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete an impact assessment</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false if assessment not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when deleting an assessment</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should list all impact assessments with pagination</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle default pagination values</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when listing assessments</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should add a finding to an assessment</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when adding a finding</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve findings for an assessment</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when retrieving findings</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">dataTransformer.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data using the default transformer</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data for CRM integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data for marketing integration</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data for data-export action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data for data-deletion action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data for CRM data-export action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform request data for marketing data-export action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle missing data in data-export action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle missing data in data-deletion action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data using the default transformer</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data for CRM integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data for marketing integration</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data for data-export action</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data for data-deletion action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data for CRM data-export action</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform response data for marketing data-export action</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate an export ID if not provided in data-export action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a deletion ID if not provided in data-deletion action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use a default status if not provided in data-deletion action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the specific transformer for integration type and action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the integration type transformer if no specific transformer exists</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the action transformer if no integration type or specific transformer exists</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the default transformer if no other transformer exists</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the specific transformer for integration type and action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the integration type transformer if no specific transformer exists</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the action transformer if no integration type or specific transformer exists</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the default transformer if no other transformer exists</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the original data</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the response data</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should map data categories to CRM fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty data categories</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle unknown data categories</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should map data categories to marketing fields</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty data categories</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle unknown data categories</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">integrationRegistry.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all integrations</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 13ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return an integration by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw NotFoundError if integration is not found</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should register a new integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw ConflictError if integration already exists</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle validation errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update an integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should not update the ID field</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle validation errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete an integration</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should configure an integration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle validation errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true if integration supports the action</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false if integration does not support the action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the handler for a supported action</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw ValidationError if action is not supported</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">dataBreach.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">regulatory-integration.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should build API URL correctly</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get request options with correct headers</h4>
        <div class="test-duration">Duration: 49ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve regulation details from cache if available</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve regulation details from API if not in cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 21ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve compliance requirements from cache if available</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve compliance requirements from API if not in cache</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve jurisdiction information from cache if available</h4>
        <div class="test-duration">Duration: 11ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve jurisdiction information from API if not in cache</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve regulatory updates from cache if available</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve regulatory updates from API if not in cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should retrieve compliance status for a regulation</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should submit a compliance report</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should refresh regulatory data and clear cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle API errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">ProgressiveDisclosureSystem.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">consentRecord.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">utils-functions.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all items when no filters are provided</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should filter items based on exact match</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should filter items based on nested properties</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle undefined or null filter values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle undefined or null item values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle exact match case insensitive</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle array contains filter</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle array intersects filter</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle date range filter</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle number range filter</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle string partial match by default</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all items when no query is provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all items when no fields are provided</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should search in specified fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should search in nested fields</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle array fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle number fields</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle boolean fields</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle undefined or null item values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should sort by createdAt desc by default</h4>
        <div class="test-duration">Duration: 62ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should sort by specified field in ascending order</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should sort by specified field in descending order</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should sort by nested field</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle undefined values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle non-string values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return items unchanged when sortBy is not provided</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should paginate with default values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should paginate with custom page and limit</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle last page</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should limit to maximum of 100 items per page</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error for invalid page</h4>
        <div class="test-duration">Duration: 21ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error for invalid limit</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should paginate with default values</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should paginate with cursor</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle last page</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should limit to maximum of 100 items per page</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error for invalid limit</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle custom cursor field</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle reverse order</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">certifications-accreditation.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">tracking-manager.validation.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should validate requirement name is required</h4>
        <div class="test-duration">Duration: 23ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement name is not empty</h4>
        <div class="test-duration">Duration: 27ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Requirement name cannot be empty"[39m
Received message:   [31m"Requirement name is required"[39m

    [0m [90m 22 |[39m     [90m// Validate the requirement data[39m
     [90m 23 |[39m     [36mif[39m ([33m![39mrequirementData[33m.[39mname) {
    [31m[1m>[22m[39m[90m 24 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m'Requirement name is required'[39m)[33m;[39m
     [90m    |[39m             [31m[1m^[22m[39m
     [90m 25 |[39m     }
     [90m 26 |[39m     
     [90m 27 |[39m     [90m// Validate status if provided[39m[0m

      [2mat TrackingManager.create_requirement ([22mtests/mocks/tracking-manager.mock.js[2m:24:13)[22m
      [2mat create_requirement ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:47:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:51:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:51:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement priority is valid</h4>
        <div class="test-duration">Duration: 6ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid priority: invalid_priority. Must be one of: low, medium, high"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:61:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement status is valid</h4>
        <div class="test-duration">Duration: 11ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid status: invalid_status. Must be one of: pending, in_progress, completed, deferred, cancelled"[39m
Received message:   [31m"Invalid status: invalid_status"[39m

    [0m [90m 29 |[39m       [36mconst[39m validStatuses [33m=[39m [[32m'pending'[39m[33m,[39m [32m'in_progress'[39m[33m,[39m [32m'completed'[39m[33m,[39m [32m'deferred'[39m[33m,[39m [32m'cancelled'[39m][33m;[39m
     [90m 30 |[39m       [36mif[39m ([33m![39mvalidStatuses[33m.[39mincludes(requirementData[33m.[39mstatus)) {
    [31m[1m>[22m[39m[90m 31 |[39m         [36mthrow[39m [36mnew[39m [33mError[39m([32m`Invalid status: ${requirementData.status}`[39m)[33m;[39m
     [90m    |[39m               [31m[1m^[22m[39m
     [90m 32 |[39m       }
     [90m 33 |[39m     }
     [90m 34 |[39m     [0m

      [2mat TrackingManager.create_requirement ([22mtests/mocks/tracking-manager.mock.js[2m:31:15)[22m
      [2mat create_requirement ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:67:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:71:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:71:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement due_date format</h4>
        <div class="test-duration">Duration: 4ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid due_date format: invalid_date. Must be YYYY-MM-DD or empty"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:81:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement tags is an array</h4>
        <div class="test-duration">Duration: 4ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Requirement tags must be an array"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:91:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate activity name is required</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity name is not empty</h4>
        <div class="test-duration">Duration: 5ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Activity name cannot be empty"[39m
Received message:   [31m"Activity name is required"[39m

    [0m [90m 123 |[39m     [90m// Validate the activity data[39m
     [90m 124 |[39m     [36mif[39m ([33m![39mactivityData[33m.[39mname) {
    [31m[1m>[22m[39m[90m 125 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m'Activity name is required'[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 126 |[39m     }
     [90m 127 |[39m     
     [90m 128 |[39m     [90m// Validate type if provided[39m[0m

      [2mat TrackingManager.create_activity ([22mtests/mocks/tracking-manager.mock.js[2m:125:13)[22m
      [2mat create_activity ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:108:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:112:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:112:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity type is valid</h4>
        <div class="test-duration">Duration: 5ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid type: invalid_type. Must be one of: task, meeting, review, documentation, implementation, assessment, training"[39m
Received message:   [31m"Invalid type: invalid_type"[39m

    [0m [90m 130 |[39m       [36mconst[39m validTypes [33m=[39m [[32m'task'[39m[33m,[39m [32m'meeting'[39m[33m,[39m [32m'review'[39m[33m,[39m [32m'audit'[39m[33m,[39m [32m'documentation'[39m[33m,[39m [32m'other'[39m][33m;[39m
     [90m 131 |[39m       [36mif[39m ([33m![39mvalidTypes[33m.[39mincludes(activityData[33m.[39mtype)) {
    [31m[1m>[22m[39m[90m 132 |[39m         [36mthrow[39m [36mnew[39m [33mError[39m([32m`Invalid type: ${activityData.type}`[39m)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 133 |[39m       }
     [90m 134 |[39m     }
     [90m 135 |[39m     [0m

      [2mat TrackingManager.create_activity ([22mtests/mocks/tracking-manager.mock.js[2m:132:15)[22m
      [2mat create_activity ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:118:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:122:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:122:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity status is valid</h4>
        <div class="test-duration">Duration: 7ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid status: invalid_status. Must be one of: pending, in_progress, completed, deferred, cancelled"[39m
Received message:   [31m"Invalid status: invalid_status"[39m

    [0m [90m 138 |[39m       [36mconst[39m validStatuses [33m=[39m [[32m'pending'[39m[33m,[39m [32m'in_progress'[39m[33m,[39m [32m'completed'[39m[33m,[39m [32m'deferred'[39m[33m,[39m [32m'cancelled'[39m][33m;[39m
     [90m 139 |[39m       [36mif[39m ([33m![39mvalidStatuses[33m.[39mincludes(activityData[33m.[39mstatus)) {
    [31m[1m>[22m[39m[90m 140 |[39m         [36mthrow[39m [36mnew[39m [33mError[39m([32m`Invalid status: ${activityData.status}`[39m)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 141 |[39m       }
     [90m 142 |[39m     }
     [90m 143 |[39m     [0m

      [2mat TrackingManager.create_activity ([22mtests/mocks/tracking-manager.mock.js[2m:140:15)[22m
      [2mat create_activity ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:128:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:132:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:132:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity start_date format</h4>
        <div class="test-duration">Duration: 4ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid start_date format: invalid_date. Must be YYYY-MM-DD or empty"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:142:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity end_date format</h4>
        <div class="test-duration">Duration: 4ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid end_date format: invalid_date. Must be YYYY-MM-DD or empty"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:152:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity requirement_id exists if provided</h4>
        <div class="test-duration">Duration: 4ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Requirement with ID non_existent_id does not exist"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:162:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement exists when updating</h4>
        <div class="test-duration">Duration: 5ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Requirement with ID non_existent_id does not exist"[39m
Received message:   [31m"Requirement not found: non_existent_id"[39m

    [0m [90m 72 |[39m   update_requirement(requirementId[33m,[39m requirementData) {
     [90m 73 |[39m     [36mif[39m ([33m![39m[36mthis[39m[33m.[39mrequirements[requirementId]) {
    [31m[1m>[22m[39m[90m 74 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Requirement not found: ${requirementId}`[39m)[33m;[39m
     [90m    |[39m             [31m[1m^[22m[39m
     [90m 75 |[39m     }
     [90m 76 |[39m     
     [90m 77 |[39m     [36mconst[39m requirement [33m=[39m [36mthis[39m[33m.[39mrequirements[requirementId][33m;[39m[0m

      [2mat TrackingManager.update_requirement ([22mtests/mocks/tracking-manager.mock.js[2m:74:13)[22m
      [2mat update_requirement ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:170:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:173:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:173:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity exists when updating</h4>
        <div class="test-duration">Duration: 11ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Activity with ID non_existent_id does not exist"[39m
Received message:   [31m"Activity not found: non_existent_id"[39m

    [0m [90m 181 |[39m   update_activity(activityId[33m,[39m activityData) {
     [90m 182 |[39m     [36mif[39m ([33m![39m[36mthis[39m[33m.[39mactivities[activityId]) {
    [31m[1m>[22m[39m[90m 183 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Activity not found: ${activityId}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 184 |[39m     }
     [90m 185 |[39m     
     [90m 186 |[39m     [36mconst[39m activity [33m=[39m [36mthis[39m[33m.[39mactivities[activityId][33m;[39m[0m

      [2mat TrackingManager.update_activity ([22mtests/mocks/tracking-manager.mock.js[2m:183:13)[22m
      [2mat update_activity ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:179:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:182:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:182:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement update data</h4>
        <div class="test-duration">Duration: 7ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid priority: invalid_priority. Must be one of: low, medium, high"[39m

Received function did not throw
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:196:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity update data</h4>
        <div class="test-duration">Duration: 15ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Invalid type: invalid_type. Must be one of: task, meeting, review, documentation, implementation, assessment, training"[39m
Received message:   [31m"Invalid type: invalid_type"[39m

    [0m [90m 193 |[39m       [36mconst[39m validTypes [33m=[39m [[32m'task'[39m[33m,[39m [32m'meeting'[39m[33m,[39m [32m'review'[39m[33m,[39m [32m'audit'[39m[33m,[39m [32m'documentation'[39m[33m,[39m [32m'other'[39m][33m;[39m
     [90m 194 |[39m       [36mif[39m ([33m![39mvalidTypes[33m.[39mincludes(activityData[33m.[39mtype)) {
    [31m[1m>[22m[39m[90m 195 |[39m         [36mthrow[39m [36mnew[39m [33mError[39m([32m`Invalid type: ${activityData.type}`[39m)[33m;[39m
     [90m     |[39m               [31m[1m^[22m[39m
     [90m 196 |[39m       }
     [90m 197 |[39m       activity[33m.[39mtype [33m=[39m activityData[33m.[39mtype[33m;[39m
     [90m 198 |[39m     }[0m

      [2mat TrackingManager.update_activity ([22mtests/mocks/tracking-manager.mock.js[2m:195:15)[22m
      [2mat update_activity ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:207:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:210:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:210:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate requirement exists when deleting</h4>
        <div class="test-duration">Duration: 6ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Requirement with ID non_existent_id does not exist"[39m
Received message:   [31m"Requirement not found: non_existent_id"[39m

    [0m [90m 105 |[39m   delete_requirement(requirementId) {
     [90m 106 |[39m     [36mif[39m ([33m![39m[36mthis[39m[33m.[39mrequirements[requirementId]) {
    [31m[1m>[22m[39m[90m 107 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Requirement not found: ${requirementId}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 108 |[39m     }
     [90m 109 |[39m     
     [90m 110 |[39m     [90m// Delete the requirement from memory[39m[0m

      [2mat TrackingManager.delete_requirement ([22mtests/mocks/tracking-manager.mock.js[2m:107:13)[22m
      [2mat delete_requirement ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:218:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:219:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:219:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should validate activity exists when deleting</h4>
        <div class="test-duration">Duration: 6ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoThrow[2m([22m[32mexpected[39m[2m)[22m

Expected substring: [32m"Activity with ID non_existent_id does not exist"[39m
Received message:   [31m"Activity not found: non_existent_id"[39m

    [0m [90m 220 |[39m   delete_activity(activityId) {
     [90m 221 |[39m     [36mif[39m ([33m![39m[36mthis[39m[33m.[39mactivities[activityId]) {
    [31m[1m>[22m[39m[90m 222 |[39m       [36mthrow[39m [36mnew[39m [33mError[39m([32m`Activity not found: ${activityId}`[39m)[33m;[39m
     [90m     |[39m             [31m[1m^[22m[39m
     [90m 223 |[39m     }
     [90m 224 |[39m     
     [90m 225 |[39m     [90m// Delete the activity from memory[39m[0m

      [2mat TrackingManager.delete_activity ([22mtests/mocks/tracking-manager.mock.js[2m:222:13)[22m
      [2mat delete_activity ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:225:25)[22m
      [2mat Object.<anonymous> ([22mnode_modules/expect/build/toThrowMatchers.js[2m:74:11)[22m
      [2mat Object.throwingMatcher [as toThrow] ([22mnode_modules/expect/build/index.js[2m:320:21)[22m
      [2mat Object.toThrow ([22mtests/unit/novatrack/tracking-manager.validation.test.js[2m:226:10)[22m
    at Object.toThrow (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:226:10)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should maintain data integrity when creating requirements</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should maintain data integrity when creating activities</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should maintain data integrity when updating requirements</h4>
        <div class="test-duration">Duration: 8ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mnot[2m.[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: not [32m"2025-07-12T09:43:09.259Z"[39m
    at Object.toBe (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:315:49)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should maintain data integrity when updating activities</h4>
        <div class="test-duration">Duration: 8ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mnot[2m.[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: not [32m"2025-07-12T09:43:09.267Z"[39m
    at Object.toBe (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.validation.test.js:339:46)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">dataProcessingController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return all data processing activities with pagination</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use query parameters for filtering and pagination</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a data processing activity by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new data processing activity</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case pending">
        <h4 class="test-case-title">should handle errors</h4>
        <div class="test-duration">Duration: nullms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update a data processing activity</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle validation errors</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete a data processing activity</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 10ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle other errors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">tracking-manager.edge-cases.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should handle file system errors gracefully</h4>
        <div class="test-duration">Duration: 79ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle invalid JSON data gracefully</h4>
        <div class="test-duration">Duration: 27ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty requirement data</h4>
        <div class="test-duration">Duration: 15ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty activity data</h4>
        <div class="test-duration">Duration: 10ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle very large requirement data</h4>
        <div class="test-duration">Duration: 395ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle special characters in requirement names</h4>
        <div class="test-duration">Duration: 8ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle concurrent operations</h4>
        <div class="test-duration">Duration: 29ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle minimum valid requirement data</h4>
        <div class="test-duration">Duration: 8ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle minimum valid activity data</h4>
        <div class="test-duration">Duration: 8ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBeNull[2m()[22m

Received: [31mundefined[39m
    at Object.toBeNull (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.edge-cases.test.js:255:39)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle updating a requirement with all fields</h4>
        <div class="test-duration">Duration: 17ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mnot[2m.[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: not [32m"2025-07-12T09:43:09.841Z"[39m
    at Object.toBe (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.edge-cases.test.js:297:49)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle updating an activity with all fields</h4>
        <div class="test-duration">Duration: 15ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mnot[2m.[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: not [32m"2025-07-12T09:43:09.856Z"[39m
    at Object.toBe (D:\novafuse-api-superstore\tests\unit\novatrack\tracking-manager.edge-cases.test.js:331:46)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">contracts-policy-lifecycle.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">business-intelligence-workflow.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">complianceKit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">apis-ipaas-developer-tools.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">requestExecutor.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">blockchainEvidence.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">rbac-service.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">testEngine.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">validators.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid email addresses</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid email addresses</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid phone numbers</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid phone numbers</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid dates</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid dates</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid date ranges</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid date ranges</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid URLs</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid URLs</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid IPv4 addresses</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid IPv6 addresses</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid IP addresses</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid DSR types</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid DSR types</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid consent types</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid consent types</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid legal bases</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid legal bases</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid breach severities</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid breach severities</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for valid notification priorities</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for invalid notification priorities</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">dateUtils.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the due date for a DSR with default jurisdiction (GDPR)</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the due date for a DSR with CCPA jurisdiction</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the due date for a DSR with LGPD jurisdiction</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return null for consent types that do not expire</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the expiry date based on consent type</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use the provided validity period if specified</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the notification deadline for GDPR (72 hours)</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the notification deadline for CCPA (5 days)</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should default to 72 hours for unknown jurisdictions</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should format a date as ISO string without milliseconds</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return null for null or undefined dates</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should format a date as a human-readable string</h4>
        <div class="test-duration">Duration: 190ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return null for null or undefined dates</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should calculate the number of days between two dates</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle string dates</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for dates in the past</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for dates in the future</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for dates in the future</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for dates in the past</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the correct dates for last-7-days</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the correct dates for last-30-days</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the correct dates for last-90-days</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the correct dates for last-12-months</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the correct dates for year-to-date</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should default to last-30-days for unknown periods</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">FeatureFlagService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">should check if user has feature access with tenant ID</h4>
        <div class="test-duration">Duration: 81ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m"test-user"[39m

Number of calls: [31m0[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\services\FeatureFlagService.test.js:129:51)
Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\AppData\Local\Temp\nova-connect-test-1752313394482\feature_flags\user_entitlements.json'</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check if user has feature access without tenant ID</h4>
        <div class="test-duration">Duration: 20ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get feature limit with tenant ID</h4>
        <div class="test-duration">Duration: 35ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get feature limit without tenant ID</h4>
        <div class="test-duration">Duration: 21ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should get user available features with tenant ID</h4>
        <div class="test-duration">Duration: 32ms</div>
        
        <div class="test-case-error">Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\AppData\Local\Temp\nova-connect-test-1752313394657\feature_flags\user_entitlements.json'</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should get user available features without tenant ID</h4>
        <div class="test-duration">Duration: 20ms</div>
        
        <div class="test-case-error">Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\AppData\Local\Temp\nova-connect-test-1752313394690\feature_flags\user_entitlements.json'</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get tenant package</h4>
        <div class="test-duration">Duration: 14ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should set tenant package</h4>
        <div class="test-duration">Duration: 18ms</div>
        
        <div class="test-case-error">Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\AppData\Local\Temp\nova-connect-test-1752313394723\feature_flags\user_entitlements.json'</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get all packages</h4>
        <div class="test-duration">Duration: 26ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get package by ID</h4>
        <div class="test-duration">Duration: 11ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should clear cache</h4>
        <div class="test-duration">Duration: 41ms</div>
        
        <div class="test-case-error">Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\AppData\Local\Temp\nova-connect-test-1752313394767\feature_flags\user_entitlements.json'
Error: ENOENT: no such file or directory, open 'C:\Users\<USER>\AppData\Local\Temp\nova-connect-test-1752313394779\feature_flags\user_entitlements.json'</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">connector-registry.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return an empty array when no connectors exist</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all connectors</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should filter connectors by status</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should filter connectors by type</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return connectors from cache when available</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when connector does not exist</h4>
        <div class="test-duration">Duration: 37ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a connector by ID</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return connector from cache when available</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new connector</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when connector data is invalid</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update an existing connector</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when connector does not exist</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when update data is invalid</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete an existing connector</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when connector does not exist</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">badgeSystem.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">tracking-manager.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new requirement with valid data</h4>
        <div class="test-duration">Duration: 27ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when creating a requirement with invalid data</h4>
        <div class="test-duration">Duration: 44ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when creating a requirement with invalid status</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new activity with valid data</h4>
        <div class="test-duration">Duration: 10ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when creating an activity with invalid data</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error when creating an activity with invalid type</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all activities for a requirement</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return an empty array for a requirement with no activities</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all requirements for a framework</h4>
        <div class="test-duration">Duration: 11ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return an empty array for a framework with no requirements</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should work with generated test dataset</h4>
        <div class="test-duration">Duration: 19ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">logger.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should log GET requests without body</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log POST requests with masked body</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle requests without user</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle requests with null body</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log responses</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle responses without user</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should pass arguments to original end method</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log errors</h4>
        <div class="test-duration">Duration: 13ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors without status</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors with requests without user</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should be a function</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use morgan with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use morgan for HTTP logging</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should be configured with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should mask password in data</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should mask sensitive fields in data</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return null for null data</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle data without sensitive fields</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">server.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create an Express app</h4>
        <div class="test-duration">Duration: 29ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set up error handling for unhandled routes</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set up global error handler</h4>
        <div class="test-duration">Duration: 16ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set up handlers for unhandled rejections and exceptions</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use the PORT environment variable</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default port 3000 if PORT is not set</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle database connection errors</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">reportController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">BruteForceProtectionService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should initialize with the correct data directory</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should call ensureDataDir</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should load attempts from file</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return empty object if file does not exist</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error if file read fails for other reasons</h4>
        <div class="test-duration">Duration: 17ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should save attempts to file</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error if file write fails</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should initialize attempts for new identifier</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should increment count for existing identifier</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should block identifier after max attempts</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for unknown identifier</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for non-blocked identifier</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return block info for blocked identifier</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should reset block if block duration has expired</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw BruteForceError if identifier is blocked</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true if identifier is not blocked</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">error.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError</h4>
        <div class="test-duration">Duration: 12ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle AuthenticationError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ConflictError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ForbiddenError</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle BadRequestError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle RateLimitError</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle default error case</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle error with custom status code</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle anonymous user</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError with default message</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle AuthenticationError with default message</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError with default message</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">cache.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case pending">
        <h4 class="test-case-title">should initialize with default options</h4>
        <div class="test-duration">Duration: nullms</div>
        
      </div>
        
      <div class="test-case pending">
        <h4 class="test-case-title">should log initialization</h4>
        <div class="test-duration">Duration: nullms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set a value in the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set a value with custom TTL</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when setting a value</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get a value from the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return null for cache miss</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when getting a value</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete a value from the cache</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false if key not found</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when deleting a value</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should clear all values from the cache</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when clearing the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return all keys in the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when getting keys</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return cache statistics</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when getting stats</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should close the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle errors when closing the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">BillingController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">IpRestrictionService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should initialize with the correct data directory</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should call ensureDataDir</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should initialize with default config</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should load restrictions from file</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return default config if file does not exist</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error if file read fails for other reasons</h4>
        <div class="test-duration">Duration: 26ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should save restrictions to file</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error if file write fails</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should allow all IPs if restrictions are disabled</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check rules first</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check allowlist in allowlist mode</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should deny IP not in allowlist in allowlist mode</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check blocklist in blocklist mode</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should allow IP not in blocklist in blocklist mode</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should add IP to allowlist</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should remove IP from blocklist if added to allowlist</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">PackageController.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">getAllPackages should return all packages</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m[{"id": "core", "name": "Core Package"}, {"id": "enterprise", "name": "Enterprise Package"}][39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:77:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getPackageById should return package by ID</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"features": ["feature1", "feature2", "feature3"], "id": "enterprise", "limits": {"connections": 100}, "name": "Enterprise Package"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:88:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getPackageById should handle not found error</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m404[39m

Number of calls: [31m0[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:106:24)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">createPackage should create a new package</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"features": ["test.feature1", "test.feature2"], "id": "test-package", "limits": {"connections": 50}, "name": "Test Package"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:125:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">createPackage should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">updatePackage should update a package</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"features": ["feature1", "feature2", "feature3"], "id": "enterprise", "limits": {"connections": 100}, "name": "Updated Enterprise Package"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:156:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">updatePackage should handle not found error</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m404[39m

Number of calls: [31m0[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:177:24)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">deletePackage should delete a package</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">deletePackage should handle not found error</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m404[39m
Received: [31m204[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:203:24)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">getTenantPackage should return tenant package</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"features": ["feature1", "feature2", "feature3"], "id": "enterprise", "limits": {"connections": 100}, "name": "Enterprise Package"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:215:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">setTenantPackage should set tenant package</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

Expected: [32m{"customFeatures": ["custom.feature1"], "customLimits": {"connections": 200}, "packageId": "enterprise", "tenantId": "test-tenant"}[39m
Received: [31mundefined[39m

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-connect\tests\unit\controllers\PackageController.test.js:233:22)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">setTenantPackage should validate required fields</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">clearCache should clear cache</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">AuthAuditService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should initialize with the correct data directory</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log successful login attempt</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log failed login attempt</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should include additional details</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log logout event</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log successful registration</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log two-factor authentication setup</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should log failed two-factor authentication verification</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get authentication audit logs with filters</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">authenticationManager.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">authenticationManager.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should generate OAuth2 headers</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate API key headers</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default API key header name if not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate Basic Auth headers</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate custom auth headers</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return empty object for custom auth with no headers</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw error for unsupported auth type</h4>
        <div class="test-duration">Duration: 12ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should encrypt configuration data</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should decrypt configuration data</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate OAuth2 configuration</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate OAuth2 configuration with refresh token</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate API key configuration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate Basic Auth configuration</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate custom auth configuration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for unsupported auth type</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">encryptionUtils.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should encrypt a string</h4>
        <div class="test-duration">Duration: 7ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should encrypt an object</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should encrypt a number</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty strings</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle null values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should decrypt an encrypted string</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should decrypt an encrypted object</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should decrypt an encrypted number</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty strings</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle null values</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error for invalid encrypted data</h4>
        <div class="test-duration">Duration: 24ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should hash a string</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should hash an object</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should hash a number</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty strings</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle null values</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should produce the same hash for the same input</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should produce different hashes for different inputs</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a key with the default length</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a key with a custom length</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different keys on each call</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">connector-executor.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">cache.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should set a value in the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set a value with custom TTL</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update an existing value</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return undefined for non-existent key</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return the cached value for existing key</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return undefined for expired key</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update lastAccessed time on get</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for non-existent key</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return true for existing key</h4>
        <div class="test-duration">Duration: 9ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false for expired key</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete a key from the cache</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return false when deleting non-existent key</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should clear all keys from the cache</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should remove expired items during cleanup</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should evict least recently used items when cache is full</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should return cache statistics</h4>
        <div class="test-duration">Duration: 3ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: [32m1[39m
Received: [31m3[39m
    at Object.toBe (D:\novafuse-api-superstore\tests\unit\utils\cache.test.js:233:28)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">blockchain-verification.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should verify evidence correctly</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle evidence with attachments</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate proof correctly</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify proof correctly</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify actual Merkle proofs correctly</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify evidence efficiently</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">connector-api.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">PackageConfigRegistry.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should initialize with default packages</h4>
        <div class="test-duration">Duration: 122ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get package by ID</h4>
        <div class="test-duration">Duration: 120ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new package</h4>
        <div class="test-duration">Duration: 123ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should update a package</h4>
        <div class="test-duration">Duration: 150ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should delete a package</h4>
        <div class="test-duration">Duration: 139ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should set and get tenant mapping</h4>
        <div class="test-duration">Duration: 174ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should check if tenant has feature access</h4>
        <div class="test-duration">Duration: 130ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get tenant feature limit</h4>
        <div class="test-duration">Duration: 140ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should get tenant available features</h4>
        <div class="test-duration">Duration: 116ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use cache for repeated calls</h4>
        <div class="test-duration">Duration: 122ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">BlockchainVerifier.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">auth.middleware.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should return 401 if no authorization header is provided</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 401 if authorization header has wrong format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 401 if token is not provided</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 401 if token verification fails</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should attach user to request and call next() if token is valid</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should pass other errors to next()</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 401 if user is not authenticated</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return 403 if user does not have required role</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should call next() if user has required role</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should call next() if no roles are required</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should call next() if user has one of multiple required roles</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">idGenerator.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a random ID with the default length</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a random ID with a custom length</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a DSR ID with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a consent ID with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a data breach ID with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a privacy notice ID with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a notification ID with the correct format</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a processing activity ID with the correct format</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs on each call</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate an integration ID with the correct format</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should normalize the integration name</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different IDs for the same name on each call</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate a UUID</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should generate different UUIDs on each call</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">RateLimitService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">should initialize with the correct data directory</h4>
        <div class="test-duration">Duration: 2ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mreceived[39m[2m).[22mtoBe[2m([22m[32mexpected[39m[2m) // Object.is equality[22m

Expected: [32m"D:\\novafuse-api-superstore\\nova-connect\\tests\\unit\\services\\test-data\\rate_limit_config.json"[39m
Received: [31mundefined[39m
    at Object.toBe (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:33:43)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should call ensureDataDir</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should load configuration from file</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.loadConfig is not a function
    at Object.loadConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:53:45)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should return default config if file does not exist</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.loadConfig is not a function
    at Object.loadConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:64:45)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should throw error if file read fails for other reasons</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.loadConfig is not a function
    at Object.loadConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:73:37)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should save configuration to file</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.saveConfig is not a function
    at Object.saveConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:87:30)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should throw error if file write fails</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.saveConfig is not a function
    at Object.saveConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:99:37)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should create a rate limiter with the correct configuration</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.createLimiter is not a function
    at Object.createLimiter (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:118:46)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should return a pass-through middleware if rate limiting is disabled</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.createLimiter is not a function
    at Object.createLimiter (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:134:46)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should update the configuration for a specific type</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.updateConfig is not a function
    at Object.updateConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:164:45)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should throw an error if the type does not exist</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">TypeError: rateLimitService.updateConfig is not a function
    at Object.updateConfig (D:\novafuse-api-superstore\nova-connect\tests\unit\services\RateLimitService.test.js:179:37)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">controlService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">tensor-runtime.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create a tensor with the correct dimensions</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle empty data</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should process a tensor correctly</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle complex data in tensors</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should transform a tensor correctly</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should process tensors efficiently</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">error.middleware.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">should handle ValidationError</h4>
        <div class="test-duration">Duration: 10ms</div>
        
        <div class="test-case-error">ReferenceError: setImmediate is not defined
    at Console.log (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\winston\lib\winston\transports\console.js:53:5)
    at Console._write (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\winston-transport\modern.js:103:17)
    at doWrite (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_writable.js:390:139)
    at writeOrBuffer (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_writable.js:381:5)
    at Console.Object.<anonymous>.Writable.write (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_writable.js:302:11)
    at DerivedLogger.ondata (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_readable.js:629:20)
    at DerivedLogger.emit (node:events:529:35)
    at addChunk (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_readable.js:279:12)
    at readableAddChunk (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_readable.js:262:11)
    at DerivedLogger.Object.<anonymous>.Readable.push (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_readable.js:228:10)
    at DerivedLogger.Object.<anonymous>.Transform.push (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_transform.js:132:32)
    at DerivedLogger._transform (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\winston\lib\winston\logger.js:314:12)
    at DerivedLogger.Object.<anonymous>.Transform._read (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_transform.js:166:10)
    at DerivedLogger.Object.<anonymous>.Transform._write (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_transform.js:155:83)
    at doWrite (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_writable.js:390:139)
    at writeOrBuffer (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_writable.js:381:5)
    at DerivedLogger.Object.<anonymous>.Writable.write (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\readable-stream\lib\_stream_writable.js:302:11)
    at DerivedLogger.<computed> [as error] (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\node_modules\winston\lib\winston\create-logger.js:81:14)
    at error (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\middleware\error.js:19:10)
    at Object.errorHandler (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\tests\unit\error.middleware.test.js:30:5)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle AuthenticationError</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ConflictError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ForbiddenError</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle BadRequestError</h4>
        <div class="test-duration">Duration: 5ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle UnprocessableEntityError</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle TooManyRequestsError</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle generic errors with status code</h4>
        <div class="test-duration">Duration: 3ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

[32m- Expected[39m
[31m+ Received[39m

[2m  Object {[22m
[2m    "error": "InternalServerError",[22m
[2m    "message": "An unexpected error occurred",[22m
[2m    "originalError": "Generic error",[22m
[32m-   "stack": undefined,[39m
[31m+   "stack": "Error: Generic error[39m
[31m+     at Object.<anonymous> (D:\\novafuse-api-superstore\\nova-marketplace\\apis\\privacy\\management\\tests\\unit\\error.middleware.test.js:140:19)[39m
[31m+     at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)[39m
[31m+     at new Promise (<anonymous>)[39m
[31m+     at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)[39m
[31m+     at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)[39m
[31m+     at processTicksAndRejections (node:internal/process/task_queues:95:5)[39m
[31m+     at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)[39m
[31m+     at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)[39m
[31m+     at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)[39m
[31m+     at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)[39m
[31m+     at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)[39m
[31m+     at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)[39m
[31m+     at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)[39m
[31m+     at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)[39m
[31m+     at Object.worker (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\testWorker.js:106:12)",[39m
[2m  }[22m,

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\tests\unit\error.middleware.test.js:146:22)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should handle unknown errors as 500 Internal Server Error</h4>
        <div class="test-duration">Duration: 8ms</div>
        
        <div class="test-case-error">Error: [2mexpect([22m[31mjest.fn()[39m[2m).[22mtoHaveBeenCalledWith[2m([22m[32m...expected[39m[2m)[22m

[32m- Expected[39m
[31m+ Received[39m

[2m  Object {[22m
[2m    "error": "InternalServerError",[22m
[2m    "message": "An unexpected error occurred",[22m
[31m+   "stack": "Error: Unknown error[39m
[31m+     at Object.<anonymous> (D:\\novafuse-api-superstore\\nova-marketplace\\apis\\privacy\\management\\tests\\unit\\error.middleware.test.js:156:19)[39m
[31m+     at Promise.then.completed (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:298:28)[39m
[31m+     at new Promise (<anonymous>)[39m
[31m+     at callAsyncCircusFn (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\utils.js:231:10)[39m
[31m+     at _callCircusTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:316:40)[39m
[31m+     at processTicksAndRejections (node:internal/process/task_queues:95:5)[39m
[31m+     at _runTest (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:252:3)[39m
[31m+     at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:126:9)[39m
[31m+     at _runTestsForDescribeBlock (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:121:9)[39m
[31m+     at run (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\run.js:71:3)[39m
[31m+     at runAndTransformResultsToJestFormat (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)[39m
[31m+     at jestAdapter (D:\\novafuse-api-superstore\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)[39m
[31m+     at runTestInternal (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:367:16)[39m
[31m+     at runTest (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\runTest.js:444:34)[39m
[31m+     at Object.worker (D:\\novafuse-api-superstore\\node_modules\\jest-runner\\build\\testWorker.js:106:12)",[39m
[2m  }[22m,

Number of calls: [31m1[39m
    at Object.toHaveBeenCalledWith (D:\novafuse-api-superstore\nova-marketplace\apis\privacy\management\tests\unit\error.middleware.test.js:161:22)
    at Promise.then.completed (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (D:\novafuse-api-superstore\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:121:9)
    at run (D:\novafuse-api-superstore\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (D:\novafuse-api-superstore\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (D:\novafuse-api-superstore\node_modules\jest-runner\build\runTest.js:444:34)
    at Object.worker (D:\novafuse-api-superstore\node_modules\jest-runner\build\testWorker.js:106:12)</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should include stack trace in development environment</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">errorHandler.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ValidationError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle UnauthorizedError</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ForbiddenError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle NotFoundError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle ConflictError</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle RateLimitError</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should handle unknown errors as 500 Internal Server Error</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use default messages when error message is empty</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">connector-model.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create a connector with default values</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a connector with provided values</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return valid=true when all required fields are present</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return valid=false when name is missing</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return valid=false when description is missing</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return valid=false when both name and description are missing</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a JSON representation of the connector</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should have the correct values</h4>
        <div class="test-duration">Duration: 14ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should have the correct values</h4>
        <div class="test-duration">Duration: 0ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">logger.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create a logger with the correct configuration in development</h4>
        <div class="test-duration">Duration: 4ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should create a logger with the correct configuration in production</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should use the LOG_LEVEL environment variable if available</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should expose a logger object with the expected methods</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">authService.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should authenticate a user with valid credentials</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error for a non-existent user</h4>
        <div class="test-duration">Duration: 8ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error for invalid credentials</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should verify a valid token</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should throw an error for an invalid token</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should hash a password</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">connector-registry.unit.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case failed">
        <h4 class="test-case-title">should load connector templates from the templates directory</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: Invalid connector template: [{"instancePath":"/authentication/oauth2Config/tokenUrl","schemaPath":"#/properties/authentication/properties/oauth2Config/properties/tokenUrl/format","keyword":"format","params":{"format":"uri"},"message":"must match format \"uri\""},{"instancePath":"/endpoints/0/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/1/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/2/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"}]
    at ConnectorRegistry.registerConnector (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:82:15)
    at ConnectorRegistry.registerConnector [as loadConnectorTemplates] (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:58:22)
    at ConnectorRegistry.initialize (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:34:7)
    at Object.<anonymous> (D:\novafuse-api-superstore\nova-connect\tests\unit\connector-registry.unit.test.js:12:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should get a connector by ID</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">Error: Invalid connector template: [{"instancePath":"/authentication/oauth2Config/tokenUrl","schemaPath":"#/properties/authentication/properties/oauth2Config/properties/tokenUrl/format","keyword":"format","params":{"format":"uri"},"message":"must match format \"uri\""},{"instancePath":"/endpoints/0/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/1/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/2/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"}]
    at ConnectorRegistry.registerConnector (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:82:15)
    at ConnectorRegistry.registerConnector [as loadConnectorTemplates] (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:58:22)
    at ConnectorRegistry.initialize (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:34:7)
    at Object.<anonymous> (D:\novafuse-api-superstore\nova-connect\tests\unit\connector-registry.unit.test.js:12:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should search connectors by query</h4>
        <div class="test-duration">Duration: 1ms</div>
        
        <div class="test-case-error">Error: Invalid connector template: [{"instancePath":"/authentication/oauth2Config/tokenUrl","schemaPath":"#/properties/authentication/properties/oauth2Config/properties/tokenUrl/format","keyword":"format","params":{"format":"uri"},"message":"must match format \"uri\""},{"instancePath":"/endpoints/0/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/1/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/2/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"}]
    at ConnectorRegistry.registerConnector (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:82:15)
    at ConnectorRegistry.registerConnector [as loadConnectorTemplates] (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:58:22)
    at ConnectorRegistry.initialize (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:34:7)
    at Object.<anonymous> (D:\novafuse-api-superstore\nova-connect\tests\unit\connector-registry.unit.test.js:12:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should get connectors by category</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">Error: Invalid connector template: [{"instancePath":"/authentication/oauth2Config/tokenUrl","schemaPath":"#/properties/authentication/properties/oauth2Config/properties/tokenUrl/format","keyword":"format","params":{"format":"uri"},"message":"must match format \"uri\""},{"instancePath":"/endpoints/0/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/1/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/2/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"}]
    at ConnectorRegistry.registerConnector (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:82:15)
    at ConnectorRegistry.registerConnector [as loadConnectorTemplates] (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:58:22)
    at ConnectorRegistry.initialize (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:34:7)
    at Object.<anonymous> (D:\novafuse-api-superstore\nova-connect\tests\unit\connector-registry.unit.test.js:12:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should register a new connector</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">Error: Invalid connector template: [{"instancePath":"/authentication/oauth2Config/tokenUrl","schemaPath":"#/properties/authentication/properties/oauth2Config/properties/tokenUrl/format","keyword":"format","params":{"format":"uri"},"message":"must match format \"uri\""},{"instancePath":"/endpoints/0/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/1/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/2/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"}]
    at ConnectorRegistry.registerConnector (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:82:15)
    at ConnectorRegistry.registerConnector [as loadConnectorTemplates] (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:58:22)
    at ConnectorRegistry.initialize (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:34:7)
    at Object.<anonymous> (D:\novafuse-api-superstore\nova-connect\tests\unit\connector-registry.unit.test.js:12:5)</div>
        
      </div>
        
      <div class="test-case failed">
        <h4 class="test-case-title">should update an existing connector</h4>
        <div class="test-duration">Duration: 0ms</div>
        
        <div class="test-case-error">Error: Invalid connector template: [{"instancePath":"/authentication/oauth2Config/tokenUrl","schemaPath":"#/properties/authentication/properties/oauth2Config/properties/tokenUrl/format","keyword":"format","params":{"format":"uri"},"message":"must match format \"uri\""},{"instancePath":"/endpoints/0/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/1/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"},{"instancePath":"/endpoints/2/pagination/type","schemaPath":"#/properties/endpoints/items/properties/pagination/properties/type/enum","keyword":"enum","params":{"allowedValues":["page","offset","cursor","token","none"]},"message":"must be equal to one of the allowed values"}]
    at ConnectorRegistry.registerConnector (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:82:15)
    at ConnectorRegistry.registerConnector [as loadConnectorTemplates] (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:58:22)
    at ConnectorRegistry.initialize (D:\novafuse-api-superstore\nova-connect\registry\connector-registry.js:34:7)
    at Object.<anonymous> (D:\novafuse-api-superstore\nova-connect\tests\unit\connector-registry.unit.test.js:12:5)</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">utils.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should export all utility modules</h4>
        <div class="test-duration">Duration: 167ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should export validators with all expected functions</h4>
        <div class="test-duration">Duration: 1ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should export idGenerator with all expected functions</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should export dateUtils with all expected functions</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should export encryptionUtils with all expected functions</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">validation.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should call next() when validation passes</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return a 400 error when validation fails</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should validate the specified request property</h4>
        <div class="test-duration">Duration: 2ms</div>
        
      </div>
        
      <div class="test-case passed">
        <h4 class="test-case-title">should return multiple validation errors</h4>
        <div class="test-duration">Duration: 3ms</div>
        
      </div>
        
    </div>
  </div>
    
  <div class="test-suite">
    <div class="test-suite-header">
      <h3 class="test-suite-title">tracking-manager.simple.test.js</h3>
      <span class="test-suite-status failed">Failed</span>
    </div>
    <div class="test-suite-body">
      
      <div class="test-case passed">
        <h4 class="test-case-title">should create a new requirement with valid data</h4>
        <div class="test-duration">Duration: 6ms</div>
        
      </div>
        
    </div>
  </div>
    
  
  <footer>
    <p>NovaFuse Universal Platform &copy; 2025</p>
  </footer>
</body>
</html>
  