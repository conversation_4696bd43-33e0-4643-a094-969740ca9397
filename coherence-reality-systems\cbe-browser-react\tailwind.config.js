/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        'mono': ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'divine-pulse': 'divine-pulse 3s ease-in-out infinite',
        'consciousness-glow': 'consciousness-glow 4s ease-in-out infinite',
      },
      keyframes: {
        'divine-pulse': {
          '0%, 100%': {
            opacity: '1',
            transform: 'scale(1)',
            boxShadow: '0 0 20px rgba(255, 215, 0, 0.3)'
          },
          '50%': {
            opacity: '0.9',
            transform: 'scale(1.02)',
            boxShadow: '0 0 30px rgba(255, 215, 0, 0.5)'
          },
        },
        'consciousness-glow': {
          '0%, 100%': { boxShadow: '0 0 15px rgba(99, 102, 241, 0.2)' },
          '50%': { boxShadow: '0 0 25px rgba(99, 102, 241, 0.4)' },
        },
      },
    },
  },
  plugins: [],
}

