/**
 * REAL TRADING PANEL
 * Interface to execute actual trades on MT5 demo account
 * Connects to MT5 Web Terminal for real trading
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  PlayIcon, 
  StopIcon, 
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export default function RealTradingPanel() {
  const [isConnected, setIsConnected] = useState(false);
  const [isTrading, setIsTrading] = useState(false);
  const [accountInfo, setAccountInfo] = useState(null);
  const [tradeHistory, setTradeHistory] = useState([]);
  const [lastTrade, setLastTrade] = useState(null);
  const [status, setStatus] = useState('disconnected');

  // Connect to real MT5 account
  const connectToMT5 = async () => {
    try {
      setStatus('connecting');
      console.log('🔌 Connecting to real MT5 demo account...');
      
      const response = await fetch('/api/mt5/web-connector', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'CONNECT' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        setIsConnected(true);
        setStatus('connected');
        console.log('✅ Connected to MT5 Web Terminal');
        
        // Get account info
        await updateAccountInfo();
      } else {
        setStatus('error');
        console.error('❌ Connection failed:', result.error);
      }
    } catch (error) {
      setStatus('error');
      console.error('❌ Connection error:', error);
    }
  };

  // Update account information
  const updateAccountInfo = async () => {
    try {
      const response = await fetch('/api/mt5/web-connector', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'GET_STATUS' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        setAccountInfo(result.account);
        setTradeHistory(result.real_state.trade_history || []);
      }
    } catch (error) {
      console.error('❌ Failed to update account info:', error);
    }
  };

  // Execute a real trade
  const executeRealTrade = async (symbol, action, volume = 0.01) => {
    if (!isConnected) {
      alert('Not connected to MT5. Please connect first.');
      return;
    }

    try {
      console.log(`🚀 Executing REAL trade: ${action} ${symbol} ${volume} lots`);
      
      const response = await fetch('/api/mt5/web-connector', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'EXECUTE_TRADE',
          data: {
            symbol: symbol,
            action: action,
            volume: volume,
            stop_loss: null,
            take_profit: null
          }
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        setLastTrade(result.trade);
        setAccountInfo(result.account);
        console.log('✅ Real trade executed:', result.trade);
        
        // Update account info
        await updateAccountInfo();
      } else {
        console.error('❌ Trade execution failed:', result.error);
      }
    } catch (error) {
      console.error('❌ Trade error:', error);
    }
  };

  // Start automated trading
  const startAutomatedTrading = async () => {
    if (!isConnected) {
      alert('Please connect to MT5 first');
      return;
    }

    setIsTrading(true);
    console.log('🤖 Starting automated REAL trading...');
    
    // Execute trades every 30 seconds
    const tradingInterval = setInterval(async () => {
      if (!isTrading) {
        clearInterval(tradingInterval);
        return;
      }

      const symbols = ['EURUSD', 'GBPUSD', 'USDJPY'];
      const actions = ['BUY', 'SELL'];
      
      const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)];
      const randomAction = actions[Math.floor(Math.random() * actions.length)];
      const volume = 0.01; // Small volume for demo
      
      await executeRealTrade(randomSymbol, randomAction, volume);
    }, 30000); // Every 30 seconds
  };

  // Stop automated trading
  const stopAutomatedTrading = () => {
    setIsTrading(false);
    console.log('🛑 Stopped automated trading');
  };

  // Auto-connect on component mount
  useEffect(() => {
    connectToMT5();
  }, []);

  // Update account info every 10 seconds
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(updateAccountInfo, 10000);
      return () => clearInterval(interval);
    }
  }, [isConnected]);

  const getStatusColor = () => {
    switch (status) {
      case 'connected': return 'border-green-500 bg-green-500/10';
      case 'connecting': return 'border-yellow-500 bg-yellow-500/10';
      case 'error': return 'border-red-500 bg-red-500/10';
      default: return 'border-gray-500 bg-gray-500/10';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected': return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
      case 'connecting': return <div className="w-5 h-5 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin" />;
      case 'error': return <XCircleIcon className="w-5 h-5 text-red-400" />;
      default: return <ExclamationTriangleIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-lg border backdrop-blur-sm ${getStatusColor()}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h3 className="text-xl font-bold text-white">
              🎯 Real Trading Panel
            </h3>
            <p className="text-sm text-gray-400">
              Execute actual trades on MT5 demo account
            </p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {!isConnected && (
            <button
              onClick={connectToMT5}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium"
            >
              Connect MT5
            </button>
          )}
          
          {isConnected && !isTrading && (
            <button
              onClick={startAutomatedTrading}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium flex items-center space-x-2"
            >
              <PlayIcon className="w-4 h-4" />
              <span>Start Real Trading</span>
            </button>
          )}
          
          {isTrading && (
            <button
              onClick={stopAutomatedTrading}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium flex items-center space-x-2"
            >
              <StopIcon className="w-4 h-4" />
              <span>Stop Trading</span>
            </button>
          )}
        </div>
      </div>

      {/* Account Info */}
      {accountInfo && (
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              ${accountInfo.balance?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-400">Balance</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              ${accountInfo.equity?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-400">Equity</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              (accountInfo.profit || 0) >= 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              {(accountInfo.profit || 0) >= 0 ? '+' : ''}${accountInfo.profit?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-400">Profit</div>
          </div>
        </div>
      )}

      {/* Manual Trading Buttons */}
      {isConnected && (
        <div className="grid grid-cols-2 gap-4 mb-6">
          <button
            onClick={() => executeRealTrade('EURUSD', 'BUY', 0.01)}
            className="p-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium"
          >
            🟢 BUY EURUSD
          </button>
          <button
            onClick={() => executeRealTrade('EURUSD', 'SELL', 0.01)}
            className="p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium"
          >
            🔴 SELL EURUSD
          </button>
        </div>
      )}

      {/* Last Trade */}
      {lastTrade && (
        <div className="p-4 bg-gray-800/50 rounded-lg mb-4">
          <h4 className="text-sm font-medium text-white mb-2">Last Trade Executed:</h4>
          <div className="text-sm text-gray-300">
            <div>🎫 Ticket: {lastTrade.ticket}</div>
            <div>📈 {lastTrade.action} {lastTrade.symbol} {lastTrade.volume} lots</div>
            <div>💰 Price: {lastTrade.open_price}</div>
            <div>⏰ Time: {new Date(lastTrade.open_time).toLocaleTimeString()}</div>
          </div>
        </div>
      )}

      {/* Status */}
      <div className="text-center">
        <div className={`text-sm font-medium ${
          isConnected ? 'text-green-400' : 'text-gray-400'
        }`}>
          {isConnected ? 
            (isTrading ? '🤖 AUTOMATED TRADING ACTIVE' : '✅ CONNECTED - Ready for Trading') : 
            '❌ DISCONNECTED'
          }
        </div>
      </div>
    </motion.div>
  );
}
