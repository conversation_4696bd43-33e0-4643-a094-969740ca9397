/**
 * Unit tests for BlockchainVerifier
 */

const { expect } = require('chai');
const BlockchainVerifier = require('../../core/BlockchainVerifier');

describe('BlockchainVerifier', () => {
  let verifier;
  
  beforeEach(() => {
    verifier = new BlockchainVerifier({
      network: 'test',
      verificationMethod: 'merkle',
      hashAlgorithm: 'sha256'
    });
  });
  
  describe('generateHash', () => {
    it('should generate a hash for a string', () => {
      const data = 'test data';
      const hash = verifier.generateHash(data);
      
      expect(hash).to.be.a('string');
      expect(hash).to.have.lengthOf(64); // SHA-256 hash length
    });
    
    it('should generate a hash for an object', () => {
      const data = { test: 'data', number: 123 };
      const hash = verifier.generateHash(data);
      
      expect(hash).to.be.a('string');
      expect(hash).to.have.lengthOf(64); // SHA-256 hash length
    });
    
    it('should generate different hashes for different data', () => {
      const data1 = 'test data 1';
      const data2 = 'test data 2';
      
      const hash1 = verifier.generateHash(data1);
      const hash2 = verifier.generateHash(data2);
      
      expect(hash1).to.not.equal(hash2);
    });
    
    it('should generate the same hash for the same data', () => {
      const data = 'test data';
      
      const hash1 = verifier.generateHash(data);
      const hash2 = verifier.generateHash(data);
      
      expect(hash1).to.equal(hash2);
    });
  });
  
  describe('createVerification', () => {
    it('should create a verification record', async () => {
      const data = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+'
      };
      
      const verification = await verifier.createVerification(data);
      
      expect(verification).to.be.an('object');
      expect(verification.verificationId).to.be.a('string');
      expect(verification.contentHash).to.be.a('string');
      expect(verification.timestamp).to.be.a('string');
      expect(verification.status).to.equal('VERIFIED');
      expect(verification.network).to.equal('test');
      expect(verification.verificationMethod).to.equal('merkle');
      expect(verification.transactionId).to.be.a('string');
      expect(verification.transactionId).to.include('tx-');
      expect(verification.metadata).to.be.an('object');
      expect(verification.metadata.dataType).to.equal('MEDICAL_EMERGENCY');
    });
    
    it('should include custom metadata', async () => {
      const data = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+'
      };
      
      const metadata = {
        source: 'unit-test',
        priority: 'high'
      };
      
      const verification = await verifier.createVerification(data, metadata);
      
      expect(verification.metadata).to.include(metadata);
      expect(verification.metadata.dataType).to.equal('MEDICAL_EMERGENCY');
      expect(verification.metadata.source).to.equal('unit-test');
      expect(verification.metadata.priority).to.equal('high');
    });
  });
  
  describe('verifyData', () => {
    it('should verify data against a previous verification', async () => {
      const data = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+'
      };
      
      const verification = await verifier.createVerification(data);
      const result = await verifier.verifyData(data, verification.verificationId);
      
      expect(result).to.be.an('object');
      expect(result.verified).to.be.true;
      expect(result.verification).to.deep.equal(verification);
      expect(result.timestamp).to.be.a('string');
    });
    
    it('should fail verification for modified data', async () => {
      const originalData = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+'
      };
      
      const modifiedData = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'B-' // Changed blood type
      };
      
      const verification = await verifier.createVerification(originalData);
      const result = await verifier.verifyData(modifiedData, verification.verificationId);
      
      expect(result).to.be.an('object');
      expect(result.verified).to.be.false;
      expect(result.error).to.equal('Content hash mismatch');
      expect(result.expectedHash).to.equal(verification.contentHash);
      expect(result.actualHash).to.equal(verifier.generateHash(modifiedData));
    });
    
    it('should fail verification for non-existent verification ID', async () => {
      const data = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+'
      };
      
      const result = await verifier.verifyData(data, 'non-existent-id');
      
      expect(result).to.be.an('object');
      expect(result.verified).to.be.false;
      expect(result.error).to.equal('Verification record not found');
    });
  });
  
  describe('getVerificationStatus', () => {
    it('should return the status of a verification', async () => {
      const data = {
        name: 'John Doe',
        dateOfBirth: '1980-01-01',
        bloodType: 'A+'
      };
      
      const verification = await verifier.createVerification(data);
      const status = verifier.getVerificationStatus(verification.verificationId);
      
      expect(status).to.be.an('object');
      expect(status.found).to.be.true;
      expect(status.status).to.equal('VERIFIED');
      expect(status.timestamp).to.equal(verification.timestamp);
      expect(status.network).to.equal('test');
      expect(status.transactionId).to.equal(verification.transactionId);
    });
    
    it('should return not found for non-existent verification ID', () => {
      const status = verifier.getVerificationStatus('non-existent-id');
      
      expect(status).to.be.an('object');
      expect(status.found).to.be.false;
      expect(status.error).to.equal('Verification record not found');
    });
  });
});
