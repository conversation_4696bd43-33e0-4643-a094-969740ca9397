/**
 * @swagger
 * components:
 *   schemas:
 *     Disclosure:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the disclosure
 *         title:
 *           type: string
 *           description: Title of the disclosure
 *         description:
 *           type: string
 *           description: Description of the disclosure
 *         regulationType:
 *           type: string
 *           description: Type of regulation (e.g., mandatory, voluntary)
 *         regulationName:
 *           type: string
 *           description: Name of the regulation
 *         jurisdiction:
 *           type: string
 *           description: Jurisdiction where the regulation applies
 *         applicabilityDate:
 *           type: string
 *           format: date
 *           description: Date when the regulation becomes applicable
 *         reportingFrequency:
 *           type: string
 *           enum: [annual, semi-annual, quarterly, one-time]
 *           description: Frequency of reporting
 *         nextDueDate:
 *           type: string
 *           format: date
 *           description: Next due date for the disclosure
 *         status:
 *           type: string
 *           enum: [not-started, in-progress, submitted, approved, rejected]
 *           description: Status of the disclosure
 *         assignedTo:
 *           type: string
 *           description: Person or team assigned to the disclosure
 *         frameworkIds:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of associated ESG frameworks
 *         metricIds:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of associated ESG metrics
 *         documents:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *               url:
 *                 type: string
 *               uploadedBy:
 *                 type: string
 *               uploadedAt:
 *                 type: string
 *                 format: date-time
 *           description: Documents related to the disclosure
 *         submissionHistory:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: string
 *               submittedBy:
 *                 type: string
 *               notes:
 *                 type: string
 *           description: History of submissions
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the disclosure was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the disclosure was last updated
 *       required:
 *         - id
 *         - title
 *         - regulationType
 *         - regulationName
 *         - jurisdiction
 *         - reportingFrequency
 *         - status
 *         - createdAt
 *         - updatedAt
 *     
 *     Regulation:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the regulation
 *         name:
 *           type: string
 *           description: Name of the regulation
 *         description:
 *           type: string
 *           description: Description of the regulation
 *         type:
 *           type: string
 *           enum: [mandatory, voluntary]
 *           description: Type of regulation
 *         category:
 *           type: string
 *           enum: [environmental, social, governance, general]
 *           description: Category of the regulation
 *         jurisdiction:
 *           type: string
 *           description: Jurisdiction where the regulation applies
 *         issuingAuthority:
 *           type: string
 *           description: Authority that issued the regulation
 *         effectiveDate:
 *           type: string
 *           format: date
 *           description: Date when the regulation became effective
 *         reportingFrequency:
 *           type: string
 *           enum: [annual, semi-annual, quarterly, one-time]
 *           description: Frequency of reporting
 *         website:
 *           type: string
 *           description: Official website for the regulation
 *         requirements:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *           description: Requirements of the regulation
 *         frameworkIds:
 *           type: array
 *           items:
 *             type: string
 *           description: IDs of associated ESG frameworks
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the regulation was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the regulation was last updated
 *       required:
 *         - id
 *         - name
 *         - type
 *         - jurisdiction
 *         - issuingAuthority
 *         - effectiveDate
 *         - createdAt
 *         - updatedAt
 */

// Sample disclosures
const disclosures = [
  {
    id: 'disc-001',
    title: 'TCFD Climate Disclosure 2023',
    description: 'Annual climate-related financial disclosure following TCFD recommendations',
    regulationType: 'mandatory',
    regulationName: 'TCFD Reporting Requirements',
    jurisdiction: 'Global',
    applicabilityDate: '2023-01-01',
    reportingFrequency: 'annual',
    nextDueDate: '2024-03-31',
    status: 'in-progress',
    assignedTo: 'Sustainability Team',
    frameworkIds: ['frm-003'],
    metricIds: ['met-001', 'met-002', 'met-003'],
    documents: [
      {
        id: 'doc-001',
        name: 'TCFD Draft Report 2023',
        type: 'pdf',
        url: '/documents/tcfd-draft-2023.pdf',
        uploadedBy: 'user-123',
        uploadedAt: '2023-12-15T10:30:00Z'
      },
      {
        id: 'doc-002',
        name: 'Climate Risk Assessment',
        type: 'xlsx',
        url: '/documents/climate-risk-assessment.xlsx',
        uploadedBy: 'user-456',
        uploadedAt: '2023-11-20T14:45:00Z'
      }
    ],
    submissionHistory: [
      {
        id: 'sub-001',
        date: '2023-03-25',
        status: 'submitted',
        submittedBy: 'user-123',
        notes: 'Submitted 2022 TCFD report'
      }
    ],
    createdAt: '2023-01-10T09:00:00Z',
    updatedAt: '2023-12-15T10:30:00Z'
  },
  {
    id: 'disc-002',
    title: 'EU CSRD Disclosure 2023',
    description: 'Corporate Sustainability Reporting Directive disclosure for EU operations',
    regulationType: 'mandatory',
    regulationName: 'Corporate Sustainability Reporting Directive',
    jurisdiction: 'European Union',
    applicabilityDate: '2023-01-01',
    reportingFrequency: 'annual',
    nextDueDate: '2024-04-30',
    status: 'not-started',
    assignedTo: 'EU Compliance Team',
    frameworkIds: ['frm-001', 'frm-002'],
    metricIds: ['met-001', 'met-004', 'met-005', 'met-006'],
    documents: [],
    submissionHistory: [],
    createdAt: '2023-02-15T11:30:00Z',
    updatedAt: '2023-02-15T11:30:00Z'
  },
  {
    id: 'disc-003',
    title: 'CDP Climate Change Response 2023',
    description: 'Annual CDP Climate Change questionnaire response',
    regulationType: 'voluntary',
    regulationName: 'CDP Climate Change',
    jurisdiction: 'Global',
    applicabilityDate: '2023-01-01',
    reportingFrequency: 'annual',
    nextDueDate: '2023-07-31',
    status: 'submitted',
    assignedTo: 'Environmental Team',
    frameworkIds: ['frm-004'],
    metricIds: ['met-001', 'met-002', 'met-003'],
    documents: [
      {
        id: 'doc-003',
        name: 'CDP Submission 2023',
        type: 'pdf',
        url: '/documents/cdp-submission-2023.pdf',
        uploadedBy: 'user-789',
        uploadedAt: '2023-07-25T16:20:00Z'
      }
    ],
    submissionHistory: [
      {
        id: 'sub-002',
        date: '2023-07-25',
        status: 'submitted',
        submittedBy: 'user-789',
        notes: 'Submitted 2023 CDP Climate Change response'
      }
    ],
    createdAt: '2023-03-01T13:45:00Z',
    updatedAt: '2023-07-25T16:20:00Z'
  }
];

// Sample regulations
const regulations = [
  {
    id: 'reg-001',
    name: 'TCFD Reporting Requirements',
    description: 'Task Force on Climate-related Financial Disclosures recommendations for climate-related financial disclosures',
    type: 'mandatory',
    category: 'environmental',
    jurisdiction: 'Global',
    issuingAuthority: 'Financial Stability Board',
    effectiveDate: '2017-06-29',
    reportingFrequency: 'annual',
    website: 'https://www.fsb-tcfd.org',
    requirements: [
      {
        id: 'req-001',
        name: 'Governance',
        description: 'Disclose the organization's governance around climate-related risks and opportunities'
      },
      {
        id: 'req-002',
        name: 'Strategy',
        description: 'Disclose the actual and potential impacts of climate-related risks and opportunities on the organization's businesses, strategy, and financial planning'
      },
      {
        id: 'req-003',
        name: 'Risk Management',
        description: 'Disclose how the organization identifies, assesses, and manages climate-related risks'
      },
      {
        id: 'req-004',
        name: 'Metrics and Targets',
        description: 'Disclose the metrics and targets used to assess and manage relevant climate-related risks and opportunities'
      }
    ],
    frameworkIds: ['frm-003'],
    createdAt: '2022-01-15T10:00:00Z',
    updatedAt: '2022-01-15T10:00:00Z'
  },
  {
    id: 'reg-002',
    name: 'Corporate Sustainability Reporting Directive',
    description: 'EU directive requiring large companies to report on environmental, social and governance issues',
    type: 'mandatory',
    category: 'general',
    jurisdiction: 'European Union',
    issuingAuthority: 'European Commission',
    effectiveDate: '2023-01-01',
    reportingFrequency: 'annual',
    website: 'https://ec.europa.eu/info/business-economy-euro/company-reporting-and-auditing/company-reporting/corporate-sustainability-reporting_en',
    requirements: [
      {
        id: 'req-005',
        name: 'Double Materiality',
        description: 'Report on both how sustainability issues affect the company and how the company impacts society and the environment'
      },
      {
        id: 'req-006',
        name: 'Mandatory Standards',
        description: 'Report according to mandatory European sustainability reporting standards'
      },
      {
        id: 'req-007',
        name: 'Audit Requirement',
        description: 'Obtain limited assurance on sustainability information'
      }
    ],
    frameworkIds: ['frm-001', 'frm-002'],
    createdAt: '2022-02-10T11:30:00Z',
    updatedAt: '2022-02-10T11:30:00Z'
  },
  {
    id: 'reg-003',
    name: 'CDP Climate Change',
    description: 'Annual questionnaire on climate change management, risks, opportunities, and performance',
    type: 'voluntary',
    category: 'environmental',
    jurisdiction: 'Global',
    issuingAuthority: 'CDP',
    effectiveDate: '2003-01-01',
    reportingFrequency: 'annual',
    website: 'https://www.cdp.net',
    requirements: [
      {
        id: 'req-008',
        name: 'Governance',
        description: 'Disclose governance structure for climate-related issues'
      },
      {
        id: 'req-009',
        name: 'Risks and Opportunities',
        description: 'Identify and assess climate-related risks and opportunities'
      },
      {
        id: 'req-010',
        name: 'Strategy',
        description: 'Disclose business strategy related to climate change'
      },
      {
        id: 'req-011',
        name: 'Targets and Performance',
        description: 'Report on emissions targets and performance'
      },
      {
        id: 'req-012',
        name: 'Emissions Methodology',
        description: 'Disclose methodology for calculating emissions'
      }
    ],
    frameworkIds: ['frm-004'],
    createdAt: '2022-03-05T14:15:00Z',
    updatedAt: '2022-03-05T14:15:00Z'
  }
];

module.exports = {
  disclosures,
  regulations
};
