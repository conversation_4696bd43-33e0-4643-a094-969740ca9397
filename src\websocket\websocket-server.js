/**
 * WebSocket Server
 * 
 * This module provides a WebSocket server for real-time communication between components.
 */

const WebSocket = require('ws');
const http = require('http');
const url = require('url');
const { EventEmitter } = require('events');
const { performance } = require('perf_hooks');

/**
 * WebSocketServer class
 */
class WebSocketServer extends EventEmitter {
  /**
   * Create a new WebSocketServer instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      port: options.port || process.env.WS_PORT || 3001,
      path: options.path || '/ws',
      enableLogging: options.enableLogging !== undefined ? options.enableLogging : true,
      enableMetrics: options.enableMetrics !== undefined ? options.enableMetrics : true,
      heartbeatInterval: options.heartbeatInterval || 30000, // 30 seconds
      maxPayloadSize: options.maxPayloadSize || 1024 * 1024, // 1MB
      ...options
    };
    
    // Initialize state
    this.state = {
      isRunning: false,
      clients: new Map(), // clientId -> { ws, metadata }
      channels: new Map(), // channelName -> Set of clientIds
      lastMessageId: 0
    };
    
    // Initialize metrics
    this.metrics = {
      connectionsTotal: 0,
      connectionsActive: 0,
      messagesReceived: 0,
      messagesSent: 0,
      bytesReceived: 0,
      bytesSent: 0,
      errors: 0,
      lastMessageTime: 0,
      processingTimeMs: 0
    };
    
    // Bind methods
    this._handleConnection = this._handleConnection.bind(this);
    this._handleMessage = this._handleMessage.bind(this);
    this._handleClose = this._handleClose.bind(this);
    this._handleError = this._handleError.bind(this);
    this._sendHeartbeat = this._sendHeartbeat.bind(this);
    
    if (this.options.enableLogging) {
      console.log('WebSocketServer initialized');
    }
  }
  
  /**
   * Start the WebSocket server
   * @returns {Promise<WebSocketServer>} - Promise that resolves with the server instance
   */
  async start() {
    if (this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('WebSocketServer is already running');
      }
      return this;
    }
    
    // Create HTTP server
    this.httpServer = http.createServer((req, res) => {
      const parsedUrl = url.parse(req.url, true);
      
      if (parsedUrl.pathname === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          status: 'ok',
          clients: this.state.clients.size,
          channels: this.state.channels.size,
          uptime: process.uptime()
        }));
        return;
      }
      
      res.writeHead(404);
      res.end();
    });
    
    // Create WebSocket server
    this.wss = new WebSocket.Server({
      server: this.httpServer,
      path: this.options.path,
      maxPayload: this.options.maxPayloadSize
    });
    
    // Set up event handlers
    this.wss.on('connection', this._handleConnection);
    
    // Start HTTP server
    await new Promise((resolve, reject) => {
      this.httpServer.listen(this.options.port, (err) => {
        if (err) {
          reject(err);
          return;
        }
        
        this.state.isRunning = true;
        
        if (this.options.enableLogging) {
          console.log(`WebSocketServer listening on port ${this.options.port}`);
        }
        
        resolve();
      });
    });
    
    // Start heartbeat interval
    this.heartbeatInterval = setInterval(this._sendHeartbeat, this.options.heartbeatInterval);
    
    return this;
  }
  
  /**
   * Stop the WebSocket server
   * @returns {Promise<void>} - Promise that resolves when the server is stopped
   */
  async stop() {
    if (!this.state.isRunning) {
      if (this.options.enableLogging) {
        console.log('WebSocketServer is not running');
      }
      return;
    }
    
    // Clear heartbeat interval
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    // Close all WebSocket connections
    for (const [clientId, client] of this.state.clients.entries()) {
      try {
        client.ws.close(1000, 'Server shutting down');
      } catch (error) {
        if (this.options.enableLogging) {
          console.error(`Error closing WebSocket connection for client ${clientId}:`, error);
        }
      }
    }
    
    // Clear state
    this.state.clients.clear();
    this.state.channels.clear();
    
    // Close WebSocket server
    if (this.wss) {
      await new Promise((resolve) => {
        this.wss.close(() => {
          this.wss = null;
          resolve();
        });
      });
    }
    
    // Close HTTP server
    if (this.httpServer) {
      await new Promise((resolve) => {
        this.httpServer.close(() => {
          this.httpServer = null;
          resolve();
        });
      });
    }
    
    this.state.isRunning = false;
    
    if (this.options.enableLogging) {
      console.log('WebSocketServer stopped');
    }
  }
  
  /**
   * Handle new WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {http.IncomingMessage} req - HTTP request
   * @private
   */
  _handleConnection(ws, req) {
    const startTime = performance.now();
    
    // Parse URL parameters
    const parsedUrl = url.parse(req.url, true);
    const clientId = parsedUrl.query.clientId || `client-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    // Store client
    this.state.clients.set(clientId, {
      ws,
      metadata: {
        id: clientId,
        ip: req.socket.remoteAddress,
        userAgent: req.headers['user-agent'],
        connectedAt: Date.now()
      },
      channels: new Set()
    });
    
    // Set up event handlers
    ws.on('message', (message) => this._handleMessage(clientId, message));
    ws.on('close', (code, reason) => this._handleClose(clientId, code, reason));
    ws.on('error', (error) => this._handleError(clientId, error));
    
    // Update metrics
    this.metrics.connectionsTotal++;
    this.metrics.connectionsActive = this.state.clients.size;
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Send welcome message
    this._sendToClient(clientId, {
      type: 'welcome',
      clientId,
      timestamp: Date.now()
    });
    
    // Emit event
    this.emit('client-connected', {
      clientId,
      ip: req.socket.remoteAddress,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`WebSocketServer: Client connected - ${clientId}`);
    }
  }
  
  /**
   * Handle WebSocket message
   * @param {string} clientId - Client ID
   * @param {string|Buffer} message - Message data
   * @private
   */
  _handleMessage(clientId, message) {
    const startTime = performance.now();
    
    // Update metrics
    this.metrics.messagesReceived++;
    this.metrics.bytesReceived += message.length;
    this.metrics.lastMessageTime = Date.now();
    
    // Parse message
    let parsedMessage;
    try {
      parsedMessage = JSON.parse(message);
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`WebSocketServer: Invalid JSON message from client ${clientId}:`, error);
      }
      
      this._sendToClient(clientId, {
        type: 'error',
        error: 'Invalid JSON message',
        timestamp: Date.now()
      });
      
      this.metrics.errors++;
      return;
    }
    
    // Handle message based on type
    switch (parsedMessage.type) {
      case 'ping':
        this._handlePingMessage(clientId, parsedMessage);
        break;
        
      case 'subscribe':
        this._handleSubscribeMessage(clientId, parsedMessage);
        break;
        
      case 'unsubscribe':
        this._handleUnsubscribeMessage(clientId, parsedMessage);
        break;
        
      case 'publish':
        this._handlePublishMessage(clientId, parsedMessage);
        break;
        
      default:
        // Emit message event for custom handling
        this.emit('message', {
          clientId,
          message: parsedMessage,
          timestamp: Date.now()
        });
    }
    
    // Update metrics
    this.metrics.processingTimeMs += performance.now() - startTime;
  }
  
  /**
   * Handle ping message
   * @param {string} clientId - Client ID
   * @param {Object} message - Message data
   * @private
   */
  _handlePingMessage(clientId, message) {
    this._sendToClient(clientId, {
      type: 'pong',
      id: message.id,
      timestamp: Date.now()
    });
  }
  
  /**
   * Handle subscribe message
   * @param {string} clientId - Client ID
   * @param {Object} message - Message data
   * @private
   */
  _handleSubscribeMessage(clientId, message) {
    const channel = message.channel;
    
    if (!channel) {
      this._sendToClient(clientId, {
        type: 'error',
        error: 'Channel is required for subscribe',
        id: message.id,
        timestamp: Date.now()
      });
      
      this.metrics.errors++;
      return;
    }
    
    // Get client
    const client = this.state.clients.get(clientId);
    
    if (!client) {
      return;
    }
    
    // Add client to channel
    if (!this.state.channels.has(channel)) {
      this.state.channels.set(channel, new Set());
    }
    
    this.state.channels.get(channel).add(clientId);
    client.channels.add(channel);
    
    // Send confirmation
    this._sendToClient(clientId, {
      type: 'subscribed',
      channel,
      id: message.id,
      timestamp: Date.now()
    });
    
    // Emit event
    this.emit('client-subscribed', {
      clientId,
      channel,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`WebSocketServer: Client ${clientId} subscribed to channel ${channel}`);
    }
  }
  
  /**
   * Handle unsubscribe message
   * @param {string} clientId - Client ID
   * @param {Object} message - Message data
   * @private
   */
  _handleUnsubscribeMessage(clientId, message) {
    const channel = message.channel;
    
    if (!channel) {
      this._sendToClient(clientId, {
        type: 'error',
        error: 'Channel is required for unsubscribe',
        id: message.id,
        timestamp: Date.now()
      });
      
      this.metrics.errors++;
      return;
    }
    
    // Get client
    const client = this.state.clients.get(clientId);
    
    if (!client) {
      return;
    }
    
    // Remove client from channel
    if (this.state.channels.has(channel)) {
      this.state.channels.get(channel).delete(clientId);
      
      // Remove channel if empty
      if (this.state.channels.get(channel).size === 0) {
        this.state.channels.delete(channel);
      }
    }
    
    client.channels.delete(channel);
    
    // Send confirmation
    this._sendToClient(clientId, {
      type: 'unsubscribed',
      channel,
      id: message.id,
      timestamp: Date.now()
    });
    
    // Emit event
    this.emit('client-unsubscribed', {
      clientId,
      channel,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`WebSocketServer: Client ${clientId} unsubscribed from channel ${channel}`);
    }
  }
  
  /**
   * Handle publish message
   * @param {string} clientId - Client ID
   * @param {Object} message - Message data
   * @private
   */
  _handlePublishMessage(clientId, message) {
    const channel = message.channel;
    const data = message.data;
    
    if (!channel) {
      this._sendToClient(clientId, {
        type: 'error',
        error: 'Channel is required for publish',
        id: message.id,
        timestamp: Date.now()
      });
      
      this.metrics.errors++;
      return;
    }
    
    // Publish message to channel
    this.publish(channel, data, clientId);
    
    // Send confirmation
    this._sendToClient(clientId, {
      type: 'published',
      channel,
      id: message.id,
      timestamp: Date.now()
    });
  }
  
  /**
   * Handle WebSocket close
   * @param {string} clientId - Client ID
   * @param {number} code - Close code
   * @param {string} reason - Close reason
   * @private
   */
  _handleClose(clientId, code, reason) {
    // Get client
    const client = this.state.clients.get(clientId);
    
    if (!client) {
      return;
    }
    
    // Remove client from channels
    for (const channel of client.channels) {
      if (this.state.channels.has(channel)) {
        this.state.channels.get(channel).delete(clientId);
        
        // Remove channel if empty
        if (this.state.channels.get(channel).size === 0) {
          this.state.channels.delete(channel);
        }
      }
    }
    
    // Remove client
    this.state.clients.delete(clientId);
    
    // Update metrics
    this.metrics.connectionsActive = this.state.clients.size;
    
    // Emit event
    this.emit('client-disconnected', {
      clientId,
      code,
      reason,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`WebSocketServer: Client disconnected - ${clientId} (${code}: ${reason})`);
    }
  }
  
  /**
   * Handle WebSocket error
   * @param {string} clientId - Client ID
   * @param {Error} error - Error object
   * @private
   */
  _handleError(clientId, error) {
    // Update metrics
    this.metrics.errors++;
    
    // Emit event
    this.emit('client-error', {
      clientId,
      error,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.error(`WebSocketServer: Client error - ${clientId}:`, error);
    }
  }
  
  /**
   * Send heartbeat to all clients
   * @private
   */
  _sendHeartbeat() {
    const message = {
      type: 'heartbeat',
      timestamp: Date.now()
    };
    
    for (const [clientId, client] of this.state.clients.entries()) {
      if (client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify(message));
          
          // Update metrics
          this.metrics.messagesSent++;
          this.metrics.bytesSent += JSON.stringify(message).length;
        } catch (error) {
          if (this.options.enableLogging) {
            console.error(`WebSocketServer: Error sending heartbeat to client ${clientId}:`, error);
          }
          
          this.metrics.errors++;
        }
      }
    }
  }
  
  /**
   * Send message to a client
   * @param {string} clientId - Client ID
   * @param {Object} message - Message to send
   * @returns {boolean} - Success status
   * @private
   */
  _sendToClient(clientId, message) {
    // Get client
    const client = this.state.clients.get(clientId);
    
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }
    
    // Add message ID if not provided
    if (!message.id) {
      message.id = ++this.state.lastMessageId;
    }
    
    // Send message
    try {
      const messageStr = JSON.stringify(message);
      client.ws.send(messageStr);
      
      // Update metrics
      this.metrics.messagesSent++;
      this.metrics.bytesSent += messageStr.length;
      
      return true;
    } catch (error) {
      if (this.options.enableLogging) {
        console.error(`WebSocketServer: Error sending message to client ${clientId}:`, error);
      }
      
      this.metrics.errors++;
      return false;
    }
  }
  
  /**
   * Publish a message to a channel
   * @param {string} channel - Channel name
   * @param {*} data - Message data
   * @param {string} [excludeClientId] - Client ID to exclude
   * @returns {number} - Number of clients the message was sent to
   */
  publish(channel, data, excludeClientId) {
    // Check if channel exists
    if (!this.state.channels.has(channel)) {
      return 0;
    }
    
    // Create message
    const message = {
      type: 'message',
      channel,
      data,
      id: ++this.state.lastMessageId,
      timestamp: Date.now()
    };
    
    // Send message to all clients in channel
    let sentCount = 0;
    
    for (const clientId of this.state.channels.get(channel)) {
      if (clientId !== excludeClientId) {
        if (this._sendToClient(clientId, message)) {
          sentCount++;
        }
      }
    }
    
    // Emit event
    this.emit('message-published', {
      channel,
      data,
      sentCount,
      timestamp: Date.now()
    });
    
    return sentCount;
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Get client count
   * @returns {number} - Number of connected clients
   */
  getClientCount() {
    return this.state.clients.size;
  }
  
  /**
   * Get channel count
   * @returns {number} - Number of active channels
   */
  getChannelCount() {
    return this.state.channels.size;
  }
  
  /**
   * Get clients in a channel
   * @param {string} channel - Channel name
   * @returns {Array} - Array of client IDs
   */
  getClientsInChannel(channel) {
    if (!this.state.channels.has(channel)) {
      return [];
    }
    
    return Array.from(this.state.channels.get(channel));
  }
  
  /**
   * Get channels for a client
   * @param {string} clientId - Client ID
   * @returns {Array} - Array of channel names
   */
  getChannelsForClient(clientId) {
    const client = this.state.clients.get(clientId);
    
    if (!client) {
      return [];
    }
    
    return Array.from(client.channels);
  }
}

module.exports = WebSocketServer;
