/**
 * Universal Ripple Stack Example
 * 
 * This example demonstrates the Universal Ripple Stack with minimal components.
 */

const { UniversalRippleStack } = require('../universal_ripple');
const { QuantumStateInferenceLayer } = require('../quantum_inference');

/**
 * Mock NovaConnect
 */
class MockNovaConnect {
  constructor() {
    this.topics = new Map();
    this.subscribers = new Map();
    console.log('MockNovaConnect initialized');
  }
  
  async publish(topic, message) {
    console.log(`[NovaConnect] Publishing to topic: ${topic}`);
    
    // Store message
    if (!this.topics.has(topic)) {
      this.topics.set(topic, []);
    }
    
    this.topics.get(topic).push({
      message,
      timestamp: new Date()
    });
    
    // Notify subscribers
    if (this.subscribers.has(topic)) {
      for (const callback of this.subscribers.get(topic)) {
        try {
          callback(message, topic);
        } catch (error) {
          console.error(`Error notifying subscriber for topic ${topic}:`, error);
        }
      }
    }
    
    return Promise.resolve();
  }
  
  async subscribe(topic, callback) {
    console.log(`[NovaConnect] Subscribing to topic: ${topic}`);
    
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, []);
    }
    
    this.subscribers.get(topic).push(callback);
    
    return Promise.resolve();
  }
  
  async unsubscribe(topic, callback) {
    console.log(`[NovaConnect] Unsubscribing from topic: ${topic}`);
    
    if (this.subscribers.has(topic)) {
      if (callback) {
        const index = this.subscribers.get(topic).indexOf(callback);
        
        if (index !== -1) {
          this.subscribers.get(topic).splice(index, 1);
        }
      } else {
        this.subscribers.delete(topic);
      }
    }
    
    return Promise.resolve();
  }
}

/**
 * Mock NovaThink
 */
class MockNovaThink {
  constructor() {
    this.decisions = [];
    this.beforeDecisionCallbacks = [];
    this.afterDecisionCallbacks = [];
    console.log('MockNovaThink initialized');
  }
  
  makeDecision(context) {
    console.log(`[NovaThink] Making decision with context:`, context);
    
    // Call before decision callbacks
    let enhancedContext = { ...context };
    
    for (const callback of this.beforeDecisionCallbacks) {
      enhancedContext = callback(enhancedContext);
    }
    
    // Make decision
    const decision = {
      id: Math.random().toString(36).substring(2, 15),
      context: enhancedContext,
      result: {
        action: 'approve',
        confidence: 0.85,
        reasoning: 'Decision based on context analysis'
      },
      fairness: 0.9,
      transparency: 0.8,
      ethicalTensor: 0.75,
      accountability: 0.85,
      timestamp: new Date()
    };
    
    // Store decision
    this.decisions.push(decision);
    
    // Call after decision callbacks
    let enhancedDecision = { ...decision };
    
    for (const callback of this.afterDecisionCallbacks) {
      enhancedDecision = callback(enhancedDecision);
    }
    
    return enhancedDecision;
  }
  
  onBeforeDecision(callback) {
    this.beforeDecisionCallbacks.push(callback);
  }
  
  offBeforeDecision(callback) {
    const index = this.beforeDecisionCallbacks.indexOf(callback);
    
    if (index !== -1) {
      this.beforeDecisionCallbacks.splice(index, 1);
    }
  }
  
  onAfterDecision(callback) {
    this.afterDecisionCallbacks.push(callback);
  }
  
  offAfterDecision(callback) {
    const index = this.afterDecisionCallbacks.indexOf(callback);
    
    if (index !== -1) {
      this.afterDecisionCallbacks.splice(index, 1);
    }
  }
}

/**
 * Run the example
 */
async function runExample() {
  console.log('=== Universal Ripple Stack Example ===');
  
  // Create mock components
  const novaConnect = new MockNovaConnect();
  const novaThink = new MockNovaThink();
  
  // Initialize Universal Ripple Stack
  console.log('Initializing Universal Ripple Stack...');
  const rippleStack = new UniversalRippleStack({
    novaConnect,
    novaThink,
    enableLogging: true,
    autoStart: false
  });
  
  // Start the stack
  console.log('Starting Universal Ripple Stack...');
  await rippleStack.start();
  
  // Register some data
  console.log('Registering data...');
  
  const threatData = {
    type: 'threat',
    entropy: 0.7,
    phase: Math.PI / 2,
    certainty: 0.8,
    direction: Math.PI / 4,
    magnitude: 0.6
  };
  
  const complianceData = {
    type: 'compliance',
    complexity: 0.6,
    adaptability: 0.7,
    resonance: 0.8,
    environmentalPressure: 0.5
  };
  
  const decisionData = {
    type: 'decision',
    fairness: 0.8,
    transparency: 0.7,
    ethicalTensor: 0.9,
    accountability: 0.8
  };
  
  rippleStack.registerData(threatData);
  rippleStack.registerData(complianceData);
  rippleStack.registerData(decisionData);
  
  // Make a decision
  console.log('Making a decision...');
  const decisionContext = {
    user: 'admin',
    action: 'approve',
    resource: 'sensitive-data',
    riskLevel: 'medium'
  };
  
  const decision = rippleStack.makeDecision(decisionContext);
  console.log('Decision result:', decision);
  
  // Publish a message
  console.log('Publishing a message...');
  await rippleStack.publish('test.topic', {
    message: 'Hello, Ripple Effect!',
    timestamp: new Date()
  });
  
  // Get metrics
  console.log('Getting metrics...');
  const metrics = rippleStack.getMetrics();
  console.log('Metrics:', JSON.stringify(metrics, null, 2));
  
  // Stop the stack
  console.log('Stopping Universal Ripple Stack...');
  await rippleStack.stop();
  
  console.log('Example completed successfully!');
}

// Run the example
runExample().catch(error => {
  console.error('Example failed:', error);
});
