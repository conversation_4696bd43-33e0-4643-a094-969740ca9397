# NovaConnect UAC Single-Tenant Architecture

This document describes the single-tenant architecture of NovaConnect UAC for Google Cloud Marketplace.

## Overview

NovaConnect UAC is designed as a single-tenant application, where each customer gets their own dedicated instance of the application with complete isolation from other customers. This architecture provides several benefits:

- **Security**: Complete isolation between tenants ensures that one tenant's data cannot be accessed by another tenant.
- **Performance**: Dedicated resources for each tenant ensure consistent performance.
- **Customization**: Each tenant can have their own configuration and customizations.
- **Compliance**: Easier to meet regulatory requirements with dedicated resources.

## Architecture Components

### 1. Tenant Isolation

Each tenant is isolated using the following mechanisms:

- **Kubernetes Namespaces**: Each tenant gets their own dedicated Kubernetes namespace (`tenant-{TENANT_ID}`).
- **Resource Quotas**: Each tenant has defined resource limits to prevent resource starvation.
- **Network Policies**: Network traffic is isolated between tenants using Kubernetes Network Policies.

### 2. Tenant-Specific Resources

Each tenant has their own dedicated resources:

- **Kubernetes Deployments**: Tenant-specific deployments with dedicated pods.
- **Kubernetes Services**: Tenant-specific services for API access.
- **Kubernetes ConfigMaps and Secrets**: Tenant-specific configuration and secrets.
- **Google Cloud Resources**:
  - **BigQuery Datasets**: Tenant-specific datasets for data storage.
  - **Cloud KMS Keys**: Tenant-specific encryption keys for data protection.
  - **Service Accounts**: Tenant-specific service accounts for access control.

### 3. Tenant Provisioning and Management

Tenant provisioning and management is handled through the following scripts:

- **provision-tenant.sh**: Creates a new tenant with all required resources.
- **deprovision-tenant.sh**: Removes a tenant and all associated resources.

## Tenant Provisioning Process

The tenant provisioning process includes the following steps:

1. **Create Namespace**: Create a dedicated Kubernetes namespace for the tenant.
2. **Create Resource Quota**: Define resource limits for the tenant.
3. **Create Network Policy**: Isolate network traffic for the tenant.
4. **Create Service Account**: Create a dedicated service account for the tenant.
5. **Create Encryption Key**: Create a dedicated encryption key for the tenant.
6. **Create BigQuery Dataset**: Create a dedicated BigQuery dataset for the tenant.
7. **Create Monitoring Dashboard**: Create a dedicated monitoring dashboard for the tenant.
8. **Deploy Application**: Deploy the NovaConnect UAC application for the tenant.

## Tenant Deprovisioning Process

The tenant deprovisioning process includes the following steps:

1. **Backup Tenant Data**: Backup tenant data to Google Cloud Storage.
2. **Delete Helm Release**: Remove the tenant's Helm release.
3. **Delete Namespace**: Remove the tenant's Kubernetes namespace.
4. **Delete Service Account**: Remove the tenant's service account.
5. **Delete Encryption Key**: Remove the tenant's encryption key.
6. **Delete BigQuery Dataset**: Remove the tenant's BigQuery dataset.
7. **Delete Monitoring Dashboard**: Remove the tenant's monitoring dashboard.

## Tenant-Specific Billing

Billing is handled on a per-tenant basis:

- **Usage Metrics**: Usage metrics are tracked per tenant.
- **Billing Reports**: Billing reports are generated per tenant.
- **Marketplace Integration**: Google Cloud Marketplace billing is integrated with tenant-specific usage reporting.

## Tenant-Specific Monitoring

Monitoring is handled on a per-tenant basis:

- **Monitoring Dashboards**: Each tenant has a dedicated monitoring dashboard.
- **Metrics**: Metrics are collected and displayed per tenant.
- **Alerts**: Alerts can be configured per tenant.

## Tenant-Specific Logging and Auditing

Logging and auditing are handled on a per-tenant basis:

- **Audit Logs**: Audit logs are stored in tenant-specific BigQuery tables.
- **Application Logs**: Application logs are tagged with tenant IDs.
- **Compliance Reports**: Compliance reports can be generated per tenant.

## Security Considerations

The single-tenant architecture provides enhanced security through:

- **Tenant Isolation**: Complete isolation between tenants.
- **Dedicated Encryption Keys**: Each tenant has their own encryption keys.
- **Dedicated Service Accounts**: Each tenant has their own service accounts with limited permissions.
- **Network Isolation**: Network traffic is isolated between tenants.

## Compliance Considerations

The single-tenant architecture helps meet compliance requirements through:

- **Data Isolation**: Each tenant's data is stored separately.
- **Audit Trails**: Tenant-specific audit trails for compliance reporting.
- **Encryption**: Tenant-specific encryption for data protection.
- **Access Control**: Tenant-specific access control for data security.

## Performance Considerations

The single-tenant architecture provides consistent performance through:

- **Dedicated Resources**: Each tenant has dedicated compute resources.
- **Resource Quotas**: Resource quotas prevent resource starvation.
- **Scalability**: Each tenant can scale independently based on their needs.

## Conclusion

The single-tenant architecture of NovaConnect UAC provides enhanced security, performance, and compliance for customers. Each tenant gets their own dedicated instance of the application with complete isolation from other tenants, ensuring that their data and resources are protected and optimized for their specific needs.
