# AI Alignment Studio - API Configuration
# Copy this file to .env.local and add your real API keys

# OpenAI API Key
NEXT_PUBLIC_OPENAI_API_KEY=sk-your-openai-key-here

# Anthropic API Key  
NEXT_PUBLIC_ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here

# Google AI API Key
NEXT_PUBLIC_GOOGLE_AI_API_KEY=your-google-ai-key-here

# Hugging Face API Key
NEXT_PUBLIC_HUGGINGFACE_API_KEY=hf_your-huggingface-key-here

# Custom AI Provider APIs
NEXT_PUBLIC_CUSTOM_API_ENDPOINT=https://your-custom-ai-api.com
NEXT_PUBLIC_CUSTOM_API_KEY=your-custom-key-here

# Monitoring Configuration
NEXT_PUBLIC_MONITORING_INTERVAL=30000  # 30 seconds
NEXT_PUBLIC_ENABLE_REAL_API=true       # Set to false for demo mode
NEXT_PUBLIC_EMERGENCY_WEBHOOK=https://your-emergency-webhook.com

# Security
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key-for-sensitive-data

# Development
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_LOG_LEVEL=info
