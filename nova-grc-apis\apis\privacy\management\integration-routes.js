/**
 * Integration Routes
 */

const express = require('express');
const router = express.Router();
const controllers = require('./integration-controllers');
const { authorize } = require('./auth');
const { cacheMiddleware } = require('./cache');

/**
 * @swagger
 * /privacy/management/integrations:
 *   get:
 *     summary: Get all available integrations
 *     description: Returns a list of all available integrations
 *     tags: [Integrations]
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Integration'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', authorize('read'), cacheMiddleware(300), controllers.getIntegrations);

/**
 * @swagger
 * /privacy/management/integrations/{id}:
 *   get:
 *     summary: Get an integration by ID
 *     description: Returns a specific integration by ID
 *     tags: [Integrations]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Integration ID
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   $ref: '#/components/schemas/Integration'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', authorize('read'), cacheMiddleware(300), controllers.getIntegrationById);

/**
 * @swagger
 * /privacy/management/integrations/{id}/{action}:
 *   post:
 *     summary: Execute an integration action
 *     description: Executes an action for a specific integration
 *     tags: [Integrations]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         description: Integration ID
 *         required: true
 *         schema:
 *           type: string
 *       - name: action
 *         in: path
 *         description: Action to execute
 *         required: true
 *         schema:
 *           type: string
 *           enum: [data-export, data-deletion, data-update, notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                 message:
 *                   type: string
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         $ref: '#/components/responses/NotFound'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/:id/:action', authorize('write'), controllers.executeIntegrationAction);

module.exports = router;
