/**
 * Feedback Context
 * 
 * This module provides a context for feedback functionality.
 */

import React, { createContext, useContext, useState, useCallback } from 'react';
import PropTypes from 'prop-types';

// Create context
const FeedbackContext = createContext();

/**
 * Use feedback hook
 * 
 * @returns {Object} Feedback context
 */
export const useFeedback = () => {
  const context = useContext(FeedbackContext);
  
  if (!context) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  
  return context;
};

/**
 * Feedback provider component
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 * @param {Function} [props.onSubmit] - Function to call when feedback is submitted
 * @returns {React.ReactElement} FeedbackProvider component
 */
export const FeedbackProvider = ({ children, onSubmit }) => {
  // State
  const [feedbackHistory, setFeedbackHistory] = useState([]);
  
  /**
   * Submit feedback
   * 
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise<Object>} - Submitted feedback
   */
  const submitFeedback = useCallback(async (feedbackData) => {
    try {
      let result;
      
      // Use custom submit function if provided
      if (onSubmit) {
        result = await onSubmit(feedbackData);
      } else {
        // Default implementation using fetch
        const response = await fetch('/api/feedback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(feedbackData)
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Error submitting feedback');
        }
        
        result = await response.json();
      }
      
      // Add to history
      setFeedbackHistory(prev => [
        {
          ...feedbackData,
          timestamp: new Date().toISOString(),
          status: 'submitted'
        },
        ...prev
      ]);
      
      return result;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      
      // Add to history as failed
      setFeedbackHistory(prev => [
        {
          ...feedbackData,
          timestamp: new Date().toISOString(),
          status: 'failed',
          error: error.message
        },
        ...prev
      ]);
      
      throw error;
    }
  }, [onSubmit]);
  
  /**
   * Get feedback history
   * 
   * @returns {Array} - Feedback history
   */
  const getFeedbackHistory = useCallback(() => {
    return feedbackHistory;
  }, [feedbackHistory]);
  
  /**
   * Clear feedback history
   */
  const clearFeedbackHistory = useCallback(() => {
    setFeedbackHistory([]);
  }, []);
  
  // Context value
  const value = {
    submitFeedback,
    getFeedbackHistory,
    clearFeedbackHistory,
    feedbackHistory
  };
  
  return (
    <FeedbackContext.Provider value={value}>
      {children}
    </FeedbackContext.Provider>
  );
};

FeedbackProvider.propTypes = {
  children: PropTypes.node.isRequired,
  onSubmit: PropTypes.func
};

export default FeedbackContext;
