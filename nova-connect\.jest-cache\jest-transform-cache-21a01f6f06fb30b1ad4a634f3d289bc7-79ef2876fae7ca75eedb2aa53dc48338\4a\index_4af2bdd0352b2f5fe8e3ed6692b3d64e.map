{"version": 3, "names": ["SecureConnectorExecutor", "require", "module", "exports"], "sources": ["index.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector Executor\n * \n * This module exports the connector executor implementations.\n */\n\nconst SecureConnectorExecutor = require('./secure-connector-executor');\n\nmodule.exports = {\n  SecureConnectorExecutor\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,uBAAuB,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AAEtEC,MAAM,CAACC,OAAO,GAAG;EACfH;AACF,CAAC", "ignoreList": []}