import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Divider,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  FormGroup,
  Alert,
  Snackbar,
} from '@mui/material';
import { Save as SaveIcon } from '@mui/icons-material';

function Settings() {
  const [serverUrl, setServerUrl] = useState('ws://localhost:3001/ws');
  const [autoConnect, setAutoConnect] = useState(true);
  const [enableLogging, setEnableLogging] = useState(true);
  const [enableMetrics, setEnableMetrics] = useState(true);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  
  const handleSaveSettings = () => {
    // In a real implementation, this would save the settings to local storage
    // and potentially reconnect to the server with the new settings
    console.log('Saving settings:', {
      serverUrl,
      autoConnect,
      enableLogging,
      enableMetrics,
    });
    
    // Show success message
    setSnackbarOpen(true);
  };
  
  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Settings
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Connection Settings
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <TextField
            label="Server URL"
            variant="outlined"
            fullWidth
            value={serverUrl}
            onChange={(e) => setServerUrl(e.target.value)}
            margin="normal"
          />
          
          <FormGroup>
            <FormControlLabel
              control={
                <Switch
                  checked={autoConnect}
                  onChange={(e) => setAutoConnect(e.target.checked)}
                />
              }
              label="Auto Connect"
            />
          </FormGroup>
        </CardContent>
      </Card>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            System Settings
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <FormGroup>
            <FormControlLabel
              control={
                <Switch
                  checked={enableLogging}
                  onChange={(e) => setEnableLogging(e.target.checked)}
                />
              }
              label="Enable Logging"
            />
            
            <FormControlLabel
              control={
                <Switch
                  checked={enableMetrics}
                  onChange={(e) => setEnableMetrics(e.target.checked)}
                />
              }
              label="Enable Metrics"
            />
          </FormGroup>
        </CardContent>
      </Card>
      
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            About
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          <Typography variant="body1" paragraph>
            NovaFuse Control Panel v0.1.0
          </Typography>
          
          <Typography variant="body2" color="text.secondary">
            This control panel provides interactive controls for manipulating the Self-Healing Tensor,
            3D Tensor Visualization, Analytics Dashboard, and other components of the NovaFuse system.
          </Typography>
        </CardContent>
      </Card>
      
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<SaveIcon />}
          onClick={handleSaveSettings}
        >
          Save Settings
        </Button>
      </Box>
      
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Settings saved successfully!
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default Settings;
