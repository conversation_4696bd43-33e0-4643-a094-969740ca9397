const crypto = require('crypto');
const rateLimit = require('express-rate-limit');

// Encryption utility
class CredentialEncryptor {
  constructor() {
    this.algorithm = 'aes-256-cbc';
    this.key = crypto.randomBytes(32);
    this.iv = crypto.randomBytes(16);
  }

  encrypt(text) {
    const cipher = crypto.createCipheriv(this.algorithm, this.key, this.iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  decrypt(encrypted) {
    const decipher = crypto.createDecipheriv(this.algorithm, this.key, this.iv);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}

// Rate limiter
class ApiRateLimiter {
  constructor() {
    this.limiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // Limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later.'
    });
  }

  getLimiter() {
    return this.limiter;
  }
}

// Request logger
class RequestLogger {
  constructor() {
    this.log = [];
  }

  logRequest(req, res, next) {
    const entry = {
      timestamp: new Date(),
      method: req.method,
      url: req.url,
      ip: req.ip,
      headers: req.headers
    };
    this.log.push(entry);
    next();
  }

  getRecentLogs() {
    return this.log.slice(-100); // Last 100 entries
  }
}

// Export utilities
module.exports = {
  CredentialEncryptor,
  ApiRateLimiter,
  RequestLogger
};
