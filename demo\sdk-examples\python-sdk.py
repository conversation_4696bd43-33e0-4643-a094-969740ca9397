# NovaFuse Python SDK Example

import requests
import json
from typing import Dict, List, Optional, Any, Union


class NovaFuseClient:
    """
    Client for interacting with the NovaFuse API Superstore.
    """
    
    def __init__(self, api_key: str, base_url: str = "https://api.novafuse.io"):
        """
        Initialize the NovaFuse client.
        
        Args:
            api_key: Your NovaFuse API key
            base_url: The base URL for the NovaFuse API (default: https://api.novafuse.io)
        """
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "apikey": api_key
        }
    
    def request(self, endpoint: str, method: str = "GET", 
                body: Optional[Dict[str, Any]] = None, 
                params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Make a request to the NovaFuse API.
        
        Args:
            endpoint: The API endpoint to call
            method: The HTTP method to use (default: GET)
            body: The request body for POST/PUT requests
            params: Query parameters for the request
            
        Returns:
            The JSON response from the API
            
        Raises:
            requests.exceptions.RequestException: If the request fails
        """
        url = f"{self.base_url}{endpoint}"
        
        # Remove None values from params
        if params:
            params = {k: v for k, v in params.items() if v is not None}
        
        try:
            if method == "GET":
                response = requests.get(url, headers=self.headers, params=params)
            elif method == "POST":
                response = requests.post(url, headers=self.headers, json=body, params=params)
            elif method == "PUT":
                response = requests.put(url, headers=self.headers, json=body, params=params)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            raise
    
    # Governance API
    
    def get_board_meetings(self, status: Optional[str] = None, 
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a list of board meetings with optional filtering.
        
        Args:
            status: Filter by status (scheduled, completed, cancelled)
            start_date: Filter by start date (YYYY-MM-DD)
            end_date: Filter by end date (YYYY-MM-DD)
            
        Returns:
            A dictionary containing the board meetings data
        """
        params = {
            "status": status,
            "start_date": start_date,
            "end_date": end_date
        }
        return self.request("/governance/board/meetings", params=params)
    
    def get_board_meeting(self, meeting_id: str) -> Dict[str, Any]:
        """
        Get details of a specific board meeting.
        
        Args:
            meeting_id: The ID of the board meeting
            
        Returns:
            A dictionary containing the board meeting details
        """
        return self.request(f"/governance/board/meetings/{meeting_id}")
    
    def get_policies(self, category: Optional[str] = None,
                    status: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a list of policies with optional filtering.
        
        Args:
            category: Filter by policy category
            status: Filter by status (active, draft, archived)
            
        Returns:
            A dictionary containing the policies data
        """
        params = {
            "category": category,
            "status": status
        }
        return self.request("/governance/policies", params=params)
    
    def get_policy(self, policy_id: str) -> Dict[str, Any]:
        """
        Get details of a specific policy.
        
        Args:
            policy_id: The ID of the policy
            
        Returns:
            A dictionary containing the policy details
        """
        return self.request(f"/governance/policies/{policy_id}")
    
    # Security API
    
    def get_vulnerabilities(self, severity: Optional[str] = None,
                           status: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a list of vulnerabilities with optional filtering.
        
        Args:
            severity: Filter by severity (low, medium, high, critical)
            status: Filter by status (open, in_progress, closed)
            
        Returns:
            A dictionary containing the vulnerabilities data
        """
        params = {
            "severity": severity,
            "status": status
        }
        return self.request("/security/vulnerabilities", params=params)
    
    def get_vulnerability(self, vulnerability_id: str) -> Dict[str, Any]:
        """
        Get details of a specific vulnerability.
        
        Args:
            vulnerability_id: The ID of the vulnerability
            
        Returns:
            A dictionary containing the vulnerability details
        """
        return self.request(f"/security/vulnerabilities/{vulnerability_id}")
    
    def run_security_scan(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run a security scan with the provided configuration.
        
        Args:
            config: The scan configuration
            
        Returns:
            A dictionary containing the scan results
        """
        return self.request("/security/scan", method="POST", body=config)
    
    # APIs & Developer Tools API
    
    def get_api_catalog(self, category: Optional[str] = None,
                       status: Optional[str] = None) -> Dict[str, Any]:
        """
        Get a catalog of available APIs with optional filtering.
        
        Args:
            category: Filter by API category
            status: Filter by status (active, deprecated, beta)
            
        Returns:
            A dictionary containing the API catalog data
        """
        params = {
            "category": category,
            "status": status
        }
        return self.request("/apis/catalog", params=params)
    
    def get_api_details(self, api_id: str) -> Dict[str, Any]:
        """
        Get details of a specific API.
        
        Args:
            api_id: The ID of the API
            
        Returns:
            A dictionary containing the API details
        """
        return self.request(f"/apis/catalog/{api_id}")
    
    def get_api_metrics(self, api_id: Optional[str] = None,
                       period: Optional[str] = None) -> Dict[str, Any]:
        """
        Get API usage metrics with optional filtering.
        
        Args:
            api_id: Filter by API ID
            period: Time period (day, week, month, year)
            
        Returns:
            A dictionary containing the API metrics data
        """
        params = {
            "api_id": api_id,
            "period": period
        }
        return self.request("/apis/metrics", params=params)


# Example usage
def main():
    # Initialize the client
    client = NovaFuseClient(
        api_key="your-api-key",
        base_url="https://api.novafuse.io"
    )
    
    try:
        # Get board meetings
        print("Fetching board meetings...")
        meetings = client.get_board_meetings(status="scheduled")
        print(f"Found {len(meetings['data'])} scheduled meetings")
        
        # Get details of the first meeting
        if meetings["data"]:
            meeting_id = meetings["data"][0]["id"]
            print(f"Fetching details for meeting {meeting_id}...")
            meeting_details = client.get_board_meeting(meeting_id)
            print("Meeting details:", json.dumps(meeting_details["data"], indent=2))
        
        # Get vulnerabilities
        print("Fetching vulnerabilities...")
        vulnerabilities = client.get_vulnerabilities(severity="high")
        print(f"Found {len(vulnerabilities['data'])} high severity vulnerabilities")
        
        # Get API catalog
        print("Fetching API catalog...")
        apis = client.get_api_catalog(status="active")
        print(f"Found {len(apis['data'])} active APIs")
        
    except Exception as e:
        print(f"Error in example: {e}")


if __name__ == "__main__":
    main()
