"""
Universal Compliance Intelligence Architecture (UCIA).

This module implements the main UCIA class that integrates all components
of the architecture into a unified compliance intelligence system.
"""

import os
import logging
from typing import Dict, List, Optional, Any, Tuple

from .core_engine import CoreComplianceEngine
from .framework_modules import ModuleRegistry, initialize_default_modules
from .knowledge_distillation import CrossRegulatoryKnowledgeDistillation, ComplianceOntology
from .model_integration import ComplianceModelIntegration

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UCIA:
    """
    Universal Compliance Intelligence Architecture.

    This class integrates all components of the UCIA into a unified
    compliance intelligence system.
    """

    def __init__(self, model_name: str = "novafuse/regulator-base", use_gpu: bool = True,
                 concept_model_name: str = None, framework_model_name: str = None):
        """
        Initialize the UCIA.

        Args:
            model_name: Name or path of the pre-trained model to use
            use_gpu: Whether to use GPU acceleration if available
            concept_model_name: Name or path of the concept detection model
            framework_model_name: Name or path of the framework detection model
        """
        logger.info("Initializing Universal Compliance Intelligence Architecture")

        # Initialize the core compliance engine
        self.core_engine = CoreComplianceEngine(
            model_name=model_name,
            use_gpu=use_gpu,
            concept_model_name=concept_model_name,
            framework_model_name=framework_model_name
        )

        # Initialize the module registry with default modules
        self.module_registry = initialize_default_modules()

        # Initialize the cross-regulatory knowledge distillation component
        self.knowledge_distillation = CrossRegulatoryKnowledgeDistillation(self.module_registry)

        # Initialize the compliance ontology
        self.ontology = ComplianceOntology(self.module_registry)

        logger.info("UCIA initialization complete")

    def process_query(self, query: str, user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a compliance query and generate a response.

        Args:
            query: The user's compliance query
            user_context: Context information about the user and their organization

        Returns:
            A dictionary containing the response and metadata
        """
        logger.info(f"Processing query: {query}")

        # Process the query with the core engine
        core_response = self.core_engine.process_query(query, user_context)

        # Generate unified guidance across frameworks
        unified_guidance = self.knowledge_distillation.get_unified_guidance(
            core_response, user_context or {})

        # Combine the core response and unified guidance
        response = self._combine_responses(core_response, unified_guidance)

        return response

    def _combine_responses(self, core_response: Dict[str, Any],
                          unified_guidance: Dict[str, Any]) -> Dict[str, Any]:
        """
        Combine the core engine response and unified guidance.

        Args:
            core_response: The response from the core engine
            unified_guidance: The unified guidance from the knowledge distillation component

        Returns:
            A combined response dictionary
        """
        # For now, prioritize the unified guidance if available
        if unified_guidance and 'response_text' in unified_guidance:
            response_text = unified_guidance['response_text']
            confidence = unified_guidance.get('confidence', 0.0)
            citations = unified_guidance.get('citations', [])
        else:
            response_text = core_response.get('response_text', '')
            confidence = core_response.get('confidence', 0.0)
            citations = core_response.get('citations', [])

        # Combine metadata
        frameworks = unified_guidance.get('frameworks', core_response.get('frameworks', []))
        concepts = unified_guidance.get('concepts', core_response.get('concepts', []))

        return {
            'response_text': response_text,
            'confidence': confidence,
            'citations': citations,
            'frameworks': frameworks,
            'concepts': concepts,
            'common_themes': unified_guidance.get('common_themes', []),
            'differences': unified_guidance.get('differences', []),
            'follow_up_questions': core_response.get('follow_up_questions', [])
        }

    def register_module(self, module_id: str, module) -> None:
        """
        Register a new compliance framework module.

        Args:
            module_id: The identifier for the module
            module: The module instance
        """
        self.module_registry.register_module(module_id, module)

        # Refresh the knowledge distillation component and ontology
        self.knowledge_distillation = CrossRegulatoryKnowledgeDistillation(self.module_registry)
        self.ontology = ComplianceOntology(self.module_registry)

    def activate_module(self, module_id: str) -> bool:
        """
        Activate a compliance framework module.

        Args:
            module_id: The identifier for the module

        Returns:
            True if the module was activated, False otherwise
        """
        result = self.module_registry.activate_module(module_id)

        # Refresh the knowledge distillation component if the module was activated
        if result:
            self.knowledge_distillation = CrossRegulatoryKnowledgeDistillation(self.module_registry)
            self.ontology = ComplianceOntology(self.module_registry)

        return result

    def deactivate_module(self, module_id: str) -> bool:
        """
        Deactivate a compliance framework module.

        Args:
            module_id: The identifier for the module

        Returns:
            True if the module was deactivated, False otherwise
        """
        result = self.module_registry.deactivate_module(module_id)

        # Refresh the knowledge distillation component if the module was deactivated
        if result:
            self.knowledge_distillation = CrossRegulatoryKnowledgeDistillation(self.module_registry)
            self.ontology = ComplianceOntology(self.module_registry)

        return result

    def get_active_modules(self) -> List[Dict[str, Any]]:
        """
        Get metadata for all active modules.

        Returns:
            A list of module metadata dictionaries
        """
        active_modules = self.module_registry.get_active_modules()
        return [module.get_metadata() for module in active_modules]

    def get_all_modules(self) -> List[Dict[str, Any]]:
        """
        Get metadata for all registered modules.

        Returns:
            A list of module metadata dictionaries
        """
        all_modules = self.module_registry.get_all_modules()
        return [module.get_metadata() for module in all_modules.values()]

    def get_related_requirements(self, concept: str, framework_id: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get requirements related to a concept across frameworks.

        Args:
            concept: The concept identifier
            framework_id: Optional framework to filter by

        Returns:
            A dictionary mapping framework identifiers to lists of requirement dictionaries
        """
        return self.knowledge_distillation.get_related_requirements(concept, framework_id)

    def get_framework_mappings(self, source_framework: str, target_framework: str) -> Dict[str, List[str]]:
        """
        Get mappings between two frameworks.

        Args:
            source_framework: The source framework identifier
            target_framework: The target framework identifier

        Returns:
            A dictionary mapping source framework requirements to target framework requirements
        """
        return self.knowledge_distillation.get_framework_mappings(source_framework, target_framework)

    def get_concept_hierarchy(self) -> Dict[str, Any]:
        """
        Get the complete concept hierarchy from the ontology.

        Returns:
            A dictionary representing the concept hierarchy
        """
        return self.ontology.concepts

    def get_child_concepts(self, concept_path: List[str]) -> List[List[str]]:
        """
        Get the child concepts of a concept.

        Args:
            concept_path: A list of strings representing the path to the concept

        Returns:
            A list of concept paths representing the child concepts
        """
        return self.ontology.get_child_concepts(concept_path)

    @staticmethod
    def get_available_models() -> List[Dict[str, str]]:
        """
        Get a list of available language models.

        Returns:
            List of dictionaries with model information
        """
        return ComplianceModelIntegration.get_available_models()


if __name__ == "__main__":
    # Simple test of the UCIA
    ucia = UCIA(model_name="gpt2")  # Use a small model for testing

    # Get active modules
    active_modules = ucia.get_active_modules()
    print(f"Active modules:")
    for module in active_modules:
        print(f"  - {module['name']} ({module['id']})")

    # Process a query
    response = ucia.process_query("What are the requirements for data breach notification?")
    print(f"\nQuery: What are the requirements for data breach notification?")
    print(f"Response: {response['response_text']}")
    print(f"\nCitations:")
    for citation in response['citations']:
        print(f"  - {citation['framework']}: {citation['reference']} - {citation['text']}")

    # Get related requirements
    data_breach_reqs = ucia.get_related_requirements('data_breach')
    print(f"\nRequirements related to data breach:")
    for framework, reqs in data_breach_reqs.items():
        print(f"  {framework.upper()}:")
        for req in reqs:
            print(f"    - {req['reference']}: {req['title']}")

    # Get concept hierarchy
    child_concepts = ucia.get_child_concepts(['data_protection'])
    print(f"\nChild concepts of data_protection:")
    for child in child_concepts:
        print(f"  - {'/'.join(child)}")
