import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  Button, 
  IconButton, 
  Paper, 
  Switch, 
  FormControlLabel, 
  Tooltip,
  Di<PERSON><PERSON>,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Help as HelpIcon
} from '@mui/icons-material';

/**
 * Dynamic Authentication Fields Component
 * 
 * This component renders and manages the authentication fields based on the selected authentication type.
 */
const DynamicAuthFields = ({ authType, fields, setFields, credentials, setCredentials }) => {
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldDescription, setNewFieldDescription] = useState('');
  const [newFieldRequired, setNewFieldRequired] = useState(false);
  const [newFieldSensitive, setNewFieldSensitive] = useState(false);
  const [showPasswords, setShowPasswords] = useState({});

  // <PERSON>le adding a new field
  const handleAddField = () => {
    if (newFieldName.trim()) {
      const updatedFields = {
        ...fields,
        [newFieldName.trim()]: {
          type: 'string',
          description: newFieldDescription.trim() || `${newFieldName.trim()} field`,
          required: newFieldRequired,
          sensitive: newFieldSensitive
        }
      };
      
      setFields(updatedFields);
      
      // Reset form
      setNewFieldName('');
      setNewFieldDescription('');
      setNewFieldRequired(false);
      setNewFieldSensitive(false);
    }
  };

  // Handle deleting a field
  const handleDeleteField = (fieldName) => {
    const { [fieldName]: _, ...remainingFields } = fields;
    setFields(remainingFields);
    
    // Also remove from credentials if present
    if (credentials[fieldName]) {
      const { [fieldName]: __, ...remainingCredentials } = credentials;
      setCredentials(remainingCredentials);
    }
  };

  // Handle credential input
  const handleCredentialChange = (fieldName, value) => {
    setCredentials({
      ...credentials,
      [fieldName]: value
    });
  };

  // Toggle password visibility
  const togglePasswordVisibility = (fieldName) => {
    setShowPasswords({
      ...showPasswords,
      [fieldName]: !showPasswords[fieldName]
    });
  };

  return (
    <Box>
      {/* Existing Fields */}
      {Object.keys(fields).length > 0 ? (
        <Grid container spacing={2} sx={{ mb: 4 }}>
          {Object.entries(fields).map(([fieldName, fieldConfig]) => (
            <Grid item xs={12} sm={6} key={fieldName}>
              <Paper sx={{ p: 2, backgroundColor: 'background.default' }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography variant="subtitle2">{fieldName}</Typography>
                    {fieldConfig.required && (
                      <Chip 
                        label="Required" 
                        size="small" 
                        color="primary" 
                        variant="outlined" 
                        sx={{ ml: 1, height: 20 }}
                      />
                    )}
                    {fieldConfig.sensitive && (
                      <Chip 
                        label="Sensitive" 
                        size="small" 
                        color="error" 
                        variant="outlined" 
                        sx={{ ml: 1, height: 20 }}
                      />
                    )}
                  </Box>
                  <IconButton 
                    color="error" 
                    onClick={() => handleDeleteField(fieldName)}
                    size="small"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
                
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                  {fieldConfig.description}
                </Typography>
                
                <TextField
                  fullWidth
                  size="small"
                  placeholder={`Enter ${fieldName}`}
                  value={credentials[fieldName] || ''}
                  onChange={(e) => handleCredentialChange(fieldName, e.target.value)}
                  type={fieldConfig.sensitive && !showPasswords[fieldName] ? 'password' : 'text'}
                  required={fieldConfig.required}
                  InputProps={{
                    endAdornment: fieldConfig.sensitive && (
                      <IconButton
                        size="small"
                        onClick={() => togglePasswordVisibility(fieldName)}
                        edge="end"
                      >
                        {showPasswords[fieldName] ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    )
                  }}
                />
              </Paper>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Paper 
          variant="outlined" 
          sx={{ 
            p: 3, 
            mb: 3, 
            backgroundColor: 'background.default',
            textAlign: 'center'
          }}
        >
          <Typography color="text.secondary">
            No authentication fields defined yet. Add fields below.
          </Typography>
        </Paper>
      )}
      
      {/* Add New Field */}
      <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
        <Typography variant="subtitle2" gutterBottom>
          Add New Authentication Field
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Field Name"
              value={newFieldName}
              onChange={(e) => setNewFieldName(e.target.value)}
              fullWidth
              placeholder="e.g., apiKey, username"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              label="Description"
              value={newFieldDescription}
              onChange={(e) => setNewFieldDescription(e.target.value)}
              fullWidth
              placeholder="e.g., API Key for authentication"
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={newFieldRequired}
                  onChange={(e) => setNewFieldRequired(e.target.checked)}
                  color="primary"
                  size="small"
                />
              }
              label="Required"
            />
            <Tooltip title="Mark this field as required for authentication">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControlLabel
              control={
                <Switch
                  checked={newFieldSensitive}
                  onChange={(e) => setNewFieldSensitive(e.target.checked)}
                  color="primary"
                  size="small"
                />
              }
              label="Sensitive"
            />
            <Tooltip title="Mark this field as sensitive (e.g., passwords, tokens)">
              <IconButton size="small">
                <HelpIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Grid>
          <Grid item xs={12}>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddField}
              disabled={!newFieldName.trim()}
              size="small"
            >
              Add Field
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default DynamicAuthFields;
