/**
 * Predictive Analytics
 *
 * This module provides predictive analytics capabilities for the Finite Universe
 * Principle defense system. It uses time series analysis to predict future
 * boundary violations and other metrics.
 */

const EventEmitter = require('events');

/**
 * PredictiveAnalytics class
 * 
 * Provides predictive analytics capabilities for boundary violations.
 */
class PredictiveAnalytics extends EventEmitter {
  /**
   * Constructor
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();

    this.options = {
      enableLogging: true,
      historyLength: 100, // Number of data points to keep in history
      forecastHorizon: 10, // Number of time steps to forecast
      seasonalityPeriod: 24, // Seasonality period (e.g., 24 hours)
      confidenceLevel: 0.95, // Confidence level for prediction intervals
      ...options
    };

    // Initialize metrics history
    this.metricsHistory = [];

    // Initialize forecasts
    this.forecasts = {
      boundaryViolations: [],
      validationFailures: [],
      domainViolations: {
        cyber: [],
        financial: [],
        medical: []
      }
    };

    // Initialize prediction accuracy
    this.accuracy = {
      boundaryViolations: 0,
      validationFailures: 0,
      domainViolations: {
        cyber: 0,
        financial: 0,
        medical: 0
      }
    };

    if (this.options.enableLogging) {
      console.log('PredictiveAnalytics initialized with options:', this.options);
    }
  }

  /**
   * Process metrics for predictive analytics
   * @param {Object} metrics - Metrics to process
   * @returns {Object} - Predictive analytics results
   */
  processMetrics(metrics) {
    // Add metrics to history
    this._addToHistory(metrics);

    // Update forecasts
    this._updateForecasts();

    // Evaluate prediction accuracy
    this._evaluateAccuracy(metrics);

    // Check for predicted violations
    const predictions = this._checkPredictions();

    // Emit events for predicted violations
    if (predictions.predictedViolations) {
      this.emit('violations-predicted', {
        predictions,
        metrics,
        timestamp: new Date()
      });

      if (this.options.enableLogging) {
        console.log('Boundary violations predicted:', predictions);
      }
    }

    return {
      forecasts: this.forecasts,
      predictions,
      accuracy: this.accuracy
    };
  }

  /**
   * Add metrics to history
   * @param {Object} metrics - Metrics to add
   * @private
   */
  _addToHistory(metrics) {
    // Add metrics to history
    this.metricsHistory.push({
      timestamp: new Date(),
      metrics: { ...metrics }
    });

    // Trim history if needed
    if (this.metricsHistory.length > this.options.historyLength) {
      this.metricsHistory.shift();
    }
  }

  /**
   * Update forecasts
   * @private
   */
  _updateForecasts() {
    // Skip if not enough history
    if (this.metricsHistory.length < 10) {
      return;
    }

    // Extract time series data
    const boundaryViolations = this.metricsHistory.map(entry => entry.metrics.boundaryViolations || 0);
    const validationFailures = this.metricsHistory.map(entry => entry.metrics.validationFailures || 0);
    
    const cyberViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.cyber?.boundaryViolations || 0
    );
    
    const financialViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.financial?.boundaryViolations || 0
    );
    
    const medicalViolations = this.metricsHistory.map(entry => 
      entry.metrics.domainMetrics?.medical?.boundaryViolations || 0
    );

    // Generate forecasts using exponential smoothing
    this.forecasts.boundaryViolations = this._exponentialSmoothing(boundaryViolations);
    this.forecasts.validationFailures = this._exponentialSmoothing(validationFailures);
    this.forecasts.domainViolations.cyber = this._exponentialSmoothing(cyberViolations);
    this.forecasts.domainViolations.financial = this._exponentialSmoothing(financialViolations);
    this.forecasts.domainViolations.medical = this._exponentialSmoothing(medicalViolations);
  }

  /**
   * Evaluate prediction accuracy
   * @param {Object} metrics - Current metrics
   * @private
   */
  _evaluateAccuracy(metrics) {
    // Skip if no forecasts available
    if (this.forecasts.boundaryViolations.length === 0) {
      return;
    }

    // Get previous forecasts
    const prevBoundaryForecast = this.forecasts.boundaryViolations[0];
    const prevValidationForecast = this.forecasts.validationFailures[0];
    const prevCyberForecast = this.forecasts.domainViolations.cyber[0];
    const prevFinancialForecast = this.forecasts.domainViolations.financial[0];
    const prevMedicalForecast = this.forecasts.domainViolations.medical[0];

    // Calculate accuracy using mean absolute percentage error (MAPE)
    const boundaryViolations = metrics.boundaryViolations || 0;
    const validationFailures = metrics.validationFailures || 0;
    const cyberViolations = metrics.domainMetrics?.cyber?.boundaryViolations || 0;
    const financialViolations = metrics.domainMetrics?.financial?.boundaryViolations || 0;
    const medicalViolations = metrics.domainMetrics?.medical?.boundaryViolations || 0;

    // Update accuracy (1 - error)
    this.accuracy.boundaryViolations = this._calculateAccuracy(prevBoundaryForecast, boundaryViolations);
    this.accuracy.validationFailures = this._calculateAccuracy(prevValidationForecast, validationFailures);
    this.accuracy.domainViolations.cyber = this._calculateAccuracy(prevCyberForecast, cyberViolations);
    this.accuracy.domainViolations.financial = this._calculateAccuracy(prevFinancialForecast, financialViolations);
    this.accuracy.domainViolations.medical = this._calculateAccuracy(prevMedicalForecast, medicalViolations);
  }

  /**
   * Calculate prediction accuracy
   * @param {number} forecast - Forecasted value
   * @param {number} actual - Actual value
   * @returns {number} - Accuracy (0-1)
   * @private
   */
  _calculateAccuracy(forecast, actual) {
    if (actual === 0 && forecast === 0) {
      return 1.0; // Perfect accuracy when both are zero
    }
    
    if (actual === 0) {
      return forecast < 1 ? 1.0 - forecast : 0.0;
    }
    
    const error = Math.abs(forecast - actual) / actual;
    return Math.max(0, 1.0 - error);
  }

  /**
   * Check for predicted violations
   * @returns {Object} - Prediction results
   * @private
   */
  _checkPredictions() {
    // Skip if no forecasts available
    if (this.forecasts.boundaryViolations.length === 0) {
      return {
        predictedViolations: false
      };
    }

    // Get forecasts for next time step
    const nextBoundaryViolations = this.forecasts.boundaryViolations[0];
    const nextValidationFailures = this.forecasts.validationFailures[0];
    const nextCyberViolations = this.forecasts.domainViolations.cyber[0];
    const nextFinancialViolations = this.forecasts.domainViolations.financial[0];
    const nextMedicalViolations = this.forecasts.domainViolations.medical[0];

    // Check if violations are predicted to increase
    const boundaryIncreasing = nextBoundaryViolations > this.metricsHistory[this.metricsHistory.length - 1].metrics.boundaryViolations;
    const validationIncreasing = nextValidationFailures > this.metricsHistory[this.metricsHistory.length - 1].metrics.validationFailures;
    const cyberIncreasing = nextCyberViolations > (this.metricsHistory[this.metricsHistory.length - 1].metrics.domainMetrics?.cyber?.boundaryViolations || 0);
    const financialIncreasing = nextFinancialViolations > (this.metricsHistory[this.metricsHistory.length - 1].metrics.domainMetrics?.financial?.boundaryViolations || 0);
    const medicalIncreasing = nextMedicalViolations > (this.metricsHistory[this.metricsHistory.length - 1].metrics.domainMetrics?.medical?.boundaryViolations || 0);

    // Determine if violations are predicted
    const predictedViolations = boundaryIncreasing || validationIncreasing || cyberIncreasing || financialIncreasing || medicalIncreasing;

    return {
      predictedViolations,
      boundaryViolations: {
        current: this.metricsHistory[this.metricsHistory.length - 1].metrics.boundaryViolations,
        predicted: nextBoundaryViolations,
        increasing: boundaryIncreasing
      },
      validationFailures: {
        current: this.metricsHistory[this.metricsHistory.length - 1].metrics.validationFailures,
        predicted: nextValidationFailures,
        increasing: validationIncreasing
      },
      domainViolations: {
        cyber: {
          current: this.metricsHistory[this.metricsHistory.length - 1].metrics.domainMetrics?.cyber?.boundaryViolations || 0,
          predicted: nextCyberViolations,
          increasing: cyberIncreasing
        },
        financial: {
          current: this.metricsHistory[this.metricsHistory.length - 1].metrics.domainMetrics?.financial?.boundaryViolations || 0,
          predicted: nextFinancialViolations,
          increasing: financialIncreasing
        },
        medical: {
          current: this.metricsHistory[this.metricsHistory.length - 1].metrics.domainMetrics?.medical?.boundaryViolations || 0,
          predicted: nextMedicalViolations,
          increasing: medicalIncreasing
        }
      }
    };
  }

  /**
   * Exponential smoothing for time series forecasting
   * @param {Array} data - Time series data
   * @param {number} alpha - Smoothing factor (0-1)
   * @returns {Array} - Forecasted values
   * @private
   */
  _exponentialSmoothing(data, alpha = 0.3) {
    if (data.length === 0) {
      return [];
    }

    // Initialize with first value
    let level = data[0];
    let trend = data.length > 1 ? data[1] - data[0] : 0;

    // Apply exponential smoothing
    for (let i = 1; i < data.length; i++) {
      const prevLevel = level;
      level = alpha * data[i] + (1 - alpha) * (level + trend);
      trend = alpha * (level - prevLevel) + (1 - alpha) * trend;
    }

    // Generate forecasts
    const forecasts = [];
    for (let i = 1; i <= this.options.forecastHorizon; i++) {
      forecasts.push(Math.max(0, Math.round(level + i * trend)));
    }

    return forecasts;
  }

  /**
   * Get forecasts
   * @returns {Object} - Forecasts
   */
  getForecasts() {
    return { ...this.forecasts };
  }

  /**
   * Get prediction accuracy
   * @returns {Object} - Prediction accuracy
   */
  getAccuracy() {
    return { ...this.accuracy };
  }

  /**
   * Reset analytics
   */
  reset() {
    // Reset history
    this.metricsHistory = [];

    // Reset forecasts
    this.forecasts = {
      boundaryViolations: [],
      validationFailures: [],
      domainViolations: {
        cyber: [],
        financial: [],
        medical: []
      }
    };

    // Reset accuracy
    this.accuracy = {
      boundaryViolations: 0,
      validationFailures: 0,
      domainViolations: {
        cyber: 0,
        financial: 0,
        medical: 0
      }
    };

    this.emit('reset');

    if (this.options.enableLogging) {
      console.log('PredictiveAnalytics reset');
    }
  }
}

/**
 * Create a predictive analytics instance with recommended settings
 * @param {Object} options - Configuration options
 * @returns {PredictiveAnalytics} - Configured predictive analytics instance
 */
function createPredictiveAnalytics(options = {}) {
  return new PredictiveAnalytics({
    enableLogging: true,
    historyLength: 100,
    forecastHorizon: 10,
    seasonalityPeriod: 24,
    confidenceLevel: 0.95,
    ...options
  });
}

module.exports = {
  PredictiveAnalytics,
  createPredictiveAnalytics
};
