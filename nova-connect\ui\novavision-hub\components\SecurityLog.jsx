/**
 * SecurityLog Component
 * 
 * A component for displaying security log entries.
 */

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useSecurity } from '../security/SecurityContext';
import { useTheme } from '../theme/ThemeContext';
import { useI18n } from '../i18n/I18nContext';
import { FormattedDate } from './FormattedDate';

/**
 * SecurityLog component
 * 
 * @param {Object} props - Component props
 * @param {number} [props.maxItems=10] - Maximum number of items to display
 * @param {boolean} [props.showPagination=true] - Whether to show pagination
 * @param {boolean} [props.showFilters=true] - Whether to show filters
 * @param {string} [props.className] - Additional CSS class names
 * @param {Object} [props.style] - Additional inline styles
 * @returns {React.ReactElement} SecurityLog component
 */
const SecurityLog = ({
  maxItems = 10,
  showPagination = true,
  showFilters = true,
  className = '',
  style = {}
}) => {
  const { theme } = useTheme();
  const { translate } = useI18n();
  const { securityLog, isLoading } = useSecurity();
  
  // State
  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilter] = useState('all');
  
  // Filter log entries
  const filteredLog = securityLog.filter(entry => {
    if (filter === 'all') {
      return true;
    }
    return entry.type === filter;
  });
  
  // Paginate log entries
  const totalPages = Math.ceil(filteredLog.length / maxItems);
  const paginatedLog = filteredLog.slice((currentPage - 1) * maxItems, currentPage * maxItems);
  
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  
  // Handle filter change
  const handleFilterChange = (event) => {
    setFilter(event.target.value);
    setCurrentPage(1);
  };
  
  // Get event type label
  const getEventTypeLabel = (type) => {
    switch (type) {
      case 'login':
        return translate('security.login', 'Login');
      case 'logout':
        return translate('security.logout', 'Logout');
      case 'login_failed':
        return translate('security.loginFailed', 'Login Failed');
      case 'password_change':
        return translate('security.passwordChange', 'Password Change');
      case 'password_reset':
        return translate('security.passwordReset', 'Password Reset');
      case 'two_factor_enabled':
        return translate('security.twoFactorEnabled', 'Two-Factor Enabled');
      case 'two_factor_disabled':
        return translate('security.twoFactorDisabled', 'Two-Factor Disabled');
      case 'two_factor_verified':
        return translate('security.twoFactorVerified', 'Two-Factor Verified');
      case 'two_factor_failed':
        return translate('security.twoFactorFailed', 'Two-Factor Failed');
      case 'account_locked':
        return translate('security.accountLocked', 'Account Locked');
      case 'account_unlocked':
        return translate('security.accountUnlocked', 'Account Unlocked');
      default:
        return type;
    }
  };
  
  // Get event icon
  const getEventIcon = (type) => {
    switch (type) {
      case 'login':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-success" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
          </svg>
        );
      case 'logout':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-textSecondary" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V7.414l-5.293-5.293A1 1 0 0010 2H3zm5 4a1 1 0 00-1 1v6a1 1 0 002 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'login_failed':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-error" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'password_change':
      case 'password_reset':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
        );
      case 'two_factor_enabled':
      case 'two_factor_verified':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-success" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'two_factor_disabled':
      case 'two_factor_failed':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-warning" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 1.944A11.954 11.954 0 012.166 5C2.056 5.649 2 6.319 2 7c0 5.225 3.34 9.67 8 11.317C14.66 16.67 18 12.225 18 7c0-.682-.057-1.35-.166-2.001A11.954 11.954 0 0110 1.944zM11 14a1 1 0 11-2 0 1 1 0 012 0zm0-7a1 1 0 10-2 0v3a1 1 0 102 0V7z" clipRule="evenodd" />
          </svg>
        );
      case 'account_locked':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-error" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
          </svg>
        );
      case 'account_unlocked':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-success" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 2a5 5 0 00-5 5v2a2 2 0 00-2 2v5a2 2 0 002 2h10a2 2 0 002-2v-5a2 2 0 00-2-2H7V7a3 3 0 015.905-.75 1 1 0 001.937-.5A5.002 5.002 0 0010 2z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-textSecondary" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };
  
  return (
    <div
      className={`${className}`}
      style={style}
      data-testid="security-log"
    >
      {/* Filters */}
      {showFilters && (
        <div className="mb-4">
          <label htmlFor="event-filter" className="block text-sm font-medium text-textSecondary mb-1">
            {translate('security.filterEvents', 'Filter Events')}
          </label>
          
          <select
            id="event-filter"
            className="w-full px-3 py-2 border border-divider rounded-md bg-background text-textPrimary focus:outline-none focus:ring-2 focus:ring-primary"
            value={filter}
            onChange={handleFilterChange}
          >
            <option value="all">{translate('common.all', 'All')}</option>
            <option value="login">{translate('security.login', 'Login')}</option>
            <option value="logout">{translate('security.logout', 'Logout')}</option>
            <option value="login_failed">{translate('security.loginFailed', 'Login Failed')}</option>
            <option value="password_change">{translate('security.passwordChange', 'Password Change')}</option>
            <option value="password_reset">{translate('security.passwordReset', 'Password Reset')}</option>
            <option value="two_factor_enabled">{translate('security.twoFactorEnabled', 'Two-Factor Enabled')}</option>
            <option value="two_factor_disabled">{translate('security.twoFactorDisabled', 'Two-Factor Disabled')}</option>
            <option value="two_factor_verified">{translate('security.twoFactorVerified', 'Two-Factor Verified')}</option>
            <option value="two_factor_failed">{translate('security.twoFactorFailed', 'Two-Factor Failed')}</option>
            <option value="account_locked">{translate('security.accountLocked', 'Account Locked')}</option>
            <option value="account_unlocked">{translate('security.accountUnlocked', 'Account Unlocked')}</option>
          </select>
        </div>
      )}
      
      {/* Log entries */}
      <div className="space-y-2">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : paginatedLog.length === 0 ? (
          <div className="text-center py-8 text-textSecondary">
            {translate('security.noLogEntries', 'No log entries found')}
          </div>
        ) : (
          paginatedLog.map(entry => (
            <div
              key={entry.id}
              className="p-3 rounded-md border border-divider bg-background"
            >
              <div className="flex items-start">
                <div className="mr-3 mt-0.5">
                  {getEventIcon(entry.type)}
                </div>
                
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <div className="font-medium text-textPrimary">
                      {getEventTypeLabel(entry.type)}
                    </div>
                    
                    <div className="text-xs text-textSecondary">
                      <FormattedDate
                        value={entry.timestamp}
                        options={{ dateStyle: 'medium', timeStyle: 'short' }}
                      />
                    </div>
                  </div>
                  
                  {entry.details && (
                    <div className="mt-1 text-sm text-textSecondary">
                      {entry.details.ipAddress && (
                        <div>
                          {translate('security.ipAddress', 'IP Address')}: {entry.details.ipAddress}
                        </div>
                      )}
                      
                      {entry.details.userAgent && (
                        <div className="truncate">
                          {translate('security.userAgent', 'User Agent')}: {entry.details.userAgent}
                        </div>
                      )}
                      
                      {entry.details.success === false && entry.details.error && (
                        <div className="text-error">
                          {entry.details.error}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
      
      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex justify-between items-center mt-4">
          <button
            type="button"
            className="px-3 py-1 text-sm text-textSecondary hover:text-textPrimary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            {translate('common.previous', 'Previous')}
          </button>
          
          <div className="text-sm text-textSecondary">
            {translate('common.pageXOfY', 'Page {{current}} of {{total}}', {
              current: currentPage,
              total: totalPages
            })}
          </div>
          
          <button
            type="button"
            className="px-3 py-1 text-sm text-textSecondary hover:text-textPrimary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            {translate('common.next', 'Next')}
          </button>
        </div>
      )}
    </div>
  );
};

SecurityLog.propTypes = {
  maxItems: PropTypes.number,
  showPagination: PropTypes.bool,
  showFilters: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object
};

export default SecurityLog;
