/**
 * NovaCore Assessment Controller
 * 
 * This controller handles API requests related to vendor assessments.
 */

const { AssessmentService } = require('../services');
const logger = require('../../../config/logger');

class AssessmentController {
  /**
   * Create a new assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async createAssessment(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.createAssessment(req.body, userId);
      
      res.status(201).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get all assessments
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAllAssessments(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      // Extract filter parameters from query
      const filter = {};
      
      if (req.query.vendorId) filter.vendorId = req.query.vendorId;
      if (req.query.status) filter.status = req.query.status;
      if (req.query.frameworks) filter.frameworks = req.query.frameworks.split(',');
      if (req.query.tags) filter.tags = req.query.tags.split(',');
      if (req.query.cyberSafetyCertified) filter.cyberSafetyCertified = req.query.cyberSafetyCertified;
      if (req.query.search) filter.search = req.query.search;
      
      // Extract pagination and sorting options
      const options = {
        page: parseInt(req.query.page, 10) || 1,
        limit: parseInt(req.query.limit, 10) || 10
      };
      
      if (req.query.sort) {
        const [field, order] = req.query.sort.split(':');
        options.sort = { [field]: order === 'desc' ? -1 : 1 };
      }
      
      const result = await AssessmentService.getAllAssessments(organizationId, filter, options);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: result.pagination
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get assessment by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAssessmentById(req, res, next) {
    try {
      const assessment = await AssessmentService.getAssessmentById(req.params.id);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateAssessment(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.updateAssessment(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Delete assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async deleteAssessment(req, res, next) {
    try {
      await AssessmentService.deleteAssessment(req.params.id);
      
      res.status(200).json({
        success: true,
        message: 'Assessment deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Submit answer for assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async submitAnswer(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.submitAnswer(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Submit assessment for review
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async submitForReview(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.submitForReview(req.params.id, userId);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Complete assessment review
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async completeReview(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.completeReview(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Add finding to assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async addFinding(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.addFinding(req.params.id, req.body, userId);
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Update finding in assessment
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async updateFinding(req, res, next) {
    try {
      const userId = req.user ? req.user.id : 'system';
      const assessment = await AssessmentService.updateFinding(
        req.params.id, 
        req.params.findingId, 
        req.body, 
        userId
      );
      
      res.status(200).json({
        success: true,
        data: assessment
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * Get assessment documents
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  async getAssessmentDocuments(req, res, next) {
    try {
      const documents = await AssessmentService.getAssessmentDocuments(req.params.id);
      
      res.status(200).json({
        success: true,
        data: documents
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AssessmentController();
