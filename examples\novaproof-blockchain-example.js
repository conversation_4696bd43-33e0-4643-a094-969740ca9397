/**
 * NovaProof Blockchain Example
 * 
 * This example demonstrates how to use the enhanced NovaProof blockchain verification
 * system with different blockchain providers and Merkle tree verification.
 */

const {
  Evidence,
  EvidenceStatus,
  verifyEvidence,
  generateProof,
  verifyProof,
  batchVerifyEvidence,
  BlockchainVerificationManager,
  BlockchainType,
  EthereumProvider,
  HyperledgerProvider,
  MerkleTree,
  evidenceUtils
} = require('../src/novaproof');

// Create a custom blockchain verification manager
const verificationManager = new BlockchainVerificationManager({
  enableLogging: true,
  defaultBlockchainType: BlockchainType.ETHEREUM,
  providers: {
    [BlockchainType.ETHEREUM]: {
      rpcUrl: 'http://localhost:8545',
      gasLimit: 200000
    },
    [BlockchainType.HYPERLEDGER]: {
      connectionProfile: './connection-profile.json',
      channelName: 'novaproof-channel',
      chaincodeName: 'novaproof-chaincode'
    }
  }
});

// Example data for compliance evidence
const evidenceData = {
  controlId: 'C-123',
  framework: 'NIST-CSF',
  source: 'GCP',
  timestamp: new Date().toISOString(),
  data: {
    value: true,
    details: 'Encryption enabled for all storage buckets',
    score: 100
  },
  status: EvidenceStatus.COLLECTED
};

// Create multiple evidence items for batch verification
function createEvidenceItems(count) {
  const items = [];
  
  for (let i = 0; i < count; i++) {
    const item = new Evidence({
      controlId: `C-${i + 100}`,
      framework: 'NIST-CSF',
      source: 'AWS',
      timestamp: new Date().toISOString(),
      data: {
        value: Math.random() > 0.5,
        details: `Evidence item ${i + 1}`,
        score: Math.floor(Math.random() * 100)
      },
      status: EvidenceStatus.COLLECTED
    });
    
    items.push(item);
  }
  
  return items;
}

// Demonstrate Merkle tree functionality
function demonstrateMerkleTree() {
  console.log('\n--- Demonstrating Merkle Tree ---');
  
  // Create some data items
  const dataItems = [
    'item1',
    'item2',
    'item3',
    'item4',
    'item5'
  ];
  
  console.log(`Creating Merkle tree with ${dataItems.length} items...`);
  
  // Create a Merkle tree
  const merkleTree = new MerkleTree(dataItems);
  
  // Get the root hash
  const rootHash = merkleTree.getRootHash();
  console.log(`Merkle root hash: ${rootHash}`);
  
  // Get the leaf hashes
  const leafHashes = merkleTree.getLeafHashes();
  console.log(`Leaf hashes: ${leafHashes.length}`);
  
  // Get a proof for a leaf
  const leafIndex = 2; // Third item (0-based index)
  const proof = merkleTree.getProof(leafIndex);
  console.log(`Proof for leaf ${leafIndex}:`, proof);
  
  // Verify the proof
  const isValid = merkleTree.verifyProof(leafHashes[leafIndex], proof, rootHash);
  console.log(`Proof verification result: ${isValid}`);
  
  // Try with an invalid proof (modify the proof)
  if (proof.length > 0) {
    const invalidProof = [...proof];
    invalidProof[0].hash = 'invalid-hash';
    
    const isInvalidValid = merkleTree.verifyProof(leafHashes[leafIndex], invalidProof, rootHash);
    console.log(`Invalid proof verification result: ${isInvalidValid}`);
  }
}

// Run the example
async function runExample() {
  try {
    console.log('Starting NovaProof Blockchain Example...');
    
    // Demonstrate Merkle tree functionality
    demonstrateMerkleTree();
    
    // Create an evidence item
    console.log('\n--- Verifying Single Evidence Item ---');
    const evidence = new Evidence(evidenceData);
    console.log(`Created evidence: ${evidence.id}`);
    console.log(`Evidence hash: ${evidence.hash()}`);
    
    // Connect to Ethereum blockchain
    console.log('\nConnecting to Ethereum blockchain...');
    await verificationManager.connect(BlockchainType.ETHEREUM);
    
    // Verify the evidence on Ethereum
    console.log('\nVerifying evidence on Ethereum blockchain...');
    const verificationResult = await verificationManager.verifyEvidence(evidence, {
      blockchainType: BlockchainType.ETHEREUM
    });
    
    console.log('Verification result:', verificationResult);
    console.log('Evidence status after verification:', evidence.status);
    console.log('Latest verification:', evidence.getLatestVerification());
    
    // Generate a proof for the verification
    console.log('\nGenerating proof for the verification...');
    const latestVerification = evidence.getLatestVerification();
    const transaction = {
      transactionId: latestVerification.blockchainReference.transactionId,
      contentHash: latestVerification.contentHash
    };
    
    const proof = verificationManager.generateProof(evidence, transaction);
    console.log('Proof generated:', proof);
    
    // Verify the proof
    console.log('\nVerifying the proof...');
    const isValid = verificationManager.verifyProof(
      proof.contentHash,
      proof.merkleRoot,
      proof.merkleProof
    );
    
    console.log('Proof is valid:', isValid);
    
    // Batch verification
    console.log('\n--- Batch Verifying Multiple Evidence Items ---');
    const evidenceItems = createEvidenceItems(5);
    console.log(`Created ${evidenceItems.length} evidence items`);
    
    // Batch verify on Ethereum
    console.log('\nBatch verifying evidence items on Ethereum blockchain...');
    const batchResults = await verificationManager.batchVerifyEvidence(evidenceItems, {
      blockchainType: BlockchainType.ETHEREUM
    });
    
    console.log(`Batch verification completed for ${batchResults.length} items`);
    console.log('First batch result:', batchResults[0]);
    
    // Check the status of all evidence items
    console.log('\nStatus of all evidence items after batch verification:');
    evidenceItems.forEach((item, index) => {
      console.log(`Item ${index + 1}: ${item.status}`);
    });
    
    // Connect to Hyperledger blockchain
    console.log('\n--- Using Hyperledger Fabric ---');
    console.log('\nConnecting to Hyperledger Fabric blockchain...');
    
    try {
      await verificationManager.connect(BlockchainType.HYPERLEDGER);
      
      // Create a new evidence item for Hyperledger
      const hyperledgerEvidence = new Evidence({
        ...evidenceData,
        controlId: 'C-456',
        source: 'Azure'
      });
      
      console.log(`Created evidence for Hyperledger: ${hyperledgerEvidence.id}`);
      
      // Verify on Hyperledger
      console.log('\nVerifying evidence on Hyperledger Fabric blockchain...');
      const hyperledgerResult = await verificationManager.verifyEvidence(hyperledgerEvidence, {
        blockchainType: BlockchainType.HYPERLEDGER
      });
      
      console.log('Hyperledger verification result:', hyperledgerResult);
    } catch (error) {
      console.log('Note: Hyperledger verification is simulated and would require an actual Fabric network in production.');
    }
    
    // Disconnect from blockchains
    console.log('\nDisconnecting from blockchains...');
    await verificationManager.disconnect(BlockchainType.ETHEREUM);
    if (verificationManager.getProvider(BlockchainType.HYPERLEDGER).isConnected) {
      await verificationManager.disconnect(BlockchainType.HYPERLEDGER);
    }
    
    console.log('\nExample completed successfully!');
  } catch (error) {
    console.error('Error in example:', error);
  }
}

// Run the example
runExample();
