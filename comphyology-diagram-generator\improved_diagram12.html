<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>12. Cyber-Safety Incident Response Workflow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>12. Cyber-Safety Incident Response Workflow</h1>

    <div class="diagram-container">
        <!-- Incident Response -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Cyber-Safety Incident Response Workflow
            <div class="element-number">1</div>
        </div>

        <!-- Detection Phase -->
        <div class="element" style="top: 150px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Detection Phase
            <div class="element-number">2</div>
        </div>

        <div class="element" style="top: 250px; left: 150px; width: 250px; font-size: 14px;">
            Continuous Monitoring (NovaShield)
            <div class="element-number">3</div>
        </div>

        <div class="element" style="top: 350px; left: 50px; width: 200px; font-size: 12px;">
            Resonance Index <span class="bold-formula">(φindex)</span><br>Identifies anomalies
            <div class="element-number">4</div>
        </div>

        <div class="element" style="top: 350px; left: 275px; width: 200px; font-size: 12px;">
            Signal-to-Noise Ratio<br><span class="bold-formula">φ</span>-harmonic sensing
            <div class="element-number">5</div>
        </div>

        <div class="element" style="top: 350px; left: 500px; width: 200px; font-size: 12px;">
            Trinity Equation<br>Assesses G, D, R aspects
            <div class="element-number">6</div>
        </div>

        <!-- Automated Triage -->
        <div class="element" style="top: 250px; left: 600px; width: 250px; font-size: 14px;">
            Automated Triage (NovaCore)
            <div class="element-number">7</div>
        </div>

        <div class="element" style="top: 350px; left: 725px; width: 200px; font-size: 12px;">
            UUFT Equation<br>Analyzes characteristics
            <div class="element-number">8</div>
        </div>

        <!-- Alert Generation -->
        <div class="element" style="top: 450px; left: 300px; width: 400px; font-size: 14px;">
            Alert Generation (NovaShield)
            <div class="element-number">9</div>
        </div>

        <div class="element" style="top: 550px; left: 150px; width: 200px; font-size: 12px;">
            Trust Equation<br>Evaluates credibility
            <div class="element-number">10</div>
        </div>

        <div class="element" style="top: 550px; left: 400px; width: 200px; font-size: 12px;">
            Adaptive Coherence<br>Determines urgency
            <div class="element-number">11</div>
        </div>

        <div class="element" style="top: 550px; left: 650px; width: 200px; font-size: 12px;">
            18/82 Principle<br>Prioritizes high-impact
            <div class="element-number">12</div>
        </div>

        <!-- Analysis Phase -->
        <div class="element" style="top: 650px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Analysis Phase
            <div class="element-number">13</div>
        </div>

        <div class="element" style="top: 750px; left: 150px; width: 250px; font-size: 14px;">
            Pattern Recognition (NovaThink)
            <div class="element-number">14</div>
        </div>

        <div class="element" style="top: 750px; left: 600px; width: 250px; font-size: 14px;">
            Impact Assessment (NovaTrack)
            <div class="element-number">15</div>
        </div>

        <!-- Connections -->
        <!-- Connect Incident Response to Detection Phase -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Connect Detection Phase to components -->
        <!-- Lines removed as requested -->

        <div class="connection" style="top: 200px; left: 725px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 700px; width: 25px; height: 2px;"></div>

        <!-- Connect Continuous Monitoring to subcomponents -->
        <!-- Lines removed as requested -->

        <!-- Lines removed as requested -->

        <!-- Lines removed as requested -->

        <!-- Connect Automated Triage to subcomponents -->
        <div class="connection" style="top: 300px; left: 725px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 300px; left: 725px; width: 100px; height: 2px;"></div>

        <!-- Connect to Alert Generation -->
        <div class="connection" style="top: 400px; left: 275px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 400px; left: 275px; width: 25px; height: 2px;"></div>

        <div class="connection" style="top: 400px; left: 725px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 400px; left: 700px; width: 25px; height: 2px;"></div>

        <!-- Connect Alert Generation to subcomponents -->
        <div class="connection" style="top: 500px; left: 250px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 500px; left: 250px; width: 50px; height: 2px;"></div>

        <div class="connection" style="top: 500px; left: 500px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 500px; left: 500px; width: 100px; height: 2px;"></div>

        <div class="connection" style="top: 500px; left: 750px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 500px; left: 700px; width: 50px; height: 2px;"></div>

        <!-- Connect to Analysis Phase -->
        <div class="connection" style="top: 600px; left: 500px; width: 2px; height: 50px;"></div>

        <!-- Connect Analysis Phase to components -->
        <div class="connection" style="top: 700px; left: 275px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 700px; left: 275px; width: 25px; height: 2px;"></div>

        <div class="connection" style="top: 700px; left: 725px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 700px; left: 700px; width: 25px; height: 2px;"></div>
    </div>
</body>
</html>
