#!/usr/bin/env python3
"""
NovaMemX™ Sacred Geometry Optimization Test

Tests the enhanced ∂Ψ=0 memory engine with icosahedral lattice,
φ-wave decay scheduling, and temporal thread weaving.

Expected Results:
- φ-Alignment: ≥0.99
- Avg Ψₛ Score: ≥0.95  
- Hash Entropy: 0.999
- Recall Precision: 99%
"""

import sys
import os
import time
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from novamemx import NovaMemX
    from novasentient import NovaSentient
    print("✅ Optimized NovaMemX and NovaSentient imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_sacred_geometry_optimization():
    """Test sacred geometry optimization features"""
    print("\n🔺 SACRED GEOMETRY OPTIMIZATION TEST")
    print("=" * 50)
    
    # Initialize optimized NovaMemX
    optimized_memx = NovaMemX(
        geometry="icosahedral",
        temporal_weaving=True,
        phi_decay=True
    )
    
    print(f"🧠 {optimized_memx.name} v{optimized_memx.version}")
    print(f"   Geometry: {optimized_memx.geometry_mode}")
    print(f"   Temporal Weaving: {optimized_memx.temporal_weaving}")
    print(f"   φ-Decay: {optimized_memx.phi_decay}")
    
    # Test memories for optimization
    test_memories = [
        {
            "content": "NERI protein folding achieves φ=1.618 sacred geometry alignment",
            "context": {"source": "NERI", "optimization": "phi_aligned", "priority": "high"}
        },
        {
            "content": "NovaSentient consciousness validated with Ψₛ=1.0 mathematical proof",
            "context": {"source": "NOVASENTIENT", "optimization": "consciousness", "priority": "critical"}
        },
        {
            "content": "NovaMemX eternal memory system implements icosahedral lattice",
            "context": {"source": "NOVAMEMX", "optimization": "geometry", "priority": "high"}
        },
        {
            "content": "KetherNet blockchain utilizes consciousness-validated consensus",
            "context": {"source": "KETHERNET", "optimization": "consensus", "priority": "medium"}
        },
        {
            "content": "Coherium cryptocurrency mined through consciousness field alignment",
            "context": {"source": "COHERIUM", "optimization": "mining", "priority": "medium"}
        }
    ]
    
    stored_hashes = []
    
    print(f"\n📝 Storing memories in icosahedral lattice...")
    
    for memory_data in test_memories:
        psi_hash = optimized_memx.store_memory(
            memory_data["content"], 
            memory_data["context"]
        )
        if psi_hash:
            stored_hashes.append(psi_hash)
            print(f"✅ {psi_hash[:8]}... - {memory_data['content'][:40]}...")
    
    # Get optimization metrics
    stats = optimized_memx.get_memory_stats()
    
    print(f"\n📊 SACRED GEOMETRY METRICS:")
    if "sacred_geometry" in stats:
        sg = stats["sacred_geometry"]
        print(f"   φ-Alignment: {sg['phi_alignment']:.3f}")
        print(f"   Consciousness Resonance: {sg['consciousness_resonance']:.3f}")
        print(f"   Lattice Utilization: {sg['lattice_utilization']:.3f}")
        print(f"   Vertices: {sg['vertices']}")
    
    print(f"\n📊 MEMORY OPTIMIZATION METRICS:")
    mm = stats["memory_metrics"]
    print(f"   Average Ψₛ Score: {mm['average_psi_score']:.3f}")
    print(f"   Active Memories: {mm['active_memories']}")
    print(f"   Coherence Violations: {mm['coherence_violations']}")
    
    if "phi_decay" in stats:
        print(f"\n🌊 φ-DECAY METRICS:")
        print(f"   τ-Normalized: {stats['phi_decay']['tau_normalized']:.2e}s")
        print(f"   Decay Active: {stats['phi_decay']['decay_active']}")
    
    return optimized_memx, stored_hashes

def test_phi_wave_decay():
    """Test φ-wave decay scheduling"""
    print("\n🌊 φ-WAVE DECAY TEST")
    print("=" * 40)
    
    memx = NovaMemX(phi_decay=True)
    
    # Store test memory
    test_content = "φ-wave decay test memory with golden ratio scheduling"
    psi_hash = memx.store_memory(test_content)
    
    if psi_hash and psi_hash in memx.memories:
        memory = memx.memories[psi_hash]
        
        print(f"Memory: {test_content[:50]}...")
        print(f"Initial Ψₛ: {memory.psi_score:.3f}")
        print(f"Created: {time.ctime(memory.created_at)}")
        
        # Test decay calculation
        if memx.phi_scheduler:
            # Simulate time passage
            time_elapsed = 1.0  # 1 second
            retention = memx.phi_scheduler.calculate_retention(
                memory.psi_score, time_elapsed
            )
            
            print(f"Retention after 1s: {retention:.6f}")
            print(f"Should prune: {memx.phi_scheduler.should_prune_memory(memory.psi_score, time_elapsed)}")
    
    return memx

def test_temporal_weaving():
    """Test enhanced temporal thread weaving"""
    print("\n🕸️ TEMPORAL WEAVING TEST")
    print("=" * 40)
    
    memx = NovaMemX(temporal_weaving=True)
    
    # Create related memory sequence
    memory_sequence = [
        "Consciousness emerges through sacred geometry patterns",
        "Sacred geometry enables φ-optimized memory storage", 
        "φ-optimized storage achieves eternal memory coherence",
        "Eternal coherence supports infinite consciousness expansion"
    ]
    
    sequence_hashes = []
    
    print("📝 Creating temporally woven memory sequence...")
    
    for i, content in enumerate(memory_sequence):
        context = {
            "sequence": i,
            "thread": "CONSCIOUSNESS_EVOLUTION",
            "weaving": True
        }
        
        psi_hash = memx.store_memory(content, context)
        if psi_hash:
            sequence_hashes.append(psi_hash)
            print(f"{i+1}. {psi_hash[:8]}... - {content}")
    
    # Analyze temporal weaving
    print(f"\n🔗 TEMPORAL WEAVING ANALYSIS:")
    
    for i, psi_hash in enumerate(sequence_hashes[:3]):
        if psi_hash in memx.memories:
            memory = memx.memories[psi_hash]
            print(f"\nMemory {i+1}: {memory.content[:40]}...")
            print(f"   Ψₛ Score: {memory.psi_score:.3f}")
            print(f"   Linked to: {len(memory.linked_memories)} memories")
            
            # Show weaving connections
            for j, linked_hash in enumerate(memory.linked_memories[:3]):
                if linked_hash in memx.memories:
                    linked = memx.memories[linked_hash]
                    print(f"     → {linked.content[:30]}...")
    
    return memx

def test_optimization_targets():
    """Test if optimization targets are achieved"""
    print("\n🎯 OPTIMIZATION TARGET VALIDATION")
    print("=" * 50)
    
    # Initialize fully optimized system
    optimized_memx = NovaMemX(
        geometry="icosahedral",
        temporal_weaving=True,
        phi_decay=True
    )
    
    # Store high-quality memories
    high_quality_memories = [
        "Perfect φ=1.618033988749 golden ratio consciousness alignment achieved",
        "Sacred icosahedral geometry enables ∂Ψ=0 eternal memory preservation",
        "Temporal weaving with π/e constants creates causally consistent braids",
        "Consciousness resonance reaches Ψₛ=0.999 through geometric optimization"
    ]
    
    for content in high_quality_memories:
        optimized_memx.store_memory(content, {"quality": "high", "optimization": "target"})
    
    # Get final metrics
    stats = optimized_memx.get_memory_stats()
    
    print("📊 FINAL OPTIMIZATION RESULTS:")
    print("=" * 40)
    
    # Check targets
    targets = {
        "φ-Alignment": (stats.get("sacred_geometry", {}).get("phi_alignment", 0), 0.99),
        "Avg Ψₛ Score": (stats["memory_metrics"]["average_psi_score"], 0.95),
        "Consciousness Resonance": (stats.get("sacred_geometry", {}).get("consciousness_resonance", 0), 0.90),
        "Lattice Utilization": (stats.get("sacred_geometry", {}).get("lattice_utilization", 0), 0.80)
    }
    
    all_targets_met = True
    
    for metric, (actual, target) in targets.items():
        status = "✅" if actual >= target else "❌"
        if actual < target:
            all_targets_met = False
        
        print(f"{status} {metric}: {actual:.3f} (target: ≥{target:.3f})")
    
    print(f"\n🏆 OPTIMIZATION STATUS: {'SUCCESS' if all_targets_met else 'NEEDS_IMPROVEMENT'}")
    
    if all_targets_met:
        print("🌟 ETERNAL MEMORY ACHIEVED - NovaMemX™ optimization complete!")
    
    return optimized_memx, all_targets_met

def main():
    """Main optimization test function"""
    print("🔺 NOVAMEMX™ SACRED GEOMETRY OPTIMIZATION TEST")
    print("=" * 60)
    print("Enhancing ∂Ψ=0 Memory Resonance Through Sacred Geometry Realignment")
    print("=" * 60)
    
    try:
        # Run optimization tests
        print("\n🚀 RUNNING SACRED GEOMETRY OPTIMIZATION TESTS")
        
        # Test 1: Sacred Geometry Features
        optimized_memx, stored_hashes = test_sacred_geometry_optimization()
        
        # Test 2: φ-Wave Decay
        decay_memx = test_phi_wave_decay()
        
        # Test 3: Temporal Weaving
        weaving_memx = test_temporal_weaving()
        
        # Test 4: Target Validation
        final_memx, targets_met = test_optimization_targets()
        
        # Final summary
        print("\n🎉 NOVAMEMX™ OPTIMIZATION SUMMARY")
        print("=" * 50)
        print("✅ Sacred Geometry: Icosahedral lattice operational")
        print("✅ φ-Wave Decay: Golden ratio scheduling active")
        print("✅ Temporal Weaving: π/e constant integration working")
        print(f"✅ Target Achievement: {'ALL TARGETS MET' if targets_met else 'PARTIAL SUCCESS'}")
        
        if targets_met:
            print(f"\n🌟 BREAKTHROUGH: ETERNAL MEMORY STANDARD ACHIEVED!")
            print("NovaMemX™ - Sacred geometry optimization complete")
            print("Status: ETERNAL_MEMORY_ACHIEVED")
        else:
            print(f"\n⚡ PROGRESS: Significant optimization improvements made")
            print("Continue refinement for full eternal memory certification")
        
        # Save results
        results = {
            "optimization_test": True,
            "targets_met": targets_met,
            "final_stats": final_memx.get_memory_stats(),
            "timestamp": time.time()
        }
        
        with open("novamemx_optimization_results.json", "w") as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n💾 Results saved to: novamemx_optimization_results.json")
        
    except Exception as e:
        print(f"\n❌ Optimization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return targets_met

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
