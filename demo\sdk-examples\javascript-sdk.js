// NovaFuse JavaScript SDK Example

class NovaFuseClient {
    constructor(config) {
        this.apiKey = config.apiKey;
        this.baseUrl = config.baseUrl || "https://api.novafuse.io";
    }

    async request(endpoint, method = "GET", body = null, queryParams = {}) {
        const url = new URL(`${this.baseUrl}${endpoint}`);
        
        // Add query parameters
        Object.keys(queryParams).forEach(key => {
            if (queryParams[key] !== null && queryParams[key] !== undefined) {
                url.searchParams.append(key, queryParams[key]);
            }
        });

        const headers = {
            "Content-Type": "application/json",
            "apikey": this.apiKey
        };

        const options = {
            method,
            headers
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(url.toString(), options);
            
            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error("NovaFuse API request failed:", error);
            throw error;
        }
    }

    // Governance API
    async getBoardMeetings(params = {}) {
        return this.request("/governance/board/meetings", "GET", null, params);
    }

    async getBoardMeeting(id) {
        return this.request(`/governance/board/meetings/${id}`);
    }

    async getPolicies(params = {}) {
        return this.request("/governance/policies", "GET", null, params);
    }

    async getPolicy(id) {
        return this.request(`/governance/policies/${id}`);
    }

    // Security API
    async getVulnerabilities(params = {}) {
        return this.request("/security/vulnerabilities", "GET", null, params);
    }

    async getVulnerability(id) {
        return this.request(`/security/vulnerabilities/${id}`);
    }

    async runSecurityScan(config) {
        return this.request("/security/scan", "POST", config);
    }

    // APIs & Developer Tools API
    async getApiCatalog(params = {}) {
        return this.request("/apis/catalog", "GET", null, params);
    }

    async getApiDetails(id) {
        return this.request(`/apis/catalog/${id}`);
    }

    async getApiMetrics(params = {}) {
        return this.request("/apis/metrics", "GET", null, params);
    }
}

// Example usage
async function main() {
    // Initialize the client
    const client = new NovaFuseClient({
        apiKey: "your-api-key",
        baseUrl: "https://api.novafuse.io"
    });

    try {
        // Get board meetings
        console.log("Fetching board meetings...");
        const meetings = await client.getBoardMeetings({ status: "scheduled" });
        console.log(`Found ${meetings.data.length} scheduled meetings`);
        
        // Get details of the first meeting
        if (meetings.data.length > 0) {
            const meetingId = meetings.data[0].id;
            console.log(`Fetching details for meeting ${meetingId}...`);
            const meetingDetails = await client.getBoardMeeting(meetingId);
            console.log("Meeting details:", meetingDetails.data);
        }
        
        // Get vulnerabilities
        console.log("Fetching vulnerabilities...");
        const vulnerabilities = await client.getVulnerabilities({ severity: "high" });
        console.log(`Found ${vulnerabilities.data.length} high severity vulnerabilities`);
        
        // Get API catalog
        console.log("Fetching API catalog...");
        const apis = await client.getApiCatalog({ status: "active" });
        console.log(`Found ${apis.data.length} active APIs`);
        
    } catch (error) {
        console.error("Error in example:", error);
    }
}

// Run the example
main();
