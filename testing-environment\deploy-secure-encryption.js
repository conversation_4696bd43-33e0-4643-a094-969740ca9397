/**
 * Deployment Script for Secure Encryption
 * 
 * This script deploys the secure encryption implementation for the NovaConnect Universal API Connector.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Ask a question and get the answer
 * 
 * @param {string} question - The question to ask
 * @returns {Promise<string>} - The answer
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

/**
 * Run a command and return the output
 * 
 * @param {string} command - The command to run
 * @returns {string} - The command output
 */
function runCommand(command) {
  try {
    return execSync(command, { encoding: 'utf8' });
  } catch (error) {
    console.error(`${colors.red}Error running command: ${command}${colors.reset}`);
    console.error(`${colors.red}${error.message}${colors.reset}`);
    return '';
  }
}

/**
 * Deploy secure encryption
 */
async function deploySecureEncryption() {
  console.log(`${colors.bright}${colors.magenta}=== NovaConnect Secure Encryption Deployment ===${colors.reset}\n`);
  
  // Step 1: Integration
  console.log(`${colors.bright}${colors.blue}Step 1: Integration${colors.reset}`);
  console.log(`${colors.yellow}Integrating secure services into the main application...${colors.reset}`);
  
  try {
    runCommand('node integration/integrate-secure-services.js');
    console.log(`${colors.green}✓ Integration complete${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.red}✗ Integration failed: ${error.message}${colors.reset}\n`);
    const proceed = await askQuestion(`${colors.yellow}Do you want to continue? (y/n) ${colors.reset}`);
    if (proceed.toLowerCase() !== 'y') {
      rl.close();
      return;
    }
  }
  
  // Step 2: Configuration
  console.log(`${colors.bright}${colors.blue}Step 2: Configuration${colors.reset}`);
  console.log(`${colors.yellow}Setting up environment variables for encryption keys...${colors.reset}`);
  
  try {
    runCommand('node integration/configure-encryption.js');
    console.log(`${colors.green}✓ Configuration complete${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.red}✗ Configuration failed: ${error.message}${colors.reset}\n`);
    const proceed = await askQuestion(`${colors.yellow}Do you want to continue? (y/n) ${colors.reset}`);
    if (proceed.toLowerCase() !== 'y') {
      rl.close();
      return;
    }
  }
  
  // Step 3: Create log directories
  console.log(`${colors.bright}${colors.blue}Step 3: Setting up logging${colors.reset}`);
  console.log(`${colors.yellow}Creating log directories...${colors.reset}`);
  
  try {
    // Create logs directory
    const logsDir = path.resolve(__dirname, 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    console.log(`${colors.green}✓ Log directories created${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.red}✗ Failed to create log directories: ${error.message}${colors.reset}\n`);
    const proceed = await askQuestion(`${colors.yellow}Do you want to continue? (y/n) ${colors.reset}`);
    if (proceed.toLowerCase() !== 'y') {
      rl.close();
      return;
    }
  }
  
  // Step 4: Initialize key backup
  console.log(`${colors.bright}${colors.blue}Step 4: Initializing key backup${colors.reset}`);
  console.log(`${colors.yellow}Setting up key backup system...${colors.reset}`);
  
  try {
    // Create a simple script to initialize key backup
    const initBackupScript = `
      const keyBackup = require('./utils/key-backup');
      keyBackup.initializeBackup();
    `;
    
    fs.writeFileSync(path.resolve(__dirname, 'init-backup.js'), initBackupScript);
    runCommand('node init-backup.js');
    
    console.log(`${colors.green}✓ Key backup initialized${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.red}✗ Failed to initialize key backup: ${error.message}${colors.reset}\n`);
    const proceed = await askQuestion(`${colors.yellow}Do you want to continue? (y/n) ${colors.reset}`);
    if (proceed.toLowerCase() !== 'y') {
      rl.close();
      return;
    }
  }
  
  // Step 5: Run encryption tests
  console.log(`${colors.bright}${colors.blue}Step 5: Running encryption tests${colors.reset}`);
  console.log(`${colors.yellow}Verifying encryption implementation...${colors.reset}`);
  
  try {
    runCommand('node api-connection-testing/encryption-test.js');
    console.log(`${colors.green}✓ Encryption tests complete${colors.reset}\n`);
  } catch (error) {
    console.log(`${colors.red}✗ Encryption tests failed: ${error.message}${colors.reset}\n`);
    const proceed = await askQuestion(`${colors.yellow}Do you want to continue? (y/n) ${colors.reset}`);
    if (proceed.toLowerCase() !== 'y') {
      rl.close();
      return;
    }
  }
  
  // Step 6: Start services
  console.log(`${colors.bright}${colors.blue}Step 6: Starting services${colors.reset}`);
  console.log(`${colors.yellow}Starting secure services...${colors.reset}`);
  
  const startServices = await askQuestion(`${colors.yellow}Do you want to start the services now? (y/n) ${colors.reset}`);
  
  if (startServices.toLowerCase() === 'y') {
    try {
      console.log(`${colors.yellow}Starting services...${colors.reset}`);
      runCommand('npm run start:all');
    } catch (error) {
      console.log(`${colors.red}✗ Failed to start services: ${error.message}${colors.reset}\n`);
    }
  } else {
    console.log(`${colors.yellow}Skipping service startup${colors.reset}\n`);
  }
  
  // Deployment complete
  console.log(`${colors.bright}${colors.green}=== Deployment Complete ===${colors.reset}\n`);
  console.log(`${colors.green}The NovaConnect Universal API Connector now has strong encryption capabilities.${colors.reset}`);
  console.log(`${colors.yellow}Important: Keep your encryption keys and backup passwords secure.${colors.reset}`);
  console.log(`${colors.yellow}Refer to the documentation for more information on the encryption implementation.${colors.reset}\n`);
  
  rl.close();
}

// Run the deployment
deploySecureEncryption();
