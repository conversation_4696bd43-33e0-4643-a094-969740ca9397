import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import BackButton from '../../components/BackButton';

// Mock the Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn()
}));

// Import the mocked router
const useRouter = jest.requireMock('next/router').useRouter;

describe('BackButton', () => {
  beforeEach(() => {
    // Reset the router mock before each test
    useRouter.mockReset();
  });

  it('renders the back button correctly', () => {
    // Mock the router
    useRouter.mockReturnValue({
      back: jest.fn(),
      push: jest.fn()
    });

    render(<BackButton />);

    // Check if the button is rendered
    const button = screen.getByRole('button', { name: /go back/i });
    expect(button).toBeInTheDocument();

    // Check if the button has the correct text
    expect(button).toHaveTextContent('Back');

    // Check if the SVG icon is rendered
    const svg = button.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('accepts a custom fallbackHref prop', () => {
    // Mock the router
    useRouter.mockReturnValue({
      back: jest.fn(),
      push: jest.fn()
    });

    // Render with custom fallbackHref
    render(<BackButton fallbackHref="/custom-path" />);

    // Check if the button is rendered
    expect(screen.getByRole('button', { name: /go back/i })).toBeInTheDocument();
  });

  it('calls router.back when clicked', () => {
    // Mock the router
    const mockBack = jest.fn();
    const mockPush = jest.fn();
    useRouter.mockReturnValue({
      back: mockBack,
      push: mockPush
    });

    // Mock window.history.length
    const originalHistoryLength = window.history.length;
    Object.defineProperty(window.history, 'length', {
      configurable: true,
      value: 2 // More than 1 to trigger router.back()
    });

    render(<BackButton />);

    // Click the back button
    fireEvent.click(screen.getByRole('button', { name: /go back/i }));

    // Restore original history length
    Object.defineProperty(window.history, 'length', {
      configurable: true,
      value: originalHistoryLength
    });

    // Check if router.back was called
    expect(mockBack).toHaveBeenCalled();
  });
});
