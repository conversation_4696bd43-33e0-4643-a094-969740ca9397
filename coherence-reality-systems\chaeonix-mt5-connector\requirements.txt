# CHAEONIX MT5 CONNECTOR REQUIREMENTS
# Divine Trading with MetaTrader 5 Integration

# Core MT5 Integration
MetaTrader5>=5.0.45
pandas>=1.5.0
numpy>=1.21.0

# Async and Networking
asyncio-mqtt>=0.11.1
websockets>=10.4
aiohttp>=3.8.0

# Data Analysis and Visualization
matplotlib>=3.6.0
plotly>=5.11.0
seaborn>=0.12.0

# Financial Data
yfinance>=0.1.87
alpha-vantage>=2.3.1

# Sacred Mathematics
scipy>=1.9.0
sympy>=1.11.1

# Logging and Configuration
python-dotenv>=0.21.0
colorlog>=6.7.0

# API Development
fastapi>=0.85.0
uvicorn>=0.18.0
pydantic>=1.10.0

# Database (for trade logging)
sqlite3  # Built-in
sqlalchemy>=1.4.0

# Encryption (for φ-protection)
cryptography>=38.0.0
hashlib  # Built-in

# Time and Date
python-dateutil>=2.8.2
pytz>=2022.6

# Testing
pytest>=7.2.0
pytest-asyncio>=0.20.0
