/**
 * NovaVision Verification Details
 * 
 * This component displays detailed verification information for a NovaStore component,
 * including Trinity CSDE metrics, Data Quality Framework results, and adaptive ratios.
 */

const React = require('react');
const PropTypes = require('prop-types');

// Import NovaVision core components
const { 
  Card, 
  Tabs, 
  Tab, 
  Chart, 
  ProgressBar, 
  Icon, 
  TrinitySymbol 
} = require('../core');

/**
 * Verification Details
 * @param {Object} props - Component props
 * @returns {JSX.Element} - Verification details
 */
function VerificationDetails({ verificationResult }) {
  // Format percentage
  const formatPercent = (value) => `${(value * 100).toFixed(2)}%`;
  
  // Get component info
  const {
    componentId,
    componentName,
    verificationLevel,
    verificationScore,
    verificationStatus,
    qualityMetrics,
    adaptiveRatios,
    revenueShare
  } = verificationResult;
  
  // Status colors
  const statusColors = {
    verified: '#4CAF50',
    rejected: '#F44336',
    unverified: '#FF9800'
  };
  
  // Prepare data for charts
  const qualityData = {
    labels: ['Governance', 'Detection', 'Response', 'Overall'],
    datasets: [{
      label: 'Quality Metrics',
      data: [
        qualityMetrics.governance,
        qualityMetrics.detection,
        qualityMetrics.response,
        qualityMetrics.overall
      ],
      backgroundColor: [
        'rgba(255, 107, 107, 0.5)',
        'rgba(78, 205, 196, 0.5)',
        'rgba(69, 183, 209, 0.5)',
        'rgba(108, 92, 231, 0.5)'
      ],
      borderColor: [
        'rgba(255, 107, 107, 1)',
        'rgba(78, 205, 196, 1)',
        'rgba(69, 183, 209, 1)',
        'rgba(108, 92, 231, 1)'
      ],
      borderWidth: 1
    }]
  };
  
  const adaptiveRatiosData = {
    labels: ['Father (α)', 'Son (β)', 'Spirit (γ)'],
    datasets: [{
      label: 'Initial',
      data: [0.18, 0.18, 0.18],
      backgroundColor: 'rgba(54, 162, 235, 0.5)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    }, {
      label: 'Optimized',
      data: [
        adaptiveRatios.father,
        adaptiveRatios.son,
        adaptiveRatios.spirit
      ],
      backgroundColor: 'rgba(255, 99, 132, 0.5)',
      borderColor: 'rgba(255, 99, 132, 1)',
      borderWidth: 1
    }]
  };
  
  const revenueShareData = {
    labels: ['NovaFuse', 'Partners'],
    datasets: [{
      data: [
        revenueShare.novaFuse,
        revenueShare.partners
      ],
      backgroundColor: [
        'rgba(108, 92, 231, 0.5)',
        'rgba(0, 184, 148, 0.5)'
      ],
      borderColor: [
        'rgba(108, 92, 231, 1)',
        'rgba(0, 184, 148, 1)'
      ],
      borderWidth: 1
    }]
  };
  
  return (
    <Card className="nova-verification-details">
      {/* Header */}
      <div className="nova-verification-header">
        <h2 className="nova-verification-title">
          <TrinitySymbol size={24} />
          <span>Verification Details</span>
        </h2>
        
        <div className="nova-verification-meta">
          <div className="nova-component-id">ID: {componentId}</div>
          <div className="nova-component-name">Name: {componentName}</div>
          <div className="nova-verification-level">Level: {verificationLevel}</div>
        </div>
      </div>
      
      {/* Verification Status */}
      <div className="nova-verification-status">
        <div 
          className="nova-status-indicator"
          style={{ color: statusColors[verificationStatus] }}
        >
          <Icon 
            name={verificationStatus === 'verified' ? 'check-circle' : 'warning'} 
            size={24} 
          />
          <span className="nova-status-text">
            {verificationStatus.toUpperCase()}
          </span>
        </div>
        
        <div className="nova-verification-score">
          <div className="nova-score-label">Verification Score</div>
          <div className="nova-score-value">{formatPercent(verificationScore)}</div>
          <ProgressBar 
            value={verificationScore}
            max={1}
            color={statusColors[verificationStatus]}
          />
        </div>
      </div>
      
      {/* Detailed Information */}
      <Tabs>
        {/* Trinity CSDE Tab */}
        <Tab label="Trinity CSDE">
          <div className="nova-trinity-csde">
            <div className="nova-trinity-description">
              <h3>Trinity CSDE Formula</h3>
              <div className="nova-formula">CSDE_Trinity = πG + ϕD + (ℏ + c^-1)R</div>
              <p>
                The Trinity CSDE formula combines Governance (Father), Detection (Son), 
                and Response (Spirit) components using universal constants to create a 
                comprehensive cyber security assessment.
              </p>
            </div>
            
            <div className="nova-trinity-components">
              <div className="nova-trinity-component">
                <h4>Father (Governance)</h4>
                <div className="nova-formula">πG = π × governanceScore</div>
                <ProgressBar 
                  value={qualityMetrics.governance}
                  max={1}
                  color="#FF6B6B"
                  showLabel
                  label={formatPercent(qualityMetrics.governance)}
                />
              </div>
              
              <div className="nova-trinity-component">
                <h4>Son (Detection)</h4>
                <div className="nova-formula">ϕD = ϕ × detectionScore</div>
                <ProgressBar 
                  value={qualityMetrics.detection}
                  max={1}
                  color="#4ECDC4"
                  showLabel
                  label={formatPercent(qualityMetrics.detection)}
                />
              </div>
              
              <div className="nova-trinity-component">
                <h4>Spirit (Response)</h4>
                <div className="nova-formula">(ℏ + c^-1)R = (ℏ + c^-1) × responseScore</div>
                <ProgressBar 
                  value={qualityMetrics.response}
                  max={1}
                  color="#45B7D1"
                  showLabel
                  label={formatPercent(qualityMetrics.response)}
                />
              </div>
            </div>
            
            <div className="nova-trinity-chart">
              <h3>Quality Metrics</h3>
              <Chart 
                type="radar"
                data={qualityData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 1
                    }
                  }
                }}
              />
            </div>
          </div>
        </Tab>
        
        {/* Data Quality Tab */}
        <Tab label="Data Quality">
          <div className="nova-data-quality">
            <div className="nova-quality-description">
              <h3>UUFT Data Quality Framework</h3>
              <div className="nova-formula">DQFramework = (S ⊗ V ⊕ C) × π10³</div>
              <p>
                The UUFT Data Quality Framework assesses the quality of data using 
                tensor products and fusion operators, combined with the π10³ factor 
                for circular trust verification.
              </p>
            </div>
            
            <div className="nova-quality-components">
              <div className="nova-quality-component">
                <h4>Source Metrics (S)</h4>
                <div className="nova-metrics-grid">
                  <div className="nova-metric">
                    <div className="nova-metric-label">Completeness</div>
                    <ProgressBar 
                      value={0.85}
                      max={1}
                      color="#FF6B6B"
                      showLabel
                      label="85%"
                    />
                  </div>
                  
                  <div className="nova-metric">
                    <div className="nova-metric-label">Timeliness</div>
                    <ProgressBar 
                      value={0.9}
                      max={1}
                      color="#FF6B6B"
                      showLabel
                      label="90%"
                    />
                  </div>
                  
                  <div className="nova-metric">
                    <div className="nova-metric-label">Provenance</div>
                    <ProgressBar 
                      value={0.8}
                      max={1}
                      color="#FF6B6B"
                      showLabel
                      label="80%"
                    />
                  </div>
                </div>
              </div>
              
              <div className="nova-quality-component">
                <h4>Validation Metrics (V)</h4>
                <div className="nova-metrics-grid">
                  <div className="nova-metric">
                    <div className="nova-metric-label">Consistency</div>
                    <ProgressBar 
                      value={0.85}
                      max={1}
                      color="#4ECDC4"
                      showLabel
                      label="85%"
                    />
                  </div>
                  
                  <div className="nova-metric">
                    <div className="nova-metric-label">Accuracy</div>
                    <ProgressBar 
                      value={0.8}
                      max={1}
                      color="#4ECDC4"
                      showLabel
                      label="80%"
                    />
                  </div>
                  
                  <div className="nova-metric">
                    <div className="nova-metric-label">Precision</div>
                    <ProgressBar 
                      value={0.75}
                      max={1}
                      color="#4ECDC4"
                      showLabel
                      label="75%"
                    />
                  </div>
                </div>
              </div>
              
              <div className="nova-quality-component">
                <h4>Context Metrics (C)</h4>
                <div className="nova-metrics-grid">
                  <div className="nova-metric">
                    <div className="nova-metric-label">Domain Appropriateness</div>
                    <ProgressBar 
                      value={0.9}
                      max={1}
                      color="#45B7D1"
                      showLabel
                      label="90%"
                    />
                  </div>
                  
                  <div className="nova-metric">
                    <div className="nova-metric-label">Application Fit</div>
                    <ProgressBar 
                      value={0.85}
                      max={1}
                      color="#45B7D1"
                      showLabel
                      label="85%"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <div className="nova-quality-overall">
              <h3>Overall Quality</h3>
              <ProgressBar 
                value={qualityMetrics.overall}
                max={1}
                color="#6C5CE7"
                showLabel
                label={formatPercent(qualityMetrics.overall)}
                size="large"
              />
            </div>
          </div>
        </Tab>
        
        {/* Adaptive Ratios Tab */}
        <Tab label="Adaptive Ratios">
          <div className="nova-adaptive-ratios">
            <div className="nova-ratios-description">
              <h3>Adaptive 18/82 Principle</h3>
              <p>
                The 18/82 principle states that 18% of resources account for 82% of results. 
                The Adaptive Trinity CSDE optimizes these ratios based on empirical results 
                to find the optimal resource allocation for each component.
              </p>
            </div>
            
            <div className="nova-ratios-chart">
              <h3>Initial vs. Optimized Ratios</h3>
              <Chart 
                type="bar"
                data={adaptiveRatiosData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      max: 1
                    }
                  }
                }}
              />
            </div>
            
            <div className="nova-ratios-components">
              <div className="nova-ratio-component">
                <h4>Father (Governance)</h4>
                <div className="nova-ratio-formula">
                  πG = (α × Policy Design) + ((1-α) × Compliance Enforcement)
                </div>
                <div className="nova-ratio-values">
                  <div className="nova-ratio-initial">Initial α: 0.18</div>
                  <div className="nova-ratio-optimized">
                    Optimized α: {formatPercent(adaptiveRatios.father)}
                  </div>
                </div>
              </div>
              
              <div className="nova-ratio-component">
                <h4>Son (Detection)</h4>
                <div className="nova-ratio-formula">
                  ϕD = (β × Baseline Signals) + ((1-β) × Threat Weight)
                </div>
                <div className="nova-ratio-values">
                  <div className="nova-ratio-initial">Initial β: 0.18</div>
                  <div className="nova-ratio-optimized">
                    Optimized β: {formatPercent(adaptiveRatios.son)}
                  </div>
                </div>
              </div>
              
              <div className="nova-ratio-component">
                <h4>Spirit (Response)</h4>
                <div className="nova-ratio-formula">
                  (ℏ + c^-1)R = (γ × Reaction Time) + ((1-γ) × Mitigation Surface)
                </div>
                <div className="nova-ratio-values">
                  <div className="nova-ratio-initial">Initial γ: 0.18</div>
                  <div className="nova-ratio-optimized">
                    Optimized γ: {formatPercent(adaptiveRatios.spirit)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Tab>
        
        {/* Revenue Share Tab */}
        <Tab label="Revenue Share">
          <div className="nova-revenue-share">
            <div className="nova-revenue-description">
              <h3>18/82 Revenue Sharing</h3>
              <p>
                The 18/82 principle is applied to revenue sharing, with NovaFuse 
                receiving 18% and partners receiving 82% of the revenue. This 
                creates a partnership-centric model that prioritizes value creation.
              </p>
            </div>
            
            <div className="nova-revenue-chart">
              <h3>Revenue Distribution</h3>
              <Chart 
                type="pie"
                data={revenueShareData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false
                }}
              />
            </div>
            
            <div className="nova-revenue-details">
              <h3>Revenue Details</h3>
              <div className="nova-revenue-grid">
                <div className="nova-revenue-item">
                  <div className="nova-revenue-label">NovaFuse Share</div>
                  <div className="nova-revenue-value">
                    {formatPercent(revenueShare.novaFuse)}
                  </div>
                </div>
                
                <div className="nova-revenue-item">
                  <div className="nova-revenue-label">Partners Share</div>
                  <div className="nova-revenue-value">
                    {formatPercent(revenueShare.partners)}
                  </div>
                </div>
                
                <div className="nova-revenue-item">
                  <div className="nova-revenue-label">Estimated Total Revenue</div>
                  <div className="nova-revenue-value">
                    ${revenueShare.estimated?.total?.toLocaleString() || '0'}
                  </div>
                </div>
                
                <div className="nova-revenue-item">
                  <div className="nova-revenue-label">NovaFuse Revenue</div>
                  <div className="nova-revenue-value">
                    ${revenueShare.estimated?.novaFuse?.toLocaleString() || '0'}
                  </div>
                </div>
                
                <div className="nova-revenue-item">
                  <div className="nova-revenue-label">Partners Revenue</div>
                  <div className="nova-revenue-value">
                    ${revenueShare.estimated?.partners?.toLocaleString() || '0'}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Tab>
      </Tabs>
    </Card>
  );
}

VerificationDetails.propTypes = {
  verificationResult: PropTypes.object.isRequired
};

module.exports = VerificationDetails;
