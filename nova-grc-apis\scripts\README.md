# NovaFuse Notification Scripts

This directory contains scripts for sending notifications about project milestones.

## Setup

1. Install the required dependencies:
   ```
   npm install
   ```

2. Configure email credentials:
   - Copy `.env.notification` to `.env` in the project root
   - Update the email credentials in the `.env` file:
     ```
     EMAIL_USER=<EMAIL>
     EMAIL_PASSWORD=your-app-password
     NOTIFICATION_RECIPIENT=<EMAIL>
     ```
   - Note: For Gmail, you'll need to use an "App Password" rather than your regular password. You can generate one at https://myaccount.google.com/apppasswords

## Usage

### Using npm scripts

The following npm scripts are available for sending notifications:

```bash
# Notify when feature flag system is complete
npm run notify:feature-flags

# Notify when NovaAssist AI integration is complete
npm run notify:ai-integration

# Notify when testing is complete
npm run notify:testing

# Send a custom notification
npm run notify:custom "Milestone Name" "Additional details"
```

### Using the scripts directly

You can also run the notification scripts directly:

```bash
# Windows
scripts\notify-milestone.bat "Milestone Name" "Additional details"

# Node.js
node scripts/send-notification.js "Milestone Name" "Additional details" "<EMAIL>"
```

## Integration with Build Process

To automatically send notifications at the end of your build process, add the notification script to your build pipeline:

```bash
# Example: Send notification after successful build
npm run build && npm run notify:custom "Build Complete" "The build was successful and is ready for deployment."
```

## Customization

You can customize the email template by modifying the HTML in the `send-notification.js` file.
