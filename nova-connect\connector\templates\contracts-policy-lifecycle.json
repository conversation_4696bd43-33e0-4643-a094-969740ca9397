{"id": "contracts-policy-lifecycle", "name": "Contracts & Policy Lifecycle Connector", "description": "Connector for contract management and policy lifecycle systems", "version": "1.0.0", "category": "contracts", "icon": "contracts-icon.svg", "author": "NovaFuse", "website": "https://novafuse.io", "documentation": "https://docs.novafuse.io/connectors/contracts", "supportEmail": "<EMAIL>", "authentication": {"type": "oauth2", "oauth2": {"authorizationUrl": "https://auth.example.com/oauth2/authorize", "tokenUrl": "https://auth.example.com/oauth2/token", "scopes": ["read:contracts", "write:contracts", "read:policies", "write:policies"], "refreshTokenUrl": "https://auth.example.com/oauth2/token"}, "fields": {"clientId": {"type": "string", "label": "Client ID", "required": true, "sensitive": false, "description": "OAuth 2.0 Client ID"}, "clientSecret": {"type": "string", "label": "Client Secret", "required": true, "sensitive": true, "description": "OAuth 2.0 Client Secret"}, "redirectUri": {"type": "string", "label": "Redirect URI", "required": true, "sensitive": false, "description": "OAuth 2.0 Redirect URI"}}}, "endpoints": [{"id": "listContracts", "name": "List Contracts", "description": "List all contracts", "method": "GET", "url": "https://api.example.com/contracts", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["draft", "review", "active", "expired", "terminated"], "description": "Filter by contract status"}, "type": {"type": "string", "label": "Type", "required": false, "description": "Filter by contract type"}, "startDate": {"type": "string", "label": "Start Date", "required": false, "description": "Filter by start date (YYYY-MM-DD)"}, "endDate": {"type": "string", "label": "End Date", "required": false, "description": "Filter by end date (YYYY-MM-DD)"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by contract status", "enum": ["draft", "review", "active", "expired", "terminated"]}, "type": {"type": "string", "description": "Filter by contract type"}, "startDate": {"type": "string", "description": "Filter by start date (YYYY-MM-DD)"}, "endDate": {"type": "string", "description": "Filter by end date (YYYY-MM-DD)"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Contract ID"}, "title": {"type": "string", "description": "Contract title"}, "description": {"type": "string", "description": "Contract description"}, "status": {"type": "string", "description": "Contract status", "enum": ["draft", "review", "active", "expired", "terminated"]}, "type": {"type": "string", "description": "Contract type"}, "parties": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Party ID"}, "name": {"type": "string", "description": "Party name"}, "role": {"type": "string", "description": "Party role"}}}}, "startDate": {"type": "string", "format": "date", "description": "Contract start date"}, "endDate": {"type": "string", "format": "date", "description": "Contract end date"}, "value": {"type": "number", "description": "Contract value"}, "currency": {"type": "string", "description": "Contract currency"}, "createdAt": {"type": "string", "format": "date-time", "description": "Contract creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Contract last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getContract", "name": "Get Contract", "description": "Get a specific contract", "method": "GET", "url": "https://api.example.com/contracts/{contractId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"contractId": {"type": "string", "label": "Contract ID", "required": true, "description": "ID of the contract to retrieve"}}, "inputSchema": {"type": "object", "properties": {"contractId": {"type": "string", "description": "ID of the contract to retrieve"}}, "required": ["contractId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Contract ID"}, "title": {"type": "string", "description": "Contract title"}, "description": {"type": "string", "description": "Contract description"}, "status": {"type": "string", "description": "Contract status", "enum": ["draft", "review", "active", "expired", "terminated"]}, "type": {"type": "string", "description": "Contract type"}, "parties": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Party ID"}, "name": {"type": "string", "description": "Party name"}, "role": {"type": "string", "description": "Party role"}, "contactInfo": {"type": "object", "properties": {"email": {"type": "string", "description": "Contact email"}, "phone": {"type": "string", "description": "Contact phone"}, "address": {"type": "string", "description": "Contact address"}}}}}}, "startDate": {"type": "string", "format": "date", "description": "Contract start date"}, "endDate": {"type": "string", "format": "date", "description": "Contract end date"}, "value": {"type": "number", "description": "Contract value"}, "currency": {"type": "string", "description": "Contract currency"}, "terms": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Term ID"}, "title": {"type": "string", "description": "Term title"}, "description": {"type": "string", "description": "Term description"}, "category": {"type": "string", "description": "Term category"}}}}, "documents": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Document ID"}, "name": {"type": "string", "description": "Document name"}, "type": {"type": "string", "description": "Document type"}, "url": {"type": "string", "description": "Document URL"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Contract creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Contract last update date"}}}}, {"id": "createContract", "name": "Create Contract", "description": "Create a new contract", "method": "POST", "url": "https://api.example.com/contracts", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "bodyParameters": {"title": {"type": "string", "label": "Title", "required": true, "description": "Contract title"}, "description": {"type": "string", "label": "Description", "required": false, "description": "Contract description"}, "type": {"type": "string", "label": "Type", "required": true, "description": "Contract type"}, "parties": {"type": "array", "label": "Parties", "required": true, "description": "Contract parties"}, "startDate": {"type": "string", "label": "Start Date", "required": true, "description": "Contract start date (YYYY-MM-DD)"}, "endDate": {"type": "string", "label": "End Date", "required": false, "description": "Contract end date (YYYY-MM-DD)"}, "value": {"type": "number", "label": "Value", "required": false, "description": "Contract value"}, "currency": {"type": "string", "label": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "description": "Contract currency"}}, "inputSchema": {"type": "object", "properties": {"title": {"type": "string", "description": "Contract title"}, "description": {"type": "string", "description": "Contract description"}, "type": {"type": "string", "description": "Contract type"}, "parties": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Party ID"}, "name": {"type": "string", "description": "Party name"}, "role": {"type": "string", "description": "Party role"}, "contactInfo": {"type": "object", "properties": {"email": {"type": "string", "description": "Contact email"}, "phone": {"type": "string", "description": "Contact phone"}, "address": {"type": "string", "description": "Contact address"}}}}, "required": ["name", "role"]}}, "startDate": {"type": "string", "format": "date", "description": "Contract start date"}, "endDate": {"type": "string", "format": "date", "description": "Contract end date"}, "value": {"type": "number", "description": "Contract value"}, "currency": {"type": "string", "description": "Contract currency"}}, "required": ["title", "type", "parties", "startDate"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Contract ID"}, "title": {"type": "string", "description": "Contract title"}, "description": {"type": "string", "description": "Contract description"}, "status": {"type": "string", "description": "Contract status", "enum": ["draft", "review", "active", "expired", "terminated"]}, "type": {"type": "string", "description": "Contract type"}, "parties": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Party ID"}, "name": {"type": "string", "description": "Party name"}, "role": {"type": "string", "description": "Party role"}}}}, "startDate": {"type": "string", "format": "date", "description": "Contract start date"}, "endDate": {"type": "string", "format": "date", "description": "Contract end date"}, "value": {"type": "number", "description": "Contract value"}, "currency": {"type": "string", "description": "Contract currency"}, "createdAt": {"type": "string", "format": "date-time", "description": "Contract creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Contract last update date"}}}}, {"id": "listPolicies", "name": "List Policies", "description": "List all policies", "method": "GET", "url": "https://api.example.com/policies", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "queryParameters": {"page": {"type": "integer", "label": "Page", "required": false, "default": 1, "description": "Page number"}, "limit": {"type": "integer", "label": "Limit", "required": false, "default": 20, "description": "Number of items per page"}, "status": {"type": "string", "label": "Status", "required": false, "enum": ["draft", "review", "active", "archived", "superseded"], "description": "Filter by policy status"}, "category": {"type": "string", "label": "Category", "required": false, "description": "Filter by policy category"}}, "inputSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "Page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "status": {"type": "string", "description": "Filter by policy status", "enum": ["draft", "review", "active", "archived", "superseded"]}, "category": {"type": "string", "description": "Filter by policy category"}}}, "outputSchema": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Policy ID"}, "title": {"type": "string", "description": "Policy title"}, "description": {"type": "string", "description": "Policy description"}, "status": {"type": "string", "description": "Policy status", "enum": ["draft", "review", "active", "archived", "superseded"]}, "category": {"type": "string", "description": "Policy category"}, "version": {"type": "string", "description": "Policy version"}, "effectiveDate": {"type": "string", "format": "date", "description": "Policy effective date"}, "reviewDate": {"type": "string", "format": "date", "description": "Policy review date"}, "createdAt": {"type": "string", "format": "date-time", "description": "Policy creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Policy last update date"}}}}, "pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "Current page number"}, "limit": {"type": "integer", "description": "Number of items per page"}, "totalItems": {"type": "integer", "description": "Total number of items"}, "totalPages": {"type": "integer", "description": "Total number of pages"}}}}}}, {"id": "getPolicy", "name": "Get Policy", "description": "Get a specific policy", "method": "GET", "url": "https://api.example.com/policies/{policyId}", "headers": {"Content-Type": "application/json", "Accept": "application/json"}, "pathParameters": {"policyId": {"type": "string", "label": "Policy ID", "required": true, "description": "ID of the policy to retrieve"}}, "inputSchema": {"type": "object", "properties": {"policyId": {"type": "string", "description": "ID of the policy to retrieve"}}, "required": ["policyId"]}, "outputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "Policy ID"}, "title": {"type": "string", "description": "Policy title"}, "description": {"type": "string", "description": "Policy description"}, "content": {"type": "string", "description": "Policy content"}, "status": {"type": "string", "description": "Policy status", "enum": ["draft", "review", "active", "archived", "superseded"]}, "category": {"type": "string", "description": "Policy category"}, "version": {"type": "string", "description": "Policy version"}, "effectiveDate": {"type": "string", "format": "date", "description": "Policy effective date"}, "reviewDate": {"type": "string", "format": "date", "description": "Policy review date"}, "approvedBy": {"type": "string", "description": "User who approved the policy"}, "approvedAt": {"type": "string", "format": "date-time", "description": "Policy approval date"}, "relatedPolicies": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Related policy ID"}, "title": {"type": "string", "description": "Related policy title"}, "relationship": {"type": "string", "description": "Relationship type"}}}}, "attachments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Attachment ID"}, "name": {"type": "string", "description": "Attachment name"}, "type": {"type": "string", "description": "Attachment type"}, "url": {"type": "string", "description": "Attachment URL"}}}}, "createdAt": {"type": "string", "format": "date-time", "description": "Policy creation date"}, "updatedAt": {"type": "string", "format": "date-time", "description": "Policy last update date"}}}}]}