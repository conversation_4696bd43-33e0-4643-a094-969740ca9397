# Simple script to fix emoji rendering in the dictionary

# Define paths
$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the content with UTF-8 encoding
$content = [System.IO.File]::ReadAllText($dictionaryPath, [System.Text.Encoding]::UTF8)

# Define replacement mappings
$replacements = @{
    # Lightbulb emoji
    'ðŸ”‘' = '💡'
    # Clipboard emoji
    'ðŸ§¬' = '📋'
    # Warning emoji
    'ðŸ§ ' = '⚠️'
    'ðŸ§' = '⚠️'
    # Special characters
    'Îº' = 'κ'
    'Ã—' = '×'
}

# Apply all replacements
foreach ($key in $replacements.Keys) {
    $content = $content -replace [regex]::Escape($key), $replacements[$key]
}

# Write the modified content back to the file with UTF-8 encoding
[System.IO.File]::WriteAllText($dictionaryPath, $content.Trim(), [System.Text.Encoding]::UTF8)

Write-Host "Fixed emoji and special character rendering in the dictionary file."
Write-Host "Please review the changes in the file: $dictionaryPath"
