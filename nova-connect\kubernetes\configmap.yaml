apiVersion: v1
kind: ConfigMap
metadata:
  name: novafuse-config
  namespace: novafuse
data:
  google-cloud-project: "PROJECT_ID"
  allowed-origins: "https://novafuse.app,https://api.novafuse.app"
  log-level: "info"
  rate-limit-window-ms: "900000"
  rate-limit-max: "100"
  feature-flags-enabled: "true"
  cache-enabled: "true"
  max-concurrent-remediations: "20"
  default-timeout: "30000"
  retry-count: "3"
  retry-delay: "1000"
