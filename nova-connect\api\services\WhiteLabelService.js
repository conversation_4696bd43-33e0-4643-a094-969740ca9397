/**
 * White Label Service
 * 
 * This service handles white-labeling settings for organizations.
 */

const fs = require('fs').promises;
const path = require('path');
const { ValidationError, NotFoundError, AuthorizationError } = require('../utils/errors');
const AuditService = require('./AuditService');
const BrandingService = require('./BrandingService');
const ThemeService = require('./ThemeService');

class WhiteLabelService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.whiteLabelDir = path.join(this.dataDir, 'white_label');
    this.whiteLabelFile = path.join(this.whiteLabelDir, 'white_label_settings.json');
    this.customDomainsFile = path.join(this.whiteLabelDir, 'custom_domains.json');
    this.auditService = new AuditService(dataDir);
    this.brandingService = new BrandingService(dataDir);
    this.themeService = new ThemeService(dataDir);
    
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.whiteLabelDir, { recursive: true });
      
      // Initialize files if they don't exist
      await this.initializeFile(this.whiteLabelFile, []);
      await this.initializeFile(this.customDomainsFile, []);
    } catch (error) {
      console.error('Error creating white label directory:', error);
      throw error;
    }
  }

  /**
   * Initialize a file with default data if it doesn't exist
   */
  async initializeFile(filePath, defaultData) {
    try {
      await fs.access(filePath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, create it with default data
        await fs.writeFile(filePath, JSON.stringify(defaultData, null, 2));
      } else {
        throw error;
      }
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error(`Error loading data from ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      console.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get white label settings for an organization
   */
  async getWhiteLabelSettings(organizationId) {
    const settings = await this.loadData(this.whiteLabelFile);
    const orgSettings = settings.find(s => s.organizationId === organizationId);
    
    if (!orgSettings) {
      // Return default settings if no settings are set for organization
      return {
        enabled: false,
        removeBranding: false,
        customEmailEnabled: false,
        customEmailDomain: null,
        customEmailSender: null,
        customEmailReplyTo: null,
        customSupportEmail: null,
        customSupportUrl: null,
        customTermsUrl: null,
        customPrivacyUrl: null,
        customHelpUrl: null
      };
    }
    
    return orgSettings;
  }

  /**
   * Update white label settings for an organization
   */
  async updateWhiteLabelSettings(organizationId, data, userId) {
    const settings = await this.loadData(this.whiteLabelFile);
    const index = settings.findIndex(s => s.organizationId === organizationId);
    
    // Validate data
    if (data.customEmailEnabled && !data.customEmailDomain) {
      throw new ValidationError('Custom email domain is required when custom email is enabled');
    }
    
    if (data.customEmailEnabled && !data.customEmailSender) {
      throw new ValidationError('Custom email sender is required when custom email is enabled');
    }
    
    let orgSettings;
    
    if (index === -1) {
      // Create new settings
      orgSettings = {
        organizationId,
        enabled: data.enabled !== undefined ? data.enabled : false,
        removeBranding: data.removeBranding !== undefined ? data.removeBranding : false,
        customEmailEnabled: data.customEmailEnabled !== undefined ? data.customEmailEnabled : false,
        customEmailDomain: data.customEmailDomain || null,
        customEmailSender: data.customEmailSender || null,
        customEmailReplyTo: data.customEmailReplyTo || null,
        customSupportEmail: data.customSupportEmail || null,
        customSupportUrl: data.customSupportUrl || null,
        customTermsUrl: data.customTermsUrl || null,
        customPrivacyUrl: data.customPrivacyUrl || null,
        customHelpUrl: data.customHelpUrl || null,
        updatedBy: userId,
        updated: new Date().toISOString()
      };
      
      settings.push(orgSettings);
    } else {
      // Update existing settings
      orgSettings = {
        ...settings[index],
        ...data,
        organizationId, // Don't allow changing the organization ID
        updatedBy: userId,
        updated: new Date().toISOString()
      };
      
      settings[index] = orgSettings;
    }
    
    await this.saveData(this.whiteLabelFile, settings);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'UPDATE',
      resourceType: 'white_label',
      resourceId: organizationId,
      details: {
        enabled: orgSettings.enabled,
        removeBranding: orgSettings.removeBranding
      }
    });
    
    return orgSettings;
  }

  /**
   * Get custom domains for an organization
   */
  async getCustomDomains(organizationId) {
    const domains = await this.loadData(this.customDomainsFile);
    return domains.filter(d => d.organizationId === organizationId);
  }

  /**
   * Add custom domain for an organization
   */
  async addCustomDomain(organizationId, data, userId) {
    if (!data.domain) {
      throw new ValidationError('Domain is required');
    }
    
    // Validate domain format
    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;
    if (!domainRegex.test(data.domain)) {
      throw new ValidationError('Invalid domain format');
    }
    
    const domains = await this.loadData(this.customDomainsFile);
    
    // Check if domain already exists
    if (domains.some(d => d.domain.toLowerCase() === data.domain.toLowerCase())) {
      throw new ValidationError(`Domain ${data.domain} is already in use`);
    }
    
    // Create new domain
    const newDomain = {
      organizationId,
      domain: data.domain.toLowerCase(),
      verified: false,
      verificationToken: this.generateVerificationToken(),
      verificationMethod: 'dns',
      createdBy: userId,
      created: new Date().toISOString(),
      updated: new Date().toISOString()
    };
    
    domains.push(newDomain);
    await this.saveData(this.customDomainsFile, domains);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'ADD',
      resourceType: 'custom_domain',
      resourceId: newDomain.domain,
      details: {
        organizationId,
        domain: newDomain.domain
      }
    });
    
    return newDomain;
  }

  /**
   * Verify custom domain for an organization
   */
  async verifyCustomDomain(organizationId, domain, userId) {
    const domains = await this.loadData(this.customDomainsFile);
    const index = domains.findIndex(d => 
      d.organizationId === organizationId && 
      d.domain.toLowerCase() === domain.toLowerCase()
    );
    
    if (index === -1) {
      throw new NotFoundError(`Domain ${domain} not found for organization ${organizationId}`);
    }
    
    const domainRecord = domains[index];
    
    // In a real implementation, this would verify the domain ownership
    // by checking DNS records or other verification methods
    
    // For now, just simulate a successful verification
    domainRecord.verified = true;
    domainRecord.verifiedAt = new Date().toISOString();
    domainRecord.verifiedBy = userId;
    domainRecord.updated = new Date().toISOString();
    
    await this.saveData(this.customDomainsFile, domains);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'VERIFY',
      resourceType: 'custom_domain',
      resourceId: domain,
      details: {
        organizationId,
        domain
      }
    });
    
    return domainRecord;
  }

  /**
   * Delete custom domain for an organization
   */
  async deleteCustomDomain(organizationId, domain, userId) {
    const domains = await this.loadData(this.customDomainsFile);
    const index = domains.findIndex(d => 
      d.organizationId === organizationId && 
      d.domain.toLowerCase() === domain.toLowerCase()
    );
    
    if (index === -1) {
      throw new NotFoundError(`Domain ${domain} not found for organization ${organizationId}`);
    }
    
    // Remove the domain
    domains.splice(index, 1);
    await this.saveData(this.customDomainsFile, domains);
    
    // Log audit event
    await this.auditService.logEvent({
      userId,
      action: 'DELETE',
      resourceType: 'custom_domain',
      resourceId: domain,
      details: {
        organizationId,
        domain
      }
    });
    
    return { success: true, message: `Domain ${domain} deleted` };
  }

  /**
   * Get domain verification instructions
   */
  getDomainVerificationInstructions(domain, verificationToken) {
    return {
      dnsRecords: [
        {
          type: 'TXT',
          name: `_novaconnect-verification.${domain}`,
          value: verificationToken,
          ttl: 3600
        },
        {
          type: 'CNAME',
          name: domain,
          value: 'app.novaconnect.com',
          ttl: 3600
        }
      ],
      instructions: `
To verify your domain ownership, please add the following DNS records:

1. Add a TXT record:
   - Name: _novaconnect-verification.${domain}
   - Value: ${verificationToken}
   - TTL: 3600 (or default)

2. Add a CNAME record to point your domain to our service:
   - Name: ${domain}
   - Value: app.novaconnect.com
   - TTL: 3600 (or default)

After adding these records, it may take up to 24-48 hours for DNS changes to propagate.
Once propagated, click the "Verify" button to complete the verification process.
      `
    };
  }

  /**
   * Generate verification token
   */
  generateVerificationToken() {
    // Generate a random string for domain verification
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let token = 'novaconnect-verification=';
    
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return token;
  }

  /**
   * Get organization by domain
   */
  async getOrganizationByDomain(domain) {
    const domains = await this.loadData(this.customDomainsFile);
    const domainRecord = domains.find(d => 
      d.domain.toLowerCase() === domain.toLowerCase() && 
      d.verified
    );
    
    if (!domainRecord) {
      return null;
    }
    
    return domainRecord.organizationId;
  }

  /**
   * Get white label package
   */
  async getWhiteLabelPackage(domain) {
    // Get organization by domain
    const organizationId = await this.getOrganizationByDomain(domain);
    
    if (!organizationId) {
      throw new NotFoundError(`No verified organization found for domain ${domain}`);
    }
    
    // Get white label settings
    const settings = await this.getWhiteLabelSettings(organizationId);
    
    if (!settings.enabled) {
      throw new ValidationError(`White labeling is not enabled for domain ${domain}`);
    }
    
    // Get branding package
    const brandingPackage = await this.brandingService.getBrandingPackage(organizationId);
    
    return {
      organizationId,
      settings,
      branding: brandingPackage.branding,
      theme: brandingPackage.theme,
      themeCss: brandingPackage.themeCss
    };
  }
}

module.exports = WhiteLabelService;
