/**
 * Animation Styles
 * 
 * This module provides CSS-in-JS animation styles.
 */

import { easings, presets } from './AnimationUtils';

/**
 * Create keyframes string
 * 
 * @param {Array} keyframes - Keyframes
 * @returns {string} Keyframes string
 */
const createKeyframesString = (keyframes) => {
  return keyframes.map((frame, index) => {
    const percentage = Math.round((index / (keyframes.length - 1)) * 100);
    const styles = Object.entries(frame)
      .map(([prop, value]) => `${prop}: ${value};`)
      .join(' ');
    
    return `${percentage}% { ${styles} }`;
  }).join('\n  ');
};

/**
 * Animation keyframes
 */
export const keyframes = {
  // Fade
  fadeIn: `
  0% { opacity: 0; }
  100% { opacity: 1; }
  `,
  fadeOut: `
  0% { opacity: 1; }
  100% { opacity: 0; }
  `,
  
  // Slide
  slideInRight: `
  0% { transform: translateX(100%); }
  100% { transform: translateX(0); }
  `,
  slideOutRight: `
  0% { transform: translateX(0); }
  100% { transform: translateX(100%); }
  `,
  slideInLeft: `
  0% { transform: translateX(-100%); }
  100% { transform: translateX(0); }
  `,
  slideOutLeft: `
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
  `,
  slideInUp: `
  0% { transform: translateY(100%); }
  100% { transform: translateY(0); }
  `,
  slideOutUp: `
  0% { transform: translateY(0); }
  100% { transform: translateY(-100%); }
  `,
  slideInDown: `
  0% { transform: translateY(-100%); }
  100% { transform: translateY(0); }
  `,
  slideOutDown: `
  0% { transform: translateY(0); }
  100% { transform: translateY(100%); }
  `,
  
  // Scale
  zoomIn: `
  0% { transform: scale(0.5); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
  `,
  zoomOut: `
  0% { transform: scale(1); opacity: 1; }
  100% { transform: scale(0.5); opacity: 0; }
  `,
  
  // Flip
  flipInX: `
  0% { transform: perspective(400px) rotateX(90deg); opacity: 0; }
  100% { transform: perspective(400px) rotateX(0); opacity: 1; }
  `,
  flipOutX: `
  0% { transform: perspective(400px) rotateX(0); opacity: 1; }
  100% { transform: perspective(400px) rotateX(90deg); opacity: 0; }
  `,
  flipInY: `
  0% { transform: perspective(400px) rotateY(90deg); opacity: 0; }
  100% { transform: perspective(400px) rotateY(0); opacity: 1; }
  `,
  flipOutY: `
  0% { transform: perspective(400px) rotateY(0); opacity: 1; }
  100% { transform: perspective(400px) rotateY(90deg); opacity: 0; }
  `,
  
  // Attention seekers
  pulse: `
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
  `,
  shake: `
  0% { transform: translateX(0); }
  12.5% { transform: translateX(-10px); }
  25% { transform: translateX(10px); }
  37.5% { transform: translateX(-10px); }
  50% { transform: translateX(10px); }
  62.5% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
  100% { transform: translateX(0); }
  `,
  bounce: `
  0% { transform: translateY(0); }
  25% { transform: translateY(-30px); }
  50% { transform: translateY(0); }
  75% { transform: translateY(-15px); }
  100% { transform: translateY(0); }
  `
};

// Add keyframes from presets
Object.entries(presets).forEach(([name, preset]) => {
  if (preset.keyframes && !keyframes[name]) {
    keyframes[name] = createKeyframesString(preset.keyframes);
  }
});

/**
 * Animation durations
 */
export const durations = {
  fastest: 100,
  faster: 200,
  fast: 300,
  normal: 400,
  slow: 500,
  slower: 600,
  slowest: 800
};

/**
 * Animation delays
 */
export const delays = {
  none: 0,
  short: 100,
  medium: 200,
  long: 300
};

/**
 * Create animation CSS
 * 
 * @param {string} name - Animation name
 * @param {Object} [options] - Animation options
 * @param {number|string} [options.duration='normal'] - Animation duration
 * @param {string|Function} [options.easing='easeOutCubic'] - Easing function
 * @param {number|string} [options.delay='none'] - Animation delay
 * @param {string} [options.fillMode='forwards'] - Animation fill mode
 * @param {number} [options.iterations=1] - Number of iterations
 * @param {string} [options.direction='normal'] - Animation direction
 * @returns {string} Animation CSS
 */
export const createAnimationCSS = (name, {
  duration = 'normal',
  easing = 'easeOutCubic',
  delay = 'none',
  fillMode = 'forwards',
  iterations = 1,
  direction = 'normal'
} = {}) => {
  // Get duration value
  const durationValue = typeof duration === 'string'
    ? durations[duration] || durations.normal
    : duration;
  
  // Get delay value
  const delayValue = typeof delay === 'string'
    ? delays[delay] || delays.none
    : delay;
  
  // Get easing function
  const easingValue = typeof easing === 'string' && !easing.includes('cubic-bezier')
    ? easings[easing] || easings.easeOutCubic
    : easing;
  
  // Create animation CSS
  return `
    animation-name: ${name};
    animation-duration: ${durationValue}ms;
    animation-timing-function: ${easingValue};
    animation-delay: ${delayValue}ms;
    animation-fill-mode: ${fillMode};
    animation-iteration-count: ${iterations};
    animation-direction: ${direction};
  `;
};

/**
 * Create animation class
 * 
 * @param {string} name - Animation name
 * @param {Object} [options] - Animation options
 * @returns {string} Animation class
 */
export const createAnimationClass = (name, options = {}) => {
  const animationCSS = createAnimationCSS(name, options);
  
  return `
    @keyframes ${name} {
      ${keyframes[name] || ''}
    }
    
    .animate-${name} {
      ${animationCSS}
    }
  `;
};

/**
 * Create all animation classes
 * 
 * @returns {string} All animation classes
 */
export const createAllAnimationClasses = () => {
  return Object.keys(keyframes).map(name => createAnimationClass(name)).join('\n');
};

/**
 * Animation CSS classes
 */
export const animationClasses = {
  // Fade
  fadeIn: 'animate-fadeIn',
  fadeOut: 'animate-fadeOut',
  
  // Slide
  slideInRight: 'animate-slideInRight',
  slideOutRight: 'animate-slideOutRight',
  slideInLeft: 'animate-slideInLeft',
  slideOutLeft: 'animate-slideOutLeft',
  slideInUp: 'animate-slideInUp',
  slideOutUp: 'animate-slideOutUp',
  slideInDown: 'animate-slideInDown',
  slideOutDown: 'animate-slideOutDown',
  
  // Scale
  zoomIn: 'animate-zoomIn',
  zoomOut: 'animate-zoomOut',
  
  // Flip
  flipInX: 'animate-flipInX',
  flipOutX: 'animate-flipOutX',
  flipInY: 'animate-flipInY',
  flipOutY: 'animate-flipOutY',
  
  // Attention seekers
  pulse: 'animate-pulse',
  shake: 'animate-shake',
  bounce: 'animate-bounce'
};

/**
 * Get animation class
 * 
 * @param {string} name - Animation name
 * @param {Object} [options] - Animation options
 * @returns {string} Animation class
 */
export const getAnimationClass = (name, options = {}) => {
  if (options && Object.keys(options).length > 0) {
    // If options are provided, create a custom animation class
    return `animate-${name}-custom`;
  }
  
  // Otherwise, return the predefined class
  return animationClasses[name] || '';
};
