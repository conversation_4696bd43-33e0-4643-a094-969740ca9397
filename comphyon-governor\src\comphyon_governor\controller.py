"""
ComphyonΨᶜ Controller - Core control engine for the ComphyonΨᶜ Governor.
"""

import time
import random

class ComphyonController:
    """
    Controller for generating control actions based on ComphyonΨᶜ metrics.
    """
    
    def __init__(self):
        """Initialize the ComphyonΨᶜ Controller."""
        self.last_control_action = None
        self.control_strategies = {
            'micro': self._generate_micro_controls,
            'meso': self._generate_meso_controls,
            'macro': self._generate_macro_controls
        }
    
    def generate_control_actions(self, metrics, exceeded_metrics, thresholds):
        """
        Generate control actions based on ComphyonΨᶜ metrics.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
            exceeded_metrics: List of metrics that exceeded thresholds
            thresholds: Dictionary of thresholds
            
        Returns:
            dict: Control actions to be applied
        """
        # Determine severity based on how much thresholds are exceeded
        severity = self._calculate_severity(metrics, exceeded_metrics, thresholds)
        
        # Determine which trinity levels to control based on severity
        trinity_levels = self._determine_trinity_levels(severity)
        
        # Generate control actions for each trinity level
        actions = []
        for level in trinity_levels:
            level_actions = self.control_strategies[level](metrics, severity)
            actions.extend(level_actions)
        
        # Create control action object
        control_action = {
            'timestamp': time.time(),
            'type': 'threshold_exceeded',
            'reason': f"Exceeded thresholds for: {', '.join(exceeded_metrics)}",
            'severity': severity,
            'trinity_levels': trinity_levels,
            'actions': actions
        }
        
        self.last_control_action = control_action
        return control_action
    
    def _calculate_severity(self, metrics, exceeded_metrics, thresholds):
        """
        Calculate the severity of the threshold exceedance.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
            exceeded_metrics: List of metrics that exceeded thresholds
            thresholds: Dictionary of thresholds
            
        Returns:
            str: Severity level ('low', 'medium', 'high', 'critical')
        """
        # Calculate the maximum exceedance ratio
        max_ratio = 0
        
        for metric in exceeded_metrics:
            threshold = thresholds.get(metric, 1.0)
            value = metrics[metric]
            ratio = value / threshold
            max_ratio = max(max_ratio, ratio)
        
        # Determine severity based on the maximum ratio
        if max_ratio < 1.2:
            return 'low'
        elif max_ratio < 1.5:
            return 'medium'
        elif max_ratio < 2.0:
            return 'high'
        else:
            return 'critical'
    
    def _determine_trinity_levels(self, severity):
        """
        Determine which trinity levels to control based on severity.
        
        Args:
            severity: Severity level ('low', 'medium', 'high', 'critical')
            
        Returns:
            list: Trinity levels to control
        """
        if severity == 'low':
            return ['micro']
        elif severity == 'medium':
            return ['micro', 'meso']
        elif severity == 'high':
            return ['micro', 'meso', 'macro']
        else:  # critical
            return ['micro', 'meso', 'macro']
    
    def _generate_micro_controls(self, metrics, severity):
        """
        Generate control actions for the Micro (Ψ₁) level.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
            severity: Severity level
            
        Returns:
            list: Control actions for the Micro level
        """
        actions = []
        
        # Component parameter adjustments
        actions.append({
            'level': 'micro',
            'type': 'parameter_adjustment',
            'target': 'component_weights',
            'value': 0.8 if severity in ['high', 'critical'] else 0.9
        })
        
        # Input filtering
        actions.append({
            'level': 'micro',
            'type': 'input_filtering',
            'target': 'data_stream',
            'value': 'strict' if severity in ['high', 'critical'] else 'moderate'
        })
        
        # Resource allocation
        actions.append({
            'level': 'micro',
            'type': 'resource_allocation',
            'target': 'computation',
            'value': 0.7 if severity in ['high', 'critical'] else 0.8
        })
        
        return actions
    
    def _generate_meso_controls(self, metrics, severity):
        """
        Generate control actions for the Meso (Ψ₂) level.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
            severity: Severity level
            
        Returns:
            list: Control actions for the Meso level
        """
        actions = []
        
        # Cross-domain coupling modulation
        actions.append({
            'level': 'meso',
            'type': 'coupling_modulation',
            'target': 'domain_interactions',
            'value': 0.6 if severity in ['high', 'critical'] else 0.8
        })
        
        # Gradient dampening
        actions.append({
            'level': 'meso',
            'type': 'gradient_dampening',
            'target': 'energy_gradients',
            'value': 0.5 if severity == 'critical' else 0.7
        })
        
        # Interference pattern management
        actions.append({
            'level': 'meso',
            'type': 'interference_management',
            'target': 'pattern_formation',
            'value': 'disruptive' if severity == 'critical' else 'regulatory'
        })
        
        return actions
    
    def _generate_macro_controls(self, metrics, severity):
        """
        Generate control actions for the Macro (Ψ₃) level.
        
        Args:
            metrics: Metrics dictionary from ComphyonMeter
            severity: Severity level
            
        Returns:
            list: Control actions for the Macro level
        """
        actions = []
        
        # System-wide thresholds
        actions.append({
            'level': 'macro',
            'type': 'system_thresholds',
            'target': 'global_limits',
            'value': 'minimum' if severity == 'critical' else 'reduced'
        })
        
        # Emergency shutdown protocols
        if severity == 'critical':
            actions.append({
                'level': 'macro',
                'type': 'emergency_protocol',
                'target': 'system_components',
                'value': 'partial_shutdown'
            })
        
        # Global resource constraints
        actions.append({
            'level': 'macro',
            'type': 'resource_constraints',
            'target': 'global_resources',
            'value': 0.3 if severity == 'critical' else 0.5
        })
        
        return actions
