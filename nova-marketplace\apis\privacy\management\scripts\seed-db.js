#!/usr/bin/env node

/**
 * Seed Database Script
 * 
 * This script runs the database seeders for the Privacy Management API.
 */

const runSeeders = require('../seeders');
const logger = require('../config/logger');

logger.info('Running database seeders...');

runSeeders()
  .then(() => {
    logger.info('Database seeding completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Error seeding database:', error);
    process.exit(1);
  });
