/**
 * Comphyon Demo
 *
 * This script demonstrates the use of the Comphyon measurement system.
 */

const ComphyonMeter = require('./comphyon_meter');

/**
 * Mock CSDE Engine for demo purposes
 */
class MockCSDEEngine {
  constructor() {
    this.performanceFactor = 3142;
    this.predictionRate = 314.2;
  }

  getPredictionRate() {
    // Simulate some variation in the prediction rate
    const variation = Math.sin(Date.now() / 1000) * 50;
    return this.predictionRate + variation;
  }
}

/**
 * Mock CSFE Engine for demo purposes
 */
class MockCSFEEngine {
  constructor() {
    this.performanceFactor = 3142;
    this.predictionRate = 251.36;
  }

  getPredictionRate() {
    // Simulate some variation in the prediction rate
    const variation = Math.cos(Date.now() / 1000) * 40;
    return this.predictionRate + variation;
  }
}

/**
 * Mock CSME Engine for demo purposes
 */
class MockCSMEEngine {
  getEthicalScore() {
    // Simulate some variation in the ethical score
    const variation = (Math.sin(Date.now() / 5000) * 0.1);
    return 0.82 + variation;
  }
}

/**
 * Run the Comphyon demo
 */
function runDemo() {
  console.log('=== Comphyon Measurement System Demo ===');
  console.log('This demo simulates the Comphyon measurement system in action.');
  console.log('Press Ctrl+C to exit.');
  console.log('');

  // Create mock engines
  const csdeEngine = new MockCSDEEngine();
  const csfeEngine = new MockCSFEEngine();
  const csmeEngine = new MockCSMEEngine();

  // Create Comphyon meter
  const comphyonMeter = new ComphyonMeter({
    csdeEngine,
    csfeEngine,
    csmeEngine,
    enableLogging: true,
    updateInterval: 1000
  });

  // Listen for updates
  comphyonMeter.on('update', (data) => {
    // Clear console
    process.stdout.write('\x1Bc');

    // Print header
    console.log('=== Comphyon Measurement System Demo ===');
    console.log('Press Ctrl+C to exit.');
    console.log('');

    // Print current values
    console.log(`Comphyon Value: ${data.cph.toFixed(4)} Cph`);
    console.log(`CSDE Rate: ${data.csdeRate.toFixed(2)} predictions/sec`);
    console.log(`CSFE Rate: ${data.csfeRate.toFixed(2)} predictions/sec`);
    console.log(`CSME Score: ${data.csmeScore.toFixed(2)}`);
    console.log('');

    // Print formula
    console.log('Formula: Cph = ((csdeRate * csfeRate) × log(csmeScore)) / 166000');
    console.log(`Calculation: ((${data.csdeRate.toFixed(2)} * ${data.csfeRate.toFixed(2)}) × log(${data.csmeScore.toFixed(2)})) / 166000`);
    console.log('');

    // Print metrics
    console.log(`Calculation Time: ${data.metrics.calculationTimeMs.toFixed(2)} ms`);
    console.log(`Update Frequency: ${data.metrics.updateFrequency.toFixed(2)} updates/sec`);
    console.log('');

    // Print ASCII gauge
    const gaugeWidth = 50;
    const gaugeValue = Math.min(Math.floor(data.cph * gaugeWidth / 3), gaugeWidth);
    const gauge = '[' + '='.repeat(gaugeValue) + ' '.repeat(gaugeWidth - gaugeValue) + ']';
    console.log(`Comphyon Gauge: ${gauge} ${data.cph.toFixed(2)} Cph`);

    // Print interpretation
    let interpretation;
    if (data.cph >= 2.0) {
      interpretation = 'High Emergence - System is exhibiting strong emergent intelligence';
    } else if (data.cph >= 1.0) {
      interpretation = 'Moderate Emergence - System is exhibiting moderate emergent intelligence';
    } else {
      interpretation = 'Low Emergence - System is exhibiting minimal emergent intelligence';
    }
    console.log(`Interpretation: ${interpretation}`);
  });

  // Start the Comphyon meter
  comphyonMeter.start();

  // Handle exit
  process.on('SIGINT', () => {
    comphyonMeter.stop();
    console.log('\nComphyon demo stopped.');
    process.exit();
  });
}

// If this script is run directly, run the demo
if (require.main === module) {
  runDemo();
}

module.exports = {
  runDemo
};
