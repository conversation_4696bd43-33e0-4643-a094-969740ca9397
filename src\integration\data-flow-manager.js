/**
 * Data Flow Manager
 * 
 * This module provides a manager for data flows between components in the unified integration system.
 * It handles the creation, management, and execution of data flows.
 */

const EventEmitter = require('events');
const { performance } = require('perf_hooks');

/**
 * DataFlowManager class
 */
class DataFlowManager extends EventEmitter {
  /**
   * Create a new DataFlowManager instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableLogging: true,
      enableMetrics: true,
      ...options
    };
    
    // Initialize state
    this.flows = new Map();
    this.registry = options.registry || null;
    
    // Initialize metrics
    this.metrics = {
      flowsCreated: 0,
      flowsExecuted: 0,
      flowsDeleted: 0,
      processingTimeMs: 0,
      dataProcessed: 0,
      lastExecutionTime: 0
    };
    
    if (this.options.enableLogging) {
      console.log('DataFlowManager initialized');
    }
  }
  
  /**
   * Create a data flow between components
   * @param {string} sourceId - Source component ID
   * @param {string} targetId - Target component ID
   * @param {Object} options - Data flow options
   * @returns {string} - Flow ID
   */
  createFlow(sourceId, targetId, options = {}) {
    const startTime = performance.now();
    
    // Validate parameters
    if (!sourceId || !targetId) {
      throw new Error('Source and target component IDs are required');
    }
    
    // Check if components exist in registry
    if (this.registry) {
      if (!this.registry.has(sourceId)) {
        throw new Error(`Source component not found: ${sourceId}`);
      }
      
      if (!this.registry.has(targetId)) {
        throw new Error(`Target component not found: ${targetId}`);
      }
    }
    
    // Create flow ID
    const flowId = `flow-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    // Create flow
    const flow = {
      id: flowId,
      sourceId,
      targetId,
      dataType: options.dataType || 'tensor', // tensor, visualization, analytics
      direction: options.direction || 'forward', // forward, backward, bidirectional
      transformations: options.transformations || [],
      filters: options.filters || [],
      priority: options.priority || 'medium', // low, medium, high, critical
      metadata: options.metadata || {},
      status: 'active',
      createdAt: Date.now(),
      lastExecuted: null,
      executionCount: 0
    };
    
    // Store flow
    this.flows.set(flowId, flow);
    
    // Update metrics
    this.metrics.flowsCreated++;
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit event
    this.emit('flow-created', {
      id: flowId,
      sourceId,
      targetId,
      dataType: flow.dataType,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`DataFlowManager: Created flow from ${sourceId} to ${targetId} (${flowId})`);
    }
    
    return flowId;
  }
  
  /**
   * Execute a data flow
   * @param {string} flowId - Flow ID
   * @param {*} data - Data to process
   * @returns {*} - Processed data
   */
  executeFlow(flowId, data) {
    const startTime = performance.now();
    
    // Validate parameters
    if (!flowId) {
      throw new Error('Flow ID is required');
    }
    
    // Check if flow exists
    if (!this.flows.has(flowId)) {
      throw new Error(`Flow not found: ${flowId}`);
    }
    
    // Get flow
    const flow = this.flows.get(flowId);
    
    // Check if flow is active
    if (flow.status !== 'active') {
      if (this.options.enableLogging) {
        console.log(`DataFlowManager: Flow ${flowId} is not active`);
      }
      return null;
    }
    
    // Apply filters
    let processedData = data;
    let shouldProcess = true;
    
    for (const filter of flow.filters) {
      if (!filter(processedData)) {
        shouldProcess = false;
        break;
      }
    }
    
    if (!shouldProcess) {
      if (this.options.enableLogging) {
        console.log(`DataFlowManager: Data filtered out for flow ${flowId}`);
      }
      return null;
    }
    
    // Apply transformations
    for (const transformation of flow.transformations) {
      processedData = transformation(processedData);
    }
    
    // Get source and target components if registry is available
    let sourceComponent = null;
    let targetComponent = null;
    
    if (this.registry) {
      sourceComponent = this.registry.get(flow.sourceId);
      targetComponent = this.registry.get(flow.targetId);
    }
    
    // Update flow state
    flow.lastExecuted = Date.now();
    flow.executionCount++;
    
    // Update metrics
    this.metrics.flowsExecuted++;
    this.metrics.dataProcessed++;
    this.metrics.lastExecutionTime = Date.now();
    this.metrics.processingTimeMs += performance.now() - startTime;
    
    // Emit event
    this.emit('flow-executed', {
      id: flowId,
      sourceId: flow.sourceId,
      targetId: flow.targetId,
      dataType: flow.dataType,
      timestamp: Date.now()
    });
    
    return processedData;
  }
  
  /**
   * Get a data flow
   * @param {string} flowId - Flow ID
   * @returns {Object|null} - Flow or null if not found
   */
  getFlow(flowId) {
    return this.flows.get(flowId) || null;
  }
  
  /**
   * Get all data flows
   * @param {string} [sourceId] - Filter by source component ID
   * @param {string} [targetId] - Filter by target component ID
   * @returns {Array} - Array of flow IDs
   */
  getFlows(sourceId, targetId) {
    const flowIds = [];
    
    for (const [id, flow] of this.flows.entries()) {
      if ((!sourceId || flow.sourceId === sourceId) && 
          (!targetId || flow.targetId === targetId)) {
        flowIds.push(id);
      }
    }
    
    return flowIds;
  }
  
  /**
   * Get flows by source component
   * @param {string} sourceId - Source component ID
   * @returns {Array} - Array of flow IDs
   */
  getFlowsBySource(sourceId) {
    return this.getFlows(sourceId);
  }
  
  /**
   * Get flows by target component
   * @param {string} targetId - Target component ID
   * @returns {Array} - Array of flow IDs
   */
  getFlowsByTarget(targetId) {
    return this.getFlows(null, targetId);
  }
  
  /**
   * Update a data flow
   * @param {string} flowId - Flow ID
   * @param {Object} updates - Flow updates
   * @returns {boolean} - Success status
   */
  updateFlow(flowId, updates) {
    // Check if flow exists
    if (!this.flows.has(flowId)) {
      return false;
    }
    
    // Get flow
    const flow = this.flows.get(flowId);
    
    // Apply updates
    Object.assign(flow, updates);
    
    // Emit event
    this.emit('flow-updated', {
      id: flowId,
      sourceId: flow.sourceId,
      targetId: flow.targetId,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`DataFlowManager: Updated flow ${flowId}`);
    }
    
    return true;
  }
  
  /**
   * Delete a data flow
   * @param {string} flowId - Flow ID
   * @returns {boolean} - Success status
   */
  deleteFlow(flowId) {
    // Check if flow exists
    if (!this.flows.has(flowId)) {
      return false;
    }
    
    // Get flow
    const flow = this.flows.get(flowId);
    
    // Remove flow
    this.flows.delete(flowId);
    
    // Update metrics
    this.metrics.flowsDeleted++;
    
    // Emit event
    this.emit('flow-deleted', {
      id: flowId,
      sourceId: flow.sourceId,
      targetId: flow.targetId,
      timestamp: Date.now()
    });
    
    if (this.options.enableLogging) {
      console.log(`DataFlowManager: Deleted flow ${flowId}`);
    }
    
    return true;
  }
  
  /**
   * Get metrics
   * @returns {Object} - Metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      flowsCreated: 0,
      flowsExecuted: 0,
      flowsDeleted: 0,
      processingTimeMs: 0,
      dataProcessed: 0,
      lastExecutionTime: 0
    };
    
    if (this.options.enableLogging) {
      console.log('DataFlowManager: Metrics reset');
    }
  }
}

module.exports = DataFlowManager;
