/**
 * MINIMAL CDAIE INTELLIGENCE GRID
 * Stripped down version for debugging
 */

import { useState } from 'react';
import { StarIcon } from '@heroicons/react/24/outline';

export default function MinimalCDAIE({ activePhase, selectedMarket, coherenceLevel, divineMode }) {
  const [test] = useState('Minimal CDAIE Working');

  return (
    <div className="bg-gray-800/50 backdrop-blur-xl rounded-lg border border-gray-600 p-6">
      <div className="flex items-center space-x-2">
        <StarIcon className="w-6 h-6 text-purple-400" />
        <h2 className="text-xl font-semibold text-white">
          {test}
        </h2>
      </div>
      <div className="mt-4 text-gray-400">
        <p>Active Phase: {activePhase}</p>
        <p>Selected Market: {selectedMarket}</p>
        <p>Coherence Level: {(coherenceLevel * 100).toFixed(1)}%</p>
        <p>Divine Mode: {divineMode ? 'ON' : 'OFF'}</p>
      </div>
    </div>
  );
}
