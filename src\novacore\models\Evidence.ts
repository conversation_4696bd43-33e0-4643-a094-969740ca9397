/**
 * Evidence.ts
 * 
 * Core data model for compliance evidence in the NovaCore system.
 * This model serves as the foundation for evidence collection, verification,
 * and management across the NovaFuse ecosystem.
 */

import { v4 as uuidv4 } from 'uuid';
import { BlockchainVerification } from './BlockchainVerification';

/**
 * Evidence status enum
 */
export enum EvidenceStatus {
  DRAFT = 'draft',
  PENDING_REVIEW = 'pending_review',
  VALID = 'valid',
  INVALID = 'invalid',
  EXPIRED = 'expired',
  SUPERSEDED = 'superseded',
}

/**
 * Evidence type enum
 */
export enum EvidenceType {
  DOCUMENT = 'document',
  CONFIGURATION = 'configuration',
  LOG = 'log',
  REPORT = 'report',
  SCREENSHOT = 'screenshot',
  API_RESPONSE = 'api_response',
  ATTESTATION = 'attestation',
  POLICY = 'policy',
  PROCEDURE = 'procedure',
}

/**
 * Evidence source enum
 */
export enum EvidenceSource {
  MANUAL = 'manual',
  AUTOMATED = 'automated',
  AWS = 'aws',
  AZURE = 'azure',
  GCP = 'gcp',
  GITHUB = 'github',
  JIRA = 'jira',
  SLACK = 'slack',
  EMAIL = 'email',
  API = 'api',
}

/**
 * Evidence metadata interface
 */
export interface EvidenceMetadata {
  [key: string]: any;
  source?: EvidenceSource;
  sourceId?: string;
  sourceUrl?: string;
  collectionDate?: Date;
  expirationDate?: Date;
  tags?: string[];
  customFields?: Record<string, any>;
}

/**
 * Evidence version interface
 */
export interface EvidenceVersion {
  versionId: string;
  createdAt: Date;
  createdBy: string;
  content: any;
  contentHash: string;
  metadata: EvidenceMetadata;
  comments?: string;
  blockchainVerification?: BlockchainVerification;
}

/**
 * Evidence interface
 */
export interface Evidence {
  id: string;
  name: string;
  description?: string;
  type: EvidenceType;
  status: EvidenceStatus;
  category: string;
  framework?: string;
  control?: string;
  organization: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  currentVersion: EvidenceVersion;
  versions: EvidenceVersion[];
  metadata: EvidenceMetadata;
  relatedEvidence?: string[];
  relatedRequirements?: string[];
  verificationStatus?: 'verified' | 'unverified' | 'failed';
}

/**
 * Create a new evidence object
 */
export function createEvidence(
  name: string,
  type: EvidenceType,
  category: string,
  content: any,
  createdBy: string,
  organization: string,
  metadata: EvidenceMetadata = {},
  description?: string,
  framework?: string,
  control?: string,
): Evidence {
  const now = new Date();
  const contentHash = generateContentHash(content);
  const versionId = uuidv4();
  
  const version: EvidenceVersion = {
    versionId,
    createdAt: now,
    createdBy,
    content,
    contentHash,
    metadata,
  };
  
  return {
    id: uuidv4(),
    name,
    description,
    type,
    status: EvidenceStatus.DRAFT,
    category,
    framework,
    control,
    organization,
    createdAt: now,
    updatedAt: now,
    createdBy,
    updatedBy: createdBy,
    currentVersion: version,
    versions: [version],
    metadata,
    verificationStatus: 'unverified',
  };
}

/**
 * Create a new version of an existing evidence
 */
export function createEvidenceVersion(
  evidence: Evidence,
  content: any,
  updatedBy: string,
  metadata: EvidenceMetadata = {},
  comments?: string,
): Evidence {
  const now = new Date();
  const contentHash = generateContentHash(content);
  const versionId = uuidv4();
  
  const version: EvidenceVersion = {
    versionId,
    createdAt: now,
    createdBy: updatedBy,
    content,
    contentHash,
    metadata: { ...evidence.metadata, ...metadata },
    comments,
  };
  
  return {
    ...evidence,
    updatedAt: now,
    updatedBy,
    currentVersion: version,
    versions: [...evidence.versions, version],
    metadata: { ...evidence.metadata, ...metadata },
    verificationStatus: 'unverified',
  };
}

/**
 * Generate a hash for the content
 * In a real implementation, this would use a secure hashing algorithm
 */
function generateContentHash(content: any): string {
  // This is a placeholder. In a real implementation, we would use a secure
  // hashing algorithm like SHA-256
  return `sha256-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}

/**
 * Validate an evidence object
 */
export function validateEvidence(evidence: Evidence): boolean {
  // Basic validation
  if (!evidence.id || !evidence.name || !evidence.type || !evidence.status || !evidence.category) {
    return false;
  }
  
  // Ensure there's at least one version
  if (!evidence.versions || evidence.versions.length === 0) {
    return false;
  }
  
  // Ensure the current version exists in the versions array
  if (!evidence.currentVersion || !evidence.versions.find(v => v.versionId === evidence.currentVersion.versionId)) {
    return false;
  }
  
  return true;
}
