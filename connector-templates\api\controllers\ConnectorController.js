/**
 * Connector Controller
 * 
 * This controller handles API requests related to connectors.
 */

const ConnectorService = require('../services/ConnectorService');
const { ValidationError, NotFoundError } = require('../utils/errors');

class ConnectorController {
  constructor() {
    this.connectorService = new ConnectorService();
  }

  /**
   * Get all connectors
   */
  async getAllConnectors(req, res, next) {
    try {
      const connectors = await this.connectorService.getAllConnectors();
      res.json(connectors);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get connector by ID
   */
  async getConnectorById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Connector ID is required');
      }
      
      const connector = await this.connectorService.getConnectorById(id);
      res.json(connector);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Create a new connector
   */
  async createConnector(req, res, next) {
    try {
      const connectorData = req.body;
      
      if (!connectorData) {
        throw new ValidationError('Connector data is required');
      }
      
      const newConnector = await this.connectorService.createConnector(connectorData);
      res.status(201).json(newConnector);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update an existing connector
   */
  async updateConnector(req, res, next) {
    try {
      const { id } = req.params;
      const connectorData = req.body;
      
      if (!id) {
        throw new ValidationError('Connector ID is required');
      }
      
      if (!connectorData) {
        throw new ValidationError('Connector data is required');
      }
      
      const updatedConnector = await this.connectorService.updateConnector(id, connectorData);
      res.json(updatedConnector);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Delete a connector
   */
  async deleteConnector(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Connector ID is required');
      }
      
      const result = await this.connectorService.deleteConnector(id);
      res.json(result);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Duplicate a connector
   */
  async duplicateConnector(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Connector ID is required');
      }
      
      const duplicatedConnector = await this.connectorService.duplicateConnector(id);
      res.status(201).json(duplicatedConnector);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }

  /**
   * Get connector versions
   */
  async getConnectorVersions(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Connector ID is required');
      }
      
      const versions = await this.connectorService.getConnectorVersions(id);
      res.json(versions);
    } catch (error) {
      if (error.message.includes('not found')) {
        next(new NotFoundError(error.message));
      } else {
        next(error);
      }
    }
  }
}

module.exports = new ConnectorController();
