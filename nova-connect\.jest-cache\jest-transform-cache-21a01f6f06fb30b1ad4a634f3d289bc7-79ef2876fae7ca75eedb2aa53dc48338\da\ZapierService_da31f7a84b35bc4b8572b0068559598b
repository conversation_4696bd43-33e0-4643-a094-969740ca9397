d964c2438aa067e9645072e3cebcd55e
/**
 * Zapier Service
 * 
 * This service handles Zapier integration for NovaConnect UAC.
 */

const fs = require('fs').promises;
const path = require('path');
const {
  v4: uuidv4
} = require('uuid');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const logger = require('../../config/logger');
class ZapierService {
  constructor(dataDir = path.join(__dirname, '../../data')) {
    this.dataDir = dataDir;
    this.zapierDir = path.join(this.dataDir, 'zapier');
    this.zapierAppsFile = path.join(this.zapierDir, 'zapier_apps.json');
    this.zapierAuthsFile = path.join(this.zapierDir, 'zapier_auths.json');
    this.zapierTriggersFile = path.join(this.zapierDir, 'zapier_triggers.json');
    this.zapierActionsFile = path.join(this.zapierDir, 'zapier_actions.json');

    // JWT settings
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    this.jwtExpiresIn = process.env.ZAPIER_JWT_EXPIRES_IN || '30d';

    // OAuth settings
    this.clientId = process.env.ZAPIER_CLIENT_ID || 'nova-connect-zapier';
    this.clientSecret = process.env.ZAPIER_CLIENT_SECRET || crypto.randomBytes(32).toString('hex');
    this.redirectUri = process.env.ZAPIER_REDIRECT_URI || 'https://zapier.com/dashboard/auth/oauth/return/App-ID/';

    // Initialize data directory
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.zapierDir, {
        recursive: true
      });

      // Initialize Zapier apps file if it doesn't exist
      try {
        await fs.access(this.zapierAppsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierAppsFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }

      // Initialize Zapier auths file if it doesn't exist
      try {
        await fs.access(this.zapierAuthsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierAuthsFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }

      // Initialize Zapier triggers file if it doesn't exist
      try {
        await fs.access(this.zapierTriggersFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierTriggersFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }

      // Initialize Zapier actions file if it doesn't exist
      try {
        await fs.access(this.zapierActionsFile);
      } catch (error) {
        if (error.code === 'ENOENT') {
          await fs.writeFile(this.zapierActionsFile, JSON.stringify([]));
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Error creating Zapier directory:', error);
      throw error;
    }
  }

  /**
   * Load data from file
   */
  async loadData(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      logger.error(`Error loading data from ${filePath}:`, error);
      return [];
    }
  }

  /**
   * Save data to file
   */
  async saveData(filePath, data) {
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    } catch (error) {
      logger.error(`Error saving data to ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get Zapier app definition
   */
  getAppDefinition() {
    return {
      title: 'NovaConnect UAC',
      description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.',
      version: '1.0.0',
      platformVersion: '1.0.0',
      authentication: {
        type: 'oauth2',
        oauth2Config: {
          authorizeUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/authorize`,
          tokenUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/token`,
          refreshUrl: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/refresh`,
          autoRefresh: true,
          scope: 'read write'
        },
        connectionLabel: '{{bundle.authData.username}}'
      },
      beforeApp: {
        url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/before-app`
      },
      afterApp: {
        url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/after-app`
      }
    };
  }

  /**
   * Get Zapier triggers
   */
  async getTriggers() {
    const triggers = await this.loadData(this.zapierTriggersFile);

    // Add default triggers if none exist
    if (triggers.length === 0) {
      const defaultTriggers = [{
        key: 'new_connector',
        noun: 'Connector',
        display: {
          label: 'New Connector',
          description: 'Triggers when a new connector is created.'
        },
        operation: {
          type: 'polling',
          perform: {
            url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/new-connector`
          },
          sample: {
            id: 'conn-123',
            name: 'Sample Connector',
            type: 'api',
            createdAt: '2023-01-01T00:00:00Z'
          }
        }
      }, {
        key: 'new_workflow',
        noun: 'Workflow',
        display: {
          label: 'New Workflow',
          description: 'Triggers when a new workflow is created.'
        },
        operation: {
          type: 'polling',
          perform: {
            url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/new-workflow`
          },
          sample: {
            id: 'wf-123',
            name: 'Sample Workflow',
            status: 'active',
            createdAt: '2023-01-01T00:00:00Z'
          }
        }
      }, {
        key: 'compliance_event',
        noun: 'Compliance Event',
        display: {
          label: 'New Compliance Event',
          description: 'Triggers when a new compliance event occurs.'
        },
        operation: {
          type: 'polling',
          perform: {
            url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/triggers/compliance-event`
          },
          sample: {
            id: 'evt-123',
            type: 'compliance.violation',
            severity: 'high',
            resource: 'storage-bucket-123',
            details: 'Public access detected',
            timestamp: '2023-01-01T00:00:00Z'
          }
        }
      }];
      await this.saveData(this.zapierTriggersFile, defaultTriggers);
      return defaultTriggers;
    }
    return triggers;
  }

  /**
   * Get Zapier actions
   */
  async getActions() {
    const actions = await this.loadData(this.zapierActionsFile);

    // Add default actions if none exist
    if (actions.length === 0) {
      const defaultActions = [{
        key: 'create_connector',
        noun: 'Connector',
        display: {
          label: 'Create Connector',
          description: 'Creates a new connector.'
        },
        operation: {
          type: 'perform',
          perform: {
            url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/create-connector`,
            method: 'POST',
            body: {
              name: '{{bundle.inputData.name}}',
              type: '{{bundle.inputData.type}}',
              config: '{{bundle.inputData.config}}'
            }
          },
          inputFields: [{
            key: 'name',
            label: 'Name',
            type: 'string',
            required: true,
            helpText: 'The name of the connector.'
          }, {
            key: 'type',
            label: 'Type',
            type: 'string',
            required: true,
            choices: {
              api: 'API',
              database: 'Database',
              file: 'File'
            },
            helpText: 'The type of the connector.'
          }, {
            key: 'config',
            label: 'Configuration',
            type: 'text',
            required: true,
            helpText: 'The configuration of the connector in JSON format.'
          }],
          sample: {
            id: 'conn-123',
            name: 'Sample Connector',
            type: 'api',
            createdAt: '2023-01-01T00:00:00Z'
          }
        }
      }, {
        key: 'execute_workflow',
        noun: 'Workflow',
        display: {
          label: 'Execute Workflow',
          description: 'Executes a workflow.'
        },
        operation: {
          type: 'perform',
          perform: {
            url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/execute-workflow`,
            method: 'POST',
            body: {
              workflowId: '{{bundle.inputData.workflowId}}',
              inputs: '{{bundle.inputData.inputs}}'
            }
          },
          inputFields: [{
            key: 'workflowId',
            label: 'Workflow ID',
            type: 'string',
            required: true,
            helpText: 'The ID of the workflow to execute.'
          }, {
            key: 'inputs',
            label: 'Inputs',
            type: 'text',
            required: false,
            helpText: 'The inputs for the workflow in JSON format.'
          }],
          sample: {
            id: 'exec-123',
            workflowId: 'wf-123',
            status: 'completed',
            result: {
              success: true,
              data: {}
            },
            startedAt: '2023-01-01T00:00:00Z',
            completedAt: '2023-01-01T00:00:01Z'
          }
        }
      }, {
        key: 'create_compliance_evidence',
        noun: 'Compliance Evidence',
        display: {
          label: 'Create Compliance Evidence',
          description: 'Creates a new compliance evidence record.'
        },
        operation: {
          type: 'perform',
          perform: {
            url: `${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/actions/create-compliance-evidence`,
            method: 'POST',
            body: {
              controlId: '{{bundle.inputData.controlId}}',
              evidenceType: '{{bundle.inputData.evidenceType}}',
              description: '{{bundle.inputData.description}}',
              data: '{{bundle.inputData.data}}'
            }
          },
          inputFields: [{
            key: 'controlId',
            label: 'Control ID',
            type: 'string',
            required: true,
            helpText: 'The ID of the compliance control.'
          }, {
            key: 'evidenceType',
            label: 'Evidence Type',
            type: 'string',
            required: true,
            choices: {
              document: 'Document',
              screenshot: 'Screenshot',
              log: 'Log',
              test_result: 'Test Result',
              attestation: 'Attestation'
            },
            helpText: 'The type of evidence.'
          }, {
            key: 'description',
            label: 'Description',
            type: 'text',
            required: true,
            helpText: 'A description of the evidence.'
          }, {
            key: 'data',
            label: 'Data',
            type: 'text',
            required: false,
            helpText: 'Additional data for the evidence in JSON format.'
          }],
          sample: {
            id: 'evid-123',
            controlId: 'ctrl-123',
            evidenceType: 'document',
            description: 'Sample evidence',
            createdAt: '2023-01-01T00:00:00Z'
          }
        }
      }];
      await this.saveData(this.zapierActionsFile, defaultActions);
      return defaultActions;
    }
    return actions;
  }

  /**
   * Create OAuth authorization URL
   */
  createAuthorizationUrl(state, redirectUri) {
    const authUrl = new URL(`${process.env.API_BASE_URL || 'https://api.nova-connect.io'}/api/zapier/oauth/authorize`);
    authUrl.searchParams.append('client_id', this.clientId);
    authUrl.searchParams.append('redirect_uri', redirectUri || this.redirectUri);
    authUrl.searchParams.append('response_type', 'code');
    authUrl.searchParams.append('state', state);
    return authUrl.toString();
  }

  /**
   * Generate OAuth access token
   */
  async generateAccessToken(code, redirectUri) {
    try {
      // In a real implementation, this would validate the code
      // For now, we'll generate a token directly

      // Generate a random user ID for demo purposes
      const userId = `user-${Math.floor(Math.random() * 1000)}`;

      // Generate access token
      const accessToken = jwt.sign({
        sub: userId,
        client_id: this.clientId,
        scope: 'read write'
      }, this.jwtSecret, {
        expiresIn: this.jwtExpiresIn
      });

      // Generate refresh token
      const refreshToken = crypto.randomBytes(32).toString('hex');

      // Save auth data
      const auths = await this.loadData(this.zapierAuthsFile);
      auths.push({
        userId,
        clientId: this.clientId,
        accessToken,
        refreshToken,
        scope: 'read write',
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      });
      await this.saveData(this.zapierAuthsFile, auths);
      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        token_type: 'Bearer',
        expires_in: 30 * 24 * 60 * 60,
        // 30 days in seconds
        scope: 'read write'
      };
    } catch (error) {
      logger.error('Error generating access token:', error);
      throw error;
    }
  }

  /**
   * Refresh OAuth access token
   */
  async refreshAccessToken(refreshToken) {
    try {
      // Load auths
      const auths = await this.loadData(this.zapierAuthsFile);

      // Find auth by refresh token
      const authIndex = auths.findIndex(auth => auth.refreshToken === refreshToken);
      if (authIndex === -1) {
        throw new Error('Invalid refresh token');
      }
      const auth = auths[authIndex];

      // Generate new access token
      const accessToken = jwt.sign({
        sub: auth.userId,
        client_id: auth.clientId,
        scope: auth.scope
      }, this.jwtSecret, {
        expiresIn: this.jwtExpiresIn
      });

      // Generate new refresh token
      const newRefreshToken = crypto.randomBytes(32).toString('hex');

      // Update auth data
      auths[authIndex] = {
        ...auth,
        accessToken,
        refreshToken: newRefreshToken,
        updatedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      };
      await this.saveData(this.zapierAuthsFile, auths);
      return {
        access_token: accessToken,
        refresh_token: newRefreshToken,
        token_type: 'Bearer',
        expires_in: 30 * 24 * 60 * 60,
        // 30 days in seconds
        scope: auth.scope
      };
    } catch (error) {
      logger.error('Error refreshing access token:', error);
      throw error;
    }
  }

  /**
   * Verify OAuth access token
   */
  async verifyAccessToken(accessToken) {
    try {
      // Verify JWT
      const decoded = jwt.verify(accessToken, this.jwtSecret);

      // Load auths
      const auths = await this.loadData(this.zapierAuthsFile);

      // Find auth by access token
      const auth = auths.find(auth => auth.accessToken === accessToken);
      if (!auth) {
        throw new Error('Invalid access token');
      }
      return {
        userId: decoded.sub,
        clientId: decoded.client_id,
        scope: decoded.scope
      };
    } catch (error) {
      logger.error('Error verifying access token:', error);
      throw error;
    }
  }

  /**
   * Register Zapier app
   */
  async registerApp(appData) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);

      // Create app
      const app = {
        id: uuidv4(),
        ...appData,
        createdAt: new Date().toISOString()
      };

      // Add app
      apps.push(app);

      // Save apps
      await this.saveData(this.zapierAppsFile, apps);
      return app;
    } catch (error) {
      logger.error('Error registering Zapier app:', error);
      throw error;
    }
  }

  /**
   * Get Zapier app by ID
   */
  async getAppById(appId) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);

      // Find app by ID
      const app = apps.find(app => app.id === appId);
      if (!app) {
        throw new Error(`App with ID ${appId} not found`);
      }
      return app;
    } catch (error) {
      logger.error('Error getting Zapier app by ID:', error);
      throw error;
    }
  }

  /**
   * Get all Zapier apps
   */
  async getAllApps() {
    try {
      // Load apps
      return await this.loadData(this.zapierAppsFile);
    } catch (error) {
      logger.error('Error getting all Zapier apps:', error);
      throw error;
    }
  }

  /**
   * Update Zapier app
   */
  async updateApp(appId, appData) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);

      // Find app index
      const appIndex = apps.findIndex(app => app.id === appId);
      if (appIndex === -1) {
        throw new Error(`App with ID ${appId} not found`);
      }

      // Update app
      apps[appIndex] = {
        ...apps[appIndex],
        ...appData,
        updatedAt: new Date().toISOString()
      };

      // Save apps
      await this.saveData(this.zapierAppsFile, apps);
      return apps[appIndex];
    } catch (error) {
      logger.error('Error updating Zapier app:', error);
      throw error;
    }
  }

  /**
   * Delete Zapier app
   */
  async deleteApp(appId) {
    try {
      // Load apps
      const apps = await this.loadData(this.zapierAppsFile);

      // Find app index
      const appIndex = apps.findIndex(app => app.id === appId);
      if (appIndex === -1) {
        throw new Error(`App with ID ${appId} not found`);
      }

      // Remove app
      apps.splice(appIndex, 1);

      // Save apps
      await this.saveData(this.zapierAppsFile, apps);
      return true;
    } catch (error) {
      logger.error('Error deleting Zapier app:', error);
      throw error;
    }
  }

  /**
   * Register Zapier trigger
   */
  async registerTrigger(triggerData) {
    try {
      // Load triggers
      const triggers = await this.loadData(this.zapierTriggersFile);

      // Create trigger
      const trigger = {
        id: uuidv4(),
        ...triggerData,
        createdAt: new Date().toISOString()
      };

      // Add trigger
      triggers.push(trigger);

      // Save triggers
      await this.saveData(this.zapierTriggersFile, triggers);
      return trigger;
    } catch (error) {
      logger.error('Error registering Zapier trigger:', error);
      throw error;
    }
  }

  /**
   * Register Zapier action
   */
  async registerAction(actionData) {
    try {
      // Load actions
      const actions = await this.loadData(this.zapierActionsFile);

      // Create action
      const action = {
        id: uuidv4(),
        ...actionData,
        createdAt: new Date().toISOString()
      };

      // Add action
      actions.push(action);

      // Save actions
      await this.saveData(this.zapierActionsFile, actions);
      return action;
    } catch (error) {
      logger.error('Error registering Zapier action:', error);
      throw error;
    }
  }
}
module.exports = ZapierService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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