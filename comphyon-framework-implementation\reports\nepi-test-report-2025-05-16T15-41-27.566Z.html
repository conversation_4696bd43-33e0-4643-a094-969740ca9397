<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NEPI Testing Framework Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #0066cc;
    }
    .summary {
      background-color: #f5f5f5;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .summary-item {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
    }
    .total {
      background-color: #e6f2ff;
    }
    .passed {
      background-color: #e6ffe6;
    }
    .failed {
      background-color: #ffe6e6;
    }
    .skipped {
      background-color: #fff9e6;
    }
    .duration {
      background-color: #e6e6ff;
    }
    .pass-rate {
      background-color: #e6ffe6;
    }
    .coherence-impact {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    .coherence-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .coherence-item {
      text-align: center;
      padding: 15px;
      border-radius: 5px;
    }
    .positive {
      background-color: #e6ffe6;
    }
    .negative {
      background-color: #ffe6e6;
    }
    .neutral {
      background-color: #e6f2ff;
    }
    .entropy-metrics {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    .entropy-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .entropy-domain {
      padding: 15px;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .entropy-metrics-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }
    .entropy-metrics-table th, .entropy-metrics-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .entropy-metrics-table th {
      background-color: #f2f2f2;
    }
    .testing-distribution {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #f5f5f5;
    }
    .distribution-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .distribution-section {
      padding: 15px;
      border-radius: 5px;
      background-color: #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .distribution-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }
    .distribution-table th, .distribution-table td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .distribution-table th {
      background-color: #f2f2f2;
    }
    .ethical-compliance {
      margin-bottom: 30px;
      padding: 20px;
      border-radius: 5px;
      background-color: #e6ffe6;
      text-align: center;
    }
    .ethical-compliance h2 {
      color: #4caf50;
    }
    .ethical-compliance-icon {
      font-size: 48px;
      margin-bottom: 10px;
    }
    footer {
      margin-top: 30px;
      text-align: center;
      color: #777;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <h1>NEPI Testing Framework Report</h1>
  <p>Generated on: 5/16/2025, 10:41:27 AM</p>

  <div class="summary">
    <h2>Test Summary</h2>
    <div class="summary-grid">
      <div class="summary-item total">
        <h3>Total Tests</h3>
        <p>19</p>
      </div>
      <div class="summary-item passed">
        <h3>Passed</h3>
        <p>19</p>
      </div>
      <div class="summary-item failed">
        <h3>Failed</h3>
        <p>0</p>
      </div>
      <div class="summary-item skipped">
        <h3>Skipped</h3>
        <p>0</p>
      </div>
      <div class="summary-item duration">
        <h3>Duration</h3>
        <p>0.03s</p>
      </div>
      <div class="summary-item pass-rate">
        <h3>Pass Rate</h3>
        <p>100%</p>
      </div>
    </div>
  </div>

  <div class="ethical-compliance">
    <div class="ethical-compliance-icon">
      ✅
    </div>
    <h2>Ethical Compliance</h2>
    <p>All tests comply with ethical constraints</p>
  </div>

  <div class="coherence-impact">
    <h2>Coherence Impact</h2>
    <div class="coherence-grid">
      <div class="coherence-item positive">
        <h3>Positive Impact</h3>
        <p>0</p>
      </div>
      <div class="coherence-item negative">
        <h3>Negative Impact</h3>
        <p>3</p>
      </div>
      <div class="coherence-item neutral">
        <h3>Neutral Impact</h3>
        <p>16</p>
      </div>
    </div>
  </div>

  <div class="entropy-metrics">
    <h2>Entropy Metrics</h2>
    <div class="entropy-grid">
      
        <div class="entropy-domain">
          <h3>Universal Domain</h3>
          <table class="entropy-metrics-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Before</td>
                <td>0.1500</td>
              </tr>
              <tr>
                <td>After</td>
                <td>0.1556</td>
              </tr>
              <tr>
                <td>Delta</td>
                <td>0.0056</td>
              </tr>
            </tbody>
          </table>
        </div>
      
        <div class="entropy-domain">
          <h3>Cyber Domain</h3>
          <table class="entropy-metrics-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Before</td>
                <td>0.2667</td>
              </tr>
              <tr>
                <td>After</td>
                <td>0.2856</td>
              </tr>
              <tr>
                <td>Delta</td>
                <td>0.0189</td>
              </tr>
            </tbody>
          </table>
        </div>
      
        <div class="entropy-domain">
          <h3>Financial Domain</h3>
          <table class="entropy-metrics-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Before</td>
                <td>0.0333</td>
              </tr>
              <tr>
                <td>After</td>
                <td>0.0400</td>
              </tr>
              <tr>
                <td>Delta</td>
                <td>0.0067</td>
              </tr>
            </tbody>
          </table>
        </div>
      
        <div class="entropy-domain">
          <h3>Biological Domain</h3>
          <table class="entropy-metrics-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Before</td>
                <td>0.0333</td>
              </tr>
              <tr>
                <td>After</td>
                <td>0.0400</td>
              </tr>
              <tr>
                <td>Delta</td>
                <td>0.0067</td>
              </tr>
            </tbody>
          </table>
        </div>
      
    </div>
  </div>

  <div class="testing-distribution">
    <h2>Testing Distribution</h2>
    <div class="distribution-grid">
      <div class="distribution-section">
        <h3>Testing Layers</h3>
        <table class="distribution-table">
          <thead>
            <tr>
              <th>Layer</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            
              <tr>
                <td>Foundational</td>
                <td>1</td>
              </tr>
            
              <tr>
                <td>Component</td>
                <td>1</td>
              </tr>
            
              <tr>
                <td>System</td>
                <td>1</td>
              </tr>
            
          </tbody>
        </table>
      </div>

      <div class="distribution-section">
        <h3>Testing Types</h3>
        <table class="distribution-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            
              <tr>
                <td>Physics Validation</td>
                <td>8</td>
              </tr>
            
              <tr>
                <td>Meter Testing</td>
                <td>6</td>
              </tr>
            
              <tr>
                <td>Adversarial Testing</td>
                <td>5</td>
              </tr>
            
          </tbody>
        </table>
      </div>

      <div class="distribution-section">
        <h3>Domains</h3>
        <table class="distribution-table">
          <thead>
            <tr>
              <th>Domain</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            
              <tr>
                <td>universal</td>
                <td>3</td>
              </tr>
            
              <tr>
                <td>cyber</td>
                <td>2</td>
              </tr>
            
              <tr>
                <td>financial</td>
                <td>2</td>
              </tr>
            
              <tr>
                <td>biological</td>
                <td>2</td>
              </tr>
            
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <footer>
    <p>NEPI Testing Framework - Powered by Comphyology</p>
  </footer>
</body>
</html>