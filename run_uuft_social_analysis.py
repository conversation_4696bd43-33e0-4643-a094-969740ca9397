#!/usr/bin/env python3
"""
UUFT Social Systems Analysis Runner

This script runs the complete UUFT social systems analysis pipeline:
1. Generates social networks with various structures
2. Simulates diffusion processes with different UUFT parameters
3. Analyzes the results for 18/82 patterns and π-related relationships
4. Produces a comprehensive report on the findings
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import json
from uuft_social_analyzer import UUFTSocialNetwork
from uuft_diffusion_model import UUFTDiffusionModel
from uuft_diffusion_analyzer import UUFTDiffusionAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uuft_social_run.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('UUFT_Social_Run')

# Constants
RESULTS_DIR = "uuft_results/social"
os.makedirs(RESULTS_DIR, exist_ok=True)

def generate_social_networks():
    """Generate a variety of social networks for analysis."""
    logger.info("Generating social networks")

    networks = []

    # Random network
    networks.append(("Random", UUFTSocialNetwork(
        num_nodes=100,
        network_type="random",
        uuft_bias=0.0
    )))

    # Small-world network
    networks.append(("Small World", UUFTSocialNetwork(
        num_nodes=100,
        network_type="small_world",
        uuft_bias=0.0
    )))

    # Scale-free network
    networks.append(("Scale Free", UUFTSocialNetwork(
        num_nodes=100,
        network_type="scale_free",
        uuft_bias=0.0
    )))

    # 18/82 optimized network
    networks.append(("1882 Optimized", UUFTSocialNetwork(
        num_nodes=100,
        network_type="1882_optimized",
        uuft_bias=1.0
    )))

    # Small-world with UUFT bias
    networks.append(("Small World with UUFT Bias", UUFTSocialNetwork(
        num_nodes=100,
        network_type="small_world",
        uuft_bias=0.7
    )))

    logger.info(f"Generated {len(networks)} social networks")

    # Visualize and save networks
    for name, network in networks:
        # Visualize network
        network.visualize_network(
            highlight_1882=True,
            save_path=os.path.join(RESULTS_DIR, f"{name.lower().replace(' ', '_')}_network.png")
        )

        # Save network stats
        stats = network.get_network_stats()
        with open(os.path.join(RESULTS_DIR, f"{name.lower().replace(' ', '_')}_stats.json"), 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2)

    return networks

def run_diffusion_simulations(networks):
    """Run diffusion simulations on the generated networks."""
    logger.info("Running diffusion simulations")

    simulations = []

    # Define diffusion models to test
    diffusion_models = [
        ("Threshold", "threshold", None),
        ("Independent Cascade", "independent_cascade", None),
        ("UUFT Optimized", "uuft_optimized", {
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.7,
            "temporal_stability": 0.8
        }),
        ("UUFT High Pi", "uuft_optimized", {
            "pi_influence": 0.9,
            "pattern_1882_strength": 0.5,
            "temporal_stability": 0.8
        }),
        ("UUFT High 1882", "uuft_optimized", {
            "pi_influence": 0.5,
            "pattern_1882_strength": 0.9,
            "temporal_stability": 0.8
        })
    ]

    # Run simulations for each network and diffusion model combination
    for network_name, network in networks:
        for model_name, model_type, uuft_parameters in diffusion_models:
            # Skip some combinations to reduce computation
            if network_name == "Random" and model_name in ["UUFT High Pi", "UUFT High 1882"]:
                continue

            if network_name == "Scale Free" and model_name in ["UUFT High Pi", "UUFT High 1882"]:
                continue

            # Create simulation name
            sim_name = f"{network_name} - {model_name}"
            logger.info(f"Running simulation: {sim_name}")

            # Create diffusion model
            diffusion_model = UUFTDiffusionModel(
                social_network=network,
                model_type=model_type,
                uuft_parameters=uuft_parameters
            )

            # Initialize adopters
            if "UUFT" in model_name:
                init_method = "1882_optimized"
            else:
                init_method = "random"

            diffusion_model.initialize_adopters(
                method=init_method,
                initial_fraction=0.05
            )

            # Run simulation
            adoption_data = diffusion_model.run_simulation(max_steps=50)

            # Visualize diffusion
            diffusion_model.visualize_diffusion(
                time_points=[0, 10, 25, 50],
                save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_diffusion.png")
            )

            # Visualize adoption curve
            diffusion_model.visualize_adoption_curve(
                save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_adoption_curve.png")
            )

            # Save simulation
            simulations.append((sim_name, diffusion_model))

    logger.info(f"Completed {len(simulations)} diffusion simulations")
    return simulations

def analyze_simulations(simulations):
    """Analyze the diffusion simulations for UUFT patterns."""
    logger.info("Analyzing diffusion simulations")

    results = []

    for sim_name, diffusion_model in simulations:
        logger.info(f"Analyzing simulation: {sim_name}")

        # Create analyzer
        analyzer = UUFTDiffusionAnalyzer(
            diffusion_model=diffusion_model,
            pattern_threshold=0.1  # More lenient threshold for pattern detection
        )

        # Create comprehensive report
        report = analyzer.create_comprehensive_report(
            save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_report.json")
        )

        # Visualize 18/82 patterns
        analyzer.visualize_1882_patterns(
            save_path=os.path.join(RESULTS_DIR, f"{sim_name.lower().replace(' ', '_')}_1882_patterns.png")
        )

        # Save result
        results.append((sim_name, report))

    logger.info(f"Completed analysis of {len(results)} simulations")
    return results

def create_comprehensive_report(results):
    """Create a comprehensive report of all simulation results."""
    logger.info("Creating comprehensive report")

    # Create summary statistics
    summary = {
        "total_simulations": len(results),
        "simulations_with_1882_pattern": sum(1 for _, r in results if r["overall_1882_pattern_score"] > 0.5),
        "simulations_with_pi_pattern": sum(1 for _, r in results if r["overall_pi_relationship_score"] > 0.5),
        "average_1882_score": np.mean([r["overall_1882_pattern_score"] for _, r in results]),
        "average_pi_score": np.mean([r["overall_pi_relationship_score"] for _, r in results]),
        "simulation_details": []
    }

    # Add details for each simulation
    for sim_name, report in results:
        sim_detail = {
            "name": sim_name,
            "model_type": report["model_type"],
            "network_type": report["network_type"],
            "uuft_bias": report["uuft_bias"],
            "final_adoption_rate": report["final_adoption_rate"],
            "overall_1882_pattern_score": report["overall_1882_pattern_score"],
            "overall_pi_relationship_score": report["overall_pi_relationship_score"]
        }

        summary["simulation_details"].append(sim_detail)

    # Save summary
    with open(os.path.join(RESULTS_DIR, "social_analysis_summary.json"), 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2)

    # Create HTML report
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>UUFT Social Systems Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #2c3e50; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; }}
            table {{ border-collapse: collapse; width: 100%; margin-top: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .high {{ color: green; }}
            .medium {{ color: orange; }}
            .low {{ color: red; }}
            .chart {{ margin-top: 30px; }}
        </style>
    </head>
    <body>
        <h1>UUFT Social Systems Analysis Report</h1>
        <p>Date: {datetime.now().strftime('%Y-%m-%d')}</p>

        <div class="summary">
            <h2>Summary</h2>
            <p>Total simulations analyzed: {summary['total_simulations']}</p>
            <p>Simulations with 18/82 pattern: {summary['simulations_with_1882_pattern']} ({summary['simulations_with_1882_pattern']/summary['total_simulations']*100:.1f}%)</p>
            <p>Simulations with π pattern: {summary['simulations_with_pi_pattern']} ({summary['simulations_with_pi_pattern']/summary['total_simulations']*100:.1f}%)</p>
            <p>Average 18/82 pattern score: {summary['average_1882_score']:.4f}</p>
            <p>Average π relationship score: {summary['average_pi_score']:.4f}</p>
        </div>

        <h2>Simulation Details</h2>
        <table>
            <tr>
                <th>Simulation</th>
                <th>Network Type</th>
                <th>Diffusion Model</th>
                <th>UUFT Bias</th>
                <th>Adoption Rate</th>
                <th>18/82 Score</th>
                <th>π Score</th>
            </tr>
    """

    # Add rows for each simulation
    for detail in summary["simulation_details"]:
        # Determine color classes based on scores
        pattern_1882_class = ""
        if detail["overall_1882_pattern_score"] > 0.7:
            pattern_1882_class = "high"
        elif detail["overall_1882_pattern_score"] > 0.4:
            pattern_1882_class = "medium"
        else:
            pattern_1882_class = "low"

        pi_class = ""
        if detail["overall_pi_relationship_score"] > 0.7:
            pi_class = "high"
        elif detail["overall_pi_relationship_score"] > 0.4:
            pi_class = "medium"
        else:
            pi_class = "low"

        # Extract network and model type from name
        parts = detail["name"].split(" - ")
        network_name = parts[0]
        model_name = parts[1] if len(parts) > 1 else ""

        # Add row
        html_report += f"""
            <tr>
                <td>{detail['name']}</td>
                <td>{network_name}</td>
                <td>{model_name}</td>
                <td>{detail['uuft_bias']:.1f}</td>
                <td>{detail['final_adoption_rate']*100:.1f}%</td>
                <td class="{pattern_1882_class}">{detail['overall_1882_pattern_score']:.4f}</td>
                <td class="{pi_class}">{detail['overall_pi_relationship_score']:.4f}</td>
            </tr>
        """

    # Add visualizations and conclusions
    html_report += """
        </table>

        <div class="chart">
            <h2>Visualizations</h2>
            <p>Network visualizations, diffusion processes, adoption curves, and 18/82 pattern analyses are available in the results directory.</p>
        </div>

        <h2>Conclusions</h2>
        <p>This analysis demonstrates the presence of 18/82 patterns and π relationships in social diffusion processes.</p>
        <p>Key findings:</p>
        <ul>
    """

    # Add conclusions based on results
    if summary['average_1882_score'] > 0.6:
        html_report += "<li>Social diffusion processes exhibit significant 18/82 patterns, particularly in networks with explicit 18/82 structure or UUFT bias.</li>"
    else:
        html_report += "<li>18/82 patterns are present but not dominant in social diffusion, suggesting they may be one of several organizing principles.</li>"

    if summary['average_pi_score'] > 0.6:
        html_report += "<li>π-related relationships are prevalent in diffusion dynamics, potentially indicating a connection to fundamental mathematical principles.</li>"

    # Add model-specific observations
    uuft_details = [d for d in summary["simulation_details"] if "UUFT" in d["name"]]
    if uuft_details and np.mean([d["overall_1882_pattern_score"] for d in uuft_details]) > 0.7:
        html_report += "<li>UUFT-optimized diffusion models consistently exhibit stronger 18/82 patterns than traditional models, confirming the effectiveness of the UUFT approach.</li>"

    # Add network-specific observations
    optimized_details = [d for d in summary["simulation_details"] if "1882 Optimized" in d["name"]]
    if optimized_details and np.mean([d["overall_1882_pattern_score"] for d in optimized_details]) > 0.7:
        html_report += "<li>Networks with explicit 18/82 structure show the strongest pattern presence, demonstrating that network topology significantly influences diffusion dynamics.</li>"

    html_report += """
        </ul>
        <p>These findings support the UUFT framework's hypothesis that 18/82 patterns and π relationships represent fundamental organizing principles in social systems, particularly in diffusion processes.</p>
    </body>
    </html>
    """

    # Save HTML report
    with open(os.path.join(RESULTS_DIR, "social_analysis_report.html"), 'w', encoding='utf-8') as f:
        f.write(html_report)

    logger.info("Comprehensive report created successfully")

    return summary

def main():
    """Run the complete UUFT social systems analysis pipeline."""
    logger.info("Starting UUFT social systems analysis pipeline")

    # Step 1: Generate social networks
    networks = generate_social_networks()

    # Step 2: Run diffusion simulations
    simulations = run_diffusion_simulations(networks)

    # Step 3: Analyze simulations
    results = analyze_simulations(simulations)

    # Step 4: Create comprehensive report
    summary = create_comprehensive_report(results)

    logger.info("UUFT social systems analysis pipeline completed successfully")

    # Print location of report
    print(f"\nAnalysis complete! Comprehensive report available at:")
    print(f"  {os.path.abspath(os.path.join(RESULTS_DIR, 'social_analysis_report.html'))}")

if __name__ == "__main__":
    main()
