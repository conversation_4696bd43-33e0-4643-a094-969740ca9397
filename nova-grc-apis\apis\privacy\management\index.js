/**
 * Privacy Management API
 * 
 * This API provides endpoints for managing privacy-related activities including:
 * - Data processing activities
 * - Data subject requests
 * - Consent management
 * - Privacy impact assessments
 * - Privacy notices
 * - Data breach management
 */

const express = require('express');
const router = express.Router();

// Import routes
const processingActivitiesRoutes = require('./routes/processingActivities');
const dsrRoutes = require('./routes/dsr');
const consentRoutes = require('./routes/consent');
const piaRoutes = require('./routes/pia');
const noticesRoutes = require('./routes/notices');
const breachRoutes = require('./routes/breach');

// Register routes
router.use('/processing-activities', processingActivitiesRoutes);
router.use('/dsr', dsrRoutes);
router.use('/consent', consentRoutes);
router.use('/pia', piaRoutes);
router.use('/notices', noticesRoutes);
router.use('/breach', breachRoutes);

module.exports = router;
