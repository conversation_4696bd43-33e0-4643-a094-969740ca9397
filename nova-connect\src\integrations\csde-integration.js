/**
 * NovaConnect - CSDE Integration
 *
 * This module integrates NovaConnect with the Cyber-Safety Domain Engine (CSDE)
 * to leverage the UUFT equation for enhanced data transformation and processing.
 *
 * Instead of reimplementing the UUFT equation, this integration connects to the
 * existing CSDE implementation, which already has the equation built in.
 */

const { performance } = require('perf_hooks');
const { OptimizedDataMapper } = require('../csde/mapping');
const httpConnectionPool = require('../utils/http-connection-pool');

class CSEDIntegration {
  /**
   * Create a new CSDE Integration instance
   * @param {Object} options - Configuration options
   * @param {string} options.csdeApiUrl - URL of the CSDE API
   * @param {boolean} options.enableCaching - Enable result caching
   * @param {boolean} options.enableMetrics - Enable performance metrics
   * @param {number} options.cacheSize - Maximum cache size
   */
  constructor(options = {}) {
    this.options = {
      csdeApiUrl: process.env.CSDE_API_URL || 'http://localhost:3010',
      enableCaching: true,
      enableMetrics: true,
      cacheSize: 1000,
      enableOptimizedMapping: true,
      enableParallelProcessing: true,
      enableDirectPropertyAccess: true,
      timeout: 30000, // 30 seconds
      maxConcurrentRequests: 20,
      ...options
    };

    // Initialize cache
    this.cache = new Map();

    // Initialize metrics
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalLatency: 0,
      averageLatency: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    // Initialize optimized data mapper if enabled
    if (this.options.enableOptimizedMapping) {
      this.dataMapper = new OptimizedDataMapper({
        enableLogging: this.options.enableLogging,
        enableMetrics: this.options.enableMetrics,
        enableCaching: this.options.enableCaching,
        enableParallelProcessing: this.options.enableParallelProcessing,
        enableDirectPropertyAccess: this.options.enableDirectPropertyAccess,
        cacheSize: this.options.cacheSize,
        logger: this.options.logger || console
      });

      console.log('Optimized data mapper initialized for CSDE Integration');
    }

    console.log(`CSDE Integration initialized with API URL: ${this.options.csdeApiUrl}`);
  }

  /**
   * Transform data using the CSDE engine
   * @param {Object} data - Data to transform
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Transformed data
   */
  async transform(data, rules) {
    const startTime = this.options.enableMetrics ? performance.now() : 0;

    try {
      // Check cache if enabled
      if (this.options.enableCaching) {
        const cacheKey = this._generateCacheKey(data, rules);
        const cachedResult = this.cache.get(cacheKey);

        if (cachedResult) {
          if (this.options.enableMetrics) {
            this.metrics.cacheHits++;
          }
          return cachedResult;
        }

        if (this.options.enableMetrics) {
          this.metrics.cacheMisses++;
        }
      }

      // Use optimized data mapper if enabled
      if (this.options.enableOptimizedMapping && this.dataMapper) {
        // Map NovaConnect data to CSDE input format using optimized mapper
        const mappingRules = this._generateMappingRules(data);
        const csdeInput = await this.dataMapper.map(data, mappingRules);

        // Call CSDE API
        const csdeResult = await this._callCSDEApi(csdeInput);

        // Generate reverse mapping rules
        const reverseMappingRules = this._generateReverseMappingRules(rules, csdeResult);

        // Map CSDE result back to NovaConnect format using optimized mapper
        const transformedData = await this.dataMapper.map(
          { ...csdeResult, originalData: data },
          reverseMappingRules
        );

        // Add CSDE metadata
        transformedData._csde = {
          csdeValue: csdeResult.csdeValue,
          performanceFactor: csdeResult.performanceFactor,
          calculatedAt: csdeResult.calculatedAt
        };

        // Add remediation actions if available
        if (csdeResult.remediationActions && csdeResult.remediationActions.length > 0) {
          transformedData._remediation = csdeResult.remediationActions;
        }

        // Cache result if caching is enabled
        if (this.options.enableCaching) {
          const cacheKey = this._generateCacheKey(data, rules);
          this.cache.set(cacheKey, transformedData);

          // Limit cache size
          if (this.cache.size > this.options.cacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
          }
        }

        // Update metrics if enabled
        if (this.options.enableMetrics) {
          const endTime = performance.now();
          const latency = endTime - startTime;

          this.metrics.totalRequests++;
          this.metrics.successfulRequests++;
          this.metrics.totalLatency += latency;
          this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.successfulRequests;
        }

        return transformedData;
      } else {
        // Use legacy mapping approach
        // Map NovaConnect data to CSDE input format
        const csdeInput = this._mapToCSDEInput(data, rules);

        // Call CSDE API
        const csdeResult = await this._callCSDEApi(csdeInput);

        // Map CSDE result back to NovaConnect format
        const transformedData = this._mapFromCSDEResult(csdeResult, data, rules);

        // Cache result if caching is enabled
        if (this.options.enableCaching) {
          const cacheKey = this._generateCacheKey(data, rules);
          this.cache.set(cacheKey, transformedData);

          // Limit cache size
          if (this.cache.size > this.options.cacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
          }
        }

        // Update metrics if enabled
        if (this.options.enableMetrics) {
          const endTime = performance.now();
          const latency = endTime - startTime;

          this.metrics.totalRequests++;
          this.metrics.successfulRequests++;
          this.metrics.totalLatency += latency;
          this.metrics.averageLatency = this.metrics.totalLatency / this.metrics.successfulRequests;
        }

        return transformedData;
      }
    } catch (error) {
      // Update metrics if enabled
      if (this.options.enableMetrics) {
        this.metrics.totalRequests++;
        this.metrics.failedRequests++;
      }

      console.error('Error transforming data with CSDE:', error);
      throw new Error(`CSDE transformation failed: ${error.message}`);
    }
  }

  /**
   * Map NovaConnect data to CSDE input format
   * @private
   * @param {Object} data - NovaConnect data
   * @param {Array} rules - Transformation rules
   * @returns {Object} - CSDE input
   */
  _mapToCSDEInput(data, rules) {
    // Extract compliance data from NovaConnect data
    const complianceData = this._extractComplianceData(data, rules);

    // Extract GCP data from NovaConnect data
    const gcpData = this._extractGCPData(data, rules);

    // Extract Cyber-Safety data from NovaConnect data
    const cyberSafetyData = this._extractCyberSafetyData(data, rules);

    return {
      complianceData,
      gcpData,
      cyberSafetyData
    };
  }

  /**
   * Extract compliance data from NovaConnect data
   * @private
   * @param {Object} data - NovaConnect data
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Compliance data
   */
  _extractComplianceData(data, rules) {
    // Extract compliance-related fields
    const complianceFields = [
      'compliance', 'regulation', 'requirement', 'control', 'policy',
      'standard', 'framework', 'audit', 'evidence', 'certification'
    ];

    return this._extractFieldsByKeywords(data, complianceFields);
  }

  /**
   * Extract GCP data from NovaConnect data
   * @private
   * @param {Object} data - NovaConnect data
   * @param {Array} rules - Transformation rules
   * @returns {Object} - GCP data
   */
  _extractGCPData(data, rules) {
    // Extract GCP-related fields
    const gcpFields = [
      'gcp', 'google', 'cloud', 'project', 'instance', 'bucket',
      'service', 'resource', 'iam', 'role', 'permission'
    ];

    return this._extractFieldsByKeywords(data, gcpFields);
  }

  /**
   * Extract Cyber-Safety data from NovaConnect data
   * @private
   * @param {Object} data - NovaConnect data
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Cyber-Safety data
   */
  _extractCyberSafetyData(data, rules) {
    // Extract Cyber-Safety-related fields
    const cyberSafetyFields = [
      'security', 'vulnerability', 'threat', 'risk', 'attack',
      'malware', 'breach', 'incident', 'protection', 'defense'
    ];

    return this._extractFieldsByKeywords(data, cyberSafetyFields);
  }

  /**
   * Extract fields from data by keywords
   * @private
   * @param {Object} data - Data object
   * @param {Array} keywords - Keywords to match
   * @returns {Object} - Extracted data
   */
  _extractFieldsByKeywords(data, keywords) {
    const result = {};

    // Helper function to recursively extract fields
    const extract = (obj, path = '') => {
      if (!obj || typeof obj !== 'object') return;

      for (const key in obj) {
        const value = obj[key];
        const currentPath = path ? `${path}.${key}` : key;

        // Check if key contains any of the keywords
        const matchesKeyword = keywords.some(keyword =>
          key.toLowerCase().includes(keyword.toLowerCase())
        );

        if (matchesKeyword) {
          result[currentPath] = value;
        }

        // Recursively process nested objects
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          extract(value, currentPath);
        }
      }
    };

    extract(data);

    return result;
  }

  /**
   * Call the CSDE API
   * @private
   * @param {Object} input - CSDE input
   * @returns {Object} - CSDE result
   */
  async _callCSDEApi(input) {
    try {
      // Use HTTP connection pool for better performance
      const response = await httpConnectionPool.post(
        this.options.csdeApiUrl,
        '/calculate',
        input,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: this.options.timeout || 30000
        }
      );

      return response.data.result;
    } catch (error) {
      console.error('Error calling CSDE API:', error);
      throw new Error(`CSDE API call failed: ${error.message}`);
    }
  }

  /**
   * Map CSDE result back to NovaConnect format
   * @private
   * @param {Object} csdeResult - CSDE result
   * @param {Object} originalData - Original NovaConnect data
   * @param {Array} rules - Transformation rules
   * @returns {Object} - Transformed data in NovaConnect format
   */
  _mapFromCSDEResult(csdeResult, originalData, rules) {
    // Initialize result object
    const result = {};

    // Apply transformation rules
    for (const rule of rules) {
      try {
        // Get value from source path in original data
        const value = this._getValueByPath(originalData, rule.source);

        // Apply CSDE enhancement if available
        let transformedValue = value;

        // Check if we have CSDE enhancement for this field
        const csdeEnhancement = this._findCSDEEnhancement(csdeResult, rule.source);

        if (csdeEnhancement) {
          // Apply CSDE enhancement
          transformedValue = csdeEnhancement;
        } else if (rule.transform) {
          // Apply standard transformation if no CSDE enhancement
          transformedValue = this._applyTransformation(value, rule.transform, rule.transformParams);
        }

        // Set value in target path
        this._setValueByPath(result, rule.target, transformedValue);
      } catch (error) {
        console.error(`Error applying rule ${rule.source} -> ${rule.target}:`, error);
      }
    }

    // Add CSDE metadata
    result._csde = {
      csdeValue: csdeResult.csdeValue,
      performanceFactor: csdeResult.performanceFactor,
      calculatedAt: csdeResult.calculatedAt
    };

    // Add remediation actions if available
    if (csdeResult.remediationActions && csdeResult.remediationActions.length > 0) {
      result._remediation = csdeResult.remediationActions;
    }

    return result;
  }

  /**
   * Find CSDE enhancement for a field
   * @private
   * @param {Object} csdeResult - CSDE result
   * @param {string} fieldPath - Field path
   * @returns {*} - CSDE enhancement or undefined
   */
  _findCSDEEnhancement(csdeResult, fieldPath) {
    // Check if we have a direct match in the CSDE result
    if (csdeResult[fieldPath]) {
      return csdeResult[fieldPath];
    }

    // Check if we have a match in the remediation actions
    if (csdeResult.remediationActions) {
      for (const action of csdeResult.remediationActions) {
        if (action.targetField === fieldPath) {
          return action.enhancedValue;
        }
      }
    }

    return undefined;
  }

  /**
   * Apply transformation to a value
   * @private
   * @param {*} value - Value to transform
   * @param {string|Array} transform - Transformation to apply
   * @param {Object} params - Transformation parameters
   * @returns {*} - Transformed value
   */
  _applyTransformation(value, transform, params) {
    // Simple transformations
    const transformers = {
      lowercase: (v) => typeof v === 'string' ? v.toLowerCase() : v,
      uppercase: (v) => typeof v === 'string' ? v.toUpperCase() : v,
      trim: (v) => typeof v === 'string' ? v.trim() : v,
      toNumber: (v) => !isNaN(parseFloat(v)) ? parseFloat(v) : v,
      toString: (v) => String(v),
      toBoolean: (v) => Boolean(v),
      split: (v, separator = ',') => typeof v === 'string' ? v.split(separator) : v,
      join: (v, separator = ',') => Array.isArray(v) ? v.join(separator) : v
    };

    if (typeof transform === 'string') {
      // Single transformation
      if (transformers[transform]) {
        return transformers[transform](value, params);
      }
      return value;
    } else if (Array.isArray(transform)) {
      // Chain of transformations
      return transform.reduce((currentValue, transformName) => {
        if (transformers[transformName]) {
          return transformers[transformName](currentValue, params);
        }
        return currentValue;
      }, value);
    }

    return value;
  }

  /**
   * Get value from an object by dot-notation path
   * @private
   * @param {Object} obj - Source object
   * @param {string} path - Dot-notation path
   * @returns {*} - Value at the path
   */
  _getValueByPath(obj, path) {
    if (!obj || !path) return undefined;

    const parts = path.split('.');
    let current = obj;

    for (const part of parts) {
      if (current === null || current === undefined) return undefined;
      current = current[part];
    }

    return current;
  }

  /**
   * Set value in an object by dot-notation path
   * @private
   * @param {Object} obj - Target object
   * @param {string} path - Dot-notation path
   * @param {*} value - Value to set
   */
  _setValueByPath(obj, path, value) {
    if (!obj || !path) return;

    const parts = path.split('.');
    let current = obj;

    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!(part in current)) {
        current[part] = {};
      }
      current = current[part];
    }

    current[parts[parts.length - 1]] = value;
  }

  /**
   * Generate mapping rules for optimized data mapper
   * @private
   * @param {Object} data - Source data
   * @returns {Array} - Mapping rules
   */
  _generateMappingRules(data) {
    // Extract compliance-related fields
    const complianceFields = [
      'compliance', 'regulation', 'requirement', 'control', 'policy',
      'standard', 'framework', 'audit', 'evidence', 'certification'
    ];

    // Extract GCP-related fields
    const gcpFields = [
      'gcp', 'google', 'cloud', 'project', 'instance', 'bucket',
      'service', 'resource', 'iam', 'role', 'permission'
    ];

    // Extract Cyber-Safety-related fields
    const cyberSafetyFields = [
      'security', 'vulnerability', 'threat', 'risk', 'attack',
      'malware', 'breach', 'incident', 'protection', 'defense'
    ];

    // Generate mapping rules
    const rules = [];

    // Helper function to recursively extract fields
    const extractFields = (obj, path = '', targetPrefix = '') => {
      if (!obj || typeof obj !== 'object') return;

      for (const key in obj) {
        const value = obj[key];
        const currentPath = path ? `${path}.${key}` : key;

        // Determine target prefix based on field type
        let prefix = targetPrefix;

        if (!prefix) {
          if (complianceFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            prefix = 'complianceData';
          } else if (gcpFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            prefix = 'gcpData';
          } else if (cyberSafetyFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
            prefix = 'cyberSafetyData';
          } else {
            prefix = 'metadata';
          }
        }

        // Add mapping rule
        rules.push({
          source: currentPath,
          target: `${prefix}.${currentPath}`,
          transform: this._determineTransformation(value)
        });

        // Recursively process nested objects
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          extractFields(value, currentPath, prefix);
        }
      }
    };

    // Extract fields from data
    extractFields(data);

    return rules;
  }

  /**
   * Generate reverse mapping rules for optimized data mapper
   * @private
   * @param {Array} originalRules - Original transformation rules
   * @param {Object} csdeResult - CSDE result
   * @returns {Array} - Reverse mapping rules
   */
  _generateReverseMappingRules(originalRules, csdeResult) {
    const rules = [];

    // Add rules for each original rule
    for (const rule of originalRules) {
      // Check if we have CSDE enhancement for this field
      const csdeEnhancement = this._findCSDEEnhancement(csdeResult, rule.source);

      if (csdeEnhancement) {
        // Add rule for CSDE enhancement
        rules.push({
          source: `originalData.${rule.source}`,
          target: rule.target,
          transform: null, // No transformation needed, use enhanced value
          transformParams: {
            enhancement: csdeEnhancement
          }
        });
      } else {
        // Add rule for original transformation
        rules.push({
          source: `originalData.${rule.source}`,
          target: rule.target,
          transform: rule.transform,
          transformParams: rule.transformParams
        });
      }
    }

    // Add rules for CSDE metadata
    rules.push({
      source: 'csdeValue',
      target: '_csde.csdeValue',
      transform: null
    });

    rules.push({
      source: 'performanceFactor',
      target: '_csde.performanceFactor',
      transform: null
    });

    rules.push({
      source: 'calculatedAt',
      target: '_csde.calculatedAt',
      transform: null
    });

    // Add rules for remediation actions
    rules.push({
      source: 'remediationActions',
      target: '_remediation',
      transform: null
    });

    return rules;
  }

  /**
   * Determine appropriate transformation for a value
   * @private
   * @param {*} value - Value to transform
   * @returns {string|Array|null} - Transformation to apply
   */
  _determineTransformation(value) {
    if (value === null || value === undefined) {
      return null;
    }

    if (typeof value === 'string') {
      return 'trim';
    }

    if (typeof value === 'number') {
      return null; // No transformation needed
    }

    if (typeof value === 'boolean') {
      return null; // No transformation needed
    }

    if (Array.isArray(value)) {
      return null; // No transformation needed
    }

    if (typeof value === 'object') {
      return null; // No transformation needed
    }

    return null;
  }

  /**
   * Generate a cache key for data and rules
   * @private
   * @param {Object} data - Source data
   * @param {Array} rules - Transformation rules
   * @returns {string} - Cache key
   */
  _generateCacheKey(data, rules) {
    const dataHash = JSON.stringify(data);
    const rulesHash = JSON.stringify(rules);
    return `${dataHash}:${rulesHash}`;
  }

  /**
   * Get current metrics
   * @returns {Object} - Metrics object
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalLatency: 0,
      averageLatency: 0,
      cacheHits: 0,
      cacheMisses: 0
    };
  }

  /**
   * Clear the cache
   */
  clearCache() {
    this.cache.clear();
  }
}

module.exports = CSEDIntegration;
