import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { useRouter } from 'next/router';
import axios from 'axios';
import Head from 'next/head';

// API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

export default function ResetPassword() {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [token, setToken] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    // Get token from URL query parameter
    if (router.query.token) {
      setToken(router.query.token);
    }
  }, [router.query]);

  const validateForm = () => {
    // Check if passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    // Check password length
    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }

    // Check password complexity
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
    if (!passwordRegex.test(password)) {
      setError('Password must contain at least one uppercase letter, one lowercase letter, and one number');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setError('');
      setIsSubmitting(true);

      await axios.post(`${API_URL}/auth/reset-password`, { token, password });

      setIsSubmitted(true);
    } catch (error) {
      setError(error.response?.data?.message || 'Password reset failed. The token may be invalid or expired.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Head>
        <title>Reset Password - NovaFuse Compliance App Store</title>
        <meta name="description" content="Create a new password for your NovaFuse Compliance App Store account" />
      </Head>

      <div className="min-h-screen flex items-center justify-center bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div>
            <Link href="/" className="flex justify-center">
              <img className="h-12 w-auto" src="/images/logo.png" alt="NovaFuse" />
            </Link>
            <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
              Create a new password
            </h2>
            <p className="mt-2 text-center text-sm text-gray-400">
              Enter your new password below.
            </p>
          </div>

          {!token && (
            <div className="bg-yellow-900 bg-opacity-30 border border-yellow-700 rounded-lg p-4 text-yellow-400">
              No reset token provided. Please use the link from your email.
            </div>
          )}

          {isSubmitted ? (
            <div className="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-6 text-center">
              <h3 className="text-xl font-bold mb-2 text-green-400">Password reset successful!</h3>
              <p className="text-gray-300 mb-4">
                Your password has been reset successfully. You can now log in with your new password.
              </p>
              <Link href="/login" className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg">
                Go to login
              </Link>
            </div>
          ) : (
            <>
              {error && (
                <div className="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 text-red-400">
                  {error}
                </div>
              )}

              <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
                <div className="rounded-md shadow-sm -space-y-px">
                  <div>
                    <label htmlFor="password" className="sr-only">New Password</label>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      autoComplete="new-password"
                      required
                      className="appearance-none rounded-t-md relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-500 text-white bg-gray-800 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                      placeholder="New Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={!token || isSubmitting}
                    />
                  </div>
                  <div>
                    <label htmlFor="confirm-password" className="sr-only">Confirm New Password</label>
                    <input
                      id="confirm-password"
                      name="confirmPassword"
                      type="password"
                      autoComplete="new-password"
                      required
                      className="appearance-none rounded-b-md relative block w-full px-3 py-2 border border-gray-700 placeholder-gray-500 text-white bg-gray-800 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                      placeholder="Confirm New Password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      disabled={!token || isSubmitting}
                    />
                  </div>
                </div>

                <div className="text-sm text-gray-400">
                  <p>Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.</p>
                </div>

                <div>
                  <button
                    type="submit"
                    className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!token || isSubmitting}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Resetting password...
                      </span>
                    ) : 'Reset Password'}
                  </button>
                </div>
              </form>

              <div className="text-center">
                <Link href="/login" className="text-sm text-blue-500 hover:text-blue-400">
                  Back to login
                </Link>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}
