import React from 'react';
import {
  Diagram<PERSON>rame,
  ComponentBox,
  ComponentLabel,
  ComponentNumber,
  ContainerBox,
  ContainerLabel,
  InventorLabel
} from '../../components/DiagramComponents';

const AlignmentArchitectureWhitepaper = () => {
  return (
    <DiagramFrame>
      <ContainerBox width="750px" height="600px" left="25px" top="20px">
        <ContainerLabel>3–6–9–12–13 ALIGNMENT ARCHITECTURE</ContainerLabel>
      </ContainerBox>

      {/* Title */}
      <div style={{
        position: 'absolute',
        top: '70px',
        left: '50%',
        transform: 'translateX(-50%)',
        textAlign: 'center',
        width: '80%'
      }}>
        <h2 style={{ fontSize: '18px', marginBottom: '30px' }}>
          Structured Emergence of Value-Based Intelligence
        </h2>
      </div>

      {/* 3 - Foundational Pillars */}
      <ComponentBox left="100px" top="120px" width="600px" height="60px">
        <ComponentNumber>1</ComponentNumber>
        <ComponentLabel>3 – Foundational Pillars</ComponentLabel>
        <div>Truth • Trust • Transparency</div>
      </ComponentBox>

      {/* 6 - Core Capacities */}
      <ComponentBox left="100px" top="200px" width="600px" height="60px">
        <ComponentNumber>2</ComponentNumber>
        <ComponentLabel>6 – Core Capacities</ComponentLabel>
        <div>Awareness • Intelligence • Control • Resilience • Adaptability • Responsibility</div>
      </ComponentBox>

      {/* 9 - Operational Engines */}
      <ComponentBox left="100px" top="280px" width="600px" height="60px">
        <ComponentNumber>3</ComponentNumber>
        <ComponentLabel>9 – Operational Engines</ComponentLabel>
        <div>Risk Analysis • Policy Automation • Identity Flow • Audit Trail • Knowledge Sync • AI Guidance • GRC Mapping • User Training • Threat Detection</div>
      </ComponentBox>

      {/* 12 - Integration Points */}
      <ComponentBox left="100px" top="360px" width="600px" height="80px">
        <ComponentNumber>4</ComponentNumber>
        <ComponentLabel>12 – Integration Points</ComponentLabel>
        <div>APIs • Compliance Modules • Data Gateways • Third-Party Connectors • Logging Systems • Reports • Real-Time Monitoring • Feedback Channels • User Interfaces • Team Roles • Workflows • Security Zones</div>
      </ComponentBox>

      {/* 13 - NovaFuse Alignment */}
      <ComponentBox left="100px" top="460px" width="600px" height="60px">
        <ComponentNumber>5</ComponentNumber>
        <ComponentLabel>13 – NovaFuse Alignment</ComponentLabel>
        <div>All Universal Components Activated in Harmony</div>
      </ComponentBox>

      <InventorLabel>Inventor: David Nigel Irvin</InventorLabel>
    </DiagramFrame>
  );
};

export default AlignmentArchitectureWhitepaper;
