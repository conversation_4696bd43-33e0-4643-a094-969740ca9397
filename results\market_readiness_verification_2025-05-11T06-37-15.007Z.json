{"timestamp": "2025-05-11T06:37:14.991Z", "quantumInference": {"iterations": 10, "certaintyRates": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25], "inferenceTimes": [2.362499999999997, 0.5716000000000037, 0.6026999999999987, 0.2862999999999971, 0.31159999999999854, 0.22629999999999484, 0.2768999999999977, 0.4035000000000082, 0.347999999999999, 0.5267999999999944], "certaintyRate": 25, "inferenceTime": 0.5916199999999989, "passed": false}, "rbac": {"testCases": [{"userId": "user-ciso", "componentType": "quantum_inference", "action": "view", "expected": true, "result": true, "passed": true, "reason": "admin_role"}, {"userId": "user-ciso", "componentType": "view:dashboard", "action": "view", "expected": true, "result": true, "passed": true, "reason": "admin_role"}, {"userId": "user-ciso", "componentType": "view:audit_logs", "action": "view", "expected": true, "result": true, "passed": true, "reason": "admin_role"}, {"userId": "user-analyst", "componentType": "quantum_inference", "action": "view", "expected": true, "result": true, "passed": true, "reason": "component_permission"}, {"userId": "user-analyst", "componentType": "view:dashboard", "action": "view", "expected": true, "result": true, "passed": true, "reason": "component_permission"}, {"userId": "user-analyst", "componentType": "view:audit_logs", "action": "view", "expected": false, "result": false, "passed": true, "reason": "access_denied"}, {"userId": "user-standard", "componentType": "quantum_inference", "action": "view", "expected": false, "result": false, "passed": true, "reason": "access_denied"}, {"userId": "user-standard", "componentType": "view:dashboard", "action": "view", "expected": false, "result": false, "passed": true, "reason": "access_denied"}, {"userId": "user-standard", "componentType": "view:audit_logs", "action": "view", "expected": false, "result": false, "passed": true, "reason": "access_denied"}, {"userId": "user-auditor", "componentType": "quantum_inference", "action": "view", "expected": true, "result": true, "passed": true, "reason": "component_permission"}, {"userId": "user-auditor", "componentType": "view:dashboard", "action": "view", "expected": true, "result": false, "passed": false, "reason": "access_denied"}, {"userId": "user-auditor", "componentType": "view:audit_logs", "action": "view", "expected": true, "result": true, "passed": true, "reason": "component_permission"}], "passCount": 11, "totalTests": 12, "passRate": 91.66666666666666, "passed": false}, "overallResult": false}