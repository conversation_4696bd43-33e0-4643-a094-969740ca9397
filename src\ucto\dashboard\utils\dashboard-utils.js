/**
 * Dashboard Utilities for the Universal Compliance Tracking Optimizer.
 *
 * This module provides utility functions for the UCTO Unified Dashboard.
 */

/**
 * Format a date for display.
 * @param {string} dateString - ISO date string
 * @param {string} format - Date format (short, medium, long)
 * @returns {string} Formatted date string
 */
function formatDate(dateString, format = 'medium') {
  if (!dateString) {
    return '';
  }
  
  const date = new Date(dateString);
  
  switch (format) {
    case 'short':
      return date.toLocaleDateString();
    case 'long':
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    case 'medium':
    default:
      return date.toLocaleDateString();
  }
}

/**
 * Calculate days until a date.
 * @param {string} dateString - ISO date string
 * @returns {number} Days until the date (negative if in the past)
 */
function daysUntil(dateString) {
  if (!dateString) {
    return 0;
  }
  
  const date = new Date(dateString);
  const now = new Date();
  
  // Reset time to midnight for both dates
  date.setHours(0, 0, 0, 0);
  now.setHours(0, 0, 0, 0);
  
  // Calculate difference in days
  const diffTime = date.getTime() - now.getTime();
  const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}

/**
 * Calculate a compliance score based on requirements.
 * @param {Array} requirements - List of requirements
 * @returns {number} Compliance score (0-100)
 */
function calculateComplianceScore(requirements) {
  if (!requirements || requirements.length === 0) {
    return 0;
  }
  
  const totalRequirements = requirements.length;
  const completedRequirements = requirements.filter(req => req.status === 'completed').length;
  
  return Math.round((completedRequirements / totalRequirements) * 100);
}

/**
 * Get a color for a status.
 * @param {string} status - Status value
 * @returns {string} Color code
 */
function getStatusColor(status) {
  const statusColors = {
    completed: '#4CAF50',
    in_progress: '#2196F3',
    pending: '#FFC107',
    overdue: '#F44336'
  };
  
  return statusColors[status] || '#9E9E9E';
}

/**
 * Get a color for a score.
 * @param {number} score - Score value (0-100)
 * @returns {string} Color code
 */
function getScoreColor(score) {
  if (score >= 90) {
    return '#4CAF50'; // Green
  } else if (score >= 80) {
    return '#8BC34A'; // Light Green
  } else if (score >= 70) {
    return '#CDDC39'; // Lime
  } else if (score >= 60) {
    return '#FFEB3B'; // Yellow
  } else if (score >= 50) {
    return '#FFC107'; // Amber
  } else if (score >= 40) {
    return '#FF9800'; // Orange
  } else {
    return '#F44336'; // Red
  }
}

/**
 * Filter requirements by criteria.
 * @param {Array} requirements - List of requirements
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered requirements
 */
function filterRequirements(requirements, filters) {
  if (!requirements) {
    return [];
  }
  
  let filtered = [...requirements];
  
  if (filters.framework) {
    filtered = filtered.filter(req => req.framework === filters.framework);
  }
  
  if (filters.status) {
    filtered = filtered.filter(req => req.status === filters.status);
  }
  
  if (filters.category) {
    filtered = filtered.filter(req => req.category === filters.category);
  }
  
  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(req => 
      req.name.toLowerCase().includes(searchLower) || 
      req.description.toLowerCase().includes(searchLower)
    );
  }
  
  return filtered;
}

/**
 * Sort requirements by field.
 * @param {Array} requirements - List of requirements
 * @param {string} field - Field to sort by
 * @param {string} direction - Sort direction (asc, desc)
 * @returns {Array} Sorted requirements
 */
function sortRequirements(requirements, field, direction = 'asc') {
  if (!requirements) {
    return [];
  }
  
  const sorted = [...requirements];
  
  sorted.sort((a, b) => {
    let valueA = a[field];
    let valueB = b[field];
    
    // Handle date fields
    if (field === 'due_date') {
      valueA = new Date(valueA).getTime();
      valueB = new Date(valueB).getTime();
    }
    
    // Handle string fields
    if (typeof valueA === 'string') {
      valueA = valueA.toLowerCase();
      valueB = valueB.toLowerCase();
    }
    
    // Compare values
    if (valueA < valueB) {
      return direction === 'asc' ? -1 : 1;
    }
    if (valueA > valueB) {
      return direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
  
  return sorted;
}

module.exports = {
  formatDate,
  daysUntil,
  calculateComplianceScore,
  getStatusColor,
  getScoreColor,
  filterRequirements,
  sortRequirements
};
