import React from 'react';
import { render, screen } from '@testing-library/react';
import AnimatedList from '../../components/AnimatedList';

// Mock framer-motion
jest.mock('framer-motion', () => {
  return {
    motion: {
      ul: ({ children, className, variants, ...props }) => (
        <ul 
          className={className} 
          data-testid="motion-ul"
          data-animation-props={JSON.stringify(props)}
        >
          {children}
        </ul>
      ),
      li: ({ children, className, variants, ...props }) => (
        <li 
          className={className} 
          data-testid="motion-li"
          data-animation-props={JSON.stringify(props)}
        >
          {children}
        </li>
      )
    }
  };
});

// Mock animations
jest.mock('../../utils/animations', () => ({
  staggerContainer: jest.fn().mockImplementation((staggerDuration, initialDelay) => ({
    animate: {
      transition: {
        staggerChildren: staggerDuration / 1000,
        delayChildren: initialDelay / 1000
      }
    }
  })),
  fadeInUp: jest.fn().mockImplementation((duration) => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: duration / 1000 }
  }))
}));

describe('AnimatedList', () => {
  const items = ['Item 1', 'Item 2', 'Item 3'];
  
  it('renders list items correctly', () => {
    render(<AnimatedList items={items} />);
    
    // Check if all items are rendered
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
    
    // Check if the correct number of list items are rendered
    const listItems = screen.getAllByTestId('motion-li');
    expect(listItems).toHaveLength(3);
  });
  
  it('applies custom className to container and items', () => {
    render(
      <AnimatedList 
        items={items} 
        className="container-class" 
        itemClassName="item-class"
      />
    );
    
    // Check if container class is applied
    const container = screen.getByTestId('motion-ul');
    expect(container).toHaveClass('container-class');
    
    // Check if item class is applied to all items
    const listItems = screen.getAllByTestId('motion-li');
    listItems.forEach(item => {
      expect(item).toHaveClass('item-class');
    });
  });
  
  it('uses custom stagger duration and initial delay', () => {
    render(
      <AnimatedList 
        items={items} 
        staggerDuration={200}
        initialDelay={300}
      />
    );
    
    // We can't directly test the animation properties, but we can check
    // that the component renders without errors
    expect(screen.getByTestId('motion-ul')).toBeInTheDocument();
    
    // Check if the correct number of list items are rendered
    const listItems = screen.getAllByTestId('motion-li');
    expect(listItems).toHaveLength(3);
  });
  
  it('uses custom render function when provided', () => {
    const renderItem = (item, index) => (
      <div data-testid={`custom-item-${index}`}>{`Custom ${item}`}</div>
    );
    
    render(<AnimatedList items={items} renderItem={renderItem} />);
    
    // Check if custom rendered items are displayed
    expect(screen.getByText('Custom Item 1')).toBeInTheDocument();
    expect(screen.getByText('Custom Item 2')).toBeInTheDocument();
    expect(screen.getByText('Custom Item 3')).toBeInTheDocument();
    
    // Check if custom test IDs are applied
    expect(screen.getByTestId('custom-item-0')).toBeInTheDocument();
    expect(screen.getByTestId('custom-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('custom-item-2')).toBeInTheDocument();
  });
  
  it('handles empty items array', () => {
    render(<AnimatedList items={[]} />);
    
    // Check if the container is rendered but no items
    expect(screen.getByTestId('motion-ul')).toBeInTheDocument();
    expect(screen.queryAllByTestId('motion-li')).toHaveLength(0);
  });
});
