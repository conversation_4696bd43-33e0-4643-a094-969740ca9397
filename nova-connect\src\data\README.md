# NovaFuse Universal API Connector - Data Layer

This directory contains the data layer for the NovaFuse Universal API Connector (UAC). The data layer is responsible for storing and retrieving data from the database.

## Structure

- `db-connection.js` - Database connection manager
- `index.js` - Main entry point for the data layer
- `models/` - MongoDB models
  - `connector.js` - Connector template model
  - `credential.js` - API credential model
  - `api-usage.js` - API usage tracking model
  - `partner.js` - Partner information model
- `repositories/` - Data access repositories
  - `connector-repository.js` - Connector template repository
  - `credential-repository.js` - API credential repository
  - `api-usage-repository.js` - API usage tracking repository
  - `partner-repository.js` - Partner information repository

## Usage

### Connecting to the Database

```javascript
const { dbConnection } = require('./data');

// Connect to the database
await dbConnection.connect();

// Disconnect from the database
await dbConnection.disconnect();
```

### Working with Connectors

```javascript
const { repositories } = require('./data');
const { connectorRepository } = repositories;

// Get a connector by ID
const connector = await connectorRepository.getConnectorById('google-cloud-security-1.0');

// Get connectors by category
const securityConnectors = await connectorRepository.getConnectorsByCategory('security');

// Search connectors
const searchResults = await connectorRepository.searchConnectors('google');

// Create a connector
const newConnector = await connectorRepository.createConnector({
  metadata: {
    name: 'New Connector',
    version: '1.0',
    category: 'security',
    description: 'A new connector'
  },
  // ... other connector properties
});

// Update a connector
await connectorRepository.updateConnector('new-connector-1.0', {
  metadata: {
    description: 'Updated description'
  }
});

// Delete a connector
await connectorRepository.deleteConnector('new-connector-1.0');
```

### Working with Credentials

```javascript
const { repositories } = require('./data');
const { credentialRepository } = repositories;

// Get a credential by ID
const credential = await credentialRepository.getCredentialById('cred-12345');

// Get credentials by connector
const credentials = await credentialRepository.getCredentialsByConnector('google-cloud-security-1.0', 'user-123');

// Create a credential
const newCredential = await credentialRepository.createCredential({
  name: 'Google Cloud Credentials',
  description: 'Credentials for Google Cloud Security',
  connectorId: 'google-cloud-security-1.0',
  credentials: new Map([
    ['apiKey', 'my-api-key']
  ]),
  ownerId: 'user-123'
});

// Update a credential
await credentialRepository.updateCredential('cred-12345', {
  name: 'Updated Credential Name'
});

// Delete a credential
await credentialRepository.deleteCredential('cred-12345');

// Get decrypted credentials
const decryptedCredentials = await credentialRepository.getDecryptedCredentials('cred-12345');
```

### Working with API Usage

```javascript
const { repositories } = require('./data');
const { apiUsageRepository } = repositories;

// Record API usage
await apiUsageRepository.recordUsage({
  partnerId: 'partner-123',
  connectorId: 'google-cloud-security-1.0',
  endpointId: 'get-findings',
  credentialId: 'cred-12345',
  requestId: 'req-12345',
  success: true,
  statusCode: 200,
  duration: 150,
  requestSize: 100,
  responseSize: 500,
  ipAddress: '***********',
  userAgent: 'Mozilla/5.0',
  billable: true
});

// Get usage statistics
const statistics = await apiUsageRepository.getStatistics({ partnerId: 'partner-123' }, 'month');

// Get usage by partner
const partnerUsage = await apiUsageRepository.getUsageByPartner('partner-123');

// Get usage summary
const usageSummary = await apiUsageRepository.getUsageSummary('partner-123', 'month');
```

### Working with Partners

```javascript
const { repositories } = require('./data');
const { partnerRepository } = repositories;

// Get a partner by ID
const partner = await partnerRepository.getPartnerById('partner-123');

// Get a partner by API key
const partnerByApiKey = await partnerRepository.getPartnerByApiKey('api-key-123');

// Validate API credentials
const validatedPartner = await partnerRepository.validateApiCredentials('api-key-123', 'api-secret-123');

// Create a partner
const newPartner = await partnerRepository.createPartner({
  name: 'New Partner',
  description: 'A new partner',
  contactInfo: {
    primaryEmail: '<EMAIL>'
  }
});

// Update a partner
await partnerRepository.updatePartner('partner-123', {
  name: 'Updated Partner Name'
});

// Delete a partner
await partnerRepository.deletePartner('partner-123');

// Regenerate API credentials
const newCredentials = await partnerRepository.regenerateApiCredentials('partner-123');

// Check partner limits
const withinLimits = await partnerRepository.checkPartnerLimits('partner-123', 'requests', 5000);
```

## Environment Variables

- `MONGODB_URI` - MongoDB connection URI (default: `mongodb://localhost:27017/nova-connect`)
- `CREDENTIALS_ENCRYPTION_KEY` - Key for encrypting sensitive credential data
- `API_USAGE_TTL_DAYS` - Number of days to keep API usage data (default: 90)
