{"name": "test_end_to_end_workflow", "status": "passed", "description": "Test a complete workflow through the NovaCortex system.", "start": 1752966149056, "stop": 1752966149069, "uuid": "042fad0e-4f8a-438f-99e5-c98d6c05c2fa", "historyId": "236f3645d2bf93d86ec818cc88690c7e", "testCaseId": "236f3645d2bf93d86ec818cc88690c7e", "fullName": "tests.novacortex.test_integration#test_end_to_end_workflow", "labels": [{"name": "tag", "value": "asyncio"}, {"name": "parentSuite", "value": "tests.novacortex"}, {"name": "suite", "value": "test_integration"}, {"name": "host", "value": "d1cae64bda82"}, {"name": "thread", "value": "1-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "tests.novacortex.test_integration"}]}