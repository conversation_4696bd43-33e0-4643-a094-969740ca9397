/**
 * NovaFuse Test Results Database
 * SQLite-based persistence for test execution history, analytics, and reporting
 */

const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;

class TestResultsDatabase {
    constructor(options = {}) {
        this.options = {
            dbPath: options.dbPath || path.join(__dirname, '../data/test-results.db'),
            enableWAL: options.enableWAL !== false,
            enableLogging: options.enableLogging !== false,
            ...options
        };
        
        this.db = null;
        this.isInitialized = false;
    }
    
    /**
     * Initialize the database
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        
        try {
            // Ensure data directory exists
            const dataDir = path.dirname(this.options.dbPath);
            await fs.mkdir(dataDir, { recursive: true });
            
            // Open database connection
            this.db = new sqlite3.Database(this.options.dbPath);
            
            // Enable WAL mode for better concurrency
            if (this.options.enableWAL) {
                await this.runQuery('PRAGMA journal_mode=WAL');
            }
            
            // Create tables
            await this.createTables();
            
            this.isInitialized = true;
            this.log('Test Results Database initialized successfully');
            
        } catch (error) {
            this.log('Failed to initialize database:', error);
            throw error;
        }
    }
    
    /**
     * Create database tables
     */
    async createTables() {
        const tables = [
            // Test executions table
            `CREATE TABLE IF NOT EXISTS test_executions (
                id TEXT PRIMARY KEY,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                duration INTEGER,
                status TEXT NOT NULL,
                categories TEXT,
                total_tests INTEGER DEFAULT 0,
                passed_tests INTEGER DEFAULT 0,
                failed_tests INTEGER DEFAULT 0,
                skipped_tests INTEGER DEFAULT 0,
                pass_rate REAL,
                error_message TEXT,
                options TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            
            // Test categories table
            `CREATE TABLE IF NOT EXISTS test_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                execution_id TEXT NOT NULL,
                category_key TEXT NOT NULL,
                category_name TEXT NOT NULL,
                total_tests INTEGER DEFAULT 0,
                passed_tests INTEGER DEFAULT 0,
                failed_tests INTEGER DEFAULT 0,
                skipped_tests INTEGER DEFAULT 0,
                duration INTEGER,
                FOREIGN KEY (execution_id) REFERENCES test_executions(id)
            )`,
            
            // Individual test results table
            `CREATE TABLE IF NOT EXISTS test_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                execution_id TEXT NOT NULL,
                category_id INTEGER NOT NULL,
                file_name TEXT NOT NULL,
                file_path TEXT,
                status TEXT NOT NULL,
                duration INTEGER,
                runner TEXT,
                output TEXT,
                error_details TEXT,
                started_at DATETIME,
                completed_at DATETIME,
                FOREIGN KEY (execution_id) REFERENCES test_executions(id),
                FOREIGN KEY (category_id) REFERENCES test_categories(id)
            )`,
            
            // Test metrics table for analytics
            `CREATE TABLE IF NOT EXISTS test_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                execution_id TEXT NOT NULL,
                metric_name TEXT NOT NULL,
                metric_value REAL,
                metric_unit TEXT,
                category TEXT,
                recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (execution_id) REFERENCES test_executions(id)
            )`,
            
            // Test trends table for historical analysis
            `CREATE TABLE IF NOT EXISTS test_trends (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE NOT NULL,
                category TEXT,
                total_executions INTEGER DEFAULT 0,
                avg_pass_rate REAL,
                avg_duration INTEGER,
                total_tests INTEGER DEFAULT 0,
                trend_direction TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];
        
        for (const tableSQL of tables) {
            await this.runQuery(tableSQL);
        }
        
        // Create indexes for better performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_executions_start_time ON test_executions(start_time)',
            'CREATE INDEX IF NOT EXISTS idx_executions_status ON test_executions(status)',
            'CREATE INDEX IF NOT EXISTS idx_categories_execution ON test_categories(execution_id)',
            'CREATE INDEX IF NOT EXISTS idx_files_execution ON test_files(execution_id)',
            'CREATE INDEX IF NOT EXISTS idx_files_status ON test_files(status)',
            'CREATE INDEX IF NOT EXISTS idx_metrics_execution ON test_metrics(execution_id)',
            'CREATE INDEX IF NOT EXISTS idx_trends_date ON test_trends(date)'
        ];
        
        for (const indexSQL of indexes) {
            await this.runQuery(indexSQL);
        }
    }
    
    /**
     * Store test execution results
     */
    async storeTestExecution(executionData) {
        const {
            id,
            startTime,
            endTime,
            duration,
            status,
            categories,
            results,
            options,
            error
        } = executionData;
        
        try {
            // Calculate pass rate
            const passRate = results.total > 0 ? (results.passed / results.total) * 100 : 0;
            
            // Store main execution record
            await this.runQuery(
                `INSERT INTO test_executions (
                    id, start_time, end_time, duration, status, categories,
                    total_tests, passed_tests, failed_tests, skipped_tests,
                    pass_rate, error_message, options
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    id,
                    startTime,
                    endTime,
                    duration,
                    status,
                    JSON.stringify(categories),
                    results.total,
                    results.passed,
                    results.failed,
                    results.skipped,
                    passRate,
                    error || null,
                    JSON.stringify(options)
                ]
            );
            
            // Store category results
            if (results.categories) {
                for (const [categoryKey, categoryData] of Object.entries(results.categories)) {
                    const categoryId = await this.runQuery(
                        `INSERT INTO test_categories (
                            execution_id, category_key, category_name,
                            total_tests, passed_tests, failed_tests, skipped_tests,
                            duration
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                        [
                            id,
                            categoryKey,
                            categoryData.name,
                            categoryData.total,
                            categoryData.passed,
                            categoryData.failed,
                            categoryData.skipped,
                            categoryData.duration || null
                        ],
                        true // Return lastID
                    );
                    
                    // Store individual file results
                    if (categoryData.files) {
                        for (const fileResult of categoryData.files) {
                            await this.runQuery(
                                `INSERT INTO test_files (
                                    execution_id, category_id, file_name, file_path,
                                    status, duration, runner, output, error_details
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                                [
                                    id,
                                    categoryId,
                                    fileResult.file,
                                    fileResult.path || null,
                                    fileResult.status,
                                    fileResult.duration,
                                    fileResult.runner,
                                    fileResult.output || null,
                                    fileResult.error || null
                                ]
                            );
                        }
                    }
                }
            }
            
            // Update trends
            await this.updateTrends(startTime, results);
            
            this.log(`Stored test execution: ${id}`);
            return { success: true, executionId: id };
            
        } catch (error) {
            this.log('Failed to store test execution:', error);
            throw error;
        }
    }
    
    /**
     * Get test execution history
     */
    async getExecutionHistory(options = {}) {
        const {
            limit = 50,
            offset = 0,
            status,
            category,
            startDate,
            endDate
        } = options;
        
        let whereClause = '';
        const params = [];
        
        const conditions = [];
        
        if (status) {
            conditions.push('status = ?');
            params.push(status);
        }
        
        if (category) {
            conditions.push('categories LIKE ?');
            params.push(`%"${category}"%`);
        }
        
        if (startDate) {
            conditions.push('start_time >= ?');
            params.push(startDate);
        }
        
        if (endDate) {
            conditions.push('start_time <= ?');
            params.push(endDate);
        }
        
        if (conditions.length > 0) {
            whereClause = 'WHERE ' + conditions.join(' AND ');
        }
        
        const query = `
            SELECT * FROM test_executions 
            ${whereClause}
            ORDER BY start_time DESC 
            LIMIT ? OFFSET ?
        `;
        
        params.push(limit, offset);
        
        const executions = await this.allQuery(query, params);
        
        // Parse JSON fields
        return executions.map(exec => ({
            ...exec,
            categories: JSON.parse(exec.categories || '[]'),
            options: JSON.parse(exec.options || '{}')
        }));
    }
    
    /**
     * Get detailed execution results
     */
    async getExecutionDetails(executionId) {
        try {
            // Get main execution data
            const execution = await this.getQuery(
                'SELECT * FROM test_executions WHERE id = ?',
                [executionId]
            );
            
            if (!execution) {
                return null;
            }
            
            // Get category results
            const categories = await this.allQuery(
                'SELECT * FROM test_categories WHERE execution_id = ?',
                [executionId]
            );
            
            // Get file results for each category
            for (const category of categories) {
                category.files = await this.allQuery(
                    'SELECT * FROM test_files WHERE category_id = ?',
                    [category.id]
                );
            }
            
            return {
                ...execution,
                categories: JSON.parse(execution.categories || '[]'),
                options: JSON.parse(execution.options || '{}'),
                categoryDetails: categories
            };
            
        } catch (error) {
            this.log('Failed to get execution details:', error);
            throw error;
        }
    }
    
    /**
     * Get test analytics and trends
     */
    async getAnalytics(options = {}) {
        const {
            days = 30,
            category
        } = options;
        
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        
        try {
            // Get execution summary
            let summaryQuery = `
                SELECT 
                    COUNT(*) as total_executions,
                    AVG(pass_rate) as avg_pass_rate,
                    AVG(duration) as avg_duration,
                    SUM(total_tests) as total_tests,
                    SUM(passed_tests) as total_passed,
                    SUM(failed_tests) as total_failed
                FROM test_executions 
                WHERE start_time >= ?
            `;
            
            const summaryParams = [startDate.toISOString()];
            
            if (category) {
                summaryQuery += ' AND categories LIKE ?';
                summaryParams.push(`%"${category}"%`);
            }
            
            const summary = await this.getQuery(summaryQuery, summaryParams);
            
            // Get daily trends
            const trendsQuery = `
                SELECT 
                    DATE(start_time) as date,
                    COUNT(*) as executions,
                    AVG(pass_rate) as avg_pass_rate,
                    AVG(duration) as avg_duration
                FROM test_executions 
                WHERE start_time >= ?
                ${category ? 'AND categories LIKE ?' : ''}
                GROUP BY DATE(start_time)
                ORDER BY date DESC
            `;
            
            const trends = await this.allQuery(trendsQuery, summaryParams);
            
            // Get category breakdown
            const categoryQuery = `
                SELECT 
                    category_key,
                    category_name,
                    COUNT(*) as executions,
                    AVG(passed_tests * 100.0 / total_tests) as avg_pass_rate,
                    AVG(duration) as avg_duration
                FROM test_categories tc
                JOIN test_executions te ON tc.execution_id = te.id
                WHERE te.start_time >= ?
                GROUP BY category_key, category_name
                ORDER BY executions DESC
            `;
            
            const categoryBreakdown = await this.allQuery(categoryQuery, [startDate.toISOString()]);
            
            return {
                summary,
                trends,
                categoryBreakdown,
                period: {
                    days,
                    startDate: startDate.toISOString(),
                    endDate: new Date().toISOString()
                }
            };
            
        } catch (error) {
            this.log('Failed to get analytics:', error);
            throw error;
        }
    }
    
    /**
     * Update trends data
     */
    async updateTrends(executionDate, results) {
        const date = new Date(executionDate).toISOString().split('T')[0];
        
        try {
            // Update overall trends
            await this.runQuery(
                `INSERT OR REPLACE INTO test_trends (
                    date, total_executions, avg_pass_rate, avg_duration, total_tests
                ) VALUES (
                    ?,
                    COALESCE((SELECT total_executions FROM test_trends WHERE date = ?), 0) + 1,
                    (SELECT AVG(pass_rate) FROM test_executions WHERE DATE(start_time) = ?),
                    (SELECT AVG(duration) FROM test_executions WHERE DATE(start_time) = ?),
                    COALESCE((SELECT total_tests FROM test_trends WHERE date = ?), 0) + ?
                )`,
                [date, date, date, date, date, results.total]
            );
            
        } catch (error) {
            this.log('Failed to update trends:', error);
            // Don't throw - trends are not critical
        }
    }
    
    /**
     * Helper method to run a query
     */
    runQuery(sql, params = [], returnLastID = false) {
        return new Promise((resolve, reject) => {
            if (returnLastID) {
                this.db.run(sql, params, function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                });
            } else {
                this.db.run(sql, params, (err) => {
                    if (err) reject(err);
                    else resolve();
                });
            }
        });
    }
    
    /**
     * Helper method to get a single row
     */
    getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) reject(err);
                else resolve(row);
            });
        });
    }
    
    /**
     * Helper method to get all rows
     */
    allQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
    }
    
    /**
     * Close database connection
     */
    async close() {
        if (this.db) {
            return new Promise((resolve) => {
                this.db.close((err) => {
                    if (err) {
                        this.log('Error closing database:', err);
                    } else {
                        this.log('Database connection closed');
                    }
                    resolve();
                });
            });
        }
    }
    
    /**
     * Logging utility
     */
    log(...args) {
        if (this.options.enableLogging) {
            const timestamp = new Date().toISOString();
            console.log(`[${timestamp}] [TestResultsDB]`, ...args);
        }
    }
}

module.exports = TestResultsDatabase;
