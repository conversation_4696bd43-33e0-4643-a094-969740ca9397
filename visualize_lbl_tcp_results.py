#!/usr/bin/env python3
"""
Visualize the LBL-TCP-3 data analysis results.
This script creates visualizations of the LBL-TCP-3 data analysis results
and the integration with the UUFT framework.
"""

import os
import sys
import json
import logging
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('lbl_tcp_visualization.log')
    ]
)
logger = logging.getLogger('LBL_TCP_Visualization')

# Constants
RESULTS_DIR = "results"
LBL_TCP_RESULTS_DIR = "lbl_tcp_results"
VISUALIZATION_DIR = "visualizations"
os.makedirs(VISUALIZATION_DIR, exist_ok=True)

def load_lbl_tcp_results():
    """Load the LBL-TCP-3 analysis results."""
    results_path = os.path.join(LBL_TCP_RESULTS_DIR, "lbl_tcp_analysis_results.json")
    logger.info(f"Loading LBL-TCP-3 analysis results from {results_path}")
    
    with open(results_path, 'r') as f:
        results = json.load(f)
    
    logger.info(f"Loaded LBL-TCP-3 analysis results with {len(results['1882_pattern_results'])} 18/82 pattern results and {len(results['pi_relationship_results'])} Pi relationship results")
    
    return results

def load_integration_report():
    """Load the LBL-TCP-3 UUFT integration report."""
    report_path = os.path.join(RESULTS_DIR, "lbl_tcp_uuft_integration_report.json")
    logger.info(f"Loading LBL-TCP-3 UUFT integration report from {report_path}")
    
    with open(report_path, 'r') as f:
        report = json.load(f)
    
    logger.info(f"Loaded LBL-TCP-3 UUFT integration report")
    
    return report

def visualize_1882_patterns(results):
    """Create visualizations of the 18/82 pattern results."""
    logger.info("Creating 18/82 pattern visualizations...")
    
    # Extract data for visualization
    features = [r['feature'] for r in results['1882_pattern_results']]
    is_1882 = [r['is_1882_pattern'] for r in results['1882_pattern_results']]
    proximity = [float(r['proximity_to_1882_percent']) for r in results['1882_pattern_results']]
    
    # Create a figure with two subplots
    fig = plt.figure(figsize=(12, 10))
    gs = GridSpec(2, 1, height_ratios=[1, 2])
    
    # Plot 1: 18/82 pattern presence (bar chart)
    ax1 = fig.add_subplot(gs[0])
    colors = ['green' if x else 'red' for x in is_1882]
    ax1.bar(features, [1 if x else 0 for x in is_1882], color=colors)
    ax1.set_title('18/82 Pattern Presence in LBL-TCP-3 Data')
    ax1.set_ylabel('Pattern Present')
    ax1.set_ylim(0, 1.2)
    ax1.set_xticklabels(features, rotation=45, ha='right')
    
    # Add text labels
    for i, feature in enumerate(features):
        ax1.text(i, 0.5, 'Present' if is_1882[i] else 'Absent', 
                 ha='center', va='center', color='white', fontweight='bold')
    
    # Plot 2: Proximity to 18/82 ratio (bar chart with log scale)
    ax2 = fig.add_subplot(gs[1])
    bars = ax2.bar(features, proximity, color='skyblue')
    ax2.set_title('Proximity to 18/82 Ratio (lower is better)')
    ax2.set_ylabel('Proximity (%)')
    ax2.set_yscale('log')
    ax2.set_xticklabels(features, rotation=45, ha='right')
    
    # Add a horizontal line at 5% threshold
    ax2.axhline(y=5, color='red', linestyle='--', label='5% Threshold')
    ax2.legend()
    
    # Add text labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        if height < 1:
            ax2.text(bar.get_x() + bar.get_width()/2., 1.1, f'{proximity[i]:.2f}%',
                     ha='center', va='bottom', rotation=0)
        else:
            ax2.text(bar.get_x() + bar.get_width()/2., height * 1.1, f'{proximity[i]:.2f}%',
                     ha='center', va='bottom', rotation=0)
    
    plt.tight_layout()
    plt.savefig(os.path.join(VISUALIZATION_DIR, '1882_patterns_lbl_tcp.png'), dpi=300)
    logger.info(f"18/82 pattern visualization saved to {os.path.join(VISUALIZATION_DIR, '1882_patterns_lbl_tcp.png')}")
    plt.close()

def visualize_pi_relationships(results):
    """Create visualizations of the Pi relationship results."""
    logger.info("Creating Pi relationship visualizations...")
    
    # Extract data for visualization
    features = [r['feature'] for r in results['pi_relationship_results']]
    pi_values = [int(r['pi_values_count']) for r in results['pi_relationship_results']]
    pi_ratios = [int(r['pi_ratios_count']) for r in results['pi_relationship_results']]
    pi_10e3_values = [int(r['pi_10e3_values_count']) for r in results['pi_relationship_results']]
    pi_10e3_ratios = [int(r['pi_10e3_ratios_count']) for r in results['pi_relationship_results']]
    
    # Create a figure
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # Set the width of the bars
    bar_width = 0.2
    
    # Set the positions of the bars on the x-axis
    r1 = np.arange(len(features))
    r2 = [x + bar_width for x in r1]
    r3 = [x + bar_width for x in r2]
    r4 = [x + bar_width for x in r3]
    
    # Create the bars
    ax.bar(r1, pi_values, width=bar_width, label='Pi Values', color='blue')
    ax.bar(r2, pi_ratios, width=bar_width, label='Pi Ratios', color='green')
    ax.bar(r3, pi_10e3_values, width=bar_width, label='Pi*10^3 Values', color='orange')
    ax.bar(r4, pi_10e3_ratios, width=bar_width, label='Pi*10^3 Ratios', color='red')
    
    # Add labels and title
    ax.set_title('Pi Relationships in LBL-TCP-3 Data')
    ax.set_xlabel('Features')
    ax.set_ylabel('Count (log scale)')
    ax.set_xticks([r + bar_width * 1.5 for r in range(len(features))])
    ax.set_xticklabels(features, rotation=45, ha='right')
    ax.set_yscale('log')
    ax.legend()
    
    # Add count labels
    for i, v in enumerate(pi_values):
        if v > 0:
            ax.text(r1[i], v * 1.1, str(v), ha='center', va='bottom')
    
    for i, v in enumerate(pi_ratios):
        if v > 0:
            ax.text(r2[i], v * 1.1, str(v), ha='center', va='bottom')
    
    for i, v in enumerate(pi_10e3_values):
        if v > 0:
            ax.text(r3[i], v * 1.1, str(v), ha='center', va='bottom')
    
    for i, v in enumerate(pi_10e3_ratios):
        if v > 0:
            ax.text(r4[i], v * 1.1, str(v), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig(os.path.join(VISUALIZATION_DIR, 'pi_relationships_lbl_tcp.png'), dpi=300)
    logger.info(f"Pi relationship visualization saved to {os.path.join(VISUALIZATION_DIR, 'pi_relationships_lbl_tcp.png')}")
    plt.close()

def visualize_uuft_alignment(integration_report):
    """Create visualizations of the UUFT alignment."""
    logger.info("Creating UUFT alignment visualizations...")
    
    # Extract data for visualization
    alignment_1882 = float(integration_report['uuft_alignment']['18/82_pattern_alignment'])
    alignment_pi = float(integration_report['uuft_alignment']['pi_relationship_alignment'])
    
    # Create a figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
    
    # Plot 1: 18/82 Pattern Alignment (gauge chart)
    gauge_colors = ['red', 'orange', 'yellow', 'yellowgreen', 'green']
    gauge_bounds = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
    
    # Create a gauge chart for 18/82 alignment
    ax1.set_title('18/82 Pattern Alignment with UUFT')
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # Draw the gauge background
    for i in range(len(gauge_bounds) - 1):
        ax1.add_patch(plt.Rectangle((gauge_bounds[i], 0.1), gauge_bounds[i+1] - gauge_bounds[i], 0.1, 
                                    color=gauge_colors[i], alpha=0.3))
    
    # Draw the gauge value
    ax1.add_patch(plt.Rectangle((0, 0.1), alignment_1882, 0.1, color='blue', alpha=0.7))
    
    # Add text labels
    ax1.text(0.5, 0.5, f'{alignment_1882:.2f}', ha='center', va='center', fontsize=24)
    ax1.text(0.5, 0.3, 'Alignment Score', ha='center', va='center', fontsize=12)
    
    # Add scale labels
    for i, bound in enumerate(gauge_bounds):
        ax1.text(bound, 0.05, f'{bound:.1f}', ha='center', va='center', fontsize=8)
    
    # Plot 2: Pi Relationship Alignment (bar chart)
    ax2.set_title('Pi Relationship Alignment with UUFT')
    ax2.bar(['Pi Relationship Alignment'], [alignment_pi], color='purple')
    ax2.set_ylabel('Alignment Score')
    ax2.set_ylim(0, alignment_pi * 1.2)
    
    # Add text label
    ax2.text(0, alignment_pi * 1.05, f'{alignment_pi:.2f}', ha='center', va='bottom', fontsize=12)
    
    plt.tight_layout()
    plt.savefig(os.path.join(VISUALIZATION_DIR, 'uuft_alignment_lbl_tcp.png'), dpi=300)
    logger.info(f"UUFT alignment visualization saved to {os.path.join(VISUALIZATION_DIR, 'uuft_alignment_lbl_tcp.png')}")
    plt.close()

def main():
    """Main function to visualize LBL-TCP-3 data analysis results."""
    logger.info("Starting LBL-TCP-3 data visualization...")
    
    # Load the results
    lbl_tcp_results = load_lbl_tcp_results()
    integration_report = load_integration_report()
    
    # Create visualizations
    visualize_1882_patterns(lbl_tcp_results)
    visualize_pi_relationships(lbl_tcp_results)
    visualize_uuft_alignment(integration_report)
    
    logger.info("LBL-TCP-3 data visualization complete.")
    logger.info(f"All visualizations saved to {VISUALIZATION_DIR}")

if __name__ == "__main__":
    main()
