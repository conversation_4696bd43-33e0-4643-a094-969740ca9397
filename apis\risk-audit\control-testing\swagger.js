/**
 * @swagger
 * tags:
 *   name: Control Testing
 *   description: Control Testing API
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Control:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the control
 *         title:
 *           type: string
 *           description: Title of the control
 *         description:
 *           type: string
 *           description: Description of the control
 *         type:
 *           type: string
 *           enum: [preventive, detective, corrective, directive]
 *           description: Type of control
 *         category:
 *           type: string
 *           enum: [administrative, technical, physical]
 *           description: Category of control
 *         status:
 *           type: string
 *           enum: [draft, implemented, under-review, approved, deprecated]
 *           description: Status of the control
 *         owner:
 *           type: string
 *           description: Owner of the control
 *         framework:
 *           type: string
 *           description: Compliance framework the control is associated with
 *         riskLevel:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Risk level the control addresses
 *         testFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, semi-annually, annually, as-needed]
 *           description: Frequency of control testing
 *         lastTestedDate:
 *           type: string
 *           format: date
 *           description: Date when the control was last tested
 *         nextTestDate:
 *           type: string
 *           format: date
 *           description: Date when the control is scheduled to be tested next
 *         testProcedure:
 *           type: string
 *           description: Procedure for testing the control
 *         testResults:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/ControlTestResult'
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the control was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the control was last updated
 *       required:
 *         - id
 *         - title
 *         - type
 *         - category
 *         - status
 *         - owner
 *         - testFrequency
 *         - createdAt
 *         - updatedAt
 *     
 *     ControlInput:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Title of the control
 *         description:
 *           type: string
 *           description: Description of the control
 *         type:
 *           type: string
 *           enum: [preventive, detective, corrective, directive]
 *           description: Type of control
 *         category:
 *           type: string
 *           enum: [administrative, technical, physical]
 *           description: Category of control
 *         status:
 *           type: string
 *           enum: [draft, implemented, under-review, approved, deprecated]
 *           description: Status of the control
 *         owner:
 *           type: string
 *           description: Owner of the control
 *         framework:
 *           type: string
 *           description: Compliance framework the control is associated with
 *         riskLevel:
 *           type: string
 *           enum: [critical, high, medium, low]
 *           description: Risk level the control addresses
 *         testFrequency:
 *           type: string
 *           enum: [daily, weekly, monthly, quarterly, semi-annually, annually, as-needed]
 *           description: Frequency of control testing
 *         testProcedure:
 *           type: string
 *           description: Procedure for testing the control
 *       required:
 *         - title
 *         - type
 *         - category
 *         - status
 *         - owner
 *         - testFrequency
 *     
 *     ControlTestResult:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the test result
 *         controlId:
 *           type: string
 *           description: ID of the control that was tested
 *         testDate:
 *           type: string
 *           format: date
 *           description: Date when the test was performed
 *         tester:
 *           type: string
 *           description: Person who performed the test
 *         result:
 *           type: string
 *           enum: [pass, fail, inconclusive, not-applicable]
 *           description: Result of the test
 *         evidence:
 *           type: string
 *           description: Evidence supporting the test result
 *         notes:
 *           type: string
 *           description: Additional notes about the test
 *         remediation:
 *           type: string
 *           description: Remediation steps if the test failed
 *         remediationDueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation if the test failed
 *         remediationStatus:
 *           type: string
 *           enum: [not-required, pending, in-progress, completed, verified]
 *           description: Status of remediation if the test failed
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the test result was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Date and time when the test result was last updated
 *       required:
 *         - id
 *         - controlId
 *         - testDate
 *         - tester
 *         - result
 *         - createdAt
 *         - updatedAt
 *     
 *     ControlTestResultInput:
 *       type: object
 *       properties:
 *         testDate:
 *           type: string
 *           format: date
 *           description: Date when the test was performed
 *         tester:
 *           type: string
 *           description: Person who performed the test
 *         result:
 *           type: string
 *           enum: [pass, fail, inconclusive, not-applicable]
 *           description: Result of the test
 *         evidence:
 *           type: string
 *           description: Evidence supporting the test result
 *         notes:
 *           type: string
 *           description: Additional notes about the test
 *         remediation:
 *           type: string
 *           description: Remediation steps if the test failed
 *         remediationDueDate:
 *           type: string
 *           format: date
 *           description: Due date for remediation if the test failed
 *         remediationStatus:
 *           type: string
 *           enum: [not-required, pending, in-progress, completed, verified]
 *           description: Status of remediation if the test failed
 *       required:
 *         - testDate
 *         - tester
 *         - result
 */
