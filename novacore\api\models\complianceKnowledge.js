/**
 * Compliance Knowledge Model
 * 
 * This model represents knowledge about compliance frameworks, controls,
 * regulations, and best practices that NovaAssistAI can use to provide
 * accurate and helpful responses.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ComplianceKnowledgeSchema = new Schema({
  title: {
    type: String,
    required: true,
    index: true
  },
  content: {
    type: String,
    required: true
  },
  source: {
    type: String,
    enum: ['framework', 'regulation', 'standard', 'best_practice', 'internal', 'external'],
    required: true
  },
  sourceDetails: {
    name: String,
    version: String,
    url: String,
    publishedDate: Date
  },
  category: {
    type: String,
    enum: [
      'access_control',
      'asset_management',
      'audit_logging',
      'business_continuity',
      'change_management',
      'cryptography',
      'data_protection',
      'incident_response',
      'network_security',
      'physical_security',
      'policy_management',
      'risk_management',
      'secure_development',
      'security_awareness',
      'third_party_management',
      'vulnerability_management',
      'other'
    ],
    required: true
  },
  frameworks: [{
    type: String,
    enum: [
      'soc2',
      'hipaa',
      'gdpr',
      'pci_dss',
      'iso27001',
      'nist_csf',
      'nist_800_53',
      'fedramp',
      'ccpa',
      'cmmc',
      'other'
    ]
  }],
  controls: [{
    frameworkId: {
      type: String,
      required: true
    },
    controlId: {
      type: String,
      required: true
    },
    controlName: String
  }],
  keywords: [{
    type: String,
    index: true
  }],
  relevantPages: [{
    type: String,
    enum: [
      'dashboard',
      'novaassure',
      'frameworks',
      'regulations',
      'settings',
      'reports',
      'evidence',
      'controls',
      'tests',
      'attestations',
      'other'
    ]
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  priority: {
    type: Number,
    default: 5,
    min: 1,
    max: 10
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {}
  }
});

// Update the updatedAt field before saving
ComplianceKnowledgeSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Generate keywords from title and content if not provided
ComplianceKnowledgeSchema.pre('save', function(next) {
  if (!this.keywords || this.keywords.length === 0) {
    const text = `${this.title} ${this.content}`;
    const stopWords = ['a', 'an', 'the', 'and', 'or', 'but', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'about', 'against', 'between', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'from', 'up', 'down', 'of', 'off', 'over', 'under', 'again', 'further', 'then', 'once', 'here', 'there', 'when', 'where', 'why', 'how', 'all', 'any', 'both', 'each', 'few', 'more', 'most', 'other', 'some', 'such', 'no', 'nor', 'not', 'only', 'own', 'same', 'so', 'than', 'too', 'very', 's', 't', 'can', 'will', 'just', 'don', 'should', 'now'];
    
    // Convert to lowercase and remove punctuation
    const cleanedText = text.toLowerCase().replace(/[^\w\s]/g, '');
    
    // Split into words and filter out stop words
    const words = cleanedText.split(/\s+/).filter(word => !stopWords.includes(word) && word.length > 2);
    
    // Get unique keywords
    this.keywords = [...new Set(words)];
  }
  next();
});

// Create text index for full-text search
ComplianceKnowledgeSchema.index({ title: 'text', content: 'text' });

module.exports = mongoose.model('ComplianceKnowledge', ComplianceKnowledgeSchema);
