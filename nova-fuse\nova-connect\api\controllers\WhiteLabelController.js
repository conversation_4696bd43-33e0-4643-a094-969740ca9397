/**
 * White Label Controller
 * 
 * This controller handles API requests related to white-labeling.
 */

const WhiteLabelService = require('../services/WhiteLabelService');
const { ValidationError } = require('../utils/errors');

class WhiteLabelController {
  constructor() {
    this.whiteLabelService = new WhiteLabelService();
  }

  /**
   * Get white label settings for an organization
   */
  async getWhiteLabelSettings(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const settings = await this.whiteLabelService.getWhiteLabelSettings(organizationId);
      res.json(settings);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update white label settings for an organization
   */
  async updateWhiteLabelSettings(req, res, next) {
    try {
      const { organizationId } = req.params;
      const data = req.body;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!data) {
        throw new ValidationError('Settings data is required');
      }
      
      const settings = await this.whiteLabelService.updateWhiteLabelSettings(
        organizationId, 
        data, 
        req.user.id
      );
      
      res.json(settings);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get custom domains for an organization
   */
  async getCustomDomains(req, res, next) {
    try {
      const { organizationId } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      const domains = await this.whiteLabelService.getCustomDomains(organizationId);
      res.json(domains);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add custom domain for an organization
   */
  async addCustomDomain(req, res, next) {
    try {
      const { organizationId } = req.params;
      const data = req.body;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!data || !data.domain) {
        throw new ValidationError('Domain is required');
      }
      
      const domain = await this.whiteLabelService.addCustomDomain(
        organizationId, 
        data, 
        req.user.id
      );
      
      res.status(201).json(domain);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Verify custom domain for an organization
   */
  async verifyCustomDomain(req, res, next) {
    try {
      const { organizationId, domain } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!domain) {
        throw new ValidationError('Domain is required');
      }
      
      const result = await this.whiteLabelService.verifyCustomDomain(
        organizationId, 
        domain, 
        req.user.id
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete custom domain for an organization
   */
  async deleteCustomDomain(req, res, next) {
    try {
      const { organizationId, domain } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!domain) {
        throw new ValidationError('Domain is required');
      }
      
      const result = await this.whiteLabelService.deleteCustomDomain(
        organizationId, 
        domain, 
        req.user.id
      );
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get domain verification instructions
   */
  async getDomainVerificationInstructions(req, res, next) {
    try {
      const { organizationId, domain } = req.params;
      
      if (!organizationId) {
        throw new ValidationError('Organization ID is required');
      }
      
      if (!domain) {
        throw new ValidationError('Domain is required');
      }
      
      // Get domains for organization
      const domains = await this.whiteLabelService.getCustomDomains(organizationId);
      const domainRecord = domains.find(d => d.domain.toLowerCase() === domain.toLowerCase());
      
      if (!domainRecord) {
        throw new ValidationError(`Domain ${domain} not found for organization ${organizationId}`);
      }
      
      const instructions = this.whiteLabelService.getDomainVerificationInstructions(
        domain, 
        domainRecord.verificationToken
      );
      
      res.json(instructions);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get white label package by domain
   */
  async getWhiteLabelPackage(req, res, next) {
    try {
      const { domain } = req.params;
      
      if (!domain) {
        throw new ValidationError('Domain is required');
      }
      
      const package = await this.whiteLabelService.getWhiteLabelPackage(domain);
      res.json(package);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new WhiteLabelController();
