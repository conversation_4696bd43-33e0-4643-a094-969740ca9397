/**
 * CHAEONIX CENTRAL DATA COORDINATOR
 * Unified data flow from MT5 to all dashboard components
 * Eliminates disconnected simulation data
 */

class CentralDataCoordinator {
  constructor() {
    this.subscribers = new Set();
    this.currentData = {
      mt5_connection: {
        status: 'disconnected',
        account: null,
        positions: [],
        balance: 0,
        equity: 0,
        profit: 0
      },
      trading_session: {
        trades_this_hour: 0,
        hourly_profit: 0,
        total_trades: 0,
        win_rate: 0,
        avg_profit_per_trade: 0
      },
      performance_metrics: {
        daily_pnl: 0,
        weekly_pnl: 0,
        monthly_pnl: 0,
        total_pnl: 0,
        max_drawdown: 0,
        sharpe_ratio: 0
      },
      market_allocation: {
        forex: 0,
        stocks: 0,
        crypto: 0
      },
      last_update: new Date().toISOString()
    };

    this.updateInterval = null;
    this.startDataSync();
  }

  // Subscribe component to data updates
  subscribe(callback) {
    this.subscribers.add(callback);
    // Immediately send current data to new subscriber
    callback(this.currentData);

    return () => {
      this.subscribers.delete(callback);
    };
  }

  // Notify all subscribers of data changes
  notifySubscribers() {
    this.subscribers.forEach(callback => {
      try {
        callback(this.currentData);
      } catch (error) {
        console.error('Error notifying subscriber:', error);
      }
    });
  }

  // Start continuous data synchronization
  startDataSync() {
    // Only start in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    console.log('🔄 Starting Central Data Coordinator');

    // Update every 2 seconds
    this.updateInterval = setInterval(() => {
      this.fetchAndSyncData();
    }, 2000);

    // Initial fetch
    this.fetchAndSyncData();
  }

  // Fetch data from MT5 and sync all components
  async fetchAndSyncData() {
    // Only run in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    try {
      // Fetch MT5 status
      const mt5Response = await fetch('/api/mt5/status');
      const mt5Data = await mt5Response.json();

      console.log('🔄 Central Data Coordinator - MT5 API Response:', mt5Data);

      // Update MT5 connection data
      const connectionStatus = mt5Data.connection?.status || 'disconnected';

      // Consider "simulated" as connected if we have real account data
      // This handles the case where MT5 API returns simulation mode but with real account info
      const hasAccountData = mt5Data.account && mt5Data.account.balance > 0;
      const isConnected = connectionStatus === 'connected' ||
                         connectionStatus === 'CONNECTED' ||
                         (connectionStatus === 'simulated' && hasAccountData);

      this.currentData.mt5_connection = {
        status: isConnected ? 'connected' : connectionStatus,
        account: mt5Data.account || null,
        positions: mt5Data.positions || [],
        balance: mt5Data.account?.balance || 0,
        equity: mt5Data.account?.equity || 0,
        profit: mt5Data.account?.profit || 0,
        mode: mt5Data.connection?.mode || 'UNKNOWN',
        isConnected: isConnected
      };

      console.log('🔄 Central Data Coordinator - Connection Status:', {
        originalStatus: connectionStatus,
        hasAccountData,
        isConnected,
        balance: mt5Data.account?.balance,
        equity: mt5Data.account?.equity,
        profit: mt5Data.account?.profit
      });

      // Calculate trading session metrics from MT5 data
      this.calculateTradingMetrics(mt5Data);

      // Calculate performance metrics
      this.calculatePerformanceMetrics(mt5Data);

      // Calculate market allocation from positions
      this.calculateMarketAllocation(mt5Data.positions || []);

      // Update timestamp
      this.currentData.last_update = new Date().toISOString();

      // Notify all subscribers
      this.notifySubscribers();

    } catch (error) {
      console.error('Error fetching MT5 data:', error);

      // If MT5 is unavailable, mark as disconnected
      this.currentData.mt5_connection.status = 'disconnected';
      this.notifySubscribers();
    }
  }

  // Calculate trading session metrics from MT5 data
  calculateTradingMetrics(mt5Data) {
    const positions = mt5Data.positions || [];
    const tradeHistory = mt5Data.trade_history || [];

    // Get trades from current hour
    const currentHour = new Date().getHours();
    const tradesThisHour = tradeHistory.filter(trade => {
      const tradeHour = new Date(trade.open_time || trade.timestamp).getHours();
      return tradeHour === currentHour;
    });

    // Calculate metrics
    const hourlyProfit = tradesThisHour.reduce((sum, trade) => sum + (trade.profit || 0), 0);
    const totalTrades = tradeHistory.length + positions.length;
    const profitableTrades = tradeHistory.filter(trade => (trade.profit || 0) > 0).length;
    const winRate = totalTrades > 0 ? profitableTrades / totalTrades : 0;
    const avgProfitPerTrade = tradesThisHour.length > 0 ? hourlyProfit / tradesThisHour.length : 0;

    this.currentData.trading_session = {
      trades_this_hour: tradesThisHour.length,
      hourly_profit: hourlyProfit,
      total_trades: totalTrades,
      win_rate: winRate,
      avg_profit_per_trade: avgProfitPerTrade
    };
  }

  // Calculate performance metrics
  calculatePerformanceMetrics(mt5Data) {
    const account = mt5Data.account || {};
    const startingBalance = 100000; // Demo account starting balance

    this.currentData.performance_metrics = {
      daily_pnl: account.profit || 0,
      weekly_pnl: account.profit || 0, // Would need historical data for accurate calculation
      monthly_pnl: account.profit || 0,
      total_pnl: (account.balance || startingBalance) - startingBalance,
      max_drawdown: 0, // Would need historical equity curve
      sharpe_ratio: 0 // Would need historical returns
    };
  }

  // Calculate market allocation from positions
  calculateMarketAllocation(positions) {
    let forexValue = 0;
    let stocksValue = 0;
    let cryptoValue = 0;

    positions.forEach(position => {
      const value = Math.abs(position.volume || 0) * (position.open_price || 0);
      const symbol = position.symbol || '';

      if (symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY')) {
        forexValue += value;
      } else if (symbol.includes('BTC') || symbol.includes('ETH') || symbol.includes('CRYPTO')) {
        cryptoValue += value;
      } else {
        stocksValue += value;
      }
    });

    const totalValue = forexValue + stocksValue + cryptoValue;

    if (totalValue > 0) {
      this.currentData.market_allocation = {
        forex: (forexValue / totalValue) * 100,
        stocks: (stocksValue / totalValue) * 100,
        crypto: (cryptoValue / totalValue) * 100
      };
    } else {
      // Default allocation when no positions
      this.currentData.market_allocation = {
        forex: 0,
        stocks: 0,
        crypto: 0
      };
    }
  }

  // Get current unified data
  getCurrentData() {
    return this.currentData;
  }

  // Stop data synchronization
  stop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.subscribers.clear();
    console.log('🛑 Central Data Coordinator stopped');
  }

  // Force immediate data refresh
  async refresh() {
    await this.fetchAndSyncData();
  }

  // Add trade manually (for when live bot executes trades)
  addTrade(tradeData) {
    console.log('📈 Adding trade to central coordinator:', tradeData);

    // Update trading session metrics
    this.currentData.trading_session.trades_this_hour += 1;
    this.currentData.trading_session.hourly_profit += tradeData.profit || 0;
    this.currentData.trading_session.total_trades += 1;

    // Recalculate averages
    if (this.currentData.trading_session.trades_this_hour > 0) {
      this.currentData.trading_session.avg_profit_per_trade =
        this.currentData.trading_session.hourly_profit / this.currentData.trading_session.trades_this_hour;
    }

    // Update timestamp and notify
    this.currentData.last_update = new Date().toISOString();
    this.notifySubscribers();
  }
}

// Create singleton instance
const centralDataCoordinator = new CentralDataCoordinator();

export default centralDataCoordinator;
