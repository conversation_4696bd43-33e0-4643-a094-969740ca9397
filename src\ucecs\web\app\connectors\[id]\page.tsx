'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import MainLayout from '@/components/MainLayout';
import { FiArrowLeft, FiServer, FiCloud, FiGithub, FiDatabase, FiSlack, FiCheckCircle, FiAlertCircle, FiPlay, FiPause, FiTrash2, FiSettings, FiEdit, FiRefreshCw } from 'react-icons/fi';
import { getConnectorById, testConnector, activateConnector, deactivateConnector } from '@/services/novaCoreApi';

// Mock connector types
const connectorTypes = {
  aws: { name: 'AWS', icon: FiCloud, color: 'text-yellow-500' },
  azure: { name: 'Azure', icon: FiCloud, color: 'text-blue-500' },
  gcp: { name: 'Google Cloud', icon: FiCloud, color: 'text-green-500' },
  github: { name: 'GitHub', icon: FiGith<PERSON>, color: 'text-gray-800 dark:text-white' },
  jira: { name: '<PERSON><PERSON>', icon: FiServer, color: 'text-blue-600' },
  slack: { name: 'Slack', icon: FiSlack, color: 'text-purple-500' },
  rest_api: { name: 'REST API', icon: FiServer, color: 'text-indigo-500' },
  database: { name: 'Database', icon: FiDatabase, color: 'text-orange-500' },
};

// Mock connector data
const mockConnector = {
  id: 'conn-001',
  name: 'AWS Security Hub',
  description: 'Collects security findings from AWS Security Hub',
  type: 'aws',
  status: 'active',
  config: {
    baseUrl: 'https://securityhub.us-east-1.amazonaws.com',
    apiVersion: '2018-10-26',
  },
  authentication: {
    type: 'aws_iam',
    credentials: {
      accessKey: 'AKIAXXXXXXXXXXXXXXXX',
      secretKey: '****************************************',
      region: 'us-east-1',
    },
  },
  schedule: {
    frequency: 'daily',
    time: '02:00',
    timezone: 'UTC',
    enabled: true,
    lastRun: '2023-10-15T02:00:00Z',
    nextRun: '2023-10-16T02:00:00Z',
  },
  lastCollection: '2023-10-15T02:00:00Z',
  evidenceCount: 128,
  createdAt: '2023-09-01T10:15:30Z',
  createdBy: 'admin',
  updatedAt: '2023-10-01T14:22:45Z',
  updatedBy: 'admin',
};

export default function ConnectorDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [connector, setConnector] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [testingConnection, setTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  // Load connector data
  useEffect(() => {
    const loadConnector = async () => {
      try {
        // In a real implementation, this would fetch the connector from the API
        // const result = await getConnectorById(params.id as string);
        // setConnector(result);
        
        // For now, use mock data
        setConnector(mockConnector);
        setLoading(false);
      } catch (error) {
        console.error('Error loading connector:', error);
        setLoading(false);
      }
    };
    
    loadConnector();
  }, [params.id]);

  // Handle test connection
  const handleTestConnection = async () => {
    setTestingConnection(true);
    setTestResult(null);
    
    try {
      // In a real implementation, this would test the connection via API
      // const result = await testConnector(connector.id);
      
      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate successful test
      setTestResult({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        responseTime: 245,
        details: {
          connectorType: connector.type,
          baseUrl: connector.config.baseUrl,
        },
      });
    } catch (error) {
      console.error('Error testing connection:', error);
      setTestResult({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        errorMessage: 'Failed to connect to the service',
      });
    } finally {
      setTestingConnection(false);
    }
  };

  // Handle activate/deactivate
  const handleToggleActive = async () => {
    try {
      if (connector.status === 'active') {
        // In a real implementation, this would deactivate the connector via API
        // await deactivateConnector(connector.id);
        
        // For now, update local state
        setConnector({
          ...connector,
          status: 'inactive',
        });
      } else {
        // In a real implementation, this would activate the connector via API
        // await activateConnector(connector.id);
        
        // For now, update local state
        setConnector({
          ...connector,
          status: 'active',
        });
      }
    } catch (error) {
      console.error('Error toggling connector status:', error);
    }
  };

  // Get connector type details
  const getConnectorTypeDetails = (type: string) => {
    return connectorTypes[type] || { name: 'Unknown', icon: FiServer, color: 'text-gray-500' };
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Loading connector details...</p>
        </div>
      </MainLayout>
    );
  }

  if (!connector) {
    return (
      <MainLayout>
        <div className="card p-8 text-center">
          <p className="text-gray-500 dark:text-gray-400">Connector not found</p>
          <Link href="/connectors" className="btn btn-primary mt-4">
            Back to Connectors
          </Link>
        </div>
      </MainLayout>
    );
  }

  const typeDetails = getConnectorTypeDetails(connector.type);
  const TypeIcon = typeDetails.icon;

  return (
    <MainLayout>
      <div className="mb-6 flex items-center">
        <Link href="/connectors" className="mr-4">
          <FiArrowLeft className="h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" />
        </Link>
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{connector.name}</h1>
          <p className="text-gray-500 dark:text-gray-400">{connector.description || 'No description'}</p>
        </div>
        <div className="flex space-x-2">
          <button
            className="btn btn-outline flex items-center"
            onClick={handleToggleActive}
          >
            {connector.status === 'active' ? (
              <>
                <FiPause className="mr-2" />
                Deactivate
              </>
            ) : (
              <>
                <FiPlay className="mr-2" />
                Activate
              </>
            )}
          </button>
          <Link href={`/connectors/${connector.id}/edit`} className="btn btn-outline flex items-center">
            <FiEdit className="mr-2" />
            Edit
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Connector Info */}
        <div className="md:col-span-2">
          <div className="card mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Connector Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Type</h3>
                <div className="flex items-center mt-1">
                  <TypeIcon className={`h-5 w-5 ${typeDetails.color} mr-2`} />
                  <p className="text-gray-900 dark:text-white">{typeDetails.name}</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h3>
                <div className="flex items-center mt-1">
                  {connector.status === 'active' ? (
                    <FiCheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  ) : (
                    <FiAlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
                  )}
                  <p className="text-gray-900 dark:text-white capitalize">{connector.status}</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Created</h3>
                <p className="text-gray-900 dark:text-white mt-1">{formatDate(connector.createdAt)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</h3>
                <p className="text-gray-900 dark:text-white mt-1">{formatDate(connector.updatedAt)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Collection</h3>
                <p className="text-gray-900 dark:text-white mt-1">{formatDate(connector.lastCollection)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Evidence Count</h3>
                <p className="text-gray-900 dark:text-white mt-1">{connector.evidenceCount}</p>
              </div>
            </div>
          </div>

          <div className="card mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Connection Settings</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Base URL</h3>
                <p className="text-gray-900 dark:text-white mt-1 font-mono text-sm">{connector.config.baseUrl}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">API Version</h3>
                <p className="text-gray-900 dark:text-white mt-1 font-mono text-sm">{connector.config.apiVersion}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Authentication Type</h3>
                <p className="text-gray-900 dark:text-white mt-1 capitalize">{connector.authentication.type.replace('_', ' ')}</p>
              </div>
            </div>
            <div className="mt-4">
              <button
                className="btn btn-outline flex items-center"
                onClick={handleTestConnection}
                disabled={testingConnection}
              >
                {testingConnection ? (
                  <>
                    <FiRefreshCw className="mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <FiRefreshCw className="mr-2" />
                    Test Connection
                  </>
                )}
              </button>
              
              {testResult && (
                <div className={`mt-4 p-4 rounded-md ${
                  testResult.status === 'healthy'
                    ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-400'
                    : 'bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400'
                }`}>
                  <div className="flex">
                    <div className="flex-shrink-0">
                      {testResult.status === 'healthy' ? (
                        <FiCheckCircle className="h-5 w-5 text-green-400" />
                      ) : (
                        <FiAlertCircle className="h-5 w-5 text-red-400" />
                      )}
                    </div>
                    <div className="ml-3">
                      <h3 className={`text-sm font-medium ${
                        testResult.status === 'healthy'
                          ? 'text-green-800 dark:text-green-200'
                          : 'text-red-800 dark:text-red-200'
                      }`}>
                        {testResult.status === 'healthy' ? 'Connection Successful' : 'Connection Failed'}
                      </h3>
                      <div className={`mt-2 text-sm ${
                        testResult.status === 'healthy'
                          ? 'text-green-700 dark:text-green-300'
                          : 'text-red-700 dark:text-red-300'
                      }`}>
                        {testResult.status === 'healthy' ? (
                          <p>Response time: {testResult.responseTime}ms</p>
                        ) : (
                          <p>{testResult.errorMessage}</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Schedule */}
        <div className="md:col-span-1">
          <div className="card mb-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Collection Schedule</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Frequency</h3>
                <p className="text-gray-900 dark:text-white mt-1 capitalize">{connector.schedule.frequency}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Time</h3>
                <p className="text-gray-900 dark:text-white mt-1">{connector.schedule.time} {connector.schedule.timezone}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Status</h3>
                <p className="text-gray-900 dark:text-white mt-1">{connector.schedule.enabled ? 'Enabled' : 'Disabled'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Run</h3>
                <p className="text-gray-900 dark:text-white mt-1">{formatDate(connector.schedule.lastRun)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Next Run</h3>
                <p className="text-gray-900 dark:text-white mt-1">{formatDate(connector.schedule.nextRun)}</p>
              </div>
            </div>
            <div className="mt-4">
              <Link href={`/connectors/${connector.id}/schedule`} className="btn btn-outline w-full">
                Edit Schedule
              </Link>
            </div>
          </div>

          <div className="card">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Actions</h2>
            <div className="space-y-2">
              <Link href={`/connectors/${connector.id}/collect`} className="btn btn-primary w-full flex items-center justify-center">
                <FiPlay className="mr-2" />
                Collect Now
              </Link>
              <Link href={`/evidence?connectorId=${connector.id}`} className="btn btn-outline w-full flex items-center justify-center">
                <FiEye className="mr-2" />
                View Evidence
              </Link>
              <button
                className="btn btn-outline btn-danger w-full flex items-center justify-center"
                onClick={() => {
                  if (confirm('Are you sure you want to delete this connector?')) {
                    router.push('/connectors');
                  }
                }}
              >
                <FiTrash2 className="mr-2" />
                Delete Connector
              </button>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
