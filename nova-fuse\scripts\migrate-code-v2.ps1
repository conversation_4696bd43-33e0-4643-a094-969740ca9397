# NovaFuse Code Migration Script (Version 2)
# This script automates the process of migrating code from the existing repositories to the new structure.
# It uses a configuration file for easier customization.

# Load configuration
$configPath = Join-Path -Path $PSScriptRoot -ChildPath "migration-config.json"
if (-not (Test-Path $configPath)) {
    Write-Host "Configuration file not found: $configPath" -ForegroundColor Red
    exit 1
}

$config = Get-Content -Path $configPath -Raw | ConvertFrom-Json
$sourceRoot = $config.sourceRoot
$destinationRoot = $config.destinationRoot
$logFile = $config.logFile

# Create log file if it doesn't exist
if (-not (Test-Path $logFile)) {
    New-Item -Path $logFile -ItemType File -Force | Out-Null
}

# Function to log messages
function Log-Message {
    param (
        [string]$message,
        [string]$type = "INFO"
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$type] $message"

    # Write to console
    if ($type -eq "ERROR") {
        Write-Host $logMessage -ForegroundColor Red
    }
    elseif ($type -eq "WARNING") {
        Write-Host $logMessage -ForegroundColor Yellow
    }
    elseif ($type -eq "SUCCESS") {
        Write-Host $logMessage -ForegroundColor Green
    }
    else {
        Write-Host $logMessage
    }

    # Write to log file
    Add-Content -Path $logFile -Value $logMessage
}

# Function to create directory if it doesn't exist
function Ensure-Directory {
    param (
        [string]$path
    )

    if (-not (Test-Path $path)) {
        New-Item -Path $path -ItemType Directory -Force | Out-Null
        Log-Message "Created directory: $path"
    }
}

# Function to copy files with directory structure
function Copy-Files {
    param (
        [string]$sourcePath,
        [string]$destinationPath,
        [string]$filter = "*.*"
    )

    # Ensure destination directory exists
    Ensure-Directory $destinationPath

    # Check if source path exists
    if (-not (Test-Path $sourcePath)) {
        Log-Message "Source path does not exist: $sourcePath" "ERROR"
        return $false
    }

    # Get all files in the source directory and subdirectories
    $files = Get-ChildItem -Path $sourcePath -Filter $filter -Recurse -File

    if ($files.Count -eq 0) {
        Log-Message "No files found in source path: $sourcePath" "WARNING"
        return $false
    }

    # Copy each file to the destination directory
    foreach ($file in $files) {
        # Get the relative path of the file
        $relativePath = $file.FullName.Substring($sourcePath.Length)

        # Create the destination path
        $destinationFilePath = Join-Path -Path $destinationPath -ChildPath $relativePath

        # Ensure the destination directory exists
        $destinationFileDir = Split-Path -Path $destinationFilePath -Parent
        Ensure-Directory $destinationFileDir

        # Copy the file
        Copy-Item -Path $file.FullName -Destination $destinationFilePath -Force
        Log-Message "Copied file: $($file.FullName) -> $destinationFilePath"
    }

    Log-Message "Copied all files from $sourcePath to $destinationPath" "SUCCESS"
    return $true
}

# Function to update import paths in JavaScript/TypeScript files
function Update-ImportPaths {
    param (
        [string]$directory,
        [PSCustomObject]$replacements
    )

    # Get all JavaScript/TypeScript files in the directory and subdirectories
    $files = Get-ChildItem -Path $directory -Include "*.js", "*.jsx", "*.ts", "*.tsx" -Recurse -File

    if ($files.Count -eq 0) {
        Log-Message "No JavaScript/TypeScript files found in directory: $directory" "WARNING"
        return
    }

    # Convert replacements to hashtable
    $replacementsHash = @{}
    $replacements.PSObject.Properties | ForEach-Object {
        $replacementsHash[$_.Name] = $_.Value
    }

    # Update import paths in each file
    foreach ($file in $files) {
        $content = Get-Content -Path $file.FullName -Raw
        $updated = $false

        # Apply each replacement
        foreach ($key in $replacementsHash.Keys) {
            $oldPath = $key
            $newPath = $replacementsHash[$key]

            # Check if the file contains the old path
            if ($content -match $oldPath) {
                $content = $content -replace $oldPath, $newPath
                $updated = $true
                Log-Message "Updated import path in $($file.FullName): $oldPath -> $newPath"
            }
        }

        # Save the file if it was updated
        if ($updated) {
            Set-Content -Path $file.FullName -Value $content
            Log-Message "Saved updated file: $($file.FullName)" "SUCCESS"
        }
    }
}

# Main migration process
Log-Message "Starting NovaFuse code migration..." "INFO"
Log-Message "Source root: $sourceRoot" "INFO"
Log-Message "Destination root: $destinationRoot" "INFO"

# Process each migration mapping
$successCount = 0
$failureCount = 0

foreach ($mapping in $config.migrationMappings) {
    $sourcePath = Join-Path -Path $sourceRoot -ChildPath $mapping.sourcePath
    $destinationPath = Join-Path -Path $destinationRoot -ChildPath $mapping.destinationPath

    Log-Message "Processing migration: $($mapping.name)" "INFO"
    Log-Message "Source path: $sourcePath" "INFO"
    Log-Message "Destination path: $destinationPath" "INFO"

    # Copy files
    $copySuccess = Copy-Files -sourcePath $sourcePath -destinationPath $destinationPath -filter $mapping.filter

    if ($copySuccess) {
        # Update import paths
        if ($mapping.importPathReplacements) {
            Update-ImportPaths -directory $destinationPath -replacements $mapping.importPathReplacements
        }

        $successCount++
    }
    else {
        $failureCount++
    }
}

# Summary
Log-Message "Migration summary:" "INFO"
Log-Message "Successful migrations: $successCount" "SUCCESS"
if ($failureCount -gt 0) {
    Log-Message "Failed migrations: $failureCount" "ERROR"
} else {
    Log-Message "Failed migrations: $failureCount" "INFO"
}
Log-Message "NovaFuse code migration completed!" "SUCCESS"
