import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Typography, 
  CircularProgress, 
  Button, 
  Tabs, 
  Tab, 
  IconButton, 
  Menu, 
  MenuItem,
  Snackbar,
  Alert
} from '@mui/material';
import { 
  Refresh as RefreshIcon, 
  MoreVert as MoreVertIcon, 
  Fullscreen as FullscreenIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

// Import visualization components
import TriDomainTensorVisualization from '../components/visualizations/TriDomainTensorVisualization';
import CyberSafetyHarmonyIndex from '../components/visualizations/CyberSafetyHarmonyIndex';
import RiskControlFusionMap from '../components/visualizations/RiskControlFusionMap';
import CyberSafetyResonanceSpectrogram from '../components/visualizations/CyberSafetyResonanceSpectrogram';
import UnifiedComplianceSecurityVisualizer from '../components/visualizations/UnifiedComplianceSecurityVisualizer';

// Import data service
import { fetchAllCyberSafetyData, exportVisualizationData } from '../services/cyberSafetyDataService';

// Import feedback dialog
import VisualizationFeedbackDialog from '../components/visualizations/VisualizationFeedbackDialog';

/**
 * CyberSafetyDashboard
 * 
 * This component displays a dashboard with all Cyber-Safety fusion visualizations.
 */
function CyberSafetyDashboard() {
  // State for visualization data
  const [visualizationData, setVisualizationData] = useState({
    triDomainTensor: null,
    harmonyIndex: null,
    riskControlFusion: null,
    resonanceSpectrogram: null,
    unifiedComplianceSecurity: null
  });
  
  // State for loading status
  const [loading, setLoading] = useState({
    triDomainTensor: false,
    harmonyIndex: false,
    riskControlFusion: false,
    resonanceSpectrogram: false,
    unifiedComplianceSecurity: false,
    all: false
  });
  
  // State for errors
  const [errors, setErrors] = useState({
    triDomainTensor: null,
    harmonyIndex: null,
    riskControlFusion: null,
    resonanceSpectrogram: null,
    unifiedComplianceSecurity: null,
    all: null
  });
  
  // State for active tab
  const [activeTab, setActiveTab] = useState(0);
  
  // State for menu
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [activeMenu, setActiveMenu] = useState(null);
  
  // State for fullscreen
  const [fullscreen, setFullscreen] = useState(null);
  
  // State for feedback dialog
  const [feedbackDialog, setFeedbackDialog] = useState({
    open: false,
    visualizationType: null
  });
  
  // State for snackbar
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Load all visualization data on component mount
  useEffect(() => {
    loadAllData();
  }, []);
  
  // Load all visualization data
  const loadAllData = async () => {
    try {
      setLoading(prev => ({ ...prev, all: true }));
      setErrors(prev => ({ ...prev, all: null }));
      
      const data = await fetchAllCyberSafetyData();
      setVisualizationData(data);
      
      setLoading(prev => ({ ...prev, all: false }));
    } catch (error) {
      console.error('Error loading all visualization data:', error);
      setErrors(prev => ({ ...prev, all: error.message }));
      setLoading(prev => ({ ...prev, all: false }));
    }
  };
  
  // Load data for a specific visualization
  const loadData = async (visualizationType) => {
    try {
      setLoading(prev => ({ ...prev, [visualizationType]: true }));
      setErrors(prev => ({ ...prev, [visualizationType]: null }));
      
      const data = await fetchAllCyberSafetyData({ visualizationType });
      setVisualizationData(prev => ({ ...prev, [visualizationType]: data[visualizationType] }));
      
      setLoading(prev => ({ ...prev, [visualizationType]: false }));
    } catch (error) {
      console.error(`Error loading ${visualizationType} data:`, error);
      setErrors(prev => ({ ...prev, [visualizationType]: error.message }));
      setLoading(prev => ({ ...prev, [visualizationType]: false }));
    }
  };
  
  // Export data for a specific visualization
  const exportData = async (visualizationType, format = 'json') => {
    try {
      const blob = await exportVisualizationData(visualizationType, format);
      
      // Create a download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${visualizationType}-${new Date().toISOString()}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      // Show success message
      setSnackbar({
        open: true,
        message: `Data exported successfully as ${format.toUpperCase()}`,
        severity: 'success'
      });
    } catch (error) {
      console.error(`Error exporting ${visualizationType} data:`, error);
      setSnackbar({
        open: true,
        message: `Error exporting data: ${error.message}`,
        severity: 'error'
      });
    }
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };
  
  // Handle menu open
  const handleMenuOpen = (event, visualizationType) => {
    setMenuAnchorEl(event.currentTarget);
    setActiveMenu(visualizationType);
  };
  
  // Handle menu close
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
    setActiveMenu(null);
  };
  
  // Handle fullscreen toggle
  const handleFullscreen = (visualizationType) => {
    setFullscreen(visualizationType);
    handleMenuClose();
  };
  
  // Handle exit fullscreen
  const handleExitFullscreen = () => {
    setFullscreen(null);
  };
  
  // Handle feedback dialog open
  const handleFeedbackOpen = (visualizationType) => {
    setFeedbackDialog({
      open: true,
      visualizationType
    });
    handleMenuClose();
  };
  
  // Handle feedback dialog close
  const handleFeedbackClose = () => {
    setFeedbackDialog({
      open: false,
      visualizationType: null
    });
  };
  
  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };
  
  // Render visualization card
  const renderVisualizationCard = (title, visualizationType, component) => {
    const isLoading = loading[visualizationType] || loading.all;
    const error = errors[visualizationType] || errors.all;
    
    return (
      <Paper 
        sx={{ 
          p: 2, 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">{title}</Typography>
          <Box>
            <IconButton 
              size="small" 
              onClick={() => loadData(visualizationType)}
              disabled={isLoading}
            >
              <RefreshIcon />
            </IconButton>
            <IconButton 
              size="small" 
              onClick={(e) => handleMenuOpen(e, visualizationType)}
            >
              <MoreVertIcon />
            </IconButton>
          </Box>
        </Box>
        
        <Box sx={{ flexGrow: 1, minHeight: 300, position: 'relative' }}>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
              <Typography color="error">{error}</Typography>
            </Box>
          ) : (
            component
          )}
        </Box>
      </Paper>
    );
  };
  
  // If a visualization is in fullscreen mode, only show that one
  if (fullscreen) {
    let fullscreenComponent = null;
    
    switch (fullscreen) {
      case 'triDomainTensor':
        fullscreenComponent = (
          <TriDomainTensorVisualization
            domainData={visualizationData.triDomainTensor}
            width="100%"
            height="calc(100vh - 100px)"
          />
        );
        break;
      case 'harmonyIndex':
        fullscreenComponent = (
          <CyberSafetyHarmonyIndex
            domainData={visualizationData.harmonyIndex?.domainData}
            harmonyHistory={visualizationData.harmonyIndex?.harmonyHistory}
            width="100%"
            height="calc(100vh - 100px)"
          />
        );
        break;
      case 'riskControlFusion':
        fullscreenComponent = (
          <RiskControlFusionMap
            riskData={visualizationData.riskControlFusion?.riskData}
            controlData={visualizationData.riskControlFusion?.controlData}
            width="100%"
            height="calc(100vh - 100px)"
          />
        );
        break;
      case 'resonanceSpectrogram':
        fullscreenComponent = (
          <CyberSafetyResonanceSpectrogram
            domainData={visualizationData.resonanceSpectrogram?.domainData}
            predictionData={visualizationData.resonanceSpectrogram?.predictionData}
            width="100%"
            height="calc(100vh - 100px)"
          />
        );
        break;
      case 'unifiedComplianceSecurity':
        fullscreenComponent = (
          <UnifiedComplianceSecurityVisualizer
            complianceData={visualizationData.unifiedComplianceSecurity?.complianceData}
            impactAnalysis={visualizationData.unifiedComplianceSecurity?.impactAnalysis}
            width="100%"
            height="calc(100vh - 100px)"
          />
        );
        break;
      default:
        break;
    }
    
    return (
      <Box sx={{ p: 2, height: '100vh' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h5">
            {fullscreen === 'triDomainTensor' ? 'Tri-Domain Tensor Visualization' :
             fullscreen === 'harmonyIndex' ? 'Cyber-Safety Harmony Index' :
             fullscreen === 'riskControlFusion' ? 'Risk-Control Fusion Map' :
             fullscreen === 'resonanceSpectrogram' ? 'Cyber-Safety Resonance Spectrogram' :
             'Unified Compliance-Security Visualizer'}
          </Typography>
          <Button 
            variant="outlined" 
            startIcon={<FullscreenIcon />}
            onClick={handleExitFullscreen}
          >
            Exit Fullscreen
          </Button>
        </Box>
        
        {fullscreenComponent}
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: 2 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h4">Cyber-Safety Fusion Dashboard</Typography>
        <Button 
          variant="contained" 
          startIcon={<RefreshIcon />}
          onClick={loadAllData}
          disabled={loading.all}
        >
          {loading.all ? 'Refreshing...' : 'Refresh All'}
        </Button>
      </Box>
      
      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="All Visualizations" />
        <Tab label="Tri-Domain Tensor" />
        <Tab label="Harmony Index" />
        <Tab label="Risk-Control Fusion" />
        <Tab label="Resonance Spectrogram" />
        <Tab label="Compliance-Security" />
      </Tabs>
      
      {activeTab === 0 && (
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            {renderVisualizationCard(
              'Tri-Domain Tensor Visualization',
              'triDomainTensor',
              <TriDomainTensorVisualization
                domainData={visualizationData.triDomainTensor}
                width="100%"
                height={300}
              />
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderVisualizationCard(
              'Cyber-Safety Harmony Index',
              'harmonyIndex',
              <CyberSafetyHarmonyIndex
                domainData={visualizationData.harmonyIndex?.domainData}
                harmonyHistory={visualizationData.harmonyIndex?.harmonyHistory}
                width="100%"
                height={300}
              />
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderVisualizationCard(
              'Risk-Control Fusion Map',
              'riskControlFusion',
              <RiskControlFusionMap
                riskData={visualizationData.riskControlFusion?.riskData}
                controlData={visualizationData.riskControlFusion?.controlData}
                width="100%"
                height={300}
              />
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            {renderVisualizationCard(
              'Cyber-Safety Resonance Spectrogram',
              'resonanceSpectrogram',
              <CyberSafetyResonanceSpectrogram
                domainData={visualizationData.resonanceSpectrogram?.domainData}
                predictionData={visualizationData.resonanceSpectrogram?.predictionData}
                width="100%"
                height={300}
              />
            )}
          </Grid>
          <Grid item xs={12}>
            {renderVisualizationCard(
              'Unified Compliance-Security Visualizer',
              'unifiedComplianceSecurity',
              <UnifiedComplianceSecurityVisualizer
                complianceData={visualizationData.unifiedComplianceSecurity?.complianceData}
                impactAnalysis={visualizationData.unifiedComplianceSecurity?.impactAnalysis}
                width="100%"
                height={300}
              />
            )}
          </Grid>
        </Grid>
      )}
      
      {/* Individual tabs for each visualization */}
      {activeTab === 1 && (
        <Box sx={{ height: 'calc(100vh - 200px)' }}>
          <TriDomainTensorVisualization
            domainData={visualizationData.triDomainTensor}
            width="100%"
            height="100%"
          />
        </Box>
      )}
      
      {activeTab === 2 && (
        <Box sx={{ height: 'calc(100vh - 200px)' }}>
          <CyberSafetyHarmonyIndex
            domainData={visualizationData.harmonyIndex?.domainData}
            harmonyHistory={visualizationData.harmonyIndex?.harmonyHistory}
            width="100%"
            height="100%"
          />
        </Box>
      )}
      
      {activeTab === 3 && (
        <Box sx={{ height: 'calc(100vh - 200px)' }}>
          <RiskControlFusionMap
            riskData={visualizationData.riskControlFusion?.riskData}
            controlData={visualizationData.riskControlFusion?.controlData}
            width="100%"
            height="100%"
          />
        </Box>
      )}
      
      {activeTab === 4 && (
        <Box sx={{ height: 'calc(100vh - 200px)' }}>
          <CyberSafetyResonanceSpectrogram
            domainData={visualizationData.resonanceSpectrogram?.domainData}
            predictionData={visualizationData.resonanceSpectrogram?.predictionData}
            width="100%"
            height="100%"
          />
        </Box>
      )}
      
      {activeTab === 5 && (
        <Box sx={{ height: 'calc(100vh - 200px)' }}>
          <UnifiedComplianceSecurityVisualizer
            complianceData={visualizationData.unifiedComplianceSecurity?.complianceData}
            impactAnalysis={visualizationData.unifiedComplianceSecurity?.impactAnalysis}
            width="100%"
            height="100%"
          />
        </Box>
      )}
      
      {/* Menu for visualization actions */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleFullscreen(activeMenu)}>
          <FullscreenIcon sx={{ mr: 1 }} />
          Fullscreen
        </MenuItem>
        <MenuItem onClick={() => exportData(activeMenu, 'json')}>
          <DownloadIcon sx={{ mr: 1 }} />
          Export as JSON
        </MenuItem>
        <MenuItem onClick={() => exportData(activeMenu, 'csv')}>
          <DownloadIcon sx={{ mr: 1 }} />
          Export as CSV
        </MenuItem>
        <MenuItem onClick={() => handleFeedbackOpen(activeMenu)}>
          <SettingsIcon sx={{ mr: 1 }} />
          Settings
        </MenuItem>
      </Menu>
      
      {/* Feedback dialog */}
      <VisualizationFeedbackDialog
        open={feedbackDialog.open}
        visualizationType={feedbackDialog.visualizationType}
        onClose={handleFeedbackClose}
      />
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
}

export default CyberSafetyDashboard;
