/**
 * Audit Controller
 * 
 * This controller handles API requests related to audit logs.
 */

const AuditService = require('../services/AuditService');
const { ValidationError } = require('../utils/errors');

class AuditController {
  constructor() {
    this.auditService = new AuditService();
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(req, res, next) {
    try {
      const filters = req.query;
      
      const auditLogs = await this.auditService.getAuditLogs(filters);
      res.json(auditLogs);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'all',
        details: { filters, count: auditLogs.logs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get audit log by ID
   */
  async getAuditLogById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Audit log ID is required');
      }
      
      const auditLog = await this.auditService.getAuditLogById(id);
      res.json(auditLog);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: id,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get audit logs for a resource
   */
  async getAuditLogsForResource(req, res, next) {
    try {
      const { resourceType, resourceId } = req.params;
      
      if (!resourceType) {
        throw new ValidationError('Resource type is required');
      }
      
      if (!resourceId) {
        throw new ValidationError('Resource ID is required');
      }
      
      const auditLogs = await this.auditService.getAuditLogsForResource(resourceType, resourceId);
      res.json(auditLogs);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'resource',
        details: { resourceType, resourceId, count: auditLogs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get audit logs for a user
   */
  async getAuditLogsForUser(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      const auditLogs = await this.auditService.getAuditLogsForUser(id);
      res.json(auditLogs);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'user',
        details: { userId: id, count: auditLogs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get my audit logs
   */
  async getMyAuditLogs(req, res, next) {
    try {
      const auditLogs = await this.auditService.getAuditLogsForUser(req.user.id);
      res.json(auditLogs);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'my',
        details: { count: auditLogs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get audit logs for a team
   */
  async getAuditLogsForTeam(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('Team ID is required');
      }
      
      const auditLogs = await this.auditService.getAuditLogsForTeam(id);
      res.json(auditLogs);
      
      // Log audit event
      this.auditService.logEvent({
        userId: req.user.id,
        action: 'GET',
        resourceType: 'audit_log',
        resourceId: 'team',
        details: { teamId: id, count: auditLogs.length },
        ip: req.ip,
        userAgent: req.headers['user-agent'],
        teamId: id
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AuditController();
