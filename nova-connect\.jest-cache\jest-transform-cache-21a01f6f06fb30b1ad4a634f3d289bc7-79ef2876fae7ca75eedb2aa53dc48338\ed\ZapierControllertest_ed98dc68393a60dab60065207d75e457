784f38b0947d26ab1d3986769da686d8
// Mock the ZapierService
_getJestObj().mock('../../../api/services/ZapierService');
function _getJestObj() {
  const {
    jest
  } = require("@jest/globals");
  _getJestObj = () => jest;
  return jest;
}
/**
 * Zapier Controller Tests
 */

const ZapierController = require('../../../api/controllers/ZapierController');
const ZapierService = require('../../../api/services/ZapierService');
describe('ZapierController', () => {
  let req, res, next;
  beforeEach(() => {
    // Mock request, response, and next
    req = {
      params: {},
      query: {},
      body: {}
    };
    res = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis(),
      redirect: jest.fn(),
      end: jest.fn()
    };
    next = jest.fn();

    // Mock ZapierService methods
    ZapierService.mockImplementation(() => ({
      getAppDefinition: jest.fn().mockReturnValue({
        title: 'NovaConnect UAC',
        description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.'
      }),
      getTriggers: jest.fn().mockResolvedValue([{
        key: 'new_connector',
        noun: 'Connector',
        display: {
          label: 'New Connector',
          description: 'Triggers when a new connector is created.'
        }
      }]),
      getActions: jest.fn().mockResolvedValue([{
        key: 'create_connector',
        noun: 'Connector',
        display: {
          label: 'Create Connector',
          description: 'Creates a new connector.'
        }
      }]),
      clientId: 'nova-connect-zapier',
      clientSecret: 'test-secret',
      redirectUri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      generateAccessToken: jest.fn().mockResolvedValue({
        access_token: 'test-access-token',
        refresh_token: 'test-refresh-token',
        token_type: 'Bearer',
        expires_in: 2592000,
        scope: 'read write'
      }),
      refreshAccessToken: jest.fn().mockResolvedValue({
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        token_type: 'Bearer',
        expires_in: 2592000,
        scope: 'read write'
      }),
      registerApp: jest.fn().mockResolvedValue({
        id: 'app-123',
        name: 'Test App',
        description: 'Test Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z'
      }),
      getAllApps: jest.fn().mockResolvedValue([{
        id: 'app-123',
        name: 'Test App',
        description: 'Test Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z'
      }]),
      getAppById: jest.fn().mockResolvedValue({
        id: 'app-123',
        name: 'Test App',
        description: 'Test Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z'
      }),
      updateApp: jest.fn().mockResolvedValue({
        id: 'app-123',
        name: 'Updated App',
        description: 'Updated Description',
        webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      }),
      deleteApp: jest.fn().mockResolvedValue(true),
      registerTrigger: jest.fn().mockResolvedValue({
        id: 'trigger-123',
        key: 'test_trigger',
        noun: 'Test',
        display: {
          label: 'Test Trigger',
          description: 'Test trigger description'
        },
        createdAt: '2023-01-01T00:00:00Z'
      }),
      registerAction: jest.fn().mockResolvedValue({
        id: 'action-123',
        key: 'test_action',
        noun: 'Test',
        display: {
          label: 'Test Action',
          description: 'Test action description'
        },
        createdAt: '2023-01-01T00:00:00Z'
      })
    }));
  });
  afterEach(() => {
    jest.clearAllMocks();
  });
  test('getAppDefinition should return app definition', async () => {
    await ZapierController.getAppDefinition(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      title: 'NovaConnect UAC',
      description: 'Connect NovaConnect UAC with 5,000+ apps on Zapier.'
    });
  });
  test('getTriggers should return triggers', async () => {
    await ZapierController.getTriggers(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([{
      key: 'new_connector',
      noun: 'Connector',
      display: {
        label: 'New Connector',
        description: 'Triggers when a new connector is created.'
      }
    }]);
  });
  test('getActions should return actions', async () => {
    await ZapierController.getActions(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([{
      key: 'create_connector',
      noun: 'Connector',
      display: {
        label: 'Create Connector',
        description: 'Creates a new connector.'
      }
    }]);
  });
  test('authorizeOAuth should redirect with code', async () => {
    req.query = {
      client_id: 'nova-connect-zapier',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      state: 'test-state',
      response_type: 'code'
    };
    await ZapierController.authorizeOAuth(req, res, next);
    expect(res.redirect).toHaveBeenCalled();
    const redirectUrl = res.redirect.mock.calls[0][0];
    expect(redirectUrl).toContain('https://zapier.com/dashboard/auth/oauth/return/App-ID/');
    expect(redirectUrl).toContain('code=');
    expect(redirectUrl).toContain('state=test-state');
  });
  test('authorizeOAuth should validate client ID', async () => {
    req.query = {
      client_id: 'invalid-client',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      state: 'test-state',
      response_type: 'code'
    };
    await ZapierController.authorizeOAuth(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'invalid_client',
      error_description: 'Invalid client ID'
    });
  });
  test('authorizeOAuth should validate response type', async () => {
    req.query = {
      client_id: 'nova-connect-zapier',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      state: 'test-state',
      response_type: 'token'
    };
    await ZapierController.authorizeOAuth(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'unsupported_response_type',
      error_description: 'Only code response type is supported'
    });
  });
  test('getOAuthToken should generate access token', async () => {
    req.body = {
      grant_type: 'authorization_code',
      code: 'test-code',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      client_id: 'nova-connect-zapier',
      client_secret: 'test-secret'
    };
    await ZapierController.getOAuthToken(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      access_token: 'test-access-token',
      refresh_token: 'test-refresh-token',
      token_type: 'Bearer',
      expires_in: 2592000,
      scope: 'read write'
    });
  });
  test('getOAuthToken should refresh access token', async () => {
    req.body = {
      grant_type: 'refresh_token',
      refresh_token: 'test-refresh-token',
      client_id: 'nova-connect-zapier',
      client_secret: 'test-secret'
    };
    await ZapierController.getOAuthToken(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      access_token: 'new-access-token',
      refresh_token: 'new-refresh-token',
      token_type: 'Bearer',
      expires_in: 2592000,
      scope: 'read write'
    });
  });
  test('getOAuthToken should validate client credentials', async () => {
    req.body = {
      grant_type: 'authorization_code',
      code: 'test-code',
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App-ID/',
      client_id: 'invalid-client',
      client_secret: 'invalid-secret'
    };
    await ZapierController.getOAuthToken(req, res, next);
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({
      error: 'invalid_client',
      error_description: 'Invalid client credentials'
    });
  });
  test('getOAuthToken should validate grant type', async () => {
    req.body = {
      grant_type: 'invalid-grant',
      client_id: 'nova-connect-zapier',
      client_secret: 'test-secret'
    };
    await ZapierController.getOAuthToken(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'unsupported_grant_type',
      error_description: 'Unsupported grant type'
    });
  });
  test('beforeApp should execute successfully', async () => {
    await ZapierController.beforeApp(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      status: 'success',
      message: 'Before app hook executed successfully'
    });
  });
  test('afterApp should execute successfully', async () => {
    await ZapierController.afterApp(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      status: 'success',
      message: 'After app hook executed successfully'
    });
  });
  test('newConnectorTrigger should return connectors', async () => {
    await ZapierController.newConnectorTrigger(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.arrayContaining([expect.objectContaining({
      id: expect.any(String),
      name: expect.any(String),
      type: expect.any(String),
      createdAt: expect.any(String)
    })]));
  });
  test('newWorkflowTrigger should return workflows', async () => {
    await ZapierController.newWorkflowTrigger(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.arrayContaining([expect.objectContaining({
      id: expect.any(String),
      name: expect.any(String),
      status: expect.any(String),
      createdAt: expect.any(String)
    })]));
  });
  test('complianceEventTrigger should return events', async () => {
    await ZapierController.complianceEventTrigger(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.arrayContaining([expect.objectContaining({
      id: expect.any(String),
      type: expect.any(String),
      severity: expect.any(String),
      resource: expect.any(String),
      details: expect.any(String),
      timestamp: expect.any(String)
    })]));
  });
  test('createConnectorAction should create connector', async () => {
    req.body = {
      name: 'Test Connector',
      type: 'api',
      config: '{"baseUrl": "https://api.example.com"}'
    };
    await ZapierController.createConnectorAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      name: 'Test Connector',
      type: 'api',
      config: {
        baseUrl: 'https://api.example.com'
      },
      createdAt: expect.any(String)
    }));
  });
  test('createConnectorAction should validate required fields', async () => {
    req.body = {
      name: 'Test Connector'
      // Missing type and config
    };
    await ZapierController.createConnectorAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Name, type, and config are required'
    });
  });
  test('executeWorkflowAction should execute workflow', async () => {
    req.body = {
      workflowId: 'wf-123',
      inputs: '{"param1": "value1"}'
    };
    await ZapierController.executeWorkflowAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      workflowId: 'wf-123',
      status: 'completed',
      result: expect.objectContaining({
        success: true,
        data: {
          param1: 'value1'
        }
      }),
      startedAt: expect.any(String),
      completedAt: expect.any(String)
    }));
  });
  test('executeWorkflowAction should validate required fields', async () => {
    req.body = {
      // Missing workflowId
      inputs: '{"param1": "value1"}'
    };
    await ZapierController.executeWorkflowAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Workflow ID is required'
    });
  });
  test('createComplianceEvidenceAction should create evidence', async () => {
    req.body = {
      controlId: 'ctrl-123',
      evidenceType: 'document',
      description: 'Test evidence',
      data: '{"source": "zapier"}'
    };
    await ZapierController.createComplianceEvidenceAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith(expect.objectContaining({
      id: expect.any(String),
      controlId: 'ctrl-123',
      evidenceType: 'document',
      description: 'Test evidence',
      data: {
        source: 'zapier'
      },
      createdAt: expect.any(String)
    }));
  });
  test('createComplianceEvidenceAction should validate required fields', async () => {
    req.body = {
      controlId: 'ctrl-123',
      // Missing evidenceType and description
      data: '{"source": "zapier"}'
    };
    await ZapierController.createComplianceEvidenceAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Control ID, evidence type, and description are required'
    });
  });
  test('registerApp should register app', async () => {
    req.body = {
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/'
    };
    await ZapierController.registerApp(req, res, next);
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'app-123',
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  test('registerApp should validate required fields', async () => {
    req.body = {
      name: 'Test App'
      // Missing webhookUrl
    };
    await ZapierController.registerApp(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Name and webhook URL are required'
    });
  });
  test('getAllApps should return apps', async () => {
    await ZapierController.getAllApps(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([{
      id: 'app-123',
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z'
    }]);
  });
  test('getAppById should return app', async () => {
    req.params = {
      id: 'app-123'
    };
    await ZapierController.getAppById(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      id: 'app-123',
      name: 'Test App',
      description: 'Test Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  test('updateApp should update app', async () => {
    req.params = {
      id: 'app-123'
    };
    req.body = {
      name: 'Updated App',
      description: 'Updated Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/'
    };
    await ZapierController.updateApp(req, res, next);
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith({
      id: 'app-123',
      name: 'Updated App',
      description: 'Updated Description',
      webhookUrl: 'https://hooks.zapier.com/hooks/catch/123/456/',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-02T00:00:00Z'
    });
  });
  test('deleteApp should delete app', async () => {
    req.params = {
      id: 'app-123'
    };
    await ZapierController.deleteApp(req, res, next);
    expect(res.status).toHaveBeenCalledWith(204);
    expect(res.end).toHaveBeenCalled();
  });
  test('registerTrigger should register trigger', async () => {
    req.body = {
      key: 'test_trigger',
      noun: 'Test',
      display: {
        label: 'Test Trigger',
        description: 'Test trigger description'
      },
      operation: {
        type: 'polling',
        perform: {
          url: 'https://api.example.com/triggers/test'
        }
      }
    };
    await ZapierController.registerTrigger(req, res, next);
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'trigger-123',
      key: 'test_trigger',
      noun: 'Test',
      display: {
        label: 'Test Trigger',
        description: 'Test trigger description'
      },
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  test('registerTrigger should validate required fields', async () => {
    req.body = {
      key: 'test_trigger',
      noun: 'Test'
      // Missing display and operation
    };
    await ZapierController.registerTrigger(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Key, noun, display, and operation are required'
    });
  });
  test('registerAction should register action', async () => {
    req.body = {
      key: 'test_action',
      noun: 'Test',
      display: {
        label: 'Test Action',
        description: 'Test action description'
      },
      operation: {
        type: 'perform',
        perform: {
          url: 'https://api.example.com/actions/test',
          method: 'POST'
        }
      }
    };
    await ZapierController.registerAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(201);
    expect(res.json).toHaveBeenCalledWith({
      id: 'action-123',
      key: 'test_action',
      noun: 'Test',
      display: {
        label: 'Test Action',
        description: 'Test action description'
      },
      createdAt: '2023-01-01T00:00:00Z'
    });
  });
  test('registerAction should validate required fields', async () => {
    req.body = {
      key: 'test_action',
      noun: 'Test'
      // Missing display and operation
    };
    await ZapierController.registerAction(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Bad Request',
      message: 'Key, noun, display, and operation are required'
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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