import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';

const OnboardingEntry = () => {
  const router = useRouter();
  const { type } = router.query;

  useEffect(() => {
    // Redirect to the appropriate onboarding page based on the type query parameter
    if (type === 'partner') {
      router.push('/onboard/partner');
    } else if (type === 'developer') {
      router.push('/onboard/developer');
    }
  }, [type, router]);

  return (
    <>
      <Head>
        <title>NovaFuse Onboarding</title>
        <meta name="description" content="Start your NovaFuse onboarding process" />
      </Head>

      <div className="min-h-screen bg-gray-50 flex flex-col">
        <header className="bg-blue-900 text-white py-4">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center">
              <Link href="/" className="text-2xl font-bold">
                NovaFuse
              </Link>
              <Link href="/partner-signup" className="text-sm hover:underline">
                Back to Signup
              </Link>
            </div>
          </div>
        </header>

        <main className="flex-grow container mx-auto px-4 py-12 flex items-center justify-center">
          <div className="max-w-2xl w-full">
            <h1 className="text-3xl font-bold text-center mb-8">Choose Your Path</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-blue-800 text-white p-4">
                  <h2 className="text-xl font-bold">Founding Partner</h2>
                  <p className="text-sm mt-1">Implementation & Consulting</p>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">
                    Join as a consulting or implementation partner with 82% revenue share for 5 years.
                  </p>
                  <ul className="text-sm text-gray-700 mb-6 space-y-2">
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>82% Revenue Share</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>Partner Success Program</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>Co-Marketing Opportunities</span>
                    </li>
                  </ul>
                  <Link
                    href="/onboard/partner"
                    className="block w-full text-center bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Continue as Partner
                  </Link>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="bg-purple-800 text-white p-4">
                  <h2 className="text-xl font-bold">Daring Developer</h2>
                  <p className="text-sm mt-1">App Store Applications</p>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">
                    Build apps for the NovaFuse Compliance App Store with 82% revenue share for 5 years.
                  </p>
                  <ul className="text-sm text-gray-700 mb-6 space-y-2">
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>82% App Store Revenue</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>Equity Opportunities</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-green-500 mr-2">✓</span>
                      <span>Category Exclusivity</span>
                    </li>
                  </ul>
                  <Link
                    href="/onboard/developer"
                    className="block w-full text-center bg-purple-600 text-white py-2 rounded-md hover:bg-purple-700 transition-colors"
                  >
                    Continue as Developer
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </main>

        <footer className="bg-gray-100 py-6">
          <div className="container mx-auto px-4 text-center text-sm text-gray-600">
            <p>© {new Date().getFullYear()} NovaGRC, Inc. d/b/a NovaFuse. All rights reserved.</p>
            <p className="mt-2">
              <Link href="/privacy-policy" className="text-blue-600 hover:underline mx-2">
                Privacy Policy
              </Link>
              <Link href="/terms-of-service" className="text-blue-600 hover:underline mx-2">
                Terms of Service
              </Link>
            </p>
          </div>
        </footer>
      </div>
    </>
  );
};

export default OnboardingEntry;
