import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Nova3DEngine, NovaQuantumMesh } from '@novafuse/nova-vision-3d';
import { useNovaCore } from '@novafuse/nova-core';
import { QuantumState } from '@novafuse/nova-quantum';
import './EntropyVisualization.css';

const QuantumEntropyViz3D = ({
  entropy = 0.5,
  coherence = 0.7,
  entanglement = 0.3,
  quantumState = null,
  onLoad = () => {},
  onError = () => {},
  className = '',
}) => {
  const canvasRef = useRef(null);
  const [engine, setEngine] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { novaTrack } = useNovaCore();
  const animationRef = useRef(null);
  const lastUpdateTime = useRef(0);
  const frameRate = 60; // Target FPS
  const frameTime = 1000 / frameRate;
  
  // Initialize 3D engine on mount
  useEffect(() => {
    if (!canvasRef.current) return;
    
    const initEngine = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const newEngine = new Nova3DEngine({
          canvas: canvasRef.current,
          antialias: true,
          alpha: true,
          preserveDrawingBuffer: true,
          powerPreference: 'high-performance',
        });
        
        // Configure engine with performance optimizations
        newEngine.setPixelRatio(window.devicePixelRatio);
        newEngine.setClearColor(0x0a0f1f, 1);
        
        // Enable performance monitoring
        if (process.env.NODE_ENV === 'development') {
          newEngine.enableStats();
        }
        
        setEngine(newEngine);
        onLoad(newEngine);
        
        // Track initialization
        novaTrack('quantum_viz_initialized', {
          entropy,
          coherence,
          entanglement,
          timestamp: Date.now(),
        });
        
      } catch (err) {
        console.error('Failed to initialize 3D engine:', err);
        setError('Failed to initialize 3D visualization. Please try refreshing the page.');
        onError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    initEngine();
    
    // Cleanup on unmount
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (engine) {
        engine.dispose();
      }
    };
  }, []);
  
  // Handle animation frame
  const animate = (time) => {
    if (!engine) return;
    
    // Throttle updates to target FPS
    const now = performance.now();
    const delta = now - lastUpdateTime.current;
    
    if (delta > frameTime) {
      lastUpdateTime.current = now - (delta % frameTime);
      
      // Update quantum state visualization
      if (quantumState) {
        engine.updateQuantumState(quantumState);
      }
      
      // Render the scene
      engine.render();
    }
    
    animationRef.current = requestAnimationFrame(animate);
  };
  
  // Start/stop animation when engine is ready
  useEffect(() => {
    if (!engine) return;
    
    // Start animation loop
    animationRef.current = requestAnimationFrame(animate);
    
    // Handle window resize
    const handleResize = () => {
      if (engine) {
        engine.setSize(
          canvasRef.current.parentElement.clientWidth,
          canvasRef.current.parentElement.clientHeight
        );
      }
    };
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Initial resize
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [engine]);
  
  // Update quantum state when props change
  useEffect(() => {
    if (!engine || !quantumState) return;
    
    // Create quantum mesh if it doesn't exist
    if (!engine.getObjectByName('quantumMesh')) {
      const mesh = new NovaQuantumMesh({
        state: quantumState,
        size: 2,
        wireframe: false,
        opacity: 0.9,
        colors: {
          base: 0x3b82f6,
          highlight: 0x8b5cf6,
          entanglement: 0xec4899,
        },
      });
      
      engine.scene.add(mesh);
    }
    
    // Update existing mesh
    const mesh = engine.getObjectByName('quantumMesh');
    if (mesh) {
      mesh.updateState(quantumState, {
        entropy,
        coherence,
        entanglement,
      });
    }
    
  }, [engine, quantumState, entropy, coherence, entanglement]);
  
  // Calculate derived values for UI
  const stats = React.useMemo(() => ({
    coherence: (coherence * 100).toFixed(1) + '%',
    entropy: entropy.toFixed(4),
    entanglement: (entanglement * 100).toFixed(1) + '%',
    qScore: (coherence * (1 - entropy) * 100).toFixed(1),
  }), [entropy, coherence, entanglement]);
  
  // Render loading/error states
  if (error) {
    return (
      <div className={`quantum-canvas-container ${className}`}>
        <div className="no-data">
          <p>Visualization Error</p>
          <p>{error}</p>
        </div>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className={`quantum-canvas-container ${className}`}>
        <div className="no-data">
          <p>Initializing Quantum Visualization</p>
          <p>Loading NovaVision 3D Engine...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`quantum-viz-container ${className}`}>
      <div className="visualization-header">
        <h3>Quantum Entanglement Visualization</h3>
        <div className="coherence-indicator">
          <span className="label">Coherence</span>
          <span className="value">{stats.coherence}</span>
          <div 
            className="coherence-bar" 
            style={{ '--coherence-level': `${coherence * 100}%` }}
          />
        </div>
      </div>
      
      <div className="quantum-canvas-container">
        <canvas 
          ref={canvasRef} 
          className="quantum-canvas"
          aria-label="3D visualization of quantum state entanglement"
          role="img"
        />
        
        {!quantumState && (
          <div className="no-data">
            <p>No Quantum Data</p>
            <p>Waiting for quantum state initialization...</p>
          </div>
        )}
      </div>
      
      <div className="visualization-stats">
        <div className="stat">
          <span className="label">Quantum Score</span>
          <span className="value">{stats.qScore}</span>
        </div>
        <div className="stat">
          <span className="label">Entropy</span>
          <span className="value">{stats.entropy}</span>
        </div>
        <div className="stat">
          <span className="label">Entanglement</span>
          <span className="value">{stats.entanglement}</span>
        </div>
      </div>
    </div>
  );
};

QuantumEntropyViz3D.propTypes = {
  entropy: PropTypes.number,
  coherence: PropTypes.number,
  entanglement: PropTypes.number,
  quantumState: PropTypes.instanceOf(QuantumState),
  onLoad: PropTypes.func,
  onError: PropTypes.func,
  className: PropTypes.string,
};

export default React.memo(QuantumEntropyViz3D);
