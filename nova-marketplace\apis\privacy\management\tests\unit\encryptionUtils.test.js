/**
 * Encryption Utils Tests
 * 
 * This file contains unit tests for the Encryption Utils utility.
 */

const { encryptionUtils } = require('../../utils');

describe('Encryption Utils', () => {
  describe('encrypt', () => {
    it('should encrypt a string', () => {
      const plaintext = 'test-data';
      const encrypted = encryptionUtils.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      expect(encrypted).not.toBe(plaintext);
      
      // The encrypted string should be in the format: iv:encryptedData
      expect(encrypted).toContain(':');
      
      // The encrypted string should be longer than the plaintext
      expect(encrypted.length).toBeGreaterThan(plaintext.length);
    });
    
    it('should encrypt an object', () => {
      const plaintext = { key: 'value', nested: { key: 'value' } };
      const encrypted = encryptionUtils.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      expect(encrypted).not.toBe(JSON.stringify(plaintext));
      
      // The encrypted string should be in the format: iv:encryptedData
      expect(encrypted).toContain(':');
    });
    
    it('should encrypt a number', () => {
      const plaintext = 12345;
      const encrypted = encryptionUtils.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      expect(encrypted).not.toBe(plaintext.toString());
      
      // The encrypted string should be in the format: iv:encryptedData
      expect(encrypted).toContain(':');
    });
    
    it('should handle empty strings', () => {
      const plaintext = '';
      const encrypted = encryptionUtils.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      
      // The encrypted string should be in the format: iv:encryptedData
      expect(encrypted).toContain(':');
    });
    
    it('should handle null values', () => {
      const plaintext = null;
      const encrypted = encryptionUtils.encrypt(plaintext);
      
      expect(encrypted).toBeDefined();
      expect(typeof encrypted).toBe('string');
      
      // The encrypted string should be in the format: iv:encryptedData
      expect(encrypted).toContain(':');
    });
  });
  
  describe('decrypt', () => {
    it('should decrypt an encrypted string', () => {
      const plaintext = 'test-data';
      const encrypted = encryptionUtils.encrypt(plaintext);
      const decrypted = encryptionUtils.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });
    
    it('should decrypt an encrypted object', () => {
      const plaintext = { key: 'value', nested: { key: 'value' } };
      const encrypted = encryptionUtils.encrypt(plaintext);
      const decrypted = encryptionUtils.decrypt(encrypted);
      
      expect(decrypted).toEqual(plaintext);
    });
    
    it('should decrypt an encrypted number', () => {
      const plaintext = 12345;
      const encrypted = encryptionUtils.encrypt(plaintext);
      const decrypted = encryptionUtils.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });
    
    it('should handle empty strings', () => {
      const plaintext = '';
      const encrypted = encryptionUtils.encrypt(plaintext);
      const decrypted = encryptionUtils.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });
    
    it('should handle null values', () => {
      const plaintext = null;
      const encrypted = encryptionUtils.encrypt(plaintext);
      const decrypted = encryptionUtils.decrypt(encrypted);
      
      expect(decrypted).toBe(plaintext);
    });
    
    it('should throw an error for invalid encrypted data', () => {
      expect(() => {
        encryptionUtils.decrypt('invalid-encrypted-data');
      }).toThrow();
    });
  });
  
  describe('hashData', () => {
    it('should hash a string', () => {
      const data = 'test-data';
      const hashed = encryptionUtils.hashData(data);
      
      expect(hashed).toBeDefined();
      expect(typeof hashed).toBe('string');
      expect(hashed).not.toBe(data);
      
      // The hash should be a fixed length
      expect(hashed.length).toBeGreaterThan(0);
    });
    
    it('should hash an object', () => {
      const data = { key: 'value', nested: { key: 'value' } };
      const hashed = encryptionUtils.hashData(data);
      
      expect(hashed).toBeDefined();
      expect(typeof hashed).toBe('string');
      expect(hashed).not.toBe(JSON.stringify(data));
    });
    
    it('should hash a number', () => {
      const data = 12345;
      const hashed = encryptionUtils.hashData(data);
      
      expect(hashed).toBeDefined();
      expect(typeof hashed).toBe('string');
      expect(hashed).not.toBe(data.toString());
    });
    
    it('should handle empty strings', () => {
      const data = '';
      const hashed = encryptionUtils.hashData(data);
      
      expect(hashed).toBeDefined();
      expect(typeof hashed).toBe('string');
    });
    
    it('should handle null values', () => {
      const data = null;
      const hashed = encryptionUtils.hashData(data);
      
      expect(hashed).toBeDefined();
      expect(typeof hashed).toBe('string');
    });
    
    it('should produce the same hash for the same input', () => {
      const data = 'test-data';
      const hash1 = encryptionUtils.hashData(data);
      const hash2 = encryptionUtils.hashData(data);
      
      expect(hash1).toBe(hash2);
    });
    
    it('should produce different hashes for different inputs', () => {
      const data1 = 'test-data-1';
      const data2 = 'test-data-2';
      const hash1 = encryptionUtils.hashData(data1);
      const hash2 = encryptionUtils.hashData(data2);
      
      expect(hash1).not.toBe(hash2);
    });
  });
  
  describe('generateKey', () => {
    it('should generate a key with the default length', () => {
      const key = encryptionUtils.generateKey();
      
      expect(key).toBeDefined();
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(0);
    });
    
    it('should generate a key with a custom length', () => {
      const length = 32;
      const key = encryptionUtils.generateKey(length);
      
      expect(key).toBeDefined();
      expect(typeof key).toBe('string');
      expect(key.length).toBe(length * 2); // Hex encoding doubles the length
    });
    
    it('should generate different keys on each call', () => {
      const key1 = encryptionUtils.generateKey();
      const key2 = encryptionUtils.generateKey();
      
      expect(key1).not.toBe(key2);
    });
  });
});
