<#
.SYNOPSIS
    Serves the NovaAlign documentation locally using MkDocs.
.DESCRIPTION
    This script sets up a Python virtual environment, installs the required packages,
    and serves the documentation on http://localhost:8000
.EXAMPLE
    .\serve-docs.ps1
#>

# Stop on first error
$ErrorActionPreference = "Stop"

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python version: $pythonVersion"
} catch {
    Write-Error "Python is not installed or not in PATH. Please install Python 3.7 or later."
    exit 1
}

# Check if virtual environment exists, create if not
$venvPath = ".venv"
if (-not (Test-Path $venvPath)) {
    Write-Host "Creating Python virtual environment..."
    python -m venv $venvPath
}

# Activate virtual environment
$activateScript = ".\$venvPath\Scripts\Activate.ps1"
if (-not (Test-Path $activateScript)) {
    Write-Error "Failed to find virtual environment activation script"
    exit 1
}

# Import the activation script
. $activateScript

# Upgrade pip
Write-Host "Upgrading pip..."
python -m pip install --upgrade pip

# Install requirements
Write-Host "Installing documentation dependencies..."
pip install -r requirements-docs.txt

# Check if mkdocs is installed
try {
    mkdocs --version
} catch {
    Write-Error "Failed to verify MkDocs installation"
    exit 1
}

# Serve the documentation
Write-Host "Starting MkDocs server..."
Write-Host "Documentation will be available at: http://localhost:8000"
Write-Host "Press Ctrl+C to stop the server"

# Run mkdocs serve
mkdocs serve
