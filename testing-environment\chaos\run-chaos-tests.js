/**
 * Chaos Testing Script for NovaConnect Universal API Connector
 * 
 * This script runs chaos tests against the NovaConnect API.
 */

const axios = require('axios');
const ChaosTester = require('./chaos-tester');

// Create chaos tester
const chaosTester = new ChaosTester({
  networkFailureRate: 0.2,
  malformedResponseRate: 0.2,
  delayRate: 0.3,
  maxDelay: 3000
});

// Configuration
const BASE_URL = 'http://localhost:3000'; // Auth service
const REGISTRY_URL = 'http://localhost:3001'; // Connector registry
const EXECUTOR_URL = 'http://localhost:3002'; // Connector executor

// Test data
const testData = {
  connector: {
    name: 'Chaos Test Connector',
    version: '1.0.0',
    category: 'Chaos',
    description: 'Connector for chaos testing',
    author: 'NovaGRC',
    tags: ['chaos', 'resilience'],
    authentication: {
      type: 'API_KEY',
      fields: {
        apiKey: {
          type: 'string',
          description: 'API Key',
          required: true
        }
      }
    },
    configuration: {
      baseUrl: 'http://localhost:3005',
      headers: {
        'Content-Type': 'application/json'
      }
    },
    endpoints: [
      {
        id: 'getResource',
        name: 'Get Resource',
        path: '/resource',
        method: 'GET'
      }
    ],
    mappings: [
      {
        sourceEndpoint: 'getResource',
        targetEntity: 'Resource',
        transformations: [
          {
            source: '$.id',
            target: 'resourceId'
          },
          {
            source: '$.name',
            target: 'resourceName'
          }
        ]
      }
    ]
  },
  credential: {
    name: 'Chaos Test Credential',
    authType: 'API_KEY',
    credentials: {
      apiKey: 'chaos-test-api-key'
    },
    userId: 'chaos-test-user'
  }
};

/**
 * Run a complete API workflow
 */
async function runApiWorkflow() {
  // Step 1: Create connector
  const connectorResponse = await axios.post(`${REGISTRY_URL}/connectors`, testData.connector);
  const connectorId = connectorResponse.data.id;
  
  // Step 2: Create credential
  const credential = {
    ...testData.credential,
    connectorId
  };
  const credentialResponse = await axios.post(`${BASE_URL}/credentials`, credential);
  const credentialId = credentialResponse.data.id;
  
  // Step 3: Get connector
  await axios.get(`${REGISTRY_URL}/connectors/${connectorId}`);
  
  // Step 4: Get credential
  await axios.get(`${BASE_URL}/credentials/${credentialId}`);
  
  // Step 5: Update connector
  const updatedConnector = {
    ...testData.connector,
    description: 'Updated description for chaos testing'
  };
  await axios.put(`${REGISTRY_URL}/connectors/${connectorId}`, updatedConnector);
  
  // Step 6: Update credential
  const updatedCredential = {
    name: 'Updated Chaos Test Credential'
  };
  await axios.put(`${BASE_URL}/credentials/${credentialId}`, updatedCredential);
  
  // Step 7: Delete credential
  await axios.delete(`${BASE_URL}/credentials/${credentialId}`);
  
  // Step 8: Delete connector
  await axios.delete(`${REGISTRY_URL}/connectors/${connectorId}`);
  
  return true;
}

/**
 * Run chaos tests
 */
async function runChaosTests() {
  console.log('Starting NovaConnect Chaos Tests...');
  
  try {
    // Start chaos testing
    chaosTester.start();
    
    // Run resilience test
    const results = await chaosTester.testSystemResilience(runApiWorkflow, 20);
    
    // Generate HTML report
    const reportPath = chaosTester.generateHtmlReport(results);
    
    console.log('\nChaos testing complete.');
    console.log(`Success Rate: ${results.successRate.toFixed(2)}%`);
    console.log(`Successful: ${results.successful}/${results.total}`);
    console.log(`Failed: ${results.failed}/${results.total}`);
    console.log(`Report saved to: ${reportPath}`);
    
    // Analyze system resilience
    if (results.successRate >= 80) {
      console.log('\n✅ System shows HIGH resilience to chaos!');
    } else if (results.successRate >= 60) {
      console.log('\n⚠️ System shows MEDIUM resilience to chaos.');
    } else {
      console.log('\n❌ System shows LOW resilience to chaos!');
    }
    
    // Analyze error types
    const chaosErrors = results.errors.filter(e => e.chaosTest).length;
    const systemErrors = results.errors.filter(e => !e.chaosTest).length;
    
    console.log(`\nChaos-induced errors: ${chaosErrors}`);
    console.log(`System errors: ${systemErrors}`);
    
    if (systemErrors > 0) {
      console.log('\n⚠️ System errors detected! These should be investigated as they indicate potential resilience issues.');
    }
  } catch (error) {
    console.error('Error running chaos tests:', error.message);
  } finally {
    // Stop chaos testing
    chaosTester.stop();
  }
}

// Run the chaos tests
runChaosTests();
