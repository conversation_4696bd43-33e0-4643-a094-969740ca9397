# AlphaFold Integration with NovaFoldClient

This example demonstrates how to integrate the AlphaFold protein structure prediction service with the NovaFoldClient for advanced protein structure analysis with comphyological metrics.

## Prerequisites

1. Python 3.8 or higher
2. Required Python packages (install with `pip install -r requirements.txt`)
3. An AlphaFold API key (optional, but required for real predictions)

## Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/consciousness-chemistry-engine.git
   cd consciousness-chemistry-engine
   ```

2. Install the required dependencies:
   ```bash
   pip install -r examples/requirements.txt
   ```

3. (Optional) Set your AlphaFold API key as an environment variable:
   - Linux/macOS:
     ```bash
     export ALPHAFOLD_API_KEY='your_api_key_here'
     ```
   - Windows (Command Prompt):
     ```cmd
     set ALPHAFOLD_API_KEY=your_api_key_here
     ```
   - Windows (PowerShell):
     ```powershell
     $env:ALPHAFOLD_API_KEY='your_api_key_here'
     ```

## Running the Example

```bash
python examples/alphafold_integration.py
```

## Example Output

```
Initializing NovaFoldClient with AlphaFold engine...

Predicting structure for sequence (length: 63):
MKALTARQQEVFDLIRDHISQTGMPPTRAEIAQRLGFRSPNADKRVNGQTYAQQARKAFQERIDKSKEA

Prediction complete!
Prediction source: external_engine
Engine: AlphaFoldEngine
Job ID: af2_1234567890

Validation results:
- sequence_length: 63
- invalid_amino_acids: []
- amino_acid_distribution:
  - A: 0.12698412698412698
  - C: 0.0
  - D: 0.07936507936507936
  - E: 0.1111111111111111
  - F: 0.047619047619047616
  - G: 0.06349206349206349
  - H: 0.031746031746031744
  - I: 0.09523809523809523
  - K: 0.1111111111111111
  - L: 0.047619047619047616
  - M: 0.031746031746031744
  - N: 0.031746031746031744
  - P: 0.06349206349206349
  - Q: 0.1111111111111111
  - R: 0.1111111111111111
  - S: 0.047619047619047616
  - T: 0.06349206349206349
  - V: 0.015873015873015872
  - W: 0.0
  - Y: 0.015873015873015872
```

## How It Works

The example script performs the following steps:

1. Initializes a `NovaFoldClient` with the AlphaFold engine
2. Submits a protein sequence for structure prediction
3. Processes the prediction results
4. Applies comphyological metrics and validations
5. Displays the results

## Customization

You can modify the example to:

1. Use a different protein sequence
2. Apply folding variants (e.g., 'misfolded', 'thermostable')
3. Validate against known PDB structures
4. Save the results to a file

## Troubleshooting

- **API Key Issues**: Ensure your AlphaFold API key is set correctly as an environment variable
- **Network Errors**: Check your internet connection and firewall settings
- **Dependency Issues**: Make sure all required packages are installed with the correct versions

## License

This example is part of the Consciousness Chemistry Engine project and is available under the MIT License.
