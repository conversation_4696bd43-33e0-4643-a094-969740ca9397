/**
 * Comphyology Visualization Worker Manager
 *
 * This class manages Web Workers for Comphyology visualization data generation,
 * providing a simple interface for offloading complex calculations to background threads.
 */

class ComphyologyVisualizationWorkerManager {
  /**
   * Constructor
   *
   * @param {Object} options - Manager options
   * @param {number} options.maxWorkers - Maximum number of workers to create (default: navigator.hardwareConcurrency || 4)
   * @param {boolean} options.enableLogging - Whether to enable logging (default: false)
   */
  constructor(options = {}) {
    this.options = {
      maxWorkers: options.maxWorkers || (typeof navigator !== 'undefined' && navigator.hardwareConcurrency) || 4,
      enableLogging: options.enableLogging || false,
      ...options
    };

    this.workers = [];
    this.activeWorkers = {};
    this.pendingTasks = [];
    this.taskCounter = 0;
    this.initialized = false;
    this.callbacks = {};

    if (this.options.enableLogging) {
      console.log(`ComphyologyVisualizationWorkerManager initialized with ${this.options.maxWorkers} workers`);
    }
  }

  /**
   * Initialize the worker manager
   *
   * @returns {Promise} - Promise that resolves when all workers are initialized
   */
  async initialize() {
    if (this.initialized) {
      return Promise.resolve();
    }

    // Check if Web Workers are supported
    if (typeof Worker === 'undefined') {
      if (this.options.enableLogging) {
        console.warn('Web Workers are not supported in this environment. Falling back to main thread processing.');
      }
      this.initialized = true;
      return Promise.resolve();
    }

    // Create workers
    const workerInitPromises = [];

    for (let i = 0; i < this.options.maxWorkers; i++) {
      workerInitPromises.push(this._createWorker(i));
    }

    // Wait for all workers to initialize
    await Promise.all(workerInitPromises);

    this.initialized = true;

    // Process any pending tasks
    this._processPendingTasks();

    return Promise.resolve();
  }

  /**
   * Create a new worker
   *
   * @param {number} index - Worker index
   * @returns {Promise} - Promise that resolves when the worker is initialized
   * @private
   */
  _createWorker(index) {
    return new Promise((resolve, reject) => {
      try {
        // Create worker
        const worker = new Worker(new URL('./visualization_worker.js', import.meta.url));

        // Set up message handler
        worker.onmessage = (e) => {
          const { type, id, payload } = e.data;

          if (type === 'ready') {
            if (this.options.enableLogging) {
              console.log(`Worker ${index} is ready`);
            }

            // Initialize worker
            worker.postMessage({
              type: 'initialize',
              id: `init-${index}`,
              payload: this.options
            });
          } else if (type === 'result') {
            // Handle task result
            this._handleTaskResult(id, payload);
          } else if (type === 'error') {
            // Handle task error
            this._handleTaskError(id, payload);
          }
        };

        // Set up error handler
        worker.onerror = (error) => {
          console.error(`Worker ${index} error:`, error);
          reject(error);
        };

        // Add worker to pool
        this.workers.push({
          worker,
          index,
          busy: false
        });

        resolve();
      } catch (error) {
        console.error(`Failed to create worker ${index}:`, error);
        reject(error);
      }
    });
  }

  /**
   * Handle task result
   *
   * @param {string} id - Task ID
   * @param {Object} result - Task result
   * @private
   */
  _handleTaskResult(id, result) {
    // Find the callback for this task
    const callback = this.callbacks[id];

    if (callback) {
      // Call the callback with the result
      callback.resolve(result);

      // Remove the callback
      delete this.callbacks[id];
    }

    // Mark the worker as free
    const workerInfo = this.activeWorkers[id];

    if (workerInfo) {
      workerInfo.busy = false;
      delete this.activeWorkers[id];

      // Process any pending tasks
      this._processPendingTasks();
    }
  }

  /**
   * Handle task error
   *
   * @param {string} id - Task ID
   * @param {Object} error - Task error
   * @private
   */
  _handleTaskError(id, error) {
    // Find the callback for this task
    const callback = this.callbacks[id];

    if (callback) {
      // Call the callback with the error
      callback.reject(new Error(error.message));

      // Remove the callback
      delete this.callbacks[id];
    }

    // Mark the worker as free
    const workerInfo = this.activeWorkers[id];

    if (workerInfo) {
      workerInfo.busy = false;
      delete this.activeWorkers[id];

      // Process any pending tasks
      this._processPendingTasks();
    }
  }

  /**
   * Process pending tasks
   *
   * @private
   */
  _processPendingTasks() {
    // Check if there are any pending tasks
    if (this.pendingTasks.length === 0) {
      return;
    }

    // Find a free worker
    const freeWorker = this.workers.find(w => !w.busy);

    if (!freeWorker) {
      return;
    }

    // Get the next task
    const task = this.pendingTasks.shift();

    // Mark the worker as busy
    freeWorker.busy = true;

    // Add to active workers
    this.activeWorkers[task.id] = freeWorker;

    // Send the task to the worker
    freeWorker.worker.postMessage({
      type: task.type,
      id: task.id,
      payload: task.payload
    });

    // Process more pending tasks if available
    if (this.pendingTasks.length > 0) {
      this._processPendingTasks();
    }
  }

  /**
   * Execute a task on a worker
   *
   * @param {string} type - Task type
   * @param {Object} payload - Task payload
   * @returns {Promise} - Promise that resolves with the task result
   * @private
   */
  _executeTask(type, payload) {
    // Generate a unique task ID
    const id = `task-${this.taskCounter++}`;

    // Create a promise for the task
    const promise = new Promise((resolve, reject) => {
      this.callbacks[id] = { resolve, reject };
    });

    // Add the task to the pending queue
    this.pendingTasks.push({
      id,
      type,
      payload
    });

    // Process pending tasks
    if (this.initialized) {
      this._processPendingTasks();
    }

    return promise;
  }

  /**
   * Generate Morphological Resonance Field data
   *
   * @param {Object} options - Visualization options
   * @returns {Promise<Object>} - Promise that resolves with visualization data
   */
  generateMorphologicalResonanceField(options = {}) {
    return this._executeTask('morphological', options);
  }

  /**
   * Generate Quantum Phase Space Map data
   *
   * @param {Object} options - Visualization options
   * @returns {Promise<Object>} - Promise that resolves with visualization data
   */
  generateQuantumPhaseSpaceMap(options = {}) {
    return this._executeTask('quantum', options);
  }

  /**
   * Generate Ethical Tensor Projection data
   *
   * @param {Object} options - Visualization options
   * @returns {Promise<Object>} - Promise that resolves with visualization data
   */
  generateEthicalTensorProjection(options = {}) {
    return this._executeTask('ethical', options);
  }

  /**
   * Generate Trinity Integration Diagram data
   *
   * @param {Object} options - Visualization options
   * @returns {Promise<Object>} - Promise that resolves with visualization data
   */
  generateTrinityIntegrationDiagram(options = {}) {
    return this._executeTask('trinity', options);
  }

  /**
   * Terminate all workers
   */
  terminate() {
    for (const workerInfo of this.workers) {
      workerInfo.worker.terminate();
    }

    this.workers = [];
    this.activeWorkers = {};
    this.initialized = false;

    if (this.options.enableLogging) {
      console.log('All workers terminated');
    }
  }
}

// Support both ESM and CommonJS
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ComphyologyVisualizationWorkerManager;
} else {
  // ESM export
  export default ComphyologyVisualizationWorkerManager;
}
