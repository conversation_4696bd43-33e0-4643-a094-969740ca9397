/**
 * HOURLY TARGET TRACKER
 * Real-time display of $425-$900/hour revenue targets
 * Shows market allocation and progress toward hourly goals
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  ClockIcon,
  CurrencyDollarIcon,
  TrophyIcon,
  ChartBarIcon,
  FireIcon,
  ShieldCheckIcon,
  BoltIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useHourlyRevenue, useMarketAllocation } from '../hooks/useUnifiedData';

const URGENCY_COLORS = {
  ACHIEVED: 'text-green-400 bg-green-900/30',
  LOW: 'text-blue-400 bg-blue-900/30',
  MEDIUM: 'text-yellow-400 bg-yellow-900/30',
  HIGH: 'text-orange-400 bg-orange-900/30',
  CRITICAL: 'text-red-400 bg-red-900/30'
};

const URGENCY_ICONS = {
  ACHIEVED: TrophyIcon,
  LOW: ShieldCheckIcon,
  MEDIUM: ClockIcon,
  HIGH: FireIcon,
  CRITICAL: ExclamationTriangleIcon
};

const MARKET_COLORS = {
  STOCKS: 'bg-blue-500',
  CRYPTO: 'bg-orange-500',
  FOREX: 'bg-green-500'
};

export default function HourlyTargetTracker() {
  // Use unified data hooks - single source of truth
  const hourlyRevenue = useHourlyRevenue();
  const marketAllocation = useMarketAllocation();

  const [isLoading, setIsLoading] = useState(false); // No longer loading from API

  // Calculate urgency level based on progress and time
  const getUrgencyLevel = () => {
    if (hourlyRevenue.percentage >= 100) return 'ACHIEVED';
    if (hourlyRevenue.timeElapsed < 30) return 'LOW';
    if (hourlyRevenue.percentage >= 60) return 'LOW';
    if (hourlyRevenue.percentage >= 40) return 'MEDIUM';
    if (hourlyRevenue.timeElapsed > 45) return 'CRITICAL';
    return 'HIGH';
  };

  // Generate trading recommendations based on unified data
  const getTradingRecommendations = () => {
    const urgency = getUrgencyLevel();

    return [
      {
        market: "FOREX",
        action: "Increase activity",
        details: `${hourlyRevenue.tradesThisHour} trades completed`,
        position_size: "2.67x",
        urgency: urgency,
        trades_needed: Math.max(0, 16 - hourlyRevenue.tradesThisHour),
        symbols: ["EURUSD", "GBPUSD", "USDJPY"]
      },
      {
        market: "STOCKS",
        action: "Monitor coherence",
        details: `${(marketAllocation.stocks || 0).toFixed(1)}% allocation`,
        position_size: "1.5x",
        urgency: marketAllocation.stocks > 15 ? "LOW" : "MEDIUM",
        trades_needed: Math.max(0, 5 - Math.floor(hourlyRevenue.tradesThisHour * 0.3)),
        symbols: ["SPY", "QQQ", "NVDA"]
      },
      {
        market: "CRYPTO",
        action: "Cautious positioning",
        details: `${(marketAllocation.crypto || 0).toFixed(1)}% allocation`,
        position_size: "1.2x",
        urgency: marketAllocation.crypto > 10 ? "LOW" : "HIGH",
        trades_needed: Math.max(0, 3 - Math.floor(hourlyRevenue.tradesThisHour * 0.2)),
        symbols: ["BTC", "ETH", "SOL"]
      }
    ];
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const getProgressColor = (rate) => {
    if (rate >= 100) return 'bg-green-500';
    if (rate >= 80) return 'bg-blue-500';
    if (rate >= 60) return 'bg-yellow-500';
    if (rate >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  // Use unified data instead of targetData
  const urgencyLevel = getUrgencyLevel();
  const tradingRecommendations = getTradingRecommendations();
  const UrgencyIcon = URGENCY_ICONS[urgencyLevel] || ClockIcon;
  const urgencyColor = URGENCY_COLORS[urgencyLevel] || URGENCY_COLORS.LOW;

  if (isLoading) {
    return (
      <div className="p-6 rounded-lg border border-gray-600 bg-gray-800/50">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-600 rounded mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-600 rounded w-3/4"></div>
            <div className="h-4 bg-gray-600 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Data source indicator
  const dataSourceColor = hourlyRevenue.isLive ? 'text-green-400' : 'text-yellow-400';
  const dataSourceText = hourlyRevenue.isLive ? 'LIVE MT5 DATA' : 'SIMULATION MODE';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-6 rounded-lg border border-green-500/30 bg-gradient-to-br from-green-900/20 to-emerald-900/20 backdrop-blur-sm"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <CurrencyDollarIcon className="w-6 h-6 text-green-400" />
          <div>
            <h3 className="text-xl font-bold text-white">
              Hourly Revenue Target
            </h3>
            <p className="text-sm text-gray-400">
              $425-$900/hour • Zero Loss Guarantee
            </p>
            <p className={`text-xs ${dataSourceColor} font-mono`}>
              {dataSourceText}
            </p>
          </div>
        </div>

        <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${urgencyColor}`}>
          <UrgencyIcon className="w-5 h-5" />
          <span className="font-bold">
            {urgencyLevel}
          </span>
        </div>
      </div>

      {/* Current Hour Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4">
            <div>
              <span className="text-sm text-gray-400">Target:</span>
              <span className="text-lg font-bold text-white ml-2">
                {formatCurrency(hourlyRevenue.target)}
              </span>
            </div>
            <div>
              <span className="text-sm text-gray-400">Progress:</span>
              <span className="text-lg font-bold text-green-400 ml-2">
                {formatCurrency(hourlyRevenue.progress)}
              </span>
            </div>
            <div>
              <span className="text-sm text-gray-400">Needed:</span>
              <span className="text-lg font-bold text-orange-400 ml-2">
                {formatCurrency(hourlyRevenue.needed)}
              </span>
            </div>
          </div>

          <div className="text-right">
            <div className="text-2xl font-bold text-white">
              {hourlyRevenue.percentage.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400">
              {hourlyRevenue.timeRemaining}min
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-4 mb-2">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${Math.min(100, hourlyRevenue.percentage)}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
            className={`h-4 rounded-full ${getProgressColor(hourlyRevenue.percentage)}`}
          />
        </div>

        <div className="flex justify-between text-sm text-gray-400">
          <span>0min</span>
          <span>{hourlyRevenue.timeElapsed}min elapsed</span>
          <span>60min</span>
        </div>
      </div>

      {/* Market Allocation */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-3">🎯 Market Allocation Strategy</h4>
        <div className="space-y-3">
          {[
            { market: 'forex', percentage: marketAllocation.forex, label: 'FOREX' },
            { market: 'stocks', percentage: marketAllocation.stocks, label: 'STOCKS' },
            { market: 'crypto', percentage: marketAllocation.crypto, label: 'CRYPTO' }
          ].map(({ market, percentage, label }) => (
            <div key={market} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-4 h-4 rounded ${MARKET_COLORS[label]}`}></div>
                <span className="text-white font-medium">{label.toLowerCase()}</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-32 bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${MARKET_COLORS[label]}`}
                    style={{ width: `${percentage || 0}%` }}
                  />
                </div>
                <span className="text-white font-bold w-12 text-right">
                  {(percentage || 0).toFixed(1)}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Trading Recommendations */}
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-white mb-3">📈 Active Trading Recommendations</h4>
        <div className="space-y-2">
          {tradingRecommendations.length > 0 ? (
            tradingRecommendations.map((rec, index) => (
              <div key={index} className="p-3 rounded-lg bg-gray-800/50 border border-gray-600">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded ${MARKET_COLORS[rec.market]}`}></div>
                    <span className="text-white font-medium">{rec.market}</span>
                    <span className="text-sm text-gray-400">({rec.details})</span>
                  </div>
                  <span className="text-green-400 font-bold">{rec.position_size}</span>
                </div>
                <div className="grid grid-cols-3 gap-2 text-sm">
                  <div>
                    <span className="text-gray-400">Trades:</span>
                    <span className="text-white ml-1">{rec.trades_needed}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Size:</span>
                    <span className="text-white ml-1">{rec.position_size}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Urgency:</span>
                    <span className={`ml-1 ${URGENCY_COLORS[rec.urgency].split(' ')[0]}`}>
                      {rec.urgency}
                    </span>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-400">
                  Focus: {rec.symbols.join(', ')}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-gray-400">
              Target achieved or analyzing markets...
            </div>
          )}
        </div>
      </div>

      {/* Stats Summary */}
      <div className="grid grid-cols-3 gap-4">
        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
          <div className="text-lg font-bold text-white">{hourlyRevenue.tradesThisHour}</div>
          <div className="text-sm text-gray-400">Trades This Hour</div>
        </div>

        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
          <div className="text-lg font-bold text-green-400">
            {formatCurrency(hourlyRevenue.avgPerTrade)}
          </div>
          <div className="text-sm text-gray-400">Avg Per Trade</div>
        </div>

        <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-600 text-center">
          <div className="text-lg font-bold text-blue-400">
            {formatCurrency(hourlyRevenue.projectedHour)}
          </div>
          <div className="text-sm text-gray-400">Projected/Hour</div>
        </div>
      </div>

      {/* Zero Loss Guarantee */}
      <div className="mt-6 p-3 rounded-lg bg-gradient-to-r from-green-900/30 to-emerald-900/30 border border-green-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ShieldCheckIcon className="w-4 h-4 text-green-400" />
            <span className="text-sm text-green-300">Zero Loss Guarantee Active</span>
          </div>
          <span className="text-green-400 font-mono text-sm">
            φ-PROTECTED
          </span>
        </div>
      </div>
    </motion.div>
  );
}
