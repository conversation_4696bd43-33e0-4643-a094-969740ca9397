'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import { FiSave, FiArrowLeft, FiCloud, FiGithub, FiDatabase, FiSlack, FiServer } from 'react-icons/fi';
import { createConnector } from '@/services/novaCoreApi';
import Link from 'next/link';

// Connector types
const connectorTypes = [
  { id: 'aws', name: 'AWS', icon: FiCloud, description: 'Connect to AWS services like Security Hub, CloudTrail, and Config' },
  { id: 'azure', name: 'Azure', icon: FiCloud, description: 'Connect to Azure services like Security Center and Monitor' },
  { id: 'gcp', name: 'Google Cloud', icon: FiCloud, description: 'Connect to Google Cloud services like Security Command Center' },
  { id: 'github', name: 'GitHub', icon: FiGithub, description: 'Connect to GitHub repositories and organization settings' },
  { id: 'jira', name: '<PERSON><PERSON>', icon: FiServer, description: 'Connect to Jira projects and issues' },
  { id: 'slack', name: 'Slack', icon: FiSlack, description: 'Connect to Slack channels and messages' },
  { id: 'rest_api', name: 'REST API', icon: FiServer, description: 'Connect to any REST API endpoint' },
  { id: 'database', name: 'Database', icon: FiDatabase, description: 'Connect to databases like MySQL, PostgreSQL, and MongoDB' },
];

// Authentication types
const authTypes = [
  { id: 'api_key', name: 'API Key', fields: ['apiKey', 'apiKeyHeader'] },
  { id: 'oauth2', name: 'OAuth 2.0', fields: ['clientId', 'clientSecret', 'authorizationUrl', 'tokenUrl', 'scope'] },
  { id: 'basic', name: 'Basic Auth', fields: ['username', 'password'] },
  { id: 'aws_iam', name: 'AWS IAM', fields: ['accessKey', 'secretKey', 'region'] },
  { id: 'azure_ad', name: 'Azure AD', fields: ['tenantId', 'clientId', 'clientSecret'] },
  { id: 'gcp_service_account', name: 'GCP Service Account', fields: ['projectId', 'privateKey'] },
];

export default function NewConnectorPage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [selectedType, setSelectedType] = useState<string | null>(null);
  const [selectedAuthType, setSelectedAuthType] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    baseUrl: '',
    apiVersion: '',
    authFields: {} as Record<string, string>,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form field change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle auth field change
  const handleAuthFieldChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      authFields: {
        ...formData.authFields,
        [field]: value,
      },
    });
  };

  // Handle connector type selection
  const handleTypeSelect = (typeId: string) => {
    setSelectedType(typeId);
    
    // Set default auth type based on connector type
    switch (typeId) {
      case 'aws':
        setSelectedAuthType('aws_iam');
        break;
      case 'azure':
        setSelectedAuthType('azure_ad');
        break;
      case 'gcp':
        setSelectedAuthType('gcp_service_account');
        break;
      case 'github':
      case 'jira':
      case 'slack':
        setSelectedAuthType('oauth2');
        break;
      default:
        setSelectedAuthType('api_key');
    }
    
    setStep(2);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedType || !selectedAuthType) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare connector data
      const connectorData = {
        name: formData.name,
        type: selectedType,
        description: formData.description,
        authentication: {
          type: selectedAuthType,
          credentials: formData.authFields,
        },
        config: {
          baseUrl: formData.baseUrl,
          apiVersion: formData.apiVersion,
        },
      };
      
      // In a real implementation, this would create the connector via API
      // await createConnector(connectorData);
      
      // For now, just log the data and redirect
      console.log('Creating connector:', connectorData);
      
      // Redirect to connectors page
      router.push('/connectors');
    } catch (error) {
      console.error('Error creating connector:', error);
      alert('Error creating connector. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get selected auth type fields
  const getAuthTypeFields = () => {
    if (!selectedAuthType) return [];
    
    const authType = authTypes.find(at => at.id === selectedAuthType);
    return authType ? authType.fields : [];
  };

  return (
    <MainLayout>
      <div className="mb-6 flex items-center">
        <Link href="/connectors" className="mr-4">
          <FiArrowLeft className="h-5 w-5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">New Connector</h1>
          <p className="text-gray-500 dark:text-gray-400">Create a new data source connector</p>
        </div>
      </div>

      <div className="card">
        {/* Step 1: Select Connector Type */}
        {step === 1 && (
          <div>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Connector Type</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {connectorTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <div
                    key={type.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedType === type.id
                        ? 'border-primary bg-primary/5'
                        : 'border-gray-200 dark:border-gray-700 hover:border-primary hover:bg-primary/5'
                    }`}
                    onClick={() => handleTypeSelect(type.id)}
                  >
                    <div className="flex items-center mb-2">
                      <Icon className="h-5 w-5 text-primary mr-2" />
                      <h3 className="font-medium text-gray-900 dark:text-white">{type.name}</h3>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">{type.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Step 2: Configure Connector */}
        {step === 2 && selectedType && (
          <form onSubmit={handleSubmit}>
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Configure Connector</h2>
            
            {/* Basic Information */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="label">Connector Name</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="input"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label htmlFor="description" className="label">Description</label>
                  <input
                    type="text"
                    id="description"
                    name="description"
                    className="input"
                    value={formData.description}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
            
            {/* Connection Settings */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">Connection Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="baseUrl" className="label">Base URL</label>
                  <input
                    type="text"
                    id="baseUrl"
                    name="baseUrl"
                    className="input"
                    value={formData.baseUrl}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label htmlFor="apiVersion" className="label">API Version</label>
                  <input
                    type="text"
                    id="apiVersion"
                    name="apiVersion"
                    className="input"
                    value={formData.apiVersion}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>
            
            {/* Authentication */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">Authentication</h3>
              <div className="mb-4">
                <label htmlFor="authType" className="label">Authentication Type</label>
                <select
                  id="authType"
                  className="input"
                  value={selectedAuthType || ''}
                  onChange={(e) => setSelectedAuthType(e.target.value)}
                  required
                >
                  <option value="">Select Authentication Type</option>
                  {authTypes.map((authType) => (
                    <option key={authType.id} value={authType.id}>
                      {authType.name}
                    </option>
                  ))}
                </select>
              </div>
              
              {selectedAuthType && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {getAuthTypeFields().map((field) => (
                    <div key={field}>
                      <label htmlFor={field} className="label">
                        {field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')}
                      </label>
                      <input
                        type={field.includes('password') || field.includes('secret') || field.includes('key') ? 'password' : 'text'}
                        id={field}
                        className="input"
                        value={formData.authFields[field] || ''}
                        onChange={(e) => handleAuthFieldChange(field, e.target.value)}
                        required
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex justify-between">
              <button
                type="button"
                className="btn btn-outline"
                onClick={() => setStep(1)}
              >
                Back
              </button>
              <button
                type="submit"
                className="btn btn-primary flex items-center"
                disabled={isSubmitting}
              >
                <FiSave className="mr-2" />
                {isSubmitting ? 'Creating...' : 'Create Connector'}
              </button>
            </div>
          </form>
        )}
      </div>
    </MainLayout>
  );
}
