/**
 * Unit tests for the Contracts & Policy Lifecycle Connector
 */

const axios = require('axios');
const ContractsPolicyLifecycleConnector = require('../../../../connector/implementations/contracts-policy-lifecycle');

// Mock axios
jest.mock('axios');

// Mock logger
jest.mock('../../../../utils/logger', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }))
}));

describe('ContractsPolicyLifecycleConnector', () => {
  let connector;
  let mockConfig;
  let mockCredentials;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock config and credentials
    mockConfig = {
      baseUrl: 'https://api.test.com'
    };
    
    mockCredentials = {
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      redirectUri: 'https://test-redirect.com'
    };
    
    // Create connector instance
    connector = new ContractsPolicyLifecycleConnector(mockConfig, mockCredentials);
  });
  
  describe('constructor', () => {
    it('should initialize with provided config and credentials', () => {
      expect(connector.config).toEqual(mockConfig);
      expect(connector.credentials).toEqual(mockCredentials);
      expect(connector.baseUrl).toBe(mockConfig.baseUrl);
    });
    
    it('should use default baseUrl if not provided', () => {
      const connectorWithDefaults = new ContractsPolicyLifecycleConnector();
      expect(connectorWithDefaults.baseUrl).toBe('https://api.example.com');
    });
  });
  
  describe('initialize', () => {
    it('should authenticate if credentials are provided', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockResolvedValue();
      
      await connector.initialize();
      
      expect(connector.authenticate).toHaveBeenCalled();
    });
    
    it('should not authenticate if credentials are not provided', async () => {
      // Create connector without credentials
      const connectorWithoutCredentials = new ContractsPolicyLifecycleConnector(mockConfig, {});
      
      // Mock authenticate method
      connectorWithoutCredentials.authenticate = jest.fn().mockResolvedValue();
      
      await connectorWithoutCredentials.initialize();
      
      expect(connectorWithoutCredentials.authenticate).not.toHaveBeenCalled();
    });
  });
  
  describe('authenticate', () => {
    it('should make a POST request to the token endpoint', async () => {
      // Mock axios post response
      axios.post.mockResolvedValue({
        data: {
          access_token: 'test-access-token',
          expires_in: 3600
        }
      });
      
      await connector.authenticate();
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/oauth2/token`,
        {
          grant_type: 'client_credentials',
          client_id: mockCredentials.clientId,
          client_secret: mockCredentials.clientSecret,
          scope: 'read:contracts write:contracts read:policies write:policies'
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(connector.accessToken).toBe('test-access-token');
      expect(connector.tokenExpiry).toBeDefined();
    });
    
    it('should throw an error if authentication fails', async () => {
      // Mock axios post error
      const errorMessage = 'Authentication failed';
      axios.post.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.authenticate()).rejects.toThrow(`Authentication failed: ${errorMessage}`);
    });
  });
  
  describe('getAuthHeaders', () => {
    it('should return authorization headers with access token', async () => {
      // Set access token and expiry
      connector.accessToken = 'test-access-token';
      connector.tokenExpiry = Date.now() + 3600000; // 1 hour from now
      
      const headers = await connector.getAuthHeaders();
      
      expect(headers).toEqual({
        'Authorization': 'Bearer test-access-token'
      });
    });
    
    it('should authenticate if access token is not set', async () => {
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      
      const headers = await connector.getAuthHeaders();
      
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
    
    it('should authenticate if token is about to expire', async () => {
      // Set access token and expiry to 4 minutes from now (less than 5 minutes)
      connector.accessToken = 'expiring-access-token';
      connector.tokenExpiry = Date.now() + 240000;
      
      // Mock authenticate method
      connector.authenticate = jest.fn().mockImplementation(() => {
        connector.accessToken = 'new-access-token';
        connector.tokenExpiry = Date.now() + 3600000;
      });
      
      const headers = await connector.getAuthHeaders();
      
      expect(connector.authenticate).toHaveBeenCalled();
      expect(headers).toEqual({
        'Authorization': 'Bearer new-access-token'
      });
    });
  });
  
  describe('listContracts', () => {
    it('should make a GET request to the contracts endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'contract-1', title: 'Contract 1' },
            { id: 'contract-2', title: 'Contract 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'active', limit: 50 };
      const result = await connector.listContracts(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/contracts`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if the request fails', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get error
      const errorMessage = 'Request failed';
      axios.get.mockRejectedValue(new Error(errorMessage));
      
      await expect(connector.listContracts()).rejects.toThrow(`Error listing contracts: ${errorMessage}`);
    });
  });
  
  describe('getContract', () => {
    it('should make a GET request to the specific contract endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'contract-123',
          title: 'Test Contract',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const contractId = 'contract-123';
      const result = await connector.getContract(contractId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/contracts/${contractId}`,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if contractId is not provided', async () => {
      await expect(connector.getContract()).rejects.toThrow('Contract ID is required');
    });
  });
  
  describe('createContract', () => {
    it('should make a POST request to the contracts endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios post response
      const mockResponse = {
        data: {
          id: 'contract-new',
          title: 'New Contract',
          status: 'draft'
        }
      };
      axios.post.mockResolvedValue(mockResponse);
      
      const contractData = {
        title: 'New Contract',
        type: 'service',
        parties: [{ name: 'Party 1', role: 'client' }],
        startDate: '2023-01-01'
      };
      
      const result = await connector.createContract(contractData);
      
      expect(axios.post).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/contracts`,
        contractData,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if required fields are missing', async () => {
      const invalidData = {
        title: 'New Contract',
        // Missing required fields: type, parties, startDate
      };
      
      await expect(connector.createContract(invalidData)).rejects.toThrow('type is required');
    });
  });
  
  describe('listPolicies', () => {
    it('should make a GET request to the policies endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          data: [
            { id: 'policy-1', title: 'Policy 1' },
            { id: 'policy-2', title: 'Policy 2' }
          ],
          pagination: {
            page: 1,
            limit: 20,
            totalItems: 2,
            totalPages: 1
          }
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const params = { status: 'active', limit: 50 };
      const result = await connector.listPolicies(params);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/policies`,
        {
          params,
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
  });
  
  describe('getPolicy', () => {
    it('should make a GET request to the specific policy endpoint', async () => {
      // Mock getAuthHeaders method
      connector.getAuthHeaders = jest.fn().mockResolvedValue({
        'Authorization': 'Bearer test-access-token'
      });
      
      // Mock axios get response
      const mockResponse = {
        data: {
          id: 'policy-123',
          title: 'Test Policy',
          description: 'Test Description'
        }
      };
      axios.get.mockResolvedValue(mockResponse);
      
      const policyId = 'policy-123';
      const result = await connector.getPolicy(policyId);
      
      expect(axios.get).toHaveBeenCalledWith(
        `${mockConfig.baseUrl}/policies/${policyId}`,
        {
          headers: {
            'Authorization': 'Bearer test-access-token',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      expect(result).toEqual(mockResponse.data);
    });
    
    it('should throw an error if policyId is not provided', async () => {
      await expect(connector.getPolicy()).rejects.toThrow('Policy ID is required');
    });
  });
});
