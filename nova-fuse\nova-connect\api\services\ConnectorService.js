/**
 * Connector Service
 * 
 * This service handles operations related to connectors.
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { ValidationError } = require('../utils/errors');

class ConnectorService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.connectorsFile = path.join(this.dataDir, 'connectors.json');
    this.ensureDataDir();
  }

  /**
   * Ensure the data directory exists
   */
  async ensureDataDir() {
    try {
      await fs.mkdir(this.dataDir, { recursive: true });
    } catch (error) {
      console.error('Error creating data directory:', error);
      throw error;
    }
  }

  /**
   * Load connectors from file
   */
  async loadConnectors() {
    try {
      const data = await fs.readFile(this.connectorsFile, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // File doesn't exist, return empty array
        return [];
      }
      console.error('Error loading connectors:', error);
      throw error;
    }
  }

  /**
   * Save connectors to file
   */
  async saveConnectors(connectors) {
    try {
      await fs.writeFile(this.connectorsFile, JSON.stringify(connectors, null, 2));
    } catch (error) {
      console.error('Error saving connectors:', error);
      throw error;
    }
  }

  /**
   * Get all connectors
   */
  async getAllConnectors() {
    return this.loadConnectors();
  }

  /**
   * Get connector by ID
   */
  async getConnectorById(id) {
    const connectors = await this.loadConnectors();
    const connector = connectors.find(c => c.id === id);
    
    if (!connector) {
      throw new Error(`Connector with ID ${id} not found`);
    }
    
    return connector;
  }

  /**
   * Create a new connector
   */
  async createConnector(connectorData) {
    if (!connectorData.name) {
      throw new ValidationError('Connector name is required');
    }
    
    const connectors = await this.loadConnectors();
    
    const newConnector = {
      id: uuidv4(),
      ...connectorData,
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      version: connectorData.version || '1.0.0',
      status: connectorData.status || 'draft',
      endpoints: connectorData.endpoints || []
    };
    
    connectors.push(newConnector);
    await this.saveConnectors(connectors);
    
    return newConnector;
  }

  /**
   * Update an existing connector
   */
  async updateConnector(id, connectorData) {
    const connectors = await this.loadConnectors();
    const index = connectors.findIndex(c => c.id === id);
    
    if (index === -1) {
      throw new Error(`Connector with ID ${id} not found`);
    }
    
    // Create a new version if version is being updated
    let versions = connectors[index].versions || [];
    if (connectorData.version && connectorData.version !== connectors[index].version) {
      versions.push({
        version: connectors[index].version,
        date: connectors[index].updated,
        author: connectorData.author || 'Unknown',
        changes: connectorData.changes || ['Version updated']
      });
    }
    
    const updatedConnector = {
      ...connectors[index],
      ...connectorData,
      updated: new Date().toISOString(),
      versions
    };
    
    connectors[index] = updatedConnector;
    await this.saveConnectors(connectors);
    
    return updatedConnector;
  }

  /**
   * Delete a connector
   */
  async deleteConnector(id) {
    const connectors = await this.loadConnectors();
    const index = connectors.findIndex(c => c.id === id);
    
    if (index === -1) {
      throw new Error(`Connector with ID ${id} not found`);
    }
    
    connectors.splice(index, 1);
    await this.saveConnectors(connectors);
    
    return { success: true, message: `Connector with ID ${id} deleted` };
  }

  /**
   * Duplicate a connector
   */
  async duplicateConnector(id) {
    const connector = await this.getConnectorById(id);
    
    const duplicatedConnector = {
      ...connector,
      id: uuidv4(),
      name: `${connector.name} (Copy)`,
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      status: 'draft'
    };
    
    const connectors = await this.loadConnectors();
    connectors.push(duplicatedConnector);
    await this.saveConnectors(connectors);
    
    return duplicatedConnector;
  }

  /**
   * Get connector versions
   */
  async getConnectorVersions(id) {
    const connector = await this.getConnectorById(id);
    return connector.versions || [];
  }
}

module.exports = ConnectorService;
