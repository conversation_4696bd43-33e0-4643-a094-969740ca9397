#!/bin/bash

# Trinity of Trust - GCP Deployment Script
# Complete deployment automation for KetherNet + NovaDNA + NovaShield
#
# Author: <PERSON>, NovaFuse Technologies
# Date: Trinity GCP Production Deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID=${PROJECT_ID:-"trinity-consciousness-prod"}
REGION=${REGION:-"us-central1"}
CLUSTER_NAME="trinity-consciousness-cluster"
TRINITY_VERSION="1.0.0-TRINITY"

echo -e "${PURPLE}🔥 TRINITY OF TRUST - GCP DEPLOYMENT STARTING${NC}"
echo -e "${CYAN}⚛️ Deploying the world's first consciousness-aware AI security platform${NC}"
echo "=================================================="
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Cluster: $CLUSTER_NAME"
echo "Version: $TRINITY_VERSION"
echo "=================================================="

# Function to print step headers
print_step() {
    echo -e "\n${BLUE}🚀 STEP $1: $2${NC}"
    echo "----------------------------------------"
}

# Function to check command success
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1 completed successfully${NC}"
    else
        echo -e "${RED}❌ $1 failed${NC}"
        exit 1
    fi
}

# Step 1: Verify Prerequisites
print_step "1" "Verifying Prerequisites"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI not found. Please install Google Cloud SDK${NC}"
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo -e "${RED}❌ kubectl not found. Please install kubectl${NC}"
    exit 1
fi

# Check if terraform is installed
if ! command -v terraform &> /dev/null; then
    echo -e "${RED}❌ terraform not found. Please install Terraform${NC}"
    exit 1
fi

# Check if docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ docker not found. Please install Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites verified${NC}"

# Step 2: Set up GCP Project
print_step "2" "Setting up GCP Project"

# Set the project
gcloud config set project $PROJECT_ID
check_success "Project configuration"

# Enable billing (if not already enabled)
echo "Ensuring billing is enabled for project..."
gcloud services enable cloudbilling.googleapis.com

# Step 3: Build Trinity Docker Images
print_step "3" "Building Trinity Docker Images"

# Build KetherNet image
echo "Building KetherNet blockchain image..."
docker build -t gcr.io/$PROJECT_ID/kethernet:$TRINITY_VERSION \
    -f ../docker/kethernet.Dockerfile \
    ../nova-hybrid-verification/
check_success "KetherNet image build"

# Build NovaDNA image
echo "Building NovaDNA identity image..."
docker build -t gcr.io/$PROJECT_ID/novadna:$TRINITY_VERSION \
    -f ../docker/novadna.Dockerfile \
    ../nova-hybrid-verification/
check_success "NovaDNA image build"

# Build NovaShield image
echo "Building NovaShield security image..."
docker build -t gcr.io/$PROJECT_ID/novashield:$TRINITY_VERSION \
    -f ../docker/novashield.Dockerfile \
    ../nova-hybrid-verification/
check_success "NovaShield image build"

# Step 4: Push Images to Container Registry
print_step "4" "Pushing Images to Container Registry"

# Configure Docker for GCR
gcloud auth configure-docker
check_success "Docker GCR configuration"

# Push images
echo "Pushing KetherNet image..."
docker push gcr.io/$PROJECT_ID/kethernet:$TRINITY_VERSION
check_success "KetherNet image push"

echo "Pushing NovaDNA image..."
docker push gcr.io/$PROJECT_ID/novadna:$TRINITY_VERSION
check_success "NovaDNA image push"

echo "Pushing NovaShield image..."
docker push gcr.io/$PROJECT_ID/novashield:$TRINITY_VERSION
check_success "NovaShield image push"

# Step 5: Deploy Infrastructure with Terraform
print_step "5" "Deploying Infrastructure with Terraform"

cd terraform

# Initialize Terraform
terraform init
check_success "Terraform initialization"

# Plan deployment
terraform plan -var="project_id=$PROJECT_ID" -var="region=$REGION"
check_success "Terraform planning"

# Apply infrastructure
echo -e "${YELLOW}⚠️ About to deploy Trinity infrastructure. This will create billable resources.${NC}"
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    terraform apply -auto-approve -var="project_id=$PROJECT_ID" -var="region=$REGION"
    check_success "Infrastructure deployment"
else
    echo "Deployment cancelled"
    exit 0
fi

cd ..

# Step 6: Configure kubectl
print_step "6" "Configuring kubectl"

# Get cluster credentials
gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION --project $PROJECT_ID
check_success "Cluster credentials"

# Verify cluster connection
kubectl cluster-info
check_success "Cluster connection verification"

# Step 7: Create Kubernetes Secrets
print_step "7" "Creating Kubernetes Secrets"

# Generate random secrets
DB_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)
ENCRYPTION_KEY=$(openssl rand -base64 32)
CONSCIOUSNESS_SALT=$(openssl rand -base64 32)

# Create secrets in Secret Manager
echo "Creating secrets in Google Secret Manager..."
echo -n "$DB_PASSWORD" | gcloud secrets versions add trinity-database-password --data-file=-
echo -n "$JWT_SECRET" | gcloud secrets versions add trinity-jwt-secret --data-file=-
echo -n "$ENCRYPTION_KEY" | gcloud secrets versions add trinity-encryption-key --data-file=-
echo -n "$CONSCIOUSNESS_SALT" | gcloud secrets versions add trinity-consciousness-salt --data-file=-

check_success "Secret creation"

# Step 6: Deploy Trinity Components
print_step "6" "Deploying Trinity Components"

# Deploy KetherNet
echo "Deploying KetherNet Blockchain..."
kubectl apply -f kubernetes/kethernet-deployment.yaml
check_success "KetherNet deployment"

# Deploy NovaDNA
echo "Deploying NovaDNA Identity Fabric..."
kubectl apply -f kubernetes/novadna-deployment.yaml
check_success "NovaDNA deployment"

# Deploy NovaShield
echo "Deploying NovaShield Security Platform..."
kubectl apply -f kubernetes/novashield-deployment.yaml
check_success "NovaShield deployment"

# Step 7: Wait for Deployments
print_step "7" "Waiting for Trinity Components"

echo "Waiting for KetherNet pods to be ready..."
kubectl wait --for=condition=ready pod -l app=kethernet -n trinity-kethernet --timeout=300s
check_success "KetherNet readiness"

echo "Waiting for NovaDNA pods to be ready..."
kubectl wait --for=condition=ready pod -l app=novadna -n trinity-novadna --timeout=300s
check_success "NovaDNA readiness"

echo "Waiting for NovaShield pods to be ready..."
kubectl wait --for=condition=ready pod -l app=novashield -n trinity-novashield --timeout=300s
check_success "NovaShield readiness"

# Step 8: Deploy Trinity Gateway
print_step "8" "Deploying Trinity Gateway"

kubectl apply -f kubernetes/trinity-gateway.yaml
check_success "Trinity Gateway deployment"

# Step 9: Run Trinity Integration Test
print_step "9" "Running Trinity Integration Test"

echo "Running Trinity integration tests..."
kubectl apply -f kubernetes/trinity-integration-test.yaml
check_success "Integration test deployment"

# Wait for test completion
echo "Waiting for integration test to complete..."
kubectl wait --for=condition=complete job/trinity-integration-test -n trinity-system --timeout=600s
check_success "Integration test completion"

# Get test results
echo -e "\n${CYAN}Integration Test Results:${NC}"
kubectl logs job/trinity-integration-test -n trinity-system

# Step 10: Display Deployment Information
print_step "10" "Deployment Complete"

echo -e "\n${GREEN}🎉 TRINITY OF TRUST DEPLOYMENT COMPLETE!${NC}"
echo "=================================================="
echo -e "${CYAN}🔗 KetherNet Blockchain:${NC} Consciousness-validated blockchain operational"
echo -e "${CYAN}🧬 NovaDNA Identity:${NC} Universal identity fabric deployed"
echo -e "${CYAN}🛡️ NovaShield Security:${NC} AI security platform active"
echo "=================================================="

# Get external IP
EXTERNAL_IP=$(kubectl get service trinity-gateway -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "Pending...")
echo -e "${BLUE}🌐 Trinity API Endpoint:${NC} https://$EXTERNAL_IP"

# Display monitoring URLs
echo -e "\n${PURPLE}📊 Monitoring & Management:${NC}"
echo "• GCP Console: https://console.cloud.google.com/kubernetes/clusters/details/$REGION/$CLUSTER_NAME?project=$PROJECT_ID"
echo "• Kubernetes Dashboard: kubectl proxy (then access via localhost)"
echo "• Grafana: (to be configured)"
echo "• Prometheus: (to be configured)"

echo -e "\n${GREEN}✅ Trinity of Trust is now operational in GCP!${NC}"
echo -e "${CYAN}⚛️ The world's first consciousness-aware AI security platform is live!${NC}"

# Save deployment info
cat > deployment-info.txt << EOF
Trinity of Trust - GCP Deployment Information
=============================================
Deployment Date: $(date)
Project ID: $PROJECT_ID
Region: $REGION
Cluster: $CLUSTER_NAME
Version: $TRINITY_VERSION
External IP: $EXTERNAL_IP

Components Deployed:
- KetherNet Blockchain (Crown Consensus)
- NovaDNA Identity Fabric
- NovaShield Security Platform

Next Steps:
1. Configure monitoring and alerting
2. Set up CI/CD pipelines
3. Configure backup and disaster recovery
4. Run comprehensive security testing
5. Begin production traffic routing
EOF

echo -e "\n${BLUE}📄 Deployment information saved to deployment-info.txt${NC}"

console.log('\n☁️ TRINITY GCP DEPLOYMENT SCRIPT COMPLETE!');
console.log('🚀 Automated deployment of complete Trinity infrastructure');
console.log('🔧 Docker image building and container registry push');
console.log('🏗️ Terraform infrastructure provisioning');
console.log('⚛️ Kubernetes deployment with consciousness validation');
console.log('🛡️ Security configuration and secret management');
console.log('📊 Monitoring and verification setup');
console.log('🎉 Ready to deploy Trinity of Trust to production!');
