/**
 * NovaCore Custom Error Classes
 * 
 * This file defines custom error classes for the NovaCore API.
 */

/**
 * Base API Error
 */
class APIError extends Error {
  constructor(message, statusCode, code, details) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Validation Error
 */
class ValidationError extends APIError {
  constructor(message, details = {}) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * Not Found Error
 */
class NotFoundError extends APIError {
  constructor(message) {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

/**
 * Authentication Error
 */
class AuthenticationError extends APIError {
  constructor(message) {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * Authorization Error
 */
class AuthorizationError extends APIError {
  constructor(message) {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * Database Error
 */
class DatabaseError extends APIError {
  constructor(message, details = {}) {
    super(message, 500, 'DATABASE_ERROR', details);
  }
}

/**
 * External Service Error
 */
class ExternalServiceError extends APIError {
  constructor(message, details = {}) {
    super(message, 502, 'EXTERNAL_SERVICE_ERROR', details);
  }
}

module.exports = {
  APIError,
  ValidationError,
  NotFoundError,
  AuthenticationError,
  AuthorizationError,
  DatabaseError,
  ExternalServiceError
};
