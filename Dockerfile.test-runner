# Use Python 3.10 base image
FROM python:3.10-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    NOVACORTEX_URL=http://novacortex:3010

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy test requirements
COPY tests/novacortex/requirements-test.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements-test.txt

# Copy test files
COPY tests /app/tests

# Create directory for test reports
RUN mkdir -p /app/reports

# Health check for test runner
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Default command to run tests
CMD ["pytest", "/app/tests/novacortex/test_integration.py", "-v", "--alluredir=/app/reports/allure-results"]
