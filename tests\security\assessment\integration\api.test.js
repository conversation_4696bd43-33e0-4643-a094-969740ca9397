const request = require('supertest');
const express = require('express');
const router = require('../../../../apis/security/assessment/routes');
const models = require('../../../../apis/security/assessment/models');

// Mock the models
jest.mock('../../../../apis/security/assessment/models', () => ({
  securityAssessments: [
    {
      id: 'sa-12345678',
      name: 'Annual Security Assessment 2023',
      description: 'Comprehensive security assessment of all systems and applications',
      type: 'internal',
      status: 'completed',
      scope: {
        systems: ['CRM', 'ERP', 'Website'],
        applications: ['Mobile App', 'Admin Portal'],
        networks: ['Corporate Network', 'Guest WiFi']
      },
      methodology: 'NIST CSF',
      startDate: '2023-01-15',
      endDate: '2023-02-28',
      findings: [
        {
          id: 'finding-12345',
          title: 'Weak Password Policy',
          description: 'Password policy does not enforce sufficient complexity',
          severity: 'high',
          category: 'authentication',
          status: 'remediated',
          affectedSystems: ['CRM', 'ERP'],
          remediationPlan: 'Implement stronger password policy with minimum 12 characters',
          remediationDate: '2023-03-15',
          assignedTo: 'Security Team'
        },
        {
          id: 'finding-67890',
          title: 'Outdated SSL Certificates',
          description: 'Several SSL certificates are using outdated encryption standards',
          severity: 'medium',
          category: 'encryption',
          status: 'in-progress',
          affectedSystems: ['Website'],
          remediationPlan: 'Update all certificates to use TLS 1.3',
          assignedTo: 'Infrastructure Team'
        }
      ],
      assessors: ['John Doe', 'Jane Smith'],
      stakeholders: ['IT Director', 'CISO'],
      attachments: [
        {
          id: 'att-12345',
          name: 'Final Report',
          type: 'pdf',
          url: 'https://example.com/reports/security-assessment-2023.pdf',
          uploadedBy: 'John Doe',
          uploadedAt: '2023-03-01T00:00:00Z'
        }
      ],
      createdAt: '2023-01-10T00:00:00Z',
      updatedAt: '2023-03-01T00:00:00Z'
    },
    {
      id: 'sa-87654321',
      name: 'Vendor Security Assessment - CloudProvider',
      description: 'Security assessment of CloudProvider services',
      type: 'vendor',
      status: 'in-progress',
      scope: {
        systems: ['Cloud Storage', 'Cloud Compute'],
        applications: ['Management Console'],
        networks: ['Cloud Network']
      },
      methodology: 'ISO 27001',
      startDate: '2023-04-01',
      findings: [],
      assessors: ['Jane Smith'],
      stakeholders: ['Procurement Manager', 'CISO'],
      attachments: [],
      createdAt: '2023-03-15T00:00:00Z',
      updatedAt: '2023-04-01T00:00:00Z'
    }
  ],
  assessmentTemplates: [
    {
      id: 'template-12345',
      name: 'Internal Security Assessment Template',
      description: 'Template for internal security assessments',
      type: 'internal',
      methodology: 'NIST CSF',
      sections: [
        {
          title: 'Identify',
          description: 'Asset management, business environment, governance, risk assessment, risk management strategy',
          questions: [
            {
              id: 'q-12345',
              text: 'Are all assets inventoried and tracked?',
              type: 'yes-no',
              required: true
            },
            {
              id: 'q-23456',
              text: 'How is the asset inventory maintained?',
              type: 'text',
              required: false
            }
          ]
        },
        {
          title: 'Protect',
          description: 'Access control, awareness and training, data security, protective technology',
          questions: [
            {
              id: 'q-34567',
              text: 'Is multi-factor authentication implemented for all remote access?',
              type: 'yes-no',
              required: true
            }
          ]
        }
      ],
      createdAt: '2022-12-01T00:00:00Z',
      updatedAt: '2022-12-01T00:00:00Z'
    }
  ]
}));

// Create a test Express app
const app = express();
app.use(express.json());
app.use('/security/assessment', router);

beforeEach(() => {
  jest.clearAllMocks();
});

describe('Security Assessment API Integration Tests', () => {
  describe('GET /security/assessment', () => {
    it('should return all assessments with default pagination', async () => {
      const response = await request(app).get('/security/assessment');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toEqual({
        total: 2,
        page: 1,
        limit: 10,
        pages: 1
      });
    });

    it('should filter assessments by type', async () => {
      const response = await request(app).get('/security/assessment?type=internal');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].type).toBe('internal');
    });

    it('should filter assessments by status', async () => {
      const response = await request(app).get('/security/assessment?status=in-progress');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('in-progress');
    });
  });

  describe('GET /security/assessment/:id', () => {
    it('should return a specific assessment by ID', async () => {
      const response = await request(app).get('/security/assessment/sa-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('sa-12345678');
      expect(response.body.data.name).toBe('Annual Security Assessment 2023');
    });

    it('should return 404 if assessment not found', async () => {
      const response = await request(app).get('/security/assessment/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/assessment', () => {
    it('should create a new assessment', async () => {
      const newAssessment = {
        name: 'Application Security Assessment',
        description: 'Security assessment of the new mobile application',
        type: 'application',
        status: 'planned',
        scope: {
          applications: ['Mobile App v2.0']
        },
        methodology: 'OWASP MASVS',
        startDate: '2023-06-01',
        assessors: ['Jane Smith'],
        stakeholders: ['Mobile App Team Lead', 'CISO']
      };

      const response = await request(app)
        .post('/security/assessment')
        .send(newAssessment);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Security assessment created successfully');
      expect(response.body.data.name).toBe('Application Security Assessment');
      expect(response.body.data.type).toBe('application');
    });

    it('should return 400 for invalid input', async () => {
      const invalidAssessment = {
        // Missing required fields
        description: 'Invalid assessment'
      };

      const response = await request(app)
        .post('/security/assessment')
        .send(invalidAssessment);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /security/assessment/:id', () => {
    it('should update an existing assessment', async () => {
      const updatedAssessment = {
        name: 'Updated Assessment Name',
        status: 'completed',
        endDate: '2023-04-15'
      };

      const response = await request(app)
        .put('/security/assessment/sa-87654321')
        .send(updatedAssessment);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Security assessment updated successfully');
      expect(response.body.data.name).toBe('Updated Assessment Name');
      expect(response.body.data.status).toBe('completed');
      expect(response.body.data.endDate).toBe('2023-04-15');
    });

    it('should return 404 if assessment not found', async () => {
      const response = await request(app)
        .put('/security/assessment/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /security/assessment/:id', () => {
    it('should delete an existing assessment', async () => {
      const response = await request(app).delete('/security/assessment/sa-12345678');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Security assessment deleted successfully');
    });

    it('should return 404 if assessment not found', async () => {
      const response = await request(app).delete('/security/assessment/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/assessment/:id/findings', () => {
    it('should return findings for a specific assessment', async () => {
      const response = await request(app).get('/security/assessment/sa-12345678/findings');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0].id).toBe('finding-12345');
    });

    it('should return 404 if assessment not found', async () => {
      const response = await request(app).get('/security/assessment/non-existent-id/findings');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/assessment/:id/findings/:findingId', () => {
    it('should return a specific finding by ID', async () => {
      const response = await request(app).get('/security/assessment/sa-12345678/findings/finding-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('finding-12345');
      expect(response.body.data.title).toBe('Weak Password Policy');
    });

    it('should return 404 if finding not found', async () => {
      const response = await request(app).get('/security/assessment/sa-12345678/findings/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/assessment/:id/findings', () => {
    it('should add a new finding to an assessment', async () => {
      const newFinding = {
        title: 'Insecure API Endpoints',
        description: 'Several API endpoints lack proper authentication',
        severity: 'high',
        category: 'authentication',
        status: 'open',
        affectedSystems: ['Website'],
        remediationPlan: 'Implement OAuth 2.0 for all API endpoints',
        assignedTo: 'Development Team'
      };

      const response = await request(app)
        .post('/security/assessment/sa-87654321/findings')
        .send(newFinding);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Finding added successfully');
      expect(response.body.data.title).toBe('Insecure API Endpoints');
      expect(response.body.data.severity).toBe('high');
    });

    it('should return 400 for invalid input', async () => {
      const invalidFinding = {
        // Missing required fields
        description: 'Invalid finding'
      };

      const response = await request(app)
        .post('/security/assessment/sa-87654321/findings')
        .send(invalidFinding);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /security/assessment/:id/findings/:findingId', () => {
    it('should update an existing finding', async () => {
      const updatedFinding = {
        title: 'Updated Finding Title',
        status: 'remediated',
        remediationDate: '2023-04-15'
      };

      const response = await request(app)
        .put('/security/assessment/sa-12345678/findings/finding-67890')
        .send(updatedFinding);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Finding updated successfully');
      expect(response.body.data.title).toBe('Updated Finding Title');
      expect(response.body.data.status).toBe('remediated');
      expect(response.body.data.remediationDate).toBe('2023-04-15');
    });

    it('should return 404 if finding not found', async () => {
      const response = await request(app)
        .put('/security/assessment/sa-12345678/findings/non-existent-id')
        .send({ title: 'Updated Title' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /security/assessment/:id/findings/:findingId', () => {
    it('should remove a finding from an assessment', async () => {
      const response = await request(app).delete('/security/assessment/sa-12345678/findings/finding-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Finding removed successfully');
    });

    it('should return 404 if finding not found', async () => {
      const response = await request(app).delete('/security/assessment/sa-12345678/findings/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/assessment/:id/attachments', () => {
    it('should return attachments for a specific assessment', async () => {
      const response = await request(app).get('/security/assessment/sa-12345678/attachments');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].id).toBe('att-12345');
    });

    it('should return 404 if assessment not found', async () => {
      const response = await request(app).get('/security/assessment/non-existent-id/attachments');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/assessment/:id/attachments', () => {
    it('should add a new attachment to an assessment', async () => {
      const newAttachment = {
        name: 'Vulnerability Scan Results',
        type: 'pdf',
        url: 'https://example.com/reports/vulnerability-scan.pdf'
      };

      const response = await request(app)
        .post('/security/assessment/sa-87654321/attachments')
        .send(newAttachment);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Attachment added successfully');
      expect(response.body.data.name).toBe('Vulnerability Scan Results');
      expect(response.body.data.type).toBe('pdf');
    });

    it('should return 400 for invalid input', async () => {
      const invalidAttachment = {
        // Missing required fields
        type: 'pdf'
      };

      const response = await request(app)
        .post('/security/assessment/sa-87654321/attachments')
        .send(invalidAttachment);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('DELETE /security/assessment/:id/attachments/:attachmentId', () => {
    it('should remove an attachment from an assessment', async () => {
      const response = await request(app).delete('/security/assessment/sa-12345678/attachments/att-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Attachment removed successfully');
    });

    it('should return 404 if attachment not found', async () => {
      const response = await request(app).delete('/security/assessment/sa-12345678/attachments/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('GET /security/assessment/templates', () => {
    it('should return all assessment templates', async () => {
      const response = await request(app).get('/security/assessment/templates');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].name).toBe('Internal Security Assessment Template');
    });
  });

  describe('GET /security/assessment/templates/:id', () => {
    it('should return a specific assessment template by ID', async () => {
      const response = await request(app).get('/security/assessment/templates/template-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data.id).toBe('template-12345');
      expect(response.body.data.name).toBe('Internal Security Assessment Template');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app).get('/security/assessment/templates/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('POST /security/assessment/templates', () => {
    it('should create a new assessment template', async () => {
      const newTemplate = {
        name: 'Vendor Security Assessment Template',
        description: 'Template for vendor security assessments',
        type: 'vendor',
        methodology: 'ISO 27001',
        sections: [
          {
            title: 'Vendor Information',
            description: 'Basic information about the vendor',
            questions: [
              {
                text: 'Vendor name and contact information',
                type: 'text',
                required: true
              },
              {
                text: 'Services provided',
                type: 'text',
                required: true
              }
            ]
          },
          {
            title: 'Security Controls',
            description: 'Assessment of vendor security controls',
            questions: [
              {
                text: 'Does the vendor have a documented security policy?',
                type: 'yes-no',
                required: true
              }
            ]
          }
        ]
      };

      const response = await request(app)
        .post('/security/assessment/templates')
        .send(newTemplate);
      
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Assessment template created successfully');
      expect(response.body.data.name).toBe('Vendor Security Assessment Template');
      expect(response.body.data.type).toBe('vendor');
    });

    it('should return 400 for invalid input', async () => {
      const invalidTemplate = {
        // Missing required fields
        description: 'Invalid template'
      };

      const response = await request(app)
        .post('/security/assessment/templates')
        .send(invalidTemplate);
      
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Bad Request');
    });
  });

  describe('PUT /security/assessment/templates/:id', () => {
    it('should update an existing assessment template', async () => {
      const updatedTemplate = {
        name: 'Updated Template Name',
        sections: [
          {
            title: 'New Section',
            description: 'New section description',
            questions: [
              {
                text: 'New question',
                type: 'text',
                required: false
              }
            ]
          }
        ]
      };

      const response = await request(app)
        .put('/security/assessment/templates/template-12345')
        .send(updatedTemplate);
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('message', 'Assessment template updated successfully');
      expect(response.body.data.name).toBe('Updated Template Name');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app)
        .put('/security/assessment/templates/non-existent-id')
        .send({ name: 'Updated Name' });
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });

  describe('DELETE /security/assessment/templates/:id', () => {
    it('should delete an existing assessment template', async () => {
      const response = await request(app).delete('/security/assessment/templates/template-12345');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Assessment template deleted successfully');
    });

    it('should return 404 if template not found', async () => {
      const response = await request(app).delete('/security/assessment/templates/non-existent-id');
      
      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error', 'Not Found');
    });
  });
});
