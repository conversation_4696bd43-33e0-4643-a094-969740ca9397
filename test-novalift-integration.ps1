# test-novalift-integration.ps1
# NovaLift Integration Test Suite
# Validates complete NovaFuse stack functionality

param(
    [string]$InstallPath = "C:\NovaFuse",
    [switch]$Verbose = $false,
    [switch]$SkipServices = $false
)

# Test configuration
$TestConfig = @{
    Name = "NovaLift Integration Test Suite"
    Version = "1.0.0"
    InstallPath = $InstallPath
    Verbose = $Verbose
    SkipServices = $SkipServices
}

# Color functions
function Write-TestInfo {
    param([string]$Message)
    Write-Host "🔍 [TEST] $Message" -ForegroundColor Cyan
}

function Write-TestSuccess {
    param([string]$Message)
    Write-Host "✅ [TEST] $Message" -ForegroundColor Green
}

function Write-TestWarning {
    param([string]$Message)
    Write-Host "⚠️ [TEST] $Message" -ForegroundColor Yellow
}

function Write-TestError {
    param([string]$Message)
    Write-Host "❌ [TEST] $Message" -ForegroundColor Red
}

# Test results tracking
$TestResults = @{
    Total = 0
    Passed = 0
    Failed = 0
    Warnings = 0
    Tests = @()
}

function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Message,
        [string]$Details = ""
    )
    
    $TestResults.Total++
    
    switch ($Status) {
        "PASS" { 
            $TestResults.Passed++
            Write-TestSuccess "$TestName: $Message"
        }
        "FAIL" { 
            $TestResults.Failed++
            Write-TestError "$TestName: $Message"
        }
        "WARN" { 
            $TestResults.Warnings++
            Write-TestWarning "$TestName: $Message"
        }
    }
    
    $TestResults.Tests += @{
        Name = $TestName
        Status = $Status
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
}

# Banner
function Show-TestBanner {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Magenta
    Write-Host "║                           NOVALIFT INTEGRATION TEST SUITE                   ║" -ForegroundColor Magenta
    Write-Host "║                        NovaFuse Coherence Operating System                   ║" -ForegroundColor Magenta
    Write-Host "║                                Version 1.0.0                                ║" -ForegroundColor Magenta
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Magenta
    Write-Host ""
}

# Test 1: Directory Structure
function Test-DirectoryStructure {
    Write-TestInfo "Testing directory structure..."
    
    $requiredDirs = @(
        "$InstallPath",
        "$InstallPath\NovaCore",
        "$InstallPath\NovaAgent",
        "$InstallPath\NovaBridge",
        "$InstallPath\NovaConsole",
        "$InstallPath\Config",
        "$InstallPath\Logs"
    )
    
    $missingDirs = @()
    foreach ($dir in $requiredDirs) {
        if (-not (Test-Path $dir)) {
            $missingDirs += $dir
        }
    }
    
    if ($missingDirs.Count -eq 0) {
        Add-TestResult "Directory Structure" "PASS" "All required directories exist"
    } else {
        Add-TestResult "Directory Structure" "FAIL" "Missing directories: $($missingDirs -join ', ')"
    }
}

# Test 2: Configuration Files
function Test-ConfigurationFiles {
    Write-TestInfo "Testing configuration files..."
    
    $configFiles = @(
        "$InstallPath\Config\novafuse-config.json",
        "$InstallPath\Config\novalift-config.json"
    )
    
    $missingConfigs = @()
    $invalidConfigs = @()
    
    foreach ($configFile in $configFiles) {
        if (-not (Test-Path $configFile)) {
            $missingConfigs += $configFile
        } else {
            try {
                $content = Get-Content $configFile -Raw | ConvertFrom-Json
                if ($Verbose) {
                    Write-TestInfo "Config valid: $(Split-Path $configFile -Leaf)"
                }
            } catch {
                $invalidConfigs += $configFile
            }
        }
    }
    
    if ($missingConfigs.Count -eq 0 -and $invalidConfigs.Count -eq 0) {
        Add-TestResult "Configuration Files" "PASS" "All configuration files valid"
    } elseif ($missingConfigs.Count -gt 0) {
        Add-TestResult "Configuration Files" "FAIL" "Missing configs: $($missingConfigs -join ', ')"
    } else {
        Add-TestResult "Configuration Files" "FAIL" "Invalid configs: $($invalidConfigs -join ', ')"
    }
}

# Test 3: NovaAgent Executable
function Test-NovaAgent {
    Write-TestInfo "Testing NovaAgent executable..."
    
    $novaAgentPath = "$InstallPath\NovaAgent\nova-agent.exe"
    
    if (-not (Test-Path $novaAgentPath)) {
        Add-TestResult "NovaAgent Executable" "FAIL" "NovaAgent executable not found"
        return
    }
    
    # Test help command
    try {
        $helpOutput = & $novaAgentPath --help 2>&1
        if ($helpOutput -match "NovaAgent") {
            Add-TestResult "NovaAgent Executable" "PASS" "NovaAgent executable responds correctly"
        } else {
            Add-TestResult "NovaAgent Executable" "WARN" "NovaAgent executable found but help output unexpected"
        }
    } catch {
        Add-TestResult "NovaAgent Executable" "FAIL" "NovaAgent executable error: $($_.Exception.Message)"
    }
}

# Test 4: Port Availability
function Test-PortAvailability {
    Write-TestInfo "Testing port availability..."
    
    $requiredPorts = @(8000, 8001, 3000)
    $unavailablePorts = @()
    
    foreach ($port in $requiredPorts) {
        try {
            $listener = [System.Net.Sockets.TcpListener]::new([System.Net.IPAddress]::Any, $port)
            $listener.Start()
            $listener.Stop()
            if ($Verbose) {
                Write-TestInfo "Port $port: Available"
            }
        } catch {
            $unavailablePorts += $port
        }
    }
    
    if ($unavailablePorts.Count -eq 0) {
        Add-TestResult "Port Availability" "PASS" "All required ports available"
    } else {
        Add-TestResult "Port Availability" "WARN" "Ports in use: $($unavailablePorts -join ', ')"
    }
}

# Test 5: Dependencies
function Test-Dependencies {
    Write-TestInfo "Testing system dependencies..."
    
    $dependencies = @{
        "Node.js" = "node"
        "Python" = "python"
        "PowerShell" = "powershell"
    }
    
    $missingDeps = @()
    
    foreach ($dep in $dependencies.GetEnumerator()) {
        try {
            $version = & $dep.Value --version 2>$null
            if ($version) {
                if ($Verbose) {
                    Write-TestInfo "$($dep.Key): $version"
                }
            } else {
                $missingDeps += $dep.Key
            }
        } catch {
            $missingDeps += $dep.Key
        }
    }
    
    if ($missingDeps.Count -eq 0) {
        Add-TestResult "Dependencies" "PASS" "All dependencies available"
    } else {
        Add-TestResult "Dependencies" "WARN" "Missing dependencies: $($missingDeps -join ', ')"
    }
}

# Test 6: NovaCore Components
function Test-NovaCoreComponents {
    Write-TestInfo "Testing NovaCore components..."
    
    $novaCoreSource = "nhetx-castl-alpha"
    $novaCoreTarget = "$InstallPath\NovaCore"
    
    if (Test-Path $novaCoreSource) {
        if (Test-Path "$novaCoreTarget\package.json") {
            Add-TestResult "NovaCore Components" "PASS" "NovaCore components installed"
        } else {
            Add-TestResult "NovaCore Components" "WARN" "NovaCore components partially installed"
        }
    } else {
        Add-TestResult "NovaCore Components" "WARN" "NovaCore source not available for testing"
    }
}

# Test 7: NovaBridge API
function Test-NovaBridgeAPI {
    Write-TestInfo "Testing NovaBridge API components..."
    
    $novaBridgeSource = "aeonix-divine-api"
    $novaBridgeTarget = "$InstallPath\NovaBridge"
    
    if (Test-Path $novaBridgeSource) {
        if (Test-Path "$novaBridgeTarget\main.py") {
            Add-TestResult "NovaBridge API" "PASS" "NovaBridge API components installed"
        } else {
            Add-TestResult "NovaBridge API" "WARN" "NovaBridge API components partially installed"
        }
    } else {
        Add-TestResult "NovaBridge API" "WARN" "NovaBridge source not available for testing"
    }
}

# Test 8: NovaConsole Dashboard
function Test-NovaConsole {
    Write-TestInfo "Testing NovaConsole dashboard..."
    
    $novaConsoleSource = "chaeonix-divine-dashboard"
    $novaConsoleTarget = "$InstallPath\NovaConsole"
    
    if (Test-Path $novaConsoleSource) {
        if (Test-Path "$novaConsoleTarget\package.json") {
            Add-TestResult "NovaConsole Dashboard" "PASS" "NovaConsole dashboard installed"
        } else {
            Add-TestResult "NovaConsole Dashboard" "WARN" "NovaConsole dashboard partially installed"
        }
    } else {
        Add-TestResult "NovaConsole Dashboard" "WARN" "NovaConsole source not available for testing"
    }
}

# Test 9: Service Integration (if not skipped)
function Test-ServiceIntegration {
    if ($SkipServices) {
        Add-TestResult "Service Integration" "WARN" "Service integration tests skipped"
        return
    }
    
    Write-TestInfo "Testing service integration..."
    
    # This would test actual service startup in a real environment
    # For now, we'll just verify the service configuration exists
    
    if (Test-Path "$InstallPath\NovaLift\install-service.ps1") {
        Add-TestResult "Service Integration" "PASS" "Service installation script available"
    } else {
        Add-TestResult "Service Integration" "WARN" "Service installation script not found"
    }
}

# Test 10: End-to-End Validation
function Test-EndToEndValidation {
    Write-TestInfo "Performing end-to-end validation..."
    
    $criticalTests = $TestResults.Tests | Where-Object { $_.Status -eq "FAIL" }
    $warningTests = $TestResults.Tests | Where-Object { $_.Status -eq "WARN" }
    
    if ($criticalTests.Count -eq 0) {
        if ($warningTests.Count -eq 0) {
            Add-TestResult "End-to-End Validation" "PASS" "All systems ready for deployment"
        } else {
            Add-TestResult "End-to-End Validation" "WARN" "System ready with minor issues"
        }
    } else {
        Add-TestResult "End-to-End Validation" "FAIL" "Critical issues prevent deployment"
    }
}

# Generate test report
function Show-TestReport {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Blue
    Write-Host "║                              TEST RESULTS SUMMARY                           ║" -ForegroundColor Blue
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Blue
    Write-Host ""
    
    Write-Host "📊 Test Statistics:" -ForegroundColor Yellow
    Write-Host "   Total Tests: $($TestResults.Total)"
    Write-Host "   Passed: $($TestResults.Passed)" -ForegroundColor Green
    Write-Host "   Failed: $($TestResults.Failed)" -ForegroundColor Red
    Write-Host "   Warnings: $($TestResults.Warnings)" -ForegroundColor Yellow
    Write-Host ""
    
    $passRate = [math]::Round(($TestResults.Passed / $TestResults.Total) * 100, 1)
    Write-Host "✅ Pass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } elseif ($passRate -ge 60) { "Yellow" } else { "Red" })
    
    if ($TestResults.Failed -eq 0) {
        Write-Host ""
        Write-Host "🎉 NovaLift Integration Test: SUCCESS" -ForegroundColor Green
        Write-Host "🚀 System ready for deployment!" -ForegroundColor Green
    } else {
        Write-Host ""
        Write-Host "⚠️ NovaLift Integration Test: ISSUES DETECTED" -ForegroundColor Yellow
        Write-Host "🔧 Review failed tests before deployment" -ForegroundColor Yellow
    }
    
    Write-Host ""
}

# Main test execution
function Start-IntegrationTests {
    Show-TestBanner
    
    Write-TestInfo "Starting NovaLift integration tests..."
    Write-TestInfo "Install Path: $InstallPath"
    Write-TestInfo "Verbose Mode: $Verbose"
    Write-TestInfo "Skip Services: $SkipServices"
    Write-Host ""
    
    # Execute all tests
    Test-DirectoryStructure
    Test-ConfigurationFiles
    Test-NovaAgent
    Test-PortAvailability
    Test-Dependencies
    Test-NovaCoreComponents
    Test-NovaBridgeAPI
    Test-NovaConsole
    Test-ServiceIntegration
    Test-EndToEndValidation
    
    # Show results
    Show-TestReport
    
    # Return exit code based on results
    if ($TestResults.Failed -eq 0) {
        return 0
    } else {
        return 1
    }
}

# Execute tests
$exitCode = Start-IntegrationTests
exit $exitCode
