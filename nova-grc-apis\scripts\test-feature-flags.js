/**
 * Feature Flag Test Script
 *
 * This script tests the feature flag middleware for API routes.
 * It verifies that API routes are correctly implementing feature flag checks
 * by making requests to each endpoint with different product tiers.
 */

const axios = require('axios');
const chalk = require('chalk');

// Define product tiers to test
const productTiers = ['novaprime', 'novacore', 'novashield', 'novalearn'];

// Define API endpoints to test
const endpoints = [
  { path: '/api/privacy/management/processing-activities', feature: 'privacy-management-processing-activities' },
  { path: '/api/privacy/management/subject-requests', feature: 'privacy-management-subject-requests' },
  { path: '/api/privacy/management/consent', feature: 'privacy-management-consent-records' },
  { path: '/api/privacy/management/privacy-notices', feature: 'privacy-management-privacy-notices' },
  { path: '/api/privacy/management/data-breaches', feature: 'privacy-management-data-breaches' },
  { path: '/api/privacy/management/impact-assessment', feature: 'privacy-management-impact-assessment' },
  { path: '/api/compliance/regulatory/frameworks', feature: 'compliance-regulatory-frameworks' },
  { path: '/api/compliance/regulatory/requirements', feature: 'compliance-regulatory-requirements' },
  { path: '/api/security/assessment/assessments', feature: 'security-assessment-assessments' },
  // Add more endpoints as needed
];

// Define expected access based on feature flags
const featureAccess = {
  'privacy-management-processing-activities': { novaprime: true, novacore: true, novashield: false, novalearn: false },
  'privacy-management-subject-requests': { novaprime: true, novacore: true, novashield: false, novalearn: false },
  'privacy-management-consent-records': { novaprime: true, novacore: true, novashield: false, novalearn: false },
  'privacy-management-privacy-notices': { novaprime: true, novacore: true, novashield: false, novalearn: false },
  'privacy-management-data-breaches': { novaprime: true, novacore: true, novashield: true, novalearn: false },
  'privacy-management-impact-assessment': { novaprime: true, novacore: false, novashield: false, novalearn: false },
  'compliance-regulatory-frameworks': { novaprime: true, novacore: true, novashield: true, novalearn: true },
  'compliance-regulatory-requirements': { novaprime: true, novacore: true, novashield: true, novalearn: true },
  'security-assessment-assessments': { novaprime: true, novacore: false, novashield: true, novalearn: false },
  // Add more features as needed
};

// Base URL for API requests
const baseUrl = process.env.API_URL || 'http://localhost:3001';

// Check if the API server is running
async function checkApiServer() {
  try {
    await axios.get(`${baseUrl}/health`, { timeout: 5000 });
    return true;
  } catch (error) {
    console.log(chalk.red(`API server not running at ${baseUrl}. Please start the API server before running this script.`));
    return false;
  }
}

// Test function
async function testFeatureFlags() {
  console.log(chalk.blue('Testing Feature Flag Access for API Routes\n'));

  // Check if the API server is running
  const isServerRunning = await checkApiServer();
  if (!isServerRunning) {
    console.log(chalk.yellow('\nPlease start the API server and try again.'));
    return;
  }

  let passCount = 0;
  let failCount = 0;

  for (const tier of productTiers) {
    console.log(chalk.yellow(`\nTesting with product tier: ${tier.toUpperCase()}\n`));

    for (const endpoint of endpoints) {
      const { path, feature } = endpoint;
      const expectedAccess = featureAccess[feature][tier];
      const url = `${baseUrl}${path}`;

      try {
        const response = await axios.get(url, {
          headers: {
            'Authorization': 'Bearer test-token',
            'X-Product-Tier': tier
          },
          validateStatus: (status) => status < 500 // Don't throw for 4xx errors
        });

        const hasAccess = response.status === 200;

        if (hasAccess === expectedAccess) {
          console.log(chalk.green(`✓ ${path} - Access: ${hasAccess} (Expected: ${expectedAccess})`));
          passCount++;
        } else {
          console.log(chalk.red(`✗ ${path} - Access: ${hasAccess} (Expected: ${expectedAccess})`));
          console.log(chalk.gray(`  Status: ${response.status}`));
          console.log(chalk.gray(`  Response: ${JSON.stringify(response.data)}`));
          failCount++;
        }
      } catch (error) {
        console.log(chalk.red(`✗ ${path} - Error: ${error.message}`));
        failCount++;
      }
    }
  }

  console.log(chalk.blue(`\nTest Results: ${passCount} passed, ${failCount} failed`));

  if (failCount > 0) {
    console.log(chalk.yellow('\nSome tests failed. This could be due to:'));
    console.log(chalk.yellow('1. Missing feature flag middleware on some routes'));
    console.log(chalk.yellow('2. Incorrect feature flag configuration'));
    console.log(chalk.yellow('3. API server not running correctly'));
    console.log(chalk.yellow('\nCheck the documentation for more information on implementing feature flags.'));
  } else {
    console.log(chalk.green('\nAll tests passed! Feature flags are correctly implemented.'));
  }
}

// Run the tests
testFeatureFlags().catch((error) => {
  console.error(chalk.red(`Error running tests: ${error.message}`));
  process.exit(1);
});
