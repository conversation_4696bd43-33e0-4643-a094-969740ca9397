/**
 * Security Components Example
 *
 * This example demonstrates how to use the security components
 * for the Finite Universe Principle defense system, including
 * RBAC, audit logging, and secure storage.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Security components
  createRBAC,
  createAuditLogger,
  createSecureStorage,
  createSecurityComponents
} = require('../src/quantum/finite-universe-principle');

/**
 * Example 1: Role-Based Access Control (RBAC)
 * 
 * This example demonstrates how to use the RBAC component.
 */
async function example1() {
  console.log('\n=== Example 1: Role-Based Access Control (RBAC) ===\n');

  // Create RBAC
  const rbac = createRBAC({
    enableLogging: true,
    defaultRole: 'guest',
    superRole: 'admin'
  });

  // Register event listeners
  rbac.on('role-created', (data) => {
    console.log('Role created:', data.roleId);
  });

  rbac.on('permission-assigned', (data) => {
    console.log('Permission assigned:', data.permissionId, 'to role:', data.roleId);
  });

  // Create custom roles
  rbac.createRole('analyst', 'Data analyst with read-only access');
  rbac.createRole('operator', 'System operator with operational access');

  // Create custom permissions
  rbac.createPermission('analyze', 'Analyze data');
  rbac.createPermission('operate', 'Operate system');
  rbac.createPermission('configure', 'Configure system');

  // Assign permissions to roles
  rbac.assignPermissionToRole('analyst', 'read');
  rbac.assignPermissionToRole('analyst', 'analyze');
  rbac.assignPermissionToRole('operator', 'read');
  rbac.assignPermissionToRole('operator', 'write');
  rbac.assignPermissionToRole('operator', 'operate');
  rbac.assignPermissionToRole('operator', 'configure');

  // Assign roles to users
  rbac.assignRoleToUser('user1', 'guest');
  rbac.assignRoleToUser('user2', 'analyst');
  rbac.assignRoleToUser('user3', 'operator');
  rbac.assignRoleToUser('admin1', 'admin');

  // Check permissions
  console.log('\nPermission checks:');
  console.log('user1 has read permission:', rbac.hasPermission('user1', 'read'));
  console.log('user1 has write permission:', rbac.hasPermission('user1', 'write'));
  console.log('user2 has read permission:', rbac.hasPermission('user2', 'read'));
  console.log('user2 has analyze permission:', rbac.hasPermission('user2', 'analyze'));
  console.log('user2 has write permission:', rbac.hasPermission('user2', 'write'));
  console.log('user3 has operate permission:', rbac.hasPermission('user3', 'operate'));
  console.log('user3 has admin permission:', rbac.hasPermission('user3', 'admin'));
  console.log('admin1 has admin permission:', rbac.hasPermission('admin1', 'admin'));

  // Get user roles
  console.log('\nUser roles:');
  console.log('user1 roles:', rbac.getUserRoles('user1'));
  console.log('user2 roles:', rbac.getUserRoles('user2'));
  console.log('user3 roles:', rbac.getUserRoles('user3'));
  console.log('admin1 roles:', rbac.getUserRoles('admin1'));

  // Get role permissions
  console.log('\nRole permissions:');
  console.log('guest permissions:', rbac.getRolePermissions('guest'));
  console.log('analyst permissions:', rbac.getRolePermissions('analyst'));
  console.log('operator permissions:', rbac.getRolePermissions('operator'));
  console.log('admin permissions:', rbac.getRolePermissions('admin'));

  // Dispose RBAC
  rbac.dispose();
}

/**
 * Example 2: Audit Logging
 * 
 * This example demonstrates how to use the audit logger component.
 */
async function example2() {
  console.log('\n=== Example 2: Audit Logging ===\n');

  // Create audit logger
  const auditLogger = createAuditLogger({
    enableLogging: true,
    enableConsole: true,
    enableFile: true,
    logLevel: 'info',
    logFormat: 'json',
    logPath: './logs',
    logFilename: 'audit.log'
  });

  // Register event listeners
  auditLogger.on('log', (entry) => {
    console.log('Log entry created:', entry.id);
  });

  // Log events
  console.log('\nLogging events:');
  
  auditLogger.info('security', 'User logged in', { username: 'user1' }, 'user1');
  auditLogger.info('security', 'Permission granted', { permission: 'read', resource: 'data1' }, 'user1');
  auditLogger.warn('security', 'Failed login attempt', { username: 'user2', attempts: 3 }, 'system');
  auditLogger.error('security', 'Access denied', { permission: 'write', resource: 'data1' }, 'user2');
  
  // Log different categories
  auditLogger.info('system', 'System started', { version: '1.0.0' }, 'system');
  auditLogger.info('data', 'Data processed', { records: 100 }, 'system');
  auditLogger.warn('system', 'High CPU usage', { usage: 90 }, 'system');
  auditLogger.error('system', 'Disk space low', { available: '100MB' }, 'system');
  auditLogger.critical('system', 'System crash', { error: 'Out of memory' }, 'system');

  // Flush logs
  auditLogger._flushLogBuffer();

  // Dispose audit logger
  auditLogger.dispose();
}

/**
 * Example 3: Secure Storage
 * 
 * This example demonstrates how to use the secure storage component.
 */
async function example3() {
  console.log('\n=== Example 3: Secure Storage ===\n');

  // Create secure storage
  const secureStorage = createSecureStorage({
    enableLogging: true,
    storageType: 'memory', // Use memory storage for example
    autoSave: false
  });

  // Register event listeners
  secureStorage.on('set', (data) => {
    console.log('Value set:', data.key);
  });

  secureStorage.on('get', (data) => {
    console.log('Value retrieved:', data.key);
  });

  // Store sensitive data
  console.log('\nStoring sensitive data:');
  
  secureStorage.set('api_key', 'sk_test_1234567890');
  secureStorage.set('user_credentials', {
    username: 'admin',
    password: 'password123'
  });
  secureStorage.set('encryption_key', Buffer.from('abcdef1234567890').toString('hex'));

  // Retrieve sensitive data
  console.log('\nRetrieving sensitive data:');
  
  const apiKey = secureStorage.get('api_key');
  console.log('API Key:', apiKey);
  
  const userCredentials = secureStorage.get('user_credentials');
  console.log('User Credentials:', userCredentials);
  
  const encryptionKey = secureStorage.get('encryption_key');
  console.log('Encryption Key:', encryptionKey);

  // Check if keys exist
  console.log('\nChecking keys:');
  console.log('api_key exists:', secureStorage.has('api_key'));
  console.log('invalid_key exists:', secureStorage.has('invalid_key'));

  // Get all keys
  console.log('\nAll keys:', secureStorage.keys());

  // Get storage size
  console.log('Storage size:', secureStorage.size());

  // Delete a key
  secureStorage.delete('user_credentials');
  console.log('\nAfter deletion:');
  console.log('user_credentials exists:', secureStorage.has('user_credentials'));
  console.log('Storage size:', secureStorage.size());

  // Dispose secure storage
  secureStorage.dispose();
}

/**
 * Example 4: Integrated Security Components
 * 
 * This example demonstrates how to use all security components together.
 */
async function example4() {
  console.log('\n=== Example 4: Integrated Security Components ===\n');

  // Create security components
  const {
    rbac,
    auditLogger,
    secureStorage
  } = createSecurityComponents({
    enableLogging: true,
    rbacOptions: {
      defaultRole: 'guest',
      superRole: 'admin'
    },
    auditLoggerOptions: {
      enableConsole: true,
      enableFile: false,
      logLevel: 'info'
    },
    secureStorageOptions: {
      storageType: 'memory',
      autoSave: false
    }
  });

  // Create roles and permissions
  rbac.createRole('api_user', 'API user with limited access');
  rbac.createPermission('api_read', 'Read API data');
  rbac.createPermission('api_write', 'Write API data');
  rbac.assignPermissionToRole('api_user', 'api_read');
  rbac.assignRoleToUser('client1', 'api_user');

  // Store API keys
  secureStorage.set('client1_api_key', 'sk_client1_1234567890');

  // Simulate API request
  console.log('\nSimulating API request:');
  
  const clientId = 'client1';
  const apiKey = 'sk_client1_1234567890';
  const requestedPermission = 'api_read';
  
  // Verify API key
  const storedApiKey = secureStorage.get(`${clientId}_api_key`);
  const apiKeyValid = apiKey === storedApiKey;
  
  // Check permission
  const hasPermission = rbac.hasPermission(clientId, requestedPermission);
  
  // Log request
  auditLogger.info(
    'api',
    'API request received',
    {
      clientId,
      apiKeyValid,
      requestedPermission,
      hasPermission
    },
    clientId
  );
  
  // Process request
  if (apiKeyValid && hasPermission) {
    console.log('API request authorized');
    auditLogger.info('api', 'API request authorized', { clientId }, clientId);
  } else {
    console.log('API request denied');
    auditLogger.warn('api', 'API request denied', {
      clientId,
      reason: !apiKeyValid ? 'Invalid API key' : 'Insufficient permissions'
    }, clientId);
  }

  // Flush logs
  auditLogger._flushLogBuffer();

  // Dispose resources
  rbac.dispose();
  auditLogger.dispose();
  secureStorage.dispose();
}

/**
 * Example 5: Integration with Defense System
 * 
 * This example demonstrates how to integrate security components with the defense system.
 */
async function example5() {
  console.log('\n=== Example 5: Integration with Defense System ===\n');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Create security components
  const {
    rbac,
    auditLogger,
    secureStorage
  } = createSecurityComponents({
    enableLogging: true
  });

  // Create roles and permissions
  rbac.createRole('data_processor', 'Data processor with specific permissions');
  rbac.createPermission('process_cyber', 'Process cyber domain data');
  rbac.createPermission('process_financial', 'Process financial domain data');
  rbac.assignPermissionToRole('data_processor', 'process_cyber');
  rbac.assignRoleToUser('processor1', 'data_processor');

  // Process data with security checks
  console.log('\nProcessing data with security checks:');
  
  async function processSecureData(data, domain, userId) {
    // Check permission
    const permissionId = `process_${domain}`;
    const hasPermission = rbac.hasPermission(userId, permissionId);
    
    // Log request
    auditLogger.info(
      'data_processing',
      'Data processing request',
      {
        userId,
        domain,
        hasPermission
      },
      userId
    );
    
    if (!hasPermission) {
      auditLogger.warn(
        'data_processing',
        'Data processing denied',
        {
          userId,
          domain,
          reason: 'Insufficient permissions'
        },
        userId
      );
      
      throw new Error(`User ${userId} does not have permission to process ${domain} data`);
    }
    
    // Process data
    const result = await defenseSystem.processData(data, domain);
    
    // Log success
    auditLogger.info(
      'data_processing',
      'Data processed successfully',
      {
        userId,
        domain
      },
      userId
    );
    
    return result;
  }
  
  // Process cyber data (authorized)
  try {
    const cyberResult = await processSecureData(
      { securityScore: 8, threatLevel: 3 },
      'cyber',
      'processor1'
    );
    console.log('Cyber data processed:', cyberResult);
  } catch (error) {
    console.error('Error processing cyber data:', error.message);
  }
  
  // Process financial data (unauthorized)
  try {
    const financialResult = await processSecureData(
      { balance: 1000, interestRate: 0.05 },
      'financial',
      'processor1'
    );
    console.log('Financial data processed:', financialResult);
  } catch (error) {
    console.error('Error processing financial data:', error.message);
  }

  // Flush logs
  auditLogger._flushLogBuffer();

  // Dispose resources
  rbac.dispose();
  auditLogger.dispose();
  secureStorage.dispose();
  defenseSystem.dispose();
}

/**
 * Run all examples
 */
async function runAllExamples() {
  await example1();
  await example2();
  await example3();
  await example4();
  await example5();
}

// Run all examples
runAllExamples().catch(error => {
  console.error('Error running examples:', error);
});
