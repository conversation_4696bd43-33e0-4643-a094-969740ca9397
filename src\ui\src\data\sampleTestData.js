/**
 * Sample Test Data
 * 
 * This file contains sample data for testing the Cyber-Safety visualizations.
 */

/**
 * Sample data for Tri-Domain Tensor visualization
 */
export const triDomainTensorData = {
  grc: {
    values: [
      { id: 'policy1', name: 'Data Protection Policy', value: 0.85 },
      { id: 'policy2', name: 'Access Control Policy', value: 0.75 },
      { id: 'policy3', name: 'Incident Response Policy', value: 0.90 },
      { id: 'policy4', name: 'Business Continuity Policy', value: 0.80 },
      { id: 'policy5', name: 'Vendor Management Policy', value: 0.70 }
    ],
    health: 0.85,
    entropyContainment: 0.05
  },
  it: {
    values: [
      { id: 'system1', name: 'ERP System', value: 0.80 },
      { id: 'system2', name: 'CRM System', value: 0.75 },
      { id: 'system3', name: 'Email System', value: 0.90 },
      { id: 'system4', name: 'File Storage', value: 0.85 },
      { id: 'system5', name: 'Database Servers', value: 0.70 }
    ],
    health: 0.75,
    entropyContainment: 0.08
  },
  cybersecurity: {
    values: [
      { id: 'control1', name: 'Firewall', value: 0.90 },
      { id: 'control2', name: 'Endpoint Protection', value: 0.85 },
      { id: 'control3', name: 'Intrusion Detection', value: 0.75 },
      { id: 'control4', name: 'Data Loss Prevention', value: 0.80 },
      { id: 'control5', name: 'Security Monitoring', value: 0.95 }
    ],
    health: 0.90,
    entropyContainment: 0.03
  },
  connections: [
    { source: 'grc', target: 'it', strength: 0.75 },
    { source: 'grc', target: 'cybersecurity', strength: 0.85 },
    { source: 'it', target: 'cybersecurity', strength: 0.65 }
  ]
};

/**
 * Sample data for Harmony Index visualization
 */
export const harmonyIndexData = {
  domainData: {
    grc: { score: 0.85, trend: 'increasing' },
    it: { score: 0.75, trend: 'stable' },
    cybersecurity: { score: 0.90, trend: 'increasing' }
  },
  harmonyHistory: [
    { date: '2023-01-01', value: 0.70 },
    { date: '2023-02-01', value: 0.72 },
    { date: '2023-03-01', value: 0.75 },
    { date: '2023-04-01', value: 0.78 },
    { date: '2023-05-01', value: 0.80 },
    { date: '2023-06-01', value: 0.82 },
    { date: '2023-07-01', value: 0.85 }
  ],
  crossDomainHarmony: [
    { source: 'grc', target: 'it', value: 0.75 },
    { source: 'grc', target: 'cybersecurity', value: 0.85 },
    { source: 'it', target: 'cybersecurity', value: 0.65 }
  ],
  resonanceFactors: {
    alignment: 0.82,
    coherence: 0.78,
    synchronization: 0.85
  }
};

/**
 * Sample data for Risk-Control Fusion visualization
 */
export const riskControlFusionData = {
  riskData: {
    grc: {
      compliance: 0.35,
      governance: 0.40,
      reporting: 0.30,
      thirdParty: 0.45,
      regulatory: 0.25
    },
    it: {
      infrastructure: 0.30,
      applications: 0.35,
      data: 0.40,
      cloud: 0.45,
      endpoints: 0.25
    },
    cybersecurity: {
      perimeter: 0.40,
      network: 0.35,
      application: 0.30,
      data: 0.45,
      identity: 0.25
    }
  },
  controlData: {
    grc: {
      compliance: 0.75,
      governance: 0.80,
      reporting: 0.70,
      thirdParty: 0.65,
      regulatory: 0.85
    },
    it: {
      infrastructure: 0.80,
      applications: 0.75,
      data: 0.70,
      cloud: 0.65,
      endpoints: 0.85
    },
    cybersecurity: {
      perimeter: 0.85,
      network: 0.80,
      application: 0.75,
      data: 0.70,
      identity: 0.90
    }
  },
  gapAnalysis: {
    highestGap: { domain: 'it', category: 'cloud', value: 0.20 },
    lowestGap: { domain: 'cybersecurity', category: 'identity', value: 0.65 },
    averageGap: 0.40
  }
};

/**
 * Sample data for Resonance Spectrogram visualization
 */
export const resonanceSpectrogramData = {
  domainData: {
    grc: { frequency: 0.30, amplitude: 0.70, phase: 0.20 },
    it: { frequency: 0.50, amplitude: 0.60, phase: 0.40 },
    cybersecurity: { frequency: 0.70, amplitude: 0.80, phase: 0.60 },
    crossDomainFlows: [
      { source: 'grc', target: 'it', flow: 0.40 },
      { source: 'grc', target: 'cybersecurity', flow: 0.60 },
      { source: 'it', target: 'cybersecurity', flow: 0.50 }
    ]
  },
  spectrogramData: Array(50).fill().map((_, i) => ({
    time: i,
    grc: Math.sin(i * 0.1) * 0.5 + 0.5,
    it: Math.sin(i * 0.15 + 1) * 0.5 + 0.5,
    cybersecurity: Math.sin(i * 0.2 + 2) * 0.5 + 0.5
  })),
  predictionData: {
    dissonanceProbability: 0.30,
    criticalPoints: [
      { timeStep: 12, severity: 0.70, domains: ['grc', 'it'] },
      { timeStep: 28, severity: 0.50, domains: ['it', 'cybersecurity'] },
      { timeStep: 42, severity: 0.80, domains: ['grc', 'cybersecurity'] }
    ],
    resonanceWindow: { start: 15, end: 35, probability: 0.75 }
  }
};

/**
 * Sample data for Unified Compliance-Security visualization
 */
export const unifiedComplianceSecurityData = {
  complianceData: {
    requirements: [
      { id: 'req_1', name: 'Data Protection', category: 'Privacy', importance: 0.90 },
      { id: 'req_2', name: 'Access Control', category: 'Security', importance: 0.85 },
      { id: 'req_3', name: 'Incident Response', category: 'Operations', importance: 0.80 },
      { id: 'req_4', name: 'Business Continuity', category: 'Resilience', importance: 0.75 },
      { id: 'req_5', name: 'Vendor Management', category: 'Third Party', importance: 0.70 }
    ],
    controls: [
      { id: 'ctrl_1', name: 'Encryption', category: 'Technical', effectiveness: 0.85 },
      { id: 'ctrl_2', name: 'Authentication', category: 'Technical', effectiveness: 0.80 },
      { id: 'ctrl_3', name: 'Monitoring', category: 'Operational', effectiveness: 0.75 },
      { id: 'ctrl_4', name: 'Backup', category: 'Operational', effectiveness: 0.70 },
      { id: 'ctrl_5', name: 'Vendor Assessment', category: 'Administrative', effectiveness: 0.65 }
    ],
    implementations: [
      { id: 'impl_1', name: 'Database Encryption', status: 'Implemented', maturity: 0.80 },
      { id: 'impl_2', name: 'MFA', status: 'Implemented', maturity: 0.85 },
      { id: 'impl_3', name: 'SIEM', status: 'Partial', maturity: 0.60 },
      { id: 'impl_4', name: 'Cloud Backup', status: 'Implemented', maturity: 0.75 },
      { id: 'impl_5', name: 'Vendor Questionnaire', status: 'Partial', maturity: 0.50 }
    ],
    links: [
      { source: 'req_1', target: 'ctrl_1', strength: 0.90 },
      { source: 'req_2', target: 'ctrl_2', strength: 0.85 },
      { source: 'req_3', target: 'ctrl_3', strength: 0.80 },
      { source: 'req_4', target: 'ctrl_4', strength: 0.75 },
      { source: 'req_5', target: 'ctrl_5', strength: 0.70 },
      { source: 'ctrl_1', target: 'impl_1', strength: 0.85 },
      { source: 'ctrl_2', target: 'impl_2', strength: 0.80 },
      { source: 'ctrl_3', target: 'impl_3', strength: 0.75 },
      { source: 'ctrl_4', target: 'impl_4', strength: 0.70 },
      { source: 'ctrl_5', target: 'impl_5', strength: 0.65 }
    ]
  },
  impactAnalysis: {
    overallCompliance: 0.75,
    gapAnalysis: {
      gaps: [
        { id: 'gap_1', requirement: 'req_3', control: 'ctrl_3', implementation: 'impl_3', severity: 'Medium' },
        { id: 'gap_2', requirement: 'req_5', control: 'ctrl_5', implementation: 'impl_5', severity: 'High' }
      ]
    },
    proposedChanges: [
      { id: 'change_1', description: 'Enhance SIEM implementation', impact: 0.15, effort: 'Medium' },
      { id: 'change_2', description: 'Improve vendor assessment process', impact: 0.20, effort: 'High' }
    ]
  }
};

/**
 * All sample data combined
 */
export const allVisualizationData = {
  triDomainTensor: triDomainTensorData,
  harmonyIndex: harmonyIndexData,
  riskControlFusion: riskControlFusionData,
  resonanceSpectrogram: resonanceSpectrogramData,
  unifiedComplianceSecurity: unifiedComplianceSecurityData
};
