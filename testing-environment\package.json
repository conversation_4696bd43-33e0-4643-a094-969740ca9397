{"name": "novafuse-api-connector-testing", "version": "1.0.0", "description": "Testing environment for NovaConnect Universal API Connector", "main": "index.js", "scripts": {"test": "jest --config jest.config.js", "test:unit": "jest --config jest.config.js --testMatch='**/__tests__/unit/**/*.test.js'", "test:integration": "jest --config jest.config.js --testMatch='**/__tests__/integration/**/*.test.js'", "test:e2e": "jest --config jest.config.js --testMatch='**/__tests__/e2e/**/*.test.js'", "test:performance": "jest --config jest.config.js --testMatch='**/__tests__/performance/**/*.test.js'", "test:security": "jest --config jest.config.js --testMatch='**/__tests__/security/**/*.test.js'", "test:coverage": "jest --config jest.config.js --coverage", "analyze:coverage": "node coverage-analyzer.js", "generate:report": "node generate-coverage-report.js", "test:full": "npm run test:coverage && npm run analyze:coverage && npm run generate:report", "start:mock-api": "node test-mock-api.js", "start:registry": "node test-connector-registry.js", "start:auth": "node test-auth-service.js", "start:executor": "node test-connector-executor.js", "start:usage": "node test-usage-metering.js", "start:all": "concurrently \"npm run start:mock-api\" \"npm run start:registry\" \"npm run start:auth\" \"npm run start:executor\" \"npm run start:usage\"", "test:api-connection": "node api-connection-testing/run-tests.js", "test:api-auth": "jest api-connection-testing/tests/authentication.test.js --verbose", "test:api-transform": "jest api-connection-testing/tests/data-transformation.test.js --verbose", "test:api-errors": "jest api-connection-testing/tests/error-handling.test.js --verbose", "test:api-params": "jest api-connection-testing/tests/parameter-handling.test.js --verbose", "test:api-rate-limit": "jest api-connection-testing/tests/rate-limiting.test.js --verbose", "security:sast": "semgrep --config=p/javascript scan .", "security:sast:ci": "semgrep --config=p/javascript --json > reports/sast-results.json", "security:deps": "npm audit", "security:deps:ci": "npm audit --json > reports/dependency-scan.json", "security:fuzz": "node security/run-fuzz-tests.js", "security:all": "npm run security:sast && npm run security:deps && npm run security:fuzz", "regression:test": "node regression/run-regression-tests.js", "regression:update": "node regression/run-regression-tests.js --update", "performance:benchmark": "node performance/run-benchmarks.js", "chaos:test": "node chaos/run-chaos-tests.js", "test:all": "node run-all-tests.js"}, "dependencies": {"axios": "^1.8.4", "body-parser": "^2.2.0", "cors": "^2.8.5", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "express": "^5.1.0", "jsonpath": "^1.1.1"}, "devDependencies": {"artillery": "^2.0.22", "axios-mock-adapter": "^2.1.0", "chai": "^5.2.0", "concurrently": "^7.6.0", "istanbul": "^0.4.5", "jest": "^29.7.0", "jest-junit": "^13.2.0", "newman": "^6.2.1", "nock": "^14.0.3", "nyc": "^17.1.0", "sinon": "^20.0.0", "supertest": "^7.1.0"}, "jest": {"testEnvironment": "node", "coverageThreshold": {"global": {"branches": 96, "functions": 96, "lines": 96, "statements": 96}}}}