/**
 * Change Request Routes
 */

const express = require('express');
const router = express.Router();
const ChangeRequestController = require('../controllers/ChangeRequestController');
const { authenticate, hasPermission } = require('../middleware/authMiddleware');

// All routes require authentication
router.use(authenticate);

// Get all change requests (admin only)
router.get('/', hasPermission('system:audit'), (req, res, next) => {
  ChangeRequestController.getAllChangeRequests(req, res, next);
});

// Get change requests for current user
router.get('/my', (req, res, next) => {
  ChangeRequestController.getMyChangeRequests(req, res, next);
});

// Get change requests for approval
router.get('/approval', (req, res, next) => {
  ChangeRequestController.getChangeRequestsForApproval(req, res, next);
});

// Create a new change request
router.post('/', (req, res, next) => {
  ChangeRequestController.createChangeRequest(req, res, next);
});

// Get change request by ID
router.get('/:id', (req, res, next) => {
  ChangeRequestController.getChangeRequestById(req, res, next);
});

// Update a change request
router.put('/:id', (req, res, next) => {
  ChangeRequestController.updateChangeRequest(req, res, next);
});

// Delete a change request
router.delete('/:id', (req, res, next) => {
  ChangeRequestController.deleteChangeRequest(req, res, next);
});

// Get change request comments
router.get('/:id/comments', (req, res, next) => {
  ChangeRequestController.getChangeRequestComments(req, res, next);
});

// Add comment to change request
router.post('/:id/comments', (req, res, next) => {
  ChangeRequestController.addChangeRequestComment(req, res, next);
});

// Update comment
router.put('/:id/comments/:commentId', (req, res, next) => {
  ChangeRequestController.updateComment(req, res, next);
});

// Delete comment
router.delete('/:id/comments/:commentId', (req, res, next) => {
  ChangeRequestController.deleteComment(req, res, next);
});

// Get change request approvals
router.get('/:id/approvals', (req, res, next) => {
  ChangeRequestController.getChangeRequestApprovals(req, res, next);
});

// Approve change request
router.post('/:id/approve', (req, res, next) => {
  ChangeRequestController.approveChangeRequest(req, res, next);
});

// Reject change request
router.post('/:id/reject', (req, res, next) => {
  ChangeRequestController.rejectChangeRequest(req, res, next);
});

// Implement change request
router.post('/:id/implement', (req, res, next) => {
  ChangeRequestController.implementChangeRequest(req, res, next);
});

module.exports = router;
