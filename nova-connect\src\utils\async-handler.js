/**
 * NovaFuse Universal API Connector - Async Handler
 * 
 * This module provides utilities for handling asynchronous operations.
 */

/**
 * Wrap an async route handler to catch errors and pass them to the error middleware
 * 
 * @param {Function} fn - The async route handler function
 * @returns {Function} - The wrapped route handler
 */
function asyncHandler(fn) {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * Retry an async function with exponential backoff
 * 
 * @param {Function} fn - The async function to retry
 * @param {Object} options - Retry options
 * @param {number} options.maxRetries - Maximum number of retries
 * @param {number} options.initialDelay - Initial delay in milliseconds
 * @param {number} options.maxDelay - Maximum delay in milliseconds
 * @param {Function} options.shouldRetry - Function to determine if retry should be attempted
 * @returns {Promise<any>} - The result of the function
 */
async function retryWithBackoff(fn, options = {}) {
  const maxRetries = options.maxRetries || 3;
  const initialDelay = options.initialDelay || 1000;
  const maxDelay = options.maxDelay || 30000;
  const shouldRetry = options.shouldRetry || (() => true);
  
  let retries = 0;
  let delay = initialDelay;
  
  while (true) {
    try {
      return await fn();
    } catch (error) {
      // If we've reached the maximum retries or shouldn't retry, throw the error
      if (retries >= maxRetries || !shouldRetry(error)) {
        throw error;
      }
      
      // Increment retry count
      retries++;
      
      // Calculate delay with exponential backoff and jitter
      delay = Math.min(delay * 2, maxDelay);
      const jitter = delay * 0.2 * Math.random();
      const actualDelay = delay + jitter;
      
      // Wait for the delay
      await new Promise(resolve => setTimeout(resolve, actualDelay));
    }
  }
}

/**
 * Create a circuit breaker for an async function
 * 
 * @param {Function} fn - The async function to protect
 * @param {Object} options - Circuit breaker options
 * @param {number} options.failureThreshold - Number of failures before opening the circuit
 * @param {number} options.resetTimeout - Time in milliseconds before attempting to close the circuit
 * @param {Function} options.isFailure - Function to determine if a result is a failure
 * @returns {Function} - The protected function
 */
function circuitBreaker(fn, options = {}) {
  const failureThreshold = options.failureThreshold || 5;
  const resetTimeout = options.resetTimeout || 30000;
  const isFailure = options.isFailure || (error => true);
  
  let failures = 0;
  let circuitOpen = false;
  let nextAttempt = Date.now();
  
  return async function circuitBreakerWrapper(...args) {
    // If the circuit is open, check if we should try again
    if (circuitOpen) {
      if (Date.now() < nextAttempt) {
        throw new Error('Circuit is open');
      }
      
      // Circuit is half-open, allow one request
      circuitOpen = false;
    }
    
    try {
      const result = await fn(...args);
      
      // Reset failures on success
      failures = 0;
      
      return result;
    } catch (error) {
      // Check if this error should count as a failure
      if (isFailure(error)) {
        failures++;
        
        // If we've reached the threshold, open the circuit
        if (failures >= failureThreshold) {
          circuitOpen = true;
          nextAttempt = Date.now() + resetTimeout;
        }
      }
      
      throw error;
    }
  };
}

module.exports = {
  asyncHandler,
  retryWithBackoff,
  circuitBreaker
};
