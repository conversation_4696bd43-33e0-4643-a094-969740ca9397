# Enhanced Reporting

NovaConnect provides comprehensive reporting capabilities to generate insights from your data. This document describes the enhanced reporting features and how to use them.

## Overview

The enhanced reporting system provides the following features:

- **Compliance Reporting**: Generate reports on compliance status for various frameworks
- **Performance Reporting**: Generate reports on system and connector performance
- **Security Reporting**: Generate reports on security status and incidents
- **Custom Reporting**: Create custom reports with advanced filtering and visualization
- **Report Scheduling**: Schedule reports to run automatically
- **Multiple Export Formats**: Export reports in PDF, CSV, Excel, and JSON formats

## API Endpoints

### Generate Compliance Report

Generate a compliance report for a specific framework.

```
POST /api/enhanced-reports/compliance
```

#### Request Body

```json
{
  "framework": "NIST-800-53",
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.999Z",
  "includeControls": true,
  "includeFindings": true,
  "includeRemediation": true,
  "format": "pdf"
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| framework | String | Compliance framework (e.g., NIST-800-53, ISO-27001) |
| startDate | ISO Date | Start date for the report period |
| endDate | ISO Date | End date for the report period |
| includeControls | Boolean | Whether to include control details |
| includeFindings | Boolean | Whether to include findings |
| includeRemediation | Boolean | Whether to include remediation actions |
| format | String | Report format (pdf, csv, excel, json) |

#### Response

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "type": "compliance",
  "name": "Compliance Report - NIST-800-53",
  "description": "Compliance report for NIST-800-53 framework",
  "parameters": {
    "framework": "NIST-800-53",
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.999Z",
    "includeControls": true,
    "includeFindings": true,
    "includeRemediation": true
  },
  "format": "pdf",
  "status": "generating",
  "createdBy": "user-123",
  "created": "2023-05-01T12:34:56.789Z",
  "updated": "2023-05-01T12:34:56.789Z"
}
```

### Generate Performance Report

Generate a performance report for the system or specific connectors.

```
POST /api/enhanced-reports/performance
```

#### Request Body

```json
{
  "metrics": ["api_calls", "response_time", "error_rate"],
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.999Z",
  "interval": "day",
  "connectorIds": ["conn-123", "conn-456"],
  "format": "pdf"
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| metrics | Array | Metrics to include in the report |
| startDate | ISO Date | Start date for the report period |
| endDate | ISO Date | End date for the report period |
| interval | String | Time interval for data aggregation (hour, day, week, month) |
| connectorIds | Array | IDs of connectors to include (empty for all) |
| format | String | Report format (pdf, csv, excel, json) |

#### Response

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440001",
  "type": "performance",
  "name": "Performance Report",
  "description": "Performance report for 2023-01-01 to 2023-12-31",
  "parameters": {
    "metrics": ["api_calls", "response_time", "error_rate"],
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.999Z",
    "interval": "day",
    "connectorIds": ["conn-123", "conn-456"]
  },
  "format": "pdf",
  "status": "generating",
  "createdBy": "user-123",
  "created": "2023-05-01T12:34:56.789Z",
  "updated": "2023-05-01T12:34:56.789Z"
}
```

### Generate Security Report

Generate a security report for various security domains.

```
POST /api/enhanced-reports/security
```

#### Request Body

```json
{
  "securityDomains": ["authentication", "authorization", "data_protection", "network_security"],
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.999Z",
  "includeIncidents": true,
  "includeVulnerabilities": true,
  "includeMitigations": true,
  "format": "pdf"
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| securityDomains | Array | Security domains to include in the report |
| startDate | ISO Date | Start date for the report period |
| endDate | ISO Date | End date for the report period |
| includeIncidents | Boolean | Whether to include security incidents |
| includeVulnerabilities | Boolean | Whether to include vulnerabilities |
| includeMitigations | Boolean | Whether to include mitigations |
| format | String | Report format (pdf, csv, excel, json) |

#### Response

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440002",
  "type": "security",
  "name": "Security Report",
  "description": "Security report for 2023-01-01 to 2023-12-31",
  "parameters": {
    "securityDomains": ["authentication", "authorization", "data_protection", "network_security"],
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2023-12-31T23:59:59.999Z",
    "includeIncidents": true,
    "includeVulnerabilities": true,
    "includeMitigations": true
  },
  "format": "pdf",
  "status": "generating",
  "createdBy": "user-123",
  "created": "2023-05-01T12:34:56.789Z",
  "updated": "2023-05-01T12:34:56.789Z"
}
```

### Generate Custom Report

Generate a custom report with advanced filtering and visualization.

```
POST /api/enhanced-reports/custom
```

#### Request Body

```json
{
  "name": "Custom Report",
  "description": "Custom report with advanced filtering",
  "dataSources": ["connectors", "audit_logs", "compliance_data"],
  "filters": {
    "connectorType": "aws",
    "status": "active",
    "region": "us-east-1"
  },
  "groupBy": ["connectorType", "region"],
  "metrics": ["count", "success_rate", "average_response_time"],
  "visualizations": [
    {
      "type": "bar_chart",
      "title": "Connector Count by Type",
      "xAxis": "connectorType",
      "yAxis": "count"
    },
    {
      "type": "line_chart",
      "title": "Success Rate Over Time",
      "xAxis": "date",
      "yAxis": "success_rate"
    }
  ],
  "format": "pdf"
}
```

#### Response

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440003",
  "type": "custom",
  "name": "Custom Report",
  "description": "Custom report with advanced filtering",
  "parameters": {
    "dataSources": ["connectors", "audit_logs", "compliance_data"],
    "filters": {
      "connectorType": "aws",
      "status": "active",
      "region": "us-east-1"
    },
    "groupBy": ["connectorType", "region"],
    "metrics": ["count", "success_rate", "average_response_time"],
    "visualizations": [
      {
        "type": "bar_chart",
        "title": "Connector Count by Type",
        "xAxis": "connectorType",
        "yAxis": "count"
      },
      {
        "type": "line_chart",
        "title": "Success Rate Over Time",
        "xAxis": "date",
        "yAxis": "success_rate"
      }
    ]
  },
  "format": "pdf",
  "status": "generating",
  "createdBy": "user-123",
  "created": "2023-05-01T12:34:56.789Z",
  "updated": "2023-05-01T12:34:56.789Z"
}
```

### Get Report Status

Get the status of a report.

```
GET /api/enhanced-reports/:reportId/status
```

#### Response

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "progress": 100,
  "message": "Report generation completed successfully",
  "updated": "2023-05-01T12:35:56.789Z"
}
```

### Download Report

Download a report in the specified format.

```
GET /api/enhanced-reports/:reportId/download?format=pdf
```

#### Query Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| format | String | Report format (pdf, csv, excel, json) |

#### Response

The response is a file download with the appropriate content type.

### Schedule Report

Schedule a report to run automatically.

```
POST /api/enhanced-reports/:reportId/schedule
```

#### Request Body

```json
{
  "schedule": {
    "type": "weekly",
    "dayOfWeek": 1,
    "hour": 8,
    "minute": 0
  },
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.999Z",
  "timezone": "America/New_York",
  "format": "pdf"
}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| schedule | Object | Schedule configuration |
| schedule.type | String | Schedule type (daily, weekly, monthly, quarterly, custom) |
| schedule.dayOfWeek | Number | Day of week (0-6, where 0 is Sunday) for weekly schedules |
| schedule.dayOfMonth | Number | Day of month (1-31) for monthly schedules |
| schedule.hour | Number | Hour (0-23) |
| schedule.minute | Number | Minute (0-59) |
| schedule.cronExpression | String | Custom cron expression for custom schedules |
| recipients | Array | Email addresses to receive the report |
| startDate | ISO Date | Start date for the schedule |
| endDate | ISO Date | End date for the schedule |
| timezone | String | Timezone for the schedule |
| format | String | Report format (pdf, csv, excel, json) |

#### Response

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440004",
  "reportId": "550e8400-e29b-41d4-a716-446655440000",
  "schedule": {
    "type": "weekly",
    "dayOfWeek": 1,
    "hour": 8,
    "minute": 0
  },
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "startDate": "2023-01-01T00:00:00.000Z",
  "endDate": "2023-12-31T23:59:59.999Z",
  "timezone": "America/New_York",
  "format": "pdf",
  "active": true,
  "createdBy": "user-123",
  "created": "2023-05-01T12:34:56.789Z",
  "updated": "2023-05-01T12:34:56.789Z"
}
```

## Report Templates

Report templates allow you to save report configurations for reuse.

### Get All Report Templates

Get all report templates.

```
GET /api/enhanced-reports/templates
```

### Get Report Template by ID

Get a report template by ID.

```
GET /api/enhanced-reports/templates/:id
```

### Create Report Template

Create a new report template.

```
POST /api/enhanced-reports/templates
```

### Update Report Template

Update an existing report template.

```
PUT /api/enhanced-reports/templates/:id
```

### Delete Report Template

Delete a report template.

```
DELETE /api/enhanced-reports/templates/:id
```

### Clone Report Template

Clone an existing report template.

```
POST /api/enhanced-reports/templates/:id/clone
```

## Scheduled Reports

Scheduled reports allow you to run reports automatically on a schedule.

### Get All Scheduled Reports

Get all scheduled reports.

```
GET /api/enhanced-reports/scheduled
```

### Get My Scheduled Reports

Get scheduled reports created by the current user.

```
GET /api/enhanced-reports/scheduled/my
```

### Get Scheduled Report by ID

Get a scheduled report by ID.

```
GET /api/enhanced-reports/scheduled/:id
```

### Update Scheduled Report

Update an existing scheduled report.

```
PUT /api/enhanced-reports/scheduled/:id
```

### Delete Scheduled Report

Delete a scheduled report.

```
DELETE /api/enhanced-reports/scheduled/:id
```

### Run Scheduled Report Now

Run a scheduled report immediately.

```
POST /api/enhanced-reports/scheduled/:id/run
```

## Security Considerations

- Reports may contain sensitive information and should be protected accordingly
- Access to reports should be restricted to authorized users
- Reports should be retained for a period consistent with compliance requirements
- Reports should be backed up regularly
- Report integrity should be maintained to prevent tampering
