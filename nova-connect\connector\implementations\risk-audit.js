/**
 * Risk & Audit Connector Implementation
 * 
 * This module implements the connector for risk management and audit systems.
 */

const axios = require('axios');
const { createLogger } = require('../../utils/logger');

const logger = createLogger('risk-audit-connector');

/**
 * Risk & Audit Connector
 */
class RiskAuditConnector {
  /**
   * Constructor
   * 
   * @param {Object} config - Connector configuration
   * @param {Object} credentials - Connector credentials
   */
  constructor(config, credentials) {
    this.config = config || {};
    this.credentials = credentials || {};
    this.baseUrl = this.config.baseUrl || 'https://api.example.com';
    this.apiKeyHeader = this.credentials.apiKeyHeader || 'X-API-Key';
  }

  /**
   * Initialize the connector
   * 
   * @returns {Promise<void>}
   */
  async initialize() {
    logger.info('Initializing Risk & Audit connector');
    
    if (!this.credentials.apiKey) {
      throw new Error('API Key is required');
    }
  }

  /**
   * Get authentication headers
   * 
   * @returns {Object} - Authentication headers
   */
  getAuthHeaders() {
    const headers = {};
    headers[this.apiKeyHeader] = this.credentials.apiKey;
    return headers;
  }

  /**
   * List risks
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Object>} - List of risks
   */
  async listRisks(params = {}) {
    logger.info('Listing risks', { params });
    
    try {
      const response = await axios.get(`${this.baseUrl}/risks`, {
        params,
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error listing risks', { error: error.message });
      throw new Error(`Error listing risks: ${error.message}`);
    }
  }

  /**
   * Get a risk
   * 
   * @param {string} riskId - Risk ID
   * @returns {Promise<Object>} - Risk details
   */
  async getRisk(riskId) {
    logger.info('Getting risk', { riskId });
    
    if (!riskId) {
      throw new Error('Risk ID is required');
    }
    
    try {
      const response = await axios.get(`${this.baseUrl}/risks/${riskId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting risk', { riskId, error: error.message });
      throw new Error(`Error getting risk: ${error.message}`);
    }
  }

  /**
   * Create a risk
   * 
   * @param {Object} riskData - Risk data
   * @returns {Promise<Object>} - Created risk
   */
  async createRisk(riskData) {
    logger.info('Creating risk');
    
    if (!riskData) {
      throw new Error('Risk data is required');
    }
    
    // Validate required fields
    const requiredFields = ['title', 'description', 'category', 'severity', 'likelihood', 'impact'];
    for (const field of requiredFields) {
      if (!riskData[field]) {
        throw new Error(`${field} is required`);
      }
    }
    
    try {
      const response = await axios.post(`${this.baseUrl}/risks`, riskData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating risk', { error: error.message });
      throw new Error(`Error creating risk: ${error.message}`);
    }
  }

  /**
   * Update risk status
   * 
   * @param {string} riskId - Risk ID
   * @param {string} status - New status
   * @param {string} comment - Comment
   * @returns {Promise<Object>} - Updated risk
   */
  async updateRiskStatus(riskId, status, comment) {
    logger.info('Updating risk status', { riskId, status });
    
    if (!riskId) {
      throw new Error('Risk ID is required');
    }
    
    if (!status) {
      throw new Error('Status is required');
    }
    
    const validStatuses = ['open', 'mitigated', 'accepted', 'transferred', 'closed'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status: ${status}. Valid statuses are: ${validStatuses.join(', ')}`);
    }
    
    try {
      const response = await axios.patch(`${this.baseUrl}/risks/${riskId}/status`, {
        status,
        comment
      }, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating risk status', { riskId, status, error: error.message });
      throw new Error(`Error updating risk status: ${error.message}`);
    }
  }

  /**
   * Update a risk
   * 
   * @param {string} riskId - Risk ID
   * @param {Object} riskData - Risk data
   * @returns {Promise<Object>} - Updated risk
   */
  async updateRisk(riskId, riskData) {
    logger.info('Updating risk', { riskId });
    
    if (!riskId) {
      throw new Error('Risk ID is required');
    }
    
    if (!riskData) {
      throw new Error('Risk data is required');
    }
    
    try {
      const response = await axios.put(`${this.baseUrl}/risks/${riskId}`, riskData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error updating risk', { riskId, error: error.message });
      throw new Error(`Error updating risk: ${error.message}`);
    }
  }

  /**
   * Delete a risk
   * 
   * @param {string} riskId - Risk ID
   * @returns {Promise<void>}
   */
  async deleteRisk(riskId) {
    logger.info('Deleting risk', { riskId });
    
    if (!riskId) {
      throw new Error('Risk ID is required');
    }
    
    try {
      await axios.delete(`${this.baseUrl}/risks/${riskId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Accept': 'application/json'
        }
      });
      
      logger.info('Risk deleted successfully', { riskId });
    } catch (error) {
      logger.error('Error deleting risk', { riskId, error: error.message });
      throw new Error(`Error deleting risk: ${error.message}`);
    }
  }

  /**
   * Get risk assessment
   * 
   * @param {string} entityType - Entity type (e.g., 'vendor', 'application', 'process')
   * @param {string} entityId - Entity ID
   * @returns {Promise<Object>} - Risk assessment
   */
  async getRiskAssessment(entityType, entityId) {
    logger.info('Getting risk assessment', { entityType, entityId });
    
    if (!entityType) {
      throw new Error('Entity type is required');
    }
    
    if (!entityId) {
      throw new Error('Entity ID is required');
    }
    
    try {
      const response = await axios.get(`${this.baseUrl}/risk-assessments/${entityType}/${entityId}`, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error getting risk assessment', { entityType, entityId, error: error.message });
      throw new Error(`Error getting risk assessment: ${error.message}`);
    }
  }

  /**
   * Create risk assessment
   * 
   * @param {string} entityType - Entity type (e.g., 'vendor', 'application', 'process')
   * @param {string} entityId - Entity ID
   * @param {Object} assessmentData - Assessment data
   * @returns {Promise<Object>} - Created risk assessment
   */
  async createRiskAssessment(entityType, entityId, assessmentData) {
    logger.info('Creating risk assessment', { entityType, entityId });
    
    if (!entityType) {
      throw new Error('Entity type is required');
    }
    
    if (!entityId) {
      throw new Error('Entity ID is required');
    }
    
    if (!assessmentData) {
      throw new Error('Assessment data is required');
    }
    
    try {
      const response = await axios.post(`${this.baseUrl}/risk-assessments/${entityType}/${entityId}`, assessmentData, {
        headers: {
          ...this.getAuthHeaders(),
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      return response.data;
    } catch (error) {
      logger.error('Error creating risk assessment', { entityType, entityId, error: error.message });
      throw new Error(`Error creating risk assessment: ${error.message}`);
    }
  }
}

module.exports = RiskAuditConnector;
