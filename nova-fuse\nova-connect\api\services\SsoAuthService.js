/**
 * SSO Authentication Service
 * 
 * This service integrates SAML and OIDC authentication for SSO.
 */

const fs = require('fs').promises;
const path = require('path');
const { ValidationError, AuthenticationError } = require('../utils/errors');
const IdentityProviderService = require('./IdentityProviderService');
const SamlAuthService = require('./SamlAuthService');
const OidcAuthService = require('./OidcAuthService');
const AuditService = require('./AuditService');

class SsoAuthService {
  constructor(dataDir = path.join(__dirname, '../data')) {
    this.dataDir = dataDir;
    this.identityProviderService = new IdentityProviderService(dataDir);
    this.samlAuthService = new SamlAuthService(dataDir);
    this.oidcAuthService = new OidcAuthService(dataDir);
    this.auditService = new AuditService(dataDir);
  }

  /**
   * Initiate SSO authentication
   */
  async initiateAuth(providerId, redirectUri = null, relayState = null, nonce = null) {
    try {
      // Get provider
      const provider = await this.identityProviderService.getProviderById(providerId);
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // Initiate authentication based on provider type
      if (provider.type === 'saml') {
        return await this.samlAuthService.generateAuthRequest(providerId, relayState);
      } else if (provider.type === 'oidc') {
        return await this.oidcAuthService.generateAuthUrl(providerId, redirectUri, nonce);
      } else {
        throw new ValidationError(`Unsupported provider type: ${provider.type}`);
      }
    } catch (error) {
      console.error('Error initiating SSO authentication:', error);
      throw error;
    }
  }

  /**
   * Process SSO callback
   */
  async processCallback(params) {
    try {
      // Determine provider type from parameters
      if (params.SAMLResponse) {
        // SAML response
        return await this.samlAuthService.processSamlResponse(
          params.SAMLResponse,
          params.RelayState
        );
      } else if (params.code && params.state) {
        // OIDC response
        return await this.oidcAuthService.processCallback(
          params.code,
          params.state,
          params.redirectUri
        );
      } else {
        throw new ValidationError('Invalid SSO callback parameters');
      }
    } catch (error) {
      console.error('Error processing SSO callback:', error);
      throw error;
    }
  }

  /**
   * Initiate SSO logout
   */
  async initiateLogout(userId, providerId, idToken = null, postLogoutRedirectUri = null) {
    try {
      // Get provider
      const provider = await this.identityProviderService.getProviderById(providerId);
      
      if (!provider.enabled) {
        throw new ValidationError('Provider is disabled');
      }
      
      // Initiate logout based on provider type
      if (provider.type === 'saml') {
        return await this.samlAuthService.generateLogoutRequest(userId, providerId);
      } else if (provider.type === 'oidc') {
        return await this.oidcAuthService.logout(userId, providerId, idToken, postLogoutRedirectUri);
      } else {
        throw new ValidationError(`Unsupported provider type: ${provider.type}`);
      }
    } catch (error) {
      console.error('Error initiating SSO logout:', error);
      throw error;
    }
  }

  /**
   * Process SSO logout callback
   */
  async processLogoutCallback(params) {
    try {
      // Determine provider type from parameters
      if (params.SAMLResponse) {
        // SAML logout response
        return await this.samlAuthService.processSamlLogoutResponse(
          params.SAMLResponse,
          params.RelayState
        );
      } else {
        // OIDC doesn't typically have a logout callback
        return { success: true, message: 'Logout successful' };
      }
    } catch (error) {
      console.error('Error processing SSO logout callback:', error);
      throw error;
    }
  }

  /**
   * Get provider by domain
   */
  async getProviderByDomain(domain) {
    return await this.identityProviderService.getProviderByDomain(domain);
  }

  /**
   * Refresh OIDC tokens
   */
  async refreshTokens(refreshToken, providerId) {
    try {
      // Get provider
      const provider = await this.identityProviderService.getProviderById(providerId);
      
      if (provider.type !== 'oidc') {
        throw new ValidationError('Provider is not an OIDC provider');
      }
      
      return await this.oidcAuthService.refreshTokens(refreshToken, providerId);
    } catch (error) {
      console.error('Error refreshing OIDC tokens:', error);
      throw error;
    }
  }

  /**
   * Clean up expired data
   */
  async cleanup() {
    try {
      // Clean up expired OIDC states
      const oidcResult = await this.oidcAuthService.cleanupExpiredStates();
      
      return {
        success: true,
        oidc: oidcResult
      };
    } catch (error) {
      console.error('Error cleaning up SSO data:', error);
      throw error;
    }
  }
}

module.exports = SsoAuthService;
