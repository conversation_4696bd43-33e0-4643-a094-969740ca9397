"""
Example of using the integrated Report Manager with the Evidence Manager.

This example demonstrates how to use the Report Manager integrated with
the Evidence Manager to generate evidence and compliance reports.
"""

import os
import sys
import json
import uuid
import logging
import datetime

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.evidence_manager import EvidenceManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the integrated Report Manager example."""
    # Create a temporary directory for the example
    temp_dir = os.path.join(os.getcwd(), 'temp_integrated_report_example')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Create directories for the Evidence Manager
    evidence_dir = os.path.join(temp_dir, 'evidence_data')
    reports_dir = os.path.join(temp_dir, 'reports')
    
    # Create an Evidence Manager
    manager = EvidenceManager(
        evidence_dir=evidence_dir,
        current_user_id="admin"
    )
    
    # Set the reports directory for the Report Manager
    manager.report_manager.reports_dir = reports_dir
    
    # Create sample requirements
    requirements = {
        'REQ-001': {
            'name': 'Password Policy',
            'description': 'The organization must have a password policy that requires complex passwords.',
            'framework': 'NIST SP 800-53',
            'control': 'IA-5'
        },
        'REQ-002': {
            'name': 'Access Control',
            'description': 'The organization must implement access controls to restrict system access to authorized users.',
            'framework': 'NIST SP 800-53',
            'control': 'AC-3'
        },
        'REQ-003': {
            'name': 'Audit Logging',
            'description': 'The organization must implement audit logging to record security-relevant events.',
            'framework': 'NIST SP 800-53',
            'control': 'AU-2'
        }
    }
    
    try:
        # Create sample evidence items
        for i in range(10):
            # Generate a unique ID
            evidence_id = str(uuid.uuid4())
            
            # Determine the type and category
            if i < 3:
                evidence_type = 'document'
                category = 'policy'
                requirement_id = 'REQ-001'
                is_valid = True
            elif i < 6:
                evidence_type = 'configuration'
                category = 'configuration'
                requirement_id = 'REQ-002'
                is_valid = i != 5  # Make one invalid
            else:
                evidence_type = 'log'
                category = 'log'
                requirement_id = 'REQ-003'
                is_valid = i != 8  # Make one invalid
            
            # Create the evidence
            created_at = (datetime.datetime.now(datetime.timezone.utc) - 
                         datetime.timedelta(days=i)).isoformat()
            
            evidence = {
                'id': evidence_id,
                'type': evidence_type,
                'source': 'example',
                'data': {
                    'title': f'Sample {evidence_type.title()} {i+1}',
                    'content': f'This is a sample {evidence_type} for testing the Report Manager.',
                    'created_at': created_at
                },
                'metadata': {
                    'tags': ['example', evidence_type, category]
                }
            }
            
            # Register the evidence
            manager.register_evidence(evidence)
            
            # Add the evidence to the category
            manager.add_evidence_to_category(evidence_id, category)
            
            # Add the evidence to the requirement
            manager.add_evidence_to_requirement(evidence_id, requirement_id)
            
            # Add tags
            for tag in ['example', evidence_type, category]:
                manager.add_evidence_tag(evidence_id, tag)
            
            # Validate the evidence
            # Create a mock validation result
            validation_results = {
                'is_valid': is_valid,
                'details': {
                    'reason': 'Example validation result'
                }
            }
            
            # Update the evidence metadata with the validation results
            manager.evidence_metadata[evidence_id]['validation_results'] = validation_results
            manager.evidence_metadata[evidence_id]['status'] = 'validated' if is_valid else 'invalid'
        
        # Generate an evidence report in JSON format
        logger.info("Generating evidence report in JSON format...")
        json_report = manager.generate_evidence_report(
            format='json'
        )
        logger.info(f"JSON report generated: {json_report}")
        
        # Generate an evidence report in HTML format
        logger.info("Generating evidence report in HTML format...")
        html_report = manager.generate_evidence_report(
            format='html'
        )
        logger.info(f"HTML report generated: {html_report}")
        
        # Generate a filtered evidence report
        logger.info("Generating filtered evidence report...")
        filtered_report = manager.generate_evidence_report(
            format='json',
            filters={
                'category': 'policy',
                'is_valid': True
            }
        )
        logger.info(f"Filtered report generated: {filtered_report}")
        
        # Generate a compliance report in JSON format
        logger.info("Generating compliance report in JSON format...")
        json_compliance_report = manager.generate_compliance_report(
            requirements=requirements,
            format='json'
        )
        logger.info(f"JSON compliance report generated: {json_compliance_report}")
        
        # Generate a compliance report in HTML format
        logger.info("Generating compliance report in HTML format...")
        html_compliance_report = manager.generate_compliance_report(
            requirements=requirements,
            format='html'
        )
        logger.info(f"HTML compliance report generated: {html_compliance_report}")
        
        # Open the HTML reports in the browser
        logger.info("Opening HTML reports in the browser...")
        import webbrowser
        webbrowser.open(f"file://{os.path.abspath(html_report)}")
        webbrowser.open(f"file://{os.path.abspath(html_compliance_report)}")
        
    except Exception as e:
        logger.error(f"Error: {e}")
    
    finally:
        # Clean up the temporary directory
        # Uncomment the following line to delete the temporary directory
        # import shutil; shutil.rmtree(temp_dir)
        pass

if __name__ == "__main__":
    main()
