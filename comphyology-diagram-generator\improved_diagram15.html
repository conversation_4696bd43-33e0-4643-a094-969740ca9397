<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>15. Hardware Architecture</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
        }
        .diagram-container {
            width: 1000px;
            height: 800px;
            position: relative;
            border: 2px solid black;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
        }
        .element {
            position: absolute;
            border-radius: 0; /* Square corners for patent diagrams */
            padding: 20px 20px 20px 50px; /* Extra padding on left for number */
            z-index: 2;
            background-color: white;
            border: 2px solid black;
        }
        .element-number {
            position: absolute;
            top: 50%;
            left: 15px;
            transform: translateY(-50%);
            background-color: black;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            z-index: 3;
        }
        .connection {
            position: absolute;
            z-index: 1;
            background-color: black;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .bold-formula {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>15. Hardware Architecture</h1>
    
    <div class="diagram-container">
        <!-- Hardware Architecture -->
        <div class="element" style="top: 50px; left: 300px; width: 400px; font-weight: bold; font-size: 20px;">
            Hardware Architecture
            <div class="element-number">1</div>
        </div>
        
        <!-- Tensor Processing Units -->
        <div class="element" style="top: 150px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Tensor Processing Units (TPUs)
            <div class="element-number">2</div>
        </div>
        
        <div class="element" style="top: 250px; left: 100px; width: 250px; font-size: 14px;">
            Tensor Core Array<br>Massively parallel processing units
            <div class="element-number">3</div>
        </div>
        
        <div class="element" style="top: 250px; left: 375px; width: 250px; font-size: 14px;">
            High-Precision Arithmetic Units<br>64-bit floating-point units
            <div class="element-number">4</div>
        </div>
        
        <div class="element" style="top: 250px; left: 650px; width: 250px; font-size: 14px;">
            Tensor Memory Cache<br>Specialized memory architecture
            <div class="element-number">5</div>
        </div>
        
        <div class="element" style="top: 350px; left: 375px; width: 250px; font-size: 14px;">
            Tensor Instruction Set<br>Custom instructions for tensor operations
            <div class="element-number">6</div>
        </div>
        
        <!-- Fusion Processing Engines -->
        <div class="element" style="top: 450px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Fusion Processing Engines (FPEs)
            <div class="element-number">7</div>
        </div>
        
        <div class="element" style="top: 550px; left: 100px; width: 250px; font-size: 14px;">
            Fusion Core<br>Implements fusion operator <span class="bold-formula">(⊕)</span>
            <div class="element-number">8</div>
        </div>
        
        <div class="element" style="top: 550px; left: 375px; width: 250px; font-size: 14px;">
            Pattern Matching Units<br>Identifies related data patterns
            <div class="element-number">9</div>
        </div>
        
        <div class="element" style="top: 550px; left: 650px; width: 250px; font-size: 14px;">
            Fusion Memory<br>Stores intermediate fusion results
            <div class="element-number">10</div>
        </div>
        
        <!-- Performance Metrics -->
        <div class="element" style="top: 650px; left: 300px; width: 400px; font-weight: bold; font-size: 16px;">
            Performance Metrics
            <div class="element-number">11</div>
        </div>
        
        <div class="element" style="top: 750px; left: 100px; width: 250px; font-size: 14px;">
            Processing Power<br>3,142 TFLOPS tensor operations
            <div class="element-number">12</div>
        </div>
        
        <div class="element" style="top: 750px; left: 375px; width: 250px; font-size: 14px;">
            Energy Efficiency<br>0.5 TFLOPS/watt
            <div class="element-number">13</div>
        </div>
        
        <div class="element" style="top: 750px; left: 650px; width: 250px; font-size: 14px;">
            Accuracy<br>95% across all domains
            <div class="element-number">14</div>
        </div>
        
        <!-- Connections -->
        <!-- Connect Hardware Architecture to Tensor Processing Units -->
        <div class="connection" style="top: 100px; left: 500px; width: 2px; height: 50px;"></div>
        
        <!-- Connect Tensor Processing Units to components -->
        <div class="connection" style="top: 200px; left: 225px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 225px; width: 75px; height: 2px;"></div>
        
        <div class="connection" style="top: 200px; left: 500px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 200px; left: 775px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 200px; left: 700px; width: 75px; height: 2px;"></div>
        
        <div class="connection" style="top: 300px; left: 500px; width: 2px; height: 50px;"></div>
        
        <!-- Connect to Fusion Processing Engines -->
        <div class="connection" style="top: 400px; left: 500px; width: 2px; height: 50px;"></div>
        
        <!-- Connect Fusion Processing Engines to components -->
        <div class="connection" style="top: 500px; left: 225px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 500px; left: 225px; width: 75px; height: 2px;"></div>
        
        <div class="connection" style="top: 500px; left: 500px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 500px; left: 775px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 500px; left: 700px; width: 75px; height: 2px;"></div>
        
        <!-- Connect to Performance Metrics -->
        <div class="connection" style="top: 600px; left: 500px; width: 2px; height: 50px;"></div>
        
        <!-- Connect Performance Metrics to components -->
        <div class="connection" style="top: 700px; left: 225px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 700px; left: 225px; width: 75px; height: 2px;"></div>
        
        <div class="connection" style="top: 700px; left: 500px; width: 2px; height: 50px;"></div>
        
        <div class="connection" style="top: 700px; left: 775px; width: 2px; height: 50px;"></div>
        <div class="connection" style="top: 700px; left: 700px; width: 75px; height: 2px;"></div>
    </div>
</body>
</html>
