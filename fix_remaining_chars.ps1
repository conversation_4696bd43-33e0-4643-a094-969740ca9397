# Script to fix remaining character encoding issues in the dictionary

# Define paths
$dictionaryPath = "d:\\novafuse-api-superstore\\coherence-reality-systems\\Comphyology Master Archive\\3. APPENDIX A - MATH FOUNDATION\\The Comphyological Dictionary 1st Edition.md"
$backupPath = "$dictionaryPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss').md"

# Create a backup of the original file
Copy-Item -Path $dictionaryPath -Destination $backupPath -Force
Write-Host "Created backup at: $backupPath"

# Read the content with UTF-8 encoding
$content = [System.IO.File]::ReadAllText($dictionaryPath, [System.Text.Encoding]::UTF8)

# Define replacements for the most problematic patterns
$replacements = @{
    # Fix the lightbulb + quote pattern
    '💡â€˜' = '💡'
    
    # Fix the Aetherium symbol
    'Ã¢Â\u008dÂ¶' = 'ℵ'  # Using Aleph symbol as a substitute
    
    # Fix remaining emoji variants
    'Ã°Å¸Â§Â\u00a0' = '⚠️'
    'Ã¢Å“Â¨' = '✨'
    'â€˜' = "'"
    'â€"' = '"'
    'Â' = ' '
    'â€"' = '–'  # en dash
    'â€"' = '—'  # em dash
    'â€œ' = '"'
    'â€' = '"'
    'â„¢' = '™'
    'â€"' = '…'  # ellipsis
}

# Apply all replacements
foreach ($key in $replacements.Keys) {
    $content = $content.Replace($key, $replacements[$key])
}

# Fix any remaining isolated encoding issues using regex
$content = $content -replace 'Ã[^\s]{1,10}', '?'

# Write the modified content back to the file with UTF-8 encoding with BOM
$utf8WithBom = New-Object System.Text.UTF8Encoding $true
[System.IO.File]::WriteAllText($dictionaryPath, $content.Trim(), $utf8WithBom)

Write-Host "Fixed remaining character rendering issues in the dictionary file."
Write-Host "Please review the changes in the file: $dictionaryPath"
