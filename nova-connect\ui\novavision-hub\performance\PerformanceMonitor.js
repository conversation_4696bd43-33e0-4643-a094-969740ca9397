/**
 * Performance Monitor
 *
 * This module provides utilities for monitoring and optimizing performance.
 */

const networkMonitor = require('./NetworkMonitor');

/**
 * Performance Monitor class
 */
class PerformanceMonitor {
  /**
   * Constructor
   */
  constructor() {
    this.metrics = {
      renders: {},
      operations: {},
      resources: {},
      memory: [],
      network: {}
    };

    this.observers = {};
    this.isMonitoring = false;
    this.resourceObserver = null;
    this.memoryInterval = null;
    this.renderCount = 0;
    this.networkMonitor = networkMonitor;

    // Initialize
    this.init();
  }

  /**
   * Initialize performance monitor
   */
  init() {
    // Check if window is available
    if (typeof window === 'undefined' && typeof global === 'undefined') {
      console.warn('Performance monitor is not available in this environment');
      return;
    }

    // Set global object
    this.global = typeof window !== 'undefined' ? window : global;

    // Check if Performance API is available
    if (!this.global.performance) {
      console.warn('Performance API is not available');
      return;
    }

    // Create resource observer
    if (this.global.PerformanceObserver) {
      try {
        this.resourceObserver = new this.global.PerformanceObserver((list) => {
          const entries = list.getEntries();

          for (const entry of entries) {
            if (entry.entryType === 'resource') {
              this.recordResourceMetric(entry);
            }
          }
        });

        this.resourceObserver.observe({ entryTypes: ['resource'] });
      } catch (error) {
        console.warn('PerformanceObserver not supported', error);
      }
    }
  }

  /**
   * Start monitoring
   */
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.clearMetrics();

    // Start memory monitoring if available
    if (this.global.performance && this.global.performance.memory) {
      this.memoryInterval = setInterval(() => {
        this.recordMemoryMetric();
      }, 5000);
    }

    // Start network monitoring
    this.networkMonitor.startMonitoring();

    console.log('Performance monitoring started');
  }

  /**
   * Stop monitoring
   */
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;

    // Stop memory monitoring
    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
      this.memoryInterval = null;
    }

    // Stop network monitoring
    this.networkMonitor.stopMonitoring();

    console.log('Performance monitoring stopped');
  }

  /**
   * Clear metrics
   */
  clearMetrics() {
    this.metrics = {
      renders: {},
      operations: {},
      resources: {},
      memory: [],
      network: {}
    };

    this.renderCount = 0;

    // Clear network metrics
    this.networkMonitor.clearMetrics();
  }

  /**
   * Record render metric
   *
   * @param {string} componentName - Component name
   * @param {number} renderTime - Render time in milliseconds
   */
  recordRenderMetric(componentName, renderTime) {
    if (!this.isMonitoring) return;

    if (!this.metrics.renders[componentName]) {
      this.metrics.renders[componentName] = {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        avgTime: 0,
        lastTime: 0
      };
    }

    const metric = this.metrics.renders[componentName];

    metric.count++;
    metric.totalTime += renderTime;
    metric.minTime = Math.min(metric.minTime, renderTime);
    metric.maxTime = Math.max(metric.maxTime, renderTime);
    metric.avgTime = metric.totalTime / metric.count;
    metric.lastTime = renderTime;

    this.renderCount++;

    // Notify observers
    this.notifyObservers('render', {
      componentName,
      renderTime,
      metric,
      totalRenders: this.renderCount
    });
  }

  /**
   * Record operation metric
   *
   * @param {string} operationName - Operation name
   * @param {number} operationTime - Operation time in milliseconds
   * @param {Object} [metadata] - Additional metadata
   */
  recordOperationMetric(operationName, operationTime, metadata = {}) {
    if (!this.isMonitoring) return;

    if (!this.metrics.operations[operationName]) {
      this.metrics.operations[operationName] = {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        avgTime: 0,
        lastTime: 0,
        metadata: []
      };
    }

    const metric = this.metrics.operations[operationName];

    metric.count++;
    metric.totalTime += operationTime;
    metric.minTime = Math.min(metric.minTime, operationTime);
    metric.maxTime = Math.max(metric.maxTime, operationTime);
    metric.avgTime = metric.totalTime / metric.count;
    metric.lastTime = operationTime;

    if (Object.keys(metadata).length > 0) {
      metric.metadata.push({
        timestamp: Date.now(),
        time: operationTime,
        ...metadata
      });

      // Limit metadata array size
      if (metric.metadata.length > 100) {
        metric.metadata.shift();
      }
    }

    // Notify observers
    this.notifyObservers('operation', {
      operationName,
      operationTime,
      metadata,
      metric
    });
  }

  /**
   * Record resource metric
   *
   * @param {PerformanceResourceTiming} entry - Resource timing entry
   */
  recordResourceMetric(entry) {
    if (!this.isMonitoring) return;

    const { name, initiatorType, duration, transferSize, decodedBodySize } = entry;

    // Group by initiator type
    if (!this.metrics.resources[initiatorType]) {
      this.metrics.resources[initiatorType] = {
        count: 0,
        totalSize: 0,
        totalTime: 0,
        items: []
      };
    }

    const metric = this.metrics.resources[initiatorType];

    metric.count++;
    metric.totalSize += transferSize || 0;
    metric.totalTime += duration || 0;

    // Add item
    metric.items.push({
      url: name,
      time: duration,
      size: transferSize,
      decodedSize: decodedBodySize,
      timestamp: Date.now()
    });

    // Limit items array size
    if (metric.items.length > 100) {
      metric.items.shift();
    }

    // Notify observers
    this.notifyObservers('resource', {
      entry,
      metric
    });
  }

  /**
   * Record memory metric
   */
  recordMemoryMetric() {
    if (!this.isMonitoring || !this.global.performance || !this.global.performance.memory) return;

    const { usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit } = this.global.performance.memory;

    const metric = {
      timestamp: Date.now(),
      used: usedJSHeapSize,
      total: totalJSHeapSize,
      limit: jsHeapSizeLimit,
      usedPercentage: (usedJSHeapSize / jsHeapSizeLimit) * 100,
      totalPercentage: (totalJSHeapSize / jsHeapSizeLimit) * 100
    };

    this.metrics.memory.push(metric);

    // Limit memory array size
    if (this.metrics.memory.length > 100) {
      this.metrics.memory.shift();
    }

    // Notify observers
    this.notifyObservers('memory', metric);
  }

  /**
   * Measure operation time
   *
   * @param {string} operationName - Operation name
   * @param {Function} operation - Operation function
   * @param {Object} [metadata] - Additional metadata
   * @returns {*} Operation result
   */
  measureOperation(operationName, operation, metadata = {}) {
    const startTime = performance.now();

    try {
      const result = operation();

      // Handle promises
      if (result instanceof Promise) {
        return result.then((value) => {
          const endTime = performance.now();
          this.recordOperationMetric(operationName, endTime - startTime, metadata);
          return value;
        }).catch((error) => {
          const endTime = performance.now();
          this.recordOperationMetric(operationName, endTime - startTime, {
            ...metadata,
            error: error.message
          });
          throw error;
        });
      }

      // Handle synchronous operations
      const endTime = performance.now();
      this.recordOperationMetric(operationName, endTime - startTime, metadata);
      return result;
    } catch (error) {
      const endTime = performance.now();
      this.recordOperationMetric(operationName, endTime - startTime, {
        ...metadata,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Add observer
   *
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   * @returns {Function} Unsubscribe function
   */
  addObserver(event, callback) {
    if (!this.observers[event]) {
      this.observers[event] = [];
    }

    this.observers[event].push(callback);

    // Return unsubscribe function
    return () => {
      this.observers[event] = this.observers[event].filter(cb => cb !== callback);
    };
  }

  /**
   * Notify observers
   *
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  notifyObservers(event, data) {
    if (!this.observers[event]) return;

    for (const callback of this.observers[event]) {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in performance observer callback', error);
      }
    }
  }

  /**
   * Get metrics
   *
   * @returns {Object} Metrics
   */
  getMetrics() {
    // Get network metrics
    const networkMetrics = this.networkMonitor.getMetrics();

    // Update network metrics
    this.metrics.network = networkMetrics;

    return this.metrics;
  }

  /**
   * Get render metrics
   *
   * @returns {Object} Render metrics
   */
  getRenderMetrics() {
    return this.metrics.renders;
  }

  /**
   * Get operation metrics
   *
   * @returns {Object} Operation metrics
   */
  getOperationMetrics() {
    return this.metrics.operations;
  }

  /**
   * Get resource metrics
   *
   * @returns {Object} Resource metrics
   */
  getResourceMetrics() {
    return this.metrics.resources;
  }

  /**
   * Get memory metrics
   *
   * @returns {Array} Memory metrics
   */
  getMemoryMetrics() {
    return this.metrics.memory;
  }

  /**
   * Get performance report
   *
   * @returns {Object} Performance report
   */
  getPerformanceReport() {
    const report = {
      timestamp: Date.now(),
      totalRenders: this.renderCount,
      slowestComponents: [],
      frequentComponents: [],
      slowestOperations: [],
      frequentOperations: [],
      largestResources: [],
      memoryUsage: null
    };

    // Get slowest components
    const components = Object.entries(this.metrics.renders);
    components.sort((a, b) => b[1].avgTime - a[1].avgTime);
    report.slowestComponents = components.slice(0, 5).map(([name, metric]) => ({
      name,
      avgTime: metric.avgTime,
      maxTime: metric.maxTime,
      count: metric.count
    }));

    // Get most frequently rendered components
    components.sort((a, b) => b[1].count - a[1].count);
    report.frequentComponents = components.slice(0, 5).map(([name, metric]) => ({
      name,
      count: metric.count,
      avgTime: metric.avgTime,
      totalTime: metric.totalTime
    }));

    // Get slowest operations
    const operations = Object.entries(this.metrics.operations);
    operations.sort((a, b) => b[1].avgTime - a[1].avgTime);
    report.slowestOperations = operations.slice(0, 5).map(([name, metric]) => ({
      name,
      avgTime: metric.avgTime,
      maxTime: metric.maxTime,
      count: metric.count
    }));

    // Get most frequent operations
    operations.sort((a, b) => b[1].count - a[1].count);
    report.frequentOperations = operations.slice(0, 5).map(([name, metric]) => ({
      name,
      count: metric.count,
      avgTime: metric.avgTime,
      totalTime: metric.totalTime
    }));

    // Get largest resources
    const resources = [];
    for (const [type, metric] of Object.entries(this.metrics.resources)) {
      for (const item of metric.items) {
        resources.push({
          type,
          url: item.url,
          size: item.size,
          time: item.time
        });
      }
    }
    resources.sort((a, b) => b.size - a.size);
    report.largestResources = resources.slice(0, 5);

    // Get memory usage
    if (this.metrics.memory.length > 0) {
      const lastMemory = this.metrics.memory[this.metrics.memory.length - 1];
      report.memoryUsage = {
        used: lastMemory.used,
        total: lastMemory.total,
        limit: lastMemory.limit,
        usedPercentage: lastMemory.usedPercentage,
        totalPercentage: lastMemory.totalPercentage
      };
    }

    return report;
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = performanceMonitor;
