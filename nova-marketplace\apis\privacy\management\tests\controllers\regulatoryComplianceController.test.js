/**
 * Regulatory Compliance Controller Tests
 *
 * This file contains tests for the regulatory compliance controller.
 */

const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const regulatoryComplianceController = require('../../controllers/regulatoryComplianceController');
const regulatoryComplianceService = require('../../services/regulatoryComplianceService');
const { RegulatoryFramework, ComplianceRequirement, ComplianceStatus } = require('../../models');

// Mock the regulatory compliance service
jest.mock('../../services/regulatoryComplianceService');

// Create Express app for testing
const app = express();
app.use(express.json());

// Define test routes
app.get('/frameworks', regulatoryComplianceController.getAllFrameworks);
app.get('/frameworks/:id', regulatoryComplianceController.getFrameworkById);
app.get('/frameworks/code/:code', regulatoryComplianceController.getFrameworkByCode);
app.get('/frameworks/:frameworkId/requirements', regulatoryComplianceController.getRequirementsByFramework);
app.get('/requirements/:id', regulatoryComplianceController.getRequirementById);
app.get('/status/:entityType/:entityId/:frameworkId', regulatoryComplianceController.getComplianceStatus);
app.patch('/status/:statusId/requirements/:requirementId', regulatoryComplianceController.updateRequirementStatus);
app.get('/reports/:entityType/:entityId/:frameworkId', regulatoryComplianceController.generateComplianceReport);
app.get('/mapping/:sourceFrameworkId/:targetFrameworkId', regulatoryComplianceController.mapRequirementsBetweenFrameworks);
app.get('/updates', regulatoryComplianceController.getRegulatoryUpdates);

describe('Regulatory Compliance Controller', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getAllFrameworks', () => {
    it('should return all frameworks', async () => {
      // Mock service response
      const mockFrameworks = {
        data: [
          {
            _id: 'framework-1',
            name: 'General Data Protection Regulation',
            code: 'GDPR',
            version: '2016',
            description: 'EU data protection regulation',
            category: 'privacy',
            regions: ['EU'],
            status: 'active'
          },
          {
            _id: 'framework-2',
            name: 'California Consumer Privacy Act',
            code: 'CCPA',
            version: '2018',
            description: 'California privacy law',
            category: 'privacy',
            regions: ['US-CA'],
            status: 'active'
          }
        ],
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          pages: 1
        }
      };

      regulatoryComplianceService.getAllFrameworks.mockResolvedValue(mockFrameworks);

      // Make request
      const response = await request(app).get('/frameworks');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockFrameworks.data);
      expect(response.body.pagination).toEqual(mockFrameworks.pagination);
      expect(regulatoryComplianceService.getAllFrameworks).toHaveBeenCalledTimes(1);
    });

    it('should handle errors', async () => {
      // Mock service error
      regulatoryComplianceService.getAllFrameworks.mockRejectedValue(new Error('Database error'));

      // Make request
      const response = await request(app).get('/frameworks');

      // Assertions
      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to get regulatory frameworks');
      expect(regulatoryComplianceService.getAllFrameworks).toHaveBeenCalledTimes(1);
    });
  });

  describe('getFrameworkById', () => {
    it('should return a framework by ID', async () => {
      // Mock service response
      const mockFramework = {
        _id: 'framework-1',
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      };

      regulatoryComplianceService.getFrameworkById.mockResolvedValue(mockFramework);

      // Make request
      const response = await request(app).get('/frameworks/framework-1');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockFramework);
      expect(regulatoryComplianceService.getFrameworkById).toHaveBeenCalledTimes(1);
      expect(regulatoryComplianceService.getFrameworkById).toHaveBeenCalledWith('framework-1');
    });

    it('should return 404 if framework not found', async () => {
      // Mock service error
      regulatoryComplianceService.getFrameworkById.mockRejectedValue(new Error('Regulatory framework with ID framework-999 not found'));

      // Make request
      const response = await request(app).get('/frameworks/framework-999');

      // Assertions
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to get regulatory framework');
      expect(regulatoryComplianceService.getFrameworkById).toHaveBeenCalledTimes(1);
    });
  });

  describe('getFrameworkByCode', () => {
    it('should return a framework by code', async () => {
      // Mock service response
      const mockFramework = {
        _id: 'framework-1',
        name: 'General Data Protection Regulation',
        code: 'GDPR',
        version: '2016',
        description: 'EU data protection regulation',
        category: 'privacy',
        regions: ['EU'],
        status: 'active'
      };

      regulatoryComplianceService.getFrameworkByCode.mockResolvedValue(mockFramework);

      // Make request
      const response = await request(app).get('/frameworks/code/GDPR');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockFramework);
      expect(regulatoryComplianceService.getFrameworkByCode).toHaveBeenCalledTimes(1);
      expect(regulatoryComplianceService.getFrameworkByCode).toHaveBeenCalledWith('GDPR');
    });

    it('should return 404 if framework not found', async () => {
      // Mock service error
      regulatoryComplianceService.getFrameworkByCode.mockRejectedValue(new Error('Regulatory framework with code NONEXISTENT not found'));

      // Make request
      const response = await request(app).get('/frameworks/code/NONEXISTENT');

      // Assertions
      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to get regulatory framework');
      expect(regulatoryComplianceService.getFrameworkByCode).toHaveBeenCalledTimes(1);
    });
  });

  describe('getRequirementsByFramework', () => {
    it('should return requirements for a framework', async () => {
      // Mock service response
      const mockRequirements = {
        data: [
          {
            _id: 'requirement-1',
            framework: 'framework-1',
            section: 'Art',
            number: '5',
            title: 'Principles relating to processing of personal data',
            description: 'Personal data shall be processed lawfully, fairly and in a transparent manner...',
            category: 'accountability',
            status: 'active'
          },
          {
            _id: 'requirement-2',
            framework: 'framework-1',
            section: 'Art',
            number: '6',
            title: 'Lawfulness of processing',
            description: 'Processing shall be lawful only if and to the extent that at least one of the following applies...',
            category: 'accountability',
            status: 'active'
          }
        ],
        pagination: {
          page: 1,
          limit: 50,
          total: 2,
          pages: 1
        }
      };

      regulatoryComplianceService.getRequirementsByFramework.mockResolvedValue(mockRequirements);

      // Make request
      const response = await request(app).get('/frameworks/framework-1/requirements');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockRequirements.data);
      expect(response.body.pagination).toEqual(mockRequirements.pagination);
      expect(regulatoryComplianceService.getRequirementsByFramework).toHaveBeenCalledTimes(1);
      expect(regulatoryComplianceService.getRequirementsByFramework).toHaveBeenCalledWith('framework-1', expect.any(Object));
    });
  });

  describe('getComplianceStatus', () => {
    it('should return compliance status for an entity', async () => {
      // Mock service response
      const mockStatus = {
        _id: 'status-1',
        entityType: 'organization',
        entityId: 'org-123',
        framework: 'framework-1',
        frameworkName: 'General Data Protection Regulation',
        frameworkCode: 'GDPR',
        frameworkVersion: '2016',
        status: 'in-progress',
        progress: 50,
        requirementStatuses: [
          {
            requirement: 'requirement-1',
            requirementCode: 'Art.5',
            requirementTitle: 'Principles relating to processing of personal data',
            status: 'compliant',
            notes: 'Implemented data processing principles'
          },
          {
            requirement: 'requirement-2',
            requirementCode: 'Art.6',
            requirementTitle: 'Lawfulness of processing',
            status: 'in-progress',
            notes: 'Working on implementing lawful basis for processing'
          }
        ],
        lastAssessment: new Date()
      };

      regulatoryComplianceService.getComplianceStatus.mockResolvedValue(mockStatus);

      // Make request
      const response = await request(app).get('/status/organization/org-123/framework-1');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Convert date strings to Date objects for comparison
      const responseData = { ...response.body.data };
      if (responseData.lastAssessment) {
        responseData.lastAssessment = new Date(responseData.lastAssessment);
      }

      expect(responseData).toEqual(mockStatus);
      expect(regulatoryComplianceService.getComplianceStatus).toHaveBeenCalledTimes(1);
      expect(regulatoryComplianceService.getComplianceStatus).toHaveBeenCalledWith('organization', 'org-123', 'framework-1');
    });
  });

  describe('updateRequirementStatus', () => {
    it('should update requirement status', async () => {
      // Mock service response
      const mockStatus = {
        _id: 'status-1',
        entityType: 'organization',
        entityId: 'org-123',
        framework: 'framework-1',
        frameworkName: 'General Data Protection Regulation',
        frameworkCode: 'GDPR',
        frameworkVersion: '2016',
        status: 'in-progress',
        progress: 50,
        requirementStatuses: [
          {
            requirement: 'requirement-1',
            requirementCode: 'Art.5',
            requirementTitle: 'Principles relating to processing of personal data',
            status: 'compliant',
            notes: 'Implemented data processing principles'
          }
        ],
        lastAssessment: new Date()
      };

      regulatoryComplianceService.updateRequirementStatus.mockResolvedValue(mockStatus);

      // Make request
      const response = await request(app)
        .patch('/status/status-1/requirements/requirement-1')
        .send({
          status: 'compliant',
          notes: 'Implemented data processing principles'
        });

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Convert date strings to Date objects for comparison
      const responseData = { ...response.body.data };
      if (responseData.lastAssessment) {
        responseData.lastAssessment = new Date(responseData.lastAssessment);
      }

      expect(responseData).toEqual(mockStatus);
      expect(regulatoryComplianceService.updateRequirementStatus).toHaveBeenCalledTimes(1);
      expect(regulatoryComplianceService.updateRequirementStatus).toHaveBeenCalledWith(
        'status-1',
        'requirement-1',
        expect.objectContaining({
          status: 'compliant',
          notes: 'Implemented data processing principles'
        })
      );
    });
  });

  describe('generateComplianceReport', () => {
    it('should generate a compliance report', async () => {
      // Mock service response
      const mockReport = {
        entityType: 'organization',
        entityId: 'org-123',
        framework: {
          id: 'framework-1',
          name: 'General Data Protection Regulation',
          code: 'GDPR',
          version: '2016',
          description: 'EU data protection regulation'
        },
        status: 'in-progress',
        progress: 50,
        sectionCompliance: {
          Art: {
            total: 2,
            compliant: 1,
            progress: 50
          }
        },
        requirementsBySection: {
          Art: [
            {
              requirementCode: 'Art.5',
              requirementTitle: 'Principles relating to processing of personal data',
              status: 'compliant',
              notes: 'Implemented data processing principles'
            },
            {
              requirementCode: 'Art.6',
              requirementTitle: 'Lawfulness of processing',
              status: 'in-progress',
              notes: 'Working on implementing lawful basis for processing'
            }
          ]
        },
        summary: {
          total: 2,
          compliant: 1,
          nonCompliant: 0,
          inProgress: 1,
          notStarted: 0,
          notApplicable: 0
        },
        generatedAt: new Date()
      };

      regulatoryComplianceService.generateComplianceReport.mockResolvedValue(mockReport);

      // Make request
      const response = await request(app).get('/reports/organization/org-123/framework-1');

      // Assertions
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);

      // Convert date strings to Date objects for comparison
      const responseData = { ...response.body.data };
      if (responseData.generatedAt) {
        responseData.generatedAt = new Date(responseData.generatedAt);
      }

      expect(responseData).toEqual(mockReport);
      expect(regulatoryComplianceService.generateComplianceReport).toHaveBeenCalledTimes(1);
      expect(regulatoryComplianceService.generateComplianceReport).toHaveBeenCalledWith('organization', 'org-123', 'framework-1');
    });
  });
});
