/**
 * Large File Routes
 * 
 * This file defines routes for handling large file uploads and downloads.
 */

const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { pipeline } = require('stream');
const { promisify } = require('util');
const { largeFileHandler, cleanupTempFile } = require('../middleware/largeFileHandler');
const largeFileConfig = require('../config/largeFileConfig');

// Promisify pipeline for async/await usage
const pipelineAsync = promisify(pipeline);

// Configure large file handler middleware
const fileHandler = largeFileHandler(largeFileConfig);

/**
 * @route POST /api/large-files/upload
 * @description Upload a large file using streaming
 * @access Public
 */
router.post('/upload', fileHandler, async (req, res) => {
  try {
    // Check for file upload errors
    if (req.fileErrors && req.fileErrors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: req.fileErrors
      });
    }
    
    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files were uploaded'
      });
    }
    
    // Process uploaded files
    const uploadedFiles = req.files.map(file => ({
      filename: file.originalname,
      size: file.size,
      mimetype: file.mimetype,
      path: file.path
    }));
    
    res.status(200).json({
      success: true,
      message: `${uploadedFiles.length} file(s) uploaded successfully`,
      files: uploadedFiles
    });
    
    // Optional: Clean up temporary files if auto-cleanup is enabled
    if (largeFileConfig.cleanup.enabled) {
      setTimeout(() => {
        req.files.forEach(file => cleanupTempFile(file));
      }, 3600000); // Clean up after 1 hour
    }
  } catch (error) {
    console.error('Error handling file upload:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during file upload'
    });
  }
});

/**
 * @route GET /api/large-files/download/:filename
 * @description Download a large file using streaming
 * @access Public
 */
router.get('/download/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(largeFileConfig.tempDir, filename);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }
    
    // Get file stats
    const stats = fs.statSync(filePath);
    
    // Set headers
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    
    // Stream the file to the response
    const readStream = fs.createReadStream(filePath);
    
    // Handle errors
    readStream.on('error', (error) => {
      console.error('Error streaming file:', error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: 'Error streaming file'
        });
      }
    });
    
    // Pipe the file to the response
    readStream.pipe(res);
  } catch (error) {
    console.error('Error handling file download:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during file download'
    });
  }
});

/**
 * @route GET /api/large-files/stream/:filename
 * @description Stream a large file for viewing in browser
 * @access Public
 */
router.get('/stream/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const filePath = path.join(largeFileConfig.tempDir, filename);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }
    
    // Get file stats
    const stats = fs.statSync(filePath);
    
    // Handle range requests for video streaming
    const range = req.headers.range;
    
    if (range) {
      const parts = range.replace(/bytes=/, '').split('-');
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : stats.size - 1;
      const chunkSize = (end - start) + 1;
      
      // Set headers for range request
      res.status(206);
      res.setHeader('Content-Range', `bytes ${start}-${end}/${stats.size}`);
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Length', chunkSize);
      
      // Determine content type based on file extension
      const ext = path.extname(filename).toLowerCase();
      const contentTypes = {
        '.mp4': 'video/mp4',
        '.webm': 'video/webm',
        '.ogg': 'video/ogg',
        '.mp3': 'audio/mpeg',
        '.wav': 'audio/wav',
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif'
      };
      
      res.setHeader('Content-Type', contentTypes[ext] || 'application/octet-stream');
      
      // Create read stream with range
      const readStream = fs.createReadStream(filePath, { start, end });
      readStream.pipe(res);
    } else {
      // Determine content type based on file extension
      const ext = path.extname(filename).toLowerCase();
      const contentTypes = {
        '.mp4': 'video/mp4',
        '.webm': 'video/webm',
        '.ogg': 'video/ogg',
        '.mp3': 'audio/mpeg',
        '.wav': 'audio/wav',
        '.pdf': 'application/pdf',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif'
      };
      
      // Set headers for full file
      res.setHeader('Content-Length', stats.size);
      res.setHeader('Content-Type', contentTypes[ext] || 'application/octet-stream');
      res.setHeader('Accept-Ranges', 'bytes');
      
      // Stream the file to the response
      const readStream = fs.createReadStream(filePath);
      readStream.pipe(res);
    }
  } catch (error) {
    console.error('Error handling file streaming:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during file streaming'
    });
  }
});

/**
 * @route POST /api/large-files/process
 * @description Process a large file in chunks
 * @access Public
 */
router.post('/process', fileHandler, async (req, res) => {
  try {
    // Check for file upload errors
    if (req.fileErrors && req.fileErrors.length > 0) {
      return res.status(400).json({
        success: false,
        errors: req.fileErrors
      });
    }
    
    // Check if files were uploaded
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files were uploaded'
      });
    }
    
    // Get the first file
    const file = req.files[0];
    
    // Process the file in chunks
    const result = await processLargeFile(file.path);
    
    // Clean up the temporary file
    cleanupTempFile(file);
    
    res.status(200).json({
      success: true,
      message: 'File processed successfully',
      result
    });
  } catch (error) {
    console.error('Error processing file:', error);
    res.status(500).json({
      success: false,
      error: 'Server error during file processing'
    });
  }
});

/**
 * Process a large file in chunks
 * @param {string} filePath - Path to the file
 * @returns {Object} Processing result
 */
async function processLargeFile(filePath) {
  return new Promise((resolve, reject) => {
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;
    const chunkSize = parseInt(largeFileConfig.chunkSize, 10);
    
    let processedBytes = 0;
    let lineCount = 0;
    
    // Create read stream
    const readStream = fs.createReadStream(filePath, {
      highWaterMark: chunkSize
    });
    
    // Process data chunks
    readStream.on('data', (chunk) => {
      // Example processing: count lines
      const chunkString = chunk.toString();
      const lines = chunkString.split('\n');
      lineCount += lines.length - 1; // Subtract 1 to account for potential partial lines
      
      processedBytes += chunk.length;
    });
    
    // Handle end of file
    readStream.on('end', () => {
      resolve({
        fileSize,
        processedBytes,
        lineCount,
        processingTime: Date.now() - stats.mtime.getTime()
      });
    });
    
    // Handle errors
    readStream.on('error', (error) => {
      reject(error);
    });
  });
}

module.exports = router;
