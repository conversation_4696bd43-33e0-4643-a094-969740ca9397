{"name": "nova-marketplace", "version": "1.0.0", "description": "NovaMarketplace GRC APIs for the NovaFuse API Superstore", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --coverage", "lint": "eslint .", "docs": "swagger-jsdoc -d swagger.js -o docs/swagger.json"}, "repository": {"type": "git", "url": "git+https://github.com/Dartan1983/nova-marketplace.git"}, "keywords": ["grc", "api", "governance", "risk", "compliance", "security", "privacy", "esg"], "author": "NovaFuse", "license": "MIT", "bugs": {"url": "https://github.com/Dartan1983/nova-marketplace/issues"}, "homepage": "https://github.com/Dartan1983/nova-marketplace#readme", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "joi": "^17.9.2", "morgan": "^1.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.3", "uuid": "^9.0.0", "winston": "^3.8.2"}, "devDependencies": {"eslint": "^8.40.0", "jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}}