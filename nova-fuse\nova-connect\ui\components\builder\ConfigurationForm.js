import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  TextField, 
  Grid, 
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  IconButton,
  Paper,
  Divider,
  Tooltip
} from '@mui/material';
import { 
  Add as AddIcon, 
  Delete as DeleteIcon,
  Help as HelpIcon
} from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

const backoffStrategies = [
  { value: 'linear', label: 'Linear' },
  { value: 'exponential', label: 'Exponential' },
  { value: 'fixed', label: 'Fixed' }
];

export default function ConfigurationForm({ connector, updateConnector }) {
  const [headers, setHeaders] = useState(connector.configuration.headers || {});
  const [newHeaderName, setNewHeaderName] = useState('');
  const [newHeaderValue, setNewHeaderValue] = useState('');

  const { control, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      baseUrl: connector.configuration.baseUrl,
      timeout: connector.configuration.timeout,
      retryPolicy: connector.configuration.retryPolicy
    }
  });

  const onSubmit = (data) => {
    updateConnector('configuration', {
      ...data,
      headers
    });
  };

  const handleAddHeader = () => {
    if (newHeaderName.trim()) {
      const updatedHeaders = {
        ...headers,
        [newHeaderName.trim()]: newHeaderValue
      };
      
      setHeaders(updatedHeaders);
      updateConnector('configuration', { headers: updatedHeaders });
      
      // Reset form
      setNewHeaderName('');
      setNewHeaderValue('');
    }
  };

  const handleDeleteHeader = (headerName) => {
    const { [headerName]: _, ...remainingHeaders } = headers;
    setHeaders(remainingHeaders);
    updateConnector('configuration', { headers: remainingHeaders });
  };

  return (
    <Box component="form" onChange={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Controller
            name="baseUrl"
            control={control}
            rules={{ 
              required: 'Base URL is required',
              pattern: {
                value: /^(https?:\/\/|{{.*}})/,
                message: 'Base URL must start with http://, https://, or contain template variables'
              }
            }}
            render={({ field }) => (
              <TextField
                {...field}
                label="Base URL"
                fullWidth
                required
                error={!!errors.baseUrl}
                helperText={errors.baseUrl?.message || 'API base URL (e.g., https://api.example.com)'}
                placeholder="https://api.example.com"
              />
            )}
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            You can use template variables like {{region}} that will be replaced at runtime.
          </Typography>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Default Headers
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Define default headers to include in all requests.
          </Typography>

          {/* Existing Headers */}
          {Object.keys(headers).length > 0 ? (
            <Paper variant="outlined" sx={{ p: 2, mb: 3, backgroundColor: 'background.default' }}>
              <Grid container spacing={2}>
                {Object.entries(headers).map(([headerName, headerValue]) => (
                  <Grid item xs={12} key={headerName}>
                    <Paper sx={{ p: 2, backgroundColor: 'background.paper' }}>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item xs={12} sm={4}>
                          <Typography variant="subtitle2">{headerName}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="body2" 
                            sx={{ 
                              fontFamily: 'monospace', 
                              backgroundColor: 'rgba(0,0,0,0.05)', 
                              p: 1, 
                              borderRadius: 1 
                            }}
                          >
                            {headerValue}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={2} sx={{ textAlign: 'right' }}>
                          <IconButton 
                            color="error" 
                            onClick={() => handleDeleteHeader(headerName)}
                            size="small"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          ) : (
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 3, 
                mb: 3, 
                backgroundColor: 'background.default',
                textAlign: 'center'
              }}
            >
              <Typography color="text.secondary">
                No default headers defined yet. Add headers below.
              </Typography>
            </Paper>
          )}

          {/* Add New Header */}
          <Paper sx={{ p: 3, backgroundColor: 'background.paper' }}>
            <Typography variant="subtitle1" gutterBottom>
              Add New Header
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={5}>
                <TextField
                  label="Header Name"
                  value={newHeaderName}
                  onChange={(e) => setNewHeaderName(e.target.value)}
                  fullWidth
                  placeholder="e.g., Content-Type, Accept"
                />
              </Grid>
              <Grid item xs={12} sm={5}>
                <TextField
                  label="Header Value"
                  value={newHeaderValue}
                  onChange={(e) => setNewHeaderValue(e.target.value)}
                  fullWidth
                  placeholder="e.g., application/json"
                />
              </Grid>
              <Grid item xs={12} sm={2}>
                <Button
                  variant="outlined"
                  startIcon={<AddIcon />}
                  onClick={handleAddHeader}
                  disabled={!newHeaderName.trim()}
                  sx={{ mt: 1 }}
                  fullWidth
                >
                  Add
                </Button>
              </Grid>
            </Grid>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              You can use template variables like {{apiToken}} that will be replaced with authentication values at runtime.
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Request Configuration
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Controller
                name="timeout"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Timeout (ms)"
                    fullWidth
                    type="number"
                    helperText="Request timeout in milliseconds (default: 30000)"
                    placeholder="30000"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="retryPolicy.maxRetries"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Max Retries"
                    fullWidth
                    type="number"
                    helperText="Maximum number of retry attempts (default: 3)"
                    placeholder="3"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <Controller
                name="retryPolicy.backoffStrategy"
                control={control}
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel id="backoff-strategy-label">Backoff Strategy</InputLabel>
                    <Select
                      {...field}
                      labelId="backoff-strategy-label"
                      label="Backoff Strategy"
                    >
                      {backoffStrategies.map((strategy) => (
                        <MenuItem key={strategy.value} value={strategy.value}>
                          {strategy.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                <strong>Linear:</strong> Increases wait time linearly between retries<br />
                <strong>Exponential:</strong> Doubles wait time between retries<br />
                <strong>Fixed:</strong> Uses the same wait time for all retries
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
