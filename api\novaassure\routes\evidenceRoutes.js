/**
 * Evidence Routes
 * 
 * This file defines the routes for evidence management.
 */

const express = require('express');
const router = express.Router();
const evidenceController = require('../controllers/evidenceController');
const authMiddleware = require('../middleware/authMiddleware');

/**
 * @swagger
 * /api/v1/novaassure/evidence:
 *   get:
 *     summary: Get all evidence
 *     description: Retrieve a list of all evidence
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: controlId
 *         schema:
 *           type: string
 *         description: Filter by control ID
 *       - in: query
 *         name: testExecutionId
 *         schema:
 *           type: string
 *         description: Filter by test execution ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [document, screenshot, log, api-response, other]
 *         description: Filter by evidence type
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Items per page
 *     responses:
 *       200:
 *         description: List of evidence
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authMiddleware.authenticate, evidenceController.getAllEvidence);

/**
 * @swagger
 * /api/v1/novaassure/evidence/{id}:
 *   get:
 *     summary: Get evidence by ID
 *     description: Retrieve evidence by its ID
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Evidence ID
 *     responses:
 *       200:
 *         description: Evidence details
 *       404:
 *         description: Evidence not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', authMiddleware.authenticate, evidenceController.getEvidenceById);

/**
 * @swagger
 * /api/v1/novaassure/evidence:
 *   post:
 *     summary: Create evidence
 *     description: Create a new evidence record
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *               - file
 *             properties:
 *               name:
 *                 type: string
 *                 description: Evidence name
 *               description:
 *                 type: string
 *                 description: Evidence description
 *               type:
 *                 type: string
 *                 enum: [document, screenshot, log, api-response, other]
 *                 description: Evidence type
 *               controlId:
 *                 type: string
 *                 description: Control ID
 *               testExecutionId:
 *                 type: string
 *                 description: Test execution ID
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Evidence file
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       201:
 *         description: Evidence created
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', authMiddleware.authenticate, evidenceController.createEvidence);

/**
 * @swagger
 * /api/v1/novaassure/evidence/{id}:
 *   put:
 *     summary: Update evidence
 *     description: Update an existing evidence record
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Evidence ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Evidence name
 *               description:
 *                 type: string
 *                 description: Evidence description
 *               type:
 *                 type: string
 *                 enum: [document, screenshot, log, api-response, other]
 *                 description: Evidence type
 *               controlId:
 *                 type: string
 *                 description: Control ID
 *               testExecutionId:
 *                 type: string
 *                 description: Test execution ID
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *     responses:
 *       200:
 *         description: Evidence updated
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Evidence not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put('/:id', authMiddleware.authenticate, evidenceController.updateEvidence);

/**
 * @swagger
 * /api/v1/novaassure/evidence/{id}:
 *   delete:
 *     summary: Delete evidence
 *     description: Delete an existing evidence record
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Evidence ID
 *     responses:
 *       200:
 *         description: Evidence deleted
 *       404:
 *         description: Evidence not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete('/:id', authMiddleware.authenticate, evidenceController.deleteEvidence);

/**
 * @swagger
 * /api/v1/novaassure/evidence/{id}/file:
 *   get:
 *     summary: Get evidence file
 *     description: Download the evidence file
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Evidence ID
 *     responses:
 *       200:
 *         description: Evidence file
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: Evidence not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id/file', authMiddleware.authenticate, evidenceController.getEvidenceFile);

/**
 * @swagger
 * /api/v1/novaassure/evidence/{id}/verify:
 *   get:
 *     summary: Verify evidence
 *     description: Verify evidence integrity using blockchain
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Evidence ID
 *     responses:
 *       200:
 *         description: Evidence verification result
 *       404:
 *         description: Evidence not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id/verify', authMiddleware.authenticate, evidenceController.verifyEvidence);

/**
 * @swagger
 * /api/v1/novaassure/evidence/collect/api:
 *   post:
 *     summary: Collect API evidence
 *     description: Collect evidence from an API
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - apiUrl
 *               - controlId
 *             properties:
 *               name:
 *                 type: string
 *                 description: Evidence name
 *               description:
 *                 type: string
 *                 description: Evidence description
 *               apiUrl:
 *                 type: string
 *                 description: API URL
 *               method:
 *                 type: string
 *                 enum: [GET, POST, PUT, DELETE]
 *                 default: GET
 *                 description: HTTP method
 *               headers:
 *                 type: object
 *                 description: HTTP headers
 *               body:
 *                 type: object
 *                 description: Request body
 *               controlId:
 *                 type: string
 *                 description: Control ID
 *               testExecutionId:
 *                 type: string
 *                 description: Test execution ID
 *     responses:
 *       201:
 *         description: Evidence collected
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/collect/api', authMiddleware.authenticate, evidenceController.collectApiEvidence);

/**
 * @swagger
 * /api/v1/novaassure/evidence/collect/system:
 *   post:
 *     summary: Collect system evidence
 *     description: Collect evidence from system logs or configuration
 *     tags: [Evidence]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - systemType
 *               - controlId
 *             properties:
 *               name:
 *                 type: string
 *                 description: Evidence name
 *               description:
 *                 type: string
 *                 description: Evidence description
 *               systemType:
 *                 type: string
 *                 enum: [os, database, application, network, cloud]
 *                 description: System type
 *               query:
 *                 type: string
 *                 description: System query
 *               controlId:
 *                 type: string
 *                 description: Control ID
 *               testExecutionId:
 *                 type: string
 *                 description: Test execution ID
 *     responses:
 *       201:
 *         description: Evidence collected
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/collect/system', authMiddleware.authenticate, evidenceController.collectSystemEvidence);

module.exports = router;
