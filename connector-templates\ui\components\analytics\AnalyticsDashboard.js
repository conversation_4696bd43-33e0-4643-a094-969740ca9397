/**
 * Analytics Dashboard Component
 * 
 * This component displays analytics data in a dashboard format.
 */

import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  CircularProgress, 
  Divider, 
  Grid, 
  Paper, 
  Typography 
} from '@mui/material';
import { analyticsApi } from '../../services/api';
import UsageChart from './UsageChart';
import ResponseTimeChart from './ResponseTimeChart';
import ErrorsChart from './ErrorsChart';
import TopEndpointsTable from './TopEndpointsTable';
import RecentRequestsTable from './RecentRequestsTable';
import AnalyticsSummary from './AnalyticsSummary';

const AnalyticsDashboard = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  
  useEffect(() => {
    loadAnalytics();
  }, []);
  
  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await analyticsApi.getDashboardAnalytics();
      setAnalytics(response.data);
    } catch (error) {
      console.error('Error loading analytics:', error);
      setError(error.response?.data?.message || error.message || 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" color="error" gutterBottom>
          Error Loading Analytics
        </Typography>
        <Typography variant="body1">
          {error}
        </Typography>
      </Box>
    );
  }
  
  if (!analytics) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="body1">
          No analytics data available.
        </Typography>
      </Box>
    );
  }
  
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        API Analytics Dashboard
      </Typography>
      
      <Typography variant="body1" color="textSecondary" paragraph>
        Overview of API usage, performance, and errors for the last 30 days.
      </Typography>
      
      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid item xs={12}>
          <AnalyticsSummary summary={analytics.summary} />
        </Grid>
        
        {/* Usage Chart */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                API Requests Over Time
              </Typography>
              <Box sx={{ height: 300 }}>
                <UsageChart data={analytics.charts.usageByDate} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Response Time Chart */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Response Time Over Time
              </Typography>
              <Box sx={{ height: 300 }}>
                <ResponseTimeChart data={analytics.charts.responseTimeByDate} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Errors Chart */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Errors Over Time
              </Typography>
              <Box sx={{ height: 300 }}>
                <ErrorsChart data={analytics.charts.errorsByDate} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Usage by Hour */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Usage by Hour of Day
              </Typography>
              <Box sx={{ height: 300 }}>
                <UsageChart 
                  data={analytics.charts.usageByHour.map((count, index) => ({ 
                    date: `${index}:00`, 
                    count 
                  }))} 
                  xAxisLabel="Hour" 
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Top Endpoints */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Top Endpoints by Usage
              </Typography>
              <TopEndpointsTable endpoints={analytics.topEndpoints} />
            </CardContent>
          </Card>
        </Grid>
        
        {/* Slowest Endpoints */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Slowest Endpoints
              </Typography>
              <TopEndpointsTable 
                endpoints={analytics.slowestEndpoints.map(endpoint => ({
                  endpoint: endpoint.endpoint,
                  count: `${Math.round(endpoint.averageResponseTime)}ms`
                }))} 
                countLabel="Avg. Response Time"
              />
            </CardContent>
          </Card>
        </Grid>
        
        {/* Recent Requests */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Requests
              </Typography>
              <RecentRequestsTable requests={analytics.recentRequests} />
            </CardContent>
          </Card>
        </Grid>
        
        {/* Recent Errors */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Errors
              </Typography>
              <RecentRequestsTable 
                requests={analytics.recentErrors.map(error => ({
                  ...error,
                  responseStatus: error.errorCode,
                  responseTime: null
                }))} 
                showErrorMessage
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AnalyticsDashboard;
