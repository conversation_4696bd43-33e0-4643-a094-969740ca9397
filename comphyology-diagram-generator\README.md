# Comphyology Patent Diagram Generator

A dynamic template system for creating consistent, well-designed diagrams for the Comphyology Patent.

## Features

- Dynamic, component-based diagram generation
- Consistent styling across all diagrams
- Proper spacing and padding for all elements
- Text positioned in front of connecting lines
- Component numbers in black boxes in corners
- Export diagrams as PNG files

## Getting Started

### Prerequisites

- Node.js 14.x or higher
- npm or yarn

### Installation

1. Clone this repository
2. Install dependencies:

```bash
cd comphyology-diagram-generator
npm install
# or
yarn install
```

### Running the Development Server

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Building for Production

```bash
npm run build
# or
yarn build
```

### Exporting as Static Site

```bash
npm run export
# or
yarn export
```

The static site will be generated in the `out` directory.

## Usage

1. Select a diagram from the dropdown menu
2. View the diagram in the container
3. Click "Download Diagram" to save the diagram as a PNG file

## Creating New Diagrams

To create a new diagram:

1. Create a new file in the `components/diagrams` directory
2. Define the elements and connections for your diagram
3. Add your diagram to the `diagramComponents` array in `pages/index.tsx`

Example:

```jsx
// components/diagrams/MyNewDiagram.tsx
import React from 'react';
import DiagramTemplate from '../DiagramTemplate';

const elements = [
  {
    id: 'element1',
    top: 50,
    left: 300,
    width: 200,
    text: 'My Element',
    number: '1',
    bold: true,
    fontSize: '18px',
    backgroundColor: '#e6f7ff'
  },
  // Add more elements...
];

const connections = [
  {
    start: { x: 300, y: 100 },
    end: { x: 400, y: 200 },
    type: 'arrow'
  },
  // Add more connections...
];

const MyNewDiagram: React.FC = () => {
  return (
    <DiagramTemplate 
      elements={elements} 
      connections={connections} 
      width="800px" 
      height="600px" 
    />
  );
};

export default MyNewDiagram;
```

Then add it to the `diagramComponents` array in `pages/index.tsx`:

```jsx
// pages/index.tsx
import MyNewDiagram from '../components/diagrams/MyNewDiagram';

// ...

const diagramComponents = [
  // ...existing diagrams
  {
    id: 'my-new-diagram',
    name: 'My New Diagram',
    component: <MyNewDiagram />
  }
];
```

## Design Specifications

- Master container bubble: Minimum 20px padding on all sides
- Text content bubbles: 15px padding on all sides
- Component numbers: Black background with white text in top left corner
- Text: Positioned in front of connecting lines and arrows
- Lines and arrows: Positioned behind text bubbles
- Spacing: Minimum 30px between connected elements
- Typography: Principles text should be bold and larger
- Colors: Consistent color scheme across all diagrams

## Diagram List

1. High-Level System Architecture Diagram
2. Finite Universe Paradigm Visualization
3. Three-Body Problem Reframing Diagram
4. UUFT Equation Flow Diagram
5. Trinity Equation Visualization
6. Meta-Field Schema Diagram
7. Cross-Domain Integration Matrix
8. Pattern Translation Process Diagram
9. 13 NovaFuse Components Diagram
10. 3-6-9-12-13 Alignment Architecture Diagram
11. Cross-Module Data Processing Pipeline
12. Cyber-Safety Incident Response Workflow
13. Healthcare Implementation Diagram
14. Dashboard and Visualization Examples
15. Hardware Architecture Diagram
