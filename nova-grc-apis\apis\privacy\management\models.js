/**
 * Models for the Privacy Management API
 */

// Sample data for data processing activities
const dataProcessingActivities = [
  {
    id: 'dpa-0001',
    name: 'Customer Data Processing',
    description: 'Processing of customer personal data for account management and service delivery',
    purpose: 'Account management and service delivery',
    dataCategories: ['Contact information', 'Account details', 'Transaction history'],
    dataSubjects: ['Customers'],
    legalBasis: 'contract',
    retentionPeriod: '7 years after account closure',
    processingOperations: ['Collection', 'Storage', 'Use', 'Disclosure to service providers'],
    crossBorderTransfers: [
      {
        country: 'United States',
        mechanism: 'Standard Contractual Clauses',
        adequacyDecision: false
      }
    ],
    securityMeasures: ['Encryption', 'Access controls', 'Regular security assessments'],
    dataControllers: ['Marketing Department', 'Customer Service'],
    dataProcessors: ['Cloud Storage Provider', 'Email Service Provider'],
    dpia: {
      required: true,
      completed: true,
      completionDate: '2023-01-15',
      reviewDate: '2024-01-15'
    },
    risks: [
      {
        description: 'Unauthorized access to customer data',
        likelihood: 'low',
        impact: 'high',
        mitigations: 'Strong access controls and encryption'
      }
    ],
    status: 'active',
    createdAt: '2023-01-10T09:00:00Z',
    updatedAt: '2023-06-15T11:00:00Z'
  },
  {
    id: 'dpa-0002',
    name: 'Employee Data Processing',
    description: 'Processing of employee personal data for HR management',
    purpose: 'HR management and payroll',
    dataCategories: ['Contact information', 'Employment details', 'Financial information'],
    dataSubjects: ['Employees'],
    legalBasis: 'legal-obligation',
    retentionPeriod: '7 years after employment ends',
    processingOperations: ['Collection', 'Storage', 'Use', 'Disclosure to tax authorities'],
    crossBorderTransfers: [],
    securityMeasures: ['Encryption', 'Access controls', 'Regular security assessments'],
    dataControllers: ['HR Department'],
    dataProcessors: ['Payroll Service Provider', 'HR Management System Provider'],
    dpia: {
      required: false,
      completed: false,
      completionDate: null,
      reviewDate: null
    },
    risks: [],
    status: 'active',
    createdAt: '2023-02-10T10:00:00Z',
    updatedAt: '2023-02-10T10:00:00Z'
  }
];

// Sample data for data subject rights requests
const dataSubjectRequests = [
  {
    id: 'dsr-0001',
    requestType: 'access',
    requestDate: '2023-05-10T14:30:00Z',
    dataSubjectName: 'John Doe',
    dataSubjectEmail: '<EMAIL>',
    dataSubjectId: 'user-123',
    identityVerified: true,
    verificationMethod: 'email verification',
    verificationDate: '2023-05-10T15:00:00Z',
    requestDetails: 'Request for all personal data held about me',
    status: 'completed',
    assignedTo: 'Privacy Team',
    dueDate: '2023-06-09T14:30:00Z', // 30 days after request
    completionDate: '2023-05-25T11:45:00Z',
    responseDetails: 'Provided data export via secure link',
    affectedSystems: ['CRM', 'Marketing Database', 'Customer Support System'],
    notes: 'Verified identity through account email and security questions',
    createdAt: '2023-05-10T14:30:00Z',
    updatedAt: '2023-05-25T11:45:00Z'
  },
  {
    id: 'dsr-0002',
    requestType: 'erasure',
    requestDate: '2023-06-15T09:45:00Z',
    dataSubjectName: 'Jane Smith',
    dataSubjectEmail: '<EMAIL>',
    dataSubjectId: 'user-456',
    identityVerified: true,
    verificationMethod: 'government ID',
    verificationDate: '2023-06-15T10:30:00Z',
    requestDetails: 'Request for deletion of all my personal data',
    status: 'in-progress',
    assignedTo: 'Privacy Team',
    dueDate: '2023-07-15T09:45:00Z', // 30 days after request
    completionDate: null,
    responseDetails: null,
    affectedSystems: ['CRM', 'Marketing Database', 'Customer Support System', 'Analytics Platform'],
    notes: 'Checking for legal basis to retain certain data for compliance purposes',
    createdAt: '2023-06-15T09:45:00Z',
    updatedAt: '2023-06-20T14:00:00Z'
  }
];

// Sample data for consent records
const consentRecords = [
  {
    id: 'con-0001',
    dataSubjectId: 'user-123',
    dataSubjectName: 'John Doe',
    dataSubjectEmail: '<EMAIL>',
    consentType: 'marketing',
    consentDescription: 'Consent to receive marketing communications',
    consentGiven: true,
    consentDate: '2023-03-15T10:30:00Z',
    consentExpiryDate: '2024-03-15T10:30:00Z',
    consentProof: 'IP: ***********, User-Agent: Mozilla/5.0...',
    consentVersion: '1.2',
    consentMethod: 'online-form',
    privacyNoticeVersion: '2.0',
    withdrawalDate: null,
    withdrawalMethod: null,
    status: 'active',
    createdAt: '2023-03-15T10:30:00Z',
    updatedAt: '2023-03-15T10:30:00Z'
  },
  {
    id: 'con-0002',
    dataSubjectId: 'user-456',
    dataSubjectName: 'Jane Smith',
    dataSubjectEmail: '<EMAIL>',
    consentType: 'marketing',
    consentDescription: 'Consent to receive marketing communications',
    consentGiven: false,
    consentDate: '2023-04-10T15:45:00Z',
    consentExpiryDate: null,
    consentProof: 'IP: ***********, User-Agent: Mozilla/5.0...',
    consentVersion: '1.2',
    consentMethod: 'online-form',
    privacyNoticeVersion: '2.0',
    withdrawalDate: '2023-05-20T09:15:00Z',
    withdrawalMethod: 'email-request',
    status: 'withdrawn',
    createdAt: '2023-04-10T15:45:00Z',
    updatedAt: '2023-05-20T09:15:00Z'
  }
];

// Sample data for privacy notices
const privacyNotices = [
  {
    id: 'pn-0001',
    title: 'Website Privacy Notice',
    version: '2.0',
    effectiveDate: '2023-01-01',
    lastUpdated: '2022-12-15',
    status: 'active',
    audience: 'website-visitors',
    language: 'en',
    format: 'html',
    content: '<h1>Privacy Notice</h1><p>This privacy notice explains how we collect and use your personal data...</p>',
    contentUrl: 'https://example.com/privacy-notice',
    previousVersions: [
      {
        version: '1.0',
        effectiveDate: '2022-01-01',
        retirementDate: '2022-12-31',
        contentUrl: 'https://example.com/privacy-notice-v1'
      }
    ],
    reviewCycle: 'annual',
    nextReviewDate: '2023-12-15',
    approvedBy: 'Legal Department',
    approvalDate: '2022-12-10',
    createdAt: '2022-12-10T09:00:00Z',
    updatedAt: '2022-12-15T14:30:00Z'
  },
  {
    id: 'pn-0002',
    title: 'Mobile App Privacy Notice',
    version: '1.0',
    effectiveDate: '2023-02-15',
    lastUpdated: '2023-02-01',
    status: 'active',
    audience: 'app-users',
    language: 'en',
    format: 'html',
    content: '<h1>Mobile App Privacy Notice</h1><p>This privacy notice explains how we collect and use your personal data in our mobile app...</p>',
    contentUrl: 'https://example.com/app-privacy-notice',
    previousVersions: [],
    reviewCycle: 'annual',
    nextReviewDate: '2024-02-01',
    approvedBy: 'Legal Department',
    approvalDate: '2023-02-01',
    createdAt: '2023-01-15T11:30:00Z',
    updatedAt: '2023-02-01T16:45:00Z'
  }
];

// Sample data for data breaches
const dataBreaches = [
  {
    id: 'db-0001',
    title: 'Unauthorized Access to Customer Database',
    description: 'Unauthorized access to customer database detected through security monitoring',
    breachType: 'unauthorized-access',
    detectionDate: '2023-07-10T08:30:00Z',
    occurrenceDate: '2023-07-09T23:15:00Z',
    affectedDataCategories: ['Contact information', 'Account details'],
    affectedDataSubjects: ['Customers'],
    approximateSubjectsCount: 1500,
    potentialImpact: 'medium',
    containmentStatus: 'contained',
    containmentDate: '2023-07-10T12:45:00Z',
    containmentMeasures: 'Blocked unauthorized access, reset affected credentials',
    rootCause: 'Compromised employee credentials',
    remedialActions: [
      'Reset all employee credentials',
      'Implemented additional authentication factors',
      'Enhanced monitoring'
    ],
    notificationStatus: {
      authorities: {
        required: true,
        completed: true,
        date: '2023-07-11T10:00:00Z',
        recipient: 'Data Protection Authority'
      },
      dataSubjects: {
        required: true,
        completed: true,
        date: '2023-07-12T15:30:00Z',
        method: 'Email notification'
      }
    },
    investigationStatus: 'completed',
    investigationReport: 'Investigation completed on 2023-07-20. Root cause identified as phishing attack.',
    status: 'closed',
    createdAt: '2023-07-10T08:45:00Z',
    updatedAt: '2023-07-25T14:30:00Z'
  },
  {
    id: 'db-0002',
    title: 'Lost Company Laptop',
    description: 'Employee reported lost company laptop containing customer data',
    breachType: 'lost-device',
    detectionDate: '2023-08-05T09:15:00Z',
    occurrenceDate: '2023-08-04T18:30:00Z',
    affectedDataCategories: ['Contact information', 'Account details'],
    affectedDataSubjects: ['Customers'],
    approximateSubjectsCount: 200,
    potentialImpact: 'low',
    containmentStatus: 'contained',
    containmentDate: '2023-08-05T10:00:00Z',
    containmentMeasures: 'Remote wipe of device executed successfully',
    rootCause: 'Physical security failure',
    remedialActions: [
      'Refreshed employee training on device security',
      'Verified encryption on all company devices'
    ],
    notificationStatus: {
      authorities: {
        required: false,
        completed: false,
        date: null,
        recipient: null
      },
      dataSubjects: {
        required: false,
        completed: false,
        date: null,
        method: null
      }
    },
    investigationStatus: 'completed',
    investigationReport: 'Investigation confirmed device was encrypted and remote wipe was successful. No evidence of data access.',
    status: 'closed',
    createdAt: '2023-08-05T09:30:00Z',
    updatedAt: '2023-08-10T11:45:00Z'
  }
];

// Export all models
module.exports = {
  dataProcessingActivities,
  dataSubjectRequests,
  consentRecords,
  privacyNotices,
  dataBreaches
};
