/**
 * Authentication API Tests
 *
 * Tests for the authentication API endpoints.
 */

const { request, app } = require('./setup');

// Mock user data
const mockUser = {
  firstName: 'Test',
  lastName: 'User',
  email: `test.user.${Date.now()}@example.com`,
  password: '$2b$10$X7o4.KTXb4xLNU/8/7NWmeEHYS.RUcj.M97qQJlzjbBCIGw1uDKXa', // hashed 'Password123!'
  company: 'Test Company'
};

// Login credentials
const loginCredentials = {
  email: mockUser.email,
  password: 'Password123!' // Plain text for login
};

describe('Authentication API', () => {
  let authToken;
  let userId;

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          email: mockUser.email,
          password: 'Password123!', // Plain text for registration
          company: mockUser.company
        })
        .expect('Content-Type', /json/)
        .expect(201);

      // Save user ID for later tests
      userId = response.body.id || response.body._id;

      // Verify response contains user data without password
      expect(response.body).toHaveProperty('email', mockUser.email);
      expect(response.body).toHaveProperty('firstName', mockUser.firstName);
      expect(response.body).toHaveProperty('lastName', mockUser.lastName);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should return 400 when required fields are missing', async () => {
      const invalidUser = {
        email: '<EMAIL>'
        // Missing required fields
      };

      await request(app)
        .post('/api/auth/register')
        .send(invalidUser)
        .expect('Content-Type', /json/)
        .expect(400);
    });

    it('should return 409 when email is already registered', async () => {
      // Skip if registration failed
      if (!userId) {
        return;
      }

      await request(app)
        .post('/api/auth/register')
        .send({
          firstName: mockUser.firstName,
          lastName: mockUser.lastName,
          email: mockUser.email, // Same email as before
          password: 'Password123!',
          company: mockUser.company
        })
        .expect('Content-Type', /json/)
        .expect(409);
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials and return token', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send(loginCredentials)
        .expect('Content-Type', /json/)
        .expect(200);

      // Save auth token for later tests
      authToken = response.body.token;

      // Verify response contains token and user data
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('email', mockUser.email);
    });

    it('should return 401 with invalid credentials', async () => {
      await request(app)
        .post('/api/auth/login')
        .send({
          email: mockUser.email,
          password: 'WrongPassword123!'
        })
        .expect('Content-Type', /json/)
        .expect(401);
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user profile with valid token', async () => {
      // Skip if login failed
      if (!authToken) {
        return;
      }

      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect('Content-Type', /json/)
        .expect(200);

      // Verify response contains user data
      expect(response.body).toHaveProperty('email', mockUser.email);
      expect(response.body).toHaveProperty('firstName', mockUser.firstName);
      expect(response.body).toHaveProperty('lastName', mockUser.lastName);
    });

    it('should return 401 without authentication', async () => {
      await request(app)
        .get('/api/auth/me')
        .expect('Content-Type', /json/)
        .expect(401);
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully with valid token', async () => {
      // Skip if login failed
      if (!authToken) {
        return;
      }

      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Verify token is invalidated by trying to access a protected route
      await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(401);
    });
  });
});
