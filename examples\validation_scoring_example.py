"""
Example of using validation result scoring and confidence levels with the Validator Manager.

This example demonstrates how to use validation result scoring and confidence levels
with the enhanced Validator Manager.
"""

import os
import sys
import json
import logging

# Add the parent directory to the path so we can import the UCECS modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.ucecs.core.validator_manager import ValidatorManager, ValidationMode, ValidationLevel, ValidationResult

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Run the validation scoring example."""
    # Create a validator manager
    validator_manager = ValidatorManager()
    
    try:
        # Create a custom validation script for security controls assessment
        script_id = "security_controls_validator"
        script_content = """
"""\"
Security Controls Validator

This script validates security controls and provides a detailed score.

<AUTHOR>
@version 1.0.0
@confidence_level HIGH
@tags security, compliance, controls, assessment
\"""

# Get the evidence data
data = evidence.get('data', {})
controls = data.get('controls', {})

# Initialize validation variables
total_controls = len(controls)
passed_controls = 0
failed_controls = 0
warning_controls = 0
not_applicable_controls = 0
errors = []
warnings = []
control_scores = {}
total_score = 0
max_possible_score = 0

# Define control weights by category
control_weights = {
    'access_control': 5,
    'data_protection': 5,
    'network_security': 4,
    'vulnerability_management': 4,
    'incident_response': 3,
    'logging_monitoring': 4,
    'configuration_management': 3,
    'awareness_training': 2
}

# Evaluate each control
for control_id, control in controls.items():
    status = control.get('status', 'unknown')
    category = control.get('category', 'other')
    weight = control_weights.get(category, 1)
    max_possible_score += weight * 100
    
    # Calculate score based on status
    if status == 'passed':
        passed_controls += 1
        control_score = weight * 100
    elif status == 'failed':
        failed_controls += 1
        control_score = 0
        errors.append(f"Control {control_id} ({control.get('name', 'Unknown')}) failed: {control.get('details', 'No details provided')}")
    elif status == 'warning':
        warning_controls += 1
        control_score = weight * 50
        warnings.append(f"Control {control_id} ({control.get('name', 'Unknown')}) has warnings: {control.get('details', 'No details provided')}")
    elif status == 'not_applicable':
        not_applicable_controls += 1
        control_score = 0
        max_possible_score -= weight * 100  # Don't count in the denominator
    else:
        control_score = 0
        errors.append(f"Control {control_id} ({control.get('name', 'Unknown')}) has unknown status: {status}")
    
    # Store the control score
    control_scores[control_id] = {
        'name': control.get('name', 'Unknown'),
        'category': category,
        'weight': weight,
        'status': status,
        'score': control_score
    }
    
    # Add to total score
    total_score += control_score

# Calculate final score as a percentage
final_score = (total_score / max_possible_score * 100) if max_possible_score > 0 else 0

# Determine validation result based on score
is_valid = final_score >= 70  # Pass if score is at least 70%

# Determine confidence level based on number of controls
if total_controls >= 50:
    confidence_level = ValidationLevel.CRITICAL
elif total_controls >= 30:
    confidence_level = ValidationLevel.HIGH
elif total_controls >= 10:
    confidence_level = ValidationLevel.MEDIUM
else:
    confidence_level = ValidationLevel.LOW

# Create the validation result
result = ValidationResult(
    validator_id='security_controls_validator',
    is_valid=is_valid,
    confidence_level=confidence_level,
    score=final_score,
    details={
        'total_controls': total_controls,
        'passed_controls': passed_controls,
        'failed_controls': failed_controls,
        'warning_controls': warning_controls,
        'not_applicable_controls': not_applicable_controls,
        'control_scores': control_scores,
        'total_score': total_score,
        'max_possible_score': max_possible_score
    },
    errors=errors,
    warnings=warnings
)
"""
        
        # Create the validation script
        script = validator_manager.create_validation_script(
            script_id=script_id,
            content=script_content
        )
        
        logger.info(f"Created validation script: {script_id}")
        
        # Create a sample security controls evidence (high score)
        high_score_evidence = {
            'type': 'security',
            'source': 'security_assessment',
            'data': {
                'controls': {
                    'AC-1': {
                        'name': 'Access Control Policy and Procedures',
                        'category': 'access_control',
                        'status': 'passed',
                        'details': 'Access control policies and procedures are documented and reviewed annually.'
                    },
                    'AC-2': {
                        'name': 'Account Management',
                        'category': 'access_control',
                        'status': 'passed',
                        'details': 'Account management procedures are implemented and followed.'
                    },
                    'AC-3': {
                        'name': 'Access Enforcement',
                        'category': 'access_control',
                        'status': 'warning',
                        'details': 'Access enforcement is implemented but some exceptions were found.'
                    },
                    'DP-1': {
                        'name': 'Data Protection Policy',
                        'category': 'data_protection',
                        'status': 'passed',
                        'details': 'Data protection policies are documented and reviewed.'
                    },
                    'DP-2': {
                        'name': 'Data Encryption',
                        'category': 'data_protection',
                        'status': 'passed',
                        'details': 'Data encryption is implemented for sensitive data.'
                    },
                    'NS-1': {
                        'name': 'Network Segmentation',
                        'category': 'network_security',
                        'status': 'passed',
                        'details': 'Network segmentation is implemented.'
                    },
                    'NS-2': {
                        'name': 'Firewall Configuration',
                        'category': 'network_security',
                        'status': 'warning',
                        'details': 'Firewall is configured but some rules need review.'
                    },
                    'VM-1': {
                        'name': 'Vulnerability Scanning',
                        'category': 'vulnerability_management',
                        'status': 'passed',
                        'details': 'Regular vulnerability scanning is performed.'
                    },
                    'VM-2': {
                        'name': 'Patch Management',
                        'category': 'vulnerability_management',
                        'status': 'warning',
                        'details': 'Patch management process exists but some systems are not up to date.'
                    },
                    'IR-1': {
                        'name': 'Incident Response Plan',
                        'category': 'incident_response',
                        'status': 'passed',
                        'details': 'Incident response plan is documented and tested.'
                    }
                }
            }
        }
        
        # Validate the high score evidence
        high_score_result = validator_manager.validate(script_id, high_score_evidence)
        
        logger.info(f"High score validation result: {json.dumps(high_score_result.to_dict(), indent=2)}")
        
        # Create a sample security controls evidence (low score)
        low_score_evidence = {
            'type': 'security',
            'source': 'security_assessment',
            'data': {
                'controls': {
                    'AC-1': {
                        'name': 'Access Control Policy and Procedures',
                        'category': 'access_control',
                        'status': 'failed',
                        'details': 'Access control policies are not documented.'
                    },
                    'AC-2': {
                        'name': 'Account Management',
                        'category': 'access_control',
                        'status': 'warning',
                        'details': 'Account management procedures exist but are not consistently followed.'
                    },
                    'AC-3': {
                        'name': 'Access Enforcement',
                        'category': 'access_control',
                        'status': 'failed',
                        'details': 'Access enforcement is not implemented.'
                    },
                    'DP-1': {
                        'name': 'Data Protection Policy',
                        'category': 'data_protection',
                        'status': 'warning',
                        'details': 'Data protection policies exist but are outdated.'
                    },
                    'DP-2': {
                        'name': 'Data Encryption',
                        'category': 'data_protection',
                        'status': 'failed',
                        'details': 'Data encryption is not implemented for sensitive data.'
                    },
                    'NS-1': {
                        'name': 'Network Segmentation',
                        'category': 'network_security',
                        'status': 'failed',
                        'details': 'Network segmentation is not implemented.'
                    },
                    'NS-2': {
                        'name': 'Firewall Configuration',
                        'category': 'network_security',
                        'status': 'warning',
                        'details': 'Firewall is configured but rules are overly permissive.'
                    },
                    'VM-1': {
                        'name': 'Vulnerability Scanning',
                        'category': 'vulnerability_management',
                        'status': 'warning',
                        'details': 'Vulnerability scanning is performed but not regularly.'
                    },
                    'VM-2': {
                        'name': 'Patch Management',
                        'category': 'vulnerability_management',
                        'status': 'failed',
                        'details': 'No formal patch management process exists.'
                    },
                    'IR-1': {
                        'name': 'Incident Response Plan',
                        'category': 'incident_response',
                        'status': 'not_applicable',
                        'details': 'Organization is too small for formal incident response plan.'
                    }
                }
            }
        }
        
        # Validate the low score evidence
        low_score_result = validator_manager.validate(script_id, low_score_evidence)
        
        logger.info(f"Low score validation result: {json.dumps(low_score_result.to_dict(), indent=2)}")
        
        # Compare the results
        logger.info("\nValidation Result Comparison:")
        logger.info(f"High Score: {high_score_result.score:.2f}% (Valid: {high_score_result.is_valid}, Confidence: {high_score_result.confidence_level.name})")
        logger.info(f"Low Score: {low_score_result.score:.2f}% (Valid: {low_score_result.is_valid}, Confidence: {low_score_result.confidence_level.name})")
        
        # Clean up
        validator_manager.delete_validation_script(script_id)
        
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    main()
