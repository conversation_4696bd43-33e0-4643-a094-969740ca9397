/**
 * Enhanced Dashboard Example
 *
 * This example demonstrates how to use the enhanced dashboard UI with
 * machine learning components for the Finite Universe Principle defense system.
 */

const {
  // Complete defense system
  createCompleteDefenseSystem,
  
  // Dashboard UI
  createDashboardUI,
  
  // Machine learning components
  createAnomalyDetector,
  createPredictiveAnalytics
} = require('../src/quantum/finite-universe-principle');

/**
 * Start the enhanced dashboard UI
 */
async function startEnhancedDashboardUI() {
  console.log('Starting Enhanced Dashboard UI Example...');

  // Create complete defense system
  const defenseSystem = createCompleteDefenseSystem({
    enableLogging: false, // Disable logging for cleaner output
    enableMonitoring: true,
    strictMode: true
  });

  // Create ML components
  const anomalyDetector = createAnomalyDetector({
    enableLogging: false, // Disable logging for cleaner output
    anomalyThreshold: 3.0,
    learningRate: 0.1,
    historyLength: 100
  });
  
  const predictiveAnalytics = createPredictiveAnalytics({
    enableLogging: false, // Disable logging for cleaner output
    forecastHorizon: 10,
    confidenceLevel: 0.95,
    seasonalityPeriod: 24
  });

  // Create enhanced dashboard UI
  const dashboardUI = createDashboardUI({
    port: 3000,
    updateInterval: 1000,
    enableLogging: true,
    enableRealTimeUpdates: true,
    dashboard: defenseSystem.monitoringDashboard,
    anomalyDetector,
    predictiveAnalytics
  });

  // Register event listeners for ML components
  setupMLEventListeners(anomalyDetector, predictiveAnalytics);

  // Start the dashboard UI
  await dashboardUI.start();
  console.log(`Enhanced Dashboard UI started on http://localhost:${dashboardUI.options.port}`);

  // Create mock NovaFuse components
  const mockNovaComponents = {
    novaConnect: createMockNovaConnect(),
    novaVision: createMockNovaVision(),
    novaDNA: createMockNovaDNA(),
    novaShield: createMockNovaShield()
  };

  // Register NovaFuse components
  defenseSystem.registerNovaComponents(mockNovaComponents);

  // Generate some test data
  generateTestData(defenseSystem, anomalyDetector, predictiveAnalytics);

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('Stopping Enhanced Dashboard UI...');
    dashboardUI.stop();
    defenseSystem.dispose();
    process.exit();
  });
}

/**
 * Set up event listeners for ML components
 * @param {Object} anomalyDetector - Anomaly detector
 * @param {Object} predictiveAnalytics - Predictive analytics
 */
function setupMLEventListeners(anomalyDetector, predictiveAnalytics) {
  // Listen for anomalies
  anomalyDetector.on('anomalies-detected', (data) => {
    console.log('Anomalies detected:', data);
  });

  // Listen for predicted violations
  predictiveAnalytics.on('violations-predicted', (data) => {
    console.log('Violations predicted:', data);
  });
}

/**
 * Generate test data for the dashboard
 * @param {Object} defenseSystem - Complete defense system
 * @param {Object} anomalyDetector - Anomaly detector
 * @param {Object} predictiveAnalytics - Predictive analytics
 */
function generateTestData(defenseSystem, anomalyDetector, predictiveAnalytics) {
  // Generate normal data at regular intervals
  const normalDataInterval = setInterval(async () => {
    // Process cyber domain data
    await defenseSystem.processData({
      securityScore: 7 + Math.random() * 2,
      threatLevel: 2 + Math.random(),
      encryptionStrength: Math.random() > 0.95 ? Infinity : 256
    }, 'cyber');
    
    // Process financial domain data
    await defenseSystem.processData({
      balance: 1000 + Math.random() * 1000,
      interestRate: 0.05 + Math.random() * 0.05,
      transactionVolume: Math.random() > 0.95 ? 1e20 : Math.random() * 1000000
    }, 'financial');
    
    // Process medical domain data
    await defenseSystem.processData({
      heartRate: 70 + Math.random() * 10,
      bloodPressure: 120 + Math.random() * 10,
      temperature: 36.5 + Math.random()
    }, 'medical');
    
    // Get current metrics
    const metrics = defenseSystem.monitoringDashboard.getCurrentMetrics();
    
    // Process metrics through ML components
    anomalyDetector.processData(metrics);
    predictiveAnalytics.processMetrics(metrics);
  }, 2000);

  // Generate anomalous data occasionally
  const anomalousDataInterval = setInterval(async () => {
    if (Math.random() > 0.7) {
      console.log('Generating anomalous data...');
      
      // Process anomalous cyber domain data
      await defenseSystem.processData({
        securityScore: Math.random() > 0.5 ? 2 : 9.5, // Very low or very high
        threatLevel: Math.random() > 0.5 ? 0.5 : 9, // Very low or very high
        encryptionStrength: Math.random() > 0.5 ? 0 : Infinity // Invalid values
      }, 'cyber');
      
      // Process anomalous financial domain data
      await defenseSystem.processData({
        balance: Math.random() > 0.5 ? -1000 : 1e10, // Negative or very large
        interestRate: Math.random() > 0.5 ? -0.1 : 2.0, // Negative or very high
        transactionVolume: Math.random() > 0.5 ? -100 : 1e20 // Negative or very large
      }, 'financial');
      
      // Process anomalous medical domain data
      await defenseSystem.processData({
        heartRate: Math.random() > 0.5 ? 30 : 200, // Very low or very high
        bloodPressure: Math.random() > 0.5 ? 60 : 200, // Very low or very high
        temperature: Math.random() > 0.5 ? 34 : 40 // Very low or very high
      }, 'medical');
    }
  }, 10000);

  // Clean up intervals on process exit
  process.on('SIGINT', () => {
    clearInterval(normalDataInterval);
    clearInterval(anomalousDataInterval);
  });
}

/**
 * Create a mock NovaConnect component
 * @returns {Object} - Mock NovaConnect component
 */
function createMockNovaConnect() {
  return {
    connect: async (config) => {
      return { status: 'connected', config };
    },
    processPolicy: async (policy) => {
      return { status: 'processed', policy };
    }
  };
}

/**
 * Create a mock NovaVision component
 * @returns {Object} - Mock NovaVision component
 */
function createMockNovaVision() {
  return {
    analyze: async (data) => {
      return { status: 'analyzed', data };
    }
  };
}

/**
 * Create a mock NovaDNA component
 * @returns {Object} - Mock NovaDNA component
 */
function createMockNovaDNA() {
  return {
    process: async (data) => {
      return { status: 'processed', data };
    }
  };
}

/**
 * Create a mock NovaShield component
 * @returns {Object} - Mock NovaShield component
 */
function createMockNovaShield() {
  return {
    protect: async (data) => {
      return { status: 'protected', data };
    }
  };
}

// Start the enhanced dashboard UI
startEnhancedDashboardUI().catch(error => {
  console.error('Error starting Enhanced Dashboard UI:', error);
});
