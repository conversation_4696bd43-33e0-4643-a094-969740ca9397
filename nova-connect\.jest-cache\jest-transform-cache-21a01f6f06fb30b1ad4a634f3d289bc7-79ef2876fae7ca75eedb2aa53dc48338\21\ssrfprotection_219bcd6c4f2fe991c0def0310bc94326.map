{"version": 3, "names": ["URL", "require", "dns", "promises", "net", "SSRFProtection", "constructor", "options", "allowedHosts", "allowedDomains", "allowedProtocols", "allowPrivateIPs", "isSafeUrl", "url", "parsedUrl", "includes", "protocol", "console", "warn", "length", "isHostAllowed", "some", "host", "hostname", "isDomainAllowed", "domain", "startsWith", "baseDomain", "substring", "endsWith", "isIP", "isPrivateIP", "addresses", "resolve", "address", "error", "ip", "addAllowedHosts", "hosts", "Array", "isArray", "push", "addAllowedDomains", "domains", "setAllowedProtocols", "protocols", "setAllowPrivateIPs", "allow", "module", "exports"], "sources": ["ssrf-protection.js"], "sourcesContent": ["/**\n * NovaFuse Universal API Connector SSRF Protection\n * \n * This module provides SSRF (Server-Side Request Forgery) protection for the UAC.\n */\n\nconst { URL } = require('url');\nconst dns = require('dns').promises;\nconst net = require('net');\n\n/**\n * SSRF Protection Utility\n * \n * Checks if a URL is safe to connect to by validating against a whitelist\n * and checking for private IP addresses.\n */\nclass SSRFProtection {\n  constructor(options = {}) {\n    this.options = {\n      allowedHosts: [],\n      allowedDomains: [],\n      allowedProtocols: ['https:'],\n      allowPrivateIPs: false,\n      ...options\n    };\n  }\n\n  /**\n   * Check if a URL is safe to connect to\n   * @param {string} url - URL to check\n   * @returns {Promise<boolean>} - Whether the URL is safe\n   */\n  async isSafeUrl(url) {\n    try {\n      // Parse URL\n      const parsedUrl = new URL(url);\n      \n      // Check protocol\n      if (!this.options.allowedProtocols.includes(parsedUrl.protocol)) {\n        console.warn(`SSRF Protection: Blocked URL with disallowed protocol: ${parsedUrl.protocol}`);\n        return false;\n      }\n      \n      // Check hostname against whitelist if enabled\n      if (this.options.allowedHosts.length > 0) {\n        const isHostAllowed = this.options.allowedHosts.some(host => \n          parsedUrl.hostname === host\n        );\n        \n        if (!isHostAllowed) {\n          // Check if hostname matches any allowed domain patterns\n          const isDomainAllowed = this.options.allowedDomains.some(domain => {\n            // Allow wildcards (e.g., *.example.com)\n            if (domain.startsWith('*.')) {\n              const baseDomain = domain.substring(2);\n              return parsedUrl.hostname.endsWith(baseDomain);\n            }\n            return parsedUrl.hostname === domain;\n          });\n          \n          if (!isDomainAllowed) {\n            console.warn(`SSRF Protection: Blocked URL with disallowed host: ${parsedUrl.hostname}`);\n            return false;\n          }\n        }\n      }\n      \n      // Check for private IP addresses\n      if (!this.options.allowPrivateIPs) {\n        // Check if hostname is an IP address\n        if (net.isIP(parsedUrl.hostname)) {\n          if (this.isPrivateIP(parsedUrl.hostname)) {\n            console.warn(`SSRF Protection: Blocked URL with private IP: ${parsedUrl.hostname}`);\n            return false;\n          }\n        } else {\n          // Resolve hostname to IP address\n          try {\n            const addresses = await dns.resolve(parsedUrl.hostname);\n            \n            // Check if any resolved IP is private\n            for (const address of addresses) {\n              if (this.isPrivateIP(address)) {\n                console.warn(`SSRF Protection: Blocked URL with hostname resolving to private IP: ${address}`);\n                return false;\n              }\n            }\n          } catch (error) {\n            console.error(`SSRF Protection: Error resolving hostname: ${parsedUrl.hostname}`, error);\n            return false;\n          }\n        }\n      }\n      \n      return true;\n    } catch (error) {\n      console.error('SSRF Protection: Error checking URL:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if an IP address is private\n   * @param {string} ip - IP address to check\n   * @returns {boolean} - Whether the IP is private\n   */\n  isPrivateIP(ip) {\n    // Check for loopback addresses\n    if (ip === '127.0.0.1' || ip === '::1') {\n      return true;\n    }\n    \n    // Check for private IPv4 ranges\n    if (ip.startsWith('10.') || \n        ip.startsWith('172.16.') || ip.startsWith('172.17.') || \n        ip.startsWith('172.18.') || ip.startsWith('172.19.') || \n        ip.startsWith('172.20.') || ip.startsWith('172.21.') || \n        ip.startsWith('172.22.') || ip.startsWith('172.23.') || \n        ip.startsWith('172.24.') || ip.startsWith('172.25.') || \n        ip.startsWith('172.26.') || ip.startsWith('172.27.') || \n        ip.startsWith('172.28.') || ip.startsWith('172.29.') || \n        ip.startsWith('172.30.') || ip.startsWith('172.31.') || \n        ip.startsWith('192.168.')) {\n      return true;\n    }\n    \n    // Check for link-local addresses\n    if (ip.startsWith('169.254.')) {\n      return true;\n    }\n    \n    // Check for private IPv6 ranges\n    if (ip.startsWith('fc00:') || ip.startsWith('fd00:')) {\n      return true;\n    }\n    \n    return false;\n  }\n\n  /**\n   * Add allowed hosts to the whitelist\n   * @param {string|string[]} hosts - Host(s) to add\n   */\n  addAllowedHosts(hosts) {\n    if (Array.isArray(hosts)) {\n      this.options.allowedHosts = [...this.options.allowedHosts, ...hosts];\n    } else {\n      this.options.allowedHosts.push(hosts);\n    }\n  }\n\n  /**\n   * Add allowed domains to the whitelist\n   * @param {string|string[]} domains - Domain(s) to add\n   */\n  addAllowedDomains(domains) {\n    if (Array.isArray(domains)) {\n      this.options.allowedDomains = [...this.options.allowedDomains, ...domains];\n    } else {\n      this.options.allowedDomains.push(domains);\n    }\n  }\n\n  /**\n   * Set allowed protocols\n   * @param {string|string[]} protocols - Protocol(s) to allow\n   */\n  setAllowedProtocols(protocols) {\n    if (Array.isArray(protocols)) {\n      this.options.allowedProtocols = protocols;\n    } else {\n      this.options.allowedProtocols = [protocols];\n    }\n  }\n\n  /**\n   * Allow or disallow private IP addresses\n   * @param {boolean} allow - Whether to allow private IPs\n   */\n  setAllowPrivateIPs(allow) {\n    this.options.allowPrivateIPs = allow;\n  }\n}\n\nmodule.exports = SSRFProtection;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAM;EAAEA;AAAI,CAAC,GAAGC,OAAO,CAAC,KAAK,CAAC;AAC9B,MAAMC,GAAG,GAAGD,OAAO,CAAC,KAAK,CAAC,CAACE,QAAQ;AACnC,MAAMC,GAAG,GAAGH,OAAO,CAAC,KAAK,CAAC;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,cAAc,CAAC;EACnBC,WAAWA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACxB,IAAI,CAACA,OAAO,GAAG;MACbC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,gBAAgB,EAAE,CAAC,QAAQ,CAAC;MAC5BC,eAAe,EAAE,KAAK;MACtB,GAAGJ;IACL,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE,MAAMK,SAASA,CAACC,GAAG,EAAE;IACnB,IAAI;MACF;MACA,MAAMC,SAAS,GAAG,IAAId,GAAG,CAACa,GAAG,CAAC;;MAE9B;MACA,IAAI,CAAC,IAAI,CAACN,OAAO,CAACG,gBAAgB,CAACK,QAAQ,CAACD,SAAS,CAACE,QAAQ,CAAC,EAAE;QAC/DC,OAAO,CAACC,IAAI,CAAC,0DAA0DJ,SAAS,CAACE,QAAQ,EAAE,CAAC;QAC5F,OAAO,KAAK;MACd;;MAEA;MACA,IAAI,IAAI,CAACT,OAAO,CAACC,YAAY,CAACW,MAAM,GAAG,CAAC,EAAE;QACxC,MAAMC,aAAa,GAAG,IAAI,CAACb,OAAO,CAACC,YAAY,CAACa,IAAI,CAACC,IAAI,IACvDR,SAAS,CAACS,QAAQ,KAAKD,IACzB,CAAC;QAED,IAAI,CAACF,aAAa,EAAE;UAClB;UACA,MAAMI,eAAe,GAAG,IAAI,CAACjB,OAAO,CAACE,cAAc,CAACY,IAAI,CAACI,MAAM,IAAI;YACjE;YACA,IAAIA,MAAM,CAACC,UAAU,CAAC,IAAI,CAAC,EAAE;cAC3B,MAAMC,UAAU,GAAGF,MAAM,CAACG,SAAS,CAAC,CAAC,CAAC;cACtC,OAAOd,SAAS,CAACS,QAAQ,CAACM,QAAQ,CAACF,UAAU,CAAC;YAChD;YACA,OAAOb,SAAS,CAACS,QAAQ,KAAKE,MAAM;UACtC,CAAC,CAAC;UAEF,IAAI,CAACD,eAAe,EAAE;YACpBP,OAAO,CAACC,IAAI,CAAC,sDAAsDJ,SAAS,CAACS,QAAQ,EAAE,CAAC;YACxF,OAAO,KAAK;UACd;QACF;MACF;;MAEA;MACA,IAAI,CAAC,IAAI,CAAChB,OAAO,CAACI,eAAe,EAAE;QACjC;QACA,IAAIP,GAAG,CAAC0B,IAAI,CAAChB,SAAS,CAACS,QAAQ,CAAC,EAAE;UAChC,IAAI,IAAI,CAACQ,WAAW,CAACjB,SAAS,CAACS,QAAQ,CAAC,EAAE;YACxCN,OAAO,CAACC,IAAI,CAAC,iDAAiDJ,SAAS,CAACS,QAAQ,EAAE,CAAC;YACnF,OAAO,KAAK;UACd;QACF,CAAC,MAAM;UACL;UACA,IAAI;YACF,MAAMS,SAAS,GAAG,MAAM9B,GAAG,CAAC+B,OAAO,CAACnB,SAAS,CAACS,QAAQ,CAAC;;YAEvD;YACA,KAAK,MAAMW,OAAO,IAAIF,SAAS,EAAE;cAC/B,IAAI,IAAI,CAACD,WAAW,CAACG,OAAO,CAAC,EAAE;gBAC7BjB,OAAO,CAACC,IAAI,CAAC,uEAAuEgB,OAAO,EAAE,CAAC;gBAC9F,OAAO,KAAK;cACd;YACF;UACF,CAAC,CAAC,OAAOC,KAAK,EAAE;YACdlB,OAAO,CAACkB,KAAK,CAAC,8CAA8CrB,SAAS,CAACS,QAAQ,EAAE,EAAEY,KAAK,CAAC;YACxF,OAAO,KAAK;UACd;QACF;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;AACA;AACA;EACEJ,WAAWA,CAACK,EAAE,EAAE;IACd;IACA,IAAIA,EAAE,KAAK,WAAW,IAAIA,EAAE,KAAK,KAAK,EAAE;MACtC,OAAO,IAAI;IACb;;IAEA;IACA,IAAIA,EAAE,CAACV,UAAU,CAAC,KAAK,CAAC,IACpBU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,SAAS,CAAC,IACpDU,EAAE,CAACV,UAAU,CAAC,UAAU,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;;IAEA;IACA,IAAIU,EAAE,CAACV,UAAU,CAAC,UAAU,CAAC,EAAE;MAC7B,OAAO,IAAI;IACb;;IAEA;IACA,IAAIU,EAAE,CAACV,UAAU,CAAC,OAAO,CAAC,IAAIU,EAAE,CAACV,UAAU,CAAC,OAAO,CAAC,EAAE;MACpD,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;AACA;EACEW,eAAeA,CAACC,KAAK,EAAE;IACrB,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACxB,IAAI,CAAC/B,OAAO,CAACC,YAAY,GAAG,CAAC,GAAG,IAAI,CAACD,OAAO,CAACC,YAAY,EAAE,GAAG8B,KAAK,CAAC;IACtE,CAAC,MAAM;MACL,IAAI,CAAC/B,OAAO,CAACC,YAAY,CAACiC,IAAI,CAACH,KAAK,CAAC;IACvC;EACF;;EAEA;AACF;AACA;AACA;EACEI,iBAAiBA,CAACC,OAAO,EAAE;IACzB,IAAIJ,KAAK,CAACC,OAAO,CAACG,OAAO,CAAC,EAAE;MAC1B,IAAI,CAACpC,OAAO,CAACE,cAAc,GAAG,CAAC,GAAG,IAAI,CAACF,OAAO,CAACE,cAAc,EAAE,GAAGkC,OAAO,CAAC;IAC5E,CAAC,MAAM;MACL,IAAI,CAACpC,OAAO,CAACE,cAAc,CAACgC,IAAI,CAACE,OAAO,CAAC;IAC3C;EACF;;EAEA;AACF;AACA;AACA;EACEC,mBAAmBA,CAACC,SAAS,EAAE;IAC7B,IAAIN,KAAK,CAACC,OAAO,CAACK,SAAS,CAAC,EAAE;MAC5B,IAAI,CAACtC,OAAO,CAACG,gBAAgB,GAAGmC,SAAS;IAC3C,CAAC,MAAM;MACL,IAAI,CAACtC,OAAO,CAACG,gBAAgB,GAAG,CAACmC,SAAS,CAAC;IAC7C;EACF;;EAEA;AACF;AACA;AACA;EACEC,kBAAkBA,CAACC,KAAK,EAAE;IACxB,IAAI,CAACxC,OAAO,CAACI,eAAe,GAAGoC,KAAK;EACtC;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG5C,cAAc", "ignoreList": []}