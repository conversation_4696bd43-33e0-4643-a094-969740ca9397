/**
 * Cyber-Safety Data Service
 *
 * This service provides data for the Cyber-Safety fusion visualizations.
 * It connects to various data sources from the NovaFuse platform.
 */

import axios from 'axios';
import { generateRandomData } from '../components/visualizations/test/mockDataService';

/**
 * Get authentication headers for API requests
 * @returns {Object} - Authentication headers
 */
const getAuthHeaders = () => {
  // Get token from local storage or session
  const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');

  if (token) {
    return {
      Authorization: `Bearer ${token}`
    };
  }

  return {};
};

/**
 * Check if an error is retryable
 * @param {Error} error - The error to check
 * @returns {boolean} - Whether the error is retryable
 */
const isRetryableError = (error) => {
  // Network errors are retryable
  if (!error.response) {
    return true;
  }

  // Server errors (5xx) are retryable
  if (error.response.status >= 500 && error.response.status < 600) {
    return true;
  }

  // Too many requests (429) is retryable
  if (error.response.status === 429) {
    return true;
  }

  // Other errors are not retryable
  return false;
};

// API base URL
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/api';

// API endpoints
const ENDPOINTS = {
  triDomainTensor: '/cyber-safety/visualizations/tri-domain-tensor',
  harmonyIndex: '/cyber-safety/visualizations/harmony-index',
  riskControlFusion: '/cyber-safety/visualizations/risk-control-fusion',
  resonanceSpectrogram: '/cyber-safety/visualizations/resonance-spectrogram',
  unifiedComplianceSecurity: '/cyber-safety/visualizations/unified-compliance-security'
};

// Cache for data
const dataCache = {
  triDomainTensor: null,
  harmonyIndex: null,
  riskControlFusion: null,
  resonanceSpectrogram: null,
  unifiedComplianceSecurity: null
};

// Cache timestamps
const cacheTimestamps = {
  triDomainTensor: 0,
  harmonyIndex: 0,
  riskControlFusion: 0,
  resonanceSpectrogram: 0,
  unifiedComplianceSecurity: 0
};

// Cache TTL in milliseconds (5 minutes)
const CACHE_TTL = 5 * 60 * 1000;

/**
 * Fetch Cyber-Safety data for a specific visualization type
 * @param {string} visualizationType - The type of visualization
 * @param {Object} options - Options for the data fetch
 * @returns {Promise<Object>} - A promise that resolves to the data
 */
export const fetchCyberSafetyData = async (visualizationType, options = {}) => {
  const {
    useCache = true,
    forceRefresh = false,
    useFallback = true,
    timeframe = '30d',
    domains = null,
    historyPoints = null,
    categories = null,
    frameworks = null,
    predictionHorizon = null,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  // Check if we have valid cached data
  const now = Date.now();
  if (
    useCache &&
    !forceRefresh &&
    dataCache[visualizationType] &&
    now - cacheTimestamps[visualizationType] < CACHE_TTL
  ) {
    return dataCache[visualizationType];
  }

  // Build query parameters
  const queryParams = {};

  // Add common parameters
  if (timeframe) queryParams.timeframe = timeframe;

  // Add visualization-specific parameters
  if (domains) queryParams.domains = Array.isArray(domains) ? domains.join(',') : domains;

  if (visualizationType === 'harmonyIndex' && historyPoints) {
    queryParams.historyPoints = historyPoints;
  }

  if (visualizationType === 'riskControlFusion' && categories) {
    queryParams.categories = Array.isArray(categories) ? categories.join(',') : categories;
  }

  if (visualizationType === 'resonanceSpectrogram' && predictionHorizon) {
    queryParams.predictionHorizon = predictionHorizon;
  }

  if (visualizationType === 'unifiedComplianceSecurity' && frameworks) {
    queryParams.frameworks = Array.isArray(frameworks) ? frameworks.join(',') : frameworks;
  }

  // Implement retry logic
  let retries = 0;
  let lastError = null;

  while (retries <= maxRetries) {
    try {
      // Fetch data from API
      const endpoint = ENDPOINTS[visualizationType];
      if (!endpoint) {
        throw new Error(`Unknown visualization type: ${visualizationType}`);
      }

      const response = await axios.get(`${API_BASE_URL}${endpoint}`, {
        params: queryParams,
        // Add authorization header if available
        headers: getAuthHeaders()
      });

      // Validate response data
      if (!response.data || !response.data.success) {
        throw new Error(`Invalid response from ${endpoint}: ${JSON.stringify(response.data)}`);
      }

      // Extract data from response
      const responseData = response.data.data;

      // Update cache
      dataCache[visualizationType] = responseData;
      cacheTimestamps[visualizationType] = now;

      // Log successful data fetch
      console.log(`Successfully fetched ${visualizationType} data`);

      return responseData;
    } catch (error) {
      lastError = error;
      console.error(`Error fetching ${visualizationType} data (attempt ${retries + 1}/${maxRetries + 1}):`, error);

      // Check if we should retry
      if (retries < maxRetries && isRetryableError(error)) {
        retries++;
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retries - 1)));
      } else {
        break;
      }
    }
  }

  // All retries failed, use fallback data if enabled
  if (useFallback) {
    console.log(`Using fallback data for ${visualizationType}`);

    // Map visualization type to fallback data type
    const fallbackType = visualizationType === 'harmonyIndex'
      ? 'cyber_safety_harmony_index'
      : visualizationType === 'triDomainTensor'
      ? 'tri_domain_tensor'
      : visualizationType === 'riskControlFusion'
      ? 'risk_control_fusion_map'
      : visualizationType === 'resonanceSpectrogram'
      ? 'cyber_safety_resonance_spectrogram'
      : visualizationType === 'unifiedComplianceSecurity'
      ? 'unified_compliance_security_visualizer'
      : null;

    if (fallbackType) {
      // Generate fallback data with the same parameters
      const fallbackData = generateRandomData(fallbackType, {
        timeframe,
        domains: domains ? (Array.isArray(domains) ? domains : domains.split(',')) : null,
        historyPoints,
        categories: categories ? (Array.isArray(categories) ? categories : categories.split(',')) : null,
        frameworks: frameworks ? (Array.isArray(frameworks) ? frameworks : frameworks.split(',')) : null,
        predictionHorizon
      });

      // Update cache with fallback data
      dataCache[visualizationType] = fallbackData;
      cacheTimestamps[visualizationType] = now;

      return fallbackData;
    }
  }

  // Re-throw the last error if no fallback
  throw lastError;
};

/**
 * Fetch real-time Cyber-Safety data
 * @param {string} visualizationType - The type of visualization
 * @param {Object} options - Options for the data fetch
 * @returns {Promise<Object>} - A promise that resolves to the real-time data
 */
export const fetchRealTimeData = async (visualizationType, options = {}) => {
  const {
    useFallback = true,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  // Implement retry logic
  let retries = 0;
  let lastError = null;

  while (retries <= maxRetries) {
    try {
      const endpoint = ENDPOINTS[visualizationType];
      if (!endpoint) {
        throw new Error(`Unknown visualization type: ${visualizationType}`);
      }

      const response = await axios.get(`${API_BASE_URL}${endpoint}/real-time`, {
        headers: getAuthHeaders()
      });

      // Validate response data
      if (!response.data || !response.data.success) {
        throw new Error(`Invalid response from ${endpoint}/real-time: ${JSON.stringify(response.data)}`);
      }

      // Extract data from response
      return response.data.data;
    } catch (error) {
      lastError = error;
      console.error(`Error fetching real-time ${visualizationType} data (attempt ${retries + 1}/${maxRetries + 1}):`, error);

      // Check if we should retry
      if (retries < maxRetries && isRetryableError(error)) {
        retries++;
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retries - 1)));
      } else {
        break;
      }
    }
  }

  // All retries failed, use fallback data if enabled
  if (useFallback) {
    console.log(`Using fallback real-time data for ${visualizationType}`);

    // Map visualization type to fallback data type
    const fallbackType = visualizationType === 'harmonyIndex'
      ? 'cyber_safety_harmony_index'
      : visualizationType === 'triDomainTensor'
      ? 'tri_domain_tensor'
      : visualizationType === 'riskControlFusion'
      ? 'risk_control_fusion_map'
      : visualizationType === 'resonanceSpectrogram'
      ? 'cyber_safety_resonance_spectrogram'
      : visualizationType === 'unifiedComplianceSecurity'
      ? 'unified_compliance_security_visualizer'
      : null;

    if (fallbackType) {
      // Generate fallback data with real-time parameters
      return generateRandomData(fallbackType, { isRealTime: true });
    }
  }

  // Re-throw the last error if no fallback
  throw lastError;
};

/**
 * Fetch historical Cyber-Safety data
 * @param {string} visualizationType - The type of visualization
 * @param {Object} timeRange - The time range for historical data
 * @param {Object} options - Options for the data fetch
 * @returns {Promise<Object>} - A promise that resolves to the historical data
 */
export const fetchHistoricalData = async (visualizationType, timeRange, options = {}) => {
  const {
    useFallback = true,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  // Implement retry logic
  let retries = 0;
  let lastError = null;

  while (retries <= maxRetries) {
    try {
      const endpoint = ENDPOINTS[visualizationType];
      if (!endpoint) {
        throw new Error(`Unknown visualization type: ${visualizationType}`);
      }

      const response = await axios.get(`${API_BASE_URL}${endpoint}/historical`, {
        params: timeRange,
        headers: getAuthHeaders()
      });

      // Validate response data
      if (!response.data || !response.data.success) {
        throw new Error(`Invalid response from ${endpoint}/historical: ${JSON.stringify(response.data)}`);
      }

      // Extract data from response
      return response.data.data;
    } catch (error) {
      lastError = error;
      console.error(`Error fetching historical ${visualizationType} data (attempt ${retries + 1}/${maxRetries + 1}):`, error);

      // Check if we should retry
      if (retries < maxRetries && isRetryableError(error)) {
        retries++;
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retries - 1)));
      } else {
        break;
      }
    }
  }

  // All retries failed, use fallback data if enabled
  if (useFallback) {
    console.log(`Using fallback historical data for ${visualizationType}`);

    // Map visualization type to fallback data type
    const fallbackType = visualizationType === 'harmonyIndex'
      ? 'cyber_safety_harmony_index'
      : visualizationType === 'triDomainTensor'
      ? 'tri_domain_tensor'
      : visualizationType === 'riskControlFusion'
      ? 'risk_control_fusion_map'
      : visualizationType === 'resonanceSpectrogram'
      ? 'cyber_safety_resonance_spectrogram'
      : visualizationType === 'unifiedComplianceSecurity'
      ? 'unified_compliance_security_visualizer'
      : null;

    if (fallbackType) {
      // Generate fallback data with historical parameters
      return generateRandomData(fallbackType, {
        isHistorical: true,
        startDate: timeRange.startDate,
        endDate: timeRange.endDate,
        interval: timeRange.interval
      });
    }
  }

  // Re-throw the last error if no fallback
  throw lastError;
};

/**
 * Fetch aggregated Cyber-Safety data
 * @param {string} visualizationType - The type of visualization
 * @param {Object} aggregationParams - Parameters for data aggregation
 * @param {Object} options - Options for the data fetch
 * @returns {Promise<Object>} - A promise that resolves to the aggregated data
 */
export const fetchAggregatedData = async (visualizationType, aggregationParams, options = {}) => {
  const {
    useFallback = true,
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  // Implement retry logic
  let retries = 0;
  let lastError = null;

  while (retries <= maxRetries) {
    try {
      const endpoint = ENDPOINTS[visualizationType];
      if (!endpoint) {
        throw new Error(`Unknown visualization type: ${visualizationType}`);
      }

      const response = await axios.get(`${API_BASE_URL}${endpoint}/aggregated`, {
        params: aggregationParams,
        headers: getAuthHeaders()
      });

      // Validate response data
      if (!response.data || !response.data.success) {
        throw new Error(`Invalid response from ${endpoint}/aggregated: ${JSON.stringify(response.data)}`);
      }

      // Extract data from response
      return response.data.data;
    } catch (error) {
      lastError = error;
      console.error(`Error fetching aggregated ${visualizationType} data (attempt ${retries + 1}/${maxRetries + 1}):`, error);

      // Check if we should retry
      if (retries < maxRetries && isRetryableError(error)) {
        retries++;
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retries - 1)));
      } else {
        break;
      }
    }
  }

  // All retries failed, use fallback data if enabled
  if (useFallback) {
    console.log(`Using fallback aggregated data for ${visualizationType}`);

    // Map visualization type to fallback data type
    const fallbackType = visualizationType === 'harmonyIndex'
      ? 'cyber_safety_harmony_index'
      : visualizationType === 'triDomainTensor'
      ? 'tri_domain_tensor'
      : visualizationType === 'riskControlFusion'
      ? 'risk_control_fusion_map'
      : visualizationType === 'resonanceSpectrogram'
      ? 'cyber_safety_resonance_spectrogram'
      : visualizationType === 'unifiedComplianceSecurity'
      ? 'unified_compliance_security_visualizer'
      : null;

    if (fallbackType) {
      // Generate fallback data with aggregation parameters
      return generateRandomData(fallbackType, {
        isAggregated: true,
        aggregationParams
      });
    }
  }

  // Re-throw the last error if no fallback
  throw lastError;
};

/**
 * Fetch data for all Cyber-Safety visualizations
 * @param {Object} options - Options for the data fetch
 * @returns {Promise<Object>} - A promise that resolves to all visualization data
 */
export const fetchAllCyberSafetyData = async (options = {}) => {
  const visualizationTypes = [
    'triDomainTensor',
    'harmonyIndex',
    'riskControlFusion',
    'resonanceSpectrogram',
    'unifiedComplianceSecurity'
  ];

  const results = {};

  await Promise.all(
    visualizationTypes.map(async (type) => {
      try {
        results[type] = await fetchCyberSafetyData(type, options);
      } catch (error) {
        console.error(`Error fetching ${type} data:`, error);
        results[type] = null;
      }
    })
  );

  return results;
};

/**
 * Submit feedback on a visualization
 * @param {string} visualizationType - The type of visualization
 * @param {Object} feedback - The feedback data
 * @param {Object} options - Options for the feedback submission
 * @returns {Promise<Object>} - A promise that resolves to the feedback response
 */
export const submitVisualizationFeedback = async (visualizationType, feedback, options = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000
  } = options;

  // Implement retry logic
  let retries = 0;
  let lastError = null;

  while (retries <= maxRetries) {
    try {
      const endpoint = ENDPOINTS[visualizationType];
      if (!endpoint) {
        throw new Error(`Unknown visualization type: ${visualizationType}`);
      }

      const response = await axios.post(`${API_BASE_URL}${endpoint}/feedback`, feedback, {
        headers: getAuthHeaders()
      });

      // Validate response data
      if (!response.data || !response.data.success) {
        throw new Error(`Invalid response from ${endpoint}/feedback: ${JSON.stringify(response.data)}`);
      }

      return response.data;
    } catch (error) {
      lastError = error;
      console.error(`Error submitting feedback for ${visualizationType} (attempt ${retries + 1}/${maxRetries + 1}):`, error);

      // Check if we should retry
      if (retries < maxRetries && isRetryableError(error)) {
        retries++;
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retries - 1)));
      } else {
        break;
      }
    }
  }

  // Re-throw the last error
  throw lastError;
};

/**
 * Export visualization data
 * @param {string} visualizationType - The type of visualization
 * @param {string} format - The export format (e.g., 'json', 'csv', 'png')
 * @param {Object} options - Options for the export
 * @returns {Promise<Blob>} - A promise that resolves to the exported data blob
 */
export const exportVisualizationData = async (visualizationType, format = 'json', options = {}) => {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    additionalParams = {}
  } = options;

  // Implement retry logic
  let retries = 0;
  let lastError = null;

  while (retries <= maxRetries) {
    try {
      const endpoint = ENDPOINTS[visualizationType];
      if (!endpoint) {
        throw new Error(`Unknown visualization type: ${visualizationType}`);
      }

      const response = await axios.get(`${API_BASE_URL}${endpoint}/export`, {
        params: {
          format,
          ...additionalParams
        },
        responseType: 'blob',
        headers: getAuthHeaders()
      });

      // Check if response is valid
      if (!response.data || response.data.size === 0) {
        throw new Error(`Empty response from ${endpoint}/export`);
      }

      return response.data;
    } catch (error) {
      lastError = error;
      console.error(`Error exporting ${visualizationType} data (attempt ${retries + 1}/${maxRetries + 1}):`, error);

      // Check if we should retry
      if (retries < maxRetries && isRetryableError(error)) {
        retries++;
        // Wait before retrying (with exponential backoff)
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, retries - 1)));
      } else {
        break;
      }
    }
  }

  // Re-throw the last error
  throw lastError;
};
