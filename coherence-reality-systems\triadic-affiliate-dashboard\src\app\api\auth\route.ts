import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs'
import { prisma } from '@/lib/prisma'
import { Conversion, Product, Network } from '@/types/websocket'

export async function GET(req: Request) {
  const { userId } = auth()
  
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        affiliateNetworks: true,
        products: true,
        conversions: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json({
      userId: user.id,
      email: user.email,
      name: user.name,
      affiliateNetworks: user.affiliateNetworks.map((n): Network => ({
        id: n.id,
        name: n.name,
        apiKey: n.apiKey,
        status: n.status
      })),
      products: user.products.map((p): Product => ({
        id: p.id,
        name: p.name,
        price: p.price,
        status: p.status,
        conversions: 0
      })),
      conversions: user.conversions.map((c): Conversion => ({
        id: c.id,
        productId: c.productId,
        networkId: c.networkId,
        revenue: c.revenue,
        status: c.status,
        createdAt: c.createdAt.toString(),
        triadicMetrics: {
          psi: c.psi || 0,
          phi: c.phi || 0,
          kappa: c.kappa || 0
        }
      })),
      triadicMetrics: {
        psi: user.psi || 0,
        phi: user.phi || 0,
        kappa: user.kappa || 0
      }
    })
  } catch (error) {
    console.error('Error fetching user data:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
