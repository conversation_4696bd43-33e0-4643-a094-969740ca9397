/**
 * NovaFuse Universal API Connector Security Utilities
 * 
 * This module provides security utilities for the UAC, including SSRF protection,
 * input validation, and other security features.
 */

const ipaddr = require('ipaddr.js');
const validator = require('validator');
const { URL } = require('url');

/**
 * SSRF Protection Utility
 * 
 * Checks if a URL is safe to connect to by validating against a whitelist
 * and checking for private IP addresses.
 */
class SSRFProtection {
  constructor(options = {}) {
    this.options = {
      allowedHosts: ['api.example.com', 'api.google.com', 'api.microsoft.com'],
      allowedProtocols: ['https:'],
      allowPrivateIPs: false,
      ...options
    };
  }

  /**
   * Check if a URL is safe to connect to
   * @param {string} url - URL to check
   * @returns {boolean} - Whether the URL is safe
   */
  isSafeUrl(url) {
    try {
      // Parse URL
      const parsedUrl = new URL(url);
      
      // Check protocol
      if (!this.options.allowedProtocols.includes(parsedUrl.protocol)) {
        console.warn(`SSRF Protection: Blocked URL with disallowed protocol: ${parsedUrl.protocol}`);
        return false;
      }
      
      // Check hostname against whitelist if enabled
      if (this.options.allowedHosts.length > 0) {
        const isHostAllowed = this.options.allowedHosts.some(host => {
          // Allow wildcards (e.g., *.example.com)
          if (host.startsWith('*.')) {
            const domain = host.substring(2);
            return parsedUrl.hostname.endsWith(domain);
          }
          return parsedUrl.hostname === host;
        });
        
        if (!isHostAllowed) {
          console.warn(`SSRF Protection: Blocked URL with disallowed host: ${parsedUrl.hostname}`);
          return false;
        }
      }
      
      // Check for private IP addresses
      if (!this.options.allowPrivateIPs) {
        // Resolve hostname to IP address
        try {
          const ip = ipaddr.parse(parsedUrl.hostname);
          
          if (ip.range() !== 'unicast') {
            console.warn(`SSRF Protection: Blocked URL with non-unicast IP: ${parsedUrl.hostname}`);
            return false;
          }
          
          // Check for private IP ranges
          if (ip.kind() === 'ipv4') {
            if (ip.range() === 'private' || ip.range() === 'loopback') {
              console.warn(`SSRF Protection: Blocked URL with private IPv4: ${parsedUrl.hostname}`);
              return false;
            }
          } else if (ip.kind() === 'ipv6') {
            if (ip.range() === 'private' || ip.range() === 'loopback') {
              console.warn(`SSRF Protection: Blocked URL with private IPv6: ${parsedUrl.hostname}`);
              return false;
            }
          }
        } catch (error) {
          // Not an IP address, continue with hostname checks
        }
      }
      
      return true;
    } catch (error) {
      console.error('SSRF Protection: Error checking URL:', error);
      return false;
    }
  }

  /**
   * Add allowed hosts to the whitelist
   * @param {string|string[]} hosts - Host(s) to add
   */
  addAllowedHosts(hosts) {
    if (Array.isArray(hosts)) {
      this.options.allowedHosts = [...this.options.allowedHosts, ...hosts];
    } else {
      this.options.allowedHosts.push(hosts);
    }
  }

  /**
   * Set allowed protocols
   * @param {string|string[]} protocols - Protocol(s) to allow
   */
  setAllowedProtocols(protocols) {
    if (Array.isArray(protocols)) {
      this.options.allowedProtocols = protocols;
    } else {
      this.options.allowedProtocols = [protocols];
    }
  }

  /**
   * Allow or disallow private IP addresses
   * @param {boolean} allow - Whether to allow private IPs
   */
  setAllowPrivateIPs(allow) {
    this.options.allowPrivateIPs = allow;
  }
}

/**
 * Input Validation Utility
 * 
 * Provides methods for validating user input to prevent injection attacks.
 */
class InputValidator {
  /**
   * Validate a string against SQL injection
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isSqlSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    
    // Check for common SQL injection patterns
    const sqlPatterns = [
      /(\s|^)(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|EXEC|UNION|CREATE|WHERE)(\s|$)/i,
      /(\s|^)(OR|AND)(\s+)(['"]?\d+['"]?\s*=\s*['"]?\d+['"]?)/i,
      /--/,
      /;.*/,
      /\/\*.+\*\//
    ];
    
    return !sqlPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Validate a string against XSS
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isXssSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    
    return validator.isLength(input, { min: 0, max: 1000 }) && 
           !/<script|javascript:|on\w+\s*=|data:|vbscript:|<iframe/i.test(input);
  }

  /**
   * Validate a string against command injection
   * @param {string} input - Input to validate
   * @returns {boolean} - Whether the input is safe
   */
  static isCommandSafe(input) {
    if (typeof input !== 'string') {
      return true;
    }
    
    // Check for common command injection patterns
    const commandPatterns = [
      /(\s|^)(bash|sh|cmd|powershell|exec|eval)(\s|$)/i,
      /[&|;`]/,
      /\$\(.+\)/,
      />\s*[a-zA-Z0-9]/,
      /<\s*[a-zA-Z0-9]/
    ];
    
    return !commandPatterns.some(pattern => pattern.test(input));
  }

  /**
   * Sanitize a string for safe use in HTML
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeHtml(input) {
    if (typeof input !== 'string') {
      return input;
    }
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }

  /**
   * Sanitize a string for safe use in SQL
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeSql(input) {
    if (typeof input !== 'string') {
      return input;
    }
    
    return input
      .replace(/'/g, "''")
      .replace(/\\/g, '\\\\')
      .replace(/\0/g, '\\0')
      .replace(/\n/g, '\\n')
      .replace(/\r/g, '\\r')
      .replace(/\t/g, '\\t')
      .replace(/\x1a/g, '\\Z');
  }

  /**
   * Sanitize a string for safe use in shell commands
   * @param {string} input - Input to sanitize
   * @returns {string} - Sanitized input
   */
  static sanitizeCommand(input) {
    if (typeof input !== 'string') {
      return input;
    }
    
    return input
      .replace(/[&;|`$><!\\]/g, '')
      .replace(/\r/g, '')
      .replace(/\n/g, '');
  }

  /**
   * Validate an object against a schema
   * @param {Object} input - Input to validate
   * @param {Object} schema - Schema to validate against
   * @returns {Object} - Validation result with isValid and errors properties
   */
  static validateObject(input, schema) {
    const errors = [];
    const result = { isValid: true, errors };
    
    if (!input || typeof input !== 'object') {
      result.isValid = false;
      errors.push('Input must be an object');
      return result;
    }
    
    if (!schema || typeof schema !== 'object') {
      result.isValid = false;
      errors.push('Schema must be an object');
      return result;
    }
    
    // Check required fields
    if (schema.required && Array.isArray(schema.required)) {
      for (const field of schema.required) {
        if (input[field] === undefined) {
          result.isValid = false;
          errors.push(`Required field '${field}' is missing`);
        }
      }
    }
    
    // Check field types and constraints
    if (schema.properties && typeof schema.properties === 'object') {
      for (const [field, fieldSchema] of Object.entries(schema.properties)) {
        if (input[field] !== undefined) {
          // Check type
          if (fieldSchema.type) {
            const type = Array.isArray(input[field]) ? 'array' : typeof input[field];
            if (type !== fieldSchema.type) {
              result.isValid = false;
              errors.push(`Field '${field}' must be of type '${fieldSchema.type}'`);
            }
          }
          
          // Check enum
          if (fieldSchema.enum && Array.isArray(fieldSchema.enum)) {
            if (!fieldSchema.enum.includes(input[field])) {
              result.isValid = false;
              errors.push(`Field '${field}' must be one of: ${fieldSchema.enum.join(', ')}`);
            }
          }
          
          // Check pattern
          if (fieldSchema.pattern && typeof input[field] === 'string') {
            const pattern = new RegExp(fieldSchema.pattern);
            if (!pattern.test(input[field])) {
              result.isValid = false;
              errors.push(`Field '${field}' does not match pattern '${fieldSchema.pattern}'`);
            }
          }
          
          // Check min/max for numbers
          if (typeof input[field] === 'number') {
            if (fieldSchema.minimum !== undefined && input[field] < fieldSchema.minimum) {
              result.isValid = false;
              errors.push(`Field '${field}' must be greater than or equal to ${fieldSchema.minimum}`);
            }
            
            if (fieldSchema.maximum !== undefined && input[field] > fieldSchema.maximum) {
              result.isValid = false;
              errors.push(`Field '${field}' must be less than or equal to ${fieldSchema.maximum}`);
            }
          }
          
          // Check minLength/maxLength for strings
          if (typeof input[field] === 'string') {
            if (fieldSchema.minLength !== undefined && input[field].length < fieldSchema.minLength) {
              result.isValid = false;
              errors.push(`Field '${field}' must be at least ${fieldSchema.minLength} characters long`);
            }
            
            if (fieldSchema.maxLength !== undefined && input[field].length > fieldSchema.maxLength) {
              result.isValid = false;
              errors.push(`Field '${field}' must be at most ${fieldSchema.maxLength} characters long`);
            }
          }
          
          // Check minItems/maxItems for arrays
          if (Array.isArray(input[field])) {
            if (fieldSchema.minItems !== undefined && input[field].length < fieldSchema.minItems) {
              result.isValid = false;
              errors.push(`Field '${field}' must have at least ${fieldSchema.minItems} items`);
            }
            
            if (fieldSchema.maxItems !== undefined && input[field].length > fieldSchema.maxItems) {
              result.isValid = false;
              errors.push(`Field '${field}' must have at most ${fieldSchema.maxItems} items`);
            }
          }
        }
      }
    }
    
    return result;
  }
}

/**
 * Rate Limiting Utility
 * 
 * Provides methods for rate limiting API requests.
 */
class RateLimiter {
  constructor(options = {}) {
    this.options = {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // 100 requests per minute
      keyGenerator: (req) => req.ip || 'default',
      ...options
    };
    
    this.requests = new Map();
  }

  /**
   * Check if a request is allowed
   * @param {Object} req - Request object
   * @returns {boolean} - Whether the request is allowed
   */
  isAllowed(req) {
    const key = this.options.keyGenerator(req);
    const now = Date.now();
    
    // Get or create request record
    let record = this.requests.get(key);
    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.options.windowMs
      };
      this.requests.set(key, record);
    }
    
    // Reset count if window has passed
    if (now > record.resetTime) {
      record.count = 0;
      record.resetTime = now + this.options.windowMs;
    }
    
    // Check if request is allowed
    if (record.count >= this.options.maxRequests) {
      return false;
    }
    
    // Increment count
    record.count++;
    
    return true;
  }

  /**
   * Get remaining requests for a key
   * @param {Object} req - Request object
   * @returns {number} - Remaining requests
   */
  getRemainingRequests(req) {
    const key = this.options.keyGenerator(req);
    const now = Date.now();
    
    // Get request record
    const record = this.requests.get(key);
    if (!record) {
      return this.options.maxRequests;
    }
    
    // Reset count if window has passed
    if (now > record.resetTime) {
      return this.options.maxRequests;
    }
    
    return Math.max(0, this.options.maxRequests - record.count);
  }

  /**
   * Get reset time for a key
   * @param {Object} req - Request object
   * @returns {number} - Reset time in milliseconds
   */
  getResetTime(req) {
    const key = this.options.keyGenerator(req);
    
    // Get request record
    const record = this.requests.get(key);
    if (!record) {
      return Date.now() + this.options.windowMs;
    }
    
    return record.resetTime;
  }

  /**
   * Clean up expired records
   */
  cleanup() {
    const now = Date.now();
    
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

// Export security utilities
module.exports = {
  SSRFProtection,
  InputValidator,
  RateLimiter
};
