import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  Button,
  CircularProgress,
  Paper,
  Divider,
  Alert,
  Tooltip,
  IconButton
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import { useSnackbar } from 'notistack';
import api from '../../services/api';

/**
 * Component for managing role inheritance
 */
const RoleInheritanceManager = ({ role, onUpdate }) => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [roles, setRoles] = useState([]);
  const [selectedRoles, setSelectedRoles] = useState([]);
  const [error, setError] = useState(null);
  const { enqueueSnackbar } = useSnackbar();

  // Load all roles on component mount
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await api.get('/api/rbac/roles');
        
        // Filter out the current role and system roles
        const filteredRoles = response.data.filter(r => 
          r._id !== role._id && !r.isSystem
        );
        
        setRoles(filteredRoles);
        
        // Set selected roles based on current role's inheritsFrom
        if (role.inheritsFrom && Array.isArray(role.inheritsFrom)) {
          // Handle both populated and unpopulated inheritsFrom
          const inheritedRoleIds = role.inheritsFrom.map(r => 
            typeof r === 'object' ? r._id : r
          );
          setSelectedRoles(inheritedRoleIds);
        }
        
        setLoading(false);
      } catch (error) {
        console.error('Error fetching roles:', error);
        setError('Failed to load roles. Please try again.');
        setLoading(false);
      }
    };

    fetchRoles();
  }, [role]);

  // Handle role selection/deselection
  const handleRoleToggle = (roleId) => {
    setSelectedRoles(prevSelected => {
      if (prevSelected.includes(roleId)) {
        return prevSelected.filter(id => id !== roleId);
      } else {
        return [...prevSelected, roleId];
      }
    });
  };

  // Save role inheritance
  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      await api.put(`/api/rbac/roles/${role._id}`, {
        inheritsFrom: selectedRoles
      });
      
      enqueueSnackbar('Role inheritance updated successfully', { variant: 'success' });
      
      if (onUpdate) {
        onUpdate();
      }
      
      setSaving(false);
    } catch (error) {
      console.error('Error updating role inheritance:', error);
      const errorMessage = error.response?.data?.message || 'Failed to update role inheritance';
      setError(errorMessage);
      enqueueSnackbar(errorMessage, { variant: 'error' });
      setSaving(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
      <Box display="flex" alignItems="center" mb={1}>
        <Typography variant="h6">Role Inheritance</Typography>
        <Tooltip title="Role inheritance allows a role to inherit permissions from other roles. When a user is assigned a role, they get all permissions from that role and all roles it inherits from.">
          <IconButton size="small" sx={{ ml: 1 }}>
            <InfoIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
      
      <Divider sx={{ mb: 2 }} />
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <Typography variant="body2" color="textSecondary" gutterBottom>
        Select roles that this role should inherit permissions from:
      </Typography>
      
      {loading ? (
        <Box display="flex" justifyContent="center" p={2}>
          <CircularProgress />
        </Box>
      ) : roles.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No roles available for inheritance.
        </Alert>
      ) : (
        <List dense>
          {roles.map((r) => (
            <ListItem key={r._id} dense button onClick={() => handleRoleToggle(r._id)}>
              <ListItemIcon>
                <Checkbox
                  edge="start"
                  checked={selectedRoles.includes(r._id)}
                  tabIndex={-1}
                  disableRipple
                  disabled={saving}
                />
              </ListItemIcon>
              <ListItemText 
                primary={r.name} 
                secondary={r.description} 
              />
            </ListItem>
          ))}
        </List>
      )}
      
      <Box display="flex" justifyContent="flex-end" mt={2}>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSave}
          disabled={loading || saving}
          startIcon={saving && <CircularProgress size={20} />}
        >
          {saving ? 'Saving...' : 'Save Inheritance'}
        </Button>
      </Box>
    </Paper>
  );
};

export default RoleInheritanceManager;
