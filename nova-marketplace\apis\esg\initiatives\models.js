/**
 * ESG Initiatives API - Models
 * 
 * This file defines the models for the ESG Initiatives API.
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Initiative Schema
 * 
 * Represents an ESG initiative.
 */
const InitiativeSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: String,
    required: true,
    enum: ['Environmental', 'Social', 'Governance']
  },
  subcategory: {
    type: String,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['Planned', 'In Progress', 'Completed', 'Cancelled'],
    default: 'Planned'
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  budget: {
    amount: {
      type: Number
    },
    currency: {
      type: String
    }
  },
  owner: {
    type: String,
    required: true
  },
  stakeholders: [{
    name: {
      type: String,
      required: true
    },
    role: {
      type: String,
      required: true
    },
    email: {
      type: String
    }
  }],
  goals: [{
    description: {
      type: String,
      required: true
    },
    targetDate: {
      type: Date,
      required: true
    },
    status: {
      type: String,
      required: true,
      enum: ['Not Started', 'In Progress', 'Completed', 'Cancelled'],
      default: 'Not Started'
    },
    metrics: [{
      metric: {
        type: Schema.Types.ObjectId,
        ref: 'Metric'
      },
      targetValue: {
        type: Schema.Types.Mixed
      }
    }]
  }],
  relatedMetrics: [{
    type: Schema.Types.ObjectId,
    ref: 'Metric'
  }],
  sdgAlignment: [{
    type: Number,
    min: 1,
    max: 17
  }],
  documents: [{
    name: {
      type: String,
      required: true
    },
    description: {
      type: String
    },
    fileUrl: {
      type: String,
      required: true
    },
    fileType: {
      type: String
    },
    uploadDate: {
      type: Date,
      default: Date.now
    },
    uploadedBy: {
      type: String
    }
  }],
  updates: [{
    date: {
      type: Date,
      default: Date.now,
      required: true
    },
    content: {
      type: String,
      required: true
    },
    author: {
      type: String,
      required: true
    }
  }],
  tags: [{
    type: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

/**
 * Impact Assessment Schema
 * 
 * Represents an impact assessment for an ESG initiative.
 */
const ImpactAssessmentSchema = new Schema({
  initiative: {
    type: Schema.Types.ObjectId,
    ref: 'Initiative',
    required: true
  },
  assessmentDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  assessor: {
    type: String,
    required: true
  },
  environmentalImpact: {
    score: {
      type: Number,
      min: 1,
      max: 5
    },
    description: {
      type: String
    },
    metrics: [{
      metric: {
        type: Schema.Types.ObjectId,
        ref: 'Metric'
      },
      value: {
        type: Schema.Types.Mixed
      },
      notes: {
        type: String
      }
    }]
  },
  socialImpact: {
    score: {
      type: Number,
      min: 1,
      max: 5
    },
    description: {
      type: String
    },
    metrics: [{
      metric: {
        type: Schema.Types.ObjectId,
        ref: 'Metric'
      },
      value: {
        type: Schema.Types.Mixed
      },
      notes: {
        type: String
      }
    }]
  },
  governanceImpact: {
    score: {
      type: Number,
      min: 1,
      max: 5
    },
    description: {
      type: String
    },
    metrics: [{
      metric: {
        type: Schema.Types.ObjectId,
        ref: 'Metric'
      },
      value: {
        type: Schema.Types.Mixed
      },
      notes: {
        type: String
      }
    }]
  },
  overallImpact: {
    score: {
      type: Number,
      min: 1,
      max: 5
    },
    description: {
      type: String
    }
  },
  recommendations: [{
    description: {
      type: String,
      required: true
    },
    priority: {
      type: String,
      enum: ['Low', 'Medium', 'High'],
      required: true
    }
  }],
  status: {
    type: String,
    required: true,
    enum: ['Draft', 'Completed', 'Reviewed'],
    default: 'Draft'
  },
  reviewedBy: {
    type: String
  },
  reviewDate: {
    type: Date
  },
  reviewComments: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create and export models
const Initiative = mongoose.model('Initiative', InitiativeSchema);
const ImpactAssessment = mongoose.model('ImpactAssessment', ImpactAssessmentSchema);

module.exports = {
  Initiative,
  ImpactAssessment
};
