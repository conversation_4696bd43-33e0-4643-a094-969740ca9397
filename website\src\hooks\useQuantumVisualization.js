import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { generateQuantumState, animateQuantumState, calculateQuantumMetrics } from '../utils/quantumStateGenerator';
import { useNovaCore } from '@novafuse/nova-core';
import { v4 as uuidv4 } from 'uuid';

/**
 * Custom hook for managing quantum visualization state and animations
 * @param {Object} options - Configuration options
 * @param {boolean} [options.autoAnimate=true] - Whether to automatically animate the visualization
 * @param {number} [options.animationDuration=8000] - Duration of animation loop in ms
 * @param {number} [options.qubits=3] - Number of qubits to simulate
 * @param {boolean} [options.autoRotate=true] - Whether to enable auto-rotation of 3D view
 * @param {number} [options.rotationSpeed=0.5] - Speed of auto-rotation (0-1)
 * @returns {Object} Visualization state and controls
 */
export const useQuantumVisualization = ({
  autoAnimate = true,
  animationDuration = 8000,
  qubits = 3,
  autoRotate = true,
  rotationSpeed = 0.5
} = {}) => {
  const { novaTrack } = useNovaCore();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quantumState, setQuantumState] = useState(null);
  const [viewMode, setViewMode] = useState('3d'); // '3d' or 'chart'
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [cameraPosition, setCameraPosition] = useState({ x: 0, y: 0, z: 5 });
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const [isAutoRotating, setIsAutoRotating] = useState(autoRotate);
  
  // State for animation and metrics
  const [metrics, setMetrics] = useState({
    entropy: 0.5,
    coherence: 0.7,
    entanglement: 0.3,
    qScore: 0.6,
    qubits,
    isPure: false,
    isMixed: true,
    isEntangled: false,
    timestamp: Date.now(),
    sessionId: uuidv4(),
    viewMode: '3d',
    animationProgress: 0
  });
  
  const [isAnimating, setIsAnimating] = useState(autoAnimate);
  const animationRef = useRef(null);
  const lastUpdateTime = useRef(0);
  const frameCount = useRef(0);
  const fps = useRef(0);
  const lastFpsUpdate = useRef(0);
  const sessionStartTime = useRef(performance.now());
  
  // Performance metrics
  const [performance, setPerformance] = useState({
    fps: 0,
    lastRenderTime: 0,
    averageFrameTime: 0,
    maxFrameTime: 0,
    frameCount: 0,
    sessionDuration: 0
  });
  
  // Format metrics for display
  const formattedMetrics = useMemo(() => ({
    entropy: metrics.entropy.toFixed(4),
    coherence: (metrics.coherence * 100).toFixed(1) + '%',
    entanglement: (metrics.entanglement * 100).toFixed(1) + '%',
    qScore: (metrics.qScore * 100).toFixed(1),
    qubits: metrics.qubits,
    isPure: metrics.isPure ? 'Yes' : 'No',
    isMixed: metrics.isMixed ? 'Yes' : 'No',
    isEntangled: metrics.isEntangled ? 'Yes' : 'No',
    fps: performance.fps.toFixed(1),
    frameTime: performance.averageFrameTime.toFixed(2) + 'ms'
  }), [metrics, performance]);
  
  // Update performance metrics
  const updatePerformanceMetrics = useCallback((timestamp) => {
    const now = performance.now();
    const deltaTime = now - lastUpdateTime.current;
    lastUpdateTime.current = now;
    
    // Calculate FPS
    frameCount.current++;
    if (now - lastFpsUpdate.current > 1000) {
      fps.current = Math.round((frameCount.current * 1000) / (now - lastFpsUpdate.current));
      frameCount.current = 0;
      lastFpsUpdate.current = now;
      
      setPerformance(prev => ({
        fps: fps.current,
        lastRenderTime: deltaTime,
        averageFrameTime: (prev.averageFrameTime * 0.9) + (deltaTime * 0.1),
        maxFrameTime: Math.max(prev.maxFrameTime, deltaTime),
        frameCount: prev.frameCount + 1,
        sessionDuration: (now - sessionStartTime.current) / 1000
      }));
    }
  }, []);

  // Generate initial quantum state
  useEffect(() => {
    try {
      setIsLoading(true);
      const initialState = generateQuantumState({ 
        qubits,
        entropy: 0.5,
        coherence: 0.7,
        entanglement: 0.3
      });
      
      setQuantumState(initialState);
      setMetrics(prev => ({
        ...calculateQuantumMetrics(initialState),
        qubits,
        timestamp: Date.now(),
        sessionId: prev.sessionId || uuidv4(),
        viewMode: prev.viewMode || '3d',
        animationProgress: 0
      }));
      
      // Track initialization
      novaTrack('quantum_viz_initialized', {
        qubits,
        autoAnimate,
        timestamp: Date.now(),
        sessionId: metrics.sessionId
      });
      
    } catch (err) {
      console.error('Failed to initialize quantum state:', err);
      setError('Failed to initialize quantum visualization. Please try again.');
      novaTrack('quantum_viz_error', {
        error: err.message,
        timestamp: Date.now(),
      });
    } finally {
      setIsLoading(false);
    }
  }, [qubits, autoAnimate]);
  
  // Handle animation
  useEffect(() => {
    if (!autoAnimate || !isAnimating || !quantumState) return;
    
    const onUpdate = (newState, { entropy, coherence, entanglement }) => {
      setQuantumState(newState);
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
        animationRef.current = null;
      }
    };
  }, [autoAnimate, isAnimating, quantumState, animate]);
  
  // Generate a new random state
  const randomizeState = useCallback(() => {
    try {
      const newState = generateQuantumState({
        qubits,
        entropy: Math.random(),
        coherence: Math.random(),
        entanglement: Math.random(),
      });
      
      setQuantumState(newState);
      setMetrics(calculateQuantumMetrics(newState));
      
      // Track state change
      novaTrack('quantum_viz_randomized', {
        entropy: metrics.entropy,
        coherence: metrics.coherence,
        entanglement: metrics.entanglement,
        timestamp: Date.now(),
      });
      
      return newState;
    } catch (err) {
      console.error('Failed to randomize quantum state:', err);
      setError('Failed to generate new quantum state');
      return null;
    }
  }, [qubits, metrics]);
  
  // Toggle animation
  const toggleAnimation = useCallback(() => {
    const newState = !isAnimating;
    setIsAnimating(newState);
    
    novaTrack('quantum_viz_animation_toggled', {
      isAnimating: newState,
      timestamp: Date.now(),
      sessionId: metrics.sessionId,
      viewMode,
      metrics: {
        entropy: metrics.entropy,
        coherence: metrics.coherence,
        entanglement: metrics.entanglement
          newState = generateQuantumState({
            qubits,
            entropy: value,
            coherence: metrics.coherence,
            entanglement: metrics.entanglement,
          });
          break;
          
        case 'coherence':
          newState = generateQuantumState({
            qubits,
            entropy: metrics.entropy,
            coherence: value,
            entanglement: metrics.entanglement,
          });
          break;
          
        case 'entanglement':
          newState = generateQuantumState({
            qubits,
            entropy: metrics.entropy,
            coherence: metrics.coherence,
            entanglement: value,
          });
          break;
          
        default:
          throw new Error(`Unknown metric: ${metric}`);
      }
      
      setQuantumState(newState);
      setMetrics(calculateQuantumMetrics(newState));
      
      // Track metric update
      novaTrack('quantum_viz_metric_updated', {
        metric,
        value,
        timestamp: Date.now(),
      });
      
      return true;
    } catch (err) {
      console.error(`Failed to update ${metric}:`, err);
      return false;
    }
  }, [qubits, metrics, quantumState]);
  
  return {
    // State
    quantumState,
    metrics,
    isLoading,
    error,
    isAnimating,
    
    // Actions
    randomizeState,
    toggleAnimation,
    updateMetric,
    
    // Derived values
    formattedMetrics: {
      entropy: metrics.entropy.toFixed(4),
      coherence: (metrics.coherence * 100).toFixed(1) + '%',
      entanglement: (metrics.entanglement * 100).toFixed(1) + '%',
      qScore: (metrics.qScore * 100).toFixed(1),
    },
  };
};
