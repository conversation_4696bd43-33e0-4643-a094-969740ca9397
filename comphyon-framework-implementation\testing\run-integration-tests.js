/**
 * Comphyon Integration Layer Tests
 * 
 * This script runs tests for the Comphyon Integration Layer.
 */

const { TestRunner } = require('./test-framework');
const { createIntegrationLayerTestSuite } = require('./integration-layer-tests');

/**
 * Run integration layer tests
 */
async function runTests() {
  console.log('=== Comphyon Integration Layer Tests ===\n');
  
  // Create test runner
  const testRunner = new TestRunner({
    enableLogging: true,
    parallelSuites: false
  });
  
  // Add test suite
  testRunner.addSuite(createIntegrationLayerTestSuite());
  
  // Run tests
  try {
    const result = await testRunner.run();
    
    // Exit with appropriate code
    process.exit(result.failed > 0 ? 1 : 0);
  } catch (error) {
    console.error('Error running tests:', error);
    process.exit(1);
  }
}

// Run tests
runTests();
