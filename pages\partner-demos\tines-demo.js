import React from 'react';
import BasicDemoTemplate from '../../components/demo-framework/BasicDemoTemplate';
import PartnerDNAAnalysis from '../../components/demo-framework/PartnerDNAAnalysis';
import EcosystemSlotSelector from '../../components/demo-framework/EcosystemSlotSelector';
import PageWithSidebar from '../../components/PageWithSidebar';
import ecosystemSlots from '../../data/ecosystem-slots';

export default function TinesDemo() {
  // SEO metadata
  const pageProps = {
    title: 'Tines + NovaFuse: Automated SOAR Workflow Validation - NovaFuse',
    description: 'Validate Tines playbooks against NIST 800-53 controls in real-time using NovaFuse\'s AI auditor.',
    keywords: 'Tines integration, SOAR workflow validation, NovaFuse demo, NIST 800-53, compliance validation',
    canonical: 'https://novafuse.io/partner-demos/tines-demo',
    ogImage: '/images/partner-demos/tines-demo-og.png'
  };

  // Partner information
  const partnerInfo = {
    name: 'Tines',
    logo: '/images/partners/tines-logo.png',
    tagline: 'Tines + NovaFuse: Automated SOAR Workflow Validation',
    valueProposition: 'Validate Tines playbooks against NIST 800-53 controls in real-time using NovaFuse\'s AI auditor'
  };

  // Partner DNA Analysis
  const painPoints = [
    'Ensuring SOAR playbooks comply with regulatory requirements',
    'Manual validation of workflows against compliance frameworks',
    'Difficulty scaling compliance checks across multiple playbooks',
    'Lack of real-time compliance feedback during playbook development'
  ];

  const strategicObjectives = [
    'Expand enterprise market share with enhanced compliance capabilities',
    'Differentiate from competitors with built-in compliance validation',
    'Reduce customer implementation time with pre-validated playbooks',
    'Build credibility in regulated industries (finance, healthcare, government)'
  ];

  const competitiveAdvantages = [
    'No-code automation platform with intuitive interface',
    'Rapid playbook development and deployment',
    'Strong security posture and enterprise readiness',
    'Flexible integration capabilities with existing security tools'
  ];

  const integrationPoints = [
    'API-based playbook validation against compliance frameworks',
    'Real-time compliance feedback in Tines interface',
    'Pre-built compliance templates for common security workflows',
    'Compliance reporting and documentation for audits'
  ];

  // Relevant collaborative roles
  const relevantSlots = [
    ecosystemSlots.find(slot => slot.id === 'SC-01'), // NIST 800-53 Controls
    ecosystemSlots.find(slot => slot.id === 'SP-02'), // Compliance Automation
    ecosystemSlots.find(slot => slot.id === 'ET-01')  // AI Governance
  ];

  // Sidebar items
  const sidebarItems = [
    { type: 'category', label: 'Tines + NovaFuse', items: [
      { label: 'Demo Overview', href: '#overview' },
      { label: 'Partner DNA Analysis', href: '#dna-analysis' },
      { label: 'Integration Features', href: '#integration' },
      { label: 'Use Cases', href: '#use-cases' }
    ]},
    { type: 'category', label: 'Resources', items: [
      { label: 'Integration Documentation', href: '/documentation/integrations/tines' },
      { label: 'API Reference', href: '/api-docs/integrations/tines' },
      { label: 'Schedule War Room', href: '/contact?partner=tines&type=war-room' }
    ]},
    { type: 'category', label: 'Other Partner Demos', items: [
      { label: 'Drata Demo', href: '/partner-demos/drata-demo' },
      { label: 'Postman Demo', href: '/partner-demos/postman-demo' },
      { label: 'View All Partners', href: '/partner-demos' }
    ]}
  ];

  return (
    <PageWithSidebar
      title={pageProps.title}
      description={pageProps.description}
      sidebarItems={sidebarItems}
    >
      <BasicDemoTemplate
        title="Tines + NovaFuse Integration"
        description="Validate Tines playbooks against NIST 800-53 controls in real-time"
        partnerInfo={partnerInfo}
        demoType="technical"
      >
        <div className="space-y-8">
          {/* Partner DNA Analysis */}
          <div id="dna-analysis" className="scroll-mt-16">
            <PartnerDNAAnalysis
              partner={partnerInfo}
              painPoints={painPoints}
              strategicObjectives={strategicObjectives}
              competitiveAdvantages={competitiveAdvantages}
              integrationPoints={integrationPoints}
            />
          </div>

          {/* Integration Features */}
          <div id="integration" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">Integration Features</h2>
            <p className="text-gray-300 mb-6">
              The NovaFuse + Tines integration provides powerful capabilities for validating SOAR workflows against compliance frameworks. Here's how it works:
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">🔍</span>
                  <h3 className="text-lg font-semibold">Real-Time Validation</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Validate Tines playbooks against NIST 800-53 controls in real-time as they're being developed. Get immediate feedback on compliance issues.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">🤖</span>
                  <h3 className="text-lg font-semibold">AI Compliance Auditor</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  NovaFuse's AI auditor automatically analyzes playbook logic and identifies potential compliance gaps or risks based on NIST guidelines.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">📋</span>
                  <h3 className="text-lg font-semibold">Compliance Documentation</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Automatically generate compliance documentation for audits, showing how each playbook maps to specific NIST controls and requirements.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">🔄</span>
                  <h3 className="text-lg font-semibold">Continuous Monitoring</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Monitor playbooks for compliance drift as regulations change. Get alerts when playbooks need to be updated to maintain compliance.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">📊</span>
                  <h3 className="text-lg font-semibold">Compliance Dashboard</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  View compliance status across all playbooks with a comprehensive dashboard showing compliance scores, gaps, and remediation recommendations.
                </p>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <div className="flex items-center mb-3">
                  <span className="text-blue-400 text-2xl mr-3">📚</span>
                  <h3 className="text-lg font-semibold">Compliance Templates</h3>
                </div>
                <p className="text-gray-400 text-sm">
                  Access pre-built, compliance-validated templates for common security workflows to accelerate playbook development.
                </p>
              </div>
            </div>
          </div>

          {/* Relevant Partner Network Roles */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4">Relevant Partner Network Roles</h2>
            <p className="text-gray-300 mb-6">
              The Tines + NovaFuse integration leverages the following collaborative roles from the NovaFuse Partner Network:
            </p>

            <div className="space-y-4">
              {relevantSlots.map(slot => (
                <div key={slot.id} className="bg-blue-900 bg-opacity-30 border border-blue-500 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <span className="bg-blue-900 text-blue-300 text-xs font-bold px-2 py-1 rounded mr-2">
                      {slot.id}
                    </span>
                    <h3 className="text-xl font-semibold">{slot.name}</h3>
                  </div>
                  <p className="text-gray-300 mb-3">{slot.description}</p>

                  <div className="flex flex-wrap gap-1 mt-2">
                    {slot.tags.map((tag, index) => (
                      <span key={index} className="bg-gray-800 text-gray-300 px-2 py-0.5 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Use Cases */}
          <div id="use-cases" className="bg-gray-800 rounded-lg p-6 scroll-mt-16">
            <h2 className="text-2xl font-bold mb-4">Use Cases</h2>
            <p className="text-gray-300 mb-6">
              Here are some key use cases for the Tines + NovaFuse integration:
            </p>

            <div className="space-y-4">
              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-xl font-semibold mb-3">Financial Services Security Automation</h3>
                <p className="text-gray-400 mb-3">
                  Financial institutions can automate security workflows while ensuring compliance with regulations like GLBA, PCI DSS, and SOX.
                </p>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="font-medium mb-2 text-sm">Example Workflow</h4>
                  <ol className="space-y-2 text-sm text-gray-400">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">1.</span>
                      <span>Tines detects suspicious account activity through its automation</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">2.</span>
                      <span>NovaFuse validates the response workflow against GLBA requirements</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">3.</span>
                      <span>Tines executes the validated workflow to investigate and respond</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">4.</span>
                      <span>NovaFuse generates compliance documentation for the incident response</span>
                    </li>
                  </ol>
                </div>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-xl font-semibold mb-3">Healthcare Security Compliance</h3>
                <p className="text-gray-400 mb-3">
                  Healthcare organizations can automate security operations while maintaining HIPAA compliance for patient data protection.
                </p>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="font-medium mb-2 text-sm">Example Workflow</h4>
                  <ol className="space-y-2 text-sm text-gray-400">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">1.</span>
                      <span>Tines monitors for potential PHI data exfiltration</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">2.</span>
                      <span>NovaFuse validates the incident response against HIPAA requirements</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">3.</span>
                      <span>Tines executes containment and investigation workflows</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">4.</span>
                      <span>NovaFuse generates HIPAA-compliant breach notification documentation</span>
                    </li>
                  </ol>
                </div>
              </div>

              <div className="bg-gray-700 p-4 rounded-lg">
                <h3 className="text-xl font-semibold mb-3">Government Security Operations</h3>
                <p className="text-gray-400 mb-3">
                  Government agencies can automate security workflows while ensuring compliance with NIST 800-53, FedRAMP, and other federal requirements.
                </p>
                <div className="bg-gray-800 p-3 rounded-lg">
                  <h4 className="font-medium mb-2 text-sm">Example Workflow</h4>
                  <ol className="space-y-2 text-sm text-gray-400">
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">1.</span>
                      <span>Tines detects potential insider threat activity</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">2.</span>
                      <span>NovaFuse validates the response against NIST 800-53 AU and IR controls</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">3.</span>
                      <span>Tines executes the compliant investigation workflow</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-blue-500 mr-2">4.</span>
                      <span>NovaFuse generates FedRAMP-compliant incident documentation</span>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="bg-gradient-to-r from-blue-900 to-indigo-900 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold mb-3">Ready to Validate Your Tines Playbooks?</h2>
            <p className="text-lg mb-6 max-w-3xl mx-auto">
              Schedule a personalized demo to see how NovaFuse can help you validate your Tines playbooks against NIST 800-53 controls in real-time.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a
                href="/contact?partner=tines&type=war-room"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-bold"
              >
                Schedule a War Room Session
              </a>
              <a
                href="/documentation/integrations/tines"
                className="border border-white text-white px-6 py-3 rounded-lg font-bold hover:bg-white hover:text-blue-900"
              >
                View Integration Documentation
              </a>
            </div>
          </div>
        </div>
      </BasicDemoTemplate>
    </PageWithSidebar>
  );
}
