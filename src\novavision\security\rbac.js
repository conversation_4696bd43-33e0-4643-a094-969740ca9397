/**
 * NovaVision RBAC (Role-Based Access Control)
 *
 * This module implements role-based access control for NovaVision.
 * It ensures that sensitive information is only visible to users with appropriate roles.
 */

/**
 * Role definitions with their permissions
 */
const ROLES = {
  // Executive roles
  EXECUTIVE: {
    name: 'Executive',
    description: 'Executive leadership (CEO, CFO, etc.)',
    permissions: ['view:summary', 'view:financial', 'view:strategic']
  },

  // Security roles
  CISO: {
    name: 'CISO',
    description: 'Chief Information Security Officer',
    permissions: ['view:summary', 'view:detailed', 'view:security', 'view:trinity_csde', 'view:optimization', 'view:uuft', 'view:quantum_inference']
  },
  SECURITY_ANALYST: {
    name: 'Security Analyst',
    description: 'Security analyst role',
    permissions: ['view:detailed', 'view:security', 'view:quantum_inference']
  },

  // Compliance roles
  COMPLIANCE_OFFICER: {
    name: 'Compliance Officer',
    description: 'Compliance officer role',
    permissions: ['view:summary', 'view:detailed', 'view:compliance']
  },
  AUDITOR: {
    name: 'Auditor',
    description: 'External or internal auditor',
    permissions: ['view:summary', 'view:compliance', 'view:audit_logs']
  },

  // Standard roles
  ADMIN: {
    name: 'Administrator',
    description: 'System administrator',
    permissions: ['view:all', 'manage:users', 'manage:roles', 'manage:system']
  },
  USER: {
    name: 'Standard User',
    description: 'Standard user with basic access',
    permissions: ['view:summary']
  },

  // Partner roles
  PARTNER: {
    name: 'Partner',
    description: 'Integration partner',
    permissions: ['view:summary', 'view:integration']
  }
};

/**
 * Permission definitions
 */
const PERMISSIONS = {
  // View permissions
  VIEW_SUMMARY: 'view:summary',
  VIEW_DETAILED: 'view:detailed',
  VIEW_ALL: 'view:all',

  // Domain-specific view permissions
  VIEW_SECURITY: 'view:security',
  VIEW_COMPLIANCE: 'view:compliance',
  VIEW_FINANCIAL: 'view:financial',
  VIEW_STRATEGIC: 'view:strategic',
  VIEW_INTEGRATION: 'view:integration',
  VIEW_AUDIT_LOGS: 'view:audit_logs',

  // NovaFuse-specific view permissions
  VIEW_TRINITY_CSDE: 'view:trinity_csde',
  VIEW_OPTIMIZATION: 'view:optimization',
  VIEW_UUFT: 'view:uuft',
  VIEW_QUANTUM_INFERENCE: 'view:quantum_inference',

  // Management permissions
  MANAGE_USERS: 'manage:users',
  MANAGE_ROLES: 'manage:roles',
  MANAGE_SYSTEM: 'manage:system'
};

/**
 * RBAC Manager for NovaVision
 */
class NovaVisionRBAC {
  constructor(options = {}) {
    this.options = {
      strictMode: true, // Deny by default if role/permission not found
      ...options
    };

    this.roles = { ...ROLES };
    this.permissions = { ...PERMISSIONS };
    this.userRoles = new Map(); // userId -> [roles]
  }

  /**
   * Assign a role to a user
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {boolean} - Success status
   */
  assignRole(userId, roleId) {
    if (!this.roles[roleId]) {
      console.warn(`Role ${roleId} does not exist`);
      return false;
    }

    if (!this.userRoles.has(userId)) {
      this.userRoles.set(userId, []);
    }

    const userRoles = this.userRoles.get(userId);
    if (!userRoles.includes(roleId)) {
      userRoles.push(roleId);
    }

    return true;
  }

  /**
   * Remove a role from a user
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {boolean} - Success status
   */
  removeRole(userId, roleId) {
    if (!this.userRoles.has(userId)) {
      return false;
    }

    const userRoles = this.userRoles.get(userId);
    const index = userRoles.indexOf(roleId);

    if (index !== -1) {
      userRoles.splice(index, 1);
      return true;
    }

    return false;
  }

  /**
   * Check if a user has a specific permission
   * @param {string} userId - User ID
   * @param {string} permission - Permission to check
   * @returns {boolean} - Whether the user has the permission
   */
  hasPermission(userId, permission) {
    // Admin role has all permissions
    if (this.hasRole(userId, 'ADMIN')) {
      return true;
    }

    // Check if user has any role with the permission
    const userRoles = this.userRoles.get(userId) || [];

    for (const roleId of userRoles) {
      const role = this.roles[roleId];
      if (role && role.permissions.includes(permission)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if a user has a specific role
   * @param {string} userId - User ID
   * @param {string} roleId - Role to check
   * @returns {boolean} - Whether the user has the role
   */
  hasRole(userId, roleId) {
    const userRoles = this.userRoles.get(userId) || [];
    return userRoles.includes(roleId);
  }

  /**
   * Get all roles assigned to a user
   * @param {string} userId - User ID
   * @returns {Array} - Array of role IDs
   */
  getUserRoles(userId) {
    return this.userRoles.get(userId) || [];
  }

  /**
   * Get all permissions for a user
   * @param {string} userId - User ID
   * @returns {Array} - Array of permissions
   */
  getUserPermissions(userId) {
    const userRoles = this.userRoles.get(userId) || [];
    const permissions = new Set();

    for (const roleId of userRoles) {
      const role = this.roles[roleId];
      if (role) {
        for (const permission of role.permissions) {
          permissions.add(permission);
        }
      }
    }

    return Array.from(permissions);
  }
}

module.exports = {
  NovaVisionRBAC,
  ROLES,
  PERMISSIONS
};
