/**
 * Authentication Controller
 * 
 * This controller handles API requests related to authentication.
 */

const AuthService = require('../services/AuthService');
const ApiKeyService = require('../services/ApiKeyService');
const { ValidationError, AuthenticationError } = require('../utils/errors');

class AuthController {
  constructor() {
    this.authService = new AuthService();
    this.apiKeyService = new ApiKeyService();
  }

  /**
   * Register a new user
   */
  async register(req, res, next) {
    try {
      const userData = req.body;
      
      if (!userData) {
        throw new ValidationError('User data is required');
      }
      
      const user = await this.authService.register(userData);
      
      res.status(201).json(user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Login a user
   */
  async login(req, res, next) {
    try {
      const { username, password } = req.body;
      
      if (!username) {
        throw new ValidationError('Username is required');
      }
      
      if (!password) {
        throw new ValidationError('Password is required');
      }
      
      const result = await this.authService.login(username, password);
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Logout a user
   */
  async logout(req, res, next) {
    try {
      const token = req.headers.authorization?.split(' ')[1];
      
      if (!token) {
        throw new ValidationError('Token is required');
      }
      
      const result = await this.authService.logout(token);
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(req, res, next) {
    try {
      // User is already attached to the request by the auth middleware
      res.json(req.user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get all users
   */
  async getAllUsers(req, res, next) {
    try {
      // Check if user has admin role
      if (!this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      const users = await this.authService.getAllUsers();
      
      res.json(users);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user is requesting their own data or has admin role
      if (id !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      const user = await this.authService.getUserById(id);
      
      res.json(user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update user
   */
  async updateUser(req, res, next) {
    try {
      const { id } = req.params;
      const userData = req.body;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      if (!userData) {
        throw new ValidationError('User data is required');
      }
      
      // Check if user is updating their own data or has admin role
      if (id !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      // Only admin can update role and permissions
      if (!this.authService.hasRole(req.user, 'admin')) {
        delete userData.role;
        delete userData.permissions;
      }
      
      const user = await this.authService.updateUser(id, userData);
      
      res.json(user);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete user
   */
  async deleteUser(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('User ID is required');
      }
      
      // Check if user is deleting their own account or has admin role
      if (id !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      const result = await this.authService.deleteUser(id);
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create API key
   */
  async createApiKey(req, res, next) {
    try {
      const { name, permissions, expiresIn } = req.body;
      
      if (!name) {
        throw new ValidationError('API key name is required');
      }
      
      // Create API key for the current user
      const apiKey = await this.apiKeyService.createApiKey(
        name,
        req.user.id,
        permissions || req.user.permissions,
        expiresIn
      );
      
      res.status(201).json(apiKey);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get API keys
   */
  async getApiKeys(req, res, next) {
    try {
      // Get API keys for the current user
      const apiKeys = await this.apiKeyService.getApiKeysByUserId(req.user.id);
      
      res.json(apiKeys);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete API key
   */
  async deleteApiKey(req, res, next) {
    try {
      const { id } = req.params;
      
      if (!id) {
        throw new ValidationError('API key ID is required');
      }
      
      // Get API key
      const apiKey = await this.apiKeyService.getApiKeyById(id);
      
      // Check if API key belongs to the current user or user is admin
      if (apiKey.userId !== req.user.id && !this.authService.hasRole(req.user, 'admin')) {
        throw new AuthenticationError('Unauthorized');
      }
      
      const result = await this.apiKeyService.deleteApiKey(id);
      
      res.json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = new AuthController();
