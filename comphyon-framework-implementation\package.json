{"name": "comphyon-framework", "version": "0.1.0", "description": "Core Framework - Mathematical foundations and tensor operations for Comphyology", "main": "index.js", "scripts": {"test": "jest", "lint": "eslint *.js", "example": "node examples/basic-example.js"}, "keywords": ["comphyology", "framework", "tensor", "fusion", "uuft"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"eslint": "^8.38.0", "jest": "^29.5.0"}}