import React from 'react';
import { render, screen } from '@testing-library/react';
import PageWithSidebar from '../../components/PageWithSidebar';

// Mock the Sidebar component to simplify testing
jest.mock('../../components/Sidebar', () => {
  return function MockSidebar({ items, title }) {
    return (
      <div data-testid="mock-sidebar">
        <h3>{title || 'Navigation'}</h3>
        <ul>
          {items.map((item, index) => (
            <li key={index}>
              {item.type === 'category' ? (
                <div>
                  <span>{item.label}</span>
                  <ul>
                    {item.items && item.items.map((subItem, subIndex) => (
                      <li key={`${index}-${subIndex}`}>{subItem.label}</li>
                    ))}
                  </ul>
                </div>
              ) : (
                <a href={item.href}>{item.label}</a>
              )}
            </li>
          ))}
        </ul>
      </div>
    );
  };
});

// Mock the BackButton component
jest.mock('../../components/BackButton', () => {
  return function MockBackButton() {
    return <button data-testid="mock-back-button">Back</button>;
  };
});

// Mock the Layout component
jest.mock('../../components/Layout', () => {
  return function MockLayout({ children, title }) {
    return (
      <div data-testid="mock-layout">
        <h1>{title}</h1>
        {children}
      </div>
    );
  };
});

describe('PageWithSidebar', () => {
  const sidebarItems = [
    { label: 'Item 1', href: '#item1' },
    { label: 'Item 2', href: '#item2' },
    { label: 'Item 3', href: '#item3' }
  ];

  it('renders the title and children correctly', () => {
    render(
      <PageWithSidebar title="Test Page" sidebarItems={sidebarItems}>
        <div data-testid="page-content">Page Content</div>
      </PageWithSidebar>
    );

    // Check if the layout is rendered with the correct title
    expect(screen.getByTestId('mock-layout')).toBeInTheDocument();
    expect(screen.getByText('Test Page')).toBeInTheDocument();

    // Check if children are rendered
    expect(screen.getByTestId('page-content')).toBeInTheDocument();
    expect(screen.getByText('Page Content')).toBeInTheDocument();

    // Check if the sidebar is rendered
    expect(screen.getByTestId('mock-sidebar')).toBeInTheDocument();

    // Check if the back button is rendered
    expect(screen.getByTestId('mock-back-button')).toBeInTheDocument();
  });

  it('renders sidebar items correctly', () => {
    render(
      <PageWithSidebar title="Test Page" sidebarItems={sidebarItems}>
        <div>Page Content</div>
      </PageWithSidebar>
    );

    // Check if the sidebar is rendered
    const sidebar = screen.getByTestId('mock-sidebar');
    expect(sidebar).toBeInTheDocument();

    // Check if sidebar items are rendered
    expect(sidebar).toHaveTextContent('Item 1');
    expect(sidebar).toHaveTextContent('Item 2');
    expect(sidebar).toHaveTextContent('Item 3');

    // Check if the mobile dropdown is rendered
    const mobileSelect = screen.getByRole('combobox');
    expect(mobileSelect).toBeInTheDocument();
  });

  it('renders nested sidebar items correctly', () => {
    const nestedSidebarItems = [
      {
        type: 'category',
        label: 'Parent Category',
        items: [
          { label: 'Child Item 1', href: '#child1' },
          { label: 'Child Item 2', href: '#child2' }
        ]
      },
      { label: 'Single Item', href: '#single' }
    ];

    render(
      <PageWithSidebar title="Test Page" sidebarItems={nestedSidebarItems}>
        <div data-testid="page-content">Page Content</div>
      </PageWithSidebar>
    );

    // Check if the mock sidebar is rendered with the correct props
    const sidebar = screen.getByTestId('mock-sidebar');
    expect(sidebar).toBeInTheDocument();

    // Check if the category label is rendered
    expect(sidebar).toHaveTextContent('Parent Category');

    // Check if the child items are rendered
    expect(sidebar).toHaveTextContent('Child Item 1');
    expect(sidebar).toHaveTextContent('Child Item 2');

    // Check if the single item is rendered
    expect(sidebar).toHaveTextContent('Single Item');

    // Check if the mobile dropdown is rendered
    const mobileSelect = screen.getByRole('combobox');
    expect(mobileSelect).toBeInTheDocument();

    // Check if the page content is rendered
    expect(screen.getByTestId('page-content')).toBeInTheDocument();
    expect(screen.getByTestId('page-content')).toHaveTextContent('Page Content');
  });

  it('applies custom className to the container', () => {
    render(
      <PageWithSidebar title="Test Page" sidebarItems={sidebarItems} className="custom-class">
        <div>Page Content</div>
      </PageWithSidebar>
    );

    // Check if the layout is rendered
    expect(screen.getByTestId('mock-layout')).toBeInTheDocument();

    // We can't directly test the className since we're using a mock Layout component,
    // but we can verify that the component renders without errors
    expect(screen.getByText('Test Page')).toBeInTheDocument();
    expect(screen.getByText('Page Content')).toBeInTheDocument();
  });
});
