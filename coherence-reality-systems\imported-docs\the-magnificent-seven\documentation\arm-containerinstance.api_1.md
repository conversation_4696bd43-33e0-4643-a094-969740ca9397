## API Report File for "@azure/arm-containerinstance"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import * as coreAuth from '@azure/core-auth';
import * as coreClient from '@azure/core-client';
import { OperationState } from '@azure/core-lro';
import { PagedAsyncIterableIterator } from '@azure/core-paging';
import { SimplePollerLike } from '@azure/core-lro';

// @public
export interface AzureFileVolume {
    readOnly?: boolean;
    shareName: string;
    storageAccountKey?: string;
    storageAccountName: string;
}

// @public
export interface CachedImages {
    image: string;
    osType: string;
}

// @public
export interface CachedImagesListResult {
    nextLink?: string;
    value?: CachedImages[];
}

// @public
export interface Capabilities {
    readonly capabilities?: CapabilitiesCapabilities;
    readonly gpu?: string;
    readonly ipAddressType?: string;
    readonly location?: string;
    readonly osType?: string;
    readonly resourceType?: string;
}

// @public
export interface CapabilitiesCapabilities {
    readonly maxCpu?: number;
    readonly maxGpuCount?: number;
    readonly maxMemoryInGB?: number;
}

// @public
export interface CapabilitiesListResult {
    nextLink?: string;
    value?: Capabilities[];
}

// @public
export interface CloudError {
    error?: CloudErrorBody;
}

// @public
export interface CloudErrorBody {
    code?: string;
    details?: CloudErrorBody[];
    message?: string;
    target?: string;
}

// @public
export interface ConfidentialComputeProperties {
    ccePolicy?: string;
}

// @public
export interface Container {
    command?: string[];
    environmentVariables?: EnvironmentVariable[];
    image: string;
    readonly instanceView?: ContainerPropertiesInstanceView;
    livenessProbe?: ContainerProbe;
    name: string;
    ports?: ContainerPort[];
    readinessProbe?: ContainerProbe;
    resources: ResourceRequirements;
    securityContext?: SecurityContextDefinition;
    volumeMounts?: VolumeMount[];
}

// @public
export interface ContainerAttachResponse {
    password?: string;
    webSocketUri?: string;
}

// @public
export interface ContainerExec {
    command?: string[];
}

// @public
export interface ContainerExecRequest {
    command?: string;
    terminalSize?: ContainerExecRequestTerminalSize;
}

// @public
export interface ContainerExecRequestTerminalSize {
    cols?: number;
    rows?: number;
}

// @public
export interface ContainerExecResponse {
    password?: string;
    webSocketUri?: string;
}

// @public
export interface ContainerGroup extends Resource, ContainerGroupProperties {
}

// @public
export interface ContainerGroupDiagnostics {
    logAnalytics?: LogAnalytics;
}

// @public
export interface ContainerGroupIdentity {
    readonly principalId?: string;
    readonly tenantId?: string;
    type?: ResourceIdentityType;
    userAssignedIdentities?: {
        [propertyName: string]: UserAssignedIdentities;
    };
}

// @public
export type ContainerGroupIpAddressType = string;

// @public
export interface ContainerGroupListResult {
    nextLink?: string;
    value?: ContainerGroup[];
}

// @public
export type ContainerGroupNetworkProtocol = string;

// @public
export type ContainerGroupPriority = string;

// @public
export interface ContainerGroupProperties {
    confidentialComputeProperties?: ConfidentialComputeProperties;
    containers: Container[];
    diagnostics?: ContainerGroupDiagnostics;
    dnsConfig?: DnsConfiguration;
    encryptionProperties?: EncryptionProperties;
    extensions?: DeploymentExtensionSpec[];
    identity?: ContainerGroupIdentity;
    imageRegistryCredentials?: ImageRegistryCredential[];
    initContainers?: InitContainerDefinition[];
    readonly instanceView?: ContainerGroupPropertiesInstanceView;
    ipAddress?: IpAddress;
    osType: OperatingSystemTypes;
    priority?: ContainerGroupPriority;
    readonly provisioningState?: string;
    restartPolicy?: ContainerGroupRestartPolicy;
    sku?: ContainerGroupSku;
    subnetIds?: ContainerGroupSubnetId[];
    volumes?: Volume[];
}

// @public
export interface ContainerGroupPropertiesInstanceView {
    readonly events?: Event_2[];
    readonly state?: string;
}

// @public
export type ContainerGroupRestartPolicy = string;

// @public
export interface ContainerGroups {
    beginCreateOrUpdate(resourceGroupName: string, containerGroupName: string, containerGroup: ContainerGroup, options?: ContainerGroupsCreateOrUpdateOptionalParams): Promise<SimplePollerLike<OperationState<ContainerGroupsCreateOrUpdateResponse>, ContainerGroupsCreateOrUpdateResponse>>;
    beginCreateOrUpdateAndWait(resourceGroupName: string, containerGroupName: string, containerGroup: ContainerGroup, options?: ContainerGroupsCreateOrUpdateOptionalParams): Promise<ContainerGroupsCreateOrUpdateResponse>;
    beginDelete(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsDeleteOptionalParams): Promise<SimplePollerLike<OperationState<ContainerGroupsDeleteResponse>, ContainerGroupsDeleteResponse>>;
    beginDeleteAndWait(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsDeleteOptionalParams): Promise<ContainerGroupsDeleteResponse>;
    beginRestart(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsRestartOptionalParams): Promise<SimplePollerLike<OperationState<void>, void>>;
    beginRestartAndWait(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsRestartOptionalParams): Promise<void>;
    beginStart(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsStartOptionalParams): Promise<SimplePollerLike<OperationState<void>, void>>;
    beginStartAndWait(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsStartOptionalParams): Promise<void>;
    get(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsGetOptionalParams): Promise<ContainerGroupsGetResponse>;
    getOutboundNetworkDependenciesEndpoints(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsGetOutboundNetworkDependenciesEndpointsOptionalParams): Promise<ContainerGroupsGetOutboundNetworkDependenciesEndpointsResponse>;
    list(options?: ContainerGroupsListOptionalParams): PagedAsyncIterableIterator<ContainerGroup>;
    listByResourceGroup(resourceGroupName: string, options?: ContainerGroupsListByResourceGroupOptionalParams): PagedAsyncIterableIterator<ContainerGroup>;
    stop(resourceGroupName: string, containerGroupName: string, options?: ContainerGroupsStopOptionalParams): Promise<void>;
    update(resourceGroupName: string, containerGroupName: string, resource: Resource, options?: ContainerGroupsUpdateOptionalParams): Promise<ContainerGroupsUpdateResponse>;
}

// @public
export interface ContainerGroupsCreateOrUpdateOptionalParams extends coreClient.OperationOptions {
    resumeFrom?: string;
    updateIntervalInMs?: number;
}

// @public
export type ContainerGroupsCreateOrUpdateResponse = ContainerGroup;

// @public
export interface ContainerGroupsDeleteOptionalParams extends coreClient.OperationOptions {
    resumeFrom?: string;
    updateIntervalInMs?: number;
}

// @public
export type ContainerGroupsDeleteResponse = ContainerGroup;

// @public
export interface ContainerGroupsGetOptionalParams extends coreClient.OperationOptions {
}

// @public
export interface ContainerGroupsGetOutboundNetworkDependenciesEndpointsOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainerGroupsGetOutboundNetworkDependenciesEndpointsResponse = {
    body: string[];
};

// @public
export type ContainerGroupsGetResponse = ContainerGroup;

// @public
export type ContainerGroupSku = string;

// @public
export interface ContainerGroupsListByResourceGroupNextOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainerGroupsListByResourceGroupNextResponse = ContainerGroupListResult;

// @public
export interface ContainerGroupsListByResourceGroupOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainerGroupsListByResourceGroupResponse = ContainerGroupListResult;

// @public
export interface ContainerGroupsListNextOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainerGroupsListNextResponse = ContainerGroupListResult;

// @public
export interface ContainerGroupsListOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainerGroupsListResponse = ContainerGroupListResult;

// @public
export interface ContainerGroupsRestartOptionalParams extends coreClient.OperationOptions {
    resumeFrom?: string;
    updateIntervalInMs?: number;
}

// @public
export interface ContainerGroupsStartOptionalParams extends coreClient.OperationOptions {
    resumeFrom?: string;
    updateIntervalInMs?: number;
}

// @public
export interface ContainerGroupsStopOptionalParams extends coreClient.OperationOptions {
}

// @public
export interface ContainerGroupSubnetId {
    id: string;
    name?: string;
}

// @public
export interface ContainerGroupsUpdateOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainerGroupsUpdateResponse = ContainerGroup;

// @public
export interface ContainerHttpGet {
    httpHeaders?: HttpHeader[];
    path?: string;
    port: number;
    scheme?: Scheme;
}

// @public (undocumented)
export class ContainerInstanceManagementClient extends coreClient.ServiceClient {
    // (undocumented)
    $host: string;
    constructor(credentials: coreAuth.TokenCredential, subscriptionId: string, options?: ContainerInstanceManagementClientOptionalParams);
    // (undocumented)
    apiVersion: string;
    // (undocumented)
    containerGroups: ContainerGroups;
    // (undocumented)
    containers: Containers;
    // (undocumented)
    location: Location_2;
    // (undocumented)
    operations: Operations;
    // (undocumented)
    subnetServiceAssociationLink: SubnetServiceAssociationLink;
    // (undocumented)
    subscriptionId: string;
}

// @public
export interface ContainerInstanceManagementClientOptionalParams extends coreClient.ServiceClientOptions {
    $host?: string;
    apiVersion?: string;
    endpoint?: string;
}

// @public
export type ContainerInstanceOperationsOrigin = string;

// @public
export type ContainerNetworkProtocol = string;

// @public
export interface ContainerPort {
    port: number;
    protocol?: ContainerNetworkProtocol;
}

// @public
export interface ContainerProbe {
    exec?: ContainerExec;
    failureThreshold?: number;
    httpGet?: ContainerHttpGet;
    initialDelaySeconds?: number;
    periodSeconds?: number;
    successThreshold?: number;
    timeoutSeconds?: number;
}

// @public
export interface ContainerPropertiesInstanceView {
    readonly currentState?: ContainerState;
    readonly events?: Event_2[];
    readonly previousState?: ContainerState;
    readonly restartCount?: number;
}

// @public
export interface Containers {
    attach(resourceGroupName: string, containerGroupName: string, containerName: string, options?: ContainersAttachOptionalParams): Promise<ContainersAttachResponse>;
    executeCommand(resourceGroupName: string, containerGroupName: string, containerName: string, containerExecRequest: ContainerExecRequest, options?: ContainersExecuteCommandOptionalParams): Promise<ContainersExecuteCommandResponse>;
    listLogs(resourceGroupName: string, containerGroupName: string, containerName: string, options?: ContainersListLogsOptionalParams): Promise<ContainersListLogsResponse>;
}

// @public
export interface ContainersAttachOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainersAttachResponse = ContainerAttachResponse;

// @public
export interface ContainersExecuteCommandOptionalParams extends coreClient.OperationOptions {
}

// @public
export type ContainersExecuteCommandResponse = ContainerExecResponse;

// @public
export interface ContainersListLogsOptionalParams extends coreClient.OperationOptions {
    tail?: number;
    timestamps?: boolean;
}

// @public
export type ContainersListLogsResponse = Logs;

// @public
export interface ContainerState {
    readonly detailStatus?: string;
    readonly exitCode?: number;
    readonly finishTime?: Date;
    readonly startTime?: Date;
    readonly state?: string;
}

// @public
export interface DeploymentExtensionSpec {
    extensionType?: string;
    name: string;
    protectedSettings?: Record<string, unknown>;
    settings?: Record<string, unknown>;
    version?: string;
}

// @public
export interface DnsConfiguration {
    nameServers: string[];
    options?: string;
    searchDomains?: string;
}

// @public
export type DnsNameLabelReusePolicy = string;

// @public
export interface EncryptionProperties {
    identity?: string;
    keyName: string;
    keyVersion: string;
    vaultBaseUrl: string;
}

// @public
export interface EnvironmentVariable {
    name: string;
    secureValue?: string;
    value?: string;
}

// @public
interface Event_2 {
    readonly count?: number;
    readonly firstTimestamp?: Date;
    readonly lastTimestamp?: Date;
    readonly message?: string;
    readonly name?: string;
    readonly type?: string;
}
export { Event_2 as Event }

// @public
export function getContinuationToken(page: unknown): string | undefined;

// @public
export interface GitRepoVolume {
    directory?: string;
    repository: string;
    revision?: string;
}

// @public
export interface GpuResource {
    count: number;
    sku: GpuSku;
}

// @public
export type GpuSku = string;

// @public
export interface HttpHeader {
    name?: string;
    value?: string;
}

// @public
export interface ImageRegistryCredential {
    identity?: string;
    identityUrl?: string;
    password?: string;
    server: string;
    username?: string;
}

// @public
export interface InitContainerDefinition {
    command?: string[];
    environmentVariables?: EnvironmentVariable[];
    image?: string;
    readonly instanceView?: InitContainerPropertiesDefinitionInstanceView;
    name: string;
    securityContext?: SecurityContextDefinition;
    volumeMounts?: VolumeMount[];
}

// @public
export interface InitContainerPropertiesDefinitionInstanceView {
    readonly currentState?: ContainerState;
    readonly events?: Event_2[];
    readonly previousState?: ContainerState;
    readonly restartCount?: number;
}

// @public
export interface IpAddress {
    autoGeneratedDomainNameLabelScope?: DnsNameLabelReusePolicy;
    dnsNameLabel?: string;
    readonly fqdn?: string;
    ip?: string;
    ports: Port[];
    type: ContainerGroupIpAddressType;
}

// @public
export enum KnownContainerGroupIpAddressType {
    Private = "Private",
    Public = "Public"
}

// @public
export enum KnownContainerGroupNetworkProtocol {
    TCP = "TCP",
    UDP = "UDP"
}

// @public
export enum KnownContainerGroupPriority {
    Regular = "Regular",
    Spot = "Spot"
}

// @public
export enum KnownContainerGroupRestartPolicy {
    Always = "Always",
    Never = "Never",
    OnFailure = "OnFailure"
}

// @public
export enum KnownContainerGroupSku {
    Confidential = "Confidential",
    Dedicated = "Dedicated",
    Standard = "Standard"
}

// @public
export enum KnownContainerInstanceOperationsOrigin {
    System = "System",
    User = "User"
}

// @public
export enum KnownContainerNetworkProtocol {
    TCP = "TCP",
    UDP = "UDP"
}

// @public
export enum KnownDnsNameLabelReusePolicy {
    Noreuse = "Noreuse",
    ResourceGroupReuse = "ResourceGroupReuse",
    SubscriptionReuse = "SubscriptionReuse",
    TenantReuse = "TenantReuse",
    Unsecure = "Unsecure"
}

// @public
export enum KnownGpuSku {
    K80 = "K80",
    P100 = "P100",
    V100 = "V100"
}

// @public
export enum KnownLogAnalyticsLogType {
    ContainerInsights = "ContainerInsights",
    ContainerInstanceLogs = "ContainerInstanceLogs"
}

// @public
export enum KnownOperatingSystemTypes {
    Linux = "Linux",
    Windows = "Windows"
}

// @public
export enum KnownScheme {
    Http = "http",
    Https = "https"
}

// @public
interface Location_2 {
    listCachedImages(location: string, options?: LocationListCachedImagesOptionalParams): PagedAsyncIterableIterator<CachedImages>;
    listCapabilities(location: string, options?: LocationListCapabilitiesOptionalParams): PagedAsyncIterableIterator<Capabilities>;
    listUsage(location: string, options?: LocationListUsageOptionalParams): PagedAsyncIterableIterator<Usage>;
}
export { Location_2 as Location }

// @public
export interface LocationListCachedImagesNextOptionalParams extends coreClient.OperationOptions {
}

// @public
export type LocationListCachedImagesNextResponse = CachedImagesListResult;

// @public
export interface LocationListCachedImagesOptionalParams extends coreClient.OperationOptions {
}

// @public
export type LocationListCachedImagesResponse = CachedImagesListResult;

// @public
export interface LocationListCapabilitiesNextOptionalParams extends coreClient.OperationOptions {
}

// @public
export type LocationListCapabilitiesNextResponse = CapabilitiesListResult;

// @public
export interface LocationListCapabilitiesOptionalParams extends coreClient.OperationOptions {
}

// @public
export type LocationListCapabilitiesResponse = CapabilitiesListResult;

// @public
export interface LocationListUsageOptionalParams extends coreClient.OperationOptions {
}

// @public
export type LocationListUsageResponse = UsageListResult;

// @public
export interface LogAnalytics {
    logType?: LogAnalyticsLogType;
    metadata?: {
        [propertyName: string]: string;
    };
    workspaceId: string;
    workspaceKey: string;
    workspaceResourceId?: string;
}

// @public
export type LogAnalyticsLogType = string;

// @public
export interface Logs {
    content?: string;
}

// @public
export type OperatingSystemTypes = string;

// @public
export interface Operation {
    display: OperationDisplay;
    name: string;
    origin?: ContainerInstanceOperationsOrigin;
    properties?: Record<string, unknown>;
}

// @public
export interface OperationDisplay {
    description?: string;
    operation?: string;
    provider?: string;
    resource?: string;
}

// @public
export interface OperationListResult {
    nextLink?: string;
    value?: Operation[];
}

// @public
export interface Operations {
    list(options?: OperationsListOptionalParams): PagedAsyncIterableIterator<Operation>;
}

// @public
export interface OperationsListNextOptionalParams extends coreClient.OperationOptions {
}

// @public
export type OperationsListNextResponse = OperationListResult;

// @public
export interface OperationsListOptionalParams extends coreClient.OperationOptions {
}

// @public
export type OperationsListResponse = OperationListResult;

// @public
export interface Port {
    port: number;
    protocol?: ContainerGroupNetworkProtocol;
}

// @public
export interface Resource {
    readonly id?: string;
    location?: string;
    readonly name?: string;
    tags?: {
        [propertyName: string]: string;
    };
    readonly type?: string;
    zones?: string[];
}

// @public
export type ResourceIdentityType = "SystemAssigned" | "UserAssigned" | "SystemAssigned, UserAssigned" | "None";

// @public
export interface ResourceLimits {
    cpu?: number;
    gpu?: GpuResource;
    memoryInGB?: number;
}

// @public
export interface ResourceRequests {
    cpu: number;
    gpu?: GpuResource;
    memoryInGB: number;
}

// @public
export interface ResourceRequirements {
    limits?: ResourceLimits;
    requests: ResourceRequests;
}

// @public
export type Scheme = string;

// @public
export interface SecurityContextCapabilitiesDefinition {
    add?: string[];
    drop?: string[];
}

// @public
export interface SecurityContextDefinition {
    allowPrivilegeEscalation?: boolean;
    capabilities?: SecurityContextCapabilitiesDefinition;
    privileged?: boolean;
    runAsGroup?: number;
    runAsUser?: number;
    seccompProfile?: string;
}

// @public
export interface SubnetServiceAssociationLink {
    beginDelete(resourceGroupName: string, virtualNetworkName: string, subnetName: string, options?: SubnetServiceAssociationLinkDeleteOptionalParams): Promise<SimplePollerLike<OperationState<void>, void>>;
    beginDeleteAndWait(resourceGroupName: string, virtualNetworkName: string, subnetName: string, options?: SubnetServiceAssociationLinkDeleteOptionalParams): Promise<void>;
}

// @public
export interface SubnetServiceAssociationLinkDeleteOptionalParams extends coreClient.OperationOptions {
    resumeFrom?: string;
    updateIntervalInMs?: number;
}

// @public
export interface Usage {
    readonly currentValue?: number;
    readonly id?: string;
    readonly limit?: number;
    readonly name?: UsageName;
    readonly unit?: string;
}

// @public
export interface UsageListResult {
    readonly value?: Usage[];
}

// @public
export interface UsageName {
    readonly localizedValue?: string;
    readonly value?: string;
}

// @public
export interface UserAssignedIdentities {
    readonly clientId?: string;
    readonly principalId?: string;
}

// @public
export interface Volume {
    azureFile?: AzureFileVolume;
    emptyDir?: Record<string, unknown>;
    gitRepo?: GitRepoVolume;
    name: string;
    secret?: {
        [propertyName: string]: string;
    };
}

// @public
export interface VolumeMount {
    mountPath: string;
    name: string;
    readOnly?: boolean;
}

// (No @packageDocumentation comment for this package)

```
